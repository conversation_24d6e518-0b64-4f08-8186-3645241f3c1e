/**
 * 公告板系统 - 兼容性优化版本
 * 保持jQuery依赖，但应用核心优化改进
 */

$(document).ready(function () {
    // 使用命名空间避免全局变量污染
    window.BulletinBoardApp = {
        // 配置常量
        CONFIG: {
            LAYOUTS: { NINE: '9', SIXTEEN: '16', ALL: 'all' },
            REFRESH_INTERVAL: 20000,
            STATUS: { WORKING: '20', PENDING: '10' },
            WARNING_TIME: 120
        },

        // 状态管理
        state: {
            currentPage: 1,
            totalPages: 1,
            currentLayout: 'all', // 默认全部展示
            rightPanelVisible: true, // 默认显示右侧面板
            itemsPerPage: Infinity,
            rawData: null,
            filteredHandPoints: [],
            bullList: [],
            autoScrollInterval: null
        },

        // 初始化
        init: function () {
            this.initRealtimeClock();
            this.loadData();
            this.initWindowResizeHandler();
        },

        // 初始化窗口大小改变处理
        initWindowResizeHandler: function () {
            const self = this;
            let resizeTimer;

            $(window).on('resize', function () {
                // 使用防抖机制，避免频繁触发
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function () {
                    // 窗口大小改变时重新应用布局
                    if (self.state.rawData) {
                        self.applyLayout();
                    }
                }, 300);
            });
        },



        // 初始化实时时钟
        initRealtimeClock: function () {
            const self = this;
            this.updateClock();
            // 每秒更新一次时间
            setInterval(function () {
                self.updateClock();
            }, 1000);
        },

        // 更新时钟显示
        updateClock: function () {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            const timeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            $('#realtime-clock').text(timeString);
        },

        // HTML转义，防止XSS攻击
        escapeHtml: function (text) {
            if (typeof text !== 'string') return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // 格式化客户名称
        formatCustomerName: function (customerName) {
            const trimmed = customerName ? customerName.trim() : '';
            return trimmed ? '-' + this.escapeHtml(trimmed) : '';
        },

        // 格式化运输起点
        formatStartOfTransport: function (startValue) {
            return startValue ? '-' + this.escapeHtml(startValue) : '';
        },

        // 生成单个项目HTML - 提取公共逻辑
        generateItemHTML: function (item, index) {
            const className = item.status == this.CONFIG.STATUS.WORKING ? 'working' : 'pending';
            let packCount = (item.hashMap && item.hashMap.totalWaitingTime) || 0;
            if (!packCount || packCount == 0) {
                packCount = item.hashMap.waitingTime || 0;
            }
            // const isOvertime = [10, 20].includes(Number(item.status)) && waitingTime >= this.CONFIG.WARNING_TIME;

            const customerName = this.formatCustomerName(item.hashMap && item.hashMap.customerName);
            const startOfTransport = this.formatStartOfTransport(item.hashMap && item.hashMap.startOfTransport);

            const vehicleNo = this.escapeHtml((item.hashMap && item.hashMap.vehicleNo) || '');

            const serialNumber = index !== undefined ? (index + 1) : '';
            const statusClass = className === 'working' ? 'working-row' : 'pending-row';
            const backgroundColor = className === 'working' ? '#4CAF50' : '#E67E22'; // 深绿色表示正在作业，柔和橙色表示等待作业
            const textColor = '#FFFFFF'; // 白色字体确保清晰

            return '<div class="vehicle-row ' + statusClass + '" style="width: 100%; padding: 6px 8px; margin: 1px 0; position: relative; border-radius: 4px; background-color: ' + backgroundColor + '; color: ' + textColor + '; display: flex; align-items: center; min-height: 40px;">' +
                (serialNumber ? '<span style="position: absolute; top: 50%; left: 8px; transform: translateY(-50%); font-size: 12px; color: ' + backgroundColor + '; font-weight: bold; width: 15px; height: 15px; background-color: #FFFFFF; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">' + serialNumber + '</span>' : '') +
                '<div style="margin-left: 20px; font-size: 12px; font-weight: bold; color: ' + textColor + '; flex: 1; word-wrap: break-word; line-height: 1.3;">' +
                vehicleNo + ' - ' + packCount + '分' + customerName + startOfTransport +
                '</div>' +
                '</div>';
        },

        // 计算装卸点区域可显示的最大车辆数量
        calculateMaxDisplayItems: function () {
            // 获取视口高度
            const viewportHeight = window.innerHeight;

            // 计算顶部区域高度（标题、状态栏等）
            const topHeight = 100; // 大概100px的顶部区域

            // 计算可用的内容区域高度
            const availableHeight = viewportHeight - topHeight;

            // 获取装卸点数量，用于计算每个装卸点的平均高度
            const doorCount = this.state.filteredHandPoints ? this.state.filteredHandPoints.length : 1;
            const minDoorCount = Math.max(1, doorCount); // 至少1个装卸点

            // 计算每个装卸点的可用高度（平均分配）
            const doorTitleHeight = 40; // 装卸点标题大概40px
            const doorPadding = 10; // 装卸点之间的间距
            const averageHeightPerDoor = Math.floor(availableHeight / minDoorCount) - doorPadding;
            const doorContentHeight = averageHeightPerDoor - doorTitleHeight;

            // 每个车辆项目的高度（min-height 40px + margin 2px）
            const itemHeight = 42;

            // 计算最多能显示多少个车辆（至少显示2个，最多显示8个）
            const calculatedMaxItems = Math.floor(doorContentHeight / itemHeight);
            const maxItems = Math.max(2, Math.min(8, calculatedMaxItems));

            return maxItems;
        },

        // 生成门点HTML - 统一逻辑
        generateDoorHTML: function (handPoint) {
            const self = this;
            const filterData = this.state.bullList.filter(function (l) {
                return l.handPointId == handPoint.handPointId;
            });

            // 先过滤掉钢材卸货类型，再生成HTML保证序号连续
            const validData = filterData.filter(function (item) {
                const businessType = self.escapeHtml((item.hashMap && item.hashMap.businessType) || '');
                return businessType !== '钢材卸货';
            });

            const itemsHTML = validData.map(function (item, index) {
                return self.generateItemHTML(item, index);
            }).join('');

            // 根据屏幕大小动态计算最大显示数量
            const maxDisplayItems = this.calculateMaxDisplayItems();
            const displayData = validData.slice(0, maxDisplayItems);

            const limitedItemsHTML = displayData.map(function (item, index) {
                return self.generateItemHTML(item, index);
            }).join('');

            // 如果车辆数量大于可显示数量的75%，启用自动滚动
            const scrollThreshold = Math.max(2, Math.floor(maxDisplayItems * 0.75));
            const needScroll = displayData.length > scrollThreshold;
            let scrollWrapperStyle = '';
            let scrollContent = limitedItemsHTML;

            if (needScroll) {
                // 复制内容用于无缝滚动，并设置动画
                scrollContent = limitedItemsHTML + limitedItemsHTML;
                const animationDuration = Math.max(displayData.length * 3, 8);
                scrollWrapperStyle = 'animation: auto-scroll-vertical ' + animationDuration + 's linear infinite;';
            }

            return '<div class="list-one">' +
                '<div class="list-one-title">' +
                '<div class="list-one-title-name">' + this.escapeHtml(handPoint.handPointName) + '</div>' +
                '</div>' +
                '<div class="list-one-con">' +
                '<div class="list-one-con-wrapper">' +
                '<div class="list-one-con-content" style="' + scrollWrapperStyle + '">' +
                scrollContent +
                '</div></div></div></div>';
        },

        // 请求数据
        loadData: function () {
            const self = this;
            const searchObj = new URLSearchParams(window.location.search);
            const queryData = {
                segNo: searchObj.get('segNo') || 'JC000000',
                serviceId: 'S_LI_RL_01381'
            };


            $.ajax({
                type: "post",
                contentType: "application/json",
                url: ytjServerUrl,
                async: true,
                data: JSON.stringify(queryData),
                success: function (data) {
                    try {
                        if (!data || !data.__sys__ || data.__sys__.status == -1) {
                            self.handleError('数据请求失败', data.__sys__ && data.__sys__.msg);
                            return;
                        }

                        self.updateCounters(data);
                        self.state.rawData = self.enhanceDataForDisplay(data);
                        self.applyLayout();
                    } catch (error) {
                        self.handleError('数据处理失败', error.message);
                    }
                },
                error: function (xhr, status, error) {
                    self.handleError('网络请求失败', error);
                }
            });
        },

        // 更新计数器 - 统计车辆状态
        updateCounters: function (data) {
            const list = data.list || [];
            const allocVehicleList = data.allocVehicleList || [];

            let workingCount = 0; // 正在作业
            let waitingCount = 0; // 等待作业

            // 统计正在作业和等待作业的车辆数量
            list.forEach(function (item) {
                if (item.status === 20) { // 正在作业
                    workingCount++;
                } else if (item.status === 10) { // 等待作业
                    waitingCount++;
                }
            });

            // 无计划车辆数量
            const noPlanCount = allocVehicleList.length;

            // 更新页面显示
            $('#workingCount').text(workingCount);
            $('#waitingCount').text(waitingCount);
            $('#noPlanCount').text(noPlanCount);

            // 兼容旧版本ID
            $('#waitCon').text(waitingCount);
            $('#workCon').text(workingCount);
        },

        // 数据处理函数 - 已移除测试数据
        enhanceDataForDisplay: function (data) {
            // 直接返回原始数据，不添加测试数据
            return data;
        },

        // 错误处理
        handleError: function (message, error) {
            console.error(message, error);

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: message,
                    text: error || '请稍后重试',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            } else {
                alert(message + ': ' + (error || '请稍后重试'));
            }
        },

        // 应用布局设置
        applyLayout: function () {
            const $left = $('#con-left');
            const $right = $('.con-right');

            // 清除旧样式并设置为全部展示模式
            $left.removeClass('layout-9 layout-16 layout-all visible-layout-9 visible-layout-16');
            $left.addClass('layout-all');

            // 重要：清空容器内容
            $left.empty();

            // 显示右侧面板
            this.showRightPanel($left, $right);

            this.processDoorData(this.state.rawData);
            this.startAutoScroll();
        },

        // 显示右侧面板
        showRightPanel: function ($left, $right) {
            $right.show();
            $left.css('width', '85%');  // 增加左侧区域宽度
            $right.css('width', '15%'); // 减少右侧区域宽度

            // 更新右侧面板内容
            this.updateRightPanelContent();
        },

        // 更新右侧面板内容
        updateRightPanelContent: function () {
            if (this.state.rawData) {
                this.lineUpAndCallForNumbers(this.state.rawData.listCallNum || []);
                this.getNotPlace(this.state.rawData.allocVehicleList || []);
            }
        },

        // 处理门点数据
        processDoorData: function (data) {
            if (!data) return;

            const handPointIdList = data.handPointIdList || [];
            const list = data.list || [];

            this.state.filteredHandPoints = handPointIdList;
            this.state.bullList = list;

            // 直接渲染所有门点
            this.renderAllDoors();
        },

        // renderCurrentPage函数已移除，只使用全部展示模式

        // 渲染所有门点 - 修复全部展示模式
        renderAllDoors: function () {
            const self = this;
            const html = this.state.filteredHandPoints.map(function (handPoint) {
                return self.generateDoorHTML(handPoint);
            }).join('');
            // 使用page-container包装器
            const $container = $('#con-left');
            const $newPage = $('<div class="page-container active"></div>').html(html);
            $container.html($newPage);
        },

        // 叫号数据 - 改进安全性，先清空容器
        lineUpAndCallForNumbers: function (data) {
            const self = this;

            // 先过滤掉钢材卸货类型，再生成HTML保证序号连续
            const validData = data.filter(function (item) {
                const businessType = self.escapeHtml(item.businessType || '');
                return businessType !== '钢材卸货';
            });

            // 限制叫号显示数量，避免滚动
            const maxCallItems = 8; // 最多显示8个叫号项目（右侧面板变小，项目也变小了）
            const limitedValidData = validData.slice(0, maxCallItems);

            const limitedHtml = limitedValidData.map(function (item, index) {
                const customerName = self.formatCustomerName(item.customerName);
                const startOfTransport = self.formatStartOfTransport(item.startOfTransport);
                const vehicleNo = self.escapeHtml(item.vehicleNo || '');
                const targetHandPointName = self.escapeHtml(item.targetHandPointName || '');
                const packCount = item.waitingTime || 0;

                const serialNumber = index + 1;

                return '<div class="all-con-one" style="position: relative; width: 100%; padding: 4px 6px; margin: 1px 0; border-radius: 3px; background-color: #E67E22; color: #FFFFFF; display: flex; align-items: center; min-height: 35px;">' +
                    '<span style="position: absolute; top: 50%; left: 6px; transform: translateY(-50%); font-size: 12px; color: #E67E22; font-weight: bold; width: 20px; height: 20px; background-color: #FFFFFF; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 1px 2px rgba(0,0,0,0.2);">' + serialNumber + '</span>' +
                    '<div style="margin-left: 32px; font-size: 12px; font-weight: bold; color: #FFFFFF; flex: 1; word-wrap: break-word; line-height: 1.2;">' +
                    vehicleNo + customerName + startOfTransport + ' → ' + targetHandPointName + ' - ' + packCount + '分' +
                    '</div>' +
                    '</div>';
            }).join('');

            let finalHtml = limitedHtml;

            // 先清空再设置内容 - 修复数据累积问题
            $('#all-con-content-list').html(finalHtml);
        },

        // 已配单未进场 - 先清空容器
        getNotPlace: function (data) {
            const self = this;

            // 限制无计划车辆显示数量，避免滚动
            const maxNoPlanItems = 12; // 最多显示12个无计划车辆（项目变小了，可以显示更多）
            const limitedData = data.slice(0, maxNoPlanItems);

            const html = limitedData.map(function (item, index) {
                const vehicleNo = self.escapeHtml(item.vehicleNo || '');
                const serialNumber = index + 1;
                let totalWaitingTime = item.totalWaitingTime;
                if (!totalWaitingTime || totalWaitingTime == 0) {
                    totalWaitingTime = item.waitingTime;
                }

                return '<div class="all-con-one" style="position: relative; width: 100%; padding: 4px 6px; margin: 1px 0; border-radius: 3px; background-color: #6C757D; color: #FFFFFF; display: flex; align-items: center; min-height: 32px;">' +
                    '<span style="position: absolute; top: 50%; left: 6px; transform: translateY(-50%); font-size: 12px; color: #6C757D; font-weight: bold; width: 20px; height: 20px; background-color: #FFFFFF; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 1px 2px rgba(0,0,0,0.2);">' + serialNumber + '</span>' +
                    '<div style="margin-left: 32px; font-size: 12px; font-weight: bold; color: #FFFFFF; flex: 1; word-wrap: break-word; line-height: 1.2;">' +
                    vehicleNo + '-' + totalWaitingTime + '分' +
                    '</div>' +
                    '</div>';
            }).join('');

            // 先清空再设置内容 - 修复数据累积问题
            $('#all-con-content-right-list').html(html);
        },

        // 启动自动滚动
        startAutoScroll: function () {
            const self = this;
            this.stopAutoScroll();

            // 定期刷新数据
            // this.state.autoScrollInterval = setInterval(function () {
            //     self.loadData();
            // }, this.CONFIG.REFRESH_INTERVAL);
        },

        // 停止自动滚动
        stopAutoScroll: function () {
            if (this.state.autoScrollInterval) {
                clearInterval(this.state.autoScrollInterval);
                this.state.autoScrollInterval = null;
            }
        }
    };

    // 初始化应用
    window.BulletinBoardApp.init();

    // 页面卸载时清理资源
    $(window).on('beforeunload', function () {
        if (window.BulletinBoardApp) {
            window.BulletinBoardApp.stopAutoScroll();
            // 清理window resize监听器
            $(window).off('resize');
        }
    });
});

