<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="清单信息" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-overhaulPlanId" cname="检修计划单号" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFSelect ename="inqu_status-0-overhaulSource" cname="检修来源" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P026"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFDateSpan startName="inqu_status-0-overhaulStartDate"
                                   endName="inqu_status-0-overhaulEndDate" readonly="true"
                                   startCname="计划检修日期(起)" endCname="计划检修日期(止)"
                                   ratio="3:3" format="yyyy-MM-dd">
                    </EF:EFDateSpan>
                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                                     ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>


                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false"
                                     containerId="deviceInfoMainQuery" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>
                    <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                                   endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                                   startCname="创建时间(起)" endCname="创建时间(止)"
                                   ratio="3:3" format="yyyy-MM-dd">
                    </EF:EFDateSpan>

                </div>
                <div class="row">
                    <EF:EFSelect ename="inqu_status-0-overhaulPlanStatus" cname="状态" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P023" filter="A"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="inqu_status-0-voucherNum" cname="依据凭单" placeholder="模糊条件"
                                colWidth="3"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="查询结果">
                <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" width="130"
                                 align="center"/>
                    <EF:EFComboColumn ename="overhaulPlanStatus" cname="状态" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P023"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="overhaulSource" cname="检修来源" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P026"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="voucherNum" cname="依据凭单" width="130" align="center" enable="false"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
                    <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
                    <EF:EFComboColumn ename="overhaulQuality" cname="检修性质" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P024"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="implementManName" cname="实施人" align="center" width="70"/>
                    <EF:EFColumn ename="overhaulStartDate" cname="计划检修日期(起)" align="center"/>
                    <EF:EFColumn ename="overhaulEndDate" cname="计划检修日期(止)" align="center"/>
                    <EF:EFColumn ename="overhaulNumber" cname="计划检修人数" align="right" width="100"/>
                    <EF:EFColumn ename="overhaulImplementDate" cname="检修实施日期" align="center" width="100"/>
                    <EF:EFColumn ename="actualOverhaulNumber" cname="实际检修人数" align="right" width="100"/>
                    <EF:EFColumn ename="actualsRevisor" cname="检修实绩操作人" align="center" width="110"/>
                    <EF:EFColumn ename="actualsRevisorName" cname="检修实绩操作人姓名" align="center" width="140"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="详细信息" id="info-2">
            <EF:EFRegion id="detail" title="详细信息">
                <EF:EFInput ename="detail_status-0-uuid" cname="UUID" type="hidden"/>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-unitCode" readonly="true" cname="业务单元代码"
                                colWidth="3"/>
                    <EF:EFSelect readonly="true" ename="detail_status-0-segNo" cname="业务单元简称" colWidth="3">
                        <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-overhaulPlanId" readonly="true" cname="检修计划单号"
                                colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-voucherNum" readonly="true" cname="依据凭单" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-equipmentName" readonly="true" cname="设备名称" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-deviceCode" readonly="true" cname="分部设备代码" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-deviceName" readonly="true" cname="分部设备名称" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput readonly="true" ename="detail_status-0-overhaulProject" cname="计划检修项目"
                                colWidth="12" ratio="1:11" type="textarea"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-actualLegacyProject" cname="实际检修项目"
                                colWidth="12" ratio="1:11" type="textarea"/>
                </div>
                <div class="row">
                    <EF:EFDatePicker ename="detail_status-0-overhaulImplementDate" required="true"
                                     cname="检修实施日期" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-actualOverhaulTime" required="true" cname="实际检修时间(min)"
                                colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                    <EF:EFInput ename="detail_status-0-actualOverhaulNumber" required="true" cname="实际检修人数"
                                colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                </div>
                <div class="row">
                    <EF:EFSelect required="true" ename="detail_status-0-isHot" cname="是否动火"
                                 colWidth="3">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFSelect>
                    <EF:EFButton ename="uploadHot" cname="上传动火手续"/>
                </div>
                <div class="row">
                    <EF:EFSelect required="true" ename="detail_status-0-isComplete" cname="是否完成" colWidth="3">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFSelect>
                    <EF:EFSelect required="true" ename="detail_status-0-isConformStandard" cname="是否符合标准"
                                 colWidth="3">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-relevantMeasures" cname="相关措施"
                                colWidth="3"/>
                    <EF:EFSelect ename="detail_status-0-offlinePartsGone" cname="下线零件去向" colWidth="3">
                        <EF:EFOption label="" value=""/>
                        <EF:EFCodeOption codeName="P027"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-overhaulSuggestions" cname="主要问题及建议"
                                colWidth="12" ratio="1:11" type="textarea"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-overhaulLegacyProject" cname="待处理及遗留问题"
                                colWidth="12" ratio="1:11" type="textarea"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-overhaulSummarize" cname="综合评价及总结"
                                colWidth="12" ratio="1:11" type="textarea"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="inqu_status2" title="查询条件" hidden="hidden">
                <EF:EFInput ename="inqu_status2-0-voucherNum" cname="检修计划号" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-unitCode" cname="业务单元代码" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-segNo" cname="系统套账" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-segName" cname="业务单元简称" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-eArchivesNo" cname="设备代码" type="hidden"/>
            </EF:EFRegion>
            <EF:EFRegion id="zc_detail" title="资材备件领用">
                <EF:EFGrid blockId="zc_detail" autoDraw="no" serviceName="VGDM0804" sort="all"
                           queryMethod="query">
                    <EF:EFColumn ename="uuid" cname="uuid" primaryKey="true" hidden="true"/>
                    <EF:EFColumn ename="inventoryUuid" cname="库存uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" readonly="true"
                                 hidden="true" enable="false" align="center"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" readonly="true"
                                 hidden="true" enable="false" align="center"/>
                    <EF:EFComboColumn ename="stuffReceivingStatus" cname="状态" enable="false" align="center"
                                      textField="textField" valueField="valueField" width="70">
                        <EF:EFCodeOption codeName="P061"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="stuffCode" cname="资材代码" readonly="true"
                                 enable="false" align="center"/>
                    <EF:EFColumn ename="stuffName" cname="资材名称" align="center" enable="false" width="120"/>
                    <EF:EFColumn ename="specDesc" cname="规格" readonly="true"
                                 enable="false" width="150"/>
                    <EF:EFColumn ename="usingWgt" cname="申请量" required="true" width="70"
                                 align="center"/>
                    <EF:EFColumn ename="measureId" cname="计量单位" readonly="true" width="70"
                                 enable="false" align="center"/>
                    <EF:EFColumn ename="stuffUsage" cname="用途" readonly="true" width="200"
                                 enable="false"/>
                    <EF:EFColumn ename="purContractNum" cname="采购合同号" readonly="true" width="160" align="center"
                                 enable="false"/>
                    <EF:EFColumn ename="purOrderNum" cname="采购合同子项号" readonly="true" width="160" align="center"
                                 enable="false"/>
                    <EF:EFColumn ename="locationId" cname="库位代码" enable="false" width="100" align="center"
                                 hidden="true"/>
                    <EF:EFColumn ename="locationName" cname="库位名称" enable="false" width="100" align="left"
                                 hidden="true"/>
                    <EF:EFColumn ename="warehouseCode" cname="仓库代码" readonly="true"
                                 enable="false" align="center" width="90"/>
                    <EF:EFColumn ename="warehouseName" cname="仓库名称" readonly="true"
                                 enable="false" align="center" width="200"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="80"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFRegion id="result5" title="检查项目">
                <EF:EFInput ename="inqu5_status-0-segNo" cname="UUID" type="hidden"/>
                <EF:EFInput ename="inqu5_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                <EF:EFGrid blockId="result5" autoDraw="no" sort="all" serviceName="VGDM0807" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                    <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                    <EF:EFColumn ename="sortIndex" cname="序号" width="60" valueType="N"
                                 data-rules="positive_integer" align="center" enable="false"/>
                    <EF:EFColumn ename="itemName" cname="项目" width="100" enable="false"/>
                    <EF:EFColumn ename="itemContent" cname="内容" width="120" enable="false"/>
                    <EF:EFColumn ename="workStandard" cname="作业标准" width="200" enable="false"/>
                    <EF:EFColumn ename="checkMethod" cname="检修方法" width="170" enable="false"/>
                    <EF:EFColumn ename="pictureUrl" cname="作业图示" hidden="true"/>
                    <EF:EFColumn ename="pictureOperate" sort="flase" enable="false" cname="作业图示" width="100"/>
                    <EF:EFColumn ename="safeItem" cname="安全注意事项" width="120" enable="false"/>
                    <EF:EFComboColumn ename="isNormal" cname="是否异常" required="true" align="center" width="80">
                        <EF:EFCodeOption codeName="P020"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="actualsRemark" cname="检修记录" width="200" required="true" editType="textarea"/>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                 width="100"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                 width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFRegion id="result6" title="维修项目">
                <EF:EFInput ename="inqu6_status-0-segNo" cname="UUID" type="hidden"/>
                <EF:EFInput ename="inqu6_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                <EF:EFInput ename="inqu6_status-0-itemType" cname="UUID" type="hidden"/>
                <EF:EFGrid blockId="result6" autoDraw="no" sort="all" serviceName="VGDM0805" queryMethod="query6" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                    <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                    <EF:EFColumn ename="itemType" cname="项目类型" enable="false" hidden="true"/>
                    <EF:EFColumn ename="sortIndex" cname="序号" width="70" valueType="N"
                                 data-rules="positive_integer" align="center" enable="false" />
                    <EF:EFColumn ename="itemName" cname="项目名称" enable="false"  width="300"/>
                    <EF:EFColumn ename="responsePerson" cname="责任人" width="100" enable="false" />
                    <EF:EFColumn ename="assistPerson" cname="协助人" width="100" enable="false" />
                    <EF:EFColumn ename="beginMinute" cname="开始时间" width="150" editType="datetime"
                                 dateFormat="HH:mm" parseFormats="['HH:mm']" enable="false"  align="center"/>
                    <EF:EFColumn ename="endMinute" cname="结束时间" width="150" editType="datetime"
                                 dateFormat="HH:mm" parseFormats="['HH:mm']" enable="false"  align="center"/>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                 width="100"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                 width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFRegion id="result7" title="给油脂记录">
                <EF:EFInput ename="inqu7_status-0-segNo" cname="UUID" type="hidden"/>
                <EF:EFInput ename="inqu7_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                <EF:EFGrid blockId="result7" autoDraw="no" sort="all" serviceName="VGDM0808" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                    <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                    <EF:EFColumn ename="deviceLocation" cname="设备位置" required="true" width="120"/>
                    <EF:EFColumn ename="lubricatePoint" cname="润滑点位置" width="120" enable="false"/>
                    <EF:EFColumn ename="lubricateQty" cname="润滑点数量" width="100" valueType="N"
                                 data-rules="positive_integer" align="center" enable="false"/>
                    <EF:EFColumn ename="addQty" cname="加油量(下)" width="100" valueType="N"
                                 data-rules="positive_integer" align="center" enable="false"/>
                    <EF:EFColumn ename="greaseSpec" cname="油脂型号" width="120" enable="false"/>
                    <EF:EFColumn ename="lubricateCycle" cname="润滑周期" width="120" enable="false"/>
                    <EF:EFColumn ename="lubricateRecord" cname="润滑记录" width="120" required="true"/>
                    <EF:EFColumn ename="remark" cname="备注" width="120" />
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                 width="100"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                 width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="附件信息">
                <EF:EFInput ename="inqu2_status-0-relevanceId" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-segNo" type="hidden"/>
                <div style="display: none;">
                    <EF:EFInput ename="fileForm" type="file"/>
                    <EF:EFInput ename="fileForm1" type="file"/>
                </div>
                <EF:EFGrid blockId="result2" autoDraw="no" readonly="true"
                           queryMethod="queryFile" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                    <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                    <EF:EFColumn ename="relevanceId" cname="检修计划单号" width="120" align="center"/>
                    <EF:EFComboColumn ename="relevanceType" cname="附件类型" enable="false" align="center" width="70">
                        <EF:EFOption label="检修相关" value="VGDM0801"/>
                        <EF:EFOption label="动火手续" value="VGDM08H"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                    <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                    <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                    <%--                    <EF:EFColumn ename="fifleSize" cname="文件大小"/>--%>
                    <EF:EFColumn ename="fifleType" cname="文件类型"/>
                    <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VFPM0101" id="inventoryInfo" width="90%" height="60%" title="资材库存查询"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>
