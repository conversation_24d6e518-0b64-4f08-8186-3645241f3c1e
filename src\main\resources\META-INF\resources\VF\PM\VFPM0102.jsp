<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-windowId" cname="弹窗ID" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-deptId" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-warehouseCode" cname="仓库代码" colWidth="3"
                             readonly="true" clear="false" containerId="warehouseInfo" originalInput="true"
                             center="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-warehouseName" cname="仓库名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-stuffCode" cname="资材代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-stuffName" cname="资材名称" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="single, row" readonly="true" sort="all"
                   serviceName="VFPM0101">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="stuffCode" cname="资材代码" align="center" enable="false" width="100"/>
            <EF:EFColumn ename="stuffName" cname="资材名称" align="center" enable="false" width="120"/>
            <EF:EFColumn ename="spec" cname="规格" enable="false" width="150"/>
            <%--            <EF:EFColumn ename="stuffUsage" cname="用途" enable="false" align="center" width="150"/>--%>
            <%-- <EF:EFColumn ename="usingWgt" cname="已申请量" enable="false" align="right" width="70"/>
            <EF:EFColumn ename="wlStockQty" cname="库存量" enable="false" align="right" width="70"/> --%>
            <EF:EFColumn ename="wlStockQty" cname="库存量" enable="false" align="right" width="70"/>
            <EF:EFComboColumn ename="measureId" cname="计量单位" enable="false"
                              align="center" width="70">
                <EF:EFCodeOption codeName="P487"/>
            </EF:EFComboColumn>
            <%--            <EF:EFColumn ename="locationId" cname="库位代码" align="center" enable="false" width="100"/>--%>
            <%--            <EF:EFColumn ename="locationName" cname="库位名称" align="center" enable="false" width="100"/>--%>
            <%--            <EF:EFColumn ename="stuffContractId" cname="采购合同号" align="center" enable="false" format="{0}"--%>
            <%--                         width="160"/>--%>
            <%--            <EF:EFColumn ename="stuffContractSubid" cname="采购合同子项号" align="center" enable="false" format="{0}"--%>
            <%--                         width="160"/>--%>
            <EF:EFColumn ename="stuffUnitPriceTaxed" cname="参考含税单价" align="right" enable="false" format="{0:N6}"
                         width="100"/>
            <%-- <EF:EFColumn ename="stuffNoTaxAmount" cname="不含税单价" format="{0:n2}" align="right" enable="false"
                         width="100"/>
            <EF:EFColumn ename="stuffNoTaxTotalAmount" cname="不含税金额" format="{0:n2}" align="right" enable="false"/>
            <EF:EFColumn ename="stuffUnitPriceTaxed" cname="参考含税单价" align="right" enable="false" format="{0:N6}"
                         width="100"/>
            <EF:EFColumn ename="stuffTaxPrice" cname="含税金额" align="right" enable="false" format="{0:N2}"
                         width="100"/> --%>
            <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" enable="false" width="100"/>
            <EF:EFColumn ename="warehouseName" cname="仓库名称" enable="false" width="220"/>
            <EF:EFColumn ename="providerCode" cname="供应商代码" align="center" enable="false" width="100"/>
            <EF:EFColumn ename="providerCName" cname="供应商名称" align="center" enable="false" width="220"/>
            <EF:EFColumn ename="firstDate" cname="入库日期" align="center" enable="false" width="160"/>
            <EF:EFColumn ename="applyInfo" cname="申请人" align="center" enable="false"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseInfo" width="90%" height="80%"/>
</EF:EFPage>