<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>工贸一体机</title>
        <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
        <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css">
        <style type="text/css">
            /* 添加触摸优化样式 */
            /*#signatureCanvas {*/
            /*    touch-action: none; */
            /*    -webkit-user-select: none;*/
            /*    user-select: none;*/
            /*}*/
        </style>
    </head>
    <body>
        <div class="wrapper">
            <div class="header">
                <div id="logo" class="logo-baosight"></div>
                <!--            <div class="title">工贸一体机登记</div>-->
                <div class="header-return">
                    <button class="return-home-btn" onclick="returnHome()">返回主页</button>
                </div>
            </div>
            <div class="nav">
                <div class="navbox">
                    <ul>
                        <li>1.登记车牌号</li>
                        <li class="arrow"></li>
                        <li id="isTypeSpan">2.选择业务</li>
                        <li class="arrow"></li>
                        <li class="fontblue">3.进厂登记</li>
                    </ul>
                </div>
            </div>
            <div class="container">
                <div class="main">
                    <div class="information5-zdy">
                        <ul>
                            <li>
                                <span>车牌号：<i id="vehicle_id"></i></span>
                            </li>
                            <li id="load_bill_id_li">
                                <span>提单号：<i id="bill_id"></i></span>
                            </li>

                            <li id="load_business_type_li">
                                <span>业务类型：<i id="load_business_type_id"></i></span>
                            </li>
                            <li>
                                <span>厂&nbsp;&nbsp;&nbsp;&nbsp;区：<i id="factoryId"></i></span>
                            </li>
                        </ul>
                    </div>
                    <div class="btn2">
                        <button class="read" onclick="javascript :history.back(-1);">上一步</button>
                        <button class="next" onclick="queryDriver()">入厂登记</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="tanchuang">
            <div class="tanchuang_box">
                <div class="tanchuang_top">
                    <div id="tanchuang_span">预约单</div>
                    <select id="province" name="cars" class="ipt2" style="width: 100%"></select>
                </div>
                <div class="tanchuang_bottom">
                    <button onclick="$('.tanchuang').hide();">取消</button>
                    <button id="confirmNoReservation">无预约登记</button>
                    <button class="sure">确定</button>
                </div>
            </div>
        </div>
                <video id="video" width="640" height="480" autoplay style="display: none;"></video>
                <canvas id="canvas" width="640" height="480" style="display: none;"></canvas>
        <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
        <script type="text/javascript" src="js/<EMAIL>"></script>
        <script type="text/javascript" src="js/config.js"></script>
        <script>
            var businessType = localStorage.getItem("Business_type");
            var Business_type_name = localStorage.getItem("Business_type_name");
            var vehicle_id = localStorage.getItem("vehicle_id");
            var hand_big_type = localStorage.getItem("hand_big_type");
            var factoryId = localStorage.getItem("factoryId");
            var factoryName = localStorage.getItem("factoryName");
            var segNo = localStorage.getItem("segNo");
            var bill_id = localStorage.getItem("bill_id"); //提单号
            var handType = localStorage.getItem("handType");
            var reservationNumber = '';
            var errorMsg = '';
            // 获取页面元素
            const video = document.getElementById('video');
            const canvas = document.getElementById('canvas');
            const context = canvas.getContext('2d');
            var facialImage = '';

            window.onload = function () {
                if (navigator && navigator.mediaDevices) {
                    // 请求摄像头访问权限
                    navigator.mediaDevices.getUserMedia({ video: true })
                        .then(stream => {
                            // 将视频流绑定到video元素
                            video.srcObject = stream;
                            video.play();
                        })
                        .catch(err => {
                            console.error('无法访问摄像头:', err);
                        });
                }

                $("#vehicle_id").html(vehicle_id);
                $("#bill_id").html(bill_id);
                $("#factoryId").html(factoryName);
                $("#load_business_type_id").html(Business_type_name);
                if (bill_id) {
                    $("#load_bill_id_li").show();
                    $("#load_business_type_li").hide();
                } else {
                    $("#load_bill_id_li").hide();
                    $("#load_business_type_li").show();
                }
            };

            // 返回上一步清空业务类型
            $(".read").click(function () {
                localStorage.setItem("Business_type", "");
                localStorage.setItem("Business_type_name", "");
                localStorage.setItem("bill_id", "");
            });

            // 第一步：查询司机
            function queryDriver() {
                showLoading("查询司机中");
                let param = {
                    segNo, //账套
                    businessType, //业务类型
                    driverIdentity: localStorage.getItem("idCard"), //司机姓名
                    driverTel: localStorage.getItem("tel"),
                    driverName: localStorage.getItem("idName"), //司机姓名
                    vehicleNo: localStorage.getItem("vehicle_id"), //车牌号
                    serviceId: "S_LI_RL_0073",

                };

                $.ajax({
                    url: ytjServerUrl, // 后台接口地址
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(param),
                    success: (data) => {
                        closeLoading();
                        if (!data || !data.__sys__ || data.__sys__.status == -1) {
                            Swal.fire({
                                title: data.__sys__.msg,
                                icon: "error",
                                confirmButtonText: "确定",
                            }).then(() => {
                                // 钢材卸货继续往下走
                                if (businessType == '10') {
                                    showCountdownModal();
                                }
                            });
                            return;


                        }
                        showCountdownModal();
                    },
                    error: () =>
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        }),
                });
            }

            // 第二步：显示提示信息弹框
            function showCountdownModal() {
                showLoading("查询安全签章中");

                const query = {
                    segNo,
                    serviceId: 'S_LI_RL_0065',
                    driverIdentity: localStorage.getItem("idCard"), //司机姓名
                    driverTel: localStorage.getItem("tel"),
                    driverName: localStorage.getItem("idName"), //司机姓名
                };

                $.ajax({
                    type: "post",
                    contentType: "application/json",
                    url: ytjServerUrl,
                    data: JSON.stringify(query),
                    success: function (data) {
                        closeLoading();

                        if (!data || !data.__sys__ || data.__sys__.status == -1 || !data.list) {
                            Swal.fire({
                                title: data.__sys__.msg,
                                icon: "error",
                                confirmButtonText: "确定",
                            });
                            return;
                        }

                        // 按照返回的list顺序显示弹框
                        const notificationList = data.list;
                        // 直接确认
                        if (notificationList.length == 0) {
                            getTransportation('');
                            return;
                        }

                        let currentIndex = 0; // 当前弹框的索引

                        function showNextModal() {
                            if (currentIndex >= notificationList.length) {
                                // 如果是最后一个，打开签名板
                                showSignatureModal();
                                return;
                            }

                            const currentNotification = notificationList[currentIndex];
                            let { countdownDuration, notificationTitle, notificationText, bold, addRed, addUnderline } = currentNotification;

                            let timeLeft = parseInt(countdownDuration, 10); // 倒计时时间
                            let scrolledToBottom = false; // 是否滚动到底部
                            let countdownComplete = false; // 是否完成倒计时

                            // **步骤1：预处理，过滤掉空字符串或纯空格**
                            const getValidSections = (str) => {
                                return (str || "")
                                    .split(/[,，]/) // 中英文逗号分割
                                    .map(section => section.trim()) // 去除首尾空格
                                    .filter(section => section.length > 0); // 过滤空字符串
                            };

                            const boldSections = getValidSections(bold);
                            const redSections = getValidSections(addRed);
                            const underlineSections = getValidSections(addUnderline);

                            // **步骤2：合并所有需要样式的文本（避免重复处理）**
                            const styleMap = new Map(); // { text: Set<styles> }

                            // 通用处理函数
                            const addToStyleMap = (sections, styleType) => {
                                sections.forEach(text => {
                                    if (!styleMap.has(text)) {
                                        styleMap.set(text, new Set());
                                    }
                                    styleMap.get(text).add(styleType);
                                });
                            };

                            addToStyleMap(boldSections, "bold");
                            addToStyleMap(redSections, "red");
                            addToStyleMap(underlineSections, "underline");

                            // **步骤3：按长度降序排序（避免短文本覆盖长文本）**
                            const sortedSections = Array.from(styleMap.keys())
                                .sort((a, b) => b.length - a.length) // 关键排序逻辑
                                .map(text => ({
                                    text,
                                    regex: new RegExp(`(${text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, "g"),
                                    styles: styleMap.get(text)
                                }));

                            // **步骤4：单次替换，生成复合样式**
                            let processedText = notificationText;
                            sortedSections.forEach(({ text, regex, styles }) => {
                                processedText = processedText.replace(regex, (match) => {
                                    const styleArr = [];
                                    if (styles.has("bold")) styleArr.push("font-weight: bold");
                                    if (styles.has("red")) styleArr.push("color: red");
                                    if (styles.has("underline")) styleArr.push("text-decoration: underline");
                                    return `<span style="${styleArr.join("; ")}">${match}</span>`;
                                });
                            });

                            // **步骤5：最后处理换行**
                            processedText = processedText.replace(/\n/g, "<br>");

                            Swal.fire({
                                title: notificationTitle,
                                width: '60%',
                                html: `
                        <div id="content" style="
                            height: 500px;
                            overflow-y: auto;
                            border: 1px solid #ddd;
                            padding: 10px;
                            text-align: left;
                            margin-bottom: 20px;
                        ">
                            ${processedText}
                        </div>
                        <button id="confirmButton" disabled style="
                            margin-top: 10px;
                            padding: 10px 20px;
                            font-size: 16px;
                            background-color: #999;
                            color: #fff;
                            border: none;
                            cursor: not-allowed;
                        ">
                            请等待 ${timeLeft} 秒
                        </button>
                    `,
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                didOpen: () => {
                                    const confirmButton = document.getElementById("confirmButton");
                                    const contentDiv = document.getElementById("content");

                                    // 检查内容是否超出容器高度
                                    const isScrollable = contentDiv.scrollHeight > contentDiv.clientHeight;

                                    if (!isScrollable) {
                                        // 如果内容不需要滚动，则直接标记为已滚动到底部
                                        scrolledToBottom = true;
                                    }

                                    contentDiv.addEventListener("scroll", () => {
                                        const tolerance = 5; // 容忍范围
                                        const isScrolledToBottom =
                                            Math.ceil(contentDiv.scrollTop + contentDiv.clientHeight) >= Math.floor(contentDiv.scrollHeight) - tolerance;
                                        if (isScrolledToBottom) {
                                            scrolledToBottom = true;
                                            Swal.resetValidationMessage();
                                            enableButtonIfReady(confirmButton);
                                        } else {
                                            scrolledToBottom = false;
                                            if (countdownComplete) {
                                                Swal.showValidationMessage('请滚动到底部后再点击按钮');
                                            }
                                        }
                                    });

                                    // 倒计时逻辑
                                    const timerInterval = setInterval(() => {
                                        timeLeft--;
                                        confirmButton.textContent = `请等待 ${timeLeft} 秒`;

                                        if (timeLeft <= 0) {
                                            clearInterval(timerInterval);
                                            countdownComplete = true;
                                            if (!scrolledToBottom && isScrollable) {
                                                // 倒计时结束且需要滚动但未滚动到底部，显示验证提示信息
                                                Swal.showValidationMessage('请滚动到底部后再点击按钮');
                                            }
                                            enableButtonIfReady(confirmButton);
                                        }
                                    }, 1000);

                                    function enableButtonIfReady(button) {
                                        // 检查是否倒计时结束并且滚动到底部
                                        if (countdownComplete && (scrolledToBottom || !isScrollable)) {
                                            button.disabled = false;
                                            button.style.backgroundColor = "#3085d6";
                                            button.style.color = "#fff";
                                            button.style.cursor = "pointer";
                                            button.textContent = "确定";
                                            button.addEventListener("click", () => {
                                                Swal.close();
                                                currentIndex++; // 显示下一个弹框
                                                showNextModal();
                                            });
                                        }
                                    }
                                },
                            });


                        }

                        // 显示第一个弹框
                        showNextModal();
                    },
                    error: () =>
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        }),
                });
            }

            // 第三步：提示信息框都弹出完成之后, 显示签名板
            function showSignatureModal() {
                takePhoto();
                Swal.fire({
                    title: '签名确认',
                    width: '700px',
                    html: `
            <div class="signature-container">
                <canvas id="signatureCanvas" style="border: 1px solid #ccc; width: 100%; height: 200px;touch-action: none;
                -webkit-user-select: none;user-select: none;"></canvas>
                <button id="clearSignature" class="clear-signature swal2-cancel" style="
                    margin-top: 10px;
                    padding: 10px 20px;
                    font-size: 16px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                ">清空签名</button>
                <div style="margin-top: 10px;">
                    <label>
                        <input type="checkbox" id="confirmRead"> 已认真阅读并遵守
                    </label>
                </div>
            </div>
        `,
                    showCancelButton: true,
                    allowOutsideClick: false,
                    confirmButtonText: '提交',
                    cancelButtonText: '取消',
                    preConfirm: () => {
                        const confirmRead = document.getElementById('confirmRead').checked;
                        const isSignatureEmpty = isCanvasBlank();

                        if (!confirmRead) {
                            Swal.showValidationMessage('请勾选“已认真阅读并遵守”');
                            return false;
                        }
                        if (isSignatureEmpty) {
                            Swal.showValidationMessage('签名板不能为空，请签名');
                            return false;
                        }

                        // 将签名板内容转为 Base64
                        const signatureBase64 = canvas.toDataURL('image/png'); // 默认转为 PNG 格式
                        return signatureBase64;
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        const signatureData = result.value; // 签名的 Base64 数据

                        // 调用后台接口
                        sendSignatureToBackend(signatureData);
                    }
                });

                // 初始化签名板
                const canvas = document.getElementById('signatureCanvas');
                const ctx = canvas.getContext('2d');

                // 设置canvas实际分辨率（重要！）
                canvas.width = 800;  // 根据实际需要调整
                canvas.height = 400;
                // 设置CSS尺寸（保持与页面布局一致）
                canvas.style.width = '100%';
                canvas.style.height = '350px';

                // 设置画笔属性
                ctx.lineWidth = 5; // 画笔加粗，默认值为1，可以根据需要调整
                ctx.lineCap = 'round'; // 线条末端为圆形
                ctx.lineJoin = 'round'; // 线条连接处为圆形
                ctx.strokeStyle = '#000000'; // 画笔颜色为黑色

                let drawing = false;

                // 添加触摸事件支持
                let lastTouch = null;

                // 统一的坐标获取方法
                function getEventPos(event) {
                    const rect = canvas.getBoundingClientRect();
                    const scaleX = canvas.width / rect.width;
                    const scaleY = canvas.height / rect.height;

                    if (event.touches) {
                        return {
                            x: (event.touches[0].clientX - rect.left) * scaleX,
                            y: (event.touches[0].clientY - rect.top) * scaleY
                        };
                    } else {
                        return {
                            x: (event.clientX - rect.left) * scaleX,
                            y: (event.clientY - rect.top) * scaleY
                        };
                    }
                }

                // 新增：防止页面滚动
                document.body.style.overflow = 'hidden';
                document.body.style.touchAction = 'none';

                // 事件监听器
                function handleStart(event) {
                    event.preventDefault();
                    const pos = getEventPos(event);
                    ctx.beginPath();
                    ctx.moveTo(pos.x, pos.y);
                    lastTouch = pos;
                }

                function handleMove(event) {
                    event.preventDefault();
                    if (!lastTouch) return;

                    const pos = getEventPos(event);
                    ctx.lineTo(pos.x, pos.y);
                    ctx.stroke();
                    lastTouch = pos;
                }

                function handleEnd() {
                    lastTouch = null;
                }

                // 添加事件监听（同时支持鼠标和触摸）
                canvas.addEventListener('mousedown', handleStart);
                canvas.addEventListener('mousemove', handleMove);
                canvas.addEventListener('mouseup', handleEnd);
                canvas.addEventListener('mouseout', handleEnd);

                canvas.addEventListener('touchstart', handleStart);
                canvas.addEventListener('touchmove', handleMove);
                canvas.addEventListener('touchend', handleEnd);

                // 在弹窗关闭时恢复页面滚动
                Swal.getPopup().addEventListener('hidden', () => {
                    document.body.style.overflow = '';
                    document.body.style.touchAction = '';
                });

                document.getElementById('clearSignature').addEventListener('click', () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                });

                function isCanvasBlank() {
                    const blankCanvas = document.createElement('canvas');
                    blankCanvas.width = canvas.width;
                    blankCanvas.height = canvas.height;
                    return canvas.toDataURL() === blankCanvas.toDataURL();
                }

                // 动态监听复选框状态
                const confirmReadCheckbox = document.getElementById('confirmRead');
                confirmReadCheckbox.addEventListener('change', () => {
                    if (confirmReadCheckbox.checked) {
                        Swal.resetValidationMessage();
                    }
                });
            }

            // 第四步：签名和人脸上传
            function sendSignatureToBackend(signatureBase64) {
                showLoading("签名上传中");
                let param = {
                    serviceId: 'S_LI_RL_0068',
                    segNo,
                    file: signatureBase64,
                    file1: facialImage,
                    id: reservationNumber,
                    idCard: localStorage.getItem("idCard"), //司机姓名
                    driverName: localStorage.getItem("idName"), //司机姓名
                    tel: localStorage.getItem("tel"),
                    vehicleNo: localStorage.getItem("vehicle_id"), //车牌号
                };

                return $.ajax({
                    url: ytjServerUrl, // 后台接口地址
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(param),
                    success: (data) => {
                        closeLoading();
                        if (!data || !data.__sys__ || data.__sys__.status == -1) {
                            Swal.fire({
                                title: data.__sys__.msg,
                                icon: "error",
                                confirmButtonText: "确定",
                            });
                            return;
                        }
                        getTransportation(data.fileUUID);
                    },
                    error: () =>
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        }),
                });
            }

            // 第五步：签名上传成功之后, 查询运输公司
            function getTransportation(fileUUID) {
                showLoading("查询运输公司中");
                let param = {
                    segNo, //账套
                    businessType: localStorage.getItem("Business_type"), //业务类型
                    driverIdentity: localStorage.getItem("idCard"), //司机姓名
                    driverTel: localStorage.getItem("tel"),
                    driverName: localStorage.getItem("idName"), //司机姓名
                    vehicleNo: localStorage.getItem("vehicle_id"), //车牌号
                    serviceId: "S_LI_RL_0086",
                    voucherNumList: bill_id?.split(','),
                    // reservationNumber,
                };

                $.ajax({
                    url: ytjServerUrl, // 后台接口地址
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(param),
                    success: (data) => {
                        closeLoading();
                        if (!data || !data.__sys__ || data.__sys__.status == -1) {
                            Swal.fire({
                                title: data.__sys__.msg,
                                icon: "error",
                                confirmButtonText: "确定",
                            });
                            return;
                        }

                        if (!data.list || data.list.length == 0) {
                            CheckIn(fileUUID, '', '');
                            return;
                        }

                        if (data.list.length == 1) {
                            const {customerId, customerName} = data.list[0];
                            // selectTransportation(reservationNumber, fileUUID, customerId, customerName);
                            CheckIn(fileUUID, customerId, customerName);
                            return;
                        }

                        $(".ipt2").html("");
                        $('#tanchuang_span')[0].innerText = '所属公司';
                        let str = "<option value='0'>--本次代表哪家公司前来装卸货--</option>";
                        $(".tanchuang").show();
                        for (let i = 0; i < data.list.length; i++) {
                            str += `<option value="${data.list[i].customerId}-${data.list[i].customerName}">${data.list[i].customerId} / ${data.list[i].customerName}</option>`;
                        }
                        $(".ipt2").append(str);
                        $(".sure").click(function () {
                            const selectElement = document.getElementById("province");
                            const selectedValue = selectElement.value;
                            if (selectedValue == "0") {
                                Swal.fire({
                                    title: "请选择运输公司！",
                                    icon: "warning",
                                    confirmButtonText: "确定",
                                });
                                return;
                            }
                            const customerList = selectedValue.split('-')
                            $(".tanchuang").css("display", "none");
                            // selectTransportation(reservationNumber, fileUUID, customerList[0], customerList[1]);
                            CheckIn(fileUUID, customerList[0], customerList[1]);
                        });

                    },
                    error: () =>
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        }),
                });
            }

            // 第六步：查询车辆是否有预约单
            function CheckIn(fileUUID, customerId, customerName) {
                showLoading("查询预约单中");
                let reservation_data = {
                    segNo, //账套
                    vehicleNo: localStorage.getItem("vehicle_id"), //车牌号
                    businessType, //业务类型
                    driverIdentity: localStorage.getItem("idCard"), //司机姓名
                    driverName: localStorage.getItem("idName"), //司机姓名
                    voucherNumList: bill_id?.split(','), //司机姓名
                    factoryArea: factoryId,
                    factoryAreaName: factoryName,
                    serviceId: "S_LI_RL_0062",
                    handType,
                    driverTel: localStorage.getItem("tel"),
                    fileUUID,
                    customerId,
                    customerName
                };
                $.ajax({
                    type: "post",
                    contentType: "application/json",
                    url: ytjServerUrl,
                    data: JSON.stringify(reservation_data),
                    complete: function () {},
                    success: function (data) {
                        closeLoading();
                        if (!data.list) {

                            let str = data.__sys__.msg;

                            if (str.includes("此车辆无预约，不允许登记，登记信息已发送仓库")) {
                                Swal.fire({
                                    title: str,
                                    icon: "error",
                                    allowOutsideClick: false,
                                    showConfirmButton: false,  // 隐藏确认按钮
                                    timer: 5000,  // 设置定时器，3秒后自动关闭
                                    timerProgressBar: true  // 显示进度条
                                }).then(() => {
                                    returnHome();
                                });
                            } else {
                                Swal.fire({
                                    title: str,
                                    icon: "error",
                                    confirmButtonText: "确定",
                                    allowOutsideClick: false,
                                });
                            }

                            return;
                        }

                        const dataList = data.list;
                        if (dataList.length == 1 && !data.overFlag) {
                            var selectedValue = data.list[0].reservationNumber;
                            reservationNumber = selectedValue;
                            queryShur(selectedValue, fileUUID);
                            return;
                        }

                        $(".ipt2").html("");
                        $('#tanchuang_span')[0].innerText = '预约单';
                        let str = "<option value='0'>--请选择预约单--</option>";
                        $(".tanchuang").show();
                        for (let i = 0; i < data.list.length; i++) {
                            const formattedDate = data.list[i].reservationDate.replace(/(\d{4})(\d{2})(\d{2})/, "$1-$2-$3");
                            const dateStr = `${formattedDate} ${data.list[i].reservationTime}`;
                            str += `<option value="${data.list[i].reservationNumber}">${data.list[i].reservationNumber} / ${dateStr} /
${data.list[i].businessTypeName} / ${data.list[i].customerName} /
起始地:  ${data.list[i].startOfTransport || '无'} / 目的地: ${data.list[i].purposeOfTransport || '无'}</option>`;
                        }
                        $(".ipt2").append(str);

                        // 有预约单也支持无预约登记
                        $('#confirmNoReservation').click(() => {
                            Swal.fire({
                                title: '提示',
                                text: '您确定没有合适的预约单选择，需要做无预约登记、等待仓库审核吗？',
                                showCancelButton: true,
                                allowOutsideClick: false,
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                // reverseButtons: true
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    confirmNoRegistration(fileUUID, customerId, customerName);
                                }
                            });
                        });

                        $(".sure").click(function () {
                            const btn = this;
                            btn.disabled = true; // 立即禁用按钮
                            
                            const selectElement = document.getElementById("province");
                            const selectedValue = selectElement.value;
                            if (selectedValue == "0") {
                                Swal.fire({
                                    title: "请选择预约单！",
                                    icon: "warning",
                                    confirmButtonText: "确定",
                                });
                                return;
                            }
                            reservationNumber = selectedValue;

                            $(".tanchuang").css("display", "none");

                            // getTransportation(fileUUID);
                            queryShur(selectedValue, fileUUID);
                        });
                    },
                    error: function () {
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        });
                    },
                });
            }

            /** 第七步：入厂登记 */
            function queryShur(selectedValue,fileUUID) {
                showLoading("入厂登记中");
                let queryData = {
                    segNo, //账套
                    reservationNumber: selectedValue, //预约单号
                    voucherNumList: bill_id?.split(','), //提单号
                    // recCreator:JSON.parse(localStorage.getItem("userInfo")).data.loginName,//创建人
                    idCard: localStorage.getItem("idCard"), //身份证号
                    factoryArea: localStorage.getItem("factoryId"), //厂区
                    factoryAreaName: factoryName,
                    vehicleNo: localStorage.getItem("vehicle_id"), //车牌号
                    driverName: localStorage.getItem("idName"), //驾驶员姓名
                    checkSource: "10",
                    businessType: localStorage.getItem("Business_type"),
                    serviceId: "S_LI_RL_0058",
                    fileUUID,
                    // customerId,
                    // customerName
                };
                $.ajax({
                    type: "post",
                    contentType: "application/json",
                    url: ytjServerUrl,
                    data: JSON.stringify(queryData),
                    complete: function () {},
                    success: function (data) {
                        closeLoading();
                        if (!data || !data.__sys__.status || data.__sys__.status != 1) {
                            Swal.fire({
                                title: data.__sys__.msg,
                                icon: "error",
                                confirmButtonText: "确定",
                            });
                            return;
                        }

                        localStorage.setItem("segNo", segNo);
                        localStorage.setItem("factoryId", factoryId);
                        localStorage.setItem("factoryName", factoryName);
                        Swal.fire({
                            title: "您已登记报到成功，请关注手机叫号短信，请勿屏蔽。若是装货业务，请后续在车间仓库办公室自助终端一体机上打印提单和出库单。",
                            icon: "success",
                            showConfirmButton: false,  // 隐藏确认按钮
                            timer: 8000,  // 设置定时器，3秒后自动关闭
                            timerProgressBar: true  // 显示进度条
                        }).then(() => {
                            returnHome();
                        });
                    },
                    error: () =>
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        }),
                });
            }

            function confirmNoRegistration(fileUUID, customerId, customerName) {
                showLoading("无预约登记中");
                let reservation_data = {
                    segNo, //账套
                    vehicleNo: localStorage.getItem("vehicle_id"), //车牌号
                    businessType, //业务类型
                    driverIdentity: localStorage.getItem("idCard"), //司机姓名
                    driverName: localStorage.getItem("idName"), //司机姓名
                    voucherNumList: bill_id?.split(','), //司机姓名
                    factoryArea: factoryId,
                    factoryAreaName: factoryName,
                    serviceId: "S_LI_RL_0062",
                    handType,
                    driverTel: localStorage.getItem("tel"),
                    fileUUID,
                    customerId,
                    customerName,
                    isNoReservation: '1',
                };

                $.ajax({
                    type: "post",
                    contentType: "application/json",
                    url: ytjServerUrl,
                    data: JSON.stringify(reservation_data),
                    complete: function () {},
                    success: function (data) {
                        closeLoading();
                        if (data.__sys__.status == -1) {
                            Swal.fire({
                                title: data.__sys__.msg,
                                icon: "error",
                                confirmButtonText: "确定",
                            });
                            return;
                        }
                        Swal.fire({
                            title: "此车辆无预约，不允许登记，登记信息已发送仓库待审核！",
                            icon: "error",
                            allowOutsideClick: false,
                            showConfirmButton: false,  // 隐藏确认按钮
                            timer: 5000,  // 设置定时器，3秒后自动关闭
                            timerProgressBar: true  // 显示进度条
                        }).then(() => {
                            returnHome();
                        });
                    },
                    error: function () {
                        Swal.fire({
                            title: "网络异常, 请联系管理员!",
                            icon: "error",
                            confirmButtonText: "确定",
                        });
                    },
                });
            }

            // 自动拍照函数
            function takePhoto() {
                // 等待2秒后拍照（确保摄像头已启动）
                setTimeout(() => {
                    // 将视频帧绘制到canvas
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // 将canvas内容转换为图片(Base64)
                    const dataUrl = canvas.toDataURL('image/png');
                    facialImage = dataUrl;
                    // 创建图片元素显示结果（可根据需要修改）
                    // const img = document.createElement('img');
                    // img.src = dataUrl;
                    // document.body.appendChild(img);
                }, 1000);
            }

        </script>
    </body>
</html>
