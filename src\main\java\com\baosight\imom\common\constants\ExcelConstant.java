package com.baosight.imom.common.constants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FACP99文件导入title 配置 常量类
 * 命名方式 ：固定的 TABTYPE_ + tabType的数字 + sheet页号
 */
public class  ExcelConstant {



    public static  Map<String,List<String>> columnMap = new HashMap<>();
    //已结算未入库捆包 excel  title
    public static final List<String> TABTYPE_1_0 = Arrays.asList("segNo","matInnerId","packId","netWeight","grossWeight","saleUserNum","saleUserName","prodCode","prodTypeId","makerNum","makerName","tradeCode","purContractNum","purOrderNum");
    //缺失捆包excel title
    public static final List<String> TABTYPE_2_0 = Arrays.asList("segNo","packId","productId");

    //存货导入Excel Title
    public static final List<String> TABTYPE_3_0 = Arrays.asList("segNo","subjectType","matInnerId","packId","businessType","storeType","qty","amount","remark","putinDate");

    //存货暂估导入Excel Title
    public static final List<String> TABTYPE_3_1 = Arrays.asList("segNo","subjectType","matInnerId","packId","businessType","storeType","qty","amount","providerId","providerName","remark","putinDate","currencyCode","foreignAmt");

    //资材导入Excel Title
    public static final List<String> TABTYPE_3_2 = Arrays.asList("segNo","materialCode","materialName","materialType","materialGroup","qty","amount");

    //资材暂估导入Excel Title
    public static final List<String> TABTYPE_3_3 = Arrays.asList("segNo","materialCode","materialName","materialType","materialGroup","qty","amount","providerCode","providerName");

    //销售截止导入 Excel Title
    public static final List<String> TABTYPE_3_4 = Arrays.asList("segNo","matInnerId","packId","weight","qty","revenueAmount","revenueTaxamt","amountIntax","costAmount","storeType","businessType","providerName","custmerId","custName","currencyCode","foreignAmt");

    //资材摊销导入
    public static final List<String> TABTYPE_3_5 = Arrays.asList("segNo","putoutId","stuffCode","receiveQuantity","equipmentAmortizationPeriod","amortizePeriodAmt","currentAmortizePeriod","totAmortizeAmt","stuffPutoutAmtFnc","requisitionUnit");

    public static final List<String> TABTYPE_VOUCHERNO = Arrays.asList("voucherNo");
    //客户资金帐
    public static final List<String> TABTYPE_8 = Arrays.asList("segNo","customerId","customerName","dUserNum","capitalOrderNum","billLock","putoutLock","settleLock","lockType","matInnerId","ifCredit","creditTraceDate","productId","currencyCode");
    //客户折扣资金帐
    public static final List<String> TABTYPE_9 = Arrays.asList("segNo","customerId","customerName","dUserNum","capitalOrderNum","discountAmount","dependNum1","currencyCode");
    //供应商资金帐
    public static final List<String> TABTYPE_10 = Arrays.asList("segNo","providerCode","providerName","capitalOrderNum","balance","payableInvoiceNo","creditTraceDate","currencyCode");

    static{
        //columnMap = new HashMap<>();
        columnMap.put("TABTYPE_1_0",TABTYPE_1_0);
        columnMap.put("TABTYPE_2_0",TABTYPE_2_0);
        columnMap.put("TABTYPE_3_0",TABTYPE_3_0);
        columnMap.put("TABTYPE_3_1",TABTYPE_3_1);
        columnMap.put("TABTYPE_3_2",TABTYPE_3_2);
        columnMap.put("TABTYPE_3_3",TABTYPE_3_3);
        columnMap.put("TABTYPE_3_4",TABTYPE_3_4);
        columnMap.put("TABTYPE_3_5",TABTYPE_3_5);
        columnMap.put("TABTYPE_VOUCHERNO",TABTYPE_VOUCHERNO);
        columnMap.put("TABTYPE_8",TABTYPE_8);
        columnMap.put("TABTYPE_9",TABTYPE_9);
        columnMap.put("TABTYPE_10",TABTYPE_10);
    }

    public static Map<String,List<String>> getColumnMap(){
        return columnMap;
    }

}
