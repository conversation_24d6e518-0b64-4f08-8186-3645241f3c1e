package com.baosight.imom.common.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title FfConstant.java
 * @package com.baosight.imom.common.constants
 * @Description 常量类(系统模块)
 * @date 2022年03月21日 15:04
 */
public class MesConstant {
    public static final String APP_CODE = "imom";
    public static final String LOGIN_USER ="loginUser";
    public static final String LOGIN_NAME = "loginName";
    public static final String USER_NAME = "userName";
    public static final String SYSTEM = "system";

    /*业务账套号*/
    public static final String SEG_NO = "segNo";



    /**
     * 静态常量.
     */
    public class Iplat {
        /** 平台默认INQU BLOCK. */
        public static final String INQU_BLOCK = "inqu";

        /** 平台默认INQU_STATUS BLOCK. */
        public static final String INQU_STATUS_BLOCK = "inqu_status";

        /** 平台默认INQU1_STATUS BLOCK. */
        public static final String INQU1_STATUS_BLOCK = "inqu1_status";

        /** 平台默认INQU2_STATUS BLOCK. */
        public static final String INQU2_STATUS_BLOCK = "inqu2_status";

        /** 平台默认INQU3_STATUS BLOCK. */
        public static final String INQU3_STATUS_BLOCK = "inqu3_status";

        /** 平台默认INQU4_STATUS BLOCK. */
        public static final String INQU4_STATUS_BLOCK = "inqu4_status";

        /** 平台默认INQU5_STATUS BLOCK. */
        public static final String INQU5_STATUS_BLOCK = "inqu5_status";

        /** 平台默认INQU6_STATUS BLOCK. */
        public static final String INQU6_STATUS_BLOCK = "inqu6_status";

        /** 平台默认INQU7_STATUS BLOCK. */
        public static final String INQU7_STATUS_BLOCK = "inqu7_status";

        /** 平台默认RESULT BLOCK. */
        public static final String RESULT_BLOCK = "result";

        /** 平台默认RESULT0 BLOCK. */
        public static final String RESULT0_BLOCK = "result0";

        /** 平台默认RESULT1 BLOCK. */
        public static final String RESULT1_BLOCK = "result1";

        /** 平台默认RESULT1 BLOCK. */
        public static final String RESULT2_BLOCK = "result2";

        /** 平台默认RESULT2 BLOCK. */
        public static final String RESULT3_BLOCK = "result3";

        /** 平台默认RESULT4 BLOCK. */
        public static final String RESULT4_BLOCK = "result4";

        /** 平台默认RESULT5 BLOCK. */
        public static final String RESULT5_BLOCK = "result5";

        /** 平台默认RESULT6 BLOCK. */
        public static final String RESULT6_BLOCK = "result6";

        /** 平台默认RESULT7 BLOCK. */
        public static final String RESULT7_BLOCK = "result7";

        /** 平台默认DETAIL BLOCK. */
        public static final String DETAIL_BLOCK = "detail";

        /** 平台默认 DETAIL_STATUS_BLOCK. */
        public static final String DETAIL_STATUS_BLOCK = "detail_status";

        /** 平台默认DETAIL BLOCK. */
        public static final String DETAIL2_BLOCK = "detail2";
        /** 控制导出记录数10000条. */
        public static final int MAX_QUERY_COUNT = 10000;

        /** 资材领用明细BLOCK. */
        public static final String ZC_DETAIL = "zc_detail";
        /** 资材领用明细查询条件BLOCK. */
        public static final String INQU_STATUS2 = "inqu_status2";

    }

    /**
     * 记录操作状态.
     */
    public class RecordStatus{
        /** 新增. */
        public static final String I="I";

        /** 修改. */
        public static final String U="U";

        /** 删除. */
        public static final String D="D";
    }

    /**
     * 通用业务状态.
     */
    public class Status{

        /** 00：删除. */
        public static final String K00="00";

        /** 10：新增. */
        public static final String K10="10";

        /** 15：已提交. */
        public static final String K15="15";

        /** 20：已提交. */
        public static final String K20="20";

        /** 30：已确认. */
        public static final String K30="30";

        /** 40：. */
        public static final String K40="40";

        /** 60：审批中. */
        public static final String K60="60";

        /** 61：一级审批. */
        public static final String K61="61";

        /** 62：二级审批. */
        public static final String K62="62";

        /** 63：三级审批. */
        public static final String K63="63";

        /** 64：四级审批. */
        public static final String K64="64";

        /** 65：五级审批. */
        public static final String K65="65";

        /** 66：六级审批. */
        public static final String K66="66";

        /** 67：七级审批. */
        public static final String K67="67";

        /** 68：八级审批. */
        public static final String K68="68";

        /** 69：九级审批. */
        public static final String K69="69";

        /** 70：审核通过. */
        public static final String K70="70";
        /** 7X：审核驳回. */
        public static final String K7X="7X";
        /** 80：审核驳回. */
        public static final String K80="80";
        /** 90：有效. */
        public static final String K90="90";

        /** 98：作废. */
        public static final String K98="98";

        /** 99：归档. */
        public static final String K99="99";
    }

    /**
     * 平台消息统一存放key.
     */
    public class EPResource {
        /** 许可证错误，原因;[{0}]. */
        public static final String EP_0000 = "ep.0000";
        /** 查询失败，原因;[{0}]. */
        public static final String EP_0001 = "ep.0001";
        /** 第{0}条记录{1}失败，原因[{2}]. */
        public static final String EP_0002 = "ep.0002";
        /** 新增记录失败，原因;[{0}]. */
        public static final String EP_0003 = "ep.0003";
        /** 修改记录失败，原因;[{0}]. */
        public static final String EP_0004 = "ep.0004";
        /** 删除记录失败，原因;[{0}]. */
        public static final String EP_0005 = "ep.0005";
        /** 删除{0}条记录失败，原因;[{0}]. */
        public static final String EP_0006 = "ep.0006";
        /** 未找到页面号[{0}]的登记信息！. */
        public static final String EP_0010 = "ep.0010";
        /** Service{0}运行失败，原因[{1}]. */
        public static final String EP_0011 = "ep.0011";
        /** 不能访问[{0}]，该页面禁止使用！. */
        public static final String EP_0012 = "ep.0012";
        /** 请输入正确的类名!. */
        public static final String EP_0020 = "ep.0020";
        /** 无法正确获取bean对象,类名;[{0}]. */
        public static final String EP_0021 = "ep.0021";
        /** 电文转换错误,原因;[{0}]. */
        public static final String EP_0022 = "ep.0022";
        /** 电文翻译错误,原因;[{0}]. */
        public static final String EP_0023 = "ep.0023";
        /** 数值转换错误,原因;[{0}]. */
        public static final String EP_0030 = "ep.0030";
        /** 对{0}条记录执行{1}操作成功！. */
        public static final String EP_1000 = "ep.1000";
        /** 未查得记录！. */
        public static final String EP_1001 = "ep.1001";
        /** 成功删除{0}条记录!删除{1}条记录失败!原因:{2}. */
        public static final String EP_1002 = "ep.1002";
        /** 查询成功！. */
        public static final String EP_1003 = "ep.1003";
        /** 查询成功，本次查询返回{0}条记录！. */
        public static final String EP_2000 = "ep.2000";
        /** 欢迎您{0}使用!. */
        public static final String EP_2001 = "ep.2001";
        /** 获取bean信息成功!. */
        public static final String EP_2002 = "ep.2002";
        /** 电文转换成功!. */
        public static final String EP_2003 = "ep.2003";
        /** 电文翻译成功!. */
        public static final String EP_2004 = "ep.2004";
        /** 新增数据成功!. */
        public static final String EP_2005 = "ep.2005";
        /** 修改数据成功!. */
        public static final String EP_2006 = "ep.2006";
        /** 删除数据成功!. */
        public static final String EP_2007 = "ep.2007";
        /** 查询成功!. */
        public static final String EP_2008 = "ep.2008";
        /** 页面加载成功!. */
        public static final String EP_2009 = "ep.2009";
        /** 删除成功，{0}. */
        public static final String EP_2022 = "ep.2022";
        /** 修改成功，{0}. */
        public static final String EP_2023 = "ep.2023";
        /** 新密码不合法. */
        public static final String EP_3001 = "ep.3001";
        /** 密码设置成功!. */
        public static final String EP_3002 = "ep.3002";
        /** 密码不一致. */
        public static final String EP_3003 = "ep.3003";
        /** 重置密码的凭证不合法或已失效. */
        public static final String EP_3004 = "ep.3004";
        /** 密码重置链接. */
        public static final String EP_3005 = "ep.3005";
        /** 您好,{0}！<br/>
         请在24小时之内打开下面的链接以重置密码，邮件发送时间为{1}。<br/><br/>
         <a href={2}>{2}</a>. */
        public static final String EP_3006 = "ep.3006";
        /** 密码不能为空. */
        public static final String EP_3007 = "ep.3007";
        /** 页面加载失败，原因:[{0}]. */
        public static final String EP_9013 = "ep.9013";
        /** [{0}]审批工作流-[{1}]提交成功！. */
        public static final String EP_9020 = "ep.9020";
        /** 审批工作流-[{0}]审批通过，[{1}]操作执行成功！. */
        public static final String EP_9030 = "ep.9030";
        /** 审批工作流-[{0}]审批驳回，返回提交人[{1}]处理！. */
        public static final String EP_9040 = "ep.9040";
        /** [{0}]审批工作流-[{1}]撤销成功！. */
        public static final String EP_9050 = "ep.9050";
        /** [{0}]审批工作流操作失败，失败原因：[{1}]. */
        public static final String EP_9060 = "ep.9060";
    }

    /**
     * 行车编号与芯片卡号对照
     * 示例：{"1": "1", "2": "2"}
     */
    public static final Map<String, String> CARD_MAP = new HashMap<>();

    static {
        CARD_MAP.put("52LE01", "651175");
        CARD_MAP.put("52LE03_01", "651143");
        CARD_MAP.put("52LE04", "651163");
        CARD_MAP.put("52LE15", "651159");
        CARD_MAP.put("52LE03_02", "651147");
        CARD_MAP.put("52LE02", "651183");
        CARD_MAP.put("52LE06", "");
        CARD_MAP.put("52LE07", "");
        CARD_MAP.put("52LE12", "");
        CARD_MAP.put("52LE05", "651162");
    }

}
