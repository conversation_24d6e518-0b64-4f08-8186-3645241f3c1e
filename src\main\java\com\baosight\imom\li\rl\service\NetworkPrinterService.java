package com.baosight.imom.li.rl.service;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.pdf.BaseFont;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.CellType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

@Service
public class NetworkPrinterService {
    private static final Logger log = LoggerFactory.getLogger(NetworkPrinterService.class);
    private static final int DEFAULT_PORT = 9100;
    private static final int DEFAULT_TIMEOUT = 5000;
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_INTERVAL = 3000;

    // 添加中文字体支持
    private static BaseFont baseFont;

    static {
        try {
            // 尝试加载系统中的中文字体
            baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            try {
                // 如果系统字体不可用，尝试使用内置字体
                baseFont = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            } catch (Exception ex) {
                log.error("无法加载中文字体，将使用默认字体", ex);
            }
        }
    }


    public static void main(String[] args) {
        printFile("/Users/<USER>/KF000000/51844EEDFC084AF8A3254BBBD52C88D2.xlsx", "**********", 9100, 5000);
    }

    /**
     * 打印文件
     * @param filePath 文件路径
     * @param printerIp 打印机IP
     * @param port 端口号
     * @param timeout 超时时间
     */
    public static void printFile(String filePath, String printerIp, int port, int timeout) {
        String fileExtension = getFileExtension(filePath);
        try {
            switch (fileExtension.toLowerCase()) {
                case "pdf":
                    printPDF(filePath, printerIp, port, timeout);
                    break;
                case "doc":
                case "docx":
                    printWord(filePath, printerIp, port, timeout);
                    break;
                case "xls":
                case "xlsx":
                    printExcel(filePath, printerIp, port, timeout);
                    break;
                case "txt":
                    printText(filePath, printerIp, port, timeout);
                    break;
                case "jpg":
                case "jpeg":
                case "png":
                    printImage(filePath, printerIp, port, timeout);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的文件格式: " + fileExtension);
            }
        } catch (Exception e) {
            log.error("打印文件失败: " + e.getMessage(), e);
            throw new RuntimeException("打印文件失败: " + e.getMessage());
        }
    }

    /**
     * 打印PDF文件
     */
    private static void printPDF(String filePath, String printerIp, int port, int timeout) throws IOException, DocumentException {
        boolean connected = false;
        int retries = MAX_RETRIES;

        while (!connected && retries > 0) {
            try {
                if (isReachable(printerIp)) {
                    // 使用iText处理PDF缩放
                    PdfReader reader = new PdfReader(filePath);
                    Document document = new Document(PageSize.A4.rotate());
                    String scaledPdfPath = filePath + ".scaled.pdf";
                    PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(scaledPdfPath));
                    document.open();

                    for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                        document.newPage();
                        PdfImportedPage importedPage = writer.getImportedPage(reader, i);
                        Image image = Image.getInstance(importedPage);
                        float scaleFactor = calculateScaleFactor(importedPage);
                        image.scalePercent(scaleFactor * 100);
                        document.add(image);
                    }

                    document.close();
                    writer.close();
                    reader.close();

                    // 发送到打印机
                    sendToPrinter(new File(scaledPdfPath), printerIp, port, timeout);
                    connected = true;
                }
            } catch (Exception e) {
                log.error("PDF打印失败，重试中...", e);
                retries--;
                if (retries > 0) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("打印被中断", ie);
                    }
                }
            }
        }

        if (!connected) {
            throw new RuntimeException("无法连接到打印机或打印失败");
        }
    }

    /**
     * 打印Word文件
     */
    private static void printWord(String filePath, String printerIp, int port, int timeout) throws IOException, DocumentException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            XWPFDocument document = new XWPFDocument(fis);
            // 转换为PDF后打印
            String pdfPath = convertWordToPdf(document, filePath);
            printPDF(pdfPath, printerIp, port, timeout);
        }
    }

    /**
     * 打印Excel文件
     */
    private static void printExcel(String filePath, String printerIp, int port, int timeout) throws IOException, DocumentException, InvalidFormatException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = WorkbookFactory.create(fis);
            // 转换为PDF后打印
            String pdfPath = convertExcelToPdf(workbook, filePath);
            printPDF(pdfPath, printerIp, port, timeout);
        }
    }

    /**
     * 打印文本文件
     */
    private static void printText(String filePath, String printerIp, int port, int timeout) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath));
             Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(printerIp, port), timeout);
            OutputStream out = socket.getOutputStream();

            String line;
            while ((line = reader.readLine()) != null) {
                out.write((line + "\n").getBytes());
            }
            out.flush();
        }
    }

    /**
     * 打印图片文件
     */
    private static void printImage(String filePath, String printerIp, int port, int timeout) throws IOException, DocumentException {
        BufferedImage image = ImageIO.read(new File(filePath));
        // 转换为PDF后打印
        String pdfPath = convertImageToPdf(image, filePath);
        printPDF(pdfPath, printerIp, port, timeout);
    }

    /**
     * 发送文件到打印机
     */
    private static void sendToPrinter(File file, String printerIp, int port, int timeout) throws IOException {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(printerIp, port), timeout);
            try (OutputStream out = socket.getOutputStream();
                 FileInputStream fis = new FileInputStream(file)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
                out.flush();
            }
        }
    }

    /**
     * 检查打印机是否可达
     */
    private static boolean isReachable(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.isReachable(DEFAULT_TIMEOUT);
        } catch (IOException e) {
            log.error("检查打印机可达性失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 计算PDF缩放比例
     */
    private static float calculateScaleFactor(PdfImportedPage page) {
        float originalWidth = page.getWidth();
        float originalHeight = page.getHeight();
        float scaleX = PageSize.A4.getWidth() / originalWidth;
        float scaleY = PageSize.A4.getHeight() / originalHeight;
        return Math.min(scaleX, scaleY);
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        return lastDotIndex > 0 ? filePath.substring(lastDotIndex + 1) : "";
    }

    /**
     * 将Word转换为PDF
     */
    private static String convertWordToPdf(XWPFDocument document, String originalPath) throws IOException, DocumentException {
        // 改进PDF文件路径生成方式
        String pdfPath;
        int lastDotIndex = originalPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            // 如果原文件有扩展名，则替换扩展名为.pdf
            pdfPath = originalPath.substring(0, lastDotIndex) + ".pdf";
        } else {
            // 如果原文件没有扩展名，则直接添加.pdf
            pdfPath = originalPath + ".pdf";
        }
        
        Document pdfDocument = new Document(PageSize.A4);
        PdfWriter.getInstance(pdfDocument, new FileOutputStream(pdfPath));
        pdfDocument.open();

        // 将Word文档内容转换为PDF
        for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : document.getParagraphs()) {
            com.itextpdf.text.Paragraph pdfParagraph = new com.itextpdf.text.Paragraph(paragraph.getText());
            pdfDocument.add(pdfParagraph);
        }

        // 处理表格
        for (org.apache.poi.xwpf.usermodel.XWPFTable table : document.getTables()) {
            com.itextpdf.text.pdf.PdfPTable pdfTable = new com.itextpdf.text.pdf.PdfPTable(table.getRow(0).getTableCells().size());
            pdfTable.setWidthPercentage(100);

            for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                    pdfTable.addCell(cell.getText());
                }
            }
            pdfDocument.add(pdfTable);
        }

        pdfDocument.close();
        return pdfPath;
    }

    /**
     * 将Excel转换为PDF
     */
    private static String convertExcelToPdf(Workbook workbook, String originalPath) throws IOException, DocumentException {
        String pdfPath;
        int lastDotIndex = originalPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            pdfPath = originalPath.substring(0, lastDotIndex) + ".pdf";
        } else {
            pdfPath = originalPath + ".pdf";
        }
        
        // 使用纵向A4纸张
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, new FileOutputStream(pdfPath));
        document.open();
        
        // 创建支持中文的字体
        com.itextpdf.text.Font chineseFont = new com.itextpdf.text.Font(baseFont, 10);
        com.itextpdf.text.Font headerFont = new com.itextpdf.text.Font(baseFont, 10, com.itextpdf.text.Font.BOLD);
        
        // 遍历所有工作表
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            
            // 添加新页面
            if (i > 0) {
                document.newPage();
            }
            
            // 添加工作表名称
            com.itextpdf.text.Paragraph sheetName = new com.itextpdf.text.Paragraph("工作表: " + sheet.getSheetName(), headerFont);
            sheetName.setSpacingAfter(10);
            document.add(sheetName);
            
            // 获取最大列数和行数
            int maxColumns = 0;
            int maxRows = sheet.getLastRowNum() + 1;
            for (int rowNum = 0; rowNum < maxRows; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row != null) {
                    maxColumns = Math.max(maxColumns, row.getLastCellNum());
                }
            }
            
            if (maxColumns == 0) {
                log.warn("工作表 " + sheet.getSheetName() + " 没有数据");
                continue;
            }
            
            // 创建表格
            com.itextpdf.text.pdf.PdfPTable table = new com.itextpdf.text.pdf.PdfPTable(maxColumns);
            table.setWidthPercentage(100);
            
            // 设置列宽
            float[] columnWidths = new float[maxColumns];
            for (int j = 0; j < maxColumns; j++) {
                columnWidths[j] = 100f / maxColumns;
            }
            table.setWidths(columnWidths);
            
            // 添加表头
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (int j = 0; j < maxColumns; j++) {
                    Cell cell = headerRow.getCell(j);
                    String value = getCellValueAsString(cell);
                    log.info("表头单元格[" + j + "]的值: " + value); // 添加日志
                    
                    com.itextpdf.text.pdf.PdfPCell pdfCell = new com.itextpdf.text.pdf.PdfPCell(
                            new com.itextpdf.text.Paragraph(value, headerFont));
                    pdfCell.setBackgroundColor(com.itextpdf.text.BaseColor.LIGHT_GRAY);
                    pdfCell.setHorizontalAlignment(com.itextpdf.text.Element.ALIGN_CENTER);
                    pdfCell.setPadding(5);
                    table.addCell(pdfCell);
                }
            }
            
            // 添加数据行
            for (int rowNum = 1; rowNum < maxRows; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row != null) {
                    for (int colNum = 0; colNum < maxColumns; colNum++) {
                        Cell cell = row.getCell(colNum);
                        String value = getCellValueAsString(cell);
                        log.info("数据单元格[" + rowNum + "," + colNum + "]的值: " + value); // 添加日志
                        
                        com.itextpdf.text.pdf.PdfPCell pdfCell = new com.itextpdf.text.pdf.PdfPCell(
                                new com.itextpdf.text.Paragraph(value, chineseFont));
                        pdfCell.setHorizontalAlignment(com.itextpdf.text.Element.ALIGN_CENTER);
                        pdfCell.setPadding(5);
                        table.addCell(pdfCell);
                    }
                } else {
                    // 如果行为空，添加空单元格
                    for (int colNum = 0; colNum < maxColumns; colNum++) {
                        com.itextpdf.text.pdf.PdfPCell pdfCell = new com.itextpdf.text.pdf.PdfPCell(
                                new com.itextpdf.text.Paragraph("", chineseFont));
                        pdfCell.setHorizontalAlignment(com.itextpdf.text.Element.ALIGN_CENTER);
                        pdfCell.setPadding(5);
                        table.addCell(pdfCell);
                    }
                }
            }
            
            document.add(table);
        }
        
        document.close();
        return pdfPath;
    }

    /**
     * 将图片转换为PDF
     */
    private static String convertImageToPdf(BufferedImage image, String originalPath) throws IOException, DocumentException {
        // 改进PDF文件路径生成方式
        String pdfPath;
        int lastDotIndex = originalPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            // 如果原文件有扩展名，则替换扩展名为.pdf
            pdfPath = originalPath.substring(0, lastDotIndex) + ".pdf";
        } else {
            // 如果原文件没有扩展名，则直接添加.pdf
            pdfPath = originalPath + ".pdf";
        }
        
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, new FileOutputStream(pdfPath));
        document.open();

        // 计算缩放比例以适应A4页面
        float pageWidth = document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin();
        float pageHeight = document.getPageSize().getHeight() - document.topMargin() - document.bottomMargin();
        float imageWidth = image.getWidth();
        float imageHeight = image.getHeight();
        float scale = Math.min(pageWidth / imageWidth, pageHeight / imageHeight);

        // 创建图片对象并设置缩放
        Image pdfImage = Image.getInstance(image, null);
        pdfImage.scalePercent(scale * 100);

        // 居中显示
        pdfImage.setAlignment(Image.MIDDLE);
        document.add(pdfImage);
        document.close();

        return pdfPath;
    }

    /**
     * 获取单元格的值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        String cellValue = "";
        try {
            // 在POI 5.2.3中，getCellType()返回CellType枚举
            CellType cellType = CellType.forInt(cell.getCellType());
            
            switch (cellType) {
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellValue = cell.getDateCellValue().toString();
                    } else {
                        // 处理数字，避免科学计数法
                        double value = cell.getNumericCellValue();
                        if (value == Math.floor(value)) {
                            // 整数
                            cellValue = String.format("%.0f", value);
                        } else {
                            // 小数，保留2位
                            cellValue = String.format("%.2f", value);
                        }
                    }
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                case FORMULA:
                    try {
                        // 尝试获取公式计算结果
                        CellType formulaResultType = CellType.forInt(cell.getCachedFormulaResultType());
                        switch (formulaResultType) {
                            case NUMERIC:
                                if (DateUtil.isCellDateFormatted(cell)) {
                                    cellValue = cell.getDateCellValue().toString();
                                } else {
                                    double value = cell.getNumericCellValue();
                                    if (value == Math.floor(value)) {
                                        cellValue = String.format("%.0f", value);
                                    } else {
                                        cellValue = String.format("%.2f", value);
                                    }
                                }
                                break;
                            case STRING:
                                cellValue = cell.getStringCellValue();
                                break;
                            case BOOLEAN:
                                cellValue = String.valueOf(cell.getBooleanCellValue());
                                break;
                            default:
                                cellValue = cell.getCellFormula();
                        }
                    } catch (Exception e) {
                        // 如果无法获取公式结果，则显示公式本身
                        cellValue = cell.getCellFormula();
                    }
                    break;
                case BLANK:
                    cellValue = "";
                    break;
                case ERROR:
                    cellValue = "ERROR: " + cell.getErrorCellValue();
                    break;
                default:
                    cellValue = "";
            }
        } catch (Exception e) {
            log.warn("读取单元格值失败: " + e.getMessage());
            cellValue = "";
        }
        
        // 如果单元格值为null，返回空字符串
        return cellValue != null ? cellValue.trim() : "";
    }
}