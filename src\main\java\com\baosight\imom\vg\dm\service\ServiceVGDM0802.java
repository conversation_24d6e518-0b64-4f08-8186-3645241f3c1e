package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.FileUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.VGDM0802;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> yzj
 * @Description : 附件信息页面后台
 * @Date : 2024/8/27
 * @Version : 1.0
 */
public class ServiceVGDM0802 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0802.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, VGDM0802.QUERY, new VGDM0802());
    }

    /**
     * 附件上传(ftp)
     */
    public EiInfo fileUpload(EiInfo inInfo) {
        try {
            // 获取前端参数
            CommonsMultipartFile file = (CommonsMultipartFile) inInfo.get("file");
            String id = inInfo.getString("id");
            String id2 = inInfo.getString("id2");
            String segNo = inInfo.getString("segNo");
            // 上传文件
            String fileName = file.getOriginalFilename();
            if (StrUtil.isBlank(fileName)) {
                fileName = "";
            }
            // 生成文件ID
            String fileId = UUIDUtils.getUUID();
            // 获取文件后缀
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            // 转换文件名防止文件重复
            String storeName = fileId + suffix;
            // 上传文件-本地
            String downloadUrl = FileUtils.uploadFile(file, "/" + id2 + "/" + segNo);
            // 上传文件-ftp
//            String downloadUrl = FtpUtils.uploadFile(segNo, file, id2 + "/" + storeName);
//            if (downloadUrl == null) {
//                throw new PlatException("文件上传失败");
//            }
            // 待新增的附件记录
            VGDM0802 vgdm0802 = new VGDM0802();
            vgdm0802.setRelevanceId(id);
            vgdm0802.setRelevanceType(id2);
            vgdm0802.setSegNo(segNo);
            // 文件信息
            vgdm0802.setUploadFileName(fileName);
            vgdm0802.setFifleType(suffix);
            vgdm0802.setFifleSize(new BigDecimal(file.getSize()));
            vgdm0802.setFileId(fileId);
            // 设置文件下载路径
            vgdm0802.setUploadFilePath(downloadUrl);
            // 新增到数据库
            Map insMap = vgdm0802.toMap();
            RecordUtils.setCreator(insMap);
            dao.insert(VGDM0802.INSERT, insMap);
            // 返回消息
            inInfo.set("downloadUrl", downloadUrl);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_UPLOAD);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 获取附件地址
     */
    public EiInfo queryUrl(EiInfo inInfo) {
        try {
            // 获取前端参数
            String relevanceId = inInfo.getString("relevanceId");
            String relevanceType = inInfo.getString("relevanceType");
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(relevanceId)
                    || StrUtil.isBlank(segNo)
                    || StrUtil.isBlank(relevanceType)) {
                throw new PlatException("参数不能为空");
            }
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("relevanceId", relevanceId);
            queryMap.put("relevanceType", relevanceType);
            List<VGDM0802> list = dao.query(VGDM0802.QUERY, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("附件不存在");
            }
            // 返回消息
            inInfo.set("downloadUrl", list.get(0).getUploadFilePath());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_UPLOAD);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

}
