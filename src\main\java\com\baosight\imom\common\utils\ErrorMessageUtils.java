package com.baosight.imom.common.utils;

import com.baosight.imc.interfaces.vz.bm.domain.VZBM1300;
import com.baosight.imc.interfaces.vz.bm.service.ServiceVZBM1300;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.StringUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.MesConstant;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR> Email:<EMAIL>
 * @version v1.0
 * @Title ErrorMessageUtils.java
 * @Package com.baosight.mfm.common.utils
 * @Description 用于获取统一报错信息
 * @Date 2022/5/23 13:59.
 * <AUTHOR> Email:<EMAIL>.
 */
public class ErrorMessageUtils {
    private static final Logger logger = LoggerFactory.getLogger(ErrorMessageUtils.class);

    public static String getMessage(String errorNum) {
        return getMessage(errorNum, new String[]{}, new String[]{}, new EiInfo());
    }

    /**
     * @param errorNum  异常编号
     * @param record    record[0] 画面编号 record[1] 按钮编号  record[2] 微服务编号
     * @param arguments 参数列表
     * @param inInfo    获取用户信息
     *                  根据单个提示编号获取报错描述
     */
    public static String getMessage(String errorNum, String[] record, String[] arguments, EiInfo inInfo) {

        String messageText = "";
        if (StringUtils.isNotEmpty(errorNum)) {
            try {
                VZBM1300 vzbm1300 = new VZBM1300();
                //异常编号
                vzbm1300.setErrorNum(errorNum);
                //画面编号
                vzbm1300.setFormEname(record[0]);
                //按钮编号
                vzbm1300.setButtonEname(record[1]);
                //微服务编号
                vzbm1300.setServiceEname(record[2]);
                vzbm1300.setAppCode(MesConstant.APP_CODE);
                vzbm1300.setRecCreator(UserSession.getLoginName());
                vzbm1300.setRecCreatorName(UserSession.getLoginCName());
                vzbm1300.setRecCreateTime(DateUtil.curDateTimeStr14());
                logger.info("传入参数：" + vzbm1300);
                messageText = ServiceVZBM1300.getMessageTextByErrorNumAndRecord(arguments, vzbm1300);
                logger.info("传出参数：" + messageText);
            } catch (Exception e) {
                logger.info("调用获取统一报错信息接口异常！", e.getMessage());
            }
        } else {
            logger.info("传入参数'异常编号'为空！");
        }
        return messageText;
    }

    /**
     * @param errorNums 异常编号数组
     * @param record    record[0] 画面编号 record[1] 按钮编号  record[2] 微服务编号
     * @param arguments 参数列表
     * @param inInfo    获取用户信息
     *                  根据多个提示编号获取报错描述拼接
     */
    public static String getMessages(String[] errorNums, String[] record, String[] arguments, EiInfo inInfo) {

        String messageText = "";
        if (errorNums.length <= 0) {
            logger.info("传入参数为空！");
            return "";
        }
        if (StringUtils.isNotEmpty(errorNums[0])) {
            try {
                VZBM1300 vzbm1300 = new VZBM1300();
                //异常编号
                vzbm1300.setErrorNum(record[0]);
                //画面编号
                vzbm1300.setFormEname(record[1]);
                //按钮编号
                vzbm1300.setButtonEname(record[2]);
                //微服务编号
                vzbm1300.setServiceEname(record[3]);
                vzbm1300.setAppCode(MesConstant.APP_CODE);
                vzbm1300.setRecCreator(UserSession.getLoginName());
                vzbm1300.setRecCreatorName(UserSession.getLoginCName());
                vzbm1300.setRecCreateTime(DateUtil.curDateTimeStr14());
                logger.info("传入参数：" + vzbm1300);
                messageText = ServiceVZBM1300.getMessageTextByErrorNumsAndRecord(arguments, vzbm1300, errorNums);
                logger.info("传出参数：" + messageText);
            } catch (Exception e) {
                logger.info("调用获取统一报错信息接口异常！", e.getMessage());
            }
        } else {
            logger.info("传入参数'异常编号'为空！");
        }
        return messageText;
    }

    /**
     * @param errorNum  异常编号
     * @param record    record[0] 画面编号 record[1] 按钮编号  record[2] 微服务编号
     * @param arguments 参数列表
     * @param loginUser    获取用户信息
     *                  根据单个提示编号获取报错描述
     */
    public static String getMessage(String errorNum, String[] record, String[] arguments, Map<String, String> loginUser) {

        String messageText = "";
        if (StringUtils.isNotEmpty(errorNum)) {
            try {
                VZBM1300 vzbm1300 = new VZBM1300();
                //异常编号
                vzbm1300.setErrorNum(errorNum);
                //画面编号
                vzbm1300.setFormEname(record[0]);
                //按钮编号
                vzbm1300.setButtonEname(record[1]);
                //微服务编号
                vzbm1300.setServiceEname(record[2]);
                vzbm1300.setAppCode(MesConstant.APP_CODE);
                vzbm1300.setRecCreator(MapUtils.getString(loginUser, MesConstant.LOGIN_NAME, ""));
                vzbm1300.setRecCreatorName(MapUtils.getString(loginUser, MesConstant.USER_NAME, ""));
                vzbm1300.setRecCreateTime(DateUtil.curDateTimeStr14());
                logger.info("传入参数：" + vzbm1300);
                messageText = ServiceVZBM1300.getMessageTextByErrorNumAndRecord(arguments, vzbm1300);
                logger.info("传出参数：" + messageText);
            } catch (Exception e) {
                logger.info("调用获取统一报错信息接口异常！", e.getMessage());
            }
        } else {
            logger.info("传入参数'异常编号'为空！");
        }
        return messageText;
    }

    /**
     * @param errorNums 异常编号数组
     * @param record    record[0] 画面编号 record[1] 按钮编号  record[2] 微服务编号
     * @param arguments 参数列表
     * @param loginUser    获取用户信息
     *                  根据多个提示编号获取报错描述拼接
     */
    public static String getMessages(String[] errorNums, String[] record, String[] arguments, Map<String, String> loginUser) {

        String messageText = "";
        if (errorNums.length <= 0) {
            logger.info("传入参数为空！");
            return "";
        }
        if (StringUtils.isNotEmpty(errorNums[0])) {
            try {
                VZBM1300 vzbm1300 = new VZBM1300();
                //异常编号
                vzbm1300.setErrorNum(record[0]);
                //画面编号
                vzbm1300.setFormEname(record[1]);
                //按钮编号
                vzbm1300.setButtonEname(record[2]);
                //微服务编号
                vzbm1300.setServiceEname(record[3]);
                vzbm1300.setAppCode(MesConstant.APP_CODE);
                vzbm1300.setRecCreator(MapUtils.getString(loginUser, MesConstant.LOGIN_NAME, ""));
                vzbm1300.setRecCreatorName(MapUtils.getString(loginUser, MesConstant.USER_NAME, ""));
                vzbm1300.setRecCreateTime(DateUtil.curDateTimeStr14());
                logger.info("传入参数：" + vzbm1300);
                messageText = ServiceVZBM1300.getMessageTextByErrorNumsAndRecord(arguments, vzbm1300, errorNums);
                logger.info("传出参数：" + messageText);
            } catch (Exception e) {
                logger.info("调用获取统一报错信息接口异常！", e.getMessage());
            }
        } else {
            logger.info("传入参数'异常编号'为空！");
        }
        return messageText;
    }

    /**
     * record[0] 异常编号  record[1] 画面编号 record[2] 按钮编号
     * record[3] 微服务编号
     * arguments参数列表
     * 获取处理意见
     */
    public static String getSolution(String errorNum) {

        String solutionText = "";
        if (StringUtils.isNotEmpty(errorNum)) {
            try {
                logger.info("传入参数：" + errorNum);
                solutionText = ServiceVZBM1300.getSolutionByErrorNum(errorNum);
                logger.info("传出参数：" + solutionText);
            } catch (Exception e) {
                logger.info("调用获取统一报错信息接口异常！", e.getMessage());
            }
        } else {
            logger.info("传入参数'异常编号'为空！");
        }
        return solutionText;
    }

    /**
     * record[0] 异常编号  record[1] 画面编号 record[2] 按钮编号
     * record[3] 微服务编号
     * arguments参数列表
     * 获取处理意见拼接
     */
    public static String getSolutions(String[] errorNums) {

        String solutionText = "";
        if (errorNums.length <= 0) {
            logger.info("传入参数为空！");
            return "";
        }
        if (StringUtils.isNotEmpty(errorNums[0])) {
            try {
                logger.info("传入参数：" + errorNums);
                solutionText = ServiceVZBM1300.getSolutionsByErrorNum(errorNums);
                logger.info("传出参数：" + solutionText);
            } catch (Exception e) {
                logger.info("调用获取统一报错信息接口异常！", e.getMessage());
            }
        } else {
            logger.info("传入参数'异常编号'为空！");
        }
        return solutionText;
    }
}
