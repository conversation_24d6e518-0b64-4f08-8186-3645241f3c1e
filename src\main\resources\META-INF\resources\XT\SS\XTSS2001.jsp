<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元名称" colWidth="3" disabled="true" placeholder="请输入用户组英文名"/>
            <EF:EFInput ename="inqu_status-0-groupEname" cname="用户组英文名" colWidth="3" placeholder="请输入用户组英文名"/>
            <EF:EFInput ename="inqu_status-0-groupCname" cname="用户组中文名" colWidth="3" placeholder="请输入用户组中文名"/>
        </div>
    </EF:EFRegion>
    
    <div id="result">
        <EF:EFRegion id="result" title="用户组列表">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true"
                       checkMode="single, row" serviceName="XTSS2001" queryMethod="query">
                <EF:EFColumn ename="userGroupId" cname="用户组ID" align="center" width="120" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="groupEname" cname="用户组英文名" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="groupCname" cname="用户组中文名" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="orgCname" cname="所属组织" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="创建时间" align="center" width="150" enable="false"
                             parseFormats="['yyyyMMddHHmmss']" editType="datetime" dateFormat="yyyy-MM-dd HH:mm:ss"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>
</EF:EFPage> 