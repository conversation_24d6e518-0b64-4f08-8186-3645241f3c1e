<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3" value=" " disabled="true"/>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-factoryArea" cname="厂区代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryAreaName" cname="厂区名称" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-factoryBuilding" cname="厂房代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryBuildingName" cname="厂房名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P032" condition="ITEM_CODE NOT IN ('30','99')"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true"
                       serviceName="LIDS0102" queryMethod="query">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" enable="false" hidden="true"/>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="left" width="150" enable="false"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="left" width="150" enable="false"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFCodeOption codeName="P032" condition="ITEM_CODE NOT IN ('30','99')"/>
                </EF:EFComboColumn>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>

</EF:EFPage>
