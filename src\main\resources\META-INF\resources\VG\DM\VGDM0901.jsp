<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                           startCname="创建时间(起)" endCname="创建时间(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-stuffName" cname="资材名称" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-apprStatus" cname="审批状态" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="审批中" value="60"/>
                <EF:EFOption label="审核通过" value="70"/>
                <EF:EFOption label="审核驳回" value="7X"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" autoDraw="no" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" enable="false" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" enable="false" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFComboColumn ename="apprStatus" enable="false" cname="审批状态" align="center" width="70">
                <EF:EFOption label="审批中" value="60"/>
                <EF:EFOption label="审核通过" value="70"/>
                <EF:EFOption label="审核驳回" value="7X"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="comment" cname="审批意见" enable="true" width="120"
                         editType="textarea" maxLength="100"
                         copy="true" sort="flase"/>
            <EF:EFComboColumn ename="scrapType" cname="报废方式" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P062"/>
            </EF:EFComboColumn>
            <EF:EFColumn enable="false" ename="scrapReason" cname="报废原因" align="center" width="200"/>
            <EF:EFColumn ename="stuffCode" cname="资材代码" align="center" width="100" enable="false"/>
            <EF:EFColumn ename="stuffName" cname="资材名称" enable="false"/>
            <EF:EFColumn ename="specDesc" cname="规格" enable="false"/>
            <EF:EFColumn ename="amountMoney" cname="总金额" align="right" width="70" enable="false"/>
            <EF:EFColumn ename="applyQty" cname="数量" align="right" width="70" enable="false"/>
            <EF:EFColumn ename="unitPrice" cname="资材单价" align="right" width="70" enable="false"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" enable="false" width="70" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称" enable="false"/>
            <EF:EFColumn enable="false" ename="recCreator" cname="创建人" align="center" width="80"/>
            <EF:EFColumn enable="false" ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn enable="false" ename="recCreateTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
            <EF:EFColumn enable="false" ename="recRevisor" cname="修改人" align="center" width="80"/>
            <EF:EFColumn enable="false" ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
            <EF:EFColumn enable="false" ename="recReviseTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow id="popup" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>