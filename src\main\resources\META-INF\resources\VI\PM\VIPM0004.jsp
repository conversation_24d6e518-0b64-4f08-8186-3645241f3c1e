<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-machineCode" cname="机组代码" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-machineName" cname="机组名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-partIdStr" cname="物料号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-processOrderIdStr" cname="工单" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-packIdStr" cname="捆包号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-dataType" cname="数据类型" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="明细" value="10"/>
                <EF:EFOption label="月度平均" value="20"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                           startCname="创建时间(起)" endCname="创建时间(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="履历清单">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" sort="flase"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="machineCode" cname="机组代码" width="70" align="center"/>
            <EF:EFColumn ename="machineName" cname="机组名称"/>
            <EF:EFColumn ename="offlineEvent" cname="离线项目" width="120"/>
            <EF:EFColumn ename="offlineTime" cname="时间(分钟)" format="{0:N1}" width="120" align="right"/>
            <EF:EFComboColumn ename="dataType" cname="数据类型" align="center" width="70">
                <EF:EFOption label="明细" value="10"/>
                <EF:EFOption label="月度平均" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="processOrderId" cname="工单" width="140" align="center"/>
            <EF:EFColumn ename="packId" cname="捆包号" width="120" align="center"/>
            <EF:EFColumn ename="inPartId" cname="物料号" width="120" align="center"/>
            <EF:EFColumn ename="inSpecsDesc" cname="规格" width="120"/>
            <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>