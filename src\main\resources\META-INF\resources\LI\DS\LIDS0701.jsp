<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-machineCode" cname="机组代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-machineName" cname="机组名称" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="启用" value="10"/>
                <EF:EFOption label="停用" value="20"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" sort="all">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="machineCode" cname="机组代码" align="center" width="150"
                             required="true"
                             primaryKey="true" enable="false"/>
                <EF:EFColumn ename="machineName" cname="机组名称" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="e_archivesNo" cname="设备档案编号" align="left" width="150" enable="false"
                             required="true"/>
                <EF:EFColumn ename="equipmentName" cname="设备名称" align="center" width="200" enable="false"
                             required="true"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="启用" value="10"/>
                    <EF:EFOption label="停用" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="processCategory" cname="工序大类代码" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="processCategoryName" cname="工序大类名称" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="center" width="100" enable="false"
                             readonly="true"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" width="100" enable="false"
                             readonly="true"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="center" width="150" enable="false"
                             readonly="true"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="center" width="150" enable="false"
                             readonly="true"/>
                <EF:EFColumn ename="crossArea" cname="跨区编码" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="crossAreaName" cname="跨区名称" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="unpackAreaId" cname="拆包区编号" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="unpackAreaName" cname="拆包区名称" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="priorityLoadMaterialArea" cname="优先上料区代码" align="center" width="200"
                             enable="true"/>
                <EF:EFColumn ename="priorityLoadMaterialAreaName" cname="优先上料区名称" align="center" width="200"
                             enable="true"/>
                <EF:EFColumn ename="materialLoadingArea" cname="机组上料区代码" align="center" width="200"
                             enable="true"/>
                <EF:EFColumn ename="materialLoadingAreaName" cname="机组上料区名称" align="center" width="200"
                             enable="true"/>
                <EF:EFColumn ename="materialUnloadingArea" cname="机组下料区代码" align="center" width="200"
                             enable="true"/>
                <EF:EFColumn ename="materialUnloadingAreaName" cname="机组下料区名称" align="center" width="200"
                             enable="true"/>
                <EF:EFColumn ename="rejectionArea" cname="机组退料区代码" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="rejectionAreaName" cname="机组退料区名称" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="mouldCart" cname="机组模具台车代码" align="center" width="200" enable="true"/>
                <EF:EFColumn ename="mouldCartName" cname="机组模具台车名称" align="center" width="200" enable="true"/>
                <EF:EFComboColumn ename="packagingType" cname="包装类型" align="center" width="150" enable="true" hidden="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFOption label="十字臂" value="10"/>
                    <EF:EFOption label="包装区" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100" primaryKey="true"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--跨区编码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo" width="90%" height="60%"/>
    <%--拆包区弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS05" id="unpackAreaInfo" width="90%" height="60%"/>
    <%--厂区区域弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS03" id="crossingChannelsInfo" width="90%" height="60%"/>
    <%--厂区厂房信息弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factoryAreaInfo" width="90%" height="60%"/>
</EF:EFPage>
