$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            },
            beforeAdd: function (e) {
            },
            afterAdd: function (e) {
            },
            beforeEdit: function (e) {
                //不可编辑
                e.preventDefault();
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
            },
            afterEdit: function (e) {
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //拆包区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "unpackAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-unpackAreaId").val(rows[0].unpackAreaId);
                $("#inqu_status-0-unpackAreaName").val(rows[0].unpackAreaName);
            }
        }
    });

})