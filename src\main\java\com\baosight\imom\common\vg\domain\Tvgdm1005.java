/**
 * Generate time : 2025-04-29 10:34:21
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm1005
 */
public class Tvgdm1005 extends DaoEPBase {

    private String relevanceId = " ";        /* 关联ID*/
    private String packId = " ";        /* 捆包号*/
    private String outPackId = " ";        /* 产出捆包号*/
    private String partId = " ";        /* 物料号*/
    private String packType = " ";        /* 捆包类型2成品4余料*/
    private String unitedPackId = " ";        /* 并包号*/
    private BigDecimal processHour = new BigDecimal("0");        /* 加工工时*/
    private BigDecimal netWeight = new BigDecimal("0");        /* 净重*/
    private BigDecimal quantity = new BigDecimal("0");        /* 数量*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String specsDesc = " ";        /* 规格描述*/
    private String stackName = "1";        /* 堆垛名称*/
    private String liftFlag = "0";        /* 吊运标记*/
    private String machineCode = " ";        /* 机组代码*/
    private String finishingShuntFlag = " ";        /* 精整分流标记*/
    private BigDecimal unitedQuantity = new BigDecimal("0");        /* 并包数量*/
    private String customerId = " ";        /* 客户代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("relevanceId");
        eiColumn.setDescName("关联ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outPackId");
        eiColumn.setDescName("产出捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packType");
        eiColumn.setDescName("捆包类型2成品4余料");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedPackId");
        eiColumn.setDescName("并包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processHour");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工工时");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("规格描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stackName");
        eiColumn.setDescName("堆垛名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("liftFlag");
        eiColumn.setDescName("吊运标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finishingShuntFlag");
        eiColumn.setDescName("精整分流标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedQuantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("并包数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);
    }

    /**
     * the constructor
     */
    public Tvgdm1005() {
        initMetaData();
    }

    /**
     * get the relevanceId - 关联ID
     *
     * @return the relevanceId
     */
    public String getRelevanceId() {
        return this.relevanceId;
    }

    /**
     * set the relevanceId - 关联ID
     */
    public void setRelevanceId(String relevanceId) {
        this.relevanceId = relevanceId;
    }

    /**
     * get the packId - 捆包号
     *
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the outPackId - 产出捆包号
     *
     * @return the outPackId
     */
    public String getOutPackId() {
        return this.outPackId;
    }

    /**
     * set the outPackId - 产出捆包号
     */
    public void setOutPackId(String outPackId) {
        this.outPackId = outPackId;
    }

    /**
     * get the partId - 物料号
     *
     * @return the partId
     */
    public String getPartId() {
        return this.partId;
    }

    /**
     * set the partId - 物料号
     */
    public void setPartId(String partId) {
        this.partId = partId;
    }

    /**
     * get the packType - 捆包类型2成品4余料
     *
     * @return the packType
     */
    public String getPackType() {
        return this.packType;
    }

    /**
     * set the packType - 捆包类型2成品4余料
     */
    public void setPackType(String packType) {
        this.packType = packType;
    }

    /**
     * get the unitedPackId - 并包号
     *
     * @return the unitedPackId
     */
    public String getUnitedPackId() {
        return this.unitedPackId;
    }

    /**
     * set the unitedPackId - 并包号
     */
    public void setUnitedPackId(String unitedPackId) {
        this.unitedPackId = unitedPackId;
    }

    /**
     * get the processHour - 加工工时
     *
     * @return the processHour
     */
    public BigDecimal getProcessHour() {
        return this.processHour;
    }

    /**
     * set the processHour - 加工工时
     */
    public void setProcessHour(BigDecimal processHour) {
        this.processHour = processHour;
    }

    /**
     * get the netWeight - 净重
     *
     * @return the netWeight
     */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
     * set the netWeight - 净重
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * get the quantity - 数量
     *
     * @return the quantity
     */
    public BigDecimal getQuantity() {
        return this.quantity;
    }

    /**
     * set the quantity - 数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the specsDesc - 规格描述
     *
     * @return the specsDesc
     */
    public String getSpecsDesc() {
        return this.specsDesc;
    }

    /**
     * set the specsDesc - 规格描述
     */
    public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
    }

    /**
     * get the stackName - 堆垛名称
     *
     * @return the stackName
     */
    public String getStackName() {
        return this.stackName;
    }

    /**
     * set the stackName - 堆垛名称
     */
    public void setStackName(String stackName) {
        this.stackName = stackName;
    }

    /**
     * get the liftFlag - 吊运标记
     *
     * @return the liftFlag
     */
    public String getLiftFlag() {
        return this.liftFlag;
    }

    /**
     * set the liftFlag - 吊运标记
     */
    public void setLiftFlag(String liftFlag) {
        this.liftFlag = liftFlag;
    }

    /**
     * get the machineCode - 机组代码
     *
     * @return the machineCode
     */
    public String getMachineCode() {
        return this.machineCode;
    }

    /**
     * set the machineCode - 机组代码
     */
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    /**
     * get the finishingShuntFlag - 精整分流标记
     *
     * @return the finishingShuntFlag
     */
    public String getFinishingShuntFlag() {
        return this.finishingShuntFlag;
    }

    /**
     * set the finishingShuntFlag - 精整分流标记
     */
    public void setFinishingShuntFlag(String finishingShuntFlag) {
        this.finishingShuntFlag = finishingShuntFlag;
    }

    /**
     * get the unitedQuantity - 并包数量
     *
     * @return the unitedQuantity
     */
    public BigDecimal getUnitedQuantity() {
        return this.unitedQuantity;
    }

    /**
     * set the unitedQuantity - 并包数量
     */
    public void setUnitedQuantity(BigDecimal unitedQuantity) {
        this.unitedQuantity = unitedQuantity;
    }

    /**
     * get the customerId - 客户代码
     *
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setRelevanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevanceId")), relevanceId));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setOutPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outPackId")), outPackId));
        setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
        setPackType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packType")), packType));
        setUnitedPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedPackId")), unitedPackId));
        setProcessHour(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processHour")), processHour));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
        setQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("quantity")), quantity));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
        setStackName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stackName")), stackName));
        setLiftFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("liftFlag")), liftFlag));
        setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
        setFinishingShuntFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finishingShuntFlag")), finishingShuntFlag));
        setUnitedQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("unitedQuantity")), unitedQuantity));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("relevanceId", StringUtils.toString(relevanceId, eiMetadata.getMeta("relevanceId")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("outPackId", StringUtils.toString(outPackId, eiMetadata.getMeta("outPackId")));
        map.put("partId", StringUtils.toString(partId, eiMetadata.getMeta("partId")));
        map.put("packType", StringUtils.toString(packType, eiMetadata.getMeta("packType")));
        map.put("unitedPackId", StringUtils.toString(unitedPackId, eiMetadata.getMeta("unitedPackId")));
        map.put("processHour", StringUtils.toString(processHour, eiMetadata.getMeta("processHour")));
        map.put("netWeight", StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
        map.put("quantity", StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("specsDesc", StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
        map.put("stackName", StringUtils.toString(stackName, eiMetadata.getMeta("stackName")));
        map.put("liftFlag", StringUtils.toString(liftFlag, eiMetadata.getMeta("liftFlag")));
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("finishingShuntFlag", StringUtils.toString(finishingShuntFlag, eiMetadata.getMeta("finishingShuntFlag")));
        map.put("unitedQuantity", StringUtils.toString(unitedQuantity, eiMetadata.getMeta("unitedQuantity")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));

        return map;

    }
}