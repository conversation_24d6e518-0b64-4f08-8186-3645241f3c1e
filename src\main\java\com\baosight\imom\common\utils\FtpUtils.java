package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Ftp操作工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since Date:2024-11-22
 */
public class FtpUtils {
    private static final String PATH_SEPARATOR = "/";
    // FTP配置文件中对应的key
    private static final String HOST_STR = ".ftpHost";
    private static final String PORT_STR = ".ftpPort";
    private static final String USERNAME_STR = ".ftpUsername";
    private static final String PASSWORD_STR = ".ftpPassword";
    private static final String BASE_PATH_STR = ".ftpBasePath";
    private static final String FILE_DOWN_URL_STR = ".ftpFileUrl";
    // 缓存各业务单元下FTP连接信息
    private static final Map<String, FtpConfig> FTP_CONFIG_MAP = new ConcurrentHashMap<>();

    /**
     * FTP连接信息
     */
    public static class FtpConfig {
        private final String host;
        private final int port;
        private final String username;
        private final String password;
        private String basePath;
        private final String fileDownUrl;
        private final String originPath;

        public FtpConfig(String host, int port, String username, String password, String fileDownUrl, String originPath) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
            this.fileDownUrl = fileDownUrl;
            this.originPath = originPath;
        }

        public String getHost() {
            return host;
        }

        public int getPort() {
            return port;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }

        public String getBasePath() {
            return basePath;
        }

        public void setBasePath(String basePath) {
            this.basePath = basePath;
        }

        public String getFileDownUrl() {
            return fileDownUrl;
        }

        public String getOriginPath() {
            return originPath;
        }
    }

    /**
     * 获取FTP连接配置信息
     */
    private static FtpConfig getFtpConfig(String segNo) {
        String segNoPrefix = segNo.substring(0, 2);
        // 先从缓存中获取
        FtpConfig config = FTP_CONFIG_MAP.get(segNoPrefix);
        if (config != null) {
            return config;
        }
        // 新建配置信息
        config = new FtpConfig(
                PlatApplicationContext.getProperty(segNoPrefix + HOST_STR),
                Integer.parseInt(PlatApplicationContext.getProperty(segNoPrefix + PORT_STR)),
                PlatApplicationContext.getProperty(segNoPrefix + USERNAME_STR),
                PlatApplicationContext.getProperty(segNoPrefix + PASSWORD_STR),
                PlatApplicationContext.getProperty(segNoPrefix + FILE_DOWN_URL_STR),
                PlatApplicationContext.getProperty(segNoPrefix + BASE_PATH_STR));
        // 添加至缓存
        FTP_CONFIG_MAP.put(segNoPrefix, config);
        return config;
    }

    /**
     * 获取FTP连接
     */
    private static FTPClient getFTPClient(FtpConfig config) {
        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient.connect(config.getHost(), config.getPort());
            ftpClient.login(config.getUsername(), config.getPassword());
            if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
                LogUtils.error(config.getHost() + ":" + config.getPort(), "未连接到FTP,请检查FTP配置信息");
                ftpClient.disconnect();
                return null;
            }
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            ftpClient.setControlEncoding("UTF-8");
            ftpClient.enterLocalPassiveMode();
        } catch (IOException e) {
            LogUtils.error("FTP连接失败", e.getMessage());
            return null;
        }
        return ftpClient;
    }

    /**
     * 上传文件
     *
     * @param segNo    业务单元代码
     * @param file     待上传文件(CommonsMultipartFile类型)
     * @param fileName 存储的文件名
     * @return 文件下载地址
     */
    public static String uploadFile(String segNo, CommonsMultipartFile file, String fileName) {
        FtpConfig config = getFtpConfig(segNo);
        return uploadFile(config, file, fileName);
    }

    /**
     * 上传文件
     *
     * @param config   FTP配置信息
     * @param file     待上传文件(CommonsMultipartFile类型)
     * @param fileName 存储的文件名
     * @return 文件下载地址
     */
    public static String uploadFile(FtpConfig config, CommonsMultipartFile file, String fileName) {
        // 如果文件名包含"/"，则将文件名和路径分开
        String appendPath = "";
        config.setBasePath(config.getOriginPath());
        if (fileName.contains(PATH_SEPARATOR)) {
            int lastIndex = fileName.lastIndexOf(PATH_SEPARATOR);
            appendPath = fileName.substring(0, lastIndex);
            // 设置存储路径
            config.setBasePath(config.getOriginPath() + PATH_SEPARATOR + appendPath);
            // 设置存储文件名
            fileName = fileName.substring(lastIndex + 1);
        }
        FTPClient ftpClient = null;
        String downloadUrl = null;
        try {
            ftpClient = getFTPClient(config);
            if (ftpClient == null) {
                LogUtils.error(config.getHost() + ":" + config.getPort(), "无法创建FTP连接");
                return null;
            }
            // 创建目录结构
            if (!createDirectories(ftpClient, config.getBasePath())) {
                LogUtils.error(config.getBasePath(), "创建FTP目录失败");
                return null;
            }
            // 生成存储路径
            String path = config.getBasePath() + PATH_SEPARATOR + fileName;
            // 上传文件内容
            if (uploadFileContent(ftpClient, file, path)) {
                if (!appendPath.isEmpty()) {
                    appendPath += PATH_SEPARATOR;
                }
                downloadUrl = config.getFileDownUrl() + appendPath + fileName;
            }
        } catch (IOException e) {
            LogUtils.error("文件上传异常", e.getMessage());
        } finally {
            disconnectFtpClient(ftpClient);
        }
        return downloadUrl;
    }

    /**
     * 创建目录结构
     *
     * @param ftpClient FTP连接
     * @param path      目录路径
     * @return 是否创建成功
     */
    private static boolean createDirectories(FTPClient ftpClient, String path) throws IOException {
        String[] dirs = path.split(PATH_SEPARATOR);
        String tempPath = "";
        for (String dir : dirs) {
            if (dir == null || dir.isEmpty()) {
                continue;
            }
            tempPath += PATH_SEPARATOR + dir;
            if (!ftpClient.makeDirectory(tempPath)) {
                // 目录可能已存在，尝试改变工作目录来验证
                if (!ftpClient.changeWorkingDirectory(tempPath)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 上传文件内容
     *
     * @param ftpClient  FTP连接
     * @param file       待上传文件(CommonsMultipartFile类型)
     * @param remotePath 存储路径
     * @return 是否上传成功
     */
    private static boolean uploadFileContent(FTPClient ftpClient, CommonsMultipartFile file, String remotePath) {
        try (BufferedInputStream bis = new BufferedInputStream(file.getInputStream())) {
            OutputStream outputStream = ftpClient.storeFileStream(remotePath);
            if (outputStream == null) {
                LogUtils.error(remotePath, "无法创建FTP输出流");
                return false;
            }
            // 缓冲区大小为16KB
            byte[] buffer = new byte[16384];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
            // 先关闭输出流
            outputStream.close();
            // 再完成传输并检查状态
            if (!ftpClient.completePendingCommand()) {
                LogUtils.error(remotePath, "文件传输未能完成");
                return false;
            }
            return true;
        } catch (IOException e) {
            LogUtils.error("上传文件内容异常", e.getMessage());
            return false;
        }
    }

    /**
     * 关闭FTP连接
     *
     * @param ftpClient FTP连接
     */
    private static void disconnectFtpClient(FTPClient ftpClient) {
        if (ftpClient != null && ftpClient.isConnected()) {
            try {
                ftpClient.disconnect();
            } catch (IOException e) {
                LogUtils.error("关闭FTP连接失败", e.getMessage());
            }
        }
    }

    /**
     * 下载文件
     *
     * @param config     FTP配置信息
     * @param remotePath 远程文件路径
     * @return 下载的文件
     */
    public static File downloadFile(FtpConfig config, String remotePath) {
        FTPClient ftpClient = getFTPClient(config);
        if (ftpClient == null) {
            return null;
        }

        File file = null;
        try {
            String fileName = remotePath.substring(remotePath.lastIndexOf(PATH_SEPARATOR) + 1);
            file = new File(System.getProperty("java.io.tmpdir") + File.separator + fileName);

            try (FileOutputStream fos = new FileOutputStream(file)) {
                if (ftpClient.retrieveFile(remotePath, fos)) {
                    return file;
                }
            }
        } catch (IOException e) {
            LogUtils.error("文件下载异常", e.getMessage());
            if (file.exists()) {
                file.delete();
            }
        } finally {
            try {
                ftpClient.disconnect();
            } catch (IOException e) {
                LogUtils.error("关闭FTP连接失败", e.getMessage());
            }
        }
        return null;
    }
}
