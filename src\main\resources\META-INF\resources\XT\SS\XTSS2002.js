$(function () {
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 10,
                pageSizes: [10, 20, 50, 100]
            },
            loadComplete: function (e) {
                // 确定按钮
                $("#CONFIRM").on("click", function (e) {
                    if(!IMOMUtil.checkSelected(resultGrid)){
                        return;
                    }
                    let windowId = $("#inqu_status-0-windowId").val();
                    if (IPLAT.isBlankString(windowId)) {
                        // 设置默认值
                        windowId = "tagInfo";
                    }
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                });
            }
        }
    };
});
