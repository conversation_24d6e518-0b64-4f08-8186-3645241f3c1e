package com.baosight.imom.vg.dm.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.commons.collections.CollectionUtils;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.common.vg.domain.Tvgdm0804;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;

/**
 * 检修资材领用信息表
 */
public class VGDM0804 extends Tvgdm0804 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0804.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0804.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0804.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0804.update";
    /**
     * 修改检修计划单号
     */
    public static final String UPDATE_VOUCHER_NUM = "VGDM0804.updateVoucherNum";
    /**
     * 查询数据用于上传IMC(检修计划)
     */
    public static final String QUERY_BY_OVERHAUL_PLAN = "VGDM0804.queryByOverhaulPlan";
    /**
     * 查询数据用于上传IMC(故障)
     */
    public static final String QUERY_BY_FAULT = "VGDM0804.queryByFault";

    @Override
    public String getCheckStatus() {
        return this.getStuffReceivingStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

    /**
     * 上传数据至IMC
     *
     * @param voucherNumList 检修计划编号
     * @param faultIdList    故障单号
     */
    public static void uploadToIMC(Dao dao, List<String> voucherNumList, List<String> faultIdList) {
        if (CollectionUtils.isEmpty(voucherNumList) && CollectionUtils.isEmpty(faultIdList)) {
            return;
        }
        // 检修单号不为空，按检修计划查询，否则按故障查询
        String sqlId = CollectionUtils.isNotEmpty(voucherNumList) ? QUERY_BY_OVERHAUL_PLAN : QUERY_BY_FAULT;
        // 查询条件
        Map<String, Object> params = new HashMap<>();
        // 按检修计划查询
        params.put("voucherNumList", voucherNumList);
        // 按故障查询
        params.put("faultIdList", faultIdList);
        // 查询待上传领用明细
        List<Map> list = dao.query(sqlId, params);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 按接口要求分组
        Map<String, List<Map>> groupedData = new HashMap<>();
        for (Map item : list) {
            String requisitionUnit = item.get("requisitionUnit").toString();
            if (StrUtil.isBlank(requisitionUnit)) {
                throw new PlatException(item.get("equipmentName") + "未找到对应机组代码，请检查");
            }
            String key = String.format("%s_%s_%s_%s_%s", item.get("segNo"), item.get("warehouseCode"), item.get("applyId"), item.get("deptId"), requisitionUnit);
            // 设置状态
            item.put("stuffReceivingStatus", "20");
            // 设置记录修改责任者
            RecordUtils.setRevisorSys(item);
            // 分组
            groupedData.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        }
        // 构建上传数据结构
        List<Map<String, Object>> uploadData = new ArrayList<>();
        for (List<Map> subList : groupedData.values()) {
            Map firstItem = subList.get(0);
            // 设置主信息
            Map<String, Object> mainInfo = new HashMap<>();
            mainInfo.put("segNo", firstItem.get("segNo"));
            mainInfo.put("warehouseCode", firstItem.get("warehouseCode"));
            mainInfo.put("warehouseName", firstItem.get("warehouseName"));
            mainInfo.put("applyId", firstItem.get("applyId"));
            mainInfo.put("applyName", firstItem.get("applyName"));
            mainInfo.put("deptId", firstItem.get("deptId"));
            mainInfo.put("deptName", firstItem.get("deptName"));
            mainInfo.put("requisitionUnit", firstItem.get("requisitionUnit"));
            mainInfo.put("requisitionUnitName", firstItem.get("requisitionUnitName"));
            // 设置明细信息
            List<Map<String, Object>> details = new ArrayList<>();
            for (Map item : subList) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("segNo", item.get("segNo"));
                detail.put("purContractNum", item.get("purContractNum"));
                detail.put("purOrderNum", item.get("purOrderNum"));
                detail.put("stuffCode", item.get("stuffCode"));
                detail.put("usingWgt", item.get("usingWgt"));
                detail.put("locationId", item.get("locationId"));
                detail.put("locationName", item.get("locationName"));
                details.add(detail);
            }
            mainInfo.put("details", details);
            uploadData.add(mainInfo);
        }
        // 调用接口上传数据至IMC
        EiInfo eiInfo = new EiInfo();
        eiInfo.addBlock(RecordUtils.createLoginUserBlock());
        eiInfo.set("result", uploadData);
        eiInfo.set(EiConstant.serviceId, "S_VF_PM_9029");
        EiInfo rtnInfo = EServiceManager.call(eiInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
        // 更新数据
        DaoUtils.updateBatch(dao, VGDM0804.UPDATE, list);
    }
}
