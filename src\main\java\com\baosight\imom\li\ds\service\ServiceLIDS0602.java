package com.baosight.imom.li.ds.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0602;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;

/**
 * 库位附属信息管理
 */
public class ServiceLIDS0602 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0602().eiMetadata);
        inInfo.addBlock("result2").addBlockMeta(new LIDS0602().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0602.QUERY, new LIDS0602());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (CollectionUtil.isNotEmpty(resultList)) {
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    int count = super.count(LIDS0602.COUNT, itemMap);
                    if (count > 0) {
                        throw new PlatException("该基准厂区，基准厂房，基准库位已存在!");
                    }
                    String[] arr = {segNo};
                    itemMap.put("pollingSchemeNumber",SequenceGenerator.getNextSequence("POLLING_SCHEME", arr));
                    //设置库位号
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0602.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();

            if (CollectionUtil.isNotEmpty(resultList)) {
                resultList.forEach(itemMap -> {
                    int count = super.count(LIDS0602.COUNT, itemMap);
                    if (count > 0) {
                        throw new PlatException("该基准厂区，基准厂房，基准库位已存在!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0602.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (CollectionUtil.isNotEmpty(resultList)) {
                resultList.forEach(itemMap -> {
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0602.DELETE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryLitter(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo,LIDS0602.QUERYLITTER, new LIDS0602(), false,
                        new LIDS0602().eiMetadata, "inqu_status","result2", "result2");
            } else {
                return outInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    /**
     * 新增子项
     *
     * @param inInfo
     * @return
     */
    public EiInfo insertLitter(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> result2List = inInfo.getBlock("result2").getRows();

            if (CollectionUtil.isNotEmpty(result2List)) {
                //List<HashMap> result1List = inInfo.getBlock("result").getRows();
                result2List.forEach(itemMap -> {
                    //校验子项是否存在
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    String pollingAcrossRegions = MapUtils.getString(itemMap, "pollingAcrossRegions");
                    String benchmarkCrossRegional = MapUtils.getString(itemMap, "benchmarkCrossRegional");
                    if(StringUtils.isNotBlank(pollingAcrossRegions)){
                        if(pollingAcrossRegions.equals(benchmarkCrossRegional)){
                            throw new PlatException("轮询跨区不能与基准跨区相同!");
                        }
                    }else{
                        throw new PlatException("轮询跨区不能为空!");
                    }
                    //校验子项是否存在
                    int count = super.count(LIDS0602.COUNTLITTER, itemMap);
                    if (count > 0) {
                        throw new PlatException("该轮询厂房，轮询跨区已存在!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                //this.dao.insertBatch( LIDS0602.INSERTLITTER,result2List );
                outInfo = super.insert(inInfo, LIDS0602.INSERTLITTER, new LIDS0602(),
                        false, "result2");
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增子项
     *
     * @param inInfo
     * @return
     */
    public EiInfo updateLitter(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> result2List = inInfo.getBlock("result2").getRows();

            if (CollectionUtil.isNotEmpty(result2List)) {
                //List<HashMap> result1List = inInfo.getBlock("result").getRows();
                result2List.forEach(itemMap -> {

                    String pollingAcrossRegions = MapUtils.getString(itemMap, "pollingAcrossRegions");
                    String benchmarkCrossRegional = MapUtils.getString(itemMap, "benchmarkCrossRegional");
                    if(StringUtils.isNotBlank(pollingAcrossRegions)){
                        if(pollingAcrossRegions.equals(benchmarkCrossRegional)){
                            throw new PlatException("轮询跨区不能与基准跨区相同!");
                        }
                    }else{
                        throw new PlatException("轮询跨区不能为空!");
                    }
                    //校验子项是否存在
                    int count = super.count(LIDS0602.COUNTLITTER, itemMap);
                    if (count > 0) {
                        throw new PlatException("该轮询厂房，轮询跨区已存在!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setRevisor(itemMap);
                });
                //this.dao.insertBatch( LIDS0602.INSERTLITTER,result2List );
                outInfo = super.insert(inInfo, LIDS0602.UPDATELITTER, new LIDS0602(),
                        false, "result2");
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    public EiInfo deleteLitter(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> result2List = inInfo.getBlock("result2").getRows();
            if (CollectionUtil.isNotEmpty(result2List)) {
                result2List.forEach(itemMap -> {
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                //outInfo = super.update(inInfo, LIDS0602.DELETELITTER);
                outInfo = super.insert(inInfo, LIDS0602.DELETELITTER, new LIDS0602(),
                        false, "result2");
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}
