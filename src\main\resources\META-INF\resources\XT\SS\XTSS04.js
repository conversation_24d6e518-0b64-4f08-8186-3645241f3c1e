$(function () {
    $("#QUERY").on("click", function (e) {
        result0Grid.dataSource.page(1);
        var dataItems = resultGrid.getDataItems();
        if (dataItems.length > 0) {
            resultGrid.removeRows(dataItems);
        }
    });

    IPLATUI.EFGrid = {
        "result0": {
            /**
             * EFGrid渲染成功的回调事件
             */
            loadComplete: function (grid) {
                // 获取勾选数据，
                if (grid.options.checkMode.indexOf("single") !== -1){
                    $(".check-all").hide(0);
                }
                // 新增开关定义
                $("#ADDSWITCH").on("click", function (e) {
                    // 新增一行
                    grid.addRow();
                    if (resultGrid.getDataItems().length > 0) {
                        resultGrid.removeRows(resultGrid.getDataItems());
                    }
                });
                // 修改
                $("#UPDATE2").on("click", function (e) {
                    if(IMOMUtil.checkSelected(grid)){
                        // 修改标记
                        var updateFlag = true;
                        var checkRows = grid.getCheckedRows();
                        $.each(checkRows, function (index, item) {
                            if (item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非修改行，请勿勾选！</b>',
                                    okFn: function (e) {},
                                    title: '修改记录'
                                });
                                updateFlag = false;
                                return false;
                            }
                        });

                        // 必填校验
                        var checkFlag = checkField(checkRows, "result0");

                        if (updateFlag && checkFlag) {
                            var inInfo = new EiInfo();
                            inInfo.addBlock(grid.getCheckedBlockData());
                            IPLAT.progress($("#result0"), true);
                            EiCommunicator.send("XTSS04", "update2", inInfo, {
                                onSuccess: function (ei) {
                                    if ("-1" == ei.status ) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        IPLAT.progress($("#result0"), false);
                                    }else{
                                        result0Grid.setEiInfo(ei);
                                        // result0Grid.refresh();
                                        NotificationUtil({msg: ei.msg}, "success");
                                        IPLAT.progress($("#result0"), false);
                                    }
                                },
                                onFail: function (ei) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    return false;
                                    IPLAT.progress($("#result0"), false);
                                }
                            });
                        }

                    }
                });
                // 	删除
                $("#DELETE2").on("click", function (e) {
                    if(IMOMUtil.checkSelected(grid)){
                        IPLAT.confirm({
                            title:"提示信息",
                            message: "你确认要删除吗?",
                            okFn: function (e) {
                                var rowNum = [];
                                var checkRows = grid.getCheckedRows();
                                $.each(checkRows, function (index, item) {
                                    if (item.isNew()) {
                                        rowNum.push(index);
                                    }
                                });
                                result0Grid.removeRows(rowNum);
                                var checkRows2 = grid.getCheckedRows();

                                var inInfo = new EiInfo();
                                inInfo.addBlock(grid.getCheckedBlockData());
                                IPLAT.progress($("#result0"), true);
                                EiCommunicator.send("XTSS04", "delete2", inInfo, {
                                    onSuccess: function (ei) {
                                        if ("-1" == ei.status ) {
                                            NotificationUtil({msg: ei.msg}, "error");
                                            IPLAT.progress($("#result0"), false);
                                        }else{
                                            result0Grid.removeRows(checkRows2);
                                            NotificationUtil({msg: ei.msg}, "success");
                                            IPLAT.progress($("#result0"), false);
                                        }
                                    },
                                    onFail: function (ei) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        return false;
                                        IPLAT.progress($("#result0"), false);
                                    }
                                });
                            }
                        });
                    }
                });
                // 新增
                $("#INSERT2").on("click", function (e) {
                    // 新增标记 true为新增行
                    var newFlag = true;
                    var checkRows = grid.getCheckedRows();
                    // 判断有没有勾选新增行
                    if (checkRows.length > 0) {
                        $.each(checkRows, function (index, item) {
                            if (!item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非新增行，请勿勾选!</b>',
                                    okFn: function (e) {},
                                    title: '新增记录'
                                });
                                newFlag = false;
                                return false;
                            }
                        });
                    } else {
                        IPLAT.alert({
                            message: '<b>没有选中待新增的行!</b>',
                            okFn: function (e) {},
                            title: '新增记录'
                        });
                        newFlag = false;
                        return false;
                    }

                    // 必填校验
                    var checkFlag = checkField(checkRows, "result0");

                    if (newFlag && checkFlag) {
                        var inInfo = new EiInfo();
                        // 将result节点下的所有子节点的值赋给EiInfo
                        inInfo.setByNode("result0");
                        inInfo.addBlock(grid.getCheckedBlockData());
                        IPLAT.progress($("#result0"), true);
                        EiCommunicator.send("XTSS04", "insert2", inInfo, {
                            onSuccess: function (ei) {
                                if ("-1" == ei.status ) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    IPLAT.progress($("#result0"), false);
                                }else{
                                    result0Grid.setEiInfo(ei);
                                    NotificationUtil({msg: ei.msg}, "success");
                                    IPLAT.progress($("#result0"), false);
                                }
                            },
                            onFail: function (ei) {
                                NotificationUtil({msg: ei.msg}, "error");
                                return false;
                                IPLAT.progress($("#result0"), false);
                            }
                        });
                    }
                });
            },
            /**
             * 点击行首checkbox，勾选行时触发的事件
             * @param e     kendo的Event对象
             * e.sender     kendoGrid对象，resultGrid
             * e.fake       用于区分是手动点击的事件还是模拟的事件
             * e.checked    用于区分是勾选还是取消勾选
             * e.model      勾选或取消勾选的行数据，kendo.data.Model
             * e.row        当前行的行号
             * e.tr         行的tr,包括固定列和数据列 jquery对象
             */
            onCheckRow: function (e) {
                if (!e.fake && e.checked) {
                    $("#inqu_status-0-processSwitchName").val(e.model.processSwitchName);
                    queryDetail(e.model);
                } else {
                    $("#inqu_status-0-processSwitchName").val('');
                }
            },
            /**
             * 勾选行后，点击单元格准备编辑时的事件
             * beforeEdit可以用于自定义单元格是否可以编辑，不要和列的readonly，enable混用
             * @param e 事件对象
             * e.sender Grid对象
             * e.container 单元格td jQuery对象
             * e.row 行号
             * e.col 列号(columns中的列配置信息数组中的column对象的index)
             * e.model 行数据对象 kendo.data.Model
             * e.field 列英文名
             * e.preventDefault 禁止编辑
             */
            beforeEdit: function (e) {
                // 获取result当前页的数据
                var dataItems = resultGrid.getDataItems();
                // 判断当前行是不是新增的行
                if (!e.model.isNew() && dataItems.length > 0) {
                    // 判断单元格 field 禁止编辑
                    if (e.field === "processSwitchName") {
                        e.preventDefault();
                        NotificationUtil({msg: "此开关定义下存在开关配置不允许修改!"}, "error");
                        return false;
                    } else if (e.field === "processSwitchDesc") {
                        e.preventDefault();
                        NotificationUtil({msg: "此开关定义下存在开关配置不允许修改!"}, "error");
                        return false;
                    } else if (e.field === "processSwitchValueDesc") {
                        e.preventDefault();
                        NotificationUtil({msg: "此开关定义下存在开关配置不允许修改!"}, "error");
                        return false;
                    }
                }
            }
        },
        "result": {
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo2",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "processSwitchValue",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "系统开关值",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "ef_popup_switchValue",
                                textElement: $(container),
                                width: 350,
                                title: ""
                            })
                        }
                    }
                }
            ],
            /**
             * EFGrid渲染成功的回调事件
             */
            loadComplete: function (grid) {
                // 开关配置
                $("#SWITCHSETTING").on("click", function (e) {
                    if(IMOMUtil.checkOneSelect(result0Grid)){
                        var checkRows = result0Grid.getCheckedRows();
                        var model = new kendo.data.Model({
                            uuid:'',
                            unitCode:'',
                            segNo:'',
                            segName:'',
                            rowType:'setting',
                            processSwitchName: checkRows[0].processSwitchName,
                            processSwitchDesc: checkRows[0].processSwitchDesc,
                            processSwitchValue:'',
                            processSwitchValueDesc: checkRows[0].processSwitchValueDesc,
                            recCreator:'',
                            recCreatorName:'',
                            recCreateTime:'',
                            recRevisor:'',
                            recRevisorName:'',
                            recReviseTime:''
                        });

                        // 新增一行
                        resultGrid.addRows(model, false, true);
                    }
                });
                // 修改
                $("#UPDATE1").on("click", function (e) {
                    if(IMOMUtil.checkSelected(grid)){
                        // 修改标记
                        var updateFlag = true;
                        var checkRows = grid.getCheckedRows();
                        $.each(checkRows, function (index, item) {
                            if (item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非修改行，请勿勾选！</b>',
                                    okFn: function (e) {},
                                    title: '修改记录'
                                });
                                updateFlag = false;
                                return false;
                            }
                        });

                        // 必填校验
                        var checkFlag = checkField(checkRows, "result");
                        if (updateFlag && checkFlag) {
                            var inInfo = new EiInfo();
                            inInfo.addBlock(grid.getCheckedBlockData());
                            IPLAT.progress($("#result"), true);
                            EiCommunicator.send("XTSS04", "update1", inInfo, {
                                onSuccess: function (ei) {
                                    if ("-1" == ei.status ) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        IPLAT.progress($("#result"), false);
                                    }else{
                                        resultGrid.setEiInfo(ei);
                                        resultGrid.refresh();
                                        NotificationUtil({msg: ei.msg}, "success");
                                        IPLAT.progress($("#result"), false);
                                    }
                                },
                                onFail: function (ei) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    return false;
                                    IPLAT.progress($("#result"), false);
                                }
                            });
                        }

                    }
                });
                // 	删除
                $("#DELETE1").on("click", function (e) {
                    if(IMOMUtil.checkSelected(grid)){
                        IPLAT.confirm({
                            title:"提示信息",
                            message: "你确认要删除吗?",
                            okFn: function (e) {
                                var rowNum = [];
                                var checkRows = grid.getCheckedRows();
                                $.each(checkRows, function (index, item) {
                                    if (item.isNew()) {
                                        rowNum.push(index);
                                    }
                                });
                                resultGrid.removeRows(rowNum);
                                var checkRows2 = grid.getCheckedRows();

                                var inInfo = new EiInfo();
                                inInfo.addBlock(grid.getCheckedBlockData());
                                IPLAT.progress($("#result"), true);
                                EiCommunicator.send("XTSS04", "delete1", inInfo, {
                                    onSuccess: function (ei) {
                                        if ("-1" == ei.status ) {
                                            NotificationUtil({msg: ei.msg}, "error");
                                            IPLAT.progress($("#result"), false);
                                        }else{
                                            resultGrid.removeRows(checkRows2);
                                            NotificationUtil({msg: ei.msg}, "success");
                                            IPLAT.progress($("#result"), false);
                                        }
                                    },
                                    onFail: function (ei) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        return false;
                                        IPLAT.progress($("#result"), false);
                                    }
                                });
                            }
                        });
                    }
                });
                // 新增
                $("#INSERT1").on("click", function (e) {
                    // 新增标记 true为新增行
                    var newFlag = true;
                    var checkRows = grid.getCheckedRows();
                    // 判断有没有勾选新增行
                    if (checkRows.length > 0) {
                        $.each(checkRows, function (index, item) {
                            if (!item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非新增行，请勿勾选!</b>',
                                    okFn: function (e) {},
                                    title: '新增记录'
                                });
                                newFlag = false;
                                return false;
                            }
                        });
                    } else {
                        IPLAT.alert({
                            message: '<b>没有选中待新增的行!</b>',
                            okFn: function (e) {},
                            title: '新增记录'
                        });
                        newFlag = false;
                        return false;
                    }

                    // 必填校验
                    var checkFlag = checkField(checkRows, "result");
                    if (newFlag && checkFlag) {
                        if (checkRows.length > 0) {
                            const unique = checkRows.filter(
                                (obj, index) =>
                                    checkRows.findIndex(
                                        (item) => item.processSwitchName === obj.processSwitchName && item.segNo === obj.segNo && item.unitCode === obj.unitCode
                                    ) === index
                            );

                            if (checkRows.length > unique.length) {
                                /*e.preventDefault();*/
                                NotificationUtil({msg: "此开关存在重复系统账套的记录，请检查！"}, "error");
                                return false;
                            }
                        }

                        var inInfo = new EiInfo();
                        // 将result节点下的所有子节点的值赋给EiInfo
                        inInfo.setByNode("result0");
                        inInfo.addBlock(grid.getCheckedBlockData());
                        IPLAT.progress($("#result"), true);
                        EiCommunicator.send("XTSS04", "insert1", inInfo, {
                            onSuccess: function (ei) {
                                if ("-1" == ei.status ) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    IPLAT.progress($("#result"), false);
                                }else{
                                    resultGrid.setEiInfo(ei);
                                    NotificationUtil({msg: ei.msg}, "success");
                                    IPLAT.progress($("#result"), false);
                                }
                            },
                            onFail: function (ei) {
                                NotificationUtil({msg: ei.msg}, "error");
                                return false;
                                IPLAT.progress($("#result"), false);
                            }
                        });
                    }
                });
            },
            /**
             * 勾选行后，点击单元格准备编辑时的事件
             * beforeEdit可以用于自定义单元格是否可以编辑，不要和列的readonly，enable混用
             * @param e 事件对象
             * e.sender Grid对象
             * e.container 单元格td jQuery对象
             * e.row 行号
             * e.col 列号(columns中的列配置信息数组中的column对象的index)
             * e.model 行数据对象 kendo.data.Model
             * e.field 列英文名
             * e.preventDefault 禁止编辑
             */
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    // 判断单元格 field 禁止编辑
                    if (e.field === "unitCode") {
                        e.preventDefault();
                        NotificationUtil({msg: "业务单元代码不可修改!"}, "error");
                        return false;
                    } else if (e.field === "segNo") {
                        e.preventDefault();
                        NotificationUtil({msg: "系统账套不可修改!"}, "error");
                        return false;
                    } else if (e.field === "processSwitchName") {
                        e.preventDefault();
                        NotificationUtil({msg: "系统开关名称不可修改!"}, "error");
                        return false;
                    } else if (e.field === "processSwitchDesc") {
                        e.preventDefault();
                        NotificationUtil({msg: "系统开关描述不可修改!"}, "error");
                        return false;
                    } else if (e.field === "processSwitchValueDesc") {
                        e.preventDefault();
                        NotificationUtil({msg: "系统开关值描述不可修改!"}, "error");
                        return false;
                    }
                } else {
                    if (e.model.rowType && e.model.rowType == "setting"){
                        if (e.field === "processSwitchName") {
                            e.preventDefault();
                            NotificationUtil({msg: "系统开关名称不可修改!"}, "error");
                            return false;
                        } else if (e.field === "processSwitchDesc") {
                            e.preventDefault();
                            NotificationUtil({msg: "系统开关描述不可修改!"}, "error");
                            return false;
                        } else if (e.field === "processSwitchValueDesc") {
                            e.preventDefault();
                            NotificationUtil({msg: "系统开关值描述不可修改!"}, "error");
                            return false;
                        }
                    }
                }
                if (e.field === "processSwitchValue") {
                    var checkRows = result0Grid.getCheckedRows();
                    querySwitchValueByName(checkRows[0].processSwitchName);
                }
            }
        },
        "result3": {
            // 隐藏右侧自定义导出按钮
            exportGrid: false,
            // 隐藏分页栏
            pageable: false,
            //双击选中
            onRowDblClick: function (e) {
                //回填数据
                resultGrid.setCellValue(editorModel,"processSwitchValue", e.model['processSwitchValue']);
                // 关闭window
                $("#ef_popup_switchValue").data("kendoWindow").close();
            }
        }
    };

    IPLATUI.EFPopupInput = {
        "inqu_status-0-unitCode": {
            clearInput: function (e) {
                $("#inqu_status-0-unitCode").val('');
                $("#inqu_status-0-segNo").val('');
            }
        }
    };

    IPLATUI.EFWindow = {
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                }
            }
        },
        "unitInfo2": {
            // 打开窗口事件
            open: function(e) {
                var $iframe = unitInfo2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo2");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    resultGrid.setCellValue(editorModel,"unitCode", row[0].unitCode);
                    resultGrid.setCellValue(editorModel,"segName", row[0].segName);
                    resultGrid.setCellValue(editorModel,"segNo", row[0].segNo);
                }
            }
        }
    };
});

/**
 * 校验字段必填项
 * @param listModel
 * @param blockId
 * @returns {boolean}
 */
function checkField(listModel, blockId) {
    // 校验标记
    var checkFlag = true;
    if (blockId == "result") {
        $.each(listModel, function (index, item) {
            if (IPLAT.isBlankString(item.unitCode)) {
                checkFlag = false;
                NotificationUtil({msg: "业务单元代码和系统账套不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.processSwitchValue)) {
                checkFlag = false;
                NotificationUtil({msg: "系统开关值不能为空!"}, "error");
                return false;
            }
        });
    } else if (blockId == "result0") {
        $.each(listModel, function (index, item) {
            if (IPLAT.isBlankString(item.processSwitchName)) {
                checkFlag = false;
                NotificationUtil({msg: "系统开关名称不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.processSwitchDesc)) {
                checkFlag = false;
                NotificationUtil({msg: "系统开关描述不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.processSwitchValueDesc)) {
                checkFlag = false;
                NotificationUtil({msg: "系统开关值描述不能为空!"}, "error");
                return false;
            } else {
                if (item.processSwitchName.length > 50) {
                    checkFlag = false;
                    NotificationUtil({msg: "系统开关名称最大长度为50个字符，请检查!"}, "error");
                    return false;
                } else if (item.processSwitchDesc.length > 100) {
                    checkFlag = false;
                    NotificationUtil({msg: "系统开关描述最大长度为100个字符，请检查!"}, "error");
                    return false;
                } else if (item.processSwitchValueDesc.length > 100) {
                    checkFlag = false;
                    NotificationUtil({msg: "系统开关值描述最大长度为100个字符，请检查!"}, "error");
                    return false;
                } else {
                    // 系统开关值描述书写格式判断
                    var colonE = item.processSwitchValueDesc.indexOf(":");
                    var colonC = item.processSwitchValueDesc.indexOf("：");
                    var semicolonE = item.processSwitchValueDesc.indexOf(";");
                    var semicolonC = item.processSwitchValueDesc.indexOf("；");
                    if ((colonE == -1 && colonC == -1) || (semicolonE == -1 && semicolonC == -1)) {
                        checkFlag = false
                        NotificationUtil({msg: "系统开关值描述格式不正确，请检查!"}, "error");
                        return false;
                    }
                }
            }
        });
    } else {
        checkFlag = false;
    }
    return checkFlag;
};

/**
 * 系统开关详细查询
 * @param model
 */
function queryDetail(model) {
    var inInfo = new EiInfo();
    inInfo.set("inqu_status-0-processSwitchName", model.processSwitchName);
    IPLAT.progress($("#result"), true);
    EiCommunicator.send("XTSS04", "queryDetail", inInfo, {
        onSuccess: function (eiInfo) {
            resultGrid.setEiInfo(eiInfo);
            IPLAT.progress($("#result"), false);
        },
        onFail: function (eiInfo) {
            NotificationUtil({msg: eiInfo.msg}, "error");
            return false;
            IPLAT.progress($("#result"), false);
        }
    });
};

/**
 * 根据开关名称获取开关值
 * @param model
 */
function querySwitchValueByName(model) {
    var inInfo = new EiInfo();
    inInfo.set("inqu_status-0-processSwitchName", model);
    EiCommunicator.send("XTSS04", "querySwitchValueByName", inInfo, {
        onSuccess: function (eiInfo) {
            result3Grid.setEiInfo(eiInfo);
        },
        onFail: function (eiInfo) {
            NotificationUtil({msg: eiInfo.msg}, "error");
            return false;
        }
    });
};