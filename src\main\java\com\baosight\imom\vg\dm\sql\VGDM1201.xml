<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2025-05-27 15:02:24
       Version :  1.0
    tableName :mevg.tvgdm1201
     UNIT_CODE  VARCHAR   NOT NULL,
     SEG_NO  VARCHAR   NOT NULL,
     ANNUAL_PROCESSING_VOLUME  DECIMAL,
     ANNUAL_PRODUCTION_RATE  DECIMAL,
     UUID  VARCHAR   NOT NULL   primarykey,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  VARCHAR   NOT NULL,
     DEL_FLAG  VARCHAR   NOT NULL
-->
<sqlMap namespace="VGDM1201">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1201">
        SELECT
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        DEPT_NAME as "deptName",<!--部门-->
        PARTICULAR_YEAR as "particularYear",<!--年份-->
        ANNUAL_PROCESSING_VOLUME as "annualProcessingVolume",  <!-- 年度加工量 -->
        ANNUAL_PRODUCTION_RATE as "annualProductionRate",  <!-- 年度达产率 -->
        STEEL_MANUFACTURING_COST_ACCUM as "steelManufacturingCostAccum",
        ACTUAL_MANUFACTURING_COST_TON as "actualManufacturingCostTon",
        MANUFACTURING_COST_COMPLETION as "manufacturingCostCompletion",
        PRODUCTION_RATE_ACCUMULATED as "productionRateAccumulated",
        PRODUCTION_CAPACITY_ACTUAL as "productionCapacityActual",
        PRODUCTION_CAPACITY_COMPLETION as "productionCapacityCompletion",
        OEE_EQUIPMENT_RATE_ACCUMULATED as "oeeEquipmentRateAccumulated",
        OEE_EQUIPMENT_RATE as "oeeEquipmentRate",
        QUALITY_DISPUTE_ACCUMULATED as "qualityDisputeAccumulated",
        QUALITY_DISPUTE_COMPLETION as "qualityDisputeCompletion",
        MANAGEMENT_IMPROVEMENT_RATE as "managementImprovementRate",
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag" <!-- 删除标记 -->
        FROM mevg.tvgdm1201 WHERE 1=1
        AND DEL_FLAG = '0'
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="particularYear">
            PARTICULAR_YEAR = #particularYear#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryVgdm1202" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1201">
        SELECT
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        PARTICULAR_YEAR as "particularYear",<!--年份-->
        ANNUAL_PROCESSING_VOLUME as "annualProcessingVolume",  <!-- 年度加工量 -->
        ANNUAL_PRODUCTION_RATE as "annualProductionRate",  <!-- 年度达产率 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag" <!-- 删除标记 -->
        FROM mevg.tvgdm1202 WHERE 1=1
        AND DEL_FLAG = '0'
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="particularYear">
            PARTICULAR_YEAR = #particularYear#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                PARTICULAR_YEAR DESC
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM mevg.tvgdm1201 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="annualProcessingVolume">
            ANNUAL_PROCESSING_VOLUME = #annualProcessingVolume#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="annualProductionRate">
            ANNUAL_PRODUCTION_RATE = #annualProductionRate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
    -->

    <insert id="insertVgdm1202">
        INSERT INTO mevg.tvgdm1202 (UNIT_CODE,  <!-- 业务单元代码 -->
        SEG_NO,  <!-- 系统帐套 -->
        PARTICULAR_YEAR,<!--年份-->
        ANNUAL_PROCESSING_VOLUME,  <!-- 年度加工量 -->
        ANNUAL_PRODUCTION_RATE,  <!-- 年度达产率 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG  <!-- 删除标记 -->
        )
        VALUES (#unitCode#, #segNo#, #particularYear#, #annualProcessingVolume#, #annualProductionRate#,
        #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#)
    </insert>

    <insert id="insert">
        INSERT INTO mevg.tvgdm1201 (UNIT_CODE,  <!-- 业务单元代码 -->
        SEG_NO,  <!-- 系统帐套 -->
        PARTICULAR_YEAR,<!--年份-->
        DEPT_NAME,<!--部门-->
        STEEL_MANUFACTURING_COST_ACCUM,
        MANUFACTURING_COST_COMPLETION,
        PRODUCTION_RATE_ACCUMULATED,
        PRODUCTION_CAPACITY_COMPLETION,
        OEE_EQUIPMENT_RATE_ACCUMULATED,
        OEE_EQUIPMENT_RATE,
        QUALITY_DISPUTE_ACCUMULATED,
        QUALITY_DISPUTE_COMPLETION,
        MANAGEMENT_IMPROVEMENT_RATE,
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG  <!-- 删除标记 -->
        )
        VALUES (#unitCode#, #segNo#, #particularYear#,#deptName#,
        #steelManufacturingCostAccum#,
        #manufacturingCostCompletion#,
        #productionRateAccumulated#,
        #productionCapacityCompletion#,
        #oeeEquipmentRateAccumulated#,
        #oeeEquipmentRate#,
        #qualityDisputeAccumulated#,
        #qualityDisputeCompletion#,
        #managementImprovementRate#,
        #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#)
    </insert>

    <delete id="delete">
        DELETE FROM mevg.tvgdm1201 WHERE
        UUID = #uuid#
    </delete>

    <delete id="deleteVgdm1202">
        DELETE FROM mevg.tvgdm1202 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE mevg.tvgdm1201
        SET
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        PARTICULAR_YEAR = #particularYear#,<!--年份-->
        DEPT_NAME = #deptName#,<!--部门-->
        ANNUAL_PRODUCTION_RATE = #annualProductionRate#,   <!-- 年度达产率 -->
        STEEL_MANUFACTURING_COST_ACCUM = #steelManufacturingCostAccum#,
        ACTUAL_MANUFACTURING_COST_TON = #actualManufacturingCostTon#,
        MANUFACTURING_COST_COMPLETION = #manufacturingCostCompletion#,
        PRODUCTION_RATE_ACCUMULATED = #productionRateAccumulated#,
        PRODUCTION_CAPACITY_ACTUAL = #productionCapacityActual#,
        PRODUCTION_CAPACITY_COMPLETION = #productionCapacityCompletion#,
        OEE_EQUIPMENT_RATE_ACCUMULATED = #oeeEquipmentRateAccumulated#,
        OEE_EQUIPMENT_RATE = #oeeEquipmentRate#,
        QUALITY_DISPUTE_ACCUMULATED = #qualityDisputeAccumulated#,
        QUALITY_DISPUTE_COMPLETION = #qualityDisputeCompletion#,
        MANAGEMENT_IMPROVEMENT_RATE = #managementImprovementRate#,
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#  <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptName">
            DEPT_NAME = #deptName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="particularYear">
            PARTICULAR_YEAR = #particularYear#
        </isNotEmpty>
    </update>

    <update id="updateVgdm1202">
        UPDATE mevg.tvgdm1202
        SET
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        PARTICULAR_YEAR = #particularYear#,<!--年份-->
        ANNUAL_PROCESSING_VOLUME = #annualProcessingVolume#,   <!-- 年度加工量 -->
        ANNUAL_PRODUCTION_RATE = #annualProductionRate#,   <!-- 年度达产率 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#  <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="particularYear">
            PARTICULAR_YEAR = #particularYear#
        </isNotEmpty>
    </update>

    <!--	修改删除标记-->
    <update id="updateStatus">
        UPDATE mevg.tvgdm1201
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#  <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <!--	修改删除标记-->
    <update id="updateStatusVgdm1202">
        UPDATE mevg.tvgdm1202
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#  <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>


    <select id="queryForScreen" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        PARTICULAR_YEAR as "particularYear",<!--年份-->
        ANNUAL_PROCESSING_VOLUME as "annualProcessingVolume",  <!-- 年度加工量 -->
        ANNUAL_PRODUCTION_RATE as "annualProductionRate"  <!-- 年度达产率 -->
        FROM mevg.tvgdm1202 WHERE 1=1
        AND SEG_NO = #segNo#
        AND DEL_FLAG = '0'
        AND PARTICULAR_YEAR &lt;= year(now())
        and PARTICULAR_YEAR &gt; year(now())-5
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                PARTICULAR_YEAR DESC
            </isEmpty>
        </dynamic>

    </select>

    <!--查询当年所有数据    -->
    <select id="queryForScreenTwo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        PARTICULAR_YEAR as "particularYear",<!--年份-->
        DEPT_NAME as "deptName",<!--部门-->
        STEEL_MANUFACTURING_COST_ACCUM as "steelManufacturingCostAccum", <!-- 吨钢制造费用累计 -->
        ACTUAL_MANUFACTURING_COST_TON as "actualManufacturingCostTon", <!-- 吨钢制造费用实绩 -->
        MANUFACTURING_COST_COMPLETION as "manufacturingCostCompletion", <!-- 吨钢制造费用完成度 -->
        PRODUCTION_RATE_ACCUMULATED as "productionRateAccumulated", <!-- 达产率累计 -->
        PRODUCTION_CAPACITY_ACTUAL as "productionCapacityActual", <!-- 达产率实绩 -->
        PRODUCTION_CAPACITY_COMPLETION as "productionCapacityCompletion", <!-- 达产率完成度 -->
        OEE_EQUIPMENT_RATE_ACCUMULATED as "oeeEquipmentRateAccumulated",
        OEE_EQUIPMENT_RATE as "oeeEquipmentRate", <!-- OEE设备综合利用率 -->
        QUALITY_DISPUTE_ACCUMULATED as "qualityDisputeAccumulated",
        QUALITY_DISPUTE_COMPLETION as "qualityDisputeCompletion", <!-- 质量异议完成度 -->
        MANAGEMENT_IMPROVEMENT_RATE as "managementImprovementRate" <!-- 工厂管理提升 -->
        FROM mevg.tvgdm1201 WHERE 1=1
        AND DEL_FLAG = '0'
        AND PARTICULAR_YEAR = year(now())
    </select>

</sqlMap>