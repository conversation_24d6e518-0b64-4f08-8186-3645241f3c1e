package com.baosight.imom.common.config;

import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * @description: restTemplate配置
 * @author: 韩亚宁
 * @createTime:2024-12-26 10:05
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 连接池的最大连接数
     */
    @Value("${pool.maxTotalConnect}")
    private int maxTotalConnect ;
    /**
     * 同路由的并发数
     */
    @Value("${pool.maxConnectPerRoute}")
    private int maxConnectPerRoute ;
    /**
     * 客户端和服务器建立连接超时，默认2s
     */
    @Value("${pool.connectTimeout}")
    private int connectTimeout;
    /**
     * 指客户端从服务器读取数据包的间隔超时时间,不是总读取时间，默认30s
     */
    @Value("${pool.readTimeout}")
    private int readTimeout;

    /**
     * 从连接池获取连接的超时时间,不宜过长,单位ms
     */
    @Value("${pool.connectionRequestTimout}")
    private int connectionRequestTimout;
    /**
     * 针对不同的地址,特别设置不同的长连接保持时间,单位 s
     */
    @Value("${pool.keepAliveTime}")
    private int keepAliveTime;

    @Bean
    public RestTemplate multiRestTemplate(RestTemplateBuilder builder) {
        RestTemplate restTemplate = builder.build();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        return restTemplate;
    }


    /**
     * 客户端请求链接策略
     *
     * @return
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder().build());
        clientHttpRequestFactory.setConnectTimeout(connectTimeout); // 连接超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(readTimeout); // 读写超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(connectionRequestTimout);// 请求超时时间/毫秒
        return clientHttpRequestFactory;
    }

    /**
     * 设置HTTP连接管理器,连接池相关配置管理
     *
     * @return 客户端链接管理器
     */
    @Bean
    public HttpClientBuilder httpClientBuilder() {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setConnectionManager(poolingConnectionManager());
        return httpClientBuilder;
    }

    /**
     * 链接线程池管理,可以keep-alive不断开链接请求,这样速度会更快 MaxTotal 连接池最大连接数 DefaultMaxPerRoute
     * 每个主机的并发 ValidateAfterInactivity
     * 可用空闲连接过期时间,重用空闲连接时会先检查是否空闲时间超过这个时间，如果超过，释放socket重新建立
     *
     * @return
     */
    @Bean
    public HttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        poolingConnectionManager.setMaxTotal(maxTotalConnect);
        poolingConnectionManager.setDefaultMaxPerRoute(maxConnectPerRoute);
        poolingConnectionManager.setValidateAfterInactivity(keepAliveTime);
        return poolingConnectionManager;
    }
}
