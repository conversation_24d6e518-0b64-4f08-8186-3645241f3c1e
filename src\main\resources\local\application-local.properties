spring.mvc.servlet.path=/
logging.level.com.baosight=info
spring.main.allow-bean-definition-overriding=true
server.port=8080
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**

projectName=imom
componentEname=imom
projectEnv=local
moduleName=imom

platSchema=iplat4j

#"Baosteel Group" unicode encoding in Chinese, and ANY Chinese should be unicode encoded in this file.
enterpriseName=\u5b9d\u4fe1\u8f6f\u4ef6ERP\u8f6f\u4ef6\u4e8b\u4e1a\u90e8
#customerName=\u5b9d\u94a2\u80a1\u4efd\u8425\u9500\u7ba1\u7406\u90e8

datasource.type=dbcp
#mysql ??mysql????? ??????????????
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=****************************************************************************************************
jdbc.username=root
jdbc.password=123456
jdbc.minIdle=2
jdbc.maxActive=5
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00

#redis
iplat.core.cache.defaultCacheType=localCache
iplat.core.cache.type=local
xservices.security.cacheType=local
iplat.core.transaction.type=local

iplat.redis.database=4
iplat.redis.host=127.0.0.1
iplat.redis.port=6379
iplat.redis.pass=

configEx=iplat4j;xservices;