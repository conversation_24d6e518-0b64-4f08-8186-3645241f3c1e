<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-02 10:17:38
   		Version :  1.0
		table comment : 生产需求单产出表
		tableName :${meviSchema}.TVIPM0007
		 UNIT_CODE  VARCHAR, 
		 PROCESS_ORDER_ID  VARCHAR, 
		 PROCESS_DEMAND_ID  VARCHAR   NOT NULL, 
		 PROCESS_DEMAND_SUB_ID  VARCHAR   NOT NULL, 
		 PART_ID  VARCHAR, 
		 PROD_NAME_CODE  VARCHAR, 
		 PROD_CNAME  VARCHAR, 
		 SPECS_DESC  VARCHAR, 
		 SHOPSIGN  VARCHAR, 
		 PRODUCT_PROCESS_QTY  DECIMAL, 
		 PRODUCT_PROCESS_WEIGHT  DECIMAL, 
		 CUSTOMER_ID  VARCHAR, 
		 CUSTOMER_NAME  VARCHAR, 
		 ORDER_NUM  VARCHAR, 
		 PRODUCT_DEMAND_ID  VARCHAR, 
		 LACK_DESTINATION_ID  VARCHAR, 
		 PROCESS_DEMAND_OUTPUT_STATUS  VARCHAR, 
		 REMARK  VARCHAR, 
		 CONTRACT_NUM  VARCHAR, 
		 STOCK_USE_WEIGHT  DECIMAL, 
		 STOCK_USE_QTY  VARCHAR, 
		 PROCESS_USE_WEIGHT  DECIMAL, 
		 PROCESS_USE_QTY  VARCHAR, 
		 PRE_DEMAND_USE_QTY  VARCHAR, 
		 PRE_DEMAND_USE_WEIGHT  DECIMAL, 
		 QUANTITY_UNIT  VARCHAR, 
		 WEIGHT_UNIT  VARCHAR, 
		 P_OUTPUT  DECIMAL, 
		 SUIT_VOUCHER_NUM  VARCHAR, 
		 PRODUCT_REMAIN_QTY  VARCHAR, 
		 PRODUCT_REMAIN_WEIGHT  DECIMAL, 
		 INDM  DECIMAL, 
		 PACKING_TYPE_CODE  VARCHAR, 
		 SALES_PERS_ID  VARCHAR, 
		 SALES_PERS_NAME  VARCHAR, 
		 PROCESS_DEMAND_QTY  VARCHAR, 
		 PROCESS_DEMAND_WEIGHT  DECIMAL, 
		 MPROVIDER_ID  VARCHAR, 
		 MPROVIDER_NAME  VARCHAR, 
		 AGREEMENT_ID  VARCHAR, 
		 AGREEMENT_SUBID  VARCHAR, 
		 PROCESS_FEE_PRICE_TYPE  VARCHAR, 
		 PROCESS_FEE_PRICE  DECIMAL, 
		 PROCESS_FEE_PRICE_TAX  DECIMAL, 
		 TAX_RATE  DECIMAL, 
		 DIAMETER  DECIMAL, 
		 LABEL_FORM  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 TENANT_USER  VARCHAR, 
		 CUSTOMER_MATERIAL_NUMBER  VARCHAR, 
		 PROCESS_DEMAND_OUTPUT_ID  VARCHAR   NOT NULL, 
		 SURFACE_GRADE  VARCHAR, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 PROD_CODE  VARCHAR, 
		 PROCESS_DEMAND_INPUT_ID  VARCHAR, 
		 D_USER_NUM  VARCHAR, 
		 D_USER_NAME  VARCHAR, 
		 PRODUCTION_PROCESSED_QUANTITY  DECIMAL, 
		 PRODUCTION_PROCESSED_WEIGHT  DECIMAL, 
		 PROCESS_SINGLE_PACK_QUANTITY  DECIMAL, 
		 PROCESS_SINGLE_PACK_WEIGHT  DECIMAL, 
		 ESTIMATED_PACKAGES_NUMBER  VARCHAR, 
		 PROCESS_BASE_VERSION_NUM  DECIMAL, 
		 WEIGHT_METHOD  VARCHAR, 
		 TECH_STANDARD  VARCHAR, 
		 BSTS_COILING_ORD  VARCHAR, 
		 QUALITY_GRADE  VARCHAR, 
		 FINAL_FM_SPEC  VARCHAR, 
		 PROCESS_BASE_NO  VARCHAR, 
		 PROD_TYPE_ID  VARCHAR, 
		 ORDER_TYPE_CODE  VARCHAR, 
		 INNER_DIM  DECIMAL, 
		 DIR_PROCESS_DEMAND_ID  VARCHAR, 
		 DIR_PROCESS_DEMAND_SUB_ID  VARCHAR, 
		 PRE_PROCESS_DEMAND_OUTPUT_SEQ_ID  VARCHAR, 
		 PROD_TYPE_DESC  VARCHAR, 
		 PROCESS_IF_SETTLE  VARCHAR, 
		 PROCESS_FEE_CALCULATE_METHOD  VARCHAR, 
		 EXCESS_STOCK_FLAG  VARCHAR, 
		 FINISHING_SHUNT_FLAG  VARCHAR, 
		 CONTRACT_PART_ID  VARCHAR, 
		 METAL_BALANCE_FLAG  VARCHAR, 
		 MANUAL_NO  VARCHAR, 
		 HS_ID  VARCHAR, 
		 PROCESS_CONSIGN_UNIT  VARCHAR, 
		 PRINT_OUTPUT_REMARK  VARCHAR, 
		 FIN_USER_NUM  VARCHAR, 
		 FIN_USER_NAME  VARCHAR, 
		 HANDBOOK_ID  VARCHAR, 
		 CUSTOMS_PRODUCT_NUM  VARCHAR, 
		 F_PACK_ID  VARCHAR, 
		 F_MAT_INNER_ID  VARCHAR, 
		 NODE_CODE  VARCHAR, 
		 DEMAND_REC_NO  VARCHAR, 
		 DEMAND_REC_VERSION_NUM  DECIMAL, 
		 CRAFT_CODE  VARCHAR, 
		 CRAFT_VERSION_NUM  DECIMAL, 
		 PROCESS_SEQ_CODE  VARCHAR, 
		 TAGEND_MGR_TYPE  VARCHAR, 
		 TRADE_CODE  VARCHAR, 
		 CUST_PART_ID  VARCHAR, 
		 CUST_PART_NAME  VARCHAR, 
		 BRACKET_TYPE  VARCHAR, 
		 IRON_BRACKET_NO  VARCHAR, 
		 MAX_COIL_WT  DECIMAL   NOT NULL, 
		 IS_RETURN_MATERIAL_AREA  VARCHAR, 
		 PRINT_BATCH_ID  VARCHAR
	-->
<sqlMap namespace="VIPM0007">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.vi.pm.domain.VIPM0007">
		SELECT
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				PROCESS_ORDER_ID	as "processOrderId",  <!-- 生产工单号 -->
				PROCESS_DEMAND_ID	as "processDemandId",  <!-- 生产需求单号 -->
				PROCESS_DEMAND_SUB_ID	as "processDemandSubId",  <!-- 生产需求单子项号 -->
				PART_ID	as "partId",  <!-- 物料号 -->
				PROD_NAME_CODE	as "prodNameCode",  <!-- 品名代码 -->
				PROD_CNAME	as "prodCname",  <!-- 品名（中文） -->
				SPECS_DESC	as "specsDesc",  <!-- 规格描述 -->
				SHOPSIGN	as "shopsign",  <!-- 牌号 -->
				PRODUCT_PROCESS_QTY	as "productProcessQty",  <!-- 产出成品张数 -->
				PRODUCT_PROCESS_WEIGHT	as "productProcessWeight",  <!-- 产出成品重量 -->
				CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
				CUSTOMER_NAME	as "customerName",  <!-- 客户名称 -->
				ORDER_NUM	as "orderNum",  <!-- 销售订单子项号 -->
				PRODUCT_DEMAND_ID	as "productDemandId",  <!-- 成品需求单号 -->
				LACK_DESTINATION_ID	as "lackDestinationId",  <!-- 锁定（去向）单据号 -->
				PROCESS_DEMAND_OUTPUT_STATUS	as "processDemandOutputStatus",  <!-- 生产需求产出表状态 -->
				REMARK	as "remark",  <!-- 备注 -->
				CONTRACT_NUM	as "contractNum",  <!-- 销售订单号 -->
				STOCK_USE_WEIGHT	as "stockUseWeight",  <!-- 自由在库库存占用重量 -->
				STOCK_USE_QTY	as "stockUseQty",  <!-- 自由在库库存占用数量 -->
				PROCESS_USE_WEIGHT	as "processUseWeight",  <!-- 在制占用重量 -->
				PROCESS_USE_QTY	as "processUseQty",  <!-- 在制占用数量 -->
				PRE_DEMAND_USE_QTY	as "preDemandUseQty",  <!-- 上道需求占用数量 -->
				PRE_DEMAND_USE_WEIGHT	as "preDemandUseWeight",  <!-- 上道需求占用重量 -->
				QUANTITY_UNIT	as "quantityUnit",  <!-- 数量单位 -->
				WEIGHT_UNIT	as "weightUnit",  <!-- 重量单位 -->
				P_OUTPUT	as "p_output",  <!-- 成品配比 -->
				SUIT_VOUCHER_NUM	as "suitVoucherNum",  <!-- 套裁封锁单据号 -->
				PRODUCT_REMAIN_QTY	as "productRemainQty",  <!-- 加工剩余数量 -->
				PRODUCT_REMAIN_WEIGHT	as "productRemainWeight",  <!-- 加工剩余重量 -->
				INDM	as "indm",  <!-- 内径 -->
				PACKING_TYPE_CODE	as "packingTypeCode",  <!-- 包装方式代码 -->
				SALES_PERS_ID	as "salesPersId",  <!-- 营销员工号 -->
				SALES_PERS_NAME	as "salesPersName",  <!-- 营销员姓名 -->
				PROCESS_DEMAND_QTY	as "processDemandQty",  <!-- 生产需求数量 -->
				PROCESS_DEMAND_WEIGHT	as "processDemandWeight",  <!-- 生产需求重量 -->
				MPROVIDER_ID	as "mproviderId",  <!-- 加工单位代码 -->
				MPROVIDER_NAME	as "mproviderName",  <!-- 加工单位名称 -->
				AGREEMENT_ID	as "agreementId",  <!-- 加工协议号 -->
				AGREEMENT_SUBID	as "agreementSubid",  <!-- 加工协议子项号 -->
				PROCESS_FEE_PRICE_TYPE	as "processFeePriceType",  <!-- 加工费计价类型 -->
				PROCESS_FEE_PRICE	as "processFeePrice",  <!-- 加工费单价(不含税) -->
				PROCESS_FEE_PRICE_TAX	as "processFeePriceTax",  <!-- 加工费单价(含税) -->
				TAX_RATE	as "taxRate",  <!-- 税率 -->
				DIAMETER	as "diameter",  <!-- 外径 -->
				LABEL_FORM	as "labelForm",  <!-- 标签格式 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				CUSTOMER_MATERIAL_NUMBER	as "customerMaterialNumber",  <!-- 客户材料代码 -->
				PROCESS_DEMAND_OUTPUT_ID	as "processDemandOutputId",  <!-- 生产需求单产出表序列号 -->
				SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
				SEG_NO	as "segNo",  <!-- 业务账套 -->
				UUID	as "uuid",  <!-- ID -->
				PROD_CODE	as "prodCode",  <!-- 品种代码 -->
				PROCESS_DEMAND_INPUT_ID	as "processDemandInputId",  <!-- 投料单据号 -->
				D_USER_NUM	as "d_userNum",  <!-- 分户号 -->
				D_USER_NAME	as "d_userName",  <!-- 分户号名称 -->
				PRODUCTION_PROCESSED_QUANTITY	as "productionProcessedQuantity",  <!-- 生产已加工数量 -->
				PRODUCTION_PROCESSED_WEIGHT	as "productionProcessedWeight",  <!-- 生产已加工重量 -->
				PROCESS_SINGLE_PACK_QUANTITY	as "processSinglePackQuantity",  <!-- 生产单包数量 -->
				PROCESS_SINGLE_PACK_WEIGHT	as "processSinglePackWeight",  <!-- 生产单包重量 -->
				ESTIMATED_PACKAGES_NUMBER	as "estimatedPackagesNumber",  <!-- 预计包数 -->
				PROCESS_BASE_VERSION_NUM	as "processBaseVersionNum",  <!-- 加工基准书版本号 -->
				WEIGHT_METHOD	as "weightMethod",  <!-- 计重方式 -->
				TECH_STANDARD	as "techStandard",  <!-- 技术标准 -->
				BSTS_COILING_ORD	as "bstsCoilingOrd",  <!-- 好面朝向 -->
				QUALITY_GRADE	as "qualityGrade",  <!-- 质量等级 -->
				FINAL_FM_SPEC	as "finalFmSpec",  <!-- 最终成品规格 -->
				PROCESS_BASE_NO	as "processBaseNo",  <!-- 加工基准书号 -->
				PROD_TYPE_ID	as "prodTypeId",  <!-- 品种附属码 -->
				ORDER_TYPE_CODE	as "orderTypeCode",  <!-- 订单性质代码 -->
				INNER_DIM	as "innerDim",  <!-- 内径(mm) -->
				DIR_PROCESS_DEMAND_ID	as "dirProcessDemandId",  <!-- 去向生产需求单号 -->
				DIR_PROCESS_DEMAND_SUB_ID	as "dirProcessDemandSubId",  <!-- 去向生产需求单子项 -->
				PRE_PROCESS_DEMAND_OUTPUT_SEQ_ID	as "preProcessDemandOutputSeqId",  <!-- 上道产出表序列号 -->
				PROD_TYPE_DESC	as "prodTypeDesc",  <!-- 品种附属码描述 -->
				PROCESS_IF_SETTLE	as "processIfSettle",  <!-- 生产加工是否结算加工费 -->
				PROCESS_FEE_CALCULATE_METHOD	as "processFeeCalculateMethod",  <!-- 加工费计算方式 -->
				EXCESS_STOCK_FLAG	as "excessStockFlag",  <!-- 余料标记 -->
				FINISHING_SHUNT_FLAG	as "finishingShuntFlag",  <!-- 精整分流标记 -->
				CONTRACT_PART_ID	as "contractPartId",  <!-- 订单物料号 -->
				METAL_BALANCE_FLAG	as "metalBalanceFlag",  <!-- 金属平衡标志 -->
				MANUAL_NO	as "manualNo",  <!-- 手册编号 -->
				HS_ID	as "hsId",  <!-- 海关HS系统编码 -->
				PROCESS_CONSIGN_UNIT	as "processConsignUnit",  <!-- 加工委托方 -->
				PRINT_OUTPUT_REMARK	as "printOutputRemark",  <!-- 打印产出物料信息备注 -->
				FIN_USER_NUM	as "finUserNum",  <!-- 最终用户代码（订单用） -->
				FIN_USER_NAME	as "finUserName",  <!-- 最终用户名称 -->
				HANDBOOK_ID	as "handbookId",  <!-- 手册系统编号 -->
				CUSTOMS_PRODUCT_NUM	as "customsProductNum",  
				F_PACK_ID	as "f_packId",  <!-- 父捆包号 -->
				F_MAT_INNER_ID	as "f_matInnerId",  <!-- 父捆包管理号 -->
				NODE_CODE	as "nodeCode",  <!-- 节点号 -->
				DEMAND_REC_NO	as "demandRecNo",  <!-- 需求识别卡号 -->
				DEMAND_REC_VERSION_NUM	as "demandRecVersionNum",  <!-- 需求识别卡版本号 -->
				CRAFT_CODE	as "craftCode",  <!-- 工艺单号 -->
				CRAFT_VERSION_NUM	as "craftVersionNum",  <!-- 工艺单版本号 -->
				PROCESS_SEQ_CODE	as "processSeqCode",  <!-- 加工序码 -->
				TAGEND_MGR_TYPE	as "tagendMgrType",  <!-- 尾料处理方式 -->
				TRADE_CODE	as "tradeCode",  <!-- 贸易方式 -->
				CUST_PART_ID	as "custPartId",  <!-- 客户零件号 -->
				CUST_PART_NAME	as "custPartName",  <!-- 客户零件号名称 -->
				BRACKET_TYPE	as "bracketType",  <!-- 料架类型 -->
				IRON_BRACKET_NO	as "ironBracketNo",  <!-- 料架编号 -->
				MAX_COIL_WT	as "maxCoilWt",  <!-- 最大卷重 -->
				IS_RETURN_MATERIAL_AREA	as "isReturnMaterialArea",  <!-- 是否退回原料库 -->
				PRINT_BATCH_ID	as "printBatchId" <!-- 打印批次号 -->
		FROM ${meviSchema}.TVIPM0007 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="partId">
			PART_ID = #partId#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meviSchema}.TVIPM0007 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandId">
			PROCESS_DEMAND_ID = #processDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandSubId">
			PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="partId">
			PART_ID = #partId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodNameCode">
			PROD_NAME_CODE = #prodNameCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodCname">
			PROD_CNAME = #prodCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specsDesc">
			SPECS_DESC = #specsDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="shopsign">
			SHOPSIGN = #shopsign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productProcessQty">
			PRODUCT_PROCESS_QTY = #productProcessQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productProcessWeight">
			PRODUCT_PROCESS_WEIGHT = #productProcessWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orderNum">
			ORDER_NUM = #orderNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productDemandId">
			PRODUCT_DEMAND_ID = #productDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="lackDestinationId">
			LACK_DESTINATION_ID = #lackDestinationId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandOutputStatus">
			PROCESS_DEMAND_OUTPUT_STATUS = #processDemandOutputStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="contractNum">
			CONTRACT_NUM = #contractNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="stockUseWeight">
			STOCK_USE_WEIGHT = #stockUseWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="stockUseQty">
			STOCK_USE_QTY = #stockUseQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processUseWeight">
			PROCESS_USE_WEIGHT = #processUseWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processUseQty">
			PROCESS_USE_QTY = #processUseQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="preDemandUseQty">
			PRE_DEMAND_USE_QTY = #preDemandUseQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="preDemandUseWeight">
			PRE_DEMAND_USE_WEIGHT = #preDemandUseWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="quantityUnit">
			QUANTITY_UNIT = #quantityUnit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="weightUnit">
			WEIGHT_UNIT = #weightUnit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="p_output">
			P_OUTPUT = #p_output#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="suitVoucherNum">
			SUIT_VOUCHER_NUM = #suitVoucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productRemainQty">
			PRODUCT_REMAIN_QTY = #productRemainQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productRemainWeight">
			PRODUCT_REMAIN_WEIGHT = #productRemainWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="indm">
			INDM = #indm#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packingTypeCode">
			PACKING_TYPE_CODE = #packingTypeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="salesPersId">
			SALES_PERS_ID = #salesPersId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="salesPersName">
			SALES_PERS_NAME = #salesPersName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandQty">
			PROCESS_DEMAND_QTY = #processDemandQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandWeight">
			PROCESS_DEMAND_WEIGHT = #processDemandWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mproviderId">
			MPROVIDER_ID = #mproviderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mproviderName">
			MPROVIDER_NAME = #mproviderName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="agreementId">
			AGREEMENT_ID = #agreementId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="agreementSubid">
			AGREEMENT_SUBID = #agreementSubid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeePriceType">
			PROCESS_FEE_PRICE_TYPE = #processFeePriceType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeePrice">
			PROCESS_FEE_PRICE = #processFeePrice#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeePriceTax">
			PROCESS_FEE_PRICE_TAX = #processFeePriceTax#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="taxRate">
			TAX_RATE = #taxRate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="diameter">
			DIAMETER = #diameter#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="labelForm">
			LABEL_FORM = #labelForm#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerMaterialNumber">
			CUSTOMER_MATERIAL_NUMBER = #customerMaterialNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandOutputId">
			PROCESS_DEMAND_OUTPUT_ID = #processDemandOutputId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="surfaceGrade">
			SURFACE_GRADE = #surfaceGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodCode">
			PROD_CODE = #prodCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandInputId">
			PROCESS_DEMAND_INPUT_ID = #processDemandInputId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="d_userNum">
			D_USER_NUM = #d_userNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="d_userName">
			D_USER_NAME = #d_userName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productionProcessedQuantity">
			PRODUCTION_PROCESSED_QUANTITY = #productionProcessedQuantity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productionProcessedWeight">
			PRODUCTION_PROCESSED_WEIGHT = #productionProcessedWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSinglePackQuantity">
			PROCESS_SINGLE_PACK_QUANTITY = #processSinglePackQuantity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSinglePackWeight">
			PROCESS_SINGLE_PACK_WEIGHT = #processSinglePackWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="estimatedPackagesNumber">
			ESTIMATED_PACKAGES_NUMBER = #estimatedPackagesNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processBaseVersionNum">
			PROCESS_BASE_VERSION_NUM = #processBaseVersionNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="weightMethod">
			WEIGHT_METHOD = #weightMethod#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="techStandard">
			TECH_STANDARD = #techStandard#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="bstsCoilingOrd">
			BSTS_COILING_ORD = #bstsCoilingOrd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="qualityGrade">
			QUALITY_GRADE = #qualityGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finalFmSpec">
			FINAL_FM_SPEC = #finalFmSpec#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processBaseNo">
			PROCESS_BASE_NO = #processBaseNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeId">
			PROD_TYPE_ID = #prodTypeId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orderTypeCode">
			ORDER_TYPE_CODE = #orderTypeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="innerDim">
			INNER_DIM = #innerDim#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dirProcessDemandId">
			DIR_PROCESS_DEMAND_ID = #dirProcessDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dirProcessDemandSubId">
			DIR_PROCESS_DEMAND_SUB_ID = #dirProcessDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="preProcessDemandOutputSeqId">
			PRE_PROCESS_DEMAND_OUTPUT_SEQ_ID = #preProcessDemandOutputSeqId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeDesc">
			PROD_TYPE_DESC = #prodTypeDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processIfSettle">
			PROCESS_IF_SETTLE = #processIfSettle#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeeCalculateMethod">
			PROCESS_FEE_CALCULATE_METHOD = #processFeeCalculateMethod#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="excessStockFlag">
			EXCESS_STOCK_FLAG = #excessStockFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finishingShuntFlag">
			FINISHING_SHUNT_FLAG = #finishingShuntFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="contractPartId">
			CONTRACT_PART_ID = #contractPartId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="metalBalanceFlag">
			METAL_BALANCE_FLAG = #metalBalanceFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="manualNo">
			MANUAL_NO = #manualNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hsId">
			HS_ID = #hsId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processConsignUnit">
			PROCESS_CONSIGN_UNIT = #processConsignUnit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printOutputRemark">
			PRINT_OUTPUT_REMARK = #printOutputRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserNum">
			FIN_USER_NUM = #finUserNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserName">
			FIN_USER_NAME = #finUserName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handbookId">
			HANDBOOK_ID = #handbookId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customsProductNum">
			CUSTOMS_PRODUCT_NUM = #customsProductNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_packId">
			F_PACK_ID = #f_packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_matInnerId">
			F_MAT_INNER_ID = #f_matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="nodeCode">
			NODE_CODE = #nodeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="demandRecNo">
			DEMAND_REC_NO = #demandRecNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="demandRecVersionNum">
			DEMAND_REC_VERSION_NUM = #demandRecVersionNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craftCode">
			CRAFT_CODE = #craftCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craftVersionNum">
			CRAFT_VERSION_NUM = #craftVersionNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSeqCode">
			PROCESS_SEQ_CODE = #processSeqCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tagendMgrType">
			TAGEND_MGR_TYPE = #tagendMgrType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tradeCode">
			TRADE_CODE = #tradeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="custPartId">
			CUST_PART_ID = #custPartId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="custPartName">
			CUST_PART_NAME = #custPartName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="bracketType">
			BRACKET_TYPE = #bracketType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ironBracketNo">
			IRON_BRACKET_NO = #ironBracketNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="maxCoilWt">
			MAX_COIL_WT = #maxCoilWt#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="isReturnMaterialArea">
			IS_RETURN_MATERIAL_AREA = #isReturnMaterialArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printBatchId">
			PRINT_BATCH_ID = #printBatchId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meviSchema}.TVIPM0007 (UNIT_CODE,  <!-- 业务单元代码 -->
										PROCESS_ORDER_ID,  <!-- 生产工单号 -->
										PROCESS_DEMAND_ID,  <!-- 生产需求单号 -->
										PROCESS_DEMAND_SUB_ID,  <!-- 生产需求单子项号 -->
										PART_ID,  <!-- 物料号 -->
										PROD_NAME_CODE,  <!-- 品名代码 -->
										PROD_CNAME,  <!-- 品名（中文） -->
										SPECS_DESC,  <!-- 规格描述 -->
										SHOPSIGN,  <!-- 牌号 -->
										PRODUCT_PROCESS_QTY,  <!-- 产出成品张数 -->
										PRODUCT_PROCESS_WEIGHT,  <!-- 产出成品重量 -->
										CUSTOMER_ID,  <!-- 客户代码 -->
										CUSTOMER_NAME,  <!-- 客户名称 -->
										ORDER_NUM,  <!-- 销售订单子项号 -->
										PRODUCT_DEMAND_ID,  <!-- 成品需求单号 -->
										LACK_DESTINATION_ID,  <!-- 锁定（去向）单据号 -->
										PROCESS_DEMAND_OUTPUT_STATUS,  <!-- 生产需求产出表状态 -->
										REMARK,  <!-- 备注 -->
										CONTRACT_NUM,  <!-- 销售订单号 -->
										STOCK_USE_WEIGHT,  <!-- 自由在库库存占用重量 -->
										STOCK_USE_QTY,  <!-- 自由在库库存占用数量 -->
										PROCESS_USE_WEIGHT,  <!-- 在制占用重量 -->
										PROCESS_USE_QTY,  <!-- 在制占用数量 -->
										PRE_DEMAND_USE_QTY,  <!-- 上道需求占用数量 -->
										PRE_DEMAND_USE_WEIGHT,  <!-- 上道需求占用重量 -->
										QUANTITY_UNIT,  <!-- 数量单位 -->
										WEIGHT_UNIT,  <!-- 重量单位 -->
										P_OUTPUT,  <!-- 成品配比 -->
										SUIT_VOUCHER_NUM,  <!-- 套裁封锁单据号 -->
										PRODUCT_REMAIN_QTY,  <!-- 加工剩余数量 -->
										PRODUCT_REMAIN_WEIGHT,  <!-- 加工剩余重量 -->
										INDM,  <!-- 内径 -->
										PACKING_TYPE_CODE,  <!-- 包装方式代码 -->
										SALES_PERS_ID,  <!-- 营销员工号 -->
										SALES_PERS_NAME,  <!-- 营销员姓名 -->
										PROCESS_DEMAND_QTY,  <!-- 生产需求数量 -->
										PROCESS_DEMAND_WEIGHT,  <!-- 生产需求重量 -->
										MPROVIDER_ID,  <!-- 加工单位代码 -->
										MPROVIDER_NAME,  <!-- 加工单位名称 -->
										AGREEMENT_ID,  <!-- 加工协议号 -->
										AGREEMENT_SUBID,  <!-- 加工协议子项号 -->
										PROCESS_FEE_PRICE_TYPE,  <!-- 加工费计价类型 -->
										PROCESS_FEE_PRICE,  <!-- 加工费单价(不含税) -->
										PROCESS_FEE_PRICE_TAX,  <!-- 加工费单价(含税) -->
										TAX_RATE,  <!-- 税率 -->
										DIAMETER,  <!-- 外径 -->
										LABEL_FORM,  <!-- 标签格式 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										TENANT_USER,  <!-- 租户 -->
										CUSTOMER_MATERIAL_NUMBER,  <!-- 客户材料代码 -->
										PROCESS_DEMAND_OUTPUT_ID,  <!-- 生产需求单产出表序列号 -->
										SURFACE_GRADE,  <!-- 表面等级 -->
										SEG_NO,  <!-- 业务账套 -->
										UUID,  <!-- ID -->
										PROD_CODE,  <!-- 品种代码 -->
										PROCESS_DEMAND_INPUT_ID,  <!-- 投料单据号 -->
										D_USER_NUM,  <!-- 分户号 -->
										D_USER_NAME,  <!-- 分户号名称 -->
										PRODUCTION_PROCESSED_QUANTITY,  <!-- 生产已加工数量 -->
										PRODUCTION_PROCESSED_WEIGHT,  <!-- 生产已加工重量 -->
										PROCESS_SINGLE_PACK_QUANTITY,  <!-- 生产单包数量 -->
										PROCESS_SINGLE_PACK_WEIGHT,  <!-- 生产单包重量 -->
										ESTIMATED_PACKAGES_NUMBER,  <!-- 预计包数 -->
										PROCESS_BASE_VERSION_NUM,  <!-- 加工基准书版本号 -->
										WEIGHT_METHOD,  <!-- 计重方式 -->
										TECH_STANDARD,  <!-- 技术标准 -->
										BSTS_COILING_ORD,  <!-- 好面朝向 -->
										QUALITY_GRADE,  <!-- 质量等级 -->
										FINAL_FM_SPEC,  <!-- 最终成品规格 -->
										PROCESS_BASE_NO,  <!-- 加工基准书号 -->
										PROD_TYPE_ID,  <!-- 品种附属码 -->
										ORDER_TYPE_CODE,  <!-- 订单性质代码 -->
										INNER_DIM,  <!-- 内径(mm) -->
										DIR_PROCESS_DEMAND_ID,  <!-- 去向生产需求单号 -->
										DIR_PROCESS_DEMAND_SUB_ID,  <!-- 去向生产需求单子项 -->
										PRE_PROCESS_DEMAND_OUTPUT_SEQ_ID,  <!-- 上道产出表序列号 -->
										PROD_TYPE_DESC,  <!-- 品种附属码描述 -->
										PROCESS_IF_SETTLE,  <!-- 生产加工是否结算加工费 -->
										PROCESS_FEE_CALCULATE_METHOD,  <!-- 加工费计算方式 -->
										EXCESS_STOCK_FLAG,  <!-- 余料标记 -->
										FINISHING_SHUNT_FLAG,  <!-- 精整分流标记 -->
										CONTRACT_PART_ID,  <!-- 订单物料号 -->
										METAL_BALANCE_FLAG,  <!-- 金属平衡标志 -->
										MANUAL_NO,  <!-- 手册编号 -->
										HS_ID,  <!-- 海关HS系统编码 -->
										PROCESS_CONSIGN_UNIT,  <!-- 加工委托方 -->
										PRINT_OUTPUT_REMARK,  <!-- 打印产出物料信息备注 -->
										FIN_USER_NUM,  <!-- 最终用户代码（订单用） -->
										FIN_USER_NAME,  <!-- 最终用户名称 -->
										HANDBOOK_ID,  <!-- 手册系统编号 -->
										CUSTOMS_PRODUCT_NUM,
										F_PACK_ID,  <!-- 父捆包号 -->
										F_MAT_INNER_ID,  <!-- 父捆包管理号 -->
										NODE_CODE,  <!-- 节点号 -->
										DEMAND_REC_NO,  <!-- 需求识别卡号 -->
										DEMAND_REC_VERSION_NUM,  <!-- 需求识别卡版本号 -->
										CRAFT_CODE,  <!-- 工艺单号 -->
										CRAFT_VERSION_NUM,  <!-- 工艺单版本号 -->
										PROCESS_SEQ_CODE,  <!-- 加工序码 -->
										TAGEND_MGR_TYPE,  <!-- 尾料处理方式 -->
										TRADE_CODE,  <!-- 贸易方式 -->
										CUST_PART_ID,  <!-- 客户零件号 -->
										CUST_PART_NAME,  <!-- 客户零件号名称 -->
										BRACKET_TYPE,  <!-- 料架类型 -->
										IRON_BRACKET_NO,  <!-- 料架编号 -->
										MAX_COIL_WT,  <!-- 最大卷重 -->
										IS_RETURN_MATERIAL_AREA,  <!-- 是否退回原料库 -->
										PRINT_BATCH_ID  <!-- 打印批次号 -->
										)		 
	    VALUES (#unitCode:VARCHAR#, #processOrderId:VARCHAR#, #processDemandId:VARCHAR#, #processDemandSubId:VARCHAR#, #partId:VARCHAR#, #prodNameCode:VARCHAR#, #prodCname:VARCHAR#, #specsDesc:VARCHAR#, #shopsign:VARCHAR#, #productProcessQty:NUMERIC#, #productProcessWeight:NUMERIC#, #customerId:VARCHAR#, #customerName:VARCHAR#, #orderNum:VARCHAR#, #productDemandId:VARCHAR#, #lackDestinationId:VARCHAR#, #processDemandOutputStatus:VARCHAR#, #remark:VARCHAR#, #contractNum:VARCHAR#, #stockUseWeight:NUMERIC#, #stockUseQty:VARCHAR#, #processUseWeight:NUMERIC#, #processUseQty:VARCHAR#, #preDemandUseQty:VARCHAR#, #preDemandUseWeight:NUMERIC#, #quantityUnit:VARCHAR#, #weightUnit:VARCHAR#, #p_output:NUMERIC#, #suitVoucherNum:VARCHAR#, #productRemainQty:VARCHAR#, #productRemainWeight:NUMERIC#, #indm:NUMERIC#, #packingTypeCode:VARCHAR#, #salesPersId:VARCHAR#, #salesPersName:VARCHAR#, #processDemandQty:VARCHAR#, #processDemandWeight:NUMERIC#, #mproviderId:VARCHAR#, #mproviderName:VARCHAR#, #agreementId:VARCHAR#, #agreementSubid:VARCHAR#, #processFeePriceType:VARCHAR#, #processFeePrice:NUMERIC#, #processFeePriceTax:NUMERIC#, #taxRate:NUMERIC#, #diameter:NUMERIC#, #labelForm:VARCHAR#, #recCreator:VARCHAR#, #recCreatorName:VARCHAR#, #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recRevisorName:VARCHAR#, #recReviseTime:VARCHAR#, #archiveFlag:VARCHAR#, #delFlag#, #tenantUser:VARCHAR#, #customerMaterialNumber:VARCHAR#, #processDemandOutputId:VARCHAR#, #surfaceGrade:VARCHAR#, #segNo:VARCHAR#, #uuid:VARCHAR#, #prodCode:VARCHAR#, #processDemandInputId:VARCHAR#, #d_userNum:VARCHAR#, #d_userName:VARCHAR#, #productionProcessedQuantity:NUMERIC#, #productionProcessedWeight:NUMERIC#, #processSinglePackQuantity:NUMERIC#, #processSinglePackWeight:NUMERIC#, #estimatedPackagesNumber:VARCHAR#, #processBaseVersionNum:NUMERIC#, #weightMethod:VARCHAR#, #techStandard:VARCHAR#, #bstsCoilingOrd:VARCHAR#, #qualityGrade:VARCHAR#, #finalFmSpec:VARCHAR#, #processBaseNo:VARCHAR#, #prodTypeId:VARCHAR#, #orderTypeCode:VARCHAR#, #innerDim:NUMERIC#, #dirProcessDemandId:VARCHAR#, #dirProcessDemandSubId:VARCHAR#, #preProcessDemandOutputSeqId:VARCHAR#, #prodTypeDesc:VARCHAR#, #processIfSettle:VARCHAR#, #processFeeCalculateMethod:VARCHAR#, #excessStockFlag:VARCHAR#, #finishingShuntFlag:VARCHAR#, #contractPartId:VARCHAR#, #metalBalanceFlag:VARCHAR#, #manualNo:VARCHAR#, #hsId:VARCHAR#, #processConsignUnit:VARCHAR#, #printOutputRemark:VARCHAR#, #finUserNum:VARCHAR#, #finUserName:VARCHAR#, #handbookId:VARCHAR#, #customsProductNum:VARCHAR#, #f_packId:VARCHAR#, #f_matInnerId:VARCHAR#, #nodeCode:VARCHAR#, #demandRecNo:VARCHAR#, #demandRecVersionNum:NUMERIC#, #craftCode:VARCHAR#, #craftVersionNum:NUMERIC#, #processSeqCode:VARCHAR#, #tagendMgrType:VARCHAR#, #tradeCode:VARCHAR#, #custPartId:VARCHAR#, #custPartName:VARCHAR#, #bracketType:VARCHAR#, #ironBracketNo:VARCHAR#, #maxCoilWt:NUMERIC#, #isReturnMaterialArea:VARCHAR#, #printBatchId:VARCHAR#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meviSchema}.TVIPM0007
		WHERE
		SEG_NO = #segNo#
		AND PROCESS_ORDER_ID = #processOrderId#
		AND PROCESS_DEMAND_SUB_ID = #processDemandSubId#
	</delete>

	<update id="update">
		UPDATE ${meviSchema}.TVIPM0007
		SET 
		UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					PROCESS_ORDER_ID	= #processOrderId#,   <!-- 生产工单号 -->
					PROCESS_DEMAND_ID	= #processDemandId#,   <!-- 生产需求单号 -->
					PROCESS_DEMAND_SUB_ID	= #processDemandSubId#,   <!-- 生产需求单子项号 -->
					PART_ID	= #partId#,   <!-- 物料号 -->
					PROD_NAME_CODE	= #prodNameCode#,   <!-- 品名代码 -->
					PROD_CNAME	= #prodCname#,   <!-- 品名（中文） -->
					SPECS_DESC	= #specsDesc#,   <!-- 规格描述 -->
					SHOPSIGN	= #shopsign#,   <!-- 牌号 -->
					PRODUCT_PROCESS_QTY	= #productProcessQty:NUMERIC#,   <!-- 产出成品张数 -->
					PRODUCT_PROCESS_WEIGHT	= #productProcessWeight:NUMERIC#,   <!-- 产出成品重量 -->
					CUSTOMER_ID	= #customerId#,   <!-- 客户代码 -->
					CUSTOMER_NAME	= #customerName#,   <!-- 客户名称 -->
					ORDER_NUM	= #orderNum#,   <!-- 销售订单子项号 -->
					PRODUCT_DEMAND_ID	= #productDemandId#,   <!-- 成品需求单号 -->
					LACK_DESTINATION_ID	= #lackDestinationId#,   <!-- 锁定（去向）单据号 -->
					PROCESS_DEMAND_OUTPUT_STATUS	= #processDemandOutputStatus#,   <!-- 生产需求产出表状态 -->
					REMARK	= #remark#,   <!-- 备注 -->
					CONTRACT_NUM	= #contractNum#,   <!-- 销售订单号 -->
					STOCK_USE_WEIGHT	= #stockUseWeight:NUMERIC#,   <!-- 自由在库库存占用重量 -->
					STOCK_USE_QTY	= #stockUseQty#,   <!-- 自由在库库存占用数量 -->
					PROCESS_USE_WEIGHT	= #processUseWeight:NUMERIC#,   <!-- 在制占用重量 -->
					PROCESS_USE_QTY	= #processUseQty#,   <!-- 在制占用数量 -->
					PRE_DEMAND_USE_QTY	= #preDemandUseQty#,   <!-- 上道需求占用数量 -->
					PRE_DEMAND_USE_WEIGHT	= #preDemandUseWeight:NUMERIC#,   <!-- 上道需求占用重量 -->
					QUANTITY_UNIT	= #quantityUnit#,   <!-- 数量单位 -->
					WEIGHT_UNIT	= #weightUnit#,   <!-- 重量单位 -->
					P_OUTPUT	= #p_output:NUMERIC#,   <!-- 成品配比 -->
					SUIT_VOUCHER_NUM	= #suitVoucherNum#,   <!-- 套裁封锁单据号 -->
					PRODUCT_REMAIN_QTY	= #productRemainQty#,   <!-- 加工剩余数量 -->
					PRODUCT_REMAIN_WEIGHT	= #productRemainWeight:NUMERIC#,   <!-- 加工剩余重量 -->
					INDM	= #indm:NUMERIC#,   <!-- 内径 -->
					PACKING_TYPE_CODE	= #packingTypeCode#,   <!-- 包装方式代码 -->
					SALES_PERS_ID	= #salesPersId#,   <!-- 营销员工号 -->
					SALES_PERS_NAME	= #salesPersName#,   <!-- 营销员姓名 -->
					PROCESS_DEMAND_QTY	= #processDemandQty#,   <!-- 生产需求数量 -->
					PROCESS_DEMAND_WEIGHT	= #processDemandWeight:NUMERIC#,   <!-- 生产需求重量 -->
					MPROVIDER_ID	= #mproviderId#,   <!-- 加工单位代码 -->
					MPROVIDER_NAME	= #mproviderName#,   <!-- 加工单位名称 -->
					AGREEMENT_ID	= #agreementId#,   <!-- 加工协议号 -->
					AGREEMENT_SUBID	= #agreementSubid#,   <!-- 加工协议子项号 -->
					PROCESS_FEE_PRICE_TYPE	= #processFeePriceType#,   <!-- 加工费计价类型 -->
					PROCESS_FEE_PRICE	= #processFeePrice:NUMERIC#,   <!-- 加工费单价(不含税) -->
					PROCESS_FEE_PRICE_TAX	= #processFeePriceTax:NUMERIC#,   <!-- 加工费单价(含税) -->
					TAX_RATE	= #taxRate:NUMERIC#,   <!-- 税率 -->
					DIAMETER	= #diameter:NUMERIC#,   <!-- 外径 -->
					LABEL_FORM	= #labelForm#,   <!-- 标签格式 -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->
					CUSTOMER_MATERIAL_NUMBER	= #customerMaterialNumber#,   <!-- 客户材料代码 -->
					PROCESS_DEMAND_OUTPUT_ID	= #processDemandOutputId#,   <!-- 生产需求单产出表序列号 -->
					SURFACE_GRADE	= #surfaceGrade#,   <!-- 表面等级 -->
					SEG_NO	= #segNo#,   <!-- 业务账套 -->
								PROD_CODE	= #prodCode#,   <!-- 品种代码 -->
					PROCESS_DEMAND_INPUT_ID	= #processDemandInputId#,   <!-- 投料单据号 -->
					D_USER_NUM	= #d_userNum#,   <!-- 分户号 -->
					D_USER_NAME	= #d_userName#,   <!-- 分户号名称 -->
					PRODUCTION_PROCESSED_QUANTITY	= #productionProcessedQuantity:NUMERIC#,   <!-- 生产已加工数量 -->
					PRODUCTION_PROCESSED_WEIGHT	= #productionProcessedWeight:NUMERIC#,   <!-- 生产已加工重量 -->
					PROCESS_SINGLE_PACK_QUANTITY	= #processSinglePackQuantity:NUMERIC#,   <!-- 生产单包数量 -->
					PROCESS_SINGLE_PACK_WEIGHT	= #processSinglePackWeight:NUMERIC#,   <!-- 生产单包重量 -->
					ESTIMATED_PACKAGES_NUMBER	= #estimatedPackagesNumber#,   <!-- 预计包数 -->
					PROCESS_BASE_VERSION_NUM	= #processBaseVersionNum:NUMERIC#,   <!-- 加工基准书版本号 -->
					WEIGHT_METHOD	= #weightMethod#,   <!-- 计重方式 -->
					TECH_STANDARD	= #techStandard#,   <!-- 技术标准 -->
					BSTS_COILING_ORD	= #bstsCoilingOrd#,   <!-- 好面朝向 -->
					QUALITY_GRADE	= #qualityGrade#,   <!-- 质量等级 -->
					FINAL_FM_SPEC	= #finalFmSpec#,   <!-- 最终成品规格 -->
					PROCESS_BASE_NO	= #processBaseNo#,   <!-- 加工基准书号 -->
					PROD_TYPE_ID	= #prodTypeId#,   <!-- 品种附属码 -->
					ORDER_TYPE_CODE	= #orderTypeCode#,   <!-- 订单性质代码 -->
					INNER_DIM	= #innerDim:NUMERIC#,   <!-- 内径(mm) -->
					DIR_PROCESS_DEMAND_ID	= #dirProcessDemandId#,   <!-- 去向生产需求单号 -->
					DIR_PROCESS_DEMAND_SUB_ID	= #dirProcessDemandSubId#,   <!-- 去向生产需求单子项 -->
					PRE_PROCESS_DEMAND_OUTPUT_SEQ_ID	= #preProcessDemandOutputSeqId#,   <!-- 上道产出表序列号 -->
					PROD_TYPE_DESC	= #prodTypeDesc#,   <!-- 品种附属码描述 -->
					PROCESS_IF_SETTLE	= #processIfSettle#,   <!-- 生产加工是否结算加工费 -->
					PROCESS_FEE_CALCULATE_METHOD	= #processFeeCalculateMethod#,   <!-- 加工费计算方式 -->
					EXCESS_STOCK_FLAG	= #excessStockFlag#,   <!-- 余料标记 -->
					FINISHING_SHUNT_FLAG	= #finishingShuntFlag#,   <!-- 精整分流标记 -->
					CONTRACT_PART_ID	= #contractPartId#,   <!-- 订单物料号 -->
					METAL_BALANCE_FLAG	= #metalBalanceFlag#,   <!-- 金属平衡标志 -->
					MANUAL_NO	= #manualNo#,   <!-- 手册编号 -->
					HS_ID	= #hsId#,   <!-- 海关HS系统编码 -->
					PROCESS_CONSIGN_UNIT	= #processConsignUnit#,   <!-- 加工委托方 -->
					PRINT_OUTPUT_REMARK	= #printOutputRemark#,   <!-- 打印产出物料信息备注 -->
					FIN_USER_NUM	= #finUserNum#,   <!-- 最终用户代码（订单用） -->
					FIN_USER_NAME	= #finUserName#,   <!-- 最终用户名称 -->
					HANDBOOK_ID	= #handbookId#,   <!-- 手册系统编号 -->
					CUSTOMS_PRODUCT_NUM	= #customsProductNum#, 
					F_PACK_ID	= #f_packId#,   <!-- 父捆包号 -->
					F_MAT_INNER_ID	= #f_matInnerId#,   <!-- 父捆包管理号 -->
					NODE_CODE	= #nodeCode#,   <!-- 节点号 -->
					DEMAND_REC_NO	= #demandRecNo#,   <!-- 需求识别卡号 -->
					DEMAND_REC_VERSION_NUM	= #demandRecVersionNum:NUMERIC#,   <!-- 需求识别卡版本号 -->
					CRAFT_CODE	= #craftCode#,   <!-- 工艺单号 -->
					CRAFT_VERSION_NUM	= #craftVersionNum:NUMERIC#,   <!-- 工艺单版本号 -->
					PROCESS_SEQ_CODE	= #processSeqCode#,   <!-- 加工序码 -->
					TAGEND_MGR_TYPE	= #tagendMgrType#,   <!-- 尾料处理方式 -->
					TRADE_CODE	= #tradeCode#,   <!-- 贸易方式 -->
					CUST_PART_ID	= #custPartId#,   <!-- 客户零件号 -->
					CUST_PART_NAME	= #custPartName#,   <!-- 客户零件号名称 -->
					BRACKET_TYPE	= #bracketType#,   <!-- 料架类型 -->
					IRON_BRACKET_NO	= #ironBracketNo#,   <!-- 料架编号 -->
					MAX_COIL_WT	= #maxCoilWt:NUMERIC#,   <!-- 最大卷重 -->
					IS_RETURN_MATERIAL_AREA	= #isReturnMaterialArea#,   <!-- 是否退回原料库 -->
					PRINT_BATCH_ID	= #printBatchId#  <!-- 打印批次号 -->
			WHERE 	
			UUID = #uuid#
	</update>

	<delete id="deleteOne">
		DELETE FROM ${meviSchema}.TVIPM0007
		WHERE
		SEG_NO = #segNo#
		AND UUID = #uuid#
	</delete>
  
</sqlMap>