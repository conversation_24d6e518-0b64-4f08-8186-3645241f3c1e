<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-chipId" cname="芯片ID" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-staffName" cname="人员姓名" colWidth="3" placeholder="模糊条件"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P032" condition="ITEM_CODE NOT IN ('30','99')"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="查询结果">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" sort="all">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="empNo" cname="员工工号" align="center" width="150" enable="true" readonly="true" required="true"/>
                <EF:EFColumn ename="staffName" cname="人员姓名" align="center" width="200" enable="true" required="true"/>
                <EF:EFColumn ename="chipId" cname="芯片ID" align="center" width="150" enable="true" readonly="true" required="true"/>
                <EF:EFComboColumn ename="teamId" cname="班组" width="150" enable="true" align="center" required="true">
                    <EF:EFOption label="甲班" value="10"/>
                    <EF:EFOption label="乙班" value="20"/>
                    <EF:EFOption label="丙班" value="30"/>
                    <EF:EFOption label="丁班" value="40"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="workingShift" cname="班次" width="150" enable="true" align="center" required="true">
                    <EF:EFOption label="早班" value="10" />
                    <EF:EFOption label="中班" value="20" />
                    <EF:EFOption label="晚班" value="30" />
                </EF:EFComboColumn>
                <EF:EFColumn ename="workingHours" cname="当班时间" align="center" width="150" enable="true" hidden="true"/>
                <EF:EFMultiSelectColumn ename="postResponsibility" cname="人员岗位职责" width="200" enable="true" align="center" required="true">
                    <EF:EFOption label="行车工" value="HC" />
                    <EF:EFOption label="机组操作工" value="JZ" />
                </EF:EFMultiSelectColumn>
                <EF:EFMultiSelectColumn ename="jobScope" cname="岗位范围" width="400" enable="true" align="center">
                    <EF:EFOption label="2050落料机组" value="L1" />
                    <EF:EFOption label="1650高强钢纵切机组" value="Z4" />
                    <EF:EFOption label="800横切线" value="J4" />
                    <EF:EFOption label="1650横切机组" value="H1" />
                </EF:EFMultiSelectColumn>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFCodeOption codeName="P032" condition="ITEM_CODE NOT IN ('30','99')"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="phoneNumber" cname="电话号码" align="left" width="150" enable="true"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>

    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>
