package com.baosight.imom.vg.dm.domain;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.common.vg.domain.Tvgdm1005;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 捆包生产实绩表
 */
public class VGDM1005 extends Tvgdm1005 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM1005.query";
    /**
     * 查询
     */
    public static final String QUERY_FOR_IMC = "VGDM1005.queryForImc";
    /**
     * 查询
     */
    public static final String QUERY_SUM = "VGDM1005.querySum";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1005.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1005.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1005.update";
    /**
     * 修改
     */
    public static final String UPDATE_HOUR = "VGDM1005.updateHour";
    /**
     * 修改
     */
    public static final String UPDATE_QUANTITY = "VGDM1005.updateQuantity";
    /**
     * 删除-逻辑删除
     */
    public static final String UPDATE_FOR_DEL = "VGDM1005.updForDel";


    private static final BigDecimal NUMBER1000 = new BigDecimal("1000");
    private static final BigDecimal NUMBER10 = new BigDecimal("10");

    /**
     * 获取已产出成品实绩信息
     *
     * @param uuid       投料捆包uuid
     * @param isQuantity 是否返回数量，是返回总数量，否返回总重量kg
     * @return 数量
     */
    public static BigDecimal querySumOutResult(Dao dao, String uuid, boolean isQuantity) {
        LogUtils.log("获取已产出实绩数量");
        // 获取已产出实绩重量
        Map<String, String> map3 = new HashMap<>();
        map3.put("relevanceId", uuid);
        map3.put("packType", "2");
        List<Map> sumList = dao.query(QUERY_SUM, map3);
        if (CollectionUtils.isEmpty(sumList)) {
            LogUtils.log("未产出实绩");
            return BigDecimal.ZERO;
        }
        if (isQuantity) {
            BigDecimal sumQuantity = MapUtils.getBigDecimal(sumList.get(0), "quantity", BigDecimal.ZERO);
            LogUtils.log("已产出实绩数量：" + sumQuantity);
            return sumQuantity;
        } else {
            BigDecimal sumWeight = MapUtils.getBigDecimal(sumList.get(0), "netWeight", BigDecimal.ZERO);
            LogUtils.log("已产出实绩重量kg：" + sumWeight);
            return sumWeight;
        }
    }

    /**
     * 获取纵切已产出成品实绩重量
     *
     * @param uuid 投料捆包uuid
     * @return 数量
     */
    public static BigDecimal querySLSumOutResult(Dao dao, String uuid) {
        LogUtils.log("获取纵切非余料实绩数量:");
        // 获取已产出实绩重量
        Map<String, String> map3 = new HashMap<>();
        map3.put("relevanceId", uuid);
        map3.put("notPackType", "4");
        List<Map> sumList = dao.query(QUERY_SUM, map3);
        if (CollectionUtils.isEmpty(sumList)) {
            LogUtils.log("未产出实绩");
            return BigDecimal.ZERO;
        }
        BigDecimal sumWeight = MapUtils.getBigDecimal(sumList.get(0), "netWeight", BigDecimal.ZERO);
        LogUtils.log("已产出实绩重量kg：" + sumWeight);
        return sumWeight;
    }

    /**
     * 重新计算实绩数量
     *
     * @param dao         dao
     * @param relevanceId 投料捆包uuid
     * @param segNo       账套
     * @param eArchivesNo 设备代码
     * @param ylWeight    原料重量 t
     * @return 更新后实绩信息
     */
    public static List<VGDM1005> reCalQuantity(Dao dao
            , String relevanceId
            , String segNo
            , String eArchivesNo
            , BigDecimal ylWeight) {
        Map<String, String> map = new HashMap<>();
        map.put("segNo", segNo);
        map.put("relevanceId", relevanceId);
        map.put("orderBy", "REC_CREATE_TIME asc");
        List<VGDM1005> packList = dao.query(VGDM1005.QUERY, map);
        if (CollectionUtils.isEmpty(packList)) {
            LogUtils.log(relevanceId + "未找到实绩信息");
            return null;
        }
        LogUtils.log("原实绩数量：" + packList.size());
        // 单片重量
        BigDecimal pieceWeight = queryPieceWeight(packList.get(0).getPartId());
        // 是否为落料
        boolean isBL = "52BL01".equals(eArchivesNo);
        LogUtils.log("是否落料：" + isBL);
        if (packList.size() < 4) {
            return reCalQuantity2(dao, packList, ylWeight, pieceWeight, isBL);
        }
        List<Map> updList = new ArrayList<>();
        // 有效捆包数
        int activeCount = 0;
        // 数量最多的捆包量
        BigDecimal mostQuantity = BigDecimal.ZERO;
        int mostCount = 0;
        // 每个值对应的次数
        Map<BigDecimal, Integer> countMap = new HashMap<>();
        for (int i = 0; i < packList.size(); i++) {
            VGDM1005 pack = packList.get(i);
            // 并包、精整分流不处理
            if (StrUtil.isNotBlank(pack.getUnitedPackId())
                    || StrUtil.isNotBlank(pack.getFinishingShuntFlag())) {
                continue;
            }
            // 最后一包是余料时倒数第二包不处理
            if (i == packList.size() - 2
                    && "4".equals(packList.get(i + 1).getPackType())) {
                continue;
            }
            // 最后一包不处理
            if (i == packList.size() - 1) {
                continue;
            }
            // 记录重复次数
            int count = 0;
            // 获取records总value重复最多的
            if (countMap.containsKey(pack.getQuantity())) {
                count = countMap.get(pack.getQuantity());
            }
            count++;
            countMap.put(pack.getQuantity(), count);
            if (count >= mostCount) {
                mostCount = count;
                mostQuantity = pack.getQuantity();
            }
            activeCount++;
        }
        LogUtils.log("有效捆包数：" + activeCount + "最多捆包数量：" + mostQuantity + "最多捆包计数：" + mostCount);
        if (activeCount < 4 || mostCount < 2) {
            return reCalQuantity2(dao, packList, ylWeight, pieceWeight, isBL);
        }
        // 遍历实绩信息
        List<VGDM1005> resultList = new ArrayList<>();
        for (int i = 0; i < packList.size(); i++) {
            VGDM1005 pack = packList.get(i);
            // 并包、精整分流不处理
            if (StrUtil.isNotBlank(pack.getUnitedPackId())
                    || StrUtil.isNotBlank(pack.getFinishingShuntFlag())) {
                resultList.add(pack);
                continue;
            }
            // 倒数第二包
            if (i == packList.size() - 2) {
                VGDM1005 lastPack = packList.get(i + 1);
                // 最后一包是余料时不处理
                if ("4".equals(lastPack.getPackType())) {
                    resultList.add(pack);
                    continue;
                }
                // 倒数第二包与最后一包数量相同且时间差10s以内不处理
                if (lastPack.getQuantity().compareTo(pack.getQuantity()) == 0) {
                    LocalDateTime lastPackTime = LocalDateTime.parse(lastPack.getRecCreateTime(), DateUtils.FORMATTER_14);
                    LocalDateTime thisPackTime = LocalDateTime.parse(pack.getRecCreateTime(), DateUtils.FORMATTER_14);
                    long seconds = Math.abs(Duration.between(lastPackTime, thisPackTime).getSeconds());
                    LogUtils.log("倒数第二包与最后一包时间差s：" + seconds);
                    if (seconds < 11) {
                        resultList.add(pack);
                        continue;
                    }
                }
            }
            // 最后一包不处理
            if (i == packList.size() - 1) {
                resultList.add(pack);
                continue;
            }
            BigDecimal newQuantity = convertNum(pack.getQuantity(), mostQuantity);
            // 与最多数量差值大于10时删除
            if (mostQuantity.subtract(newQuantity).compareTo(NUMBER10) > 0) {
                // 落料时删除
                if (isBL) {
                    pack.setDelFlag("1");
                } else {
                    resultList.add(pack);
                    continue;
                }
            } else {
                // 新数量
                pack.setQuantity(newQuantity);
                // 净重
                pack.setNetWeight(newQuantity
                        .multiply(pieceWeight)
                        .setScale(0, RoundingMode.HALF_UP));
                resultList.add(pack);
            }
            Map updMap = pack.toMap();
            RecordUtils.setRevisorSys(updMap);
            updList.add(updMap);
        }
        // 批量更新实绩信息
        DaoUtils.updateBatch(dao, VGDM1005.UPDATE_QUANTITY, updList);
        // 返回
        return reCalQuantity2(dao, resultList, ylWeight, pieceWeight, isBL);
    }

    /**
     * 重新计算实绩数量(精整分流和余料）
     *
     * @param dao         dao
     * @param packList    实绩信息
     * @param ylWeight    原料重量t
     * @param pieceWeight 单片重量kg
     * @param isBL        是否落料
     * @return 实绩信息
     */
    private static List<VGDM1005> reCalQuantity2(Dao dao
            , List<VGDM1005> packList
            , BigDecimal ylWeight
            , BigDecimal pieceWeight
            , boolean isBL) {
        List<VGDM1005> resultList = new ArrayList<>();
        if (isBL) {
            LogUtils.log("落料合并精整分流");
            VGDM1005 finalPack = null;
            BigDecimal totalQuantity = BigDecimal.ZERO;
            List<Map> updList = new ArrayList<>();
            // 循环合并精整分流量到一个
            for (VGDM1005 pack : packList) {
                if (StrUtil.isNotBlank(pack.getFinishingShuntFlag())) {
                    LogUtils.log("合并捆包：" + pack.getOutPackId() + "数量：" + pack.getQuantity());
                    totalQuantity = totalQuantity.add(pack.getQuantity());
                    // 合并后的捆包信息，取最后一个精整分流的包
                    finalPack = pack;
                    // 删除原捆包
                    Map updMap = pack.toMap();
                    updMap.put("delFlag", "1");
                    RecordUtils.setRevisorSys(updMap);
                    updList.add(updMap);
                    continue;
                }
                resultList.add(pack);
            }
            if (finalPack != null) {
                DaoUtils.updateBatch(dao, VGDM1005.UPDATE_QUANTITY, updList);
                LogUtils.log("合并后的数量" + totalQuantity + "捆包号：" + finalPack.getOutPackId());
                BigDecimal totalWeight = totalQuantity.multiply(pieceWeight)
                        .setScale(0, RoundingMode.HALF_UP);
                finalPack.setNetWeight(totalWeight);
                finalPack.setQuantity(totalQuantity);
                // 新主键
                finalPack.setUuid(UUIDUtils.getUUID());
                dao.insert(VGDM1005.INSERT, finalPack.toMap());
                // 添加新的实绩信息
                resultList.add(finalPack);
            }
        } else {
            LogUtils.log("非落料，无需合并精整分流");
            resultList = packList;
        }
        // 重算余料重量
        BigDecimal productWeight = BigDecimal.ZERO;
        VGDM1005 leftPack = null;
        for (VGDM1005 pack : resultList) {
            if ("4".equals(pack.getPackType())) {
                leftPack = pack;
                continue;
            }
            productWeight = productWeight.add(pack.getNetWeight());
        }
        if (leftPack != null) {
            BigDecimal leftWeight = ylWeight.multiply(NUMBER1000).subtract(productWeight);
            LogUtils.log("更新余料重量，原重量" + leftPack.getNetWeight() + "新重量" + leftWeight);
            leftPack.setNetWeight(leftWeight);
            Map updMap = leftPack.toMap();
            RecordUtils.setRevisorSys(updMap);
            dao.update(VGDM1005.UPDATE_QUANTITY, updMap);
        }
        return resultList;
    }

    /**
     * 转换数量
     *
     * @param quantity     原数量
     * @param mostQuantity 最多数量
     * @return 转换后数量
     */
    private static BigDecimal convertNum(BigDecimal quantity, BigDecimal mostQuantity) {
        LogUtils.log("传入数量：" + quantity + "比对数量：" + mostQuantity);
        BigDecimal left = mostQuantity.subtract(quantity).abs();
        BigDecimal rate = left.divide(mostQuantity, 2, RoundingMode.HALF_UP);
        LogUtils.log("差值：" + left + "比例：" + rate);
        if (rate.compareTo(new BigDecimal("0.1")) <= 0) {
            return mostQuantity;
        }
        return quantity;
    }

    /**
     * 获取单片重量kg
     *
     * @param partId 物料号
     * @return 单片重量，没有时返回1
     */
    public static BigDecimal queryPieceWeight(String partId) {
        // 根据物料号获取单片重量
        EiInfo imcInfo = new EiInfo();
        imcInfo.set("partId", partId);
        LogUtils.log("根据物料号查询信息：" + partId);
        // 服务ID
        imcInfo.set(EiConstant.serviceId, "S_VI_PM_1025");
        EiInfo rtnInfo = EServiceManager.call(imcInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
            LogUtils.log("查询物料信息失败：" + rtnInfo.getMsg());
            return BigDecimal.ONE;
        }
        List<Map> list = (List<Map>) rtnInfo.get("result");
        if (CollectionUtils.isEmpty(list)) {
            LogUtils.log("返回结果为空");
            return BigDecimal.ONE;
        }
        BigDecimal pieceWeight = MapUtils.getBigDecimal(list.get(0), "pieceWt", BigDecimal.ONE)
                .multiply(NUMBER1000);
        LogUtils.log("单片重量kg：" + pieceWeight);
        return pieceWeight;
    }
}
