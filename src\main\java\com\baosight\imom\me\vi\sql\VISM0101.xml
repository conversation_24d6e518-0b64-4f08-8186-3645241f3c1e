<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-10-16 14:41:55
   		Version :  1.0
		tableName :${meviSchema}.tvism0101 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 PROCESS_SWITCH_NAME  VARCHAR   NOT NULL, 
		 PROCESS_SWITCH_DESC  VARCHAR   NOT NULL, 
		 PROCESS_SWITCH_VALUE  VARCHAR   NOT NULL, 
		 PROCESS_SWITCH_VALUE_DESC  VARCHAR   NOT NULL
	-->
<sqlMap namespace="VISM0101">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.me.dv.domin.VISM0101">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				UUID	as "uuid",  
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				PROCESS_SWITCH_NAME	as "processSwitchName",  <!-- 生产开关名称（见名知义） -->
				PROCESS_SWITCH_DESC	as "processSwitchDesc",  <!-- 生产开关描述 -->
				PROCESS_SWITCH_VALUE	as "processSwitchValue",  <!-- 生产开关值（0:关;1:开;） -->
				PROCESS_SWITCH_VALUE_DESC	as "processSwitchValueDesc" <!-- 生产开关值描述（0:关;1:开;） -->
		FROM ${meviSchema}.tvism0101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="querySwitchValue" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select T.PROCESS_SWITCH_VALUE "switchValue"
		from MEVI.TVISM0101 T
		WHERE 1 = 1
	</select>

	<select id="querySwitchValueQueueCall" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select T.PROCESS_SWITCH_VALUE "switchValue",
				T.SEG_NO AS "segNo"
		from MEVI.TVISM0101 T
		WHERE 1 = 1
		and PROCESS_SWITCH_NAME=#processSwitchName#
		AND PROCESS_SWITCH_VALUE='1'
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meviSchema}.tvism0101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSwitchName">
			PROCESS_SWITCH_NAME = #processSwitchName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSwitchDesc">
			PROCESS_SWITCH_DESC = #processSwitchDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSwitchValue">
			PROCESS_SWITCH_VALUE = #processSwitchValue#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSwitchValueDesc">
			PROCESS_SWITCH_VALUE_DESC = #processSwitchValueDesc#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meviSchema}.tvism0101 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										UUID,
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										TENANT_USER,  <!-- 租户 -->
										PROCESS_SWITCH_NAME,  <!-- 生产开关名称（见名知义） -->
										PROCESS_SWITCH_DESC,  <!-- 生产开关描述 -->
										PROCESS_SWITCH_VALUE,  <!-- 生产开关值（0:关;1:开;） -->
										PROCESS_SWITCH_VALUE_DESC  <!-- 生产开关值描述（0:关;1:开;） -->
										)		 
	    VALUES (#segNo#, #unitCode#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #tenantUser#, #processSwitchName#, #processSwitchDesc#, #processSwitchValue#, #processSwitchValueDesc#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meviSchema}.tvism0101 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${meviSchema}.tvism0101 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
								REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					PROCESS_SWITCH_NAME	= #processSwitchName#,   <!-- 生产开关名称（见名知义） -->  
					PROCESS_SWITCH_DESC	= #processSwitchDesc#,   <!-- 生产开关描述 -->  
					PROCESS_SWITCH_VALUE	= #processSwitchValue#,   <!-- 生产开关值（0:关;1:开;） -->  
					PROCESS_SWITCH_VALUE_DESC	= #processSwitchValueDesc#  <!-- 生产开关值描述（0:关;1:开;） -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>