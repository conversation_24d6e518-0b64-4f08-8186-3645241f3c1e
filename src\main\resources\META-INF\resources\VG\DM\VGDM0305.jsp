<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                           startCname="创建时间(起)" endCname="创建时间(止)"
                           ratio="3:3" format="yyyy-MM-dd" required="true">
            </EF:EFDateSpan>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="离线时间">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码"  align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称" align="center"/>
            <EF:EFColumn ename="deviceId" cname="设备ID"  align="center"/>
            <EF:EFColumn ename="deviceName" cname="设备名称"  align="center"/>
            <EF:EFColumn ename="offlineMinutes" cname="离线时间(分)" align="right"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>