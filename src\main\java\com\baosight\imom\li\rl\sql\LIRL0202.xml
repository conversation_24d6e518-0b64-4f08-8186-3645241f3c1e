<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-26 10:22:22
       Version :  1.0
    tableName :${meliSchema}.tlirl0202
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     BLACKLIST_FLAG  VARCHAR   NOT NULL,
     CUSTOMER_ID  VARCHAR   NOT NULL,
     CUSTOMER_NAME  VARCHAR   NOT NULL,
     DRIVER_NAME  VARCHAR   NOT NULL,
     DRIVER_TEL  VARCHAR   NOT NULL,
     DRIVER_IDENTITY  VARCHAR   NOT NULL,
     VEHICLE_NO  VARCHAR   NOT NULL,
     RESERVATION_TIME  VARCHAR   NOT NULL,
     ENTER_TIME  VARCHAR   NOT NULL,
     FORBIDDEN_TIME  VARCHAR   NOT NULL,
     ENABLE_TIME  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0202">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			a.SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			a.UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="blacklistFlag">
			a.BLACKLIST_FLAG = #blacklistFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			a.CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			a.CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			a.DRIVER_NAME like concat('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			a.DRIVER_TEL = #driverTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			a.DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationTime">
			a.RESERVATION_TIME = #reservationTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="enterTime">
			a.ENTER_TIME = #enterTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="forbiddenTime">
			a.FORBIDDEN_TIME = #forbiddenTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="enableTime">
			a.ENABLE_TIME = #enableTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			a.REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			a.REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			a.REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			a.REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			a.REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			a.REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			a.ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			a.DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			a.REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			a.SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			a.UUID != #notUuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			a.UUID like concat('%',#uuid#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			a.TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0202">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		a.BLACKLIST_FLAG as "blacklistFlag",  <!-- 状态(禁用：1，启用：2) -->
		(select b.RESERVATION_IDENTITY from MELI.tlirl0101 b where
		b.SEG_NO = a.SEG_NO
		and b.CUSTOMER_ID = a.CUSTOMER_ID
		and b.STATUS = '20'
		limit 1) as "reservationIdentity",
		a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
		a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
		a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
		a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
		a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
		a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
		a.RESERVATION_TIME as "reservationTime",  <!-- 预约时间 -->
		a.ENTER_TIME as "enterTime",  <!-- 登记时间 -->
		a.FORBIDDEN_TIME as "forbiddenTime",  <!-- 禁用时间 -->
		a.ENABLE_TIME as "enableTime",  <!-- 解禁时间 -->
		a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
		a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		a.REMARK as "remark",  <!-- 备注 -->
		a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
		a.UUID as "uuid",  <!-- uuid -->
		a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0202 a WHERE 1=1
		<include refid="condition"/>
		<isNotEmpty prepend="and" property="noFlag">
			STR_TO_DATE(a.FORBIDDEN_TIME, '%Y%m%d%H%i%s') &lt; DATE_SUB(NOW(), INTERVAL 30 DAY)
		</isNotEmpty>
		<isNotEmpty prepend="and" property="insType">
			INS_TYPE=#insType#
		</isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
				REC_CREATE_TIME DESC,
				REC_REVISE_TIME DESC,
				BLACKLIST_FLAG DESC,
				CUSTOMER_ID DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0202 a WHERE 1=1
		<include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="blacklistFlag">
            BLACKLIST_FLAG = #blacklistFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverTel">
            DRIVER_TEL = #driverTel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverIdentity">
            DRIVER_IDENTITY = #driverIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="enterTime">
            ENTER_TIME = #enterTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="forbiddenTime">
            FORBIDDEN_TIME = #forbiddenTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="enableTime">
            ENABLE_TIME = #enableTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0202 (SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        BLACKLIST_FLAG,  <!-- 状态(禁用：1，启用：2) -->
        CUSTOMER_ID,  <!-- 承运商/客户代码 -->
        CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
        DRIVER_NAME,  <!-- 司机姓名 -->
        DRIVER_TEL,  <!-- 司机电话 -->
        DRIVER_IDENTITY,  <!-- 司机身份 -->
        VEHICLE_NO,  <!-- 车牌号 -->
        RESERVATION_TIME,  <!-- 预约时间 -->
        FORBIDDEN_TIME,  <!-- 禁用时间 -->
        ENABLE_TIME,  <!-- 解禁时间 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID,  <!-- 租户ID -->
		INS_TYPE
        )
        VALUES (#segNo#, #unitCode#, #blacklistFlag#, #customerId#, #customerName#, #driverName#, #driverTel#,
        #driverIdentity#, #vehicleNo#, #reservationTime#, #forbiddenTime#, #enableTime#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#,
        #remark#, #sysRemark#, #uuid#, #tenantId#,#insType#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0202 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0202
        SET
        SEG_NO = #segNo#,   <!-- 业务单元代代码 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        BLACKLIST_FLAG = #blacklistFlag#,   <!-- 状态(禁用：1，启用：2) -->
        CUSTOMER_ID = #customerId#,   <!-- 承运商/客户代码 -->
        CUSTOMER_NAME = #customerName#,   <!-- 承运商/客户名称 -->
        DRIVER_NAME = #driverName#,   <!-- 司机姓名 -->
        DRIVER_TEL = #driverTel#,   <!-- 司机电话 -->
        DRIVER_IDENTITY = #driverIdentity#,   <!-- 司机身份 -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        RESERVATION_TIME = #reservationTime#,   <!-- 预约时间 -->
        ENTER_TIME = #enterTime#,   <!-- 登记时间 -->
        FORBIDDEN_TIME = #forbiddenTime#,   <!-- 禁用时间 -->
        ENABLE_TIME = #enableTime#,   <!-- 解禁时间 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        TENANT_ID = #tenantId#  <!-- 租户ID -->
        WHERE
        UUID = #uuid#
    </update>


	<update id="updateForbiddenStatus">
		UPDATE ${meliSchema}.tlirl0202
		SET
		ENTER_TIME = #enterTime#,   <!-- 登记时间 -->
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
		SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
		BLACKLIST_FLAG = #blacklistFlag#   <!-- 系统备注 -->
		WHERE
		UUID = #uuid#
		AND SEG_NO = #segNo#
	</update>

</sqlMap>