$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfoMainQuery",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });
    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
    });
    var addFlag = false;
    var updateFlag = false;
    var finishFlag = false;
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId === "info-2") {
                    // 只判断非新增、修改和处理完成按钮跳转
                    if (!addFlag && !updateFlag && !finishFlag) {
                        const checkedRows = resultGrid.getCheckedRows();
                        const checkRowLength = checkedRows.length;
                        if (checkRowLength !== 1) {
                            NotificationUtil({msg: "请勾选一条故障信息"}, "error");
                            e.preventDefault();
                        } else {
                            setDetailData(checkedRows[0]);
                            setReadStatus();
                            setFinishAreaShow();
                        }
                    }
                } else {
                    addFlag = false;
                    updateFlag = false;
                    finishFlag = false;
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        // 故障信息
        "result": {
            onRowDblClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                setReadStatus();
                tab_Strip.select(1);
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT_ADD").on("click", function (e) {
                    addFlag = true;
                    IPLAT.clearNode(document.getElementById("info-2"));
                    setInsertStatus();
                    tab_Strip.select(1);
                    $("#detail_status-0-unitCode").val(unitInfo.unitCode);
                    IPLAT.EFSelect.value($("#detail_status-0-segNo"), unitInfo.segNo);
                });
                // 修改按钮
                $("#UPDATE").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能对一行数据进行修改！]"}, "error");
                        return;
                    }
                    const faultStatus = checkedRows[0].faultStatus;
                    if (faultStatus !== "10") {
                        NotificationUtil({msg: "操作失败，原因[只能新增状态数据进行修改！]"}, "error");
                        return;
                    }
                    updateFlag = true;
                    setDetailData(checkedRows[0]);
                    setUpdateStatus();
                    tab_Strip.select(1);
                });
                // 处理完成按钮
                $("#FINISH").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能对一行数据进行修改！]"}, "error");
                        return;
                    }
                    const faultStatus = checkedRows[0].faultStatus;
                    if (faultStatus !== "20") {
                        NotificationUtil({msg: "操作失败，原因[只能对已提交状态数据进行操作！]"}, "error");
                        return;
                    }
                    finishFlag = true;
                    tab_Strip.select(1);
                    setDetailData(checkedRows[0]);
                    setFinishStatus();
                    setFinishAreaShow();
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM07", "submit", true, null, null, false);
                });
                // 取消提交按钮
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM07", "cancelSubmit", true, null, null, false);
                });
                // 加入设备履历按钮
                $("#ADDRESUME").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM07", "addResume", true, null, null, false);
                });
                // 新增保存按钮
                $("#INSERT_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM07", "insert", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
                // 修改保存按钮
                $("#UPDATE_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM07", "update", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
                // 处理完成保存按钮
                $("#FINISH_SAVE").on("click", function (e) {
                    let validator = IPLAT.Validator({
                        id: "area1"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    // 是否检修为是时校验检修数据和领用申请信息
                    const isOverhaul = $("#detail_status-0-isOverhaul").val();
                    if (isOverhaul === "1") {
                        validator = IPLAT.Validator({
                            id: "area2"
                        });
                        if (!validator.validate()) {
                            return;
                        }
                        checkAndFinishFault();
                    } else {
                        finishFault();
                    }
                });
            }
        },
        // 附件信息
        "result2": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
            loadComplete: function (grid) {
                // 附件上传按钮
                $("#FILEUPLOAD").click(function () {
                    const relevanceId = $("#inqu2_status-0-relevanceId").val();
                    if (IPLAT.isBlankString(relevanceId)) {
                        NotificationUtil("操作失败，原因[请先维护故障信息！]", "error");
                        return;
                    }
                    $("#fileForm").click();
                });
                // 动火证附件上传按钮
                $("#uploadHot").click(function () {
                    $("#fileForm1").click();
                });
            }
        },
        // 资材领用申请明细
        "zc_detail": {
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["stuffReceivingStatus"] = "10";
                    item["faultId"] = $("#inqu_status2-0-faultId").val();
                    item["unitCode"] = $("#inqu_status2-0-unitCode").val();
                    item["segNo"] = $("#inqu_status2-0-segNo").val();
                });
            },
            beforeEdit: function (e) {
                // 控制只有新增状态可修改
                if (e.field === "usingWgt") {
                    if (e.model.stuffReceivingStatus !== "10") {
                        e.preventDefault();
                    }
                }
            },
            loadComplete: function (grid) {
                // 选择库存按钮
                $("#APPLYMATERIALS").on("click", function (e) {
                    const row = resultGrid.getCheckedRows()[0];
                    if (row.faultStatus !== "20") {
                        NotificationUtil({msg: "操作失败，原因[只能对已提交状态的故障信息进行操作]"}, "error");
                        return;
                    }
                    // 查询IMC库存
                    inventoryInfoWindow.center().open();
                });
                // 新增按钮
                $("#INSERTM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("relevanceType", "12");
                    eiInfo.set("faultId", $("#inqu_status2-0-faultId").val());
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "insert", function (ei) {
                        zc_detailGrid.dataSource.page(1);
                    });
                });
                // 修改按钮
                $("#UPDATEM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("relevanceType", "12");
                    eiInfo.set("faultId", $("#inqu_status2-0-faultId").val());
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "update", function (ei) {
                        zc_detailGrid.dataSource.page(1);
                    });
                });
                // 删除按钮
                $("#DELETEM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    IPLAT.confirm({
                        title: "删除",
                        message: "确认删除此数据么?",
                        okFn: function (e) {
                            const eiInfo = new EiInfo();
                            eiInfo.set("relevanceType", "12");
                            eiInfo.set("faultId", $("#inqu_status2-0-faultId").val());
                            eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                            IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "delete", function (ei) {
                                zc_detailGrid.dataSource.page(1);
                            });
                        }
                    });
                });
                // 生成报废申请
                $("#ADDSCRAP").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("segNo", $("#detail_status-0-unitCode").val());
                    eiInfo.set("eArchivesNo", $("#detail_status-0-eArchivesNo").val());
                    eiInfo.set("equipmentName", $("#detail_status-0-equipmentName").val());
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM09", "insertFromStuff", function (ei) {
                        // zc_detailGrid.dataSource.page(1);
                    });
                });
            }
        }
    };
    IPLATUI.EFSelect = {
        // 是否检修
        "detail_status-0-isOverhaul": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                var dataItem = e.dataItem;
                // 是否检修 1-是 0-否
                if (dataItem.valueField === "1") {
                    $("#area2").show();
                    $("#zc_detail").show();
                } else {
                    $("#area2").hide();
                    $("#zc_detail").hide();
                }
            }
        },
        // 施工类别
        "detail_status-0-overhaulType": {
            // 点击下拉选项时触发
            select: function (e) {
                // 施工类别为检修时显示检修区域
                var dataItem = e.dataItem;
                if (dataItem.valueField === "10") {
                    $("#detail_status-0-implementManName").val(loginCName);
                    // 委外联络单号清空
                    $("#detail_status-0-outsourcingContactId").val("");
                } else if (dataItem.valueField === "20") {
                    //实施人设置为委外人员
                    $("#detail_status-0-implementManName").val("委外人员");
                    // 委外联络单号可编辑
                    $("#detail_status-0-outsourcingContactId").attr("readonly", false);
                }
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 详情区域业务单元弹窗
    IMOMUtil.importDetailUnitInfo();
    // 详情区域设备弹窗
    IMOMUtil.importDetailEquipmentInfo();
    // 详情区域分部设备弹窗
    IMOMUtil.importDetailDeviceInfo();
    // 详情区域IMC库存弹窗
    IMOMUtil.windowTemplate({
        windowId: "inventoryInfo",
        notQuery: true,
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status2-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status2-0-segNo").val());
            iframejQuery("#inqu_status-0-segName").val($("#inqu_status2-0-segName").val());
        },
        afterSelect: function (rows) {
        }
    });

    /**
     * 检查备品备件领用申请并处理完成
     */
    function checkAndFinishFault() {
        // 故障单号列表
        const idList = [$("#inqu2_status-0-relevanceId").val()];
        const checkInfo = new EiInfo();
        checkInfo.set("relevanceType", "12");
        checkInfo.set("idList", idList);
        // 查询备品备件领用申请数量
        IMOMUtil.submitEiInfo(checkInfo, "VGDM0804", "queryApplyCount", function (ei) {
            const rowCount = ei.get("rowCount");
            if (rowCount === 0) {
                IPLAT.confirm({
                    message: "未维护资材备件领用信息，是否直接处理完成?",
                    okFn: function (e) {
                        finishFault();
                    },
                    title: "提示"
                });
                return;
            }
            finishFault();
        });
    }

    /**
     * 处理完成
     */
    function finishFault() {
        const eiInfo = new EiInfo();
        eiInfo.setByNode("info-2");
        IMOMUtil.submitEiInfo(eiInfo, "VGDM07", "finish", function (ei) {
            tab_Strip.select(0);
            resultGrid.dataSource.page(1);
            NotificationUtil({msg: ei.msg}, "success");
        });
    }

    /**
     * 设置详情区域新增状态
     */
    function setInsertStatus() {
        // 新增按钮启用
        $("#INSERT_SAVE").attr("disabled", false);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 处理完成按钮禁用
        $("#FINISH_SAVE").attr("disabled", true);
        setZcEdit(false);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", false);
        $("#detail_status-0-equipmentName").attr("disabled", false);
        $("#detail_status-0-deviceName").attr("disabled", false);
        $("#detail_status-0-faultStartTime").data("kendoDateTimePicker").enable(true);
        $("#detail_status-0-faultDesc").attr("readonly", false);
        setFieldEditable(true);
        setAreaEditable(false);
        // 故障来源设置为手工新增
        IPLAT.EFSelect.value($("#detail_status-0-faultSource"), "1");
        // 处理完成区域隐藏
        $("#detail2").hide();
        $("#zc_detail").hide();
        // 清除附件信息
        if (result2Grid.getDataItems().length > 0) {
            result2Grid.removeRows(result2Grid.getDataItems());
        }
    }

    /**
     * 设置详情区域修改状态
     */
    function setUpdateStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮启用
        $("#UPDATE_SAVE").attr("disabled", false);
        // 处理完成按钮禁用
        $("#FINISH_SAVE").attr("disabled", true);
        setZcEdit(false);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-deviceName").attr("disabled", true);
        const faultSource = IPLAT.EFSelect.value($("#detail_status-0-faultSource"));
        if (faultSource === "1") {
            $("#detail_status-0-faultStartTime").data("kendoDateTimePicker").enable(true);
            $("#detail_status-0-faultDesc").attr("readonly", false);
        } else {
            $("#detail_status-0-faultStartTime").data("kendoDateTimePicker").enable(false);
            $("#detail_status-0-faultDesc").attr("readonly", true);
        }
        setFieldEditable(true);
        setAreaEditable(false);
        // 处理完成区域隐藏
        $("#detail2").hide();
    }

    /**
     * 设置详情区域只读状态
     */
    function setReadStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 处理完成按钮禁用
        $("#FINISH_SAVE").attr("disabled", true);
        setZcEdit(false);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-deviceName").attr("disabled", true);
        $("#detail_status-0-faultStartTime").data("kendoDateTimePicker").enable(false);
        $("#detail_status-0-faultDesc").attr("readonly", true);
        setFieldEditable(false);
        setAreaEditable(false);
    }

    /**
     * 设置详情区域处理完成状态
     */
    function setFinishStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 处理完成按钮启用
        $("#FINISH_SAVE").attr("disabled", false);
        setZcEdit(true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-deviceName").attr("disabled", true);
        $("#detail_status-0-faultStartTime").data("kendoDateTimePicker").enable(false);
        $("#detail_status-0-faultDesc").attr("readonly", true);
        setFieldEditable(false);
        setAreaEditable(true);
        // 设置处理措施值为空
        $("#detail_status-0-handleMeasures").val("");
    }

    /**
     * 设置资材相关按钮编辑性
     * @param isEdit
     */
    function setZcEdit(isEdit) {
        if (isEdit) {
            $("#FILEUPLOAD").css("pointer-events", "auto");
            $("#FILEUPLOAD").find("button").attr("disabled", false)
            $("#APPLYMATERIALS").css("pointer-events", "auto");
            $("#APPLYMATERIALS").find("button").attr("disabled", false)
            $("#INSERTM").css("pointer-events", "auto");
            $("#INSERTM").find("button").attr("disabled", false)
            $("#UPDATEM").css("pointer-events", "auto");
            $("#UPDATEM").find("button").attr("disabled", false)
            $("#DELETEM").css("pointer-events", "auto");
            $("#DELETEM").find("button").attr("disabled", false)
        } else {
            //$("#FILEUPLOAD").css("pointer-events", "none");
            //$("#FILEUPLOAD").find("button").attr("disabled", true)
            $("#APPLYMATERIALS").css("pointer-events", "none");
            $("#APPLYMATERIALS").find("button").attr("disabled", true)
            $("#INSERTM").css("pointer-events", "none");
            $("#INSERTM").find("button").attr("disabled", true)
            $("#UPDATEM").css("pointer-events", "none");
            $("#UPDATEM").find("button").attr("disabled", true)
            $("#DELETEM").css("pointer-events", "none");
            $("#DELETEM").find("button").attr("disabled", true)
        }
    }

    /**
     * 设置详情区域字段是否可编辑
     * @param isEdit 是否可编辑
     */
    function setFieldEditable(isEdit) {
        IPLAT.EFSelect.enable($("#detail_status-0-faultType"), isEdit);
        IPLAT.EFSelect.enable($("#detail_status-0-faultLevel"), isEdit);
    }

    /**
     * 设置处理结果区域是否可编辑
     * @param isEdit 是否可编辑
     */
    function setAreaEditable(isEdit) {
        // 动火证附件上传按钮
        $("#uploadHot").attr("disabled", !isEdit);
        $("#detail_status-0-faultEndTime").data("kendoDateTimePicker").enable(isEdit);
        IPLAT.EFSelect.enable($("#detail_status-0-isOverhaul"), isEdit);
        $("#detail_status-0-handleMeasures").attr("readonly", !isEdit);
        IPLAT.EFSelect.enable($("#detail_status-0-overhaulType"), isEdit);
        $("#detail_status-0-outsourcingContactId").attr("readonly", true);
        $("#detail_status-0-overhaulImplementDate").data("kendoDatePicker").enable(isEdit);
        $("#detail_status-0-actualOverhaulNumber").attr("readonly", !isEdit);
        $("#detail_status-0-actualOverhaulTime").attr("readonly", !isEdit);
        IPLAT.EFSelect.enable($("#detail_status-0-offlinePartsGone"), isEdit);
        IPLAT.EFSelect.enable($("#detail_status-0-isHot"), isEdit);
        $("#detail_status-0-hotCardId").attr("readonly", true);
        $("#detail_status-0-overhaulSuggestions").attr("readonly", !isEdit);
        $("#detail_status-0-overhaulSummarize").attr("readonly", !isEdit);
    }

    /**
     * 设置处理完成区域显示
     */
    function setFinishAreaShow() {
        const faultStatus = $("#detail_status-0-faultStatus").val();
        // 提交或处理完成时显示处理完成区域
        if (faultStatus === "20" || faultStatus === "40") {
            $("#detail2").show();
            // 处理检修数据显示
            const isOverhaul = IPLAT.EFSelect.value($("#detail_status-0-isOverhaul"));
            if (isOverhaul === "1") {
                $("#area2").show();
                $("#zc_detail").show();
            } else {
                $("#area2").hide();
                $("#zc_detail").hide();
            }
        } else {
            $("#detail2").hide();
        }
    }

    /**
     * 设置详情区域内容
     * @param row grid行数据
     */
    function setDetailData(row) {
        // 清除原数据
        IPLAT.clearNode(document.getElementById("info-2"));
        // 将数据回填到详情页
        IMOMUtil.fillNode(row, "detail");
        // 将数据回填到处理完成区域
        IMOMUtil.fillNode(row, "detail2");
        // 附件查询
        $("#inqu2_status-0-relevanceId").val(row.faultId);
        $("#inqu2_status-0-segNo").val(row.segNo);
        result2Grid.dataSource.page(1);
        // 资材相关
        $("#inqu_status2-0-eArchivesNo").val(row.eArchivesNo);
        $("#inqu_status2-0-unitCode").val(row.unitCode);
        $("#inqu_status2-0-segNo").val(row.segNo);
        $("#inqu_status2-0-faultId").val(row.faultId);
        // 业务单元简称特殊处理
        const rowIndex = resultGrid.getCheckedRowsIndex()[0];
        const segNoLabel = `result-${rowIndex}-segNo`;
        const segName = resultGrid.getDisplayEiInfo().get(segNoLabel);
        $("#inqu_status2-0-segName").val(segName);
        // 查询资材领用申请明细
        zc_detailGrid.dataSource.page(1);
    }

    var relevanceId = "";
    var segNo = "";
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            result2Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM07";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            relevanceId = $("#inqu2_status-0-relevanceId").val();
            segNo = $("#inqu_status-0-segNo").val();
        }
    });
    IPLAT.FileUploader({
        id: "fileForm1",
        ename: "fileForm1",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            result2Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm1" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM07H";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            relevanceId = $("#inqu2_status-0-relevanceId").val();
            segNo = $("#inqu_status-0-segNo").val();
        }
    });
});
