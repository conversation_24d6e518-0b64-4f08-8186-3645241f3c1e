package com.baosight.hdsdk.domain.data;

import com.baosight.hdjni.Jni_iHyperDB;
import com.baosight.hdjni.LongWrapper;
import com.baosight.hdjni.RedisInfo;
import com.baosight.hdsdk.*;
import com.baosight.hdsdk.base.HDConnection;
import com.baosight.hdsdk.base.HDServer;
import com.baosight.hdsdk.exception.HDSdkException;
import org.apache.log4j.Logger;

import java.util.Map;

public class HDDataConnection extends HDConnection {
    private static final Logger logger = Logger.getLogger(com.baosight.hdsdk.domain.data.HDDataConnection.class);
    private HDDataProvider singleDataProvider = null;
    private HDDataWriter singleDataWriter = null;
    private HDAlarmManager singleAlarmManager = null;
    private HDTagManager singleTagManager = null;
    private HDDataAnalysis singleDataAnalysis = null;
    private HDUserManager singleUserManager = null;

    public HDDataConnection() {
    }

    public HDDataConnection(HDDataServer server) throws HDSdkException {
        super(server);
        this.buildConnection(server);
    }

    public void loginToServer(String userName, String userPwd) throws HDSdkException {
        logger.debug("登录服务器 server" + this.getServer().toString());
        if (userName == null) {
            logger.error("用户名是空指针！");
            throw new HDSdkException("用户名是空指针！");
        } else if (userPwd == null) {
            logger.error("密码是空指针！");
            throw new HDSdkException("密码是空指针！");
        } else if (userName.equals("")) {
            logger.error("用户名为空！");
            throw new HDSdkException("用户名为空！");
        } else if (userPwd.equals("")) {
            logger.error("密码为空！");
            throw new HDSdkException("密码为空！");
        } else if (!this.isConnected()) {
            logger.error("当前还未建立与服务器");
            throw new HDSdkException("当前还未建立与服务端的连接！");
        } else {
            int returnValue = Jni_iHyperDB.login(userName, userPwd);
            if (returnValue != 0) {
                HDSdkException hdEx = new HDSdkException("登录服务器失败！Server=" + this.getServer().toString(), returnValue);
                logger.error("登录服务器 " + this.getServer().toString() + " 失败！错误码 = " + returnValue);
                logger.error(hdEx.getStackTraceAsString());
                throw hdEx;
            } else {
                logger.debug("登录服务器完成！");
            }
        }
    }

    public void buildConnection(HDServer server) throws HDSdkException {
        logger.debug("建立连接 server" + server);
        this.setServer(server);
        int retValue = Jni_iHyperDB.connectEx(server.getServerIp(), server.getServerPort(), server.getServerBakIp(), server.getServerBakPort());
        if (retValue != 0) {
            HDSdkException hdEx = new HDSdkException("建立连接失败！Server=" + server.toString(), retValue);
            logger.error("建立连接失败！错误码 = " + retValue);
            logger.error(hdEx.getStackTraceAsString());
            int disRet = Jni_iHyperDB.disconnect();
            logger.error("释放连接返回："+disRet);
            throw hdEx;
        } else {
            super.setConnected(true);
            this.setServer(server);
            logger.debug("建立连接完成！");
        }
    }

    public void dispose() throws HDSdkException {
        logger.debug("释放连接" + super.toString());
        if (!this.isConnected()) {
            logger.warn("连接已经断开！");
        } else {
            int disRet = Jni_iHyperDB.disconnect();
            if (disRet != 0) {
                HDSdkException sdkEx = new HDSdkException("释放连接出错!Server=" + this.getServer().toString(), disRet);
                logger.error("释放连接失败！错误码 = " + sdkEx.getErrorCode().getErrorNum());
                logger.error(sdkEx.getStackTraceAsString());
                throw sdkEx;
            } else {
                super.setConnected(false);
                logger.debug("释放连接完成！");
            }
        }
    }

    public HDTagManager getTagManager() {
        if (!this.isConnected() || this.singleTagManager == null) {
            this.singleTagManager = new HDTagManager(this);
        }

        return this.singleTagManager;
    }

    public HDDataProvider getDataProvider() {
        if (!this.isConnected() || this.singleDataProvider == null) {
            this.singleDataProvider = new HDDataProvider(this);
        }

        return this.singleDataProvider;
    }

    public int sendRedisInfo(Map<String, String> initMap) throws HDSdkException {
        if (!this.isConnected() || this.singleDataProvider == null) {
            this.singleDataProvider = new HDDataProvider(this);
        }

        if (!this.isConnected()) {
            logger.error("当前还未建立与服务器");
            throw new HDSdkException("当前还未建立与服务端的连接！");
        } else {
            RedisInfo redisInfo = new RedisInfo();
            redisInfo.m_name = (String) initMap.get("name");
            redisInfo.m_hostip = (String) initMap.get("hostip");
            redisInfo.m_port = (String) initMap.get("port");
            redisInfo.m_password = (String) initMap.get("password");
            redisInfo.m_maxTotal = (String) initMap.get("maxTotal");
            redisInfo.m_maxWait = (String) initMap.get("maxWait");
            redisInfo.m_mastername = (String) initMap.get("mastername");
            redisInfo.m_routingrtalms = (String) initMap.get("routingrtalms");
            redisInfo.m_routingclearalm = (String) initMap.get("routingclearalm");
            redisInfo.m_routingrtrec = (String) initMap.get("routingrtrec");
            redisInfo.m_routingcrs = (String) initMap.get("routingcrs");
            int returnValue = Jni_iHyperDB.sendRedisInfo(redisInfo);
            if (returnValue != 0) {
                HDSdkException hdEx = new HDSdkException("发送redis配置失败！Server=" + this.getServer().toString(), returnValue);
                logger.error("发送redis配置失败！错误码 = " + returnValue);
                logger.error(hdEx.getStackTraceAsString());
                throw hdEx;
            } else {
                return returnValue;
            }
        }
    }

    public HDAlarmManager getAlarmManager() {
        if (!this.isConnected() || this.singleAlarmManager == null) {
            this.singleAlarmManager = new HDAlarmManager(this);
        }

        return this.singleAlarmManager;
    }

    public HDDataAnalysis getDataAnalysis() {
        if (!this.isConnected() || this.singleDataAnalysis == null) {
            this.singleDataAnalysis = new HDDataAnalysis(this);
        }

        return this.singleDataAnalysis;
    }

    public HDDataWriter getDataWriter() {
        if (!this.isConnected() || this.singleDataWriter == null) {
            this.singleDataWriter = new HDDataWriter(this);
        }

        return this.singleDataWriter;
    }

    public HDUserManager getUserManager() {
        if (!this.isConnected() || this.singleUserManager == null) {
            this.singleUserManager = new HDUserManager(this);
        }

        return this.singleUserManager;
    }

    public int getNewlyTagVersion(LongWrapper lversion) {
        int nRet = Jni_iHyperDB.getNewlyTagVersion(lversion);
        return nRet;
    }

    public void setRequestCacheCapacity(int nCapacity) {
        Jni_iHyperDB.setRequestCacheCapacity(nCapacity);
    }

    public void setTimeoutSec(int timeoutSec) throws HDSdkException {
        logger.debug("setTimeoutSec:" + timeoutSec);
        if (!this.isConnected()) {
            logger.error("当前还未建立与服务器");
            throw new IllegalStateException("当前还未建立与服务端的连接！");
        } else {
            Jni_iHyperDB.setRequestTimeout(timeoutSec);
        }
    }

    public void setAPIOption(int option) throws HDSdkException {
        if (!this.isConnected()) {
            logger.error("当前还未建立与服务器");
            throw new IllegalStateException("当前还未建立与服务端的连接！");
        } else {
            Jni_iHyperDB.setAPIOption(option);
        }
    }

    public boolean checkConnect() throws HDSdkException {
        logger.trace("checkConnect");
        int ret = 0;
        if (!this.isConnected()) {
            logger.error("当前还未建立与服务器");
            throw new IllegalStateException("当前还未建立与服务端的连接！");
        } else {
            LongWrapper longWrapper = new LongWrapper();
            longWrapper.Value = 0L;
            ret = Jni_iHyperDB.getServerMsTime(longWrapper);
            if (ret != 0) {
                HDSdkException sdkEx = new HDSdkException("getServerMsTime faield", ret);
                logger.error("getServerMsTime faield  " + sdkEx.getErrorCode().getErrorNum());
                logger.error(sdkEx.getStackTraceAsString());
                throw sdkEx;
            } else {
                logger.debug("checkConnect done！ getServerMsTime=" + longWrapper.Value);
                return longWrapper.Value > 0L;
            }
        }
    }
}
