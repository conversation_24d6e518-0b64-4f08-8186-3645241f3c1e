package com.baosight.imom.common.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XLocalManager;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.client.CookieStore;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.cookie.Cookie;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * tag点实时刷新客户端
 *
 * <AUTHOR> 郁在杰
 * @Description : 注册点后实时反馈点位数据变化
 * @Date : 2024/12/5
 * @Version : 1.0
 */
public class TagRefreshClient extends WebSocketClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(TagRefreshClient.class);

    private static final String WEB_SOCKET_STR = ".TagRefreshEndpoint";
    private static final String LOGIN_URL_STR = ".TagRefreshLoginUrl";
    private static final String USERNAME_STR = ".TagRefreshUsername";
    private static final String PASSWORD_STR = ".TagRefreshPassword";
    /**
     * 缓存各业务单元下Tag读取WebSocket地址
     */
    private static final Map<String, WebSocketConfig> CONFIG_MAP = new ConcurrentHashMap<>();

    // 缓存监控点位
    private final List<String> monitorList = new ArrayList<>();

    /**
     * websocket连接信息
     */
    public static class WebSocketConfig {
        private final String url;
        private final String loginUrl;
        private final String username;
        private final String password;

        public WebSocketConfig(String url, String loginUrl, String username, String password) {
            this.url = url;
            this.loginUrl = loginUrl;
            this.username = username;
            this.password = password;
        }

        public String getUrl() {
            return url;
        }

        public String getLoginUrl() {
            return loginUrl;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }
    }

    private TagRefreshClient(URI serverUri) {
        super(serverUri);
    }

    /**
     * 创建新的客户端
     *
     * @param segNoPrefix 业务单元前缀
     */
    public static TagRefreshClient newClient(String segNoPrefix) throws URISyntaxException {
        // 获取WebSocket配置
        WebSocketConfig config = getWebSocketConfig(segNoPrefix);
        // 创建客户端
        TagRefreshClient instance = new TagRefreshClient(new URI(config.getUrl()));
        // 生成cookie
        String cookie = instance.generateCookie(config);
        if (StrUtil.isBlank(cookie)) {
            throw new PlatException("获取cookie失败");
        }
        // 设置cookie
        instance.addHeader("Cookie", cookie);
        return instance;
    }

    /**
     * 获取BA实时报警WebSocket配置
     *
     * @param segNoPrefix 业务单元前缀
     * @return BA实时报警WebSocket配置
     */
    private static WebSocketConfig getWebSocketConfig(String segNoPrefix) {
        // 先从缓存中获取
        WebSocketConfig config = CONFIG_MAP.get(segNoPrefix);
        if (config != null) {
            return config;
        }
        // 从配置文件中获取地址前缀
        String wsUrl = PlatApplicationContext.getProperty(segNoPrefix + WEB_SOCKET_STR);
        if (StrUtil.isBlank(wsUrl)) {
            throw new PlatException(String.format(
                    "Tag读取WebSocket地址未配置，请检查配置项：%s",
                    segNoPrefix + WEB_SOCKET_STR
            ));
        }
        // 生成页面ID
        String pageId = "tempDisplays/tagRefresh.json" + StrUtil.getUUID();
        // 拼接WebSocket地址
        wsUrl = wsUrl + Base64.encodeBase64String(pageId.getBytes());
        // 新建配置信息
        config = new WebSocketConfig(
                wsUrl,
                PlatApplicationContext.getProperty(segNoPrefix + LOGIN_URL_STR),
                PlatApplicationContext.getProperty(segNoPrefix + USERNAME_STR),
                PlatApplicationContext.getProperty(segNoPrefix + PASSWORD_STR));
        // 添加至缓存
        CONFIG_MAP.put(segNoPrefix, config);
        return config;
    }

    /**
     * 获取cookie
     *
     * @param segNoPrefix 业务单元前缀
     * @return cookie
     */
    public static String getCookie(String segNoPrefix) throws URISyntaxException {
        WebSocketConfig config = getWebSocketConfig(segNoPrefix);
        return new TagRefreshClient(new URI(config.getUrl())).generateCookie(config);
    }

    /**
     * 登录以获取session
     */
    private String generateCookie(WebSocketConfig config) {
        try {
            HttpClientContext context = HttpClientContext.create();
            CookieStore cookieStore = new BasicCookieStore();
            context.setCookieStore(cookieStore);
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPost httpPost = new HttpPost(config.getLoginUrl());
                StringEntity params = new StringEntity("p_username=" + config.getUsername() + "&p_password=" + config.getPassword(), "UTF-8");
                httpPost.addHeader("content-type", "application/x-www-form-urlencoded");
                httpPost.setEntity(params);
                httpClient.execute(httpPost, context);
            }
            List<Cookie> cookies = cookieStore.getCookies();
            if (cookies != null) {
                return cookies.stream()
                        .map(cookie -> cookie.getName() + "=" + cookie.getValue())
                        .collect(Collectors.joining("; "));
            }
        } catch (Exception e) {
            LOGGER.error("登录失败", e);
        }
        return null;
    }

    /**
     * 连接成功
     * 回复：inited
     */
    @Override
    public void onOpen(ServerHandshake handshakedata) {
        LOGGER.info("Connected to WebSocket server");
        this.sendPingStr();
    }

    /**
     * 接收消息
     */
    @Override
    public void onMessage(String message) {
        LOGGER.info("Received message from server: {}", message);
        // 过滤无用消息
        if (StrUtil.isBlank(message)
                || "2#inited".equals(message)
                || "1#pong".equals(message)) {
            return;
        }
        try {
            // 数据以"4#"或"3#"开头，需截取
            message = message.substring(2);
            // 类型转换
            JSONObject jsonObject = JSONObject.parseObject(message);
            // 处理注册失败
            if (jsonObject.containsKey("registerFail")) {
                dealRegisterFail(jsonObject);
            } else {
                // 调用接口处理数据
                EiInfo info = new EiInfo();
                info.set(EiConstant.serviceName, "VGDM99");
                info.set(EiConstant.methodName, "receiveWsData");
                info.set("data", jsonObject);
                EiInfo outInfo = XLocalManager.call(info);
                if (outInfo.getStatus() < 0) {
                    LOGGER.error("数据接收出错:{}", outInfo.getMsg());
                }
            }
        } catch (Exception ex) {
            LOGGER.error("数据接收处理出错:{}", ex.getMessage());
        }
    }

    private static final String ERROR_MSG_PREFIX = "注册失败:";
    private static final String TAG_SEPARATOR = ",";
    private static final String GROUP_SEPARATOR = ";";
    private static final String KEY_VALUE_SEPARATOR = ":";

    /**
     * 处理注册失败
     */
    private void dealRegisterFail(JSONObject jsonObject) {
        // 获取失败标签信息
        JSONObject registerFail = jsonObject.getJSONObject("registerFail");
        JSONObject failedTags = registerFail.getJSONObject("failedTags");
        // 使用Stream API构建错误信息
        String errorMsg = failedTags.keySet().stream()
                .map(tagKey -> {
                    JSONArray tagArray = failedTags.getJSONArray(tagKey);
                    List<String> tagList = tagArray.toJavaList(String.class);
                    // 从监控列表移除失败标签
                    if (CollectionUtils.isNotEmpty(tagList)) {
                        monitorList.removeAll(tagList);
                    }
                    return tagKey + KEY_VALUE_SEPARATOR + String.join(TAG_SEPARATOR, tagList);
                })
                .collect(Collectors.joining(GROUP_SEPARATOR, ERROR_MSG_PREFIX, GROUP_SEPARATOR));
        // 打印错误信息
        LOGGER.error(errorMsg);
    }

    /**
     * 连接关闭
     */
    @Override
    public void onClose(int code, String reason, boolean remote) {
        LOGGER.warn("WebSocket connection closed: {" + code + "}reason:{" + reason + "}");
        // 可以尝试重连逻辑
    }

    /**
     * 错误发生
     */
    @Override
    public void onError(Exception ex) {
        LOGGER.error("WebSocket error occurred", ex);
        // 错误处理
    }

    /**
     * 发送ping消息
     * 回复：pong
     */
    public void sendPingStr() {
        this.send("{\"type\":\"ping\"}");
    }

    /**
     * tag点注册
     *
     * @param tags 标签列表,点位必需为全称如：iplat_demo.demoUse13
     */
    public void sendRegisterTags(List<String> tags) {
        if (CollectionUtils.isNotEmpty(tags)) {
            // 缓存监控点位
            monitorList.addAll(tags);
            // 发送注册标签
            this.send("{\"type\":\"registerTags\",\"content\":" + JSON.toJSONString(tags) + "}");
        }
    }

    /**
     * 暂停
     */
    public void sendPause() {
        this.send("{\"type\":\"pause\"}");
    }

    /**
     * 恢复
     */
    public void sendRestart() {
        this.send("{\"type\":\"restart\"}");
    }

    /**
     * 获取监控点位
     *
     * @return 监控点位列表
     */
    public List<String> getMonitorList() {
        return monitorList;
    }
}
