/**
 * Generate time : 2024-10-14 10:47:43
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids1101;

import java.util.Map;

/**
 * TLIDS1101
 *
 */
public class LIDS1101 extends Tlids1101 {
        public static final String QUERY = "LIDS1101.query";
        public static final String QUERY_PACK_INFO = "LIDS1101.queryPackInfo";
        public static final String COUNT = "LIDS1101.count";
        public static final String COUNT_UUID = "LIDS1101.count_uuid";
        public static final String INSERT = "LIDS1101.insert";
        public static final String UPDATE = "LIDS1101.update";
        public static final String DELETE = "LIDS1101.delete";
        public static final String DELETE_FLAG = "LIDS1101.deleteFlag";
        public static final String COUNT_CRANE_ORDER = "LIDS1101.countCraneOrder";
        public static final String QUERY_CRANE_ORDER_ID = "LIDS1101.queryCraneOrderId";
        public static final String QUERY_REPETITION_ORDER = "LIDS1101.queryRepetitionOrder";


        public static final String UPDATE_STATUS = "LIDS1101.updateStatus";
        public static final String GET_WORK_ORDER_LIST_BY_AREA = "LIDS1101.getWorkOrderListByArea";
        public static final String GET_WORK_ORDER_LIST_BY_PACK_ID = "LIDS1101.getWorkOrderListByPackId";
        public static final String QUERY_ACTIVE_CRANE_ORDERS = "LIDS1101.queryActiveCraneOrders";


        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS1101() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}