package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.li.rl.dao.LIRL0106;
import com.baosight.imom.li.rl.dao.LIRL0201;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.xservices.em.util.SmsSendManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: 张博翔
 * @Description: ${预约时段管理}
 * @Date: 2024/8/9 10:37
 * @Version: 1.0
 */
public class ServiceLIRL0106 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        /*list.add("TEST_TYPE1");
        list.add("TEST_TYPE2");
        EiInfo eiInfo = CodeValueUtils.queryToBlock("QL00000", list, inInfo);*/
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0106().eiMetadata);
        inInfo.addBlock("result2").addBlockMeta(new LIRL0106().eiMetadata);
        inInfo.addBlock("result3").addBlockMeta(new LIRL0106().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String weekDays = MapUtils.getString(queryBlock, "weekDays", "");
        if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        try {
            List<HashMap> result = new ArrayList<HashMap>();
            inInfo = super.query(inInfo, LIRL0106.QUERY);
            List<HashMap> list = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (list.size() == 0) {
                list = new ArrayList<HashMap>();
                SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
                String format = "00:00";
                Calendar cal = Calendar.getInstance();
                for (int i = 0; i < 99; i++) {
                    Date date = dateFormat.parse(format);
                    cal.setTime(date);
                    cal.add(Calendar.MINUTE, 30);
                    String format2 = dateFormat.format(cal.getTime());
                    String reservationTime = format + "-" + format2;
                    HashMap resultMap = new HashMap();
                    resultMap.put("segNo", segNo);
                    resultMap.put("segName", segName);
                    resultMap.put("unitCode", segNo);
                    resultMap.put("reservationTime", reservationTime);
                    resultMap.put("status", 10);
                    resultMap.put("delFlag", 0);
                    resultMap.put("uuid", StrUtil.getUUID());//UUID
                    RecordUtils.setCreator(resultMap);
                    list.add(resultMap);
                    LIRL0106 lirl0106 = new LIRL0106();
                    lirl0106.fromMap(resultMap);
                    dao.insert(LIRL0106.INSERT,lirl0106);
                    if ("23:30".equals(format)) {
                        break;
                    }
                    format = format2;
                }
            }
            for (int i = 0; i < 16; i++) {
                result.add(list.get(i));

            }
            outInfo.addBlock(EiConstant.resultBlock).addRows(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    public EiInfo query2(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        try {
            List<HashMap> result = new ArrayList<HashMap>();
            List<HashMap> list = this.dao.query(LIRL0106.QUERY, queryBlock);
//            List<HashMap> list = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (list.size() == 0) {
                list = new ArrayList<HashMap>();
                SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
                String format = "00:00";
                Calendar cal = Calendar.getInstance();
                for (int i = 0; i < 99; i++) {
                    Date date = dateFormat.parse(format);
                    cal.setTime(date);
                    cal.add(Calendar.MINUTE, 30);
                    String format2 = dateFormat.format(cal.getTime());
                    String reservationTime = format + "-" + format2;
                    HashMap resultMap = new HashMap();
                    resultMap.put("segNo", segNo);
                    resultMap.put("segName", segName);
                    resultMap.put("unitCode", segNo);
                    resultMap.put("reservationTime", reservationTime);
                    resultMap.put("status", 10);
                    RecordUtils.setCreator(resultMap);
                    list.add(resultMap);
                    if ("23:30".equals(format)) {
                        break;
                    }
                    format = format2;
                }
            }
            for (int i = 0; i < list.size(); i++) {
                if (i > 15 && i < 32){
                    result.add(list.get(i));
                }
            }
            outInfo.addBlock("result2").addRows(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    public EiInfo query3(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        try {
            List<HashMap> result = new ArrayList<HashMap>();
            List<HashMap> list = this.dao.query(LIRL0106.QUERY, queryBlock);
//            List<HashMap> list = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (list.size() == 0) {
                list = new ArrayList<HashMap>();
                SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
                String format = "00:00";
                Calendar cal = Calendar.getInstance();
                for (int i = 0; i < 99; i++) {
                    Date date = dateFormat.parse(format);
                    cal.setTime(date);
                    cal.add(Calendar.MINUTE, 30);
                    String format2 = dateFormat.format(cal.getTime());
                    String reservationTime = format + "-" + format2;
                    HashMap resultMap = new HashMap();
                    resultMap.put("segNo", segNo);
                    resultMap.put("segName", segName);
                    resultMap.put("unitCode", segNo);
                    resultMap.put("reservationTime", reservationTime);
                    resultMap.put("status", 10);
                    RecordUtils.setCreator(resultMap);
                    list.add(resultMap);
                    if ("23:30".equals(format)) {
                        break;
                    }
                    format = format2;
                }
            }
            for (int i = 0; i < list.size(); i++) {
                if (i > 31){
                    result.add(list.get(i));
                }
            }
            outInfo.addBlock("result3").addRows(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记

                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                hashMap.put("uuid", StrUtil.getUUID());//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0106.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            String flag = (String) inInfo.get("flag");
//            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
//            String segNo = MapUtils.getString(queryBlock, "segNo", "");
//            if (StrUtil.isBlank(segNo)) {
//                String massage = "请先选择业务单元代码！";
//                inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                inInfo.setMsg(massage);
//                return inInfo;
//            }
            if (CollectionUtils.isEmpty(listHashMap)){
                String massage = "请双击数据进行修改！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            //查询该账套是否有预约时段数据,没有就新增
            int count = 0;
            if (listHashMap.size() > 0) {
                /*for (HashMap hashMap:listHashMap){
                    String status = MapUtils.getString(hashMap, "status", "");
                    if ("10".equals(status)){
                        hashMap.put("status","20");
                    }else {
                        hashMap.put("status","10");
                    }
                }*/
                HashMap queryMap = new HashMap();
                queryMap.put("segNo", listHashMap.get(0).get("segNo"));
                count = super.count(LIRL0106.COUNT, queryMap);
            } else {
                String massage = "没有数据不能修改！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            for (HashMap hashMap : listHashMap) {
                String status = MapUtils.getString(hashMap, "status", "");
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String reservationTime = MapUtils.getString(hashMap, "reservationTime", "");//预约时段
                if ("10".equals(status)&&"20".equals(flag)){
                    //取消预约时段
                    // 如果某个时段可预约标记被取消，检查下此时段是否有预约的车辆，若有页面给与提示“此时段有几辆车预约，
                    // 确认取消？”，确认后给司机发送短信，告知“因仓库工作时间调整，您在XX年XX月XX日XX时段的预约单已被取消，请重新预约。”
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("reservationTime",reservationTime);
                    objectObjectHashMap.put("status","20");//生效
                    objectObjectHashMap.put("flag1","20");//生效
                    List<HashMap> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_HASH_MAP_ALL, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0201)){
                        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                        return inInfo;
                    }
                }else if ("10".equals(status)&&"10".equals(flag)){
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("reservationTime",reservationTime);
                    objectObjectHashMap.put("status","20");//生效
                    objectObjectHashMap.put("flag1","20");//生效
                    List<HashMap> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_HASH_MAP_ALL, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0201)){
                        //获取信息发送短信
                        for (HashMap map : queryLIRL0201) {
                           EiInfo vzbmInfo = new EiInfo();
                            String driverName = MapUtils.getString(map, "driverName");
                            String driverTel = MapUtils.getString(map, "driverTel");
                            String reservationDate = MapUtils.getString(map, "reservationDate");
                            String uuid = MapUtils.getString(map, "uuid");
                            map.put("status","00");
                            map.put("delFlag","1");
                            map.put("segNo",segNo);
                            RecordUtils.setRevisor(map);
                            //撤销预约
                            this.dao.update(LIRL0201.UPDATE_STATUS,map);
                            // 提取年份、月份和日期
                            int year = Integer.parseInt(reservationDate.substring(0, 4));
                            int month = Integer.parseInt(reservationDate.substring(4, 6)) - 1; // Java中月份从0开始
                            int day = Integer.parseInt(reservationDate.substring(6, 8));
                            // 创建LocalDate对象
                            LocalDate ld = LocalDate.of(year, month, day);
                            // 定义日期格式为"yyyy年MM月dd日"
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                            // 格式化日期
                            String formattedDate = ld.format(formatter);

//                           因仓库工作时间调整，您在XX年XX月XX日XX时段的预约单已被取消，请重新预约。
                            String content = "因仓库工作时间调整，您在"+formattedDate+reservationTime+"时段的预约单已被取消，请重新预约";
                            EiInfo outInfo = new EiInfo();
                            outInfo.set("content",content);
                            outInfo.set("mobileNum",driverTel);
                            vzbmInfo = SmsSendManager.sendMobile(outInfo);
                            if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                                //打印日志到elk
                                log(  "取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                //输出到应用日志
                                System.out.println("取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                throw new RuntimeException(vzbmInfo.toJSONString());
                            } else {
                                //打印日志到elk
                                log( "取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                //输出到应用日志
                                System.out.println("取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                            }
                        }
                    }
                }
                if (count < 1) {
                    hashMap.put("segNo", segNo);
                    hashMap.put("unitCode", segNo);
                    hashMap.put("delFlag", 0);//记录删除标记
                    hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                    hashMap.put("uuid", StrUtil.getUUID());//UUID
                    RecordUtils.setCreator(hashMap);
                } else {
                    RecordUtils.setRevisor(hashMap);
                }
            }
            if (count < 1) {
                inInfo = super.insert(inInfo, LIRL0106.INSERT);
            } else {
                inInfo = super.update(inInfo, LIRL0106.UPDATE);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update2(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
            String flag = (String) inInfo.get("flag");
//            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
//            String segNo = MapUtils.getString(queryBlock, "segNo", "");
//            if (StrUtil.isBlank(segNo)) {
//                String massage = "请先选择业务单元代码！";
//                inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                inInfo.setMsg(massage);
//                return inInfo;
//            }
            if (CollectionUtils.isEmpty(listHashMap)){
                String massage = "请双击数据进行修改！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            //查询该账套是否有预约时段数据,没有就新增
            int count = 0;
            if (listHashMap.size() > 0) {
                /*for (HashMap hashMap:listHashMap){
                    String status = MapUtils.getString(hashMap, "status", "");
                    if ("10".equals(status)){
                        hashMap.put("status","20");
                    }else {
                        hashMap.put("status","10");
                    }
                }*/
                HashMap queryMap = new HashMap();
                queryMap.put("segNo", listHashMap.get(0).get("segNo"));
                count = super.count(LIRL0106.COUNT, queryMap);
            } else {
                String massage = "没有数据不能修改！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            for (HashMap hashMap : listHashMap) {
                String status = MapUtils.getString(hashMap, "status", "");
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String reservationTime = MapUtils.getString(hashMap, "reservationTime", "");//预约时段
                if ("10".equals(status)&&"20".equals(flag)){
                    //取消预约时段
                    // 如果某个时段可预约标记被取消，检查下此时段是否有预约的车辆，若有页面给与提示“此时段有几辆车预约，
                    // 确认取消？”，确认后给司机发送短信，告知“因仓库工作时间调整，您在XX年XX月XX日XX时段的预约单已被取消，请重新预约。”
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("reservationTime",reservationTime);
                    objectObjectHashMap.put("status","20");//生效
                    objectObjectHashMap.put("flag1","20");//生效
                    List<HashMap> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_HASH_MAP, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0201)){
                        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                        return inInfo;
                    }
                }else if ("10".equals(status)&&"10".equals(flag)){
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("reservationTime",reservationTime);
                    objectObjectHashMap.put("status","20");//生效
                    objectObjectHashMap.put("flag1","20");//生效
                    List<HashMap> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_HASH_MAP, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0201)){
                        //获取信息发送短信
                        for (HashMap map : queryLIRL0201) {
                            EiInfo vzbmInfo = new EiInfo();
                            String driverName = MapUtils.getString(map, "driverName");
                            String driverTel = MapUtils.getString(map, "driverTel");
                            String reservationDate = MapUtils.getString(map, "reservationDate");
                            String uuid = MapUtils.getString(map, "uuid");
                            map.put("status","00");
                            //撤销预约
                            this.dao.update(LIRL0201.UPDATE_STATUS,map);
                            // 提取年份、月份和日期
                            int year = Integer.parseInt(reservationDate.substring(0, 4));
                            int month = Integer.parseInt(reservationDate.substring(4, 6)) - 1; // Java中月份从0开始
                            int day = Integer.parseInt(reservationDate.substring(6, 8));
                            // 创建LocalDate对象
                            LocalDate ld = LocalDate.of(year, month, day);
                            // 定义日期格式为"yyyy年MM月dd日"
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                            // 格式化日期
                            String formattedDate = ld.format(formatter);

//                           因仓库工作时间调整，您在XX年XX月XX日XX时段的预约单已被取消，请重新预约。
                            String content = "因仓库工作时间调整，您在"+formattedDate+reservationTime+"时段的预约单已被取消，请重新预约";
                            EiInfo outInfo = new EiInfo();
                            outInfo.set("content",content);
                            outInfo.set("mobileNum",driverTel);
                            vzbmInfo = SmsSendManager.sendMobile(outInfo);
                            if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                                //打印日志到elk
                                log(  "取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                //输出到应用日志
                                System.out.println("取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                throw new RuntimeException(vzbmInfo.toJSONString());
                            } else {
                                //打印日志到elk
                                log( "取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                //输出到应用日志
                                System.out.println("取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                            }
                        }
                    }
                }
                if (count < 1) {
                    hashMap.put("segNo", segNo);
                    hashMap.put("unitCode", segNo);
                    hashMap.put("delFlag", 0);//记录删除标记
                    hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                    hashMap.put("uuid", StrUtil.getUUID());//UUID
                    RecordUtils.setCreator(hashMap);
                } else {
                    RecordUtils.setRevisor(hashMap);
                }
            }
            if (count < 1) {
                inInfo = super.insert(inInfo, LIRL0106.INSERT,new LIRL0106(),true,"result2");
            } else {
                inInfo = super.update(inInfo, LIRL0106.UPDATE,new LIRL0106(),true,"result2");
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update3(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result3").getRows();
            String flag = (String) inInfo.get("flag");
//            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
//            String segNo = MapUtils.getString(queryBlock, "segNo", "");
//            if (StrUtil.isBlank(segNo)) {
//                String massage = "请先选择业务单元代码！";
//                inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                inInfo.setMsg(massage);
//                return inInfo;
//            }
            if (CollectionUtils.isEmpty(listHashMap)){
                String massage = "请双击数据进行修改！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            //查询该账套是否有预约时段数据,没有就新增
            int count = 0;
            if (listHashMap.size() > 0) {
                /*for (HashMap hashMap:listHashMap){
                    String status = MapUtils.getString(hashMap, "status", "");
                    if ("10".equals(status)){
                        hashMap.put("status","20");
                    }else {
                        hashMap.put("status","10");
                    }
                }*/
                HashMap queryMap = new HashMap();
                queryMap.put("segNo", listHashMap.get(0).get("segNo"));
                count = super.count(LIRL0106.COUNT, queryMap);
            } else {
                String massage = "没有数据不能修改！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            for (HashMap hashMap : listHashMap) {
                String status = MapUtils.getString(hashMap, "status", "");
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String reservationTime = MapUtils.getString(hashMap, "reservationTime", "");//预约时段
                if ("10".equals(status)&&"20".equals(flag)){
                    //取消预约时段
                    // 如果某个时段可预约标记被取消，检查下此时段是否有预约的车辆，若有页面给与提示“此时段有几辆车预约，
                    // 确认取消？”，确认后给司机发送短信，告知“因仓库工作时间调整，您在XX年XX月XX日XX时段的预约单已被取消，请重新预约。”
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("reservationTime",reservationTime);
                    objectObjectHashMap.put("status","20");//生效
                    objectObjectHashMap.put("flag1","20");//生效
                    List<HashMap> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_HASH_MAP, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0201)){
                        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                        return inInfo;
                    }
                }else if ("10".equals(status)&&"10".equals(flag)){
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo",segNo);
                    objectObjectHashMap.put("reservationTime",reservationTime);
                    objectObjectHashMap.put("status","20");//生效
                    objectObjectHashMap.put("flag1","20");//生效
                    List<HashMap> queryLIRL0201 = this.dao.query(LIRL0201.QUERY_HASH_MAP, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0201)){
                        //获取信息发送短信
                        for (HashMap map : queryLIRL0201) {
                            EiInfo vzbmInfo = new EiInfo();
                            String driverName = MapUtils.getString(map, "driverName");
                            String driverTel = MapUtils.getString(map, "driverTel");
                            String reservationDate = MapUtils.getString(map, "reservationDate");
                            String uuid = MapUtils.getString(map, "uuid");
                            map.put("status","00");
                            //撤销预约
                            this.dao.update(LIRL0201.UPDATE_STATUS,map);
                            // 提取年份、月份和日期
                            int year = Integer.parseInt(reservationDate.substring(0, 4));
                            int month = Integer.parseInt(reservationDate.substring(4, 6)) - 1; // Java中月份从0开始
                            int day = Integer.parseInt(reservationDate.substring(6, 8));
                            // 创建LocalDate对象
                            LocalDate ld = LocalDate.of(year, month, day);
                            // 定义日期格式为"yyyy年MM月dd日"
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                            // 格式化日期
                            String formattedDate = ld.format(formatter);

//                           因仓库工作时间调整，您在XX年XX月XX日XX时段的预约单已被取消，请重新预约。
                            String content = "因仓库工作时间调整，您在"+formattedDate+reservationTime+"时段的预约单已被取消，请重新预约";
                            EiInfo outInfo = new EiInfo();
                            outInfo.set("content",content);
                            outInfo.set("mobileNum",driverTel);
                            vzbmInfo = SmsSendManager.sendMobile(outInfo);
                            if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                                //打印日志到elk
                                log(  "取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                //输出到应用日志
                                System.out.println("取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                throw new RuntimeException(vzbmInfo.toJSONString());
                            } else {
                                //打印日志到elk
                                log( "取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                                //输出到应用日志
                                System.out.println("取消预约：短信接口传入参数：" + vzbmInfo + "\n" + "取消预约 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
                            }
                        }
                    }
                }
                if (count < 1) {
                    hashMap.put("segNo", segNo);
                    hashMap.put("unitCode", segNo);
                    hashMap.put("delFlag", 0);//记录删除标记
                    hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                    hashMap.put("uuid", StrUtil.getUUID());//UUID
                    RecordUtils.setCreator(hashMap);
                } else {
                    RecordUtils.setRevisor(hashMap);
                }
            }
            if (count < 1) {
                inInfo = super.insert(inInfo, LIRL0106.INSERT,new LIRL0106(),true,"result3");
            } else {
                inInfo = super.update(inInfo, LIRL0106.UPDATE,new LIRL0106(),true,"result3");
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0106> query = dao.query(LIRL0106.QUERY, map);
                for (LIRL0106 lirl0106 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0106);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0106.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
