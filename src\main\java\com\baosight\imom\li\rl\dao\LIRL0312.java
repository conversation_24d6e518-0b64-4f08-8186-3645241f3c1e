/**
 * Generate time : 2024-11-25 14:46:15
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0312
 *
 */
public class LIRL0312 extends DaoEPBase {
    /**
     * 查询
     */
    public static final String QUERY = "LIRL0312.query";
    public static final String DELETE = "LIRL0312.delete";
    public static final String QUERY_FILE = "LIRL0312.queryFile";
    public static final String QUERY_RELEVANCE_ID = "LIRL0312.queryRelevanceId";
    public static final String QUERY_SEG_NO_NAME = "LIRL0312.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "LIRL0312.count";
    /**
     * 新增
     */
    public static final String INSERT = "LIRL0312.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "LIRL0312.update";
    public static final String UPDATE_ATTACHMENT_PRINT = "LIRL0312.updateAttachmentPrint";
    public static final String UPDATE_RELEVANCE_ID = "LIRL0312.updateRelevanceId";

    private String relevanceType = " ";        /* 关联类型*/
    private String relevanceId = " ";        /* 关联ID*/
    private String uploadFilePath = " ";        /* 文件路径*/
    private String uploadFileName = " ";        /* 文件名称*/
    private String fifleType = " ";        /* 文件类型*/
    private BigDecimal fifleSize = new BigDecimal(0);        /* 文件大小*/
    private String fileId = " ";        /* 文件id*/
    private String uuid = " ";        /* 唯一编码*/
    private String signatureMark = " ";        /* 签名标记*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String segName = " ";        /* 业务单元名称*/
    private String attachmentPrint = " ";        /* 打印机名称*/

    /**
     * the constructor
     */
    public LIRL0312() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("relevanceType");
        eiColumn.setDescName("关联类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("relevanceId");
        eiColumn.setDescName("关联ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uploadFilePath");
        eiColumn.setDescName("文件路径");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uploadFileName");
        eiColumn.setDescName("文件名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fifleType");
        eiColumn.setDescName("文件类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fifleSize");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(9);
        eiColumn.setDescName("文件大小");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fileId");
        eiColumn.setDescName("文件id");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("signatureMark");
        eiColumn.setDescName("签名标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attachmentPrint");
        eiColumn.setDescName("打印机名称");
        eiMetadata.addMeta(eiColumn);



    }

    public String getAttachmentPrint() {
        return attachmentPrint;
    }

    public void setAttachmentPrint(String attachmentPrint) {
        this.attachmentPrint = attachmentPrint;
    }

    /**
     * get the relevanceType - 关联类型
     * @return the relevanceType
     */
    public String getRelevanceType() {
        return this.relevanceType;
    }

    /**
     * set the relevanceType - 关联类型
     */
    public void setRelevanceType(String relevanceType) {
        this.relevanceType = relevanceType;
    }

    /**
     * get the relevanceId - 关联ID
     * @return the relevanceId
     */
    public String getRelevanceId() {
        return this.relevanceId;
    }

    /**
     * set the relevanceId - 关联ID
     */
    public void setRelevanceId(String relevanceId) {
        this.relevanceId = relevanceId;
    }

    /**
     * get the uploadFilePath - 文件路径
     * @return the uploadFilePath
     */
    public String getUploadFilePath() {
        return this.uploadFilePath;
    }

    /**
     * set the uploadFilePath - 文件路径
     */
    public void setUploadFilePath(String uploadFilePath) {
        this.uploadFilePath = uploadFilePath;
    }

    /**
     * get the uploadFileName - 文件名称
     * @return the uploadFileName
     */
    public String getUploadFileName() {
        return this.uploadFileName;
    }

    /**
     * set the uploadFileName - 文件名称
     */
    public void setUploadFileName(String uploadFileName) {
        this.uploadFileName = uploadFileName;
    }

    /**
     * get the fifleType - 文件类型
     * @return the fifleType
     */
    public String getFifleType() {
        return this.fifleType;
    }

    /**
     * set the fifleType - 文件类型
     */
    public void setFifleType(String fifleType) {
        this.fifleType = fifleType;
    }

    /**
     * get the fifleSize - 文件大小
     * @return the fifleSize
     */
    public BigDecimal getFifleSize() {
        return this.fifleSize;
    }

    /**
     * set the fifleSize - 文件大小
     */
    public void setFifleSize(BigDecimal fifleSize) {
        this.fifleSize = fifleSize;
    }

    /**
     * get the fileId - 文件id
     * @return the fileId
     */
    public String getFileId() {
        return this.fileId;
    }

    /**
     * set the fileId - 文件id
     */
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    /**
     * get the uuid - 唯一编码
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the signatureMark - 签名标记
     * @return the signatureMark
     */
    public String getSignatureMark() {
        return this.signatureMark;
    }

    /**
     * set the signatureMark - 签名标记
     */
    public void setSignatureMark(String signatureMark) {
        this.signatureMark = signatureMark;
    }

    /**
     * get the recCreator - 记录创建责任者
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }


    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setRelevanceType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevanceType")), relevanceType));
        setRelevanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevanceId")), relevanceId));
        setUploadFilePath(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uploadFilePath")), uploadFilePath));
        setUploadFileName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uploadFileName")), uploadFileName));
        setFifleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("fifleType")), fifleType));
        setFifleSize(NumberUtils.toBigDecimal(StringUtils.toString(map.get("fifleSize")), fifleSize));
        setFileId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("fileId")), fileId));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setSignatureMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("signatureMark")), signatureMark));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setAttachmentPrint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attachmentPrint")), attachmentPrint));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("relevanceType", StringUtils.toString(relevanceType, eiMetadata.getMeta("relevanceType")));
        map.put("relevanceId", StringUtils.toString(relevanceId, eiMetadata.getMeta("relevanceId")));
        map.put("uploadFilePath", StringUtils.toString(uploadFilePath, eiMetadata.getMeta("uploadFilePath")));
        map.put("uploadFileName", StringUtils.toString(uploadFileName, eiMetadata.getMeta("uploadFileName")));
        map.put("fifleType", StringUtils.toString(fifleType, eiMetadata.getMeta("fifleType")));
        map.put("fifleSize", StringUtils.toString(fifleSize, eiMetadata.getMeta("fifleSize")));
        map.put("fileId", StringUtils.toString(fileId, eiMetadata.getMeta("fileId")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("signatureMark", StringUtils.toString(signatureMark, eiMetadata.getMeta("signatureMark")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("attachmentPrint", StringUtils.toString(attachmentPrint, eiMetadata.getMeta("attachmentPrint")));

        return map;

    }
}