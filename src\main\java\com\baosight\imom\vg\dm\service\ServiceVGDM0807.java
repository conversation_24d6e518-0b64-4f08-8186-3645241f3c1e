package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0807;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 月度检修项目维护
 * @Date : 2025/4/24
 * @Version : 1.0
 */
public class ServiceVGDM0807 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0807.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(MesConstant.Iplat.RESULT5_BLOCK).addBlockMeta(new VGDM0807().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        String overhaulPlanId = inInfo.getCellStr(MesConstant.Iplat.INQU5_STATUS_BLOCK, 0, "overhaulPlanId");
        if (StrUtil.isBlank(overhaulPlanId)) {
            inInfo.setCell(MesConstant.Iplat.INQU5_STATUS_BLOCK, 0, "overhaulPlanId", UUIDUtils.getUUID());
        }
        return super.query(inInfo, VGDM0807.QUERY, null, false, new VGDM0807().eiMetadata,
                MesConstant.Iplat.INQU5_STATUS_BLOCK, MesConstant.Iplat.RESULT5_BLOCK, MesConstant.Iplat.RESULT5_BLOCK);
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT5_BLOCK);
            VGDM0807 vgdm0807 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0807 = new VGDM0807();
                vgdm0807.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0807);
                Map insMap = vgdm0807.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            // 更新数据库
            DaoUtils.insertBatch(dao, VGDM0807.INSERT, block.getRows());
            // 重新排序
            this.reSortData(vgdm0807);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }


    /**
     * 数据校验
     *
     * @param vgdm0807 数据
     */
    private void checkData(VGDM0807 vgdm0807) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0807);
        vgdm0807.setActualsRemark(vgdm0807.getCheckRecord());
    }

    /**
     * 修改实绩
     */
    public EiInfo updateActuals(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT5_BLOCK);
            VGDM0807 vgdm0807;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0807 = new VGDM0807();
                vgdm0807.fromMap(block.getRow(i));
                if (StrUtil.isBlank(vgdm0807.getIsNormal())
                        || StrUtil.isBlank(vgdm0807.getActualsRemark())) {
                    throw new PlatException("是否异常或检修记录不能为空");
                }
                vgdm0807.setActualsRevisor(UserSession.getLoginName());
                vgdm0807.setActualsRevisorName(UserSession.getLoginCName());
                vgdm0807.setActualsTime(DateUtil.curDateTimeStr14());
                Map updMap = vgdm0807.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0807.UPDATE_ACTUAL, block.getRows());
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>
     * 修改点检标准状态为删除，删除标记置1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT5_BLOCK);
            VGDM0807 vgdm0807 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0807 = new VGDM0807();
                vgdm0807.fromMap(block.getRow(i));
                // 设置删除标记
                vgdm0807.setDelFlag("1");
                // 设置修改人
                Map delMap = vgdm0807.toMap();
                RecordUtils.setRevisor(delMap);
                // 数据返回前端
                block.getRows().set(i, delMap);
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0807.UPD_FOR_DEL, block.getRows());
            // 重新排序
            this.reSortData(vgdm0807);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT5_BLOCK);
            VGDM0807 vgdm0807 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0807 = new VGDM0807();
                vgdm0807.fromMap(block.getRow(i));
                this.checkData(vgdm0807);
                vgdm0807.setDelFlag("0");
                Map updMap = vgdm0807.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0807.UPDATE, block.getRows());
            // 重新排序
            this.reSortData(vgdm0807);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 重新排序
     *
     * @param vgdm0807 数据
     */
    private void reSortData(VGDM0807 vgdm0807) {
        if (vgdm0807 == null) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put("overhaulPlanId", vgdm0807.getOverhaulPlanId());
        map.put("segNo", vgdm0807.getSegNo());
        List<VGDM0807> list = dao.query(VGDM0807.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Map> updList = new ArrayList<>();
        int sortIndex = 1;
        for (VGDM0807 temp : list) {
            if (sortIndex != temp.getSortIndex()) {
                temp.setSortIndex(sortIndex);
                updList.add(temp.toMap());
            }
            sortIndex++;
        }
        DaoUtils.updateBatch(dao, VGDM0807.UPDATE, updList);
    }
}
