package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm1001;

/**
 * 设备当前作业表
 */
public class VGDM1001 extends Tvgdm1001 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM1001.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1001.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1001.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1001.update";
    /**
     * 根据设备档案删除-逻辑删除
     */
    public static final String UPDATE_BY_EQUIPMENT = "VGDM1001.updateByEquipment";
}
