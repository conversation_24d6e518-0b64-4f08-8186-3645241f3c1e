<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-05-29 9:34:18
   		Version :  1.0
		tableName :meli.tlids1301 
		 SEG_NO  VARCHAR, 
		 HOIST_TYPE  VARCHAR, 
		 HOIST_ID  VARCHAR, 
		 HOIST_WEIGHT  DECIMAL, 
		 DEL_FLAG  SMALLINT
	-->
<sqlMap namespace="LIDS1301">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.ds.domain.LIDS1301">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				HOIST_TYPE	as "hoistType",  <!-- 吊具类型 -->
				HOIST_ID	as "hoistId",  <!-- 吊具编号 -->
				HOIST_WEIGHT	as "hoistWeight",  <!-- 吊具重量 -->
				DEL_FLAG	as "delFlag" <!-- 删除标记 -->
		FROM meli.tlids1301 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hoistType">
			HOIST_TYPE = #hoistType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hoistId">
			HOIST_ID = #hoistId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hoistWeight">
			HOIST_WEIGHT = #hoistWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids1301 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hoistType">
			HOIST_TYPE = #hoistType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hoistId">
			HOIST_ID = #hoistId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hoistWeight">
			HOIST_WEIGHT = #hoistWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids1301 (SEG_NO,  <!-- 系统账套 -->
										HOIST_TYPE,  <!-- 吊具类型 -->
										HOIST_ID,  <!-- 吊具编号 -->
										HOIST_WEIGHT,  <!-- 吊具重量 -->
										DEL_FLAG  <!-- 删除标记 -->
										)		 
	    VALUES (#segNo#, #hoistType#, #hoistId#, #hoistWeight#, #delFlag#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids1301 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlids1301 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					HOIST_TYPE	= #hoistType#,   <!-- 吊具类型 -->  
					HOIST_ID	= #hoistId#,   <!-- 吊具编号 -->  
					HOIST_WEIGHT	= #hoistWeight#,   <!-- 吊具重量 -->  
					DEL_FLAG	= #delFlag#  <!-- 删除标记 -->  
			WHERE 	
	</update>
  
</sqlMap>