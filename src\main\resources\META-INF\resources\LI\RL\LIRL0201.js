$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    $("#SUB_QUERY").on("click", function (e) {
        sub_resultGrid.dataSource.page(1);
    });

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    var addsubwindow = $("#ADDSUBWINDOW");

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 100,
                pageSizes: [10,20,50,100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: true,
                    name: "后端导出",
                    sort: 2
                },
                // /**
                //  * 导出前的事件
                //  *
                //  * @param gridInstance kendoGrid对象
                //  * @return {boolean} 是否执行导出的逻辑
                //  */
                // beforeExport: (gridInstance) => {
                //     let segNo = IPLAT.EFInput.value($('#inqu_status-0-segNo'));
                //     if (IPLAT.isBlankString(segNo)) {
                //         IPLAT.alert("请选择业务单元进行查询操作");
                //         return false;
                //     }
                //     return gridInstance.getDataItems().length > 0; // false不执行导出，true时执行导出
                // },
                // exportServiceName: "LIRL0201", // 配置导出服务名，默认和grid的serviceName相同
                // exportMethodName: "postExport",    // 配置导出方法名，默认和grid的queryMethod相同
                // exportFileName: (gridInstance) => {
                //     // 导出的文件名包含时间戳 yyyyMMddHHmmss
                //     return "result" + kendo.toString(new Date(), IPLAT.FORMAT.DATE_14);
                // },
                // exportMode: "after", //导出模式
                // exportFileType: "xls",  // 默认值是xls
                // exportBlockId: "result"  // 默认值和blockId相同，导出的EiInfo中的指定数据块
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "signForAttachments",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "安全告知签章附件",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "安全告知签章附件"
                            })
                        }
                    },
                    template: function (e) {
                        return '<span style="color:blue" >查看</span>'
                    },
                },
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

                /**
                 * 大数据后端导出
                 */
                $("#EXPORTEXCEL").on("click",function () {
                    let segNo = $("#inqu_status-0-segNo").val();

                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil("请在查询条件区域内选择相应的[业务单元]！", "error");
                        return;
                    }

                    var fileName = segNo+"车辆预约单管理" + ".xlsx";

                    if(resultGrid.getDataItems().length > 0) {
                        let exportEi = new EiInfo();
                        exportEi.setByNode("inqu");
                        IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                        exportEi.set("exportColumnBlock", 'fileName', fileName);
                        IMOMUtil.callService({
                            service: "LIRL0201",
                            method: "postExport",
                            eiInfo: exportEi,
                            showProgress: true,
                            async: true,
                            callback: function (ei) {
                                if (ei.status > -1) {
                                    let docUrl = ei.getBlock("excelDoc").get("docUrl");
                                    window.open(docUrl);
                                }
                            }
                        });
                    }
                });

                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });
                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0201", "confirm", true, function (e) {
                        if (e.status==-1){
                            resultGrid.setEiInfo(e);
                        }else {
                            resultGrid.setEiInfo(e);
                            resultGrid.dataSource.page(1);
                        }
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {

            }
        },
        "sub_result3": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },
    };
    IPLATUI.EFWindow = {
        //关闭新增子项弹出框时的事件
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId").val(row[0].userNum);
                    $("#inqu_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "userNum2": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum2");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId2").val(row[0].userNum);
                    $("#inqu_status-0-customerName2").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "FILEWINDOW": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result3Grid.getDataItems().length > 0) {
                    sub_result3Grid.removeRows(sub_result3Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let reservationNumber = varModel.reservationNumber;
                    if (IPLAT.isBlankString(reservationNumber)) {
                        NotificationUtil({msg: "预约单号为空！"}, "error");
                        return;
                    }
                    $("#sub3_query_status-0-relevanceId").val(varModel.reservationNumber);
                    $("#sub3_query_status-0-driverName").val(varModel.driverName);
                    $("#sub3_query_status-0-driverTel").val(varModel.driverTel);
                    $("#sub3_query_status-0-driverIdentity").val(varModel.driverIdentity);
                    $("#sub3_query_status-0-segNo").val(varModel.segNo);
                }
                sub_result3Grid.dataSource.page(1);
            }
        },
    }

});
