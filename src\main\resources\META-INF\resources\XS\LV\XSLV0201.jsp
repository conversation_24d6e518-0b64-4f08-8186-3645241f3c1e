<!DOCTYPE html>
<%@page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage>
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-resourceEname" cname="资源英文名" placeholder="请输入资源英文名"/>
            <EF:EFInput ename="inqu_status-0-resourceCname" cname="资源中文名" placeholder="请输入资源中文名"/>
            <EF:EFInput ename="inqu_status-0-resourceType" cname="资源类型" value="RESOURCE" type="hidden"/>
            <div class="col-xs-4" style="text-align: right" id="inqu_inside"></div>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" autoDraw="no" autoBind="false">
            <EF:EFColumn ename="resourceEname" cname="资源英文名" style="text-align:left;" readonly="true" locked="true"
                         width="300"/>
            <EF:EFColumn ename="resourceCname" cname="资源中文名" style="text-align:left;" readonly="true"/>
            <EF:EFColumn ename="type" cname="资源类型" style="text-align:left;" readonly="true"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <div style="text-align: right" id="addresource"></div>

</EF:EFPage>
