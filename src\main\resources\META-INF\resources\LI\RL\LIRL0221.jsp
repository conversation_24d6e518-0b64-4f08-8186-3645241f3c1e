<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
<%--            <EF:EFDateSpan startName="inqu_status-0-reservationDateStart"--%>
<%--                           endName="inqu_status-0-reservationDateEnd"--%>
<%--                           startCname="起始预约日期" endCname="截止预约日期"--%>
<%--                           parseFormats="['yyyy-MM-ddHH:mm']"--%>
<%--                           colWidth="3" ratio="3:3"--%>
<%--                           format="yyyy-MM-dd" role="date"/>--%>
<%--            <EF:EFDateSpan startName="inqu_status-0-checkDateStart"--%>
<%--                           endName="inqu_status-0-checkDateEnd"--%>
<%--                           startCname="起始登记日期" endCname="截止登记日期"--%>
<%--                           parseFormats="['yyyy-MM-ddHH:mm']"--%>
<%--                           colWidth="3" ratio="3:3"--%>
<%--                           format="yyyy-MM-dd" role="date"/>--%>
            <EF:EFDateSpan startName="inqu_status-0-enterFactoryStart"
                           endName="inqu_status-0-enterFactoryEnd"
                           startCname="起始进厂日期" endCname="截止进厂日期"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
            <EF:EFDateSpan startName="inqu_status-0-leaveFactoryDateStart"
                           endName="inqu_status-0-leaveFactoryDateEnd"
                           startCname="起始出厂日期" endCname="截止出厂日期"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
<%--            <EF:EFInput ename="inqu_status-0-startOfTransport" cname="运输起始地" colWidth="3"/>--%>
<%--            <EF:EFInput ename="inqu_status-0-purposeOfTransport" cname="运输目的地" colWidth="3"/>--%>
                <%--<EF:EFSelect ename="inqu_status-0-reservationIdentity" cname="预约身份" optionLabel="全部" colWidth="3"
                             valueField="valueField" textField="textField"
                             template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                    <EF:EFCodeOption codeName="P002"/>
                </EF:EFSelect>--%>
<%--            <EF:EFPopupInput ename="inqu_status-0-customerId2" cname="客户代码" resizable="true" colWidth="3"--%>
<%--                             ratio="4:8"--%>
<%--                             readonly="true" backFillFieldIds="inqu_status-0-customerName2"--%>
<%--                             containerId="userNum2" popupWidth="600" pupupHeight="300" originalInput="true"--%>
<%--                             center="true"--%>
<%--                             popupTitle="客户代码查询">--%>
<%--            </EF:EFPopupInput>--%>

<%--            <EF:EFInput type="text" ename="inqu_status-0-customerName2" cname="客户代码名称" colWidth="3"--%>
<%--                        ratio="4:8" disabled="true"/>--%>
<%--            <EF:EFInput type="text" ename="inqu_status-0-carTraceNo" cname="车辆跟踪号" colWidth="3"--%>
<%--                        ratio="4:8"/>--%>
<%--            <EF:EFMultiSelect ename="inqu_status-0-status" cname="车辆跟踪单状态" optionLabel=""--%>
<%--                              valueField="valueField" textField="textField"--%>
<%--                              template="#=valueField#-#=textField#" autoClose="false"--%>
<%--                              valueTemplate="#=valueField#-#=textField#" colWidth="3">--%>
<%--                &lt;%&ndash;<EF:EFCodeOption codeName="P353" condition="ITEM_CODE != 'ZZ'"/>&ndash;%&gt;--%>
<%--                <EF:EFOption label="撤销" value="00"/>--%>
<%--&lt;%&ndash;                <EF:EFOption label="进厂登记" value="10"/>&ndash;%&gt;--%>
<%--                <EF:EFOption label="车辆进厂" value="20"/>--%>
<%--                <EF:EFOption label="开始装卸货" value="30"/>--%>
<%--                <EF:EFOption label="结束装卸货" value="40"/>--%>
<%--                <EF:EFOption label="车辆出厂" value="50"/>--%>
<%--                <EF:EFOption label="车辆签收" value="60"/>--%>
<%--            </EF:EFMultiSelect>--%>
            <EF:EFInput type="text" ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
<%--            <EF:EFInput type="text" ename="inqu_status-0-driverName" cname="司机姓名" colWidth="3"--%>
<%--                        placeholder="模糊查询"--%>
<%--                        ratio="4:8"/>--%>
            <EF:EFInput type="text" ename="inqu_status-0-allocateVehicleNo" cname="配车单号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
        </div>
        <div class="row">



<%--            <EF:EFInput type="text" ename="inqu_status-0-telNum" cname="司机手机号" colWidth="3"--%>
<%--                        placeholder="模糊查询"--%>
<%--                        ratio="4:8"/>--%>


<%--            <EF:EFSelect ename="inqu_status-0-businessType" cname="业务类型" optionLabel="全部" colWidth="3"--%>
<%--                         valueField="valueField" textField="textField"--%>
<%--                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">--%>
<%--                <EF:EFCodeOption codeName="P007"/>--%>
<%--            </EF:EFSelect>--%>
<%--            <EF:EFSelect ename="inqu_status-0-isReservation" cname="是否有预约单" optionLabel="全部" colWidth="3"--%>
<%--                         valueField="valueField" textField="textField"--%>
<%--                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">--%>
<%--                <EF:EFOption label="无预约单" value="00"/>--%>
<%--                <EF:EFOption label="有预约单" value="10"/>--%>
<%--            </EF:EFSelect>--%>
<%--            <EF:EFInput type="text" ename="inqu_status-0-voucherNum" cname="依据凭单号" colWidth="3"--%>
<%--                        placeholder="模糊查询"--%>
<%--                        ratio="4:8"/>--%>
<%--            <EF:EFInput type="text" ename="inqu_status-0-packId" cname="捆包号" colWidth="3"--%>
<%--                        placeholder="模糊查询"--%>
<%--                        ratio="4:8"/>--%>
        </div>
        <%--<div class="row">

        </div>--%>
        <%--        <div class="row">--%>
        <%--            --%>
        <%--        </div>--%>
    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="查询结果">
            <EF:EFRegion id="result" title="结果集">
                <EF:EFGrid blockId="result" autoDraw="no"
                           autoBind="false" isFloat="true" personal="true" sort="all">
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"
                                 hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
<%--                    <EF:EFComboColumn ename="isReservation" cname="是否预约" align="center" enable="false">--%>
<%--                        <EF:EFOption label="无预约" value="00"/>--%>
<%--                        <EF:EFOption label="有预约" value="10"/>--%>
<%--                    </EF:EFComboColumn>--%>
<%--                    <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" align="center" enable="false"/>--%>
<%--                    <EF:EFComboColumn ename="businessType" cname="业务类型" align="center" enable="false">--%>
<%--                        <EF:EFCodeOption codeName="P007"/>--%>
<%--                    </EF:EFComboColumn>--%>
<%--                    <EF:EFComboColumn ename="status" cname="车辆跟踪单状态" align="center" enable="false">--%>
<%--                        <EF:EFCodeOption codeName="P008"/>--%>
<%--                    </EF:EFComboColumn>--%>
                    <EF:EFColumn ename="allocateVehicleNo" cname="配车单号" align="center" enable="false"/>
                    <EF:EFComboColumn ename="allocType" cname="配单类型" align="center" enable="false">
                        <EF:EFOption label="装货配单" value="10"/>
                        <EF:EFOption label="卸货配单" value="20"/>
                        <%--                        <EF:EFOption label="废次材配单" value="30"/>--%>
                    </EF:EFComboColumn>
<%--                    <EF:EFColumn ename="reservationNumber" cname="预约单号" align="center" enable="false"/>--%>
<%--                    <EF:EFComboColumn ename="appointmentStatus" cname="预约单状态" align="center" enable="false">--%>
<%--                        <EF:EFCodeOption codeName="P006"/>--%>
<%--                    </EF:EFComboColumn>--%>

<%--                    <EF:EFColumn ename="startOfTransport" cname="运输起始地" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="purposeOfTransport" cname="运输目的地" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" enable="false"/>--%>
                    <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false"/>
<%--                    <EF:EFColumn ename="driverName" cname="司机姓名" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="telNum" cname="司机手机号" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="idCard" cname="司机身份证号" align="center" enable="false"/>--%>

<%--                    <EF:EFColumn ename="reservationDate" cname="预约日期" align="center" enable="false"--%>
<%--                                 editType="datetime" displayType="datetime" width="140"--%>
<%--                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"--%>
<%--                    />--%>
                    <EF:EFColumn ename="emergencyDeliveryTime" cname="紧急交货时间" align="center" enable="false"
                                 editType="datetime" displayType="datetime" width="180"
                                 parseFormats="['yyyyMMddHHmm']" dateFormat="yyyy/MM/dd HH:mm"
                    />
<%--                    <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>--%>
<%--                    <EF:EFComboColumn ename="lateEarlyFlag" cname="迟到早到标记" align="center" enable="false">--%>
<%--                        <EF:EFOption label="未标记" value="0"/>--%>
<%--                        <EF:EFOption label="迟到" value="10"/>--%>
<%--                        <EF:EFOption label="早到" value="20"/>--%>
<%--                        <EF:EFOption label="正常" value="30"/>--%>
<%--                    </EF:EFComboColumn>--%>
                    <EF:EFColumn ename="theLoadingTimeOfTheBundle" cname="捆包的装货时间(分钟)" align="center"
                                 enable="false" width="180"/>
                    <EF:EFColumn ename="plantTurnaroundTime" cname="厂区周转时间(分钟)" align="center" enable="false"
                                 width="132"/>
                    <EF:EFColumn ename="theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory"
                                 cname="装货完成到出厂时间(分钟)" align="center" enable="false" width="176"/>
                    <EF:EFColumn ename="theTimeFromTheSiteToTheDeliveryAddress" cname="现场到交货地的时间(分钟)"
                                 align="center" enable="false" width="174"/>
                    <EF:EFColumn ename="estimatedTimeOfArrival" cname="预计到达时间" align="center" enable="false"
                                 width="140"/>
<%--                    <EF:EFColumn ename="checkDate" cname="登记时间" align="center" enable="false"--%>
<%--                                 editType="datetime" displayType="datetime" width="140"--%>
<%--                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>--%>
<%--                    <EF:EFColumn ename="callDate" cname="叫号时间" align="center" enable="false"--%>
<%--                                 editType="datetime" displayType="datetime" width="140"--%>
<%--                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>--%>
                    <EF:EFColumn ename="enterFactory" cname="进厂时间" align="center" enable="false"
                                 editType="datetime" displayType="datetime" width="180"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>
                    <EF:EFColumn ename="beginEntruckingTime" cname="作业开始时间" align="center" enable="false"
                                 editType="datetime" displayType="datetime" width="180"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>
                    <EF:EFColumn ename="completeUninstallTime" cname="作业完成时间" align="center" enable="false"
                                 editType="datetime" displayType="datetime" width="180"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>
                    <EF:EFColumn ename="leaveFactoryDate" cname="出厂时间" align="center" enable="false"
                                 editType="datetime" displayType="datetime" width="180"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>
                    <EF:EFComboColumn ename="handType" cname="装卸类型" align="center" hidden="true"
                                      enable="false" width="120">
                        <EF:EFCodeOption codeName="P046"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="nextTarget" cname="下一目标" align="center">
                        <EF:EFOption label="下一装卸点" value="10"/>
                        <EF:EFOption label="离厂" value="20"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="targetHandPointId" cname="目标装卸点" align="center" enable="false"/>
                    <EF:EFColumn ename="targetHandPointName" cname="目标装卸点名称" align="center" enable="false"/>
                    <EF:EFColumn ename="currentHandPointId" cname="当前装卸点" align="center" enable="false"/>
                    <EF:EFColumn ename="currentHandPointName" cname="当前装卸点名称" align="center" enable="false"/>
                    <EF:EFColumn ename="factoryArea" cname="厂区编码" align="center" enable="false"/>
                    <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" enable="false"/>
<%--                    <EF:EFColumn ename="theTimeFromRegistrationToEntry" cname="登记至进厂时长(分钟)" align="center"--%>
<%--                                 enable="false" width="150" hidden="true"/>--%>
                    <EF:EFColumn ename="theTimeFromEnteringTheFactoryToTheStartOfTheJob"
                                 cname="进厂至作业开始时长(分钟)" align="center" enable="false" width="175"
                                 hidden="true"/>
                    <EF:EFColumn ename="theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity"
                                 cname="作业开始至作业完成时长(分钟)" align="center" enable="false" width="200"
                                 hidden="true"/>
                    <EF:EFColumn ename="theTimeFromTheCompletionOfTheJobToTheFactory"
                                 cname="作业完成至出厂时长(分钟)" align="center" enable="false" width="200"
                                 hidden="true"/>
                    <EF:EFColumn ename="theTimeFromEnteringTheFactoryToTheCompletionOfTheJob"
                                 cname="进厂至作业完成时长(分钟)" align="center" enable="false" width="170"
                                 hidden="true"/>
                    <EF:EFColumn ename="theTimeFromEnteringTheFactoryToLeavingTheFactory" cname="进厂至出厂时长(分钟)"
                                 align="center" enable="false" width="150" hidden="true"/>
                    <EF:EFColumn ename="registeredToTheFactoryTime" cname="登记至出厂时长(分钟)" align="center"
                                 enable="false" width="150" hidden="true"/>
<%--                    <EF:EFColumn ename="signOffTime" cname="签收时间" align="center" enable="false"--%>
<%--                                 editType="datetime" displayType="datetime" width="140"--%>
<%--                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>--%>
                    <EF:EFColumn ename="theTimeFromTheFactoryToTheTimeOfReceipt" cname="出厂至签收时长(时分)"
                                 align="center"
                                 enable="false" width="150" hidden="true"/>
<%--                    <EF:EFColumn ename="finalStationLongitude" cname="终到站经度" align="center" enable="false"--%>
<%--                                 sort="false" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="finalStationLatitude" cname="终到站纬度" align="center" enable="false"--%>
<%--                                 sort="false" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="signingLocationLongitude" cname="签收地经度" align="center" enable="false"--%>
<%--                                 sort="false" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="signingLocationLatitude" cname="签收地纬度" align="center" enable="false"--%>
<%--                                 sort="false" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="longitudeLatitudeCheck" cname="经纬度校验" align="center" enable="false"--%>
<%--                                 sort="false" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="signForAttachments" cname="签收附件" align="center" enable="false"--%>
<%--                                 sort="false"/>--%>
<%--                    <EF:EFColumn ename="signForAttachments3" cname="安全告知签章附件" align="center" enable="false"--%>
<%--                                 sort="false"--%>
<%--                                 width="126"/>--%>
                    <EF:EFColumn ename="voucherNum" cname="依凭单据号" align="center" enable="false" sort="false"/>
                    <EF:EFColumn ename="remark" cname="备注" align="center" enable="false"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="uuid" cname="ID" align="center" hidden="true"/>
                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
<%--        <div id="info-2" title="装载实绩">--%>
<%--            <EF:EFRegion id="inqu2" title="查询条件" hidden="hidden">--%>
<%--                <EF:EFInput ename="inqu2_status-0-unitCode" cname="业务单元代码" type="hidden"/>--%>
<%--                <EF:EFInput ename="inqu2_status-0-segNo" cname="系统账套" type="hidden"/>--%>
<%--                <EF:EFInput ename="inqu2_status-0-carTraceNo" cname="车辆跟踪号" type="hidden"/>--%>
<%--            </EF:EFRegion>--%>
<%--            <EF:EFRegion id="result2" title="装载实绩">--%>
<%--                <EF:EFGrid blockId="result2" autoDraw="no"--%>
<%--                           autoBind="false" isFloat="true" personal="true" sort="all"--%>
<%--                           serviceName="LIRL0308" queryMethod="queryLoadingPerformance">--%>
<%--                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>--%>
<%--                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false"/>--%>
<%--                    <EF:EFComboColumn ename="status" cname="车辆跟踪状态" align="center" hidden="true"--%>
<%--                                      enable="false" width="120">--%>
<%--                        <EF:EFCodeOption codeName="P008"/>--%>
<%--                    </EF:EFComboColumn>--%>
<%--                    <EF:EFColumn ename="driverTel" cname="司机手机号" align="center" enable="false" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="driverName" cname="司机姓名" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="driverIdentity" cname="司机身份证号" align="center" enable="false" width="150"--%>
<%--                                 hidden="true"/>--%>
<%--                    <EF:EFColumn ename="customerId" cname="客户代码" align="center" enable="false" sort="false"--%>
<%--                                 hidden="true"/>--%>
<%--                    <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" width="300" enable="false"--%>
<%--                                 sort="false"/>--%>
<%--                    <EF:EFColumn ename="putinId" cname="入库单号" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="putoutId" cname="出库单号" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="voucherNum" cname="依据凭单号" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="deliverTypeName" cname="交货方式" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="packId" cname="捆包号" align="center" enable="false" width="150" sort="false"/>--%>
<%--                    <EF:EFColumn ename="matInnerId" cname="材料管理号" align="center" enable="false" width="150"--%>
<%--                                 sort="false"/>--%>
<%--                    <EF:EFColumn ename="factoryOrderNum" cname="钢厂资源号" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFComboColumn ename="putInOutFlag" cname="出入库标记" align="center" sort="false">--%>
<%--                        <EF:EFOption label="入库" value="10"/>--%>
<%--                        <EF:EFOption label="出库" value="20"/>--%>
<%--                    </EF:EFComboColumn>--%>
<%--                    <EF:EFComboColumn ename="signatureFlag" cname="签收标记" align="center" sort="false">--%>
<%--                        <EF:EFOption label="未签收" value="0"/>--%>
<%--                        <EF:EFOption label="已签收" value="1"/>--%>
<%--                    </EF:EFComboColumn>--%>
<%--                    <EF:EFColumn ename="prodTypeId" cname="品种附属码" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="prodTypeName" cname="品种附属码名称" align="center" enable="false"--%>
<%--                                 sort="false"/>--%>
<%--                    <EF:EFColumn ename="shopsign" cname="牌号" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="specDesc" cname="规格" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="weight" cname="重量" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="quantity" cname="数量" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="warehouseName" cname="仓库名称" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="locationId" cname="库位代码" align="center" enable="false" sort="false"/>--%>
<%--                    <EF:EFColumn ename="locationName" cname="库位名称" align="center" enable="false" sort="false"/>--%>
<%--&lt;%&ndash;                    <EF:EFColumn ename="signForAttachments2" cname="签收附件" align="center" enable="false"&ndash;%&gt;--%>
<%--&lt;%&ndash;                                 sort="false"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;                    <EF:EFColumn ename="signForAttachments4" cname="安全告知签章附件" align="center" enable="false"&ndash;%&gt;--%>
<%--&lt;%&ndash;                                 sort="false"&ndash;%&gt;--%>
<%--&lt;%&ndash;                                 width="126"/>&ndash;%&gt;--%>
<%--                    <EF:EFColumn ename="remark" cname="备注" align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"--%>
<%--                                 readonly="true" align="center"/>--%>
<%--                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"--%>
<%--                                 readonly="true" align="center"/>--%>
<%--                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"--%>
<%--                                 readonly="true" align="center"/>--%>
<%--                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"--%>
<%--                                 readonly="true" align="center"/>--%>
<%--                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"--%>
<%--                                 readonly="true" align="center"/>--%>
<%--                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"--%>
<%--                                 readonly="true" align="center"/>--%>
<%--                    <EF:EFColumn ename="uuid" cname="ID" align="center" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>--%>
<%--                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>--%>
<%--                </EF:EFGrid>--%>
<%--            </EF:EFRegion>--%>
<%--        </div>--%>
    </EF:EFTab>


    <EF:EFWindow id="FILEWINDOW" url="" width="90%" height="60%" top="70%" left="170px" refresh="true" lazyload="true"
                 title="附件查询">
        <div title="附件信息查询">
            <EF:EFRegion id="sub_query" title="查询条件">
                <EF:EFInput ename="sub_query_status-0-relevanceId" cname="车辆跟踪号"
                            disabled="true"/>
                <%--<EF:EFInput ename="sub_query_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>--%>
            </EF:EFRegion>
        </div>
        <EF:EFRegion id="sub_result" title="结果集">
            <EF:EFGrid isFloat="true" blockId="sub_result" autoBind="false" autoDraw="false" serviceName="LIRL0221"
                       queryMethod="fileQuery">
                <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                <EF:EFColumn ename="relevanceId" cname="车辆跟踪单号" width="120" align="center"/>
                <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                <EF:EFColumn ename="fifleSize" cname="文件大小"/>
                <EF:EFColumn ename="fifleType" cname="文件类型"/>
                <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                <EF:EFColumn ename="recCreateTime" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
            </EF:EFGrid>
        </EF:EFRegion>

    </EF:EFWindow>

    <EF:EFWindow id="FILEWINDOW2" url="" width="90%" height="60%" top="70%" left="170px" refresh="true" lazyload="true"
                 title="附件查询">
        <div title="附件信息查询">
            <EF:EFRegion id="sub2_query" title="查询条件">
                <EF:EFInput ename="sub2_query_status-0-packId" cname="捆包号"
                            disabled="true"/>
                <%--<EF:EFInput ename="sub_query_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>--%>
            </EF:EFRegion>
        </div>
        <EF:EFRegion id="sub_result2" title="结果集">
            <EF:EFGrid isFloat="true" blockId="sub_result2" autoBind="false" autoDraw="false" serviceName="LIRL0221"
                       queryMethod="fileQuery2">
                <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                <EF:EFColumn ename="relevanceId" cname="车辆跟踪单号" width="120" align="center"/>
                <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                <EF:EFColumn ename="fifleSize" cname="文件大小"/>
                <EF:EFColumn ename="fifleType" cname="文件类型"/>
                <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                <EF:EFColumn ename="recCreateTime" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
            </EF:EFGrid>
        </EF:EFRegion>

    </EF:EFWindow>

    <EF:EFWindow id="FILEWINDOW3" url="" width="90%" height="60%" top="70%" left="170px" refresh="true" lazyload="true"
                 title="附件查询">
        <div title="附件信息查询">
            <EF:EFRegion id="sub3_query" title="查询条件">
                <EF:EFInput ename="sub3_query_status-0-relevanceId" cname="车辆跟踪号"
                            disabled="true"/>
            </EF:EFRegion>
        </div>
        <EF:EFRegion id="sub_result3" title="结果集">
            <EF:EFGrid isFloat="true" blockId="sub_result3" autoBind="false" autoDraw="false" serviceName="LIRL0221"
                       queryMethod="fileQuery3">
                <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                <EF:EFColumn ename="relevanceId" cname="车辆跟踪单号" width="120" align="center"/>
                <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                <EF:EFColumn ename="fifleSize" cname="文件大小"/>
                <EF:EFColumn ename="fifleType" cname="文件类型"/>
                <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                <EF:EFColumn ename="recCreateTime" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
            </EF:EFGrid>
        </EF:EFRegion>

    </EF:EFWindow>

    <EF:EFWindow id="FILEWINDOW4" url="" width="90%" height="60%" top="70%" left="170px" refresh="true" lazyload="true"
                 title="附件查询">
        <div title="附件信息查询">
            <EF:EFRegion id="sub4_query" title="查询条件">
                <EF:EFInput ename="sub4_query_status-0-relevanceId" cname="车辆跟踪号"
                            disabled="true"/>
            </EF:EFRegion>
        </div>
        <EF:EFRegion id="sub_result4" title="结果集">
            <EF:EFGrid isFloat="true" blockId="sub_result4" autoBind="false" autoDraw="false" serviceName="LIRL0221"
                       queryMethod="fileQuery4">
                <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                <EF:EFColumn ename="relevanceId" cname="车辆跟踪单号" width="120" align="center"/>
                <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                <EF:EFColumn ename="fifleSize" cname="文件大小"/>
                <EF:EFColumn ename="fifleType" cname="文件类型"/>
                <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                <EF:EFColumn ename="recCreateTime" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
            </EF:EFGrid>
        </EF:EFRegion>

    </EF:EFWindow>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0001" id="userNum2" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
