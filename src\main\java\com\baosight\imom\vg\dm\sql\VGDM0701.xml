<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0701">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultStatus">
            FAULT_STATUS = #faultStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="faultStatus">
            FAULT_STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="faultSource">
            FAULT_SOURCE = #faultSource#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultType">
            FAULT_TYPE = #faultType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultLevel">
            FAULT_LEVEL = #faultLevel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            DEVICE_CODE = #deviceCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultId">
            FAULT_ID like concat('%',#faultId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equalId">
            FAULT_ID = #equalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="faultStartTime">
            substr(FAULT_START_TIME,1,8) &gt;= replace(#faultStartTime#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="faultEndTime">
            replace(#faultEndTime#,'-','') &gt;= substr(FAULT_START_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startTime">
            FAULT_START_TIME like concat(#startTime#,'%')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0701">
        SELECT
        FAULT_ID as "faultId",  <!-- 故障编号 -->
        FAULT_STATUS as "faultStatus",  <!-- 故障状态 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        FAULT_START_TIME as "faultStartTime",  <!-- 故障开始时间 -->
        FAULT_END_TIME as "faultEndTime",  <!-- 故障结束时间 -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        FAULT_SOURCE as "faultSource",  <!-- 故障来源 -->
        FAULT_TYPE as "faultType",  <!-- 故障类型 -->
        FAULT_LEVEL as "faultLevel",  <!-- 故障级别 -->
        FAULT_DESC as "faultDesc",  <!-- 故障描述 -->
        HANDLE_MEASURES as "handleMeasures",  <!-- 处理措施 -->
        CAUSE_ANALYSIS as "causeAnalysis",  <!-- 原因分析 -->
        PREVENT_MEASURE as "preventMeasure",  <!-- 预防措施 -->
        IS_OVERHAUL as "isOverhaul",  <!-- 是否检修 -->
        OVERHAUL_TYPE as "overhaulType",  <!-- 检修类别 -->
        OUTSOURCING_CONTACT_ID as "outsourcingContactId",  <!-- 委外联络单号 -->
        SECURITY_MEASURES as "securityMeasures",  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA as "acceptanceCriteria",  <!-- 检修验收标准 -->
        OVERHAUL_IMPLEMENT_DATE as "overhaulImplementDate",  <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER as "actualOverhaulNumber",  <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME as "actualOverhaulTime",  <!-- 实际检修时间 -->
        IMPLEMENT_MAN_ID as "implementManId",  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME as "implementManName",  <!-- 实施人姓名 -->
        IS_COMPLETE as "isComplete",  <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT as "overhaulLegacyProject",  <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD as "isConformStandard",  <!-- 是否符合标准 -->
        RELEVANT_MEASURES as "relevantMeasures",  <!-- 相关措施 -->
        IS_HOT as "isHot",  <!-- 是否动火 -->
        HOT_CARD_ID as "hotCardId",  <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE as "offlinePartsGone",  <!-- 下线零件去向 -->
        OVERHAUL_SUGGESTIONS as "overhaulSuggestions",  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE as "overhaulSummarize",  <!-- 检修总结 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0701 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0701 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryAvgFinish" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        IFNULL(SUM(TIMESTAMPDIFF(SECOND,FAULT_START_TIME,FAULT_END_TIME)),0) as "totalSeconds",
        COUNT(*) as "count"
        FROM ${mevgSchema}.TVGDM0701 WHERE
        DEL_FLAG = '0'
        AND SEG_NO = #segNo#
        AND E_ARCHIVES_NO = #eArchivesNo#
        AND FAULT_STATUS = '40'
        AND FAULT_START_TIME like concat(#startTime#,'%')
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0701 (FAULT_ID,  <!-- 故障编号 -->
        FAULT_STATUS,  <!-- 故障状态 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        FAULT_START_TIME,  <!-- 故障开始时间 -->
        FAULT_END_TIME,  <!-- 故障结束时间 -->
        VOUCHER_NUM,  <!-- 依据凭单 -->
        FAULT_SOURCE,  <!-- 故障来源 -->
        FAULT_TYPE,  <!-- 故障类型 -->
        FAULT_LEVEL,  <!-- 故障级别 -->
        FAULT_DESC,  <!-- 故障描述 -->
        HANDLE_MEASURES,  <!-- 处理措施 -->
        CAUSE_ANALYSIS,  <!-- 原因分析 -->
        PREVENT_MEASURE,  <!-- 预防措施 -->
        IS_OVERHAUL,  <!-- 是否检修 -->
        OVERHAUL_TYPE,  <!-- 检修类别 -->
        OUTSOURCING_CONTACT_ID,  <!-- 委外联络单号 -->
        SECURITY_MEASURES,  <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA,  <!-- 检修验收标准 -->
        OVERHAUL_IMPLEMENT_DATE,  <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER,  <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME,  <!-- 实际检修时间 -->
        IMPLEMENT_MAN_ID,  <!-- 实施人 -->
        IMPLEMENT_MAN_NAME,  <!-- 实施人姓名 -->
        IS_COMPLETE,  <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT,  <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD,  <!-- 是否符合标准 -->
        RELEVANT_MEASURES,  <!-- 相关措施 -->
        IS_HOT,  <!-- 是否动火 -->
        HOT_CARD_ID,  <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE,  <!-- 下线零件去向 -->
        OVERHAUL_SUGGESTIONS,  <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE,  <!-- 检修总结 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#faultId#, #faultStatus#, #eArchivesNo#, #deviceCode#, #deviceName#, #equipmentName#, #faultStartTime#,
        #faultEndTime#,
        #voucherNum#, #faultSource#, #faultType#, #faultLevel#, #faultDesc#, #handleMeasures#, #causeAnalysis#,
        #preventMeasure#, #isOverhaul#, #overhaulType#, #outsourcingContactId#, #securityMeasures#,
        #acceptanceCriteria#, #overhaulImplementDate#, #actualOverhaulNumber#, #actualOverhaulTime#, #implementManId#,
        #implementManName#, #isComplete#, #overhaulLegacyProject#, #isConformStandard#, #relevantMeasures#, #isHot#,
        #hotCardId#, #offlinePartsGone#, #overhaulSuggestions#, #overhaulSummarize#, #uuid#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0701 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0701
        SET
        FAULT_STATUS = #faultStatus#,   <!-- 故障状态 -->
        FAULT_START_TIME = #faultStartTime#,   <!-- 故障开始时间 -->
        FAULT_END_TIME = #faultEndTime#,   <!-- 故障结束时间 -->
        FAULT_TYPE = #faultType#,   <!-- 故障类型 -->
        FAULT_LEVEL = #faultLevel#,   <!-- 故障级别 -->
        FAULT_DESC = #faultDesc#,   <!-- 故障描述 -->
        HANDLE_MEASURES = #handleMeasures#,   <!-- 处理措施 -->
        CAUSE_ANALYSIS = #causeAnalysis#,   <!-- 原因分析 -->
        PREVENT_MEASURE = #preventMeasure#,   <!-- 预防措施 -->
        IS_OVERHAUL = #isOverhaul#,   <!-- 是否检修 -->
        OVERHAUL_TYPE = #overhaulType#,   <!-- 检修类别 -->
        OUTSOURCING_CONTACT_ID = #outsourcingContactId#,   <!-- 委外联络单号 -->
        SECURITY_MEASURES = #securityMeasures#,   <!-- 安全措施 -->
        ACCEPTANCE_CRITERIA = #acceptanceCriteria#,   <!-- 检修验收标准 -->
        OVERHAUL_IMPLEMENT_DATE = #overhaulImplementDate#,   <!-- 检修实施日期 -->
        ACTUAL_OVERHAUL_NUMBER = #actualOverhaulNumber#,   <!-- 实际检修人数 -->
        ACTUAL_OVERHAUL_TIME = #actualOverhaulTime#,   <!-- 实际检修时间 -->
        IMPLEMENT_MAN_ID = #implementManId#,   <!-- 实施人 -->
        IMPLEMENT_MAN_NAME = #implementManName#,   <!-- 实施人姓名 -->
        IS_COMPLETE = #isComplete#,   <!-- 是否完成 -->
        OVERHAUL_LEGACY_PROJECT = #overhaulLegacyProject#,   <!-- 遗留检修项目 -->
        IS_CONFORM_STANDARD = #isConformStandard#,   <!-- 是否符合标准 -->
        RELEVANT_MEASURES = #relevantMeasures#,   <!-- 相关措施 -->
        IS_HOT = #isHot#,   <!-- 是否动火 -->
        HOT_CARD_ID = #hotCardId#,   <!-- 动火证编号 -->
        OFFLINE_PARTS_GONE = #offlinePartsGone#,   <!-- 下线零件去向 -->
        OVERHAUL_SUGGESTIONS = #overhaulSuggestions#,   <!-- 问题及建议 -->
        OVERHAUL_SUMMARIZE = #overhaulSummarize#,   <!-- 检修总结 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>


    <select id="queryDayFaultTime" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">

        select IFNULL(sum(t1.FAULT_END_TIME-t1.FAULT_START_TIME),0)/86400 as "faultTime"
        from mevg.tvgdm0701 t1
        where  t1.FAULT_START_TIME &gt; #faultStartTime#
        and t1.FAULT_END_TIME !=' '
        and t1.FAULT_STATUS = '40'

    </select>

</sqlMap>