package com.baosight.imom.vg.dm.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.VGDM0401;
import com.baosight.imom.vg.dm.domain.VGDM0402;
import com.baosight.imom.vg.dm.domain.VGDM0501;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 点检实绩清单页面后台
 * @Date : 2024/9/2
 * @Version : 1.0
 */
public class ServiceVGDM0402 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0402.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0402().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        // 设置查询条件-主项非新增状态
        inInfo.setCell(EiConstant.queryBlock, 0, "notNewlyStatus", "1");
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0402.QUERY_WITH_MAIN, null, false, null,
                null, null, null, VGDM0402.COUNT_WITH_MAIN);
    }

    /**
     * 修改实绩信息
     *
     * <p>未上传数据才可修改，修改后更新状态为已点检，
     * 当子项全部已点检时更新主项状态为完成，否则更新为启动。
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0402 vgdm0402;
            VGDM0402 dbData;
            // 主项id列表，用于判断更新主项状态
            List<String> checkPlanIdList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0402 = new VGDM0402();
                vgdm0402.fromMap(block.getRow(i));
                // 校验实绩信息和状态
                dbData = this.checkData(vgdm0402);
                // 更新状态为已点检
                dbData.setCheckPlanSubStatus(MesConstant.Status.K20);
                // 更新实绩操作信息
                dbData.setActualsRevisor(UserSession.getLoginName());
                dbData.setActualsRevisorName(UserSession.getLoginCName());
                dbData.setActualsTime(DateUtil.curDateTimeStr14());
                // 点检实绩来源-PC
                dbData.setActualsSource("10");
                // 异常数据生成异常联络单
                if ("1".equals(dbData.getIsNormal())) {
                    VGDM0501 vgdm0501 = new VGDM0501();
                    vgdm0501.fromMap(block.getRow(i));
                    vgdm0501.fromMap(dbData.toMap());
                    vgdm0501.setExceptionSource(vgdm0501.getSpotCheckImplemente());
                    vgdm0501.insertData(dao, true);
                    dbData.setExceptionContactId(vgdm0501.getExceptionContactId());
                }
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRow(i).putAll(updMap);
                checkPlanIdList.add(dbData.getCheckPlanId());
            }
            // 更新实绩信息
            DaoUtils.updateBatch(dao, VGDM0402.UPDATE_ACTUAL, block.getRows());
            // 获取待更新主项列表
            List<Map> updList = getMainUpdList(checkPlanIdList, true, null);
            // 更新主项状态
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE_STATUS, updList);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * PDA修改实绩信息
     *
     * <p>未上传数据才可修改，修改后更新状态为已点检，
     * 当子项全部已点检时更新主项状态为完成，否则更新为启动。
     */
    public EiInfo pdaUpdate(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            // 主项id列表，用于判断更新主项状态
            List<String> checkPlanIdList = new ArrayList<>();
            // 获取点检数据(参数已校验)
            VGDM0402 dbData = new VGDM0402();
            dbData.fromMap(block.getRow(0));
            // 主项信息
            VGDM0401 vgdm0401 = VGDM0401.queryById(dao, dbData.getCheckPlanId());
            // 更新状态为已点检
            dbData.setCheckPlanSubStatus(MesConstant.Status.K20);
            // 点检实绩来源-PDA
            dbData.setActualsSource("20");
            checkPlanIdList.add(dbData.getCheckPlanId());
            // 异常数据生成异常联络单
            if ("1".equals(dbData.getIsNormal())) {
                VGDM0501 vgdm0501 = new VGDM0501();
                vgdm0501.fromMap(dbData.toMap());
                // 设备信息
                vgdm0501.setEArchivesNo(vgdm0401.getEArchivesNo());
                vgdm0501.setEquipmentName(vgdm0401.getEquipmentName());
                // 异常来源
                vgdm0501.setExceptionSource(vgdm0501.getSpotCheckImplemente());
                // 创建人信息
                vgdm0501.setRecCreator(dbData.getActualsRevisor());
                vgdm0501.setRecCreatorName(dbData.getActualsRevisorName());
                vgdm0501.setRecCreateTime(dbData.getActualsTime());
                // 修改人信息
                vgdm0501.setRecRevisor(" ");
                vgdm0501.setRecRevisorName(" ");
                vgdm0501.setRecReviseTime(" ");
                // 插入异常联络单
                vgdm0501.insertData(dao, false);
                // 设置异常联络单id
                dbData.setExceptionContactId(vgdm0501.getExceptionContactId());
            }
            // 更新实绩信息
            dao.update(VGDM0402.UPDATE_ACTUAL, dbData.toMap());
            // 获取待更新主项列表
            List<Map> updList = getMainUpdList(checkPlanIdList, true, dbData);
            // 更新主项状态
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE_STATUS, updList);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 获取主项更新列表
     *
     * @param checkPlanIdList 主项id列表
     * @param isUpdate        是否为实绩录入
     * @return 更新列表
     */
    private List<Map> getMainUpdList(List<String> checkPlanIdList, boolean isUpdate, VGDM0402 subData) {
        List<Map> updList = new ArrayList<>();
        VGDM0401 vgdm0401;
        // 校验状态-实绩录入时判断未点检数量，撤销时校验已点检数量
        String checkPlanSubStatus = isUpdate ? MesConstant.Status.K10 : MesConstant.Status.K20;
        // 更新状态，确认时为完成，撤销时为确认
        String emptyStatus = isUpdate ? MesConstant.Status.K40 : MesConstant.Status.K20;
        for (String checkPlanId : checkPlanIdList) {
            vgdm0401 = new VGDM0401();
            vgdm0401.setCheckPlanId(checkPlanId);
            // 根据未确认的子项数量更新主项状态
            Map<String, String> countMap = new HashMap<>();
            countMap.put("checkPlanId", checkPlanId);
            countMap.put("checkPlanSubStatus", checkPlanSubStatus);
            if (super.count(VGDM0402.COUNT, countMap) > 0) {
                // 启动状态
                vgdm0401.setCheckPlanStatus(MesConstant.Status.K30);
            } else {
                // 确认时为完成状态，撤销时为确认状态
                vgdm0401.setCheckPlanStatus(emptyStatus);
            }
            if (subData != null) {
                vgdm0401.setRecRevisor(subData.getRecRevisor());
                vgdm0401.setRecRevisorName(subData.getRecRevisorName());
                vgdm0401.setRecReviseTime(subData.getRecReviseTime());
                updList.add(vgdm0401.toMap());
            } else {
                Map updMap = vgdm0401.toMap();
                RecordUtils.setRevisor(updMap);
                updList.add(updMap);
            }
        }
        return updList;
    }

    /**
     * 校验实绩信息和状态
     *
     * @param checkData 待校验数据
     * @return 数据库数据
     */
    private VGDM0402 checkData(VGDM0402 checkData) {
        if (StrUtil.isBlank(checkData.getActualsRemark())
                || StrUtil.isBlank(checkData.getIsNormal())) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_ACTUALS_EMPTY);
        }
        // 状态为未点检的数据才可修改
        VGDM0402 dbData = DaoUtils.queryAndCheckStatus(dao, checkData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NOT_CHECK_STATUS, MesConstant.Status.K10);
        // 更新数据
        dbData.setIsNormal(checkData.getIsNormal());
        dbData.setActualsRemark(checkData.getActualsRemark());
        return dbData;
    }

    /**
     * 撤销实绩信息
     * <p>
     * 状态为已点检的数据才可撤销，撤销后更新状态为未点检
     */
    public EiInfo cancel(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0402 vgdm0402;
            VGDM0402 dbData;
            // 主项id列表，用于更新主项状态
            List<String> checkPlanIdList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0402 = new VGDM0402();
                vgdm0402.fromMap(block.getRow(i));
                // 状态为已点检的数据才可撤销
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0402, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CHECK_STATUS, MesConstant.Status.K20);
                // 校验异常信息
                if (StrUtil.isNotBlank(dbData.getExceptionContactId())) {
                    throw new PlatException("已生成点检异常信息，无法撤销");
                }
                // 撤销后更新状态为未点检
                dbData.setCheckPlanSubStatus(MesConstant.Status.K10);
                // 更新实绩相关信息
                dbData.setIsNormal(" ");
                dbData.setActualsRemark(" ");
                dbData.setActualsRevisor(" ");
                dbData.setActualsRevisorName(" ");
                dbData.setActualsTime(" ");
                dbData.setActualsSource(" ");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
                // 待更新主项信息
                checkPlanIdList.add(dbData.getCheckPlanId());
            }
            // 更新子项信息
            DaoUtils.updateBatch(dao, VGDM0402.UPDATE_ACTUAL, block.getRows());
            // 获取待更新主项列表
            List<Map> updList = getMainUpdList(checkPlanIdList, false, null);
            // 更新主项状态
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE_STATUS, updList);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }
}
