<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-windowId" cname="弹窗ID" type="hidden"/>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-deviceCode" cname="分部设备代码" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-deviceName" cname="分部设备名称" placeholder="模糊条件"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="single, row" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="deviceCode" cname="分部设备代码" align="center"/>
            <EF:EFColumn ename="deviceName" cname="分部设备名称"/>
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>