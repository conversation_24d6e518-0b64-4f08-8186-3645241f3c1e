package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0808;

/**
 * 月度检修给油脂记录表
 */
public class VGDM0808 extends Tvgdm0808 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0808.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0808.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0808.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0808.update";
    /**
     * 修改
     */
    public static final String UPDATE_RECORD = "VGDM0808.updateRecord";
    /**
     * 逻辑删除
     */
    public static final String UPD_FOR_DEL = "VGDM0808.updForDel";

}
