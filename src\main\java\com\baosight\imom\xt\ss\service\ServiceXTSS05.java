package com.baosight.imom.xt.ss.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.xeye.entity.XEyeEntity;
import com.baosight.iplat4j.core.security.util.LoginRsaKeyUtil;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XEventManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.util.ExceptionUtil;
import com.baosight.imom.xt.ss.domain.XTSS05;
import com.baosight.xservices.xs.authentication.AuthenticationInfo;
import com.baosight.xservices.xs.authentication.SecurityBridgeFactory;
import com.baosight.xservices.xs.domain.XS01;
import com.baosight.xservices.xs.domain.XS02;
import com.baosight.xservices.xs.util.UserSession;
import com.baosight.xservices.xs.util.XSServiceUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 重写平台用户注册
 */
public class ServiceXTSS05  extends ServiceBase {

    private static Logger logger = LogManager.getLogger(ServiceXTSS05.class);

    private static final String defaultPassword = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.default.password"), "pwd123!@#");
    private static final String ENABLE_EHR_INFO = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.enableEhrInfo"), "on");


    /**
     * XTSS05页面查询注册用户
     * @param info
     * @return
     */
    @Override
    public EiInfo query(EiInfo info){
        EiInfo outInfo = super.query(info, "XTSS05.query", new XTSS05());
        return outInfo;
    }

    /**
     * XTSS05删除已注册用户
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        EiBlock eiBlock = inInfo.getBlock(EiConstant.resultBlock);

        for(int i = 0; i < eiBlock.getRowCount(); ++i) {
            Map<String, Object> inInfoRowMap = eiBlock.getRow(i);
            inInfoRowMap.put("recRevisor", UserSession.getUser().getUsername());
            inInfoRowMap.put("recReviseTime", DateUtils.curDateTimeStr14());
        }

        eiInfo.addBlock(inInfo.getBlock(EiConstant.resultBlock));
        eiInfo.set(EiConstant.serviceId, "S_XS_16");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return outInfo;
    }

    /**
     * xtss05更新已注册用户信息
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        EiBlock eiBlock = inInfo.getBlock(EiConstant.resultBlock);

        for(int i = 0; i < eiBlock.getRowCount(); ++i) {
            Map<String, Object> inInfoRowMap = eiBlock.getRow(i);
            inInfoRowMap.put("recRevisor", UserSession.getUser().getUsername());
            inInfoRowMap.put("recReviseTime", DateUtils.curDateTimeStr14());
        }

        eiInfo.addBlock(inInfo.getBlock(EiConstant.resultBlock));
        eiInfo.set(EiConstant.serviceId, "S_XS_17");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return outInfo;
    }

    /**
     * xtss05新注册用户
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        StringBuffer buffer = new StringBuffer();
        StringBuffer detail = new StringBuffer();
        List insertedUser = new ArrayList();
        EiBlock eiBlock = inInfo.getBlock("details");

        try {
            Map<String, Object> inInfoRowMap = eiBlock.getRow(0);
            String userId = XSServiceUtils.getUUID();
            inInfoRowMap.put("userId", userId);
            String password = (String)inInfoRowMap.get("password");
            String loginName = (String)inInfoRowMap.get("loginName");
            String userName = (String)inInfoRowMap.get("userName");
            String rePass = (String)inInfoRowMap.get("rePass");
            String mobile = (String)inInfoRowMap.get("mobile");
            String email = (String)inInfoRowMap.get("email");
            String segNo = (String)inInfoRowMap.get("segNo");
            if ("on".equals(StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.cryptoPasswordEnable"), "on"))) {
                password = LoginRsaKeyUtil.decrypt(password);
                loginName = LoginRsaKeyUtil.decrypt(loginName);
                userName = LoginRsaKeyUtil.decrypt(userName);
                rePass = LoginRsaKeyUtil.decrypt(rePass);
                segNo = LoginRsaKeyUtil.decrypt(segNo);
                inInfoRowMap.replace("loginName", loginName);
                inInfoRowMap.replace("userName", userName);
            }

            boolean flag = "".equals(segNo) ||"".equals(userName) || "".equals(loginName) || "".equals(password) || "".equals(rePass) || "".equals(mobile) || "".equals(email);
            if (flag) {
                inInfo.setStatus(-1);
                inInfo.setMsg("注册失败！业务账套号,登录账号,用户姓名，密码,确认密码,手机及邮箱信息都必须填写");
                return inInfo;
            }

            if (!rePass.equals(password)) {
                inInfo.setStatus(-1);
                inInfo.setMsg("注册失败！两次输入的密码不一致");
                return inInfo;
            }

            String userGroupEname = (String)inInfoRowMap.get("groupName");
            inInfoRowMap.put("userGroupEname", userGroupEname);
            inInfoRowMap.put("password", password);
            inInfoRowMap.put("rePass", rePass);
            inInfoRowMap.put("status", "1");
            inInfoRowMap.put("isLocked", "1");
            inInfoRowMap.put("recCreator", UserSession.getUser().getUsername());
            inInfoRowMap.put("recCreateTime", DateUtils.curDateTimeStr14());
            String accountExpireDays = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.accountExpireDays"), "90");
            String pwdExpireDays = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.pwdExpireDays"), "90");
            GregorianCalendar gc = new GregorianCalendar();
            gc.setTime(new Date());

            try {
                gc.add(5, Integer.parseInt(accountExpireDays));
            } catch (Exception var22) {
                inInfo.setStatus(-1);
                inInfo.setMsg("xservices.security.accountExpireDays参数没有配置");
                return inInfo;
            }

            inInfoRowMap.put("accountExpireDate", DateUtils.toDateStr8(gc.getTime()));
            gc.setTime(new Date());

            try {
                gc.add(5, Integer.parseInt(pwdExpireDays));
            } catch (Exception var21) {
                inInfo.setStatus(-1);
                inInfo.setMsg("xservices.security.pwdExpireDays参数没有配置");
                return inInfo;
            }

            inInfoRowMap.put("pwdExpireDate", DateUtils.toDateStr8(gc.getTime()));
            String archiveFlag = (String)inInfoRowMap.get("archiveFlag");
            if (null == archiveFlag || "".equals(archiveFlag)) {
                archiveFlag = "0";
                inInfoRowMap.put("archiveFlag", archiveFlag);
            }

            String sortIndex = (String)inInfoRowMap.get("sortIndex");
            inInfoRowMap.put("sortIndex", sortIndex);
            inInfoRowMap.put("userType", "USER");
            inInfoRowMap.put("recRevisor", " ");
            inInfoRowMap.put("recReviseTime", " ");
            inInfoRowMap.put("pwdReviseDate", " ");
            inInfoRowMap.put("pwdRevisor", " ");
            insertedUser = this.callAddUserService(inInfoRowMap);
            buffer.append("注册成功\n");
        } catch (Exception var23) {
            buffer.append("注册失败\n" + var23.getMessage());
            inInfo.setStatus(-1);
            detail.append(var23.getMessage());
            logger.error(var23.getMessage());
        }

        if (inInfo.getStatus() != -1) {
            inInfo.setStatus(1);
        }

        inInfo.setMsg(buffer.toString());
        inInfo.setDetailMsg(detail.toString());
        if (((List)insertedUser).size() > 0) {
            eiBlock.setRows((List)insertedUser);
        }

        return inInfo;
    }


    /**
     * 调用新增用户接口
     * @param inInfoRowMap
     * @return
     * @throws PlatException
     */
    private List callAddUserService(Map<String, Object> inInfoRowMap) throws PlatException {
        List insertedUser = null;
        EiInfo eiInfo = new EiInfo();
        //eiInfo.set(EiConstant.serviceId, "S_XS_15");
        EiBlock resultBlock = eiInfo.addBlock(EiConstant.resultBlock);
        resultBlock.addBlockMeta((new XTSS05()).eiMetadata);
        resultBlock.addRow(inInfoRowMap);
        EiInfo outInfo =this.insertUser(eiInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        } else {
            resultBlock = outInfo.getBlock(EiConstant.resultBlock);
            if (resultBlock != null) {
                insertedUser = resultBlock.getRows();
            } else {
                insertedUser = new ArrayList();
            }

            return (List)insertedUser;
        }
    }


    /**
     * 新增用户
     * @param inInfo
     * @return
     */
    public EiInfo insertUser(EiInfo inInfo) {
        XSServiceUtils.apiDataHandleDecorator(inInfo);
        if (inInfo.getStatus() == -1) {
            return inInfo;
        } else {
            StringBuffer buffer = new StringBuffer();
            StringBuffer detail = new StringBuffer();
            List insertedUser = new ArrayList();
            List insertedFailUser = new ArrayList();
            EiBlock eiBlock = inInfo.getBlock("result");
            String passwordMode = inInfo.getString("passwordMode");
            boolean ignoreDuplicate = "true".equals(inInfo.getString("ignoreDuplicate"));
            int rowCount = eiBlock.getRowCount();

            for(int i = 0; i < rowCount; ++i) {
                try {
                    Map<String, Object> inInfoRowMap = eiBlock.getRow(i);
                    String userId = inInfoRowMap.get("uuid") != null ? inInfoRowMap.get("uuid").toString() : (inInfoRowMap.get("userId") != null ? inInfoRowMap.get("userId").toString() : "");
                    if (StringUtils.isBlank(userId)) {
                        userId = XSServiceUtils.getUUID();
                    }

                    inInfoRowMap.put("userId", userId);
                    String password = (String)inInfoRowMap.get("password");
                    String rePass = (String)inInfoRowMap.get("rePass");
                    String loginName = (String)inInfoRowMap.get("loginName");
                    EiInfo xInfo = this.getEiInfo(inInfo, passwordMode, inInfoRowMap, password, rePass, loginName);
                    if (xInfo != null && xInfo.getStatus() < 0) {
                        return xInfo;
                    }
                    String segNo = (String)inInfoRowMap.get("segNo");
                    String userName = (String)inInfoRowMap.get("userName");
                    String recCreator = (String)inInfoRowMap.get("recCreator");
                    String userGroupEname = (String)inInfoRowMap.get("userGroupEname");
                    String isLocked = (String)inInfoRowMap.get("isLocked");
                    String gender = (String)inInfoRowMap.get("gender");
                    String mobile = (String)inInfoRowMap.get("mobile");
                    boolean flag = "".equals(userName) || "".equals(loginName) || "".equals(recCreator);
                    String accountExpireDays;
                    if (null == segNo ||null == loginName || null == userName || null == recCreator || flag) {
                        accountExpireDays = "传入的登录号，用户姓名，创建者不能为空";
                        inInfo.setStatus(-1);
                        inInfo.setMsg(accountExpireDays);
                        return inInfo;
                    }

                    if (null == userGroupEname) {
                        inInfoRowMap.put("userGroupEname", " ");
                    }

                    inInfoRowMap.put("userType", "USER");
                    inInfoRowMap.put("status", "1");
                    if (com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(recCreator)) {
                        inInfoRowMap.put("recCreator", recCreator);
                    } else {
                        inInfoRowMap.put("recCreator", UserSession.getUser().getUsername());
                    }

                    if (!com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(gender)) {
                        inInfoRowMap.put("gender", " ");
                    }

                    if (!com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(mobile)) {
                        inInfoRowMap.put("mobile", " ");
                    }

                    inInfoRowMap.put("recCreateTime", DateUtils.curDateTimeStr14());
                    accountExpireDays = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.accountExpireDays"), "90");
                    String pwdExpireDays = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.pwdExpireDays"), "90");
                    GregorianCalendar gc = new GregorianCalendar();
                    gc.setTime(new Date());
                    gc.add(5, Integer.parseInt(accountExpireDays));
                    inInfoRowMap.put("accountExpireDate", DateUtils.toDateStr8(gc.getTime()));
                    gc.setTime(new Date());
                    gc.add(5, Integer.parseInt(pwdExpireDays));
                    inInfoRowMap.put("pwdExpireDate", DateUtils.toDateStr8(gc.getTime()));
                    String archiveFlag = (String)inInfoRowMap.get("archiveFlag");
                    if (null == archiveFlag || "".equals(archiveFlag)) {
                        archiveFlag = "0";
                        inInfoRowMap.put("archiveFlag", archiveFlag);
                    }

                    String sortIndex = (String)inInfoRowMap.get("sortIndex");
                    if (StringUtils.isBlank(sortIndex)) {
                        inInfoRowMap.put("sortIndex", 0);
                    }

                    if ("-1".equals(isLocked)) {
                        inInfoRowMap.put("isLocked", "-1");
                        inInfoRowMap.put("status", "-1");
                    } else {
                        inInfoRowMap.put("isLocked", "1");
                        inInfoRowMap.put("status", "1");
                    }

                    String jobId = (String)inInfoRowMap.get("jobId");
                    if (!com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(jobId)) {
                        inInfoRowMap.put("jobId", " ");
                    }

                    String jobName = (String)inInfoRowMap.get("jobName");
                    if (!com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(jobName)) {
                        inInfoRowMap.put("jobName", " ");
                    }

                    String ehrOrgId = (String)inInfoRowMap.get("ehrOrgId");
                    if (!com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(ehrOrgId)) {
                        inInfoRowMap.put("ehrOrgId", " ");
                    }

                    inInfoRowMap.put("recRevisor", " ");
                    inInfoRowMap.put("recReviseTime", " ");
                    inInfoRowMap.put("pwdReviseDate", " ");
                    inInfoRowMap.put("pwdRevisor", " ");
                    Map paramMap = new HashMap();
                    paramMap.put("loginName", loginName);
                    List existUser = this.dao.query("XSUser.query", paramMap);
                    if (null != existUser && existUser.size() > 0) {
                        String errorMsg = "注册失败！该用户已经存在";
                        if (!ignoreDuplicate) {
                            inInfo.setStatus(-1);
                            inInfo.setMsg("注册失败！该用户已经存在");
                            return inInfo;
                        }

                        inInfoRowMap.put("failReason", errorMsg);
                        insertedFailUser.add(inInfoRowMap);
                    } else {
                        if ("on".equals(ENABLE_EHR_INFO)) {
                            this.dao.insert("XTSS05.insertWithEhrInfo", inInfoRowMap);
                        } else {
                            this.dao.insert("XTSS05.insert", inInfoRowMap);
                        }

                        Map map = new HashMap();
                        map.put("userId", userId);
                        List insertUserList = this.dao.query("XTSS05.query", map);
                        Map userMap = (Map)insertUserList.get(0);
                        insertedUser.add(userMap);
                        buffer.append("注册成功\n");
                        XEyeEntity xEyeEntity = new XEyeEntity();
                        xEyeEntity.setLogId("1000");
                        xEyeEntity.setLogName("新增用户");
                        xEyeEntity.setInvokeInfo(recCreator + "在" + DateUtils.curDateStr("yyyy-MM-dd HH:mm:ss") + "调用接口注册了登录名为 " + inInfoRowMap.get("loginName") + " 的用户信息");
                        xEyeEntity.setStatus(inInfo.getStatus() + "");
                        xEyeEntity.set("x_xs_id", inInfoRowMap.get("userId"));
                        xEyeEntity.set("x_xs_on", recCreator);
                        xEyeEntity.set("x_xs_ln", inInfoRowMap.get("loginName"));
                        this.log(xEyeEntity);
                    }
                } catch (Exception var38) {
                    buffer.append("注册失败\n").append(var38.getMessage());
                    inInfo.setStatus(-1);
                    detail.append(ExceptionUtil.getRootCauseMessage(var38));
                    logger.error(var38);
                }
            }

            inInfo.setMsg(buffer.toString());
            inInfo.setDetailMsg(detail.toString());
            if (insertedUser.size() > 0) {
                EiInfo eiInfo = new EiInfo();
                eiInfo.set("list", insertedUser);
                eiInfo.set(EiConstant.eventId, "E_XS_15");
                EiInfo outInfo = XEventManager.call(eiInfo);
                if (outInfo.getStatus() < 0) {
                    buffer.append("注册失败\n").append(outInfo.getMsg());
                    inInfo.setStatus(-1);
                    detail.append(outInfo.getDetailMsg());
                }
            }

            inInfo.set("successCount", insertedUser.size());
            inInfo.set("failCount", insertedFailUser.size());
            return inInfo;
        }
    }


    private EiInfo getEiInfo(EiInfo inInfo, String passwordMode, Map<String, Object> inInfoRowMap, String password, String rePass, String loginName) {
        if (passwordMode == null) {
            passwordMode = "";
        }

        switch (passwordMode) {
            case "BCryptEncoded":
                if (!com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(password)) {
                    inInfo.setStatus(-1);
                    inInfo.setMsg("传入的加密后的密码参数不能为空");
                    return inInfo;
                }
                break;
            case "SystemDefault":
                if (StringUtils.isNotBlank(defaultPassword)) {
                    password = defaultPassword;
                } else {
                    password = loginName;
                }

                inInfoRowMap.put("password", SecurityBridgeFactory.getSecurityPasswordEncrypt().encode(password));
                break;
            default:
                if (StringUtils.isBlank(password) || StringUtils.isBlank(rePass)) {
                    inInfo.setStatus(-1);
                    inInfo.setMsg("传入的密码，确认密码不能为空");
                    return inInfo;
                }

                if (!rePass.equals(password)) {
                    inInfo.setStatus(-1);
                    inInfo.setMsg("注册失败！两次输入的密码不一致");
                    return inInfo;
                }

                inInfoRowMap.put("password", SecurityBridgeFactory.getSecurityPasswordEncrypt().encode(password));
        }

        return null;
    }


    public EiInfo queryUserAuthInfo(EiInfo inInfo) {
        String loginNameForAuthInfo = (String)inInfo.get("inqu_status-0-loginNameForAuthInfo");
        String resourceEnameOperation = inInfo.getString("inqu_status-1-resourceEname");
        String resourceCnameOperation = inInfo.getString("inqu_status-1-resourceCname");
        boolean needFilter = StringUtils.isNotBlank(resourceEnameOperation) || StringUtils.isNotBlank(resourceCnameOperation);
        int status = 0;
        //int count = false;
        EiBlock eiBlock = null;
        List resultList = new ArrayList();
        String msg = "";

        try {
            List<Map> authList = AuthenticationInfo.getUserAuthenticationOfResourceList(loginNameForAuthInfo);
            if (needFilter) {
                String resourceEnameFilterVal = inInfo.getString("inqu_status-0-resourceEname");
                String resourceCnameFilterVal = inInfo.getString("inqu_status-0-resourceCname");
                authList = (List)authList.stream().filter((map) -> {
                    String resourceCname;
                    if (StringUtils.isNotBlank(resourceEnameOperation)) {
                        resourceCname = (String)map.get("resourceEname");
                        if (!this.filterOperate(resourceEnameOperation, resourceEnameFilterVal, resourceCname)) {
                            return false;
                        }
                    }

                    if (StringUtils.isNotBlank(resourceCnameOperation)) {
                        resourceCname = (String)map.get("resourceCname");
                        if (!this.filterOperate(resourceCnameOperation, resourceCnameFilterVal, resourceCname)) {
                            return false;
                        }
                    }

                    return true;
                }).collect(Collectors.toList());
            }

            int count = authList.size();
            int offset = 0;
            int limit = 10;
            if (inInfo.getBlock("resultE") == null) {
                eiBlock = new EiBlock("resultE");
                eiBlock.set("offset", offset);
                eiBlock.set("limit", limit);
            } else {
                eiBlock = inInfo.getBlock("resultE");
                limit = eiBlock.get("limit") == null ? 0 : (Integer)eiBlock.get("limit");
                offset = eiBlock.get("offset") == null ? 0 : (Integer)eiBlock.get("offset");
            }

            eiBlock.set("count", count);
            int authOffset = offset > count ? 0 : offset;
            int authCount = offset + limit > count ? count : offset + limit;
            if (authList != null && authList.size() > 0) {
                for(int i = authOffset; i < authCount; ++i) {
                    Map resultMap = (Map)authList.get(i);
                    if (resultMap != null) {
                        resultList.add(resultMap);
                    }
                }
            }
        } catch (Exception var18) {
            logger.error(var18.getMessage());
            status = -1;
            msg = "查询失败！";
        }

        eiBlock.setRows(resultList);
        inInfo.setBlock(eiBlock);
        inInfo.setStatus(status);
        inInfo.setMsg(msg);
        return inInfo;
    }


    private boolean filterOperate(String operateType, String conditionField, String value) {
        boolean returnVal = true;
        switch (operateType) {
            case "eq":
                returnVal = conditionField.equals(value);
                break;
            case "neq":
                returnVal = !conditionField.equals(value);
                break;
            case "contains":
                try {
                    returnVal = value.contains(conditionField);
                } catch (NullPointerException var8) {
                    returnVal = false;
                }
                break;
            default:
                returnVal = true;
        }

        return returnVal;
    }

    public EiInfo queryUserParentGroups(EiInfo inInfo) {
        if (null == inInfo.getBlock("result") && null != inInfo.getBlock("resultD")) {
            EiBlock inBlock = new EiBlock("result");
            inBlock.setAttr(inInfo.getBlock("resultD").getAttr());
            inInfo.setBlock(inBlock);
        }

        String userIdForParentGroups = (String)inInfo.get("inqu_status-0-userIdForParentGroups");
        if (!StringUtils.isNotEmpty(userIdForParentGroups)) {
            inInfo.set("inqu_status-0-userIdForParentGroups", " ");
        }

        EiInfo outInfo = super.query(inInfo, "XS01.queryUserParentGroups", new XS02());
        EiBlock eiBlock = new EiBlock("resultD");
        eiBlock.setRows(outInfo.getBlock("result").getRows());
        if (null != inInfo.getBlock("resultD")) {
            eiBlock.setAttr(inInfo.getBlock("result").getAttr());
        } else {
            eiBlock.setAttr(outInfo.getBlock("result").getAttr());
        }

        outInfo.setBlock(eiBlock);
        outInfo.setMsg("");
        return outInfo;
    }

    public EiInfo queryA(EiInfo inInfo) {
        if (null == inInfo.getBlock("result") && null != inInfo.getBlock("resultA")) {
            EiBlock inBlock = new EiBlock("result");
            inBlock.setAttr(inInfo.getBlock("resultA").getAttr());
            inInfo.setBlock(inBlock);
        }

        EiInfo outInfo = super.query(inInfo, "XS01.query", new XS01());
        EiBlock eiBlock = new EiBlock("resultA");
        eiBlock.setRows(outInfo.getBlock("result").getRows());
        if (null != inInfo.getBlock("resultA")) {
            eiBlock.setAttr(inInfo.getBlock("result").getAttr());
        } else {
            eiBlock.setAttr(outInfo.getBlock("result").getAttr());
        }

        outInfo.setBlock(eiBlock);
        outInfo.setMsg("");
        return outInfo;
    }

    public EiInfo queryGroup(EiInfo inInfo) {
        if (null == inInfo.getBlock("result") && null != inInfo.getBlock("resultB")) {
            EiBlock inBlock = new EiBlock("result");
            inBlock.setAttr(inInfo.getBlock("resultB").getAttr());
            inInfo.setBlock(inBlock);
        }

        EiInfo outInfo = super.query(inInfo, "XS02.queryGroup", new XS02());
        EiBlock eiBlock = new EiBlock("resultB");
        eiBlock.setRows(outInfo.getBlock("result").getRows());
        if (null != inInfo.getBlock("resultB")) {
            eiBlock.setAttr(inInfo.getBlock("result").getAttr());
        } else {
            eiBlock.setAttr(outInfo.getBlock("result").getAttr());
        }

        outInfo.setBlock(eiBlock);
        outInfo.setMsg("");
        return outInfo;
    }

    public int getAllRecordCount(StringBuffer buffer, Map param) {
        try {
            List aa = this.dao.query("XS01.count", param);
            int count = (Integer)aa.get(0);
            return count;
        } catch (Exception var5) {
            buffer.append(var5.getMessage()).append("\n");
            return -1;
        }
    }

}
