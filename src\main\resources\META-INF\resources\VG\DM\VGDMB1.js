$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();
    // 日期默认当天查询条件
    const now = new Date();
    $("#inqu_status-0-startDate").val(DateUtils.format(now, "yyyy-MM-dd"));
    $("#inqu_status-0-endDate").val(DateUtils.format(now, "yyyy-MM-dd"));
    // 查询按钮
    $("#QUERY1").on("click", function (e) {
        const validator = IPLAT.Validator({
            id: "inqu"
        });
        if (!validator.validate()) {
            return;
        }
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        const dataType = IPLAT.EFSelect.value($("#inqu_status-0-dataType"))
        if (IPLAT.isBlankString(dataType)) {
            NotificationUtil({msg: "报表类型不能为空!"}, "error");
            return;
        }
        const node = $("#inqu");
        IMOMUtil.submitNode(node, "VGDMB1", "createReport", function (ei) {
            let docUrl = ei.getBlock("excelDoc").get("docUrl");
            window.open(docUrl);
        });
    });
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();

    $("#QUERY").on("click", function (e) {

        var ei = new EiInfo()
        ei.setByNode("inqu");
        EiCommunicator.send("VGDMB1","query",ei,{
            onSuccess:function (ei) {
                IPLAT.Util.unbindHandlers(resultGrid.dataSource, "change");
                // 动态渲染的列需要清除原来的列配置l，否则会重复
                resultGrid.options.toolbarConfig = null;
                resultGrid._rebuild(ei);//调用rebuild方法
            }
        })

    });

/*    IPLATUI.EFGrid = {
        "result": {
            query: function () {
                var ei = new EiInfo();
                ei.setByNode("inqu");
                return ei;
            },
            loadComplete: function (grid) {
                $("#QUERY").on("click", function () {
                    grid.dataSource.page(1);
                });
            }
        }
    };*/


});
