<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-11-28 14:53:59
   		Version :  1.0
		tableName :meli.tlids0602 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 BENCHMARK_FACTORY_AREA  VARCHAR   NOT NULL, 
		 POLLING_FACTORY_AREA  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 WAREHOUSE_CODE  VARCHAR, 
		 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CROSS_REGIONAL  VARCHAR, 
		 POLLING_ACROSS_REGIONS  VARCHAR, 
		 POLLING_SCHEME_NUMBER  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR
	-->
<sqlMap namespace="tlids0602">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids0602">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				BENCHMARK_FACTORY_AREA	as "benchmarkFactoryArea",  <!-- 基准厂区 -->
				POLLING_FACTORY_AREA	as "pollingFactoryArea",  <!-- 轮询厂区 -->
				STATUS	as "status",  <!-- 状态(启用：10、停用：20) -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				BENCHMARK_CROSS_REGIONAL	as "benchmarkCrossRegional",  <!-- 基准跨区 -->
				POLLING_ACROSS_REGIONS	as "pollingAcrossRegions",  <!-- 轮询跨区 -->
				POLLING_SCHEME_NUMBER	as "pollingSchemeNumber",  <!-- 轮询方案编号 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids0602 WHERE 1=1
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0602 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="benchmarkFactoryArea">
			BENCHMARK_FACTORY_AREA = #benchmarkFactoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="pollingFactoryArea">
			POLLING_FACTORY_AREA = #pollingFactoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="benchmarkCrossRegional">
			BENCHMARK_CROSS_REGIONAL = #benchmarkCrossRegional#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="pollingAcrossRegions">
			POLLING_ACROSS_REGIONS = #pollingAcrossRegions#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="pollingSchemeNumber">
			POLLING_SCHEME_NUMBER = #pollingSchemeNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0602 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										BENCHMARK_FACTORY_AREA,  <!-- 基准厂区 -->
										POLLING_FACTORY_AREA,  <!-- 轮询厂区 -->
										STATUS,  <!-- 状态(启用：10、停用：20) -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										BENCHMARK_CROSS_REGIONAL,  <!-- 基准跨区 -->
										POLLING_ACROSS_REGIONS,  <!-- 轮询跨区 -->
										POLLING_SCHEME_NUMBER,  <!-- 轮询方案编号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #benchmarkFactoryArea#, #pollingFactoryArea#, #status#, #warehouseCode#, #benchmarkCrossRegional#, #pollingAcrossRegions#, #pollingSchemeNumber#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0602 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlids0602 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					BENCHMARK_FACTORY_AREA	= #benchmarkFactoryArea#,   <!-- 基准厂区 -->  
					POLLING_FACTORY_AREA	= #pollingFactoryArea#,   <!-- 轮询厂区 -->  
					STATUS	= #status#,   <!-- 状态(启用：10、停用：20) -->  
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库代码 -->  
					BENCHMARK_CROSS_REGIONAL	= #benchmarkCrossRegional#,   <!-- 基准跨区 -->  
					POLLING_ACROSS_REGIONS	= #pollingAcrossRegions#,   <!-- 轮询跨区 -->  
					POLLING_SCHEME_NUMBER	= #pollingSchemeNumber#,   <!-- 轮询方案编号 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					UUID	= #uuid#  <!-- ID -->  
			WHERE 	
	</update>
  
</sqlMap>