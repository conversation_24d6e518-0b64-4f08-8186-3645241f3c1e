<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0401">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanStatus">
            CHECK_PLAN_STATUS = #checkPlanStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanId">
            CHECK_PLAN_ID like concat('%',#checkPlanId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0401">
        SELECT
        CHECK_PLAN_ID as "checkPlanId",  <!-- 点检计划主项号 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        CHECK_PLAN_DATE as "checkPlanDate",  <!-- 点检日期 -->
        CHECK_PLAN_STATUS as "checkPlanStatus",  <!-- 点检计划主档状态 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        DELAY_REMARK as "delayRemark"  <!-- 延期备注 -->
        FROM ${mevgSchema}.TVGDM0401 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                CHECK_PLAN_DATE desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0401 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryById" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0401">
        SELECT
        CHECK_PLAN_ID as "checkPlanId",  <!-- 点检计划主项号 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        CHECK_PLAN_DATE as "checkPlanDate",  <!-- 点检日期 -->
        CHECK_PLAN_STATUS as "checkPlanStatus",  <!-- 点检计划主档状态 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        DELAY_REMARK as "delayRemark"  <!-- 延期备注 -->
        FROM ${mevgSchema}.TVGDM0401 WHERE
        CHECK_PLAN_ID = #checkPlanId#
        AND DEL_FLAG = '0'
    </select>

    <select id="queryTodayEquipment" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT t.E_ARCHIVES_NO as "eArchivesNo", MAX(t.EQUIPMENT_NAME) as "equipmentName", MAX(t.UUID) as "uuid"
        FROM MEVG.TVGDM0401 t
        left join mevg.tvgdm0101 t1 on t.E_ARCHIVES_NO = t1.E_ARCHIVES_NO
        WHERE 1=1
        AND t.SEG_NO = #segNo#
        AND t.CHECK_PLAN_DATE = #checkPlanDate#
        AND t.DEL_FLAG = '0'
        AND t.CHECK_PLAN_STATUS in ('20','30')
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            t.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            t1.FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        GROUP BY t.E_ARCHIVES_NO
        ORDER BY t.E_ARCHIVES_NO
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0401 (CHECK_PLAN_ID,  <!-- 点检计划主项号 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        CHECK_PLAN_DATE,  <!-- 点检日期 -->
        CHECK_PLAN_STATUS,  <!-- 点检计划主档状态 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        SPOT_CHECK_NATURE,  <!-- 点检性质 -->
        DELAY_REMARK  <!-- 延期备注 -->
        )
        VALUES (#checkPlanId#, #eArchivesNo#, #equipmentName#, #checkPlanDate#, #checkPlanStatus#, #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #spotCheckNature#, #delayRemark#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0401 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0401
        SET
        CHECK_PLAN_ID = #checkPlanId#,   <!-- 点检计划主项号 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        CHECK_PLAN_DATE = #checkPlanDate#,   <!-- 点检日期 -->
        CHECK_PLAN_STATUS = #checkPlanStatus#,   <!-- 点检计划主档状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        SPOT_CHECK_NATURE = #spotCheckNature#,  <!-- 点检性质 -->
        DELAY_REMARK = #delayRemark#  <!-- 延期备注 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateStatus">
        UPDATE ${mevgSchema}.TVGDM0401
        SET
        CHECK_PLAN_STATUS = #checkPlanStatus#,   <!-- 点检计划主档状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        CHECK_PLAN_ID = #checkPlanId#
    </update>

    <update id="updateByDay">
        UPDATE ${mevgSchema}.TVGDM0401
        SET
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE DEL_FLAG = '0'
        AND CHECK_PLAN_DATE = #checkPlanDate#
        AND E_ARCHIVES_NO = #eArchivesNo#
    </update>

</sqlMap>