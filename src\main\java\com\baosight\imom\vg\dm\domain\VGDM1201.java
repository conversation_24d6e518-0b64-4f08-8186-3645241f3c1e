package com.baosight.imom.vg.dm.domain;


import com.baosight.imom.common.vg.domain.Tvgdm1201;

import java.util.Map;

/**
 * 年度加工量/达产率维护表
 */
public class VGDM1201 extends Tvgdm1201 {

    /**
     * 查询
     */
    public static final String QUERY = "VGDM1201.query";

    public static final String QUERY_VGDM1202 = "VGDM1201.queryVgdm1202";

    /**
     * 查询大屏参数
     */
    public static final String QUERY_FOR_SCREEN = "VGDM1201.queryForScreen";

    /**
     * 查询大屏参数(所有)
     */
    public static final String QUERY_FOR_SCREEN_TWO = "VGDM1201.queryForScreenTwo";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1201.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1201.insert";
    public static final String INSERT_VGDM1202 = "VGDM1201.insertVgdm1202";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1201.update";
    public static final String UPDATE_VGDM1202 = "VGDM1201.updateVgdm1202";
    /**
     * 修改
     */
    public static final String UPDATE_STATUS = "VGDM1201.updateStatus";
    public static final String UPDATE_STATUS_VGDM1202 = "VGDM1201.updateStatusVgdm1202";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public VGDM1201() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}
