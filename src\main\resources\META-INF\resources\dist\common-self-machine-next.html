<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css">
    <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />
</head>

<body>
<div class="wrapper">
    <div class="header">
        <div id="logo" class="logo-baosight"></div>
        <div class="header-return">
            <button class="return-home-btn" onclick="returnHome()">返回主页</button>
        </div>
    </div>
    <div class="nav">
        <div class="navbox">
            <ul>
                <li class="fontblue">1.登记身份证</li>
                <li id="xzyw1" class="arrow"></li>
                <li id="xzyw">2.选择业务</li>
                <li class="arrow"></li>
                <li id="jcdj">3.进厂登记</li>
            </ul>
        </div>
    </div>
    <div class="container">
        <div class="main">
            <div class="information3-zdy" style="height: 220px;">
                <ul>
                    <li>
                        <span>身份证</span>
                        <div class="ipt-div-btn">
                            <input id="idCard" type="text" class="ipt1 id-card" readonly />
                        </div>
                    </li>
                    <li>
                        <span>姓名</span>
                        <div class="ipt-div-btn">
                            <input id="idName" type="text" class="ipt1 id-name">
                        </div>

                    </li>
                    <li><span>厂&nbsp;&nbsp;&nbsp;&nbsp;区</span>
                        <div class="ipt-div-btn">
                            <input id="factory_id" class="ipt2" readonly="readonly" />
                        </div>
                    </li>
                </ul>
                <div class="div-wxts">
                    <p class="div-p-wxts" style="color: #FF0000;">*</p>温馨提示：请您扫描 <p class="div-p-wxts">身份证</p>，如果您没有携带
                    <p class="div-p-wxts">身份证</p>，也可以手动输入相关信息。
                </div>
            </div>
            <div id="keyboard" style="display: none;"></div>

            <div class="btn2">
                <button id="indexRead"  class="btn2-flex-item">读取身份证</button>
                <button class="btn2-flex-item" onclick="javascript :history.back(-1);">上一步</button>
                <button class="btn2-flex-item" onclick="nextStep();">下一步</button>
            </div>
        </div>
    </div>

</div>


<script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="js/<EMAIL>"></script>
<script type="text/javascript" src="js/config.js"></script>
<script>
    var segNo = '';
    var factoryId = '';
    var factoryName = '';

    //页面加载将车牌号与装卸货填充
    window.onload = function() {
        const urlObj = new URLSearchParams(window.location.search);
        segNo = urlObj.get('segNo');
        factoryId = urlObj.get('factoryId');
        factoryName = urlObj.get('factoryName');
        localStorage.setItem('segNo', segNo);
        localStorage.setItem('factoryId', factoryId);
        localStorage.setItem('factoryName', factoryName);
        $('#factory_id').val(factoryName);

        const toUrlType = localStorage.getItem("type");
        let eleLi = $('#jcdj')[0];
        switch (toUrlType) {
            case '0':
                eleLi.innerText = '3.进厂登记';
               break;
            case '1':
                eleLi.innerText = '2.出库单打印';
                $('#xzyw1')[0].style.display = 'none';
                $('#xzyw')[0].style.display = 'none';
                break;
            default:
                eleLi.innerText = '2.提单打印';
                $('#xzyw1')[0].style.display = 'none';
                $('#xzyw')[0].style.display = 'none';
                break;
        }
    }
    //下一步
    function nextStep() {
        const idCardValue = $("#idCard").val();
        const idNameValue = $('#idName').val();
        //车牌框不能为空
        if (!idCardValue) {
            Swal.fire({
                title: '请输入身份证号',
                icon: 'warning',
                confirmButtonText: '确定'
            });
            return;
        }

        const idReg = /^[1-9]\d{5}(18|19|20|21|22)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|[Xx])$/;
        if (!idReg.test(idCardValue)) {
            Swal.fire({
                title: '请输入正确的身份证',
                icon: 'warning',
                confirmButtonText: '确定'
            });
            return;
        }

        localStorage.setItem('idCard', idCardValue);
        localStorage.setItem('idName', idNameValue);
        if (localStorage.getItem("type") == '0'){
            window.location.href = "selfServiceMachineNext.html";
        }else if (localStorage.getItem("type") == '1'){
            window.location.href = "Outbound-print.html";
        }else{
            window.location.href = "lading-print.html";
        }

    }
    let queryData = {
        segNo: localStorage.getItem('segNo'),
    };
    $("#indexRead").bind("click", function() {
        const picpath = encodeURIComponent("D:/1/123.bmp"); // 替换路径
        const url = `http://127.0.0.1:19196/readcard&picpath=${picpath}`;
        showLoading('扫描身份证中');
        const xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.onreadystatechange = function () {
            closeLoading();
            console.log(xhr,'返回数据');
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    const obj = JSON.parse(xhr.responseText);
                    const { partyName, certNumber } = obj;
                    $("#idCard").val(certNumber);
                    $("#idName").val(partyName);
                } else {
                    Swal.fire({
                        title: '读取身份证失败',
                        text: xhr.statusText,
                        icon: 'error',
                        confirmButtonText: '确定'
                    });
                }
                return;
            }

            Swal.fire({
                title: '网络异常, 请稍后重试',
                icon: 'error',
                confirmButtonText: '确定'
            });
        };
        xhr.send();
    });

    $("#idName").bind("click", function() {
        document.querySelectorAll('.id-name').forEach(function(ipt2) {
            ipt2.focus();
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        const keys = [
            '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'X',
            '退格', '清空'
        ];

        const keyboard = document.getElementById('keyboard');
        let activeInputField = null;

        keys.forEach(function(key) {
            const keyElement = document.createElement('div');
            keyElement.className = 'key';
            keyElement.textContent = key;
            keyboard.appendChild(keyElement);

            keyElement.addEventListener('click', function() {
                if (!activeInputField) { return; }
                if (key === '退格') {
                    activeInputField.value = activeInputField.value.slice(0, -1);
                    return;
                }
                if (key === '清空') {
                    activeInputField.value = '';
                    return;
                }

                activeInputField.value += key;
                activeInputField.focus();  // 确保输入框保持焦点

            });
        });

        document.querySelectorAll('.id-card').forEach(function(ipt2) {
            ipt2.addEventListener('focus', function() {
                activeInputField = ipt2;
                const rect = ipt2.getBoundingClientRect();
                keyboard.style.display = 'flex';
                keyboard.style.top = rect.bottom + window.scrollY + 'px';
                keyboard.style.left = rect.left + window.scrollX + 'px';
            });
        });

        // 点击其他地方隐藏小键盘
        document.addEventListener('click', function(event) {
            if (!keyboard.contains(event.target) && !event.target.classList.contains('id-card')) {
                keyboard.style.display = 'none';
                activeInputField = null;
            }
        });

        // 防止点击键盘时触发隐藏
        keyboard.addEventListener('click', function(event) {
            event.stopPropagation();
        });
    });
</script>
</body>

</html>