$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //厂区厂房弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-factoryBuilding").val(rows[0].factoryBuilding);
                $("#inqu_status-0-factoryBuildingName").val(rows[0].factoryBuildingName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });

    // 设备档案查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 分部设备检修配置修改
    $("#UPDATE1").on("click", function (e) {
        setDetailEdit(true);
    });
    // 分部设备检修配置保存
    $("#SAVE1").on("click", function (e) {
        const validator = IPLAT.Validator({
            id: "detail"
        });
        if (!validator.validate("__none__")) {
            return;
        }
        const cycleType = $("#detail-0-overhaulCycleType").val();
        if (cycleType === "10") {
            if (!validator.validate("group1")) {
                return;
            }
        }
        if (cycleType === "20") {
            if (!validator.validate("group2")) {
                return;
            }
        }
        const node = $("#detail");
        IMOMUtil.submitNode(node, "VGDM0105", "save", function (ei) {
            IPLAT.fillNode(document.getElementById("detail"), ei);
            setDetailEdit(false);
        });
    });
    IPLATUI.EFSelect = {
        // 文件类型下拉框
        "inqu2_status-0-fifleType": {
            // 下拉选项改变之后触发
            change: function (e) {
                // 重新查询
                result3Grid.dataSource.page(1);
            }
        },
        // 检修周期下拉框
        "detail-0-overhaulCycleType": {
            select: function (e) {
                // 检修周期改变
                onOverhaulCycleTypeChange(e.dataItem.valueField);
            }
        }
    };
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                var tableId = e.contentElement.id;
                if (tableId !== "info-1") {
                    var checkedRows = resultGrid.getCheckedRows();
                    var checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "请勾选一条设备档案信息"}, "error");
                        e.preventDefault(); //阻止切换第二个tab
                    } else {
                        var checkedRow = checkedRows[0];
                        $("#inqu-0-eArchivesNo").val(checkedRow.eArchivesNo);
                        $("#inqu2_status-0-eArchivesNo").val(checkedRow.eArchivesNo);
                        // 查询分部设备
                        if (tableId === "info-2") {
                            result2Grid.dataSource.page(1);
                            setDetailEdit(false);
                            // 修改按钮禁用
                            $("#UPDATE1").attr("disabled", true);
                        }
                        // 查询设备文档
                        if (tableId === "info-3") {
                            result3Grid.dataSource.page(1);
                        }
                    }
                }
            }
        }
    };
    IPLATUI.EFGrid = {

        "result": {
                loadComplete: function (grid) {
                // 批量赋值
                $("#UPDATEDATE").on("click", function (e) {

                    var factoryBuilding = $("#inqu_status-0-factoryBuilding").val();
                    var factoryBuildingName = $("#inqu_status-0-factoryBuildingName").val();
                    if (!factoryBuilding) {
                        NotificationUtil("操作失败，原因[请先选择厂房！]", "error");
                        e.preventDefault();
                        return;
                    }
                    var checkedRows = resultGrid.getCheckedRows();
                    var checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "请勾选一条设备档案信息"}, "error");
                        e.preventDefault(); //阻止切换第二个tab
                    }

                    const rowNums = resultGrid.getCheckedRowsIndex();
                    const updateValues = {
                        factoryBuilding: $("#inqu_status-0-factoryBuilding").val(),
                        factoryBuildingName: $("#inqu_status-0-factoryBuildingName").val(),
                    };
                    Object.entries(updateValues).forEach(([key, value]) => {
                        resultGrid.setCellValue(rowNums, key, value);
                    });
                });
                    // 修改设备
                    $("#UPDATE").on("click", function (e) {
                        if (!IMOMUtil.checkSelected(resultGrid)) {
                            return;
                        }
                        IMOMUtil.submitGridsData("result", "VGDM01", "update", true, null, null, false);
                    });
            }
        },

        // 分部设备
        "result2": {
            pageable: {
                pageSize: 20//单页展示20条数据
            },
            /**
             * 点击行首checkbox，勾选行时触发的事件
             * @param e     kendo的Event对象
             * e.sender     kendoGrid对象，resultGrid
             * e.fake       用于区分是手动点击的事件还是模拟的事件
             * e.checked    用于区分是勾选还是取消勾选
             * e.model      勾选或取消勾选的行数据，kendo.data.Model
             * e.row        当前行的行号
             * e.tr         行的tr,包括固定列和数据列 jquery对象
             */
            onCheckRow: function (e) {
                if (!e.fake && e.checked) {
                    $("#detail-0-deviceCode").val(e.model.deviceCode);
                    queryDetail();
                }
            }
        },
        // 设备文档
        "result3": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                },
                {
                    field: "fifleSize",
                    template: function (e) {
                        const kbSize = e.fifleSize / 1024;
                        if (kbSize < 1024) {
                            return kbSize.toFixed(2) + " KB";
                        } else {
                            return (kbSize / 1024).toFixed(2) + " MB";
                        }
                    }
                }
            ],
            loadComplete: function (grid) {
                // 附件上传按钮
                $("#FILEUPLOAD").click(function () {
                    var fileType = IPLAT.EFSelect.value($("#inqu2_status-0-fifleType"));
                    if (IPLAT.isBlankString(fileType)) {
                        NotificationUtil({msg: "请选择文件类型"}, "error");
                        return;
                    }
                    $("#fileForm").click();
                });
            }
        },
        // 设备文档历史版本
        "result4": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                },
                {
                    field: "fifleSize",
                    template: function (e) {
                        const kbSize = e.fifleSize / 1024;
                        if (kbSize < 1024) {
                            return kbSize.toFixed(2) + " KB";
                        } else {
                            return (kbSize / 1024).toFixed(2) + " MB";
                        }
                    }
                }
            ],
            loadComplete: function (grid) {
                // 历史版本按钮
                $("#FILEVERSION").click(function () {
                    const checkedRows = result3Grid.getCheckedRows();
                    if (checkedRows.length !== 1) {
                        NotificationUtil({msg: "请勾选一条设备文档信息"}, "error");
                        return;
                    }

                    const checkedRow = checkedRows[0];
                    const {eArchivesNo, uploadFileName, fifleType} = checkedRow;

                    $("#inqu1_status-0-eArchivesNo").val(eArchivesNo);
                    $("#inqu1_status-0-uploadFileName").val(uploadFileName);
                    $("#inqu1_status-0-fifleType").val(fifleType);

                    result4Grid.dataSource.page(1);
                    popDataWindow.open().center();
                });
            }
        }
    };

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();

    // 检修周期改变
    function onOverhaulCycleTypeChange(value) {
        if (!value) {
            value = $("#detail-0-overhaulCycleType").val();
        }
        if (value === "10") {
            $("#dayDiv").show();
            $("#numDiv").hide();
        } else {
            $("#dayDiv").hide();
            $("#numDiv").show();
        }
    }

    // 查询分部设备检修配置信息
    function queryDetail() {
        // 设置详情区域修改状态
        setDetailEdit(false);
        const deviceCode = $("#detail-0-deviceCode").val();
        // 清除原数据
        const detailNode = document.getElementById("detail");
        IPLAT.clearNode(detailNode);
        $("#detail-0-deviceCode").val(deviceCode);
        // 当前选中分部设备信息
        const eiInfo = new EiInfo();
        eiInfo.set("deviceCode", deviceCode);
        // 查询分部设备检修配置信息
        IMOMUtil.submitEiInfo(eiInfo, "VGDM0105", "query", function (ei) {
            // 填充分部设备检修配置信息
            IPLAT.fillNode(detailNode, ei);
            // 检修周期改变
            onOverhaulCycleTypeChange();
        });
    }

    // 设置详情区域修改状态
    function setDetailEdit(isEdit) {
        IPLAT.EFSelect.enable($("#detail-0-overhaulCycleType"), isEdit);
        $("#detail-0-cycleDay").attr("readonly", !isEdit);
        $("#detail-0-cycleNum").attr("readonly", !isEdit);
        $("#detail-0-overhaulTool").attr("readonly", !isEdit);
        $("#detail-0-overhaulStep").attr("readonly", !isEdit);
        $("#detail-0-securityMeasures").attr("readonly", !isEdit);
        // 修改按钮
        $("#UPDATE1").attr("disabled", isEdit);
        // 保存按钮
        $("#SAVE1").attr("disabled", !isEdit);
    }

    // 附件上传相关
    var eArchivesNo = "";
    var fifleType = "";
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "VGDM01",
        methodName: "fileUpload",
        callback: function (e) {
            result3Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "VGDM01" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                eArchivesNo +
                "&id2=" +
                fifleType;
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            eArchivesNo = $("#inqu2_status-0-eArchivesNo").val();
            fifleType = $("#inqu2_status-0-fifleType").val();
        }
    });
});
