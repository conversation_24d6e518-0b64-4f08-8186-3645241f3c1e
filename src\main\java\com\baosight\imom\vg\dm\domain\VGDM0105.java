package com.baosight.imom.vg.dm.domain;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.vg.domain.Tvgdm0105;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

/**
 * 分部设备检修配置表
 */
public class VGDM0105 extends Tvgdm0105 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0105.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0105.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0105.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0105.update";
    /**
     * 根据检修周期自动生成预警
     */
    public static final String QUERY_FOR_ALARM = "VGDM0105.queryForAlarm";

    /**
     * 根据分部设备代码查询分部设备检修配置信息
     * 
     * @param dao 数据库操作对象
     * @param deviceCode 分部设备代码
     * @return 分部设备检修配置信息
     */
    public static VGDM0105 queryByDeviceCode(Dao dao, String deviceCode) {
        if (StrUtil.isBlank(deviceCode)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        map.put("deviceCode", deviceCode);
        List<VGDM0105> list = dao.query(VGDM0105.QUERY, map);
        return list.size() > 0 ? list.get(0) : null;
    }
}
