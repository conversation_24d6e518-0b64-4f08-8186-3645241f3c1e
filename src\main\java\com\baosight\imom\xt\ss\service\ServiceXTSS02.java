package com.baosight.imom.xt.ss.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.xt.ss.domain.XTSS02;

/**
 * <AUTHOR>
 * @Date 2024-08-14
 * 业务单元管理服务类
 */
public class ServiceXTSS02 extends ServiceBase {


    private static final Logger LOG = LoggerFactory.getLogger(ServiceXTSS02.class);

    /**
     * 页面初始化加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS02().eiMetadata);

        return inInfo;
    }

    /**
     * 查询 -业务单元查询
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, XTSS02.QUERY_TVZBM81, new XTSS02());
    }


/*    @Override
    public EiInfo insert(EiInfo inInfo) {


        return inInfo;

    }*/


/*    @Override
    public EiInfo update(EiInfo inInfo) {


        return inInfo;

    }*/


/*    @Override
    public EiInfo delete(EiInfo inInfo) {
        return inInfo;
    }*/


}
