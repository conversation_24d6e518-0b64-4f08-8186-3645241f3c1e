package com.baosight.imom.vg.dm.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 辅助作业时间列信息
 */
public class VGDMB103 extends DaoEPBase {

    /**
     * 机组代码
     */
    private String machineCode = " ";

    /**
     * 机组名称
     */
    private String machineName = " ";

    /**
     * 工单
     */
    private String processOrderId = " ";

    /**
     * 作业项
     */
    private String offlineEvent = " ";



    /**
     * 作业时间
     */
    private String offlineTime = " ";

    /**
     * 原料捆包号
     */
    private String packId = " ";

    /**
     * 原料规格
     */
    private String inSpecsDesc = " ";

    /**
     * 原料物料号
     */
    private String inPartId = " ";


    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setDescName("工单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("offlineEvent");
        eiColumn.setDescName("作业项");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("offlineTime");
        eiColumn.setDescName("作业时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("原料捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inSpecsDesc");
        eiColumn.setDescName("原料规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inPartId");
        eiColumn.setDescName("原料物料号");
        eiMetadata.addMeta(eiColumn);
    }


    /**
     * the constructor
     */
    public VGDMB103() {
        initMetaData();
    }


    /**

 * get the value from Map
 */
public void fromMap(Map map) {
    setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
    setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
    setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
    setOfflineEvent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("offlineEvent")), offlineEvent));
    setOfflineTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("offlineTime")), offlineTime));
    setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
    setInSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inSpecsDesc")), inSpecsDesc));
    setInPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inPartId")), inPartId));
}

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("machineName", StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("processOrderId", StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
        map.put("offlineEvent", StringUtils.toString(offlineEvent, eiMetadata.getMeta("offlineEvent")));
        map.put("offlineTime", StringUtils.toString(offlineTime, eiMetadata.getMeta("offlineTime")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("inSpecsDesc", StringUtils.toString(inSpecsDesc, eiMetadata.getMeta("inSpecsDesc")));
        map.put("inPartId", StringUtils.toString(inPartId, eiMetadata.getMeta("inPartId")));
        return map;
    }


    public String getMachineCode() {
        return machineCode;
    }

    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    public String getMachineName() {
        return machineName;
    }

    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }


    public String getPackId() {
        return packId;
    }

    public void setPackId(String packId) {
        this.packId = packId;
    }

    public String getInSpecsDesc() {
        return inSpecsDesc;
    }

    public void setInSpecsDesc(String inSpecsDesc) {
        this.inSpecsDesc = inSpecsDesc;
    }

    public String getInPartId() {
        return inPartId;
    }

    public void setInPartId(String inPartId) {
        this.inPartId = inPartId;
    }

    public String getOfflineEvent() {
        return offlineEvent;
    }

    public void setOfflineEvent(String offlineEvent) {
        this.offlineEvent = offlineEvent;
    }

    public String getOfflineTime() {
        return offlineTime;
    }

    public void setOfflineTime(String offlineTime) {
        this.offlineTime = offlineTime;
    }

    public String getProcessOrderId() {
        return processOrderId;
    }

    public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
    }
}
