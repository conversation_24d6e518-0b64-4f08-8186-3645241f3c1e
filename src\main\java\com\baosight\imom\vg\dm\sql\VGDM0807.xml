<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0807">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            UUID != #notUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overhaulPlanId">
            OVERHAUL_PLAN_ID = #overhaulPlanId#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0807">
        SELECT
        OVERHAUL_PLAN_ID as "overhaulPlanId",  <!-- 检修计划编号 -->
        ITEM_NAME as "itemName",  <!-- 项目名称 -->
        ITEM_INDEX as "itemIndex",  <!-- 项目序号 -->
        SORT_INDEX as "sortIndex",  <!-- 序号 -->
        ITEM_CONTENT as "itemContent",  <!-- 项目内容 -->
        WORK_STANDARD as "workStandard",  <!-- 作业标准 -->
        CHECK_METHOD as "checkMethod",  <!-- 检查方法 -->
        PICTURE_URL as "pictureUrl",  <!-- 图片地址 -->
        SAFE_ITEM as "safeItem",  <!-- 安全注意事项 -->
        CHECK_RECORD as "checkRecord",  <!-- 检查记录 -->
        STATUS as "status",  <!-- 状态 -->
        IS_NORMAL as "isNormal",  <!-- 是否异常 -->
        ACTUALS_REMARK as "actualsRemark",  <!-- 异常信息 -->
        ACTUALS_REVISOR as "actualsRevisor",  <!-- 实绩操作人 -->
        ACTUALS_TIME as "actualsTime",  <!-- 实绩操作时间 -->
        ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 实绩操作人姓名 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0807 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SORT_INDEX asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0807 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0807 (OVERHAUL_PLAN_ID,  <!-- 检修计划编号 -->
        ITEM_NAME,  <!-- 项目名称 -->
        ITEM_INDEX,  <!-- 项目序号 -->
        SORT_INDEX,  <!-- 序号 -->
        ITEM_CONTENT,  <!-- 项目内容 -->
        WORK_STANDARD,  <!-- 作业标准 -->
        CHECK_METHOD,  <!-- 检查方法 -->
        PICTURE_URL,  <!-- 图片地址 -->
        SAFE_ITEM,  <!-- 安全注意事项 -->
        CHECK_RECORD,  <!-- 检查记录 -->
        STATUS,  <!-- 状态 -->
        IS_NORMAL,  <!-- 是否异常 -->
        ACTUALS_REMARK,  <!-- 异常信息 -->
        ACTUALS_REVISOR,  <!-- 实绩操作人 -->
        ACTUALS_TIME,  <!-- 实绩操作时间 -->
        ACTUALS_REVISOR_NAME,  <!-- 实绩操作人姓名 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#overhaulPlanId#, #itemName#, #itemIndex#, #sortIndex#, #itemContent#, #workStandard#, #checkMethod#,
        #pictureUrl#, #safeItem#, #checkRecord#, #status#, #isNormal#, #actualsRemark#, #actualsRevisor#, #actualsTime#,
        #actualsRevisorName#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0807 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0807
        SET
        ITEM_NAME = #itemName#,   <!-- 项目名称 -->
        ITEM_INDEX = #itemIndex#,   <!-- 项目序号 -->
        SORT_INDEX = #sortIndex#,   <!-- 序号 -->
        ITEM_CONTENT = #itemContent#,   <!-- 项目内容 -->
        WORK_STANDARD = #workStandard#,   <!-- 作业标准 -->
        CHECK_METHOD = #checkMethod#,   <!-- 检查方法 -->
        PICTURE_URL = #pictureUrl#,   <!-- 图片地址 -->
        SAFE_ITEM = #safeItem#,   <!-- 安全注意事项 -->
        CHECK_RECORD = #checkRecord#,   <!-- 检查记录 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateActual">
        UPDATE ${mevgSchema}.TVGDM0807
        SET
        IS_NORMAL = #isNormal#,   <!-- 是否异常 -->
        ACTUALS_REMARK = #actualsRemark#,   <!-- 异常信息 -->
        ACTUALS_REVISOR = #actualsRevisor#,   <!-- 实绩操作人 -->
        ACTUALS_TIME = #actualsTime#,   <!-- 实绩操作时间 -->
        ACTUALS_REVISOR_NAME = #actualsRevisorName#,   <!-- 实绩操作人姓名 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updForDel">
        UPDATE ${mevgSchema}.TVGDM0807
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>