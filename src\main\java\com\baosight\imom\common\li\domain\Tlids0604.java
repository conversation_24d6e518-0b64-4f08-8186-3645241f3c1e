/**
* Generate time : 2024-12-25 14:31:06
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids0604
* 
*/
public class Tlids0604 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String crossArea = " ";		/* 跨区代码*/
                private String crossAreaName = " ";		/* 跨区名称*/
                private String factoryArea = " ";		/* 厂区代码*/
                private String factoryAreaName = " ";		/* 厂区名称*/
                private String firstProcessingProcedure = " ";		/* 原卷首道加工工序*/
                private String prodcode = " ";		/* 品种代码*/
                private String specsDesc = " ";		/* 原料规格*/
                private String pollingSchemeNumber = " ";		/* 轮询方案编号*/
                private String status = " ";		/* 状态*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";		/* ID*/
                private String wproviderId = " ";		/* 仓库代码*/
                private String factoryBuilding = " ";		/* 厂房代码*/
                private String factoryBuildingName = " ";		/* 厂房名称*/
                private String customerId = " ";		/* 客户代码*/
                private String businessType = " ";		/* 业务类型*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossArea");
        eiColumn.setDescName("跨区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossAreaName");
        eiColumn.setDescName("跨区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("firstProcessingProcedure");
        eiColumn.setDescName("原卷首道加工工序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodcode");
        eiColumn.setDescName("品种代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("原料规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pollingSchemeNumber");
        eiColumn.setDescName("轮询方案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wproviderId");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tlids0604() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the crossArea - 跨区代码
        * @return the crossArea
        */
        public String getCrossArea() {
        return this.crossArea;
        }

        /**
        * set the crossArea - 跨区代码
        */
        public void setCrossArea(String crossArea) {
        this.crossArea = crossArea;
        }
        /**
        * get the crossAreaName - 跨区名称
        * @return the crossAreaName
        */
        public String getCrossAreaName() {
        return this.crossAreaName;
        }

        /**
        * set the crossAreaName - 跨区名称
        */
        public void setCrossAreaName(String crossAreaName) {
        this.crossAreaName = crossAreaName;
        }
        /**
        * get the factoryArea - 厂区代码
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区代码
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the factoryAreaName - 厂区名称
        * @return the factoryAreaName
        */
        public String getFactoryAreaName() {
        return this.factoryAreaName;
        }

        /**
        * set the factoryAreaName - 厂区名称
        */
        public void setFactoryAreaName(String factoryAreaName) {
        this.factoryAreaName = factoryAreaName;
        }
        /**
        * get the firstProcessingProcedure - 原卷首道加工工序
        * @return the firstProcessingProcedure
        */
        public String getFirstProcessingProcedure() {
        return this.firstProcessingProcedure;
        }

        /**
        * set the firstProcessingProcedure - 原卷首道加工工序
        */
        public void setFirstProcessingProcedure(String firstProcessingProcedure) {
        this.firstProcessingProcedure = firstProcessingProcedure;
        }
        /**
        * get the prodcode - 品种代码
        * @return the prodcode
        */
        public String getProdcode() {
        return this.prodcode;
        }

        /**
        * set the prodcode - 品种代码
        */
        public void setProdcode(String prodcode) {
        this.prodcode = prodcode;
        }
        /**
        * get the specsDesc - 原料规格
        * @return the specsDesc
        */
        public String getSpecsDesc() {
        return this.specsDesc;
        }

        /**
        * set the specsDesc - 原料规格
        */
        public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
        }
        /**
        * get the pollingSchemeNumber - 轮询方案编号
        * @return the pollingSchemeNumber
        */
        public String getPollingSchemeNumber() {
        return this.pollingSchemeNumber;
        }

        /**
        * set the pollingSchemeNumber - 轮询方案编号
        */
        public void setPollingSchemeNumber(String pollingSchemeNumber) {
        this.pollingSchemeNumber = pollingSchemeNumber;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the wproviderId - 仓库代码
        * @return the wproviderId
        */
        public String getWproviderId() {
        return this.wproviderId;
        }

        /**
        * set the wproviderId - 仓库代码
        */
        public void setWproviderId(String wproviderId) {
        this.wproviderId = wproviderId;
        }
        /**
        * get the factoryBuilding - 厂房代码
        * @return the factoryBuilding
        */
        public String getFactoryBuilding() {
        return this.factoryBuilding;
        }

        /**
        * set the factoryBuilding - 厂房代码
        */
        public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
        }
        /**
        * get the factoryBuildingName - 厂房名称
        * @return the factoryBuildingName
        */
        public String getFactoryBuildingName() {
        return this.factoryBuildingName;
        }

        /**
        * set the factoryBuildingName - 厂房名称
        */
        public void setFactoryBuildingName(String factoryBuildingName) {
        this.factoryBuildingName = factoryBuildingName;
        }
        /**
        * get the customerId - 客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the businessType - 业务类型
        * @return the businessType
        */
        public String getBusinessType() {
        return this.businessType;
        }

        /**
        * set the businessType - 业务类型
        */
        public void setBusinessType(String businessType) {
        this.businessType = businessType;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setCrossArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossArea")), crossArea));
                setCrossAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossAreaName")), crossAreaName));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
                setFirstProcessingProcedure(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("firstProcessingProcedure")), firstProcessingProcedure));
                setProdcode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodcode")), prodcode));
                setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
                setPollingSchemeNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pollingSchemeNumber")), pollingSchemeNumber));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setWproviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wproviderId")), wproviderId));
                setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
                setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessType")), businessType));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("crossArea",StringUtils.toString(crossArea, eiMetadata.getMeta("crossArea")));
                map.put("crossAreaName",StringUtils.toString(crossAreaName, eiMetadata.getMeta("crossAreaName")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("factoryAreaName",StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
                map.put("firstProcessingProcedure",StringUtils.toString(firstProcessingProcedure, eiMetadata.getMeta("firstProcessingProcedure")));
                map.put("prodcode",StringUtils.toString(prodcode, eiMetadata.getMeta("prodcode")));
                map.put("specsDesc",StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
                map.put("pollingSchemeNumber",StringUtils.toString(pollingSchemeNumber, eiMetadata.getMeta("pollingSchemeNumber")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("wproviderId",StringUtils.toString(wproviderId, eiMetadata.getMeta("wproviderId")));
                map.put("factoryBuilding",StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
                map.put("factoryBuildingName",StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("businessType",StringUtils.toString(businessType, eiMetadata.getMeta("businessType")));

return map;

}
}