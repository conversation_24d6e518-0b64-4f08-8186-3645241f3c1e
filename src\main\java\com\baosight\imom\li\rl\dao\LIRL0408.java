/**
* Generate time : 2024-09-25 10:19:18
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0408 车辆出厂表
* 
*/
public class LIRL0408 extends DaoEPBase {

        public static final String QUERY = "LIRL0408.query";
        public static final String INSERT = "LIRL0408.insert";
        public static final String UPDATE = "LIRL0408.update";
        public static final String UPDATE_BACK_STATUS = "LIRL0408.updateBackStatus";
        public static final String DELETE = "LIRL0408.delete";
                private String segNo = " ";		/* 账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String leaveFactoryId = " ";		/* 出厂流水号*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String status = " ";		/* 状态(00撤销 10新增)*/
                private String leaveFactoryDate = " ";		/* 出厂时间*/
                private String cancelLeaveFactoryDate = " ";		/* 撤销出厂时间*/
                private String carTraceNo = " ";		/* 车辆跟踪号*/
                private String dateSource = " ";		/* 数据来源(10：mes 20pda 30：车牌识别)*/
                private String deviceNumber = " ";		/* 设备号（车牌识别）*/
                private String deviceVoucherNum = " ";		/* 依据凭单号(车牌识别系统流水号)*/
                private String voucherNum = " ";		/* 依据凭单号(PAD作业流水号)*/
                private String factoryArea = " ";		/* 厂区*/
                private String unloadLeaveFlag = " ";		/* 未装离厂标记*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String backStatus = " ";		/* 返单状态*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryId");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("出厂流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00撤销 10新增)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cancelLeaveFactoryDate");
        eiColumn.setDescName("撤销出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dateSource");
        eiColumn.setDescName("数据来源(10：mes 20pda 30：车牌识别)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceNumber");
        eiColumn.setDescName("设备号（车牌识别）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceVoucherNum");
        eiColumn.setDescName("依据凭单号(车牌识别系统流水号)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单号(PAD作业流水号)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unloadLeaveFlag");
        eiColumn.setDescName("未装离厂标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("backStatus");
        eiColumn.setDescName("返单状态");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0408() {
initMetaData();
}

        /**
        * get the segNo - 账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the leaveFactoryId - 出厂流水号
        * @return the leaveFactoryId
        */
        public String getLeaveFactoryId() {
        return this.leaveFactoryId;
        }

        /**
        * set the leaveFactoryId - 出厂流水号
        */
        public void setLeaveFactoryId(String leaveFactoryId) {
        this.leaveFactoryId = leaveFactoryId;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the status - 状态(00撤销 10新增)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(00撤销 10新增)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the leaveFactoryDate - 出厂时间
        * @return the leaveFactoryDate
        */
        public String getLeaveFactoryDate() {
        return this.leaveFactoryDate;
        }

        /**
        * set the leaveFactoryDate - 出厂时间
        */
        public void setLeaveFactoryDate(String leaveFactoryDate) {
        this.leaveFactoryDate = leaveFactoryDate;
        }
        /**
        * get the cancelLeaveFactoryDate - 撤销出厂时间
        * @return the cancelLeaveFactoryDate
        */
        public String getCancelLeaveFactoryDate() {
        return this.cancelLeaveFactoryDate;
        }

        /**
        * set the cancelLeaveFactoryDate - 撤销出厂时间
        */
        public void setCancelLeaveFactoryDate(String cancelLeaveFactoryDate) {
        this.cancelLeaveFactoryDate = cancelLeaveFactoryDate;
        }
        /**
        * get the carTraceNo - 车辆跟踪号
        * @return the carTraceNo
        */
        public String getCarTraceNo() {
        return this.carTraceNo;
        }

        /**
        * set the carTraceNo - 车辆跟踪号
        */
        public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
        }
        /**
        * get the dateSource - 数据来源(10：mes 20pda 30：车牌识别)
        * @return the dateSource
        */
        public String getDateSource() {
        return this.dateSource;
        }

        /**
        * set the dateSource - 数据来源(10：mes 20pda 30：车牌识别)
        */
        public void setDateSource(String dateSource) {
        this.dateSource = dateSource;
        }
        /**
        * get the deviceNumber - 设备号（车牌识别）
        * @return the deviceNumber
        */
        public String getDeviceNumber() {
        return this.deviceNumber;
        }

        /**
        * set the deviceNumber - 设备号（车牌识别）
        */
        public void setDeviceNumber(String deviceNumber) {
        this.deviceNumber = deviceNumber;
        }
        /**
        * get the deviceVoucherNum - 依据凭单号(车牌识别系统流水号)
        * @return the deviceVoucherNum
        */
        public String getDeviceVoucherNum() {
        return this.deviceVoucherNum;
        }

        /**
        * set the deviceVoucherNum - 依据凭单号(车牌识别系统流水号)
        */
        public void setDeviceVoucherNum(String deviceVoucherNum) {
        this.deviceVoucherNum = deviceVoucherNum;
        }
        /**
        * get the voucherNum - 依据凭单号(PAD作业流水号)
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 依据凭单号(PAD作业流水号)
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the factoryArea - 厂区
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the unloadLeaveFlag - 未装离厂标记
        * @return the unloadLeaveFlag
        */
        public String getUnloadLeaveFlag() {
        return this.unloadLeaveFlag;
        }

        /**
        * set the unloadLeaveFlag - 未装离厂标记
        */
        public void setUnloadLeaveFlag(String unloadLeaveFlag) {
        this.unloadLeaveFlag = unloadLeaveFlag;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }

        public String getBackStatus() {
                return backStatus;
        }

        public void setBackStatus(String backStatus) {
                this.backStatus = backStatus;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setLeaveFactoryId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leaveFactoryId")), leaveFactoryId));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setLeaveFactoryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leaveFactoryDate")), leaveFactoryDate));
                setCancelLeaveFactoryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("cancelLeaveFactoryDate")), cancelLeaveFactoryDate));
                setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
                setDateSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dateSource")), dateSource));
                setDeviceNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceNumber")), deviceNumber));
                setDeviceVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceVoucherNum")), deviceVoucherNum));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setUnloadLeaveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unloadLeaveFlag")), unloadLeaveFlag));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setBackStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("backStatus")), backStatus));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("leaveFactoryId",StringUtils.toString(leaveFactoryId, eiMetadata.getMeta("leaveFactoryId")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("leaveFactoryDate",StringUtils.toString(leaveFactoryDate, eiMetadata.getMeta("leaveFactoryDate")));
                map.put("cancelLeaveFactoryDate",StringUtils.toString(cancelLeaveFactoryDate, eiMetadata.getMeta("cancelLeaveFactoryDate")));
                map.put("carTraceNo",StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
                map.put("dateSource",StringUtils.toString(dateSource, eiMetadata.getMeta("dateSource")));
                map.put("deviceNumber",StringUtils.toString(deviceNumber, eiMetadata.getMeta("deviceNumber")));
                map.put("deviceVoucherNum",StringUtils.toString(deviceVoucherNum, eiMetadata.getMeta("deviceVoucherNum")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("unloadLeaveFlag",StringUtils.toString(unloadLeaveFlag, eiMetadata.getMeta("unloadLeaveFlag")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("backStatus",StringUtils.toString(tenantId, eiMetadata.getMeta("backStatus")));

return map;

}
}