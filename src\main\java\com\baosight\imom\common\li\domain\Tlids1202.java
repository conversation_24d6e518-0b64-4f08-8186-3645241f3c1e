/**
* Generate time : 2025-01-07 9:52:31
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids1202
* 
*/
public class Tlids1202 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String segName = " ";        /* 业务单元简称*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String craneResultId = " ";		/* 行车实绩单号*/
                private String craneResultSubId = " ";		/* 行车实绩子项号*/
                private String packId = " ";		/* 捆包号*/
                private String labelId = " ";		/* 标签号*/
                private BigDecimal netWeight = new BigDecimal(0.00000000);		/* 净重*/
                private Integer quantity = Integer.valueOf(0);		/* 数量*/
                private String status = " ";		/* 状态*/
                private String mouldId = " ";		/* 模具ID*/
                private String mouldName = " ";		/* 模具名称*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";		/* ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneResultId");
        eiColumn.setDescName("行车实绩单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneResultSubId");
        eiColumn.setDescName("行车实绩子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setDescName("标签号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldId");
        eiColumn.setDescName("模具ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldName");
        eiColumn.setDescName("模具名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tlids1202() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the craneResultId - 行车实绩单号
        * @return the craneResultId
        */
        public String getCraneResultId() {
        return this.craneResultId;
        }

        /**
        * set the craneResultId - 行车实绩单号
        */
        public void setCraneResultId(String craneResultId) {
        this.craneResultId = craneResultId;
        }
        /**
        * get the craneResultSubId - 行车实绩子项号
        * @return the craneResultSubId
        */
        public String getCraneResultSubId() {
        return this.craneResultSubId;
        }

        /**
        * set the craneResultSubId - 行车实绩子项号
        */
        public void setCraneResultSubId(String craneResultSubId) {
        this.craneResultSubId = craneResultSubId;
        }
        /**
        * get the packId - 捆包号
        * @return the packId
        */
        public String getPackId() {
        return this.packId;
        }

        /**
        * set the packId - 捆包号
        */
        public void setPackId(String packId) {
        this.packId = packId;
        }
        /**
        * get the labelId - 标签号
        * @return the labelId
        */
        public String getLabelId() {
        return this.labelId;
        }

        /**
        * set the labelId - 标签号
        */
        public void setLabelId(String labelId) {
        this.labelId = labelId;
        }
        /**
        * get the netWeight - 净重
        * @return the netWeight
        */
        public BigDecimal getNetWeight() {
        return this.netWeight;
        }

        /**
        * set the netWeight - 净重
        */
        public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
        }
        /**
        * get the quantity - 数量
        * @return the quantity
        */
        public Integer getQuantity() {
        return this.quantity;
        }

        /**
        * set the quantity - 数量
        */
        public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the mouldId - 模具ID
        * @return the mouldId
        */
        public String getMouldId() {
        return this.mouldId;
        }

        /**
        * set the mouldId - 模具ID
        */
        public void setMouldId(String mouldId) {
        this.mouldId = mouldId;
        }
        /**
        * get the mouldName - 模具名称
        * @return the mouldName
        */
        public String getMouldName() {
        return this.mouldName;
        }

        /**
        * set the mouldName - 模具名称
        */
        public void setMouldName(String mouldName) {
        this.mouldName = mouldName;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }

        public String getSegName() {
            return segName;
        }

        public void setSegName(String segName) {
            this.segName = segName;
        }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setCraneResultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneResultId")), craneResultId));
                setCraneResultSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneResultSubId")), craneResultSubId));
                setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
                setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));
                setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
                setQuantity(NumberUtils.toInteger(StringUtils.toString(map.get("quantity")), quantity));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setMouldId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldId")), mouldId));
                setMouldName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldName")), mouldName));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("craneResultId",StringUtils.toString(craneResultId, eiMetadata.getMeta("craneResultId")));
                map.put("craneResultSubId",StringUtils.toString(craneResultSubId, eiMetadata.getMeta("craneResultSubId")));
                map.put("packId",StringUtils.toString(packId, eiMetadata.getMeta("packId")));
                map.put("labelId",StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));
                map.put("netWeight",StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
                map.put("quantity",StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("mouldId",StringUtils.toString(mouldId, eiMetadata.getMeta("mouldId")));
                map.put("mouldName",StringUtils.toString(mouldName, eiMetadata.getMeta("mouldName")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));

return map;

}
}