package com.baosight.imom.vg.dm.domain;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.imom.common.vg.domain.Tvgdm0102;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import org.apache.commons.collections.CollectionUtils;

/**
 * 分部档案信息表
 */
public class VGDM0102 extends Tvgdm0102 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0102.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0102.count";
    /**
     * 查询条数
     */
    public static final String COUNT_BY_ID = "VGDM0102.countById";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0102.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0102.update";
    /**
     * 修改
     */
    public static final String UPDATE_ARCHIVE = "VGDM0102.updateArchive";
    /**
     * 修改
     */
    public static final String UPDATE_ARCHIVE2 = "VGDM0102.updateArchive2";
    /**
     * 根据分部设备编码查询分部设备信息
     */
    public static final String QUERY_BY_NO = "VGDM0102.queryByNo";

    /**
     * 根据分部设备编码查询分部设备信息
     *
     * @param dao        dao
     * @param deviceCode 分部设备编码
     * @return 分部档案信息或null
     */
    public static VGDM0102 queryByNo(Dao dao, String deviceCode) {
        Map<String, String> queryMap = new HashMap<>(1);
        queryMap.put("deviceCode", deviceCode);
        List list = dao.query(QUERY_BY_NO, queryMap);
        if (CollectionUtils.isNotEmpty(list)) {
            return (VGDM0102) list.get(0);
        }
        return null;
    }
}
