/**
 * Generate time : 2025-04-24 9:41:11
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * Tvgdm0807
 */
public class Tvgdm0807 extends DaoEPBase {

    private String overhaulPlanId = " ";        /* 检修计划编号*/
    @NotBlank(message = "项目名称不能为空")
    private String itemName = " ";        /* 项目名称*/
    private Integer itemIndex = 1;        /* 项目序号*/
    @NotNull(message = "序号不能为空")
    private Integer sortIndex = 1;        /* 序号*/
    @NotBlank(message = "内容不能为空")
    private String itemContent = " ";        /* 项目内容*/
    @NotBlank(message = "作业标准不能为空")
    private String workStandard = " ";        /* 作业标准*/
    @NotBlank(message = "检修方法不能为空")
    private String checkMethod = " ";        /* 检查方法*/
    @NotBlank(message = "作业图示不能为空")
    private String pictureUrl = " ";        /* 图片地址*/
    @NotBlank(message = "安全注意事项不能为空")
    private String safeItem = " ";        /* 安全注意事项*/
    @NotBlank(message = "检查记录不能为空")
    private String checkRecord = " ";        /* 检查记录*/
    private String status = " ";        /* 状态*/
    private String isNormal = "0";        /* 是否异常*/
    private String actualsRemark = " ";        /* 异常信息*/
    private String actualsRevisor = " ";        /* 实绩操作人*/
    private String actualsTime = " ";        /* 实绩操作时间*/
    private String actualsRevisorName = " ";        /* 实绩操作人姓名*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("overhaulPlanId");
        eiColumn.setDescName("检修计划编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("itemName");
        eiColumn.setDescName("项目名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("itemIndex");
        eiColumn.setDescName("项目序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortIndex");
        eiColumn.setDescName("序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("itemContent");
        eiColumn.setDescName("项目内容");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workStandard");
        eiColumn.setDescName("作业标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkMethod");
        eiColumn.setDescName("检查方法");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pictureUrl");
        eiColumn.setDescName("图片地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("safeItem");
        eiColumn.setDescName("安全注意事项");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkRecord");
        eiColumn.setDescName("检查记录");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isNormal");
        eiColumn.setDescName("是否异常");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsRemark");
        eiColumn.setDescName("异常信息");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsRevisor");
        eiColumn.setDescName("实绩操作人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsTime");
        eiColumn.setDescName("实绩操作时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsRevisorName");
        eiColumn.setDescName("实绩操作人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0807() {
        initMetaData();
    }

    /**
     * get the overhaulPlanId - 检修计划编号
     *
     * @return the overhaulPlanId
     */
    public String getOverhaulPlanId() {
        return this.overhaulPlanId;
    }

    /**
     * set the overhaulPlanId - 检修计划编号
     */
    public void setOverhaulPlanId(String overhaulPlanId) {
        this.overhaulPlanId = overhaulPlanId;
    }

    /**
     * get the itemName - 项目名称
     *
     * @return the itemName
     */
    public String getItemName() {
        return this.itemName;
    }

    /**
     * set the itemName - 项目名称
     */
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    /**
     * get the itemIndex - 项目序号
     *
     * @return the itemIndex
     */
    public Integer getItemIndex() {
        return this.itemIndex;
    }

    /**
     * set the itemIndex - 项目序号
     */
    public void setItemIndex(Integer itemIndex) {
        this.itemIndex = itemIndex;
    }

    /**
     * get the sortIndex - 序号
     *
     * @return the sortIndex
     */
    public Integer getSortIndex() {
        return this.sortIndex;
    }

    /**
     * set the sortIndex - 序号
     */
    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }

    /**
     * get the itemContent - 项目内容
     *
     * @return the itemContent
     */
    public String getItemContent() {
        return this.itemContent;
    }

    /**
     * set the itemContent - 项目内容
     */
    public void setItemContent(String itemContent) {
        this.itemContent = itemContent;
    }

    /**
     * get the workStandard - 作业标准
     *
     * @return the workStandard
     */
    public String getWorkStandard() {
        return this.workStandard;
    }

    /**
     * set the workStandard - 作业标准
     */
    public void setWorkStandard(String workStandard) {
        this.workStandard = workStandard;
    }

    /**
     * get the checkMethod - 检查方法
     *
     * @return the checkMethod
     */
    public String getCheckMethod() {
        return this.checkMethod;
    }

    /**
     * set the checkMethod - 检查方法
     */
    public void setCheckMethod(String checkMethod) {
        this.checkMethod = checkMethod;
    }

    /**
     * get the pictureUrl - 图片地址
     *
     * @return the pictureUrl
     */
    public String getPictureUrl() {
        return this.pictureUrl;
    }

    /**
     * set the pictureUrl - 图片地址
     */
    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    /**
     * get the safeItem - 安全注意事项
     *
     * @return the safeItem
     */
    public String getSafeItem() {
        return this.safeItem;
    }

    /**
     * set the safeItem - 安全注意事项
     */
    public void setSafeItem(String safeItem) {
        this.safeItem = safeItem;
    }

    /**
     * get the checkRecord - 检查记录
     *
     * @return the checkRecord
     */
    public String getCheckRecord() {
        return this.checkRecord;
    }

    /**
     * set the checkRecord - 检查记录
     */
    public void setCheckRecord(String checkRecord) {
        this.checkRecord = checkRecord;
    }

    /**
     * get the status - 状态
     *
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the isNormal - 是否异常
     *
     * @return the isNormal
     */
    public String getIsNormal() {
        return this.isNormal;
    }

    /**
     * set the isNormal - 是否异常
     */
    public void setIsNormal(String isNormal) {
        this.isNormal = isNormal;
    }

    /**
     * get the actualsRemark - 异常信息
     *
     * @return the actualsRemark
     */
    public String getActualsRemark() {
        return this.actualsRemark;
    }

    /**
     * set the actualsRemark - 异常信息
     */
    public void setActualsRemark(String actualsRemark) {
        this.actualsRemark = actualsRemark;
    }

    /**
     * get the actualsRevisor - 实绩操作人
     *
     * @return the actualsRevisor
     */
    public String getActualsRevisor() {
        return this.actualsRevisor;
    }

    /**
     * set the actualsRevisor - 实绩操作人
     */
    public void setActualsRevisor(String actualsRevisor) {
        this.actualsRevisor = actualsRevisor;
    }

    /**
     * get the actualsTime - 实绩操作时间
     *
     * @return the actualsTime
     */
    public String getActualsTime() {
        return this.actualsTime;
    }

    /**
     * set the actualsTime - 实绩操作时间
     */
    public void setActualsTime(String actualsTime) {
        this.actualsTime = actualsTime;
    }

    /**
     * get the actualsRevisorName - 实绩操作人姓名
     *
     * @return the actualsRevisorName
     */
    public String getActualsRevisorName() {
        return this.actualsRevisorName;
    }

    /**
     * set the actualsRevisorName - 实绩操作人姓名
     */
    public void setActualsRevisorName(String actualsRevisorName) {
        this.actualsRevisorName = actualsRevisorName;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setOverhaulPlanId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulPlanId")), overhaulPlanId));
        setItemName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("itemName")), itemName));
        setItemIndex(NumberUtils.toInteger(StringUtils.toString(map.get("itemIndex")), itemIndex));
        setSortIndex(NumberUtils.toInteger(StringUtils.toString(map.get("sortIndex")), sortIndex));
        setItemContent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("itemContent")), itemContent));
        setWorkStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workStandard")), workStandard));
        setCheckMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkMethod")), checkMethod));
        setPictureUrl(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pictureUrl")), pictureUrl));
        setSafeItem(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("safeItem")), safeItem));
        setCheckRecord(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkRecord")), checkRecord));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setIsNormal(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isNormal")), isNormal));
        setActualsRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsRemark")), actualsRemark));
        setActualsRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsRevisor")), actualsRevisor));
        setActualsTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsTime")), actualsTime));
        setActualsRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsRevisorName")), actualsRevisorName));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("overhaulPlanId", StringUtils.toString(overhaulPlanId, eiMetadata.getMeta("overhaulPlanId")));
        map.put("itemName", StringUtils.toString(itemName, eiMetadata.getMeta("itemName")));
        map.put("itemIndex", StringUtils.toString(itemIndex, eiMetadata.getMeta("itemIndex")));
        map.put("sortIndex", StringUtils.toString(sortIndex, eiMetadata.getMeta("sortIndex")));
        map.put("itemContent", StringUtils.toString(itemContent, eiMetadata.getMeta("itemContent")));
        map.put("workStandard", StringUtils.toString(workStandard, eiMetadata.getMeta("workStandard")));
        map.put("checkMethod", StringUtils.toString(checkMethod, eiMetadata.getMeta("checkMethod")));
        map.put("pictureUrl", StringUtils.toString(pictureUrl, eiMetadata.getMeta("pictureUrl")));
        map.put("safeItem", StringUtils.toString(safeItem, eiMetadata.getMeta("safeItem")));
        map.put("checkRecord", StringUtils.toString(checkRecord, eiMetadata.getMeta("checkRecord")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("isNormal", StringUtils.toString(isNormal, eiMetadata.getMeta("isNormal")));
        map.put("actualsRemark", StringUtils.toString(actualsRemark, eiMetadata.getMeta("actualsRemark")));
        map.put("actualsRevisor", StringUtils.toString(actualsRevisor, eiMetadata.getMeta("actualsRevisor")));
        map.put("actualsTime", StringUtils.toString(actualsTime, eiMetadata.getMeta("actualsTime")));
        map.put("actualsRevisorName", StringUtils.toString(actualsRevisorName, eiMetadata.getMeta("actualsRevisorName")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}