<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0808">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            UUID != #notUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overhaulPlanId">
            OVERHAUL_PLAN_ID = #overhaulPlanId#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0808">
        SELECT
        OVERHAUL_PLAN_ID as "overhaulPlanId",  <!-- 检修计划编号 -->
        DEVICE_LOCATION as "deviceLocation",  <!-- 设备位置 -->
        LUBRICATE_POINT as "lubricatePoint",  <!-- 润滑点 -->
        LUBRICATE_QTY as "lubricateQty",  <!-- 润滑点数量 -->
        ADD_QTY as "addQty",  <!-- 加油量 -->
        GREASE_SPEC as "greaseSpec",  <!-- 油脂规格 -->
        LUBRICATE_CYCLE as "lubricateCycle",  <!-- 润滑周期 -->
        LUBRICATE_RECORD as "lubricateRecord",  <!-- 润滑记录 -->
        REMARK as "remark",  <!-- 备注 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0808 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0808 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0808 (OVERHAUL_PLAN_ID,  <!-- 检修计划编号 -->
        DEVICE_LOCATION,  <!-- 设备位置 -->
        LUBRICATE_POINT,  <!-- 润滑点 -->
        LUBRICATE_QTY,  <!-- 润滑点数量 -->
        ADD_QTY,  <!-- 加油量 -->
        GREASE_SPEC,  <!-- 油脂规格 -->
        LUBRICATE_CYCLE,  <!-- 润滑周期 -->
        LUBRICATE_RECORD,  <!-- 润滑记录 -->
        REMARK,  <!-- 备注 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#overhaulPlanId#, #deviceLocation#, #lubricatePoint#, #lubricateQty#, #addQty#, #greaseSpec#,
        #lubricateCycle#, #lubricateRecord#, #remark#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#,
        #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0808 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0808
        SET
        DEVICE_LOCATION = #deviceLocation#,   <!-- 设备位置 -->
        LUBRICATE_POINT = #lubricatePoint#,   <!-- 润滑点 -->
        LUBRICATE_QTY = #lubricateQty#,   <!-- 润滑点数量 -->
        ADD_QTY = #addQty#,   <!-- 加油量 -->
        GREASE_SPEC = #greaseSpec#,   <!-- 油脂规格 -->
        LUBRICATE_CYCLE = #lubricateCycle#,   <!-- 润滑周期 -->
        LUBRICATE_RECORD = #lubricateRecord#,   <!-- 润滑记录 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateRecord">
        UPDATE ${mevgSchema}.TVGDM0808
        SET
        LUBRICATE_RECORD = #lubricateRecord#,   <!-- 润滑记录 -->
        REMARK = #remark#,   <!-- 备注 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updForDel">
        UPDATE ${mevgSchema}.TVGDM0808
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>