/**
* Generate time : 2025-01-06 10:29:43
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0503
* 
*/
public class LIRL0503 extends DaoEPBase {
        public static final String QUERYPAGE = "LIRL0503.queryPage";
        public static final String QUERY = "LIRL0503.query";
        public static final String QUERY_0503 = "LIRL0503.query0503";
        public static final String QUERY_COUNT = "LIRL0503.queryCount";
        public static final String QUERY_BILL_METHOD = "LIRL0503.queryBillMethod";
        public static final String QUERY_ALL_PACK = "LIRL0503.queryAllPack";
        public static final String QUERY_ALL_PACK_NEW = "LIRL0503.queryAllPackNew";
        public static final String QUERY_PACK_INFO = "LIRL0503.queryPackInfo";
        public static final String QUERY_ALLOCATE_VEHICLE_NO_INFO = "LIRL0503.queryAllocateVehicleNoInfo";
        public static final String QUERY_ALL_ALLOCATE_VEHICLE_NO_INFO = "LIRL0503.queryAllAllocateVehicleNoInfo";
        public static final String QUERY_ALL_PACK_INFO = "LIRL0503.queryAllPackInfo";
        public static final String QUERY_MATCHING_ORDER = "LIRL0503.queryMatchingOrder";
        public static final String COUNT = "LIRL0503.count";
        public static final String UPDATE = "LIRL0503.update";
        public static final String INSERT = "LIRL0503.insert";
        public static final String UPDATE_STATUS = "LIRL0503.updateStatus";
        public static final String UPDATE_TARGET_HAND_POINT_ID = "LIRL0503.updateHandPointId";
        public static final String UPDATE_PACK_STATUS = "LIRL0503.updatePackStatus";
        public static final String GET_PUTIN_PLAN = "LIRL0503.getPutinPlan";
        public static final String GET_PUTIN_PLAN_KB = "LIRL0503.getPutinPlanKb";
        public static final String QUERY_ALL_ALLOCATE_VEHICLE_NO = "LIRL0503.queryAllAllocateVehicleNo";
        public static final String UPDATE_BILL_STATUS = "LIRL0503.updateBillStatus";
        //根据配车单号查询明细数据，配车单号必填
        public static final String QUERY_BY_ALLOCATE_VEHICLE_NO = "LIRL0503.queryByAllocateVehicleNo";
        //根据配车单号统计条数，配车单号必填
        public static final String COUNT_BY_ALLOCATE_VEHICLE_NO = "LIRL0503.countByAllocateVehicleNo";
        //查询所有状态不为完成的普通提单的配车单明细
        public static final String QUERY_BY_STATUS_NOT_COMPLETE = "LIRL0503.queryByStatusNotComplete";
        //更新配车单状态不为完成的明细数据,更新状态为完成，拼接备注
        public static final String UPDATE_COMPLETE_BY_ALLOCATE_VEHICLE_NO = "LIRL0503.updateCompleteByAllocateVehicleNo";
        public static final String QUERY_MAXALLOC_VEHICLE_S = "LIRL0503.queryMaxallocVehicleS";
        public static final String QUERY_SUM_WEIGHT = "LIRL0503.querySumWeight";



                private String segNo = " ";		/* 业务单元代码*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String segName = " ";        /* 业务单元简称*/
                private String status = " ";		/* 状态*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录创建人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String allocateVehicleNo = " ";		/* 配车单号*/
                private String allocVehicleSeq = " ";		/* 配车单子项序号*/
                private String voucherNum = " ";		/* 依据凭单(卸货:入库计划;装货:提单号)*/
                private String packId = " ";		/* 捆包号*/
                private String outPackFlag = "0";		/* 自带货标记(0:非自带货;1:自带货)*/
                private String warehouseCode = " ";		/* 仓库代码*/
                private String warehouseName = " ";		/* 仓库名称*/
                private String putinType = " ";		/* 入库类型*/
                private BigDecimal innerDiameter = new BigDecimal(0.000000);		/* 内径*/
                private BigDecimal prodDensity = new BigDecimal(0.000000);		/* 密度*/
                private String productProcessId = " ";		/* 首道加工工序*/
                private BigDecimal netWeight = new BigDecimal(0.00000000);		/* 重量*/
                private String customerId = " ";		/* 客户代码*/
                private String customerName = " ";		/* 客户名称*/
                private String matInnerId = " ";		/* 材料管理号*/
                private String targetHandPointId = " ";        /* 目标装卸点*/
                private String targetHandPointName = " ";  /*目标装卸点名称*/
                private String locationId = " ";		/* 库位代码*/
                private String locationName = " ";		/* 库位名称*/
                private String factoryOrderNum = " ";		/* 钢厂订单号*/
                private String piceNum= " ";		/* 张数*/
                private String factoryArea= " ";		/* 厂区代码*/
                private String specsDesc = " ";        /* 规格表述*/
                private String prodTypeId = " ";        /* 规格表述*/
                private String tradeCode = " ";        /* 规格表述*/
                private String custPartId = " ";        /* 客户零件号*/
                private String custPartName = " ";        /* 客户零件名称*/
                private String mPackId = " ";        /* 母捆包*/
                private String purOrderNum = " ";        /* 销售订单子项号*/
                private String ladingBillRemark = " ";        /* 提单备注*/
                private String billingMethod = " ";        /* 提单备注*/
                private String firstMachineCode = " ";        /* 首道机组*/
                private String logisticPlanType = " ";        /* 采购类型*/
                private String labelId = " ";        /* 标签号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocateVehicleNo");
        eiColumn.setDescName("配车单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocVehicleSeq");
        eiColumn.setDescName("配车单子项序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单(卸货:入库计划;装货:提单号)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outPackFlag");
        eiColumn.setDescName("自带货标记(0:非自带货;1:自带货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putinType");
        eiColumn.setDescName("入库类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("innerDiameter");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("内径");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodDensity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("密度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productProcessId");
        eiColumn.setDescName("首道加工工序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("matInnerId");
        eiColumn.setDescName("材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointId");
        eiColumn.setDescName("目标装卸点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointName");
        eiColumn.setDescName("目标装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryOrderNum");
        eiColumn.setDescName("钢厂订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("piceNum");
        eiColumn.setDescName("张数");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("规格表述");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setDescName("贸易方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartId");
        eiColumn.setDescName("客户零件号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartName");
        eiColumn.setDescName("客户零件名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mPackId");
        eiColumn.setDescName("母捆包");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("purOrderNum");
        eiColumn.setDescName("销售订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillRemark");
        eiColumn.setDescName("提单备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billingMethod");
        eiColumn.setDescName("提单类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("firstMachineCode");
        eiColumn.setDescName("提单类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("logisticPlanType");
        eiColumn.setDescName("采购类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setDescName("标签号");
        eiMetadata.addMeta(eiColumn);





}
/**
* the constructor
*/
public LIRL0503() {
initMetaData();
}


        public String getProdTypeId() {
                return prodTypeId;
        }

        public void setProdTypeId(String prodTypeId) {
                this.prodTypeId = prodTypeId;
        }

        public String getTradeCode() {
                return tradeCode;
        }

        public void setTradeCode(String tradeCode) {
                this.tradeCode = tradeCode;
        }

        /**
        * get the segNo - 业务单元代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录创建人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录创建人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
        /**
        * get the allocateVehicleNo - 配车单号
        * @return the allocateVehicleNo
        */
        public String getAllocateVehicleNo() {
        return this.allocateVehicleNo;
        }

        /**
        * set the allocateVehicleNo - 配车单号
        */
        public void setAllocateVehicleNo(String allocateVehicleNo) {
        this.allocateVehicleNo = allocateVehicleNo;
        }
        /**
        * get the allocVehicleSeq - 配车单子项序号
        * @return the allocVehicleSeq
        */
        public String getAllocVehicleSeq() {
        return this.allocVehicleSeq;
        }

        /**
        * set the allocVehicleSeq - 配车单子项序号
        */
        public void setAllocVehicleSeq(String allocVehicleSeq) {
        this.allocVehicleSeq = allocVehicleSeq;
        }
        /**
        * get the voucherNum - 依据凭单(卸货:入库计划;装货:提单号)
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 依据凭单(卸货:入库计划;装货:提单号)
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the packId - 捆包号
        * @return the packId
        */
        public String getPackId() {
        return this.packId;
        }

        /**
        * set the packId - 捆包号
        */
        public void setPackId(String packId) {
        this.packId = packId;
        }
        /**
        * get the outPackFlag - 自带货标记(0:非自带货;1:自带货)
        * @return the outPackFlag
        */
        public String getOutPackFlag() {
        return this.outPackFlag;
        }

        /**
        * set the outPackFlag - 自带货标记(0:非自带货;1:自带货)
        */
        public void setOutPackFlag(String outPackFlag) {
        this.outPackFlag = outPackFlag;
        }
        /**
        * get the warehouseCode - 仓库代码
        * @return the warehouseCode
        */
        public String getWarehouseCode() {
        return this.warehouseCode;
        }

        /**
        * set the warehouseCode - 仓库代码
        */
        public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
        }
        /**
        * get the warehouseName - 仓库名称
        * @return the warehouseName
        */
        public String getWarehouseName() {
        return this.warehouseName;
        }

        /**
        * set the warehouseName - 仓库名称
        */
        public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        }
        /**
        * get the putinType - 入库类型
        * @return the putinType
        */
        public String getPutinType() {
        return this.putinType;
        }

        /**
        * set the putinType - 入库类型
        */
        public void setPutinType(String putinType) {
        this.putinType = putinType;
        }
        /**
        * get the innerDiameter - 内径
        * @return the innerDiameter
        */
        public BigDecimal getInnerDiameter() {
        return this.innerDiameter;
        }

        /**
        * set the innerDiameter - 内径
        */
        public void setInnerDiameter(BigDecimal innerDiameter) {
        this.innerDiameter = innerDiameter;
        }
        /**
        * get the prodDensity - 密度
        * @return the prodDensity
        */
        public BigDecimal getProdDensity() {
        return this.prodDensity;
        }

        /**
        * set the prodDensity - 密度
        */
        public void setProdDensity(BigDecimal prodDensity) {
        this.prodDensity = prodDensity;
        }
        /**
        * get the productProcessId - 首道加工工序
        * @return the productProcessId
        */
        public String getProductProcessId() {
        return this.productProcessId;
        }

        /**
        * set the productProcessId - 首道加工工序
        */
        public void setProductProcessId(String productProcessId) {
        this.productProcessId = productProcessId;
        }
        /**
        * get the netWeight - 重量
        * @return the netWeight
        */
        public BigDecimal getNetWeight() {
        return this.netWeight;
        }

        /**
        * set the netWeight - 重量
        */
        public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
        }
        /**
        * get the customerId - 客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 客户名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 客户名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }

        public String getMatInnerId() {
                return matInnerId;
        }

        public void setMatInnerId(String matInnerId) {
                this.matInnerId = matInnerId;
        }

        public String getTargetHandPointId() {
                return targetHandPointId;
        }

        public void setTargetHandPointId(String targetHandPointId) {
                this.targetHandPointId = targetHandPointId;
        }

        public String getLocationId() {
                return locationId;
        }

        public void setLocationId(String locationId) {
                this.locationId = locationId;
        }

        public String getLocationName() {
                return locationName;
        }

        public void setLocationName(String locationName) {
                this.locationName = locationName;
        }

        public String getFactoryOrderNum() {
                return factoryOrderNum;
        }

        public void setFactoryOrderNum(String factoryOrderNum) {
                this.factoryOrderNum = factoryOrderNum;
        }

        public String getPiceNum() {
                return piceNum;
        }

        public void setPiceNum(String piceNum) {
                this.piceNum = piceNum;
        }

        public String getFactoryArea() {
                return factoryArea;
        }

        public void setFactoryArea(String factoryArea) {
                this.factoryArea = factoryArea;
        }

        /**
         * get the specsDesc - 规格表述
         * @return the specsDesc
         */
        public String getSpecsDesc() {
                return this.specsDesc;
        }

        /**
         * set the specsDesc - 规格表述
         */
        public void setSpecsDesc(String specsDesc) {
                this.specsDesc = specsDesc;
        }

        public String getCustPartId() {
                return custPartId;
        }

        public void setCustPartId(String custPartId) {
                this.custPartId = custPartId;
        }

        public String getCustPartName() {
                return custPartName;
        }

        public void setCustPartName(String custPartName) {
                this.custPartName = custPartName;
        }

        public String getmPackId() {
                return mPackId;
        }

        public void setmPackId(String mPackId) {
                this.mPackId = mPackId;
        }


        public String getPurOrderNum() {
                return purOrderNum;
        }

        public void setPurOrderNum(String purOrderNum) {
                this.purOrderNum = purOrderNum;
        }

        public String getLadingBillRemark() {
                return ladingBillRemark;
        }

        public void setLadingBillRemark(String ladingBillRemark) {
                this.ladingBillRemark = ladingBillRemark;
        }

        public String getBillingMethod() {
                return billingMethod;
        }

        public void setBillingMethod(String billingMethod) {
                this.billingMethod = billingMethod;
        }

        public String getSegName() {
                return segName;
        }

        public void setSegName(String segName) {
                this.segName = segName;
        }

        public String getTargetHandPointName() {
                return targetHandPointName;
        }

        public void setTargetHandPointName(String targetHandPointName) {
                this.targetHandPointName = targetHandPointName;
        }

        public String getFirstMachineCode() {
                return firstMachineCode;
        }

        public void setFirstMachineCode(String firstMachineCode) {
                this.firstMachineCode = firstMachineCode;
        }

        public String getLogisticPlanType() {
                return logisticPlanType;
        }

        public void setLogisticPlanType(String logisticPlanType) {
                this.logisticPlanType = logisticPlanType;
        }

        public String getLabelId() {
                return labelId;
        }

        public void setLabelId(String labelId) {
                this.labelId = labelId;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setAllocateVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("allocateVehicleNo")), allocateVehicleNo));
                setAllocVehicleSeq(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("allocVehicleSeq")), allocVehicleSeq));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
                setOutPackFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outPackFlag")), outPackFlag));
                setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
                setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
                setPutinType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putinType")), putinType));
                setInnerDiameter(NumberUtils.toBigDecimal(StringUtils.toString(map.get("innerDiameter")), innerDiameter));
                setProdDensity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("prodDensity")), prodDensity));
                setProductProcessId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productProcessId")), productProcessId));
                setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
                setTargetHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointId")), targetHandPointId));
                setTargetHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointName")), targetHandPointName));
                setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
                setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
                setFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryOrderNum")), factoryOrderNum));
                setPiceNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("piceNum")), piceNum));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
                setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
                setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
                setCustPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartId")), custPartId));
                setCustPartName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartName")), custPartName));
                setmPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mPackId")), mPackId));
                setPurOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("purOrderNum")), purOrderNum));
                setLadingBillRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillRemark")), ladingBillRemark));
                setBillingMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billingMethod")), billingMethod));
                setFirstMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("firstMachineCode")), firstMachineCode));
                setLogisticPlanType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("logisticPlanType")), logisticPlanType));
                setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));

}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("allocateVehicleNo",StringUtils.toString(allocateVehicleNo, eiMetadata.getMeta("allocateVehicleNo")));
                map.put("allocVehicleSeq",StringUtils.toString(allocVehicleSeq, eiMetadata.getMeta("allocVehicleSeq")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("packId",StringUtils.toString(packId, eiMetadata.getMeta("packId")));
                map.put("outPackFlag",StringUtils.toString(outPackFlag, eiMetadata.getMeta("outPackFlag")));
                map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
                map.put("warehouseName",StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
                map.put("putinType",StringUtils.toString(putinType, eiMetadata.getMeta("putinType")));
                map.put("innerDiameter",StringUtils.toString(innerDiameter, eiMetadata.getMeta("innerDiameter")));
                map.put("prodDensity",StringUtils.toString(prodDensity, eiMetadata.getMeta("prodDensity")));
                map.put("productProcessId",StringUtils.toString(productProcessId, eiMetadata.getMeta("productProcessId")));
                map.put("netWeight",StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("matInnerId",StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
                map.put("targetHandPointId",StringUtils.toString(targetHandPointId, eiMetadata.getMeta("targetHandPointId")));
                map.put("targetHandPointName",StringUtils.toString(targetHandPointName, eiMetadata.getMeta("targetHandPointName")));
                map.put("locationId",StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
                map.put("locationName",StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
                map.put("factoryOrderNum",StringUtils.toString(factoryOrderNum, eiMetadata.getMeta("factoryOrderNum")));
                map.put("piceNum",StringUtils.toString(piceNum, eiMetadata.getMeta("piceNum")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("specsDesc", StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
                map.put("tradeCode", StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
                map.put("prodTypeId", StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
                map.put("custPartId", StringUtils.toString(custPartId, eiMetadata.getMeta("custPartId")));
                map.put("custPartName", StringUtils.toString(custPartName, eiMetadata.getMeta("custPartName")));
                map.put("mPackId", StringUtils.toString(mPackId, eiMetadata.getMeta("mPackId")));
                map.put("purOrderNum", StringUtils.toString(purOrderNum, eiMetadata.getMeta("purOrderNum")));
                map.put("ladingBillRemark", StringUtils.toString(ladingBillRemark, eiMetadata.getMeta("ladingBillRemark")));
                map.put("billingMethod", StringUtils.toString(billingMethod, eiMetadata.getMeta("billingMethod")));
                map.put("firstMachineCode", StringUtils.toString(firstMachineCode, eiMetadata.getMeta("firstMachineCode")));
                map.put("logisticPlanType", StringUtils.toString(logisticPlanType, eiMetadata.getMeta("logisticPlanType")));
                map.put("labelId", StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));


        return map;

}
}