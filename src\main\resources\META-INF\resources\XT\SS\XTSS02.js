$(function () {
    // 业务单元号查询
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings:{
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings:{
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [

            ]
        }
    };

});