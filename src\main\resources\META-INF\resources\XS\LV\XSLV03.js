$(function () {


    //为树头部过滤器设置数据源
    var dataSource = new kendo.data.DataSource({
        transport: {
            read: {
                url: IPLATUI.CONTEXT_PATH + "/service/XSLV0300/search",
                type: 'POST',
                dataType: "json",
                contentType: "application/json"
            },
            parameterMap: function (options, operation) {
                var info = new EiInfo();
                info.set("inqu_status-0-orgCname", $("#filterText").val());
                return info.toJSONString();
            }
        },
        schema: {
            data: function (response) {
                ajaxEi = EiInfo.parseJSONObject(response);
                return ajaxEi.getBlock("result").getMappedRows();
            }
        },
        pageSize: 10,
        serverFiltering: true
    });

    $("#filterText").kendoAutoComplete({
        dataSource: dataSource,
        minLength: 1,
        template: '#:orgEname#-#:text#',
        enforceMinLength: true,
        select: function (e) {
            var _data = e.dataItem || {};
            var tree = $('#categoryTree').data('kendoTreeView');
            expandtree(tree, _data);
        },
        dataTextField: "text"
    });

    /**
     * 展开树
     */
    var expandtree = function (tree, data) {
        if (!data) {
            return;
        }
        var info = new EiInfo();
        info.set("inqu_status-0-orgId", data.label);
        // 查找点击分类的树路径
        EiCommunicator.send("XSLV0300", "expandPath", info, {
            // 服务调用成功后的回调函数 onSuccess
            onSuccess: function (eiInfo) {
                var rows = eiInfo.getBlock('result').getMappedRows();
                var labels = [];
                for (var i = 0; i < rows.length; i++) {
                    labels.push(rows[i].LABEL);
                }
                ;
                labels = labels.reverse();
                var treeOrgId = labels[labels.length - 1];
                // 根据树路径逐级展开分类树
                tree.expandPath(labels, function () {
                    // 展开成功后选中对应的树节点
                    var barDataItem = tree.dataSource.get(treeOrgId);
                    var barElement = tree.findByUid(barDataItem.uid);
                    tree.select(barElement);
                    var text = barDataItem.text;
                    $("[name = 'inqu_status-0-orgId']").val(treeOrgId);
                    $("[name = 'inqu_status-0-orgCname']").val(text);
                    resultGrid.dataSource.page(1);
                });
            },
            // 服务调用失败后的回调函数 onFail
            // errorMsg 是平台格式化后的异常消息， status， e的含义和$.ajax的含义相同，请参考jQuery文档
            onFail: function (errorMsg, status, e) {
                // 调用发生异常
                NotificationUtil(errorMsg, "error");
            }
        });
    };
    /**
     * 为树上结点设置，点击操作时，将组织机构id和Cname传递给右侧查询去，同时查询，并将数据渲染到右侧的数据集上
     * @type {{categoryTree: {select: IPLATUI.EFTree.categoryTree.select}}}
     */
    IPLATUI.EFTree = {
        "categoryTree": {
            select: function (e) {
                var _data = this.dataItem(e.node);
                var labelValue = _data.label;
                var textValue = _data.text;
                $("#inqu_status-0-orgId").val(labelValue);
                $("#inqu_status-0-orgCname").val(textValue);

                //将组织机构的中英文名填到创建用户组的前后缀去
                $("#categoryEname").val("@" + _data.ename);
                $("#categoryCname").val("[" + textValue+"]");

                resultGrid.dataSource.page(1);
            }
        }
    }

    IPLATUI.EFGrid = {
        "result": {
            //为添加用户组设置弹窗，并添加判断
            beforeAdd: function (e) {
                e.preventDefault();
                var orgId = $("#inqu_status-0-orgId").val();
                var orgCnameValue = $("#inqu_status-0-orgCname").val();
                if (orgId == "" || orgId == null || orgCnameValue == "组织机构") {
                    IPLAT.alert("请选择一个组织机构!");
                } else {
                    $("#createUserGroup").data("kendoWindow").open().center();
                    $("#createUserGroup_wnd_title").html("正在为: [" + orgCnameValue + "] 添加用户组");
                }
            }
        }
    }
    //为查询按钮设置自定义查询事件
    $("#QUERY").on("click", function (e) {
        var orgId = $("#inqu_status-0-orgId").val();
        if (orgId == "" || orgId == null||"root" == orgId) {
            IPLAT.alert("查询前需要选择组织机构!");
        } else {
            resultGrid.dataSource.page(1);
        }
    });

    //为查询按钮设置自定义查询事件
    $("#QUERYBASE").on("click", function (e) {
        basesGrid.dataSource.page(1);
    });
    /**
     * 将选中的父用户组回填到创建用户组的input中
     */
    $("#addParentGroup").on("click", function (e) {
        var parentGroupCname,parentGroupId,parentGroupEname;
        var isChecked = basesGrid.getCheckedRows()[0].toJSON();
        if (isChecked) {
            parentGroupCname = isChecked.groupCname;
            parentGroupEname = isChecked.groupEname;
            parentGroupId = isChecked.id;
            IPLAT.EFPopupInput.setAllFields($(add_baseGroup), parentGroupId, parentGroupCname)
            $("#addBaseGroup").data("kendoWindow").close();
        }
    });

    $("#addNode").on("click", function (e) {
        //第一步：创建用户组
        var groupEname, groupCname;
        groupEname = $("#groupEname").val() + $("#categoryEname").val();
        groupCname = $("#categoryCname").val() + $("#groupCname").val();
        var ei = new EiInfo();
        ei.set("result-0-groupEname", groupEname);
        ei.set("result-0-groupCname", groupCname);
        ei.set("result-0-parentId", IPLAT.EFPopupInput.value($(add_baseGroup)));
        ei.set("result-0-orgId", $("#inqu_status-0-orgId").val());
        EiCommunicator.send("XSLV03","insertGroup",ei,{
            onSuccess: function(ei){
                IPLAT.alert(ei.getMsg());
                if (ei.getStatus() >= 0) {
                    resultGrid.dataSource.page(1);
                }
            },onFail:function(ei){
                IPLAT.alert(ei.getMsg());
            }
        });
        $("#createUserGroup").data("kendoWindow").close();
    });
});
