package com.baosight.imom.common.utils;

import com.baosight.imom.li.rl.dao.LIRL0100;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SwitchUtils extends ServiceBase {

    /**
     * @Service
     * @description 获取开关值
     * @param segNo, switchType
     * @return java.lang.String
     * @throws
     */
    public String getProcessSwitchValue(String segNo, String switchName, Dao dao) {

        Map<String, String> map = new HashMap<>();
        if (!ObjectUtils.isEmpty(segNo) && !ObjectUtils.isEmpty(switchName)) {
            map.put("segNo", segNo);
            map.put("processSwitchName", switchName);
        }else{
            return "-1";
        }

        List<Map> list = dao.query(LIRL0100.QUERY_SWITCH, map);
        if(list.size() > 0){
            Map<String, String> result = (Map<String, String>) list.get(0);
            return result.get("switchValue");
        } else {
            return "-1";
        }
    }
}
