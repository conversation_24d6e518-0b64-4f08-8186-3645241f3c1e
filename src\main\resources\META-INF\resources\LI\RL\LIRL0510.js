$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
        //清空子项表格
        subResultGrid.removeRows(subResultGrid.getDataItems());
    });

    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            },
            beforeAdd: function (e) {
            },
            afterAdd: function (e) {
            },
            beforeEdit: function (e) {
                //不可编辑
                e.preventDefault();
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
            },
            afterEdit: function (e) {
            },
            onCheckRow: function (e) {
                if(e.checked){
                    //勾选，查询
                    let eiInfo = new EiInfo();
                    eiInfo.setByNode("inqu");
                    eiInfo.addBlock(resultGrid.getCheckedBlockData());
                    IMOMUtil.submitGridsData("subResult", "LIRL0510", "querySubResult", false, function (ei) {
                        subResultGrid.setEiInfo(ei);
                        subResultGrid.refresh();
                    }, eiInfo, false);
                }else{
                    //取消勾选，清空子项表格
                    subResultGrid.removeRows(subResultGrid.getDataItems());
                }
            }
        },
        "subResult": {
            loadComplete: function (grid) {
            },
            beforeRequest: function (grid) {
                if (grid.type === "read") {
                    console.log(grid);
                    //自定义查询
                    if (resultGrid.getCheckedRows().length > 0) {
                        //不使用默认查询
                        grid.preventDefault();
                        //勾选，查询
                        let eiInfo = new EiInfo();
                        eiInfo.setByNode("inqu");
                        eiInfo.addBlock(resultGrid.getCheckedBlockData());
                        IMOMUtil.submitGridsData("subResult", "LIRL0510", "querySubResult", false, function (ei) {
                            subResultGrid.setEiInfo(ei);
                            subResultGrid.refresh();
                        }, eiInfo, false);
                    }
                }
            }

        }
    }

    IPLATUI.EFWindow = {
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                let $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                let iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                let segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                let $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                let iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                let dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                let row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId").val(row[0].userNum);
                    $("#inqu_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });


    //仓库弹窗
    IMOMUtil.windowTemplate({
        windowId: "warehouseInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-warehouseCode").val(rows[0].stockCode);
                $("#inqu_status-0-warehouseName").val(rows[0].stockName);
            }
        }
    });

})