package com.baosight.imom.vg.dm.domain;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.baosight.imom.common.vg.domain.Tvgdm0601;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

/**
 * 设备报警信息表
 */
public class VGDM0601 extends Tvgdm0601 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0601.query";
    /**
     * 根据报警ID查询数据
     */
    public static final String QUERY_BY_ID = "VGDM0601.queryById";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0601.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0601.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0601.update";
    /**
     * 删除
     */
    public static final String DELETE_FOR_BACK = "VGDM0601.delForBack";
    /**
     * 修改报警信息
     */
    public static final String UPDATE_ALARM = "VGDM0601.updateAlarm";
    /**
     * 修改推送信息
     */
    public static final String UPDATE_PUSH = "VGDM0601.updatePush";
    /**
     * 查询待推送的报警信息
     */
    public static final String QUERY_FOR_PUSH = "VGDM0601.queryForPush";
    /**
     * 查询待推送管理的报警信息
     */
    public static final String QUERY_FOR_MANAGE_PUSH = "VGDM0601.queryForManagePush";

    @Override
    public String getCheckStatus() {
        return this.getConfirmStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

    /**
     * 根据报警编号查询报警信息
     *
     * @param alarmId 报警编号
     * @return 报警信息或null
     */
    public static VGDM0601 queryByAlarmId(Dao dao, String alarmId) {
        Map<String, String> queryMap = new HashMap<>(1);
        queryMap.put("alarmId", alarmId);
        List list = dao.query(QUERY_BY_ID, queryMap);
        if (CollectionUtils.isNotEmpty(list)) {
            return (VGDM0601) list.get(0);
        }
        return null;
    }

    /**
     * 从tag信息复制报警信息
     *
     * @param tagInfo tag信息
     */
    public void copyFromAlarm(VGDM0301 tagInfo) {
        // 设置业务单元
        this.setSegNo(tagInfo.getSegNo());
        this.setUnitCode(tagInfo.getUnitCode());
        // 设置节点描述
        this.setAlarmTagDesc(tagInfo.getTagDesc());
        // 设置设备信息
        this.setEArchivesNo(tagInfo.getEArchivesNo());
        this.setEquipmentName(tagInfo.getEquipmentName());
        // 设置分部设备信息
        this.setDeviceCode(tagInfo.getDeviceCode());
        this.setDeviceName(tagInfo.getDeviceName());
    }
}
