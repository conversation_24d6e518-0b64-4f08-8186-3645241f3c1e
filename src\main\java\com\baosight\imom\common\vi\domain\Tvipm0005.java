/**
 * Generate time : 2024-12-31 15:35:00
 * Version : 1.0
 */
package com.baosight.imom.common.vi.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvipm0005
 */
public class Tvipm0005 extends DaoEPBase {

    private String standardHoursSeqId = " ";        /* 标准工时序列号*/
    private String machineCode = " ";        /* 机组代码*/
    private String machineName = " ";        /* 机组名称*/
    private String standardWorkingHoursStatus = "10";        /* 标准工时状态*/
    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String processCategory = " ";        /* 工序大类代码*/
    private String processCategoryName = " ";        /* 工序大类名称*/
    private Integer stripCount = 1;        /* 分条数*/
    private String partId = " ";        /* 物料号*/
    private String prodTypeId = " ";        /* 材质（品种）*/
    private String prodTypeName = " ";        /* 材质名称（品种名称）*/
    private String surfaceGrade = " ";        /* 内外板（表面等级）*/
    private BigDecimal tensile = new BigDecimal("0");        /* 抗拉强度*/
    private String specsDesc = " ";        /* 规格表述*/
    private String processMark = " ";        /* 加工剪切标志（精整分流标志）*/
    private BigDecimal sph = new BigDecimal(0);        /* sph值(片/h)每小时加工片数(落料和精剪)*/
    private BigDecimal processSpeed = new BigDecimal("0");        /* 加工速度(米/分钟)*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String dataType = "10";        /* 数据类型(10实时数据20每天平均)*/
    private String processOrderId = " ";        /* 生产工单号*/
    private String packId = " ";                /* 原料捆包号*/
    private String inPartId = " ";              /* 原料物料号*/
    private String inSpecsDesc = " ";           /* 原料规格*/
    private BigDecimal netWeight = new BigDecimal("0"); /* 净重*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("standardHoursSeqId");
        eiColumn.setDescName("标准工时序列号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("standardWorkingHoursStatus");
        eiColumn.setDescName("标准工时状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategoryName");
        eiColumn.setDescName("工序大类名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stripCount");
        eiColumn.setDescName("分条数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("材质（品种）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeName");
        eiColumn.setDescName("材质名称（品种名称）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("surfaceGrade");
        eiColumn.setDescName("内外板（表面等级）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tensile");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("抗拉强度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("规格表述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processMark");
        eiColumn.setDescName("加工剪切标志（精整分流标志）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sph");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("sph值(片/h)每小时加工片数(落料和精剪)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSpeed");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工速度(米/分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataType");
        eiColumn.setDescName("数据类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("原料捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inPartId");
        eiColumn.setDescName("原料物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inSpecsDesc");
        eiColumn.setDescName("原料规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvipm0005() {
        initMetaData();
    }

    /**
     * get the standardHoursSeqId - 标准工时序列号
     *
     * @return the standardHoursSeqId
     */
    public String getStandardHoursSeqId() {
        return this.standardHoursSeqId;
    }

    /**
     * set the standardHoursSeqId - 标准工时序列号
     */
    public void setStandardHoursSeqId(String standardHoursSeqId) {
        this.standardHoursSeqId = standardHoursSeqId;
    }

    /**
     * get the machineCode - 机组代码
     *
     * @return the machineCode
     */
    public String getMachineCode() {
        return this.machineCode;
    }

    /**
     * set the machineCode - 机组代码
     */
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    /**
     * get the machineName - 机组名称
     *
     * @return the machineName
     */
    public String getMachineName() {
        return this.machineName;
    }

    /**
     * set the machineName - 机组名称
     */
    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    /**
     * get the standardWorkingHoursStatus - 标准工时状态
     *
     * @return the standardWorkingHoursStatus
     */
    public String getStandardWorkingHoursStatus() {
        return this.standardWorkingHoursStatus;
    }

    /**
     * set the standardWorkingHoursStatus - 标准工时状态
     */
    public void setStandardWorkingHoursStatus(String standardWorkingHoursStatus) {
        this.standardWorkingHoursStatus = standardWorkingHoursStatus;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the processCategory - 工序大类代码
     *
     * @return the processCategory
     */
    public String getProcessCategory() {
        return this.processCategory;
    }

    /**
     * set the processCategory - 工序大类代码
     */
    public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
    }

    /**
     * get the processCategoryName - 工序大类名称
     *
     * @return the processCategoryName
     */
    public String getProcessCategoryName() {
        return this.processCategoryName;
    }

    /**
     * set the processCategoryName - 工序大类名称
     */
    public void setProcessCategoryName(String processCategoryName) {
        this.processCategoryName = processCategoryName;
    }

    /**
     * get the stripCount - 分条数
     *
     * @return the stripCount
     */
    public Integer getStripCount() {
        return this.stripCount;
    }

    /**
     * set the stripCount - 分条数
     */
    public void setStripCount(Integer stripCount) {
        this.stripCount = stripCount;
    }

    /**
     * get the partId - 物料号
     *
     * @return the partId
     */
    public String getPartId() {
        return this.partId;
    }

    /**
     * set the partId - 物料号
     */
    public void setPartId(String partId) {
        this.partId = partId;
    }

    /**
     * get the prodTypeId - 材质（品种）
     *
     * @return the prodTypeId
     */
    public String getProdTypeId() {
        return this.prodTypeId;
    }

    /**
     * set the prodTypeId - 材质（品种）
     */
    public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
    }

    /**
     * get the prodTypeName - 材质名称（品种名称）
     *
     * @return the prodTypeName
     */
    public String getProdTypeName() {
        return this.prodTypeName;
    }

    /**
     * set the prodTypeName - 材质名称（品种名称）
     */
    public void setProdTypeName(String prodTypeName) {
        this.prodTypeName = prodTypeName;
    }

    /**
     * get the surfaceGrade - 内外板（表面等级）
     *
     * @return the surfaceGrade
     */
    public String getSurfaceGrade() {
        return this.surfaceGrade;
    }

    /**
     * set the surfaceGrade - 内外板（表面等级）
     */
    public void setSurfaceGrade(String surfaceGrade) {
        if (surfaceGrade == null) {
            surfaceGrade = " ";
        }
        this.surfaceGrade = surfaceGrade;
    }

    /**
     * get the tensile - 抗拉强度
     *
     * @return the tensile
     */
    public BigDecimal getTensile() {
        return this.tensile;
    }

    /**
     * set the tensile - 抗拉强度
     */
    public void setTensile(BigDecimal tensile) {
        this.tensile = tensile;
    }

    /**
     * get the specsDesc - 规格表述
     *
     * @return the specsDesc
     */
    public String getSpecsDesc() {
        return this.specsDesc;
    }

    /**
     * set the specsDesc - 规格表述
     */
    public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
    }

    /**
     * get the processMark - 加工剪切标志（精整分流标志）
     *
     * @return the processMark
     */
    public String getProcessMark() {
        return this.processMark;
    }

    /**
     * set the processMark - 加工剪切标志（精整分流标志）
     */
    public void setProcessMark(String processMark) {
        if (processMark == null) {
            processMark = " ";
        }
        this.processMark = processMark;
    }

    /**
     * get the sph - sph值(片/h)每小时加工片数(落料和精剪)
     *
     * @return the sph
     */
    public BigDecimal getSph() {
        return this.sph;
    }

    /**
     * set the sph - sph值(片/h)每小时加工片数(落料和精剪)
     */
    public void setSph(BigDecimal sph) {
        this.sph = sph;
    }

    /**
     * get the processSpeed - 加工速度(米/分钟)
     *
     * @return the processSpeed
     */
    public BigDecimal getProcessSpeed() {
        return this.processSpeed;
    }

    /**
     * set the processSpeed - 加工速度(米/分钟)
     */
    public void setProcessSpeed(BigDecimal processSpeed) {
        this.processSpeed = processSpeed;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the dataType - 数据类型
     *
     * @return the dataType
     */
    public String getDataType() {
        return this.dataType;
    }

    /**
     * set the dataType - 数据类型
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * get the processOrderId - 生产工单号
     */
    public String getProcessOrderId() {
        return this.processOrderId;
    }

    /**
     * set the processOrderId - 生产工单号
     */
    public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
    }

    /**
     * get the packId - 原料捆包号
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 原料捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the inPartId - 原料物料号
     */
    public String getInPartId() {
        return this.inPartId;
    }

    /**
     * set the inPartId - 原料物料号
     */
    public void setInPartId(String inPartId) {
        this.inPartId = inPartId;
    }

    /**
     * get the inSpecsDesc - 原料规格
     */
    public String getInSpecsDesc() {
        return this.inSpecsDesc;
    }

    /**
     * set the inSpecsDesc - 原料规格
     */
    public void setInSpecsDesc(String inSpecsDesc) {
        this.inSpecsDesc = inSpecsDesc;
    }

    /**
     * get the netWeight - 净重
     */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
     * set the netWeight - 净重
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setStandardHoursSeqId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("standardHoursSeqId")), standardHoursSeqId));
        setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
        setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
        setStandardWorkingHoursStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("standardWorkingHoursStatus")), standardWorkingHoursStatus));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
        setProcessCategoryName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategoryName")), processCategoryName));
        setStripCount(NumberUtils.toInteger(StringUtils.toString(map.get("stripCount")), stripCount));
        setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
        setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
        setProdTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeName")), prodTypeName));
        setSurfaceGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("surfaceGrade")), surfaceGrade));
        setTensile(NumberUtils.toBigDecimal(StringUtils.toString(map.get("tensile")), tensile));
        setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
        setProcessMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processMark")), processMark));
        setSph(NumberUtils.toBigDecimal(StringUtils.toString(map.get("sph")), sph));
        setProcessSpeed(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processSpeed")), processSpeed));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setDataType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataType")), dataType));
        setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setInPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inPartId")), inPartId));
        setInSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inSpecsDesc")), inSpecsDesc));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("standardHoursSeqId", StringUtils.toString(standardHoursSeqId, eiMetadata.getMeta("standardHoursSeqId")));
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("machineName", StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("standardWorkingHoursStatus", StringUtils.toString(standardWorkingHoursStatus, eiMetadata.getMeta("standardWorkingHoursStatus")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("processCategory", StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
        map.put("processCategoryName", StringUtils.toString(processCategoryName, eiMetadata.getMeta("processCategoryName")));
        map.put("stripCount", StringUtils.toString(stripCount, eiMetadata.getMeta("stripCount")));
        map.put("partId", StringUtils.toString(partId, eiMetadata.getMeta("partId")));
        map.put("prodTypeId", StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
        map.put("prodTypeName", StringUtils.toString(prodTypeName, eiMetadata.getMeta("prodTypeName")));
        map.put("surfaceGrade", StringUtils.toString(surfaceGrade, eiMetadata.getMeta("surfaceGrade")));
        map.put("tensile", StringUtils.toString(tensile, eiMetadata.getMeta("tensile")));
        map.put("specsDesc", StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
        map.put("processMark", StringUtils.toString(processMark, eiMetadata.getMeta("processMark")));
        map.put("sph", StringUtils.toString(sph, eiMetadata.getMeta("sph")));
        map.put("processSpeed", StringUtils.toString(processSpeed, eiMetadata.getMeta("processSpeed")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("dataType", StringUtils.toString(dataType, eiMetadata.getMeta("dataType")));
        map.put("processOrderId", StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("inPartId", StringUtils.toString(inPartId, eiMetadata.getMeta("inPartId")));
        map.put("inSpecsDesc", StringUtils.toString(inSpecsDesc, eiMetadata.getMeta("inSpecsDesc")));
        map.put("netWeight", StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));

        return map;

    }
}