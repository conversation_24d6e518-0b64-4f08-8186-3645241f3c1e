/**
 * Generate time : 2024-10-14 10:47:43
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids1103;
import org.apache.tools.ant.types.resources.Last;

import java.util.Map;

/**
 * TLIDS1103
 *
 */
public class LIDS1103 extends Tlids1103 {
    public static final String QUERY = "LIDS1103.query";
    public static final String COUNT = "LIDS1103.count";
    public static final String INSERT = "LIDS1103.insert";
    public static final String UPDATE = "LIDS1103.update";
    public static final String DELETE = "LIDS1103.delete";
    public static final String GET_LAST_RELEASE_RECORD_BY_CRANE_ID = "LIDS1103.getLastReleaseRecordByCraneId";
    public static final String QUERY_INTERFACE_LIST = "LIDS1103.queryInterfaceList";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS1103() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}