/**
 * Generate time : 2024-09-27 11:07:46
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0307 车辆登记提单信息表
 *
 */
public class LIRL0307 extends DaoEPBase {
    public static final String QUERY = "LIRL0307.query";
    public static final String QUERY_MOBILE = "LIRL0307.queryMobile";
    public static final String QUERY_MOBILE_ALL = "LIRL0307.queryMobileAll";
    public static final String QUERY_DELIVER_TYPE = "LIRL0307.queryDeliverType";
    public static final String QUERY_PUTOUT_ID = "LIRL0307.queryPutoutId";
    public static final String INSERT = "LIRL0307.insert";
    public static final String UPDATE = "LIRL0307.update";
    public static final String DELETE = "LIRL0307.delete";

    private String segNo = " ";        /* 账套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String checkId = " ";        /* 车辆流水号*/
    private String voucherNum = " ";        /* 提单号（多个）*/
    private String deliverType = " ";        /* 交货方式（10：自提、20：代运）*/
    private String deliverTypeName = " ";        /* 交货方式名称*/
    private String packId = " ";        /* 捆包号*/
    private String matInnerId = " ";        /* 材料管理号*/
    private String prodTypeId = " ";        /* 品种附属码*/
    private String prodTypeName = " ";        /* 品种附属码名称*/
    private String shopsign = " ";        /* 牌号*/
    private String specDesc = " ";        /* 规格*/
    private BigDecimal weight = new BigDecimal(0.000000);        /* 重量*/
    private BigDecimal quantity = new BigDecimal(0.000000);        /* 数量*/
    private String customerId = " ";        /* 客户代码*/
    private String customerName = " ";        /* 客户名称*/
    private String locationId = " ";        /* 库位代码*/
    private String locationName = " ";        /* 库位名称*/
    private String handPointId = " ";        /* 装卸点代码*/
    private String handPointName = " ";        /* 装卸点名称*/

    private String documenterPerson = " ";        /* 制单人工号*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private String dUserNum = " ";        /* 租户ID*/
    private String dUserName = " ";        /* 租户ID*/

    /**
     * the constructor
     */
    public LIRL0307() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkId");
        eiColumn.setDescName("车辆流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("提单号（多个）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliverType");
        eiColumn.setDescName("交货方式（10：自提、20：代运）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliverTypeName");
        eiColumn.setDescName("交货方式名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("matInnerId");
        eiColumn.setDescName("材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeName");
        eiColumn.setDescName("品种附属码名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointId");
        eiColumn.setDescName("装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointName");
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("documenterPerson");
        eiColumn.setDescName("制单人工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dUserNum");
        eiColumn.setDescName("客户分户号代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dUserName");
        eiColumn.setDescName("客户分户号简称");
        eiMetadata.addMeta(eiColumn);




    }


    public String getdUserNum() {
        return dUserNum;
    }

    public void setdUserNum(String dUserNum) {
        this.dUserNum = dUserNum;
    }

    public String getdUserName() {
        return dUserName;
    }

    public void setdUserName(String dUserName) {
        this.dUserName = dUserName;
    }

    /**
     * get the segNo - 账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the checkId - 车辆流水号
     * @return the checkId
     */
    public String getCheckId() {
        return this.checkId;
    }

    /**
     * set the checkId - 车辆流水号
     */
    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    /**
     * get the voucherNum - 提单号（多个）
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 提单号（多个）
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the deliverType - 交货方式（10：自提、20：代运）
     * @return the deliverType
     */
    public String getDeliverType() {
        return this.deliverType;
    }

    /**
     * set the deliverType - 交货方式（10：自提、20：代运）
     */
    public void setDeliverType(String deliverType) {
        this.deliverType = deliverType;
    }

    /**
     * get the deliverTypeName - 交货方式名称
     * @return the deliverTypeName
     */
    public String getDeliverTypeName() {
        return this.deliverTypeName;
    }

    /**
     * set the deliverTypeName - 交货方式名称
     */
    public void setDeliverTypeName(String deliverTypeName) {
        this.deliverTypeName = deliverTypeName;
    }

    /**
     * get the packId - 捆包号
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the matInnerId - 材料管理号
     * @return the matInnerId
     */
    public String getMatInnerId() {
        return this.matInnerId;
    }

    /**
     * set the matInnerId - 材料管理号
     */
    public void setMatInnerId(String matInnerId) {
        this.matInnerId = matInnerId;
    }

    /**
     * get the prodTypeId - 品种附属码
     * @return the prodTypeId
     */
    public String getProdTypeId() {
        return this.prodTypeId;
    }

    /**
     * set the prodTypeId - 品种附属码
     */
    public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
    }

    /**
     * get the prodTypeName - 品种附属码名称
     * @return the prodTypeName
     */
    public String getProdTypeName() {
        return this.prodTypeName;
    }

    /**
     * set the prodTypeName - 品种附属码名称
     */
    public void setProdTypeName(String prodTypeName) {
        this.prodTypeName = prodTypeName;
    }

    /**
     * get the shopsign - 牌号
     * @return the shopsign
     */
    public String getShopsign() {
        return this.shopsign;
    }

    /**
     * set the shopsign - 牌号
     */
    public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
    }

    /**
     * get the specDesc - 规格
     * @return the specDesc
     */
    public String getSpecDesc() {
        return this.specDesc;
    }

    /**
     * set the specDesc - 规格
     */
    public void setSpecDesc(String specDesc) {
        this.specDesc = specDesc;
    }

    /**
     * get the weight - 重量
     * @return the weight
     */
    public BigDecimal getWeight() {
        return this.weight;
    }

    /**
     * set the weight - 重量
     */
    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    /**
     * get the quantity - 数量
     * @return the quantity
     */
    public BigDecimal getQuantity() {
        return this.quantity;
    }

    /**
     * set the quantity - 数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * get the customerId - 客户代码
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the customerName - 客户名称
     * @return the customerName
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * set the customerName - 客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * get the locationId - 库位代码
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locationName - 库位名称
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the handPointId - 装卸点代码
     * @return the handPointId
     */
    public String getHandPointId() {
        return this.handPointId;
    }

    /**
     * set the handPointId - 装卸点代码
     */
    public void setHandPointId(String handPointId) {
        this.handPointId = handPointId;
    }

    /**
     * get the handPointName - 装卸点名称
     * @return the handPointName
     */
    public String getHandPointName() {
        return this.handPointName;
    }

    /**
     * set the handPointName - 装卸点名称
     */
    public void setHandPointName(String handPointName) {
        this.handPointName = handPointName;
    }

    /**
     * get the documenterPerson - 制单人工号
     * @return the documenterPerson
     */
    public String getDocumenterPerson() {
        return this.documenterPerson;
    }

    /**
     * set the documenterPerson - 制单人工号
     */
    public void setDocumenterPerson(String documenterPerson) {
        this.documenterPerson = documenterPerson;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }


    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setCheckId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkId")), checkId));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setDeliverType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliverType")), deliverType));
        setDeliverTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliverTypeName")), deliverTypeName));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
        setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
        setProdTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeName")), prodTypeName));
        setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
        setSpecDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specDesc")), specDesc));
        setWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("weight")), weight));
        setQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("quantity")), quantity));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointId")), handPointId));
        setHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointName")), handPointName));
        setDocumenterPerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("documenterPerson")), documenterPerson));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setdUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dUserNum")), dUserNum));
        setdUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dUserName")), dUserName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("checkId", StringUtils.toString(checkId, eiMetadata.getMeta("checkId")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("deliverType", StringUtils.toString(deliverType, eiMetadata.getMeta("deliverType")));
        map.put("deliverTypeName", StringUtils.toString(deliverTypeName, eiMetadata.getMeta("deliverTypeName")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("matInnerId", StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
        map.put("prodTypeId", StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
        map.put("prodTypeName", StringUtils.toString(prodTypeName, eiMetadata.getMeta("prodTypeName")));
        map.put("shopsign", StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
        map.put("specDesc", StringUtils.toString(specDesc, eiMetadata.getMeta("specDesc")));
        map.put("weight", StringUtils.toString(weight, eiMetadata.getMeta("weight")));
        map.put("quantity", StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("handPointId", StringUtils.toString(handPointId, eiMetadata.getMeta("handPointId")));
        map.put("handPointName", StringUtils.toString(handPointName, eiMetadata.getMeta("handPointName")));
        map.put("documenterPerson", StringUtils.toString(documenterPerson, eiMetadata.getMeta("documenterPerson")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("dUserName", StringUtils.toString(dUserName, eiMetadata.getMeta("dUserName")));
        map.put("dUserNum", StringUtils.toString(dUserNum, eiMetadata.getMeta("dUserNum")));

        return map;

    }
}