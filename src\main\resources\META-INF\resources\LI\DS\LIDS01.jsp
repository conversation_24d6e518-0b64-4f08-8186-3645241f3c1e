<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-warehouseBusinessType" cname="仓库业务类型" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3" value=" " disabled="true"/>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-warehouseCode" cname="仓库代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-warehouseName" cname="仓库名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-wareHouseBusinessType" cname="仓库业务类型" type="hidden"/>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true">
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="stockCode" cname="仓库代码" align="left" width="150" enable="true"
                             primaryKey="true"/>
                <EF:EFColumn ename="stockName" cname="仓库名称" align="center" width="200" enable="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>
</EF:EFPage>
