$(function () {
    IPLATUI.EFGrid = {
        "result2": {
            /**
             * EFGrid新增后触发的事件
             */
            afterAdd: function (e) {
                $.each(e.items, function (index, item) {
                    item.internalCode = "";
                });
            },
            loadComplete: function () {
                $("#QUERY").on("click", function () {
                    result2Grid.dataSource.page(1);
                });
                $("#RESET").click(function () {
                    var inquNode = $("#inqu2")[0];
                    IPLAT.clearNode(inquNode);
                });
            },
            //双击选中
            onRowDblClick: function (e) {
                var windowId = $("#inqu2_status-0-windowId").val();
                if (IPLAT.isBlankString(windowId)) {
                    // 设置默认值
                    windowId = "processKey";
                }
                //双击选中前先把双击的数据勾选上
                result2Grid.setCheckedRows(e.row);
                //关闭下拉框
                window.parent[windowId + "Window"].close();
            }
        },
    };
});
