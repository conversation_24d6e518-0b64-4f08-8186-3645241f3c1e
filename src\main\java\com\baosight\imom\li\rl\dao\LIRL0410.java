/**
* Generate time : 2025-04-26 19:52:37
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* lirl0410
*
 *排队叫号备份表
*/
public class LIRL0410 extends DaoEPBase {
    public static final String INSERT = "LIRL0410.insert";
                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private Long queueNumber = 0L;		/* 顺序号*/
                private String carTraceNo = " ";		/* 车辆跟踪号*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String voucherNum = " ";		/* 提单号*/
                private String priorityLevel = "99";		/* 优先级（默认99）*/
                private String queueDate = " ";		/* 排序时间（当前时间）*/
                private String backDate = " ";		/* 备份时间（当前时间）*/
                private String targetHandPointId = " ";		/* 目标装卸点*/
                private String factoryArea = " ";		/* 厂区*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("queueNumber");
        eiColumn.setDescName("顺序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("提单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("priorityLevel");
        eiColumn.setDescName("优先级（默认99）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("queueDate");
        eiColumn.setDescName("排序时间（当前时间）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("backDate");
        eiColumn.setDescName("备份时间（当前时间）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointId");
        eiColumn.setDescName("目标装卸点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0410() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the queueNumber - 顺序号
        * @return the queueNumber
        */
        public Long getQueueNumber() {
        return this.queueNumber;
        }

        /**
        * set the queueNumber - 顺序号
        */
        public void setQueueNumber(Long queueNumber) {
        this.queueNumber = queueNumber;
        }
        /**
        * get the carTraceNo - 车辆跟踪号
        * @return the carTraceNo
        */
        public String getCarTraceNo() {
        return this.carTraceNo;
        }

        /**
        * set the carTraceNo - 车辆跟踪号
        */
        public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the voucherNum - 提单号
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 提单号
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the priorityLevel - 优先级（默认99）
        * @return the priorityLevel
        */
        public String getPriorityLevel() {
        return this.priorityLevel;
        }

        /**
        * set the priorityLevel - 优先级（默认99）
        */
        public void setPriorityLevel(String priorityLevel) {
        this.priorityLevel = priorityLevel;
        }
        /**
        * get the queueDate - 排序时间（当前时间）
        * @return the queueDate
        */
        public String getQueueDate() {
        return this.queueDate;
        }

        /**
        * set the queueDate - 排序时间（当前时间）
        */
        public void setQueueDate(String queueDate) {
        this.queueDate = queueDate;
        }
        /**
        * get the backDate - 备份时间（当前时间）
        * @return the backDate
        */
        public String getBackDate() {
        return this.backDate;
        }

        /**
        * set the backDate - 备份时间（当前时间）
        */
        public void setBackDate(String backDate) {
        this.backDate = backDate;
        }
        /**
        * get the targetHandPointId - 目标装卸点
        * @return the targetHandPointId
        */
        public String getTargetHandPointId() {
        return this.targetHandPointId;
        }

        /**
        * set the targetHandPointId - 目标装卸点
        */
        public void setTargetHandPointId(String targetHandPointId) {
        this.targetHandPointId = targetHandPointId;
        }
        /**
        * get the factoryArea - 厂区
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setQueueNumber(NumberUtils.toLong(StringUtils.toString(map.get("queueNumber")), queueNumber));
                setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setPriorityLevel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("priorityLevel")), priorityLevel));
                setQueueDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("queueDate")), queueDate));
                setBackDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("backDate")), backDate));
                setTargetHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointId")), targetHandPointId));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("queueNumber",StringUtils.toString(queueNumber, eiMetadata.getMeta("queueNumber")));
                map.put("carTraceNo",StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("priorityLevel",StringUtils.toString(priorityLevel, eiMetadata.getMeta("priorityLevel")));
                map.put("queueDate",StringUtils.toString(queueDate, eiMetadata.getMeta("queueDate")));
                map.put("backDate",StringUtils.toString(backDate, eiMetadata.getMeta("backDate")));
                map.put("targetHandPointId",StringUtils.toString(targetHandPointId, eiMetadata.getMeta("targetHandPointId")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));

return map;

}
}