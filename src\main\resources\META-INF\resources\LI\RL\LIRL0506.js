$(function () {

    //获取当前登录人对应的业务单元
    /*var unitInfo =  IMOMUtil.fillUnitInfo();*/

    // TODO 查询 按钮事件
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "loadingPointNo",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "装卸点编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "handPointInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂内装卸点查询"
                            })
                        }
                    }
                },
                {

                    field: "timeStart",
                    editor: function (container, options) {
                        var input = $("<input><input/>");
                        input.appendTo(container);
                        input.kendoTimePicker({
                            format: "HH:mm:ss",//时刻选择器显示选项的格式
                            close: function(e) {
                                var dateObj = e.sender.value();//获取到Date对象
                                var timeStr = dateObj.getHours()+":"+dateObj.getMinutes()+":"+dateObj.getSeconds();//Date格式化为自定义格式
                                //$("#ef_grid_result").data("kendoGrid").setCellValue(options.model, "timeStart", timeStr);//回填值
                                resultGrid.setCellValue(options.model, "timeStart", timeStr);
                            }
                        });
                    }
                },
                {

                    field: "timeEnd",
                    editor: function (container, options) {
                        var input = $("<input><input/>");
                        input.appendTo(container);
                        input.kendoTimePicker({
                            format: "HH:mm:ss",//时刻选择器显示选项的格式
                            close: function(e) {
                                var dateObj = e.sender.value();//获取到Date对象
                                var timeStr = dateObj.getHours()+":"+dateObj.getMinutes()+":"+dateObj.getSeconds();//Date格式化为自定义格式
                                //$("#ef_grid_result").data("kendoGrid").setCellValue(options.model, "timeEnd", timeStr);//回填值
                                resultGrid.setCellValue(options.model, "timeEnd", timeStr);
                            }
                        });
                    }
                },
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // 获取勾选数据，
                $("#INSERTSAVEN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0506", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                // 获取勾选数据，
                $("#UPDATESAVEN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("修改失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0506", "update", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                /**
                 * 删除
                 */
                $("#DELETESAVEN").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("删除失败，原因[请勾选记录后再进行删除！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0506", "delete", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0506", "confirm", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0506", "confirmNo", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            /**
             * 编辑结束，关闭单元格编辑状态时的事件
             */
            afterEdit: function (e) {
                if ("reservationIdentity" == e.field) {
                    resultGrid.setCellValue(e.row, "customerId", "");
                    resultGrid.setCellValue(e.row, "customerName", "");
                }
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            }
        },
    };
    IPLATUI.EFWindow = {
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }


            }
        },
        "unitInfo01": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo01");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    resultGrid.setCellValue(editorModel, "segNo", row[0].segNo);
                    resultGrid.setCellValue(editorModel, "segName", row[0].segName);
                    resultGrid.setCellValue(editorModel, "unitCode", row[0].unitCode);
                    resultGrid.setCellValue(editorModel, "loadingPointNo", "");
                    resultGrid.setCellValue(editorModel, "loadingPointName", "");
                    /**for (i = 0; i < row.length; i++) {
                        var varModel = row[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    **/
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
    }
    //厂内装卸点管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "handPointInfo",
        _open: function (e, iframejQuery) {
            const unitCode = editorModel.unitCode;
            const segName = editorModel.segName;
            const segNo = editorModel.segNo;
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            // iframejQuery("#inqu_status-0-loadFlag").val("1");
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            // 显示 div
            iframejQuery("#BTN3").css('display', 'block');
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                var handPointId = "";
                var handPointName = "";
                for (let i = 0; i < rows.length; i++) {
                   if (handPointId == null || IPLAT.isBlankString(handPointId) ){
                        handPointId = rows[i].handPointId;
                        handPointName = rows[i].handPointName;
                   }else{
                       handPointId = handPointId + "," + rows[i].handPointId;
                       handPointName = handPointName + "," + rows[i].handPointName;
                   }
                }
                resultGrid.setCellValue(editorModel, "loadingPointNo", handPointId);
                resultGrid.setCellValue(editorModel, "loadingPointName", handPointName);
            }
        }
    });
});
