<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-24 10:43:48
   		Version :  1.0
		tableName :meli.tlids1103 
		 UNIT_CODE  VARCHAR, 
		 SEG_NO  VARCHAR, 
		 CRANE_ID  VARCHAR, 
		 CRANE_NAME  VARCHAR, 
		 ACTION_TYPE  VARCHAR, 
		 GRAB_SYS_ID  VARCHAR, 
		 RELEASE_SYS_ID  VARCHAR, 
		 X_VALUE  VARCHAR, 
		 Y_VALUE  VARCHAR, 
		 Z_VALUE  VARCHAR, 
		 LOCATION_ID  VARCHAR, 
		 LOCATION_NAME  VARCHAR, 
		 WEIGHT  DECIMAL, 
		 UPLOAD_TIME  VARCHAR, 
		 STATUS  VARCHAR, 
		 REMAR<PERSON>  VARCHAR, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR
	-->
<sqlMap namespace="tlids1103">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids1103">
		SELECT
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				CRANE_ID	as "craneId",  <!-- 行车编号 -->
				CRANE_NAME	as "craneName",  <!-- 行车名称 -->
				ACTION_TYPE	as "actionType",  <!-- 动作类型 -->
				GRAB_SYS_ID	as "grabSysId",  <!-- 抓取流水号 -->
				RELEASE_SYS_ID	as "releaseSysId",  <!-- 释放流水号 -->
				X_VALUE	as "x_value",  <!-- X轴值 -->
				Y_VALUE	as "y_value",  <!-- Y轴值 -->
				Z_VALUE	as "z_value",  <!-- Z轴值 -->
				LOCATION_ID	as "locationId",  <!-- 库位代码 -->
				LOCATION_NAME	as "locationName",  <!-- 库位名称 -->
				WEIGHT	as "weight",  <!-- 重量 -->
				UPLOAD_TIME	as "uploadTime",  <!-- 上传时间 -->
				STATUS	as "status",  <!-- 接收状态 -->
				REMARK	as "remark",  <!-- 备注 -->
				UUID	as "uuid",  <!-- uuid -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime" <!-- 记录修改时间 -->
		FROM meli.tlids1103 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids1103 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="actionType">
			ACTION_TYPE = #actionType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="grabSysId">
			GRAB_SYS_ID = #grabSysId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="releaseSysId">
			RELEASE_SYS_ID = #releaseSysId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_value">
			X_VALUE = #x_value#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_value">
			Y_VALUE = #y_value#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="z_value">
			Z_VALUE = #z_value#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationId">
			LOCATION_ID = #locationId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationName">
			LOCATION_NAME = #locationName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="weight">
			WEIGHT = #weight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uploadTime">
			UPLOAD_TIME = #uploadTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids1103 (UNIT_CODE,  <!-- 业务单元代码 -->
										SEG_NO,  <!-- 系统账套 -->
										CRANE_ID,  <!-- 行车编号 -->
										CRANE_NAME,  <!-- 行车名称 -->
										ACTION_TYPE,  <!-- 动作类型 -->
										GRAB_SYS_ID,  <!-- 抓取流水号 -->
										RELEASE_SYS_ID,  <!-- 释放流水号 -->
										X_VALUE,  <!-- X轴值 -->
										Y_VALUE,  <!-- Y轴值 -->
										Z_VALUE,  <!-- Z轴值 -->
										LOCATION_ID,  <!-- 库位代码 -->
										LOCATION_NAME,  <!-- 库位名称 -->
										WEIGHT,  <!-- 重量 -->
										UPLOAD_TIME,  <!-- 上传时间 -->
										STATUS,  <!-- 接收状态 -->
										REMARK,  <!-- 备注 -->
										UUID,  <!-- uuid -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME  <!-- 记录修改时间 -->
										)		 
	    VALUES (#unitCode#, #segNo#, #craneId#, #craneName#, #actionType#, #grabSysId#, #releaseSysId#, #x_value#, #y_value#, #z_value#, #locationId#, #locationName#, #weight#, #uploadTime#, #status#, #remark#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids1103 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlids1103 
		SET 
		UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					CRANE_ID	= #craneId#,   <!-- 行车编号 -->  
					CRANE_NAME	= #craneName#,   <!-- 行车名称 -->  
					ACTION_TYPE	= #actionType#,   <!-- 动作类型 -->  
					GRAB_SYS_ID	= #grabSysId#,   <!-- 抓取流水号 -->  
					RELEASE_SYS_ID	= #releaseSysId#,   <!-- 释放流水号 -->  
					X_VALUE	= #x_value#,   <!-- X轴值 -->  
					Y_VALUE	= #y_value#,   <!-- Y轴值 -->  
					Z_VALUE	= #z_value#,   <!-- Z轴值 -->  
					LOCATION_ID	= #locationId#,   <!-- 库位代码 -->  
					LOCATION_NAME	= #locationName#,   <!-- 库位名称 -->  
					WEIGHT	= #weight#,   <!-- 重量 -->  
					UPLOAD_TIME	= #uploadTime#,   <!-- 上传时间 -->  
					STATUS	= #status#,   <!-- 接收状态 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
								REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#  <!-- 记录修改时间 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>