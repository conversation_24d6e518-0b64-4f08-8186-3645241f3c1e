package com.baosight.imom.vg.dm.domain;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.common.vg.domain.Tvgdm1002;
import com.baosight.imom.vi.pm.domain.VIPM0007;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 捆包作业时间表
 */
public class VGDM1002 extends Tvgdm1002 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM1002.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1002.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1002.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1002.update";
    /**
     * 删除-逻辑删除
     */
    public static final String UPDATE_FOR_DEL = "VGDM1002.updForDel";
    /**
     * 查询未加工捆包数
     */
    public static final String COUNT_NOT_DO = "VGDM1002.countNotDo";
    /**
     * 更新上料时间
     */
    public static final String UPDATE_UP_TIME = "VGDM1002.updateUpTime";

    /**
     * 计算原料捆包长度
     *
     * @return 长度m
     */
    public BigDecimal calPackLength() {
        if (!"SL".equals(this.getProcessCategory())) {
            return BigDecimal.ONE;
        }
        // 根据规格获取厚度、宽度
        LogUtils.log("规格：" + this.getSpecsDesc());
        String[] arr = this.getSpecsDesc().split("\\*");
        BigDecimal thickness = new BigDecimal(arr[0]);
        BigDecimal width = new BigDecimal(arr[1]);
        LogUtils.log("厚度：" + thickness + "宽度：" + width);
        // 原料长度（m） = (原料重量（吨）*1000000)/（原料密度7.85（t/m³）*原料宽度（mm）*原料厚度（mm））
        BigDecimal up = this.getNetWeight().multiply(new BigDecimal(1000000));
        BigDecimal down = thickness.multiply(width).multiply(new BigDecimal("7.85"));
        BigDecimal length = up.divide(down, 2, RoundingMode.HALF_UP);
        LogUtils.log("原料长度：" + length);
        return length;
    }

    /**
     * 获取产出信息
     *
     * @param dao        dao
     * @param resultList 产出信息
     * @return 是否有排刀（投料捆包是否有指定的产出规格信息）
     */
    public boolean queryOutPack(Dao dao, List<VIPM0007> resultList) {
        return this.queryOutPack(dao, resultList, true);
    }

    /**
     * 获取产出信息
     *
     * @param dao        dao
     * @param resultList 产出信息
     * @param checkKnife 是否校验排刀
     * @return 是否有排刀（投料捆包是否有指定的产出规格信息）
     */
    public boolean queryOutPack(Dao dao, List<VIPM0007> resultList, boolean checkKnife) {
        // 查询产出信息
        Map<String, String> map = new HashMap<>();
        map.put("processOrderId", this.getProcessOrderId());
        LogUtils.log("查询工单产出信息：" + this.getProcessOrderId());
        List<VIPM0007> outPackList = dao.query(VIPM0007.QUERY, map);
        if (CollectionUtils.isEmpty(outPackList)) {
            throw new PlatException("未找到工单产出信息:" + this.getProcessOrderId());
        }
        if (!this.checkIsSL()) {
            LogUtils.log("非纵切，返回所有产出信息");
            resultList.addAll(outPackList);
            return false;
        }
        // 纵切调用接口获取排刀方案
        List<Map<String, Object>> planList = this.queryKnifeList();
        if (CollectionUtils.isEmpty(planList)) {
            LogUtils.log("未查询到排刀方案");
            resultList.addAll(outPackList);
            return false;
        }
        LogUtils.log("当前排刀：" + this.getCurrentKnife());
        // 根据排刀方案总物料号找到工单产出表中对应产出信息
        for (Map<String, Object> plan : planList) {
            String partId = MapUtils.getStr(plan, "partId");
            long qty = MapUtils.getInt(plan, "cutArticls");
            BigDecimal weight = MapUtils.getBigDecimal(plan, "distributeWeight");
            String suitCutSlitId = MapUtils.getString(plan, "suitCutSlitId", "1");
            LogUtils.log("排刀方案排刀号：" + suitCutSlitId + "，物料号：" + partId + "，分条数：" + qty + "，重量：" + weight);
            if (checkKnife && !suitCutSlitId.equals(this.getCurrentKnife())) {
                LogUtils.log("非当前排刀");
                continue;
            }
            for (VIPM0007 outPack : outPackList) {
                if (partId.equals(outPack.getPartId())) {
                    LogUtils.log("找到对应产出信息：" + outPack.getUuid());
                    outPack.setProductProcessQty(qty);
                    outPack.setProductProcessWeight(weight);
                    resultList.add(outPack);
                    break;
                }
            }
        }
        if (CollectionUtils.isEmpty(resultList)) {
            LogUtils.log("排刀方案未找到对应产出信息");
            resultList.addAll(outPackList);
            return false;
        }
        return true;
    }

    /**
     * 查询排刀方案
     *
     * @return 排刀方案
     */
    public List<Map<String, Object>> queryKnifeList() {
        // 纵切调用接口获取排刀方案
        EiInfo imcInfo = new EiInfo();
        imcInfo.set("segNo", this.getSegNo());
        imcInfo.set("packId", this.getPackId());
        imcInfo.set("processOrderId", this.getProcessOrderId());
        LogUtils.log("准备查询排刀方案：" + this.getProcessOrderId() + "，" + this.getPackId() + "，" + this.getSegNo());
        // 服务ID
        imcInfo.set(EiConstant.serviceId, "S_VI_PM_1024");
        EiInfo rtnInfo = EServiceManager.call(imcInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
        List<Map<String, Object>> planList = (List<Map<String, Object>>) rtnInfo.get("result");
        if (CollectionUtils.isEmpty(planList)) {
            LogUtils.log("未查询到排刀方案");
            return null;
        }
        return planList;
    }

    /**
     * 获取投料捆包数
     *
     * @param dao dao
     */
    public int queryInCount(Dao dao) {
        Map<String, String> map = new HashMap<>();
        map.put("processOrderId", this.getProcessOrderId());
        List aa = dao.query(VIPM0008.COUNT, map);
        int totalInCount = (Integer) aa.get(0);
        LogUtils.log("工单下投料捆包数：" + totalInCount);
        if (totalInCount == 0) {
            throw new PlatException("未找到工单的投料捆包:" + this.getProcessOrderId());
        }
        return totalInCount;
    }

    /**
     * 判断是否换刀
     *
     * @param updFlag 是否更新当前刀
     * @return 是否换刀
     */
    public boolean changeKnife(boolean updFlag) {
        List<String> knifeSortList = Arrays.asList(this.getKnifeSort().split(","));
        // 如果刀具数量大于 1，则需要判断是否换刀
        if (knifeSortList.size() > 1) {
            // 当前刀索引
            int index = knifeSortList.indexOf(this.getCurrentKnife());
            // 当前刀不是最后一个时，则换刀
            if (index >= 0 && index < knifeSortList.size() - 1) {
                LogUtils.log("需换刀");
                if (updFlag) {
                    this.setCurrentKnife(knifeSortList.get(index + 1));
                    LogUtils.log("换刀：" + this.getCurrentKnife());
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 是否为纵切
     *
     * @return bool
     */
    public boolean checkIsSL() {
        return StrUtil.isNotBlank(this.getProcessCategory()) && "SL".equals(this.getProcessCategory());
    }

    /**
     * 是否1650横切
     *
     * @return bool
     */
    public boolean checkIsCL01() {
        return StrUtil.isNotBlank(this.getEArchivesNo()) && "52CL01".equals(this.getEArchivesNo());
    }

    /**
     * 是否800横切
     *
     * @return bool
     */
    public boolean checkIsCL02() {
        return StrUtil.isNotBlank(this.getEArchivesNo()) && "52CL02".equals(this.getEArchivesNo());
    }

    /**
     * 是否2050横切
     *
     * @return bool
     */
    public boolean checkIsBL01() {
        return StrUtil.isNotBlank(this.getEArchivesNo()) && "52BL01".equals(this.getEArchivesNo());
    }

    /**
     * 是否为余料
     *
     * @return bool
     */
    public boolean checkIsLeftPack() {
        return this.checkEndType(EndType.LEFT);
    }

    /**
     * 是否为换刀
     *
     * @return bool
     */
    public boolean checkIsChangeKnife() {
        return this.checkEndType(EndType.CHANGE_KNIFE);
    }

    /**
     * 是否为退料
     *
     * @return bool
     */
    public boolean checkIsBackPack() {
        return this.checkEndType(EndType.BACK);
    }

    /**
     * 是否为尾包
     *
     * @return bool
     */
    public boolean checkIsEnd() {
        return this.checkEndType(EndType.TAIL);
    }

    /**
     * 是否为并包
     *
     * @return bool
     */
    public boolean checkIsUnited() {
        return StrUtil.isNotBlank(this.getUnitedPackId());
    }

    /**
     * 检验结束类型
     *
     * @param endType 0 正常结束，1退料，2余料，3换刀，4尾包，5并包
     * @return bool
     */
    private boolean checkEndType(String endType) {
        return StrUtil.isNotBlank(this.getEndType()) && endType.equals(this.getEndType());
    }

    /**
     * 计算横切落料加工工时
     *
     * @param dao dao
     * @return 更新后可上传IMC的实绩信息
     */
    public List<Map> calCLWorkHour(Dao dao) {
        List<VGDM1005> packList = VGDM1005.reCalQuantity(dao, this.getUuid(), this.getSegNo()
                , this.getEArchivesNo(), this.getNetWeight());
        if (CollectionUtils.isEmpty(packList)) {
            return null;
        }
        int count = packList.size();
        LogUtils.log("产出捆包数量：" + count);
        List<Map> packMapList = new ArrayList<>();
        // 计算总穿带时间
        long totalSeconds = this.querySumPrepare(dao);
        LogUtils.log("总穿带时间s：" + totalSeconds);
        // 平均穿带时间
        long averageSeconds = (totalSeconds / count) + 1;
        LogUtils.log("平均穿带时间s：" + averageSeconds);
        // 所有自动运行时间
        List<LocalDateTime> autoList = this.queryAutoList(dao);
        // 上一捆包产出时间-(第一条从开卷时间开始)
        LocalDateTime lastTime = LocalDateTime.parse(this.getStartTime(), DateUtils.FORMATTER_14);
        // 上一捆包生产时间
        long lastSeconds = 0;
        // 计算加工工时
        for (int i = 0; i < packList.size(); i++) {
            VGDM1005 outPack = packList.get(i);
            long seconds = 0;
            LogUtils.log("计算加工工时：" + outPack.getOutPackId());
            // 本次捆包产出时间
            LocalDateTime thisTime = LocalDateTime.parse(outPack.getRecCreateTime(), DateUtils.FORMATTER_14);
            // 第一个捆包
            if (i == 0) {
                // 捆包前的自动运行时间
                LocalDateTime autoTime = this.getAutoTime(autoList, lastTime, thisTime);
                // 第一个捆包的开始时间取第一次自动运行的开始时间
                if (autoTime != null) {
                    lastTime = autoTime;
                }
            }
            // 除第一个捆包后续都取上一捆包到本次的时间差
            seconds = ChronoUnit.SECONDS.between(lastTime, thisTime) + 1;
            if (seconds < 60 && lastSeconds >= 60) {
                LogUtils.log("时间太短" + seconds + ",取上一捆包时间");
                seconds = lastSeconds;
            }
            lastTime = thisTime;
            // 余料以开卷时间到生成时间计算
            if ("4".equals(outPack.getPackType())) {
                LogUtils.log("余料");
                LocalDateTime startTime = LocalDateTime.parse(this.getStartTime(), DateUtils.FORMATTER_14);
                seconds = ChronoUnit.SECONDS.between(startTime, thisTime) + 1;
            }
            lastSeconds = seconds;
            LogUtils.log("捆包加工时间s：" + seconds);
            if (seconds < 0) {
                seconds = 600;
            }
            long sumSeconds = seconds + averageSeconds;
            long minutes = (sumSeconds / 60) + 1;
            LogUtils.log("加工工时m：" + minutes);
            outPack.setProcessHour(new BigDecimal(minutes));
            // 待更新数据
            Map updMap = outPack.toMap();
            updMap.put("outPutPackId", outPack.getOutPackId());
            updMap.put("processOrderId", this.getProcessOrderId());
            updMap.put("quantity", outPack.getQuantity().intValue());
            RecordUtils.setRevisorSys(updMap);
            packMapList.add(updMap);
            if (!"4".equals(outPack.getPackType())) {
                // 更新上一捆包时间
                lastTime = thisTime;
            }
        }
        // 更新加工工时
        DaoUtils.updateBatch(dao, VGDM1005.UPDATE_HOUR, packMapList);
        return packMapList;
    }

    /**
     * 查询自动运行时间
     *
     * @param dao dao
     * @return 自动运行时间段
     */
    private List<LocalDateTime> queryAutoList(Dao dao) {
        List<LocalDateTime> autoList = new ArrayList<>();
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("relevanceId", this.getUuid());
        queryMap.put("procedureCode", "ZDYX");
        queryMap.put("orderBy", "START_TIME");
        List<VGDM1003> list = dao.query(VGDM1003.QUERY, queryMap);
        if (CollectionUtils.isEmpty(list)) {
            return autoList;
        }
        for (VGDM1003 auto : list) {
            // 排除时间为空数据
            if (StrUtil.isBlank(auto.getStartTime())) {
                continue;
            }
            autoList.add(LocalDateTime.parse(auto.getStartTime(), DateUtils.FORMATTER_23));
        }
        return autoList;
    }

    /**
     * 获取实绩前自动运行时间段
     *
     * @param autoList 所有自动运行时间
     * @param lastTime 上一捆包实绩时间
     * @param thisTime 捆包实绩时间
     * @return 在捆包实绩前的自动运行时间
     */
    private LocalDateTime getAutoTime(List<LocalDateTime> autoList, LocalDateTime lastTime, LocalDateTime thisTime) {
        List<LocalDateTime> timeList = new ArrayList<>();
        for (LocalDateTime startTime : autoList) {
            // 开始时间晚于上一时间且开始时间早于实绩时间且结束时间早于实绩时间
            if (startTime.isAfter(lastTime)
                    && startTime.isBefore(thisTime)) {
                return startTime;
            }
        }
        return null;
    }

    /**
     * 查询总准备时间
     *
     * @return 总准备时间
     */
    private long querySumPrepare(Dao dao) {
        Map<String, String> map = new HashMap<>();
        map.put("segNo", this.getSegNo());
        map.put("relevanceId", this.getUuid());
        List aa = dao.query(VGDM1003.SUM_PREPARE, map);
        return (Integer) aa.get(0);
    }
}
