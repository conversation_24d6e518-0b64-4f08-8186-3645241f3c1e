/**
 * Generate time : 2023-03-08 10:03:58
 * Version : 1.0
 */
package com.baosight.imom.xt.ss.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tvism0116
 * table comment : 生产审批属性配置表
 */
public class XTSS12 extends DaoEPBase {

    private String segNo = "";        /* 系统账套*/
    private String unitCode = "";        /* 业务单元代码*/
    private String recCreator = "";        /* 记录创建人*/
    private String recCreatorName = "";        /* 记录创建人姓名*/
    private String recCreateTime = "";        /* 记录创建时间*/
    private String recRevisor = "";        /* 记录修改人*/
    private String recRevisorName = "";        /* 记录修改人姓名*/
    private String recReviseTime = "";        /* 记录修改时间*/
    private String tenantUser = "";        /* 租户*/
    private Integer delFlag = 0;        /* 记录删除标记*/
    private String uuid = "";        /* ID*/
    private String processId = "";        /* 流程ID*/
    private String configName = "";        /* 流程名称*/
    private String nodeNo = "";        /* 节点号*/
    private String protRoleType = "";        /* 角色类型*/
    private String apprId = "";        /* 审批人工号*/
    private String apprName = "";        /* 审批人姓名*/
    private String nodeName = "";        /* 节点名称*/
    private String segName = "";        /* 业务单元简称*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;
        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("流程ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("configName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("流程名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nodeNo");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("节点号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("protRoleType");
        eiColumn.setFieldLength(3);
        eiColumn.setDescName("角色类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprId");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("审批人工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("审批人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nodeName");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("节点名称");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public XTSS12() {
        initMetaData();
    }

    /**
     * get the segNo - 系统账套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantUser - 租户
     *
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the delFlag - 记录删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the uuid - ID
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the processId - 流程ID
     *
     * @return the processId
     */
    public String getProcessId() {
        return this.processId;
    }

    /**
     * set the processId - 流程ID
     */
    public void setProcessId(String processId) {
        this.processId = processId;
    }

    /**
     * get the configName - 流程名称
     *
     * @return the configName
     */
    public String getConfigName() {
        return this.configName;
    }

    /**
     * set the configName - 流程名称
     */
    public void setConfigName(String configName) {
        this.configName = configName;
    }

    /**
     * get the nodeNo - 节点号
     *
     * @return the nodeNo
     */
    public String getNodeNo() {
        return this.nodeNo;
    }

    /**
     * set the nodeNo - 节点号
     */
    public void setNodeNo(String nodeNo) {
        this.nodeNo = nodeNo;
    }

    /**
     * get the protRoleType - 角色类型
     *
     * @return the protRoleType
     */
    public String getProtRoleType() {
        return this.protRoleType;
    }

    /**
     * set the protRoleType - 角色类型
     */
    public void setProtRoleType(String protRoleType) {
        this.protRoleType = protRoleType;
    }

    /**
     * get the apprId - 审批人工号
     *
     * @return the apprId
     */
    public String getApprId() {
        return this.apprId;
    }

    /**
     * set the apprId - 审批人工号
     */
    public void setApprId(String apprId) {
        this.apprId = apprId;
    }

    /**
     * get the apprName - 审批人姓名
     *
     * @return the apprName
     */
    public String getApprName() {
        return this.apprName;
    }

    /**
     * set the apprName - 审批人姓名
     */
    public void setApprName(String apprName) {
        this.apprName = apprName;
    }

    /**
     * get the nodeName - 节点名称
     *
     * @return the nodeName
     */
    public String getNodeName() {
        return this.nodeName;
    }

    /**
     * set the nodeName - 节点名称
     */
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setProcessId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processId")), processId));
        setConfigName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("configName")), configName));
        setNodeNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nodeNo")), nodeNo));
        setProtRoleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("protRoleType")), protRoleType));
        setApprId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprId")), apprId));
        setApprName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprName")), apprName));
        setNodeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nodeName")), nodeName));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));

    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("processId", StringUtils.toString(processId, eiMetadata.getMeta("processId")));
        map.put("configName", StringUtils.toString(configName, eiMetadata.getMeta("configName")));
        map.put("nodeNo", StringUtils.toString(nodeNo, eiMetadata.getMeta("nodeNo")));
        map.put("protRoleType", StringUtils.toString(protRoleType, eiMetadata.getMeta("protRoleType")));
        map.put("apprId", StringUtils.toString(apprId, eiMetadata.getMeta("apprId")));
        map.put("apprName", StringUtils.toString(apprName, eiMetadata.getMeta("apprName")));
        map.put("nodeName", StringUtils.toString(nodeName, eiMetadata.getMeta("nodeName")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));

        return map;

    }

    public static final String QUERY_FOR_AUDIT = "XTSS12.queryForAudit";
}