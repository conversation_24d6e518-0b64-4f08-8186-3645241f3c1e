<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0601">

    <sql id="queryResult">
        SELECT
        ALARM_ID as "alarmId",  <!-- 报警ID -->
        SCADA_NAME as "scadaName",  <!-- scada节点名 -->
        ALARM_TAG as "alarmTag",  <!-- 报警点 -->
        ALARM_TAG_DESC as "alarmTagDesc",  <!-- 报警点描述 -->
        ALARM_ADDRESS as "alarmAddress",  <!-- 报警地址 -->
        CONFIRM_STATUS as "confirmStatus",  <!-- 确认状态 -->
        CONFIRMOR as "confirmor",  <!-- 确认人 -->
        CONFIRMOR_NAME as "confirmorName",  <!-- 确认人姓名 -->
        CONFIRM_TIME as "confirmTime",  <!-- 确认时间 -->
        OCCUR_TIME as "occurTime",  <!-- 发生时间 -->
        RECOVER_TIME as "recoverTime",  <!-- 恢复时间 -->
        ALARM_TYPE as "alarmType",  <!-- 报警类型 -->
        ALARM_TAG_VALUE as "alarmTagValue",  <!-- 报警值 -->
        RECOVER_TAG_VALUE as "recoverTagValue",  <!-- 恢复值 -->
        PRIORITY as "priority",  <!-- 优先级 -->
        ALARM_STATE as "alarmState",  <!-- 报警状态 -->
        REPEAT_COUNT as "repeatCount",  <!-- 重复报警次数 -->
        ALARM_TYPEDM as "alarmTypedm",  <!-- 报警类型代码 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        IS_FAULT as "isFault",  <!-- 是否故障 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        PUSH_HANDLE_FLAG as "pushHandleFlag",  <!-- 推送处理标记 -->
        PUSH_HANDLE_TIME as "pushHandleTime",  <!-- 推送处理时间 -->
        PUSH_MANAGE_FLAG as "pushManageFlag",  <!-- 推送管理标记 -->
        PUSH_MANAGE_TIME as "pushManageTime"  <!-- 推送管理时间 -->
        FROM ${mevgSchema}.TVGDM0601
    </sql>

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="alarmId">
            ALARM_ID like concat('%',#alarmId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="alarmTag">
            ALARM_TAG like concat('%',#alarmTag#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="alarmState">
            ALARM_STATE = #alarmState#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="alarmTypedm">
            ALARM_TYPEDM = #alarmTypedm#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="confirmStatus">
            CONFIRM_STATUS = #confirmStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            DEVICE_CODE = #deviceCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="scadaName">
            SCADA_NAME = #scadaName#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="occurTimeStart">
            substr(OCCUR_TIME,1,19) &gt;= #occurTimeStart#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="occurTimeEnd">
            #occurTimeEnd# &gt;= substr(OCCUR_TIME,1,19)
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0601">
        <include refid="queryResult"/>
        WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                OCCUR_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0601 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryById" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0601">
        <include refid="queryResult"/>
        WHERE ALARM_ID = #alarmId#
    </select>

    <select id="queryForPush" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T6.ALARM_ID as "alarmId",  <!-- 报警ID -->
        T6.SCADA_NAME as "scadaName",  <!-- scada节点名 -->
        T6.ALARM_TAG as "alarmTag",  <!-- 报警点 -->
        T6.ALARM_TAG_DESC as "alarmTagDesc",  <!-- 报警点描述 -->
        T6.ALARM_ADDRESS as "alarmAddress",  <!-- 报警地址 -->
        T6.CONFIRM_STATUS as "confirmStatus",  <!-- 确认状态 -->
        T6.CONFIRMOR as "confirmor",  <!-- 确认人 -->
        T6.CONFIRMOR_NAME as "confirmorName",  <!-- 确认人姓名 -->
        T6.CONFIRM_TIME as "confirmTime",  <!-- 确认时间 -->
        T6.OCCUR_TIME as "occurTime",  <!-- 发生时间 -->
        T6.RECOVER_TIME as "recoverTime",  <!-- 恢复时间 -->
        T6.ALARM_TYPE as "alarmType",  <!-- 报警类型 -->
        T6.ALARM_TAG_VALUE as "alarmTagValue",  <!-- 报警值 -->
        T6.RECOVER_TAG_VALUE as "recoverTagValue",  <!-- 恢复值 -->
        T6.PRIORITY as "priority",  <!-- 优先级 -->
        T6.ALARM_STATE as "alarmState",  <!-- 报警状态 -->
        T6.REPEAT_COUNT as "repeatCount",  <!-- 重复报警次数 -->
        T6.ALARM_TYPEDM as "alarmTypedm",  <!-- 报警类型代码 -->
        T6.DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        T6.DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        T6.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T6.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T6.IS_FAULT as "isFault",  <!-- 是否故障 -->
        T6.UUID as "uuid",  <!-- 唯一编码 -->
        T6.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        T6.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        T6.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        T6.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        T6.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        T6.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        T6.TENANT_ID as "tenantId",  <!-- 租户ID -->
        T6.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        T6.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        T6.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T6.UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        T6.PUSH_HANDLE_FLAG as "pushHandleFlag",  <!-- 推送处理标记 -->
        T6.PUSH_HANDLE_TIME as "pushHandleTime",  <!-- 推送处理时间 -->
        T6.PUSH_MANAGE_FLAG as "pushManageFlag",  <!-- 推送管理标记 -->
        T6.PUSH_MANAGE_TIME as "pushManageTime",  <!-- 推送管理时间 -->
        T3.HANDLE_USER_ID as "handleUserId",  <!-- 处理人 -->
        T3.HANDLE_USER_NAME as "handleUserName",  <!-- 处理人姓名 -->
        T3.HANDLE_MOBILE as "handleMobile",  <!-- 处理人手机 -->
        T3.MACHINERY_HANDLE_ID as "machineryHandleId",  <!-- 机械处理人 -->
        T3.MACHINERY_HANDLE_NAME as "machineryHandleName",  <!-- 机械处理人姓名 -->
        T3.MACHINERY_HANDLE_MOBILE as "machineryHandleMobile",  <!-- 机械处理人手机 -->
        T4.PUSH_CONFIG_TYPE as "pushConfigType",  <!-- 推送配置类型 -->
        T4.PUSH_CONFIG_VALUE as "pushConfigValue",  <!-- 推送配置值 -->
        T4.HANDLE_TYPE as "handleType"  <!-- 处理人类型 -->
        FROM ${mevgSchema}.TVGDM0601 T6
        LEFT JOIN ${mevgSchema}.TVGDM0603 T3
        ON T6.SEG_NO = T3.SEG_NO
        AND T6.E_ARCHIVES_NO = T3.E_ARCHIVES_NO
        AND T3.STATUS = '10'
        AND T3.DEL_FLAG = '0'
        LEFT JOIN ${mevgSchema}.TVGDM0604 T4
        ON T4.SEG_NO = T6.SEG_NO
        AND T4.E_ARCHIVES_NO = T6.E_ARCHIVES_NO
        AND T4.TAG_ID = T6.ALARM_TAG
        AND T4.DEL_FLAG = '0'
        WHERE T6.SEG_NO = #segNo#
        AND T6.DEL_FLAG = '0'
        AND T6.CONFIRM_STATUS = '未确认'
        AND T6.ALARM_STATE = '未恢复'
        AND T6.PUSH_HANDLE_FLAG = '0'
        AND T3.UUID IS NOT NULL
        AND T3.HANDLE_MOBILE != ''
        AND T3.MACHINERY_HANDLE_MOBILE != ''
        AND T4.UUID IS NOT NULL
    </select>

    <select id="queryForManagePush" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T6.ALARM_ID as "alarmId",  <!-- 报警ID -->
        T3.MANAGE_USER_NAME as "scadaName",  <!-- scada节点名 借用为短信接收人姓名 -->
        T6.ALARM_TAG as "alarmTag",  <!-- 报警点 -->
        T6.ALARM_TAG_DESC as "alarmTagDesc",  <!-- 报警点描述 -->
        T3.MANAGE_MOBILE as "alarmAddress",  <!-- 报警地址 借用为手机号-->
        T6.CONFIRM_STATUS as "confirmStatus",  <!-- 确认状态 -->
        T6.CONFIRMOR as "confirmor",  <!-- 确认人 -->
        T6.CONFIRMOR_NAME as "confirmorName",  <!-- 确认人姓名 -->
        T6.CONFIRM_TIME as "confirmTime",  <!-- 确认时间 -->
        T6.OCCUR_TIME as "occurTime",  <!-- 发生时间 -->
        T6.RECOVER_TIME as "recoverTime",  <!-- 恢复时间 -->
        T6.ALARM_TYPE as "alarmType",  <!-- 报警类型 -->
        T6.ALARM_TAG_VALUE as "alarmTagValue",  <!-- 报警值 -->
        T6.RECOVER_TAG_VALUE as "recoverTagValue",  <!-- 恢复值 -->
        T6.PRIORITY as "priority",  <!-- 优先级 -->
        T6.ALARM_STATE as "alarmState",  <!-- 报警状态 -->
        T6.REPEAT_COUNT as "repeatCount",  <!-- 重复报警次数 -->
        T6.ALARM_TYPEDM as "alarmTypedm",  <!-- 报警类型代码 -->
        T6.DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        T6.DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        T6.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T6.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T6.IS_FAULT as "isFault",  <!-- 是否故障 -->
        T6.UUID as "uuid",  <!-- 唯一编码 -->
        T6.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        T6.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        T6.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        T6.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        T6.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        T6.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        T6.TENANT_ID as "tenantId",  <!-- 租户ID -->
        T6.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        T6.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        T6.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T6.UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        T6.PUSH_HANDLE_FLAG as "pushHandleFlag",  <!-- 推送处理标记 -->
        T6.PUSH_HANDLE_TIME as "pushHandleTime",  <!-- 推送处理时间 -->
        T6.PUSH_MANAGE_FLAG as "pushManageFlag",  <!-- 推送管理标记 -->
        T6.PUSH_MANAGE_TIME as "pushManageTime",  <!-- 推送管理时间 -->
        T3.MANAGE_USER_ID as "manageUserId",  <!-- 管理人 -->
        T3.MANAGE_USER_NAME as "manageUserName",  <!-- 管理人姓名 -->
        T3.MANAGE_MOBILE as "manageMobile"  <!-- 管理人手机 -->
        FROM ${mevgSchema}.TVGDM0601 T6
        LEFT JOIN ${mevgSchema}.TVGDM0603 T3
        ON T6.SEG_NO = T3.SEG_NO
        AND T6.E_ARCHIVES_NO = T3.E_ARCHIVES_NO
        AND T3.STATUS = '10'
        AND T3.DEL_FLAG = '0'
        WHERE T6.SEG_NO = #segNo#
        AND T6.DEL_FLAG = '0'
        AND T6.CONFIRM_STATUS = '未确认'
        AND T6.ALARM_STATE = '未恢复'
        AND T6.PUSH_HANDLE_FLAG = '1'
        AND T6.PUSH_MANAGE_FLAG = '0'
        AND STR_TO_DATE(T6.PUSH_HANDLE_TIME, '%Y%m%d%H%i%s') &lt;= NOW() - INTERVAL 8 HOUR
        AND T3.UUID IS NOT NULL
        AND T3.MANAGE_MOBILE != ''
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0601 (
        ALARM_ID,  <!-- 报警ID -->
        SCADA_NAME,  <!-- scada节点名 -->
        ALARM_TAG,  <!-- 报警点 -->
        ALARM_TAG_DESC,  <!-- 报警点描述 -->
        ALARM_ADDRESS,  <!-- 报警地址 -->
        CONFIRM_STATUS,  <!-- 确认状态 -->
        CONFIRMOR,  <!-- 确认人 -->
        CONFIRMOR_NAME,  <!-- 确认人姓名 -->
        CONFIRM_TIME,  <!-- 确认时间 -->
        OCCUR_TIME,  <!-- 发生时间 -->
        RECOVER_TIME,  <!-- 恢复时间 -->
        ALARM_TYPE,  <!-- 报警类型 -->
        ALARM_TAG_VALUE,  <!-- 报警值 -->
        RECOVER_TAG_VALUE,  <!-- 恢复值 -->
        PRIORITY,  <!-- 优先级 -->
        ALARM_STATE,  <!-- 报警状态 -->
        REPEAT_COUNT,  <!-- 重复报警次数 -->
        ALARM_TYPEDM,  <!-- 报警类型代码 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        PUSH_HANDLE_FLAG,  <!-- 推送处理标记 -->
        PUSH_HANDLE_TIME,  <!-- 推送处理时间 -->
        PUSH_MANAGE_FLAG,  <!-- 推送管理标记 -->
        PUSH_MANAGE_TIME  <!-- 推送管理时间 -->
        )
        VALUES (
        #alarmId#, #scadaName#, #alarmTag#, #alarmTagDesc#, #alarmAddress#, #confirmStatus#, #confirmor#,
        #confirmorName#, #confirmTime#, #occurTime#, #recoverTime#, #alarmType#, #alarmTagValue#, #recoverTagValue#,
        #priority#, #alarmState#, #repeatCount#, #alarmTypedm#, #deviceCode#,
        #deviceName#, #eArchivesNo#, #equipmentName#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#,
        #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#,
        #pushHandleFlag#, #pushHandleTime#, #pushManageFlag#, #pushManageTime#
        )
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0601 WHERE
        UUID = #uuid#
    </delete>

    <delete id="delForBack">
        DELETE FROM ${mevgSchema}.TVGDM0601 WHERE
        SEG_NO = #segNo#
        AND SCADA_NAME = #scadaName#
        AND ALARM_STATE = '已恢复'
        AND OCCUR_TIME != ''
        AND RECOVER_TIME != ''
        AND TIMESTAMPDIFF(SECOND ,OCCUR_TIME,RECOVER_TIME) &lt;= 60
        AND REC_CREATE_TIME &lt;= #minTime#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0601
        SET
        CONFIRM_STATUS = #confirmStatus#,   <!-- 确认状态 -->
        CONFIRMOR = #confirmor#,   <!-- 确认人 -->
        CONFIRMOR_NAME = #confirmorName#,   <!-- 确认人姓名 -->
        CONFIRM_TIME = #confirmTime#,   <!-- 确认时间 -->
        IS_FAULT = #isFault#,   <!-- 是否故障 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateAlarm">
        UPDATE ${mevgSchema}.TVGDM0601
        SET
        ALARM_ADDRESS = #alarmAddress#,   <!-- 报警地址 -->
        OCCUR_TIME = #occurTime#,   <!-- 发生时间 -->
        RECOVER_TIME = #recoverTime#,   <!-- 恢复时间 -->
        ALARM_TYPE = #alarmType#,   <!-- 报警类型 -->
        ALARM_TAG_VALUE = #alarmTagValue#,   <!-- 报警值 -->
        RECOVER_TAG_VALUE = #recoverTagValue#,   <!-- 恢复值 -->
        PRIORITY = #priority#,   <!-- 优先级 -->
        ALARM_STATE = #alarmState#,   <!-- 报警状态 -->
        REPEAT_COUNT = #repeatCount#,   <!-- 重复报警次数 -->
        ALARM_TYPEDM = #alarmTypedm#,   <!-- 报警类型代码 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updatePush">
        UPDATE ${mevgSchema}.TVGDM0601
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        PUSH_HANDLE_FLAG = #pushHandleFlag#,   <!-- 推送处理标记 -->
        PUSH_HANDLE_TIME = #pushHandleTime#,   <!-- 推送处理时间 -->
        PUSH_MANAGE_FLAG = #pushManageFlag#,   <!-- 推送管理标记 -->
        PUSH_MANAGE_TIME = #pushManageTime#   <!-- 推送管理时间 -->
        WHERE
        UUID = #uuid#
    </update>
</sqlMap>