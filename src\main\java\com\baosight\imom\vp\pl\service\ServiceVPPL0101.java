package com.baosight.imom.vp.pl.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.EasyExcelUtil;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.vp.pl.domain.VPPL0101;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServiceVPPL0101 extends ServiceBase {
    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VPPL0101().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, VPPL0101.QUERY, new VPPL0101());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo","");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    String chipId = MapUtils.getString(itemMap, "chipId","");
                    if (StringUtils.isBlank(chipId)) {
                        throw new PlatException("传入芯片ID为空，不可新增!");
                    }
                    String staffName = MapUtils.getString(itemMap, "staffName","");
                    if (StringUtils.isBlank(staffName)) {
                        throw new PlatException("传入人员姓名为空，不可新增!");
                    }
                    /*String workingHours = MapUtils.getString(itemMap, "workingHours","");
                    if (StringUtils.isBlank(workingHours)) {
                        throw new PlatException("传入当班时间为空，不可新增!");
                    }*/
                    Map queryMap = new HashMap();
                    //判断芯片ID不能重复
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("chipId",MapUtils.getString(itemMap,"chipId",""));
                    queryMap.put("delFlag" ,"0");
                    int count = super.count(VPPL0101.COUNT, queryMap);
                    if (count > 0){
                        throw new PlatException(MapUtils.getString(itemMap, "chipId")+"芯片ID重复!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, VPPL0101.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //查询状态，判断非新增状态数据不可修改
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    List<VPPL0101> count = this.dao.query(VPPL0101.QUERY,queryMap);
                    if (CollectionUtils.isNotEmpty(count)) {
                        String status = count.get(0).getStatus();
                        if (!"10".equals(status)){
                            throw new PlatException(MapUtils.getString(itemMap, "chipId")+",状态非新增状态，不可修改!");
                        }
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, VPPL0101.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                List<VPPL0101> count = this.dao.query(VPPL0101.QUERY,queryMap);
                if (CollectionUtils.isNotEmpty(count)) {
                    String status = count.get(0).getStatus();
                    if (!"10".equals(status)){
                        throw new PlatException(MapUtils.getString(itemMap, "chipId")+",状态非新增状态，不可删除!");
                    }
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, VPPL0101.UPDATE_STATUS);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo validateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可启用!");
                }
                //根据UUID查询状态，判断非新增状态数据不可生效
                Map queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                List<VPPL0101> count = this.dao.query(VPPL0101.QUERY,queryMap);
                if (CollectionUtils.isNotEmpty(count)) {
                    String status = count.get(0).getStatus();
                    if (!"10".equals(status)){
                        throw new PlatException(MapUtils.getString(itemMap, "chipId")+"状态非新增状态，不可生效!");
                    }
                }
                //状态变更为生效
                itemMap.put("status", "20");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, VPPL0101.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 反生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo deValidateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可反生效!");
                }
                //查询区域代码状态，判断非生效状态
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                List<VPPL0101> count = this.dao.query(VPPL0101.QUERY,queryMap);
                if (CollectionUtils.isNotEmpty(count)) {
                    String status = count.get(0).getStatus();
                    if (!"20".equals(status)){
                        throw new PlatException(MapUtils.getString(itemMap, "chipId") + "状态非生效状态，不可反生效!");
                    }
                }
                //状态变更为新增
                itemMap.put("status", "10");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, VPPL0101.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行反生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 后端导出
     */
    public EiInfo postExport(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map<String, Object> loginMap = new HashMap();
            loginMap.put("userId", UserSession.getUserId());
            loginMap.put("userName", UserSession.getLoginCName());
            loginMap.put("loginName", UserSession.getLoginName());
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            if (StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            List<Map<String, Object>> hashMapList = dao.queryAll(VPPL0101.QUERY_EXPORT, queryBlock);
            inInfo.addBlock("exportColumnBlock").addRows(hashMapList);
            inInfo.getBlock("exportColumnBlock").addBlockMeta(VPPL0101.exportBlockMeta());
            Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, hashMapList, loginMap);
            outInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);

            outInfo.setMsg("导出成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
