package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.constants.WorkFlowConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0201;
import com.baosight.imom.vg.dm.domain.VGDM0402;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 点检标准审批页面后台
 * @Date : 2024/9/19
 * @Version : 1.0
 */
public class ServiceVGDM0201 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0201.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0201().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        String processInstanceId = inInfo.getString("processInstanceId");
        if (!StrUtil.isBlank(processInstanceId)) {
            // 首页待审批跳转
            Map<String, String> map = new HashMap<>();
            map.put("processInstanceId", processInstanceId);
            map.put("apprStatus", MesConstant.Status.K60);
            map.put("loginId", UserSession.getLoginName());
            List list = dao.query(VGDM0201.QUERY, map);
            inInfo.getBlock(EiConstant.resultBlock).addRows(list);
        }
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        inInfo.setCell(EiConstant.queryBlock, 0, "loginId", UserSession.getLoginName());
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0201.QUERY, new VGDM0201());
    }

    /**
     * 审批通过
     */
    public EiInfo agree(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            VGDM0201 dbData;
            String userId = UserSession.getLoginName();
            // 待更新点检计划信息
            List<Map> updList = new ArrayList<>();
            // 工作流参数
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                String comment = block.getCellStr(i, "comment");
                if (StrUtil.isBlank(comment)) {
                    comment = "同意";
                }
                vgdm0201.fromMap(block.getRow(i));
                // 校验数据状态已提交
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0201, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K20);
                // 校验审批状态审批中
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVAL_STATUS, dbData.getApprStatus(), MesConstant.Status.K60);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.SPOT_CHECK_STANDARD);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "grant");
                paramMap.put("userId", userId);
                paramMap.put("comment", comment);
                paramMap.put("variables", new HashMap<>());
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 审批通过
                boolean finishFlag = WorkFlowUtils.addAuditPersons(dao, paramMap);
                // 流程结束后更新状态
                if (finishFlag) {
                    // 状态-生效
                    dbData.setSpotCheckStatus(MesConstant.Status.K30);
                    // 审批状态-审批通过
                    dbData.setApprStatus(MesConstant.Status.K70);
                    // 更新点检计划
                    Map tempMap = dbData.toMap();
                    RecordUtils.setRevisor(tempMap);
                    updList.add(tempMap);
                }
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, true);
            // 批量更新数据
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE_STATUS, block.getRows());
            // 批量更新点检标准
            DaoUtils.updateBatch(dao, VGDM0402.UPDATE_SPOT_CHECK_STANDARD, updList);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 审批驳回
     */
    public EiInfo reject(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            VGDM0201 dbData;
            String userId = UserSession.getLoginName();
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                String comment = block.getCellStr(i, "comment");
                if (StrUtil.isBlank(comment)) {
                    throw new PlatException("审核驳回时审批意见不能为空");
                }
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                // 校验数据状态已提交
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0201, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K20);
                // 校验审批状态审批中
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVAL_STATUS, dbData.getApprStatus(), MesConstant.Status.K60);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.SPOT_CHECK_STANDARD);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "reject");
                paramMap.put("userId", userId);
                paramMap.put("comment", comment);
                paramMap.put("variables", new HashMap<>());
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 状态-新增
                dbData.setSpotCheckStatus(MesConstant.Status.K10);
                // 审批状态-审批驳回
                dbData.setApprStatus(MesConstant.Status.K7X);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 批量更新
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE_STATUS, block.getRows());
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, false);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 审批撤销
     *
     * <p>对审批通过的数据清空审批信息，状态改为新增。
     */
    public EiInfo cancelAudit(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            VGDM0201 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                // 校验数据状态生效
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0201, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ACTIVE_STATUS, MesConstant.Status.K30);
                // 校验审批状态审批通过
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVED_STATUS, dbData.getApprStatus(), MesConstant.Status.K70);
                // 终止工作流
                WorkFlowUtils.deleteProcess(dbData.getProcessInstanceId());
                // 更新数据
                dbData.setApprStatus(" ");
                dbData.setProcessInstanceId(" ");
                dbData.setSpotCheckStatus(MesConstant.Status.K10);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE_STATUS, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
