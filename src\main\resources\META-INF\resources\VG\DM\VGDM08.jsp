<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<c:set var="reportAddress" value='<%=PlatApplicationContext.getProperty("reportAddress")%>'/>
<c:set var="loginCName" value='<%=UserSession.getLoginCName()%>'/>
<script>
    var reportAddress = "${reportAddress}";
    var loginCName = "${loginCName}";
</script>
<EF:EFPage prefix="imom">
    <jsp:attribute name="header">
        <style>
            .kendo-xplat-INSERT2,
            .kendo-xplat-INSERT3,
            .kendo-xplat-INSERT4,
            .kendo-xplat-INSERT5,
            .kendo-xplat-INSERT6,
            .kendo-xplat-INSERT7 {
                float: left;
            }
        </style>
    </jsp:attribute>
    <jsp:body>
        <EF:EFTab id="info">
            <div title="清单信息" id="info-1">
                <EF:EFRegion id="inqu" title="查询条件">
                    <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                    <div class="row">
                        <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                         readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                         center="true" required="true">
                        </EF:EFPopupInput>
                        <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                    required="true"/>
                        <EF:EFInput ename="inqu_status-0-overhaulPlanId" cname="检修计划单号" placeholder="模糊条件"
                                    colWidth="3"/>
                        <EF:EFSelect ename="inqu_status-0-overhaulQuality" cname="检修性质" colWidth="3"
                                     template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                     optionLabel="{valueField:'', textField:'全部'}">
                            <EF:EFCodeOption codeName="P024"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row">
                        <EF:EFDateSpan startName="inqu_status-0-overhaulStartDate"
                                       endName="inqu_status-0-overhaulEndDate" readonly="true"
                                       startCname="计划检修日期(起)" endCname="计划检修日期(止)"
                                       ratio="3:3" format="yyyy-MM-dd">
                        </EF:EFDateSpan>
                        <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                        <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                                         ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>



                    </div>
                    <div class="row">
                        <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
                        <EF:EFPopupInput originalInput="true" clear="false"
                                         containerId="deviceInfoMainQuery" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                                         cname="分部设备名称" colWidth="3"/>
                        <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                                       endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                                       startCname="创建时间(起)" endCname="创建时间(止)"
                                       ratio="3:3" format="yyyy-MM-dd">
                        </EF:EFDateSpan>
                    </div>
                    <div class="row">
                        <EF:EFSelect ename="inqu_status-0-overhaulPlanStatus" cname="状态" colWidth="3"
                                     template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                     optionLabel="{valueField:'', textField:'全部'}">
                            <EF:EFCodeOption codeName="P023"/>
                        </EF:EFSelect>
                        <EF:EFInput ename="inqu_status-0-voucherNum" cname="依据凭单" placeholder="模糊条件"
                                    colWidth="3"/>
                    </div>
                </EF:EFRegion>
                <EF:EFRegion id="result" title="查询结果">
                    <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                        <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                          blockName="unitBlock" valueField="segNo" textField="segName"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" width="130"
                                     align="center"/>
                        <EF:EFComboColumn ename="overhaulPlanStatus" cname="状态" align="center" width="70"
                                          enable="false">
                            <EF:EFCodeOption codeName="P023"/>
                        </EF:EFComboColumn>
                        <EF:EFComboColumn ename="apprStatus" enable="false" cname="审批状态" align="center" width="70">
                            <EF:EFOption label="审批中" value="60"/>
                            <EF:EFOption label="审核通过" value="70"/>
                            <EF:EFOption label="审核驳回" value="7X"/>
                        </EF:EFComboColumn>
                        <EF:EFComboColumn ename="overhaulSource" cname="检修来源" align="center" width="70"
                                          enable="false">
                            <EF:EFCodeOption codeName="P026"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="voucherNum" cname="依据凭单" width="130" align="center" enable="false"/>
                        <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                        <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                        <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
                        <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
                        <EF:EFComboColumn ename="overhaulQuality" cname="检修性质" align="center" width="70"
                                          enable="false">
                            <EF:EFCodeOption codeName="P024"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="implementManName" cname="实施人" align="center" width="70"/>
                        <EF:EFColumn ename="overhaulStartDate" cname="计划检修日期(起)" align="center"/>
                        <EF:EFColumn ename="overhaulEndDate" cname="计划检修日期(止)" align="center"/>
                        <EF:EFColumn ename="overhaulNumber" cname="计划检修人数" align="right" width="100"/>
                        <EF:EFColumn ename="overhaulTime" cname="计划检修时间(min)" align="right" width="140"/>
                        <EF:EFColumn ename="overhaulProject" cname="计划检修项目"/>
                        <EF:EFColumn ename="acceptanceCriteria" cname="验收标准"/>
                        <EF:EFComboColumn ename="overhaulType" cname="施工类别" align="center" width="70"
                                          enable="false">
                            <EF:EFCodeOption codeName="P025"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="outsourcingContactId" cname="委外联络单号" align="center"/>
                        <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="80"/>
                        <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                        <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
                        <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                        <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
            <div title="详细信息" id="info-2">
                <EF:EFRegion id="detail" title="详细信息">
                    <EF:EFInput ename="detail_status-0-uuid" cname="UUID" type="hidden"/>
                    <div class="row">
                        <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                         containerId="unitInfo01" center="true"
                                         ename="detail_status-0-unitCode" cname="业务单元代码" colWidth="3"/>
                        <EF:EFSelect readonly="true" ename="detail_status-0-segNo" cname="业务单元简称" colWidth="3">
                            <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                        </EF:EFSelect>
                        <EF:EFInput ename="detail_status-0-overhaulPlanId" readonly="true" cname="检修计划单号"
                                    colWidth="3"/>
                        <EF:EFSelect readonly="true" ename="detail_status-0-overhaulSource" cname="检修来源"
                                     colWidth="3">
                            <EF:EFCodeOption codeName="P026"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row">
                        <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                        <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                         containerId="equipmentInfo" center="true"
                                         ename="detail_status-0-equipmentName" cname="设备名称" colWidth="3"/>
                        <EF:EFInput ename="detail_status-0-deviceCode" readonly="true" cname="分部设备代码"
                                    colWidth="3"/>
                        <EF:EFPopupInput originalInput="true" readonly="true" clear="false"
                                         containerId="deviceInfo" center="true" ename="detail_status-0-deviceName"
                                         cname="分部设备名称" colWidth="3"/>
                    </div>
                    <div class="row">
                        <EF:EFSelect required="true" ename="detail_status-0-overhaulQuality" cname="检修性质"
                                     colWidth="3">
                            <EF:EFCodeOption codeName="P024"/>
                        </EF:EFSelect>
                        <EF:EFSelect required="true" ename="detail_status-0-overhaulType" cname="施工类别"
                                     colWidth="3">
                            <EF:EFCodeOption codeName="P025"/>
                        </EF:EFSelect>
                        <EF:EFInput required="true" ename="detail_status-0-implementManName" readonly="true"
                                    cname="实施人"
                                    colWidth="3"/>
                        <EF:EFInput ename="detail_status-0-outsourcingContactId" readonly="true" cname="委外联络单号"
                                    colWidth="3"/>
                    </div>
                    <div class="row">
                        <EF:EFDateSpan startName="detail_status-0-overhaulStartDate" role="datetime"
                                       endName="detail_status-0-overhaulEndDate" readonly="true" required="true"
                                       startCname="计划检修日期(起)" endCname="计划检修日期(止)" interval="10"
                                       ratio="3:3" format="yyyy-MM-dd HH:mm">
                        </EF:EFDateSpan>
                        <EF:EFInput ename="detail_status-0-overhaulNumber" required="true" cname="计划检修人数"
                                    colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                    </div>
                    <div class="row">
                        <EF:EFInput required="true" ename="detail_status-0-overhaulProject" cname="计划检修项目"
                                    colWidth="12"
                                    ratio="1:11" type="textarea"/>
                    </div>
                    <div class="row">
                        <EF:EFInput required="true" ename="detail_status-0-securityMeasures" cname="安全措施"
                                    colWidth="12"
                                    ratio="1:11" type="textarea"/>
                    </div>
                    <div class="row">
                        <EF:EFInput required="true" ename="detail_status-0-acceptanceCriteria" cname="验收标准"
                                    colWidth="12"
                                    ratio="1:11" type="textarea"/>
                    </div>
                </EF:EFRegion>
                <EF:EFRegion id="result2" title="检修计划进度">
                    <EF:EFInput ename="inqu2_status-0-segNo" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu2_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu2_status-0-itemType" cname="UUID" type="hidden"/>
                    <EF:EFGrid blockId="result2" autoDraw="no" sort="all" serviceName="VGDM0805" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                        <EF:EFColumn ename="itemType" cname="项目类型" enable="false" hidden="true"/>
                        <EF:EFColumn ename="sortIndex" cname="序号" width="70" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="itemName" cname="检修项目名称" required="true" width="300"/>
                        <EF:EFColumn ename="beginMinute" cname="开始时间" width="150" editType="datetime"
                                     dateFormat="HH:mm" parseFormats="['HH:mm']" required="true" align="center"/>
                        <EF:EFColumn ename="endMinute" cname="结束时间" width="150" editType="datetime"
                                     dateFormat="HH:mm" parseFormats="['HH:mm']" required="true" align="center"/>
                        <EF:EFColumn ename="responsePerson" cname="责任人" width="100"/>
                        <EF:EFColumn ename="assistPerson" cname="协助人" width="100"/>
                        <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                        <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                        <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
                <EF:EFRegion id="result3" title="检修工具清单">
                    <EF:EFInput ename="inqu3_status-0-segNo" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu3_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu3_status-0-dataType" cname="UUID" type="hidden" value="10"/>
                    <EF:EFGrid blockId="result3" autoDraw="no" sort="all" serviceName="VGDM0806" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                        <EF:EFColumn ename="dataType" cname="数据类型" enable="false" hidden="true"/>
                        <EF:EFColumn ename="sortIndex" cname="序号" width="70" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="stuffName" cname="名称" required="true" width="120"/>
                        <EF:EFColumn ename="stuffQty" cname="数量" width="70" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="stuffUnit" cname="单位" align="center" required="true" width="70"/>
                        <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                        <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                        <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
                <EF:EFRegion id="result4" title="检修资材清单">
                    <EF:EFInput ename="inqu4_status-0-segNo" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu4_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu4_status-0-dataType" cname="UUID" type="hidden" value="10"/>
                    <EF:EFGrid blockId="result4" autoDraw="no" sort="all" serviceName="VGDM0806" queryMethod="query4" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                        <EF:EFColumn ename="dataType" cname="数据类型" enable="false" hidden="true"/>
                        <EF:EFColumn ename="sortIndex" cname="序号" width="70" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="stuffName" cname="名称" required="true" width="150"/>
                        <EF:EFColumn ename="stuffQty" cname="数量" width="70" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="stuffUnit" cname="单位" align="center" required="true" width="70"/>
                        <EF:EFColumn ename="specDesc" cname="规格" width="150"/>
                        <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                        <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                        <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
                <EF:EFRegion id="result5" title="检查项目">
                    <div style="display: none;">
                        <EF:EFInput ename="fileForm" type="file"/>
                    </div>
                    <EF:EFInput ename="inqu5_status-0-segNo" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu5_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                    <EF:EFGrid blockId="result5" autoDraw="no" sort="all" serviceName="VGDM0807" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                        <EF:EFColumn ename="sortIndex" cname="序号" width="60" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="itemName" cname="项目" required="true" width="100"/>
                        <EF:EFColumn ename="itemContent" cname="内容" width="120"/>
                        <EF:EFColumn ename="workStandard" cname="作业标准" width="200" required="true"/>
                        <EF:EFColumn ename="checkMethod" cname="检修方法" width="170" required="true"/>
                        <EF:EFColumn ename="pictureUrl" cname="作业图示" hidden="true"/>
                        <EF:EFColumn ename="pictureOperate" sort="flase" enable="false" cname="作业图示" width="100"
                                     required="true"/>
                        <EF:EFColumn ename="safeItem" cname="安全注意事项" width="120" required="true"/>
                        <EF:EFColumn ename="checkRecord" cname="检修记录" width="120" required="true"/>
                        <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                        <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                        <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
                <EF:EFRegion id="result6" title="维修项目">
                    <EF:EFInput ename="inqu6_status-0-segNo" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu6_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu6_status-0-itemType" cname="UUID" type="hidden"/>
                    <EF:EFGrid blockId="result6" autoDraw="no" sort="all" serviceName="VGDM0805" queryMethod="query6" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                        <EF:EFColumn ename="itemType" cname="项目类型" enable="false" hidden="true"/>
                        <EF:EFColumn ename="sortIndex" cname="序号" width="70" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="itemName" cname="项目名称" required="true" width="300"/>
                        <EF:EFColumn ename="responsePerson" cname="责任人" width="100"/>
                        <EF:EFColumn ename="assistPerson" cname="协助人" width="100"/>
                        <EF:EFColumn ename="beginMinute" cname="开始时间" width="150" editType="datetime"
                                     dateFormat="HH:mm" parseFormats="['HH:mm']" required="true" align="center"/>
                        <EF:EFColumn ename="endMinute" cname="结束时间" width="150" editType="datetime"
                                     dateFormat="HH:mm" parseFormats="['HH:mm']" required="true" align="center"/>
                        <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                        <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                        <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
                <EF:EFRegion id="result7" title="给油脂记录">
                    <EF:EFInput ename="inqu7_status-0-segNo" cname="UUID" type="hidden"/>
                    <EF:EFInput ename="inqu7_status-0-overhaulPlanId" cname="UUID" type="hidden"/>
                    <EF:EFGrid blockId="result7" autoDraw="no" sort="all" serviceName="VGDM0808" isFloat="true">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="业务单元代码" enable="false" hidden="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元名称" enable="false" hidden="true"/>
                        <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" hidden="true"/>
                        <EF:EFColumn ename="deviceLocation" cname="设备位置" required="true" width="120"/>
                        <EF:EFColumn ename="lubricatePoint" cname="润滑点位置" width="120" required="true"/>
                        <EF:EFColumn ename="lubricateQty" cname="润滑点数量" width="100" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="addQty" cname="加油量(下)" width="100" valueType="N"
                                     data-rules="positive_integer" align="center" required="true"/>
                        <EF:EFColumn ename="greaseSpec" cname="油脂型号" width="120"/>
                        <EF:EFColumn ename="lubricateCycle" cname="润滑周期" width="120"/>
                        <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                        <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="创建时间"/>
                        <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                        <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center"
                                     width="100"/>
                        <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="修改时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
            <div title="异常信息" id="info-3">
                <EF:EFRegion id="detail2">
                    <div class="row">
                        <EF:EFInput ename="detail2-0-uuid" cname="UUID" type="hidden"/>
                        <EF:EFInput readonly="true" ename="detail2-0-unitCode" cname="业务单元代码" colWidth="3"/>
                        <EF:EFSelect readonly="true" ename="detail2-0-segNo" cname="业务单元简称" colWidth="3"
                                     defaultValue="">
                            <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                        </EF:EFSelect>
                        <EF:EFInput ename="detail2-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                        <EF:EFInput readonly="true" ename="detail2-0-equipmentName" cname="设备名称" colWidth="3"/>
                    </div>
                    <div class="row">
                        <EF:EFInput ename="detail2-0-deviceCode" readonly="true" cname="分部设备代码" colWidth="3"/>
                        <EF:EFInput readonly="true" ename="detail2-0-deviceName" cname="分部设备名称" colWidth="3"/>
                        <EF:EFSelect readonly="true" ename="detail2-0-exceptionSource" cname="异常来源" colWidth="3"
                                     defaultValue="">
                            <EF:EFCodeOption codeName="P022"/>
                        </EF:EFSelect>
                        <EF:EFSelect readonly="true" ename="detail2-0-spotCheckImplemente" cname="实施方"
                                     colWidth="3" defaultValue="">
                            <EF:EFCodeOption codeName="P048"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row">
                        <EF:EFInput readonly="true" ename="detail2-0-spotCheckContent" cname="点检内容" colWidth="6"
                                    ratio="2:10" minLength="1" maxLength="200"/>
                        <EF:EFInput readonly="true" ename="detail2-0-judgmentStandard" cname="判断标准" colWidth="6"
                                    ratio="2:10" minLength="1" maxLength="200"/>
                    </div>
                    <div class="row">
                        <EF:EFSelect readonly="true" ename="detail2-0-spotCheckMethod" cname="点检方法"
                                     colWidth="3">
                            <EF:EFCodeOption codeName="P049"/>
                        </EF:EFSelect>
                        <EF:EFSelect readonly="true" ename="detail2-0-spotCheckStandardType" cname="标准类型"
                                     colWidth="3">
                            <EF:EFCodeOption codeName="P051"/>
                        </EF:EFSelect>
                        <EF:EFInput readonly="true" ename="detail2-0-measureId" cname="计量单位" colWidth="3"/>
                        <EF:EFSelect readonly="true" ename="detail2-0-isOverhaulHandle" cname="是否检修处理"
                                     colWidth="3">
                            <EF:EFOption value="1" label="是"/>
                            <EF:EFOption value="0" label="否"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row">
                        <EF:EFInput readonly="true" ename="detail2-0-temporaryMeasures" cname="临时措施" colWidth="6"
                                    ratio="2:10"/>
                        <EF:EFInput readonly="true" ename="detail2-0-upperLimit" cname="上限值" colWidth="3"/>
                        <EF:EFInput readonly="true" ename="detail2-0-lowerLimit" cname="下限值" colWidth="3"/>
                    </div>
                    <div class="row">
                        <EF:EFInput readonly="true" ename="detail2-0-actualsRemark" cname="点检实绩" colWidth="12"
                                    ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
                    </div>
                    <div class="row">
                        <EF:EFInput readonly="true" ename="detail2-0-handleMeasures" cname="处理措施" colWidth="12"
                                    ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
                    </div>
                    <div class="row">
                        <EF:EFInput readonly="true" ename="detail2-0-procResult" cname="处理结果" colWidth="12"
                                    ratio="1:11" type="textarea" minLength="1" maxLength="200"/>
                    </div>
                </EF:EFRegion>
            </div>
        </EF:EFTab>
        <EF:EFWindow id="popup" width="90%" height="60%"/>
        <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
        <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
        <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
        <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
        <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
        <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfoMainQuery" width="90%" height="60%"/>
    </jsp:body>
</EF:EFPage>