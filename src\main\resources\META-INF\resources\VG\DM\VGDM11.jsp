<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<script src="${ctx}/common/js/echarts.min.js"></script>

<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备档案编号" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-procedureName" cname="工序名称" placeholder="模糊条件" colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-ruleStatus" cname="状态" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P016" filter="A"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
    </EF:EFRegion>
    <div class="row">
        <div class="col-md-4">
            <EF:EFRegion id="result2" title="设备清单">
                <EF:EFGrid blockId="result2" autoDraw="no" readonly="true" sort="all"
                           queryMethod="queryDevice" checkMode="single, row" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" hidden="true"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase" hidden="true"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="100" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div class="col-md-8">
            <EF:EFRegion id="result" title="设备工序规则清单">
                <EF:EFGrid blockId="result" autoDraw="no" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFComboColumn ename="ruleStatus" enable="false" cname="状态" align="center" width="80">
                        <EF:EFCodeOption codeName="P016"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="procedureCode" required="true" cname="工序代码" width="100" align="center"/>
                    <EF:EFColumn ename="procedureName" required="true" cname="工序名称" width="100"/>
                    <EF:EFColumn ename="startTimeRule" required="true" cname="开始时间规则" editType="textarea"
                                 width="200"/>
                    <EF:EFColumn ename="stopTimeRule" required="true" cname="结束时间规则" editType="textarea"
                                 width="200"/>
                    <EF:EFComboColumn ename="multiFlag" cname="多次" align="center" width="70" required="true">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="sortIndex" required="true" cname="排序" width="70" align="center"/>
                    <EF:EFColumn ename="remark" cname="备注" width="200"/>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </div>
    <div id="firstChart" style="width: 600px;height:400px;"></div>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>
