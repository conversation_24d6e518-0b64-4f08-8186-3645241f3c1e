package com.baosight.imom.li.ds.service;

import com.baosight.bpm.util.StringUtil;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.li.ds.domain.LIDS0701;
import com.baosight.imom.li.ds.domain.LIDS0901;
import com.baosight.imom.li.ds.domain.LIDS1201;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存管理
 */
public class ServiceLIDS0901 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0901().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                // 加工协议子项号
                String packId = inInfo.getString("inqu_status-0-packId");
                List<String> newPackIdList = new ArrayList<>();
                if (StringUtil.isNotEmpty(packId)) {
                    // 处理加工协议子项号
                    List<String> packIdList = Arrays.asList(packId.split("\n"));
                    if (packIdList != null && packIdList.size() > 0) {
                        // 过滤掉空的值
                        newPackIdList = packIdList.stream().filter(f -> !"".equals(f.trim())).collect(Collectors.toList());
                    }
                }
                inInfo.set("inqu_status-0-packIdList", newPackIdList);
                inInfo.set("inqu_status-0-packId", "");

                // 加工协议子项号
                String labelId = inInfo.getString("inqu_status-0-labelId");
                List<String> newLabelIdList = new ArrayList<>();
                if (StringUtil.isNotEmpty(labelId)) {
                    // 处理加工协议子项号
                    List<String> labelIdList = Arrays.asList(labelId.split("\n"));
                    if (labelIdList != null && labelIdList.size() > 0) {
                        // 过滤掉空的值
                        newLabelIdList = labelIdList.stream().filter(f -> !"".equals(f.trim())).collect(Collectors.toList());
                    }
                }
                inInfo.set("inqu_status-0-labelIdList", newLabelIdList);
                outInfo = super.query(inInfo, LIDS0901.QUERY, new LIDS0901());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    /**
     * PDA库位变更
     * @param inInfo
     * @return outInfo
     */
    public EiInfo scanGeneratedInventory(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            log("调用ServiceLIDS0901.scanGeneratedInventory传入参数：" + inInfo );
            System.out.println("调用ServiceLIDS0901.scanGeneratedInventory传入参数：" + inInfo);

            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少系统账套信息，库位变更失败");
            }
            String warehouseCode = inInfo.getString("warehouseCode");
            String warehouseName = inInfo.getString("warehouseName");
            if (org.apache.commons.lang3.StringUtils.isBlank(warehouseCode)
                    || org.apache.commons.lang3.StringUtils.isBlank(warehouseName)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("传入仓库代码或仓库名称为空！库位变更失败");
            }
            String craneId = inInfo.getString("craneId");
            List<HashMap> dList = (List<HashMap>) inInfo.get("dList");
            List<Map> packList = new ArrayList<>();
            for (HashMap hashMap : dList) {
                HashMap packMap = new HashMap();
                String packId = MapUtils.getString(hashMap, "packId", "");
                String locationId = MapUtils.getString(hashMap, "newLocationId", "");
                String locationName = MapUtils.getString(hashMap, "newLocationName", "");
                if (org.apache.commons.lang3.StringUtils.isBlank(locationId)
                        || org.apache.commons.lang3.StringUtils.isBlank(locationName)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("捆包号："+packId +"传入库位代码或库位名称为空！库位变更失败");
                }
                packMap.put("putoutLocationId", MapUtils.getString(hashMap, "locationId", ""));//转出库位代码
                packMap.put("putoutLocationName", MapUtils.getString(hashMap, "putoutLocationName", ""));//转出库位名称
                packMap.put("segNo", segNo);
                packMap.put("locationId", locationId);
                packMap.put("locationName", locationName);
                packMap.put("warehouseCode", warehouseCode);
                packMap.put("warehouseName", warehouseName);
                packMap.put("packId", packId);
                packMap.put("factoryBuilding", inInfo.getString("factoryBuilding"));
                packMap.put("factoryArea", inInfo.getString("factoryArea"));
                packMap.put("netWeight", hashMap.get("netWeight"));
                packMap.put("quantity", hashMap.get("pieceNum"));
                packMap.put("qualityGrade", hashMap.get("qualityGrade"));
                packMap.put("userId", inInfo.getString("userId"));
                packMap.put("userName", inInfo.getString("userName"));
                packList.add(packMap);
            }
            //调用IMC倒库
            EiInfo sendInfo = new EiInfo();
            sendInfo.set("userId",inInfo.getString("userId"));
            sendInfo.set("userName",inInfo.getString("userName"));
            sendInfo.addBlock(EiConstant.resultBlock).addRows(packList);
            sendInfo.set("serviceName", "LIDSInterfaces");
            sendInfo.set("methodName", "transferInventory");
            sendInfo = XLocalManager.callNoTx(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                log(sendInfo.getMsg());
                System.out.println(sendInfo.getMsg());
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(sendInfo.getMsg());
                return outInfo;
            }
            //调用成功后生成IMOM实物库存
            String statusFlag =changeInventory(packList ,craneId,segNo,dao) ;
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("变更成功");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("变更失败" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 余料捆包生成实物库存
     *机组代码、机组名称、余料捆包号（系统自动生成）、余料重量（余料的重量根据已切长度反算，余料都是理论计重）、余料位置（1号鞍座/2号鞍座，判定原则看当前1号鞍座、2号鞍座是否有捆包）、投料捆包号，同时生成MES库存，MES库存主要字段有：捆包号、仓库代码、仓库名称、区域类型（机组上料区）、区域代码（1号鞍座/2号鞍座代码）、区域名称（1号鞍座/2号鞍座名称）、XYZ值（空）、入库时间（当前时间）
     * @param inInfo
     * @returnhe
     */
    public EiInfo surplusMaterialIsGeneratedIntoInventory (EiInfo inInfo){
        try {
            List<HashMap> dList = (List<HashMap>) inInfo.get("dList");
            if (CollectionUtils.isNotEmpty(dList)){
                for (HashMap attr : dList) {
                    LIDS0901 lids0901 = new LIDS0901();
                    lids0901.setSegNo(MapUtils.getString(attr,"segNo",""));
                    lids0901.setUnitCode(MapUtils.getString(attr,"segNo",""));
                    //传入机组代码查询对应的余料仓库
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo",MapUtils.getString(attr,"segNo",""));
                    queryMap.put("machineCode",MapUtils.getString(attr,"machineCode",""));
                    List<Map> machineList = this.dao.query(LIDS0701.QUERY_LOCATION,queryMap);
                    if (CollectionUtils.isNotEmpty(machineList)){
                        //仓库代码
                        lids0901.setWarehouseCode((String)machineList.get(0).get("materialWarehouseCode"));
                        //仓库名称
                        lids0901.setWarehouseName((String)machineList.get(0).get("materialWarehouseName"));
                    }
                    //区域类型
                    lids0901.setAreaType("60");
                    //区域代码
                    lids0901.setAreaCode((String)attr.get("areaCode"));
                    //区域名称
                    lids0901.setAreaName((String)attr.get("areaName"));
                    //捆包号
                    lids0901.setPackId((String)attr.get("packId"));
                    //并包号
                    lids0901.setUnitedPackId("");
                    //标签号
                    lids0901.setLabelId((String)attr.get("labelId"));
                    //净重
                    lids0901.setNetWeight(new BigDecimal((String)attr.get("netWeight")));
                    //毛重
                    lids0901.setGrossWeight(new BigDecimal((String)attr.get("netWeight")).add(new BigDecimal(20)));
                    //吊装重量
                    lids0901.setCraneOperationWeight(new BigDecimal(0));
                    //数量
                    lids0901.setQuantity(1);
                    //上下层标记
                    lids0901.setPosDirCode("1");
                    //行车实绩单号
                    lids0901.setCraneResultId("");
                    //板卷标记
                    lids0901.setActionFlag("1");
                    //内外版标记
                    lids0901.setInnerOutterPlateFlag("");
                    //状态
                    lids0901.setStatus("10");
                    //x轴坐标
                    lids0901.setX_position("0");
                    //y轴坐标
                    lids0901.setY_position("0");
                    //z轴坐标
                    lids0901.setZ_position("0");
                    //记录创建人
                    lids0901.setRecCreator("system");
                    //记录创建人姓名
                    lids0901.setRecCreatorName("system");
                    //记录创建时间
                    lids0901.setRecCreateTime(DateUtils.curDateTimeStr14());
                    //记录修改人
                    lids0901.setRecRevisor("system");
                    //记录修改人姓名
                    lids0901.setRecRevisorName("system");
                    //记录修改时间
                    lids0901.setRecReviseTime(DateUtils.curDateTimeStr14());
                    //归档标记
                    lids0901.setArchiveFlag("");
                    //租户
                    lids0901.setTenantUser("");
                    //删除标记
                    lids0901.setDelFlag(0);
                    //ID
                    lids0901.setUuid(UUIDUtils.getUUID());
                    this.dao.insert(LIDS0901.INSERT,lids0901);
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("生成成功");
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("生成失败" + ex.getMessage());
        }
        return inInfo;
    }
    /**
     * 根据捆包号查询捆包库存位置
     * @param inInfo
     * @returnhe
     */
    public EiInfo getLocation(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少账套信息，查询失败");
                return outInfo;
            }
            List<HashMap> packIdList =(List<HashMap>) inInfo.get("packIdList");
            List<HashMap> locationList = new ArrayList<>();
            if (CollectionUtils.isEmpty(packIdList)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少捆包信息，查询失败");
                return outInfo;
            }else {
                for (HashMap hashMap : packIdList) {
                    String packId = MapUtils.getString(hashMap,"packId","");
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo",segNo);
                    queryMap.put("packId",packId);
                    List<LIDS0901> messageList = this.dao.query(LIDS0901.QUERY_PACK_MESSAGE,queryMap);
                    if (CollectionUtils.isNotEmpty(messageList)){
                        HashMap locationMap = new HashMap();
                        locationMap.put("locationName", messageList.get(0).getAreaName());
                        locationList.add(locationMap);
                    }
                }
            }
            outInfo.set("locationList",locationList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功，本次返回"+locationList.size()+"条参数");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /**
     * 根据捆包号查询是否存在库存
     * @param inInfo
     * @return outInfo
     * @ServiceId:S_LI_DS_0016
     */
    public EiInfo queryInventory(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少账套信息，查询失败");
                return outInfo;
            }
            Map attr = inInfo.getAttr();
            String packId = MapUtils.getString(attr,"packId","");
            if (StringUtils.isBlank(packId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少捆包号信息，查询失败");
                return outInfo;
            }
            HashMap queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("packId",packId);
            List<LIDS0901> packList = this.dao.query(LIDS0901.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(packList)){
                outInfo.set("actionFlag",packList.get(0).getActionFlag());
                outInfo.set("netWeight",packList.get(0).getNetWeight());
                outInfo.set("quantity",packList.get(0).getQuantity());
                outInfo.set("storageType","1");
            }else {
                String specsDesc =MapUtils.getString(attr,"specsDesc","");
                if (specsDesc.contains("*C")){
                    //卷
                    outInfo.set("actionFlag","1");
                }else {
                    //板
                    outInfo.set("actionFlag","0");
                }
                outInfo.set("netWeight","");
                outInfo.set("quantity","");
                outInfo.set("storageType","0");
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功！");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    //库位变更判断IMOM是否有实物库存，没有则新增库存，有则变更库存位置。
    public String changeInventory(List<Map> packList , String craneId , String segNo, Dao dao){
        String statusFlag = "";
        String startXPosition = "0";
        String startYPosition = "0";
        String startZPosition = "0";
        //查找行车最后一条实绩
        if (StringUtils.isNotBlank(craneId)){
            HashMap queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("craneId",craneId);
            queryMap.put("status", "20");
            List<LIDS1201> positionList = dao.query(LIDS1201.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(positionList)){
                startXPosition = positionList.get(0).getStartXPosition();
                startYPosition = positionList.get(0).getStartYPosition();
                startZPosition = positionList.get(0).getStartZPosition();
            }
        }
        for (Map map : packList) {
            HashMap hashMap = new HashMap();
            hashMap.put("segNo",segNo);
            hashMap.put("packId",map.get("packId"));
            hashMap.put("status","10");
            List<LIDS0901> lids0901List = dao.query(LIDS0901.QUERY, hashMap);
            //IMOM存在当前库存，更换位置信息，不存在则新增
            if (CollectionUtils.isNotEmpty(lids0901List)){
                HashMap updateMap = new HashMap();
                updateMap.put("segNo",segNo);
                updateMap.put("packId",map.get("packId"));
                updateMap.put("areaType","10");
                updateMap.put("areaCode",MapUtils.getString(map, "locationId", ""));
                updateMap.put("areaName",MapUtils.getString(map, "locationName", ""));
                updateMap.put("xPosition", startXPosition);
                updateMap.put("yPosition", startYPosition);
                updateMap.put("zPosition", startZPosition);
                updateMap.put("recRevisor", map.get("userId"));
                updateMap.put("recRevisorName", map.get("userName"));
                updateMap.put("recReviseTime", DateUtils.curDateTimeStr14());
                updateMap.put("tenantUser", "库位变更");
                dao.update(LIDS0901.UPDATE_AREA_BY_PACK_ID,updateMap);
            }else {
                LIDS0901 lids0901 = new LIDS0901();
                lids0901.setSegNo(segNo);
                lids0901.setUnitCode(segNo);
                //仓库代码
                lids0901.setWarehouseCode(MapUtils.getString(map, "warehouseCode", ""));
                //仓库名称
                lids0901.setWarehouseName(MapUtils.getString(map, "warehouseName", ""));
                //区域类型
                lids0901.setAreaType("10");
                //区域代码
                lids0901.setAreaCode(MapUtils.getString(map, "locationId", ""));
                //区域名称
                lids0901.setAreaName(MapUtils.getString(map, "locationName", ""));
                //捆包号
                lids0901.setPackId(MapUtils.getString(map, "packId", ""));
                //并包号
                lids0901.setUnitedPackId("");
                //标签号
                lids0901.setLabelId("");
                //净重
                lids0901.setNetWeight(new BigDecimal(String.valueOf(map.get("netWeight"))));
                //毛重
                lids0901.setGrossWeight(new BigDecimal(0));
                //吊装重量
                lids0901.setCraneOperationWeight(new BigDecimal(0));
                //数量
                lids0901.setQuantity(new Integer((String.valueOf(map.get("qualityGrade")))));
                //上下层标记
                lids0901.setPosDirCode("1");
                //行车实绩单号
                lids0901.setCraneResultId("");
                //板卷标记
                lids0901.setActionFlag("");
                //内外版标记
                lids0901.setInnerOutterPlateFlag("");
                //状态
                lids0901.setStatus("10");
                //X轴坐标
                lids0901.setX_position(startXPosition);
                //Y轴坐标
                lids0901.setY_position(startYPosition);
                //Z轴坐标
                lids0901.setZ_position(startZPosition);
                //记录创建人
                lids0901.setRecCreator(MapUtils.getString(map, "userId", ""));
                //记录创建人姓名
                lids0901.setRecCreatorName(MapUtils.getString(map, "userName", ""));
                //记录创建时间
                lids0901.setRecCreateTime(DateUtils.curDateTimeStr14());
                //记录修改人
                lids0901.setRecRevisor(MapUtils.getString(map, "userId", ""));
                //记录修改人姓名
                lids0901.setRecRevisorName(MapUtils.getString(map, "userName", ""));
                //记录修改时间
                lids0901.setRecReviseTime(DateUtils.curDateTimeStr14());
                //归档标记
                lids0901.setArchiveFlag("");
                //租户
                lids0901.setTenantUser("库位变更");
                //删除标记
                lids0901.setDelFlag(0);
                //ID
                lids0901.setUuid(UUIDUtils.getUUID());
                dao.insert(LIDS0901.INSERT, lids0901);
            }

        }
        return statusFlag;
    }

}
