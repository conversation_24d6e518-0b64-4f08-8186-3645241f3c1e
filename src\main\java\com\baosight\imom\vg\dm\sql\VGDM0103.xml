<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0103">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0103">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        UPLOAD_FILE_PATH as "uploadFilePath",  <!-- 文件路径 -->
        UPLOAD_FILE_NAME as "uploadFileName",  <!-- 文件名称 -->
        FIFLE_TYPE as "fifleType",  <!-- 文件类型 -->
        FIFLE_SIZE as "fifleSize",  <!-- 文件大小 -->
        FILE_ID as "fileId",  <!-- 文件id -->
        FILE_VERSION as "fileVersion",  <!-- 文件版本 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0103 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uploadFileName">
            UPLOAD_FILE_NAME = #uploadFileName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fifleType">
            FIFLE_TYPE = #fifleType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                FILE_VERSION DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0103 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uploadFileName">
            UPLOAD_FILE_NAME = #uploadFileName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fifleType">
            FIFLE_TYPE = #fifleType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </select>

    <select id="queryMax" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0103">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        UPLOAD_FILE_PATH as "uploadFilePath",  <!-- 文件路径 -->
        UPLOAD_FILE_NAME as "uploadFileName",  <!-- 文件名称 -->
        FIFLE_TYPE as "fifleType",  <!-- 文件类型 -->
        FIFLE_SIZE as "fifleSize",  <!-- 文件大小 -->
        FILE_ID as "fileId",  <!-- 文件id -->
        FILE_VERSION as "fileVersion",  <!-- 文件版本 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0103
        JOIN (
        SELECT T2.UPLOAD_FILE_NAME AS FILE_NAME,
        MAX(T2.FILE_VERSION) AS VERSION
        FROM ${mevgSchema}.TVGDM0103 T2
        WHERE
        T2.E_ARCHIVES_NO = #eArchivesNo#
        AND T2.FIFLE_TYPE = #fifleType#
        AND T2.DEL_FLAG = '0'
        GROUP BY T2.UPLOAD_FILE_NAME
        ) T1
        ON UPLOAD_FILE_NAME = T1.FILE_NAME
        AND FILE_VERSION = T1.VERSION
        WHERE
        E_ARCHIVES_NO = #eArchivesNo#
        AND FIFLE_TYPE = #fifleType#
        AND DEL_FLAG = '0'
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>
    </select>

    <select id="countMax" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0103
        JOIN (
        SELECT T2.UPLOAD_FILE_NAME AS FILE_NAME,
        MAX(T2.FILE_VERSION) AS VERSION
        FROM ${mevgSchema}.TVGDM0103 T2
        WHERE
        T2.E_ARCHIVES_NO = #eArchivesNo#
        AND T2.FIFLE_TYPE = #fifleType#
        AND T2.DEL_FLAG = '0'
        GROUP BY T2.UPLOAD_FILE_NAME
        ) T1
        ON UPLOAD_FILE_NAME = T1.FILE_NAME
        AND FILE_VERSION = T1.VERSION
        WHERE
        E_ARCHIVES_NO = #eArchivesNo#
        AND FIFLE_TYPE = #fifleType#
        AND DEL_FLAG = '0'
    </select>

    <select id="queryMaxVersion" resultClass="int">
        SELECT IFNULL(MAX(FILE_VERSION),0) FROM ${mevgSchema}.TVGDM0103 WHERE
        E_ARCHIVES_NO = #eArchivesNo#
        AND UPLOAD_FILE_NAME = #uploadFileName#
        AND FIFLE_TYPE = #fifleType#
        AND DEL_FLAG = '0'
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0103 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        UPLOAD_FILE_PATH,  <!-- 文件路径 -->
        UPLOAD_FILE_NAME,  <!-- 文件名称 -->
        FIFLE_TYPE,  <!-- 文件类型 -->
        FIFLE_SIZE,  <!-- 文件大小 -->
        FILE_ID,  <!-- 文件id -->
        FILE_VERSION,  <!-- 文件版本 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#eArchivesNo#, #equipmentName#, #uploadFilePath#, #uploadFileName#, #fifleType#, #fifleSize#, #fileId#,
        #fileVersion#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0103 WHERE
        UUID = #uuid#
    </delete>

    <update id="updForDel">
        UPDATE ${mevgSchema}.TVGDM0103
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0103
        SET
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        UPLOAD_FILE_PATH = #uploadFilePath#,   <!-- 文件路径 -->
        UPLOAD_FILE_NAME = #uploadFileName#,   <!-- 文件名称 -->
        FIFLE_TYPE = #fifleType#,   <!-- 文件类型 -->
        FIFLE_SIZE = #fifleSize#,   <!-- 文件大小 -->
        FILE_ID = #fileId#,   <!-- 文件id -->
        FILE_VERSION = #fileVersion#,   <!-- 文件版本 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#  <!-- 业务单元代码 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>