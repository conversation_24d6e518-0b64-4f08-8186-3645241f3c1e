/**
* Generate time : 2025-01-10 13:41:22
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
* Tlirl0505
* 
*/
public class LIRL0505 extends DaoEPBase {

                public static final String QUERY = "LIRL0505.query";
                public static final String COUNT = "LIRL0505.count";
                public static final String UPDATE = "LIRL0505.update";
                public static final String INSERT = "LIRL0505.insert";
                private String segNo = " ";		/* 业务单元代码*/
                private String segName = "";        /* 业务单元简称*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录创建人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sendType = "";		/* 发送分类*/
                private String perNo = " ";		/* 人员工号*/
                private String perName = " ";		/* 人员姓名*/
                private String perTel = " ";		/* 人员手机号*/
                private String workDays = ""; /*一周排班天数*/
                private String timeStart = "";		/* 上班时间*/
                private String timeEnd = "";		/* 下班时间*/
                private String typeOfHandling = "";   /*业务类型*/
                private Integer timeoutThreshold = Integer.valueOf(0);		/* 超时阈值*/
                private String loadingPointNo = " ";		/* 装卸点代码*/
                private String loadingPointName = " ";		/* 装卸点名称*/
                private Integer doorChargeFlag  = Integer.valueOf(0); /*门长标记*/
                private String status = " ";		/* 状态*/
                private String uuid = " ";		/* uuid*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sendType");
        eiColumn.setDescName("发送分类");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("perNo");
        eiColumn.setDescName("人员工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("perName");
        eiColumn.setDescName("人员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("perTel");
        eiColumn.setDescName("人员手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workDays");
        eiColumn.setDescName("一周排班天数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("timeStart");
        eiColumn.setDescName("上班时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("timeEnd");
        eiColumn.setDescName("下班时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("typeOfHandling");
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("timeoutThreshold");
        eiColumn.setDescName("超时阈值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingPointNo");
        eiColumn.setDescName("装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingPointName");
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("doorChargeFlag");
        eiColumn.setDescName("门长标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0505() {
initMetaData();
}

        /**
        * get the segNo - 业务单元代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
         * get the segName - 业务单元简称
        *
        * @return the segName
        */
        public String getSegName() {
        return this.segName;
        }

        /**
         * set the segName - 业务单元简称
        */
        public void setSegName(String segName) {
        this.segName = segName;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录创建人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录创建人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }

        /**
        * get the sendType - 发送分类
        * @return the sendType
        */
        public String getSendType() {
        return this.sendType;
        }

        /**
        * set the sendType - 发送分类
        */
        public void setSendType(String sendType) {
        this.sendType = sendType;
        }

        /**
        * get the perNo - 人员工号
        * @return the perNo
        */
        public String getPerNo() {
        return this.perNo;
        }

        /**
        * set the perNo - 人员工号
        */
        public void setPerNo(String perNo) {
        this.perNo = perNo;
        }
        /**
        * get the perName - 人员姓名
        * @return the perName
        */
        public String getPerName() {return this.perName;}

        /**
         * set the perName - 人员姓名
        */
        public void setPerName(String perName) {
        this.perName = perName;
        }
        /**
         * get the perTel - 人员手机号
         * @return the perTel
        */
        public String getPerTel() {return this.perTel;}
        /**
         * set the perTel - 人员手机号
         */
        public void setPerTel(String perTel) {this.perTel = perTel;}

        /**
         * get the workDays - 一周排班天数
         * @return the workDays
         */
        public String getWorkDays() {return this.workDays;}
    /**
     * set the workDays - 一周排班天数
     */
     public void setWorkDays(String workDays) {this.workDays = workDays;}
        /**
         * get the timeStart - 有效开始时间
         * @return the timeStart
        */

        public String getTimeStart() {return this.timeStart;}
        /**
            * set the timeStart - 有效开始时间
          */
         public void setTimeStart(String timeStart) {this.timeStart = timeStart;}

        /**
        * get the timeEnd - 有效截止时间
        * @return the timeEnd
        */
        public String getTimeEnd() {return this.timeEnd;}
        /**
         * set the timeEnd - 有效截止时间
        */
        public void setTimeEnd(String timeEnd) {this.timeEnd = timeEnd;}

        /**
          * get the typeOfHandling - 业务类型
          * @return the typeOfHandling
        */
        public String getTypeOfHandling(){return this.typeOfHandling;}

        /**
          * set the typeOfHandling - 业务类型
        */
        public void setTypeOfHandling(String typeOfHandling) {this.typeOfHandling = typeOfHandling;}
        /**
     * get the timeoutThreshold - 超时阈值
     * @return the timeoutThreshold
     */
        public Integer getTimeoutThreshold() {
        return this.timeoutThreshold;
    }

    /**
     * set the timeoutThreshold - 超时阈值
     */
        public void setTimeoutThreshold(Integer timeoutThreshold) {
        this.timeoutThreshold = timeoutThreshold;
    }
    /**
         * get the loadingPointNo - 装卸点代码
         * @return the loadingPointNo
         */
        public String getLoadingPointNo() {return this.loadingPointNo;}

        /**
         * set the loadingPointNo - 装卸点代码
         */
        public void setLoadingPointNo(String loadingPointNo) {this.loadingPointNo = loadingPointNo;}

        /**
         * get the loadingPointName - 装卸点名称
         * @return the loadingPointName
         */
        public String getLoadingPointName() {return this.loadingPointName;}

        /**
         * set the loadingPointName - 装卸点名称
         */
        public void setLoadingPointName(String loadingPointName) {this.loadingPointName = loadingPointName;}

        /**
         * get the doorChargeFlag - 门长标记
         * @return the doorChargeFlag
         */
        public Integer getDoorChargeFlag() {return this.doorChargeFlag;}

        /**
         * set the doorChargeFlag - 门长标记
         */
         public void setDoorChargeFlag(Integer doorChargeFlag) { this.doorChargeFlag = doorChargeFlag;}
        /**
         * get the status - 状态
         * @return the status
         */
        public String getStatus() {return this.status;}

        /**
        * set the status - 状态
        */
        public void setStatus(String status) { this.status = status;    }

         /**
             * get the uuid - uuid
          * @return the uuid
            */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSendType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sendType")), sendType));
                setPerNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("perNo")), perNo));
                setPerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("perName")), perName));
                setPerTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("perTel")), perTel));
                setWorkDays(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workDays")), workDays));
                setTimeStart(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("timeStart")), timeStart));
                setTimeEnd(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("timeEnd")), timeEnd));
                setTypeOfHandling(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("typeOfHandling")), typeOfHandling));
                setTimeoutThreshold(NumberUtils.toInteger(StringUtils.toString(map.get("timeoutThreshold")), timeoutThreshold));
                setLoadingPointNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingPointNo")), loadingPointNo));
                setLoadingPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingPointName")), loadingPointName));
                setDoorChargeFlag(NumberUtils.toInteger(StringUtils.toString(map.get("doorChargeFlag")),  doorChargeFlag));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sendType",StringUtils.toString(sendType, eiMetadata.getMeta("sendType")));
                map.put("perNo",StringUtils.toString(perNo, eiMetadata.getMeta("perNo")));
                map.put("perName",StringUtils.toString(perName, eiMetadata.getMeta("perName")));
                map.put("perTel",StringUtils.toString(perTel, eiMetadata.getMeta("perTel")));
                map.put("workDays",StringUtils.toString(workDays, eiMetadata.getMeta("workDays")));
                map.put("timeStart",StringUtils.toString(timeStart, eiMetadata.getMeta("timeStart")));
                map.put("timeEnd",StringUtils.toString(timeEnd, eiMetadata.getMeta("timeEnd")));
                map.put("typeOfHandling",StringUtils.toString(typeOfHandling, eiMetadata.getMeta("typeOfHandling")));
                map.put("timeoutThreshold",StringUtils.toString(timeoutThreshold, eiMetadata.getMeta("timeoutThreshold")));
                map.put("loadingPointNo",StringUtils.toString(loadingPointNo, eiMetadata.getMeta("loadingPointNo")));
                map.put("loadingPointName",StringUtils.toString(loadingPointName, eiMetadata.getMeta("loadingPointName")));
                map.put("doorChargeFlag",StringUtils.toString(doorChargeFlag, eiMetadata.getMeta("doorChargeFlag")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));

return map;

}
}