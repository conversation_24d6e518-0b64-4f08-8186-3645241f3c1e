package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0806;

/**
 * 月度检修工具资材清单表
 */
public class VGDM0806 extends Tvgdm0806 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0806.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0806.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0806.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0806.update";
    /**
     * 逻辑删除
     */
    public static final String UPD_FOR_DEL = "VGDM0806.updForDel";

}
