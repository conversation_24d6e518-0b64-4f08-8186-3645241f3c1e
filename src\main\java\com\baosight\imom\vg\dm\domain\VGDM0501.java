package com.baosight.imom.vg.dm.domain;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.vg.domain.Tvgdm0501;

import java.util.Map;

/**
 * 点检异常信息表
 */
public class VGDM0501 extends Tvgdm0501 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0501.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0501.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0501.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0501.update";

    @Override
    public String getCheckStatus() {
        return this.getExceptionStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

    /**
     * 新增数据
     *
     * @param dao      dao
     * @param isSetSys 是否设置系统字段
     */
    public void insertData(Dao dao, boolean isSetSys) {
        this.setExceptionStatus(MesConstant.Status.K10);
        String[] arr = {this.getSegNo()};
        this.setExceptionContactId(SequenceGenerator.getNextSequence(SequenceConstant.EXCEPTION_CONTACT_ID, arr));
        Map data = this.toMap();
        if (isSetSys) {
            RecordUtils.setCreator(data);
        }
        dao.insert(INSERT, data);
    }
}
