<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>


<EF:EFPage>
    <jsp:attribute name="footer">
        <script>
            var ctx = "${ctx}";
        </script>
    </jsp:attribute>
    <jsp:body>
        <div class="row">
            <div class="col-md-3">
                <EF:EFRegion title="组织机构树" id="tree" fitHeight="true">
                    <div class="row">
                        <div class="col-md-4">
                            <span style="line-height: 25px">过滤器</span>
                        </div>
                        <div class="col-md-8">
                            <input id="filterText" name="filterText" />
                        </div>
                    </div>

                    <div id="menu" style="margin-top: 12px; margin-bottom: 8px">
                        <EF:EFTree bindId="categoryTree" ename="tree_name" textField="text" valueField="label"
                                   hasChildren="leaf" pid="parent_id"
                                   serviceName="XSLV0200" methodName="query">
                        </EF:EFTree>
                    </div>
                </EF:EFRegion>
            </div>
            <div class="col-md-9">
                <EF:EFRegion id="inqu" title="查询条件">
                    <EF:EFInput blockId="inqu_status" ename="resourceEname" cname="资源英文名" row="0" placeholder="请输入资源英文名"/>
                    <EF:EFInput blockId="inqu_status" ename="resourceCname" cname="资源中文名" row="0" placeholder="请输入资源中文名"/>
                </EF:EFRegion>
                <EF:EFInput ename="inqu_status-0-orgId" cname="组织ID" type="hidden"/><%--隐藏--%>
                <EF:EFInput ename="inqu_status-0-orgType" cname="组织类型" type="hidden"/>
                <EF:EFInput ename="inqu_status-0-orgCname" cname="组织名称" type="hidden"/>
                <EF:EFRegion id="result" title="查询结果" fitHeight="true">
                    <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" checkMode="multiple">
                        <EF:EFColumn ename="resourceEname" cname="资源英文名" readonly="true" locked="true"/>
                        <EF:EFColumn ename="resourceCname" cname="资源中文名" readonly="true" locked="true"/>
                        <EF:EFColumn ename="objectType" cname="资源类型" readonly="true" locked="true"/>

                        <EF:EFColumn ename="recCreator" cname="创建人" readonly="true"/>
                        <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" parseFormats="['yyyyMMddHHmmss']"
                                     editType="datetime"
                                     dateFormat="yyyy-MM-dd HH:mm:ss" readonly="true"/>
                        <EF:EFColumn ename="recRevisor" cname="修改人" readonly="true"/>
                        <EF:EFColumn ename="recReviseTime" cname="修改时间" readonly="true"/>
                        <EF:EFColumn ename="orgId" cname="组织机构ID" hidden="true"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>

        <EF:EFWindow id="insertUser" width="58%" height="70%" top="100px" left="280px">
            <EF:EFTab id="info" showClose="false" contentType="iframe">
                <ul>
                    <li>资源视角</li>
                    <li>菜单视角</li>
                    <li>资源组视角</li>
                </ul>
                <div data-src="${ctx}/web/XSLV0201"></div>
                <div data-src="${ctx}/web/XSLV0202"></div>
                <div data-src="${ctx}/web/XSLV0203"></div>
            </EF:EFTab>
        </EF:EFWindow>
    </jsp:body>

</EF:EFPage>
