package com.baosight.imom.common.utils;

import com.baosight.hdsdk.domain.data.HDRecord;
import java.util.ArrayList;
import java.util.List;

/**
 * tag点位数据处理工具类
 *
 * <AUTHOR>
 * @Date 2025/4/25 10:00
 * @Version 1.0
 *
 */
public class TagDataUtil {


    /**
     * 基于持续时间的滤波
     * 只有状态持续超过指定时间才视为有效操作
     * @param hdRecords
     * @return
     */
    public static List<HDRecord> DurationFilter(List<HDRecord> hdRecords){
        if (hdRecords == null || hdRecords.isEmpty()) {
            return hdRecords;
        }

        // 设置最小持续时间阈值(毫秒)
        final long MIN_DURATION = 1000;
        
        // 存储过滤后的记录
        List<HDRecord> filteredRecords = new ArrayList<>();
        
        HDRecord lastRecord = hdRecords.get(0);
        long lastTimestamp = lastRecord.getSecond() * 1000L + lastRecord.getMicroSecond();
        
        for (int i = 1; i < hdRecords.size(); i++) {
            HDRecord currentRecord = hdRecords.get(i);
            long currentTimestamp = currentRecord.getSecond() * 1000L + currentRecord.getMicroSecond();
            
            // 如果当前值与上一个值相同,更新时间戳继续
            if (currentRecord.getValueStr().equals(lastRecord.getValueStr())) {
                lastTimestamp = currentTimestamp;
                continue;
            }
            
            // 计算持续时间
            long duration = currentTimestamp - lastTimestamp;
            
            // 如果持续时间超过阈值,则认为是有效操作
            if (duration >= MIN_DURATION) {
                filteredRecords.add(lastRecord);
            }
            
            // 更新上一条记录信息
            lastRecord = currentRecord;
            lastTimestamp = currentTimestamp;
        }
        
        // 添加最后一条记录
        filteredRecords.add(lastRecord);
        
        return filteredRecords;
    }

     /**
     * 防抖动滤波
     * 在指定时间窗口内，只保留第一个或最后一个状态变化事件
     * @param hdRecords
     * @return
     */
    public static List<HDRecord> DebounceFilter(List<HDRecord> hdRecords){
        if (hdRecords == null || hdRecords.isEmpty()) {
            return hdRecords;
        }

        // 设置防抖动时间窗口(毫秒)
        final long DEBOUNCE_WINDOW = 1000;
        
        // 存储过滤后的记录
        List<HDRecord> filteredRecords = new ArrayList<>();
        
        // 添加第一条记录
        HDRecord lastRecord = hdRecords.get(0);
        filteredRecords.add(lastRecord);
        long windowStartTime = lastRecord.getSecond() * 1000L + lastRecord.getMicroSecond();
        
        for (int i = 1; i < hdRecords.size(); i++) {
            HDRecord currentRecord = hdRecords.get(i);
            long currentTime = currentRecord.getSecond() * 1000L + currentRecord.getMicroSecond();
            
            // 如果当前值与上一个值相同,直接跳过
            if (currentRecord.getValueStr().equals(lastRecord.getValueStr())) {
                continue;
            }
            
            // 检查是否在防抖动时间窗口内
            if (currentTime - windowStartTime <= DEBOUNCE_WINDOW) {
                // 在时间窗口内,更新最后一条记录(保留最后一个状态)
                filteredRecords.set(filteredRecords.size() - 1, currentRecord);
            } else {
                // 超出时间窗口,添加新记录并重置窗口起始时间
                filteredRecords.add(currentRecord);
                windowStartTime = currentTime;
            }
            
            lastRecord = currentRecord;
        }
        
        return filteredRecords;


    }



}
