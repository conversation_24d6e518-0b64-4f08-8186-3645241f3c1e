package com.baosight.imom.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期时间工具类
 */
public class DateTimeUtil {
    
    /**
     * 将 YYYYMMDDHHMMSS 格式的字符串转换为年月日格式
     * 
     * @param dateTimeStr 格式为YYYYMMDDHHMMSS的日期时间字符串，如：20250114165030
     * @return 年月日格式的字符串，如：2025年01月14日
     */
    public static String convertToYearMonthDay(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.length() != 14) {
            throw new IllegalArgumentException("日期时间字符串格式不正确，应为14位数字：YYYYMMDDHHMMSS");
        }
        
        try {
            // 解析输入的日期时间字符串
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, inputFormatter);
            
            // 格式化为年月日
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
            return dateTime.format(outputFormatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析日期时间字符串：" + dateTimeStr, e);
        }
    }
    
    /**
     * 将 YYYYMMDDHHMMSS 格式的字符串转换为标准日期格式 yyyy-MM-dd
     * 
     * @param dateTimeStr 格式为YYYYMMDDHHMMSS的日期时间字符串
     * @return 标准日期格式字符串，如：2025-01-14
     */
    public static String convertToStandardDate(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.length() != 14) {
            throw new IllegalArgumentException("日期时间字符串格式不正确，应为14位数字：YYYYMMDDHHMMSS");
        }
        
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, inputFormatter);
            
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return dateTime.format(outputFormatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析日期时间字符串：" + dateTimeStr, e);
        }
    }

    public static String convertToYearMonthDayHour(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.length() != 12) {
            throw new IllegalArgumentException("日期时间字符串格式不正确，应为12位数字：YYYYMMDDHHMM");
        }

        try {
            // 解析输入的日期时间字符串
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, inputFormatter);

            // 格式化为年月日
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时");
            return dateTime.format(outputFormatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析日期时间字符串：" + dateTimeStr, e);
        }
    }
}