<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">


<sqlMap namespace="LIRL0221">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            t0502.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            t0502.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            ALLOC_TYPE = #allocType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            SHOW_FLAG = #showFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr((
            select
            ENTER_FACTORY
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr((
            select
            ENTER_FACTORY
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr((
            select
            LEAVE_FACTORY_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr((
            select
            LEAVE_FACTORY_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)E,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr((
            select
            LEAVE_FACTORY_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            from
            ${meliSchema}.tlirl0503 t0503
            where
            t0503.ALLOCATE_VEHICLE_NO = t0502.ALLOCATE_VEHICLE_NO
            and t0503.STATUS != '00'
            and t0503.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0503 t0503
            where
            t0503.ALLOCATE_VEHICLE_NO = t0502.ALLOCATE_VEHICLE_NO
            and t0503.STATUS != '00'
            and t0503.CUSTOMER_ID = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO
            and lirl0301.STATUS in ($status$)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0503 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = t0502.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
    </sql>
    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select * from (SELECT tlirl0301.SEG_NO as "segNo",
        tlirl0301.SEG_NO as "unitCode",
        tlirl0301.STATUS                                                     as "status",
        tlirl0301.REC_CREATOR                                                as "recCreator",
        tlirl0301.REC_CREATOR_NAME                                                     as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                                                     as "recCreateTime",
        tlirl0301.REC_REVISOR                                                          as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                                                     as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                                                     as "recReviseTime",
        tlirl0301.REMARK                                                     as "remark",
        tlirl0301.VEHICLE_NO                                                 as "vehicleNo",


        (
        select
        group_concat(tlirl0502.ALLOCATE_VEHICLE_NO)
        from
        meli.tlirl0502 tlirl0502
        where
        1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0502.STATUS != '00'
        group by tlirl0502.CAR_TRACE_NO
        limit 1

        )  as "allocateVehicleNo",

        (select ALLOC_TYPE
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "allocType",
        ifnull(tlirl0301.CAR_TRACE_NO, tlirl0301.CAR_TRACE_NO)               as "carTraceNo",
        (select SHOW_FLAG
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "showFlag",
        (select ESTIMATED_TIME_OF_ARRIVAL
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "estimatedTimeOfArrival",
        (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') *
        5                                                                    as "theLoadingTimeOfTheBundle",
        (case
        when (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') = 0 then 0
        else (select count(distinct ifnull(t0503.FACTORY_AREA, '-'))
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') * 10 -
        10 end)                                                    as "plantTurnaroundTime",
        30                                                                   as "theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory",
        120                                                                  as "theTimeFromTheSiteToTheDeliveryAddress",
        (select b.IS_RESERVATION
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "isReservation",
        (select START_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "startOfTransport",
        (select PURPOSE_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "purposeOfTransport",
        (select CUSTOMER_NAME
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                            as "customerName",
        tlirl0301.DRIVER_NAME                                                as "driverName",
        tlirl0301.ID_CARD                                                    as "idCard",
        tlirl0301.TEL_NUM                                                    as "telNum",
        tlirl0301.RESERVATION_NUMBER                                         as "reservationNumber",
        (select STATUS
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "appointmentStatus",
        (select RESERVATION_DATE
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "reservationDate",
        (select TYPE_OF_HANDLING
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                             as "businessType",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1))                                            "callDate",
        (IFNULL(tlirl0301.ENTER_FACTORY, tlirl0301.ENTER_FACTORY))           as "enterFactory",
        (IFNULL(tlirl0301.BEGIN_ENTRUCKING_TIME,
        tlirl0301.BEGIN_ENTRUCKING_TIME))                            as "beginEntruckingTime",
        (IFNULL(tlirl0301.COMPLETE_UNINSTALL_TIME,
        tlirl0301.COMPLETE_UNINSTALL_TIME))                          as "completeUninstallTime",
        (IFNULL(tlirl0301.LEAVE_FACTORY_DATE, tlirl0301.LEAVE_FACTORY_DATE)) as "leaveFactoryDate",
        (select MAX(tlirl0407.NEXT_TATGET)
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.FINISH_LOAD_DATE DESC)                                as "nextTarget",
        tlirl0301.TARGET_HAND_POINT_ID                                       as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                      as "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "currentHandPointName",
        tlirl0301.FACTORY_AREA                                               as "factoryArea",
        IFNULL(tlirl0301.FACTORY_AREA_NAME, tlirl0301.FACTORY_AREA_NAME)     as "factoryAreaName",
        (select VOUCHER_NUM
        from MELI.tlirl0503 t0503
        where
        1=1
        and t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.STATUS != '00'
        limit 1)                                                            as "voucherNum",
        (select EMERGENCY_DELIVERY_TIME
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1) as "emergencyDeliveryTime",
        ifnull(A.totalOperationTimeMinutes, ' ')                                       as "totalOperationTimeMinutes",

        case when (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
        then (select case when tlirl0501.AFFILIATED_UNIT is null
        or tlirl0501.AFFILIATED_UNIT = ''
        then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
        where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
        else (select case
        when tlirl0201.VISIT_UNIT is null
        or tlirl0201.VISIT_UNIT = '' then 10
        else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
        where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        end ) = '10'
        then "重庆宝钢"
        when
        (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
        then (select case when tlirl0501.AFFILIATED_UNIT is null
        or tlirl0501.AFFILIATED_UNIT = ''
        then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
        where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
        else (select case
        when tlirl0201.VISIT_UNIT is null
        or tlirl0201.VISIT_UNIT = '' then 10
        else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
        where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        end ) = '20'
        then "杭州宝伟"
        else (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
        then (select case when tlirl0501.AFFILIATED_UNIT is null
        or tlirl0501.AFFILIATED_UNIT = ''
        then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
        where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
        else (select case
        when tlirl0201.VISIT_UNIT is null
        or tlirl0201.VISIT_UNIT = '' then 10
        else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
        where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        end ) end as "visitUnit"

        FROM MELI.tlirl0301 tlirl0301
        LEFT JOIN (select t.CAR_TRACE_NO,
        t.SEG_NO,
        sum(t.totalOperationTimeMinutes) as "totalOperationTimeMinutes"
        from (SELECT tlirl0407.CAR_TRACE_NO,
        tlirl0407.SEG_NO,
        (TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(min(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s'),
        STR_TO_DATE(max(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
        )) AS totalOperationTimeMinutes
        FROM meli.tlirl0407 tlirl0407
        JOIN meli.tlirl0406 tlirl0406
        ON tlirl0407.SEG_NO = tlirl0406.SEG_NO
        AND tlirl0407.LOAD_ID = tlirl0406.LOAD_ID
        AND tlirl0407.CAR_TRACE_NO = tlirl0406.CAR_TRACE_NO
        GROUP BY tlirl0407.SEG_NO, tlirl0407.CURRENT_HAND_POINT_ID, tlirl0407.CAR_TRACE_NO) t
        GROUP BY t.CAR_TRACE_NO, t.SEG_NO) A
        ON A.SEG_NO = tlirl0301.SEG_NO AND A.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        WHERE 1 = 1
        AND tlirl0301.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="visitUnit">
            case when (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '10'
            then "重庆宝钢"
            when
            (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '20'
            then "杭州宝伟"
            else (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) end  = #visitUnit#
        </isNotEmpty>


        <isNotEmpty prepend=" AND " property="unitCode">
            tlirl0301.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            tlirl0301.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            tlirl0301.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            tlirl0301.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            tlirl0301.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            tlirl0301.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            tlirl0301.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            tlirl0301.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            tlirl0301.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            tlirl0301.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            tlirl0301.ALLOCATE_VEHICLE_NO like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ALLOC_TYPE = #allocType#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            tlirl0301.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.SHOW_FLAG = #showFlag#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
            limit 1)
            )
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(tlirl0301.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(tlirl0301.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            ) )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.SEG_NO = tlirl0301.SEG_NO
            and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
        union
        SELECT tlirl0301.SEG_NO as "segNo",
        tlirl0301.SEG_NO as "unitCode",
        tlirl0301.STATUS                                                     as "status",
        tlirl0301.REC_CREATOR                                                as "recCreator",
        tlirl0301.REC_CREATOR_NAME                                                     as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                                                     as "recCreateTime",
        tlirl0301.REC_REVISOR                                                          as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                                                     as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                                                     as "recReviseTime",
        tlirl0301.REMARK                                                     as "remark",
        tlirl0301.VEHICLE_NO                                                 as "vehicleNo",
        (
        select
        group_concat(tlirl0502.ALLOCATE_VEHICLE_NO)
        from
        meli.tlirl0502 tlirl0502
        where
        1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0502.STATUS != '00'

        group by tlirl0502.CAR_TRACE_NO
        limit 1

        )  as "allocateVehicleNo",


        (select ALLOC_TYPE
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "allocType",
        ifnull(tlirl0301.CAR_TRACE_NO, tlirl0301.CAR_TRACE_NO)               as "carTraceNo",
        (select SHOW_FLAG
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "showFlag",
        (select ESTIMATED_TIME_OF_ARRIVAL
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "estimatedTimeOfArrival",
        (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') *
        5                                                                    as "theLoadingTimeOfTheBundle",
        (case
        when (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') = 0 then 0
        else (select count(distinct ifnull(t0503.FACTORY_AREA, '-'))
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') * 10 -
        10 end)                                                    as "plantTurnaroundTime",
        30                                                                   as "theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory",
        120                                                                  as "theTimeFromTheSiteToTheDeliveryAddress",
        (select b.IS_RESERVATION
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "isReservation",
        (select START_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "startOfTransport",
        (select PURPOSE_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "purposeOfTransport",
        (select CUSTOMER_NAME
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                            as "customerName",
        tlirl0301.DRIVER_NAME                                                as "driverName",
        tlirl0301.ID_CARD                                                    as "idCard",
        tlirl0301.TEL_NUM                                                    as "telNum",
        tlirl0301.RESERVATION_NUMBER                                         as "reservationNumber",
        (select STATUS
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "appointmentStatus",
        (select RESERVATION_DATE
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "reservationDate",
        (select TYPE_OF_HANDLING
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                             as "businessType",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1))                                            "callDate",
        (IFNULL(tlirl0301.ENTER_FACTORY, tlirl0301.ENTER_FACTORY))           as "enterFactory",
        (IFNULL(tlirl0301.BEGIN_ENTRUCKING_TIME,
        tlirl0301.BEGIN_ENTRUCKING_TIME))                            as "beginEntruckingTime",
        (IFNULL(tlirl0301.COMPLETE_UNINSTALL_TIME,
        tlirl0301.COMPLETE_UNINSTALL_TIME))                          as "completeUninstallTime",
        (IFNULL(tlirl0301.LEAVE_FACTORY_DATE, tlirl0301.LEAVE_FACTORY_DATE)) as "leaveFactoryDate",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID
        order by tlirl0407.FINISH_LOAD_DATE DESC)                      as "nextTarget",
        tlirl0301.TARGET_HAND_POINT_ID                                       as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                      as "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "currentHandPointName",
        tlirl0301.FACTORY_AREA                                               as "factoryArea",
        IFNULL(tlirl0301.FACTORY_AREA_NAME, tlirl0301.FACTORY_AREA_NAME)     as "factoryAreaName",
        (select VOUCHER_NUM
        from MELI.tlirl0503 t0503
        where
        1=1
        and t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.STATUS != '00'
        limit 1)                                                            as "voucherNum",
        (select EMERGENCY_DELIVERY_TIME
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        as "emergencyDeliveryTime",
        ifnull(A.totalOperationTimeMinutes, ' ')                                       as "totalOperationTimeMinutes",

        case when (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
        then (select case when tlirl0501.AFFILIATED_UNIT is null
        or tlirl0501.AFFILIATED_UNIT = ''
        then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
        where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
        else (select case
        when tlirl0201.VISIT_UNIT is null
        or tlirl0201.VISIT_UNIT = '' then 10
        else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
        where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        end ) = '10'
        then "重庆宝钢"
        when
        (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
        then (select case when tlirl0501.AFFILIATED_UNIT is null
        or tlirl0501.AFFILIATED_UNIT = ''
        then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
        where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
        else (select case
        when tlirl0201.VISIT_UNIT is null
        or tlirl0201.VISIT_UNIT = '' then 10
        else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
        where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        end ) = '20'
        then "杭州宝伟"
        else (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
        then (select case when tlirl0501.AFFILIATED_UNIT is null
        or tlirl0501.AFFILIATED_UNIT = ''
        then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
        where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
        else (select case
        when tlirl0201.VISIT_UNIT is null
        or tlirl0201.VISIT_UNIT = '' then 10
        else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
        where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        end ) end as "visitUnit"

        FROM MELI.tlirl0311 tlirl0301
        LEFT JOIN (select t.CAR_TRACE_NO,
        t.SEG_NO,
        sum(t.totalOperationTimeMinutes) as "totalOperationTimeMinutes"
        from (SELECT tlirl0407.CAR_TRACE_NO,
        tlirl0407.SEG_NO,
        (TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(min(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s'),
        STR_TO_DATE(max(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
        )) AS totalOperationTimeMinutes
        FROM meli.tlirl0407 tlirl0407
        JOIN meli.tlirl0406 tlirl0406
        ON tlirl0407.SEG_NO = tlirl0406.SEG_NO
        AND tlirl0407.LOAD_ID = tlirl0406.LOAD_ID
        AND tlirl0407.CAR_TRACE_NO = tlirl0406.CAR_TRACE_NO
        GROUP BY tlirl0407.SEG_NO, tlirl0407.CURRENT_HAND_POINT_ID, tlirl0407.CAR_TRACE_NO) t
        GROUP BY t.CAR_TRACE_NO, t.SEG_NO) A
        ON A.SEG_NO = tlirl0301.SEG_NO AND A.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        WHERE 1 = 1
        AND tlirl0301.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="visitUnit">
            case when (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '10'
            then "重庆宝钢"
            when
            (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '20'
            then "杭州宝伟"
            else (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) end = #visitUnit#
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="unitCode">
            tlirl0301.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            tlirl0301.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            tlirl0301.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            tlirl0301.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            tlirl0301.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            tlirl0301.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            tlirl0301.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            tlirl0301.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            tlirl0301.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            tlirl0301.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            tlirl0301.ALLOCATE_VEHICLE_NO like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ALLOC_TYPE = #allocType#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            tlirl0301.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.SHOW_FLAG = #showFlag#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
            limit 1)
            )
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(tlirl0301.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(tlirl0301.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            ) )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where
            t0503.SEG_NO = tlirl0301.SEG_NO
            and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
        )A
        order by A.recCreateTime desc

    </select>
    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT DISTINCT tlirl0301.SEG_NO                                     as "segNo",
        tlirl0301.SEG_NO                                     as "unitCode",
        tlirl0301.STATUS                                     as "status",
        tlirl0301.REC_CREATOR                                as "recCreator",
        tlirl0301.REC_CREATOR_NAME                           as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                            as "recCreateTime",
        tlirl0301.REC_REVISOR                                as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                           as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                            as "recReviseTime",
        tlirl0301.REMARK                                     as "remark",
        tlirl0301.VEHICLE_NO                                 as "vehicleNo",
        bd.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
        bd.ALLOC_TYPE                                        as "allocType",
        bd.SHOW_FLAG                                         as "showFlag",
        bd.ESTIMATED_TIME_OF_ARRIVAL                         as "estimatedTimeOfArrival",
        bd.EMERGENCY_DELIVERY_TIME                           as "emergencyDeliveryTime",
        (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') *
        5                                                    as "theLoadingTimeOfTheBundle",
        (case
        when (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') = 0 then 0
        else (select count(distinct ifnull(t0503.FACTORY_AREA, '-'))
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') * 10 - 10 end) as "plantTurnaroundTime",
        30                                                   as "theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory",
        120                                                  as "theTimeFromTheSiteToTheDeliveryAddress",
        ot.totalOperationTimeMinutes                         as "totalOperationTimeMinutes",
        vi.VOUCHER_NUM                                       as "voucherNum",
        tlirl0301.ENTER_FACTORY                              as "enterFactory",
        tlirl0301.BEGIN_ENTRUCKING_TIME                      as "beginEntruckingTime",
        tlirl0301.COMPLETE_UNINSTALL_TIME                    as "completeUninstallTime",
        tlirl0301.LEAVE_FACTORY_DATE                         as "leaveFactoryDate",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID
        order by tlirl0407.FINISH_LOAD_DATE DESC)                                     as "nextTarget",
        tlirl0301.TARGET_HAND_POINT_ID                                                 as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                               as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                                as "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                               as "currentHandPointName",
        tlirl0301.FACTORY_AREA                                                         as "factoryArea",
        tlirl0301.FACTORY_AREA_NAME               as "factoryAreaName"
        FROM (SELECT *
        FROM MELI.tlirl0301
        WHERE SEG_NO = #segNo#
        AND STATUS != '00'
        AND UNIT_CODE = #segNo#
        UNION ALL
        SELECT *
        FROM MELI.tlirl0311
        WHERE SEG_NO = #segNo#
        AND STATUS != '00'
        AND UNIT_CODE = #segNo#) tlirl0301
        LEFT JOIN (SELECT SEG_NO,
        CAR_TRACE_NO,
        VEHICLE_NO,
        ALLOCATE_VEHICLE_NO,
        ALLOC_TYPE,
        SHOW_FLAG,
        ESTIMATED_TIME_OF_ARRIVAL,
        EMERGENCY_DELIVERY_TIME
        FROM meli.tlirl0502
        WHERE SEG_NO = #segNo#) bd
        ON bd.SEG_NO = tlirl0301.SEG_NO AND bd.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO AND
        bd.VEHICLE_NO = tlirl0301.VEHICLE_NO
        LEFT JOIN (SELECT t.CAR_TRACE_NO, t.SEG_NO, SUM(t.totalOperationTimeMinutes) as totalOperationTimeMinutes
        FROM (SELECT tlirl0407.CAR_TRACE_NO,
        tlirl0407.SEG_NO,
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(MIN(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s'),
        STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')) AS totalOperationTimeMinutes
        FROM meli.tlirl0407 tlirl0407
        JOIN meli.tlirl0406 tlirl0406 ON tlirl0407.SEG_NO = tlirl0406.SEG_NO AND
        tlirl0407.LOAD_ID = tlirl0406.LOAD_ID AND
        tlirl0407.CAR_TRACE_NO = tlirl0406.CAR_TRACE_NO
        WHERE tlirl0407.SEG_NO = #segNo#
        GROUP BY tlirl0407.SEG_NO, tlirl0407.CURRENT_HAND_POINT_ID, tlirl0407.CAR_TRACE_NO) t
        GROUP BY t.CAR_TRACE_NO, t.SEG_NO) ot
        ON ot.SEG_NO = tlirl0301.SEG_NO AND ot.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        LEFT JOIN (SELECT ALLOCATE_VEHICLE_NO, MAX(VOUCHER_NUM) as VOUCHER_NUM
        FROM MELI.tlirl0503
        WHERE SEG_NO = #segNo#
        AND STATUS != '00'
        AND DEL_FLAG = 0
        GROUP BY ALLOCATE_VEHICLE_NO) vi ON vi.ALLOCATE_VEHICLE_NO = bd.ALLOCATE_VEHICLE_NO
        WHERE bd.EMERGENCY_DELIVERY_TIME != ' '
        AND vi.VOUCHER_NUM IS NOT NULL
        AND (vi.VOUCHER_NUM LIKE 'BL%' OR vi.VOUCHER_NUM LIKE 'ZK%')
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            tlirl0301.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            tlirl0301.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            tlirl0301.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            tlirl0301.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            tlirl0301.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            tlirl0301.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            tlirl0301.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            tlirl0301.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            tlirl0301.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            tlirl0301.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            tlirl0301.ALLOCATE_VEHICLE_NO like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ALLOC_TYPE = #allocType#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            tlirl0301.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.SHOW_FLAG = #showFlag#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
            limit 1)
            )
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(tlirl0301.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(tlirl0301.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            ) )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
        order by tlirl0301.REC_CREATE_TIME desc

    </select>
    <select id="count" resultClass="int" >
        select count(1) from (SELECT tlirl0301.SEG_NO as "segNo",
        tlirl0301.SEG_NO as "unitCode",
        tlirl0301.STATUS                                                     as "status",
        tlirl0301.REC_CREATOR                                                as "recCreator",
        tlirl0301.REC_CREATOR_NAME                                                     as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                                                     as "recCreateTime",
        tlirl0301.REC_REVISOR                                                          as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                                                     as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                                                     as "recReviseTime",
        tlirl0301.REMARK                                                     as "remark",
        tlirl0301.VEHICLE_NO                                                 as "vehicleNo",
        tlirl0301.ALLOCATE_VEHICLE_NO                                                           as "allocateVehicleNo",
        (select ALLOC_TYPE
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "allocType",
        ifnull(tlirl0301.CAR_TRACE_NO, tlirl0301.CAR_TRACE_NO)               as "carTraceNo",
        (select SHOW_FLAG
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "showFlag",
        (select ESTIMATED_TIME_OF_ARRIVAL
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "estimatedTimeOfArrival",
        (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') *
        5                                                                    as "theLoadingTimeOfTheBundle",
        (case
        when (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') = 0 then 0
        else (select count(distinct ifnull(t0503.FACTORY_AREA, '-'))
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') * 10 -
        10 end)                                                    as "plantTurnaroundTime",
        30                                                                   as "theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory",
        120                                                                  as "theTimeFromTheSiteToTheDeliveryAddress",
        (select b.IS_RESERVATION
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "isReservation",
        (select START_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "startOfTransport",
        (select PURPOSE_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "purposeOfTransport",
        (select CUSTOMER_NAME
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                            as "customerName",
        tlirl0301.DRIVER_NAME                                                as "driverName",
        tlirl0301.ID_CARD                                                    as "idCard",
        tlirl0301.TEL_NUM                                                    as "telNum",
        tlirl0301.RESERVATION_NUMBER                                         as "reservationNumber",
        (select STATUS
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "appointmentStatus",
        (select RESERVATION_DATE
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "reservationDate",
        (select TYPE_OF_HANDLING
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                             as "businessType",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1))                                            "callDate",
        (IFNULL(tlirl0301.ENTER_FACTORY, tlirl0301.ENTER_FACTORY))           as "enterFactory",
        (IFNULL(tlirl0301.BEGIN_ENTRUCKING_TIME,
        tlirl0301.BEGIN_ENTRUCKING_TIME))                            as "beginEntruckingTime",
        (IFNULL(tlirl0301.COMPLETE_UNINSTALL_TIME,
        tlirl0301.COMPLETE_UNINSTALL_TIME))                          as "completeUninstallTime",
        (IFNULL(tlirl0301.LEAVE_FACTORY_DATE, tlirl0301.LEAVE_FACTORY_DATE)) as "leaveFactoryDate",
        (select MAX(tlirl0407.NEXT_TATGET)
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.FINISH_LOAD_DATE DESC)                                as "nextTarget",
        tlirl0301.TARGET_HAND_POINT_ID                                       as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                      as "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "currentHandPointName",
        tlirl0301.FACTORY_AREA                                               as "factoryArea",
        IFNULL(tlirl0301.FACTORY_AREA_NAME, tlirl0301.FACTORY_AREA_NAME)     as "factoryAreaName",
        (select VOUCHER_NUM
        from MELI.tlirl0503 t0503
        where
        1=1
        and t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.STATUS != '00'
        limit 1)                                                            as "voucherNum",
        (select EMERGENCY_DELIVERY_TIME
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1) as "emergencyDeliveryTime"
        FROM MELI.tlirl0301 tlirl0301
        WHERE 1 = 1
        AND tlirl0301.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="visitUnit">
            case when (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '10'
            then "重庆宝钢"
            when
            (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '20'
            then "杭州宝伟"
            else (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) end = #visitUnit#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            tlirl0301.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            tlirl0301.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            tlirl0301.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            tlirl0301.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            tlirl0301.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            tlirl0301.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            tlirl0301.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            tlirl0301.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            tlirl0301.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            tlirl0301.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            tlirl0301.ALLOCATE_VEHICLE_NO like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ALLOC_TYPE = #allocType#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            tlirl0301.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.SHOW_FLAG = #showFlag#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
            limit 1)
            )
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(tlirl0301.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(tlirl0301.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            ) )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where
            t0503.SEG_NO = tlirl0301.SEG_NO
            and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
        union
        SELECT tlirl0301.SEG_NO as "segNo",
        tlirl0301.SEG_NO as "unitCode",
        tlirl0301.STATUS                                                     as "status",
        tlirl0301.REC_CREATOR                                                as "recCreator",
        tlirl0301.REC_CREATOR_NAME                                                     as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                                                     as "recCreateTime",
        tlirl0301.REC_REVISOR                                                          as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                                                     as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                                                     as "recReviseTime",
        tlirl0301.REMARK                                                     as "remark",
        tlirl0301.VEHICLE_NO                                                 as "vehicleNo",
        tlirl0301.ALLOCATE_VEHICLE_NO                                                            as "allocateVehicleNo",
        (select ALLOC_TYPE
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "allocType",
        ifnull(tlirl0301.CAR_TRACE_NO, tlirl0301.CAR_TRACE_NO)               as "carTraceNo",
        (select SHOW_FLAG
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "showFlag",
        (select ESTIMATED_TIME_OF_ARRIVAL
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)                                                            as "estimatedTimeOfArrival",
        (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') *
        5                                                                    as "theLoadingTimeOfTheBundle",
        (case
        when (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') = 0 then 0
        else (select count(distinct ifnull(t0503.FACTORY_AREA, '-'))
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') * 10 -
        10 end)                                                    as "plantTurnaroundTime",
        30                                                                   as "theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory",
        120                                                                  as "theTimeFromTheSiteToTheDeliveryAddress",
        (select b.IS_RESERVATION
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "isReservation",
        (select START_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "startOfTransport",
        (select PURPOSE_OF_TRANSPORT
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "purposeOfTransport",
        (select CUSTOMER_NAME
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                            as "customerName",
        tlirl0301.DRIVER_NAME                                                as "driverName",
        tlirl0301.ID_CARD                                                    as "idCard",
        tlirl0301.TEL_NUM                                                    as "telNum",
        tlirl0301.RESERVATION_NUMBER                                         as "reservationNumber",
        (select STATUS
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "appointmentStatus",
        (select RESERVATION_DATE
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                                "reservationDate",
        (select TYPE_OF_HANDLING
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                             as "businessType",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and b.DEL_FLAG = '0'
        limit 1))                                            "callDate",
        (IFNULL(tlirl0301.ENTER_FACTORY, tlirl0301.ENTER_FACTORY))           as "enterFactory",
        (IFNULL(tlirl0301.BEGIN_ENTRUCKING_TIME,
        tlirl0301.BEGIN_ENTRUCKING_TIME))                            as "beginEntruckingTime",
        (IFNULL(tlirl0301.COMPLETE_UNINSTALL_TIME,
        tlirl0301.COMPLETE_UNINSTALL_TIME))                          as "completeUninstallTime",
        (IFNULL(tlirl0301.LEAVE_FACTORY_DATE, tlirl0301.LEAVE_FACTORY_DATE)) as "leaveFactoryDate",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID
        order by tlirl0407.FINISH_LOAD_DATE DESC)                      as "nextTarget",
        tlirl0301.TARGET_HAND_POINT_ID                                       as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                      as "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                     as "currentHandPointName",
        tlirl0301.FACTORY_AREA                                               as "factoryArea",
        IFNULL(tlirl0301.FACTORY_AREA_NAME, tlirl0301.FACTORY_AREA_NAME)     as "factoryAreaName",
        (select VOUCHER_NUM
        from MELI.tlirl0503 t0503
        where
        1=1
        and t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.STATUS != '00'
        limit 1)                                                            as "voucherNum",
        (select EMERGENCY_DELIVERY_TIME
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        as "emergencyDeliveryTime"
        FROM MELI.tlirl0311 tlirl0301
        WHERE 1 = 1
        AND tlirl0301.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="visitUnit">
            case when (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '10'
            then "重庆宝钢"
            when
            (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) = '20'
            then "杭州宝伟"
            else (case when tlirl0301.RESERVATION_NUMBER is null or tlirl0301.RESERVATION_NUMBER = ''
            then (select case when tlirl0501.AFFILIATED_UNIT is null
            or tlirl0501.AFFILIATED_UNIT = ''
            then 10 else tlirl0501.AFFILIATED_UNIT end  from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null
            or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO =  tlirl0301.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
            end ) end = #visitUnit#
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="unitCode">
            tlirl0301.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            tlirl0301.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            tlirl0301.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            tlirl0301.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            tlirl0301.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            tlirl0301.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            tlirl0301.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            tlirl0301.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            tlirl0301.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            tlirl0301.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            tlirl0301.ALLOCATE_VEHICLE_NO like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ALLOC_TYPE = #allocType#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            tlirl0301.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.SHOW_FLAG = #showFlag#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
            limit 1)
            )
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(tlirl0301.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(tlirl0301.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            ) )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.SEG_NO = tlirl0301.SEG_NO
            and  t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
        )A
        order by A.recCreateTime desc

    </select>
    <select id="queryAllCount" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select count(1) from (
        SELECT DISTINCT tlirl0301.SEG_NO                                     as "segNo",
        tlirl0301.SEG_NO                                     as "unitCode",
        tlirl0301.STATUS                                     as "status",
        tlirl0301.REC_CREATOR                                as "recCreator",
        tlirl0301.REC_CREATOR_NAME                           as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                            as "recCreateTime",
        tlirl0301.REC_REVISOR                                as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                           as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                            as "recReviseTime",
        tlirl0301.REMARK                                     as "remark",
        tlirl0301.VEHICLE_NO                                 as "vehicleNo",
        bd.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
        bd.ALLOC_TYPE                                        as "allocType",
        bd.SHOW_FLAG                                         as "showFlag",
        bd.ESTIMATED_TIME_OF_ARRIVAL                         as "estimatedTimeOfArrival",
        bd.EMERGENCY_DELIVERY_TIME                           as "emergencyDeliveryTime",
        (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') *
        5                                                    as "theLoadingTimeOfTheBundle",
        (case
        when (select count(1)
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') = 0 then 0
        else (select count(distinct ifnull(t0503.FACTORY_AREA, '-'))
        from MELI.tlirl0503 t0503
        where t0503.SEG_NO = tlirl0301.SEG_NO
        and t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)
        and t0503.DEL_FLAG = 0
        and t0503.STATUS > '00') * 10 - 10 end) as "plantTurnaroundTime",
        30                                                   as "theTimeFromTheCompletionOfLoadingToTheTimeOfLeavingTheFactory",
        120                                                  as "theTimeFromTheSiteToTheDeliveryAddress",
        ot.totalOperationTimeMinutes                         as "totalOperationTimeMinutes",
        vi.VOUCHER_NUM                                       as "voucherNum",
        tlirl0301.ENTER_FACTORY                              as "enterFactory",
        tlirl0301.BEGIN_ENTRUCKING_TIME                      as "beginEntruckingTime",
        tlirl0301.COMPLETE_UNINSTALL_TIME                    as "completeUninstallTime",
        tlirl0301.LEAVE_FACTORY_DATE                         as "leaveFactoryDate",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID
        order by tlirl0407.FINISH_LOAD_DATE DESC)                                     as "nextTarget",
        tlirl0301.TARGET_HAND_POINT_ID                                                 as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                               as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                                as "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                               as "currentHandPointName",
        tlirl0301.FACTORY_AREA                                                         as "factoryArea",
        tlirl0301.FACTORY_AREA_NAME               as "factoryAreaName"
        FROM (SELECT *
        FROM MELI.tlirl0301
        WHERE SEG_NO = #segNo#
        AND STATUS != '00'
        AND UNIT_CODE = #segNo#
        UNION ALL
        SELECT *
        FROM MELI.tlirl0311
        WHERE SEG_NO = #segNo#
        AND STATUS != '00'
        AND UNIT_CODE = #segNo#) tlirl0301
        LEFT JOIN (SELECT SEG_NO,
        CAR_TRACE_NO,
        VEHICLE_NO,
        ALLOCATE_VEHICLE_NO,
        ALLOC_TYPE,
        SHOW_FLAG,
        ESTIMATED_TIME_OF_ARRIVAL,
        EMERGENCY_DELIVERY_TIME
        FROM meli.tlirl0502
        WHERE SEG_NO = #segNo#) bd
        ON bd.SEG_NO = tlirl0301.SEG_NO AND bd.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO AND
        bd.VEHICLE_NO = tlirl0301.VEHICLE_NO
        LEFT JOIN (SELECT t.CAR_TRACE_NO, t.SEG_NO, SUM(t.totalOperationTimeMinutes) as totalOperationTimeMinutes
        FROM (SELECT tlirl0407.CAR_TRACE_NO,
        tlirl0407.SEG_NO,
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(MIN(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s'),
        STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')) AS totalOperationTimeMinutes
        FROM meli.tlirl0407 tlirl0407
        JOIN meli.tlirl0406 tlirl0406 ON tlirl0407.SEG_NO = tlirl0406.SEG_NO AND
        tlirl0407.LOAD_ID = tlirl0406.LOAD_ID AND
        tlirl0407.CAR_TRACE_NO = tlirl0406.CAR_TRACE_NO
        WHERE tlirl0407.SEG_NO = #segNo#
        GROUP BY tlirl0407.SEG_NO, tlirl0407.CURRENT_HAND_POINT_ID, tlirl0407.CAR_TRACE_NO) t
        GROUP BY t.CAR_TRACE_NO, t.SEG_NO) ot
        ON ot.SEG_NO = tlirl0301.SEG_NO AND ot.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        LEFT JOIN (SELECT ALLOCATE_VEHICLE_NO, MAX(VOUCHER_NUM) as VOUCHER_NUM
        FROM MELI.tlirl0503
        WHERE SEG_NO = #segNo#
        AND STATUS != '00'
        AND DEL_FLAG = 0
        GROUP BY ALLOCATE_VEHICLE_NO) vi ON vi.ALLOCATE_VEHICLE_NO = bd.ALLOCATE_VEHICLE_NO
        WHERE bd.EMERGENCY_DELIVERY_TIME != ' '
        AND vi.VOUCHER_NUM IS NOT NULL
        AND (vi.VOUCHER_NUM LIKE 'BL%' OR vi.VOUCHER_NUM LIKE 'ZK%')
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            tlirl0301.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            tlirl0301.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            tlirl0301.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            tlirl0301.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            tlirl0301.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            tlirl0301.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            tlirl0301.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            tlirl0301.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            tlirl0301.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            tlirl0301.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            exists (select 1
            from MELI.tlirl0201 b
            where 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            tlirl0301.ALLOCATE_VEHICLE_NO like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ALLOC_TYPE = #allocType#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            tlirl0301.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.SHOW_FLAG = #showFlag#
            limit 1)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            EXISTS((select 1
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.ESTIMATED_TIME_OF_ARRIVAL = #estimatedTimeOfArrival#
            limit 1)
            )
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr((
            select
            CHECK_DATE
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO),1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(tlirl0301.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(tlirl0301.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(tlirl0301.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS  (
            SELECT
            1
            from
            ${meliSchema}.tlirl0201 tlirl0201
            where 1=1
            and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.STATUS != '00'
            and tlirl0201.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            tlirl0301.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            tlirl0301.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = (
            select
            RESERVATION_NUMBER
            from
            ${meliSchema}.tlirl0301 lirl0301
            where
            lirl0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO)
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            ) )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            exists(
            (select 1
            from MELI.tlirl0503 t0503
            where t0503.ALLOCATE_VEHICLE_NO = (select ALLOCATE_VEHICLE_NO
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            limit 1)
            and t0503.PACK_ID like concat('%',#packId#,'%')
            ) )
        </isNotEmpty>
        order by tlirl0301.REC_CREATE_TIME desc
        )A
    </select>
</sqlMap>