$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //编辑行
    let editorCell;

    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "factoryArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                },
                {
                    field: "factoryAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                }, {
                    field: "factoryBuilding",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂房代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                }, {
                    field: "factoryBuildingName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂房名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                },
                {
                    field: "crossArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "跨区编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "跨区编码查询"
                            })
                        }
                    }
                }
            ],
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {
                //启用(状态为禁用，新增时可启用)
                $("#ENABLE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "10" && rows[i].status !== "99" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为新增或禁用,不可启用!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0101", "enableArea", true, null, null, false);
                });

                //禁用(状态不为启用，不可禁用)
                $("#DISABLE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "10" && rows[i].status !== "30" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为新增或启用,不可禁用!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0101", "disableArea", true, null, null, false);
                });
            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'areaCode', "");
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
                /**
                 * 校验区域类型为通道时，才可以修改装卸货通道类型
                 */
                if (e.field === "aisleType" && e.model.areaType !== "40") {
                    e.preventDefault();
                    return;
                }

                /**
                 * 状态不为新增时，不允许编辑
                 */
                if(e.model.status !== "10" && !e.model.isNew()){
                    e.preventDefault();
                    return;
                }

            },
            onDelete: function (e) {
                let rows = e.sender.getCheckedRows();
                for (let i = 0; i < rows.length; i++) {
                    if (rows[i].status !== "10") {
                        NotificationUtil({msg: "勾选数据状态不为新增,不可删除!"}, "error");
                        e.preventDefault();
                        return;
                    }
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //厂区厂房管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-factoryArea").val(rows[0].factoryArea);
                $("#inqu_status-0-factoryAreaName").val(rows[0].factoryAreaName);
                $("#inqu_status-0-factoryBuilding").val(rows[0].factoryBuilding);
                $("#inqu_status-0-factoryBuildingName").val(rows[0].factoryBuildingName);
            }
        }
    });

    //厂区厂房管理弹窗(表格用)
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfoGrid",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorCell.model, "factoryArea", rows[0].factoryArea);
                resultGrid.setCellValue(editorCell.model, "factoryAreaName", rows[0].factoryAreaName);
                resultGrid.setCellValue(editorCell.model, "factoryBuilding", rows[0].factoryBuilding);
                resultGrid.setCellValue(editorCell.model, "factoryBuildingName", rows[0].factoryBuildingName);
            }
        }
    });

    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            /*if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "crossArea", rows[0].crossArea);
                resultGrid.setCellValue(editorModel, "crossAreaName", rows[0].crossAreaName);
            }*/
            var checkedRows = resultGrid.getCheckedRows();
            if (checkedRows.length > 0 && rows.length > 0) {
                for(let i=0;i<checkedRows.length;i++){
                    resultGrid.setCellValue(checkedRows[i], "crossArea", rows[0].crossArea);
                    resultGrid.setCellValue(checkedRows[i], "crossAreaName", rows[0].crossAreaName);
                }
            }

        }
    });

})