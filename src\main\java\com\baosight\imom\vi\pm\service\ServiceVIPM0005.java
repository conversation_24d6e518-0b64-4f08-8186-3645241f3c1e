package com.baosight.imom.vi.pm.service;

import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.vi.pm.domain.VIPM0005;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * 标准工时查询页面后台
 *
 * <AUTHOR> 郁在杰
 * @Description : 标准工时查询页面后台
 * @Date : 2025/7/16
 * @Version : 1.0
 */
public class ServiceVIPM0005 extends ServiceBase {

    /**
     * 页面初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VIPM0005().eiMetadata);
        // 业务单元
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VIPM0005.QUERY, new VIPM0005());
    }

}
