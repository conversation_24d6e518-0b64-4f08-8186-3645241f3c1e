create table TMEDV0004
(
    ALARM_ID          VARCHAR(16) default ' '    not null comment '报警ID',
    DA_NAME           VARCHAR(32) default ' '    not null comment 'scada名',
    ALARM_TAG         VARCHAR(64) default ' '    not null comment '报警点',
    ALARM_TAG_DESC    VARCHAR(64) default ' '    not null comment '报警点描述',
    ALARM_ADDRESS     VARCHAR(64) default ' '    not null comment '报警地址',
    CONFIRM_STATUS    VARCHAR(20) default ' '    not null comment '确认状态',
    CONFIRMOR         VARCHAR(50) default ' '    not null comment '确认人',
    CONFIRM_TIME      VARCHAR(24) default ' '    not null comment '确认时间',
    OCCUR_TIME        VARCHAR(24) default ' '    not null comment '发生时间',
    RECOVER_TIME      VARCHAR(24) default ' '    not null comment '恢复时间',
    ALARM_TYPE        VARCHAR(10) default ' '    not null comment '报警类型',
    ALARM_TAG_VALUE   VARCHAR(32) default ' '    not null comment '报警值',
    RECOVER_TAG_VALUE VARCHAR(32) default ' '    not null comment '恢复值',
    PRIORITY          INT         default 0      not null comment '优先级',
    ALARM_STATE       VARCHAR(20) default ' '    not null comment '报警状态',
    REPEAT_COUNT      INT         default 0      not null comment '重复报警次数',
    ALARM_TYPEDM      VARCHAR(2)  default ' '    not null comment '报警类型代码',
    -- 固定字段
    UUID              VARCHAR(32)                NOT NULL COMMENT '唯一编码',
    REC_CREATOR       VARCHAR(16) DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME   VARCHAR(17) DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR       VARCHAR(16) DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME   VARCHAR(17) DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID         VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG      VARCHAR(1)  DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='设备报警表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;