package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0104;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;

/**
 * 设备履历表
 */
public class VGDM0104 extends Tvgdm0104 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0104.query";
    /**
     * 计数
     */
    public static final String COUNT = "VGDM0104.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0104.insert";
    /**
     * 更新
     */
    public static final String UPDATE = "VGDM0104.update";

    /**
     * 数据来源
     */
    public static class RelevanceType {
        /**
         * 点检异常
         */
        public static final String EXCEPTION = "10";
        /**
         * 设备报警
         */
        public static final String ALARM = "11";
        /**
         * 故障信息
         */
        public static final String FAULT = "12";
        /**
         * 检修记录
         */
        public static final String OVERHAUL = "13";
    }

    /**
     * 添加履历
     *
     * @param block         待添加数据对象
     * @param relevanceType 数据来源
     */
    public static void addHistory(EiBlock block, String relevanceType) {
        EiInfo eiInfo = new EiInfo();
        // 设置待添加数据对象
        eiInfo.getBlocks().put(EiConstant.resultBlock, block);
        // 设置数据来源
        eiInfo.set("relevanceType", relevanceType);
        // 设置服务名和方法名
        eiInfo.set(EiConstant.serviceName, "VGDM0104");
        eiInfo.set(EiConstant.methodName, "addResume");
        // 调用服务
        EiInfo rtnInfo = XLocalManager.call(eiInfo);
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
    }
}
