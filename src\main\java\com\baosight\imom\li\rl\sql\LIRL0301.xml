<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-09-04 14:39:25
       Version :  1.0
    tableName :${meliSchema}.tlirl0301 
     SEG_NO  VARCHAR   NOT NULL   primarykey, 
     UNIT_CODE  VARCHAR   NOT NULL, 
     CAR_TRACE_NO  VARCHAR   NOT NULL   primarykey, 
     STATUS  VARCHAR   NOT NULL, 
     HAND_TYPE  VARCHAR   NOT NULL, 
     VEHICLE_NO  VARCHAR   NOT NULL, 
     ID_CARD  VARCHAR   NOT NULL, 
     DRIVER_NAME  VARCHAR   NOT NULL, 
     TEL_NUM  VARCHAR   NOT NULL, 
     RESERVATION_NUMBER  VARCHAR   NOT NULL, 
     CHECK_DATE  VARCHAR   NOT NULL, 
     <PERSON>NTER_FACTORY  VARCHAR   NOT NULL, 
     BEGIN_ENTRUCKING_TIME  VARCHAR   NOT NULL, 
     COMPLETE_UNINSTALL_TIME  VARCHAR   NOT NULL, 
     LEAVE_FACTORY_DATE  VARCHAR   NOT NULL, 
     CUSTOMER_SIGNING_TIME  VARCHAR   NOT NULL, 
     TARGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
     CURRENT_HAND_POINT_ID  VARCHAR   NOT NULL, 
     FACTORY_AREA  VARCHAR   NOT NULL, 
     UNLOAD_LEAVE_FLAG  VARCHAR   NOT NULL, 
     REC_CREATOR  VARCHAR   NOT NULL, 
     REC_CREATOR_NAME  VARCHAR   NOT NULL, 
     REC_CREATE_TIME  VARCHAR   NOT NULL, 
     REC_REVISOR  VARCHAR   NOT NULL, 
     REC_REVISOR_NAME  VARCHAR   NOT NULL, 
     REC_REVISE_TIME  VARCHAR   NOT NULL, 
     ARCHIVE_FLAG  SMALLINT   NOT NULL, 
     DEL_FLAG  SMALLINT   NOT NULL, 
     REMARK  VARCHAR   NOT NULL, 
     SYS_REMARK  VARCHAR   NOT NULL, 
     UUID  VARCHAR   NOT NULL, 
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0301">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0301">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        ID_CARD as "idCard",  <!-- 身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        TEL_NUM as "telNum",  <!-- 手机号 -->
        RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        ifnull(if(FACTORY_AREA = '',null,FACTORY_AREA),(select b.FACTORY_AREA from ${meliSchema}.tlirl0302 b where b.SEG_NO =
        a.SEG_NO and b.CAR_TRACE_NO = a.CAR_TRACE_NO)) as "factoryArea",
        ifnull(if(FACTORY_AREA_NAME = '',null,FACTORY_AREA_NAME),(select b.FACTORY_AREA_NAME from ${meliSchema}.tlirl0302 b where
        b.SEG_NO = a.SEG_NO and b.CAR_TRACE_NO = a.CAR_TRACE_NO)) as "factoryAreaName",
        UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId",
        (select tlirl0201.START_OF_TRANSPORT from meli.tlirl0201 tlirl0201
        where 1=1 and STATUS='20'
        and tlirl0201.SEG_NO=a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER=a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO=a.VEHICLE_NO
        limit 1) as "startOfTransport",
        EXPECTED_LOADING_TIME as "expectedLoadingTime",
        (select tlirl0201.TYPE_OF_HANDLING
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and STATUS = '20'
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1) as "typeOfHandling",
        a.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
        IF_PAUSE AS "ifPause",
        PAUSE_TIME AS "pauseTime"
        FROM ${meliSchema}.tlirl0301 a WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNo">
            STATUS != #statusNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusIn1">
            STATUS in ('20','30','40')
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumber">
            RESERVATION_NUMBER = #reservationNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusAll">
            STATUS != #statusAll#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryFlag">
            (a.TARGET_HAND_POINT_ID=' ' and a.CURRENT_HAND_POINT_ID=' ')
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="statusList">
            STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <!--登记日期-->
        <isNotEmpty prepend=" and " property="checkDateTime">
            substr(a.CHECK_DATE,1,10) = replace(#checkDateTime#,'-','')
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

<!--查询正在作业的装卸点-->
    <select id="queryLadingInfo" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0301">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->

        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        CURRENT_HAND_POINT_ID as "handPointId",  <!-- 当前装卸点代码 -->
        (select HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where 1 = 1
        and tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '20') as "handPointName"
        FROM ${meliSchema}.tlirl0301 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumber">
            RESERVATION_NUMBER = #reservationNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusAll">
            STATUS != #statusAll#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>
<!--查询该装卸点正在作业的车辆信息-->
    <select id="queryLadingVehicle" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        tlirl0301.VEHICLE_NO as "vehicleNo",
        tlirl0301.STATUS as "status",
        tlirl0301.CAR_TRACE_NO AS "carTraceNo",
        tlirl0301.CURRENT_HAND_POINT_ID AS "handPointId",
        (select HAND_POINT_NAME from meli.tlirl0304 tlirl0304 where 1=1 and
        tlirl0304.HAND_POINT_ID=tlirl0301.CURRENT_HAND_POINT_ID and status='30' limit 1) as "handPointName",
        tlirl0301.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
        tlirl0301.TEL_NUM as "driverTel"
        FROM ${meliSchema}.tlirl0301 tlirl0301 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0301.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId">
            tlirl0301.CURRENT_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            tlirl0301.FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0301.STATUS = #status#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                tlirl0301.REC_REVISE_TIME asc
            </isEmpty>
        </dynamic>

    </select>


    <select id="queryKBdata" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        REPLACE(ifnull(ifnull((select ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE =
        ( select tlirl0201.TYPE_OF_HANDLING
        from ${meliSchema}.tlirl0201 tlirl0201 where 1 = 1
        and tlirl0201.STATUS = '20'
        and tlirl0201.IS_RESERVATION = '10'
        and tlirl0201.SEG_NO = tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER limit 1)
        and tedcm01.CODESET_CODE = 'P007'),
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE =
        (select tlirl0302.HAND_TYPE
        from ${meliSchema}.tlirl0302 tlirl0302
        where 1 = 1 and tlirl0302.STATUS > 00
        and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0302.VOUCHER_NUM != '' )
        and tedcm01.CODESET_CODE = 'P007') ),
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE =
        (select BUSINESS_TYPE
        from ${meliSchema}.tlirl0302 tlirl0302
        where 1 = 1 and tlirl0302.STATUS > 00
        and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO limit 1)
        and tedcm01.CODESET_CODE = 'P010')), '钢材卸货+装货', '卸货装货') as "businessType",
        ifnull(tlirl0301.TARGET_HAND_POINT_ID,
        tlirl0301.CURRENT_HAND_POINT_ID) as "targetHandPointId",
        ifnull((select HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        limit 1),
        (select HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        limit 1) ) AS "targetHandPointIdName",
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(CHECK_DATE, '%Y%m%d%H%i%s'), now()) as "waitingTime",
        (select tlirl0201.START_OF_TRANSPORT
        from ${meliSchema}.tlirl0201 tlirl0201
        where tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and tlirl0201.TYPE_OF_HANDLING = '20'
        and tlirl0201.STATUS = '20'
        and tlirl0301.STATUS != '00'
        limit 1) as "startOfTransport"
        FROM ${meliSchema}.tlirl0301 tlirl0301 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        and not exists(select 1
        from ${meliSchema}.tlirl0402 tlirl0402
        where tlirl0402.SEG_NO = tlirl0301.SEG_NO
        and tlirl0402.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0402.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0402.DEL_FLAG = '0')
        <isNotEmpty prepend=" AND " property="statusList">
            STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SEG_NO asc,
                CAR_TRACE_NO asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryKBdataCount"
            resultClass="int">
        SELECT
        count(1) as "count"
        FROM ${meliSchema}.tlirl0301 tlirl0301 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        and not exists(select 1
        from ${meliSchema}.tlirl0402 tlirl0402
        where tlirl0402.SEG_NO = tlirl0301.SEG_NO
        and tlirl0402.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0402.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0402.DEL_FLAG = '0')
        <isNotEmpty prepend=" AND " property="statusList">
            STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SEG_NO asc,
                CAR_TRACE_NO asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryVehicle"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        tlirl0301.VEHICLE_NO AS "vehicleNo",
        tlirl0301.CAR_TRACE_NO as "carTraceNo",
        tlirl0301.DRIVER_NAME as "driverName",
        tlirl0301.TEL_NUM as "driverTel",
        tlirl0301.ALLOCATE_VEHICLE_NO as "allocateVehicleNo"
        FROM ${meliSchema}.tlirl0301 tlirl0301 WHERE 1=1
       AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS =#status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handTypeList">
            HAND_TYPE in
            <iterate property="handTypeList" open="("
                     close=")" conjunction=" , ">
                #handTypeList[]#
            </iterate>
        </isNotEmpty>
        AND (STATUS ='20' or STATUS = '40')
        <isNotEmpty prepend=" AND " property="targetHandPointIdNotNull">
            TARGET_HAND_POINT_ID != ''
        </isNotEmpty>
            <isNotEmpty prepend=" AND " property="targetHandPointIdNull">
            TARGET_HAND_POINT_ID = ''
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SEG_NO asc,
                CAR_TRACE_NO asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryVehicleCQ"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select * from (SELECT tlirl0401.VEHICLE_NO          AS "vehicleNo",
        tlirl0401.CAR_TRACE_NO        as "carTraceNo",
        tlirl0301.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
        (select TEL_NUM
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
        and tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
        limit 1)                     as "driverTel",
        (select DRIVER_NAME
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
        and tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
        limit 1)                     as "driverName",
        tlirl0401.QUEUE_NUMBER        AS "recCreateTime",
        tlirl0401.PRIORITY_LEVEL as "priorityLevel"
        FROM MELI.tlirl0401 tlirl0401,
        meli.tlirl0301
        WHERE 1 = 1
        and tlirl0401.SEG_NO = tlirl0301.SEG_NO
        and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0401.VOUCHER_NUM = tlirl0301.ALLOCATE_VEHICLE_NO
        and tlirl0401.VOUCHER_NUM != ''
        AND tlirl0401.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="handPointId401">
            tlirl0401.TARGET_HAND_POINT_ID = #handPointId401#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId503">
            exists(select 1 from ${meliSchema}.tlirl0503 tlirl0503,${meliSchema}.tlirl0502 tlirl0502
            where tlirl0502.SEG_NO = tlirl0503.SEG_NO
            and tlirl0502.ALLOCATE_VEHICLE_NO=tlirl0503.ALLOCATE_VEHICLE_NO
            and tlirl0502.DEL_FLAG = 0
            and tlirl0502.STATUS = '20'
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0503.TARGET_HAND_POINT_ID = #handPointId503#
            and tlirl0503.DEL_FLAG = '0')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS =#status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handTypeList">
            HAND_TYPE in
            <iterate property="handTypeList" open="("
                     close=")" conjunction=" , ">
                #handTypeList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointIdNotNull">
            TARGET_HAND_POINT_ID != ''
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointIdNull">
            TARGET_HAND_POINT_ID = ''
        </isNotEmpty>
        union
        SELECT tlirl0401.VEHICLE_NO          AS "vehicleNo",
        tlirl0401.CAR_TRACE_NO        as "carTraceNo",
        tlirl0401.VOUCHER_NUM as "allocateVehicleNo",
        (select TEL_NUM
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
        and tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
        limit 1)                     as "driverTel",
        (select DRIVER_NAME
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
        and tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
        limit 1)                     as "driverName",
        tlirl0401.QUEUE_NUMBER        AS "recCreateTime",
        tlirl0401.PRIORITY_LEVEL as "priorityLevel"
        FROM MELI.tlirl0401 tlirl0401,
        meli.tlirl0301
        WHERE 1 = 1
        and tlirl0401.SEG_NO = tlirl0301.SEG_NO
        and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0401.VOUCHER_NUM = ''
        AND tlirl0401.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="handPointId401">
            tlirl0401.TARGET_HAND_POINT_ID = #handPointId401#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId503">
            exists(select 1 from ${meliSchema}.tlirl0503 tlirl0503,${meliSchema}.tlirl0502 tlirl0502
            where tlirl0502.SEG_NO = tlirl0503.SEG_NO
            and tlirl0502.ALLOCATE_VEHICLE_NO=tlirl0503.ALLOCATE_VEHICLE_NO
            and tlirl0502.DEL_FLAG = 0
            and tlirl0502.STATUS = '20'
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0503.TARGET_HAND_POINT_ID = #handPointId503#
            and tlirl0503.DEL_FLAG = '0')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS =#status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handTypeList">
            HAND_TYPE in
            <iterate property="handTypeList" open="("
                     close=")" conjunction=" , ">
                #handTypeList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointIdNotNull">
            TARGET_HAND_POINT_ID != ''
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointIdNull">
            TARGET_HAND_POINT_ID = ''
        </isNotEmpty>
        union
        SELECT
        tlirl0301.VEHICLE_NO AS "vehicleNo",
        tlirl0301.CAR_TRACE_NO as "carTraceNo",
        (SELECT MAX(tlirl0502.ALLOCATE_VEHICLE_NO)
        FROM MELI.tlirl0502 tlirl0502
        where tlirl0502.SEG_NO = tlirl0301.SEG_NO
        and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0301.DEL_FLAG = '0'
        limit 1) as "allocateVehicleNo",
        tlirl0301.TEL_NUM as "driverTel",
        tlirl0301.DRIVER_NAME as "driverName",
        (select QUEUE_NUMBER
        from meli.tlirl0401 tlirl0401
        where 1 = 1
        and tlirl0401.SEG_NO = tlirl0301.SEG_NO
        and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0401.TARGET_HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        limit 1)              AS "recCreateTime",
        (select PRIORITY_LEVEL
        from meli.tlirl0401 tlirl0401
        where 1 = 1
        and tlirl0401.SEG_NO = tlirl0301.SEG_NO
        and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0401.TARGET_HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        limit 1)              AS "priorityLevel"
        FROM ${meliSchema}.tlirl0301 tlirl0301 WHERE 1=1
        AND tlirl0301.SEG_NO = #segNo#
        and tlirl0301.DEL_FLAG = '0'
        <isNotEmpty prepend=" AND " property="handPointId">
            TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId503">
            exists(select 1 from ${meliSchema}.tlirl0503 tlirl0503,${meliSchema}.tlirl0502 tlirl0502
            where tlirl0502.SEG_NO = tlirl0503.SEG_NO
            and tlirl0502.ALLOCATE_VEHICLE_NO=tlirl0503.ALLOCATE_VEHICLE_NO
            and tlirl0502.DEL_FLAG = 0
            and tlirl0502.STATUS = '20'
            and tlirl0502.SEG_NO = tlirl0301.SEG_NO
            and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            and tlirl0503.TARGET_HAND_POINT_ID = #handPointId503#
            and tlirl0503.DEL_FLAG = '0')
        </isNotEmpty>
        and tlirl0301.TARGET_HAND_POINT_ID = #handPointId401#
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS =#status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handTypeList">
            HAND_TYPE in
            <iterate property="handTypeList" open="("
                     close=")" conjunction=" , ">
                #handTypeList[]#
            </iterate>
        </isNotEmpty>
        AND (STATUS ='20' or STATUS = '40')
        <isNotEmpty prepend=" AND " property="targetHandPointIdNotNull">
            TARGET_HAND_POINT_ID != ''
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointIdNull">
            TARGET_HAND_POINT_ID = ''
        </isNotEmpty>
        and not exists(select 1
        from meli.tlirl0402 tlirl0402
        where 1 = 1
        and tlirl0402.SEG_NO = tlirl0301.SEG_NO
        and tlirl0402.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0402.TARGET_HAND_POINT_ID = #handPointId401#
        limit 1)
        ) A
        ORDER BY A.priorityLevel,A.recCreateTime ASC

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0301 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNoNot">
            CAR_TRACE_NO != #carTraceNoNot#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
    </select>

    <select id="queryCountByHandPoint" resultClass="int">
        select count(1)
        from meli.tlirl0301 tlirl0301
        where STATUS in ('25', '30', '40')
          and tlirl0301.SEG_NO = #segNo#
          and (tlirl0301.TARGET_HAND_POINT_ID = #handPointId# and  tlirl0301.CURRENT_HAND_POINT_ID = #handPointId#)
          and DEL_FLAG = '0'
    </select>

    <select id="queryDuplicateRegistration" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        ID_CARD as "idCard",  <!-- 身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        TEL_NUM as "telNum",  <!-- 手机号 -->
        RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0301
        WHERE 1=1
        AND STATUS &lt; 50
        and STATUS > 00
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notCarTraceNo">
            CAR_TRACE_NO != #notCarTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>

    </select>

    <select id="countDuplicateRegistration" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0301 WHERE 1=1
        AND STATUS &lt; 50
        and STATUS > 00
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notCarTraceNo">
            CAR_TRACE_NO != #notCarTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
    </select>

    <select id="queryCustomerInfo" resultClass="int">
        SELECT COUNT(1) FROM ${meliSchema}.tlirl0313 WHERE 1=1
         and   SEG_NO = #segNo#
         and   STATUS='20'
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
    </select>


<!--   查询下一装卸点-->
    <select id="queryNextTargetPoint" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT distinct tlirl0305.HAND_POINT_ID         as "handPointId",
        (select tlirl0304.HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0305.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0305.HAND_POINT_ID
        and tlirl0304.STATUS = '30') as "handPoinName",
        tlirl0305.HAND_TYPE             as "handType"
        FROM ${meliSchema}.tlirl0301 tlirl0301,
        ${meliSchema}.tlirl0404 tlirl0305
        where 1 = 1
        and tlirl0301.SEG_NO = tlirl0305.SEG_NO
        and tlirl0301.CAR_TRACE_NO = tlirl0305.CAR_TRACE_NO
        and tlirl0301.VEHICLE_NO = tlirl0305.VEHICLE_NO
        and tlirl0305.SEG_NO = #segNo#
        and tlirl0305.CAR_TRACE_NO =#carTraceNo#
        and tlirl0305.VEHICLE_NO = #vehicleNo#
        <isNotEmpty prepend=" AND " property="factoryArea">
            tlirl0301.FACTORY_AREA=#factoryArea#
        </isNotEmpty>
        and tlirl0305.DEL_FLAG='0'
    </select>

    <select id="querySiteName2" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT (SELECT START_OF_TRANSPORT
                FROM ${meliSchema}.tlirl0201 tlirl0201
                WHERE 1 = 1
                  AND tlirl0201.SEG_NO = tlirl0301.SEG_NO
                  AND tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                  and tlirl0201.STATUS = '20'
                   limit 1) as "startOfTransport"
        FROM ${meliSchema}.tlirl0301 tlirl0301
        WHERE 1 = 1
          AND SEG_NO = #segNo#
          AND CAR_TRACE_NO = #carTraceNo#
          AND VEHICLE_NO = #vehicleId#
          AND STATUS != '00'
    </select>


    <select id="queryReservationDate" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select (select tlirl0201.RESERVATION_DATE
                from meli.tlirl0201 tlirl0201
                where 1 = 1
                  and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                  and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                  and tlirl0201.STATUS = '20'
                   limit 1) as "reservationDate",
    (select tlirl0201.RESERVATION_TIME
        from meli.tlirl0201 tlirl0201
        where 1 = 1
          and tlirl0201.SEG_NO = tlirl0301.SEG_NO
          and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
          and tlirl0201.STATUS = '20'
        limit 1) as " reservationTime",
        tlirl0301.CHECK_DATE as "checkDate"
        from meli.tlirl0301 tlirl0301
        where 1 = 1
          and tlirl0301.SEG_NO = #segNo#
          and tlirl0301.CAR_TRACE_NO = #carTraceNo#
          and tlirl0301.STATUS = #status#
            limit 1
    </select>

    <select id="querySiteName3" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
            COUNT(1) AS "count",
            CASE
                WHEN tlirl0301.TARGET_HAND_POINT_ID = ' '
                    THEN tlirl0301.CURRENT_HAND_POINT_ID
                ELSE tlirl0301.TARGET_HAND_POINT_ID
                END AS "targetHandPointId"
        FROM
            MELI.tlirl0301 tlirl0301
                JOIN
            MELI.tlirl0302 tlirl0302
            ON tlirl0301.SEG_NO = tlirl0302.SEG_NO
                AND tlirl0301.CAR_TRACE_NO = tlirl0302.CAR_TRACE_NO
                AND tlirl0301.VEHICLE_NO = tlirl0302.VEHICLE_NO
        WHERE
            tlirl0301.STATUS IN ('10', '20', '30', '40')
          AND (tlirl0301.TARGET_HAND_POINT_ID != ' '
         OR tlirl0301.CURRENT_HAND_POINT_ID != ' ')
          AND tlirl0301.SEG_NO = 'KF000000'
          AND SUBSTR(tlirl0301.REC_CREATE_TIME, 1, 8) &lt;= SUBSTR(REPLACE(NOW(), '-', ''), 1, 8)
        GROUP BY
            CASE
                WHEN tlirl0301.TARGET_HAND_POINT_ID = ' '
                    THEN tlirl0301.CURRENT_HAND_POINT_ID
                ELSE tlirl0301.TARGET_HAND_POINT_ID
                END;
    </select>

    <select id="querySiteName4" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select count(1) as "count", targetHandPointId as "targetHandPointId"
        from (select distinct tlirl0301.VEHICLE_NO as "vehicleNo",
                              (select tlirl0407.CURRENT_HAND_POINT_ID
                               from MELI.tlirl0407 tlirl0407
                               where tlirl0407.SEG_NO = tlirl0301.SEG_NO
                                 and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                 and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                 and tlirl0407.NEXT_TATGET = '20'
                                 AND substr(tlirl0407.REC_CREATE_TIME, 1, 8) = substr(replace(now(), '-', ''), 1, 8)
                                                      limit 1)            as "targetHandPointId"
            from MELI.tlirl0301 tlirl0301
        where 1 = 1
          and tlirl0301.STATUS != '00'
          and tlirl0301.SEG_NO = #segNo#
          and substr(tlirl0301.REC_CREATE_TIME, 1, 8) = substr(replace(now(), '-', ''), 1, 8)
          and exists(
            (select 1
            from MELI.tlirl0407 tlirl0407
            where tlirl0407.SEG_NO = tlirl0301.SEG_NO
          and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
          and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
          and tlirl0407.NEXT_TATGET = '20'
          AND substr(tlirl0407.REC_CREATE_TIME, 1, 8) = substr(replace(now(), '-', ''), 1, 8)
            limit 1))
        union
        select distinct tlirl0301.VEHICLE_NO as "vehicleNo",
                        (select tlirl0407.CURRENT_HAND_POINT_ID
                         from MELI.tlirl0407 tlirl0407
                         where tlirl0407.SEG_NO = tlirl0301.SEG_NO
                           and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                           and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
                           and tlirl0407.NEXT_TATGET = '20'
                           AND substr(tlirl0407.REC_CREATE_TIME, 1, 8) = substr(replace(now(), '-', ''), 1, 8)
                                                limit 1)            as "targetHandPointId"
        from MELI.tlirl0311 tlirl0301
        where 1 = 1
          and tlirl0301.STATUS != '00'
          and tlirl0301.SEG_NO = #segNo#
          and substr(tlirl0301.COMPLETE_UNINSTALL_TIME, 1, 8) = substr(replace(now(), '-', ''), 1, 8)
          and exists(
            (select 1
            from MELI.tlirl0407 tlirl0407
            where tlirl0407.SEG_NO = tlirl0301.SEG_NO
          and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
          and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
          and tlirl0407.NEXT_TATGET = '20'
          AND substr(tlirl0407.REC_CREATE_TIME, 1, 8) = substr(replace(now(), '-', ''), 1, 8)
            limit 1))) A
        group by A.targetHandPointId
    </select>

    <select id="queryUser" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
            xs_user.USER_ID as "userId",
            xs_user.USER_NAME as "userName",
            xs_user.MOBILE as "tel",
            xs_user.SEG_NO as "segNo"
        from iplat4j.xs_user xs_user
        where 1 = 1
          AND xs_user.MOBILE = #tel#
          AND xs_user.SEG_NO = #segNo#
          and  STATUS='1'
    </select>
    <!--  
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="idCard">
            ID_CARD = #idCard#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            TEL_NUM = #telNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumber">
            RESERVATION_NUMBER = #reservationNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkDate">
            CHECK_DATE = #checkDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="enterFactory">
            ENTER_FACTORY = #enterFactory#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="beginEntruckingTime">
            BEGIN_ENTRUCKING_TIME = #beginEntruckingTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="completeUninstallTime">
            COMPLETE_UNINSTALL_TIME = #completeUninstallTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="leaveFactoryDate">
            LEAVE_FACTORY_DATE = #leaveFactoryDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerSigningTime">
            CUSTOMER_SIGNING_TIME = #customerSigningTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointId">
            TARGET_HAND_POINT_ID = #targetHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentHandPointId">
            CURRENT_HAND_POINT_ID = #currentHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unloadLeaveFlag">
            UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0301 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
        STATUS,  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO,  <!-- 车牌号 -->
        ID_CARD,  <!-- 身份证号 -->
        DRIVER_NAME,  <!-- 驾驶员姓名 -->
        TEL_NUM,  <!-- 手机号 -->
        RESERVATION_NUMBER,  <!-- 车辆预约单号 -->
        CHECK_DATE,  <!-- 进厂登记时间 -->
        ENTER_FACTORY,  <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME,  <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME,  <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE,  <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME,  <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID,  <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID,  <!-- 当前装卸点代码 -->
        FACTORY_AREA,  <!-- 厂区 -->
        FACTORY_AREA_NAME,  <!-- 厂区 -->
        UNLOAD_LEAVE_FLAG,  <!-- 未装离厂标记 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID  <!-- 租户ID -->
        <isNotEmpty prepend=" , " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO
        </isNotEmpty>
        )
        VALUES (#segNo#, #unitCode#, #carTraceNo#, #status#, #handType#, #vehicleNo#, #idCard#, #driverName#, #telNum#,
        #reservationNumber#, #checkDate#, #enterFactory#, #beginEntruckingTime#, #completeUninstallTime#,
        #leaveFactoryDate#, #customerSigningTime#, #targetHandPointId#, #currentHandPointId#, #factoryArea#,
        #factoryAreaName#,#unloadLeaveFlag#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#,#allocateVehicleNo#)
    </insert>

    <delete id="delete">
        delete  from ${meliSchema}.tlirl0301
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </delete>

    <delete id="deleteStatus">
        UPDATE ${meliSchema}.tlirl0301
        SET
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = '0'   <!-- 记录删除标记 -->
        <isNotEmpty prepend=" , " property="unloadLeaveFlag">
            UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="targetHandPointId">
            TARGET_HAND_POINT_ID = ' '
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0301
        SET
        SEG_NO = #segNo#,
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        CAR_TRACE_NO = #carTraceNo#,
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE = #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        ID_CARD = #idCard#,   <!-- 身份证号 -->
        DRIVER_NAME = #driverName#,   <!-- 驾驶员姓名 -->
        TEL_NUM = #telNum#,   <!-- 手机号 -->
        RESERVATION_NUMBER = #reservationNumber#,   <!-- 车辆预约单号 -->
        CHECK_DATE = #checkDate#,   <!-- 进厂登记时间 -->
        ENTER_FACTORY = #enterFactory#,   <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME = #beginEntruckingTime#,   <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME = #completeUninstallTime#,   <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE = #leaveFactoryDate#,   <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME = #customerSigningTime#,   <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID = #targetHandPointId#,   <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID = #currentHandPointId#,   <!-- 当前装卸点代码 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区 -->
        UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#,   <!-- 未装离厂标记 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        UUID = #uuid#,   <!-- uuid -->
        TENANT_ID = #tenantId#  <!-- 租户ID -->
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <!--插入叫号表更新目标装卸点-->
    <update id="updateTargetHandPoint">
        UPDATE ${meliSchema}.tlirl0301
        SET
        TARGET_HAND_POINT_ID = #targetHandPointId#,   <!-- 目标装卸点代码 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend=" , " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="callTime">
            CALL_TIME = #callTime#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo# AND
        CAR_TRACE_NO = #carTraceNo#
    </update>

    <!--插入叫号表更新目标装卸点-->
    <update id="updateTargetHandPoint0402">
        UPDATE ${meliSchema}.tlirl0402
        SET
        TARGET_HAND_POINT_ID = #targetHandPointId#,   <!-- 目标装卸点代码 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend=" , " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
        AND VEHICLE_NO	= #vehicleNo#
    </update>

    <update id="updateDriver">
        UPDATE ${meliSchema}.tlirl0301
        SET
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        ID_CARD = #idCard#,   <!-- 身份证号 -->
        DRIVER_NAME = #driverName#,   <!-- 驾驶员姓名 -->
        TEL_NUM = #telNum#,   <!-- 手机号 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        SEG_NO = #segNo#
        AND TEL_NUM = #tel2#
    </update>

    <select id="countByTel" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0301 WHERE 1=1
        AND SEG_NO = #segNo#
        AND TEL_NUM = #tel#
        AND DEL_FLAG = #delFlag#
        AND STATUS > 00
    </select>


    <!--    根据装卸点查询装卸的状态（装卸点是否被占用）-->
    <select id="queryHandPointOccupy" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        RESERVATION_NUMBER as "reservationNumber",
        STATUS as "status"
        FROM ${meliSchema}.tlirl0301 WHERE 1=1
        and TARGET_HAND_POINT_ID=#handPointId#
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            STATUS NOT IN ('50','60')
        </isNotEmpty>
    </select>


    <select id="queryEnterFactoryCarList" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0301">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        a.STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        a.BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        a.COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        a.LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        a.CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        a.TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        (
        select
        HAND_POINT_NAME
        from
        ${meliSchema}.tlirl0304 t
        where
        t.SEG_NO = a.SEG_NO
        and t.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        ) as "targetHandPointIdName",
        a.CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        (
        select
        HAND_POINT_NAME
        from
        ${meliSchema}.tlirl0304 t
        where
        t.SEG_NO = a.SEG_NO
        and t.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        ) as "currentHandPointIdName",
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        a.UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        ifnull((select b.FACTORY_TYPE
        from ${meliSchema}.tlirl0405 b
        where b.SEG_NO = a.SEG_NO
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        ),'10') as "factoryType"
        FROM ${meliSchema}.tlirl0301 a WHERE 1=1
        and ((exists (
        select
        1
        from
        ${meliSchema}.tlirl0402 t
        where
        1 = 1
        and t.SEG_NO = a.SEG_NO
        and t.CAR_TRACE_NO = a.CAR_TRACE_NO
        and t.DEL_FLAG = '0' )
        and STATUS = '10')
        or
        (exists (
        select
        1
        from
        ${meliSchema}.tlirl0405 t
        where
        1 = 1
        and t.SEG_NO = a.SEG_NO
        and t.CAR_TRACE_NO = a.CAR_TRACE_NO
        and t.FACTORY_TYPE = '20'
        and t.DEL_FLAG = '0')
        ))
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        union
        select
        SEG_NO as "segNo",
        UNIT_CODE as "unitCode",
        CAR_TRACE_NO as "carTraceNo",
        STATUS as "status",
        '' as "handType",
        VEHICLE_NO as "vehicleNo",
        '' as "idCard",
        '' as "driverName",
        '' as "telNum",
        '' as "reservationNumber",
        '' as "checkDate",
        '' as "enterFactory",
        '' as "beginEntruckingTime",
        '' as "completeUninstallTime",
        '' as "leaveFactoryDate",
        '' as "customerSigningTime",
        TARGET_HAND_POINT_ID as "targetHandPointId",
        (
        select
        HAND_POINT_NAME
        from
        ${meliSchema}.tlirl0304 t
        where
        t.SEG_NO = a.SEG_NO
        and t.HAND_POINT_ID = TARGET_HAND_POINT_ID ) as "targetHandPointIdName",
        '' as "currentHandPointId",
        '' as "currentHandPointIdName",
        FACTORY_AREA as "factoryArea",
        FACTORY_AREA_NAME as "factoryAreaName",
        '' as "unloadLeaveFlag",
        REC_CREATOR as "recCreator",
        REC_CREATOR_NAME as "recCreatorName",
        REC_CREATE_TIME as "recCreateTime",
        REC_REVISOR as "recRevisor",
        REC_REVISOR_NAME as "recRevisorName",
        REC_REVISE_TIME as "recReviseTime",
        ARCHIVE_FLAG as "archiveFlag",
        DEL_FLAG as "delFlag",
        REMARK as "remark",
        SYS_REMARK as "sysRemark",
        UUID as "uuid",
        TENANT_ID as "tenantId",
        FACTORY_TYPE as "factoryType"
        from ${meliSchema}.tlirl0415 a
        where
        STATUS = '10'
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                segNo asc,
                carTraceNo asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="querySingInCarList" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0301">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        a.STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        a.BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        a.COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        a.LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        a.CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        a.TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        (
        select
        HAND_POINT_NAME
        from
        ${meliSchema}.tlirl0304 t
        where
        t.SEG_NO = a.SEG_NO
        and t.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        ) as "targetHandPointIdName",
        a.CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        (
        select
        HAND_POINT_NAME
        from
        ${meliSchema}.tlirl0304 t
        where
        t.SEG_NO = a.SEG_NO
        and t.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        ) as "currentHandPointIdName",
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        a.UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0301 a WHERE 1=1
        AND NOT EXISTS (
        SELECT
        1
        FROM
        ${meliSchema}.tlirl0402 t
        WHERE 1 = 1
        AND t.SEG_NO = a.SEG_NO
        and t.CAR_TRACE_NO = a.CAR_TRACE_NO
        and t.DEL_FLAG = '0'
        )
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SEG_NO asc,
                CAR_TRACE_NO asc
            </isEmpty>
        </dynamic>

    </select>

    <update id="updateLIRL0301JC">
        UPDATE ${meliSchema}.tlirl0301
        SET
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        ENTER_FACTORY = #enterFactory#,   <!-- 入厂时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend="," property="ifPause">
            IF_PAUSE = #IF_PAUSE#
        </isNotEmpty>
        <isNotEmpty prepend="," property="pauseTime">
            PAUSE_TIME = #pauseTime#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
        AND DEL_FLAG = #delFlag#
    </update>

    <update id="updateLIRL0301CC">
        UPDATE ${meliSchema}.tlirl0301
        SET
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        LEAVE_FACTORY_DATE = #leaveFactoryDate#,   <!-- 出厂时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
        AND DEL_FLAG = #delFlag#
    </update>

    <update id="updateLIRL0301TargetHandPointId">
        UPDATE ${meliSchema}.tlirl0301
        SET
        <isNotEmpty property="targetHandPointId">
            TARGET_HAND_POINT_ID = #targetHandPointId#
        </isNotEmpty>
        <isEmpty property="targetHandPointId">
            TARGET_HAND_POINT_ID = ''
        </isEmpty>
        <isNotEmpty prepend="," property="expectedLoadingTime">
            EXPECTED_LOADING_TIME = #expectedLoadingTime#
        </isNotEmpty>
        <isNotEmpty prepend="," property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
        AND DEL_FLAG = #delFlag#
    </update>

    <update id="updateLIRL0301JSZX">
        UPDATE ${meliSchema}.tlirl0301
        SET
        TARGET_HAND_POINT_ID = #tatgetHandPointId#,
        CURRENT_HAND_POINT_ID = '',
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        COMPLETE_UNINSTALL_TIME = #completeUninstallTime#   <!-- 作业结束时间 -->
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
        AND DEL_FLAG = #delFlag#
    </update>

    <update id="updateLIRL0301JSZX2">
        UPDATE ${meliSchema}.tlirl0301
        SET
        TARGET_HAND_POINT_ID = '',
        CURRENT_HAND_POINT_ID = '',
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        COMPLETE_UNINSTALL_TIME = #completeUninstallTime#   <!-- 作业结束时间 -->
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <update id="revokeLIRL0301ByCarTraceNo">
        UPDATE ${meliSchema}.tlirl0301
        SET
        STATUS = #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#   <!-- 记录删除标记 -->
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <select id="todayLoadingAndUnloadingKanban" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        tlirl0301.VEHICLE_NO as "vehicleNo",
        tlirl0301.DRIVER_NAME as "driverName",
        TEL_NUM as "telNum",
        tlirl0301.HAND_TYPE as "handType",
        ifnull(( select if(BUSINESS_TYPE = '', null,
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = BUSINESS_TYPE
        and tedcm01.CODESET_CODE = 'P010'))
        from ${meliSchema}.tlirl0302 b where 1 = 1
        and b.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO and b.DEL_FLAG = '0' ),
        (select ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = tlirl0301.HAND_TYPE
        and tedcm01.CODESET_CODE = 'P007')) "handTypeName",
        (
        select
        START_OF_TRANSPORT
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20','99')
        limit 1) as "startOfTransport",
        (
        select
        PURPOSE_OF_TRANSPORT
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20','99')
        limit 1) as "purposeOfTransport"
        ,
        if((
        select
        tlirl0302.VOUCHER_NUM
        from ${meliSchema}.tlirl0302 tlirl0302
        where
        tlirl0302.SEG_NO = tlirl0301.SEG_NO
        and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        ) = '',(
        select
        CUSTOMER_NAME
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20', '99')
        limit 1),
        (
        select
        t.CUSTOMER_NAME
        from
        ${meliSchema}.tlirl0307 t
        where
        t.SEG_NO = tlirl0301.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VOUCHER_NUM = (
        select
        tlirl0302.VOUCHER_NUM
        from ${meliSchema}.tlirl0302 tlirl0302
        where
        tlirl0302.SEG_NO = tlirl0301.SEG_NO
        and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        )
        limit 1)
        ) as "customerName",
        (
        select
        ITEM_CNAME
        from
        iplat4j.tedcm01 tedcm01
        where
        tedcm01.ITEM_CODE = tlirl0301.STATUS
        and tedcm01.CODESET_CODE = 'P008') as "statusName",
        tlirl0301.STATUS as "status",
        (
        select
        RESERVATION_TIME
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20','99')
        limit 1) as "reservationTime"
        from
        ${meliSchema}.tlirl0301 tlirl0301
        where
        SEG_NO = #segNo#
        and tlirl0301.STATUS != '00'
        <isNotEmpty prepend=" and " property="reservationDate">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) = replace(#reservationDate#,'-','')
            )
        </isNotEmpty>
        union
        select
        tlirl0311.VEHICLE_NO as "vehicleNo",
        tlirl0311.DRIVER_NAME as "driverName",
        TEL_NUM as "telNum",
        tlirl0311.HAND_TYPE as "handType",
        ifnull(( select if(BUSINESS_TYPE = '', null,
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = BUSINESS_TYPE
        and tedcm01.CODESET_CODE = 'P010')) from ${meliSchema}.tlirl0302 b where 1 = 1
        and b.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO and b.DEL_FLAG = '0' ),
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = HAND_TYPE
        and tedcm01.CODESET_CODE = 'P007')) "handTypeName",
        (
        select
        START_OF_TRANSPORT
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0311.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0311.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20','99')
        limit 1) as "startOfTransport",
        (
        select
        PURPOSE_OF_TRANSPORT
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0311.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0311.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20','99')
        limit 1) as "purposeOfTransport"
        ,
        if((
        select
        tlirl0302.VOUCHER_NUM
        from ${meliSchema}.tlirl0302 tlirl0302
        where
        tlirl0302.SEG_NO = tlirl0311.SEG_NO
        and tlirl0302.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        ) = '',(
        select
        CUSTOMER_NAME
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0311.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0311.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20', '99')
        limit 1),
        (
        select
        t.CUSTOMER_NAME
        from
        ${meliSchema}.tlirl0307 t
        where
        t.SEG_NO = tlirl0311.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VOUCHER_NUM = (
        select
        tlirl0302.VOUCHER_NUM
        from ${meliSchema}.tlirl0302 tlirl0302
        where
        tlirl0302.SEG_NO = tlirl0311.SEG_NO
        and tlirl0302.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        )
        limit 1)
        ) as "customerName",
        (
        select
        ITEM_CNAME
        from
        iplat4j.tedcm01 tedcm01
        where
        tedcm01.ITEM_CODE = tlirl0311.STATUS
        and tedcm01.CODESET_CODE = 'P008') as "statusName",
        tlirl0311.STATUS as "status",
        (
        select
        RESERVATION_TIME
        from
        ${meliSchema}.tlirl0201 tlirl0201
        where
        tlirl0201.SEG_NO = tlirl0311.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = tlirl0311.RESERVATION_NUMBER
        and tlirl0201.STATUS in ('20','99')
        limit 1) as "reservationTime"
        from
        ${meliSchema}.tlirl0311 tlirl0311
        where
        SEG_NO = #segNo#
        <isNotEmpty prepend=" and " property="reservationDate">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = tlirl0311.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) = replace(#reservationDate#,'-','')
            )
        </isNotEmpty>
    </select>

    <select id="queryDriverInfo" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0301">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        ID_CARD as "idCard",  <!-- 身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        TEL_NUM as "telNum",  <!-- 手机号 -->
        RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId"
        FROM ${meliSchema}.tlirl0301 a WHERE 1=1
        AND TARGET_HAND_POINT_ID is null or TARGET_HAND_POINT_ID = ''
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusIn">
            STATUS in ('20','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusIn1">
            STATUS in ('20','30','40')
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>
    <!--插入排队表更新目标装卸点和厂区-->
    <update id="updateTargetHandPointAndFact">
        UPDATE ${meliSchema}.tlirl0301
        SET
        TARGET_HAND_POINT_ID = #targetHandPointId#,   <!-- 目标装卸点代码 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区代码 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        SEG_NO = #segNo# AND
        CAR_TRACE_NO = #carTraceNo#
    </update>

    <select id="qureyVehicleListCC" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
       select
                          t0401.CAR_TRACE_NO as "carTraceNo",
            t0401.VEHICLE_NO                                         as "vehicleNo",
            t0201.TYPE_OF_HANDLING                                   as "businessType",
            (select tedcm01.ITEM_CNAME
             from iplat4j.tedcm01 tedcm01
             where 1 = 1
               and tedcm01.CODESET_CODE = 'P007'
               and tedcm01.ITEM_CODE = t0201.TYPE_OF_HANDLING)       as "businessTypeName",
            t0301.ENTER_FACTORY                                      as "intoFactoryDate",
            ifnull((select tlirl0502.ALLOC_TYPE
                    from meli.tlirl0502 tlirl0502
                    where 1 = 1
                      and tlirl0502.SEG_NO = t0401.SEG_NO
                      and tlirl0502.CAR_TRACE_NO = t0401.CAR_TRACE_NO
                      and tlirl0502.VEHICLE_NO = t0401.VEHICLE_NO
                      and tlirl0502.ALLOCATE_VEHICLE_NO = t0401.VOUCHER_NUM
                      and tlirl0502.STATUS = '20'),' ')                                                            as "allocType",
                          t0301.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
                          t0301.DRIVER_NAME as "driverName",
                          t0301.TEL_NUM as "driverTel"
                               from MELI.tlirl0401 t0401,
             MELI.tlirl0301 t0301,
             MELI.tlirl0201 t0201
        where t0201.SEG_NO = t0301.SEG_NO
          and t0201.RESERVATION_NUMBER = t0301.RESERVATION_NUMBER
          and t0301.RESERVATION_NUMBER != ' '
  and t0401.DEL_FLAG = 0
  and t0301.DEL_FLAG = 0
  and t0401.CAR_TRACE_NO=t0301.CAR_TRACE_NO
  and t0301.STATUS!='00'
  and t0201.TYPE_OF_HANDLING in ( '40','70','80')
        and t0401.SEG_NO=#segNo#
        order by    ENTER_FACTORY  DESC
    </select>

    <select id="qureyVehicleListHC" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select   distinct                        t0401.CAR_TRACE_NO as "carTraceNo",
                                               t0401.VEHICLE_NO                                         as "vehicleNo",
                     t0201.TYPE_OF_HANDLING                                   as "businessType",
                     case
                         when t0201.RESERVATION_NUMBER != ' '
                     then
                             (select tedcm01.ITEM_CNAME
                              from iplat4j.tedcm01 tedcm01
                              where 1 = 1
                                and tedcm01.CODESET_CODE = 'P007'
                                and tedcm01.ITEM_CODE = t0201.TYPE_OF_HANDLING)
                         else
                             case
                                 when (select ALLOC_TYPE
                                       from meli.tlirl0502 tlirl0502
                                       where tlirl0502.SEG_NO = t0301.SEG_NO
                                         and tlirl0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
                                         and tlirl0502.ALLOCATE_VEHICLE_NO = t0301.ALLOCATE_VEHICLE_NO
                                         and t0301.STATUS != '00') = '10'
                             then '钢材卸货'
                         when (select ALLOC_TYPE
                               from meli.tlirl0502 tlirl0502
                               where tlirl0502.SEG_NO = t0301.SEG_NO
                                 and tlirl0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
                                 and tlirl0502.ALLOCATE_VEHICLE_NO = t0301.ALLOCATE_VEHICLE_NO
                                 and t0301.STATUS != '00') = '20' then
                             case
                                 when (select NEXT_ALC_VEHICLE_NO
                                       from meli.tlirl0502 tlirl0502
                                       where tlirl0502.SEG_NO = t0301.SEG_NO
                                         and tlirl0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
                                         and tlirl0502.ALLOCATE_VEHICLE_NO = t0301.ALLOCATE_VEHICLE_NO
                                         and t0301.STATUS != '00') != '' then
                                     '钢材卸货+装货'
                                 else '钢材卸货' end
        end
        end                                             as "businessTypeName",
             t0301.ENTER_FACTORY                                      as "intoFactoryDate",
             ifnull((select tlirl0502.ALLOC_TYPE
              from meli.tlirl0502 tlirl0502
              where 1 = 1
                and tlirl0502.SEG_NO = t0401.SEG_NO
                and tlirl0502.CAR_TRACE_NO = t0401.CAR_TRACE_NO
                and tlirl0502.VEHICLE_NO = t0401.VEHICLE_NO
                and tlirl0502.ALLOCATE_VEHICLE_NO = t0401.VOUCHER_NUM
                and tlirl0502.STATUS = '20'),' ')                                                            as "allocType",
                                                        t0301.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
                          t0301.DRIVER_NAME as "driverName",
                          t0301.TEL_NUM as "driverTel"
      from MELI.tlirl0401 t0401,
           MELI.tlirl0301 t0301,
           MELI.tlirl0201 t0201
      where t0201.SEG_NO = t0301.SEG_NO
        and t0401.SEG_NO='JC000000'
        and t0201.RESERVATION_NUMBER = t0301.RESERVATION_NUMBER
        and t0401.DEL_FLAG = 0
        and t0301.DEL_FLAG = 0
        and t0401.CAR_TRACE_NO = t0301.CAR_TRACE_NO
        and t0301.STATUS != '00'
        and t0201.TYPE_OF_HANDLING in ('10', '20', '30', '60')
   order by  ENTER_FACTORY DESC
    </select>

    <update id="backAllocateVehicleNo">
        UPDATE ${meliSchema}.tlirl0301
        SET
        ALLOCATE_VEHICLE_NO = #allocateVehicleNo#,
        TARGET_HAND_POINT_ID = ' '
        <isNotEmpty prepend="," property="handType">
            HAND_TYPE=#handType#
        </isNotEmpty>
        <isNotEmpty prepend="," property="ifPause">
            IF_PAUSE=#ifPause#
        </isNotEmpty>
        <isNotEmpty prepend="," property="telNum">
            TEL_NUM=#telNum#
        </isNotEmpty>
        <isNotEmpty prepend="," property="driverName">
            DRIVER_NAME=#driverName#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <select id="querySomeOne" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        ID_CARD as "idCard",  <!-- 身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        TEL_NUM as "telNum",  <!-- 手机号 -->
        RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId"
        FROM ${meliSchema}.tlirl0301 a WHERE 1=1
        AND TARGET_HAND_POINT_ID is not null
        and TARGET_HAND_POINT_ID != ''
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        union all
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        ID_CARD as "idCard",  <!-- 身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        TEL_NUM as "telNum",  <!-- 手机号 -->
        RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        ENTER_FACTORY as "enterFactory",  <!-- 入厂时间 -->
        BEGIN_ENTRUCKING_TIME as "beginEntruckingTime",  <!-- 作业开始时间 -->
        COMPLETE_UNINSTALL_TIME as "completeUninstallTime",  <!-- 作业结束时间 -->
        LEAVE_FACTORY_DATE as "leaveFactoryDate",  <!-- 出厂时间 -->
        CUSTOMER_SIGNING_TIME as "customerSigningTime",  <!-- 客户签约时间 -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId"
        FROM ${meliSchema}.tlirl0311 a WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = '50'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
    </select>


    <select id="queryVehicleCountCq" resultClass="String">
        select count(1) as "count"
        from meli.tlirl0301
        where 1 = 1
          and SEG_NO = #segNo#
          and CURRENT_HAND_POINT_ID != ' '
  and STATUS = '30'
    </select>

    <select id="queryPutoutCar" parameterClass="java.util.HashMap" resultClass="com.baosight.imom.li.rl.dao.LIRL0301">
       select * from ( SELECT SEG_NO                                                                                               as "segNo",
               UNIT_CODE                                                                                            as "unitCode",
               CAR_TRACE_NO                                                                                         as "carTraceNo",
               STATUS                                                                                               as "status",
               HAND_TYPE                                                                                            as "handType",
               VEHICLE_NO                                                                                           as "vehicleNo",
               ID_CARD                                                                                              as "idCard",
               DRIVER_NAME                                                                                          as "driverName",
               TEL_NUM                                                                                              as "telNum",
               RESERVATION_NUMBER                                                                                   as "reservationNumber",
               CHECK_DATE                                                                                           as "checkDate",
               ENTER_FACTORY                                                                                        as "enterFactory",
               BEGIN_ENTRUCKING_TIME                                                                                as "beginEntruckingTime",
               COMPLETE_UNINSTALL_TIME                                                                              as "completeUninstallTime",
               LEAVE_FACTORY_DATE                                                                                   as "leaveFactoryDate",
               CUSTOMER_SIGNING_TIME                                                                                as "customerSigningTime",
               TARGET_HAND_POINT_ID                                                                                 as "targetHandPointId",
               CURRENT_HAND_POINT_ID                                                                                as "currentHandPointId",
               ifnull(if(FACTORY_AREA = '', null, FACTORY_AREA), (select b.FACTORY_AREA
                                                                  from MELI.tlirl0302 b
                                                                  where b.SEG_NO = a.SEG_NO
                                                                    and b.CAR_TRACE_NO = a.CAR_TRACE_NO))           as "factoryArea",
               ifnull(if(FACTORY_AREA_NAME = '', null, FACTORY_AREA_NAME), (select b.FACTORY_AREA_NAME
                                                                            from MELI.tlirl0302 b
                                                                            where b.SEG_NO = a.SEG_NO
                                                                              and b.CAR_TRACE_NO = a.CAR_TRACE_NO)) as "factoryAreaName",
               UNLOAD_LEAVE_FLAG                                                                                    as "unloadLeaveFlag",
               REC_CREATOR                                                                                          as "recCreator",
               REC_CREATOR_NAME                                                                                     as "recCreatorName",
               REC_CREATE_TIME                                                                                      as "recCreateTime",
               REC_REVISOR                                                                                          as "recRevisor",
               REC_REVISOR_NAME                                                                                     as "recRevisorName",
               REC_REVISE_TIME                                                                                      as "recReviseTime",
               ARCHIVE_FLAG                                                                                         as "archiveFlag",
               DEL_FLAG                                                                                             as "delFlag",
               REMARK                                                                                               as "remark",
               SYS_REMARK                                                                                           as "sysRemark",
               UUID                                                                                                 as "uuid",
               TENANT_ID                                                                                            as "tenantId",
               (select tlirl0201.START_OF_TRANSPORT
                from meli.tlirl0201 tlirl0201
                where 1 = 1
                  and STATUS = '20'
                  and tlirl0201.SEG_NO = a.SEG_NO
                  and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
                  and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
                                                                                                                       limit 1)                                                                                            as "startOfTransport",
       EXPECTED_LOADING_TIME                                                                                as "expectedLoadingTime",
       (select tlirl0201.TYPE_OF_HANDLING
        from meli.tlirl0201 tlirl0201
        where 1 = 1
          and STATUS = '20'
          and tlirl0201.SEG_NO = a.SEG_NO
          and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
          and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1)                                                                                            as "typeOfHandling",
       a.ALLOCATE_VEHICLE_NO                                                                                as "allocateVehicleNo"
        FROM MELI.tlirl0301 a
        WHERE 1 = 1
          AND SEG_NO = #segNo#
          AND STATUS in ('40','50')
        <isNotEmpty prepend="and " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend="and " property="vehicleNoY">
            VEHICLE_NO like concat('%', #vehicleNoY#,'%')
        </isNotEmpty>
          AND STATUS in ('40','50')
          AND DEL_FLAG = '0'
       union
        select SEG_NO           as "segNo",
        UNIT_CODE        as "unitCode",
        (select CAR_TRACE_NO
        from meli.tlirl0301
        where 1 = 1
        and tlirl0301.STATUS = a.STATUS
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0301.HAND_TYPE = ' '
        and tlirl0301.STATUS = '20'
        limit 1)              as "carTraceNo",
        '40'           as "status",
        ' '              as "handType",
        VEHICLE_NO       as "vehicleNo",
        ' '              as "idCard",
        ' '              as "driverName",
        ' '              as "telNum",
        ' '              as "reservationNumber",
        ' '              as "checkDate",
        ' '              as "enterFactory",
        ' '              as "beginEntruckingTime",
        ' '              as "completeUninstallTime",
        ' '              as "leaveFactoryDate",
        ' '              as "customerSigningTime",
        ' '              as "targetHandPointId",
        ' '              as "currentHandPointId",
        ' '              as "factoryArea",
        ' '              as "factoryAreaName",
        ' '              as "unloadLeaveFlag",
        REC_CREATOR      as "recCreator",
        REC_CREATOR_NAME as "recCreatorName",
        REC_CREATE_TIME  as "recCreateTime",
        REC_REVISOR      as "recRevisor",
        REC_REVISOR_NAME as "recRevisorName",
        REC_REVISE_TIME  as "recReviseTime",
        ARCHIVE_FLAG     as "archiveFlag",
        DEL_FLAG         as "delFlag",
        REMARK           as "remark",
        ' '              as "sysRemark",
        UUID             as "uuid",
        TENANT_ID        as "tenantId",
        ' '              as "startOfTransport",
        ' '              as "expectedLoadingTime",
        ' '              as "typeOfHandling",
        ' '              as "allocateVxehicleNo"
        FROM MELI.tlirl0501 a
        WHERE 1 = 1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO like concat('%', #vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend="and " property="vehicleNoY">
            VEHICLE_NO like concat('%', #vehicleNoY#,'%')
        </isNotEmpty>
        AND STATUS in ('20')
        and exists(select 1
        from meli.tlirl0301
        where 1 = 1
        and tlirl0301.STATUS = a.STATUS
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0301.HAND_TYPE = ' '
        and tlirl0301.STATUS in('20','40')
        limit 1)
        and not exists(select 1
        from meli.tlirl0502
        where 1 = 1
        and tlirl0502.STATUS = a.STATUS
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0502.STATUS = '20'
        limit 1)
        union
        SELECT SEG_NO                                                                                               as "segNo",
        UNIT_CODE                                                                                            as "unitCode",
        CAR_TRACE_NO                                                                                         as "carTraceNo",
        STATUS                                                                                               as "status",
        HAND_TYPE                                                                                            as "handType",
        VEHICLE_NO                                                                                           as "vehicleNo",
        ID_CARD                                                                                              as "idCard",
        DRIVER_NAME                                                                                          as "driverName",
        TEL_NUM                                                                                              as "telNum",
        RESERVATION_NUMBER                                                                                   as "reservationNumber",
        CHECK_DATE                                                                                           as "checkDate",
        ENTER_FACTORY                                                                                        as "enterFactory",
        BEGIN_ENTRUCKING_TIME                                                                                as "beginEntruckingTime",
        COMPLETE_UNINSTALL_TIME                                                                              as "completeUninstallTime",
        LEAVE_FACTORY_DATE                                                                                   as "leaveFactoryDate",
        CUSTOMER_SIGNING_TIME                                                                                as "customerSigningTime",
        TARGET_HAND_POINT_ID                                                                                 as "targetHandPointId",
        CURRENT_HAND_POINT_ID                                                                                as "currentHandPointId",
        ifnull(if(FACTORY_AREA = '', null, FACTORY_AREA), (select b.FACTORY_AREA
        from MELI.tlirl0302 b
        where b.SEG_NO = a.SEG_NO
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO))           as "factoryArea",
        ifnull(if(FACTORY_AREA_NAME = '', null, FACTORY_AREA_NAME), (select b.FACTORY_AREA_NAME
        from MELI.tlirl0302 b
        where b.SEG_NO = a.SEG_NO
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO)) as "factoryAreaName",
        UNLOAD_LEAVE_FLAG                                                                                    as "unloadLeaveFlag",
        REC_CREATOR                                                                                          as "recCreator",
        REC_CREATOR_NAME                                                                                     as "recCreatorName",
        REC_CREATE_TIME                                                                                      as "recCreateTime",
        REC_REVISOR                                                                                          as "recRevisor",
        REC_REVISOR_NAME                                                                                     as "recRevisorName",
        REC_REVISE_TIME                                                                                      as "recReviseTime",
        ARCHIVE_FLAG                                                                                         as "archiveFlag",
        DEL_FLAG                                                                                             as "delFlag",
        REMARK                                                                                               as "remark",
        SYS_REMARK                                                                                           as "sysRemark",
        UUID                                                                                                 as "uuid",
        TENANT_ID                                                                                            as "tenantId",
        (select tlirl0201.START_OF_TRANSPORT
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and STATUS = '20'
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1)                                                                                            as "startOfTransport",
        EXPECTED_LOADING_TIME                                                                                as "expectedLoadingTime",
        (select tlirl0201.TYPE_OF_HANDLING
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and STATUS = '20'
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1)                                                                                            as "typeOfHandling",
        a.ALLOCATE_VEHICLE_NO                                                                                as "allocateVehicleNo"
        FROM MELI.tlirl0301 a
        WHERE 1 = 1
        AND SEG_NO = #segNo#
        AND STATUS = '50'
        and UNLOAD_LEAVE_FLAG = '10'
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO like concat('%', #vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend="and " property="vehicleNoY">
            VEHICLE_NO like concat('%', #vehicleNoY#,'%')
        </isNotEmpty>
        union
        SELECT SEG_NO                                                                                               as "segNo",
        UNIT_CODE                                                                                            as "unitCode",
        CAR_TRACE_NO                                                                                         as "carTraceNo",
        STATUS                                                                                               as "status",
        HAND_TYPE                                                                                            as "handType",
        VEHICLE_NO                                                                                           as "vehicleNo",
        ID_CARD                                                                                              as "idCard",
        DRIVER_NAME                                                                                          as "driverName",
        TEL_NUM                                                                                              as "telNum",
        RESERVATION_NUMBER                                                                                   as "reservationNumber",
        CHECK_DATE                                                                                           as "checkDate",
        ENTER_FACTORY                                                                                        as "enterFactory",
        BEGIN_ENTRUCKING_TIME                                                                                as "beginEntruckingTime",
        COMPLETE_UNINSTALL_TIME                                                                              as "completeUninstallTime",
        LEAVE_FACTORY_DATE                                                                                   as "leaveFactoryDate",
        CUSTOMER_SIGNING_TIME                                                                                as "customerSigningTime",
        TARGET_HAND_POINT_ID                                                                                 as "targetHandPointId",
        CURRENT_HAND_POINT_ID                                                                                as "currentHandPointId",
        ifnull(if(FACTORY_AREA = '', null, FACTORY_AREA), (select b.FACTORY_AREA
        from MELI.tlirl0302 b
        where b.SEG_NO = a.SEG_NO
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO))           as "factoryArea",
        ifnull(if(FACTORY_AREA_NAME = '', null, FACTORY_AREA_NAME), (select b.FACTORY_AREA_NAME
        from MELI.tlirl0302 b
        where b.SEG_NO = a.SEG_NO
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO)) as "factoryAreaName",
        UNLOAD_LEAVE_FLAG                                                                                    as "unloadLeaveFlag",
        REC_CREATOR                                                                                          as "recCreator",
        REC_CREATOR_NAME                                                                                     as "recCreatorName",
        REC_CREATE_TIME                                                                                      as "recCreateTime",
        REC_REVISOR                                                                                          as "recRevisor",
        REC_REVISOR_NAME                                                                                     as "recRevisorName",
        REC_REVISE_TIME                                                                                      as "recReviseTime",
        ARCHIVE_FLAG                                                                                         as "archiveFlag",
        DEL_FLAG                                                                                             as "delFlag",
        REMARK                                                                                               as "remark",
        SYS_REMARK                                                                                           as "sysRemark",
        UUID                                                                                                 as "uuid",
        TENANT_ID                                                                                            as "tenantId",
        (select tlirl0201.START_OF_TRANSPORT
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and STATUS = '20'
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1)                                                                                            as "startOfTransport",
        EXPECTED_LOADING_TIME                                                                                as "expectedLoadingTime",
        (select tlirl0201.TYPE_OF_HANDLING
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and STATUS = '20'
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1)                                                                                            as "typeOfHandling",
        a.ALLOCATE_VEHICLE_NO                                                                                as "allocateVehicleNo"
        FROM MELI.tlirl0301 a
        WHERE 1 = 1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO like concat('%', #vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend="and " property="vehicleNoY">
            VEHICLE_NO like concat('%', #vehicleNoY#,'%')
        </isNotEmpty>
        AND STATUS = '20'
        and TARGET_HAND_POINT_ID =''
        )A
        order by recCreateTime desc
    </select>

    <select id="queryLoadingCar" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select distinct tlirl0301.CAR_TRACE_NO as "carTraceNo", tlirl0301.VEHICLE_NO as "vehicleNo", tlirl0502.ALLOCATE_VEHICLE_NO as "allocateVehicleNo"
        from meli.tlirl0301 tlirl0301
                 left join meli.tlirl0502 tlirl0502
                           on tlirl0502.SEG_NO = tlirl0301.SEG_NO
                               and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                               and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
        where 1 = 1
          and tlirl0301.SEG_NO = #segNo#
          AND tlirl0301.STATUS = '40'
          and tlirl0502.ALLOC_TYPE = '20'
          and not exists(select 1
                         from meli.tlirl0503 tlirl0503
                         where 1 = 1
                           and tlirl0503.SEG_NO = tlirl0502.SEG_NO
                           and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO
                           and tlirl0503.STATUS in ('20', '00','99'))
    </select>

    <update id="updateFactory">
        update meli.tlirl0301
        set FACTORY_AREA = #factoryArea#
            where 1 = 1
                and SEG_NO = #segNo#
                and CAR_TRACE_NO = #carTraceNo#
    </update>


    <update id="updateAllocateVehicleNo">
        update meli.tlirl0301
        set ALLOCATE_VEHICLE_NO = ' '
            where 1 = 1
                and SEG_NO = #segNo#
                and CAR_TRACE_NO = #carTraceNo#
                and ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
    </update>

    <select id="queryCallNo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT CAR_TRACE_NO as "carTraceNo",
               SEG_NO as "segNo",
               VEHICLE_NO as "vehicleNo",
               TARGET_HAND_POINT_ID as "targetHandPointId",
               ALLOCATE_VEHICLE_NO as "allocateVehicleNo"
        FROM meli.tlirl0301 tlirl0301
        WHERE 1 = 1
          AND tlirl0301.SEG_NO = #segNo#
          AND tlirl0301.STATUS IN ('20', '40')
  AND EXISTS (SELECT 1
              FROM meli.tlirl0402 tlirl0402
              WHERE 1 = 1
                AND tlirl0402.SEG_NO = tlirl0301.SEG_NO
                AND tlirl0402.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO)
  AND TIMESTAMPDIFF(MINUTE, STR_TO_DATE(tlirl0301.CALL_TIME, '%Y%m%d%H%i%s'), NOW()) >= #timM#
    </select>


    <update id="backAll">
        UPDATE ${meliSchema}.tlirl0301
        SET
        TARGET_HAND_POINT_ID = '',
        CURRENT_HAND_POINT_ID=''
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <select id="queryWasteVehicleNo" resultClass="String">
        select
               VEHICLE_NO             as "vehicleNo"
        from meli.tlirl0301
        where 1 = 1
          and SEG_NO = #segNo#
          AND TEL_NUM = #tel#
          and RESERVATION_NUMBER = (select tlirl0201.RESERVATION_NUMBER
                                    from meli.tlirl0201
                                    where 1 = 1
                                      and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                      and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                                      and tlirl0201.STATUS = '20'
                                      and tlirl0201.TYPE_OF_HANDLING in( '10','60')
            limit 1)
        union
        select
               tlirl0501.VEHICLE_NO   as "vehicleNo"
        from meli.tlirl0501 tlirl0501,
             meli.tlirl0301 tlirl0301
        where 1 = 1
          and tlirl0501.SEG_NO = tlirl0301.SEG_NO
          and tlirl0501.VEHICLE_NO = tlirl0301.VEHICLE_NO
          and tlirl0501.VEHICLE_TYPE = '1'
          and tlirl0501.STATUS = '20'
          and tlirl0301.STATUS != '00'
  and tlirl0501.SEG_NO=#segNo#
          and exists(select 1
            from meli.tlirl0102 tlirl0102
            where 1 = 1
          and tlirl0102.UUID = tlirl0501.DR_UUID
          and tlirl0102.TEL = #tel#
            limit 1)
    </select>


    <update id="updateLIRL0301Pause">
        UPDATE ${meliSchema}.tlirl0301
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend="," property="ifPause">
            IF_PAUSE = #ifPause#
        </isNotEmpty>
        <isNotEmpty prepend="," property="pauseTime">
            PAUSE_TIME = #pauseTime#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <select id="queryHandPointExzist" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select VEHICLE_NO
        from meli.tlirl0301
        where 1 = 1
          and SEG_NO = 'JC000000'
          AND CURRENT_HAND_POINT_ID = #handPointId#
          and CAR_TRACE_NO != #carTraceNo#
          AND STATUS = '30'
        UNION
        select VEHICLE_NO
        from meli.tlirl0402
        where 1 = 1
          and SEG_NO = 'JC000000'
          AND TARGET_HAND_POINT_ID = #handPointId#
          and CAR_TRACE_NO != #carTraceNo#
    </select>

    <select id="querySpotVehicle" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select CAR_TRACE_NO as "carTraceNo", VEHICLE_NO as "vehicleNo", DRIVER_NAME as "driverName", TEL_NUM as "driverTel",ID_CARD as "idCard"
        from meli.tlirl0301
        where 1 = 1
          and SEG_NO = #segNo#
          and STATUS != '00'
        and TEL_NUM=#driverTel#
    </select>

    <update id="updateTelByCar">
        UPDATE ${meliSchema}.tlirl0301
        SET
        TEL_NUM = #telNum#,
        DRIVER_NAME = #driverName#
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>
</sqlMap>