package com.baosight.imom.common.websocket;

import com.alibaba.fastjson.JSONObject;
import com.baosight.imom.common.utils.LogUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实时报警客户端
 *
 * <AUTHOR> 郁在杰
 * @Description :BA实时报警客户端
 * @Date : 2024/9/19
 * @Version : 1.0
 */
public class TagAlarmClient extends WebSocketClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(TagAlarmClient.class);
    /**
     * BA实时报警WebSocket地址配置后缀，实际配置为segNo.RealTimeAlarmsEndpoint
     */
    private static final String CONFIG_STR = ".RealTimeAlarmsEndpoint";

    /**
     * 缓存各业务单元下BA实时报警WebSocket地址
     */
    private static final Map<String, String> URI_MAP = new ConcurrentHashMap<>();

    private TagAlarmClient(URI serverUri) {
        super(serverUri);
    }

    /**
     * 获取BA实时报警WebSocket地址
     *
     * @param segNoPrefix 业务单元前缀
     * @return BA实时报警WebSocket地址
     */
    private static String getUri(String segNoPrefix) {
        // 先从缓存中获取
        String uri = URI_MAP.get(segNoPrefix);
        if (!StrUtil.isBlank(uri)) {
            return uri;
        }
        // 从配置文件中获取
        uri = PlatApplicationContext.getProperty(segNoPrefix + CONFIG_STR);
        if (StrUtil.isBlank(uri)) {
            throw new PlatException(String.format(
                    "BA实时报警WebSocket地址未配置，请检查配置项：%s",
                    segNoPrefix + CONFIG_STR
            ));
        }
        // 添加至缓存
        URI_MAP.put(segNoPrefix, uri);
        return uri;
    }

    /**
     * 创建BA实时报警WebSocket客户端
     *
     * @param segNoPrefix 业务单元前缀
     * @return BA实时报警WebSocket客户端
     */
    public static TagAlarmClient newClient(String segNoPrefix) throws URISyntaxException {
        return new TagAlarmClient(new URI(getUri(segNoPrefix)));
    }

    /**
     * 连接成功回调
     *
     * @param handshakedata 握手数据
     */
    @Override
    public void onOpen(ServerHandshake handshakedata) {
        LOGGER.info("TagAlarmClient连接开启");
        LogUtils.log("TagAlarmClient连接开启");
        // 查询前20条的报警信息
        this.send("Q {\"filter_str\":\"{\\\"enableFilterArea\\\":0,\\\"enableFilterStatus\\\":0,\\\"enableFilterTime\\\":0,\\\"enableFilterPriority\\\":0,\\\"enableFilterTag\\\":0,\\\"enableFilterType\\\":0,\\\"enableFilterAgent\\\":0,\\\"enableFilterDesc\\\":0,\\\"enableFilterSubsysname\\\":0,\\\"enableFilterGroup\\\":0,\\\"enableAutoTimeFilter\\\":0,\\\"enableFilterAlarmGroupName\\\":0,\\\"filterAreaSetting\\\":[],\\\"filterStatusSetting\\\":\\\"\\\",\\\"filterTimeSetting\\\":\\\"\\\",\\\"filterPrioritySetting\\\":\\\"\\\",\\\"filterTagSetting\\\":\\\"\\\",\\\"filterTypeSetting\\\":\\\"\\\",\\\"filterAgentSetting\\\":\\\"\\\",\\\"filterDescSetting\\\":\\\"\\\",\\\"filterSubsysnameSetting\\\":\\\"\\\",\\\"filterGroupSetting\\\":\\\"\\\",\\\"filterAlarmGroupNameSetting\\\":\\\"\\\"}\",\"sidx\":\"\",\"sord\":\"asc\",\"page\":1,\"rows\":20}");
    }

    /**
     * 接收消息回调
     *
     * @param message 消息
     */
    @Override
    public void onMessage(String message) {
        try {
            LOGGER.log("收到消息："+message);
            LogUtils.log("TagAlarmClient收到消息："+message);
            EiInfo info = new EiInfo();
            info.set(EiConstant.serviceName, "VGDM06");
            info.set(EiConstant.methodName, "receiveWsData");
            // 数据以"Q "开头，需截取
            String data = message.substring(2);
            JSONObject jsonObject = JSONObject.parseObject(data);
            info.set("data", jsonObject);
            EiInfo outInfo = XLocalManager.call(info);
            if (outInfo.getStatus() < 0) {
                LOGGER.error("TagAlarmClient数据接收出错:{}", outInfo.getMsg());
                LogUtils.error("TagAlarmClient数据接收出错1", outInfo.getMsg());
            }
        } catch (Exception ex) {
            LOGGER.error("TagAlarmClient数据接收出错:{}", ex.getMessage());
            LogUtils.error("TagAlarmClient数据接收出错2", ex.getMessage());
        }
    }

    /**
     * 连接关闭回调
     *
     * @param code   关闭代码
     * @param reason 关闭原因
     * @param remote 是否远程关闭
     */
    @Override
    public void onClose(int code, String reason, boolean remote) {
        LOGGER.warn("WebSocket closed: {" + code + "}reason:{" + reason + "}");
        // 可以尝试重连逻辑
        LogUtils.log("TagAlarmClient closed: {" + code + "}reason:{" + reason + "}");
    }

    /**
     * 错误回调
     *
     * @param ex 异常
     */
    @Override
    public void onError(Exception ex) {
        LOGGER.error("WebSocket error", ex);
        // 错误处理
        LogUtils.error("TagAlarmClient连接出错",ex.getMessage());
    }
}