<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="清单信息" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-checkPlanId" cname="主项号" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFDatePicker ename="inqu_status-0-checkPlanDate" cname="点检日期" colWidth="3"
                                     format="yyyyMMdd">
                    </EF:EFDatePicker>

                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                                     ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

                    <EF:EFSelect ename="inqu_status-0-spotCheckNature" cname="点检性质" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P050"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="inqu_status-0-checkPlanStatus" cname="点检状态" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P018"/>
                    </EF:EFSelect>

                </div>
                <div class="row">
                    <EF:EFInput ename="inqu-0-checkPlanId" type="hidden"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="主项清单">
                <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="checkPlanId" cname="主项号" width="130" align="center"/>
                    <EF:EFComboColumn ename="checkPlanStatus" cname="主项状态" align="center">
                        <EF:EFCodeOption codeName="P018"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="checkPlanDate" cname="点检日期" align="center"/>
                    <EF:EFComboColumn ename="spotCheckNature" cname="点检性质" align="center">
                        <EF:EFCodeOption codeName="P050"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="点检计划" id="info-2">
            <EF:EFRegion id="detail" title="主项信息">
                <EF:EFInput ename="detail_status-0-uuid" cname="UUID" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="unitInfo01" center="true"
                                     ename="detail_status-0-unitCode" cname="业务单元代码" colWidth="3"/>
                    <EF:EFSelect readonly="true" ename="detail_status-0-segNo" cname="业务单元简称" colWidth="3">
                        <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="equipmentInfo" center="true"
                                     ename="detail_status-0-equipmentName" cname="设备名称" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-checkPlanId" cname="主项号"
                                colWidth="3" readonly="true"/>
                    <EF:EFDatePicker required="true" ename="detail_status-0-checkPlanDate" cname="点检日期"
                                     colWidth="3" format="yyyyMMdd"/>
                    <EF:EFSelect required="true" ename="detail_status-0-spotCheckNature" cname="点检性质" colWidth="3">
                        <EF:EFCodeOption codeName="P050"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="detail_status-0-checkPlanStatus" cname="状态" colWidth="3" enable="false"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                        <EF:EFOption value="" label=""/>
                        <EF:EFCodeOption codeName="P018"/>
                    </EF:EFSelect>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="子项清单">
                <EF:EFGrid blockId="result2" autoDraw="no"
                           queryMethod="querySubByMain" sort="all">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="业务单元简称" hidden="true"/>
                    <EF:EFColumn ename="checkPlanSubId" cname="点检计划号" enable="false" width="150" align="center"/>
                    <EF:EFComboColumn ename="checkPlanSubStatus" cname="状态" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P019"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
                    <EF:EFColumn ename="deviceName" required="true" cname="分部设备名称"/>
                    <EF:EFComboColumn ename="deviceCheckStatus" required="true" cname="设备状态" align="center"
                                      width="90">
                        <EF:EFCodeOption codeName="P047"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="spotCheckContent" required="true" cname="点检内容" width="200"/>
                    <EF:EFComboColumn ename="spotCheckMethod" required="true" cname="点检方法" align="center"
                                      width="110">
                        <EF:EFCodeOption codeName="P049"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="spotCheckImplemente" cname="实施方" align="center"
                                      width="70" enable="false">
                        <EF:EFCodeOption codeName="P048"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="isPublish" required="true" cname="是否挂牌" align="center" width="80">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="spotCheckStandardType" required="true" cname="标准类型" align="center"
                                      width="80">
                        <EF:EFCodeOption codeName="P051"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="judgmentStandard" required="true" cname="判断标准" width="200"/>
                    <EF:EFComboColumn ename="isPicture" required="true" cname="是否上传照片" align="center" width="110">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="measureId" cname="计量单位"/>
                    <EF:EFColumn ename="upperLimit" cname="上限值" align="right"/>
                    <EF:EFColumn ename="lowerLimit" cname="下限值" align="right"/>
                    <EF:EFComboColumn ename="planSource" cname="计划来源" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P052"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="spotCheckStandardId" cname="点检标准编号" width="120" align="center"
                                 enable="false"/>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow id="plan" width="90%" height="15%">
        <EF:EFRegion id="inqu3" title="刷新点检计划">
            <EF:EFInput ename="inqu3_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                             containerId="equipmentInfo2" center="true"
                             ename="inqu3_status-0-equipmentName" cname="设备名称" colWidth="3"/>
            <EF:EFDatePicker ename="inqu3_status-0-workingDate" cname="计划月份" required="true"
                             role="date" format="yyyyMM" parseFormats="['yyyyMM','yyyyMM']"
                             readonly="true" colWidth="3"/>
            <EF:EFButton ename="planSynchronization" cname="点检计划同步"/>
        </EF:EFRegion>
    </EF:EFWindow>
    <EF:EFWindow id="planDate" width="90%" height="20%">
        <EF:EFRegion id="inqu4" title="点检计划延期">
            <div class="row">
                <EF:EFDatePicker ename="inqu4_status-0-checkPlanDate" cname="点检日期" colWidth="3"
                                 format="yyyyMMdd" required="true"/>
                <EF:EFButton ename="planDelayDate" cname="延期"/>
            </div>
            <div class="row">
                <EF:EFInput required="true" ename="inqu4_status-0-delayRemark" cname="延期理由"
                            colWidth="12" ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
            </div>
        </EF:EFRegion>
    </EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo2" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>