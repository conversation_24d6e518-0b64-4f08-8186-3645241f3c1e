package com.baosight.imom.vg.dm.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;

/**
 * 设备管理模块PDA相关接口
 *
 * <AUTHOR> 郁在杰
 * @Description :pda相关接口
 * @Date : 2024/12/9
 * @Version : 1.0
 */
public class ServiceVGDMInterfacePda extends ServiceBase {

    /**
     * 通用检查token方法，所有接口必须先调用此方法
     */
    private EiInfo checkToken(EiInfo inInfo) {
        String segNo = inInfo.getString("segNo");
        if (StrUtil.isBlank(segNo)) {
            throw new PlatException("业务单元代码为空");
        }
        // 调用物流模块token校验方法
        inInfo.set(EiConstant.serviceName, "LIRLInterfacePda");
        inInfo.set(EiConstant.methodName, "validToken");
        EiInfo tempInfo = XLocalManager.call(inInfo);
        if (tempInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            throw new PlatException(tempInfo.getMsg());
        }
        // 清除参数
        inInfo.set(EiConstant.serviceName, "");
        inInfo.set(EiConstant.methodName, "");
        return inInfo;
    }

    /**
     * 查询今天待点检设备信息
     * <p>
     * serviceId:S_VG_DM_0009
     */
    public EiInfo queryEquipment(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("账套为空");
            }
            String spotCheckNature = inInfo.getString("spotCheckNature");
            String factoryBuilding = inInfo.getString("factoryBuilding");
            log("查询设备参数：" + segNo + "|" + spotCheckNature);
            // 点检日期时获取当前日期
            String checkPlanDate = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            log("点检日期：" + checkPlanDate);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("checkPlanDate", checkPlanDate);
            queryMap.put("spotCheckNature", spotCheckNature);
            //增加厂房查询条件 2025-07-07 Shinn
            queryMap.put("factoryBuilding", factoryBuilding);
            List<Map> list = dao.query(VGDM0401.QUERY_TODAY_EQUIPMENT, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询今天待点检分部设备信息
     * <p>
     * serviceId:S_VG_DM_0010
     */
    public EiInfo queryDevice(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("账套或设备编码为空");
            }
            String spotCheckNature = inInfo.getString("spotCheckNature");
            log("查询设备参数：" + segNo + "|" + eArchivesNo + "|" + spotCheckNature);
            // 点检日期时获取当前日期
            String checkPlanDate = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            log("点检日期：" + checkPlanDate);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            queryMap.put("checkPlanDate", checkPlanDate);
            queryMap.put("spotCheckNature", spotCheckNature);
            List<Map> list = dao.query(VGDM0402.QUERY_TODAY_DEVICE, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询待点检信息
     * <p>
     * serviceId:S_VG_DM_0001
     */
    public EiInfo queryWaitSpotCheck(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String deviceCode = inInfo.getString("deviceCode");
            String spotCheckNature = inInfo.getString("spotCheckNature");
            if (StrUtil.isBlank(deviceCode) || StrUtil.isBlank(spotCheckNature)) {
                throw new PlatException("分部设备代码或点检性质为空");
            }
            log("查询待点检参数：" + segNo + "|" + deviceCode + "|" + spotCheckNature);
            // 点检日期时获取当前日期
            String checkPlanDate = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            log("点检日期：" + checkPlanDate);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("deviceCode", deviceCode);
            queryMap.put("spotCheckNature", spotCheckNature);
            queryMap.put("checkPlanDate", checkPlanDate);
            // 主项状态标记-确认或启动状态
            queryMap.put("activeFlag", segNo);
            // 子项状态-未点检
            queryMap.put("checkPlanSubStatus", MesConstant.Status.K10);
            List<Map> list = dao.query(VGDM0402.QUERY_WITH_MAIN, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 更新点检实绩
     * serviceId:S_VG_DM_0002
     */
    public EiInfo updateSpotCheckActual(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取传入参数
            String uuid = inInfo.getString("uuid");
            String isNormal = inInfo.getString("isNormal");
            String actualsRemark = inInfo.getString("actualsRemark");
            String actualsRevisor = inInfo.getString("userId");
            String actualsRevisorName = inInfo.getString("userName");
            // 校验传入参数
            if (StrUtil.isBlank(uuid) || StrUtil.isBlank(isNormal) || StrUtil.isBlank(actualsRemark) || StrUtil.isBlank(actualsRevisor) || StrUtil.isBlank(actualsRevisorName)) {
                throw new PlatException("参数不能为空");
            }
            log("更新点检实绩参数：" + uuid + "|" + isNormal + "|" + actualsRemark + "|" + actualsRevisor + "|" + actualsRevisorName);
            // 校验数据
            VGDM0402 vgdm0402 = new VGDM0402();
            vgdm0402.setUuid(uuid);
            VGDM0402 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0402, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NOT_CHECK_STATUS, MesConstant.Status.K10);
            // 更新数据
            dbData.setIsNormal(isNormal);
            dbData.setActualsRemark(actualsRemark);
            dbData.setActualsRevisor(actualsRevisor);
            dbData.setActualsRevisorName(actualsRevisorName);
            dbData.setActualsTime(DateUtil.curDateTimeStr14());
            dbData.setRecRevisor(actualsRevisor);
            dbData.setRecRevisorName(actualsRevisorName);
            dbData.setRecReviseTime(dbData.getActualsTime());
            // 附件信息
            List<String> fileList = (List<String>) inInfo.get("fileList");
            if ("1".equals(dbData.getIsPicture()) && CollectionUtils.isEmpty(fileList)) {
                throw new PlatException("附件信息不能为空");
            }
            // 构建eiInfo
            EiInfo eiInfo = new EiInfo();
            eiInfo.addBlock(EiConstant.resultBlock).addRow(dbData);
            // 调用更新方法更新实绩信息
            eiInfo.set(EiConstant.serviceName, "VGDM0402");
            eiInfo.set(EiConstant.methodName, "pdaUpdate");
            eiInfo = XLocalManager.call(eiInfo);
            if (eiInfo.getStatus() != EiConstant.STATUS_DEFAULT) {
                throw new PlatException(eiInfo.getMsg());
            }
            // 上传附件
            uploadFile(dbData, fileList);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 上传附件
     *
     * @param vgdm0402 点检数据
     * @param fileList 附件列表
     */
    private void uploadFile(VGDM0402 vgdm0402, List<String> fileList) throws Exception {
        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }
        Map paramMap = vgdm0402.toMap();
        paramMap.put("fileType", "VGDM0402");
        paramMap.put("id", vgdm0402.getCheckPlanSubId());
        uploadFile(paramMap, fileList);
    }

    /**
     * 上传附件
     *
     * @param vgdm0801 检修数据
     * @param fileList 附件列表
     */
    private void uploadFile(VGDM0801 vgdm0801, List<String> fileList) throws Exception {
        Map paramMap = vgdm0801.toMap();
        paramMap.put("fileType", "VGDM0801");
        paramMap.put("id", vgdm0801.getOverhaulPlanId());
        uploadFile(paramMap, fileList);
    }

    /**
     * 上传附件
     *
     * @param vgdm0801 检修数据
     * @param fileList 附件列表
     */
    private void uploadFile2(VGDM0801 vgdm0801, List<String> fileList) throws Exception {
        Map paramMap = vgdm0801.toMap();
        paramMap.put("fileType", "VGDM08H");
        paramMap.put("id", vgdm0801.getOverhaulPlanId());
        uploadFile(paramMap, fileList);
    }

    /**
     * 上传附件
     *
     * @param paramMap 参数
     * @param fileList 附件列表
     */
    private void uploadFile(Map paramMap, List<String> fileList) throws Exception {
        List<Map> insList = new ArrayList<>(fileList.size());
        for (String fileStr : fileList) {
            // 转换文件
            CommonsMultipartFile file = Base64ToMultipartFileConverter.convertToMultipartFile(fileStr);
            // 上传文件
            String fileName = file.getOriginalFilename();
            if (StrUtil.isBlank(fileName)) {
                fileName = "";
            }
            String segNo = paramMap.get("segNo").toString();
            String fileType = paramMap.get("fileType").toString();
            String id = paramMap.get("id").toString();
            // 生成文件ID
            String fileId = UUIDUtils.getUUID();
            // 获取文件后缀
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            // 转换文件名防止文件重复
            String storeName = fileId + suffix;
            // 上传文件
            String downloadUrl = FileUtils.uploadFile(file, "/" + fileType + "/" + segNo);
//            String downloadUrl = FtpUtils.uploadFile(vgdm0402.getSegNo(), file, "VGDM0402/" + storeName);
//            if (downloadUrl == null) {
//                throw new PlatException("文件上传失败");
//            }
            // 待新增的附件记录
            VGDM0802 vgdm0802 = new VGDM0802();
            vgdm0802.setUuid(UUIDUtils.getUUID());
            vgdm0802.setRelevanceId(id);
            vgdm0802.setRelevanceType(fileType);
            vgdm0802.setSegNo(segNo);
            vgdm0802.setUnitCode(vgdm0802.getSegNo());
            // 文件信息
            vgdm0802.setUploadFileName(fileName);
            vgdm0802.setFifleType(suffix);
            vgdm0802.setFifleSize(new BigDecimal(file.getSize()));
            vgdm0802.setFileId(fileId);
            // 设置文件下载路径
            vgdm0802.setUploadFilePath(downloadUrl);
            // 创建人信息
            vgdm0802.setRecCreator(paramMap.get("actualsRevisor").toString());
            vgdm0802.setRecCreatorName(paramMap.get("actualsRevisorName").toString());
            vgdm0802.setRecCreateTime(paramMap.get("actualsTime").toString());
            // 新增到列表
            insList.add(vgdm0802.toMap());
        }
        // 批量新增附件
        DaoUtils.insertBatch(dao, VGDM0802.INSERT, insList);
    }

    /**
     * 获取小代码值集
     * serviceId:S_VG_DM_0012
     */
    public EiInfo getCodeset(EiInfo inInfo) {
        try {
            // 调用平台微服务
            inInfo.set(EiConstant.serviceId, "S_ED_02");
            inInfo = XServiceManager.call(inInfo);
            if (inInfo.getStatus() != EiConstant.STATUS_DEFAULT) {
                throw new PlatException(inInfo.getMsg());
            }
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询待检修设备信息
     * <p>
     * serviceId:S_VG_DM_0013
     */
    public EiInfo queryOverhaulEquipment(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("账套为空");
            }
            log("查询设备参数：" + segNo);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            List<Map> list = dao.query(VGDM0801.QUERY_EQUIPMENT, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询检修计划信息
     * <p>
     * serviceId:S_VG_DM_0014
     */
    public EiInfo queryOverhaul(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("设备代码为空");
            }
            log("查询检修计划参数：" + segNo + "|" + eArchivesNo);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            // 状态标记-生效或启动状态
            queryMap.put("activeFlag", segNo);
            List<Map> list = dao.query(VGDM0801.QUERY_FOR_PDA, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 更新检修实绩
     * serviceId:S_VG_DM_0015
     */
    public EiInfo updateOverhaul(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取传入参数
            String uuid = inInfo.getString("uuid");
            String actualsRevisor = inInfo.getString("userId");
            String actualsRevisorName = inInfo.getString("userName");
            // 校验传入参数
            if (StrUtil.isBlank(uuid) || StrUtil.isBlank(actualsRevisor) || StrUtil.isBlank(actualsRevisorName)) {
                throw new PlatException("参数不能为空");
            }
            log("更新检修实绩参数：" + uuid + "|" + actualsRevisor + "|" + actualsRevisorName);
            Map params = inInfo.getAttr();
            // 校验数据
            VGDM0801 vgdm0801 = new VGDM0801();
            vgdm0801.fromMap(params);
            vgdm0801.setUuid(uuid);
            vgdm0801.setActualsRevisor(actualsRevisor);
            vgdm0801.setActualsRevisorName(actualsRevisorName);
            vgdm0801.setActualsTime(DateUtil.curDateTimeStr14());
            // 构建eiInfo
            EiInfo eiInfo = new EiInfo();
            eiInfo.addBlock(EiConstant.resultBlock).addRow(vgdm0801);
            // 调用更新方法更新实绩信息
            eiInfo.set(EiConstant.serviceName, "VGDM0801");
            eiInfo.set(EiConstant.methodName, "pdaConfirm");
            eiInfo = XLocalManager.call(eiInfo);
            if (eiInfo.getStatus() != EiConstant.STATUS_DEFAULT) {
                throw new PlatException(eiInfo.getMsg());
            }
            // 上传附件
            List<String> fileList = (List<String>) inInfo.get("fileList");
            if (CollectionUtils.isNotEmpty(fileList)) {
                uploadFile(vgdm0801, fileList);
            }
            // 上传附件
            List<String> hotFileList = (List<String>) inInfo.get("hotFileList");
            if (CollectionUtils.isNotEmpty(hotFileList)) {
                uploadFile2(vgdm0801, hotFileList);
            }
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }


    /**
     * 查询生效生产设备信息
     * <p>
     * serviceId:S_VG_DM_0016
     */
    public EiInfo queryActiveEquipment(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("账套为空");
            }
            log("查询生效设备：" + segNo);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            // 只查询生产设备
            queryMap.put("equipmentType", "1");
            // 只查询生效设备
            queryMap.put("equipmentStatus", MesConstant.Status.K30);
            List<Map> list = dao.query(VGDM0101.QUERY, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询今天待点检分部设备信息
     * <p>
     * serviceId:S_VG_DM_0017
     */
    public EiInfo queryActiveDevice(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("账套或设备编码为空");
            }
            log("查询分部设备参数：" + segNo + "|" + eArchivesNo);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            queryMap.put("deviceStatus", MesConstant.Status.K30);
            List<Map> list = dao.query(VGDM0102.QUERY, queryMap);
            inInfo.set("list", list);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 新增故障
     * serviceId:S_VG_DM_0018
     */
    public EiInfo insertFault(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取传入参数
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            String equipmentName = inInfo.getString("equipmentName");
            String deviceCode = inInfo.getString("deviceCode");
            String deviceName = inInfo.getString("deviceName");
            String faultDesc = inInfo.getString("faultDesc");
            String recCreator = inInfo.getString("userId");
            String recCreatorName = inInfo.getString("userName");
            // 校验传入参数
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo) || StrUtil.isBlank(recCreator) || StrUtil.isBlank(recCreatorName) || StrUtil.isBlank(equipmentName) || StrUtil.isBlank(deviceCode) || StrUtil.isBlank(deviceName)) {
                throw new PlatException("参数不能为空");
            }
            log("新增故障参数：" + eArchivesNo + "|" + equipmentName + "|" + deviceCode + "|" + deviceName + "|" + recCreator + "|" + recCreatorName);
            // 赋值数据
            VGDM0701 vgdm0701 = new VGDM0701();
            vgdm0701.setSegNo(segNo);
            vgdm0701.setUnitCode(segNo);
            vgdm0701.setEArchivesNo(eArchivesNo);
            vgdm0701.setEquipmentName(equipmentName);
            vgdm0701.setDeviceCode(deviceCode);
            vgdm0701.setDeviceName(deviceName);
            vgdm0701.setFaultDesc(faultDesc);
            vgdm0701.setFaultStartTime(DateUtil.curDateTimeStr14());
            vgdm0701.setFaultSource("1");
            vgdm0701.setFaultStatus(MesConstant.Status.K10);
            //  插入数据
            vgdm0701.setRecCreator(recCreator);
            vgdm0701.setRecCreatorName(recCreatorName);
            vgdm0701.setRecCreateTime(vgdm0701.getFaultStartTime());
            vgdm0701.setRecRevisor(recCreator);
            vgdm0701.setRecRevisorName(recCreatorName);
            vgdm0701.setRecReviseTime(vgdm0701.getRecCreateTime());
            vgdm0701.setUuid(UUIDUtils.getUUID());
            String[] arr = {vgdm0701.getSegNo()};
            vgdm0701.setFaultId(SequenceGenerator.getNextSequence(SequenceConstant.FAULT_ID, arr));
            // 新增数据
            Map insMap = vgdm0701.toMap();
            dao.insert(VGDM0701.INSERT, insMap);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询机组作业信息
     * <p>
     * serviceId:S_VG_DM_0023
     */
    public EiInfo queryWorkingPack(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String machineCode = inInfo.getString("machineCode");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(machineCode)) {
                throw new PlatException("账套或机组为空");
            }
            log("查询机组作业信息参数：" + segNo + "|" + machineCode);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("machineCode", machineCode);
            queryMap.put("activeFlag", "1");
            queryMap.put("orderBy", "REC_CREATE_TIME DESC");
            List<VGDM1002> list = dao.query(VGDM1002.QUERY, queryMap);
            List<Map> resultList = new ArrayList<>();
            for (VGDM1002 vgdm1002 : list) {
                Map map = vgdm1002.toMap();
                resultList.add(map);
            }
            inInfo.set("list", resultList);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询机组作业信息
     * <p>
     * serviceId:S_VG_DM_0024
     */
    public EiInfo forceEndPack(EiInfo inInfo) {
        try {
            // 检查token
            checkToken(inInfo);
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String uuid = inInfo.getString("uuid");
            String userId = inInfo.getString("userId");
            String userName = inInfo.getString("userName");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(uuid)) {
                throw new PlatException("传入参数为空");
            }
            log("强制结束参数：" + segNo + "|" + uuid + "|" + userId + "|" + userName);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("uuid", uuid);
            List<VGDM1002> list = dao.query(VGDM1002.QUERY, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("查询结果为空" + uuid);
            }
            VGDM1002 vgdm1002 = list.get(0);
            if (!"1".equals(vgdm1002.getPackStatus()) && !"0".equals(vgdm1002.getPackStatus())) {
                throw new PlatException("当前状态不能结束" + vgdm1002.getPackStatus());
            }
            vgdm1002.setEndTime(DateUtil.curDateTimeStr14());
            vgdm1002.setPackStatus("2");
            vgdm1002.setEndType("9");
            Map updMap = vgdm1002.toMap();
            RecordUtils.setRevisor(updMap);
            updMap.put("recRevisor", userId);
            updMap.put("recRevisorName", userName);
            dao.update(VGDM1002.UPDATE, updMap);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 机组对应设备信息
     */
    private final Map<String, String> machineDeviceMap = new HashMap<>();
    /**
     * 机组对应点位信息
     */
    private final Map<String, String> machineTagMap = new HashMap<>();

    {
        // 1650横切
        machineDeviceMap.put("H1", "modbus_JC52CL01MAIN,snap7drv_JC52CL01");
        // 1650纵切
        machineDeviceMap.put("Z4", "modbus_JC52SL05MAIN,modbus_JC52SL05CHENG");
        // 800横切
        machineDeviceMap.put("J4", "modbus_JC52CL02MAIN,snap7drv_JC52CL02");
        // 2050落料
        machineDeviceMap.put("L1", "modbus_JC52BL01DD,modbus_JC52BL01MAIN");
        // 1650横切
        machineTagMap.put("H1", "JC52CL011001");
        // 1650纵切
        machineTagMap.put("Z4", "JC52SL050001");
        // 800横切
        machineTagMap.put("J4", "JC52CL020034");
        // 2050落料
        machineTagMap.put("L1", "JC52BL010001");
    }

    /**
     * 查询设备状态
     */
    public EiInfo queryBCDeviceStatus(EiInfo inInfo) {
        try {
            String machineCode = inInfo.getString("machineCode");
            if (StrUtil.isBlank(machineCode)) {
                throw new PlatException("机组为空");
            }
            log("查询设备状态参数：" + machineCode);
            String machineDevice = machineDeviceMap.get(machineCode);
            if (StrUtil.isBlank(machineDevice)) {
                throw new PlatException("机组对应设备为空" + machineCode);
            }
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("scadaName", "JC_01");
            eiInfo.set("scadaIp", "************");
            eiInfo.set(EiConstant.serviceId, "S_BI_DX_36");
            EiInfo rtnInfo = XServiceManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            String returnValue = rtnInfo.getString("result");
            log("设备状态：" + returnValue);
            JSONArray jsonArray = JSON.parseArray(returnValue);
            int flag = 1;
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String deviceName = jsonObject.getString("name");
                int deviceStatus = jsonObject.getIntValue("status");
                log(deviceName + "|" + deviceStatus);
                if (machineDevice.contains(deviceName) && deviceStatus == 0) {
                    log("设备" + deviceName + "已停机");
                    flag = 0;
                    break;
                }
            }
            inInfo.set("deviceFlag", flag);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }


    /**
     * 手动上料并启动工单
     */
    public EiInfo startOrderPda(EiInfo inInfo) {
        try {
            // 获取查询条件
            String machineCode = inInfo.getString("machineCode");
            if (StrUtil.isBlank(machineCode)) {
                throw new PlatException("传入参数为空");
            }
            log("手工启动工单传入参数：" + machineCode);
            String tagId = machineTagMap.get(machineCode);
            String upPackMethod = "upPack";
            String startOrderMethod = "startProcessOrder";
            if ("L1".equals(machineCode)) {
                upPackMethod = "upBLPack";
                startOrderMethod = "startBLOrder";
            } else if ("H1".equals(machineCode)) {
                startOrderMethod = "startCL01Order";
            } else if ("J4".equals(machineCode)) {
                startOrderMethod = "startCL02Order";
            }
            log("手工启动工单参数：" + tagId + "|" + upPackMethod + "|" + startOrderMethod);
            // 上料
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("tagId", tagId);
            eiInfo.set("notAutoFlag", "1");
            eiInfo.set(EiConstant.serviceName, "VGDM1002");
            eiInfo.set(EiConstant.methodName, upPackMethod);
            EiInfo rtnInfo = XLocalManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            log("上料成功,继续启动工单");
            // 启动工单
            eiInfo.set(EiConstant.methodName, startOrderMethod);
            rtnInfo = XLocalManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            log("启动工单成功");
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }
}

