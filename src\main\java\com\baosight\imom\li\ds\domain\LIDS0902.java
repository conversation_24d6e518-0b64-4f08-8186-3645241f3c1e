/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids0902;

import java.util.Map;

/**
* TLIDS0902
* 
*/
public class LIDS0902 extends Tlids0902 {
        public static final String QUERY = "LIDS0902.query";
        public static final String COUNT = "LIDS0902.count";
        public static final String INSERT = "LIDS0902.insert";
        public static final String UPDATE = "LIDS0902.update";
        public static final String DELETE = "LIDS0902.delete";
        public static final String INERT_INTERFACE_LIST = "LIDS0902.inert0902InterfaceList";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0902() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}