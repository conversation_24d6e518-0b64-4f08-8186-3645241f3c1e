package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.EasyExcelUtil;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0504;
import com.baosight.imom.li.rl.dao.LIRL0506;
import com.baosight.imom.li.rl.dao.LIRL0509;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @Author: 韩亚宁
 * @Description:
 * @Date: 2025/8/20 13:00
 * @Version: 1.0
 */
public class ServiceLIRL0509 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0509().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        outInfo = super.query(inInfo, LIRL0509.QUERY);
        return outInfo;
    }

    public EiInfo query2(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        String segNo = (String) inInfo.get("segNo");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo", segNo);
        objectObjectHashMap.put("status", "20");
        List query = this.dao.query(LIRL0506.QUERY, objectObjectHashMap);
        outInfo.addBlock("attachmentPrintR").setRows(query);
        return outInfo;
    }


    public EiInfo postExport(EiInfo inInfo) {
        Map<String, Object> loginMap = new HashMap();
        loginMap.put("userId", UserSession.getUserId());
        loginMap.put("userName", UserSession.getLoginCName());
        loginMap.put("loginName", UserSession.getLoginName());

        EiInfo outInfo = new EiInfo();
        String segNo = (String) inInfo.get("segNo");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo", segNo);
        objectObjectHashMap.put("status", "20");
        List<LIRL0509> query = this.dao.query(LIRL0509.QUERY, objectObjectHashMap);
        List<Map<String,Object>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(query)){
            for (LIRL0509 lirl0509 : query) {
                Map map = lirl0509.toMap();
                list.add(map);
            }
        }
        inInfo.getBlock("exportColumnBlock").addRows(list);
        inInfo.getBlock("exportColumnBlock").addBlockMeta(new LIRL0509().eiMetadata);
        if (CollectionUtils.isNotEmpty(list)){
            extracted(list,"1");
        }
        Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, list, loginMap);
        inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        return inInfo;
    }

    private static void extracted(List<Map<String,Object>> hashMapList, String b) {
        for (Map hashMap : hashMapList) {
            if ("1".equals(b)) {
                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "撤销");
                        break;
                    case "20":
                        hashMap.put("status", "生效");
                        break;
                    case "99":
                        hashMap.put("status", "完成");
                        break;
                }
                String deliveryStatus = MapUtils.getString(hashMap, "deliveryStatus", "");
                switch (deliveryStatus) {
                    case "10":
                        hashMap.put("deliveryStatus", "预警");
                        break;
                    case "20":
                        hashMap.put("deliveryStatus", "紧急");
                        break;
                        case "30":
                        hashMap.put("deliveryStatus", "特急");
                        break;
                }
                String jobStatus = MapUtils.getString(hashMap, "jobStatus", "");
                switch (jobStatus) {
                    case "10":
                        hashMap.put("jobStatus", "未配单");
                        break;
                    case "20":
                        hashMap.put("jobStatus", "未进厂已配单");
                        break;
                    case "30":
                        hashMap.put("jobStatus", "进厂已配单未启动排队");
                        break;
                    case "40":
                        hashMap.put("jobStatus", "排队中");
                        break;
                    case "50":
                        hashMap.put("jobStatus", "已叫号");
                        break;
                    case "60":
                        hashMap.put("jobStatus", "作业中");
                        break;
                    case "70":
                        hashMap.put("jobStatus", "作业完成未出厂");
                }
            }
        }
    }
    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                String billPrintType = MapUtils.getString(hashMap, "billPrintType", "");//单据类型
                String printerIpAddr = MapUtils.getString(hashMap, "printerIpAddr", "");//打印机ip地址
                String printerPort = MapUtils.getString(hashMap, "printerPort", "");//打印机端口
                String printerType = MapUtils.getString(hashMap, "printerType", "");//打印机类型
                String factoryBuilding = MapUtils.getString(hashMap, "factoryBuilding", "");//厂房
                //查询数据是否重复
                Map query = new HashMap();
                query.put("segNo", segNo);
                query.put("billPrintType", billPrintType);
                query.put("printerIpAddr", printerIpAddr);
                query.put("printerPort", printerPort);
                query.put("printerType", printerType);
                query.put("delFlag", 0);
                if (StringUtils.isNotBlank(factoryBuilding)){
                    query.put("factoryBuilding", factoryBuilding);
                }

                int count = super.count(LIRL0506.COUNT, query);
                if (count > 0) {
                    String massage = "相同单据类型的打印机配置已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                String uuid = UUIDHexIdGenerator.generate().toString();
                hashMap.put("uuid", uuid);//UUID
                // 创建人工号
                hashMap.put("recCreator", UserSession.getUserId());
                // 创建人姓名
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", UserSession.getUserId());
                // 修改人姓名
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                hashMap.put("delFlag", "0");
                hashMap.put("archiveFlag", "0");
                hashMap.put("status", "10");
            }
            inInfo = super.insert(inInfo, LIRL0506.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0509> query = dao.query(LIRL0509.QUERY, map);
                for (LIRL0509 LIRL0506 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusEffect(inInfo, LIRL0506);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
                hashMap.put("status","99");
            }
            inInfo = super.update(inInfo, LIRL0509.UPDATE_STATUS);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                if (org.apache.commons.lang.StringUtils.isBlank(uuid)) {
                    String massage = "记录不存在,不能删除!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0506> query = dao.query(LIRL0506.QUERY, map);
                for (LIRL0506 LIRL0506 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0506);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("delFlag", 1);//记录删除标记
                hashMap.put("status", "00");//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0506.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0506> query = dao.query(LIRL0506.QUERY, map);
                for (LIRL0506 LIRL0506 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0506);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                //查询数据是否重复
                Map queryCount = new HashMap();
                queryCount.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                queryCount.put("billPrintType", MapUtils.getString(hashMap, "billPrintType", ""));
                queryCount.put("status", "20");
                int count = super.count(LIRL0506.COUNT, queryCount);
                if (count>=1){
                    String massage = "相同单据类型的配置只能有一条确认数据!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", "20");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0506.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0506> query = dao.query(LIRL0506.QUERY, map);
                for (LIRL0506 LIRL0506 : query) {
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusConfirmNo(inInfo, LIRL0506);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "10");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0506.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

}
