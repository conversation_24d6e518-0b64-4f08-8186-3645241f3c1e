/**
* Generate time : 2025-03-11 11:04:15
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* LIRL0316
* 
*/
public class LIRL0316 extends DaoEPBase {
                public static final String QUERY = "LIRL0316.query";
                public static final String COUNT = "LIRL0316.count";
                public static final String INSERT = "LIRL0316.insert";
                public static final String UPDATE = "LIRL0316.update";
                public static final String DELETE = "LIRL0316.delete";
                public static final String UPDATE_STATUS = "LIRL0316.updateStatus";
                public static final String QUERY_FILE = "LIRL0316.queryFile";
                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String segName = " ";		/* 业务单元简称*/
                private String status = " ";		/* 状态(00撤销 10新增 20确认 99 反确认)*/
                private String voucherNum = " ";		/* 提单号*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String driverName = " ";		/* 司机姓名*/
                private String driverTel = " ";		/* 司机手机号*/
                private String driverIdentity = " ";		/* 司机身份证号*/
                private String attachmentChapterMark = " ";		/* 附件加盖发货章标记(0 不加盖，1，加盖)*/
                private String attachmentPrint = " ";		/* 附件打印机(针式打印机/A4打印机)*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String uploadFilePath = " ";		/* 文件路径*/
                private String uploadFileName = " ";		/* 文件名称*/
                private String printIp = " ";		/* 打印机IP*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00撤销 10新增 20确认 99 反确认)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("提单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attachmentChapterMark");
        eiColumn.setDescName("附件加盖发货章标记(0 不加盖，1，加盖)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attachmentPrint");
        eiColumn.setDescName("附件打印机(针式打印机/A4打印机)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uploadFilePath");
        eiColumn.setDescName("文件路径");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uploadFileName");
        eiColumn.setDescName("文件名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printIp");
        eiColumn.setDescName("打印机Ip");
        eiMetadata.addMeta(eiColumn);



}
/**
* the constructor
*/
public LIRL0316() {
initMetaData();
}


    public String getPrintIp() {
        return printIp;
    }

    public void setPrintIp(String printIp) {
        this.printIp = printIp;
    }

    /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }

        public String getSegName() {
            return segName;
        }

        public void setSegName(String segName) {
            this.segName = segName;
        }

    /**
        * get the status - 状态(00撤销 10新增 20确认 99 反确认)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(00撤销 10新增 20确认 99 反确认)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the voucherNum - 提单号
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 提单号
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the driverName - 司机姓名
        * @return the driverName
        */
        public String getDriverName() {
        return this.driverName;
        }

        /**
        * set the driverName - 司机姓名
        */
        public void setDriverName(String driverName) {
        this.driverName = driverName;
        }
        /**
        * get the driverTel - 司机手机号
        * @return the driverTel
        */
        public String getDriverTel() {
        return this.driverTel;
        }

        /**
        * set the driverTel - 司机手机号
        */
        public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
        }
        /**
        * get the driverIdentity - 司机身份证号
        * @return the driverIdentity
        */
        public String getDriverIdentity() {
        return this.driverIdentity;
        }

        /**
        * set the driverIdentity - 司机身份证号
        */
        public void setDriverIdentity(String driverIdentity) {
        this.driverIdentity = driverIdentity;
        }
        /**
        * get the attachmentChapterMark - 附件加盖发货章标记(0 不加盖，1，加盖)
        * @return the attachmentChapterMark
        */
        public String getAttachmentChapterMark() {
        return this.attachmentChapterMark;
        }

        /**
        * set the attachmentChapterMark - 附件加盖发货章标记(0 不加盖，1，加盖)
        */
        public void setAttachmentChapterMark(String attachmentChapterMark) {
        this.attachmentChapterMark = attachmentChapterMark;
        }
        /**
        * get the attachmentPrint - 附件打印机(针式打印机/A4打印机)
        * @return the attachmentPrint
        */
        public String getAttachmentPrint() {
        return this.attachmentPrint;
        }

        /**
        * set the attachmentPrint - 附件打印机(针式打印机/A4打印机)
        */
        public void setAttachmentPrint(String attachmentPrint) {
        this.attachmentPrint = attachmentPrint;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }

        public String getUploadFilePath() {
            return uploadFilePath;
        }

        public void setUploadFilePath(String uploadFilePath) {
            this.uploadFilePath = uploadFilePath;
        }

        public String getUploadFileName() {
            return uploadFileName;
        }

        public void setUploadFileName(String uploadFileName) {
            this.uploadFileName = uploadFileName;
        }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
                setDriverTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverTel")), driverTel));
                setDriverIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverIdentity")), driverIdentity));
                setAttachmentChapterMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attachmentChapterMark")), attachmentChapterMark));
                setAttachmentPrint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attachmentPrint")), attachmentPrint));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setUploadFilePath(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uploadFilePath")), uploadFilePath));
                setUploadFileName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uploadFileName")), uploadFileName));
                setPrintIp(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printIp")), printIp));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("driverName",StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
                map.put("driverTel",StringUtils.toString(driverTel, eiMetadata.getMeta("driverTel")));
                map.put("driverIdentity",StringUtils.toString(driverIdentity, eiMetadata.getMeta("driverIdentity")));
                map.put("attachmentChapterMark",StringUtils.toString(attachmentChapterMark, eiMetadata.getMeta("attachmentChapterMark")));
                map.put("attachmentPrint",StringUtils.toString(attachmentPrint, eiMetadata.getMeta("attachmentPrint")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("uploadFilePath",StringUtils.toString(uploadFilePath, eiMetadata.getMeta("uploadFilePath")));
                map.put("uploadFileName",StringUtils.toString(uploadFileName, eiMetadata.getMeta("uploadFileName")));
                map.put("printIp",StringUtils.toString(printIp, eiMetadata.getMeta("printIp")));

return map;

}
}