CREATE TABLE ED_XM_2PC_LOG (
  LOG_ID VARCHAR2(36) DEFAULT ' '    NOT NULL ,
  TRANSACTION_ID VARCHAR2(50) DEFAULT ' '    NOT NULL ,
  SERVICE_NAME VARCHAR2(20) DEFAULT ' '    NOT NULL ,
  SERVICE_ID VARCHAR2(20) DEFAULT ' '    NOT NULL ,
  METHOD_NAME VARCHAR2(50) DEFAULT ' '    NOT NULL ,
  STATUS VARCHAR2(2) DEFAULT ' '    NOT NULL ,
  LOG_INFO VARCHAR2(1000) DEFAULT ' '    NOT NULL ,
  TIME_STAMP VARCHAR2(17) ,
  TRACE_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL
);
COMMENT ON TABLE ED_XM_2PC_LOG IS '2PC日志信息表';
ALTER TABLE ED_XM_2PC_LOG ADD CONSTRAINT PK_ED_XM_2PC_LOG PRIMARY KEY (LOG_ID) ;


CREATE TABLE ED_XM_EVENT (
  EVENT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  EVENT_DESC VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  SYNC_TYPE VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  IS_AUTH VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT.EVENT_DESC IS '事件描述';
COMMENT ON COLUMN ED_XM_EVENT.SYNC_TYPE IS '同/异步标志';
COMMENT ON COLUMN ED_XM_EVENT.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_EVENT.IS_AUTH IS '是否授权';
COMMENT ON COLUMN ED_XM_EVENT.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_EVENT IS '微服务事件表';
ALTER TABLE ED_XM_EVENT ADD CONSTRAINT PK_ED_XM_EVENT  PRIMARY KEY (EVENT_ID) ;


CREATE TABLE ED_XM_EVENT_PARAM (
  PARAM_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  EVENT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PARAM_KEY VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  PARAM_KEY_CNAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  PARAM_TYPE VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  PARAM_DEF_VALUE VARCHAR2(4000)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_ID IS '微事件参数标识';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_KEY IS '参数英文名';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_KEY_CNAME IS '参数中文名';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_TYPE IS '参数类型';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_DEF_VALUE IS '参数缺省值';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_EVENT_PARAM IS '微事件参数信息表';
ALTER TABLE ED_XM_EVENT_PARAM ADD CONSTRAINT PK_ED_XM_EVENT_PARAM  PRIMARY KEY (PARAM_ID) ;


CREATE TABLE ED_XM_EVENT_ROUTE (
  EVENT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SERVICE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ROUTE_KEY VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  ROUTE_VALUE VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.ROUTE_KEY IS '路由键';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.ROUTE_VALUE IS '路由值';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_EVENT_ROUTE IS '路由表';
--TODO


CREATE TABLE ED_XM_EVENT_SERVICE_RELA (
  EVENT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SERVICE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SORT_INDEX VARCHAR2(3)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.SORT_INDEX IS '排序';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON TABLE ED_XM_EVENT_SERVICE_RELA IS '微服务事件服务关联表';
ALTER TABLE ED_XM_EVENT_SERVICE_RELA ADD CONSTRAINT PK_ED_XM_EVENT_SERVICE_RELA  PRIMARY KEY (EVENT_ID,SERVICE_ID,SORT_INDEX) ;


CREATE TABLE ED_XM_PARAM (
  PARAM_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SERVICE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PARAM_KEY VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  PARAM_KEY_CNAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  PARAM_TYPE VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  PARAM_DEF_VALUE VARCHAR2(4000)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_PARAM.PARAM_ID IS '微服务参数标识';
COMMENT ON COLUMN ED_XM_PARAM.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_KEY IS '参数英文名';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_KEY_CNAME IS '参数中文名';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_TYPE IS '参数类型';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_DEF_VALUE IS '参数缺省值';
COMMENT ON COLUMN ED_XM_PARAM.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_PARAM.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_PARAM.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_PARAM.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_PARAM.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_PARAM.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_PARAM IS '微服务参数信息表';
ALTER TABLE ED_XM_PARAM ADD CONSTRAINT PK_ED_XM_PARAM  PRIMARY KEY (PARAM_ID) ;


CREATE TABLE ED_XM_SERVICE (
  SERVICE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SERVICE_ENAME VARCHAR2(64) ,
  METHOD_ENAME VARCHAR2(64) ,
  SERVICE_TYPE VARCHAR2(16) ,
  SERVICE_DESC VARCHAR2(256) ,
  URL VARCHAR2(256) ,
  REMARK VARCHAR2(512) ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  IS_AUTH VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  TRANS_TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_ENAME IS '服务英文名';
COMMENT ON COLUMN ED_XM_SERVICE.METHOD_ENAME IS '方法英文名';
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_TYPE IS '服务类型';
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_DESC IS '中文描述';
COMMENT ON COLUMN ED_XM_SERVICE.URL IS 'URL';
COMMENT ON COLUMN ED_XM_SERVICE.REMARK IS '备注';
COMMENT ON COLUMN ED_XM_SERVICE.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_SERVICE.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_SERVICE.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_SERVICE.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_SERVICE.IS_AUTH IS '是否授权';
COMMENT ON COLUMN ED_XM_SERVICE.TRANS_TYPE IS '事务类型';
COMMENT ON COLUMN ED_XM_SERVICE.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_SERVICE.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_SERVICE IS '微服务信息表';
ALTER TABLE ED_XM_SERVICE ADD CONSTRAINT PK_ED_XM_SERVICE_SERVICE_ID  PRIMARY KEY (SERVICE_ID) ;


CREATE TABLE EJ_QRTZ_BLOB_TRIGGERS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TRIGGER_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  BLOB_DATA BLOB
);
ALTER TABLE EJ_QRTZ_BLOB_TRIGGERS ADD CONSTRAINT PK_EJ_QRTZ_BLOB_TRIGGERS  PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) ;


CREATE TABLE EJ_QRTZ_CALENDARS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  CALENDAR_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  CALENDAR BLOB NOT NULL
);
ALTER TABLE EJ_QRTZ_CALENDARS ADD CONSTRAINT PK_EJ_QRTZ_CALENDARS  PRIMARY KEY (SCHED_NAME,CALENDAR_NAME) ;

CREATE TABLE EJ_QRTZ_CRON_TRIGGERS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TRIGGER_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  CRON_EXPRESSION VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TIME_ZONE_ID VARCHAR2(80)
);
ALTER TABLE EJ_QRTZ_CRON_TRIGGERS ADD CONSTRAINT PK_EJ_QRTZ_CRON_TRIGGERS  PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) ;


CREATE TABLE EJ_QRTZ_FIRED_TRIGGERS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  ENTRY_ID VARCHAR2(95)  DEFAULT ' '    NOT NULL ,
  TRIGGER_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  INSTANCE_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  FIRED_TIME NUMBER(13,0) NOT NULL ,
  SCHED_TIME NUMBER(13,0) NOT NULL ,
  PRIORITY NUMBER(11) NOT NULL ,
  STATE VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  JOB_NAME VARCHAR2(200) ,
  JOB_GROUP VARCHAR2(200) ,
  IS_NONCONCURRENT VARCHAR2(1) ,
  REQUESTS_RECOVERY VARCHAR2(1)
);
ALTER TABLE EJ_QRTZ_FIRED_TRIGGERS ADD CONSTRAINT PK_EJ_QRTZ_FIRED_TRIGGERS  PRIMARY KEY (SCHED_NAME,ENTRY_ID) ;


CREATE TABLE EJ_QRTZ_JOB_DETAILS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  JOB_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  JOB_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  DESCRIPTION VARCHAR2(250) ,
  JOB_CLASS_NAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  IS_DURABLE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  IS_NONCONCURRENT VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  IS_UPDATE_DATA VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  REQUESTS_RECOVERY VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  JOB_DATA BLOB
);
ALTER TABLE EJ_QRTZ_JOB_DETAILS ADD CONSTRAINT PK_EJ_QRTZ_JOB_DETAILS  PRIMARY KEY (SCHED_NAME,JOB_NAME,JOB_GROUP) ;


CREATE TABLE EJ_QRTZ_LOCKS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  LOCK_NAME VARCHAR2(40)  DEFAULT ' '    NOT NULL
);
ALTER TABLE EJ_QRTZ_LOCKS ADD CONSTRAINT PK_EJ_QRTZ_LOCKS  PRIMARY KEY (SCHED_NAME,LOCK_NAME) ;


CREATE TABLE EJ_QRTZ_LOGGING (
  SID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  START_TIME NUMBER(13,0) ,
  END_TIME NUMBER(13,0) ,
  JOB_NAME VARCHAR2(200) ,
  JOB_GROUP VARCHAR2(80) ,
  TRIGGER_NAME VARCHAR2(80) ,
  TRIGGER_GROUP VARCHAR2(80) ,
  STATUS NUMBER(2,0) ,
  EXE_STATUS NUMBER(2,0) ,
  EXE_INFO VARCHAR2(250) ,
  EXE_MSG VARCHAR2(4000),
  PROJECT_NAME VARCHAR2(32)
);
ALTER TABLE EJ_QRTZ_LOGGING ADD CONSTRAINT PK_EJ_QRTZ_LOGGING  PRIMARY KEY (SID) ;


CREATE TABLE EJ_QRTZ_PAUSED_TRIGGER_GRPS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL
);
ALTER TABLE EJ_QRTZ_PAUSED_TRIGGER_GRPS ADD CONSTRAINT PK_EJ_QRTZ_PAUSED_TRIGGER_GRPS  PRIMARY KEY (SCHED_NAME,TRIGGER_GROUP) ;


CREATE TABLE EJ_QRTZ_SCHEDULER_STATE (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  INSTANCE_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  LAST_CHECKIN_TIME NUMBER(13,0) NOT NULL ,
  CHECKIN_INTERVAL NUMBER(13,0) NOT NULL
);
ALTER TABLE EJ_QRTZ_SCHEDULER_STATE ADD CONSTRAINT PK_EJ_QRTZ_SCHEDULER_STATE  PRIMARY KEY (SCHED_NAME,INSTANCE_NAME) ;


CREATE TABLE EJ_QRTZ_SIMPLE_TRIGGERS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TRIGGER_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  REPEAT_COUNT NUMBER(7,0) NOT NULL ,
  REPEAT_INTERVAL NUMBER(12,0) NOT NULL ,
  TIMES_TRIGGERED NUMBER(12,0) NOT NULL
);
ALTER TABLE EJ_QRTZ_SIMPLE_TRIGGERS ADD CONSTRAINT PK_EJ_QRTZ_SIMPLE_TRIGGERS  PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) ;

CREATE TABLE EJ_QRTZ_SIMPROP_TRIGGERS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TRIGGER_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  STR_PROP_1 VARCHAR2(512) ,
  STR_PROP_2 VARCHAR2(512) ,
  STR_PROP_3 VARCHAR2(512) ,
  INT_PROP_1 NUMBER(11) ,
  INT_PROP_2 NUMBER(11) ,
  LONG_PROP_1 NUMBER(13,0) ,
  LONG_PROP_2 NUMBER(13,0) ,
  DEC_PROP_1 NUMBER(13,4) ,
  DEC_PROP_2 NUMBER(13,4) ,
  BOOL_PROP_1 VARCHAR2(1) ,
  BOOL_PROP_2 VARCHAR2(1)
);
ALTER TABLE EJ_QRTZ_SIMPROP_TRIGGERS ADD CONSTRAINT PK_EJ_QRTZ_SIMPROP_TRIGGERS  PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) ;

CREATE TABLE EJ_QRTZ_TRIGGERS (
  SCHED_NAME VARCHAR2(120)  DEFAULT ' '    NOT NULL ,
  TRIGGER_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  TRIGGER_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  JOB_NAME VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  JOB_GROUP VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  DESCRIPTION VARCHAR2(250) ,
  NEXT_FIRE_TIME NUMBER(13,0) ,
  PREV_FIRE_TIME NUMBER(13,0) ,
  PRIORITY NUMBER(11) ,
  TRIGGER_STATE VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  TRIGGER_TYPE VARCHAR2(8)  DEFAULT ' '    NOT NULL ,
  START_TIME NUMBER(13,0) NOT NULL ,
  END_TIME NUMBER(13,0) ,
  CALENDAR_NAME VARCHAR2(200) ,
  MISFIRE_INSTR NUMBER(2,0) ,
  JOB_DATA BLOB
);
ALTER TABLE EJ_QRTZ_TRIGGERS ADD CONSTRAINT PK_EJ_QRTZ_TRIGGERS  PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) ;


CREATE TABLE EM_HI_LOG (
  ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  CATEGORY VARCHAR2(16) ,
  MESSAGE_TYPE_ID VARCHAR2(255) ,
  STATE VARCHAR2(16) ,
  RETURN_STATUS VARCHAR2(10) ,
  RETURN_BODY BLOB ,
  OPERATE_START_TIME VARCHAR2(20) ,
  OPERATE_RESULT NUMBER(5,0) ,
  OPERATE_END_TIME VARCHAR2(20) ,
  OPERATE_COUNT NUMBER(5,0) ,
  SERVICE_NAME VARCHAR2(128) ,
  METHOD_NAME VARCHAR2(128) ,
  RPC_TYPE VARCHAR2(64) ,
  MESSAGE_BODY BLOB ,
  PROJECT_NAME VARCHAR2(64) ,
  MOUDLE_NAME VARCHAR2(64) ,
  SEND_ADDRESS VARCHAR2(255) ,
  RECEIVE_ADDRESS VARCHAR2(255) ,
  EXT1 VARCHAR2(255) ,
  EXT2 VARCHAR2(255) ,
  EXT3 VARCHAR2(255) ,
  GROUP_NAME VARCHAR2(128) ,
  HTTP_STATUS VARCHAR2(16) ,
  EVENT_ID VARCHAR2(128)
);
COMMENT ON COLUMN EM_HI_LOG.ID IS 'UUID';
COMMENT ON COLUMN EM_HI_LOG.CATEGORY IS '消息分类(SEND:发送消息,RECEIVE:接收消息)';
COMMENT ON COLUMN EM_HI_LOG.MESSAGE_TYPE_ID IS '消息类型ID';
COMMENT ON COLUMN EM_HI_LOG.STATE IS '消息调用状态';
COMMENT ON COLUMN EM_HI_LOG.RETURN_STATUS IS '调用消息返回状态';
COMMENT ON COLUMN EM_HI_LOG.RETURN_BODY IS '返回内容';
COMMENT ON COLUMN EM_HI_LOG.OPERATE_START_TIME IS '开始调用时间';
COMMENT ON COLUMN EM_HI_LOG.OPERATE_RESULT IS '调用结果';
COMMENT ON COLUMN EM_HI_LOG.OPERATE_END_TIME IS '结束调用时间';
COMMENT ON COLUMN EM_HI_LOG.OPERATE_COUNT IS '调用次数';
COMMENT ON COLUMN EM_HI_LOG.SERVICE_NAME IS '服务调用类名';
COMMENT ON COLUMN EM_HI_LOG.METHOD_NAME IS '服务调用方法名';
COMMENT ON COLUMN EM_HI_LOG.RPC_TYPE IS '调用类型';
COMMENT ON COLUMN EM_HI_LOG.MESSAGE_BODY IS '消息内容';
COMMENT ON COLUMN EM_HI_LOG.PROJECT_NAME IS '项目英文名';
COMMENT ON COLUMN EM_HI_LOG.MOUDLE_NAME IS '模块英文名';
COMMENT ON COLUMN EM_HI_LOG.SEND_ADDRESS IS '消息发送方地址';
COMMENT ON COLUMN EM_HI_LOG.RECEIVE_ADDRESS IS '消息接收方地址';
COMMENT ON COLUMN EM_HI_LOG.EXT1 IS '扩展字段1';
COMMENT ON COLUMN EM_HI_LOG.EXT2 IS '扩展字段2';
COMMENT ON COLUMN EM_HI_LOG.EXT3 IS '业务信息';
COMMENT ON COLUMN EM_HI_LOG.GROUP_NAME IS '消息接收方地址';
ALTER TABLE EM_HI_LOG ADD CONSTRAINT PK_EM_HI_LOG  PRIMARY KEY (ID) ;


CREATE TABLE EM_HISTORY (
  ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  CATEGORY VARCHAR2(16) ,
  MESSAGE_TYPE_ID VARCHAR2(255) ,
  STATE VARCHAR2(16) ,
  RETURN_STATUS VARCHAR2(10) ,
  RETURN_BODY BLOB ,
  OPERATE_START_TIME VARCHAR2(20) ,
  OPERATE_RESULT NUMBER(5,0) ,
  OPERATE_END_TIME VARCHAR2(14) ,
  OPERATE_COUNT NUMBER(5,0) ,
  SERVICE_NAME VARCHAR2(128) ,
  METHOD_NAME VARCHAR2(128) ,
  RPC_TYPE VARCHAR2(64) ,
  MESSAGE_BODY BLOB ,
  PROJECT_NAME VARCHAR2(64) ,
  MOUDLE_NAME VARCHAR2(64) ,
  SEND_ADDRESS VARCHAR2(255) ,
  RECEIVE_ADDRESS VARCHAR2(255) ,
  EXT1 VARCHAR2(255) ,
  EXT2 VARCHAR2(255) ,
  EXT3 VARCHAR2(255) ,
  GROUP_NAME VARCHAR2(128) ,
  EVENT_ID VARCHAR2(128) ,
  HTTP_STATUS VARCHAR2(16)
);
COMMENT ON COLUMN EM_HISTORY.ID IS 'UUID';
COMMENT ON COLUMN EM_HISTORY.CATEGORY IS '消息分类(SEND:发送消息,RECEIVE:接收消息)';
COMMENT ON COLUMN EM_HISTORY.MESSAGE_TYPE_ID IS '消息类型ID';
COMMENT ON COLUMN EM_HISTORY.STATE IS '消息调用状态';
COMMENT ON COLUMN EM_HISTORY.RETURN_STATUS IS '调用消息返回状态';
COMMENT ON COLUMN EM_HISTORY.RETURN_BODY IS '返回内容';
COMMENT ON COLUMN EM_HISTORY.OPERATE_START_TIME IS '开始调用时间';
COMMENT ON COLUMN EM_HISTORY.OPERATE_RESULT IS '调用结果';
COMMENT ON COLUMN EM_HISTORY.OPERATE_END_TIME IS '结束调用时间';
COMMENT ON COLUMN EM_HISTORY.OPERATE_COUNT IS '调用结果';
COMMENT ON COLUMN EM_HISTORY.SERVICE_NAME IS '服务调用方法名';
COMMENT ON COLUMN EM_HISTORY.METHOD_NAME IS '服务调用方法名';
COMMENT ON COLUMN EM_HISTORY.RPC_TYPE IS '调用类型';
COMMENT ON COLUMN EM_HISTORY.MESSAGE_BODY IS '消息内容';
COMMENT ON COLUMN EM_HISTORY.PROJECT_NAME IS '项目英文名';
COMMENT ON COLUMN EM_HISTORY.MOUDLE_NAME IS '模块英文名';
COMMENT ON COLUMN EM_HISTORY.SEND_ADDRESS IS '消息发送方地址';
COMMENT ON COLUMN EM_HISTORY.RECEIVE_ADDRESS IS '消息接收方地址';
COMMENT ON COLUMN EM_HISTORY.EXT1 IS '扩展字段1';
COMMENT ON COLUMN EM_HISTORY.EXT2 IS '扩展字段2';
COMMENT ON COLUMN EM_HISTORY.EXT3 IS '业务信息';
COMMENT ON COLUMN EM_HISTORY.GROUP_NAME IS '消息接收方地址';
COMMENT ON COLUMN EM_HISTORY.EVENT_ID IS '事件ID';
COMMENT ON COLUMN EM_HISTORY.HTTP_STATUS IS 'HTTP状态';
ALTER TABLE EM_HISTORY ADD CONSTRAINT PK_EM_HISTORY  PRIMARY KEY (ID) ;
CREATE INDEX IDX_EM_HISTORY ON EM_HISTORY(OPERATE_START_TIME,OPERATE_END_TIME,CATEGORY);
CREATE INDEX IDX_EM_HISTORY_EVENT_ID on EM_HISTORY (EVENT_ID);
CREATE INDEX IDX_EM_HISTORY_STATUS ON EM_HISTORY(RETURN_STATUS);
CREATE INDEX IDX_EM_HISTORY_GROUP ON EM_HISTORY (GROUP_NAME);
CREATE INDEX IDX_EM_HISTORY_GROUP_QUERY on EM_HISTORY (GROUP_NAME, RETURN_STATUS, OPERATE_START_TIME ASC);

create table EM_TELE_METADATA
(
    EVENT_ID           VARCHAR(64) default ' '      not null,
    BLOCK_ENAME        VARCHAR(32) default 'result' not null,
    FIELD_ENAME        VARCHAR(32) default ' '      not null,
    FIELD_CNAME        VARCHAR(64),
    FIELD_TYPE         VARCHAR(32),
    FIELD_LENGTH       DECIMAL(11),
    FIELD_SCALE_LENGTH DECIMAL(11),
    FIELD_SORT_ID      DECIMAL(11),
    DESCRIPTION        VARCHAR(255),
    primary key (EVENT_ID, BLOCK_ENAME, FIELD_ENAME)
);
COMMENT ON COLUMN EM_TELE_METADATA.EVENT_ID IS '电文号';
COMMENT ON COLUMN EM_TELE_METADATA.BLOCK_ENAME IS '数据块英文名';
COMMENT ON COLUMN EM_TELE_METADATA.FIELD_ENAME IS '业务对象字段英文名';
COMMENT ON COLUMN EM_TELE_METADATA.FIELD_CNAME IS '业务对象字段中文名';
COMMENT ON COLUMN EM_TELE_METADATA.FIELD_TYPE IS '业务对象字段类型';
COMMENT ON COLUMN EM_TELE_METADATA.FIELD_LENGTH IS '业务对象字段长度';
COMMENT ON COLUMN EM_TELE_METADATA.FIELD_SCALE_LENGTH IS '业务对象字段精度';
COMMENT ON COLUMN EM_TELE_METADATA.FIELD_SORT_ID IS '业务对象字段排序号';
COMMENT ON COLUMN EM_TELE_METADATA.DESCRIPTION IS '电文描述';
COMMENT ON TABLE EM_TELE_METADATA IS '电文参数表';

create table EM_TELE_TYPE
(
    TELE_TYPE               VARCHAR(20)              not null
        primary key,
    TELE_TYPE_DESC          VARCHAR(250) default ' ' not null,
    TRANS_TELE_SERVICE_ID   VARCHAR(20)  default ' ' not null,
    TRANS_EIINFO_SERVICE_ID VARCHAR(20)  default ' ' not null,
    REC_CREATOR             VARCHAR(256) default ' ' not null,
    REC_CREATE_TIME         VARCHAR(14)  default ' ' not null,
    REC_REVISOR             VARCHAR(256) default ' ' not null,
    REC_REVISE_TIME         VARCHAR(14)  default ' ' not null,
    ARCHIVE_FLAG            VARCHAR(1)   default ' ' not null,
    TELE_BEAN_TYPE          VARCHAR(1)   default 0
);



CREATE TABLE TEDCC02 (
  CONFIG_ENV_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  CONFIG_ENV_CNAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROJECT VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  VERSION VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENV VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(16) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  MODULE_ENAME_1 VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_2 VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCC02.CONFIG_ENV_ID IS '配置环境标识';
COMMENT ON COLUMN TEDCC02.CONFIG_ENV_CNAME IS '配置环境中文名';
COMMENT ON COLUMN TEDCC02.PROJECT IS '项目';
COMMENT ON COLUMN TEDCC02.VERSION IS '版本';
COMMENT ON COLUMN TEDCC02.PROJECT_ENV IS '环境';
COMMENT ON COLUMN TEDCC02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCC02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCC02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCC02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCC02.MODULE_ENAME_1 IS '一级模块';
COMMENT ON COLUMN TEDCC02.MODULE_ENAME_2 IS '二级模块';
COMMENT ON COLUMN TEDCC02.TENANT_ID IS '租户';
COMMENT ON COLUMN TEDCC02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCC02 IS '配置环境';
ALTER TABLE TEDCC02 ADD CONSTRAINT PK_TEDCC02  PRIMARY KEY (CONFIG_ENV_ID) ;


CREATE TABLE TEDCC03 (
  STATUS NUMBER(11) ,
  CONFIG_TYPE VARCHAR2(255) ,
  CONFIG_CUE VARCHAR2(255) ,
  REC_CREATOR VARCHAR2(16) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(16) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  CONFIG_ENV_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  FKEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  FVALUE VARCHAR2(4000)  DEFAULT ' '    NOT NULL,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  CONFIG_DESC VARCHAR2(255)
);
COMMENT ON COLUMN TEDCC03.STATUS IS '状态：1是正常 0是删除';
COMMENT ON COLUMN TEDCC03.CONFIG_TYPE IS '配置项类别';
COMMENT ON COLUMN TEDCC03.CONFIG_CUE IS '配置项提示';
COMMENT ON COLUMN TEDCC03.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCC03.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCC03.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCC03.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCC03.CONFIG_ENV_ID IS '配置环境';
COMMENT ON COLUMN TEDCC03.FKEY IS '配置名称';
COMMENT ON COLUMN TEDCC03.FVALUE IS '配置内容';
COMMENT ON COLUMN TEDCC03.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDCC03.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCC03 IS '配置信息';
ALTER TABLE TEDCC03 ADD CONSTRAINT PK_TEDCC03  PRIMARY KEY (CONFIG_ENV_ID,FKEY) ;


CREATE TABLE TEDCM00 (
  CODESET_CODE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CODESET_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CODESET_ENAME VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  GB_CODE VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REMARK VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  CODESET_TYPE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CODESET_URL VARCHAR2(2000) DEFAULT ' ' NOT NULL,
  PROJECT_NAME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  SUB_CODESET_CODE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REF_ID VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  CODESET_LEVEL VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CATEGORY VARCHAR2(255)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCM00.CODESET_CODE IS '代码分类编号';
COMMENT ON COLUMN TEDCM00.CODESET_NAME IS '代码分类名称';
COMMENT ON COLUMN TEDCM00.CODESET_ENAME IS '代码分类英文名称';
COMMENT ON COLUMN TEDCM00.GB_CODE IS '国标编号';
COMMENT ON COLUMN TEDCM00.REMARK IS '备注';
COMMENT ON COLUMN TEDCM00.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCM00.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCM00.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCM00.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCM00.CODESET_TYPE IS '代码类型';
COMMENT ON COLUMN TEDCM00.CODESET_URL IS '对应URL';
COMMENT ON COLUMN TEDCM00.PROJECT_NAME IS '应用系统';
COMMENT ON COLUMN TEDCM00.SUB_CODESET_CODE IS '子代码分类编号';
COMMENT ON COLUMN TEDCM00.REF_ID IS '关联字段';
COMMENT ON COLUMN TEDCM00.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDCM00.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDCM00.CATEGORY IS '目录';
COMMENT ON TABLE TEDCM00 IS '代码大类表';
ALTER TABLE TEDCM00 ADD CONSTRAINT PK_TEDCM00  PRIMARY KEY (CODESET_CODE,PROJECT_NAME) ;

CREATE TABLE TEDCM0001 (
  ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CATEGORY_KEY VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CATEGORY_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PARENT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON TABLE TEDCM0001 IS '代码分类目录表';
ALTER TABLE TEDCM0001 ADD CONSTRAINT PK_TEDCM0001  PRIMARY KEY (ID) ;


CREATE TABLE TEDCM01 (
  CODESET_CODE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ITEM_CODE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ITEM_CNAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  ITEM_ENAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REMARK VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  ITEM_STATUS VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  SORT_ID VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  STATUS VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  PROJECT_NAME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  SUB_CODESET_CODE VARCHAR2(64) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCM01.CODESET_CODE IS '代码分类编号';
COMMENT ON COLUMN TEDCM01.ITEM_CODE IS '代码明细编号';
COMMENT ON COLUMN TEDCM01.ITEM_CNAME IS '代码明细中文名称';
COMMENT ON COLUMN TEDCM01.ITEM_ENAME IS '代码明细英文名称';
COMMENT ON COLUMN TEDCM01.REMARK IS '备注';
COMMENT ON COLUMN TEDCM01.ITEM_STATUS IS '代码状态';
COMMENT ON COLUMN TEDCM01.SORT_ID IS '顺序号';
COMMENT ON COLUMN TEDCM01.STATUS IS '字段状态';
COMMENT ON COLUMN TEDCM01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCM01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCM01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCM01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCM01.PROJECT_NAME IS '应用系统';
COMMENT ON COLUMN TEDCM01.SUB_CODESET_CODE IS '子代码分类编号';
COMMENT ON COLUMN TEDCM01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDCM01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCM01 IS '代码详情表';
ALTER TABLE TEDCM01 ADD CONSTRAINT PK_TEDCM01  PRIMARY KEY (CODESET_CODE, ITEM_CODE, PROJECT_NAME) ;

CREATE TABLE TEDCM02 (
  CASCADE_ID VARCHAR(100)  DEFAULT ' '    NOT NULL ,
  CASCADE_CODESET_CODE VARCHAR(100)  DEFAULT ' '    NOT NULL ,
  CASCADE_TYPE VARCHAR(1)  DEFAULT ' '    NOT NULL ,
  CODESET_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
  ITEM_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
  SUB_CODESET_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
  SUB_ITEM_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
  ACTIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
  PROJECT_NAME VARCHAR(32)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCM02.CASCADE_ID IS 'ID';
COMMENT ON COLUMN TEDCM02.CASCADE_CODESET_CODE IS '级联代码分类编号';
COMMENT ON COLUMN TEDCM02.CASCADE_TYPE IS '级联关系类型';
COMMENT ON COLUMN TEDCM02.CODESET_CODE IS '代码分类编号';
COMMENT ON COLUMN TEDCM02.ITEM_CODE IS '代码明细编号';
COMMENT ON COLUMN TEDCM02.SUB_CODESET_CODE IS '子代码分类编号';
COMMENT ON COLUMN TEDCM02.SUB_ITEM_CODE IS '子代码明细编号';
COMMENT ON COLUMN TEDCM02.ACTIVE_FLAG IS '启用标记';
COMMENT ON COLUMN TEDCM02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCM02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCM02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCM02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCM02.PROJECT_NAME IS '应用系统';
COMMENT ON COLUMN TEDCM02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCM02 IS '代码级联关系表';
ALTER TABLE TEDCM02 ADD CONSTRAINT PK_TEDCM02  PRIMARY KEY (CASCADE_ID) ;

CREATE TABLE TEDDBI0 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  TABLE_ENAME VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  INDEX_ENAME VARCHAR2(40)  DEFAULT ' '    NOT NULL ,
  TABLE_INDEX_TYPE VARCHAR2(3)  DEFAULT ' '    NOT NULL ,
  INDEX_SEQ NUMBER(9,0) NOT NULL ,
  ITEM_SEQ NUMBER(9,0) NOT NULL ,
  ITEM_SORT_TYPE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDDBI0.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDDBI0.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDDBI0.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDDBI0.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDDBI0.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDDBI0.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDDBI0.TABLE_ENAME IS '系统数据结构表英文名';
COMMENT ON COLUMN TEDDBI0.INDEX_ENAME IS '数据库索引英文名称';
COMMENT ON COLUMN TEDDBI0.TABLE_INDEX_TYPE IS '数据库表索引类型';
COMMENT ON COLUMN TEDDBI0.INDEX_SEQ IS '索引内字段顺序号';
COMMENT ON COLUMN TEDDBI0.ITEM_SEQ IS '字段索引号';
COMMENT ON COLUMN TEDDBI0.ITEM_SORT_TYPE IS '索引内数据项排序类型';
COMMENT ON COLUMN TEDDBI0.TENANT_ID IS '租户ID';
COMMENT ON TABLE TEDDBI0 IS '数据库索引信息';
ALTER TABLE TEDDBI0 ADD CONSTRAINT PK_TEDDBI0  PRIMARY KEY (PROJECT_ENAME, TABLE_ENAME, INDEX_ENAME, INDEX_SEQ) ;


CREATE TABLE TEDDBT0 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  TABLE_ENAME VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  TABLE_CNAME VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  REFER_PROJECT_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_COUNT NUMBER(10,0)  DEFAULT 0    NOT NULL ,
  REMARK VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  DB_SCHEMA VARCHAR2(40)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_1 VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_2 VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDDBT0.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDDBT0.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDDBT0.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDDBT0.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDDBT0.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDDBT0.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDDBT0.TABLE_ENAME IS '系统数据结构表英文名';
COMMENT ON COLUMN TEDDBT0.TABLE_CNAME IS '数据库表中文名称';
COMMENT ON COLUMN TEDDBT0.REFER_PROJECT_ENAME IS '引用的项目英文名称';
COMMENT ON COLUMN TEDDBT0.REC_COUNT IS '记录条数';
COMMENT ON COLUMN TEDDBT0.REMARK IS '备注';
COMMENT ON COLUMN TEDDBT0.MODULE_ENAME_1 IS '一级模块';
COMMENT ON COLUMN TEDDBT0.MODULE_ENAME_2 IS '二级模块';
COMMENT ON COLUMN TEDDBT0.TENANT_ID IS '租户ID';
COMMENT ON TABLE TEDDBT0 IS '数据库表头信息';
ALTER TABLE TEDDBT0 ADD CONSTRAINT PK_TEDDBT0  PRIMARY KEY (PROJECT_ENAME, TABLE_ENAME) ;


CREATE TABLE TEDDBT1 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  TABLE_ENAME VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  TABLE_ITEM_SEQ NUMBER(9,0) NOT NULL ,
  TABLE_ITEM_TYPE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  ITEM_SEQ NUMBER(9,0) NOT NULL ,
  MODEL_TABLE_ENAME VARCHAR2(18)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDDBT1.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDDBT1.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDDBT1.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDDBT1.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDDBT1.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDDBT1.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDDBT1.TABLE_ENAME IS '系统数据结构表英文名';
COMMENT ON COLUMN TEDDBT1.TABLE_ITEM_SEQ IS '表内数据项序号';
COMMENT ON COLUMN TEDDBT1.TABLE_ITEM_TYPE IS '表内数据项类型';
COMMENT ON COLUMN TEDDBT1.ITEM_SEQ IS '字段索引号';
COMMENT ON COLUMN TEDDBT1.MODEL_TABLE_ENAME IS '模板表英文名称';
COMMENT ON COLUMN TEDDBT1.TENANT_ID IS '租户ID';
COMMENT ON TABLE TEDDBT1 IS '数据库表体信息';
ALTER TABLE TEDDBT1 ADD CONSTRAINT PK_TEDDBT1  PRIMARY KEY (PROJECT_ENAME, TABLE_ENAME, ITEM_SEQ) ;


CREATE TABLE TEDDBT3 (
  UNITTB_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  UNITTB_REMARK VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME VARCHAR2(18)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDDBT3 ADD CONSTRAINT PK_TEDDBT3  PRIMARY KEY (UNITTB_ENAME,PROJECT_ENAME) ;


CREATE TABLE TEDDBT5 (
  TABLE_ENAME VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  USE_CLASSIFY VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  SQL_TEXT VARCHAR2(500)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(16) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDDBT5 ADD CONSTRAINT PK_TEDDBT5  PRIMARY KEY (TABLE_ENAME) ;


  CREATE TABLE TEDFA00 (
  REC_CREATOR VARCHAR2(16)   DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)   DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)   DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)   DEFAULT ' '    NOT NULL ,
  FORM_ENAME VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  FORM_CNAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  FORM_LOAD_PATH VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  FORM_TYPE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_1 VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_2 VARCHAR2(10)   DEFAULT ' '    NOT NULL ,
  INIT_LOAD_SERVICE_ENAME VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  IS_AUTH VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  FORM_PARAM VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  SUBAPP_CODE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ICON_INFO VARCHAR2(128)   DEFAULT ' '    NOT NULL ,
  BUSINESS_CATEGORY VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  OPERATE_TYPE VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)   DEFAULT ' '   NOT NULL
);
COMMENT ON COLUMN TEDFA00.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDFA00.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDFA00.REC_REVISOR IS '记录创建责任者';
COMMENT ON COLUMN TEDFA00.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDFA00.FORM_ENAME IS '画面英文名';
COMMENT ON COLUMN TEDFA00.FORM_CNAME IS '画面中文名';
COMMENT ON COLUMN TEDFA00.FORM_LOAD_PATH IS '画面载入的路径';
COMMENT ON COLUMN TEDFA00.FORM_TYPE IS '画面类型';
COMMENT ON COLUMN TEDFA00.MODULE_ENAME_1 IS '一级模块英文名';
COMMENT ON COLUMN TEDFA00.MODULE_ENAME_2 IS '二级模块英文名';
COMMENT ON COLUMN TEDFA00.INIT_LOAD_SERVICE_ENAME IS '初始处理服务英文名';
COMMENT ON COLUMN TEDFA00.IS_AUTH IS '是否授权';
COMMENT ON COLUMN TEDFA00.FORM_PARAM IS '画面参数';
COMMENT ON COLUMN TEDFA00.SUBAPP_CODE IS '子系统代码';
COMMENT ON COLUMN TEDFA00.ICON_INFO IS '图标';
COMMENT ON COLUMN TEDFA00.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDFA00.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDFA00 IS '画面信息定义';
ALTER TABLE TEDFA00 ADD CONSTRAINT PK_TEDFA00  PRIMARY KEY (FORM_ENAME) ;


CREATE TABLE TEDFA01 (
  REC_CREATOR VARCHAR2(256) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(256) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  FORM_ENAME VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  REGION_ID VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  BUTTON_ENAME VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  BUTTON_CNAME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  BUTTON_DESC VARCHAR2(100) ,
  NODE_SORT_ID VARCHAR2(20) ,
  IS_AUTH VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  URI VARCHAR2(64) ,
  LAYOUT VARCHAR2(32) ,
  POSITION VARCHAR2(32) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  BUSINESS_CATEGORY VARCHAR2(50) ,
  OPERATE_TYPE VARCHAR2(50)
);
COMMENT ON COLUMN TEDFA01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDFA01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDFA01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDFA01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDFA01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDFA01.FORM_ENAME IS '画面英文名';
COMMENT ON COLUMN TEDFA01.REGION_ID IS '区域标识';
COMMENT ON COLUMN TEDFA01.BUTTON_ENAME IS '按钮英文名';
COMMENT ON COLUMN TEDFA01.BUTTON_CNAME IS '按钮中文';
COMMENT ON COLUMN TEDFA01.BUTTON_DESC IS '按钮描述';
COMMENT ON COLUMN TEDFA01.NODE_SORT_ID IS '节点排序标识';
COMMENT ON COLUMN TEDFA01.IS_AUTH IS '是否授权';
COMMENT ON COLUMN TEDFA01.URI IS '子系统代码';
COMMENT ON COLUMN TEDFA01.LAYOUT IS '子系统代码';
COMMENT ON COLUMN TEDFA01.POSITION IS '子系统代码';
COMMENT ON TABLE TEDFA01 IS '画面按钮信息定义';
ALTER TABLE TEDFA01 ADD CONSTRAINT PK_TEDFA01  PRIMARY KEY (FORM_ENAME, BUTTON_ENAME) ;


CREATE TABLE TEDFA10 (
  PK_TEDFA10_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  USER_ID VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  FORM_ENAME VARCHAR2(8)  DEFAULT ' '    NOT NULL ,
  FORM_CNAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  NODE_SORT_ID VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDFA10 ADD CONSTRAINT PK_TEDFA10  PRIMARY KEY (PK_TEDFA10_ID) ;


CREATE TABLE TEDFA60 (
  PK_TEDFA60_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  FORM_ENAME VARCHAR2(8)  DEFAULT ' '    NOT NULL ,
  GRID_ID VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  USER_ID VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  COLUMN_ENAME VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  COLUMN_CNAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  COLUMN_LOCKED VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  COLUMN_HIDDEN VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  COLUMN_WIDTH NUMBER(11)      NOT NULL ,
  COLUMN_ORDER NUMBER(11)      NOT NULL ,
  SOFT_DELETE VARCHAR2(1)
);
ALTER TABLE TEDFA60 ADD CONSTRAINT PK_TEDFA60  PRIMARY KEY (PK_TEDFA60_ID) ;


CREATE TABLE TEDFA61 (
  PK_TEDFA61_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  USER_ID VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  STYLE_ENAME VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  STYLE_FONT VARCHAR2(255) ,
  STYLE_ECOLOR VARCHAR2(20) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDFA61 ADD CONSTRAINT PK_TEDFA61  PRIMARY KEY (PK_TEDFA61_ID) ;


CREATE TABLE TEDMDM2 (
  ACCSET_NO NUMBER(11,0) NOT NULL ,
  SEQ_TYPE_ID VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  SEQ_SUBSECS NUMBER(2,0) NOT NULL ,
  DATE_CYCLE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  SEQ_LMT NUMBER(2,0) NOT NULL ,
  SUBID_LMT_LEN NUMBER(3,0) NOT NULL ,
  REMARK VARCHAR2(512)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  IS_LINKED NUMBER(1,0) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDMDM2.ACCSET_NO IS '帐套序号';
COMMENT ON COLUMN TEDMDM2.SEQ_TYPE_ID IS '序列号类型ID';
COMMENT ON COLUMN TEDMDM2.SEQ_SUBSECS IS '序列号分段数';
COMMENT ON COLUMN TEDMDM2.DATE_CYCLE IS '日期循环级别';
COMMENT ON COLUMN TEDMDM2.SEQ_LMT IS '序列号长度限制';
COMMENT ON COLUMN TEDMDM2.SUBID_LMT_LEN IS '子项长度限制';
COMMENT ON COLUMN TEDMDM2.REMARK IS '备注';
COMMENT ON COLUMN TEDMDM2.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDMDM2.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDMDM2.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDMDM2.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDMDM2.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDMDM2.IS_LINKED IS '是否连号';
COMMENT ON TABLE TEDMDM2 IS '序列号定义';
ALTER TABLE TEDMDM2 ADD CONSTRAINT PK_TEDMDM2  PRIMARY KEY (SEQ_TYPE_ID) ;


CREATE TABLE TEDMDM3 (
  SEQ_TYPE_ID VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  SUBSEC_SEQ NUMBER(2,0) NOT NULL ,
  SUBSEC_NAME VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  SUBSEC_TYPE VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  SUBSEC_CONTENT VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  SUBSEC_LEN NUMBER(2,0) NOT NULL ,
  DATE_FORMAT VARCHAR2(40)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(25)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(25)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDMDM3.SEQ_TYPE_ID IS '序列号类型ID';
COMMENT ON COLUMN TEDMDM3.SUBSEC_SEQ IS '分段序号';
COMMENT ON COLUMN TEDMDM3.SUBSEC_NAME IS '分段名称';
COMMENT ON COLUMN TEDMDM3.SUBSEC_TYPE IS '分段类型';
COMMENT ON COLUMN TEDMDM3.SUBSEC_CONTENT IS '定义内容';
COMMENT ON COLUMN TEDMDM3.SUBSEC_LEN IS '分段长度';
COMMENT ON COLUMN TEDMDM3.DATE_FORMAT IS '日期格式';
COMMENT ON COLUMN TEDMDM3.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDMDM3.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDMDM3.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDMDM3.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDMDM3.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDMDM3 IS '序列号分段信息';
ALTER TABLE TEDMDM3 ADD CONSTRAINT PK_TEDMDM3  PRIMARY KEY (SEQ_TYPE_ID, SUBSEC_SEQ) ;


CREATE TABLE TEDMDM4 (
  ACCSET_NO NUMBER(11,0) NOT NULL ,
  SEQ_TYPE_ID VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  SEQ_PREFIX VARCHAR2(25)  DEFAULT ' '    NOT NULL ,
  YEAR_MON VARCHAR2(8)  DEFAULT ' '    NOT NULL ,
  CURRENT_SEQ NUMBER(11,0) NOT NULL ,
  REC_CREATOR VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(25)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(25)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDMDM4.ACCSET_NO IS '帐套序号';
COMMENT ON COLUMN TEDMDM4.SEQ_TYPE_ID IS '序列号类型ID';
COMMENT ON COLUMN TEDMDM4.SEQ_PREFIX IS '序列号前缀';
COMMENT ON COLUMN TEDMDM4.YEAR_MON IS '年月';
COMMENT ON COLUMN TEDMDM4.CURRENT_SEQ IS '当前序列';
COMMENT ON COLUMN TEDMDM4.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDMDM4.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDMDM4.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDMDM4.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDMDM4.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDMDM4 IS '当前序列';
ALTER TABLE TEDMDM4 ADD CONSTRAINT PK_TEDMDM4  PRIMARY KEY (ACCSET_NO, SEQ_TYPE_ID, SEQ_PREFIX, YEAR_MON) ;


CREATE TABLE TEDNM01 (
  MACHINE_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  MACHINE_NAME VARCHAR2(50) ,
  MACHINE_HOST VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  MACHINE_POID VARCHAR2(50) ,
  SYSTEM_TYPE VARCHAR2(50) ,
  AUTH_TYPE VARCHAR2(50) ,
  AUTH_USERNAME VARCHAR2(50) ,
  AUTH_CERT VARCHAR2(200) ,
  AUTH_PORT VARCHAR2(50) ,
  PROTOCOL VARCHAR2(200) ,
  REC_CREATOR VARCHAR2(16) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(16) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDNM01.MACHINE_ID IS '主机标识';
COMMENT ON COLUMN TEDNM01.MACHINE_NAME IS '主机名称';
COMMENT ON COLUMN TEDNM01.MACHINE_HOST IS '主机地址';
COMMENT ON COLUMN TEDNM01.MACHINE_POID IS '机器物理标识';
COMMENT ON COLUMN TEDNM01.SYSTEM_TYPE IS '系统类型';
COMMENT ON COLUMN TEDNM01.AUTH_TYPE IS '认证类型';
COMMENT ON COLUMN TEDNM01.AUTH_USERNAME IS '认证账号';
COMMENT ON COLUMN TEDNM01.AUTH_CERT IS '认证凭证';
COMMENT ON COLUMN TEDNM01.AUTH_PORT IS '认证链接端口';
COMMENT ON COLUMN TEDNM01.PROTOCOL IS '上传协议';
COMMENT ON COLUMN TEDNM01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDNM01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDNM01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDNM01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDNM01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDNM01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDNM01 IS '主机信息';
ALTER TABLE TEDNM01 ADD CONSTRAINT PK_TEDNM01  PRIMARY KEY (MACHINE_ID) ;


CREATE TABLE TEDNM02 (
  NODE_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  NODE_CNAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  PORT VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  MACHINE_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENV VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  MM_TYPE VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  MM_PATH VARCHAR2(50) ,
  STATUS VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  SERVER_USERNAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  SERVER_PASSWORD VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  GROUP_TAG VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  MM_CONTENT VARCHAR2(1500) DEFAULT ' ' NOT NULL,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  CMPT_ENAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PACKAGE_PATH VARCHAR2(100)
);
COMMENT ON COLUMN TEDNM02.NODE_ID IS '节点标识';
COMMENT ON COLUMN TEDNM02.NODE_CNAME IS '节点名称';
COMMENT ON COLUMN TEDNM02.PORT IS 'PORT';
COMMENT ON COLUMN TEDNM02.MACHINE_ID IS '主机标识';
COMMENT ON COLUMN TEDNM02.PROJECT_ENV IS '环境';
COMMENT ON COLUMN TEDNM02.MM_TYPE IS '中间件类型';
COMMENT ON COLUMN TEDNM02.MM_PATH IS '中间件路径';
COMMENT ON COLUMN TEDNM02.STATUS IS '停用标识';
COMMENT ON COLUMN TEDNM02.SERVER_USERNAME IS '服务账号';
COMMENT ON COLUMN TEDNM02.SERVER_PASSWORD IS '账号密码';
COMMENT ON COLUMN TEDNM02.GROUP_TAG IS '分组标识';
COMMENT ON COLUMN TEDNM02.MM_CONTENT IS '中间件脚本内容';
COMMENT ON COLUMN TEDNM02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDNM02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDNM02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDNM02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDNM02.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDNM02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDNM02.CMPT_ENAME IS '组件英文名';
COMMENT ON COLUMN TEDNM02.PACKAGE_PATH IS '上传包路径';
COMMENT ON TABLE TEDNM02 IS '节点信息';
ALTER TABLE TEDNM02 ADD CONSTRAINT PK_TEDNM02  PRIMARY KEY (NODE_ID) ;


CREATE TABLE TEDNM03 (
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  CMPT_ENAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CMPT_CNAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CONTEXT_PATH VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDNM03.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDNM03.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDNM03.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDNM03.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDNM03.TENANT_ID IS '归档标记';
COMMENT ON COLUMN TEDNM03.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEDNM03 ADD CONSTRAINT PK_TEDNM03 PRIMARY KEY (CMPT_ENAME,PROJECT_ENAME) ;


CREATE TABLE TEDNM04 (
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  CMPT_ENAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CMPT_MEMBER_ID VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CMPT_MEMBER_TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDNM04.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDNM04.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDNM04.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDNM04.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDNM04.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDNM04 IS '发布信息';
ALTER TABLE TEDNM04 ADD CONSTRAINT PK_TEDNM04 PRIMARY KEY (CMPT_ENAME,CMPT_MEMBER_ID,CMPT_MEMBER_TYPE) ;


CREATE TABLE TEDNM05 (
  CMPT_ENAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CMPT_VERSION VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON TABLE TEDNM05 IS 'NGINX配置';
ALTER TABLE TEDNM05 ADD CONSTRAINT PK_TEDNM05 PRIMARY KEY (CMPT_ENAME,CMPT_VERSION) ;


CREATE TABLE TEDNM06 (
  CONFIG_ID VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  NAME VARCHAR2(50) ,
  CONFIGURE VARCHAR2(50) ,
  PATH VARCHAR2(250)
);
ALTER TABLE TEDNM06 ADD CONSTRAINT PK_TEDNM06 PRIMARY KEY (CONFIG_ID) ;


CREATE TABLE TEDPC01 (
  IMAGE_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  IMAGE_TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  IMAGE_PATH VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REMARK VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDPC01 ADD CONSTRAINT PK_TEDPC01 PRIMARY KEY (IMAGE_ID) ;


CREATE TABLE TEDPC02 (
  TAG_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  TAG_NAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDPC02 ADD CONSTRAINT PK_TEDPC02 PRIMARY KEY (TAG_ID) ;


CREATE TABLE TEDPC03 (
  IMAGE_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  TAG_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDPC03 ADD CONSTRAINT PK_TEDPC03 PRIMARY KEY (IMAGE_ID,TAG_ID) ;

CREATE TABLE TEDPC04 (
  IMAGE_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDPC04 ADD CONSTRAINT PK_TEDPC04 PRIMARY KEY (IMAGE_ID) ;


CREATE TABLE TEDPC05 (
  IMAGE_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEDPC05 ADD CONSTRAINT PK_TEDPC05 PRIMARY KEY (IMAGE_ID) ;


CREATE TABLE TEDPI01 (
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  LEADER VARCHAR2(256) DEFAULT ' ' NOT NULL,
  PROJECT_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  PROJECT_CNAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  PROJECT_DESC VARCHAR2(2000) DEFAULT ' ' NOT NULL
);
COMMENT ON COLUMN TEDPI01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDPI01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDPI01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDPI01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDPI01.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDPI01.PROJECT_CNAME IS '项目中文名';
COMMENT ON COLUMN TEDPI01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDPI01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDPI01.PROJECT_DESC IS '项目描述';
COMMENT ON TABLE TEDPI01 IS '项目信息';
ALTER TABLE TEDPI01 ADD CONSTRAINT PK_TEDPI01 PRIMARY KEY (PROJECT_ENAME) ;


CREATE TABLE TEDPI02 (
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_1 VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  MODULE_CNAME_1 VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  INDEX_SPACE_ENAME VARCHAR2(40)  DEFAULT ' '    NOT NULL ,
  TABLE_SPACE_ENAME VARCHAR2(40)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDPI02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDPI02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDPI02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDPI02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDPI02.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDPI02.MODULE_ENAME_1 IS '模块英文名称';
COMMENT ON COLUMN TEDPI02.MODULE_CNAME_1 IS '项目中文名';
COMMENT ON COLUMN TEDPI02.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDPI02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDPI02.INDEX_SPACE_ENAME IS '索引空间英文名称';
COMMENT ON COLUMN TEDPI02.TABLE_SPACE_ENAME IS '表空间英文名称';
COMMENT ON TABLE TEDPI02 IS '项目模块信息';
ALTER TABLE TEDPI02 ADD CONSTRAINT PK_TEDPI02 PRIMARY KEY (MODULE_ENAME_1) ;


CREATE TABLE TEDPI03 (
  MODULE_ENAME_2 VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  MODULE_CNAME_2 VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME_1 VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDPI03.MODULE_ENAME_2 IS '二级模块英文名';
COMMENT ON COLUMN TEDPI03.MODULE_CNAME_2 IS '二级模块';
COMMENT ON COLUMN TEDPI03.MODULE_ENAME_1 IS '一级模块英文名';
COMMENT ON COLUMN TEDPI03.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDPI03.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDPI03.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDPI03.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDPI03.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDPI03.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDPI03 IS '版本表';
ALTER TABLE TEDPI03 ADD CONSTRAINT PK_TEDPI03 PRIMARY KEY (MODULE_ENAME_1,MODULE_ENAME_2) ;


CREATE TABLE TEDPI10 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  TREE_ENAME VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  NODE_ENAME VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  NODE_CNAME VARCHAR2(80)  DEFAULT ' '    NOT NULL ,
  NODE_TYPE NUMBER(1,0) NOT NULL ,
  NODE_URL VARCHAR2(200)  DEFAULT ' '    NOT NULL ,
  NODE_SORT_ID VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  NODE_PARAM VARCHAR2(200) ,
  NODE_IMAGE_PATH VARCHAR2(200) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDPI10.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDPI10.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDPI10.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDPI10.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDPI10.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDPI10.TREE_ENAME IS '节点树英文名';
COMMENT ON COLUMN TEDPI10.NODE_ENAME IS '节点英文名';
COMMENT ON COLUMN TEDPI10.NODE_CNAME IS '节点中文名';
COMMENT ON COLUMN TEDPI10.NODE_TYPE IS '节点类型';
COMMENT ON COLUMN TEDPI10.NODE_URL IS '节点URL';
COMMENT ON COLUMN TEDPI10.NODE_SORT_ID IS '节点排序标识';
COMMENT ON COLUMN TEDPI10.NODE_PARAM IS '节点参数配置';
COMMENT ON COLUMN TEDPI10.NODE_IMAGE_PATH IS '节点图片路径';
COMMENT ON TABLE TEDPI10 IS '项目菜单节点信息';
ALTER TABLE TEDPI10 ADD CONSTRAINT PK_TEDPI10 PRIMARY KEY (TREE_ENAME, NODE_ENAME) ;


CREATE TABLE TEIIT00 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ITEM_SEQ NUMBER(9,0) NOT NULL ,
  ITEM_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  ITEM_CNAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  ITEM_GRADE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  ITEM_TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  ITEM_LEN VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  ITEM_UNIT VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  ITEM_UPPER_VALUE VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  ITEM_LOWER_VALUE VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  ITEM_DEFAULT_VALUE VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  ITEM_ALLOW_NULL VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  REMARK VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  CHECK_RESULT VARCHAR2(4000) DEFAULT ' ' NOT NULL,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEIIT00.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEIIT00.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEIIT00.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEIIT00.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEIIT00.ITEM_SEQ IS '字段索引号';
COMMENT ON COLUMN TEIIT00.ITEM_ENAME IS '字段英文名';
COMMENT ON COLUMN TEIIT00.ITEM_CNAME IS '字段中文名';
COMMENT ON COLUMN TEIIT00.ITEM_GRADE IS '字段等级';
COMMENT ON COLUMN TEIIT00.ITEM_TYPE IS '字段类型';
COMMENT ON COLUMN TEIIT00.ITEM_LEN IS '字段长度';
COMMENT ON COLUMN TEIIT00.ITEM_UNIT IS '字段单位';
COMMENT ON COLUMN TEIIT00.ITEM_UPPER_VALUE IS '字段上限值';
COMMENT ON COLUMN TEIIT00.ITEM_LOWER_VALUE IS '字段下限值';
COMMENT ON COLUMN TEIIT00.ITEM_DEFAULT_VALUE IS '字段缺省值';
COMMENT ON COLUMN TEIIT00.ITEM_ALLOW_NULL IS '字段是否可空';
COMMENT ON COLUMN TEIIT00.REMARK IS '备注';
COMMENT ON COLUMN TEIIT00.CHECK_RESULT IS '检查结果';
COMMENT ON COLUMN TEIIT00.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEIIT00.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEIIT00 IS '信息项定义';
ALTER TABLE TEIIT00 ADD CONSTRAINT PK_TEIIT00 PRIMARY KEY (ITEM_SEQ) ;


CREATE TABLE TEIIT10 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ITEM_ENAME_KEY VARCHAR2(30)  DEFAULT ' '    NOT NULL ,
  ITEM_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  ITEM_CNAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  ITEM_TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEIIT10.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEIIT10.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEIIT10.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEIIT10.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEIIT10.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEIIT10.ITEM_ENAME_KEY IS '字段英文字根';
COMMENT ON COLUMN TEIIT10.ITEM_ENAME IS '字段英文名';
COMMENT ON COLUMN TEIIT10.ITEM_CNAME IS '字段中文名';
COMMENT ON COLUMN TEIIT10.ITEM_TYPE IS '字段类型';
COMMENT ON COLUMN TEIIT10.TENANT_ID IS '租户ID';
COMMENT ON TABLE TEIIT10 IS '信息项字根定义';
ALTER TABLE TEIIT10 ADD CONSTRAINT PK_TEIIT10 PRIMARY KEY (ITEM_ENAME_KEY, ITEM_CNAME) ;


CREATE TABLE TEUDM01 (
  DIR_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  DIR_ENAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  DIR_CNAME VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PARENT_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  IS_LEAF VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  DIR_PATH VARCHAR2(512)  DEFAULT ' '    NOT NULL ,
  PROJECT_ENAME VARCHAR2(250)  DEFAULT ' '    NOT NULL ,
  MODULE_ENAME VARCHAR2(50)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(512)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(512)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  REAL_PATH VARCHAR2(512) ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEUDM01 ADD CONSTRAINT PK_TEUDM01 PRIMARY KEY (DIR_ID, PROJECT_ENAME, MODULE_ENAME) ;


CREATE TABLE TEUDM02 (
  DOC_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  DIR_ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  DOC_NAME VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  CHG_NAME VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  DOC_SIZE NUMBER(16,0) NOT NULL ,
  DOC_TAG VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(512)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(512)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  TENANT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEUDM02 ADD CONSTRAINT PK_TEUDM02 PRIMARY KEY (DOC_ID) ;


CREATE TABLE TEWPD00 (
  ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CATEGORY_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CATEGORY_NAME VARCHAR2(255) ,
  PARENT_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DESCRIPTION VARCHAR2(2048) DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  CATEGORY_TYPE VARCHAR2(10)
);
ALTER TABLE TEWPD00 ADD CONSTRAINT PK_TEWPD00 PRIMARY KEY (ID) ;


CREATE TABLE TEWPD01 (
  FORM VARCHAR2(200) ,
  PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_PROC_DEF_ID VARCHAR2(64) ,
  PROCESS_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROCESS_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  DESCRIPTION VARCHAR2(2048) DEFAULT ' '    NOT NULL ,
  CATEGORY VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DEPLOYTIME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  AUTHOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  PROCESS_VERSION VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  STATE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  MXGRAPH_XML CLOB ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  BPMN_XML CLOB
);
ALTER TABLE TEWPD01 ADD CONSTRAINT PK_TEWPD01 PRIMARY KEY (PROCESS_DEF_ID) ;


CREATE TABLE TEWPD02 (
  TRANSITION_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  TRANSITION_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  TRANSITION_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  TRANSITION_TYPE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  CONDITION_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CONDITION_CODE VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  BEGIN_NODE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  END_NODE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEWPD02 ADD CONSTRAINT PK_TEWPD02 PRIMARY KEY (TRANSITION_DEF_ID, PROCESS_DEF_ID) ;


CREATE TABLE TEWPD03 (
  NODE_KEY VARCHAR2(255) ,
  NODE_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  NODE_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  DESCRIPTION VARCHAR2(2048) DEFAULT ' '    NOT NULL ,
  ISDEFFERED NUMBER(11,0) NOT NULL ,
  NODE_TYPE VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  WORK_ADDRESS_TYPE VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  WORK_ADDRESS VARCHAR2(1024) DEFAULT ' '    NOT NULL ,
  WORKITEM_HANDLE_TYPE VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  SET_TIME_OUT VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  TIME_OUT NUMBER(11,0) NOT NULL ,
  TIME_OUT_POLICY VARCHAR2(2048) DEFAULT ' '    NOT NULL ,
  BACK_JSCRIPT CLOB ,
  SUBMIT_METHOD VARCHAR2(1048) DEFAULT ' '    NOT NULL ,
  FORE_JSCRIPT CLOB ,
  XMLS CLOB NOT NULL ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEWPD03 ADD CONSTRAINT PK_TEWPD03 PRIMARY KEY (NODE_DEF_ID, PROCESS_DEF_ID) ;


CREATE TABLE TEWPD04 (
  NODE_NAME VARCHAR2(255) ,
  NODE_KEY VARCHAR2(255) ,
  PROCESS_DEF_ID VARCHAR2(64) ,
  ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_PROCESS_DEF_ID VARCHAR2(64) ,
  NODE_ID VARCHAR2(64) ,
  VOTE_AMOUNT NUMBER(11,0) NOT NULL ,
  DECIDE_TYPE VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  VOTE_TYPE VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
ALTER TABLE TEWPD04 ADD CONSTRAINT PK_TEWPD04 PRIMARY KEY (ID) ;


CREATE TABLE TEWPD05 (
  ID VARCHAR2(36)  DEFAULT ' '    NOT NULL ,
  NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  VARVALUE VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  DESCRIPTION VARCHAR2(255)
);
ALTER TABLE TEWPD05 ADD CONSTRAINT PK_TEWPD05 PRIMARY KEY (ID) ;


CREATE TABLE TEWPD06 (
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  DEPUTY_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DELEGATE_START_DATE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DELEGATE_END_DATE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  F_REAL_ENDT VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DELEGATE_TYPE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DELEGATE_DEPT VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DELEGATE_ROLE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DELEGATER VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DELEGATER_NAME VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACCEPT_DEPT VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  ACCEPT_ROLE VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACCEPTER VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  ACCEPTER_NAME VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DELEGATE_SCOPE_TYPE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_CODE VARCHAR2(2048)  DEFAULT ' '    NOT NULL ,
  PROCESS_NODE_CODE VARCHAR2(2048)  DEFAULT ' '    NOT NULL ,
  ENABLE_STATUS VARCHAR2(48)  DEFAULT ' '    NOT NULL ,
  REMARK VARCHAR2(1024)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEWPD06 ADD CONSTRAINT PK_TEWPD06 PRIMARY KEY (DEPUTY_ID) ;


CREATE TABLE TEWPD07 (
  DEPUTY_PATH_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DEPUTY_DATE VARCHAR2(8) ,
  DELEGATE_TYPE VARCHAR2(32) ,
  DELEGATE_DEPT VARCHAR2(32) ,
  DELEGATE_ROLE VARCHAR2(64) ,
  DELEGATER VARCHAR2(32) ,
  DELEGATER_FULLNAME VARCHAR2(64) ,
  DELEGATE_SCOPE_TYPE VARCHAR2(32) ,
  ACCEPT_DEPT VARCHAR2(32) ,
  ACCEPT_ROLE VARCHAR2(64) ,
  ACCEPTER VARCHAR2(32) ,
  ACCEPTER_FULLNAME VARCHAR2(64) ,
  COMPLETE_PATH VARCHAR2(2048) DEFAULT ' '    NOT NULL ,
  DEPUTY_ID VARCHAR2(64) ,
  ENABLE_STATUS VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  DEPUTY_START_TIME VARCHAR2(17) ,
  DEPUTY_END_TIME VARCHAR2(17)
);
ALTER TABLE TEWPD07 ADD CONSTRAINT PK_TEWPD07 PRIMARY KEY (DEPUTY_PATH_ID) ;


CREATE TABLE TEWPD08 (
  DEPUTY_PATH_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  DEPUTY_PROCESS_CODE VARCHAR2(64) ,
  DEPUTY_NODE_CODE VARCHAR2(64)
);
ALTER TABLE TEWPD08 ADD CONSTRAINT PK_TEWPD08 PRIMARY KEY (DEPUTY_PATH_ID) ;


CREATE TABLE TEWPD09 (
  DEPUTY_PATH_ID VARCHAR2(64) ,
  ACCEPTED_SCOPE_TYPE VARCHAR2(32) ,
  DEPUTY_PROCESS_CODE VARCHAR2(64) ,
  DEPUTY_NODE_CODE VARCHAR2(64)
);
--TODO


CREATE TABLE TEWPD10 (
  REC_CREATOR VARCHAR2(8) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(8) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ARCHIVE_TIME VARCHAR2(17) ,
  HIS_ID VARCHAR2(64) ,
  PROCESS_INSTANCE_ID VARCHAR2(64) ,
  TASK_ID VARCHAR2(64) ,
  HIS_TYPE VARCHAR2(32) ,
  FROM_USER_ID VARCHAR2(64) ,
  FROM_USER_NAME VARCHAR2(64) ,
  TO_USER_ID VARCHAR2(64) ,
  TO_USER_NAME VARCHAR2(64) ,
  LOG_INFO VARCHAR2(2048) ,
  LOG_TIME VARCHAR2(17)
);
COMMENT ON COLUMN TEWPD10.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEWPD10.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEWPD10.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEWPD10.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEWPD10.ARCHIVE_TIME IS '归档时刻';
COMMENT ON COLUMN TEWPD10.HIS_ID IS '履历ID';
COMMENT ON COLUMN TEWPD10.PROCESS_INSTANCE_ID IS '流程实例ID';
COMMENT ON COLUMN TEWPD10.TASK_ID IS '任务ID';
COMMENT ON COLUMN TEWPD10.HIS_TYPE IS '履历类型';
COMMENT ON COLUMN TEWPD10.FROM_USER_ID IS '来源方ID';
COMMENT ON COLUMN TEWPD10.FROM_USER_NAME IS '来源方姓名';
COMMENT ON COLUMN TEWPD10.TO_USER_ID IS '接收方ID';
COMMENT ON COLUMN TEWPD10.TO_USER_NAME IS '接收方姓名';
COMMENT ON COLUMN TEWPD10.LOG_INFO IS '日志信息';
COMMENT ON COLUMN TEWPD10.LOG_TIME IS '记录时间';
COMMENT ON TABLE TEWPD10 IS '委托代理履历';
--TODO


CREATE TABLE TEWPD11 (
  REC_CREATOR VARCHAR2(8) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(8) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ARCHIVE_TIME VARCHAR2(17) ,
  MAPPER_ID VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  PROCESS_KEY VARCHAR2(64) ,
  START_APP VARCHAR2(20) ,
  START_MODULE VARCHAR2(20) ,
  START_MODULE_NAME VARCHAR2(100) ,
  NODE_FORM_ID VARCHAR2(50) ,
  APP_FORM_CODE VARCHAR2(50) ,
  REMARK VARCHAR2(500) ,
  START_BUSSTYPE VARCHAR2(50) ,
  START_BUSSTYPE_NAME VARCHAR2(200) ,
  FORM_TYPE VARCHAR2(20)
);
ALTER TABLE TEWPD11 ADD CONSTRAINT PK_TEWPD11 PRIMARY KEY (MAPPER_ID) ;


CREATE TABLE TEWPD12 (
  REC_CREATOR VARCHAR2(8) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(8) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ARCHIVE_TIME VARCHAR2(17) ,
  MAPPER_ID VARCHAR2(100)  DEFAULT ' '    NOT NULL ,
  PROCESS_KEY VARCHAR2(64) ,
  START_APP VARCHAR2(20) ,
  START_MODULE VARCHAR2(20) ,
  START_MODULE_NAME VARCHAR2(100) ,
  NODE_SERVICE_ID VARCHAR2(50) ,
  APP_SERVICE_CODE VARCHAR2(50) ,
  REMARK VARCHAR2(500) ,
  START_BUSSTYPE VARCHAR2(50) ,
  START_BUSSTYPE_NAME VARCHAR2(200) ,
  SERVICE_TYPE VARCHAR2(20)
);
ALTER TABLE TEWPD12 ADD CONSTRAINT PK_TEWPD12 PRIMARY KEY (MAPPER_ID) ;


CREATE TABLE TEWPD13 (
  REC_CREATOR VARCHAR2(8) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(8) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ARCHIVE_TIME VARCHAR2(17) ,
  USER_ID VARCHAR2(50) ,
  PROCESS_KEY VARCHAR2(64) ,
  PRIORITY NUMBER(6)
);
--TODO


CREATE TABLE TEWPD14 (
  REC_CREATOR VARCHAR2(8) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(8) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ARCHIVE_TIME VARCHAR2(17) ,
  PROCESS_KEY VARCHAR2(64) ,
  OBJECT_TYPE VARCHAR2(8) ,
  OBJECT_CODE VARCHAR2(32) ,
  OBJECT_NAME VARCHAR2(32)
);
--TODO


CREATE TABLE TEWPD15 (
  REC_CREATOR VARCHAR2(8) ,
  REC_CREATE_TIME VARCHAR2(17) ,
  REC_REVISOR VARCHAR2(8) ,
  REC_REVISE_TIME VARCHAR2(17) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ARCHIVE_TIME VARCHAR2(17) ,
  HIS_ID VARCHAR2(64) ,
  PROCESS_INSTANCE_ID VARCHAR2(64) ,
  TASK_ID VARCHAR2(64) ,
  HIS_TYPE VARCHAR2(32) ,
  FROM_USER_ID VARCHAR2(64) ,
  FROM_USER_NAME VARCHAR2(64) ,
  TO_USER_ID VARCHAR2(64) ,
  TO_USER_NAME VARCHAR2(64) ,
  LOG_INFO CLOB ,
  LOG_TIME VARCHAR2(17)
);
--TODO


CREATE TABLE TEWPG00 (
  TASK_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ASSIGNEE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  NODE_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  NODE_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  NODE_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  ASSIGNEE_FULLNAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROCESS_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROCESS_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  GROUP_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  GROUP_NAME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  CREATE_TIME VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  DUE_DATE VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  MONITOR_STATUS VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  IS_ACTIVITY VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
--TODO


CREATE TABLE TEWPI00 (
  PARENT_INSTANCE_ID VARCHAR2(64) ,
  PROCESS_VERSION VARCHAR2(10) ,
  ACT_PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_NAME VARCHAR2(1000)  DEFAULT ' '    NOT NULL ,
  STARTER VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  START_TIME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  END_TIME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DURATION NUMBER(19,0)  DEFAULT 0    NOT NULL ,
  INST_STATUS VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  END_ACTIVITY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  INST_ACTIVITY VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SYNC VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  PARENT_PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ROOT_PROCINST VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  INST_SUBJECT VARCHAR2(1000) ,
  TIMEOUT_GAP NUMBER(19,0) DEFAULT 0 NOT NULL ,
  WARN_GAP NUMBER(19,0) DEFAULT 0 NOT NULL ,
  STARTER_ORG_ID VARCHAR2(255) ,
  STARTER_ORG_NAME VARCHAR2(255) ,
  SEQUENCE_ID VARCHAR2(255) ,
  FORM VARCHAR2(255) ,
  STARTER_NAME VARCHAR2(255) ,
  BUSINESS_KEY VARCHAR2(255) ,
  EXT1 VARCHAR2(255) ,
  EXT2 VARCHAR2(255) ,
  EXT3 VARCHAR2(255) ,
  EXT4 VARCHAR2(255) ,
  EXT5 VARCHAR2(255) ,
  EXT6 VARCHAR2(255) ,
  EXT7 VARCHAR2(255) ,
  EXT8 VARCHAR2(255) ,
  EXT9 VARCHAR2(255) ,
  EXT10 VARCHAR2(255) ,
  EXT11 VARCHAR2(255) ,
  EXT12 VARCHAR2(255) ,
  EXT13 VARCHAR2(255) ,
  EXT14 VARCHAR2(255) ,
  EXT15 VARCHAR2(255) ,
  EXT16 VARCHAR2(255) ,
  EXT17 VARCHAR2(255) ,
  EXT18 VARCHAR2(255) ,
  EXT19 VARCHAR2(255) ,
  EXT20 VARCHAR2(255)
);
ALTER TABLE TEWPI00 ADD CONSTRAINT PK_TEWPI00 PRIMARY KEY (PROCESS_INSTANCE_ID) ;


CREATE TABLE HEWPI00 (
  PARENT_INSTANCE_ID VARCHAR2(64) ,
  PROCESS_VERSION VARCHAR2(10) ,
  ACT_PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_KEY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_NAME VARCHAR2(1000)  DEFAULT ' '    NOT NULL ,
  STARTER VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  START_TIME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  END_TIME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  DURATION NUMBER(19,0)  DEFAULT 0    NOT NULL ,
  INST_STATUS VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  END_ACTIVITY VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  INST_ACTIVITY VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SYNC VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  PARENT_PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ROOT_PROCINST VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  INST_SUBJECT VARCHAR2(1000) ,
  TIMEOUT_GAP NUMBER(19,0) DEFAULT 0 NOT NULL ,
  WARN_GAP NUMBER(19,0) DEFAULT 0 NOT NULL ,
  STARTER_ORG_ID VARCHAR2(255) ,
  STARTER_ORG_NAME VARCHAR2(255) ,
  SEQUENCE_ID VARCHAR2(255) ,
  FORM VARCHAR2(255) ,
  STARTER_NAME VARCHAR2(255) ,
  BUSINESS_KEY VARCHAR2(255) ,
  EXT1 VARCHAR2(255) ,
  EXT2 VARCHAR2(255) ,
  EXT3 VARCHAR2(255) ,
  EXT4 VARCHAR2(255) ,
  EXT5 VARCHAR2(255) ,
  EXT6 VARCHAR2(255) ,
  EXT7 VARCHAR2(255) ,
  EXT8 VARCHAR2(255) ,
  EXT9 VARCHAR2(255) ,
  EXT10 VARCHAR2(255) ,
  EXT11 VARCHAR2(255) ,
  EXT12 VARCHAR2(255) ,
  EXT13 VARCHAR2(255) ,
  EXT14 VARCHAR2(255) ,
  EXT15 VARCHAR2(255) ,
  EXT16 VARCHAR2(255) ,
  EXT17 VARCHAR2(255) ,
  EXT18 VARCHAR2(255) ,
  EXT19 VARCHAR2(255) ,
  EXT20 VARCHAR2(255)
);
ALTER TABLE HEWPI00 ADD CONSTRAINT PK_HEWPI00 PRIMARY KEY (PROCESS_INSTANCE_ID) ;


CREATE TABLE TEWPI01 (
  REFER_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  MASTER_PROC_INST VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SLAVE_PROC_INST VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  SLAVE_PROC_SUBJECT VARCHAR2(1000)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  SLAVE_PROC_CATEGORY VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  SLAVE_PROC_NAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  SLAVE_PROC_KEY VARCHAR2(256)
);
ALTER TABLE TEWPI01 ADD CONSTRAINT PK_TEWPI01 PRIMARY KEY (REFER_ID) ;


CREATE TABLE TEWPI02 (
  TRACE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  EXECUTION_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  START_NODE_KEY VARCHAR2(64) ,
  END_NODE_KEY VARCHAR2(64) ,
  TRANSITION_KEY VARCHAR2(255) ,
  PROCESS_DEF_ID VARCHAR2(64) ,
  OPT_TYPE VARCHAR2(32) ,
  ACT_PROCESS_DEF_ID VARCHAR2(128) ,
  EXECUTION_TIME VARCHAR2(32) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(32)
);
ALTER TABLE TEWPI02 ADD CONSTRAINT PK_TEWPI02 PRIMARY KEY (TRACE_ID) ;


CREATE TABLE TEWPT00 (
  PROCESS_INSTANCE_ID VARCHAR2(64) ,
  OPINION_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  TASK_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PARENT_TASK_ID VARCHAR2(64) ,
  START_TIME VARCHAR2(32) ,
  END_TIME VARCHAR2(32) ,
  DURATION NUMBER(19,0) ,
  COMPLETER_ID VARCHAR2(64) ,
  COMPLETER_FULLNAME VARCHAR2(254) ,
  OPINION CLOB ,
  STATE VARCHAR2(32) ,
  TASK_DEF_KEY VARCHAR2(64) ,
  TASK_NAME VARCHAR2(64) ,
  FORM VARCHAR2(255) ,
  TASK_TYPE VARCHAR2(32) ,
  COMPLETER_IP_ADDRESS VARCHAR2(30) ,
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ASSIGNEE_ID VARCHAR2(64) ,
  ASSIGNEE_FULLNAME VARCHAR2(254) ,
  CONSIGNEE_ID VARCHAR2(64) ,
  CONSIGNEE_FULLNAME VARCHAR2(254) ,
  CLAIM_STATUS VARCHAR2(20) ,
  APPROVAL_RESULT VARCHAR2(20) ,
  TIMEOUT_GAP NUMBER(19,0) DEFAULT 0 ,
  WARN_GAP NUMBER(19,0) DEFAULT 0 ,
  DEPT_CNAME VARCHAR2(100) ,
  DEPT_ENAME VARCHAR2(36) ,
  PROCESS_KEY VARCHAR2(255) ,
  OPERATION_TYPE VARCHAR2(255) ,
  HAS_AUTH VARCHAR2(8) ,
  ROLE_CNAME VARCHAR2(100) ,
  ROLE_ENAME VARCHAR2(100) ,
  EXT1 VARCHAR2(128) ,
  EXT2 VARCHAR2(128) ,
  SIGN_TYPE VARCHAR2(32) ,
  PROCESS_DEF_ID VARCHAR2(64) ,
  EXECUTION_ID VARCHAR2(64) ,
  OWNER_ID VARCHAR2(64)
);
ALTER TABLE TEWPT00 ADD CONSTRAINT PK_TEWPT00 PRIMARY KEY (OPINION_ID) ;


CREATE TABLE HEWPT00 (
  PROCESS_INSTANCE_ID VARCHAR2(64) ,
  OPINION_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_PROCESS_DEF_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  TASK_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PARENT_TASK_ID VARCHAR2(64) ,
  START_TIME VARCHAR2(32) ,
  END_TIME VARCHAR2(32) ,
  DURATION NUMBER(19,0) ,
  COMPLETER_ID VARCHAR2(64) ,
  COMPLETER_FULLNAME VARCHAR2(254) ,
  OPINION CLOB ,
  STATE VARCHAR2(32) ,
  TASK_DEF_KEY VARCHAR2(64) ,
  TASK_NAME VARCHAR2(64) ,
  FORM VARCHAR2(255) ,
  TASK_TYPE VARCHAR2(32) ,
  COMPLETER_IP_ADDRESS VARCHAR2(30) ,
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  ASSIGNEE_ID VARCHAR2(64) ,
  ASSIGNEE_FULLNAME VARCHAR2(254) ,
  CONSIGNEE_ID VARCHAR2(64) ,
  CONSIGNEE_FULLNAME VARCHAR2(254) ,
  CLAIM_STATUS VARCHAR2(20) ,
  APPROVAL_RESULT VARCHAR2(20) ,
  TIMEOUT_GAP NUMBER(19,0) DEFAULT 0 ,
  WARN_GAP NUMBER(19,0) DEFAULT 0 ,
  DEPT_CNAME VARCHAR2(100) ,
  DEPT_ENAME VARCHAR2(36) ,
  PROCESS_KEY VARCHAR2(255) ,
  OPERATION_TYPE VARCHAR2(255) ,
  HAS_AUTH VARCHAR2(8) ,
  ROLE_CNAME VARCHAR2(100) ,
  ROLE_ENAME VARCHAR2(100) ,
  EXT1 VARCHAR2(128) ,
  EXT2 VARCHAR2(128) ,
  SIGN_TYPE VARCHAR2(32) ,
  PROCESS_DEF_ID VARCHAR2(64) ,
  EXECUTION_ID VARCHAR2(64) ,
  OWNER_ID VARCHAR2(64)
);
ALTER TABLE HEWPT00 ADD CONSTRAINT PK_HEWPT00 PRIMARY KEY (OPINION_ID) ;


CREATE TABLE TEWPT01 (
  PROCESS_INST_ID VARCHAR2(64) ,
  ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_PROCESS_INST_ID VARCHAR2(64) ,
  TASK_ID VARCHAR2(64) ,
  USER_ID VARCHAR2(255) ,
  USER_NAME VARCHAR2(255) ,
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
ALTER TABLE TEWPT01 ADD CONSTRAINT PK_TEWPT01 PRIMARY KEY (ID) ;


CREATE TABLE TEWPT02 (
  ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  ACT_PROCESS_DEF_ID VARCHAR2(64) ,
  ACT_PROCESS_INST_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  NODE_NAME VARCHAR2(255) ,
  NODE_ID VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  TASK_ID VARCHAR2(64) ,
  VOTE_USER_ID VARCHAR2(1000)  DEFAULT ' '    NOT NULL ,
  VOTE_USER_NAME VARCHAR2(1000) ,
  VOTE_TIME VARCHAR2(14) ,
  IS_AGREE VARCHAR2(2) ,
  CONTENT VARCHAR2(200) ,
  SIGNNUMS NUMBER(18,0) ,
  IS_COMPLETED VARCHAR2(2) ,
  BATCH NUMBER(11,0) ,
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
ALTER TABLE TEWPT02 ADD CONSTRAINT PK_TEWPT02 PRIMARY KEY (ID) ;


CREATE TABLE TEWPT03 (
  JOB_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_INSTANCE_ID VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PROCESS_DEF_ID VARCHAR2(64) ,
  PROCESS_KEY VARCHAR2(128) ,
  PROCESS_NAME VARCHAR2(256) ,
  NODE_KEY VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  NODE_NAME VARCHAR2(256) ,
  STATE VARCHAR2(20) ,
  REC_CREATE_TIME VARCHAR2(64) ,
  REC_CREATOR VARCHAR2(64) ,
  REC_REVISE_TIME VARCHAR2(64) ,
  REC_REVISOR VARCHAR2(64) ,
  FAILURE_REASON VARCHAR2(400) ,
  REMARK VARCHAR2(255)
);
ALTER TABLE TEWPT03 ADD CONSTRAINT PK_TEWPT03 PRIMARY KEY (JOB_ID, PROCESS_INSTANCE_ID, NODE_KEY) ;

CREATE TABLE TEWPR00 (
    RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
    RULE_PARAM_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
    RULE_PARAM_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL,
    RULE_PARAM_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL,
    REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
);
COMMENT ON TABLE TEWPR00 IS '规则参数表';
COMMENT ON COLUMN TEWPR00.RULE_KEY IS '规则编码';
COMMENT ON COLUMN TEWPR00.RULE_PARAM_ENAME IS '规则参数英文名';
COMMENT ON COLUMN TEWPR00.RULE_PARAM_CNAME IS '规则参数中文名';
COMMENT ON COLUMN TEWPR00.RULE_PARAM_TYPE IS '规则参数类型';
COMMENT ON COLUMN TEWPR00.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEWPR00.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEWPR00.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEWPR00.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEWPR00.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEWPR00 ADD CONSTRAINT PK_TEWPR00 PRIMARY KEY (RULE_KEY, RULE_PARAM_ENAME, RULE_PARAM_TYPE) ;

CREATE TABLE TEWPR01 (
    PROCESS_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
    PROCESS_RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
    PROCESS_RULE_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL,
    PROCESS_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
    PROCESS_RULE_STATUS VARCHAR(1) DEFAULT ' ' NOT NULL,
    PROCESS_RULE_REMARK VARCHAR(4000) DEFAULT ' ' NOT NULL,
    REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
);
COMMENT ON TABLE TEWPR01 IS '流程定义规则表';
COMMENT ON COLUMN TEWPR01.PROCESS_RULE_ID IS '流程定义规则ID';
COMMENT ON COLUMN TEWPR01.PROCESS_RULE_KEY IS '流程定义规则编码';
COMMENT ON COLUMN TEWPR01.PROCESS_RULE_EXPRESSION IS '规则表达式';
COMMENT ON COLUMN TEWPR01.PROCESS_KEY IS '流程定义编码';
COMMENT ON COLUMN TEWPR01.PROCESS_RULE_STATUS IS '规则状态';
COMMENT ON COLUMN TEWPR01.PROCESS_RULE_REMARK IS '备注';
COMMENT ON COLUMN TEWPR01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEWPR01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEWPR01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEWPR01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEWPR01.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEWPR01 ADD CONSTRAINT PK_TEWPR01 PRIMARY KEY (PROCESS_RULE_ID) ;

CREATE TABLE TEWPR02 (
    BRANCH_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
    BRANCH_RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
    BRANCH_RULE_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL,
    BRANCH_RULE_RESULT VARCHAR(255) DEFAULT ' ' NOT NULL,
    BRANCH_RULE_STATUS VARCHAR(1) DEFAULT ' ' NOT NULL,
    BRANCH_RULE_REMARK VARCHAR(4000) DEFAULT ' ' NOT NULL,
    REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
);
COMMENT ON TABLE TEWPR02 IS '分支条件规则表';
COMMENT ON COLUMN TEWPR02.BRANCH_RULE_ID IS '分支条件规则ID';
COMMENT ON COLUMN TEWPR02.BRANCH_RULE_KEY IS '分支条件规则编码';
COMMENT ON COLUMN TEWPR02.BRANCH_RULE_EXPRESSION IS '规则表达式';
COMMENT ON COLUMN TEWPR02.BRANCH_RULE_RESULT IS '规则条件';
COMMENT ON COLUMN TEWPR02.BRANCH_RULE_STATUS IS '规则状态';
COMMENT ON COLUMN TEWPR02.BRANCH_RULE_REMARK IS '备注';
COMMENT ON COLUMN TEWPR02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEWPR02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEWPR02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEWPR02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEWPR02.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEWPR02 ADD CONSTRAINT PK_TEWPR02 PRIMARY KEY (BRANCH_RULE_ID) ;

CREATE TABLE TEWPR03 (
    PARTICIPANT_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
    PARTICIPANT_RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
    PARTICIPANT_RULE_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL,
    PARTICIPANT_RULE_STATUS VARCHAR(1) DEFAULT ' ' NOT NULL,
    PARTICIPANT_RULE_REMARK VARCHAR(4000) DEFAULT ' ' NOT NULL,
    REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
);
COMMENT ON TABLE TEWPR03 IS '参与者规则表';
COMMENT ON COLUMN TEWPR03.PARTICIPANT_RULE_ID IS '参与者规则ID';
COMMENT ON COLUMN TEWPR03.PARTICIPANT_RULE_KEY IS '参与者规则编码';
COMMENT ON COLUMN TEWPR03.PARTICIPANT_RULE_EXPRESSION IS '规则表达式';
COMMENT ON COLUMN TEWPR03.PARTICIPANT_RULE_STATUS IS '规则状态';
COMMENT ON COLUMN TEWPR03.PARTICIPANT_RULE_REMARK IS '备注';
COMMENT ON COLUMN TEWPR03.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEWPR03.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEWPR03.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEWPR03.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEWPR03.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEWPR03 ADD CONSTRAINT PK_TEWPR03 PRIMARY KEY (PARTICIPANT_RULE_ID) ;

CREATE TABLE TEWPR04 (
    PARTICIPANT_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
    PARTICIPANT_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
    PARTICIPANT_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL,
    PARTICIPANT_CNAME VARCHAR(32) DEFAULT ' ' NOT NULL,
    PARTICIPANT_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL,
    REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
    REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
);
COMMENT ON TABLE TEWPR04 IS '参与者明细表';
COMMENT ON COLUMN TEWPR04.PARTICIPANT_RULE_ID IS '参与者规则ID';
COMMENT ON COLUMN TEWPR04.PARTICIPANT_ID IS '参与者ID';
COMMENT ON COLUMN TEWPR04.PARTICIPANT_ENAME IS '参与者英文名';
COMMENT ON COLUMN TEWPR04.PARTICIPANT_CNAME IS '参与者中文名';
COMMENT ON COLUMN TEWPR04.PARTICIPANT_TYPE IS '参与者类型';
COMMENT ON COLUMN TEWPR04.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEWPR04.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEWPR04.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEWPR04.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEWPR04.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEWPR04 ADD CONSTRAINT PK_TEWPR04 PRIMARY KEY (PARTICIPANT_RULE_ID, PARTICIPANT_ID) ;

CREATE TABLE XS_AUTHORIZATION (
  SUBJECT_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  SUBJECT_TYPE VARCHAR2(16) ,
  OBJECT_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  OBJECT_TYPE VARCHAR2(16) ,
  OPERATION_TYPE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  SORT_INDEX NUMBER(11)
);
COMMENT ON COLUMN XS_AUTHORIZATION.SUBJECT_ID IS '授权主体ID';
COMMENT ON COLUMN XS_AUTHORIZATION.SUBJECT_TYPE IS '授权主体类别';
COMMENT ON COLUMN XS_AUTHORIZATION.OBJECT_ID IS '授权客体ID';
COMMENT ON COLUMN XS_AUTHORIZATION.OBJECT_TYPE IS '授权客体类别';
COMMENT ON COLUMN XS_AUTHORIZATION.OPERATION_TYPE IS '操作类型:访问:OPT_ACESS,管理:OPT_MANAGE';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_AUTHORIZATION.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_AUTHORIZATION.SORT_INDEX IS '排序';
ALTER TABLE XS_AUTHORIZATION ADD CONSTRAINT PK_XS_AUTHORIZATION PRIMARY KEY (SUBJECT_ID, OBJECT_ID, OPERATION_TYPE) ;


CREATE TABLE XS_DATAS_AUTHORIZATION (
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  AUTH_RESOURCE_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  AUTH_GROUP_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  AUTH_GROUP_TYPE VARCHAR2(32) ,
  SQL_ID VARCHAR2(20),
  AUTH_OBJECT_ID VARCHAR(32) DEFAULT ' ' NOT NULL,
  AUTH_OBJECT_TYPE VARCHAR(32) DEFAULT ' ' NOT NULL
);
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.AUTH_RESOURCE_ID IS '授权资源标识';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.AUTH_GROUP_ID IS '授权集合标识';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.AUTH_GROUP_TYPE IS '授权集合类型';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.SQL_ID IS 'SQL语句的标识';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.AUTH_OBJECT_ID IS '资源标识';
COMMENT ON COLUMN XS_DATAS_AUTHORIZATION.AUTH_OBJECT_TYPE IS '资源标识类型';
ALTER TABLE XS_DATAS_AUTHORIZATION ADD CONSTRAINT PK_XS_DATAS_AUTHORIZATION PRIMARY KEY (AUTH_RESOURCE_ID,AUTH_GROUP_ID, AUTH_OBJECT_ID) ;


CREATE TABLE XS_DATAS_SQL (
  REC_CREATOR VARCHAR2(255) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(255) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  SQL_ID VARCHAR2(20)  DEFAULT ' '    NOT NULL ,
  SQL_TEXT VARCHAR2(1000)
);
COMMENT ON COLUMN XS_DATAS_SQL.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_DATAS_SQL.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_DATAS_SQL.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_DATAS_SQL.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_DATAS_SQL.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_DATAS_SQL.SQL_ID IS 'SQL语句的标识';
COMMENT ON COLUMN XS_DATAS_SQL.SQL_TEXT IS 'SQL语句的正文';
ALTER TABLE XS_DATAS_SQL ADD CONSTRAINT PK_XS_DATAS_SQL PRIMARY KEY (SQL_ID) ;


CREATE TABLE XS_RESOURCE (
  ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  RESOURCE_ENAME VARCHAR2(128) ,
  RESOURCE_CNAME VARCHAR2(256) ,
  TYPE VARCHAR2(16) ,
  IS_AUTH VARCHAR2(2) ,
  SORT_INDEX NUMBER(11) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  TENANT_ID VARCHAR2(64)
);
COMMENT ON COLUMN XS_RESOURCE.ID IS '资源ID';
COMMENT ON COLUMN XS_RESOURCE.RESOURCE_ENAME IS '资源英文名';
COMMENT ON COLUMN XS_RESOURCE.RESOURCE_CNAME IS '资源中文名';
COMMENT ON COLUMN XS_RESOURCE.TYPE IS 'PAGE:页面，BUTTON:按钮，AREA页面区域，URL:URL';
COMMENT ON COLUMN XS_RESOURCE.IS_AUTH IS '是否授权(1:授权，-1:不授权)默认:-1';
COMMENT ON COLUMN XS_RESOURCE.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_RESOURCE.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_RESOURCE.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_RESOURCE.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_RESOURCE.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_RESOURCE.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_RESOURCE ADD CONSTRAINT PK_XS_RESOURCE PRIMARY KEY (ID) ;


CREATE TABLE XS_RESOURCE_GROUP (
  ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  RESOURCE_GROUP_ENAME VARCHAR2(32) ,
  RESOURCE_GROUP_CNAME VARCHAR2(128) ,
  RESOURCE_GROUP_TYPE VARCHAR2(16) ,
  SORT_INDEX NUMBER(11) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
COMMENT ON COLUMN XS_RESOURCE_GROUP.ID IS '资源组ID';
COMMENT ON COLUMN XS_RESOURCE_GROUP.RESOURCE_GROUP_ENAME IS '资源分组英文名';
COMMENT ON COLUMN XS_RESOURCE_GROUP.RESOURCE_GROUP_CNAME IS '资源组中文名';
COMMENT ON COLUMN XS_RESOURCE_GROUP.RESOURCE_GROUP_TYPE IS '类别:资源组,模块';
COMMENT ON COLUMN XS_RESOURCE_GROUP.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_RESOURCE_GROUP ADD CONSTRAINT PK_XS_RESOURCE_GROUP PRIMARY KEY (ID) ;


CREATE TABLE XS_RESOURCE_GROUP_MEMBER (
  RESOURCE_MEMBER_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  RESOURCE_PARENT_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  MEMBER_TYPE VARCHAR2(16) ,
  PATH VARCHAR2(255) ,
  SORT_INDEX NUMBER(11) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.RESOURCE_MEMBER_ID IS '资源组成员ID';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.RESOURCE_PARENT_ID IS '资源组父节点英文名';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.MEMBER_TYPE IS '资源体类别，0:资源组,1:资源';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.PATH IS '来源';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER ADD CONSTRAINT PK_XS_RESOURCE_GROUP_MEMBER PRIMARY KEY (RESOURCE_MEMBER_ID, RESOURCE_PARENT_ID) ;


CREATE TABLE XS_USER (
  USER_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  LOGIN_NAME VARCHAR2(64)  DEFAULT ' '    NOT NULL ,
  PASSWORD VARCHAR2(255)  DEFAULT ' '    NOT NULL ,
  STATUS VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  USER_NAME VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  GENDER VARCHAR2(2) DEFAULT '1' NOT NULL,
  MOBILE VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  EMAIL VARCHAR2(128)  DEFAULT ' '    NOT NULL ,
  USER_TYPE VARCHAR2(16)  DEFAULT ' '    NOT NULL ,
  ACCOUNT_EXPIRE_DATE VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  PWD_EXPIRE_DATE VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  IS_LOCKED VARCHAR2(2)  DEFAULT ' '    NOT NULL ,
  SORT_INDEX NUMBER(11) ,
  REC_CREATOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  PWD_REVISE_DATE VARCHAR2(14) ,
  PWD_REVISOR VARCHAR2(32) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  USER_GROUP_ENAME VARCHAR2(32),
  JOB_ID  VARCHAR2(16)  default ' ',
  EHR_ORG_ID VARCHAR2(16)  default ' ',
  JOB_NAME VARCHAR2(256)  default ' '
);
COMMENT ON COLUMN XS_USER.USER_ID IS '用户ID';
COMMENT ON COLUMN XS_USER.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_USER.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER.PWD_REVISE_DATE IS '密码修改时间';
COMMENT ON COLUMN XS_USER.PWD_REVISOR IS '密码修改人';
COMMENT ON COLUMN XS_USER.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_USER.USER_GROUP_ENAME IS '用户组';
COMMENT ON COLUMN XS_USER.JOB_ID IS '用户岗位号';
COMMENT ON COLUMN XS_USER.JOB_NAME IS '用户岗位名';
COMMENT ON COLUMN XS_USER.EHR_ORG_ID IS 'EHR组织机构代码';
ALTER TABLE XS_USER ADD CONSTRAINT PK_XS_USER PRIMARY KEY (USER_ID) ;


CREATE TABLE XS_USER_EXT (
  ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  USER_ID VARCHAR2(32) ,
  FIELD_ID VARCHAR2(128) ,
  VALUE VARCHAR2(128) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
COMMENT ON COLUMN XS_USER_EXT.ID IS 'ID';
COMMENT ON COLUMN XS_USER_EXT.USER_ID IS '用户ID';
COMMENT ON COLUMN XS_USER_EXT.FIELD_ID IS '属性ID';
COMMENT ON COLUMN XS_USER_EXT.VALUE IS '属性值';
COMMENT ON COLUMN XS_USER_EXT.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_USER_EXT.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER_EXT.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER_EXT.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER_EXT.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_USER_EXT ADD CONSTRAINT PK_XS_USER_EXT PRIMARY KEY (ID) ;


CREATE TABLE XS_USER_GROUP (
  ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  GROUP_ENAME VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  GROUP_CNAME VARCHAR2(128) ,
  GROUP_TYPE VARCHAR2(32) ,
  SORT_INDEX NUMBER(11) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1) ,
  MANAGE_GROUP_ENAME VARCHAR2(32)
);
COMMENT ON COLUMN XS_USER_GROUP.ID IS '用户群组ID';
COMMENT ON COLUMN XS_USER_GROUP.GROUP_CNAME IS '群组中文名';
COMMENT ON COLUMN XS_USER_GROUP.GROUP_TYPE IS '群组类型';
COMMENT ON COLUMN XS_USER_GROUP.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_USER_GROUP.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_USER_GROUP.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER_GROUP.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER_GROUP.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER_GROUP.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_USER_GROUP.MANAGE_GROUP_ENAME IS '管辖组';
ALTER TABLE XS_USER_GROUP ADD CONSTRAINT PK_XS_USER_GROUP PRIMARY KEY (ID) ;


CREATE TABLE XS_USER_GROUP_MEMBER (
  MEMBER_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  PARENT_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  MEMBER_TYPE VARCHAR2(16) ,
  SORT_INDEX NUMBER(11) ,
  PATH VARCHAR2(255) ,
  REC_CREATOR VARCHAR2(32) ,
  REC_CREATE_TIME VARCHAR2(14) ,
  REC_REVISOR VARCHAR2(32) ,
  REC_REVISE_TIME VARCHAR2(14) ,
  ARCHIVE_FLAG VARCHAR2(1)
);
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.MEMBER_ID IS '成员ID';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.PARENT_ID IS '父节点ID';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.MEMBER_TYPE IS '授权类别:USER,GROUP';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.PATH IS '来源';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_USER_GROUP_MEMBER ADD CONSTRAINT PK_XS_USER_GROUP_MEMBER PRIMARY KEY (PARENT_ID, MEMBER_ID) ;

CREATE TABLE TXSOG01 (
  ORG_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  ORG_ENAME VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ORG_CNAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  ORG_BRIEF_NAME VARCHAR2(256)  DEFAULT ' '    NOT NULL ,
  ORG_TYPE VARCHAR2(10)  DEFAULT ' '    NOT NULL ,
  PARENT_ORG_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  ESTABLISH_DATE VARCHAR2(14)  DEFAULT ' '    NOT NULL ,
  ORG_LEVEL VARCHAR2(4)  DEFAULT ' '    NOT NULL ,
  ORG_NODE_TYPE VARCHAR2(1)  DEFAULT ' '    NOT NULL ,
  SORT_INDEX NUMBER(10) DEFAULT 0  NOT NULL   ,
  REC_CREATOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL,
  IS_DELETED VARCHAR2(1) DEFAULT '' NOT NULL
);
COMMENT ON COLUMN TXSOG01.ORG_ID IS '组织ID';
COMMENT ON COLUMN TXSOG01.ORG_ENAME IS '组织编码';
COMMENT ON COLUMN TXSOG01.ORG_CNAME IS '组织名称';
COMMENT ON COLUMN TXSOG01.ORG_BRIEF_NAME IS '组织别名';
COMMENT ON COLUMN TXSOG01.ORG_TYPE IS '组织类型';
COMMENT ON COLUMN TXSOG01.PARENT_ORG_ID IS '上级组织ID';
COMMENT ON COLUMN TXSOG01.ESTABLISH_DATE IS '成立日期';
COMMENT ON COLUMN TXSOG01.ORG_LEVEL IS '组织级别';
COMMENT ON COLUMN TXSOG01.ORG_NODE_TYPE IS '结点类型(1-树结点|2-叶子结点)';
COMMENT ON COLUMN TXSOG01.SORT_INDEX IS '排序';
COMMENT ON COLUMN TXSOG01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TXSOG01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TXSOG01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TXSOG01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TXSOG01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TXSOG01.IS_DELETED IS '逻辑删除(1-已删除|0-正常状态)';
COMMENT ON TABLE TXSOG01 IS '组织机构表';
ALTER TABLE TXSOG01 ADD CONSTRAINT PK_TXSOG01  PRIMARY KEY (ORG_ID) ;

CREATE TABLE TXSOG02 (
  ORG_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  USER_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL,
  DEFAULT_RELATION VARCHAR2(32) DEFAULT ' ' NOT NULL
);
COMMENT ON COLUMN TXSOG02.ORG_ID IS '组织ID';
COMMENT ON COLUMN TXSOG02.USER_ID IS '用户ID';
COMMENT ON COLUMN TXSOG02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TXSOG02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TXSOG02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TXSOG02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TXSOG02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TXSOG02.DEFAULT_RELATION IS '属性组织与用户的默认关系(1 代表默认关系)';
COMMENT ON TABLE TXSOG02 IS '组织机构与人员表';
ALTER TABLE TXSOG02 ADD CONSTRAINT PK_TXSOG02  PRIMARY KEY (ORG_ID,USER_ID) ;

CREATE TABLE TXSOG03 (
  ORG_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  USER_GROUP_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  REC_REVISOR VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(17)  DEFAULT ' '    NOT NULL ,
  ARCHIVE_FLAG VARCHAR2(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TXSOG03.ORG_ID IS '组织ID';
COMMENT ON COLUMN TXSOG03.USER_GROUP_ID IS '用户组ID';
COMMENT ON COLUMN TXSOG03.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TXSOG03.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TXSOG03.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TXSOG03.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TXSOG03.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TXSOG03 IS '组织机构与用户组表';
ALTER TABLE TXSOG03 ADD CONSTRAINT PK_TXSOG03  PRIMARY KEY (ORG_ID,USER_GROUP_ID) ;

CREATE TABLE TXSOG0101
(
  ID              VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  ORG_ID         VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  PARENT_ORG_ID VARCHAR2(32)  DEFAULT ' '    NOT NULL ,
  FIELD_ID        VARCHAR2(128) DEFAULT ' '    NOT NULL ,
  VALUE           VARCHAR2(128) DEFAULT ' '    NOT NULL ,
  DEFAULT_TYPE		VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  REC_CREATOR     VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14) DEFAULT ' '    NOT NULL ,
  REC_REVISOR     VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14) DEFAULT ' '    NOT NULL ,
  REC_FLAG        VARCHAR2(1) DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN "TXSOG0101"."ID" IS 'ID';
COMMENT ON COLUMN "TXSOG0101"."ORG_ID" IS '组织ID';
COMMENT ON COLUMN "TXSOG0101"."PARENT_ORG_ID" IS '父组织ID';
COMMENT ON COLUMN "TXSOG0101"."FIELD_ID" IS '属性ID';
COMMENT ON COLUMN "TXSOG0101"."VALUE" IS '属性值';
COMMENT ON COLUMN "TXSOG0101"."DEFAULT_TYPE" IS '默认属性标记(1 代表默认属性)';
COMMENT ON COLUMN "TXSOG0101"."REC_CREATOR" IS '创建人';
COMMENT ON COLUMN "TXSOG0101"."REC_CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "TXSOG0101"."REC_REVISOR" IS '修改人';
COMMENT ON COLUMN "TXSOG0101"."REC_REVISE_TIME" IS '修改时间';
COMMENT ON COLUMN "TXSOG0101"."REC_FLAG" IS '归档标记';
COMMENT ON TABLE TXSOG0101 IS '组织机构属性扩展表';
ALTER TABLE TXSOG0101 ADD CONSTRAINT PK_TXSOG0101  PRIMARY KEY (ID) ;

CREATE TABLE TXSOG07
(
  ID              VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  ORG_ID         VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  COMPANY_ENAME        VARCHAR2(128) DEFAULT ' '    NOT NULL ,
  COMPANY_CNAME        VARCHAR2(128) DEFAULT ' '    NOT NULL ,
  SOB_ENAME        VARCHAR2(128) DEFAULT ' '    NOT NULL ,
  SOB_CNAME        VARCHAR2(128) DEFAULT ' '    NOT NULL ,
  REC_CREATOR     VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  REC_CREATE_TIME VARCHAR2(14) DEFAULT ' '    NOT NULL ,
  REC_REVISOR     VARCHAR2(32) DEFAULT ' '    NOT NULL ,
  REC_REVISE_TIME VARCHAR2(14) DEFAULT ' '    NOT NULL ,
  REC_FLAG        VARCHAR2(1) DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN "TXSOG07"."ID" IS 'ID';
COMMENT ON COLUMN "TXSOG07"."ORG_ID" IS '组织ID';
COMMENT ON COLUMN "TXSOG07"."COMPANY_ENAME" IS '公司编码';
COMMENT ON COLUMN "TXSOG07"."COMPANY_CNAME" IS '公司中文名';
COMMENT ON COLUMN "TXSOG07"."SOB_ENAME" IS '账套编码';
COMMENT ON COLUMN "TXSOG07"."SOB_CNAME" IS '账套中文名';
COMMENT ON COLUMN "TXSOG07"."REC_CREATOR" IS '创建人';
COMMENT ON COLUMN "TXSOG07"."REC_CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "TXSOG07"."REC_REVISOR" IS '修改人';
COMMENT ON COLUMN "TXSOG07"."REC_REVISE_TIME" IS '修改时间';
COMMENT ON COLUMN "TXSOG07"."REC_FLAG" IS '归档标记';
COMMENT ON TABLE TXSOG07 IS '公司别和账套表';
ALTER TABLE TXSOG07 ADD CONSTRAINT PK_TXSOG07  PRIMARY KEY (ID) ;

create table TXSLV01
(
  REC_CREATOR                 VARCHAR2(256) default ' ' not null,
  REC_CREATE_TIME             VARCHAR2(14)  default ' ' not null,
  REC_REVISOR                 VARCHAR2(256) default ' ' not null,
  REC_REVISE_TIME             VARCHAR2(14)  default ' ' not null,
  ARCHIVE_FLAG                VARCHAR2(1)   default ' ' not null,
  ORG_ID                      VARCHAR2(32)  default ' ' not null,
  ORG_ADM_ID                  VARCHAR2(32)  default ' ' not null,
  ORG_PERM_MANAGER            VARCHAR2(1)   default '0' not null,
  ORG_PERM_RES_RANGE          VARCHAR2(1)   default '0' not null,
  ORG_PERM_ORG_MAPPING        VARCHAR2(1)   default '0' not null,
  ORG_PERM_USER_GROUP         VARCHAR2(1)   default '0' not null,
  ORG_PERM_USER_GROUP_MEMBRER VARCHAR2(1)   default '0' not null,
  ORG_PERM_AUTH               VARCHAR2(1)   default '0' not null,
  ORG_PERM_ORG                VARCHAR2(1)   default '0' not null,
  TENANT_ID                   VARCHAR2(32)  default ' ' not null,
  primary key (ORG_ID, ORG_ADM_ID)
);
comment on table TXSLV01 is '分级管理员权限';
comment on column TXSLV01.REC_CREATOR is '创建人';
comment on column TXSLV01.REC_CREATE_TIME is '创建时间';
comment on column TXSLV01.REC_REVISOR is '修改人';
comment on column TXSLV01.REC_REVISE_TIME is '修改时间';
comment on column TXSLV01.ARCHIVE_FLAG is '归档标记';
comment on column TXSLV01.ORG_ID is '组织机构ID';
comment on column TXSLV01.ORG_ADM_ID is '分级管理员ID';
comment on column TXSLV01.ORG_PERM_MANAGER is '维护分级管理员权限';
comment on column TXSLV01.ORG_PERM_RES_RANGE is '分级资源授权范围管理权限';
comment on column TXSLV01.ORG_PERM_ORG_MAPPING is '组织机构映射权限';
comment on column TXSLV01.ORG_PERM_USER_GROUP is '用户组维护权限';
comment on column TXSLV01.ORG_PERM_USER_GROUP_MEMBRER is '用户组成员维护权限';
comment on column TXSLV01.ORG_PERM_AUTH is '授权关系维护权限';
comment on column TXSLV01.ORG_PERM_ORG is '组织机构维护权限';
comment on column TXSLV01.TENANT_ID is '租户ID';

create table TXSLV02
(
  REC_CREATOR     VARCHAR2(256) default ' ' not null,
  REC_CREATE_TIME VARCHAR2(14)  default ' ' not null,
  REC_REVISOR     VARCHAR2(256) default ' ' not null,
  REC_REVISE_TIME VARCHAR2(14)  default ' ' not null,
  ARCHIVE_FLAG    VARCHAR2(1)   default ' ' not null,
  ORG_ID          VARCHAR2(32)  default ' ' not null,
  OBJECT_ID       VARCHAR2(32)  default ' ' not null,
  OBJECT_TYPE     VARCHAR2(16)  default ' ' not null,
  TENANT_ID       VARCHAR2(32)  default ' ' not null,
  primary key (ORG_ID, OBJECT_ID)
);
comment on table TXSLV02 is '分级资源范围表';
comment on column TXSLV02.REC_CREATOR is '创建人';
comment on column TXSLV02.REC_CREATE_TIME is '创建时间';
comment on column TXSLV02.REC_REVISOR is '修改人';
comment on column TXSLV02.REC_REVISE_TIME is '修改时间';
comment on column TXSLV02.ARCHIVE_FLAG is '归档标记';
comment on column TXSLV02.ORG_ID is '组织机构ID';
comment on column TXSLV02.OBJECT_ID is '授权客体ID';
comment on column TXSLV02.OBJECT_TYPE is '授权客体类型';
comment on column TXSLV02.TENANT_ID is '租户ID';

create table TXSLV03
(
  ORG_ID          VARCHAR2(32)             not null,
  HROG_CODE       VARCHAR2(32)             not null,
  REC_CREATOR     VARCHAR2(16) default ' ' not null,
  REC_CREATE_TIME VARCHAR2(17) default ' ' not null,
  REC_REVISOR     VARCHAR2(16) default ' ' not null,
  REC_REVISE_TIME VARCHAR2(17) default ' ' not null,
  TENANT_ID       VARCHAR2(64) default ' ' not null,
  ARCHIVE_FLAG    VARCHAR2(1)  default ' ' not null,
  primary key (ORG_ID, HROG_CODE)
);
comment on table TXSLV03 is '分级授权组织机构映射关系表';
comment on column TXSLV03.ORG_ID is '组织机构ID';
comment on column TXSLV03.HROG_CODE is '映射EHR组织机构代码';
comment on column TXSLV03.REC_CREATOR is '记录创建责任者';
comment on column TXSLV03.REC_CREATE_TIME is '记录创建时刻';
comment on column TXSLV03.REC_REVISOR is '记录修改责任者';
comment on column TXSLV03.REC_REVISE_TIME is '记录修改时刻';
comment on column TXSLV03.TENANT_ID is '租户ID';
comment on column TXSLV03.ARCHIVE_FLAG is '归档标记';



create table XS_POST
(
  POST_ID         VARCHAR2(32)              not null,
  POST_ENAME      VARCHAR2(32)  default ' ' not null,
  POST_CNAME      VARCHAR2(128) default ' ' not null,
  ORG_ID          VARCHAR2(32)  default ' ' not null,
  POST_ATTRIBUTE  VARCHAR2(10)  default ' ' not null,
  POST_PROFESSION VARCHAR2(10)  default ' ' not null,
  PHONE_NUMBER    VARCHAR2(20)  default ' ' not null,
  REC_CREATOR     VARCHAR2(32)  default ' ' not null,
  REC_CREATE_TIME VARCHAR2(14)  default ' ' not null,
  REC_REVISOR     VARCHAR2(32)  default ' ' not null,
  REC_REVISE_TIME VARCHAR2(14)  default ' ' not null,
  ARCHIVE_FLAG    VARCHAR2(1)   default ' ' not null,
  constraint XS_POST_PK primary key (POST_ID)
);
comment on table XS_POST is '岗位信息表';
comment on column XS_POST.POST_ID is '岗位编码';
comment on column XS_POST.POST_ENAME is '岗位英文名';
comment on column XS_POST.POST_CNAME is '岗位中文名';
comment on column XS_POST.ORG_ID is '组织机构编码';
comment on column XS_POST.POST_ATTRIBUTE is '岗位性质';
comment on column XS_POST.POST_PROFESSION is '专业';
comment on column XS_POST.PHONE_NUMBER is '电话号码';
comment on column XS_POST.REC_CREATOR is '创建人';
comment on column XS_POST.REC_CREATE_TIME is '创建时间';
comment on column XS_POST.REC_REVISOR is '修改人';
comment on column XS_POST.REC_REVISE_TIME is '修改时间';
comment on column XS_POST.ARCHIVE_FLAG is '归档标记';
create index INDEX_XS_POST_ORG_ID on XS_POST (ORG_ID);


create table XS_POST_USER
(
  POST_ID         VARCHAR2(32)             not null,
  USER_ID         VARCHAR2(32)             not null,
  USER_TYPE       VARCHAR2(10) default ' ' not null,
  EXPIRE_DATE     VARCHAR2(8)  default ' ' not null,
  REC_CREATOR     VARCHAR2(32) default ' ' not null,
  REC_CREATE_TIME VARCHAR2(14) default ' ' not null,
  REC_REVISOR     VARCHAR2(32) default ' ' not null,
  REC_REVISE_TIME VARCHAR2(14) default ' ' not null,
  ARCHIVE_FLAG    VARCHAR2(1)  default ' ' not null,
  constraint XS_POST_USER_PK
    primary key (POST_ID, USER_ID)
);

comment on table XS_POST_USER is '岗位用户成员表';
comment on column XS_POST_USER.POST_ID is '岗位编码';
comment on column XS_POST_USER.USER_ID is '用户编码';
comment on column XS_POST_USER.USER_TYPE is '用户类型';
comment on column XS_POST_USER.EXPIRE_DATE is '过期日期';
comment on column XS_POST_USER.REC_CREATOR is '创建人';
comment on column XS_POST_USER.REC_CREATE_TIME is '创建时间';
comment on column XS_POST_USER.REC_REVISOR is '修改人';
comment on column XS_POST_USER.REC_REVISE_TIME is '修改时间';
comment on column XS_POST_USER.ARCHIVE_FLAG is '归档标记';


--索引创建--

--配置管理--

CREATE INDEX IDX_2PC_LOG_TRACE_TRANS_ID ON ED_XM_2PC_LOG (TRACE_ID, TRANSACTION_ID);
CREATE INDEX IDX_2PC_LOG_TIME_STAMP ON ED_XM_2PC_LOG (TIME_STAMP);

--授权管理--

CREATE INDEX IDX_XS_USER_LOGIN_NAME ON XS_USER (LOGIN_NAME);
CREATE INDEX IDX_XS_USER_GROUP_ENAME ON XS_USER_GROUP (GROUP_ENAME);
--CREATE INDEX IDX_XS_AUTH_IDS_OPERATION_TYPE ON XS_AUTHORIZATION (SUBJECT_ID, OBJECT_ID, OPERATION_TYPE);
CREATE INDEX IDX_XS_USER_GROUP_MEMBER_IDS ON XS_USER_GROUP_MEMBER (MEMBER_ID, PARENT_ID);
CREATE INDEX IDX_XS_RESOURCE_RESOURCE_ENAME ON XS_RESOURCE (RESOURCE_ENAME);
CREATE INDEX IDX_XS_RESOURCE_GROUP_ENAME ON XS_RESOURCE_GROUP (RESOURCE_GROUP_ENAME);
--CREATE INDEX IDX_XS_DATAS_AUTH_RES_GROUP ON XS_DATAS_AUTHORIZATION (AUTH_RESOURCE_ID,AUTH_GROUP_ID);

--消息管理--

CREATE INDEX IDX_EM_HISTORY_TIME_SERVICE ON EM_HISTORY(OPERATE_START_TIME,OPERATE_END_TIME,EVENT_ID,SERVICE_NAME,METHOD_NAME);


--流程管理--
--CREATE INDEX IDX_TEWPD01_PROC_DEF_ID ON TEWPD01(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPD01_ACT_PROC_DEF_ID ON TEWPD01(ACT_PROC_DEF_ID);
CREATE INDEX IDX_TEWPD02_TRANSITION_DEF_ID ON TEWPD02(TRANSITION_DEF_ID);
CREATE INDEX IDX_TEWPD02_PROC_DEF_ID ON TEWPD02(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPD02_BEGIN_NODE_ID ON TEWPD02(BEGIN_NODE_ID);
CREATE INDEX IDX_TEWPD02_END_NODE_ID ON TEWPD02(END_NODE_ID);
CREATE INDEX IDX_TEWPD03_NODE_DEF_ID ON TEWPD03(NODE_DEF_ID);
CREATE INDEX IDX_TEWPD03_PROC_DEF_ID ON TEWPD03(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPD03_NODE_KEY ON TEWPD03(NODE_KEY);
--CREATE INDEX IDX_TEWPD04_ID ON TEWPD04(ID);
CREATE INDEX IDX_TEWPD04_NODE_KEY ON TEWPD04(NODE_KEY);
CREATE INDEX IDX_TEWPI00_ACT_PROC_DEF_ID ON TEWPI00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPI00_ACT_INST_ID ON TEWPI00(ACT_INSTANCE_ID);
CREATE INDEX IDX_TEWPI00_PROC_DEF_ID ON TEWPI00(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPI00_PARENT_INST_ID ON TEWPI00(PARENT_INSTANCE_ID);
CREATE INDEX IDX_HEWPI00_ACT_PROC_DEF_ID ON HEWPI00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_HEWPI00_ACT_INST_ID ON HEWPI00(ACT_INSTANCE_ID);
CREATE INDEX IDX_HEWPI00_PROC_DEF_ID ON HEWPI00(PROCESS_DEF_ID);
CREATE INDEX IDX_HEWPI00_PARENT_INST_ID ON HEWPI00(PARENT_INSTANCE_ID);
CREATE INDEX IDX_TEWPI02_PROC_INST_ID ON TEWPI02(PROCESS_INSTANCE_ID);
CREATE INDEX IDX_TEWPI02_PROC_DEF_ID ON TEWPI02(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPT00_PROC_INST_ID ON TEWPT00(PROCESS_INSTANCE_ID);
CREATE INDEX IDX_TEWPT00_TASK_ID ON TEWPT00(TASK_ID);
CREATE INDEX IDX_TEWPT00_STATE ON TEWPT00(STATE);
CREATE INDEX IDX_TEWPT00_ASSIGNEE_ID ON TEWPT00(ASSIGNEE_ID);
CREATE INDEX IDX_TEWPT00_COMPLETER_ID ON TEWPT00(COMPLETER_ID);
CREATE INDEX IDX_TEWPT00_PROC_KEY ON TEWPT00(PROCESS_KEY);
CREATE INDEX IDX_TEWPT00_ACT_PROC_DEF_ID ON TEWPT00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPT00_ACT_INSTANCE_ID ON TEWPT00(ACT_INSTANCE_ID);
CREATE INDEX IDX_TEWPT00_TASK_DEF_KEY ON TEWPT00(TASK_DEF_KEY);
CREATE INDEX IDX_HEWPT00_PROC_INST_ID ON HEWPT00(PROCESS_INSTANCE_ID);
CREATE INDEX IDX_HEWPT00_TASK_ID ON HEWPT00(TASK_ID);
CREATE INDEX IDX_HEWPT00_STATE ON HEWPT00(STATE);
CREATE INDEX IDX_HEWPT00_ASSIGNEE_ID ON HEWPT00(ASSIGNEE_ID);
CREATE INDEX IDX_HEWPT00_COMPLETER_ID ON HEWPT00(COMPLETER_ID);
CREATE INDEX IDX_HEWPT00_PROC_KEY ON HEWPT00(PROCESS_KEY);
CREATE INDEX IDX_HEWPT00_ACT_PROC_DEF_ID ON HEWPT00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_HEWPT00_ACT_INSTANCE_ID ON HEWPT00(ACT_INSTANCE_ID);
CREATE INDEX IDX_HEWPT00_TASK_DEF_KEY ON HEWPT00(TASK_DEF_KEY);
--CREATE INDEX IDX_TEWPT01_ID ON TEWPT01(ID);
CREATE INDEX IDX_TEWPT01_PROC_INST_ID ON TEWPT01(PROCESS_INST_ID);
CREATE INDEX IDX_TEWPT01_ACT_PROC_INST_ID ON TEWPT01(ACT_PROCESS_INST_ID);
CREATE INDEX IDX_TEWPT01_TASK_ID ON TEWPT01(TASK_ID);


--任务管理--

CREATE INDEX IDX_EJ_QRTZ_J_REQ_RECOVERY ON EJ_QRTZ_JOB_DETAILS(SCHED_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_EJ_QRTZ_J_GRP ON EJ_QRTZ_JOB_DETAILS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_J ON EJ_QRTZ_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_JG ON EJ_QRTZ_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_C ON EJ_QRTZ_TRIGGERS(SCHED_NAME,CALENDAR_NAME);
CREATE INDEX IDX_EJ_QRTZ_T_G ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_STATE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_N_STATE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_N_G_STATE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_NEXT_FIRE_TIME ON EJ_QRTZ_TRIGGERS(SCHED_NAME,NEXT_FIRE_TIME);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_ST ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE,NEXT_FIRE_TIME);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_MISFIRE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_ST_MISF ON EJ_QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_ST_MISF_GRP ON EJ_QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_FT_TRIG_INST ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME);
CREATE INDEX IDX_EJ_QRTZ_FT_INST_REQ_RCVRY ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_EJ_QRTZ_FT_J_G ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_JG ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_T_G ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_TG ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_LOG01 ON EJ_QRTZ_LOGGING(START_TIME);
CREATE INDEX IDX_EJ_QRTZ_FT_LOG02 ON EJ_QRTZ_LOGGING(STATUS, TRIGGER_NAME);

--消息分组

CREATE TABLE
    EM_GROUP
    (
        GROUP_NAME VARCHAR2(20) DEFAULT ' ' NOT NULL,
        GROUP_DESC VARCHAR2(250) DEFAULT ' ' NOT NULL,
        MAX_RETRY_TIMES NUMBER(4) NOT NULL,
        INTERVAL_TIME NUMBER(6) NOT NULL,
        IS_SEQUENCE  VARCHAR2(1)   DEFAULT '0' NOT NULL,
        REC_CREATOR VARCHAR2(256) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR2(14) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR2(256) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR2(14) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR2(1) DEFAULT ' ' NOT NULL,
        CONSTRAINT EM_GROUP_PK PRIMARY KEY (GROUP_NAME)
    );
COMMENT ON TABLE EM_GROUP IS '分组策略配置';
COMMENT ON COLUMN EM_GROUP.GROUP_NAME IS '分组名称';
COMMENT ON COLUMN EM_GROUP.GROUP_DESC IS '分组描述';
COMMENT ON COLUMN EM_GROUP.MAX_RETRY_TIMES IS '最大重试次数';
COMMENT ON COLUMN EM_GROUP.INTERVAL_TIME IS '重发间隔时间';
COMMENT ON COLUMN EM_GROUP.IS_SEQUENCE IS '是否时序';
COMMENT ON COLUMN EM_GROUP.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN EM_GROUP.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN EM_GROUP.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN EM_GROUP.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN EM_GROUP.ARCHIVE_FLAG IS '归档标记';

CREATE TABLE
    EM_GROUP_EVENT_RELA
    (
        GROUP_NAME VARCHAR2(20) DEFAULT ' ' NOT NULL,
        EVENT_ID VARCHAR2(128) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR2(256) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR2(14) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR2(256) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR2(14) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR2(1) DEFAULT ' ' NOT NULL,
        CONSTRAINT EM_GROUP_EVENT_RELA_PK PRIMARY KEY (EVENT_ID)
    );
create index IDX_EM_GROUP_EVENT_RELA on EM_GROUP_EVENT_RELA (GROUP_NAME);
COMMENT ON TABLE EM_GROUP_EVENT_RELA IS '分组电文表';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.GROUP_NAME IS '分组名称';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.EVENT_ID IS '电文号';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN EM_GROUP_EVENT_RELA.ARCHIVE_FLAG IS '归档标记';


create table EM_TELE_CONFIG
(
    EVENT_ID        VARCHAR(64)              not null
        primary key,
    TELE_TYPE       VARCHAR(20)  default 'text',
    REMOTE_SYS_CODE VARCHAR(20)  default ' ',
    CATEGORY        VARCHAR(16)  default '  ',
    REC_CREATOR     VARCHAR(256) default ' ' not null,
    REC_CREATE_TIME VARCHAR(14)  default ' ' not null,
    REC_REVISOR     VARCHAR(256) default ' ' not null,
    REC_REVISE_TIME VARCHAR(14)  default ' ' not null,
    ARCHIVE_FLAG    VARCHAR(1)   default ' ' not null
);

create index IDX_EM_TELE_CONFIG_TELE_TYPE
    on EM_TELE_CONFIG (TELE_TYPE);

create table TEUDM03
(
    REC_CREATOR     VARCHAR2(16)  default ' ',
    REC_REVISOR     VARCHAR2(16)  default ' ',
    REC_CREATE_TIME VARCHAR2(14)  default ' ',
    REC_REVISE_TIME VARCHAR2(14)  default ' ',
    ARCHIVE_FLAG    VARCHAR2(1)   default ' ',
    FORM_ENAME      VARCHAR2(20)                  not null,
    LOCALE_LANG     VARCHAR2(20)  default 'zh_CN' not null,
    DOC_PATH        VARCHAR2(250) default ' ',
    DOC_CNAME       VARCHAR2(200) default ' ',
    KEY_WORD        VARCHAR2(200) default ' ',
    BOOKMARK        VARCHAR2(200) default ' ',
    constraint TEUDM03_PRIMARY_KEY
        primary key (FORM_ENAME, LOCALE_LANG)
);

comment on table TEUDM03 is '帮助文档管理表';
comment on column TEUDM03.REC_CREATOR is '创建人';
comment on column TEUDM03.REC_REVISOR is '修改人';
comment on column TEUDM03.REC_CREATE_TIME is '创建时间';
comment on column TEUDM03.REC_REVISE_TIME is '修改时间';
comment on column TEUDM03.ARCHIVE_FLAG is '归档标记';
comment on column TEUDM03.FORM_ENAME is '页面英文名';
comment on column TEUDM03.LOCALE_LANG is '国际化语言';
comment on column TEUDM03.DOC_PATH is '文档路径';
comment on column TEUDM03.DOC_CNAME is '文档中文名';
comment on column TEUDM03.KEY_WORD is '关键字';
comment on column TEUDM03.BOOKMARK is '书签';

create table TEDLG00
(
    TYPE_NAME         VARCHAR2(32)  default ' ' not null,
    TYPE_DESC         VARCHAR2(255) default ' ' not null,
    REC_CREATOR       VARCHAR2(16)  default ' ' not null,
    REC_CREATE_TIME   VARCHAR2(17)  default ' ' not null,
    KEYWORD_ONE       VARCHAR2(32)  default ' ',
    KEYWORD_TWO       VARCHAR2(32)  default ' ',
    KEYWORD_THREE     VARCHAR2(32)  default ' ',
    KEYWORD_FOUR      VARCHAR2(32)  default ' ',
    KEYWORD_FIVE      VARCHAR2(32)  default ' ',
    REC_REVISOR       VARCHAR2(16)  default ' ' not null,
    REC_REVISE_TIME   VARCHAR2(17)  default ' ' not null,
    ARCHIVE_FLAG      VARCHAR2(1)   default ' ' not null,
    ARCHIVE_TYPE      VARCHAR2(1)   default '3',
    ARCHIVE_KEEP_TIME DECIMAL(5)    default 3   not null,
    constraint PK_TEDLG00
        primary key (TYPE_NAME)
);

comment on column TEDLG00.TYPE_NAME is '类别名称';
comment on column TEDLG00.TYPE_DESC is '类别描述';
comment on column TEDLG00.REC_CREATOR is '创建人';
comment on column TEDLG00.REC_CREATE_TIME is '创建时间';
comment on column TEDLG00.KEYWORD_ONE is '关键字1';
comment on column TEDLG00.KEYWORD_TWO is '关键字2';
comment on column TEDLG00.KEYWORD_THREE is '关键字3';
comment on column TEDLG00.KEYWORD_FOUR is '关键字4';
comment on column TEDLG00.KEYWORD_FIVE is '关键字5';
comment on column TEDLG00.REC_REVISOR is '修改者';
comment on column TEDLG00.REC_REVISE_TIME is '修改时间';
comment on column TEDLG00.ARCHIVE_FLAG is '归档标记';

