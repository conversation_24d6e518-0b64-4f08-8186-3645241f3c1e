package com.baosight.imom.vg.dm.domain;


import com.baosight.imom.common.utils.StrUtil;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 工序时间规则
 *
 * <AUTHOR> 郁在杰
 * @Description : 工序时间规则
 * @Date : 2025/4/9
 * @Version : 1.0
 */
public class ProcedureRule {
    /**
     * 字段名
     */
    private String field;

    /**
     * 操作符
     */
    private String operator;

    /**
     * 值
     */
    private double value;

    /**
     * 条件类型
     */
    private String conditionType;

    /**
     * 子条件列表
     */
    private List<ProcedureRule> subRules;

    /**
     * 持续时间
     */
    private int duration;

    /**
     * 是否校验
     */
    private boolean isCheck = false;

    public ProcedureRule() {
        this.subRules = new ArrayList<>();
        this.duration = 0;
        this.conditionType = null;
        this.isCheck = false;
        this.field = "";
        this.operator = "";
        this.value = 0;
    }

    /**
     * 获取字段名
     *
     * @return 字段名
     */
    public String getField() {
        return field;
    }

    /**
     * 设置字段名
     *
     * @param field 字段名
     */
    public void setField(String field) {
        this.field = field;
    }

    /**
     * 获取操作符
     *
     * @return 操作符
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置操作符
     *
     * @param operator 操作符
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 获取值
     *
     * @return 值
     */
    public double getValue() {
        return value;
    }

    /**
     * 设置值
     *
     * @param value 值
     */
    public void setValue(double value) {
        this.value = value;
    }

    /**
     * 获取条件类型
     *
     * @return 条件类型
     */
    public String getConditionType() {
        return conditionType;
    }

    /**
     * 设置条件类型
     *
     * @param conditionType 条件类型
     */
    public void setConditionType(String conditionType) {
        this.conditionType = conditionType;
    }

    /**
     * 获取子条件列表
     *
     * @return 子件列表
     */
    public List<ProcedureRule> getSubRules() {
        if (subRules == null) {
            subRules = new ArrayList<>();
        }
        return subRules;
    }

    /**
     * 设置子条件列表
     *
     * @param subRules 子条件列表
     */
    public void setSubRules(List<ProcedureRule> subRules) {
        this.subRules = subRules;
    }

    /**
     * 获取持续时间
     *
     * @return 持续时间
     */
    public int getDuration() {
        return duration;
    }

    /**
     * 设置持续时间
     *
     * @param duration 持续时间
     */
    public void setDuration(int duration) {
        this.duration = duration;
    }

    /**
     * 获取是否校验
     *
     * @return 是否校验
     */
    public boolean isCheck() {
        return isCheck;
    }

    /**
     * 设置是否校验
     *
     * @param check 是否校验
     */
    public void setCheck(boolean check) {
        isCheck = check;
    }

    /**
     * 获取field
     *
     * @return list
     */
    public Set<String> getAllField() {
        Set<String> fieldList = new HashSet<>();
        if (!StrUtil.isBlank(this.field)) {
            fieldList.add(this.field);
        }
        for (ProcedureRule procedureRule : subRules) {
            fieldList.addAll(procedureRule.getAllField());
        }
        return fieldList;
    }
}
