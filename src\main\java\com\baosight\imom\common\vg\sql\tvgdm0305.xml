<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-31 15:15:10
   		Version :  1.0
		tableName :${projectSchema}.tvgdm0305 
		 DEVICE_ID  VARCHAR   NOT NULL, 
		 DEVICE_STATUS  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  VARCHAR   NOT NULL, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL
	-->
<sqlMap namespace="tvgdm0305">

	<select id="query" parameterClass="java.util.Map"
			resultClass="com.baosight.imom.common.vg.domain.Tvgdm0305">
		SELECT
				DEVICE_ID	as "deviceId",  <!-- 设备ID -->
				DEVICE_STATUS	as "deviceStatus",  <!-- 设备状态 -->
				UUID	as "uuid",  <!-- 唯一编码 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建责任者 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时刻 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改责任者 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时刻 -->
				TENANT_ID	as "tenantId",  <!-- 租户ID -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				SEG_NO	as "segNo",  <!-- 系统帐套 -->
				UNIT_CODE	as "unitCode" <!-- 业务单元代码 -->
		FROM ${projectSchema}.tvgdm0305 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${projectSchema}.tvgdm0305 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>

	<insert id="insert">
		INSERT INTO ${projectSchema}.tvgdm0305 (DEVICE_ID,  <!-- 设备ID -->
										DEVICE_STATUS,  <!-- 设备状态 -->
										UUID,  <!-- 唯一编码 -->
										REC_CREATOR,  <!-- 记录创建责任者 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时刻 -->
										REC_REVISOR,  <!-- 记录修改责任者 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时刻 -->
										TENANT_ID,  <!-- 租户ID -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 删除标记 -->
										SEG_NO,  <!-- 系统帐套 -->
										UNIT_CODE  <!-- 业务单元代码 -->
										)		 
	    VALUES (#deviceId#, #deviceStatus#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${projectSchema}.tvgdm0305 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${projectSchema}.tvgdm0305 
		SET 
		DEVICE_ID	= #deviceId#,   <!-- 设备ID -->  
					DEVICE_STATUS	= #deviceStatus#,   <!-- 设备状态 -->  
								REC_CREATOR	= #recCreator#,   <!-- 记录创建责任者 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时刻 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改责任者 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时刻 -->  
					TENANT_ID	= #tenantId#,   <!-- 租户ID -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					SEG_NO	= #segNo#,   <!-- 系统帐套 -->  
					UNIT_CODE	= #unitCode#  <!-- 业务单元代码 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>