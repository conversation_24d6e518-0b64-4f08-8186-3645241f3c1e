$(function () {
    // TODO 查询 按钮事件
    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    $("#QUERY").on("click", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        var unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({msg: "请先选择业务单元代码!"}, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings:{
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings:{
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            }
        }
    };
    IPLATUI.EFPopupInput = {
        "inqu_status-0-customerId": {
            clearInput: function (e) {
                $("#inqu_status-0-customerId").val('');
                $("#inqu_status-0-customerName").val('');
            }
        }
    };
    IPLATUI.EFWindow = {
        "unitInfo": {
            open: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
                iframejQuery("#inqu_status-0-segName").val($("#inqu_status-0-segName").val());
                iframejQuery("#inqu_status-0-windowId").val($("unitInfo"));
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();
                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                }
            }
        },
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                var segNo=$("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)){
                    NotificationUtil({msg:"请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId").val(row[0].userNum);
                    $("#inqu_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "userNum2": {
        // 打开窗口事件
        open: function (e) {
            var $iframe = userNum2Window.element.children("iframe");
            // 子窗口中的jQuery对象
            var iframejQuery = $iframe[0].contentWindow.$;
            // 把EFWindow的id传入到子窗口input框中
            iframejQuery("#sub_query_status-0-windowId").val("userNum2");
            var segNo=$("#inqu_status-0-segNo").val();
            if (IPLAT.isBlankString(segNo)){
                NotificationUtil({msg:"请先选择系统账套！"}, "error");
                return;
            }
            iframejQuery("#sub_query_status-0-segNo").val(segNo);
            iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
        },
        // 关闭窗口事件
        close: function (e) {
            var $iframe = userNum2Window.element.children("iframe");
            // 子窗口中的jQuery对象
            var iframejQuery = $iframe[0].contentWindow.$;

            // $iframe[0].contentWindow.resultGrid
            var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
            //
            // // 也可以使用如下的方式获取dataGrid
            // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

            var row = dataGrid.getCheckedRows();

            if (row.length > 0) {

                $("#inqu_status-0-customerId2").val(row[0].userNum);
                $("#inqu_status-0-customerName2").val(row[0].chineseUserName);

            }
            // 清空弹出框内容
            if (dataGrid.getDataItems().length > 0) {
                dataGrid.removeRows(dataGrid.getDataItems());
            }
        }
    }
    }

});
