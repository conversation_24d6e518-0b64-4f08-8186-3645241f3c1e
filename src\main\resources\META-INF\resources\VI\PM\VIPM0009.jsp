<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-processOrderId" cname="生产工单号" colWidth="3"/>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="工单信息">
            <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" needAuth="true" isFloat="true" personal="true">
                <EF:EFColumn ename="uuid" cname="ID" enable="false" width="235" align="left" hidden="true"/>
                <EF:EFColumn ename="unitCode" cname="业务单元代码" required="true" enable="false" align="center"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" primaryKey="true" enable="false" align="center"/>
                <EF:EFColumn ename="segNo" cname="系统账套" primaryKey="true" enable="false" align="left"
                             hidden="true"/>
                <EF:EFColumn ename="processOrderId" cname="生产工单号" enable="false" align="center"/>
                <EF:EFColumn ename="processDemandId" cname="生产需求单号" enable="false" align="center"/>
                <EF:EFColumn ename="processDemandSubId" cname="生产需求单子项号" enable="false" align="center"/>
                <EF:EFColumn ename="processCategory" cname="工序大类代码" enable="false" align="center"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" enable="false" align="center"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" enable="false" align="center"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" enable="false" align="center"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" enable="false" align="center"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" enable="false" align="center"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" enable="false" align="center"/>
                <EF:EFColumn ename="archiveFlag" cname="归档标记" enable="false" align="center"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" enable="false" align="center"/>
                <EF:EFColumn ename="tenantUser" cname="租户" enable="false" align="center"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <!-- 系统账套查询弹出框(查询条件中使用的) -->
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>
