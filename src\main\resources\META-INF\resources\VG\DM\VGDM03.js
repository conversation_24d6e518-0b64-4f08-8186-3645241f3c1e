$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId !== "info-1") {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({ msg: "请勾选一条scada信息" }, "error");
                        e.preventDefault(); //阻止切换第二个tab
                    } else {
                        queryTag(checkedRows[0]);
                    }
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
                // 同步数据按钮
                $("#SYNCDATA").click(function () {
                    const unitCode = $("#inqu_status-0-unitCode").val().trim();
                    const segNo = $("#inqu_status-0-segNo").val().trim();
                    if (!unitCode || !segNo) {
                        NotificationUtil({ msg: "请先选择业务单元" }, "error");
                        return;
                    }
                    IPLAT.confirm({
                        title: "同步",
                        message: "同步时会删除原数据，确认同步数据么?",
                        okFn: function (e) {
                            IMOMUtil.submitNode($("#inqu"), "VGDM03", "syncScada", function (ei) {
                                resultGrid.dataSource.page(1);
                                NotificationUtil({ msg: ei.msg }, "success");
                            });
                        }
                    });
                });
            }
        },
        "result2": {
            loadComplete: function (grid) {
                // 查询按钮
                $("#QUERY2").on("click", function (e) {
                    result2Grid.dataSource.page(1);
                });
                // 批量赋值
                $("#UPDATE").on("click", function (e) {
                    const validator = IPLAT.Validator({ id: "detail" });
                    if (!validator.validate() || !IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    const rowNums = result2Grid.getCheckedRowsIndex();
                    const updateValues = {
                        eArchivesNo: $("#detail_status-0-eArchivesNo").val(),
                        equipmentName: $("#detail_status-0-equipmentName").val(),
                        deviceCode: $("#detail_status-0-deviceCode").val(),
                        deviceName: $("#detail_status-0-deviceName").val()
                    };
                    Object.entries(updateValues).forEach(([key, value]) => {
                        result2Grid.setCellValue(rowNums, key, value);
                    });
                });
                // 修改设备
                $("#UPDATE2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM03", "update", true, null, null, false);
                });
                // 同步数据按钮-同步点位信息
                $("#SYNCDATA2").click(function () {
                    const node = $("#inqu2");
                    IMOMUtil.submitNode(node, "VGDM03", "syncTag", function (ei) {
                        result2Grid.dataSource.page(1);
                        NotificationUtil({ msg: ei.msg }, "success");
                    });
                });
                // 清空缓存按钮
                $("#CLEAR").click(function () {
                    const info = new EiInfo();
                    IMOMUtil.submitEiInfo(info, "VGDM03", "clear");
                });
                // 点位转换
                $("#CONVERT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0303", "convert", true, null, null, false);
                });
                // IHD点位同步
                $("#SYNCIHD").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM03", "syncIhd", true, null, null, false);
                });
                // 添加至报警推送规则
                $("#ADDCONFIG").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0603", "convert", true, null, null, false);
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();
    // 批量赋值区域设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu2_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu2_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#detail_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#detail_status-0-equipmentName").val(rows[0].equipmentName);
                $("#detail_status-0-deviceCode").val("");
                $("#detail_status-0-deviceName").val("");
            }
        }
    });
    // 批量赋值区域分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfo",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#detail_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#detail_status-0-deviceCode").val(rows[0].deviceCode);
                $("#detail_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });

    /**
     * 查询勾选scada下点位信息
     */
    function queryTag(checkedRow) {
        const { scadaName, unitCode, segNo } = checkedRow;
        $("#inqu2_status-0-scadaName").val(scadaName);
        $("#inqu2_status-0-unitCode").val(unitCode);
        $("#inqu2_status-0-segNo").val(segNo);
        result2Grid.dataSource.page(1);
        // 清空批量赋值区域
        $("#detail_status-0-eArchivesNo").val("");
        $("#detail_status-0-equipmentName").val("");
        $("#detail_status-0-deviceCode").val("");
        $("#detail_status-0-deviceName").val("");
    }
});
