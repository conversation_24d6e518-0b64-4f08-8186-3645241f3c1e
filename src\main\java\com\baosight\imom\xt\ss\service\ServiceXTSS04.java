package com.baosight.imom.xt.ss.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.xt.ss.domain.XTSS04;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.StringUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import java.util.*;

/**
 * 类的简介:开关维护后台服务-ServiceXTSS04 .
 */
public class ServiceXTSS04 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(XTSS04.RESULT0).addBlockMeta(new XTSS04().eiMetadata);
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS04().eiMetadata);
        return inInfo;
    }
    /**
     * @Service:S_VI_SM_0102
     *
     * 查询功能
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = super.query(inInfo,XTSS04.QUERY, new XTSS04(),false,new XTSS04().eiMetadata,"inqu_status","result0","result0");
        outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS04().eiMetadata);
        return outInfo;
    }

    /**
     * @Service:S_VI_SM_0103
     *
     * 查询详情信息功能
     * @param inInfo
     * @return
     */
    public EiInfo queryDetail(EiInfo inInfo) {
        String processSwitchName = inInfo.getString("inqu_status-0-processSwitchName");
        if (!StringUtils.isNotEmpty(processSwitchName)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("缺少生产开关名称！");
            return inInfo;
        }
        EiInfo outInfo = super.query(inInfo, XTSS04.QUERY_DETAIL, new XTSS04());
        return outInfo;
    }

    /**
     * @Service:S_VI_SM_0104
     *
     * 修改功能
     * @param inInfo
     * @return
     */
    public EiInfo update1(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                // 记录修改人,修改时间
                hashMap.put("recRevisor", UserSession.getUserId());
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
            }

            inInfo = super.update(inInfo, XTSS04.UPDATE);

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @Service:S_VI_SM_0105
     *
     * 删除功能
     * @param inInfo
     * @return
     */
    public EiInfo delete1(EiInfo inInfo) {
        try{
            inInfo = super.delete(inInfo, XTSS04.DELETE);

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @Service:S_VI_SM_0106
     *
     * 新增功能
     * @param inInfo
     * @return
     */
    public EiInfo insert1(EiInfo inInfo) {
        try {
            String messageText = "";
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                // 根据开关定义名称,系统账套获取数据判重
                Map map = new HashMap(16);
                String segNo = (String) Optional.ofNullable(hashMap.get("segNo")).orElse("");
                String unitCode = (String) Optional.ofNullable(hashMap.get("unitCode")).orElse("");
                String processSwitchName = (String) Optional.ofNullable(hashMap.get("processSwitchName")).orElse("");
                map.put("segNo", segNo);
                map.put("unitCode", unitCode);
                map.put("processSwitchName", processSwitchName);
                int switchNameCount = super.count(XTSS04.QUERY_DETAIL_COUNT, map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(messageText);
                    inInfo.setMsg(processSwitchName+"此开关当前系统账套："+ segNo +" 的记录已存在");
                    return inInfo;
                }
                //生成UUID唯一编码
                String uuid = UUIDHexIdGenerator.generate().toString();
                hashMap.put("uuid", uuid);
                hashMap.put("archiveFlag", "");
                hashMap.put("delFlag", 0);
                hashMap.put("tenantUser", "");
                // 记录创建人,创建时间
                hashMap.put("recCreator", UserSession.getUserId());
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 记录修改人,修改时间
                hashMap.put("recRevisor", UserSession.getUserId());
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
            }

            inInfo = super.insert(inInfo, XTSS04.INSERT);

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @Service:S_VI_SM_0107
     *
     * 根据生产开关名称获取生产开关值功能
     * @param inInfo
     * @return
     */
    public EiInfo querySwitchValueByName(EiInfo inInfo) {
        Map map = new HashMap(16);
        String processSwitchName = inInfo.get("inqu_status-0-processSwitchName").toString();
        map.put("processSwitchName", processSwitchName);
        List<HashMap> list = new ArrayList<>();

        List<XTSS04> switcInfo = this.dao.query(XTSS04.QUERY_ONE_SWITCH_INFO, map);
        if (switcInfo != null && switcInfo.size() > 0) {
            XTSS04 switc = switcInfo.get(0);
            String newChar = switc.getProcessSwitchValueDesc().replace("：", ":").replace("；", ";");
            String[] splitStr = newChar.split(";");
            for (int i = 0; i < splitStr.length; i++) {
                HashMap hashMap = new HashMap(16);
                String[] splitValue = splitStr[i].split(":");
                hashMap.put("processSwitchValue", splitValue[0]);
                hashMap.put("processSwitchValueDesc", splitValue[1]);
                list.add(hashMap);
            }
        }
        inInfo.addBlock(MesConstant.Iplat.RESULT3_BLOCK).addRows(list);
        inInfo.getBlock(MesConstant.Iplat.RESULT3_BLOCK).set(EiConstant.countStr, list.size());
        inInfo.getBlock(MesConstant.Iplat.RESULT3_BLOCK).set(EiConstant.offsetStr, 0);
        inInfo.getBlock(MesConstant.Iplat.RESULT3_BLOCK).set(EiConstant.limitStr, 10);
        return inInfo;
    }

    /**
     * @Service:S_VI_SM_0108
     *
     * 开关定义修改功能
     * @param inInfo
     * @return
     */
    public EiInfo update2(EiInfo inInfo) {
        try {
            String messageText = "";
            List<HashMap> listHashMap = inInfo.getBlock(XTSS04.RESULT0).getRows();
            for (HashMap hashMap : listHashMap) {
                String processSwitchName  = (String) Optional.ofNullable(hashMap.get("processSwitchName")).orElse("");
                Map map = new HashMap(16);
                map.put("processSwitchName", processSwitchName);
                // 根据开关定义名称获取开关配置总数
                int switchSettingCount = super.count(XTSS04.QUERY_SWITCH_SETTING_COUNT, map);
                if (switchSettingCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("此开关定义下存在开关配置不允许修改!");
                    return inInfo;
                }

                // 根据开关定义名称获取数据判重
                map.put("uuid", hashMap.get("uuid"));
                int switchNameCount = super.count(XTSS04.QUERY_PROCESS_SWITCH_NAME_COUNT, map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(processSwitchName+"此开关定义名称已存在");
                    return inInfo;
                }

                // 记录修改人,修改时间
                hashMap.put("recRevisor", UserSession.getUserId());
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                dao.update(XTSS04.UPDATE2, hashMap);
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @Service:S_VI_SM_0109
     *
     * 开关定义删除功能
     * @param inInfo
     * @return
     */
    public EiInfo delete2(EiInfo inInfo) {
        try{
            String messageText = "";
            List<HashMap> listHashMap = inInfo.getBlock(XTSS04.RESULT0).getRows();
            for (HashMap hashMap : listHashMap) {
                String processSwitchName  = (String) Optional.ofNullable(hashMap.get("processSwitchName")).orElse("");
                Map map = new HashMap(16);
                map.put("processSwitchName", processSwitchName);
                // 根据开关定义名称获取开关配置总数
                int switchSettingCount = super.count(XTSS04.QUERY_SWITCH_SETTING_COUNT, map);
                if (switchSettingCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("此开关定义下存在开关配置不允许删除!");
                    return inInfo;
                }

                dao.delete(XTSS04.DELETE2, hashMap);
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @Service:S_VI_SM_0110
     *
     * 开关定义新增功能
     * @param inInfo
     * @return
     */
    public EiInfo insert2(EiInfo inInfo) {
        try {
            String messageText = "";
            List<HashMap> listHashMap = inInfo.getBlock(XTSS04.RESULT0).getRows();
            for (HashMap hashMap : listHashMap) {
                // 根据开关定义名称获取数据判重
                Map map = new HashMap(16);
                String processSwitchName  = (String) Optional.ofNullable(hashMap.get("processSwitchName")).orElse("");
                map.put("processSwitchName", processSwitchName);
                int switchNameCount = super.count(XTSS04.QUERY_PROCESS_SWITCH_NAME_COUNT, map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(processSwitchName+"此开关定义名称已存在");
                    return inInfo;
                }

                //生成UUID唯一编码
                String uuid = UUIDHexIdGenerator.generate().toString();
                hashMap.put("uuid", uuid);
                hashMap.put("archiveFlag", "");
                hashMap.put("delFlag", 0);
                hashMap.put("tenantUser", "");
                // 记录创建人,创建时间
                hashMap.put("recCreator", UserSession.getUserId());
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 记录修改人,修改时间
                hashMap.put("recRevisor", UserSession.getUserId());
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
//                hashMap.put("recRevisor", "");
//                hashMap.put("recRevisorName", "");
//                hashMap.put("recReviseTime", "");

                dao.insert(XTSS04.INSERT, hashMap);
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
