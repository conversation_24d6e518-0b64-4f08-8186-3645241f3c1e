<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField" defaultValue="20+99"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P006"/>
                <EF:EFOption label="待审核" value="10"/>
                <EF:EFOption label="生效+完成" value="20+99"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-typeOfHandling" cname="业务类型" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P007"/>
                <EF:EFOption label="钢材装卸货" value="90"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-reservationDateStart"
                           endName="inqu_status-0-reservationTimeEnd"
                           startCname="起始预约日期" endCname="截止预约日期"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
            <EF:EFSelect ename="inqu_status-0-reservationDateRange" cname="预约范围" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="只看本日预约" value="10"/>
                <EF:EFOption label="只看明日预约" value="20"/>
            </EF:EFSelect>
            <EF:EFInput type="text" ename="inqu_status-0-reservationNumber" cname="预约单号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
        </div>
        <div class="row">
                <%--<EF:EFSelect ename="inqu_status-0-reservationIdentity" cname="预约身份(客户/承运商)" optionLabel="全部"
                             colWidth="3"
                             valueField="valueField" textField="textField"
                             template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                    <EF:EFCodeOption codeName="P002"/>
                </EF:EFSelect>--%>
            <EF:EFPopupInput ename="inqu_status-0-customerId" cname="承运商" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-customerName"
                             containerId="userNum" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true"
                             popupTitle="承运商查询">
            </EF:EFPopupInput>
            <EF:EFInput type="text" ename="inqu_status-0-customerName" cname="承运商名称" colWidth="3"
                        ratio="4:8" disabled="true"/>
            <EF:EFPopupInput ename="inqu_status-0-customerId2" cname="客户代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-customerName2"
                             containerId="userNum2" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true"
                             popupTitle="客户代码查询">
            </EF:EFPopupInput>
            <EF:EFInput type="text" ename="inqu_status-0-customerName2" cname="客户代码名称" colWidth="3"
                        ratio="4:8" disabled="true"/>
        </div>
        <div class="row">
            <EF:EFInput type="text" ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
            <EF:EFInput type="text" ename="inqu_status-0-driverName" cname="司机姓名" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
            <EF:EFSelect ename="inqu_status-0-isReservation" cname="是否有预约单" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="无预约单" value="00"/>
                <EF:EFOption label="有预约单" value="10"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-isHouseCars" cname="车辆是否在厂" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="已预约未登记" value="10"/>
                <EF:EFOption label="已登记未进厂" value="20"/>
                <EF:EFOption label="在厂" value="30"/>
                <EF:EFOption label="已出厂" value="40"/>
                <EF:EFOption label="本日预约未登记" value="50"/>
                <EF:EFOption label="本日已出厂" value="60"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-enterFactoryStart"
                           endName="inqu_status-0-enterFactoryEnd"
                           startCname="起始进厂时间" endCname="截止进厂时间"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
            <EF:EFDateSpan startName="inqu_status-0-leaveFactoryDateStart"
                           endName="inqu_status-0-leaveFactoryDateEnd"
                           startCname="起始出厂时间" endCname="截止出厂时间"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
            <EF:EFInput type="text" ename="inqu_status-0-visitUnit" cname="拜访单位" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0201" queryMethod="query"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="reservationNumber" cname="预约单号" align="center" enable="false"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                <EF:EFCodeOption codeName="P006"/>
                <EF:EFOption label="待审核" value="10"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="isReservation" cname="是否有预约单" align="center" enable="false" sort="false">
                <EF:EFOption label="无预约单" value="00"/>
                <EF:EFOption label="有预约单" value="10"/>
            </EF:EFComboColumn>

            <EF:EFColumn ename="customerId" cname="承运商/客户代码" align="center" width="160"/>
            <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" enable="false" width="160"/>
            <EF:EFColumn ename="vehicleNo" cname="车牌号" required="false" align="center" editType="textarea"
                         maxlength="500"/>
            <EF:EFColumn ename="driverName" cname="司机姓名" enable="false" align="center"/>
            <EF:EFColumn ename="driverTel" cname="司机手机号" align="center" enable="false"/>
            <EF:EFColumn ename="driverIdentity" cname="司机身份证号" align="center" enable="false"/>
            <EF:EFComboColumn ename="typeOfHandling" cname="业务类型" align="center" enable="false">
                <EF:EFOption label="钢材装卸货" value="90"/>
                <EF:EFCodeOption codeName="P007"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="visitUnit" cname="拜访单位" enable="false" align="center" />
            <EF:EFColumn ename="startOfTransport" cname="起始地" align="center" enable="false"/>
            <EF:EFColumn ename="purposeOfTransport" cname="目的地" align="center" enable="false"/>
            <EF:EFColumn ename="reservationDate" cname="预约日期" align="center" enable="false"/>
            <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>

            <EF:EFComboColumn ename="lateEarlyFlag" cname="迟到早到标记" align="center" enable="false" sort="false">
                <EF:EFOption label="未标记" value="0"/>
                <EF:EFOption label="迟到" value="10"/>
                <EF:EFOption label="早到" value="20"/>
                <EF:EFOption label="正常" value="30"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="checkDate" cname="登记时间" align="center" width="140" enable="false"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" sort="false"/>
            <EF:EFColumn ename="callDate" cname="叫号时间" align="center" width="140" enable="false"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" sort="false"/>
            <EF:EFColumn ename="enterFactory" cname="进厂时间" align="center" width="140" enable="false"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" sort="false"/>
            <EF:EFColumn ename="beginEntruckingTime" cname="首次作业开始时间" align="center" width="140" enable="false"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" sort="false"/>
            <EF:EFColumn ename="completeUninstallTime" cname="最终作业完成时间" align="center" width="140" enable="false"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" sort="false"/>
            <EF:EFColumn ename="leaveFactoryDate" cname="出厂时间" align="center" width="140" enable="false"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" sort="false"/>
            <EF:EFColumn ename="theTimeFromRegistrationToEntry" cname="登记至进厂时长" align="center"
                         width="163" sort="false" enable="false"/>
            <EF:EFColumn ename="enterFactoryCompleteUninstallTime" cname="进厂至出厂时长" align="center"
                         width="143" sort="false" enable="false"/>
            <EF:EFColumn ename="enterFactoryLeaveFactoryDate" cname="作业开始至作业完成时长" align="center" sort="false"
                         enable="false"/>
            <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" align="center" enable="false" sort="false"/>
            <EF:EFComboColumn ename="carTraceStatus" cname="车辆跟踪单状态" align="center"  enable="false" sort="false">
                <EF:EFCodeOption codeName="P008"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="currentHandPointName" cname="当前装卸点名称" align="center" enable="false"  sort="false"
                         width="126"/>

            <EF:EFComboColumn ename="nextTarget" cname="下一目标" align="center">
                <EF:EFOption label="下一装卸点" value="10"/>
                <EF:EFOption label="离厂" value="20"/>
            </EF:EFComboColumn>

            <EF:EFComboColumn ename="isSelfProduced" cname="是否自带货" align="center">
                <EF:EFOption label="是" value="1"/>
                <EF:EFOption label="否" value="0"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="selfProducedDesc" cname="自带货描述" align="center" enable="false"  sort="false"
                         width="126"/>
            <EF:EFColumn ename="targetHandPointName" cname="目标装卸点名称" align="center" enable="false"  sort="false"
                         width="126"/>
            <EF:EFColumn ename="signForAttachments" cname="安全告知签章附件" align="center" enable="false"  sort="false"
                         width="126"/>
            <EF:EFColumn ename="remark" cname="备注" required="false" align="left" editType="textarea" maxlength="500"
                         sort="false" enable="false"/>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>

    <EF:EFWindow id="FILEWINDOW" url="" width="90%" height="60%" top="70%" left="170px" refresh="true" lazyload="true"
                 title="附件查询">
        <div title="附件信息查询">
            <EF:EFRegion id="sub3_query" title="查询条件">
                <EF:EFInput ename="sub3_query_status-0-relevanceId" cname="预约单号"
                            disabled="true"/>
                <EF:EFInput ename="sub3_query_status-0-driverName" cname="司机姓名" type="hidden"
                            disabled="true"/>
                <EF:EFInput ename="sub3_query_status-0-driverTel" cname="手机号" type="hidden"
                            disabled="true"/>
                <EF:EFInput ename="sub3_query_status-0-driverIdentity" cname="身份证号" type="hidden"
                            disabled="true"/>
                <EF:EFInput ename="sub3_query_status-0-segNo" cname="账套" hidden="true"
                            disabled="true"/>
                <%--<EF:EFInput ename="sub_query_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>--%>
            </EF:EFRegion>
        </div>
        <EF:EFRegion id="sub_result3" title="结果集">
            <EF:EFGrid isFloat="true" blockId="sub_result3" autoBind="false" autoDraw="false" serviceName="LIRL0220"
                       queryMethod="fileQuery3">
                <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                <EF:EFColumn ename="relevanceId" cname="车辆跟踪单号" width="120" align="center"/>
                <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                <EF:EFColumn ename="fifleSize" cname="文件大小"/>
                <EF:EFColumn ename="fifleType" cname="文件类型"/>
                <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                <EF:EFColumn ename="recCreateTime" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
            </EF:EFGrid>
        </EF:EFRegion>

    </EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0001" id="userNum2" width="90%" height="60%"></EF:EFWindow>

</EF:EFPage>
