create table TMEDV0002
(
    MAIN_ID         VARCHAR(32)    DEFAULT ' ' NOT NULL COMMENT '主表ID',
    PARAM_CODE      VARCHAR(20)    default ' ' not null comment '参数代码',
    PARAM_NAME      VARCHAR(32)    default ' ' not null comment '参数名称',
    PARAM_VALUE     VARCHAR(20)    default ' ' not null comment '参数值',
    UPPER_LIMIT     DECIMAL(20, 8) default 0   not null comment '上限值',
    LOWER_LIMIT     DECIMAL(20, 8) default 0   not null comment '下限值',
    -- 固定字段
    UUID            VARCHAR(32)                NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)    DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)    DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)    DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)    DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)    DEFAULT '0' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)     DEFAULT '0' NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='设备工艺参数子表' ENGINE = INNODB
                              DEFAULT CHARSET = UTF8
                              COLLATE UTF8_BIN;
-- 增加ER图外键
ALTER TABLE TMEDV0002 ADD FOREIGN KEY (MAIN_ID) REFERENCES TMEDV0001(UUID);