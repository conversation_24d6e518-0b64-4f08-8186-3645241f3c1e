/**
* Generate time : 2024-10-14 10:47:54
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0701;

import java.util.Map;

/**
* Tlids0701
* 
*/
public class LIDS0701 extends Tlids0701 {
        public static final String QUERY = "LIDS0701.query";
        public static final String QUERY_TO_MAP = "LIDS0701.queryToMap";
        public static final String COUNT = "LIDS0701.count";
        public static final String COUNT_UUID = "LIDS0701.count_uuid";
        public static final String INSERT = "LIDS0701.insert";
        public static final String UPDATE = "LIDS0701.update";
        public static final String DELETE = "LIDS0701.delete";
        public static final String QUERY_LOCATION = "LIDS0701.queryLocation";
        public static final String QUERY_MACHINE_CODE_CROSS_AREA = "LIDS0701.queryMachineCodeCrossArea";
        public static final String QUERY_MACHINE_BY_AREA = "LIDS0701.queryMachineByArea";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0701() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}