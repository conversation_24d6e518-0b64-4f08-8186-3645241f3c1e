package com.baosight.imom.vg.dm.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.VGDM0102;

/**
 * <AUTHOR> yzj
 * @Description : 分部设备查询弹窗页面后台
 * @Date : 2024/8/28
 * @Version : 1.0
 */
public class ServiceVGDM0102 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0102.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0102().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        String eArchivesNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "eArchivesNo");
        if (StrUtil.isBlank(eArchivesNo)) {
            // 更新状态信息用于返回前端
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("缺少设备档案编号不能查询!");
            return inInfo;
        }
        // 只查询生效状态分部设备
        inInfo.setCell(EiConstant.queryBlock, 0, "deviceStatus", MesConstant.Status.K30);
        return super.query(inInfo, VGDM0102.QUERY, new VGDM0102());
    }

}
