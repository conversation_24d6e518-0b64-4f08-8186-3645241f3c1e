package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0104;
import com.baosight.imom.vg.dm.domain.VGDM0701;
import com.baosight.imom.vg.dm.domain.VGDM0801;
import com.baosight.imom.vg.dm.domain.VGDM0804;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;


/**
 * 检修实绩资材领用后台
 *
 * <AUTHOR> 郁在杰
 * @Description :
 * @Date : 2024/12/17
 * @Version : 1.0
 */
public class ServiceVGDM0804 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0804.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0804().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 列表页面查询所有数据
     */
    public EiInfo queryAll(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0804.QUERY, new VGDM0804());
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        String voucherNum = inInfo.getCellStr(MesConstant.Iplat.INQU_STATUS2, 0, "voucherNum");
        String faultId = inInfo.getCellStr(MesConstant.Iplat.INQU_STATUS2, 0, "faultId");
        // 如果故障号和资材领用单号都为空，则生成一个UUID防止查询数据
        if (StrUtil.isBlank(faultId) && StrUtil.isBlank(voucherNum)) {
            inInfo.setCell(MesConstant.Iplat.INQU_STATUS2, 0, "voucherNum", UUIDUtils.getUUID());
        }
        return super.query(inInfo, VGDM0804.QUERY, null, false, new VGDM0804().eiMetadata,
                MesConstant.Iplat.INQU_STATUS2, MesConstant.Iplat.ZC_DETAIL, MesConstant.Iplat.ZC_DETAIL);
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            checkSource(inInfo);
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.ZC_DETAIL);
            for (int i = 0; i < block.getRowCount(); i++) {
                VGDM0804 vgdm0804 = new VGDM0804();
                vgdm0804.fromMap(block.getRow(i));
                // 数据校验
                vgdm0804.setUuid(UUIDUtils.getUUID());
                this.checkData(vgdm0804);
                // 赋值
                vgdm0804.setStuffReceivingStatus(MesConstant.Status.K10);
                // 新增数据
                Map data = vgdm0804.toMap();
                RecordUtils.setCreator(data);
                block.getRows().set(i, data);
            }
            // 批量插入
            DaoUtils.insertBatch(dao, VGDM0804.INSERT, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验点检异常信息
     *
     * @param vgdm0804 点检异常信息
     */
    private void checkData(VGDM0804 vgdm0804) {
        if (vgdm0804.getUsingWgt().compareTo(BigDecimal.ZERO) <= 0) {
            throw new PlatException("申请量不能为0");
        }
        // 校验数据重复（与库存记录对应）
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("inventoryUuid", vgdm0804.getInventoryUuid());
        countMap.put("notUuid", vgdm0804.getUuid());
        countMap.put("faultId", vgdm0804.getFaultId().trim());
        countMap.put("voucherNum", vgdm0804.getVoucherNum().trim());
        int count = super.count(VGDM0804.COUNT, countMap);
        if (count > 0) {
            throw new PlatException("资材【" + vgdm0804.getStuffCode() + "-" + vgdm0804.getStuffName() + "】已存在");
        }
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            checkSource(inInfo);
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.ZC_DETAIL);
            for (int i = 0; i < block.getRowCount(); i++) {
                VGDM0804 vgdm0804 = new VGDM0804();
                vgdm0804.fromMap(block.getRow(i));
                // 查询数据
                VGDM0804 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0804, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                // 数据校验
                vgdm0804.setUuid(dbData.getUuid());
                vgdm0804.setInventoryUuid(dbData.getInventoryUuid());
                this.checkData(vgdm0804);
                // 可修改字段
                dbData.setUsingWgt(vgdm0804.getUsingWgt());
                // 赋值通用字段
                Map data = dbData.toMap();
                RecordUtils.setRevisor(data);
                block.getRows().set(i, data);
            }
            // 批量修改
            DaoUtils.updateBatch(dao, VGDM0804.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>删除标记改为1，状态改为00
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            checkSource(inInfo);
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.ZC_DETAIL);
            VGDM0804 frontData;
            VGDM0804 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0804();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                dbData.setDelFlag("1");
                dbData.setStuffReceivingStatus(MesConstant.Status.K00);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            // 批量修改
            DaoUtils.updateBatch(dao, VGDM0804.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 查询备品备件领用申请数量
     */
    public EiInfo queryApplyCount(EiInfo inInfo) {
        try {
            List<String> idList = (List<String>) inInfo.get("idList");
            if (CollectionUtils.isEmpty(idList)) {
                throw new PlatException("来源单据号不能为空!");
            }
            String relevanceType = inInfo.getString("relevanceType");
            Map<String, Object> queryMap = new HashMap<>();
            if (VGDM0104.RelevanceType.FAULT.equals(relevanceType)) {
                queryMap.put("faultIdList", idList);
            } else if (VGDM0104.RelevanceType.OVERHAUL.equals(relevanceType)) {
                queryMap.put("voucherNumList", idList);
            } else {
                throw new PlatException("数据来源错误!" + relevanceType);
            }
            //查询备品备件领用申请数量
            int a = super.count(VGDM0804.COUNT, queryMap);
            // 返回
            inInfo.set("rowCount", a);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 检查数据来源
     */
    private void checkSource(EiInfo inInfo) {
        String relevanceType = inInfo.getString("relevanceType");
        if (StrUtil.isBlank(relevanceType)) {
            throw new PlatException("数据来源错误!");
        }
        Map<String, String> queryMap = new HashMap<>();
        switch (relevanceType) {
            // 故障信息
            case VGDM0104.RelevanceType.FAULT:
                String faultId = inInfo.getString("faultId");
                if (StrUtil.isBlank(faultId)) {
                    throw new PlatException("故障单号不能为空!");
                }
                queryMap.put("equalId", faultId);
                queryMap.put("faultStatus", MesConstant.Status.K20);
                List list = dao.query(VGDM0701.QUERY, queryMap);
                if (CollectionUtils.isEmpty(list)) {
                    throw new PlatException("只能对已提交状态的故障单进行操作");
                }
                break;
            // 检修记录
            case VGDM0104.RelevanceType.OVERHAUL:
                String overhaulPlanId = inInfo.getString("voucherNum");
                if (StrUtil.isBlank(overhaulPlanId)) {
                    throw new PlatException("检修单号不能为空!");
                }
                queryMap.put("equalId", overhaulPlanId);
                list = dao.query(VGDM0801.QUERY, queryMap);
                if (CollectionUtils.isEmpty(list)) {
                    throw new PlatException("只能对生效或启动状态的检修单进行操作");
                }
                VGDM0801 vgdm0801 = (VGDM0801) list.get(0);
                DaoUtils.compareStr("只能对生效或启动状态的检修单进行操作", vgdm0801.getOverhaulPlanStatus(), MesConstant.Status.K20, MesConstant.Status.K30);
                break;
            default:
                throw new PlatException("数据来源错误!");
        }
    }

}
