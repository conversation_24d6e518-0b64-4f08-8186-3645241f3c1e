<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
        <EF:EFRegion id="inqu" title="查询条件">
            <div class="row">
                <EF:EFInput ename="inqu_status-0-applyDept" cname="部门代码" colWidth="3"/>
                <EF:EFInput ename="inqu_status-0-applyDeptName" cname="部门名称" colWidth="3"/>
                <EF:EFInput ename="inqu_status-0-windowId" cname="ID" type="hidden"/>
                <EF:EFInput ename="inqu_status-0-unitCode" cname="系统账套" type="hidden"/>
                <EF:EFInput ename="inqu_status-0-segNo" cname="业务单元代码" type="hidden"/>
            </div>
        </EF:EFRegion>
        <EF:EFRegion id="result" title="查询结果">
            <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" isFloat="true">
                <EF:EFColumn ename="applyDept" cname="部门代码" enable="false" width="100" align="center"/>
                <EF:EFColumn ename="applyDeptName" cname="部门名称" enable="false" width="100" align="center"/>
            </EF:EFGrid>
        </EF:EFRegion>
</EF:EFPage>
