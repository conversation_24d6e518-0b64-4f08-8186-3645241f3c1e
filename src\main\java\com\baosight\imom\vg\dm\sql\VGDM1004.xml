<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1004">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="minSecond">
            DURATION_SECOND &gt;= #minSecond#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1004">
        SELECT
        START_TIME as "startTime",  <!-- 开始时间 -->
        END_TIME as "endTime",  <!-- 结束时间 -->
        RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        DURATION_SECOND as "durationSecond" <!-- 持续时间 -->
        FROM ${mevgSchema}.TVGDM1004 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1004 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1004 (START_TIME,  <!-- 开始时间 -->
        END_TIME,  <!-- 结束时间 -->
        RELEVANCE_ID,  <!-- 关联ID -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        DURATION_SECOND  <!-- 持续时间 -->
        )
        VALUES (#startTime#, #endTime#, #relevanceId#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#,
        #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#,
        #durationSecond#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1004 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1004
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        DURATION_SECOND = #durationSecond#   <!-- 持续时间 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>