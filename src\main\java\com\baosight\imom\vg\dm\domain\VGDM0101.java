package com.baosight.imom.vg.dm.domain;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.vg.domain.Tvgdm0101;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备档案信息表
 */
public class VGDM0101 extends Tvgdm0101 {
    /**
     * 根据设备编码查询设备信息
     */
    public static final String QUERY_BY_NO = "VGDM0101.queryByNo";
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0101.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0101.count";
    /**
     * 查询条数
     */
    public static final String COUNT_BY_ID = "VGDM0101.countById";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0101.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0101.update";
    /**
     * 修改
     */
    public static final String UPDATE_ARCHIVE = "VGDM0101.updateArchive";
    /**
     * 查询设备信息和机组信息
     */
    public static final String QUERY_WITH_MACHINE = "VGDM0101.queryWithMachine";

    /**
     * 更新厂房信息
     */
    public static final String UPDATE_FACTORY_BUILDING = "VGDM0101.updateFactoryBuilding";

    /**
     * 根据设备档案编号查询设备信息
     *
     * @param dao         dao
     * @param eArchivesNo 设备档案编号
     * @param isAll       是否查询所有
     * @return 设备档案信息或null
     */
    public static VGDM0101 queryByNo(Dao dao, String eArchivesNo, boolean isAll) {
        Map<String, String> queryMap = new HashMap<>(1);
        queryMap.put("eArchivesNo", eArchivesNo);
        if (!isAll) {
            queryMap.put("equipmentStatus", "30");
        }
        List list = dao.query(QUERY_BY_NO, queryMap);
        if (CollectionUtils.isNotEmpty(list)) {
            return (VGDM0101) list.get(0);
        }
        return null;
    }

    /**
     * 根据设备档案编号查询设备信息
     *
     * @param dao         dao
     * @param eArchivesNo 设备档案编号
     * @return 设备档案信息或null
     */
    public static VGDM0101 queryByNo(Dao dao, String eArchivesNo) {
        return queryByNo(dao, eArchivesNo, false);
    }

    /**
     * 查询设备信息和机组信息
     *
     * @param dao         dao
     * @param segNo       机组编号
     * @param eArchivesNo 设备档案编号
     * @return 设备信息和机组信息
     */
    public static Map<String, String> queryWithMachine(Dao dao, String segNo, String eArchivesNo) {
        Map<String, String> queryMap = new HashMap<>(2);
        queryMap.put("segNo", segNo);
        queryMap.put("eArchivesNo", eArchivesNo);
        List<Map<String, String>> equipmentList = dao.query(QUERY_WITH_MACHINE, queryMap);
        if (CollectionUtils.isEmpty(equipmentList)) {
            throw new PlatException("未查询到设备[" + eArchivesNo + "]信息");
        }
        Map<String, String> equipment = equipmentList.get(0);
        String machineCode = MapUtils.getStr(equipment, "machineCode");
        String machineName = MapUtils.getStr(equipment, "machineName");
        if (StrUtil.isBlank(machineCode) || StrUtil.isBlank(machineName)) {
            throw new PlatException("未查询到设备[" + eArchivesNo + "]对应机组信息");
        }
        return equipment;
    }
}
