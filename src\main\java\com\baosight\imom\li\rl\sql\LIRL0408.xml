<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-09-25 10:19:18
   		Version :  1.0
		tableName :${meliSchema}.tlirl0408
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 LEAVE_FACTORY_ID  VARCHAR   NOT NULL   primarykey, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 LEAVE_FACTORY_DATE  VARCHAR   NOT NULL, 
		 CANCEL_LEAVE_FACTORY_DATE  VARCHAR   NOT NULL, 
		 CAR_TRACE_NO  VARCHAR   NOT NULL, 
		 DATE_SOURCE  VARCHAR   NOT NULL, 
		 DEVICE_NUMBER  VARCHAR   NOT NULL, 
		 DEVICE_VOUCHER_NUM  VARCHAR   NOT NULL, 
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 UNLOAD_LEAVE_FLAG  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0408">
	
	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryId">
			LEAVE_FACTORY_ID = #leaveFactoryId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryDate">
			LEAVE_FACTORY_DATE = #leaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cancelLeaveFactoryDate">
			CANCEL_LEAVE_FACTORY_DATE = #cancelLeaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateSource">
			DATE_SOURCE = #dateSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deviceNumber">
			DEVICE_NUMBER = #deviceNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deviceVoucherNum">
			DEVICE_VOUCHER_NUM = #deviceVoucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unloadLeaveFlag">
			UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryId">
			LEAVE_FACTORY_ID = #leaveFactoryId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryDate">
			LEAVE_FACTORY_DATE = #leaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cancelLeaveFactoryDate">
			CANCEL_LEAVE_FACTORY_DATE = #cancelLeaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateSource">
			DATE_SOURCE = #dateSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deviceNumber">
			DEVICE_NUMBER = #deviceNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deviceVoucherNum">
			DEVICE_VOUCHER_NUM = #deviceVoucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unloadLeaveFlag">
			UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0408">
		SELECT
				SEG_NO	as "segNo",  <!-- 账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				LEAVE_FACTORY_ID	as "leaveFactoryId",  <!-- 出厂流水号 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				STATUS	as "status",  <!-- 状态(00撤销 10新增) -->
				LEAVE_FACTORY_DATE	as "leaveFactoryDate",  <!-- 出厂时间 -->
				CANCEL_LEAVE_FACTORY_DATE	as "cancelLeaveFactoryDate",  <!-- 撤销出厂时间 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				DATE_SOURCE	as "dateSource",  <!-- 数据来源(10：mes 20pda 30：车牌识别) -->
				DEVICE_NUMBER	as "deviceNumber",  <!-- 设备号（车牌识别） -->
				DEVICE_VOUCHER_NUM	as "deviceVoucherNum",  <!-- 依据凭单号(车牌识别系统流水号) -->
				VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单号(PAD作业流水号) -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				UNLOAD_LEAVE_FLAG	as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId", <!-- 租户ID -->
				BACK_STATUS	as "backStatus"  <!-- 返单状态 -->
		FROM ${meliSchema}.tlirl0408 WHERE 1=1
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="leaveFactoryId">
			LEAVE_FACTORY_ID = #leaveFactoryId#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy"></isEmpty>
		</dynamic>
		<isNotEmpty prepend=" AND " property="leaveFactoryId">
			LEAVE_FACTORY_ID = #leaveFactoryId#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  LEAVE_FACTORY_ID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0408 WHERE 1=1
		<isNotEmpty prepend=" AND " property="leaveFactoryId">
			LEAVE_FACTORY_ID = #leaveFactoryId#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryId">
			LEAVE_FACTORY_ID = #leaveFactoryId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryDate">
			LEAVE_FACTORY_DATE = #leaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cancelLeaveFactoryDate">
			CANCEL_LEAVE_FACTORY_DATE = #cancelLeaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateSource">
			DATE_SOURCE = #dateSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deviceNumber">
			DEVICE_NUMBER = #deviceNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deviceVoucherNum">
			DEVICE_VOUCHER_NUM = #deviceVoucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unloadLeaveFlag">
			UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0408 (SEG_NO,  <!-- 账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										LEAVE_FACTORY_ID,  <!-- 出厂流水号 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										STATUS,  <!-- 状态(00撤销 10新增) -->
										LEAVE_FACTORY_DATE,  <!-- 出厂时间 -->
										CANCEL_LEAVE_FACTORY_DATE,  <!-- 撤销出厂时间 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										DATE_SOURCE,  <!-- 数据来源(10：mes 20pda 30：车牌识别) -->
										DEVICE_NUMBER,  <!-- 设备号（车牌识别） -->
										DEVICE_VOUCHER_NUM,  <!-- 依据凭单号(车牌识别系统流水号) -->
										VOUCHER_NUM,  <!-- 依据凭单号(PAD作业流水号) -->
										FACTORY_AREA,  <!-- 厂区 -->
										UNLOAD_LEAVE_FLAG,  <!-- 未装离厂标记 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
						BACK_STATUS
										)		 
	    VALUES (#segNo#, #unitCode#, #leaveFactoryId#, #vehicleNo#, #status#, #leaveFactoryDate#, #cancelLeaveFactoryDate#,
		#carTraceNo#, #dateSource#, #deviceNumber#, #deviceVoucherNum#, #voucherNum#, #factoryArea#, #unloadLeaveFlag#, #recCreator#,
		#recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#,
		#tenantId#,#backStatus#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0408 WHERE
			LEAVE_FACTORY_ID = #leaveFactoryId#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0408
		SET 
		SEG_NO	= #segNo#,   <!-- 账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
								VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					STATUS	= #status#,   <!-- 状态(00撤销 10新增) -->  
					LEAVE_FACTORY_DATE	= #leaveFactoryDate#,   <!-- 出厂时间 -->  
					CANCEL_LEAVE_FACTORY_DATE	= #cancelLeaveFactoryDate#,   <!-- 撤销出厂时间 -->  
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->  
					DATE_SOURCE	= #dateSource#,   <!-- 数据来源(10：mes 20pda 30：车牌识别) -->  
					DEVICE_NUMBER	= #deviceNumber#,   <!-- 设备号（车牌识别） -->  
					DEVICE_VOUCHER_NUM	= #deviceVoucherNum#,   <!-- 依据凭单号(车牌识别系统流水号) -->  
					VOUCHER_NUM	= #voucherNum#,   <!-- 依据凭单号(PAD作业流水号) -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					UNLOAD_LEAVE_FLAG	= #unloadLeaveFlag#,   <!-- 未装离厂标记 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			LEAVE_FACTORY_ID = #leaveFactoryId#
	</update>
  
</sqlMap>