<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-factoryArea" cname="厂区代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryBuilding" cname="厂房代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryBuildingName" cname="厂房名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-crossArea" cname="跨区代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-locationId" cname="库位代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFSelect ename="inqu_status-0-useStatus" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="空闲" value="10"/>
                <EF:EFOption label="预占" value="20"/>
                <EF:EFOption label="占用" value="30"/>
                <EF:EFOption label="禁用" value="99"/>
            </EF:EFSelect>

            <EF:EFSelect ename="inqu_status-0-upDownFlag" cname="上下层标记" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="下层" value="1"/>
                <EF:EFOption label="上层" value="2"/>
            </EF:EFSelect>
        </div>

    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" >
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <%--<EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>--%>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="locationId" cname="库位代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="wproviderId" cname="仓库代码" align="center" width="120" enable="false"/>

                <EF:EFColumn ename="locViewPackId" cname="库位占用捆包号" align="center" width="120" enable="false"/>
                <%--<EF:EFColumn ename="locColumnId" cname="库位(跨+列)" align="center" width="120" enable="false"/>--%>
                <EF:EFColumn ename="idleIntervalId" cname="库位空闲区间编号" align="center" width="120" enable="false"/>

                <EF:EFComboColumn ename="useStatus" cname="状态" align="center" width="150">
                    <EF:EFOption label="空闲" value="10"/>
                    <EF:EFOption label="预占" value="20"/>
                    <EF:EFOption label="占用" value="30"/>
                    <EF:EFOption label="禁用" value="99"/>
                </EF:EFComboColumn>

                <EF:EFComboColumn ename="locationType" cname="库位类型" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P038"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="x_pointStart" cname="X轴起始点" align="center" width="120"/>
                <EF:EFColumn ename="x_pointEnd" cname="X轴结束点" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="leftViewPackId" cname="左侧同层捆包" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="rightViewPcakId" cname="右侧同层捆包" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="upLeftViewPackId" cname="左侧跨层捆包" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="upRightViewPackId" cname="右侧跨层捆包" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="specUpper" cname="宽度上限" align="center" width="120"/>
                <EF:EFColumn ename="specLower" cname="宽度下限" align="center" width="120"/>
                <EF:EFComboColumn ename="upDownFlag" cname="上下层标记" align="center" width="150" enable="false">
                    <EF:EFOption label="下层" value="1"/>
                    <EF:EFOption label="上层" value="2"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="locationLength" cname="库位长度" align="center" width="120" enable="false"/>
                <EF:EFComboColumn ename="standFlag" cname="是否立式库位" align="center" width="150">
                    <EF:EFOption label="卧式" value="0"/>
                    <EF:EFOption label="立式" value="1"/>
                </EF:EFComboColumn>
               <%-- <EF:EFComboColumn ename="lrAccupyFlag" cname="下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）" align="center" width="150">
                    <EF:EFOption label="两侧无卷" value="0"/>
                    <EF:EFOption label="至少一侧有卷" value="1"/>
                </EF:EFComboColumn>--%>
                <EF:EFComboColumn ename="jgLockFlag" cname="下层卷是否已下生产计划/工单（上层库位属性）" align="center" width="150">
                    <EF:EFOption label="无计划封锁" value="0"/>
                    <EF:EFOption label="有计划封锁" value="1"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="upForbinFlag" cname="下层外板卷时不容许放卷（上层库位属性）" align="center" width="150">
                    <EF:EFOption label="上层允许放卷" value="0"/>
                    <EF:EFOption label="上层不允许放卷" value="1"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="factoryProductId" cname="钢厂资源号" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="productTypeId" cname="品种" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="shopsign" cname="牌号" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="spec" cname="规格" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="innerDiameter" cname="内径 单位:mm" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="outerDiameter" cname="外径 单位:mm" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="putinWeight" cname="重量" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="refWidth" cname="宽度 单位:mm" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="customerId" cname="客户代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="remark" cname="备注" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="locationName" cname="库位名称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="factoryArea" cname="厂区" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="crossArea" cname="跨区" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="surfaceGrade" cname="表面等级" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="avaliableMinLength" cname="条状单个库位最小可用长度(单位：厘米cm)" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="occupiedFlag" cname="已占用库位标记" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="x_pointStartOrign" cname="X轴起始点初始值" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="x_pointEndOrign" cname="X轴结束点初始值" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="pointLowerLength" cname="点状库位长度下限" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="pointUpperLength" cname="点状库位长度上限" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="center" width="120" enable="false"/>
            </EF:EFGrid>
        </div>
    </EF:EFTab>
    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--区域代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS0101" id="crossingChannelsInfo" width="90%" height="60%"/>
    <%--跨区编码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS0201" id="crossAreaInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS0602" id="pollingSchemeNumberInfo" width="90%" height="60%"/>

</EF:EFPage>
