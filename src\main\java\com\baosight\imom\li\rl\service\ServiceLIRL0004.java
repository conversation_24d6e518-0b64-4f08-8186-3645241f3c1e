package com.baosight.imom.li.rl.service;


import com.baosight.imom.li.rl.dao.LIRL0102;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.MapUtils;

import java.util.Map;


/**
 * @Author: 韩亚宁
 * @Description: ${车辆司机信息维护}
 * @Date: 2024/8/14 09:37
 * @Version: 1.0
 */
public class ServiceLIRL0004 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0102().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0102.QUERY);
        return outInfo;
    }




}
