/**
 * 公告板系统 - 兼容性优化版本
 * 保持jQuery依赖，但应用核心优化改进
 */

$(document).ready(function () {
    // 使用命名空间避免全局变量污染
    window.BulletinBoardApp = {
        // 配置常量
        CONFIG: {
            LAYOUTS: { NINE: '9', SIXTEEN: '16', ALL: 'all' },
            REFRESH_INTERVAL: 20000,
            STATUS: { WORKING: '20', PENDING: '10' },
            WARNING_TIME: 120
        },

        // 状态管理
        state: {
            currentPage: 1,
            totalPages: 1,
            currentLayout: localStorage.getItem('layout') || '9',
            rightPanelVisible: localStorage.getItem('rightPanelVisible') !== 'false',
            itemsPerPage: 9,
            rawData: null,
            filteredHandPoints: [],
            bullList: [],
            autoScrollInterval: null
        },

        // 初始化
        init: function () {
            // 确保currentLayout是字符串类型
            this.state.currentLayout = String(this.state.currentLayout);
            this.state.itemsPerPage = this.state.currentLayout === 'all' ? Infinity : parseInt(this.state.currentLayout);
            this.initRealtimeClock();
            this.loadData();
        },

        // 初始化实时时钟
        initRealtimeClock: function () {
            const self = this;
            this.updateClock();
            // 每秒更新一次时间
            setInterval(function() {
                self.updateClock();
            }, 1000);
        },

        // 更新时钟显示
        updateClock: function () {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            const timeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            $('#realtime-clock').text(timeString);
        },

        // HTML转义，防止XSS攻击
        escapeHtml: function (text) {
            if (typeof text !== 'string') return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // 格式化客户名称
        formatCustomerName: function (customerName) {
            const trimmed = customerName ? customerName.trim() : '';
            return trimmed ? '-' + this.escapeHtml(trimmed) : '';
        },

        // 格式化运输起点
        formatStartOfTransport: function (startValue) {
            return startValue ? '-' + this.escapeHtml(startValue) : '';
        },

        // 生成单个项目HTML - 提取公共逻辑
        generateItemHTML: function (item) {
            const className = item.status == this.CONFIG.STATUS.WORKING ? 'working' : 'pending';
            const waitingTime = (item.hashMap && item.hashMap.waitingTime) || 0;
            // const isOvertime = [10, 20].includes(Number(item.status)) && waitingTime >= this.CONFIG.WARNING_TIME;

            const customerName = this.formatCustomerName(item.hashMap && item.hashMap.customerName);
            const startOfTransport = this.formatStartOfTransport(item.hashMap && item.hashMap.startOfTransport);

            const vehicleNo = this.escapeHtml((item.hashMap && item.hashMap.vehicleNo) || '');
            const businessType = this.escapeHtml((item.hashMap && item.hashMap.businessType) || '');

            return '<div class="div-' + className + '-tag"' +
                // (isOvertime ? ' style="background: #FF0000;"' : '')
                '>' +
                vehicleNo + '-' + waitingTime + '分<br>' +
                businessType + customerName + startOfTransport +
                '</div>';
        },

        // 生成门点HTML - 统一逻辑
        generateDoorHTML: function (handPoint) {
            const self = this;
            const filterData = this.state.bullList.filter(function (l) {
                return l.handPointId == handPoint.handPointId;
            });

            const itemsHTML = filterData.map(function (item) {
                return self.generateItemHTML(item);
            }).join('');

            return '<div class="list-one">' +
                '<div class="list-one-title">' +
                '<div class="list-one-title-name">' + this.escapeHtml(handPoint.handPointName) + '</div>' +
                '</div>' +
                '<div class="list-one-con">' +
                '<div class="list-one-con-wrapper">' +
                '<div class="list-one-con-content">' +
                itemsHTML +
                '</div></div></div></div>';
        },

        // 请求数据 - 改进错误处理
        loadData: function () {
            const self = this;
            const searchObj = new URLSearchParams(window.location.search);
            const queryData = {
                segNo: searchObj.get('segNo') || 'JC000000',
                serviceId: 'S_LI_RL_0138'
            };

            $.ajax({
                type: "post",
                contentType: "application/json",
                url: ytjServerUrl,
                async: true,
                data: JSON.stringify(queryData),
                success: function (data) {
                    try {
                        if (!data || !data.__sys__ || data.__sys__.status == -1) {
                            self.handleError('数据请求失败', data.__sys__ && data.__sys__.msg);
                            return;
                        }

                        self.updateCounters(data);
                        self.state.rawData = data;
                        self.initLayout();
                    } catch (error) {
                        self.handleError('数据处理失败', error.message);
                    }
                },
                error: function (xhr, status, error) {
                    self.handleError('网络请求失败', error);
                }
            });
        },

        // 更新计数器 - 优化计算逻辑
        updateCounters: function (data) {
            const waitingList = data.waitingList || [];
            const workingList = data.workingList || [];

            const waitSum = waitingList.reduce(function (sum, w) {
                return sum + Number(w.count || 0);
            }, 0);

            const workSum = workingList.reduce(function (sum, w) {
                return sum + Number(w.count || 0);
            }, 0);

            $('#waitCon').text(waitSum);
            $('#workCon').text(workSum);
        },

        // 错误处理
        handleError: function (message, error) {
            console.error(message, error);

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: message,
                    text: error || '请稍后重试',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            } else {
                alert(message + ': ' + (error || '请稍后重试'));
            }
        },

        // 初始化布局
        initLayout: function () {
            const self = this;

            // 布局按钮点击
            $('#layoutBtn').off('click').on('click', function (e) {
                $('#layoutMenu').toggle();
                e.stopPropagation();
            });

            // 布局菜单点击
            $('#layoutMenu div').off('click').on('click', function () {
                const dataItemValue = $(this).data('items');
                if (dataItemValue != 'noneList') {
                    // 确保值是字符串类型
                    self.state.currentLayout = String(dataItemValue);
                    localStorage.setItem('layout', self.state.currentLayout);
                } else {
                    self.state.rightPanelVisible = !self.state.rightPanelVisible;
                    localStorage.setItem('rightPanelVisible', self.state.rightPanelVisible);
                }

                self.applyLayout();
                $('#layoutMenu').hide();
            });

            // 点击外部关闭菜单
            $(document).off('click.layoutMenu').on('click.layoutMenu', function () {
                $('#layoutMenu').hide();
            });

            this.applyLayout();
        },

        // 应用布局设置
        applyLayout: function () {
            const $left = $('#con-left');
            const $right = $('.con-right');

            // 清除旧样式
            $left.removeClass('layout-9 layout-16 layout-all visible-layout-9 visible-layout-16');

            // 重要：先设置正确的itemsPerPage，再处理数据
            switch (this.state.currentLayout) {
                case '9':
                    this.state.itemsPerPage = 9;
                    break;
                case '16':
                    this.state.itemsPerPage = 16;
                    break;
                case 'all':
                    this.state.itemsPerPage = Infinity;
                    break;
                default:
                    // 默认设置为9
                    this.state.itemsPerPage = 9;
                    break;
            }

            // 重要：清空容器内容，确保布局切换时状态正确
            $left.empty();

            if (this.state.rightPanelVisible) {
                this.showRightPanel($left, $right);
            } else {
                this.hideRightPanel($left, $right);
            }

            $('#noneList').text(this.state.rightPanelVisible ? '隐藏右侧列表' : '显示右侧列表');

            this.processDoorData(this.state.rawData);
            this.startAutoScroll();
        },

        // 显示右侧面板
        showRightPanel: function ($left, $right) {
            $right.show();
            $left.css('width', '70%');

            // 根据布局设置CSS类（itemsPerPage已经在applyLayout中设置了）
            switch (this.state.currentLayout) {
                case '9':
                    $left.addClass('layout-9');
                    break;
                case '16':
                    $left.addClass('layout-16');
                    break;
                case 'all':
                    $left.addClass('layout-all');
                    break;
            }

            // 更新右侧面板内容
            this.updateRightPanelContent();
        },

        // 隐藏右侧面板
        hideRightPanel: function ($left, $right) {
            $right.hide();
            $left.css('width', '100%');

            // 根据布局设置CSS类（itemsPerPage已经在applyLayout中设置了）
            switch (this.state.currentLayout) {
                case '9':
                    $left.addClass('visible-layout-9');
                    break;
                case '16':
                    $left.addClass('visible-layout-16');
                    break;
                case 'all':
                    $left.addClass('layout-all');
                    break;
            }
        },

        // 更新右侧面板内容
        updateRightPanelContent: function () {
            if (this.state.rawData) {
                this.lineUpAndCallForNumbers(this.state.rawData.listCallNum || []);
                this.getNotPlace(this.state.rawData.allocVehicleList || []);
            }
        },

        // 处理门点数据
        processDoorData: function (data) {
            if (!data) return;

            const handPointIdList = data.handPointIdList || [];
            const list = data.list || [];

            this.state.filteredHandPoints = handPointIdList;
            this.state.bullList = list;

            if (this.state.currentLayout === 'all') {
                this.renderAllDoors();
            } else {
                this.state.totalPages = Math.ceil(handPointIdList.length / this.state.itemsPerPage);
                this.state.currentPage = 1;
                this.renderCurrentPage();
            }
        },

        // 渲染当前页面
        renderCurrentPage: function () {
            const startIndex = (this.state.currentPage - 1) * this.state.itemsPerPage;
            const endIndex = startIndex + this.state.itemsPerPage;
            const currentPageData = this.state.filteredHandPoints.slice(startIndex, endIndex);

            const self = this;
            const html = currentPageData.map(function (handPoint) {
                return self.generateDoorHTML(handPoint);
            }).join('');

            // 获取容器并创建新页面元素
            const $container = $('#con-left');

            // 如果容器不为空，先清空（防止重复调用时出现问题）
            if ($container.children().length > 0) {
                $container.empty();
            }

            const $newPage = $('<div class="page-container incoming"></div>').html(html);
            $container.append($newPage);

            // 触发动画
            setTimeout(function () {
                $newPage.removeClass('incoming').addClass('active');
            }, 50);
        },

        // 渲染所有门点 - 修复全部展示模式
        renderAllDoors: function () {
            const self = this;
            const html = this.state.filteredHandPoints.map(function (handPoint) {
                return self.generateDoorHTML(handPoint);
            }).join('');

            // 全部展示模式直接放到容器中，不使用page-container
            $('#con-left').html(html);
        },

        // 叫号数据 - 改进安全性，先清空容器
        lineUpAndCallForNumbers: function (data) {
            const self = this;
            const html = data.map(function (item) {
                const customerName = self.formatCustomerName(item.customerName);
                const startOfTransport = self.formatStartOfTransport(item.startOfTransport);
                const vehicleNo = self.escapeHtml(item.vehicleNo || '');
                const targetHandPointName = self.escapeHtml(item.targetHandPointName || '');
                const businessType = self.escapeHtml(item.businessType || '');
                const waitingTime = item.waitingTime || 0;

                return '<div class="all-con-one">' +
                    vehicleNo + '-' + targetHandPointName + '-' + waitingTime + '分<br>' +
                    businessType + customerName + startOfTransport +
                    '</div>';
            }).join('');

            // 先清空再设置内容 - 修复数据累积问题
            $('#all-con-content-list').html(html);
        },

        // 已配单未进场 - 先清空容器
        getNotPlace: function (data) {
            const self = this;
            const html = data.map(function (item) {
                console.log(item, '你干嘛')
                const vehicleNo = self.escapeHtml(item.vehicleNo || '');
                const waitingTime = item.waitingTime || 0;
                return '<div class="all-con-one">' + vehicleNo + '-' + waitingTime + '分' + '</div>';
            }).join('');

            // 先清空再设置内容 - 修复数据累积问题
            $('#all-con-content-right-list').html(html);
        },

        // 启动自动滚动
        startAutoScroll: function () {
            const self = this;
            this.stopAutoScroll();

            this.state.autoScrollInterval = setInterval(function () {
                if (self.state.currentLayout === 'all') {
                    // 刷新数据而不是重载页面
                    self.loadData();
                } else {
                    if (self.state.currentPage >= self.state.totalPages) {
                        self.loadData();
                    } else {
                        self.state.currentPage++;
                        self.renderCurrentPage();
                    }
                }
            }, this.CONFIG.REFRESH_INTERVAL);
        },

        // 停止自动滚动
        stopAutoScroll: function () {
            if (this.state.autoScrollInterval) {
                clearInterval(this.state.autoScrollInterval);
                this.state.autoScrollInterval = null;
            }
        }
    };

    // 初始化应用
    window.BulletinBoardApp.init();

    // 页面卸载时清理资源
    $(window).on('beforeunload', function () {
        if (window.BulletinBoardApp) {
            window.BulletinBoardApp.stopAutoScroll();
        }
    });
});

