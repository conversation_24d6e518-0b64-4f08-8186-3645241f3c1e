<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XSLVOrg">
    <sql id="getLevelManagerResource">
        WITH org(PARENT_ORG_ID, ORG_ID,ORG_ENAME,ORG_CNAME,NODE_TYPE) AS (
        SELECT PARENT_ORG_ID, ORG_ID,ORG_ENAME,ORG_CNAME,'currentNode' as NODE_TYPE
        FROM ${platSchema}.TXSOG01
        WHERE ORG_ID = #orgId# and is_deleted != '1'
        UNION ALL
        SELECT b.PARENT_ORG_ID, b.ORG_ID,b.ORG_ENAME,b.ORG_CNAME,'parentNode' as NODE_TYPE
        FROM org a, ${platSchema}.TXSOG01 b
        WHERE b.ORG_ID = a.PARENT_ORG_ID and b.is_deleted != '1'
        )
    </sql>

    <select id="queryLevelManagerResource" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        <include refid="getLevelManagerResource"/>
        SELECT
            org.ORG_ID as orgId,
            org.ORG_ENAME as orgEname,
            org.ORG_CNAME as orgCname,
            org.NODE_TYPE as nodeType,
            l.OBJECT_TYPE as objectType,
            CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_ENAME
                ELSE r.RESOURCE_ENAME
            END AS "objectEname",
            CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_CNAME
                ELSE
                    CASE r.TYPE WHEN 'PAGE' THEN f.FORM_CNAME
                        WHEN 'BUTTON' THEN b.BUTTON_CNAME
                    ELSE r.RESOURCE_ENAME END
            END AS "objectName"
        FROM org join ${platSchema}.TXSLV02 l on org.ORG_ID = l.ORG_ID
        join ${platSchema}.TXSLV01 lm on lm.ORG_ID = org.ORG_ID AND lm.ORG_ADM_ID = #orgAdmId#
        LEFT OUTER JOIN ${platSchema}.XS_RESOURCE_GROUP rg ON l.OBJECT_ID = rg.ID AND l.OBJECT_TYPE = 'RESOURCE_GROUP'
        LEFT OUTER JOIN ${platSchema}.XS_RESOURCE r ON l.OBJECT_ID = r.ID AND l.OBJECT_TYPE = 'RESOURCE'
        LEFT OUTER JOIN ${platSchema}.TEDFA00 f on r.TYPE = 'PAGE' AND r.RESOURCE_ENAME = f.FORM_ENAME
        LEFT OUTER JOIN ${platSchema}.TEDFA01 b on r.TYPE = 'BUTTON' AND r.RESOURCE_ENAME = b.FORM_ENAME||b.BUTTON_ENAME
        where 1=1
    </select>

    <select id="queryLevelResource" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        l.ORG_ID as orgId,
        l.OBJECT_TYPE as objectType,
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_ENAME
        ELSE r.RESOURCE_ENAME
        END AS "objectEname",
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_CNAME
        ELSE
        CASE r.TYPE WHEN 'PAGE' THEN f.FORM_CNAME
        WHEN 'BUTTON' THEN b.BUTTON_CNAME
        ELSE r.RESOURCE_ENAME END
        END AS "objectName"
        FROM  ${platSchema}.TXSLV02 l
        LEFT OUTER JOIN ${platSchema}.XS_RESOURCE_GROUP rg ON l.OBJECT_ID = rg.ID AND l.OBJECT_TYPE = 'RESOURCE_GROUP'
        LEFT OUTER JOIN ${platSchema}.XS_RESOURCE r ON l.OBJECT_ID = r.ID AND l.OBJECT_TYPE = 'RESOURCE'
        LEFT OUTER JOIN ${platSchema}.TEDFA00 f on r.TYPE = 'PAGE' AND r.RESOURCE_ENAME = f.FORM_ENAME
        LEFT OUTER JOIN ${platSchema}.TEDFA01 b on r.TYPE = 'BUTTON' AND r.RESOURCE_ENAME = b.FORM_ENAME||b.BUTTON_ENAME
        where 1=1 AND l.ORG_ID = #orgId#
    </select>


    <select id="queryLevelManagerResourceMenu" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        <include refid="getLevelManagerResource"/>
        SELECT
        org.ORG_ID as orgId,
        org.ORG_ENAME as orgEname,
        org.ORG_CNAME as orgCname,
        org.NODE_TYPE as nodeType,
        l.OBJECT_TYPE as objectType,
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_ENAME
        ELSE r.RESOURCE_ENAME
        END AS "objectEname",
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_CNAME
        ELSE
        CASE r.TYPE WHEN 'PAGE' THEN f.FORM_CNAME
        WHEN 'BUTTON' THEN b.BUTTON_CNAME
        ELSE r.RESOURCE_ENAME
        END AS "objectName"
        FROM org join ${platSchema}.TXSLV02 l on org.ORG_ID = l.ORG_ID
        join ${platSchema}.TXSLV01 lm on lm.ORG_ID = org.ORG_ID AND lm.ORG_ADM_ID = #orgAdmId#
        JOIN ${platSchema}.XS_RESOURCE r ON l.OBJECT_ID = r.ID AND l.OBJECT_TYPE = 'RESOURCE'
        LEFT OUTER JOIN ${platSchema}.TEDFA00 f on r.TYPE = 'PAGE' AND r.RESOURCE_ENAME = f.FORM_ENAME
        LEFT OUTER JOIN ${platSchema}.TEDFA01 b on r.TYPE = 'BUTTON' AND r.RESOURCE_ENAME = b.FORM_ENAME||b.BUTTON_ENAME
        where 1=1
    </select>

    <select id="queryLevelResourceMenu" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
            tree.node_ename             as "parent",
            res."objectName"          as "text",
            tree.node_type              as "leaf",
            res."objectEname"         as "label",
            res."type",
            res."objectId"
        from (SELECT
        org.ORG_ID as orgId,
        org.ORG_ENAME as orgEname,
        org.ORG_CNAME as orgCname,
        l.OBJECT_TYPE as objectType,
        CASE r.TYPE WHEN 'PAGE' THEN  CONCAT(r.RESOURCE_ENAME, '.PAGE')
            ELSE r.RESOURCE_ENAME END AS "objectEname",
        CASE r.TYPE WHEN 'PAGE' THEN  concat(concat(f.FORM_ENAME, '---'), concat(f.FORM_CNAME, '[页面]'))
            WHEN 'BUTTON' THEN  concat(concat(concat(concat(b.FORM_ENAME, '.'), b.BUTTON_ENAME), '---'),
                concat(concat(f2.FORM_CNAME, '-'), concat(b.BUTTON_CNAME, '[按钮]')))
            ELSE r.RESOURCE_ENAME
        END AS "objectName",
        CASE r.TYPE WHEN 'PAGE' THEN f.FORM_ENAME
            WHEN 'BUTTON' THEN b.FORM_ENAME
        END AS "formEname",
        r.TYPE as "type",
        r.ID as "objectId"
        FROM ${platSchema}.TXSOG01 org join ${platSchema}.TXSLV02 l on org.ORG_ID = l.ORG_ID
        AND org.ORG_ID = #orgId#
        JOIN ${platSchema}.XS_RESOURCE r ON l.OBJECT_ID = r.ID AND l.OBJECT_TYPE = 'RESOURCE'
        LEFT JOIN ${platSchema}.TEDFA00 f on r.TYPE = 'PAGE' AND r.RESOURCE_ENAME = f.FORM_ENAME
        LEFT JOIN ${platSchema}.TEDFA01 b on r.TYPE = 'BUTTON' AND r.RESOURCE_ENAME = b.FORM_ENAME||'.'||b.BUTTON_ENAME
        LEFT JOIN  ${platSchema}.TEDFA00 f2 on b.FORM_ENAME = f2.FORM_ENAME
        where 1=1
        ) res join ${platSchema}.TEDPI10 tree
        on res."formEname" = tree.NODE_ENAME
    </select>

    <!--查询哪些组织机构是否有管理权限，支持模糊匹配查询，返回有行则有管理权限，否则没有权限-->
    <select id="queryOrgWithLevelPermission" parameterClass="java.util.HashMap"
        resultClass="java.util.HashMap">
        WITH org (ORG_ID,PARENT_ORG_ID,"leaf",ORI_ORG_ID,ORI_ORG_ENAME,ORI_ORG_CNAME) AS (
        SELECT
        ORG_ID  ,
        PARENT_ORG_ID,
        '1'  as "leaf",
        ORG_ID as ORI_ORG_ID,
        ORG_ENAME as ORI_ORG_ENAME,
        ORG_CNAME as ORI_ORG_CNAME
        FROM  ${platSchema}.TXSOG01
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgCname">
            ORG_CNAME LIKE ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgEname">
            ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
        AND IS_DELETED = '0'
        UNION ALL
        SELECT
        b.ORG_ID as "label",
        b.PARENT_ORG_ID,
        '0' as "leaf",
        a.ORI_ORG_ID,
        a.ORI_ORG_ENAME,
        a.ORI_ORG_CNAME
        FROM org a,${platSchema}.TXSOG01 b
        WHERE a.PARENT_ORG_ID = b.ORG_ID AND a.ORG_ID != 'root' AND b.IS_DELETED = '0'
        )
        SELECT
        org.ORI_ORG_ID as "label",
        org.ORI_ORG_ENAME as "orgEname",
        org.ORI_ORG_CNAME as "text",
        "leaf",
        lm.ORG_ID as "levelParentOrgId",
        lm.ORG_PERM_MANAGER	as "orgPermManager",  <!-- 维护分级管理员权限 -->
        lm.ORG_PERM_RES_RANGE	as "orgPermResRange",  <!-- 分级资源授权范围管理权限 -->
        lm.ORG_PERM_ORG_MAPPING	as "orgPermOrgMapping",  <!-- 组织机构映射权限 -->
        lm.ORG_PERM_USER_GROUP	as "orgPermUserGroup",  <!-- 用户组维护权限 -->
        lm.ORG_PERM_USER_GROUP_MEMBRER	as "orgPermUserGroupMembrer",  <!-- 用户组成员维护权限 -->
        lm.ORG_PERM_AUTH	as "orgPermAuth",  <!-- 授权关系维护权限 -->
        lm.ORG_PERM_ORG	as "orgPermOrg" <!-- 组织机构维护权限 -->
        FROM org join ${platSchema}.TXSLV01 lm on org.ORG_ID = lm.ORG_ID
        where lm.ORG_ADM_ID = #userId#
        <!-- 分级管理员、分级授权资源范围、组织机构维护权限只能划分到子组织，其余可以划分当前管理组织 -->
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            lm.ORG_PERM_MANAGER = '1' AND org.ORI_ORG_ID != lm.ORG_ID
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            lm.ORG_PERM_RES_RANGE = '1' AND org.ORI_ORG_ID != lm.ORG_ID
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            lm.ORG_PERM_ORG = '1' AND org.ORI_ORG_ID != lm.ORG_ID
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            lm.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            lm.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            lm.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            lm.ORG_PERM_AUTH = '1'
        </isNotEmpty>
    </select>

    <select id="search" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH org (ORG_ID,PARENT_ORG_ID,"leaf",ORI_ORG_ID,ORI_ORG_ENAME,ORI_ORG_CNAME) AS (
        SELECT
        ORG_ID  ,
        PARENT_ORG_ID,
        '1'  as "leaf",
        ORG_ID as ORI_ORG_ID,
        ORG_ENAME as ORI_ORG_ENAME,
        ORG_CNAME as ORI_ORG_CNAME
        FROM  ${platSchema}.TXSOG01
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgCname">
            ORG_CNAME LIKE ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgEname">
            ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
        AND IS_DELETED = '0'
        UNION ALL
        SELECT
        b.ORG_ID as "label",
        b.PARENT_ORG_ID,
        '0' as "leaf",
        a.ORI_ORG_ID,
        a.ORI_ORG_ENAME,
        a.ORI_ORG_CNAME
        FROM org a,${platSchema}.TXSOG01 b
        WHERE a.PARENT_ORG_ID = b.ORG_ID AND a.ORG_ID != 'root' AND b.IS_DELETED = '0'
        )
        SELECT
        org.ORI_ORG_ID as "label",
        org.ORI_ORG_ENAME as "orgEname",
        org.ORI_ORG_CNAME as "text",
        "leaf"
        FROM org join ${platSchema}.TXSLV01 lm on org.ORG_ID = lm.ORG_ID
        where lm.ORG_ADM_ID = #userId#
        <!-- 分级管理员、分级授权资源范围、组织机构维护权限只能划分到子组织，其余可以划分当前管理组织 -->
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            lm.ORG_PERM_MANAGER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            lm.ORG_PERM_RES_RANGE = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            lm.ORG_PERM_ORG = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            lm.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            lm.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            lm.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            lm.ORG_PERM_AUTH = '1'
        </isNotEmpty>
    </select>

    <select id="expandPath" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH org (ORG_ID,PARENT_ORG_ID,LEVEL,ORG_ENAME,ORI_ORG_ID,ORG_CNAME,ORI_ORG_ENAME,ORI_ORG_CNAME,PATH) AS (
        SELECT
        ORG_ID  ,
        PARENT_ORG_ID,
        1   as LEVEL,
        ORG_ENAME,
        ORG_CNAME,
        ORG_ID as ORI_ORG_ID,
        ORG_ENAME as ORI_ORG_ENAME,
        ORG_CNAME as ORI_ORG_CNAME,
        cast(ORG_ID as varchar(2000)) as PATH
        FROM  ${platSchema}.TXSOG01
            WHERE ORG_ID =#orgId# AND IS_DELETED = '0'
        UNION ALL
        SELECT
        b.ORG_ID ,
        b.PARENT_ORG_ID,
        a.LEVEL + 1 as LEVEL,
        b.ORG_ENAME,
        b.ORG_CNAME,
        a.ORI_ORG_ID,
        a.ORI_ORG_ENAME,
        a.ORI_ORG_CNAME,
        concat(concat(b.ORG_ID,','),a.PATH) as PATH
        FROM org a,${platSchema}.TXSOG01 b
            WHERE a.PARENT_ORG_ID = b.ORG_ID AND a.ORG_ID != 'root' AND b.IS_DELETED = '0'
        )
        select
            org.ORG_ID as "label",
            org.PATH AS "path"
        from org join ${platSchema}.TXSLV01 a on a.ORG_ID = org.ORG_ID where
            a.ORG_ADM_ID =  #userId#
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            a.ORG_PERM_MANAGER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            a.ORG_PERM_RES_RANGE = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            a.ORG_PERM_ORG = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            a.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            a.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            a.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            a.ORG_PERM_AUTH = '1'
        </isNotEmpty>
        ORDER BY LEVEL ASC
    </select>

    <!--组织机构子节点树查询-->
    <select id="queryOrganiation" resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "label",
        ORG_ENAME as "ename",
        ORG_CNAME as "text",
        ORG_NODE_TYPE as "leaf",
        ORG_TYPE as "type",
        PARENT_ORG_ID as "parentOrgId"
        FROM ${platSchema}.TXSOG01 WHERE IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="node">
            PARENT_ORG_ID = #node#
        </isNotEmpty>
        order by SORT_INDEX asc
    </select>

    <!--组织机构子节点树查询-->
    <select id="queryLevelManagerTopOrg" resultClass="java.util.HashMap">
        SELECT
        b.ORG_ID as "label",
        b.ORG_ENAME as "ename",
        b.ORG_CNAME as "text",
        b.ORG_NODE_TYPE as "leaf",
        b.ORG_TYPE as "type",
        'root' as "parentOrgId"
        FROM ${platSchema}.TXSLV01 a join ${platSchema}.TXSOG01 b
        on a.ORG_ID = b.ORG_ID
        WHERE b.IS_DELETED = '0'
        AND a.ORG_ADM_ID = #userId#
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            a.ORG_PERM_MANAGER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            a.ORG_PERM_RES_RANGE = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            a.ORG_PERM_ORG = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            a.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            a.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            a.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            a.ORG_PERM_AUTH = '1'
        </isNotEmpty>
        order by SORT_INDEX asc
    </select>


    <select id="queryUserGroup" resultClass="java.util.HashMap">
        SELECT
        ug.ID as "label",
        ug.GROUP_CNAME as "text",
        1 as "leaf",
        og03.ORG_ID as "parentOrgId"
        FROM ${platSchema}.XS_USER_GROUP ug
        join ${platSchema}.TXSOG03 og03
        on ug.ID = og03.USER_GROUP_ID
        WHERE og03.ORG_ID = #parent_id#
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                GROUP_ENAME asc
            </isEmpty>
        </dynamic>
    </select>



</sqlMap>