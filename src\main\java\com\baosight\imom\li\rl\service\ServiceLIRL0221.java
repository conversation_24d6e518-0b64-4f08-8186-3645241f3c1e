package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.li.rl.dao.LIRL0101;
import com.baosight.imom.li.rl.dao.LIRL0308;
import com.baosight.imom.li.rl.dao.LIRL0312;
import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.MapUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 张博翔
 * @Description: ${加工中心厂内物流作业查询}
 * @Date: 2024/11/11 09:37
 * @Version: 1.0
 */
public class ServiceLIRL0221 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0308().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String isHouseCars = MapUtils.getString(queryBlock, "isHouseCars", "");//是否厂内车
        String businessType = MapUtils.getString(queryBlock, "businessType", "");//业务类型
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        if ("10".equals(isHouseCars)){
            queryBlock.put("noHouse","10");
        } else if ("20".equals(isHouseCars)){
            queryBlock.put("inHouse","20");
        }else if ("30".equals(isHouseCars)){
            queryBlock.put("outHouse","30");
        }else if ("40".equals(isHouseCars)){
            queryBlock.put("outHouse","30");
            queryBlock.put("leaveFactoryDate",format);
        }

        if ("20".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("voucherNumN","1");
        }else if ("60".equals(businessType)){
            queryBlock.put("businessType","20");
            queryBlock.put("voucherNumN","1");
        }else if ("40".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("voucherNumN","1");
        }else if ("50".equals(businessType)){
            queryBlock.put("businessType","40");
            queryBlock.put("voucherNumN","1");
        }else if ("70".equals(businessType)) {
            queryBlock.put("businessType","50");
            queryBlock.put("voucherNumN","1");
        }else if ("10".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("handType","10");
        }else if ("30".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("handType","30");
        }

        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo,"LIRL0221.queryAll");
        outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
        List<HashMap> hashMapList = outInfo.getBlock(EiConstant.resultBlock).getRows();
        extracted(hashMapList,"0");
        return outInfo;
    }

    public EiInfo postExport(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String isHouseCars = MapUtils.getString(queryBlock, "isHouseCars", "");//是否厂内车
        String businessType = MapUtils.getString(queryBlock, "businessType", "");//业务类型
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        if ("10".equals(isHouseCars)){
            queryBlock.put("noHouse","10");
        } else if ("20".equals(isHouseCars)){
            queryBlock.put("inHouse","20");
        }else if ("30".equals(isHouseCars)){
            queryBlock.put("outHouse","30");
        }

        if ("20".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("voucherNumN","1");
        }else if ("60".equals(businessType)){
            queryBlock.put("businessType","20");
            queryBlock.put("voucherNumN","1");
        }else if ("40".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("voucherNumN","1");
        }else if ("50".equals(businessType)){
            queryBlock.put("businessType","40");
            queryBlock.put("voucherNumN","1");
        }else if ("70".equals(businessType)) {
            queryBlock.put("businessType","50");
            queryBlock.put("voucherNumN","1");
        }else if ("10".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("handType","10");
        }else if ("30".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("handType","30");
        }

        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo,"LIRL0221.query");
        outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
        List<HashMap> hashMapList = outInfo.getBlock(EiConstant.resultBlock).getRows();
        extracted(hashMapList,"1");
        return outInfo;
    }

    private static void extracted(List<HashMap> hashMapList,String b) {
        for (HashMap hashMap: hashMapList){
            //判断计算时间为0 给空值
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromRegistrationToEntry",""))){
                hashMap.put("theTimeFromRegistrationToEntry",1);
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromEnteringTheFactoryToTheStartOfTheJob",""))){
                hashMap.put("theTimeFromEnteringTheFactoryToTheStartOfTheJob",1);
            }
            if ("0".equals(MapUtils.getString(hashMap,"theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",""))){
                hashMap.put("theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",1);
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromTheCompletionOfTheJobToTheFactory",""))){
                hashMap.put("theTimeFromTheCompletionOfTheJobToTheFactory",1);
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",""))){
                hashMap.put("theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",1);
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromEnteringTheFactoryToLeavingTheFactory",""))){
                hashMap.put("theTimeFromEnteringTheFactoryToLeavingTheFactory",1);
            }
            if ("0".equals(MapUtils.getString(hashMap,"registeredToTheFactoryTime",""))){
                hashMap.put("registeredToTheFactoryTime",1);
            }
            if ("0 时 0 分".equals(MapUtils.getString(hashMap,"theTimeFromTheFactoryToTheTimeOfReceipt",""))){
                hashMap.put("theTimeFromTheFactoryToTheTimeOfReceipt",1);
            }
            if ("1".equals(b)){
                String isReservation = MapUtils.getString(hashMap, "isReservation", "");
                switch (isReservation) {
                    case "00":
                        hashMap.put("isReservation", "无预约单");
                        break;
                    case "10":
                        hashMap.put("isReservation", "有预约单");
                        break;
                }

                String businessType = MapUtils.getString(hashMap, "businessType", "");
                switch (businessType) {
                    case "10":
                        hashMap.put("businessType", "钢材装货");
                        break;
                    case "20":
                        hashMap.put("businessType", "钢材卸货");
                        break;
                    case "30":
                        hashMap.put("businessType", "钢材卸货+装货");
                        break;
                    case "40":
                        hashMap.put("businessType", "托盘运输");
                        break;
                    case "50":
                        hashMap.put("businessType", "资材卸货");
                        break;
                    case "60":
                        hashMap.put("businessType", "废料提货");
                        break;
                    case "70":
                        hashMap.put("businessType", "欧冶提货");
                        break;
                }

                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "撤销");
                        break;
                    case "05":
                        hashMap.put("status", "新增");
                        break;
                    case "10":
                        hashMap.put("status", "进厂登记");
                        break;
                    case "20":
                        hashMap.put("status", "车辆进厂");
                        break;
                    case "30":
                        hashMap.put("status", "开始装卸货");
                        break;
                    case "40":
                        hashMap.put("status", "结束装卸货");
                        break;
                    case "50":
                        hashMap.put("status", "车辆出厂");
                        break;
                    case "60":
                        hashMap.put("status", "车辆签收");
                        break;
                }
                String appointmentStatus = MapUtils.getString(hashMap, "appointmentStatus", "");
                switch (appointmentStatus) {
                    case "00":
                        hashMap.put("appointmentStatus", "撤销");
                        break;
                    case "20":
                        hashMap.put("appointmentStatus", "生效");
                        break;
                    case "99":
                        hashMap.put("appointmentStatus", "完成");
                        break;
                }

                String lateEarlyFlag = MapUtils.getString(hashMap, "lateEarlyFlag", "");
                switch (lateEarlyFlag) {
                    case "0":
                        hashMap.put("lateEarlyFlag", "未标记");
                        break;
                    case "10":
                        hashMap.put("lateEarlyFlag", "迟到");
                        break;
                    case "20":
                        hashMap.put("lateEarlyFlag", "早到");
                        break;
                    case "30":
                        hashMap.put("lateEarlyFlag", "正常");
                        break;
                }
                String handType = MapUtils.getString(hashMap, "handType", "");
                switch (handType) {
                    case "10":
                        hashMap.put("handType", "装货");
                        break;
                    case "20":
                        hashMap.put("handType", "卸货");
                        break;
                    case "30":
                        hashMap.put("handType", "卸货+装货");
                        break;
                    case "40":
                        hashMap.put("handType", "托盘运输");
                        break;
                }
                String nextTarget = MapUtils.getString(hashMap, "nextTarget", "");
                switch (nextTarget) {
                    case "10":
                        hashMap.put("handType", "下一装卸点");
                        break;
                    case "20":
                        hashMap.put("handType", "离厂");
                        break;
                }
            }
        }
    }

    /**
     * 附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String relevanceId = MapUtils.getString(queryBlock, "relevanceId", "");
        List<String> stringList = new ArrayList<>();
        stringList.add(relevanceId);
        //查询车辆作业实绩表 出库单号
        List<LIRL0308> lirl0308s = dao.query(LIRL0308.QUERY, queryBlock);
        for (LIRL0308 lirl0308:lirl0308s){
            stringList.add(lirl0308.getPutoutId());
        }
        queryBlock.put("relevanceIdList",stringList);
        queryBlock.put("relevanceId","");
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0312.QUERY, new LIRL0312(), false, null, "sub_query_status", "sub_result", "sub_result");
        return outInfo;
    }

    /**
     * 捆包附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery2(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub2_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String packId = MapUtils.getString(queryBlock, "packId", "");
        queryBlock.put("relevanceType",packId);
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0312.QUERY, new LIRL0312(), false, null, "sub2_query_status", "sub_result2", "sub_result2");
        return outInfo;
    }

    /**
     * 安全告知签章附件附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery3(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub3_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String relevanceId = MapUtils.getString(queryBlock, "relevanceId", "");
        List<String> stringList = new ArrayList<>();
        stringList.add(relevanceId);
        queryBlock.put("relevanceIdList",stringList);
        queryBlock.put("relevanceId","");
        queryBlock.put("fifleType",".pdf");
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0312.QUERY, new LIRL0312(), false, null, "sub3_query_status", "sub_result3", "sub_result3");
        return outInfo;
    }

    /**
     * 安全告知签章附件附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery4(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub4_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String relevanceId = MapUtils.getString(queryBlock, "relevanceId", "");
        List<String> stringList = new ArrayList<>();
        stringList.add(relevanceId);
        queryBlock.put("relevanceIdList",stringList);
        queryBlock.put("relevanceId","");
        queryBlock.put("fifleType",".pdf");
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0312.QUERY, new LIRL0312(), false, null, "sub4_query_status", "sub_result4", "sub_result4");
        return outInfo;
    }

    public EiBlockMeta getExportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("isReservation");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("是否预约");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setFieldLength(23);
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("车辆跟踪单状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startOfTransport");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("运输起始地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purposeOfTransport");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("运输目的地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("telNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("idCard");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("appointmentStatus");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约单状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lateEarlyFlag");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("迟到早到标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("叫号时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactory");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("beginEntruckingTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("completeUninstallTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业完成时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handType");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("装卸类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nextTarget");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("下一目标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("目标装卸点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("目标装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("当前装卸点");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("currentHandPointName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("当前装卸点名称");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("factoryArea");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("厂区编码");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("厂区名称");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromRegistrationToEntry");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("登记至进厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromEnteringTheFactoryToTheStartOfTheJob");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂至作业开始时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业开始至作业完成时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromTheCompletionOfTheJobToTheFactory");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业完成至出厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromEnteringTheFactoryToTheCompletionOfTheJob");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂至作业完成时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromEnteringTheFactoryToLeavingTheFactory");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂至出厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("registeredToTheFactoryTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("登记至出厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("signOffTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("签收时间");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromTheFactoryToTheTimeOfReceipt");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("出厂至签收时长(时分)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("finalStationLongitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("终到站经度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("finalStationLatitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("终到站纬度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("signingLocationLongitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("签收地经度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("signingLocationLatitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("签收地纬度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("longitudeLatitudeCheck");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("经纬度校验");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("voucherNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("提单号");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("备注");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建人");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建人姓名");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建时间");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改人");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改人姓名");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改时间");

        return eiMetadata;
    }
}
