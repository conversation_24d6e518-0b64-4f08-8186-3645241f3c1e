<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2025-01-21 11:21:57
       Version :  1.0
    tableName :mevp.tvppl0102
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CHIP_ID  VARCHAR   NOT NULL,
     STAFF_NAME  VARCHAR,
     AREA_NAME  VARCHAR   NOT NULL,
     ACTION_TYPE  VARCHAR,
     WORKING_HOURS  VARCHAR,
     WORKING_TIME  VARCHAR,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL
-->
<sqlMap namespace="VPPL0102">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vp.pl.domain.VPPL0102">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        SERIAL_NUMBER as "serialNumber",
        CHIP_ID as "chipId",  <!-- 芯片ID -->
        EMP_NO as "empNo",  <!-- 员工工号 -->
        STAFF_NAME as "staffName",  <!-- 人员姓名 -->
        AREA_NAME as "areaName",  <!-- 区域名称 -->
        ACTION_TYPE as "actionType",  <!-- 动作类型(1、进入 2、离开) -->
        WORKING_HOURS as "workingHours",  <!-- 有效工作时间 -->
        WORKING_TIME as "workingTime",  <!-- 操作时间 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        WORKING_DATE as "workingDate",
        WORKING_SHIFT as "workingShift",
        DEPARTURE_HOURS AS "departureHours"
        FROM mevp.tvppl0102 t WHERE 1=1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="chipId">
            t.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            t.STAFF_NAME like '%$staffName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="teamId">
            EXISTS(SELECT 1
            FROM mevp.tvppl0101 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.CHIP_ID = t.CHIP_ID
            AND t1.EMP_NO = t.EMP_NO
            AND t1.TEAM_ID = #teamId#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingDate">
            t.WORKING_DATE = #workingDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingShift">
            t.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateStart">
            SUBSTR(t.WORKING_TIME, 1, 14) >= #productDateStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateEnd">
            #productDateEnd# >= SUBSTR(t.WORKING_TIME, 1, 14)
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SERIAL_NUMBER DESC, ACTION_TYPE DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM mevp.tvppl0102 t WHERE 1=1
        <isNotEmpty prepend="AND" property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="chipId">
            t.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            t.STAFF_NAME like '%$staffName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="teamId">
            EXISTS(SELECT 1
            FROM mevp.tvppl0101 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.CHIP_ID = t.CHIP_ID
            AND t1.EMP_NO = t.EMP_NO
            AND t1.TEAM_ID = #teamId#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingDate">
            t.WORKING_DATE = #workingDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingShift">
            t.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateStart">
            SUBSTR(t.WORKING_TIME, 1, 14) >= #productDateStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateEnd">
            #productDateEnd# >= SUBSTR(t.WORKING_TIME, 1, 14)
        </isNotEmpty>
    </select>

    <select id="countEqual" resultClass="int">
        SELECT COUNT(*) FROM mevp.tvppl0102 WHERE 1=1
        AND SEG_NO = #segNo#
        AND CHIP_ID = #chipId#
        AND ACTION_TYPE = #actionType#
        AND WORKING_TIME = #workingTime#
        AND AREA_NAME = #areaName#
        <isNotEmpty prepend=" AND " property="staffName">
            STAFF_NAME = #staffName#
        </isNotEmpty>
    </select>


    <!--页面查询汇总-->
    <select id="sumTotal" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT ROUND(SUM(WORKING_HOURS),2) as "workingHours"
        FROM mevp.tvppl0102 t WHERE 1=1
        <isNotEmpty prepend=" AND " property="chipId">
            CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            STAFF_NAME like '%$staffName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateStart">
            SUBSTR(t.WORKING_TIME, 1, 14) >= #productDateStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateEnd">
            #productDateEnd# >= SUBSTR(t.WORKING_TIME, 1, 14)
        </isNotEmpty>
    </select>
    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="chipId">
            CHIP_ID = #chipId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            STAFF_NAME = #staffName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="areaName">
            AREA_NAME = #areaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="actionType">
            ACTION_TYPE = #actionType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingHours">
            WORKING_HOURS = #workingHours#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingTime">
            WORKING_TIME = #workingTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO mevp.tvppl0102 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        CHIP_ID,  <!-- 芯片ID -->
        EMP_NO,  <!-- 人员姓名 -->
        STAFF_NAME,  <!-- 人员姓名 -->
        AREA_NAME,  <!-- 区域名称 -->
        ACTION_TYPE,  <!-- 动作类型(1、进入 2、离开) -->
        WORKING_HOURS,  <!-- 有效工作时间 -->
        WORKING_TIME,  <!-- 操作时间 -->
        STATUS,  <!-- 状态 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        WORKING_DATE,
        WORKING_SHIFT,
        DEPARTURE_HOURS,
        SERIAL_NUMBER
        )
        VALUES (#segNo#, #unitCode#, #chipId#, #empNo#, #staffName#, #areaName#, #actionType#, #workingHours#,
        #workingTime#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#,
        #uuid#,#workingDate#,#workingShift#,#departureHours#,#serialNumber#)
    </insert>

    <delete id="delete">
        DELETE FROM mevp.tvppl0102 WHERE
    </delete>

    <update id="update">
        UPDATE mevp.tvppl0102
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        CHIP_ID = #chipId#,   <!-- 芯片ID -->
        EMP_NO = #empNo#,   <!-- 员工工号 -->
        STAFF_NAME = #staffName#,   <!-- 人员姓名 -->
        AREA_NAME = #areaName#,   <!-- 区域名称 -->
        ACTION_TYPE = #actionType#,   <!-- 动作类型(1、进入 2、离开) -->
        WORKING_HOURS = #workingHours#,   <!-- 有效工作时间 -->
        WORKING_TIME = #workingTime#,   <!-- 操作时间 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        UUID = #uuid#  <!-- ID -->
        WHERE
    </update>

    <!--	依据UUID修改数据-->
    <update id="updateByUuId">
        UPDATE mevp.tvppl0102
        SET
        REC_REVISOR = #recRevisor#,
        REC_REVISOR_NAME = #recRevisorName#,
        REC_REVISE_TIME = #recReviseTime#
        <isNotEmpty prepend=" , " property="chipId">
            CHIP_ID = #chipId#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="staffName">
            STAFF_NAME = #staffName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="areaName">
            AREA_NAME = #areaName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="actionType">
            ACTION_TYPE = #actionType#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="workingHours">
            WORKING_HOURS = #workingHours#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="workingTime">
            WORKING_TIME = #workingTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="workingDate">
            WORKING_DATE = #workingDate#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="workingShift">
            WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        WHERE SEG_NO = #segNo#
        AND UUID = #uuid#
    </update>

    <!--	根据芯片ID，员工姓名，机组查询进入时间-->
    <select id="queryOrderByTime" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vp.pl.domain.VPPL0102">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CHIP_ID as "chipId",  <!-- 芯片ID -->
        STAFF_NAME as "staffName",  <!-- 人员姓名 -->
        AREA_NAME as "areaName",  <!-- 区域名称 -->
        ACTION_TYPE as "actionType",  <!-- 动作类型(1、进入 2、离开) -->
        WORKING_HOURS as "workingHours",  <!-- 有效工作时间 -->
        WORKING_TIME as "workingTime",  <!-- 操作时间 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        WORKING_DATE as "workingDate",
        WORKING_SHIFT as "workingShift",
        SERIAL_NUMBER as "serialNumber"
        FROM mevp.tvppl0102 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND CHIP_ID = #chipId#
        AND STAFF_NAME = #staffName#
        <isNotEmpty prepend="AND" property="actionType">
            <isEqual property="actionType" compareValue="1">
                ACTION_TYPE = '2'
                AND NOT EXISTS (SELECT 1
                FROM mevp.tvppl0102 t2
                WHERE t2.SEG_NO = t.SEG_NO
                AND t2.CHIP_ID = t.CHIP_ID
                AND t2.ACTION_TYPE = '1'
                AND t2.REC_CREATE_TIME >= t.REC_CREATE_TIME)
            </isEqual>
            <isEqual property="actionType" compareValue="2">
                ACTION_TYPE = '1'
                AND AREA_NAME = #areaName#
                AND NOT EXISTS (SELECT 1
                FROM mevp.tvppl0102 t2
                WHERE t2.SEG_NO = t.SEG_NO
                AND t2.CHIP_ID = t.CHIP_ID
                AND t2.AREA_NAME = t.AREA_NAME
                AND t2.ACTION_TYPE = '2'
                AND t2.SERIAL_NUMBER = t.SERIAL_NUMBER
                AND t2.REC_CREATE_TIME >= t.REC_CREATE_TIME)
            </isEqual>
        </isNotEmpty>
        ORDER BY REC_CREATE_TIME DESC
    </select>

    <!--汇总统计所有机组当班人数-->
    <select id="countCrewVisitsToday" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        <!--SELECT ROUND(
        (
        (SELECT SUM(dd.WORKING_HOURS)
        FROM mevp.tvppl0102 dd
        WHERE dd.SEG_NO = mm.SEG_NO
        AND dd.AREA_NAME = mm.AREA_NAME
        AND dd.ACTION_TYPE = mm.ACTION_TYPE)&lt;!&ndash;当前机组所有人在本机组的总工作时间&ndash;&gt;
        /
        (SELECT SUM(dd1.WORKING_HOURS)
        FROM mevp.tvppl0102 dd1
        WHERE dd1.SEG_NO = mm.SEG_NO
        AND dd1.ACTION_TYPE = mm.ACTION_TYPE
        AND dd1.CHIP_ID IN (SELECT DISTINCT dd2.CHIP_ID
        FROM mevp.tvppl0102 dd2
        WHERE dd2.SEG_NO = mm.SEG_NO
        AND dd2.AREA_NAME = mm.AREA_NAME
        AND dd2.ACTION_TYPE = mm.ACTION_TYPE)) &lt;!&ndash;当前机组所有人所有机组的总工作时间&ndash;&gt;
        ) *
        (SELECT COUNT(DISTINCT dd.CHIP_ID)
        FROM mevp.tvppl0102 dd
        WHERE dd.SEG_NO = mm.SEG_NO
        AND dd.AREA_NAME = mm.AREA_NAME
        AND dd.ACTION_TYPE = mm.ACTION_TYPE),2)&lt;!&ndash;当前机组的总人数&ndash;&gt;
        AS "dutyQuantity",&lt;!&ndash;当班人数&ndash;&gt;
        mm.AREA_NAME AS "areaName",&lt;!&ndash;机组名称&ndash;&gt;
        MAX(pm.TEAM_ID) AS "teamId",      &lt;!&ndash;班组&ndash;&gt;
        MAX(pm.WORKING_SHIFT) AS "workingShift" &lt;!&ndash;班次&ndash;&gt;
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON pm.SEG_NO = mm.SEG_NO
        AND pm.CHIP_ID = mm.CHIP_ID
        AND pm.STATUS = '20'
        WHERE mm.SEG_NO = #segNo#
        AND mm.ACTION_TYPE = '2'
        AND mm.WORKING_TIME BETWEEN #startTime# AND #endTime#
        GROUP BY mm.AREA_NAME-->

        SELECT ROUND(
        (
        (SELECT SUM(dd.WORKING_HOURS)
        FROM mevp.tvppl0102 dd
        WHERE dd.SEG_NO = mm.SEG_NO
        AND dd.AREA_NAME = mm.AREA_NAME
        AND dd.ACTION_TYPE = mm.ACTION_TYPE)<!--当前机组所有人在本机组的总工作时间-->
        /#workTime#),2)<!--当前机组的总人数-->
        AS "dutyQuantity",<!--当班人数-->
        mm.AREA_NAME AS "areaName",<!--机组名称-->
        MAX(pm.TEAM_ID) AS "teamId",      <!--班组-->
        MAX(pm.WORKING_SHIFT) AS "workingShift" <!--班次-->
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON pm.SEG_NO = mm.SEG_NO
        AND pm.CHIP_ID = mm.CHIP_ID
        AND pm.STATUS = '20'
        WHERE mm.SEG_NO = #segNo#
        AND mm.ACTION_TYPE = '2'
        AND mm.WORKING_TIME BETWEEN #startTime# AND #endTime#
        GROUP BY mm.AREA_NAME
    </select>

    <!--统计当前机组当班人数-->
    <select id="countShiftPersonnelByMachine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT ROUND(
        SUM(COALESCE(
        t2.WORKING_HOURS,
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(t1.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#endTime#, '%Y%m%d%H%i%s')) / 60.0 ))
        / #workTime#, 2) AS "dutyQuantity"
        FROM mevp.tvppl0102 t1
        LEFT JOIN mevp.tvppl0102 t2
        ON t1.SERIAL_NUMBER = t2.SERIAL_NUMBER
        AND t1.SERIAL_NUMBER &lt;> ''
        AND t1.SERIAL_NUMBER IS NOT NULL
        AND t2.ACTION_TYPE = '2'
        AND STR_TO_DATE(t2.WORKING_TIME, '%Y%m%d%H%i%s')
        BETWEEN STR_TO_DATE(#startTime#, '%Y%m%d%H%i%s')
        AND STR_TO_DATE(#endTime#, '%Y%m%d%H%i%s')
        WHERE t1.ACTION_TYPE = '1'
        AND STR_TO_DATE(t1.WORKING_TIME, '%Y%m%d%H%i%s')
        BETWEEN STR_TO_DATE(#startTime#, '%Y%m%d%H%i%s')
        AND STR_TO_DATE(#endTime#, '%Y%m%d%H%i%s')
        AND t1.AREA_NAME = #areaName#
        AND (
        (t2.SERIAL_NUMBER IS NOT NULL AND t1.SERIAL_NUMBER &lt;> '' AND t1.SERIAL_NUMBER IS NOT NULL)
        OR
        (t2.SERIAL_NUMBER IS NULL AND (t1.SERIAL_NUMBER = '' OR t1.SERIAL_NUMBER IS NULL)
        AND STR_TO_DATE(t1.WORKING_TIME, '%Y%m%d%H%i%s') &lt;= STR_TO_DATE(#endTime#, '%Y%m%d%H%i%s'))
        )
    </select>

    <!--	按员工统计有效工时-->
    <select id="statisticGroupByEmp" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t.SEG_NO AS "segNo",
        t.EMP_NO AS "empNo",
        t.STAFF_NAME AS "staffName",
        t.CHIP_ID AS "chipId",
        MAX(t.POST_RESPONSIBILITY) AS "postResponsibility",
        GROUP_CONCAT(DISTINCT t.AREA_NAME) AS "areaName",
        CAST(SUM(t.WORKING_HOURS) AS DECIMAL(18, 2)) AS "totalWorkingHours",
        CAST(SUM(t.DEPARTURE_HOURS) AS DECIMAL(18, 2)) AS "totalDepartureHours",
        MAX(t.TEAM_ID) AS "teamId",
        t.WORKING_SHIFT AS "workingShift",
        t.WORKING_DATE AS "workingDate"
        FROM (SELECT mm.SEG_NO AS SEG_NO,
        mm.EMP_NO AS EMP_NO,
        mm.STAFF_NAME AS STAFF_NAME,
        mm.CHIP_ID AS CHIP_ID,
        pm.POST_RESPONSIBILITY AS POST_RESPONSIBILITY,
        mm.AREA_NAME AS AREA_NAME,
        mm.DEPARTURE_HOURS AS DEPARTURE_HOURS,
        pm.TEAM_ID AS TEAM_ID,
        mm.WORKING_SHIFT AS WORKING_SHIFT,
        mm.WORKING_DATE AS WORKING_DATE,
        CASE
        <!-- 最后一次打卡是进入，计算到结束时间的时长-->
        WHEN mm.WORKING_TIME = max_times.max_time AND mm.ACTION_TYPE = '1' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#productDateEnd#, '%Y%m%d%H%i%s')) / 60
        <!-- 第一次打卡是离开，计算从开始时间到离开的时长-->
        WHEN mm.WORKING_TIME = min_times.min_time AND mm.ACTION_TYPE = '2' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(#productDateStart#, '%Y%m%d%H%i%s'),
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s')) / 60
        <!-- 中间的工作时间直接累加-->
        WHEN mm.WORKING_TIME > min_times.min_time AND mm.WORKING_TIME
        &lt; max_times.max_time THEN
        mm.WORKING_HOURS
        ELSE
        0
        END AS WORKING_HOURS
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON mm.SEG_NO = pm.SEG_NO
        AND mm.CHIP_ID = pm.CHIP_ID
        AND pm.STATUS = '20'
        JOIN (SELECT mm2.STAFF_NAME, MAX(WORKING_TIME) as max_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) max_times ON mm.STAFF_NAME = max_times.STAFF_NAME
        JOIN (SELECT mm2.STAFF_NAME, MIN(WORKING_TIME) as min_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) min_times ON mm.STAFF_NAME = min_times.STAFF_NAME
        WHERE mm.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm.WORKING_TIME >= #productDateStart#
        AND mm.WORKING_TIME &lt;= #productDateEnd#
        <isNotEmpty prepend=" AND " property="chipId">
            mm.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            mm.STAFF_NAME LIKE '%$staffName$%'  <!-- 支持姓名模糊查询 -->
        </isNotEmpty>
        <isNotEmpty property="teamId">
            AND pm.TEAM_ID = #teamId#
        </isNotEmpty>
        <isNotEmpty property="workingShift">
            AND mm.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty property="workingDate">
            AND mm.WORKING_DATE = #workingDate#
        </isNotEmpty>
        ) t
        WHERE t.WORKING_HOURS > 0 || t.DEPARTURE_HOURS > 0
        GROUP BY t.SEG_NO, t.EMP_NO, t.CHIP_ID, t.STAFF_NAME, t.WORKING_DATE, t.WORKING_SHIFT
        ORDER BY t.WORKING_DATE DESC
    </select>

    <!--	按机组统计有效工时-->
    <select id="statisticGroupByArea" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t.SEG_NO AS "segNo",
        GROUP_CONCAT(DISTINCT t.EMP_NO) AS "empNo",
        GROUP_CONCAT(DISTINCT t.STAFF_NAME) AS "staffName",
        GROUP_CONCAT(DISTINCT t.CHIP_ID) AS "chipId",
        MAX(t.POST_RESPONSIBILITY) AS "postResponsibility",
        t.AREA_NAME AS "areaName",
        CAST(SUM(t.WORKING_HOURS) AS DECIMAL(18, 2)) AS "totalWorkingHours",
        CAST(SUM(t.DEPARTURE_HOURS) AS DECIMAL(18, 2)) AS "totalDepartureHours",
        MAX(t.TEAM_ID) AS "teamId",
        t.WORKING_SHIFT AS "workingShift",
        t.WORKING_DATE AS "workingDate"
        FROM (SELECT mm.SEG_NO AS SEG_NO,
        mm.EMP_NO AS EMP_NO,
        mm.STAFF_NAME AS STAFF_NAME,
        mm.CHIP_ID AS CHIP_ID,
        pm.POST_RESPONSIBILITY AS POST_RESPONSIBILITY,
        mm.AREA_NAME AS AREA_NAME,
        mm.DEPARTURE_HOURS AS DEPARTURE_HOURS,
        pm.TEAM_ID AS TEAM_ID,
        mm.WORKING_SHIFT AS WORKING_SHIFT,
        mm.WORKING_DATE AS WORKING_DATE,
        CASE
        <!-- 最后一次打卡是进入，计算到结束时间的时长-->
        WHEN mm.WORKING_TIME = max_times.max_time AND mm.ACTION_TYPE = '1' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#productDateEnd#, '%Y%m%d%H%i%s')) / 60
        <!-- 第一次打卡是离开，计算从开始时间到离开的时长-->
        WHEN mm.WORKING_TIME = min_times.min_time AND mm.ACTION_TYPE = '2' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(#productDateStart#, '%Y%m%d%H%i%s'),
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s')) / 60
        <!-- 中间的工作时间直接累加-->
        WHEN mm.WORKING_TIME > min_times.min_time AND mm.WORKING_TIME
        &lt; max_times.max_time THEN
        mm.WORKING_HOURS
        ELSE
        0
        END AS WORKING_HOURS
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON mm.SEG_NO = pm.SEG_NO
        AND mm.CHIP_ID = pm.CHIP_ID
        AND pm.STATUS = '20'
        JOIN (SELECT mm2.STAFF_NAME, MAX(WORKING_TIME) as max_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) max_times ON mm.STAFF_NAME = max_times.STAFF_NAME
        JOIN (SELECT mm2.STAFF_NAME, MIN(WORKING_TIME) as min_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) min_times ON mm.STAFF_NAME = min_times.STAFF_NAME
        WHERE mm.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm.WORKING_TIME >= #productDateStart#
        AND mm.WORKING_TIME &lt;= #productDateEnd#
        <isNotEmpty prepend=" AND " property="chipId">
            mm.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            mm.STAFF_NAME LIKE '%$staffName$%'  <!-- 支持姓名模糊查询 -->
        </isNotEmpty>
        <isNotEmpty property="teamId">
            AND pm.TEAM_ID = #teamId#
        </isNotEmpty>
        <isNotEmpty property="workingShift">
            AND mm.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty property="workingDate">
            AND mm.WORKING_DATE = #workingDate#
        </isNotEmpty>
        ) t
        WHERE t.WORKING_HOURS > 0 || t.DEPARTURE_HOURS > 0
        GROUP BY t.SEG_NO, t.AREA_NAME, t.WORKING_DATE, t.WORKING_SHIFT
        ORDER BY t.WORKING_DATE DESC
    </select>

    <!--	单独查询人员在每个区域的时间-->
    <select id="statisticPersonTimeInArea" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t.SEG_NO AS "segNo",
        t.EMP_NO AS "empNo",
        t.STAFF_NAME AS "staffName",
        t.CHIP_ID AS "chipId",
        MAX(t.POST_RESPONSIBILITY) AS "postResponsibility",
        t.AREA_NAME AS "areaName",
        CAST(SUM(t.WORKING_HOURS) AS DECIMAL(18, 2)) AS "totalWorkingHours",
        CAST(SUM(t.DEPARTURE_HOURS) AS DECIMAL(18, 2)) AS "totalDepartureHours",
        MAX(t.TEAM_ID) AS "teamId",
        t.WORKING_SHIFT AS "workingShift",
        t.WORKING_DATE AS "workingDate"
        FROM (SELECT mm.SEG_NO AS SEG_NO,
        mm.EMP_NO AS EMP_NO,
        mm.STAFF_NAME AS STAFF_NAME,
        mm.CHIP_ID AS CHIP_ID,
        pm.POST_RESPONSIBILITY AS POST_RESPONSIBILITY,
        mm.AREA_NAME AS AREA_NAME,
        mm.DEPARTURE_HOURS AS DEPARTURE_HOURS,
        pm.TEAM_ID AS TEAM_ID,
        mm.WORKING_SHIFT AS WORKING_SHIFT,
        mm.WORKING_DATE AS WORKING_DATE,
        CASE
        <!-- 最后一次打卡是进入，计算到结束时间的时长-->
        WHEN mm.WORKING_TIME = max_times.max_time AND mm.ACTION_TYPE = '1' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#productDateEnd#, '%Y%m%d%H%i%s')) / 60
        <!-- 第一次打卡是离开，计算从开始时间到离开的时长-->
        WHEN mm.WORKING_TIME = min_times.min_time AND mm.ACTION_TYPE = '2' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(#productDateStart#, '%Y%m%d%H%i%s'),
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s')) / 60
        <!-- 中间的工作时间直接累加-->
        WHEN mm.WORKING_TIME > min_times.min_time AND mm.WORKING_TIME
        &lt; max_times.max_time THEN
        mm.WORKING_HOURS
        ELSE
        0
        END AS WORKING_HOURS
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON mm.SEG_NO = pm.SEG_NO
        AND mm.CHIP_ID = pm.CHIP_ID
        AND pm.STATUS = '20'
        JOIN (SELECT mm2.STAFF_NAME, MAX(WORKING_TIME) as max_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) max_times ON mm.STAFF_NAME = max_times.STAFF_NAME
        JOIN (SELECT mm2.STAFF_NAME, MIN(WORKING_TIME) as min_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) min_times ON mm.STAFF_NAME = min_times.STAFF_NAME
        WHERE mm.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm.WORKING_TIME >= #productDateStart#
        AND mm.WORKING_TIME &lt;= #productDateEnd#
        <isNotEmpty prepend=" AND " property="chipId">
            mm.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            mm.STAFF_NAME LIKE '%$staffName$%'  <!-- 支持姓名模糊查询 -->
        </isNotEmpty>
        <isNotEmpty property="teamId">
            AND pm.TEAM_ID = #teamId#
        </isNotEmpty>
        <isNotEmpty property="workingShift">
            AND mm.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty property="workingDate">
            AND mm.WORKING_DATE = #workingDate#
        </isNotEmpty>
        ) t
        WHERE t.WORKING_HOURS > 0 || t.DEPARTURE_HOURS > 0
        GROUP BY t.SEG_NO, t.CHIP_ID, t.EMP_NO, t.STAFF_NAME, t.AREA_NAME, t.WORKING_DATE, t.WORKING_SHIFT
        ORDER BY t.WORKING_DATE DESC
    </select>

    <!--	明细信息导出-->
    <select id="queryExport" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        SERIAL_NUMBER as "serialNumber",
        CHIP_ID as "chipId",  <!-- 芯片ID -->
        EMP_NO as "empNo",  <!-- 员工工号 -->
        STAFF_NAME as "staffName",  <!-- 人员姓名 -->
        AREA_NAME as "areaName",  <!-- 区域名称 -->
        CASE
        WHEN ACTION_TYPE = 1 THEN '进入'
        WHEN ACTION_TYPE = 2 THEN '离开'
        ELSE '未知'
        END as "actionType",  <!-- 动作类型(1、进入 2、离开) -->
        WORKING_HOURS as "workingHours",  <!-- 有效工作时间 -->
        WORKING_TIME as "workingTime" <!-- 操作时间 -->
        FROM mevp.tvppl0102 t WHERE 1=1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="chipId">
            t.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            t.STAFF_NAME like '%$staffName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="teamId">
            EXISTS(SELECT 1
            FROM mevp.tvppl0101 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.CHIP_ID = t.CHIP_ID
            AND t1.EMP_NO = t.EMP_NO
            AND t1.TEAM_ID = #teamId#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingDate">
            t.WORKING_DATE = #workingDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="workingShift">
            t.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateStart">
            SUBSTR(t.WORKING_TIME, 1, 14) >= #productDateStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productDateEnd">
            #productDateEnd# >= SUBSTR(t.WORKING_TIME, 1, 14)
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SERIAL_NUMBER DESC, ACTION_TYPE DESC
            </isEmpty>
        </dynamic>

    </select>

    <!--	按员工统计有效工时导出-->
    <select id="statisticGroupByEmpExport" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t.SEG_NO AS "segNo",
        t.EMP_NO AS "empNo",
        t.STAFF_NAME AS "staffName",
        t.CHIP_ID AS "chipId",
        CASE
        WHEN MAX(t.POST_RESPONSIBILITY) = 'HC' THEN '行车工'
        WHEN MAX(t.POST_RESPONSIBILITY) = 'JZ' THEN '机组操作工'
        ELSE '未知'
        END AS "postResponsibility",
        GROUP_CONCAT(DISTINCT t.AREA_NAME) AS "areaName",
        CAST(SUM(t.WORKING_HOURS) AS DECIMAL(18, 2)) AS "totalWorkingHours",
        CAST(SUM(t.DEPARTURE_HOURS) AS DECIMAL(18, 2)) AS "totalDepartureHours",
        CASE
        WHEN MAX(t.TEAM_ID) = '10' THEN '甲班'
        WHEN MAX(t.TEAM_ID) = '20' THEN '乙班'
        WHEN MAX(t.TEAM_ID) = '30' THEN '丙班'
        WHEN MAX(t.TEAM_ID) = '40' THEN '丁班'
        ELSE '未知'
        END AS "teamId",
        CASE
        WHEN t.WORKING_SHIFT = '10' THEN '早班'
        WHEN t.WORKING_SHIFT = '20' THEN '中班'
        WHEN t.WORKING_SHIFT = '30' THEN '晚班'
        ELSE '未知'
        END AS "workingShift",
        t.WORKING_DATE AS "workingDate"
        FROM (SELECT mm.SEG_NO AS SEG_NO,
        mm.EMP_NO AS EMP_NO,
        mm.STAFF_NAME AS STAFF_NAME,
        mm.CHIP_ID AS CHIP_ID,
        pm.POST_RESPONSIBILITY AS POST_RESPONSIBILITY,
        mm.AREA_NAME AS AREA_NAME,
        mm.DEPARTURE_HOURS AS DEPARTURE_HOURS,
        pm.TEAM_ID AS TEAM_ID,
        mm.WORKING_SHIFT AS WORKING_SHIFT,
        mm.WORKING_DATE AS WORKING_DATE,
        CASE
        <!-- 最后一次打卡是进入，计算到结束时间的时长-->
        WHEN mm.WORKING_TIME = max_times.max_time AND mm.ACTION_TYPE = '1' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#productDateEnd#, '%Y%m%d%H%i%s')) / 60
        <!-- 第一次打卡是离开，计算从开始时间到离开的时长-->
        WHEN mm.WORKING_TIME = min_times.min_time AND mm.ACTION_TYPE = '2' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(#productDateStart#, '%Y%m%d%H%i%s'),
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s')) / 60
        <!-- 中间的工作时间直接累加-->
        WHEN mm.WORKING_TIME > min_times.min_time AND mm.WORKING_TIME
        &lt; max_times.max_time THEN
        mm.WORKING_HOURS
        ELSE
        0
        END AS WORKING_HOURS
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON mm.SEG_NO = pm.SEG_NO
        AND mm.CHIP_ID = pm.CHIP_ID
        AND pm.STATUS = '20'
        JOIN (SELECT mm2.STAFF_NAME, MAX(WORKING_TIME) as max_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) max_times ON mm.STAFF_NAME = max_times.STAFF_NAME
        JOIN (SELECT mm2.STAFF_NAME, MIN(WORKING_TIME) as min_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) min_times ON mm.STAFF_NAME = min_times.STAFF_NAME
        WHERE mm.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm.WORKING_TIME >= #productDateStart#
        AND mm.WORKING_TIME &lt;= #productDateEnd#
        <isNotEmpty prepend=" AND " property="chipId">
            mm.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            mm.STAFF_NAME LIKE '%$staffName$%'  <!-- 支持姓名模糊查询 -->
        </isNotEmpty>
        <isNotEmpty property="teamId">
            AND pm.TEAM_ID = #teamId#
        </isNotEmpty>
        <isNotEmpty property="workingShift">
            AND mm.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty property="workingDate">
            AND mm.WORKING_DATE = #workingDate#
        </isNotEmpty>
        ) t
        WHERE t.WORKING_HOURS > 0 || t.DEPARTURE_HOURS > 0
        GROUP BY t.SEG_NO, t.EMP_NO, t.CHIP_ID, t.STAFF_NAME, t.WORKING_DATE, t.WORKING_SHIFT
        ORDER BY t.WORKING_DATE DESC
    </select>

    <!--	按机组统计有效工时导出-->
    <select id="statisticGroupByAreaExport" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t.SEG_NO AS "segNo",
        GROUP_CONCAT(DISTINCT t.EMP_NO) AS "empNo",
        GROUP_CONCAT(DISTINCT t.STAFF_NAME) AS "staffName",
        GROUP_CONCAT(DISTINCT t.CHIP_ID) AS "chipId",
        CASE
        WHEN MAX(t.POST_RESPONSIBILITY) = 'HC' THEN '行车工'
        WHEN MAX(t.POST_RESPONSIBILITY) = 'JZ' THEN '机组操作工'
        ELSE '未知'
        END AS "postResponsibility",
        t.AREA_NAME AS "areaName",
        CAST(SUM(t.WORKING_HOURS) AS DECIMAL(18, 2)) AS "totalWorkingHours",
        CAST(SUM(t.DEPARTURE_HOURS) AS DECIMAL(18, 2)) AS "totalDepartureHours",
        CASE
        WHEN MAX(t.TEAM_ID) = '10' THEN '甲班'
        WHEN MAX(t.TEAM_ID) = '20' THEN '乙班'
        WHEN MAX(t.TEAM_ID) = '30' THEN '丙班'
        WHEN MAX(t.TEAM_ID) = '40' THEN '丁班'
        ELSE '未知'
        END AS "teamId",
        CASE
        WHEN t.WORKING_SHIFT = '10' THEN '早班'
        WHEN t.WORKING_SHIFT = '20' THEN '中班'
        WHEN t.WORKING_SHIFT = '30' THEN '晚班'
        ELSE '未知'
        END AS "workingShift",
        t.WORKING_DATE AS "workingDate"
        FROM (SELECT mm.SEG_NO AS SEG_NO,
        mm.EMP_NO AS EMP_NO,
        mm.STAFF_NAME AS STAFF_NAME,
        mm.CHIP_ID AS CHIP_ID,
        pm.POST_RESPONSIBILITY AS POST_RESPONSIBILITY,
        mm.AREA_NAME AS AREA_NAME,
        mm.DEPARTURE_HOURS AS DEPARTURE_HOURS,
        pm.TEAM_ID AS TEAM_ID,
        mm.WORKING_SHIFT AS WORKING_SHIFT,
        mm.WORKING_DATE AS WORKING_DATE,
        CASE
        <!-- 最后一次打卡是进入，计算到结束时间的时长-->
        WHEN mm.WORKING_TIME = max_times.max_time AND mm.ACTION_TYPE = '1' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#productDateEnd#, '%Y%m%d%H%i%s')) / 60
        <!-- 第一次打卡是离开，计算从开始时间到离开的时长-->
        WHEN mm.WORKING_TIME = min_times.min_time AND mm.ACTION_TYPE = '2' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(#productDateStart#, '%Y%m%d%H%i%s'),
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s')) / 60
        <!-- 中间的工作时间直接累加-->
        WHEN mm.WORKING_TIME > min_times.min_time AND mm.WORKING_TIME
        &lt; max_times.max_time THEN
        mm.WORKING_HOURS
        ELSE
        0
        END AS WORKING_HOURS
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON mm.SEG_NO = pm.SEG_NO
        AND mm.CHIP_ID = pm.CHIP_ID
        AND pm.STATUS = '20'
        JOIN (SELECT mm2.STAFF_NAME, MAX(WORKING_TIME) as max_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) max_times ON mm.STAFF_NAME = max_times.STAFF_NAME
        JOIN (SELECT mm2.STAFF_NAME, MIN(WORKING_TIME) as min_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) min_times ON mm.STAFF_NAME = min_times.STAFF_NAME
        WHERE mm.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm.WORKING_TIME >= #productDateStart#
        AND mm.WORKING_TIME &lt;= #productDateEnd#
        <isNotEmpty prepend=" AND " property="chipId">
            mm.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            mm.STAFF_NAME LIKE '%$staffName$%'  <!-- 支持姓名模糊查询 -->
        </isNotEmpty>
        <isNotEmpty property="teamId">
            AND pm.TEAM_ID = #teamId#
        </isNotEmpty>
        <isNotEmpty property="workingShift">
            AND mm.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty property="workingDate">
            AND mm.WORKING_DATE = #workingDate#
        </isNotEmpty>
        ) t
        WHERE t.WORKING_HOURS > 0 || t.DEPARTURE_HOURS > 0
        GROUP BY t.SEG_NO, t.AREA_NAME, t.WORKING_DATE, t.WORKING_SHIFT
        ORDER BY t.WORKING_DATE DESC
    </select>

    <!--	单独查询人员在每个区域的时间(人员区域明细)导出-->
    <select id="statisticPersonTimeInAreaExport" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t.SEG_NO AS "segNo",
        t.EMP_NO AS "empNo",
        t.STAFF_NAME AS "staffName",
        t.CHIP_ID AS "chipId",
        CASE
        WHEN MAX(t.POST_RESPONSIBILITY) = 'HC' THEN '行车工'
        WHEN MAX(t.POST_RESPONSIBILITY) = 'JZ' THEN '机组操作工'
        ELSE '未知'
        END AS "postResponsibility",
        t.AREA_NAME AS "areaName",
        CAST(SUM(t.WORKING_HOURS) AS DECIMAL(18, 2)) AS "totalWorkingHours",
        CAST(SUM(t.DEPARTURE_HOURS) AS DECIMAL(18, 2)) AS "totalDepartureHours",
        CASE
        WHEN MAX(t.TEAM_ID) = '10' THEN '甲班'
        WHEN MAX(t.TEAM_ID) = '20' THEN '乙班'
        WHEN MAX(t.TEAM_ID) = '30' THEN '丙班'
        WHEN MAX(t.TEAM_ID) = '40' THEN '丁班'
        ELSE '未知'
        END AS "teamId",
        CASE
        WHEN t.WORKING_SHIFT = '10' THEN '早班'
        WHEN t.WORKING_SHIFT = '20' THEN '中班'
        WHEN t.WORKING_SHIFT = '30' THEN '晚班'
        ELSE '未知'
        END AS "workingShift",
        t.WORKING_DATE AS "workingDate"
        FROM (SELECT mm.SEG_NO AS SEG_NO,
        mm.EMP_NO AS EMP_NO,
        mm.STAFF_NAME AS STAFF_NAME,
        mm.CHIP_ID AS CHIP_ID,
        pm.POST_RESPONSIBILITY AS POST_RESPONSIBILITY,
        mm.AREA_NAME AS AREA_NAME,
        mm.DEPARTURE_HOURS AS DEPARTURE_HOURS,
        pm.TEAM_ID AS TEAM_ID,
        mm.WORKING_SHIFT AS WORKING_SHIFT,
        mm.WORKING_DATE AS WORKING_DATE,
        CASE
        <!-- 最后一次打卡是进入，计算到结束时间的时长-->
        WHEN mm.WORKING_TIME = max_times.max_time AND mm.ACTION_TYPE = '1' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(#productDateEnd#, '%Y%m%d%H%i%s')) / 60
        <!-- 第一次打卡是离开，计算从开始时间到离开的时长-->
        WHEN mm.WORKING_TIME = min_times.min_time AND mm.ACTION_TYPE = '2' THEN
        TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(#productDateStart#, '%Y%m%d%H%i%s'),
        STR_TO_DATE(mm.WORKING_TIME, '%Y%m%d%H%i%s')) / 60
        <!-- 中间的工作时间直接累加-->
        WHEN mm.WORKING_TIME > min_times.min_time AND mm.WORKING_TIME
        &lt; max_times.max_time THEN
        mm.WORKING_HOURS
        ELSE
        0
        END AS WORKING_HOURS
        FROM mevp.tvppl0102 mm
        LEFT JOIN mevp.tvppl0101 pm
        ON mm.SEG_NO = pm.SEG_NO
        AND mm.CHIP_ID = pm.CHIP_ID
        AND pm.STATUS = '20'
        JOIN (SELECT mm2.STAFF_NAME, MAX(WORKING_TIME) as max_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) max_times ON mm.STAFF_NAME = max_times.STAFF_NAME
        JOIN (SELECT mm2.STAFF_NAME, MIN(WORKING_TIME) as min_time
        FROM mevp.tvppl0102 mm2
        LEFT JOIN mevp.tvppl0101 pm2
        ON mm2.SEG_NO = pm2.SEG_NO
        AND mm2.CHIP_ID = pm2.CHIP_ID
        AND pm2.STATUS = '20'
        WHERE mm2.SEG_NO = #segNo#
        AND mm2.WORKING_TIME >= #productDateStart#
        AND mm2.WORKING_TIME &lt;= #productDateEnd#
        GROUP BY STAFF_NAME) min_times ON mm.STAFF_NAME = min_times.STAFF_NAME
        WHERE mm.SEG_NO = #segNo#
        <!--时间范围必填-->
        AND mm.WORKING_TIME >= #productDateStart#
        AND mm.WORKING_TIME &lt;= #productDateEnd#
        <isNotEmpty prepend=" AND " property="chipId">
            mm.CHIP_ID like '%$chipId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="staffName">
            mm.STAFF_NAME LIKE '%$staffName$%'  <!-- 支持姓名模糊查询 -->
        </isNotEmpty>
        <isNotEmpty property="teamId">
            AND pm.TEAM_ID = #teamId#
        </isNotEmpty>
        <isNotEmpty property="workingShift">
            AND mm.WORKING_SHIFT = #workingShift#
        </isNotEmpty>
        <isNotEmpty property="workingDate">
            AND mm.WORKING_DATE = #workingDate#
        </isNotEmpty>
        ) t
        WHERE t.WORKING_HOURS > 0 || t.DEPARTURE_HOURS > 0
        GROUP BY t.SEG_NO, t.CHIP_ID, t.EMP_NO, t.STAFF_NAME, t.AREA_NAME, t.WORKING_DATE, t.WORKING_SHIFT
        ORDER BY t.WORKING_DATE DESC
    </select>

    <!--	查询对应区域当前存在的人员信息(最后一条记录为进入)-->
    <select id="queryChipIdInArea" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT t1.CHIP_ID AS "chipId",
        t1.EMP_NO AS "empNo",
        t1.STAFF_NAME AS "staffName"
        FROM mevp.tvppl0102 t1
        WHERE t1.SEG_NO = #segNo#
        AND t1.AREA_NAME = #areaName#
        AND t1.ACTION_TYPE = 1
        AND t1.WORKING_TIME = (SELECT MAX(t2.WORKING_TIME)
        FROM mevp.tvppl0102 t2
        WHERE t2.SEG_NO = t1.SEG_NO
        AND t2.CHIP_ID = t1.CHIP_ID
        AND t2.AREA_NAME = t1.AREA_NAME)
    </select>

    <!--	统计各员工在所有区域进入未离开的数据-->
    <select id="queryEmpActive" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vp.pl.domain.VPPL0102">
        SELECT enter.SEG_NO AS "segNo",
        enter.UNIT_CODE AS "unitCode",
        enter.CHIP_ID AS "chipId",
        enter.EMP_NO AS "empNo",
        enter.STAFF_NAME AS "staffName",
        enter.AREA_NAME AS "areaName",
        enter.ACTION_TYPE AS "actionType",
        enter.WORKING_HOURS AS "workingHours",
        enter.WORKING_TIME AS "workingTime",
        enter.STATUS AS "status",
        enter.REC_CREATOR AS "recCreator",
        enter.REC_CREATOR_NAME AS "recCreatorName",
        enter.REC_CREATE_TIME AS "recCreateTime",
        enter.REC_REVISOR AS "recRevisor",
        enter.REC_REVISOR_NAME AS "recRevisorName",
        enter.REC_REVISE_TIME AS "recReviseTime",
        enter.ARCHIVE_FLAG AS "archiveFlag",
        enter.TENANT_USER AS "tenantUser",
        enter.DEL_FLAG AS "delFlag",
        enter.UUID AS "uuid",
        enter.WORKING_DATE AS "workingDate",
        enter.WORKING_SHIFT AS "workingShift"
        FROM (
        <!-- 获取每个区域的最后进入记录-->
        SELECT t1.*
        FROM mevp.tvppl0102 t1
        INNER JOIN (SELECT SEG_NO,
        CHIP_ID,
        AREA_NAME,
        MAX(WORKING_TIME) AS max_working_time
        FROM mevp.tvppl0102
        WHERE ACTION_TYPE = '1'
        GROUP BY SEG_NO,CHIP_ID, AREA_NAME) t2
        ON t1.SEG_NO = t2.SEG_NO
        AND t1.CHIP_ID = t2.CHIP_ID
        AND t1.AREA_NAME = t2.AREA_NAME
        AND t1.WORKING_TIME = t2.max_working_time
        WHERE t1.SEG_NO = 'JC000000'
        AND t1.ACTION_TYPE = '1') enter
        LEFT JOIN mevp.tvppl0102 leave_record
        ON enter.SEG_NO = leave_record.SEG_NO
        AND enter.CHIP_ID = leave_record.CHIP_ID
        AND enter.AREA_NAME = leave_record.AREA_NAME
        AND leave_record.ACTION_TYPE = '2'
        AND leave_record.WORKING_TIME > enter.WORKING_TIME
        WHERE leave_record.CHIP_ID IS NULL
    </select>

    <!--查询前一班次进入次数-->
    <select id="queryPreviousShiftEntryCount" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vp.pl.domain.VPPL0102">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        CHIP_ID as "chipId",  <!-- 芯片ID -->
        EMP_NO as "empNo",  <!-- 员工工号 -->
        STAFF_NAME as "staffName",  <!-- 人员姓名 -->
        AREA_NAME as "areaName",  <!-- 区域名称 -->
        ACTION_TYPE as "actionType",  <!-- 动作类型(1、进入 2、离开) -->
        WORKING_HOURS as "workingHours",  <!-- 有效工作时间 -->
        WORKING_TIME as "workingTime",  <!-- 操作时间 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        WORKING_DATE as "workingDate",
        WORKING_SHIFT as "workingShift"
        FROM mevp.tvppl0102 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND CHIP_ID = #chipId#
        AND WORKING_DATE = #workingDate#
        AND WORKING_SHIFT = #workingShift#
        <isEqual prepend=" AND " property="workingShift" compareValue="10">
            SUBSTR(WORKING_TIME, 9, 6) BETWEEN '140000' AND '200000'
        </isEqual>
        <isEqual prepend=" AND " property="workingShift" compareValue="30">
            SUBSTR(WORKING_TIME, 9, 6) BETWEEN '000000' AND '080000'
        </isEqual>
    </select>
</sqlMap>