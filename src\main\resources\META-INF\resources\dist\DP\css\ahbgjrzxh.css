@charset "utf-8";
dl {
    margin: 0;
}

dd {
    margin: 0;
}

dt {
    margin: 0;
}

ul,
li {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

body {
    margin: 0;
    padding: 0;
    color: #cccccc;
    font-family: "微软雅黑";
}

#bg {
    width: 100%;
    height: 100vh;
    /*background-color: #324E73;*/
    display: flex;
}

.left{
    width: 11vw;
    height: 100%;
    background: #ffffff;
    color: black;
    display: flex;
    justify-content: flex-end;
}

ul li{
    padding: 5px;
    border-radius: 3px;
    margin-right: 30px;
}

ul li:nth-child(1){
    margin-top: 10px;
}

.checked{
    background: #ebebeb;
}

.center{
    width: 69vw;
    height: 100%;
    overflow: auto;
    display: flex;
    background: #EDECED;
    /*padd: 20vw;*/
}

/* 隐藏滚动条 */
/*.center::-webkit-scrollbar {*/
/*    display: none; !* 隐藏滚动条 (Chrome, Safari, Edge) *!*/
/*}*/
/*.center {*/
/*    -ms-overflow-style: none;  !* 隐藏滚动条 (IE 和 Edge) *!*/
/*    scrollbar-width: none;     !* 隐藏滚动条 (Firefox) *!*/
/*}*/

.center-list{
    min-width: 13vw;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
}

.center-top{
    margin: 10px 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.time{
    padding: 0 5px;
    background: #2ba471;
    margin-right: 10px;
    color: #ffffff;
}

.num{
    color: #9d9d9d;
}

.center-list-list{
    padding: 0 10px;
    border: 1px solid #9d9d9d;
    margin: 5px 0;
    border-radius: 3px;
    background: #ffffff;
}

.line{
    margin: 5px 0;
}

.text{
    color: #9d9d9d;
    margin: 5px 0;
}

.value{
    color: black;
    font-weight: 500;
    font-size: 16px;
    border-radius: 3px;
    padding: 5px 10px;
}

.right{
    width: 20vw;
    height: 100%;
    background: #ffffff;
}

.right-top{
    font-weight: bold;
    text-align: center;
    background-color: #d6f1ff;
    color: black;
    font-size: 20px;
}

.right-bottom{
    width: 100%;
    border-collapse: separate;
    border-spacing: 0px 5px;
    padding: 0px 5px;
}
.pending{
    color: black;
    padding: 2px 6px;
    font-size: 15px;
    background: #ebebeb;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0px 5px;
    padding: 0px 5px;
}

table tr td{
    width: calc(100% / 3);
    text-align: center;
}

table tr td:first-child {
    border-top-left-radius: 4px; /* 左上角圆角 */
    border-bottom-left-radius: 4px; /* 左下角圆角 */
}

table tr td:last-child {
    border-top-right-radius: 4px; /* 右上角圆角 */
    border-bottom-right-radius: 4px; /* 右下角圆角 */
}
