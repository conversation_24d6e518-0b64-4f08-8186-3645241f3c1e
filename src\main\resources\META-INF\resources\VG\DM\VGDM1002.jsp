<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="detail" title="参数">
        <div class="row">
            <EF:EFSelect ename="detail_status-0-tagId" cname="设备" colWidth="3" >
                <EF:EFOption label="未选择" value=""/>
                <EF:EFOption label="1650纵切" value="JC52SL050001"/>
                <EF:EFOption label="1650横切" value="JC52CL011001"/>
                <EF:EFOption label="800横切" value="JC52CL020001"/>
                <EF:EFOption label="1#落料" value="JC52BL010001"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="当前作业">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="70" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称"/>
            <EF:EFColumn ename="processOrderId" cname="工单" width="150" align="center"/>
            <EF:EFColumn ename="packId" cname="捆包号" width="150" align="center"/>
            <EF:EFComboColumn ename="packStatus" enable="false" cname="捆包状态" align="center" width="80">
                <EF:EFOption value="0" label="上料"/>
                <EF:EFOption value="1" label="加工"/>
                <EF:EFOption value="2" label="完成"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="upPackTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上料时间"/>
            <EF:EFColumn ename="startTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="开始时间"/>
            <EF:EFColumn ename="endTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="结束时间"/>
            <EF:EFColumn ename="originalPackId" cname="原始捆包号" width="140" align="center"/>
            <EF:EFColumn ename="partId" cname="物料号" width="150" align="center"/>
            <EF:EFColumn ename="netWeight" cname="净重" align="right"/>
            <EF:EFComboColumn ename="endType" enable="false" cname="标记" align="center" width="80">
                <EF:EFOption value=" " label="加工中"/>
                <EF:EFOption value="0" label="完成"/>
                <EF:EFOption value="1" label="退料"/>
                <EF:EFOption value="2" label="余料"/>
                <EF:EFOption value="3" label="换刀"/>
                <EF:EFOption value="4" label="尾包"/>
                <EF:EFOption value="9" label="手工结束"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="machineCode" cname="机组代码" align="center"/>
            <EF:EFColumn ename="machineName" cname="机组名称" align="center"/>
            <EF:EFColumn ename="processCategory" cname="工序大类" width="70" align="center"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>