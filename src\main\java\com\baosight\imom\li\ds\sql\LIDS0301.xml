<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-27 13:44:37
       Version :  1.0
    tableName :meli.tlids0301
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CRANE_ID  VARCHAR   NOT NULL,
     CRANE_NAME  VARCHAR   NOT NULL,
     CROSS_AREA  VARCHAR   NOT NULL,
     CROSS_AREA_NAME  VARCHAR   NOT NULL,
     FACTORY_AREA  VARCHAR   NOT NULL,
     FACTORY_AREA_NAME  VARCHAR   NOT NULL,
     FACTORY_BUILDING  VARCHAR   NOT NULL,
     FACTORY_BUILDING_NAME  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR
-->
<sqlMap namespace="LIDS0301">

    <sql id="condition">
        AND SEG_NO = #segNo#
        <isNotEmpty prepend="AND" property="craneId">
            CRANE_ID LIKE '%$craneId$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="craneName">
            CRANE_NAME LIKE '%$craneName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneDuty">
            CRANE_DUTY = #craneDuty#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME LIKE '%$factoryAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME LIKE '%$factoryBuildingName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS > '00'
            AND DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0301">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        CRANE_DUTY as "craneDuty",  <!-- 行车职责 -->
        CROSS_AREA as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids0301 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0301 WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--根据UUID用来校验数据状态-->
    <select id="count_uuid" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0301 WHERE 1=1
        <include refid="condition"/>
        AND UUID = #uuid#
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneId">
            CRANE_ID = #craneId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneName">
            CRANE_NAME = #craneName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME = #crossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME = #factoryAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME = #factoryBuildingName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids0301 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        E_ARCHIVES_NO, <!-- 设备档案编码 -->
        CRANE_ID,  <!-- 行车编号 -->
        CRANE_NAME,  <!-- 行车名称 -->
        CRANE_DUTY, <!-- 行车职责 -->
        CROSS_AREA,  <!-- 跨区代码 -->
        CROSS_AREA_NAME,  <!-- 跨区名称 -->
        FACTORY_AREA,  <!-- 厂区代码 -->
        FACTORY_AREA_NAME,  <!-- 厂区名称 -->
        FACTORY_BUILDING,  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME,  <!-- 厂房名称 -->
        STATUS,  <!-- 状态 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID  <!-- ID -->
        )
        VALUES (#segNo:VARCHAR#, #unitCode:VARCHAR#,#eArchivesNo:VARCHAR#, #craneId:VARCHAR#, #craneName:VARCHAR#,#craneDuty:VARCHAR#, #crossArea:VARCHAR#,
        #crossAreaName:VARCHAR#, #factoryArea:VARCHAR#, #factoryAreaName:VARCHAR#, #factoryBuilding:VARCHAR#,
        #factoryBuildingName:VARCHAR#, #status:VARCHAR#, #recCreator:VARCHAR#, #recCreatorName:VARCHAR#,
        #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recRevisorName:VARCHAR#, #recReviseTime:VARCHAR#,
        #archiveFlag:VARCHAR#, #tenantUser:VARCHAR#, #delFlag#, #uuid:VARCHAR#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids0301 WHERE
    </delete>

    <update id="update">
        UPDATE meli.tlids0301
        SET
        E_ARCHIVES_NO = #eArchivesNo#,<!-- 设备档案编码 -->
        CRANE_ID = #craneId#,   <!-- 行车编号 -->
        CRANE_NAME = #craneName#,   <!-- 行车名称 -->
        CRANE_DUTY = #craneDuty#,   <!-- 行车职责 -->
        CROSS_AREA = #crossArea#,   <!-- 跨区代码 -->
        CROSS_AREA_NAME = #crossAreaName#,   <!-- 跨区名称 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区代码 -->
        FACTORY_AREA_NAME = #factoryAreaName#,   <!-- 厂区名称 -->
        FACTORY_BUILDING = #factoryBuilding#,   <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME = #factoryBuildingName#,   <!-- 厂房名称 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE 1=1
        AND SEG_NO= #segNo#
        AND CRANE_ID = #craneId#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <update id="updateStatus">
        UPDATE meli.tlids0301
        SET
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend="," property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO= #segNo#
        AND CRANE_ID = #craneId#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <!--    根据跨区代码匹配所有的行车编号并拼接返回-->
    <select id="queryCraneIdsByCrossArea" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT GROUP_CONCAT(DISTINCT CRANE_ID ORDER BY CRANE_ID SEPARATOR ',') as "craneIds",<!--行车编号-->
        GROUP_CONCAT(DISTINCT CRANE_NAME ORDER BY CRANE_NAME SEPARATOR ',') as "craneNames"<!--行车名称-->
        FROM meli.tlids0301 t
        WHERE 1 = 1
        AND t.CROSS_AREA = (SELECT lm.CROSS_AREA
        FROM meli.tlids0601 lm
        WHERE lm.SEG_NO = t.SEG_NO
        AND lm.FACTORY_AREA = t.FACTORY_AREA
        AND lm.FACTORY_BUILDING = t.FACTORY_BUILDING
        AND lm.LOCATION_ID = #areaCode#)
        AND t.STATUS = '20'
    </select>

<!--    根据行车编码查询行车所属厂区厂房-->
    <select id="queryByCraneId" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0301">
        SELECT
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName"  <!-- 厂房名称 -->
        FROM meli.tlids0301 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_ID = #craneId#
        AND STATUS = '20'
    </select>
</sqlMap>