<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true" required="true"/>
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                             ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>


        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false"
                             containerId="deviceInfoMainQuery" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                             cname="分部设备名称" colWidth="3"/>
            <EF:EFDateSpan startName="inqu_status-0-checkStartDate"
                           endName="inqu_status-0-checkEndDate" readonly="true"
                           startCname="点检日期(起)" endCname="点检日期(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>


        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-checkPlanSubId" cname="点检计划分档号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-checkPlanSubStatus" cname="分档状态" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P019"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-spotCheckNature" cname="点检性质" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P050"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="实绩清单">
        <EF:EFGrid blockId="result" autoDraw="no" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" alias="t1.unit_code"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="checkPlanSubId" cname="点检计划分档号" enable="false" width="150" align="center"/>
            <EF:EFComboColumn ename="checkPlanSubStatus" cname="分档状态" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P019"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center" enable="false"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称" enable="false"/>
            <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
            <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
            <EF:EFColumn ename="checkPlanDate" cname="点检日期" align="center" enable="false" width="80"/>
            <EF:EFComboColumn ename="spotCheckNature" cname="点检性质" align="center" width="70" enable="false">
                <EF:EFCodeOption codeName="P050"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="spotCheckContent" cname="点检内容" width="200" enable="false"/>
            <EF:EFComboColumn ename="spotCheckMethod" cname="点检方法" align="center" width="70" enable="false">
                <EF:EFCodeOption codeName="P049"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="deviceCheckStatus" cname="设备状态" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P047"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="isPublish" cname="是否挂牌" align="center" width="70" enable="false">
                <EF:EFOption value="1" label="是"/>
                <EF:EFOption value="0" label="否"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="spotCheckStandardType" cname="标准类型" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P051"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="judgmentStandard" cname="判断标准" width="200" enable="false"/>
            <EF:EFComboColumn ename="isNormal" cname="是否异常" required="true" align="center" width="80">
                <EF:EFCodeOption codeName="P020"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="actualsRemark" cname="点检实绩" width="200" required="true" editType="textarea"/>
            <EF:EFColumn ename="exceptionContactId" cname="异常联络单号" align="center" enable="false"/>
            <EF:EFComboColumn ename="actualsSource" cname="实绩来源" align="center" width="70"
                              enable="false">
                <EF:EFOption value="10" label="PC"/>
                <EF:EFOption value="20" label="PDA"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="actualsRevisor" cname="点检实绩操作人" align="center" enable="false"/>
            <EF:EFColumn ename="actualsRevisorName" cname="点检实绩操作人姓名" align="center" width="150"
                         enable="false"/>
            <EF:EFColumn ename="actualsTime" cname="点检实绩时间" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" enable="false"/>
            <EF:EFComboColumn ename="spotCheckImplemente" cname="实施方" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P048"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="measureId" cname="计量单位" enable="false"/>
            <EF:EFColumn ename="upperLimit" cname="上限值" enable="false" align="right"/>
            <EF:EFColumn ename="lowerLimit" cname="下限值" enable="false" align="right"/>
            <EF:EFColumn ename="spotCheckStandardId" cname="点检标准编号" width="120" align="center"
                         enable="false"/>
            <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
            <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow id="attachmentInfo" width="90%" height="60%">
        <EF:EFRegion id="result2" title="附件信息">
            <EF:EFInput ename="inqu2_status-0-relevanceId" type="hidden"/>
            <EF:EFInput ename="inqu2_status-0-segNo" type="hidden"/>
            <div style="display: none;">
                <EF:EFInput ename="fileForm" type="file"/>
            </div>
            <EF:EFGrid blockId="result2" autoDraw="no" readonly="true"
                       serviceName="VGDM0801" queryMethod="queryFile" sort="all">
                <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                <EF:EFColumn ename="relevanceId" cname="点检计划号" width="120" align="center"/>
                <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                <EF:EFColumn ename="fifleSize" cname="文件大小"/>
                <EF:EFColumn ename="fifleType" cname="文件类型"/>
                <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                <EF:EFColumn ename="recCreateTime" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>