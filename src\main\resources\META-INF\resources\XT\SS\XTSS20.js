/**
* Generate time : 2025-07-07 22:39:41
* Version : 1.0
*/
$(function () {
    //获取当前登录人对应的业务单元
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);

    let editorModel;

    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    $("#QUERY").on("click", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        var unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({ msg: "请先选择业务单元代码!" }, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 10,
                pageSizes: [10, 50, 100, 1000]
            },
            columns: [
                {
                    field: "groupEname",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "用户组英文名",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;

                            // 打开弹出框并传递参数
                            IPLAT.Popup.popupContainer({
                                containerId: "userGroupInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "用户组查询",
                            })
                        }
                    }
                },
                {
                    field: "userPermission",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "用户权限",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;

                            // 打开弹出框并传递参数
                            IPLAT.Popup.popupContainer({
                                containerId: "menuInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "权限菜单查询",
                            })
                        }
                    }
                },
            ],
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({ msg: "请选择业务单元代码!" }, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                const userGroupId = $("#inqu_status-0-userGroupId").val();
                const groupEname = $("#inqu_status-0-groupEname").val();
                const groupCname = $("#inqu_status-0-groupCname").val();

                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({ msg: "请选择业务单元代码!" }, "error");
                    e.preventDefault();
                    return;
                }

                // 填充业务单元信息
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);

                // 填充用户组信息
                if (!IPLAT.isBlankString(userGroupId)) {
                    resultGrid.setCellValue(0, 'userGroupId', userGroupId);
                    resultGrid.setCellValue(0, 'groupEname', groupEname);
                    resultGrid.setCellValue(0, 'groupCname', groupCname);
                }

                resultGrid.refresh();
            },
        }
    }

        IMOMUtil.windowTemplate({
            windowId: "userGroupInfo",
            _open: function (e, iframejQuery) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                    NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                    e.preventDefault();
                    return;
                }
                iframejQuery("#inqu_status-0-unitCode").val(unitCode);
                iframejQuery("#inqu_status-0-segName").val(segName);
                iframejQuery("#inqu_status-0-segNo").val(segNo);
                iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
                iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
    
            },
            afterSelect: function (rows) {
                if (rows.length > 0) {
                    console.log(rows[0]);
                    const data = rows[0];
                    resultGrid.setCellValue(editorModel, "userGroupId", data.id);
                    resultGrid.setCellValue(editorModel, "groupEname", data.groupEname);
                    resultGrid.setCellValue(editorModel, "groupCname", data.groupCname);
                }
            }
        });

    IMOMUtil.windowTemplate({
        windowId: "menuInfo",
        _open: function (e, iframejQuery) {
            if (IPLAT.isBlankString(unitInfo.unitCode) || IPLAT.isBlankString(unitInfo.segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitInfo.unitCode);
            iframejQuery("#inqu_status-0-segNo").val(unitInfo.segNo);
        },
        afterSelect: function (rows, iframejQuery) {
            if (rows.length > 0) {
                console.log(rows)
                resultGrid.setCellValue(editorModel, "userPermission", rows.map(r => r.menuName).join('、'));
            }
        }
    });
});