/**
* Generate time : 2024-10-14 10:47:54
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0601;

import java.util.Map;

/**
* Tlids0601
* 
*/
public class LIDS0601 extends Tlids0601 {
        public static final String QUERY = "LIDS0601.query";
        public static final String QUERY_WAREHOUSE = "LIDS0601.queryWarehouse";
        public static final String QUERYInPDA = "LIDS0601.queryInPDA";
        public static final String COUNT = "LIDS0601.count";
        public static final String COUNT_EXISTS = "LIDS0601.countExists";
        public static final String COUNT_UUID = "LIDS0601.count_uuid";
        public static final String INSERT = "LIDS0601.insert";
        public static final String UPDATE = "LIDS0601.update";
        public static final String DELETE = "LIDS0601.delete";
        public static final String QUERY_TO_MAP = "LIDS0601.queryToMap";
        public static final String QUERY_EXPORT = "LIDS0601.queryExport";
        public static final String QUERY_LOCATION_ID = "LIDS0601.queryLocationId";
        public static final String COUNT_LOCATION_ID = "LIDS0601.countLocationId";
        public static final String QUERY_EXISTING_LOCATIONS = "LIDS0601.queryExistingLocations";
        public static final String QUERY_MIN_LOCATION = "LIDS0601.queryMinLocation";
        public static final String QUERY_MAX_LOCATION = "LIDS0601.queryMaxLocation";
        public static final String QUERY_THOROUGHFARE_AREA_CODE = "LIDS0601.queryThoroughfare";
        public static final String QUERY_LOCATION_CQ = "LIDS0601.queryLocationCq";
        public static final String QUERY_LOCATION_ID_JZ = "LIDS0601.queryLocationIdJZ";
        public static final String QUERY_LOCATION_OCCUPY_C_Q_SUM = "LIDS0601.queryLocationOccupyCQSum";


        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0601() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}