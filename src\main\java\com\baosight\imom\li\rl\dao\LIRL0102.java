/**
* Generate time : 2024-08-14 8:58:23
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0102 司机维护子表
*
*/
public class LIRL0102 extends DaoEPBase {

                public static final String QUERY = "LIRL0102.query";
                public static final String SUB_QUERY = "LIRL0102.subQuery";
                public static final String SUB_QUERY_ALL = "LIRL0102.subQueryAll";
                public static final String COUNT_DRIVER_INFO = "LIRL0102.countDriverInfo";
                public static final String SUB_QUERY_UUID = "LIRL0102.subQueryUuid";
                public static final String INSERT = "LIRL0102.insert";
                public static final String UPDATE = "LIRL0102.update";
                public static final String QUERY_BY_TEL = "LIRL0102.queryByTel";
                public static final String QUERY_ADMIN_BY_DRIVER_INFO = "LIRL0102.queryAdminByDriverInfo";
                public static final String QUERY_ADMIN_BY_REVESION_INFO = "LIRL0102.queryAdminByRevesionInfo";

                private String segNo = " ";		/* 业务单元代代码*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String segName = " ";		/* 业务单元简称*/
                private String uuid = " ";		/* uuid*/
                private String status = " ";		/* 状态(撤销：00、新增：10、生效：20)*/
                private String driverName = " ";		/* 司机姓名*/
                private String driverIdentity = " ";		/* 司机身份证号*/
                private String reservationIdentity = " ";		/* 司机身份:30*/
                private String customerId = " ";		/* 承运商/客户代码*/
                private String customerName = " ";		/* 承运商/客户名称*/
                private String tel = " ";		/* 司机手机号*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String tenantId = " ";		/* 租户ID*/
                private String vehicleNo = " ";		/* 牌号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10、生效：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationIdentity");
        eiColumn.setDescName("司机身份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tel");
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0102() {
initMetaData();
}

        /**
        * get the segNo - 业务单元代代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

            public String getReservationIdentity() {
            return reservationIdentity;
        }

        public void setReservationIdentity(String reservationIdentity) {
            this.reservationIdentity = reservationIdentity;
        }

    /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the status - 状态(撤销：00、新增：10、生效：20)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(撤销：00、新增：10、生效：20)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the driverName - 司机姓名
        * @return the driverName
        */
        public String getDriverName() {
        return this.driverName;
        }

        /**
        * set the driverName - 司机姓名
        */
        public void setDriverName(String driverName) {
        this.driverName = driverName;
        }
        /**
        * get the driverIdentity - 司机身份证号
        * @return the driverIdentity
        */
        public String getDriverIdentity() {
        return this.driverIdentity;
        }

        /**
        * set the driverIdentity - 司机身份证号
        */
        public void setDriverIdentity(String driverIdentity) {
        this.driverIdentity = driverIdentity;
        }
        /**
        * get the customerId - 承运商/客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 承运商/客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 承运商/客户名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 承运商/客户名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }
        /**
        * get the tel - 司机手机号
        * @return the tel
        */
        public String getTel() {
        return this.tel;
        }

        /**
        * set the tel - 司机手机号
        */
        public void setTel(String tel) {
        this.tel = tel;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }


        public String getVehicleNo() {
            return vehicleNo;
        }

        public void setVehicleNo(String vehicleNo) {
            this.vehicleNo = vehicleNo;
        }

            public String getSegName() {
            return segName;
        }

        public void setSegName(String segName) {
            this.segName = segName;
        }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
                setDriverIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverIdentity")), driverIdentity));
                setReservationIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationIdentity")), reservationIdentity));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tel")), tel));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), tenantId));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("driverName",StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
                map.put("driverIdentity",StringUtils.toString(driverIdentity, eiMetadata.getMeta("driverIdentity")));
                map.put("reservationIdentity",StringUtils.toString(reservationIdentity, eiMetadata.getMeta("reservationIdentity")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("tel",StringUtils.toString(tel, eiMetadata.getMeta("tel")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));

return map;

}
}