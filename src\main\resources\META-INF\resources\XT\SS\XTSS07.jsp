<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-windowId" cname="弹窗ID" type="hidden"/>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-segNo" required="true" cname="业务单元代码" colWidth="3" readonly="true"/>
            <EF:EFInput ename="inqu_status-0-segName" required="true" cname="业务单元简称" colWidth="3"
                        readonly="true"/>
            <EF:EFInput ename="inqu_status-0-loginName" cname="登录账号" placeholder="模糊条件" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-userName" cname="用户姓名" placeholder="模糊条件" colWidth="3"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="single, cell" readonly="true" sort="all">
            <EF:EFColumn ename="loginName" cname="登录账号" align="center" alias="t8.login_name"/>
            <EF:EFColumn ename="userName" cname="用户姓名" align="center" alias="t8.user_name"/>
            <EF:EFColumn ename="mobile" cname="手机号" align="center" alias="t8.mobile"/>
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>