package com.baosight.imom.vg.dm.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.hdsdk.domain.data.HDRecord;
import com.baosight.hdsdk.exception.HDSdkException;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.LIDS0701;
import com.baosight.imom.li.ds.domain.LIDS0901;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.imom.vi.pm.domain.VIPM0007;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;


/**
 * <AUTHOR> yzj
 * @Description : 设备当前作业
 * @Date : 2025/04/14
 * @Version : 1.0
 */
public class ServiceVGDM1002 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM1002.class);

    private static final BigDecimal NUMBER1000 = new BigDecimal("1000");
    private static final BigDecimal NUMBER200 = new BigDecimal("200");
    private static final BigDecimal NUMBER300 = new BigDecimal("300");
    private static final BigDecimal NUMBER20 = new BigDecimal("20");
    private static final BigDecimal NUMBER10 = new BigDecimal("10");
    private static final BigDecimal NUMBER30 = new BigDecimal("30");
    private static final BigDecimal NUMBER60 = new BigDecimal("60");
    private static final BigDecimal NUMBER70 = new BigDecimal("70");
    private static final BigDecimal NUMBER350 = new BigDecimal("350");
    private static final BigDecimal NUMBER500 = new BigDecimal("500");
    private static final BigDecimal NUMBER3 = new BigDecimal("3");
    private static final BigDecimal DENSITY = new BigDecimal("7.85");
    private static final BigDecimal NEGATIVE_NUMBER1 = new BigDecimal("-1");
    private static final int INT4 = 4;
    /**
     * 纵切称重点位 JC52SL05CHENG0001
     */
    private static final String SL_WEIGHT_ID = "1784";
    /**
     * 纵切下料小车出点位 JC52SL05MAIN0818 PLC_出料台车出Q_Q23.1
     */
    private static final String SL_OUT_PACK_ID = "1129";
    /**
     * 纵切前油压剪 JC52SL05MAIN0855 PLC_前油压剪下切Q_Q35.4
     */
    private static final String SL_FRONT_KNIFE_ID = "1166";
    /**
     * 1650横切堆垛判断点位 JC52CL01MAIN0348 PLC_送料平台上阀_Q17.7
     */
    private static final String CL01_STACK_ID = "1651";
    /**
     * 1650横切计数器点位 JC52CL011001 码垛计数数量1
     */
    private static final String CL01_STACK_COUNT_ID = "2066";

    /**
     * 800横切计数器点位2 JC52CL020034 1号码垛计数数量1
     */
    private static final String CL02_STACK1_COUNT_ID = "2068";
    /**
     * 800横切计数器点位2 JC52CL020037 2号码垛计数数量1
     */
    private static final String CL02_STACK2_COUNT_ID = "2072";
    /**
     * 2050落料堆垛堆放片数点位 JC52BL01DD0033 堆垛台堆垛数量
     */
    private static final String BL_STACK_PIECE_ID = "2100";
    /**
     * 2050落料堆垛选择 JC52BL01DD0034 选择堆垛1
     */
    private static final String BL_STACK1_SELECT = "2101";
    /**
     * 2050落料堆垛选择 JC52BL01DD0035 选择堆垛2
     */
    private static final String BL_STACK2_SELECT = "2102";
    /**
     * 2050落料堆垛选择 JC52BL01MAIN0123 PLC_侧堆垛准备好M810.1
     */
    private static final String BL_STACK3_SELECT = "2106";
    /**
     * 2050落料1#堆垛-操作侧
     */
    private static final String BL_STACK1_NAME = "JCQY250422046";
    /**
     * 2050落料1#堆垛-马达侧
     */
    private static final String BL_STACK1B_NAME = "JCQY250422077";
    /**
     * 2050落料2#堆垛-操作侧
     */
    private static final String BL_STACK2_NAME = "JCQY250422045";
    /**
     * 2050落料2#堆垛-马达侧
     */
    private static final String BL_STACK2B_NAME = "JCQY250422078";
    /**
     * 2050落料侧堆垛
     */
    private static final String BL_STACK3_NAME = "JCQY250618003";
    /**
     * 2050落料1#堆垛-操作侧 JC52BL01DD0027 PLC_1#堆垛操作侧小车出限位I61.1
     */
    private static final String BL_STACK1_OUT_ID = "2094";
    /**
     * 2050落料1#堆垛-操作侧 JC52BL01DD0028 PLC_1#堆垛操作侧小车入限位I61.3
     */
    private static final String BL_STACK1_IN_ID = "2095";
    /**
     * 2050落料1#堆垛-马达侧 JC52BL01DD0025 PLC_1#堆垛马达侧小车出限位I60.5
     */
    private static final String BL_STACK1B_OUT_ID = "2092";
    /**
     * 2050落料1#堆垛-马达侧 JC52BL01DD0026 PLC_1#堆垛马达侧小车入限位I60.7
     */
    private static final String BL_STACK1B_IN_ID = "2093";
    /**
     * 2050落料2#堆垛-操作侧 JC52BL01DD0031 PLC_2#堆垛操作侧小车出限位I63.1
     */
    private static final String BL_STACK2_OUT_ID = "2098";
    /**
     * 2050落料2#堆垛-操作侧 JC52BL01DD0032 PLC_2#堆垛操作侧小车入限位I63.3
     */
    private static final String BL_STACK2_IN_ID = "2099";
    /**
     * 2050落料2#堆垛-马达侧 JC52BL01DD0029 PLC_2#堆垛马达侧小车出限位I62.5
     */
    private static final String BL_STACK2B_OUT_ID = "2096";
    /**
     * 2050落料2#堆垛-马达侧 JC52BL01DD0030 PLC_2#堆垛马达侧小车入限位I62.7
     */
    private static final String BL_STACK2B_IN_ID = "2097";
    /**
     * 设备对应自动运行点位
     */
    private final Map<String, String> autoRunMap = new HashMap<>();
    /**
     * 设备开卷机逆转对应点位
     */
    private final Map<String, String> uncoilerBackMap = new HashMap<>();
    /**
     * 横切落料堆垛对照
     */
    private final Map<String, String> sphStackMap = new HashMap<>();
    /**
     * 横切落料退卷区
     */
    private final Map<String, String> sphBackMap = new HashMap<>();

    {
        // 纵切取JC52SL05MAIN0787 PLC_产线运行Q_Q16.7点位
        autoRunMap.put("52SL05", "1098");
        // 1650横切取JC52CL01MAIN0376 PLC_产线自动运行中I43.7
        autoRunMap.put("52CL01", "2086");
        // 800横切取JC52CL02MAIN0066 PLC_产线运转X033
        autoRunMap.put("52CL02", "245");
        // 2050落料取JC52BL01MAIN0120 PLC_产线运行Q_Q16.7点位
        autoRunMap.put("52BL01", "2083");
//        // 2050落料
//        sphMap.put("52BL01", "3");
        // 1650横切堆垛
        sphStackMap.put("JC52CL0110020", "JCQY250422035");
        sphStackMap.put("JC52CL0110021", "JCQY250422036");
        sphStackMap.put("JC52CL0110010", "JCQY250422035");
        sphStackMap.put("JC52CL0110011", "JCQY250422036");
        // 800横切堆垛
        sphStackMap.put("JC52CL020035", "JCQY250422040");
        sphStackMap.put("JC52CL020038", "JCQY250422039");
        sphStackMap.put("JC52CL020034", "JCQY250422040");
        sphStackMap.put("JC52CL020037", "JCQY250422039");
        // 1650横切退卷
        sphBackMap.put("52CL01", "JCQY250422034");
        // 800横切退卷
        sphBackMap.put("52CL02", "JCQY250422041");
        // 2050落料退卷
        sphBackMap.put("52BL01", "JCQY250422047");
        // 1650纵切   JC52SL05MAIN0603  PLC_开卷机逆寸_I32.1
        uncoilerBackMap.put("52SL05", "914");
        // 1650横切 JC52CL01MAIN0303   PLC_解板逆转输出_Q44.5
        uncoilerBackMap.put("52CL01", "1606");
        // 800横切 JC52CL02MAIN0016  PLC_开卷机逆寸M117
        uncoilerBackMap.put("52CL02", "195");
        // 2050落料 JC52BL01MAIN0118  PLC_M13.4_开卷机向后点动
        uncoilerBackMap.put("52BL01", "2059");
    }

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM1002().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        // 只查询未结束的捆包
        inInfo.setCell(EiConstant.queryBlock, 0, "activeFlag", "1");
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM1002.QUERY, new VGDM1002());
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
            List<Map> updList = new ArrayList<>();
            List<Map> resultDelList = new ArrayList<>();
            for (int i = 0; i < resultBlock.getRows().size(); i++) {
                VGDM1002 vgdm1002 = new VGDM1002();
                vgdm1002.fromMap(resultBlock.getRow(i));
                if (!"0".equals(vgdm1002.getDelFlag())) {
                    throw new PlatException("数据已删除");
                }
                vgdm1002.setDelFlag("1");
                Map updMap = vgdm1002.toMap();
                RecordUtils.setRevisor(updMap);
                updList.add(updMap);
                // 待删除工序时间结果
                Map<String, String> params = new HashMap<>();
                RecordUtils.setRevisor(params);
                params.put("relevanceId", vgdm1002.getUuid());
                resultDelList.add(params);
            }
            // 删除工序时间结果
            DaoUtils.updateBatch(dao, VGDM1003.UPDATE_BY_RELEVANCE, resultDelList);
            // 删除捆包作业
            DaoUtils.updateBatch(dao, VGDM1002.UPDATE_FOR_DEL, updList);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 上料
     * serviceId:S_VG_DM_1006
     * <p>
     * 接收设备上料信号后，查询上料捆包和工单信息，出库。
     * 入参：
     * tagId 点位
     */
    public EiInfo upPack(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("接收上料信息，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 校验
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 校验数量
            this.checkWorkCount(tagInfo.getSegNo(), tagInfo.getEArchivesNo());
            // 查找上料状态捆包
            VGDM1002 upPack = this.queryWorkPack(tagInfo, "0", false);
            if (upPack != null) {
                throw new PlatException("已有上料中捆包" + upPack.getPackId() + "，请先完成上料");
            }
            // 创建作业信息
            VGDM1002 vgdm1002 = this.createUpPack(tagInfo);
            // 出库实物库存
            Map<String, String> outMap = new HashMap<>();
            outMap.put("segNo", vgdm1002.getSegNo());
            outMap.put("packId", vgdm1002.getPackId());
            outMap.put("outMark", "1");
            RecordUtils.setRevisorSys(outMap);
            EiInfo tempInfo = new EiInfo();
            tempInfo.set("queryMap", outMap);
            tempInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            tempInfo.set(EiConstant.methodName, "inertInterfaceList");
            EiInfo rtnInfo = XLocalManager.call(tempInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 1650横切上料
     * serviceId:S_VG_DM_1020
     * <p>
     * 入参：
     * tagId 点位 JC52CL01MAIN0309 解板台车出料(近开卷机)_Q44.1
     * tagValue 开卷支撑状态 1为上0为下
     */
    public EiInfo upCL01Pack(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            String tagValue = inInfo.getString("tagValue");
            if (StrUtil.isBlank(tagId) || StrUtil.isBlank(tagValue)) {
                throw new PlatException("传入参数为空！");
            }
            log("1650横切上料参数：" + tagId + "|" + tagValue);
            // 校验
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 校验开卷支撑为下
            if ("1".equals(tagValue)) {
                throw new PlatException("开卷支撑为上，不能上料");
            }
            // 校验数量
            this.checkWorkCount(tagInfo.getSegNo(), tagInfo.getEArchivesNo());
            // 查找上料状态捆包
            VGDM1002 upPack = this.queryWorkPack(tagInfo, "0", false);
            if (upPack != null) {
                throw new PlatException("已有上料中捆包" + upPack.getPackId() + "，请先完成上料");
            }
            // 创建作业信息
            VGDM1002 vgdm1002 = this.createUpPack(tagInfo);
            // 出库实物库存
            Map<String, String> outMap = new HashMap<>();
            outMap.put("segNo", vgdm1002.getSegNo());
            outMap.put("packId", vgdm1002.getPackId());
            outMap.put("outMark", "1");
            RecordUtils.setRevisorSys(outMap);
            EiInfo tempInfo = new EiInfo();
            tempInfo.set("queryMap", outMap);
            tempInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            tempInfo.set(EiConstant.methodName, "inertInterfaceList");
            EiInfo rtnInfo = XLocalManager.call(tempInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 落料上料
     * serviceId:S_VG_DM_1013
     * <p>
     * 接收设备上料信号后，查询上料捆包和工单信息，出库。
     * 入参：
     * tagId 点位
     */
    public EiInfo upBLPack(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("接收落料上料信息，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 校验
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 校验点位 JC52BL01MAIN0011 PLC_捆包在夹送辊入口检测I20.2
            HDRecord record = IhdSdkUtils.querySnapshot(tagInfo.getSegNo(), "1685");
            if ("1".equals(record.getValueStr())) {
                throw new PlatException("捆包在夹送辊入口，无法上料" + record.getTimeStampStr());
            }
            // 查找上料状态捆包
            VGDM1002 upPack = this.queryWorkPack(tagInfo, "0", false);
            if (upPack != null) {
                throw new PlatException("已有上料中捆包" + upPack.getPackId() + "，请先完成上料");
            }
            // 创建作业信息
            VGDM1002 vgdm1002 = this.createUpPack(tagInfo);
            // 出库实物库存
            Map<String, String> outMap = new HashMap<>();
            outMap.put("segNo", vgdm1002.getSegNo());
            outMap.put("packId", vgdm1002.getPackId());
            outMap.put("outMark", "1");
            RecordUtils.setRevisorSys(outMap);
            EiInfo tempInfo = new EiInfo();
            tempInfo.set("queryMap", outMap);
            tempInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            tempInfo.set(EiConstant.methodName, "inertInterfaceList");
            EiInfo rtnInfo = XLocalManager.call(tempInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 启动工单
     * serviceId:S_VG_DM_1001
     * <p>
     * 接收设备上料信号后，查询上料捆包和工单信息，出库并启动工单。
     * 入参：
     * tagId 点位
     */
    public EiInfo startProcessOrder(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("接收启动工单信息，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 获取点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 校验并获取待启动工单信息
            VGDM1002 vgdm1002 = this.checkTodoPack(tagInfo);
            // 启动工单
            this.startOrder(vgdm1002);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 更新纵切机组上料时间
     * 信号
     *
     * @param vgdm1002 捆包信息
     */
    private void updateSLUpTime(VGDM1002 vgdm1002) throws Exception {
        if (vgdm1002.checkIsSL()) {
            // 更新纵切上料时间，判断点位 JC52SL05MAIN0833 入料台车上Q32.0  JC52SL05MAIN0588 入料台车入I30.2
            this.updateUpPackTime(vgdm1002, "1144", "899");
        }
    }

    /**
     * 更新机组上料时间
     * <p>
     * 原上料时间距当前时间大于5分钟时查找最近的上料点位更新上料时间
     *
     * @param vgdm1002 捆包信息
     */
    private void updateUpPackTime(VGDM1002 vgdm1002, String... tagIhdIds) throws Exception {
        log(vgdm1002.getEArchivesNo() + "|准备更新上料时间,点位" + Arrays.toString(tagIhdIds));
        LocalDateTime nowLdt = LocalDateTime.now();
        String nowTime = DateUtils.FORMATTER_14.format(nowLdt);
        // 原上料时间
        String time = vgdm1002.getUpPackTime();
        LocalDateTime upTimeLdt = LocalDateTime.parse(time, DateUtils.FORMATTER_14);
        if (this.checkUpTime(time, nowLdt)) {
            log("无需更新上料时间");
            return;
        }
        boolean flag = false;
        for (String tagIhdId : tagIhdIds) {
            // 查询从上料时间到当前时间点位信息
            List<HDRecord> recordList = this.queryIhdRecord(vgdm1002.getSegNo()
                    , vgdm1002.getUpPackTime(), nowTime, tagIhdId);
            String tempTime = this.getOneValueTime(recordList, false);
            if (tempTime != null) {
                // 更新时间，当找不5分钟内的时间时取最近一个时间
                time = tempTime;
            }
            if (this.checkUpTime(tempTime, nowLdt)) {
                log("找到符合的入料台车上时间" + time);
                flag = true;
                break;
            }
            log(tagIhdId + "未找到符合的入料台车上时间");
        }
        LocalDateTime newTimeLdt = LocalDateTime.parse(time, DateUtils.FORMATTER_14);
        // 没有距离当前时间5分钟以内或晚于原上料时间的数据
        if (!flag && newTimeLdt.isBefore(upTimeLdt)) {
            log("未找到符合的入料台车上时间");
            return;
        }
        log("更新机组上料时间：" + time);
        // 更新上料时间
        vgdm1002.setUpPackTime(time);
        Map updMap = vgdm1002.toMap();
        dao.update(VGDM1002.UPDATE_UP_TIME, updMap);
    }

    /**
     * 校验上料时间距离开卷时间是否在5分钟内
     *
     * @param time   待校验上料时间
     * @param nowLdt 开卷时间
     * @return 是否符合要求
     */
    private boolean checkUpTime(String time, LocalDateTime nowLdt) {
        if (StrUtil.isBlank(time)) {
            log("上料时间为空");
            return false;
        }
        LocalDateTime timeLdt = LocalDateTime.parse(time, DateUtils.FORMATTER_14);
        long duration = ChronoUnit.MINUTES.between(timeLdt, nowLdt);
        log("距上料时间差（分钟）：" + duration);
        return duration < 5;
    }

    /**
     * 启动工单
     *
     * @param vgdm1002 工单
     */
    private void startOrder(VGDM1002 vgdm1002) throws Exception {
        // 更新上料时间
        this.updateSLUpTime(vgdm1002);
        // 更新捆包状态为加工中
        vgdm1002.setPackStatus("1");
        vgdm1002.setStartTime(DateUtil.curDateTimeStr14());
        Map updMap = vgdm1002.toMap();
        RecordUtils.setRevisorSys(updMap);
        dao.update(VGDM1002.UPDATE, updMap);
        String switchValue = (new SwitchUtils()).getProcessSwitchValue(vgdm1002.getSegNo(), "IF_SEND_ACTUAL_PRODUCT_IMC", dao);
        log("配置开关：" + switchValue);
        if ("1".equals(switchValue)) {
            // 启动工单
            log("启动工单参数：" + vgdm1002.getSegNo() + "|" + vgdm1002.getPackId() + "|" + vgdm1002.getProcessOrderId());
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("segNo", vgdm1002.getSegNo());
            eiInfo.set("packId", vgdm1002.getPackId());
            eiInfo.set("processOrderId", vgdm1002.getProcessOrderId());
            eiInfo.set(EiConstant.serviceName, "VIPMInterfaces");
            eiInfo.set(EiConstant.methodName, "startOrderAndOutbound");
            EiInfo rtnInfo = XLocalManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            log("调用成功，返回消息：" + rtnInfo.getMsg());
        }
    }

    /**
     * 启动工单--1650横切
     * serviceId:S_VG_DM_1016
     * <p>
     * 接收设备上料信号后，查询上料捆包和工单信息，出库并启动工单。
     * 入参：
     * tagId 点位
     */
    public EiInfo startCL01Order(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("接收启动工单信息，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 获取点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 校验并获取待启动工单信息
            VGDM1002 vgdm1002 = this.checkTodoPack(tagInfo);
            log("并包判断");
            // 查找并包捆包
            VGDM1002 curPack = this.queryWorkPack(tagInfo, "1", false);
            if (curPack != null && curPack.checkIsUnited()) {
                // 横切
                this.updateCLUnited(vgdm1002, curPack, tagInfo);
            }
            // 更新1650横切上料时间，判断点位 JC52CL01MAIN0349 台车上阀_Q12.0 JC52CL01MAIN0309 解板台车出料(近开卷机)_Q44.1
            this.updateUpPackTime(vgdm1002, "1652", "1612");
            // 启动工单
            this.startOrder(vgdm1002);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 启动工单-800飞剪
     * serviceId:S_VG_DM_1017
     * <p>
     * 接收设备上料信号后，查询上料捆包和工单信息，出库并启动工单。
     * 入参：
     * tagId 点位
     */
    public EiInfo startCL02Order(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            log("接收800横切启动工单信息，点位：" + tagId);
            // 获取点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 人工触发标记
            String notAutoFlag = inInfo.getString("notAutoFlag");
            if (StrUtil.isNotBlank(notAutoFlag)) {
                log("人工操作触发启动工单，重新获取tagId");
                // JC52CL02DD0082  收料架选择 2#_Y003  ,默认1#计数器
                tagId = "JC52CL020034";
                HDRecord record = IhdSdkUtils.querySnapshot(tagInfo.getSegNo(), "1890");
                if (record != null && "1".equals(record.getValueStr())) {
                    tagId = "JC52CL020037";
                }
                log("tagId：" + tagId);
                // 更新点位信息
                tagInfo = this.queryTag(tagId);
            } else {
                notAutoFlag = "0";
            }
            log("非自动触发标记：" + notAutoFlag);
            // 校验并获取待启动工单信息
            VGDM1002 vgdm1002 = this.checkTodoPack(tagInfo);
            log("并包判断");
            // 查找并包捆包
            VGDM1002 curPack = this.queryWorkPack(tagInfo, "1", false);
            if (curPack != null && curPack.checkIsUnited()) {
                // 800横切
                this.updateCL02United(vgdm1002, curPack, tagInfo);
            }
            // 更新800横切上料时间，判断点位 JC52CL02MAIN0001 入料台车上M100
            this.updateUpPackTime(vgdm1002, "180");
            // 启动工单
            this.startOrder(vgdm1002);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 启动工单-2050落料
     * serviceId:S_VG_DM_1018
     * <p>
     * 接收设备上料信号后，查询上料捆包和工单信息，出库并启动工单。
     * 入参：
     * tagId 点位
     */
    public EiInfo startBLOrder(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("接收启动工单信息，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 获取点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 校验并获取待启动工单信息
            VGDM1002 vgdm1002 = this.checkTodoPack(tagInfo);
            log("并包判断");
            // 查找并包捆包
            VGDM1002 curPack = this.queryWorkPack(tagInfo, "1", false);
            if (curPack != null && curPack.checkIsUnited()) {
                // 落料
                this.updateBLUnited(vgdm1002, curPack);
            }
            // 启动工单
            this.startOrder(vgdm1002);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 更新落料并包信息
     *
     * @param vgdm1002 待启动工单
     * @param curPack  当前捆包
     * @throws Exception 异常
     */
    private void updateBLUnited(VGDM1002 vgdm1002, VGDM1002 curPack) throws Exception {
        log("落料并包更新");
        // 时间
        String tagTimeStr = DateUtil.curDateTimeStr14();
        // 工单相同时生成尾包并包信息
        if (curPack.getProcessOrderId().equals(vgdm1002.getProcessOrderId())) {
            log("落料尾包并包生成");
            // 单个堆垛的片数
            int repeatCount = this.getBLStackPiece(curPack.getSegNo(), tagTimeStr);
            // 是否有侧堆垛
            boolean thirdFlag = this.checkThirdStack(curPack.getSegNo(), tagTimeStr);
            // 产出捆包信息
            VIPM0007 outPack = this.getCLOutPack(curPack);
            // 堆垛信息
            String[] stackNameArr = curPack.getUnitedStackName().split(",");
            // 并包号
            String unitedPackId = curPack.getUnitedPackId();
            log("并包号：" + unitedPackId);
            String[] unitedPackIdArr = unitedPackId.split(",");
            // 堆垛计数器数量
            BigDecimal quantity = curPack.getUnitedQuantity().subtract(curPack.getSumUnitedQuantity());
            log("并包量：" + quantity);
            // 加工工时
            BigDecimal processHour = this.calProcessHour(curPack, tagTimeStr);
            // 生成实绩
            curPack.setUnitedPackId(unitedPackIdArr[0]);
            log("第一个实绩");
            this.createBLPack(curPack
                    , outPack
                    , quantity
                    , "2"
                    , stackNameArr[0]
                    , BigDecimal.ZERO, null, processHour);
            if (stackNameArr.length > 1) {
                log("双堆垛第二个实绩");
                curPack.setUnitedPackId(unitedPackIdArr[1]);
                this.createBLPack(curPack
                        , outPack
                        , quantity
                        , "2"
                        , stackNameArr[1]
                        , BigDecimal.ZERO, null, processHour);
            }
            if (repeatCount > 1) {
                log("一出二第二个实绩");
                curPack.setUnitedPackId(unitedPackIdArr[1]);
                this.createBLPack(curPack
                        , outPack
                        , quantity
                        , "2"
                        , stackNameArr[0]
                        , BigDecimal.ZERO, null, processHour);
            }
            if (thirdFlag) {
                log("侧堆垛实绩");
                curPack.setUnitedPackId(unitedPackIdArr[1]);
                this.createBLPack(curPack
                        , outPack
                        , quantity
                        , "2"
                        , BL_STACK3_NAME
                        , BigDecimal.ZERO, null, processHour);
            }
            // 还原并包号
            curPack.setUnitedPackId(unitedPackId);
            // 结束捆包
            this.endPackWork(curPack, EndType.NORMAL);
            // 上传IMC
            this.sendCLResult(curPack);
            // 更新当前捆包并包信息
            vgdm1002.setUnitedPackId(curPack.getUnitedPackId());
            vgdm1002.setUnitedStackName(curPack.getUnitedStackName());
            vgdm1002.setUnitedQuantity(quantity.add(curPack.getSumUnitedQuantity()));
            vgdm1002.setSumUnitedQuantity(vgdm1002.getUnitedQuantity());
            vgdm1002.setUnitedUuid(curPack.getUuid());
            this.updatePackWork(vgdm1002);
        } else {
            log("工单不同，非并包");
            // 获取堆垛
            String stackName = this.getBLStackName(vgdm1002.getSegNo(), DateUtil.curDateTimeStr14());
            // 获取计数器
            BigDecimal quantity = this.getBLQuantity(vgdm1002.getSegNo(), stackName);
            // 清除并包信息
            curPack.setUnitedQuantity(BigDecimal.ZERO);
            curPack.setSumUnitedQuantity(BigDecimal.ZERO);
            curPack.setUnitedPackId(" ");
            curPack.setUnitedStackName(" ");
            curPack.setUnitedUuid(" ");
            this.updatePackWork(curPack);
            // 堆垛相同且计数器为0时结束捆包
            if (stackName.equals(curPack.getEndStackName())
                    && quantity.compareTo(BigDecimal.ZERO) == 0) {
                log("结束捆包");
                // 结束捆包
                this.endPackWork(curPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(curPack);
            }
        }
    }

    /**
     * 更新横切并包信息
     *
     * @param vgdm1002 待启动工单
     * @param curPack  当前捆包
     * @throws Exception 异常
     */
    private void updateCLUnited(VGDM1002 vgdm1002, VGDM1002 curPack, VGDM0301 tagInfo) throws Exception {
        log("1650横切并包更新");
        // 获取计数器数量
        BigDecimal quantity = this.getSphQuantity(tagInfo.getSegNo(), tagInfo.getTagIhdId().toString());
        if (curPack.getProcessOrderId().equals(vgdm1002.getProcessOrderId())) {
            log("工单相同");
            if (quantity.compareTo(BigDecimal.ZERO) > 0) {
                log("并包数量：" + quantity);
                curPack.setUnitedQuantity(quantity);
                // 产出捆包信息
                VIPM0007 outPack = this.getCLOutPack(curPack);
                // 查找计数器最后一个数的时间
                String nowTime = DateUtil.curDateTimeStr14();
                String quantityTime = this.getQuantityTime(quantity
                        , curPack.getSegNo()
                        , tagInfo.getTagIhdId().toString()
                        , nowTime);
                log("计数器数字时间：" + quantityTime);
                // 计算加工工时
                BigDecimal processHour = this.calProcessHour(curPack, quantityTime);
                String stackName = MapUtils.getString(sphStackMap, tagInfo.getTagId(), " ");
                log("堆垛名称：" + stackName);
                if (curPack.checkIsCL01()) {
                    log("1650横切并包堆垛判断1");
                    // 取计数器时间的送料平台点位，1时为2#堆垛，0时为1#堆垛
                    HDRecord record = IhdSdkUtils.querySingle(curPack.getSegNo()
                            , CL01_STACK_ID
                            , quantityTime
                            , true);
                    String tackTagId = tagInfo.getTagId() + record.getValueStr();
                    log("堆垛点位：" + tackTagId);
                    stackName = MapUtils.getString(sphStackMap, tackTagId, " ");
                    log("堆垛名称：" + stackName);
                }
                // 生成实绩信息
                this.createSphPack(curPack, outPack, quantity, "2", stackName, processHour, quantityTime);
                // 结束捆包
                this.endPackWork(curPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(curPack);
                // 更新当前捆包并包信息
                vgdm1002.setUnitedPackId(curPack.getUnitedPackId());
                vgdm1002.setUnitedStackName(curPack.getUnitedStackName());
                vgdm1002.setUnitedQuantity(quantity);
                vgdm1002.setUnitedUuid(curPack.getUuid());
                this.updatePackWork(vgdm1002);
            }
        } else {
            log("工单不同，非并包");
            // 清除并包信息
            curPack.setUnitedQuantity(BigDecimal.ZERO);
            curPack.setUnitedPackId(" ");
            curPack.setUnitedStackName(" ");
            curPack.setUnitedUuid(" ");
            this.endPackWork(curPack, EndType.TAIL);
        }
        // 为0时结束捆包
        if (quantity.compareTo(BigDecimal.ZERO) == 0) {
            this.endPackWork(curPack, EndType.NORMAL);
            // 上传IMC
            this.sendCLResult(curPack);
        }
    }

    /**
     * 更新800横切并包信息
     *
     * @param vgdm1002 待启动工单
     * @param curPack  当前捆包
     * @throws Exception 异常
     */
    private void updateCL02United(VGDM1002 vgdm1002, VGDM1002 curPack, VGDM0301 tagInfo) throws Exception {
        log("800横切并包更新");
        // 获取计数器数量
        BigDecimal quantity = this.getSphQuantity(tagInfo.getSegNo(), tagInfo.getTagIhdId().toString());
        if (curPack.getProcessOrderId().equals(vgdm1002.getProcessOrderId())) {
            log("工单相同");
            if (quantity.compareTo(BigDecimal.ZERO) > 0) {
                log("并包数量：" + quantity);
                curPack.setUnitedQuantity(quantity);
                // 产出捆包信息
                VIPM0007 outPack = this.getCLOutPack(curPack);
                // 查找计数器最后一个数的时间
                String nowTime = DateUtil.curDateTimeStr14();
                String quantityTime = this.getQuantityTime(quantity
                        , curPack.getSegNo()
                        , tagInfo.getTagIhdId().toString()
                        , nowTime);
                log("计数器数字时间：" + quantityTime);
                // 计算加工工时
                BigDecimal processHour = this.calProcessHour(curPack, quantityTime);
                String stackName = MapUtils.getString(sphStackMap, tagInfo.getTagId(), " ");
                log("堆垛名称：" + stackName);
                // 标记是否相同原始捆包号
                boolean sameOriginalPack = curPack.getOriginalPackId().equals(vgdm1002.getOriginalPackId());
                log("是否相同原始捆包号：" + sameOriginalPack + "|" + curPack.getOriginalPackId());
                if (!sameOriginalPack) {
                    // 生成实绩信息
                    this.createSphPack(curPack, outPack, quantity, "2", stackName, processHour, quantityTime);
                }
                // 结束捆包
                this.endPackWork(curPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(curPack);
                // 更新当前捆包并包信息
                vgdm1002.setUnitedPackId(curPack.getUnitedPackId());
                vgdm1002.setUnitedStackName(curPack.getUnitedStackName());
                vgdm1002.setUnitedQuantity(quantity);
                // 记录上一并包投料信息
                if (sameOriginalPack) {
                    // 同母捆包时记录上一并包投料信息id
                    vgdm1002.setUnitedUuid(curPack.getUuid());
                } else {
                    // 非同母捆包时特殊标记
                    vgdm1002.setUnitedUuid(MesConstant.APP_CODE);
                }
                this.updatePackWork(vgdm1002);
            }
        } else {
            log("工单不同，非并包");
            // 清除并包信息
            curPack.setUnitedQuantity(BigDecimal.ZERO);
            curPack.setUnitedPackId(" ");
            curPack.setUnitedStackName(" ");
            curPack.setUnitedUuid(" ");
            this.endPackWork(curPack, EndType.TAIL);
        }
        // 为0时结束捆包
        if (quantity.compareTo(BigDecimal.ZERO) == 0) {
            this.endPackWork(curPack, EndType.NORMAL);
            // 上传IMC
            this.sendCLResult(curPack);
        }
    }

    /**
     * 非纵切获取产出信息
     *
     * @param inPack 投料信息
     * @return 产出信息
     */
    private VIPM0007 getCLOutPack(VGDM1002 inPack) {
        // 获取理论产出数据
        List<VIPM0007> outPackList = new ArrayList<>();
        inPack.queryOutPack(dao, outPackList);
        // 返回第一个规格
        VIPM0007 outPack = outPackList.get(0);
        // 借用字段存总数量
        outPack.setProductionProcessedQuantity(outPack.getProductProcessQty());
        // 更新单片重量
        outPack.setProcessSinglePackWeight(VGDM1005.queryPieceWeight(outPack.getPartId()));
        return outPack;
    }

    /**
     * 横切尾包或并包判断
     * serviceId:S_VG_DM_1005
     * <p>
     * 1650横切-中间桥B上升，判断据开卷时间大于5分钟，
     * 判断同一工单下是否存在未加工捆包，存在且计数器不为0说明有并包
     * <p>
     * 800横切-尾料标记为1时触发
     */
    public EiInfo checkCLEnd(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("横切尾包或并包判断，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            log(tagInfo.getEArchivesNo() + "判断尾包");
            // 当前捆包
            VGDM1002 resultPack = this.getPackForResult(tagInfo);
            // 已经是尾料或余料时不处理
            if (resultPack.checkIsEnd()
                    || resultPack.checkIsLeftPack()) {
                throw new PlatException("标记不为空：" + resultPack.getEndType());
            }
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
            // 校验数量是否从0开始
            boolean flag = this.checkIsCurQuantity(tagInfo.getSegNo()
                    , resultPack.getStartTime()
                    , DateUtil.curDateTimeStr14()
                    , tagInfo.getTagIhdId().toString());
            if (sumQuantity.compareTo(BigDecimal.ZERO) <= 0 && !flag) {
                throw new PlatException("未产出实绩");
            }
            log("开卷时间判断");
            LocalDateTime localDateTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
            long minutes = Duration.between(localDateTime, LocalDateTime.now()).toMinutes();
            log("时间差值m：" + minutes);
            if (minutes < 5) {
                throw new PlatException("距开卷时间过短:" + minutes);
            }
            String stackName = MapUtils.getString(sphStackMap, tagId, " ");
            resultPack.setEndStackName(stackName);
            // 查找当前工单下未加工的捆包
            Map<String, String> map = new HashMap<>();
            map.put("processOrderId", resultPack.getProcessOrderId());
            map.put("segNo", resultPack.getSegNo());
            int aa = super.count(VGDM1002.COUNT_NOT_DO, map);
            log("未加工捆包数：" + aa);
            // 获取计数器数量
            BigDecimal quantity = this.getSphQuantity(tagInfo.getSegNo(), tagInfo.getTagIhdId().toString());
            // 更新尾包标记
            this.endPackWork(resultPack, EndType.TAIL);
            // 并包号
            log("已有并包号：" + resultPack.getUnitedPackId());
            // 无并包信息且未加工数大于0且数量大于0
            if (!resultPack.checkIsUnited()
                    && aa > 0
                    && quantity.compareTo(BigDecimal.ZERO) > 0) {
                log("并包信息");
                // 并包信息
                String unitedPackId = generatePackId(resultPack.getSegNo(), "B");
                resultPack.setUnitedPackId(unitedPackId);
                resultPack.setUnitedQuantity(quantity);
                resultPack.setUnitedStackName(stackName);
                resultPack.setUnitedUuid(resultPack.getUuid());
                this.updatePackWork(resultPack);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 800横切尾包或并包判断
     * serviceId:S_VG_DM_1015
     * <p>
     * 800横切-尾料标记为1时触发
     */
    public EiInfo checkCL02End(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("800飞剪尾包或并包判断，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 当前捆包
            VGDM1002 resultPack = this.getPackForResult(tagInfo);
            // 已经是尾料或余料时不处理
            if (resultPack.checkIsEnd()
                    || resultPack.checkIsLeftPack()) {
                throw new PlatException("标记不为空：" + resultPack.getEndType());
            }
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
            // 校验计数器是否有变化
            boolean flag = this.checkQuantityChange(tagInfo.getSegNo()
                    , resultPack.getStartTime()
                    , DateUtil.curDateTimeStr14()
                    , tagInfo.getTagIhdId().toString());
            if (sumQuantity.compareTo(BigDecimal.ZERO) <= 0 && !flag) {
                throw new PlatException("未产出实绩且计数器未变化");
            }
            log("开卷时间判断");
            LocalDateTime localDateTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
            long minutes = Duration.between(localDateTime, LocalDateTime.now()).toMinutes();
            log("时间差值m：" + minutes);
            if (minutes < 5) {
                throw new PlatException("距开卷时间过短:" + minutes);
            }
            String stackName = MapUtils.getString(sphStackMap, tagId, " ");
            resultPack.setEndStackName(stackName);
            // 查找当前工单下未加工的捆包
            Map<String, String> map = new HashMap<>();
            map.put("processOrderId", resultPack.getProcessOrderId());
            map.put("segNo", resultPack.getSegNo());
            int aa = super.count(VGDM1002.COUNT_NOT_DO, map);
            log("未加工捆包数：" + aa);
            // 获取计数器数量
            BigDecimal quantity = this.getSphQuantity(tagInfo.getSegNo(), tagInfo.getTagIhdId().toString());
            // 更新尾包标记
            this.endPackWork(resultPack, EndType.TAIL);
            // 并包号
            log("已有并包号：" + resultPack.getUnitedPackId());
            // 无并包信息且未加工数大于0且数量大于0
            if (!resultPack.checkIsUnited()
                    && aa > 0
                    && quantity.compareTo(BigDecimal.ZERO) > 0) {
                log("并包信息");
                // 并包信息
                String unitedPackId = generatePackId(resultPack.getSegNo(), "B");
                resultPack.setUnitedPackId(unitedPackId);
                resultPack.setUnitedQuantity(quantity);
                resultPack.setUnitedStackName(stackName);
                resultPack.setUnitedUuid(resultPack.getUuid());
                this.updatePackWork(resultPack);
            } else {
                if (quantity.compareTo(BigDecimal.ONE) < 0) {
                    log("800飞剪数量小于1");
                    // 结束捆包
                    this.endPackWork(resultPack, EndType.NORMAL);
                    // 上传IMC
                    this.sendCLResult(resultPack);
                }
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 落料尾包判断1
     * serviceId:S_VG_DM_1009
     *
     * <p>
     * 2050落料-活套入料辊入口处存在材料I37.0
     */
    public EiInfo checkBLEndS(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("落料尾包判断1，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 实绩捆包
            VGDM1002 resultPack = this.getPackForResult(tagInfo);
            // 已经是尾料或余料时
            log("标记判断1：" + resultPack.getEndType() + "|" + resultPack.getEndStackName());
            if (resultPack.checkIsEnd()
                    || resultPack.checkIsLeftPack()
                    || StrUtil.isNotBlank(resultPack.getEndStackName())) {
                throw new PlatException("标记不为空：" + resultPack.getEndType() + "|" + resultPack.getEndStackName());
            }
            // 更新尾包堆垛信息
            String tagTimeStr = DateUtil.curDateTimeStr14();
            String stackName = this.getBLStackName(tagInfo.getSegNo(), tagTimeStr);
            resultPack.setEndStackName(stackName);
            this.updatePackWork(resultPack);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 落料尾包或并包判断
     * serviceId:S_VG_DM_1010
     * 判断同一工单下是否存在未加工捆包，存在且计数器不为0说明有并包
     * <p>
     * 2050落料-出料辊信号为0时触发
     */
    public EiInfo checkBLEndE(EiInfo inInfo) {
        try {
            // tag点
            String tagId = inInfo.getString("tagId");
            log("落料尾包或并包判断2，点位：" + tagId);
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 当前捆包
            VGDM1002 resultPack = this.getPackForResult(tagInfo);
            // 已经是并包或尾料或余料时不处理
            if (resultPack.checkIsEnd()
                    || resultPack.checkIsLeftPack()) {
                throw new PlatException("标记不为空：" + resultPack.getEndType());
            }
            String tagTimeStr = DateUtil.curDateTimeStr14();
            // 落料尾包堆垛
            String stackName = resultPack.getEndStackName();
            if (StrUtil.isBlank(stackName)) {
                stackName = this.getBLStackName(tagInfo.getSegNo(), tagTimeStr);
            }
            // 获取堆垛数量
            BigDecimal quantity = this.getBLQuantity(resultPack.getSegNo(), stackName);
            // 已有实绩
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
            if (sumQuantity.compareTo(BigDecimal.ONE) < 0 && quantity.compareTo(BigDecimal.ONE) < 0) {
                throw new PlatException("未产出实绩");
            }
            // 数量为0结束捆包
            if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                // 无并包，更新尾包标记
                this.endPackWork(resultPack, EndType.NORMAL);
                this.sendCLResult(resultPack);
                return inInfo;
            }
            // 查找当前工单下未加工的捆包
            Map<String, String> map = new HashMap<>();
            map.put("processOrderId", resultPack.getProcessOrderId());
            map.put("segNo", resultPack.getSegNo());
            int aa = super.count(VGDM1002.COUNT_NOT_DO, map);
            log("未加工捆包数：" + aa);
            // 存在未加工捆包，
            if (aa > 0) {
                // 连续并包
                if (resultPack.checkIsUnited()) {
                    log("连续并包，更新并包量");
                    resultPack.setSumUnitedQuantity(resultPack.getUnitedQuantity());
                    resultPack.setUnitedQuantity(quantity);
                } else {
                    // 并包号
                    String[] stackNameArr = stackName.split(",");
                    // 是否有侧堆垛
                    boolean thirdFlag = this.checkThirdStack(tagInfo.getSegNo(), tagTimeStr);
                    // 单个堆垛的片数
                    int repeatCount = this.getBLStackPiece(tagInfo.getSegNo(), tagTimeStr);
                    // 第一个并包号
                    String unitedPackId = generatePackId(resultPack.getSegNo(), "B");
                    if (stackNameArr.length > 1 || thirdFlag || repeatCount > 1) {
                        log("双堆垛或侧堆垛或一出二并包号");
                        String newUnitedPackId = generatePackId(resultPack.getSegNo(), "B");
                        unitedPackId = unitedPackId + "," + newUnitedPackId;
                    }
                    resultPack.setUnitedPackId(unitedPackId);
                    resultPack.setUnitedQuantity(quantity);
                    resultPack.setUnitedStackName(stackName);
                    resultPack.setUnitedUuid(resultPack.getUuid());
                }
            } else {
                log("无并包");
                resultPack.setUnitedQuantity(BigDecimal.ZERO);
                resultPack.setSumUnitedQuantity(BigDecimal.ZERO);
                resultPack.setUnitedPackId(" ");
                resultPack.setUnitedStackName(" ");
                resultPack.setUnitedUuid(" ");
            }
            // 更新尾包标记
            this.endPackWork(resultPack, EndType.TAIL);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 获取落料堆垛
     *
     * @param segNo   账套
     * @param tagTime 时间，格式yyyyMMddHHmmss
     * @return 堆垛名称，多个以，分割
     * @throws Exception 异常
     */
    private String getBLStackName(String segNo, String tagTime) throws Exception {
        log("获取落料堆垛,时间点：" + tagTime);
        List<String> nameList = new ArrayList<>();
        // 判断是否为1#堆垛
        HDRecord record = IhdSdkUtils.querySingle(segNo, BL_STACK1_SELECT, tagTime, true);
        int a = Integer.parseInt(record.getValueStr());
        if (a > 0) {
            log("1#堆垛");
            // 判断是否为操作侧
            record = IhdSdkUtils.querySingle(segNo, BL_STACK1_OUT_ID, tagTime, false);
            a = Integer.parseInt(record.getValueStr());
            if (a < 1) {
                log("1#堆垛操作侧");
                nameList.add(BL_STACK1_NAME);
            } else {
                log("1#堆垛马达侧");
                nameList.add(BL_STACK1B_NAME);
            }
        }
        // 2#堆垛
        record = IhdSdkUtils.querySingle(segNo, BL_STACK2_SELECT, tagTime, true);
        a = Integer.parseInt(record.getValueStr());
        if (a > 0) {
            log("2#堆垛");
            // 判断是否为操作侧
            record = IhdSdkUtils.querySingle(segNo, BL_STACK2_OUT_ID, tagTime, false);
            a = Integer.parseInt(record.getValueStr());
            if (a < 1) {
                log("2#堆垛操作侧");
                nameList.add(BL_STACK2_NAME);
            } else {
                log("2#堆垛马达侧");
                nameList.add(BL_STACK2B_NAME);
            }
        }
        if (CollectionUtils.isEmpty(nameList)) {
            log("无堆垛");
            return " ";
        }
        return String.join(",", nameList);
    }

    /**
     * 校验是否有侧堆垛
     *
     * @param segNo   账套
     * @param tagTime 时间，格式yyyyMMddHHmmss
     * @return boolean
     * @throws Exception 异常
     */
    private boolean checkThirdStack(String segNo, String tagTime) throws Exception {
        // 侧堆垛
        HDRecord record = IhdSdkUtils.querySingle(segNo, BL_STACK3_SELECT, tagTime, true);
        int a = Integer.parseInt(record.getValueStr());
        if (a > 0) {
            log("侧堆垛");
            return true;
        }
        log("无侧堆垛");
        return false;
    }

    /**
     * 根据tag筛选堆垛名称
     *
     * @param stackName 堆垛
     * @param tagId     tag
     */
    private String getBLStackWithTag(String stackName, String tagId) {
        // 1#计数器
        if (("JC52BL01DD0023").equals(tagId)) {
            if (stackName.contains(BL_STACK1_NAME)) {
                return BL_STACK1_NAME;
            } else {
                return BL_STACK1B_NAME;
            }
        } else {
            if (stackName.contains(BL_STACK2_NAME)) {
                return BL_STACK2_NAME;
            } else {
                return BL_STACK2B_NAME;
            }
        }
    }

    /**
     * 获取落料计数器值
     *
     * @param segNo     账套
     * @param stackName 堆垛名称
     * @return 计数器值
     */
    private BigDecimal getBLQuantity(String segNo, String stackName) throws Exception {
        log("获取落料堆垛计数:" + stackName);
        if (StrUtil.isBlank(stackName)) {
            return BigDecimal.ZERO;
        }
        String[] stackNameArr = stackName.split(",");
        stackName = stackNameArr[0];
        BigDecimal curQuantity;
        if (BL_STACK1_NAME.equals(stackName) || BL_STACK1B_NAME.equals(stackName)) {
            log("获取落料堆垛1计数");
            curQuantity = this.getBLStackNum(segNo, "2077");
        } else {
            log("获取落料堆垛2计数");
            // 2#堆垛计数 JC52BL01DD0024
            curQuantity = this.getBLStackNum(segNo, "2080");
        }
        log("堆垛数：" + curQuantity);
        return curQuantity;
    }

    /**
     * 获取落料堆垛计数
     *
     * @param segNo 账套
     * @param ihdId ihdid
     * @return 计数器值
     */
    private BigDecimal getBLStackNum(String segNo, String ihdId) throws Exception {
        HDRecord hdRecord = IhdSdkUtils.querySnapshot(segNo, ihdId);
        if (hdRecord != null) {
            BigDecimal curQuantity = new BigDecimal(hdRecord.getValueStr());
            log("堆垛计数：" + curQuantity);
            if (curQuantity.compareTo(BigDecimal.ZERO) > 0) {
                return curQuantity;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取落料堆垛片数
     *
     * @param segNo   账套
     * @param tagTime 时间点 yyyyMMddHHmmss
     * @return 单堆垛片数，默认1
     */
    private int getBLStackPiece(String segNo, String tagTime) throws Exception {
        HDRecord record = IhdSdkUtils.querySingle(segNo, BL_STACK_PIECE_ID, tagTime, true);
        if (record != null) {
            int a = new BigDecimal(record.getValueStr()).intValue();
            if (a > 0) {
                log("堆垛片数：" + a);
                return a;
            }
        }
        return 1;
    }

    /**
     * 生成横切捆包实绩
     *
     * @param inPack    投料捆包
     * @param quantity  数量
     * @param packType  捆包类型2成品4余料
     * @param stackName 堆垛名称
     */
    private void createSphPack(VGDM1002 inPack
            , VIPM0007 outPack
            , BigDecimal quantity
            , String packType
            , String stackName, BigDecimal processHour, String recCreateTime) {
        this.createSphPack(inPack, outPack, quantity, packType, stackName, null, processHour, recCreateTime);
    }

    /**
     * 生成横切捆包实绩
     *
     * @param inPack             投料捆包
     * @param quantity           数量
     * @param packType           捆包类型2成品4余料
     * @param stackName          堆垛名称
     * @param finishingShuntFlag 精整分流标记
     * @param processHour        加工工时
     */
    private void createSphPack(VGDM1002 inPack
            , VIPM0007 outPack
            , BigDecimal quantity
            , String packType
            , String stackName
            , String finishingShuntFlag
            , BigDecimal processHour
            , String recCreateTime) {
        // 获取投料捆包号
        String packId = inPack.getPackId();
        log("投料捆包号：" + packId + "产出捆包类型：" + packType + "产出数量：" + quantity);
        BigDecimal netWeight;
        if (quantity.compareTo(BigDecimal.ONE) < 0) {
            log("数量小于1，不生成");
            return;
        }
        // 成品重量=单片重量*数量
        if ("2".equals(packType)) {
            // 净重kg=单片重量*数量-去除小数
            netWeight = quantity
                    .multiply(outPack.getProcessSinglePackWeight())
                    .setScale(0, RoundingMode.HALF_UP);
        } else {
            // 余料重量=原料重量-已有成品重量
            BigDecimal sumWeight = VGDM1005.querySumOutResult(dao, inPack.getUuid(), false);
            netWeight = inPack.getNetWeight().multiply(NUMBER1000).subtract(sumWeight);
            log("横切余料重量kg：" + netWeight);
            if (netWeight.compareTo(NUMBER500) < 0) {
                log("横切余料重量小于500kg，不生成");
                return;
            }
        }
        log("净重：" + netWeight);
        // 生成产出捆包号
        String outPutPackId = generatePackId(inPack.getSegNo(), "2".equals(packType) ? "20" : "10");
        log("产出捆包号：" + outPutPackId);
        // 新增实绩信息
        Map<String, Object> packMap = new HashMap<>();
        packMap.put("packId", packId);
        // 捆包类型2成品4余料
        packMap.put("packType", packType);
        packMap.put("outPutPackId", outPutPackId);
        packMap.put("unitedPackId", inPack.getUnitedPackId());
        packMap.put("processHour", processHour);
        packMap.put("netWeight", netWeight);
        packMap.put("quantity", quantity.intValue());
        // 成品
        packMap.put("partId", outPack.getPartId());
        packMap.put("stackName", stackName);
        packMap.put("specsDesc", outPack.getSpecsDesc());
        // 余料
        if ("4".equals(packType)) {
            packMap.put("partId", inPack.getPartId());
            packMap.put("stackName", MapUtils.getString(sphBackMap, inPack.getEArchivesNo(), " "));
            packMap.put("specsDesc", inPack.getSpecsDesc());
            packMap.put("unitedPackId", " ");
        }
        packMap.put("areaCode", packMap.get("stackName"));
        // 客户代码
        packMap.put("customerId", outPack.getCustomerId());
        // 保存到数据库
        packMap.put("relevanceId", inPack.getUuid());
        packMap.put("machineCode", inPack.getMachineCode());
        packMap.put("outPackId", outPutPackId);
        packMap.put("liftFlag", "0");
        packMap.put("unitedQuantity", BigDecimal.ZERO);
        packMap.put("finishingShuntFlag", finishingShuntFlag == null ? " " : finishingShuntFlag);
        packMap.put("segNo", inPack.getSegNo());
        packMap.put("unitCode", inPack.getSegNo());
        RecordUtils.setCreatorSys(packMap);
        if (StrUtil.isNotBlank(recCreateTime)) {
            packMap.put("recCreateTime", recCreateTime);
        }
        dao.insert(VGDM1005.INSERT, packMap);
        // 上传imc
        this.callVIPMService(inPack.getSegNo(), inPack.getProcessOrderId(), packMap, false);
    }


    /**
     * 生成落料捆包实绩
     *
     * @param inPack   投料捆包
     * @param quantity 数量
     * @param packType 捆包类型2成品4余料
     */
    private void createBLPack(VGDM1002 inPack
            , VIPM0007 outPack
            , BigDecimal quantity
            , String packType
            , String stackName
            , BigDecimal unitedQuantity
            , VGDM1005 tempPack
            , BigDecimal processHour) {
        // 获取投料捆包号
        String packId = inPack.getPackId();
        log("投料捆包号：" + packId + "产出捆包类型：" + packType + "数量：" + quantity);
        if (quantity.compareTo(BigDecimal.ONE) < 0) {
            log("数量小于1，不生成");
            return;
        }
        BigDecimal netWeight;
        // 并包更新逻辑
        String unitedPackId = inPack.getUnitedPackId();
        if (tempPack != null && tempPack.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
            quantity = tempPack.getQuantity();
            unitedQuantity = tempPack.getUnitedQuantity();
            unitedPackId = tempPack.getUnitedPackId();
        }
        // 成品重量=单片重量*数量
        if ("2".equals(packType)) {
            // 净重kg=单片重量*数量-去除小数
            netWeight = quantity
                    .multiply(outPack.getProcessSinglePackWeight())
                    .setScale(0, RoundingMode.HALF_UP);
        } else {
            // 余料重量=原料重量-已有成品重量
            BigDecimal sumWeight = VGDM1005.querySumOutResult(dao, inPack.getUuid(), false);
            netWeight = inPack.getNetWeight().multiply(NUMBER1000).subtract(sumWeight);
            log("落料余料重量kg：" + netWeight);
            if (netWeight.compareTo(NUMBER500) < 0) {
                log("落料余料重量小于500kg，不生成");
                return;
            }
        }
        log("净重：" + netWeight);
        // 并包号
//        String unitedPackId = inPack.getUnitedPackId();
        log("产出并包号：" + unitedPackId);
        // 生成产出捆包号
        String outPutPackId = generatePackId(inPack.getSegNo(), "2".equals(packType) ? "20" : "10");
        log("产出捆包号：" + outPutPackId);
        // 新增实绩信息
        Map<String, Object> packMap = new HashMap<>();
        packMap.put("packId", packId);
        // 捆包类型2成品4余料
        packMap.put("packType", packType);
        packMap.put("outPutPackId", outPutPackId);
        packMap.put("unitedPackId", unitedPackId);
        packMap.put("processHour", processHour);
        packMap.put("netWeight", netWeight);
        packMap.put("quantity", quantity.intValue());
        // 成品
        packMap.put("partId", outPack.getPartId());
        packMap.put("stackName", stackName);
        packMap.put("specsDesc", outPack.getSpecsDesc());
        // 精整分流-非并包、非一出多，且堆垛为2#马达侧
        packMap.put("finishingShuntFlag", " ");
        if (StrUtil.isNotBlank(inPack.getFinishingShuntFlag())
                && StrUtil.isBlank(unitedPackId)
                && stackName.equals(BL_STACK2B_NAME)) {
            packMap.put("finishingShuntFlag", inPack.getFinishingShuntFlag());
        }
        // 并包量
        packMap.put("unitedQuantity", unitedQuantity);
        // 余料
        if ("4".equals(packType)) {
            packMap.put("partId", inPack.getPartId());
            packMap.put("stackName", stackName);
            packMap.put("specsDesc", inPack.getSpecsDesc());
            packMap.put("unitedPackId", " ");
            packMap.put("finishingShuntFlag", " ");
        }
        packMap.put("areaCode", stackName);
        // 客户代码
        packMap.put("customerId", outPack.getCustomerId());
        // 保存到数据库
        packMap.put("relevanceId", inPack.getUuid());
        packMap.put("machineCode", inPack.getMachineCode());
        packMap.put("outPackId", outPutPackId);
        packMap.put("liftFlag", "0");
        packMap.put("segNo", inPack.getSegNo());
        packMap.put("unitCode", inPack.getSegNo());
        RecordUtils.setCreatorSys(packMap);
        dao.insert(VGDM1005.INSERT, packMap);
        if ("2".equals(packType) && quantity.intValue() < 30) {
            log("成品数量小于30，不上传实绩" + quantity);
            return;
        }
        // 上传imc
        this.callVIPMService(inPack.getSegNo(), inPack.getProcessOrderId(), packMap, false);
    }

    /**
     * 校验并生成待上料捆包信息
     *
     * @param tagInfo 点位信息
     * @return 上料区对应工单信息
     */
    private VGDM1002 createUpPack(VGDM0301 tagInfo) {
        // 根据设备获取上料区捆包及捆包关联的工单信息
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("eArchivesNo", tagInfo.getEArchivesNo());
        queryMap.put("segNo", tagInfo.getSegNo());
        queryMap.put("status", "10");
        List<LIDS0701> machineList = dao.query(LIDS0701.QUERY, queryMap);
        if (CollectionUtils.isEmpty(machineList)) {
            throw new PlatException("未找到设备相关机组附属信息");
        }
        // 机组信息
        LIDS0701 machine = machineList.get(0);
        log("机组：" + machine.getMachineCode()
                + "优先上料区：" + machine.getPriorityLoadMaterialArea()
                + "上料区：" + machine.getMaterialLoadingArea());
        VIPM0008 processOrder = null;
        // 捆包信息
        List<LIDS0901> packList = this.queryToUpPack(machine, tagInfo.getEArchivesNo());
        LIDS0901 inPack = packList.get(0);
        for (LIDS0901 pack : packList) {
            log("待上料捆包：" + pack.getPackId());
            inPack = pack;
            // 工单信息
            Map<String, String> queryMap2 = new HashMap<>();
            queryMap2.put("packId", pack.getPackId());
            queryMap2.put("segNo", tagInfo.getSegNo());
            List<VIPM0008> processOrderList = dao.query(VIPM0008.QUERY, queryMap2);
            if (CollectionUtils.isEmpty(processOrderList)) {
                this.deletePack(pack);
                continue;
            }
            processOrder = processOrderList.get(0);
            break;
        }
        if (processOrder == null) {
            throw new PlatException("未找到上料区捆包对应工单" + inPack.getPackId());
        }
        log("工单：" + processOrder.getProcessOrderId());
        // 新增上料状态捆包信息
        VGDM1002 vgdm1002 = new VGDM1002();
        // 机组信息
        vgdm1002.setMachineCode(machine.getMachineCode());
        vgdm1002.setMachineName(machine.getMachineName());
        vgdm1002.setProcessCategory(machine.getProcessCategory());
        // 捆包信息
        vgdm1002.setPackId(inPack.getPackId());
        // 工单信息
        vgdm1002.setProcessOrderId(processOrder.getProcessOrderId());
        vgdm1002.setProdNameCode(processOrder.getProdTypeId());
        vgdm1002.setProdCname(processOrder.getProdTypeDesc());
        vgdm1002.setShopsign(processOrder.getShopsign());
        vgdm1002.setNetWeight(processOrder.getNetWeight());
        vgdm1002.setSpecsDesc(processOrder.getSpecsDesc());
        vgdm1002.setPartId(processOrder.getPartId());
        vgdm1002.setOriginalPackId(processOrder.getOriginalPackId());
        vgdm1002.setMatInnerId(processOrder.getMatInnerId());
        vgdm1002.setTradeCode(processOrder.getTradeCode());
        // 精整分流标记
        if (StrUtil.isNotBlank(processOrder.getFinishingShuntFlag())) {
            vgdm1002.setFinishingShuntFlag(processOrder.getFinishingShuntFlag());
        }
        // 其他信息
        vgdm1002.setSegNo(tagInfo.getSegNo());
        vgdm1002.setUnitCode(tagInfo.getUnitCode());
        vgdm1002.setEArchivesNo(tagInfo.getEArchivesNo());
        vgdm1002.setEquipmentName(tagInfo.getEquipmentName());
        vgdm1002.setUpPackTime(DateUtil.curDateTimeStr14());
        vgdm1002.setPackStatus("0");
        // 纵切排刀信息
        if (vgdm1002.checkIsSL()) {
            // 纵切调用接口获取排刀方案
            List<Map<String, Object>> planList = vgdm1002.queryKnifeList();
            if (CollectionUtils.isNotEmpty(planList)) {
                Set<String> knifeSet = new HashSet<>();
                for (Map<String, Object> plan : planList) {
                    knifeSet.add(MapUtils.getString(plan, "suitCutSlitId", "1"));
                }
                List<String> knifeList = new ArrayList<>(knifeSet);
                // 默认第一刀
                vgdm1002.setCurrentKnife(knifeList.get(0));
                // 排刀号,分割
                vgdm1002.setKnifeSort(String.join(",", knifeList));
            }
        }
        // 新增上料中作业信息
        Map insMap = vgdm1002.toMap();
        RecordUtils.setCreatorSys(insMap);
        dao.insert(VGDM1002.INSERT, insMap);
        vgdm1002.fromMap(insMap);
        return vgdm1002;
    }

    /**
     * 查询作业中捆包
     *
     * @param segNo       账套
     * @param eArchivesNo 设备代码
     */
    private void checkWorkCount(String segNo, String eArchivesNo) {
        log("查询设备加工中捆包：" + segNo + "|" + eArchivesNo);
        Map<String, String> countMap = new HashMap<>();
        countMap.put("segNo", segNo);
        countMap.put("eArchivesNo", eArchivesNo);
        countMap.put("activeFlag", "10");
        int count = super.count(VGDM1002.COUNT, countMap);
        log("设备加工中捆包数：" + count);
        if (count > 1) {
            throw new PlatException("设备" + eArchivesNo + "加工中捆包数过多" + count);
        }
    }

    /**
     * 删除实物库存信息
     *
     * @param pack 实物库存
     */
    private void deletePack(LIDS0901 pack) {
        log("未找到对应工单，删除实物库存信息：" + pack.getPackId());
        pack.setDelFlag(1);
        Map updMap = pack.toMap();
        RecordUtils.setRevisorSys(updMap);
        dao.update(LIDS0901.UPDATE_STATUS, updMap);
    }

    /**
     * 根据机组附属信息查找捆包信息
     *
     * @param machine     机组信息
     * @param eArchivesNo 设备代码
     * @return 捆包信息
     */
    private List<LIDS0901> queryToUpPack(LIDS0701 machine, String eArchivesNo) {
        // 先取优先上料区的捆包信息
        List<LIDS0901> packList = this.queryUpPack(machine.getPriorityLoadMaterialArea(), machine.getSegNo(), eArchivesNo);
        if (CollectionUtils.isEmpty(packList)) {
            log("未找到优先上料区捆包");
            // 从上料区取
            String[] areaCode = machine.getMaterialLoadingArea().split(",");
            for (String area : areaCode) {
                // 优先上料区已查询，忽略
                if (area.equals(machine.getPriorityLoadMaterialArea())) {
                    continue;
                }
                packList = this.queryUpPack(area, machine.getSegNo(), eArchivesNo);
                if (CollectionUtils.isNotEmpty(packList)) {
                    break;
                }
            }
        }
        if (CollectionUtils.isEmpty(packList)) {
            throw new PlatException(eArchivesNo + "未找到上料区捆包");
        }
        log("待上料捆包数：" + packList.size());
        return packList;
    }

    /**
     * 获取上料区域捆包信息
     *
     * @param areaCode 区域编码
     */
    private List<LIDS0901> queryUpPack(String areaCode, String segNo, String eArchivesNo) {
        log(eArchivesNo + "查询上料区捆包信息：" + areaCode);
        if (StrUtil.isBlank(areaCode)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("areaCode", areaCode);
        map.put("segNo", segNo);
        map.put("status", "10");
        map.put("delFlag", 0);
        // 800飞剪存在多个捆包，需要按Y轴降序取最靠近开卷机的一个（其他机组只会有一个捆包，排序不影响）
        map.put("orderBy", "Y_POSITION DESC");
        if (!"52CL02".equals(eArchivesNo)) {
            map.put("orderBy", "Y_POSITION");
        }
        List<LIDS0901> packList = dao.query(LIDS0901.QUERY, map);
        if (CollectionUtils.isEmpty(packList)) {
            log(eArchivesNo + "未找到上料区捆包");
            return null;
        }
        return packList;
    }

    /**
     * 查询点位信息
     *
     * @param tagId 点位id
     * @return 点位信息
     */
    private VGDM0301 queryTag(String tagId) {
        VGDM0301 tagInfo = VGDM0301.queryWithCache(dao, tagId);
        if (tagInfo == null) {
            logError(tagId, "未找到对应点位配置");
            throw new PlatException("点位信息不存在！" + tagId);
        }
        return tagInfo;
    }

    /**
     * 查询作业捆包信息
     *
     * @param tagInfo    点位信息
     * @param packStatus 捆包状态0上料1加工2完成
     * @param latestFlag 是否查找最近的
     * @return 捆包信息
     */
    private VGDM1002 queryWorkPack(VGDM0301 tagInfo, String packStatus, boolean latestFlag) {
        log("查询状态为" + packStatus + "的捆包,是否取最近：" + latestFlag);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("eArchivesNo", tagInfo.getEArchivesNo());
        queryMap.put("segNo", tagInfo.getSegNo());
        queryMap.put("packStatus", packStatus);
        if (!latestFlag && "1".equals(packStatus)) {
            log("查询状态为1的捆包，按开卷时间升序");
            queryMap.put("orderBy", "START_TIME asc");
        } else {
            log("查询捆包，按开卷时间降序");
            queryMap.put("orderBy", "START_TIME DESC");
        }
        List<VGDM1002> packList = dao.query(VGDM1002.QUERY, queryMap);
        if (CollectionUtils.isEmpty(packList)) {
            log("未找到状态为" + packStatus + "的捆包");
            return null;
        }
        log("捆包号：" + packList.get(0).getPackId());
        return packList.get(0);
    }

    /**
     * 检查待加工捆包信息
     *
     * @param tagInfo tagInfo
     * @return 上料区对应工单信息
     */
    private VGDM1002 checkTodoPack(VGDM0301 tagInfo) {
        // 获取当前上料中捆包
        VGDM1002 upPack = this.queryWorkPack(tagInfo, "0", false);
        if (upPack == null) {
            throw new PlatException(tagInfo.getEArchivesNo() + "未找到上料中捆包");
        }
        return upPack;
    }

    /**
     * 纵切捆包生产实绩时间上传
     * serviceId:S_VG_DM_1007
     * <p>
     * 入参：
     * <li>tagId 点位</li>
     * <li>tagValue 值</li>
     */
    public EiInfo addPackResultTime(EiInfo inInfo) {
        try {
            String tagId = inInfo.getString("tagId");
            String startTime = inInfo.getString("startTime");
            String endTime = inInfo.getString("endTime");
            log("addPackResultTime参数tagId:" + tagId + " startTime:" + startTime + "endTime:" + endTime);
            if (StrUtil.isBlank(tagId) || StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) {
                throw new PlatException("参数不能为空");
            }
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 获取当前加工中捆包信息
            VGDM1002 resultPack = this.getPackForResult(tagInfo, false);
            // 待新增数据
            VGDM1004 resultTime = new VGDM1004();
            resultTime.setSegNo(tagInfo.getSegNo());
            resultTime.setUnitCode(tagInfo.getUnitCode());
            // 毫秒时间戳转换未LocalDateTime类型
            LocalDateTime startTimeLdt = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(startTime)), ZoneId.systemDefault())
                    .truncatedTo(ChronoUnit.SECONDS);
            LocalDateTime endTimeLdt = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(endTime)), ZoneId.systemDefault())
                    .truncatedTo(ChronoUnit.SECONDS);
            resultTime.setStartTime(startTimeLdt.format(DateUtils.FORMATTER_14));
            resultTime.setEndTime(endTimeLdt.format(DateUtils.FORMATTER_14));
            long durationSecond = ChronoUnit.SECONDS.between(startTimeLdt, endTimeLdt);
            resultTime.setDurationSecond((int) durationSecond);
            // 判断实绩时间与开卷时间是否超过8分钟
            if (resultPack != null) {
                // 捆包开卷时间
                LocalDateTime workStartTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
                long minutes = Duration.between(workStartTime, startTimeLdt).toMinutes();
                log("实绩时间与开卷时间差值m：" + minutes);
                if (minutes < 8) {
                    resultPack = null;
                }
            }
            resultTime.setRelevanceId(resultPack == null ? " " : resultPack.getUuid());
            // 新增实绩时间
            Map insMap = resultTime.toMap();
            RecordUtils.setCreatorSys(insMap);
            dao.insert(VGDM1004.INSERT, insMap);
            // 持续实绩超过10s且存在加工捆包信息时计算生产实绩
            if (durationSecond > 10 && resultPack != null) {
                // 计算纵切生产实绩
                resultPack.setEndTime(resultTime.getEndTime());
                this.addSLResult(resultPack, resultTime);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 计算纵切捆包生产实绩
     *
     * @param resultPack 加工捆包信息
     * @param resultTime 实绩时间访问
     */
    private synchronized void addSLResult(VGDM1002 resultPack, VGDM1004 resultTime) throws Exception {
        // 获取工单产出信息
        List<VIPM0007> outPackList = new ArrayList<>();
        boolean isKnife = resultPack.queryOutPack(dao, outPackList);
        // 投料捆包数默认1
        int inPackCount = 1;
        if (!isKnife) {
            // 没有排刀方案时按投料捆包数平均产出捆包量
            inPackCount = resultPack.queryInCount(dao);
        }
        // 获取理论总重量/宽度/数量
        Map<String, BigDecimal> totalMap = new HashMap<>();
        this.calTheoryOutResult(outPackList, totalMap, inPackCount);
        // 判断是否使用理论重量
        BigDecimal theoryFlagNum = totalMap.get("theoryFlag");
        boolean theoryFlag = theoryFlagNum.compareTo(BigDecimal.ZERO) > 0;
        log("是否使用理论重量：" + theoryFlag);
        // 获取称重开始时间
        String startTime = this.getWeightStartTime(resultTime);
        if (startTime == null) {
            log("无下料小车出信号");
            return;
        }
        // 获取称重记录
        List<HDRecord> weightRecords = this.queryIhdRecord(resultTime.getSegNo()
                , startTime
                , resultTime.getEndTime(), SL_WEIGHT_ID);
        // 获取实绩重量
        BigDecimal weight = this.getSLMaxDurationWeight(weightRecords, true);
        if (weight.compareTo(BigDecimal.ZERO) < 0) {
            log("时间范围内未找到实绩重量");
            return;
        }
        // 加工开始时间-上料时间
        LocalDateTime startTimeLdt = LocalDateTime.parse(resultPack.getUpPackTime(), DateUtils.FORMATTER_14);
        // 加工结束时间-实绩结束时间
        LocalDateTime endTimeLdt = LocalDateTime.parse(resultPack.getEndTime(), DateUtils.FORMATTER_14);
        // 存在换刀时计算工序时间
        if (resultPack.getKnifeSort().contains(",")) {
            log("计算准备时间");
            this.calPackTime(resultPack, true);
        }
        // 计算工时
        long workMinutes = this.calSLWorkHour(resultPack, startTimeLdt, endTimeLdt);
        log("加工工时(分)：" + workMinutes);
        if (workMinutes < 0) {
            log("负数加工工时，返回");
            return;
        }
        BigDecimal totalQty = totalMap.get("totalQty");
        workMinutes = workMinutes / totalQty.intValue();
        log("单个捆包加工工时m:" + workMinutes);
        // 插棒判断
        BigDecimal sumWidth = totalMap.get("sumWidth");
        int extraWeightCount = this.checkExtraWeight(outPackList, resultPack.getSpecsDesc(), sumWidth);
        log("插棒数：" + extraWeightCount);
        // 原料规格
        String[] arr = resultPack.getSpecsDesc().split("\\*");
        // 厚度
        BigDecimal thickness = new BigDecimal(arr[0]);
        // 宽度
        BigDecimal ylWidth = new BigDecimal(arr[1]);
        log("原料厚度：" + thickness + "宽度" + ylWidth);
        BigDecimal bsWidth = ylWidth.subtract(sumWidth).subtract(NUMBER3);
        log("原料宽度-成品宽度-3：" + bsWidth);
        BigDecimal totalWeight = totalMap.get("totalWeight");
        if (bsWidth.compareTo(NUMBER60) > 0) {
            log("生成窄带");
            BigDecimal zdWeight = BigDecimal.ZERO;
            if (theoryFlag) {
                zdWeight = this.calSLBs(totalWeight, 0, sumWidth, thickness, bsWidth);
            }
            // 添加窄带产出信息
            this.addSLSmallOutPack(outPackList, bsWidth, thickness, zdWeight);
            // 更新总宽度
            sumWidth = sumWidth.add(bsWidth);
            totalMap.put("sumWidth", sumWidth);
        }
        // 生成捆包信息
        List<Map<String, Object>> packList = this.createSLOutPackList(outPackList,
                totalMap,
                weight,
                resultPack,
                workMinutes,
                extraWeightCount,
                theoryFlag);
        // 阶梯重量重算
        if (!theoryFlag) {
            this.calSLStepWeight(packList, weightRecords, weight, extraWeightCount);
        }
        // 边丝理论重量
        BigDecimal bsWeight = this.calSLBs(weight, extraWeightCount, sumWidth
                , thickness, ylWidth.subtract(sumWidth));
        // 剩余重量
        BigDecimal leftWeight = this.getSLLeftWeight(resultPack, packList);
        // 余料-剩余重量大于1t才会有余料
        if (resultPack.checkIsLeftPack()
                && leftWeight.compareTo(NUMBER1000) > 0) {
            log("纵切余料");
            // 余料生成纵切边丝
            this.createSLSurplusBs(bsWeight, resultPack, workMinutes, packList);
            this.createSLSurplusPack(resultPack, workMinutes, packList);
        } else {
            log("纵切边丝");
            this.createSLBsPack(resultPack, workMinutes, packList, leftWeight);
        }
        // 新增到数据库
        DaoUtils.insertBatch(dao, VGDM1005.INSERT, packList);
        // 上传实绩
        this.callVIPMService(resultPack.getSegNo(), resultPack.getProcessOrderId(), packList, false);
        // 换刀-更新刀信息
        if (resultPack.checkIsChangeKnife() && resultPack.changeKnife(true)) {
            // 清除换刀标记
            resultPack.setEndType(" ");
            resultPack.setEndTime(" ");
            // 更新捆包作业信息
            Map updateMap = resultPack.toMap();
            RecordUtils.setRevisorSys(updateMap);
            dao.update(VGDM1002.UPDATE, updateMap);
        }
        // 已有实绩重量
        BigDecimal sumWeight = VGDM1005.querySLSumOutResult(dao, resultPack.getUuid());
        // 剩余重量
        BigDecimal leftResultWeight = resultPack.getNetWeight().multiply(NUMBER1000)
                .subtract(sumWeight).subtract(bsWeight);
        log("原料减实绩减边丝剩余重量：" + leftResultWeight);
        // 原料重量-已有实绩重量>300kg时认为未加工完，不结束捆包
        if (!resultPack.checkIsLeftPack() && leftResultWeight.compareTo(NUMBER300) > 0) {
            log("不结束捆包");
            return;
        }
        // 完成捆包
        this.endPackWork(resultPack, EndType.NORMAL);
        // 计算工序时间
        this.calPackTime(resultPack);
    }

    /**
     * 添加纵切窄带产出信息
     *
     * @param outPackList 原产出信息
     * @param bsWidth     窄带宽度
     * @param thickness   厚度
     * @param zdWeight    重量（理论重量时使用）
     */
    private void addSLSmallOutPack(List<VIPM0007> outPackList
            , BigDecimal bsWidth, BigDecimal thickness, BigDecimal zdWeight) {
        log("开始生成窄带模拟产出信息" + bsWidth);
        VIPM0007 zdPack = new VIPM0007();
        zdPack.fromMap(outPackList.get(0).toMap());
        zdPack.setPartId("FZ230416002");
        zdPack.setSpecsDesc(thickness.toPlainString() + "*" + bsWidth.toPlainString() + "*C");
        zdPack.setProductProcessQty(1L);
        // 重量转换为吨
        zdWeight = zdWeight.divide(NUMBER1000, 3, RoundingMode.HALF_UP);
        zdPack.setProductProcessWeight(zdWeight);
        outPackList.add(zdPack);
    }

    /**
     * 计算边丝重量
     *
     * @param weight           称重
     * @param extraWeightCount 插棒数
     * @param sumWidth         成品总宽度
     * @param thickness        厚度
     * @param bsWidth          边丝宽度
     * @return 边丝理论重量
     */
    private BigDecimal calSLBs(BigDecimal weight, int extraWeightCount
            , BigDecimal sumWidth, BigDecimal thickness, BigDecimal bsWidth) {
        log("计算边丝理论重量");
        // 减去插棒重量
        if (extraWeightCount > 0) {
            weight = weight.subtract(NUMBER10.multiply(new BigDecimal(extraWeightCount)));
        }
        // 长度 m= (重量（kg）*1000)/（密度7.85（t/m³）*宽度（mm）*厚度（mm））
        BigDecimal up = weight.multiply(NUMBER1000);
        BigDecimal down = thickness.multiply(sumWidth).multiply(DENSITY);
        BigDecimal length = up.divide(down, 0, RoundingMode.HALF_UP);
        log("边丝长度：" + length);
        // 边丝重量kg = （边丝长度（m）*厚度（mm）*宽度（mm）*密度7.85（t/m³））/1000
        BigDecimal bsWeight = length.multiply(thickness).multiply(bsWidth).multiply(DENSITY).divide(NUMBER1000, 0, RoundingMode.HALF_UP);
        log("边丝重量：" + bsWeight);
        return bsWeight;
    }

    /**
     * 纵切余料时边丝实绩
     *
     * @param resultPack  投料信息
     * @param workMinutes 加工工时
     * @param packList    已有实绩
     */
    private void createSLSurplusBs(BigDecimal bsWeight, VGDM1002 resultPack, long workMinutes
            , List<Map<String, Object>> packList) {
        log("纵切余料边丝");
        // 生成实绩
        String packId = resultPack.getPackId();
        // 产出捆包号
        String outPutPackId = generatePackId(resultPack.getSegNo(), "20");
        log("边丝捆包号2：" + outPutPackId);
        // 并包号
        String unitedPackId = " ";
        // 接口数据
        Map<String, Object> packMap = new HashMap<>();
        packMap.put("packId", packId);
        packMap.put("partId", "FZ230416013");
        // 捆包类型2成品4余料6原料废次材
        packMap.put("packType", "6");
        packMap.put("outPutPackId", outPutPackId);
        packMap.put("unitedPackId", unitedPackId);
        packMap.put("processHour", new BigDecimal(workMinutes));
        packMap.put("netWeight", bsWeight);
        packMap.put("quantity", 1);
        // 客户代码
        packMap.put("customerId", " ");
        // 保存到数据库
        packMap.put("stackName", " ");
        packMap.put("areaCode", " ");
        packMap.put("machineCode", resultPack.getMachineCode());
        packMap.put("specsDesc", "0*0*0");
        packMap.put("outPackId", outPutPackId);
        packMap.put("liftFlag", "0");
        packMap.put("unitedQuantity", BigDecimal.ZERO);
        packMap.put("finishingShuntFlag", " ");
        packMap.put("relevanceId", resultPack.getUuid());
        packMap.put("segNo", resultPack.getSegNo());
        packMap.put("unitCode", resultPack.getSegNo());
        RecordUtils.setCreatorSys(packMap);
        // 添加边丝实绩
        packList.add(packMap);
    }

    /**
     * 判断纵切是否有插棒
     *
     * @param specDesc 投料规格
     * @param sumWidth 产出总宽度
     * @return 插棒个数
     */
    private int checkExtraWeight(List<VIPM0007> outPackList, String specDesc, BigDecimal sumWidth) {
        if (outPackList.size() == 1 && outPackList.get(0).getProductProcessQty() == 1) {
            log("只有一个产出");
            return 0;
        }
        // 是否有小于350的卷
        boolean smallFlag = false;
        // 大于350的卷个数
        int bigCount = 0;
        // 总个数
        int totalCount = 0;
        for (VIPM0007 outPack : outPackList) {
            String[] arr = outPack.getSpecsDesc().split("\\*");
            BigDecimal width = new BigDecimal(arr[1]);
            if (width.compareTo(NUMBER350) > 0) {
                bigCount = (int) (bigCount + outPack.getProductProcessQty());
            } else {
                smallFlag = true;
            }
            totalCount = (int) (totalCount + outPack.getProductProcessQty());
        }
        log("总产出数：" + totalCount + "是否存在小于350的卷：" + smallFlag + "大于350的卷数" + bigCount);
        // 存在小于350的卷
        if (smallFlag) {
            // 只有一个大于350的卷，2根插棒
            if (bigCount == 1) {
                return 2;
            }
            // 都小于350
            if (bigCount == 0) {
                // 总产数>=10时，4根插棒，否则2根插棒
                if (totalCount >= 10) {
                    return 4;
                } else {
                    return 2;
                }
            }
            // 超过2个卷大于350，1根插棒
            if (bigCount >= 2) {
                return 1;
            }
        } else {
            // 根据规格获取原料宽度
            String[] arr = specDesc.split("\\*");
            BigDecimal width = new BigDecimal(arr[1]);
            log("原料宽度：" + width + "成品宽度：" + sumWidth);
            // 原料宽度-成品宽度<70mm时认为不加插棒
            if (width.subtract(sumWidth).compareTo(NUMBER70) <= 0) {
                log("不加插棒");
                return 0;
            }
            return 2;
        }
        return 0;
    }

    /**
     * 获取称重开始时间
     * <p>
     * 一下料小车第一次出的时间作为开始时间
     *
     * @param time 实绩时间
     * @return 开始时间  yyyy-MM-dd HH:mm:ss:SSS
     * @throws Exception 异常
     */
    private String getWeightStartTime(VGDM1004 time) throws Exception {
        List<HDRecord> records = this.queryIhdRecord(time.getSegNo(),
                time.getStartTime()
                , time.getEndTime()
                , SL_OUT_PACK_ID);
        log("获取下料车出时间");
        return this.getOneValueTime(records, true);
    }

    /**
     * 获取值为1的时间
     *
     * @param records 点位记录
     * @param isFirst 是否最早
     * @return 匹配时间
     */
    private String getOneValueTime(List<HDRecord> records, boolean isFirst) {
        if (isFirst) {
            // 按时间正序（取最早的时间）
            for (int i = records.size() - 1; i >= 0; i--) {
                HDRecord record = records.get(i);
                if ("1".equals(record.getValueStr())) {
                    String outTime = record.getTimeStampStr().substring(0, 19).replaceAll("[\\-: ]", "");
                    log("匹配时间：" + outTime);
                    return outTime;
                }
            }
        } else {
            // 按时间逆序（取最近的时间）
            for (HDRecord record : records) {
                if ("1".equals(record.getValueStr())) {
                    String outTime = record.getTimeStampStr().substring(0, 19).replaceAll("[\\-: ]", "");
                    log("匹配时间：" + outTime);
                    return outTime;
                }
            }
        }
        return null;
    }

    /**
     * 计算纵切加工工时
     *
     * @param resultPack   加工捆包信息
     * @param startTimeLdt 开始时间
     * @param endTimeLdt   实绩时间
     * @return 当前实绩工时，负数表示忽略此实绩
     */
    private long calSLWorkHour(VGDM1002 resultPack,
                               LocalDateTime startTimeLdt,
                               LocalDateTime endTimeLdt) throws Exception {
        // 是否多分刀
        String[] knifeArr = resultPack.getKnifeSort().split(",");
        // 平均准备时间（分刀用）
        int averageMinutes = 0;
        // 时间重算标记（用于分刀时间取不到时）
        boolean timeFlag = true;
        if (knifeArr.length > 1) {
            // 查询上料和开卷的工序时间
            VGDM1003 kjTime = new VGDM1003();
            int prepareSeconds = this.queryPrepareSeconds(resultPack.getSegNo(), resultPack.getUuid(), kjTime);
            log("总准备时间s：" + prepareSeconds);
            if (prepareSeconds < 1) {
                log("未查询到SL或KJ时间");
            } else {
                int averageSeconds = (prepareSeconds / knifeArr.length) + 1;
                averageMinutes = (averageSeconds / 60) + 1;
                log("平均时间准备时间m:" + averageMinutes);
                // 第一分切
                if ("1".equals(resultPack.getCurrentKnife())) {
                    log("第一分切");
                    // 第一分切开始时间取开卷结束时间
                    startTimeLdt = LocalDateTime.parse(kjTime.getStopTime(), DateUtils.FORMATTER_23);
                    timeFlag = false;
                } else {
                    log("非第一分切");
                    // 查找油压剪下切时间
                    List<HDRecord> records = this.queryIhdRecord(resultPack.getSegNo(),
                            startTimeLdt.format(DateUtils.FORMATTER_14),
                            endTimeLdt.format(DateUtils.FORMATTER_14),
                            SL_FRONT_KNIFE_ID);
                    for (HDRecord record : records) {
                        // 存在油压剪下切时间
                        if ("1".equals(record.getValueStr())) {
                            String outTime = record.getTimeStampStr()
                                    .replaceAll("[\\-: ]", "").substring(0, 14);
                            log("换刀时间：" + outTime);
                            startTimeLdt = LocalDateTime.parse(outTime, DateUtils.FORMATTER_14);
                            timeFlag = false;
                            break;
                        }
                    }
                }
            }
        }
        // 取平均准备时间+开始时间和结束时间的分差
        long workMinutes = averageMinutes + ChronoUnit.MINUTES.between(startTimeLdt, endTimeLdt) + 1;
        // 多次实绩没有分刀
        if (timeFlag) {
            // 取上一实绩产生时间
            LocalDateTime lastOutTime = this.queryLastOutTime(resultPack.getSegNo(), resultPack.getUuid());
            if (lastOutTime == null) {
                return workMinutes;
            }
            long outMinutes = ChronoUnit.MINUTES.between(lastOutTime, endTimeLdt);
            log("距离上一实绩产生时间：" + outMinutes);
            if (outMinutes < 5) {
                log("距上一实绩产生时间少于5分钟，忽略此数据");
                return -1;
            }
            return outMinutes;
        }
        return workMinutes;
    }

    /**
     * 查询准备工序时间（上料、开卷）
     *
     * @param segNo           账套
     * @param uuid            uuid
     * @param kjProcedureTime 开卷时间
     * @return 准备时间s
     */
    private int queryPrepareSeconds(String segNo, String uuid, VGDM1003 kjProcedureTime) {
        // 查询上料和开卷的工序时间
        log("查询上料和开卷时间");
        Map<String, String> map1 = new HashMap<>();
        map1.put("relevanceId", uuid);
        map1.put("segNo", segNo);
        map1.put("slQuery", "1");
        map1.put("orderBy", "START_TIME");
        List<VGDM1003> timeList = dao.query(VGDM1003.QUERY, map1);
        VGDM1003 slTime = null;
        VGDM1003 kjTime = null;
        int prepareSeconds = 0;
        int prepareCount = 0;
        for (VGDM1003 time : timeList) {
            if (slTime == null && "SL".equals(time.getProcedureCode())) {
                slTime = time;
                prepareSeconds = prepareSeconds + time.getDuration();
                prepareCount++;
                continue;
            }
            if (kjTime == null && "KJ".equals(time.getProcedureCode())) {
                kjTime = time;
                prepareSeconds = prepareSeconds + time.getDuration();
                prepareCount++;
            }
            if (prepareCount > 1) {
                break;
            }
        }
        if (prepareCount < 2) {
            log("未查询到SL或KJ时间");
            return 0;
        }
        kjProcedureTime.setStopTime(kjTime.getStopTime());
        return prepareSeconds;
    }

    /**
     * 查询最近一次产出实绩时间
     *
     * @param segNo 账套
     * @param uuid  uuid
     * @return 最近一次产出实绩时间，无时返回null
     */
    private LocalDateTime queryLastOutTime(String segNo, String uuid) {
        Map<String, String> map = new HashMap<>();
        map.put("relevanceId", uuid);
        map.put("segNo", segNo);
        map.put("orderBy", "REC_CREATE_TIME desc");
        List<VGDM1005> list = dao.query(VGDM1005.QUERY, map);
        if (CollectionUtils.isNotEmpty(list)) {
            return LocalDateTime.parse(list.get(0).getRecCreateTime(), DateUtils.FORMATTER_14);
        }
        return null;
    }

    /**
     * 获取纵切剩余实绩重量
     *
     * @param resultPack 投料捆包
     * @param packList   实绩信息
     * @return 剩余重量
     */
    private BigDecimal getSLLeftWeight(VGDM1002 resultPack, List<Map<String, Object>> packList) {
        // 获取已产生实绩重量
        BigDecimal sumWeight = VGDM1005.querySLSumOutResult(dao, resultPack.getUuid());
        for (Map<String, Object> packMap : packList) {
            sumWeight = sumWeight.add(MapUtils.getBigDecimal(packMap, "netWeight", BigDecimal.ZERO));
        }
        log("数据库实绩重量：" + sumWeight);
        // 余料重量kg=原料重量t*1000-已产生实绩重量kg
        BigDecimal netWeight = resultPack.getNetWeight()
                .multiply(NUMBER1000)
                .subtract(sumWeight);
        log("剩余重量kg：" + netWeight);
        return netWeight;
    }

    /**
     * 生成纵切余料实绩
     *
     * @param resultPack  捆包
     * @param workMinutes 加工工时
     */
    private void createSLSurplusPack(VGDM1002 resultPack, long workMinutes
            , List<Map<String, Object>> packList) {
        log("开始生成余料实绩");
        // 获取已产生实绩重量
        BigDecimal sumWeight = VGDM1005.querySLSumOutResult(dao, resultPack.getUuid());
        for (Map<String, Object> packMap : packList) {
            sumWeight = sumWeight.add(MapUtils.getBigDecimal(packMap, "netWeight", BigDecimal.ZERO));
        }
        log("实绩重量：" + sumWeight);
        // 余料重量kg=原料重量t*1000-已产生实绩重量kg
        BigDecimal netWeight = resultPack.getNetWeight()
                .multiply(NUMBER1000)
                .subtract(sumWeight);
        log("余料重量kg：" + netWeight);
        // 获取投料捆包号
        String packId = resultPack.getPackId();
        // 投料物料号
        String partId = resultPack.getPartId();
        log("投料捆包号：" + packId + "，投料物料号：" + partId);
        // 产出捆包号
        String outPutPackId = generatePackId(resultPack.getSegNo(), "10");
        log("余料捆包号：" + outPutPackId);
        // 并包号
        String unitedPackId = " ";
        // 接口数据
        Map<String, Object> packMap = new HashMap<>();
        packMap.put("packId", packId);
        packMap.put("partId", partId);
        // 捆包类型2成品4余料
        packMap.put("packType", "4");
        packMap.put("outPutPackId", outPutPackId);
        packMap.put("unitedPackId", unitedPackId);
        packMap.put("processHour", new BigDecimal(workMinutes));
        packMap.put("netWeight", netWeight);
        packMap.put("quantity", 1);
        // 客户代码
        packMap.put("customerId", " ");
        // 保存到数据库
        packMap.put("stackName", "JCQY250214001");
        packMap.put("areaCode", packMap.get("stackName"));
        packMap.put("machineCode", resultPack.getMachineCode());
        packMap.put("specsDesc", resultPack.getSpecsDesc());
        packMap.put("outPackId", outPutPackId);
        packMap.put("liftFlag", "0");
        packMap.put("unitedQuantity", BigDecimal.ZERO);
        packMap.put("finishingShuntFlag", " ");
        packMap.put("relevanceId", resultPack.getUuid());
        packMap.put("segNo", resultPack.getSegNo());
        packMap.put("unitCode", resultPack.getSegNo());
        RecordUtils.setCreatorSys(packMap);
        // 添加余料实绩
        packList.add(packMap);
    }

    /**
     * 生成纵切边丝
     *
     * @param resultPack  捆包
     * @param workMinutes 加工工时
     * @param leftWeight  剩余重量
     */
    private void createSLBsPack(VGDM1002 resultPack, long workMinutes
            , List<Map<String, Object>> packList, BigDecimal leftWeight) {
        log("开始生成边丝实绩" + leftWeight);
        if (leftWeight.compareTo(BigDecimal.ZERO) < 0) {
            log("负数重量");
            return;
        }
        // 原料重量-已有实绩重量>300kg时认为未加工完，不结束捆包
        if (leftWeight.compareTo(NUMBER300) > 0) {
            log("边丝重量过大");
            return;
        }
        // 获取投料捆包号
        String packId = resultPack.getPackId();
        log("投料捆包号：" + packId);
        // 产出捆包号
        String outPutPackId = generatePackId(resultPack.getSegNo(), "20");
        log("边丝捆包号：" + outPutPackId);
        // 并包号
        String unitedPackId = " ";
        // 接口数据
        Map<String, Object> packMap = new HashMap<>();
        packMap.put("packId", packId);
        packMap.put("partId", "FZ230416013");
        // 捆包类型2成品4余料6原料废次材
        packMap.put("packType", "6");
        packMap.put("outPutPackId", outPutPackId);
        packMap.put("unitedPackId", unitedPackId);
        packMap.put("processHour", new BigDecimal(workMinutes));
        packMap.put("netWeight", leftWeight);
        packMap.put("quantity", 1);
        // 客户代码
        packMap.put("customerId", " ");
        // 保存到数据库
        packMap.put("stackName", " ");
        packMap.put("areaCode", " ");
        packMap.put("machineCode", resultPack.getMachineCode());
        packMap.put("specsDesc", "0*0*0");
        packMap.put("outPackId", outPutPackId);
        packMap.put("liftFlag", "0");
        packMap.put("unitedQuantity", BigDecimal.ZERO);
        packMap.put("finishingShuntFlag", " ");
        packMap.put("relevanceId", resultPack.getUuid());
        packMap.put("segNo", resultPack.getSegNo());
        packMap.put("unitCode", resultPack.getSegNo());
        RecordUtils.setCreatorSys(packMap);
        // 添加边丝实绩
        packList.add(packMap);
    }

    /**
     * 纵切生产实绩
     *
     * @param outPackList  理论产出捆包
     * @param totalMap     理论总重量/总数量kg/总宽度
     * @param deviceWeight 设备采集总重量kg
     * @param resultPack   投料捆包信息
     * @param workMinutes  加工时间
     */
    private List<Map<String, Object>> createSLOutPackList(List<VIPM0007> outPackList, Map<String, BigDecimal> totalMap,
                                                          BigDecimal deviceWeight, VGDM1002 resultPack,
                                                          long workMinutes, int extraWeightCount, boolean theoryFlag) {
        // 减去插棒重量
        if (extraWeightCount > 0) {
            deviceWeight = deviceWeight.subtract(NUMBER10.multiply(new BigDecimal(extraWeightCount)));
        }
        log("生成纵切生产实绩，总重量kg：" + deviceWeight);
        List<Map<String, Object>> packList = new ArrayList<>();
        // 总宽度
        BigDecimal sumWidth = totalMap.get("sumWidth");
        // 总重量-kg-舍去小数
        BigDecimal sumWeight = deviceWeight.setScale(0, RoundingMode.FLOOR);
        log("生成纵切生产实绩，总重量kg：" + sumWeight);
        // 剩余总重量
        BigDecimal leftWeight = sumWeight;
        for (int i = 0; i < outPackList.size(); i++) {
            VIPM0007 outPack = outPackList.get(i);
            int qty = outPack.getProductProcessQty().intValue();
            log("产出捆包规格：" + outPack.getSpecsDesc() + "，数量：" + qty);
            if (theoryFlag) {
                BigDecimal theoryWeight = outPack.getProductProcessWeight().multiply(NUMBER1000)
                        .setScale(0, RoundingMode.HALF_UP);
                log("按理论重量kg生成:" + theoryWeight);
                // 按规格创建捆包信息
                packList.addAll(this.createSLPack(qty,
                        theoryWeight,
                        resultPack,
                        outPack,
                        workMinutes));
                continue;
            }
            log("按设备采集重量生成");
            String[] arr = outPack.getSpecsDesc().split("\\*");
            BigDecimal width = new BigDecimal(arr[1]);
            BigDecimal weight;
            // 最后一个捆包占所有重量
            if (i == outPackList.size() - 1) {
                weight = leftWeight;
            } else {
                // 分子 =单个宽度*捆包数量*总重量
                BigDecimal up = width.multiply(BigDecimal.valueOf(qty)).multiply(sumWeight);
                // 捆包重量=分子/总宽度 -舍去小数
                weight = up.divide(sumWidth, 0, RoundingMode.FLOOR);
                // 剩余总重量
                leftWeight = leftWeight.subtract(weight);
            }
            // 按规格创建捆包信息
            packList.addAll(this.createSLPack(qty,
                    weight,
                    resultPack,
                    outPack,
                    workMinutes));
        }
        return packList;
    }

    /**
     * 创建纵切产出捆包信息（同规格）
     *
     * @param qty         捆包数
     * @param weight      捆包总重量kg
     * @param resultPack  投料捆包
     * @param outPack     产出信息
     * @param workMinutes 加工时间
     */
    private List<Map<String, Object>> createSLPack(int qty,
                                                   BigDecimal weight,
                                                   VGDM1002 resultPack,
                                                   VIPM0007 outPack,
                                                   long workMinutes) {
        log("同规格捆包总重量kg：" + weight + "数量" + qty);
        BigDecimal left = weight.subtract(BigDecimal.ZERO);
        String packType = "2";
        String specsDesc = outPack.getSpecsDesc();
        if ("FZ230416002".equals(outPack.getPartId())) {
            log("生成纵切窄带实绩");
            packType = "6";
            String[] arr = specsDesc.split("\\*");
            specsDesc = arr[0] + "*0*C";
        }
        // 接口数据
        List<Map<String, Object>> packList = new ArrayList<>();
        for (int i = 0; i < qty; i++) {
            BigDecimal netWeight;
            if (i == qty - 1) {
                netWeight = left;
            } else {
                // 重量kg-舍去小数
                netWeight = weight.divide(new BigDecimal(qty), 0, RoundingMode.FLOOR);
                left = left.subtract(netWeight);
            }
            log("产出捆包重量kg：" + netWeight);
            Map<String, Object> packMap = new HashMap<>();
            // 投料捆包信息
            packMap.put("packId", resultPack.getPackId());
            // 捆包类型2成品4余料6废次材
            packMap.put("packType", packType);
            // 产出物料号
            packMap.put("partId", outPack.getPartId());
            // 产出捆包号
            String outPutPackId = generatePackId(resultPack.getSegNo(), "20");
            packMap.put("outPutPackId", outPutPackId);
            // 纵切无并包号
            packMap.put("unitedPackId", " ");
            // 生产时间
            packMap.put("processHour", new BigDecimal(workMinutes));
            packMap.put("netWeight", netWeight);
            // 数量默认1
            packMap.put("quantity", 1);
            // 客户代码
            packMap.put("customerId", outPack.getCustomerId());
            // 保存到数据库需要的信息
            packMap.put("stackName", "JCQY250224002");
            packMap.put("areaCode", packMap.get("stackName"));
            packMap.put("specsDesc", specsDesc);
            packMap.put("machineCode", resultPack.getMachineCode());
            packMap.put("outPackId", outPutPackId);
            packMap.put("liftFlag", "0");
            packMap.put("unitedQuantity", BigDecimal.ZERO);
            packMap.put("finishingShuntFlag", " ");
            packMap.put("relevanceId", resultPack.getUuid());
            packMap.put("segNo", resultPack.getSegNo());
            packMap.put("unitCode", resultPack.getSegNo());
            RecordUtils.setCreatorSys(packMap);
            // 返回结果
            packList.add(packMap);
        }
        return packList;
    }

    /**
     * 计算纵切产出捆包理论重量kg和总宽度
     *
     * @param outPackList 产出捆包信息
     * @param inPackCount 投料捆包数
     * @param totalMap    理论产出总数据totalWeight总重量kg,sumWidth总宽度，totalQty总数量
     */
    private void calTheoryOutResult(List<VIPM0007> outPackList, Map<String, BigDecimal> totalMap,
                                    int inPackCount) {
        // 理论称重标记
        totalMap.put("theoryFlag", BigDecimal.ZERO);
        // 产出总重量
        BigDecimal totalWeight = BigDecimal.ZERO;
        // 总宽度
        BigDecimal sumWidth = BigDecimal.ZERO;
        // 总捆包数
        long totalQty = 0;
        for (VIPM0007 outPack : outPackList) {
            if (StrUtil.isNotBlank(outPack.getDirProcessDemandId())) {
                log("半成品，使用理论重量" + outPack.getDirProcessDemandId());
                totalMap.put("theoryFlag", BigDecimal.ONE);
            }
            log("总产出规格：" + outPack.getSpecsDesc() + "数量：" + outPack.getProductProcessQty() + "重量：" + outPack.getProductProcessWeight());
            long qty = outPack.getProductProcessQty() / inPackCount;
            outPack.setProductProcessQty(qty);
            log("单投料产出捆包数量：" + qty);
            // 重量=单规格总重量/投料捆包数
            totalWeight = totalWeight.add(outPack.getProductProcessWeight()
                    .divide(new BigDecimal(inPackCount), 8, RoundingMode.HALF_UP));
            // 数量
            totalQty = totalQty + qty;
            // 根据规格获取宽度
            String[] arr = outPack.getSpecsDesc().split("\\*");
            BigDecimal width = new BigDecimal(arr[1]);
            // 宽度
            sumWidth = sumWidth.add(width.multiply(BigDecimal.valueOf(qty)));
        }
        log("捆包产出理论总重量t：" + totalWeight + "总捆包数：" + totalQty + "总宽度：" + sumWidth);
        // 吨转换为kg
        totalMap.put("totalWeight", totalWeight.multiply(NUMBER1000));
        // 规格总宽度
        totalMap.put("sumWidth", sumWidth);
        // 总捆包数
        totalMap.put("totalQty", BigDecimal.valueOf(totalQty));
    }

    /**
     * 获取纵切持续时间最长的重量值
     *
     * @param weightRecords 时间记录
     * @param checkDuration 是否校验持续时间，true时时间需大于10
     * @return 称重实绩
     */
    private BigDecimal getSLMaxDurationWeight(List<HDRecord> weightRecords
            , boolean checkDuration) {
        // 每个值的持续时间
        int maxCount = 0;
        // 持续时间最长的值
        BigDecimal longValue = NEGATIVE_NUMBER1;
        // 每个值对应的次数
        Map<BigDecimal, Integer> countMap = new HashMap<>();
        for (int i = weightRecords.size() - 1; i >= 0; i--) {
            HDRecord record = weightRecords.get(i);
            BigDecimal value = new BigDecimal(record.getValueStr());
            // 重量小于30kg时忽略此数据
            if (value.compareTo(NUMBER30) < 0) {
                continue;
            }
            int count = 0;
            // 获取records总value重复最多的
            if (countMap.containsKey(value)) {
                count = countMap.get(value);
            }
            count++;
            countMap.put(value, count);
            // 持续时间最长且超过10次
            if ((!checkDuration || count > 10) && count >= maxCount) {
                maxCount = count;
                longValue = value;
            }
        }
        log("各重量持续时间：" + countMap);
        log("持续时间最长的重量：" + longValue + "，持续时间：" + maxCount);
        return longValue;
    }

    /**
     * 获取ihd记录
     *
     * @param startTimeStr 开始时间 yyyyMMddHHmmss
     * @param endTimeStr   结束时间 yyyyMMddHHmmss
     * @param ihdId        实时数据库id
     * @return 记录
     * @throws Exception 异常
     */
    private List<HDRecord> queryIhdRecord(String segNo, String startTimeStr, String endTimeStr, String ihdId) throws Exception {
        log("获取时间范围内记录：" + startTimeStr + "-" + endTimeStr + "ihdId：" + ihdId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date startTime = sdf.parse(startTimeStr);
        Date endTime = sdf.parse(endTimeStr);
        // 称重数据
        List<HDRecord> records = IhdSdkUtils.querySortedRecord(segNo, ihdId, startTime, endTime);
        log("记录数：" + records.size());
        return records;
    }

    /**
     * 纵切阶梯称重
     * <p>
     * 通过分析称重记录来重新计算每个捆包的重量
     * 主要逻辑：
     * 1. 从称重记录中找重量的多个下降时间段（时间段数应该为捆包数-1）
     * 2. 根据这些时间段的重量值重新计算每个捆包的重量
     * 3. 将计算结果更新到packList中
     *
     * @param packList      产出捆包信息列表
     * @param weightRecords 称重记录
     * @param weight        总重量
     */
    private void calSLStepWeight(List<Map<String, Object>> packList
            , List<HDRecord> weightRecords
            , BigDecimal weight
            , int extraWeightCount) {
        // 单个卷不处理
        if (packList.size() == 1) {
            return;
        }
        // 获取需要计算的捆包数量
        int packCount = packList.size();
        log("产出数：" + packCount);
        // 存储有效时间段的重量值
        List<BigDecimal> weightList = new ArrayList<>();
        // 遍历称重记录，识别有效时间段
        identifyWeightPeriods(weightRecords, weight, weightList);
        // 验证找到的时间段数量是否符合预期
        int newCount = weightList.size();
        log("阶梯时间段数：" + newCount);
        if (newCount != packCount - 1) {
            log("阶梯时间段数不匹配，忽略");
            return;
        }
        // 根据找到的重量时间段重新计算每个捆包的重量
        calAndMatchWeight(packList, weight, weightList, extraWeightCount);
    }

    /**
     * 遍历称重记录，识别有效时间段
     *
     * @param records       称重记录
     * @param initialWeight 初始重量
     * @param weightList    存储有效时间段重量的列表
     */
    private void identifyWeightPeriods(List<HDRecord> records,
                                       BigDecimal initialWeight,
                                       List<BigDecimal> weightList) {
        // 上段时间重量
        BigDecimal lastPeriodValue = initialWeight;
        // 当前段时间开始索引
        int currentStartIndex = -1;
        // 本段时间重量
        BigDecimal currentPeriodValue = null;
        // 循环取
        for (int i = records.size() - 1; i >= 0; i--) {
            HDRecord record = records.get(i);
            BigDecimal currentValue = new BigDecimal(record.getValueStr());
            // 当前重量与前一段重量差值大于200kg时视为下降段
            if (currentValue.add(NUMBER200).compareTo(lastPeriodValue) <= 0) {
                if (currentStartIndex == -1) {
                    // 第一次检测时设置开始点
                    currentStartIndex = i;
                    currentPeriodValue = currentValue;
                } else {
                    // 本段内的重量差大于30kg视为不连续，此段结束
                    if (currentValue.subtract(currentPeriodValue).abs().compareTo(NUMBER30) > 0) {
                        boolean flag = updatePeriodAndWeightList(currentStartIndex, i + 1, records, weightList);
                        if (flag) {
                            lastPeriodValue = currentPeriodValue;
                            // 重新开始段
                            currentStartIndex = -1;
                            currentPeriodValue = null;
                        } else {
                            // 持续时间小于4秒，此段结束
                            currentStartIndex = i;
                            currentPeriodValue = currentValue;
                        }

                    }
                }
            } else {
                // 恢复上升时结束，记录有效时间段
                if (currentStartIndex != -1) {
                    boolean flag = updatePeriodAndWeightList(currentStartIndex, i + 1, records, weightList);
                    if (flag) {
                        lastPeriodValue = currentPeriodValue;
                    }
                    currentStartIndex = -1;
                    currentPeriodValue = null;
                }
            }
        }
    }

    /**
     * 更新有效时间段
     *
     * @param startIndex 开始索引
     * @param endIndex   结束索引
     * @param records    总记录
     * @param weightList 返回记录
     */
    private boolean updatePeriodAndWeightList(int startIndex, int endIndex,
                                              List<HDRecord> records,
                                              List<BigDecimal> weightList) {
        // 持续时间大于等于4秒
        if (startIndex - endIndex + 1 >= INT4) {
            // 截取指定时间段记录，左包含右不包含
            List<HDRecord> subList = records.subList(endIndex, startIndex + 1);
            HDRecord startRecord = subList.get(subList.size() - 1);
            HDRecord endRecord = subList.get(0);
            log("开始时间点：" + startRecord.getTimeStampStr() + "|值：" + startRecord.getValueStr());
            log("结束时间点：" + endRecord.getTimeStampStr() + "|值：" + endRecord.getValueStr());
            BigDecimal maxDurationWeight = this.getSLMaxDurationWeight(subList, false);
            weightList.add(maxDurationWeight);
            return true;
        }
        return false;
    }

    /**
     * 根据找到的重量时间段重新计算每个捆包的重量
     *
     * @param packList   捆包信息列表
     * @param weight     总重量
     * @param weightList 时间段重量列表
     */
    private void calAndMatchWeight(List<Map<String, Object>> packList
            , BigDecimal weight
            , List<BigDecimal> weightList
            , int extraWeightCount) {
        // 计算并分配每个捆包的重量
        BigDecimal otherWeight = BigDecimal.ZERO;
        for (BigDecimal tempWeight : weightList) {
            BigDecimal newWeight = weight.subtract(tempWeight).subtract(otherWeight);
            log("计算结果：" + newWeight);
            otherWeight = otherWeight.add(newWeight);
            if (matchWeight(packList, newWeight)) {
                return;
            }
        }
        // 处理最后一个捆包
        BigDecimal lastWeight = weight.subtract(otherWeight);
        if (extraWeightCount > 0) {
            // 有插棒时最后一个减插棒重量
            lastWeight = lastWeight.subtract(NUMBER10.multiply(new BigDecimal(extraWeightCount)));
        }
        log("计算结果：" + lastWeight);
        if (matchWeight(packList, lastWeight)) {
            return;
        }
        log("匹配成功，更新重量");
        // 更新所有捆包的重量
        for (Map<String, Object> packMap : packList) {
            packMap.put("netWeight", packMap.get("newWeight"));
        }
    }

    /**
     * 匹配重量
     *
     * @param packList  捆包信息
     * @param newWeight 重量
     * @return 是否失败
     */
    private boolean matchWeight(List<Map<String, Object>> packList, BigDecimal newWeight) {
        for (Map<String, Object> packMap : packList) {
            String flag = MapUtils.getStr(packMap, "flag");
            if ("1".equals(flag)) {
                continue;
            }
            BigDecimal weight = MapUtils.getBigDecimal(packMap, "netWeight", BigDecimal.ZERO);
            if (weight.subtract(newWeight).abs().compareTo(NUMBER30) < 0) {
                log("匹配成功，原重量：" + weight);
                packMap.put("newWeight", newWeight);
                packMap.put("flag", "1");
                return false;
            }
        }
        log("匹配失败");
        return true;
    }

    /**
     * 落料生成实绩
     * serviceId:S_VG_DM_1008
     * <p>
     * 接收设备生产实绩信息后调用生产接口上传
     *
     * @param inInfo 入参
     *               <li>tagId 点位</li>
     *               <li>tagValue 数量</li>
     */
    public synchronized EiInfo addBLResult(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            String tagValue = inInfo.getString("tagValue");
            String tagTime = inInfo.getString("tagTime");
            if (StrUtil.isBlank(tagId)
                    || StrUtil.isBlank(tagValue)
                    || StrUtil.isBlank(tagTime)) {
                throw new PlatException("传入参数为空！");
            }
            log("传入参数：" + tagId + "-" + tagValue + "-" + tagTime);
            // 毫秒时间戳转换未LocalDateTime类型
            LocalDateTime tagTimeLdt = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tagTime)), ZoneId.systemDefault())
                    .truncatedTo(ChronoUnit.SECONDS);
            String tagTimeStr = tagTimeLdt.format(DateUtils.FORMATTER_14);
            log("tagTimeStr：" + tagTimeStr);
            // 点位信息
            VGDM0301 tagInfo = queryTag(tagId);
            // 设备当前作业信息
            VGDM1002 resultPack = getPackForResult(tagInfo);
            // 捆包开卷时间
            LocalDateTime workStartTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
            long minutes = Duration.between(workStartTime, LocalDateTime.now()).toMinutes();
            log("实绩时间与开卷时间差值：" + minutes);
            if (minutes < 5 && !resultPack.checkIsUnited()) {
                throw new PlatException("距离开卷时间少于5分钟，忽略！" + minutes);
            }
            // 获取实绩数量
            BigDecimal quantity = new BigDecimal(tagValue);
            if (quantity.compareTo(NUMBER3) < 0) {
                throw new PlatException("实绩数量小于3，忽略！" + quantity);
            }
            // 本次实绩堆垛
            String thisStackName = this.getBLStackName(tagInfo.getSegNo(), tagTimeStr);
            if (StrUtil.isBlank(thisStackName)) {
                throw new PlatException("堆垛信息为空！" + tagTimeStr);
            }
            // 过滤数据双堆垛时只取当前实绩堆垛
            thisStackName = this.getBLStackWithTag(thisStackName, tagId);
            log("实绩堆垛：" + thisStackName);
            // 是否有侧堆垛
            boolean thirdFlag = this.checkThirdStack(tagInfo.getSegNo(), tagTimeStr);
            // 往前查实绩量应该又比当前值小的数据
            VGDM1005 tempPack = new VGDM1005();
            this.checkBLQuantity(resultPack, thisStackName, tagInfo, quantity, tagTimeStr, tempPack);
            // 加工工时
            BigDecimal processHour = this.calProcessHour(resultPack, tagTimeStr);
            // 侧堆垛校验
            VGDM1005 thirdPack = new VGDM1005();
            if (thirdFlag) {
                log("侧堆垛数量校验1");
                this.checkBLQuantity(resultPack, BL_STACK3_NAME, tagInfo, quantity, tagTimeStr, thirdPack);
            }
            // 产出捆包信息
            VIPM0007 outPack = this.getCLOutPack(resultPack);
            // 单个堆垛的片数
            int repeatCount = this.getBLStackPiece(tagInfo.getSegNo(), tagTimeStr);
            // 来料加工数量校验
            Map<String, BigDecimal> rtnMap = new HashMap<>();
            boolean forceEnd = this.checkLLJGQuantity(resultPack, outPack, quantity, rtnMap);
            if (forceEnd) {
                quantity = rtnMap.get("quantity");
            }
            // 并包处理
            boolean unitedFlag = false;
            if (!forceEnd && resultPack.checkIsUnited() && !resultPack.checkIsEnd()) {
                // 本次实绩堆垛在并包堆垛里
                if (resultPack.getUnitedStackName().contains(thisStackName)) {
                    unitedFlag = true;
                    // 并包剩余量
                    quantity = this.getBLUnitedQuantity(resultPack, quantity);
                    String[] stackNameArr = resultPack.getUnitedStackName().split(",");
                    String[] unitedPackIdArr = resultPack.getUnitedPackId().split(",");
                    resultPack.setUnitedPackId(unitedPackIdArr[0]);
                    log("第一个实绩");
                    this.createBLPack(resultPack
                            , outPack
                            , quantity
                            , "2"
                            , stackNameArr[0]
                            , resultPack.getUnitedQuantity(), null, processHour);
                    if (stackNameArr.length > 1) {
                        log("2双堆垛剩余待并包信息");
                        resultPack.setUnitedPackId(unitedPackIdArr[1]);
                        // 清除本次已并包堆垛
                        String leftStackName = resultPack.getUnitedStackName()
                                .replace(thisStackName, "")
                                .replace(",", "");
                        log("多堆垛剩余堆垛：" + leftStackName);
                        resultPack.setUnitedStackName(leftStackName);
                    }
                    if (repeatCount > 1) {
                        log("2一出二第二个实绩");
                        resultPack.setUnitedPackId(unitedPackIdArr[1]);
                        this.createBLPack(resultPack
                                , outPack
                                , quantity
                                , "2"
                                , stackNameArr[0]
                                , resultPack.getUnitedQuantity(), null, processHour);
                        // 清除并包信息
                        resultPack.setUnitedQuantity(BigDecimal.ZERO);
                        resultPack.setSumUnitedQuantity(BigDecimal.ZERO);
                        resultPack.setUnitedPackId(" ");
                        resultPack.setUnitedStackName(" ");
                        resultPack.setUnitedUuid(" ");
                    }
                    if (thirdFlag) {
                        log("侧堆垛实绩");
                        resultPack.setUnitedPackId(unitedPackIdArr[1]);
                        this.createBLPack(resultPack
                                , outPack
                                , quantity
                                , "2"
                                , BL_STACK3_NAME
                                , resultPack.getUnitedQuantity(), null, processHour);
                        // 清除并包信息
                        resultPack.setUnitedQuantity(BigDecimal.ZERO);
                        resultPack.setSumUnitedQuantity(BigDecimal.ZERO);
                        resultPack.setUnitedPackId(" ");
                        resultPack.setUnitedStackName(" ");
                        resultPack.setUnitedUuid(" ");
                    }
                    if (stackNameArr.length < 2) {
                        // 清除并包信息
                        resultPack.setUnitedQuantity(BigDecimal.ZERO);
                        resultPack.setSumUnitedQuantity(BigDecimal.ZERO);
                        resultPack.setUnitedPackId(" ");
                        resultPack.setUnitedStackName(" ");
                        resultPack.setUnitedUuid(" ");
                    }
                    this.updatePackWork(resultPack);
                }
            } else {
                log("非并包或尾包并包，清除并包信息");
                // 清除并包信息
                resultPack.setUnitedQuantity(BigDecimal.ZERO);
                resultPack.setSumUnitedQuantity(BigDecimal.ZERO);
                resultPack.setUnitedPackId(" ");
                resultPack.setUnitedStackName(" ");
                resultPack.setUnitedUuid(" ");
                this.updatePackWork(resultPack);
            }
            if (!unitedFlag) {
                String tempId = resultPack.getUnitedPackId();
                resultPack.setUnitedPackId(" ");
                // 生成实绩信息
                log("生成实绩");
                this.createBLPack(resultPack
                        , outPack
                        , quantity
                        , "2"
                        , thisStackName
                        , BigDecimal.ZERO, tempPack, processHour);
                if (repeatCount > 1) {
                    log("一出二第二个实绩");
                    this.createBLPack(resultPack
                            , outPack
                            , quantity
                            , "2"
                            , thisStackName
                            , BigDecimal.ZERO, null, processHour);
                }
                if (thirdFlag) {
                    log("侧堆垛实绩");
                    // 生成侧堆垛实绩信息
                    this.createBLPack(resultPack
                            , outPack
                            , quantity
                            , "2"
                            , BL_STACK3_NAME
                            , BigDecimal.ZERO, thirdPack, processHour);
                }
                // 还原并包号
                resultPack.setUnitedPackId(tempId);
            }
            // 余料
            if (resultPack.checkIsLeftPack() && this.dealDoubleEndStack(resultPack, thisStackName)) {
                log("余料结束");
                // 余料实绩
                this.createBLPack(resultPack, outPack, BigDecimal.ONE, "4"
                        , sphBackMap.get(tagInfo.getEArchivesNo()), BigDecimal.ZERO, null, processHour);
                // 结束捆包
                this.endPackWork(resultPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(resultPack);
            }
            // 尾包结束
            if (forceEnd || (resultPack.checkIsEnd()
                    && this.dealDoubleEndStack(resultPack, thisStackName))) {
                this.endPackWork(resultPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(resultPack);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 尾包多堆垛处理
     *
     * @param resultPack    投料捆包
     * @param thisStackName 当前堆垛
     * @return 是否可以结束
     */
    private boolean dealDoubleEndStack(VGDM1002 resultPack, String thisStackName) {
        log("尾包多堆垛处理");
        if (!resultPack.getEndStackName().contains(",")) {
            log("非多堆垛");
            return true;
        }
        // 清除本次已并包堆垛
        String leftStackName = resultPack.getEndStackName()
                .replace(thisStackName, "")
                .replace(",", "");
        log("尾包多堆垛剩余堆垛：" + leftStackName);
        // 更新并包堆垛
        resultPack.setEndStackName(leftStackName);
        this.updatePackWork(resultPack);
        return false;
    }


    /**
     * 落料并包处理
     *
     * @param resultPack 捆包信息
     * @param quantity   计数器数量
     * @return 是否为并包
     */
    private BigDecimal getBLUnitedQuantity(VGDM1002 resultPack
            , BigDecimal quantity) {
        log("并包逻辑");
        BigDecimal newQuantity = quantity.subtract(resultPack.getUnitedQuantity());
        log("下一捆包数量：" + newQuantity);
        if (newQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            log("并包数量不足:" + newQuantity);
            return quantity;
        }
        return newQuantity;
    }

    /**
     * 查询设备当前作业捆包
     *
     * @param resultPack 实绩捆包
     * @return 投料捆包信息
     */
    private VGDM1002 queryCurPack(VGDM1002 resultPack) {
        log("查询设备当前作业捆包:" + resultPack.getProcessOrderId() + "|" + resultPack.getUuid());
        //  查询当前设备作业捆包
        Map<String, String> map1 = new HashMap<>();
        map1.put("segNo", resultPack.getSegNo());
        map1.put("processOrderId", resultPack.getProcessOrderId());
        map1.put("notUuid", resultPack.getUuid());
        map1.put("packStatus", "1");
        log("查询当前设备作业捆包：" + resultPack.getProcessOrderId());
        List<VGDM1002> list = dao.query(VGDM1002.QUERY, map1);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        log("无当前设备作业捆包");
        return null;
    }

    /**
     * 校验落料实绩数量是否正确
     * <p>
     * 查找最近的相同投料相同堆垛的实绩时间作为开始时间，当前时间为结束时间
     * 此段时间内应存在多个小于实绩数量的值
     *
     * @param resultPack 投料捆包
     * @param tagInfo    点位信息
     * @param quantity   数量
     * @throws Exception 异常
     */
    private void checkBLQuantity(VGDM1002 resultPack, String stackName, VGDM0301 tagInfo
            , BigDecimal quantity, String endTime, VGDM1005 tempPack) throws Exception {
        // 默认从捆包启动开始
        String startTime = resultPack.getStartTime();
        // 查询相同堆垛的实绩信息
        Map<String, String> map = new HashMap<>();
        map.put("relevanceId", resultPack.getUuid());
        log("查找相同堆垛的实绩信息：" + stackName);
        map.put("stackName", stackName);
        map.put("orderBy", "REC_CREATE_TIME desc");
        List<VGDM1005> list = dao.query(VGDM1005.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            log("无相同堆垛实绩信息");
            this.checkBLQuantity2(quantity, resultPack, tagInfo, startTime, endTime);
            return;
        }
        if (resultPack.checkIsUnited()) {
            log("并包不处理");
            this.checkBLQuantity2(quantity, resultPack, tagInfo, startTime, endTime);
            return;
        }
        VGDM1005 vgdm1005 = list.get(0);
        log("相同堆垛的实绩信息：" + vgdm1005.getUuid());
        if (StrUtil.isNotBlank(vgdm1005.getFinishingShuntFlag())) {
            log("精整分流不过滤" + vgdm1005.getFinishingShuntFlag());
            this.checkBLQuantity2(quantity, resultPack, tagInfo, startTime, endTime);
            return;
        }
        // 开始时间为上一实绩时间
        startTime = vgdm1005.getRecCreateTime();
        // 获取实绩值开始值
        BigDecimal startValue = this.checkBLQuantity2(quantity, resultPack, tagInfo, startTime, endTime);
        // 上一计数器的值（实绩量+并包量）
        BigDecimal lastQuantity = vgdm1005.getQuantity().add(vgdm1005.getUnitedQuantity());
        // 开始值与上一实绩值差异
        BigDecimal absQuantity = startValue.subtract(lastQuantity).abs();
        log("开始值与上一实绩差值：" + absQuantity);
        if (absQuantity.compareTo(NUMBER10) < 0) {
            log("差值在10以内，删除上一实绩");
            // 删除上一实绩
            vgdm1005.setDelFlag("1");
            Map updMap = vgdm1005.toMap();
            RecordUtils.setRevisorSys(updMap);
            dao.update(VGDM1005.UPDATE_FOR_DEL, updMap);
            // 并包量更新逻辑
            if (StrUtil.isNotBlank(vgdm1005.getUnitedPackId())
                    && quantity.compareTo(lastQuantity) > 0) {
                log("上一捆包为并包，更新并包捆包");
                tempPack.setUnitedPackId(vgdm1005.getUnitedPackId());
                tempPack.setUnitedQuantity(vgdm1005.getUnitedQuantity());
                tempPack.setQuantity(quantity.subtract(vgdm1005.getUnitedQuantity()));
            }
        }
    }

    /**
     * 校验落料实绩数量
     *
     * @param quantity   实绩数量
     * @param resultPack 投料捆包
     * @param tagInfo    tag信息
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 计数器开始值
     * @throws Exception 异常
     */
    private BigDecimal checkBLQuantity2(BigDecimal quantity, VGDM1002 resultPack, VGDM0301 tagInfo, String startTime, String endTime) throws Exception {
        log("校验落料实绩数量：" + quantity);
        // 查找上一实绩到当前实绩内的记录值
        List<HDRecord> records = this.queryIhdRecord(resultPack.getSegNo(), startTime, endTime, tagInfo.getTagIhdId().toString());
        if (CollectionUtils.isEmpty(records)) {
            throw new PlatException("无实绩记录信息");
        }
        // 小于当前记录值的个数
        int minCount = 0;
        // 去除重复数据
        Set<String> set = new HashSet<>();
        // 实绩开始值
        BigDecimal startValue = quantity;
        for (HDRecord record : records) {
            String valueStr = record.getValueStr();
            BigDecimal value = new BigDecimal(valueStr);
            if (value.compareTo(BigDecimal.ONE) < 0) {
                log("实绩值小于1:" + valueStr + "时间：" + record.getTimeStampStr());
                break;
            }
            if (set.contains(valueStr)) {
                continue;
            }
            set.add(valueStr);
            // 记录值小于当前值时记录
            if (value.compareTo(quantity) < 0) {
                minCount++;
            }
            // 记录值小于最小值，且差值小于3
            if (value.compareTo(startValue) < 0 && startValue.subtract(value).compareTo(NUMBER3) < 0) {
                startValue = value;
            }
        }
        log("小于实绩量记录数：" + minCount + "开始值：" + startValue);
        if (minCount < 4) {
            throw new PlatException("实绩数量有误！" + minCount);
        }
        return startValue;
    }

    /**
     * 1650横切生产实绩上传
     * serviceId:S_VG_DM_1002
     * <p>
     * 接收设备生产实绩信息后调用生产接口上传
     *
     * @param inInfo 入参
     *               <li>tagId 点位</li>
     *               <li>tagValue 数量</li>
     */
    public EiInfo addCL01Result(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            String tagValue = inInfo.getString("tagValue");
            String tagTime = inInfo.getString("tagTime");
            if (StrUtil.isBlank(tagId)
                    || StrUtil.isBlank(tagValue)
                    || StrUtil.isBlank(tagTime)) {
                throw new PlatException("传入参数为空！");
            }
            log("传入参数：" + tagId + "-" + tagValue + "-" + tagTime);
            // 毫秒时间戳转换未LocalDateTime类型
            LocalDateTime tagTimeLdt = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tagTime)), ZoneId.systemDefault())
                    .truncatedTo(ChronoUnit.SECONDS);
            String tagTimeStr = tagTimeLdt.format(DateUtils.FORMATTER_14);
            log("上一实绩时间tagTimeStr：" + tagTimeStr);
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 清除计数器
            this.clearSphQuantity(tagInfo);
            // 设备当前作业信息
            VGDM1002 resultPack = getPackForResult(tagInfo);
            // 捆包开卷时间
            LocalDateTime workStartTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
            LocalDateTime nowLdt = LocalDateTime.now();
            long minutes = Duration.between(workStartTime, nowLdt).toMinutes();
            log("实绩时间与开卷时间差值：" + minutes);
            if (minutes < 5 && !resultPack.checkIsUnited()) {
                throw new PlatException("距离开卷时间少于5分钟，忽略！" + minutes);
            }
            // 产出捆包信息
            VIPM0007 outPack = this.getCLOutPack(resultPack);
            // 获取实绩数量
            BigDecimal quantity = new BigDecimal(tagValue);
            log("实绩数量：" + quantity);
            if (quantity.compareTo(NUMBER3) < 0) {
                throw new PlatException("实绩数量过小，忽略！" + minutes);
            }
            // 查找计数器最后一个数的时间
            String nowTime = DateUtil.curDateTimeStr14();
            String quantityTime = this.getQuantityTime(quantity
                    , resultPack.getSegNo()
                    , CL01_STACK_COUNT_ID
                    , nowTime);
            log("计数器数字时间：" + quantityTime);
            // 计算加工工时
            BigDecimal processHour = this.calProcessHour(resultPack, quantityTime);
            // 堆垛处理
            String tackTagId;
            // 取计数器时间的送料平台点位，1时为2#堆垛，0时为1#堆垛
            HDRecord record = IhdSdkUtils.querySingle(resultPack.getSegNo()
                    , CL01_STACK_ID
                    , quantityTime
                    , true);
            tackTagId = tagId + record.getValueStr();
            log("堆垛点位：" + tackTagId);
            String stackName = MapUtils.getString(sphStackMap, tackTagId, " ");
            log("堆垛名称：" + stackName);
            // 精整分流处理
            String finishingShuntFlag = null;
            BigDecimal newQuantity = this.dealFinishingShunt(resultPack, tagTimeLdt, workStartTime, quantityTime
                    , quantity, outPack, tagId, processHour);
            if (newQuantity.compareTo(quantity) != 0) {
                finishingShuntFlag = resultPack.getFinishingShuntFlag();
                quantity = newQuantity;
            }
            // 并包处理
            if (resultPack.checkIsUnited()) {
                log("并包处理，并包uuid:" + resultPack.getUnitedUuid() + "当前uuid:" + resultPack.getUuid());
                if (resultPack.getUnitedUuid().equals(resultPack.getUuid())) {
                    log("uuid相同，非并包");
                    resultPack.setUnitedPackId(" ");
                    resultPack.setUnitedStackName(" ");
                    resultPack.setUnitedUuid(" ");
                    resultPack.setUnitedQuantity(BigDecimal.ZERO);
                } else {
                    quantity = this.getCLUnitedQuantity(resultPack, quantity);
                }
            }
            // 来料加工校验
            Map<String, BigDecimal> rtnMap = new HashMap<>();
            boolean forceEnd = this.checkLLJGQuantity(resultPack, outPack, quantity, rtnMap);
            if (forceEnd) {
                quantity = rtnMap.get("quantity");
            }
            // 生成实绩信息
            this.createSphPack(resultPack, outPack, quantity, "2", stackName, finishingShuntFlag, processHour, quantityTime);
            // 清除并包信息
            resultPack.setUnitedPackId(" ");
            resultPack.setUnitedStackName(" ");
            resultPack.setUnitedUuid(" ");
            resultPack.setUnitedQuantity(BigDecimal.ZERO);
            this.updatePackWork(resultPack);
            // 余料
            if (resultPack.checkIsLeftPack()) {
                // 余料实绩
                this.createSphPack(resultPack, outPack, BigDecimal.ONE, "4", stackName, processHour, quantityTime);
                // 更新结束时间
                resultPack.setEndTime(quantityTime);
                // 结束捆包
                this.endPackWork(resultPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(resultPack);
            }
            // 尾包
            if (forceEnd || resultPack.checkIsEnd()) {
                resultPack.setEndTime(quantityTime);
                // 结束捆包
                this.endPackWork(resultPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(resultPack);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 来料加工实绩数量校验
     *
     * @param resultPack 投料捆包
     * @param outPack    产出
     * @param quantity   实绩数量
     * @param rtnMap     返回修改后实绩数量
     * @return 是否结束工单
     */
    private boolean checkLLJGQuantity(VGDM1002 resultPack, VIPM0007 outPack, BigDecimal quantity
            , Map<String, BigDecimal> rtnMap) {
        log("校验来料加工实绩数量" + quantity);
        if (quantity.compareTo(BigDecimal.ONE) < 0 ||
                !"1".equals(resultPack.getTradeCode())) {
            log("非来料加工，忽略！");
            return false;
        }
        // 已有实绩重量
        BigDecimal sumWeight = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), false);
        // 本次重量
        BigDecimal thisWeight = quantity
                .multiply(outPack.getProcessSinglePackWeight())
                .setScale(0, RoundingMode.HALF_UP);
        log("本次实绩重量：" + thisWeight);
        // 剩余重量
        BigDecimal leftWeight = resultPack.getNetWeight().multiply(NUMBER1000)
                .subtract(sumWeight).subtract(thisWeight);
        log("剩余实绩重量：" + leftWeight);
        if (leftWeight.compareTo(BigDecimal.ZERO) >= 0) {
            log("未超出原料重量，忽略！");
            return false;
        }
        // 本次可用重量
        BigDecimal availableWeight = resultPack.getNetWeight().multiply(NUMBER1000)
                .subtract(sumWeight);
        log("本次可用重量：" + availableWeight);
        if (availableWeight.compareTo(BigDecimal.ZERO) <= 0) {
            log("无可用重量");
            // 数量设为0，结束工单
            rtnMap.put("quantity", BigDecimal.ZERO);
            return true;
        }
        // 可用数量
        BigDecimal newQuantity = availableWeight
                .divide(outPack.getProcessSinglePackWeight(), 0, RoundingMode.DOWN);
        log("可用数量：" + newQuantity);
        if (newQuantity.compareTo(BigDecimal.ONE) < 0) {
            log("无可用数量");
            // 数量设为0，结束工单
            rtnMap.put("quantity", BigDecimal.ZERO);
            return true;
        }
        if (newQuantity.compareTo(quantity) > 0) {
            log("可用数量大于实绩数量，取实绩数量");
            return false;
        }
        // 返回可用数量，生成实绩后结束工单
        quantity = newQuantity;
        log("更新实绩数量，结束工单");
        rtnMap.put("quantity", quantity);
        return true;
    }

    /**
     * 计算加工工时
     *
     * @param resultPack   投料捆包
     * @param quantityTime 实绩时间
     * @return 加工工时
     */
    private BigDecimal calProcessHour(VGDM1002 resultPack, String quantityTime) {
        log("计算加工工时");
        LocalDateTime quantityTimeLdt = LocalDateTime.parse(quantityTime, DateUtils.FORMATTER_14);
        LocalDateTime startTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("segNo", resultPack.getSegNo());
        queryMap.put("relevanceId", resultPack.getUuid());
        queryMap.put("orderBy", "REC_CREATE_TIME desc");
        List<VGDM1005> packList = dao.query(VGDM1005.QUERY, queryMap);
        if (CollectionUtils.isNotEmpty(packList)) {
            for (VGDM1005 pack : packList) {
                LocalDateTime packTime = LocalDateTime.parse(pack.getRecCreateTime(), DateUtils.FORMATTER_14);
                long minutes = Duration.between(packTime, quantityTimeLdt).toMinutes();
                log("距上一实绩实绩m：" + minutes);
                if (minutes < 2) {
                    continue;
                }
                log("1加工工时m：" + minutes);
                return new BigDecimal(minutes);
            }
        }
        long minutes = Duration.between(startTime, quantityTimeLdt).toMinutes();
        log("2加工工时m：" + minutes);
        if (minutes < 0) {
            minutes = 10;
        }
        return new BigDecimal(minutes);
    }

    /**
     * 获取计数器指定数量时间
     *
     * @param quantity  数量
     * @param segNo     账套
     * @param tagIhdId  点位
     * @param startTime 开始时间
     * @return 计数器数字时间
     * @throws Exception 异常
     */
    private String getQuantityTime(BigDecimal quantity, String segNo, String tagIhdId, String startTime) throws Exception {
        List<HDRecord> records = IhdSdkUtils.queryWithCount(segNo, tagIhdId, startTime, true, 5);
        if (CollectionUtils.isEmpty(records)) {
            log("无计数器记录");
            return startTime;
        }
        log("变化记录数" + records.size());
        for (HDRecord record : records) {
            BigDecimal value = new BigDecimal(record.getValueStr());
            if (value.compareTo(quantity) == 0) {
                return record.getTimeStampStr()
                        .replaceAll("[\\-: ]", "")
                        .substring(0, 14);
            }
        }
        log("未找到计数器变化时间");
        return startTime;
    }

    /**
     * 1650横切精整分流处理
     *
     * @param resultPack    投料捆包
     * @param tagTimeLdt    上一实绩时间
     * @param workStartTime 开卷时间
     * @param quantityTime  计数器时间
     * @param quantity      计数器值
     * @param outPack       产出信息
     * @param tagId         tag点
     * @return 更新后数量
     * @throws Exception 异常
     */
    private BigDecimal dealFinishingShunt(VGDM1002 resultPack
            , LocalDateTime tagTimeLdt
            , LocalDateTime workStartTime
            , String quantityTime
            , BigDecimal quantity
            , VIPM0007 outPack
            , String tagId
            , BigDecimal processHour) throws Exception {
        if (StrUtil.isBlank(resultPack.getFinishingShuntFlag())) {
            log("无精整分流标记");
            return quantity;
        }
        log("有精整分流标记:" + resultPack.getFinishingShuntFlag());
        // 获取开始时间(上一捆包实绩时间）
        String startTimeStr = tagTimeLdt.format(DateUtils.FORMATTER_14);
        // 上一捆包实绩早于开卷时间时取开卷时间
        if (tagTimeLdt.isBefore(workStartTime)) {
            startTimeStr = DateUtils.FORMATTER_14.format(workStartTime);
        }
        // 查询计数器从1到当前值的记录
        List<HDRecord> hdRecords = this.queryIhdRecord(resultPack.getSegNo()
                , startTimeStr, quantityTime, CL01_STACK_COUNT_ID);
        if (CollectionUtils.isEmpty(hdRecords)) {
            log("无计数器数据");
            return quantity;
        }
        String checkStartTime = "";
        // 循环取从1到当前值的时间
        for (HDRecord record : hdRecords) {
            int value = new BigDecimal(record.getValueStr()).intValue();
            if (value == 1) {
                checkStartTime = record.getTimeStampStr()
                        .replaceAll("[\\-: ]", "")
                        .substring(0, 14);
                log("堆垛计数为1的时间：" + checkStartTime);
                break;
            }
        }
        if (StrUtil.isBlank(checkStartTime)) {
            log("无计数器为1的时间");
            return quantity;
        }
        // 从计数器为1到当前值，判断是否切换过堆垛
        List<HDRecord> stackRecords = this.queryIhdRecord(resultPack.getSegNo()
                , checkStartTime, quantityTime, CL01_STACK_ID);
        if (CollectionUtils.isEmpty(hdRecords)) {
            log("无堆垛切换信息");
            return quantity;
        }
        String firstValue = stackRecords.get(0).getValueStr();
        String tackTagId = tagId + firstValue;
        log("初始堆垛信息：" + firstValue);
        String changeTime = "";
        for (HDRecord record : stackRecords) {
            if (!firstValue.equals(record.getValueStr())) {
                tackTagId = tagId + record.getValueStr();
                log("堆垛切换：" + record.getValueStr() + "堆垛切换时间：" + record.getTimeStampStr());
                changeTime = record.getTimeStampStr()
                        .replaceAll("[\\-: ]", "")
                        .substring(0, 14);
            }
        }
        if (StrUtil.isBlank(changeTime)) {
            log("无堆垛切换信息");
            return quantity;
        }
        // 获取堆垛切换时计数器值
        HDRecord changeRecord = IhdSdkUtils.querySingle(resultPack.getSegNo()
                , CL01_STACK_COUNT_ID, changeTime, true);
        BigDecimal changeValue = new BigDecimal(changeRecord.getValueStr());
        log("堆垛切换计数值：" + changeValue);
        if (changeValue.compareTo(BigDecimal.ONE) > 0) {
            // 新增实绩
            String stackName = MapUtils.getString(sphStackMap, tackTagId, " ");
            this.createSphPack(resultPack, outPack, changeValue
                    , "2", stackName, resultPack.getFinishingShuntFlag(), processHour, changeTime);
        }
        quantity = quantity.subtract(changeValue);
        log("精整分流剩余数量：" + quantity);
        return quantity;
    }

    /**
     * 800横切生产实绩上传
     * serviceId:S_VG_DM_1011
     * <p>
     * 接收设备生产实绩信息后调用生产接口上传
     *
     * @param inInfo 入参
     *               <li>tagId 点位</li>
     *               <li>tagValue 数量</li>
     */
    public synchronized EiInfo addCL02Result(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            String tagValue = inInfo.getString("tagValue");
            if (StrUtil.isBlank(tagId)
                    || StrUtil.isBlank(tagValue)) {
                throw new PlatException("传入参数为空！");
            }
            log("传入参数：" + tagId + "-" + tagValue);
            // 点位信息
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 清除计数器
            this.clearSphQuantity(tagInfo);
            // 设备当前作业信息
            VGDM1002 resultPack = getPackForResult(tagInfo);
            // 捆包开卷时间
            LocalDateTime workStartTime = LocalDateTime.parse(resultPack.getStartTime(), DateUtils.FORMATTER_14);
            long minutes = Duration.between(workStartTime, LocalDateTime.now()).toMinutes();
            log("实绩时间与开卷时间差值：" + minutes);
            if (minutes < 5 && !resultPack.checkIsUnited()) {
                throw new PlatException("距离开卷时间少于5分钟，忽略！" + minutes);
            }
            // 产出捆包信息
            VIPM0007 outPack = this.getCLOutPack(resultPack);
            // 获取实绩数量
            BigDecimal quantity = new BigDecimal(tagValue);
            log("实绩数量：" + quantity);
            if (quantity.intValue() < 3) {
                throw new PlatException("数量小于3忽略");
            }
            String stackName = MapUtils.getString(sphStackMap, tagId, " ");
            log("堆垛名称：" + stackName);
            // 查找计数器最后一个数的时间
            String nowTime = DateUtil.curDateTimeStr14();
            String tagIhdId = CL02_STACK1_COUNT_ID;
            if ("JC52CL020038".equals(tagId)) {
                tagIhdId = CL02_STACK2_COUNT_ID;
            }
            String quantityTime = this.getQuantityTime(quantity
                    , resultPack.getSegNo()
                    , tagIhdId
                    , nowTime);
            log("计数器数字时间：" + quantityTime);
            // 更新为单包数量
            quantity = this.updateSinglePackQuantity(quantity, resultPack.getMachineCode(), resultPack.getSegNo(), outPack.getPartId());
            // 计算加工工时
            BigDecimal processHour = this.calProcessHour(resultPack, quantityTime);
            // 并包处理
            if (this.checkCL02United(resultPack, stackName)) {
                quantity = this.getCLUnitedQuantity(resultPack, quantity);
                // 生成并包实绩
                this.createCL02UnitedPack(resultPack, outPack, quantity, stackName, processHour, quantityTime);
                // 清除并包信息
                resultPack.setUnitedPackId(" ");
                resultPack.setUnitedStackName(" ");
                resultPack.setUnitedUuid(" ");
                resultPack.setUnitedQuantity(BigDecimal.ZERO);
                this.updatePackWork(resultPack);
            } else {
                log("非并包处理");
                // 生成实绩信息
                String unitedPackId = resultPack.getUnitedPackId();
                resultPack.setUnitedPackId(" ");
                this.createSphPack(resultPack, outPack, quantity, "2", stackName, processHour, quantityTime);
                resultPack.setUnitedPackId(unitedPackId);
            }
            // 尾包是否可以结束
            boolean endFlag = true;
            log("判断能否结束");
            // 尾包或余料时，尾包堆垛不为空且本次实绩堆垛与尾包堆垛不一致时不能结束捆包
            if (resultPack.checkIsLeftPack() || resultPack.checkIsEnd()) {
                log("判断尾包堆垛是否一致:" + resultPack.getEndStackName());
                if (StrUtil.isNotBlank(resultPack.getEndStackName())
                        && !stackName.equals(resultPack.getEndStackName())) {
                    log("尾包堆垛不一致，不能结束！");
                    endFlag = false;
                }
            } else {
                log("非结束点");
                endFlag = false;
            }
            // 余料
            if (resultPack.checkIsLeftPack() && endFlag) {
                // 余料实绩
                this.createSphPack(resultPack, outPack, BigDecimal.ONE, "4", stackName, processHour, quantityTime);
                // 更新结束时间
                resultPack.setEndTime(quantityTime);
                // 结束捆包
                this.endPackWork(resultPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(resultPack);
            }
            // 尾包
            if (resultPack.checkIsEnd() && endFlag) {
                // 更新结束时间
                resultPack.setEndTime(quantityTime);
                // 结束捆包
                this.endPackWork(resultPack, EndType.NORMAL);
                // 上传IMC
                this.sendCLResult(resultPack);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 校验本次实绩是否未并包实绩
     *
     * @param resultPack 投料信息
     * @param stackName  堆垛名称
     * @return bool
     */
    private boolean checkCL02United(VGDM1002 resultPack, String stackName) {
        log("800横切并包判断：" + resultPack.getUnitedPackId()
                + "|" + resultPack.getUnitedStackName()
                + "|" + resultPack.getUnitedUuid()
                + "|" + resultPack.getUuid()
                + "|" + stackName);
        // 有并包号且尾包堆垛与并包堆垛一致且并包单据号与当前uuid不一致
        return resultPack.checkIsUnited()
                && stackName.equals(resultPack.getUnitedStackName())
                && !resultPack.getUnitedUuid().equals(resultPack.getUuid());
    }

    /**
     * 校验单包数量
     *
     * @param quantity    数量
     * @param machineCode 机组
     * @param segNo       账套
     * @param partId      物料号
     * @return 满足要求时返回单包数量
     */
    private BigDecimal updateSinglePackQuantity(BigDecimal quantity, String machineCode, String segNo, String partId) {
        log("校验单包数量：" + partId);
        Map<String, String> map = new HashMap<>();
        map.put("machineCode", machineCode);
        map.put("segNo", segNo);
        map.put("partId", partId);
        map.put("status", "10");
        List<VGDM1006> list = dao.query(VGDM1006.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            log("未找到单包数量配置信息");
            return quantity;
        }
        VGDM1006 config = list.get(0);
        log("最小数量：" + config.getMinQuantity());
        if (quantity.intValue() > config.getMinQuantity()) {
            log("更新为单包数量：" + config.getSinglePackQuantity());
            return new BigDecimal(config.getSinglePackQuantity());
        }
        log("无需更新");
        return quantity;
    }


    /**
     * 生成800横切并包实绩
     *
     * @param inPack      投料捆包
     * @param quantity    数量
     * @param stackName   堆垛名称
     * @param processHour 加工工时
     */
    private void createCL02UnitedPack(VGDM1002 inPack
            , VIPM0007 outPack
            , BigDecimal quantity
            , String stackName
            , BigDecimal processHour
            , String recCreateTime) {
        String packType = "2";
        // 并包号
        String unitedPackId = inPack.getUnitedPackId();
        // 获取投料捆包号
        String packId = inPack.getPackId();
        log("投料捆包号：" + packId + "产出捆包类型：" + packType + "数量" + quantity);
        if (quantity.compareTo(BigDecimal.ONE) < 0) {
            log("数量小于1，不生成并包实绩");
            return;
        }
        // 净重kg=单片重量*数量-去除小数
        BigDecimal netWeight = quantity
                .multiply(outPack.getProcessSinglePackWeight())
                .setScale(0, RoundingMode.HALF_UP);
        log("净重：" + netWeight);
        // 生成产出捆包号
        String outPutPackId = generatePackId(inPack.getSegNo(), "20");
        log("产出捆包号：" + outPutPackId);
        // 多捆包信息
        Map<String, BigDecimal> backMap = new HashMap<>();
        List<Map<String, Object>> multiPackList = this.createMultiPack(inPack
                , outPack, quantity, outPutPackId, backMap);
        // 新增实绩信息
        Map<String, Object> packMap = new HashMap<>();
        packMap.put("packId", packId);
        // 多投料信息
        if (multiPackList != null) {
            log("添加多捆包信息");
            packMap.put("multiPackList", multiPackList);
            netWeight = MapUtils.getBigDecimal(backMap, "backNetWeight", netWeight);
            quantity = MapUtils.getBigDecimal(backMap, "backQuantity", quantity);
            unitedPackId = " ";
        }
        // 捆包类型2成品4余料
        packMap.put("packType", packType);
        packMap.put("outPutPackId", outPutPackId);
        packMap.put("unitedPackId", unitedPackId);
        packMap.put("processHour", processHour);
        packMap.put("netWeight", netWeight);
        packMap.put("quantity", quantity.intValue());
        // 成品
        packMap.put("partId", outPack.getPartId());
        packMap.put("stackName", stackName);
        packMap.put("specsDesc", outPack.getSpecsDesc());
        packMap.put("areaCode", packMap.get("stackName"));
        // 客户代码
        packMap.put("customerId", outPack.getCustomerId());
        // 保存到数据库
        packMap.put("relevanceId", inPack.getUuid());
        packMap.put("machineCode", inPack.getMachineCode());
        packMap.put("outPackId", outPutPackId);
        packMap.put("liftFlag", "0");
        packMap.put("unitedQuantity", BigDecimal.ZERO);
        packMap.put("finishingShuntFlag", " ");
        packMap.put("segNo", inPack.getSegNo());
        packMap.put("unitCode", inPack.getSegNo());
        RecordUtils.setCreatorSys(packMap);
        if (StrUtil.isNotBlank(recCreateTime)) {
            packMap.put("recCreateTime", recCreateTime);
        }
        dao.insert(VGDM1005.INSERT, packMap);
        // 上传imc
        this.callVIPMService(inPack.getSegNo(), inPack.getProcessOrderId(), packMap, false);
    }

    /**
     * 生成多捆包信息
     *
     * @param inPack       投料捆包
     * @param outPack      产出
     * @param quantity     数量
     * @param outPutPackId 产出捆包号
     * @return 多投料捆包信息
     */
    private List<Map<String, Object>> createMultiPack(VGDM1002 inPack
            , VIPM0007 outPack
            , BigDecimal quantity
            , String outPutPackId
            , Map<String, BigDecimal> backMap) {
        // 返回数据
        List<Map<String, Object>> multiPackList = new ArrayList<>();
        log("多投料合并并包判断");
        if (StrUtil.isBlank(inPack.getUnitedUuid())
                || MesConstant.APP_CODE.equals(inPack.getUnitedUuid())) {
            log("无多捆包");
            return null;
        }
        // 查找上一作业信息
        VGDM1002 lastInPack = this.queryLastInPack(inPack.getUnitedUuid());
        if (lastInPack == null) {
            log("无上一作业信息");
            return null;
        }
        BigDecimal backNetWeight = BigDecimal.ZERO;
        BigDecimal backQuantity = BigDecimal.ZERO;
        // 投料1信息
        BigDecimal netWeight = inPack.getUnitedQuantity()
                .multiply(outPack.getProcessSinglePackWeight())
                .setScale(0, RoundingMode.HALF_UP);
        log("分配信息1：" + inPack.getUnitedQuantity() + "|" + netWeight);
        backNetWeight = backNetWeight.add(netWeight);
        backQuantity = backQuantity.add(inPack.getUnitedQuantity());
        Map<String, Object> pack1 = new HashMap<>();
        pack1.put("packId", outPutPackId);
        pack1.put("f_packId", lastInPack.getPackId());
        pack1.put("f_matInnerId", lastInPack.getMatInnerId());
        pack1.put("distributeWeight", netWeight.intValue());
        pack1.put("distributeQty", inPack.getUnitedQuantity().intValue());
        // 投料2信息
        BigDecimal netWeight2 = quantity
                .multiply(outPack.getProcessSinglePackWeight())
                .setScale(0, RoundingMode.HALF_UP);
        log("分配信息2：" + quantity + "|" + netWeight2);
        backNetWeight = backNetWeight.add(netWeight2);
        backQuantity = backQuantity.add(quantity);
        Map<String, Object> pack2 = new HashMap<>();
        pack2.put("packId", outPutPackId);
        pack2.put("f_packId", inPack.getPackId());
        pack2.put("f_matInnerId", inPack.getMatInnerId());
        pack2.put("distributeWeight", netWeight2.intValue());
        pack2.put("distributeQty", quantity.intValue());
        multiPackList.add(pack1);
        multiPackList.add(pack2);
        // 更新返回重量
        log("返回重量：" + backNetWeight + "数量" + backQuantity);
        backMap.put("backNetWeight", backNetWeight);
        backMap.put("backQuantity", backQuantity);
        return multiPackList;
    }

    /**
     * 根据uuid查找加工信息
     *
     * @param unitedUuid uuid
     */
    private VGDM1002 queryLastInPack(String unitedUuid) {
        log("查询上一作业信息:" + unitedUuid);
        if (StrUtil.isBlank(unitedUuid)) {
            return null;
        }
        Map<String, String> param = new HashMap<>();
        param.put("uuid", unitedUuid);
        List<VGDM1002> list = dao.query(VGDM1002.QUERY, param);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 横切并包
     *
     * @param resultPack 捆包信息
     * @param quantity   计数器数量
     * @return 是否为并包
     */
    private BigDecimal getCLUnitedQuantity(VGDM1002 resultPack,
                                           BigDecimal quantity) {
        log("并包逻辑");
        BigDecimal newQuantity = quantity.subtract(resultPack.getUnitedQuantity());
        log("并包数量：" + newQuantity);
        if (newQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            log("并包数量不足:" + newQuantity);
            return BigDecimal.ZERO;
        }
        return newQuantity;
    }

    /**
     * 获取横切和落料计数器数量
     *
     * @param segNo    账套
     * @param tagIhdId ihdId
     */
    private BigDecimal getSphQuantity(String segNo, String tagIhdId) throws HDSdkException {
        log("读取计数器:" + tagIhdId);
        HDRecord record = IhdSdkUtils.querySnapshot(segNo, tagIhdId);
        log("当前值：" + record.getValueStr());
        return new BigDecimal(record.getValueStr());
    }


    /**
     * 清空横切和落料计数器数量
     *
     * @param tagInfo 点位信息
     */
    private void clearSphQuantity(VGDM0301 tagInfo) {
        String tagName = tagInfo.getScadaName() + "." + tagInfo.getTagId();
        log("清除计数器：" + tagName);
        String switchValue = (new SwitchUtils()).getProcessSwitchValue(tagInfo.getSegNo(), "IF_CLEAR_COUNTER", dao);
        log("配置开关：" + switchValue);
        if (!"1".equals(switchValue)) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put("tagName", tagName);
        map.put("value", "0");
        Map<String, String> result = IplatUtils.writeValues(Collections.singletonList(map));
        if (result != null && !result.isEmpty()) {
            log("写值失败：" + result);
        }
    }

    /**
     * CL实绩上传
     */
    private void sendCLResult(VGDM1002 resultPack) {
        log("CL实绩上传:" + resultPack.getUuid());
        // 计算工序时间
        this.calPackTime(resultPack);
        // 计算加工工时
        List<Map> packMapList = resultPack.calCLWorkHour(dao);
        // 更新加工工时
        this.updateIMCProcessHour(resultPack.getSegNo(), packMapList);
    }

    /**
     * 计算工序时间
     *
     * @param resultPack 捆包信息
     */
    private void calPackTime(VGDM1002 resultPack, boolean simpleFlag) {
        // 计算工序时间
        EiInfo calInfo = new EiInfo();
        if (simpleFlag) {
            calInfo.set("simpleFlag", "1");
        }
        calInfo.addBlock(EiConstant.resultBlock).addRow(resultPack.toMap());
        calInfo.set(EiConstant.serviceName, "VGDM10");
        calInfo.set(EiConstant.methodName, "calculate");
        EiInfo rtnInfo = XLocalManager.callNewTx(calInfo);
        if (rtnInfo.getStatus() < 0) {
            logError("工序时间计算出错：", rtnInfo.getMsg());
        }
    }

    /**
     * 计算工序时间
     *
     * @param resultPack 捆包信息
     */
    private void calPackTime(VGDM1002 resultPack) {
        // 计算工序时间
        this.calPackTime(resultPack, false);
    }

    /**
     * 余料标记
     * serviceId:S_VG_DM_1003
     * <p>
     * 接收余料实绩信息后调用生产接口上传
     * 入参：
     * <li>tagId 点位</li>
     * <li>tagValue 值（重量）</li>
     */
    public EiInfo addSurplusResult(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            log("接收余料信息，点位：" + tagId);
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 设备当前作业信息
            VGDM1002 resultPack = this.queryWorkPack(tagInfo, "1", true);
            if (resultPack == null) {
                throw new PlatException("未找到当前作业信息:" + tagInfo.getEArchivesNo());
            }
            // 已经余料标记或则不处理
            if (resultPack.checkIsLeftPack()) {
                log("已有余料标记:" + resultPack.getEndType());
                return inInfo;
            }
            // 尾料标记
            if (resultPack.checkIsEnd()) {
                // 1650横切特殊判断
                if (resultPack.checkIsCL01()) {
                    LocalDateTime lastTime = LocalDateTime.parse(resultPack.getRecReviseTime(), DateUtils.FORMATTER_14);
                    log("1650横切尾包时间：" + lastTime);
                    long seconds = ChronoUnit.SECONDS.between(lastTime, LocalDateTime.now()) + 1;
                    log("距尾包时间秒数：" + seconds);
                    if (seconds > 120) {
                        log("1650横切尾包时间超时:" + seconds);
                        return inInfo;
                    }
                } else {
                    log("已有尾料标记:" + resultPack.getEndType());
                    return inInfo;
                }
            }
            // 标记是否为余料
            boolean flag = false;
            // 1650横切判断
            if (resultPack.checkIsCL01()
                    || resultPack.checkIsCL02()) {
                log("横切余料判断");
                // 已生成的实绩量
                BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
                if (sumQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    log("有实绩，横切余料");
                    flag = true;
                } else {
                    // 无实绩时判断从开卷到现在计数器是否从1开始
                    flag = this.checkIsCurQuantity(tagInfo.getSegNo()
                            , resultPack.getStartTime()
                            , DateUtil.curDateTimeStr14()
                            , tagInfo.getTagIhdId().toString());
                }
            } else {
                // 校验自动运行tag点
                String checkIhd = autoRunMap.get(resultPack.getEArchivesNo());
                if (StrUtil.isBlank(checkIhd)) {
                    throw new PlatException("设备对应自动运行点位未配置:" + resultPack.getEArchivesNo());
                }
                log("自动运行点位：" + checkIhd);
                List<HDRecord> hdRecordList = this.queryIhdRecord(resultPack.getSegNo()
                        , resultPack.getStartTime()
                        , DateUtil.curDateTimeStr14()
                        , checkIhd);
                // 判断是否存在自动运行条件
                for (HDRecord record : hdRecordList) {
                    if ("1".equals(record.getValueStr())) {
                        flag = true;
                        log("存在自动运行点位：" + record.getTimeStampStr());
                        break;
                    }
                }
            }
            if (flag) {
                // 纵切判断换刀
                if (resultPack.checkIsSL() && resultPack.changeKnife(false)) {
                    this.endPackWork(resultPack, EndType.CHANGE_KNIFE);
                } else {
                    // 更新当前作业信息-余料
                    this.endPackWork(resultPack, EndType.LEFT);
                }
            } else {
                log("非余料");
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 1650横切余料判断1
     * serviceId:S_VG_DM_1014
     * <p>
     * 判断开卷机逆寸时间是否大于10s
     * 入参：
     * <li>tagId 点位</li>
     * <li>tagValue 值（重量）</li>
     */
    public EiInfo checkCL01Surplus(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            String startTime = inInfo.getString("startTime");
            String endTime = inInfo.getString("endTime");
            if (StrUtil.isBlank(tagId) || StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) {
                throw new PlatException("传入参数为空！");
            }
            log("1650横切余料判断参数：" + tagId + "|" + startTime + "|" + endTime);
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 设备当前作业信息
            VGDM1002 resultPack = this.queryWorkPack(tagInfo, "1", true);
            if (resultPack == null) {
                throw new PlatException("未找到当前作业信息:" + tagInfo.getEArchivesNo());
            }
            // 已经余料标记则不处理
            if (resultPack.checkIsLeftPack()) {
                log("已有余料标记:" + resultPack.getEndType());
                return inInfo;
            }
            // 毫秒时间戳转换未LocalDateTime类型
            LocalDateTime startTimeLdt = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(startTime)), ZoneId.systemDefault())
                    .truncatedTo(ChronoUnit.SECONDS);
            LocalDateTime endTimeLdt = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(endTime)), ZoneId.systemDefault())
                    .truncatedTo(ChronoUnit.SECONDS);
            log("开卷机逆寸时间：" + startTimeLdt + "|" + endTimeLdt);
            long seconds = ChronoUnit.SECONDS.between(startTimeLdt, endTimeLdt);
            log("开卷机逆寸时间秒数：" + seconds);
            if (seconds < 10) {
                throw new PlatException("开卷机逆寸时间小于10s" + seconds);
            }
            // 尾料标记
            if (resultPack.checkIsEnd()) {
                LocalDateTime lastTime = LocalDateTime.parse(resultPack.getRecReviseTime(), DateUtils.FORMATTER_14);
                log("1650横切尾包时间：" + lastTime);
                seconds = ChronoUnit.SECONDS.between(lastTime, startTimeLdt) + 1;
                log("距尾包时间秒数：" + seconds);
                if (seconds < 1 || seconds > 100) {
                    log("1650横切尾包时间超时:" + seconds);
                    return inInfo;
                }
            }
            // 标记是否为余料
            boolean flag;
            // 已生成的实绩量
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
            if (sumQuantity.compareTo(BigDecimal.ZERO) > 0) {
                log("有实绩，横切余料");
                flag = true;
            } else {
                // 无实绩时判断从开卷到现在计数器是否有变化
                flag = this.checkQuantityChange(tagInfo.getSegNo()
                        , resultPack.getStartTime()
                        , DateUtils.FORMATTER_14.format(startTimeLdt)
                        , CL01_STACK_COUNT_ID);
            }
            if (flag) {
                // 更新当前作业信息-余料
                this.endPackWork(resultPack, EndType.LEFT);
            } else {
                log("非余料,退料");
                // 更新当前作业信息-退料
                this.endPackWork(resultPack, EndType.BACK);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }


    /**
     * 横切判断当前计数器是否有变化
     *
     * @param segNo        账套
     * @param startTimeStr 开始时间
     * @param endTimeStr   结束时间
     * @param ihdId        ihd
     * @return 是否为当前捆包实绩
     */
    private boolean checkQuantityChange(String segNo, String startTimeStr, String endTimeStr, String ihdId) throws Exception {
        log("校验计数器值是否发生变化:" + ihdId);
        // 查询记录
        List<HDRecord> records = this.queryIhdRecord(segNo
                , startTimeStr
                , endTimeStr
                , ihdId);
        if (CollectionUtils.isEmpty(records)) {
            log("计数器无记录");
            return false;
        }
        int curValue = new BigDecimal(records.get(0).getValueStr()).intValue();
        log("当前值：" + curValue);
        if (curValue < 3) {
            log("计数器为小于3");
            return false;
        }
        return records.size() > 1;
    }

    /**
     * 横切判断当前计数器的值是否从0开始
     *
     * @param segNo        账套
     * @param startTimeStr 开始时间
     * @param endTimeStr   结束时间
     * @param ihdId        ihd
     * @return 是否为当前捆包实绩
     */
    private boolean checkIsCurQuantity(String segNo, String startTimeStr, String endTimeStr, String ihdId) throws Exception {
        // 查询记录
        List<HDRecord> records = this.queryIhdRecord(segNo
                , startTimeStr
                , endTimeStr
                , ihdId);
        if (CollectionUtils.isEmpty(records)) {
            log("计数器无记录");
            return false;
        }
        int curValue = new BigDecimal(records.get(0).getValueStr()).intValue();
        log("当前值：" + curValue);
        if (curValue < 1) {
            log("计数器为0");
            return false;
        }
        // 除0的最小值
        for (HDRecord record : records) {
            int value = new BigDecimal(record.getValueStr()).intValue();
            if (value > 0 && value < curValue) {
                curValue = value;
            }
        }
        log("最小值：" + curValue);
        return curValue == 1;
    }

    /**
     * 落料余料标记
     * serviceId:S_VG_DM_1012
     * <p>
     * 入参：
     * <li>tagId 点位</li>
     */
    public EiInfo checkBLSurplus(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            if (StrUtil.isBlank(tagId)) {
                throw new PlatException("传入参数为空！");
            }
            log("接收落料余料信息，点位：" + tagId);
            VGDM0301 tagInfo = this.queryTag(tagId);
            // 设备当前作业信息
            VGDM1002 resultPack = this.queryWorkPack(tagInfo, "1", true);
            if (resultPack == null) {
                throw new PlatException("未找到当前作业信息:" + tagInfo.getEArchivesNo());
            }
            // 已经余料标记不处理
            if (resultPack.checkIsLeftPack()) {
                log("已有余料标记:" + resultPack.getEndType());
                return inInfo;
            }
            // 尾料标记
            if (resultPack.checkIsEnd()) {
                log("已有尾料标记:" + resultPack.getEndType());
                return inInfo;
            }
            if (StrUtil.isNotBlank(resultPack.getEndStackName())) {
                log("已有尾料堆垛，非余料" + resultPack.getEndStackName());
                return inInfo;
            }
            // 标记是否为余料
            boolean flag = false;
            // 落料判断 JC52BL01MAIN0024 PLC_卷材在矫直机入口I28.0
            log("获取点位信息：" + tagInfo.getTagIhdId());
            HDRecord record = IhdSdkUtils.querySnapshot(resultPack.getSegNo(), tagInfo.getTagIhdId().toString());
            log("卷材在矫直机入口：" + record.getTimeStampStr() + "值" + record.getValueStr());
            if ("1".equals(record.getValueStr())) {
                // 获取堆垛信息
                String tagTime = DateUtil.curDateTimeStr14();
                String stackName = this.getBLStackName(resultPack.getSegNo(), tagTime);
                // 获取计数器值和已有实绩量，任意值大于0时为余料
                BigDecimal quantity = this.getBLQuantity(resultPack.getSegNo(), stackName);
                BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
                if (quantity.compareTo(BigDecimal.ZERO) > 0
                        || sumQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    log("落料余料");
                    flag = true;
                    resultPack.setEndStackName(stackName);
                }
            }
            if (flag) {
                // 更新当前作业信息-余料
                this.endPackWork(resultPack, EndType.LEFT);
            } else {
                log("非余料");
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 退料
     * serviceId:S_VG_DM_1004
     * <p>
     * 退料
     * 入参：
     * <li>tagId 点位</li>
     * <li>tagValue 值（重量）</li>
     */
    public EiInfo backPack(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            log("接收退料信息，点位：" + tagId);
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 设备当前作业信息
            VGDM1002 resultPack = this.queryWorkPack(tagInfo, "1", true);
            if (resultPack == null) {
                throw new PlatException("未找到当前作业信息:" + tagInfo.getEArchivesNo());
            }
            // 已经并包、余料标记或尾料标记则不处理
            if (resultPack.checkIsLeftPack()
                    || resultPack.checkIsUnited()
                    || resultPack.checkIsEnd()) {
                log("已有标记:" + resultPack.getEndType());
                return inInfo;
            }
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
            if (sumQuantity.compareTo(BigDecimal.ZERO) > 0) {
                throw new PlatException("已有实绩，无法退料！");
            }
            // 校验开卷机逆转的点位
            String checkIhd = uncoilerBackMap.get(resultPack.getEArchivesNo());
            if (StrUtil.isBlank(checkIhd)) {
                throw new PlatException("设备对应开卷机逆寸点位未配置:" + resultPack.getEArchivesNo());
            }
            log("开卷机逆寸点位：" + checkIhd);
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime start = now.minusMinutes(3);
            String startTime = start.format(DateUtils.FORMATTER_14);
            String endTime = now.format(DateUtils.FORMATTER_14);
            log("时间范围：" + startTime + "--" + endTime);
            List<HDRecord> hdRecordList = this.queryIhdRecord(resultPack.getSegNo()
                    , startTime
                    , endTime
                    , checkIhd);
            boolean flag = false;
            // 判断是否存在开卷机逆寸点位
            for (HDRecord record : hdRecordList) {
                if ("1".equals(record.getValueStr())) {
                    flag = true;
                    log("存在开卷机逆寸点位：" + record.getTimeStampStr());
                    break;
                }
            }
            log("是否存在开卷机逆寸点位：" + flag);
            if (flag) {
                // 更新当前作业信息-退料
                this.endPackWork(resultPack, EndType.BACK);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 1650横切退料
     * serviceId:S_VG_DM_1019
     * <p>
     * 中心支持(开卷支持)下时触发校验  待定
     * 入参：
     * <li>tagId 点位</li>
     */
    public EiInfo backCL01Pack(EiInfo inInfo) {
        try {
            // 入参tag点
            String tagId = inInfo.getString("tagId");
            log("1650横切退料判断，点位：" + tagId);
            VGDM0301 tagInfo = this.queryTag(tagId);
            log("设备：" + tagInfo.getEArchivesNo());
            // 设备当前作业信息
            VGDM1002 resultPack = this.queryWorkPack(tagInfo, "1", true);
            if (resultPack == null) {
                throw new PlatException("未找到当前作业信息:" + tagInfo.getEArchivesNo());
            }
            // 已经余料标记或尾料标记则不处理
            if (resultPack.checkIsLeftPack()
                    || resultPack.checkIsEnd()) {
                log("已有标记:" + resultPack.getEndType());
                return inInfo;
            }
            LocalDateTime now = LocalDateTime.now();
            String endTime = DateUtils.FORMATTER_14.format(now);
            // 已有实绩信息
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao, resultPack.getUuid(), true);
            // 判断计数器值是否变化
            boolean flag = this.checkQuantityChange(tagInfo.getSegNo(),
                    resultPack.getStartTime(), endTime, CL01_STACK_COUNT_ID);
            if (flag || sumQuantity.compareTo(BigDecimal.ZERO) > 0) {
                log("已有实绩，更新为尾包");
                // 更新尾包标记
                this.endPackWork(resultPack, EndType.TAIL);
            } else {
                log("无实绩，退料判断");
                // JC52CL01MAIN0308 解板缩小阀_Q12.3
                String startTime = now.minusMinutes(5).format(DateUtils.FORMATTER_14);
                List<HDRecord> hdRecordList = this.queryIhdRecord(resultPack.getSegNo()
                        , startTime
                        , endTime
                        , "1611");
                boolean smallFlag = false;
                // 判断是否存在解板缩小点位
                for (HDRecord record : hdRecordList) {
                    if ("1".equals(record.getValueStr())) {
                        smallFlag = true;
                        log("存在解板缩小点位：" + record.getTimeStampStr());
                        break;
                    }
                }
                log("是否存在解板缩小点位：" + smallFlag);
                // 获取当前计数器量
                BigDecimal curQuantity = this.getSphQuantity(resultPack.getSegNo(), CL01_STACK_COUNT_ID);
                if (smallFlag || curQuantity.intValue() < 3) {
                    log("存在解板缩小点位或当前计数器值小于3，更新为退料");
                    // 更新当前作业信息-退料
                    this.endPackWork(resultPack, EndType.BACK);
                }
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 捆包作业结束时获取对应捆包信息
     * 注意：当生产实绩产生时可能会有新的捆包开始加工，因此查询状态为1的捆包时要查询最早的
     *
     * @param tagInfo 点位
     * @return 设备当前作业信息
     */
    private VGDM1002 getPackForResult(VGDM0301 tagInfo) {
        return getPackForResult(tagInfo, true);
    }

    /**
     * 捆包作业结束时获取对应捆包信息
     * 注意：当生产实绩产生时可能会有新的捆包开始加工，因此查询状态为1的捆包时要查询最早的
     *
     * @param tagInfo 点位
     * @return 设备当前作业信息
     */
    private VGDM1002 getPackForResult(VGDM0301 tagInfo, boolean throwError) {
        log("设备：" + tagInfo.getEArchivesNo());
        // 获取当前设备作业信息
        VGDM1002 curProcess = this.queryWorkPack(tagInfo, "1", false);
        if (curProcess == null) {
            if (throwError) {
                throw new PlatException(tagInfo.getEArchivesNo() + "未找到当前作业工单信息");
            } else {
                log("未找到当前作业捆包信息");
                return null;
            }
        }
        log("当前作业工单：" + curProcess.getProcessOrderId());
        return curProcess;
    }

    /**
     * 结束捆包作业
     *
     * @param resultPack 当前作业信息
     * @param endType    结束类型（0加工完成1退料2余料3换刀4尾包5并包)
     */
    private void endPackWork(VGDM1002 resultPack, String endType) {
        log("准备更新捆包作业标记，endType：" + endType);
        // 更新捆包作业时间
        if (StrUtil.isBlank(resultPack.getEndTime())) {
            resultPack.setEndTime(DateUtil.curDateTimeStr14());
        }
        resultPack.setEndType(endType);
        resultPack.setPackStatus("2");
        // 余料时不结束捆包，仅标记用于后续实绩判断
        if (EndType.LEFT.equals(endType)
                || EndType.CHANGE_KNIFE.equals(endType)
                || EndType.TAIL.equals(endType)) {
            log("余料、换刀、尾包标记");
            resultPack.setEndTime(" ");
            resultPack.setPackStatus("1");
        }
        // 更新捆包作业信息
        Map updateMap = resultPack.toMap();
        RecordUtils.setRevisorSys(updateMap);
        dao.update(VGDM1002.UPDATE, updateMap);
    }

    /**
     * 更新捆包作业信息
     *
     * @param resultPack 当前作业信息
     */
    private void updatePackWork(VGDM1002 resultPack) {
        log("更新捆包作业信息");
        // 更新捆包作业信息
        Map updateMap = resultPack.toMap();
        RecordUtils.setRevisorSys(updateMap);
        dao.update(VGDM1002.UPDATE, updateMap);
    }

    /**
     * 生成捆包号、并包号
     *
     * @param segNo       业务单元代码
     * @param packTypeStr 捆包类型10原料 20成品 B并包
     * @return 捆包号
     */
    private String generatePackId(String segNo, String packTypeStr) {
        // 捆包类型10原料 20成品 B并包
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("segNo", segNo);
        eiInfo.set("packType", packTypeStr);
        eiInfo.set(EiConstant.serviceId, "S_VI_PM_9033");
        // 调用共享服务
        log("调用共享服务获取捆包号，业务单元：" + segNo + "，捆包类型：" + packTypeStr);
        EiInfo rtnInfo = EServiceManager.call(eiInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
        return rtnInfo.getString("packId");
    }

    /**
     * 上传实绩到IMC(横切、落料）
     *
     * @param segNo          账套
     * @param processOrderId 工单
     * @param packMap        捆包信息
     * @param isError        是否抛出异常
     */
    public void callVIPMService(String segNo, String processOrderId, Map<String, Object> packMap, boolean isError) {
        String machineCode = MapUtils.getStr(packMap, "machineCode");
        String customerId = MapUtils.getStr(packMap, "customerId");
        log("判断并包号是否上传：" + machineCode + "|" + customerId);
        if ("001259".equals(customerId)
                && ("H1".equals(machineCode) || "L1".equals(machineCode))) {
            log("清除并包号");
            packMap.put("unitedPackId", " ");
        }
        List<Map<String, Object>> packList = new ArrayList<>();
        packList.add(packMap);
        callVIPMService(segNo, processOrderId, packList, isError);
    }

    /**
     * 上传实绩到IMC
     *
     * @param segNo          账套
     * @param processOrderId 工单
     * @param packList       捆包信息
     * @param isError        是否抛出异常
     */
    public void callVIPMService(String segNo, String processOrderId, List packList, boolean isError) {
        log("准备上传实绩：" + segNo + "|" + processOrderId + "|" + packList.size());
        String switchValue = (new SwitchUtils()).getProcessSwitchValue(segNo, "IF_SEND_ACTUAL_PRODUCT_IMC", dao);
        log("配置开关：" + switchValue);
        if (!"1".equals(switchValue)) {
            return;
        }
        // 上传实绩
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("segNo", segNo);
        eiInfo.set("processOrderId", processOrderId);
        eiInfo.set("packList", packList);
        eiInfo.set(EiConstant.serviceName, "VIPMInterfaces");
        eiInfo.set(EiConstant.methodName, "addProcessResult");
        if (isError) {
            EiInfo rtnInfo = XLocalManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                log("生产实绩上传出错" + rtnInfo.getMsg());
                throw new PlatException(rtnInfo.getMsg());
            }
        } else {
            EiInfo rtnInfo = XLocalManager.callNewTx(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                logError("生产实绩上传出错", rtnInfo.getMsg());
            }
        }
    }

    /**
     * 更新加工工时到IMC
     *
     * @param packList 捆包信息
     */
    public void updateIMCProcessHour(String segNo, List<Map> packList) {
        if (CollectionUtils.isEmpty(packList)) {
            log("无可上传实绩信息");
            return;
        }
        log("准备更新加工工时|" + packList.size());
        String switchValue = (new SwitchUtils()).getProcessSwitchValue(segNo, "IF_UPDATE_IMC_PROCESS_HOUR", dao);
        log("配置开关：" + switchValue);
        if (!"1".equals(switchValue)) {
            return;
        }
        // 上传实绩
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("packList", packList);
        eiInfo.set(EiConstant.serviceId, "S_VI_PM_0015");
        log("调用共享服务更新加工工时");
        EiInfo rtnInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
        if (rtnInfo.getStatus() < 0) {
            logError("更新加工工时出错", rtnInfo.getMsg());
        }
    }

    /**
     * pda退料
     * <p>
     * 入参：
     * <li>packId 捆包号</li>
     */
    public EiInfo pdaBackPack(EiInfo inInfo) {
        try {
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            // 入参tag点
            String packId = inInfo.getString("packId");
            String segNo = inInfo.getString("segNo");
            String machineCode = inInfo.getString("machineCode");
            if (StrUtil.isBlank(packId) || StrUtil.isBlank(segNo) || StrUtil.isBlank(machineCode)) {
                throw new PlatException("传入参数为空！");
            }
            log("传入参数：packId:" + packId + "|segNo:" + segNo + "|machineCode" + machineCode);
            Map<String, String> map = new HashMap<>();
            map.put("packId", packId);
            map.put("machineCode", machineCode);
            map.put("segNo", segNo);
            map.put("activeFlag", "1");
            List<VGDM1002> packList = dao.query(VGDM1002.QUERY, map);
            if (CollectionUtils.isEmpty(packList)) {
                log("没有当前作业信息");
                return inInfo;
            }
            VGDM1002 pack = packList.get(0);
            log("捆包状态：" + pack.getPackStatus());
            // 上料时删除原数据
            if ("0".equals(pack.getPackStatus())) {
                pack.setDelFlag("1");
            } else if ("1".equals(pack.getPackStatus())) {
                // 加工中，删除数据，todo imc领料回退
                pack.setDelFlag("1");
            } else {
                log("当前状态不允许退料");
                return inInfo;
            }
            Map updateMap = pack.toMap();
            RecordUtils.setRevisorSys(updateMap);
            dao.update(VGDM1002.UPDATE, updateMap);
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }


    /**
     * 超过时间未结束的作业自动结束
     */
    public EiInfo autoEnd(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            int hours = inInfo.getInt("hours");
            if (hours < 1) {
                throw new PlatException("时间间隔不能小于1小时");
            }
            log("传入参数：" + segNo + "|" + hours);
            LocalDateTime minTime = LocalDateTime.now().minusHours(hours);
            String timeStr = DateUtils.FORMATTER_14.format(minTime);
            log("时间：" + timeStr);
            Map<String, String> map = new HashMap<>();
            map.put("segNo", segNo);
            map.put("minCreateTime", timeStr);
            map.put("activeFlag", "1");
            List<VGDM1002> list = dao.query(VGDM1002.QUERY, map);
            if (CollectionUtils.isEmpty(list)) {
                inInfo.setMsg("无超时加工信息");
                return inInfo;
            }
            String endTime = DateUtil.curDateTimeStr14();
            List<Map> updList = new ArrayList<>();
            for (VGDM1002 pack : list) {
                log("准备结束,机组：" + pack.getMachineCode()
                        + "uuid:" + pack.getUuid()
                        + "工单：" + pack.getProcessOrderId()
                        + "捆包：" + pack.getPackId());
                pack.setEndTime(endTime);
                pack.setPackStatus("2");
                pack.setEndType("9");
                Map updateMap = pack.toMap();
                RecordUtils.setRevisorSys(updateMap);
                updList.add(updateMap);
            }
            // 批量更新
            DaoUtils.updateBatch(dao, VGDM1002.UPDATE, updList);
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }
}
