package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0501;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拆包区管理
 */
public class ServiceLIDS0501 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0501().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0501.QUERY, new LIDS0501());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0501.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //查询拆包区编码状态，判断非新增状态数据不可修改
                    Map queryMap = new HashMap();
                    queryMap.clear();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    //queryMap.put("unpackAreaId", MapUtils.getString(itemMap, "unpackAreaId"));
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    queryMap.put("status", "10");
                    int count = super.count(LIDS0501.COUNT_UUID, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "unpackAreaId") + ",拆包区编码状态非新增状态，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0501.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询拆包区代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                //queryMap.put("unpackAreaId", MapUtils.getString(itemMap, "unpackAreaId"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0501.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "unpackAreaId") + ",拆包区编码状态非新增状态，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0501.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo validateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可启用!");
                }
                //查询拆包区编码状态，判断非新增状态数据不可启用
                Map queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                //queryMap.put("unpackAreaId", MapUtils.getString(itemMap, "unpackAreaId"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0501.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "unpackAreaId") + ",拆包区编码状态非新增状态，不可生效!");
                }
                //状态变更为生效
                itemMap.put("status", "20");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0501.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 反生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo deValidateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可反生效!");
                }
                //查询拆包区编码状态，判断非启用状态
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                //queryMap.put("unpackAreaId", MapUtils.getString(itemMap, "unpackAreaId"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "20");
                int count = super.count(LIDS0501.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "unpackAreaId") + ",拆包区编码状态生效状态，不可反生效!");
                }
                //状态变更为新增
                itemMap.put("status", "10");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0501.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行反生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
