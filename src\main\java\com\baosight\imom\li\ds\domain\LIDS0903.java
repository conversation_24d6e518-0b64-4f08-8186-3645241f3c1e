/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids0903;

import java.util.Map;

/**
* Tlids0903
* 库存履历表
*/
public class LIDS0903 extends Tlids0903 {
        public static final String QUERY = "LIDS0903.query";
        public static final String COUNT = "LIDS0903.count";
        public static final String INSERT = "LIDS0903.insert";
        public static final String UPDATE = "LIDS0903.update";
        public static final String DELETE = "LIDS0903.delete";
        public static final String INERT_INTERFACE_LIST = "LIDS0903.inert0903InterfaceList";
        public static final String RESET_PACK_XYZ = "LIDS0903.resetPackXyz";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0903() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}