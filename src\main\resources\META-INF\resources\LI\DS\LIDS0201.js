$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //编辑行
    let editorCell;

    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "factoryArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                },
                {
                    field: "factoryAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                }, {
                    field: "factoryBuilding",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂房代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                }, {
                    field: "factoryBuildingName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂房名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房查询"
                            })
                        }
                    }
                },
                {
                    field: "loadingPointNo",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "装卸点编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "handPointInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂内装卸点查询"
                            })
                        }
                    }
                }
            ],
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {
                //生效(状态不为新增，不可生效)
                $("#VALIDATE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "10" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为新增,不可生效!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0201", "validateCross", true, null, null, false);
                });

                //反生效(状态不为生效，不可反生效)
                $("#DEVALIDATION").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "20" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为生效,不可反生效!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0201", "deValidateCross", true, null, null, false);
                });

                //弹框编号为空时，隐藏确认回填按钮
                let windowId = $("#inqu_status-0-windowId").val();
                if(IPLAT.isBlankString(windowId)){
                    $("#CONFIRM").hide();
                }
                //确认回填
                $("#CONFIRM").on("click", function (e) {
                    let windowId = $("#inqu_status-0-windowId").val();
                    if (!IPLAT.isBlankString(windowId)) {
                        //判断是否勾选数据
                        let checkRows = resultGrid.getCheckedRows();
                        if(checkRows.length < 1){
                            NotificationUtil({msg: "未勾选需要回填的数据，不可点击确认!"}, "error");
                            return;
                        }
                        //校验勾选厂区，厂房必须一致(取第一条的数据)
                        let factoryArea = checkRows[0].factoryArea;
                        let factoryBuilding = checkRows[0].factoryBuilding;
                        for (let i = 1; i < checkRows.length; i++) {
                            if (checkRows[i].factoryArea !== factoryArea || checkRows[i].factoryBuilding !== factoryBuilding) {
                                NotificationUtil({msg: "所勾选的跨区信息中，厂区厂房不一致!"}, "error");
                                return;
                            }
                        }
                        //关闭下拉框
                        window.parent[windowId + "Window"].close();
                    }
                });

                //更改装卸点
                $("#UPDATE_LOADING_POINT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status === "00" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态为撤销或未保存,不可更改装卸点!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0201", "updateLoadingPoint", true, null, null, false);
                });
            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
                /**
                 * 状态不为新增时，不允许编辑
                 */
                if (e.model.status !== "10" && !e.model.isNew() && e.field !== "loadingPointNo" && e.field !== "loadingPointName") {
                    e.preventDefault();
                    return;
                }
            },
            onDelete: function (e) {
                let rows = e.sender.getCheckedRows();
                for (let i = 0; i < rows.length; i++) {
                    if (rows[i].status !== "10") {
                        NotificationUtil({msg: "勾选数据状态不为新增,不可删除!"}, "error");
                        e.preventDefault();
                        return;
                    }
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //厂区厂房管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-factoryArea").val(rows[0].factoryArea);
                $("#inqu_status-0-factoryAreaName").val(rows[0].factoryAreaName);
                $("#inqu_status-0-factoryBuilding").val(rows[0].factoryBuilding);
                $("#inqu_status-0-factoryBuildingName").val(rows[0].factoryBuildingName);
            }
        }
    });

    //厂区厂房管理弹窗(表格用)
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfoGrid",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorCell.model, "factoryArea", rows[0].factoryArea);
                resultGrid.setCellValue(editorCell.model, "factoryAreaName", rows[0].factoryAreaName);
                resultGrid.setCellValue(editorCell.model, "factoryBuilding", rows[0].factoryBuilding);
                resultGrid.setCellValue(editorCell.model, "factoryBuildingName", rows[0].factoryBuildingName);
            }
        }
    });

    //厂内装卸点管理弹窗-表格
    IMOMUtil.windowTemplate({
        windowId: "handPointInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-loadFlag").val("1");
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            // 显示 div
            iframejQuery("#BTN3").css('display', 'block');
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            var checkedRows = resultGrid.getCheckedRows();
            if (checkedRows.length > 0 && rows.length > 0) {
                //如果勾选多条，装卸点信息按,号分隔
                let handPointId = rows.map(c => c.handPointId).join(`,`);
                let handPointName = rows.map(c => c.handPointName).join(`,`);
                for (let i = 0; i < checkedRows.length; i++) {
                    resultGrid.setCellValue(checkedRows[i], "loadingPointNo", handPointId);
                    resultGrid.setCellValue(checkedRows[i], "loadingPointName", handPointName);
                }
            }
        }
    });

    //厂内装卸点管理弹窗-查询
    IMOMUtil.windowTemplate({
        windowId: "inquHandPointInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-loadFlag").val("1");
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-loadingPointNo").val(rows[0].handPointId);
                $("#inqu_status-0-loadingPointName").val(rows[0].handPointName);
            }
        }
    });

})