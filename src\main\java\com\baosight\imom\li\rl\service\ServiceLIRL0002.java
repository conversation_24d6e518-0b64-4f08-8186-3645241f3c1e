package com.baosight.imom.li.rl.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/***
 * 客户、承运商公共下拉框
 */
public class ServiceLIRL0002 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return new ServiceLIRL0101().subQuery(inInfo);
    }
}
