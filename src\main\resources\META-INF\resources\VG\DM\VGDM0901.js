$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();
    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
                // 查看流程图
                $("#FLOWCHART").on("click", function (e) {
                    IMOMUtil.handleFlowchartClick(resultGrid);
                });
                //审核通过
                $("#AGREE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0901", "agree", true, null, null, false);
                });
                // 审核驳回
                $("#REJECT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0901", "reject", true, null, null, false);
                });
                // 审核撤销
                $("#AUDITBACK").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0901", "cancelAudit", true, null, null, false);
                });
            }
        }
    };
});
