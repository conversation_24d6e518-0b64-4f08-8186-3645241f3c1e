package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.exception.PlatException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通用javabean验证工具类
 *
 * <AUTHOR> 郁在杰
 * @Description :通用javabean验证工具类
 * @Date : 2024/10/12
 * @Version : 1.0
 */
public class ValidationUtils {
    private static final Validator VALIDATOR;

    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        VALIDATOR = factory.getValidator();
    }

    /**
     * 验证实体类对象是否符合指定的约束条件
     *
     * @param obj    待验证的实体类对象
     * @param groups 验证分组，可以指定多个分组
     * @param <T>    实体类的泛型类型
     * @throws PlatException 如果验证失败，抛出包含所有违反约束的错误信息
     */
    public static <T> void validateEntity(T obj, Class<?>... groups) {
        Set<ConstraintViolation<T>> violations = VALIDATOR.validate(obj, groups);
        if (!violations.isEmpty()) {
            String errorMessage = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            throw new PlatException(errorMessage);
        }
    }

    /**
     * 验证点检数据
     *
     * @param spotCheckStandardType 点检标准类型
     * @param measureId             计量单位
     * @param upperLimit            上限值
     * @param lowerLimit            下限值
     * @param judgmentStandard      判断标准
     * @throws PlatException 如果验证失败，抛出包含错误信息的异常
     */
    public static void validateSpotCheckData(String spotCheckStandardType,
                                             String measureId,
                                             BigDecimal upperLimit,
                                             BigDecimal lowerLimit,
                                             String judgmentStandard) {
        // 定量
        if ("20".equals(spotCheckStandardType)) {
            if (StrUtil.isBlank(measureId)) {
                throw new PlatException("标准类型为定量时,计量单位不能为空");
            }
            if (upperLimit.compareTo(lowerLimit) < 0) {
                throw new PlatException("上限值必须大于等于下限值");
            }
        } else {
            if (StrUtil.isBlank(judgmentStandard)) {
                throw new PlatException("标准类型为定性时,判断标准不能为空");
            }
        }
    }

    public interface Group1 {
    }

    public interface Group2 {
    }

    public interface Group3 {
    }
}
