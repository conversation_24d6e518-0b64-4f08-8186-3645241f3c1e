<!doctype html>
<html>

<head>
    <meta charset="UTF-8">
    <title>一体机终端</title>
    <link rel="stylesheet" type="text/css" href="css/indexstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css">
</head>

<body>
<div class="top">
    <div id="logo" class="logo-baosight"></div>
    <div style="text-align: center; font-size: 42px; font-weight: bold; margin-top: 20px; margin-right: 120px;font-family: SourceHanSansCN-Bold;
    color: #00296D;
    letter-spacing: 0;">
            <span>
                安徽宝钢数智物流
            </span>
    </div>
</div>
<div class="content">
    <div class="entrance">
        <div class="sel">
            <div class="cho">
                <a class="tidan" href="common-self-machine-next.html">
                    <button class="gm">提单打印</button>
                </a>

                <a class="gongmao" href="common-self-machine-next.html">
                    <button class="gm">进厂登记</button>
                </a>
                <a class="chuku" href="common-self-machine-next.html">
                    <button class="gm">出库单打印</button>
                </a>
            </div>
        </div>

        <div class="instructions">
            <span>操作指引</span>
            <ol>
                <li>司机需点击“进厂登记”进行报到，只有登记成功后，才可进入排队叫号进程。</li>
                <li>对于钢材装货、钢材卸货+装货业务，必须有提单号才能登记；对于钢材卸货、废料提货、托盘运输、资材卸货、欧冶提货、其他物品运输，支持无提单登记。</li>
                <li>司机进厂登记时，请注意一体机上的安全告知，避免违章。</li>
                <li>司机进厂登记成功后，请关注叫号短信，避免过号。</li>
                <li>司机装货前，请在仓库办公室的一体机上点击“提单打印”，自助打印提单。</li>
                <li>司机离厂前，请在仓库办公室的一体机上点击“出库单打印”，自助打印出库单。</li>
            </ol>
        </div>
    </div>
</div>

<script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="js/config.js"></script>
<script type="text/javascript" src="js/<EMAIL>"></script>
<script type="text/javascript">
    $(document).ready(function () {
        localStorage.clear();
        const searchObj = new URLSearchParams(window.location.search);
        const segNo = searchObj.get('segNo');
        const factoryId = searchObj.get('factoryId');
        const factoryName = searchObj.get('factoryName');
        const key = searchObj.get('key');
        const strUrl = 'common-self-machine-next.html?segNo=' + segNo + '&factoryId=' + factoryId + '&factoryName=' + factoryName;

        switch (key) {
            case 'CK':
                $('.gongmao')[0].style = 'display:none;';
                $('.tidan')[0].style = 'display:block;';
                $('.chuku')[0].style = 'display:block;';
                break;
            case 'MW':
                $('.gongmao')[0].style = 'display:block;';
                $('.tidan')[0].style = 'display:none;';
                $('.chuku')[0].style = 'display:none;';
                break;
            default:
                $('.gongmao')[0].style = 'display:block;';
                $('.tidan')[0].style = 'display:block;';
                $('.chuku')[0].style = 'display:block;';
                break;
        }

        $(".gongmao").attr('href', strUrl);
        $('.gongmao').click(function () {
            localStorage.setItem("type", '0');
            if (!key) {
                return;
            }
            localStorage.setItem("key", key);
        })
        $(".chuku").attr('href', strUrl);
        $('.chuku').click(function () {
            localStorage.setItem("type", '1');
            if (!key) {
                return;
            }
            localStorage.setItem("key", key);
        })
        $(".tidan").attr('href', strUrl);
        $('.tidan').click(function () {
            localStorage.setItem("type", '2');
            if (!key) {
                return;
            }
            localStorage.setItem("key", key);
        })
    });
</script>
</body>

</html>
