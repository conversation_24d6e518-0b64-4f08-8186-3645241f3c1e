package com.baosight.imom.li.ds.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.ds.domain.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 库位附属信息管理
 */
public class ServiceLIDS0605 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0605().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0605.QUERY, new LIDS0605());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {

            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                //managementStyle: 10点状库位，20条状库位。
                String managementStyle = MapUtils.getString(itemMap, "managementStyle");
                //是否进行库位推荐 0参与推荐，1不参与推荐
                String ifPlanFlag = MapUtils.getString(itemMap, "ifPlanFlag");
                if (!"0".equals(ifPlanFlag)) {
                    throw new PlatException("不参与推荐的库位不能新增附属信息");
                }
                if (StringUtils.isBlank(managementStyle)) {
                    throw new PlatException("管理方式为空，生成失败！");
                } else if (managementStyle.equals("20")) {
                    //判断当前库位是否生成过精确点位，如果生成过，则不再生成
                    String warehouseCode = MapUtils.getString(itemMap, "warehouseCode");//X轴起点
                    String locationId = MapUtils.getString(itemMap, "locationId");//X轴终点
                    String segNo = MapUtils.getString(itemMap, "segNo");//X轴终点

                    HashMap params = new HashMap<>();
                    params.put("segNo",segNo);
                    params.put("warehouseCode", warehouseCode);
                    params.put("locationId", locationId);
                    //查询仓库，库位是否已经生成过精确点位
                    int count = super.count(LIDS0605.COUNT, params);
                    if (count > 0) {
                        throw new PlatException("当前库位已经生成过精确点位，请勿重复生成！");
                    }
                    //条状库位精确点位生成逻辑
                    BigDecimal xInitialPoint = new BigDecimal(MapUtils.getString(itemMap, "xInitialPoint"));//X轴起点
                    BigDecimal xDestination = new BigDecimal(MapUtils.getString(itemMap, "xDestination"));//X轴终点
                    //判断 xInitialPoint，xDestination是否为空
                    if (StringUtils.isBlank(MapUtils.getString(itemMap, "xInitialPoint")) || StringUtils.isBlank(MapUtils.getString(itemMap, "xDestination"))) {
                        throw new PlatException("X轴起点或X轴终点为空，生成失败！");
                    }
                    //一条库位默认个数为29，上层14，下层15，上层不参与单个库位精确位置计算，下层参与单个库位精确位置计算
                    //X轴起点-X轴终点 获取库位长度，然后计算生成单个库位的长度：库位长度/14 29为库位数量目前默认29个库位
                    BigDecimal length = xDestination.subtract(xInitialPoint);
                    //计算单个库位长度，库位长度保留整数 向下取整
                    BigDecimal singleLength = length.divide(BigDecimal.valueOf(15), 0, BigDecimal.ROUND_DOWN) ;
                    //计算库位长度-（库位数量*单个库位长度）
                    BigDecimal remainderLength = length .subtract (length.multiply(BigDecimal.valueOf(15)));
                    List<LIDS0605> lids0605List = new ArrayList<>();

                    //生成库位信息
                    LIDS0605 lids0605 = new LIDS0605();

                    lids0605.setSegNo((String) itemMap.get("segNo"));
                    lids0605.setUnitCode((String) itemMap.get("unitCode"));
                    //判断当前i是偶数还是奇数：偶数为上层库位，奇数为下层库位
                    lids0605.setWproviderId((String) itemMap.get("warehouseCode"));//仓库
                    lids0605.setLocationId((String) itemMap.get("locationId"));//库位代码
                    lids0605.setLocViewPackId("");//库位精确码
                    lids0605.setLocationName((String) itemMap.get("locationName"));// 库位名称
                    lids0605.setLocColumnId((String) itemMap.get("locColumnId"));//库位（跨+列）
                    lids0605.setUseStatus("10");//状态默认空闲
                    lids0605.setLocationType("20");//库位类型
                    lids0605.setLocationLength(length);//库位长度
                    lids0605.setSpecLower(new BigDecimal((String) itemMap.get("specLower")));//宽度下限
                    lids0605.setSpecUpper(new BigDecimal((String) itemMap.get("specUpper")));//宽度上限
                    lids0605.setStandFlag((String) itemMap.get("standFlag"));//是否立式库位 0卧式，1立式
                    lids0605.setLrAccupyFlag("0");//下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷
                    lids0605.setJgLockFlag("");//下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁
                    lids0605.setFactoryArea((String) itemMap.get("factoryArea"));//厂区
                    lids0605.setC_no((String) itemMap.get("c_no"));
                    lids0605.setCrossArea((String) itemMap.get("crossArea"));//跨区
                    lids0605.setIdleIntervalId(new BigDecimal("1"));
                    lids0605.setX_pointEndOrign(xDestination);
                    lids0605.setX_pointStartOrign(xInitialPoint);

                    lids0605.setX_pointEnd(xDestination);
                    lids0605.setX_pointStart(xInitialPoint);

                    lids0605.setFactoryBuilding((String) itemMap.get("factoryBuilding"));
                    lids0605.setFactoryBuildingName((String) itemMap.get("factoryBuildingName"));
                    lids0605.setUpForbinFlag(" ");//下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷
                    lids0605.setUpDownFlag("1");

                    //lids0605.setStandFlag((String) itemMap.get("standFlag"));

                    //设置创建人信息
                    lids0605.setRecCreator(UserSession.getUserId());
                    // 创建人姓名
                    lids0605.setRecCreatorName(UserSession.getLoginCName());
                    // 创建时间
                    lids0605.setRecCreateTime(DateUtil.curDateTimeStr14());
                    // 修改人工号
                    lids0605.setRecRevisor(UserSession.getUserId());
                    // 修改人姓名
                    lids0605.setRecRevisorName(UserSession.getLoginCName());
                    // 修改时间
                    lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
                    // UUID
                    lids0605.setUuid(UUIDUtils.getUUID());
                    // 删除标记: 0-新增;1-删除;
                    lids0605.setDelFlag(0);
                    lids0605.setArchiveFlag("0");
                    lids0605.setTenantId(" ");
                    //设置库位id
                    lids0605List.add(lids0605);
                    System.out.println(lids0605List);
                    //批量插入库位信息
                    this.dao.insert("LIDS0605.insert", lids0605);
                }
            });
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("新增成功！");
            return inInfo;
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0604.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();

            if (CollectionUtil.isNotEmpty(resultList)) {
                resultList.forEach(itemMap -> {
                    //校验库位是否已经被使用
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    String locationId = MapUtils.getString(itemMap, "locationId");
                    HashMap params = new HashMap<>();
                    params.put("segNo", segNo);
                    params.put("locationId", locationId);
                    int count = this.dao.count(LIDS0605.COUNTNUM, params);
                    if (count > 0) {
                        throw new PlatException("当前库位已经被使用，请先解除使用！");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0605.DELETE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


}
