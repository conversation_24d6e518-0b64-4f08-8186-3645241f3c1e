var vehicle_id = localStorage.getItem("vehicle_id"); //车牌号
var hand_big_type = ''; //装卸货类型
var segNo = localStorage.getItem("segNo");
var factoryId = localStorage.getItem("factoryId");
var factoryName = localStorage.getItem("factoryName");
var idCard = localStorage.getItem("idCard");
var vehicleNo = ''
let vehicleList = [];
let isTel = true;

document.addEventListener('DOMContentLoaded', function() {
    const keys = [
        '皖', '京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘',
        '鲁', '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋',
        '蒙', '陕', '吉', '闽', '贵', '粤', '青', '藏', '川', '宁',
        '琼', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
        'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
        'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
        '退格', '清空'
    ];

    const telKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
        '退格', '清空'];

    const keyboard = document.getElementById('keyboard');
    let activeInputField = null;

    const telKeyboard = document.getElementById('telKeyboard');
    let telActiveInputField = null;

    keys.forEach(function(key) {
        const keyElement = document.createElement('div');
        keyElement.className = 'key';
        keyElement.textContent = key;
        keyboard.appendChild(keyElement);

        keyElement.addEventListener('click', function() {
            if (!activeInputField) { return; }
            if (key === '退格') {
                activeInputField.value = activeInputField.value.slice(0, -1);
                return;
            }
            if (key === '清空') {
                activeInputField.value = '';
                return;
            }

            activeInputField.value += key;
            activeInputField.focus();  // 确保输入框保持焦点

        });
    });

    document.querySelectorAll('.ipt2').forEach(function(ipt2) {
        ipt2.addEventListener('focus', function() {
            activeInputField = ipt2;
            const rect = ipt2.getBoundingClientRect();
            keyboard.style.display = 'flex';
            keyboard.style.top = rect.bottom + window.scrollY + 'px';
            keyboard.style.left = rect.left + window.scrollX + 'px';
        });
    });

    // 点击其他地方隐藏小键盘
    document.addEventListener('click', function(event) {
        if (!keyboard.contains(event.target) && !event.target.classList.contains('ipt2')) {
            keyboard.style.display = 'none';
            activeInputField = null;
        }
    });

    // 防止点击键盘时触发隐藏
    keyboard.addEventListener('click', function(event) {
        event.stopPropagation();
    });

    // 电话号码键盘
    telKeys.forEach(function(key) {
        const keyElement = document.createElement('div');
        keyElement.className = 'key';
        keyElement.textContent = key;
        telKeyboard.appendChild(keyElement);

        keyElement.addEventListener('click', function() {
            if (!telActiveInputField) { return; }
            if (key === '退格') {
                telActiveInputField.value = telActiveInputField.value.slice(0, -1);
                return;
            }
            if (key === '清空') {
                telActiveInputField.value = '';
                return;
            }

            telActiveInputField.value += key;
            telActiveInputField.focus();  // 确保输入框保持焦点

        });
    });

    document.querySelectorAll('.ipt2-tel').forEach(function(ipt2) {
        ipt2.addEventListener('focus', function() {
            telActiveInputField = ipt2;
            const rect = ipt2.getBoundingClientRect();
            telKeyboard.style.display = 'flex';
            telKeyboard.style.top = rect.bottom + window.scrollY + 'px';
            telKeyboard.style.left = rect.left + window.scrollX + 'px';
        });
    });

    // 点击其他地方隐藏小键盘
    document.addEventListener('click', function(event) {
        if (!telKeyboard.contains(event.target) && !event.target.classList.contains('ipt2-tel')) {
            telKeyboard.style.display = 'none';
            telActiveInputField = null;
        }
    });

    // 防止点击键盘时触发隐藏
    telKeyboard.addEventListener('click', function(event) {
        event.stopPropagation();
    });

});

$("#factory_id").val(factoryName);
var queryData = {
    segNo:localStorage.getItem("segNo"),
    driverIdentity:localStorage.getItem("idCard"),
    serviceId: 'S_LI_RL_01',
}
showLoading('加载中');

window.onload =() => {
    $('#driverTel').val('');
    $('#province').val('');
}

$.ajax({
    type: 'post',
    contentType: "application/json",
    url: ytjServerUrl,
    data: JSON.stringify(queryData),
    complete :function(){},
    success: function(res) {
        // 接口调用成功
        closeLoading();
        $('#driverTel').val('');
        $('#province').val('');
        if (res.list){
            vehicleList = res.list;
            let str = ''
            for (let i = 0; i < res.list.length; i++) {
                str += `<button class="vehicleNoList" onclick="selectedVehicleNo(this.innerText)" >${res.list[i].vehicleNo}</button>`
            }
            $('.vehicleNo').append(str)
        }
    },
    error: function(xhr, status, error) {
        closeLoading();
    }
});

$('#province').on("input",function (){
    $('.vehicleNoList').removeClass('active');
    $('#driverTel').val('');
    isTel = true;
    $('#driverTel').prop('disabled', false).css({
        'background-color': '',
        'color': ''
    });
});

function selectedVehicleNo(value) {
    $('#province').val(value)
    // 移除所有按钮的 'active' 类
    $('.vehicleNoList').removeClass('active');

    // 添加 'active' 类到被点击的按钮
    $(`.vehicleNoList:contains('${value}')`).addClass('active');
    vehicleNo = value;
    const vehicle = vehicleList.find(v => v.vehicleNo = value);
    if (!vehicle) {
        isTel = true;
        $('#driverTel').prop('disabled', false).css({
            'background-color': '',
            'color': ''
        });
        return;
    }
    isTel = false;
    $('#driverTel').prop('disabled', true).css({
        'background-color': '#d3d3d3',
        'color': '#333'
    });
    $('#driverTel').val(vehicle.driverTel);
}
//  10 : 有提单  ；20 ： 无提单
function selected(value, css) {
    if (value == 10) {
        $(".loading").css("background-image",
            'url("img/typeactive.png")');
        $(".discharge").css("background-image",
            'url("img/typebg.png")');
        // $('#isTypeSpan').html("2.读取提单号");
    } else {
        $(".discharge").css("background-image",
            'url("img/typeactive.png")');
        $(".loading").css("background-image",
            'url("img/typebg.png")');
        // $('#isTypeSpan').html("2.选择装卸货");
    }

    //选择提单时，将装卸货类型保存
    hand_big_type = value;
    localStorage.setItem("hand_big_type", value);
}

//下一步
function nextStep() {
    const selectedValue = $('#province').val();

    // 车牌号校验
    const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{5}$|^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{6}$/;
    if (!selectedValue || !creg.test(selectedValue)) {
        Swal.fire({
            title: "请输入正确的车牌号！",
            icon: "warning",
            confirmButtonText: "确定"
        });
        return;
    }

    // 判断手机号
    const tel = $("#driverTel").val();
    const telReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(tel);
    if (!tel || !telReg) {
        Swal.fire({
            title: "请输入正确的手机号！",
            icon: "warning",
            confirmButtonText: "确定"
        });
        return;
    }

    if (!['10','20'].includes(hand_big_type)) {
        Swal.fire({
            title: "请选择提单类型！",
            icon: "warning",
            confirmButtonText: "确定"
        });
        return ;
    }

    //以防手动修改车牌号，在跳转下个页面时保存车牌号
    localStorage.setItem("vehicle_id", selectedValue);
    localStorage.setItem("factoryId", factoryId);

    if (isTel) {
        showLoading('发送中');
        getTel(tel);
        return;
    }
    localStorage.setItem('tel',tel);
    window.location.href = hand_big_type == '10' ? "billScan.html" : "billType.html";
}

function getTel(tel) {
    const parms = {
        serviceId: 'S_LI_RL_0017',
        driverTel: tel,
        driverName: localStorage.getItem('idName'),
    };
    $.ajax({
        type: 'post',
        contentType: "application/json",
        url: ytjServerUrl,
        data: JSON.stringify(parms),
        complete :function(){},
        success: function(res) {
            closeLoading();
            if (!res || !res.content) {
                Swal.fire({
                    title: "验证码发送失败！",
                    icon: "error",
                    confirmButtonText: "确定"
                });
                return;
            }

            // 弹框输入验证码
            showCodeInput(tel);

        },
        onFail: () => Swal.fire({
            title: "网络异常, 请联系管理员",
            icon: "warning",
            confirmButtonText: "确定"
        }),
    });
}

// 验证验证码的接口
function confirmCode(tel, inputCode) {
    showLoading('验证中');
    const confirmParams = {
        serviceId: 'S_LI_RL_0066', // 验证验证码的接口ID
        driverTel: tel,
        messageCode: inputCode, // 用户输入的验证码
    };

    // 调用确认验证码接口
    $.ajax({
        type: 'post',
        contentType: "application/json",
        url: ytjServerUrl,
        async: true,
        data: JSON.stringify(confirmParams),
        success: function (res) {
            closeLoading();
            if (res && res.__sys__ && res.__sys__.status == 1) {
                // 验证成功, 直接进入下一步
                localStorage.setItem('tel',tel);
                window.location.href = hand_big_type == '10' ? "billScan.html" : "billType.html";
            } else {
                // 验证失败
                Swal.fire({
                    title: '验证失败！',
                    text: res.message || '验证码错误，请重新输入。',
                    icon: 'error',
                    confirmButtonText: '重新输入',
                }).then(() => {
                    // 重新弹出验证码输入框
                    showCodeInput(tel);
                });
            }
        },
        error: function () {
            Swal.close(); // 确保异常时关闭加载提示框
            Swal.fire({
                title: "网络异常",
                text: "请联系管理员或稍后重试。",
                icon: "warning",
                confirmButtonText: "确定"
            });
        }
    });
}

// 显示验证码输入弹框
function showCodeInput(tel) {
    Swal.fire({
        title: '请输入验证码',
        html: `
            <div >
               <input id="swal2-input" type="text" class="swal2-input" placeholder="请输入收到的验证码" readonly>
            </div>
            <div id="swalKeyboard" style="display: none;"></div>
        `,
        width: '700px',
        showCancelButton: true,
        allowOutsideClick: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        position: 'top',
        preConfirm: () => {
            const inputVal = $("#swal2-input").val();
            // 检查输入框是否为空
            if (!inputVal) {
                Swal.showValidationMessage('验证码不能为空！');
                return false;
            }
            return inputVal;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // 用户点击确定按钮，输入验证码
            const inputCode = result.value;
            // 调用验证接口确认验证码
            confirmCode(tel, inputCode);
        }
    });

    // Swal.fire({
    //     title: '请输入验证码',
    //     input: 'text', // 输入框类型
    //     inputPlaceholder: '请输入收到的验证码',
    //     showCancelButton: true,
    //     allowOutsideClick: false,
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     position: 'top',
    //     preConfirm: (inputValue) => {
    //         // 检查输入框是否为空
    //         if (!inputValue) {
    //             Swal.showValidationMessage('验证码不能为空！');
    //             return false;
    //         }
    //         return inputValue;
    //     }
    // }).then((result) => {
    //     if (result.isConfirmed) {
    //         // 用户点击确定按钮，输入验证码
    //         const inputCode = result.value;
    //         // 调用验证接口确认验证码
    //         confirmCode(tel, inputCode);
    //     }
    // });

        const keys = [
            '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
            '退格', '清空'
        ];

        const keyboard = document.getElementById('swalKeyboard');
        let activeInputField = null;

        keys.forEach(function(key) {
            const keyElement = document.createElement('div');
            keyElement.className = 'key';
            keyElement.textContent = key;
            keyboard.appendChild(keyElement);

            keyElement.addEventListener('click', function() {
                if (!activeInputField) { return; }
                if (key === '退格') {
                    activeInputField.value = activeInputField.value.slice(0, -1);
                    return;
                }
                if (key === '清空') {
                    activeInputField.value = '';
                    return;
                }

                activeInputField.value += key;
                activeInputField.focus();  // 确保输入框保持焦点

            });
        });

        document.querySelectorAll('.swal2-input').forEach(function(ipt2) {
            ipt2.addEventListener('focus', function() {
                activeInputField = ipt2;
                const rect = ipt2.getBoundingClientRect();
                keyboard.style.display = 'flex';
                keyboard.style.top = rect.bottom + window.scrollY + 'px';
                keyboard.style.left = rect.left + window.scrollX + 'px';
            });
        });

        // 点击其他地方隐藏小键盘
        document.addEventListener('click', function(event) {
            if (!keyboard.contains(event.target) && !event.target.classList.contains('swal2-input')) {
                keyboard.style.display = 'none';
                activeInputField = null;
            }
        });

        // 防止点击键盘时触发隐藏
        keyboard.addEventListener('click', function(event) {
            event.stopPropagation();
        });

}