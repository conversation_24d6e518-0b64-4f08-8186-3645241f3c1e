<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0101">

    <sql id="queryResult">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        EQUIPMENT_TYPE as "equipmentType",  <!-- 设备类型 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME as "processCategoryName",  <!-- 工序大类名称 -->
        PROCESS_CATEGORY_SUB as "processCategorySub",  <!-- 工序小类代码 -->
        PROCESS_CATEGORY_SUB_NAME as "processCategorySubName",  <!-- 工序小类名称 -->
        DESIGN_PRODUCTION_CAPACITY_WEI as "designProductionCapacityWei",  <!-- 设计产能（万吨/年） -->
        DESIGN_PRODUCTION_CAPACITY_NUM as "designProductionCapacityNum",  <!-- 设计产能（万片/年） -->
        EQUIPMENT_PRODUCING_AREA as "equipmentProducingArea",  <!-- 设备产地 -->
        MAKER_NAME as "makerName",  <!-- 制造商名称 -->
        EQUIPMENT_COMMISSIONING_DATE as "equipmentCommissioningDate",  <!-- 设备投产日期 -->
        FIXED_ASSET_NUMBER as "fixedAssetNumber",  <!-- 固定资产编号 -->
        COLLECT_FLAG as "collectFlag",  <!-- 采集标记 -->
        EQUIPMENT_STATUS as "equipmentStatus",  <!-- 设备状态 -->
        ARCHIVE_ALTER_DESC as "archiveAlterDesc",  <!-- 设备档案变更说明 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName"  <!-- 厂房名称 -->
        FROM ${mevgSchema}.TVGDM0101
    </sql>

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="equipmentStatus">
            EQUIPMENT_STATUS = #equipmentStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="equipmentStatus">
            EQUIPMENT_STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="equipmentType">
            EQUIPMENT_TYPE = #equipmentType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO like concat('%',#eArchivesNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="makerName">
            MAKER_NAME like concat('%',#makerName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fixedAssetNumber">
            FIXED_ASSET_NUMBER like concat('%',#fixedAssetNumber#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategory">
            PROCESS_CATEGORY = #processCategory#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategorySub">
            PROCESS_CATEGORY_SUB = #processCategorySub#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0101">
        <include refid="queryResult"/>
        WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                E_ARCHIVES_NO asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0101 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="countById" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0101 WHERE UUID = #uuid#
    </select>

    <select id="queryByNo" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0101">
        <include refid="queryResult"/>
        WHERE
        E_ARCHIVES_NO = #eArchivesNo#
        AND DEL_FLAG = '0'
        <isNotEmpty prepend=" AND " property="equipmentStatus">
            EQUIPMENT_STATUS = #equipmentStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="equipmentStatus">
            EQUIPMENT_STATUS != '00'
        </isEmpty>
    </select>

    <select id="queryWithMachine" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T1.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T1.EQUIPMENT_TYPE as "equipmentType",  <!-- 设备类型 -->
        T1.PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        T1.PROCESS_CATEGORY_NAME as "processCategoryName",  <!-- 工序大类名称 -->
        T2.MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        T2.MACHINE_NAME as "machineName"  <!-- 机组名称 -->
        FROM ${mevgSchema}.TVGDM0101 T1
        LEFT JOIN ${meliSchema}.TLIDS0701 T2 ON T1.E_ARCHIVES_NO = T2.E_ARCHIVES_NO
        AND T1.SEG_NO = T2.SEG_NO
        AND T2.STATUS = '10'
        AND T2.DEL_FLAG = 0
        WHERE
        T1.SEG_NO = #segNo#
        AND T1.E_ARCHIVES_NO = #eArchivesNo#
        AND T1.EQUIPMENT_STATUS != '00'
        AND T1.DEL_FLAG = '0'
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0101 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        EQUIPMENT_TYPE,  <!-- 设备类型 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME,  <!-- 工序大类名称 -->
        PROCESS_CATEGORY_SUB,  <!-- 工序小类代码 -->
        PROCESS_CATEGORY_SUB_NAME,  <!-- 工序小类名称 -->
        DESIGN_PRODUCTION_CAPACITY_WEI,  <!-- 设计产能（万吨/年） -->
        DESIGN_PRODUCTION_CAPACITY_NUM,  <!-- 设计产能（万片/年） -->
        EQUIPMENT_PRODUCING_AREA,  <!-- 设备产地 -->
        MAKER_NAME,  <!-- 制造商名称 -->
        EQUIPMENT_COMMISSIONING_DATE,  <!-- 设备投产日期 -->
        FIXED_ASSET_NUMBER,  <!-- 固定资产编号 -->
        COLLECT_FLAG,  <!-- 采集标记 -->
        EQUIPMENT_STATUS,  <!-- 设备状态 -->
        ARCHIVE_ALTER_DESC,  <!-- 设备档案变更说明 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        FACTORY_BUILDING,  <!-- 厂房 -->
        FACTORY_BUILDING_NAME  <!-- 厂房名称 -->
        )
        VALUES (#eArchivesNo#, #equipmentName#, #equipmentType#, #processCategory#, #processCategoryName#,
        #processCategorySub#, #processCategorySubName#, #designProductionCapacityWei#, #designProductionCapacityNum#,
        #equipmentProducingArea#, #makerName#, #equipmentCommissioningDate#, #fixedAssetNumber#, #collectFlag#,
        #equipmentStatus#, #archiveAlterDesc#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #factoryBuilding#, #factoryBuildingName#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0101 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0101
        SET
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        EQUIPMENT_TYPE = #equipmentType#,   <!-- 设备类型 -->
        PROCESS_CATEGORY = #processCategory#,   <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME = #processCategoryName#,   <!-- 工序大类名称 -->
        PROCESS_CATEGORY_SUB = #processCategorySub#,   <!-- 工序小类代码 -->
        PROCESS_CATEGORY_SUB_NAME = #processCategorySubName#,   <!-- 工序小类名称 -->
        DESIGN_PRODUCTION_CAPACITY_WEI = #designProductionCapacityWei#,   <!-- 设计产能（万吨/年） -->
        DESIGN_PRODUCTION_CAPACITY_NUM = #designProductionCapacityNum#,   <!-- 设计产能（万片/年） -->
        EQUIPMENT_PRODUCING_AREA = #equipmentProducingArea#,   <!-- 设备产地 -->
        MAKER_NAME = #makerName#,   <!-- 制造商名称 -->
        EQUIPMENT_COMMISSIONING_DATE = #equipmentCommissioningDate#,   <!-- 设备投产日期 -->
        FIXED_ASSET_NUMBER = #fixedAssetNumber#,   <!-- 固定资产编号 -->
        COLLECT_FLAG = #collectFlag#,   <!-- 采集标记 -->
        EQUIPMENT_STATUS = #equipmentStatus#,   <!-- 设备状态 -->
        ARCHIVE_ALTER_DESC = #archiveAlterDesc#,   <!-- 设备档案变更说明 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        FACTORY_BUILDING = #factoryBuilding#,   <!-- 厂房 -->
        FACTORY_BUILDING_NAME = #factoryBuildingName#  <!-- 厂房名称 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateFactoryBuilding">
        UPDATE ${mevgSchema}.TVGDM0101
        SET
            FACTORY_BUILDING = #factoryBuilding#,   <!-- 厂房 -->
            FACTORY_BUILDING_NAME = #factoryBuildingName#,   <!-- 厂房名称 -->
            REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
            REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
            REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
            UUID = #uuid#
    </update>

    <update id="updateArchive">
        UPDATE ${mevgSchema}.TVGDM0101
        SET
        EQUIPMENT_STATUS = '98',   <!-- 设备状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        SEG_NO = #segNo#
        AND E_ARCHIVES_NO = #eArchivesNo#
    </update>
</sqlMap>