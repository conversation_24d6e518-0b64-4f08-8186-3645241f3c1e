<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0305">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="scadaName">
            SCADA_NAME = #scadaName#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.Map"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0305">
        SELECT
        DEVICE_ID as "deviceId",  <!-- 设备ID -->
        DEVICE_STATUS as "deviceStatus",  <!-- 设备状态 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0305 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0305 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryOffline" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T11.DEVICE_ID as "deviceId",  <!-- 设备ID -->
        T11.OFFLINE_MINUTES as "offlineMinutes",  <!-- 离线时间 -->
        T21.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T21.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T21.DEVICE_NAME as "deviceName"  <!-- 设备名称 -->
        FROM(
        SELECT
        DEVICE_ID,
        SEG_NO,
        SUM(
        CASE
        WHEN next_time IS NULL THEN 0
        ELSE TIMESTAMPDIFF(MINUTE, REC_CREATE_TIME, next_time)
        END
        ) AS OFFLINE_MINUTES
        FROM (
        SELECT
        t1.DEVICE_ID,
        t1.SEG_NO,
        t1.REC_CREATE_TIME,
        t1.DEVICE_STATUS,
        (SELECT MIN(REC_CREATE_TIME) FROM ${mevgSchema}.TVGDM0305 t2
        WHERE t2.REC_CREATE_TIME > t1.REC_CREATE_TIME
        AND t2.DEVICE_ID = t1.DEVICE_ID
        AND t2.SEG_NO = t1.SEG_NO
        AND substr(t1.REC_CREATE_TIME,1,8) = substr(t2.REC_CREATE_TIME,1,8)) AS next_time
        FROM ${mevgSchema}.TVGDM0305 t1
        WHERE
        SEG_NO = #segNo#
        AND substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        AND replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        ) AS derived
        WHERE DEVICE_STATUS = '0'
        group by DEVICE_ID,SEG_NO) T11
        LEFT JOIN ${mevgSchema}.TVGDM0304 T21
        ON T11.DEVICE_ID = T21.DEVICE_ID
        AND T11.SEG_NO = T21.SEG_NO
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0305 (DEVICE_ID,  <!-- 设备ID -->
        DEVICE_STATUS,  <!-- 设备状态 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#deviceId#, #deviceStatus#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0305 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0305
        SET
        DEVICE_ID = #deviceId#,   <!-- 设备ID -->
        DEVICE_STATUS = #deviceStatus#,   <!-- 设备状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#  <!-- 业务单元代码 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>