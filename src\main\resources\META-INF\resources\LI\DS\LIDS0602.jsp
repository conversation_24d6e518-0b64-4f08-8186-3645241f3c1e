<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-pollingSchemeNumber" cname="轮询方案编号" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-benchmarkFactoryArea" cname="基准厂区" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-benchmarkFactoryBuilding" cname="基准厂房" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-benchmarkCrossRegional" cname="基准跨区" colWidth="3" placeholder="模糊条件"/>
        </div>

    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" >
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="pollingSchemeNumber" cname="轮询方案编号" align="center" width="120" enable="false"/>

                <EF:EFColumn ename="benchmarkFactoryArea" cname="基准厂区" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="benchmarkFactoryBuilding" cname="基准厂房" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="benchmarkCrossRegional" cname="基准跨区" align="left" width="200"
                             required="true"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="新增" value="10"/>
                    <EF:EFOption label="删除" value="00"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" width="200"
                             required="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" primaryKey="true" width="120" enable="true" hidden="true"/>

            </EF:EFGrid>
        </div>
        <div id="info-2" title="详情">
            <EF:EFGrid isFloat="true" id="result2" blockId="result2" autoBind="false" autoDraw="no" needAuth="true" queryMethod="queryLitter"
                       insertMethod = "insertLitter" updateMethod = "updateLitter" deleteMethod = "deleteLitter">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="pollingSchemeNumber" cname="轮询方案编号" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" width="200"
                             required="true"/>
                <EF:EFColumn ename="benchmarkFactoryArea" cname="基准厂区" align="left" width="200"
                             required="true"/>
                <EF:EFColumn ename="benchmarkFactoryBuilding" cname="基准厂房" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="benchmarkCrossRegional" cname="基准跨区" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="pollOrder" cname="轮询序号" align="center" width="120"/>
                <EF:EFColumn ename="pollingFactoryArea" cname="轮询厂区" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="pollingFactoryBuilding" cname="轮询厂房" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFColumn ename="pollingAcrossRegions" cname="轮询跨区" align="left" width="200" enable="true"
                             required="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>

            </EF:EFGrid>
        </div>
    </EF:EFTab>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--区域代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="crossingChannelsInfo" width="90%" height="60%"/>
    <%--跨区编码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo2" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseCodeInfo" width="90%" height="60%"/>
</EF:EFPage>
