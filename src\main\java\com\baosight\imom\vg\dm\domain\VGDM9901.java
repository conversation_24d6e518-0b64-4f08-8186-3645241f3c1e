package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm9901;

/**
 * 点检标准表
 */
public class VGDM9901 extends Tvgdm9901 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM9901.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM9901.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM9901.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM9901.update";
    /**
     * 删除
     */
    public static final String DELETE = "VGDM9901.delete";
    /**
     * 查询
     */
    public static final String QUERY_WITH_TAG = "VGDM9901.queryWithTag";
    /**
     * 查询条数
     */
    public static final String COUNT_WITH_TAG = "VGDM9901.countWithTag";

}
