<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-05 13:10:12
       Version :  1.0
    tableName :meli.tlids1202
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CRANE_RESULT_ID  VARCHAR   NOT NULL,
     CRANE_RESULT_SUB_ID  VARCHAR   NOT NULL,
     PACK_ID  VARCHAR,
     LABEL_ID  VARCHAR,
     NET_WEIGHT  DECIMAL,
     QUANTITY  INTEGER,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_<PERSON>AG  SMALLINT,
     UUID  VARCHAR   NOT NULL   primarykey
-->
<sqlMap namespace="LIDS1202">

	<sql id="condition">
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="craneResultId">
			CRANE_RESULT_ID = #craneResultId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneResultSubId">
			CRANE_RESULT_SUB_ID = #craneResultSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="labelId">
			LABEL_ID = #labelId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isEmpty prepend=" AND " property="status">
			STATUS > '00'
			AND DEL_FLAG = '0'
		</isEmpty>
	</sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1202">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_RESULT_SUB_ID as "craneResultSubId",  <!-- 行车实绩子项号 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        STATUS as "status",  <!-- 状态 -->
        MOULD_ID	as "mouldId",  <!-- 模具ID -->
        MOULD_NAME	as "mouldName",  <!-- 模具名称 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids1202 t WHERE 1=1
		<include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
				REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids1202 WHERE 1=1
		<include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneResultId">
            CRANE_RESULT_ID = #craneResultId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneResultSubId">
            CRANE_RESULT_SUB_ID = #craneResultSubId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="labelId">
            LABEL_ID = #labelId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="netWeight">
            NET_WEIGHT = #netWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="quantity">
            QUANTITY = #quantity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids1202 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
        CRANE_RESULT_SUB_ID,  <!-- 行车实绩子项号 -->
        PACK_ID,  <!-- 捆包号 -->
        LABEL_ID,  <!-- 标签号 -->
        NET_WEIGHT,  <!-- 净重 -->
        QUANTITY,  <!-- 数量 -->
        STATUS,  <!-- 状态 -->
        MOULD_ID,  <!-- 模具ID -->
        MOULD_NAME,  <!-- 模具名称 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID  <!-- ID -->
        )
        VALUES (#segNo#, #unitCode#, #craneResultId#, #craneResultSubId#, #packId#, #labelId#, #netWeight#, #quantity#,
        #status#, #mouldId#, #mouldName#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids1202 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlids1202
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        CRANE_RESULT_ID = #craneResultId#,   <!-- 行车实绩单号 -->
        CRANE_RESULT_SUB_ID = #craneResultSubId#,   <!-- 行车实绩子项号 -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        LABEL_ID = #labelId#,   <!-- 标签号 -->
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        QUANTITY = #quantity#,   <!-- 数量 -->
        STATUS = #status#,   <!-- 状态 -->
        MOULD_ID	= #mouldId#,   <!-- 模具ID -->
        MOULD_NAME	= #mouldName#,   <!-- 模具名称 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <!--    根据行车编号，查询是否存在捆包号为空的行车实绩-->
    <select id="resultPackIsNull" resultClass="int">
        SELECT 1
        FROM meli.tlids1202 rd
        WHERE rd.SEG_NO = #segNo#
        AND TRIM(IFNULL(rd.PACK_ID,'')) = ''
        AND rd.STATUS > '00'
        AND EXISTS(SELECT 1
        FROM meli.tlids1201 rm
        WHERE rm.SEG_NO = rd.SEG_NO
        AND rm.CRANE_ID = #craneId#
        AND rm.CRANE_RESULT_ID = rd.CRANE_RESULT_ID
        AND rm.STATUS = rd.STATUS)
    </select>

<!--    更新行车实绩子表信息-->
    <update id="updateCranePerformanceSubTable">
        UPDATE meli.tlids1202
        SET
        PACK_ID = #packId#,   <!-- 捆包号 -->
        LABEL_ID = #labelId#,   <!-- 标签号 -->
        STATUS = #status#   <!-- 状态 -->
        <isNotEmpty prepend=" , " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        <isNotEmpty prepend=" AND " property="craneResultSubId">
            CRANE_RESULT_SUB_ID = #craneResultSubId#
        </isNotEmpty>
        AND STATUS > '00'
    </update>

    <!--    更新行车实绩子表状态-->
    <update id="updateStatusByCraneResultId">
        UPDATE meli.tlids1202
        SET
        STATUS = #status#   <!-- 状态 -->
        <isNotEmpty prepend=" , " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        <isNotEmpty prepend=" AND " property="craneResultSubId">
            CRANE_RESULT_SUB_ID = #craneResultSubId#
        </isNotEmpty>
        AND STATUS > '00'
    </update>

    <!--依据传入捆包号,行车实绩单号查询行车作业实绩子项-->
    <select id="queryCranePerformanceInPack" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_RESULT_SUB_ID as "craneResultSubId",  <!-- 行车实绩子项号 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        STATUS as "status",  <!-- 状态 -->
        MOULD_ID as "mouldId",  <!-- 模具ID -->
        MOULD_NAME as "mouldName",  <!-- 模具名称 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids1202
        WHERE SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        AND PACK_ID IN ($packIds$)
    </select>
</sqlMap>