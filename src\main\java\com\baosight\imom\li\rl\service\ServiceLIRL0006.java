package com.baosight.imom.li.rl.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.MapUtils;

import java.util.*;


/**
 * @Author: 韩亚宁
 * @Description: ${部门信息查询}
 * @Date: 2024/12/18 22:00
 * @Version: 1.0
 */
public class ServiceLIRL0006 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 利用材种类查询
     * serviceId
     * @param inInfo
     * @return
     */
    /*查询可利用材接口*/
    public EiInfo query (EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        Map map = inInfo.getAttr();
        String scrapType = MapUtils.getString(map, "scrapType", "");
        String bigTypeDesc = MapUtils.getString(map, "bigTypeDesc", "");
        String partId = MapUtils.getString(map, "partId", "");
        String processCodeList = inInfo.getString("processCodeList");
        HashMap hashMap = new HashMap();
        EiBlock block = inInfo.getBlock("result");
        if (block!=null){
            Map attr = block.getAttr();
            int limit = MapUtils.getInteger(attr, "limit");
            int offset = MapUtils.getInteger(attr, "offset");
            hashMap.put("limit",limit);//页数
            hashMap.put("offset",offset);//开始条数
        }
        if (org.apache.commons.lang.StringUtils.isBlank(processCodeList)){

        }else {
            hashMap.put("processCodeList",processCodeList);
        }
        hashMap.put("scrapType",scrapType);
        hashMap.put("bigTypeDesc",bigTypeDesc);
        hashMap.put("partId",partId);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("param",hashMap);
        String token = ImcGlobalUtils.getToken();
        eiInfo.set(EiConstant.serviceId,"S_UR_PB_0002");
        outInfo= EServiceManager.call(eiInfo,token);
        List<Map<String, Object>> rows=new ArrayList<>();
        if (MapUtils.isNotEmpty(outInfo.getAttr())){
            rows = (List) outInfo.getAttr().get("list");
        }
        EiBlock eiBlock = new EiBlock("result");
        outInfo.addBlock(eiBlock);
        outInfo.getBlock("result").addRows(rows);
        outInfo.getBlock("result").set("limit",rows.size());
        outInfo.setMsg("查询成功！");
        outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        //打印日志到elk
        log(DateUtil.getTimeNow(new Date()) + "："+"利用材查询传入参数："+hashMap +"\n"+"利用材查询产品中心返回的参数："+outInfo.toJSONString());
        //输出到应用日志
        System.out.println(DateUtil.getTimeNow(new Date()) + "："+"利用材查询传入参数："+hashMap +"\n"+"利用材查询产品中心返回的参数："+outInfo.toJSONString());
        return outInfo;
    }


}
