package com.baosight.imom.common.utils;

import org.apache.commons.lang.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 工程项目:.
 * 开发公司:Baosight Software LTD.co Copyright (c) 2022.
 * 类的简介:日期处理工具类-DateUtils.
 * 类的描述:封装日期处理公共方法.
 * 开发日期:2022年5月13日 上午9:00:00.
 *
 * <AUTHOR> TEL:17762608719 （开发人）.
 * @version 1.0 （开发版本）.
 * @since 1.8 （JDK版本号）.
 */
public final class DateUtils {
    public static final DateTimeFormatter FORMATTER_6 = DateTimeFormatter.ofPattern("yyyyMM");
    public static final DateTimeFormatter FORMATTER_7 = DateTimeFormatter.ofPattern("yyyy-MM");
    public static final DateTimeFormatter FORMATTER_14 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter FORMATTER_19 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter FORMATTER_16 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter FORMATTER_23 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    public static final DateTimeFormatter FORMATTER_24 = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");

    private DateUtils() {

    }

    /**
     * 将原始日期字符串转化为目标日期字符串.
     * 示例："yyyy-MM-dd" -> "yyyyMMdd000000".
     * "yyyy-MM-dd" -> "yyyyMMdd235959".
     *
     * @param strDate
     * @param strSourePattern
     * @param strTargetPattern
     * @return
     * @throws ParseException
     */
    public static String formatDate(String strDate, String strSourePattern, String strTargetPattern) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(strSourePattern);
            strDate = DateFormatUtils.format(simpleDateFormat.parse(strDate),
                    strTargetPattern);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return strDate;
    }

    /**
     * 获取当前时间方法.
     *
     * @return
     */
    public static String getFormat() {
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = dateFormat.format(l);
        return format;
    }

    /**
     * 判断时间在两个时间内.
     *
     * @return
     */
    public static boolean isTimeBetween(Date startTime, Date endTime, Date targetTime) {
        return targetTime.compareTo(startTime) >= 0 && targetTime.compareTo(endTime) <= 0;
    }

    /**
     * 格式化日期
     *
     * @param date   日期
     * @param format 格式
     * @return 字符串
     */
    public static String formatDate(Date date, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(date);
    }
}