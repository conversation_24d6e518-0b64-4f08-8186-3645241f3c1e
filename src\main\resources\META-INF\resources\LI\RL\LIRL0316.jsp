<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput type="text" ename="inqu_status-0-voucherNum" cname="提单号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
            <EF:EFInput ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3" placeholder="模糊查询"/>
        </div>
        <div class="row">
        <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                     valueField="valueField" textField="textField"
                     template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
            <EF:EFCodeOption codeName="P001"/>
        </EF:EFSelect>
            <div style="display: none;">
                <EF:EFInput ename="fileForm" type="file"/>
            </div>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0316" queryMethod="query"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true" enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false" type="hidden" hidden="true"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                <EF:EFCodeOption codeName="P001"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="voucherNum" cname="提单号" align="center" enable="false"  sort="false" />
            <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false"/>
            <EF:EFColumn ename="driverName" cname="司机姓名" align="center" enable="false"/>
            <EF:EFColumn ename="driverTel" cname="司机手机号" align="center" enable="false"/>
            <EF:EFColumn ename="driverIdentity" cname="司机身份证号" align="center" enable="false" width="180"/>
            <EF:EFColumn ename="uploadFilePath" cname="路径" align="center" enable="false"  hidden="true" />
            <EF:EFColumn ename="uploadFilePathD" cname="文件路径" align="center" enable="false"/>
<%--            <EF:EFColumn ename="attachmentChapterMark" cname="附加加盖发货章标记" align="center" width="150" enable="true"/>--%>
<%--            <EF:EFComboColumn ename="attachmentPrint" cname="附件打印机(针式打印机/A4打印机)" align="center" enable="false">--%>
<%--                <EF:EFOption label=" " value=" "/>--%>
<%--                <EF:EFOption label="一体机内置A4" value="10"/>--%>
<%--                <EF:EFOption label="一体机内置针打" value="20"/>--%>
<%--                <EF:EFOption label="仓库办公室A4" value="30"/>--%>
<%--                <EF:EFOption label="仓库办公室针打" value="40"/>--%>
<%--            </EF:EFComboColumn>--%>
<%--            <EF:EFColumn ename="attachmentPrint" cname="附件打印机" align="center" enable="false"/>--%>
            <EF:EFColumn ename="printIp" cname="打印机名称" width="100" enable="true" hidden="true"/>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>

    <!-- 附件详情弹框 -->
    <div style="display: none">
        <EF:EFWindow id="AttachmentDetails" title="" height="60%" width="90%"  refresh="true">
            <EF:EFRegion id="AttachmentDetails" title="附件详情" isFloat="true" >
                <EF:EFButton ename="QUERYFILE" style="    right: 7em; top: 1em; z-index: 1; position: absolute;" cname="确认" float="right" hidden="true"></EF:EFButton>
                <EF:EFButton ename="FILEDELETE" style="    right: 7em; top: 1em; z-index: 1; position: absolute;" cname="删除" float="right"></EF:EFButton>
<%--                <EF:EFButton ename="FILEDINSERT" style="    right: 7em; top: 1em; z-index: 1; position: absolute;" cname="删除" float="right"></EF:EFButton>--%>
                <EF:EFButton ename="FILEDUPDATE" style="    right: 11em; top: 1em; z-index: 2; position: absolute;" cname="修改" float="right"></EF:EFButton>
                <div class="row" style="margin-top: 10px; padding-left: 5px;padding-right: 20px;">
                    <EF:EFGrid blockId="AttachmentDetails" readonly="false" autoDraw="false" autoBind="false" height="260">
                        <EF:EFColumn ename="relevanceId" cname="提单号" align="center" enable="false"  sort="false"/>
                        <EF:EFColumn ename="uploadFilePath" cname="路径" align="center" enable="false"  hidden="true" />
                        <EF:EFColumn ename="uploadFilePathD" cname="附件路径" align="center" enable="false"/>
                        <EF:EFColumn ename="uploadFileName" cname="附件名称" align="center" enable="false"/>
                        <EF:EFColumn ename="uuid" cname="uuid" align="center" enable="false" hidden="true"/>
                        <EF:EFComboColumn ename="attachmentPrint" cname="附件打印机" align="center" enable="true">
                            <EF:EFOption label=" " value=" "/>
                            <EF:EFOption label="一体机内置A4" value="10"/>
                            <EF:EFOption label="一体机内置针打" value="20"/>
                            <EF:EFOption label="仓库办公室A4" value="30"/>
                            <EF:EFOption label="仓库办公室针打" value="40"/>
                        </EF:EFComboColumn>
                    </EF:EFGrid>
                </div>
            </EF:EFRegion>
        </EF:EFWindow>
    </div>

    <!-- 提单委托弹框 -->
    <div style="display: none">
        <EF:EFWindow id="billOfLading" title="" height="60%" width="90%"  refresh="true">
            <EF:EFRegion id="inqu2" title="查询条件">
                <EF:EFButton ename="QUERYDELEGATE" style="    right: 7em; top: 1em; z-index: 1; position: absolute;" cname="查询" float="right" ></EF:EFButton>
                <div class="row">
                    <EF:EFInput type="text" ename="inqu2_status-0-voucherNum" cname="提单号" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                    <EF:EFInput type="text" ename="inqu2_status-0-driverName" cname="司机姓名" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                    <EF:EFInput type="text" ename="inqu2_status-0-driverTel" cname="司机手机号" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                    <EF:EFInput type="text" ename="inqu2_status-0-driverIdentity" cname="司机身份证号" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="billOfLading" title="提货委托" isFloat="true" >
                <div class="row" style="margin-top: 10px; padding-left: 5px;padding-right: 20px;">
                    <EF:EFGrid blockId="billOfLading" readonly="true" autoDraw="false" autoBind="false" height="260">
                        <EF:EFColumn ename="ladingBillId" cname="提单号" align="center" enable="false"  sort="false"/>
                        <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false"/>
                        <EF:EFColumn ename="driver" cname="司机姓名" align="center" enable="false"/>
                        <EF:EFColumn ename="driverPhone" cname="司机手机号" align="center" enable="false"/>
                        <EF:EFColumn ename="driverId" cname="司机身份证号" align="center" enable="false" width="180"/>
                        <EF:EFColumn ename="driverId" cname="司机身份证号" align="center" enable="false" width="180"/>
                    </EF:EFGrid>
                </div>
            </EF:EFRegion>
        </EF:EFWindow>
    </div>

<%--    打印机配置页面--%>
    <div style="display: none">
        <EF:EFWindow id="attachmentPrintR" title="" height="60%" width="90%"  refresh="true">
            <EF:EFRegion id="inqu3" title="查询条件">
            </EF:EFRegion>
            <EF:EFRegion id="attachmentPrintR" title="打印机页面" isFloat="true" >
                <div class="row" style="margin-top: 10px; padding-left: 5px;padding-right: 20px;">
                    <EF:EFGrid blockId="attachmentPrintR" readonly="true" autoDraw="false" autoBind="false" height="260"
                    >
                        <EF:EFComboColumn ename="printerType" cname="打印机类型" align="center"  required="true">
                            <EF:EFOption label="一体机内置A4" value="10"/>
                            <EF:EFOption label="一体机内置针打" value="20"/>
                            <EF:EFOption label="仓库办公室A4" value="30"/>
                            <EF:EFOption label="仓库办公室针打" value="40"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="printerIpAddr" cname="打印机名称" align="center" enable="false"/>
                        <EF:EFComboColumn ename="billPrintType" cname="单据类型" align="center"  required="true">
                            <EF:EFOption label="提单" value="10"/>
                            <EF:EFOption label="出库单" value="20"/>
                        </EF:EFComboColumn>
                    </EF:EFGrid>
                </div>
            </EF:EFRegion>
        </EF:EFWindow>
    </div>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
