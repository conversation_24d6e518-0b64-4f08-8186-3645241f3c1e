<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0501">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equalId">
            EXCEPTION_CONTACT_ID = #equalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="exceptionContactId">
            EXCEPTION_CONTACT_ID like concat('%',#exceptionContactId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckImplemente">
            SPOT_CHECK_IMPLEMENTE = #spotCheckImplemente#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="exceptionSource">
            EXCEPTION_SOURCE = #exceptionSource#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="exceptionStatus">
            EXCEPTION_STATUS = #exceptionStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="exceptionStatus">
            EXCEPTION_STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0501">
        SELECT
        EXCEPTION_CONTACT_ID as "exceptionContactId",  <!-- 异常信息联络单号 -->
        EXCEPTION_SOURCE as "exceptionSource",  <!-- 异常信息来源 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        SPOT_CHECK_CONTENT as "spotCheckContent",  <!-- 点检内容 -->
        SPOT_CHECK_METHOD as "spotCheckMethod",  <!-- 点检方法 -->
        SPOT_CHECK_STANDARD_TYPE as "spotCheckStandardType",  <!-- 点检标准类型 -->
        JUDGMENT_STANDARD as "judgmentStandard",  <!-- 判断标准 -->
        IS_OVERHAUL_HANDLE as "isOverhaulHandle",  <!-- 是否检修处理 -->
        TEMPORARY_MEASURES as "temporaryMeasures",  <!-- 临时措施 -->
        ACTUALS_REMARK as "actualsRemark",  <!-- 点检实绩 -->
        HANDLE_MEASURES as "handleMeasures",  <!-- 处理措施 -->
        PROC_RESULT as "procResult",  <!-- 处理结果 -->
        SPOT_CHECK_IMPLEMENTE as "spotCheckImplemente",  <!-- 点检实施方 -->
        EXCEPTION_STATUS as "exceptionStatus",  <!-- 点检异常息状态 -->
        IS_FAULT_HANDLE as "isFaultHandle",  <!-- 是否故障处理 -->
        MEASURE_ID as "measureId",  <!-- 计量单位 -->
        UPPER_LIMIT as "upperLimit",  <!-- 上限值 -->
        LOWER_LIMIT as "lowerLimit",  <!-- 下限值 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode"  <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0501 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0501 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0501 (EXCEPTION_CONTACT_ID,  <!-- 异常信息联络单号 -->
        EXCEPTION_SOURCE,  <!-- 异常信息来源 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        SPOT_CHECK_CONTENT,  <!-- 点检内容 -->
        SPOT_CHECK_METHOD,  <!-- 点检方法 -->
        SPOT_CHECK_STANDARD_TYPE,  <!-- 点检标准类型 -->
        JUDGMENT_STANDARD,  <!-- 判断标准 -->
        IS_OVERHAUL_HANDLE,  <!-- 是否检修处理 -->
        TEMPORARY_MEASURES,  <!-- 临时措施 -->
        ACTUALS_REMARK,  <!-- 点检实绩 -->
        HANDLE_MEASURES,  <!-- 处理措施 -->
        PROC_RESULT,  <!-- 处理结果 -->
        SPOT_CHECK_IMPLEMENTE,  <!-- 点检实施方 -->
        EXCEPTION_STATUS,  <!-- 点检异常息状态 -->
        IS_FAULT_HANDLE,  <!-- 是否故障处理 -->
        MEASURE_ID,  <!-- 计量单位 -->
        UPPER_LIMIT,  <!-- 上限值 -->
        LOWER_LIMIT,  <!-- 下限值 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#exceptionContactId#, #exceptionSource#, #deviceCode#, #deviceName#, #eArchivesNo#, #equipmentName#,
        #spotCheckContent#, #spotCheckMethod#, #spotCheckStandardType#, #judgmentStandard#, #isOverhaulHandle#,
        #temporaryMeasures#, #actualsRemark#, #handleMeasures#, #procResult#, #spotCheckImplemente#, #exceptionStatus#,
        #isFaultHandle#, #measureId#, #upperLimit#, #lowerLimit#, #uuid#, #recCreator#, #recCreatorName#,
        #recCreateTime#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0501 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0501
        SET
        EXCEPTION_CONTACT_ID = #exceptionContactId#,   <!-- 异常信息联络单号 -->
        EXCEPTION_SOURCE = #exceptionSource#,   <!-- 异常信息来源 -->
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        SPOT_CHECK_CONTENT = #spotCheckContent#,   <!-- 点检内容 -->
        SPOT_CHECK_METHOD = #spotCheckMethod#,   <!-- 点检方法 -->
        SPOT_CHECK_STANDARD_TYPE = #spotCheckStandardType#,   <!-- 点检标准类型 -->
        JUDGMENT_STANDARD = #judgmentStandard#,   <!-- 判断标准 -->
        IS_OVERHAUL_HANDLE = #isOverhaulHandle#,   <!-- 是否检修处理 -->
        TEMPORARY_MEASURES = #temporaryMeasures#,   <!-- 临时措施 -->
        ACTUALS_REMARK = #actualsRemark#,   <!-- 点检实绩 -->
        HANDLE_MEASURES = #handleMeasures#,   <!-- 处理措施 -->
        PROC_RESULT = #procResult#,   <!-- 处理结果 -->
        SPOT_CHECK_IMPLEMENTE = #spotCheckImplemente#,   <!-- 点检实施方 -->
        EXCEPTION_STATUS = #exceptionStatus#,   <!-- 点检异常息状态 -->
        IS_FAULT_HANDLE = #isFaultHandle#,   <!-- 是否故障处理 -->
        MEASURE_ID = #measureId#,   <!-- 计量单位 -->
        UPPER_LIMIT = #upperLimit#,   <!-- 上限值 -->
        LOWER_LIMIT = #lowerLimit#,   <!-- 下限值 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>
