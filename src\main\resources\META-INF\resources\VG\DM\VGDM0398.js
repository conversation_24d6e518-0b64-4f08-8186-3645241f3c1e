$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);

    IPLATUI.EFGrid = {
        "result": {
            // 隐藏分页栏
            pageable: false,
            loadComplete: function (e) {
                // 关闭连接按钮
                $("#CLOSE").on("click", function (e) {
                    const info = new EiInfo();
                    info.setByNode("inqu");
                    IMOMUtil.submitEiInfo(info, "VGDM0398", "closeConnection", function (ei) {
                        NotificationUtil("关闭连接成功！", "success");
                    });
                });
            }
        }
    };
});
