<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-processId" cname="流程ID" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-configName" cname="流程名称" placeholder="模糊条件" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-nodeName" cname="节点名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-apprId" cname="审批人工号" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-apprName" cname="审批人姓名" colWidth="3"/>
        </div>
    </EF:EFRegion>
    <div class="row">
        <div class="col-md-5">
            <EF:EFRegion id="result0" title="审批定义" style="position: relative;">
                <EF:EFGrid blockId="result0" autoBind="false" autoDraw="no" needAuth="true" checkMode="single, cell"
                           isFloat="true" personal="true" sort="all" height="550"
                           serviceName="XTSS12" insertMethod="insert2" updateMethod="update2" deleteMethod="delete2"
                           queryMethod="query">
                    <EF:EFColumn ename="uuid" cname="ID" enable="false" width="235" align="left" hidden="true"/>
                    <EF:EFColumn ename="processId" enable="false" cname="流程ID"/>
                    <EF:EFColumn ename="configName" cname="流程名称" enable="false" required="true" align="left"/>
                    <EF:EFColumn ename="nodeNo" cname="节点号" enable="false" required="true" align="left"/>
                    <EF:EFColumn ename="nodeName" cname="节点名称" enable="false" required="true" align="left"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div class="col-md-7">
            <EF:EFRegion id="result" title="审批配置">
                <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" needAuth="true" isFloat="true"
                           serviceName="XTSS12" insertMethod="insert1" updateMethod="update1" deleteMethod="delete1"
                           queryMethod="queryDetail" personal="true" sort="all">
                    <EF:EFColumn ename="uuid" cname="ID" enable="false" width="235" align="left" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" required="true" enable="false" align="left"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="left" sort="flase"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" primaryKey="true" enable="false" align="left"
                                 hidden="true"/>
                    <EF:EFColumn ename="nodeNo" cname="节点号" required="true" align="left" enable="false"/>
                    <EF:EFColumn ename="nodeName" cname="节点名称" required="true" align="left" enable="false"/>
                    <EF:EFColumn ename="processId" cname="流程名称" hidden="true" required="true" align="left"/>
                    <EF:EFColumn ename="configName" cname="流程名称" hidden="true" required="true" align="left"/>
                    <EF:EFComboColumn ename="protRoleType" cname="角色类型" enable="true" align="center" width="130">
                        <EF:EFOption label=" " value=" "/>
                        <EF:EFOption label="资材" value="1"/>
                        <EF:EFOption label="加工费" value="2"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="apprId" cname="审批人工号" required="true" align="left"/>
                    <EF:EFColumn ename="apprName" cname="审批人姓名" required="true" align="left" enable="false"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" enable="false" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" enable="false" align="center"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" displayType="datetime" editType="datetime"
                                 width="150"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" enable="false"
                                 align="center"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" enable="false" align="center"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" enable="false" align="center"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" displayType="datetime" editType="datetime"
                                 width="150"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" enable="false"
                                 align="center"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </div>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS07" id="userInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS1201" id="processKey" width="90%" height="60%"/>
</EF:EFPage>
