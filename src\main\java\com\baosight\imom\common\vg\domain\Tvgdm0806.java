/**
 * Generate time : 2025-04-24 9:41:11
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Tvgdm0806
 */
public class Tvgdm0806 extends DaoEPBase {

    private String overhaulPlanId = " ";        /* 检修计划编号*/
    @NotNull(message = "序号不能为空")
    private Integer sortIndex = 1;        /* 序号*/
    @NotBlank(message = "数据类型不能为空")
    private String dataType = " ";        /* 数据类型10工器具2资材*/
    private String stuffType = " ";        /* 资材类型*/
    @NotBlank(message = "名称不能为空")
    private String stuffName = " ";        /* 资材名称*/
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer stuffQty = 1;        /* 数量*/
    @NotBlank(message = "单位不能为空")
    private String stuffUnit = " ";        /* 单位*/
    private String specDesc = " ";        /* 规格*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("overhaulPlanId");
        eiColumn.setDescName("检修计划编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortIndex");
        eiColumn.setDescName("序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataType");
        eiColumn.setDescName("数据类型10工器具2资材");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffType");
        eiColumn.setDescName("资材类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffName");
        eiColumn.setDescName("资材名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffQty");
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffUnit");
        eiColumn.setDescName("单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0806() {
        initMetaData();
    }

    /**
     * get the overhaulPlanId - 检修计划编号
     *
     * @return the overhaulPlanId
     */
    public String getOverhaulPlanId() {
        return this.overhaulPlanId;
    }

    /**
     * set the overhaulPlanId - 检修计划编号
     */
    public void setOverhaulPlanId(String overhaulPlanId) {
        this.overhaulPlanId = overhaulPlanId;
    }

    /**
     * get the sortIndex - 序号
     *
     * @return the sortIndex
     */
    public Integer getSortIndex() {
        return this.sortIndex;
    }

    /**
     * set the sortIndex - 序号
     */
    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }

    /**
     * get the dataType - 数据类型10工器具2资材
     *
     * @return the dataType
     */
    public String getDataType() {
        return this.dataType;
    }

    /**
     * set the dataType - 数据类型10工器具2资材
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * get the stuffType - 资材类型
     *
     * @return the stuffType
     */
    public String getStuffType() {
        return this.stuffType;
    }

    /**
     * set the stuffType - 资材类型
     */
    public void setStuffType(String stuffType) {
        this.stuffType = stuffType;
    }

    /**
     * get the stuffName - 资材名称
     *
     * @return the stuffName
     */
    public String getStuffName() {
        return this.stuffName;
    }

    /**
     * set the stuffName - 资材名称
     */
    public void setStuffName(String stuffName) {
        this.stuffName = stuffName;
    }

    /**
     * get the stuffQty - 数量
     *
     * @return the stuffQty
     */
    public Integer getStuffQty() {
        return this.stuffQty;
    }

    /**
     * set the stuffQty - 数量
     */
    public void setStuffQty(Integer stuffQty) {
        this.stuffQty = stuffQty;
    }

    /**
     * get the stuffUnit - 单位
     *
     * @return the stuffUnit
     */
    public String getStuffUnit() {
        return this.stuffUnit;
    }

    /**
     * set the stuffUnit - 单位
     */
    public void setStuffUnit(String stuffUnit) {
        this.stuffUnit = stuffUnit;
    }

    /**
     * get the specDesc - 规格
     *
     * @return the specDesc
     */
    public String getSpecDesc() {
        return this.specDesc;
    }

    /**
     * set the specDesc - 规格
     */
    public void setSpecDesc(String specDesc) {
        this.specDesc = specDesc;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setOverhaulPlanId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulPlanId")), overhaulPlanId));
        setSortIndex(NumberUtils.toInteger(StringUtils.toString(map.get("sortIndex")), sortIndex));
        setDataType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataType")), dataType));
        setStuffType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffType")), stuffType));
        setStuffName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffName")), stuffName));
        setStuffQty(NumberUtils.toInteger(StringUtils.toString(map.get("stuffQty")), stuffQty));
        setStuffUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffUnit")), stuffUnit));
        setSpecDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specDesc")), specDesc));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("overhaulPlanId", StringUtils.toString(overhaulPlanId, eiMetadata.getMeta("overhaulPlanId")));
        map.put("sortIndex", StringUtils.toString(sortIndex, eiMetadata.getMeta("sortIndex")));
        map.put("dataType", StringUtils.toString(dataType, eiMetadata.getMeta("dataType")));
        map.put("stuffType", StringUtils.toString(stuffType, eiMetadata.getMeta("stuffType")));
        map.put("stuffName", StringUtils.toString(stuffName, eiMetadata.getMeta("stuffName")));
        map.put("stuffQty", StringUtils.toString(stuffQty, eiMetadata.getMeta("stuffQty")));
        map.put("stuffUnit", StringUtils.toString(stuffUnit, eiMetadata.getMeta("stuffUnit")));
        map.put("specDesc", StringUtils.toString(specDesc, eiMetadata.getMeta("specDesc")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}