@charset "utf-8";

dl {
    margin: 0;
}

dd {
    margin: 0;
}

dt {
    margin: 0;
}

ul,
li {
    margin: 0;
    padding: 0;
}

body {
    margin: 0;
    padding: 0;
    color: #cccccc;
    font-family: "微软雅黑";
}

#bg {
    width: 100%;
    height: 100vh;
    background-color: #324E73;
}

.top {
    width: 100%;
    height: 11%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
}

.left-section {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.realtime-clock {
    color: #FFF;
    font-size: 24px;
    font-weight: bold;
    margin-top: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    letter-spacing: 2px;
}

.header {
    font-size: 36px;
    color: #FFF;
    text-align: center;
    letter-spacing: 12px;
    padding: 0;
    margin: 0;
}

.chang-arr {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    color: #FFF;
    font-size: 16px;
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
}

.status-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
}

.status-info div {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.completed span {
    color: #75BD42;
    /* 绿色 */
    font-size: 18px;
    font-weight: bold;
}

.remaining span {
    color: #FF0000;
    /* 红色 */
    font-size: 18px;
    font-weight: bold;
}

.status-details {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

.status-details div {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-top: 5px;
}

.status-details i {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 5px;
}

.working {
    color: black;
    padding: 2px 6px;
    font-size: 15px;
    background: #75BD42;
}

.pending {
    color: black;
    padding: 2px 6px;
    font-size: 15px;
    background: #FBD603;
}

/* con */
.con-all {
    width: 100%;
    height: 89%;
    padding: 5px;
    box-sizing: border-box;
}

.con-main {
    width: 100%;
    height: 100%;
}

.con-main {
    display: flex;
}

.list-one {
    height: 100%;
    /*margin: 0 5px 5px 0;*/
    background-color: #033;
}

/*.list-one:nth-child(10),*/
/*.list-one:nth-child(11).list-one:nth-child(13) {*/
/*    margin: 0 5px 0 0;*/
/*}*/

.list-one-title {
    height: 30px;
    background-color: #0070C0;
    color: #FFFFFF;
    font-size: 20px;
    line-height: 30px;
    position: relative;
}

.list-one-title-name {
    text-align: center;
}

.list-one-title-value {
    position: absolute;
    right: 5px;
    top: 0;
}

.list-one-complete {
    color: #75BD42;
}

.list-one-remaining {
    color: #ff0000;
}

.list-one-con {
    height: calc(100% - 30px);
    overflow-y: auto;  /* 改为auto显示滚动条 */
    position: relative;
    display: flex;
    flex-direction: column;

    scrollbar-width: thin;
    scrollbar-color: #0070C0 #033;
}

.list-one-con-wrapper {
    position: absolute;
    width: 100%;
    animation: none;
}

.list-one-con-content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.list-one-con-one {
    width: 98%; /* 改为一行一个，占满整行 */
    margin: 5px 1% 0;
    text-align: center;
    /*background: #FF0000;*/
    border-radius: 10px;
    color: #000000;
    font-size: 16px;
}

.div-working-tag,
.div-pending-tag {
    width: 98%; /* 改为一行一个，占满整行 */
    margin: 5px 1% 0;
    text-align: center;
    border-radius: 10px;
    color: #000000;
    font-size: 16px;
}

.div-working-tag {
    background: #75BD42;
}

.div-pending-tag {
    background: #FBD603;
}

.con-right {
    width: 30%;
    height: 100%;
    transition: opacity 0.3s, transform 0.3s;
}

.con-right-one {
    width: 43%;
    height: 100%;
    padding-right: 5px;
    box-sizing: border-box;
}

.con-right-one-first,
.con-right-two-first {
    width: 100%;

    height: 100%;
    margin: 0 5px 5px 0;
}

.con-right-one-first-title {
    height: 30px;
    background-color: #0070C0;
    color: #FFFFFF;
    font-size: 20px;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /*padding: 0 5px;*/
    /*margin: 0 5px 5px 0;*/

}

.right-one-first-title-name {
    flex: 1;
    text-align: center;
}

.right-one-first-title-value {
    text-align: right;
}

.con-right-one-second {
    width: 100%;
    /*background-color: #033;*/
    height: calc(((100% - 15px)/2) + 5px);
}

.con-right-two {
    display: flex;
    height: 100%;
    /*flex: 1;*/
}

.con-right-two-second {
    width: 100%;
}

/* 超出滚动 */
.all-con,
.all-con-right-two {
    height: calc(100% - 35px);
    overflow: hidden;
    position: relative;
    background-color: #033;
}

@keyframes scroll {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-50%);
    }
}

.all-con-wrapper {
    position: absolute;
    width: 100%;
    overflow: hidden;
}

.all-con-content {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.all-con-one {
    width: 98%;
    margin: 5px 1% 0;
    text-align: center;
    background: #FBD603;
    border-radius: 10px;
    color: #000000;
    font-size: 16px;
}

.con-left {
    margin-right: 5px;
    position: relative;
    width: 70%;
    height: 100%;
    overflow: hidden;
    /*gap: 5px;*/
}

.page-container {
    position: absolute;
    width: 100%;
    height: 100%;
    /*gap: 5px;*/
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.8s ease-in-out;
}

.page-container.active {
    opacity: 1;
    transform: translateY(0);
}

.page-container.incoming {
    transform: translateY(100%);
}

.page-container.outgoing {
    transform: translateY(-100%);
    opacity: 0;
}

/* 新增CSS */
.con-main {
    transition: all 0.3s ease;
}

.layout-collapsed {
    display: none !important;
}

.con-right.layout-expanded {
    display: block;
    flex: 0 0 30% !important;
}

/* 动态宽度调整 */
#con-left {
    transition: width 0.3s ease;
}

.page-container {
    display: grid;
    gap: 8px;
    transition: all 0.3s ease;
}

/* 将网格设置转移到布局类 */
.con-left.layout-9 .page-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: calc((100% - 20px)/3);
}

.con-left.visible-layout-9 .page-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-auto-rows: calc((100% - 20px)/2);
}

.con-left.layout-16 .page-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: calc((100% - 30px)/4);
}

.con-left.visible-layout-16 .page-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-auto-rows: calc((100% - 20px)/4);
}

.con-left.layout-all .page-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 自适应布局 */
    grid-auto-rows: auto;
}
/* layout-all 样式 */
.con-left.layout-all {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 自适应布局 */
    grid-auto-rows: auto; /* 自适应行高 */
    width: 100%;
    gap: 8px;
}

/* 布局按钮样式 - 保持原始外观，只调整定位 */
#layoutBtn {
    position: fixed;
    top: 10px; /* 与HTML内联样式保持一致 */
    right: 20px; /* 与HTML内联样式保持一致 */
    z-index: 1000;
    background: #324E73;
}

#layoutMenu {
    display: none;
    position: fixed;
    top: 45px; /* 调整为45px，确保在按钮下方且有合适的间距 */
    right: 20px;
    background: #324E73;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    z-index: 999999; /* 设置为最高的z-index值，确保在最顶层 */
    color: #FFF;
    font-size: 14px;
    min-width: 120px;
    transform: none; /* 确保没有transform影响 */
    opacity: 1; /* 确保不透明 */
    pointer-events: auto; /* 确保可以接收鼠标事件 */
}

#layoutMenu div {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s ease;
    white-space: nowrap;
}

#layoutMenu div:hover {
    background-color: rgba(255, 255, 255, 0.1);
}