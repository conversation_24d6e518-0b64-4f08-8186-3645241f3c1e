package com.baosight.imom.li.ds.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.li.ds.domain.LIDS0102;
import com.baosight.imom.li.ds.domain.LIDS0201;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 线边物流提供IMC相关接口
 *
 * <AUTHOR> 徐攀
 * @Description :对外相关接口
 * @Date : 2024/12/9
 * @Version : 1.0
 */
public class ServiceLIDSInterfacesImc extends ServiceBase {
    private Gson gson = new Gson();
    /***
     * (共享服务)
     * 查询MES厂区信息
     * @SharedServiceId S_LI_DS_0001
     * @param inInfo
     * @return
     */
    public EiInfo queryFactoryAreaList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //查询条件
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("传入业务单元代码为空!");
                return outInfo;
            }
            String factoryArea = inInfo.getString("factoryArea");
            String factoryAreaName = inInfo.getString("factoryAreaName");
            //分页参数
            int limit = inInfo.getInt("limit") == 0 ? 10 : inInfo.getInt("limit");
            int offset = inInfo.getInt("offset");

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("factoryArea", factoryArea);
            queryMap.put("factoryAreaName", factoryAreaName);
            queryMap.put("status", "20");
            queryMap.put("offset", offset);
            queryMap.put("limit", limit);
            List<HashMap> lids0101List= dao.query(LIDS0102.QUERY_TO_MAP, queryMap,offset,limit);
            //打印日志到elk
            log(DateUtil.getTimeNow(new Date()) + "：" + "厂区信息查询信息传入参数：" + gson.toJson(queryMap) + "\n" + "厂区信息返回的参数：" + gson.toJson(lids0101List));
            //输出到应用日志
            System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "厂区信息查询信息传入参数：" + gson.toJson(queryMap) + "\n" + "厂区信息返回的参数：" + gson.toJson(lids0101List));

            outInfo.addBlock(EiConstant.resultBlock).addRows(lids0101List);
            if(CollectionUtils.isNotEmpty(lids0101List)){
                outInfo.setMsg("查询成功！");
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }else{
                outInfo.setMsg("查询无数据！");
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }


    /***
     * (共享服务)
     * 查询MES跨区信息
     * @SharedServiceId S_LI_DS_0002
     * @param inInfo
     * @return
     */
    public EiInfo queryCrossAreaList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //查询条件
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("传入业务单元代码为空!");
                return outInfo;
            }

            String factoryArea = inInfo.getString("factoryArea");
            if(StringUtils.isBlank(factoryArea)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("传入厂区代码为空!");
                return outInfo;
            }
            String factoryAreaName = inInfo.getString("factoryAreaName");


            String crossArea = inInfo.getString("crossArea");
            String crossAreaName = inInfo.getString("crossAreaName");
            //分页参数
            int limit = inInfo.getInt("limit") == 0 ? 10 : inInfo.getInt("limit");
            int offset = inInfo.getInt("offset");

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("factoryBuilding", factoryArea);
            queryMap.put("factoryBuildingName", factoryAreaName);
            queryMap.put("crossArea", crossArea);
            queryMap.put("crossAreaName", crossAreaName);
            queryMap.put("status", "20");
            queryMap.put("offset", offset);
            queryMap.put("limit", limit);
            List<HashMap> lids0201List= dao.query(LIDS0201.QUERY_TO_MAP, queryMap,offset,limit);
            //打印日志到elk
            log(DateUtil.getTimeNow(new Date()) + "：" + "跨区信息查询传入参数：" + gson.toJson(queryMap) + "\n" + "跨区信息查询返回的参数：" + gson.toJson(lids0201List));
            //输出到应用日志
            System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "跨区信息查询传入参数：" + gson.toJson(queryMap) + "\n" + "跨区信息查询返回的参数：" + gson.toJson(lids0201List));

            outInfo.addBlock(EiConstant.resultBlock).addRows(lids0201List);
            if(CollectionUtils.isNotEmpty(lids0201List)){
                outInfo.setMsg("查询成功！");
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }else{
                outInfo.setMsg("查询无数据！");
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }


    /**
     * 接收IMC传入的捆包下发行车工信息
     *
     * @ServiceId S_LI_DS_0005
     * @param inInfo
     * @return
     */
    public EiInfo receiveIssueCraneInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //传入信息
            List<HashMap> receiveList = (List<HashMap>) inInfo.get("messageList");

            //打印日志
            log("调用receiveIssueCraneInfo传入信息：" + receiveList);
            System.out.println("调用receiveIssueCraneInfo传入信息：" + receiveList);
            //批量修改下发行车工标记
            int count = dao.updateBatch(VIPM0008.UPDATE_ISSUE_CRANE_FLAG, receiveList);
            if (count != receiveList.size()) {
                throw new RuntimeException("修改下发行车工标记失败![传入" + receiveList.size() + "条数据与修改数据条数" + count + "不一致]");
            }
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("接收成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * IMOM吊运捆包确认时，返回结果至IMC
     *
     * @param inInfo
     * @return
     * @ServiceId
     */
    public EiInfo confirmIssueCraneInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //传入捆包
            List<HashMap> demandMaterailList = (List<HashMap>) inInfo.get("demandMaterailList");
            //打印日志
            log("调用confirmIssueCraneInfo传入信息：" + demandMaterailList);
            System.out.println("调用confirmIssueCraneInfo传入信息：" + demandMaterailList);


            EiInfo sendInfo = new EiInfo();
            sendInfo.set("messageList", demandMaterailList);
            sendInfo.set(EiConstant.serviceId, "S_VI_PM_0014");
            sendInfo =  EServiceManager.call(sendInfo, TokenUtils.getXplatToken());
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException("确认结果返回IMC失败：" + sendInfo.getMsg());
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}
