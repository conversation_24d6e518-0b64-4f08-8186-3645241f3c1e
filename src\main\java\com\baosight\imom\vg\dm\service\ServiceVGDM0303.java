package com.baosight.imom.vg.dm.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.hdsdk.exception.HDSdkException;
import org.apache.commons.collections.CollectionUtils;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.DateUtils;
import com.baosight.imom.common.utils.IhdSdkUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.VGDM0301;
import com.baosight.imom.vg.dm.domain.VGDM0303;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * <AUTHOR> yzj
 * @Description : 采集点位监控清单页面后台
 * @Date : 2024/11/21
 * @Version : 1.0
 */
public class ServiceVGDM0303 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0303.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0303().eiMetadata);
        // 业务单元
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0303.QUERY, new VGDM0303());
    }

    /**
     * 点位转换
     * <p>
     * 从TVGDM0301表数据转换到TVGDM0303表中
     * 前端页面为VGDM03页面
     */
    public EiInfo convert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0301 vgdm0301;
            VGDM0303 vgdm0303;
            List<Map> insList = new ArrayList<>();
            List<String> uuidList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                // 查询点位
                String tagId = block.getCellStr(i, "tagId");
                vgdm0301 = VGDM0301.queryWithCache(dao, tagId);
                if (vgdm0301 == null) {
                    throw new PlatException("点位不存在");
                }
                if (vgdm0301.getTagIhdId() == null
                        || vgdm0301.getTagIhdId() == 0) {
                    throw new PlatException("点位ID不能为空，请先执行IHD点位同步");
                }
                // 去重
                if (uuidList.contains(vgdm0301.getUuid())) {
                    continue;
                }
                uuidList.add(vgdm0301.getUuid());
                // 转换
                vgdm0303 = new VGDM0303();
                vgdm0303.fromMap(vgdm0301.toMap());
                // 校验数据是否已存在
                checkExist(vgdm0303);
                // 添加到插入列表
                Map insMap = vgdm0303.toMap();
                RecordUtils.setCreator(insMap);
                insList.add(insMap);
                // 返回前端
                block.getRows().set(i, vgdm0301.toMap());
            }
            // 批量插入
            DaoUtils.insertBatch(dao, VGDM0303.INSERT, insList);
            // 设置返回信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 校验数据是否已存在
     */
    private void checkExist(VGDM0303 vgdm0303) {
        Map<String, String> map = new HashMap<>();
        map.put("equalId", vgdm0303.getTagId());
        map.put("segNo", vgdm0303.getSegNo());
        map.put("unitCode", vgdm0303.getUnitCode());
        map.put("scadaName", vgdm0303.getScadaName());
        int count = super.count(VGDM0303.COUNT, map);
        if (count > 0) {
            throw new PlatException("数据已存在,不能重复转换");
        }
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0303 vgdm0303;
            VGDM0303 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0303 = new VGDM0303();
                vgdm0303.fromMap(block.getRow(i));
                // 校验数据库数据存在
                dbData = checkData(vgdm0303);
                // 校验上限值必须大于下限值
                if (vgdm0303.getUpperLimit().compareTo(vgdm0303.getLowerLimit()) < 0) {
                    throw new PlatException("上限值必须大于下限值");
                }
                dbData.setMeasureId(vgdm0303.getMeasureId());
                dbData.setUpperLimit(vgdm0303.getUpperLimit());
                dbData.setLowerLimit(vgdm0303.getLowerLimit());
                dbData.setBigScreenFlag(vgdm0303.getBigScreenFlag());
                dbData.setBigScreenKeyFlag(vgdm0303.getBigScreenKeyFlag());
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0303.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 检查数据
     */
    private VGDM0303 checkData(VGDM0303 vgdm0303) {
        if (StrUtil.isBlank(vgdm0303.getUuid())) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
        }
        Map<String, String> map = new HashMap<>();
        map.put("uuid", vgdm0303.getUuid());
        List<VGDM0303> list = this.dao.query(VGDM0303.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
        }
        return list.get(0);
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0303 vgdm0303;
            VGDM0303 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0303 = new VGDM0303();
                vgdm0303.fromMap(block.getRow(i));
                // 校验数据库数据存在
                dbData = checkData(vgdm0303);
                // 设置删除标志
                dbData.setDelFlag("1");
                Map delMap = dbData.toMap();
                RecordUtils.setRevisor(delMap);
                block.getRows().set(i, delMap);
            }
            DaoUtils.updateBatch(dao, VGDM0303.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 查询数据
     */
    public EiInfo queryData(EiInfo inInfo) {
        try {
            String queryType = inInfo.getString("queryType");
            List<String> tagIhdIdList = (List<String>) inInfo.get("tagId");
            if (CollectionUtils.isEmpty(tagIhdIdList)) {
                throw new PlatException("点位不能为空");
            }
            if ("1".equals(queryType)) {
                // 实时数据
                return queryRealTime(inInfo, tagIhdIdList);
            } else if ("2".equals(queryType)) {
                return queryHistory(inInfo, tagIhdIdList);
            } else {
                throw new PlatException("查询类型错误");
            }
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 查询历史数据
     */
    private EiInfo queryHistory(EiInfo inInfo, List<String> tagIhdIdList) throws HDSdkException {
        // 历史数据
        String startTime = inInfo.getCellStr(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "startTime");
        String endTime = inInfo.getCellStr(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "endTime");
        if (StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) {
            throw new PlatException("采集时间不能为空");
        }
        LocalDateTime start = LocalDateTime.parse(startTime, DateUtils.FORMATTER_19);
        LocalDateTime end = LocalDateTime.parse(endTime, DateUtils.FORMATTER_19);
        // 时间范围不能超过8小时
        if (end.isBefore(start) || end.isAfter(start.plusHours(8))) {
            throw new PlatException("时间范围不能超过8小时");
        }
        Map<String, List<Map<String, Object>>> resultMap = IhdSdkUtils.queryHisValue(inInfo.getString("segNo"), tagIhdIdList, Date.from(start.atZone(ZoneId.systemDefault()).toInstant()), Date.from(end.atZone(ZoneId.systemDefault()).toInstant()));

        inInfo.set("resultMap", resultMap);
        return inInfo;
    }

    /**
     * 查询实时数据
     *
     * <p>
     * 模拟生成实时数据
     * 每次根据上次查询时间作为开始时间，当前时间作为结束时间，查询数据
     */
    private EiInfo queryRealTime(EiInfo inInfo, List<String> tagIhdIdList) throws HDSdkException {
        String lastQueryTime = inInfo.getString("lastQueryTime");
        // 结束时间-当前时间
        String endTime = LocalDateTime.now().format(DateUtils.FORMATTER_19);
        // 开始时间-有上次查询时间则取上次查询时间，否则取当前时间-5秒
        String startTime = StrUtil.isBlank(lastQueryTime) ?
                LocalDateTime.now().minusSeconds(5).format(DateUtils.FORMATTER_19) : lastQueryTime;
        LocalDateTime start = LocalDateTime.parse(startTime, DateUtils.FORMATTER_19);
        // 转换为Date
        Date startDate = Date.from(start.atZone(ZoneId.systemDefault()).toInstant());
        // 查询从开始时间到现在的数据
        Map<String, List<Map<String, Object>>> resultMap =
                IhdSdkUtils.queryHisValue(inInfo.getString("segNo"), tagIhdIdList, startDate, null);
        // 返回数据        
        inInfo.set("resultMap", resultMap);
        // 更新上次查询时间
        inInfo.set("lastQueryTime", endTime);
        return inInfo;
    }
}
