<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>重庆宝钢发货看板</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <style>
        @charset "utf-8";

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            color: #cccccc;
            font-family: "微软雅黑";
            background-color: #324E73;
            height: 100vh;
            overflow: hidden;
        }

        /* 顶部区域 */
        .top {
            width: 100%;
            height: 11%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
            z-index: 2000; 
        }

        .left-section {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
        }

        .realtime-clock {
            color: #FFF;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            letter-spacing: 2px;
        }

        .header {
            font-size: 36px;
            color: #FFF;
            text-align: center;
            letter-spacing: 12px;
            margin: 0;
        }

        .right-section {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .status-details {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }

        .status-details div {
            display: flex;
            align-items: center;
        }

        .status-details i {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 5px;
        }

        #layoutBtn {
            background: #0070C0;
            color: #FFF;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }

        #layoutMenu {
            display: none;
            position: absolute;
            top: 35px;
            right: 0;
            background: #324E73;
            border: 1px solid #0070C0;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 1000;
            min-width: 120px;
        }

        #layoutMenu div {
            padding: 10px 15px;
            cursor: pointer;
            color: #FFF;
            font-size: 14px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        #layoutMenu div:last-child {
            border-bottom: none;
        }

        #layoutMenu div:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        #layoutMenu div.selected {
            background-color: #0056b3;
            color: #fff;
            font-weight: bold;
        }



        /* 主内容区域 */
        .main-content {
            width: 100%;
            height: 89%;
            display: flex;
        }

        /* 左侧装卸点区域 */
        .loading-area {
            width: 60%;
            height: 100%;
            padding: 5px;
            position: relative;
            overflow: hidden;
        }

        .loading-area.layout-9 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 8px;
        }

        .loading-area.layout-16 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 8px;
        }

        .loading-area.layout-all {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-auto-rows: auto;
            gap: 8px;
        }

        .loading-area.hidden {
            display: none !important;
        }

        .page-container {
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.8s ease-in-out;
            display: grid;
            gap: 8px;
        }

        /* 分页模式下的网格布局 */
        .loading-area.layout-9 .page-container {
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 5px;
        }

        .loading-area.layout-16 .page-container {
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 5px;
        }

        .page-container.active {
            opacity: 1;
            transform: translateY(0);
        }

        .door-item {
            background-color: #033;
            border-radius: 8px;
            overflow: hidden;
        }

        /* 分页模式下的装卸点样式 */
        .loading-area.layout-9 .door-item,
        .loading-area.layout-16 .door-item {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .loading-area.layout-9 .door-content,
        .loading-area.layout-16 .door-content {
            flex: 1;
            height: auto;
        }

        .door-title {
            height: 30px;
            background-color: #0070C0;
            color: #FFFFFF;
            font-size: 16px;
            line-height: 30px;
            text-align: center;
        }

        .door-content {
            padding: 5px;
            height: calc(100% - 30px);
            overflow-y: auto;
            /* 滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #0070C0 #033;
        }

        .door-content::-webkit-scrollbar {
            width: 6px;
        }

        .door-content::-webkit-scrollbar-track {
            background: #033;
        }

        .door-content::-webkit-scrollbar-thumb {
            background: #0070C0;
            border-radius: 3px;
        }

        .door-content::-webkit-scrollbar-thumb:hover {
            background: #005a9e;
        }

        .vehicle-item {
            background: #FBD603;
            color: #000;
            margin: 3px 0;
            padding: 5px;
            border-radius: 5px;
            font-size: 12px;
            text-align: center;
        }

        .vehicle-item.working {
            background: #75BD42;
        }

        /* 车辆进度条样式 */
        .vehicle-progress {
            margin-top: 5px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .vehicle-progress-bar {
            height: 100%;
            background: #0070C0;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .vehicle-progress-text {
            font-size: 10px;
            margin-top: 2px;
            color: #333;
        }

        .vehicle-item.working .vehicle-progress-text {
            color: #fff;
        }

        /* 发货列表面板 */
        .delivery-panel {
            width: 40%;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 5px;
            transition: width 0.3s ease;
        }

        .delivery-panel.hidden {
            display: none !important;
        }

        .delivery-panel.fullscreen {
            width: 100%;
        }

        .delivery-row {
            flex: 1;
            background-color: #033;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .delivery-row-title {
            height: 40px;
            color: #FFFFFF;
            font-size: 18px;
            line-height: 40px;
            text-align: center;
            font-weight: bold;
        }

        .delivery-row-title.call-title {
            background-color: #0070C0;
        }

        .delivery-row-title.unplanned-title {
            background-color: #0070C0;
        }

        .delivery-row-title.urgent-title {
            background-color: #ff4444;
        }

        .delivery-row-title.emergency-title {
            background-color: #ff8800;
        }

        .delivery-row-title.warning-title {
            background-color: #ffcc00;
            color: #000;
        }

        .delivery-row-content {
            flex: 1;
            padding: 5px;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 5px;
            align-content: start;
            /* 滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #0070C0 #033;
        }

        .delivery-row-content::-webkit-scrollbar {
            width: 6px;
        }

        .delivery-row-content::-webkit-scrollbar-track {
            background: #033;
        }

        .delivery-row-content::-webkit-scrollbar-thumb {
            background: #0070C0;
            border-radius: 3px;
        }

        .delivery-row-content::-webkit-scrollbar-thumb:hover {
            background: #005a9e;
        }

        .delivery-item {
            background: #FBD603;
            color: #000;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
            height: 110px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            overflow: hidden;
            word-wrap: break-word;
            box-sizing: border-box;
        }

        .delivery-item .item-line {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 1px 0;
            font-size: 11px;
            line-height: 1.1;
        }

        .delivery-item .customer-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 11px;
            margin: 1px 0;
            font-weight: bold;
        }

        .delivery-item.urgent {
            background: #ff4444;
            color: #fff;
        }

        .delivery-item.emergency {
            background: #ff8800;
            color: #fff;
        }

        .delivery-item.warning {
            background: #ffcc00;
            color: #000;
        }

        /* 叫号和无计划车辆的简化样式 */
        .simple-delivery-item {
            background: #FBD603;
            color: #000;
            padding: 4px 6px;
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
            height: 60px; /* 较小的高度，适合显示2-3个字段 */
            display: flex;
            flex-direction: column;
            justify-content: center; /* 居中对齐 */
            overflow: hidden;
            word-wrap: break-word;
            box-sizing: border-box;
            margin: 2px;
        }

        .simple-delivery-item .item-line {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 2px 0;
            font-size: 11px;
            line-height: 1.1;
        }

        /* 强制隐藏类 */
        .force-hidden {
            display: none !important;
        }

        /* 合并行样式 - 覆盖原有的 flex-direction: column */
        .delivery-row.combined-row {
            display: flex !important;
            flex-direction: row !important;
            gap: 5px;
            background-color: transparent !important;
        }

        .combined-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #033;
            border-radius: 8px;
            overflow: hidden;
        }

        .combined-column .delivery-row-title {
            height: 40px;
            color: #FFFFFF;
            font-size: 18px;
            line-height: 40px;
            text-align: center;
            font-weight: bold;
        }

        .combined-column .delivery-row-content {
            flex: 1;
            padding: 5px;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 5px;
            align-content: start;
            /* 滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #0070C0 #033;
        }

        .combined-column .delivery-row-content::-webkit-scrollbar {
            width: 6px;
        }

        .combined-column .delivery-row-content::-webkit-scrollbar-track {
            background: #033;
        }

        .combined-column .delivery-row-content::-webkit-scrollbar-thumb {
            background: #0070C0;
            border-radius: 3px;
        }

        .combined-column .delivery-row-content::-webkit-scrollbar-thumb:hover {
            background: #005a9e;
        }


    </style>
</head>

<body>
        <div class="top">
            <div class="left-section">
                <div id="realtime-clock" class="realtime-clock"></div>
            </div>
        
            <h1 class="header">数智物流厂内装卸货仓库作业看板</h1>

        <div class="right-section">
                <div class="status-details">
                    <div>
                        <i style="background: #75BD42;"></i>
                        正在作业
                    </div>
                    <div>
                        <i style="background: #FBD603;"></i>
                        等待作业
                </div>
            </div>
            <button id="layoutBtn">布局</button>
            <div id="layoutMenu">
                <div data-layout="9">每页9个</div>
                <div data-layout="16">每页16个</div>
                <div data-layout="all">全部显示</div>
                <div data-layout="hideRight" id="hideRight">隐藏右侧列表</div>
                <div data-layout="vehicleTracking">车辆跟踪</div>
            </div>
            </div>
        </div>
        
    <div class="main-content">
        <!-- 左侧装卸点区域 -->
        <div class="loading-area" id="loadingArea"></div>

        <!-- 右侧发货列表面板 -->
        <div class="delivery-panel" id="deliveryPanel">
            <!-- 叫号和无计划车辆合并行 -->
            <div class="delivery-row combined-row">
                <div class="combined-column">
                    <div class="delivery-row-title call-title">叫号</div>
                    <div class="delivery-row-content" id="deliveryCallList"></div>
                </div>
                <div class="combined-column">
                    <div class="delivery-row-title unplanned-title">无计划车辆</div>
                    <div class="delivery-row-content" id="deliveryUnplannedList"></div>
                </div>
            </div>

            <!-- 特急行 -->
            <div class="delivery-row">
                <div class="delivery-row-title urgent-title">特急 (<span id="urgentCount">0</span>)</div>
                <div class="delivery-row-content" id="deliveryUrgentList"></div>
                                    </div>

            <!-- 紧急行 -->
            <div class="delivery-row">
                <div class="delivery-row-title emergency-title">紧急 (<span id="emergencyCount">0</span>)</div>
                <div class="delivery-row-content" id="deliveryEmergencyList"></div>
                                </div>

            <!-- 预警行 -->
            <div class="delivery-row">
                <div class="delivery-row-title warning-title">预警 (<span id="warningCount">0</span>)</div>
                <div class="delivery-row-content" id="deliveryWarningList"></div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="./../js/config.js"></script>
    <script>
        $(document).ready(function () {
            // 应用状态
            const AppState = {
                rawData: null,
                refreshInterval: null,
                currentLayout: 'all',
                currentPage: 1,
                totalPages: 1,
                itemsPerPage: 9,
                rightPanelVisible: true,
                vehicleTrackingMode: false,
                handPointsList: [],
                autoScrollInterval: null
            };

            // 初始化应用
            function initApp() {
                initClock();
                initLayout();
                loadData();
                startAutoRefresh();
            }

            // 初始化时钟
            function initClock() {
                function updateClock() {
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const hours = String(now.getHours()).padStart(2, '0');
                    const minutes = String(now.getMinutes()).padStart(2, '0');
                    const seconds = String(now.getSeconds()).padStart(2, '0');
                    
                    const timeString = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
                    $('#realtime-clock').text(timeString);
                }
                updateClock();
                setInterval(updateClock, 1000);
            }

            // 初始化布局控制
            function initLayout() {
                // 布局按钮点击
                $('#layoutBtn').on('click', function(e) {
                    $('#layoutMenu').toggle();
                    e.stopPropagation();
                });

                // 菜单选项点击
                $('#layoutMenu div').on('click', function() {
                    const layout = $(this).data('layout');
                    handleLayoutChange(layout);
                    $('#layoutMenu').hide();
                });

                // 点击外部关闭菜单
                $(document).on('click', function() {
                    $('#layoutMenu').hide();
                });
            }

                         // 处理布局切换
             function handleLayoutChange(layout) {
                 if (layout === 'hideRight') {
                     AppState.rightPanelVisible = !AppState.rightPanelVisible;
                     AppState.vehicleTrackingMode = false;
                     $('#hideRight').text(AppState.rightPanelVisible ? '隐藏右侧列表' : '显示右侧列表');
                     
                     // 退出车辆跟踪模式时，移除强制隐藏类
                     $('.combined-row').removeClass('force-hidden');
                 } else if (layout === 'vehicleTracking') {
                     AppState.vehicleTrackingMode = !AppState.vehicleTrackingMode;
                     AppState.rightPanelVisible = true;
                     
                     // 立即应用车辆跟踪模式的隐藏逻辑
                     if (AppState.vehicleTrackingMode) {
                         $('.combined-row').addClass('force-hidden');
                     } else {
                         $('.combined-row').removeClass('force-hidden');
                     }
                 } else {
                     // 布局选项（9、16、all）只影响装卸点显示，不改变右侧面板状态
                     AppState.currentLayout = layout;
                     AppState.vehicleTrackingMode = false;
                     AppState.itemsPerPage = layout === 'all' ? Infinity : parseInt(layout);
                     AppState.currentPage = 1;
                     
                     // 切换到普通布局时，确保移除强制隐藏类
                     $('.combined-row').removeClass('force-hidden');
                     // 移除了 AppState.rightPanelVisible = true; 保持用户的选择
                 }
                 
                 updateLayoutMenuState();
                 applyLayout();
                 if (AppState.rawData) {
                     updateLoadingArea();
                 }
             }

            // 更新布局菜单状态
            function updateLayoutMenuState() {
                // 清除所有选中状态
                $('#layoutMenu div').removeClass('selected');
                
                // 设置当前选中项
                if (AppState.vehicleTrackingMode) {
                    $('[data-layout="vehicleTracking"]').addClass('selected');
                } else if (!AppState.rightPanelVisible) {
                    $('#hideRight').addClass('selected');
                } else {
                    $('[data-layout="' + AppState.currentLayout + '"]').addClass('selected');
                }
            }

            // 应用布局设置
            function applyLayout() {
                const $loadingArea = $('#loadingArea');
                const $deliveryPanel = $('#deliveryPanel');

                // 清除旧样式
                $loadingArea.removeClass('layout-9 layout-16 layout-all hidden');
                $deliveryPanel.removeClass('hidden fullscreen');

                if (AppState.vehicleTrackingMode) {
                    // 车辆跟踪模式：只显示发货面板中的特急、紧急、预警
                    $loadingArea.addClass('hidden');
                    $deliveryPanel.addClass('fullscreen');
                } else {
                    // 普通模式
                    if (AppState.rightPanelVisible) {
                        $deliveryPanel.removeClass('hidden');
                        $loadingArea.css('width', '60%');
                    } else {
                        $deliveryPanel.addClass('hidden');
                        $loadingArea.css('width', '100%');
                    }

                    // 设置装卸点布局
                    if (AppState.currentLayout !== 'all') {
                        $loadingArea.addClass('layout-' + AppState.currentLayout);
                    } else {
                        $loadingArea.addClass('layout-all');
                    }
                }
            }

            // 加载数据
            function loadData() {
                const searchObj = new URLSearchParams(window.location.search);
                const queryData = {
                    segNo: searchObj.get('segNo') || 'JC000000',
                    serviceId: 'S_LI_RL_0138'
                };

                $.ajax({
                    type: "post",
                    contentType: "application/json",
                    url: ytjServerUrl,
                    data: JSON.stringify(queryData),
                    success: function(data) {
                        if (data && data.__sys__ && data.__sys__.status !== -1) {
                            AppState.rawData = data;
                            AppState.handPointsList = data.handPointIdList || [];
                            updateAllViews();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('数据加载失败:', error);
                    }
                });
            }

            // 更新所有视图
            function updateAllViews() {
                updateLayoutMenuState();
                applyLayout();
                updateLoadingArea();
                
                // 只调用一次updateDeliveryPanel，避免重复调用
                if (AppState.rightPanelVisible || AppState.vehicleTrackingMode) {
                    updateDeliveryPanel();
                }
            }

            // 更新左侧装卸点区域
            function updateLoadingArea() {
                if (!AppState.rawData || AppState.vehicleTrackingMode) return;

                const handPointList = AppState.handPointsList;
                const vehicleList = AppState.rawData.list || [];

                if (AppState.currentLayout === 'all') {
                    renderAllHandPoints(handPointList, vehicleList);
                    stopAutoScroll(); // 全部显示时停止自动滚动
                } else {
                    // 计算分页
                    AppState.totalPages = Math.ceil(handPointList.length / AppState.itemsPerPage);
                    renderCurrentPage(handPointList, vehicleList);
                    startAutoScroll(); // 分页模式才启动自动滚动
                }
            }

            // 渲染所有装卸点
            function renderAllHandPoints(handPointList, vehicleList) {
                let loadingHtml = '';
                handPointList.forEach(function(handPoint) {
                    loadingHtml += generateHandPointHtml(handPoint, vehicleList);
                });
                $('#loadingArea').html(loadingHtml);
            }

            // 渲染当前页
            function renderCurrentPage(handPointList, vehicleList) {
                const startIndex = (AppState.currentPage - 1) * AppState.itemsPerPage;
                const endIndex = startIndex + AppState.itemsPerPage;
                const currentPageData = handPointList.slice(startIndex, endIndex);

                let loadingHtml = '';
                currentPageData.forEach(function(handPoint) {
                    loadingHtml += generateHandPointHtml(handPoint, vehicleList);
                });

                // 创建页面容器
                const $container = $('#loadingArea');
                $container.empty();
                const $newPage = $('<div class="page-container active"></div>').html(loadingHtml);
                $container.append($newPage);
            }

            // 生成装卸点HTML
            function generateHandPointHtml(handPoint, vehicleList) {
                const vehicles = vehicleList.filter(v => v.handPointId === handPoint.handPointId);
                
                let vehicleHtml = '';
                vehicles.forEach(function(vehicle) {
                    const isWorking = vehicle.status == 20;
                    const vehicleNo = vehicle.hashMap?.vehicleNo || '';
                    const waitingTime = vehicle.hashMap?.waitingTime || 0;
                    const businessType = vehicle.hashMap?.businessType || '';
                    
                    // 获取捆包进度数据
                    const packCountAll = vehicle.hashMap?.packCountAll || 0;
                    const packCountRemin = vehicle.hashMap?.packCountRemin || 0; // 已扫描的捆包数
                    // <div class="vehicle-progress-text">${packCountRemin}/${packCountAll}</div>
                    let progressHtml = '';
                    if (isWorking && packCountAll > 0) {
                        const progressPercentage = Math.min((packCountRemin / packCountAll) * 100, 100);
                        progressHtml = `
                            <div class="vehicle-progress">
                                <div class="vehicle-progress-bar" style="width: ${progressPercentage}%"></div>
                            </div>
                        `;
                    }
                    
                    vehicleHtml += `<div class="vehicle-item ${isWorking ? 'working' : ''}">
                        ${vehicleNo}-${waitingTime}分<br>
                        ${businessType}
                        ${progressHtml}
                    </div>`;
                });

                return `<div class="door-item">
                    <div class="door-title">${handPoint.handPointName}</div>
                    <div class="door-content">${vehicleHtml}</div>
                </div>`;
            }

            // 启动自动滚动
            function startAutoScroll() {
                stopAutoScroll();
                
                if (AppState.currentLayout !== 'all' && AppState.totalPages > 1) {
                    AppState.autoScrollInterval = setInterval(function() {
                        if (AppState.currentPage >= AppState.totalPages) {
                            AppState.currentPage = 1;
                            loadData(); // 重新加载数据
                        } else {
                            AppState.currentPage++;
                            renderCurrentPage(AppState.handPointsList, AppState.rawData.list || []);
                        }
                    }, 5000); // 5秒切换一页
                }
            }

            // 停止自动滚动
            function stopAutoScroll() {
                if (AppState.autoScrollInterval) {
                    clearInterval(AppState.autoScrollInterval);
                    AppState.autoScrollInterval = null;
                }
            }

            // 更新右侧发货面板
            function updateDeliveryPanel() {
                if (!AppState.rawData) return;

                if (AppState.vehicleTrackingMode) {
                    // 车辆跟踪模式：强制隐藏叫号和无计划车辆，只显示特急、紧急、预警
                    $('.combined-row').addClass('force-hidden');
                    $('#deliveryUrgentList').closest('.delivery-row').removeClass('force-hidden');
                    $('#deliveryEmergencyList').closest('.delivery-row').removeClass('force-hidden');
                    $('#deliveryWarningList').closest('.delivery-row').removeClass('force-hidden');
                    
                    // 只更新发货数据（特急、紧急、预警）
                    updateDeliveryData();
                } else {
                    // 普通模式：显示所有数据，移除强制隐藏类
                    $('.delivery-row').removeClass('force-hidden').show();
                    
                    // 更新叫号列表
                    updateDeliveryCallList();
                    
                    // 更新无计划车辆列表
                    updateDeliveryUnplannedList();
                    
                    // 更新发货数据
                    updateDeliveryData();
                }
            }

            // 更新发货面板中的叫号列表
            function updateDeliveryCallList() {
                const callData = AppState.rawData.listCallNum || [];
                let html = '';
                
                callData.forEach(function(item) {
                    html += `<div class="simple-delivery-item">
                        <div class="item-line">${item.vehicleNo}</div>
                        <div class="item-line">${item.targetHandPointName}</div>
                        <div class="item-line">${item.waitingTime}分</div>
                    </div>`;
                });

                $('#deliveryCallList').html(html);
            }

            // 更新发货面板中的无计划车辆列表
            function updateDeliveryUnplannedList() {
                const unplannedData = AppState.rawData.allocVehicleList || [];
                let html = '';
                
                unplannedData.forEach(function(item) {
                    html += `<div class="simple-delivery-item">
                        <div class="item-line">${item.vehicleNo}</div>
                        <div class="item-line">${item.waitingTime}分</div>
                    </div>`;
                });

                $('#deliveryUnplannedList').html(html);
            }

            // 更新发货数据
            function updateDeliveryData() {
                const deliveryList = AppState.rawData.deliveryList || [];
                
                // 按状态分组
                const groupedData = {
                    '30': [], // 特急
                    '20': [], // 紧急
                    '10': []  // 预警
                };
                
                deliveryList.forEach(function(item) {
                    const status = String(item.deliveryStatus);
                    if (groupedData[status]) {
                        groupedData[status].push(item);
                    }
                });

                // 对每组数据按截止交付剩余时间升序排列
                function sortByRemainingTime(items) {
                    return items.sort(function(a, b) {
                        const timeA = a.remainingTimeDisplay || '';
                        const timeB = b.remainingTimeDisplay || '';
                        
                        // 解析时间格式：-3590:-26 (小时:分钟)
                        function parseTime(timeStr) {
                            if (!timeStr || timeStr === '') return 0;
                            const parts = timeStr.split(':');
                            if (parts.length !== 2) return 0;
                            
                            const hours = parseInt(parts[0]) || 0;
                            const minutes = parseInt(parts[1]) || 0;
                            
                            // 转换为总分钟数进行比较
                            return hours * 60 + minutes;
                        }
                        
                        const minutesA = parseTime(timeA);
                        const minutesB = parseTime(timeB);
                        
                        return minutesA - minutesB; // 升序排列
                    });
                }

                // 更新各组（先排序再更新）
                updateDeliveryGroup('30', sortByRemainingTime(groupedData['30']), 'deliveryUrgentList', 'urgentCount', 'urgent');
                updateDeliveryGroup('20', sortByRemainingTime(groupedData['20']), 'deliveryEmergencyList', 'emergencyCount', 'emergency');
                updateDeliveryGroup('10', sortByRemainingTime(groupedData['10']), 'deliveryWarningList', 'warningCount', 'warning');
            }

            // 更新发货分组
            function updateDeliveryGroup(status, items, containerId, countId, cssClass) {
                // 更新计数
                $('#' + countId).text(items.length);
                
                let html = '';
                items.forEach(function(item) {
                    const dUserNumName = item.dUserNumName || '';
                    const customerName = item.customerName || '';
                    const ladingBillIds = item.ladingBillIds || '';
                    const totalLadingWeight = item.totalLadingWeight || 0;
                    const jobStatusName = item.jobStatusName || '';
                    const vehicleNo = item.vehicleNo || '无';
                    const remainingTime = item.remainingTimeDisplay || '';
                    
                    html += `<div class="delivery-item ${cssClass}">
                        <div class="item-line" title="车牌号: ${vehicleNo}">车牌:${vehicleNo}</div>
                        <div class="item-line" title="提单号: ${ladingBillIds}"><strong>${ladingBillIds}</strong></div>
                        <div class="item-line" title="作业状态: ${jobStatusName}">状态:${jobStatusName}</div>
                        <div class="item-line" title="重量: ${totalLadingWeight}吨">重量:${totalLadingWeight}吨</div>
                        <div class="item-line" title="剩余时间: ${remainingTime}" style="font-weight: bold; color: ${cssClass === 'urgent' ? '#fff' : '#d00'};">剩余:${remainingTime}</div>
                        <div class="item-line" title="分户名称: ${dUserNumName}">分户:${dUserNumName}</div>
                        <div class="item-line" title="运输单位: ${customerName}">运输:${customerName}</div>
                    </div>`;
                });

                $('#' + containerId).html(html);
            }

            // 启动自动刷新
            function startAutoRefresh() {
                AppState.refreshInterval = setInterval(function() {
                    loadData();
                }, 20000);
            }

            // 页面卸载时清理
            $(window).on('beforeunload', function() {
                if (AppState.refreshInterval) {
                    clearInterval(AppState.refreshInterval);
                }
                stopAutoScroll();
            });

            // 启动应用
            initApp();
        });
    </script>
</body>

</html>
