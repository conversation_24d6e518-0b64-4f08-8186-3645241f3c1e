$(function () {
    var unitInfo = IMOMUtil.fillUnitInfo();
    $("#QUERY").on("click", function (e) {
        var validator = IPLAT.Validator({
            id: "inqu"
        });
        if (!validator.validate()) {
            NotificationUtil({msg: "缺少业务单元代码和业务单元简称不可查询！"}, "error");
            return false;
        }
        result0Grid.dataSource.page(1);
        /*resultGrid.dataSource.page(1);*/
        var dataItems = resultGrid.getDataItems();
        if (dataItems.length > 0) {
            resultGrid.removeRows(dataItems);
        }
    });

    IPLATUI.EFGrid = {
        "result0": {
            /**
             * EFGrid渲染成功的回调事件
             */
            loadComplete: function (grid) {
                // 新增开关定义
                $("#ADDSWITCH").on("click", function (e) {
                    // 新增一行
                    grid.addRow();
                    if (resultGrid.getDataItems().length > 0) {
                        resultGrid.removeRows(resultGrid.getDataItems());
                    }
                });
                // 修改
                $("#UPDATE2").on("click", function (e) {
                    if (IMOMUtil.checkSelected(grid)) {
                        // 修改标记
                        var updateFlag = true;
                        var checkRows = grid.getCheckedRows();
                        $.each(checkRows, function (index, item) {
                            if (item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非修改行，请勿勾选！</b>',
                                    okFn: function (e) {
                                    },
                                    title: '修改记录'
                                });
                                updateFlag = false;
                                return false;
                            }
                        });

                        // 必填校验
                        var checkFlag = checkField(checkRows, "result0");

                        if (updateFlag && checkFlag) {
                            var inInfo = new EiInfo();
                            inInfo.addBlock(grid.getCheckedBlockData());
                            IPLAT.progress($("#result0"), true);
                            EiCommunicator.send("XTSS12", "update2", inInfo, {
                                onSuccess: function (ei) {
                                    if ("-1" == ei.status) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        IPLAT.progress($("#result0"), false);
                                    } else {
                                        result0Grid.setEiInfo(ei);
                                        // result0Grid.refresh();
                                        NotificationUtil({msg: ei.msg}, "success");
                                        IPLAT.progress($("#result0"), false);
                                    }
                                },
                                onFail: function (ei) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    return false;
                                    IPLAT.progress($("#result0"), false);
                                }
                            });
                        }

                    }
                });
                // 	删除
                $("#DELETE2").on("click", function (e) {
                    if (IMOMUtil.checkSelected(grid)) {
                        IPLAT.confirm({
                            title: "提示信息",
                            message: "你确认要删除吗?",
                            okFn: function (e) {
                                var rowNum = [];
                                var checkRows = grid.getCheckedRows();
                                $.each(checkRows, function (index, item) {
                                    if (item.isNew()) {
                                        rowNum.push(index);
                                    }
                                });
                                result0Grid.removeRows(rowNum);
                                var checkRows2 = grid.getCheckedRows();

                                var inInfo = new EiInfo();
                                inInfo.addBlock(grid.getCheckedBlockData());
                                IPLAT.progress($("#result0"), true);
                                EiCommunicator.send("XTSS12", "delete2", inInfo, {
                                    onSuccess: function (ei) {
                                        if ("-1" == ei.status) {
                                            NotificationUtil({msg: ei.msg}, "error");
                                            IPLAT.progress($("#result0"), false);
                                        } else {
                                            result0Grid.removeRows(checkRows2);
                                            NotificationUtil({msg: ei.msg}, "success");
                                            IPLAT.progress($("#result0"), false);
                                        }
                                    },
                                    onFail: function (ei) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        return false;
                                        IPLAT.progress($("#result0"), false);
                                    }
                                });
                            }
                        });
                    }
                });
                // 新增
                $("#INSERT2").on("click", function (e) {
                    // 新增标记 true为新增行
                    var newFlag = true;
                    var checkRows = grid.getCheckedRows();
                    // 判断有没有勾选新增行
                    if (checkRows.length > 0) {
                        $.each(checkRows, function (index, item) {
                            if (!item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非新增行，请勿勾选!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '新增记录'
                                });
                                newFlag = false;
                                return false;
                            }
                        });
                    } else {
                        IPLAT.alert({
                            message: '<b>没有选中待新增的行!</b>',
                            okFn: function (e) {
                            },
                            title: '新增记录'
                        });
                        newFlag = false;
                        return false;
                    }

                    // 必填校验
                    var checkFlag = checkField(checkRows, "result0");

                    if (newFlag && checkFlag) {
                        var inInfo = new EiInfo();
                        // 将result节点下的所有子节点的值赋给EiInfo
                        inInfo.setByNode("result0");
                        inInfo.addBlock(grid.getCheckedBlockData());
                        IPLAT.progress($("#result0"), true);
                        EiCommunicator.send("XTSS12", "insert2", inInfo, {
                            onSuccess: function (ei) {
                                if ("-1" == ei.status) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    IPLAT.progress($("#result0"), false);
                                } else {
                                    result0Grid.setEiInfo(ei);
                                    NotificationUtil({msg: ei.msg}, "success");
                                    IPLAT.progress($("#result0"), false);
                                }
                            },
                            onFail: function (ei) {
                                NotificationUtil({msg: ei.msg}, "error");
                                return false;
                                IPLAT.progress($("#result0"), false);
                            }
                        });
                    }
                });
            },
            /**
             * 点击行首checkbox，勾选行时触发的事件
             * @param e     kendo的Event对象
             * e.sender     kendoGrid对象，resultGrid
             * e.fake       用于区分是手动点击的事件还是模拟的事件
             * e.checked    用于区分是勾选还是取消勾选
             * e.model      勾选或取消勾选的行数据，kendo.data.Model
             * e.row        当前行的行号
             * e.tr         行的tr,包括固定列和数据列 jquery对象
             */
            onCheckRow: function (e) {
                if (e.checked && e.fake == null) {
                    $("#inqu_status-0-processId").val(e.model.processId);
                    queryDetail(e.model);
                } else {
                    $("#inqu_status-0-processId").val('');
                }
            },
            /**
             * 勾选行后，点击单元格准备编辑时的事件
             * beforeEdit可以用于自定义单元格是否可以编辑，不要和列的readonly，enable混用
             * @param e 事件对象
             * e.sender Grid对象
             * e.container 单元格td jQuery对象
             * e.row 行号
             * e.col 列号(columns中的列配置信息数组中的column对象的index)
             * e.model 行数据对象 kendo.data.Model
             * e.field 列英文名
             * e.preventDefault 禁止编辑
             */
            beforeEdit: function (e) {
                // 获取result当前页的数据
                var dataItems = resultGrid.getDataItems();
                // 判断当前行是不是新增的行
                if (!e.model.isNew() && dataItems.length > 0) {
                    // 判断单元格 field 禁止编辑
                    if (e.field === "configName") {
                        e.preventDefault();
                        NotificationUtil({msg: "此审批定义下存在开关审批不允许修改!"}, "error");
                        return false;
                    } else if (e.field === "nodeNo") {
                        e.preventDefault();
                        NotificationUtil({msg: "此审批定义下存在审批配置不允许修改!"}, "error");
                        return false;
                    } else if (e.field === "nodeName") {
                        e.preventDefault();
                        NotificationUtil({msg: "此审批定义下存在审批配置不允许修改!"}, "error");
                        return false;
                    } else if (e.field === "processId") {
                        e.preventDefault();
                        NotificationUtil({msg: "此审批定义下存在审批配置不允许修改!"}, "error");
                        return false;
                    }
                }
            },
            columns: [
                {
                    field: "processId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "流程ID",
                    editor: function (container, param) {
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "processKey",
                                textElement: $(container),
                                width: 800,
                                center: true,
                                title: "双击添加用户"
                            });
                        }
                    }
                },
            ]
        },
        "result": {
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "apprId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "审批人工号",
                    editor: function (container, param) {
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "userInfo",
                                textElement: $(container),
                                width: 800,
                                center: true,
                                title: "双击添加用户"
                            });
                        }
                    }
                },

            ],
            /**
             * EFGrid渲染成功的回调事件
             */
            loadComplete: function (grid) {
                // 开关配置
                $("#SWITCHSETTING").on("click", function (e) {
                    if (IMOMUtil.checkOneSelect(result0Grid)) {
                        var checkRows = result0Grid.getCheckedRows();
                        var model = new kendo.data.Model({
                            uuid: '',
                            unitCode: unitInfo.unitCode,
                            segNo: unitInfo.segNo,
                            segName: unitInfo.segName,
                            rowType: 'setting',
                            nodeNo: checkRows[0].nodeNo,
                            nodeName: checkRows[0].nodeName,
                            protRoleType: '',
                            apprId: checkRows[0].apprId,
                            apprName: checkRows[0].apprName,
                            recCreator: '',
                            recCreatorName: '',
                            recCreateTime: '',
                            recRevisor: '',
                            recRevisorName: '',
                            recReviseTime: '',
                            processId: checkRows[0].processId,
                            configName: checkRows[0].configName
                        });

                        // 新增一行
                        resultGrid.addRows(model, false, true);
                    }
                });
                // 修改
                $("#UPDATE1").on("click", function (e) {
                    if (IMOMUtil.checkSelected(grid)) {
                        // 修改标记
                        var updateFlag = true;
                        var checkRows = grid.getCheckedRows();
                        $.each(checkRows, function (index, item) {
                            if (item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非修改行，请勿勾选！</b>',
                                    okFn: function (e) {
                                    },
                                    title: '修改记录'
                                });
                                updateFlag = false;
                                return false;
                            }
                        });
                        for (var a = 0; a < checkRows.length; a++) {
                            if (checkRows[a].processId == "VFPM0403") {
                                if (checkRows[a].protRoleType == "" || checkRows[a].protRoleType == " ") {
                                    IPLAT.alert({
                                        message: '<b>付款申请审批请选择角色类型!</b>',
                                        okFn: function (e) {
                                        },
                                    });
                                    return false;
                                }
                            }
                        }
                        // 必填校验
                        var checkFlag = checkField(checkRows, "result");
                        if (updateFlag && checkFlag) {
                            var inInfo = new EiInfo();
                            inInfo.addBlock(grid.getCheckedBlockData());
                            IPLAT.progress($("#result"), true);
                            EiCommunicator.send("XTSS12", "update1", inInfo, {
                                onSuccess: function (ei) {
                                    if ("-1" == ei.status) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        IPLAT.progress($("#result"), false);
                                    } else {
                                        resultGrid.setEiInfo(ei);
                                        resultGrid.refresh();
                                        NotificationUtil({msg: ei.msg}, "success");
                                        IPLAT.progress($("#result"), false);
                                    }
                                },
                                onFail: function (ei) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    return false;
                                    IPLAT.progress($("#result"), false);
                                }
                            });
                        }

                    }
                });
                // 	删除
                $("#DELETE1").on("click", function (e) {
                    if (IMOMUtil.checkSelected(grid)) {
                        IPLAT.confirm({
                            title: "提示信息",
                            message: "你确认要删除吗?",
                            okFn: function (e) {
                                var rowNum = [];
                                var checkRows = grid.getCheckedRows();
                                $.each(checkRows, function (index, item) {
                                    if (item.isNew()) {
                                        rowNum.push(index);
                                    }
                                });
                                resultGrid.removeRows(rowNum);
                                var checkRows2 = grid.getCheckedRows();

                                var inInfo = new EiInfo();
                                inInfo.addBlock(grid.getCheckedBlockData());
                                IPLAT.progress($("#result"), true);
                                EiCommunicator.send("XTSS12", "delete1", inInfo, {
                                    onSuccess: function (ei) {
                                        if ("-1" == ei.status) {
                                            NotificationUtil({msg: ei.msg}, "error");
                                            IPLAT.progress($("#result"), false);
                                        } else {
                                            resultGrid.removeRows(checkRows2);
                                            NotificationUtil({msg: ei.msg}, "success");
                                            IPLAT.progress($("#result"), false);
                                        }
                                    },
                                    onFail: function (ei) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        return false;
                                        IPLAT.progress($("#result"), false);
                                    }
                                });
                            }
                        });
                    }
                });
                // 新增
                $("#INSERT1").on("click", function (e) {
                    // 新增标记 true为新增行
                    var newFlag = true;
                    var checkRows = grid.getCheckedRows();
                    // 判断有没有勾选新增行
                    if (checkRows.length > 0) {
                        $.each(checkRows, function (index, item) {
                            if (!item.isNew()) {
                                IPLAT.alert({
                                    message: '<b>非新增行，请勿勾选!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '新增记录'
                                });
                                newFlag = false;
                                return false;
                            }
                        });
                    } else {
                        IPLAT.alert({
                            message: '<b>没有选中待新增的行!</b>',
                            okFn: function (e) {
                            },
                            title: '新增记录'
                        });
                        newFlag = false;
                        return false;
                    }
                    for (var a = 0; a < checkRows.length; a++) {
                        if (checkRows[a].processId == "VFPM0403") {
                            if (checkRows[a].protRoleType == "" || checkRows[a].protRoleType == " ") {
                                IPLAT.alert({
                                    message: '<b>付款申请审批请选择角色类型!</b>',
                                    okFn: function (e) {
                                    },
                                });
                                return false;
                            }
                        }
                    }
                    // 必填校验
                    var checkFlag = checkField(checkRows, "result");
                    if (newFlag && checkFlag) {
                        var inInfo = new EiInfo();
                        // 将result节点下的所有子节点的值赋给EiInfo
                        inInfo.setByNode("result0");
                        inInfo.addBlock(grid.getCheckedBlockData());
                        IPLAT.progress($("#result"), true);
                        EiCommunicator.send("XTSS12", "insert1", inInfo, {
                            onSuccess: function (ei) {
                                if ("-1" == ei.status) {
                                    NotificationUtil({msg: ei.msg}, "error");
                                    IPLAT.progress($("#result"), false);
                                } else {
                                    resultGrid.setEiInfo(ei);
                                    NotificationUtil({msg: ei.msg}, "success");
                                    IPLAT.progress($("#result"), false);
                                }
                            },
                            onFail: function (ei) {
                                NotificationUtil({msg: ei.msg}, "error");
                                return false;
                                IPLAT.progress($("#result"), false);
                            }
                        });
                    }
                });
            },
            /**
             * 勾选行后，点击单元格准备编辑时的事件
             * beforeEdit可以用于自定义单元格是否可以编辑，不要和列的readonly，enable混用
             * @param e 事件对象
             * e.sender Grid对象
             * e.container 单元格td jQuery对象
             * e.row 行号
             * e.col 列号(columns中的列配置信息数组中的column对象的index)
             * e.model 行数据对象 kendo.data.Model
             * e.field 列英文名
             * e.preventDefault 禁止编辑
             */
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    // 判断单元格 field 禁止编辑
                    if (e.field === "unitCode") {
                        e.preventDefault();
                        NotificationUtil({msg: "业务单元代码不可修改!"}, "error");
                        return false;
                    } else if (e.field === "segNo") {
                        e.preventDefault();
                        NotificationUtil({msg: "系统账套不可修改!"}, "error");
                        return false;
                    } else if (e.field === "configName") {
                        e.preventDefault();
                        NotificationUtil({msg: "流程名称不可修改!"}, "error");
                        return false;
                    } else if (e.field === "processId") {
                        e.preventDefault();
                        NotificationUtil({msg: "流程ID不可修改!"}, "error");
                        return false;
                    }
                    /*else if (e.field === "protRoleType") {
                        e.preventDefault();
                        NotificationUtil({msg: "角色类型不可修改!"}, "error");
                        return false;
                    }*/
                } else {
                    if (e.model.rowType && e.model.rowType == "setting") {
                        if (e.field === "nodeNo") {
                            e.preventDefault();
                            NotificationUtil({msg: "节点号不可修改!"}, "error");
                            return false;
                        } else if (e.field === "nodeName") {
                            e.preventDefault();
                            NotificationUtil({msg: "节点名称不可修改!"}, "error");
                            return false;
                        }
                        /*
                                                if (e.field === "apprId") {
                                                    e.preventDefault();
                                                    NotificationUtil({msg: "审批人工号不可修改!"}, "error");
                                                    return false;
                                                } else if (e.field === "apprName") {
                                                    e.preventDefault();
                                                    NotificationUtil({msg: "审批人姓名不可修改!"}, "error");
                                                    return false;
                                                } else*/

                    }
                }
                if (e.field === "processSwitchValue") {
                    var checkRows = result0Grid.getCheckedRows();
                    querySwitchValueByName(checkRows[0].processSwitchName);
                }
            }
        }

    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);

    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo01",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                const selectUnitInfo = rows[0];
                resultGrid.setCellValue(editorModel, "unitCode", selectUnitInfo.unitCode);
                resultGrid.setCellValue(editorModel, "segName", selectUnitInfo.segName);
                resultGrid.setCellValue(editorModel, "segNo", selectUnitInfo.segNo);
            }
        }
    });
    // 列表人员信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "userInfo",
        _open: function (e, iframejQuery) {
            let a = editorModel.unitCode;
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(editorModel.unitCode);
            iframejQuery("#inqu_status-0-segNo").val(editorModel.segNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "apprId", rows[0].loginName);
                resultGrid.setCellValue(editorModel, "apprName", rows[0].userName);
            }
        }
    });
    // 列表审批信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "processKey",
        gridId:"ef_grid_result2",
        _open: function (e, iframejQuery) {
            // let a = editorModel.unitCode;
            // if (a == null || IPLAT.isBlankString(a)) {
            //     NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
            //     e.preventDefault();
            //     return false;
            // }
            // iframejQuery("#inqu_status-0-unitCode").val(editorModel.unitCode);
            // iframejQuery("#inqu_status-0-segNo").val(editorModel.segNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                result0Grid.setCellValue(editorModel, "processId", rows[0].processKey);
                result0Grid.setCellValue(editorModel, "configName", rows[0].procesName);
                result0Grid.setCellValue(editorModel, "nodeNo", rows[0].nodeKey);
                result0Grid.setCellValue(editorModel, "nodeName", rows[0].nodeName);
            }
        }
    });
});

/**
 * 校验字段必填项
 * @param listModel
 * @param blockId
 * @returns {boolean}
 */
function checkField(listModel, blockId) {
    // 校验标记
    var checkFlag = true;
    if (blockId == "result") {
        $.each(listModel, function (index, item) {
            if (IPLAT.isBlankString(item.unitCode)) {
                checkFlag = false;
                NotificationUtil({msg: "业务单元代码和系统账套不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.nodeNo)) {
                checkFlag = false;
                NotificationUtil({msg: "节点号不能为空!"}, "error");
                return false;
            }
        });
    } else if (blockId == "result0") {
        $.each(listModel, function (index, item) {
            if (IPLAT.isBlankString(item.configName)) {
                checkFlag = false;
                NotificationUtil({msg: "流程名称不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.nodeNo)) {
                checkFlag = false;
                NotificationUtil({msg: "节点号不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.processId)) {
                checkFlag = false;
                NotificationUtil({msg: "流程ID不能为空!"}, "error");
                return false;
            } else if (IPLAT.isBlankString(item.nodeName)) {
                checkFlag = false;
                NotificationUtil({msg: "节点名称不能为空!"}, "error");
                return false;
            } else {
                if (item.processId.length > 50) {
                    checkFlag = false;
                    NotificationUtil({msg: "流程ID最大长度为50个字符，请检查!"}, "error");
                    return false;
                } else if (item.configName.length > 100) {
                    checkFlag = false;
                    NotificationUtil({msg: "流程名称最大长度为100个字符，请检查!"}, "error");
                    return false;
                } else if (item.nodeNo.length > 50) {
                    checkFlag = false;
                    NotificationUtil({msg: "节点号最大长度为50个字符，请检查!"}, "error");
                    return false;
                } else if (item.nodeName.length > 50) {
                    checkFlag = false;
                    NotificationUtil({msg: "节点名称最大长度为50个字符，请检查!"}, "error");
                    return false;
                }
            }
        });
    } else {
        checkFlag = false;
    }
    return checkFlag;
};

/**
 * 生产开关详细查询
 * @param model
 */
function queryDetail(model) {
    var inInfo = new EiInfo();
    inInfo.set("inqu_status-0-processId", model.processId);
    inInfo.set("inqu_status-0-nodeNo", model.nodeNo);
    inInfo.set("inqu_status-0-segNo", $("#inqu_status-0-segNo").val());
    IPLAT.progress($("#result"), true);
    EiCommunicator.send("XTSS12", "queryDetail", inInfo, {
        onSuccess: function (eiInfo) {
            resultGrid.setEiInfo(eiInfo);
            IPLAT.progress($("#result"), false);
        },
        onFail: function (eiInfo) {
            NotificationUtil({msg: eiInfo.msg}, "error");
            return false;
            IPLAT.progress($("#result"), false);
        }
    });
};


/**
 * 根据开关名称获取开关值
 * @param model
 */
function querySwitchValueByName(model) {
    var inInfo = new EiInfo();
    inInfo.set("inqu_status-0-processSwitchName", model);
    EiCommunicator.send("XTSS12", "querySwitchValueByName", inInfo, {
        onSuccess: function (eiInfo) {
            result3Grid.setEiInfo(eiInfo);
        },
        onFail: function (eiInfo) {
            NotificationUtil({msg: eiInfo.msg}, "error");
            return false;
        }
    });
};