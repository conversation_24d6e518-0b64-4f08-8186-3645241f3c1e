package com.baosight.imom.li.rl.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.rl.dao.LIRL0110;
import com.baosight.imom.vp.pl.domain.VPPL0101;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.li.rl.dao.LIRL0101;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @Author: 张博翔
 * @Description: ${车辆预约身份维护}
 * @Date: 2024/8/6 10:37
 * @Version: 1.0
 */
public class ServiceLIRL0101 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0101().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        String customerId = MapUtils.getString(queryMap, "customerId", "");
        String customerId2 = MapUtils.getString(queryMap, "customerId2", "");
        if (StringUtils.isNotEmpty(customerId)){
            queryMap.put("reservationIdentity",20);
        }else if(StringUtils.isNotEmpty(customerId2)){
            queryMap.put("reservationIdentity",10);
        }
        outInfo = super.query(inInfo, LIRL0101.QUERY);
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String reservationIdentity = MapUtils.getString(hashMap, "reservationIdentity", "");
                String tel = MapUtils.getString(hashMap, "tel", "");
                String adminIndtity = MapUtils.getString(hashMap, "adminIndtity", "");//预约身份(客户/承运商)
                String customerId = MapUtils.getString(hashMap, "customerId", "");//承运商
                String customerName = MapUtils.getString(hashMap, "customerName", "");//承运商
                String segNo = MapUtils.getString(hashMap, "segNo", "");//账套
                EiInfo eiInfo = StrUtil.determineIDcardAndPhone(inInfo, tel, adminIndtity);
                if (eiInfo.getStatus()<0){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return eiInfo;
                }
                inInfo.set("unitCode", segNo);
                inInfo.set("segNo", segNo);
                inInfo.set("userNum", customerId);
                //检查承运商、客户
                if ("10".equals(reservationIdentity)){
                    inInfo.set("hqFlag", "Q");
                    inInfo.set("customerPropertyId", "A");
//                    inInfo.set("userName", customerName);
                    // inInfo.set("cancelMark", "N");
                    inInfo.set(EiConstant.serviceId, "S_UN_BI_0001");
                }else {
//                    inInfo.set("chineseUserName", customerName);
                    inInfo.set("IstabUserNum", "Y");
                    inInfo.set("cancelMark", "N");
                    inInfo.set(EiConstant.serviceId, "S_UN_BI_0017");
                }
                EiInfo outinfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                if (outinfo.getStatus()==-1){
                    String massage = outinfo.getMsg();
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                Map row = outinfo.getBlock(EiConstant.resultBlock).getRow(0);
                if (ObjectUtils.isNotEmpty(row)){
                    String chineseUserName = MapUtils.getString(row, "chineseUserName");
                    if (!chineseUserName.equals(customerName)){
                        if ("10".equals(reservationIdentity)){
                            String massage= "客户:"+customerName+"不存在！";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg(massage);
                            return inInfo;
                        }else {
                            String massage= "承运商:"+customerName+"不存在！";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg(massage);
                            return inInfo;
                        }
                    }
                }

                //查询数据是否重复
                Map query = new HashMap();
                query.put("reservationIdentity",reservationIdentity);
                query.put("customerId",customerId);
                query.put("customerName",customerName);
                query.put("tel",tel);
                query.put("delFlag", 0);
                query.put("segNo", segNo);
                int count = super.count(LIRL0101.COUNT, query);
                if (count>0){
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }

                //配置开关
                //判断自动打印开关是否开启
                String ifAllocationOrganization = new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALLOCATION_ORGANIZATION", dao);
                if ("1".equals(ifAllocationOrganization)){
                    //判断运输管理人员只能维护承运商的，内销人员只能维护客户的
                    EiInfo outInfo = verifyCustomerInfo(inInfo, reservationIdentity, segNo);
                    if (outInfo.getStatus() == -1) return outInfo;
                }

                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ01";
                segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0101.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }



    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String reservationIdentity = MapUtils.getString(hashMap, "reservationIdentity", "");
                String tel = MapUtils.getString(hashMap, "tel", "");
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                String adminIndtity = MapUtils.getString(hashMap, "adminIndtity", "");
                String customerId = MapUtils.getString(hashMap, "customerId", "");//承运商
                String customerName = MapUtils.getString(hashMap, "customerId", "");//承运商
                String segNo = MapUtils.getString(hashMap, "segNo", "");//账套
                EiInfo eiInfo = StrUtil.determineIDcardAndPhone(inInfo, tel, adminIndtity);
                if (eiInfo.getStatus()<0){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return eiInfo;
                }
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, map);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                //查询数据是否重复
                Map queryCount = new HashMap();
                queryCount.put("reservationIdentity",reservationIdentity);
                queryCount.put("customerId",customerId);
                queryCount.put("customerName",customerName);
                queryCount.put("tel",tel);
                queryCount.put("delFlag", 0);
                queryCount.put("notUuid", uuid);
                int count = super.count(LIRL0101.COUNT, queryCount);
                if (count>0){
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                //预约身份维护是否控制部门组织
                String ifAllocationOrganization = new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALLOCATION_ORGANIZATION", dao);
                if ("1".equals(ifAllocationOrganization)) {
                    //判断运输管理人员只能维护承运商的，内销人员只能维护客户的
                    EiInfo outInfo = verifyCustomerInfo(inInfo, reservationIdentity, segNo);
                    if (outInfo.getStatus() == -1) return outInfo;
                }

                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 验证客户信息
     * @param inInfo
     * @param segNo
     * @return
     */
    private EiInfo verifyCustomerInfo(EiInfo inInfo, String reservationIdentity, String segNo) {
        EiInfo outInfo = new EiInfo();
        List<HashMap> returnList=new ArrayList<>();
        String userId = UserSession.getUserId();
        // String userId = "781567";
        String userName = UserSession.getLoginCName();
        List<String> userIdList = new ArrayList<>();
        userIdList.add(userId);
        inInfo.set("related0SegNo", segNo);
        inInfo.set("empNos",userIdList );
        inInfo.set(EiConstant.serviceId,"S_UC_EW_0765");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            returnList = (List<HashMap>) outInfo.get("returnList");
            outInfo.addBlock("sub_result").addRows(returnList);
            outInfo.addBlock(EiConstant.resultBlock).addRows(returnList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        if (CollectionUtils.isEmpty(returnList)){
            String massage = "当前登录人:"+userName + "部门组织不存在!";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        for (HashMap hashMap : returnList) {
            String applyDeptName = MapUtils.getString(hashMap, "segName", "");
            String applyDept = MapUtils.getString(hashMap, "segNo", "");
            String related0SegNo = MapUtils.getString(hashMap, "related0SegNo", "");
            if (related0SegNo.equals(segNo)) {
                HashMap<String, String> hashMapLirl0110 = new HashMap<>();
                hashMapLirl0110.put("segNo", segNo);
                hashMapLirl0110.put("status", "20");
                hashMapLirl0110.put("applyDept", applyDept);

                if ("10".equals(reservationIdentity)) {
                    hashMapLirl0110.put("subCategory", "10");
                    List<LIRL0110> queryLIRL0110 = dao.query(LIRL0110.QUERY, hashMapLirl0110);
                    if (CollectionUtils.isEmpty(queryLIRL0110)) {
                        String massage = "当前登录人:" + userName + "不属于客户管理人员，无法维护！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                } else if ("20".equals(reservationIdentity)) {
                    hashMapLirl0110.put("subCategory", "20");
                    List<LIRL0110> queryLIRL0110 = dao.query(LIRL0110.QUERY, hashMapLirl0110);
                    if (CollectionUtils.isEmpty(queryLIRL0110)) {
                        String massage = "当前登录人:" + userName + "不属承运商管理人员，无法维护！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
            }else {
                String massage = "当前登录人:"+userName + "部门组织不存在!";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }

        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, map);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                String reservationIdentity = MapUtils.getString(hashMap, "reservationIdentity");
                String segNo = MapUtils.getString(hashMap, "segNo");
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, map);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", 20);//状态
                RecordUtils.setRevisor(hashMap);

                //配置开关
                //判断自动打印开关是否开启
                String ifAllocationOrganization = new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALLOCATION_ORGANIZATION", dao);
                if ("1".equals(ifAllocationOrganization)){
                    //判断运输管理人员只能维护承运商的，内销人员只能维护客户的
                    EiInfo outInfo = verifyCustomerInfo(inInfo, reservationIdentity, segNo);
                    if (outInfo.getStatus() == -1) return outInfo;
                }
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, map);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", 10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 承运商和客户调用IMC客商的服务.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo subQuery(EiInfo inInfo) {
        try {

            System.out.println("--------------------------开始subQuery---------------------------------");
            JSONObject jsonObject = new JSONObject();
            Map queryMap = inInfo.getBlock("sub_query_status").getRow(0);
            Map hashMap = inInfo.getBlock("sub_result").getAttr();
            // int limit = MapUtils.getInteger(hashMap, "limit");
            int limit = MapUtils.getInteger(hashMap, "limit");
            int offset = MapUtils.getInteger(hashMap, "offset");
            String userNum = MapUtils.getString(queryMap,"userNum","");
            if (StringUtils.isNotEmpty(userNum)) {
                jsonObject.put("userNum",userNum);
            }
            // //20 - 承运商 ，10 - 客户
            String reservationIdentity = MapUtils.getString(queryMap,"reservationIdentity","");
            // if ("20".equals(reservationIdentity)){
            //     jsonObject.put("IstabUserNum","Y");
            // }
            String chineseUserName = MapUtils.getString(queryMap,"chineseUserName","");
            // if (StringUtils.isNotEmpty(chineseUserName)) {
            //     if ("10".equals(reservationIdentity)){
            //         jsonObject.put("userName",chineseUserName);
            //     }else {
            //         jsonObject.put("chineseUserName",chineseUserName);
            //     }
            // }
            String segNo = MapUtils.getString(queryMap,"segNo","");
            if (StringUtils.isNotEmpty(segNo)){
                inInfo.set("segNo", segNo);
            }else {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            String unitCode = MapUtils.getString(queryMap,"unitCode","");
            if (StringUtils.isNotEmpty(unitCode)){
                inInfo.set("unitCode", unitCode);
            }
            // jsonObject.put("offset", offset);// 起始行
            // jsonObject.put("limit", limit);// 每页条数
            // //获取配置参数url
            // String prefixUrl = PlatApplicationContext.getProperty("service.url.prefix");
            // String url = prefixUrl.concat("S_UN_BI_0017");
            inInfo.set("userNum", userNum);
            inInfo.set("offset", offset);
            inInfo.set("limit", limit);
            if ("10".equals(reservationIdentity)){
                inInfo.set("hqFlag", "Q");
                inInfo.set("customerPropertyId", "A");
                inInfo.set("userName", chineseUserName);
                // inInfo.set("cancelMark", "N");
                inInfo.set(EiConstant.serviceId, "S_UN_BI_0001");
            }else if("30".equals(reservationIdentity)){
                inInfo.set("hqFlag", "Q");
                inInfo.set("customerPropertyId", "A");
                inInfo.set("userName", chineseUserName);
                // inInfo.set("cancelMark", "N");
                inInfo.set(EiConstant.serviceId, "S_UN_BI_0001");
            }else if("40".equals(reservationIdentity)){
                inInfo.set("hqFlag", "Q");
                inInfo.set("userNum",userNum);
                inInfo.set("offerService", "14");
                inInfo.set("userName", chineseUserName);
                // inInfo.set("cancelMark", "N");
                inInfo.set(EiConstant.serviceId, "S_UN_BI_0002");
            }else {
                inInfo.set("chineseUserName", chineseUserName);
                inInfo.set("IstabUserNum", "Y");
                inInfo.set("cancelMark", "N");
                // inInfo.set("custDisNum", "0100");
                inInfo.set(EiConstant.serviceId, "S_UN_BI_0017");
            }
            EiInfo eiInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            // //调post请求
            // String sendPostParam = TokenUtils.sendPost(url, String.valueOf(jsonObject), "UTF-8", TokenUtils.getXplatToken());
            //
            // EiInfo eiInfo = EiInfo.parseJSONString(sendPostParam);
            inInfo.setMsg(eiInfo.getMsg());
            List rusult = eiInfo.getBlock("result").getRows();
            Integer count = (Integer) eiInfo.getBlock("result").getAttr().get("count");
            inInfo.getBlock("sub_result").addRows(rusult);
            inInfo.getBlock("sub_result").set("showCount","true");
            inInfo.getBlock("sub_result").set("count",count);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 承运商和客户调用IMC客商的服务.
     *
     * 	S_LI_RL_0113
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo queryCustomerInfo(EiInfo inInfo) {
        try {

            System.out.println("--------------------------开始subQuery---------------------------------");
            JSONObject jsonObject = new JSONObject();
            String segNo = (String) inInfo.get("segNo");
            if (StringUtils.isBlank(segNo)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("账套为空！");
                return inInfo;
            }
            String customerId = (String) inInfo.get("customerId");
            String customerName = (String) inInfo.get("customerName");
            String reservationIdentity = (String) inInfo.get("reservationIdentity");//20 - 承运商 ，10 - 客户
            if (StringUtils.isNotEmpty(customerId)) {
                jsonObject.put("userNum",customerId);
            }
            if (StringUtils.isNotEmpty(segNo)){
                inInfo.set("segNo", segNo);
            }else {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }

            if (StringUtils.isNotEmpty(segNo)){
                inInfo.set("unitCode", segNo);
            }else {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }

            inInfo.set("userNum", customerId);
//            inInfo.set("offset", offset);
//            inInfo.set("limit", limit);
            if ("10".equals(reservationIdentity)){
                inInfo.set("hqFlag", "Q");
                inInfo.set("customerPropertyId", "A");
                inInfo.set("userName", customerName);
                inInfo.set(EiConstant.serviceId, "S_UN_BI_0001");
            }else {
                inInfo.set("chineseUserName", customerName);
                inInfo.set("IstabUserNum", "Y");
                inInfo.set("cancelMark", "N");
                inInfo.set(EiConstant.serviceId, "S_UN_BI_0017");
            }
            EiInfo eiInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            inInfo.setMsg(eiInfo.getMsg());
            List result = eiInfo.getBlock("result").getRows();
            inInfo.set("list",result);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    public EiInfo postExport(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能导出！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        Map<String, Object> loginMap = new HashMap<>();
        loginMap.put("userId", UserSession.getUserId());
        loginMap.put("userName", UserSession.getLoginCName());
        loginMap.put("loginName", UserSession.getLoginName());
        String customerId = MapUtils.getString(queryMap, "customerId", "");
        String customerId2 = MapUtils.getString(queryMap, "customerId2", "");
        if (StringUtils.isNotEmpty(customerId)){
            queryMap.put("reservationIdentity",20);
        }else if(StringUtils.isNotEmpty(customerId2)){
            queryMap.put("reservationIdentity",10);
        }
        LIRL0101 dao = new LIRL0101();
        inInfo.getBlock(EiConstant.resultBlock).set("limit","-999999");
        outInfo = super.query(inInfo, "LIRL0101.querypostExport", new LIRL0101(), false, new LIRL0101().eiMetadata, EiConstant.queryBlock, EiConstant.resultBlock, EiConstant.resultBlock);
        outInfo.removeMeta("result", "tenantId");
        outInfo.removeMeta("result", "delFlag");
        outInfo.removeMeta("result", "archiveFlag");
        outInfo.removeMeta("result", "unitCode");
        outInfo.removeMeta("result", "identityType");

        return outInfo;
    }

    public EiBlockMeta getExportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setFieldLength(23);
        eiColumn.setDescName("流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationIdentity");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约身份(承运商/客户)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("administrator");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("管理员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tel");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("管理员手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("adminIndtity");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("管理员身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建人");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建人姓名");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建时间");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改人");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改人姓名");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改时间");
        return eiMetadata;
    }

    private static void extracted(List<Map<String,Object>> hashMapList,String b) {

        for (Map<String,Object> hashMap: hashMapList){
            //判断计算时间为0 给空值
            if ("1".equals(b)){
                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "无预约单");
                        break;
                    case "10":
                        hashMap.put("status", "有预约单");
                        break;
                }

                String reservationIdentity = MapUtils.getString(hashMap, "reservationIdentity", "");
                switch (reservationIdentity) {
                    case "00":
                        hashMap.put("reservationIdentity", "无预约单");
                        break;
                    case "10":
                        hashMap.put("reservationIdentity", "有预约单");
                        break;
                }

            }
        }
    }

}
