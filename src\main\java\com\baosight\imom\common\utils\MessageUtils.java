package com.baosight.imom.common.utils;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.SoaConstants;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import static com.ibm.db2.jcc.resources.ResourceKeys.driverName;

/***
 * 发送短信工具类
 */
public class MessageUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageUtils.class);

    /***
     * 发送短信
     * @param HashMap 短信内容
     * @param msgTemplateId
     */
    public static void sendMessage(HashMap HashMap, String msgTemplateId ) {

//          String  transationId = EServiceManager.callTxStart();//发起一次广播通知，开启两段式事务

        try {
            // 短信发送
            EiInfo vzbmInfo = new EiInfo();
            EiInfo outInfo = new EiInfo();
            List list = new ArrayList();
            String param1 = MapUtils.getString(HashMap, "param1"); //手机号
            String param2 = MapUtils.getString(HashMap, "param2");
            String param3 = MapUtils.getString(HashMap, "param3");
            String param4 = MapUtils.getString(HashMap, "param4");
            String param5 = MapUtils.getString(HashMap, "param5");
            String param6 = MapUtils.getString(HashMap, "param6");
            String param7 = MapUtils.getString(HashMap, "param7");
            String param8 = MapUtils.getString(HashMap, "param8");
            String param9 = MapUtils.getString(HashMap, "param9");
            String param10 = MapUtils.getString(HashMap, "param10");
            //短信内容
//            list.add(param1);
            list.add(param2);
            list.add(param3);
            list.add(param4);
            list.add(param5);
            list.add(param6);
            list.add(param7);
            list.add(param8);
            list.add(param9);
            list.add(param10);

            //短信接收人信息
            List listReceiver = new ArrayList();
            HashMap hashMap2 = new HashMap<>();
            hashMap2.put("mobile", param1);
            hashMap2.put("receiverJobNo", param2);
            hashMap2.put("receiverName", param2);//短信接收人姓名不能为空！！！
            listReceiver.add(hashMap2);

            //组装参数
            vzbmInfo.set("params", list);
            vzbmInfo.set("receiver", listReceiver);
            vzbmInfo.set("msgTemplateId", msgTemplateId);//短信登记号
//            vzbmInfo.set("transactionId", transationId);//事务Id
            vzbmInfo.set(EiConstant.serviceId, "S_VI_PM_9067");
            outInfo = EServiceManager.call(vzbmInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == -1) {
                // 调用失败
                throw new RuntimeException("调用平台短信发送服务报错:" + outInfo.getMsg());
            }


        } catch (Exception e) {
                e.printStackTrace();
        }
    }
}
