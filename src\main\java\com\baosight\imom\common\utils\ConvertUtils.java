package com.baosight.imom.common.utils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ConvertUtils {

    /**
     * 将map转换为实体类
     *
     * @param map
     * @param entityClass
     * @return
     */
    public static <T> T convert(Map<String, Object> map, Class<T> entityClass) {
        try {
            T entity = entityClass.getDeclaredConstructor().newInstance();
            Field[] fields = entityClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (map.containsKey(fieldName)) {
                    Object value = map.get(fieldName);
                    if (value!= null && field.getType().isAssignableFrom(value.getClass())) {
                        field.set(entity, value);
                    }
                }
            }
            return entity;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
