<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-09-23 9:21:54
   		Version :  1.0
		tableName :${meliSchema}.xs_seg_no
		 ID  VARCHAR   NOT NULL   primarykey, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 SEG_FULL_NAME  VARCHAR   NOT NULL, 
		 FATHER_SEG_NO  VARCHAR   NOT NULL, 
		 SEG_NAME  VARCHAR   NOT NULL, 
		 ORG_CODE  VARCHAR   NOT NULL, 
		 COMPANY_CODE  VARCHAR   NOT NULL, 
		 IF_VIRTUAL_ORG  VARCHAR   NOT NULL, 
		 SUB_PART_1  VARCHAR   NOT NULL, 
		 CORP_TYPE  VARCHAR   NOT NULL, 
		 SEG_STATUS  VARCHAR   NOT NULL, 
		 SPARE_ATTRIBUTE  VARCHAR   NOT NULL, 
		 INTEGRATION_SEG_NO  VARCHAR   NOT NULL, 
		 ACCOUNT_SET  VARCHAR   NOT NULL, 
		 USER_NUM  VARCHAR   NOT NULL, 
		 REGIONAL_INTEGRATION_SEG_NO  VARCHAR   NOT NULL, 
		 FACTORY_TYPE  VARCHAR   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 SEG_E_START  VARCHAR   NOT NULL, 
		 SEG_E_END  VARCHAR   NOT NULL, 
		 SEG_LEVEL  VARCHAR   NOT NULL, 
		 SORT_ID  VARCHAR   NOT NULL, 
		 SUB_SORT_ID  VARCHAR   NOT NULL, 
		 ORG_TYPE  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0100">

	<select id="querySwitch" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select t.PROCESS_SWITCH_VALUE "switchValue",
		t.SEG_NO AS "segNo"
		from ${platSchema}.xs_switch t
		WHERE 1 = 1
		<isNotEmpty prepend="and" property="segNo">
			t.SEG_NO=#segNo#
		</isNotEmpty>
		and t.PROCESS_SWITCH_NAME=#processSwitchName#
		<isNotEmpty prepend="and" property="processSwitchValue">
			t.PROCESS_SWITCH_VALUE=#processSwitchValue#
		</isNotEmpty>


	</select>

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
				ID	as "id",  <!-- uuid -->
				SEG_NO	as "segNo",  <!-- 业务单元编码 -->
				SEG_FULL_NAME	as "segFullName",  <!-- 业务单元名称 -->
				FATHER_SEG_NO	as "fatherSegNo",  <!-- 上级业务单元 -->
				SEG_NAME	as "segName",  <!-- 业务单元简称 -->
				ORG_CODE	as "orgCode",  <!-- 对应EHR组织机构码 -->
				COMPANY_CODE	as "companyCode",  <!-- 公司别代码（法人单位编码）,基于公司别扩充 -->
				IF_VIRTUAL_ORG	as "ifVirtualOrg",  <!-- 是否虚拟组织,0真实机构/1虚拟机构 -->
				SUB_PART_1	as "subPart1",  <!-- 预留分段一,参与业务单元代码编码 -->
				CORP_TYPE	as "corpType",  <!-- 法人单位分类 -->
				SEG_STATUS	as "segStatus",  <!-- 业务单元状态：00作废,10锁定(预留),20正常 -->
				SPARE_ATTRIBUTE	as "spareAttribute",  <!-- 备用属性 -->
				INTEGRATION_SEG_NO	as "integrationSegNo",  <!-- 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码 -->
				ACCOUNT_SET	as "accountSet",  <!-- 标财帐套号,法人业务单元必填 -->
				USER_NUM	as "userNum",  <!-- 集团统一客商编码 -->
				REGIONAL_INTEGRATION_SEG_NO	as "regionalIntegrationSegNo",  <!-- 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码 -->
				FACTORY_TYPE	as "factoryType",  <!-- 加工中心细分类,00-非加工中心,10核心工厂 -->
				REMARK	as "remark",  <!-- 备注 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 记录归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记(默认0 删除1) -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				SEG_E_START	as "segEStart",  <!-- 有效期起始 -->
				SEG_E_END	as "segEEnd",  <!-- 有效期截止 -->
				SEG_LEVEL	as "segLevel",  <!-- 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级 -->
				SORT_ID	as "sortId",  <!-- 排序号 -->
				SUB_SORT_ID	as "subSortId",  <!-- 子排序号 -->
				ORG_TYPE	as "orgType" <!-- 机构分类 -->
		FROM ${meliSchema}.xs_seg_no WHERE 1=1
		and SEG_STATUS='20'
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  ID asc
		</isEmpty>
  		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.xs_seg_no WHERE 1=1
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
	</select>
	<!--
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segFullName">
			SEG_FULL_NAME = #segFullName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="fatherSegNo">
			FATHER_SEG_NO = #fatherSegNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segName">
			SEG_NAME = #segName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orgCode">
			ORG_CODE = #orgCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="companyCode">
			COMPANY_CODE = #companyCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifVirtualOrg">
			IF_VIRTUAL_ORG = #ifVirtualOrg#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="subPart1">
			SUB_PART_1 = #subPart1#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="corpType">
			CORP_TYPE = #corpType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segStatus">
			SEG_STATUS = #segStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="spareAttribute">
			SPARE_ATTRIBUTE = #spareAttribute#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="integrationSegNo">
			INTEGRATION_SEG_NO = #integrationSegNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="accountSet">
			ACCOUNT_SET = #accountSet#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userNum">
			USER_NUM = #userNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="regionalIntegrationSegNo">
			REGIONAL_INTEGRATION_SEG_NO = #regionalIntegrationSegNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryType">
			FACTORY_TYPE = #factoryType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segEStart">
			SEG_E_START = #segEStart#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segEEnd">
			SEG_E_END = #segEEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segLevel">
			SEG_LEVEL = #segLevel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sortId">
			SORT_ID = #sortId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="subSortId">
			SUB_SORT_ID = #subSortId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orgType">
			ORG_TYPE = #orgType#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.xs_seg_no (ID,  <!-- uuid -->
										SEG_NO,  <!-- 业务单元编码 -->
										SEG_FULL_NAME,  <!-- 业务单元名称 -->
										FATHER_SEG_NO,  <!-- 上级业务单元 -->
										SEG_NAME,  <!-- 业务单元简称 -->
										ORG_CODE,  <!-- 对应EHR组织机构码 -->
										COMPANY_CODE,  <!-- 公司别代码（法人单位编码）,基于公司别扩充 -->
										IF_VIRTUAL_ORG,  <!-- 是否虚拟组织,0真实机构/1虚拟机构 -->
										SUB_PART_1,  <!-- 预留分段一,参与业务单元代码编码 -->
										CORP_TYPE,  <!-- 法人单位分类 -->
										SEG_STATUS,  <!-- 业务单元状态：00作废,10锁定(预留),20正常 -->
										SPARE_ATTRIBUTE,  <!-- 备用属性 -->
										INTEGRATION_SEG_NO,  <!-- 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码 -->
										ACCOUNT_SET,  <!-- 标财帐套号,法人业务单元必填 -->
										USER_NUM,  <!-- 集团统一客商编码 -->
										REGIONAL_INTEGRATION_SEG_NO,  <!-- 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码 -->
										FACTORY_TYPE,  <!-- 加工中心细分类,00-非加工中心,10核心工厂 -->
										REMARK,  <!-- 备注 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 记录归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记(默认0 删除1) -->
										TENANT_USER,  <!-- 租户 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										SEG_E_START,  <!-- 有效期起始 -->
										SEG_E_END,  <!-- 有效期截止 -->
										SEG_LEVEL,  <!-- 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级 -->
										SORT_ID,  <!-- 排序号 -->
										SUB_SORT_ID,  <!-- 子排序号 -->
										ORG_TYPE  <!-- 机构分类 -->
										)		 
	    VALUES (#id#, #segNo#, #segFullName#, #fatherSegNo#, #segName#, #orgCode#, #companyCode#, #ifVirtualOrg#, #subPart1#, #corpType#, #segStatus#, #spareAttribute#, #integrationSegNo#, #accountSet#, #userNum#, #regionalIntegrationSegNo#, #factoryType#, #remark#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #tenantUser#, #unitCode#, #segEStart#, #segEEnd#, #segLevel#, #sortId#, #subSortId#, #orgType#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.xs_seg_no WHERE
			ID = #id#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.xs_seg_no
		SET 
					SEG_NO	= #segNo#,   <!-- 业务单元编码 -->  
					SEG_FULL_NAME	= #segFullName#,   <!-- 业务单元名称 -->  
					FATHER_SEG_NO	= #fatherSegNo#,   <!-- 上级业务单元 -->  
					SEG_NAME	= #segName#,   <!-- 业务单元简称 -->  
					ORG_CODE	= #orgCode#,   <!-- 对应EHR组织机构码 -->  
					COMPANY_CODE	= #companyCode#,   <!-- 公司别代码（法人单位编码）,基于公司别扩充 -->  
					IF_VIRTUAL_ORG	= #ifVirtualOrg#,   <!-- 是否虚拟组织,0真实机构/1虚拟机构 -->  
					SUB_PART_1	= #subPart1#,   <!-- 预留分段一,参与业务单元代码编码 -->  
					CORP_TYPE	= #corpType#,   <!-- 法人单位分类 -->  
					SEG_STATUS	= #segStatus#,   <!-- 业务单元状态：00作废,10锁定(预留),20正常 -->  
					SPARE_ATTRIBUTE	= #spareAttribute#,   <!-- 备用属性 -->  
					INTEGRATION_SEG_NO	= #integrationSegNo#,   <!-- 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码 -->  
					ACCOUNT_SET	= #accountSet#,   <!-- 标财帐套号,法人业务单元必填 -->  
					USER_NUM	= #userNum#,   <!-- 集团统一客商编码 -->  
					REGIONAL_INTEGRATION_SEG_NO	= #regionalIntegrationSegNo#,   <!-- 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码 -->  
					FACTORY_TYPE	= #factoryType#,   <!-- 加工中心细分类,00-非加工中心,10核心工厂 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 记录归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记(默认0 删除1) -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					SEG_E_START	= #segEStart#,   <!-- 有效期起始 -->  
					SEG_E_END	= #segEEnd#,   <!-- 有效期截止 -->  
					SEG_LEVEL	= #segLevel#,   <!-- 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级 -->  
					SORT_ID	= #sortId#,   <!-- 排序号 -->  
					SUB_SORT_ID	= #subSortId#,   <!-- 子排序号 -->  
					ORG_TYPE	= #orgType#  <!-- 机构分类 -->  
			WHERE 	
			ID = #id#
	</update>
  
</sqlMap>