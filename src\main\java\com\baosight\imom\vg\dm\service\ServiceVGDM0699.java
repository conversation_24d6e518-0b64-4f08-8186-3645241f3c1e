package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0301;
import com.baosight.imom.vg.dm.domain.VGDM0302;
import com.baosight.imom.vg.dm.domain.VGDM0601;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR> yzj
 * @Description : BA报警履历页面后台
 * @Date : 2024/11/14
 * @Version : 1.0
 */
public class ServiceVGDM0699 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0699.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0601().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
            String unitCode = inInfo.getCellStr(EiConstant.queryBlock, 0, "unitCode");
            String segNoPrefix = segNo.substring(0, 2);
            // 加载SCADA节点
            Map<String, Object> scadaMap = new HashMap<>();
            scadaMap.put("segNo", segNo);
            scadaMap.put("unitCode", unitCode);
            List<VGDM0302> scadaList = dao.query(VGDM0302.QUERY, scadaMap);
            if (CollectionUtils.isEmpty(scadaList)) {
                throw new PlatException("业务单元下没有可用的SCADA节点");
            }
            String nodeName = scadaList.get(0).getScadaName();
            // 前端传入的查询条件
            Map frontCondition = inInfo.getRow(EiConstant.queryBlock, 0);
            // 分页及排序信息
            EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
            // 组装查询条件
            Map<String, String> queryMap = getQueryMap(frontCondition, resultBlock);
            // 接口参数
            EiInfo info = new EiInfo();
            // scada节点名
            info.set("scadaName", nodeName);
            // 查询类型
            info.set("queryType", "protobuf");
            // 查询条件
            info.set("queryHisAlmInfo", queryMap);
            // 查询总报警数量
            info.set("alarmCount", true);
            info.set(EiConstant.serviceId, IplatUtils.SERVICE_QUERY_ALARM_COUNT + segNoPrefix);
            EiInfo outCountInfo = XServiceManager.call(info);
            if (outCountInfo.getStatus() < 0) {
                throw new PlatException(outCountInfo.getMsg());
            }
            int totalCount = (Integer) outCountInfo.get("count");
            // 分页查询
            info.set("alarmCount", false);
            info.set("fromIndex", resultBlock.getInt(EiConstant.offsetStr));
            info.set("totalNumber", resultBlock.getInt(EiConstant.limitStr));
            info.set(EiConstant.serviceId, IplatUtils.SERVICE_QUERY_ALARM + segNoPrefix);
            EiInfo outInfo = XServiceManager.call(info);
            if (outInfo.getStatus() != 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List alarms = (List) outInfo.get("alarms");
            // 转换数据结构
            List<VGDM0601> list = IplatUtils.javaBean2Alarm(alarms);
            // 返回数据
            resultBlock.setRows(list);
            // 返回总条数
            resultBlock.set(EiConstant.countStr, totalCount);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2000, new String[]{list.size() + ""});
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 组装查询条件
     *
     * @param frontCondition 前端传入的查询条件
     * @param resultBlock    分页信息
     */
    private Map<String, String> getQueryMap(Map frontCondition, EiBlock resultBlock) {
        Map<String, String> queryMap = new HashMap<>();
        // 使用数组存储需要处理的字段
        String[] fields = {
                "time_begin",
                "time_end",
                "tag_name",
                "alarm_status",
                "alarm_type"
        };
        // 统一处理所有字段
        for (String field : fields) {
            String value = MapUtils.getString(frontCondition, field);
            if (StrUtil.isNotBlank(value)) {
                queryMap.put(field, value);
            }
        }
        // 排序条件
        String orderByStr = resultBlock.getString(EiConstant.orderByStr);
        if (StrUtil.isNotBlank(orderByStr)) {
            String[] orderByArr = orderByStr.split(" ", 2);
            String field = IplatUtils.convertHisAlarmParam2SqlParam(orderByArr[0]);
            String order = orderByArr[1].toUpperCase();
            queryMap.put("ordering_rules", String.format("%s;%s", field, order));
        } else {
            // 默认按发生时间降序
            queryMap.put("ordering_rules", "fd_ocurrtime;DESC");
        }
        return queryMap;
    }

    /**
     * 转储
     */
    public EiInfo convert(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元不能为空");
            }
            String segNoPrefix = segNo.substring(0, 2);
            EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
            List<Map> insertList = new ArrayList<>();
            for (int i = 0; i < resultBlock.getRowCount(); i++) {
                VGDM0601 alarm = new VGDM0601();
                alarm.fromMap(resultBlock.getRow(i));
                String alarmId = segNoPrefix + alarm.getAlarmId();
                VGDM0601 dbAlarm = VGDM0601.queryByAlarmId(dao, alarmId);
                if (dbAlarm != null) {
                    throw new PlatException("报警编号" + alarmId + "已存在");
                }
                // 查询tag信息用于获取业务单元信息
                VGDM0301 tagInfo = VGDM0301.queryWithCache(dao, alarm.getAlarmTag());
                if (tagInfo == null) {
                    throw new PlatException("报警编号" + alarmId + "对应的tag不存在");
                }
                alarm.setAlarmId(alarmId);
                alarm.copyFromAlarm(tagInfo);
                Map map = alarm.toMap();
                RecordUtils.setCreator(map);
                insertList.add(map);
            }
            DaoUtils.insertBatch(dao, VGDM0601.INSERT, insertList);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
