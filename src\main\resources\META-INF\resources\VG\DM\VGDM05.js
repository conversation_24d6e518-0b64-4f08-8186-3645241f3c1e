$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
    });
    var addFlag = false;
    var updateFlag = false;
    // 点检异常信息查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                var tableId = e.contentElement.id;
                if (tableId === "info-2") {
                    // 只判断非新增和修改按钮跳转
                    if (!addFlag && !updateFlag) {
                        var checkedRows = resultGrid.getCheckedRows();
                        var checkRowLength = checkedRows.length;
                        if (checkRowLength !== 1) {
                            NotificationUtil({msg: "请勾选一条点检异常信息"}, "error");
                            e.preventDefault();
                        } else {
                            setDetailData(checkedRows[0]);
                            setReadStatus();
                        }
                    }
                } else {
                    addFlag = false;
                    updateFlag = false;
                }
            }
        }
    };

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfoMainQuery",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });
    IPLATUI.EFGrid = {
        // 点检异常信息
        "result": {
            onRowDblClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                setReadStatus();
                tab_Strip.select(1);
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT_ADD").on("click", function (e) {
                    addFlag = true;
                    IPLAT.clearNode(document.getElementById("info-2"));
                    setInsertStatus();
                    tab_Strip.select(1);
                    $("#detail_status-0-unitCode").val(unitInfo.unitCode);
                    IPLAT.EFSelect.value($("#detail_status-0-segNo"), unitInfo.segNo);
                });
                // 修改按钮
                $("#UPDATE").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能对一行数据进行修改！]"}, "error");
                        return;
                    }
                    const exceptionStatus = checkedRows[0].exceptionStatus;
                    if (exceptionStatus !== "10") {
                        NotificationUtil({msg: "操作失败，原因[只能新增状态数据进行修改！]"}, "error");
                        return;
                    }
                    updateFlag = true;
                    setDetailData(checkedRows[0]);
                    setUpdateStatus();
                    tab_Strip.select(1);
                });
                // 直接确认按钮
                $("#CONFIRM1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IPLAT.confirm({
                        message: "此操作将直接确认，不提交审核，是否继续?",
                        okFn: function (e) {
                            IMOMUtil.submitGridsData("result", "VGDM05", "confirm", true, null, null, false);
                        },
                        title: "提示"
                    });
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "submit", true, null, null, false);
                });
                // 取消提交按钮
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "cancelSubmit", true, null, null, false);
                });
                // 审核通过按钮
                $("#AGREE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "agree", true, null, null, false);
                });
                // 审核驳回按钮
                $("#REJECT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "reject", true, null, null, false);
                });
                // 确认退回按钮
                $("#CANCEL2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "cancelConfirm", true, null, null, false);
                });
                // 转设备按钮
                $("#TODEVICE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "toDevice", true, null, null, false);
                });
                // 加入设备履历按钮
                $("#ADDRESUME").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM05", "addResume", true, null, null, false);
                });
                // 新增保存按钮
                $("#INSERT_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM05", "insert", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
                // 修改保存按钮
                $("#UPDATE_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM05", "update", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
            }
        }
    };
    IPLATUI.EFSelect = {
        //异常来源
        "detail_status-0-exceptionSource": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                var dataItem = e.dataItem;
                // 30-操作新增. 40-专业新增.
                if (dataItem.valueField === "30") {
                    //设置实施方为生产人员-10
                    IPLAT.EFSelect.value($("#detail_status-0-spotCheckImplemente"), "10");
                    //设置临时措施和是否检修不可编辑
                    IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), false);
                    IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), false);
                    $("#detail_status-0-temporaryMeasures").attr("readonly", true);
                }
                if (dataItem.valueField === "40") {
                    //设置实施方为设备人员-20
                    IPLAT.EFSelect.value($("#detail_status-0-spotCheckImplemente"), "20");
                    //设置临时措施和是否检修可编辑
                    IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), true);
                    IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), true);
                    $("#detail_status-0-temporaryMeasures").attr("readonly", false);
                }
                if (dataItem.valueField === "20" || dataItem.valueField === "10") {
                    IPLAT.alert({
                        message: '<b style="color: red">手工新增的异常信息，异常来源必须选择“操作新增”或者“专业新增”</b>',
                        okFn: function (e) {
                            IPLAT.EFSelect.value($("#detail_status-0-exceptionSource"), "");
                        },
                        title: "提示信息"
                    });
                }
            }
        },
        //实施方
        "detail_status-0-spotCheckImplemente": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                var dataItem = e.dataItem;
                // 10-生产人员. 20-设备人员.
                if (dataItem.valueField === "20") {
                    //设置临时措施和是否检修可编辑
                    IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), true);
                    IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), true);
                    $("#detail_status-0-temporaryMeasures").attr("readonly", false);
                } else {
                    //设置临时措施和是否检修不可编辑
                    IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), false);
                    IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), false);
                    $("#detail_status-0-temporaryMeasures").attr("readonly", true);
                }
            }
        },
        //点检标准类型
        "detail_status-0-spotCheckStandardType": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                var dataItem = e.dataItem;
                // 10-定性. 20-定量
                if (dataItem.valueField === "20") {
                    // 释放
                    $("#detail_status-0-measureId").attr("readonly", false);
                    $("#detail_status-0-upperLimit").attr("readonly", false);
                    $("#detail_status-0-lowerLimit").attr("readonly", false);
                } else {
                    // 禁用
                    $("#detail_status-0-measureId").attr("readonly", true);
                    $("#detail_status-0-upperLimit").attr("readonly", true);
                    $("#detail_status-0-lowerLimit").attr("readonly", true);
                }
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 详情区域业务单元弹窗
    IMOMUtil.importDetailUnitInfo();
    // 详情区域设备弹窗
    IMOMUtil.importDetailEquipmentInfo();
    // 详情区域分部设备弹窗
    IMOMUtil.importDetailDeviceInfo();

    /**
     * 设置详情区域新增状态
     */
    function setInsertStatus() {
        // 新增按钮启用
        $("#INSERT_SAVE").attr("disabled", false);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", false);
        $("#detail_status-0-equipmentName").attr("disabled", false);
        $("#detail_status-0-deviceName").attr("disabled", false);
        IPLAT.EFSelect.enable($("#detail_status-0-exceptionSource"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckImplemente"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckMethod"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckStandardType"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), true);
        $("#detail_status-0-spotCheckContent").attr("readonly", false);
        $("#detail_status-0-judgmentStandard").attr("readonly", false);
        $("#detail_status-0-measureId").attr("readonly", false);
        $("#detail_status-0-temporaryMeasures").attr("readonly", false);
        $("#detail_status-0-upperLimit").attr("readonly", false);
        $("#detail_status-0-lowerLimit").attr("readonly", false);
        $("#detail_status-0-actualsRemark").attr("readonly", false);
        $("#detail_status-0-handleMeasures").attr("readonly", false);
        $("#detail_status-0-procResult").attr("readonly", false);
    }

    /**
     * 设置详情区域修改状态
     */
    function setUpdateStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮启用
        $("#UPDATE_SAVE").attr("disabled", false);
        // 标记数据是否为人工新增的数据
        var artificialFlag = false;
        // 标记计量单位信息是否可修改
        var measureFlag = false;
        // 标记是否为设备人员
        var deviceFlag = false;
        var exceptionSource = $("#detail_status-0-exceptionSource").val();
        var spotCheckStandardType = $("#detail_status-0-spotCheckStandardType").val();
        var spotCheckImplemente = $("#detail_status-0-spotCheckImplemente").val();
        // 异常来源：操作新增或专业新增时可修改点检标准相关信息
        if (exceptionSource === "30" || exceptionSource === "40") {
            artificialFlag = true;
            // 标准类型为定量时计量单位、下限、上限信息可修改
            if (spotCheckStandardType === "20") {
                measureFlag = true;
            }
        }
        // 点检实施方为设备人员时可修改是否检修、临时措施
        if (spotCheckImplemente === "20") {
            deviceFlag = true;
        }
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-deviceName").attr("disabled", true);
        IPLAT.EFSelect.enable($("#detail_status-0-exceptionSource"), artificialFlag);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckImplemente"), artificialFlag);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckMethod"), artificialFlag);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckStandardType"), artificialFlag);
        IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), deviceFlag);
        IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), deviceFlag);
        $("#detail_status-0-spotCheckContent").attr("readonly", !artificialFlag);
        $("#detail_status-0-judgmentStandard").attr("readonly", !artificialFlag);
        $("#detail_status-0-measureId").attr("readonly", !measureFlag);
        $("#detail_status-0-temporaryMeasures").attr("readonly", !deviceFlag);
        $("#detail_status-0-upperLimit").attr("readonly", !measureFlag);
        $("#detail_status-0-lowerLimit").attr("readonly", !measureFlag);
        $("#detail_status-0-actualsRemark").attr("readonly", !artificialFlag);
        $("#detail_status-0-handleMeasures").attr("readonly", false);
        $("#detail_status-0-procResult").attr("readonly", false);
    }

    /**
     * 设置详情区域只读状态
     */
    function setReadStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-deviceName").attr("disabled", true);
        IPLAT.EFSelect.enable($("#detail_status-0-exceptionSource"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckImplemente"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckMethod"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckStandardType"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-isOverhaulHandle"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-isFaultHandle"), false);
        $("#detail_status-0-spotCheckContent").attr("readonly", true);
        $("#detail_status-0-judgmentStandard").attr("readonly", true);
        $("#detail_status-0-measureId").attr("readonly", true);
        $("#detail_status-0-temporaryMeasures").attr("readonly", true);
        $("#detail_status-0-upperLimit").attr("readonly", true);
        $("#detail_status-0-lowerLimit").attr("readonly", true);
        $("#detail_status-0-actualsRemark").attr("readonly", true);
        $("#detail_status-0-handleMeasures").attr("readonly", true);
        $("#detail_status-0-procResult").attr("readonly", true);
    }

    /**
     * 设置详情区域内容
     * @param row grid行数据
     */
    function setDetailData(row) {
        // 清除原数据
        IPLAT.clearNode(document.getElementById("info-2"));
        // 将数据回填到详情页
        IMOMUtil.fillNode(row, "detail");
    }
});
