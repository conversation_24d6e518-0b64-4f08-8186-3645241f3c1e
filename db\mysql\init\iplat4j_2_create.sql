CREATE TABLE ED_XM_2PC_LOG (
        LOG_ID VARCHAR(36) NOT NULL,
        TRANSACTION_ID VARCHAR(50) NOT NULL,
        SERVICE_NAME VARCHAR(20) NOT NULL,
        SERVICE_ID VARCHAR(20) NOT NULL,
        METHOD_NAME VARCHAR(50) NOT NULL,
        STATUS VARCHAR(2) NOT NULL,
        LOG_INFO VARCHAR(1000) NOT NULL,
        TIME_STAMP VARCHAR(17),
        TRACE_ID VARCHAR(50) NOT NULL,
        PRIMARY KEY (LOG_ID)
    )COMMENT='2PC日志信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT (
        EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
        EVENT_DESC VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '事件描述',
        SYNC_TYPE VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '同/异步标志',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '是否授权',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_ED_XM_EVENT PRIMARY KEY (EVENT_ID)
    )COMMENT='微服务事件表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT_PARAM (
        PARAM_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微事件参数标识',
        EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
        PARAM_KEY VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '参数英文名',
        PARAM_KEY_CNAME VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '参数中文名',
        PARAM_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '参数类型',
        PARAM_DEF_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '参数缺省值',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_ED_XM_EVENT_PARAM PRIMARY KEY (PARAM_ID)
    )COMMENT='微事件参数信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT_ROUTE (
        EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
        SERVICE_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务标识',
        ROUTE_KEY VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '路由键',
        ROUTE_VALUE VARCHAR(100) NOT NULL COMMENT '路由值',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_ED_XM_EVENT_ROUTE PRIMARY KEY (EVENT_ID, SERVICE_ID, ROUTE_KEY)
    )COMMENT='路由表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT_SERVICE_RELA (
        EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
        SERVICE_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务标识',
        SORT_INDEX VARCHAR(3) DEFAULT ' ' NOT NULL COMMENT '排序',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        CONSTRAINT PK_ED_XM_EVENT_SERVICE_RELA PRIMARY KEY (EVENT_ID, SERVICE_ID, SORT_INDEX)
    )COMMENT='微服务事件服务关联表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE ED_XM_PARAM (
        PARAM_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务参数标识',
        SERVICE_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务标识',
        PARAM_KEY VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '参数英文名',
        PARAM_KEY_CNAME VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '参数中文名',
        PARAM_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '参数类型',
        PARAM_DEF_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '参数缺省值',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL  COMMENT '记录修改时刻',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_ED_XM_PARAM PRIMARY KEY (PARAM_ID)
    )COMMENT='微服务参数信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE ED_XM_SERVICE (
        SERVICE_ID VARCHAR(64) NOT NULL COMMENT '微服务标识',
        SERVICE_ENAME VARCHAR(64) COMMENT '服务英文名',
        METHOD_ENAME VARCHAR(64) COMMENT '方法英文名',
        SERVICE_TYPE VARCHAR(16) COMMENT '服务类型',
        SERVICE_DESC VARCHAR(256) COMMENT '中文描述',
        URL VARCHAR(256) COMMENT 'URL',
        REMARK VARCHAR(512) COMMENT '备注',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '是否授权',
        TRANS_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '事务类型',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_ED_XM_SERVICE PRIMARY KEY (SERVICE_ID)
    )COMMENT='微服务信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_JOB_DETAILS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        JOB_NAME VARCHAR(200) NOT NULL,
        JOB_GROUP VARCHAR(200) NOT NULL,
        DESCRIPTION VARCHAR(250) NULL,
        JOB_CLASS_NAME VARCHAR(250) NOT NULL,
        IS_DURABLE VARCHAR(1) NOT NULL,
        IS_NONCONCURRENT VARCHAR(1) NOT NULL,
        IS_UPDATE_DATA VARCHAR(1) NOT NULL,
        REQUESTS_RECOVERY VARCHAR(1) NOT NULL,
        JOB_DATA BLOB NULL,
        PRIMARY KEY (SCHED_NAME,JOB_NAME,JOB_GROUP)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_TRIGGERS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        TRIGGER_NAME VARCHAR(200) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        JOB_NAME VARCHAR(200) NOT NULL,
        JOB_GROUP VARCHAR(200) NOT NULL,
        DESCRIPTION VARCHAR(250) NULL,
        NEXT_FIRE_TIME DECIMAL(13,0) NULL,
        PREV_FIRE_TIME DECIMAL(13,0) NULL,
        PRIORITY INTEGER NULL,
        TRIGGER_STATE VARCHAR(16) NOT NULL,
        TRIGGER_TYPE VARCHAR(8) NOT NULL,
        START_TIME DECIMAL(13,0) NOT NULL,
        END_TIME DECIMAL(13,0) NULL,
        CALENDAR_NAME VARCHAR(200) NULL,
        MISFIRE_INSTR DECIMAL(2,0) NULL,
        JOB_DATA BLOB NULL,
        PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
        FOREIGN KEY (SCHED_NAME,JOB_NAME,JOB_GROUP) REFERENCES EJ_QRTZ_JOB_DETAILS(SCHED_NAME,
        JOB_NAME,JOB_GROUP)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_BLOB_TRIGGERS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        TRIGGER_NAME VARCHAR(200) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        BLOB_DATA BLOB NULL,
        PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
        FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) REFERENCES EJ_QRTZ_TRIGGERS(SCHED_NAME,
        TRIGGER_NAME,TRIGGER_GROUP)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_CALENDARS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        CALENDAR_NAME VARCHAR(200) NOT NULL,
        CALENDAR BLOB NOT NULL,
        PRIMARY KEY (SCHED_NAME,CALENDAR_NAME)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN ;

CREATE TABLE EJ_QRTZ_CRON_TRIGGERS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        TRIGGER_NAME VARCHAR(200) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        CRON_EXPRESSION VARCHAR(120) NOT NULL,
        TIME_ZONE_ID VARCHAR(80),
        PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
        FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) REFERENCES EJ_QRTZ_TRIGGERS(SCHED_NAME,
        TRIGGER_NAME,TRIGGER_GROUP)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_FIRED_TRIGGERS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        ENTRY_ID VARCHAR(95) NOT NULL,
        TRIGGER_NAME VARCHAR(200) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        INSTANCE_NAME VARCHAR(200) NOT NULL,
        FIRED_TIME DECIMAL(13,0) NOT NULL,
        SCHED_TIME DECIMAL(13,0) NOT NULL,
        PRIORITY INTEGER NOT NULL,
        STATE VARCHAR(16) NOT NULL,
        JOB_NAME VARCHAR(200) NULL,
        JOB_GROUP VARCHAR(200) NULL,
        IS_NONCONCURRENT VARCHAR(1) NULL,
        REQUESTS_RECOVERY VARCHAR(1) NULL,
        PRIMARY KEY (SCHED_NAME,ENTRY_ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;



CREATE TABLE EJ_QRTZ_LOCKS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        LOCK_NAME VARCHAR(40) NOT NULL,
        PRIMARY KEY (SCHED_NAME,LOCK_NAME)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_LOGGING (
        SID VARCHAR(32) NOT NULL,
        START_TIME DECIMAL(13,0),
        END_TIME DECIMAL(13,0),
        JOB_NAME VARCHAR(200),
        JOB_GROUP VARCHAR(80),
        TRIGGER_NAME VARCHAR(80),
        TRIGGER_GROUP VARCHAR(80),
        STATUS DECIMAL(2,0),
        EXE_STATUS DECIMAL(2,0),
        EXE_INFO VARCHAR(250),
        EXE_MSG VARCHAR(4000),
        PROJECT_NAME VARCHAR(32),
        PRIMARY KEY (SID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `EJ_QRTZ_LOGGING`
MODIFY COLUMN `SID`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '序列号' FIRST ,
MODIFY COLUMN `START_TIME`  decimal(13,0) NULL DEFAULT NULL COMMENT '开始时间' AFTER `SID`,
MODIFY COLUMN `END_TIME`  decimal(13,0) NULL DEFAULT NULL COMMENT '结束时间' AFTER `START_TIME`,
MODIFY COLUMN `JOB_NAME`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '任务编号' AFTER `END_TIME`,
MODIFY COLUMN `JOB_GROUP`  varchar(80) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '任务分组' AFTER `JOB_NAME`,
MODIFY COLUMN `TRIGGER_NAME`  varchar(80) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '任务触发器编号' AFTER `JOB_GROUP`,
MODIFY COLUMN `TRIGGER_GROUP`  varchar(80) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '触发器分组' AFTER `TRIGGER_NAME`,
MODIFY COLUMN `STATUS`  decimal(2,0) NULL DEFAULT NULL COMMENT '任务状态' AFTER `TRIGGER_GROUP`,
MODIFY COLUMN `EXE_STATUS`  decimal(2,0) NULL DEFAULT NULL COMMENT '调用状态' AFTER `STATUS`,
MODIFY COLUMN `EXE_INFO`  varchar(250) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '执行信息' AFTER `EXE_STATUS`,
MODIFY COLUMN `EXE_MSG`  varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '反馈信息' AFTER `EXE_INFO`,
MODIFY COLUMN `PROJECT_NAME`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '项目名称' AFTER `EXE_MSG`,
COMMENT='任务日志类';

CREATE TABLE EJ_QRTZ_PAUSED_TRIGGER_GRPS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        PRIMARY KEY (SCHED_NAME,TRIGGER_GROUP)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_SCHEDULER_STATE (
        SCHED_NAME VARCHAR(120) NOT NULL,
        INSTANCE_NAME VARCHAR(200) NOT NULL,
        LAST_CHECKIN_TIME DECIMAL(13,0) NOT NULL,
        CHECKIN_INTERVAL DECIMAL(13,0) NOT NULL,
        PRIMARY KEY (SCHED_NAME,INSTANCE_NAME)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE EJ_QRTZ_SIMPLE_TRIGGERS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        TRIGGER_NAME VARCHAR(200) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        REPEAT_COUNT DECIMAL(7,0) NOT NULL,
        REPEAT_INTERVAL DECIMAL(12,0) NOT NULL,
        TIMES_TRIGGERED DECIMAL(10,0) NOT NULL,
        PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
        FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) REFERENCES EJ_QRTZ_TRIGGERS(SCHED_NAME,
        TRIGGER_NAME,TRIGGER_GROUP)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN ;

CREATE TABLE EJ_QRTZ_SIMPROP_TRIGGERS (
        SCHED_NAME VARCHAR(120) NOT NULL,
        TRIGGER_NAME VARCHAR(200) NOT NULL,
        TRIGGER_GROUP VARCHAR(200) NOT NULL,
        STR_PROP_1 VARCHAR(512) NULL,
        STR_PROP_2 VARCHAR(512) NULL,
        STR_PROP_3 VARCHAR(512) NULL,
        INT_PROP_1 INT NULL,
        INT_PROP_2 INT NULL,
        LONG_PROP_1 DECIMAL(13,0) NULL,
        LONG_PROP_2 DECIMAL(13,0) NULL,
        DEC_PROP_1 NUMERIC(13,4) NULL,
        DEC_PROP_2 NUMERIC(13,4) NULL,
        BOOL_PROP_1 VARCHAR(1) NULL,
        BOOL_PROP_2 VARCHAR(1) NULL,
        PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
        FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) REFERENCES EJ_QRTZ_TRIGGERS(SCHED_NAME,
        TRIGGER_NAME,TRIGGER_GROUP)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;





CREATE TABLE EM_HI_LOG (
  ID VARCHAR(32)  NOT NULL COMMENT 'UUID',
  CATEGORY VARCHAR(16)  DEFAULT NULL COMMENT '消息分类(SEND:发送消息,RECEIVE:接收消息)',
  MESSAGE_TYPE_ID VARCHAR(255)  DEFAULT NULL COMMENT '消息类型ID',
  STATE VARCHAR(16)  DEFAULT NULL COMMENT '消息调用状态',
  RETURN_STATUS VARCHAR(10)  DEFAULT NULL COMMENT '调用消息返回状态',
  RETURN_BODY LONGBLOB COMMENT '返回内容',
  OPERATE_START_TIME VARCHAR(20)  DEFAULT NULL COMMENT '开始调用时间',
  OPERATE_RESULT DECIMAL(4,0) DEFAULT NULL COMMENT '调用结果',
  OPERATE_END_TIME VARCHAR(20)  DEFAULT NULL COMMENT '结束调用时间',
  OPERATE_COUNT DECIMAL(4,0) DEFAULT NULL COMMENT '调用次数',
  SERVICE_NAME VARCHAR(128)  DEFAULT NULL COMMENT '服务调用类名',
  METHOD_NAME VARCHAR(128)  DEFAULT NULL COMMENT '服务调用方法名',
  RPC_TYPE VARCHAR(64)  DEFAULT NULL COMMENT '调用类型',
  MESSAGE_BODY LONGBLOB COMMENT '消息内容',
  PROJECT_NAME VARCHAR(64)  DEFAULT NULL COMMENT '项目英文名',
  MOUDLE_NAME VARCHAR(64)  DEFAULT NULL COMMENT '模块英文名',
  SEND_ADDRESS VARCHAR(255)  DEFAULT NULL COMMENT '消息发送方地址',
  RECEIVE_ADDRESS VARCHAR(255)  DEFAULT NULL COMMENT '消息接收方地址',
  EXT1 VARCHAR(255)  DEFAULT NULL COMMENT '扩展字段1',
  EXT2 VARCHAR(255)  DEFAULT NULL COMMENT '扩展字段2',
  EXT3 VARCHAR(255)  DEFAULT NULL COMMENT '业务信息',
  GROUP_NAME VARCHAR(128)  DEFAULT NULL COMMENT '消息接收方地址',
  HTTP_STATUS VARCHAR(16)  DEFAULT NULL COMMENT 'HTTP状态',
  EVENT_ID VARCHAR(128)  DEFAULT NULL COMMENT '事件ID',
  PRIMARY KEY (ID),
  KEY EVENT_ID_INDEX (EVENT_ID)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE=UTF8_BIN COMMENT='历史日志';

CREATE TABLE EM_HISTORY (
        ID VARCHAR(32) NOT NULL COMMENT 'UUID',
        CATEGORY VARCHAR(16) COMMENT '消息分类(SEND:发送消息,RECEIVE:接收消息)',
        MESSAGE_TYPE_ID VARCHAR(255) COMMENT '消息类型ID',
        STATE VARCHAR(16) COMMENT '消息调用状态',
        RETURN_STATUS VARCHAR(10)  COMMENT '调用消息返回状态',
        RETURN_BODY BLOB(2147483647) COMMENT '返回内容',
        OPERATE_START_TIME VARCHAR(20) COMMENT '开始调用时间',
        OPERATE_RESULT DECIMAL(4,0) COMMENT '调用结果',
        OPERATE_END_TIME VARCHAR(14) COMMENT '结束调用时间',
        OPERATE_COUNT DECIMAL(4,0) COMMENT '调用结果',
        SERVICE_NAME VARCHAR(128) COMMENT '服务调用方法名',
        METHOD_NAME VARCHAR(128) COMMENT '服务调用方法名',
        RPC_TYPE VARCHAR(64) COMMENT '调用类型',
        MESSAGE_BODY BLOB(2147483647) COMMENT '消息内容',
        PROJECT_NAME VARCHAR(64) COMMENT '项目英文名',
        MOUDLE_NAME VARCHAR(64) COMMENT '模块英文名',
        SEND_ADDRESS VARCHAR(255) COMMENT '消息发送方地址',
        RECEIVE_ADDRESS VARCHAR(255) COMMENT '消息接收方地址',
        EXT1 VARCHAR(255) COMMENT '扩展字段1',
        EXT2 VARCHAR(255) COMMENT '扩展字段2',
        EXT3 VARCHAR(255) COMMENT '业务信息',
        GROUP_NAME VARCHAR(128) COMMENT '消息接收方地址',
        EVENT_ID VARCHAR(128) COMMENT '事件ID',
		HTTP_STATUS VARCHAR(16) COMMENT 'HTTP状态',
        CONSTRAINT PK_EM_HISTORY PRIMARY KEY (ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='消息履历表';
CREATE INDEX IDX_EM_HISTORY ON EM_HISTORY(OPERATE_START_TIME,OPERATE_END_TIME,CATEGORY);
CREATE INDEX IDX_EM_HISTORY_EVENT_ID on EM_HISTORY (EVENT_ID);
CREATE INDEX IDX_EM_HISTORY_STATUS ON EM_HISTORY(RETURN_STATUS);
create index IDX_EM_HISTORY_GROUP on EM_HISTORY (GROUP_NAME);
create index IDX_EM_HISTORY_GROUP_QUERY on EM_HISTORY (GROUP_NAME, RETURN_STATUS, OPERATE_START_TIME ASC);

CREATE TABLE EM_TELE_METADATA (
  EVENT_ID VARCHAR(64) COLLATE UTF8_BIN NOT NULL COMMENT '电文号',
  BLOCK_ENAME  VARCHAR(32) default 'result' not null  COMMENT '数据块英文名',
  FIELD_ENAME VARCHAR(32) COLLATE UTF8_BIN NOT NULL COMMENT '业务对象字段英文名',
  FIELD_CNAME VARCHAR(64) COLLATE UTF8_BIN DEFAULT NULL COMMENT '业务对象字段中文名',
  FIELD_TYPE VARCHAR(32) COLLATE UTF8_BIN DEFAULT NULL COMMENT '业务对象字段类型',
  FIELD_LENGTH INT(11) DEFAULT NULL COMMENT '业务对象字段长度',
  FIELD_SCALE_LENGTH INT(11) DEFAULT NULL COMMENT '业务对象字段精度',
  FIELD_SORT_ID INT(11) DEFAULT NULL COMMENT '业务对象字段排序号',
  DESCRIPTION VARCHAR(255) COLLATE UTF8_BIN DEFAULT NULL COMMENT '电文描述',
  PRIMARY KEY (EVENT_ID,BLOCK_ENAME,FIELD_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE=UTF8_BIN COMMENT='电文参数表';

CREATE TABLE TEDCC02 (
        CONFIG_ENV_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '配置环境标识',
        CONFIG_ENV_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置环境中文名',
        PROJECT VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '项目',
        VERSION VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '版本',
        PROJECT_ENV VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '环境',
        REC_CREATOR VARCHAR(16) COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) COMMENT '记录修改时刻',
        MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '一级模块',
        MODULE_ENAME_2 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '二级模块',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEDCC02 PRIMARY KEY (CONFIG_ENV_ID)
    )COMMENT='配置环境' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDCC03 (
        STATUS INTEGER DEFAULT '1' COMMENT '状态：1是正常 0是删除',
        CONFIG_TYPE VARCHAR(255) COMMENT '配置项类别',
        CONFIG_CUE VARCHAR(255) COMMENT '配置项提示',
        REC_CREATOR VARCHAR(16) COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) COMMENT '记录修改时刻',
        CONFIG_ENV_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '配置环境',
        FKEY VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置名称',
        FVALUE VARCHAR(4000) COMMENT '配置内容',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONFIG_DESC VARCHAR(255),
        CONSTRAINT PK_TEDCC03 PRIMARY KEY (CONFIG_ENV_ID, FKEY)
    )COMMENT='配置信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEDCC03`
MODIFY COLUMN `CONFIG_DESC`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '配置项描述' AFTER `ARCHIVE_FLAG`;


CREATE TABLE TEDCM00 (
        CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类编号',
        CODESET_NAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码分类名称',
        CODESET_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类英文名称',
        GB_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '国标编号',
        REMARK VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '备注',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        CODESET_TYPE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码类型',
        CODESET_URL VARCHAR(2000) DEFAULT ' ' NOT NULL COMMENT '对应URL',
        PROJECT_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '应用系统',
        SUB_CODESET_CODE VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '子代码分类编号',
        REF_ID VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '关联字段',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CODESET_LEVEL VARCHAR(64) DEFAULT ' ' NOT NULL,
        CATEGORY VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '目录',
        CONSTRAINT PK_TEDCM00 PRIMARY KEY (CODESET_CODE, PROJECT_NAME)
    )COMMENT='代码大类表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDCM00`
MODIFY COLUMN `CODESET_LEVEL`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '代码管理层级' AFTER `ARCHIVE_FLAG`;

CREATE TABLE TEDCM0001 (
        ID VARCHAR(64) DEFAULT ' ' NOT NULL,
        CATEGORY_KEY VARCHAR(64) DEFAULT ' ' NOT NULL,
        CATEGORY_NAME VARCHAR(255) DEFAULT ' ' NOT NULL,
        PARENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
        CONSTRAINT PK_TEDCM0001 PRIMARY KEY (ID)
    )COMMENT='代码分类目录表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDCM0001`
MODIFY COLUMN `ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '树ID' FIRST ,
MODIFY COLUMN `CATEGORY_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '树节点英文名' AFTER `ID`,
MODIFY COLUMN `CATEGORY_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '树节点中文名' AFTER `CATEGORY_KEY`,
MODIFY COLUMN `PARENT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '父节点' AFTER `CATEGORY_NAME`,
MODIFY COLUMN `REC_CREATOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录创建责任者' AFTER `PARENT_ID`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '归档标记' AFTER `REC_REVISE_TIME`;

CREATE TABLE TEDCM01 (
        CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类编号',
        ITEM_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码明细编号',
        ITEM_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码明细中文名称',
        ITEM_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码明细英文名称',
        REMARK VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '备注',
        ITEM_STATUS VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '代码状态',
        SORT_ID VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '顺序号',
        STATUS VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '字段状态',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        PROJECT_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '应用系统',
        SUB_CODESET_CODE VARCHAR(64) DEFAULT ' ' COMMENT '子代码分类编号',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEDCM01 PRIMARY KEY (CODESET_CODE, ITEM_CODE, PROJECT_NAME)
    )COMMENT='代码详情表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDCM02 (
    CASCADE_ID VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT 'ID',
    CASCADE_CODESET_CODE VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '级联代码分类编号',
    CASCADE_TYPE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '级联关系类型',
    CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类编号',
    ITEM_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码明细编号',
    SUB_CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子代码分类编号',
    SUB_ITEM_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子代码明细编号',
    ACTIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '启用标记',
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    PROJECT_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '应用系统',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    CONSTRAINT PK_TEDCM02 PRIMARY KEY (CASCADE_ID)
)COMMENT='代码级联关系表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDDBI0 (
	REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT  '记录创建责任者',
	REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
	REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
	REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
	ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
	PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
	TABLE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '系统数据结构表英文名',
	INDEX_ENAME VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '数据库索引英文名称',
	TABLE_INDEX_TYPE VARCHAR(3) DEFAULT ' ' NOT NULL COMMENT '数据库表索引类型',
	INDEX_SEQ DECIMAL(9,0) DEFAULT 0 NOT NULL COMMENT '索引内字段顺序号',
	ITEM_SEQ DECIMAL(9,0) DEFAULT 0 NOT NULL COMMENT '字段索引号',
	ITEM_SORT_TYPE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '索引内数据项排序类型',
	TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
	CONSTRAINT PK_TEDDBI0 PRIMARY KEY (PROJECT_ENAME, TABLE_ENAME, INDEX_ENAME, INDEX_SEQ)
)COMMENT='数据库索引信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDDBT0 (
   REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
   REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
   REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
   REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
   ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
   PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
   TABLE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '系统数据结构表英文名',
   TABLE_CNAME VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '数据库表中文名称',
   REFER_PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '引用的项目英文名称',
   REC_COUNT DECIMAL(10,0) DEFAULT 0 NOT NULL COMMENT '记录条数',
   REMARK VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '备注',
   DB_SCHEMA VARCHAR(40) DEFAULT ' ' NOT NULL ,
   MODULE_ENAME_1 VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '一级模块',
   MODULE_ENAME_2 VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '二级模块',
   TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
   CONSTRAINT PK_TEDDBT0 PRIMARY KEY (PROJECT_ENAME, TABLE_ENAME)
   )COMMENT='数据库表头信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEDDBT0`
MODIFY COLUMN `REC_CREATOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '记录创建责任者' FIRST ,
MODIFY COLUMN `DB_SCHEMA`  varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '模式名' AFTER `REMARK`;





CREATE TABLE TEDDBT1 (
     REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
     REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
     REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
     REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
     ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
     PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
     TABLE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '系统数据结构表英文名',
     TABLE_ITEM_SEQ DECIMAL(9,0) DEFAULT 0 NOT NULL COMMENT '表内数据项序号',
     TABLE_ITEM_TYPE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '表内数据项类型',
     ITEM_SEQ DECIMAL(9,0) DEFAULT 0 NOT NULL COMMENT '字段索引号',
     MODEL_TABLE_ENAME VARCHAR(18) DEFAULT ' ' NOT NULL COMMENT '模板表英文名称',
	 TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
     CONSTRAINT PK_TEDDBT1 PRIMARY KEY (PROJECT_ENAME, TABLE_ENAME, ITEM_SEQ)
     )COMMENT='数据库表体信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDDBT3 (
        UNITTB_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL,
        UNITTB_REMARK VARCHAR(256) DEFAULT ' ' NOT NULL,
        PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        MODULE_ENAME VARCHAR(18) DEFAULT ' ' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        CONSTRAINT PK_TEDDBT3 PRIMARY KEY (UNITTB_ENAME, PROJECT_ENAME)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDDBT5 (
        TABLE_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL,
        USE_CLASSIFY VARCHAR(50) DEFAULT ' ' NOT NULL,
        SQL_TEXT VARCHAR(500) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16),
        REC_CREATE_TIME VARCHAR(17),
        REC_REVISOR VARCHAR(16),
        REC_REVISE_TIME VARCHAR(17),
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        CONSTRAINT PK_TEDDBT5 PRIMARY KEY (TABLE_ENAME, SQL_TEXT)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDDBT5`
MODIFY COLUMN `TABLE_ENAME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '系统数据结构表英文名' FIRST ,
MODIFY COLUMN `USE_CLASSIFY`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '使用分类' AFTER `TABLE_ENAME`,
MODIFY COLUMN `SQL_TEXT`  varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT 'SQL语句的正文' AFTER `USE_CLASSIFY`,
MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' AFTER `SQL_TEXT`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `ARCHIVE_FLAG`,
COMMENT='数据更新配置';

  CREATE TABLE TEDFA00 (
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        FORM_ENAME VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '画面英文名',
        FORM_CNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '画面中文名',
        FORM_LOAD_PATH VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '画面载入的路径',
        FORM_TYPE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '画面类型',
        MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '一级模块英文名',
        MODULE_ENAME_2 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '二级模块英文名',
        INIT_LOAD_SERVICE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '初始处理服务英文名',
        IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT  '是否授权',
        FORM_PARAM VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '画面参数',
        SUBAPP_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
        ICON_INFO VARCHAR(128) DEFAULT ' ' NOT NULL COMMENT '图标',
        BUSINESS_CATEGORY VARCHAR(50) DEFAULT ' ' NOT NULL,
        OPERATE_TYPE VARCHAR(50) DEFAULT ' ' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEDFA00 PRIMARY KEY (FORM_ENAME)
    )COMMENT ='画面信息定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDFA01 (
    REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    FORM_ENAME VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '画面英文名',
    REGION_ID VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '区域标识',
    BUTTON_ENAME VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '按钮英文名',
    BUTTON_CNAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '按钮中文',
    BUTTON_DESC VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '按钮描述',
    NODE_SORT_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '节点排序标识',
    IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '是否授权',
    URI VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
    LAYOUT VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
    POSITION VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
	TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
	BUSINESS_CATEGORY VARCHAR(50) DEFAULT ' ' NOT NULL,
	OPERATE_TYPE VARCHAR(50) DEFAULT ' ' NOT NULL,
    CONSTRAINT PK_TEDFA01 PRIMARY KEY (FORM_ENAME, BUTTON_ENAME)
    ) COMMENT ='画面按钮信息定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDFA01`
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `POSITION`,
MODIFY COLUMN `BUSINESS_CATEGORY`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '业务类型' AFTER `TENANT_ID`,
MODIFY COLUMN `OPERATE_TYPE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '操作分类' AFTER `BUSINESS_CATEGORY`;

CREATE TABLE TEDFA10 (
        PK_TEDFA10_ID VARCHAR(36) NOT NULL,
        USER_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
        PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL,
        FORM_ENAME VARCHAR(8) DEFAULT ' ' NOT NULL,
        FORM_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
        NODE_SORT_ID VARCHAR(20) DEFAULT ' ' NOT NULL,
		TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
        CONSTRAINT PK_TEDFA10 PRIMARY KEY (PK_TEDFA10_ID),
        CONSTRAINT TEDFA10_UNIQUE UNIQUE (USER_ID, PROJECT_ENAME, FORM_ENAME)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDFA60 (
        PK_TEDFA60_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
        PROJECT_ENAME VARCHAR(128) DEFAULT ' ' NOT NULL,
        FORM_ENAME VARCHAR(8) DEFAULT ' ' NOT NULL,
        GRID_ID VARCHAR(128) DEFAULT ' ' NOT NULL,
        USER_ID VARCHAR(128) DEFAULT ' ' NOT NULL,
        COLUMN_ENAME VARCHAR(128) DEFAULT ' ' NOT NULL,
        COLUMN_CNAME VARCHAR(256) DEFAULT ' ' NOT NULL,
        COLUMN_LOCKED VARCHAR(1) DEFAULT '0' NOT NULL,
        COLUMN_HIDDEN VARCHAR(1) DEFAULT '0' NOT NULL,
        COLUMN_WIDTH INTEGER DEFAULT 120 NOT NULL,
        COLUMN_ORDER INTEGER DEFAULT 0 NOT NULL,
        SOFT_DELETE VARCHAR(1) DEFAULT '0',
        PRIMARY KEY (PK_TEDFA60_ID),
        UNIQUE (PROJECT_ENAME, FORM_ENAME, GRID_ID, USER_ID, COLUMN_ENAME)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDFA61 (
        PK_TEDFA61_ID VARCHAR(36) DEFAULT '' NOT NULL,
        PROJECT_ENAME VARCHAR(250) DEFAULT '' NOT NULL,
        USER_ID VARCHAR(255) DEFAULT '' NOT NULL,
        STYLE_ENAME VARCHAR(10) DEFAULT '' NOT NULL,
        STYLE_FONT VARCHAR(255) DEFAULT '',
        STYLE_ECOLOR VARCHAR(20) DEFAULT '',
		TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        PRIMARY KEY (PK_TEDFA61_ID),
        UNIQUE (PROJECT_ENAME, USER_ID, STYLE_ENAME)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDMDM2 (
        ACCSET_NO DECIMAL(11,0) DEFAULT 0 NOT NULL COMMENT '帐套序号',
        SEQ_TYPE_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '序列号类型ID',
        SEQ_SUBSECS DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '序列号分段数',
        DATE_CYCLE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '日期循环级别',
        SEQ_LMT DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '序列号长度限制',
        SUBID_LMT_LEN DECIMAL(3,0) DEFAULT 0 NOT NULL COMMENT '子项长度限制',
        REMARK VARCHAR(512) DEFAULT ' ' NOT NULL COMMENT '备注',
        REC_CREATOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
        REC_CREATE_TIME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
        REC_REVISOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
        REC_REVISE_TIME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
        IS_LINKED DECIMAL(1,0) DEFAULT 0 COMMENT '是否连号',
		TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '',
        CONSTRAINT PK_TEDMDM2 PRIMARY KEY (SEQ_TYPE_ID)
    )COMMENT='序列号定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDMDM3 (
        SEQ_TYPE_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '序列号类型ID',
        SUBSEC_SEQ DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '分段序号',
        SUBSEC_NAME VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '分段名称',
        SUBSEC_TYPE VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '分段类型',
        SUBSEC_CONTENT VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '定义内容',
        SUBSEC_LEN DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '分段长度',
        DATE_FORMAT VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '日期格式',
        REC_CREATOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
        REC_CREATE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
        REC_REVISOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
        REC_REVISE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
		TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEDMDM3 PRIMARY KEY (SEQ_TYPE_ID, SUBSEC_SEQ)
    )COMMENT='序列号分段信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDMDM4 (
        ACCSET_NO DECIMAL(11,0) DEFAULT 0 NOT NULL COMMENT '帐套序号',
        SEQ_TYPE_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '序列号类型ID',
        SEQ_PREFIX VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '序列号前缀',
        YEAR_MON VARCHAR(8) DEFAULT ' ' NOT NULL COMMENT '年月',
        CURRENT_SEQ DECIMAL(11,0) DEFAULT 0 NOT NULL COMMENT '当前序列',
        REC_CREATOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
        REC_CREATE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
        REC_REVISOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
        REC_REVISE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
		TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEDMDM4 PRIMARY KEY (ACCSET_NO, SEQ_TYPE_ID, SEQ_PREFIX, YEAR_MON)
    )COMMENT='当前序列' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDNM01 (
        MACHINE_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '主机标识',
        MACHINE_NAME VARCHAR(50) COMMENT '主机名称',
        MACHINE_HOST VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '主机地址',
        MACHINE_POID VARCHAR(50) COMMENT '机器物理标识',
        SYSTEM_TYPE VARCHAR(50) COMMENT '系统类型',
        AUTH_TYPE VARCHAR(50) COMMENT '认证类型',
        AUTH_USERNAME VARCHAR(50) COMMENT '认证账号',
        AUTH_CERT VARCHAR(200) COMMENT '认证凭证',
        AUTH_PORT VARCHAR(50) COMMENT '认证链接端口',
        PROTOCOL VARCHAR(200) COMMENT '上传协议',
        REC_CREATOR VARCHAR(16) COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) COMMENT '记录修改时刻',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEDNM01 PRIMARY KEY (MACHINE_ID)
    )COMMENT ='主机信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDNM02 (
        NODE_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '节点标识',
        NODE_CNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '节点名称',
        PORT VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT 'PORT',
        MACHINE_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '主机标识',
        PROJECT_ENV VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '环境',
        MM_TYPE VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '中间件类型',
        MM_PATH VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '中间件路径',
        STATUS VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '停用标识',
        SERVER_USERNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '服务账号',
        SERVER_PASSWORD VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '账号密码',
        GROUP_TAG VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '分组标识',
        MM_CONTENT VARCHAR(1500) DEFAULT ' ' NOT NULL COMMENT '中间件脚本内容',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '组件英文名',
        PACKAGE_PATH VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '上传包路径',
        CONSTRAINT TEDNM02 PRIMARY KEY (NODE_ID)
    )COMMENT ='节点信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDNM03 (
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '归档标记',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
        CMPT_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL,
        CONTEXT_PATH VARCHAR(255) DEFAULT ' ' NOT NULL,
        PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL,
        PRIMARY KEY (CMPT_ENAME)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDNM03`
MODIFY COLUMN `CMPT_ENAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件英文名' FIRST ,
MODIFY COLUMN `CMPT_CNAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件中文名' AFTER `CMPT_ENAME`,
MODIFY COLUMN `CONTEXT_PATH`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '上下文' AFTER `CMPT_CNAME`,
MODIFY COLUMN `PROJECT_ENAME`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '项目英文名' AFTER `CONTEXT_PATH`,
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '归档标记' AFTER `PROJECT_ENAME`;


CREATE TABLE TEDNM04 (
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
        CMPT_MEMBER_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
        CMPT_MEMBER_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL,
        PRIMARY KEY (CMPT_ENAME, CMPT_MEMBER_ID, CMPT_MEMBER_TYPE)
    )COMMENT='发布信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDNM04`
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `CMPT_ENAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件英文名' AFTER `ARCHIVE_FLAG`,
MODIFY COLUMN `CMPT_MEMBER_ID`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件成员标识' AFTER `CMPT_ENAME`,
MODIFY COLUMN `CMPT_MEMBER_TYPE`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件成员类型（项目、一级、二级模块）' AFTER `CMPT_MEMBER_ID`;

CREATE TABLE TEDNM05 (
        CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
        CMPT_VERSION VARCHAR(255) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        PRIMARY KEY (CMPT_ENAME, CMPT_VERSION)
    )COMMENT='NGINX配置' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

ALTER TABLE `TEDNM05`
MODIFY COLUMN `CMPT_ENAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件英文名' FIRST ,
MODIFY COLUMN `CMPT_VERSION`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件版本号' AFTER `CMPT_ENAME`,
MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人' AFTER `CMPT_VERSION`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间' AFTER `REC_REVISOR`,
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记' AFTER `TENANT_ID`;

CREATE TABLE TEDNM06 (
        CONFIG_ID VARCHAR(50) NOT NULL,
        NAME VARCHAR(50),
        CONFIGURE VARCHAR(50),
        PATH VARCHAR(250),
        PRIMARY KEY (CONFIG_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDNM06`
MODIFY COLUMN `CONFIG_ID`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '配置ID' FIRST ,
MODIFY COLUMN `NAME`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '配置名称' AFTER `CONFIG_ID`,
MODIFY COLUMN `CONFIGURE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '原项目地址' AFTER `NAME`,
MODIFY COLUMN `PATH`  varchar(250) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '访问地址' AFTER `CONFIGURE`;

CREATE TABLE TEDPC01 (
        IMAGE_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
        IMAGE_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL,
        IMAGE_PATH VARCHAR(50) DEFAULT ' ' NOT NULL,
        REMARK VARCHAR(255) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        CONSTRAINT PK_TEDPC01 PRIMARY KEY (IMAGE_ID)
    );
ALTER TABLE `TEDPC01`
MODIFY COLUMN `IMAGE_ID`  varchar(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '图片编号' FIRST ,
MODIFY COLUMN `IMAGE_TYPE`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '图片分类' AFTER `IMAGE_ID`,
MODIFY COLUMN `IMAGE_PATH`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '图片保存路径' AFTER `IMAGE_TYPE`,
MODIFY COLUMN `REMARK`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '备注' AFTER `IMAGE_PATH`,
MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人（上传人）' AFTER `REMARK`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间（上传时间）' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='图片基本信息表';

ALTER TABLE `TEDPC01`
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `ARCHIVE_FLAG`;

CREATE TABLE TEDPC02 (
  `TAG_ID` varchar(36) COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '标签编号',
  `TAG_NAME` varchar(50) COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '标签名称',
  `REC_CREATOR` varchar(16) COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人',
  `REC_CREATE_TIME` varchar(17) COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间',
  `REC_REVISOR` varchar(16) COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人',
  `REC_REVISE_TIME` varchar(17) COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间',
  `ARCHIVE_FLAG` varchar(1) COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记',
  `TENANT_ID` varchar(64) COLLATE utf8_bin NOT NULL DEFAULT 'BDAS',
  PRIMARY KEY (`TAG_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='图片标签信息表';


ALTER TABLE `TEDPC02`
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `ARCHIVE_FLAG`;

CREATE TABLE TEDPC03 (
        IMAGE_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
        TAG_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        CONSTRAINT PK_TEDPC03 PRIMARY KEY (IMAGE_ID, TAG_ID)
    );


ALTER TABLE `TEDPC03`
MODIFY COLUMN `IMAGE_ID`  varchar(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '图片编号' FIRST ,
MODIFY COLUMN `TAG_ID`  varchar(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '标签编号' AFTER `IMAGE_ID`,
MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人' AFTER `TAG_ID`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='图片标签关系表';

ALTER TABLE `TEDPC03`
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `ARCHIVE_FLAG`;


CREATE TABLE TEDPC04 (
        IMAGE_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        CONSTRAINT PK_TEDPC04 PRIMARY KEY (IMAGE_ID, REC_CREATOR, REC_CREATE_TIME)
    );

ALTER TABLE `TEDPC04`
MODIFY COLUMN `IMAGE_ID`  varchar(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '图片编号' FIRST ,
MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人（下载人）' AFTER `IMAGE_ID`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间（下载时间）' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='图片下载信息表';

ALTER TABLE `TEDPC04`
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `ARCHIVE_FLAG`;

CREATE TABLE TEDPC05 (
        IMAGE_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        CONSTRAINT PK_TEDPC05 PRIMARY KEY (IMAGE_ID, REC_CREATOR, REC_CREATE_TIME)
    );


ALTER TABLE `TEDPC05`
MODIFY COLUMN `IMAGE_ID`  varchar(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '图片编号' FIRST ,
MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人（下载人）' AFTER `IMAGE_ID`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间（下载时间）' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `ARCHIVE_FLAG`;

CREATE TABLE TEDPI01 (
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        LEADER VARCHAR(256) DEFAULT ' ' NOT NULL,
        PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
        PROJECT_CNAME VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '项目中文名',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        PROJECT_DESC VARCHAR(1500) DEFAULT ' ' NOT NULL COMMENT '项目描述',
        CONSTRAINT PK_TEDPI01 PRIMARY KEY (PROJECT_ENAME)
    )COMMENT ='项目信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDPI02 (
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
        MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '模块英文名称',
        MODULE_CNAME_1 VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '项目中文名',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        INDEX_SPACE_ENAME VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '索引空间英文名称',
        TABLE_SPACE_ENAME VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '表空间英文名称',
        PRIMARY KEY (MODULE_ENAME_1)
    )COMMENT ='项目模块信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDPI03 (
        MODULE_ENAME_2 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '二级模块英文名',
        MODULE_CNAME_2 VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '二级模块',
        MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '一级模块英文名',
        REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
        REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
        REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
        REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        PRIMARY KEY (MODULE_ENAME_2)
    )COMMENT='版本表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDPI10 (
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
        TREE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '节点树英文名',
        NODE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '节点英文名',
        NODE_CNAME VARCHAR(80) DEFAULT ' ' NOT NULL COMMENT '节点中文名',
        NODE_TYPE DECIMAL(1,0) DEFAULT 0 NOT NULL COMMENT '节点类型',
        NODE_URL VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '节点URL',
        NODE_SORT_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '节点排序标识',
        NODE_PARAM VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '节点参数配置',
        NODE_IMAGE_PATH VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '节点图片路径',
		TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
        CONSTRAINT PK_TEDPI10 PRIMARY KEY (TREE_ENAME, NODE_ENAME)
    )COMMENT='项目菜单节点信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEIIT00 (
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        ITEM_SEQ DECIMAL(9,0) DEFAULT 0 NOT NULL COMMENT '字段索引号',
        ITEM_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '字段英文名',
        ITEM_CNAME VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '字段中文名',
        ITEM_GRADE VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '字段等级',
        ITEM_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '字段类型',
        ITEM_LEN VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '字段长度',
        ITEM_UNIT VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '字段单位',
        ITEM_UPPER_VALUE VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '字段上限值',
        ITEM_LOWER_VALUE VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '字段下限值',
        ITEM_DEFAULT_VALUE VARCHAR(20) DEFAULT '' NOT NULL COMMENT '字段缺省值',
        ITEM_ALLOW_NULL VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '字段是否可空',
        REMARK VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '备注',
        CHECK_RESULT VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '检查结果',
		TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
        CONSTRAINT PK_TEIIT00 PRIMARY KEY (ITEM_SEQ),
        CONSTRAINT TEIIT00_IDX1 UNIQUE (ITEM_ENAME, ITEM_CNAME, ITEM_TYPE, ITEM_LEN,
        ITEM_DEFAULT_VALUE, ITEM_ALLOW_NULL)
    )ROW_FORMAT=DYNAMIC COMMENT='信息项定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEIIT10 (
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
        ITEM_ENAME_KEY VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '字段英文字根',
        ITEM_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '字段英文名',
        ITEM_CNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '字段中文名',
        ITEM_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '字段类型',
		TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
        CONSTRAINT PK_TEIIT10 PRIMARY KEY (ITEM_ENAME_KEY, ITEM_CNAME)
    )ROW_FORMAT=DYNAMIC COMMENT='信息项字根定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;



CREATE TABLE TEUDM01 (
  DIR_ID          VARCHAR(36) DEFAULT ' '  NOT NULL,
  DIR_ENAME       VARCHAR(256) DEFAULT ' '  NOT NULL,
  DIR_CNAME       VARCHAR(64) DEFAULT ' '  NOT NULL,
  PARENT_ID       VARCHAR(36) DEFAULT ' '  NOT NULL,
  IS_LEAF         VARCHAR(1) DEFAULT ' '   NOT NULL,
  DIR_PATH        VARCHAR(512) DEFAULT ' ' NOT NULL,
  PROJECT_ENAME   VARCHAR(250) DEFAULT ' ' NOT NULL,
  MODULE_ENAME    VARCHAR(50) DEFAULT ' '  NOT NULL,
  REC_CREATOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
  REC_REVISOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
  ARCHIVE_FLAG    VARCHAR(1) DEFAULT ' '   NOT NULL,
  REAL_PATH       VARCHAR(512),
  TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
  PRIMARY KEY (DIR_ID, PROJECT_ENAME, MODULE_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEUDM02 (
  DOC_ID          VARCHAR(36) DEFAULT ' '  NOT NULL,
  DIR_ID          VARCHAR(36) DEFAULT ' '  NOT NULL,
  DOC_NAME        VARCHAR(128) DEFAULT ' ' NOT NULL,
  CHG_NAME        VARCHAR(128) DEFAULT ' ' NOT NULL,
  DOC_SIZE        DECIMAL(16, 0)           NOT NULL,
  DOC_TAG         VARCHAR(64) DEFAULT ' '  NOT NULL,
  REC_CREATOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
  REC_REVISOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
  ARCHIVE_FLAG    VARCHAR(1) DEFAULT ' '   NOT NULL,
  TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
  PRIMARY KEY (DOC_ID)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEWPD00 (
        ID VARCHAR(64) NOT NULL,
        CATEGORY_KEY VARCHAR(255) NOT NULL,
        CATEGORY_NAME VARCHAR(255) DEFAULT NULL,
        PARENT_ID VARCHAR(64) NOT NULL,
        DESCRIPTION VARCHAR(2048) DEFAULT NULL,
        REC_CREATOR VARCHAR(255) NOT NULL,
        REC_CREATE_TIME VARCHAR(14) NOT NULL,
        REC_REVISOR VARCHAR(255) NOT NULL,
        REC_REVISE_TIME VARCHAR(14) NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) NOT NULL,
        CATEGORY_TYPE VARCHAR(10) DEFAULT NULL,
        PRIMARY KEY (ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD00`
MODIFY COLUMN `ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程目录表ID' FIRST ,
MODIFY COLUMN `CATEGORY_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程目录表编码' AFTER `ID`,
MODIFY COLUMN `CATEGORY_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程目录表名称' AFTER `CATEGORY_KEY`,
MODIFY COLUMN `PARENT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '父目录的标识' AFTER `CATEGORY_NAME`,
MODIFY COLUMN `DESCRIPTION`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程目录的描述信息' AFTER `PARENT_ID`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者' AFTER `DESCRIPTION`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `CATEGORY_TYPE`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程目录分类' AFTER `ARCHIVE_FLAG`,
COMMENT='流程目录表';


CREATE TABLE TEWPD01 (
        FORM VARCHAR(200) DEFAULT NULL,
        PROCESS_DEF_ID VARCHAR(64) NOT NULL,
        ACT_PROC_DEF_ID VARCHAR(64) DEFAULT NULL,
        PROCESS_KEY VARCHAR(255) NOT NULL,
        PROCESS_NAME VARCHAR(255) NOT NULL,
        DESCRIPTION VARCHAR(2048) NOT NULL,
        CATEGORY VARCHAR(64) NOT NULL,
        DEPLOYTIME VARCHAR(32) NOT NULL,
        AUTHOR VARCHAR(32) NOT NULL,
        PROCESS_VERSION VARCHAR(32) NOT NULL,
        STATE VARCHAR(32) NOT NULL,
        MXGRAPH_XML TEXT,
        REC_CREATOR VARCHAR(255) NOT NULL,
        REC_CREATE_TIME VARCHAR(14) NOT NULL,
        REC_REVISOR VARCHAR(255) NOT NULL,
        REC_REVISE_TIME VARCHAR(14) NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) NOT NULL,
        BPMN_XML TEXT,
        PRIMARY KEY (PROCESS_DEF_ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD01`
MODIFY COLUMN `FORM`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单' FIRST ,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义的标识' AFTER `FORM`,
MODIFY COLUMN `ACT_PROC_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '工作流系统表ID' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `PROCESS_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义编码' AFTER `ACT_PROC_DEF_ID`,
MODIFY COLUMN `PROCESS_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义名称' AFTER `PROCESS_KEY`,
MODIFY COLUMN `DESCRIPTION`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义的描述信息' AFTER `PROCESS_NAME`,
MODIFY COLUMN `CATEGORY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义的所在目录ID' AFTER `DESCRIPTION`,
MODIFY COLUMN `DEPLOYTIME`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '创建时间' AFTER `CATEGORY`,
MODIFY COLUMN `AUTHOR`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '创建人' AFTER `DEPLOYTIME`,
MODIFY COLUMN `PROCESS_VERSION`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '版本' AFTER `AUTHOR`,
MODIFY COLUMN `STATE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '状态' AFTER `PROCESS_VERSION`,
MODIFY COLUMN `MXGRAPH_XML`  text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '流程定义MVL' AFTER `STATE`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者' AFTER `MXGRAPH_XML`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `BPMN_XML`  text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'BPMN流程定义XML' AFTER `ARCHIVE_FLAG`,
COMMENT='流程定义表';


CREATE TABLE TEWPD02 (
        TRANSITION_DEF_ID VARCHAR(64) NOT NULL,
        PROCESS_DEF_ID VARCHAR(64) NOT NULL,
        TRANSITION_KEY VARCHAR(255) NOT NULL,
        TRANSITION_NAME VARCHAR(255) NOT NULL,
        TRANSITION_TYPE VARCHAR(64) NOT NULL,
        CONDITION_NAME VARCHAR(255) NOT NULL,
        CONDITION_CODE VARCHAR(255) NOT NULL,
        BEGIN_NODE_ID VARCHAR(64) NOT NULL,
        END_NODE_ID VARCHAR(64) NOT NULL,
        REC_CREATOR VARCHAR(255) NOT NULL,
        REC_CREATE_TIME VARCHAR(14) NOT NULL,
        REC_REVISOR VARCHAR(255) NOT NULL,
        REC_REVISE_TIME VARCHAR(14) NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) NOT NULL,
        PRIMARY KEY (TRANSITION_DEF_ID, PROCESS_DEF_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD02`
MODIFY COLUMN `TRANSITION_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '转移定义的标识' FIRST ,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义的标识' AFTER `TRANSITION_DEF_ID`,
MODIFY COLUMN `TRANSITION_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '转移编码' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `TRANSITION_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '转移标签显示名称' AFTER `TRANSITION_KEY`,
MODIFY COLUMN `TRANSITION_TYPE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '转移类型' AFTER `TRANSITION_NAME`,
MODIFY COLUMN `CONDITION_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '条件名称' AFTER `TRANSITION_TYPE`,
MODIFY COLUMN `CONDITION_CODE`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '条件' AFTER `CONDITION_NAME`,
MODIFY COLUMN `BEGIN_NODE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '起始活动定义标识' AFTER `CONDITION_CODE`,
MODIFY COLUMN `END_NODE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '结束活动定义标识' AFTER `BEGIN_NODE_ID`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者' AFTER `END_NODE_ID`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='转移定义表';


CREATE TABLE TEWPD03 (
        NODE_KEY VARCHAR(255) DEFAULT NULL,
        NODE_DEF_ID VARCHAR(64) NOT NULL,
        PROCESS_DEF_ID VARCHAR(64) NOT NULL,
        PROCESS_NAME VARCHAR(255) NOT NULL,
        NODE_NAME VARCHAR(255) NOT NULL,
        DESCRIPTION VARCHAR(2048) NOT NULL,
        ISDEFFERED DECIMAL(11,0) NOT NULL,
        NODE_TYPE VARCHAR(255) NOT NULL,
        WORK_ADDRESS_TYPE VARCHAR(2) NOT NULL,
        WORK_ADDRESS VARCHAR(1024) NOT NULL,
        WORKITEM_HANDLE_TYPE VARCHAR(2) NOT NULL,
        SET_TIME_OUT VARCHAR(2) NOT NULL,
        TIME_OUT DECIMAL(11,0) NOT NULL,
        TIME_OUT_POLICY VARCHAR(2048) NOT NULL,
        BACK_JSCRIPT TEXT,
        SUBMIT_METHOD VARCHAR(1048) DEFAULT NULL,
        FORE_JSCRIPT TEXT,
        XMLS TEXT NOT NULL,
        REC_CREATOR VARCHAR(255) NOT NULL,
        REC_CREATE_TIME VARCHAR(14) NOT NULL,
        REC_REVISOR VARCHAR(255) NOT NULL,
        REC_REVISE_TIME VARCHAR(14) NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) NOT NULL,
        PRIMARY KEY (NODE_DEF_ID, PROCESS_DEF_ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD03`
MODIFY COLUMN `NODE_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '活动定义编码' FIRST ,
MODIFY COLUMN `NODE_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '活动定义标识' AFTER `NODE_KEY`,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义的标识' AFTER `NODE_DEF_ID`,
MODIFY COLUMN `PROCESS_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义的编码' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `NODE_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '活动定义的显示名称' AFTER `PROCESS_NAME`,
MODIFY COLUMN `DESCRIPTION`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '活动定义的描述信息' AFTER `NODE_NAME`,
MODIFY COLUMN `ISDEFFERED`  decimal(11,0) NOT NULL COMMENT '是否延迟分配' AFTER `DESCRIPTION`,
MODIFY COLUMN `NODE_TYPE`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '活动定义的类型' AFTER `ISDEFFERED`,
MODIFY COLUMN `WORK_ADDRESS_TYPE`  varchar(2) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '业务地址的类型' AFTER `NODE_TYPE`,
MODIFY COLUMN `WORK_ADDRESS`  varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '业务地址' AFTER `WORK_ADDRESS_TYPE`,
MODIFY COLUMN `WORKITEM_HANDLE_TYPE`  varchar(2) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '工作项处理的类型' AFTER `WORK_ADDRESS`,
MODIFY COLUMN `SET_TIME_OUT`  varchar(2) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '是否设置延时' AFTER `WORKITEM_HANDLE_TYPE`,
MODIFY COLUMN `TIME_OUT`  decimal(11,0) NOT NULL COMMENT '超过时限' AFTER `SET_TIME_OUT`,
MODIFY COLUMN `TIME_OUT_POLICY`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '超过策略' AFTER `TIME_OUT`,
MODIFY COLUMN `BACK_JSCRIPT`  text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '后台脚本' AFTER `TIME_OUT_POLICY`,
MODIFY COLUMN `SUBMIT_METHOD`  varchar(1048) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '提交方法' AFTER `BACK_JSCRIPT`,
MODIFY COLUMN `FORE_JSCRIPT`  text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '前台脚本' AFTER `SUBMIT_METHOD`,
MODIFY COLUMN `XMLS`  text CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '活动特性的XML描述' AFTER `FORE_JSCRIPT`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者' AFTER `XMLS`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='活动定义表';


CREATE TABLE TEWPD04 (
        NODE_NAME VARCHAR(255) DEFAULT NULL,
        NODE_KEY VARCHAR(255) DEFAULT NULL,
        PROCESS_DEF_ID VARCHAR(64) DEFAULT NULL,
        ID VARCHAR(64) NOT NULL,
        ACT_PROCESS_DEF_ID VARCHAR(64) DEFAULT NULL,
        NODE_ID VARCHAR(64) DEFAULT NULL,
        VOTE_AMOUNT DECIMAL(11,0) NOT NULL,
        DECIDE_TYPE VARCHAR(2) NOT NULL,
        VOTE_TYPE VARCHAR(2) NOT NULL,
        REC_CREATOR VARCHAR(255) DEFAULT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT NULL,
        REC_REVISOR VARCHAR(255) DEFAULT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT NULL,
        PRIMARY KEY (ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD04`
MODIFY COLUMN `NODE_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '节点名称' FIRST ,
MODIFY COLUMN `NODE_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '节点KEY' AFTER `NODE_NAME`,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程定义ID' AFTER `NODE_KEY`,
MODIFY COLUMN `ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '会签配置ID' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `ACT_PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'Activity流程定义ID' AFTER `ID`,
MODIFY COLUMN `NODE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '节点ID' AFTER `ACT_PROCESS_DEF_ID`,
MODIFY COLUMN `VOTE_AMOUNT`  decimal(11,0) NOT NULL COMMENT '票数' AFTER `NODE_ID`,
MODIFY COLUMN `DECIDE_TYPE`  varchar(2) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '决策类型' AFTER `VOTE_AMOUNT`,
MODIFY COLUMN `VOTE_TYPE`  varchar(2) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '投票类型' AFTER `DECIDE_TYPE`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' AFTER `VOTE_TYPE`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='会签配置信息表';


CREATE TABLE TEWPD05 (
        ID VARCHAR(36) NOT NULL,
        NAME VARCHAR(255) NOT NULL,
        TYPE VARCHAR(10) NOT NULL,
        VARVALUE VARCHAR(255) DEFAULT ' ' NOT NULL,
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
        DESCRIPTION VARCHAR(255) DEFAULT NULL,
        PRIMARY KEY (ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD05`
MODIFY COLUMN `ID`  varchar(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'ID' FIRST ,
MODIFY COLUMN `NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '变量名称' AFTER `ID`,
MODIFY COLUMN `TYPE`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '变量类型' AFTER `NAME`,
MODIFY COLUMN `VARVALUE`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '变量值' AFTER `TYPE`,
MODIFY COLUMN `REC_CREATOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录创建责任者' AFTER `VARVALUE`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `DESCRIPTION`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '描述' AFTER `ARCHIVE_FLAG`,
COMMENT='扩展属性表';



CREATE TABLE TEWPD06 (
        REC_CREATOR VARCHAR(256) DEFAULT '' NOT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT '' NOT NULL,
        REC_REVISOR VARCHAR(256) DEFAULT '' NOT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT '' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT '' NOT NULL,
        DEPUTY_ID VARCHAR(64) DEFAULT '' NOT NULL,
        DELEGATE_START_DATE VARCHAR(32) DEFAULT '' NOT NULL,
        DELEGATE_END_DATE VARCHAR(32) DEFAULT '' NOT NULL,
        F_REAL_ENDT VARCHAR(32) DEFAULT '' NOT NULL,
        DELEGATE_TYPE VARCHAR(32) DEFAULT '' NOT NULL,
        DELEGATE_DEPT VARCHAR(32) DEFAULT '' NOT NULL,
        DELEGATE_ROLE VARCHAR(64) DEFAULT '' NOT NULL,
        DELEGATER VARCHAR(32) DEFAULT '' NOT NULL,
        DELEGATER_NAME VARCHAR(64) DEFAULT ' ' NOT NULL,
        ACCEPT_DEPT VARCHAR(32) DEFAULT '' NOT NULL,
        ACCEPT_ROLE VARCHAR(64) DEFAULT '' NOT NULL,
        ACCEPTER VARCHAR(32) DEFAULT '' NOT NULL,
        ACCEPTER_NAME VARCHAR(64) DEFAULT '' NOT NULL,
        DELEGATE_SCOPE_TYPE VARCHAR(32) DEFAULT '' NOT NULL,
        PROCESS_DEF_CODE VARCHAR(2048) DEFAULT '' NOT NULL,
        PROCESS_NODE_CODE VARCHAR(2048) DEFAULT '' NOT NULL,
        ENABLE_STATUS VARCHAR(48) DEFAULT '' NOT NULL,
        REMARK VARCHAR(1024) DEFAULT '' NOT NULL,
        PRIMARY KEY (DEPUTY_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD06`
MODIFY COLUMN `REC_CREATOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '记录创建责任者' FIRST ,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `DEPUTY_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '代理ID' AFTER `ARCHIVE_FLAG`,
MODIFY COLUMN `DELEGATE_START_DATE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '代理开始时间' AFTER `DEPUTY_ID`,
MODIFY COLUMN `DELEGATE_END_DATE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '代理结束时间' AFTER `DELEGATE_START_DATE`,
MODIFY COLUMN `F_REAL_ENDT`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '代理实际结束时间' AFTER `DELEGATE_END_DATE`,
MODIFY COLUMN `DELEGATE_TYPE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '委托类型' AFTER `F_REAL_ENDT`,
MODIFY COLUMN `DELEGATE_DEPT`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '委托部门' AFTER `DELEGATE_TYPE`,
MODIFY COLUMN `DELEGATE_ROLE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '委托角色' AFTER `DELEGATE_DEPT`,
MODIFY COLUMN `DELEGATER`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '委托人ID' AFTER `DELEGATE_ROLE`,
MODIFY COLUMN `DELEGATER_NAME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '委托人名称' AFTER `DELEGATER`,
MODIFY COLUMN `ACCEPT_DEPT`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '受理部门' AFTER `DELEGATER_NAME`,
MODIFY COLUMN `ACCEPT_ROLE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '受理角色' AFTER `ACCEPT_DEPT`,
MODIFY COLUMN `ACCEPTER`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '受理人ID' AFTER `ACCEPT_ROLE`,
MODIFY COLUMN `ACCEPTER_NAME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '受理人名称' AFTER `ACCEPTER`,
MODIFY COLUMN `DELEGATE_SCOPE_TYPE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '委托流程范围类型' AFTER `ACCEPTER_NAME`,
MODIFY COLUMN `PROCESS_DEF_CODE`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '流程定义编码' AFTER `DELEGATE_SCOPE_TYPE`,
MODIFY COLUMN `PROCESS_NODE_CODE`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '流程节点编码' AFTER `PROCESS_DEF_CODE`,
MODIFY COLUMN `ENABLE_STATUS`  varchar(48) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '启用状态' AFTER `PROCESS_NODE_CODE`,
MODIFY COLUMN `REMARK`  varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '代理说明' AFTER `ENABLE_STATUS`,
COMMENT='流程代理表';


CREATE TABLE TEWPD07 (
        DEPUTY_PATH_ID VARCHAR(64) NOT NULL,
        DEPUTY_DATE VARCHAR(8) DEFAULT '',
        DELEGATE_TYPE VARCHAR(32) DEFAULT '',
        DELEGATE_DEPT VARCHAR(32) DEFAULT '',
        DELEGATE_ROLE VARCHAR(64) DEFAULT '',
        DELEGATER VARCHAR(32) DEFAULT '',
        DELEGATER_FULLNAME VARCHAR(64),
        DELEGATE_SCOPE_TYPE VARCHAR(32) DEFAULT '',
        ACCEPT_DEPT VARCHAR(32) DEFAULT '',
        ACCEPT_ROLE VARCHAR(64) DEFAULT '',
        ACCEPTER VARCHAR(32) DEFAULT '',
        ACCEPTER_FULLNAME VARCHAR(64),
        COMPLETE_PATH VARCHAR(2048) DEFAULT '',
        DEPUTY_ID VARCHAR(64) DEFAULT '',
        ENABLE_STATUS VARCHAR(10) DEFAULT '' NOT NULL,
        DEPUTY_START_TIME VARCHAR(17),
        DEPUTY_END_TIME VARCHAR(17),
        CONSTRAINT PK_TEWPD07 PRIMARY KEY (DEPUTY_PATH_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD07`
MODIFY COLUMN `DEPUTY_PATH_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '代理路径ID' FIRST ,
MODIFY COLUMN `DEPUTY_DATE`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理日期' AFTER `DEPUTY_PATH_ID`,
MODIFY COLUMN `DELEGATE_TYPE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '委托类型' AFTER `DEPUTY_DATE`,
MODIFY COLUMN `DELEGATE_DEPT`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '委托部门' AFTER `DELEGATE_TYPE`,
MODIFY COLUMN `DELEGATE_ROLE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '委托角色' AFTER `DELEGATE_DEPT`,
MODIFY COLUMN `DELEGATER`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '委托人' AFTER `DELEGATE_ROLE`,
MODIFY COLUMN `DELEGATER_FULLNAME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '委托人姓名' AFTER `DELEGATER`,
MODIFY COLUMN `DELEGATE_SCOPE_TYPE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '委托流程范围类型' AFTER `DELEGATER_FULLNAME`,
MODIFY COLUMN `ACCEPT_DEPT`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '受理部门' AFTER `DELEGATE_SCOPE_TYPE`,
MODIFY COLUMN `ACCEPT_ROLE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '受理角色' AFTER `ACCEPT_DEPT`,
MODIFY COLUMN `ACCEPTER`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '受理人' AFTER `ACCEPT_ROLE`,
MODIFY COLUMN `ACCEPTER_FULLNAME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '接收人姓名' AFTER `ACCEPTER`,
MODIFY COLUMN `COMPLETE_PATH`  varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理完整路径' AFTER `ACCEPTER_FULLNAME`,
MODIFY COLUMN `DEPUTY_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理ID' AFTER `COMPLETE_PATH`,
MODIFY COLUMN `ENABLE_STATUS`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '代理生效时间' AFTER `DEPUTY_ID`,
MODIFY COLUMN `DEPUTY_START_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '代理失效时间' AFTER `ENABLE_STATUS`,
COMMENT='代理路径信息表';
ALTER TABLE `TEWPD07`
MODIFY COLUMN `ENABLE_STATUS`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' AFTER `DEPUTY_ID`,
MODIFY COLUMN `DEPUTY_START_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '代理生效时间' AFTER `ENABLE_STATUS`,
MODIFY COLUMN `DEPUTY_END_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '代理失效时间' AFTER `DEPUTY_START_TIME`;


CREATE TABLE TEWPD08 (
        DEPUTY_PATH_ID VARCHAR(64) NOT NULL,
        DEPUTY_PROCESS_CODE VARCHAR(64) DEFAULT '',
        DEPUTY_NODE_CODE VARCHAR(64) DEFAULT ''
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD08`
MODIFY COLUMN `DEPUTY_PATH_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '代理路径ID' FIRST ,
MODIFY COLUMN `DEPUTY_PROCESS_CODE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理流程编码' AFTER `DEPUTY_PATH_ID`,
MODIFY COLUMN `DEPUTY_NODE_CODE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理节点编码' AFTER `DEPUTY_PROCESS_CODE`,
COMMENT='代理路径继承集合表';

CREATE TABLE TEWPD09 (
        DEPUTY_PATH_ID VARCHAR(64),
        ACCEPTED_SCOPE_TYPE VARCHAR(32) DEFAULT '',
        DEPUTY_PROCESS_CODE VARCHAR(64) DEFAULT '',
        DEPUTY_NODE_CODE VARCHAR(64) DEFAULT ''
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEWPD09`
MODIFY COLUMN `DEPUTY_PATH_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '代理路径ID ' FIRST ,
MODIFY COLUMN `ACCEPTED_SCOPE_TYPE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '被继承流程范围类型 ' AFTER `DEPUTY_PATH_ID`,
MODIFY COLUMN `DEPUTY_PROCESS_CODE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理流程编码 ' AFTER `ACCEPTED_SCOPE_TYPE`,
MODIFY COLUMN `DEPUTY_NODE_CODE`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '代理节点编码 ' AFTER `DEPUTY_PROCESS_CODE`,
COMMENT='代理路径被继承集合表 ';


CREATE TABLE TEWPD10 (
  REC_CREATOR VARCHAR(8) DEFAULT NULL COMMENT '记录创建责任者',
  REC_CREATE_TIME VARCHAR(17) DEFAULT NULL COMMENT '记录创建时刻',
  REC_REVISOR VARCHAR(8) DEFAULT NULL COMMENT '记录修改责任者',
  REC_REVISE_TIME VARCHAR(17) DEFAULT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT NULL COMMENT '归档标记',
  ARCHIVE_TIME VARCHAR(17) DEFAULT NULL COMMENT '归档时刻',
  HIS_ID VARCHAR(64) DEFAULT NULL COMMENT '履历ID',
  PROCESS_INSTANCE_ID VARCHAR(64) DEFAULT NULL COMMENT '流程实例ID',
  TASK_ID VARCHAR(64) DEFAULT NULL COMMENT '任务ID',
  HIS_TYPE VARCHAR(32) DEFAULT NULL COMMENT '履历类型',
  FROM_USER_ID VARCHAR(64) DEFAULT NULL COMMENT '来源方ID',
  FROM_USER_NAME VARCHAR(64) DEFAULT NULL COMMENT '来源方姓名',
  TO_USER_ID VARCHAR(64) DEFAULT NULL COMMENT '接收方ID',
  TO_USER_NAME VARCHAR(64) DEFAULT NULL COMMENT '接收方姓名',
  LOG_INFO VARCHAR(2048) DEFAULT NULL COMMENT '日志信息',
  LOG_TIME VARCHAR(17) DEFAULT NULL COMMENT '记录时间'
) ENGINE=INNODB DEFAULT CHARSET=LATIN1 COMMENT='委托代理履历';
ALTER TABLE `TEWPD10`
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`;

CREATE TABLE TEWPD11 (
        REC_CREATOR VARCHAR(8),
        REC_CREATE_TIME VARCHAR(17),
        REC_REVISOR VARCHAR(8),
        REC_REVISE_TIME VARCHAR(17),
        ARCHIVE_FLAG VARCHAR(1),
        ARCHIVE_TIME VARCHAR(17),
        MAPPER_ID VARCHAR(100) NOT NULL,
        PROCESS_KEY VARCHAR(64),
        START_APP VARCHAR(20),
        START_MODULE VARCHAR(20),
        START_MODULE_NAME VARCHAR(100),
        NODE_FORM_ID VARCHAR(50),
        APP_FORM_CODE VARCHAR(50),
        REMARK VARCHAR(500),
        START_BUSSTYPE VARCHAR(50),
        START_BUSSTYPE_NAME VARCHAR(200),
        FORM_TYPE VARCHAR(20),
        CONSTRAINT PK_TEWPD11 PRIMARY KEY (MAPPER_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD11`
MODIFY COLUMN `REC_CREATOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' FIRST ,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `ARCHIVE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档时刻' AFTER `ARCHIVE_FLAG`,
MODIFY COLUMN `MAPPER_ID`  varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '映射ID' AFTER `ARCHIVE_TIME`,
MODIFY COLUMN `PROCESS_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程定义代码' AFTER `MAPPER_ID`,
MODIFY COLUMN `START_APP`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起系统代码' AFTER `PROCESS_KEY`,
MODIFY COLUMN `START_MODULE`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起模块代码' AFTER `START_APP`,
MODIFY COLUMN `START_MODULE_NAME`  varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起模块名称' AFTER `START_MODULE`,
MODIFY COLUMN `NODE_FORM_ID`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程节点表单ID' AFTER `START_MODULE_NAME`,
MODIFY COLUMN `APP_FORM_CODE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '应用页面代码' AFTER `NODE_FORM_ID`,
MODIFY COLUMN `REMARK`  varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注' AFTER `APP_FORM_CODE`,
MODIFY COLUMN `START_BUSSTYPE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起业务类型代码' AFTER `REMARK`,
MODIFY COLUMN `START_BUSSTYPE_NAME`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起业务类型名称' AFTER `START_BUSSTYPE`,
MODIFY COLUMN `FORM_TYPE`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表单类型' AFTER `START_BUSSTYPE_NAME`,
COMMENT='流程表单映射表';

CREATE TABLE TEWPD12 (
        REC_CREATOR VARCHAR(8),
        REC_CREATE_TIME VARCHAR(17),
        REC_REVISOR VARCHAR(8),
        REC_REVISE_TIME VARCHAR(17),
        ARCHIVE_FLAG VARCHAR(1),
        ARCHIVE_TIME VARCHAR(17),
        MAPPER_ID VARCHAR(100) NOT NULL,
        PROCESS_KEY VARCHAR(64),
        START_APP VARCHAR(20),
        START_MODULE VARCHAR(20),
        START_MODULE_NAME VARCHAR(100),
        NODE_SERVICE_ID VARCHAR(50),
        APP_SERVICE_CODE VARCHAR(50),
        REMARK VARCHAR(500),
        START_BUSSTYPE VARCHAR(50),
        START_BUSSTYPE_NAME VARCHAR(200),
        SERVICE_TYPE VARCHAR(20),
        CONSTRAINT PK_TEWPD12 PRIMARY KEY (MAPPER_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD12`
MODIFY COLUMN `REC_CREATOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' FIRST ,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `ARCHIVE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档时刻' AFTER `ARCHIVE_FLAG`,
MODIFY COLUMN `MAPPER_ID`  varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '映射ID' AFTER `ARCHIVE_TIME`,
MODIFY COLUMN `PROCESS_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程定义代码' AFTER `MAPPER_ID`,
MODIFY COLUMN `START_APP`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起系统代码' AFTER `PROCESS_KEY`,
MODIFY COLUMN `START_MODULE`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起模块代码' AFTER `START_APP`,
MODIFY COLUMN `START_MODULE_NAME`  varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起模块名称' AFTER `START_MODULE`,
MODIFY COLUMN `NODE_SERVICE_ID`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程节点服务ID' AFTER `START_MODULE_NAME`,
MODIFY COLUMN `APP_SERVICE_CODE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '应用服务代码' AFTER `NODE_SERVICE_ID`,
MODIFY COLUMN `REMARK`  varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注' AFTER `APP_SERVICE_CODE`,
MODIFY COLUMN `START_BUSSTYPE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起业务类型代码' AFTER `REMARK`,
MODIFY COLUMN `START_BUSSTYPE_NAME`  varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '发起业务类型名称' AFTER `START_BUSSTYPE`,
MODIFY COLUMN `SERVICE_TYPE`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '服务类型' AFTER `START_BUSSTYPE_NAME`,
COMMENT='流程服务映射表';

CREATE TABLE TEWPD13 (
        REC_CREATOR VARCHAR(8),
        REC_CREATE_TIME VARCHAR(17),
        REC_REVISOR VARCHAR(8),
        REC_REVISE_TIME VARCHAR(17),
        ARCHIVE_FLAG VARCHAR(1),
        ARCHIVE_TIME VARCHAR(17),
        USER_ID VARCHAR(50),
        PROCESS_KEY VARCHAR(64),
        PRIORITY SMALLINT
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD13`
MODIFY COLUMN `REC_CREATOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' FIRST ,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `ARCHIVE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档时刻' AFTER `ARCHIVE_FLAG`,
MODIFY COLUMN `USER_ID`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '用户ID' AFTER `ARCHIVE_TIME`,
MODIFY COLUMN `PROCESS_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程定义代码' AFTER `USER_ID`,
MODIFY COLUMN `PRIORITY`  smallint(6) NULL DEFAULT NULL COMMENT '优先级' AFTER `PROCESS_KEY`,
COMMENT='我最关心流程';




CREATE TABLE TEWPD14 (
        REC_CREATOR VARCHAR(8),
        REC_CREATE_TIME VARCHAR(17),
        REC_REVISOR VARCHAR(8),
        REC_REVISE_TIME VARCHAR(17),
        ARCHIVE_FLAG VARCHAR(1),
        ARCHIVE_TIME VARCHAR(17),
        PROCESS_KEY VARCHAR(64),
        OBJECT_TYPE VARCHAR(8),
        OBJECT_CODE VARCHAR(32),
        OBJECT_NAME VARCHAR(32)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPD14`
MODIFY COLUMN `ARCHIVE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档时刻' FIRST ,
MODIFY COLUMN `PROCESS_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程定义代码' AFTER `ARCHIVE_TIME`,
MODIFY COLUMN `OBJECT_TYPE`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '对象类型' AFTER `PROCESS_KEY`,
MODIFY COLUMN `OBJECT_CODE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '对象代码' AFTER `OBJECT_TYPE`,
MODIFY COLUMN `OBJECT_NAME`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '对象名称' AFTER `OBJECT_CODE`,
MODIFY COLUMN `REC_CREATOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' AFTER `OBJECT_NAME`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(8) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='流程参与者';

CREATE TABLE TEWPD15 (
        REC_CREATOR VARCHAR(8),
        REC_CREATE_TIME VARCHAR(17),
        REC_REVISOR VARCHAR(8),
        REC_REVISE_TIME VARCHAR(17),
        ARCHIVE_FLAG VARCHAR(1),
        ARCHIVE_TIME VARCHAR(17),
        HIS_ID VARCHAR(64),
        PROCESS_INSTANCE_ID VARCHAR(64),
        TASK_ID VARCHAR(64),
        HIS_TYPE VARCHAR(32),
        FROM_USER_ID VARCHAR(64),
        FROM_USER_NAME VARCHAR(64),
        TO_USER_ID VARCHAR(64),
        TO_USER_NAME VARCHAR(64),
        LOG_INFO VARCHAR(2048),
        LOG_TIME VARCHAR(17)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEWPG00 (
        TASK_ID VARCHAR(64) NOT NULL,
        ASSIGNEE_ID VARCHAR(64) NOT NULL,
        NODE_DEF_ID VARCHAR(64) NOT NULL,
        NODE_KEY VARCHAR(255) NOT NULL,
        NODE_NAME VARCHAR(255) NOT NULL,
        ASSIGNEE_FULLNAME VARCHAR(255) NOT NULL,
        PROCESS_INSTANCE_ID VARCHAR(64) NOT NULL,
        PROCESS_DEF_ID VARCHAR(64) NOT NULL,
        PROCESS_KEY VARCHAR(255) NOT NULL,
        PROCESS_NAME VARCHAR(255) NOT NULL,
        GROUP_ID VARCHAR(64) NOT NULL,
        GROUP_NAME VARCHAR(255) NOT NULL,
        CREATE_TIME VARCHAR(255) NOT NULL,
        DUE_DATE VARCHAR(255) NOT NULL,
        MONITOR_STATUS VARCHAR(32) NOT NULL,
        IS_ACTIVITY VARCHAR(32) NOT NULL,
        REC_CREATOR VARCHAR(255) NOT NULL,
        REC_CREATE_TIME VARCHAR(14) NOT NULL,
        REC_REVISOR VARCHAR(255) NOT NULL,
        REC_REVISE_TIME VARCHAR(14) NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) NOT NULL,
        PRIMARY KEY (TASK_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

ALTER TABLE `TEWPG00`
MODIFY COLUMN `TASK_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '任务ID' FIRST ,
MODIFY COLUMN `ASSIGNEE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '负责人ID' AFTER `TASK_ID`,
MODIFY COLUMN `NODE_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '节点定义ID' AFTER `ASSIGNEE_ID`,
MODIFY COLUMN `NODE_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '任务定义编码' AFTER `NODE_DEF_ID`,
MODIFY COLUMN `NODE_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '任务名称' AFTER `NODE_KEY`,
MODIFY COLUMN `ASSIGNEE_FULLNAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '负责人姓名' AFTER `NODE_NAME`,
MODIFY COLUMN `PROCESS_INSTANCE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程实例ID' AFTER `ASSIGNEE_FULLNAME`,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义ID' AFTER `PROCESS_INSTANCE_ID`,
MODIFY COLUMN `PROCESS_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义编码' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `PROCESS_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程定义名称' AFTER `PROCESS_KEY`,
MODIFY COLUMN `GROUP_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '用户组ID' AFTER `PROCESS_NAME`,
MODIFY COLUMN `GROUP_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '用户组名称' AFTER `GROUP_ID`,
MODIFY COLUMN `CREATE_TIME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '任务执行人名称' AFTER `GROUP_NAME`,
MODIFY COLUMN `DUE_DATE`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '评审意见' AFTER `CREATE_TIME`,
MODIFY COLUMN `MONITOR_STATUS`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '报警状态' AFTER `DUE_DATE`,
MODIFY COLUMN `IS_ACTIVITY`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '是否活动' AFTER `MONITOR_STATUS`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者' AFTER `IS_ACTIVITY`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='逾期流程任务表';


CREATE TABLE TEWPI00 (
        PARENT_INSTANCE_ID VARCHAR(64) DEFAULT NULL COMMENT '父流程实例ID',
        PROCESS_VERSION VARCHAR(10) DEFAULT NULL COMMENT '流程定义版本',
        ACT_PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT 'Activity流程定义ID',
        ACT_INSTANCE_ID VARCHAR(64) NOT NULL COMMENT 'Activity流程实例ID',
        PROCESS_INSTANCE_ID VARCHAR(64) NOT NULL COMMENT '流程实例ID',
        PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT '流程定义ID',
        PROCESS_DEF_KEY VARCHAR(255) NOT NULL COMMENT '流程编码',
        PROCESS_DEF_NAME VARCHAR(1000) NOT NULL COMMENT '流程名称',
        STARTER VARCHAR(255) NOT NULL COMMENT '流程启动着',
        START_TIME VARCHAR(32) NOT NULL COMMENT '开始时间',
        END_TIME VARCHAR(32) NOT NULL COMMENT '结束时间',
        DURATION BIGINT NOT NULL COMMENT '持续时间',
        INST_STATUS VARCHAR(32) NOT NULL COMMENT '状态',
        END_ACTIVITY VARCHAR(255) NOT NULL COMMENT '结束活动',
        INST_ACTIVITY VARCHAR(64) NOT NULL COMMENT '父流程中子流程节点活动实例ID',
        SYNC VARCHAR(2) NOT NULL COMMENT '同步标识',
        PARENT_PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT '父流程ID',
        ROOT_PROCINST VARCHAR(64) NOT NULL COMMENT '父流程实例ID',
        REC_CREATOR VARCHAR(255) NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) NOT NULL COMMENT '记录创建时间' ,
        REC_REVISOR VARCHAR(255) NOT NULL COMMENT '记录修改人',
        REC_REVISE_TIME VARCHAR(14) NOT NULL COMMENT '记录修改时间',
        ARCHIVE_FLAG VARCHAR(1) NOT NULL COMMENT '归档标记',
        INST_SUBJECT VARCHAR(1000) DEFAULT NULL COMMENT '流程主题',
        TIMEOUT_GAP BIGINT DEFAULT 0 NOT NULL COMMENT '超时时间间隔（毫秒）',
        WARN_GAP BIGINT DEFAULT 0 NOT NULL COMMENT '催办时间间隔（毫秒）',
        STARTER_ORG_ID VARCHAR(255) DEFAULT NULL COMMENT '程序启动着所在部门编码',
        STARTER_ORG_NAME VARCHAR(255) DEFAULT NULL COMMENT '程序启动着所在部门名称',
        SEQUENCE_ID VARCHAR(255) DEFAULT NULL COMMENT '流程实例编码',
        FORM VARCHAR(255) DEFAULT NULL COMMENT '表单',
        STARTER_NAME VARCHAR(255) DEFAULT NULL COMMENT '流程启动着名称',
        BUSINESS_KEY VARCHAR(255) DEFAULT NULL COMMENT '业务数据KEY',
        EXT1 VARCHAR(255) COMMENT '拓展字段1',
        EXT2 VARCHAR(255) COMMENT '拓展字段2',
        EXT3 VARCHAR(255) COMMENT '拓展字段3',
        EXT4 VARCHAR(255) COMMENT '拓展字段4',
        EXT5 VARCHAR(255) COMMENT '拓展字段5',
        EXT6 VARCHAR(255) COMMENT '拓展字段6',
        EXT7 VARCHAR(255) COMMENT '拓展字段7',
        EXT8 VARCHAR(255) COMMENT '拓展字段8',
        EXT9 VARCHAR(255) COMMENT '拓展字段9',
        EXT10 VARCHAR(255) COMMENT '拓展字段10',
        EXT11 VARCHAR(255) COMMENT '拓展字段11',
        EXT12 VARCHAR(255) COMMENT '拓展字段12',
        EXT13 VARCHAR(255) COMMENT '拓展字段13',
        EXT14 VARCHAR(255) COMMENT '拓展字段14',
        EXT15 VARCHAR(255) COMMENT '拓展字段15',
        EXT16 VARCHAR(255) COMMENT '拓展字段16',
        EXT17 VARCHAR(255) COMMENT '拓展字段17',
        EXT18 VARCHAR(255) COMMENT '拓展字段18',
        EXT19 VARCHAR(255) COMMENT '拓展字段19',
        EXT20 VARCHAR(255) COMMENT '拓展字段20',
        PRIMARY KEY (PROCESS_INSTANCE_ID)
    )COMMENT='流程实例表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

 CREATE TABLE HEWPI00 (
        PARENT_INSTANCE_ID VARCHAR(64) DEFAULT NULL COMMENT '父流程实例ID',
        PROCESS_VERSION VARCHAR(10) DEFAULT NULL COMMENT '流程定义版本',
        ACT_PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT 'Activity流程定义ID',
        ACT_INSTANCE_ID VARCHAR(64) NOT NULL COMMENT 'Activity流程实例ID',
        PROCESS_INSTANCE_ID VARCHAR(64) NOT NULL COMMENT '流程实例ID',
        PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT '流程定义ID',
        PROCESS_DEF_KEY VARCHAR(255) NOT NULL COMMENT '流程编码',
        PROCESS_DEF_NAME VARCHAR(1000) NOT NULL COMMENT '流程名称',
        STARTER VARCHAR(255) NOT NULL COMMENT '流程启动着',
        START_TIME VARCHAR(32) NOT NULL COMMENT '开始时间',
        END_TIME VARCHAR(32) NOT NULL COMMENT '结束时间',
        DURATION BIGINT NOT NULL COMMENT '持续时间',
        INST_STATUS VARCHAR(32) NOT NULL COMMENT '状态',
        END_ACTIVITY VARCHAR(255) NOT NULL COMMENT '结束活动',
        INST_ACTIVITY VARCHAR(64) NOT NULL COMMENT '父流程中子流程节点活动实例ID',
        SYNC VARCHAR(2) NOT NULL COMMENT '同步标识',
        PARENT_PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT '父流程ID',
        ROOT_PROCINST VARCHAR(64) NOT NULL COMMENT '父流程实例ID',
        REC_CREATOR VARCHAR(255) NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) NOT NULL COMMENT '记录创建时间' ,
        REC_REVISOR VARCHAR(255) NOT NULL COMMENT '记录修改人',
        REC_REVISE_TIME VARCHAR(14) NOT NULL COMMENT '记录修改时间',
        ARCHIVE_FLAG VARCHAR(1) NOT NULL COMMENT '归档标记',
        INST_SUBJECT VARCHAR(1000) DEFAULT NULL COMMENT '流程主题',
        TIMEOUT_GAP BIGINT DEFAULT 0 NOT NULL COMMENT '超时时间间隔（毫秒）',
        WARN_GAP BIGINT DEFAULT 0 NOT NULL COMMENT '催办时间间隔（毫秒）',
        STARTER_ORG_ID VARCHAR(255) DEFAULT NULL COMMENT '程序启动着所在部门编码',
        STARTER_ORG_NAME VARCHAR(255) DEFAULT NULL COMMENT '程序启动着所在部门名称',
        SEQUENCE_ID VARCHAR(255) DEFAULT NULL COMMENT '流程实例编码',
        FORM VARCHAR(255) DEFAULT NULL COMMENT '表单',
        STARTER_NAME VARCHAR(255) DEFAULT NULL COMMENT '流程启动着名称',
        BUSINESS_KEY VARCHAR(255) DEFAULT NULL COMMENT '业务数据KEY',
        EXT1 VARCHAR(255) COMMENT '拓展字段1',
        EXT2 VARCHAR(255) COMMENT '拓展字段2',
        EXT3 VARCHAR(255) COMMENT '拓展字段3',
        EXT4 VARCHAR(255) COMMENT '拓展字段4',
        EXT5 VARCHAR(255) COMMENT '拓展字段5',
        EXT6 VARCHAR(255) COMMENT '拓展字段6',
        EXT7 VARCHAR(255) COMMENT '拓展字段7',
        EXT8 VARCHAR(255) COMMENT '拓展字段8',
        EXT9 VARCHAR(255) COMMENT '拓展字段9',
        EXT10 VARCHAR(255) COMMENT '拓展字段10',
        EXT11 VARCHAR(255) COMMENT '拓展字段11',
        EXT12 VARCHAR(255) COMMENT '拓展字段12',
        EXT13 VARCHAR(255) COMMENT '拓展字段13',
        EXT14 VARCHAR(255) COMMENT '拓展字段14',
        EXT15 VARCHAR(255) COMMENT '拓展字段15',
        EXT16 VARCHAR(255) COMMENT '拓展字段16',
        EXT17 VARCHAR(255) COMMENT '拓展字段17',
        EXT18 VARCHAR(255) COMMENT '拓展字段18',
        EXT19 VARCHAR(255) COMMENT '拓展字段19',
        EXT20 VARCHAR(255) COMMENT '拓展字段20',
        PRIMARY KEY (PROCESS_INSTANCE_ID)
    )COMMENT='流程实例历史归档表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEWPI01 (
        REFER_ID VARCHAR(64) NOT NULL,
        MASTER_PROC_INST VARCHAR(64) NOT NULL,
        SLAVE_PROC_INST VARCHAR(64) NOT NULL,
        SLAVE_PROC_SUBJECT VARCHAR(1000) NOT NULL,
        REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
        SLAVE_PROC_CATEGORY VARCHAR(256) NOT NULL,
        SLAVE_PROC_NAME VARCHAR(256) NOT NULL,
        SLAVE_PROC_KEY VARCHAR(256) DEFAULT NULL,
        PRIMARY KEY (REFER_ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEWPI02 (
        TRACE_ID VARCHAR(64) NOT NULL,
        PROCESS_INSTANCE_ID VARCHAR(64) NOT NULL,
        EXECUTION_ID VARCHAR(64) NOT NULL,
        START_NODE_KEY VARCHAR(64) DEFAULT '',
        END_NODE_KEY VARCHAR(64) DEFAULT '',
        TRANSITION_KEY VARCHAR(255) DEFAULT '',
        PROCESS_DEF_ID VARCHAR(64) DEFAULT '',
        OPT_TYPE VARCHAR(32) DEFAULT '',
        ACT_PROCESS_DEF_ID VARCHAR(128) DEFAULT '',
        EXECUTION_TIME VARCHAR(32) DEFAULT '',
        REC_CREATOR VARCHAR(32) DEFAULT '',
        REC_CREATE_TIME VARCHAR(32) DEFAULT '',
        CONSTRAINT PK_TEWPI02 PRIMARY KEY (TRACE_ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPI02`
MODIFY COLUMN `TRACE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'UUID' FIRST ,
MODIFY COLUMN `PROCESS_INSTANCE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程实例ID' AFTER `TRACE_ID`,
MODIFY COLUMN `START_NODE_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '起始字节编码' AFTER `EXECUTION_ID`,
MODIFY COLUMN `END_NODE_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '结束字节编码' AFTER `START_NODE_KEY`,
MODIFY COLUMN `TRANSITION_KEY`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '途径转移线' AFTER `END_NODE_KEY`,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '流程定义ID' AFTER `TRANSITION_KEY`,
MODIFY COLUMN `OPT_TYPE`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '操作类型' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `ACT_PROCESS_DEF_ID`  varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'activity流程定义编码' AFTER `OPT_TYPE`,
MODIFY COLUMN `EXECUTION_TIME`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '跳转执行时间' AFTER `ACT_PROCESS_DEF_ID`,
MODIFY COLUMN `REC_CREATOR`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '记录创建人' AFTER `EXECUTION_TIME`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '记录创建时间' AFTER `REC_CREATOR`,
COMMENT='流程运转跟踪表';


CREATE TABLE TEWPT00 (
        PROCESS_INSTANCE_ID VARCHAR(64) DEFAULT NULL COMMENT '流程实例ID',
        OPINION_ID VARCHAR(64) NOT NULL COMMENT '意见ID',
        ACT_PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT 'ACTIVITI流程定义ID',
        ACT_INSTANCE_ID VARCHAR(64) NOT NULL COMMENT 'ACTIVITI流程实例ID',
        TASK_ID VARCHAR(64) NOT NULL COMMENT '任务ID',
        PARENT_TASK_ID VARCHAR(64) DEFAULT NULL COMMENT '父任务ID',
        START_TIME VARCHAR(32) DEFAULT NULL COMMENT '任务开始时间',
        END_TIME VARCHAR(32) DEFAULT NULL COMMENT '任务结束时间',
        DURATION BIGINT DEFAULT NULL COMMENT '持续时间',
        COMPLETER_ID VARCHAR(64) DEFAULT NULL COMMENT '任务执行人ID',
        COMPLETER_FULLNAME VARCHAR(254) DEFAULT NULL COMMENT '任务执行人名称',
        OPINION TEXT(1048576) COMMENT '评审意见' ,
        STATE VARCHAR(32) DEFAULT NULL COMMENT '任务状态',
        TASK_DEF_KEY VARCHAR(64) DEFAULT NULL COMMENT '活动定义KEY',
        TASK_NAME VARCHAR(64) DEFAULT NULL COMMENT '活动定义名称',
        FORM VARCHAR(255) DEFAULT NULL COMMENT '关联表单',
        TASK_TYPE VARCHAR(32) DEFAULT NULL COMMENT '任务类型',
        COMPLETER_IP_ADDRESS VARCHAR(30) DEFAULT NULL COMMENT '任务执行人机器IP地址',
        REC_CREATOR VARCHAR(255) DEFAULT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) DEFAULT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(255) DEFAULT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) DEFAULT NULL COMMENT '记录修改时刻',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT NULL COMMENT '归档标记',
        ASSIGNEE_ID VARCHAR(64) DEFAULT NULL COMMENT '负责人',
        ASSIGNEE_FULLNAME VARCHAR(254) DEFAULT NULL COMMENT '负责人名称',
        CONSIGNEE_ID VARCHAR(64) DEFAULT NULL COMMENT '转派人' ,
        CONSIGNEE_FULLNAME VARCHAR(254) DEFAULT NULL COMMENT '转派人名称',
        CLAIM_STATUS VARCHAR(20) DEFAULT '' COMMENT '领取状态',
        APPROVAL_RESULT VARCHAR(20) DEFAULT '' COMMENT '审批结果',
        TIMEOUT_GAP  BIGINT DEFAULT 0 COMMENT '超时间隔',
        WARN_GAP  BIGINT DEFAULT 0 COMMENT '告警间隔',
        DEPT_CNAME VARCHAR(100) COMMENT '部门名称',
        DEPT_ENAME VARCHAR(36) COMMENT '部门编码',
        PROCESS_KEY VARCHAR(255) DEFAULT NULL COMMENT '流程定义编码',
        OPERATION_TYPE VARCHAR(255) DEFAULT NULL COMMENT '操作类型',
        HAS_AUTH VARCHAR(8) COMMENT '是否授权',
        ROLE_CNAME VARCHAR(100) COMMENT '角色名称',
        ROLE_ENAME VARCHAR(100) COMMENT '角色编码',
        EXT1 VARCHAR(128) COMMENT '扩展字段1',
        EXT2 VARCHAR(128) COMMENT '扩展字段2',
        SIGN_TYPE VARCHAR(32) COMMENT '标记类别',
        PROCESS_DEF_ID VARCHAR(64) COMMENT '流程定义ID',
        EXECUTION_ID VARCHAR(64) COMMENT '流程执行ID',
        OWNER_ID VARCHAR(64) COMMENT '责任人ID',
        PRIMARY KEY (OPINION_ID)
    )COMMENT='任务表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE HEWPT00 (
        PROCESS_INSTANCE_ID VARCHAR(64) DEFAULT NULL COMMENT '流程实例ID',
        OPINION_ID VARCHAR(64) NOT NULL COMMENT '意见ID',
        ACT_PROCESS_DEF_ID VARCHAR(64) NOT NULL COMMENT 'ACTIVITI流程定义ID',
        ACT_INSTANCE_ID VARCHAR(64) NOT NULL COMMENT 'ACTIVITI流程实例ID',
        TASK_ID VARCHAR(64) NOT NULL COMMENT '任务ID',
        PARENT_TASK_ID VARCHAR(64) DEFAULT NULL COMMENT '父任务ID',
        START_TIME VARCHAR(32) DEFAULT NULL COMMENT '任务开始时间',
        END_TIME VARCHAR(32) DEFAULT NULL COMMENT '任务结束时间',
        DURATION BIGINT DEFAULT NULL COMMENT '持续时间',
        COMPLETER_ID VARCHAR(64) DEFAULT NULL COMMENT '任务执行人ID',
        COMPLETER_FULLNAME VARCHAR(254) DEFAULT NULL COMMENT '任务执行人名称',
        OPINION TEXT(1048576) COMMENT '评审意见' ,
        STATE VARCHAR(32) DEFAULT NULL COMMENT '任务状态',
        TASK_DEF_KEY VARCHAR(64) DEFAULT NULL COMMENT '活动定义KEY',
        TASK_NAME VARCHAR(64) DEFAULT NULL COMMENT '活动定义名称',
        FORM VARCHAR(255) DEFAULT NULL COMMENT '关联表单',
        TASK_TYPE VARCHAR(32) DEFAULT NULL COMMENT '任务类型',
        COMPLETER_IP_ADDRESS VARCHAR(30) DEFAULT NULL COMMENT '任务执行人机器IP地址',
        REC_CREATOR VARCHAR(255) DEFAULT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) DEFAULT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(255) DEFAULT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) DEFAULT NULL COMMENT '记录修改时刻',
        ARCHIVE_FLAG VARCHAR(1) DEFAULT NULL COMMENT '归档标记',
        ASSIGNEE_ID VARCHAR(64) DEFAULT NULL COMMENT '负责人',
        ASSIGNEE_FULLNAME VARCHAR(254) DEFAULT NULL COMMENT '负责人名称',
        CONSIGNEE_ID VARCHAR(64) DEFAULT NULL COMMENT '转派人' ,
        CONSIGNEE_FULLNAME VARCHAR(254) DEFAULT NULL COMMENT '转派人名称',
        CLAIM_STATUS VARCHAR(20) DEFAULT '' COMMENT '领取状态',
        APPROVAL_RESULT VARCHAR(20) DEFAULT '' COMMENT '审批结果',
        TIMEOUT_GAP  BIGINT DEFAULT 0 COMMENT '超时间隔',
        WARN_GAP  BIGINT DEFAULT 0 COMMENT '告警间隔',
        DEPT_CNAME VARCHAR(100) COMMENT '部门名称',
        DEPT_ENAME VARCHAR(36) COMMENT '部门编码',
        PROCESS_KEY VARCHAR(255) DEFAULT NULL COMMENT '流程定义编码',
        OPERATION_TYPE VARCHAR(255) DEFAULT NULL COMMENT '操作类型',
        HAS_AUTH VARCHAR(8) COMMENT '是否授权',
        ROLE_CNAME VARCHAR(100) COMMENT '角色名称',
        ROLE_ENAME VARCHAR(100) COMMENT '角色编码',
        EXT1 VARCHAR(128) COMMENT '扩展字段1',
        EXT2 VARCHAR(128) COMMENT '扩展字段2',
        SIGN_TYPE VARCHAR(32) COMMENT '标记类别',
        PROCESS_DEF_ID VARCHAR(64) COMMENT '流程定义ID',
        EXECUTION_ID VARCHAR(64) COMMENT '流程执行ID',
        OWNER_ID VARCHAR(64) COMMENT '责任人ID',
        PRIMARY KEY (OPINION_ID)
    )COMMENT='任务历史归档表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEWPT01 (
        PROCESS_INST_ID VARCHAR(64) DEFAULT NULL,
        ID VARCHAR(64) NOT NULL,
        ACT_PROCESS_INST_ID VARCHAR(64) DEFAULT NULL,
        TASK_ID VARCHAR(64) DEFAULT NULL,
        USER_ID VARCHAR(255) DEFAULT NULL,
        USER_NAME VARCHAR(255) DEFAULT NULL,
        REC_CREATOR VARCHAR(255) DEFAULT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT NULL,
        REC_REVISOR VARCHAR(255) DEFAULT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT NULL,
        PRIMARY KEY (ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPT01`
MODIFY COLUMN `PROCESS_INST_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程实例ID' FIRST ,
MODIFY COLUMN `ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '阅读ID' AFTER `PROCESS_INST_ID`,
MODIFY COLUMN `ACT_PROCESS_INST_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'Activiti流程实例ID' AFTER `ID`,
MODIFY COLUMN `TASK_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '任务ID' AFTER `ACT_PROCESS_INST_ID`,
MODIFY COLUMN `USER_ID`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '用户ID' AFTER `TASK_ID`,
MODIFY COLUMN `USER_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '用户名称' AFTER `USER_ID`,
MODIFY COLUMN `REC_CREATOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建责任者' AFTER `USER_NAME`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '归档标记' AFTER `REC_REVISE_TIME`,
COMMENT='已阅任务表';


CREATE TABLE TEWPT02 (
        ID VARCHAR(64) NOT NULL,
        ACT_PROCESS_DEF_ID VARCHAR(64) DEFAULT NULL,
        ACT_PROCESS_INST_ID VARCHAR(64) NOT NULL,
        NODE_NAME VARCHAR(255) DEFAULT NULL,
        NODE_ID VARCHAR(255) NOT NULL,
        TASK_ID VARCHAR(64) DEFAULT NULL,
        VOTE_USER_ID VARCHAR(1000) NOT NULL,
        VOTE_USER_NAME VARCHAR(1000) DEFAULT NULL,
        VOTE_TIME VARCHAR(14) DEFAULT NULL,
        IS_AGREE VARCHAR(2) DEFAULT '0',
        CONTENT VARCHAR(200) DEFAULT NULL,
        SIGNNUMS DECIMAL(18,0) DEFAULT '1',
        IS_COMPLETED VARCHAR(2) DEFAULT '0',
        BATCH DECIMAL(11,0) DEFAULT NULL,
        REC_CREATOR VARCHAR(255) DEFAULT NULL,
        REC_CREATE_TIME VARCHAR(14) DEFAULT NULL,
        REC_REVISOR VARCHAR(255) DEFAULT NULL,
        REC_REVISE_TIME VARCHAR(14) DEFAULT NULL,
        ARCHIVE_FLAG VARCHAR(1) DEFAULT NULL,
        PRIMARY KEY (ID)
    ) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEWPT03 (
        JOB_ID VARCHAR(64) NOT NULL,
        PROCESS_INSTANCE_ID VARCHAR(64) NOT NULL,
        PROCESS_DEF_ID VARCHAR(64),
        PROCESS_KEY VARCHAR(128),
        PROCESS_NAME VARCHAR(256),
        NODE_KEY VARCHAR(128) NOT NULL,
        NODE_NAME VARCHAR(256),
        STATE VARCHAR(20),
        REC_CREATE_TIME VARCHAR(64),
        REC_CREATOR VARCHAR(64),
        REC_REVISE_TIME VARCHAR(64),
        REC_REVISOR VARCHAR(64),
        FAILURE_REASON VARCHAR(400),
        REMARK VARCHAR(255),
        PRIMARY KEY (JOB_ID, PROCESS_INSTANCE_ID, NODE_KEY)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEWPT03`
MODIFY COLUMN `JOB_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '任务Job ID' FIRST ,
MODIFY COLUMN `PROCESS_INSTANCE_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '流程实例ID' AFTER `JOB_ID`,
MODIFY COLUMN `PROCESS_DEF_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程定义ID' AFTER `PROCESS_INSTANCE_ID`,
MODIFY COLUMN `PROCESS_KEY`  varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程编码' AFTER `PROCESS_DEF_ID`,
MODIFY COLUMN `PROCESS_NAME`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '流程名称' AFTER `PROCESS_KEY`,
MODIFY COLUMN `NODE_KEY`  varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '活动编码' AFTER `PROCESS_NAME`,
MODIFY COLUMN `NODE_NAME`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '活动名称' AFTER `NODE_KEY`,
MODIFY COLUMN `STATE`  varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '任务执行状态' AFTER `NODE_NAME`,
MODIFY COLUMN `REC_CREATE_TIME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录创建时间' AFTER `STATE`,
MODIFY COLUMN `REC_CREATOR`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'ͬ记录创建人' AFTER `REC_CREATE_TIME`,
MODIFY COLUMN `REC_REVISE_TIME`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改时间' AFTER `REC_CREATOR`,
MODIFY COLUMN `REC_REVISOR`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '记录修改人' AFTER `REC_REVISE_TIME`,
MODIFY COLUMN `FAILURE_REASON`  varchar(400) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '失败原因' AFTER `REC_REVISOR`,
MODIFY COLUMN `REMARK`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注' AFTER `FAILURE_REASON`,
COMMENT='自动活动执行异常记录表';

CREATE TABLE TEWPR00 (
  RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
  RULE_PARAM_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
  RULE_PARAM_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL,
  RULE_PARAM_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL,
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='规则参数表';
ALTER TABLE TEWPR00 MODIFY COLUMN RULE_KEY VARCHAR(255) COMMENT '规则编码';
ALTER TABLE TEWPR00 MODIFY COLUMN RULE_PARAM_ENAME VARCHAR(255) COMMENT '规则参数英文名';
ALTER TABLE TEWPR00 MODIFY COLUMN RULE_PARAM_CNAME VARCHAR(255) COMMENT '规则参数中文名';
ALTER TABLE TEWPR00 MODIFY COLUMN RULE_PARAM_TYPE VARCHAR(16) COMMENT '规则参数类型';
ALTER TABLE TEWPR00 MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE TEWPR00 MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE TEWPR00 MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE TEWPR00 MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE TEWPR00 MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE TEWPR00 ADD PRIMARY KEY (RULE_KEY, RULE_PARAM_ENAME, RULE_PARAM_TYPE);

CREATE TABLE TEWPR01 (
  PROCESS_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
  PROCESS_RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
  PROCESS_RULE_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL,
  PROCESS_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
  PROCESS_RULE_STATUS VARCHAR(1) DEFAULT ' ' NOT NULL,
  PROCESS_RULE_REMARK VARCHAR(4000) DEFAULT ' ' NOT NULL,
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='流程定义规则表';
ALTER TABLE TEWPR01 MODIFY COLUMN PROCESS_RULE_ID VARCHAR(255) COMMENT '流程定义规则ID';
ALTER TABLE TEWPR01 MODIFY COLUMN PROCESS_RULE_KEY VARCHAR(255) COMMENT '流程定义规则编码';
ALTER TABLE TEWPR01 MODIFY COLUMN PROCESS_RULE_EXPRESSION VARCHAR(255) COMMENT '规则表达式';
ALTER TABLE TEWPR01 MODIFY COLUMN PROCESS_KEY VARCHAR(255) COMMENT '流程定义编码';
ALTER TABLE TEWPR01 MODIFY COLUMN PROCESS_RULE_STATUS VARCHAR(1) COMMENT '规则状态';
ALTER TABLE TEWPR01 MODIFY COLUMN PROCESS_RULE_REMARK VARCHAR(4000) COMMENT '备注';
ALTER TABLE TEWPR01 MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE TEWPR01 MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE TEWPR01 MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE TEWPR01 MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE TEWPR01 MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE TEWPR01 ADD PRIMARY KEY (PROCESS_RULE_ID);

CREATE TABLE TEWPR02 (
  BRANCH_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
  BRANCH_RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
  BRANCH_RULE_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL,
  BRANCH_RULE_RESULT VARCHAR(255) DEFAULT ' ' NOT NULL,
  BRANCH_RULE_STATUS VARCHAR(1) DEFAULT ' ' NOT NULL,
  BRANCH_RULE_REMARK VARCHAR(4000) DEFAULT ' ' NOT NULL,
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='分支条件规则表';
ALTER TABLE TEWPR02 MODIFY COLUMN BRANCH_RULE_ID VARCHAR(255) COMMENT '分支条件规则ID';
ALTER TABLE TEWPR02 MODIFY COLUMN BRANCH_RULE_KEY VARCHAR(255) COMMENT '分支条件规则编码';
ALTER TABLE TEWPR02 MODIFY COLUMN BRANCH_RULE_EXPRESSION VARCHAR(255) COMMENT '规则表达式';
ALTER TABLE TEWPR02 MODIFY COLUMN BRANCH_RULE_RESULT VARCHAR(255) COMMENT '规则条件';
ALTER TABLE TEWPR02 MODIFY COLUMN BRANCH_RULE_STATUS VARCHAR(1) COMMENT '规则状态';
ALTER TABLE TEWPR02 MODIFY COLUMN BRANCH_RULE_REMARK VARCHAR(4000) COMMENT '备注';
ALTER TABLE TEWPR02 MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE TEWPR02 MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE TEWPR02 MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE TEWPR02 MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE TEWPR02 MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE TEWPR02 ADD PRIMARY KEY (BRANCH_RULE_ID);

CREATE TABLE TEWPR03 (
  PARTICIPANT_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
  PARTICIPANT_RULE_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
  PARTICIPANT_RULE_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL,
  PARTICIPANT_RULE_STATUS VARCHAR(1) DEFAULT ' ' NOT NULL,
  PARTICIPANT_RULE_REMARK VARCHAR(4000) DEFAULT ' ' NOT NULL,
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='参与者规则表';
ALTER TABLE TEWPR03 MODIFY COLUMN PARTICIPANT_RULE_ID VARCHAR(255) COMMENT '参与者规则ID';
ALTER TABLE TEWPR03 MODIFY COLUMN PARTICIPANT_RULE_KEY VARCHAR(255) COMMENT '参与者规则编码';
ALTER TABLE TEWPR03 MODIFY COLUMN PARTICIPANT_RULE_EXPRESSION VARCHAR(255) COMMENT '规则表达式';
ALTER TABLE TEWPR03 MODIFY COLUMN PARTICIPANT_RULE_STATUS VARCHAR(1) COMMENT '规则状态';
ALTER TABLE TEWPR03 MODIFY COLUMN PARTICIPANT_RULE_REMARK VARCHAR(4000) COMMENT '备注';
ALTER TABLE TEWPR03 MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE TEWPR03 MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE TEWPR03 MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE TEWPR03 MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE TEWPR03 MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE TEWPR03 ADD PRIMARY KEY (PARTICIPANT_RULE_ID);

CREATE TABLE TEWPR04 (
  PARTICIPANT_RULE_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
  PARTICIPANT_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
  PARTICIPANT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
  PARTICIPANT_CNAME VARCHAR(1) DEFAULT ' ' NOT NULL,
  PARTICIPANT_TYPE VARCHAR(4000) DEFAULT ' ' NOT NULL,
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='参与者明细表';
ALTER TABLE TEWPR04 MODIFY COLUMN PARTICIPANT_RULE_ID VARCHAR(255) COMMENT '参与者规则ID';
ALTER TABLE TEWPR04 MODIFY COLUMN PARTICIPANT_ID VARCHAR(255) COMMENT '参与者ID';
ALTER TABLE TEWPR04 MODIFY COLUMN PARTICIPANT_ENAME VARCHAR(255) COMMENT '参与者英文名';
ALTER TABLE TEWPR04 MODIFY COLUMN PARTICIPANT_CNAME VARCHAR(1) COMMENT '参与者中文名';
ALTER TABLE TEWPR04 MODIFY COLUMN PARTICIPANT_TYPE VARCHAR(4000) COMMENT '参与者类型';
ALTER TABLE TEWPR04 MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE TEWPR04 MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE TEWPR04 MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE TEWPR04 MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE TEWPR04 MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE TEWPR04 ADD PRIMARY KEY (PARTICIPANT_RULE_ID, PARTICIPANT_ID);

CREATE TABLE XS_AUTHORIZATION (
  SUBJECT_ID      VARCHAR(32) ,
  SUBJECT_TYPE    VARCHAR(16) ,
  OBJECT_ID       VARCHAR(32) ,
  OBJECT_TYPE     VARCHAR(16) ,
  OPERATION_TYPE  VARCHAR(32) ,
  REC_CREATOR     VARCHAR(32) ,
  REC_CREATE_TIME VARCHAR(14) ,
  REC_REVISOR     VARCHAR(32) ,
  REC_REVISE_TIME VARCHAR(14) ,
  ARCHIVE_FLAG        VARCHAR(1) ,
  SORT_INDEX      INT(11) DEFAULT 0
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='授权信息表';

ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN SUBJECT_ID VARCHAR(32) COMMENT '授权主体ID';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN SUBJECT_TYPE VARCHAR(16) COMMENT '授权主体类别';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN OBJECT_ID VARCHAR(32) COMMENT '授权客体ID';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN OBJECT_TYPE VARCHAR(16) COMMENT '授权客体类别';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN OPERATION_TYPE VARCHAR(32) COMMENT '操作类型:访问:OPT_ACESS,管理:OPT_MANAGE';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_AUTHORIZATION ADD PRIMARY KEY(SUBJECT_ID, OBJECT_ID, OPERATION_TYPE);

CREATE TABLE XS_DATAS_AUTHORIZATION (
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
  AUTH_RESOURCE_ID VARCHAR(32) DEFAULT ' ' NOT NULL,
  AUTH_GROUP_ID VARCHAR(32) DEFAULT ' ' NOT NULL,
  AUTH_GROUP_TYPE VARCHAR(32) DEFAULT ' ' NOT NULL,
  SQL_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
  AUTH_OBJECT_ID VARCHAR(32) DEFAULT ' ' NOT NULL,
  AUTH_OBJECT_TYPE VARCHAR(32) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='数据集过滤条件表';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN AUTH_RESOURCE_ID VARCHAR(32) COMMENT '授权资源标识';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN AUTH_GROUP_ID VARCHAR(32) COMMENT '授权集合标识';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN AUTH_GROUP_TYPE VARCHAR(32) COMMENT '授权集合类型';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN SQL_ID VARCHAR(64) COMMENT 'SQL语句的标识';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN AUTH_OBJECT_ID VARCHAR(32) COMMENT '资源标识';
ALTER TABLE XS_DATAS_AUTHORIZATION MODIFY COLUMN AUTH_OBJECT_TYPE VARCHAR(32) COMMENT '资源标识类型';
ALTER TABLE XS_DATAS_AUTHORIZATION ADD PRIMARY KEY (AUTH_RESOURCE_ID,AUTH_GROUP_ID, AUTH_OBJECT_ID);

CREATE TABLE XS_DATAS_SQL (
  REC_CREATOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  REC_REVISOR VARCHAR(255) DEFAULT ' ' NOT NULL,
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
  ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
  SQL_ID VARCHAR(20) DEFAULT ' ' NOT NULL,
  SQL_TEXT VARCHAR(1000) DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='数据集SQL条件定义表';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN REC_CREATOR VARCHAR(255) COMMENT '创建人';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN REC_REVISOR VARCHAR(255) COMMENT '修改人';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN SQL_ID VARCHAR(20) COMMENT 'SQL语句的标识';
ALTER TABLE XS_DATAS_SQL MODIFY COLUMN SQL_TEXT VARCHAR(1000) COMMENT 'SQL语句的正文';
ALTER TABLE XS_DATAS_SQL ADD PRIMARY KEY (SQL_ID);


CREATE TABLE XS_RESOURCE (
        ID VARCHAR(32) NOT NULL COMMENT '资源ID',
        RESOURCE_ENAME VARCHAR(128) COMMENT '资源英文名',
        RESOURCE_CNAME VARCHAR(256) COMMENT '资源中文名',
        TYPE VARCHAR(16) COMMENT 'PAGE:页面，BUTTON:按钮，AREA页面区域，URL:URL',
        IS_AUTH VARCHAR(2) COMMENT '是否授权(1:授权，-1:不授权)默认:-1',
        SORT_INDEX INTEGER DEFAULT 0 COMMENT '排序',
        REC_CREATOR VARCHAR(32) COMMENT '创建人',
        REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间',
        REC_REVISOR VARCHAR(32) COMMENT '修改人',
        REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间',
        ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记',
        TENANT_ID VARCHAR(64) DEFAULT 'BDAS',
        PRIMARY KEY (ID)
    )ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='资源信息表';
CREATE UNIQUE INDEX XS_XS_RESOURCE_RESOURCE_ENAME_UINDEX ON XS_RESOURCE (RESOURCE_ENAME);

CREATE TABLE XS_RESOURCE_GROUP (
  ID                   VARCHAR(32) PRIMARY KEY,
  RESOURCE_GROUP_ENAME VARCHAR(32) ,
  RESOURCE_GROUP_CNAME VARCHAR(128),
  RESOURCE_GROUP_TYPE  VARCHAR(16),
  SORT_INDEX           INT(11) DEFAULT 0 ,
  REC_CREATOR          VARCHAR(32),
  REC_CREATE_TIME      VARCHAR(14),
  REC_REVISOR          VARCHAR(32),
  REC_REVISE_TIME      VARCHAR(14),
  ARCHIVE_FLAG             VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='资源组信息表';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN ID VARCHAR(32) COMMENT '资源组ID';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN RESOURCE_GROUP_ENAME VARCHAR(32) COMMENT '资源分组英文名';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN RESOURCE_GROUP_CNAME VARCHAR(128) COMMENT '资源组中文名';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN RESOURCE_GROUP_TYPE VARCHAR(16) COMMENT '类别:资源组,模块';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
CREATE UNIQUE INDEX XS_RESOURCE_GROUP_RESOURCE_GROUP_ENAME_UINDEX ON XS_RESOURCE_GROUP (RESOURCE_GROUP_ENAME);

CREATE TABLE XS_RESOURCE_GROUP_MEMBER (
  RESOURCE_MEMBER_ID VARCHAR(128) ,
  RESOURCE_PARENT_ID VARCHAR(32)  ,
  MEMBER_TYPE        VARCHAR(16),
  PATH               VARCHAR(255),
  SORT_INDEX         INT(11) DEFAULT 0 ,
  REC_CREATOR        VARCHAR(32),
  REC_CREATE_TIME    VARCHAR(14),
  REC_REVISOR        VARCHAR(32),
  REC_REVISE_TIME    VARCHAR(14),
  ARCHIVE_FLAG           VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='资源组资源信息表';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN RESOURCE_MEMBER_ID VARCHAR(32) COMMENT '资源组成员ID';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN RESOURCE_PARENT_ID VARCHAR(32) COMMENT '资源组父节点英文名';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN MEMBER_TYPE VARCHAR(16) COMMENT '资源体类别，0:资源组,1:资源';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN PATH VARCHAR(255) COMMENT '来源';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER ADD PRIMARY KEY (RESOURCE_MEMBER_ID, RESOURCE_PARENT_ID);


CREATE TABLE XS_USER (
  USER_ID             VARCHAR(32) PRIMARY KEY ,
  LOGIN_NAME          VARCHAR(64)  ,
  PASSWORD            VARCHAR(255) ,
  STATUS              VARCHAR(16)  ,
  USER_NAME           VARCHAR(128)  ,
  GENDER VARCHAR(2) DEFAULT '1',
  MOBILE              VARCHAR(32)  ,
  EMAIL               VARCHAR(128) ,
  USER_TYPE           VARCHAR(16)  ,
  ACCOUNT_EXPIRE_DATE VARCHAR(14)  ,
  PWD_EXPIRE_DATE     VARCHAR(14)  ,
  IS_LOCKED           VARCHAR(2)   ,
  SORT_INDEX          INT(11) DEFAULT 0,
  REC_CREATOR         VARCHAR(32) ,
  REC_CREATE_TIME     VARCHAR(14) ,
  REC_REVISOR         VARCHAR(32) ,
  REC_REVISE_TIME     VARCHAR(14) ,
  PWD_REVISE_DATE     VARCHAR(14) ,
  PWD_REVISOR         VARCHAR(32) ,
  ARCHIVE_FLAG            VARCHAR(1),
  USER_GROUP_ENAME    VARCHAR(32),
  JOB_ID              VARCHAR(16)  default ' ',
  EHR_ORG_ID          VARCHAR(16)  default ' ',
  JOB_NAME            VARCHAR(256)  default ' '

)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='用户信息表';
ALTER TABLE XS_USER MODIFY COLUMN USER_ID VARCHAR(32) COMMENT '用户ID';
ALTER TABLE XS_USER MODIFY COLUMN LOGIN_NAME VARCHAR(64) COMMENT '登录账号';
ALTER TABLE XS_USER MODIFY COLUMN PASSWORD VARCHAR(255) COMMENT '登录密码';
ALTER TABLE XS_USER MODIFY COLUMN STATUS VARCHAR(16) COMMENT '账号状态(1：可用,-1不可用)';
ALTER TABLE XS_USER MODIFY COLUMN USER_NAME VARCHAR(128) COMMENT '用户姓名';
ALTER TABLE XS_USER MODIFY COLUMN GENDER VARCHAR(2) COMMENT '性别';
ALTER TABLE XS_USER MODIFY COLUMN MOBILE VARCHAR(32) COMMENT '手机';
ALTER TABLE XS_USER MODIFY COLUMN EMAIL VARCHAR(128) COMMENT '邮箱';
ALTER TABLE XS_USER MODIFY COLUMN USER_TYPE VARCHAR(16) COMMENT '用户类别(COMPANY企业用户，USER个人用户)';
ALTER TABLE XS_USER MODIFY COLUMN ACCOUNT_EXPIRE_DATE VARCHAR(14) COMMENT '账号过期时间';
ALTER TABLE XS_USER MODIFY COLUMN PWD_EXPIRE_DATE VARCHAR(14) COMMENT '密码过期时间';
ALTER TABLE XS_USER MODIFY COLUMN IS_LOCKED VARCHAR(2) COMMENT '是否锁定：-1锁定，1正常';
ALTER TABLE XS_USER MODIFY COLUMN PWD_REVISOR VARCHAR(32) COMMENT '密码修改人';
ALTER TABLE XS_USER MODIFY COLUMN PWD_REVISE_DATE VARCHAR(14) COMMENT '密码修改时间';
ALTER TABLE XS_USER MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_USER MODIFY COLUMN USER_GROUP_ENAME VARCHAR(32)  COMMENT '用户组';
ALTER TABLE XS_USER MODIFY COLUMN JOB_ID VARCHAR(16) COMMENT '用户岗位号';
ALTER TABLE XS_USER MODIFY COLUMN EHR_ORG_ID VARCHAR(16) COMMENT 'EHR组织机构代码';
ALTER TABLE XS_USER MODIFY COLUMN JOB_NAME VARCHAR(32)  COMMENT '用户岗位名';

CREATE UNIQUE INDEX XS_USER_LOGIN_NAME_UINDEX ON XS_USER (LOGIN_NAME);
ALTER TABLE XS_USER MODIFY  LOGIN_NAME VARCHAR(64) NOT NULL;
ALTER TABLE XS_USER MODIFY  PASSWORD VARCHAR(255) NOT NULL;
ALTER TABLE XS_USER MODIFY  STATUS VARCHAR(16) NOT NULL;
ALTER TABLE XS_USER MODIFY  USER_NAME VARCHAR(128) NOT NULL;
ALTER TABLE XS_USER MODIFY  GENDER VARCHAR(2) NOT NULL;
ALTER TABLE XS_USER MODIFY  MOBILE VARCHAR(32) NOT NULL;
ALTER TABLE XS_USER MODIFY  EMAIL VARCHAR(128) NOT NULL;
ALTER TABLE XS_USER MODIFY  USER_TYPE VARCHAR(16) NOT NULL;
ALTER TABLE XS_USER MODIFY  ACCOUNT_EXPIRE_DATE VARCHAR(14) NOT NULL;
ALTER TABLE XS_USER MODIFY  PWD_EXPIRE_DATE VARCHAR(14) NOT NULL;
ALTER TABLE XS_USER MODIFY  IS_LOCKED VARCHAR(2) NOT NULL;
ALTER TABLE XS_USER MODIFY  REC_CREATOR VARCHAR(32) NOT NULL;

CREATE TABLE XS_USER_EXT (
  ID              VARCHAR(32) PRIMARY KEY,
  USER_ID         VARCHAR(32) ,
  FIELD_ID        VARCHAR(128) ,
  VALUE           VARCHAR(128) ,
  REC_CREATOR     VARCHAR(32) ,
  REC_CREATE_TIME VARCHAR(14) ,
  REC_REVISOR     VARCHAR(32) ,
  REC_REVISE_TIME VARCHAR(14) ,
  ARCHIVE_FLAG        VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='用户拓展信息表';
ALTER TABLE XS_USER_EXT MODIFY COLUMN ID VARCHAR(32) COMMENT 'ID';
ALTER TABLE XS_USER_EXT MODIFY COLUMN USER_ID VARCHAR(32) COMMENT '用户ID';
ALTER TABLE XS_USER_EXT MODIFY COLUMN FIELD_ID VARCHAR(128) COMMENT '属性ID';
ALTER TABLE XS_USER_EXT MODIFY COLUMN VALUE VARCHAR(128) COMMENT '属性值';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER_EXT MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';

CREATE TABLE XS_USER_GROUP (
  ID              VARCHAR(32) PRIMARY KEY ,
  GROUP_ENAME     VARCHAR(32)  ,
  GROUP_CNAME     VARCHAR(128) ,
  GROUP_TYPE      VARCHAR(32) ,
  SORT_INDEX      INT(11) DEFAULT 0  ,
  REC_CREATOR     VARCHAR(32) ,
  REC_CREATE_TIME VARCHAR(14) ,
  REC_REVISOR     VARCHAR(32) ,
  REC_REVISE_TIME VARCHAR(14) ,
  ARCHIVE_FLAG        VARCHAR(1),
  MANAGE_GROUP_ENAME VARCHAR(32)

)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='用户组信息表';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN ID VARCHAR(32) COMMENT '用户群组ID';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN GROUP_ENAME VARCHAR(32) COMMENT '群组英文名';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN GROUP_CNAME VARCHAR(128) COMMENT '群组中文名';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN GROUP_TYPE VARCHAR(32) COMMENT '群组类型';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN MANAGE_GROUP_ENAME VARCHAR(32) COMMENT '管辖组';
CREATE UNIQUE INDEX GROUP_ENAME_UINDEX ON XS_USER_GROUP (GROUP_ENAME);
ALTER TABLE XS_USER_GROUP MODIFY  GROUP_ENAME VARCHAR(32) NOT NULL;

CREATE TABLE XS_USER_GROUP_MEMBER (
  MEMBER_ID       VARCHAR(32) ,
  PARENT_ID       VARCHAR(32) ,
  MEMBER_TYPE     VARCHAR(16) ,
  SORT_INDEX      INT(11) DEFAULT 0 ,
  PATH            VARCHAR(255) ,
  REC_CREATOR     VARCHAR(32) ,
  REC_CREATE_TIME VARCHAR(14) ,
  REC_REVISOR     VARCHAR(32) ,
  REC_REVISE_TIME VARCHAR(14) ,
  ARCHIVE_FLAG        VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='用户组成员信息表';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN MEMBER_ID VARCHAR(32) COMMENT '成员ID';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN PARENT_ID VARCHAR(32) COMMENT '父节点ID';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN MEMBER_TYPE VARCHAR(16) COMMENT '授权类别:USER,GROUP';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN PATH VARCHAR(255) COMMENT '来源';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_USER_GROUP_MEMBER ADD PRIMARY KEY (PARENT_ID, MEMBER_ID);

CREATE TABLE TXSOG01 (
  ORG_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '组织ID',
  ORG_ENAME VARCHAR(14)  DEFAULT ' '    NOT NULL COMMENT '组织编码',
  ORG_CNAME VARCHAR(256)  DEFAULT ' '    NOT NULL COMMENT '组织名称',
  ORG_BRIEF_NAME VARCHAR(256)  DEFAULT ' '    NOT NULL COMMENT '组织别名',
  ORG_TYPE VARCHAR(10)  DEFAULT ' '    NOT NULL COMMENT '组织类型',
  PARENT_ORG_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '上级组织ID',
  ESTABLISH_DATE VARCHAR(14)  DEFAULT ' '    NOT NULL COMMENT '成立日期',
  ORG_LEVEL VARCHAR(4)  DEFAULT ' '    NOT NULL COMMENT '组织级别',
  ORG_NODE_TYPE VARCHAR(1)  DEFAULT ' '    NOT NULL COMMENT '结点类型(1-树结点|2-叶子结点)',
  SORT_INDEX INTEGER  DEFAULT 0  NOT NULL   COMMENT '排序',
  REC_CREATOR VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
  REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
  REC_REVISOR VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
  REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
  ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL COMMENT '归档标记',
  IS_DELETED VARCHAR(1) DEFAULT '' NOT NULL COMMENT '逻辑删除(1-已删除|0-正常状态)',
  PRIMARY KEY (ORG_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='组织机构表';

CREATE TABLE TXSOG02 (
  ORG_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '组织ID',
  USER_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '用户ID',
  REC_CREATOR VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
  REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
  REC_REVISOR VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
  REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
  ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL COMMENT '归档标记',
  DEFAULT_RELATION VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '属性组织与用户的默认关系(1 代表默认关系)',
  PRIMARY KEY (ORG_ID,USER_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='组织机构与人员表';

CREATE TABLE TXSOG03 (
  ORG_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '组织ID',
  USER_GROUP_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '用户组ID',
  REC_CREATOR VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
  REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
  REC_REVISOR VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
  REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
  ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL COMMENT '归档标记',
  PRIMARY KEY (ORG_ID,USER_GROUP_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='组织机构与用户组表';

CREATE TABLE TXSOG0101
(
  ID              VARCHAR(32) DEFAULT ' '   NOT NULL COMMENT 'ID',
  ORG_ID         VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '组织ID',
  PARENT_ORG_ID VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '父组织ID',
  FIELD_ID        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '属性ID',
  VALUE           VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '属性值',
  DEFAULT_TYPE		VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '默认属性标记(1 代表默认属性)',
  REC_CREATOR     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '创建人',
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' '    NOT NULL COMMENT '创建时间',
  REC_REVISOR     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '修改人',
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' '    NOT NULL COMMENT '修改时间',
  REC_FLAG        VARCHAR(1) DEFAULT ' '    NOT NULL COMMENT '归档标记',
  PRIMARY KEY (ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='组织机构与用户组表';

CREATE TABLE TXSOG07
(
  ID              VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT 'ID',
  ORG_ID         VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '组织ID',
  COMPANY_ENAME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '公司编码',
  COMPANY_CNAME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '公司中文名',
  SOB_ENAME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '账套编码',
  SOB_CNAME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '账套中文名',
  REC_CREATOR     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '创建人',
  REC_CREATE_TIME VARCHAR(14) DEFAULT ' '    NOT NULL COMMENT '创建时间',
  REC_REVISOR     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '修改人',
  REC_REVISE_TIME VARCHAR(14) DEFAULT ' '    NOT NULL COMMENT '修改时间',
  REC_FLAG        VARCHAR(1) DEFAULT ' '    NOT NULL COMMENT '归档标记',
  PRIMARY KEY (ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='公司别和账套表';

create table TXSLV01
(
  REC_CREATOR                 VARCHAR(256) default ' ' not null COMMENT '创建人',
  REC_CREATE_TIME             VARCHAR(14)  default ' ' not null COMMENT '创建时间',
  REC_REVISOR                 VARCHAR(256) default ' ' not null COMMENT '修改人',
  REC_REVISE_TIME             VARCHAR(14)  default ' ' not null COMMENT '修改时间',
  ARCHIVE_FLAG                VARCHAR(1)   default ' ' not null COMMENT '归档标记',
  ORG_ID                      VARCHAR(32)  default ' ' not null COMMENT '组织机构ID',
  ORG_ADM_ID                  VARCHAR(32)  default ' ' not null COMMENT '分级管理员ID',
  ORG_PERM_MANAGER            VARCHAR(1)   default '0' not null COMMENT '维护分级管理员权限',
  ORG_PERM_RES_RANGE          VARCHAR(1)   default '0' not null COMMENT '分级资源授权范围管理权限',
  ORG_PERM_ORG_MAPPING        VARCHAR(1)   default '0' not null COMMENT '组织机构映射权限',
  ORG_PERM_USER_GROUP         VARCHAR(1)   default '0' not null COMMENT '用户组维护权限',
  ORG_PERM_USER_GROUP_MEMBRER VARCHAR(1)   default '0' not null COMMENT '用户组成员维护权限',
  ORG_PERM_AUTH               VARCHAR(1)   default '0' not null COMMENT '授权关系维护权限',
  ORG_PERM_ORG                VARCHAR(1)   default '0' not null COMMENT '组织机构维护权限',
  TENANT_ID                   VARCHAR(32)  default ' ' not null COMMENT '租户ID',
  primary key (ORG_ID, ORG_ADM_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='分级管理员权限';

create table TXSLV02
(
  REC_CREATOR     VARCHAR(256) default ' ' not null COMMENT '创建人',
  REC_CREATE_TIME VARCHAR(14)  default ' ' not null COMMENT '创建时间',
  REC_REVISOR     VARCHAR(256) default ' ' not null COMMENT '修改人',
  REC_REVISE_TIME VARCHAR(14)  default ' ' not null COMMENT '修改时间',
  ARCHIVE_FLAG    VARCHAR(1)   default ' ' not null COMMENT '归档标记',
  ORG_ID          VARCHAR(32)  default ' ' not null COMMENT '组织机构ID',
  OBJECT_ID       VARCHAR(32)  default ' ' not null COMMENT '授权客体ID',
  OBJECT_TYPE     VARCHAR(16)  default ' ' not null COMMENT '授权客体类型',
  TENANT_ID       VARCHAR(32)  default ' ' not null COMMENT '租户ID',
  primary key (ORG_ID, OBJECT_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='分级资源范围表';

create table TXSLV03
(
  ORG_ID          VARCHAR(32)             not null COMMENT '组织机构ID',
  HROG_CODE       VARCHAR(32)             not null COMMENT '映射EHR组织机构代码',
  REC_CREATOR     VARCHAR(16) default ' ' not null COMMENT '记录创建责任者',
  REC_CREATE_TIME VARCHAR(17) default ' ' not null COMMENT '记录创建时刻',
  REC_REVISOR     VARCHAR(16) default ' ' not null COMMENT '记录修改责任者',
  REC_REVISE_TIME VARCHAR(17) default ' ' not null COMMENT '记录修改时刻',
  TENANT_ID       VARCHAR(64) default ' ' not null COMMENT '租户ID',
  ARCHIVE_FLAG    VARCHAR(1)  default ' ' not null COMMENT '归档标记',
  primary key (ORG_ID, HROG_CODE)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='分级授权组织机构映射关系表';



create table XS_POST
(
  POST_ID         VARCHAR(32)              not null COMMENT '岗位编码',
  POST_ENAME      VARCHAR(32)  default ' ' not null COMMENT '岗位英文名',
  POST_CNAME      VARCHAR(128) default ' ' not null COMMENT '岗位中文名',
  ORG_ID          VARCHAR(32)  default ' ' not null COMMENT '组织机构编码',
  POST_ATTRIBUTE  VARCHAR(10)  default ' ' not null COMMENT '岗位性质',
  POST_PROFESSION VARCHAR(10)  default ' ' not null COMMENT '专业',
  PHONE_NUMBER    VARCHAR(20)  default ' ' not null COMMENT '电话号码',
  REC_CREATOR     VARCHAR(32)  default ' ' not null COMMENT '创建人',
  REC_CREATE_TIME VARCHAR(14)  default ' ' not null COMMENT '创建时间',
  REC_REVISOR     VARCHAR(32)  default ' ' not null COMMENT '修改人',
  REC_REVISE_TIME VARCHAR(14)  default ' ' not null COMMENT '修改时间',
  ARCHIVE_FLAG    VARCHAR(1)   default ' ' not null COMMENT '归档标记',
  constraint XS_POST_PK primary key (POST_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='岗位信息表';
create index INDEX_XS_POST_ORG_ID on XS_POST (ORG_ID);



create table XS_POST_USER
(
  POST_ID         VARCHAR(32)             not null COMMENT '岗位编码',
  USER_ID         VARCHAR(32)             not null COMMENT '用户编码',
  USER_TYPE       VARCHAR(10) default ' ' not null COMMENT '用户类型',
  EXPIRE_DATE     VARCHAR(8)  default ' ' not null COMMENT '过期日期',
  REC_CREATOR     VARCHAR(32)  default ' ' not null COMMENT '创建人',
  REC_CREATE_TIME VARCHAR(14)  default ' ' not null COMMENT '创建时间',
  REC_REVISOR     VARCHAR(32)  default ' ' not null COMMENT '修改人',
  REC_REVISE_TIME VARCHAR(14)  default ' ' not null COMMENT '修改时间',
  ARCHIVE_FLAG    VARCHAR(1)   default ' ' not null COMMENT '归档标记',
  constraint XS_POST_USER_PK
    primary key (POST_ID, USER_ID)
)COMMENT ='岗位用户成员表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

COMMIT;



CREATE INDEX IDX_2PC_LOG_TRACE_TRANS_ID ON ED_XM_2PC_LOG (TRACE_ID, TRANSACTION_ID);
CREATE INDEX IDX_2PC_LOG_TIME_STAMP ON ED_XM_2PC_LOG (TIME_STAMP);

CREATE INDEX IDX_XS_USER_LOGIN_NAME ON XS_USER (LOGIN_NAME);
CREATE INDEX IDX_XS_USER_GROUP_ENAME ON XS_USER_GROUP (GROUP_ENAME);
CREATE INDEX IDX_XS_USER_GROUP_MEMBER_IDS ON XS_USER_GROUP_MEMBER (MEMBER_ID, PARENT_ID);
CREATE INDEX IDX_XS_RESOURCE_RESOURCE_ENAME ON XS_RESOURCE (RESOURCE_ENAME);
CREATE INDEX IDX_XS_RESOURCE_GROUP_ENAME ON XS_RESOURCE_GROUP (RESOURCE_GROUP_ENAME);

CREATE INDEX IDX_EM_HISTORY_TIME_SERVICE ON EM_HISTORY(OPERATE_START_TIME,OPERATE_END_TIME,EVENT_ID,SERVICE_NAME,METHOD_NAME);

CREATE INDEX IDX_TEWPD01_ACT_PROC_DEF_ID ON TEWPD01(ACT_PROC_DEF_ID);
CREATE INDEX IDX_TEWPD02_TRANSITION_DEF_ID ON TEWPD02(TRANSITION_DEF_ID);
CREATE INDEX IDX_TEWPD02_PROC_DEF_ID ON TEWPD02(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPD02_BEGIN_NODE_ID ON TEWPD02(BEGIN_NODE_ID);
CREATE INDEX IDX_TEWPD02_END_NODE_ID ON TEWPD02(END_NODE_ID);
CREATE INDEX IDX_TEWPD03_NODE_DEF_ID ON TEWPD03(NODE_DEF_ID);
CREATE INDEX IDX_TEWPD03_PROC_DEF_ID ON TEWPD03(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPD03_NODE_KEY ON TEWPD03(NODE_KEY);
CREATE INDEX IDX_TEWPD04_NODE_KEY ON TEWPD04(NODE_KEY);
CREATE INDEX IDX_TEWPI00_ACT_PROC_DEF_ID ON TEWPI00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPI00_ACT_INST_ID ON TEWPI00(ACT_INSTANCE_ID);
CREATE INDEX IDX_TEWPI00_PROC_DEF_ID ON TEWPI00(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPI00_PARENT_INST_ID ON TEWPI00(PARENT_INSTANCE_ID);
CREATE INDEX IDX_HEWPI00_ACT_PROC_DEF_ID ON HEWPI00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_HEWPI00_ACT_INST_ID ON HEWPI00(ACT_INSTANCE_ID);
CREATE INDEX IDX_HEWPI00_PROC_DEF_ID ON HEWPI00(PROCESS_DEF_ID);
CREATE INDEX IDX_HEWPI00_PARENT_INST_ID ON HEWPI00(PARENT_INSTANCE_ID);
CREATE INDEX IDX_TEWPI02_PROC_INST_ID ON TEWPI02(PROCESS_INSTANCE_ID);
CREATE INDEX IDX_TEWPI02_PROC_DEF_ID ON TEWPI02(PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPT00_PROC_INST_ID ON TEWPT00(PROCESS_INSTANCE_ID);
CREATE INDEX IDX_TEWPT00_TASK_ID ON TEWPT00(TASK_ID);
CREATE INDEX IDX_TEWPT00_STATE ON TEWPT00(STATE);
CREATE INDEX IDX_TEWPT00_ASSIGNEE_ID ON TEWPT00(ASSIGNEE_ID);
CREATE INDEX IDX_TEWPT00_COMPLETER_ID ON TEWPT00(COMPLETER_ID);
CREATE INDEX IDX_TEWPT00_PROC_KEY ON TEWPT00(PROCESS_KEY);
CREATE INDEX IDX_TEWPT00_ACT_PROC_DEF_ID ON TEWPT00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_TEWPT00_ACT_INSTANCE_ID ON TEWPT00(ACT_INSTANCE_ID);
CREATE INDEX IDX_TEWPT00_TASK_DEF_KEY ON TEWPT00(TASK_DEF_KEY);
CREATE INDEX IDX_HEWPT00_PROC_INST_ID ON HEWPT00(PROCESS_INSTANCE_ID);
CREATE INDEX IDX_HEWPT00_TASK_ID ON HEWPT00(TASK_ID);
CREATE INDEX IDX_HEWPT00_STATE ON HEWPT00(STATE);
CREATE INDEX IDX_HEWPT00_ASSIGNEE_ID ON HEWPT00(ASSIGNEE_ID);
CREATE INDEX IDX_HEWPT00_COMPLETER_ID ON HEWPT00(COMPLETER_ID);
CREATE INDEX IDX_HEWPT00_PROC_KEY ON HEWPT00(PROCESS_KEY);
CREATE INDEX IDX_HEWPT00_ACT_PROC_DEF_ID ON HEWPT00(ACT_PROCESS_DEF_ID);
CREATE INDEX IDX_HEWPT00_ACT_INSTANCE_ID ON HEWPT00(ACT_INSTANCE_ID);
CREATE INDEX IDX_HEWPT00_TASK_DEF_KEY ON HEWPT00(TASK_DEF_KEY);
CREATE INDEX IDX_TEWPT01_PROC_INST_ID ON TEWPT01(PROCESS_INST_ID);
CREATE INDEX IDX_TEWPT01_ACT_PROC_INST_ID ON TEWPT01(ACT_PROCESS_INST_ID);
CREATE INDEX IDX_TEWPT01_TASK_ID ON TEWPT01(TASK_ID);


CREATE INDEX IDX_EJ_QRTZ_J_REQ_RECOVERY ON EJ_QRTZ_JOB_DETAILS(SCHED_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_EJ_QRTZ_J_GRP ON EJ_QRTZ_JOB_DETAILS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_J ON EJ_QRTZ_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_JG ON EJ_QRTZ_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_C ON EJ_QRTZ_TRIGGERS(SCHED_NAME,CALENDAR_NAME);
CREATE INDEX IDX_EJ_QRTZ_T_G ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_EJ_QRTZ_T_STATE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_N_STATE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_N_G_STATE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_NEXT_FIRE_TIME ON EJ_QRTZ_TRIGGERS(SCHED_NAME,NEXT_FIRE_TIME);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_ST ON EJ_QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE,NEXT_FIRE_TIME);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_MISFIRE ON EJ_QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_ST_MISF ON EJ_QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_T_NFT_ST_MISF_GRP ON EJ_QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_EJ_QRTZ_FT_TRIG_INST ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME);
CREATE INDEX IDX_EJ_QRTZ_FT_INST_REQ_RCVRY ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_EJ_QRTZ_FT_J_G ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_JG ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_T_G ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_TG ON EJ_QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_EJ_QRTZ_FT_LOG01 ON EJ_QRTZ_LOGGING(START_TIME);
CREATE INDEX IDX_EJ_QRTZ_FT_LOG02 ON EJ_QRTZ_LOGGING(STATUS, TRIGGER_NAME);


CREATE TABLE
    EM_GROUP
    (
        GROUP_NAME VARCHAR(20) COLLATE utf8_bin NOT NULL COMMENT '分组名称',
        GROUP_DESC VARCHAR(250) COLLATE utf8_bin NOT NULL COMMENT '分组描述',
        MAX_RETRY_TIMES DECIMAL(4,0) NOT NULL COMMENT '最大重试次数',
        INTERVAL_TIME DECIMAL(6,0) NOT NULL COMMENT '重发间隔时间',
        IS_SEQUENCE VARCHAR(1) COLLATE utf8_bin DEFAULT ' ' NOT NULL COMMENT '是否时序',
        REC_CREATOR VARCHAR(255) COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(255) COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻',
        ARCHIVE_FLAG VARCHAR(1) COLLATE utf8_bin NOT NULL COMMENT '归档标记',
        PRIMARY KEY (GROUP_NAME)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分组策略配置';
CREATE TABLE
    EM_GROUP_EVENT_RELA
    (
        GROUP_NAME VARCHAR(20) COLLATE utf8_bin NOT NULL COMMENT '分组名称',
        EVENT_ID VARCHAR(128) COLLATE utf8_bin NOT NULL COMMENT '电文号',
        REC_CREATOR VARCHAR(255) COLLATE utf8_bin NOT NULL COMMENT '记录创建责任者',
        REC_CREATE_TIME VARCHAR(14) COLLATE utf8_bin NOT NULL COMMENT '记录创建时刻',
        REC_REVISOR VARCHAR(255) COLLATE utf8_bin NOT NULL COMMENT '记录修改责任者',
        REC_REVISE_TIME VARCHAR(14) COLLATE utf8_bin NOT NULL COMMENT '记录修改时刻',
        ARCHIVE_FLAG VARCHAR(1) COLLATE utf8_bin NOT NULL COMMENT '归档标记',
        PRIMARY KEY (EVENT_ID),
        INDEX IDX_GROUP_NAME (GROUP_NAME)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分组电文表';

create index IDX_EM_GROUP_EVENT_RELA on EM_GROUP_EVENT_RELA (GROUP_NAME);

CREATE TABLE
    EM_TELE_CONFIG
    (
        EVENT_ID VARCHAR(64) COLLATE utf8_bin NOT NULL ,
        TELE_TYPE VARCHAR(20) COLLATE utf8_bin default 'text' NOT NULL ,
        REMOTE_SYS_CODE VARCHAR(20) COLLATE utf8_bin NOT NULL ,
        CATEGORY VARCHAR(16) COLLATE utf8_bin NOT NULL ,
        REC_CREATOR VARCHAR(256) COLLATE utf8_bin NOT NULL ,
        REC_CREATE_TIME VARCHAR(14) COLLATE utf8_bin NOT NULL ,
        REC_REVISOR VARCHAR(256) COLLATE utf8_bin NOT NULL ,
        REC_REVISE_TIME VARCHAR(14) COLLATE utf8_bin NOT NULL ,
        ARCHIVE_FLAG VARCHAR(1) COLLATE utf8_bin NOT NULL ,
        PRIMARY KEY (EVENT_ID)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8;
create index IDX_EM_TELE_CONFIG_TELE_TYPE
    on EM_TELE_CONFIG (TELE_TYPE);

    create table EM_TELE_TYPE
    (
        TELE_TYPE VARCHAR(20) COLLATE utf8_bin default ' ' NOT NULL ,
        TELE_TYPE_DESC VARCHAR(250) COLLATE utf8_bin default ' ' NOT NULL ,
        TRANS_TELE_SERVICE_ID VARCHAR(20) COLLATE utf8_bin NOT NULL ,
        TRANS_EIINFO_SERVICE_ID VARCHAR(20) COLLATE utf8_bin default ' ' NOT NULL  ,
        REC_CREATOR VARCHAR(256) COLLATE utf8_bin  default ' ' NOT NULL ,
        REC_CREATE_TIME VARCHAR(14) COLLATE utf8_bin  default ' ' NOT NULL ,
        REC_REVISOR VARCHAR(256) COLLATE utf8_bin  default ' ' NOT NULL ,
        REC_REVISE_TIME VARCHAR(14) COLLATE utf8_bin  default ' ' NOT NULL ,
        ARCHIVE_FLAG VARCHAR(1) COLLATE utf8_bin  default ' ' NOT NULL ,
        TELE_BEAN_TYPE VARCHAR(1) COLLATE utf8_bin  default 0,
        PRIMARY KEY (TELE_TYPE)
    );


create table TEUDM03
(
    REC_CREATOR     VARCHAR(16)  COLLATE utf8_bin default ' ',
    REC_REVISOR     VARCHAR(16)  COLLATE utf8_bin  default ' ',
    REC_CREATE_TIME VARCHAR(14)  COLLATE utf8_bin  default ' ',
    REC_REVISE_TIME VARCHAR(14)  COLLATE utf8_bin  default ' ',
    ARCHIVE_FLAG    VARCHAR(1)   COLLATE utf8_bin  default ' ',
    FORM_ENAME      VARCHAR(20)  COLLATE utf8_bin  not null,
    LOCALE_LANG     VARCHAR(20)  COLLATE utf8_bin  default 'zh_CN' not null,
    DOC_PATH        VARCHAR(250) COLLATE utf8_bin  default ' ',
    DOC_CNAME       VARCHAR(200) COLLATE utf8_bin  default ' ',
    KEY_WORD        VARCHAR(200) COLLATE utf8_bin  default ' ',
    BOOKMARK        VARCHAR(200) COLLATE utf8_bin  default ' ',
    constraint TEUDM03_PRIMARY_KEY
        primary key (FORM_ENAME, LOCALE_LANG)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



create table TEDLG00
(
    TYPE_NAME         VARCHAR(32)  COLLATE utf8_bin default ' ' not null,
    TYPE_DESC         VARCHAR(255) COLLATE utf8_bin default ' ' not null,
    REC_CREATOR       VARCHAR(16)  COLLATE utf8_bin default ' ' not null,
    REC_CREATE_TIME   VARCHAR(17)  COLLATE utf8_bin default ' ' not null,
    KEYWORD_ONE       VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_TWO       VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_THREE     VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_FOUR      VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_FIVE      VARCHAR(32)  COLLATE utf8_bin default ' ',
    REC_REVISOR       VARCHAR(16)  COLLATE utf8_bin default ' ' not null,
    REC_REVISE_TIME   VARCHAR(17)  COLLATE utf8_bin default ' ' not null,
    ARCHIVE_FLAG      VARCHAR(1)   COLLATE utf8_bin default ' ' not null,
    ARCHIVE_TYPE      VARCHAR(1)   COLLATE utf8_bin default '3',
    ARCHIVE_KEEP_TIME DECIMAL(5)   COLLATE utf8_bin default 3   not null,
    constraint PK_TEDLG00
        primary key (TYPE_NAME)
);

DROP FUNCTION IF EXISTS queryAuthInfo;
DROP FUNCTION IF EXISTS queryMenuPageInfo;
DROP FUNCTION IF EXISTS queryParentInfo;
DROP FUNCTION IF EXISTS queryResourceParentInfo;
DROP FUNCTION IF EXISTS queryChildInfo;
DROP FUNCTION IF EXISTS queryParentOrgList;
DROP FUNCTION IF EXISTS getChildrenOrgType;
DROP FUNCTION IF EXISTS getChildrenOrg;
DROP FUNCTION IF EXISTS queryParentOrgs;

--/
CREATE FUNCTION queryAuthInfo(queryId VARCHAR(32))
        RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd is not null DO
        SET sTemp = concat(sTemp,',',sTempChd);
        SELECT group_concat(object_id) INTO sTempChd FROM XS_AUTHORIZATION where subject_id <> object_id and  FIND_IN_SET(subject_id,sTempChd)>0;
        END WHILE;
        RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryMenuPageInfo(queryId VARCHAR(32))
        RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd is not null DO
        SET sTemp = concat(sTemp,',',sTempChd);
        SELECT group_concat(node_ename) INTO sTempChd FROM tedpi10 where node_ename <> tree_ename and  FIND_IN_SET(tree_ename,sTempChd)>0;
        END WHILE;
        RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryParentInfo(queryId VARCHAR(32))
        RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd is not null DO
        SET sTemp = concat(sTemp,',',sTempChd);
        SELECT group_concat(parent_id) INTO sTempChd FROM xs_user_group_member where member_id <> parent_id and  FIND_IN_SET(member_id,sTempChd)>0;
        END WHILE;
        RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryChildInfo(queryId VARCHAR(32))
        RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd is not null DO
        SET sTemp = concat(sTemp,',',sTempChd);
        SELECT group_concat(member_id) INTO sTempChd FROM xs_user_group_member where  FIND_IN_SET(parent_id,sTempChd)>0;
        END WHILE;
        RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryResourceParentInfo(queryId VARCHAR(32))
        RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd is not null DO
        SET sTemp = concat(sTemp,',',sTempChd);
        SELECT group_concat(resource_parent_id) INTO sTempChd FROM xs_resource_group_member where resource_member_id <> resource_parent_id and  FIND_IN_SET(resource_member_id,sTempChd)>0;
        END WHILE;
        RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryParentOrgList(orgid VARCHAR(50),orgType VARCHAR(50))
        RETURNS longtext
BEGIN
        DECLARE sOrgId longtext;
        DECLARE sParentId longtext;
        DECLARE sType varchar(50);
        SET sOrgId=orgid;
        SET sParentId = orgid;
        SET sType='$';
        SELECT ORG_TYPE INTO sType FROM txsog01 WHERE org_id = sParentId;
        SELECT PARENT_ORG_ID INTO sParentId FROM txsog01 WHERE org_id = sParentId;
        WHILE sParentId <> '' and sType<>orgType DO
        set sOrgId=CONCAT(sOrgId,',',sParentId);
        SELECT PARENT_ORG_ID INTO sParentId FROM txsog01 WHERE org_id = sParentId;
        SELECT ORG_TYPE INTO sType FROM txsog01 WHERE org_id = sParentId;
        END WHILE;
        RETURN sOrgId;
END
/


--/
create function getChildrenOrg(orgid VARCHAR(4000))
        returns longtext
BEGIN
        DECLARE oTemp longtext;
        DECLARE oTempChild longtext;
        SET oTemp = '$';
        SET oTempChild = orgid;
        WHILE oTempChild IS NOT NULL
        DO
        SET oTemp = CONCAT(oTemp,',',oTempChild);
        SELECT GROUP_CONCAT(org_id) INTO oTempChild FROM txsog01 WHERE FIND_IN_SET(parent_org_id,oTempChild) > 0;
        END WHILE;
        RETURN oTemp;
END
/


--/
CREATE FUNCTION getChildrenOrgType(orgid VARCHAR(400),orgType varchar(50))
        RETURNS longtext
BEGIN
        DECLARE oTemp longtext;
        DECLARE oTempChild longtext;
        SET oTemp = '$';
        SET oTempChild = orgid;
        WHILE oTempChild IS NOT NULL
        DO
        SET oTemp = CONCAT(oTemp,',',oTempChild);
        SELECT GROUP_CONCAT(org_id) INTO oTempChild FROM txsog01 WHERE FIND_IN_SET(parent_org_id,oTempChild) > 0 AND IS_DELETED = '0'
				AND ORG_TYPE != orgType;
        END WHILE;
        RETURN oTemp;
END
/


--/
CREATE FUNCTION queryParentOrgs(orgid VARCHAR(5000))
        RETURNS longtext
BEGIN
        DECLARE sOrgId longtext;
        DECLARE sParentId longtext;

        SET sOrgId=orgid;
        SET sParentId = orgid;
        SELECT GROUP_CONCAT(PARENT_ORG_ID) INTO sParentId FROM txsog01 WHERE FIND_IN_SET(org_id,sParentId) > 0;

        WHILE sParentId <> '' DO
        set sOrgId=CONCAT(sOrgId,',',sParentId);
        SELECT GROUP_CONCAT(PARENT_ORG_ID) INTO sParentId FROM txsog01 WHERE FIND_IN_SET(org_id,sParentId) > 0;
        END WHILE;
        RETURN sOrgId;
END
/

--新增初始化表

CREATE TABLE TEDNM07
(
    REC_CREATOR              VARCHAR(16) DEFAULT ' '    NOT NULL COMMENT '创建人',
    REC_CREATE_TIME         VARCHAR(17) DEFAULT ' '    NOT NULL COMMENT '创建时间',
    REC_REVISOR        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '修改时间',
    APP_NAME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '应用名称',
    KEY_TYPE     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '密钥类型',
    SECRET_KEY VARCHAR(14) DEFAULT ' '    NOT NULL COMMENT '密钥',
    ALGORITHM     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '算法',
    PRIMARY KEY (APP_NAME, KEY_TYPE, ALGORITHM)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='表EDNM07';


CREATE TABLE TEDCC12
(
    REC_CREATOR              VARCHAR(16) DEFAULT ' '    NOT NULL COMMENT '创建人',
    REC_CREATE_TIME         VARCHAR(17) DEFAULT ' '    NOT NULL COMMENT '创建时间',
    REC_REVISOR        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME        VARCHAR(128) DEFAULT ' '    NOT NULL COMMENT '修改时间',
    URL_CONFIG_ENAME        VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '访问地址配置英文名',
    CONFIG_DESC     VARCHAR(255) DEFAULT ' '    NOT NULL COMMENT '配置描述',
    MATCH_TYPE VARCHAR(10) DEFAULT ' '    NOT NULL COMMENT '匹配模式',
    MATCH_EXPRESSION     VARCHAR(4000) DEFAULT ' '    NOT NULL COMMENT '匹配表达式',
    IS_AUTHORIZED     VARCHAR(1) DEFAULT ' '    NOT NULL COMMENT '是否授权',
    CONFIG_ID     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '配置ID',
    SORT_INDEX     VARCHAR(32) DEFAULT ' '    NOT NULL COMMENT '排序',
        PRIMARY KEY (CONFIG_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='访问地址管理表';


