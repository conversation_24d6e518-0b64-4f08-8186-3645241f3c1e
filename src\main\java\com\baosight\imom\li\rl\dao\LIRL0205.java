/**
 * Generate time : 2025-02-07 9:06:15
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Tlirl0205
 *
 */
public class LIRL0205 extends DaoEPBase {

    public static final String QUERY = "LIRL0205.query";
    public static final String COUNT = "LIRL0205.count";
    public static final String INSERT = "LIRL0205.insert";
    public static final String UPDATE = "LIRL0205.update";
    public static final String DELETE = "LIRL0205.delete";

    private String reservationNumber = " ";        /* 预约单号*/
    private String segNo = " ";        /* 业务单元代代码*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String visitUnitCode = " ";        /* 拜访单位代码*/
    private String visitUnitName = " ";        /* 拜访单位*/
    private Integer levelNum = Integer.valueOf(0);        /* 层级*/
    private String fvisitUnitCode = " ";        /* 上级单位代码*/
    private String fvisitUnitName = " ";        /* 上级单位*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 说明备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private List<LIRL0205> children;

    /**
     * the constructor
     */
    public LIRL0205() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitUnitCode");
        eiColumn.setDescName("拜访单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitUnitName");
        eiColumn.setDescName("拜访单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("levelNum");
        eiColumn.setDescName("层级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fvisitUnitCode");
        eiColumn.setDescName("上级单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fvisitUnitName");
        eiColumn.setDescName("上级单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("说明备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * get the reservationNumber - 预约单号
     * @return the reservationNumber
     */
    public String getReservationNumber() {
        return this.reservationNumber;
    }

    /**
     * set the reservationNumber - 预约单号
     */
    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    /**
     * get the segNo - 业务单元代代码
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 业务单元代代码
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the visitUnitCode - 拜访单位代码
     * @return the visitUnitCode
     */
    public String getVisitUnitCode() {
        return this.visitUnitCode;
    }

    /**
     * set the visitUnitCode - 拜访单位代码
     */
    public void setVisitUnitCode(String visitUnitCode) {
        this.visitUnitCode = visitUnitCode;
    }

    /**
     * get the visitUnitName - 拜访单位
     * @return the visitUnitName
     */
    public String getVisitUnitName() {
        return this.visitUnitName;
    }

    /**
     * set the visitUnitName - 拜访单位
     */
    public void setVisitUnitName(String visitUnitName) {
        this.visitUnitName = visitUnitName;
    }

    /**
     * get the levelNum - 层级
     * @return the levelNum
     */
    public Integer getLevelNum() {
        return this.levelNum;
    }

    /**
     * set the levelNum - 层级
     */
    public void setLevelNum(Integer levelNum) {
        this.levelNum = levelNum;
    }

    /**
     * get the fvisitUnitCode - 上级单位代码
     * @return the fvisitUnitCode
     */
    public String getFvisitUnitCode() {
        return this.fvisitUnitCode;
    }

    /**
     * set the fvisitUnitCode - 上级单位代码
     */
    public void setFvisitUnitCode(String fvisitUnitCode) {
        this.fvisitUnitCode = fvisitUnitCode;
    }

    /**
     * get the fvisitUnitName - 上级单位
     * @return the fvisitUnitName
     */
    public String getFvisitUnitName() {
        return this.fvisitUnitName;
    }

    /**
     * set the fvisitUnitName - 上级单位
     */
    public void setFvisitUnitName(String fvisitUnitName) {
        this.fvisitUnitName = fvisitUnitName;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 说明备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 说明备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public void setChildren(List<LIRL0205> children) {
        this.children = children;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setReservationNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationNumber")), reservationNumber));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setVisitUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitUnitCode")), visitUnitCode));
        setVisitUnitName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitUnitName")), visitUnitName));
        setLevelNum(NumberUtils.toInteger(StringUtils.toString(map.get("levelNum")), levelNum));
        setFvisitUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("fvisitUnitCode")), fvisitUnitCode));
        setFvisitUnitName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("fvisitUnitName")), fvisitUnitName));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}