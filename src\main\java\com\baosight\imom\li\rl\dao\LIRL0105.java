/**
 * Generate time : 2024-08-06 16:57:39
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0105 车辆预约数管理表
 */
public class LIRL0105 extends DaoEPBase {

    public static final String QUERY = "LIRL0105.query";
    public static final String QUERY_RESERVESION = "LIRL0105.queryReservesion";
    public static final String QUERY_RESERVATION_MAX_NUM = "LIRL0105.queryReservationMaxNum";
    public static final String QUERY_TYPE_OF_HANDLING = "LIRL0105.queryTypeOfHandling";
    public static final String COUNT = "LIRL0105.count";
    public static final String INSERT = "LIRL0105.insert";
    public static final String UPDATE = "LIRL0105.update";
    public static final String UPDATE_STATUS = "LIRL0105.updateStatus";
    public static final String DELETE = "LIRL0105.delete";
    private String segNo = " ";        /* 业务单元代代码*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String status = " ";        /* 状态(撤销：00、新增：10、生效：20)*/
    private String typeOfHandling = " ";        /* 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)*/
    private String reservationIdentity = " ";        /* 预约身份(包含承运商和客户)*/
    private String customerId = " ";        /* 承运商/客户代码*/
    private String customerName = " ";        /* 承运商/客户名称*/
    private Long reservationMaxNum = 0L;        /* 预约最大数*/
    private String mergeTags = " ";        /* 合并标记*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private String reservationMaxNumDay = " ";        /* 预约数(每日)*/

    /**
     * the constructor
     */
    public LIRL0105() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10、生效：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("typeOfHandling");
        eiColumn.setDescName("装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationIdentity");
        eiColumn.setDescName("预约身份(包含承运商和客户)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationMaxNum");
        eiColumn.setDescName("预约最大数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mergeTags");
        eiColumn.setDescName("合并标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationMaxNumDay");
        eiColumn.setDescName("预约数最大数（每日）");
        eiMetadata.addMeta(eiColumn);


    }

    public String getReservationMaxNumDay() {
        return reservationMaxNumDay;
    }

    public void setReservationMaxNumDay(String reservationMaxNumDay) {
        this.reservationMaxNumDay = reservationMaxNumDay;
    }

    /**
     * get the segNo - 业务单元代代码
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 业务单元代代码
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the segName - 业务单元简称
     *
     * @return the segName
     */
    public String getSegName() {
        return this.segName;
    }

    /**
     * set the segName - 业务单元简称
     */
    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the unitCode - 业务单元代代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the status - 状态(撤销：00、新增：10、生效：20)
     *
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态(撤销：00、新增：10、生效：20)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the typeOfHandling - 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
     *
     * @return the typeOfHandling
     */
    public String getTypeOfHandling() {
        return this.typeOfHandling;
    }

    /**
     * set the typeOfHandling - 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
     */
    public void setTypeOfHandling(String typeOfHandling) {
        this.typeOfHandling = typeOfHandling;
    }

    /**
     * get the reservationIdentity - 预约身份(包含承运商和客户)
     *
     * @return the reservationIdentity
     */
    public String getReservationIdentity() {
        return this.reservationIdentity;
    }

    /**
     * set the reservationIdentity - 预约身份(包含承运商和客户)
     */
    public void setReservationIdentity(String reservationIdentity) {
        this.reservationIdentity = reservationIdentity;
    }

    /**
     * get the customerId - 承运商/客户代码
     *
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 承运商/客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the customerName - 承运商/客户名称
     *
     * @return the customerName
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * set the customerName - 承运商/客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * get the reservationMaxNum - 预约最大数
     *
     * @return the reservationMaxNum
     */
    public Long getReservationMaxNum() {
        return this.reservationMaxNum;
    }

    /**
     * set the reservationMaxNum - 预约最大数
     */
    public void setReservationMaxNum(Long reservationMaxNum) {
        this.reservationMaxNum = reservationMaxNum;
    }

    /**
     * get the mergeTags - 合并标记
     *
     * @return the mergeTags
     */
    public String getMergeTags() {
        return this.mergeTags;
    }

    /**
     * set the mergeTags - 合并标记
     */
    public void setMergeTags(String mergeTags) {
        this.mergeTags = mergeTags;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the uuid - uuid
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setTypeOfHandling(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("typeOfHandling")), typeOfHandling));
        setReservationIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationIdentity")), reservationIdentity));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setReservationMaxNum(NumberUtils.toLong(StringUtils.toString(map.get("reservationMaxNum")), reservationMaxNum));
        setReservationMaxNumDay(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationMaxNumDay")), reservationMaxNumDay));
        setMergeTags(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mergeTags")), mergeTags));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("typeOfHandling", StringUtils.toString(typeOfHandling, eiMetadata.getMeta("typeOfHandling")));
        map.put("reservationIdentity", StringUtils.toString(reservationIdentity, eiMetadata.getMeta("reservationIdentity")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("reservationMaxNum", StringUtils.toString(reservationMaxNum, eiMetadata.getMeta("reservationMaxNum")));
        map.put("reservationMaxNumDay", StringUtils.toString(reservationMaxNumDay, eiMetadata.getMeta("reservationMaxNumDay")));
        map.put("mergeTags", StringUtils.toString(mergeTags, eiMetadata.getMeta("mergeTags")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));

        return map;

    }
}