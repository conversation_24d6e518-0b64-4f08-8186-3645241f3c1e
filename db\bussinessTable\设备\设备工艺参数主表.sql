create table TMEDV0001
(
    E_ARCHIVES_NO   VARCHAR(20)  default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME  VARCHAR(200) default ' '    not null comment '设备名称',
    PROD_CODE       VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '品种',
    PROD_NAME       VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '品种名称',
    SHOPSIGN        VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '牌号',
    SPEC_DESC       VARCHAR(32)  DEFAULT ' '    NOT NULL COMMENT '规格',
    STATUS          VARCHAR(2)   DEFAULT ' '    NOT NULL COMMENT '状态',
    -- 固定字段
    UUID            VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='设备工艺参数主表' ENGINE = INNODB
                              DEFAULT CHARSET = UTF8
                              COLLATE UTF8_BIN;

-- 增加ER图外键
ALTER TABLE TMEDV0001 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);