<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1006">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE = #machineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineName">
            MACHINE_NAME like concat('%',#machineName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="partId">
            PART_ID = #partId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1006">
        SELECT
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        PART_ID as "partId",  <!-- 物料号 -->
        SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
        MIN_QUANTITY as "minQuantity",  <!-- 最小数量 -->
        SINGLE_PACK_QUANTITY as "singlePackQuantity",  <!-- 单包数量 -->
        STATUS as "status",  <!-- 状态 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM1006 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                MACHINE_CODE
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1006 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </select>

    <select id="countRepeat" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1006 WHERE
        DEL_FLAG = '0'
        AND MACHINE_CODE = #machineCode#
        AND PART_ID = #partId#
        <isNotEmpty prepend=" AND " property="uuid">
            UUID != #uuid#
        </isNotEmpty>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1006 (MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        PART_ID,  <!-- 物料号 -->
        SPECS_DESC,  <!-- 规格描述 -->
        MIN_QUANTITY,  <!-- 最小数量 -->
        SINGLE_PACK_QUANTITY,  <!-- 单包数量 -->
        STATUS,  <!-- 状态 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#machineCode#, #machineName#, #partId#, #specsDesc#, #minQuantity#, #singlePackQuantity#, #status#,
        #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#,
        #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1006 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1006
        SET
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        MACHINE_NAME = #machineName#,   <!-- 机组名称 -->
        PART_ID = #partId#,   <!-- 物料号 -->
        SPECS_DESC = #specsDesc#,   <!-- 规格描述 -->
        MIN_QUANTITY = #minQuantity#,   <!-- 最小数量 -->
        SINGLE_PACK_QUANTITY = #singlePackQuantity#,   <!-- 单包数量 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>