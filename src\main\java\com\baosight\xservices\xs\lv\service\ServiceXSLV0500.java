package com.baosight.xservices.xs.lv.service;


import java.util.List;
import java.util.Map;

public class ServiceXSLV0500 extends ServiceXSLVTreeBase {
    public ServiceXSLV0500() {
    }

    protected String getPermType() {
        return "orgPermUserGroupMembrer";
    }

    public List getChildNodes(String parentLabel) {
        List<Map> ret = super.getChildNodes(parentLabel);
        this.addUserGroup(parentLabel, ret);
        return ret;
    }
}