package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.constants.WorkFlowConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> yzj
 * @Description : 检修计划清单页面后台
 * @Date : 2024/8/22
 * @Version : 1.0
 */
public class ServiceVGDM08 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM08.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0801().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        // 只查询未删除数据
        inInfo.setCell(EiConstant.queryBlock, 0, "delFlag", "0");
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0801.QUERY, new VGDM0801());
    }

    /**
     * 查询异常信息
     */
    public EiInfo queryExceptionContact(EiInfo inInfo) {
        try {
            String exceptionContactId = inInfo.getString("exceptionContactId");
            if (StrUtil.isBlank(exceptionContactId)) {
                return inInfo;
            }
            Map<String, String> map = new HashMap<>();
            map.put("equalId", exceptionContactId);
            List list = dao.query(VGDM0501.QUERY, map);
            inInfo.addBlock(MesConstant.Iplat.DETAIL2_BLOCK).addBlockMeta(new VGDM0501().eiMetadata);
            inInfo.getBlock(MesConstant.Iplat.DETAIL2_BLOCK).addRows(list);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0801 vgdm0801 = new VGDM0801();
            vgdm0801.fromMap(block.getRow(0));
            // 数据校验
            this.checkData(vgdm0801);
            // 新增数据
            vgdm0801.insertData(dao);
            // 生成月度计划明细
            this.insertDetail(vgdm0801);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验点检异常信息
     *
     * @param vgdm0801 点检异常信息
     */
    private void checkData(VGDM0801 vgdm0801) throws Exception {
        // 使用ValidationUtils进行基础校验
        ValidationUtils.validateEntity(vgdm0801);
        // 特殊校验
        if ("20".equals(vgdm0801.getOverhaulType())
                && StrUtil.isBlank(vgdm0801.getOutsourcingContactId())) {
            throw new PlatException("施工类别为委外时委外联络单号不能为空");
        }
        vgdm0801.setOverhaulStartDate(vgdm0801.getOverhaulStartDate().substring(0, 16));
        vgdm0801.setOverhaulEndDate(vgdm0801.getOverhaulEndDate().substring(0, 16));
        // 校验计划检修日期范围
        LocalDateTime startDate = LocalDateTime.parse(vgdm0801.getOverhaulStartDate(), DateUtils.FORMATTER_16);
        LocalDateTime endDate = LocalDateTime.parse(vgdm0801.getOverhaulEndDate(), DateUtils.FORMATTER_16);
        if (startDate.isAfter(endDate)) {
            throw new PlatException("计划检修日期(起)不能晚于计计划检修日期(止)");
        }
        // 计算时间差值（分钟）
        long minutes = ChronoUnit.MINUTES.between(startDate, endDate);
        vgdm0801.setOverhaulTime(BigDecimal.valueOf(minutes));
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0801 vgdm0801 = new VGDM0801();
            vgdm0801.fromMap(block.getRow(0));
            VGDM0801 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
            this.checkData(vgdm0801);
            // 可修改字段
            dbData.setOverhaulQuality(vgdm0801.getOverhaulQuality());
            dbData.setOverhaulType(vgdm0801.getOverhaulType());
            dbData.setImplementManId(vgdm0801.getImplementManId());
            dbData.setImplementManName(vgdm0801.getImplementManName());
            dbData.setOutsourcingContactId(vgdm0801.getOutsourcingContactId());
            dbData.setOverhaulStartDate(vgdm0801.getOverhaulStartDate());
            dbData.setOverhaulEndDate(vgdm0801.getOverhaulEndDate());
            dbData.setOverhaulNumber(vgdm0801.getOverhaulNumber());
            dbData.setOverhaulProject(vgdm0801.getOverhaulProject());
            dbData.setOverhaulTime(vgdm0801.getOverhaulTime());
            dbData.setSecurityMeasures(vgdm0801.getSecurityMeasures());
            dbData.setAcceptanceCriteria(vgdm0801.getAcceptanceCriteria());
            // 赋值通用字段
            Map data = dbData.toMap();
            RecordUtils.setRevisor(data);
            dao.update(VGDM0801.UPDATE, data);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>删除标记改为1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 frontData;
            VGDM0801 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0801();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                dbData.setDelFlag("1");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 提交审核
     */
    public EiInfo submit(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            String userId = UserSession.getLoginName();
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                // 校验数据防止提交空数据
                this.checkData(dbData);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.OVERHAUL_PLAN);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "");
                paramMap.put("userId", userId);
                paramMap.put("comment", "提交审批");
                paramMap.put("variables", new HashMap<>());
                // 判断流程实例ID，空时启动新流程，不空时提交流程
                if (StrUtil.isBlank(dbData.getProcessInstanceId())) {
                    String subject = "检修计划审批:" + vgdm0801.getOverhaulPlanId();
                    String processInstanceId = WorkFlowUtils.start(dbData.getSegNo(),
                            WorkFlowConstant.processKey.OVERHAUL_PLAN
                            , dbData.getUuid(), subject, null);
                    dbData.setProcessInstanceId(processInstanceId);
                }
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 获取下一节点审批人
                WorkFlowUtils.addAuditPersons(dao, paramMap);
                // 状态-已提交
                dbData.setOverhaulPlanStatus(MesConstant.Status.K15);
                // 审批状态-审批中
                dbData.setApprStatus(MesConstant.Status.K60);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, true);
            // 批量更新数据
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            // 返回前端
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_SUBMIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 取消提交
     *
     * <p>终止工作流，清空审批信息
     */
    public EiInfo cancel(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K15);
                // 终止工作流
                WorkFlowUtils.deleteProcess(dbData.getProcessInstanceId());
                // 状态-新增
                dbData.setOverhaulPlanStatus(MesConstant.Status.K10);
                // 流程实例-空
                dbData.setProcessInstanceId(" ");
                // 审批状态-空
                dbData.setApprStatus(" ");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 生成明细
     * <p>
     * 根据设备代码查找最近的月度计划明细生成
     */
    private void insertDetail(VGDM0801 vgdm0801) {
        if (!"20".equals(vgdm0801.getOverhaulQuality())) {
            log("非月度检修");
            return;
        }
        log("月度检修明细自动生成");
        // 获取最近一条月度检修计划
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("segNo", vgdm0801.getSegNo());
        queryMap.put("eArchivesNo", vgdm0801.getEArchivesNo());
        // 查询生效数据
        queryMap.put("minStatus", MesConstant.Status.K20);
        // 月度检修
        queryMap.put("overhaulQuality", "20");
        // 排除当前检修计划
        queryMap.put("notUuid", vgdm0801.getUuid());
        List<VGDM0801> list = dao.query(VGDM0801.QUERY, queryMap, 0, 1);
        if (CollectionUtils.isEmpty(list)) {
            log(vgdm0801.getEquipmentName() + "无最近的月度检修计划");
            return;
        }
        String oldOverhaulPlanId = list.get(0).getOverhaulPlanId();
        log("参考检修计划号：" + oldOverhaulPlanId);
        // tvgdm05表
        List<Map> insList5 = this.copyInsert(vgdm0801, VGDM0805.QUERY, oldOverhaulPlanId);
        // tvgdm06表
        List<Map> insList6 = this.copyInsert(vgdm0801, VGDM0806.QUERY, oldOverhaulPlanId);
        // tvgdm07表
        List<Map> insList7 = this.copyInsert(vgdm0801, VGDM0807.QUERY, oldOverhaulPlanId);
        // tvgdm08表
        List<Map> insList8 = this.copyInsert(vgdm0801, VGDM0808.QUERY, oldOverhaulPlanId);
        // 批量新增
        DaoUtils.insertBatch(dao, VGDM0805.INSERT, insList5);
        DaoUtils.insertBatch(dao, VGDM0806.INSERT, insList6);
        DaoUtils.insertBatch(dao, VGDM0807.INSERT, insList7);
        DaoUtils.insertBatch(dao, VGDM0808.INSERT, insList8);
    }

    /**
     * 校验明细项是否已存在
     *
     * @param dbData dbData
     */
    private void checkDetailCount(VGDM0801 dbData) {
        Map<String, String> checkMap = new HashMap<>();
        checkMap.put("overhaulPlanId", dbData.getOverhaulPlanId());
        checkMap.put("segNo", dbData.getSegNo());
        List list1 = dao.query(VGDM0805.COUNT, checkMap);
        int c1 = Integer.parseInt(list1.get(0).toString());
        if (c1 > 0) {
            throw new PlatException(dbData.getOverhaulPlanId() + "已维护检维修项目，无法生成");
        }
        List list2 = dao.query(VGDM0806.COUNT, checkMap);
        int c2 = Integer.parseInt(list1.get(0).toString());
        if (c2 > 0) {
            throw new PlatException(dbData.getOverhaulPlanId() + "已维护检修工具或资材，无法生成");
        }
        List list3 = dao.query(VGDM0807.COUNT, checkMap);
        int c3 = Integer.parseInt(list1.get(0).toString());
        if (c3 > 0) {
            throw new PlatException(dbData.getOverhaulPlanId() + "已维护检查项，无法生成");
        }
        List list4 = dao.query(VGDM0808.COUNT, checkMap);
        int c4 = Integer.parseInt(list1.get(0).toString());
        if (c4 > 0) {
            throw new PlatException(dbData.getOverhaulPlanId() + "已维护给油脂记录，无法生成");
        }
    }

    /**
     * 复制上月数据并性质
     *
     * @param dbData            dbData
     * @param querySql          查询语句
     * @param oldOverhaulPlanId 参考的检修计划号
     * @return 待新增的数据
     */
    private List<Map> copyInsert(VGDM0801 dbData, String querySql, String oldOverhaulPlanId) {
        List<Map> insertList = new ArrayList<>();
        log("开始复制数据参数：" + dbData.getOverhaulPlanId() + "|" + querySql);
        Map<String, String> map = new HashMap<>();
        map.put("segNo", dbData.getSegNo());
        map.put("overhaulPlanId", oldOverhaulPlanId);
        List<DaoEPBase> list = dao.query(querySql, map);
        if (CollectionUtils.isEmpty(list)) {
            log("未查询到历史数据");
            return insertList;
        }
        log("查询到历史数据：" + list.size());
        for (DaoEPBase item : list) {
            Map temMap = item.toMap();
            temMap.put("overhaulPlanId", dbData.getOverhaulPlanId());
            RecordUtils.setCreator(temMap);
            insertList.add(temMap);
        }
        return insertList;
    }
}
