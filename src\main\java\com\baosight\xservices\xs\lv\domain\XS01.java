package com.baosight.xservices.xs.lv.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;

public class XS01 extends DaoEPBase {
    public XS01() {
        this.initMetaData();
    }

    public void initMetaData() {
        EiColumn eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元号");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        eiColumn.setAlign("right");
        eiColumn.setVisible(false);
        eiColumn.setPrimaryKey(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元名称");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        eiColumn.setAlign("right");
        eiColumn.setVisible(false);
        eiColumn.setPrimaryKey(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("userId");
        eiColumn.setDescName("用户ID");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        eiColumn.setAlign("right");
        eiColumn.setVisible(false);
        eiColumn.setPrimaryKey(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("loginName");
        eiColumn.setDescName("登录账号");
        eiColumn.setMaxLength(64);
        eiColumn.setNullable(false);
        eiColumn.setAlign("right");
        eiColumn.setVisible(false);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("password");
        eiColumn.setDescName("登录密码");
        eiColumn.setMaxLength(255);
        eiColumn.setWidth(150);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("status");
        eiColumn.setDescName("账号状态");
        eiColumn.setMaxLength(16);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("userName");
        eiColumn.setDescName("用户姓名");
        eiColumn.setMaxLength(128);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("userGroupEname");
        eiColumn.setDescName("管辖组");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("gender");
        eiColumn.setDescName("性别");
        eiColumn.setMaxLength(2);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("mobile");
        eiColumn.setDescName("手机");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("email");
        eiColumn.setDescName("邮箱");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("userType");
        eiColumn.setDescName("用户类别");
        eiColumn.setMaxLength(16);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("accountExpireDate");
        eiColumn.setDescName("账号过期时间");
        eiColumn.setMaxLength(14);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("pwdExpireDate");
        eiColumn.setDescName("密码过期时间");
        eiColumn.setMaxLength(14);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("isLocked");
        eiColumn.setDescName("是否锁定");
        eiColumn.setMaxLength(1);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("创建人");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("创建时间");
        eiColumn.setMaxLength(14);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("修改人");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("修改时间");
        eiColumn.setMaxLength(14);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("pwdReviseDate");
        eiColumn.setDescName("密码修改时间");
        eiColumn.setMaxLength(128);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("pwdRevisor");
        eiColumn.setDescName("密码修改人");
        eiColumn.setMaxLength(32);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiColumn.setMaxLength(1);
        eiColumn.setNullable(true);
        this.eiMetadata.addMeta(eiColumn);
    }
}