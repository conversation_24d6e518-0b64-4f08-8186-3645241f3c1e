$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        $("#inqu_status-0-eArchivesNo").val("");
        // 设备清单
        result2Grid.dataSource.page(1);
        var dataItems = resultGrid.getDataItems();
        if (dataItems.length > 0) {
            resultGrid.removeRows(dataItems);
        }
    });
    IPLATUI.EFGrid = {
        "result2": {
            onCheckRow: function (e) {
                if (e.checked === true) {
                    $("#inqu_status-0-eArchivesNo").val(e.model.eArchivesNo);
                    $("#inqu_status-0-equipmentName").val(e.model.equipmentName);
                    resultGrid.dataSource.page(1);
                } else {
                    $("#inqu_status-0-eArchivesNo").val("");
                    $("#inqu_status-0-equipmentName").val("");
                }
            }
        },
        "result": {
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                if (!IMOMUtil.checkOneSelect(result2Grid, "请选择一条设备信息再操作")) {
                    e.preventDefault();
                    return;
                }
                var row = result2Grid.getCheckedRows()[0];
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["ruleStatus"] = "10";
                    item["multiFlag"] = "0";
                    item["eArchivesNo"] = row.eArchivesNo;
                    item["equipmentName"] = row.equipmentName;
                    item["unitCode"] = row.unitCode;
                    item["segNo"] = row.segNo;
                });
            },
            beforeEdit: function (e) {
                if (e.model.ruleStatus !== "10") {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                //修改
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM11", "update", true, null, null, false);
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM11", "submit", true, null, null, false);
                });
                // 取消提交
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM11", "cancel", true, null, null, false);
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();
    // 初始化ECharts图表实例
    var firstChart = echarts.init(document.getElementById("firstChart"));

    /**
     * 将时间字符串转换为秒数
     * @param {string} timeStr - 格式为"yyyy-MM-dd HH:mm:ss.SSS"的时间字符串
     * @returns {number} 转换后的秒数
     */
    function convertToSeconds(timeStr) {
        var date = DateUtils.parseDate(timeStr, "yyyy-MM-dd HH:mm:ss.SSS");
        return Math.floor(date.getTime() / 1000);
    }

    /**
     * 自定义渲染甘特图中的矩形项
     * @param {Object} params - 渲染参数
     * @param {Object} api - ECharts API接口
     * @returns {Object} 渲染的图形配置对象
     */
    function renderItem(params, api) {
        // 获取类别索引
        var categoryIndex = api.value(0);
        // 计算开始和结束坐标点
        var start = api.coord([api.value(1), categoryIndex]);
        var end = api.coord([api.value(2), categoryIndex]);
        // 计算矩形高度
        var height = api.size([0, 1])[1] * 0.6;
        // 裁剪矩形,确保在坐标系内显示
        var rectShape = echarts.graphic.clipRectByRect(
            {
                x: start[0],
                y: start[1] - height / 2,
                width: end[0] - start[0],
                height: height
            },
            {
                x: params.coordSys.x,
                y: params.coordSys.y,
                width: params.coordSys.width,
                height: params.coordSys.height
            }
        );
        // 返回矩形的渲染配置
        return (
            rectShape && {
                type: "rect",
                transition: ["shape"],
                shape: rectShape,
                style: api.style()
            }
        );
    }

    /**
     * 查询计算结果并展示甘特图
     * 通过调用后台服务获取数据,并使用ECharts绘制显示工序执行情况
     */
    function queryCalResult() {
        const eArchivesNo = $("#inqu_status-0-eArchivesNo").val();
        // 清空查询条件防止查询结果为空
        $("#inqu_status-0-eArchivesNo").val("");
        // 查询数据
        IMOMUtil.submitNode($("#inqu"), "VGDM11", "queryResult", function (ei) {
            // 还原设备代码查询条件
            $("#inqu_status-0-eArchivesNo").val(eArchivesNo);
            // 查询结果
            const rows = ei.getBlock("result3").getMappedRows();
            if (!rows?.length) {
                firstChart?.clear();
                return;
            }
            // 优化数据处理
            const { data, categories, baseTime } = processChartData(rows);
            // 构建图表配置
            if (firstChart) {
                const chartOption = buildChartOption(data, categories, baseTime);
                firstChart.setOption(chartOption);
            }
        });
    }

    // 数据处理函数
    function processChartData(rows) {
        const data = new Map();
        const categories = new Set();
        const categoryMap = new Map();
        // 找到最早的开始时间作为基准时间
        const baseTime = Math.min(...rows.map(row => convertToSeconds(row.startTime)));
        // 遍历数据进行处理
        rows.forEach(row => {
            const { equipmentName, procedureName, startTime, stopTime, duration } = row;
            // 根据设备名称生成类别信息(y轴数据)
            if (!categoryMap.has(equipmentName)) {
                categories.add(equipmentName);
                categoryMap.set(equipmentName, categoryMap.size);
            }
            // 初始化设备下数据（x轴数据）
            if (!data.has(procedureName)) {
                data.set(procedureName, []);
            }
            // 每一个设备对应的数据
            data.get(procedureName).push({
                name: procedureName,
                value: [
                    categoryMap.get(equipmentName),
                    convertToSeconds(startTime),
                    convertToSeconds(stopTime),
                    duration,
                    startTime,
                    stopTime
                ]
            });
        });
        // 转换数据结构为series数组
        const series = Array.from(data.entries()).map(([name, points]) => ({
            name,
            type: "custom",
            renderItem,
            label: {
                show: true,
                formatter: params => `${params.data.value[3]}s`
            },
            encode: {
                x: [1, 2],
                y: 0
            },
            data: points
        }));
        // 返回处理结果
        return {
            data: series,
            categories: Array.from(categories),
            baseTime
        };
    }

    // 构建图表配置
    function buildChartOption(series, categories, baseTime) {
        const dateArr = [];
        return {
            // 图例
            legend: {},
            // 提示框
            tooltip: {
                formatter: params => `开始:${params.value[4]}<br />结束:${params.value[5]}`
            },
            grid: {
                // 防止坐标轴标签超出图表区域
                containLabel: true
            },
            xAxis: {
                min: baseTime,
                axisLabel: {
                    formatter: val => {
                        const date = new Date(val * 1000).toLocaleString();
                        const [dateStr, timeStr] = date.split(" ");
                        // 如果日期已存在，则返回时间，否则返回日期
                        return dateArr.includes(dateStr) ? timeStr : (dateArr.push(dateStr), date);
                    }
                }
            },
            yAxis: {
                data: categories
            },
            series
        };
    }
    // 鼠标移入高亮
    firstChart.on("mouseover", (param) => {
        firstChart.dispatchAction({
            type: "highlight",
            seriesName: param.seriesName
        });
    });
    // 鼠标移出恢复
    firstChart.on("mouseout", (param) => {
        firstChart.dispatchAction({
            type: "downplay"
        });
    });
});
