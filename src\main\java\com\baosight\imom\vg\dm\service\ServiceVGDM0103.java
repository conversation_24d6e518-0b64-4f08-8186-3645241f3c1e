package com.baosight.imom.vg.dm.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.VGDM0103;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 设备档案附件下载后台
 * @Date : 2024/8/26
 * @Version : 1.0
 */
public class ServiceVGDM0103 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0103.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 根据id查询数据
     */
    public EiInfo queryById(EiInfo inInfo) {
        try {
            String id = inInfo.getString("id");
            if (StrUtil.isBlank(id)) {
                throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
            }
            Map<String, String> map = new HashMap<>();
            map.put("uuid", id);
            List list = dao.query(VGDM0103.QUERY, map);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
            }
            VGDM0103 vgdm0103 = (VGDM0103) list.get(0);
            inInfo.set("docName", vgdm0103.getUploadFileName());
            inInfo.set("filePath", vgdm0103.getUploadFilePath());
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{e.getMessage()});
        }
        return inInfo;
    }

}
