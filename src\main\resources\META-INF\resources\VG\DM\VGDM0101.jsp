<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-windowId" cname="弹窗ID" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-processCategory" cname="工序大类代码" disabled="true"
                        defaultValue="" type="hidden" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-processCategorySub" cname="工序小类代码" disabled="true"
                        defaultValue="" type="hidden" colWidth="3"/>
          </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no"  readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称"/>
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>