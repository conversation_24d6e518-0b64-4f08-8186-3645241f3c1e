<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFPopupInput ename="inqu_status-0-warehouseCode" cname="仓库代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="warehouseInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-warehouseName"
                             popupTitle="仓库查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-warehouseName" cname="仓库名称" colWidth="3" disabled="true"/>
        </div>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-locationId" cname="库位代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="locationInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-locationName"
                             popupTitle="库位查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-locationName" cname="库位名称" colWidth="3" disabled="true"/>
            <%--<EF:EFInput ename="inqu_status-0-packId" cname="捆包号" colWidth="3" placeholder="模糊条件"/>--%>
            <EF:EFInput ename="inqu_status-0-packId" cname="捆包号" type="textarea" placeholder="支持批量查询,可将内容使用ENTER键换行分隔" colWidth="3" ratio="4:8"/>
            <EF:EFInput ename="inqu_status-0-labelId" cname="标签号" type="textarea" placeholder="支持批量查询,可将内容使用ENTER键换行分隔" colWidth="3" ratio="4:8"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-areaType" cname="区域类型" width="150" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P031"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-unitedPackId" cname="并包号" colWidth="3" placeholder="模糊条件"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true" type="hidden"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="在库" value="10"/>
                <EF:EFOption label="出库" value="30"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" sort="all">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" width="150"
                             required="true"
                             primaryKey="true" enable="false"/>
                <EF:EFColumn ename="warehouseName" cname="仓库名称" align="left" width="200" enable="false"
                             required="true"/>
                <EF:EFComboColumn ename="areaType" cname="区域类型" align="center" width="150" enable="false"
                                  required="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P031"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="areaCode" cname="区域代码/库位代码" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="areaName" cname="区域名称/库位名称" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="packId" cname="捆包号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="unitedPackId" cname="并包号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="labelId" cname="标签号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="netWeight" cname="净重" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="grossWeight" cname="毛重" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="craneOperationWeight" cname="吊装重量" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="quantity" cname="数量" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false" hidden="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFOption label="在库" value="10"/>
                    <EF:EFOption label="出库" value="30"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="posDirCode" cname="层数标记" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="craneResultId" cname="行车实绩单号" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="actionFlag" cname="板卷标记" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P039"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="innerOutterPlateFlag" cname="内外版标记" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFOption label="1" value="1"/>
                    <EF:EFOption label="2" value="2"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="x_position" cname="X轴坐标" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="y_position" cname="Y轴坐标" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="z_position" cname="Z轴坐标" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--仓库代码弹窗    --%>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseInfo" width="90%" height="60%"/>
    <%--库位代码弹窗    --%>
    <EF:EFWindow url="${ctx}/web/LIDS06" id="locationInfo" width="90%" height="60%"/>

</EF:EFPage>
