<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFDatePicker ename="inqu_status-0-reportDate" colWidth="3" cname="查询日期" format="yyyyMMdd"/>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true"
                       serviceName="LIDS001R" queryMethod="getDailyReport">
                <%--<&lt;%&ndash;EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>&ndash;%&gt;--%>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="false"/>
                <EF:EFColumn ename="locationId" cname="作业区" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="locationName" cname="属性" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="warehouseCode" cname="工艺" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="machineCode" cname="机组" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="plannedProductionWeight" cname="排产量" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="inventoryWeight" cname="投料量" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="totleMaterailWeight" cname="总产量" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="uncoilingInspectionWeight" cname="开卷检查量" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="returnWeight" cname="退料量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="planCompletionRate" cname="计划完成率" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="dutyQuantity" cname="人员配置" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="actualStartUpTime" cname="作业时间" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="averageOutputPerPerson" cname="人均产量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="hourOutout" cname="小时产量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="pendingProcessingWeight" cname="待加工量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="hourDemand24" cname="24小时需求" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="hourDemand48" cname="48小时需求" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="出库量" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="铝板加工量" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="loadingPointNo" cname="预测加工量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingPointName" cname="实际加工量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="完成度" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelName" cname="实际加工量(3倍铝板)" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="预测出库量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="实际出库量" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="完成度" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="设计产能/年" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="月达产" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="center" width="100" enable="false" hidden="true"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="center" width="120" hidden="true"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false" hidden="true"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="center" width="100" enable="false" hidden="true"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="center" width="120" hidden="true"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false" hidden="true"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="false" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>
