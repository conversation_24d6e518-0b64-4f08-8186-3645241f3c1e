package com.baosight.imom.common.utils;

import com.baosight.imom.li.ds.domain.*;
import com.baosight.imom.li.ds.service.ServiceLIDS0606;
import com.baosight.imom.li.rl.dao.LIRL0503;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class UWBUtils extends ServiceBase {
    public static final String UWB_AUTHORIZATION = "imom:ds:uwb_authorization";

    private static RedisUtil redisUtil = PlatApplicationContext.getBean("redisUtil",RedisUtil.class);


    /**
     * @param hashMap: segNo,XPosition,YPosition,ZPosition,packId
     * @return java.lang.String
     * @throws
     * @Service
     * @description 根据传入XY轴转换为库位
     */
    public static Map transitionLocation(HashMap hashMap, Dao dao) {
        Map map = new HashMap();
        String warehouseCode = "";
        String warehouseName = "";
        String locationId = "";
        String locationName = "";
        String posDirCode = "";
        map.put("warehouseCode", warehouseCode);
        map.put("warehouseName", warehouseName);
        map.put("locationId", locationId);
        map.put("locationName", locationName);
        map.put("posDirCode", posDirCode);
        String segNo = MapUtils.getString(hashMap, "segNo", "");
        String XPosition = MapUtils.getString(hashMap, "XPosition", "");
        String YPosition = MapUtils.getString(hashMap, "YPosition", "");
        String ZPosition = MapUtils.getString(hashMap, "ZPosition", "");
        String packId = MapUtils.getString(hashMap, "packId", "");
        String factoryArea = MapUtils.getString(hashMap, "factoryArea", "");
        String factoryBuilding = MapUtils.getString(hashMap, "factoryBuilding", "");
        String crossArea = MapUtils.getString(hashMap, "crossArea", "");

        if (StringUtils.isBlank(segNo)) {
            return map;
        }
        if (StringUtils.isBlank(XPosition) && StringUtils.isBlank(YPosition)) {
            return map;
        }
        HashMap queryMap = new HashMap();
        queryMap.put("segNo", segNo);
        queryMap.put("XPosition", XPosition);
        queryMap.put("YPosition", YPosition);
        queryMap.put("factoryArea", factoryArea);
        queryMap.put("factoryBuilding", factoryBuilding);
        queryMap.put("crossArea", crossArea);
        //先根据传入的X和Y轴查询库位
        //查询先使用精准坐标查询库位，如果查询不到，再使用偏差值查询
        List<LIDS0601> locationIdListJZ = dao.query(LIDS0601.QUERY_LOCATION_ID_JZ, queryMap);
        if (CollectionUtils.isNotEmpty(locationIdListJZ)) {
            warehouseCode = locationIdListJZ.get(0).getWarehouseCode();
            warehouseName = locationIdListJZ.get(0).getWarehouseName();
            locationId = locationIdListJZ.get(0).getLocationId();
            locationName = locationIdListJZ.get(0).getLocationName();
            map.put("warehouseCode", warehouseCode);
            map.put("warehouseName", warehouseName);
            map.put("locationId", locationId);
            map.put("locationName", locationName);
            try {
                queryMap.put("locationId", locationId);
                List<LIDS0901> posDirCodeList = dao.query(LIDS0901.QUERY_X_POSITION, queryMap);
                //若未查到当前库位下左右两边有近X轴的中心点，则为下层
                if (posDirCodeList != null && posDirCodeList.size() == 2) {
                    //查询时为X轴倒序排列，第一行为最大
                    String x_positionMax = posDirCodeList.get(0).getX_position();
                    String x_positionMin = posDirCodeList.get(1).getX_position();
                    Double distance = Double.valueOf(x_positionMax) - Double.valueOf(x_positionMin);
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", posDirCodeList.get(0).getPackId());
                    //间隔的单位为CM 外径的单位为mm 注意xyz轴和距离单位的转换
                    List<LIRL0503> innerDiameterList = dao.query(LIRL0503.QUERY, queryMap);
                    BigDecimal rightOuterDiameter = new BigDecimal(0);
                    if (CollectionUtils.isNotEmpty(innerDiameterList)) {
                        //获取右侧捆包外径
                        BigDecimal innerDiameter = innerDiameterList.get(0).getInnerDiameter();
                        BigDecimal prodDensity = innerDiameterList.get(0).getProdDensity();
                        BigDecimal netWeight = innerDiameterList.get(0).getNetWeight();
                        String specsDesc = innerDiameterList.get(0).getSpecsDesc();
                        double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                        double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), netWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                        rightOuterDiameter = BigDecimal.valueOf(externalDiameterNumber);
                    }
                    queryMap.put("packId", posDirCodeList.get(1).getPackId());
                    List<LIRL0503> innerDiameterList1 = dao.query(LIRL0503.QUERY, queryMap);
                    BigDecimal leftOuterDiameter = new BigDecimal(0);
                    if (CollectionUtils.isNotEmpty(innerDiameterList1)) {
                        //获取左侧捆包外径
                        BigDecimal innerDiameter = innerDiameterList1.get(0).getInnerDiameter();
                        BigDecimal prodDensity = innerDiameterList1.get(0).getProdDensity();
                        BigDecimal netWeight = innerDiameterList1.get(0).getNetWeight();
                        String specsDesc = innerDiameterList1.get(0).getSpecsDesc();
                        double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                        double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), netWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                        leftOuterDiameter = BigDecimal.valueOf(externalDiameterNumber);
                    }
                    BigDecimal interval = new BigDecimal(distance).subtract(rightOuterDiameter.divide(new BigDecimal(2)).subtract(leftOuterDiameter.divide(new BigDecimal(2))));
                    queryMap.put("key", "INTERVAL_DISTANCE");
                    queryMap.put("warehouseCode", warehouseCode);
                    List<Map> defaultIntervalList = dao.query("LIDS0606.querySwitchValue", queryMap);
                    Double defaultInterval = Double.valueOf(defaultIntervalList.get(0).get("configureValue").toString());
                    //默认间隔大于两卷之间间隔，证明无法在两卷之间的下层放入卷，当前卷在上层
                    if (new BigDecimal(defaultInterval).compareTo(interval.divide(new BigDecimal(10))) > 0) {
                        queryMap.put("packId", packId);
                        List<LIRL0503> innerDiameterList2 = dao.query(LIRL0503.QUERY, queryMap);
                        BigDecimal outerDiameter = new BigDecimal(0);
                        if (CollectionUtils.isNotEmpty(innerDiameterList2)) {
                            //获取当前捆包外径
                            BigDecimal innerDiameter = innerDiameterList1.get(0).getInnerDiameter();
                            BigDecimal prodDensity = innerDiameterList1.get(0).getProdDensity();
                            BigDecimal netWeight = innerDiameterList1.get(0).getNetWeight();
                            String specsDesc = innerDiameterList2.get(0).getSpecsDesc();
                            double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                            double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), netWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                            outerDiameter = BigDecimal.valueOf(externalDiameterNumber);
                        }
                        //加增判定，如果Z轴的坐标大于卷的半径，则表示当前卷在上层
//                        BigDecimal outerDiameter2 = outerDiameter.divide(new BigDecimal(10).divide(new BigDecimal(2)));
//                        if (new BigDecimal(ZPosition).compareTo(outerDiameter2) > 0) {
//                            posDirCode = "2";
//                            map.put("posDirCode", posDirCode);
//                        } else {
//                            posDirCode = "1";
//                            map.put("posDirCode", posDirCode);
//                        }
                    } else {
                        posDirCode = "1";
                        map.put("posDirCode", posDirCode);
                    }
                } else {
                    posDirCode = "1";
                    map.put("posDirCode", posDirCode);
                }
            } catch (Exception ex) {
                posDirCode = "1";
                map.put("posDirCode", posDirCode);
            }
        } else {
            List<LIDS0601> locationIdList = dao.query(LIDS0601.QUERY_LOCATION_ID, queryMap);
            if (CollectionUtils.isNotEmpty(locationIdList)) {
                warehouseCode = locationIdList.get(0).getWarehouseCode();
                warehouseName = locationIdList.get(0).getWarehouseName();
                locationId = locationIdList.get(0).getLocationId();
                locationName = locationIdList.get(0).getLocationName();
                map.put("warehouseCode", warehouseCode);
                map.put("warehouseName", warehouseName);
                map.put("locationId", locationId);
                map.put("locationName", locationName);
                /**
                 *  1：根据放下X轴的中心点查询库存表当前库位中最近的两个X轴的中心点
                 *  2：有内径时，使用两个X轴中心点的距离减去第一个卷的半径的距离和第二个卷的半径的距离就是两个卷之间的间隔，
                 *  使用此间隔与库位分配的配置间隔相比，如果小于配置间隔则表示两个卷之间的下层库位不能放入卷料，则传入捆包为上层
                 *  xs_configuration_information 查询卷间隔表，查询条件为仓库代码、系统账套、CONFIGURE_KEY=INTERVAL_DISTANCE
                 *  3：添加Z轴的判定 如果Z轴的值大于当前卷的半径，则认为是上层
                 */
                try {
                    queryMap.put("locationId", locationId);
                    List<LIDS0901> posDirCodeList = dao.query(LIDS0901.QUERY_X_POSITION, queryMap);
                    //若未查到当前库位下左右两边有近X轴的中心点，则为下层
                    if (posDirCodeList != null && posDirCodeList.size() == 2) {
                        //查询时为X轴倒序排列，第一行为最大
                        String x_positionMax = posDirCodeList.get(0).getX_position();
                        String x_positionMin = posDirCodeList.get(1).getX_position();
                        Double distance = Double.valueOf(x_positionMax) - Double.valueOf(x_positionMin);
                        queryMap.clear();
                        queryMap.put("segNo", segNo);
                        queryMap.put("packId", posDirCodeList.get(0).getPackId());
                        //间隔的单位为CM 外径的单位为mm 注意xyz轴和距离单位的转换
                        List<LIRL0503> innerDiameterList = dao.query(LIRL0503.QUERY, queryMap);
                        BigDecimal rightOuterDiameter = new BigDecimal(0);
                        if (CollectionUtils.isNotEmpty(innerDiameterList)) {
                            //获取右侧捆包外径
                            BigDecimal innerDiameter = innerDiameterList.get(0).getInnerDiameter();
                            BigDecimal prodDensity = innerDiameterList.get(0).getProdDensity();
                            BigDecimal netWeight = innerDiameterList.get(0).getNetWeight();
                            String specsDesc = innerDiameterList.get(0).getSpecsDesc();
                            double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                            double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), netWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                            rightOuterDiameter = BigDecimal.valueOf(externalDiameterNumber);
                        }
                        queryMap.put("packId", posDirCodeList.get(1).getPackId());
                        List<LIRL0503> innerDiameterList1 = dao.query(LIRL0503.QUERY, queryMap);
                        BigDecimal leftOuterDiameter = new BigDecimal(0);
                        if (CollectionUtils.isNotEmpty(innerDiameterList1)) {
                            //获取左侧捆包外径
                            BigDecimal innerDiameter = innerDiameterList1.get(0).getInnerDiameter();
                            BigDecimal prodDensity = innerDiameterList1.get(0).getProdDensity();
                            BigDecimal netWeight = innerDiameterList1.get(0).getNetWeight();
                            String specsDesc = innerDiameterList1.get(0).getSpecsDesc();
                            double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                            double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), netWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                            leftOuterDiameter = BigDecimal.valueOf(externalDiameterNumber);
                        }
                        BigDecimal interval = new BigDecimal(distance).subtract(rightOuterDiameter.divide(new BigDecimal(2)).subtract(leftOuterDiameter.divide(new BigDecimal(2))));
                        queryMap.put("key", "INTERVAL_DISTANCE");
                        queryMap.put("warehouseCode", warehouseCode);
                        List<Map> defaultIntervalList = dao.query("LIDS0606.querySwitchValue", queryMap);
                        Double defaultInterval = Double.valueOf(defaultIntervalList.get(0).get("configureValue").toString());
                        //默认间隔大于两卷之间间隔，证明无法在两卷之间的下层放入卷，当前卷在上层
                        if (new BigDecimal(defaultInterval).compareTo(interval.divide(new BigDecimal(10))) > 0) {
                            queryMap.put("packId", packId);
                            List<LIRL0503> innerDiameterList2 = dao.query(LIRL0503.QUERY, queryMap);
                            BigDecimal outerDiameter = new BigDecimal(0);
                            if (CollectionUtils.isNotEmpty(innerDiameterList2)) {
                                //获取当前捆包外径
                                BigDecimal innerDiameter = innerDiameterList1.get(0).getInnerDiameter();
                                BigDecimal prodDensity = innerDiameterList1.get(0).getProdDensity();
                                BigDecimal netWeight = innerDiameterList1.get(0).getNetWeight();
                                String specsDesc = innerDiameterList2.get(0).getSpecsDesc();
                                double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                                double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), netWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                                outerDiameter = BigDecimal.valueOf(externalDiameterNumber);
                            }
                            //加增判定，如果Z轴的坐标大于卷的半径，则表示当前卷在上层
//                        BigDecimal outerDiameter2 = outerDiameter.divide(new BigDecimal(10).divide(new BigDecimal(2)));
//                        if (new BigDecimal(ZPosition).compareTo(outerDiameter2) > 0) {
//                            posDirCode = "2";
//                            map.put("posDirCode", posDirCode);
//                        } else {
//                            posDirCode = "1";
//                            map.put("posDirCode", posDirCode);
//                        }
                        } else {
                            posDirCode = "1";
                            map.put("posDirCode", posDirCode);
                        }
                    } else {
                        posDirCode = "1";
                        map.put("posDirCode", posDirCode);
                    }
                } catch (Exception ex) {
                    posDirCode = "1";
                    map.put("posDirCode", posDirCode);
                }
            } else {
                return map;
            }
        }
        return map;
    }


    /**
     * @param paramMap: segNo,XPosition,YPosition,ZPosition,factoryArea,factoryBuilding
     * @return Map
     * @throws
     * @Service
     * @description 根据传入XY轴坐标，厂区厂房查询厂区区域
     */
    public static Map transitionArea(Map paramMap, Dao dao) {
        Map map = new HashMap();
        String factoryArea = "";
        String factoryAreaName = "";
        String factoryBuilding = "";
        String factoryBuildingName = "";
        String areaType = "";
        String aisleType = "";
        String areaCode = "";
        String areaName = "";
        String crossArea = "";
        String crossAreaName = "";

        map.put("areaType", areaType);
        map.put("aisleType", aisleType);
        map.put("areaCode", areaCode);
        map.put("areaName", areaName);
        map.put("factoryArea", factoryArea);
        map.put("factoryBuilding", factoryBuilding);
        map.put("crossArea", crossArea);
        map.put("crossAreaName", crossAreaName);
        map.put("factoryAreaName", factoryAreaName);
        map.put("factoryBuildingName", factoryBuildingName);

        String segNo = MapUtils.getString(paramMap, "segNo", "");
        String XPosition = MapUtils.getString(paramMap, "XPosition", "");
        String YPosition = MapUtils.getString(paramMap, "YPosition", "");

        factoryArea = MapUtils.getString(paramMap, "factoryArea", "");
        factoryBuilding = MapUtils.getString(paramMap, "factoryBuilding", "");
        crossArea = MapUtils.getString(paramMap, "crossArea", "");
        if (StringUtils.isBlank(segNo)) {
            return map;
        }
        if (StringUtils.isBlank(XPosition) && StringUtils.isBlank(YPosition)) {
            return map;
        }
        if(StringUtils.isBlank(factoryArea) || StringUtils.isBlank(factoryBuilding)){
            return map;
        }
        if(StringUtils.isBlank(crossArea)){
            return map;
        }

        HashMap queryMap = new HashMap();
        queryMap.put("segNo", segNo);
        queryMap.put("factoryArea", factoryArea);
        queryMap.put("factoryBuilding", factoryBuilding);
        queryMap.put("crossArea", crossArea);
        queryMap.put("XPosition", XPosition);
        queryMap.put("YPosition", YPosition);
        //根据传入的X和Y轴查询区域
        List<LIDS0101> areaList = dao.query(LIDS0101.QUERY_AREA, queryMap);
        if (CollectionUtils.isNotEmpty(areaList)) {
            map.put("areaType", StringUtils.defaultString(areaList.get(0).getAreaType(), ""));
            map.put("aisleType", StringUtils.defaultString(areaList.get(0).getAisleType(), ""));
            map.put("areaCode", StringUtils.defaultString(areaList.get(0).getAreaCode(), ""));
            map.put("areaName", StringUtils.defaultString(areaList.get(0).getAreaName(), ""));
            map.put("factoryArea", StringUtils.defaultString(areaList.get(0).getFactoryArea(), ""));
            map.put("factoryBuilding", StringUtils.defaultString(areaList.get(0).getFactoryBuilding(), ""));
            map.put("factoryAreaName", StringUtils.defaultString(areaList.get(0).getFactoryAreaName(), ""));
            map.put("factoryBuildingName", StringUtils.defaultString(areaList.get(0).getFactoryBuildingName(), ""));
            map.put("crossArea", StringUtils.defaultString(areaList.get(0).getCrossArea(), ""));
            map.put("crossAreaName", StringUtils.defaultString(areaList.get(0).getCrossAreaName(), ""));
        }

        return map;
    }

    /**
     * 根据传入的起始区域类型,终到区域类型匹配相应的清单来源
     *
     * @param listSource     清单来源
     * @param startAreaType  起始区域类型 必填
     * @param endAreaType    终到区域类型 必填
     * @param startAisleType 起始通道装卸货类型
     * @param endAisleType   终到通道装卸货类型
     * @return listSource
     */
    public static String getListSource(String listSource, String startAreaType, String endAreaType, String startAisleType, String endAisleType) {
        //设置默认值
        startAreaType = StringUtils.isBlank(startAreaType) ? "" : startAreaType;
        endAreaType = StringUtils.isBlank(endAreaType) ? "" : endAreaType;
        startAisleType = StringUtils.isBlank(startAisleType) ? "" : startAisleType;
        endAisleType = StringUtils.isBlank(endAisleType) ? "" : endAisleType;

        StringBuffer startToEndAreaType = new StringBuffer(startAreaType);
        if (StringUtils.isNotBlank(startAisleType)) {
            startToEndAreaType.append("&").append(startAisleType);
        }
        startToEndAreaType.append("->" + endAreaType);
        if (StringUtils.isNotBlank(endAisleType)) {
            startToEndAreaType.append("&").append(endAisleType);
        }

        switch (startToEndAreaType.toString()) {
            //卸货通道(通道，类型为卸货) -> 库区
            case "40&20->10":
            case "40&10,20->10":
                //卸货入库
                listSource = "10";
                break;
            //库区->发货通道(通道，类型为发货)
            case "10->40&10":
            case "10->40&10,20":
                //成品发货
                listSource = "20";
                break;
            //库区->过跨通道
            case "10->20":
            //库区->拆包区
            case "10->30":
            //过跨小车->拆包区
            case "20->30":
            //拆包区->过跨小车
            case "30->20":
            //过跨小车->上料区
            case "20->60":
            //拆包区->上料区
            case "30->60":
                //原料上料
                listSource = "30";
                break;
            //上料区->退卷区
            case "60->63":
                //加工退卷
                listSource = "40";
                break;
            //库区->库区
            case "10->10":
            //退料区->推荐库位
            case "63->10":
            //机组下料区->库位
            case "61->10":
                //倒库
                listSource = "50";
                break;
            //机组模具台车->模具区
            case "65->50":
            //模具区->机组模具台车
            case "50->65":
                //模具更换
                listSource = "70";
                break;
            //模具维修区<->模具区(没有行车作业清单来源，这里是为了方便判断)
            case "51->50":
            case "50->51":
                listSource = "80";
                break;
            //下料区->下料区
            case "61->61":
                //下料
                listSource = "90";
                break;
            //下料区->未匹配记录
            case "61->":
                //下料
                listSource = "X1";
                break;
            default:
                return listSource;
        }
        return listSource;
    }


    /**
     * 调用UWB用户登入接口获取token
     *
     * @return
     */
    public static String getUwbToken() {
        String access_token = "";
        //从redis中获取token
        String token = (String) redisUtil.get(UWB_AUTHORIZATION);
        if (StringUtils.isNotBlank(token)) {
            return token;
        } else {
            JSONObject areaInfo = new JSONObject();
            areaInfo.put("username", "admin");//用户名
            areaInfo.put("password", MD5Utils.encrypt("*cqbg@2015"));//密码,调用MD5加密

            String ip = "************"; // IP地址
            int port = 80; // 端口号
            String url = "http://" + ip + ":" + port + "/EHCommon/admin/user/login";
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Content-Type", "application/json");

                // 设置请求体
                StringEntity entity = new StringEntity(areaInfo.toString(), "UTF-8");
                httpPost.setEntity(entity);

                // 执行请求
                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    HttpEntity responseEntity = response.getEntity();
                    if (responseEntity != null) {
                        String responseString = EntityUtils.toString(responseEntity);
                        if (response.getStatusLine().getStatusCode() != 200) {
                            throw new RuntimeException("Failed to get access token. Response: " + responseString);
                        }
                        // 解析JSON响应
                        JSONObject jsonResponse = JSONObject.fromObject(responseString);

                        // 业务状态判断
                        if (jsonResponse.getInt("type") != 1) {
                            throw new RuntimeException("接口调用失败: " + jsonResponse.getString("message"));
                        }

                        // 提取Authorization令牌
                        JSONObject resultObj = jsonResponse.getJSONObject("result");
                        String authorization = resultObj.getString("Authorization"); // Bearer令牌
                        access_token = authorization;

                        // 缓存到redis
                        if (StringUtils.isNotBlank(access_token)) {
                            redisUtil.set(UWB_AUTHORIZATION, access_token,604800);
                        }
                    }
                }
            } catch (IOException e) {
                throw new RuntimeException("获取token失败：" + e);
            }
            return access_token;
        }
    }
}
