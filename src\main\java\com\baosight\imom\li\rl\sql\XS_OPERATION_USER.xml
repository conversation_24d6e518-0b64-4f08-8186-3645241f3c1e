<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-10-30 9:03:34
   		Version :  1.0
		tableName :${meliSchema}.xs_operation_user
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 SEG_CNAME  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 FACTORY_AREA_NAME  VARCHAR   NOT NULL, 
		 WAREHOUSE_CODE  VARCHAR   NOT NULL, 
		 WAREHOUSE_NAME  VARCHAR   NOT NULL, 
		 HAND_POINT_ID  VARCHAR   NOT NULL, 
		 HAND_POINT_NAME  VARCHAR   NOT NULL, 
		 EMPLOYEE_JOB_NUMBER  VARCHAR   NOT NULL, 
		 <PERSON><PERSON><PERSON>OYEE_NAME  VARCHAR   NOT NULL, 
		 O<PERSON><PERSON>ATION_TYPE  VARCHAR   NOT NULL, 
		 SOURCE_SYSTEM  VARCHAR   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL
	-->
<sqlMap namespace="XS_OPERATION_USER">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.XS_OPERATION_USER">
		SELECT
				UUID	as "uuid",  <!-- ID -->
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
				SEG_CNAME	as "segCname",  <!-- 业务单元名称 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区编码 -->
				FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
				HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
				HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
				EMPLOYEE_JOB_NUMBER	as "employeeJobNumber",  <!-- 员工号 -->
				EMPLOYEE_NAME	as "employeeName",  <!-- 员工姓名 -->
				OPERATION_TYPE	as "operationType",  <!-- 操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录
20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库  -->
				SOURCE_SYSTEM	as "sourceSystem",  <!-- 数据来源(10系统 20车载APP 30员工PDA) -->
				REMARK	as "remark",  <!-- 备注 -->
				REC_CREATOR	as "recCreator",  <!-- 创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户ID -->
				UNIT_CODE	as "unitCode" <!-- 业务单元名称 -->
		FROM ${meliSchema}.xs_operation_user WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.xs_operation_user WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segCname">
			SEG_CNAME = #segCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			HAND_POINT_NAME = #handPointName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="employeeJobNumber">
			EMPLOYEE_JOB_NUMBER = #employeeJobNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="employeeName">
			EMPLOYEE_NAME = #employeeName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="operationType">
			OPERATION_TYPE = #operationType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sourceSystem">
			SOURCE_SYSTEM = #sourceSystem#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.xs_operation_user (UUID,  <!-- ID -->
										SEG_NO,  <!-- 业务单元代码 -->
										SEG_CNAME,  <!-- 业务单元名称 -->
										FACTORY_AREA,  <!-- 厂区编码 -->
										FACTORY_AREA_NAME,  <!-- 厂区名称 -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										WAREHOUSE_NAME,  <!-- 仓库名称 -->
										HAND_POINT_ID,  <!-- 装卸点代码 -->
										HAND_POINT_NAME,  <!-- 装卸点名称 -->
										EMPLOYEE_JOB_NUMBER,  <!-- 员工号 -->
										EMPLOYEE_NAME,  <!-- 员工姓名 -->
										OPERATION_TYPE,  <!-- 操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录
20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库  -->
										SOURCE_SYSTEM,  <!-- 数据来源(10系统 20车载APP 30员工PDA) -->
										REMARK,  <!-- 备注 -->
										REC_CREATOR,  <!-- 创建人 -->
										REC_CREATOR_NAME,  <!-- 创建人姓名 -->
										REC_CREATE_TIME,  <!-- 创建时间 -->
										REC_REVISOR,  <!-- 修改人 -->
										REC_REVISOR_NAME,  <!-- 修改人姓名 -->
										REC_REVISE_TIME,  <!-- 修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										TENANT_USER,  <!-- 租户ID -->
										UNIT_CODE  <!-- 业务单元名称 -->
										)		 
	    VALUES (#uuid#, #segNo#, #segCname#, #factoryArea#, #factoryAreaName#, #warehouseCode#, #warehouseName#, #handPointId#, #handPointName#, #employeeJobNumber#, #employeeName#, #operationType#, #sourceSystem#, #remark#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #tenantUser#, #unitCode#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.xs_operation_user WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.xs_operation_user
		SET 
					SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					SEG_CNAME	= #segCname#,   <!-- 业务单元名称 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区编码 -->  
					FACTORY_AREA_NAME	= #factoryAreaName#,   <!-- 厂区名称 -->  
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库代码 -->  
					WAREHOUSE_NAME	= #warehouseName#,   <!-- 仓库名称 -->  
					HAND_POINT_ID	= #handPointId#,   <!-- 装卸点代码 -->  
					HAND_POINT_NAME	= #handPointName#,   <!-- 装卸点名称 -->  
					EMPLOYEE_JOB_NUMBER	= #employeeJobNumber#,   <!-- 员工号 -->  
					EMPLOYEE_NAME	= #employeeName#,   <!-- 员工姓名 -->  
					OPERATION_TYPE	= #operationType#,   <!-- 操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录
20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库  -->  
					SOURCE_SYSTEM	= #sourceSystem#,   <!-- 数据来源(10系统 20车载APP 30员工PDA) -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					REC_CREATOR	= #recCreator#,   <!-- 创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户ID -->  
					UNIT_CODE	= #unitCode#  <!-- 业务单元名称 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>