<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfo" center="true"
                             ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false"
                             containerId="deviceInfo" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                             cname="分部设备名称" colWidth="3"/>
            <EF:EFDateSpan startName="inqu_status-0-eventStartTime"
                           endName="inqu_status-0-eventEndTime" readonly="true"
                           startCname="履历日期(起)" endCname="履历日期(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>


        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-relevanceType" cname="数据来源" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P057"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-relevanceId" cname="来源单据号" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="履历清单">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="70" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称"/>
            <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center"/>
            <EF:EFColumn ename="deviceName" cname="分部设备名称"/>
            <EF:EFComboColumn ename="relevanceType" cname="数据来源" width="70" align="center">
                <EF:EFCodeOption codeName="P057"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="relevanceId" cname="来源单据号" width="120" align="center"/>
            <EF:EFColumn ename="eventStartTime" cname="履历时间" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"/>
            <EF:EFColumn ename="eventContent" cname="履历内容" width="200"/>
            <EF:EFColumn ename="handleContent" cname="处理内容" width="200"/>
            <EF:EFColumn ename="handlePerson" cname="处理人" align="center" width="100"/>
            <EF:EFColumn ename="handlePersonName" cname="处理人姓名" align="center" width="100"/>
            <EF:EFColumn ename="handleTime" cname="处理时间" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"/>
            <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
</EF:EFPage>