package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.vg.domain.Tvgdm0701;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.ed.util.SequenceGenerator;

import java.util.Map;

/**
 * 设备故障信息表
 */
public class VGDM0701 extends Tvgdm0701 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0701.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0701.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0701.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0701.update";
    /**
     * 查询平均完成时间
     */
    public static final String QUERY_AVG_FINISH = "VGDM0701.queryAvgFinish";

    /**
     * 查询当天的故障时间
     */
    public static final String QUERY_DAY_FALUT_TIME = "VGDM0701.queryDayFaultTime";

    @Override
    public String getCheckStatus() {
        return this.getFaultStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

    /**
     * 新增数据
     *
     * @param dao dao
     */
    public void insertData(Dao dao) {
        this.setFaultStatus(MesConstant.Status.K10);
        String[] arr = {this.getSegNo()};
        this.setFaultId(SequenceGenerator.getNextSequence(SequenceConstant.FAULT_ID, arr));
        Map data = this.toMap();
        RecordUtils.setCreator(data);
        dao.insert(INSERT, data);
    }
}
