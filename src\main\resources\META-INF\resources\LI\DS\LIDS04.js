$(function () {
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {
                //弹框编号为空时，隐藏确认回填按钮
                let windowId = $("#inqu_status-0-windowId").val();
                if (IPLAT.isBlankString(windowId)) {
                    $("#CONFIRM").hide();
                }
                //确认回填
                $("#CONFIRM").on("click", function (e) {
                    let windowId = $("#inqu_status-0-windowId").val();
                    if (!IPLAT.isBlankString(windowId)) {
                        //判断是否勾选数据
                        let checkRows = resultGrid.getCheckedRows();
                        if (checkRows.length < 1) {
                            NotificationUtil({msg: "未勾选需要回填的数据，不可点击确认!"}, "error");
                            return;
                        }
                        //校验勾选厂区，厂房必须一致(取第一条的数据)
                        let factoryArea = checkRows[0].factoryArea;
                        let factoryBuilding = checkRows[0].factoryBuilding;
                        for (let i = 1; i < checkRows.length; i++) {
                            if (checkRows[i].factoryArea !== factoryArea || checkRows[i].factoryBuilding !== factoryBuilding) {
                                NotificationUtil({msg: "所勾选的跨区信息中，厂区厂房不一致!"}, "error");
                                return;
                            }
                        }
                        //关闭下拉框
                        window.parent[windowId + "Window"].close();
                    }
                });
            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            beforeEdit: function (e) {
                e.preventDefault();
                return;
            }
        }
    }

    //厂区厂房管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-factoryArea").val(rows[0].factoryArea);
                $("#inqu_status-0-factoryAreaName").val(rows[0].factoryAreaName);
                $("#inqu_status-0-factoryBuilding").val(rows[0].factoryBuilding);
                $("#inqu_status-0-factoryBuildingName").val(rows[0].factoryBuildingName);
            }
        }
    });

})