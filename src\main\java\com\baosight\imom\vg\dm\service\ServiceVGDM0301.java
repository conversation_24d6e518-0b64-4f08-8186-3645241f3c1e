package com.baosight.imom.vg.dm.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.vg.dm.domain.VGDM0301;

/**
 * <AUTHOR> yzj
 * @Description : 采集点位查询弹窗页面后台
 * @Date : 2024/8/28
 * @Version : 1.0
 */
public class ServiceVGDM0301 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0301.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0301().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, VGDM0301.QUERY, new VGDM0301());
    }
}
