/**
 * Generate time : 2024-08-19 11:16:44
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0201 预约维护表
 */
public class LIRL0201 extends DaoEPBase {
    public static final String QUERY = "LIRL0201.query";
    public static final String QUERY_TIMEOUT_DATA = "LIRL0201.queryTimeoutData";
    public static final String QUERY_UNREGISTERED = "LIRL0201.queryUnregistered";
    public static final String QUERY_REVERSION_INFO = "LIRL0201.queryReversionInfo";
    public static final String QUERY_OVER_TIME_REVERSION = "LIRL0201.queryOverTimeReversion";
    public static final String QUERY_VEHICLE_NO = "LIRL0201.queryVehicleNo";
    public static final String QUERY_VEHICLE_NO_BY_DIST_ORDER = "LIRL0201.queryVehicleNoByDistOrder";
    public static final String QUERY_HASH_MAP = "LIRL0201.queryHashMap";
    public static final String QUERY_HASH_MAP_ALL = "LIRL0201.queryHashMapAll";
    public static final String COUNT = "LIRL0201.count";
    public static final String INSERT = "LIRL0201.insert";
    public static final String UPDATE = "LIRL0201.update";
    public static final String DELETE = "LIRL0201.delete";
    public static final String UPDATE_STATUS = "LIRL0201.updateStatus";
    public static final String DELETE_BY_RESERVATION_NUMBER = "LIRL0201.deleteByReservationNumber";
    public static final String DELETE_BY_RESERVATION = "LIRL0201.deleteByReservation";
    public static final String QUERY_REVERSION = "LIRL0201.queryReversion";
    public static final String QUERY_REVERSION1 = "LIRL0201.queryReversion1";
    public static final String UPDATE_BY_CUSTOMER_INFO = "LIRL0201.updateByCustomerInfo";
    public static final String QUERY_ENTERING_THE_FACTORY = "LIRL0201.queryEnteringTheFactory";
    public static final String QUERY_ALL_RESERVATION_TIME = "LIRL0201.queryAllReservationTime";
    public static final String QUERY_VEHICLE_COUNT_CQ = "LIRL0201.queryVehicleCountCq";
    public static final String QUERY_VEHICLE_COUNT_CQ1 = "LIRL0201.queryVehicleCountCq1";

    private String reservationNumber = " ";        /* 预约单号*/
    private String segNo = " ";        /* 业务单元代代码*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String status = " ";        /* 状态(00：撤销，10：新增)*/
    private String carTraceStatus = " ";        /* 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）*/
    private String typeOfHandling = " ";        /* 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)*/
    private String customerId = " ";        /* 承运商/客户代码*/
    private String customerName = " ";        /* 承运商/客户名称*/
    private String driverName = " ";        /* 司机姓名*/
    private String driverTel = " ";        /* 司机电话*/
    private String driverIdentity = " ";        /* 司机身份*/
    private String vehicleNo = " ";        /* 车牌号*/
    private String startOfTransport = " ";        /* 运输起始地*/
    private String purposeOfTransport = " ";        /* 运输目的地*/
    private String reservationDate = " ";        /* 预约日期*/
    private String reservationTime = " ";        /* 预约时段*/
    private String isReservation = " ";        /* 是否有预约单*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private String lateEarlyFlag = "0";        /* 迟到早到标记*/
    private String checkDate = " ";        /* 进厂登记时间*/
    private String callDate = " ";        /* 叫号日期*/
    private String callTime = " ";        /* 叫号时段*/
    private String enterFactory = " ";        /* 入厂时间*/
    private String beginEntruckingTime = " ";        /* 作业开始时间*/
    private String completeUninstallTime = " ";        /* 作业结束时间*/
    private String leaveFactoryDate = " ";        /* 出厂时间*/
    private String theTimeFromRegistrationToEntry = " ";        /* 登记至进厂时长*/
    private String enterFactoryCompleteUninstallTime = " ";        /* 进厂至作业完成时长*/
    private String enterFactoryLeaveFactoryDate = " ";        /* 进厂至出厂时长*/
    private String businessTypeName = " ";        /* 装卸类型名称*/
    private String carTraceNo = " ";        /* 车辆跟踪号*/
    private String currentHandPointName = " ";        /* 当前装卸点名称*/
    private String targetHandPointName = " ";        /* 目标装卸点名称*/
    private String nextTarget = " ";/* 下一目标 */
    private String isSelfProduced = " ";/* 是否自带货 0 否，1 是 */
    private String selfProducedDesc = " ";/* 自带货描述 */
    private String visitUnit = " ";/* 拜访单位 */


    /**
     * the constructor
     */
    public LIRL0201() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00：撤销，10：新增)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isReservation");
        eiColumn.setDescName("是否有预约单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("typeOfHandling");
        eiColumn.setDescName("装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setDescName("预约日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setDescName("预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startOfTransport");
        eiColumn.setDescName("运输起始地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purposeOfTransport");
        eiColumn.setDescName("运输目的地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lateEarlyFlag");
        eiColumn.setDescName("迟到早到标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setDescName("进厂登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callDate");
        eiColumn.setDescName("叫号日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callTime");
        eiColumn.setDescName("叫号时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactory");
        eiColumn.setDescName("入厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("beginEntruckingTime");
        eiColumn.setDescName("作业开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("completeUninstallTime");
        eiColumn.setDescName("作业结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("theTimeFromRegistrationToEntry");
        eiColumn.setDescName("登记至进厂时长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactoryCompleteUninstallTime");
        eiColumn.setDescName("进厂至作业完成时长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactoryLeaveFactoryDate");
        eiColumn.setDescName("进厂至出厂时长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceStatus");
        eiColumn.setDescName("状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessTypeName");
        eiColumn.setDescName("装卸业务名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointName");
        eiColumn.setDescName("当前装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nextTarget");
        eiColumn.setDescName("下一目标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointName");
        eiColumn.setDescName("目标装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isSelfProduced");
        eiColumn.setDescName("是否自带货");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("selfProducedDesc");
        eiColumn.setDescName("自带货描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitUnit");
        eiColumn.setDescName("拜访单位");
        eiMetadata.addMeta(eiColumn);



    }

    public String getCurrentHandPointName() {
        return currentHandPointName;
    }

    public void setCurrentHandPointName(String currentHandPointName) {
        this.currentHandPointName = currentHandPointName;
    }

    public String getIsSelfProduced() {
        return isSelfProduced;
    }

    public void setIsSelfProduced(String isSelfProduced) {
        this.isSelfProduced = isSelfProduced;
    }

    public String getSelfProducedDesc() {
        return selfProducedDesc;
    }

    public void setSelfProducedDesc(String selfProducedDesc) {
        this.selfProducedDesc = selfProducedDesc;
    }

    public String getTargetHandPointName() {
        return targetHandPointName;
    }

    public void setTargetHandPointName(String targetHandPointName) {
        this.targetHandPointName = targetHandPointName;
    }

    public String getNextTarget() {
        return nextTarget;
    }

    public void setNextTarget(String nextTarget) {
        this.nextTarget = nextTarget;
    }

    /**
     * get the reservationNumber - 预约单号
     *
     * @return the reservationNumber
     */
    public String getReservationNumber() {
        return this.reservationNumber;
    }

    /**
     * set the reservationNumber - 预约单号
     */
    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    /**
     * get the segNo - 业务单元代代码
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 业务单元代代码
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the segName - 业务单元简称
     *
     * @return the segName
     */
    public String getSegName() {
        return this.segName;
    }

    /**
     * set the segName - 业务单元简称
     */
    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the unitCode - 业务单元代代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the status - 状态(00：撤销，10：新增)
     *
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态(00：撤销，10：新增)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the carTraceStatus
     * - 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）
     *
     * @return the carTraceStatus
     */
    public String getCarTraceStatus
    () {
        return this.carTraceStatus
                ;
    }

    /**
     * set the carTraceStatus
     * - 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）
     */
    public void setCarTraceStatus
    (String carTraceStatus
    ) {
        this.carTraceStatus
                = carTraceStatus
        ;
    }

    /**
     * get the typeOfHandling - 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
     *
     * @return the typeOfHandling
     */
    public String getTypeOfHandling() {
        return this.typeOfHandling;
    }

    /**
     * set the typeOfHandling - 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
     */
    public void setTypeOfHandling(String typeOfHandling) {
        this.typeOfHandling = typeOfHandling;
    }

    /**
     * get the customerId - 承运商/客户代码
     *
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 承运商/客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the customerName - 承运商/客户名称
     *
     * @return the customerName
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * set the customerName - 承运商/客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * get the driverName - 司机姓名
     *
     * @return the driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * set the driverName - 司机姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * get the driverTel - 司机电话
     *
     * @return the driverTel
     */
    public String getDriverTel() {
        return this.driverTel;
    }

    /**
     * set the driverTel - 司机电话
     */
    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
    }

    /**
     * get the driverIdentity - 司机身份
     *
     * @return the driverIdentity
     */
    public String getDriverIdentity() {
        return this.driverIdentity;
    }

    /**
     * set the driverIdentity - 司机身份
     */
    public void setDriverIdentity(String driverIdentity) {
        this.driverIdentity = driverIdentity;
    }

    /**
     * get the vehicleNo - 车牌号
     *
     * @return the vehicleNo
     */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
     * set the vehicleNo - 车牌号
     */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    /**
     * get the startOfTransport - 运输起始地
     *
     * @return the startOfTransport
     */
    public String getStartOfTransport() {
        return this.startOfTransport;
    }

    /**
     * set the startOfTransport - 运输起始地
     */
    public void setStartOfTransport(String startOfTransport) {
        this.startOfTransport = startOfTransport;
    }

    /**
     * get the purposeOfTransport - 运输目的地
     *
     * @return the purposeOfTransport
     */
    public String getPurposeOfTransport() {
        return this.purposeOfTransport;
    }

    /**
     * set the purposeOfTransport - 运输目的地
     */
    public void setPurposeOfTransport(String purposeOfTransport) {
        this.purposeOfTransport = purposeOfTransport;
    }

    /**
     * get the reservationDate - 预约日期
     *
     * @return the reservationDate
     */
    public String getReservationDate() {
        return this.reservationDate;
    }

    /**
     * set the reservationDate - 预约日期
     */
    public void setReservationDate(String reservationDate) {
        this.reservationDate = reservationDate;
    }

    /**
     * get the reservationTime - 预约时段
     *
     * @return the reservationTime
     */
    public String getReservationTime() {
        return this.reservationTime;
    }

    /**
     * set the reservationTime - 预约时段
     */
    public void setReservationTime(String reservationTime) {
        this.reservationTime = reservationTime;
    }

    /**
     * get the isReservation - 是否有预约单
     *
     * @return the isReservation
     */
    public String getIsReservation() {
        return this.isReservation;
    }

    /**
     * set the isReservation - 是否有预约单
     */
    public void setIsReservation(String isReservation) {
        this.isReservation = isReservation;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     *
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the lateEarlyFlag - 迟到早到标记
     *
     * @return the lateEarlyFlag
     */
    public String getLateEarlyFlag() {
        return this.lateEarlyFlag;
    }

    /**
     * set the lateEarlyFlag - 迟到早到标记
     */
    public void setLateEarlyFlag(String lateEarlyFlag) {
        this.lateEarlyFlag = lateEarlyFlag;
    }

    /**
     * get the checkDate - 进厂登记时间
     *
     * @return the checkDate
     */
    public String getCheckDate() {
        return this.checkDate;
    }

    /**
     * set the checkDate - 进厂登记时间
     */
    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    /**
     * get the callDate - 叫号日期
     *
     * @return the callDate
     */
    public String getCallDate() {
        return this.callDate;
    }

    /**
     * set the callDate - 叫号日期
     */
    public void setCallDate(String callDate) {
        this.callDate = callDate;
    }

    /**
     * get the callTime - 叫号时段
     *
     * @return the callTime
     */
    public String getCallTime() {
        return this.callTime;
    }

    /**
     * set the callTime - 叫号时段
     */
    public void setCallTime(String callTime) {
        this.callTime = callTime;
    }

    /**
     * get the enterFactory - 入厂时间
     *
     * @return the enterFactory
     */
    public String getEnterFactory() {
        return this.enterFactory;
    }

    /**
     * set the enterFactory - 入厂时间
     */
    public void setEnterFactory(String enterFactory) {
        this.enterFactory = enterFactory;
    }

    /**
     * get the beginEntruckingTime - 作业开始时间
     *
     * @return the beginEntruckingTime
     */
    public String getBeginEntruckingTime() {
        return this.beginEntruckingTime;
    }

    /**
     * set the beginEntruckingTime - 作业开始时间
     */
    public void setBeginEntruckingTime(String beginEntruckingTime) {
        this.beginEntruckingTime = beginEntruckingTime;
    }

    /**
     * get the completeUninstallTime - 作业结束时间
     *
     * @return the completeUninstallTime
     */
    public String getCompleteUninstallTime() {
        return this.completeUninstallTime;
    }

    /**
     * set the completeUninstallTime - 作业结束时间
     */
    public void setCompleteUninstallTime(String completeUninstallTime) {
        this.completeUninstallTime = completeUninstallTime;
    }

    /**
     * get the leaveFactoryDate - 出厂时间
     *
     * @return the leaveFactoryDate
     */
    public String getLeaveFactoryDate() {
        return this.leaveFactoryDate;
    }

    /**
     * set the leaveFactoryDate - 出厂时间
     */
    public void setLeaveFactoryDate(String leaveFactoryDate) {
        this.leaveFactoryDate = leaveFactoryDate;
    }

    /**
     * get the theTimeFromRegistrationToEntry - 登记至进厂时长
     *
     * @return the theTimeFromRegistrationToEntry
     */
    public String getTheTimeFromRegistrationToEntry() {
        return this.theTimeFromRegistrationToEntry;
    }

    /**
     * set the theTimeFromRegistrationToEntry - 登记至进厂时长
     */
    public void setTheTimeFromRegistrationToEntry(String theTimeFromRegistrationToEntry) {
        this.theTimeFromRegistrationToEntry = theTimeFromRegistrationToEntry;
    }

    /**
     * get the enterFactoryCompleteUninstallTime - 进厂至作业完成时长
     *
     * @return the enterFactoryCompleteUninstallTime
     */
    public String getEnterFactoryCompleteUninstallTime() {
        return this.enterFactoryCompleteUninstallTime;
    }

    /**
     * set the enterFactoryCompleteUninstallTime - 进厂至作业完成时长
     */
    public void setEnterFactoryCompleteUninstallTime(String enterFactoryCompleteUninstallTime) {
        this.enterFactoryCompleteUninstallTime = enterFactoryCompleteUninstallTime;
    }

    /**
     * get the enterFactoryLeaveFactoryDate - 进厂至出厂时长
     *
     * @return the enterFactoryLeaveFactoryDate
     */
    public String getEnterFactoryLeaveFactoryDate() {
        return this.enterFactoryLeaveFactoryDate;
    }

    /**
     * set the enterFactoryLeaveFactoryDate - 进厂至出厂时长
     */
    public void setEnterFactoryLeaveFactoryDate(String enterFactoryLeaveFactoryDate) {
        this.enterFactoryLeaveFactoryDate = enterFactoryLeaveFactoryDate;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    /**
     * get the carTraceNo - 车辆跟踪号
     *
     * @return the carTraceNo
     */
    public String getCarTraceNo() {
        return this.carTraceNo;
    }

    /**
     * set the carTraceNo - 车辆跟踪号
     */
    public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
    }

    public String getVisitUnit() {
        return visitUnit;
    }

    public void setVisitUnit(String visitUnit) {
        this.visitUnit = visitUnit;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setReservationNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationNumber")), reservationNumber));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setCarTraceStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceStatus")), carTraceStatus));
        setTypeOfHandling(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("typeOfHandling")), typeOfHandling));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
        setDriverTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverTel")), driverTel));
        setDriverIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverIdentity")), driverIdentity));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setStartOfTransport(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startOfTransport")), startOfTransport));
        setPurposeOfTransport(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("purposeOfTransport")), purposeOfTransport));
        setReservationDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationDate")), reservationDate));
        setReservationTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationTime")), reservationTime));
        setIsReservation(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isReservation")), isReservation));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setLateEarlyFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lateEarlyFlag")), lateEarlyFlag));
        setCheckDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkDate")), checkDate));
        setCallDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("callDate")), callDate));
        setCallTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("callTime")), callTime));
        setEnterFactory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("enterFactory")), enterFactory));
        setBeginEntruckingTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("beginEntruckingTime")), beginEntruckingTime));
        setCompleteUninstallTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("completeUninstallTime")), completeUninstallTime));
        setLeaveFactoryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leaveFactoryDate")), leaveFactoryDate));
        setTheTimeFromRegistrationToEntry(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("theTimeFromRegistrationToEntry")), theTimeFromRegistrationToEntry));
        setEnterFactoryCompleteUninstallTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("enterFactoryCompleteUninstallTime")), enterFactoryCompleteUninstallTime));
        setEnterFactoryLeaveFactoryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("enterFactoryLeaveFactoryDate")), enterFactoryLeaveFactoryDate));
        setBusinessTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessTypeName")), businessTypeName));
        setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
        setCurrentHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("currentHandPointName")), currentHandPointName));
        setTargetHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointName")), targetHandPointName));
        setNextTarget(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nextTarget")), nextTarget));
        setIsSelfProduced(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isSelfProduced")), isSelfProduced));
        setSelfProducedDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("selfProducedDesc")), selfProducedDesc));
        setVisitUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitUnit")), visitUnit));

    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("reservationNumber", StringUtils.toString(reservationNumber, eiMetadata.getMeta("reservationNumber")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("carTraceStatus", StringUtils.toString(carTraceStatus, eiMetadata.getMeta("carTraceStatus")));
        map.put("typeOfHandling", StringUtils.toString(typeOfHandling, eiMetadata.getMeta("typeOfHandling")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("driverName", StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
        map.put("driverTel", StringUtils.toString(driverTel, eiMetadata.getMeta("driverTel")));
        map.put("driverIdentity", StringUtils.toString(driverIdentity, eiMetadata.getMeta("driverIdentity")));
        map.put("vehicleNo", StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("startOfTransport", StringUtils.toString(startOfTransport, eiMetadata.getMeta("startOfTransport")));
        map.put("purposeOfTransport", StringUtils.toString(purposeOfTransport, eiMetadata.getMeta("purposeOfTransport")));
        map.put("reservationDate", StringUtils.toString(reservationDate, eiMetadata.getMeta("reservationDate")));
        map.put("reservationTime", StringUtils.toString(reservationTime, eiMetadata.getMeta("reservationTime")));
        map.put("isReservation", StringUtils.toString(isReservation, eiMetadata.getMeta("isReservation")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("lateEarlyFlag", StringUtils.toString(lateEarlyFlag, eiMetadata.getMeta("lateEarlyFlag")));
        map.put("checkDate", StringUtils.toString(checkDate, eiMetadata.getMeta("checkDate")));
        map.put("callDate", StringUtils.toString(callDate, eiMetadata.getMeta("callDate")));
        map.put("callTime", StringUtils.toString(callTime, eiMetadata.getMeta("callTime")));
        map.put("enterFactory", StringUtils.toString(enterFactory, eiMetadata.getMeta("enterFactory")));
        map.put("beginEntruckingTime", StringUtils.toString(beginEntruckingTime, eiMetadata.getMeta("beginEntruckingTime")));
        map.put("completeUninstallTime", StringUtils.toString(completeUninstallTime, eiMetadata.getMeta("completeUninstallTime")));
        map.put("leaveFactoryDate", StringUtils.toString(leaveFactoryDate, eiMetadata.getMeta("leaveFactoryDate")));
        map.put("theTimeFromRegistrationToEntry", StringUtils.toString(theTimeFromRegistrationToEntry, eiMetadata.getMeta("theTimeFromRegistrationToEntry")));
        map.put("enterFactoryCompleteUninstallTime", StringUtils.toString(enterFactoryCompleteUninstallTime, eiMetadata.getMeta("enterFactoryCompleteUninstallTime")));
        map.put("enterFactoryLeaveFactoryDate", StringUtils.toString(enterFactoryLeaveFactoryDate, eiMetadata.getMeta("enterFactoryLeaveFactoryDate")));
        map.put("businessTypeName", StringUtils.toString(businessTypeName, eiMetadata.getMeta("businessTypeName")));
        map.put("carTraceNo", StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
        map.put("currentHandPointName", StringUtils.toString(currentHandPointName, eiMetadata.getMeta("currentHandPointName")));
        map.put("targetHandPointName", StringUtils.toString(targetHandPointName, eiMetadata.getMeta("targetHandPointName")));
        map.put("nextTarget", StringUtils.toString(nextTarget, eiMetadata.getMeta("nextTarget")));
        map.put("isSelfProduced", StringUtils.toString(isSelfProduced, eiMetadata.getMeta("isSelfProduced")));
        map.put("selfProducedDesc", StringUtils.toString(selfProducedDesc, eiMetadata.getMeta("selfProducedDesc")));
        map.put("visitUnit", StringUtils.toString(visitUnit, eiMetadata.getMeta("visitUnit")));
        return map;

    }
}