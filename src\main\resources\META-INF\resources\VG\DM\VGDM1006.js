$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    // 列表选中的业务单元信息
    var selectUnitInfo = {};
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务单元查询"
                            });
                        }
                    }
                }
            ],
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["uuid"] = " ";
                    item["status"] = "10";
                    if (IPLAT.isBlankString(item["unitCode"])) {
                        item["unitCode"] = unitInfo.unitCode;
                        item["segNo"] = unitInfo.segNo;
                    }
                });
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    // 修改时unitCode(业务单元代码)和equipmentName(设备名称)不可修改
                    if (e.field === "unitCode" ) {
                        e.preventDefault();
                    }
                }
            },
            loadComplete: function (grid) {
                //修改
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM1006", "update", true, null, null, false);
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo01",
        notInqu: true,
        afterSelect: function (rows) {
            selectUnitInfo = {};
            if (rows.length > 0) {
                selectUnitInfo = rows[0];
                let rowNums = resultGrid.getCheckedRowsIndex();
                resultGrid.setCellValue(rowNums, "unitCode", rows[0].unitCode);
                resultGrid.setCellValue(rowNums, "segNo", rows[0].segNo);
                resultGrid.setCellValue(rowNums, "eArchivesNo", "");
                resultGrid.setCellValue(rowNums, "equipmentName", "");
                resultGrid.refresh();
            }
        }
    });
});
