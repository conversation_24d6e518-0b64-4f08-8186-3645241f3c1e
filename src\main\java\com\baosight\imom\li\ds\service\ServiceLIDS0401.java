package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0401;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行车管理
 */
public class ServiceLIDS0401 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0401().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0401.QUERY, new LIDS0401());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //跨区代码
                    String crossArea = MapUtils.getString(itemMap, "crossArea");
                    //增加校验,同一跨区不能匹配多部过跨小车
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("transferCarId", MapUtils.getString(itemMap, "transferCarId"));
                    String[] crossAreasArray = crossArea.split(",");
                    queryMap.put("crossAreas", crossAreasArray);
                    int count = super.count(LIDS0401.COUNT, queryMap);
                    if (count > 0) {
                        //打印日志
                        Gson gson = new Gson();
                        throw new PlatException(gson.toJson(crossAreasArray) + "中存在已维护过跨小车的跨区编码，不可新增!");
                    }
                    //查询区域代码状态，同一过跨小车编码(设备档案编码)只能存在一条非撤销状态的数据
                    queryMap.clear();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("transferCarId", MapUtils.getString(itemMap, "transferCarId"));
                    count = super.count(LIDS0401.COUNT, queryMap);
                    if (count > 0) {
                        throw new PlatException(MapUtils.getString(itemMap, "transferCarId") + ",过跨小车编码已存在，不可新增!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0401.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //跨区代码
                    String crossArea = MapUtils.getString(itemMap, "crossArea");
                    //增加校验,同一跨区不能匹配多部过跨小车
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("transferCarId", MapUtils.getString(itemMap, "transferCarId"));
                    String[] crossAreasArray = crossArea.split(",");
                    queryMap.put("crossAreas", crossAreasArray);
                    queryMap.put("uuidX", MapUtils.getString(itemMap, "uuid"));
                    int count = super.count(LIDS0401.COUNT, queryMap);
                    if (count > 0) {
                        //打印日志
                        Gson gson = new Gson();
                        throw new PlatException(gson.toJson(crossAreasArray) + "中存在已维护过跨小车的跨区编码，不可修改!");
                    }
                    //查询区域代码状态，判断非新增状态数据不可修改
                    queryMap.clear();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    /*queryMap.put("transferCarId", MapUtils.getString(itemMap, "transferCarId"));*/
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    queryMap.put("status", "10");
                    count = super.count(LIDS0401.COUNT_UUID, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "transferCarId") + ",过跨小车编码状态非空闲，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0401.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                /*queryMap.put("transferCarId", MapUtils.getString(itemMap, "transferCarId"));*/
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0401.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "transferCarId") + ",过跨小车编码状态非空闲，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0401.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}
