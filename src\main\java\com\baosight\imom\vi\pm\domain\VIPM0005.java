package com.baosight.imom.vi.pm.domain;

import com.baosight.imom.common.vi.domain.Tvipm0005;

/**
 * 标准工时表实体类
 */
public class VIPM0005 extends Tvipm0005 {
    /**
     * 查询
     */
    public static final String QUERY = "VIPM0005.query";
    public static final String QUERY_FOR_B1 = "VIPM0005.queryForB1";
    /**
     * 查询条数
     */
    public static final String COUNT = "VIPM0005.count";
    /**
     * 新增
     */
    public static final String INSERT = "VIPM0005.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VIPM0005.update";
    /**
     * 修改
     */
    public static final String UPD_FOR_DEL = "VIPM0005.updForDel";
    /**
     * 查询条数
     */
    public static final String QUERY_FOR_AVG = "VIPM0005.queryForAvg";
}
