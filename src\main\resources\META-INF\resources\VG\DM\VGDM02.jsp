<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true" required="true"/>
            <EF:EFInput ename="inqu_status-0-spotCheckStandardId" cname="点检标准编号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-spotCheckStatus" cname="状态" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P016"/>
            </EF:EFSelect>

        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                             ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false"
                             containerId="deviceInfoMainQuery" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                             cname="分部设备名称" colWidth="3"/>


        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-spotCheckNature" cname="点检性质" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P050"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="点检标准清单">
        <EF:EFGrid blockId="result" autoDraw="no" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" required="true" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" enable="false" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFComboColumn ename="spotCheckStatus" enable="false" cname="状态" align="center" width="60">
                <EF:EFCodeOption codeName="P016"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="apprStatus" enable="false" cname="审批状态" align="center" width="70">
                <EF:EFOption label="审批中" value="60"/>
                <EF:EFOption label="审核通过" value="70"/>
                <EF:EFOption label="审核驳回" value="7X"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="spotCheckStandardId" enable="false" cname="点检标准编号" width="120" align="center"/>
            <EF:EFColumn ename="eArchivesNo" enable="false" cname="设备代码" width="70" align="center"/>
            <EF:EFColumn ename="equipmentName" required="true" cname="设备名称"/>
            <EF:EFColumn ename="deviceCode" enable="false" cname="分部设备代码" width="110" align="center"/>
            <EF:EFColumn ename="deviceName" required="true" cname="分部设备名称"/>
            <EF:EFComboColumn ename="deviceCheckStatus" required="true" cname="设备状态" align="center" width="90">
                <EF:EFCodeOption codeName="P047"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="isPublish" required="true" cname="是否挂牌" align="center" width="80">
                <EF:EFOption value="1" label="是"/>
                <EF:EFOption value="0" label="否"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="spotCheckContent" required="true" cname="点检内容" width="200"/>
            <EF:EFComboColumn ename="spotCheckMethod" required="true" cname="点检方法" align="center" width="110">
                <EF:EFCodeOption codeName="P049"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="spotCheckStandardType" required="true" cname="标准类型" align="center" width="80">
                <EF:EFCodeOption codeName="P051"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="judgmentStandard" cname="判断标准" width="200"/>
            <EF:EFComboColumn ename="isPicture" required="true" cname="是否上传照片" align="center" width="110">
                <EF:EFOption value="1" label="是"/>
                <EF:EFOption value="0" label="否"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="spotCheckNature" required="true" cname="点检性质" align="center" width="110">
                <EF:EFCodeOption codeName="P050"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="spotCheckImplemente" enable="false" required="true" cname="实施方" align="center" width="70">
                <EF:EFCodeOption codeName="P048"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="benchmarkDate" required="true" cname="基准日期" editType="date" align="center" width="110"
                         dateFormat="yyyyMMdd"/>
            <EF:EFColumn ename="spotCheckCycle" required="true" cname="点检周期(天)" align="right" width="110"/>
            <EF:EFColumn ename="measureId" cname="计量单位"/>
            <EF:EFColumn ename="lowerLimit" cname="下限值" align="right"/>
            <EF:EFColumn ename="upperLimit" cname="上限值" align="right"/>
            <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
            <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
            <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
            <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow id="popup" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfoMainQuery" width="90%" height="60%"/>
    <EF:EFWindow id="clip" title="粘贴板导入" height="40%" width="25%">
        <textarea id="clipContent" name="clipContent" class="json_input" rows="16" style="width: 99%; height: 95%;"
                  spellcheck="false" placeholder="请粘贴"></textarea>
    </EF:EFWindow>
</EF:EFPage>