create table meli.tlids0301
(
    SEG_NO                varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE             varchar(12)  default ' ' not null comment '业务单元代代码',
    CRANE_ID              varchar(20)  default ' ' not null comment '行车编号',
    CRANE_NAME            varchar(200) default ' ' not null comment '行车名称',
    CROSS_AREA            varchar(20)  default ' ' not null comment '跨区代码',
    CROSS_AREA_NAME       varchar(32)  default ' ' not null comment '跨区名称',
    FACTORY_AREA          varchar(20)  default ' ' not null comment '厂区代码',
    FACTORY_AREA_NAME     varchar(50)  default ' ' not null comment '厂区名称',
    FACTORY_BUILDING      varchar(20)  default ' ' not null comment '厂房代码',
    FACTORY_BUILDING_NAME varchar(32)  default ' ' not null comment '厂房名称',
    STATUS                varchar(2)   default ' ' not null comment '状态',
    REC_CREATOR           varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME      varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME       varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR           varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME      varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME       varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG          varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER           varchar(10)  default ' ' null comment '租户',
    DEL_FLAG              smallint     default 0   null comment '删除标记',
    UUID                  varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids0301_UUID_uindex
        unique (UUID)
)
    comment '行车管理表' collate = utf8_bin;

