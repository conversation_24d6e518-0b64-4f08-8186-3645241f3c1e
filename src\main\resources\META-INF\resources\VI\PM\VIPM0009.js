$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            /**
             * EFGrid渲染成功的回调事件
             */
            loadComplete: function (grid) {
                //生成行车作业清单
                $("#ADD_CRANE_ORDER").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VIPM0009", "createCraneOrderFromLoading", true, null, null, false);
                });
            },
            /**
             * 勾选行后，点击单元格准备编辑时的事件
             * beforeEdit可以用于自定义单元格是否可以编辑，不要和列的readonly，enable混用
             * @param e 事件对象
             * e.sender Grid对象
             * e.container 单元格td jQuery对象
             * e.row 行号
             * e.col 列号(columns中的列配置信息数组中的column对象的index)
             * e.model 行数据对象 kendo.data.Model
             * e.field 列英文名
             * e.preventDefault 禁止编辑
             */
            beforeEdit: function (e) {
            }
        }
    };

    IPLATUI.EFPopupInput = {
        "inqu_status-0-unitCode": {
            clearInput: function (e) {
                $("#inqu_status-0-unitCode").val('');
                $("#inqu_status-0-segNo").val('');
            }
        }
    };

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });
});