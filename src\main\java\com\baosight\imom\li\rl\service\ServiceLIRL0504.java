package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0309;
import com.baosight.imom.li.rl.dao.LIRL0504;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.MapUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 谢徐皓
 * @Description: ${人员信息管理}
 * @Date: 2025/1/10 16:00
 * @Version: 1.0
 */
public class ServiceLIRL0504 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0504().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        outInfo = super.query(inInfo, LIRL0504.QUERY);
        return outInfo;
    }
    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                String perNo = MapUtils.getString(hashMap, "perNo", "");//人员工号
                String perType = MapUtils.getString(hashMap, "perType", "");//人员类型
                String perName = MapUtils.getString(hashMap, "perName", "");//人员类型
                //查询数据是否重复
                Map query = new HashMap();
                query.put("segNo", segNo);
                query.put("perName", perName);
                query.put("perType", perType);
                query.put("delFlag", 0);
                int count = super.count(LIRL0504.COUNT, query);
                if (count > 0) {
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                String uuid = UUIDHexIdGenerator.generate().toString();
                hashMap.put("uuid", uuid);//UUID
                hashMap.put("status", "10");//UUID
                // 创建人工号
                hashMap.put("recCreator", UserSession.getUserId());
                // 创建人姓名
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", UserSession.getUserId());
                // 修改人姓名
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                hashMap.put("delFlag", "0");
                hashMap.put("archiveFlag", "0");
            }
            inInfo = super.insert(inInfo, LIRL0504.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                List<LIRL0504> query = dao.query(LIRL0504.QUERY, hashMap);
                for (LIRL0504 lirl0504:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0504);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                String perNo = MapUtils.getString(hashMap, "perNo", "");
                String perType = MapUtils.getString(hashMap, "perType", "");//人员类型
                String perName = MapUtils.getString(hashMap, "perName", "");//人员类型
                //查询数据是否重复
                Map queryCount = new HashMap();
//                queryCount.put("perNo",perNo);
                queryCount.put("delFlag", 0);
                queryCount.put("perType", perType);
                queryCount.put("notUuid", uuid);
                queryCount.put("perName", perName);
                int count = super.count(LIRL0504.COUNT, queryCount);
                if (count>0){
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0504.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                List<LIRL0504> query = dao.query(LIRL0504.QUERY, hashMap);
                for (LIRL0504 lirl0504:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0504);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0504.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 启用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0504> query = dao.query(LIRL0504.QUERY, hashMap);
                for (LIRL0504 lirl0504:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0504);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", MesConstant.Status.K20);//确认状态
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0504.UPDATE,hashMap);
            }
            // inInfo = super.update(inInfo, LIRL0309.UPDATE,RESULT1);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 停用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    /**
     * 启用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0504> query = dao.query(LIRL0504.QUERY, hashMap);
                for (LIRL0504 lirl0504:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0504);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", MesConstant.Status.K10);//确认状态
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0504.UPDATE,hashMap);
            }
            // inInfo = super.update(inInfo, LIRL0309.UPDATE,RESULT1);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }



}
