<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">


<sqlMap namespace="LIRL0222">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select * from
        (select a.SEG_NO                                          as       "segNo",
        a.SEG_NO,
        a.UNIT_CODE                                       as       "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO                                    as       "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS                                          as       "status",
        a.HAND_TYPE                                       as       "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO                                      as       "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD                                         as       "idCard",
        a.ID_CARD,
        a.DRIVER_NAME                                     as       "driverName",
        a.DRIVER_NAME,
        a.<PERSON><PERSON>_NUM                                         as       "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER                              as       "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE                                      as       "checkDate",
        a.CHECK_DATE,
        ENTER_FACTORY                                     as       "enterFactory",
        ENTER_FACTORY,
        BEGIN_ENTRUCKING_TIME                             as       "beginEntruckingTime",
        BEGIN_ENTRUCKING_TIME,
        COMPLETE_UNINSTALL_TIME                           as       "completeUninstallTime",
        COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE                                as       "leaveFactoryDate",
        LEAVE_FACTORY_DATE,
        CUSTOMER_SIGNING_TIME                             as       "customerSigningTime",
        CUSTOMER_SIGNING_TIME,
        TARGET_HAND_POINT_ID                              as       "targetHandPointId",
        TARGET_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "targetHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "target_hand_point_name",
        CURRENT_HAND_POINT_ID                             as       "currentHandPointId",
        CURRENT_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "currentHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "current_hand_point_name",
        a.FACTORY_AREA                                    as       "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME                               as       "factoryAreaName",
        a.FACTORY_AREA_NAME,
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "nextTarget",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "next_target",
        UNLOAD_LEAVE_FLAG                                 as       "unloadLeaveFlag",
        UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR                                     as       "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME                                as       "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME                                 as       "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR                                     as       "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME                                as       "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME                                 as       "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG                                        as       "delFlag",
        a.DEL_FLAG,
        a.REMARK                                          as       "remark",
        a.SYS_REMARK                                      as       "sysRemark",
        a.SYS_REMARK,
        a.UUID                                            as       "uuid",
        tlirl0201.IS_RESERVATION                                   "isReservation",
        (select if(count(*) > 0, 1, 0)
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                   "whet_appointment",
        tlirl0201.START_OF_TRANSPORT                               "startOfTransport",
        tlirl0201.START_OF_TRANSPORT                               "start_of_transport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purposeOfTransport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purpose_of_transport",
        tlirl0201.CUSTOMER_NAME                                    "customerName",
        tlirl0201.CUSTOMER_NAME                                    "customer_name",
        tlirl0201.STATUS                                           "appointmentStatus",
        tlirl0201.STATUS                                           "appointment_status",
        tlirl0201.RESERVATION_DATE                                 "reservationDate",
        tlirl0201.RESERVATION_DATE                                 "reservation_date",
        tlirl0201.RESERVATION_TIME                                 "reservationTime",
        tlirl0201.RESERVATION_TIME                                 "reservation_time",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "lateEarlyFlag",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "late_early_flag",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "businessType",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "business_type",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "callDate",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "call_date",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromRegistrationToEntry",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_registration_to_entry",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "theTimeFromEnteringTheFactoryToTheStartOfTheJob",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "the_time_from_entering_the_factory_to_the_start_of_the_job",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromTheCompletionOfTheJobToTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_the_completion_of_the_job_to_the_factory",
        if(a.COMPLETE_UNINSTALL_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0, TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",
        if(COMPLETE_UNINSTALL_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "the_time_from_entering_the_factory_to_the_completion_of_the_job",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromEnteringTheFactoryToLeavingTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_entering_the_factory_to_leaving_the_factory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registeredToTheFactoryTime",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registered_to_the_factory_time",
        tlirl0308.SIGN_OFF_TIME                           as       "sign_off_time",
        tlirl0308.SIGN_OFF_TIME                           as       "signOffTime",
        CASE
        WHEN (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0 THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')) / 60), '时', MOD(
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')), 60), '分')
        WHEN a.LEAVE_FACTORY_DATE != '' and tlirl0308.SIGN_OFF_TIME != '' then '0时0分'
        ELSE '' END                                            "theTimeFromTheFactoryToTheTimeOfReceipt",
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')),
        0)                                                      "the_time_from_the_factory_to_the_time_of_receipt",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signing_location_longitude",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signingLocationLongitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signing_location_latitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signingLocationLatitude",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitude_latitude_check",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitudeLatitudeCheck",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "final_station_longitude",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "finalStationLongitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "final_station_latitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "finalStationLatitude",
        ifnull(tlirl0308.WEIGHT, 0)                    as       "outWeight",
        ifnull(tlirl0308.QUANTITY, 0)                  as       "outQuantity",
        tlirl0308.SIGNING_ADDRESS                         AS       "signingAddress",
        tlirl0308.SIGNING_ADDRESS                         AS       "signing_address",
        tlirl0308.DEST_SPOT_ADDR                          AS       "destSpotAddr",
        tlirl0308.DEST_SPOT_ADDR                          AS       "dest_spot_addr",
        tlirl0308.PUTIN_ID                                as       "putinId",
        tlirl0308.PUTOUT_ID                               as       "putoutId",
        tlirl0308.VOUCHER_NUM                               as       "voucherNum",
        tlirl0308.DELIVER_NAME                            as       "deliverName",
        tlirl0308.PACK_ID                                 as       "packId",
        tlirl0308.MAT_INNER_ID                            as       "matInnerId",
        tlirl0308.CUSTOMER_NAME                           as       "customerNamePack",
        tlirl0308.FACTORY_ORDER_NUM                       as       "factoryOrderNum",
        tlirl0308.PUT_IN_OUT_FLAG                         as       "putInOutFlag",
        tlirl0308.SIGNATURE_FLAG                          as       "signatureFlag",
        tlirl0308.PROD_TYPE_ID                            as       "prodTypeId",
        tlirl0308.PROD_TYPE_NAME                          as       "prodTypeName",
        tlirl0308.SHOPSIGN                                as       "shopsign",
        tlirl0308.SPEC_DESC                               as       "specDesc",
        tlirl0308.WAREHOUSE_CODE                          as       "warehouseCode",
        tlirl0308.WAREHOUSE_NAME                          as       "warehouseName",
        tlirl0308.FINAL_DESTINATION                       as       "finalDestination",
        tlirl0308.PER_NAME                       as       "perName"
        from MELI.tlirl0301 a
        left join meli.tlirl0201 tlirl0201
        on tlirl0201.SEG_NO = a.SEG_NO and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER and
        tlirl0201.STATUS != '00' and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        left join
        meli.tlirl0308 tlirl0308
        on tlirl0308.SEG_NO = a.SEG_NO and tlirl0308.CAR_TRACE_NO = a.CAR_TRACE_NO and
        tlirl0308.VEHICLE_NO = a.VEHICLE_NO
        left join meli.tlirl0302 tlirl0302
        on tlirl0302.SEG_NO = a.SEG_NO and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO and
        tlirl0302.STATUS != '00'
        where 1 = 1
        and tlirl0308.PACK_ID !=' '
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        union
        select a.SEG_NO                                          as       "segNo",
        a.SEG_NO,
        a.UNIT_CODE                                       as       "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO                                    as       "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS                                          as       "status",
        a.HAND_TYPE                                       as       "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO                                      as       "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD                                         as       "idCard",
        a.ID_CARD,
        a.DRIVER_NAME                                     as       "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM                                         as       "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER                              as       "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE                                      as       "checkDate",
        a.CHECK_DATE,
        ENTER_FACTORY                                     as       "enterFactory",
        ENTER_FACTORY,
        BEGIN_ENTRUCKING_TIME                             as       "beginEntruckingTime",
        BEGIN_ENTRUCKING_TIME,
        COMPLETE_UNINSTALL_TIME                           as       "completeUninstallTime",
        COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE                                as       "leaveFactoryDate",
        LEAVE_FACTORY_DATE,
        CUSTOMER_SIGNING_TIME                             as       "customerSigningTime",
        CUSTOMER_SIGNING_TIME,
        TARGET_HAND_POINT_ID                              as       "targetHandPointId",
        TARGET_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "targetHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "target_hand_point_name",
        CURRENT_HAND_POINT_ID                             as       "currentHandPointId",
        CURRENT_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "currentHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "current_hand_point_name",
        a.FACTORY_AREA                                    as       "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME                               as       "factoryAreaName",
        a.FACTORY_AREA_NAME,
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "nextTarget",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "next_target",
        UNLOAD_LEAVE_FLAG                                 as       "unloadLeaveFlag",
        UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR                                     as       "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME                                as       "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME                                 as       "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR                                     as       "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME                                as       "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME                                 as       "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG                                        as       "delFlag",
        a.DEL_FLAG,
        a.REMARK                                          as       "remark",
        a.SYS_REMARK                                      as       "sysRemark",
        a.SYS_REMARK,
        a.UUID                                            as       "uuid",
        tlirl0201.IS_RESERVATION                                   "isReservation",
        (select if(count(*) > 0, 1, 0)
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                   "whet_appointment",
        tlirl0201.START_OF_TRANSPORT                               "startOfTransport",
        tlirl0201.START_OF_TRANSPORT                               "start_of_transport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purposeOfTransport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purpose_of_transport",
        tlirl0201.CUSTOMER_NAME                                    "customerName",
        tlirl0201.CUSTOMER_NAME                                    "customer_name",
        tlirl0201.STATUS                                           "appointmentStatus",
        tlirl0201.STATUS                                           "appointment_status",
        tlirl0201.RESERVATION_DATE                                 "reservationDate",
        tlirl0201.RESERVATION_DATE                                 "reservation_date",
        tlirl0201.RESERVATION_TIME                                 "reservationTime",
        tlirl0201.RESERVATION_TIME                                 "reservation_time",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "lateEarlyFlag",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "late_early_flag",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "businessType",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "business_type",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "callDate",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "call_date",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromRegistrationToEntry",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_registration_to_entry",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "theTimeFromEnteringTheFactoryToTheStartOfTheJob",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "the_time_from_entering_the_factory_to_the_start_of_the_job",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromTheCompletionOfTheJobToTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_the_completion_of_the_job_to_the_factory",
        if(a.COMPLETE_UNINSTALL_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0, TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",
        if(COMPLETE_UNINSTALL_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "the_time_from_entering_the_factory_to_the_completion_of_the_job",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromEnteringTheFactoryToLeavingTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_entering_the_factory_to_leaving_the_factory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registeredToTheFactoryTime",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registered_to_the_factory_time",
        tlirl0308.SIGN_OFF_TIME                           as       "sign_off_time",
        tlirl0308.SIGN_OFF_TIME                           as       "signOffTime",
        CASE
        WHEN (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0 THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')) / 60), '时', MOD(
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')), 60), '分')
        WHEN a.LEAVE_FACTORY_DATE != '' and tlirl0308.SIGN_OFF_TIME != '' then '0时0分'
        ELSE '' END                                            "theTimeFromTheFactoryToTheTimeOfReceipt",
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')),
        0)                                                      "the_time_from_the_factory_to_the_time_of_receipt",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signing_location_longitude",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signingLocationLongitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signing_location_latitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signingLocationLatitude",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitude_latitude_check",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitudeLatitudeCheck",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "final_station_longitude",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "finalStationLongitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "final_station_latitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "finalStationLatitude",
        ifnull(tlirl0308.WEIGHT, 0)                    as       "outWeight",
        ifnull(tlirl0308.QUANTITY, 0)                  as       "outQuantity",
        tlirl0308.SIGNING_ADDRESS                         AS       "signingAddress",
        tlirl0308.SIGNING_ADDRESS                         AS       "signing_address",
        tlirl0308.DEST_SPOT_ADDR                          AS       "destSpotAddr",
        tlirl0308.DEST_SPOT_ADDR                          AS       "dest_spot_addr",
        tlirl0308.PUTIN_ID                                as       "putinId",
        tlirl0308.PUTOUT_ID                               as       "putoutId",
        tlirl0308.VOUCHER_NUM                               as       "voucherNum",
        tlirl0308.DELIVER_NAME                            as       "deliverName",
        tlirl0308.PACK_ID                                 as       "packId",
        tlirl0308.MAT_INNER_ID                            as       "matInnerId",
        tlirl0308.CUSTOMER_NAME                           as       "customerNamePack",
        tlirl0308.FACTORY_ORDER_NUM                       as       "factoryOrderNum",
        tlirl0308.PUT_IN_OUT_FLAG                         as       "putInOutFlag",
        tlirl0308.SIGNATURE_FLAG                          as       "signatureFlag",
        tlirl0308.PROD_TYPE_ID                            as       "prodTypeId",
        tlirl0308.PROD_TYPE_NAME                          as       "prodTypeName",
        tlirl0308.SHOPSIGN                                as       "shopsign",
        tlirl0308.SPEC_DESC                               as       "specDesc",
        tlirl0308.WAREHOUSE_CODE                          as       "warehouseCode",
        tlirl0308.WAREHOUSE_NAME                          as       "warehouseName",
        tlirl0308.FINAL_DESTINATION                       as       "finalDestination",
        tlirl0308.PER_NAME                       as       "perName"

        from MELI.tlirl0311 a
        left join meli.tlirl0201 tlirl0201
        on tlirl0201.SEG_NO = a.SEG_NO and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER and
        tlirl0201.STATUS != '00' and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        left join
        meli.tlirl0308 tlirl0308
        on tlirl0308.SEG_NO = a.SEG_NO and tlirl0308.CAR_TRACE_NO = a.CAR_TRACE_NO and
        tlirl0308.VEHICLE_NO = a.VEHICLE_NO
        left join meli.tlirl0302 tlirl0302
        on tlirl0302.SEG_NO = a.SEG_NO and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO and
        tlirl0302.STATUS != '00'
        where 1 = 1
        and tlirl0308.PACK_ID !=' '
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        ) a1
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a1.CHECK_DATE desc,
                a1.CAR_TRACE_NO,
                a1.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>


    <select id="count"
            resultClass="int">
        select count(*) from
        (select a.SEG_NO                                          as       "segNo",
        a.SEG_NO,
        a.UNIT_CODE                                       as       "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO                                    as       "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS                                          as       "status",
        a.HAND_TYPE                                       as       "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO                                      as       "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD                                         as       "idCard",
        a.ID_CARD,
        a.DRIVER_NAME                                     as       "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM                                         as       "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER                              as       "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE                                      as       "checkDate",
        a.CHECK_DATE,
        ENTER_FACTORY                                     as       "enterFactory",
        ENTER_FACTORY,
        BEGIN_ENTRUCKING_TIME                             as       "beginEntruckingTime",
        BEGIN_ENTRUCKING_TIME,
        COMPLETE_UNINSTALL_TIME                           as       "completeUninstallTime",
        COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE                                as       "leaveFactoryDate",
        LEAVE_FACTORY_DATE,
        CUSTOMER_SIGNING_TIME                             as       "customerSigningTime",
        CUSTOMER_SIGNING_TIME,
        TARGET_HAND_POINT_ID                              as       "targetHandPointId",
        TARGET_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "targetHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "target_hand_point_name",
        CURRENT_HAND_POINT_ID                             as       "currentHandPointId",
        CURRENT_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "currentHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "current_hand_point_name",
        a.FACTORY_AREA                                    as       "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME                               as       "factoryAreaName",
        a.FACTORY_AREA_NAME,
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "nextTarget",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "next_target",
        UNLOAD_LEAVE_FLAG                                 as       "unloadLeaveFlag",
        UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR                                     as       "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME                                as       "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME                                 as       "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR                                     as       "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME                                as       "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME                                 as       "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG                                        as       "delFlag",
        a.DEL_FLAG,
        a.REMARK                                          as       "remark",
        a.SYS_REMARK                                      as       "sysRemark",
        a.SYS_REMARK,
        a.UUID                                            as       "uuid",
        tlirl0201.IS_RESERVATION                                   "isReservation",
        (select if(count(*) > 0, 1, 0)
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                   "whet_appointment",
        tlirl0201.START_OF_TRANSPORT                               "startOfTransport",
        tlirl0201.START_OF_TRANSPORT                               "start_of_transport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purposeOfTransport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purpose_of_transport",
        tlirl0201.CUSTOMER_NAME                                    "customerName",
        tlirl0201.CUSTOMER_NAME                                    "customer_name",
        tlirl0201.STATUS                                           "appointmentStatus",
        tlirl0201.STATUS                                           "appointment_status",
        tlirl0201.RESERVATION_DATE                                 "reservationDate",
        tlirl0201.RESERVATION_DATE                                 "reservation_date",
        tlirl0201.RESERVATION_TIME                                 "reservationTime",
        tlirl0201.RESERVATION_TIME                                 "reservation_time",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "lateEarlyFlag",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "late_early_flag",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "businessType",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "business_type",
        tlirl0302.VOUCHER_NUM                                      "voucherNum",
        tlirl0302.VOUCHER_NUM                                      "voucher_num",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "callDate",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "call_date",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromRegistrationToEntry",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_registration_to_entry",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "theTimeFromEnteringTheFactoryToTheStartOfTheJob",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "the_time_from_entering_the_factory_to_the_start_of_the_job",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromTheCompletionOfTheJobToTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_the_completion_of_the_job_to_the_factory",
        if(a.COMPLETE_UNINSTALL_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0, TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",
        if(COMPLETE_UNINSTALL_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "the_time_from_entering_the_factory_to_the_completion_of_the_job",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromEnteringTheFactoryToLeavingTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_entering_the_factory_to_leaving_the_factory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registeredToTheFactoryTime",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registered_to_the_factory_time",
        tlirl0308.SIGN_OFF_TIME                           as       "sign_off_time",
        tlirl0308.SIGN_OFF_TIME                           as       "signOffTime",
        CASE
        WHEN (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0 THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')) / 60), '时', MOD(
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')), 60), '分')
        WHEN a.LEAVE_FACTORY_DATE != '' and tlirl0308.SIGN_OFF_TIME != '' then '0时0分'
        ELSE '' END                                            "theTimeFromTheFactoryToTheTimeOfReceipt",
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')),
        0)                                                      "the_time_from_the_factory_to_the_time_of_receipt",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signing_location_longitude",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signingLocationLongitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signing_location_latitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signingLocationLatitude",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitude_latitude_check",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitudeLatitudeCheck",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "final_station_longitude",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "finalStationLongitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "final_station_latitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "finalStationLatitude",
        ifnull(tlirl0308.outWeight,
        0)                                         as       "outWeight",
        ifnull(tlirl0308.outQuantity,
        0)                                         as       "outQuantity",
        ifnull(tlirl0308.inWeight,
        0)                                         as       "inWeight",
        ifnull(tlirl0308.inQuantity, 0)                   as       "inQuantity",
        tlirl0308.SIGNING_ADDRESS AS "signingAddress",
        tlirl0308.SIGNING_ADDRESS AS "signing_address"
        from MELI.tlirl0301 a
        left join meli.tlirl0201 tlirl0201
        on tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.STATUS != '00'
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        left join (select tlirl0308.SEG_NO,
        tlirl0308.VEHICLE_NO,
        tlirl0308.CAR_TRACE_NO,
        max(tlirl03082.SIGNING_LOCATION_LONGITUDE) as "SIGNING_LOCATION_LONGITUDE",
        max(tlirl03082.SIGNING_LOCATION_LATITUDE) as "SIGNING_LOCATION_LATITUDE",
        max(tlirl03082.LONGITUDE_LATITUDE_CHECK) as "LONGITUDE_LATITUDE_CHECK",
        max(tlirl03082.FINAL_STATION_LONGITUDE) as "FINAL_STATION_LONGITUDE",
        max(tlirl03082.FINAL_STATION_LATITUDE) as "FINAL_STATION_LATITUDE",
        max(tlirl03082.SIGN_OFF_TIME) as "SIGN_OFF_TIME",
        max(tlirl03082.SIGNING_ADDRESS) as "SIGNING_ADDRESS",
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN tlirl03082.QUANTITY
        ELSE 0 END) AS outQuantity,
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN tlirl03082.QUANTITY
        ELSE 0 END) AS inQuantity,
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN tlirl03082.WEIGHT
        ELSE 0 END) AS outWeight,
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN tlirl03082.WEIGHT
        ELSE 0 END) AS inWeight
        from MELI.tlirl0308 tlirl0308
        left join meli.tlirl0308 tlirl03082 on
        tlirl03082.SEG_NO = tlirl0308.SEG_NO
        and tlirl03082.CAR_TRACE_NO = tlirl0308.CAR_TRACE_NO
        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO
        where tlirl0308.SEG_NO = #segNo#
        and tlirl0308.DEL_FLAG = '0'
        group by tlirl0308.SEG_NO, tlirl0308.VEHICLE_NO, tlirl0308.CAR_TRACE_NO) tlirl0308
        on
        tlirl0308.SEG_NO = a.SEG_NO
        and tlirl0308.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO = a.VEHICLE_NO
        left join meli.tlirl0302 tlirl0302
        on tlirl0302.SEG_NO = a.SEG_NO
        and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0302.STATUS != '00'
        where 1 = 1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        union
        select a.SEG_NO                                          as       "segNo",
        a.SEG_NO,
        a.UNIT_CODE                                       as       "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO                                    as       "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS                                          as       "status",
        a.HAND_TYPE                                       as       "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO                                      as       "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD                                         as       "idCard",
        a.ID_CARD,
        a.DRIVER_NAME                                     as       "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM                                         as       "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER                              as       "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE                                      as       "checkDate",
        a.CHECK_DATE,
        ENTER_FACTORY                                     as       "enterFactory",
        ENTER_FACTORY,
        BEGIN_ENTRUCKING_TIME                             as       "beginEntruckingTime",
        BEGIN_ENTRUCKING_TIME,
        COMPLETE_UNINSTALL_TIME                           as       "completeUninstallTime",
        COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE                                as       "leaveFactoryDate",
        LEAVE_FACTORY_DATE,
        CUSTOMER_SIGNING_TIME                             as       "customerSigningTime",
        CUSTOMER_SIGNING_TIME,
        TARGET_HAND_POINT_ID                              as       "targetHandPointId",
        TARGET_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "targetHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "target_hand_point_name",
        CURRENT_HAND_POINT_ID                             as       "currentHandPointId",
        CURRENT_HAND_POINT_ID,
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "currentHandPointName",
        (select tlirl0304.HAND_POINT_NAME
        from MELI.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = a.SEG_NO
        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                  as       "current_hand_point_name",
        a.FACTORY_AREA                                    as       "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME                               as       "factoryAreaName",
        a.FACTORY_AREA_NAME,
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "nextTarget",
        (select tlirl0407.NEXT_TATGET
        from MELI.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        order by tlirl0407.REC_CREATE_TIME desc
        limit 1)                                         as       "next_target",
        UNLOAD_LEAVE_FLAG                                 as       "unloadLeaveFlag",
        UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR                                     as       "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME                                as       "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME                                 as       "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR                                     as       "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME                                as       "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME                                 as       "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG                                        as       "delFlag",
        a.DEL_FLAG,
        a.REMARK                                          as       "remark",
        a.SYS_REMARK                                      as       "sysRemark",
        a.SYS_REMARK,
        a.UUID                                            as       "uuid",
        tlirl0201.IS_RESERVATION                                   "isReservation",
        (select if(count(*) > 0, 1, 0)
        from MELI.tlirl0201 b
        where 1 = 1
        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and b.DEL_FLAG = '0')                                   "whet_appointment",
        tlirl0201.START_OF_TRANSPORT                               "startOfTransport",
        tlirl0201.START_OF_TRANSPORT                               "start_of_transport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purposeOfTransport",
        tlirl0201.PURPOSE_OF_TRANSPORT                             "purpose_of_transport",
        tlirl0201.CUSTOMER_NAME                                    "customerName",
        tlirl0201.CUSTOMER_NAME                                    "customer_name",
        tlirl0201.STATUS                                           "appointmentStatus",
        tlirl0201.STATUS                                           "appointment_status",
        tlirl0201.RESERVATION_DATE                                 "reservationDate",
        tlirl0201.RESERVATION_DATE                                 "reservation_date",
        tlirl0201.RESERVATION_TIME                                 "reservationTime",
        tlirl0201.RESERVATION_TIME                                 "reservation_time",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "lateEarlyFlag",
        ifnull(tlirl0302.LATE_EARLY_FLAG,
        0)                                                  "late_early_flag",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "businessType",
        (case
        when tlirl0302.VOUCHER_NUM = ' ' then case
        when tlirl0302.BUSINESS_TYPE = '10'
        then '20'
        when tlirl0302.BUSINESS_TYPE = '20'
        then '60'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '40'
        when tlirl0302.BUSINESS_TYPE = '40'
        then '50'
        when tlirl0302.BUSINESS_TYPE = '50'
        then '70'
        when tlirl0302.BUSINESS_TYPE = '60'
        then '80' end
        else case
        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null
        THEN case when a.HAND_TYPE = '10' then '10' when a.HAND_TYPE = '30' then '30' end
        else case
        when tlirl0302.BUSINESS_TYPE = '10' then '10'
        when tlirl0302.BUSINESS_TYPE = '30'
        then '30' end end end) as       "business_type",
        tlirl0302.VOUCHER_NUM                                      "voucherNum",
        tlirl0302.VOUCHER_NUM                                      "voucher_num",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "callDate",
        ifnull((select QUEUE_DATE
        from MELI.tlirl0402 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0'), (select QUEUE_DATE
        from MELI.tlirl0409 b
        where 1 = 1
        and b.CAR_TRACE_NO = a.CAR_TRACE_NO
        and b.DEL_FLAG = '0')) "call_date",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromRegistrationToEntry",
        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_registration_to_entry",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "theTimeFromEnteringTheFactoryToTheStartOfTheJob",
        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s')),
        0),
        '')                                                     "the_time_from_entering_the_factory_to_the_start_of_the_job",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",
        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromTheCompletionOfTheJobToTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_the_completion_of_the_job_to_the_factory",
        if(a.COMPLETE_UNINSTALL_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0, TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",
        if(COMPLETE_UNINSTALL_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),
        0),
        "")                                                     "the_time_from_entering_the_factory_to_the_completion_of_the_job",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "theTimeFromEnteringTheFactoryToLeavingTheFactory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "the_time_from_entering_the_factory_to_leaving_the_factory",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registeredToTheFactoryTime",
        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),
        '')                                                     "registered_to_the_factory_time",
        tlirl0308.SIGN_OFF_TIME                           as       "sign_off_time",
        tlirl0308.SIGN_OFF_TIME                           as       "signOffTime",
        CASE
        WHEN (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0 THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')) / 60), '时', MOD(
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')), 60), '分')
        WHEN a.LEAVE_FACTORY_DATE != '' and tlirl0308.SIGN_OFF_TIME != '' then '0时0分'
        ELSE '' END                                            "theTimeFromTheFactoryToTheTimeOfReceipt",
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')),
        0)                                                      "the_time_from_the_factory_to_the_time_of_receipt",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signing_location_longitude",
        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signingLocationLongitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signing_location_latitude",
        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signingLocationLatitude",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitude_latitude_check",
        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitudeLatitudeCheck",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "final_station_longitude",
        tlirl0308.FINAL_STATION_LONGITUDE                 as       "finalStationLongitude",
        tlirl0308.FINAL_STATION_LATITUDE
        as       "final_station_latitude",
        tlirl0308.FINAL_STATION_LATITUDE                  as       "finalStationLatitude",
        ifnull(tlirl0308.outWeight,
        0)                                         as       "outWeight",
        ifnull(tlirl0308.outQuantity,
        0)                                         as       "outQuantity",
        ifnull(tlirl0308.inWeight,
        0)                                         as       "inWeight",
        ifnull(tlirl0308.inQuantity, 0)                   as       "inQuantity",
        tlirl0308.SIGNING_ADDRESS AS "signingAddress",
        tlirl0308.SIGNING_ADDRESS AS "signing_address"
        from MELI.tlirl0311 a
        left join meli.tlirl0201 tlirl0201
        on tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.STATUS != '00'
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        left join (select tlirl0308.SEG_NO,
        tlirl0308.VEHICLE_NO,
        tlirl0308.CAR_TRACE_NO,
        max(tlirl03082.SIGNING_LOCATION_LONGITUDE) as "SIGNING_LOCATION_LONGITUDE",
        max(tlirl03082.SIGNING_LOCATION_LATITUDE) as "SIGNING_LOCATION_LATITUDE",
        max(tlirl03082.LONGITUDE_LATITUDE_CHECK) as "LONGITUDE_LATITUDE_CHECK",
        max(tlirl03082.FINAL_STATION_LONGITUDE) as "FINAL_STATION_LONGITUDE",
        max(tlirl03082.FINAL_STATION_LATITUDE) as "FINAL_STATION_LATITUDE",
        max(tlirl03082.SIGN_OFF_TIME) as "SIGN_OFF_TIME",
        max(tlirl03082.SIGNING_ADDRESS) as "SIGNING_ADDRESS",
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN tlirl03082.QUANTITY
        ELSE 0 END) AS outQuantity,
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN tlirl03082.QUANTITY
        ELSE 0 END) AS inQuantity,
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN tlirl03082.WEIGHT
        ELSE 0 END) AS outWeight,
        SUM(CASE
        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN tlirl03082.WEIGHT
        ELSE 0 END) AS inWeight
        from MELI.tlirl0308 tlirl0308
        left join meli.tlirl0308 tlirl03082 on
        tlirl03082.SEG_NO = tlirl0308.SEG_NO
        and tlirl03082.CAR_TRACE_NO = tlirl0308.CAR_TRACE_NO
        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO
        where tlirl0308.SEG_NO = #segNo#
        and tlirl0308.DEL_FLAG = '0'
        group by tlirl0308.SEG_NO, tlirl0308.VEHICLE_NO, tlirl0308.CAR_TRACE_NO) tlirl0308
        on
        tlirl0308.SEG_NO = a.SEG_NO
        and tlirl0308.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO = a.VEHICLE_NO
        left join meli.tlirl0302 tlirl0302
        on tlirl0302.SEG_NO = a.SEG_NO
        and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0302.STATUS != '00'
        where 1 = 1
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        ) a1
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a1.CHECK_DATE desc,
                a1.CAR_TRACE_NO,
                a1.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>


    </select>


</sqlMap>