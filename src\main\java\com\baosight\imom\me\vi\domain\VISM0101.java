/**
* Generate time : 2024-10-16 14:41:55
* Version : 1.0
*/
package com.baosight.imom.me.vi.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tvism0101
* 
*/
public class VISM0101 extends DaoEPBase {
    public static final String QUERY = "VISM0101.query";
    public static final String QUERY_SWITCH_VALUE = "VISM0101.querySwitchValue";
    public static final String QUERY_SWITCH_VALUE_QUEUE_CALL = "VISM0101.querySwitchValueQueueCall";
    public static final String COUNT = "VISM0101.count";
    public static final String INSERT = "VISM0101.insert";
    public static final String UPDATE = "VISM0101.update";
    public static final String DELETE = "VISM0101.delete";
    public static final String UPDATE_STATUS = "VISM0101.updateStatus";
                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String uuid = " ";		
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String tenantUser = " ";		/* 租户*/
                private String processSwitchName = " ";		/* 生产开关名称（见名知义）*/
                private String processSwitchDesc = " ";		/* 生产开关描述*/
                private String processSwitchValue = " ";		/* 生产开关值（0:关;1:开;）*/
                private String processSwitchValueDesc = " ";		/* 生产开关值描述（0:关;1:开;）*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSwitchName");
        eiColumn.setDescName("生产开关名称（见名知义）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSwitchDesc");
        eiColumn.setDescName("生产开关描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSwitchValue");
        eiColumn.setDescName("生产开关值（0:关;1:开;）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSwitchValueDesc");
        eiColumn.setDescName("生产开关值描述（0:关;1:开;）");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public VISM0101() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the uuid 
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid 
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the processSwitchName - 生产开关名称（见名知义）
        * @return the processSwitchName
        */
        public String getProcessSwitchName() {
        return this.processSwitchName;
        }

        /**
        * set the processSwitchName - 生产开关名称（见名知义）
        */
        public void setProcessSwitchName(String processSwitchName) {
        this.processSwitchName = processSwitchName;
        }
        /**
        * get the processSwitchDesc - 生产开关描述
        * @return the processSwitchDesc
        */
        public String getProcessSwitchDesc() {
        return this.processSwitchDesc;
        }

        /**
        * set the processSwitchDesc - 生产开关描述
        */
        public void setProcessSwitchDesc(String processSwitchDesc) {
        this.processSwitchDesc = processSwitchDesc;
        }
        /**
        * get the processSwitchValue - 生产开关值（0:关;1:开;）
        * @return the processSwitchValue
        */
        public String getProcessSwitchValue() {
        return this.processSwitchValue;
        }

        /**
        * set the processSwitchValue - 生产开关值（0:关;1:开;）
        */
        public void setProcessSwitchValue(String processSwitchValue) {
        this.processSwitchValue = processSwitchValue;
        }
        /**
        * get the processSwitchValueDesc - 生产开关值描述（0:关;1:开;）
        * @return the processSwitchValueDesc
        */
        public String getProcessSwitchValueDesc() {
        return this.processSwitchValueDesc;
        }

        /**
        * set the processSwitchValueDesc - 生产开关值描述（0:关;1:开;）
        */
        public void setProcessSwitchValueDesc(String processSwitchValueDesc) {
        this.processSwitchValueDesc = processSwitchValueDesc;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setProcessSwitchName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSwitchName")), processSwitchName));
                setProcessSwitchDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSwitchDesc")), processSwitchDesc));
                setProcessSwitchValue(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSwitchValue")), processSwitchValue));
                setProcessSwitchValueDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSwitchValueDesc")), processSwitchValueDesc));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("processSwitchName",StringUtils.toString(processSwitchName, eiMetadata.getMeta("processSwitchName")));
                map.put("processSwitchDesc",StringUtils.toString(processSwitchDesc, eiMetadata.getMeta("processSwitchDesc")));
                map.put("processSwitchValue",StringUtils.toString(processSwitchValue, eiMetadata.getMeta("processSwitchValue")));
                map.put("processSwitchValueDesc",StringUtils.toString(processSwitchValueDesc, eiMetadata.getMeta("processSwitchValueDesc")));

return map;

}
}