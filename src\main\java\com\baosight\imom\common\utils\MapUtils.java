package com.baosight.imom.common.utils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 这里填写描述
 * 填写修改说明
 *
 * <AUTHOR>
 * @version Revision:v1.0
 * @since Date:2022-08-17 14:27
 */
public class MapUtils {

    /**
     * 获取字符串, 默认空 ""
     */
    public static String getStr(final Map map, final Object key) {
        String r = getString(map, key);
        if (r == null) {
            return "";
        }
        return r;
    }

    /**
     * 获取 bool 值
     * */
    public static boolean getBoolean(final Map map, final Object key){
        String val = getString(map, key);
        if ("true".equals(val) || "1".equals(val)){
            return true;
        }
        return false;
    }

    /**
     * 从 Map 中获取字符串
     */
    public static String getString(final Map map, final Object key) {
        return getString(map, key, null);
    }

    public static String getString(final Map map, final Object key, String defaultValue) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                return answer.toString();
            }
        }
        return defaultValue;
    }

    /**
     * 获取 int 整数, 默认 0
     * */
    public static int getInt(final Map map, final Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                return Integer.parseInt(answer.toString());
            }
        }
        return 0;
    }

    /** 获取日期类型 */
    public static Date getDate(final Map map, final Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                return (Date) answer;
            }
        }
        return null;
    }


    public static <K, V> BigDecimal bigDecimal(Map<K, V> map, K key) {
        return getBigDecimal(map, key, BigDecimal.ZERO);
    }

    public static <K, V> BigDecimal getBigDecimal(Map<K, V> map, K key) {
        return getBigDecimal(map, key, null);
    }

    /**
     * 获取 BigDecimal 类型数据
     */
    public static <K, V> BigDecimal getBigDecimal(Map<K, V> map, K key, BigDecimal defVal) {
        Object obj = get(map, key);
        if (null == obj) {
            return defVal;
        }
        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }
        return new BigDecimal(obj.toString());
    }

    /**
     * 根据 key 获取 value
     */
    public static <K, V> Object get(Map<K, V> map, K key) {
        if (null == map) {
            return null;
        }
        return map.get(key);
    }

    /**
     * 合计集合中 BigDecimal 数据的值
     *
     * @param mapList list< map{key: bigDecimal}>
     * @param key     要合计的 key
     * @return BigDecimal 合计值
     */
    @SuppressWarnings("all")
    public static BigDecimal sumBigDecimal(List<Map> mapList, String key) {
        if (mapList == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal summary = BigDecimal.ZERO;
        for (Map map : mapList) {
            summary = summary.add(getBigDecimal(map, key, BigDecimal.ZERO));
        }
        return summary;
    }

    public static boolean isEmpty(Map map) {
        return (map == null || map.isEmpty());
    }

    public static boolean isNotEmpty(Map map) {
        return !org.apache.commons.collections.MapUtils.isEmpty(map);
    }

    /**
     * 删除全是空的数据
     * */
    public static  <K, V> void removeEmptyMap(List<Map<K, V>> mapList){
        Iterator<Map<K, V>> iterator = mapList.iterator();
        while (iterator.hasNext()) {
            Map<?, ?> next = iterator.next();
            boolean validated = false;
            for (Object value : next.values()) {
                if (value != null && !"".equals(value)){
                    validated = true;
                    break;
                }
            }
            if (!validated){
                iterator.remove();
            }
        }
    }

    /**
     * 将一个 map key 映射到新 key
     * */
    public static  <K, V> List<Map<K, V>> mapToList(List<Map<String, Object>> mapList, Map<String, String> colMapping){
        List<Map<K, V>> newMapList = new ArrayList<>(mapList.size());
        for (Map<String, Object> map : mapList) {
            Map<K, V> rec = new HashMap<>();
            colMapping.forEach((fromProp, toProp) -> {
                rec.put( (K) toProp, (V) map.get(fromProp) );
            });
            newMapList.add(rec);
        }
        return newMapList;
    }
    /**
     * 去除空格
     * @param str
     * @param defaultString
     * @return
     */
    public static String defaultIfEmpty(String str, String defaultString) {
        if (str == null) {
            return defaultString;
        } else {
            return " ".equals(str)? "" : str;
        }
    }

    public static List<Map> defaultIfEmpty(List<Map> maps) {
        maps = maps.stream().map(map1 -> defaultIfEmpty(map1)).collect(Collectors.toList());
        return maps;
    }

    public static Map defaultIfEmpty(Map map) {
        for (Object key:map.keySet()) {
            Object e = map.get(key);
            if(e!=null && e.equals(" ")){
                map.put(key,"");
            }
        }
        return map;
    }

    /**
     * 获取BigDecimal lq
     * @param o
     * @return
     */
    public static BigDecimal getBigDecimal(Object o){
        if(o == null || "".equals(o)){
            return BigDecimal.ZERO;
        }
        return new BigDecimal(String.valueOf(o));
    }

}
