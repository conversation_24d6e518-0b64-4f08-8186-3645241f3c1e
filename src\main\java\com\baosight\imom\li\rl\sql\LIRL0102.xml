<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-14 8:58:23
   		Version :  1.0
		tableName :${meliSchema}.LIRL0102 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 STATUS  VARCHAR   NOT NULL, 
		 DRIVER_NAME  VARCHAR   NOT NULL,
		 DRIVER_IDENTITY  VARCHAR   NOT NULL, 
		 CUSTOMER_ID  VARCHAR   NOT NULL, 
		 CUSTOMER_NAME  VARCHAR   NOT NULL, 
		 TEL  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0102">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="java.util.HashMap">
		SELECT
		tlirl0102.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
		tlirl0102.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0102.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0102.UUID as "pageId",  <!-- uuid -->
		tlirl0102.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
		tlirl0102.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
		tlirl0102.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
		tlirl0102.RESERVATION_IDENTITY as "reservationIdentity",  <!-- 司机身份证号 -->
		tlirl0102.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
		tlirl0102.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
		tlirl0102.TEL as "tel",  <!-- 司机手机号 -->
		tlirl0102.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		tlirl0102.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0102.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0102.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0102.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0102.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0102.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0102.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0102.REMARK as "remark",  <!-- 备注 -->
		tlirl0102.TENANT_ID as "tenantId", <!-- 租户ID -->
		tlirl0103.VEHICLE_NO as "vehicleNo" <!--车牌号-->
		FROM ${meliSchema}.TLIRL0102 tlirl0102
		left join ${meliSchema}.TLIRL0103 tlirl0103 on tlirl0102.SEG_NO = tlirl0103.SEG_NO
		and tlirl0102.UUID = tlirl0103.M_UUID AND tlirl0103.DEL_FLAG = '0'
		WHERE 1=1
		<isNotEmpty prepend=" AND " property="status">
			tlirl0102.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			tlirl0102.DEL_FLAG =#delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0102.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			tlirl0102.DRIVER_NAME like concat('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId2">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId2#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName2">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName2#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			tlirl0103.VEHICLE_NO like concat('%',#vehicleNo#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			tlirl0102.DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			tlirl0102.UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
<!--         <isNotEmpty property="orderBy">-->
<!--    		  $orderBy$-->
<!--   		 </isNotEmpty>-->
   		<isEmpty property="orderBy">
			tlirl0102.REC_CREATE_TIME desc,tlirl0102.REC_REVISE_TIME desc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="subQuery" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		tlirl0102.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
		tlirl0102.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0102.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0102.UUID as "pageId",  <!-- uuid -->
		tlirl0102.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
		tlirl0102.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
		tlirl0102.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
		tlirl0102.RESERVATION_IDENTITY as "reservationIdentity",  <!-- 司机身份证号 -->
		tlirl0102.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
		tlirl0102.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
		tlirl0102.TEL as "tel",  <!-- 司机手机号 -->
		tlirl0102.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		tlirl0102.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0102.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0102.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0102.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0102.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0102.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0102.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0102.REMARK as "remark",  <!-- 备注 -->
		tlirl0102.TENANT_ID as "tenantId"
		FROM ${meliSchema}.TLIRL0102 tlirl0102
		WHERE 1=1
		and tlirl0102.STATUS !='00'
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0102.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0102.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			tlirl0102.DRIVER_NAME like concat('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			tlirl0102.UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity1">
			tlirl0102.DRIVER_IDENTITY = #driverIdentity1#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="flag">
			exists(select 1
			from meli.tlirl0101 tlirl0101
			where tlirl0102.SEG_NO = tlirl0101.SEG_NO
			and tlirl0102.CUSTOMER_ID = tlirl0101.CUSTOMER_ID
			and tlirl0101.TEL = #adminTel#
			and tlirl0101.STATUS = '20'
			limit 1)
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<!--         <isNotEmpty property="orderBy">-->
			<!--    		  $orderBy$-->
			<!--   		 </isNotEmpty>-->
			<isEmpty property="orderBy">
				tlirl0102.REC_CREATE_TIME asc
			</isEmpty>
		</dynamic>

	</select>


	<select id="subQueryAll" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		tlirl0102.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
		tlirl0102.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0102.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0102.UUID as "pageId",  <!-- uuid -->
		tlirl0102.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
		tlirl0102.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
		tlirl0102.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
		tlirl0102.RESERVATION_IDENTITY as "reservationIdentity",  <!-- 司机身份证号 -->
		tlirl0102.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
		tlirl0102.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
		tlirl0102.TEL as "tel",  <!-- 司机手机号 -->
		GROUP_CONCAT(TLIRL0103.VEHICLE_NO ORDER BY TLIRL0103.VEHICLE_NO SEPARATOR ', ')         AS "vehicleNo",
		tlirl0102.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		tlirl0102.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0102.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0102.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0102.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0102.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0102.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0102.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0102.REMARK as "remark",  <!-- 备注 -->
		tlirl0102.TENANT_ID as "tenantId"
		FROM meli.TLIRL0102 tlirl0102
		LEFT JOIN
		meli.TLIRL0103 TLIRL0103 ON tlirl0103.SEG_NO = tlirl0102.SEG_NO
		and tlirl0103.M_UUID = tlirl0102.UUID
		and tlirl0103.STATUS=tlirl0102.STATUS
		WHERE 1=1
		AND tlirl0102.SEG_NO =#segNo#
		<isNotEmpty prepend=" AND " property="status">
			tlirl0102.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			tlirl0102.DEL_FLAG =#delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			tlirl0102.DRIVER_NAME like concat('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId2">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId2#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName2">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName2#,'%')
		</isNotEmpty>
<!--		<isNotEmpty prepend=" AND " property="vehicleNo">-->
<!--			tlirl0103.VEHICLE_NO like concat('%',#vehicleNo#,'%')-->
<!--		</isNotEmpty>-->
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL like concat('%',#tel#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			tlirl0102.DRIVER_IDENTITY like concat('%',#driverIdentity#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			tlirl0102.UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			exists(select 1 from ${meliSchema}.TLIRL0103 tlirl0103 where  tlirl0103.SEG_NO = tlirl0102.SEG_NO and tlirl0103.M_UUID = tlirl0102.UUID and
			tlirl0103.VEHICLE_NO like concat('%',#vehicleNo#,'%'))
		</isNotEmpty>
		GROUP BY tlirl0102.UUID
		<dynamic prepend="ORDER BY">
			<!--         <isNotEmpty property="orderBy">-->
			<!--    		  $orderBy$-->
			<!--   		 </isNotEmpty>-->
			<isEmpty property="orderBy">
				tlirl0102.REC_CREATE_TIME desc,tlirl0102.REC_REVISE_TIME desc
			</isEmpty>
		</dynamic>

	</select>


	<select id="subQueryAllExport" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		tlirl0102.UNIT_CODE                                                                     as "segNo",
		(select SEG_NAME from iplat4j.TVZBM81 where SEG_NO = tlirl0102.SEG_NO and DEL_FLAG = 0) as "segName",
		tlirl0102.UUID                                                                          AS "pageId",
		tlirl0102.STATUS                                                                        as "status",
		tlirl0102.CUSTOMER_ID                                                                   as "customerId",
		tlirl0102.CUSTOMER_NAME                                                                 as "customerName",
		tlirl0102.DRIVER_NAME                                                                   as "driverName",
		tlirl0102.TEL                                                                           as "tel",
		tlirl0102.DRIVER_IDENTITY                                                               as "driverIdentity",
		tlirl0102.REMARK                                                                        as "remark",
		tlirl0102.REC_CREATOR                                                                   as "recCreator",
		tlirl0102.REC_CREATOR_NAME                                                              as "recCreatorName",
		tlirl0102.REC_CREATE_TIME                                                               as "recCreateTime",
		tlirl0102.REC_REVISOR                                                                   as "recRevisor",
		tlirl0102.REC_REVISOR_NAME                                                              as "recRevisorName",
		tlirl0102.REC_REVISE_TIME                                                               as "recReviseTime",
		GROUP_CONCAT(TLIRL0103.VEHICLE_NO ORDER BY TLIRL0103.VEHICLE_NO SEPARATOR ', ')         AS "vehicleNo"
		FROM meli.TLIRL0102 tlirl0102
		LEFT JOIN
		meli.TLIRL0103 TLIRL0103 ON tlirl0103.SEG_NO = tlirl0102.SEG_NO
		and tlirl0103.M_UUID = tlirl0102.UUID
		and tlirl0103.STATUS=tlirl0102.STATUS
		WHERE 1=1
		AND tlirl0102.SEG_NO =#segNo#
		<isNotEmpty prepend=" AND " property="status">
			tlirl0102.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			tlirl0102.DEL_FLAG =#delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			tlirl0102.DRIVER_NAME like concat('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId2">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId2#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName2">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName2#,'%')
		</isNotEmpty>
		<!--		<isNotEmpty prepend=" AND " property="vehicleNo">-->
		<!--			tlirl0103.VEHICLE_NO like concat('%',#vehicleNo#,'%')-->
		<!--		</isNotEmpty>-->
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL like concat('%',#tel#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			tlirl0102.DRIVER_IDENTITY like concat('%',#driverIdentity#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			tlirl0102.UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			exists(select 1 from ${meliSchema}.TLIRL0103 tlirl0103 where  tlirl0103.SEG_NO = tlirl0102.SEG_NO and tlirl0103.M_UUID = tlirl0102.UUID and
			tlirl0103.VEHICLE_NO like concat('%',#vehicleNo#,'%'))
		</isNotEmpty>
		GROUP BY tlirl0102.UUID
		<dynamic prepend="ORDER BY">
			<!--         <isNotEmpty property="orderBy">-->
			<!--    		  $orderBy$-->
			<!--   		 </isNotEmpty>-->
			<isEmpty property="orderBy">
				tlirl0102.REC_CREATE_TIME desc,tlirl0102.REC_REVISE_TIME desc
			</isEmpty>
		</dynamic>

	</select>

	<select id="subQueryUuid"
			resultClass="String">
		SELECT
		tlirl0102.UUID as "uuid" <!-- uuid -->
		FROM ${meliSchema}.TLIRL0102 tlirl0102
		WHERE 1=1
		and tlirl0102.STATUS !='00'
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0102.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0102.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			tlirl0102.DRIVER_NAME like concat('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			tlirl0102.UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<!--         <isNotEmpty property="orderBy">-->
			<!--    		  $orderBy$-->
			<!--   		 </isNotEmpty>-->
			<isEmpty property="orderBy">
				tlirl0102.REC_CREATE_TIME asc
			</isEmpty>
		</dynamic>

	</select>


	<select id="subQueryL"
			resultClass="String">
		SELECT TLIRL0201.VEHICLE_NO as "vehicleNo"
		FROM MELI.TLIRL0201 tlirl0201
		WHERE 1 = 1
		and TLIRL0201.STATUS != '00'
		AND TLIRL0201.SEG_NO = #segNo#
		AND TLIRL0201.DRIVER_TEL = #tel#
		and TLIRL0201.STATUS = '20'

	</select>


	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.TLIRL0102 WHERE 1=1
		<isNotEmpty prepend=" AND " property="status">
			tlirl0102.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0102.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			tlirl0102.DRIVER_NAME like concat('%',#driverName$#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			tlirl0102.CUSTOMER_NAME like concat('%',#customerName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			tlirl0103.VEHICLE_NO like concat('%',#vehicleNo#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			tlirl0102.DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			tlirl0102.UUID != #notUuid#
		</isNotEmpty>
	</select>


	<select id="countDriverInfo" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.TLIRL0102 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0102.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			tlirl0102.CUSTOMER_ID like concat('%',#customerId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			tlirl0102.TEL = #tel#
		</isNotEmpty>
		and Status !='00'
	</select>

	<select id="querytvzbm81" resultClass="int">
		select count(1) from iplat4j.TVZBM81 where 1=1
		and SEG_NO = #segNo#
	</select>
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.TLIRL0102 (SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										UUID,  <!-- uuid -->
										STATUS,  <!-- 状态(撤销：00、新增：10、生效：20) -->
										DRIVER_NAME,  <!-- 司机姓名 -->
										DRIVER_IDENTITY,  <!-- 司机身份证号 -->
										CUSTOMER_ID,  <!-- 承运商/客户代码 -->
										CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
										TEL,  <!-- 司机手机号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #uuid#, #status#, #driverName#, #driverIdentity#, #customerId#, #customerName#, #tel#, #recCreator#,#recCreatorName#, #recCreateTime#, #recRevisor#,#recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #tenantId#)
	</insert>
  
	<delete id="delete">
		UPDATE ${meliSchema}.TLIRL0102
		SET
		STATUS	= '00',   <!-- 状态(撤销：00、新增：10、生效：20) -->
		DEL_FLAG	= '1',   <!-- 记录删除标记 -->
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		UUID = #pageId#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.TLIRL0102
		SET
			SEG_NO = #segNo#
		<isNotEmpty prepend=" , " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="driverIdentity">
			DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="tel">
			TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
			WHERE 1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend=" and " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="pageId">
			UUID = #pageId#
		</isNotEmpty>
	</update>


	<select id="queryByTel" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		tlirl0102.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
		tlirl0102.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0102.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0102.UUID as "pageId",  <!-- uuid -->
		tlirl0102.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
		tlirl0102.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
		tlirl0102.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
		tlirl0102.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
		tlirl0102.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
		tlirl0102.TEL as "tel",  <!-- 司机手机号 -->
		tlirl0102.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		tlirl0102.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0102.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0102.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0102.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0102.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0102.REMARK as "remark",  <!-- 备注 -->
		tlirl0102.TENANT_ID as "tenantId", <!-- 租户ID -->
		tlirl0103.VEHICLE_NO as "vehicleNo" <!--车牌号-->
		FROM ${meliSchema}.TLIRL0102 tlirl0102
		left join ${meliSchema}.TLIRL0103 tlirl0103 on tlirl0102.SEG_NO = tlirl0103.SEG_NO
		and tlirl0102.UUID = tlirl0103.M_UUID AND tlirl0103.DEL_FLAG = '0'
		WHERE 1=1
		AND tlirl0102.SEG_NO = #segNo#
		AND tlirl0102.STATUS = '10'
		AND tlirl0102.DRIVER_NAME = #driverName#
		AND tlirl0102.TEL = #driverTel#
		AND tlirl0102.DEL_FLAG = '0'
	</select>

	<select id="queryAdminByDriverInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select DISTINCT tlirl0102.DRIVER_NAME as "driverName",tlirl0102.TEL as "tel",
		tlirl0102.DRIVER_IDENTITY as "driverIdentity",
		tlirl0102.UUID AS "uuid",
		tlirl0102.STATUS AS "status"
		from ${meliSchema}.tlirl0101 tlirl0101,
		${meliSchema}.tlirl0102 tlirl0102
		where tlirl0101.SEG_NO = tlirl0102.SEG_NO
		and tlirl0101.CUSTOMER_ID = tlirl0102.CUSTOMER_ID
		and tlirl0102.STATUS != '00'
		and tlirl0101.SEG_NO = #segNo#
		AND tlirl0101.CUSTOMER_ID = #customerId#
		and tlirl0101.STATUS = '20'
		<isNotEmpty prepend=" and " property="driverName">
			tlirl0102.DRIVER_NAME like concat ('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="driverTel">
			tlirl0102.TEL like concat ('%',#driverTel#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="tel">
			tlirl0101.TEL =#tel#
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="reservationIdentity">
			tlirl0101.RESERVATION_IDENTITY = #reservationIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="driverIdentity">
			tlirl0102.DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="eStatus">
			tlirl0102.STATUS = '20'
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="vehicleNoAll">
			exists(
			select 1 from meli.tlirl0103  tlirl0103
			where 1=1 and tlirl0103.SEG_NO=tlirl0102.SEG_NO
			and tlirl0103.M_UUID=tlirl0102.UUID
			and tlirl0103.VEHICLE_NO like concat ('%',#vehicleNoAll#,'%')
			)
		</isNotEmpty>
		order by tlirl0102.STATUS asc
	</select>
<!--	查询生效的预约司机信息-->
	<select id="queryAdminByRevesionInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select DISTINCT tlirl0102.DRIVER_NAME as "driverName",tlirl0102.TEL as "tel",
		tlirl0102.DRIVER_IDENTITY as "driverIdentity",
		tlirl0102.UUID AS "uuid",
		tlirl0102.STATUS AS "status"
		from ${meliSchema}.tlirl0101 tlirl0101,
		${meliSchema}.tlirl0102 tlirl0102
		where tlirl0101.SEG_NO = tlirl0102.SEG_NO
		and tlirl0101.CUSTOMER_ID = tlirl0102.CUSTOMER_ID
		and tlirl0102.STATUS = '20'
		and tlirl0101.SEG_NO = #segNo#
		AND tlirl0101.CUSTOMER_ID = #customerId#
		and tlirl0101.STATUS = '20'
		<isNotEmpty prepend=" and " property="driverName">
			tlirl0102.DRIVER_NAME like concat ('%',#driverName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="driverTel">
			tlirl0102.TEL like concat ('%',#driverTel#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="tel">
			tlirl0101.TEL =#tel#
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="reservationIdentity">
			tlirl0101.RESERVATION_IDENTITY = #reservationIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" and " property="driverIdentity">
			tlirl0102.DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
	</select>
</sqlMap>