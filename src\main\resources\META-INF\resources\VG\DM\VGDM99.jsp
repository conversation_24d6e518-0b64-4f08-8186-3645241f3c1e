<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-tagId" cname="点位ID" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-startTime" role="datetime"
                           endName="inqu_status-0-endTime" interval="5"
                           startCname="采集时间(起)" endCname="采集时间(止)"
                           ratio="3:3" format="yyyy-MM-dd HH:mm:ss">
            </EF:EFDateSpan>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="detail" title="数据采集监控">
        <div class="row">
            <EF:EFInput ename="detail_status-0-clientStatus" cname="数据采集状态" colWidth="3" disabled="true"/>
            <label style="color: red;">数据会持续推送，监听不要长时间开启</label>
        </div>
        <div class="row">
            <EF:EFInput ename="detail_status-0-monitorStr" cname="监控点" colWidth="12" ratio="1:11" disabled="true"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" alias="b.unit_code"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center" alias="b.e_archives_no"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称" alias="b.equipment_name"/>
            <EF:EFColumn ename="tagId" cname="点位ID" alias="a.tag_id"/>
            <EF:EFColumn ename="tagDesc" cname="点位描述" alias="b.tag_desc"/>
            <EF:EFColumn ename="tagValue" cname="点位值"/>
            <EF:EFColumn ename="tagTime" cname="点位时间" width="170" align="center"/>
            <EF:EFColumn ename="tagTimeStr" cname="点位时间" width="170" align="center"/>
            <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100" alias="a.rec_creator"/>
            <EF:EFColumn ename="recCreateTime" editType="datetime" width="140" alias="a.rec_create_time"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0301" id="tagInfo" width="90%" height="60%"/>
</EF:EFPage>