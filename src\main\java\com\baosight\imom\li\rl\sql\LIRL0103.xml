<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-14 8:58:23
   		Version :  1.0
		tableName :${meliSchema}.TLIRL0103
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 M_UUID  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMAR<PERSON>  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0103">

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0103">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				M_UUID	as "m_uuid",  <!-- 主表UUID -->
				STATUS	as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				UUID	as "uuid",  <!-- UUID -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.TLIRL0103 WHERE 1=1
		and SEG_NO=#segNo#
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_uuid">
			M_UUID = #m_uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusDel">
			STATUS !='00'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statuss">
			STATUS =#statuss#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			REC_CREATE_TIME desc
		</isEmpty>
  		</dynamic>

	</select>


	<select id="subQuery" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT

		VEHICLE_NO	as "vehicleNo",<!-- 车牌号 -->
		"STATUS" AS "status"<!--状态-->

		FROM ${meliSchema}.TLIRL0103 WHERE 1=1
		and status in ('10','20')
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_uuid">
			M_UUID = #m_uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				UUID asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.TLIRL0103 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="arrList">
			VEHICLE_NO IN
			<iterate open="(" close=")" conjunction="," property="arrList">
				#arrList[]#
			</iterate>
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_uuid">
			M_UUID = #m_uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.TLIRL0103 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										M_UUID,  <!-- 主表UUID -->
										STATUS,  <!-- 状态(撤销：00、新增：10、生效：20) -->
										VEHICLE_NO,  <!-- 车牌号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										UUID,  <!-- UUID -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #m_uuid#, #status#, #vehicleNo#, #recCreator#,#recCreatorName#, #recCreateTime#, #recRevisor#,#recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #uuid#, #tenantId#)
	</insert>
  
	<delete id="delete">
		UPDATE ${meliSchema}.TLIRL0103
		SET
		STATUS	= '00',  <!-- 状态(撤销：00、新增：10、生效：20) -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= '1'   <!-- 记录删除标记 -->
		WHERE
		SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_uuid">
			M_UUID = #m_uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.TLIRL0103
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					M_UUID	= #m_uuid#,   <!-- 主表UUID -->  
					STATUS	= #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
								TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>


	<update id="updateVehicleNoAll">
		UPDATE ${meliSchema}.TLIRL0103
		SET
		SEG_NO	= #segNo#,   <!-- 系统账套 -->
		UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->
		STATUS	= #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
		DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
		REMARK	= #remark#,   <!-- 备注 -->
		TENANT_ID	= #tenantId#  <!-- 租户ID -->
		WHERE
		1=1
		and
		SEG_NO	= #segNo#
		and
		M_UUID	= #m_uuid#
		and
		STATUS !='00'
	</update>

	<update id="updateIsVhicleNo">
		UPDATE ${meliSchema}.TLIRL0103
		SET
		STATUS	= '00',   <!-- 状态(撤销：00、新增：10、生效：20) -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= '1'   <!-- 记录删除标记 -->
		WHERE
		M_UUID = #m_uuid#
		AND STATUS != 00
		AND DEL_FLAG = 0
		AND VEHICLE_NO = #vehicleNo#
	</update>

	<select id="queryVehicleNo"
		resultClass="String">
		SELECT
		VEHICLE_NO	as "vehicleNo"  <!-- 车牌号 -->
		FROM ${meliSchema}.TLIRL0103 WHERE 1=1
		and M_UUID=#uuid#
		and STATUS !='00'
		and DEL_FLAG='0'
	</select>


	<select id="queryAllVehicleNo"
			resultClass="String">
		SELECT
		VEHICLE_NO	as "vehicleNo"  <!-- 车牌号 -->
		FROM ${meliSchema}.TLIRL0103 WHERE 1=1
		and SEG_NO=#segNo#
		<isNotEmpty prepend="and" property="arrList">
			M_UUID IN
			<iterate open="(" close=")" conjunction="," property="arrList">
				#arrList[]#
			</iterate>
		</isNotEmpty>
		and STATUS !='00'
		and DEL_FLAG='0'
	</select>

	<select id="countByCustomerId" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.TLIRL0103 a WHERE 1=1
		and SEG_NO = #segNo#
		and a.DEL_FLAG = '0'
		AND EXISTS (
		SELECT
		1
		FROM
		${meliSchema}.tlirl0102 t
		WHERE 1 = 1
		AND a.SEG_NO = t.SEG_NO
		and t.UUID = a.M_UUID
		and t.STATUS in ('10','20')
		<isNotEmpty prepend=" AND " property="reservationIdentity">
			t.RESERVATION_IDENTITY = #reservationIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tel">
			t.TEL = #tel#
		</isNotEmpty>
		and t.CUSTOMER_ID = #customerId#
		)
		<isNotEmpty prepend=" AND " property="uuid">
			a.UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			a.M_UUID = #notUuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			a.STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			a.VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="arrList">
			a.VEHICLE_NO IN
			<iterate open="(" close=")" conjunction="," property="arrList">
				#arrList[]#
			</iterate>
		</isNotEmpty>
	</select>

	<update id="updateByMUUID">
		UPDATE ${meliSchema}.TLIRL0103
		SET
		STATUS = #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#  <!-- 记录修改时间 -->
		WHERE
		M_UUID = #m_uuid#
		AND DEL_FLAG = #delFlag#
		and STATUS != '00'
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO != #vehicleNo#
		</isNotEmpty>
	</update>

	<select id="queryLookUpTheLicensePlate" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		a.SEG_NO	as "segNo",  <!-- 系统账套 -->
		a.UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
		a.VEHICLE_NO	as "vehicleNo"  <!-- 车牌号 -->
		FROM ${meliSchema}.TLIRL0103 a WHERE 1=1
		AND a.SEG_NO = #segNo#
		AND EXISTS (
		SELECT
		1
		FROM
		${meliSchema}.tlirl0102 t
		WHERE 1 = 1
		AND a.SEG_NO = t.SEG_NO
		and t.UUID = a.M_UUID
		and t.STATUS in ('20')
		and t.DRIVER_IDENTITY = #driverIdentity#
		)
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_uuid">
			M_UUID = #m_uuid#
		</isNotEmpty>

		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="queryVehicleNo2" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select SEG_NO                    as "segNo",
			   UNIT_CODE                 as "unitCode",
			   M_UUID                    as "m_uuid",
			   STATUS                    as "status",
			   VEHICLE_NO                as "vehicleNo",
			   REC_CREATOR               as "recCreator",
			   REC_CREATOR_NAME          as "recCreatorName",
			   REC_CREATE_TIME           as "recCreateTime",
			   REC_REVISOR               as "recRevisor",
			   REC_REVISOR_NAME          as "recRevisorName",
			   REC_REVISE_TIME           as "recReviseTime",
			   ARCHIVE_FLAG              as "archiveFlag",
			   DEL_FLAG                  as "delFlag",
			   REMARK                    as "remark",
			   UUID                      as "uuid",
			   (SELECT DRIVER_NAME
				FROM ${meliSchema}.tlirl0102 t
				WHERE 1 = 1
				  AND a.SEG_NO = t.SEG_NO
				  and t.UUID = a.M_UUID) as "driverName",
			   (SELECT DRIVER_IDENTITY
				FROM ${meliSchema}.tlirl0102 t
				WHERE 1 = 1
				  AND a.SEG_NO = t.SEG_NO
				  and t.UUID = a.M_UUID) as "driverIdentity",
			   (SELECT TEL
				FROM ${meliSchema}.tlirl0102 t
				WHERE 1 = 1
				  AND a.SEG_NO = t.SEG_NO
				  and t.UUID = a.M_UUID) as "tel",
		(SELECT TEL
		FROM ${meliSchema}.tlirl0102 t
		WHERE 1 = 1
		AND a.SEG_NO = t.SEG_NO
		and t.UUID = a.M_UUID) as "driverTel"
		FROM ${meliSchema}.TLIRL0103 a
		WHERE 1 = 1
		  and SEG_NO = #segNo#
		  and STATUS = '20'
		  AND EXISTS(
			SELECT 1
			FROM ${meliSchema}.tlirl0102 t
			WHERE 1 = 1
			  AND a.SEG_NO = t.SEG_NO
			  and t.UUID = a.M_UUID
			  and t.STATUS in ('20')
				<isNotEmpty prepend=" AND " property="customerId">
					t.CUSTOMER_ID in((select tlirl0101.CUSTOMER_ID
					from meli.tlirl0101
					where 1 = 1
					and tlirl0101.SEG_NO = t.SEG_NO
					and tlirl0101.TEL = #tel#
					<isNotEmpty prepend="and" property="customerId">
						tlirl0101.CUSTOMER_ID IN
						<iterate open="(" close=")" conjunction="," property="customerId">
							#customerId[]#
						</iterate>
					</isNotEmpty>)
					)
				</isNotEmpty>
		)
	</select>


	<select id="queryVehicleNoUndo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct SEG_NO                                                                                       as "segNo",
						VEHICLE_NO                                                                                   as "vehicleNo",
						(SELECT DRIVER_NAME
						 FROM MELI.tlirl0102 t
						 WHERE 1 = 1
						   AND a.SEG_NO = t.SEG_NO
						   and t.UUID = a.M_UUID)                                                                    as "driverName",
						(SELECT DRIVER_IDENTITY
						 FROM MELI.tlirl0102 t
						 WHERE 1 = 1
						   AND a.SEG_NO = t.SEG_NO
						   and t.UUID = a.M_UUID)                                                                    as "driverIdentity",
						(SELECT TEL FROM MELI.tlirl0102 t WHERE 1 = 1 AND a.SEG_NO = t.SEG_NO and t.UUID = a.M_UUID) as "driverTel",
						(select CAR_TRACE_NO
						 from meli.tlirl0301
						 where 1 = 1
						   and tlirl0301.SEG_NO = a.SEG_NO
						   and tlirl0301.VEHICLE_NO = a.VEHICLE_NO
																														limit 1)                                                                                    as "carTraceNo"
		FROM MELI.TLIRL0103 a
		WHERE 1 = 1
		  and SEG_NO = #segNo#
		  and STATUS = '20'
		  AND EXISTS(SELECT 1
			FROM MELI.tlirl0102 t
			WHERE 1 = 1
		  AND a.SEG_NO = t.SEG_NO
		  and t.UUID = a.M_UUID
		  and t.STATUS in ('20')
		  AND EXISTS(SELECT 1
			FROM MELI.tlirl0102 t
			WHERE 1 = 1
		  AND a.SEG_NO = t.SEG_NO
		  and t.UUID = a.M_UUID
		  and t.STATUS in ('20')
		  and t.TEL = #tel#))

		UNION all
		select DISTINCT SEG_NO            as "segNo",
						VEHICLE_NO        as "vehicleNo",
						A.DRIVER_NAME     as "driverName",
						A.DRIVER_IDENTITY AS "driverIdentity",
						A.DRIVER_TEL      as "driverTel",
						(select CAR_TRACE_NO
						 from meli.tlirl0301
						 where 1 = 1
						   and tlirl0301.SEG_NO = a.SEG_NO
						   and tlirl0301.RESERVATION_NUMBER = a.RESERVATION_NUMBER
											 limit 1)         as "carTraceNo"
		FROM MELI.tlirl0201 a
		WHERE 1 = 1
		  and SEG_NO = #segNo#
		  and STATUS = '20'
		  AND A.DRIVER_TEL = #tel#

	</select>

</sqlMap>