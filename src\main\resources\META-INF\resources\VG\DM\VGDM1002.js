$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
                // 上料
                $("#UPPACK").on("click", function (e) {
                    callBackService(false, false, "upPack");
                });
                // 启动工单
                $("#START").on("click", function (e) {
                    callBackService(false, false, "startProcessOrder");
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);

    /**
     * 调用后端方法
     * @param {boolean} isCheckGrid 是否校验grid
     * @param {boolean} isCheckValue 是否校验tagValue
     * @param {string} serviceName 后台
     * @returns
     */
    function callBackService(isCheckGrid, isCheckValue, serviceName) {
        if (isCheckGrid && !IMOMUtil.checkOneSelect(resultGrid)) {
            return;
        }
        const tagId = IPLAT.EFSelect.value($("#detail_status-0-tagId"));
        if (IPLAT.isBlankString(tagId)) {
            NotificationUtil("操作失败，原因[点位不能为空！]", "error");
            return;
        }
        if (serviceName === 'upPack') {
            if (tagId === 'JC52BL010001') {
                serviceName = 'upBLPack';
            }
        }
        if (serviceName === 'startProcessOrder') {
            if (tagId === 'JC52BL010001') {
                serviceName = 'startBLOrder';
            } else if (tagId === 'JC52CL011001') {
                serviceName = 'startCL01Order';
            } else if (tagId === 'JC52CL020001') {
                serviceName = 'startCL02Order';
            }
        }
        const eiInfo = new EiInfo();
        eiInfo.set("tagId", tagId);
        eiInfo.set("notAutoFlag", "2");
        // 调用接口
        IMOMUtil.submitEiInfo(eiInfo, "VGDM1002", serviceName, function (ei) {
        });
    }
});