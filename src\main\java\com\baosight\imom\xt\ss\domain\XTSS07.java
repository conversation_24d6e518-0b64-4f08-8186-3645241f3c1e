package com.baosight.imom.xt.ss.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class XTSS07 extends DaoEPBase {
    public static final String QUERY = "XTSS07.query";
    public static final String COUNT = "XTSS07.count";

    private String segNo = " ";        /* 业务单元代代码*/
    private String segName = " ";        /* 业务单元代简称*/
    private String loginName = " ";        /* 登录账号*/
    private String userName = " ";        /* 用户姓名*/
    private String mobile = " ";        /* 手机号*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元代简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loginName");
        eiColumn.setDescName("登录账号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userName");
        eiColumn.setDescName("用户姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mobile");
        eiColumn.setDescName("手机号");
        eiMetadata.addMeta(eiColumn);

    }

    public String getSegNo() {
        return segNo;
    }

    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * the constructor
     */
    public XTSS07() {
        initMetaData();
    }


    /**
     * get the value from Map
     */
    public void fromMap(Map map) {
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setLoginName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loginName")), loginName));
        setUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userName")), userName));
        setMobile(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mobile")), mobile));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {
        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("loginName", StringUtils.toString(loginName, eiMetadata.getMeta("loginName")));
        map.put("userName", StringUtils.toString(userName, eiMetadata.getMeta("userName")));
        map.put("mobile", StringUtils.toString(mobile, eiMetadata.getMeta("mobile")));
        return map;
    }
}
