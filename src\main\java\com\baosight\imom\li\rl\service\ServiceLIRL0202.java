package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: 张博翔
 * @Description: ${预约黑名单管理表}
 * @Date: 2024/8/19 9:26
 * @Version: 1.0
 */
public class ServiceLIRL0202 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0202().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String reservationDateRange = MapUtils.getString(queryBlock, "reservationDateRange", "");
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDateRange", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, -1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDateRange", format);
            }
            outInfo = super.query(inInfo, LIRL0202.QUERY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    public EiInfo query2(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map queryBlock = inInfo.getBlock("inqu2_status").getRow(0);
            outInfo = super.query(inInfo, LIRL0203.QUERY, new LIRL0203(), false, null, "inqu2_status", "result2", "result2");

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 解禁.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo liftaban(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0202> query = dao.query(LIRL0202.QUERY, map);
                for (LIRL0202 lirl0202 : query) {
                    String blacklistFlag = lirl0202.getBlacklistFlag();
                    if (!"1".equals(blacklistFlag)) {
                        String massage = "只能对禁用状态进行解禁!";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("enableTime", DateUtil.curDateTimeStr14());
                hashMap.put("blacklistFlag", "2");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0202.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

//    /**
//     * 定时任务.
//     * 每天晚上8点一次，处理预约黑名单数据
//     *
//     * @param inInfo
//     * @return
//     * @Service:
//     */
//    public EiInfo processingAppointmentBlacklist(EiInfo inInfo) {
//        try {
//            Calendar calendar = Calendar.getInstance();
//            long l = System.currentTimeMillis(); //获取时间戳效率最高
//            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
//            String format = dateFormat.format(l);
//            String substring = format.substring(0, 8);
//            //如果预约时间段在0点-18点之间
//            Date startTime = dateFormat.parse(substring + "0000");//预约时间
//            Date endTime = dateFormat.parse(substring + "1800");//预约时间
//            //查询所有预约单
//            Map queryLIRL0201 = new HashMap();
//            queryLIRL0201.put("delFlag", 0);
//            queryLIRL0201.put("notStatus", "00");
//            queryLIRL0201.put("isReservation", "10");
//            List<LIRL0201> lirl0201s = dao.query(LIRL0201.QUERY, queryLIRL0201);
//            for (LIRL0201 lirl0201 : lirl0201s) {
//                String reservationDate = lirl0201.getReservationDate();//预约日期
//                String reservationTime = lirl0201.getReservationTime();//预约时段
//                String[] split = reservationTime.split("-");
//                String startReservationTime = reservationDate + split[0].replace(":", "");
//                String endReservationTime = substring + split[1].replace(":", "");
//                Date targetTime = dateFormat.parse(endReservationTime);
//                calendar.setTime(targetTime);
//                calendar.add(calendar.DATE, 1);//给日期加上天
//                Date time = calendar.getTime();
//                String endReservation24Time = dateFormat.format(time);
//
//                String segNo = lirl0201.getSegNo();
//                String driverTel = lirl0201.getDriverTel();//司机手机号
//                String vehicleNo = lirl0201.getVehicleNo();//司机车牌号
//
//                //查询是否在黑名单表里存在
//                Map queryLIRL0202 = new HashMap();
//                queryLIRL0202.put("segNo", segNo);
//                queryLIRL0202.put("driverTel", driverTel);
//                queryLIRL0202.put("vehicleNo", vehicleNo);
//                queryLIRL0202.put("delFlag", 0);
//                //不判断状态
//                /*queryLIRL0202.put("blacklistFlag", "1");*/
//                int count = super.count(LIRL0202.COUNT, queryLIRL0202);
//                if (count > 0) {
//                    break;
//                }
//                //插入黑名单临时表
//                HashMap hashMap = new HashMap();
//
//                //查询车辆进厂登记表数据
//                Map queryMap = new HashMap();
//                queryMap.put("delFlag", 0);
//                queryMap.put("reservationNumber", lirl0201.getReservationNumber());
//                List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY_PROCESSING_APPOINTMENT_BLACKLIST, queryMap);
//                for (LIRL0302 lirl0302 : lirl0302s) {
//                    String telNum = lirl0302.getTelNum();//司机电话
//                    String driverName = lirl0302.getDriverName();//司机姓名
//                    String driverIdentity = lirl0302.getIdCard();//司机身份证
//
//                    String checkDateString = lirl0302.getCheckDate().substring(0, 10);//进厂登记时间
//                    String reservationNumber = lirl0302.getReservationNumber();//车辆预约单号
//
//                    //查询预约时间过24小时后是否进厂
//                    BigDecimal end24Time = new BigDecimal(endReservation24Time);
//                    BigDecimal checkDateTime = new BigDecimal(checkDateString);//进厂登记时间
//                    /*int i = checkDateTime.compareTo(end24Time);*/
//
//                    //判断预约已经插入临时黑名单表的数据进厂不再插入
//                    //查询临时黑名单表
//                    Map queryLIRL0203 = new HashMap();
//                    queryLIRL0203.put("segNo",segNo);
//                    queryLIRL0203.put("reservationNumber",reservationNumber);
//                    queryLIRL0203.put("delFlag", 0);
//                    int count1 = super.count(LIRL0203.COUNT, queryLIRL0203);
//                    if (count1>0){
//                        break;
//                    }
//
//                    //插入黑名单临时表数据
//                    hashMap = new HashMap();
//                    hashMap.put("driverName", driverName);
//                    hashMap.put("driverTel", telNum);
//                    hashMap.put("driverIdentity", driverIdentity);
//                    hashMap.put("vehicleNo", vehicleNo);
//                    hashMap.put("reservationTime", endReservationTime);
//                    hashMap.put("customerId", lirl0201.getCustomerId());
//                    hashMap.put("customerName", lirl0201.getCustomerName());
//                    hashMap.put("enterTime", lirl0302.getCheckDate());
//                    hashMap.put("reservationNumber", lirl0201.getReservationNumber());
//                    hashMap.put("sysRemark", "预约后超时抵达登记");
//
//
//                    //判断预约时间是否0点-18点之间
//                    boolean timeBetween = DateUtils.isTimeBetween(startTime, endTime, targetTime);
//                    if (timeBetween) {
//                        //进厂日期截取到天
//                        String checkDatedayString = checkDateString.substring(0, 8);
//                        //预约日期截取到天
//                        String reservationDayString = reservationDate;
//                        //超时
//                        if (!checkDatedayString.equals(reservationDayString)) {
//
//                            //插入黑名单临时表
//                            insert0203(hashMap, segNo);
//                            //查询临时表数据 插入预约黑名单管理表
//                            insert0202(segNo, telNum, vehicleNo, reservationNumber, checkDateString);
//                        }
//                    } else {
//                        //如果预约时间段在18点-24点之间，实际到达时间在早上10点之后
//                        String reservationDayString = reservationDate + "1000";
//                        Date overtime = dateFormat.parse(reservationDayString);
//                        // 使用calendar类给日期加天
//                        calendar.setTime(overtime);
//                        calendar.add(calendar.DATE, 1);//给日期加上天
//                        time = calendar.getTime();
//                        String day10time = dateFormat.format(time);
//                        BigDecimal day10timeBigDecimal = new BigDecimal(day10time);
//                        int i1 = day10timeBigDecimal.compareTo(checkDateTime);
//                        //判断是否超时
//                        if (i1 < 0) {
//                            //插入黑名单临时表
//                            insert0203(hashMap, segNo);
//                            //查询临时表数据 插入预约黑名单管理表
//                            insert0202(segNo, telNum, vehicleNo);
//                        }
//                    }
//                }
//
//
//                //判断没有进厂登记信息的预约单
//                if (lirl0302s.size() < 1){
//                    String telNum = driverTel;//司机电话
//                    String driverName = lirl0201.getDriverName();//司机姓名
//                    String driverIdentity = lirl0201.getDriverIdentity();//司机身份证
//
//                    String reservationNumber = lirl0201.getReservationNumber();//车辆预约单号
//
//                    //查询预约时间过24小时后是否进厂
//                    BigDecimal end24Time = new BigDecimal(endReservation24Time);
//
//                    //判断预约已经插入临时黑名单表的数据进厂不再插入
//                    //查询临时黑名单表
//                    Map queryLIRL0203 = new HashMap();
//                    queryLIRL0203.put("segNo",segNo);
//                    queryLIRL0203.put("reservationNumber",reservationNumber);
//                    queryLIRL0203.put("delFlag", 0);
//                    int count1 = super.count(LIRL0203.COUNT, queryLIRL0203);
//                    if (count1>0){
//                        break;
//                    }
//
//                    //插入黑名单临时表数据
//                    hashMap = new HashMap();
//                    hashMap.put("driverName", driverName);
//                    hashMap.put("driverTel", telNum);
//                    hashMap.put("driverIdentity", driverIdentity);
//                    hashMap.put("vehicleNo", vehicleNo);
//                    hashMap.put("reservationTime", endReservationTime);
//                    hashMap.put("customerId", lirl0201.getCustomerId());
//                    hashMap.put("customerName", lirl0201.getCustomerName());
//                    hashMap.put("enterTime","");
//                    hashMap.put("reservationNumber", lirl0201.getReservationNumber());
//                    hashMap.put("sysRemark", "预约后超时一直未到");
//
//
//                    //判断预约时间是否0点-18点之间
//                    boolean timeBetween = DateUtils.isTimeBetween(startTime, endTime, targetTime);
//                    if (timeBetween) {
//                        //当前时间日期截取到天
//                        String checkDatedayString = format.substring(0, 8);
//                        //预约日期截取到天
//                        String reservationDayString = reservationDate;
//                        //超时
//                        if (!checkDatedayString.equals(reservationDayString)) {
//                            //插入黑名单临时表
//                            insert0203(hashMap, segNo);
//                            //查询临时表数据 插入预约黑名单管理表
//                            insert0202(segNo, telNum, vehicleNo);
//                        }
//                    } else {
//                        //如果预约时间段在18点-24点之间，早上10点之后未到
//                        String reservationDayString = reservationDate + "1000";
//                        Date overtime = dateFormat.parse(reservationDayString);
//                        // 使用calendar类给日期加天
//                        calendar.setTime(overtime);
//                        calendar.add(calendar.DATE, 1);//给日期加上天
//                        time = calendar.getTime();
//                        String day10time = dateFormat.format(time);
//                        BigDecimal day10timeBigDecimal = new BigDecimal(day10time);
//                        //当前时间
//                        BigDecimal nowDate = new BigDecimal(format);
//                        int i1 = day10timeBigDecimal.compareTo(nowDate);
//                        //判断是否超时
//                        if (i1 < 0) {
//                            //插入黑名单临时表
//                            insert0203(hashMap, segNo);
//                            //查询临时表数据 插入预约黑名单管理表
//                            insert0202(segNo, telNum, vehicleNo);
//                        }
//                    }
//                }
//            }
//            // 返回成功状态和消息
//            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
//            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
//        } catch (Exception ex) {
//            // 异常返回失败状态和消息
//            inInfo.setStatus(EiConstant.STATUS_FAILURE);
//            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
//        }
//        return inInfo;
//    }

    /**
     * 插入黑名单临时表.
     * 每天晚上8点一次，处理预约黑名单数据
     *
     * @param hashMap 车辆数据
     * @param segNo   业务单元代码
     * @return
     * @Service:
     */
    private void insert0203(HashMap hashMap, String segNo) {

        Map insertLirl0203 = new HashMap();
        insertLirl0203.put("segNo", segNo);
        insertLirl0203.put("unitCode", segNo);
        insertLirl0203.put("blacklistFlag", 1);//新增
        insertLirl0203.put("driverName", MapUtils.getString(hashMap, "driverName", ""));
        insertLirl0203.put("driverTel", MapUtils.getString(hashMap, "driverTel", ""));
        insertLirl0203.put("driverIdentity", MapUtils.getString(hashMap, "driverIdentity", ""));
        insertLirl0203.put("vehicleNo", MapUtils.getString(hashMap, "vehicleNo", ""));
        insertLirl0203.put("reservationTime", MapUtils.getString(hashMap, "reservationTime", "")+"00");
        insertLirl0203.put("uuid", StrUtil.getUUID());
        insertLirl0203.put("customerId", MapUtils.getString(hashMap,"customerId",""));
        insertLirl0203.put("customerName", MapUtils.getString(hashMap,"customerName",""));
        insertLirl0203.put("checkDateString", MapUtils.getString(hashMap,"checkDateString",""));
        insertLirl0203.put("reservationNumber", MapUtils.getString(hashMap,"reservationNumber",""));
        insertLirl0203.put("enterTime", MapUtils.getString(hashMap,"enterTime",""));
        insertLirl0203.put("sysRemark", MapUtils.getString(hashMap,"sysRemark",""));
        RecordUtils.setCreator(insertLirl0203);
        LIRL0203 lirl0203 = new LIRL0203();
        lirl0203.fromMap(insertLirl0203);
        //插入黑名单临时表
        dao.insert(LIRL0203.INSERT, lirl0203);
    }

    /**
     * 查询临时表数据 插入预约黑名单管理表.
     * 每天晚上8点一次，处理预约黑名单数据
     *
     * @param segNo     业务单元代码
     * @param telNum    手机号
     * @param vehicleNo 车牌号
     * @return
     * @Service:
     */
    private void insert0202(String segNo, String telNum, String vehicleNo,String reservationNumber ,String checkDate,String remark,String driveridentity,String driverName) {
        Map queryL0203 = new HashMap();
        queryL0203.put("segNo", segNo);
        queryL0203.put("driverTel", telNum);
        queryL0203.put("vehicleNo", vehicleNo);
        queryL0203.put("driverIdentity", driveridentity);
        queryL0203.put("driverName", driverName);
        queryL0203.put("blacklistFlag", 1);
        queryL0203.put("noFlag", 1);
        queryL0203.put("isBlocked", 1); //已拉黑不处理
        List<LIRL0203> lirl0203s = dao.query(LIRL0203.QUERY, queryL0203);
        //每个月若有两次爽约，则自动进入到黑名单中
        List<String> list = new ArrayList<>();
        if (lirl0203s.size() > 1) {
            for (LIRL0203 lirl0203 : lirl0203s) {
                Map map = lirl0203.toMap();
                map.put("isBlocked","1");//拉黑
                this.dao.update(LIRL0203.UPDATE_IS_BLOCKED, lirl0203);
                String reservationTime = lirl0203.getReservationTime();
                list.add(reservationTime);
            }
            LIRL0203 lirl0203 = lirl0203s.get(0);
            Map insertLirl0202 = lirl0203.toMap();
            /*insertLirl0202.put("enterTime", DateUtil.curDateTimeStr14());*/
            insertLirl0202.put("forbiddenTime", DateUtil.curDateTimeStr14());
            insertLirl0202.put("sysRemark", "定时任务新增");
            insertLirl0202.put("reservationNumber", reservationNumber);
            insertLirl0202.put("enterTime", checkDate);
            insertLirl0202.put("remark", remark);
            insertLirl0202.put("insType", "20");
            RecordUtils.setCreator(insertLirl0202);
            LIRL0202 lirl0202 = new LIRL0202();
            lirl0202.fromMap(insertLirl0202);
            dao.insert(LIRL0202.INSERT, lirl0202);
            //拉黑发送短信：姓名+车牌号因x月x日和x月x日分别有一次爽约记录，现被拉入黑名单，一个月内不得预约。

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("param1",telNum);
            hashMap.put("param2",driverName);
            hashMap.put("param3",vehicleNo);
            String revDate = DateTimeUtil.convertToYearMonthDay(list.get(0));
            String revDateTwo = DateTimeUtil.convertToYearMonthDay(list.get(1));
            hashMap.put("param4",revDate);
            hashMap.put("param5",revDateTwo);
            MessageUtils.sendMessage(hashMap, "MT0000001018");
        }
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo", "");
                String driverTel = MapUtils.getString(hashMap, "driverTel", "");
                String adminIndtity = MapUtils.getString(hashMap, "adminIndtity", "");//预约身份(客户/承运商)
                String customerId = MapUtils.getString(hashMap, "customerId", "");//承运商
                /*EiInfo eiInfo = StrUtil.determineIDcardAndPhone(inInfo, tel, adminIndtity);
                if (eiInfo.getStatus()<0){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return eiInfo;
                }*/
                //查询数据是否重复
                Map query = new HashMap();
                query.put("segNo",segNo);
                query.put("vehicleNo",vehicleNo);
                query.put("customerId",customerId);
                query.put("blacklistFlag",1);
                query.put("delFlag", 0);
                int count = super.count(LIRL0202.COUNT, query);
                if (count>0){
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("blacklistFlag", 1);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                hashMap.put("sysRemark", "手工新增");//备注
                hashMap.put("forbiddenTime", DateUtil.curDateTimeStr14());//禁用时间
                hashMap.put("insType", "10");
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0202.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }



    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo", "");
                String driverTel = MapUtils.getString(hashMap, "driverTel", "");
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                String adminIndtity = MapUtils.getString(hashMap, "adminIndtity", "");
                String customerId = MapUtils.getString(hashMap, "customerId", "");//承运商
                /*EiInfo eiInfo = StrUtil.determineIDcardAndPhone(inInfo, driverTel, adminIndtity);
                if (eiInfo.getStatus()<0){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return eiInfo;
                }*/
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0202> query = dao.query(LIRL0202.QUERY, map);
                for (LIRL0202 LIRL0202:query){
                    String blacklistFlag = LIRL0202.getBlacklistFlag();
                    if ("1".equals(blacklistFlag)){
                        String massage = "只能对解禁数据进行修改！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                //查询数据是否重复
                Map queryCount = new HashMap();
                queryCount.put("vehicleNo",vehicleNo);
                queryCount.put("customerId",customerId);
                queryCount.put("delFlag", 0);
                queryCount.put("notUuid", uuid);
                int count = super.count(LIRL0202.COUNT, queryCount);
                if (count>0){
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0202.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0202> query = dao.query(LIRL0202.QUERY, map);
                for (LIRL0202 LIRL0202:query){
                    String blacklistFlag = LIRL0202.getBlacklistFlag();
                    if ("1".equals(blacklistFlag)){
                        String massage = "只能对解禁数据进行删除！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0202.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0202> queryLIRL0202 = dao.query(LIRL0202.QUERY, map);
                for (LIRL0202 LIRL0202:queryLIRL0202){
                    String blacklistFlag = LIRL0202.getBlacklistFlag();
                    if ("1".equals(blacklistFlag)){
                        String massage = "只能对解禁数据进行确认！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                //查询数据是否重复
                HashMap query = new HashMap();
                String customerName = MapUtils.getString(hashMap, "customerName", "");
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo", "");
                query.put("vehicleNo",vehicleNo);
                query.put("customerId",MapUtils.getString(hashMap,"customerId",""));
                query.put("blacklistFlag",1);
                query.put("delFlag", 0);
                int count = super.count(LIRL0202.COUNT, query);
                if (count>0){
                    String massage = customerName+"已存在车牌号为:["+vehicleNo+"]的禁用数据!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("blacklistFlag", 1);//状态
                hashMap.put("forbiddenTime", DateUtil.curDateTimeStr14());//状态
                hashMap.put("enableTime", "");//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0202.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /***
     * 定时任务黑名单重写
     * @param inInfo
     * @return
     * S_LI_RL_0001
     */
    public EiInfo processingAppointmentBlacklist(EiInfo inInfo) {

        //获取预约单信息，预约时间以及车辆实际到达时间
        //查询所有预约单
        Map queryLIRL0201 = new HashMap();
        queryLIRL0201.put("delFlag", 0);
        queryLIRL0201.put("notStatus", "00");
        queryLIRL0201.put("isReservation", "10");
        queryLIRL0201.put("remarkNo","离厂回退");
        queryLIRL0201.put("segNo","KF000000");
        queryLIRL0201.put("flag","10");
        queryLIRL0201.put("insType","20");
        List<LIRL0201> lirl0201s = dao.query(LIRL0201.QUERY_TIMEOUT_DATA, queryLIRL0201);
        try {
            for (LIRL0201 lirl0201 : lirl0201s) {
                //获取预约时间
                String reservationDate = lirl0201.getReservationDate();//预约日期
                String reservationTime = lirl0201.getReservationTime();//预约时段
                String ReservationNumber = lirl0201.getReservationNumber();//预约单号
                String segNo = lirl0201.getSegNo();
                String driverTel = lirl0201.getDriverTel();//司机手机号
                String vehicleNo = lirl0201.getVehicleNo();//司机车牌号
                String driverName = lirl0201.getDriverName();//司机名称
                String driveridentity = lirl0201.getDriverIdentity();//司机名称
                //格式化时间
                String scheduledTime = formatAppointmentTime(reservationDate.concat(reservationTime));//预约时间
                //根据预约单查询车辆进厂登记的登记时间
                HashMap<String, String> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("vehicleNo", vehicleNo);
                stringStringHashMap.put("reservationNumber", ReservationNumber);
                System.out.println("预约单号："+ReservationNumber);
                stringStringHashMap.put("segNo", segNo);
                stringStringHashMap.put("status", "20");
                List<LIRL0302> queryLIRL0301 = this.dao.query(LIRL0302.QUERY_CHECK_DATE, stringStringHashMap);
                if (CollectionUtils.isNotEmpty(queryLIRL0301)){
                    //获取进厂时间
                    String checkDate = queryLIRL0301.get(0).getCheckDate();
                    //根据预约时间规则，用进厂时间和预约时间比较，符合规则算爽约
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("scheduledTime", scheduledTime);//预约时间
                    hashMap.put("actualArrivalTime", checkDate);//实际到达时间
                    boolean missed = isMissed(hashMap);
                    if (missed){
                        //查询是否在黑名单表里存在
                        Map queryLIRL0202 = new HashMap();
                        queryLIRL0202.put("segNo", segNo);
                        queryLIRL0202.put("driverTel", driverTel);
                        queryLIRL0202.put("vehicleNo", vehicleNo);
                        queryLIRL0202.put("driverIdentity", driveridentity);
                        queryLIRL0202.put("driverName", driverName);
                        queryLIRL0202.put("delFlag", 0);
                        queryLIRL0202.put("blacklistFlag", "1");//禁用
                        int count = super.count(LIRL0202.COUNT, queryLIRL0202);
                        if (count <=0) {
                            //判断是否在临时表中存在
                            //不是当前预约单
                            queryLIRL0202.put("reservationNumber", ReservationNumber);
                            //该预约单是否已经近了黑名单
                            List<LIRL0203> query = this.dao.query(LIRL0203.QUERY, queryLIRL0202);
                            if (CollectionUtils.isEmpty(query)){
                                queryLIRL0202.put("reservationNumber", "");
                                int count03 = super.count(LIRL0203.COUNT, queryLIRL0202);
                                if (count03 <=0) {
                                    //插入黑名单临时表数据
                                    hashMap = new HashMap();
                                    hashMap.put("driverName", driverName);
                                    hashMap.put("driverTel", driverTel);
                                    hashMap.put("driverIdentity",driveridentity );
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("reservationTime", scheduledTime.substring(0, scheduledTime.length() - 4));
                                    hashMap.put("customerId", lirl0201.getCustomerId());
                                    hashMap.put("customerName", lirl0201.getCustomerName());
                                    hashMap.put("enterTime", checkDate);//登记时间
                                    hashMap.put("reservationNumber", ReservationNumber);
                                    hashMap.put("sysRemark", "预约后超时抵达登记"+DateUtil.curDateTimeStr14());
                                    hashMap.put("remark", "预约后超时抵达登记:"+DateUtil.curDateTimeStr14());
                                    //插入黑名单临时表
                                    insert0203(hashMap, segNo);
                                }else {
                                    //一个月爽约2次 自动进入黑名单
                                    //插入黑名单临时表
                                    //插入黑名单表
                                    hashMap = new HashMap();
                                    hashMap.put("driverName", driverName);
                                    hashMap.put("driverTel", driverTel);
                                    hashMap.put("driverIdentity",driveridentity );
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("reservationTime", scheduledTime.substring(0, scheduledTime.length() - 4));
                                    hashMap.put("customerId", lirl0201.getCustomerId());
                                    hashMap.put("customerName", lirl0201.getCustomerName());
                                    hashMap.put("enterTime", checkDate);//登记时间
                                    hashMap.put("reservationNumber", lirl0201.getReservationNumber());
                                    hashMap.put("sysRemark", "预约后超时抵达登记"+DateUtil.curDateTimeStr14());
                                    hashMap.put("remark", "预约后超时抵达登记:"+DateUtil.curDateTimeStr14());
                                    //插入黑名单临时表
                                    insert0203(hashMap, segNo);
                                    //查询临时表数据 插入预约黑名单管理表
                                    insert0202(segNo, driverTel, vehicleNo,ReservationNumber,checkDate,"预约后超时抵达登记:"+DateUtil.curDateTimeStr14(),driveridentity,driverName);
                                }
                            }else {
                                queryLIRL0202.put("reservationNumber", "");
                                int count03 = super.count(LIRL0203.COUNT, queryLIRL0202);
                                if (count03>1){
                                    //一个月爽约2次 自动进入黑名单
                                    //插入黑名单临时表
                                    //插入黑名单表
                                    hashMap = new HashMap();
                                    hashMap.put("driverName", driverName);
                                    hashMap.put("driverTel", driverTel);
                                    hashMap.put("driverIdentity",driveridentity );
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("reservationTime", scheduledTime.substring(0, scheduledTime.length() - 4));
                                    hashMap.put("customerId", lirl0201.getCustomerId());
                                    hashMap.put("customerName", lirl0201.getCustomerName());
                                    hashMap.put("enterTime", " ");//登记时间
                                    hashMap.put("reservationNumber", lirl0201.getReservationNumber());
                                    hashMap.put("sysRemark", "预约后超时未登记"+DateUtil.curDateTimeStr14());
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //插入黑名单临时表
//                                    insert0203(hashMap, segNo);
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //查询临时表数据 插入预约黑名单管理表
                                    insert0202(segNo, driverTel, vehicleNo,ReservationNumber,"","预约后超时未登记:"
                                            +DateUtil.curDateTimeStr14(),driveridentity,driverName);
                                }
                            }
                        }
                    }
                }else {
                    //判断预约单是否超时
                    //获取进厂时间
                    String checkDate = DateUtil.curDateTimeStr14();
                    //根据预约时间规则，用进厂时间和预约时间比较，符合规则算爽约
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("scheduledTime", scheduledTime);//预约时间
                    hashMap.put("actualArrivalTime", checkDate);//实际到达时间
                    boolean missed = isMissed(hashMap);
                    if (missed){
                        //查询是否在黑名单表里存在
                        Map queryLIRL0202 = new HashMap();
                        queryLIRL0202.put("segNo", segNo);
                        queryLIRL0202.put("driverTel", driverTel);
                        queryLIRL0202.put("vehicleNo", vehicleNo);
                        queryLIRL0202.put("driverIdentity", driveridentity);
                        queryLIRL0202.put("driverName", driverName);
                        queryLIRL0202.put("delFlag", 0);
                        queryLIRL0202.put("blacklistFlag", "1");//禁用
                        int count = super.count(LIRL0202.COUNT, queryLIRL0202);
                        if (count <=0) {
                            //判断是否在临时表中存在
                            //不是当前预约单
                            queryLIRL0202.put("reservationNumber", ReservationNumber);
                            //该预约单是否已经近了黑名单履历表
                            List<LIRL0203> query = this.dao.query(LIRL0203.QUERY, queryLIRL0202);
                            if (CollectionUtils.isEmpty(query)){
                                queryLIRL0202.put("reservationNumber", "");
                                int count03 = super.count(LIRL0203.COUNT, queryLIRL0202);
                                if (count03 <=0) {
                                    //插入黑名单临时表数据
                                    hashMap = new HashMap();
                                    hashMap.put("driverName", driverName);
                                    hashMap.put("driverTel", driverTel);
                                    hashMap.put("driverIdentity",driveridentity );
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("reservationTime", scheduledTime.substring(0, scheduledTime.length() - 4));
                                    hashMap.put("customerId", lirl0201.getCustomerId());
                                    hashMap.put("customerName", lirl0201.getCustomerName());
                                    hashMap.put("enterTime", " ");//登记时间
                                    hashMap.put("reservationNumber", ReservationNumber);
                                    hashMap.put("sysRemark", "预约后超时未登记"+DateUtil.curDateTimeStr14());
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //插入黑名单临时表
                                    insert0203(hashMap, segNo);
                                }else {
                                    //一个月爽约2次 自动进入黑名单
                                    //插入黑名单临时表
                                    //插入黑名单表
                                    hashMap = new HashMap();
                                    hashMap.put("driverName", driverName);
                                    hashMap.put("driverTel", driverTel);
                                    hashMap.put("driverIdentity",driveridentity );
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("reservationTime", scheduledTime.substring(0, scheduledTime.length() - 4));
                                    hashMap.put("customerId", lirl0201.getCustomerId());
                                    hashMap.put("customerName", lirl0201.getCustomerName());
                                    hashMap.put("enterTime", " ");//登记时间
                                    hashMap.put("reservationNumber", lirl0201.getReservationNumber());
                                    hashMap.put("sysRemark", "预约后超时未登记"+DateUtil.curDateTimeStr14());
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //插入黑名单临时表
                                    insert0203(hashMap, segNo);
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //查询临时表数据 插入预约黑名单管理表
                                    insert0202(segNo, driverTel, vehicleNo,ReservationNumber,"","预约后超时未登记:"
                                            +DateUtil.curDateTimeStr14(),driveridentity,driverName);
                                }
                            }else {
                                queryLIRL0202.put("reservationNumber", "");
                                int count03 = super.count(LIRL0203.COUNT, queryLIRL0202);
                                if (count03>1){
                                    //一个月爽约2次 自动进入黑名单
                                    //插入黑名单临时表
                                    //插入黑名单表
                                    hashMap = new HashMap();
                                    hashMap.put("driverName", driverName);
                                    hashMap.put("driverTel", driverTel);
                                    hashMap.put("driverIdentity",driveridentity );
                                    hashMap.put("vehicleNo", vehicleNo);
                                    hashMap.put("reservationTime", scheduledTime.substring(0, scheduledTime.length() - 4));
                                    hashMap.put("customerId", lirl0201.getCustomerId());
                                    hashMap.put("customerName", lirl0201.getCustomerName());
                                    hashMap.put("enterTime", " ");//登记时间
                                    hashMap.put("reservationNumber", lirl0201.getReservationNumber());
                                    hashMap.put("sysRemark", "预约后超时未登记"+DateUtil.curDateTimeStr14());
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //插入黑名单临时表
//                                    insert0203(hashMap, segNo);
                                    hashMap.put("remark", "预约后超时未登记:"+DateUtil.curDateTimeStr14());
                                    //查询临时表数据 插入预约黑名单管理表
                                    insert0202(segNo, driverTel, vehicleNo,ReservationNumber,"","预约后超时未登记:"
                                            +DateUtil.curDateTimeStr14(),driveridentity,driverName);
                                }

                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    public static String formatAppointmentTime(String originalTime) {
        // 1. 移除时间中的冒号
        String timeWithoutColon = originalTime.replace(":", "");

        // 2. 将 "-" 替换为连接符（这里直接替换为空字符串）
        String formattedTime = timeWithoutColon.replace("-", "");

        return formattedTime;
    }

    /***
     * 判断是否爽约
     * @param appointment
     * @return
     */
    public boolean isMissed(HashMap appointment) {
        String scheduledTime1 = MapUtils.getString(appointment, "scheduledTime");
        String actualArrivalTime1 = MapUtils.getString(appointment, "actualArrivalTime");
        actualArrivalTime1=  actualArrivalTime1.substring(0, actualArrivalTime1.length() - 2);
        scheduledTime1=  scheduledTime1.substring(0, scheduledTime1.length() - 4);

        // 转换为 LocalDateTime
        LocalDateTime scheduledDateTime = parseDateTime(scheduledTime1);
        LocalDateTime actualArrivalDateTime = parseDateTime(actualArrivalTime1);

        // 判断是否爽约
        boolean isMissed = isMissedAppointment(scheduledDateTime, actualArrivalDateTime);

        // 输出结果
        if (isMissed) {
            System.out.println("本次预约算作爽约一次。");
        } else {
            System.out.println("本次预约未爽约。");
        }

        return isMissed;
    }

    /**
     * 将时间字符串转换为 LocalDateTime
     *
     * @param timeString 时间字符串（格式：yyyyMMddHHmm）
     * @return LocalDateTime 对象
     */
    private static LocalDateTime parseDateTime(String timeString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        return LocalDateTime.parse(timeString, formatter);
    }

    /**
     * 判断是否爽约
     *
     * @param scheduledDateTime     预约时间
     * @param actualArrivalDateTime 实际到达时间
     * @return 是否爽约
     */
    private static boolean isMissedAppointment(LocalDateTime scheduledDateTime, LocalDateTime actualArrivalDateTime) {
        // 提取预约时间和实际到达时间的小时和分钟
        int scheduledHour = scheduledDateTime.getHour();
        int actualArrivalHour = actualArrivalDateTime.getHour();

        // 规则1：预约时间在 0点-18点之间，实际到达时间在 24点（第二天0点）之后
        if (scheduledHour >= 0 && scheduledHour < 18) {
            // 判断实际到达时间是否在第二天
            if (actualArrivalDateTime.isAfter(scheduledDateTime.plusDays(1).withHour(0).withMinute(0))) {
                return true;
            }
        }
        // 规则2：预约时间在 18点-24点之间，实际到达时间在 10点之后
        else if (scheduledHour >= 18 && scheduledHour < 24) {
            // 判断实际到达时间是否在 10点之后
            if (actualArrivalDateTime.isAfter(scheduledDateTime.plusDays(1).withHour(10).withMinute(0))) {
                return true;
            }
        }

        // 其他情况不算爽约
        return false;
    }

    /***
     * 定时自动解禁黑名单（进入黑名单后30天自动解禁给予短信通知）
     * 轮训策略：每5分钟执行一次
     *
     * S_LI_RL_0178
     */
    public EiInfo autoUnbanScheduledTask(EiInfo inInfo) {
        try {
            String segNo="KF000000";
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("blacklistFlag", "1");
            hashMap.put("delFlag", "0");
            hashMap.put("noFlag", 1);
            hashMap.put("insType", "20");
            List<LIRL0202> list = this.dao.query(LIRL0202.QUERY, hashMap);
            if (CollectionUtils.isNotEmpty(list)){
                for (LIRL0202 lirl0202 : list) {
                    //解禁黑名单
                    String driverName = lirl0202.getDriverName();
                    String driverTel = lirl0202.getDriverTel();
                    String vehicleNo = lirl0202.getVehicleNo();
                    String uuid = lirl0202.getUuid();
                    HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                    stringObjectHashMap.put("segNo", segNo);
                    stringObjectHashMap.put("uuid", uuid);
                    stringObjectHashMap.put("blacklistFlag", "2");
                    stringObjectHashMap.put("recRevisor", "system");
                    stringObjectHashMap.put("recRevisorName", "system");
                    stringObjectHashMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                    stringObjectHashMap.put("enterTime",DateUtil.curDateTimeStr14());
                    stringObjectHashMap.put("sysRemark","定时任务自动解禁："+DateUtil.curDateTimeStr14());
                    this.dao.update(LIRL0202.UPDATE_FORBIDDEN_STATUS,stringObjectHashMap);
                    //发送短信
                    HashMap<Object, Object> sendMessage = new HashMap<>();
                    sendMessage.put("param1",driverTel);
                    sendMessage.put("param2",driverName);
                    sendMessage.put("param3",vehicleNo);
                    MessageUtils.sendMessage(sendMessage, "MT0000001019");
                }
            }
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }
}