/**
 * Generate time : 2025-07-10 20:43:32
 * Version : 1.0
 */
package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.ValidationUtils;
import com.baosight.imom.vg.dm.domain.VGDM1006;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.HashMap;
import java.util.Map;

public class ServiceVGDM1006 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM1006().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM1006.QUERY, new VGDM1006());
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            // 用于缓存已查询的设备和分部设备信息
            VGDM1006 vgdm1006;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1006 = new VGDM1006();
                vgdm1006.fromMap(block.getRow(i));
                // 使用ValidationUtils进行基本的非空和数值校验
                ValidationUtils.validateEntity(vgdm1006);
                // 数据重复性校验
                vgdm1006.setUuid("");
                this.checkDataRepeat(vgdm1006);
                // 转换为map
                Map insMap = vgdm1006.toMap();
                // 设置创建人
                RecordUtils.setCreator(insMap);
                // 设置行
                block.getRows().set(i, insMap);
            }
            // 插入
            DaoUtils.insertBatch(dao, VGDM1006.INSERT, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }


    /**
     * 数据重复性校验
     *
     * @param vgdm1006 数据
     */
    private void checkDataRepeat(VGDM1006 vgdm1006) {
        Map<String, Object> map = new HashMap<>();
        map.put("delFlag", "0");
        map.put("machineCode", vgdm1006.getMachineCode());
        map.put("partId", vgdm1006.getPartId());
        map.put("uuid", vgdm1006.getUuid());
        int count = super.count(VGDM1006.COUNT_REPEAT, map);
        if (count > 0) {
            throw new PlatException("[" + vgdm1006.getMachineName() + "-" + vgdm1006.getPartId() + "]配置已存在");
        }
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM1006 vgdm1006;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1006 = new VGDM1006();
                vgdm1006.fromMap(block.getRow(i));
                // 使用ValidationUtils进行基本的非空和数值校验
                ValidationUtils.validateEntity(vgdm1006);
                // 数据重复性校验
                this.checkDataRepeat(vgdm1006);
                // 默认状态
                vgdm1006.setStatus(MesConstant.Status.K10);
                vgdm1006.setDelFlag("0");
                // 转换为map
                Map updMap = vgdm1006.toMap();
                // 设置修改人
                RecordUtils.setRevisor(updMap);
                // 更新
                block.getRows().set(i, updMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM1006.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM1006 vgdm1006;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1006 = new VGDM1006();
                vgdm1006.fromMap(block.getRow(i));
                // 删除标记
                vgdm1006.setStatus(MesConstant.Status.K00);
                vgdm1006.setDelFlag("1");
                // 转换为map
                Map delMap = vgdm1006.toMap();
                // 设置修改人
                RecordUtils.setRevisor(delMap);
                // 设置行
                block.getRows().set(i, delMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM1006.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

}