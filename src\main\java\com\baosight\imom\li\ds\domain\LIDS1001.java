/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids1001;

import java.util.Map;

/**
* TLIDS1001
* 
*/
public class LIDS1001 extends Tlids1001 {
        public static final String QUERY = "LIDS1001.query";
        public static final String COUNT = "LIDS1001.count";
        public static final String COUNT_UUID = "LIDS1001.count_uuid";
        public static final String INSERT = "LIDS1001.insert";
        public static final String UPDATE = "LIDS1001.update";
        public static final String DELETE = "LIDS1001.delete";
        public static final String SEQ_ID = "TLIDS1001_SEQ01";

        public static final String UPDATE_DEPARTURE_TIME = "LIDS1001.updateDepartureTime";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS1001() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}