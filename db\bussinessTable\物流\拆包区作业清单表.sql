create table meli.tlids1001
(
    SEG_NO           varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE        varchar(12)  default ' ' not null comment '业务单元代代码',
    UNPACK_ORDER_ID  varchar(32)  default ' ' not null comment '拆包作业清单号',
    PACK_ID          varchar(50)  default ' ' null comment '捆包号',
    UNPACK_AREA_ID   varchar(20)  default ' ' not null comment '拆包区编号',
    UNPACK_AREA_NAME varchar(200) default ' ' not null comment '拆包区名称',
    STATUS           varchar(2)   default ' ' not null comment '状态(10到达、20离开)',
    ARRIVAL_TIME     varchar(32)              null comment '到达时间',
    DEPARTURE_TIME   varchar(32)              null comment '离开时间',
    REC_CREATOR      varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME  varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR      varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME  varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG     varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER      varchar(10)  default ' ' null comment '租户',
    DEL_FLAG         smallint     default 0   null comment '删除标记',
    UUID             varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids1001_UNPACK_ORDER_ID_uindex
        unique (UNPACK_ORDER_ID),
    constraint tlids1001_UUID_uindex
        unique (UUID)
)
    comment '拆包区作业清单表' collate = utf8_bin;

