package com.baosight.imom.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;

/**
 * 获取token
 */
public class TokenUtils extends ServiceBase {



    /**
     * redis服务
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final Logger log = LoggerFactory.getLogger(TokenUtils.class);
    public static final String CLIENT_SECRET = PlatApplicationContext.getProperty("eplat.security.client.clientSecret");
    private static final String TOKEN_URI = PlatApplicationContext.getProperty("eplat.security.client.accessTokenUri");
    public static final String CLIENT_ID = PlatApplicationContext.getProperty("eplat.security.client.clientId");


    /**
     *
     * 根据client_id、client_secret获取请求所需token
     * @return token
     */
    public static String getXplatToken(){
        try {
            //System.out.println("--------------------------开始getXplatToken---------------------------------");
            //token_url
            String tokenUrl = PlatApplicationContext.getProperty("eplat.security.client.accessTokenUri");
            String clientId = PlatApplicationContext.getProperty("service.token.imc.client_id");
            String clientSecret = PlatApplicationContext.getProperty("service.token.imc.client_secret");
            if (StringUtils.isBlank(tokenUrl)){
                throw new RuntimeException("请前往EDCC03页面配置url参数");
            }
            PostMethod postMethod = new PostMethod(tokenUrl);
            postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
            //参数设置，需要注意的就是里边不能传NULL，要传空字符串
            //key  value 形式参数
            NameValuePair[] data = {
                    new NameValuePair("client_id",clientId),
                    new NameValuePair("client_secret", clientSecret),
                    new NameValuePair("grant_type", "client_credentials"),
                    new NameValuePair("scope", "read")
            };
            postMethod.setRequestBody(data);
            HttpClient httpClient = new HttpClient();
            int response = httpClient.executeMethod(postMethod); // 执行POST方法
            String result = postMethod.getResponseBodyAsString();  //返回结果
            String access_token = "";
            if (response == 200 && result != null) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                access_token = (String) jsonObject.get("access_token");
            }
            //System.out.println("--------------------------------------------------------------token====="+access_token);
            return access_token;
        }catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     *
     * 根据client_id、client_secret获取请求所需token
     * @return token
     */
    public static String getImomToken(){
        try {
            //System.out.println("--------------------------开始getXplatToken---------------------------------");
            //token_url
            if (StringUtils.isBlank(TOKEN_URI)){
                throw new RuntimeException("请前往EDCC03页面配置url参数");
            }
            PostMethod postMethod = new PostMethod(TOKEN_URI);
            postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
            //参数设置，需要注意的就是里边不能传NULL，要传空字符串
            //key  value 形式参数
            NameValuePair[] data = {
                    new NameValuePair("client_id",CLIENT_ID),
                    new NameValuePair("client_secret", CLIENT_SECRET),
                    new NameValuePair("grant_type", "client_credentials"),
                    new NameValuePair("scope", "read")
            };
            postMethod.setRequestBody(data);
            HttpClient httpClient = new HttpClient();
            int response = httpClient.executeMethod(postMethod); // 执行POST方法
            String result = postMethod.getResponseBodyAsString();  //返回结果
            String access_token = "";
            if (response == 200 && result != null) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                access_token = (String) jsonObject.get("access_token");
            }
            //System.out.println("--------------------------------------------------------------token====="+access_token);
            return access_token;
        }catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }



    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url 发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param, String contentType,String token)
    {
        //System.out.println("--------------------------开始sendPost---------------------------------");
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            String urlNameString = url;
            URL realUrl = new URL(urlNameString);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Accept-Charset", contentType);
            conn.setRequestProperty("contentType", contentType);
            conn.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            if (StringUtils.isNotEmpty(token)) {
                conn.setRequestProperty("Xplat-Token", token); // 设置发送数据的token
                conn.setRequestProperty("token", token); // 设置发送数据的token
            }
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), contentType));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendPost ConnectException, url=" + url + ",param=" + param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendPost SocketTimeoutException, url=" + url + ",param=" + param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendPost IOException, url=" + url + ",param=" + param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendPost Exception, url=" + url + ",param=" + param, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("调用in.close Exception, url=" + url + ",param=" + param, ex);
            }
            log.info(Thread.currentThread().getName() + "============================ HTTP POST 请求结束 ============================");
        }
        return result.toString();
    }

    /***
     * 获取用户信息
     * @param code
     * @param access_token
     * @param appid
     * @param secret
     * @return
     */
    public static String getWxUserInfo(String code, String access_token, String appid, String secret) throws JSONException {
        String requestUrl = "http://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + access_token;
        System.out.println("发送post请求："+requestUrl);
        log.info("发送post请求："+requestUrl);
        org.json.JSONObject jsonParams = new org.json.JSONObject();
        jsonParams.put("code", code);
        //小程序端返回的code
        //发送post请求读取调用微信接口获取openid用户唯一标识
        String jsonParams1 = doPost(requestUrl, jsonParams);
        // JSONObject jsonObject = JSON.parseObject();
        return jsonParams1;
    }


    public static String doPost(String url, org.json.JSONObject param) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建 HttpClient
            HttpPost httpPost = new HttpPost(url);

            // 设置请求体为 JSON 字符串
            StringEntity entity = new StringEntity((param.toString()));
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/json");

            // 发送请求并获取响应
            HttpResponse response1 = httpClient.execute(httpPost);
            HttpEntity responseEntity = response1.getEntity();
            resultString = EntityUtils.toString(responseEntity);
            System.out.println(resultString);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultString;
    }
}
