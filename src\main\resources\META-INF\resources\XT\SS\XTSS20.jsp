<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true" resizable="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-groupCname" cname="用户组中文名" colWidth="3" />
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" needAuth="true" checkMode="single, cell"
                   isFloat="true" personal="true" sort="all" height="550"
                   serviceName="XTSS20" insertMethod="insert" updateMethod="update" deleteMethod="delete"
                   queryMethod="query">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                         required="true"
                         enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
            <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                         enable="false" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="ID" enable="false" width="235" align="left" hidden="true"/>
            <EF:EFColumn ename="userGroupId" cname="用户组ID" hidden="true" align="left"/>
            <EF:EFColumn ename="groupEname"  cname="用户组英文名"/>
            <EF:EFColumn ename="groupCname"  cname="用户组中文名"/>
            <EF:EFColumn ename="userPermission" cname="用户权限" width="300" required="true" align="left"/>
            <EF:EFColumn ename="recCreator" cname="创建人" enable="false" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" enable="false" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" displayType="datetime" editType="datetime"
                         width="150"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" enable="false"
                         align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" enable="false" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" enable="false" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" displayType="datetime" editType="datetime"
                         width="150"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" enable="false"
                         align="center"/>
        </EF:EFGrid>
    </EF:EFRegion>



    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    
    <%--用户组选择弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS2001" id="userGroupInfo" width="90%" height="70%"/>

    <EF:EFWindow url="${ctx}/web/XTSS2002" id="menuInfo" width="60%" height="70%"/>
</EF:EFPage>