<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-09-27 11:07:46
   		Version :  1.0
		tableName :${meliSchema}.tlirl0307 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CHECK_ID  VARCHAR   NOT NULL, 
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 DELIVER_TYPE  VARCHAR   NOT NULL, 
		 DELIVER_TYPE_NAME  VARCHAR   NOT NULL, 
		 PACK_ID  VARCHAR   NOT NULL, 
		 MAT_INNER_ID  VARCHAR, 
		 PROD_TYPE_ID  VARCHAR   NOT NULL, 
		 PROD_TYPE_NAME  VARCHAR   NOT NULL, 
		 SHOPSIGN  VARCHAR   NOT NULL, 
		 SPEC_DESC  VARCHAR   NOT NULL, 
		 WEIGHT  DECIMAL   NOT NULL, 
		 QUANTITY  DECIMAL   NOT NULL, 
		 CUSTOMER_ID  VARCHAR   NOT NULL, 
		 CUSTOMER_NAME  VARCHAR   NOT NULL, 
		 LOCATION_ID  VARCHAR   NOT NULL, 
		 LOCATION_NAME  VARCHAR   NOT NULL, 
		 HAND_POINT_ID  VARCHAR   NOT NULL, 
		 HAND_POINT_NAME  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0307">


	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0307">
		SELECT
				SEG_NO	as "segNo",  <!-- 账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				CHECK_ID	as "checkId",  <!-- 车辆流水号 -->
				VOUCHER_NUM	as "voucherNum",  <!-- 提单号（多个） -->
				DELIVER_TYPE	as "deliverType",  <!-- 交货方式（10：自提、20：代运） -->
				DELIVER_TYPE_NAME	as "deliverTypeName",  <!-- 交货方式名称 -->
				PACK_ID	as "packId",  <!-- 捆包号 -->
				MAT_INNER_ID	as "matInnerId",  <!-- 材料管理号 -->
				PROD_TYPE_ID	as "prodTypeId",  <!-- 品种附属码 -->
				PROD_TYPE_NAME	as "prodTypeName",  <!-- 品种附属码名称 -->
				SHOPSIGN	as "shopsign",  <!-- 牌号 -->
				SPEC_DESC	as "specDesc",  <!-- 规格 -->
				WEIGHT	as "weight",  <!-- 重量 -->
				QUANTITY	as "quantity",  <!-- 数量 -->
				CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
				CUSTOMER_NAME	as "customerName",  <!-- 客户名称 -->
				LOCATION_ID	as "locationId",  <!-- 库位代码 -->
				LOCATION_NAME	as "locationName",  <!-- 库位名称 -->
				HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
				HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
				DOCUMENTER_PERSON	as "documenterPerson",  <!-- 制单人工号 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0307 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNumList">
			VOUCHER_NUM in
			<iterate property="voucherNumList" open="("
					 close=")" conjunction=" , ">
				#voucherNumList[]#
			</iterate>
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0307 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>


	<select id="queryMobile" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct (SELECT xs_user.MOBILE
		FROM iplat4j.xs_user xs_user
		WHERE USER_ID = tlirl0307.DOCUMENTER_PERSON
		AND STATUS = '1'
		limit 1) as "mobile",
		(SELECT xs_user.USER_NAME
		FROM iplat4j.xs_user xs_user
		WHERE USER_ID = tlirl0307.DOCUMENTER_PERSON
		AND STATUS = '1'
		limit 1) as "userName",
		tlirl0307.VOUCHER_NUM as "voucherNum",
		tlirl0307.PACK_ID as "packId"
		from meli.tlirl0307 tlirl0307
		where SEG_NO = #segNo#
		AND VOUCHER_NUM =#voucherNum#
		<isNotEmpty prepend=" AND " property="packList">
			PACK_ID in
			<iterate property="packList" open="("
					 close=")" conjunction=" , ">
				#packList[]#
			</iterate>
		</isNotEmpty>

	</select>
<!--整单签收通知-->
	<select id="queryMobileAll" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct xs_user.MOBILE as "mobile"
		FROM iplat4j.xs_user xs_user
		WHERE USER_ID =#userId#
		AND STATUS = '1'
		limit 1

	</select>


	<select id="queryDeliverType" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct tlirl0308.VOUCHER_NUM  as "voucherNum",
		FINAL_STATION_LONGITUDE as "finalStationLongitude",
		FINAL_STATION_LATITUDE as "finalStationLatitude"
		from
		meli.tlirl0308 tlirl0308
		where 1 = 1
		and tlirl0308.SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="putoutIdStr">
			tlirl0308.PUTOUT_ID in
			<iterate property="putoutIdStr" open="("
					 close=")" conjunction=" , ">
				#putoutIdStr[]#
			</iterate>
		</isNotEmpty>

	</select>

	<select id="queryPutoutId" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct PACK_ID as "packId"
		from
		meli.tlirl0308 tlirl0308
		where 1 = 1
		and tlirl0308.SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="putoutIdStr">
			tlirl0308.PUTOUT_ID in
			<iterate property="putoutIdStr" open="("
					 close=")" conjunction=" , ">
				#putoutIdStr[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo1">
			tlirl0308.CAR_TRACE_NO =#carTraceNo1#
		</isNotEmpty>

	</select>
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="checkId">
			CHECK_ID = #checkId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deliverType">
			DELIVER_TYPE = #deliverType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deliverTypeName">
			DELIVER_TYPE_NAME = #deliverTypeName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeId">
			PROD_TYPE_ID = #prodTypeId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeName">
			PROD_TYPE_NAME = #prodTypeName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="shopsign">
			SHOPSIGN = #shopsign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specDesc">
			SPEC_DESC = #specDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="weight">
			WEIGHT = #weight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="quantity">
			QUANTITY = #quantity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationId">
			LOCATION_ID = #locationId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationName">
			LOCATION_NAME = #locationName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			HAND_POINT_NAME = #handPointName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0307 (SEG_NO,  <!-- 账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										CHECK_ID,  <!-- 车辆流水号 -->
										VOUCHER_NUM,  <!-- 提单号（多个） -->
										DELIVER_TYPE,  <!-- 交货方式（10：自提、20：代运） -->
										DELIVER_TYPE_NAME,  <!-- 交货方式名称 -->
										PACK_ID,  <!-- 捆包号 -->
										MAT_INNER_ID,  <!-- 材料管理号 -->
										PROD_TYPE_ID,  <!-- 品种附属码 -->
										PROD_TYPE_NAME,  <!-- 品种附属码名称 -->
										SHOPSIGN,  <!-- 牌号 -->
										SPEC_DESC,  <!-- 规格 -->
										WEIGHT,  <!-- 重量 -->
										QUANTITY,  <!-- 数量 -->
										CUSTOMER_ID,  <!-- 客户代码 -->
										CUSTOMER_NAME,  <!-- 客户名称 -->
										LOCATION_ID,  <!-- 库位代码 -->
										LOCATION_NAME,  <!-- 库位名称 -->
										HAND_POINT_ID,  <!-- 装卸点代码 -->
										HAND_POINT_NAME,  <!-- 装卸点名称 -->
										DOCUMENTER_PERSON,  <!-- 制单人工号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID, <!-- uuid -->
		D_USER_NUM,
		D_USER_NAME
										)		 
	    VALUES (#segNo#, #unitCode#, #checkId#, #voucherNum#, #deliverType#, #deliverTypeName#, #packId#, #matInnerId#,
		#prodTypeId#, #prodTypeName#, #shopsign#, #specDesc#, #weight#, #quantity#, #customerId#,
		#customerName#, #locationId#, #locationName#, #handPointId#, #handPointName#,#documenterPerson#, #recCreator#,
		#recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#,
		#remark#, #sysRemark#, #uuid#,#dUserNum#,#dUserName#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0307 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0307 
		SET 
		SEG_NO	= #segNo#,   <!-- 账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					CHECK_ID	= #checkId#,   <!-- 车辆流水号 -->  
					VOUCHER_NUM	= #voucherNum#,   <!-- 提单号（多个） -->  
					DELIVER_TYPE	= #deliverType#,   <!-- 交货方式（10：自提、20：代运） -->  
					DELIVER_TYPE_NAME	= #deliverTypeName#,   <!-- 交货方式名称 -->  
					PACK_ID	= #packId#,   <!-- 捆包号 -->  
					MAT_INNER_ID	= #matInnerId#,   <!-- 材料管理号 -->  
					PROD_TYPE_ID	= #prodTypeId#,   <!-- 品种附属码 -->  
					PROD_TYPE_NAME	= #prodTypeName#,   <!-- 品种附属码名称 -->  
					SHOPSIGN	= #shopsign#,   <!-- 牌号 -->  
					SPEC_DESC	= #specDesc#,   <!-- 规格 -->  
					WEIGHT	= #weight#,   <!-- 重量 -->  
					QUANTITY	= #quantity#,   <!-- 数量 -->  
					CUSTOMER_ID	= #customerId#,   <!-- 客户代码 -->  
					CUSTOMER_NAME	= #customerName#,   <!-- 客户名称 -->  
					LOCATION_ID	= #locationId#,   <!-- 库位代码 -->  
					LOCATION_NAME	= #locationName#,   <!-- 库位名称 -->  
					HAND_POINT_ID	= #handPointId#,   <!-- 装卸点代码 -->  
					HAND_POINT_NAME	= #handPointName#,   <!-- 装卸点名称 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
								TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>