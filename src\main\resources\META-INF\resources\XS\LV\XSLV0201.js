$(function () {
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });


    IPLATUI.EFGrid = {
        'result': {
            query: function () {
                //自定义查询eiInfo封装，增加orgId的参数
                var postInfo = EiInfo.build(document.body);
                if(!IPLAT.isUndefined(window.parent.permissionControl)
                    && typeof(window.parent.permissionControl)==="function"){
                    window.parent.permissionControl(postInfo);
                }
                return postInfo;
            }
        }
    };

    //点击确认按钮触发新增事件
    $("#addresource").click(function () {
        var checkRows = resultGrid.getCheckedRows();
        if (checkRows.length > 0) {
            var eiInfo = new EiInfo();
            var block = new EiBlock("result");
            block.getBlockMeta().addMeta(new EiColumn("objectId"));
            block.getBlockMeta().addMeta(new EiColumn("objectType"));
            for (var i = 0; i < checkRows.length; i++) {
                var model = checkRows[i];
                block.setCell(i, "objectId", model["objectId"]);
                block.setCell(i, "objectType", "RESOURCE");
            }
            eiInfo.addBlock(block);
            window.parent.addResource(eiInfo);
        }
    });

})

