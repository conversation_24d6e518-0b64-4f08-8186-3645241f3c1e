/**
 * Generate time : 2024-11-22 9:12:13
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm0104
 *
 */
public class Tvgdm0104 extends DaoEPBase {

    private String deviceCode = " ";        /* 分部设备代码*/
    private String deviceName = " ";        /* 分部设备名称*/
    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String relevanceType = " ";        /* 关联类型*/
    private String relevanceId = " ";        /* 关联ID*/
    private String eventStartTime = " ";        /* 事件开始时间*/
    private String eventEndTime = " ";        /* 事件结束时间*/
    private String eventContent = " ";        /* 事件内容*/
    private String handleContent = " ";        /* 处理内容*/
    private String handlePerson = " ";        /* 处理人*/
    private String handlePersonName = " ";        /* 处理人姓名*/
    private String handleTime = " ";        /* 处理时间*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("relevanceType");
        eiColumn.setDescName("关联类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("relevanceId");
        eiColumn.setDescName("关联ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eventStartTime");
        eiColumn.setDescName("事件开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eventEndTime");
        eiColumn.setDescName("事件结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eventContent");
        eiColumn.setDescName("事件内容");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handleContent");
        eiColumn.setDescName("处理内容");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handlePerson");
        eiColumn.setDescName("处理人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handlePersonName");
        eiColumn.setDescName("处理人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handleTime");
        eiColumn.setDescName("处理时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0104() {
        initMetaData();
    }

    /**
     * get the deviceCode - 分部设备代码
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the relevanceType - 关联类型
     * @return the relevanceType
     */
    public String getRelevanceType() {
        return this.relevanceType;
    }

    /**
     * set the relevanceType - 关联类型
     */
    public void setRelevanceType(String relevanceType) {
        this.relevanceType = relevanceType;
    }

    /**
     * get the relevanceId - 关联ID
     * @return the relevanceId
     */
    public String getRelevanceId() {
        return this.relevanceId;
    }

    /**
     * set the relevanceId - 关联ID
     */
    public void setRelevanceId(String relevanceId) {
        this.relevanceId = relevanceId;
    }

    /**
     * get the eventStartTime - 事件开始时间
     * @return the eventStartTime
     */
    public String getEventStartTime() {
        return this.eventStartTime;
    }

    /**
     * set the eventStartTime - 事件开始时间
     */
    public void setEventStartTime(String eventStartTime) {
        this.eventStartTime = eventStartTime;
    }

    /**
     * get the eventEndTime - 事件结束时间
     * @return the eventEndTime
     */
    public String getEventEndTime() {
        return this.eventEndTime;
    }

    /**
     * set the eventEndTime - 事件结束时间
     */
    public void setEventEndTime(String eventEndTime) {
        this.eventEndTime = eventEndTime;
    }

    /**
     * get the eventContent - 事件内容
     * @return the eventContent
     */
    public String getEventContent() {
        return this.eventContent;
    }

    /**
     * set the eventContent - 事件内容
     */
    public void setEventContent(String eventContent) {
        this.eventContent = eventContent;
    }

    /**
     * get the handleContent - 处理内容
     * @return the handleContent
     */
    public String getHandleContent() {
        return this.handleContent;
    }

    /**
     * set the handleContent - 处理内容
     */
    public void setHandleContent(String handleContent) {
        this.handleContent = handleContent;
    }

    /**
     * get the handlePerson - 处理人
     * @return the handlePerson
     */
    public String getHandlePerson() {
        return this.handlePerson;
    }

    /**
     * set the handlePerson - 处理人
     */
    public void setHandlePerson(String handlePerson) {
        this.handlePerson = handlePerson;
    }

    /**
     * get the handlePersonName - 处理人姓名
     * @return the handlePersonName
     */
    public String getHandlePersonName() {
        return this.handlePersonName;
    }

    /**
     * set the handlePersonName - 处理人姓名
     */
    public void setHandlePersonName(String handlePersonName) {
        this.handlePersonName = handlePersonName;
    }

    /**
     * get the handleTime - 处理时间
     * @return the handleTime
     */
    public String getHandleTime() {
        return this.handleTime;
    }

    /**
     * set the handleTime - 处理时间
     */
    public void setHandleTime(String handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * get the uuid - 唯一编码
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setRelevanceType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevanceType")), relevanceType));
        setRelevanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevanceId")), relevanceId));
        setEventStartTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eventStartTime")), eventStartTime));
        setEventEndTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eventEndTime")), eventEndTime));
        setEventContent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eventContent")), eventContent));
        setHandleContent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handleContent")), handleContent));
        setHandlePerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handlePerson")), handlePerson));
        setHandlePersonName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handlePersonName")), handlePersonName));
        setHandleTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handleTime")), handleTime));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("relevanceType", StringUtils.toString(relevanceType, eiMetadata.getMeta("relevanceType")));
        map.put("relevanceId", StringUtils.toString(relevanceId, eiMetadata.getMeta("relevanceId")));
        map.put("eventStartTime", StringUtils.toString(eventStartTime, eiMetadata.getMeta("eventStartTime")));
        map.put("eventEndTime", StringUtils.toString(eventEndTime, eiMetadata.getMeta("eventEndTime")));
        map.put("eventContent", StringUtils.toString(eventContent, eiMetadata.getMeta("eventContent")));
        map.put("handleContent", StringUtils.toString(handleContent, eiMetadata.getMeta("handleContent")));
        map.put("handlePerson", StringUtils.toString(handlePerson, eiMetadata.getMeta("handlePerson")));
        map.put("handlePersonName", StringUtils.toString(handlePersonName, eiMetadata.getMeta("handlePersonName")));
        map.put("handleTime", StringUtils.toString(handleTime, eiMetadata.getMeta("handleTime")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}