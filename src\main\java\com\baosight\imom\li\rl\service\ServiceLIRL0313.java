package com.baosight.imom.li.rl.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.li.rl.dao.LIRL0313;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


/**
 * @Author: 张博翔
 * @Description: ${车辆预约身份维护}
 * @Date: 2024/8/6 10:37
 * @Version: 1.0
 */
public class ServiceLIRL0313 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0313().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        String customerId = MapUtils.getString(queryMap, "customerId", "");
        String customerId2 = MapUtils.getString(queryMap, "customerId2", "");
        if (StringUtils.isNotEmpty(customerId)){
            queryMap.put("reservationIdentity",20);
        }else if(StringUtils.isNotEmpty(customerId2)){
            queryMap.put("reservationIdentity",10);
        }
        outInfo = super.query(inInfo, LIRL0313.QUERY);
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                String customerId = MapUtils.getString(hashMap, "customerId", "");//承运商
                //查询数据是否重复
                Map query = new HashMap();
                query.put("customerId", customerId);
                query.put("delFlag", 0);
                int count = super.count(LIRL0313.COUNT, query);
                if (count > 0) {
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ0313";

                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                // 创建人工号
                hashMap.put("recCreator", UserSession.getUserId());
                // 创建人姓名
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", UserSession.getUserId());
                // 修改人姓名
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                hashMap.put("delFlag", "0");
                hashMap.put("archiveFlag", "0");
                hashMap.put("tenantId", "BDAS");
            }
            inInfo = super.insert(inInfo, LIRL0313.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }



    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                String customerId = MapUtils.getString(hashMap, "customerId", "");//承运商
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0313> query = dao.query(LIRL0313.QUERY, map);
                for (LIRL0313 LIRL0313:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0313);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                //查询数据是否重复
                Map queryCount = new HashMap();
                queryCount.put("customerId",customerId);
                queryCount.put("delFlag", 0);
                queryCount.put("notUuid", uuid);
                int count = super.count(LIRL0313.COUNT, queryCount);
                if (count>0){
                    String massage = "数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0313.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0313> query = dao.query(LIRL0313.QUERY, map);
                for (LIRL0313 LIRL0313:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0313);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0313.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0313> query = dao.query(LIRL0313.QUERY, map);
                for (LIRL0313 LIRL0313:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0313);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", 20);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0313.UPDATE);
            EiInfo outInfo = new EiInfo();
            //调用IMC物流服务同步装车清单客户信息

            inInfo.set("customerList",listHashMap);
            inInfo.set(EiConstant.serviceId,"S_UC_PR_0418");
            try {
                outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return outInfo;

                }
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(e.getMessage());
                return outInfo;
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();

            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid",MapUtils.getString(hashMap,"uuid",""));
                List<LIRL0313> query = dao.query(LIRL0313.QUERY, map);
                for (LIRL0313 LIRL0313:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, LIRL0313);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", 10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0313.UPDATE);

            EiInfo outInfo = new EiInfo();
            //调用IMC物流服务同步装车清单客户信息

            inInfo.set("customerList",listHashMap);
            inInfo.set(EiConstant.serviceId,"S_UC_PR_0418");
            try {
                outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return outInfo;

                }
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } catch (Exception e) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(e.getMessage());
                return outInfo;
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

}
