v\:group, v\:rect, v\:imagedata, v\:oval, v\:line, v\:polyline,
	v\:stroke, v\:textbox {
	display: inline-block;
	background: transparent
}

.GooFlow {
	background: #F5F5F5;
	/*border: #ccc 1px solid;*/
	border: rgb(17,101,163) 1px solid;
	font: 0.8em Microsoft Yahei;
	-moz-user-select: none;
	-webkit-user-select: none;
	border-radius: 4px;
	color: #333
}

.GooFlow i {
	font: 1em;
}

.GooFlow_head {
	clear: both;
	height: 20px;
	padding: 1px;
	border-bottom: #00B4E1 2px solid;
}

.GooFlow_head label {
	font-weight: bold;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 18px;
	padding: 2px;
	width: 170px;
	text-align: center;
	background: #00B4E1;
	float: left;
	color: #fff;
	border-radius: 3px 0px 0px 0px;
	overflow: hidden;
	margin: -1px 4px 0px -1px;
}

.GooFlow_head span {
	float: left;
	height: 22px;
	width: 0px;
	overflow: hidden;
	border-left: #3892D3 1px solid;
	margin: 0px 3px;
}

.GooFlow_head_btn {
	display: block;
	border: 0px;
	height: 18px;
	width: 18px;
	cursor: default;
	padding: 1px;
	margin: 0px 3px;
	float: left;
	outline: none;
	blr: expression(this.onFocus = this.blur ());
}

.GooFlow_head_btn i {
	display: inline-block;
	overflow: hidden;
	width: 18px;
	height: 18px;
	border: 0px;
}

.GooFlow_head_btn:hover {
	border-radius: 2px;
	background: #FFBF00
}

.GooFlow_head_btn:hover i {
	
}

.GooFlow_tool {
	float: left;
	padding: 0px 3px;
	overflow: hidden;
	clear: left;
	border-right: #ccc 1px solid
}

.GooFlow_tool_div {
	overflow: hidden;
	border-radius: 2px;
	width: 24px;
	padding: 2px
}

.GooFlow_tool span {
	height: 0px;
	overflow: hidden;
	border-top: #ccc 1px solid;
	margin: 3px 1px;
	clear: both;
	display: block;
}

.GooFlow_tool_btn {
	display: block;
	border: 0px;
	height: 18px;
	width: 18px;
	cursor: default;
	padding: 2px;
	margin: 3px 1px;
	outline: none;
	blr: expression(this.onFocus = this.blur ());
}

.GooFlow_tool_btn i {
	display: block;
	overflow: hidden;
	width: 18px;
	height: 18px;
	border: 0px
}

.GooFlow_tool_btn:hover {
	border-radius: 3px;
	padding: 0px;
	border: #99B1CE 1px solid;
	width: 20px;
	height: 20px;
	background: #fff
}

.GooFlow_tool_btn:hover i {
	margin: 1px;
	border-radius: 2px
}

.GooFlow_tool_btndown {
	cursor: default;
	margin: 3px 1px;
	outline: none;
	blr: expression(this.onFocus = this.blur ());
	border-radius: 3px;
	padding: 2px;
	background: #FFBF00;
	display: block;
}

.GooFlow_tool_btndown i {
	display: block;
	overflow: hidden;
	width: 18px;
	height: 18px;
	border-radius: 2px
}

.GooFlow_work {
	float: right;
	margin: 0px 3px 3px 0px;
	border: #F5F5F5 1px solid;
	position: relative;
	overflow: scroll;
}

.GooFlow_work .GooFlow_work_inner {
	background-image: url(../images/gooflow_blank2.gif);
	position: relative;
	overflow: hidden;
}

.GooFlow_work .GooFlow_work_group {
	cursor: default;
	position: absolute;
	overflow: hidden;
	top: 0px;
	left: 0px
}

.GooFlow_work text {
	color: #fff
}

.GooFlow_area {
	cursor: default;
	position: absolute;
	overflow: hidden;
}

.GooFlow_area .lock {
	cursor: default;
}

.GooFlow_area .bg {
	cursor: move;
	filter: Alpha(Opacity = 30);
	-moz-opacity: 0.3;
	opacity: 0.3;
}

.GooFlow_work .lock .bg {
	cursor: default;
}

.GooFlow_area label {
	cursor: text;
	top: 1px;
	left: 1px;
	position: absolute;
	display: block;
	font-size: 12px;
	text-indent: 18px;
	height: 18px;
	line-height: 18px
}

.GooFlow_work .lock label {
	cursor: default;
}

.GooFlow_area i {
	display: block;
	height: 18px;
	widht: 18px;
	top: 0px;
	left: 0px;
	position: absolute;
	cursor: pointer
}

.GooFlow_work .area_red .bg {
	border: 1px solid red;
	background-color: #FF7865
}

.GooFlow_work .area_red label {
	color: red;
	background: url(../images/gooflow_bullet.png) no-repeat 1px 1px
}

.GooFlow_work .area_yellow .bg {
	border: 1px solid #CD925A;
	background-color: #FFD564
}

.GooFlow_work .area_yellow label {
	color: #FFBA1D;
	background: url(../images/gooflow_bullet.png) no-repeat 1px -16px
}

.GooFlow_work .area_blue .bg {
	border: 1px solid #347BB1;
	background-color: #549CDE
}

.GooFlow_work .area_blue label {
	color: #347BB1;
	background: url(../images/gooflow_bullet.png) no-repeat 1px -33px
}

.GooFlow_work .area_green .bg {
	border: 1px solid green;
	background-color: #84CA04
}

.GooFlow_work .area_green label {
	color: green;
	background: url(../images/gooflow_bullet.png) no-repeat 1px -50px
}

.GooFlow_work svg {
	display: block;
	position: absolute
}

.GooFlow_work v\:group {
	position: relative;
	display: block
}

.GooFlow_work v\:group v\:line {
	overflow: visible
}

.GooFlow_work v\:group v\:polyline {
	overflow: visible
}

.GooFlow_work v\:group div {
	cursor: text;
	position: absolute;
	overflow: visible;
	display: inline;
	float: left;
	white-space: nowrap
}

.GooFlow_work .draw {
	color: #ff3300
}



.GooFlow table {
	padding: 1px;
	border-radius: 2px
}

.GooFlow td {
	vertical-align: middle;
	text-align: center;
	padding: 0px;
	cursor: default;
	word-wrap: break-word;
	word-break: break-all
}

.GooFlow .ico {
	width: 18px;
	cursor: move
}

.GooFlow i {
	display: block;
	width: 18px;
	height: 18px;
	overflow: hidden
}

.GooFlow .item_round {
	border-radius: 11px;
	border: #C1DCFC solid 1px;
	width: 22px;
	height: 22px;
	overflow: visible
}

.GooFlow .item_round table {
	border: 0px;
	padding: 2px;
	width: 22px;
	height: 22px
}

.GooFlow .item_round .span {
	display: block;
	text-align: center;
	position: absolute;
	top: 24px;
	left: -28px;
	width: 80px;
	overflow: visible;
	text-align: center;
	padding: 0px;
	cursor: default;
	word-wrap: break-word;
	word-break: break-all
}

.GooFlow .item_mix {
	background: #B6F700;
	border-color: #C2DB4E;
	color: #fff
}

.GooFlow div .rs_right {
	overflow: hidden;
	position: absolute;
	right: -1px;
	top: -1px;
	height: 100%;
	width: 6px;
	cursor: w-resize
}

.GooFlow div .rs_bottom {
	overflow: hidden;
	position: absolute;
	left: -1px;
	bottom: -1px;
	width: 100%;
	height: 6px;
	cursor: n-resize
}

.GooFlow div .rs_rb {
	position: absolute;
	right: -1px;
	bottom: -1px;
	width: 9px;
	height: 9px;
	overflow: hidden;
	cursor: nw-resize;
	background: url(../images/gooflow_tip.png) no-repeat 0px -8px;
}

.GooFlow div .rs_close {
	position: absolute;
	right: 1px;
	top: 1px;
	width: 7px;
	height: 7px;
	overflow: hidden;
	cursor: pointer;
	background: url(../images/gooflow_tip.png) no-repeat 0px 0px
}

.GooFlow .rs_ghost {
	position: absolute;
	display: none;
	overflow: hidden;
	border: #8EA4C1 1px dashed;
	background: #D9E8FB;
	filter: Alpha(Opacity = 60);
	-moz-opacity: 0.6;
	opacity: 0.6;
	z-index: 10
}

.GooFlow .item_focus {
	border: #FF0000 1px solid
}

.GooFlow .item_mark {
	border: #ff3300 1px solid
}

.GooFlow .item_mark td {
	cursor: crosshair
}

.GooFlow textarea {
	position: absolute;
	border: #3892D3 1px solid;
	display: none;
	font: 1em Microsoft Yahei;
	overflow-y: visible;
	width: 100px;
	z-index: 10001
}

.GooFlow .GooFlow_line_oper {
	width: 70px;
	height: 15px;
	background-color: #D8E8FC;
	border: #7DA2CE 1px solid;
	position: absolute;
	filter: Alpha(Opacity = 50);
	-moz-opacity: 0.5;
	opacity: 0.5;
	z-index: 10000;
}

.GooFlow .GooFlow_line_mp {
	width: 9px;
	height: 9px;
	filter: Alpha(Opacity = 40);
	-moz-opacity: 0.4;
	opacity: 0.4;
	overflow: hidden;
	position: absolute;
	z-index: 9999;
	background: #333;
	cursor: crosshair
}

.GooFlow .GooFlow_line_move {
	filter: Alpha(Opacity = 50);
	-moz-opacity: 0.5;
	opacity: 0.5;
	overflow: hidden;
	position: absolute;
	z-index: 9999;
}

.GooFlow .GooFlow_line_oper i {
	display: inline-block;
	width: 15px;
	height: 15px;
	margin-left: 2px;
	cursor: pointer
}

.GooFlow .b_l1 {
	background: url(../images/GooFlow_line_oper.png) no-repeat 1px 1px
}

.GooFlow .b_l2 {
	background: url(../images/GooFlow_line_oper.png) no-repeat 1px -14px
}

.GooFlow .b_l3 {
	background: url(../images/GooFlow_line_oper.png) no-repeat 1px -29px
}

.GooFlow .b_x {
	background: url(../images/GooFlow_line_oper.png) no-repeat 1px -44px;
	margin-left: 10px
}

/*以下为图标样式（固定大小18px*18px，矢量字体大小16px），用户可自定义扩展自己的新矢量图标字体，写法参照以下的内容*/
.GooFlow .ico_cursor {
	background: url(../images/gooflow_icon2.png) no-repeat 2px -20px
}

/*.GooFlow .ico_start {*/
/*	background: url(../img/gooflow_icon2.png) no-repeat -19px -20px*/
/*}*/

/*.GooFlow .ico_end {*/
/*	background: url(../img/gooflow_icon2.png) no-repeat -39px -20px*/
/*}*/

.GooFlow .ico_fork {
	background: url(../images/gooflow_icon2.png) no-repeat -59px -20px
}

.GooFlow .ico_join {
	background: url(../images/gooflow_icon2.png) no-repeat -78px -20px
}

.GooFlow .ico_direct {
	background: url(../images/gooflow_icon2.png) no-repeat -137px -20px
}

.GooFlow .ico_group {
	background: url(../images/gooflow_icon2.png) no-repeat -96px -20px
}

.GooFlow .ico_recombination {
	background: url(../images/gooflow_icon2.png) no-repeat -116px -20px
}

.GooFlow .ico_node {
	background: url(../images/gooflow_icon2.png) no-repeat -19px -45px
}

.GooFlow .ico_task {
	background: url(../images/gooflow_icon2.png) no-repeat 2px -45px
}

.GooFlow .ico_chat {
	background: url(../images/gooflow_icon2.png) no-repeat -38px -45px
}

.GooFlow .ico_state {
	background: url(../images/gooflow_icon2.png) no-repeat -59px -45px
}

.GooFlow .ico_plug {
	background: url(../images/gooflow_icon2.png) no-repeat -135px -45px
}

.GooFlow .ico_menu {
	background: url(../images/gooflow_icon2.png) no-repeat 1px -65px
}

.GooFlow .ico_topo {
	background: url(../images/gooflow_icon2.png) no-repeat -117px -45px
}

.GooFlow .ico_sound {
	background: url(../images/gooflow_icon2.png) no-repeat -18px -65px
}

/*以下是内部用头部工具栏的样式*/
.GooFlow .ico_open {
	background: url(../images/gooflow_icon2.png) no-repeat -19px 1px
}

.GooFlow .ico_new {
	background: url(../images/gooflow_icon2.png) no-repeat 2px 1px
}

.GooFlow .ico_reload {
	background: url(../images/gooflow_icon2.png) no-repeat -97px 1px
}

.GooFlow .ico_save {
	background: url(../images/gooflow_icon2.png) no-repeat -39px 1px
}

.GooFlow .ico_undo {
	background: url(../images/gooflow_icon2.png) no-repeat -58px 1px
}

.GooFlow .ico_redo {
	background: url(../images/gooflow_icon2.png) no-repeat -78px 1px
}

.GooFlow .ico_mutiselect {
	background: url(../images/gooflow_icon2.png) no-repeat -156px 1px
}

.GooFlow .item_round .span {
	display: block;
	text-align: center;
	position: absolute;
	top: 24px;
	left: -28px;
	width: 80px;
	overflow: visible;
	text-align: center;
	padding: 0px;
	cursor: default;
	word-wrap: break-word;
	word-break: break-all
}

.GooFlow_item {
	position: absolute;
	border: #A1DCEB solid 1px;
	border-radius: 3px;
	/*background:#A1DCEB;
				border:#A1DCEB solid 1px;
				border-radius:3px;
				background-color:#C1DCFC;
				box-shadow:1px 1px 2px rgba(99,99,99,2);*/
}

.GooFlow_item {
	position: absolute;
	/*background: #A1DCEB;
	border: #A1DCEB solid 1px;*/
	background:rgb(10,69,118);
	border: rgb(10,69,118) solid 1px;
	border-radius: 3px;
	/*background-color: #C1DCFC;*/
	background-color: rgb(10,69,118);
	box-shadow: 1px 1px 2px rgba(99, 99, 99, 2);
}

/**去掉背景表格展示，背景颜色变为白色*/
.GooFlow_work .GooFlow_work_inner {
	background-image:url(../images/gooflow_blank2.gif);
	/*background: #fff !important;*/
	position: relative !important;
	overflow: hidden !important;
}

.GooFlow_head {
	clear: both;
	height: 20px;
	padding: 1px;
	border-bottom: #00B4E1 2px solid;
}
/*样式修改*/
.GooFlow .item_round {
	/*border-radius:11px;
				border:#C1DCFC solid 1px;*/
	/*width:22px;
				height:22px;*/
	width: 42px !important;
	height: 42px !important;
	overflow: visible
}

.GooFlow .item_round table {
	border: 0px;
	padding: 2px;
	/*width:22px;
				height:22px*/
	width: 40px !important;
	height: 40px !important;

}

.GooFlow .item_round table td {
	width: 40px !important;
	height: 40px !important;

}

.GooFlow .item_round table i {
	width: 40px !important;
	height: 40px !important;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
}

.GooFlow .item_round .span {
	top: 42px !important;
	left: -21px !important;
}

.GooFlow .item_round table .ico_start{
	background:url(../images/gooflow_ban.jpg) !important;

}
.GooFlow .item_round table .ico_end{
	background:url(../images/gooflow_juan.jpg) !important;

}
.GooFlow .item_round table .ico_bujian{
	background:url(../images/gooflow_bujian_s.png) !important;

}
.GooFlow .item_round table .ico_guan{
	background:url(../images/gooflow_guan_s.png) !important;

}
/*.GooFlow .item_round table .ico_task{
	background:url(../../img/images3.png) !important; 
}
.GooFlow .item_round table .ico_node{
	background:url(../../img/images4.png) !important; 
}*/
.GooFlow .ico_start{
	background:url(../images/gooflow_ban_t.jpg)  !important;
}
.GooFlow .ico_end{
	background:url(../images/gooflow_juan_t.jpg)  !important;
}
.GooFlow .ico_bujian{
	background:url(../images/gooflow_bujian_t.png)  !important;
}
.GooFlow .ico_guan{
	background:url(../images/gooflow_guan_t.png)  !important;
}
/*.GooFlow .ico_task{
	background:url(../../img/images3_icon.png)  !important;
}
.GooFlow .ico_node{
	background:url(../../img/images4_icon.png)  !important;
}*/

dl,dt,dd,li,ul{
	margin:0;
	padding:0;
}
input{
	font-size:14px;
}
			/*渐变色*/
.gradient{
	/*background:-webkit-linear-gradient(
		top,skyblue,lightblue,white
	)*/
	background:rgb(235,235,235);
}
			
.buttonDiv{
	float:left !important;
	width:28% !important;
	font-size:14px !important;
	margin-left:4% !important;
	height:25px !important;
	line-height:25px !important;
	text-align:center !important;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	background:rgb(240,240,240);
}

	/*零件信息*/
.formDivCss{
	float:left;
	/*高度宽度暂时写死，后续可通过js自适应*/
	height:498px;
	width:200px;
	border:1px solid rgb(17,101,163);
	/*圆角*/
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	margin-left:5px !important;
}
		
#formDiv input{
	border:1px solid #bfbfbf;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
}			

			/**/
.topDivCss{
    height:30px;
	width:100%;
	border-bottom:1px solid rgb(17,101,163);
	-webkit-border-top-left-radius: 5px;
	-webkit-border-top-right-radius: 5px;
	-moz-border-radius-topleft: 5px;
	-moz-border-radius-topright: 5px;
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;

}
.topDivCss div:nth-child(1){
	width:100%;
	height:30px;
	line-height:30px;
	text-align:center;
}
			
.topDivCss div:nth-child(2){
	float:left;
	height:20px;
	margin-top:10px;
	line-height:20px;
	width:22%;
	margin-left:2%;		
}
.topDivCss div:nth-child(3){
	float:left;
	height:20px;
	margin-top:10px;
	width:70%;
	/*background:#fff;*/
	/*border:1px groove #ccc;*/
}

.topDivCss div:nth-child(3) input{
	height:15px;
	width:100%;	
				/*border:1px groove #ccc;*/
}

			/*下拉框选择CSS*/
.selectCss{
	overflow: visible;
    margin-top:0;
	height:150px;/*高度*/
	background:#fff;/*背景颜色*/
	border:1px solid #ccc;/*边框*/
	z-index:999;/**/
	opacity: 0.99;/*透明度*/
	OVERFLOW-Y:auto;/*滚动条*/
	OVERFLOW-X:none;/*滚动条*/
}

.selectCss ul{
				 /*height:135px;*//*高度*/
	OVERFLOW-Y:auto;/*滚动条*/
	OVERFLOW-X:none;/*滚动条*/
}

.selectCss li{
	list-style:none;
	height:20px !important;
	line-height:20px !important;
	text-align:center;
	width:100%;
	font-size:14px;
	white-space:nowrap;
	overflow: hidden;
	text-overflow:ellipsis; 
}
.selectCss li:nth-child(odd){
	background:rgb(244,244,244);
}
.selectCss li:nth-child(even){
	background:#fff;
}
.clearDivCss{
	font-size:12px !important;
	width:95% !important;
	height:15px !important;
	text-align:right !important;
	line-height:15px !important;
}


			/*    position: absolute;*/
.bottomDivCss{
	height:417px;
	width:100%;
	-webkit-border-bottom-right-radius: 5px;
	-webkit-border-bottom-left-radius: 5px;
	-moz-border-radius-bottomright: 5px;
	-moz-border-radius-bottomleft: 5px;
	border-bottom-right-radius: 5px;
	border-bottom-left-radius: 5px;
}
			
			
			
			
			/*li设置*/
.bottomDivCss ul{
	width:100%;
}
.bottomDivCss ul li{
	line-height:35px;
	list-style:none;
	width:100%;
	height:35px;
	text-align:center;
}
.bottomDivCss ul li div{
	float:left;
	height:35px;
	line-height:35px;
}
.bottomDivCss ul li div:nth-child(1){
	font-size:16px;
	width:30%;
	text-align:right;
}
.bottomDivCss ul li div:nth-child(2){
	height:35px;
	width:60%;
}
.bottomDivCss ul li div:nth-child(2) input{
	width:100%;
	height:15px;
	margin:0;
	margin-top:5px;
}
.bottomDivCss ul li div:nth-child(3){
	height:35px;
	line-height:30px;
	width:10%;
	text-align:center;
	font-size:20px;
}


			/*iframe*/
.iframeDivCss{
	position:fixed;
	top:0;
	width:100%;
	height:100%;
	z-index:9999;
	opacity:0.99;
}
.iframeDivCss div{
	background:#333;
}

.iframeDivCss div{
	width:100%;
	background:#fff;
}
.iframeDivCss div iframe{
	width:100%;
	height:800px;
				
}
			
/*左侧工具栏样式修改*/
.GooFlow_tool_div{
	overflow:hidden;
	border-radius:2px;
	/*width:24px;*/
	width:42px;
	padding:2px
}

.GooFlow_tool_div i {
	display: block;
	width: 35px !important;
	height: 18px;
	overflow: hidden;
}
.GooFlow_tool_div .GooFlow_tool_btn {
	width:36px !important;
}
div.procedure {
	display: none;
}
div.main-info {
	display: block;
}
div.mbomId {
	display: none;
}
div.tmachine{
	visibility: hidden;
	max-height: 0;
}
div#process-operation {
	height: 100%;
}
div#process-operation > .block-content{
	height: 100% !important;
}
div.div-new-frame {
	border:2px solid  #DBEFFF;
	margin-bottom: 2em;
}