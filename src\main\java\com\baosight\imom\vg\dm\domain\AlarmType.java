package com.baosight.imom.vg.dm.domain;

import java.util.HashMap;
import java.util.Map;

/**
 * iplatBA平台报警类型相关工具类
 *
 * <AUTHOR> 郁在杰
 * @Description :iplatBA平台报警类型相关工具类
 * @Date : 2024/9/11
 * @Version : 1.0
 */
public enum AlarmType {
    <PERSON>OW<PERSON><PERSON>(0),
    <PERSON>OW(1),
    <PERSON>IG<PERSON>(2),
    <PERSON><PERSON><PERSON>HIGH(3),
    CHANGINGRATIO(4),
    DEVIATION(5),
    VARIABLEBIT(6),
    CHANGING01(7),
    CHANGING10(8),
    RANGE(9),
    STATUSKEEPON(10),
    STATUSKEEPOFF(11),
    UNDERRANGE(12),
    OVERRANGE(13),
    OK(14),
    FLOAT(15),
    IO(16),
    COMM(17),
    DEVICE(18),
    STATION(19),
    ACCESS(20),
    NODATA(21),
    NOXDATA(22),
    <PERSON><PERSON><PERSON><PERSON>(23),
    <PERSON><PERSON><PERSON>(24),
    <PERSON><PERSON><PERSON><PERSON>_TIMER(25),
    SY<PERSON>SG(26),
    NETMSG(27),
    OPRMSG(28),
    <PERSON>IN<PERSON>_MSG(29),
    USER_DEF0(30),
    USER_DEF1(31),
    USER_DEF2(32),
    USER_DEF3(33),
    USER_DEF4(34),
    USER_DEF5(35),
    USER_DEF6(36),
    USER_DEF7(37),
    USER_DEF8(38),
    USER_DEF9(39),
    USER_DEF10(40),
    USER_DEF11(41),
    USER_DEF12(42),
    USER_DEF13(43),
    USER_DEF14(44),
    USER_DEF15(45),
    OTHER_ALM(46),
    COUNT(47),
    ALL(48);

    private final int type;
    private static Map<Integer, AlarmType> map = new HashMap<>();

    private AlarmType(int typePara) {
        this.type = typePara;
    }

    public int numberOfType() {
        return this.type;
    }

    public static AlarmType getAlarmType(int type) {
        return (AlarmType) map.get(type);
    }

    static {
        AlarmType[] var0 = values();
        int var1 = var0.length;
        for (int var2 = 0; var2 < var1; ++var2) {
            AlarmType e = var0[var2];
            map.put(e.type, e);
        }
    }

    public static int transAlmType(int almType) {
        int ucType = 1;
        if (almType == AlarmType.LOW.numberOfType()) {
            ucType = 2;
        } else if (almType == AlarmType.HIGH.numberOfType()) {
            ucType = 3;
        } else if (almType == AlarmType.HIGHHIGH.numberOfType()) {
            ucType = 4;
        } else if (almType == AlarmType.CHANGINGRATIO.numberOfType()) {
            ucType = 5;
        } else if (almType == AlarmType.VARIABLEBIT.numberOfType()) {
            ucType = 6;
        } else if (almType == AlarmType.DEVIATION.numberOfType()) {
            ucType = 7;
        } else if (almType == AlarmType.CHANGING01.numberOfType()) {
            ucType = 26;
        } else if (almType == AlarmType.CHANGING10.numberOfType()) {
            ucType = 27;
        } else if (almType == AlarmType.STATUSKEEPON.numberOfType()) {
            ucType = 44;
        } else if (almType == AlarmType.STATUSKEEPOFF.numberOfType()) {
            ucType = 45;
        } else if (almType == AlarmType.OTHER_ALM.numberOfType()) {
            ucType = 46;
        } else if (almType == AlarmType.USER_DEF0.numberOfType()) {
            ucType = 28;
        } else if (almType == AlarmType.USER_DEF1.numberOfType()) {
            ucType = 29;
        } else if (almType == AlarmType.USER_DEF2.numberOfType()) {
            ucType = 30;
        } else if (almType == AlarmType.USER_DEF3.numberOfType()) {
            ucType = 31;
        } else if (almType == AlarmType.USER_DEF4.numberOfType()) {
            ucType = 32;
        } else if (almType == AlarmType.USER_DEF5.numberOfType()) {
            ucType = 33;
        } else if (almType == AlarmType.USER_DEF6.numberOfType()) {
            ucType = 34;
        } else if (almType == AlarmType.USER_DEF7.numberOfType()) {
            ucType = 35;
        } else if (almType == AlarmType.USER_DEF8.numberOfType()) {
            ucType = 36;
        } else if (almType == AlarmType.USER_DEF9.numberOfType()) {
            ucType = 37;
        } else if (almType == AlarmType.USER_DEF10.numberOfType()) {
            ucType = 38;
        } else if (almType == AlarmType.USER_DEF11.numberOfType()) {
            ucType = 39;
        } else if (almType == AlarmType.USER_DEF12.numberOfType()) {
            ucType = 40;
        } else if (almType == AlarmType.USER_DEF13.numberOfType()) {
            ucType = 41;
        } else if (almType == AlarmType.USER_DEF14.numberOfType()) {
            ucType = 42;
        } else if (almType == AlarmType.USER_DEF15.numberOfType()) {
            ucType = 43;
        } else {
            ucType = almType;
        }
        return ucType;
    }
}
