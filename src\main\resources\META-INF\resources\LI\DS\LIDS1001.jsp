<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-unpackOrderId" cname="拆包作业清单号" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-packId" cname="捆包号" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unpackAreaId" cname="拆包区编号" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="unpackAreaInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-unpackAreaName"
                             popupTitle="拆包区查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-unpackAreaName" cname="拆包区名称" colWidth="3" disabled="true"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="到达" value="10"/>
                <EF:EFOption label="离开" value="20"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="unpackOrderId" cname="拆包作业清单号" align="center" width="150"
                             primaryKey="true" enable="false"/>
                <EF:EFColumn ename="packId" cname="捆包号" align="left" width="200" enable="false"/>
                <EF:EFColumn ename="unpackAreaId" cname="拆包区编号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="unpackAreaName" cname="拆包区名称" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="到达" value="10"/>
                    <EF:EFOption label="离开" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="arrivalTime" cname="到达时间" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="departureTime" cname="离开时间" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--拆包区弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS05" id="unpackAreaInfo" width="90%" height="60%"/>
</EF:EFPage>
