package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0301;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行车管理
 */
public class ServiceLIDS0301 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0301().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0301.QUERY, new LIDS0301());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 查询行车
     * S_LI_DS_0006
     * @param inInfo
     * @return
     */
    public EiInfo queryPDA(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map paramMap = inInfo.getAttr();
            String segNo = StringUtils.defaultString((String) paramMap.get("segNo"), "");
            String handPointId = StringUtils.defaultString((String) paramMap.get("handPointId"), "");
            String factoryBuilding = StringUtils.defaultString((String) paramMap.get("factoryBuilding"), "");
            String factoryArea = StringUtils.defaultString((String) paramMap.get("factoryArea"), "");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            
//            inInfo.set("segNo",segNo);
//            inInfo.set("handPointId",handPointId);
//            inInfo.set("factoryBuilding",factoryBuilding);//厂房
//            inInfo.set("factoryArea",factoryArea);//厂区
//            inInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
//            inInfo.set(EiConstant.methodName, "queryCraneResultEnd");
//            inInfo = XLocalManager.call(inInfo);
//            if(inInfo.getStatus() < EiConstant.STATUS_DEFAULT){
//                throw new RuntimeException("查询行车失败！");
//            }
//            List<Map> craneIdList = (List<Map>) inInfo.get("craneIdList");
            List<Map> craneIdList = new ArrayList<>();
            List<Map> lids0301List = new ArrayList<>();
            if (CollectionUtils.isEmpty(craneIdList)){
                HashMap queryMap = new HashMap();
                queryMap.put("segNo",segNo);
                List<LIDS0301> list = dao.query(LIDS0301.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(list)){
                    for (LIDS0301 lids0301 : list) {
                        Map map = lids0301.toMap();
                        lids0301List.add(map);
                    }
                }
                outInfo.set("list",lids0301List);
            }else {
                outInfo.set("list",craneIdList);
            }
            outInfo.setMsg("查询成功！");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //增加校验,同一行车编号只能绑定一个跨区
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", segNo);//系统账套
                    queryMap.put("craneId", MapUtils.getString(itemMap, "craneId"));//行车编码
                    int count = super.count(LIDS0301.COUNT, queryMap);
                    if (count > 0) {
                        throw new PlatException(MapUtils.getString(itemMap, "craneId") + ",同一行车编码只能绑定一个跨区!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0301.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //增加校验,同一行车编号只能绑定一个跨区
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", segNo);//系统账套
                    queryMap.put("craneId", MapUtils.getString(itemMap, "craneId"));//行车编码
                    int count = super.count(LIDS0301.COUNT, queryMap);
                    if (count > 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "craneId") + ",同一行车编码只能绑定一个跨区!");
                    }
                    //查询区域代码状态，判断非新增状态数据不可修改
                    queryMap.clear();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("craneId", MapUtils.getString(itemMap, "craneId"));
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    queryMap.put("status", "10");
                    count = super.count(LIDS0301.COUNT_UUID, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "craneId") + ",行车编码状态非新增状态，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0301.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("craneId", MapUtils.getString(itemMap, "craneId"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0301.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "craneId") + ",行车编码状态非新增状态，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0301.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo validateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可启用!");
                }
                //查询区域代码状态，判断非新增状态数据不可启用
                Map queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("craneId", MapUtils.getString(itemMap, "craneId"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0301.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "craneId")+",行车编码状态非新增状态，不可生效!");
                }
                //状态变更为生效
                itemMap.put("status", "20");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0301.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 反生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo deValidateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可反生效!");
                }
                //查询区域代码状态，判断非启用状态u
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("craneId", MapUtils.getString(itemMap, "craneId"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "20");
                int count = super.count(LIDS0301.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "craneId") + ",行车编码状态非生效状态，不可反生效!");
                }
                //状态变更为新增
                itemMap.put("status", "10");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0301.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行反生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
