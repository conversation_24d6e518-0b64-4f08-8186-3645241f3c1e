<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-26 9:06:18
   		Version :  1.0
		tableName :${meliSchema}.tlirl0305
		 SEG_NO  VARCHAR   NOT NULL,
		 UNIT_CODE  VARCHAR   NOT NULL,
		 QUEUE_NUMBER  VARCHAR   NOT NULL,
		 CAR_TRACE_NO  VARCHAR   NOT NULL,
		 VEHICLE_ID  VARCHAR   NOT NULL,
		 HAND_POINT_ID  VARCHAR   NOT NULL,
		 HAND_TYPE  VARCHAR   NOT NULL,
		 REC_CREATOR  VARCHAR   NOT NULL,
		 REC_CREATOR_NAME  VARCHAR   NOT NULL,
		 REC_CREATE_TIME  VARCHAR   NOT NULL,
		 REC_REVISOR  VARCHAR   NOT NULL,
		 REC_REVISOR_NAME  VARCHAR   NOT NULL,
		 REC_REVISE_TIME  VARCHAR   NOT NULL,
		 ARCHIVE_FLAG  SMALLINT   NOT NULL,
		 DEL_FLAG  SMALLINT   NOT NULL,
		 REMARK  VARCHAR   NOT NULL,
		 SYS_REMARK  VARCHAR   NOT NULL,
		 UUID  VARCHAR   NOT NULL,
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0305">
	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueNumber">
			QUEUE_NUMBER = #queueNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleId">
			VEHICLE_ID = #vehicleId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handType">
			HAND_TYPE = #handType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0305">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				VEHICLE_ID	as "vehicleId",  <!-- 车牌号 -->
				HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
				HAND_TYPE	as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0305 WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0305 WHERE 1=1
	</select>


	<select id="queryHandPointId" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT

		HAND_TYPE	as "handType"  <!-- 装卸类型(10 装 20卸 30装卸) -->

		FROM ${meliSchema}.tlirl0305 WHERE 1=1
		and SEG_NO	= #segNo#
		and HAND_POINT_ID	= #handPointId#

union

		SELECT

		HAND_TYPE	as "handType"  <!-- 装卸类型(10 装 20卸 30装卸) -->

		FROM ${meliSchema}.tlirl0404 WHERE 1=1
		and SEG_NO	= #segNo#
		and HAND_POINT_ID	= #handPointId#

	</select>
	<!--
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueNumber">
			QUEUE_NUMBER = #queueNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleId">
			VEHICLE_ID = #vehicleId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handType">
			HAND_TYPE = #handType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0305 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										QUEUE_NUMBER,  <!-- 顺序号 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										VEHICLE_ID,  <!-- 车牌号 -->
										HAND_POINT_ID,  <!-- 装卸点代码 -->
										HAND_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID  <!-- uuid -->
										)
	    VALUES (#segNo#, #unitCode#, #queueNumber#, #carTraceNo#, #vehicleId#, #handPointId#, #handType#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#, #remark#, #sysRemark#, #uuid#)
	</insert>

	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0305 WHERE
		SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleId">
			VEHICLE_ID = #vehicleId#
		</isNotEmpty>
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0305
		SET
		SEG_NO	= #segNo#,   <!-- 系统账套 -->
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					QUEUE_NUMBER	= #queueNumber#,   <!-- 顺序号 -->
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->
					VEHICLE_ID	= #vehicleId#,   <!-- 车牌号 -->
					HAND_POINT_ID	= #handPointId#,   <!-- 装卸点代码 -->
					HAND_TYPE	= #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
					REMARK	= #remark#,   <!-- 备注 -->
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->
					UUID	= #uuid#,   <!-- uuid -->
					TENANT_ID	= #tenantId#  <!-- 租户ID -->
			WHERE 	1=1
		and
		SEG_NO=#segNo#
		and
		HAND_POINT_ID=#handPointId#

	</update>


	<update id="updateHandPointId">
		UPDATE ${meliSchema}.tlirl0305
		set
		DEL_FLAG = '1',   <!-- 记录删除标记 -->
		STATUS	= '00'   <!-- 记录删除标记 -->
		WHERE 	1=1
		and
		SEG_NO=#segNo#
		and
		HAND_POINT_ID=#handPointId#
	</update>

	<update id="updateTargetHandPointId">
		UPDATE ${meliSchema}.tlirl0305
		set
		HAND_POINT_ID=#targetHandPointId#
		WHERE 	1=1
		and
		SEG_NO=#segNo#
		and
		CAR_TRACE_NO=#carTraceNo#
		and
		VEHICLE_ID=#vehicleNo#
		and
		HAND_POINT_ID=#oldTargetHandPointId#

	</update>

	<!--查询装卸表装卸货状态-->
	<select id="queryLoadingUnloadingStatus" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct
		a.HAND_POINT_ID as "handPointId",<!-- 装卸点代码 -->
		a.HAND_TYPE as "handType"
		from
		${meliSchema}.tlirl0305 a
		where
		a.SEG_NO = #segNo#
		and a.CAR_TRACE_NO = #carTraceNo#
		and a.VEHICLE_ID = #vehicleId#
		order by HAND_TYPE desc

	</select>

	<!--有提单，装卸业务查找卸货点-->
	<select id="queryLoadingUnloading" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct a.HAND_POINT_ID as "handPointId",
		       a.FACTORY_AREA as "factoryArea",
		a.VEHICLE_NUMER as "vehicleNumer"
		from meli.tlirl0304 a,
			 meli.tlirl0309 b
		where a.SEG_NO = #segNo#
		  and a.HAND_POINT_ID = b.HAND_POINT_ID
		  and a.STATUS = '30'
		  and b.STATUS = '30'
		  and b.BUSINESS_TYPE='10'
		<isNotEmpty prepend=" AND " property="statusFlag">
			 exists(
			select 1 from
			meli.tlirl0315  tlirl0315 where tlirl0315.SEG_NO=b.SEG_NO
			AND tlirl0315.HAND_POINT_ID=b.HAND_POINT_ID
			AND tlirl0315.STATUS='10')
		</isNotEmpty>
		order by a.VEHICLE_NUMER desc

	</select>

	<!--有提单，装卸业务查找卸货点-->
	<select id="queryLoadingHandPointId"
			resultClass="String">
		select a.HAND_POINT_ID as "handPointId"
		from meli.tlirl0304 a
		where a.SEG_NO = #segNo#
		  and a.STATUS = '30'
		  and a.LOAD_FLAG = '1'
	</select>
	<!--车辆进厂登记修改车辆排队叫号装卸点子表-->
	<update id="updateProcPreHandPoint">
		UPDATE ${meliSchema}.tlirl0305
		SET
		<isNotEmpty property="queueNumber">
			QUEUE_NUMBER	= #queueNumber#,   <!-- 顺序号 -->
		</isNotEmpty>
		HAND_TYPE	= #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO = #segNo#
		and VEHICLE_ID = #vehicleId#
		and CAR_TRACE_NO = #carTraceNo#
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
	</update>
</sqlMap>