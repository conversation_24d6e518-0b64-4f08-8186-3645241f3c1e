<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu2_status-0-windowId" cname="ID" type="hidden"/>
            <EF:EFInput ename="inqu2_status-0-scrapType" cname="利用材种类" colWidth="3" ratio="4:8" />
            <EF:EFInput ename="inqu2_status-0-bigTypeDesc" cname="种类描述" colWidth="3" ratio="4:8" />
            <EF:EFInput ename="inqu2_status-0-partId" cname="物料号" colWidth="3" ratio="4:8" />
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true">
                <EF:EFColumn ename="scrapType" cname="利用材种类" readonly="true" align="center" enable="false"/>
                <EF:EFColumn ename="bigTypeDesc" cname="种类描述" readonly="true" align="center" enable="false"/>
                <EF:EFColumn ename="partId" cname="物料号" readonly="true" align="center" enable="false"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>
</EF:EFPage>
