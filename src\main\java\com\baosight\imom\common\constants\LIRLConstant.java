package com.baosight.imom.common.constants;

import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;

import java.math.BigDecimal;

/**
 * @Author: lifeng
 * @Description: 加工中心厂内精细化
 * @Date: 2022/8/12 13:59
 */
public class LIRLConstant {


    /**
     * 状态
     * 10：添加
     * 20：审核
     * 00：取消
     */
    public static final String STATUS_ADD = "10";
    public static final String STATUS_EXAM = "20";
    public static final String STATUS_CANCEL = "00";


    /**
     * 系统来源
     * 10：系统
     * 20：车载APP
     * 30：员工PDA
     */
    public static final String SOURCE_SYSTEM = "10";
    public static final String SOURCE_APP = "20";
    public static final String SOURCE_PDA = "30";
    public static final String SOURCE_ACCESS_CONTROL = "40";

    /**
     * 库位状态
     * 30：启用
     * 99：停用
     */
    public static final String STATUS_TURN_ON = "30";
    public static final String STATUS_TURN_OFF = "99";

    /**
     * 交货方式
     * 10：代运
     * 20：自提
     */
    public static final String DELIVER_TYPE_CARRY = "10";
    public static final String DELIVER_TYPE_SELF = "20";

    /**
     * 配车单状态
     * 10：已配车
     * 11：已签到
     * 20：排队中
     * 21：允许进厂
     * 30：已进厂
     * 31：装货中
     * 32：装货完成
     * 33：卸货中
     * 34：卸货完成
     * 35：已出库
     * 36：验货完成
     * 37：已出厂
     * 40：运输中
     * 45：已到达
     * 50：已签收
     */
    public static final String BILL_STATUS_CREATE = "10";
    public static final String BILL_STATUS_REPORT = "11";
    public static final String BILL_STATUS_LINE_UP = "20";
    public static final String BILL_STATUS_ALLOW_ENTER = "21";
    public static final String BILL_STATUS_ENTER_FACTORY = "30";
    public static final String BILL_STATUS_LOADING = "31";
    public static final String BILL_STATUS_LOADING_COMPLETE = "32";
    public static final String BILL_STATUS_UNLOADING = "33";
    public static final String BILL_STATUS_UNLOADING_COMPLETE = "34";
    public static final String BILL_STATUS_DELIVERED = "35";
    public static final String BILL_STATUS_INSPECTION_COMPLETE = "36";
    public static final String BILL_STATUS_EX_FACTORY = "37";
    public static final String BILL_STATUS_IN_TRANSIT = "40";
    public static final String BILL_STATUS_ARRIVE = "45";
    public static final String BILL_STATUS_SIGNED_FOR = "50";


    /**
     * 操作状态
     * 00：登录
     * 01：更换信息(组织、厂区、仓库)
     * 03：修改装卸点
     * 09：退出登录
     * 10：生成配车单
     * 11：修改配车单
     * 12：删除配车单
     * 17：签到
     * 20：人工排队
     * 21：取消排队
     * 30：入库
     * 31：出库
     * 32：装车开始
     * 33：卸车开始
     * 34：装卸结束
     * 35：下一步
     * 36：验货
     * 37：倒库
     * 38：盘库
     * 39：进厂
     * 40：出厂
     * 45：到达
     * 50：签收
     */
    public static final String OPERATE_STATUS_LOGIN = "00";
    public static final String OPERATE_STATUS_UPDATE_INFO = "01";
    public static final String OPERATE_STATUS_UPDATE_LOADING = "03";
    public static final String OPERATE_STATUS_UN_LOGIN = "09";
    public static final String OPERATE_STATUS_GENERATE_BILL = "10";
    public static final String OPERATE_STATUS_UPDATE_BILL = "11";
    public static final String OPERATE_STATUS_DELETE_BILL = "12";
    public static final String OPERATE_STATUS_SIGN_IN = "17";
    public static final String OPERATE_STATUS_MANUAL_QUEUE = "20";
    public static final String OPERATE_STATUS_DELETE_QUEUE = "21";
    public static final String OPERATE_STATUS_IN_WAREHOUSE = "30";
    public static final String OPERATE_STATUS_OUT_WAREHOUSE = "31";
    public static final String OPERATE_STATUS_LOADING_START = "32";
    public static final String OPERATE_STATUS_UN_LOADING_START = "33";
    public static final String OPERATE_STATUS_LOADING_END = "34";
    public static final String OPERATE_STATUS_NEXT_STEP = "35";
    public static final String OPERATE_STATUS_INSPECTION_COMPLETE = "36";
    public static final String OPERATE_STATUS_STOCK_TRANSFER = "37";
    public static final String OPERATE_STATUS_INVENTORY = "38";
    public static final String OPERATE_STATUS_IN_FACTORY = "39";
    public static final String OPERATE_STATUS_EX_FACTORY = "40";
    public static final String OPERATE_STATUS_ARRIVE = "45";
    public static final String OPERATE_STATUS_SIGN_FOR = "50";

    /**
     * 人工叫号标记
     * 0：否
     * 1：是
     */
    public static final String MANUAL_CALL_TYPE_Y = "1";
    public static final String MANUAL_CALL_TYPE_N = "0";

    /**
     * 岗位
     * 1：操作员
     * 2：司机
     */
    public static final String USER_SORT_OPERATION = "1";
    public static final String USER_SORT_DRIVER = "2";
    public static final String USER_SORT_CUST = "3";
    public static final String USER_SORT_CUST_DRIVER = "4";

    /**
     * 配车单类型
     * 10：有计划
     * 20：无计划
     */
    public static final String ALLOCATE_VEHICLE_TYPE_PLAN = "10";
    public static final String ALLOCATE_VEHICLE_TYPE_NO_PLAN = "20";


    /**
     * 排队状态
     * 00：排队取消
     * 10：排队中
     * 20：排队完成
     * 30：未排队
     */
    public static final String QUEUE_CANCEL = "00";
    public static final String QUEUE_ING = "10";
    public static final String QUEUE_COMPLETE = "20";
    public static final String QUEUE_NOTING = "30";

    /**
     * 进出厂标记
     * 10：进厂
     * 20：出厂
     */
    public static final String FACTORY_TYPE_ENTER = "10";
    public static final String FACTORY_TYPE_EX = "20";


    /**
     * 进出厂异常标记
     * 10：正常
     * 00：异常
     */
    public static final String EXCEPTION_MARKER_TRUE = "10";
    public static final String EXCEPTION_MARKER_FALSE = "00";

    /**
     * 装卸类型
     * 10：装
     * 00：卸
     * 20：装卸
     */
    public static final String HAND_TYPE_LOADING = "10";
    public static final String HAND_TYPE_UN_LOADING = "00";
    public static final String HAND_TYPE_ALL = "20";

    /**
     * 装卸状态
     * 00：撤销
     * 10：作业中
     * 20：装卸完成
     */
    public static final String LOADING_STATUS_ING = "10";
    public static final String LOADING_STATUS_CANCEL = "00";
    public static final String LOADING_STATUS_COMPLETE = "20";


    /**
     * 装卸顺序
     * 10：有序
     * 20：无序
     */
    public static final String LOADING_ORDER_HAVE = "10";
    public static final String LOADING_ORDER_NOTHING = "00";

    /**
     * 状态返回
     */
    public static final Integer STATUS_UPDATE_TOKEN = 2;


    /**
     * 状态计算用
     * 37 ： 出厂
     */
    public static final Integer NUM_BILL_EX_FACTORY = 37;


    /**
     * 出入库类型
     * 11：入库
     * 12：出库
     */
    public static final String STYLE_WAREHOUSE_IN = "11";
    public static final String STYLE_WAREHOUSE_OUT = "12";


    /**
     * 有效状态
     * 1：有效
     * 0：无效
     */
    public static final Integer VALID_STYLE_Y = 1;
    public static final Integer VALID_STYLE_N = 0;

    /**
     * 库位状态
     * 10:占用
     * 20:空闲
     */
    public static final String LOCATION_STATUS_OCCUPY = "10";
    public static final String LOCATION_STATUS_FREE = "20";

    /**
     * 库位形态
     * 10:卧式
     * 20:立式
     */
    public static final String LOCATION_FORM_HORIZONTAL = "10";
    public static final String LOCATION_FORM_VERTICAL = "20";


    /**
     * 库位位置
     * 10：上层
     * 20：下层
     */
    public static final String POS_DIR_UPPER = "10";
    public static final String POS_DIR_LOWER = "20";


    /**
     * 库位作业状态
     * 10：作业中
     */
    public static final String POINT_WORK_ING = "10";


    /**
     * 排队方式
     * 10：普通
     * 20：代运优先
     */
    public static final String QUEUE_TYPE_NORMAL = "10";
    public static final String QUEUE_TYPE_FORWARDING_PRIORITY = "20";

    /**
     * 排队类型
     * 10：厂区
     * 20：装卸点
     */
    public static final String QUEUE_KIND_IN_FACTORY = "10";
    public static final String QUEUE_KIND_IN_CHANNEL = "20";


    /**
     * 库位推荐类型
     * 10：点状
     * 20：条状
     * 21：条状特殊
     */
    public static final String MANAGE_STYLE_SPOT = "10";
    public static final String MANAGE_STYLE_STRIP = "20";
    public static final String MANAGE_STYLE_STRIP_SPECIAL = "21";


    /**
     * 清单类型
     * 10：准入
     * 20：准出
     */
    public static final String LIST_TYPE_ADMITTANCE = "10";
    public static final String LIST_TYPE_EXIST = "20";


    /**
     * redis key
     * redisLock_factory_queue_:加工中心排队队列
     * elim:bc:factory_vehicle_total_ :加工中心最大车辆数
     * elim:bc:factory_vehicle_now_:加工中心当前车辆
     * redisLock_factory_hand_point_:加工中心装卸点队列
     * elim:bc:factory_vehicle_total_ :加工中心装卸点最大车辆数
     * elim:bc:factory_vehicle_now_:加工中心装卸点当前车辆数
     * elim:bc:user_id_token_:登录用户token
     * elim:bc:factory_team_shift_:班组班次
     * redisLock_vehicle_loading_bill_ : 入库出库单号锁
     * elim:bc:vehicle_loading_bill_: 入库出库队列
     * redisLock_factory_hand_channel_:加工中心装卸通道队列
     * elim:bc:factory_channel_point_total_:加工中心装卸通道最大车辆
     * elim:bc:allocate_vehicle_detail_param_list_:配车单详情参数列表
     */
    public static final String REDIS_LOCK_FACTORY_QUEUE = "redisLock_factory_queue_";
    public static final String VEHICLE_FACTORY_TOTAL = "elim:bc:factory_vehicle_total_";
    public static final String VEHICLE_FACTORY_NOW = "elim:bc:factory_vehicle_now_";
    public static final String REDIS_LOCK_FACTORY_HAND_POINT = "redisLock_factory_hand_point_";
    public static final String HAND_POINT_FACTORY_TOTAL = "elim:bc:factory_hand_point_total_";
    public static final String REDIS_LOCK_HAND_POINT_NOW = "redisLock_hand_point_now_";
    public static final String HAND_POINT_FACTORY_NOW = "elim:bc:factory_hand_point_now_";
    public static final String HAND_POINT_FACTORY_VEHICLE_NOW = "elim:bc:factory_hand_point_vehicle_now_";
    public static final String HAND_CHANNEL_FACTORY_VEHICLE_NOW = "elim:bc:factory_hand_channel_vehicle_now_";
    public static final String USER_ID_TOKEN = "elim:bc:user_id_token_";
    public static final String TEAM_SHIFT = "elim:bc:factory_team_shift_";
    public static final String REDIS_LOCK_VEHICLE_LOADING_BILL = "redisLock_vehicle_loading_bill_";
    public static final String VEHICLE_LOADING_BILL = "elim:bc:vehicle_loading_bill_";
    public static final String REDIS_LOCK_PACK_LOCATION_ADD = "redisLock_pack_location_add_";
    public static final String REDIS_LOCK_WAREHOUSE_LOCATION_RECOMMENDATION = "redisLock_warehouse_location_recommend_";
    public static final String WAREHOUSE_LOCATION_RECOMMENDATION = "elim:bc:warehouse_location_recommend_";
    public static final String REDIS_LOCK_FACTORY_HAND_CHANNEL = "redisLock_factory_hand_channel_";
    public static final String REDIS_LOCK_HAND_CHANNEL_NOW = "redisLock_hand_channel_now_";
    public static final String HAND_CHANNEL_FACTORY_TOTAL = "elim:bc:factory_hand_channel_total_";
    public static final String HAND_CHANNEL_FACTORY_NOW = "elim:bc:factory_hand_channel_now_";
    public static final String ACCESS_CONTROL_EQUIPMENT = "elim:bc:access_control_equipment_";
    public static final String ALLOCATE_VEHICLE_DETAIL_PARAM_LIST = "elim:bc:allocate_vehicle_detail_param_list_";
    public static final String ALLOCATE_ESTIMATED_TIME = "elim:bc:allocate_estimated_time_";
    public static final String REDIS_LOCK_ALLOCATE_ESTIMATED_TIME = "redisLock_allocate_estimated_time_";
    public static final String ALLOCATE_NO_TIME_KEY = "elim:bc:allocate_no_time_key_";
    public static final String REDIS_LOCK_PDA_USER_CACHE = "redisLock_pda_user_cache_";
    public static final String PDA_USER_CACHE = "elim:bc:pda_user_cache_";
    public static final String REDIS_LOCK_MOVE_VERSION = "redisLock_move_version";
    public static final String PDA_UNPACKED_CACHE = "elim:bc:pda_unpacked_cache_";
    public static final String PDA_MAKE_WAREHOUSE = "elim:bc:pda_make_warehouse_";
    public static final String REDIS_LOCK_PDA_UNPACKED = "redisLock_pda_unpacked_";
    public static final String PDA_MESSAGE_AUTHENTICATION_CODE = "elim:bc:pda_message_authentication_code_";

    public static final String R_UC_PR_0101 = "R_R_UC_PR_0101_1";

    public static final String R_UC_PR_0306 = "R_R_UC_PR_0306_1";

    public static final String R_UC_PR_0307 = "R_R_UC_PR_0307_1";

    public static final String R_UC_PR_0209 = "R_R_UC_PR_0209_1";

    public static final String R_UC_PR_0308 = "R_R_UC_PR_0308_1";

    public static final String R_UC_PR_0309 = "R_R_UC_PR_0309_1";

    public static final String R_UC_PR_0310 = "R_R_UC_PR_0310_1";

    public static final String R_UC_PR_0312 = "R_R_UC_PR_0312_1";

    /**
     * 平台推送短信服务
     */
    public static final String S_EPLAT_04 = "S_EPLAT_04";

    /**
     * FTP 标签打印相关信息
     */
    public static final String PDA_FTP_ADDRESS = PlatApplicationContext.getProperty("pdaFtpAddress");
    public static final String PDA_FTP_USER_NAME = PlatApplicationContext.getProperty("pdaFtpUserName");
    public static final String PDA_FTP_PASSWORD = PlatApplicationContext.getProperty("pdaFtpPassword");
    public static final String PDA_FTP_FILE_DIR = PlatApplicationContext.getProperty("pdaFtpFileDir");
    public static final String PDA_FTP_ADDRESS_PORT = PlatApplicationContext.getProperty("pdaFtpAddressPort");
    /**
     * FTP 标签打印相关信息
     */
    public static final String BQ_FTP_ADDRESS = PlatApplicationContext.getProperty("pdaFtpAddress");
    public static final String BQ_FTP_USER_NAME = PlatApplicationContext.getProperty("pdaFtpUserName");
    public static final String BQ_FTP_PASSWORD = PlatApplicationContext.getProperty("pdaFtpPassword");
    public static final String BQ_FTP_FILE_DIR = PlatApplicationContext.getProperty("pdaFtpFileDir");
    public static final String BQ_TEMPLATE_FILE_NUM = PlatApplicationContext.getProperty("pdaTemplateFileNum");

    /**
     * 库位使用状态
     * 启用：10
     * 停用：00
     */
    public static final String LOCATION_USE_STATUS_ENABLE_CODE = "10";
    public static final String LOCATION_USE_STATUS_DISABLE_CODE = "00";
    public static final String LOCATION_USE_STATUS_DISABLE_CODE_NEW = "20";
    public static final String LOCATION_USE_STATUS_ENABLE_NAME = "启用";
    public static final String LOCATION_USE_STATUS_DISABLE_NAME = "停用";


    /**
     * 库位状态
     * 占用：10
     * 空闲：20
     */
    public static final String LOCATION_STATUS_OCCUPY_CODE = "10";
    public static final String LOCATION_STATUS_FREE_CODE = "20";
    public static final String LOCATION_STATUS_OCCUPY_NAME = "占用";
    public static final String LOCATION_STATUS_FREE_NAME = "空闲";

    /**
     * 库位位置
     * 上层：10
     * 下层：20
     */
    public static final String LOCATION_POS_DIR_UPPER_CODE = "10";
    public static final String LOCATION_POS_DIR_LOWER_CODE = "20";
    public static final String LOCATION_POS_DIR_UPPER_NAME = "上层";
    public static final String LOCATION_POS_DIR_LOWER_NAME = "下层";

    /**
     * 库位管理方式
     * 点状：10
     * 条状：20
     */
    public static final String LOCATION_MANAGEMENT_STYLE_SPOT_CODE = "10";
    public static final String LOCATION_MANAGEMENT_STYLE_STRIP_CODE = "20";
    public static final String LOCATION_MANAGEMENT_STYLE_STRIP_SPECIAL_CODE = "21";
    public static final String LOCATION_MANAGEMENT_STYLE_SPOT_NAME = "点状";
    public static final String LOCATION_MANAGEMENT_STYLE_STRIP_NAME = "条状";
    public static final String LOCATION_MANAGEMENT_STYLE_STRIP_SPECIAL_NAME = "条状特殊";

    /**
     * 库位形态
     * 卧式：10
     * 立式：20
     */
    public static final String LOCATION_FORM_SIT_CODE = "10";
    public static final String LOCATION_FORM_VERTICAL_CODE = "20";
    public static final String LOCATION_FORM_SIT_NAME = "卧式";
    public static final String LOCATION_FORM_VERTICAL_NAME = "立式";

    /**
     * 配车单最大重量
     */
    public static final BigDecimal VEHICLE_ALLOCATE_MAX_WEIGHT = BigDecimal.valueOf(55);

    /**
     * 贸易方式
     * 0 ： 一般贸易
     */
    public static final String TRADE_CODE_NORMAL = "0";

    /**
     * 装卸点使用状态
     * 使用中：10
     * 空闲：00
     */
    public static final String LOADING_USE_STATUS_ING_CODE = "10";
    public static final String LOADING_USE_STATUS_IDLE_CODE = "00";


    /**
     * 是否参与库位推荐
     * 1：不参与
     * 0：参与
     */
    public static final String LOCATION_DIVIDE_JOIN_CODE = "0";
    public static final String LOCATION_DIVIDE_NOT_JOIN_CODE = "1";
    public static final String LOCATION_DIVIDE_JOIN_NAME = "参与";
    public static final String LOCATION_DIVIDE_NOT_JOIN_NAME = "不参与";


    /**
     * 库位配置参数
     * 3.1416:圆周率
     * 0.305:内半径
     * 7.85:密度
     */
    public static final BigDecimal LOCATION_PI = new BigDecimal(3.1416);
    public static final BigDecimal LOCATION_HALF_BORE = new BigDecimal(0.305);
    public static final BigDecimal LOCATION_DENSITY = new BigDecimal(7.85);


    /**
     * 配置类型
     * 20 ： 库位
     * 10 ： 配车单
     * 50 ： 所有
     */
    public static final String PARAM_TYPE_LOCATION = "20";
    public static final String PARAM_TYPE_VEHICLE_ALLOCATE = "10";
    public static final String PARAM_TYPE_ALL = "50";

    /**
     * 配车单预计时间配置
     * DELIVERY_ARRIVE_TIME_DIFFER
     */
    public static final BigDecimal DELIVERY_ARRIVE_TIME_DIFFER = new BigDecimal(20);
    public static final BigDecimal ENTER_LOADING_TIME_DIFFER = new BigDecimal(10);
    public static final BigDecimal ARRIVE_ENTER_TIME_DIFFER = new BigDecimal(20);
    public static final BigDecimal LOADING_TRANS_TIME_DIFFER = new BigDecimal(30);
    public static final BigDecimal LOADING_LOADING_TIME_DIFFER = new BigDecimal(5);
    public static final BigDecimal LOADING_ROLL_TIME = new BigDecimal(6);
    public static final BigDecimal LOADING_NARROW_ROLL_TIME = new BigDecimal(6);
    public static final BigDecimal LOADING_BOARD_TIME = new BigDecimal(3.5);
    public static final BigDecimal NARROW_ROLL_MAX_WIDTH = new BigDecimal(400);


    /**
     * 配置类型
     * 0 ： 一般贸易
     * P ： 配送
     * 1 ： 来料加工
     * L ： 仓储
     */
    public static final String TRADE_TYPE_NORMAL = "0";
    public static final String TRADE_TYPE_DISTRIBUTION = "P";
    public static final String TRADE_TYPE_INCOMING = "1";
    public static final String TRADE_TYPE_WAREHOUSE = "L";

    /**
     * 库位高度
     */
    public static final BigDecimal LOCATION_HEIGHT = new BigDecimal(2800);

    /**
     * 配车单生成周期(小时)
     */
    public static final Integer ALLOCATE_VEHICLE_TIMED = 2;


    /**
     * 捆包类型
     * ROLL : 卷
     * NARROW_ROLL : 窄卷
     * BOARD : 板
     */
    public static final String PACK_TYPE_ROLL = "ROLL";
    public static final String PACK_TYPE_NARROW_ROLL = "NARROW_ROLL";
    public static final String PACK_TYPE_BOARD = "BOARD";


    /**
     * 板卷标记
     * 1 : 卷
     * 0 : 板
     */
    public static final String LOCATION_PACK_TYPE_ROLL = "1";
    public static final String LOCATION_PACK_TYPE_BOARD = "0";
    public static final String LOCATION_PACK_TYPE_ROLL_NAME = "卷";
    public static final String LOCATION_PACK_TYPE_BOARD_NAME = "板";


    /**
     * 库位性质
     * 0 : 实际库位
     */
    public static final String LOCATION_PROPERTIES_REAL = "0";

    /**
     * DPS状态
     * 10：进厂
     * 20：装车
     * 30：装车完成
     * 40：出厂
     * 50：已到达
     * 60：签收
     * 99：完成
     */
    public static final String DPS_STATUS_ENTER_FACTORY = "10";
    public static final String DPS_STATUS_LOADING = "20";
    public static final String DPS_STATUS_LOADING_COMPLETE = "30";
    public static final String DPS_STATUS_LOADING_EX_FACTORY = "40";
    public static final String DPS_STATUS_ARRIVE = "50";
    public static final String DPS_STATUS_SIGNED_FOR = "60";
    public static final String DPS_STATUS_COMPLETE = "99";


    /**
     * 捆包类型
     * 1:实捆包
     * 0:虚捆包
     */
    public static final String PACK_TYPE_REAL = "1";
    public static final String PACK_TYPE_EMPTY = "0";

    /**
     * 值集
     * remote_print_url : 远程打印地址
     * remote_print_report : 远程报表打印参数
     * remote_summary_type : 是否汇总打印
     * remote_print_label : 远程标签打印参数
     * remote_print_warehouse_in_report : 入库打印参数
     * remote_print_warehouse_out_report : 出库打印参数
     * cargo_damage_type : 货损类型
     */
    public static final String ITEM_CODE_REMOTE_PRINT_URL = "remote_print_url";
    public static final String ITEM_CODE_REMOTE_PRINT_REPORT = "remote_print_report";
    public static final String ITEM_CODE_REMOTE_SUMMARY_TYPE = "remote_summary_type";
    public static final String ITEM_CODE_REMOTE_PRINT_LABEL = "remote_print_label";
    public static final String ITEM_CODE_REMOTE_PRINT_WAREHOUSE_IN_REPORT = "remote_print_warehouse_in_report";
    public static final String ITEM_CODE_REMOTE_PRINT_WAREHOUSE_OUT_REPORT = "remote_print_warehouse_out_report";
    public static final String ITEM_CODE_REMOTE_PRINT_OUTBOUND_CODE_REPORT = "remote_print_outbound_code_report";
    public static final String ITEM_CODE_REMOTE_PRINT_EXIT_PERMIT_REPORT = "remote_print_exit_permit_report";
    public static final String ITEM_CODE_REMOTE_PRINT_WAREHOUSE_IN_URL = "remote_print_warehouse_in_url";
    public static final String ITEM_CODE_REMOTE_PRINT_DELIVERY_NOTE_URL = "remote_print_delivery_note_url";
    public static final String ITEM_CODE_REMOTE_PRINT_DELIVERY_NOTE_REPORT = "remote_print_delivery_note_report";
    public static final String ITEM_CODE_REMOTE_PRINT_WAREHOUSE_OUT_URL = "remote_print_warehouse_out_url";
    public static final String ITEM_CODE_REMOTE_PRINT_LABEL_URL = "remote_print_label_url";
    public static final String ITEM_CODE_REMOTE_PRINT_MACHINE_LABEL_URL = "remote_print_machine_label_url";
    public static final String ITEM_CODE_CARGO_DAMAGE_TYPE = "cargo_damage_type";
    public static final String ITEM_CODE_JOURNEY_WAREHOUSING = "journey_warehousing";
    public static final String ITEM_CODE_PRINT_LABEL_TEMPLATE = "print_label_template";
    public static final String ITEM_CODE_INTEGRATED_MACHINE_IP = "integrated_machine_ip";
    public static final String ITEM_CODE_DELIVERY_ORDER_TEMPLATE = "delivery_order_template";
    public static final String ITEM_CODE_DELIVERY_NOTE_TEMPLATE = "delivery_note_template";
    public static final String ITEM_CODE_LOAD_LOADING_POINT_NO = "load_loading_point_no";
    public static final String ITEM_CODE_UNLOAD_LOADING_POINT_NO = "unload_loading_point_no";
    public static final String ITEM_CODE_SPECIAL_TREATMENT_BUNDLING_SUB = "special_treatment_bundling_sub";
    public static final String ITEM_CODE_FACTORY_NOT_QUEUE = "factory_not_queue";
    public static final String ITEM_CODE_LOADING_BOARD_FACTORY_SUB = "loading_board_factory_sub";
    public static final String ITEM_CODE_QUEUE_WAIT_TIME = "queue_wait_time";
    public static final String ITEM_CODE_ZHY_TC_UKEY = "zhy_tc_ukey";
    public static final String ITEM_CODE_BATCH_LADING_TEAM = "batch_lading_team";
    public static final String ITEM_CODE_TEMPLATE_SPLIT_NUM = "template_split_num";
    public static final String ITEM_CODE_MULTIPLE_INVENTORY_WAREHOUSE = "multiple_inventory_warehouse";
    public static final String ITEM_CODE_REMOTE_PRINT_LABEL_NUM = "remote_print_label_num";
    public static final String ITEM_CODE_LOADING_LIST_WEIGHT_UNIT = "loading_list_weight_unit";
    public static final String ITEM_CODE_TEMPLATE_SCAN_PACK = "template_scan_pack";
    public static final String ITEM_CODE_TEMPLATE_NOTICE_MSG = "template_notice_msg";
    public static final String ITEM_CODE_OUT_FACTORY_CONFIRM_SPECIAL = "out_factory_confirm_special";
    public static final String ITEM_CODE_LADING_FORM_REPORT = "lading_form_report";
    public static final String ITEM_CODE_ENTER_FACTORY_NOT = "enter_factory_not";
    public static final String ITEM_CODE_EXIT_FACTORY_AUTOMATIC = "exit_factory_automatic";
    public static final String ITEM_CODE_OUT_FACTORY_ALLOCATE_VEHICLE = "out_factory_allocate_vehicle";
    public static final String ITEM_CODE_GENERATOR_ALLOCATE_VEHICLE_STEP = "generator_allocate_vehicle_step";
    public static final String ITEM_CODE_QUEUE_CHANNEL_REPORT = "queue_channel_report";
    public static final String ITEM_CODE_WHETHER_PRINT_OUT_WAREHOUSE_AUTO = "whether_print_out_warehouse_auto";
    public static final String ITEM_CODE_WHETHER_OUT_WAREHOUSE_SIGN = "whether_out_warehouse_sign";
    public static final String ITEM_CODE_BUSINESS_CUSTOM_SIGN = "business_custom_sign";
    public static final String ITEM_CODE_LOADING_NO_WAREHOUSE = "loading_no_warehouse";
    public static final String ITEM_CODE_PRINT_OUT_WAREHOUSE_LAST_CUSTOMER = "print_out_warehouse_last_customer";
    public static final String ITEM_CODE_PRINT_OUT_WAREHOUSE_AUTOMATIC = "print_out_warehouse_automatic";
    public static final String ITEM_CODE_PRINT_DELIVERY_NOTE_AUTOMATIC = "print_delivery_note_automatic";
    public static final String ITEM_CODE_PRINT_LABEL_LAST_CUSTOMER = "print_label_last_customer";
    public static final String ITEM_CODE_LADING_BILL_FORM_REPORT = "lading_bill_form_report";
    public static final String ITEM_CODE_UPLOAD_PACK_INFO = "upload_pack_info";
    public static final String ITEM_CODE_BALING_WEIGHT_DIFFERENCE_LOCATION = "baling_weight_difference_location";
    public static final String ITEM_CODE_WAREHOUSE_IN_SCAN_CLEAR_LOCATION = "warehouse_in_scan_clear_location";
    public static final String ITEM_CODE_UPDATE_ALLOCATE_VEHICLE_STATUS = "update_allocate_vehicle_status";
    public static final String ITEM_CODE_WAREHOUSE_IN_PRINT_FLAG = "warehouse_in_print_flag";
    public static final String ITEM_CODE_MULTI_SEG_NO_WAREHOUSE_FACTORY = "multi_seg_no_warehouse_factory";
    public static final String ITEM_CODE_WAREHOUSE_IN_VEHICLE_REQUIRED = "warehouse_in_vehicle_required";
    public static final String ITEM_CODE_PRINT_LABEL_PRODUCING_AREA_DESC = "print_label_producing_area_desc";
    public static final String ITEM_CODE_WAREHOUSE_IN_SCAN_NOT_AUTO_JOIN = "warehouse_in_scan_not_auto_join";
    public static final String ITEM_CODE_CARGO_DAMAGE_FLAG_Y = "cargo_damage_flag_y";
    public static final String ITEM_CODE_PRINT_OUT_WAREHOUSE_CALL_COUNT = "print_out_warehouse_call_count";
    public static final String ITEM_CODE_NOT_LOAD_EX_FACTORY_DEL = "not_load_ex_factory_del";
    public static final String ITEM_CODE_PACK_ID_SPECIAL_SPLIT_BEHIND = "pack_id_special_split_behind";
    public static final String ITEM_CODE_PRINT_OUT_WAREHOUSE_HOUSEHOLD_NO = "print_out_warehouse_household_no";
    public static final String ITEM_CODE_WAREHOUSE_OUT_JUMP_TO_MAIN = "warehouse_out_jump_to_main";
    public static final String ITEM_CODE_PDA_OPERATION_RECORD_LOCATION_DESC = "pda_operation_record_location_desc";
    public static final String ITEM_CODE_WAREHOUSE_OUT_SEARCH_LABEL = "warehouse_out_search_label";
    /**
     * 附件类型
     * 36:司机签字
     * 50:配车单签收
     * 90:装卸载确认
     */
    public static final String AFFIX_TYPE_PACK_WAREHOUSE_IN = "20";
    public static final String AFFIX_TYPE_UN_PACK = "21";
    public static final String AFFIX_TYPE_DRIVER_SIGN = "36";
    public static final String AFFIX_TYPE_BUSINESS_SIGN = "37";
    public static final String AFFIX_TYPE_GUARD_SIGN = "38";
    public static final String AFFIX_TYPE_ALLOCATE_SIGN = "50";
    public static final String AFFIX_TYPE_LOADING_CONFIRM = "90";
    public static final String AFFIX_TYPE_PUTIN_WAREHOUSE = "91";



    /**
     * 库存状态
     * 30:已出库
     */
    public static final String ENTITY_STOCK_STATUS_OUT = "30";

    /**
     * 是否厂内精细化
     *
     */
    public static final String FINE_FACTORY_FLAG_TRUE = "1";


    /**
     * 货损类型
     */
    public static final String CARGO_DAMAGE_TYPE_NAME = "进水痕迹,包装生锈,表面凹坑,包装破损,卷径变形,表面撞伤";


    /**
     * 操作记录类型
     * 39 : 出厂确认
     */
    public static final String OPERATOR_RECORD_CONFIRM_EX_FACTORY = "39";


    /**
     * 移动端类型
     * 20 : PDA
     */
    public static final String PROXY_TYPE_PDA = "20";

    /**
     * 3PL入库url
     */
    public static final String PL_JOURNEY_WAREHOUSING_URL = "/baosteel_3pl/services/TplStockInScanWebService?wsdl";


    /**
     * 3PL入库方法名称
     */
    public static final String PL_JOURNEY_WAREHOUSING_METHOD = "stockInScan";


    /**
     * 进出厂标记
     * 1、进厂
     * 2、出厂
     */
    public static final String PROXY_TYPE_ENTER_FACTORY = "1";
    public static final String PROXY_TYPE_EX_FACTORY = "2";


    /**
     * 在厂状态
     * 00、不在厂
     * 01、在厂
     */
    public static final String USE_STATUS_FACTORY_NO = "00";
    public static final String USE_STATUS_FACTORY_HAVE = "01";


    /**
     * PDA捆包记录类型
     * 10：入库
     * 11:现货入库
     * 20：出库
     * 30：倒库
     * 40：无单盘库
     * 41：有单盘库
     */
    public static final String PROXY_TYPE_WAREHOUSE_IN = "10";
    public static final String PROXY_TYPE_WAREHOUSE_NOW_IN = "11";
    public static final String PROXY_TYPE_WAREHOUSE_OUT = "20";
    public static final String PROXY_TYPE_WAREHOUSE_TRANSFER = "30";
    public static final String PROXY_TYPE_WAREHOUSE_NO_BILL_INVENTORY = "40";
    public static final String PROXY_TYPE_WAREHOUSE_INVENTORY = "41";

    /**
     * 状态
     */
    public static final String NOT_LOAD_EX_FACTORY_STATUS = "01";
    public static final String LOAD_EX_FACTORY_STATUS = "00";

    /**
     * 是否生成装车清单
     * 1：是
     * 0：否
     */
    public static final String IF_GENERATE_LOADING_LIST_Y = "1";
    public static final String IF_GENERATE_LOADING_LIST_N = "0";
}
