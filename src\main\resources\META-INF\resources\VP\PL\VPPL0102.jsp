<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-chipId" cname="芯片ID" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-staffName" cname="人员姓名" colWidth="3" placeholder="模糊条件"/>
            <EF:EFDateSpan startName="inqu_status-0-productDateStart" endName="inqu_status-0-productDateEnd"
                           startCname="操作日期起始" endCname="操作日期截止" required="true" readonly="true"
                           role="datetime" format="yyyyMMdd HH:mm:ss"  ratio="3:3"/>
            <EF:EFDatePicker format="yyyyMMdd" colWidth="3" readonly="true" maxLength="8"
                             ename="inqu_status-0-workingDate" cname="当班日期">
            </EF:EFDatePicker>
            <EF:EFSelect ename="inqu_status-0-teamId" cname="班组" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="甲班" value="10"/>
                <EF:EFOption label="乙班" value="20"/>
                <EF:EFOption label="丙班" value="30"/>
                <EF:EFOption label="丁班" value="40"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-workingShift" cname="班次" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="早班" value="10"/>
                <EF:EFOption label="中班" value="20"/>
                <EF:EFOption label="晚班" value="30"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-2" title="汇总(按员工)">
            <div id="groupByEmp">
                <EF:EFRegion id="groupByEmp" title="汇总(按员工)">
                    <EF:EFGrid isFloat="true" id="groupByEmp" blockId="groupByEmp" autoBind="false" autoDraw="no"
                               queryMethod="statisticGroupByEmp" needAuth="true" sort="all">
                        <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="workingDate" cname="当班日期" align="center" width="100" enable="false"/>
                        <EF:EFColumn ename="empNo" cname="员工工号" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="staffName" cname="人员姓名" align="center" width="200" enable="false"/>
                        <EF:EFColumn ename="chipId" cname="芯片ID" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFMultiSelectColumn ename="postResponsibility" cname="人员岗位职责" width="200"
                                                enable="false" align="center">
                            <EF:EFOption label="行车工" value="HC"/>
                            <EF:EFOption label="机组操作工" value="JZ"/>
                        </EF:EFMultiSelectColumn>
                        <EF:EFColumn ename="areaName" cname="区域名称" align="center" width="100" enable="false"/>
                        <EF:EFComboColumn ename="teamId" cname="班组" width="150" enable="false" align="center"
                                          required="true">
                            <EF:EFOption label="甲班" value="10"/>
                            <EF:EFOption label="乙班" value="20"/>
                            <EF:EFOption label="丙班" value="30"/>
                            <EF:EFOption label="丁班" value="40"/>
                        </EF:EFComboColumn>
                        <EF:EFComboColumn ename="workingShift" cname="班次" width="100" enable="false" align="center">
                            <EF:EFOption label="早班" value="10"/>
                            <EF:EFOption label="中班" value="20"/>
                            <EF:EFOption label="晚班" value="30"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="totalWorkingHours" cname="有效工作时间(H)" align="center" width="150"
                                     enable="false"/>
                        <EF:EFColumn ename="totalDepartureHours" cname="离开时间(H)" align="center" width="150"
                                     enable="false"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>
        <div id="info-3" title="汇总(按机组)">
            <div id="groupByArea">
                <EF:EFRegion id="groupByArea" title="汇总(按机组)">
                    <EF:EFGrid isFloat="true" id="groupByArea" blockId="groupByArea" autoBind="false" autoDraw="no"
                               queryMethod="statisticGroupByArea" needAuth="true" sort="all">
                        <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="workingDate" cname="当班日期" align="center" width="100" enable="false"/>
                        <EF:EFColumn ename="areaName" cname="区域名称" align="center" width="150" enable="false"/>
                        <EF:EFColumn ename="empNo" cname="员工工号" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="staffName" cname="人员姓名" align="center" width="200" enable="false"/>
                        <EF:EFComboColumn ename="teamId" cname="班组" width="150" enable="false" align="center"
                                          required="true">
                            <EF:EFOption label="甲班" value="10"/>
                            <EF:EFOption label="乙班" value="20"/>
                            <EF:EFOption label="丙班" value="30"/>
                            <EF:EFOption label="丁班" value="40"/>
                        </EF:EFComboColumn>
                        <EF:EFComboColumn ename="workingShift" cname="班次" width="150" enable="false" align="center">
                            <EF:EFOption label="早班" value="10"/>
                            <EF:EFOption label="中班" value="20"/>
                            <EF:EFOption label="晚班" value="30"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="totalWorkingHours" cname="有效工作时间(H)" align="center" width="150"
                                     enable="false"
                                     sumType="all"/>
                        <EF:EFColumn ename="totalDepartureHours" cname="离开时间(H)" align="center" width="150"
                                     enable="false"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>
        <div id="info-4" title="人员区域明细">
            <div id="personTimeInArea">
                <EF:EFRegion id="personTimeInArea" title="人员区域明细">
                    <EF:EFGrid isFloat="true" id="personTimeInArea" blockId="personTimeInArea" autoBind="false" autoDraw="no"
                               queryMethod="statisticPersonTimeInArea" needAuth="true" sort="all">
                        <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="workingDate" cname="当班日期" align="center" width="100" enable="false"/>
                        <EF:EFColumn ename="areaName" cname="区域名称" align="center" width="150" enable="false"/>
                        <EF:EFColumn ename="empNo" cname="员工工号" align="center" width="100" enable="false"
                                     hidden="true"/>
                        <EF:EFColumn ename="staffName" cname="人员姓名" align="center" width="200" enable="false"/>
                        <EF:EFComboColumn ename="teamId" cname="班组" width="150" enable="false" align="center"
                                          required="true">
                            <EF:EFOption label="甲班" value="10"/>
                            <EF:EFOption label="乙班" value="20"/>
                            <EF:EFOption label="丙班" value="30"/>
                            <EF:EFOption label="丁班" value="40"/>
                        </EF:EFComboColumn>
                        <EF:EFComboColumn ename="workingShift" cname="班次" width="150" enable="false" align="center">
                            <EF:EFOption label="早班" value="10"/>
                            <EF:EFOption label="中班" value="20"/>
                            <EF:EFOption label="晚班" value="30"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="totalWorkingHours" cname="有效工作时间(H)" align="center" width="150"
                                     enable="false"/>
                        <EF:EFColumn ename="totalDepartureHours" cname="离开时间(H)" align="center" width="150"
                                     enable="false"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>
        <div id="info-1" title="查询结果">
            <div id="result">
                <EF:EFRegion id="result" title="详细信息">
                    <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no"
                               needAuth="true" sort="all">
                        <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                                     enable="false"/>
                        <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                        <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                                     enable="false" hidden="true"/>
                        <EF:EFColumn ename="chipId" cname="芯片ID" align="center" width="150" enable="false"/>
                        <EF:EFColumn ename="staffName" cname="人员姓名" align="center" width="200" enable="false"/>
                        <EF:EFColumn ename="areaName" cname="区域名称" align="center" width="150" enable="false"/>
                        <EF:EFComboColumn ename="actionType" cname="动作类型" width="150" enable="false" align="center">
                            <EF:EFOption label="进入" value="1"/>
                            <EF:EFOption label="离开" value="2"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="workingHours" cname="有效工作时间(H)" align="center" width="150"
                                     enable="false"/>
                        <EF:EFColumn ename="departureHours" cname="离开时间(H)" align="center" width="150"
                                     enable="false"/>
                        <EF:EFColumn ename="workingTime" cname="操作时间" align="center" width="200" enable="false"
                                     parseFormats="['yyyyMMddHHmmss']" format="yyyy/MM/dd HH:mm:ss" editType="datetime"
                                     displayType="datetime"/>
                        <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                        <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                                     enable="false"/>
                        <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180"
                                     enable="false"/>
                        <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                        <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                                     enable="false"/>
                        <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180"
                                     enable="false"/>
                        <EF:EFColumn ename="serialNumber" cname="流水号" align="center" width="150" enable="false"/>
                        <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                                     enable="true" hidden="true"/>
                        <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>
    </EF:EFTab>
    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>
