/**
 * Generate time : 2025-04-24 9:41:11
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Tvgdm0808
 */
public class Tvgdm0808 extends DaoEPBase {

    private String overhaulPlanId = " ";        /* 检修计划编号*/
    @NotBlank(message = "设备位置不能为空")
    private String deviceLocation = " ";        /* 设备位置*/
    @NotBlank(message = "润滑点位置不能为空")
    private String lubricatePoint = " ";        /* 润滑点*/
    @NotNull(message = "润滑点数量不能为空")
    @Min(value = 1, message = "润滑点数量必须大于0")
    private Integer lubricateQty = 1;        /* 润滑点数量*/
    @NotNull(message = "加油量不能为空")
    @Min(value = 1, message = "加油量必须大于0")
    private Integer addQty = 1;        /* 加油量*/
    private String greaseSpec = " ";        /* 油脂规格*/
    private String lubricateCycle = " ";        /* 润滑周期*/
    private String lubricateRecord = " ";        /* 润滑记录*/
    private String remark = " ";        /* 备注*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("overhaulPlanId");
        eiColumn.setDescName("检修计划编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceLocation");
        eiColumn.setDescName("设备位置");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lubricatePoint");
        eiColumn.setDescName("润滑点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lubricateQty");
        eiColumn.setDescName("润滑点数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("addQty");
        eiColumn.setDescName("加油量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("greaseSpec");
        eiColumn.setDescName("油脂规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lubricateCycle");
        eiColumn.setDescName("润滑周期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lubricateRecord");
        eiColumn.setDescName("润滑记录");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0808() {
        initMetaData();
    }

    /**
     * get the overhaulPlanId - 检修计划编号
     *
     * @return the overhaulPlanId
     */
    public String getOverhaulPlanId() {
        return this.overhaulPlanId;
    }

    /**
     * set the overhaulPlanId - 检修计划编号
     */
    public void setOverhaulPlanId(String overhaulPlanId) {
        this.overhaulPlanId = overhaulPlanId;
    }

    /**
     * get the deviceLocation - 设备位置
     *
     * @return the deviceLocation
     */
    public String getDeviceLocation() {
        return this.deviceLocation;
    }

    /**
     * set the deviceLocation - 设备位置
     */
    public void setDeviceLocation(String deviceLocation) {
        this.deviceLocation = deviceLocation;
    }

    /**
     * get the lubricatePoint - 润滑点
     *
     * @return the lubricatePoint
     */
    public String getLubricatePoint() {
        return this.lubricatePoint;
    }

    /**
     * set the lubricatePoint - 润滑点
     */
    public void setLubricatePoint(String lubricatePoint) {
        this.lubricatePoint = lubricatePoint;
    }

    /**
     * get the lubricateQty - 润滑点数量
     *
     * @return the lubricateQty
     */
    public Integer getLubricateQty() {
        return this.lubricateQty;
    }

    /**
     * set the lubricateQty - 润滑点数量
     */
    public void setLubricateQty(Integer lubricateQty) {
        this.lubricateQty = lubricateQty;
    }

    /**
     * get the addQty - 加油量
     *
     * @return the addQty
     */
    public Integer getAddQty() {
        return this.addQty;
    }

    /**
     * set the addQty - 加油量
     */
    public void setAddQty(Integer addQty) {
        this.addQty = addQty;
    }

    /**
     * get the greaseSpec - 油脂规格
     *
     * @return the greaseSpec
     */
    public String getGreaseSpec() {
        return this.greaseSpec;
    }

    /**
     * set the greaseSpec - 油脂规格
     */
    public void setGreaseSpec(String greaseSpec) {
        this.greaseSpec = greaseSpec;
    }

    /**
     * get the lubricateCycle - 润滑周期
     *
     * @return the lubricateCycle
     */
    public String getLubricateCycle() {
        return this.lubricateCycle;
    }

    /**
     * set the lubricateCycle - 润滑周期
     */
    public void setLubricateCycle(String lubricateCycle) {
        this.lubricateCycle = lubricateCycle;
    }

    /**
     * get the lubricateRecord - 润滑记录
     *
     * @return the lubricateRecord
     */
    public String getLubricateRecord() {
        return this.lubricateRecord;
    }

    /**
     * set the lubricateRecord - 润滑记录
     */
    public void setLubricateRecord(String lubricateRecord) {
        this.lubricateRecord = lubricateRecord;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setOverhaulPlanId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulPlanId")), overhaulPlanId));
        setDeviceLocation(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceLocation")), deviceLocation));
        setLubricatePoint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lubricatePoint")), lubricatePoint));
        setLubricateQty(NumberUtils.toInteger(StringUtils.toString(map.get("lubricateQty")), lubricateQty));
        setAddQty(NumberUtils.toInteger(StringUtils.toString(map.get("addQty")), addQty));
        setGreaseSpec(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("greaseSpec")), greaseSpec));
        setLubricateCycle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lubricateCycle")), lubricateCycle));
        setLubricateRecord(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lubricateRecord")), lubricateRecord));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("overhaulPlanId", StringUtils.toString(overhaulPlanId, eiMetadata.getMeta("overhaulPlanId")));
        map.put("deviceLocation", StringUtils.toString(deviceLocation, eiMetadata.getMeta("deviceLocation")));
        map.put("lubricatePoint", StringUtils.toString(lubricatePoint, eiMetadata.getMeta("lubricatePoint")));
        map.put("lubricateQty", StringUtils.toString(lubricateQty, eiMetadata.getMeta("lubricateQty")));
        map.put("addQty", StringUtils.toString(addQty, eiMetadata.getMeta("addQty")));
        map.put("greaseSpec", StringUtils.toString(greaseSpec, eiMetadata.getMeta("greaseSpec")));
        map.put("lubricateCycle", StringUtils.toString(lubricateCycle, eiMetadata.getMeta("lubricateCycle")));
        map.put("lubricateRecord", StringUtils.toString(lubricateRecord, eiMetadata.getMeta("lubricateRecord")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}