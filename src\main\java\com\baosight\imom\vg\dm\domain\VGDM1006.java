package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm1006;

/**
 * 机组单包数量配置
 */
public class VGDM1006 extends Tvgdm1006 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM1006.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1006.count";
    /**
     * 查询条数
     */
    public static final String COUNT_REPEAT = "VGDM1006.countRepeat";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1006.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1006.update";
}
