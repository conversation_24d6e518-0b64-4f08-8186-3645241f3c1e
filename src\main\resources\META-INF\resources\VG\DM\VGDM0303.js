$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfo",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });

    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
        // 时间默认最近半小时
        const now = new Date();
        const startTime = new Date(now.getTime() - 30 * 60 * 1000);
        $("#inqu2_status-0-startTime").val(DateUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        $("#inqu2_status-0-endTime").val(DateUtils.format(now, "yyyy-MM-dd HH:mm:ss"));
    });
    // 监控列表
    const monitorMap = new Map();
    // 定时器
    let timer = null;
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId !== "info-1") {
                    if (monitorMap.size < 1) {
                        NotificationUtil({msg: "请先添加监控点"}, "error");
                        e.preventDefault();
                        return;
                    }
                } else {
                    // 停止定时器
                    clearTimer();
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 50//单页展示1000条数据
            },
            loadComplete: function (grid) {
                //修改
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0303", "update", true, null, null, false);
                });
                // 添加监控
                $("#MONITOR").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkedLength = checkedRows.length;
                    if (monitorMap.size + checkedLength > 5) {
                        NotificationUtil({msg: "最多只能添加5个监控点,目前已有" + monitorMap.size + "个监控点"}, "error");
                        return;
                    }
                    checkedRows.forEach((row) => {
                        monitorMap.set(row.tagIhdId.toString(), row);
                    });
                    $("#inqu2_status-0-tags").val(Array.from(monitorMap.values()).map((row) => row.tagDesc).join(","));
                    // 清空已有图表数据
                    const container = $("#result2>.block-content");
                    container.empty();
                    chartMap.clear();
                    chartData = [];
                    // 成功提醒
                    NotificationUtil("添加成功", "success");
                });
                // 停止按钮禁用
                $("#STOP").attr("disabled", true);
            }
        }
    };
    // 清空监控按钮
    $("#CLEAR").on("click", function (e) {
        monitorMap.clear();
        $("#inqu2_status-0-tags").val("");
        tab_Strip.select(0);
        clearTimer();
        chartMap.clear();
        chartData = [];
        // 成功提醒
        NotificationUtil("清空成功", "success");
    });
    // 刷新按钮
    $("#QUERY2").on("click", function (e) {
        const queryType = $('input[name="inqu2_status-0-queryType"]:checked').val();
        if (!queryType) {
            NotificationUtil({msg: "请先选择数据类型"}, "error");
            return;
        }
        clearTimer();
        chartData = [];
        lastQueryTime = null;
        if (queryType === "1") {
            // 实时数据
            const refreshType = $("#inqu2_status-0-refreshType").val();
            // 设置定时器
            timer = setInterval(() => {
                // console.log("定时执行了---------------");
                queryData(queryType);
            }, refreshType * 1000);
            // 停止按钮启用
            $("#STOP").attr("disabled", false);
        } else {
            // 历史数据
            const startTime = $("#inqu2_status-0-startTime").val();
            const endTime = $("#inqu2_status-0-endTime").val();
            if (IPLAT.isBlankString(startTime) || IPLAT.isBlankString(endTime)) {
                NotificationUtil({msg: "采集时间不能为空"}, "error");
                return;
            }
            // 停止按钮禁用
            $("#STOP").attr("disabled", true);
        }
        queryData(queryType);
    });
    // 停止按钮
    $("#STOP").on("click", function (e) {
        clearTimer();
        // 停止按钮禁用
        $("#STOP").attr("disabled", true);
        // 成功提醒
        NotificationUtil("停止成功", "success");
    });
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);

    // 清空计时器
    function clearTimer() {
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
    }

    // 绑定radio切换事件，选中实时数据，显示刷新频率
    $('input[type="radio"][name="inqu2_status-0-queryType"]').on("change", function () {
        // console.log("Selected value: " + $(this).val());
        if ($(this).val() === "1") {
            $("#timeDiv").show();
            $("#historyDiv").hide();
        } else {
            $("#timeDiv").hide();
            $("#historyDiv").show();
        }
    });

    // 实时查询时记录上次查询时间
    let lastQueryTime = null;

    // 查询数据
    function queryData(queryType) {
        const eiInfo = new EiInfo();
        eiInfo.setByNode("inqu2");
        // tagid
        const tagIds = Array.from(monitorMap.keys());
        eiInfo.set("tagId", tagIds);
        eiInfo.set("segNo", unitInfo.segNo);
        eiInfo.set("queryType", queryType);
        eiInfo.set("lastQueryTime", lastQueryTime);
        IMOMUtil.submitEiInfo(eiInfo, "VGDM0303", "queryData", function (ei) {
            const resultMap = ei.get("resultMap");
            if (!resultMap || Object.keys(resultMap).length === 0) {
                NotificationUtil("查询结果为空", "warning");
                clearTimer();
                return;
            }
            lastQueryTime = ei.get("lastQueryTime");
            loadData(resultMap, queryType === "1");
        });
    }

    // echarts实例缓存
    const chartMap = new Map();
    // 图表数据
    let chartData = [];
    // 公共的图表配置
    const commonChartConfig = {
        dataZoom: [
            {
                type: "slider",
                xAxisIndex: 0
            },
            {
                type: "slider",
                yAxisIndex: 0
            },
            {
                type: "inside",
                xAxisIndex: 0
            },
            {
                type: "inside",
                yAxisIndex: 0
            }
        ],
        toolbox: {
            feature: {
                restore: {},
                dataZoom: {}
            }
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {animation: false}
        },
        xAxis: {type: "time"},
        yAxis: {type: "value"}
    };

    /**
     * 加载数据
     * @param {Object} resultMap 查询结果
     * @param {boolean} isRealTime 是否是实时数据
     */
    function loadData(resultMap, isRealTime) {
        // 实时数据累加
        if (isRealTime) {
            Object.entries(resultMap).forEach(([tagId, list]) => {
                // 限制每个标签最多保留1000个数据点
                const maxDataPoints = 1000;
                if (chartData[tagId]) {
                    // 使用对象作为查找表，避免数据重复
                    const timeMap = {};
                    chartData[tagId].forEach((item) => {
                        timeMap[item.tagTime] = item;
                    });
                    // 只添加新的数据点
                    list.forEach((item) => {
                        timeMap[item.tagTime] = item;
                    });
                    // 转换回数组
                    chartData[tagId] = Object.values(timeMap);
                    // 如果数据点超过限制,只保留最新的数据
                    if (chartData[tagId].length > maxDataPoints) {
                        chartData[tagId] = chartData[tagId].slice(-maxDataPoints);
                    }
                } else {
                    chartData[tagId] = list;
                }
            });
        } else {
            chartData = resultMap;
        }
        // 处理每个监控点的数据
        Object.entries(chartData).forEach(([tagId, list]) => {
            // 构建图表格式
            const series = [
                {
                    name: monitorMap.get(tagId).tagDesc,
                    type: "line",
                    encode: {x: "tagTime", y: "tagValue"},
                    markLine: {
                        animation: false,
                        data: [
                            {
                                yAxis: monitorMap.get(tagId).upperLimit,
                                label: {
                                    show: true,
                                    formatter: "上限"
                                }
                            },
                            {
                                yAxis: monitorMap.get(tagId).lowerLimit,
                                label: {
                                    show: true,
                                    formatter: "下限"
                                }
                            }
                        ]
                    }
                }
            ];
            // 创建并初始化图表
            const echartInstance = createEchartsInstance(tagId);
            let titleText = monitorMap.get(tagId).tagDesc;
            if (monitorMap.get(tagId).measureId.trim() !== "") {
                titleText += "(" + monitorMap.get(tagId).measureId + ")";
            }
            // 设置图表配置
            const options = {
                ...commonChartConfig,
                title: {
                    text: titleText,
                    left: "center"
                },
                dataset: {source: list},
                series
            };
            echartInstance.setOption(options, true);
        });
    }

    /**
     * 创建图表容器
     * @param {string} tagId 标签ID
     * @returns {Object} echarts实例
     */
    function createEchartsInstance(tagId) {
        // 如果图表已存在，则返回已有的图表实例
        if (chartMap.has(tagId)) {
            return chartMap.get(tagId);
        }
        // 创建图表容器
        const container = $("#result2>.block-content");
        const chartDiv = $("<div>").css({width: "100%", height: "400px"}).appendTo(container);
        // 创建echarts实例
        const echartInstance = echarts.init(chartDiv[0]);
        chartMap.set(tagId, echartInstance);
        return echartInstance;
    }

    // 图表自适应
    window.addEventListener("resize", function () {
        chartMap.forEach((chart) => {
            chart.resize();
        });
    });
});
