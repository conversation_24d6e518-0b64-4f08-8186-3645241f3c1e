<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-10-23 15:51:34
       Version :  1.0
    tableName :${meliSchema}.tlirl0404
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     QUEUE_NUMBER  VARCHAR   NOT NULL,
     CAR_TRACE_NO  VARCHAR   NOT NULL,
     VEHICLE_NO  VARCHAR   NOT NULL,
     HAND_POINT_ID  VARCHAR   NOT NULL,
     BACKUP_DATE  VARCHAR   NOT NULL,
     HAND_TYPE  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0404">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="queueNumber">
            QUEUE_NUMBER = #queueNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="priorityLevel">
            PRIORITY_LEVEL = #priorityLevel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="queueDate">
            QUEUE_DATE = #queueDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointId">
            TARGET_HAND_POINT_ID = #targetHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overtimeCount">
            OVERTIME_COUNT = #overtimeCount#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0404">
        SELECT
        SEG_NO as "segNo",  <!-- 账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        QUEUE_NUMBER as "queueNumber",  <!-- 顺序号 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        HAND_POINT_ID as "handPointId",  <!-- 装卸点代码 -->
        BACKUP_DATE as "backupDate",  <!-- 备份日期 -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0404 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0404 WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="queueNumber">
            QUEUE_NUMBER = #queueNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId">
            HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="backupDate">
            BACKUP_DATE = #backupDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0404 (SEG_NO,  <!-- 账套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        QUEUE_NUMBER,  <!-- 顺序号 -->
        CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
        VEHICLE_NO,  <!-- 车牌号 -->
        HAND_POINT_ID,  <!-- 装卸点代码 -->
        BACKUP_DATE,  <!-- 备份日期 -->
        HAND_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID  <!-- 租户ID -->
        )
        VALUES (#segNo#, #unitCode#, #queueNumber#, #carTraceNo#, #vehicleNo#, #handPointId#, #backupDate#, #handType#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
        #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0404 WHERE
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0404
        SET
        SEG_NO = #segNo#,   <!-- 账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        QUEUE_NUMBER = #queueNumber#,   <!-- 顺序号 -->
        CAR_TRACE_NO = #carTraceNo#,   <!-- 车辆跟踪号 -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        HAND_POINT_ID = #handPointId#,   <!-- 装卸点代码 -->
        BACKUP_DATE = #backupDate#,   <!-- 备份日期 -->
        HAND_TYPE = #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        UUID = #uuid#,   <!-- uuid -->
        TENANT_ID = #tenantId#  <!-- 租户ID -->
        WHERE
    </update>

</sqlMap>