/**
 * Generate time : 2024-08-29 10:26:39
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm0101
 *
 */
public class Tvgdm0101 extends DaoEPBase {

    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String equipmentType = " ";        /* 设备类型*/
    private String processCategory = " ";        /* 工序大类代码*/
    private String processCategoryName = " ";        /* 工序大类名称*/
    private String processCategorySub = " ";        /* 工序小类代码*/
    private String processCategorySubName = " ";        /* 工序小类名称*/
    private BigDecimal designProductionCapacityWei = new BigDecimal(0.00000000);        /* 设计产能（万吨/年）*/
    private BigDecimal designProductionCapacityNum = new BigDecimal(0.00000000);        /* 设计产能（万片/年）*/
    private String equipmentProducingArea = " ";        /* 设备产地*/
    private String makerName = " ";        /* 制造商名称*/
    private String equipmentCommissioningDate = " ";        /* 设备投产日期*/
    private String fixedAssetNumber = " ";        /* 固定资产编号*/
    private String collectFlag = " ";        /* 采集标记*/
    private String equipmentStatus = " ";        /* 设备状态*/
    private String archiveAlterDesc = " ";        /* 设备档案变更说明*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String factoryBuilding = " ";        /* 厂房 */
    private String factoryBuildingName = " ";    /* 厂房名称 */
    

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentType");
        eiColumn.setDescName("设备类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategoryName");
        eiColumn.setDescName("工序大类名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategorySub");
        eiColumn.setDescName("工序小类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategorySubName");
        eiColumn.setDescName("工序小类名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("designProductionCapacityWei");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("设计产能（万吨/年）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("designProductionCapacityNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("设计产能（万片/年）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentProducingArea");
        eiColumn.setDescName("设备产地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("makerName");
        eiColumn.setDescName("制造商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentCommissioningDate");
        eiColumn.setDescName("设备投产日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fixedAssetNumber");
        eiColumn.setDescName("固定资产编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("collectFlag");
        eiColumn.setDescName("采集标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentStatus");
        eiColumn.setDescName("设备状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveAlterDesc");
        eiColumn.setDescName("设备档案变更说明");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0101() {
        initMetaData();
    }

    /**
     * get the eArchivesNo - 设备档案编号
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the equipmentType - 设备类型
     * @return the equipmentType
     */
    public String getEquipmentType() {
        return this.equipmentType;
    }

    /**
     * set the equipmentType - 设备类型
     */
    public void setEquipmentType(String equipmentType) {
        this.equipmentType = equipmentType;
    }

    /**
     * get the processCategory - 工序大类代码
     * @return the processCategory
     */
    public String getProcessCategory() {
        return this.processCategory;
    }

    /**
     * set the processCategory - 工序大类代码
     */
    public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
    }

    /**
     * get the processCategoryName - 工序大类名称
     * @return the processCategoryName
     */
    public String getProcessCategoryName() {
        return this.processCategoryName;
    }

    /**
     * set the processCategoryName - 工序大类名称
     */
    public void setProcessCategoryName(String processCategoryName) {
        this.processCategoryName = processCategoryName;
    }

    /**
     * get the processCategorySub - 工序小类代码
     * @return the processCategorySub
     */
    public String getProcessCategorySub() {
        return this.processCategorySub;
    }

    /**
     * set the processCategorySub - 工序小类代码
     */
    public void setProcessCategorySub(String processCategorySub) {
        this.processCategorySub = processCategorySub;
    }

    /**
     * get the processCategorySubName - 工序小类名称
     * @return the processCategorySubName
     */
    public String getProcessCategorySubName() {
        return this.processCategorySubName;
    }

    /**
     * set the processCategorySubName - 工序小类名称
     */
    public void setProcessCategorySubName(String processCategorySubName) {
        this.processCategorySubName = processCategorySubName;
    }

    /**
     * get the designProductionCapacityWei - 设计产能（万吨/年）
     * @return the designProductionCapacityWei
     */
    public BigDecimal getDesignProductionCapacityWei() {
        return this.designProductionCapacityWei;
    }

    /**
     * set the designProductionCapacityWei - 设计产能（万吨/年）
     */
    public void setDesignProductionCapacityWei(BigDecimal designProductionCapacityWei) {
        this.designProductionCapacityWei = designProductionCapacityWei;
    }

    /**
     * get the designProductionCapacityNum - 设计产能（万片/年）
     * @return the designProductionCapacityNum
     */
    public BigDecimal getDesignProductionCapacityNum() {
        return this.designProductionCapacityNum;
    }

    /**
     * set the designProductionCapacityNum - 设计产能（万片/年）
     */
    public void setDesignProductionCapacityNum(BigDecimal designProductionCapacityNum) {
        this.designProductionCapacityNum = designProductionCapacityNum;
    }

    /**
     * get the equipmentProducingArea - 设备产地
     * @return the equipmentProducingArea
     */
    public String getEquipmentProducingArea() {
        return this.equipmentProducingArea;
    }

    /**
     * set the equipmentProducingArea - 设备产地
     */
    public void setEquipmentProducingArea(String equipmentProducingArea) {
        this.equipmentProducingArea = equipmentProducingArea;
    }

    /**
     * get the makerName - 制造商名称
     * @return the makerName
     */
    public String getMakerName() {
        return this.makerName;
    }

    /**
     * set the makerName - 制造商名称
     */
    public void setMakerName(String makerName) {
        this.makerName = makerName;
    }

    /**
     * get the equipmentCommissioningDate - 设备投产日期
     * @return the equipmentCommissioningDate
     */
    public String getEquipmentCommissioningDate() {
        return this.equipmentCommissioningDate;
    }

    /**
     * set the equipmentCommissioningDate - 设备投产日期
     */
    public void setEquipmentCommissioningDate(String equipmentCommissioningDate) {
        this.equipmentCommissioningDate = equipmentCommissioningDate;
    }

    /**
     * get the fixedAssetNumber - 固定资产编号
     * @return the fixedAssetNumber
     */
    public String getFixedAssetNumber() {
        return this.fixedAssetNumber;
    }

    /**
     * set the fixedAssetNumber - 固定资产编号
     */
    public void setFixedAssetNumber(String fixedAssetNumber) {
        this.fixedAssetNumber = fixedAssetNumber;
    }

    /**
     * get the collectFlag - 采集标记
     * @return the collectFlag
     */
    public String getCollectFlag() {
        return this.collectFlag;
    }

    /**
     * set the collectFlag - 采集标记
     */
    public void setCollectFlag(String collectFlag) {
        this.collectFlag = collectFlag;
    }

    /**
     * get the equipmentStatus - 设备状态
     * @return the equipmentStatus
     */
    public String getEquipmentStatus() {
        return this.equipmentStatus;
    }

    /**
     * set the equipmentStatus - 设备状态
     */
    public void setEquipmentStatus(String equipmentStatus) {
        this.equipmentStatus = equipmentStatus;
    }

    /**
     * get the archiveAlterDesc - 设备档案变更说明
     * @return the archiveAlterDesc
     */
    public String getArchiveAlterDesc() {
        return this.archiveAlterDesc;
    }

    /**
     * set the archiveAlterDesc - 设备档案变更说明
     */
    public void setArchiveAlterDesc(String archiveAlterDesc) {
        this.archiveAlterDesc = archiveAlterDesc;
    }

    /**
     * get the uuid - 唯一编码
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the factoryBuilding - 厂房
     * @return the factoryBuilding
     */
    public String getFactoryBuilding() {
        return this.factoryBuilding;
    }

    /**
     * set the factoryBuilding - 厂房
     */
    public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
    }

    /**
     * get the factoryBuildingName - 厂房名称
     * @return the factoryBuildingName
     */
    public String getFactoryBuildingName() {
        return this.factoryBuildingName;
    }

    /**
     * set the factoryBuildingName - 厂房名称
     */
    public void setFactoryBuildingName(String factoryBuildingName) {
        this.factoryBuildingName = factoryBuildingName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setEquipmentType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentType")), equipmentType));
        setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
        setProcessCategoryName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategoryName")), processCategoryName));
        setProcessCategorySub(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategorySub")), processCategorySub));
        setProcessCategorySubName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategorySubName")), processCategorySubName));
        setDesignProductionCapacityWei(NumberUtils.toBigDecimal(StringUtils.toString(map.get("designProductionCapacityWei")), designProductionCapacityWei));
        setDesignProductionCapacityNum(NumberUtils.toBigDecimal(StringUtils.toString(map.get("designProductionCapacityNum")), designProductionCapacityNum));
        setEquipmentProducingArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentProducingArea")), equipmentProducingArea));
        setMakerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("makerName")), makerName));
        setEquipmentCommissioningDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentCommissioningDate")), equipmentCommissioningDate));
        setFixedAssetNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("fixedAssetNumber")), fixedAssetNumber));
        setCollectFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("collectFlag")), collectFlag));
        setEquipmentStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentStatus")), equipmentStatus));
        setArchiveAlterDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveAlterDesc")), archiveAlterDesc));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
        setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("equipmentType", StringUtils.toString(equipmentType, eiMetadata.getMeta("equipmentType")));
        map.put("processCategory", StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
        map.put("processCategoryName", StringUtils.toString(processCategoryName, eiMetadata.getMeta("processCategoryName")));
        map.put("processCategorySub", StringUtils.toString(processCategorySub, eiMetadata.getMeta("processCategorySub")));
        map.put("processCategorySubName", StringUtils.toString(processCategorySubName, eiMetadata.getMeta("processCategorySubName")));
        map.put("designProductionCapacityWei", StringUtils.toString(designProductionCapacityWei, eiMetadata.getMeta("designProductionCapacityWei")));
        map.put("designProductionCapacityNum", StringUtils.toString(designProductionCapacityNum, eiMetadata.getMeta("designProductionCapacityNum")));
        map.put("equipmentProducingArea", StringUtils.toString(equipmentProducingArea, eiMetadata.getMeta("equipmentProducingArea")));
        map.put("makerName", StringUtils.toString(makerName, eiMetadata.getMeta("makerName")));
        map.put("equipmentCommissioningDate", StringUtils.toString(equipmentCommissioningDate, eiMetadata.getMeta("equipmentCommissioningDate")));
        map.put("fixedAssetNumber", StringUtils.toString(fixedAssetNumber, eiMetadata.getMeta("fixedAssetNumber")));
        map.put("collectFlag", StringUtils.toString(collectFlag, eiMetadata.getMeta("collectFlag")));
        map.put("equipmentStatus", StringUtils.toString(equipmentStatus, eiMetadata.getMeta("equipmentStatus")));
        map.put("archiveAlterDesc", StringUtils.toString(archiveAlterDesc, eiMetadata.getMeta("archiveAlterDesc")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("factoryBuilding", StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
        map.put("factoryBuildingName", StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));

        return map;

    }
}