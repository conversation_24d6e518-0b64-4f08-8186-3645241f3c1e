package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0101;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 厂区区域管理
 */
public class ServiceLIDS0101 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0101().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0101.QUERY, new LIDS0101());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //区域类型不为通道,装卸货通道类型为空
                    String areaType = MapUtils.getString(itemMap, "areaType");
                    if (!"40".equals(areaType)) {
                        itemMap.put("aisleType", "");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //生成序列号
                    itemMap.put("areaCode",SequenceGenerator.getNextSequence(LIDS0101.SEQ_ID, new String[]{segNo, ""}));
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0101.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //区域类型不为通道,装卸货通道类型为空
                    String areaType = MapUtils.getString(itemMap, "areaType");
                    if (!"40".equals(areaType)) {
                        itemMap.put("aisleType", "");
                    }
                    //查询区域代码状态，判断非新增状态数据不可修改
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("areaCode", MapUtils.getString(itemMap, "areaCode"));
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    queryMap.put("status", "10");
                    int count = super.count(LIDS0101.COUNT_UUID, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "areaCode")+",区域代码状态非新增状态，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0101.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("areaCode", MapUtils.getString(itemMap, "areaCode"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0101.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "areaCode")+",区域代码状态非新增状态，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0101.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 启用
     *
     * @param inInfo
     * @return
     */
    public EiInfo enableArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可启用!");
                }
                //查询区域代码状态，判断非新增或禁用状态数据不可启用
                Map queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("areaCode", MapUtils.getString(itemMap, "areaCode"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("strStatus", "'10','99'");
                int count = super.count(LIDS0101.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "areaCode")+",区域代码状态非新增或禁用状态，不可启用!");
                }
                //校验，(x,y相同坐标只能存在一条启用状态的数据)
                queryMap.clear();
                queryMap.put("segNo",segNo);
                queryMap.put("factoryArea", MapUtils.getString(itemMap, "factoryArea"));//厂区编码
                queryMap.put("factoryBuilding", MapUtils.getString(itemMap, "factoryBuilding"));//厂房编码
                queryMap.put("xInitialPoint",MapUtils.getString(itemMap, "xInitialPoint"));
                queryMap.put("xDestination",MapUtils.getString(itemMap,"xDestination"));
                queryMap.put("yInitialPoint",MapUtils.getString(itemMap, "yInitialPoint"));
                queryMap.put("yDestination",MapUtils.getString(itemMap, "yDestination"));
                queryMap.put("status", "30");
                count = super.count(LIDS0101.COUNT_EQUAL, queryMap);
                if (count > 0) {
                    throw new PlatException("坐标(x,y)相同坐标只能存在一条启用状态的数据，不可启用!");
                }
                //状态变更为启用
                itemMap.put("status", "30");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            //修改区域状态
            outInfo = super.update(inInfo, LIDS0101.UPDATE_STATUS);
            //区域启用，推送UWB区域信息
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (HashMap map : resultList) {
                    EiInfo sendInfo = new EiInfo();
                    sendInfo.set("segNo", MapUtils.getString(map, "segNo"));
                    sendInfo.set("areaCode", MapUtils.getString(map, "areaCode"));
                    sendInfo.set("areaName", MapUtils.getString(map, "areaName"));
                    sendInfo.set("factoryArea", MapUtils.getString(map, "factoryArea"));
                    sendInfo.set("factoryBuilding", MapUtils.getString(map, "factoryBuilding"));
                    sendInfo.set("xInitialPoint", MapUtils.getString(map, "xInitialPoint"));
                    sendInfo.set("xDestination", MapUtils.getString(map, "xDestination"));
                    sendInfo.set("yInitialPoint", MapUtils.getString(map, "yInitialPoint"));
                    sendInfo.set("yDestination", MapUtils.getString(map, "yDestination"));
                    sendInfo.set(EiConstant.serviceName, "VPPLInterfaces");
                    sendInfo.set(EiConstant.methodName, "pushAreaInfoToUWB");
                    sendInfo = XLocalManager.call(sendInfo);
                    if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        throw new PlatException(sendInfo.getMsg());
                    }
                }
            }
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行启用操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 禁用
     *
     * @param inInfo
     * @return
     */
    public EiInfo disableArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可禁用!");
                }
                //查询区域代码状态，判断非启用状态u
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("areaCode", MapUtils.getString(itemMap, "areaCode"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("strStatus", "'10','30'");
                int count = super.count(LIDS0101.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("区域代码状态非新增或启用状态，不可禁用!");
                }
                //状态变更为禁用
                itemMap.put("status", "99");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0101.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行禁用操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
