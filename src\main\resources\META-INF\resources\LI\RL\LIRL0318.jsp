<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput type="text" ename="inqu_status-0-prodTypeId" cname="品种附属码" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
            <EF:EFInput type="text" ename="inqu_status-0-prodTypeIdName" cname="品种附属码名称" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P001"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0318" queryMethod="query"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true" enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false" type="hidden" hidden="true"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                <EF:EFCodeOption codeName="P001"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="prodTypeId" cname="品种附属码" align="center" enable="false" sort="false" required="true"/>
            <EF:EFColumn ename="prodTypeIdName" cname="品种附属码名称" align="center" enable="false" sort="false"/>
            <EF:EFComboColumn ename="coilSheetFlag" cname="板卷标记" align="center" enable="true" required="true">
                <EF:EFOption label=" " value=" "/>
                <EF:EFOption label="卷" value="10"/>
                <EF:EFOption label="板" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="reverseDurationSeconds"  cname="倒车时间（秒）【装卸点】" align="center"  required="true"  width="200"/>
            <EF:EFColumn ename="searchDurationSeconds" cname="找货时间（秒）【装卸点】" align="center" required="true" width="200"/>
            <EF:EFColumn ename="toolChangeDurationSeconds" cname="切换吊具时间（秒）【装卸点】" align="center" width="200"
                         required="true"/>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>

    <!-- 提单委托弹框 -->
    <div style="display: none">
        <EF:EFWindow id="prodThirdCode" title="" height="60%" width="90%" refresh="true">
            <EF:EFRegion id="inqu2" title="查询条件">
                <div class="row">
                    <EF:EFInput type="text" ename="inqu2_status-0-prodTypeId" cname="品种附属码" colWidth="3"
                                ratio="4:8"/>
                    <EF:EFInput type="text" ename="inqu2_status-0-prodTypeIdName" cname="品种附属码名称" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="prodThirdCode" title="三级品种附属信息查询" isFloat="true">
                <div class="row" style="margin-top: 10px; padding-left: 5px;padding-right: 20px;">
                    <EF:EFGrid blockId="prodThirdCode" readonly="true" autoDraw="false" autoBind="false" height="260" serviceName="LIRL0318" queryMethod="query2"
                    >
                        <EF:EFColumn ename="prodTypeId" cname="品种附属码" align="center" enable="false" sort="false" required="true"/>
                        <EF:EFColumn ename="prodTypeDesc" cname="品种附属码名称" align="center" enable="false" sort="false"/>
                    </EF:EFGrid>
                </div>
            </EF:EFRegion>
        </EF:EFWindow>
    </div>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
