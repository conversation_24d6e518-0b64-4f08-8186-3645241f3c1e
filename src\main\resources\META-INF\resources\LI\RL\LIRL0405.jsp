<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
    <div class="row">
<%--        <div class="col-md-12">--%>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFPopupInput ename="inqu_status-0-factoryArea" cname="厂区编码" colWidth="3"
                             resizable="true"
                             containerId="queryFactoryArea" popupTitle="请选择厂区" popupWidth="1000"
                             pupupHeight="500" center="true" originalInput="true" readonly="true">
            </EF:EFPopupInput>
            <EF:EFInput type="text" ename="inqu_status-0-factoryAreaName" cname="厂区名称" colWidth="3" ratio="4:8"
                        readonly="true"/>
<%--        </div>--%>
    </div>
    </EF:EFRegion>
    <div class="row">
        <div class="col-md-6">
            <EF:EFRegion id="result" title="待进厂">
                <EF:EFGrid blockId="result" autoDraw="no" isFloat="true" serviceName="LIRL0405"
                           queryMethod="queryEnterFactoryCarList" personal="true">
                    <EF:EFColumn ename="uuid" cname="" hidden="true"/>
                    <EF:EFColumn ename="factoryArea" cname="厂区编码" width="80" readonly="true"/>
                    <EF:EFColumn ename="factoryAreaName" cname="厂区名称" width="150" readonly="true"/>
                    <EF:EFColumn ename="vehicleNo" align="center" cname="车牌号" width="120" readonly="true"/>
                    <EF:EFColumn ename="carTraceNo" align="center" cname="车辆跟踪号" width="120" readonly="true" hidden="true"/>
                    <%--                <EF:EFColumn ename="allowEnterTime" cname="进厂登记时间" align="center" width="200"  required="true" readonly="true"/>--%>
                    <EF:EFColumn ename="checkDate" width="150" cname="进厂登记时间"
                                 editType="datetime" displayType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" align="center"
                                 readonly="true"/>
                    <%--<EF:EFColumn ename="status" enable="false" cname="状态" align="center" width="100"/>--%>
                    <EF:EFComboColumn ename="status" cname="状态"
                                      valueField="valueField" columnTemplate="#=valueField#-#=textField#"
                                      itemTemplate="#=valueField#-#=textField#" readonly="true">
                        <EF:EFOption label="进厂登记" value="10"/>
                        <EF:EFOption label="车辆进厂" value="20"/>
                        <EF:EFOption label="开始装卸货" value="30"/>
                        <EF:EFOption label="结束装卸货" value="40"/>
                        <EF:EFOption label="车辆出厂" value="50"/>
                        <EF:EFOption label="车辆签收" value="60"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="factoryType" cname="进厂类型"
                                      valueField="valueField" columnTemplate="#=valueField#-#=textField#"
                                      itemTemplate="#=valueField#-#=textField#" readonly="true">
                        <EF:EFOption label="首次进厂" value="10"/>
                        <EF:EFOption label="厂内周转" value="20"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="targetHandPointId" enable="false" readonly="true" cname="目标装卸点"
                                 align="center"/>
                    <EF:EFColumn ename="targetHandPointIdName" enable="false" readonly="true" cname="目标装卸点名称"
                                 align="center"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div class="col-md-6">
            <EF:EFRegion id="result2" title="待出厂">
                <EF:EFGrid blockId="result2" autoDraw="no" isFloat="true" serviceName="LIRL0405"
                           queryMethod="queryPutoutCarList" personal="true">
                    <EF:EFColumn ename="uuid" cname="" hidden="true"/>
                    <EF:EFColumn ename="factoryArea" cname="厂区编码" width="80" readonly="true"/>
                    <EF:EFColumn ename="factoryAreaName" cname="厂区名称" width="150" readonly="true"/>
                    <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" readonly="true"/>
                    <EF:EFColumn ename="beginEntruckingTime" width="150" cname="作业开始时间"
                                 editType="datetime" displayType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" align="center"
                                 readonly="true"/>
                    <EF:EFColumn ename="completeUninstallTime" width="150" cname="作业结束时间"
                                 editType="datetime" displayType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" align="center"
                                 readonly="true"/>
                    <EF:EFComboColumn ename="status" cname="状态"
                                      valueField="valueField" columnTemplate="#=valueField#-#=textField#"
                                      itemTemplate="#=valueField#-#=textField#" readonly="true">
                        <EF:EFOption label="进厂登记" value="10"/>
                        <EF:EFOption label="车辆进厂" value="20"/>
                        <EF:EFOption label="开始装卸货" value="30"/>
                        <EF:EFOption label="结束装卸货" value="40"/>
                        <EF:EFOption label="车辆出厂" value="50"/>
                        <EF:EFOption label="车辆签收" value="60"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="reservationNumber" cname="车辆预约单号" align="center" readonly="true"/>
                    <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" enable="false" align="center"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>

    </div>
    <div class="row">
        <div class="col-md-12">
            <EF:EFRegion id="result3" title="登记等待车辆">
                <EF:EFGrid blockId="result3" autoDraw="no" isFloat="true" serviceName="LIRL0405"
                           queryMethod="querySingInCarList" personal="true">
                    <EF:EFColumn ename="uuid" cname="" hidden="true"/>
                    <EF:EFColumn ename="factoryArea" cname="厂区编码" width="80" readonly="true"/>
                    <EF:EFColumn ename="factoryAreaName" cname="厂区名称" width="150" readonly="true"/>
                    <EF:EFColumn ename="vehicleNo" align="center" cname="车牌号" width="120" readonly="true"/>
                    <%--                <EF:EFColumn ename="allowEnterTime" cname="进厂登记时间" align="center" width="200"  required="true" readonly="true"/>--%>
                    <EF:EFColumn ename="checkDate" width="150" cname="进厂登记时间"
                                 editType="datetime" displayType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss" align="center"
                                 readonly="true"/>

                    <%--<EF:EFColumn ename="status" enable="false" cname="状态" align="center" width="100"/>--%>
                    <EF:EFComboColumn ename="status" cname="状态"
                                      valueField="valueField" columnTemplate="#=valueField#-#=textField#"
                                      itemTemplate="#=valueField#-#=textField#" readonly="true">
                        <EF:EFOption label="进厂登记" value="10"/>
                        <EF:EFOption label="车辆进厂" value="20"/>
                        <EF:EFOption label="开始装卸货" value="30"/>
                        <EF:EFOption label="结束装卸货" value="40"/>
                        <EF:EFOption label="车辆出厂" value="50"/>
                        <EF:EFOption label="车辆签收" value="60"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="targetHandPointId" enable="false" readonly="true" cname="目标装卸点"
                                 align="center"/>
                    <EF:EFColumn ename="targetHandPointIdName" enable="false" readonly="true" cname="目标装卸点名称"
                                 align="center"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="driverName" cname="驾驶员姓名" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="telNum" cname="手机号" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" enable="false" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </div>

    <div style="display: none;" id="queryFactoryArea">
        <EF:EFRegion id="inqux" title="查询条件">
            <div class="row">
                <EF:EFInput ename="inqux_status-0-factoryArea" cname="厂区编码" colWidth="3"/>
                <EF:EFInput ename="inqux_status-0-factoryAreaName" cname="厂区名称" colWidth="3"/>
                <EF:EFInput type="hidden" ename="inqux_status-0-segNo1" cname="业务单元编码(query)"/>
                <EF:EFInput type="hidden" ename="inqux_status-0-segNo2" cname="业务单元编码(result)"/>
            </div>
            <div class="col-xs-4" style="text-align: right; float: right" id="inqu_group">
                <EF:EFButton ename="QUERY_FACTORY" cname="查询"></EF:EFButton>
            </div>
        </EF:EFRegion>

        <EF:EFRegion id="resultX" title="厂区基本信息">
            <EF:EFGrid blockId="resultX" enable="false" serviceName="LPPR0005" queryMethod="queryFactoryArea"
                       autoDraw="override" checkMode="single" rowNo="true" autoBind="false" isFloat="true" height="360">
                <EF:EFColumn ename="factoryArea" cname="厂区编码" width="120" align="center" enable="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" width="120" align="center" enable="false"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
