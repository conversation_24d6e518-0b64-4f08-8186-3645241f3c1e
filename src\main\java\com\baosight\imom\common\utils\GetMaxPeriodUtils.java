package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.ei.EiInfo;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created with IntelliJ IDEA.
 *
 * @param
 * @return
 * @Description
 * @Author: 王燚
 * @ServiceId:
 * @Date: 2022/09/09/11:21
 * @Description:
 */
public class GetMaxPeriodUtils {
    //获取会计期间状态为1的最大期间
    public static EiInfo getMaxPeriod(){
        EiInfo inInfo = new EiInfo();
        ArrayList<Object> list = new ArrayList<>();
        HashMap<Object, Object> map = new HashMap<>();
        map.put("periodId","");
        list.add(map);
//        inInfo.set(EiConstant.serviceId, MessageCodeConstant.S_VC_BM_0506);
        inInfo.addRows("maxPeriodIdBlock", list);
        return  inInfo;
    }
}
