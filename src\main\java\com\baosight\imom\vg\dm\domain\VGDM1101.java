package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm1101;

/**
 * 工序时间规则表
 */
public class VGDM1101 extends Tvgdm1101 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM1101.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1101.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1101.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1101.update";

    @Override
    public String getCheckStatus() {
        return this.getRuleStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

}
