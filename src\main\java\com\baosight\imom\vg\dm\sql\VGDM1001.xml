<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1001">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1001">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        PROCESS_ORDER_ID as "processOrderId",  <!-- 生产工单号 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        PART_ID as "partId",  <!-- 物料号 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        START_TIME as "startTime", <!-- 开始时间 -->
        DELIVERY_DATE as "deliveryDate" <!-- 加工完成时间 -->
        FROM ${mevgSchema}.TVGDM1001 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1001 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1001 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        PROCESS_ORDER_ID,  <!-- 生产工单号 -->
        PACK_ID,  <!-- 捆包号 -->
        NET_WEIGHT,  <!-- 净重 -->
        PART_ID,  <!-- 物料号 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        START_TIME,  <!-- 开始时间 -->
        DELIVERY_DATE  <!-- 加工完成时间 -->
        )
        VALUES (#eArchivesNo#, #equipmentName#, #processCategory#, #machineCode#, #machineName#, #processOrderId#,
        #packId#, #netWeight#, #partId#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#,
        #startTime#, #deliveryDate#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1001 WHERE
        UUID = #uuid#
    </delete>

    <update id="updateForDel">
        UPDATE ${mevgSchema}.TVGDM1001
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>
    
    <update id="updateByEquipment">
        UPDATE ${mevgSchema}.TVGDM1001
        SET        
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = '1'   <!-- 删除标记 -->
        WHERE
        SEG_NO = #segNo#
        AND E_ARCHIVES_NO = #eArchivesNo#
        AND DEL_FLAG = '0'
    </update>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1001
        SET
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCESS_CATEGORY = #processCategory#,   <!-- 工序大类代码 -->
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        MACHINE_NAME = #machineName#,   <!-- 机组名称 -->
        PROCESS_ORDER_ID = #processOrderId#,   <!-- 生产工单号 -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        PART_ID = #partId#,   <!-- 物料号 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        START_TIME = #startTime#,  <!-- 开始时间 -->
        DELIVERY_DATE = #deliveryDate#  <!-- 加工完成时间 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>