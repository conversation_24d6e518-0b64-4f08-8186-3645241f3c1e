<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P001"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-sendType" cname="分类" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="未预约审核" value="10"/>
                <EF:EFOption label="超时通知" value="20"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd"
                           startCname="创建日期(始)" endCname="创建日期(止)"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0505" queryMethod="query"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false" type="hidden"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                <EF:EFCodeOption codeName="P001"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="sendType" cname="分类" align="center"  required="true">
                <EF:EFOption label="未预约审核" value="10"/>
                <EF:EFOption label="超时通知" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="perNo" cname="工号" align="center"  required="true" />
            <EF:EFColumn ename="perName" cname="姓名" align="center" required="true"/>
            <EF:EFColumn ename="perTel" cname="手机号" align="center" required="true" />
            <EF:EFMultiSelectColumn ename="workDays" cname="一周排班天数" width="200" align="center" required="true">
                <EF:EFOption label="周一" value="1"/>
                <EF:EFOption label="周二" value="2"/>
                <EF:EFOption label="周三" value="3"/>
                <EF:EFOption label="周四" value="4"/>
                <EF:EFOption label="周五" value="5"/>
                <EF:EFOption label="周六" value="6"/>
                <EF:EFOption label="周日" value="7"/>
            </EF:EFMultiSelectColumn>
            <EF:EFColumn ename="timeStart" cname="上班时间"  editType="datetime" width="150"
                         parseFormats="['HH:mm:ss']" dateFormat="HH:mm:ss" align="center"/>
            <EF:EFColumn ename="timeEnd" cname="下班时间"  editType="datetime" width="150"
                         parseFormats="['HH:mm:ss']" dateFormat="HH:mm:ss" align="center"/>
            <EF:EFComboColumn ename="typeOfHandling" cname="业务类型" align="center" optionLabel=" " >
                <EF:EFCodeOption codeName="P007"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="timeoutThreshold" cname="超时阈值" align="center" required="true" />
            <EF:EFColumn ename="loadingPointNo" cname="装卸点编码" align="center" width="150" enable="true"/>
            <EF:EFColumn ename="loadingPointName" cname="装卸点名称" align="center" width="150" enable="false"/>
            <EF:EFComboColumn ename="doorChargeFlag" cname="门长标记" align="center"  required="true">
                <EF:EFOption label="否" value="0"/>
                <EF:EFOption label="是" value="1"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="remark" cname="备注" align="center"/>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>



    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"></EF:EFWindow>
    <%--厂内装卸点弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIRL0003" id="handPointInfo" width="90%" height="60%"/>
</EF:EFPage>
