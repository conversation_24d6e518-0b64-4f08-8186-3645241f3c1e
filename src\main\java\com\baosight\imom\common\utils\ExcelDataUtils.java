package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 公司: Baosight Software LTD.co Copyright (c) 2022.<p>
 * 项目: 管理平台.<p>
 * 模块: 管理.<p>
 * 包名: com.baosight.imom.common.utils.<p>
 * 类名: ExcelDataUtils-List数据转化成EXCEL导出数据集工具类.<p>
 * 描述: 主要数据表：.<p>
 *
 * @Author: 武汉宝信-夏双全 TEL:17762608719（开发人）
 * @Create: 2022/9/15 9:00（开发日期）
 * @Version: 1.0（开发版本）
 * @Since: 1.8（JDK版本号）
 */
public class ExcelDataUtils {

    /**
     * 方法名: 构造方法.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: ExcelDataUtils
     * @param:
     * @return: null
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/12/2 15:13
     */
    public ExcelDataUtils() {

    }

    /**
     * 方法名: 后台List数据转化成前台EXCEL文件能识别的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: String mapTitle
     * @param: String> listMap
     * @return: List
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:04
     */
    public static List list2Excel(Map<String, String> mapTitle, List<Map<String, Object>> listMap) {
        List listData = new ArrayList();

        if (MapUtils.isNotEmpty(mapTitle)) {
            List<String> listHeadE = new ArrayList<>();
            List<String> listHeadC = new ArrayList<>();

            mapTitle.forEach((k, v) -> {
                listHeadE.add(k);
                listHeadC.add(v);
            });

            listData = list2Excel(listHeadE, listHeadC, listMap);
        }

        return listData;
    }

    /**
     * 方法名: 后台List数据转化成前台EXCEL文件能识别的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: String> listHeadE
     * @param: String> listHeadC
     * @param: Map> listMap
     * @return: List
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:05
     */
    public static List list2Excel(List<String> listHeadE, List<String> listHeadC, List<Map<String, Object>> listMap) {
        List listData = new ArrayList();

        if (CollectionUtils.isNotEmpty(listHeadE) && CollectionUtils.isNotEmpty(listHeadC)) {
            String[] arrayHeadE = listHeadE.toArray(new String[0]);
            String[] arrayHeadC = listHeadC.toArray(new String[0]);

            listData = list2Excel(arrayHeadE, arrayHeadC, listMap);
        }

        return listData;
    }

    /**
     * 方法名: 后台List数据转化成前台EXCEL文件能识别的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: String arrayHeadE
     * @param: String arrayHeadC
     * @param: Map> listMap
     * @return: List
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:05
     */
    public static List list2Excel(String[] arrayHeadE, String[] arrayHeadC, List<Map<String, Object>> listMap) {
        Map<String, Object> mapCell;
        Map<String, Object> mapRow;
        List<Map> listRow;
        List<Map> listData = new ArrayList<>();

        Map<String, Object> mapborder = new HashMap<>(16);
        mapborder.put("color", "#000000");
        mapborder.put("size", 1);

        if (arrayHeadE.length > 0 && arrayHeadC.length > 0) {
            listRow = new ArrayList<>();
            for (String s : arrayHeadC) {
                mapCell = new HashMap<>(16);
                mapCell.put("value", s);
                mapCell.put("bold", true);
                mapCell.put("fontSize", 11);
                mapCell.put("background", "#C0C0C0");
                mapCell.put("color", "#000000");
                mapCell.put("textAlign", "center");
                mapCell.put("vAlign", "middle");
                mapCell.put("borderTop", mapborder);
                mapCell.put("borderBottom", mapborder);
                mapCell.put("borderLeft", mapborder);
                mapCell.put("borderRight", mapborder);
                listRow.add(mapCell);
            }

            mapRow = new HashMap<>(16);
            mapRow.put("cells", listRow);
            listData.add(mapRow);
        }

        if (arrayHeadE.length > 0 && CollectionUtils.isNotEmpty(listMap)) {
            Object object;
            String strValue;
            for (Map<String, Object> map : listMap) {
                listRow = new ArrayList();
                for (String s : arrayHeadE) {
                    mapCell = new HashMap<>(16);
                    object = MapUtils.getObject(map, s);
                    strValue = Objects.toString(object, "");
                    if (object instanceof String) {
                        mapCell.put("value", strValue);
                        mapCell.put("textAlign", "left");
                    }

                    if (object instanceof Integer) {
                        mapCell.put("value", NumberUtils.toInt(strValue, 0));
                        mapCell.put("textAlign", "right");
                    }

                    if (object instanceof BigDecimal) {
                        mapCell.put("value", NumberUtils.toDouble(strValue, 0.0));
                        mapCell.put("textAlign", "right");
                    }

                    mapCell.put("fontSize", 10);
                    mapCell.put("vAlign", "middle");
                    mapCell.put("borderTop", mapborder);
                    mapCell.put("borderBottom", mapborder);
                    mapCell.put("borderLeft", mapborder);
                    mapCell.put("borderRight", mapborder);
                    listRow.add(mapCell);
                }

                mapRow = new HashMap<>(16);
                mapRow.put("cells", listRow);
                listData.add(mapRow);
            }
        }

        return listData;
    }

    /**
     * 方法名: 后台listMap数据转化成前台EXCEL文件导出的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: EiBlockMeta eiBlockMeta
     * @param: String mapTitle
     * @param: String> listMap
     * @return: EiInfo
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/12/8 9:18
     */
    public static EiInfo list2Excel(EiBlockMeta eiBlockMeta, Map<String, String> mapTitle, List<Map> listMap) {
        EiInfo outInfo = new EiInfo();
        EiBlockMeta eiMetadataReturn = new EiBlockMeta();

        if (Objects.nonNull(eiBlockMeta) && MapUtils.isNotEmpty(mapTitle)
                && CollectionUtils.isNotEmpty(listMap)) {

            mapTitle.forEach((k, v) -> {
                EiColumn eiColumn = eiBlockMeta.getMeta(k);
                eiColumn.setDescName(v);
                eiMetadataReturn.addMeta(eiColumn);
            });

            List<Map> listReturn = listMap.stream().map(map -> {
                Map<String, Object> mapReturn = new HashMap<>(16);
                mapTitle.forEach((k, v) -> {
                    String strValue = MapUtils.getString(map, k, "");
                    String strType = eiMetadataReturn.getMeta(k).getType(); //C-字符串；N-数字类型
                    int intScaleLength = eiMetadataReturn.getMeta(k).getScaleLength(); //大于0为小数

                    final String finalType = "N";
                    if (StringUtils.equals(finalType, strType)) {
                        if (intScaleLength > 0) {
                            strValue = String.valueOf(NumberUtils.toDouble(strValue));
                        } else {
                            strValue = String.valueOf(NumberUtils.toInt(strValue));
                        }
                    }

                    mapReturn.put(k, strValue);
                });
                return mapReturn;
            }).collect(Collectors.toList());

            outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(eiMetadataReturn);
            outInfo.getBlock(EiConstant.resultBlock).addRows(listReturn);
        }

        return outInfo;
    }

    /**
     * 方法名: 后台listMap数据转化成前台EXCEL文件导出的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: EiBlockMeta eiBlockMeta
     * @param: String> listRetainColumn
     * @param: Map> listMap
     * @return: EiInfo
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:05
     */
    public static EiInfo list2Excel(EiBlockMeta eiBlockMeta, List<String> listRetainColumn, List<Map> listMap) {
        EiInfo outInfo = new EiInfo();
        EiBlockMeta eiMetadataReturn = new EiBlockMeta();

        Map<String, Object> mapReturn;
        List<Map> listReturn = new ArrayList<>();

        if (Objects.nonNull(eiBlockMeta) && CollectionUtils.isNotEmpty(listRetainColumn)
                && CollectionUtils.isNotEmpty(listMap)) {

            for (String strKey : listRetainColumn) {
                eiMetadataReturn.addMeta(eiBlockMeta.getMeta(strKey));
            }

            for (Map map : listMap) {
                mapReturn = new HashMap<>(16);
                for (String strKey : listRetainColumn) {
                    String strValue = MapUtils.getString(map, strKey, "");
                    //C-字符串；N-数字类型
                    String strType = eiMetadataReturn.getMeta(strKey).getType();
                    //大于0为小数
                    int intScaleLength = eiMetadataReturn.getMeta(strKey).getScaleLength();

                    if (StringUtils.equals("N", strType)) {
                        if (intScaleLength > 0) {
                            mapReturn.put(strKey, NumberUtils.toDouble(strValue));
                        } else {
                            mapReturn.put(strKey, NumberUtils.toInt(strValue));
                        }
                    } else {
                        mapReturn.put(strKey, strValue);
                    }
                }

                listReturn.add(mapReturn);
            }

            outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(eiMetadataReturn);
            outInfo.getBlock(EiConstant.resultBlock).addRows(listReturn);
        }

        return outInfo;
    }

    /**
     * 方法名: 后台listMap数据转化成前台EXCEL文件导出的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: EiBlockMeta eiBlockMeta
     * @param: String> listRemoveColumn
     * @param: String> listRetainColumn
     * @param: Map> listMap
     * @return: EiInfo
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:06
     */
    public static EiInfo list2Excel(EiBlockMeta eiBlockMeta, List<String> listRemoveColumn,
                                    List<String> listRetainColumn, List<Map> listMap) {
        List<String> listRetain = new ArrayList<>();

        if (CollectionUtils.isEmpty(listRemoveColumn)) {
            listRetain = listRetainColumn;
        } else {
            if (CollectionUtils.isEmpty(listRetainColumn)) {
                Set setCol = eiBlockMeta.getMetas().keySet();
                for (Object o : setCol) {
                    String strKey = o.toString();
                    if (!listRemoveColumn.contains(strKey)) {
                        listRetain.add(strKey);
                    }
                }
            } else {
                listRetain = ListUtils.removeAll(listRetainColumn, listRemoveColumn);
            }
        }

        return list2Excel(eiBlockMeta, listRetain, listMap);
    }

    /**
     * 方法名: 后台listMap数据转化成前台EXCEL文件导出的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: EiBlockMeta eiBlockMeta
     * @param: String arrayRetainColumn
     * @param: Map> listMap
     * @return: EiInfo
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:06
     */
    public static EiInfo list2Excel(EiBlockMeta eiBlockMeta, String[] arrayRetainColumn, List<Map> listMap) {

        List<String> listRetainColumn = Stream.of(arrayRetainColumn).collect(Collectors.toList());

        return list2Excel(eiBlockMeta, listRetainColumn, listMap);
    }

    /**
     * 方法名: 后台listMap数据转化成前台EXCEL文件导出的数据类型.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: list2Excel
     * @param: EiBlockMeta eiBlockMeta
     * @param: String arrayRemoveColumn
     * @param: String arrayRetainColumn
     * @param: Map> listMap
     * @return: EiInfo
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:06
     */
    public static EiInfo list2Excel(EiBlockMeta eiBlockMeta, String[] arrayRemoveColumn,
                                    String[] arrayRetainColumn, List<Map> listMap) {

        List<String> listRemoveColumn = Stream.of(arrayRemoveColumn).collect(Collectors.toList());
        List<String> listRetainColumn = Stream.of(arrayRetainColumn).collect(Collectors.toList());

        return list2Excel(eiBlockMeta, listRemoveColumn, listRetainColumn, listMap);
    }

    /**
     * 方法名: 将上传上来的EXCEL文件解析成List.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: file2List
     * @param: File file
     * @param: int ignoreRows
     * @return: List
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:06
     */
    public static List<Map> file2List(File file, int ignoreRows) throws Exception {
        return workbook2List(WorkbookFactory.create(file), ignoreRows);
    }

    /**
     * 方法名: 将上传上来的EXCEL文件流解析成List.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: inputStream2List
     * @param: InputStream ins
     * @param: int ignoreRows
     * @return: List
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:07
     */
    public static List<Map> inputStream2List(InputStream ins, int ignoreRows) throws Exception {
        return workbook2List(WorkbookFactory.create(ins), ignoreRows);
    }

    /**
     * 方法名: 将EXCEL文件解析成List.<p>
     * 方法描述: .<p>
     *
     * @serviceId: S_XX_XX_0000
     * @method: workbook2List
     * @param: Workbook wb
     * @param: int ignoreRows
     * @return: List
     * @throws:
     * @author: 971145-夏双全
     * @date: 2022/9/15 15:07
     */
    public static List<Map> workbook2List(Workbook wb, int ignoreRows) {
        int columnSize;
        Map<String, String> mapCell = null;
        List<String> listTitle = new ArrayList<>();
        List<Map> listRow = new ArrayList<>();

        Cell cell;
        Sheet st = wb.getSheetAt(0);

        Row rowTitle = st.getRow(ignoreRows);
        if (Objects.nonNull(rowTitle)) {
            for (short columnIndex = 0; columnIndex <= rowTitle.getLastCellNum(); ++columnIndex) {
                cell = rowTitle.getCell(columnIndex);
                if (Objects.nonNull(cell)) {
                    String strCellValue = cell.getStringCellValue();
                    if (Objects.nonNull(strCellValue)) {
                        listTitle.add(strCellValue);
                    }
                }
            }

            columnSize = listTitle.size();

            for (int rowIndex = ignoreRows + 1; rowIndex <= st.getLastRowNum(); ++rowIndex) {
                Row row = st.getRow(rowIndex);
                if (Objects.nonNull(row)) {
                    mapCell = new LinkedHashMap<>();
                    for (short columnIndex = 0; columnIndex <= columnSize; ++columnIndex) {
                        cell = row.getCell(columnIndex);
                        if (Objects.nonNull(cell)) {
                            String strCellValue = "";
                            switch (cell.getCellType()) {
                                case SXSSFCell.CELL_TYPE_NUMERIC:
                                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                                        Date date = cell.getDateCellValue();
                                        if (date != null) {
                                            strCellValue = (new SimpleDateFormat("yyyy-MM-dd")).format(date);
                                        } else {
                                            strCellValue = "";
                                        }
                                    } else {
                                        strCellValue = (new DecimalFormat("0.00000")).format(cell.getNumericCellValue());
                                    }
                                    break;
                                case SXSSFCell.CELL_TYPE_STRING:
                                    strCellValue = cell.getStringCellValue();
                                    break;
                                case SXSSFCell.CELL_TYPE_FORMULA:
                                    if (!"".equals(cell.getStringCellValue())) {
                                        strCellValue = cell.getStringCellValue();
                                    } else {
                                        strCellValue = cell.getNumericCellValue() + "";
                                    }
                                    break;
                                case SXSSFCell.CELL_TYPE_BLANK:
                                    break;
                                case SXSSFCell.CELL_TYPE_BOOLEAN:
                                    strCellValue = cell.getBooleanCellValue() ? "Y" : "N";
                                    break;
                                case SXSSFCell.CELL_TYPE_ERROR:
                                default:
                                    strCellValue = "";
                            }

                            if (columnIndex == 0 && StringUtils.isBlank(strCellValue)) {
                                break;
                            }

                            mapCell.put(listTitle.get(columnIndex), strCellValue);
                        }
                    }
                }

                listRow.add(mapCell);
            }

        }

        return listRow;
    }
}