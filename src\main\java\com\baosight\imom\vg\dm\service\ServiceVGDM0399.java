package com.baosight.imom.vg.dm.service;

import com.baosight.hdsdk.exception.HDSdkException;
import com.baosight.imom.common.utils.*;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR> yzj
 * @Description : ihd数据查询页面后台
 * @Date : 2025/2/16
 * @Version : 1.0
 */
public class ServiceVGDM0399 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0399.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询数据
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
            // 获取点位信息
            String tagIds = inInfo.getCellStr(EiConstant.queryBlock, 0, "tagId");
            String tagDescStr = inInfo.getCellStr(EiConstant.queryBlock, 0, "tagDesc");
            String tagIhdIds = inInfo.getCellStr(EiConstant.queryBlock, 0, "tagIhdId");
            if (StrUtil.isBlank(tagIds) || StrUtil.isBlank(tagDescStr) || StrUtil.isBlank(tagIhdIds)) {
                throw new PlatException("点位不能为空");
            }
            List<String> tagIhdIdList = Arrays.asList(tagIhdIds.split(","));
            // 时间条件
            String startTime = inInfo.getCellStr(EiConstant.queryBlock, 0, "startTime");
            String endTime = inInfo.getCellStr(EiConstant.queryBlock, 0, "endTime");
            if (StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) {
                throw new PlatException("采集时间不能为空");
            }
            // 查询历史数据
            Map<String, List<Map<String, Object>>> resultMap = queryHistory(segNo, tagIhdIdList, startTime, endTime);
            // 便利resultMap
            List<Map<String, Object>> resultList = new ArrayList<>();
            String[] tagDescArray = tagDescStr.split(",");
            String[] tagIhdIdArray = tagIhdIds.split(",");
            String[] tagIdArray = tagIds.split(",");
            for (int i = 0; i < tagIhdIdList.size(); i++) {
                List<Map<String, Object>> list = resultMap.get(tagIhdIdList.get(i));
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                for (Map<String, Object> map : list) {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("tagDesc", tagDescArray[i]);
                    dataMap.put("tagIhdId", tagIhdIdArray[i]);
                    dataMap.put("tagId", tagIdArray[i]);
                    dataMap.put("tagValue", map.get("tagValue"));
                    dataMap.put("tagTime", map.get("tagTime"));
                    resultList.add(dataMap);
                }
            }
            EiBlock resultBlock = inInfo.addBlock(EiConstant.resultBlock);
            resultBlock.getRows().clear();
            resultBlock.set(EiConstant.limitStr, resultList.size());
            resultBlock.setRows(resultList);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg("查询成功");
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 查询历史数据
     */
    private Map<String, List<Map<String, Object>>> queryHistory(String segNo, List<String> tagIhdIdList, String startTime, String endTime) throws HDSdkException {

        LocalDateTime start = LocalDateTime.parse(startTime, DateUtils.FORMATTER_19);
        LocalDateTime end = LocalDateTime.parse(endTime, DateUtils.FORMATTER_19);
        // 时间范围不能超过8小时
        if (end.isBefore(start) || end.isAfter(start.plusHours(8))) {
            throw new PlatException("时间范围不能超过8小时");
        }
        return IhdSdkUtils.queryHisValue(segNo, tagIhdIdList, Date.from(start.atZone(ZoneId.systemDefault()).toInstant()), Date.from(end.atZone(ZoneId.systemDefault()).toInstant()));
    }

}
