<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-16 9:33:35
       Version :  1.0
    tableName :${meliSchema}.tlirl0109
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     NOTIFICATION_TITLE  VARCHAR   NOT NULL,
     NOTIFICATION_TEXT  VARCHAR   NOT NULL,
     START_OF_VALIDITY  VARCHAR   NOT NULL,
     EXPIRATION_OF_VALIDITY  VARCHAR   NOT NULL,
     COUNTDOWN_DURATION  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0109">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			a.SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			a.UNIT_CODE = #unitCode#
		</isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
			a.STATUS = #status#
		</isNotEmpty>
		<isEmpty prepend=" AND " property="status">
			a.STATUS != '00'
		</isEmpty>
		<isNotEmpty prepend=" AND " property="notificationTitle">
			a.NOTIFICATION_TITLE = #notificationTitle#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notificationText">
			a.NOTIFICATION_TEXT = #notificationText#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startOfValidity">
			a.START_OF_VALIDITY = #startOfValidity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="expirationOfValidity">
			a.EXPIRATION_OF_VALIDITY = #expirationOfValidity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="countdownDuration">
			a.COUNTDOWN_DURATION = #countdownDuration#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			a.REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			a.REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			a.REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			a.REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			a.REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			a.REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			a.ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			a.DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			a.REMARK = #remark#
		</isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID like concat('%',#uuid#,'%')
        </isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			a.TENANT_ID = #tenantId#
		</isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(a.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(a.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="signatureMarking">
            a.SIGNATURE_MARKING = #signatureMarking#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notSignatureMarking">
            a.SIGNATURE_MARKING != '1'
        </isNotEmpty>
	</sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0109">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(撤销：00、新增：10) -->
        a.NOTIFICATION_TITLE as "notificationTitle",  <!-- 通知标题 -->
        a.NOTIFICATION_TEXT as "notificationText",  <!-- 通知正文 -->
        a.START_OF_VALIDITY as "startOfValidity",  <!-- 有效期起始 -->
        a.EXPIRATION_OF_VALIDITY as "expirationOfValidity",  <!-- 有效期截止 -->
        a.COUNTDOWN_DURATION as "countdownDuration",  <!-- 倒计时时长(秒) -->
        a.SIGNATURE_MARKING	as "signatureMarking",  <!-- 一体机签章标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.BOLD as "bold", <!-- 加粗 -->
        a.ADD_RED as "addRed", <!-- 加红 -->
        a.ADD_UNDERLINE as "addUnderline" <!-- 加下划线 -->
        FROM ${meliSchema}.tlirl0109 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryTitle" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0109">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(撤销：00、新增：10) -->
        a.NOTIFICATION_TITLE as "notificationTitle",  <!-- 通知标题 -->
        a.NOTIFICATION_TEXT as "notificationText",  <!-- 通知正文 -->
        a.START_OF_VALIDITY as "startOfValidity",  <!-- 有效期起始 -->
        a.EXPIRATION_OF_VALIDITY as "expirationOfValidity",  <!-- 有效期截止 -->
        a.COUNTDOWN_DURATION as "countdownDuration",  <!-- 倒计时时长(秒) -->
        a.SIGNATURE_MARKING	as "signatureMarking",  <!-- 一体机签章标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.BOLD as "bold", <!-- 加粗 -->
        a.ADD_RED as "addRed", <!-- 加红 -->
        a.ADD_UNDERLINE as "addUnderline" <!-- 加下划线 -->
        FROM ${meliSchema}.tlirl0109 a WHERE 1=1
        and a.EXPIRATION_OF_VALIDITY>CURDATE()
		<include refid="condition"/>
        and a.COUNTDOWN_DURATION!='0'
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
				a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>


    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0109">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(撤销：00、新增：10) -->
        a.NOTIFICATION_TITLE as "notificationTitle",  <!-- 通知标题 -->
        a.NOTIFICATION_TEXT as "notificationText",  <!-- 通知正文 -->
        a.START_OF_VALIDITY as "startOfValidity",  <!-- 有效期起始 -->
        a.EXPIRATION_OF_VALIDITY as "expirationOfValidity",  <!-- 有效期截止 -->
        a.COUNTDOWN_DURATION as "countdownDuration",  <!-- 倒计时时长(秒) -->
        a.SIGNATURE_MARKING	as "signatureMarking",  <!-- 一体机签章标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0109 a WHERE 1=1
        and a.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID =#uuid#
        </isNotEmpty>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0109 a WHERE 1=1
		<include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notificationTitle">
            NOTIFICATION_TITLE = #notificationTitle#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notificationText">
            NOTIFICATION_TEXT = #notificationText#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfValidity">
            START_OF_VALIDITY = #startOfValidity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="expirationOfValidity">
            EXPIRATION_OF_VALIDITY = #expirationOfValidity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="countdownDuration">
            COUNTDOWN_DURATION = #countdownDuration#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0109 (SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        STATUS,  <!-- 状态(撤销：00、新增：10) -->
        NOTIFICATION_TITLE,  <!-- 通知标题 -->
        NOTIFICATION_TEXT,  <!-- 通知正文 -->
        START_OF_VALIDITY,  <!-- 有效期起始 -->
        EXPIRATION_OF_VALIDITY,  <!-- 有效期截止 -->
        COUNTDOWN_DURATION,  <!-- 倒计时时长(秒) -->
        SIGNATURE_MARKING,  <!-- 一体机签章标记 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID, <!-- 租户ID -->
        BOLD, <!-- 加粗 -->
        ADD_RED, <!-- 加红 -->
        ADD_UNDERLINE <!-- 加下划线 -->
        )
        VALUES (#segNo#, #unitCode#, #status#, #notificationTitle#, #notificationText#, #startOfValidity#,
        #expirationOfValidity#, #countdownDuration#, #signatureMarking#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #uuid#, #tenantId#, #bold#, #addRed#, #addUnderline#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0109 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0109
        SET
        SEG_NO = #segNo#,   <!-- 业务单元代代码 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        STATUS = #status#,   <!-- 状态(撤销：00、新增：10) -->
        NOTIFICATION_TITLE = #notificationTitle#,   <!-- 通知标题 -->
        NOTIFICATION_TEXT = #notificationText#,   <!-- 通知正文 -->
        START_OF_VALIDITY = #startOfValidity#,   <!-- 有效期起始 -->
        EXPIRATION_OF_VALIDITY = #expirationOfValidity#,   <!-- 有效期截止 -->
        COUNTDOWN_DURATION = #countdownDuration#,   <!-- 倒计时时长(秒) -->
        SIGNATURE_MARKING	= #signatureMarking#,   <!-- 一体机签章标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        BOLD = #bold#, <!-- 加粗 -->
        ADD_RED = #addRed#, <!-- 加红 -->
        ADD_UNDERLINE = #addUnderline# <!-- 加下划线 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>