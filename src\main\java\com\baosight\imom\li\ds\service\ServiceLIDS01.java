package com.baosight.imom.li.ds.service;


import com.baosight.imom.li.ds.domain.LIDS0101;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * 仓库代码下拉
 */
public class ServiceLIDS01 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLIDS01.class);

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0101().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //查询条件
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            //表格分页条件
            Map hashMap = inInfo.getBlock(EiConstant.resultBlock).getAttr();
            int limit = MapUtils.getInteger(hashMap, "limit");
            int offset = MapUtils.getInteger(hashMap, "offset");
            //查询仓库信息
            EiInfo sendInfo = new EiInfo();
            sendInfo.addBlock(EiConstant.queryBlock).addRow(queryMap);
            sendInfo.set("limit", limit);
            sendInfo.set("offset", offset);
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "queryWarehouseList");
            outInfo = XLocalManager.call(sendInfo);
            if(outInfo.getStatus() < EiConstant.STATUS_DEFAULT){
                throw new PlatException(outInfo.getMsg());
            }
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}
