package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0807;

/**
 * 月度检修项目表
 */
public class VGDM0807 extends Tvgdm0807 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0807.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0807.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0807.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0807.update";
    /**
     * 修改
     */
    public static final String UPDATE_ACTUAL = "VGDM0807.updateActual";
    /**
     * 逻辑删除
     */
    public static final String UPD_FOR_DEL = "VGDM0807.updForDel";

}
