package com.baosight.imom.vg.dm.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 报表-点检完成率
 */
public class VGDMB101  extends DaoEPBase {

    /**
     * 点检性质
     */
    private String spotCheckNatureName = " ";

    /**
     * 设备档案号
     */
    private String eArchivesNo = " ";

    /**
     * 设备名称
     */
    private String equipmentName = " ";

    /**
     * 点检日期
     */
    private String checkPlanDate = " ";

    /**
     * 总数量
     */
    private String totalNum = " ";

    /**
     * 未完成数量
     */
    private String notNum = " ";

    /**
     * 已完成数量
     */
    private String doNum = " ";

    /**
     * 异常数量
     */
    private String eNum = " ";


    public String getSpotCheckNatureName() {
        return spotCheckNatureName;
    }

    public void setSpotCheckNatureName(String spotCheckNatureName) {
        this.spotCheckNatureName = spotCheckNatureName;
    }

    public String geteArchivesNo() {
        return eArchivesNo;
    }

    public void seteArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    public String getEquipmentName() {
        return equipmentName;
    }

    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    public String getCheckPlanDate() {
        return checkPlanDate;
    }

    public void setCheckPlanDate(String checkPlanDate) {
        this.checkPlanDate = checkPlanDate;
    }

    public String getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(String totalNum) {
        this.totalNum = totalNum;
    }

    public String getNotNum() {
        return notNum;
    }

    public void setNotNum(String notNum) {
        this.notNum = notNum;
    }

    public String getDoNum() {
        return doNum;
    }

    public void setDoNum(String doNum) {
        this.doNum = doNum;
    }

    public String geteNum() {
        return eNum;
    }

    public void seteNum(String eNum) {
        this.eNum = eNum;
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("spotCheckNatureName");
        eiColumn.setDescName("点检性质");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkPlanDate");
        eiColumn.setDescName("点检日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalNum");
        eiColumn.setDescName("总数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("notNum");
        eiColumn.setDescName("未完成数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("doNum");
        eiColumn.setDescName("已完成数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eNum");
        eiColumn.setDescName("异常数量");
        eiMetadata.addMeta(eiColumn);
    }


    /**
     * the constructor
     */
    public VGDMB101() {
        initMetaData();
    }


    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSpotCheckNatureName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckNatureName")), spotCheckNatureName));
        seteArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setCheckPlanDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkPlanDate")), checkPlanDate));
        setTotalNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("totalNum")), totalNum));
        setDoNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("doNum")), doNum));
        setNotNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("notNum")), notNum));
        seteNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eNum")), eNum));
    }


    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("spotCheckNatureName", StringUtils.toString(spotCheckNatureName, eiMetadata.getMeta("spotCheckNatureName")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("checkPlanDate", StringUtils.toString(checkPlanDate, eiMetadata.getMeta("checkPlanDate")));
        map.put("totalNum", StringUtils.toString(totalNum, eiMetadata.getMeta("totalNum")));
        map.put("doNum", StringUtils.toString(doNum, eiMetadata.getMeta("doNum")));
        map.put("notNum", StringUtils.toString(notNum, eiMetadata.getMeta("notNum")));
        map.put("eNum", StringUtils.toString(eNum, eiMetadata.getMeta("eNum")));
        return map;

    }

}
