<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-27 10:36:23
       Version :  1.0
    # 配单信息维护主表
create table ${meliSchema}.tlirl0502
(
    SEG_NO              VARCHAR(12)  DEFAULT ' ' not null comment '业务单元代码',
    UNIT_CODE           VARCHAR(12)  DEFAULT ' ' not null comment '业务单元代码',
    STATUS              varchar(2)   DEFAULT ' ' not null comment '状态',
    REC_CREATOR         varchar(255) DEFAULT ' ' not null comment '记录创建人',
    REC_CREATOR_NAME    varchar(255) DEFAULT ' ' not null comment '记录创建人姓名',
    REC_CREATE_TIME     varchar(14)  DEFAULT ' ' not null comment '记录创建时间',
    REC_REVISOR         varchar(255) DEFAULT ' ' not null comment '记录修改人',
    REC_REVISOR_NAME    varchar(255) DEFAULT ' ' not null comment '记录创建人姓名',
    REC_REVISE_TIME     varchar(14)  DEFAULT ' ' not null comment '记录修改时间',
    ARCHIVE_FLAG        smallint     DEFAULT 0   not null comment '归档标记',
    DEL_FLAG            smallint     DEFAULT 0   not null comment '记录删除标记',
    REMARK              varchar(255) DEFAULT ' ' not null comment '备注',
    VEHICLE_NO          varchar(20)  default ' ' not null comment '车牌号',
    UUID                varchar(32)  DEFAULT ' ' not null comment 'uuid',
    TENANT_ID           varchar(255) DEFAULT ' ' not null comment '租户ID',
    ALLOCATE_VEHICLE_NO varchar(20)  DEFAULT ' ' not null comment '配车单号',
    ALLOC_TYPE          varchar(2)   DEFAULT ' ' not null comment '配单类型',
    CAR_TRACE_NO        varchar(20)  DEFAULT ' ' not null comment '车辆管理号',
    primary key (UUID)
)
    comment '车辆类型管理表' ENGINE = INNODB
                               DEFAULT CHARSET = UTF8
                               collate = utf8_bin;

# 配单信息维护子表
create table ${meliSchema}.tlirl0503
(
    SEG_NO              VARCHAR(12)  DEFAULT ' ' not null comment '业务单元代码',
    UNIT_CODE           VARCHAR(12)  DEFAULT ' ' not null comment '业务单元代码',
    STATUS              varchar(2)   DEFAULT ' ' not null comment '状态',
    REC_CREATOR         varchar(255) DEFAULT ' ' not null comment '记录创建人',
    REC_CREATOR_NAME    varchar(255) DEFAULT ' ' not null comment '记录创建人姓名',
    REC_CREATE_TIME     varchar(14)  DEFAULT ' ' not null comment '记录创建时间',
    REC_REVISOR         varchar(255) DEFAULT ' ' not null comment '记录修改人',
    REC_REVISOR_NAME    varchar(255) DEFAULT ' ' not null comment '记录创建人姓名',
    REC_REVISE_TIME     varchar(14)  DEFAULT ' ' not null comment '记录修改时间',
    ARCHIVE_FLAG        smallint     DEFAULT 0   not null comment '归档标记',
    DEL_FLAG            smallint     DEFAULT 0   not null comment '记录删除标记',
    REMARK              varchar(255) DEFAULT ' ' not null comment '备注',
    UUID                varchar(32)  DEFAULT ' ' not null comment 'uuid',
    TENANT_ID           varchar(255) DEFAULT ' ' not null comment '租户ID',
    ALLOCATE_VEHICLE_NO varchar(20)  DEFAULT ' ' not null comment '配车单号',
    ALLOC_VEHICLE_SEQ   DECIMAL(12)  DEFAULT 0   not null comment '配车单子项序号',
    VOUCHER_NUM         varchar(32)  DEFAULT ' ' not null comment '依据凭单(卸货:入库计划;装货:提单号)',
    PACK_ID             varchar(50)  default ' ' not null comment '捆包号',
    OUT_PACK_FLAG       varchar(1)   default '0' not null comment '自带货标记(0:非自带货;1:自带货)',
    WAREHOUSE_CODE      varchar(20)  default ' ' not null comment '仓库代码',
    WAREHOUSE_NAME      varchar(255) default ' ' not null comment '仓库名称',
    PUTIN_TYPE          varchar(10)  default ' ' not null comment '入库类型',
    INNER_DIAMETER      DECIMAL(12, 6) default 0 not null comment '内径',
    PROD_DENSITY        DECIMAL(18, 6) default 0 not null comment '密度',
    PRODUCT_PROCESS_ID  varchar(50)  default ' ' not null comment '首道加工工序',
    NET_WEIGHT          DECIMAL(20, 8) default 0 not null comment '重量',
    CUSTOMER_ID         varchar(20)  default ' ' not null comment '客户代码',
    CUSTOMER_NAME       varchar(255) default ' ' not null comment '客户名称',
    primary key (UUID)
)
    comment '车辆类型管理子表' ENGINE = INNODB
                               DEFAULT CHARSET = UTF8
                               collate = utf8_bin;
-->
<sqlMap namespace="LIRL0502">

	<sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            ALLOC_TYPE = #allocType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="showFlag">
            SHOW_FLAG = #showFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="estimatedTimeOfArrival">
            substr(replace(ESTIMATED_TIME_OF_ARRIVAL,'-',''),1,8) <![CDATA[<=]]> replace(#estimatedTimeOfArrival#,'-','')
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(t0502.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(t0502.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
	</sql>
	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0502">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId",  <!-- 租户ID -->
				ALLOCATE_VEHICLE_NO	as "allocateVehicleNo",  <!-- 配车单号 -->
				ALLOC_TYPE	as "allocType",  <!-- 配单类型 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆管理号 -->
				SHOW_FLAG	as "showFlag", <!-- 展示标记 -->
                ESTIMATED_TIME_OF_ARRIVAL	as "estimatedTimeOfArrival", <!-- 预计到达时间 -->
                CUSTOMER_ID AS "customerId",
                CUSTOMER_NAME AS "customerName",
        NEXT_ALC_VEHICLE_NO as "nextAlcVehicleNo",
        PROFORMA_VOUCHER_NUM as "proformaVoucherNum",
        EMERGENCY_DELIVERY_TIME as "emergencyDeliveryTime",
        LADING_BILL_REMARK as "ladingBillRemark",
        DRIVER_NAME AS "driverName",
        DRIVER_TEL AS "driverTel",
        NO_PLAN_FLAG as "noPlanFlag",
        PROFORMA_ORDER_NUM as "proformaOrderNum"
		FROM ${meliSchema}.tlirl0502 WHERE 1=1
		<include refid="condition"/>
        <isNotEmpty prepend="and" property="carTraceNoNull">
            CAR_TRACE_NO !=''
		</isNotEmpty>
        <isNotEmpty prepend="and" property="proformaVoucherNum">
            PROFORMA_VOUCHER_NUM = #proformaVoucherNum#
		</isNotEmpty>
        <isNotEmpty prepend="and " property="statusIn">
            STATUS in ('20','99')
		</isNotEmpty>
        <isNotEmpty prepend="and " property="noProformaVoucherNum">
            PROFORMA_VOUCHER_NUM !=' '
		</isNotEmpty>
        <isNotEmpty prepend="and " property="nextAlcVehicleNo">
            NEXT_ALC_VEHICLE_NO=#nextAlcVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend="and " property="nextAlcVehicleNoIn">
            NEXT_ALC_VEHICLE_NO=#nextAlcVehicleNoIn#
        </isNotEmpty>
        <isNotEmpty prepend="and " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO=#allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend="and " property="noAllocateVehicleNo">
            ALLOCATE_VEHICLE_NO!=#noAllocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend="and " property="flag">
            exists(
            select 1 from meli.tlirl0401 where 1=1 and tlirl0401.CAR_TRACE_NO=tlirl0502.CAR_TRACE_NO
            and tlirl0401.VOUCHER_NUM=tlirl0502.ALLOCATE_VEHICLE_NO
            and tlirl0401.VEHICLE_NO=tlirl0502.VEHICLE_NO
            )
        </isNotEmpty>
        <isNotEmpty prepend="and " property="flag">
            exists(
            select 1 from meli.tlirl0301 where 1=1 and tlirl0301.CAR_TRACE_NO=tlirl0502.CAR_TRACE_NO
            and tlirl0401.VEHICLE_NO=tlirl0502.VEHICLE_NO
            )
        </isNotEmpty>
        <isNotEmpty prepend="and " property="isNotStatus">
        exists(
        select 1 from meli.tlirl0301  tlirl0301 where 1=1 and tlirl0301.SEG_NO=tlirl0502.SEG_NO
        and tlirl0301.CAR_TRACE_NO=tlirl0502.CAR_TRACE_NO
        and tlirl0301.VEHICLE_NO=tlirl0502.VEHICLE_NO
        and tlirl0301.STATUS='20'
        and tlirl0301.TARGET_HAND_POINT_ID =' '
            )
        </isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>

   		<isEmpty property="orderBy">
			REC_CREATE_TIME desc
		</isEmpty>
  		</dynamic>

	</select>

    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 业务单元代码 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
        STATUS	as "status",  <!-- 状态 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
        REMARK	as "remark",  <!-- 备注 -->
        VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
        UUID	as "uuid",  <!-- uuid -->
        TENANT_ID	as "tenantId",  <!-- 租户ID -->
        ALLOCATE_VEHICLE_NO	as "allocateVehicleNo",  <!-- 配车单号 -->
        ALLOC_TYPE	as "allocType",  <!-- 配单类型 -->
        CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆管理号 -->
        SHOW_FLAG	as "showFlag", <!-- 展示标记 -->
        ESTIMATED_TIME_OF_ARRIVAL	as "estimatedTimeOfArrival", <!-- 预计到达时间 -->
        CUSTOMER_ID AS "customerId",
        CUSTOMER_NAME AS "customerName",
        (select count(1)
        from meli.tlirl0503
        where 1 = 1
        and tlirl0502.SEG_NO = tlirl0503.SEG_NO
        and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
        AND tlirl0503.STATUS = '20') as "packCount",
        ifnull(DRIVER_NAME, ' ')        as "driverName",
        ifnull(DRIVER_TEL, ' ')         as "driverTel"
        FROM ${meliSchema}.tlirl0502 WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend="and " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend="and " property="tel">
            tlirl0502.VEHICLE_NO in (SELECT tlirl0103.VEHICLE_NO
            FROM MELI.tlirl0103
            where 1 = 1
            and tlirl0103.SEG_NO = tlirl0502.SEG_NO
            and tlirl0103.STATUS = '20'
            and tlirl0103.M_UUID IN (select tlirl0102.UUID
            from meli.tlirl0102
            where 1 = 1
            and tlirl0102.SEG_NO = tlirl0502.SEG_NO
            and tlirl0102.TEL = #tel#
            AND tlirl0102.STATUS = '20'))
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <sql id="conditionPage">
        <isNotEmpty prepend=" AND " property="segNo">
            t0502.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            t0502.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            t0502.UUID like concat('%',#uuid#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            t0502.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            t0502.VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            t0502.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            t0502.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            t0502.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            t0502.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            t0502.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            t0502.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            t0502.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            t0502.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            t0502.TENANT_ID = #tenantId#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(t0502.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(t0502.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="driverName">
            t0102.DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="tel">
            t0102.TEL = #tel#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="customerId">
            t0102.CUSTOMER_ID = #customerId#
        </isNotEmpty>
    </sql>

    <select id="queryOrder" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT t0502.SEG_NO                                                          AS "segNo",
        t0502.UNIT_CODE                                                       AS "unitCode",
        t0502.STATUS                                                          AS "status",
        t0502.REC_CREATOR                                                     AS "recCreator",
        t0502.REC_CREATOR_NAME                                                AS "recCreatorName",
        t0502.REC_CREATE_TIME                                                 AS "recCreateTime",
        t0502.REC_REVISOR                                                     AS "recRevisor",
        t0502.REC_REVISOR_NAME                                                AS "recRevisorName",
        t0502.REC_REVISE_TIME                                                 AS "recReviseTime",
        t0502.ARCHIVE_FLAG                                                    AS "archiveFlag",
        t0502.DEL_FLAG                                                        AS "delFlag",
        t0502.ALLOC_MES                                                       AS "remark",
        t0502.VEHICLE_NO                                                      AS "vehicleNo",
        t0502.UUID                                                            AS "uuid",
        t0502.TENANT_ID                                                       AS "tenantId",
        t0502.ALLOCATE_VEHICLE_NO                                             AS "allocateVehicleNo",
        t0502.ALLOC_TYPE                                                      AS "allocType",
        t0502.CAR_TRACE_NO                                                    AS "carTraceNo",
        CASE
        WHEN EXISTS (SELECT 1
        FROM meli.tlirl0401 s
        WHERE s.SEG_NO = t0502.SEG_NO
        AND s.VEHICLE_NO = t0502.VEHICLE_NO
        AND s.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        AND s.VOUCHER_NUM = t0502.ALLOCATE_VEHICLE_NO
        limit 1) THEN '1'
        when
        EXISTS (SELECT 1
        FROM meli.tlirl0402 s
        WHERE s.SEG_NO = t0502.SEG_NO
        AND s.VEHICLE_NO = t0502.VEHICLE_NO
        AND s.CAR_TRACE_NO = t0502.CAR_TRACE_NO) THEN '1'
        when
        EXISTS (SELECT 1
        FROM meli.tlirl0301 s
        WHERE s.SEG_NO = t0502.SEG_NO
        AND s.VEHICLE_NO = t0502.VEHICLE_NO
        AND s.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        and s.ALLOCATE_VEHICLE_NO = t0502.ALLOCATE_VEHICLE_NO
        and s.STATUS = '30') THEN '1'
        ELSE '0'
        END                                                               AS "isStart",
        COALESCE(t0301.CAR_TRACE_NO, t0301s_max.CAR_TRACE_NO)                 AS "carTraceNo",
        COALESCE(t0301.ENTER_FACTORY, t0301s_max.ENTER_FACTORY)               AS "intoFactoryDate",
        COALESCE(t0301.TARGET_HAND_POINT_ID, t0301s_max.TARGET_HAND_POINT_ID) AS "targetHandPointId",
        COALESCE(t0301.FACTORY_AREA, t0301s_max.FACTORY_AREA)                 AS "factoryArea"
        FROM MELI.tlirl0502 t0502
        LEFT JOIN meli.tlirl0301 t0301
        ON t0301.SEG_NO = t0502.SEG_NO
        AND t0301.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        AND t0301.VEHICLE_NO = t0502.VEHICLE_NO
        AND t0301.STATUS != '00'
        LEFT JOIN (SELECT SEG_NO,
        VEHICLE_NO,
        MAX(CAR_TRACE_NO)         AS CAR_TRACE_NO,
        MAX(ENTER_FACTORY)        AS ENTER_FACTORY,
        MAX(TARGET_HAND_POINT_ID) AS TARGET_HAND_POINT_ID,
        MAX(FACTORY_AREA)         AS FACTORY_AREA
        FROM meli.tlirl0301
        WHERE STATUS >= '20'
        AND STATUS &lt;= '40'
        AND DEL_FLAG = 0
        GROUP BY SEG_NO, VEHICLE_NO) t0301s_max
        ON t0301s_max.SEG_NO = t0502.SEG_NO
        AND t0301s_max.VEHICLE_NO = t0502.VEHICLE_NO
        WHERE t0502.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="startDate">
            substr(t0502.REC_CREATE_TIME,1,8) >= substr(replace(#startDate#,'-',''),1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">
            substr(t0502.REC_CREATE_TIME,1,8) &lt;= substr(replace(#endDate#,'-',''),1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            t0502.STATUS IN
            <iterate property="statusList" open="(" close=")" conjunction=",">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId1">
            t0502.CUSTOMER_ID IN
            <iterate property="customerId1" open="(" close=")" conjunction=",">
                #customerId1[]#
            </iterate>
        </isNotEmpty>
        <isEmpty prepend=" AND " property="statusList">
            t0502.STATUS IN ('20')
        </isEmpty>
        AND t0502.DEL_FLAG = 0
        <isNotEmpty prepend=" and " property="reservationIdentityDr">
            (EXISTS (SELECT 1
            FROM meli.tlirl0102 t2
            JOIN meli.tlirl0103 t3 ON t2.UUID = t3.M_UUID AND t2.STATUS = t3.STATUS
            WHERE t2.STATUS = '20'
            AND t2.TEL = #tel#
            AND t3.VEHICLE_NO = t0502.VEHICLE_NO))
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="reservationIdentity">
             (t0502.REC_CREATOR = #tel#
            OR t0502.REC_REVISOR = #tel#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNoList">
            t0502.ALLOCATE_VEHICLE_NO IN
            <iterate property="allocateVehicleNoList" open="(" close=")" conjunction=",">
                #allocateVehicleNoList[]#
            </iterate>
        </isNotEmpty>

        ORDER BY t0502.REC_CREATE_TIME asc

    </select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0502 WHERE 1=1
		<include refid="condition"/>
	</select>

	<!--
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocType">
			ALLOC_TYPE = #allocType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="showFlag">
			SHOW_FLAG = #showFlag#
		</isNotEmpty>
	-->
    <select id="queryPersonType" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select distinct t0504.SEG_NO as "segNo",
        t0504.PER_NO as "perNo",
        t0504.PER_TYPE as "perType"
        from ${meliSchema}.tlirl0504 t0504
        where t0504.SEG_NO = #segNo#
          and t0504.PER_NO = #perNo#
          and t0504.DEL_FLAG  = 0
    </select>

    <select id="queryInfactoryVehicle" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        <!-- 临时车有预约排队 -->
        select t0502.uuid as "uuid",
        t0401.TARGET_HAND_POINT_ID as "handPointId",  <!-- 装卸点 -->
        (select max(t0304.HAND_POINT_NAME) from ${meliSchema}.tlirl0304 t0304 where t0304.SEG_NO= t0401.SEG_NO and t0304.HAND_POINT_ID=t0401.TARGET_HAND_POINT_ID ) as "handPointName",
        t0401.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        t0201.TYPE_OF_HANDLING   as "businessType", <!-- 业务类型 -->
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE = 'P007'
        and tedcm01.ITEM_CODE = t0201.TYPE_OF_HANDLING)  as "businessTypeName",
        t0301.ENTER_FACTORY            as "intoFactoryDate" <!-- 进厂时间 -->
        from ${meliSchema}.tlirl0401 t0401,
        ${meliSchema}.tlirl0301 t0301,
        ${meliSchema}.tlirl0502 t0502,
        ${meliSchema}.tlirl0201 t0201
        where t0201.SEG_NO = t0301.SEG_NO
        and t0201.RESERVATION_NUMBER = t0301.RESERVATION_NUMBER
        and t0301.RESERVATION_NUMBER != ' '
        and t0401.DEL_FLAG = 0
        and t0401.SEG_NO = t0502.SEG_NO
        and t0401.VEHICLE_NO = t0502.VEHICLE_NO
        and t0401.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        and t0502.SEG_NO = t0301.SEG_NO
        and t0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
        and t0301.DEL_FLAG = 0
        and t0502.SEG_NO = #segNo#
        <!-- 人员类型(叉车工：1、行车工：2)
             资材卸货80、周转架40、存货退货80、零件退货80。
             装货10、卸货20、卸货+装货30、废料提货60 -->
        <isNotEmpty property="perType" prepend=" and ">
            <isEqual property="perType" compareValue="0">
                t0201.TYPE_OF_HANDLING in( '10','20','30','60','80','40','80','80')
            </isEqual>
            <isEqual property="perType" compareValue="1">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0201.TYPE_OF_HANDLING in('80','40','80','80')))
            </isEqual>
            <isEqual property="perType" compareValue="2">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0201.TYPE_OF_HANDLING in('10','20','30','60')))
            </isEqual>
        </isNotEmpty>
        union all
        <!-- 临时车有预约叫号 -->
        select t0502.uuid as "uuid",
        t0401.TARGET_HAND_POINT_ID as "handPointId",  <!-- 装卸点 -->
        (select max(t0304.HAND_POINT_NAME) from ${meliSchema}.tlirl0304 t0304 where t0304.SEG_NO= t0401.SEG_NO and t0304.HAND_POINT_ID=t0401.TARGET_HAND_POINT_ID ) as "handPointName",
        t0401.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        t0201.TYPE_OF_HANDLING   as "businessType", <!-- 业务类型 -->
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE = 'P007'
        and tedcm01.ITEM_CODE = t0201.TYPE_OF_HANDLING)  as "businessTypeName",
        t0301.ENTER_FACTORY            as "intoFactoryDate" <!-- 进厂时间 -->
        from ${meliSchema}.tlirl0402 t0401,
        ${meliSchema}.tlirl0301 t0301,
        ${meliSchema}.tlirl0502 t0502,
        ${meliSchema}.tlirl0201 t0201
        where t0201.SEG_NO = t0301.SEG_NO
        and t0201.RESERVATION_NUMBER = t0301.RESERVATION_NUMBER
        and t0301.RESERVATION_NUMBER != ' '
        and t0401.DEL_FLAG = 0
        and t0401.SEG_NO = t0502.SEG_NO
        and t0401.VEHICLE_NO = t0502.VEHICLE_NO
        and t0401.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        and t0502.SEG_NO = t0301.SEG_NO
        and t0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
        and t0301.DEL_FLAG = 0
        and t0502.SEG_NO = #segNo#
        <!-- 人员类型(叉车工：1、行车工：2)
             资材卸货80、周转架40、存货退货80、零件退货80。
             装货10、卸货20、卸货+装货30、废料提货60 -->
        <isNotEmpty property="perType" prepend=" and ">
            <isEqual property="perType" compareValue="0">
                t0201.TYPE_OF_HANDLING in( '10','20','30','60','80','40','80','80')
            </isEqual>
            <isEqual property="perType" compareValue="1">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0201.TYPE_OF_HANDLING in('80','40','80','80')))
            </isEqual>
            <isEqual property="perType" compareValue="2">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0201.TYPE_OF_HANDLING in('10','20','30','60')))
            </isEqual>
        </isNotEmpty>
        union
        <!-- 常驻车排队 -->
        select t0502.uuid as "uuid",
        t0401.TARGET_HAND_POINT_ID as "handPointId",  <!-- 装卸点 -->
        (select max(t0304.HAND_POINT_NAME) from ${meliSchema}.tlirl0304 t0304 where t0304.SEG_NO= t0401.SEG_NO and t0304.HAND_POINT_ID=t0401.TARGET_HAND_POINT_ID ) as "handPointName",
        t0401.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        t0502.ALLOC_TYPE   as "businessType", <!-- 业务类型 -->
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE = 'P007'
        and tedcm01.ITEM_CODE = t0502.ALLOC_TYPE )  as "businessTypeName",
        t0301.ENTER_FACTORY            as "intoFactoryDate" <!-- 进厂时间 -->
        from ${meliSchema}.tlirl0401 t0401,
        ${meliSchema}.tlirl0301 t0301,
        ${meliSchema}.tlirl0502 t0502
        where t0301.RESERVATION_NUMBER = ' '
        and t0401.DEL_FLAG = 0
        and t0401.SEG_NO = t0502.SEG_NO
        and t0401.VEHICLE_NO = t0502.VEHICLE_NO
        and t0401.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        and t0502.SEG_NO = t0301.SEG_NO
        and t0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
        and t0301.DEL_FLAG = 0
        and t0502.SEG_NO = #segNo#
        <!-- 人员类型(叉车工：1、行车工：2)
             资材卸货80、周转架40、存货退货80、零件退货80。
             装货10、卸货20、卸货+装货30、废料提货60 -->
        <isNotEmpty property="perType" prepend=" and ">
            <isEqual property="perType" compareValue="0">
                t0502.ALLOC_TYPE in( '10','20','30','60','80','40','80','80')
            </isEqual>
            <isEqual property="perType" compareValue="1">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0502.ALLOC_TYPE in('80','40','80','80')))
            </isEqual>
            <isEqual property="perType" compareValue="2">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0502.ALLOC_TYPE in('10','20','30','60')))
            </isEqual>
        </isNotEmpty>
        union all
        <!-- 常驻车叫号 -->
        select t0502.uuid as "uuid",
        t0401.TARGET_HAND_POINT_ID as "handPointId",  <!-- 装卸点 -->
        (select max(t0304.HAND_POINT_NAME) from ${meliSchema}.tlirl0304 t0304 where t0304.SEG_NO= t0401.SEG_NO and t0304.HAND_POINT_ID=t0401.TARGET_HAND_POINT_ID ) as "handPointName",
        t0401.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        t0502.ALLOC_TYPE     as "businessType", <!-- 业务类型 -->
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE = 'P007'
        and tedcm01.ITEM_CODE = t0502.ALLOC_TYPE  )  as "businessTypeName",
        t0301.ENTER_FACTORY            as "intoFactoryDate" <!-- 进厂时间 -->
        from ${meliSchema}.tlirl0402 t0401,
        ${meliSchema}.tlirl0301 t0301,
        ${meliSchema}.tlirl0502 t0502
        where t0301.RESERVATION_NUMBER = ' '
        and t0401.DEL_FLAG = 0
        and t0401.SEG_NO = t0502.SEG_NO
        and t0401.VEHICLE_NO = t0502.VEHICLE_NO
        and t0401.CAR_TRACE_NO = t0502.CAR_TRACE_NO
        and t0502.SEG_NO = t0301.SEG_NO
        and t0502.CAR_TRACE_NO = t0301.CAR_TRACE_NO
        and t0301.DEL_FLAG = 0
        and t0502.SEG_NO = #segNo#
        <!-- 人员类型(叉车工：1、行车工：2)
             资材卸货80、周转架40、存货退货80、零件退货80。
             装货10、卸货20、卸货+装货30、废料提货60 -->
        <isNotEmpty property="perType" prepend=" and ">
            <isEqual property="perType" compareValue="0">
                t0502.ALLOC_TYPE in( '10','20','30','60','80','40','80','80')
            </isEqual>
            <isEqual property="perType" compareValue="1">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0502.ALLOC_TYPE in('80','40','80','80')))
            </isEqual>
            <isEqual property="perType" compareValue="2">
                (t0502.SHOW_FLAG = 1 or (t0502.SHOW_FLAG = 0 and t0502.ALLOC_TYPE in('10','20','30','60')))
            </isEqual>
        </isNotEmpty>
    </select>
<!--    PDA找料查询司机信息-->
    <select id="queryVehicleInfo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select DISTINCT tlirl0502.VEHICLE_NO          as "vehicleNo",
                        tlirl0502.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
                        tlirl0502.CAR_TRACE_NO        as "carTraceNo"
        from meli.tlirl0502 tlirl0502
        where 1 = 1
          and tlirl0502.SEG_NO = #segNo#
          and tlirl0502.ALLOC_TYPE = '10'
          and tlirl0502.STATUS = '20'
    </select>
    <update id="updateShowFlag">
        UPDATE ${meliSchema}.tlirl0502
        SET
        SHOW_FLAG = #showFlag#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#  <!-- 记录修改时间 -->
        WHERE
        UUID = #uuid#
    </update>

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0502 (SEG_NO,  <!-- 业务单元代码 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录创建人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
										ALLOCATE_VEHICLE_NO,  <!-- 配车单号 -->
										ALLOC_TYPE,  <!-- 配单类型 -->
										CAR_TRACE_NO,  <!-- 车辆管理号 -->
										SHOW_FLAG,
		NEXT_ALC_VEHICLE_NO, <!-- 展示标记 -->
        ESTIMATED_TIME_OF_ARRIVAL  ,<!-- 预计到达时间 -->
		ALLOC_MES,    <!-- 配单信息 -->
        CUSTOMER_ID,
        CUSTOMER_NAME,
        PROFORMA_VOUCHER_NUM,
        EMERGENCY_DELIVERY_TIME,
		LADING_BILL_REMARK,
        NO_PLAN_FLAG,
        DRIVER_NAME,
        DRIVER_TEL,
        PROFORMA_ORDER_NUM,
        PROFORMA_SPECS_DESC
										)
	    VALUES (#segNo#, #unitCode#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
	            #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #vehicleNo#, #uuid#, #tenantId#, #allocateVehicleNo#,
	            #allocType#, #carTraceNo#, #showFlag#,#nextAlcVehicleNo#,#estimatedTimeOfArrival#,#allocMes#,#customerId#,#customerName#,
	            #proformaVoucherNum#,#emergencyDeliveryTime#,#ladingBillRemark#,#noPlanFlag#,#driverName#,#driverTel#,#proformaOrderNum#,#proformaSpecsDesc#)
	</insert>

	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0502 WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0502
		SET
		SEG_NO	= #segNo#,   <!-- 业务单元代码 -->
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					STATUS	= #status#,   <!-- 状态 -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
					REMARK	= #remark#,   <!-- 备注 -->
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->
								TENANT_ID	= #tenantId#,   <!-- 租户ID -->
					ALLOCATE_VEHICLE_NO	= #allocateVehicleNo#,   <!-- 配车单号 -->
					ALLOC_TYPE	= #allocType#,   <!-- 配单类型 -->
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆管理号 -->
					SHOW_FLAG	= #showFlag#,  <!-- 展示标记 -->
                    ESTIMATED_TIME_OF_ARRIVAL	= #estimatedTimeOfArrival# , <!-- 预计到达时间 -->
		            ALLOC_MES	= #allocMes# <!-- 配单信息 -->
        WHERE
			UUID = #uuid#
	</update>

    <update id="updateCarTraceNo">
        UPDATE ${meliSchema}.tlirl0502
        SET
        CAR_TRACE_NO = #carTraceNo#,   <!-- 车辆跟踪号 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        UUID = #uuid# and status='20'
    </update>

    <select id="queryEnteringTheFactory" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 业务单元代码 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
        STATUS	as "status",  <!-- 状态 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
        REMARK	as "remark",  <!-- 备注 -->
        VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
        UUID	as "uuid",  <!-- uuid -->
        TENANT_ID	as "tenantId",  <!-- 租户ID -->
        ALLOCATE_VEHICLE_NO	as "allocateVehicleNo",  <!-- 配车单号 -->
        ALLOC_TYPE	as "allocType",  <!-- 配单类型 -->
        CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆管理号 -->
        SHOW_FLAG	as "showFlag", <!-- 展示标记 -->
        ESTIMATED_TIME_OF_ARRIVAL	as "estimatedTimeOfArrival", <!-- 预计到达时间 -->
        (select a.FACTORY_AREA from ${meliSchema}.tlirl0304 a
        where a.SEG_NO = t.SEG_NO
        and a.HAND_POINT_ID = (select b.TARGET_HAND_POINT_ID
        from
        ${meliSchema}.tlirl0503 b
        where
        b.SEG_NO = t.SEG_NO
        and b.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
        and b.TARGET_HAND_POINT_ID is not null
        limit 1
        )limit 1) as "factoryArea",
        (select a.FACTORY_AREA_NAME from ${meliSchema}.tlirl0304 a
        where a.SEG_NO = t.SEG_NO
        and a.HAND_POINT_ID = (select b.TARGET_HAND_POINT_ID
        from
        ${meliSchema}.tlirl0503 b
        where
        b.SEG_NO = t.SEG_NO
        and b.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
        and b.TARGET_HAND_POINT_ID is not null
        limit 1
        )limit 1) as "factoryAreaName",
        (select a.CHECK_DATE from ${meliSchema}.tlirl0301 a
        where a.SEG_NO = t.SEG_NO
        and a.CAR_TRACE_NO = t.CAR_TRACE_NO
        and t.CAR_TRACE_NO != ''
        and a.STATUS != '00'
        limit 1) as "checkDate",
        (select a.TARGET_HAND_POINT_ID from ${meliSchema}.tlirl0503 a
        where a.SEG_NO = t.SEG_NO
        and a.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
        limit 1) as "targetHandPointId",
        (select a.HAND_POINT_NAME from ${meliSchema}.tlirl0304 a
        where a.SEG_NO = t.SEG_NO
        and a.HAND_POINT_ID = (select b.TARGET_HAND_POINT_ID
        from
        ${meliSchema}.tlirl0503 b
        where
        b.SEG_NO = t.SEG_NO
        and b.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
        and b.TARGET_HAND_POINT_ID is not null
        limit 1
        )limit 1) as "targetHandPointIdName"
        FROM ${meliSchema}.tlirl0502 t WHERE 1=1
         and not exists(select 1
                          from meli.tlirl0405 t0405
                         where t0405.SEG_NO = t.SEG_NO
                           and t0405.CAR_TRACE_NO = t.CAR_TRACE_NO
                           and t0405.DEL_FLAG = 0
                           and t0405.STATUS != '00')  <!-- 已进厂的不显示了 -->
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>


    <select id="queryAllocVehicleList" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT tlirl0502.VEHICLE_NO                                                                   as "vehicleNo",
        (case
        when tlirl0502.ALLOC_TYPE = '10' then '装货配单'
        when tlirl0502.ALLOC_TYPE = '20' then '卸货配单'
        when
        tlirl0502.ALLOC_TYPE = '30' then '废次材装货配单'
        end)                                                                               as "allocType",
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE((tlirl0502.REC_CREATE_TIME), '%Y%m%d%H%i%s'), now()) as "allocTime"
        from meli.tlirl0502 tlirl0502
        where tlirl0502.SEG_NO = #segNo#
        and not exists(select 1
        from meli.tlirl0405 t0415
        where t0415.SEG_NO = tlirl0502.SEG_NO
        and t0415.CAR_TRACE_NO = tlirl0502.CAR_TRACE_NO
        and t0415.VEHICLE_NO = tlirl0502.VEHICLE_NO
        and t0415.DEL_FLAG = 0
        and t0415.STATUS != '00')  <!-- 已进厂的不显示了 -->
        and tlirl0502.STATUS = '20'
        and tlirl0502.DEL_FLAG = '0'

    </select>

    <update id="updateStatus">
        UPDATE ${meliSchema}.tlirl0502
        SET
        REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->
        REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
        REMARK	= #remark#,  <!-- 备注 -->
        STATUS	= #status#   <!-- 备注 -->
        WHERE 1=1
        and SEG_NO = #segNo#
        <isNotEmpty prepend="and" property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="allocateVehicleNoAdd">
            ALLOCATE_VEHICLE_NO IN
            <iterate open="(" close=")" conjunction="," property="allocateVehicleNoAdd">
                #allocateVehicleNoAdd[]#
            </iterate>
        </isNotEmpty>
    </update>

    <update id="updateStatusAllocateVehicleNo">
        UPDATE ${meliSchema}.tlirl0502
        SET
        <isNotEmpty property="nextAlcVehicleNo">
        NEXT_ALC_VEHICLE_NO=#nextAlcVehicleNo#
        </isNotEmpty>
        WHERE 1=1
        and SEG_NO = #segNo#
        <isNotEmpty prepend="and" property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
    </update>

    <update id="updateStatusAllocate">
        UPDATE ${meliSchema}.tlirl0502
        SET
        <isNotEmpty property="noPlanFlag">
            NO_PLAN_FLAG = '20'
        </isNotEmpty>
        WHERE 1=1
        and SEG_NO = #segNo#
        <isNotEmpty prepend="and" property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
    </update>


    <update id="updateCarNo">
        UPDATE ${meliSchema}.tlirl0502
        SET
        <isNotEmpty property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend="," property="allocStartTime">
            ALLOC_START_TIME = #allocStartTime#
        </isNotEmpty>
        WHERE 1=1
        and SEG_NO = #segNo#
        <isNotEmpty prepend="and" property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
    </update>

    <select id="queryAllVoucherNum" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select distinct tlirl0503.VOUCHER_NUM as "ladingBillId",
               tlirl0503.CUSTOMER_ID as "settleUserNum",
               tlirl0503.CUSTOMER_NAME as "settleUserName",
               tlirl0503.WAREHOUSE_CODE as "warehouseCode",
               tlirl0503.WAREHOUSE_NAME as "warehouseName",
               tlirl0503.BILLING_METHOD as "billingMethod"
        from meli.tlirl0502 tlirl0502,
             meli.tlirl0503 tlirl0503
        where 1 = 1
          and tlirl0502.SEG_NO = tlirl0503.SEG_NO
          and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
          and tlirl0502.STATUS = '20'
        <isNotEmpty prepend="and" property="statusIn">
        tlirl0503.STATUS='20'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="allocateVehicleNo">
                and tlirl0502.ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
            </isNotEmpty>
          <isNotEmpty prepend="and" property="allocType">
              tlirl0502.ALLOC_TYPE='10'
          </isNotEmpty>
          and tlirl0502.CAR_TRACE_NO = #carTraceNo#
          and tlirl0502.VEHICLE_NO = #vehicleNo#

    </select>


    <select id="queryAllVoucherNumPack"  parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select DISTINCT PACK_ID AS "packId",
        TARGET_HAND_POINT_ID as "handPointId"
        from meli.tlirl0503
        where 1 = 1
          and tlirl0503.STATUS = '20'
          and tlirl0503.SEG_NO = #segNo#
          <isNotEmpty prepend="and" property="voucherNumList">
              tlirl0503.VOUCHER_NUM in
              <iterate property="voucherNumList" open="("
                       close=")" conjunction=" , ">
                  #voucherNumList[]#
              </iterate>
          </isNotEmpty>
          and not exists(select 1
                         from meli.tlirl0308 tlirl0308
                         where 1 = 1
                           and tlirl0308.CAR_TRACE_NO = #carTraceNo#
                            <isNotEmpty prepend="and" property="voucherNumList">
                                tlirl0308.VOUCHER_NUM in
                                <iterate property="voucherNumList" open="("
                                         close=")" conjunction=" , ">
                                    #voucherNumList[]#
                                </iterate>
                            </isNotEmpty>
                           and tlirl0308.PACK_ID = tlirl0503.PACK_ID
                           and tlirl0308.VEHICLE_NO = tlirl0308.VEHICLE_NO
                           and tlirl0308.PUT_IN_OUT_FLAG='20'
        )
                           and tlirl0503.ALLOCATE_VEHICLE_NO=#allocateVehicleNo#
    </select>

    <select id="queryAllVoucherNumAllPack"  parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select DISTINCT PACK_ID AS "packId",
        TARGET_HAND_POINT_ID as "handPointId",
        M_PACK_ID as "mPackId"
        from meli.tlirl0503
        where 1 = 1
        and tlirl0503.STATUS in ('20','30')
        and tlirl0503.SEG_NO = #segNo#
        <isNotEmpty prepend="and" property="voucherNumList">
            tlirl0503.VOUCHER_NUM in
            <iterate property="voucherNumList" open="("
                     close=")" conjunction=" , ">
                #voucherNumList[]#
            </iterate>
        </isNotEmpty>
        and tlirl0503.ALLOCATE_VEHICLE_NO=#allocateVehicleNo#
    </select>

    <!--取消配单-->
    <update id="deleteByAllocateVehicleNo">
        UPDATE ${meliSchema}.tlirl0502
        SET
        REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->
        REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG	= 1,   <!-- 记录删除标记 -->
        REMARK	= #remark#,  <!-- 备注 -->
        STATUS	= '00'   <!-- 备注 -->
        WHERE 1=1
        and SEG_NO = #segNo#
        <isNotEmpty prepend="and" property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>

    </update>

    <select id="queryEmergencyInfo"  parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select tlirl0502.CAR_TRACE_NO as "carTraceNo",
               tlirl0502.SEG_NO as "segNo",
               tlirl0502.EMERGENCY_DELIVERY_TIME as "emergencyDeliveryTime",
               tlirl0502.CUSTOMER_ID as "customerId",
               tlirl0502.VEHICLE_NO as "vehicleNo",
               tlirl0502.REC_CREATOR as "recCreator",
               tlirl0502.REC_CREATOR_NAME as "recCreatorName",
               (select count(1)
                from meli.tlirl0503 tlirl0503
                where 1 = 1
                  and tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  and tlirl0503.STATUS in ('20')
                  and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO) * 5 as "packMin",
               (SELECT GROUP_CONCAT(DISTINCT tlirl0503.VOUCHER_NUM)
                FROM meli.tlirl0503
                WHERE tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  AND tlirl0503.STATUS IN ('20' )
                  AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO)     AS "voucherNums",
               (SELECT GROUP_CONCAT(DISTINCT tlirl0503.SPECS_DESC)
                FROM meli.tlirl0503
                WHERE tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  AND tlirl0503.STATUS IN ('20')
                  AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO)     AS "specsDescs",
               (SELECT GROUP_CONCAT(DISTINCT tlirl0503.LOCATION_NAME)
                FROM meli.tlirl0503
                WHERE tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  AND tlirl0503.STATUS IN ('20')
                  AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO)     AS "locationNames"
        from meli.tlirl0502 tlirl0502
        where 1 = 1
          and SEG_NO = #segNo#
          and ALLOC_TYPE = '10'
          AND EMERGENCY_DELIVERY_TIME IS NOT NULL
          AND EMERGENCY_DELIVERY_TIME != ' '
          and STATUS='20'
  and not exists(select 1
                 from meli.tlirl0405 tlirl0405
                 where 1 = 1
                   and tlirl0405.SEG_NO = tlirl0502.SEG_NO
                   and tlirl0405.CAR_TRACE_NO
                     = tlirl0502.CAR_TRACE_NO
                 limit 1)
        union
        select tlirl0502.CAR_TRACE_NO as "carTraceNo",
               tlirl0502.SEG_NO as "segNo",
               tlirl0502.EMERGENCY_DELIVERY_TIME as "emergencyDeliveryTime",
               tlirl0502.CUSTOMER_ID as "customerId",
               tlirl0502.VEHICLE_NO as "vehicleNo",
               tlirl0502.REC_CREATOR as "recCreator",
               tlirl0502.REC_CREATOR_NAME as "recCreatorName",
               (select count(1)
                from meli.tlirl0503 tlirl0503
                where 1 = 1
                  and tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  and tlirl0503.STATUS in ('20')
                  and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO) * 5 as "packMin",
               (SELECT GROUP_CONCAT(DISTINCT tlirl0503.VOUCHER_NUM)
                FROM meli.tlirl0503
                WHERE tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  AND tlirl0503.STATUS IN ('20')
                  AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO)     AS "voucherNums",
               (SELECT GROUP_CONCAT(DISTINCT tlirl0503.SPECS_DESC)
                FROM meli.tlirl0503
                WHERE tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  AND tlirl0503.STATUS IN ('20')
                  AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO)     AS "specsDescs",
               (SELECT GROUP_CONCAT(DISTINCT tlirl0503.LOCATION_NAME)
                FROM meli.tlirl0503
                WHERE tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  AND tlirl0503.STATUS IN ('20')
                  AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO)     AS "locationNames"
        from meli.tlirl0502 tlirl0502
        where 1 = 1
          and SEG_NO = #segNo#
          and ALLOC_TYPE = '10'
          AND EMERGENCY_DELIVERY_TIME IS NOT NULL
          AND EMERGENCY_DELIVERY_TIME != ' '
          and STATUS='20'
  and exists(select 1
             from meli.tlirl0301 tlirl0301
             where 1 = 1
               and tlirl0301.SEG_NO = tlirl0502.SEG_NO
               and tlirl0301.CAR_TRACE_NO = tlirl0502.CAR_TRACE_NO
               and tlirl0301.STATUS = '20'
               and tlirl0301.TARGET_HAND_POINT_ID = ' '
             limit 1)
  and not exists(select 1
                 from meli.tlirl0401 tlirl0401
                 where 1 = 1
                   and tlirl0401.SEG_NO = tlirl0502.SEG_NO
                   and tlirl0401.CAR_TRACE_NO = tlirl0502.CAR_TRACE_NO
                 limit 1)
    </select>

    <!-- 根据车牌号和车辆跟踪号查询捆包数据（重庆看板用） -->
    <select id="queryPackDataForKanbanCQ" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select tlirl0502.VEHICLE_NO as "vehicleNo",
               tlirl0502.CAR_TRACE_NO as "carTraceNo",
               tlirl0502.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
               (select count(1)
                from meli.tlirl0503 tlirl0503
                where 1 = 1
                  and tlirl0503.SEG_NO = tlirl0502.SEG_NO
                  and tlirl0503.STATUS in ('20')
                  and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO) * 5 as "packCount"
        from meli.tlirl0502 tlirl0502
        where 1 = 1
          and tlirl0502.SEG_NO = #segNo#
          and tlirl0502.VEHICLE_NO = #vehicleNo#
          and tlirl0502.CAR_TRACE_NO = #carTraceNo#
          and tlirl0502.ALLOC_TYPE = '10'
          and tlirl0502.STATUS = '20'
    </select>

    <!--IMOM我的配单页面查询    -->
    <select id="queryPage" parameterClass="java.util.HashMap" resultClass="com.baosight.imom.li.rl.dao.LIRL0502">
        SELECT
        SEG_NO as "segNo",  <!-- 业务单元代码 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ALLOCATE_VEHICLE_NO as "allocateVehicleNo",  <!-- 配车单号 -->
        ALLOC_TYPE as "allocType",  <!-- 配单类型 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆管理号 -->
        SHOW_FLAG as "showFlag", <!-- 展示标记 -->
        ESTIMATED_TIME_OF_ARRIVAL as "estimatedTimeOfArrival", <!-- 预计到达时间 -->
        CUSTOMER_ID AS "customerId",
        CUSTOMER_NAME AS "customerName",
        NEXT_ALC_VEHICLE_NO as "nextAlcVehicleNo",
        PROFORMA_VOUCHER_NUM as "proformaVoucherNum",
        EMERGENCY_DELIVERY_TIME as "emergencyDeliveryTime",
        LADING_BILL_REMARK as "ladingBillRemark"
        FROM ${meliSchema}.tlirl0502 t
        WHERE SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            ALLOCATE_VEHICLE_NO LIKE '%$allocateVehicleNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO LIKE '%$vehicleNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocType">
            ALLOC_TYPE = #allocType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO LIKE '%$carTraceNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS IN ('20','99')
        </isEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTimeStart">
            REC_CREATE_TIME >= #recCreateTimeStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTimeEnd">
            REC_CREATE_TIME &lt;= #recCreateTimeEnd#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            EXISTS
            (SELECT 1 FROM
            ${meliSchema}.tlirl0503 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
            AND t1.WAREHOUSE_CODE = #warehouseCode#
            AND t1.STATUS > '00'
            AND t1.DEL_FLAG = 0)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            EXISTS
            (SELECT 1 FROM
            ${meliSchema}.tlirl0503 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
            AND t1.PACK_ID LIKE '%$packId$%'
            AND t1.STATUS > '00'
            AND t1.DEL_FLAG = 0)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            EXISTS
            (SELECT 1 FROM
            ${meliSchema}.tlirl0503 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
            AND t1.VOUCHER_NUM LIKE '%$voucherNum$%'
            AND t1.STATUS > '00'
            AND t1.DEL_FLAG = 0)
        </isNotEmpty>

        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <!--更新配车单状态不为完成的明细数据,更新状态为完成，拼接备注-->
    <update id="updateCompleteByAllocateVehicleNo">
        UPDATE meli.tlirl0502
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        REMARK = CONCAT(REMARK,';',#remark#),  <!-- 备注 -->
        STATUS = #status#   <!-- 状态 -->
        WHERE SEG_NO = #segNo#
        AND ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
        AND STATUS NOT IN ('00','99')
        AND DEL_FLAG = 0
    </update>

    <select id="queryAllVoucherNumAllocate" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select tlirl0502.ALLOCATE_VEHICLE_NO AS "allocateVehicleNo",
               tlirl0503.VOUCHER_NUM         AS "voucherNum",
               tlirl0502.CAR_TRACE_NO        AS "carTraceNo"
        from meli.tlirl0503 tlirl0503,
             meli.tlirl0502 tlirl0502
        where tlirl0502.STATUS = '20'
          and tlirl0502.SEG_NO = tlirl0503.SEG_NO
          and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
          and tlirl0502.ALLOC_TYPE = '10'
          and tlirl0503.STATUS = '20'
          and tlirl0502.SEG_NO = #segNo#
        group by tlirl0503.VOUCHER_NUM, tlirl0502.ALLOCATE_VEHICLE_NO, tlirl0502.CAR_TRACE_NO
    </select>
</sqlMap>