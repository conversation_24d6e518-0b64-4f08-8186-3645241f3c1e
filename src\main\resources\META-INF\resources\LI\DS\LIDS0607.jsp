<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                              
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-factoryArea" cname="厂区" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryBuilding" cname="厂房" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-crossArea" cname="跨区" colWidth="3" placeholder="模糊条件"/>
        </div>

    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="跨区库容统计">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" >
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"

                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="factoryArea" cname="厂区" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="factoryBuilding" cname="厂房" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="crossArea" cname="跨区" align="left" width="120"
                              />
                <EF:EFColumn ename="crossAreaName" cname="跨区名称" align="left" width="120"
                              />
                <EF:EFColumn ename="beExpectedTo" cname="预计存放数量" align="left" width="120"
                              />
                <EF:EFColumn ename="current" cname="当前存放数量" align="left" width="120"
                              />
                <EF:EFColumn ename="remaining" cname="剩余数量" align="left" width="120"
                              />
            </EF:EFGrid>
        </div>
        <div id="info-2" title="库位库容统计">
            <EF:EFGrid isFloat="true" id="result2" blockId="result2" autoBind="false" autoDraw="no" needAuth="true" queryMethod="queryLitter">
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="factoryArea" cname="厂区" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="factoryBuilding" cname="厂房" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="left" width="120" enable="false"
                              />
                <EF:EFColumn ename="crossArea" cname="跨区" align="left" width="120"
                              />
                <EF:EFColumn ename="crossAreaName" cname="跨区名称" align="left" width="120"
                              />
                <EF:EFColumn ename="locationId" cname="库位" align="left" width="120"
                              />
                <EF:EFColumn ename="locationName" cname="库位名称" align="left" width="120"
                              />
                <EF:EFColumn ename="beExpectedTo" cname="预计存放数量" align="left" width="120"
                              />
                <EF:EFColumn ename="current" cname="当前存放数量" align="left" width="120"
                              />
                <EF:EFColumn ename="remaining" cname="剩余数量" align="left" width="120"
                              />
            </EF:EFGrid>
        </div>
    </EF:EFTab>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--区域代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="crossingChannelsInfo" width="90%" height="60%"/>
    <%--跨区编码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo2" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseCodeInfo" width="90%" height="60%"/>
</EF:EFPage>
