/**
 * Generate time : 2025-05-27 15:02:24
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm1201
 *
 */
public class Tvgdm1201 extends DaoEPBase {

    private String unitCode = " ";        /* 业务单元代码*/
    private String segNo = " ";        /* 系统帐套*/
    private String particularYear = "";
    /**年份**/
    private String deptName = "";/*部门*/
    private BigDecimal annualProcessingVolume = new BigDecimal("0");        /* 年度加工量*/
    private BigDecimal annualProductionRate = new BigDecimal("0");        /* 年度达产率*/
    private BigDecimal steelManufacturingCostAccum = new BigDecimal("0");
    private BigDecimal actualManufacturingCostTon = new BigDecimal("0");
    private BigDecimal manufacturingCostCompletion = new BigDecimal("0");
    private BigDecimal productionRateAccumulated = new BigDecimal("0");
    private BigDecimal productionCapacityActual = new BigDecimal("0");
    private BigDecimal productionCapacityCompletion = new BigDecimal("0");
    private BigDecimal oeeEquipmentRateAccumulated = new BigDecimal("0");
    private BigDecimal oeeEquipmentRate = new BigDecimal("0");
    private BigDecimal qualityDisputeAccumulated = new BigDecimal("0");
    private BigDecimal qualityDisputeCompletion = new BigDecimal("0");
    private BigDecimal managementImprovementRate = new BigDecimal("0");
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("particularYear");
        eiColumn.setDescName("年份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deptName");
        eiColumn.setDescName("部门");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("annualProcessingVolume");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("年度加工量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("annualProductionRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(2);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("年度达产率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("steelManufacturingCostAccum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("吨钢制造费用累计");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualManufacturingCostTon");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("吨钢制造费用实绩");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("manufacturingCostCompletion");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("吨钢制造费用完成度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productionRateAccumulated");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("达产率累计");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productionCapacityActual");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("达产率实绩");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productionCapacityCompletion");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("达产率完成度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("oeeEquipmentRateAccumulated");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("累计OEE设备综合利用率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("oeeEquipmentRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("OEE设备综合利用率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityDisputeAccumulated");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("质量异议PPM累计值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityDisputeCompletion");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("质量异议完成度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("managementImprovementRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("工厂管理提升");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm1201() {
        initMetaData();
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the segNo - 系统帐套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    public String getParticularYear() {
        return particularYear;
    }

    public void setParticularYear(String particularYear) {
        this.particularYear = particularYear;
    }

    /**
     * get the annualProcessingVolume - 年度加工量
     * @return the annualProcessingVolume
     */
    public BigDecimal getAnnualProcessingVolume() {
        return this.annualProcessingVolume;
    }

    /**
     * set the annualProcessingVolume - 年度加工量
     */
    public void setAnnualProcessingVolume(BigDecimal annualProcessingVolume) {
        this.annualProcessingVolume = annualProcessingVolume;
    }

    /**
     * get the annualProductionRate - 年度达产率
     * @return the annualProductionRate
     */
    public BigDecimal getAnnualProductionRate() {
        return this.annualProductionRate;
    }

    /**
     * set the annualProductionRate - 年度达产率
     */
    public void setAnnualProductionRate(BigDecimal annualProductionRate) {
        this.annualProductionRate = annualProductionRate;
    }

    /**
     * get the uuid - 唯一编码
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public BigDecimal getSteelManufacturingCostAccum() {
        return steelManufacturingCostAccum;
    }

    public void setSteelManufacturingCostAccum(BigDecimal steelManufacturingCostAccum) {
        this.steelManufacturingCostAccum = steelManufacturingCostAccum;
    }

    public BigDecimal getActualManufacturingCostTon() {
        return actualManufacturingCostTon;
    }

    public void setActualManufacturingCostTon(BigDecimal actualManufacturingCostTon) {
        this.actualManufacturingCostTon = actualManufacturingCostTon;
    }

    public BigDecimal getManufacturingCostCompletion() {
        return manufacturingCostCompletion;
    }

    public void setManufacturingCostCompletion(BigDecimal manufacturingCostCompletion) {
        this.manufacturingCostCompletion = manufacturingCostCompletion;
    }

    public BigDecimal getProductionRateAccumulated() {
        return productionRateAccumulated;
    }

    public void setProductionRateAccumulated(BigDecimal productionRateAccumulated) {
        this.productionRateAccumulated = productionRateAccumulated;
    }

    public BigDecimal getProductionCapacityActual() {
        return productionCapacityActual;
    }

    public void setProductionCapacityActual(BigDecimal productionCapacityActual) {
        this.productionCapacityActual = productionCapacityActual;
    }

    public BigDecimal getProductionCapacityCompletion() {
        return productionCapacityCompletion;
    }

    public void setProductionCapacityCompletion(BigDecimal productionCapacityCompletion) {
        this.productionCapacityCompletion = productionCapacityCompletion;
    }

    public BigDecimal getOeeEquipmentRate() {
        return oeeEquipmentRate;
    }

    public void setOeeEquipmentRate(BigDecimal oeeEquipmentRate) {
        this.oeeEquipmentRate = oeeEquipmentRate;
    }

    public BigDecimal getQualityDisputeCompletion() {
        return qualityDisputeCompletion;
    }

    public void setQualityDisputeCompletion(BigDecimal qualityDisputeCompletion) {
        this.qualityDisputeCompletion = qualityDisputeCompletion;
    }

    public BigDecimal getManagementImprovementRate() {
        return managementImprovementRate;
    }

    public void setManagementImprovementRate(BigDecimal managementImprovementRate) {
        this.managementImprovementRate = managementImprovementRate;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setParticularYear(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("particularYear")), particularYear));
        setAnnualProcessingVolume(NumberUtils.toBigDecimal(StringUtils.toString(map.get("annualProcessingVolume")), annualProcessingVolume));
        setAnnualProductionRate(NumberUtils.toBigDecimal(StringUtils.toString(map.get("annualProductionRate")), annualProductionRate));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSteelManufacturingCostAccum(NumberUtils.toBigDecimal(StringUtils.toString(map.get("steelManufacturingCostAccum")), steelManufacturingCostAccum));
        setActualManufacturingCostTon(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actualManufacturingCostTon")), actualManufacturingCostTon));
        setManufacturingCostCompletion(NumberUtils.toBigDecimal(StringUtils.toString(map.get("manufacturingCostCompletion")), manufacturingCostCompletion));
        setProductionRateAccumulated(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productionRateAccumulated")), productionRateAccumulated));
        setProductionCapacityActual(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productionCapacityActual")), productionCapacityActual));
        setProductionCapacityCompletion(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productionCapacityCompletion")), productionCapacityCompletion));
        setOeeEquipmentRateAccumulated(NumberUtils.toBigDecimal(StringUtils.toString(map.get("oeeEquipmentRateAccumulated")), oeeEquipmentRateAccumulated));
        setOeeEquipmentRate(NumberUtils.toBigDecimal(StringUtils.toString(map.get("oeeEquipmentRate")), oeeEquipmentRate));
        setQualityDisputeAccumulated(NumberUtils.toBigDecimal(StringUtils.toString(map.get("qualityDisputeAccumulated")), qualityDisputeAccumulated));
        setQualityDisputeCompletion(NumberUtils.toBigDecimal(StringUtils.toString(map.get("qualityDisputeCompletion")), qualityDisputeCompletion));
        setManagementImprovementRate(NumberUtils.toBigDecimal(StringUtils.toString(map.get("managementImprovementRate")), managementImprovementRate));
        setDeptName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deptName")), deptName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("particularYear", StringUtils.toString(particularYear, eiMetadata.getMeta("particularYear")));
        map.put("annualProcessingVolume", StringUtils.toString(annualProcessingVolume, eiMetadata.getMeta("annualProcessingVolume")));
        map.put("annualProductionRate", StringUtils.toString(annualProductionRate, eiMetadata.getMeta("annualProductionRate")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("steelManufacturingCostAccum", StringUtils.toString(steelManufacturingCostAccum, eiMetadata.getMeta("steelManufacturingCostAccum")));
        map.put("actualManufacturingCostTon", StringUtils.toString(actualManufacturingCostTon, eiMetadata.getMeta("actualManufacturingCostTon")));
        map.put("manufacturingCostCompletion", StringUtils.toString(manufacturingCostCompletion, eiMetadata.getMeta("manufacturingCostCompletion")));
        map.put("productionRateAccumulated", StringUtils.toString(productionRateAccumulated, eiMetadata.getMeta("productionRateAccumulated")));
        map.put("productionCapacityActual", StringUtils.toString(productionCapacityActual, eiMetadata.getMeta("productionCapacityActual")));
        map.put("productionCapacityCompletion", StringUtils.toString(productionCapacityCompletion, eiMetadata.getMeta("productionCapacityCompletion")));
        map.put("oeeEquipmentRateAccumulated", StringUtils.toString(oeeEquipmentRateAccumulated, eiMetadata.getMeta("oeeEquipmentRateAccumulated")));
        map.put("oeeEquipmentRate", StringUtils.toString(oeeEquipmentRate, eiMetadata.getMeta("oeeEquipmentRate")));
        map.put("qualityDisputeAccumulated", StringUtils.toString(qualityDisputeAccumulated, eiMetadata.getMeta("qualityDisputeAccumulated")));
        map.put("qualityDisputeCompletion", StringUtils.toString(qualityDisputeCompletion, eiMetadata.getMeta("qualityDisputeCompletion")));
        map.put("managementImprovementRate", StringUtils.toString(managementImprovementRate, eiMetadata.getMeta("managementImprovementRate")));
        map.put("deptName", StringUtils.toString(deptName, eiMetadata.getMeta("deptName")));
        return map;

    }

    public BigDecimal getOeeEquipmentRateAccumulated() {
        return oeeEquipmentRateAccumulated;
    }

    public void setOeeEquipmentRateAccumulated(BigDecimal oeeEquipmentRateAccumulated) {
        this.oeeEquipmentRateAccumulated = oeeEquipmentRateAccumulated;
    }

    public BigDecimal getQualityDisputeAccumulated() {
        return qualityDisputeAccumulated;
    }

    public void setQualityDisputeAccumulated(BigDecimal qualityDisputeAccumulated) {
        this.qualityDisputeAccumulated = qualityDisputeAccumulated;
    }
}