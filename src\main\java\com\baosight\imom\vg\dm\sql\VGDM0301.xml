<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0301">

    <sql id="queryResult">
        SELECT
        TAG_IHD_ID as "tagIhdId",  <!-- 点位数据库id -->
        TAG_ID as "tagId",  <!-- 点位ID -->
        TAG_TYPE as "tagType",  <!-- 点位类型 -->
        TAG_DESC as "tagDesc",  <!-- 点位描述 -->
        DATA_TYPE as "dataType",  <!-- 数据类型 -->
        DRIVER_TYPE as "driverType",  <!-- 驱动类型 -->
        DEVICE_ID as "deviceId",  <!-- 设备ID -->
        DEVICE_ADDRESS as "deviceAddress",  <!-- 设备地址 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        ALARM_CLASS as "alarmClass",  <!-- 报警分类 -->
        SCADA_NAME as "scadaName",  <!-- 节点名 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0301
    </sql>

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="scadaName">
            SCADA_NAME = #scadaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            DEVICE_CODE = #deviceCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="alarmClass">
            ALARM_CLASS = #alarmClass#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tagId">
            TAG_ID like concat('%',#tagId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tagDesc">
            TAG_DESC like concat('%',#tagDesc#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0301">
        <include refid="queryResult"/>
        WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                TAG_ID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0301 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryById" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0301">
        <include refid="queryResult"/>
        WHERE TAG_ID = #tagId# AND DEL_FLAG = '0'
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0301 (
        TAG_IHD_ID,  <!-- 点位数据库id -->
        TAG_ID,  <!-- 点位ID -->
        TAG_TYPE,  <!-- 点位类型 -->
        TAG_DESC,  <!-- 点位描述 -->
        DATA_TYPE,  <!-- 数据类型 -->
        DRIVER_TYPE,  <!-- 驱动类型 -->
        DEVICE_ID,  <!-- 设备ID -->
        DEVICE_ADDRESS,  <!-- 设备地址 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        ALARM_CLASS,  <!-- 报警分类 -->
        SCADA_NAME,  <!-- 节点名 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#tagIhdId#, #tagId#, #tagType#, #tagDesc#, #dataType#, #driverType#, #deviceId#, #deviceAddress#,
        #deviceCode#,
        #deviceName#, #eArchivesNo#, #equipmentName#, #alarmClass#, #scadaName#, #uuid#, #recCreator#, #recCreatorName#,
        #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#,
        #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0301 WHERE
        SCADA_NAME = #scadaName#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0301
        SET
        TAG_IHD_ID = #tagIhdId#,   <!-- 点位数据库id -->
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        ALARM_CLASS = #alarmClass#,   <!-- 报警分类 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateTag">
        UPDATE ${mevgSchema}.TVGDM0301
        SET
        TAG_TYPE = #tagType#,   <!-- 点位类型 -->
        TAG_DESC = #tagDesc#,   <!-- 点位描述 -->
        DATA_TYPE = #dataType#,   <!-- 数据类型 -->
        DRIVER_TYPE = #driverType#,   <!-- 驱动类型 -->
        DEVICE_ID = #deviceId#,   <!-- 设备ID -->
        DEVICE_ADDRESS = #deviceAddress#,   <!-- 设备地址 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>