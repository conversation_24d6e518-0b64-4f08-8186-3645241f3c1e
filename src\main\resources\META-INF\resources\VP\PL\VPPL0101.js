$(function () {
//查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {
                //生效(状态不为新增，不可生效)
                $("#VALIDATE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "10" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据" + rows[i].chipId + ",状态不为新增,不可生效!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "VPPL0101", "validateCross", true, null, null, false);
                });

                //反生效(状态不为生效，不可反生效)
                $("#DEVALIDATION").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "20" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据" +rows[i].chipId + ",状态不为生效,不可反生效!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "VPPL0101", "deValidateCross", true, null, null, false);
                });

                /**
                 * 大数据后端导出
                 */
                $("#EXPORTEXCEL").on("click", function () {
                    let segNo = $("#inqu_status-0-segNo").val();
                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil("请选择业务单元代码！", "error");
                        return;
                    }
                    let fileName = segNo + "员工维护导出" + ".xlsx";
                    let exportEi = new EiInfo();
                    exportEi.setByNode("inqu");
                    IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                    exportEi.set("exportColumnBlock", 'fileName', fileName);
                    IMOMUtil.callService({
                        service: "VPPL0101",
                        method: "postExport",
                        eiInfo: exportEi,
                        showProgress: true,
                        async: true,
                        callback: function (ei) {
                            if (ei.status > -1) {
                                let docUrl = ei.getBlock("excelDoc").get("docUrl");
                                window.open(docUrl);
                            }
                        }
                    });
                });
            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },


        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });
})
