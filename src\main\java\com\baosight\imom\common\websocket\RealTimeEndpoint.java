package com.baosight.imom.common.websocket;

import com.baosight.imom.common.utils.LogUtils;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;

import java.util.concurrent.CopyOnWriteArraySet;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;

/**
 * websocket服务端
 *
 * <AUTHOR> 郁在杰
 * @Description : 用于接收前端消息，根据需要发送消息至前端
 * @Date : 2024/11/29
 * @Version : 1.0
 */
@ServerEndpoint(value = "/RealTimeEndpoint")
public class RealTimeEndpoint extends Endpoint {
    private static final Logger LOGGER = LoggerFactory.getLogger(RealTimeEndpoint.class);

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的Session对象。
     */
    private static final CopyOnWriteArraySet<Session> SESSION_SET = new CopyOnWriteArraySet<>();

    /**
     * 开启连接
     *
     * @param session        会话
     * @param endpointConfig 端点配置
     */
    @Override
    public void onOpen(Session session, EndpointConfig endpointConfig) {
        LOGGER.info("RealTimeEndpoint开启连接");
        LogUtils.log("RealTimeEndpoint开启连接");
        session.addMessageHandler(new MessageHandler.Whole<String>() {
            public void onMessage(String s) {
                // todo 接收消息处理
                LOGGER.info("RealTimeEndpoint接收内容：" + s);
                LogUtils.log("RealTimeEndpoint接收内容：" + s);
            }
        });
        // todo 根据来源创建不同的分组，用于后续消息推送时区分不同的用户
        SESSION_SET.add(session);
        LOGGER.info("RealTimeEndpoint开启连接后添加会话:" + session.getId());
        LogUtils.log("RealTimeEndpoint开启连接后添加会话:" + session.getId());
    }

    /**
     * 关闭连接
     *
     * @param session     会话
     * @param closeReason 关闭原因
     */
    @Override
    public void onClose(Session session, CloseReason closeReason) {
        LOGGER.info("RealTimeEndpoint关闭连接:" + closeReason.getCloseCode() + "|" + closeReason.getReasonPhrase());
        LogUtils.log("RealTimeEndpoint关闭连接:" + closeReason.getCloseCode() + "|" + closeReason.getReasonPhrase());
        SESSION_SET.remove(session);
        LOGGER.info("RealTimeEndpoint关闭连接后移除会话:" + session.getId());
        LogUtils.log("RealTimeEndpoint关闭连接后移除会话:" + session.getId());
    }

    /**
     * 连接报错
     *
     * @param session 会话
     * @param thr     异常
     */
    @Override
    public void onError(Session session, Throwable thr) {
        LOGGER.error("RealTimeEndpoint连接报错:" + thr.getMessage());
        LogUtils.error("RealTimeEndpoint连接报错:" , thr.getMessage());
        SESSION_SET.remove(session);
        LOGGER.info("RealTimeEndpoint连接报错后移除会话:" + session.getId());
        LogUtils.log("RealTimeEndpoint连接报错后移除会话:" + session.getId());
    }

    /**
     * 发送消息
     *
     * @param message 消息内容
     */
    public static void sendMessage(String message) {
        LOGGER.info("RealTimeEndpoint发送消息开始");
        LogUtils.log("RealTimeEndpoint发送消息开始");
        int i = 1;
        for (Session session : SESSION_SET) {
            if (session.isOpen()) {
                LogUtils.log("RealTimeEndpoint发送消息:" + i + "|sessionId:" + session.getId());
                session.getAsyncRemote().sendText(message);
            }
            i++;
        }
        LogUtils.log("RealTimeEndpoint发送消息结束");
    }
}
