<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-28 14:53:59
       Version :  1.0
    tableName :meli.tlids0602
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     BENCHMARK_FACTORY_AREA  VARCHAR   NOT NULL,
     POLLING_FACTORY_AREA  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     WAREHOUSE_CODE  VARCHAR,
     <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CROSS_REGIONAL  VARCHAR,
     POLLING_ACROSS_REGIONS  VARCHAR,
     POLLING_SCHEME_NUMBER  VARCHAR,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR
-->
<sqlMap namespace="LIDS0606">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="wproviderId">
            WPROVIDER_ID = #wproviderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locViewId">
            LOC_VIEW_ID = #locViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locColumnId">
            LOC_COLUMN_ID = #locColumnId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="useStatus">
            USE_STATUS = #useStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationType">
            LOCATION_TYPE = #locationType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationLength">
            LOCATION_LENGTH = #locationLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="specUpper">
            SPEC_UPPER = #specUpper#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="specLower">
            SPEC_LOWER = #specLower#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointStart">
            X_POINT_START = #x_pointStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointEnd">
            X_POINT_END = #x_pointEnd#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="y_pointCenter">
            Y_POINT_CENTER = #y_pointCenter#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="leftViewId">
            LEFT_VIEW_ID = #leftViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rightViewId">
            RIGHT_VIEW_PCAK_ID = #rightViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upDownFlag">
            UP_DOWN_FLAG = #upDownFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="standFlag">
            STAND_FLAG = #standFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="lrAccupyFlag">
            LR_ACCUPY_FLAG = #lrAccupyFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jgLockFlag">
            JG_LOCK_FLAG = #jgLockFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upForbinFlag">
            UP_FORBIN_FLAG = #upForbinFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryProductId">
            FACTORY_PRODUCT_ID = #factoryProductId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productTypeId">
            PRODUCT_TYPE_ID = #productTypeId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="shopsign">
            SHOPSIGN = #shopsign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spec">
            SPEC = #spec#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="innerDiameter">
            INNER_DIAMETER = #innerDiameter#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outerDiameter">
            OUTER_DIAMETER = #outerDiameter#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putinWeight">
            PUTIN_WEIGHT = #putinWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="refWidth">
            REF_WIDTH = #refWidth#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="contractId">
            CONTRACT_ID = #contractId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationName">
            LOCATION_NAME = #locationName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="surfaceGrade">
            SURFACE_GRADE = #surfaceGrade#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="c_no">
            C_NO = #c_no#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upLeftViewPackId">
            UP_LEFT_VIEW_PACK_ID = #upLeftViewPackId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upRightViewPackId">
            UP_RIGHT_VIEW_PACK_ID = #upRightViewPackId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="idleIntervalId">
            IDLE_INTERVAL_ID = #idleIntervalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="avaliableMinLength">
            AVALIABLE_MIN_LENGTH = #avaliableMinLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="occupiedFlag">
            OCCUPIED_FLAG = #occupiedFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointStartOrign">
            X_POINT_START_ORIGN = #x_pointStartOrign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointEndOrign">
            X_POINT_END_ORIGN = #x_pointEndOrign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="pointLowerLength">
            POINT_LOWER_LENGTH = #pointLowerLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="pointUpperLength">
            POINT_UPPER_LENGTH = #pointUpperLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME LIKE '%$factoryBuildingName$%'
        </isNotEmpty>
    </sql>
    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 系统账套 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID	as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID	as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID	as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID	as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS	as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE	as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH	as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER	as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER	as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START	as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END	as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER	as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID	as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID	as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG	as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG	as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG	as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG	as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG	as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID	as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID	as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID	as "productTypeId",  <!-- 品种 -->
        SHOPSIGN	as "shopsign",  <!-- 牌号 -->
        SPEC	as "spec",  <!-- 规格 -->
        INNER_DIAMETER	as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER	as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT	as "putinWeight",  <!-- 重量 -->
        REF_WIDTH	as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID	as "contractId",  <!-- 销售合同 -->
        REMARK	as "remark",  <!-- 备注 -->
        LOCATION_NAME	as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA	as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
        C_NO	as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID	as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID	as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID	as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH	as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG	as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN	as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN	as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH	as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH	as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER	as "tenantUser",  <!-- 租户 -->
        DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
        UUID	as "uuid",  <!-- ID -->
        FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME	as "factoryBuildingName" <!-- 厂房名称 -->
        FROM meli.tlids0605 t WHERE 1=1
        and DEL_FLAG = 0
        and (USE_STATUS = "20" or USE_STATUS = "30") <!-- 预占、占用 -->
        and LOC_VIEW_Pack_ID = #packId#
        <isNotEmpty  prepend=" AND " property="factoryOrderNum">
             FACTORY_PRODUCT_ID = #factoryOrderNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        and SEG_NO = #segNo#
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0605 WHERE 1=1 and DEL_FLAG = 0
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        <include refid="condition"/>
    </select>

    <select id="countNum" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0605 WHERE 1=1 and DEL_FLAG = 0 and USE_STATUS != "10" and USE_STATUS != "99"
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>

    </select>


    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="wproviderId">
            WPROVIDER_ID = #wproviderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locViewId">
            LOC_VIEW_ID = #locViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locColumnId">
            LOC_COLUMN_ID = #locColumnId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="useStatus">
            USE_STATUS = #useStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationType">
            LOCATION_TYPE = #locationType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationLength">
            LOCATION_LENGTH = #locationLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="specUpper">
            SPEC_UPPER = #specUpper#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="specLower">
            SPEC_LOWER = #specLower#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointStart">
            X_POINT_START = #x_pointStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointEnd">
            X_POINT_END = #x_pointEnd#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="y_pointCenter">
            Y_POINT_CENTER = #y_pointCenter#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="leftViewId">
            LEFT_VIEW_ID = #leftViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rightViewId">
            RIGHT_VIEW_PCAK_ID = #rightViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upDownFlag">
            UP_DOWN_FLAG = #upDownFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="standFlag">
            STAND_FLAG = #standFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="lrAccupyFlag">
            LR_ACCUPY_FLAG = #lrAccupyFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jgLockFlag">
            JG_LOCK_FLAG = #jgLockFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upForbinFlag">
            UP_FORBIN_FLAG = #upForbinFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryProductId">
            FACTORY_PRODUCT_ID = #factoryProductId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productTypeId">
            PRODUCT_TYPE_ID = #productTypeId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="shopsign">
            SHOPSIGN = #shopsign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spec">
            SPEC = #spec#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="innerDiameter">
            INNER_DIAMETER = #innerDiameter#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outerDiameter">
            OUTER_DIAMETER = #outerDiameter#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putinWeight">
            PUTIN_WEIGHT = #putinWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="refWidth">
            REF_WIDTH = #refWidth#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="contractId">
            CONTRACT_ID = #contractId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationName">
            LOCATION_NAME = #locationName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="surfaceGrade">
            SURFACE_GRADE = #surfaceGrade#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="c_no">
            C_NO = #c_no#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upLeftViewId">
            UP_LEFT_VIEW_ID = #upLeftViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="upRightViewId">
            UP_RIGHT_VIEW_ID = #upRightViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="idleIntervalId">
            IDLE_INTERVAL_ID = #idleIntervalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="avaliableMinLength">
            AVALIABLE_MIN_LENGTH = #avaliableMinLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="occupiedFlag">
            OCCUPIED_FLAG = #occupiedFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointStartOrign">
            X_POINT_START_ORIGN = #x_pointStartOrign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="x_pointEndOrign">
            X_POINT_END_ORIGN = #x_pointEndOrign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="pointLowerLength">
            POINT_LOWER_LENGTH = #pointLowerLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="pointUpperLength">
            POINT_UPPER_LENGTH = #pointUpperLength#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME = #factoryBuildingName#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids0605 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        WPROVIDER_ID,  <!-- 仓库代码 -->
        LOCATION_ID,  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID,  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID,  <!-- 库位(跨+列) -->
        USE_STATUS,  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE,  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH,  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER,  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER,  <!-- 宽度下限 单位(mm) -->
        X_POINT_START,  <!-- X轴起始点 -->
        X_POINT_END,  <!-- X轴结束点 -->
        Y_POINT_CENTER,  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID,  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID,  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG,  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG,  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG,  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG,  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG,  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID,  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID,  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID,  <!-- 品种 -->
        SHOPSIGN,  <!-- 牌号 -->
        SPEC,  <!-- 规格 -->
        INNER_DIAMETER,  <!-- 内径 单位:mm -->
        OUTER_DIAMETER,  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT,  <!-- 重量 -->
        REF_WIDTH,  <!-- 宽度 单位:mm -->
        CUSTOMER_ID,  <!-- 客户代码 -->
        CONTRACT_ID,  <!-- 销售合同 -->
        REMARK,  <!-- 备注 -->
        LOCATION_NAME,  <!-- 库位名称 -->
        FACTORY_AREA,  <!-- 厂区 -->
        CROSS_AREA,  <!-- 跨区 -->
        SURFACE_GRADE,  <!-- 表面等级 -->
        C_NO,  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID,  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID,  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID,  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH,  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG,  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN,  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN,  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH,  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH,  <!-- 点状库位长度上限 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        FACTORY_BUILDING,  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME  <!-- 厂房名称 -->
        )
        VALUES (#segNo#, #unitCode#, #wproviderId#, #locationId#, #locViewPackId#, #locColumnId#, #useStatus#,
        #locationType#, #locationLength#, #specUpper#, #specLower#, #x_pointStart#, #x_pointEnd#, #y_pointCenter#,
        #leftViewPackId#, #rightViewPcakId#, #upDownFlag#, #standFlag#, #lrAccupyFlag#, #jgLockFlag#, #upForbinFlag#,
        #packId#, #factoryProductId#, #productTypeId#, #shopsign#, #spec#, #innerDiameter#, #outerDiameter#,
        #putinWeight#, #refWidth#, #customerId#, #contractId#, #remark#, #locationName#, #factoryArea#, #crossArea#,
        #surfaceGrade#, #c_no#, #upLeftViewPackId#, #upRightViewPackId#, #idleIntervalId#, #avaliableMinLength#,
        #occupiedFlag#, #x_pointStartOrign#, #x_pointEndOrign#, #pointLowerLength#, #pointUpperLength#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#,
        #delFlag#, #uuid#, #factoryBuilding#, #factoryBuildingName#)
    </insert>

    <delete id="delete">
        UPDATE meli.tlids0605
        SET
        STATUS = "00",   <!-- 状态(删除：00，新增：10) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = "1"   <!-- 删除标记 -->
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
    </delete>

    <select id="querySwitchValue" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CONFIGURE_KEY as "configureKey",  <!-- 配置项代码 -->
        CONFIGURE_NAME as "configureName",  <!-- 配置项名称 -->
        CONFIGURE_VALUE as "configureValue",  <!-- 配置项集值 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM ${platSchema}.XS_CONFIGURATION_INFORMATION WHERE 1=1
        and DEL_FLAG = 0
        and CONFIGURE_KEY = #key#
        and WAREHOUSE_CODE = #warehouseCode#
        and SEG_NO = #segNo#
    </select>

    <select id="queryMatching" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.wprovider_id,
        t.location_id,
        t.location_type,
        t.location_name,
        t.idle_interval_id,
        t.c_no,
        t.x_point_end,
        t.x_point_start,
        t.x_point_end - t.x_point_start as free_length,
        t.right_view_pcak_id as right_view_pack_id,
        t.left_view_pack_id,
        t.stand_flag,
        t.spec_lower as "specLower",
        t.spec_upper as "specUpper",
        t.point_lower_length as "pointLowerLength",
        t.point_upper_length as "pointUpperLength"
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        <isNotEmpty prepend=" AND " property="locationId">
             t.location_id = #locationId#
        </isNotEmpty>
        and (
        ((t.right_view_pcak_id is null or t.right_view_pcak_id = '') and t.x_point_end - t.x_point_start >= #externalDiameter#)
        or
        (t.RIGHT_VIEW_PCAK_ID != '' and t.x_point_end - t.x_point_start - #intervalDistanceMini# >= #externalDiameter#)
        )
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.up_down_flag = '1'
        and t.use_status = '10'
        and t.IDLE_INTERVAL_ID != 0
        and ((t.stand_flag = '0'
        and
        t.spec_lower &lt;= #width#
        and
        t.spec_upper >= #width#)
        or
        (t.stand_flag = '1'
        and
        t.spec_lower &lt;= #externalDiameter#
        and
        t.spec_upper >= #externalDiameter#))
    </select>

    <select id="queryAdjacent" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.OUTER_DIAMETER AS "outerDiameter",
        t.PUTIN_WEIGHT AS "putinWeight"
        FROM meli.tlids0605 t
        WHERE 1=1
        and t.DEL_FLAG = 0
        <isNotEmpty property="listQuery">
            and t.LOC_VIEW_PACK_ID in
            <iterate open="(" close=")" conjunction="," property="listQuery">
                #listQuery[]#
            </iterate>
        </isNotEmpty>
        and t.USE_STATUS in ('20','30')
        and t.LOCATION_ID = #locationId#
        and t.WPROVIDER_ID = #warehouseCode#
        and t.SEG_NO = #segNo#

    </select>


    <select id="queryLarge" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 系统账套 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        FACTORY_AREA	as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        CROSS_AREA	as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME	as "crossAreaName",  <!-- 跨区名称 -->
        SPECIFY_CROSS_COLUMN	as "specifyCrossColumn",  <!-- 指定跨所在列 -->
        STATUS	as "status",  <!-- 状态 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER	as "tenantUser",  <!-- 租户 -->
        DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
        UUID	as "uuid" <!-- ID -->
        FROM meli.tlids0603 t WHERE 1=1 and DEL_FLAG	= '0'
        and SEG_NO = #segNo#
        and WPROVIDER_ID = #warehouseCode#
    </select>
    <!--查询连续空闲区间信息-->
    <select id="queryIdleInterval" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.wprovider_id,
        t.location_id,
        t.location_type,
        t.location_name,
        t.idle_interval_id,
        t.c_no,
        t.x_point_end,
        t.x_point_start,
        t.x_point_end - t.x_point_start as free_length,
        t.right_view_pcak_id as right_view_pack_id,
        t.left_view_pack_id
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.UP_DOWN_FLAG = '1'
        and t.use_status = '10'
        <isNotEmpty prepend=" AND " property="idleIntervalId">
             t.idle_interval_id = #idleIntervalId#
        </isNotEmpty>
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </select>
    <!--删除连续空闲区间信息-->
    <update id="deleteIdleInterval">
        UPDATE meli.tlids0605 t
        SET
        DEL_FLAG = 1,
        USE_STATUS = 99
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.idle_interval_id = #idleIntervalId#
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </update>
    <!--修改连续空闲区间信息-->
    <update id="updateIdleInterval">
        UPDATE meli.tlids0605 t
        SET
        <isNotEmpty  property="leftViewPackId">
            t.left_view_pack_id = #leftViewPackId#,
        </isNotEmpty>
        <isNotEmpty  property="rightViewPackId">
           t.right_view_pcak_id = #rightViewPackId#,
        </isNotEmpty>
        t.x_point_start = #x_point_start#,
        t.x_point_end = #x_point_end#
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.idle_interval_id = #idleIntervalId#
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </update>

    <!--修改相邻捆包信息-->
    <update id="updateAdjacentPack">
        UPDATE meli.tlids0605 t
        SET
        <isNotEmpty  property="leftViewPackIdNow">
            t.left_view_pack_id = #leftViewPackIdNow#,
        </isNotEmpty>
        <isNotEmpty  property="rightViewPackIdNow">
            t.right_view_pcak_id = #rightViewPackIdNow#,
        </isNotEmpty>
        t.seg_no =  t.seg_no
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
        <isNotEmpty prepend=" AND " property="locationPackId">
            t.LOC_VIEW_PACK_ID = #locationPackId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="leftViewPackId">
            t.left_view_pack_id = #leftViewPackId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rightViewPackId">
            t.right_view_pcak_id = #rightViewPackId#
        </isNotEmpty>

    </update>


    <select id="queryFactoryArea" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 系统账套 -->
        POLLING_SCHEME_NUMBER	as "pollingSchemeNumber",  <!-- 轮询方案编号 -->
        CROSS_AREA	as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME	as "crossAreaName",  <!-- 跨区名称 -->
        FIRST_PROCESSING_PROCEDURE	as "productProcessId",
        FACTORY_AREA	as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
        POLLING_SCHEME_NUMBER	as "pollingSchemeNumber",  <!-- 轮询方案编号 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME	as "factoryBuildingName", <!-- 厂房名称 -->
        FIRST_MACHINE_CODE AS "firstMachineCode", <!--首道加工机组-->
        case when UNLOADING_TYPE  = '' or UNLOADING_TYPE= null
        then 0 else UNLOADING_TYPE end
        as "unloadingType"
        FROM meli.tlids0604 t
        WHERE 1=1
        and t.DEL_FLAG	= '0'
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WPROVIDER_ID = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productProcessId">
            FIRST_PROCESSING_PROCEDURE = #productProcessId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstMachineCode">
            FIRST_MACHINE_CODE = #firstMachineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unloadingType">
            UNLOADING_TYPE = #unloadingType#
        </isNotEmpty>
    </select>


    <select id="queryFactoryArea2" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 系统账套 -->
        POLLING_SCHEME_NUMBER	as "pollingSchemeNumber",  <!-- 轮询方案编号 -->
        CROSS_AREA	as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME	as "crossAreaName",  <!-- 跨区名称 -->
        FIRST_PROCESSING_PROCEDURE	as "productProcessId",
        FACTORY_AREA	as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
        POLLING_SCHEME_NUMBER	as "pollingSchemeNumber",  <!-- 轮询方案编号 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME	as "factoryBuildingName", <!-- 厂房名称 -->
        FIRST_MACHINE_CODE AS "firstMachineCode", <!--首道加工机组-->
        case when UNLOADING_TYPE  = '' or UNLOADING_TYPE= null
        then 0 else UNLOADING_TYPE end
        as "unloadingType"
        FROM meli.tlids0604 t
        WHERE 1=1
        and t.DEL_FLAG	= '0'
        AND (t.UNLOADING_TYPE != '2' or t.UNLOADING_TYPE is null)
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WPROVIDER_ID = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="productProcessId">
            FIRST_PROCESSING_PROCEDURE = #productProcessId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstMachineCode">
            FIRST_MACHINE_CODE = #firstMachineCode#
        </isNotEmpty>


    </select>


    <!--查询连续空闲区间信息-->
    <select id="queryMatchingUp" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.seg_no as "segNo",
        t.wprovider_id,
        t.location_id,
        t.location_type,
        t.location_name,
        t.idle_interval_id,
        t.c_no,
        t.stand_flag,
        t.x_point_end,
        t.x_point_start,
        t.x_point_end - t.x_point_start as free_length,
        t. UP_RIGHT_VIEW_PACK_ID as right_view_pack_id,
        t.FACTORY_AREA as "factoryArea",
        t.FACTORY_BUILDING as "factoryBuilding",
        t.CROSS_AREA  as "crossArea",
        t.spec_lower as "specLower",
        t.spec_upper as "specUpper",
        t.point_lower_length as "pointLowerLength",
        t.point_upper_length as "pointUpperLength",
        IFNULL((select tl.X_POINT_START + tl.OUTER_DIAMETER from meli.tlids0605 tl where 1=1 and tl.SEG_NO =t.SEG_NO and tl.LOC_VIEW_PACK_ID  = t.UP_LEFT_VIEW_PACK_ID and tl.LOCATION_ID = t.LOCATION_ID and DEL_FLAG = 0 limit 1),0) as left_x_point_end ,
        IFNULL((select tl.X_POINT_START  from meli.tlids0605 tl where 1=1 and tl.SEG_NO =t.SEG_NO and tl.LOC_VIEW_PACK_ID  = t.UP_RIGHT_VIEW_PACK_ID and tl.LOCATION_ID = t.LOCATION_ID and DEL_FLAG = 0 limit 1),0) as right_x_point_start ,
        t.UP_LEFT_VIEW_PACK_ID as left_view_pack_id
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.USE_STATUS = '10'
        and t.UP_DOWN_FLAG = '2'
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            t.FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            t.cross_area = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            t.factory_area = #factoryArea#
        </isNotEmpty>

        and (select t1.PUTIN_WEIGHT
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.UP_LEFT_VIEW_PACK_ID
        and DEL_FLAG = 0 limit 1)  -#netWeight# &lt;= #weightDeviation#

        and 0 &lt;= ((select t1.OUTER_DIAMETER
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.UP_LEFT_VIEW_PACK_ID
        and DEL_FLAG = 0 limit 1) - #externalDiameter#) &lt;= #outerDiameterDeviation#


        and (select t1.PUTIN_WEIGHT
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.UP_RIGHT_VIEW_PACK_ID
        and DEL_FLAG = 0 limit 1)  -#netWeight# &lt;= #weightDeviation#

        and 0 &lt;= ((select t1.OUTER_DIAMETER
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.UP_RIGHT_VIEW_PACK_ID
        and DEL_FLAG = 0 limit 1) - #externalDiameter# ) &lt;= #outerDiameterDeviation#
    </select>


    <!--查询连续空闲区间信息-->
    <select id="queryMatchingDownTwo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.seg_no as "segNo",
        t.wprovider_id,
        t.location_id,
        t.location_type,
        t.location_name,
        t.idle_interval_id,
        t.c_no,
        t.x_point_end,
        t.x_point_start,
        t.x_point_end - t.x_point_start as free_length,
        t.right_view_pcak_id as right_view_pack_id,
        t.left_view_pack_id,
        t.stand_flag,
        t.spec_lower as "specLower",
        t.spec_upper as "specUpper",
        t.point_lower_length as "pointLowerLength",
        t.point_upper_length as "pointUpperLength",
        t.FACTORY_AREA as "factoryArea",
        t.FACTORY_BUILDING as "factoryBuilding",
        t.CROSS_AREA  as "crossArea"

        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.USE_STATUS = '10'
        and t.UP_DOWN_FLAG = '1'
        and t.UP_LEFT_VIEW_PACK_ID != ''
        and t.UP_RIGHT_VIEW_PACK_ID != ''
        and t.x_point_end - t.x_point_start - #intervalDistanceMini# >= #externalDiameter#
        <isNotEmpty prepend=" AND " property="locationId">
            t.location_id = #locationId#
        </isNotEmpty>
        and (
        ((t.right_view_pcak_id is null or t.right_view_pcak_id= '') and t.x_point_end - t.x_point_start >= #externalDiameter#)
        or
        (t.RIGHT_VIEW_PCAK_ID != '' and t.x_point_end - t.x_point_start - #intervalDistanceMini# >= #externalDiameter#)
        )
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.up_down_flag = '1'
        and t.use_status = '10'
        and t.IDLE_INTERVAL_ID != 0
        and ((t.stand_flag = '0'
        and
        t.spec_lower &lt;= #width#
        and
        t.spec_upper >= #width#)
        or
        (t.stand_flag = '1'
        and
        t.spec_lower &lt;= #externalDiameter#
        and
        t.spec_upper >= #externalDiameter#))


        and (select t1.PUTIN_WEIGHT
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.left_view_pack_id
        and DEL_FLAG = 0 limit 1)  -#netWeight# &lt;= #weightDeviation#

        and (select t1.OUTER_DIAMETER
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.left_view_pack_id
        and DEL_FLAG = 0 limit 1) - #externalDiameter# &lt;= #outerDiameterDeviation#


        and (select t1.PUTIN_WEIGHT
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.right_view_pcak_id
        and DEL_FLAG = 0 limit 1)  -#netWeight# &lt;= #weightDeviation#

        and (select t1.OUTER_DIAMETER
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.right_view_pcak_id
        and DEL_FLAG = 0 limit 1) - #externalDiameter# &lt;= #outerDiameterDeviation#
        <!--宽度偏差-->
        and #widthDeviation# &lt;= (select
        cast(SUBSTRING(
        SPEC ,
        INSTR( SPEC, '*') + 1,
        INSTR(SUBSTRING( SPEC, INSTR( SPEC, '*') + 1), '*') - 1
        )as DECIMAL(10, 0))as "spec"
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.left_view_pack_id
        and DEL_FLAG = 0 limit 1) - #width# &lt;= #widthDeviation#

        and #widthDeviation# &lt;= (select
        cast(SUBSTRING(
        SPEC ,
        INSTR( SPEC, '*') + 1,
        INSTR(SUBSTRING( SPEC, INSTR( SPEC, '*') + 1), '*') - 1
        )as DECIMAL(10, 0))as "spec"
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.right_view_pcak_id
        and DEL_FLAG = 0 limit 1) - #width# &lt;= #widthDeviation#

    </select>

    <!--查询连续空闲区间信息-->
    <select id="queryMatchingDownOne" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.seg_no as "segNo",
        t.wprovider_id,
        t.location_id,
        t.location_type,
        t.location_name,
        t.idle_interval_id,
        t.c_no,
        t.x_point_end,
        t.x_point_start,
        t.spec_lower as "specLower",
        t.spec_upper as "specUpper",
        t.point_lower_length as "pointLowerLength",
        t.point_upper_length as "pointUpperLength",
        t.x_point_end - t.x_point_start as free_length,
        t.right_view_pcak_id as right_view_pack_id,
        t.FACTORY_AREA as "factoryArea",
        t.FACTORY_BUILDING as "factoryBuilding",
        t.CROSS_AREA  as "crossArea",
        t.left_view_pack_id
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS = '10'
        and t.UP_DOWN_FLAG = '1'
        and t.location_type = '20'
        and (t.LEFT_VIEW_PACK_ID != '' or t.RIGHT_VIEW_PCAK_ID != '')
       <!-- and t.idle_interval_id = #idleIntervalId#-->
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            t.FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            t.cross_area = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            t.factory_area = #factoryArea#
        </isNotEmpty>
        and (((t.right_view_pcak_id is null or t.right_view_pcak_id= '') and t.x_point_end - t.x_point_start >= #externalDiameter#)
        or (t.right_view_pcak_id != '' and t.x_point_start - t.x_point_end - #intervalDistanceMini# >= #externalDiameter#))

        and (
        ((t.right_view_pcak_id is null or t.right_view_pcak_id= '')
        and (select t1.PUTIN_WEIGHT
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.left_view_pack_id
        and DEL_FLAG = 0 limit 1)  -#netWeight# &lt;= #weightDeviation#

        and (select t1.OUTER_DIAMETER
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.left_view_pack_id
        and DEL_FLAG = 0 limit 1) - #externalDiameter# &lt;= #outerDiameterDeviation#

        )
        or
        (
        t.right_view_pcak_id != ''
        and (select t1.PUTIN_WEIGHT
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.right_view_pcak_id
        and DEL_FLAG = 0 limit 1)  -#netWeight# &lt;= #weightDeviation#

        and (select t1.OUTER_DIAMETER
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.right_view_pcak_id
        and DEL_FLAG = 0 limit 1) - #externalDiameter# &lt;= #outerDiameterDeviation#
        )
        )

        <!--宽度偏差-->
        and #widthDeviation# &lt;= (select
        cast(SUBSTRING(
        SPEC ,
        INSTR( SPEC, '*') + 1,
        INSTR(SUBSTRING( SPEC, INSTR( SPEC, '*') + 1), '*') - 1
        )as DECIMAL(10, 0))as "spec"
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.left_view_pack_id
        and DEL_FLAG = 0 limit 1) - #width# &lt;= #widthDeviation#

        and #widthDeviation# &lt;= (select
        cast(SUBSTRING(
        SPEC ,
        INSTR( SPEC, '*') + 1,
        INSTR(SUBSTRING( SPEC, INSTR( SPEC, '*') + 1), '*') - 1
        )as DECIMAL(10, 0))as "spec"
        from meli.tlids0605 t1
        where t1.SEG_NO = t.SEG_NO
        and t1.LOC_VIEW_PACK_ID= t.right_view_pcak_id
        and DEL_FLAG = 0 limit 1) - #width# &lt;= #widthDeviation#


    </select>


    <!--查询连续空闲区间信息-->
    <select id="queryMatchingDown" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.seg_no as "segNo",
        t.wprovider_id,
        t.location_id,
        t.location_type,
        t.location_name,
        t.idle_interval_id,
        t.c_no,
        t.x_point_end,
        t.x_point_start,
        t.x_point_end - t.x_point_start as free_length,
        t.right_view_pcak_id as right_view_pack_id,
        t.left_view_pack_id,
        t.stand_flag,
        t.spec_lower as "specLower",
        t.spec_upper as "specUpper",
        t.point_lower_length as "pointLowerLength",
        t.point_upper_length as "pointUpperLength",
        t.FACTORY_AREA as "factoryArea",
        t.FACTORY_BUILDING as "factoryBuilding",
        t.CROSS_AREA  as "crossArea"
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.LEFT_VIEW_PACK_ID = ''
        and t.RIGHT_VIEW_PCAK_ID = ''

        <isNotEmpty prepend=" AND " property="locationId">
            t.location_id = #locationId#
        </isNotEmpty>
        and (
        ((t.right_view_pcak_id is null or t.right_view_pcak_id = '') and (t.x_point_end - t.x_point_start) * 10 >= #externalDiameter#)
        or
        (t.RIGHT_VIEW_PCAK_ID != '' and (t.x_point_end - t.x_point_start) * 10 - #intervalDistanceMini# >= #externalDiameter#)
        )
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            t.FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            t.cross_area = #crossArea#
        </isNotEmpty>
        and t.up_down_flag = '1'
        and t.use_status = '10'
        and t.IDLE_INTERVAL_ID != 0
        and ((t.stand_flag = '0'
        and
        t.spec_lower &lt;= #width#
        and
        t.spec_upper >= #width#)
        or
        (t.stand_flag = '1'
        and
        t.spec_lower &lt;= #externalDiameter#
        and
        t.spec_upper >= #externalDiameter#))

    </select>

    <insert id="insertUpperIdle">
        INSERT INTO meli.tlids0605 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        WPROVIDER_ID,  <!-- 仓库代码 -->
        LOCATION_ID,  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID,  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID,  <!-- 库位(跨+列) -->
        USE_STATUS,  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE,  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH,  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER,  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER,  <!-- 宽度下限 单位(mm) -->
        X_POINT_START,  <!-- X轴起始点 -->
        X_POINT_END,  <!-- X轴结束点 -->
        Y_POINT_CENTER,  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID,  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID,  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG,  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG,  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG,  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG,  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG,  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID,  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID,  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID,  <!-- 品种 -->
        SHOPSIGN,  <!-- 牌号 -->
        SPEC,  <!-- 规格 -->
        INNER_DIAMETER,  <!-- 内径 单位:mm -->
        OUTER_DIAMETER,  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT,  <!-- 重量 -->
        REF_WIDTH,  <!-- 宽度 单位:mm -->
        CUSTOMER_ID,  <!-- 客户代码 -->
        CONTRACT_ID,  <!-- 销售合同 -->
        REMARK,  <!-- 备注 -->
        LOCATION_NAME,  <!-- 库位名称 -->
        FACTORY_AREA,  <!-- 厂区 -->
        CROSS_AREA,  <!-- 跨区 -->
        SURFACE_GRADE,  <!-- 表面等级 -->
        C_NO,  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID,  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID,  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID,  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH,  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG,  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN,  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN,  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH,  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH,  <!-- 点状库位长度上限 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        FACTORY_BUILDING,  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME  <!-- 厂房名称 -->
        )
        VALUES (#segNo#, #unitCode#, #wproviderId#, #locationId#, #locViewPackId#, #locColumnId#, #useStatus#,
        #locationType#, #locationLength#, #specUpper#, #specLower#, #x_pointStart#, #x_pointEnd#, #y_pointCenter#,
        #leftViewPackId#, #rightViewPackId#, #upDownFlag#, #standFlag#, #lrAccupyFlag#, #jgLockFlag#, #upForbinFlag#,
        #packId#, #factoryProductId#, #productTypeId#, #shopsign#, #spec#, #innerDiameter#, #outerDiameter#,
        #putinWeight#, #refWidth#, #customerId#, #contractId#, #remark#, #locationName#, #factoryArea#, #crossArea#,
        #surfaceGrade#, #c_no#, #upLeftViewPackId#, #upRightViewPackId#, #idleIntervalId#, #avaliableMinLength#,
        #occupiedFlag#, #x_pointStartOrign#, #x_pointEndOrign#, #pointLowerLength#, #pointUpperLength#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#,
        #delFlag#, #uuid#, #factoryBuilding#, #factoryBuildingName#)
    </insert>


    <!--更改捆包信息到上层库位-->
    <update id="updatePackInfo">
        UPDATE meli.tlids0605 t
        SET
        LOC_VIEW_PACK_ID = #locViewPackId#,   <!-- 捆包号 -->
        FACTORY_PRODUCT_ID = #factoryProductId#,   <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID = #productTypeId#,   <!-- 品种 -->
        SHOPSIGN = #shopsign#,   <!-- 牌号 -->
        SPEC = #spec#,   <!-- 规格 -->
        INNER_DIAMETER = #innerDiameter#,   <!-- 内径 单位:mm -->
        OUTER_DIAMETER = #outerDiameter#,   <!-- 外径 单位:mm -->
        PUTIN_WEIGHT = #putinWeight#,   <!-- 重量 -->
        REF_WIDTH = #refWidth#,   <!-- 宽度 单位:mm -->
        CUSTOMER_ID = #customerId#,   <!-- 客户代码 -->

        USE_STATUS = #useStatus#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #wproviderId#
        and t.location_type = '20'
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
        and t.UP_LEFT_VIEW_PACK_ID = #upLeftViewPackId#
        and t.UP_RIGHT_VIEW_PACK_ID = #upRightViewPackId#
        and t.location_id = #locationId#
    </update>


    <!--查询当前捆包信息-->
    <select id="queryPackInfo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.LOC_VIEW_PACK_ID = #packId#
        and t.USE_STATUS IN ('20', '30')
        AND t.DEL_FLAG = 0
        <!--and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#-->
    </select>

    <!--查询当前捆包上层是否有捆包占用-->
    <select id="queryPackInfoUp" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS IN ('20', '30')
        AND t.DEL_FLAG = 0

        and (t.up_left_view_pack_id = #packId# or t.up_right_view_pack_id = #packId#)
    </select>

    <!--查询当前空包左侧空闲信息-->
    <select id="queryIdleIntervalLeft" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.RIGHT_VIEW_PCAK_ID  = #packId#
        and t.USE_STATUS in ('10')
        AND t.DEL_FLAG = 0
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </select>

    <!--查询当前空包右侧空闲信息-->
    <select id="queryIdleIntervalRight" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.LEFT_VIEW_PACK_ID  = #packId#
        and t.USE_STATUS in ('10')
        AND t.DEL_FLAG = 0
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </select>

    <!--删除当前占用信息-->
    <update id="deleteLocation">
        UPDATE meli.tlids0605 t
        SET
        DEL_FLAG = 1,
        USE_STATUS = 99
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #warehouseCode#
        and t.loc_view_pack_id = #packId#
        and t.use_status IN ('20', '30')
    </update>

    <!--删除当前占用信息-->
    <update id="deleteLocationUp">
        UPDATE meli.tlids0605 t
        SET
        DEL_FLAG = 1,
        USE_STATUS = 99
        WHERE
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS IN ('10')
        AND t.DEL_FLAG = 0
        and (t.up_left_view_pack_id = #packId# or t.up_right_view_pack_id = #packId#)
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </update>


    <!--删除当前捆包的左侧捆包信息-->
    <update id="deletePackLeft">
        UPDATE meli.tlids0605 t
        SET
        t.right_view_pcak_id = null
        WHERE
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS IN ('20', '30')
        AND t.DEL_FLAG = 0
        and t.right_view_pcak_id = #packId#
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </update>

    <!--删除当前捆包的右侧捆包信息-->
    <update id="deletePackRight">
        UPDATE meli.tlids0605 t
        SET
        t.left_view_pack_id = null
        WHERE
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS IN ('20', '30')
        AND t.DEL_FLAG = 0
        and t.left_view_pack_id = #packId#
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
    </update>


    <!--更新左侧空闲区间的X轴结束点-->
    <update id="updateIdleIntervalLeft">
        UPDATE meli.tlids0605 t
        SET
        t.x_point_end = #x_pointEnd#,
        t.RIGHT_VIEW_PCAK_ID = #rightViewPackId#
        WHERE
        t.seg_no =  #segNo#
        and t.wprovider_id = #wproviderId#
        and t.USE_STATUS IN ('10')
        AND t.DEL_FLAG = 0
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
        and t.IDLE_INTERVAL_ID = #idleIntervalId#

    </update>

    <!--更新右侧空闲区间的X轴起始点-->
    <update id="updateIdleIntervalRight">
        UPDATE meli.tlids0605 t
        SET
        t.x_point_start = #xPointStart#,
        <isNotEmpty  property="leftViewPackIdNow">
            t.left_view_pack_id = #leftViewPackIdNow#,
        </isNotEmpty>
        t.seg_no =  #segNo#
        WHERE
        t.seg_no =  #segNo#
        and t.wprovider_id = #wproviderId#
        and t.USE_STATUS IN ('10')
        AND t.DEL_FLAG = 0
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
        and t.IDLE_INTERVAL_ID = #idleIntervalId#
    </update>

    <!--更新右侧空闲区间的X轴起始点-->
    <update id="deleteIdleIntervalRight">
        UPDATE meli.tlids0605 t
        SET
        t.use_status = 99,
        t.del_flag = 1
        WHERE
        t.seg_no =  #segNo#
        and t.wprovider_id = #wproviderId#
        and t.USE_STATUS IN ('10')
        AND t.DEL_FLAG = 0
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
        and t.IDLE_INTERVAL_ID = #idleIntervalId#
    </update>


    <!--查询当前空包左侧空闲信息-->
    <select id="queryTlirl0503" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        PROD_DENSITY as "prodDensity",  <!-- 产品密度 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        SPECS_DESC as  "specsDesc",  <!-- 规格描述 -->
        FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 工厂订单号 -->
        CUSTOMER_ID as "customerId" , <!-- 客户代码 -->
        PRODUCT_PROCESS_ID as "productProcessId",  <!-- 首道加工工序 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        INNER_DIAMETER as "innerDiameter"  <!-- 内径 -->
        from
        meli.tlirl0503 t
        where
        t.seg_no =  #segNo#
        and t.PACK_ID = #packId#
        and t.DEL_FLAG = 0
    </select>


    <!--查询中间坐标是否存在占用信息-->
    <select id="locationMapListOccupy" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPackId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.LOCATION_ID = #locationId#
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS in ('10','20','30')
        and t.USE_STATUS != '99'
        AND t.DEL_FLAG = 0
        and t.UP_DOWN_FLAG = '1'
        AND t.X_POINT_START &lt;= #xPoint#
        AND t.X_POINT_END >= #xPoint#
    </select>

    <!--查询中间坐标是否存在占用信息-->
    <select id="locationMapListUp" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPackId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.PACK_ID = #packId#
        and t.USE_STATUS in ('10','20')
        AND t.DEL_FLAG = 0
        and t.UP_DOWN_FLAG = '2'
        <isNotEmpty prepend=" AND " property="rightViewId">
            RIGHT_VIEW_PCAK_ID = #rightViewId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="leftViewId">
            LEFT_VIEW_PACK_ID = #leftViewId#
        </isNotEmpty>
    </select>
    <!--上层库位信息占用更新-->
    <update id="updateupperLayer">
        UPDATE meli.tlids0605 t
        SET
        LOC_VIEW_PACK_ID = #locViewPackId#,   <!-- 捆包号 -->
        FACTORY_PRODUCT_ID = #factoryProductId#,   <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID = #productTypeId#,   <!-- 品种 -->
        SHOPSIGN = #shopsign#,   <!-- 牌号 -->
        SPEC = #spec#,   <!-- 规格 -->
        INNER_DIAMETER = #innerDiameter#,   <!-- 内径 单位:mm -->
        OUTER_DIAMETER = #outerDiameter#,   <!-- 外径 单位:mm -->
        PUTIN_WEIGHT = #putinWeight#,   <!-- 重量 -->
        REF_WIDTH = #refWidth#,   <!-- 宽度 单位:mm -->
        CUSTOMER_ID = #customerId#,   <!-- 客户代码 -->
        USE_STATUS = #useStatus#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->

        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #wproviderId#
        and t.location_type = '20'
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.factory_area = #factoryArea#
        and t.UP_LEFT_VIEW_PACK_ID = #upLeftViewPackId#
        and t.UP_RIGHT_VIEW_PACK_ID = #upRightViewPackId#
        and t.location_id = #locationId#
    </update>

    <!--查询空闲区间详细信息-->
    <select id="queryPackInfoAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        <isNotEmpty prepend=" AND " property="idleIntervalId">
            t.idle_interval_id = #idleIntervalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            t.location_id = #locationId#
        </isNotEmpty>
        and t.USE_STATUS IN ('10')
        AND t.DEL_FLAG = 0
    </select>


    <!--修改捆包占用状态-->
    <update id="updatePackStatus">
        UPDATE meli.tlids0605 t
        SET
        USE_STATUS = 30
        WHERE
        t.DEL_FLAG = 0
        <isNotEmpty prepend=" AND " property="segNo">
            t.seg_no = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            t.wprovider_id = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            t.FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            t.cross_area = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            t.factory_area = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            t.LOC_VIEW_PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            t.LOCATION_ID = #locationId#
        </isNotEmpty>
    </update>


    <!--查询中间坐标是否存在占用信息-->
    <select id="locationMapListOccupy1" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID as "rightViewPackId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID as "productTypeId",  <!-- 品种 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC as "spec",  <!-- 规格 -->
        INNER_DIAMETER as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT as "putinWeight",  <!-- 重量 -->
        REF_WIDTH as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID as "contractId",  <!-- 销售合同 -->
        REMARK as "remark",  <!-- 备注 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
        C_NO as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName" <!-- 厂房名称 -->
        from
        meli.tlids0605 t
        where
        t.seg_no =  #segNo#
        and t.LOCATION_ID = #locationId#
        and t.wprovider_id = #warehouseCode#
        and t.USE_STATUS in ('10','20','30')
        and t.USE_STATUS != '99'
        AND t.DEL_FLAG = 0
        and t.UP_DOWN_FLAG = '1'
        AND t.X_POINT_START >= #xPointEndDouble#
        AND t.X_POINT_END &lt;= #xPointStartDouble#
    </select>

    <select id="queryConfigure" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CONFIGURE_KEY as "configureKey",  <!-- 配置项代码 -->
        CONFIGURE_NAME as "configureName",  <!-- 配置项名称 -->
        CONFIGURE_VALUE as "configureValue",  <!-- 配置项集值 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM ${platSchema}.XS_CONFIGURATION_INFORMATION WHERE 1=1
        and DEL_FLAG = 0
        and CONFIGURE_KEY = #key#
        and STATUS = '10'
    </select>

    <!--查询当前库位总长度-->
    <select id="queryMatchingUpLength" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        sum(X_DESTINATION -t.X_INITIAL_POINT) as "total_length"
        FROM
        meli.tlids0601 t
        where
        t.seg_no =  #segNo#
        and t.LOCATION_ID = #locationId#
        AND t.DEL_FLAG = 0
    </select>

    <!--查询轮询方案-->
    <select id="queryMatchingUpOccupiedLength" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        sum(X_POINT_END-X_POINT_START) as "occupied_length"
        FROM meli.tlids0605
        where
        1=1
        and seg_no =  #segNo#
        and location_id = #locationId#
        AND DEL_FLAG = 0
        and factory_building = #factoryBuilding#
        and cross_area = #crossArea#
        and factory_area = #factoryArea#
        and use_status in ('20','30')
        group by location_id
    </select>

    <!--查询轮询方案-->
    <select id="queryPollingScheme" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        POLLING_FACTORY_BUILDING as "factoryBuilding",
        POLLING_FACTORY_AREA  as "factoryArea",
        POLLING_ACROSS_REGIONS as "crossArea"
        from
        meli.tlids0602 t
        where
        1 = 1
        and DEL_FLAG = 0
        and POLLING_ACROSS_REGIONS != ''
        and SEG_NO =  #segNo#
        and POLLING_SCHEME_NUMBER = #pollingSchemeNumber#
        order by t.POLL_ORDER asc
    </select>


    <!--查询轮询方案-->
    <select id="queryNonUpperLocation" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        t.SEG_NO AS segNo,
        t.UNIT_CODE AS unitCode,
        t.WPROVIDER_ID AS wproviderId,
        t.LOCATION_ID AS locationId,
        t.LOC_VIEW_PACK_ID AS locViewPackId,
        t.LOC_COLUMN_ID AS locColumnId,
        t.USE_STATUS AS useStatus,
        t.LOCATION_TYPE AS locationType,
        t.LOCATION_LENGTH AS locationLength,
        t.SPEC_UPPER AS specUpper,
        t.SPEC_LOWER AS specLower,
        t.X_POINT_START AS xPointStart,
        t.X_POINT_END AS xPointEnd,
        t.Y_POINT_CENTER AS yPointCenter,
        t.LEFT_VIEW_PACK_ID AS leftViewPackId,
        t.RIGHT_VIEW_PCAK_ID AS rightViewPcakId,
        t.UP_DOWN_FLAG AS upDownFlag,
        t.STAND_FLAG AS standFlag,
        t.LR_ACCUPY_FLAG AS lrAccupyFlag,
        t.JG_LOCK_FLAG AS jgLockFlag,
        t.UP_FORBIN_FLAG AS upForbinFlag,
        t.PACK_ID AS packId,
        t.FACTORY_PRODUCT_ID AS factoryProductId,
        t.PRODUCT_TYPE_ID AS productTypeId,
        t.SHOPSIGN AS shopsign,
        t.SPEC AS spec,
        t.INNER_DIAMETER AS innerDiameter,
        t.OUTER_DIAMETER AS outerDiameter,
        t.PUTIN_WEIGHT AS putinWeight,
        t.REF_WIDTH AS refWidth,
        t.CUSTOMER_ID AS customerId,
        t.CONTRACT_ID AS contractId,
        t.REMARK AS remark,
        t.LOCATION_NAME AS locationName,
        t.FACTORY_AREA AS factoryArea,
        t.CROSS_AREA AS crossArea,
        t.SURFACE_GRADE AS surfaceGrade,
        t.C_NO AS cNo,
        t.UP_LEFT_VIEW_PACK_ID AS upLeftViewPackId,
        t.UP_RIGHT_VIEW_PACK_ID AS upRightViewPackId,
        t.IDLE_INTERVAL_ID AS idleIntervalId,
        t.AVALIABLE_MIN_LENGTH AS avaliableMinLength,
        t.OCCUPIED_FLAG AS occupiedFlag,
        t.X_POINT_START_ORIGN AS xPointStartOrign,
        t.X_POINT_END_ORIGN AS xPointEndOrign,
        t.POINT_LOWER_LENGTH AS pointLowerLength,
        t.POINT_UPPER_LENGTH AS pointUpperLength,
        t.REC_CREATOR AS recCreator,
        t.REC_CREATOR_NAME AS recCreatorName,
        t.REC_CREATE_TIME AS recCreateTime,
        t.REC_REVISOR AS recRevisor,
        t.REC_REVISOR_NAME AS recRevisorName,
        t.REC_REVISE_TIME AS recReviseTime,
        t.ARCHIVE_FLAG AS archiveFlag,
        t.TENANT_USER AS tenantUser,
        t.DEL_FLAG AS delFlag,
        t.UUID AS uuid,
        t.FACTORY_BUILDING as factoryBuilding,
        t.FACTORY_BUILDING_NAME as factoryBuildingName
        from
        meli.tlids0605 t

        where
        t.seg_no =  #segNo#
        and t.wprovider_id = #warehouseCode#
        and t.location_type = '20'
        and t.USE_STATUS = '10'
        and t.UP_DOWN_FLAG = '1'
        and t.FACTORY_BUILDING = #factoryBuilding#
        and t.cross_area = #crossArea#
        and t.use_status = '10'
        and t.spec_lower &lt;= #width#
        and t.spec_upper >= #width#

        and t.X_POINT_END_ORIGN - t.X_POINT_START_ORIGN - (
        select
        IFNULL(sum(OUTER_DIAMETER + 5),0)
        from
        meli.tlids0605 s
        where
        s.seg_no =  #segNo#
        and s.wprovider_id = #warehouseCode#
        and s.location_type = '20'
        and s.USE_STATUS = '10'
        and s.UP_DOWN_FLAG = '1'
        and s.cross_area = #crossArea#
        and s.use_status in ('20','30')
        )>= #externalDiameter#

        <isNotEmpty prepend=" AND " property="storageMark">
            (t.X_POINT_END_ORIGN - t.X_POINT_START_ORIGN - (
            select
            IFNULL(sum(OUTER_DIAMETER + 5),0)
            from
            meli.tlids0605 s
            where
            s.seg_no =  #segNo#
            and s.wprovider_id = #warehouseCode#
            and s.location_type = '20'
            and s.USE_STATUS = '10'
            and s.UP_DOWN_FLAG = '1'
            and s.cross_area = #crossArea#
            and s.use_status in ('20','30')
            )) / (t.X_POINT_END_ORIGN - t.X_POINT_START_ORIGN)>0.8
        </isNotEmpty>
    </select>

    <!--删除连续空闲区间信息-->
    <update id="deleteIdleIntervalTwo">
        UPDATE meli.tlids0605 t
        SET
        DEL_FLAG = 1,
        USE_STATUS = 99,
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 0
        and t.wprovider_id = #wproviderId#
        and t.LOC_VIEW_PACK_ID = #locViewPackId#
        and t.location_id = #locationId#
        and t.FACTORY_PRODUCT_ID = #factoryProductId#
    </update>

    <!--撤销-->
    <update id="deleteIdleIntervalBlack">
        UPDATE meli.tlids0605 t
        SET
        DEL_FLAG = 0,
        USE_STATUS = 30,
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        t.seg_no =  #segNo#
        and t.DEL_FLAG = 1
        and t.USE_STATUS = 99
        and t.wprovider_id = #wproviderId#
        and t.LOC_VIEW_PACK_ID = #locViewPackId#
        and t.location_id = #locationId#
        and t.FACTORY_PRODUCT_ID = #factoryProductId#
    </update>

    <!--查询轮询方案-->
    <select id="queryBlack" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        t.SEG_NO AS segNo,
        t.UNIT_CODE AS unitCode,
        t.WPROVIDER_ID AS wproviderId,
        t.LOCATION_ID AS locationId,
        t.LOC_VIEW_PACK_ID AS locViewPackId,
        t.LOC_COLUMN_ID AS locColumnId,
        t.USE_STATUS AS useStatus,
        t.LOCATION_TYPE AS locationType,
        t.LOCATION_LENGTH AS locationLength,
        t.SPEC_UPPER AS specUpper,
        t.SPEC_LOWER AS specLower,
        t.X_POINT_START AS xPointStart,
        t.X_POINT_END AS xPointEnd,
        t.Y_POINT_CENTER AS yPointCenter,
        t.LEFT_VIEW_PACK_ID AS leftViewPackId,
        t.RIGHT_VIEW_PCAK_ID AS rightViewPcakId,
        t.UP_DOWN_FLAG AS upDownFlag,
        t.STAND_FLAG AS standFlag,
        t.LR_ACCUPY_FLAG AS lrAccupyFlag,
        t.JG_LOCK_FLAG AS jgLockFlag,
        t.UP_FORBIN_FLAG AS upForbinFlag,
        t.PACK_ID AS packId,
        t.FACTORY_PRODUCT_ID AS factoryProductId,
        t.PRODUCT_TYPE_ID AS productTypeId,
        t.SHOPSIGN AS shopsign,
        t.SPEC AS spec,
        t.INNER_DIAMETER AS innerDiameter,
        t.OUTER_DIAMETER AS outerDiameter,
        t.PUTIN_WEIGHT AS putinWeight,
        t.REF_WIDTH AS refWidth,
        t.CUSTOMER_ID AS customerId,
        t.CONTRACT_ID AS contractId,
        t.REMARK AS remark,
        t.LOCATION_NAME AS locationName,
        t.FACTORY_AREA AS factoryArea,
        t.CROSS_AREA AS crossArea,
        t.SURFACE_GRADE AS surfaceGrade,
        t.C_NO AS cNo,
        t.UP_LEFT_VIEW_PACK_ID AS upLeftViewPackId,
        t.UP_RIGHT_VIEW_PACK_ID AS upRightViewPackId,
        t.IDLE_INTERVAL_ID AS idleIntervalId,
        t.AVALIABLE_MIN_LENGTH AS avaliableMinLength,
        t.OCCUPIED_FLAG AS occupiedFlag,
        t.X_POINT_START_ORIGN AS xPointStartOrign,
        t.X_POINT_END_ORIGN AS xPointEndOrign,
        t.POINT_LOWER_LENGTH AS pointLowerLength,
        t.POINT_UPPER_LENGTH AS pointUpperLength,
        t.REC_CREATOR AS recCreator,
        t.REC_CREATOR_NAME AS recCreatorName,
        t.REC_CREATE_TIME AS recCreateTime,
        t.REC_REVISOR AS recRevisor,
        t.REC_REVISOR_NAME AS recRevisorName,
        t.REC_REVISE_TIME AS recReviseTime,
        t.ARCHIVE_FLAG AS archiveFlag,
        t.TENANT_USER AS tenantUser,
        t.DEL_FLAG AS delFlag,
        t.UUID AS uuid,
        t.FACTORY_BUILDING as factoryBuilding,
        t.FACTORY_BUILDING_NAME as factoryBuildingName
        FROM meli.tlids0605 t
        where
        1=1
        and t.seg_no =  #segNo#
        and t.location_id = #locationId#
        AND t.DEL_FLAG = 0
        and t.use_status in ('20','30')
        and t.LOC_VIEW_PACK_ID = #locViewPackId#
        AND t.FACTORY_PRODUCT_ID = #factoryProductId#
    </select>


    <select id="queryBlackAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        t.SEG_NO AS segNo,
        t.UNIT_CODE AS unitCode,
        t.WPROVIDER_ID AS wproviderId,
        t.LOCATION_ID AS locationId,
        t.LOC_VIEW_PACK_ID AS locViewPackId,
        t.LOC_COLUMN_ID AS locColumnId,
        t.USE_STATUS AS useStatus,
        t.LOCATION_TYPE AS locationType,
        t.LOCATION_LENGTH AS locationLength,
        t.SPEC_UPPER AS specUpper,
        t.SPEC_LOWER AS specLower,
        t.X_POINT_START AS xPointStart,
        t.X_POINT_END AS xPointEnd,
        t.Y_POINT_CENTER AS yPointCenter,
        t.LEFT_VIEW_PACK_ID AS leftViewPackId,
        t.RIGHT_VIEW_PCAK_ID AS rightViewPcakId,
        t.UP_DOWN_FLAG AS upDownFlag,
        t.STAND_FLAG AS standFlag,
        t.LR_ACCUPY_FLAG AS lrAccupyFlag,
        t.JG_LOCK_FLAG AS jgLockFlag,
        t.UP_FORBIN_FLAG AS upForbinFlag,
        t.PACK_ID AS packId,
        t.FACTORY_PRODUCT_ID AS factoryProductId,
        t.PRODUCT_TYPE_ID AS productTypeId,
        t.SHOPSIGN AS shopsign,
        t.SPEC AS spec,
        t.INNER_DIAMETER AS innerDiameter,
        t.OUTER_DIAMETER AS outerDiameter,
        t.PUTIN_WEIGHT AS putinWeight,
        t.REF_WIDTH AS refWidth,
        t.CUSTOMER_ID AS customerId,
        t.CONTRACT_ID AS contractId,
        t.REMARK AS remark,
        t.LOCATION_NAME AS locationName,
        t.FACTORY_AREA AS factoryArea,
        t.CROSS_AREA AS crossArea,
        t.SURFACE_GRADE AS surfaceGrade,
        t.C_NO AS cNo,
        t.UP_LEFT_VIEW_PACK_ID AS upLeftViewPackId,
        t.UP_RIGHT_VIEW_PACK_ID AS upRightViewPackId,
        t.IDLE_INTERVAL_ID AS idleIntervalId,
        t.AVALIABLE_MIN_LENGTH AS avaliableMinLength,
        t.OCCUPIED_FLAG AS occupiedFlag,
        t.X_POINT_START_ORIGN AS xPointStartOrign,
        t.X_POINT_END_ORIGN AS xPointEndOrign,
        t.POINT_LOWER_LENGTH AS pointLowerLength,
        t.POINT_UPPER_LENGTH AS pointUpperLength,
        t.REC_CREATOR AS recCreator,
        t.REC_CREATOR_NAME AS recCreatorName,
        t.REC_CREATE_TIME AS recCreateTime,
        t.REC_REVISOR AS recRevisor,
        t.REC_REVISOR_NAME AS recRevisorName,
        t.REC_REVISE_TIME AS recReviseTime,
        t.ARCHIVE_FLAG AS archiveFlag,
        t.TENANT_USER AS tenantUser,
        t.DEL_FLAG AS delFlag,
        t.UUID AS uuid,
        t.FACTORY_BUILDING as factoryBuilding,
        t.FACTORY_BUILDING_NAME as factoryBuildingName
        FROM meli.tlids0605 t
        where
        1=1
        and t.seg_no =  #segNo#
        and t.location_id = #locationId#
        AND t.DEL_FLAG = 1
        and t.use_status in ('99')
        and t.LOC_VIEW_PACK_ID = #locViewPackId#
        AND t.FACTORY_PRODUCT_ID = #factoryProductId#
    </select>

    <!--查询轮询方案-->
    <select id="queryBlackOne" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        t.SEG_NO AS segNo,
        t.UNIT_CODE AS unitCode,
        t.WPROVIDER_ID AS wproviderId,
        t.LOCATION_ID AS locationId,
        t.LOC_VIEW_PACK_ID AS locViewPackId,
        t.LOC_COLUMN_ID AS locColumnId,
        t.USE_STATUS AS useStatus,
        t.LOCATION_TYPE AS locationType,
        t.LOCATION_LENGTH AS locationLength,
        t.SPEC_UPPER AS specUpper,
        t.SPEC_LOWER AS specLower,
        t.X_POINT_START AS xPointStart,
        t.X_POINT_END AS xPointEnd,
        t.Y_POINT_CENTER AS yPointCenter,
        t.LEFT_VIEW_PACK_ID AS leftViewPackId,
        t.RIGHT_VIEW_PCAK_ID AS rightViewPcakId,
        t.UP_DOWN_FLAG AS upDownFlag,
        t.STAND_FLAG AS standFlag,
        t.LR_ACCUPY_FLAG AS lrAccupyFlag,
        t.JG_LOCK_FLAG AS jgLockFlag,
        t.UP_FORBIN_FLAG AS upForbinFlag,
        t.PACK_ID AS packId,
        t.FACTORY_PRODUCT_ID AS factoryProductId,
        t.PRODUCT_TYPE_ID AS productTypeId,
        t.SHOPSIGN AS shopsign,
        t.SPEC AS spec,
        t.INNER_DIAMETER AS innerDiameter,
        t.OUTER_DIAMETER AS outerDiameter,
        t.PUTIN_WEIGHT AS putinWeight,
        t.REF_WIDTH AS refWidth,
        t.CUSTOMER_ID AS customerId,
        t.CONTRACT_ID AS contractId,
        t.REMARK AS remark,
        t.LOCATION_NAME AS locationName,
        t.FACTORY_AREA AS factoryArea,
        t.CROSS_AREA AS crossArea,
        t.SURFACE_GRADE AS surfaceGrade,
        t.C_NO AS cNo,
        t.UP_LEFT_VIEW_PACK_ID AS upLeftViewPackId,
        t.UP_RIGHT_VIEW_PACK_ID AS upRightViewPackId,
        t.IDLE_INTERVAL_ID AS idleIntervalId,
        t.AVALIABLE_MIN_LENGTH AS avaliableMinLength,
        t.OCCUPIED_FLAG AS occupiedFlag,
        t.X_POINT_START_ORIGN AS xPointStartOrign,
        t.X_POINT_END_ORIGN AS xPointEndOrign,
        t.POINT_LOWER_LENGTH AS pointLowerLength,
        t.POINT_UPPER_LENGTH AS pointUpperLength,
        t.REC_CREATOR AS recCreator,
        t.REC_CREATOR_NAME AS recCreatorName,
        t.REC_CREATE_TIME AS recCreateTime,
        t.REC_REVISOR AS recRevisor,
        t.REC_REVISOR_NAME AS recRevisorName,
        t.REC_REVISE_TIME AS recReviseTime,
        t.ARCHIVE_FLAG AS archiveFlag,
        t.TENANT_USER AS tenantUser,
        t.DEL_FLAG AS delFlag,
        t.UUID AS uuid,
        t.FACTORY_BUILDING as factoryBuilding,
        t.FACTORY_BUILDING_NAME as factoryBuildingName
        FROM meli.tlids0605 t
        where
        1=1
        and t.seg_no =  #segNo#
        and t.LOC_VIEW_PACK_ID = #packId#
        AND t.FACTORY_PRODUCT_ID = #factoryOrderNum#
        AND t.WPROVIDER_ID = #warehouseCode#
        AND t.SPEC = #spec#
        AND t.DEL_FLAG = 1
        and t.use_status in ('99')
    </select>


    <select id="queryListOne" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 系统账套 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
        WPROVIDER_ID	as "wproviderId",  <!-- 仓库代码 -->
        LOCATION_ID	as "locationId",  <!-- 库位代码 -->
        LOC_VIEW_PACK_ID	as "locViewPackId",  <!-- 库位捆包号(2位流水码) -->
        LOC_COLUMN_ID	as "locColumnId",  <!-- 库位(跨+列) -->
        USE_STATUS	as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
        LOCATION_TYPE	as "locationType",  <!-- 库位类型。(10点状 20条状) -->
        LOCATION_LENGTH	as "locationLength",  <!-- 库位长度 单位(cm) -->
        SPEC_UPPER	as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER	as "specLower",  <!-- 宽度下限 单位(mm) -->
        X_POINT_START	as "x_pointStart",  <!-- X轴起始点 -->
        X_POINT_END	as "x_pointEnd",  <!-- X轴结束点 -->
        Y_POINT_CENTER	as "y_pointCenter",  <!-- Y轴 -->
        LEFT_VIEW_PACK_ID	as "leftViewPackId",  <!-- 同层相邻左侧库位捆包号（下层库位属性） -->
        RIGHT_VIEW_PCAK_ID	as "rightViewPcakId",  <!-- 同层相邻右侧库位捆包号（下层库位属性） -->
        UP_DOWN_FLAG	as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
        STAND_FLAG	as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        LR_ACCUPY_FLAG	as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
        JG_LOCK_FLAG	as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
        UP_FORBIN_FLAG	as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
        PACK_ID	as "packId",  <!-- 捆包号 -->
        FACTORY_PRODUCT_ID	as "factoryProductId",  <!-- 钢厂资源号 -->
        PRODUCT_TYPE_ID	as "productTypeId",  <!-- 品种 -->
        SHOPSIGN	as "shopsign",  <!-- 牌号 -->
        SPEC	as "spec",  <!-- 规格 -->
        INNER_DIAMETER	as "innerDiameter",  <!-- 内径 单位:mm -->
        OUTER_DIAMETER	as "outerDiameter",  <!-- 外径 单位:mm -->
        PUTIN_WEIGHT	as "putinWeight",  <!-- 重量 -->
        REF_WIDTH	as "refWidth",  <!-- 宽度 单位:mm -->
        CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
        CONTRACT_ID	as "contractId",  <!-- 销售合同 -->
        REMARK	as "remark",  <!-- 备注 -->
        LOCATION_NAME	as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
        CROSS_AREA	as "crossArea",  <!-- 跨区 -->
        SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
        C_NO	as "c_no",  <!-- 所在跨区的库位序号 -->
        UP_LEFT_VIEW_PACK_ID	as "upLeftViewPackId",  <!-- 跨层相邻左侧库位捆包号（上层库位属性） -->
        UP_RIGHT_VIEW_PACK_ID	as "upRightViewPackId",  <!-- 跨层相邻右侧库位捆包号（上层库位属性） -->
        IDLE_INTERVAL_ID	as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
        AVALIABLE_MIN_LENGTH	as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
        OCCUPIED_FLAG	as "occupiedFlag",  <!-- 已占用库位标记 -->
        X_POINT_START_ORIGN	as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
        X_POINT_END_ORIGN	as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
        POINT_LOWER_LENGTH	as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH	as "pointUpperLength",  <!-- 点状库位长度上限 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER	as "tenantUser",  <!-- 租户 -->
        DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
        UUID	as "uuid",  <!-- ID -->
        FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME	as "factoryBuildingName" <!-- 厂房名称 -->
        FROM meli.tlids0605 t WHERE 1=1
        and DEL_FLAG = 0
        and (USE_STATUS = "10") <!-- 新增 -->
        and SEG_NO = #segNo#
        and LOCATION_ID = #locationId#
        and WPROVIDER_ID = #wproviderId#
    </select>
</sqlMap>