<%@ page contentType="text/html;charset=UTF-8" trimDirectiveWhitespaces="true" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiConstant" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiInfo" %>
<%@ page import="com.baosight.iplat4j.core.service.soa.XLocalManager" %>
<%@ page import="org.springframework.web.multipart.MultipartHttpServletRequest" %>
<%@ page import="org.springframework.web.multipart.commons.CommonsMultipartResolver" %>
<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<%@ page import="com.baosight.iplat4j.core.exception.PlatException" %>
<%@ page import="org.springframework.web.multipart.MultipartFile" %>
<%@ page import="java.util.Base64" %>

<%
    UserSession.web2Service(request);
    CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();

    String ename = request.getParameter("ename");
    String serviceName = request.getParameter(EiConstant.serviceName);
    String methodName = request.getParameter(EiConstant.methodName);
    // 通用参数，业务代码
    String id = request.getParameter("id");
    String type = request.getParameter("type");
    String segNo = request.getParameter("segNo");
    MultipartHttpServletRequest multipartRequest = multipartResolver.resolveMultipart(request);
    MultipartFile file = multipartRequest.getFileMap().get(ename);
    byte[] fileContent = file.getBytes(); // 获取文件的字节数据
    String fileBase64 = Base64.getEncoder().encodeToString(fileContent); // 将字节数据编码为Base64字符串
    String mimeType = file.getContentType(); // 获取文件的MIME类型
    String base64Data = "data:" + mimeType + ";base64," + fileBase64; // 添加前缀


    // 反射 serviceName methodName
    EiInfo inInfo = new EiInfo();
    inInfo.set(EiConstant.serviceName, serviceName);
    inInfo.set(EiConstant.methodName, methodName);
    inInfo.set("multipartRequest", multipartRequest);
    inInfo.set("id", id);
    inInfo.set("type", type);
    inInfo.set("segNo", segNo);
    inInfo.set("file", file);
    inInfo.set("file1", base64Data);
    inInfo.set("signatureMark", "1");


    EiInfo outInfo = XLocalManager.call(inInfo);
    UserSession.getData().clear();
    if (outInfo.getStatus() < 0) {
        throw new PlatException(outInfo.getMsg());
    }
%>
