create table meli.tlids0701
(
    SEG_NO                       varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE                    varchar(12)  default ' ' not null comment '业务单元代代码',
    MACHINE_CODE                 varchar(30)  default ' ' not null comment '机组代码',
    MACHINE_NAME                 varchar(50)  default ' ' not null comment '机组名称',
    E_ARCHIVES_NO                varchar(20)  default ' ' not null comment '设备档案编号',
    EQUIPMENT_NAME               varchar(200) default ' ' not null comment '设备名称',
    PROCESS_CATEGORY             varchar(20)  default ' ' not null comment '工序大类代码',
    PROCESS_CATEGORY_NAME        varchar(50)  default ' ' not null comment '工序大类名称',
    CROSS_AREA                   varchar(20)  default ' ' not null comment '跨区编码',
    CROSS_AREA_NAME              varchar(20)  default ' ' not null comment '跨区名称',
    UNPACK_AREA_ID               varchar(20)  default ' ' not null comment '拆包区编号',
    UNPACK_AREA_NAME             varchar(200) default ' ' not null comment '拆包区名称',
    MATERIAL_LOADING_AREA        varchar(20)  default ' ' not null comment '机组上料区代码',
    MATERIAL_LOADING_AREA_NAME   varchar(200) default ' ' not null comment '机组上料区名称',
    MATERIAL_UNLOADING_AREA      varchar(20)  default ' ' not null comment '机组下料区代码',
    MATERIAL_UNLOADING_AREA_NAME varchar(200) default ' ' not null comment '机组下料区名称',
    REJECTION_AREA               varchar(20)  default ' ' not null comment '机组退料区代码',
    REJECTION_AREA_NAME          varchar(200) default ' ' not null comment '机组退料区名称',
    MOULD_CART                   varchar(20)  default ' ' not null comment '机组模具台车代码',
    MOULD_CART_NAME              varchar(200) default ' ' not null comment '机组模具台车名称',
    PACKAGING_TYPE               varchar(20)  default ' ' not null comment '包装类型',
    STATUS                       varchar(2)   default ' ' not null comment '状态',
    REC_CREATOR                  varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME             varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME              varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR                  varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME             varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME              varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG                 varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER                  varchar(10)  default ' ' null comment '租户',
    DEL_FLAG                     smallint     default 0   null comment '删除标记',
    UUID                         varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids0701_UUID_uindex
        unique (UUID)
)
    comment '机组附属信息表' collate = utf8_bin;

