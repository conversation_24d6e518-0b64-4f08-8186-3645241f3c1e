package com.baosight.imom.li.rl.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 韩亚宁
 * @Description: ${部门信息查询}
 * @Date: 2024/12/18 22:00
 * @Version: 1.0
 */
public class ServiceLIRL0005 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询部门信息
     * serviceId
     * @param inInfo
     * @return
     */
    public EiInfo queryDepartment(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = (String) queryMap.get("segNo");
        // String userId = UserSession.getUserId();
        // String userId = UserSession.getUserId();
        List<String> userIdList = new ArrayList<>();
        // userIdList.add(userId);
        inInfo.set("related0SegNo",segNo);
        // inInfo.set("empNos",userIdList );
        inInfo.set(EiConstant.serviceId,"S_UC_EW_0765");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            List<HashMap> returnList = (List<HashMap>) outInfo.get("returnList");
            outInfo.addBlock("sub_result").addRows(returnList);
            outInfo.addBlock(EiConstant.resultBlock).addRows(returnList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }



}
