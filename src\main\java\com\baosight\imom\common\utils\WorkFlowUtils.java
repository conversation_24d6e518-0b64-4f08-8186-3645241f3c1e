package com.baosight.imom.common.utils;

import com.baosight.bpm.engine.WorkflowManager;
import com.baosight.imom.xt.ss.domain.XTSS12;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * 工作流工具类
 *
 * <AUTHOR> 郁在杰
 * @Description :工作流相关操作
 * @Date : 2024/9/19
 * @Version : 1.0
 */
public class WorkFlowUtils {

    /**
     * 启动工作流实例并提交第一个节点
     *
     * @param segNo                业务单元代码
     * @param processDefinitionKey 流程定义key
     * @param businessKey          业务键
     * @param subject              流程描述（用于首页提醒）
     * @param variables            其他参数
     */
    public static String start(String segNo, String processDefinitionKey, String businessKey, String subject,
                               Map<String, Object> variables) {
        if (variables == null) {
            variables = new HashMap<>();
            variables.put("segNo", segNo);
        }
        String starterId = UserSession.getLoginName();
        String comment = "提交审批";
        EiInfo tempInInfo = new EiInfo();
        tempInInfo.set("processDefinitionKey", processDefinitionKey);
        tempInInfo.set("starterId", starterId);
        tempInInfo.set("comment", comment);
        tempInInfo.set("businessKey", businessKey);
        tempInInfo.set("subject", subject);
        tempInInfo.set("variables", variables);
        tempInInfo.set(EiConstant.serviceId, "S_EW_00");
        EiInfo tempOutInfo = XServiceManager.call(tempInInfo);
        if (tempOutInfo.getStatus() < 0) {
            throw new PlatException(tempOutInfo.getMsg());
        }
        return tempOutInfo.getString("processInstanceId");
    }

    /**
     * 获取用户待办任务id
     *
     * @param processInstanceId 流程实例id
     * @return taskId
     */
    public static String getTodoTask(String processInstanceId) {
        String userId = UserSession.getLoginName();
        List<String> idList = new ArrayList<>();
        idList.add(processInstanceId);
        EiInfo tempInInfo = new EiInfo();
        tempInInfo.set("processInstanceId", idList);
        tempInInfo.set("userId", userId);
        tempInInfo.set(EiConstant.serviceId, "S_EW_06");
        EiInfo tempOutInfo = XServiceManager.call(tempInInfo);
        if (tempOutInfo.getStatus() < 0) {
            throw new PlatException(tempOutInfo.getMsg());
        }
        List<Map> taskList = (List<Map>) tempOutInfo.get("tasks");
        return taskList.get(0).get("id").toString();
    }

    /**
     * 获取用户待办任务id
     *
     * @param processInstanceId 流程实例id
     * @return taskId
     */
    public static String getTodoTaskForInter(String processInstanceId, String userId) {
        //String userId = UserSession.getLoginName();
        List<String> idList = new ArrayList<>();
        idList.add(processInstanceId);
        EiInfo tempInInfo = new EiInfo();
        tempInInfo.set("processInstanceId", idList);
        tempInInfo.set("userId", userId);
        tempInInfo.set(EiConstant.serviceId, "S_EW_06");
        EiInfo tempOutInfo = XServiceManager.call(tempInInfo);
        if (tempOutInfo.getStatus() < 0) {
            throw new PlatException(tempOutInfo.getMsg());
        }
        List<Map> taskList = (List<Map>) tempOutInfo.get("tasks");
        return taskList.get(0).get("id").toString();
    }

    /**
     * 审批通过时获取流程下一节点审批人并设为参数
     *
     * @param paramMap 参数：processDefinitionKey，segNo，taskId，variables
     * @return 下一节点是否为结束节点
     */
    public static boolean addAuditPersons(Dao dao,
                                          Map<String, Object> paramMap) {
        String segNo = MapUtils.getStr(paramMap, "segNo");
        if (StrUtil.isBlank(segNo)) {
            throw new PlatException("业务单元代码不能为空");
        }
        String taskId = MapUtils.getStr(paramMap, "taskId");
        Map variables = (Map) paramMap.get("variables");
        List<Map<String, Object>> nextList = WorkflowManager.getNextActivityList(taskId, variables);
        if (CollectionUtils.isEmpty(nextList)) {
            throw new PlatException("找不到下一审批节点" + taskId);
        }
        Map<String, Object> map = nextList.get(0);
        String nodeKey = MapUtils.getStr(map, "nodeKey");
        String nodeName = MapUtils.getStr(map, "nodeName");
        LogUtils.log("下一节点：" + nodeKey);
        if ("End".equalsIgnoreCase(nodeKey)) {
            LogUtils.log("下一节点结束，不处理");
            return true;
        }
        Map<String, String> queryMap = new HashMap<>();
        String processDefinitionKey = MapUtils.getStr(paramMap, "processDefinitionKey");
        queryMap.put("segNo", segNo);
        queryMap.put("processId", processDefinitionKey);
        queryMap.put("nodeNo", nodeKey);
        List<Map> list = dao.query(XTSS12.QUERY_FOR_AUDIT, queryMap);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException("找不到下一节点审批人，请联系管理员配置，审批节点:" + nodeName);
        }
        paramMap.put("nextActivityUserList", list);
        LogUtils.log("下一节点审批人：" + JSONArray.fromObject(list).toString());
        return false;
    }

    /**
     * 批量审批操作
     *
     * @param isAgree 是否为审批同意
     * @return 流程是否结束
     */
    public static void batchAudit(
            List<Map<String, Object>> paramList,
            boolean isAgree) {
        EiInfo tempInInfo = new EiInfo();
        tempInInfo.set("list", paramList);
        if (isAgree) {
            // 批量提交
            tempInInfo.set(EiConstant.serviceId, "S_EW_25");
        } else {
            // 批量回退
            tempInInfo.set(EiConstant.serviceId, "S_EW_26");
        }
        EiInfo tempOutInfo = XServiceManager.call(tempInInfo);
        if (tempOutInfo.getStatus() < 0) {
            throw new PlatException(tempOutInfo.getMsg());
        }
    }

    /**
     * 删除任务
     *
     * @param processInstanceId 流程实例ID
     */
    public static void deleteProcess(String processInstanceId) {
        if (StrUtil.isBlank(processInstanceId)) {
            return;
        }
        String userId = UserSession.getLoginName();
        LogUtils.log("batchDeleteProcessInstances参数信息:" + processInstanceId + "|" + userId);
        // 删除流程实例
        WorkflowManager.deleteProcessInstance(processInstanceId, "", userId);
    }

}
