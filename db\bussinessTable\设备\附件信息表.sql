create table TMEDV0109
(
    RELEVANCE_TYPE   VARCHAR(10)  default ' '    not null comment '关联类型',
    RELEVANCE_ID     VARCHAR(64)  default ' '    not null comment '关联ID',
    UPLOAD_FILE_PATH VARCHAR(100) default ' '    not null comment '文件路径',
    UPLOAD_FILE_NAME VARCHAR(100) default ' '    not null comment '文件名称',
    FIFLE_TYPE       VARCHAR(10)  default ' '    not null comment '文件类型',
    FIFLE_SIZE       DECIMAL(9)   default 0      not null comment '文件大小',
    FILE_ID          VARCHAR(64)  default ' '    not null comment '文件id',
    -- 固定字段
    UUID             VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR      VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME  VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR      VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME  VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID        VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG     VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='附件信息表' ENGINE = INNODB
                        DEFAULT CHARSET = UTF8
                        COLLATE UTF8_BIN;