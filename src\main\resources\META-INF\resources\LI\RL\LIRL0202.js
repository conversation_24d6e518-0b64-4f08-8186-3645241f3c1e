$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    $("#SUB_QUERY").on("click", function (e) {
        sub_resultGrid.dataSource.page(1);
    });
    var addsubwindow = $("#ADDSUBWINDOW");

    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                //隐藏存档按钮
                var tableId = e.contentElement.id;
                if (tableId == 'info-1') {
                    /*$("#inqu_status-0-agreementId").val("");
                    IPLAT.EFPopupInput.enable($("#inqu_status-0-unitCode"), true);
                    IPLAT.EFInput.readonly($("#inqu_status-0-agreementId"), false);
                    if_add = false;*/
                }
                if (tableId == 'info-2') {
                    var model = null;
                    if (resultGrid.getSelectedRows().length <= 0 && resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("操作失败，原因[请勾选一条记录再进行查看！]", "error");
                        e.preventDefault();
                        return false;
                    } else if (resultGrid.getCheckedRows().length > 1) {
                        NotificationUtil("操作失败，原因[只能查看一条数据！]", "error");
                        e.preventDefault(); //阻止切换第二个tab
                        return;
                    }
                    model = resultGrid.getCheckedRows()[0];
                    var segNo = model["segNo"];
                    var vehicleNo = model["vehicleNo"];
                    $("#inqu2_status-0-vehicleNo").val(vehicleNo);
                    $("#inqu2_status-0-segNo").val(segNo);
                    $("#inqu2_status-0-unitCode").val(segNo);

                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil({msg: "原因[请先选择主项业务单元代码！]"}, "error")
                        return;
                    }
                    var eiInfo = new EiInfo();
                    IPLAT.progress($("body"), false);
                    eiInfo.setByNode("inqu2");
                    eiInfo.addBlock(resultGrid.getCheckedBlockData());
                    EiCommunicator.send("LIRL0202", "query2", eiInfo, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil(ei);
                            } else {
                                ei.setMsg("查询成功！");
                                NotificationUtil({msg: ei.msg}, "success");
                                result2Grid.setEiInfo(ei);
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            // NotificationUtil({msg: ei.msg}, "error");
                            NotificationUtil(ei);
                            return false;
                        }
                    }, {async: false});
                    var detail = document.getElementById("info-2");
                    IPLAT.clearNode(detail);
                }
            }
        }
    };


    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 100,
                pageSizes: [10,20,50,100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "customerId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "承运商/客户代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "ADDSUBWINDOW",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "承运商/客户代码"
                            })
                        }
                    }
                },
                {
                    field: "driverName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "司机姓名",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "driver",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "司机信息"
                            })
                        }
                    }
                },
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });

                // 获取勾选数据，
                $("#LIFTABAN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行解禁！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0202", "liftaban", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                // 获取勾选数据，
                $("#INSERTSAVEN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0202", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                // 获取勾选数据，
                $("#UPDATESAVEN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("修改失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0202", "update", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("确认失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0202", "confirm", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("反确认失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0202", "liftaban", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {

            }
        },
        "sub_result": {
            onRowDblClick: function (e) {
                /*$("#inqu_status-0-userNum").val(e.model.userNum)
                $("#inqu_status-0-userName").val(e.model.chineseUserName)*/
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    varModel.customerId = e.model.userNum;
                    varModel.customerName = e.model.chineseUserName;
                }
                resultGrid.refresh();
                //关闭弹出框
                ADDSUBWINDOWWindow.close();
            }
        },
    };
    IPLATUI.EFWindow = {
        //关闭新增子项弹出框时的事件
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
        "unitInfo01": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo01");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = resultGrid.getCheckedRows(),
                        eiInfo = new EiInfo();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = resultGrid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId").val(row[0].userNum);
                    $("#inqu_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "userNum2": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum2");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId2").val(row[0].userNum);
                    $("#inqu_status-0-customerName2").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "ADDSUBWINDOW": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_resultGrid.getDataItems().length > 0) {
                    sub_resultGrid.removeRows(sub_resultGrid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let reservationIdentity = varModel.reservationIdentity;
                    if (IPLAT.isBlankString(reservationIdentity)){
                        NotificationUtil({msg:"预约身份(承运商/客户)！"}, "error");
                        return;
                    }
                    IPLAT.EFSelect.value($("#sub_query_status-0-reservationIdentity"),reservationIdentity);
                    $("#sub_query_status-0-unitCode").val(varModel.unitCode);
                    $("#sub_query_status-0-segNo").val(varModel.segNo);
                }
            }
        },
        "driver": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = driverWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu_status-0-windowId").val("driver");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let customerId = varModel.customerId;
                    let customerName = varModel.customerName;
                    if (IPLAT.isBlankString(customerId)){
                        NotificationUtil({msg:"请先选择承运商/客户信息！"}, "error");
                        return;
                    }
                    /*IPLAT.EFSelect.value($("#sub_query_status-0-reservationIdentity"),reservationIdentity);
                    $("#sub_query_status-0-unitCode").val(varModel.unitCode);
                    $("#sub_query_status-0-segNo").val(varModel.segNo);*/
                    iframejQuery("#inqu_status-0-segNo").val(segNo);
                    iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                    iframejQuery("#inqu_status-0-segName").val($("#inqu_status-0-segName").val());
                    iframejQuery("#inqu_status-0-customerId").val(customerId);
                    iframejQuery("#inqu_status-0-customerName").val(customerName);
                }
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = driverWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = resultGrid.getCheckedRows(),
                        eiInfo = new EiInfo();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = resultGrid.getCheckedRows()[i];
                        varModel.driverName = row[0].driverName;
                        varModel.vehicleNo = row[0].vehicleNo;
                        varModel.driverTel = row[0].tel;
                        varModel.driverIdentity = row[0].driverIdentity;
                    }
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
    }

});
