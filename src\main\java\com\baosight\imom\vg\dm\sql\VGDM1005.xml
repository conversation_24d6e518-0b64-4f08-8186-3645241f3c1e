<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1005">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stackName">
            STACK_NAME = #stackName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packType">
            PACK_TYPE = #packType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1005">
        SELECT
        RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        OUT_PACK_ID as "outPackId",  <!-- 产出捆包号 -->
        PART_ID as "partId",  <!-- 物料号 -->
        PACK_TYPE as "packType",  <!-- 捆包类型2成品4余料 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        PROCESS_HOUR as "processHour",  <!-- 加工工时 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
        STACK_NAME as "stackName",  <!-- 堆垛名称 -->
        LIFT_FLAG as "liftFlag",  <!-- 吊运标记 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        FINISHING_SHUNT_FLAG as "finishingShuntFlag",  <!-- 精整分流标记 -->
        UNITED_QUANTITY as "unitedQuantity",  <!-- 并包数量 -->
        CUSTOMER_ID as "customerId"  <!-- 客户代码 -->
        FROM ${mevgSchema}.TVGDM1005 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1005 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryForImc" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        PACK_ID as "packId",  <!-- 捆包号 -->
        OUT_PACK_ID as "outPutPackId",  <!-- 产出捆包号 -->
        PART_ID as "partId",  <!-- 物料号 -->
        PACK_TYPE as "packType",  <!-- 捆包类型2成品4余料 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        PROCESS_HOUR as "processHour",  <!-- 加工工时 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        FLOOR(QUANTITY) as "quantity",  <!-- 数量 -->
        STACK_NAME as "areaCode"  <!-- 堆垛 -->
        FROM ${mevgSchema}.TVGDM1005 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="querySum" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SUM(NET_WEIGHT) as "netWeight",  <!-- 净重 -->
        SUM(QUANTITY) as "quantity"  <!-- 数量 -->
        FROM ${mevgSchema}.TVGDM1005 WHERE 1=1
        AND DEL_FLAG = '0'
        AND RELEVANCE_ID = #relevanceId#
        <isNotEmpty prepend=" AND " property="partId">
            PART_ID = #partId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packType">
            PACK_TYPE = #packType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notPackType">
            PACK_TYPE != #notPackType#
        </isNotEmpty>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1005 (RELEVANCE_ID,  <!-- 关联ID -->
        PACK_ID,  <!-- 捆包号 -->
        OUT_PACK_ID,  <!-- 产出捆包号 -->
        PART_ID,  <!-- 物料号 -->
        PACK_TYPE,  <!-- 捆包类型2成品4余料 -->
        UNITED_PACK_ID,  <!-- 并包号 -->
        PROCESS_HOUR,  <!-- 加工工时 -->
        NET_WEIGHT,  <!-- 净重 -->
        QUANTITY,  <!-- 数量 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        SPECS_DESC,  <!-- 规格描述 -->
        STACK_NAME,  <!-- 堆垛名称 -->
        LIFT_FLAG,  <!-- 吊运标记 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        FINISHING_SHUNT_FLAG,  <!-- 精整分流标记 -->
        UNITED_QUANTITY,  <!-- 并包数量 -->
        CUSTOMER_ID  <!-- 客户代码 -->
        )
        VALUES (#relevanceId#, #packId#, #outPackId#, #partId#, #packType#, #unitedPackId#, #processHour#, #netWeight#,
        #quantity#,#uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#,#tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #specsDesc#, #stackName#, #liftFlag#,
        #machineCode#,#finishingShuntFlag#, #unitedQuantity#, #customerId#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1005 WHERE
        UUID = #uuid#
    </delete>

    <update id="updForDel">
        UPDATE ${mevgSchema}.TVGDM1005
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1005
        SET
        RELEVANCE_ID = #relevanceId#,   <!-- 关联ID -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        OUT_PACK_ID = #outPackId#,   <!-- 产出捆包号 -->
        PART_ID = #partId#,   <!-- 物料号 -->
        PACK_TYPE = #packType#,   <!-- 捆包类型2成品4余料 -->
        UNITED_PACK_ID = #unitedPackId#,   <!-- 并包号 -->
        PROCESS_HOUR = #processHour#,   <!-- 加工工时 -->
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        QUANTITY = #quantity#,   <!-- 数量 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        SPECS_DESC = #specsDesc#,   <!-- 规格描述 -->
        STACK_NAME = #stackName#,   <!-- 堆垛名称 -->
        LIFT_FLAG = #liftFlag#,   <!-- 吊运标记 -->
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        UNITED_QUANTITY = #unitedQuantity#   <!-- 并包数量 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateQuantity">
        UPDATE ${mevgSchema}.TVGDM1005
        SET
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        QUANTITY = #quantity#,   <!-- 数量 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateHour">
        UPDATE ${mevgSchema}.TVGDM1005
        SET
        PROCESS_HOUR = #processHour#,   <!-- 加工工时 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>