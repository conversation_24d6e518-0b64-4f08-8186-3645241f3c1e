<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>



<EF:EFPage>
    <jsp:attribute name="footer">
        <script>
            var ctx = "${ctx}";
        </script>
    </jsp:attribute>
    <jsp:body>
        <div class="row">
            <!--页面左侧树-->
            <div class="col-md-3">
                <EF:EFRegion title="组织机构树"
                             id="tree"
                             fitHeight="true">
                    <!--树顶部过滤器-->
                    <div class="row">
                        <div class="col-md-4">
                            <span style="line-height: 25px">过滤器</span>
                        </div>
                        <div class="col-md-8">
                            <input id="filterText" name="filterText" />
                        </div>
                    </div>
                    <!--菜单树-->
                    <div id="menu"
                         style="margin-top: 12px; margin-bottom: 8px">
                        <EF:EFTree bindId="categoryTree" ename="tree_name" textField="text"
                                   valueField="label" hasChildren="leaf"  pid="parent_id"
                                   serviceName="XSLV0300" methodName="query">
                        </EF:EFTree>
                    </div>
                </EF:EFRegion>
            </div>
            <div class="col-md-9">
                <EF:EFRegion id="inqu" title="查询条件">
                    <EF:EFInput blockId="inqu_status" ename="groupEname" cname="用户组英文名"
                                row="0" placeholder="请输入用户组英文名"/>
                    <EF:EFInput blockId="inqu_status" ename="groupCname" cname="用户组中文名"
                                row="0" placeholder="请输入用户组中文名"/>
                    <EF:EFInput blockId="inqu_status" ename="orgCname" cname="所属组织" row="0" readonly="true"/>
                    <EF:EFInput blockId="inqu_status" ename="orgId" cname="组织ID" row="0" type="hidden"/><%--隐藏--%>
                </EF:EFRegion>
                <EF:EFRegion id="result" title="查询结果" fitHeight="true">
                    <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" checkMode="multiple, row">
                        <EF:EFColumn ename="userGroupId" cname="用户组ID" readonly="true" hidden="true"/><%--隐藏--%>
                        <EF:EFColumn ename="groupEname" cname="用户组英文名" readonly="true" locked="true"/>
                        <EF:EFColumn ename="groupCname" cname="用户组中文名" readonly="true" locked="true"/>
                        <EF:EFColumn ename="parentGroupEname" cname="父用户组英文名" readonly="true" locked="true"/>
                        <EF:EFColumn ename="parentGroupCname" cname="父用户组中文名" readonly="true" locked="true"/>
                        <EF:EFColumn ename="orgCname" cname="所属组织" readonly="true" locked="true"/>
                        <EF:EFColumn ename="recCreator" cname="创建人" readonly="true"/>
                        <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150"
                                     parseFormats="['yyyyMMddHHmmss']" editType="datetime"
                                     dateFormat="yyyy-MM-dd HH:mm:ss" readonly="true"/>
                        <EF:EFColumn ename="recRevisor" cname="修改人" readonly="true"/>
                        <EF:EFColumn ename="recReviseTime" cname="修改时间" readonly="true"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>
        <EF:EFWindow id="createUserGroup"
                     width="43%"
                     height="33%">
            <EF:EFRegion id="createUserGroupRegion"
                         title="创建用户组"
                         style="margin-bottom:0;">
                <div id="createUserGroupDiv">
                    <div class="row">
                        <EF:EFInput ename="groupEname" cname="用户组英文名" colWidth="6" required="true"/>
                        <EF:EFInput ename="categoryEname" readonly="true" inline="true"/>
                    </div>
                    <div class="row">
                        <EF:EFInput ename="categoryCname" cname="用户组中文名" colWidth="6" readonly="true"/>
                        <EF:EFInput ename="groupCname" inline="true" required="true" colWidth="6"/>
                    </div>
                    <div class="row">
                        <EF:EFPopupInput ename="add_baseGroup" clear="true"  readonly="true" colWidth="6"
                                         containerId="addBaseGroup" popupWidth="900" popupTitle="添加父用户组"
                                         cname="父用户组"/>
                    </div>
                </div>
                <div class="k-window-save k-popup-save">
                    <EF:EFButton ename="addNode" cname="确定" layout="1" class="i-btn-wide"/>

                </div>
            </EF:EFRegion>
        </EF:EFWindow>
        <div id="addBaseGroup"
             style="display: none">
            <EF:EFRegion id="inquGroup" title="查询条件"  type="query"
                         efRegionShowClear="true" efRegionSave="true">
                <div class="row">
                    <div class="col-xs-2 control-label">
                        <span>用户组英文名</span>
                    </div>
                    <div class="col-xs-2">
                        <EF:EFInput blockId="inqu_status" ename="groupEname1" row="0"
                                    cname="用户组英文名" inline="true"/>
                    </div>
                    <div class="col-xs-2 control-label">
                        <span>用户组中名</span>
                    </div>
                    <div class="col-xs-2">
                        <EF:EFInput blockId="inqu_status" ename="groupCname1" row="0"
                                    cname="用户组中文名" inline="true"/>
                    </div>
                    <div class="col-xs-12"
                         style="text-align: right"
                         id="inqubase">
                    </div>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="baseGroups"
                         title="记录集" >
                <EF:EFGrid blockId="bases" queryMethod="queryBaseGroup"
                           autoDraw="false" checkMode="single, row">
                    <EF:EFColumn ename="id" cname="用户组ID"
                                 primaryKey="true"  hidden="true" readonly="true" />
                    <EF:EFColumn ename="groupEname" width="400" locked="true"
                                 cname="用户组英文名" readonly="true"/>
                    <EF:EFColumn ename="groupCname" width="400" locked="true"
                                 cname="用户组中文名" readonly="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <div class="k-window-save k-popup-save">
                <EF:EFButton ename="addParentGroup" cname="确定" layout="1" class="i-btn-wide"/>
            </div>
        </div>
    </jsp:body>
</EF:EFPage>
