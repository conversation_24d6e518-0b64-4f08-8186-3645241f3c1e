<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-deviceName" cname="分部设备名称" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-caseId" cname="案例编号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-voucherNum" cname="依据凭单" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-faultType" cname="故障类型" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P055"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-faultLevel" cname="故障级别" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P056"/>
            </EF:EFSelect>
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                           startCname="创建时间(起)" endCname="创建时间(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="caseId" cname="案例编号" align="center" width="120"/>
            <EF:EFColumn ename="voucherNum" cname="依据凭单" align="center" width="120"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称"/>
            <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center"/>
            <EF:EFColumn ename="deviceName" cname="分部设备名称"/>
            <EF:EFComboColumn ename="faultType" cname="故障类型" align="center" width="70">
                <EF:EFCodeOption codeName="P055"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="faultLevel" cname="故障级别" align="center" width="70">
                <EF:EFCodeOption codeName="P056"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="faultDesc" cname="故障描述" width="200"/>
            <EF:EFColumn ename="handleMeasures" cname="处理措施" width="200"/>
            <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="80"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>