/**
* Generate time : 2024-11-05 9:08:46
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0407
* 
*/
public class LIRL0407 extends DaoEPBase {
        public static final String QUERY = "LIRL0407.query";
        public static final String QUERY_ALL_HAND_POINT_TIME = "LIRL0407.queryAllHandPointTime";
        public static final String COUNT = "LIRL0407.count";
        public static final String INSERT = "LIRL0407.insert";
        public static final String UPDATE = "LIRL0407.update";
        public static final String UPDATE_NEXT_TARGET = "LIRL0407.updateNextTarget";
        public static final String UPDATE_STATUS = "LIRL0407.updateStatus";
        public static final String DELETE = "LIRL0407.delete";

                private String segNo = " ";		/* 账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String finishLoadId = " ";		/* 结束装卸货流水号*/
                private String status = " ";		/* 状态（00：撤销，10：新增）*/
                private String finishLoadDate = " ";		/* 结束装卸货日期*/
                private String cancelFinishLoadDate = " ";		/* 撤销结束装卸货时间*/
                private String carTraceNo = " ";		/* 车辆跟踪号*/
                private String dateSource = " ";		/* 数据源（10：MES,20:PDA）*/
                private String voucherNum = " ";		/* 依据凭单号（PAD作业流水号）*/
                private String nextTatget = " ";		/* 下一目标（10：下个装卸点，20：离厂）*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String currentHandPointId = " ";		/* 当前装卸点代码*/
                private String tatgetHandPointId = " ";		/* 目标装卸点代码（下个装卸点）*/
                private String factoryArea = " ";		/* 厂区*/
                private String documentType = " ";		/* 单据类型(10普通 20重复)*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String loadId = " ";		/* 开始装卸货流水号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finishLoadId");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("结束装卸货流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态（00：撤销，10：新增）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finishLoadDate");
        eiColumn.setDescName("结束装卸货日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cancelFinishLoadDate");
        eiColumn.setDescName("撤销结束装卸货时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dateSource");
        eiColumn.setDescName("数据源（10：MES,20:PDA）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单号（PAD作业流水号）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nextTatget");
        eiColumn.setDescName("下一目标（10：下个装卸点，20：离厂）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointId");
        eiColumn.setDescName("当前装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tatgetHandPointId");
        eiColumn.setDescName("目标装卸点代码（下个装卸点）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("documentType");
        eiColumn.setDescName("单据类型(10普通 20重复)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadId");
        eiColumn.setDescName("开始装卸货流水号");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0407() {
initMetaData();
}

        /**
        * get the segNo - 账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the finishLoadId - 结束装卸货流水号
        * @return the finishLoadId
        */
        public String getFinishLoadId() {
        return this.finishLoadId;
        }

        /**
        * set the finishLoadId - 结束装卸货流水号
        */
        public void setFinishLoadId(String finishLoadId) {
        this.finishLoadId = finishLoadId;
        }
        /**
        * get the status - 状态（00：撤销，10：新增）
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态（00：撤销，10：新增）
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the finishLoadDate - 结束装卸货日期
        * @return the finishLoadDate
        */
        public String getFinishLoadDate() {
        return this.finishLoadDate;
        }

        /**
        * set the finishLoadDate - 结束装卸货日期
        */
        public void setFinishLoadDate(String finishLoadDate) {
        this.finishLoadDate = finishLoadDate;
        }
        /**
        * get the cancelFinishLoadDate - 撤销结束装卸货时间
        * @return the cancelFinishLoadDate
        */
        public String getCancelFinishLoadDate() {
        return this.cancelFinishLoadDate;
        }

        /**
        * set the cancelFinishLoadDate - 撤销结束装卸货时间
        */
        public void setCancelFinishLoadDate(String cancelFinishLoadDate) {
        this.cancelFinishLoadDate = cancelFinishLoadDate;
        }
        /**
        * get the carTraceNo - 车辆跟踪号
        * @return the carTraceNo
        */
        public String getCarTraceNo() {
        return this.carTraceNo;
        }

        /**
        * set the carTraceNo - 车辆跟踪号
        */
        public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
        }
        /**
        * get the dateSource - 数据源（10：MES,20:PDA）
        * @return the dateSource
        */
        public String getDateSource() {
        return this.dateSource;
        }

        /**
        * set the dateSource - 数据源（10：MES,20:PDA）
        */
        public void setDateSource(String dateSource) {
        this.dateSource = dateSource;
        }
        /**
        * get the voucherNum - 依据凭单号（PAD作业流水号）
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 依据凭单号（PAD作业流水号）
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the nextTatget - 下一目标（10：下个装卸点，20：离厂）
        * @return the nextTatget
        */
        public String getNextTatget() {
        return this.nextTatget;
        }

        /**
        * set the nextTatget - 下一目标（10：下个装卸点，20：离厂）
        */
        public void setNextTatget(String nextTatget) {
        this.nextTatget = nextTatget;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the currentHandPointId - 当前装卸点代码
        * @return the currentHandPointId
        */
        public String getCurrentHandPointId() {
        return this.currentHandPointId;
        }

        /**
        * set the currentHandPointId - 当前装卸点代码
        */
        public void setCurrentHandPointId(String currentHandPointId) {
        this.currentHandPointId = currentHandPointId;
        }
        /**
        * get the tatgetHandPointId - 目标装卸点代码（下个装卸点）
        * @return the tatgetHandPointId
        */
        public String getTatgetHandPointId() {
        return this.tatgetHandPointId;
        }

        /**
        * set the tatgetHandPointId - 目标装卸点代码（下个装卸点）
        */
        public void setTatgetHandPointId(String tatgetHandPointId) {
        this.tatgetHandPointId = tatgetHandPointId;
        }
        /**
        * get the factoryArea - 厂区
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the documentType - 单据类型(10普通 20重复)
        * @return the documentType
        */
        public String getDocumentType() {
        return this.documentType;
        }

        /**
        * set the documentType - 单据类型(10普通 20重复)
        */
        public void setDocumentType(String documentType) {
        this.documentType = documentType;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }

        public String getLoadId() {
                return loadId;
        }

        public void setLoadId(String loadId) {
                this.loadId = loadId;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setFinishLoadId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finishLoadId")), finishLoadId));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setFinishLoadDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finishLoadDate")), finishLoadDate));
                setCancelFinishLoadDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("cancelFinishLoadDate")), cancelFinishLoadDate));
                setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
                setDateSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dateSource")), dateSource));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setNextTatget(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nextTatget")), nextTatget));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setCurrentHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("currentHandPointId")), currentHandPointId));
                setTatgetHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tatgetHandPointId")), tatgetHandPointId));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setDocumentType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("documentType")), documentType));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setLoadId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadId")), loadId));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("finishLoadId",StringUtils.toString(finishLoadId, eiMetadata.getMeta("finishLoadId")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("finishLoadDate",StringUtils.toString(finishLoadDate, eiMetadata.getMeta("finishLoadDate")));
                map.put("cancelFinishLoadDate",StringUtils.toString(cancelFinishLoadDate, eiMetadata.getMeta("cancelFinishLoadDate")));
                map.put("carTraceNo",StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
                map.put("dateSource",StringUtils.toString(dateSource, eiMetadata.getMeta("dateSource")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("nextTatget",StringUtils.toString(nextTatget, eiMetadata.getMeta("nextTatget")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("currentHandPointId",StringUtils.toString(currentHandPointId, eiMetadata.getMeta("currentHandPointId")));
                map.put("tatgetHandPointId",StringUtils.toString(tatgetHandPointId, eiMetadata.getMeta("tatgetHandPointId")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("documentType",StringUtils.toString(documentType, eiMetadata.getMeta("documentType")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("loadId",StringUtils.toString(loadId, eiMetadata.getMeta("loadId")));

return map;

}
}