package com.baosight.imom.li.ds.service;


import com.baosight.imom.li.ds.domain.LIDS0102;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * 厂区厂房代码下拉
 */
public class ServiceLIDS02 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLIDS02.class);

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0102().eiMetadata);
        return inInfo;
    }

}
