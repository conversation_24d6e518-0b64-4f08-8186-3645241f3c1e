$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 刷新按钮
    $("#REFRESH").on("click", function (e) {
        const info = new EiInfo();
        info.setByNode("inqu");
        info.set("clientType", "refresh");
        IMOMUtil.submitEiInfo(info, "VGDM99", "queryClientStatus", function (ei) {
            $("#detail_status-0-clientStatus").val(ei.get("detail_status-0-clientStatus"));
            $("#detail_status-0-monitorStr").val(ei.get("detail_status-0-monitorStr"));
        });
    });
    // 启动监听按钮
    $("#START").on("click", function (e) {
        const info = new EiInfo();
        info.setByNode("inqu");
        info.set("clientType", "refresh");
        IMOMUtil.submitEiInfo(info, "VGDM06", "startClient", function (ei) {
            $("#detail_status-0-clientStatus").val(ei.get("detail_status-0-clientStatus"));
            $("#detail_status-0-monitorStr").val("");
        });
    });
    // 添加监控点
    $("#MONITOR").on("click", function (e) {
        tagInfoWindow.open().center();
    });
    // 停止监听按钮
    $("#STOP").on("click", function (e) {
        const info = new EiInfo();
        info.setByNode("inqu");
        info.set("clientType", "refresh");
        IMOMUtil.submitEiInfo(info, "VGDM06", "stopClient", function (ei) {
            $("#detail_status-0-clientStatus").val(ei.get("detail_status-0-clientStatus"));
            $("#detail_status-0-monitorStr").val("");
        });
    });
    // grid配置
    IPLATUI.EFGrid = {
        // 点检计划子项
        "result": {
            columns: [
                {
                    field: "tagTimeStr",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        return DateUtils.format(Number(item["tagTime"]), "yyyy-MM-dd HH:mm:ss.SSS");
                    }
                }
            ]
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 监控点弹窗
    IMOMUtil.windowTemplate({
        windowId: "tagInfo",
        _open: function (e, iframejQuery) {
            if (IPLAT.isBlankString(unitInfo.unitCode) || IPLAT.isBlankString(unitInfo.segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitInfo.unitCode);
            iframejQuery("#inqu_status-0-segNo").val(unitInfo.segNo);
            iframejQuery("#inqu_status-0-closeFlag").val("0");
        },
        afterSelect: function (rows, iframejQuery) {
            // 关闭弹窗标记,只有点击确定按钮后关闭才会是1
            const closeFlag = iframejQuery("#inqu_status-0-closeFlag").val();
            if (closeFlag !== "1") {
                return;
            }
            if (rows.length > 0) {
                const tagIds = rows.map((row) => `${row.scadaName}.${row.tagId}`);
                const info = new EiInfo();
                info.setByNode("inqu");
                info.set("clientType", "refresh");
                info.set("tagIds", tagIds);
                IMOMUtil.submitEiInfo(info, "VGDM99", "addMonitor", function (ei) {
                    $("#detail_status-0-monitorStr").val(ei.get("detail_status-0-monitorStr"));
                });
            }
        }
    });
});
