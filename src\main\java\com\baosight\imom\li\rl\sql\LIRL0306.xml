<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-26 9:06:18
   		Version :  1.0
		tableName :${meliSchema}.tlirl0306 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 HAND_POINT_ID  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 CURRENT_JOB_NUMBER  VARCHAR   NOT NULL, 
		 PRE_JOB_NUMBER  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0306">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0306">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
				STATUS	as "status",  <!-- 作业状态(10车辆饱和 20可以进车 30暂停进车) -->
				CURRENT_JOB_NUMBER	as "currentJobNumber",  <!-- 当前作业数 -->
				PRE_JOB_NUMBER	as "preJobNumber",  <!-- 预计作业数 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0306 WHERE 1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0306 WHERE 1=1
	</select>

	<!--	车辆进车状态查询最大容纳数的装卸点-->
	<select id="queryMaxHandPoint" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select (tlirl0304.VEHICLE_NUMER - (tlirl0306.PRE_JOB_NUMBER - tlirl0306.CURRENT_JOB_NUMBER)) as
		"maxNum",
		tlirl0304.HAND_POINT_ID                                                               as "handPointId"
		from meli.tlirl0306 tlirl0306,
		meli.tlirl0304 tlirl0304
		where tlirl0306.SEG_NO = #segNo#
		and tlirl0306.SEG_NO = tlirl0304.SEG_NO
		and tlirl0306.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
		<isNotEmpty prepend=" AND " property="arrayList">
			tlirl0306.HAND_POINT_ID in
			<iterate property="arrayList" open="("
					 close=")" conjunction=" , ">
				#arrayList[]#
			</iterate>
		</isNotEmpty>
		AND tlirl0304.STATUS='30'
		order by maxNum desc
	</select>


<!--	车辆饱和状态查询排队表占用最少车辆的点-->
	<select id="queryMaxQueueVehicleNo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select TARGET_HAND_POINT_ID as "handPointId",
		       count(1) as  "handPointNum"
		from meli.tlirl0401 tlirl0401
		where tlirl0401.SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="arrayList">
			tlirl0401.TARGET_HAND_POINT_ID in
			<iterate property="arrayList" open="("
					 close=")" conjunction=" , ">
				#arrayList[]#
			</iterate>
		</isNotEmpty>
		group by TARGET_HAND_POINT_ID asc
		order by handPointNum
		limit 1
	</select>
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentJobNumber">
			CURRENT_JOB_NUMBER = #currentJobNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="preJobNumber">
			PRE_JOB_NUMBER = #preJobNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->
	<!--	车辆饱和状态查询排队表占用最少车辆的点-->
	<select id="queryExistReversion" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select tlirl0201.IS_RESERVATION as "isReservation",
			   concat(tlirl0201.RESERVATION_DATE,concat(SUBSTRING_INDEX(REPLACE(RESERVATION_TIME,':',''), '-', 1)),'00')
				   as "reservationTime"
		from meli.tlirl0301 tlirl0301,
			 meli.tlirl0201 tlirl0201
		where 1 = 1
		  and tlirl0301.SEG_NO = tlirl0201.SEG_NO
		  and tlirl0301.RESERVATION_NUMBER = tlirl0201.RESERVATION_NUMBER
		  and tlirl0301.SEG_NO = #segNo#
		  and tlirl0301.CAR_TRACE_NO = #carTraceNo#
		  and tlirl0301.STATUS != '00'
		  and tlirl0201.STATUS='20'
	</select>

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0306 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										HAND_POINT_ID,  <!-- 装卸点代码 -->
										STATUS,  <!-- 作业状态(10车辆饱和 20可以进车 30暂停进车) -->
										CURRENT_JOB_NUMBER,  <!-- 当前作业数 -->
										PRE_JOB_NUMBER,  <!-- 预计作业数 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #handPointId#, #status#, #currentJobNumber#, #preJobNumber#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0306 WHERE 
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0306 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					HAND_POINT_ID	= #handPointId#,   <!-- 装卸点代码 -->  
					STATUS	= #status#,   <!-- 作业状态(10车辆饱和 20可以进车 30暂停进车) -->  
					CURRENT_JOB_NUMBER	= #currentJobNumber#,   <!-- 当前作业数 -->  
					PRE_JOB_NUMBER	= #preJobNumber#,   <!-- 预计作业数 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE
		SEG_NO =#segNo#
		and
		HAND_POINT_ID =#handPointId#
	</update>



	<update id="updateLirl0306">
		UPDATE ${meliSchema}.tlirl0306
		SET
		REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
		REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= #delFlag#   <!-- 记录删除标记 -->
		WHERE
		SEG_NO =#segNo#
		and
		HAND_POINT_ID =#handPointId#
	</update>


	<update id="updateJobs">
		UPDATE ${meliSchema}.tlirl0306
		SET
			PRE_JOB_NUMBER=0,
			CURRENT_JOB_NUMBER=0,
			STATUS='20'
		WHERE
		SEG_NO =#segNo#
		<isNotEmpty prepend=" AND " property="handPointList">
			HAND_POINT_ID in
			<iterate property="handPointList" open="("
					 close=")" conjunction=" , ">
				#handPointList[]#
			</iterate>
		</isNotEmpty>
	</update>


	<update id="updateJob">
		UPDATE ${meliSchema}.tlirl0306
		SET
		STATUS=#status#,
		CURRENT_JOB_NUMBER=#currentJobNumber#,
		PRE_JOB_NUMBER=#preJobNumber#
		WHERE
		SEG_NO =#segNo#
		and HAND_POINT_ID=#handPointId#
	</update>
<!--&lt;!&ndash;更新当前作业数，预计作业数&ndash;&gt;-->
<!--	<update id="updateJobNumberByHandPoint">-->
<!--		UPDATE ${meliSchema}.tlirl0306 tlirl0306-->
<!--		SET-->
<!--		CURRENT_JOB_NUMBER	= tlirl0306.CURRENT_JOB_NUMBER + #currentJobNumber#,   &lt;!&ndash; 当前作业数 &ndash;&gt;-->
<!--		PRE_JOB_NUMBER	= tlirl0306.PRE_JOB_NUMBER + #preJobNumber#,   &lt;!&ndash; 预计作业数 &ndash;&gt;-->
<!--		REC_CREATOR	= #recCreator#,   &lt;!&ndash; 记录创建人 &ndash;&gt;-->
<!--		REC_CREATOR_NAME	= #recCreatorName#,   &lt;!&ndash; 记录创建人姓名 &ndash;&gt;-->
<!--		REC_CREATE_TIME	= #recCreateTime#,   &lt;!&ndash; 记录创建时间 &ndash;&gt;-->
<!--		REC_REVISOR	= #recRevisor#,   &lt;!&ndash; 记录修改人 &ndash;&gt;-->
<!--		REC_REVISOR_NAME	= #recRevisorName#,   &lt;!&ndash; 记录修改人姓名 &ndash;&gt;-->
<!--		REC_REVISE_TIME	= #recReviseTime#,   &lt;!&ndash; 记录修改时间 &ndash;&gt;-->
<!--		ARCHIVE_FLAG	= #archiveFlag#,   &lt;!&ndash; 归档标记 &ndash;&gt;-->
<!--		DEL_FLAG	= #delFlag#,   &lt;!&ndash; 记录删除标记 &ndash;&gt;-->
<!--		REMARK	= #remark#,   &lt;!&ndash; 备注 &ndash;&gt;-->
<!--		SYS_REMARK	= #sysRemark#,   &lt;!&ndash; 系统备注 &ndash;&gt;-->
<!--		UUID	= #uuid#,   &lt;!&ndash; uuid &ndash;&gt;-->
<!--		TENANT_ID	= #tenantId#  &lt;!&ndash; 租户ID &ndash;&gt;-->
<!--		WHERE-->
<!--		SEG_NO =#segNo#-->
<!--		and-->
<!--		HAND_POINT_ID =#handPointId#-->
<!--	</update>-->

	<!--如果是进厂删除叫号表，预计作业数都是-1+1-->
	<update id="updateJobNumberByHandPoint">
		UPDATE ${meliSchema}.tlirl0306 tlirl0306
		SET
		CURRENT_JOB_NUMBER	= tlirl0306.CURRENT_JOB_NUMBER + #currentJobNumber#,   <!-- 当前作业数 -->
		PRE_JOB_NUMBER	= tlirl0306.PRE_JOB_NUMBER + #preJobNumber#,
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
		DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
		REMARK	= #remark#,   <!-- 备注 -->
		SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->
		TENANT_ID	= #tenantId#  <!-- 租户ID -->
		WHERE
		SEG_NO =#segNo#
		and
		HAND_POINT_ID =#handPointId#
	</update>

	<update id="updateJobNumberByHandPoint2">
		UPDATE ${meliSchema}.tlirl0306 tlirl0306
		SET
		<isNotEmpty property="currentJobNumber">
			CURRENT_JOB_NUMBER	= tlirl0306.CURRENT_JOB_NUMBER + #currentJobNumber#,   <!-- 当前作业数 -->
		</isNotEmpty>
		<isNotEmpty property="currentJobNumber">
			PRE_JOB_NUMBER	= tlirl0306.PRE_JOB_NUMBER - #preJobNumber#,   <!-- 预计作业数 -->
		</isNotEmpty>
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO =#segNo#
		and
		HAND_POINT_ID =#handPointId#
	</update>


	<update id="updateJobNumberByHandPoint3">
		UPDATE ${meliSchema}.tlirl0306 tlirl0306
		SET
		<isNotEmpty property="currentJobNumber">
			CURRENT_JOB_NUMBER	= tlirl0306.CURRENT_JOB_NUMBER + #currentJobNumber#,   <!-- 当前作业数 -->
		</isNotEmpty>
		<isNotEmpty property="currentJobNumber">
			PRE_JOB_NUMBER	= tlirl0306.PRE_JOB_NUMBER + #preJobNumber#,   <!-- 预计作业数 -->
		</isNotEmpty>
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO =#segNo#
		and
		HAND_POINT_ID =#handPointId#
	</update>

	<update id="updateJobNumberByHandPoint4">
		UPDATE ${meliSchema}.tlirl0306 tlirl0306
		SET
		<isNotEmpty property="currentJobNumber">
			CURRENT_JOB_NUMBER	= tlirl0306.CURRENT_JOB_NUMBER - #currentJobNumber#,   <!-- 当前作业数 -->
		</isNotEmpty>
		<isNotEmpty property="currentJobNumber">
			PRE_JOB_NUMBER	= tlirl0306.PRE_JOB_NUMBER + #preJobNumber#,   <!-- 预计作业数 -->
		</isNotEmpty>
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO =#segNo#
		and
		HAND_POINT_ID =#handPointId#
	</update>


<!--	更新装卸进车状态-->
	<update id="updatelvStatus">
		update ${meliSchema}.tlirl0306 t
		set t.status = #status#
		where t.seg_no = #segNo#
		and t.hand_point_id =#handPointId#
	</update>
  
</sqlMap>