package com.baosight.imom.li.rl.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.rl.dao.LIRL0308;
import com.baosight.imom.li.rl.dao.LIRL0312;
import com.baosight.imom.li.rl.dao.LIRL0316;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;


import java.math.BigDecimal;
import java.util.*;

public class ServiceLIRL0316 extends ServiceBase {
    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0316().eiMetadata);
        return inInfo;
    }
    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIRL0316.QUERY, new LIRL0316());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    String attachmentPrint = MapUtils.getString(itemMap, "attachmentPrint");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //判断提单号不能重复
                    String voucherNum = MapUtils.getString(itemMap, "voucherNum");
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo",segNo);
                    queryMap.put("voucherNum",voucherNum);
                    queryMap.put("delFlag","0");
                    List<LIRL0316> voucherNumList = this.dao.query(LIRL0316.QUERY,queryMap);
                    if (CollectionUtils.isNotEmpty(voucherNumList)){
                        throw new PlatException("提单号:"+voucherNum + "已存在，不可新增!");
                    }
                    //when ATTACHMENT_PRINT = '10' then '一体机内置A4'
                    //				when ATTACHMENT_PRINT = '20' then '一体机内置针打'
                    //				when ATTACHMENT_PRINT = '30' then '仓库办公室A4'
                    //				when ATTACHMENT_PRINT = '40' then '仓库办公室针打'
                    if ("一体机内置针打".equals(attachmentPrint)){
                        itemMap.put("attachmentPrint", "10");
                    }else if ("一体机内置A4".equals(attachmentPrint)){
                        itemMap.put("attachmentPrint", "20");
                    }else if ("仓库办公室A4".equals(attachmentPrint))
                        itemMap.put("attachmentPrint", "30");
                    if ("仓库办公室针打".equals(attachmentPrint))
                        itemMap.put("attachmentPrint", "40");
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIRL0316.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    String attachmentPrint = MapUtils.getString(itemMap, "attachmentPrint");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //查询区域代码状态，判断非新增状态数据不可修改
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("voucherNum", MapUtils.getString(itemMap, "voucherNum"));
                    queryMap.put("status", "10");
                    int count = super.count(LIRL0316.COUNT, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "voucherNum")+",提单号重复，不可修改!");
                    }
                    queryMap.clear();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    queryMap.put("voucherNum", MapUtils.getString(itemMap, "voucherNum"));
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    queryMap.put("status", "10");
                     count = super.count(LIRL0316.COUNT, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "voucherNum")+",提单状态非新增状态，不可修改!");
                    }

                    if ("一体机内置针打".equals(attachmentPrint)){
                        itemMap.put("attachmentPrint", "10");
                    }else if ("一体机内置A4".equals(attachmentPrint)){
                        itemMap.put("attachmentPrint", "20");
                    }else if ("仓库办公室A4".equals(attachmentPrint))
                        itemMap.put("attachmentPrint", "30");
                    if ("仓库办公室针打".equals(attachmentPrint))
                        itemMap.put("attachmentPrint", "40");
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIRL0316.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("voucherNum", MapUtils.getString(itemMap, "voucherNum"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIRL0316.COUNT, queryMap);
                if (count < 1) {
                    throw new PlatException(MapUtils.getString(itemMap, "voucherNum")+",提单状态非新增状态，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIRL0316.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    /**
     * 查询附件详情
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryFile(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            String voucherNum =(String) inInfo.get("voucherNum");
            HashMap queryMap = new HashMap();
            queryMap.put("voucherNum",voucherNum);
            List<LIRL0312> lirl0312 = this.dao.query(LIRL0316.QUERY_FILE, queryMap);
            outInfo.addBlock("AttachmentDetails");
            outInfo.getBlock("AttachmentDetails").setRows(lirl0312);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("查询成功");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 更新附件打印机
     *
     * @param inInfo
     * @return
     */
    public EiInfo updateFile(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> attachmentDetails = inInfo.getBlock("AttachmentDetails").getRows();
            if (CollectionUtil.isEmpty(attachmentDetails)){
                throw new PlatException("附件为空，不可删除!");
            }
            for (HashMap attachmentDetail : attachmentDetails) {
                String uuid = (String) attachmentDetail.get("uuid");
                HashMap queryMap = new HashMap();
                queryMap.put("uuid",uuid);
                queryMap.put("attachmentPrint",attachmentDetail.get("attachmentPrint"));
                this.dao.update(LIRL0312.UPDATE_ATTACHMENT_PRINT, queryMap);
            }

        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除附件
     *
     * @param inInfo
     * @return
     */
    public EiInfo deleteFile(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> attachmentDetails = inInfo.getBlock("AttachmentDetails").getRows();
            if (CollectionUtil.isEmpty(attachmentDetails)){
                throw new PlatException("附件为空，不可删除!");
            }
            for (HashMap attachmentDetail : attachmentDetails) {
                String uuid = (String) attachmentDetail.get("uuid");
                HashMap queryMap = new HashMap();
                queryMap.put("uuid",uuid);
                this.dao.delete(LIRL0312.DELETE, queryMap);
            }
            //同步删除IMC附件
            inInfo.set("delFlag", "1");
            inInfo.set("delList", attachmentDetails);
            inInfo.set(EiConstant.serviceId, "S_UC_PR_0417");
            //调post请求
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            //打印日志到elk
            if (outInfo.getStatus() == -1) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("调用文件上传物流报错:" + outInfo.getMsg());
                throw new RuntimeException(outInfo.getMsg());
            }

        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 查询提货委托
     * @param inInfo
     * @return
     */
    public EiInfo queryDelegate(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            String segNo =(String) inInfo.get("segNo");
            String driverName =(String) inInfo.get("driverName");
            String driverTel =(String) inInfo.get("driverTel");
            String driverIdentity =(String) inInfo.get("driverIdentity");
            String voucherNum =(String) inInfo.get("voucherNum");
            HashMap hashMap = new HashMap();
            hashMap.put("segNo", segNo);
            hashMap.put("verifiStatus", "10");
            if (StringUtils.isNotBlank(voucherNum)){
                hashMap.put("ladingBillIdEq", voucherNum);
            }
            if (StringUtils.isNotBlank(driverTel)){
                hashMap.put("driverPhone", driverTel);
            }
            if (StringUtils.isNotBlank(driverIdentity)){
                hashMap.put("driverId", driverIdentity);
            }
            if (StringUtils.isNotBlank(driverName)){
                hashMap.put("driver", driverName);
            }
            inInfo.set("main", hashMap);
            inInfo.set(EiConstant.serviceId, "S_UV_SL_9020");
            inInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            outInfo.addBlock("billOfLading");
            outInfo.getBlock("billOfLading").setRows((ArrayList)inInfo.get("result"));
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("查询成功");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 附件上传
     * S_LI_RL_0049
     */
    public EiInfo fileUpload(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            // 获取前端参数
            CommonsMultipartFile file = (CommonsMultipartFile) inInfo.get("file");
            String type = (String) inInfo.get("type"); //1，发货人，2，领用人，3，收货人
//            CommonsMultipartFile file = Base64ToMultipartFileConverter.convertToMultipartFile(base64String);
            /*CommonsMultipartFile file = (CommonsMultipartFile) inInfo.get("file");*/
            String segNo = inInfo.getString("segNo");
            String voucherNum = inInfo.getString("id");
            String signatureMark = (String) inInfo.get("signatureMark");
            // 上传文件
            String fileName = file.getOriginalFilename();
            if (StrUtil.isBlank(fileName)) {
                fileName = "";
            }
            // 生成文件ID
            String fileId = StrUtil.getUUID();
            // 获取文件后缀
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            // 转换文件名防止文件重复
            String directory = "/miniPrograms/"+segNo;;
            String downloadUrl = FileUtils.uploadFile(file, directory);
            if (downloadUrl == null) {
                throw new PlatException("文件上传失败");
            }
            // 待新增的附件记录
            LIRL0312 lirl0312 = new LIRL0312();
            // lirl0312.setRelevanceId(id);
            // 文件信息
            lirl0312.setUploadFileName(fileName);
            lirl0312.setFifleType(suffix);
            lirl0312.setFifleSize(new BigDecimal(file.getSize()));
            lirl0312.setFileId(fileId);
            // 设置文件下载路径
            lirl0312.setUploadFilePath(downloadUrl);
            lirl0312.setRecCreator("System");
            lirl0312.setRecCreatorName("System");
            lirl0312.setRecRevisor("System");
            lirl0312.setRecRevisorName("System");
            lirl0312.setSignatureMark(signatureMark);
            lirl0312.setSegNo(segNo);
            lirl0312.setUnitCode(segNo);
            lirl0312.setRecCreateTime(DateUtil.curDateTimeStr14());
            lirl0312.setRecReviseTime(DateUtil.curDateTimeStr14());
            lirl0312.setUuid(fileId);
            lirl0312.setRelevanceType(voucherNum);
            lirl0312.setRelevanceId(voucherNum);
            Map insMap = lirl0312.toMap();
            dao.insert(LIRL0312.INSERT, insMap);
            //文件上传到物流服务器
            if ("1".equals(signatureMark)) {
                EiInfo eiInfo1 = new EiInfo();
                eiInfo1.set("userId", "System");
                eiInfo1.set("userName", "System");
                Map pictureMap = new HashMap();
                pictureMap.put("billNo", voucherNum);
                pictureMap.put("segNo", segNo);
                pictureMap.put("billSubid", type);
                //查询账套中文名称
                Map querySegNoName = new HashMap();
                querySegNoName.put("segNo", segNo);
                List<LIRL0312> queryName = dao.query(LIRL0312.QUERY_SEG_NO_NAME, querySegNoName);
                if (queryName.size() > 0) {
                    LIRL0312 lirl03121 = queryName.get(0);
                    Map map = lirl03121.toMap();
                    pictureMap.put("segCname", org.apache.commons.collections.MapUtils.getString(map,"segName"));
                }
                pictureMap.put("affixType", "312");
                pictureMap.put("type", suffix);
                //如果type为1,2 PDA上传 根据车辆跟踪号查询该车的出库单号，出库单单号与mes绑定回写imc附件信息表
                    List<String> fileList = new ArrayList();
                    //截取图片文件参数 base64String
                    String base64String = (String) inInfo.get("file1");
                    String[] split = base64String.split("base64,");
                    fileList.add(split[1]);
                    pictureMap.put("pictureList", fileList);
                    pictureMap.put("uuid", fileId);
                    eiInfo1.set("pictureMap", pictureMap);
                    eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0417");
                    //调post请求
                    outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                    /*System.out.println("入参："+eiInfo1.toJSON());*/
                    //打印日志到elk
                    if (outInfo.getStatus() == -1) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("调用文件上传物流报错:" + outInfo.getMsg());
                        throw new RuntimeException(outInfo.getMsg());
                    }
                }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("附件上传成功!");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }
}
