<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="XTSS11">
    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 业务单元代码 -->
        REC_CREATOR	as "recCreator",  <!-- 创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 修改人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
        TENANT_USER	as "tenantUser",  <!-- 租户ID -->
        UNIT_CODE	as "unitCode", <!-- 业务单元名称 -->
        VERSION as "version", <!-- 版本号 -->
        VERSION_DESC as "versionDesc", <!-- 版本描述 -->
        DOWN_URL as "downUrl" <!-- 下载地址 -->
        FROM ${platSchema}.xs_pda_automatic_upgrade WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>
</sqlMap>