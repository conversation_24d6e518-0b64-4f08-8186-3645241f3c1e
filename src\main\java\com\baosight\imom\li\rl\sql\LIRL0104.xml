<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-09-26 11:15:13
   		Version :  1.0
		tableName :${meliSchema}.tlirl0104 
		 TEL  VARCHAR   NOT NULL, 
		 DRIVER_NAME  VARCHAR, 
		 MESSAGE_CODE  VARCHAR, 
		 EFFECTIVE_TIME  INTEGER, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMAR<PERSON>  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0104">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0104">
		SELECT
				TEL	as "tel",  <!-- 手机号 -->
				DRIVER_NAME	as "driverName",  <!-- 司机姓名 -->
				MESSAGE_CODE	as "messageCode",  <!-- 验证码 -->
				EFFECTIVE_TIME	as "effectiveTime",  <!-- 有效时间 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0104 WHERE 1=1
		<isNotEmpty prepend=" AND " property="tel">
			TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			REC_CREATE_TIME desc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0104 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="tel">
			TEL = #tel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="messageCode">
			MESSAGE_CODE = #messageCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="effectiveTime">
			EFFECTIVE_TIME = #effectiveTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0104 (TEL,  <!-- 手机号 -->
										DRIVER_NAME,  <!-- 司机姓名 -->
										MESSAGE_CODE,  <!-- 验证码 -->
										EFFECTIVE_TIME,  <!-- 有效时间 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#tel#, #driverName#, #messageCode#, #effectiveTime#, #recCreator#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #uuid#, #tenantId#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0104 WHERE 
			UUID = #uuid#
	</delete>

	<delete id="deleteByTel">
		DELETE FROM ${meliSchema}.tlirl0104 WHERE
		TEL = #tel#
	</delete>

	<delete id="deleteByTelCode">
		DELETE FROM ${meliSchema}.tlirl0104 WHERE
		TEL = #tel#
		and
		MESSAGE_CODE=#messageCode#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0104 
		SET 
		TEL	= #tel#,   <!-- 手机号 -->  
					DRIVER_NAME	= #driverName#,   <!-- 司机姓名 -->  
					MESSAGE_CODE	= #messageCode#,   <!-- 验证码 -->  
					EFFECTIVE_TIME	= #effectiveTime#,   <!-- 有效时间 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
								TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>