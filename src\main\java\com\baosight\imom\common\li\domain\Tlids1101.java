/**
 * Generate time : 2024-12-05 14:44:45
 * Version : 1.0
 */
package com.baosight.imom.common.li.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tlids1101
 */
public class Tlids1101 extends DaoEPBase {

    private String segNo = " ";        /* 系统账套*/
    private String unitCode = " ";        /* 业务单元代代码*/

    private String segName = " ";        /* 业务单元简称*/
    private String craneOrderId = " ";        /* 行车作业清单号*/
    private String listSource = " ";        /* 清单来源*/
    private String voucherNum = " ";        /* 依据凭单号*/
    private String craneId = " ";        /* 行车编号*/
    private String craneName = " ";        /* 行车名称*/
    private String batchNumber = " ";        /* 批次号*/
    private String serialNumber = " ";        /* 顺序号*/
    private String machineCode = " ";        /* 机组代码*/
    private String machineName = " ";        /* 机组名称*/
    private String unpackAreaId = " ";        /* 拆包区编号*/
    private String unpackAreaName = " ";        /* 拆包区名称*/
    private String mouldId = " ";        /* 模具ID*/
    private String mouldName = " ";        /* 模具名称*/
    private String startTime = " ";        /* 作业开始时间*/
    private String endTime = " ";        /* 作业结束时间*/
    private String jobTime = " ";        /* 作业时间*/
    private String startAreaType = " ";        /* 起始区域类型*/
    private String startAreaCode = " ";        /* 起始区域类型代码*/
    private String startAreaName = " ";        /* 起始区域类型名称*/
    private String endAreaType = " ";        /* 终到区域类型*/
    private String endAreaCode = " ";        /* 终到区域类型代码*/
    private String endAreaName = " ";        /* 终到区域类型名称*/
    private String status = " ";        /* 状态*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private String archiveFlag = " ";        /* 归档标记*/
    private String tenantUser = " ";        /* 租户*/
    private Integer delFlag = Integer.valueOf(0);        /* 删除标记*/
    private String uuid = " ";        /* ID*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneOrderId");
        eiColumn.setDescName("行车作业清单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("listSource");
        eiColumn.setDescName("清单来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneId");
        eiColumn.setDescName("行车编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneName");
        eiColumn.setDescName("行车名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("batchNumber");
        eiColumn.setDescName("批次号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("serialNumber");
        eiColumn.setDescName("顺序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackAreaId");
        eiColumn.setDescName("拆包区编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackAreaName");
        eiColumn.setDescName("拆包区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldId");
        eiColumn.setDescName("模具ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldName");
        eiColumn.setDescName("模具名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startTime");
        eiColumn.setDescName("作业开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endTime");
        eiColumn.setDescName("作业结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jobTime");
        eiColumn.setDescName("作业时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startAreaType");
        eiColumn.setDescName("起始区域类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startAreaCode");
        eiColumn.setDescName("起始区域类型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startAreaName");
        eiColumn.setDescName("起始区域类型名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endAreaType");
        eiColumn.setDescName("终到区域类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endAreaCode");
        eiColumn.setDescName("终到区域类型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endAreaName");
        eiColumn.setDescName("终到区域类型名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tlids1101() {
        initMetaData();
    }

    /**
     * get the segNo - 系统账套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the craneOrderId - 行车作业清单号
     *
     * @return the craneOrderId
     */
    public String getCraneOrderId() {
        return this.craneOrderId;
    }

    /**
     * set the craneOrderId - 行车作业清单号
     */
    public void setCraneOrderId(String craneOrderId) {
        this.craneOrderId = craneOrderId;
    }

    /**
     * get the listSource - 清单来源
     *
     * @return the listSource
     */
    public String getListSource() {
        return this.listSource;
    }

    /**
     * set the listSource - 清单来源
     */
    public void setListSource(String listSource) {
        this.listSource = listSource;
    }

    /**
     * get the voucherNum - 依据凭单号
     *
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 依据凭单号
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the craneId - 行车编号
     *
     * @return the craneId
     */
    public String getCraneId() {
        return this.craneId;
    }

    /**
     * set the craneId - 行车编号
     */
    public void setCraneId(String craneId) {
        this.craneId = craneId;
    }

    /**
     * get the craneName - 行车名称
     *
     * @return the craneName
     */
    public String getCraneName() {
        return this.craneName;
    }

    /**
     * set the craneName - 行车名称
     */
    public void setCraneName(String craneName) {
        this.craneName = craneName;
    }

    /**
     * get the batchNumber - 批次号
     *
     * @return the batchNumber
     */
    public String getBatchNumber() {
        return this.batchNumber;
    }

    /**
     * set the batchNumber - 批次号
     */
    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    /**
     * get the serialNumber - 顺序号
     *
     * @return the serialNumber
     */
    public String getSerialNumber() {
        return this.serialNumber;
    }

    /**
     * set the serialNumber - 顺序号
     */
    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    /**
     * get the machineCode - 机组代码
     *
     * @return the machineCode
     */
    public String getMachineCode() {
        return this.machineCode;
    }

    /**
     * set the machineCode - 机组代码
     */
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    /**
     * get the machineName - 机组名称
     *
     * @return the machineName
     */
    public String getMachineName() {
        return this.machineName;
    }

    /**
     * set the machineName - 机组名称
     */
    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    /**
     * get the unpackAreaId - 拆包区编号
     *
     * @return the unpackAreaId
     */
    public String getUnpackAreaId() {
        return this.unpackAreaId;
    }

    /**
     * set the unpackAreaId - 拆包区编号
     */
    public void setUnpackAreaId(String unpackAreaId) {
        this.unpackAreaId = unpackAreaId;
    }

    /**
     * get the unpackAreaName - 拆包区名称
     *
     * @return the unpackAreaName
     */
    public String getUnpackAreaName() {
        return this.unpackAreaName;
    }

    /**
     * set the unpackAreaName - 拆包区名称
     */
    public void setUnpackAreaName(String unpackAreaName) {
        this.unpackAreaName = unpackAreaName;
    }

    /**
     * get the mouldId - 模具ID
     *
     * @return the mouldId
     */
    public String getMouldId() {
        return this.mouldId;
    }

    /**
     * set the mouldId - 模具ID
     */
    public void setMouldId(String mouldId) {
        this.mouldId = mouldId;
    }

    /**
     * get the mouldName - 模具名称
     *
     * @return the mouldName
     */
    public String getMouldName() {
        return this.mouldName;
    }

    /**
     * set the mouldName - 模具名称
     */
    public void setMouldName(String mouldName) {
        this.mouldName = mouldName;
    }

    /**
     * get the startTime - 作业开始时间
     *
     * @return the startTime
     */
    public String getStartTime() {
        return this.startTime;
    }

    /**
     * set the startTime - 作业开始时间
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * get the endTime - 作业结束时间
     *
     * @return the endTime
     */
    public String getEndTime() {
        return this.endTime;
    }

    /**
     * set the endTime - 作业结束时间
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    /**
     * get the jobTime - 作业时间
     *
     * @return the jobTime
     */
    public String getJobTime() {
        return this.jobTime;
    }

    /**
     * set the jobTime - 作业时间
     */
    public void setJobTime(String jobTime) {
        this.jobTime = jobTime;
    }

    /**
     * get the startAreaType - 起始区域类型
     *
     * @return the startAreaType
     */
    public String getStartAreaType() {
        return this.startAreaType;
    }

    /**
     * set the startAreaType - 起始区域类型
     */
    public void setStartAreaType(String startAreaType) {
        this.startAreaType = startAreaType;
    }

    /**
     * get the startAreaCode - 起始区域类型代码
     *
     * @return the startAreaCode
     */
    public String getStartAreaCode() {
        return this.startAreaCode;
    }

    /**
     * set the startAreaCode - 起始区域类型代码
     */
    public void setStartAreaCode(String startAreaCode) {
        this.startAreaCode = startAreaCode;
    }

    /**
     * get the startAreaName - 起始区域类型名称
     *
     * @return the startAreaName
     */
    public String getStartAreaName() {
        return this.startAreaName;
    }

    /**
     * set the startAreaName - 起始区域类型名称
     */
    public void setStartAreaName(String startAreaName) {
        this.startAreaName = startAreaName;
    }

    /**
     * get the endAreaType - 终到区域类型
     *
     * @return the endAreaType
     */
    public String getEndAreaType() {
        return this.endAreaType;
    }

    /**
     * set the endAreaType - 终到区域类型
     */
    public void setEndAreaType(String endAreaType) {
        this.endAreaType = endAreaType;
    }

    /**
     * get the endAreaCode - 终到区域类型代码
     *
     * @return the endAreaCode
     */
    public String getEndAreaCode() {
        return this.endAreaCode;
    }

    /**
     * set the endAreaCode - 终到区域类型代码
     */
    public void setEndAreaCode(String endAreaCode) {
        this.endAreaCode = endAreaCode;
    }

    /**
     * get the endAreaName - 终到区域类型名称
     *
     * @return the endAreaName
     */
    public String getEndAreaName() {
        return this.endAreaName;
    }

    /**
     * set the endAreaName - 终到区域类型名称
     */
    public void setEndAreaName(String endAreaName) {
        this.endAreaName = endAreaName;
    }

    /**
     * get the status - 状态
     *
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the tenantUser - 租户
     *
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the uuid - ID
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setCraneOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneOrderId")), craneOrderId));
        setListSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("listSource")), listSource));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setCraneId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneId")), craneId));
        setCraneName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneName")), craneName));
        setBatchNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("batchNumber")), batchNumber));
        setSerialNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("serialNumber")), serialNumber));
        setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
        setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
        setUnpackAreaId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackAreaId")), unpackAreaId));
        setUnpackAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackAreaName")), unpackAreaName));
        setMouldId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldId")), mouldId));
        setMouldName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldName")), mouldName));
        setStartTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startTime")), startTime));
        setEndTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endTime")), endTime));
        setJobTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jobTime")), jobTime));
        setStartAreaType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startAreaType")), startAreaType));
        setStartAreaCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startAreaCode")), startAreaCode));
        setStartAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startAreaName")), startAreaName));
        setEndAreaType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endAreaType")), endAreaType));
        setEndAreaCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endAreaCode")), endAreaCode));
        setEndAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endAreaName")), endAreaName));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("craneOrderId", StringUtils.toString(craneOrderId, eiMetadata.getMeta("craneOrderId")));
        map.put("listSource", StringUtils.toString(listSource, eiMetadata.getMeta("listSource")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("craneId", StringUtils.toString(craneId, eiMetadata.getMeta("craneId")));
        map.put("craneName", StringUtils.toString(craneName, eiMetadata.getMeta("craneName")));
        map.put("batchNumber", StringUtils.toString(batchNumber, eiMetadata.getMeta("batchNumber")));
        map.put("serialNumber", StringUtils.toString(serialNumber, eiMetadata.getMeta("serialNumber")));
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("machineName", StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("unpackAreaId", StringUtils.toString(unpackAreaId, eiMetadata.getMeta("unpackAreaId")));
        map.put("unpackAreaName", StringUtils.toString(unpackAreaName, eiMetadata.getMeta("unpackAreaName")));
        map.put("mouldId", StringUtils.toString(mouldId, eiMetadata.getMeta("mouldId")));
        map.put("mouldName", StringUtils.toString(mouldName, eiMetadata.getMeta("mouldName")));
        map.put("startTime", StringUtils.toString(startTime, eiMetadata.getMeta("startTime")));
        map.put("endTime", StringUtils.toString(endTime, eiMetadata.getMeta("endTime")));
        map.put("jobTime", StringUtils.toString(jobTime, eiMetadata.getMeta("jobTime")));
        map.put("startAreaType", StringUtils.toString(startAreaType, eiMetadata.getMeta("startAreaType")));
        map.put("startAreaCode", StringUtils.toString(startAreaCode, eiMetadata.getMeta("startAreaCode")));
        map.put("startAreaName", StringUtils.toString(startAreaName, eiMetadata.getMeta("startAreaName")));
        map.put("endAreaType", StringUtils.toString(endAreaType, eiMetadata.getMeta("endAreaType")));
        map.put("endAreaCode", StringUtils.toString(endAreaCode, eiMetadata.getMeta("endAreaCode")));
        map.put("endAreaName", StringUtils.toString(endAreaName, eiMetadata.getMeta("endAreaName")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));

        return map;

    }
}