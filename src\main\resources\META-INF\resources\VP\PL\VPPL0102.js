$(function () {
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        //当前TAB页ID
        let tab_Info = $("#info").data("kendoTabStrip");
        let tabIndex = tab_Info.select().index();

        const productDateStart = $("#inqu_status-0-productDateStart").val();
        const productDateEnd = $("#inqu_status-0-productDateEnd").val();
        if (tabIndex < 3) {
            if (IPLAT.isBlankString(productDateStart) || IPLAT.isBlankString(productDateEnd)) {
                NotificationUtil({msg: "请选择操作时间起止再进行查询!"}, "error");
                return;
            }
        }

        //根据TabId控制查询
        if (0 === tabIndex) {
            groupByEmpGrid.dataSource.page(1);
        } else if (1 === tabIndex) {
            groupByAreaGrid.dataSource.page(1);
        } else if (2 === tabIndex) {
            personTimeInAreaGrid.dataSource.page(1);
        } else {
            resultGrid.dataSource.page(1);
        }
    });

    /**
     * 来回切换还要刷新tab页面
     */
    /*IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                const productDateStart = $("#inqu_status-0-productDateStart").val();
                const productDateEnd = $("#inqu_status-0-productDateEnd").val();
                let tabId = e.contentElement.id;
                switch (tabId) {
                    case 'info-1':
                        if (!IPLAT.isBlankString(unitCode) && !IPLAT.isBlankString(segNo)) {
                            resultGrid.dataSource.page(1);
                        }
                        break;
                    case 'info-2':
                        if (!IPLAT.isBlankString(unitCode) && !IPLAT.isBlankString(segNo)) {
                            groupByEmpGrid.dataSource.page(1);
                        }
                        break;
                    case 'info-3':
                        if (!IPLAT.isBlankString(unitCode) && !IPLAT.isBlankString(segNo)) {
                            groupByAreaGrid.dataSource.page(1);
                        }
                        break;
                    case 'info-4':
                        if (!IPLAT.isBlankString(unitCode) && !IPLAT.isBlankString(segNo)) {
                            personTimeInAreaGrid.dataSource.page(1);
                        }
                        break;
                }


            }
        }
    };*/

    /**
     * 大数据后端导出
     */
    $("#EXPORTEXCEL").on("click", function () {
        let segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(segNo)) {
            NotificationUtil("请选择业务单元代码！", "error");
            return;
        }
        //获取当前tab页ID
        let tab_Info = $("#info").data("kendoTabStrip");
        let tabIndex = tab_Info.select().index();

        const productDateStart = $("#inqu_status-0-productDateStart").val();
        const productDateEnd = $("#inqu_status-0-productDateEnd").val();
        if (tabIndex < 3) {
            if (IPLAT.isBlankString(productDateStart) || IPLAT.isBlankString(productDateEnd)) {
                NotificationUtil({msg: "请选择操作时间起止再进行导出!"}, "error");
                return;
            }
        }

        let fileName = segNo + "员工有效工时统计" + ".xlsx";
        let exportEi = new EiInfo();
        exportEi.setByNode("inqu");
        IMOMUtil.setExportColumnBlock(exportEi, groupByEmpGrid);
        IMOMUtil.setExportSheetColumnBlock(exportEi, groupByAreaGrid);
        IMOMUtil.setExportSheetColumnBlock(exportEi, personTimeInAreaGrid);
        IMOMUtil.setExportSheetColumnBlock(exportEi, resultGrid);
        exportEi.set("exportColumnBlock", 'fileName', fileName);
        exportEi.set("exportColumnBlock", 'sheetName', "员工有效工时汇总(按人员)");
        exportEi.set("exportColumnBlockSheet" + groupByAreaGrid.options.blockId, 'sheetName', "员工有效工时汇总(按机组)");
        exportEi.set("exportColumnBlockSheet" + personTimeInAreaGrid.options.blockId, 'sheetName', "员工区域明细");
        exportEi.set("exportColumnBlockSheet" +resultGrid.options.blockId, 'sheetName', "人员进出明细");
        IMOMUtil.callService({
            service: "VPPL0102",
            method: "postExport",
            eiInfo: exportEi,
            showProgress: true,
            async: true,
            callback: function (ei) {
                if (ei.status > -1) {
                    let docUrl = ei.getBlock("excelDoc").get("docUrl");
                    window.open(docUrl);
                }
            }
        });
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "workingHours",
                    valueType: "N",//页面汇总设置
                    type: "N"
                }
            ],
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },
        }
    }
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });
})