<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-02 10:17:53
   		Version :  1.0
		table comment : 投料捆包表
		tableName :${meviSchema}.VIPM0008 
		 UNIT_CODE  VARCHAR, 
		 PROCESS_ORDER_ID  VARCHAR, 
		 PROCESS_DEMAND_ID  VARCHAR, 
		 PROCESS_DEMAND_SUB_ID  VARCHAR, 
		 PART_ID  VARCHAR, 
		 SPECS_DESC  VARCHAR, 
		 SHOPSIGN  VARCHAR, 
		 PACK_ID  VARCHAR   NOT NULL, 
		 MAT_INNER_ID  VARCHAR   NOT NULL, 
		 PACK_QTY  BIGINT   NOT NULL, 
		 NET_WEIGHT  DECIMAL, 
		 PROD_NAME_CODE  VARCHAR, 
		 PROD_C<PERSON>ME  VARCHAR, 
		 FACTORY_ORDER_NUM  VARCHAR, 
		 PROCESS_DEMAND_MATERAIL_STATUS  VARCHAR, 
		 VOUCHER_NUM  VARCHAR, 
		 AUDIT_FLAG  VARCHAR, 
		 WAREHOUSE_CODE  VARCHAR, 
		 WAREHOUSE_NAME  VARCHAR, 
		 LOCATION_ID  VARCHAR, 
		 LOCATION_NAME  VARCHAR, 
		 F_PACK_ID  VARCHAR, 
		 F_MAT_INNER_ID  VARCHAR, 
		 M_PACK_ID  VARCHAR, 
		 M_MAT_INNER_ID  VARCHAR, 
		 PACK_STATUS  VARCHAR, 
		 YIELD  DECIMAL, 
		 PRODUCING_AREA_DESC  VARCHAR, 
		 REMARK  VARCHAR, 
		 MATERIAL_LOCK  VARCHAR, 
		 HEAT_NUM  VARCHAR, 
		 CHARGE_PLAT  VARCHAR, 
		 CAN_RETURN_MATERIAL  VARCHAR, 
		 SORT_ID  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR, 
		 PROCESS_DEMAND_MATERAIL_ID  VARCHAR, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 PROD_CODE  VARCHAR, 
		 STORE_TYPE  VARCHAR, 
		 ORDER_TYPE_CODE  VARCHAR, 
		 CUSTOMER_ID  VARCHAR, 
		 CUSTOMER_NAME  VARCHAR, 
		 F_PACK_QTY  DECIMAL, 
		 F_PACK_WEIGHT  DECIMAL, 
		 F_PACK_FACTORY_ORDER_NUM  VARCHAR, 
		 M_PACK_QTY  DECIMAL, 
		 M_PACK_WEIGHT  DECIMAL, 
		 M_PACK_FACTORY_ORDER_NUM  VARCHAR, 
		 FINISHING_SHUNT_FLAG  VARCHAR, 
		 D_USER_NUM  VARCHAR, 
		 D_USER_NAME  VARCHAR, 
		 ON_WAY_FLAG  VARCHAR, 
		 PUTOUT_ID  VARCHAR, 
		 ENTITY_PUTOUT_DATE  VARCHAR, 
		 PERIOD_ID  VARCHAR, 
		 TEAM_ID  VARCHAR, 
		 WORKING_SHIFT  VARCHAR, 
		 TEAM_REPORT_ID  VARCHAR, 
		 PROXY_TYPE  VARCHAR, 
		 IF_UPLOAD_FINANCE  VARCHAR, 
		 TRADE_CODE  VARCHAR, 
		 QUALITY_GRADE  VARCHAR, 
		 QUALITY_STATUS  VARCHAR, 
		 PACKING_TYPE_CODE  VARCHAR, 
		 QUANTITY_UNIT  VARCHAR, 
		 WEIGHT_UNIT  VARCHAR, 
		 PROD_TYPE_ID  VARCHAR, 
		 AUDITOR_ID  VARCHAR, 
		 AUDITOR_NAME  VARCHAR, 
		 AUDIT_TIME  VARCHAR, 
		 AUDIT_STATUS  VARCHAR, 
		 AUDIT_OPNION  VARCHAR, 
		 PROD_TYPE_DESC  VARCHAR, 
		 SCRAP_TYPE  VARCHAR, 
		 MATERIAL_RACK_ID  VARCHAR, 
		 GROSS_WEIGHT  DECIMAL, 
		 WL_ACCOUNT_MARK  VARCHAR, 
		 CL_ACCOUNT_MARK  VARCHAR, 
		 LABEL_ID  VARCHAR, 
		 FIRST_PUTIN_DATE  VARCHAR, 
		 TRANSFER_FLAG  VARCHAR, 
		 TECH_STANDARD  VARCHAR, 
		 PACK_TRANSFER_FLAG  VARCHAR, 
		 MANUAL_NO  VARCHAR, 
		 HS_ID  VARCHAR, 
		 PROCESS_CONSIGN_UNIT  VARCHAR, 
		 ORIGINAL_PACK_ID  VARCHAR, 
		 APPR_STATUS  VARCHAR, 
		 PROCESS_ID  VARCHAR, 
		 HANDBOOK_ID  VARCHAR, 
		 M_HANDBOOK_ID  VARCHAR, 
		 F_HANDBOOK_ID  VARCHAR, 
		 CUSTOMS_PRODUCT_NUM  VARCHAR, 
		 SURFACE_GRADE  VARCHAR, 
		 FIN_USER_ID  VARCHAR, 
		 FIN_USER_NAME  VARCHAR, 
		 CUST_PART_ID  VARCHAR, 
		 CUST_PART_NAME  VARCHAR, 
		 PRODUCING_AREA  VARCHAR, 
		 MAKER_NUM  VARCHAR, 
		 MAKER_NAME  VARCHAR, 
		 PROVIDER_CODE  VARCHAR, 
		 PROVIDER_CNAME  VARCHAR, 
		 CUST_PROVIDER_ID  VARCHAR, 
		 PRODUCT_DESC  VARCHAR, 
		 BIG_TYPE_DESC  VARCHAR, 
		 ACTUAL_MATERIAL_WEIGHT  DECIMAL, 
		 IF_SYNCHRONIZATION_WL  VARCHAR, 
		 WMS_SEND_FLAG  VARCHAR, 
		 WMS_CX_FLAG  VARCHAR, 
		 PROPERTY_RESULT  VARCHAR, 
		 JP_SIGN  VARCHAR   NOT NULL, 
		 JP_EMBARKATION_SIGN  VARCHAR   NOT NULL, 
		 PARTS_CODE  VARCHAR, 
		 PARTS_NAME  VARCHAR, 
		 CAR_TYPE_CODE  VARCHAR, 
		 VEHICLE_NAME  VARCHAR, 
		 QUOTA  DECIMAL, 
		 JP_DEBATCHING_SIGN  VARCHAR, 
		 JP_FINISHED_PRODUCT_SIGN  VARCHAR, 
		 PRINT_BATCH_ID  VARCHAR
	-->
<sqlMap namespace="VIPM0008">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.vi.pm.domain.VIPM0008">
		SELECT
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				PROCESS_ORDER_ID	as "processOrderId",  <!-- 生产工单号 -->
				PROCESS_DEMAND_ID	as "processDemandId",  <!-- 生产需求单号 -->
				PROCESS_DEMAND_SUB_ID	as "processDemandSubId",  <!-- 生产需求单子项号 -->
				PART_ID	as "partId",  <!-- 物料号 -->
				SPECS_DESC	as "specsDesc",  <!-- 规格描述 -->
				SHOPSIGN	as "shopsign",  <!-- 牌号 -->
				PACK_ID	as "packId",  <!-- 捆包号 -->
				MAT_INNER_ID	as "matInnerId",  <!-- 材料管理号 -->
				PACK_QTY	as "packQty",  <!-- 捆包数量 -->
				NET_WEIGHT	as "netWeight",  <!-- 净重 -->
				PROD_NAME_CODE	as "prodNameCode",  <!-- 品名代码 -->
				PROD_CNAME	as "prodCname",  <!-- 品名名称 -->
				FACTORY_ORDER_NUM	as "factoryOrderNum",  <!-- 钢厂订单号 -->
				PROCESS_DEMAND_MATERAIL_STATUS	as "processDemandMaterailStatus",  <!-- 生产需求单投料捆包状态 -->
				VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单 -->
				AUDIT_FLAG	as "auditFlag",  <!-- 审核标记 -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
				LOCATION_ID	as "locationId",  <!-- 库位代码 -->
				LOCATION_NAME	as "locationName",  <!-- 库位名称 -->
				F_PACK_ID	as "f_packId",  <!-- 父捆包号 -->
				F_MAT_INNER_ID	as "f_matInnerId",  <!-- 父捆包号材料管理号 -->
				M_PACK_ID	as "m_packId",  <!-- 母捆包号 -->
				M_MAT_INNER_ID	as "m_matInnerId",  <!-- 母捆包号管理号 -->
				PACK_STATUS	as "packStatus",  <!-- 捆包状态 -->
				YIELD	as "yield",  <!-- 成材率 -->
				PRODUCING_AREA_DESC	as "producingAreaDesc",  <!-- 产地名称 -->
				REMARK	as "remark",  <!-- 备注 -->
				MATERIAL_LOCK	as "materialLock",  <!-- 原料封锁标记 -->
				HEAT_NUM	as "heatNum",  <!-- 炉号 -->
				CHARGE_PLAT	as "chargePlat",  <!-- 料台号 -->
				CAN_RETURN_MATERIAL	as "canReturnMaterial",  <!-- 是否可以退料 -->
				SORT_ID	as "sortId",  <!-- 顺序号 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				PROCESS_DEMAND_MATERAIL_ID	as "processDemandMaterailId",  <!-- 生产需求投料捆包表单据号 -->
				SEG_NO	as "segNo",  <!-- 业务账套 -->
				UUID	as "uuid",  <!-- ID -->
				PROD_CODE	as "prodCode",  <!-- 品种代码 -->
				STORE_TYPE	as "storeType",  <!-- 存货性质 -->
				ORDER_TYPE_CODE	as "orderTypeCode",  <!-- 订单性质代码(是否QZP) -->
				CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
				CUSTOMER_NAME	as "customerName",  <!-- 客户名称 -->
				F_PACK_QTY	as "f_packQty",  <!-- 父捆包件数 -->
				F_PACK_WEIGHT	as "f_packWeight",  <!-- 父捆包重量 -->
				F_PACK_FACTORY_ORDER_NUM	as "f_packFactoryOrderNum",  <!-- 父捆包钢厂订单号 -->
				M_PACK_QTY	as "m_packQty",  <!-- 母捆包件数 -->
				M_PACK_WEIGHT	as "m_packWeight",  <!-- 母捆包重量 -->
				M_PACK_FACTORY_ORDER_NUM	as "m_packFactoryOrderNum",  <!-- 母捆包钢厂订单号 -->
				FINISHING_SHUNT_FLAG	as "finishingShuntFlag",  <!-- 精整分流标记 -->
				D_USER_NUM	as "d_userNum",  <!-- 分户号 -->
				D_USER_NAME	as "d_userName",  <!-- 分户号简称 -->
				ON_WAY_FLAG	as "onWayFlag",  <!-- 在途标记 -->
				PUTOUT_ID	as "putoutId",  <!-- 出库单号 -->
				ENTITY_PUTOUT_DATE	as "entityPutoutDate",  <!-- 实物出库时间 -->
				PERIOD_ID	as "periodId",  <!-- 会计期间 -->
				TEAM_ID	as "teamId",  <!-- 班组 -->
				WORKING_SHIFT	as "workingShift",  <!-- 班次 -->
				TEAM_REPORT_ID	as "teamReportId",  <!-- 班报序号 -->
				PROXY_TYPE	as "proxyType",  <!-- 类型 -->
				IF_UPLOAD_FINANCE	as "ifUploadFinance",  <!-- 是否上传财务 -->
				TRADE_CODE	as "tradeCode",  <!-- 贸易方式 -->
				QUALITY_GRADE	as "qualityGrade",  <!-- 质量等级 -->
				QUALITY_STATUS	as "qualityStatus",  <!-- 质量状态 -->
				PACKING_TYPE_CODE	as "packingTypeCode",  <!-- 包装方式代码 -->
				QUANTITY_UNIT	as "quantityUnit",  <!-- 数量单位 -->
				WEIGHT_UNIT	as "weightUnit",  <!-- 重量单位 -->
				PROD_TYPE_ID	as "prodTypeId",  <!-- 品种附属码 -->
				AUDITOR_ID	as "auditorId",  <!-- 审核人工号 -->
				AUDITOR_NAME	as "auditorName",  <!-- 审核人姓名 -->
				AUDIT_TIME	as "auditTime",  <!-- 审核时间 -->
				AUDIT_STATUS	as "auditStatus",  <!-- 审核状态 -->
				AUDIT_OPNION	as "auditOpnion",  <!-- 审核意见 -->
				PROD_TYPE_DESC	as "prodTypeDesc",  <!-- 品种附属码描述 -->
				SCRAP_TYPE	as "scrapType",  <!-- 利用材种类 -->
				MATERIAL_RACK_ID	as "materialRackId",  <!-- 料架号 -->
				GROSS_WEIGHT	as "grossWeight",  <!-- 毛重 -->
				WL_ACCOUNT_MARK	as "wlAccountMark",  <!-- 物流对账标记 -->
				CL_ACCOUNT_MARK	as "clAccountMark",  <!-- 财务对账标记 -->
				LABEL_ID	as "labelId",  <!-- 标签号 -->
				FIRST_PUTIN_DATE	as "firstPutinDate",  <!-- 最初入库时间 -->
				TRANSFER_FLAG	as "transferFlag",  <!-- 转换标记 -->
				TECH_STANDARD	as "techStandard",  <!-- 技术标准 -->
				PACK_TRANSFER_FLAG	as "packTransferFlag",  <!-- 捆包转移标记 -->
				MANUAL_NO	as "manualNo",  <!-- 手册编号 -->
				HS_ID	as "hsId",  <!-- 海关HS系统编码 -->
				PROCESS_CONSIGN_UNIT	as "processConsignUnit",  <!-- 加工委托方（股份委托加工来源） -->
				ORIGINAL_PACK_ID	as "originalPackId",  <!-- 原始捆包号 -->
				APPR_STATUS	as "apprStatus",  <!-- 审批状态 -->
				PROCESS_ID	as "processId",  <!-- 流程ID -->
				HANDBOOK_ID	as "handbookId",  <!-- 手册系统编号 -->
				M_HANDBOOK_ID	as "m_handbookId",  <!-- 母手册号(最初手册号) -->
				F_HANDBOOK_ID	as "f_handbookId",  <!-- 父手册号(上层手册号) -->
				CUSTOMS_PRODUCT_NUM	as "customsProductNum",  
				SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
				FIN_USER_ID	as "finUserId",  <!-- 最终用户代码 -->
				FIN_USER_NAME	as "finUserName",  <!-- 最终用户名称 -->
				CUST_PART_ID	as "custPartId",  <!-- 客户零件号 -->
				CUST_PART_NAME	as "custPartName",  <!-- 客户零件号名称 -->
				PRODUCING_AREA	as "producingArea",  <!-- 产地 -->
				MAKER_NUM	as "makerNum",  <!-- 制造商代码 -->
				MAKER_NAME	as "makerName",  <!-- 制造商名称 -->
				PROVIDER_CODE	as "providerCode",  <!-- 供应商代码 -->
				PROVIDER_CNAME	as "providerCname",  <!-- 供应商名称 -->
				CUST_PROVIDER_ID	as "custProviderId",  <!-- 客户供料商代码 -->
				PRODUCT_DESC	as "productDesc",  <!-- 产品描述 -->
				BIG_TYPE_DESC	as "bigTypeDesc",  <!-- 利用材种类描述 -->
				ACTUAL_MATERIAL_WEIGHT	as "actualMaterialWeight",  <!-- 实绩领料重量 -->
				IF_SYNCHRONIZATION_WL	as "ifSynchronizationWl",  <!-- 是否同步物流标记 -->
				WMS_SEND_FLAG	as "wmsSendFlag",  <!-- 立体库WMS发送标记 1表示已发送，其他表示未发送 -->
				WMS_CX_FLAG	as "wmsCxFlag",  <!-- 立体库WMS发送撤销标记 1表示已发送，其他表示未发送 -->
				PROPERTY_RESULT	as "propertyResult",  <!-- 性能判定结果(1合格;2不合格;0或者空表示未判定) -->
				JP_SIGN	as "jpSign",  <!-- 精品标记 -->
				JP_EMBARKATION_SIGN	as "jpEmbarkationSign",  <!-- 精品上机标记 -->
				PARTS_CODE	as "partsCode",  <!-- 部件编码 -->
				PARTS_NAME	as "partsName",  <!-- 部品名称 -->
				CAR_TYPE_CODE	as "carTypeCode",  <!-- 车型代码 -->
				VEHICLE_NAME	as "vehicleName",  <!-- 车型名称 -->
				QUOTA	as "quota",  <!-- 定额(KG) -->
				JP_DEBATCHING_SIGN	as "jpDebatchingSign",  <!-- 精品退料标记 -->
				JP_FINISHED_PRODUCT_SIGN	as "jpFinishedProductSign",  <!-- 精品成品标记 -->
				PRINT_BATCH_ID	as "printBatchId" <!-- 打印批次号 -->
		FROM ${meviSchema}.TVIPM0008 WHERE 1=1
		and DEL_FLAG = 0
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderIdLike">
			PROCESS_ORDER_ID LIKE '%$processOrderIdLike$%'
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meviSchema}.TVIPM0008 WHERE 1=1
		and DEL_FLAG = 0
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandId">
			PROCESS_DEMAND_ID = #processDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandSubId">
			PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="partId">
			PART_ID = #partId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specsDesc">
			SPECS_DESC = #specsDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="shopsign">
			SHOPSIGN = #shopsign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packQty">
			PACK_QTY = #packQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="netWeight">
			NET_WEIGHT = #netWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodNameCode">
			PROD_NAME_CODE = #prodNameCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodCname">
			PROD_CNAME = #prodCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryOrderNum">
			FACTORY_ORDER_NUM = #factoryOrderNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandMaterailStatus">
			PROCESS_DEMAND_MATERAIL_STATUS = #processDemandMaterailStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditFlag">
			AUDIT_FLAG = #auditFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationId">
			LOCATION_ID = #locationId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationName">
			LOCATION_NAME = #locationName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_packId">
			F_PACK_ID = #f_packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_matInnerId">
			F_MAT_INNER_ID = #f_matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_packId">
			M_PACK_ID = #m_packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_matInnerId">
			M_MAT_INNER_ID = #m_matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packStatus">
			PACK_STATUS = #packStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="yield">
			YIELD = #yield#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="producingAreaDesc">
			PRODUCING_AREA_DESC = #producingAreaDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="materialLock">
			MATERIAL_LOCK = #materialLock#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="heatNum">
			HEAT_NUM = #heatNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="chargePlat">
			CHARGE_PLAT = #chargePlat#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="canReturnMaterial">
			CAN_RETURN_MATERIAL = #canReturnMaterial#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sortId">
			SORT_ID = #sortId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandMaterailId">
			PROCESS_DEMAND_MATERAIL_ID = #processDemandMaterailId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodCode">
			PROD_CODE = #prodCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="storeType">
			STORE_TYPE = #storeType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orderTypeCode">
			ORDER_TYPE_CODE = #orderTypeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_packQty">
			F_PACK_QTY = #f_packQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_packWeight">
			F_PACK_WEIGHT = #f_packWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_packFactoryOrderNum">
			F_PACK_FACTORY_ORDER_NUM = #f_packFactoryOrderNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_packQty">
			M_PACK_QTY = #m_packQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_packWeight">
			M_PACK_WEIGHT = #m_packWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_packFactoryOrderNum">
			M_PACK_FACTORY_ORDER_NUM = #m_packFactoryOrderNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finishingShuntFlag">
			FINISHING_SHUNT_FLAG = #finishingShuntFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="d_userNum">
			D_USER_NUM = #d_userNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="d_userName">
			D_USER_NAME = #d_userName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="onWayFlag">
			ON_WAY_FLAG = #onWayFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="putoutId">
			PUTOUT_ID = #putoutId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="entityPutoutDate">
			ENTITY_PUTOUT_DATE = #entityPutoutDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="periodId">
			PERIOD_ID = #periodId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="teamId">
			TEAM_ID = #teamId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="workingShift">
			WORKING_SHIFT = #workingShift#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="teamReportId">
			TEAM_REPORT_ID = #teamReportId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="proxyType">
			PROXY_TYPE = #proxyType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifUploadFinance">
			IF_UPLOAD_FINANCE = #ifUploadFinance#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tradeCode">
			TRADE_CODE = #tradeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="qualityGrade">
			QUALITY_GRADE = #qualityGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="qualityStatus">
			QUALITY_STATUS = #qualityStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packingTypeCode">
			PACKING_TYPE_CODE = #packingTypeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="quantityUnit">
			QUANTITY_UNIT = #quantityUnit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="weightUnit">
			WEIGHT_UNIT = #weightUnit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeId">
			PROD_TYPE_ID = #prodTypeId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditorId">
			AUDITOR_ID = #auditorId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditorName">
			AUDITOR_NAME = #auditorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditTime">
			AUDIT_TIME = #auditTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditStatus">
			AUDIT_STATUS = #auditStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditOpnion">
			AUDIT_OPNION = #auditOpnion#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeDesc">
			PROD_TYPE_DESC = #prodTypeDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scrapType">
			SCRAP_TYPE = #scrapType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="materialRackId">
			MATERIAL_RACK_ID = #materialRackId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="grossWeight">
			GROSS_WEIGHT = #grossWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="wlAccountMark">
			WL_ACCOUNT_MARK = #wlAccountMark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="clAccountMark">
			CL_ACCOUNT_MARK = #clAccountMark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="labelId">
			LABEL_ID = #labelId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="firstPutinDate">
			FIRST_PUTIN_DATE = #firstPutinDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="transferFlag">
			TRANSFER_FLAG = #transferFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="techStandard">
			TECH_STANDARD = #techStandard#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packTransferFlag">
			PACK_TRANSFER_FLAG = #packTransferFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="manualNo">
			MANUAL_NO = #manualNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="hsId">
			HS_ID = #hsId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processConsignUnit">
			PROCESS_CONSIGN_UNIT = #processConsignUnit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="originalPackId">
			ORIGINAL_PACK_ID = #originalPackId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="apprStatus">
			APPR_STATUS = #apprStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processId">
			PROCESS_ID = #processId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handbookId">
			HANDBOOK_ID = #handbookId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="m_handbookId">
			M_HANDBOOK_ID = #m_handbookId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="f_handbookId">
			F_HANDBOOK_ID = #f_handbookId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customsProductNum">
			CUSTOMS_PRODUCT_NUM = #customsProductNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="surfaceGrade">
			SURFACE_GRADE = #surfaceGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserId">
			FIN_USER_ID = #finUserId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserName">
			FIN_USER_NAME = #finUserName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="custPartId">
			CUST_PART_ID = #custPartId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="custPartName">
			CUST_PART_NAME = #custPartName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="producingArea">
			PRODUCING_AREA = #producingArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="makerNum">
			MAKER_NUM = #makerNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="makerName">
			MAKER_NAME = #makerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="providerCode">
			PROVIDER_CODE = #providerCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="providerCname">
			PROVIDER_CNAME = #providerCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="custProviderId">
			CUST_PROVIDER_ID = #custProviderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productDesc">
			PRODUCT_DESC = #productDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="bigTypeDesc">
			BIG_TYPE_DESC = #bigTypeDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="actualMaterialWeight">
			ACTUAL_MATERIAL_WEIGHT = #actualMaterialWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifSynchronizationWl">
			IF_SYNCHRONIZATION_WL = #ifSynchronizationWl#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="wmsSendFlag">
			WMS_SEND_FLAG = #wmsSendFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="wmsCxFlag">
			WMS_CX_FLAG = #wmsCxFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="propertyResult">
			PROPERTY_RESULT = #propertyResult#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jpSign">
			JP_SIGN = #jpSign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jpEmbarkationSign">
			JP_EMBARKATION_SIGN = #jpEmbarkationSign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="partsCode">
			PARTS_CODE = #partsCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="partsName">
			PARTS_NAME = #partsName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTypeCode">
			CAR_TYPE_CODE = #carTypeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleName">
			VEHICLE_NAME = #vehicleName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="quota">
			QUOTA = #quota#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jpDebatchingSign">
			JP_DEBATCHING_SIGN = #jpDebatchingSign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jpFinishedProductSign">
			JP_FINISHED_PRODUCT_SIGN = #jpFinishedProductSign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printBatchId">
			PRINT_BATCH_ID = #printBatchId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meviSchema}.TVIPM0008 (UNIT_CODE,  <!-- 业务单元代码 -->
										PROCESS_ORDER_ID,  <!-- 生产工单号 -->
										PROCESS_DEMAND_ID,  <!-- 生产需求单号 -->
										PROCESS_DEMAND_SUB_ID,  <!-- 生产需求单子项号 -->
										PART_ID,  <!-- 物料号 -->
										SPECS_DESC,  <!-- 规格描述 -->
										SHOPSIGN,  <!-- 牌号 -->
										PACK_ID,  <!-- 捆包号 -->
										MAT_INNER_ID,  <!-- 材料管理号 -->
										PACK_QTY,  <!-- 捆包数量 -->
										NET_WEIGHT,  <!-- 净重 -->
										PROD_NAME_CODE,  <!-- 品名代码 -->
										PROD_CNAME,  <!-- 品名名称 -->
										FACTORY_ORDER_NUM,  <!-- 钢厂订单号 -->
										PROCESS_DEMAND_MATERAIL_STATUS,  <!-- 生产需求单投料捆包状态 -->
										VOUCHER_NUM,  <!-- 依据凭单 -->
										AUDIT_FLAG,  <!-- 审核标记 -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										WAREHOUSE_NAME,  <!-- 仓库名称 -->
										LOCATION_ID,  <!-- 库位代码 -->
										LOCATION_NAME,  <!-- 库位名称 -->
										F_PACK_ID,  <!-- 父捆包号 -->
										F_MAT_INNER_ID,  <!-- 父捆包号材料管理号 -->
										M_PACK_ID,  <!-- 母捆包号 -->
										M_MAT_INNER_ID,  <!-- 母捆包号管理号 -->
										PACK_STATUS,  <!-- 捆包状态 -->
										YIELD,  <!-- 成材率 -->
										PRODUCING_AREA_DESC,  <!-- 产地名称 -->
										REMARK,  <!-- 备注 -->
										MATERIAL_LOCK,  <!-- 原料封锁标记 -->
										HEAT_NUM,  <!-- 炉号 -->
										CHARGE_PLAT,  <!-- 料台号 -->
										CAN_RETURN_MATERIAL,  <!-- 是否可以退料 -->
										SORT_ID,  <!-- 顺序号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										TENANT_USER,  <!-- 租户 -->
										PROCESS_DEMAND_MATERAIL_ID,  <!-- 生产需求投料捆包表单据号 -->
										SEG_NO,  <!-- 业务账套 -->
										UUID,  <!-- ID -->
										PROD_CODE,  <!-- 品种代码 -->
										STORE_TYPE,  <!-- 存货性质 -->
										ORDER_TYPE_CODE,  <!-- 订单性质代码(是否QZP) -->
										CUSTOMER_ID,  <!-- 客户代码 -->
										CUSTOMER_NAME,  <!-- 客户名称 -->
										F_PACK_QTY,  <!-- 父捆包件数 -->
										F_PACK_WEIGHT,  <!-- 父捆包重量 -->
										F_PACK_FACTORY_ORDER_NUM,  <!-- 父捆包钢厂订单号 -->
										M_PACK_QTY,  <!-- 母捆包件数 -->
										M_PACK_WEIGHT,  <!-- 母捆包重量 -->
										M_PACK_FACTORY_ORDER_NUM,  <!-- 母捆包钢厂订单号 -->
										FINISHING_SHUNT_FLAG,  <!-- 精整分流标记 -->
										D_USER_NUM,  <!-- 分户号 -->
										D_USER_NAME,  <!-- 分户号简称 -->
										ON_WAY_FLAG,  <!-- 在途标记 -->
										PUTOUT_ID,  <!-- 出库单号 -->
										ENTITY_PUTOUT_DATE,  <!-- 实物出库时间 -->
										PERIOD_ID,  <!-- 会计期间 -->
										TEAM_ID,  <!-- 班组 -->
										WORKING_SHIFT,  <!-- 班次 -->
										TEAM_REPORT_ID,  <!-- 班报序号 -->
										PROXY_TYPE,  <!-- 类型 -->
										IF_UPLOAD_FINANCE,  <!-- 是否上传财务 -->
										TRADE_CODE,  <!-- 贸易方式 -->
										QUALITY_GRADE,  <!-- 质量等级 -->
										QUALITY_STATUS,  <!-- 质量状态 -->
										PACKING_TYPE_CODE,  <!-- 包装方式代码 -->
										QUANTITY_UNIT,  <!-- 数量单位 -->
										WEIGHT_UNIT,  <!-- 重量单位 -->
										PROD_TYPE_ID,  <!-- 品种附属码 -->
										AUDITOR_ID,  <!-- 审核人工号 -->
										AUDITOR_NAME,  <!-- 审核人姓名 -->
										AUDIT_TIME,  <!-- 审核时间 -->
										AUDIT_STATUS,  <!-- 审核状态 -->
										AUDIT_OPNION,  <!-- 审核意见 -->
										PROD_TYPE_DESC,  <!-- 品种附属码描述 -->
										SCRAP_TYPE,  <!-- 利用材种类 -->
										MATERIAL_RACK_ID,  <!-- 料架号 -->
										GROSS_WEIGHT,  <!-- 毛重 -->
										WL_ACCOUNT_MARK,  <!-- 物流对账标记 -->
										CL_ACCOUNT_MARK,  <!-- 财务对账标记 -->
										LABEL_ID,  <!-- 标签号 -->
										FIRST_PUTIN_DATE,  <!-- 最初入库时间 -->
										TRANSFER_FLAG,  <!-- 转换标记 -->
										TECH_STANDARD,  <!-- 技术标准 -->
										PACK_TRANSFER_FLAG,  <!-- 捆包转移标记 -->
										MANUAL_NO,  <!-- 手册编号 -->
										HS_ID,  <!-- 海关HS系统编码 -->
										PROCESS_CONSIGN_UNIT,  <!-- 加工委托方（股份委托加工来源） -->
										ORIGINAL_PACK_ID,  <!-- 原始捆包号 -->
										APPR_STATUS,  <!-- 审批状态 -->
										PROCESS_ID,  <!-- 流程ID -->
										HANDBOOK_ID,  <!-- 手册系统编号 -->
										M_HANDBOOK_ID,  <!-- 母手册号(最初手册号) -->
										F_HANDBOOK_ID,  <!-- 父手册号(上层手册号) -->
										CUSTOMS_PRODUCT_NUM,
										SURFACE_GRADE,  <!-- 表面等级 -->
										FIN_USER_ID,  <!-- 最终用户代码 -->
										FIN_USER_NAME,  <!-- 最终用户名称 -->
										CUST_PART_ID,  <!-- 客户零件号 -->
										CUST_PART_NAME,  <!-- 客户零件号名称 -->
										PRODUCING_AREA,  <!-- 产地 -->
										MAKER_NUM,  <!-- 制造商代码 -->
										MAKER_NAME,  <!-- 制造商名称 -->
										PROVIDER_CODE,  <!-- 供应商代码 -->
										PROVIDER_CNAME,  <!-- 供应商名称 -->
										CUST_PROVIDER_ID,  <!-- 客户供料商代码 -->
										PRODUCT_DESC,  <!-- 产品描述 -->
										BIG_TYPE_DESC,  <!-- 利用材种类描述 -->
										ACTUAL_MATERIAL_WEIGHT,  <!-- 实绩领料重量 -->
										IF_SYNCHRONIZATION_WL,  <!-- 是否同步物流标记 -->
										WMS_SEND_FLAG,  <!-- 立体库WMS发送标记 1表示已发送，其他表示未发送 -->
										WMS_CX_FLAG,  <!-- 立体库WMS发送撤销标记 1表示已发送，其他表示未发送 -->
										PROPERTY_RESULT,  <!-- 性能判定结果(1合格;2不合格;0或者空表示未判定) -->
										JP_SIGN,  <!-- 精品标记 -->
										JP_EMBARKATION_SIGN,  <!-- 精品上机标记 -->
										PARTS_CODE,  <!-- 部件编码 -->
										PARTS_NAME,  <!-- 部品名称 -->
										CAR_TYPE_CODE,  <!-- 车型代码 -->
										VEHICLE_NAME,  <!-- 车型名称 -->
										QUOTA,  <!-- 定额(KG) -->
										JP_DEBATCHING_SIGN,  <!-- 精品退料标记 -->
										JP_FINISHED_PRODUCT_SIGN,  <!-- 精品成品标记 -->
										PRINT_BATCH_ID  <!-- 打印批次号 -->
										)		 
	    VALUES (#unitCode:VARCHAR#, #processOrderId:VARCHAR#, #processDemandId:VARCHAR#, #processDemandSubId:VARCHAR#, #partId:VARCHAR#, #specsDesc:VARCHAR#, #shopsign:VARCHAR#, #packId:VARCHAR#, #matInnerId:VARCHAR#, #packQty#, #netWeight:NUMERIC#, #prodNameCode:VARCHAR#, #prodCname:VARCHAR#, #factoryOrderNum:VARCHAR#, #processDemandMaterailStatus:VARCHAR#, #voucherNum:VARCHAR#, #auditFlag:VARCHAR#, #warehouseCode:VARCHAR#, #warehouseName:VARCHAR#, #locationId:VARCHAR#, #locationName:VARCHAR#, #f_packId:VARCHAR#, #f_matInnerId:VARCHAR#, #m_packId:VARCHAR#, #m_matInnerId:VARCHAR#, #packStatus:VARCHAR#, #yield:NUMERIC#, #producingAreaDesc:VARCHAR#, #remark:VARCHAR#, #materialLock:VARCHAR#, #heatNum:VARCHAR#, #chargePlat:VARCHAR#, #canReturnMaterial:VARCHAR#, #sortId:VARCHAR#, #recCreator:VARCHAR#, #recCreatorName:VARCHAR#, #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recRevisorName:VARCHAR#, #recReviseTime:VARCHAR#, #archiveFlag:VARCHAR#, #delFlag#, #tenantUser:VARCHAR#, #processDemandMaterailId:VARCHAR#, #segNo:VARCHAR#, #uuid:VARCHAR#, #prodCode:VARCHAR#, #storeType:VARCHAR#, #orderTypeCode:VARCHAR#, #customerId:VARCHAR#, #customerName:VARCHAR#, #f_packQty:NUMERIC#, #f_packWeight:NUMERIC#, #f_packFactoryOrderNum:VARCHAR#, #m_packQty:NUMERIC#, #m_packWeight:NUMERIC#, #m_packFactoryOrderNum:VARCHAR#, #finishingShuntFlag:VARCHAR#, #d_userNum:VARCHAR#, #d_userName:VARCHAR#, #onWayFlag:VARCHAR#, #putoutId:VARCHAR#, #entityPutoutDate:VARCHAR#, #periodId:VARCHAR#, #teamId:VARCHAR#, #workingShift:VARCHAR#, #teamReportId:VARCHAR#, #proxyType:VARCHAR#, #ifUploadFinance:VARCHAR#, #tradeCode:VARCHAR#, #qualityGrade:VARCHAR#, #qualityStatus:VARCHAR#, #packingTypeCode:VARCHAR#, #quantityUnit:VARCHAR#, #weightUnit:VARCHAR#, #prodTypeId:VARCHAR#, #auditorId:VARCHAR#, #auditorName:VARCHAR#, #auditTime:VARCHAR#, #auditStatus:VARCHAR#, #auditOpnion:VARCHAR#, #prodTypeDesc:VARCHAR#, #scrapType:VARCHAR#, #materialRackId:VARCHAR#, #grossWeight:NUMERIC#, #wlAccountMark:VARCHAR#, #clAccountMark:VARCHAR#, #labelId:VARCHAR#, #firstPutinDate:VARCHAR#, #transferFlag:VARCHAR#, #techStandard:VARCHAR#, #packTransferFlag:VARCHAR#, #manualNo:VARCHAR#, #hsId:VARCHAR#, #processConsignUnit:VARCHAR#, #originalPackId:VARCHAR#, #apprStatus:VARCHAR#, #processId:VARCHAR#, #handbookId:VARCHAR#, #m_handbookId:VARCHAR#, #f_handbookId:VARCHAR#, #customsProductNum:VARCHAR#, #surfaceGrade:VARCHAR#, #finUserId:VARCHAR#, #finUserName:VARCHAR#, #custPartId:VARCHAR#, #custPartName:VARCHAR#, #producingArea:VARCHAR#, #makerNum:VARCHAR#, #makerName:VARCHAR#, #providerCode:VARCHAR#, #providerCname:VARCHAR#, #custProviderId:VARCHAR#, #productDesc:VARCHAR#, #bigTypeDesc:VARCHAR#, #actualMaterialWeight:NUMERIC#, #ifSynchronizationWl:VARCHAR#, #wmsSendFlag:VARCHAR#, #wmsCxFlag:VARCHAR#, #propertyResult:VARCHAR#, #jpSign:VARCHAR#, #jpEmbarkationSign:VARCHAR#, #partsCode:VARCHAR#, #partsName:VARCHAR#, #carTypeCode:VARCHAR#, #vehicleName:VARCHAR#, #quota:NUMERIC#, #jpDebatchingSign:VARCHAR#, #jpFinishedProductSign:VARCHAR#, #printBatchId:VARCHAR#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meviSchema}.TVIPM0008
		WHERE
		SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandSubId">
			PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>

	</delete>

	<update id="update">
		UPDATE ${meviSchema}.TVIPM0008
		SET 
		UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					PROCESS_ORDER_ID	= #processOrderId#,   <!-- 生产工单号 -->
					PROCESS_DEMAND_ID	= #processDemandId#,   <!-- 生产需求单号 -->
					PROCESS_DEMAND_SUB_ID	= #processDemandSubId#,   <!-- 生产需求单子项号 -->
					PART_ID	= #partId#,   <!-- 物料号 -->
					SPECS_DESC	= #specsDesc#,   <!-- 规格描述 -->
					SHOPSIGN	= #shopsign#,   <!-- 牌号 -->
					PACK_ID	= #packId#,   <!-- 捆包号 -->
					MAT_INNER_ID	= #matInnerId#,   <!-- 材料管理号 -->
					PACK_QTY	= #packQty#,   <!-- 捆包数量 -->
					NET_WEIGHT	= #netWeight:NUMERIC#,   <!-- 净重 -->
					PROD_NAME_CODE	= #prodNameCode#,   <!-- 品名代码 -->
					PROD_CNAME	= #prodCname#,   <!-- 品名名称 -->
					FACTORY_ORDER_NUM	= #factoryOrderNum#,   <!-- 钢厂订单号 -->
					PROCESS_DEMAND_MATERAIL_STATUS	= #processDemandMaterailStatus#,   <!-- 生产需求单投料捆包状态 -->
					VOUCHER_NUM	= #voucherNum#,   <!-- 依据凭单 -->
					AUDIT_FLAG	= #auditFlag#,   <!-- 审核标记 -->
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库代码 -->
					WAREHOUSE_NAME	= #warehouseName#,   <!-- 仓库名称 -->
					LOCATION_ID	= #locationId#,   <!-- 库位代码 -->
					LOCATION_NAME	= #locationName#,   <!-- 库位名称 -->
					F_PACK_ID	= #f_packId#,   <!-- 父捆包号 -->
					F_MAT_INNER_ID	= #f_matInnerId#,   <!-- 父捆包号材料管理号 -->
					M_PACK_ID	= #m_packId#,   <!-- 母捆包号 -->
					M_MAT_INNER_ID	= #m_matInnerId#,   <!-- 母捆包号管理号 -->
					PACK_STATUS	= #packStatus#,   <!-- 捆包状态 -->
					YIELD	= #yield:NUMERIC#,   <!-- 成材率 -->
					PRODUCING_AREA_DESC	= #producingAreaDesc#,   <!-- 产地名称 -->
					REMARK	= #remark#,   <!-- 备注 -->
					MATERIAL_LOCK	= #materialLock#,   <!-- 原料封锁标记 -->
					HEAT_NUM	= #heatNum#,   <!-- 炉号 -->
					CHARGE_PLAT	= #chargePlat#,   <!-- 料台号 -->
					CAN_RETURN_MATERIAL	= #canReturnMaterial#,   <!-- 是否可以退料 -->
					SORT_ID	= #sortId#,   <!-- 顺序号 -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->
					PROCESS_DEMAND_MATERAIL_ID	= #processDemandMaterailId#,   <!-- 生产需求投料捆包表单据号 -->
					SEG_NO	= #segNo#,   <!-- 业务账套 -->
								PROD_CODE	= #prodCode#,   <!-- 品种代码 -->
					STORE_TYPE	= #storeType#,   <!-- 存货性质 -->
					ORDER_TYPE_CODE	= #orderTypeCode#,   <!-- 订单性质代码(是否QZP) -->
					CUSTOMER_ID	= #customerId#,   <!-- 客户代码 -->
					CUSTOMER_NAME	= #customerName#,   <!-- 客户名称 -->
					F_PACK_QTY	= #f_packQty:NUMERIC#,   <!-- 父捆包件数 -->
					F_PACK_WEIGHT	= #f_packWeight:NUMERIC#,   <!-- 父捆包重量 -->
					F_PACK_FACTORY_ORDER_NUM	= #f_packFactoryOrderNum#,   <!-- 父捆包钢厂订单号 -->
					M_PACK_QTY	= #m_packQty:NUMERIC#,   <!-- 母捆包件数 -->
					M_PACK_WEIGHT	= #m_packWeight:NUMERIC#,   <!-- 母捆包重量 -->
					M_PACK_FACTORY_ORDER_NUM	= #m_packFactoryOrderNum#,   <!-- 母捆包钢厂订单号 -->
					FINISHING_SHUNT_FLAG	= #finishingShuntFlag#,   <!-- 精整分流标记 -->
					D_USER_NUM	= #d_userNum#,   <!-- 分户号 -->
					D_USER_NAME	= #d_userName#,   <!-- 分户号简称 -->
					ON_WAY_FLAG	= #onWayFlag#,   <!-- 在途标记 -->
					PUTOUT_ID	= #putoutId#,   <!-- 出库单号 -->
					ENTITY_PUTOUT_DATE	= #entityPutoutDate#,   <!-- 实物出库时间 -->
					PERIOD_ID	= #periodId#,   <!-- 会计期间 -->
					TEAM_ID	= #teamId#,   <!-- 班组 -->
					WORKING_SHIFT	= #workingShift#,   <!-- 班次 -->
					TEAM_REPORT_ID	= #teamReportId#,   <!-- 班报序号 -->
					PROXY_TYPE	= #proxyType#,   <!-- 类型 -->
					IF_UPLOAD_FINANCE	= #ifUploadFinance#,   <!-- 是否上传财务 -->
					TRADE_CODE	= #tradeCode#,   <!-- 贸易方式 -->
					QUALITY_GRADE	= #qualityGrade#,   <!-- 质量等级 -->
					QUALITY_STATUS	= #qualityStatus#,   <!-- 质量状态 -->
					PACKING_TYPE_CODE	= #packingTypeCode#,   <!-- 包装方式代码 -->
					QUANTITY_UNIT	= #quantityUnit#,   <!-- 数量单位 -->
					WEIGHT_UNIT	= #weightUnit#,   <!-- 重量单位 -->
					PROD_TYPE_ID	= #prodTypeId#,   <!-- 品种附属码 -->
					AUDITOR_ID	= #auditorId#,   <!-- 审核人工号 -->
					AUDITOR_NAME	= #auditorName#,   <!-- 审核人姓名 -->
					AUDIT_TIME	= #auditTime#,   <!-- 审核时间 -->
					AUDIT_STATUS	= #auditStatus#,   <!-- 审核状态 -->
					AUDIT_OPNION	= #auditOpnion#,   <!-- 审核意见 -->
					PROD_TYPE_DESC	= #prodTypeDesc#,   <!-- 品种附属码描述 -->
					SCRAP_TYPE	= #scrapType#,   <!-- 利用材种类 -->
					MATERIAL_RACK_ID	= #materialRackId#,   <!-- 料架号 -->
					GROSS_WEIGHT	= #grossWeight:NUMERIC#,   <!-- 毛重 -->
					WL_ACCOUNT_MARK	= #wlAccountMark#,   <!-- 物流对账标记 -->
					CL_ACCOUNT_MARK	= #clAccountMark#,   <!-- 财务对账标记 -->
					LABEL_ID	= #labelId#,   <!-- 标签号 -->
					FIRST_PUTIN_DATE	= #firstPutinDate#,   <!-- 最初入库时间 -->
					TRANSFER_FLAG	= #transferFlag#,   <!-- 转换标记 -->
					TECH_STANDARD	= #techStandard#,   <!-- 技术标准 -->
					PACK_TRANSFER_FLAG	= #packTransferFlag#,   <!-- 捆包转移标记 -->
					MANUAL_NO	= #manualNo#,   <!-- 手册编号 -->
					HS_ID	= #hsId#,   <!-- 海关HS系统编码 -->
					PROCESS_CONSIGN_UNIT	= #processConsignUnit#,   <!-- 加工委托方（股份委托加工来源） -->
					ORIGINAL_PACK_ID	= #originalPackId#,   <!-- 原始捆包号 -->
					APPR_STATUS	= #apprStatus#,   <!-- 审批状态 -->
					PROCESS_ID	= #processId#,   <!-- 流程ID -->
					HANDBOOK_ID	= #handbookId#,   <!-- 手册系统编号 -->
					M_HANDBOOK_ID	= #m_handbookId#,   <!-- 母手册号(最初手册号) -->
					F_HANDBOOK_ID	= #f_handbookId#,   <!-- 父手册号(上层手册号) -->
					CUSTOMS_PRODUCT_NUM	= #customsProductNum#, 
					SURFACE_GRADE	= #surfaceGrade#,   <!-- 表面等级 -->
					FIN_USER_ID	= #finUserId#,   <!-- 最终用户代码 -->
					FIN_USER_NAME	= #finUserName#,   <!-- 最终用户名称 -->
					CUST_PART_ID	= #custPartId#,   <!-- 客户零件号 -->
					CUST_PART_NAME	= #custPartName#,   <!-- 客户零件号名称 -->
					PRODUCING_AREA	= #producingArea#,   <!-- 产地 -->
					MAKER_NUM	= #makerNum#,   <!-- 制造商代码 -->
					MAKER_NAME	= #makerName#,   <!-- 制造商名称 -->
					PROVIDER_CODE	= #providerCode#,   <!-- 供应商代码 -->
					PROVIDER_CNAME	= #providerCname#,   <!-- 供应商名称 -->
					CUST_PROVIDER_ID	= #custProviderId#,   <!-- 客户供料商代码 -->
					PRODUCT_DESC	= #productDesc#,   <!-- 产品描述 -->
					BIG_TYPE_DESC	= #bigTypeDesc#,   <!-- 利用材种类描述 -->
					ACTUAL_MATERIAL_WEIGHT	= #actualMaterialWeight:NUMERIC#,   <!-- 实绩领料重量 -->
					IF_SYNCHRONIZATION_WL	= #ifSynchronizationWl#,   <!-- 是否同步物流标记 -->
					WMS_SEND_FLAG	= #wmsSendFlag#,   <!-- 立体库WMS发送标记 1表示已发送，其他表示未发送 -->
					WMS_CX_FLAG	= #wmsCxFlag#,   <!-- 立体库WMS发送撤销标记 1表示已发送，其他表示未发送 -->
					PROPERTY_RESULT	= #propertyResult#,   <!-- 性能判定结果(1合格;2不合格;0或者空表示未判定) -->
					JP_SIGN	= #jpSign#,   <!-- 精品标记 -->
					JP_EMBARKATION_SIGN	= #jpEmbarkationSign#,   <!-- 精品上机标记 -->
					PARTS_CODE	= #partsCode#,   <!-- 部件编码 -->
					PARTS_NAME	= #partsName#,   <!-- 部品名称 -->
					CAR_TYPE_CODE	= #carTypeCode#,   <!-- 车型代码 -->
					VEHICLE_NAME	= #vehicleName#,   <!-- 车型名称 -->
					QUOTA	= #quota:NUMERIC#,   <!-- 定额(KG) -->
					JP_DEBATCHING_SIGN	= #jpDebatchingSign#,   <!-- 精品退料标记 -->
					JP_FINISHED_PRODUCT_SIGN	= #jpFinishedProductSign#,   <!-- 精品成品标记 -->
					PRINT_BATCH_ID	= #printBatchId#  <!-- 打印批次号 -->
			WHERE 	
			UUID = #uuid#
	</update>

	<delete id="deleteOneBale">
		DELETE FROM ${meviSchema}.TVIPM0008
		WHERE SEG_NO = #segNo#
		AND PACK_ID = #packId#
		AND MAT_INNER_ID = #matInnerId#
	</delete>

	<!--	修改下发行车工标记-->
	<update id="updateIssueCraneFlag">
		UPDATE ${meviSchema}.TVIPM0008
		SET
		IF_ISSUE_CRANE_OPERATOR = #ifIssueCraneOperator#,
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
		WHERE SEG_NO = #segNo#
		AND PACK_ID = #packId#
		<isNotEmpty prepend="AND" property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		AND PROCESS_DEMAND_MATERAIL_STATUS > '00'
		AND DEL_FLAG = '0'
	</update>

    <!--PDA上料吊运信息查询	-->
	<select id="queryMaterialLiftingInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		t0009.SEG_NO AS "segNo",
		t0009.PROCESS_ORDER_ID AS "processOrderId",
		t0009.MACHINE_CODE AS "machineCode",
		t0008.SPECS_DESC AS "specsDesc",
		t0008.PACK_ID AS "packId",
		t0008.MAT_INNER_ID AS "matInnerId",
		t0008.NET_WEIGHT AS "netWeight",
		t0008.LOCATION_ID AS "locationId",
		t0009.MOULD_ID AS "mouldId"
		FROM mevi.tvipm0009 t0009
		JOIN
		mevi.tvipm0008 t0008
		ON t0009.SEG_NO = t0008.SEG_NO
		AND t0009.PROCESS_ORDER_ID = t0008.PROCESS_ORDER_ID
		AND t0009.DEL_FLAG = t0008.DEL_FLAG
		WHERE t0009.SEG_NO = #segNo#
		AND t0009.MACHINE_CODE = #machineCode#
		AND t0008.IF_ISSUE_CRANE_OPERATOR = '10'
		AND t0008.DEL_FLAG = '0'
	</select>

	<!--PDA根据捆包号查询工单和机组信息	-->
	<select id="queryMaterialProcessInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		t0008.PROCESS_ORDER_ID AS "processOrderId",
		t0009.MACHINE_CODE AS "machineCode",
		t0701.MACHINE_NAME AS "machineName"
		FROM
		mevi.tvipm0008 t0008
		JOIN
		mevi.tvipm0009 t0009
		ON t0008.SEG_NO = t0009.SEG_NO
		AND t0008.PROCESS_ORDER_ID = t0009.PROCESS_ORDER_ID
		JOIN
		meli.tlids0701 t0701
		ON t0009.MACHINE_CODE = t0701.MACHINE_CODE
		WHERE
		t0008.DEL_FLAG = '0'
		AND t0009.DEL_FLAG = '0'
		AND t0008.PACK_ID = #packId#
		AND t0008.SEG_NO = #segNo#
	</select>
</sqlMap>