package com.baosight.imom.xt.ss.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class XTSS99 extends DaoEPBase {


    public static final  String UPDATE_USER_INFO = "XTSS99.updateUserInfo";
    public static final  String INSERT_USER_SEG_NO = "XTSS99.insertUserSegNo";
    public static final  String INSERT_USER_SEG_NO_SUBMIT = "XTSS99.insertUserSegNoSubmit";
    public static final  String QUERY_NOT_EXIST_USER = "XTSS99.queryNotExistUser";
    public static final  String COUNT_USER_SUBMIT = "XTSS99.countUserSubmit";
    public static final  String COUNT_USER_SEG_NO = "XTSS99.countUserSegNo";
    public static final  String COUNT_USER = "XTSS99.countUser";



    public XTSS99() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元代简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userId");
        eiColumn.setDescName("登录账号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userName");
        eiColumn.setDescName("用户姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mobile");
        eiColumn.setDescName("联系方式");
        eiMetadata.addMeta(eiColumn);

    }


    private String segNo = " ";		/* 系统账套*/
    private String segName = " ";		/* 系统账套名称*/
    private String unitCode = " ";		/* 业务单元代码*/
    private String uuid = " ";
    private String recCreator = " ";		/* 记录创建人*/
    private String recCreatorName = " ";		/* 记录创建人姓名*/
    private String recCreateTime = " ";		/* 记录创建时间*/
    private String recRevisor = " ";		/* 记录修改人*/
    private String recRevisorName = " ";		/* 记录修改人姓名*/
    private String recReviseTime = " ";		/* 记录修改时间*/
    private String archiveFlag = " ";		/* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
    private String tenantUser = " ";		/* 租户*/

    private String userId = " "; /*工号*/
    private String userName = " "; /*用户名称*/
    private String mobile = " "; /*联系方式*/


    public String getSegNo() {
        return segNo;
    }

    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getRecCreator() {
        return recCreator;
    }

    @Override
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    public String getRecCreatorName() {
        return recCreatorName;
    }

    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    public String getRecCreateTime() {
        return recCreateTime;
    }

    @Override
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    public String getRecRevisor() {
        return recRevisor;
    }

    @Override
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    public String getRecRevisorName() {
        return recRevisorName;
    }

    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    public String getRecReviseTime() {
        return recReviseTime;
    }

    @Override
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    public String getArchiveFlag() {
        return archiveFlag;
    }

    @Override
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getTenantUser() {
        return tenantUser;
    }

    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }


    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userId")), userId));
        setUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userName")), userName));
        setMobile(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mobile")), mobile));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("userId",StringUtils.toString(userId, eiMetadata.getMeta("userId")));
        map.put("userName",StringUtils.toString(userName, eiMetadata.getMeta("userName")));
        map.put("mobile",StringUtils.toString(mobile, eiMetadata.getMeta("mobile")));
        return map;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }
}
