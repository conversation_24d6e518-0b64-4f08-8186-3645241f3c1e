package com.baosight.imom.common.websocket;

import org.java_websocket.client.WebSocketClient;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * websocket 客户端管理器
 *
 * <AUTHOR> 郁在杰
 * @Description : websocket 客户端管理器
 * @Date : 2024/9/19
 * @Version : 1.0
 */
@Component
public class ClientManager {
    // 缓存客户端map
    private final Map<String, WebSocketClient> clientMap = new ConcurrentHashMap<>();

    /**
     * 客户端字段
     */
    public class ClientField {
        // 客户端类型
        public static final String CLIENT_TYPE = "clientType";
        // 客户端状态
        public static final String CLIENT_STATUS = "clientStatus";
    }

    /**
     * 客户端类型
     */
    public class ClientType {
        // 报警
        public static final String ALARM_CLIENT = "alarm";
        // 数据采集
        public static final String REFRESH_CLIENT = "refresh";
    }

    /**
     * 客户端状态
     */
    public class ClientStatus {
        public static final String START_STATUS = "监听中";
        public static final String STOP_STATUS = "未启动";
    }

    /**
     * 添加客户端
     *
     * @param keyName 客户端key
     * @param client  客户端
     */
    public void addClient(String keyName, WebSocketClient client) {
        // 如果已有连接则关闭现有连接
        this.removeClient(keyName);
        clientMap.put(keyName, client);
    }

    /**
     * 根据key获取客户端
     *
     * @param keyName 客户端key
     */
    public WebSocketClient getClient(String keyName) {
        return clientMap.get(keyName);
    }

    /**
     * 移除客户端
     *
     * @param keyName 客户端key
     */
    public void removeClient(String keyName) {
        WebSocketClient client = clientMap.get(keyName);
        if (client != null && client.isOpen()) {
            client.close();
        }
        clientMap.remove(keyName);
    }

    /**
     * 移除所有客户端
     */
    public void removeAllClient() {
        for (WebSocketClient client : clientMap.values()) {
            if (client != null && client.isOpen()) {
                client.close();
            }
        }
        clientMap.clear();
    }
}
