<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-31 15:35:00
       Version :  1.0
    tableName :mevi.tvipm0004
     MACHINE_OFFLINE_SEQ_ID  VARCHAR   NOT NULL,
     MACHINE_CODE  VARCHAR   NOT NULL,
     MACHINE_NAME  VARCHAR   NOT NULL,
     THICK_MIN  DECIMAL   NOT NULL,
     THICK_MAX  DECIMAL   NOT NULL,
     OFFLINE_EVENT  VARCHAR   NOT NULL,
     OFFLINE_TIME  DECIMAL   NOT NULL,
     MACHINE_OFFLINE_STATUS  VARCHAR   NOT NULL,
     E_ARCHIVES_NO  VARCHAR   NOT NULL,
     EQUIPMENT_NAME  VARCHAR   NOT NULL,
     PROCESS_CATEGORY  VARCHAR   NOT NULL,
     <PERSON><PERSON><PERSON><PERSON>  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  VARCHAR   NOT NULL,
     DEL_FLAG  VARCHAR   NOT NULL,
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL
-->
<sqlMap namespace="tvipm0004">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.common.vi.domain.Tvipm0004">
        SELECT
        MACHINE_OFFLINE_SEQ_ID as "machineOfflineSeqId",  <!-- 机组离线时间序列号 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        THICK_MIN as "thickMin",  <!-- 最小厚度 -->
        THICK_MAX as "thickMax",  <!-- 最大厚度 -->
        OFFLINE_EVENT as "offlineEvent",  <!-- 离线项目 -->
        OFFLINE_TIME as "offlineTime",  <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS as "machineOfflineStatus",  <!-- 机组离线时状态 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        REMARK as "remark",  <!-- 备注 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM mevi.tvipm0004 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM mevi.tvipm0004 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="machineOfflineSeqId">
            MACHINE_OFFLINE_SEQ_ID = #machineOfflineSeqId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE = #machineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineName">
            MACHINE_NAME = #machineName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="thickMin">
            THICK_MIN = #thickMin#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="thickMax">
            THICK_MAX = #thickMax#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="offlineEvent">
            OFFLINE_EVENT = #offlineEvent#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="offlineTime">
            OFFLINE_TIME = #offlineTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineOfflineStatus">
            MACHINE_OFFLINE_STATUS = #machineOfflineStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME = #equipmentName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategory">
            PROCESS_CATEGORY = #processCategory#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO mevi.tvipm0004 (MACHINE_OFFLINE_SEQ_ID,  <!-- 机组离线时间序列号 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        THICK_MIN,  <!-- 最小厚度 -->
        THICK_MAX,  <!-- 最大厚度 -->
        OFFLINE_EVENT,  <!-- 离线项目 -->
        OFFLINE_TIME,  <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS,  <!-- 机组离线时状态 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        REMARK,  <!-- 备注 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#machineOfflineSeqId#, #machineCode#, #machineName#, #thickMin#, #thickMax#, #offlineEvent#,
        #offlineTime#, #machineOfflineStatus#, #eArchivesNo#, #equipmentName#, #processCategory#, #remark#, #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM mevi.tvipm0004 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE mevi.tvipm0004
        SET
        MACHINE_OFFLINE_SEQ_ID = #machineOfflineSeqId#,   <!-- 机组离线时间序列号 -->
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        MACHINE_NAME = #machineName#,   <!-- 机组名称 -->
        THICK_MIN = #thickMin#,   <!-- 最小厚度 -->
        THICK_MAX = #thickMax#,   <!-- 最大厚度 -->
        OFFLINE_EVENT = #offlineEvent#,   <!-- 离线项目 -->
        OFFLINE_TIME = #offlineTime#,   <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS = #machineOfflineStatus#,   <!-- 机组离线时状态 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCESS_CATEGORY = #processCategory#,   <!-- 工序大类代码 -->
        REMARK = #remark#,   <!-- 备注 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#  <!-- 业务单元代码 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>