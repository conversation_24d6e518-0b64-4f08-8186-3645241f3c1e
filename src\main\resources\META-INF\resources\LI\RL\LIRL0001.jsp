<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="sub_query_status-0-windowId" cname="ID" type="hidden"/>
            <EF:EFInput ename="sub_query_status-0-userNum" colWidth="3"  placeholder="模糊条件" cname="客户代码"/>
            <EF:EFInput ename="sub_query_status-0-chineseUserName" colWidth="3" placeholder="模糊条件" cname="客户名称"/>
            <EF:EFSelect ename="sub_query_status-0-reservationIdentity" cname="预约身份(承运商/客户)"
                         colWidth="3" disabled="true"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="客户" value="10"/>
            </EF:EFSelect>
            <EF:EFInput ename="sub_query_status-0-unitCode" cname="业务单元代码" colWidth="3" type="hidden" disabled="true"/>
            <EF:EFInput ename="sub_query_status-0-segName" cname="业务单元简称" colWidth="3" type="hidden" disabled="true"/>
            <EF:EFInput ename="sub_query_status-0-segNo" cname="系统账套" colWidth="3"  type="hidden" disabled="true"/>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="sub_result" title="结果集">
        <EF:EFGrid blockId="sub_result" autoDraw="no"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="userNum" cname="客户代码" align="center"  enable="false" required="true"/>
            <EF:EFColumn ename="chineseUserName" cname="客户名称" align="center"  required="true" enable="false"/>
        </EF:EFGrid>
    </EF:EFRegion>

</EF:EFPage>
