<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="XTSS01">

<select id="queryCodeValue" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">


    select  t.seg_no     as "segNo",
            t.code_type  as "codeType",
            t.code_seq   as "codeSeq",
            t.code_value as "codeValue",
            t.code_desc  as "codeDesc"
    from ${platSchema}.t_ss_code_value t
    where 1=1
    <isNotEmpty prepend=" AND " property="segNo">
        t.seg_no = #segNo#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="codeType">
        t.code_type = #codeType#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="codeSeq">
        t.code_seq = #codeSeq#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="codeValue">
        t.code_value = #codeValue#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="codeTypeList">
        t.code_type in
        <iterate property="codeTypeList" open="(" conjunction="," close=")">
            #codeTypeList[]#
        </iterate>
    </isNotEmpty>
    and t.DISPLAY_FLAG = '1'
    and t.DEL_FLAG = '0'

</select>

    <select id="queryUserSegNo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        t.SEG_NO as "segNo"
        FROM ${platSchema}.tvzbm8102 t
        where EMP_NO =#emp_no#
    </select>

    <select id="querySegNo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        t.SEG_NO as "segNo",
        t.UNIT_CODE as "unitCode",
        t.SEG_NAME as "segName",
        t.SEG_FULL_NAME as "segFullName"
        FROM ${platSchema}.tvzbm8101 t
        where t.ATTRIBUTE_1  in ($attribute_1$)
        <isNotEmpty prepend=" AND " property="segNo">
            t.SEG_NO like '%$segNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segName">
            t.SEG_NAME like '%$segName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segFullName">
            t.SEG_FULL_NAME like '%$segFullName$%'
        </isNotEmpty>
        AND t.DEL_FLAG = '0'
        AND t.SEG_RULE_STATUS = '20'
    </select>

    <select id="queryUserInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">

        select SEG_NO as "segNo",IS_DATA_AUTHOR as "isDataAuthor" from ${platSchema}.xs_user where LOGIN_NAME =#userId#

    </select>


    <select id="querySegNoByUserId" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">

        select  SEG_NO as "unitCode", SEG_NO  as "segNo", SEG_FULL_NAME as "segFullName", SEG_NAME as "segName" from ${platSchema}.tvzbm81 where SEG_NO IN  (select SEG_NO
        from ${platSchema}.tvzbm8101
        where ATTRIBUTE_1 = (select SEG_NO from ${platSchema}.xs_user where LOGIN_NAME =#userId#)
        union
        select SEG_NO
        from ${platSchema}.xs_user
        where LOGIN_NAME = #userId#
        )
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO like '%$segNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segName">
            SEG_NAME like '%$segName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segFullName">
            SEG_FULL_NAME like '%$segFullName$%'
        </isNotEmpty>


    </select>


    <select id="querySegNoListByAu" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">

        select SEG_NO as "unitCode", SEG_NO  as "segNo", SEG_FULL_NAME as "segFullName", SEG_NAME as "segName"
        from ${platSchema}.tvzbm81
        where SEG_NO IN (select SEG_NO
        from ${platSchema}.tvzbm8101
        where ATTRIBUTE_1 = #segNo#
        )
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO like '%$segNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segName">
            SEG_NAME like '%$segName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segFullName">
            SEG_FULL_NAME like '%$segFullName$%'
        </isNotEmpty>

    </select>

    <select id="querySegNoListByBlankSegNo" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">

        select SEG_NO as "unitCode", SEG_NO  as "segNo", SEG_FULL_NAME as "segFullName", SEG_NAME as "segName"
        from ${platSchema}.tvzbm81
        where 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO like '%$segNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segName">
            SEG_NAME like '%$segName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segFullName">
            SEG_FULL_NAME like '%$segFullName$%'
        </isNotEmpty>

        order by SEG_NO

    </select>


    <select id="querySegnoListViewByF" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">

        select  T1.SEG_NO AS "unitCode",T1.SEG_NO AS "segNo", T1.SEG_NAME as "segName", T1.SEG_FULL_NAME as "segFullName"
        from ${platSchema}.v_user_segno t1
        where t1.F_SEG_NO = #segNo#

    </select>


    <select id="querySegnoListViewByOwn" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">

        select  T1.SEG_NO AS "unitCode",T1.SEG_NO AS "segNo", T1.SEG_NAME as "segName", T1.SEG_FULL_NAME as "segFullName"
        from ${platSchema}.v_user_segno t1
        where t1.F_SEG_NO = #segNo#
        AND T1.SEG_NO = #segNo#

    </select>

</sqlMap>