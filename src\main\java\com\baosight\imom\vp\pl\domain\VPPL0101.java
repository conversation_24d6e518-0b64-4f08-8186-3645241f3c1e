/**
* Generate time : 2025-01-20 10:54:54
* Version : 1.0
*/
package com.baosight.imom.vp.pl.domain;
import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* VPPL0101
* 
*/
public class VPPL0101 extends DaoEPBase {

                public static final String QUERY = "VPPL0101.query";
                public static final String COUNT = "VPPL0101.count";
                public static final String INSERT = "VPPL0101.insert";
                public static final String UPDATE = "VPPL0101.update";
                public static final String DELETE = "VPPL0101.delete";
                public static final String UPDATE_STATUS = "VPPL0101.updateStatus";
                public static final String QUERY_EXPORT = "VPPL0101.queryExport";

                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String segName = " ";        /* 业务单元简称*/
                private String chipId = " ";		/* 芯片ID*/
                private String empNo = " ";         /*员工工号*/
                private String staffName = " ";		/* 人员姓名*/
                private String teamId = " ";		/* 班组*/
                private String workingShift = " ";		/* 班次*/
                private String workingHours = " ";		/* 当班时间*/
                private String postResponsibility = " ";		/* 人员岗位责任*/
                private String jobScope = " ";          /*岗位范围*/
                private String status = " ";		/* 状态*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";        /* ID*/
                private String phoneNumber;      /*电话号码*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("chipId");
        eiColumn.setDescName("芯片ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("empNo");
        eiColumn.setDescName("员工工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("staffName");
        eiColumn.setDescName("人员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingHours");
        eiColumn.setDescName("当班时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("postResponsibility");
        eiColumn.setDescName("人员岗位责任");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jobScope");
        eiColumn.setDescName("岗位范围");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("phoneNumber");
        eiColumn.setDescName("电话号码");
        eiMetadata.addMeta(eiColumn);

}
/**
* the constructor
*/
public VPPL0101() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }

        public String getSegName() {
            return segName;
        }

        public void setSegName(String segName) {
            this.segName = segName;
        }

    /**
        * get the chipId - 芯片ID
        * @return the chipId
        */
        public String getChipId() {
        return this.chipId;
        }

        /**
        * set the chipId - 芯片ID
        */
        public void setChipId(String chipId) {
        this.chipId = chipId;
        }
        /**
        * get the staffName - 人员姓名
        * @return the staffName
        */
        public String getStaffName() {
        return this.staffName;
        }

        /**
        * set the staffName - 人员姓名
        */
        public void setStaffName(String staffName) {
        this.staffName = staffName;
        }
        /**
        * get the teamId - 班组
        * @return the teamId
        */
        public String getTeamId() {
        return this.teamId;
        }

        /**
        * set the teamId - 班组
        */
        public void setTeamId(String teamId) {
        this.teamId = teamId;
        }
        /**
        * get the workingShift - 班次
        * @return the workingShift
        */
        public String getWorkingShift() {
        return this.workingShift;
        }

        /**
        * set the workingShift - 班次
        */
        public void setWorkingShift(String workingShift) {
        this.workingShift = workingShift;
        }
        /**
        * get the workingHours - 当班时间
        * @return the workingHours
        */
        public String getWorkingHours() {
        return this.workingHours;
        }

        /**
        * set the workingHours - 当班时间
        */
        public void setWorkingHours(String workingHours) {
        this.workingHours = workingHours;
        }
        /**
        * get the postResponsibility - 人员岗位责任
        * @return the postResponsibility
        */
        public String getPostResponsibility() {
        return this.postResponsibility;
        }

        /**
        * set the postResponsibility - 人员岗位责任
        */
        public void setPostResponsibility(String postResponsibility) {
        this.postResponsibility = postResponsibility;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }

        public String getEmpNo() {
            return empNo;
        }

        public void setEmpNo(String empNo) {
            this.empNo = empNo;
        }

        public String getJobScope() {
            return jobScope;
        }

        public void setJobScope(String jobScope) {
            this.jobScope = jobScope;
        }

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setEmpNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("empNo")), empNo));
                setChipId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("chipId")), chipId));
                setStaffName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("staffName")), staffName));
                setTeamId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teamId")), teamId));
                setWorkingShift(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workingShift")), workingShift));
                setWorkingHours(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workingHours")), workingHours));
                setPostResponsibility(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("postResponsibility")), postResponsibility));
                setJobScope(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jobScope")), jobScope));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setPhoneNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("phoneNumber")), phoneNumber));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("empNo",StringUtils.toString(empNo, eiMetadata.getMeta("empNo")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("chipId",StringUtils.toString(chipId, eiMetadata.getMeta("chipId")));
                map.put("staffName",StringUtils.toString(staffName, eiMetadata.getMeta("staffName")));
                map.put("teamId",StringUtils.toString(teamId, eiMetadata.getMeta("teamId")));
                map.put("workingShift",StringUtils.toString(workingShift, eiMetadata.getMeta("workingShift")));
                map.put("workingHours",StringUtils.toString(workingHours, eiMetadata.getMeta("workingHours")));
                map.put("postResponsibility",StringUtils.toString(postResponsibility, eiMetadata.getMeta("postResponsibility")));
                map.put("jobScope",StringUtils.toString(jobScope, eiMetadata.getMeta("jobScope")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("phoneNumber",StringUtils.toString(phoneNumber, eiMetadata.getMeta("phoneNumber")));

return map;

}


    /**
     * 导出表头信息
     */
    public static EiBlockMeta exportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("chipId");
        eiColumn.setDescName("芯片ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("empNo");
        eiColumn.setDescName("员工工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("staffName");
        eiColumn.setDescName("人员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingHours");
        eiColumn.setDescName("当班时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("postResponsibility");
        eiColumn.setDescName("人员岗位责任");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jobScope");
        eiColumn.setDescName("岗位范围");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("phoneNumber");
        eiColumn.setDescName("联系电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }
}