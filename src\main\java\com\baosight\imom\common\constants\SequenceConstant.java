package com.baosight.imom.common.constants;

/**
 * <AUTHOR>
 * @date 2024-08-07
 * 序列号常量类，所有调用序列号生成的单据类型在这里配置
 */
public class SequenceConstant {

    /**
     * 例：入库单的序列号类型是I02
     */
    public static final String SEQ_I02 = "I02";

    /**
     * 点检异常信息联络单单号
     */
    public static final String EXCEPTION_CONTACT_ID = "EXCEPTION_CONTACT_ID";
    /**
     * 设备报警知识库案例编号
     */
    public static final String CASE_ID = "CASE_ID";
    /**
     * 点检标准编号
     */
    public static final String SPOT_CHECK_STANDARD_ID = "SPOT_CHECK_ID";
    /**
     * 点检计划主项编号
     */
    public static final String CHECK_PLAN_ID = "CHECK_PLAN_ID";
    /**
     * 点检计划明细编号
     */
    public static final String CHECK_PLAN_SUB_ID = "CHECK_PLAN_SUB_ID";
    /**
     * 检修计划编号
     */
    public static final String OVERHAUL_PLAN_ID = "OVERHAUL_PLAN_ID";
    /**
     * 资材备件报废申请单号
     */
    public static final String SCRAP_APPLY_ID = "SCRAP_APPLY_ID";

    /**
     * 故障单号
     */
    public static final String FAULT_ID = "FAULT_ID";
    /**
     * 报警编号
     */
    public static final String ALARM_ID = "ALARM_ID";
}
