package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> yzj
 * @Description : 工序规则页面后台
 * @Date : 2024/10/22
 * @Version : 1.0
 */
public class ServiceVGDM11 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM11.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM1101().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM1101.QUERY, new VGDM1101());
    }

    /**
     * 查询设备清单
     */
    public EiInfo queryDevice(EiInfo inInfo) {
        // 设置设备状态为有效30
        inInfo.setCell(EiConstant.queryBlock, 0, "equipmentStatus", "30");
        // 只查询设备类型生产设备
        inInfo.setCell(EiConstant.queryBlock, 0, "equipmentType", "1");
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo
                : super.query(inInfo, VGDM0101.QUERY, null, false, null,
                EiConstant.queryBlock, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM1101 vgdm1101;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1101 = new VGDM1101();
                vgdm1101.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm1101);
                // 状态-10新增
                vgdm1101.setRuleStatus(MesConstant.Status.K10);
                Map insMap = vgdm1101.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            DaoUtils.insertBatch(dao, VGDM1101.INSERT, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 数据校验
     *
     * @param vgdm1101 数据
     */
    private void checkData(VGDM1101 vgdm1101) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm1101);
        // 校验开始时间规则和结束时间规则
        Set<String> tagIds = checkJsonFormat(vgdm1101.getStartTimeRule(), "开始时间规则");
        tagIds.addAll(checkJsonFormat(vgdm1101.getStopTimeRule(), "结束时间规则"));
        // 遍历获取tagIhdId
        Map<String, String> idMap = new HashMap<>();
        for (String tagId : tagIds) {
            if (idMap.containsKey(tagId)) {
                continue;
            }
            VGDM0301 tag = VGDM0301.queryWithCache(dao, tagId);
            if (tag == null) {
                throw new PlatException(tagId + "点位信息不存在！");
            }
            if (!tag.getSegNo().equals(vgdm1101.getSegNo())) {
                throw new PlatException(tagId + "点位不属于当前业务单元！" + vgdm1101.getSegNo());
            }
            idMap.put(tagId, tag.getTagIhdId().toString());
        }
        // 替换字符串
        String startTimeRule = vgdm1101.getStartTimeRule();
        String stopTimeRule = vgdm1101.getStopTimeRule();
        for (Map.Entry<String, String> entry : idMap.entrySet()) {
            startTimeRule = startTimeRule.replace(entry.getKey(), entry.getValue());
            stopTimeRule = stopTimeRule.replace(entry.getKey(), entry.getValue());
        }
        vgdm1101.setIhdStartRule(startTimeRule);
        vgdm1101.setIhdStopRule(stopTimeRule);
    }

    /**
     * 检查JSON格式是否正确并能转换为Condition对象
     *
     * @param jsonString 要检查的JSON字符串
     * @param fieldName  字段名称,用于错误提示
     * @throws PlatException 如果JSON格式不正确或无法转换为Condition对象
     */
    private Set<String> checkJsonFormat(String jsonString, String fieldName) {
        try {
            if ("END".equals(jsonString)) {
                return new HashSet<>();
            }
            ProcedureRule rule = CalculateUtils.objectMapper.readValue(jsonString,
                    ProcedureRule.class);
            if (rule == null) {
                throw new PlatException(fieldName + "不能转换为ProcedureRule类");
            }
            return rule.getAllField();
        } catch (Exception e) {
            throw new PlatException(fieldName + "不是有效的JSON格式或无法转换为ProcedureRule类: " + e.getMessage());
        }
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM1101 vgdm1101;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1101 = new VGDM1101();
                vgdm1101.fromMap(block.getRow(i));
                DaoUtils.queryAndCheckStatus(dao, vgdm1101,
                        MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                this.checkData(vgdm1101);
                vgdm1101.setRuleStatus(MesConstant.Status.K10);
                vgdm1101.setDelFlag("0");
                Map updMap = vgdm1101.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM1101.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     * <p>
     * 修改状态为删除，删除标记置1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM1101 vgdm1101;
            VGDM1101 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1101 = new VGDM1101();
                vgdm1101.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm1101,
                        MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                dbData.setDelFlag("1");
                dbData.setRuleStatus(MesConstant.Status.K00);
                Map delMap = dbData.toMap();
                RecordUtils.setRevisor(delMap);
                block.getRows().set(i, delMap);
            }
            DaoUtils.updateBatch(dao, VGDM1101.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 提交
     * 状态：新增-已提交
     */
    public EiInfo submit(EiInfo inInfo) {
        return changeStatus(inInfo, MesConstant.Status.K10, MesConstant.Status.K20,
                MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS,
                MessageCodeConstant.successMessage.MSG_SUCCESS_SUBMIT);
    }

    /**
     * 撤销提交
     * 状态：已提交-新增
     */
    public EiInfo cancel(EiInfo inInfo) {
        return changeStatus(inInfo, MesConstant.Status.K20, MesConstant.Status.K10,
                MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS,
                MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
    }

    /**
     * 通用状态变更方法
     *
     * @param inInfo     请求参数
     * @param fromStatus 原状态
     * @param toStatus   目标状态
     * @param errorMsg   错误信息
     * @param successMsg 成功信息
     */
    private EiInfo changeStatus(EiInfo inInfo, String fromStatus, String toStatus, String errorMsg, String successMsg) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM1101 vgdm1101;
            VGDM1101 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm1101 = new VGDM1101();
                vgdm1101.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm1101, errorMsg, fromStatus);
                dbData.setRuleStatus(toStatus);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM1101.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(successMsg);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
