<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-11-05 9:08:46
   		Version :  1.0
		tableName :${meliSchema}.tlirl0407
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 FINISH_LOAD_ID  VARCHAR   NOT NULL   primarykey, 
		 STATUS  VARCHAR   NOT NULL, 
		 FINISH_LOAD_DATE  VARCHAR   NOT NULL, 
		 CANCEL_FINISH_LOAD_DATE  VARCHAR   NOT NULL, 
		 CAR_TRACE_NO  VARCHAR   NOT NULL, 
		 DATE_SOURCE  VARCHAR   NOT NULL, 
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 NEXT_TATGET  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 CURRENT_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 TATGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 DOCUMENT_TYPE  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0407">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finishLoadId">
			FINISH_LOAD_ID = #finishLoadId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finishLoadDate">
			FINISH_LOAD_DATE = #finishLoadDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cancelFinishLoadDate">
			CANCEL_FINISH_LOAD_DATE = #cancelFinishLoadDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateSource">
			DATE_SOURCE = #dateSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="nextTatget">
			NEXT_TATGET = #nextTatget#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			CURRENT_HAND_POINT_ID = #currentHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tatgetHandPointId">
			TATGET_HAND_POINT_ID = #tatgetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="documentType">
			DOCUMENT_TYPE = #documentType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0407">
		SELECT
				SEG_NO	as "segNo",  <!-- 账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				FINISH_LOAD_ID	as "finishLoadId",  <!-- 结束装卸货流水号 -->
				STATUS	as "status",  <!-- 状态（00：撤销，10：新增） -->
				FINISH_LOAD_DATE	as "finishLoadDate",  <!-- 结束装卸货日期 -->
				CANCEL_FINISH_LOAD_DATE	as "cancelFinishLoadDate",  <!-- 撤销结束装卸货时间 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				DATE_SOURCE	as "dateSource",  <!-- 数据源（10：MES,20:PDA） -->
				VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单号（PAD作业流水号） -->
				NEXT_TATGET	as "nextTatget",  <!-- 下一目标（10：下个装卸点，20：离厂） -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				CURRENT_HAND_POINT_ID	as "currentHandPointId",  <!-- 当前装卸点代码 -->
				TATGET_HAND_POINT_ID	as "tatgetHandPointId",  <!-- 目标装卸点代码（下个装卸点） -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				DOCUMENT_TYPE	as "documentType",  <!-- 单据类型(10普通 20重复) -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0407 WHERE 1=1
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="finishLoadId">
			FINISH_LOAD_ID = #finishLoadId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="flag">
			NOT exists(select 1
			from meli.tlirl0408 tlirl0408
			where 1 = 1
			and tlirl0408.SEG_NO = tlirl0407.SEG_NO
			and tlirl0408.CAR_TRACE_NO = tlirl0407.CAR_TRACE_NO
			and tlirl0408.DEL_FLAG='0'
			limit 1)
		</isNotEmpty>
		<isNotEmpty prepend="and " property="flag2">
			exists(select 1
			from meli.tlirl0301 tlirl0301
			where 1 = 1
			and tlirl0301.SEG_NO = tlirl0407.SEG_NO
			and tlirl0301.CAR_TRACE_NO = tlirl0407.CAR_TRACE_NO
			and tlirl0301.VEHICLE_NO = tlirl0407.VEHICLE_NO
			and tlirl0301.DEL_FLAG = '0'
			and tlirl0301.STATUS != '00'
			limit 1)
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  FINISH_LOAD_ID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="queryAllHandPointTime" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select max(tlirl0406.CURRENT_HAND_POINT_ID)                                        as "handPointId",
		(select HAND_POINT_NAME
		from meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO
		= tlirl0407.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0407.CURRENT_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		limit 1)                                                                   as "handPointName",
		TIMESTAMPDIFF(MINUTE,
		STR_TO_DATE(MIN(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s'),
		STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')) as "totalOperationTimeMinutes"
		from meli.tlirl0407 tlirl0407,
		meli.tlirl0406 tlirl0406
		where 1 = 1
		and tlirl0407.SEG_NO = tlirl0406.SEG_NO
		and tlirl0407.LOAD_ID = tlirl0406.LOAD_ID
		and tlirl0407.SEG_NO = #segNo#
		and tlirl0406.CAR_TRACE_NO = #carTraceNo#
		group by tlirl0407.CURRENT_HAND_POINT_ID
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0407 WHERE 1=1
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="finishLoadId">
			FINISH_LOAD_ID = #finishLoadId#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finishLoadId">
			FINISH_LOAD_ID = #finishLoadId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finishLoadDate">
			FINISH_LOAD_DATE = #finishLoadDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cancelFinishLoadDate">
			CANCEL_FINISH_LOAD_DATE = #cancelFinishLoadDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateSource">
			DATE_SOURCE = #dateSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="nextTatget">
			NEXT_TATGET = #nextTatget#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			CURRENT_HAND_POINT_ID = #currentHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tatgetHandPointId">
			TATGET_HAND_POINT_ID = #tatgetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="documentType">
			DOCUMENT_TYPE = #documentType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0407 (SEG_NO,  <!-- 账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										FINISH_LOAD_ID,  <!-- 结束装卸货流水号 -->
										STATUS,  <!-- 状态（00：撤销，10：新增） -->
										FINISH_LOAD_DATE,  <!-- 结束装卸货日期 -->
										CANCEL_FINISH_LOAD_DATE,  <!-- 撤销结束装卸货时间 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										DATE_SOURCE,  <!-- 数据源（10：MES,20:PDA） -->
										VOUCHER_NUM,  <!-- 依据凭单号（PAD作业流水号） -->
										NEXT_TATGET,  <!-- 下一目标（10：下个装卸点，20：离厂） -->
										VEHICLE_NO,  <!-- 车牌号 -->
										CURRENT_HAND_POINT_ID,  <!-- 当前装卸点代码 -->
										TATGET_HAND_POINT_ID,  <!-- 目标装卸点代码（下个装卸点） -->
										FACTORY_AREA,  <!-- 厂区 -->
										DOCUMENT_TYPE,  <!-- 单据类型(10普通 20重复) -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
		LOAD_ID
										)		 
	    VALUES (#segNo#, #unitCode#, #finishLoadId#, #status#, #finishLoadDate#, #cancelFinishLoadDate#, #carTraceNo#, #dateSource#, #voucherNum#, #nextTatget#, #vehicleNo#,
		#currentHandPointId#, #tatgetHandPointId#, #factoryArea#, #documentType#,
		#recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
		#recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#,
		#sysRemark#, #uuid#, #tenantId#,#loadId#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0407 WHERE
			FINISH_LOAD_ID = #finishLoadId#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0407
		SET 
		SEG_NO	= #segNo#,   <!-- 账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
								STATUS	= #status#,   <!-- 状态（00：撤销，10：新增） -->  
					FINISH_LOAD_DATE	= #finishLoadDate#,   <!-- 结束装卸货日期 -->  
					CANCEL_FINISH_LOAD_DATE	= #cancelFinishLoadDate#,   <!-- 撤销结束装卸货时间 -->  
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->  
					DATE_SOURCE	= #dateSource#,   <!-- 数据源（10：MES,20:PDA） -->  
					VOUCHER_NUM	= #voucherNum#,   <!-- 依据凭单号（PAD作业流水号） -->  
					NEXT_TATGET	= #nextTatget#,   <!-- 下一目标（10：下个装卸点，20：离厂） -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					CURRENT_HAND_POINT_ID	= #currentHandPointId#,   <!-- 当前装卸点代码 -->  
					TATGET_HAND_POINT_ID	= #tatgetHandPointId#,   <!-- 目标装卸点代码（下个装卸点） -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					DOCUMENT_TYPE	= #documentType#,   <!-- 单据类型(10普通 20重复) -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE
			SEG_NO = #segNo#
			and
			FINISH_LOAD_ID = #finishLoadId#
	</update>


	<update id="updateStatus">
		UPDATE ${meliSchema}.tlirl0407
		SET
		NEXT_TATGET	= #nextTatget#,   <!-- 下一目标（10：下个装卸点，20：离厂） -->
		TATGET_HAND_POINT_ID	= #tatgetHandPointId#,   <!-- 目标装卸点代码（下个装卸点） -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		REMARK	= #remark#,   <!-- 备注 -->
		SYS_REMARK	= #sysRemark#  <!-- 系统备注 -->
		WHERE
		SEG_NO = #segNo#
		and
		FINISH_LOAD_ID = #finishLoadId#
	</update>

	<update id="updateNextTarget">
		UPDATE ${meliSchema}.tlirl0407
		SET
		NEXT_TATGET	= #nextTatget#   <!-- 下一目标（10：下个装卸点，20：离厂） -->
		WHERE
		SEG_NO = #segNo#
		and
		CAR_TRACE_NO=#carTraceNo#
		and
		VEHICLE_NO = #vehicleNo#
		order by FINISH_LOAD_ID desc limit 1
	</update>
  
</sqlMap>