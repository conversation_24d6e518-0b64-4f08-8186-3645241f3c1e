<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="XTSS99">


    <update id="updateUserInfo" parameterClass="com.baosight.imom.xt.ss.domain.XTSS99">


        update ${platSchema}.xs_user set MOBILE = #mobile# ,SEG_NO = #segNo# where USER_ID = #userId# AND MOBILE != ' '


    </update>

    <insert id="insertUserSegNo" parameterClass="com.baosight.imom.xt.ss.domain.XTSS99">

        insert into ${platSchema}.xs_user_segno (user_id, login_name, user_name, seg_no, uuid, rec_creator, rec_create_time,
        rec_revisor, rec_revise_time, seg_name) values (
        #userId#,#userId#,#userName#,#segNo#,#uuid#,#recCreator#,#recCreateTime#,#recRevisor#,#recReviseTime#,#segName#
        )

    </insert>

    <insert id="insertUserSegNoSubmit" parameterClass="com.baosight.imom.xt.ss.domain.XTSS99">

        insert into ${platSchema}.xs_user_submit (seg_no, seg_name, user_id, user_name, mobile, rec_creator, rec_creator_name,
        rec_create_time, rec_revisor, rec_revisor_name, rec_revise_time, archive_flag,
        del_flag, tenant_user, unit_code, uuid)
        values (#segNo#,
        #segName#,
        #userId#,
        #userName#,
        #mobile#,
        #recCreator#,
        #recCreatorName#,
        #recCreateTime#,
        #recCreator#,
        #recCreatorName#,
        #recCreateTime#,
        #archiveFlag#,
        #delFlag#,
        #tenantUser#,#segNo#,#uuid#)

    </insert>

    <select id="queryNotExistUser" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">

        select t1.seg_no as "segNo",
        t1.seg_name as "segName",
        t1.user_id as "userId",
        t1.user_name as "userName",
        t1.mobile as "mobile"
        from ${platSchema}.xs_user_submit t1
        where not exists(select 1 from ${platSchema}.xs_user t2 where t1.USER_ID = t2.USER_ID)
        and SEG_NO = #segNo#

    </select>

    <select id="countUserSubmit" parameterClass="com.baosight.imom.xt.ss.domain.XTSS99" resultClass="int">

        select 1 from ${platSchema}.xs_user_submit t1 where t1.USER_ID = #userId#

    </select>

    <select id="countUserSegNo" parameterClass="com.baosight.imom.xt.ss.domain.XTSS99" resultClass="int">

        select 1 from ${platSchema}.xs_user_segno  t1 where t1.USER_ID = #userId# and t1.SEG_NO = #segNo#

    </select>

    <select id="countUser" parameterClass="com.baosight.imom.xt.ss.domain.XTSS99" resultClass="int">

        select 1 from ${platSchema}.xs_user  t1 where t1.USER_ID = #userId#

    </select>



</sqlMap>