<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0603">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS != '00'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0603">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        HANDLE_USER_ID as "handleUserId",  <!-- 处理人 -->
        HANDLE_USER_NAME as "handleUserName",  <!-- 处理人姓名 -->
        HANDLE_MOBILE as "handleMobile",  <!-- 处理人手机 -->
        MANAGE_USER_ID as "manageUserId",  <!-- 管理人 -->
        MANAGE_USER_NAME as "manageUserName",  <!-- 管理人姓名 -->
        MANAGE_MOBILE as "manageMobile",  <!-- 管理人手机 -->
        MACHINERY_HANDLE_ID as "machineryHandleId",  <!-- 机械处理人 -->
        MACHINERY_HANDLE_NAME as "machineryHandleName",  <!-- 机械处理人姓名 -->
        MACHINERY_HANDLE_MOBILE as "machineryHandleMobile",  <!-- 机械处理人手机 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        STATUS as "status"  <!-- 状态 -->
        FROM ${mevgSchema}.TVGDM0603 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0603 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="countRepeat" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0603 WHERE
        DEL_FLAG = '0'
        AND E_ARCHIVES_NO = #eArchivesNo#
        <isNotEmpty prepend=" AND " property="uuid">
            UUID != #uuid#
        </isNotEmpty>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0603 (
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        HANDLE_USER_ID,  <!-- 处理人 -->
        HANDLE_USER_NAME,  <!-- 处理人姓名 -->
        HANDLE_MOBILE,  <!-- 处理人手机 -->
        MANAGE_USER_ID,  <!-- 管理人 -->
        MANAGE_USER_NAME,  <!-- 管理人姓名 -->
        MANAGE_MOBILE,  <!-- 管理人手机 -->
        MACHINERY_HANDLE_ID,  <!-- 机械处理人 -->
        MACHINERY_HANDLE_NAME,  <!-- 机械处理人姓名 -->
        MACHINERY_HANDLE_MOBILE,  <!-- 机械处理人手机 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        STATUS  <!-- 状态 -->
        )
        VALUES (
        #eArchivesNo#, #equipmentName#, #handleUserId#, #handleUserName#, #handleMobile#, #manageUserId#,
        #manageUserName#, #manageMobile#, #machineryHandleId#, #machineryHandleName#, #machineryHandleMobile#,
        #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #status#
        )
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0603 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0603
        SET
        MACHINERY_HANDLE_ID = #machineryHandleId#,  <!-- 机械处理人 -->
        MACHINERY_HANDLE_NAME = #machineryHandleName#,  <!-- 机械处理人姓名 -->
        MACHINERY_HANDLE_MOBILE = #machineryHandleMobile#,  <!-- 机械处理人手机 -->
        HANDLE_USER_ID = #handleUserId#,   <!-- 处理人 -->
        HANDLE_USER_NAME = #handleUserName#,   <!-- 处理人姓名 -->
        HANDLE_MOBILE = #handleMobile#,   <!-- 处理人手机 -->
        MANAGE_USER_ID = #manageUserId#,   <!-- 管理人 -->
        MANAGE_USER_NAME = #manageUserName#,   <!-- 管理人姓名 -->
        MANAGE_MOBILE = #manageMobile#,   <!-- 管理人手机 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        STATUS = #status#   <!-- 状态 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>