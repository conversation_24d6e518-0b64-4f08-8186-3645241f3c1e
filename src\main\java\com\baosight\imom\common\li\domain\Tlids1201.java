/**
 * Generate time : 2024-12-05 13:10:12
 * Version : 1.0
 */
package com.baosight.imom.common.li.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tlids1201
 */
public class Tlids1201 extends DaoEPBase {

    private String segNo = "";        /* 系统账套*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = "";        /* 业务单元代代码*/
    private String craneResultId = "";        /* 行车实绩单号*/
    private String craneOrderId = "";        /* 行车作业清单号*/
    private String craneId = "";        /* 行车编号*/
    private String craneName = "";        /* 行车名称*/
    private String startXPosition = "";        /* 起始X轴坐标*/
    private String startYPosition = "";        /* 起始X轴坐标*/
    private String startZPosition = "";        /* 起始Y轴坐标*/
    private String endxPosition = "";        /* 终到X轴坐标*/
    private String endyPosition = "";        /* 终到X轴坐标*/
    private String endzPosition = "";        /* 终到Y轴坐标*/
    private String inboundSequenceId = "";        /* 抓取流水号*/
    private String outboundSequenceId = "";        /* 释放流水号*/
    private String abnormalFlag = "";        /* 异常标记*/
    private String status = "";        /* 状态*/
    private String recCreator = "";        /* 记录创建人*/
    private String recCreatorName = "";        /* 记录创建人姓名*/
    private String recCreateTime = "";        /* 记录创建时间*/
    private String recRevisor = "";        /* 记录修改人*/
    private String recRevisorName = "";        /* 记录修改人姓名*/
    private String recReviseTime = "";        /* 记录修改时间*/
    private String archiveFlag = "";        /* 归档标记*/
    private String tenantUser = "";        /* 租户*/
    private Integer delFlag = Integer.valueOf(0);        /* 删除标记*/
    private String uuid = "";        /* ID*/

    private String startAreaType = "";		/* 起始区域类型*/
    private String startAreaCode = "";		/* 起始区域代码*/
    private String startAreaName = "";		/* 起始区域名称*/
    
    private String endAreaType = "";		/* 终到区域类型*/
    private String endAreaCode = "";		/* 终到区域代码*/
    private String endAreaName = "";        /* 终到区域名称*/
    private String crossArea = "";          /*跨区代码*/
    private String factoryArea = "";		/* 厂区代码*/
    private String factoryBuilding = "";		/* 厂房代码*/
    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneResultId");
        eiColumn.setDescName("行车实绩单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneOrderId");
        eiColumn.setDescName("行车作业清单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneId");
        eiColumn.setDescName("行车编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneName");
        eiColumn.setDescName("行车名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startXPosition");
        eiColumn.setDescName("起始X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startYPosition");
        eiColumn.setDescName("起始X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startZPosition");
        eiColumn.setDescName("起始Y轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endxPosition");
        eiColumn.setDescName("终到X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endyPosition");
        eiColumn.setDescName("终到X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endzPosition");
        eiColumn.setDescName("终到Y轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inboundSequenceId");
        eiColumn.setDescName("抓取流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outboundSequenceId");
        eiColumn.setDescName("释放流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("abnormalFlag");
        eiColumn.setDescName("异常标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("startAreaType");
        eiColumn.setDescName("起始区域类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startAreaCode");
        eiColumn.setDescName("起始区域类型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startAreaName");
        eiColumn.setDescName("起始区域类型名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endAreaType");
        eiColumn.setDescName("终到区域类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endAreaCode");
        eiColumn.setDescName("终到区域类型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endAreaName");
        eiColumn.setDescName("终到区域类型名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossArea");
        eiColumn.setDescName("跨区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);
    }

    /**
     * the constructor
     */
    public Tlids1201() {
        initMetaData();
    }

    /**
     * get the segNo - 系统账套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the craneResultId - 行车实绩单号
     *
     * @return the craneResultId
     */
    public String getCraneResultId() {
        return this.craneResultId;
    }

    /**
     * set the craneResultId - 行车实绩单号
     */
    public void setCraneResultId(String craneResultId) {
        this.craneResultId = craneResultId;
    }

    /**
     * get the craneOrderId - 行车作业清单号
     *
     * @return the craneOrderId
     */
    public String getCraneOrderId() {
        return this.craneOrderId;
    }

    /**
     * set the craneOrderId - 行车作业清单号
     */
    public void setCraneOrderId(String craneOrderId) {
        this.craneOrderId = craneOrderId;
    }

    /**
     * get the craneId - 行车编号
     *
     * @return the craneId
     */
    public String getCraneId() {
        return this.craneId;
    }

    /**
     * set the craneId - 行车编号
     */
    public void setCraneId(String craneId) {
        this.craneId = craneId;
    }

    /**
     * get the craneName - 行车名称
     *
     * @return the craneName
     */
    public String getCraneName() {
        return this.craneName;
    }

    /**
     * set the craneName - 行车名称
     */
    public void setCraneName(String craneName) {
        this.craneName = craneName;
    }

    /**
     * get the startXPosition - 起始X轴坐标
     *
     * @return the startXPosition
     */
    public String getStartXPosition() {
        return this.startXPosition;
    }

    /**
     * set the startXPosition - 起始X轴坐标
     */
    public void setStartXPosition(String startXPosition) {
        this.startXPosition = startXPosition;
    }

    /**
     * get the startYPosition - 起始X轴坐标
     *
     * @return the startYPosition
     */
    public String getStartYPosition() {
        return this.startYPosition;
    }

    /**
     * set the startYPosition - 起始X轴坐标
     */
    public void setStartYPosition(String startYPosition) {
        this.startYPosition = startYPosition;
    }

    /**
     * get the startZPosition - 起始Y轴坐标
     *
     * @return the startZPosition
     */
    public String getStartZPosition() {
        return this.startZPosition;
    }

    /**
     * set the startZPosition - 起始Y轴坐标
     */
    public void setStartZPosition(String startZPosition) {
        this.startZPosition = startZPosition;
    }

    /**
     * get the endxPosition - 终到X轴坐标
     *
     * @return the endxPosition
     */
    public String getEndxPosition() {
        return this.endxPosition;
    }

    /**
     * set the endxPosition - 终到X轴坐标
     */
    public void setEndxPosition(String endxPosition) {
        this.endxPosition = endxPosition;
    }

    /**
     * get the endyPosition - 终到X轴坐标
     *
     * @return the endyPosition
     */
    public String getEndyPosition() {
        return this.endyPosition;
    }

    /**
     * set the endyPosition - 终到X轴坐标
     */
    public void setEndyPosition(String endyPosition) {
        this.endyPosition = endyPosition;
    }

    /**
     * get the endzPosition - 终到Y轴坐标
     *
     * @return the endzPosition
     */
    public String getEndzPosition() {
        return this.endzPosition;
    }

    /**
     * set the endzPosition - 终到Y轴坐标
     */
    public void setEndzPosition(String endzPosition) {
        this.endzPosition = endzPosition;
    }

    /**
     * get the inboundSequenceId - 抓取流水号
     *
     * @return the inboundSequenceId
     */
    public String getInboundSequenceId() {
        return this.inboundSequenceId;
    }

    /**
     * set the inboundSequenceId - 抓取流水号
     */
    public void setInboundSequenceId(String inboundSequenceId) {
        this.inboundSequenceId = inboundSequenceId;
    }

    /**
     * get the outboundSequenceId - 释放流水号
     *
     * @return the outboundSequenceId
     */
    public String getOutboundSequenceId() {
        return this.outboundSequenceId;
    }

    /**
     * set the outboundSequenceId - 释放流水号
     */
    public void setOutboundSequenceId(String outboundSequenceId) {
        this.outboundSequenceId = outboundSequenceId;
    }

    /**
     * get the abnormalFlag - 异常标记
     *
     * @return the abnormalFlag
     */
    public String getAbnormalFlag() {
        return this.abnormalFlag;
    }

    /**
     * set the abnormalFlag - 异常标记
     */
    public void setAbnormalFlag(String abnormalFlag) {
        this.abnormalFlag = abnormalFlag;
    }

    /**
     * get the status - 状态
     *
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the tenantUser - 租户
     *
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the uuid - ID
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }


    /**
     * get the startAreaType - 起始区域类型
     *
     * @return the startAreaType
     */
    public String getStartAreaType() {
        return this.startAreaType;
    }

    /**
     * set the startAreaType - 起始区域类型
     */
    public void setStartAreaType(String startAreaType) {
        this.startAreaType = startAreaType;
    }

    /**
     * get the startAreaCode - 起始区域类型代码
     *
     * @return the startAreaCode
     */
    public String getStartAreaCode() {
        return this.startAreaCode;
    }

    /**
     * set the startAreaCode - 起始区域类型代码
     */
    public void setStartAreaCode(String startAreaCode) {
        this.startAreaCode = startAreaCode;
    }

    /**
     * get the startAreaName - 起始区域类型名称
     *
     * @return the startAreaName
     */
    public String getStartAreaName() {
        return this.startAreaName;
    }

    /**
     * set the startAreaName - 起始区域类型名称
     */
    public void setStartAreaName(String startAreaName) {
        this.startAreaName = startAreaName;
    }

    /**
     * get the endAreaType - 终到区域类型
     *
     * @return the endAreaType
     */
    public String getEndAreaType() {
        return this.endAreaType;
    }

    /**
     * set the endAreaType - 终到区域类型
     */
    public void setEndAreaType(String endAreaType) {
        this.endAreaType = endAreaType;
    }

    /**
     * get the endAreaCode - 终到区域类型代码
     *
     * @return the endAreaCode
     */
    public String getEndAreaCode() {
        return this.endAreaCode;
    }

    /**
     * set the endAreaCode - 终到区域类型代码
     */
    public void setEndAreaCode(String endAreaCode) {
        this.endAreaCode = endAreaCode;
    }

    /**
     * get the endAreaName - 终到区域类型名称
     *
     * @return the endAreaName
     */
    public String getEndAreaName() {
        return this.endAreaName;
    }

    /**
     * set the endAreaName - 终到区域类型名称
     */
    public void setEndAreaName(String endAreaName) {
        this.endAreaName = endAreaName;
    }

    public String getCrossArea() {
        return crossArea;
    }

    public void setCrossArea(String crossArea) {
        this.crossArea = crossArea;
    }

    public String getFactoryArea() {
        return factoryArea;
    }

    public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
    }

    public String getFactoryBuilding() {
        return factoryBuilding;
    }

    public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setCraneResultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneResultId")), craneResultId));
        setCraneOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneOrderId")), craneOrderId));
        setCraneId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneId")), craneId));
        setCraneName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneName")), craneName));
        setStartXPosition(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startXPosition")), startXPosition));
        setStartYPosition(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startYPosition")), startYPosition));
        setStartZPosition(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startZPosition")), startZPosition));
        setEndxPosition(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endxPosition")), endxPosition));
        setEndyPosition(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endyPosition")), endyPosition));
        setEndzPosition(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endzPosition")), endzPosition));
        setInboundSequenceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inboundSequenceId")), inboundSequenceId));
        setOutboundSequenceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outboundSequenceId")), outboundSequenceId));
        setAbnormalFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("abnormalFlag")), abnormalFlag));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setStartAreaType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startAreaType")), startAreaType));
        setStartAreaCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startAreaCode")), startAreaCode));
        setStartAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startAreaName")), startAreaName));
        setEndAreaType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endAreaType")), endAreaType));
        setEndAreaCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endAreaCode")), endAreaCode));
        setEndAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endAreaName")), endAreaName));
        setCrossArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossArea")), crossArea));
        setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("craneResultId", StringUtils.toString(craneResultId, eiMetadata.getMeta("craneResultId")));
        map.put("craneOrderId", StringUtils.toString(craneOrderId, eiMetadata.getMeta("craneOrderId")));
        map.put("craneId", StringUtils.toString(craneId, eiMetadata.getMeta("craneId")));
        map.put("craneName", StringUtils.toString(craneName, eiMetadata.getMeta("craneName")));
        map.put("startXPosition", StringUtils.toString(startXPosition, eiMetadata.getMeta("startXPosition")));
        map.put("startYPosition", StringUtils.toString(startYPosition, eiMetadata.getMeta("startYPosition")));
        map.put("startZPosition", StringUtils.toString(startZPosition, eiMetadata.getMeta("startZPosition")));
        map.put("endxPosition", StringUtils.toString(endxPosition, eiMetadata.getMeta("endxPosition")));
        map.put("endyPosition", StringUtils.toString(endyPosition, eiMetadata.getMeta("endyPosition")));
        map.put("endzPosition", StringUtils.toString(endzPosition, eiMetadata.getMeta("endzPosition")));
        map.put("inboundSequenceId", StringUtils.toString(inboundSequenceId, eiMetadata.getMeta("inboundSequenceId")));
        map.put("outboundSequenceId", StringUtils.toString(outboundSequenceId, eiMetadata.getMeta("outboundSequenceId")));
        map.put("abnormalFlag", StringUtils.toString(abnormalFlag, eiMetadata.getMeta("abnormalFlag")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("startAreaType", StringUtils.toString(startAreaType, eiMetadata.getMeta("startAreaType")));
        map.put("startAreaCode", StringUtils.toString(startAreaCode, eiMetadata.getMeta("startAreaCode")));
        map.put("startAreaName", StringUtils.toString(startAreaName, eiMetadata.getMeta("startAreaName")));
        map.put("endAreaType", StringUtils.toString(endAreaType, eiMetadata.getMeta("endAreaType")));
        map.put("endAreaCode", StringUtils.toString(endAreaCode, eiMetadata.getMeta("endAreaCode")));
        map.put("endAreaName", StringUtils.toString(endAreaName, eiMetadata.getMeta("endAreaName")));
        map.put("crossArea", StringUtils.toString(crossArea, eiMetadata.getMeta("crossArea")));
        map.put("factoryArea", StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));

        return map;

    }
}