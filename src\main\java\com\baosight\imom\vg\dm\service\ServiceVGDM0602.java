package com.baosight.imom.vg.dm.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.vg.dm.domain.VGDM0602;

/**
 * <AUTHOR> yzj
 * @Description : 设备故障知识库页面后台
 * @Date : 2024/8/26
 * @Version : 1.0
 */
public class ServiceVGDM0602 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0602.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0602().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        // 查询未删除数据
        inInfo.setCell(EiConstant.queryBlock, 0, "delFlag", "0");
        return super.query(inInfo, VGDM0602.QUERY, new VGDM0602());
    }

}
