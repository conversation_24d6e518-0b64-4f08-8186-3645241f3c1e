<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-06 16:57:39
       Version :  1.0
    tableName :${meliSchema}.tlirl0105
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     TYPE_OF_HANDLING  VARCHAR   NOT NULL,
     RESERVATION_MAX_NUM  BIGINT   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_<PERSON>AG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0105">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="typeOfHandling">
            a.TYPE_OF_HANDLING = #typeOfHandling#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationIdentity">
            RESERVATION_IDENTITY = #reservationIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerIdNotNull">
            CUSTOMER_ID != ''
            and CUSTOMER_ID is not null
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="typeOfHandlingNotNull">
            TYPE_OF_HANDLING != ''
            and TYPE_OF_HANDLING is not null
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            a.CUSTOMER_ID = #customerId2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            a.CUSTOMER_NAME = #customerName2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationMaxNum">
            a.RESERVATION_MAX_NUM = #reservationMaxNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            a.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID like concat('%',#uuid#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            a.TENANT_ID = #tenantId#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(a.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(a.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="mergeTags">
            a.MERGE_TAGS = #mergeTags#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0105">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
        a.TYPE_OF_HANDLING as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        RESERVATION_IDENTITY	as "reservationIdentity",  <!-- 预约身份(包含承运商和客户) -->
        CUSTOMER_ID	as "customerId",  <!-- 承运商/客户代码 -->
        CUSTOMER_NAME	as "customerName",  <!-- 承运商/客户名称 -->
        a.RESERVATION_MAX_NUM as "reservationMaxNum",  <!-- 预约最大数 -->
        a.RESERVATION_MAX_NUM_DAY as "reservationMaxNumDay",  <!-- 预约最大数 -->
        a.MERGE_TAGS	as "mergeTags",  <!-- 合并标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0105 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryReservesion" resultClass="String">
        select RESERVATION_TIME as "reservationTime"
        from meli.tlirl0106 tlirl0106
        where SEG_NO = #segNo#
          AND STATUS = '20'
          AND WEEK_DAYS=#weekDays#
        ORDER BY RESERVATION_TIME ASC
        LIMIT 1
    </select>
    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0105 a WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="typeOfHandling">
            TYPE_OF_HANDLING = #typeOfHandling#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationMaxNum">
            RESERVATION_MAX_NUM = #reservationMaxNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0105 (SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        STATUS,  <!-- 状态(撤销：00、新增：10、生效：20) -->
        TYPE_OF_HANDLING,  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        RESERVATION_IDENTITY,  <!-- 预约身份(包含承运商和客户) -->
        CUSTOMER_ID,  <!-- 承运商/客户代码 -->
        CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
        RESERVATION_MAX_NUM,  <!-- 预约最大数 -->
        RESERVATION_MAX_NUM_DAY,  <!-- 预约最大数 -->
        MERGE_TAGS,  <!-- 合并标记 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID  <!-- 租户ID -->
        )
        VALUES (#segNo#, #unitCode#, #status#, #typeOfHandling#, #reservationIdentity#, #customerId#, #customerName#, #reservationMaxNum#,#reservationMaxNumDay#, #mergeTags#, #recCreator#, #recCreatorName#,
        #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#, #remark#, #uuid#, #tenantId#)
    </insert>

    <delete id="delete">
        UPDATE ${meliSchema}.tlirl0105
        SET
        STATUS = #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#   <!-- 记录删除标记 -->
        WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0105
        SET
        STATUS = #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->
        TYPE_OF_HANDLING = #typeOfHandling#,   <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        RESERVATION_IDENTITY	= #reservationIdentity#,   <!-- 预约身份(包含承运商和客户) -->
        CUSTOMER_ID	= #customerId#,   <!-- 承运商/客户代码 -->
        CUSTOMER_NAME	= #customerName#,   <!-- 承运商/客户名称 -->
        RESERVATION_MAX_NUM = #reservationMaxNum#,   <!-- 预约最大数 -->
        RESERVATION_MAX_NUM_DAY = #reservationMaxNumDay#,   <!-- 预约最大数 -->
        MERGE_TAGS	= #mergeTags#,   <!-- 合并标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#   <!-- 备注 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateStatus">
        UPDATE ${meliSchema}.tlirl0105
        SET
        STATUS = #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        UUID = #uuid#
    </update>

    <select id="queryReservationMaxNum" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        sum(a.RESERVATION_MAX_NUM) as "reservationMaxNum"  <!-- 预约最大数 -->
        FROM ${meliSchema}.tlirl0105 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
    </select>

    <select id="queryTypeOfHandling" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.TYPE_OF_HANDLING as "typeOfHandling"  <!-- 预约最大数 -->
        FROM ${meliSchema}.tlirl0105 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
    </select>

</sqlMap>