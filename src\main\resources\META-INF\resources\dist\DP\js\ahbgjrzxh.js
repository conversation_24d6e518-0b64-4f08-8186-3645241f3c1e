var queryData = {
    segNo: 'KF000000',
    time: getTime(),
    type: '4'
}

$(document).ready(function () {
    $('.left ul').on('click', 'li', function () {
        $('ul li').removeClass('checked');
        $(this).addClass('checked');
        if ($(this).index() == 0) {
            queryData = {
                segNo: 'KF000000',
                time: '',
                type: '1'
            }
        } else if ($(this).index() == 1) {
            queryData = {
                segNo: 'KF000000',
                time: getTime(),
                type: '2'
            }
        } else if ($(this).index() == 2) {
            queryData = {
                segNo: 'KF000000',
                time: getTime('2'),
                type: '3'
            }
        } else if ($(this).index() == 3) {
            queryData = {
                segNo: 'KF000000',
                time: getTime(),
                type: '4'
            }
        }

        queryData = {
            ...queryData,
            serviceId: 'S_LI_RL_0048',
        }
        initData(queryData); //加载数据
    });
    queryData = {
        ...queryData,
        serviceId: 'S_LI_RL_0048',
    }
    initData(queryData); //加载数据
});

function getTime(type) {
    // 获取当前日期
    var currentDate = new Date();

    var yesterdayDate = new Date(currentDate);
    if (type == '2') {
        yesterdayDate.setDate(currentDate.getDate() + 1);
    } else {
        yesterdayDate.setDate(currentDate.getDate());
    }
    // 获取年、月、日
    var year = yesterdayDate.getFullYear();
    var month = (yesterdayDate.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以需要+1
    var day = yesterdayDate.getDate().toString().padStart(2, '0');

    // 拼接成YYYYMMDD格式
    return year + month + day
}

function initData(queryData) {
    showLoading('查询中');
    $.ajax({
        type: "post",
        contentType: "application/json",
        url: ytjServerUrl,
        data: JSON.stringify(queryData),
        success: function (data) {
            closeLoading();
            allData(data)
        }
    });
    //页面每隔20秒自动刷新
    slidRow();
}

function allData(data) {
    let str = ''
    let str1 = ''
    $('.center').html('')
    $('.right-bottom table').html('')
    let handTypeColor = ''
    let cheduiColor = ''
    if (data.listWork) {
        // 预约单信息
        data.listWork.forEach((item, index) => { // 遍历data数组
            // 构建每个列表的顶部信息
            str += `
            <div class="center-list">
                <div class="center-top">
                    <div class="time">预约时段：${item.time}</div>
                    <div class="num">${item.list.length}项</div>
                </div>
        `;

            // 构建列表项的具体内容
            item.list.forEach(subItem => {

                const transortType = "";
                let timer = subItem.checkDate ?? '';
                timer = timer.substring(0, timer.length - 2).replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/, "$1年$2月$3日 $4:$5");
                str += `
                <div class="center-list-list">
                    <div class="line">
                        <div class="text" style="color: black;font-size: 16px;">${subItem.vehicleNo || ''}</div>
                    </div>
                    <div class="line">
                        <div class="text">登记时间</div>
                        <div class="value">${timer || ''}</div>
                    </div>
                    `;

                let handTypeFlag = 0;
                let fontColor = 'color: #ffffff';
                switch (subItem.handTypeName) {
                    case '卸货':
                    case '资材卸货':
                    case '周转架':
                    case '原料卸货':
                        handTypeColor = "background: #0052d9;";
                        handTypeFlag = 0;
                        break;
                    case '装货':
                    case '废料提货':
                    case '欧冶提货':
                        handTypeColor = "background: #69c0ff;";
                        handTypeFlag = 1;
                        fontColor = 'color: #004b8f;';
                        break;
                    default:
                        handTypeColor = "background: #e37318;"
                        handTypeFlag = 2;
                        break;
                }
                str += `
                    <div class="line">
                        <div class="text">业务类型</div>
                        <div class="value" style="background: #e37318;color:#FFFFFF;">${subItem.handTypeName ?? ''}</div>
                    </div>
                    `;

                const { startOfTransport, purposeOfTransport } = subItem;
                if (handTypeFlag == 0) {
                    str += `
                        <div class="line">
                            <div class="text">运输起始地</div>
                            <div class="value">${startOfTransport ?? ''}</div>
                        </div>
                        `;
                } else if (handTypeFlag == 1) {
                    str += `
                        <div class="line">
                            <div class="text">运输目的地</div>
                            <div class="value">${purposeOfTransport ?? ''}</div>
                        </div>
                        `;
                } else {
                    str += `
                        <div class="line">
                            <div class="text">运输目的地</div>
                            <div class="value">${purposeOfTransport ?? ''}</div>
                        </div>
                        <div class="line">
                            <div class="text">运输起始地</div>
                            <div class="value">${startOfTransport ?? ''}</div>
                        </div>
                        `;
                }
                // switch (subItem.chedui) {
                //     case '上海联达':
                //         cheduiColor = "background: #fbdbff;"
                //         break;
                //     case '同行物流':
                //         cheduiColor = "background: #fbdbff;"
                //         break;
                //     case '欧冶物流':
                //         cheduiColor = "background: #ffdcdb;"
                //         break;
                //     case '光泽物流':
                //         cheduiColor = "background: #d6f1ff;"
                //         break;
                // }
                //     str +=`
                //         <div class="line">
                //             <div class="text">所属车队（必填）</div>
                //             <div class="value" style="${cheduiColor}">${subItem.chedui}</div>
                //         </div>
                //         `
                str += `
                    <div class="line">
                        <div class="text">司机姓名</div>
                        <div class="value">${subItem.driverName || ''}</div>
                    </div>
                    <div class="line">
                        <div class="text">司机手机号</div>
                        <div class="value">${subItem.telNum || ''}</div>
                    </div>
                    <div class="line">
                        <div class="text">客户</div>
                        <div class="value">${subItem.customerName || ''}</div>
                    </div>
            `;

                // 仅当subItem.type不为空时，才添加这一行
                if (subItem.complete == '10') {
                    str += `
                    <div class="line">
                        <div class="text">是否完成装卸货</div>
                        <div class="value">已完成</div>
                    </div>
                `;
                }

                // 关闭.center-list-list div
                str += `
                </div>
            `;
            });

            // 关闭.center-list div
            str += `
            </div>
        `;
        });
    }

    if (data.listOut) {
        // 离场车辆信息
        data.listOut.forEach((item, index) => {
            item.list.forEach(subItem => {
                str1 += `
                <tr class="pending">
                    <td>${subItem.vehicleNo || ''}</td>
                    <td>${subItem.handType || ''}</td>
                    <td>${subItem.purposeOfTransport || ''}</td>
                </tr>
            `
            })
        })
    }

    $('.center').append(str)
    $('.right-bottom table').append(str1)
}

function slidRow() {
    // setTimeout(function() { initData(queryData)}, 20000);
}