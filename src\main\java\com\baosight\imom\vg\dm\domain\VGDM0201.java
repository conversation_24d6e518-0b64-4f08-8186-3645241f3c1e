package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0201;

/**
 * 点检标准表
 */
public class VGDM0201 extends Tvgdm0201 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0201.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0201.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0201.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0201.update";
    /**
     * 修改状态信息（审批相关）
     */
    public static final String UPDATE_STATUS = "VGDM0201.updateStatus";
    /**
     * 数据重复性校验
     */
    public static final String COUNT_REPEAT = "VGDM0201.countRepeat";

    @Override
    public String getCheckStatus() {
        return this.getSpotCheckStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

}
