$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    var addsubwindow = $("#ADDSUBWINDOW");

    // TODO 查询 按钮事件
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });


    $("#SUB_QUERY").on("click", function (e) {
        sub_resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "applyDept",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "部门代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "ADDSUBWINDOW",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "部门代码"
                            })
                        }
                    }
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // 获取勾选数据，
                $("#INSERTSAVEN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }

                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0110", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("确认失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0110", "CONFIRM", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("反确认失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0110", "CONFIRMNO", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    if (e.field === "remark") { //不是新增行,业务单元代码不能修改
                        NotificationUtil({msg: "备注不可修改!"}, "error");
                        // 判断单元格 field 禁止编辑
                        e.preventDefault();
                    }
                    if (e.field === "unitCode") { //不是新增行,业务单元代码不能修改
                        NotificationUtil({msg: "业务单元代码不可修改!"}, "error");
                        // 判断单元格 field 禁止编辑
                        e.preventDefault();
                    }
                }
            },
        },
        "result2": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo02",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "applyDept",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "部门代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "ADDSUBWINDOW",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "部门代码"
                            })
                        }
                    }
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // 获取勾选数据，
                $("#INSERTSAVEN2").on("click", function (e) {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result2");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0105", "insert2", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                result2Grid.dataSource.page(1);
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                // 获取勾选数据，
                $("#UPDATESAVE2").on("click", function (e) {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("修改失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result2");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0105", "update2", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                result2Grid.dataSource.page(1);
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                // 获取勾选数据，
                $("#DELETE2").on("click", function (e) {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("删除失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result2");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0105", "delete2", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                result2Grid.dataSource.page(1);
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                /**
                 * 确认
                 */
                $("#CONFIRM2").click(function () {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("确认失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result2");
                    IMOMUtil.submitGridsData("result2", "LIRL0105", "CONFIRM2", true, function (e) {
                        result2Grid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO2").click(function () {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("反确认失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result2");
                    IMOMUtil.submitGridsData("result2", "LIRL0105", "CONFIRMNO2", true, function (e) {
                        result2Grid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    if (e.field === "remark") { //不是新增行,业务单元代码不能修改
                        NotificationUtil({msg: "备注不可修改!"}, "error");
                        // 判断单元格 field 禁止编辑
                        e.preventDefault();
                    }
                    if (e.field === "unitCode") { //不是新增行,业务单元代码不能修改
                        NotificationUtil({msg: "业务单元代码不可修改!"}, "error");
                        // 判断单元格 field 禁止编辑
                        e.preventDefault();
                    }
                }
            },
        },
        "sub_result":{
            onRowDblClick: function (e) {
                /*$("#inqu_status-0-userNum").val(e.model.userNum)
                $("#inqu_status-0-userName").val(e.model.chineseUserName)*/
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    varModel.applyDept = e.model.segNo;
                    varModel.applyDeptName = e.model.segName;
                }
                resultGrid.refresh();
                //关闭弹出框
                ADDSUBWINDOWWindow.close();

            }
        },
    };
    IPLATUI.EFWindow = {
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
        "unitInfo2": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo2");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu2_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu2_status-0-segNo").val(row[0].segNo);
                    $("#inqu2_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
        "unitInfo01": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo01");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = resultGrid.getCheckedRows(),
                        eiInfo = new EiInfo();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = resultGrid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "unitInfo02": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo02Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo02");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo02Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = result2Grid.getCheckedRows(),
                        eiInfo = new EiInfo();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = result2Grid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    result2Grid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                var segNo = $("#inqu2_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu2_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu2_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu2_status-0-customerId").val(row[0].userNum);
                    $("#inqu2_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "userNum2": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum2");
                var segNo = $("#inqu2_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu2_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu2_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu2_status-0-customerId2").val(row[0].userNum);
                    $("#inqu2_status-0-customerName2").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "ADDSUBWINDOW": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_resultGrid.getDataItems().length > 0) {
                    sub_resultGrid.removeRows(sub_resultGrid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    $("#sub_query_status-0-unitCode").val(checkRows[0].unitCode);
                    $("#sub_query_status-0-segNo").val(checkRows[0].segNo);
                }
            }
        },
    }

});
