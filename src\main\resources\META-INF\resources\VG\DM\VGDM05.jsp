<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="清单信息" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-exceptionContactId" cname="异常联络单号" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFSelect ename="inqu_status-0-exceptionSource" cname="异常来源" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P022"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                                     ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

                    <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false"
                                     containerId="deviceInfoMainQuery" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>

                </div>
                <div class="row">
                    <EF:EFSelect ename="inqu_status-0-exceptionStatus" cname="状态" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P016"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="inqu_status-0-spotCheckImplemente" cname="实施方" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P048"/>
                    </EF:EFSelect>
                    <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                                   endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                                   startCname="创建时间(起)" endCname="创建时间(止)"
                                   ratio="3:3" format="yyyy-MM-dd">
                    </EF:EFDateSpan>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="查询结果">
                <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="exceptionContactId" cname="异常联络单号" enable="false" width="120"
                                 align="center"/>
                    <EF:EFComboColumn ename="exceptionStatus" cname="状态" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P016"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="exceptionSource" cname="异常来源" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P022"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
                    <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
                    <EF:EFComboColumn ename="spotCheckImplemente" cname="实施方" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P048"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="actualsRemark" cname="点检实绩" width="200"/>
                    <EF:EFColumn ename="procResult" cname="处理结果"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="详细信息" id="info-2">
            <EF:EFRegion id="detail" title="详细信息">
                <EF:EFInput ename="detail_status-0-uuid" cname="UUID" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="unitInfo01" center="true"
                                     ename="detail_status-0-unitCode" cname="业务单元代码" colWidth="3"/>
                    <EF:EFSelect readonly="true" ename="detail_status-0-segNo" cname="业务单元简称" colWidth="3">
                        <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="equipmentInfo" center="true"
                                     ename="detail_status-0-equipmentName" cname="设备名称" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-deviceCode" readonly="true" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="deviceInfo" center="true" ename="detail_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>
                    <EF:EFSelect required="true" ename="detail_status-0-exceptionSource" cname="异常来源" colWidth="3">
                        <EF:EFCodeOption codeName="P022"/>
                    </EF:EFSelect>
                    <EF:EFSelect required="true" ename="detail_status-0-spotCheckImplemente" cname="实施方"
                                 colWidth="3">
                        <EF:EFCodeOption codeName="P048"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-spotCheckContent" cname="点检内容" colWidth="6"
                                ratio="2:10" minLength="1" maxLength="200"/>
                    <EF:EFInput required="true" ename="detail_status-0-judgmentStandard" cname="判断标准" colWidth="6"
                                ratio="2:10" minLength="1" maxLength="200"/>
                </div>
                <div class="row">
                    <EF:EFSelect required="true" ename="detail_status-0-spotCheckMethod" cname="点检方法"
                                 colWidth="3">
                        <EF:EFCodeOption codeName="P049"/>
                    </EF:EFSelect>
                    <EF:EFSelect required="true" ename="detail_status-0-spotCheckStandardType" cname="标准类型"
                                 colWidth="3">
                        <EF:EFCodeOption codeName="P051"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="detail_status-0-isFaultHandle" cname="是否故障处理" colWidth="3">
                        <EF:EFOption label="" value=""/>
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="detail_status-0-isOverhaulHandle" cname="是否检修处理" colWidth="3">
                        <EF:EFOption label="" value=""/>
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-measureId" cname="计量单位" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-upperLimit" cname="上限值" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-lowerLimit" cname="下限值" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-temporaryMeasures" cname="临时措施" colWidth="6" ratio="2:10"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-actualsRemark" cname="点检实绩" colWidth="12"
                                ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-handleMeasures" cname="处理措施" colWidth="12"
                                ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-procResult" cname="处理结果" colWidth="12"
                                ratio="1:11" type="textarea" minLength="1" maxLength="200"/>
                </div>
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>
