package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0101;

import java.util.Map;

/**
 * 厂区区域管理
 */
public class LIDS0101 extends Tlids0101 {
    public static final String SEQ_ID = "TLIDS0101_SEQ01";

    public static final String QUERY = "LIDS0101.query";
    public static final String QUERY_TO_MAP = "LIDS0101.queryToMap";
    public static final String COUNT = "LIDS0101.count";
    public static final String COUNT_UUID = "LIDS0101.count_uuid";
    public static final String INSERT = "LIDS0101.insert";
    public static final String UPDATE = "LIDS0101.update";
    public static final String UPDATE_STATUS = "LIDS0101.updateStatus";
    public static final String DELETE = "LIDS0101.delete";

    public static final String QUERY_AREA = "LIDS0101.query_area";
    public static final String COUNT_EQUAL = "LIDS0101.countEqual";
    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS0101() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }

}
