$(function () {

    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {

            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu2_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            }
        }
    }

})