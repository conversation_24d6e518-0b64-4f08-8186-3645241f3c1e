<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0804">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            UUID != #notUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventoryUuid">
            INVENTORY_UUID = #inventoryUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stuffReceivingStatus">
            STUFF_RECEIVING_STATUS = #stuffReceivingStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="stuffReceivingStatus">
            STUFF_RECEIVING_STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="faultId">
            FAULT_ID = #faultId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="likeFaultId">
            FAULT_ID like concat('%',#likeFaultId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="likeVoucherNum">
            VOUCHER_NUM like concat('%',#likeVoucherNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stuffCode">
            STUFF_CODE like concat('%',#stuffCode#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stuffName">
            STUFF_NAME like concat('%',#stuffName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="faultIdList">
            FAULT_ID IN
            <iterate property="faultIdList" open="(" close=")"
                     conjunction=",">
                #faultIdList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="voucherNumList">
            VOUCHER_NUM IN
            <iterate property="voucherNumList" open="(" close=")"
                     conjunction=",">
                #voucherNumList[]#
            </iterate>
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0804">
        SELECT
        STUFF_RECEIVING_STATUS as "stuffReceivingStatus",  <!-- 资材领用单状态 -->
        DEPT_ID as "deptId",  <!-- 部门 -->
        DEPT_NAME as "deptName",  <!-- 部门名称 -->
        FAULT_ID as "faultId",  <!-- 故障编号 -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        PUR_CONTRACT_NUM as "purContractNum",  <!-- 采购合同号 -->
        PUR_ORDER_NUM as "purOrderNum",  <!-- 采购合同子项号 -->
        STUFF_CODE as "stuffCode",  <!-- 资材代码 -->
        STUFF_NAME as "stuffName",  <!-- 资材名称 -->
        SPEC_DESC as "specDesc",  <!-- 规格 -->
        STUFF_USAGE as "stuffUsage",  <!-- 用途 -->
        MEASURE_ID as "measureId",  <!-- 计量单位 -->
        USING_WGT as "usingWgt",  <!-- 申请量 -->
        INVENTORY_UUID as "inventoryUuid",  <!-- 库存id -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        UNIT_PRICE as "unitPrice"  <!-- 单价 -->
        FROM ${mevgSchema}.TVGDM0804 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0804 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryByOverhaulPlan" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T1.UUID as "uuid",  <!-- 唯一编码 -->
        T1.VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        T1.DEPT_ID as "deptId",  <!-- 部门 -->
        T1.DEPT_NAME as "deptName",  <!-- 部门名称 -->
        T1.WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        T1.WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        T1.LOCATION_ID as "locationId",  <!-- 库位代码 -->
        T1.LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        T1.PUR_CONTRACT_NUM as "purContractNum",  <!-- 采购合同号 -->
        T1.PUR_ORDER_NUM as "purOrderNum",  <!-- 采购合同子项号 -->
        T1.STUFF_CODE as "stuffCode",  <!-- 资材代码 -->
        T1.USING_WGT as "usingWgt",  <!-- 申请量 -->
        T1.REC_CREATOR as "applyId",  <!-- 记录创建责任者 -->
        T1.REC_CREATOR_NAME as "applyName",  <!-- 记录创建人姓名 -->
        T1.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T1.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        T2.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T2.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        IFNULL(T3.MACHINE_CODE, '') as "requisitionUnit",  <!-- 机组代码 -->
        IFNULL(T3.MACHINE_NAME, '') as "requisitionUnitName"  <!-- 机组名称 -->
        FROM ${mevgSchema}.TVGDM0804 T1
        LEFT JOIN ${mevgSchema}.TVGDM0801 T2 ON T1.VOUCHER_NUM = T2.OVERHAUL_PLAN_ID
        AND T1.SEG_NO = T2.SEG_NO
        LEFT JOIN ${mevgSchema}.TVGDM0106 T3 ON T2.E_ARCHIVES_NO = T3.E_ARCHIVES_NO
        AND T2.SEG_NO = T3.SEG_NO
        AND T3.DEL_FLAG = '0'
        WHERE
        T1.STUFF_RECEIVING_STATUS = '10'
        AND T1.VOUCHER_NUM IN
        <iterate property="voucherNumList" open="(" close=")"
                 conjunction=",">
            #voucherNumList[]#
        </iterate>
    </select>

    <select id="queryByFault" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T1.UUID as "uuid",  <!-- 唯一编码 -->
        T1.VOUCHER_NUM as "voucherNum",  <!-- 依据凭单 -->
        T1.DEPT_ID as "deptId",  <!-- 部门 -->
        T1.DEPT_NAME as "deptName",  <!-- 部门名称 -->
        T1.WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        T1.WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        T1.LOCATION_ID as "locationId",  <!-- 库位代码 -->
        T1.LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        T1.PUR_CONTRACT_NUM as "purContractNum",  <!-- 采购合同号 -->
        T1.PUR_ORDER_NUM as "purOrderNum",  <!-- 采购合同子项号 -->
        T1.STUFF_CODE as "stuffCode",  <!-- 资材代码 -->
        T1.USING_WGT as "usingWgt",  <!-- 申请量 -->
        T1.REC_CREATOR as "applyId",  <!-- 记录创建责任者 -->
        T1.REC_CREATOR_NAME as "applyName",  <!-- 记录创建人姓名 -->
        T1.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T1.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        T2.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T2.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        IFNULL(T3.MACHINE_CODE, '') as "requisitionUnit",  <!-- 机组代码 -->
        IFNULL(T3.MACHINE_NAME, '') as "requisitionUnitName"  <!-- 机组名称 -->
        FROM ${mevgSchema}.TVGDM0804 T1
        LEFT JOIN ${mevgSchema}.TVGDM0701 T2 ON T1.FAULT_ID = T2.FAULT_ID
        AND T1.SEG_NO = T2.SEG_NO
        LEFT JOIN ${mevgSchema}.TVGDM0106 T3 ON T2.E_ARCHIVES_NO = T3.E_ARCHIVES_NO
        AND T2.SEG_NO = T3.SEG_NO
        AND T3.DEL_FLAG = '0'
        WHERE
        T1.STUFF_RECEIVING_STATUS = '10'
        AND T1.FAULT_ID IN
        <iterate property="faultIdList" open="(" close=")"
                 conjunction=",">
            #faultIdList[]#
        </iterate>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0804 (STUFF_RECEIVING_STATUS,  <!-- 资材领用单状态 -->
        DEPT_ID,  <!-- 部门 -->
        DEPT_NAME,  <!-- 部门名称 -->
        FAULT_ID,  <!-- 故障编号 -->
        VOUCHER_NUM,  <!-- 依据凭单 -->
        WAREHOUSE_CODE,  <!-- 仓库代码 -->
        WAREHOUSE_NAME,  <!-- 仓库名称 -->
        LOCATION_ID,  <!-- 库位代码 -->
        LOCATION_NAME,  <!-- 库位名称 -->
        PUR_CONTRACT_NUM,  <!-- 采购合同号 -->
        PUR_ORDER_NUM,  <!-- 采购合同子项号 -->
        STUFF_CODE,  <!-- 资材代码 -->
        STUFF_NAME,  <!-- 资材名称 -->
        SPEC_DESC,  <!-- 规格 -->
        STUFF_USAGE,  <!-- 用途 -->
        MEASURE_ID,  <!-- 计量单位 -->
        USING_WGT,  <!-- 申请量 -->
        INVENTORY_UUID,  <!-- 库存id -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        UNIT_PRICE  <!-- 单价 -->
        )
        VALUES (#stuffReceivingStatus#, #deptId#, #deptName#, #faultId#, #voucherNum#, #warehouseCode#, #warehouseName#,
        #locationId#, #locationName#, #purContractNum#, #purOrderNum#, #stuffCode#, #stuffName#, #specDesc#,
        #stuffUsage#, #measureId#, #usingWgt#, #inventoryUuid#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#,
        #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#,
        #unitPrice#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0804 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0804
        SET
        STUFF_RECEIVING_STATUS = #stuffReceivingStatus#,   <!-- 资材领用单状态 -->
        USING_WGT = #usingWgt#,   <!-- 申请量 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateVoucherNum">
        UPDATE ${mevgSchema}.TVGDM0804
        SET
        VOUCHER_NUM = #voucherNum#
        WHERE
        FAULT_ID = #faultId#
        AND DEL_FLAG = '0'
    </update>
</sqlMap>