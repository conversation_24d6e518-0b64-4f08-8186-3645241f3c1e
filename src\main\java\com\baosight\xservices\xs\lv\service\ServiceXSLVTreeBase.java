package com.baosight.xservices.xs.lv.service;

import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ef.ui.tree.TreeService;
import com.baosight.xservices.xs.util.LoginUserDetails;
import org.apache.commons.lang.StringUtils;

import java.util.*;

public class ServiceXSLVTreeBase extends TreeService {
    private EiBlockMeta eiMetadata = null;

    public ServiceXSLVTreeBase() {
    }

    public List getTopNodes() {
        HashMap<String, String> map = new HashMap();
        List<HashMap> list = new ArrayList();
        map.put("label", "root");
        map.put("text", "组织机构");
        map.put("leaf", "0");
        map.put("parent", "0");
        list.add(map);
        return list;
    }

    public List getChildNodes(String parentLabel) {
        Map params = new HashMap();
        String stmt = null;
        String loginName = UserSession.getLoginName();
        if (!StringUtils.isEmpty(parentLabel) && !"$".equals(parentLabel) && !"root".equals(parentLabel)) {
            params.put("node", parentLabel);
            stmt = "XSLVOrg.queryOrganiation";
        } else if (!LoginUserDetails.isUserAdmin(loginName)) {
            params.put("userId", UserSession.getUserUuid());
            String permType = this.getPermType();
            if (permType != null) {
                params.put(permType, "1");
            }

            stmt = "XSLVOrg.queryLevelManagerTopOrg";
        } else {
            params.put("node", "root");
            stmt = "XSLVOrg.queryOrganiation";
        }

        List<Map> ret = this.dao.query(stmt, params);
        return ret;
    }

    public EiInfo search(EiInfo inInfo) {
        String stmt = null;
        String loginName = UserSession.getLoginName();
        if (!LoginUserDetails.isUserAdmin(loginName)) {
            inInfo.set("inqu_status-0-loginName", loginName);
            inInfo.set("inqu_status-0-userId", UserSession.getUserUuid());
            String permType = this.getPermType();
            if (permType != null) {
                inInfo.set("inqu_status-0-" + permType, "1");
            }

            stmt = "XSLVOrg.search";
        } else {
            stmt = "XSLV0100.search";
        }

        EiInfo outInfo = super.query(inInfo, stmt);
        return outInfo;
    }

    public EiInfo expandPath(EiInfo inInfo) {
        String stmt = null;
        String loginName = UserSession.getLoginName();
        if (!LoginUserDetails.isUserAdmin(loginName)) {
            inInfo.set("inqu_status-0-loginName", loginName);
            String permType = this.getPermType();
            if (permType != null) {
                inInfo.set("inqu_status-0-" + permType, "1");
            }

            stmt = "XSLVOrg.expandPath";
        } else {
            stmt = "XSLV0100.expandPath";
        }

        EiInfo outInfo = super.query(inInfo, stmt);
        return outInfo;
    }

    public EiBlockMeta initMetaData() {
        if (this.eiMetadata == null) {
            this.eiMetadata = new EiBlockMeta();
            EiColumn eiColumn = new EiColumn("label");
            eiColumn.setDescName("label");
            eiColumn.setNullable(false);
            eiColumn.setPrimaryKey(false);
            this.eiMetadata.addMeta(eiColumn);
            eiColumn = new EiColumn("parent");
            eiColumn.setDescName("parent");
            eiColumn.setNullable(false);
            eiColumn.setPrimaryKey(false);
            this.eiMetadata.addMeta(eiColumn);
            eiColumn = new EiColumn("text");
            eiColumn.setDescName("text");
            eiColumn.setNullable(false);
            eiColumn.setPrimaryKey(false);
            this.eiMetadata.addMeta(eiColumn);
            eiColumn = new EiColumn("leaf");
            eiColumn.setDescName("leaf");
            eiColumn.setType(EiConstant.COLUMN_TYPE_NUMBER);
            eiColumn.setNullable(false);
            eiColumn.setPrimaryKey(false);
            this.eiMetadata.addMeta(eiColumn);
            eiColumn = new EiColumn("path");
            eiColumn.setDescName("path");
            eiColumn.setNullable(false);
            eiColumn.setPrimaryKey(false);
            this.eiMetadata.addMeta(eiColumn);
        }

        return this.eiMetadata;
    }

    protected String getPermType() {
        return null;
    }

    protected void addUserGroup(String parentLabel, List<Map> ret) {
        Iterator var3 = ret.iterator();

        while(var3.hasNext()) {
            Map map = (Map)var3.next();
            map.put("leaf", "0");
        }

        Map params = new HashMap();
        params.put("parent_id", parentLabel);
        List<Map> userGroup = this.dao.query("XSLVOrg.queryUserGroup", params);
        Iterator var5 = userGroup.iterator();

        while(var5.hasNext()) {
            Map map = (Map)var5.next();
            map.put("icon", "fa fa-users");
        }

        ret.addAll(userGroup);
    }
}
