/**
 * Generate time : 2024-11-21 9:57:21
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm0303
 */
public class Tvgdm0303 extends DaoEPBase {

    private String tagId = " ";        /* 点位ID*/
    private String tagDesc = " ";        /* 点位描述*/
    private String deviceCode = " ";        /* 分部设备代码*/
    private String deviceName = " ";        /* 分部设备名称*/
    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String scadaName = " ";        /* 节点名*/
    private String measureId = " ";        /* 计量单位*/
    private BigDecimal upperLimit = new BigDecimal("0");        /* 上限值*/
    private BigDecimal lowerLimit = new BigDecimal("0");        /* 下限值*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private Integer tagIhdId = 0;        /* 点位数据库id*/
    private String bigScreenFlag = "0";        /* 大屏标记*/
    private String bigScreenKeyFlag = "0";        /* 大屏关键标记*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("tagId");
        eiColumn.setDescName("点位ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagDesc");
        eiColumn.setDescName("点位描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scadaName");
        eiColumn.setDescName("节点名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("measureId");
        eiColumn.setDescName("计量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upperLimit");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("上限值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lowerLimit");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("下限值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagIhdId");
        eiColumn.setType("N");
        eiColumn.setDescName("点位数据库id");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bigScreenFlag");
        eiColumn.setDescName("大屏标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bigScreenKeyFlag");
        eiColumn.setDescName("大屏关键标记");
        eiMetadata.addMeta(eiColumn);
    }

    /**
     * the constructor
     */
    public Tvgdm0303() {
        initMetaData();
    }

    /**
     * get the tagId - 点位ID
     *
     * @return the tagId
     */
    public String getTagId() {
        return this.tagId;
    }

    /**
     * set the tagId - 点位ID
     */
    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    /**
     * get the tagDesc - 点位描述
     *
     * @return the tagDesc
     */
    public String getTagDesc() {
        return this.tagDesc;
    }

    /**
     * set the tagDesc - 点位描述
     */
    public void setTagDesc(String tagDesc) {
        this.tagDesc = tagDesc;
    }

    /**
     * get the deviceCode - 分部设备代码
     *
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     *
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the scadaName - 节点名
     *
     * @return the scadaName
     */
    public String getScadaName() {
        return this.scadaName;
    }

    /**
     * set the scadaName - 节点名
     */
    public void setScadaName(String scadaName) {
        this.scadaName = scadaName;
    }

    /**
     * get the measureId - 计量单位
     *
     * @return the measureId
     */
    public String getMeasureId() {
        return this.measureId;
    }

    /**
     * set the measureId - 计量单位
     */
    public void setMeasureId(String measureId) {
        this.measureId = measureId;
    }

    /**
     * get the upperLimit - 上限值
     *
     * @return the upperLimit
     */
    public BigDecimal getUpperLimit() {
        return this.upperLimit;
    }

    /**
     * set the upperLimit - 上限值
     */
    public void setUpperLimit(BigDecimal upperLimit) {
        this.upperLimit = upperLimit;
    }

    /**
     * get the lowerLimit - 下限值
     *
     * @return the lowerLimit
     */
    public BigDecimal getLowerLimit() {
        return this.lowerLimit;
    }

    /**
     * set the lowerLimit - 下限值
     */
    public void setLowerLimit(BigDecimal lowerLimit) {
        this.lowerLimit = lowerLimit;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the tagIhdId - 点位数据库id
     *
     * @return the tagIhdId
     */
    public Integer getTagIhdId() {
        return this.tagIhdId;
    }

    /**
     * set the tagIhdId - 点位数据库id
     */
    public void setTagIhdId(Integer tagIhdId) {
        this.tagIhdId = tagIhdId;
    }

    /**
     * get the bigScreenFlag - 大屏标记
     *
     * @return the bigScreenFlag
     */
    public String getBigScreenFlag() {
        return this.bigScreenFlag;
    }

    /**
     * set the bigScreenFlag - 大屏标记
     */
    public void setBigScreenFlag(String bigScreenFlag) {
        this.bigScreenFlag = bigScreenFlag;
    }

    /**
     * get the bigScreenKeyFlag - 大屏关键标记
     *
     * @return the bigScreenKeyFlag
     */
    public String getBigScreenKeyFlag() {
        return this.bigScreenKeyFlag;
    }

    /**
     * set the bigScreenKeyFlag - 大屏关键标记
     */
    public void setBigScreenKeyFlag(String bigScreenKeyFlag) {
        this.bigScreenKeyFlag = bigScreenKeyFlag;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setTagId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagId")), tagId));
        setTagDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagDesc")), tagDesc));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setScadaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scadaName")), scadaName));
        setMeasureId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("measureId")), measureId));
        setUpperLimit(NumberUtils.toBigDecimal(StringUtils.toString(map.get("upperLimit")), upperLimit));
        setLowerLimit(NumberUtils.toBigDecimal(StringUtils.toString(map.get("lowerLimit")), lowerLimit));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setTagIhdId(NumberUtils.toInteger(StringUtils.toString(map.get("tagIhdId")), tagIhdId));
        setBigScreenFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bigScreenFlag")), bigScreenFlag));
        setBigScreenKeyFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bigScreenKeyFlag")), bigScreenKeyFlag));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("tagId", StringUtils.toString(tagId, eiMetadata.getMeta("tagId")));
        map.put("tagDesc", StringUtils.toString(tagDesc, eiMetadata.getMeta("tagDesc")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("scadaName", StringUtils.toString(scadaName, eiMetadata.getMeta("scadaName")));
        map.put("measureId", StringUtils.toString(measureId, eiMetadata.getMeta("measureId")));
        map.put("upperLimit", StringUtils.toString(upperLimit, eiMetadata.getMeta("upperLimit")));
        map.put("lowerLimit", StringUtils.toString(lowerLimit, eiMetadata.getMeta("lowerLimit")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("tagIhdId", StringUtils.toString(tagIhdId, eiMetadata.getMeta("tagIhdId")));
        map.put("bigScreenFlag", StringUtils.toString(bigScreenFlag, eiMetadata.getMeta("bigScreenFlag")));
        map.put("bigScreenKeyFlag", StringUtils.toString(bigScreenKeyFlag, eiMetadata.getMeta("bigScreenKeyFlag")));

        return map;

    }
}