/**
 * 项目公共js
 * @param $
 * <AUTHOR> TEL:17762608719  （开发人）.
 * @date 2022年3月26日 上午9:00:00  （开发日期）.
 * @version 1.0 （开发版本）.
 */
(function ($) {
    /**
     * 前端向后端提交数据通用方法
     * @param {String} grid_ids (必传) . 说明：如果是多个用逗号隔开，"result01,result02";如果只提交form表单填"".
     * @param {String} service_name （必传）. 说明：后台service.
     * @param {String} method_name （必传）. 说明：后台方法.
     * @param {Boolean} isRefreshre （必传）. 说明：是否刷新页面数据（false/true）.
     * @param {Function} callback （选传）. 说明：回调函数.
     * @param {Object} eiinfo （选传）. 说明：传入的eiinfo信息.
     * @param {Boolean} isGetForm （选传）[默认传:true]. 说明：是否传入form表单的数据（false/true）.
     */
    var submitGridsData = function (grid_ids, service_name, method_name, isRefreshre, callback, eiinfo, isGetForm) {
        // 点击的按钮
        var $activeElement = $(document.activeElement);
        var _IPLAT = IPLAT;
        $activeElement.attr("disabled", true);
        _IPLAT.progress($("body"), true);

        var eiInfo = __eiInfo;
        var info = eiinfo || new EiInfo();

        if (!IPLAT.isAvailable(isGetForm) || isGetForm === true) {
            info.setByNodeObject(document.body);
        }

        if (IPLAT.isAvailable(grid_ids)) {
            var _grid_ids = grid_ids.split(",");
            for (var j = 0, count = _grid_ids.length; j < count; j++) {
                var grid_id = _grid_ids[j];
                info.addBlock(checkedRows2Block(grid_id));
            }
        }

        // 调用请求 onSuccess 成功回掉函数
        EiCommunicator.send(service_name, method_name, info, {
            onSuccess: function (ei) {
                if (ei.getStatus() >= 0) {
                    if (isRefreshre) {
                        //console.log(EiConstant.EF_CUR_FORM_ENAME);
                        try {
                            IPLAT.fillNode(document.getElementById(eiInfo.extAttr[EiConstant.EF_FORM_ENAME]), ei);
                        } catch (e) {
                            // 发生异常
                            NotificationUtil("操作失败，原因[" + e + "]", "error");
                        }
                        var resultGrid = window[grid_id + "Grid"];
                        resultGrid.dataSource.page(1);
                        //grid_ids.data("kendoGrid").dataSource.page(1).dataSource.page(1);
                    }

                    if (ei.getStatus() == 0) {
                        NotificationUtil(ei, "warning");
                    } else {
                        NotificationUtil(ei);
                    }

                    if (typeof callback === "function") {
                        callback(ei);
                    }
                } else {
                    NotificationUtil(ei, "error");

                    if (typeof callback === "function") {
                        callback(ei);
                    }
                }

                $activeElement.attr("disabled", false);
                _IPLAT.progress($("body"), false);
            },
            onFail: function (ei) {
                // 发生异常
                $activeElement.attr("disabled", false);
                _IPLAT.progress($("body"), false);
                NotificationUtil("操作失败，原因[" + ei + "]", "error");
            }
        });
    };

    /**
     * 前端按向后端提交dom节点数据通用方法
     * @param {jQuery} node (必传) . 说明：页面节点，如$("#detail").
     * @param {String} service_name （必传）. 说明：后台service.
     * @param {String} method_name （必传）. 说明：后台方法.
     * @param {Function} successCallback （选传）. 说明：成功回调函数.
     * @param {Function} failCallback （选传）. 说明：失败回调函数.
     */
    function submitNode(node, service_name, method_name, successCallback, failCallback) {
        // 点击的按钮
        var $activeElement = $(document.activeElement);
        var _IPLAT = IPLAT;
        $activeElement.attr("disabled", true);
        _IPLAT.progress($("body"), true);
        // 调用请求 onSuccess 成功回掉函数
        _IPLAT.submitNode(node, service_name, method_name, {
            onSuccess: function (ei) {
                if (ei.getStatus() < 0) {
                    NotificationUtil(ei, "error");
                    if (typeof failCallback === "function") {
                        failCallback(ei);
                    }
                } else {
                    NotificationUtil(ei, "success");
                    if (typeof successCallback === "function") {
                        successCallback(ei);
                    }
                }
                $activeElement.attr("disabled", false);
                _IPLAT.progress($("body"), false);
            },
            onFail: function (ei) {
                // 发生异常
                $activeElement.attr("disabled", false);
                _IPLAT.progress($("body"), false);
                NotificationUtil("操作失败，原因[" + ei + "]", "error");
            }
        });
    }

    /**
     * 前端向后端提交eiInfo数据通用方法
     * @param {EiInfo} eiInfo (必传) . 说明：EiInfo对象.
     * @param {String} service_name （必传）. 说明：后台service.
     * @param {String} method_name （必传）. 说明：后台方法.
     * @param {Function} successCallback （选传）. 说明：成功回调函数.
     * @param {Function} failCallback （选传）. 说明：失败回调函数.
     */
    function submitEiInfo(eiInfo, service_name, method_name, successCallback, failCallback) {
        // 点击的按钮
        var $activeElement = $(document.activeElement);
        var _IPLAT = IPLAT;
        $activeElement.attr("disabled", true);
        _IPLAT.progress($("body"), true);
        // 调用请求 onSuccess 成功回掉函数
        EiCommunicator.send(service_name, method_name, eiInfo, {
            onSuccess: function (ei) {
                if (ei.getStatus() < 0) {
                    NotificationUtil(ei, "error");
                    if (typeof failCallback === "function") {
                        failCallback(ei);
                    }
                } else {
                    NotificationUtil(ei, "success");
                    if (typeof successCallback === "function") {
                        successCallback(ei);
                    }
                }
                $activeElement.attr("disabled", false);
                _IPLAT.progress($("body"), false);
            },
            onFail: function (ei) {
                // 发生异常
                $activeElement.attr("disabled", false);
                _IPLAT.progress($("body"), false);
                NotificationUtil("操作失败，原因[" + ei + "]", "error");
            }
        });
    }

    /**
     * 将选中的行数据转化为数据块
     * @param grid_id
     * @returns {EiBlock}
     */
    var checkedRows2Block = function (grid_id) {
        var resultGrid = window[grid_id + "Grid"];
        var columns = resultGrid.columns;
        var dateColumns = _.filter(columns, function (column) {
            return column.editType === "date" || column.editType === "datetime";
        });
        var eiblock = new EiBlock(grid_id);
        var rowsDate = resultGrid.getCheckedRows();
        for (var int = 0; int < rowsDate.length; int++) {
            if (int == 0) {
                for (var key in rowsDate[0].toJSON()) {
                    var eColumn = new EiColumn(key);
                    eiblock.getBlockMeta().addMeta(eColumn);
                }
            }

            var model = rowsDate[int];
            $.each(dateColumns, function (index, dateColumn) {
                var field = dateColumn.field,
                    dateFormat = dateColumn.dateFormat;
                model[field] = kendo.toString(model[field], dateFormat); // 日期转String
            });

            eiblock.addRow(eiblock.getMappedArray(model, true));
        }

        var showCount = eiblock.get(EiConstant.SHOW_COUNT) || "true";
        eiblock.set(EiConstant.SHOW_COUNT, showCount);
        eiblock.set(EiConstant.LIMIT, resultGrid.dataSource["_pageSize"]);
        // 默认查询第一页
        eiblock.set(EiConstant.OFFSET, 0);
        return eiblock;
    };

    /**
     * 将数据块中的数据转化为数据源
     * @param eiBlock
     * @param dataSource
     */
    var gridSetData = function (eiBlock, dataSource) {
        var count = 0,
            e = [],
            limit = 0,
            offset = 0;
        if (eiBlock != undefined) {
            count = eiBlock.get(EiConstant.COUNT) || 0;
            limit = eiBlock.get(EiConstant.LIMIT) || 0;
            offset = eiBlock.get(EiConstant.OFFSET) || 0;
            e = eiBlock.getMappedRows();
        }

        var r,
            n = null;
        if (dataSource.constructor == String) {
            var grid = $("#ef_grid_" + dataSource).data("kendoGrid");
            if (grid) {
                n = grid.dataSource;
            } else {
                return;
            }
        } else {
            n = dataSource;
        }

        n._skip = offset;
        n._take = limit;

        n._detachObservableParents();
        n._data = n._observe(e);
        n._pristineData = e.slice(0);
        n._storeData();
        n._ranges = [];
        n._addRange(n._data);
        n._total = count; // n._data.length,
        n._pristineTotal = n._total;
        n._process(n._data);
    };

    /**
     * 判断是否勾选了数据.
     * @param {EFGrid} grid_id （必传）EFGrid,默认resultGrid.
     * @param {String} errMsg （选传）错误提示信息,默认"请勾选相应的数据进行操作!"
     * @returns {Boolean} 是否勾选了数据
     */
    checkSelected = function (grid_id, errMsg = "请勾选相应的数据进行操作!") {
        if (!IPLAT.isAvailable(grid_id)) {
            grid_id = resultGrid;
        }
        var grid = grid_id;
        var checkRows = grid.getCheckedRows();
        if (checkRows.length < 1) {
            NotificationUtil(errMsg, "error");
            return false;
        }
        return true;
    };

    /**
     * 判断是否只勾选了一条数据.
     * @param {EFGrid} grid_id （必传）EFGrid，不传默认resultGrid
     * @param {String} errMsg （选传）错误提示信息,默认"只能选择一条数据进行操作!"
     * @returns {Boolean} 是否勾选了一条数据
     */
    checkOneSelect = function (grid_id, errMsg = "只能选择一条数据进行操作!") {
        if (!IPLAT.isAvailable(grid_id)) {
            grid_id = resultGrid;
        }
        var grid = grid_id;
        var checkRows = grid.getCheckedRows();
        if (checkRows.length !== 1) {
            NotificationUtil(errMsg, "error");
            return false;
        }
        return true;
    };

    /**
     * 将后台数据导出成EXCEL文件.
     * @param grid_id
     * @returns {boolean}
     */
    excelExport = function (eiInfo) {
        if (!IPLAT.isAvailable(eiInfo) || !IPLAT.isAvailable(eiInfo.get("listData"))) {
            NotificationUtil("请将数据放到eiInfo.set('listData', listData)中！", "error");
        }

        var columnWidth = [];
        var listData = eiInfo.get("listData");
        var headerLength = eiInfo.get("listData")[0].cells.length;

        if (IPLAT.isAvailable(listData) && IPLAT.isAvailable(headerLength)) {
            for (var i = 0; i < headerLength; i++) {
                columnWidth.push({ width: 16, autoWidth: true });
            }
        }

        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    columns: columnWidth,
                    rows: listData
                }
            ]
        });

        var fileName = "result_" + kendo.toString(new Date(), IPLAT.FORMAT.DATE_14) + ".xlsx";

        // 导出excel
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: fileName
        });
    };

    /**
     * 所有列信息字符串 "col1,col2,col3"
     * */
    function setExportColumnBlock(eiInfo, gridId) {
        let gridColumns = getGridColumns(gridId);
        let columns = [];
        let idx = 0;
        for (let i = 0; i < gridColumns.gridColumns.length; i++) {
            let colName = gridColumns.gridColumns[i];
            let colInfo = gridColumns.mappedGridColumns[colName];
            let title = colInfo.title;
            // 跳过隐藏字段
            if (colInfo.hidden === true) {
                continue;
            }
            if (colName === null || colName === undefined || colName === "") {
                continue;
            }
            columns.push({
                idx: idx++,
                column: colName,
                title: title
            });
        }
        eiInfo.set("exportColumnBlock", "columns", columns);
    }

    /**
     * 封装多个sheet的grid
     * @param eiInfo
     * @param gridId
     */
    function setExportSheetColumnBlock(eiInfo, gridId) {
        let gridColumns = getGridColumns(gridId);
        let columns = [];
        let idx = 0;
        for (let i = 0; i < gridColumns.gridColumns.length; i++) {
            let colName = gridColumns.gridColumns[i];
            let colInfo = gridColumns.mappedGridColumns[colName];
            let title = colInfo.title;
            // 跳过隐藏字段
            if (colInfo.hidden === true) {
                continue;
            }
            if (colName === null || colName === undefined || colName === "") {
                continue;
            }
            columns.push({
                idx: idx++,
                column: colName,
                title: title
            });
        }
        eiInfo.set("exportColumnBlockSheet"+gridId.options.blockId, "columns", columns);
    }

    /**
     * 根据 EFGrid.blockId 获取 列信息
     *
     * 不要重名, 也不要相似. 例如 result like result1
     * IPLAT.EFGrid 记录的 gridId=blockId-i9if23-fwaf32-f23f23-f32r2
     * 下面以 blockId = substr(${gridId}, 0, blockId.length) 获取 grid column 信息
     * */
    function getGridColumns(gridId) {
        let gridColumns = [];
        let mappedGridColumns = {};

        let grids;
        // 传 grid 字符串
        if (typeof gridId === "string") {
            grids = IMOMUtil.getGrids(gridId);
        }
        // 直接传 resultGrid 对象进来
        else {
            grids = [gridId];
        }

        for (let i = 0; i < grids.length; i++) {
            let efGrid = grids[i];
            if (efGrid.columns) {
                for (let i = 0; i < efGrid.columns.length; i++) {
                    let colI = efGrid.columns[i];
                    if (!mappedGridColumns[colI.field]) {
                        gridColumns.push(colI.field);
                        mappedGridColumns[colI.field] = colI;
                    }
                }
            }
        }

        return {
            gridColumns: gridColumns,
            mappedGridColumns: mappedGridColumns
        };
    }

    /**
     * 获取单个字段
     * */
    function getGridColumn(gridId, column) {
        let columnObj = getGridColumns(gridId);
        if (columnObj) {
            return columnObj.mappedGridColumns[column];
        }
        return null;
    }

    /**
     * hzy 20220813
     * 根据 gridId 查找 grid
     * @param gridId
     *  gridID 命名尽量不要相似
     *
     * 不要重名, 也不要相似. 例如 result like result1
     * IPLAT.EFGrid 记录的 gridId=blockId-i9if23-fwaf32-f23f23-f32r2
     * 下面以 blockId = substr(${gridId}, 0, blockId.length) 获取 grid column 信息
     * */
    function getGrids(gridId) {
        let grids = [];
        for (let key in IPLAT.EFGrid) {
            if (key.length >= gridId.length && gridId === key.substr(0, gridId.length)) {
                let efGrid = IPLAT.EFGrid[key];
                grids.push(efGrid);
            }
        }
        return grids;
    }

    /**
     * 分页查询-前台组合数据生成 excel
     IMOMUtil.excelPageExport({
            sqlId: "VCDP01.queryAggregate",
            fileName: "期间销售毛利边际汇总",
            grid: "result1",
            eiInfo: eiInfo
        });
     * */
    function excelPageExport(optios) {
        let { sqlId, fileName, grid, eiInfo = new EiInfo() } = optios;

        if ($('span[span-for="export-span"]').length === 0) {
            let span = document.createElement("span");
            span.style = "display: none; color: red;";
            span.setAttribute("span-for", "export-span");
            $("body")[0].prepend(span);
        }
        let $export_span = $('span[span-for="export-span"]');
        $export_span.css("display", "block");
        $export_span.text("数据导出开始...");

        function doExport() {
            let offset = 0;
            let limit = 20000;
            let total = 0;

            let fileCounter = 0;
            let splitFileSize = 100000;
            let startTime = new Date().getTime();

            let EXPORT_INFO_BLOCK = "exportInfoBlock";
            let DATA_LIST = "dataList";
            let COUNT = "count";

            try {
                let exportDataList = [];

                do {
                    console.log(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss") + " exp:" + offset + "/" + total + "/" + limit);
                    if (total > 0) {
                        $export_span.text("正在查询数据: " + offset + "/" + total);
                    }
                    eiInfo.set(EXPORT_INFO_BLOCK, "service", sqlId);
                    eiInfo.set(EXPORT_INFO_BLOCK, "offset", offset);
                    eiInfo.set(EXPORT_INFO_BLOCK, "limit", limit);

                    IMOMUtil.callService({
                        service: "FCCC05",
                        method: "excelQuery",
                        eiInfo: eiInfo,
                        showProgress: false,
                        callback: function (respEi) {
                            let exportInfoBlock = respEi.getBlock(EXPORT_INFO_BLOCK);
                            let resultBlock = respEi.getBlock("result");
                            let list = resultBlock.getMappedRows();
                            if (list && list.length > 0) {
                                offset = offset + list.length;
                                for (const item of list) {
                                    exportDataList.push(item);
                                }
                            }
                            if (total == 0) {
                                total = exportInfoBlock.get(COUNT);
                            }
                        },
                        async: false
                    });

                    if (exportDataList.length >= splitFileSize) {
                        IMOMUtil.fmtGridData(window[grid + "Grid"], exportDataList);

                        IMOMUtil.kendoExport({
                            fixFileName: fileName + "-" + ++fileCounter + "-" + startTime,
                            grid: grid,
                            data: exportDataList,
                            showProgress: false
                        });
                        exportDataList = [];
                    }
                } while (offset < total);

                if (exportDataList.length > 0) {
                    IMOMUtil.fmtGridData(window[grid + "Grid"], exportDataList);

                    IMOMUtil.kendoExport({
                        fixFileName: fileName + "-" + ++fileCounter + "-" + startTime,
                        grid: grid,
                        data: exportDataList,
                        showProgress: false
                    });
                    exportDataList = null;
                }

                $export_span.css("display", "none");
                let endTime = new Date().getTime();
                console.log("export cost:" + (endTime - startTime) / 1000 + "s");
            } finally {
                IPLAT.progress($("body"), false);
            }
        }

        IPLAT.progress($("body"), true);
        // progress 延迟打开, 直接走后面逻辑会卡掉， 延迟 2ms
        setTimeout(() => {
            doExport();
        }, 2);
    }

    /**
     * hzy 20220704
     * 使用 kendo 导出数据
     * 需要引入
     * <script src="${ctx}/kendoui/js/kendo.all.min.js"></script>
     * <script src="${ctx}/kendoui/js/kendo.ooxml.min.js"></script>
     * <script src="${ctx}/kendoui/js/kendo.virtuallist.min.js"></script>
     * @param opinion 可以不填, 自动根据 grid 获取
     * {
            fileName: '导出文件名称',
            grid: '导出grid ID, 获取列名使用',
            // 需要导出的数据
            data: ei.getBlock("result").getMappedRows(),
            // 导出的列详情, 字符串 或者对象类型
            // 1. string 根据 grid 获取列头名称
            // 2. object {prop: 属性, desc: 属性名称, width: 宽度, 默认 100}
            columns: [
                'segNo',
                'customerId',
                {prop: 'customerName', desc: '客户名称', width: 160}
            ]
        }
     *
     */
    function kendoExport(opinion) {
        let body = $("body");
        if (opinion.showProgress !== false && body) {
            IPLAT.progress(body, true);
        }

        let exportData = [];
        let mates = __eiInfo.getBlock(opinion.grid).getBlockMeta().getMetas();

        let columnInfo = getGridColumns(opinion.grid);
        let gridColumns = columnInfo.gridColumns;
        let mappedGridColumns = columnInfo.mappedGridColumns;

        if (!opinion.columns) {
            opinion.columns = gridColumns;
        }

        // 要导出的列
        for (let i = 0; i < opinion.columns.length; i++) {
            let colType = typeof opinion.columns[i];

            let col = null;
            if ("string" === colType) {
                col = {
                    prop: opinion.columns[i]
                };
            } else if ("object" === colType) {
                col = opinion.columns[i];
            } else {
                col = {};
            }

            col.width = col.width || 100;

            opinion.columns[i] = col;
        }

        //设置边框样式
        let title_border = {};
        title_border.color = "#000000"; //边框颜色
        title_border.size = 1; //边框长度 可选值 1 2 3
        // 首行-标题
        let title = [];
        for (let i = 0; i < opinion.columns.length; i++) {
            let col = opinion.columns[i];
            let colMeta = mates[col.prop];
            let colDesc = col.desc;

            if (!colDesc && mappedGridColumns[col.prop]) {
                colDesc = mappedGridColumns[col.prop].title;
            }

            if (!colDesc && colMeta) {
                colDesc = colMeta.descName;
            }

            if (!colDesc) {
                colDesc = col.prop;
            }

            title.push({
                value: colDesc,
                bold: true, //粗体
                fontSize: 11, //字体大小
                background: "#C0C0C0", //背景颜色
                color: "#000000", //字体颜色
                textAlign: "center", //水平对齐方式
                verticalAlign: "center", //垂直对齐方式
                borderTop: title_border,
                borderBottom: title_border,
                borderLeft: title_border
            });
        }

        exportData.push({ cells: title });

        // 数据
        let data_border = new Map();
        data_border.color = "#000000"; //边框颜色
        data_border.size = 1; //边框长度 可选值 1 2 3
        for (let i = 0; i < opinion.data.length; i++) {
            let rowData = opinion.data[i];
            let row = [];

            for (let i = 0; i < opinion.columns.length; i++) {
                let col = opinion.columns[i];
                let celVal = rowData[col.prop];
                if (celVal === null || celVal === undefined) {
                    celVal = "";
                }
                row.push({
                    value: celVal
                    /*fontSize : 10, //字体大小
                    verticalAlign : "center", //垂直对齐方式
                    borderTop : data_border,
                    borderBottom : data_border,
                    borderLeft : data_border,
                    borderRight : data_border*/
                });
            }

            exportData.push({ cells: row });
        }

        let workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    columns: opinion.columns,
                    rows: exportData
                }
            ]
        });

        // 批量导出自己生成文件名称
        if (opinion.fixFileName) {
            opinion.fileName = opinion.fixFileName;
        } else {
            opinion.fileName = (opinion.fileName || "export") + kendo.toString(new Date(), IPLAT.FORMAT.DATE_17);
        }

        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: opinion.fileName
        });

        if (opinion.showProgress !== false && body) {
            IPLAT.progress(body, false);
        }
    }

    /**
     * 格式化数字显示方式
     * 用法
     * formatNumber(12345.999,'#,##0.00');
     * formatNumber(12345.999,'#,##0.##');
     * formatNumber(123,'000000');
     * */
    function formatNumber(num, pattern) {
        let fmtarr = pattern ? pattern.split(".") : [""]; // 格式化字串转为数组
        // 四舍五入处理
        let decimals = fmtarr.length > 1 ? fmtarr[1].length : 0; // 小数位
        num = Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);

        let sign = num >= 0 ? "" : "-";
        num = Math.abs(num);

        let strarr = num ? num.toString().split(".") : ["0"]; // 值->字符串->数组
        let retstr = "";

        // 整数部分
        let str = strarr[0];
        let fmt = fmtarr[0];
        let i = str.length - 1;
        let comma = false;
        for (let f = fmt.length - 1; f >= 0; f--) {
            switch (fmt.substr(f, 1)) {
                case "#":
                    if (i >= 0) {
                        retstr = str.substr(i--, 1) + retstr; // 加当前字符
                    }
                    break;
                case "0":
                    if (i >= 0) {
                        retstr = str.substr(i--, 1) + retstr; // 加当前字符
                    } else {
                        retstr = "0" + retstr; // 补0
                    }
                    break;
                case ",":
                    comma = true; // 有千位符
                    retstr = "," + retstr; // 直接加上逗号（，）
                    break;
            }
        }
        // 整数字符串替换格式后剩余字符串处理
        if (i >= 0) {
            if (comma) {
                let l = str.length;
                for (; i >= 0; i--) {
                    retstr = str.substr(i, 1) + retstr;
                    if (i > 0 && (l - i) % 3 == 0 && !(str.charAt(i - 1) === "-")) {
                        // 加上千位符
                        retstr = "," + retstr;
                    }
                }
            } else {
                retstr = str.substr(0, i + 1) + retstr; // 无千位符直接加在前面
            }
        }

        retstr = retstr + ".";
        // 处理小数部分
        str = strarr.length > 1 ? strarr[1] : "";
        fmt = fmtarr.length > 1 ? fmtarr[1] : "";
        i = 0;
        for (let f = 0; f < fmt.length; f++) {
            switch (fmt.substr(f, 1)) {
                case "#":
                    if (i < str.length) {
                        retstr += str.substr(i++, 1); // 加当前字符
                    }
                    break;
                case "0":
                    if (i < str.length) {
                        retstr += str.substr(i++, 1); // 加当前字符
                    } else {
                        retstr += "0"; // 补0
                    }
                    break;
            }
        }
        return sign + retstr.replace(/^,+/, "").replace(/\.$/, "");
    }

    /**
     * 过滤 对象,数组,数字,字符串的 0E-8
     * */
    function filter0E_8(obj) {
        if (typeof obj === "number" || typeof obj === "string") {
            if (obj.toString() === "0E-8") {
                return 0.0;
            } else {
                return obj;
            }
        }
        if (Array.isArray(obj)) {
            for (let i = 0; i < obj.length; i++) {
                obj[i] = filter0E_8(obj[i]);
            }
            return obj;
        }
        if (typeof obj === "object") {
            for (let p in obj) {
                obj[p] = filter0E_8(obj[p]);
            }
            return obj;
        }
        return obj;
    }

    /**
     * 批量转换对象属性为 float, 处理数字 format 失效
     * obj: 对象或者数组 {} | []
     * props: 对象的 float 属性
     * */
    function propToFloat(obj, props) {
        if (!obj) {
            return;
        }
        if (
            Array.isArray(obj) ||
            // kendo.init
            (obj.__proto__?.constructor?.name === "init" && obj.length > 0)
        ) {
            for (let i = 0; i < obj.length; i++) {
                propToFloat(obj[i], props);
            }
        } else {
            for (let i = 0; i < props.length; i++) {
                let p = props[i];
                let val = obj[p] || 0.0;
                obj[p] = parseFloat(val + "");
            }
        }
    }

    /**
     * 格式化 Grid Column 数据格式, 目前都是 string
     * 1. 数字类型 -> float
     * 2. 日期类型 -> 数据库存储时间格式不统一, 导致页面不能按照统一格式解析
     *
     * @param grid efGrid 对象, 或者 gridId
     * @param dataItems 需要格式化的数据集合 []
     *
     * 用途
     * 1. kendo.ui.Grid.dataBinding
     *
     * 示例.
     * dataBinding: function (e) {
            IMOMUtil.fmtGridData(e.sender, e.items);
        },
     * */
    function fmtGridData(grid, dataItems) {
        try {
            if (!grid || !dataItems) {
                return;
            }
            let g = grid;
            if (typeof grid !== "object") {
                let grids = getGrids(grid);
                if (grids && grids.length > 0) {
                    g = grids[0];
                }
            }

            if (!g) {
                return;
            }
            let floatColumns = []; // 需要转 float 类型的字段
            let dateColumns = [];
            let columns = grid.columns;
            for (let i = 0; i < columns.length; i++) {
                let col = columns[i];
                let fmt = col.format;
                if (fmt && fmt.substr(0, 4) === "{0:n") {
                    floatColumns.push(col.field);
                }
                if (col.displayType === "datetime" || col.displayType === "date") {
                    dateColumns.push({
                        field: col.field,
                        parseFormats: col.parseFormats,
                        dateFormat: col.dateFormat
                    });
                }
            }
            // 转 float 类型
            for (let i = 0; i < dataItems.length; i++) {
                propToFloat(dataItems[i], floatColumns);
            }
            // 日期格式, 精度: yyyyMMddHHmmss, kendo 不能解析 SSS
            if (dateColumns.length > 0) {
                for (let i = 0; i < dataItems.length; i++) {
                    let data = dataItems[i];
                    for (let j = 0; j < dateColumns.length; j++) {
                        let colInfo = dateColumns[j];
                        let oldVal = data[colInfo.field];
                        let parseFormat = colInfo.parseFormats[0];
                        let matchFormat = DateUtils.getFormat(oldVal);
                        // 没有数据, 跳过
                        if (!oldVal) {
                            continue;
                        }
                        // 格式正确, 跳过
                        if (matchFormat === parseFormat) {
                            continue;
                        }
                        let dt = DateUtils.parse(oldVal, matchFormat);
                        data[colInfo.field] = DateUtils.format(dt, parseFormat);
                    }
                }
            }
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * 快捷调用服务方法
     * @param {Object} opinions (必传) 参数
     * @param {String} opinions.service (必传) 后端服务名
     * @param {String} opinions.method (必传) 后端方法名
     * @param {EiInfo} opinions.eiInfo (必传) 传入参数
     * @param {Function} opinions.callback (选传) 回调函数（调用成功后onSuccess之前）
     * @param {Function} opinions.onSuccess (选传) 成功后回调函数
     * @param {Function} opinions.onFail (选传) 失败后回调函数
     * @param {Boolean} opinions.quiet (选传) 是否显示提示信息，默认否
     * @param {Boolean} opinions.showProgress (选传) 是否显示加载效果，默认否
     * @param {Boolean} opinions.async (选传) 是否异步，默认是
     */
    function callService(opinions) {
        let { service, method, eiInfo, callback, onSuccess, onFail, quiet, showProgress = false, async = true } = opinions;
        let respEi = null;
        if (showProgress) {
            IPLAT.progress($("body"), true);
        }
        EiCommunicator.send(
            service,
            method,
            eiInfo,
            {
                onSuccess: function (ei) {
                    if (showProgress) {
                        // progress 会卡屏幕, 需要先停掉
                        IPLAT.progress($("body"), false);
                    }
                    respEi = ei;
                    if (!quiet) {
                        if (-1 === ei.status) {
                            NotificationUtil(ei, "error");
                        } else {
                            NotificationUtil(ei, "success");
                        }
                    }
                    if (callback) {
                        callback.call(this, ei);
                    }
                    if (onSuccess) {
                        onSuccess.call(this, ei);
                    }
                },
                onFail: function (errorMsg, status, e) {
                    if (showProgress) {
                        IPLAT.progress($("body"), false);
                    }
                    if (!quiet) {
                        NotificationUtil(e, "error");
                    } else {
                        console.log(errorMsg, status, e);
                    }
                    if (onFail) {
                        onFail.call(this, errorMsg, status, e);
                    }
                    return false;
                }
            },
            { async: async }
        );
        return respEi;
    }

    /**
     * 扩展 EFWindow, 动态配置 EFWindow
     * */
    function extendEFWindow(opinions) {
        if (undefined === IPLATUI.EFWindow || null == IPLATUI.EFWindow) {
            IPLATUI.EFWindow = {};
        }
        $.extend(IPLATUI.EFWindow, opinions);
    }

    /**
     * 弹出框回写数据
     * @param {Object} args (必传) 参数
     * @param {Object} args.assignMap (选传) 用于弹窗反写数据到input,如{
     *                 'inqu_status-0-segNo': 'segNo',
     *             }
     * @param {Function} args.callback (选传) 回调函数,参数rows
     * @param {Function} args.afterSelect (选传) 选择后回调函数,参数rows
     * @param {rows} rows (必传) grid行数据
     * @param {jQuery} iframejQuery (选传) 弹窗iframe jQuery对象
     * */
    function assignData(args, rows, iframejQuery) {
        let { assignMap, callback, afterSelect } = args;
        if (callback) {
            callback.call(this, rows);
        }
        if (rows.length > 0 && assignMap) {
            let row = rows[0];
            for (let keyVal of Object.entries(assignMap)) {
                let val = "";
                let props = keyVal[1].split(",");
                if (props.length > 1) {
                    for (let i = 0; i < props.length; i++) {
                        let pi = props[i];
                        let piv = row[pi];
                        if (piv) {
                            val += piv;
                        } else {
                            val += pi;
                        }
                    }
                } else if (props.length === 1) {
                    val = row[props[0]];
                    if (IPLAT.isUndefined(val)) {
                        val = "";
                    }
                }
                value($("#" + keyVal[0]), val);
                // $("#" + keyVal[0]).val(val);
            }
        }
        if (afterSelect) {
            afterSelect.call(this, rows, iframejQuery);
        }
    }

    /**
     * EFWindow模板
     * @param {Object} args 参数
     * @param {String} args.windowId (必传) 弹窗ID
     * @param {Object} args.assignMap (选传) 用于弹窗反写数据到input,如{
     *                 'inqu_status-0-segNo': 'segNo',
     *             }
     * @param {String} args.gridId (选传) 弹窗页面的gridId, 默认为 ef_grid_result
     * @param {String} args.windowField (选传) 弹窗页面的页面标识字段, 默认为 inqu_status-0-windowId
     * @param {Function} args._open (选传) 打开事件,不传时触发grid查询, 参数:e,iframejQuery
     * @param {Boolean} args.notQuery (选传) 打开时不触发grid查询，默认查询(在_open事件后)
     * @param {Function} args.callback (选传) assignMap赋值前回调函数,参数rows
     * @param {Function} args.afterSelect (选传) assignMap赋值后回调函数,参数rows
     */
    function windowTemplate(args) {
        function efWindow(windowId) {
            return window[windowId + "Window"];
        }

        function contentWindow(windowId) {
            return window[windowId + "Window"].element.find("iframe")[0].contentWindow;
        }

        let options = {};
        let windowId = args.windowId;
        let gridId = args.gridId || "ef_grid_result";
        let windowField = args.windowField || "inqu_status-0-windowId";
        // 打开关闭事件
        options[windowId] = {
            open: function (e) {
                var $subJQ = contentWindow(windowId).$;
                if ($subJQ) {
                    $subJQ("#" + windowField)?.val(windowId);
                }
                if (args._open) {
                    args._open.call(this, e, contentWindow(windowId).$);
                }
                // _open未阻止弹窗且为阻止默认查询时自动查询
                if (!e.isDefaultPrevented() && !args.notQuery) {
                    let grid = contentWindow(windowId)
                        .$("#" + gridId)
                        .data("kendoGrid");
                    grid.dataSource.page(1);
                }
            },
            close: function (e) {
                let grid = contentWindow(windowId)
                    .$("#" + gridId)
                    .data("kendoGrid");
                let row = grid.getCheckedRows();
                try {
                    grid.unCheckAllRows();
                } catch (e) {}
                assignData(args, row, contentWindow(windowId).$);
            }
        };
        extendEFWindow(options);
        return options;
    }

    /**
     * 引入业务单元弹出页面
     * @param {{}} args 参数 (选传) 默认查询条件区域
     * @param {String} args.windowId (选传) 弹窗ID, 默认为unitInfo
     * @param {Boolean} args.notInqu (选传) 不传或false时为默认查询条件区域，使用默认的assignMap参数，
     * @param {Object} args.assignMap (选传) 用于弹窗反写数据到input,如{
     *                 'inqu_status-0-segNo': 'segNo',
     *             }
     * @param {Function} args.callback (选传) 回写数据之前调用的方法, 参数: rows 勾选的数据
     * @param {Function} args.afterSelect (选传) 回写数据之后调用的方法, 参数: rows 勾选的数据
     * @param {Function} args._open (选传) 打开事件, 参数:e原事件参数,iframejQuery 弹出页面的jquery对象
     * @param {Object} unitData (选传) 业务单元数据, 用于回写数据
     *
     */
    function importUnitInfo(args = {}, unitData) {
        // 默认配置
        const defaultConfig = {
            windowId: "unitInfo",
            gridId: "ef_grid_result2",
            windowField: "inqu2_status-0-windowId",
            assignMap: !args.notInqu
                ? {
                      "inqu_status-0-segNo": "segNo",
                      "inqu_status-0-unitCode": "unitCode",
                      "inqu_status-0-segName": "segName"
                  }
                : undefined
        };
        // 合并配置
        const config = {
            ...defaultConfig,
            ...args,
            afterSelect: (rows) => {
                // 只在有选中行且unitData存在时更新数据
                if (rows?.[0] && unitData) {
                    const { unitCode, segNo, segName, segFullName } = rows[0];
                    Object.assign(unitData, { unitCode, segNo, segName, segFullName });
                }
                // 调用原有的afterSelect回调
                args.afterSelect?.call(this, rows);
            }
        };
        windowTemplate(config);
    }


    /**
     * 引入详情区域业务单元弹窗
     */
    function importDetailUnitInfo(args = {}) {
        importUnitInfo({
            windowId: "unitInfo01",
            notInqu: true,
            assignMap: args.assignMap || {
                "detail_status-0-unitCode": "unitCode",
                "detail_status-0-segNo": "segNo",
                "detail_status-0-eArchivesNo": "",
                "detail_status-0-equipmentName": "",
                "detail_status-0-deviceCode": "",
                "detail_status-0-deviceName": ""
            }
        });
    }

    /**
     * 引入详情区域设备弹窗
     */
    function importDetailEquipmentInfo(args = {}) {
        windowTemplate({
            windowId: "equipmentInfo",
            _open: function (e, iframejQuery) {
                let a = $("#detail_status-0-unitCode").val();
                if (a == null || IPLAT.isBlankString(a)) {
                    NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                    e.preventDefault();
                    return;
                }
                iframejQuery("#inqu_status-0-unitCode").val(a);
                iframejQuery("#inqu_status-0-segNo").val(IPLAT.EFSelect.value($("#detail_status-0-segNo")));
            },
            assignMap: args.assignMap || {
                "detail_status-0-eArchivesNo": "eArchivesNo",
                "detail_status-0-equipmentName": "equipmentName",
                "detail_status-0-deviceCode": "",
                "detail_status-0-deviceName": ""
            }
        });
    }

    /**
     * 引入详情区域分部设备弹窗
     */
    function importDetailDeviceInfo(args = {}) {
        windowTemplate({
            windowId: "deviceInfo",
            _open: function (e, iframejQuery) {
                let a = $("#detail_status-0-eArchivesNo").val();
                if (a == null || IPLAT.isBlankString(a)) {
                    NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                    e.preventDefault();
                    return;
                }
                iframejQuery("#inqu_status-0-eArchivesNo").val(a);
            },
            assignMap: args.assignMap || {
                "detail_status-0-deviceCode": "deviceCode",
                "detail_status-0-deviceName": "deviceName"
            }
        });
    }

    /**
     * 对象转 EiInfo
     * {segNo: "", unitCode: ""}
     *
     * EiInfo{
     *     segNo: "",
     *     unitCode: ""
     * }
     * */
    function objToEiInfo(obj, paramBlock) {
        let eiInfo = new EiInfo();
        if (obj) {
            for (const key of Object.keys(obj)) {
                if (paramBlock) {
                    eiInfo.set(paramBlock, 0, key, obj[key]);
                } else {
                    eiInfo.set(key, obj[key]);
                }
            }
        }
        return eiInfo;
    }

    /**
     * EiInfo block 转换为下拉格式
     * [ {textField: "", valueField: ""} ]
     *
     * 参数: {
            eiInfo: EiInfo 对象,
            blockId: 返回数据集合block,
            allOpinion: true | false 是否添加 "全部" 选项,
            removeEmpty: '排除空数据 true | false'
            textField: 文本字段,
            valueField: value字段
        }
     * */
    function mapTextValueField(args) {
        let { eiInfo, blockId, allOpinion, removeEmpty, textField, valueField } = args;
        let values = [];

        let block = eiInfo.getBlock(blockId);
        if (!block) {
            return values;
        }

        let rows = block.getMappedRows();

        if (allOpinion) {
            values.push({
                textField: "全部",
                valueField: ""
            });
        }

        if (rows) {
            for (let i = 0; i < rows.length; i++) {
                let row = rows[i];
                let textFieldVal = row[textField] || "";
                let valueFieldVal = row[valueField] || "";
                if (removeEmpty && textFieldVal.trim() === "" && valueFieldVal.trim() === "") {
                    continue;
                }
                row["textField"] = textFieldVal;
                row["valueField"] = valueFieldVal;
                values.push(row);
            }
        }
        return values;
    }

    function initEFSelect(args) {
        let {
            eiInfo = new EiInfo(),
            service = "FCCC99",
            method,
            textField,
            valueField,
            selectId,
            defaultValue = "",
            allOpinion = true,
            template = "#=valueField#-#=textField#",
            valueTemplate = "#=valueField#-#=textField#",
            filter = "contains",
            readnoly = true,
            autoWidth = true
        } = args;

        if (!selectId || !textField || !valueField) {
            return;
        }

        IMOMUtil.callService({
            service,
            method,
            eiInfo,
            callback: function (ei) {
                let rows = ei?.getBlock("result")?.getMappedRows();
                if (rows) {
                    IPLAT.EFOptions = [];
                    if (allOpinion) {
                        IPLAT.EFOptions.push({
                            textField: "全部",
                            valueField: ""
                        });
                    }

                    for (let i = 0; i < rows.length; i++) {
                        IPLAT.EFOptions.push({
                            textField: rows[i][textField],
                            valueField: rows[i][valueField]
                        });
                    }

                    IPLAT.Select({
                        selectId,
                        template,
                        valueTemplate,
                        filter,
                        readnoly,
                        autoWidth,
                        defaultValue
                    });
                }
            }
        });
    }

    /**
     * 调用服务刷新下拉框(EFSelect)数据
     *
     * 示例.
     *
     * 刷新下拉框
     * refreshEFSelect({
            param: {
                serviceId: "S_UM_CC_9901",
                segNo: segNo,
            },
            paramBlock: '参数默认放到 EiInfo.attr 里面, 可以指定 block'
            resultBlock: "costCenterBlock",
            textField: "subReceiptCode",
            valueField: "subReceiptCode",
            element: "#add_status-0-subReceiptCode"
        });

        清空下拉框
        refreshEFSelect({
            clear: true,
            element: "#add_status-0-subReceiptCode"
        });

     * */
    function refreshEFSelect(args) {
        let { serviceName, methodName, param, paramBlock, resultBlock, textField, valueField, element, callback, clear } = args;

        // 清空下拉框
        if (element && clear) {
            let dataSource = new kendo.data.DataSource({ data: [] });
            let e = $(element);
            IPLAT.EFSelect.setDataSource(e, dataSource);
            IPLAT.EFSelect.value(e, "");
            return;
        }

        serviceName = serviceName || "FCCC99";
        methodName = methodName || "queryService";
        resultBlock = resultBlock || "result";
        textField = textField || valueField;

        // 公共查询方法, 指定服务返回的 blockID resultBlock
        if (args.resultBlock && !param.resultBlock && param.serviceId === "S_UM_CC_9999") {
            param.resultBlock = args.resultBlock;
        }
        let serviceId = param.serviceId;
        param.serviceId = null;
        let eiInfo = objToEiInfo(param, paramBlock);
        eiInfo.set("serviceId", serviceId);

        EiCommunicator.send(
            serviceName,
            methodName,
            eiInfo,
            {
                onSuccess: function (resp) {
                    if (resp.status === -1) {
                        let values = [];
                        values.push({
                            textField: "全部",
                            valueField: ""
                        });
                        let dataSource = new kendo.data.DataSource({ data: values });
                        let e = $(element);
                        IPLAT.EFSelect.setDataSource(e, dataSource);
                        IPLAT.EFSelect.value(e, "");
                        NotificationUtil("操作失败，原因【" + resp.msg + "】", "error");
                        return;
                    }

                    let options = mapTextValueField({
                        eiInfo: resp,
                        blockId: resultBlock,
                        allOpinion: true,
                        removeEmpty: true,
                        textField: textField,
                        valueField: valueField
                    });

                    let dataSource = new kendo.data.DataSource({ data: options });

                    let elements = [];
                    if (typeof element === "string") {
                        elements.push($(element));
                    } else if (Array.isArray(element)) {
                        for (let i = 0; i < element.length; i++) {
                            elements.push($(element[i]));
                        }
                    }

                    for (let i = 0; i < elements.length; i++) {
                        let e = elements[i];
                        IPLAT.EFSelect.setDataSource(e, dataSource);
                        // 重置值
                        IPLAT.EFSelect.value(e, "");
                    }
                    if (callback) {
                        callback.apply(this);
                    }
                },
                onFail: function (errorMsg, status, e) {
                    console.log(errorMsg, status, e);
                }
            },
            { async: true }
        );
    }

    /**
     * 调用服务刷新输入框(EFInput)数据
     *
     * 示例.
     * refreshDropDown({
            param: {
                serviceId: "S_UM_CC_9901",
                segNo: segNo,
            },
            resultBlock: "costCenterBlock",
            element: "#add_status-0-subReceiptCode"
        });
     * */
    function refreshEFInput(args) {
        let { serviceName, methodName, param, resultBlock, element, callback } = args;

        serviceName = serviceName || "FCCC99";
        methodName = methodName || "queryService";
        resultBlock = resultBlock || "result";

        let eiInfo = objToEiInfo(param);

        EiCommunicator.send(
            serviceName,
            methodName,
            eiInfo,
            {
                onSuccess: function (resp) {
                    if (resp.status === -1) {
                        return;
                    }

                    let options = mapTextValueField({
                        eiInfo: resp,
                        blockId: resultBlock,
                        allOpinion: true
                    });

                    let dataSource = new kendo.data.DataSource({ data: options });
                    let e = $(element);
                    IPLAT.EFInput.value(e, dataSource);
                    // 重置值
                    IPLAT.EFInput.value(e, "");
                    if (callback) {
                        callback.apply(this);
                    }
                },
                onFail: function (errorMsg, status, e) {
                    console.log(errorMsg, status, e);
                }
            },
            { async: true }
        );
    }

    /**
     * hzy 20220813
     * 刷新 EFComboColumn 下拉框的数据
     * */
    function refreshEFComboColumn(args) {
        let { serviceName, methodName, param, resultBlock, textField, valueField, gridId, column, allOpinion, event, callback } = args,
            grid;

        serviceName = serviceName || "FCCC99";
        methodName = methodName || "queryService";
        resultBlock = resultBlock || "result";
        textField = textField || valueField;
        allOpinion = allOpinion || false;

        let eiInfo = objToEiInfo(param);

        let grids = IMOMUtil.getGrids(gridId);
        if (!grids || grids.length === 0) {
            return;
        }
        grid = grids[0];
        let gridColumns = grid.columns;
        for (let i = 0; i < gridColumns.length; i++) {
            let _col = gridColumns[i];
            // 匹配名称, 刷新数据
            if (_col.field === column) {
                IMOMUtil.callService({
                    service: serviceName,
                    method: methodName,
                    eiInfo: eiInfo,
                    quiet: true,
                    callback: function (ei) {
                        let values = mapTextValueField({
                            eiInfo: ei,
                            blockId: resultBlock,
                            allOpinion: allOpinion,
                            textField: textField,
                            valueField: valueField
                        });
                        // 更新 IPLAT.EFGrid.columns[?].values
                        _col.values = values;

                        // 判断字段当前值是否存在于 values 中, 不存在清空
                        try {
                            // TODO 下拉框回填还要用, 修改此数据需要后置
                            /*setTimeout(function(){
                                if (event && event.model){
                                    let oldVal = event.model.get(column);
                                    if (values.indexOf(oldVal) < 0){
                                        event.model.set(column, "")
                                    }
                                }
                            }, 100);*/
                            if (event && event.model) {
                                let oldVal = event.model.get(column);
                                if (values.indexOf(oldVal) < 0) {
                                    event.model.set(column, "");
                                }
                            }
                        } catch (e) {
                            console.error(e);
                        }

                        // 更新 kendoDropDownList.datasource
                        // @see iplat.ui.min.js function _buildColumnEditor() { case 'dropdown' }
                        try {
                            let _ele = $("input[name='" + column + "']");
                            let kendoDropDownList = _ele.data("kendoDropDownList");
                            if (kendoDropDownList) {
                                let template = _col.itemTemplate || "#=" + _col.textField + "#";
                                let dropDownOption = $.extend(
                                    {
                                        valuePrimitive: true,
                                        value: _col.defaultValue,
                                        template: template,
                                        valueTemplate: template,
                                        dataTextField: _col.textField,
                                        dataValueField: _col.valueField,
                                        dataSource: values
                                    },
                                    _col.attributes
                                );

                                _ele.kendoDropDownList(dropDownOption);
                            }
                        } catch (e) {
                            console.error(e);
                        }

                        if (callback && callback instanceof Function) {
                            callback.call(this, event, values);
                        }
                    }
                });
                break;
            }
        }
    }

    /**
     * 点击查询按钮之后同步不同 Tab 的 grid 查询结果
     *
     * 根据最近一次点击查询按钮的时间,
     * 比较不同 tab 最近一次查询数据时间, 进行自动刷新
     *
     * eg.
     * const syncOpinion = {
            // 当前页面的 Grid 信息记录
            gridMap: {
                // summary: grid 的 ID
                summary: {
                    // 对应 tab 的 ID, IPLAT 编号 info-1, info-2
                    tabId: 'info-1',
                    // 是否默认 tab, 一般第一个 tab 为默认
                    default: true,
                    // 查询前校验函数, 返回 false, 不执行查询
                    beforeQuery,
                },
                detail: {
                    tabId: 'info-2',
                    beforeQuery,
                }
            }
        };
     IMOMUtil.syncTabGridQuery(syncOpinion);
     */
    function syncTabGridQuery(args) {
        let { lastLoad = -1, tabId = "info", queryBtn, tab, gridMap, tabSelect } = args;

        if (!gridMap) {
            console.error("gridMap not exists.");
            return;
        }

        queryBtn = queryBtn || "QUERY";
        if (!tab) {
            tab = {};
            tab[tabId] = {
                activateTab: null
            };
        }

        IMOMUtil.context("tab", tab);

        let EfTab = IPLATUI.EFTab;
        if (!EfTab) {
            EfTab = IPLATUI.EFTab = {};
        }
        EfTab[tabId] = {
            select: function (e) {
                if (tabSelect && typeof tabSelect === "function") {
                    tabSelect.call(this, e);
                }
                let currTabId = e.contentElement.id;
                for (const grid of Object.keys(gridMap)) {
                    let gridInfo = gridMap[grid];
                    gridInfo.lastLoad = gridInfo.lastLoad || -1;
                    if (gridInfo.tabId === currTabId && gridInfo.lastLoad < lastLoad) {
                        window[grid + "Grid"].dataSource.page(1);
                        gridInfo.lastLoad = lastLoad;
                    }
                }
                tab[tabId].activateTab = currTabId;
            }
        };

        $("#" + queryBtn).on("click", function (e) {
            let currentGrid = null,
                defaultTab = null;
            let activateTab = tab[tabId].activateTab;

            for (const grid of Object.keys(gridMap)) {
                let gridInfo = gridMap[grid];
                if (gridInfo.tabId === activateTab) {
                    currentGrid = grid;
                }
                if (gridInfo.default) {
                    defaultTab = grid;
                }
            }

            let opGrid = currentGrid || defaultTab;
            let gridInfo = gridMap[opGrid];
            if (typeof gridInfo.beforeQuery === "function" && gridInfo.beforeQuery() === false) {
                return;
            }

            gridInfo.lastLoad = lastLoad = new Date().getTime();
            window[opGrid + "Grid"].dataSource.page(1);
        });
    }

    /**
     * 页面上下文对象, 可以用于存放常量
     * */
    function context(key, val) {
        if (!window.IPLAT_PAGE_CONTEXT) {
            window.IPLAT_PAGE_CONTEXT = {};
        }
        let context = IPLAT_PAGE_CONTEXT[window.location];
        if (!context) {
            context = IPLAT_PAGE_CONTEXT[window.location] = {};
        }
        if (key && val) {
            context[key] = val;
            return context;
        } else if (key) {
            return context[key];
        }
        return context;
    }

    /**
     绑定 EFGrid EFComboColumn, kendoDropDownList 中下拉框的事件
     IMOMUtil.bindEFGridDropDownList({
        // EFGrid Id
        gridId: 'result',
        // 要绑定的属性
        field: 'apportionId',
        // 要绑定的事件
        event: {
            select: function(e) {
                console.log("testSelect", e);
            }
        },
        // 页面初始化的时候, 需要延迟等待 IPLAT 初始化完成
        delay: 500
     });
     * */
    function bindEFGridDropDownList(opinions) {
        let { gridId, field, event = {}, delay = -1 } = opinions;
        if (!gridId) {
            return;
        }
        function bind() {
            let column = getGridColumn(gridId, field);
            for (let evt of Object.entries(event)) {
                column.attributes[evt[0]] = evt[1];
            }
        }
        if (delay > 0) {
            setTimeout(() => {
                bind();
            }, delay);
        } else {
            bind();
        }
    }

    /**
     * 监控 DataSource 改变事件
     * 提供 旧值 (oldValue) 新值 (newValue) 做对比
     *
     * @param e dataSource.change 事件对象
     * @param watcher 自定已监控事件
     * watcher({
            // 当前实体对象
            entity: obj,
            // 改变的属性
            prop: prop,
            // 改变后的值
            newValue: value,
            // 改变前值
            oldValue: observer ? observer[prop] : null,
            // 改变前对象
            oldEntity: observer
         }){

         }
     * eg.
     * IPLATUI.EFGrid = {
                dataSource: {
                    change: function(e){
                        IMOMUtil.watchDataSourceItems(e, function(args){
                            console.log("prop: " + args.prop + " newValue: " + args.newValue + " oldValue:  " + args.oldValue);
                        });
                    }
                }
            }
     * */
    function watchDataSourceItems(e, watcher) {
        let OBSERVER_MAP = IMOMUtil.context("dataSource.observerMap");
        if (!OBSERVER_MAP) {
            OBSERVER_MAP = {};
            IMOMUtil.context("dataSource.observerMap", OBSERVER_MAP);
        }
        function watch(obj, watcher) {
            let field_uid = "uid";
            let uid = obj[field_uid];
            let observer = OBSERVER_MAP[uid];
            if (observer) {
                return;
            } else {
                observer = ObjectUtils.clone(obj);
                OBSERVER_MAP[uid] = observer;
            }

            for (let prop of Object.keys(obj)) {
                Object.defineProperty(obj, prop, {
                    configurable: true,
                    enumerable: true,
                    get() {
                        return observer[prop];
                    },
                    set(value) {
                        if (watcher) {
                            let ret = watcher.call(this, {
                                entity: obj,
                                prop: prop,
                                newValue: value,
                                oldValue: observer ? observer[prop] : null,
                                oldEntity: observer
                            });
                            if (ret === false) {
                                return;
                            }
                        }
                        observer[prop] = value;
                    }
                });
            }
        }

        let items = e.items;
        if (!items) {
            return;
        }
        for (let i = 0; i < items.length; i++) {
            let item = items[i];
            watch(item, watcher);
        }
    }

    /**
     * 防止连续重复点击
     * IMOMUtil.onceClick("#BUTTON_ID", function (e) {});
     * */
    function onceClick(button, func) {
        // 防止重复点击
        let onceClick = function (e) {
            $(button).attr("disabled", true);
            setTimeout(() => {
                $(button).attr("disabled", false);
            }, 500);
            func.apply(this);
        };
        $(button).on("click", onceClick);
    }

    /**
     * 集成 EFInput, EFSelect, EFPopupInput 提供统一的赋值取值逻辑
     * eg.
     *   取值: let testNum = IMOMUtil.value($("#inqu_status-0-userNum"));
     *   赋值: IMOMUtil.value($("#inqu_status-0-userNum"), 'test');
     * @param element jq 元素选择器, 或者 jquery 对象
     * @param value 赋值时传, 取值不用传
     */
    function value(element, value) {
        let ele = $(element);
        // DropDownList
        let dropDown = ele.data("kendoDropDownList");
        if (dropDown) {
            if (value !== undefined) {
                dropDown.value(value);
            } else {
                return dropDown.value();
            }
        }
        // other
        else {
            // textField
            let textFieldId = ele.attr("id") + "_textField";
            let textField = $("#" + textFieldId);

            if (value !== undefined) {
                textField.val(value);
                ele.val(value);
            } else {
                return ele.val();
            }
        }
    }

    /** hzy 20220822 */
    const DateUtils = {
        /**
         * 解析日期字符串
         * 解析不出来, 自动匹配格式
         *
         * @param dateStr 日期字符串 2022-08-22
         * @param fmt 格式 yyyyMMdd HHmmssSSS
         *
         * Date 类型可解析格式
         * new Date('2022/08/22 13:25:33.123456')
         * */
        parse: function (dateStr, fmt) {
            // 通过部分格式获取日期
            if (!dateStr || dateStr instanceof Date) {
                return dateStr;
            }
            let date = this.parseDate(dateStr, fmt);
            if (date) {
                return date;
            }

            let matchFmt = this.getFormat(dateStr);
            if (matchFmt) {
                return this.parseDate(dateStr, matchFmt);
            }
            return dateStr;
        },
        /**
         * 通过日期字符串 和 格式 解析日期
         * @param dateStr 日期字符串 2022-08-22
         * @param fmt 格式 yyyyMMdd HHmmssSSS
         * */
        parseDate: function (dateStr, fmt) {
            function getByFmt(subFmt) {
                if (!subFmt) {
                    return null;
                }
                let idx = fmt.indexOf(subFmt);
                if (idx > -1) {
                    return dateStr.substr(idx, subFmt.length);
                }
                return null;
            }
            try {
                if (!dateStr || !fmt) {
                    return null;
                }
                let now = new Date();
                let yyyy = getByFmt("yyyy");
                if (!yyyy) {
                    let yy = getByFmt("yy");
                    if (yy) {
                        let year = now.getFullYear();
                        yyyy = year.toString().substr(0, 2) + yy;
                    } else {
                        yyyy = now.getFullYear();
                    }
                }
                let MM = getByFmt("MM") || now.getMonth() + 1;
                let dd = getByFmt("dd") || now.getDate();
                let HH = getByFmt("HH") || "00";
                let mm = getByFmt("mm") || "00";
                let ss = getByFmt("ss") || "00";
                let SSS = getByFmt("SSS") || "000";

                let fullDateStr = yyyy + "/" + MM + "/" + dd + " " + HH + ":" + mm + ":" + ss + "." + SSS;
                return new Date(fullDateStr);
            } catch (e) {
                console.error(e);
                return null;
            }
        },
        /**
         * 获取字符串的日期格式
         * */
        getFormat: function (dateStr) {
            // 匹配日期格式
            const formats = [
                [/^[0-9]{8}$/g, "yyyyMMdd"],
                [/^[0-9]{10}$/g, "yyyyMMddHH"],
                [/^[0-9]{12}$/g, "yyyyMMddHHmm"],
                [/^[0-9]{14}$/g, "yyyyMMddHHmmss"],
                [/^[0-9]{17}$/g, "yyyyMMddHHmmssSSS"],
                // 2022/08/22
                [/^[0-9]{1,4}\D[0-9]{1,2}\D[0-9]{1,2}$/g, "yyyy/MM/dd"],
                // 2022/08/22 14:27
                [/^[0-9]{1,4}\D[0-9]{1,2}\D[0-9]{1,2} [0-9]{1,2}\D[0-9]{1,2}$/g, "yyyy/MM/dd HH:mm"],
                // 2022/08/22 14:27:00
                [/^[0-9]{1,4}\D[0-9]{1,2}\D[0-9]{1,2} [0-9]{1,2}\D[0-9]{1,2}\D[0-9]{1,2}$/g, "yyyy/MM/dd HH:mm:ss"],
                // 2022/08/22 14:27:00.000
                [/^[0-9]{1,4}\D[0-9]{1,2}\D[0-9]{1,2} [0-9]{1,2}\D[0-9]{1,2}\D[0-9]{1,2}\D[0-9]{1,3}$/g, "yyyy/MM/dd HH:mm:ss.SSS"]
            ];

            let matchFmt;
            for (let i = 0; i < formats.length; i++) {
                let ptn = formats[i];
                if (ptn[0].test(dateStr)) {
                    matchFmt = ptn[1];
                    break;
                }
            }
            return matchFmt;
        },
        /**
         * 格式化日期
         * @param dateObj date obj
         * @param fmt y, M, q, d, H, m, s, S
         * @returns {*}
         */
        format: function (dateObj, fmt) {
            let date = new Date(dateObj);
            let obj = {
                "y+": date.getFullYear(),
                "M+": this.appendZero(date.getMonth() + 1, 2),
                "q+": Math.floor((date.getMonth() + 3) / 3), //季度
                "d+": this.appendZero(date.getDate(), 2),
                "H+": this.appendZero(date.getHours(), 2),
                "m+": this.appendZero(date.getMinutes(), 2),
                "s+": this.appendZero(date.getSeconds(), 2),
                "S+": this.appendZero(date.getMilliseconds(), 3) //毫秒
            };
            for (let i in obj) if (new RegExp("(" + i + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, obj[i]);
            return fmt;
        },
        /**
         * 不足len补0
         * */
        appendZero: function (str, len) {
            try {
                let str_len = str.toString().length;
                if (!len) len = str_len;
                while (str_len < len) {
                    str = "0" + str;
                    str_len++;
                }
            } catch (e) {}
            return str;
        },
        /**
         * 字符串日期格式
         * 转换成字符串日期格式
         * */
        strToStr: function (dateStr, fmt, toFmt) {
            let date = this.parse(dateStr, fmt);
            if (!date) {
                return dateStr;
            }
            return this.format(date, toFmt);
        }
    };

    const URLUtils = {
        /**
         * 页面携带的参数 window.location.search 转对象
         * @param search 默认当前页面
         * */
        locationSearch2Obj: function (search) {
            search = search || window.location.search;
            if (search.startsWith("?")) {
                search = search.substring(1);
            }
            let data = {};
            let keyValArr = search.split("&");
            for (let i = 0; i < keyValArr.length; i++) {
                let value = keyValArr[i].split("=");
                for (let j = 0; j < keyValArr.length; j++) {
                    data[value[0]] = value[1];
                }
            }
            return data;
        },
        obj2locationSearch: function (obj) {
            if (!obj) {
                return "";
            }
            let search = "";
            let keys = Object.keys(obj);
            for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                let val = obj[key];
                search += search === "" ? "" : "&";
                search += key + "=" + val;
            }
            return search;
        }
    };

    const ObjectUtils = {
        clone: function (obj) {
            let source = obj;
            if (typeof source === "string") {
                return source;
            }
            if (typeof source === "number") {
                return source;
            }
            if (Array.isArray(source)) {
                let target = [];
                for (let i = 0; i < source.length; i++) {
                    target.push(ObjectUtils.clone(source[i]));
                }
                return target;
            }
            if (typeof source === "object") {
                let target = {};
                let keys = Object.keys(source);
                for (let i = 0; i < keys.length; i++) {
                    let key = keys[i];
                    target[key] = ObjectUtils.clone(source[key]);
                }
                return target;
            }
            if (typeof source === "function") {
                return source;
            }
        }
    };

    /**
     * 通过当前登录人回填业务单元代码
     * 并返回业务单元信息对象
     * @returns {{segNo: string, segName: string, unitCode: string, segFullName: string}}
     */
    function fillUnitInfo() {
        var eiInfo = new EiInfo();
        let obj = {
            unitCode: "",
            segNo: "",
            segName: "",
            segFullName: ""
        };
        EiCommunicator.send(
            "XTSS01",
            "queryForInqu",
            eiInfo,
            {
                onSuccess: function (ei) {
                    if (ei.status === 0) {
                        let block = ei.getBlock("inqu_status");
                        let rows = block.getMappedRows();
                        if (rows.length === 1) {
                            //回填数据
                            var inqu = document.getElementById("inqu");
                            if (inqu) {
                                IPLAT.fillNode(inqu, ei);
                            }
                            const row = rows[0];
                            // 赋值
                            Object.assign(obj, {
                                unitCode: row.unitCode,
                                segNo: row.segNo,
                                segName: row.segName,
                                segFullName: row.segFullName
                            });
                        }
                    }
                }
            },
            { async: false }
        );
        return obj;
    }


    /**
     * 通过当前登录人回填业务单元代码
     * 并返回业务单元信息对象
     * @returns {{segNo: string, segName: string, unitCode: string, segFullName: string}}
     */
    function fillUnitInfo2() {
        var eiInfo = new EiInfo();
        let obj = {
            unitCode: "",
            segNo: "",
            segName: "",
            segFullName: ""
        };
        EiCommunicator.send(
            "XTSS01",
            "queryForInqu",
            eiInfo,
            {
                onSuccess: function (ei) {
                    if (ei.status === 0) {
                        let block = ei.getBlock("inqu_status");
                        let rows = block.getMappedRows();
                        if (rows.length === 1) {
                            //回填数据
                            var inqu = document.getElementById("inqu");
                            var inqu2 = document.getElementById("inqu2");
                            if (inqu) {
                                IPLAT.fillNode(inqu, ei);
                            }
                            if (inqu2) {
                                IPLAT.fillNode(inqu2, ei);
                            }
                            const row = rows[0];
                            // 赋值
                            Object.assign(obj, {
                                unitCode: row.unitCode,
                                segNo: row.segNo,
                                segName: row.segName,
                                segFullName: row.segFullName
                            });
                        }
                    }
                }
            },
            { async: false }
        );
        return obj;
    }




    /**
     * 文件上传组件
     * @param {Object} options - 上传配置选项
     * @param {string} options.id - 上传组件的DOM元素id
     * @param {string} options.ename - 上传组件的名称
     * @param {string} options.serviceName - 处理上传的服务名
     * @param {string} options.methodName - 处理上传的方法名
     * @param {Function} [options.callback] - 上传成功后的回调函数
     * @returns {Object} Kendo UI Upload组件实例
     */
    function FileUploader(options) {
        var element = $("#" + options.id);
        //文件导入后会跳转到这里指定到jsp，可增加自定义参数
        var saveUrl =
            IPLATUI.CONTEXT_PATH +
            "/XS/FA/XSFA4000.jsp?ename=" +
            options.ename +
            "&serviceName=" +
            options.serviceName +
            "&methodName=" +
            options.methodName;
        $.extend(options, {
            async: {
                saveUrl: saveUrl
            },
            success: function (data) {
                if (typeof options.callback === "function") {
                    options.callback(data);
                }
                NotificationUtil("附件上传成功！", "success");
                saveUrl = "";
            },
            error: function (data) {
                var response = data.XMLHttpRequest.response;
                var text = response.substring(response.lastIndexOf("<pre>") + 5);
                var msg = text.substring(0, text.indexOf("org"));
                var msg2 = msg.substring(51) + "<br>";
                IPLAT.NotificationUtil(msg2, "error");
                saveUrl = "";
            },
            complete: function (data) {
                IPLAT.progress($("body"), false);
            }
        });
        return element.kendoUpload(options).data("kendoUpload");
    }

    // 将FileUploader绑定到IPLAT对象上
    IPLAT.FileUploader = FileUploader;

    /**
     * 处理流程图查看事件
     * @param {Object} grid - 表格对象
     * @description
     */
    function handleFlowchartClick(grid) {
        // 检查是否只选中了一行
        if (checkOneSelect(grid)) {
            // 获取选中行的流程实例ID
            const [{ processInstanceId }] = grid.getCheckedRows();
            // 检查流程实例ID是否有效
            if (IPLAT.isUndefined(processInstanceId) || IPLAT.isBlankString(processInstanceId)) {
                NotificationUtil("操作失败，原因[暂无相应的审批流程！]", "warning");
                return;
            }
            // 构造流程图URL
            const strURL = `EWPI01?inqu_status-0-processInstanceId=${processInstanceId}`;
            // 设置弹出窗口选项
            popupWindow.setOptions({
                open: () => popupWindow.refresh({ url: strURL })
            });
            // 打开并居中显示弹出窗口
            popupWindow.open().center();
        }
    }

    /**
     * 将数据回填到详情页
     * @param {Object} model - 数据对象
     * @param {string} nodeStr - 节点字符串
     */
    function fillNode(model, nodeStr) {
        try {
            const eiInfo = new EiInfo();
            eiInfo.setByNode(nodeStr);
            const blocks = eiInfo.getBlocks();
            const eiBlock = eiInfo.getBlock(Object.keys(blocks)[0]);
            // 构建数据数组
            const dataArray = Object.keys(eiBlock.meta.metas).map((key) => {
                // 处理未定义的值
                if (IPLAT.isUndefined(model[key])) {
                    return "";
                }
                // 处理数值类型
                if (typeof model[key] === "number") {
                    return model[key] === "0E-8" ? 0 : model[key];
                }
                // 处理字符串类型
                return model[key].trim();
            });
            // 设置数据行
            eiBlock.setRows([dataArray]);
            // 获取目标节点并填充数据
            const targetNode = document.getElementById(nodeStr);
            IPLAT.fillNode(targetNode, eiInfo);
        } catch (e) {
            console.log("数据回填失败,请检查传入参数!");
        }
    }

    /**
     * 通过配置文件获取报表打印路径
     * @returns {string}
     */
    function getReportAddress() {

        let reportAddress;
        var eiInfo = new EiInfo();
        EiCommunicator.send("LIRL0000", "getReportAddress", eiInfo, {
            onSuccess: function (ei) {
                if ("-1" != ei.status) {
                    reportAddress = ei.get("reportAddress");
                }
            }
        }, {async: false})
        return reportAddress;
    }

    /**
     * export 到全局作用域 window对象.
     * 说明：将常用的JS工具类进行封装.
     * <AUTHOR> TEL:17762608719  （开发人）.
     * @date 2022年3月26日 上午9:00:00  （开发日期）.
     * @version 1.0 （开发版本）.
     */
    $.extend(window, {
        IMOMUtil: {
            submitGridsData: submitGridsData,
            gridSetData: gridSetData,
            checkSelected: checkSelected,
            checkOneSelect: checkOneSelect,
            excelExport: excelExport,
            excelPageExport,
            kendoExport,
            getGridColumns,
            getGrids,
            setExportColumnBlock,
            setExportSheetColumnBlock,
            formatNumber,
            filter0E_8,
            propToFloat,
            fmtGridData,
            callService,
            importUnitInfo,
            initEFSelect,
            refreshEFSelect,
            refreshEFInput,
            refreshEFComboColumn,
            syncTabGridQuery,
            context,
            bindEFGridDropDownList,
            watchDataSourceItems,
            onceClick,
            value,
            checkedRows2Block,
            fillUnitInfo,
            fillUnitInfo2,
            submitNode,
            submitEiInfo,
            windowTemplate,
            handleFlowchartClick,
            fillNode,
            importDetailUnitInfo,
            importDetailEquipmentInfo,
            importDetailDeviceInfo,
            getReportAddress
        },
        DateUtils,
        URLUtils,
        ObjectUtils
    });
})(window.jQuery);
