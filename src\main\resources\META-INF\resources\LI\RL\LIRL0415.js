$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });



    $("#QUERY2").on("click", function (e) {
        result2Grid.dataSource.page(1);
    });

    /*setInterval(function () {
        resultGrid.dataSource.page(1);
    }, 20000);
    setInterval(function () {
        result2Grid.dataSource.page(1);
    }, 20000);*/

    $("#QUERY_SEG").on("click", function () {
        resultZGrid.dataSource.page(1);
    });

    $("#QUERY_FACTORY").on("click", function () {
        $("#inqux_status-0-segNo2").val("");
        if ($("#inqux_status-0-segNo1").val() === "") {
            NotificationUtil("请选择要查询的业务单元!", "error");
            var queryFactoryArea = $("#queryFactoryArea").data("kendoWindow");
            queryFactoryArea.close();
            return false;
        } else {
            resultXGrid.dataSource.page(1);
        }
    });

    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // TODO 查询 按钮事件
                $("#UPDATE").on("click", function (e) {
                    var inInfo = new EiInfo();
                    var checkedRows = resultGrid.getCheckedRows();
                    if (checkedRows.length === 0) {
                        return;
                    }
                    inInfo.setByNode("result");
                    inInfo.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0415", "vehicleIntoFactoryFlagCQ", inInfo, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    }, {async: false});
                });
            },
        },
        "result2": {
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // TODO 查询 按钮事件
                $("#UPDATE2").on("click", function (e) {
                    var inInfo = new EiInfo();
                    var checkedRows = result2Grid.getCheckedRows();
                    if (checkedRows.length === 0) {
                        return;
                    }
                    inInfo.setByNode("result2");
                    inInfo.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0415", "vehicleLeaveFactory", inInfo, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                result2Grid.dataSource.page(1);
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    }, {async: false});
                });
            },
        },
        "resultX": {
            onRowDblClick: function (e) {
                var factoryArea = e.model.factoryArea;//厂区编码
                var factoryAreaName = e.model.factoryAreaName;//厂区名称
                $("#inqu_status-0-factoryArea").val(factoryArea);
                $("#inqu_status-0-factoryAreaName").val(factoryAreaName);
                var queryFactoryArea = $("#queryFactoryArea").data("kendoWindow");
                queryFactoryArea.close();
            }
        },
        "resultZ": {
            onRowDblClick: function (e) {
                var segNo = e.model.segNo;//业务单元号
                var segName = e.model.segFullName;//业务单元名称
                $("#inqu_status-0-segNo").val(segNo);
                $("#inqu_status-0-segCname").val(segName);
                var querySegInfo = $("#querySegInfo").data("kendoWindow");
                querySegInfo.close();
            }
        },
    }
    IPLATUI.EFPopupInput = {
        "inqu_status-0-segNo": {
            clearInput: function (e) {
                $("#inqu_status-0-segNo").val("");
                $("#inqu_status-0-segCname").val("");
            }
        },
        "inqu_status-0-factoryArea": {
            init: function (e) {
                if ($("#inqu_status-0-segNo").val() === "") {
                    var inInfo = new EiInfo();
                    EiCommunicator.send("LIRL0415", "querySegNameInit", inInfo, {
                        // 服务调用成功后的回调函数 onSuccess
                        onSuccess: function (response) {
                            if (response.getStatus() == -1) {//-1表示后台返回的状态为异常
                                NotificationUtil(response.getMsg(), "error");
                            } else {
                                $("#inqu_status-0-segNo").val(response.get("segNo"));
                                $("#inqu_status-0-segCname").val(response.get("segCname"));
                                $("#inqux_status-0-segNo1").val(response.get("segNo"));
                                $("#inqux_status-0-segNo2").val(response.get("segNo"));
                            }
                        },
                        // 服务调用失败后的回调函数 onFail
                        onFail: function (inInfo) {
                            NotificationUtil(inInfo.getMsg(), "error");
                        }
                    }, {async: false});
                } else {
                    $("#inqux_status-0-segNo1").val($("#inqu_status-0-segNo").val());
                    $("#inqux_status-0-segNo2").val($("#inqu_status-0-segCname").val());
                }
            },
            clearInput: function (e) {
                $("#inqu_status-0-factoryArea").val("");
                $("#inqu_status-0-factoryAreaName").val("");
            }
        }
    }
    IPLATUI.EFWindow = {
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }


            }
        }
    }
});
