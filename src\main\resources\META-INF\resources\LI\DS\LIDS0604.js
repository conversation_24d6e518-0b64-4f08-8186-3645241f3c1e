$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();
    //编辑行
    let editorModel;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });


    IPLATUI.EFGrid = {
        "result": {
            columns: [{
                field: "crossArea",
                enable: true,
                readonly: true,
                hidden: false,
                locked: false,
                title: "跨区代码",
                editor: function (container, param) {
                    // 设置产生弹框model
                    if (container.hasClass("fake-edit")) {
                        container.removeClass("fake-edit");
                    } else {
                        editorModel = param.model;
                        IPLAT.Popup.popupContainer({
                            containerId: "crossAreaInfo",
                            textElement: $(container),
                            width: 600,
                            center: true,
                            title: "跨区查询"
                        })
                    }
                }
            },{
                field: "pollingSchemeNumber",
                enable: true,
                readonly: true,
                hidden: false,
                locked: false,
                title: "轮询方案编号",
                editor: function (container, param) {
                    // 设置产生弹框model
                    if (container.hasClass("fake-edit")) {
                        container.removeClass("fake-edit");
                    } else {
                        editorModel = param.model;
                        IPLAT.Popup.popupContainer({
                            containerId: "pollingSchemeNumberInfo",
                            textElement: $(container),
                            width: 600,
                            center: true,
                            title: "轮询方案编号"
                        })
                    }
                }
            },{
                field: "wproviderId",
                readonly: true,
                hidden: false,
                locked: false,
                title: "仓库",
                editor: function (container, param) {
                    // 设置产生弹框model
                    if (container.hasClass("fake-edit")) {
                        container.removeClass("fake-edit");
                    } else {
                        editorModel = param.model;
                        IPLAT.Popup.popupContainer({
                            containerId: "warehouseCodeInfo",
                            textElement: $(container),
                            width: 600,
                            center: true,
                            title: "仓库基础信息"
                        })
                    }
                }
            }], loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

            }, afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.refresh();
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo", notInqu: true, afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
            //隐藏按钮
            iframejQuery("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-insertsave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-updatesave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-VALIDATE']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-DEVALIDATION']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-delete']").attr("style", "display:none;");
        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                resultGrid.setCellValue(editorModel, "factoryArea", rows[0].factoryArea);
                resultGrid.setCellValue(editorModel, "factoryAreaName", rows[0].factoryAreaName);
                resultGrid.setCellValue(editorModel, "factoryBuilding", rows[0].factoryBuilding);
                resultGrid.setCellValue(editorModel, "factoryBuildingName", rows[0].factoryBuildingName);
                resultGrid.setCellValue(editorModel, "crossArea", rows[0].crossArea);
                resultGrid.setCellValue(editorModel, "crossAreaName", rows[0].crossAreaName);
            }
        }
    });



    //轮询方案
    IMOMUtil.windowTemplate({
        windowId: "pollingSchemeNumberInfo", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            /*iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);*/
            //隐藏按钮
            iframejQuery("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-insertsave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-updatesave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-VALIDATE']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-DEVALIDATION']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-delete']").attr("style", "display:none;");
        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                resultGrid.setCellValue(editorModel, "pollingSchemeNumber", rows[0].pollingSchemeNumber);
            }
        }
    });

    //仓库
    IMOMUtil.windowTemplate({
        windowId: "warehouseCodeInfo", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-windowId").val("warehouseCodeInfo");

        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                resultGrid.setCellValue(editorModel, "wproviderId", rows[0].stockCode);
            }
        }
    });

})