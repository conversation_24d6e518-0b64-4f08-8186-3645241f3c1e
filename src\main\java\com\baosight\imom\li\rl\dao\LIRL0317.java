/**
* Generate time : 2025-03-18 9:25:24
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0317
* 
*/
public class LIRL0317 extends DaoEPBase {
    public static final String QUERY = "LIRL0317.query";
    public static final String QUERY_D = "LIRL0317.queryD";
    public static final String COUNT = "LIRL0317.count";
    public static final String INSERT = "LIRL0317.insert";
    public static final String UPDATE = "LIRL0317.update";
    public static final String DELETE = "LIRL0317.delete";
    public static final String DISTINCT_QUERY = "LIRL0317.distinctQuery";

    private String segNo = " ";		/* 业务单元代代码*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String segName = " ";		/* 业务单元代简称*/
                private String status = " ";		/* 状态(撤销：00、新增：10、生效：20)*/
                private String typeOfHandling = " ";		/* 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)*/
                private String reservationIdentity = " ";		/* 预约身份(包含承运商和客户)*/
                private String customerId = " ";		/* 承运商/客户代码*/
                private String customerName = " ";		/* 承运商/客户名称*/
                private String siteType = " ";		/* 站点类型*/
                private String siteName = " ";		/* 站点名称*/
                private String revStartTime = " ";		/* 预约保留时段起*/
                private String revEndTime = " ";		/* 预约保留时段止*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10、生效：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("typeOfHandling");
        eiColumn.setDescName("装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationIdentity");
        eiColumn.setDescName("预约身份(包含承运商和客户)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("siteType");
        eiColumn.setDescName("站点类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("siteName");
        eiColumn.setDescName("站点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("revStartTime");
        eiColumn.setDescName("预约保留时段起");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("revEndTime");
        eiColumn.setDescName("预约保留时段止");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0317() {
initMetaData();
}


    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
        * get the segNo - 业务单元代代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the status - 状态(撤销：00、新增：10、生效：20)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(撤销：00、新增：10、生效：20)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the typeOfHandling - 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
        * @return the typeOfHandling
        */
        public String getTypeOfHandling() {
        return this.typeOfHandling;
        }

        /**
        * set the typeOfHandling - 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
        */
        public void setTypeOfHandling(String typeOfHandling) {
        this.typeOfHandling = typeOfHandling;
        }
        /**
        * get the reservationIdentity - 预约身份(包含承运商和客户)
        * @return the reservationIdentity
        */
        public String getReservationIdentity() {
        return this.reservationIdentity;
        }

        /**
        * set the reservationIdentity - 预约身份(包含承运商和客户)
        */
        public void setReservationIdentity(String reservationIdentity) {
        this.reservationIdentity = reservationIdentity;
        }
        /**
        * get the customerId - 承运商/客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 承运商/客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 承运商/客户名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 承运商/客户名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }
        /**
        * get the siteType - 站点类型
        * @return the siteType
        */
        public String getSiteType() {
        return this.siteType;
        }

        /**
        * set the siteType - 站点类型
        */
        public void setSiteType(String siteType) {
        this.siteType = siteType;
        }
        /**
        * get the siteName - 站点名称
        * @return the siteName
        */
        public String getSiteName() {
        return this.siteName;
        }

        /**
        * set the siteName - 站点名称
        */
        public void setSiteName(String siteName) {
        this.siteName = siteName;
        }
        /**
        * get the revStartTime - 预约保留时段起
        * @return the revStartTime
        */
        public String getRevStartTime() {
        return this.revStartTime;
        }

        /**
        * set the revStartTime - 预约保留时段起
        */
        public void setRevStartTime(String revStartTime) {
        this.revStartTime = revStartTime;
        }
        /**
        * get the revEndTime - 预约保留时段止
        * @return the revEndTime
        */
        public String getRevEndTime() {
        return this.revEndTime;
        }

        /**
        * set the revEndTime - 预约保留时段止
        */
        public void setRevEndTime(String revEndTime) {
        this.revEndTime = revEndTime;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setTypeOfHandling(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("typeOfHandling")), typeOfHandling));
                setReservationIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationIdentity")), reservationIdentity));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setSiteType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("siteType")), siteType));
                setSiteName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("siteName")), siteName));
                setRevStartTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("revStartTime")), revStartTime));
                setRevEndTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("revEndTime")), revEndTime));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("typeOfHandling",StringUtils.toString(typeOfHandling, eiMetadata.getMeta("typeOfHandling")));
                map.put("reservationIdentity",StringUtils.toString(reservationIdentity, eiMetadata.getMeta("reservationIdentity")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("siteType",StringUtils.toString(siteType, eiMetadata.getMeta("siteType")));
                map.put("siteName",StringUtils.toString(siteName, eiMetadata.getMeta("siteName")));
                map.put("revStartTime",StringUtils.toString(revStartTime, eiMetadata.getMeta("revStartTime")));
                map.put("revEndTime",StringUtils.toString(revEndTime, eiMetadata.getMeta("revEndTime")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));

return map;

}
}