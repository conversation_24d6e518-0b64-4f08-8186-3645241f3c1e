<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-27 9:31:25
       Version :  1.0
    tableName :${meliSchema}.tlirl0302
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CHECK_ID  VARCHAR   NOT NULL,
     CAR_TRACE_NO  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     HAND_TYPE  VARCHAR   NOT NULL,
     VEHICLE_NO  VARCHAR   NOT NULL,
     ID_CARD  VARCHAR   NOT NULL,
     DRIVER_NAME  VARCHAR   NOT NULL,
     TEL_NUM  VARCHAR   NOT NULL,
     RESERVATION_NUMBER  VARCHAR   NOT NULL,
     CHECK_SOURCE  VARCHAR   NOT NULL,
     VOUCHER_NUM  VARCHAR   NOT NULL,
     BUSINESS_TYPE  VARCHAR   NOT NULL,
     RESERVATION_TIME  VARCHAR   NOT NULL,
     CHECK_DATE  VARCHAR   NOT NULL,
     LATE_EARLY_FLAG  VARCHAR   NOT NULL,
     CALL_DATE  VARCHAR   NOT NULL,
     CALL_TIME  VARCHAR   NOT NULL,
     FACTORY_AREA  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0302">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkId">
            a.CHECK_ID = #checkId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNoIsNull">
            (a.CAR_TRACE_NO = ' '
            or a.CAR_TRACE_NO is null)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            a.HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="idCard">
            a.ID_CARD = #idCard#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM = #telNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumber">
            a.RESERVATION_NUMBER = #reservationNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkSource">
            a.CHECK_SOURCE = #checkSource#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            a.VOUCHER_NUM like concat('%',#voucherNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessType">
            a.BUSINESS_TYPE = #businessType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationDate">
            a.RESERVATION_DATE = #reservationDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            a.RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkDate">
            a.CHECK_DATE = #checkDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="lateEarlyFlag">
            a.LATE_EARLY_FLAG = #lateEarlyFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="callDate">
            a.CALL_DATE = #callDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="callTime">
            a.CALL_TIME = #callTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            a.FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            a.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            a.SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            a.TENANT_ID = #tenantId#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(a.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(a.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
        <!--预约时间起-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            substr(a.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
        </isNotEmpty>
        <!--预约时间止-->
        <isNotEmpty prepend=" and " property="reservationTimeEnd">
            substr(a.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationTimeEnd#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationDate">
            substr(a.RESERVATION_DATE,1,8) = substr(replace(#reservationDate#,'-',''),1,8)
        </isNotEmpty>

    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0302">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CHECK_ID as "checkId",  <!-- 车辆登记流水号 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        a.STATUS as "status",  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_SOURCE as "checkSource",  <!-- 数据来源(10一体机) -->
        a.VOUCHER_NUM as "voucherNum",  <!-- 提单号 -->
        ifnull(if(a.BUSINESS_TYPE = '', null, a.BUSINESS_TYPE),(select ITEM_CNAME from iplat4j.tedcm01 tedcm01 where
        tedcm01.ITEM_CODE = a.HAND_TYPE and tedcm01.CODESET_CODE = 'P007')) as
        "businessType",  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 车辆预约时间 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 车辆预约时段 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.LATE_EARLY_FLAG as "lateEarlyFlag",  <!-- 迟到早到标记 -->
        a.CALL_DATE as "callDate",  <!-- 叫号日期 -->
        a.CALL_TIME as "callTime",  <!-- 叫号时段 -->
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId",<!-- 租户ID -->
        (case
        when (select CUSTOMER_NAME
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1) = null
        then
        case
        when a.VOUCHER_NUM = null || a.VOUCHER_NUM = ' '
        then
        (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0102
        where 1 = 1
        and tlirl0102.SEG_NO = a.SEG_NO
        and tlirl0102.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0102.TEL = a.TEL_NUM
        limit 1)
        else
        (select CUSTOMER_NAME
        from meli.tlirl0307 tlirl0307
        where 1 = 1
        and tlirl0307.SEG_NO = a.SEG_NO
        and tlirl0307.VOUCHER_NUM = a.VOUCHER_NUM
        limit 1)
        end
        else
        (select CUSTOMER_NAME
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1)
        end
        ) as "customerName"
        FROM ${meliSchema}.tlirl0302 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>


    <select id="queryCheckDate" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0302">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CHECK_ID as "checkId",  <!-- 车辆登记流水号 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        a.STATUS as "status",  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_SOURCE as "checkSource",  <!-- 数据来源(10一体机) -->
        a.VOUCHER_NUM as "voucherNum",  <!-- 提单号 -->
        ifnull(if(a.BUSINESS_TYPE = '', null, a.BUSINESS_TYPE),(select ITEM_CNAME from iplat4j.tedcm01 tedcm01 where
        tedcm01.ITEM_CODE = a.HAND_TYPE and tedcm01.CODESET_CODE = 'P007')) as
        "businessType",  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 车辆预约时间 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 车辆预约时段 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.LATE_EARLY_FLAG as "lateEarlyFlag",  <!-- 迟到早到标记 -->
        a.CALL_DATE as "callDate",  <!-- 叫号日期 -->
        a.CALL_TIME as "callTime",  <!-- 叫号时段 -->
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName"
        FROM ${meliSchema}.tlirl0302 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0302">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CHECK_ID as "checkId",  <!-- 车辆登记流水号 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        (case
        when (select tlirl0301.STATUS
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1) = '00'
        then
        (select tlirl0301.STATUS
        from meli.tlirl0311 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1)
        else
        (select tlirl0301.STATUS
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1) end
        ) as "carTraceStatus",  <!-- 车辆跟踪单状态 -->
        a.STATUS as "status",  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_SOURCE as "checkSource",  <!-- 数据来源(10一体机) -->
        a.VOUCHER_NUM as "voucherNum",  <!-- 提单号 -->
        (case
        when a.VOUCHER_NUM = ' '
        then
        case
        when a.BUSINESS_TYPE = '10'
        then
        '20'
        when a.BUSINESS_TYPE = '20'
        then
        '60'
        when a.BUSINESS_TYPE = '30'
        then '40'
        when a.BUSINESS_TYPE = '40'
        then
        '50'
        when a.BUSINESS_TYPE = '50'
        then '70'
        when a.BUSINESS_TYPE = '60'
        then '80'
        end
        else
        case
        when a.BUSINESS_TYPE = ' ' || a.BUSINESS_TYPE = null
        THEN
        case
        when a.HAND_TYPE = '10'
        then
        '10'
        when a.HAND_TYPE = '30'
        then
        '30'
        end
        else
        case
        when a.BUSINESS_TYPE = '10'
        then
        '10'
        when a.BUSINESS_TYPE = '30'
        then
        '30'
        end
        end
        end)
        as "businessType",  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 车辆预约时间 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 车辆预约时段 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.LATE_EARLY_FLAG as "lateEarlyFlag",  <!-- 迟到早到标记 -->
        a.CALL_DATE as "callDate",  <!-- 叫号日期 -->
        a.CALL_TIME as "callTime",  <!-- 叫号时段 -->
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId",<!-- 租户ID -->
        (case
        when a.CUSTOMER_NAME = null || a.CUSTOMER_NAME = ' '
        then
        case
        when (select CUSTOMER_NAME
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1) = null then case
        when a.VOUCHER_NUM = null || a.VOUCHER_NUM = ' '
        then (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0102
        where 1 = 1
        and tlirl0102.SEG_NO = a.SEG_NO
        and tlirl0102.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0102.TEL = a.TEL_NUM
        limit 1)
        else (select CUSTOMER_NAME
        from meli.tlirl0307 tlirl0307
        where 1 = 1
        and tlirl0307.SEG_NO = a.SEG_NO
        and tlirl0307.VOUCHER_NUM = a.VOUCHER_NUM
        limit 1) end
        else case
        when (select count(1)
        from meli.tlirl0102 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.TEL = a.TEL_NUM
        and tlirl0201.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0201.STATUS = '20') > 1 then ' '
        else (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.TEL = a.TEL_NUM
        and tlirl0201.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0201.STATUS = '20'
        limit 1) end end
        else
        a.CUSTOMER_NAME end
        ) as "customerName",
        a.CUSTOMER_ID as "customerId",
        (select tlirl0407.NEXT_TATGET
        from ${meliSchema}.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID = (select max(tlirl0407.FINISH_LOAD_ID)
        from ${meliSchema}.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'))             as "nextTarget"
        FROM ${meliSchema}.tlirl0302 a WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend="and" property="vehicleNoStr">
                a.VEHICLE_NO IN
                <iterate open="(" close=")" conjunction="," property="vehicleNoStr">
                    #vehicleNoStr[]#
                </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNumN">
            a.VOUCHER_NUM = ' '
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                <isNotEmpty property="pendingReview">
                    REC_CREATE_TIME asc
                </isNotEmpty>
                <isEmpty property="pendingReview">
                    REC_CREATE_TIME DESC
                </isEmpty>
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0302 a WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryLirl0302" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT SEG_NO             as segNo,
               RESERVATION_NUMBER as "reservationNumber",
               UUID               as "uuid"
        FROM MELI.tlirl0302
        WHERE 1 = 1
          and STATUS = '10'
          AND SEG_NO = #segNo#
          AND (CAR_TRACE_NO != #notCarTraceNo#)
          AND VEHICLE_NO = #vehicleNo#
          AND DEL_FLAG = 0
    </select>

    <select id="queryAllExport" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.CHECK_ID as "checkId",  <!-- 车辆登记流水号 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        (select tlirl0301.STATUS
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1) as "carTraceStatus",  <!-- 车辆跟踪单状态 -->
        a.STATUS as "status",  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_SOURCE as "checkSource",  <!-- 数据来源(10一体机) -->
        a.VOUCHER_NUM as "voucherNum",  <!-- 提单号 -->
        (case
        when a.VOUCHER_NUM = ' '
        then
        case
        when a.BUSINESS_TYPE = '10'
        then
        '20'
        when a.BUSINESS_TYPE = '20'
        then
        '60'
        when a.BUSINESS_TYPE = '30'
        then '40'
        when a.BUSINESS_TYPE = '40'
        then
        '50'
        when a.BUSINESS_TYPE = '50'
        then '70'
        when a.BUSINESS_TYPE = '60'
        then '80'
        end
        else
        case
        when a.BUSINESS_TYPE = ' ' || a.BUSINESS_TYPE = null
        THEN
        case
        when a.HAND_TYPE = '10'
        then
        '10'
        when a.HAND_TYPE = '30'
        then
        '30'
        end
        else
        case
        when a.BUSINESS_TYPE = '10'
        then
        '10'
        when a.BUSINESS_TYPE = '30'
        then
        '30'
        end
        end
        end)
        as "businessType",  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 车辆预约时间 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 车辆预约时段 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.LATE_EARLY_FLAG as "lateEarlyFlag",  <!-- 迟到早到标记 -->
        a.CALL_DATE as "callDate",  <!-- 叫号日期 -->
        a.CALL_TIME as "callTime",  <!-- 叫号时段 -->
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId",<!-- 租户ID -->
        (case
        when a.CUSTOMER_NAME = null || a.CUSTOMER_NAME = ' '
        then
        case
        when (select CUSTOMER_NAME
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1) = null then case
        when a.VOUCHER_NUM = null || a.VOUCHER_NUM = ' '
        then (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0102
        where 1 = 1
        and tlirl0102.SEG_NO = a.SEG_NO
        and tlirl0102.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0102.TEL = a.TEL_NUM
        limit 1)
        else (select CUSTOMER_NAME
        from meli.tlirl0307 tlirl0307
        where 1 = 1
        and tlirl0307.SEG_NO = a.SEG_NO
        and tlirl0307.VOUCHER_NUM = a.VOUCHER_NUM
        limit 1) end
        else case
        when (select count(1)
        from meli.tlirl0102 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.TEL = a.TEL_NUM
        and tlirl0201.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0201.STATUS = '20') > 1 then ' '
        else (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.TEL = a.TEL_NUM
        and tlirl0201.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0201.STATUS = '20'
        limit 1) end end
        else
        a.CUSTOMER_NAME end
        ) as "customerName",
        a.CUSTOMER_ID as "customerId"
        FROM ${meliSchema}.tlirl0302 a WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend=" and " property="voucherNumN">
            a.VOUCHER_NUM = ' '
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="checkPendingNonAppointment" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select VEHICLE_NO                                                                             as "vehicleNo",
               ifnull(
                       (select tlirl0307.D_USER_NAME
                        from meli.tlirl0307 tlirl0307
                        where tlirl0307.SEG_NO = tlirl0302.SEG_NO
                          and tlirl0307.VOUCHER_NUM = tlirl0302.VOUCHER_NUM
                           limit 1)
           , ' ')                                                   as "customerName",
               (
                   case
                       when VOUCHER_NUM != ' '
                   then case
                                                                    when BUSINESS_TYPE = '10'
                                                                        then '钢材装货'
                                                                    when BUSINESS_TYPE = '30'
                                                                        then
                                                                        '钢材卸货+装货'
                           end
                       when VOUCHER_NUM = ' '
                           then
                           case
                               when BUSINESS_TYPE = '10'
                                   then
                                   '钢材卸货'
                               when BUSINESS_TYPE = '20'
                                   then '废料提货'
                               when BUSINESS_TYPE = '30'
                                   then '托架运输'
                               when BUSINESS_TYPE = '40'
                                   then '资材卸货'
                               when BUSINESS_TYPE = '50'
                                   then '欧冶提货'
                               else '欧冶提货' end
                       end
                   )                                                                                  as "businessType",

               TIMESTAMPDIFF(MINUTE, STR_TO_DATE((tlirl0302.REC_CREATE_TIME), '%Y%m%d%H%i%s'), now()) as "waitingTime"
        from meli.tlirl0302
        where SEG_NO = #segNo#
          and STATUS = '10'
          and DEL_FLAG = '0'
    </select>


    <select id="checkPendingNonAppointmentCount" resultClass="int">
        select count(1) as "count"
        from meli.tlirl0302
        where SEG_NO = #segNo#
          and STATUS = '10'
          and DEL_FLAG = '0'
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkId">
            CHECK_ID = #checkId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="idCard">
            ID_CARD = #idCard#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            TEL_NUM = #telNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumber">
            RESERVATION_NUMBER = #reservationNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkSource">
            CHECK_SOURCE = #checkSource#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessType">
            BUSINESS_TYPE = #businessType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkDate">
            CHECK_DATE = #checkDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="lateEarlyFlag">
            LATE_EARLY_FLAG = #lateEarlyFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="callDate">
            CALL_DATE = #callDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="callTime">
            CALL_TIME = #callTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0302 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        CHECK_ID,  <!-- 车辆登记流水号 -->
        CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
        STATUS,  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        HAND_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO,  <!-- 车牌号 -->
        ID_CARD,  <!-- 身份证号 -->
        DRIVER_NAME,  <!-- 驾驶员姓名 -->
        TEL_NUM,  <!-- 手机号 -->
        RESERVATION_NUMBER,  <!-- 车辆预约单号 -->
        CHECK_SOURCE,  <!-- 数据来源(10一体机) -->
        VOUCHER_NUM,  <!-- 提单号 -->
        BUSINESS_TYPE,  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        RESERVATION_DATE,  <!-- 车辆预约时间 -->
        RESERVATION_TIME,  <!-- 车辆预约时段 -->
        CHECK_DATE,  <!-- 进厂登记时间 -->
        LATE_EARLY_FLAG,  <!-- 迟到早到标记 -->
        CALL_DATE,  <!-- 叫号日期 -->
        CALL_TIME,  <!-- 叫号时段 -->
        FACTORY_AREA,  <!-- 厂区 -->
        FACTORY_AREA_NAME,  <!-- 厂区 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID,  <!-- 租户ID -->
        CUSTOMER_ID,
        CUSTOMER_NAME
        )
        VALUES (#segNo#, #unitCode#, #checkId#, #carTraceNo#, #status#, #handType#, #vehicleNo#, #idCard#, #driverName#,
        #telNum#, #reservationNumber#, #checkSource#, #voucherNum#, #businessType#, #reservationDate#,
        #reservationTime#, #checkDate#,
        #lateEarlyFlag#, #callDate#, #callTime#, #factoryArea#,#factoryAreaName#,#recCreator#, #recCreatorName#,
        #recCreateTime#,
        #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#,
        #tenantId#,#customerId#,#customerName#)
    </insert>

    <delete id="delete">
        UPDATE ${meliSchema}.tlirl0302
        SET
        STATUS = #status#,   <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#   <!-- 记录删除标记 -->
        <isNotEmpty prepend="," property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend="," property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        WHERE
        UUID = #uuid#
    </delete>

    <delete id="deleteStatus">
        UPDATE ${meliSchema}.tlirl0302
        SET
        STATUS = '00',   <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = '1'   <!-- 记录删除标记 -->
        <isNotEmpty prepend="," property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend="," property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        WHERE
          SEG_NO=#segNo#
          and
          CAR_TRACE_NO = #carTraceNo#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0302
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        CHECK_ID = #checkId#,   <!-- 车辆登记流水号 -->
        CAR_TRACE_NO = #carTraceNo#,   <!-- 车辆跟踪号 -->
        STATUS = #status#,   <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        HAND_TYPE = #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        ID_CARD = #idCard#,   <!-- 身份证号 -->
        DRIVER_NAME = #driverName#,   <!-- 驾驶员姓名 -->
        TEL_NUM = #telNum#,   <!-- 手机号 -->
        RESERVATION_NUMBER = #reservationNumber#,   <!-- 车辆预约单号 -->
        CHECK_SOURCE = #checkSource#,   <!-- 数据来源(10一体机) -->
        VOUCHER_NUM = #voucherNum#,   <!-- 提单号 -->
        BUSINESS_TYPE = #businessType#,   <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        RESERVATION_DATE = #reservationDate#,   <!-- 车辆预约时间 -->
        RESERVATION_TIME = #reservationTime#,   <!-- 车辆预约时段 -->
        CHECK_DATE = #checkDate#,   <!-- 进厂登记时间 -->
        LATE_EARLY_FLAG = #lateEarlyFlag#,   <!-- 迟到早到标记 -->
        CALL_DATE = #callDate#,   <!-- 叫号日期 -->
        CALL_TIME = #callTime#,   <!-- 叫号时段 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        UUID = #uuid#,   <!-- uuid -->
        TENANT_ID = #tenantId#  <!-- 租户ID -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateByCustomerInfo">
        UPDATE ${meliSchema}.tlirl0302
        SET
        CUSTOMER_ID = #customerId#,   <!-- 承运商/客户代码 -->
        CUSTOMER_NAME = #customerName#  <!-- 承运商/客户名称 -->
        WHERE
        RESERVATION_NUMBER = #reservationNumber#
        and SEG_NO = #segNo#
        and ID_CARD=#driverIdentity#
        and DRIVER_NAME=#driverName#
        and TEL_NUM=#driverTel#
    </update>
    <select id="queryProcessingAppointmentBlacklist" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0302">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CHECK_ID as "checkId",  <!-- 车辆登记流水号 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        STATUS as "status",  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        ID_CARD as "idCard",  <!-- 身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        TEL_NUM as "telNum",  <!-- 手机号 -->
        RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        CHECK_SOURCE as "checkSource",  <!-- 数据来源(10一体机) -->
        VOUCHER_NUM as "voucherNum",  <!-- 提单号 -->
        BUSINESS_TYPE as "businessType",  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        RESERVATION_DATE as "reservationDate",  <!-- 车辆预约时间 -->
        RESERVATION_TIME as "reservationTime",  <!-- 车辆预约时段 -->
        CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        LATE_EARLY_FLAG as "lateEarlyFlag",  <!-- 迟到早到标记 -->
        CALL_DATE as "callDate",  <!-- 叫号日期 -->
        CALL_TIME as "callTime",  <!-- 叫号时段 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0302 WHERE 1=1
        AND DEL_FLAG = #delFlag#
        AND RESERVATION_NUMBER = #reservationNumber#
        <!--AND substr(CHECK_DATE,1,8) <![CDATA[=]]> substr(replace(#checkDate#,'-',''),1,8) -->
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>

    </select>

    <update id="updateDriver">
        UPDATE ${meliSchema}.tlirl0302
        SET
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        ID_CARD = #idCard#,   <!-- 身份证号 -->
        DRIVER_NAME = #driverName#,   <!-- 驾驶员姓名 -->
        TEL_NUM = #telNum#,   <!-- 手机号 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        SEG_NO = #segNo#
        AND TEL_NUM = #tel2#
    </update>

    <update id="updateTranslation">
        UPDATE ${meliSchema}.tlirl0302
        SET
        STATUS = #status#,   <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = '0',   <!-- 归档标记 -->
        DEL_FLAG = '1',   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#   <!-- 系统备注 -->
        WHERE
        SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <update id="updateTranslation1">
        UPDATE ${meliSchema}.tlirl0302
        SET
        STATUS = #status#,   <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = '0',   <!-- 归档标记 -->
        DEL_FLAG = '1',   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#   <!-- 系统备注 -->
        WHERE
        SEG_NO = #segNo#
        AND UUID = #uuid#
        and RESERVATION_NUMBER=#reservationNumber#

    </update>
    <!--查询最大装卸类型-->
    <select id="queryMaxHandType"
            resultClass="int">
        select max(HAND_TYPE)
        from ${meliSchema}.tlirl0302
        where SEG_NO = #segNo#
          and CAR_TRACE_NO = #carTraceNo#
          and VEHICLE_NO = #vehicleNo#
    </select>

    <select id="queryHandPointIsUsed" resultClass="int">
        select count(1)
        from ${meliSchema}.tlirl0305 tlirl0305
           , ${meliSchema}.tlirl0304 tlirl0304
        where 1 = 1
          and tlirl0305.SEG_NO = #segNo#
          and tlirl0305.CAR_TRACE_NO = #carTraceNo#
          and tlirl0305.VEHICLE_ID = #vehicleNo#
          and tlirl0305.SEG_NO = tlirl0304.SEG_NO
          and tlirl0305.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and tlirl0305.HAND_TYPE in ('20', '30')
          and tlirl0304.STATUS = '30'
          and exists(select tlirl0306.HAND_POINT_ID
                     from ${meliSchema}.tlirl0306 tlirl0306
                     where tlirl0306.SEG_NO = #segNo#
                       and tlirl0306.HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                       and tlirl0306.STATUS = '20')
    </select>
    <!--查询装卸点是否存在-->
    <select id="queryHandPointIsExist"
            resultClass="int">
        select count(1)
        from ${meliSchema}.tlirl0305 tlirl0305
           , ${meliSchema}.tlirl0304 tlirl0304
        where 1 = 1
          and tlirl0305.SEG_NO = #segNo#
          and tlirl0305.CAR_TRACE_NO = #carTraceNo#
          and tlirl0305.VEHICLE_ID = #vehicleNo#
          and tlirl0305.SEG_NO = tlirl0304.SEG_NO
          and tlirl0305.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and tlirl0304.STATUS = '30'
          and exists(select tlirl0306.HAND_POINT_ID
                     from ${meliSchema}.tlirl0306 tlirl0306
                     where tlirl0306.SEG_NO = #segNo#
                       and tlirl0306.HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                       and tlirl0306.STATUS = '20')
    </select>
    <!--查找最少装卸点-->
    <select id="queryMinHandPoint"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select tlirl0305.HAND_POINT_ID                                   as "handPointId",
               tlirl0305.SEG_NO                                          as "segNo",
               (tlirl0306.PRE_JOB_NUMBER + tlirl0306.CURRENT_JOB_NUMBER) as "jobNumber",
               tlirl0305.HAND_TYPE                                       as "handType"
        from ${meliSchema}.tlirl0304 tlirl0304,
             ${meliSchema}.tlirl0305 tlirl0305,
             ${meliSchema}.tlirl0306 tlirl0306
        where tlirl0304.SEG_NO = tlirl0305.SEG_NO
          and tlirl0304.SEG_NO = tlirl0306.SEG_NO
          and tlirl0305.SEG_NO = #segNo#
          and tlirl0305.CAR_TRACE_NO = #carTraceNo#
          and tlirl0305.VEHICLE_ID = #vehicleNo#
          and tlirl0305.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and tlirl0304.STATUS = '30'
          and tlirl0306.DEL_FLAG = '0'
          and tlirl0306.SEG_NO = tlirl0304.SEG_NO
          and tlirl0306.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and exists(select *
                     from ${meliSchema}.tlirl0306 tlirl0306
                     where tlirl0306.SEG_NO = #segNo#
                       and tlirl0306.HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                       and tlirl0306.STATUS = '20'
                       and tlirl0306.DEL_FLAG = '0')
        order by tlirl0305.QUEUE_NUMBER asc,
                 tlirl0304.ORDER_NUMBER asc,
                 (tlirl0306.PRE_JOB_NUMBER + tlirl0306.CURRENT_JOB_NUMBER) asc
    </select>

    <!--查找最少装卸点-->
    <select id="queryMinHandPointByJobNumber"
            resultClass="int">
        select COUNT(1)
        from ${meliSchema}.tlirl0304 tlirl0304,
             ${meliSchema}.tlirl0305 tlirl0305,
             ${meliSchema}.tlirl0306 tlirl0306
        where tlirl0304.SEG_NO = tlirl0305.SEG_NO
          and tlirl0305.SEG_NO = #segNo#
          and tlirl0305.CAR_TRACE_NO = #carTraceNo#
          and tlirl0305.VEHICLE_ID = #vehicleNo#
          and tlirl0305.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and tlirl0304.STATUS = '30'
          and tlirl0306.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and tlirl0306.SEG_NO = tlirl0304.SEG_NO
          and exists(select *
                     from ${meliSchema}.tlirl0306 tlirl0306
                     where tlirl0306.SEG_NO = #segNo#
                       and tlirl0306.HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                       and tlirl0306.STATUS = '20')
          and (tlirl0306.pre_job_number + tlirl0306.current_job_number) = #queueNumber#
    </select>

    <!--如果作业点数最小的有多个  再考虑后续进车最少以及最大容纳数问题-->
    <select id="queryHandPointByJobNumberDesc"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select tlirl0305.HAND_POINT_ID as "handPointId", tlirl0305.SEG_NO as "segNo"
        from ${meliSchema}.tlirl0304 tlirl0304,
             ${meliSchema}.tlirl0305 tlirl0305,
             ${meliSchema}.tlirl0306 tlirl0306
        where tlirl0304.SEG_NO = tlirl0305.SEG_NO
          and tlirl0304.SEG_NO = tlirl0306.SEG_NO
          and tlirl0305.SEG_NO = #segNo#
          and tlirl0305.CAR_TRACE_NO = #carTraceNo#
          and tlirl0305.VEHICLE_ID = #vehicleNo#
          and tlirl0305.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and tlirl0304.STATUS = '30'
          and tlirl0306.SEG_NO = tlirl0305.SEG_NO
          and tlirl0306.HAND_POINT_ID = tlirl0304.HAND_POINT_ID
          and exists(select 1
                     from ${meliSchema}.tlirl0306 tlirl03061
                     where tlirl03061.SEG_NO = #segNo#
                       and tlirl03061.HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                       and tlirl03061.STATUS = '20')
          and (tlirl0306.pre_job_number + tlirl0306.current_job_number) = #jobNumber#
        order by tlirl0304.ORDER_NUMBER asc, tlirl0305.QUEUE_NUMBER asc, tlirl0304.VEHICLE_NUMER desc


    </select>


    <select id="queryInfactoryVehicleRegAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.SEG_NO as "segNo",  <!-- 系统账套 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CHECK_ID as "checkId",  <!-- 车辆登记流水号 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        (case
        when (select tlirl0301.STATUS
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1) = '00'
        then
        (select tlirl0301.STATUS
        from meli.tlirl0311 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1)
        else
        (select tlirl0301.STATUS
        from meli.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = a.SEG_NO
        and tlirl0301.CAR_TRACE_NO = a.CAR_TRACE_NO
        limit 1) end
        ) as "carTraceStatus",  <!-- 车辆跟踪单状态 -->
        a.STATUS as "status",  <!-- 状态(00撤销  10待审核 20审核 99驳回) -->
        a.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.ID_CARD as "idCard",  <!-- 身份证号 -->
        a.DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        a.TEL_NUM as "telNum",  <!-- 手机号 -->
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 车辆预约单号 -->
        a.CHECK_SOURCE as "checkSource",  <!-- 数据来源(10一体机) -->
        a.VOUCHER_NUM as "voucherNum",  <!-- 提单号 -->
        (case
        when a.VOUCHER_NUM = ' '
        then
        case
        when a.BUSINESS_TYPE = '10'
        then
        '20'
        when a.BUSINESS_TYPE = '20'
        then
        '60'
        when a.BUSINESS_TYPE = '30'
        then '40'
        when a.BUSINESS_TYPE = '40'
        then
        '50'
        when a.BUSINESS_TYPE = '50'
        then '70'
        when a.BUSINESS_TYPE = '60'
        then '80'
        end
        else
        case
        when a.BUSINESS_TYPE = ' ' || a.BUSINESS_TYPE = null
        THEN
        case
        when a.HAND_TYPE = '10'
        then
        '10'
        when a.HAND_TYPE = '30'
        then
        '30'
        end
        else
        case
        when a.BUSINESS_TYPE = '10'
        then
        '10'
        when a.BUSINESS_TYPE = '30'
        then
        '30'
        end
        end
        end)
        as "businessType",  <!-- 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货) -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 车辆预约时间 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 车辆预约时段 -->
        a.CHECK_DATE as "checkDate",  <!-- 进厂登记时间 -->
        a.LATE_EARLY_FLAG as "lateEarlyFlag",  <!-- 迟到早到标记 -->
        a.CALL_DATE as "callDate",  <!-- 叫号日期 -->
        a.CALL_TIME as "callTime",  <!-- 叫号时段 -->
        a.FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        a.FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId",<!-- 租户ID -->
        (case
        when a.CUSTOMER_NAME = null || a.CUSTOMER_NAME = ' '
        then
        case
        when (select CUSTOMER_NAME
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO
        limit 1) = null then case
        when a.VOUCHER_NUM = null || a.VOUCHER_NUM = ' '
        then (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0102
        where 1 = 1
        and tlirl0102.SEG_NO = a.SEG_NO
        and tlirl0102.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0102.TEL = a.TEL_NUM
        limit 1)
        else (select CUSTOMER_NAME
        from meli.tlirl0307 tlirl0307
        where 1 = 1
        and tlirl0307.SEG_NO = a.SEG_NO
        and tlirl0307.VOUCHER_NUM = a.VOUCHER_NUM
        limit 1) end
        else case
        when (select count(1)
        from meli.tlirl0102 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.TEL = a.TEL_NUM
        and tlirl0201.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0201.STATUS = '20') > 1 then ' '
        else (select CUSTOMER_NAME
        from meli.tlirl0102 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = a.SEG_NO
        and tlirl0201.TEL = a.TEL_NUM
        and tlirl0201.DRIVER_IDENTITY = a.ID_CARD
        and tlirl0201.STATUS = '20'
        limit 1) end end
        else
        a.CUSTOMER_NAME end
        ) as "customerName",
        a.CUSTOMER_ID as "customerId",
        (select tlirl0407.NEXT_TATGET
        from ${meliSchema}.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID = (select max(tlirl0407.FINISH_LOAD_ID)
        from ${meliSchema}.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0407.STATUS != '00'))             as "nextTarget"
        FROM ${meliSchema}.tlirl0302 a WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend="and" property="vehicleNoStr">
            a.VEHICLE_NO IN
            <iterate open="(" close=")" conjunction="," property="vehicleNoStr">
                #vehicleNoStr[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNumN">
            a.VOUCHER_NUM = ' '
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                <isNotEmpty property="pendingReview">
                    REC_CREATE_TIME asc
                </isNotEmpty>
                <isEmpty property="pendingReview">
                    REC_CREATE_TIME DESC
                </isEmpty>
            </isEmpty>
        </dynamic>

    </select>

</sqlMap>