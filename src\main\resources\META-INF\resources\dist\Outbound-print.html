<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css">
    <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />
    <style type="">
        .container{
            width: 100%;
            margin: 140px 0px 50px;
            background-image: url("img/bg.png");
        }
        .main{
            width: 100%;
            display: flex;
            align-content: center;
            justify-content: space-around;
        }
        .information-bill{
            flex: 0.5;
        }
        .information-bill ul{
            display: flex;
            /*flex-wrap: wrap;*/
            justify-content: flex-start;
        }
        .information-bill ul li{
            display: flex;
            /*flex-wrap: wrap;*/
            justify-content: flex-start;
        }
         .btn{
             width: auto;
             flex: 0.45;
             display: flex;
             align-content: center;
             justify-content: space-around;
             height: auto;
         }
         .ipt1{
             width: 140px;
         }
        table tbody{
            max-height: 460px;
        }
        #telKeyboard {
            display: none;
            position: absolute;
            flex-wrap: wrap;
            width: 60%;
            margin-top: 10px;
            background-color: white;
            border: 1px solid #ccc;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        #carKeyboard {
            display: none;
            position: absolute;
            flex-wrap: wrap;
            width: 60%;
            margin-top: 10px;
            background-color: white;
            border: 1px solid #ccc;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <div class="header">
            <div id="logo" class="logo-baosight"></div>
<!--            <div class="title">工贸一体机登记</div>-->
            <div class="header-return">
                <button class="return-home-btn" onclick="returnHome()">返回主页</button>
            </div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li>1.登记车牌号</li>
                    <li class="arrow"></li>
                    <li class="fontblue">2.出库单打印</li>
                </ul>
            </div>
        </div>
        <div style="float: left;height: 10px;margin-left: 5%;margin-top: 10px;">
            <p class="div-p-wxts" style="color: #FF0000;">*</p>温馨提示：
            请从列表中选择您需要打印的<p>出库单</p>，然后点击<p>“打印”</p>按钮。接下来，只需稍作等待，打印机就会为您输出所需的文件。注意：请等待<p>所有</p>的出库单打印完毕。
        </div>
        <div class="container">
            <div class="main">
                <div class="information-bill">
                    <ul>
                        <li><span class="tag" style="float: none">查询条件：</span></li>
                        <li><span class="tag">车牌号</span><input id="bill_id" class="ipt1 ipt-car-number" type="text" placeholder="请输入车牌号"></li>
                        <li><span class="tag">提单号</span><input id="bill_id1" class="ipt1 ipt-ti-dan" type="text" placeholder="请输入提单号"></li>
                        <li><span class="tag">手机号</span><input id="bill_id2" class="ipt1 ipt-phone" type="text" placeholder="请输入手机号"></li>
                        <li style="margin-left: auto;margin-right: auto">
                            <input style="width: 6em;" type="checkbox" id="confirmCheckbox" checked /> <span class="tag">只查本日</span>
                        </li>
                    </ul>
                </div>
                <div id="carKeyboard" style="display: none;"></div>
                <div id="telKeyboard" style="display: none;"></div>
                <div id="keyboard" style="display: none;"></div>
                <div class="btn">
                    <button id="indexRead" class="btn2-flex-item" onclick="queryData()">查询</button>
                    <button class="btn2-flex-item" onclick="javascript :history.back(-1);">上一步</button>
                    <button class="btn2-flex-item" onclick="nextStep();">打印</button>
                </div>
            </div>
        </div>
        <table class="printable">
            <thead>
                <tr>
                    <th style="width: 40px;">
                        <input type="checkbox" id="allcheck" name="colors" value="red" onclick="toggleCheckboxes(this)">
                    </th>
                    <th>出库单号</th>
                    <th>出库日期</th>
                    <th>车牌号</th>
                    <th>提单号</th>
                    <th>手机号</th>
                    <th>出库重量</th>
                    <th>出库数量</th>
                    <th>客户名称</th>
                    <th>终到站（仓库）</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/<EMAIL>"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script>
        var bill_id = localStorage.getItem("bill_id"); //提单号
        var putoutId;
        var listLenght = 1;
        var dataList = [];


        document.addEventListener('DOMContentLoaded', function() {
            getCarKeyBoard();
            getTelKeyBoard();
            getTDKeyBoard();
        });

        /* 车牌号键盘 */
        function getCarKeyBoard() {
            const keys = [
                '皖', '京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘',
                '鲁', '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋',
                '蒙', '陕', '吉', '闽', '贵', '粤', '青', '藏', '川', '宁',
                '琼', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
                'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
                'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                '退格', '清空'
            ];

            const keyboard = document.getElementById('carKeyboard');
            let activeInputField = null;

            keys.forEach(function(key) {
                const keyElement = document.createElement('div');
                keyElement.className = 'key';
                keyElement.textContent = key;
                keyboard.appendChild(keyElement);

                keyElement.addEventListener('click', function() {
                    if (!activeInputField) { return; }
                    if (key === '退格') {
                        activeInputField.value = activeInputField.value.slice(0, -1);
                        return;
                    }
                    if (key === '清空') {
                        activeInputField.value = '';
                        return;
                    }

                    activeInputField.value += key;
                    activeInputField.focus();  // 确保输入框保持焦点

                });
            });

            document.querySelectorAll('.ipt-car-number').forEach(function(ipt2) {
                ipt2.addEventListener('focus', function() {
                    activeInputField = ipt2;
                    const rect = ipt2.getBoundingClientRect();
                    keyboard.style.display = 'flex';
                    keyboard.style.top = rect.bottom + window.scrollY + 'px';
                    keyboard.style.left = rect.left + window.scrollX + 'px';
                });
            });

            // 点击其他地方隐藏小键盘
            document.addEventListener('click', function(event) {
                if (!keyboard.contains(event.target) && !event.target.classList.contains('ipt-car-number')) {
                    keyboard.style.display = 'none';
                    activeInputField = null;
                }
            });

            // 防止点击键盘时触发隐藏
            keyboard.addEventListener('click', function(event) {
                event.stopPropagation();
            });

        }

        /* 电话号码键盘 */
        function getTelKeyBoard() {
            const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                '退格', '清空'];

            const keyboard = document.getElementById('telKeyboard');
            let activeInputField = null;

            keys.forEach(function(key) {
                const keyElement = document.createElement('div');
                keyElement.className = 'key';
                keyElement.textContent = key;
                keyboard.appendChild(keyElement);

                keyElement.addEventListener('click', function() {
                    if (!activeInputField) { return; }
                    if (key === '退格') {
                        activeInputField.value = activeInputField.value.slice(0, -1);
                        return;
                    }
                    if (key === '清空') {
                        activeInputField.value = '';
                        return;
                    }

                    activeInputField.value += key;
                    activeInputField.focus();  // 确保输入框保持焦点

                });
            });

            document.querySelectorAll('.ipt-phone').forEach(function(ipt2) {
                ipt2.addEventListener('focus', function() {
                    activeInputField = ipt2;
                    const rect = ipt2.getBoundingClientRect();
                    keyboard.style.display = 'flex';
                    keyboard.style.top = rect.bottom + window.scrollY + 'px';
                    keyboard.style.left = rect.left + window.scrollX + 'px';
                });
            });

            // 点击其他地方隐藏小键盘
            document.addEventListener('click', function(event) {
                if (!keyboard.contains(event.target) && !event.target.classList.contains('ipt-phone')) {
                    keyboard.style.display = 'none';
                    activeInputField = null;
                }
            });

            // 防止点击键盘时触发隐藏
            keyboard.addEventListener('click', function(event) {
                event.stopPropagation();
            });

        }

        /* 提单键盘 */
        function getTDKeyBoard() {
            const keys = [
                'BL', 'ZK','1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                '退格', '清空'
            ];

            const keyboard = document.getElementById('keyboard');
            let activeInputField = null;

            keys.forEach(function(key) {
                const keyElement = document.createElement('div');
                keyElement.className = 'key';
                keyElement.textContent = key;
                keyboard.appendChild(keyElement);

                keyElement.addEventListener('click', function() {
                    if (!activeInputField) { return; }
                    if (key === '退格') {
                        activeInputField.value = activeInputField.value.slice(0, -1);
                        return;
                    }
                    if (key === '清空') {
                        activeInputField.value = '';
                        return;
                    }

                    activeInputField.value += key;
                    activeInputField.focus();  // 确保输入框保持焦点

                });
            });

            document.querySelectorAll('.ipt-ti-dan').forEach(function(ipt2) {
                ipt2.addEventListener('focus', function() {
                    activeInputField = ipt2;
                    const rect = ipt2.getBoundingClientRect();
                    keyboard.style.display = 'flex';
                    keyboard.style.top = rect.bottom + window.scrollY + 'px';
                    keyboard.style.left = rect.left + window.scrollX + 'px';
                });
            });

            // 点击其他地方隐藏小键盘
            document.addEventListener('click', function(event) {
                if (!keyboard.contains(event.target) && !event.target.classList.contains('ipt-ti-dan')) {
                    keyboard.style.display = 'none';
                    activeInputField = null;
                }
            });

            // 防止点击键盘时触发隐藏
            keyboard.addEventListener('click', function(event) {
                event.stopPropagation();
            });

        }


        function toggleCheckboxes(source) {
            var checkboxes = document.querySelectorAll('input[name="colorsList"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = source.checked;
            }
        }

        function updateSelectAllCheckbox() {
            var allChecked = true;
            var checkboxes = document.querySelectorAll('input[name="colorsList"]');
            for (var i = 0; i < checkboxes.length; i++) {
                if (!checkboxes[i].checked) {
                    allChecked = false;
                    break;
                }
            }
            console.log(allChecked)
            document.getElementById('allcheck').checked = allChecked;
        }

        var allChecked = true;
        //页面加载将车牌号与装卸货填充
        window.onload = function() {
            if (bill_id) {
                $("#bill_id").val(bill_id);
            }
            localStorage.removeItem("hand_big_type");
            localStorage.removeItem("loadingType");
            queryData();
        }
        // 查询信息
        function queryData(){
            const checkbox = document.getElementById("confirmCheckbox");
            const flag = checkbox.checked ? '10' : '';

            let queryData = {
                segNo:localStorage.getItem("segNo"),//账套
                idCard:localStorage.getItem("idCard"),//身份证号
                driverName:localStorage.getItem("idName"),//驾驶员姓名
                vehicleNo:$("#bill_id").val(),//车牌号
                voucherNum:$("#bill_id1").val(),//提单号
                telNum:$("#bill_id2").val(),//手机号
                serviceId: 'S_LI_RL_0059',
                flag,
            }
            showLoading('查询中');
            $.ajax({
                type: 'post',
                contentType: "application/json",
                url: ytjServerUrl,
                cache: false,
                data: JSON.stringify(queryData),
                complete :function(){},
                success: function(data){
                    closeLoading();
                    $('table tbody').html('')
                    // const dataList = data.dataList.filter(d => !!d.weight);
                    console.log(data.list);
                    if (!data.list || data.list.length == 0){
                        Swal.fire({
                            title: "未查询到出库单!",
                            icon: "question",
                            confirmButtonText: "确定",
                        });
                        return;
                    }

                    let str = ''
                    listLenght = data.list.length
                    dataList = data.list.map(r => {
                        const dateString = r.putoutDate;
                        const formattedDate = dateString.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
                        r['dateStr'] = formattedDate;
                        return r;
                    });
                    for (let i = 0; i < data.list.length; i++) {
                        str += `<tr>
                                    <td style="width: 40px;"><input type="checkbox" name="colorsList" value="${data.list[i].putoutId}"></td>
                                    <td>${data.list[i].putoutId}</td>
                                    <td>${data.list[i].dateStr}</td>
                                    <td>${data.list[i].vehicleNo}</td>
                                    <td>${data.list[i].voucherNum}</td>
                                    <td>${data.list[i].driverTel}</td>
                                    <td>${data.list[i].weight}</td>
                                    <td>${data.list[i].quantity}</td>
                                    <td>${data.list[i].customerName}</td>
                                    <td>${data.list[i].finalDestination ?? ''}</td>
                                </tr>`
                    }
                    $('table').show()
                    $('table tbody').append(str)
                    document.getElementById('allcheck').checked = false
                    document.getElementById('allcheck').addEventListener('click', function() {
                        toggleCheckboxes(this);
                    });

                    var checkboxes = document.querySelectorAll('input[name="colorsList"]');
                    checkboxes.forEach(function(checkbox) {
                        checkbox.addEventListener('click', function() {
                            updateSelectAllCheckbox();
                        });
                    });
                },
                error: () => Swal.fire({
                    title: "网络异常, 请联系管理员!",
                    icon: "error",
                    confirmButtonText: "确定",
                }),
            });
        }
        //打印
        function nextStep() {
            let checkboxes = document.querySelectorAll('input[type="checkbox"]:checked:not(#allcheck)')
            let selectedIndices = [];
            checkboxes.forEach(function (checkbox,index){
                if (checkbox.checked) {
                    selectedIndices.push(dataList.find(function(item) {
                        return item.putoutId === checkbox.value;
                    }))
                }
            })
            let data = {
                result: selectedIndices.filter(s => !!s),
                serviceId: 'S_LI_RL_0060',
            }
            if(selectedIndices.length == 0){
                Swal.fire({
                    title: "请选择需要打印的出库单!",
                    icon: "warning",
                    confirmButtonText: "确定",
                });
                return
            }
            showLoading('打印中');
            $.ajax({
                type: 'post',
                contentType: "application/json",
                url: ytjServerUrl,
                data: JSON.stringify(data),
                complete :function(){},
                success: function(data){
                    closeLoading();
                    console.log(data);
                    if (!data || !data.__sys__ || data.__sys__.status == -1) {

                        Swal.fire({
                            title: data.__sys__.msg,
                            icon: "error",
                            confirmButtonText: "确定",
                        });
                        return;
                    }

                    Swal.fire({
                        title: `正在打印，共有${data.docUrlList.length}份单据，请勿遗漏`,
                        icon: "success",
                        allowOutsideClick: false, // 点击其他区域部关闭弹框
                        showConfirmButton: false,  // 隐藏确认按钮
                        timer: 3000,  // 设置定时器，3秒后自动关闭
                        timerProgressBar: true  // 显示进度条
                    }).then(() => {
                        if (!data || !data.docUrlList) {
                            returnHome();
                            return;
                        }
                        data.docUrlList.forEach(d => {
                            window.open(d.uploadFilePath, '_blank'); // 从新标签页打开
                        });
                        returnHome();
                    });
                },
                error: () => Swal.fire({
                    title: "网络异常, 请联系管理员!",
                    icon: "error",
                    confirmButtonText: "确定",
                }),
            });
        }
    </script>
</body>

</html>