<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-25 16:20:14
       Version :  1.0
    tableName :meli.tlids0601
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     WAREHOUSE_CODE  VARCHAR   NOT NULL,
     WAREHOUSE_NAME  VARCHAR   NOT NULL,
     LOCATION_ID  VARCHAR   NOT NULL,
     LOCATION_NAME  VARCHAR   NOT NULL,
     FACTORY_AREA  VARCHAR   NOT NULL,
     FACTORY_AREA_NAME  VARCHAR   NOT NULL,
     FACTORY_BUILDING  VARCHAR   NOT NULL,
     FACTORY_BUILDING_NAME  VARCHAR   NOT NULL,
     CROSS_AREA  VARCHAR   NOT NULL,
     CROSS_AREA_NAME  VARCHAR   NOT NULL,
     POS_DIR_CODE  VARCHAR   NOT NULL,
     MANAGEMENT_STYLE  VARCHAR   NOT NULL,
     ACTION_FLAG  VARCHAR   NOT NULL,
     IF_PLAN_FLAG  VARCHAR   NOT NULL,
     LOADING_CHANNEL_NO  VARCHAR   NOT NULL,
     LOADING_CHANNEL_NAME  VARCHAR   NOT NULL,
     LOADING_POINT_NO  VARCHAR   NOT NULL,
     LOADING_POINT_NAME  VARCHAR   NOT NULL,
     X_INITIAL_POINT  VARCHAR   NOT NULL,
     X_DESTINATION  VARCHAR   NOT NULL,
     Y_INITIAL_POINT  VARCHAR   NOT NULL,
     Y_DESTINATION  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR
-->
<sqlMap namespace="LIDS0601">

    <sql id="condition">
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseName">
            WAREHOUSE_NAME LIKE '%$warehouseName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID LIKE '%$locationId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationName">
            LOCATION_NAME LIKE '%$locationName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME LIKE '%$factoryAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME = #factoryBuildingName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="posDirCode">
            POS_DIR_CODE = #posDirCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="managementStyle">
            MANAGEMENT_STYLE = #managementStyle#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="actionFlag">
            ACTION_FLAG = #actionFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ifPlanFlag">
            IF_PLAN_FLAG = #ifPlanFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingChannelNo">
            LOADING_CHANNEL_NO = #loadingChannelNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingChannelName">
            LOADING_CHANNEL_NAME = #loadingChannelName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointNo">
            LOADING_POINT_NO = #loadingPointNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointName">
            LOADING_POINT_NAME = #loadingPointName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="xInitialPoint">
            X_INITIAL_POINT = #xInitialPoint#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="xDestination">
            X_DESTINATION = #xDestination#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="yInitialPoint">
            Y_INITIAL_POINT = #yInitialPoint#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="yDestination">
            Y_DESTINATION = #yDestination#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="accurateLocationId">
            LOCATION_ID = #accurateLocationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endPoint">
            END_POINT = #endPoint#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="locationNameList">
            LOCATION_ID IN
            <iterate open="(" close=")" conjunction="," property="locationNameList">
                #locationNameList[]#
            </iterate>
        </isNotEmpty>
    </sql>
    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0601">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        MANAGEMENT_STYLE as "managementStyle",  <!-- 库位形态(10点状 20条状 21条状特殊) -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        IF_PLAN_FLAG as "ifPlanFlag",  <!-- 是否参与库位推荐(0参与 1不参与) -->
        LOADING_CHANNEL_NO as "loadingChannelNo",  <!-- 装卸通道编码 -->
        LOADING_CHANNEL_NAME as "loadingChannelName",  <!-- 装卸通道名称 -->
        LOADING_POINT_NO as "loadingPointNo",  <!-- 装卸点编码 -->
        LOADING_POINT_NAME as "loadingPointName",  <!-- 装卸点名称 -->
        X_INITIAL_POINT as "xInitialPoint",  <!-- X轴起始点 -->
        X_DESTINATION as "xDestination",  <!-- X轴终到点 -->
        Y_INITIAL_POINT as "yInitialPoint",  <!-- Y轴起始点 -->
        Y_DESTINATION as "yDestination",  <!-- Y轴终到点 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        C_NO as "c_no", <!-- 所在跨区序列号 -->
        END_POINT AS "endPoint"
        FROM meli.tlids0601 t WHERE 1=1
        AND SEG_NO = #segNo#
        <include refid="condition"/>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="10">
            TRIM(IFNULL(X_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(X_DESTINATION,'')) &lt;> ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(Y_DESTINATION,'')) &lt;> ''
        </isEqual>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="20">
            TRIM(IFNULL(X_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(X_DESTINATION,'')) = ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(Y_DESTINATION,''))= ''
        </isEqual>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryInPDA" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0601">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        MANAGEMENT_STYLE as "managementStyle",  <!-- 库位形态(10点状 20条状 21条状特殊) -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        IF_PLAN_FLAG as "ifPlanFlag",  <!-- 是否参与库位推荐(0参与 1不参与) -->
        LOADING_CHANNEL_NO as "loadingChannelNo",  <!-- 装卸通道编码 -->
        LOADING_CHANNEL_NAME as "loadingChannelName",  <!-- 装卸通道名称 -->
        LOADING_POINT_NO as "loadingPointNo",  <!-- 装卸点编码 -->
        LOADING_POINT_NAME as "loadingPointName",  <!-- 装卸点名称 -->
        X_INITIAL_POINT as "xInitialPoint",  <!-- X轴起始点 -->
        X_DESTINATION as "xDestination",  <!-- X轴终到点 -->
        Y_INITIAL_POINT as "yInitialPoint",  <!-- Y轴起始点 -->
        Y_DESTINATION as "yDestination",  <!-- Y轴终到点 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        C_NO as "c_no", <!-- 所在跨区序列号 -->
        END_POINT AS "endPoint"
        FROM meli.tlids0601 t WHERE 1=1
        AND SEG_NO = #segNo#
        <include refid="condition"/>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="10">
            TRIM(IFNULL(X_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(X_DESTINATION,'')) &lt;> ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(Y_DESTINATION,'')) &lt;> ''
        </isEqual>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="20">
            TRIM(IFNULL(X_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(X_DESTINATION,'')) = ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(Y_DESTINATION,''))= ''
        </isEqual>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                LOCATION_ID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryWarehouse" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0601">
        select
        SEG_NO as "segNo",
        UNIT_CODE as "unitCode",
        (
        select
        tt.SEG_NAME
        from
        ${platSchema}.TVZBM81 tt
        where
        tt.SEG_NO = t.SEG_NO
        and tt.DEL_FLAG = 0) as "segName",
        WAREHOUSE_CODE as "warehouseCode",
        WAREHOUSE_NAME as "warehouseName",
        FACTORY_AREA as "factoryArea",
        FACTORY_AREA_NAME as "factoryAreaName",
        FACTORY_BUILDING as "factoryBuilding",
        FACTORY_BUILDING_NAME as "factoryBuildingName"
        from
        ${meliSchema}.tlids0601 t
        where
        1 = 1
        <include refid="condition"/>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="10">
            TRIM(IFNULL(X_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(X_DESTINATION,'')) &lt;> ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(Y_DESTINATION,'')) &lt;> ''
        </isEqual>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="20">
            TRIM(IFNULL(X_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(X_DESTINATION,'')) = ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(Y_DESTINATION,''))= ''
        </isEqual>
        group by WAREHOUSE_CODE,UNIT_CODE,WAREHOUSE_NAME,FACTORY_AREA,FACTORY_AREA_NAME,FACTORY_BUILDING,FACTORY_BUILDING_NAME
    </select>


    <!-- 查询库位信息，并返回Map-->
    <select id="queryToMap" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        MANAGEMENT_STYLE as "managementStyle",  <!-- 库位形态(10点状 20条状 21条状特殊) -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        IF_PLAN_FLAG as "ifPlanFlag",  <!-- 是否参与库位推荐(0参与 1不参与) -->
        LOADING_CHANNEL_NO as "loadingChannelNo",  <!-- 装卸通道编码 -->
        LOADING_CHANNEL_NAME as "loadingChannelName",  <!-- 装卸通道名称 -->
        LOADING_POINT_NO as "loadingPointNo",  <!-- 装卸点编码 -->
        LOADING_POINT_NAME as "loadingPointName",  <!-- 装卸点名称 -->
        X_INITIAL_POINT as "xInitialPoint",  <!-- X轴起始点 -->
        X_DESTINATION as "xDestination",  <!-- X轴终到点 -->
        Y_INITIAL_POINT as "yInitialPoint",  <!-- Y轴起始点 -->
        Y_DESTINATION as "yDestination",  <!-- Y轴终到点 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        C_NO as "c_no" <!-- 所在跨区序列号 -->
        FROM meli.tlids0601 t WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0601 WHERE 1=1
        AND SEG_NO = #segNo#
        <include refid="condition"/>
    </select>

    <select id="countExists" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0601 WHERE 1=1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseName">
            WAREHOUSE_NAME = #warehouseName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID LIKE #locationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationName">
            LOCATION_NAME LIKE #locationName#
        </isNotEmpty>
    </select>

    <!--根据UUID用来校验数据状态-->
    <select id="count_uuid" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0601 WHERE 1=1
        <include refid="condition"/>
        AND UUID = #uuid#
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseName">
            WAREHOUSE_NAME = #warehouseName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationName">
            LOCATION_NAME = #locationName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME = #factoryAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME = #factoryBuildingName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME = #crossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="posDirCode">
            POS_DIR_CODE = #posDirCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="managementStyle">
            MANAGEMENT_STYLE = #managementStyle#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="actionFlag">
            ACTION_FLAG = #actionFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ifPlanFlag">
            IF_PLAN_FLAG = #ifPlanFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingChannelNo">
            LOADING_CHANNEL_NO = #loadingChannelNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingChannelName">
            LOADING_CHANNEL_NAME = #loadingChannelName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointNo">
            LOADING_POINT_NO = #loadingPointNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointName">
            LOADING_POINT_NAME = #loadingPointName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="xInitialPoint">
            X_INITIAL_POINT = #xInitialPoint#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="xDestination">
            X_DESTINATION = #xDestination#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="yInitialPoint">
            Y_INITIAL_POINT = #yInitialPoint#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="yDestination">
            Y_DESTINATION = #yDestination#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids0601 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        WAREHOUSE_CODE,  <!-- 仓库代码 -->
        WAREHOUSE_NAME,  <!-- 仓库名称 -->
        LOCATION_ID,  <!-- 库位代码 -->
        LOCATION_NAME,  <!-- 库位名称 -->
        FACTORY_AREA,  <!-- 厂区代码 -->
        FACTORY_AREA_NAME,  <!-- 厂区名称 -->
        FACTORY_BUILDING,  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME,  <!-- 厂房名称 -->
        CROSS_AREA,  <!-- 跨区编码 -->
        CROSS_AREA_NAME,  <!-- 跨区名称 -->
        POS_DIR_CODE,  <!-- 层数标记 -->
        MANAGEMENT_STYLE,  <!-- 管理方式(10点状 20条状) -->
        ACTION_FLAG,  <!-- 板卷标记(0板 1卷) -->
        IF_PLAN_FLAG,  <!-- 是否参与库位推荐(0参与 1不参与) -->
        LOADING_CHANNEL_NO,  <!-- 装卸通道编码 -->
        LOADING_CHANNEL_NAME,  <!-- 装卸通道名称 -->
        LOADING_POINT_NO,  <!-- 装卸点编码 -->
        LOADING_POINT_NAME,  <!-- 装卸点名称 -->
        X_INITIAL_POINT,  <!-- X轴起始点 -->
        X_DESTINATION,  <!-- X轴终到点 -->
        Y_INITIAL_POINT,  <!-- Y轴起始点 -->
        Y_DESTINATION,  <!-- Y轴终到点 -->
        STATUS,  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        SPEC_UPPER,  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER,  <!-- 宽度下限 单位(mm) -->
        POINT_LOWER_LENGTH,  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH,  <!-- 点状库位长度上限 -->
        STAND_FLAG,  <!-- 是否立式库位。0：卧式、1：立式 -->
        C_NO,  <!-- 所在跨区序列号 -->
        END_POINT
        )
        VALUES (#segNo#, #unitCode#, #warehouseCode#, #warehouseName#, #locationId#, #locationName#, #factoryArea#,
        #factoryAreaName#, #factoryBuilding#, #factoryBuildingName#, #crossArea#, #crossAreaName#, #posDirCode#,
        #managementStyle#, #actionFlag#, #ifPlanFlag#, #loadingChannelNo#, #loadingChannelName#, #loadingPointNo#,
        #loadingPointName#, #xInitialPoint#, #xDestination#, #yInitialPoint#, #yDestination#, #status#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
        #tenantUser#, #delFlag#, #uuid#, #specUpper#, #specLower#, #pointLowerLength#, #pointUpperLength#, #standFlag#,
        #c_no#,#endPoint#)

    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids0601 WHERE
    </delete>

    <update id="update">
        UPDATE meli.tlids0601
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        WAREHOUSE_CODE = #warehouseCode#,   <!-- 仓库代码 -->
        WAREHOUSE_NAME = #warehouseName#,   <!-- 仓库名称 -->
        LOCATION_ID = #locationId#,   <!-- 库位代码 -->
        LOCATION_NAME = #locationName#,   <!-- 库位名称 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区代码 -->
        FACTORY_AREA_NAME = #factoryAreaName#,   <!-- 厂区名称 -->
        FACTORY_BUILDING = #factoryBuilding#,   <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME = #factoryBuildingName#,   <!-- 厂房名称 -->
        CROSS_AREA = #crossArea#,   <!-- 跨区编码 -->
        CROSS_AREA_NAME = #crossAreaName#,   <!-- 跨区名称 -->
        POS_DIR_CODE = #posDirCode#,   <!-- 层数标记 -->
        MANAGEMENT_STYLE = #managementStyle#,   <!-- 库位形态(10点状 20条状 21条状特殊) -->
        ACTION_FLAG = #actionFlag#,   <!-- 板卷标记(0板 1卷) -->
        IF_PLAN_FLAG = #ifPlanFlag#,   <!-- 是否参与库位推荐(0参与 1不参与) -->
        LOADING_CHANNEL_NO = #loadingChannelNo#,   <!-- 装卸通道编码 -->
        LOADING_CHANNEL_NAME = #loadingChannelName#,   <!-- 装卸通道名称 -->
        LOADING_POINT_NO = #loadingPointNo#,   <!-- 装卸点编码 -->
        LOADING_POINT_NAME = #loadingPointName#,   <!-- 装卸点名称 -->
        X_INITIAL_POINT = #xInitialPoint#,   <!-- X轴起始点 -->
        X_DESTINATION = #xDestination#,   <!-- X轴终到点 -->
        Y_INITIAL_POINT = #yInitialPoint#,   <!-- Y轴起始点 -->
        Y_DESTINATION = #yDestination#,   <!-- Y轴终到点 -->
        STATUS = #status#,   <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag# ,   <!-- 删除标记 -->
        SPEC_UPPER = #specUpper#,   <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER = #specLower#,   <!-- 宽度下限 单位(mm) -->
        POINT_LOWER_LENGTH = #pointLowerLength#,   <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH = #pointUpperLength#,   <!-- 点状库位长度上限 -->
        STAND_FLAG = #standFlag#,   <!-- 是否立式库位。0：卧式、1：立式 -->
        C_NO = #c_no#,  <!-- 所在跨区序列号 -->
        END_POINT = #endPoint#  <!-- 所在跨区序列号 -->
        WHERE SEG_NO = #segNo#
        AND LOCATION_ID = #locationId#
        AND UUID= #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <select id="queryLocationId" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0601">
        SELECT WAREHOUSE_CODE as "warehouseCode",WAREHOUSE_NAME as "warehouseName",
        LOCATION_ID as "locationId", LOCATION_NAME as "locationName"
        FROM MELI.TLIDS0601 t
        WHERE t.SEG_NO = #segNo#
        AND t.FACTORY_AREA = #factoryArea#
        AND t.FACTORY_BUILDING = #factoryBuilding#
        AND t.X_INITIAL_POINT - 50 &lt;= #XPosition#
        AND t.X_DESTINATION + 50 &gt;= #XPosition#
        AND t.Y_INITIAL_POINT - 50 &lt;= #YPosition#
        AND t.Y_DESTINATION + 50 &gt;= #YPosition#
    </select>

    <!--查询传入库位是否为重庆宝钢一厂库位 注意上线时修改对应的厂区厂房代码-->
    <select id="countLocationId" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0601 WHERE 1=1
        AND SEG_NO = 'JC000000'
        AND LOCATION_ID = #locationId#
        AND FACTORY_AREA = 'CQBG'
        AND FACTORY_BUILDING = 'F1'
    </select>

    <!-- 批量查询已存在的库位 -->
    <select id="queryExistingLocations" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT SEG_NO as "segNo", WAREHOUSE_CODE as "warehouseCode", LOCATION_ID as "locationId"
        FROM meli.tlids0601
        WHERE (seg_no, warehouse_code, location_id) IN
        <isNotEmpty property="locationKeys">
            (
            <iterate property="locationKeys" conjunction=",">
                (
                #locationKeys[].segNo#, #locationKeys[].warehouseCode#, #locationKeys[].locationId#
                )
            </iterate>
            )
        </isNotEmpty>
        AND STATUS > '00'
    </select>

    <!-- 后端导出查询方法 ，查询方法变更时sql同步变更-->
    <select id="queryExport" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        POS_DIR_CODE as "posDirCode",  <!-- 层数标记 -->
        MANAGEMENT_STYLE as "managementStyle",  <!-- 库位形态(10点状 20条状 21条状特殊) -->
        ACTION_FLAG as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
        IF_PLAN_FLAG as "ifPlanFlag",  <!-- 是否参与库位推荐(0参与 1不参与) -->
        LOADING_CHANNEL_NO as "loadingChannelNo",  <!-- 装卸通道编码 -->
        LOADING_CHANNEL_NAME as "loadingChannelName",  <!-- 装卸通道名称 -->
        LOADING_POINT_NO as "loadingPointNo",  <!-- 装卸点编码 -->
        LOADING_POINT_NAME as "loadingPointName",  <!-- 装卸点名称 -->
        X_INITIAL_POINT as "xInitialPoint",  <!-- X轴起始点 -->
        X_DESTINATION as "xDestination",  <!-- X轴终到点 -->
        Y_INITIAL_POINT as "yInitialPoint",  <!-- Y轴起始点 -->
        Y_DESTINATION as "yDestination",  <!-- Y轴终到点 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid",  <!-- ID -->
        SPEC_UPPER as "specUpper",  <!-- 宽度上限 单位(mm) -->
        SPEC_LOWER as "specLower",  <!-- 宽度下限 单位(mm) -->
        POINT_LOWER_LENGTH as "pointLowerLength",  <!-- 点状库位长度下限 -->
        POINT_UPPER_LENGTH as "pointUpperLength",  <!-- 点状库位长度上限 -->
        STAND_FLAG as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
        C_NO as "c_no" <!-- 所在跨区序列号 -->
        FROM meli.tlids0601 t WHERE 1=1
        AND SEG_NO = #segNo#
        <include refid="condition"/>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="10">
            TRIM(IFNULL(X_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(X_DESTINATION,'')) &lt;> ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) &lt;> ''
            AND TRIM(IFNULL(Y_DESTINATION,'')) &lt;> ''
        </isEqual>
        <isEqual prepend="AND" property="ifMaintainPoint" compareValue="20">
            TRIM(IFNULL(X_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(X_DESTINATION,'')) = ''
            AND TRIM(IFNULL(Y_INITIAL_POINT,'')) = ''
            AND TRIM(IFNULL(Y_DESTINATION,''))= ''
        </isEqual>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <!-- 查询最小库位 -->
    <select id="queryMinLocation"
            resultClass="String">
        select LOCATION_ID as "locationId"
        from meli.tlids0601 tlids0601
        where tlids0601.SEG_NO = #segNo#
        AND LOCATION_ID LIKE CONCAT (#locationId#, '%')
        and STATUS='10'
        ORDER BY LOCATION_ID asc
        LIMIT 1
    </select>

    <!-- 查询最大库位 -->
    <select id="queryMaxLocation"
            resultClass="String">
        select LOCATION_ID as "locationId"
        from meli.tlids0601 tlids0601
        where tlids0601.SEG_NO = #segNo#
        AND LOCATION_ID LIKE CONCAT (#locationId#, '%')
        and STATUS='10'
        ORDER BY LOCATION_ID asc
        LIMIT 1
    </select>

    <!-- 查询传入的坐标是否为装卸货通道   -->
    <select id="queryThoroughfare" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        AREA_TYPE as "areaType",
        AREA_CODE as "areaCode",
        AREA_NAME as "areaName"
        FROM MELI.TLIDS0101 t
        WHERE t.SEG_NO = #segNo#
        AND t.FACTORY_AREA = #factoryArea#
        AND t.FACTORY_BUILDING = #factoryBuilding#
        AND t.AREA_TYPE = '40'
        AND t.X_INITIAL_POINT - 50 &lt;= #XPosition#
        AND t.X_DESTINATION + 50 &gt;= #XPosition#
        AND t.Y_INITIAL_POINT - 50 &lt;= #YPosition#
        AND t.Y_DESTINATION + 50 &gt;= #YPosition#
    </select>

    <select id="queryHandPointId" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0601">
        SELECT WAREHOUSE_CODE as "warehouseCode",
        WAREHOUSE_NAME as "warehouseName",
        LOCATION_ID as "locationId",
        LOCATION_NAME as "locationName"
        FROM MELI.TLIDS0601 t
        WHERE t.SEG_NO = #segNo#
        AND t.LOADING_POINT_NO = #handPointId#
        AND t.LOCATION_ID = #locationId#

    </select>


    <select id="queryLocationCq" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select CROSS_AREA as "crossArea",LOCATION_NAME as "locationName"
        from meli.tlids0601 tlids0601
        where 1 = 1
        and tlids0601.SEG_NO = #segNo#
        AND STATUS = #status#
        AND FACTORY_BUILDING = #factoryBuilding#
        AND LOCATION_MARK = '10'
    </select>

    <select id="queryLocationIdJZ" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0601">
        SELECT WAREHOUSE_CODE as "warehouseCode",WAREHOUSE_NAME as "warehouseName",
        LOCATION_ID as "locationId", LOCATION_NAME as "locationName"
        FROM MELI.TLIDS0601 t
        WHERE t.SEG_NO = #segNo#
        AND t.FACTORY_AREA = #factoryArea#
        AND t.FACTORY_BUILDING = #factoryBuilding#
        AND t.X_INITIAL_POINT  &lt;= #XPosition#
        AND t.X_DESTINATION  &gt;= #XPosition#
        AND t.Y_INITIAL_POINT  &lt;= #YPosition#
        AND t.Y_DESTINATION  &gt;= #YPosition#
    </select>

    <select id="queryLocationOccupyCQSum" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select sum((((tlids0601.Y_DESTINATION - Y_INITIAL_POINT) / 100) / 1.5) +
        ((((tlids0601.Y_DESTINATION - Y_INITIAL_POINT) / 100) / 1.5) - 1)) as "count",
        CROSS_AREA as "crossArea"
        from meli.tlids0601 tlids0601
        where 1 = 1
        and tlids0601.SEG_NO = 'JC000000'
        AND STATUS = '10'
        AND FACTORY_BUILDING = 'F1'
        AND LOCATION_MARK = '10'
        group by tlids0601.CROSS_AREA
    </select>
</sqlMap>