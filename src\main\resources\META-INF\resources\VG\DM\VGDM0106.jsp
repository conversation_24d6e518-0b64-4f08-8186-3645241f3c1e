<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true" required="true"/>
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                             ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-machineCode" cname="机组代码"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-machineName" cname="机组名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="机组信息">
        <EF:EFGrid blockId="result" autoDraw="no" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" required="true" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" enable="false" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="eArchivesNo" enable="false" cname="设备代码" width="70" align="center"/>
            <EF:EFColumn ename="equipmentName" required="true" cname="设备名称"/>
            <EF:EFColumn ename="machineCode" required="true" cname="机组代码" align="center"/>
            <EF:EFColumn ename="machineName" required="true" cname="机组名称"/>
            <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="100"/>
            <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
            <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
            <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow id="popup" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>