/**
 * Generate time : 2024-12-05 11:18:04
 * Version : 1.0
 */
package com.baosight.imom.common.li.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tlids1001
 *
 */
public class Tlids1001 extends DaoEPBase {

    private String segNo = " ";        /* 系统账套*/
    private String segName = " ";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String unpackOrderId = " ";        /* 拆包作业清单号*/
    private String packId = " ";        /* 捆包号*/
    private String unpackAreaId = " ";        /* 拆包区编号*/
    private String unpackAreaName = " ";        /* 拆包区名称*/
    private String status = " ";        /* 状态(10到达、20离开)*/
    private String arrivalTime = " ";        /* 到达时间*/
    private String departureTime = " ";        /* 离开时间*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private String archiveFlag = " ";        /* 归档标记*/
    private String tenantUser = " ";        /* 租户*/
    private Integer delFlag = Integer.valueOf(0);        /* 删除标记*/
    private String uuid = " ";        /* ID*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackOrderId");
        eiColumn.setDescName("拆包作业清单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackAreaId");
        eiColumn.setDescName("拆包区编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackAreaName");
        eiColumn.setDescName("拆包区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(10到达、20离开)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("arrivalTime");
        eiColumn.setDescName("到达时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("departureTime");
        eiColumn.setDescName("离开时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tlids1001() {
        initMetaData();
    }

    /**
     * get the segNo - 系统账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the unpackOrderId - 拆包作业清单号
     * @return the unpackOrderId
     */
    public String getUnpackOrderId() {
        return this.unpackOrderId;
    }

    /**
     * set the unpackOrderId - 拆包作业清单号
     */
    public void setUnpackOrderId(String unpackOrderId) {
        this.unpackOrderId = unpackOrderId;
    }

    /**
     * get the packId - 捆包号
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the unpackAreaId - 拆包区编号
     * @return the unpackAreaId
     */
    public String getUnpackAreaId() {
        return this.unpackAreaId;
    }

    /**
     * set the unpackAreaId - 拆包区编号
     */
    public void setUnpackAreaId(String unpackAreaId) {
        this.unpackAreaId = unpackAreaId;
    }

    /**
     * get the unpackAreaName - 拆包区名称
     * @return the unpackAreaName
     */
    public String getUnpackAreaName() {
        return this.unpackAreaName;
    }

    /**
     * set the unpackAreaName - 拆包区名称
     */
    public void setUnpackAreaName(String unpackAreaName) {
        this.unpackAreaName = unpackAreaName;
    }

    /**
     * get the status - 状态(10到达、20离开)
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态(10到达、20离开)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the arrivalTime - 到达时间
     * @return the arrivalTime
     */
    public String getArrivalTime() {
        return this.arrivalTime;
    }

    /**
     * set the arrivalTime - 到达时间
     */
    public void setArrivalTime(String arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    /**
     * get the departureTime - 离开时间
     * @return the departureTime
     */
    public String getDepartureTime() {
        return this.departureTime;
    }

    /**
     * set the departureTime - 离开时间
     */
    public void setDepartureTime(String departureTime) {
        this.departureTime = departureTime;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the tenantUser - 租户
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the uuid - ID
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setUnpackOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackOrderId")), unpackOrderId));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setUnpackAreaId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackAreaId")), unpackAreaId));
        setUnpackAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackAreaName")), unpackAreaName));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setArrivalTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("arrivalTime")), arrivalTime));
        setDepartureTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("departureTime")), departureTime));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("unpackOrderId", StringUtils.toString(unpackOrderId, eiMetadata.getMeta("unpackOrderId")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("unpackAreaId", StringUtils.toString(unpackAreaId, eiMetadata.getMeta("unpackAreaId")));
        map.put("unpackAreaName", StringUtils.toString(unpackAreaName, eiMetadata.getMeta("unpackAreaName")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("arrivalTime", StringUtils.toString(arrivalTime, eiMetadata.getMeta("arrivalTime")));
        map.put("departureTime", StringUtils.toString(departureTime, eiMetadata.getMeta("departureTime")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));

        return map;

    }
}