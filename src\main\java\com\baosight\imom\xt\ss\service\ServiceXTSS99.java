package com.baosight.imom.xt.ss.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.vg.dm.domain.VGDM0201;
import com.baosight.imom.xt.ss.domain.XTSS99;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServiceXTSS99 extends ServiceBase {

    private static Logger logger = LogManager.getLogger(ServiceXTSS99.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0201().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo){
        return super.query(inInfo, XTSS99.QUERY_NOT_EXIST_USER, new XTSS99());
    }


    /**
     * 提交信息到平台，使平台进行添加用户
     * @param inInfo
     * @return
     */
    public EiInfo submit(EiInfo inInfo) {
        //获取前台页面result block中传到后台的数据
        List<HashMap> submitList = inInfo.getBlock(EiConstant.resultBlock).getRows();
        EiInfo outInfo = new EiInfo();
        List<XTSS99> xtss99List = new ArrayList<>();
        try{
            //提交平台进行访问人员下发
            String appCode = PlatApplicationContext.getProperty("eplat.security.client.appCode");
            String appId = PlatApplicationContext.getProperty("eplat.security.client.clientId");
            //调用平台服务S_BE_AM_106 进行访问人员下发
            EiInfo eiInfo =new EiInfo();
            eiInfo.set("appCode", appCode);
            eiInfo.set("appId",appId);
            eiInfo.set("token", TokenUtils.getImomToken());

            List<String> users = new ArrayList<>();
            for(Map userList :submitList){
                RecordUtils.setCreator(userList);
                //将用户工号添加进userList
                users.add(userList.get("userId").toString());
                XTSS99 xtss99 = new XTSS99();
                xtss99.fromMap(userList);
                xtss99List.add(xtss99);
            }
            eiInfo.set("users", users);
            eiInfo.set("operator", UserSession.getUserId());
            eiInfo.set("isSynUserToApp", true);
            eiInfo.set(EiConstant.serviceId, "S_BE_AM_106");
            outInfo= EServiceManager.call(eiInfo, TokenUtils.getImomToken());

            List<XTSS99> insertListForSgeNo = new ArrayList<>();
            List<XTSS99> insertListForSubMit = new ArrayList<>();
            //返回之后进行数据补录
            for(XTSS99 xtss99 :xtss99List){
                //重复校验
                int userSubCount = dao.count(XTSS99.COUNT_USER_SUBMIT,xtss99);
                int userSegNoCount = dao.count(XTSS99.COUNT_USER_SEG_NO,xtss99);
                int userCount = dao.count(XTSS99.COUNT_USER,xtss99);
                if(userCount!=0){
                    if (userSegNoCount==0){
                        //insertListForSubMit.add(xtss99);
                        //插入用户准入提交表
                        dao.insert(XTSS99.INSERT_USER_SEG_NO_SUBMIT,xtss99);
                    }
                    //用户信息更新
                    dao.update(XTSS99.UPDATE_USER_INFO,xtss99);
                }
                if(userSubCount==0){
                    //insertListForSgeNo.add(xtss99);
                    //插入用户账套关联表
                    dao.insert(XTSS99.INSERT_USER_SEG_NO,xtss99);
                }
            }
            //dao.insertBatch(XTSS99.INSERT_USER_SEG_NO,insertListForSgeNo);
            //dao.insertBatch(XTSS99.INSERT_USER_SEG_NO_SUBMIT,insertListForSubMit);
            //dao.updateBatch(XTSS99.UPDATE_USER_INFO,insertListForSubMit);
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("提交平台报错");
            outInfo.setDetailMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }

}
