<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<script src="${ctx}/common/js/echarts.min.js"></script>

<EF:EFPage prefix="imom">
    <jsp:attribute name="header">
        <style>
            .kendo-xplat-CALCULATE,
            .kendo-xplat-FORCEEND,
            .kendo-xplat-CALWORKHOUR{
                float: left;
            }
        </style>
    </jsp:attribute>
    <jsp:body>
        <EF:EFRegion id="inqu" title="查询条件">
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
            <div class="row">
                <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                 readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                 center="true" required="true">
                </EF:EFPopupInput>
                <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                            required="true"/>
                <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                            colWidth="3"/>
                <EF:EFInput ename="inqu_status-0-partIdStr" cname="物料号" placeholder="模糊条件"
                            colWidth="3"/>
            </div>
            <div class="row">
                <EF:EFInput ename="inqu_status-0-packIdStr" cname="捆包号" placeholder="模糊条件"
                            colWidth="3"/>
                <EF:EFInput ename="inqu_status-0-processOrderIdStr" cname="工单" placeholder="模糊条件"
                            colWidth="3"/>
                <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                               endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                               startCname="创建时间(起)" endCname="创建时间(止)"
                               ratio="3:3" format="yyyy-MM-dd">
                </EF:EFDateSpan>
            </div>
            <div class="row">
                <EF:EFSelect ename="inqu_status-0-packStatus" cname="捆包状态" colWidth="3">
                    <EF:EFOption value="" label="全部"/>
                    <EF:EFOption value="0" label="上料"/>
                    <EF:EFOption value="1" label="加工"/>
                    <EF:EFOption value="2" label="完成"/>
                </EF:EFSelect>
                <EF:EFInput ename="inqu_status-0-machineCode" cname="机组代码"
                            colWidth="3"/>
            </div>
        </EF:EFRegion>
        <EF:EFRegion id="result" title="捆包作业履历">
            <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
                <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                  blockName="unitBlock" valueField="segNo" textField="segName"/>
                <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="70" align="center"/>
                <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                <EF:EFColumn ename="processOrderId" cname="工单" width="140" align="center"/>
                <EF:EFColumn ename="packId" cname="捆包号" width="140" align="center"/>
                <EF:EFComboColumn ename="packStatus" enable="false" cname="捆包状态" align="center" width="80">
                    <EF:EFOption value="0" label="上料"/>
                    <EF:EFOption value="1" label="加工"/>
                    <EF:EFOption value="2" label="完成"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="upPackTime" editType="datetime" width="140"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上料时间"/>
                <EF:EFColumn ename="startTime" editType="datetime" width="140"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="开始时间"/>
                <EF:EFColumn ename="endTime" editType="datetime" width="140"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="结束时间"/>
                <EF:EFColumn ename="netWeight" cname="净重" align="right"/>
                <EF:EFColumn ename="specsDesc" cname="规格描述" width="120"/>
                <EF:EFComboColumn ename="endType" enable="false" cname="标记" align="center" width="80">
                    <EF:EFOption value=" " label="加工中"/>
                    <EF:EFOption value="0" label="完成"/>
                    <EF:EFOption value="1" label="退料"/>
                    <EF:EFOption value="2" label="余料"/>
                    <EF:EFOption value="3" label="换刀"/>
                    <EF:EFOption value="4" label="尾包"/>
                    <EF:EFOption value="9" label="手工结束"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="unitedPackId" cname="并包号" width="120" align="center"/>
                <EF:EFColumn ename="unitedQuantity" cname="并包量" width="70" align="center"/>
                <EF:EFColumn ename="currentKnife" cname="当前刀" width="70" align="center"/>
                <EF:EFColumn ename="knifeSort" cname="排刀顺序" width="70" align="center"/>
                <EF:EFColumn ename="originalPackId" cname="原始捆包号" width="140" align="center"/>
                <EF:EFColumn ename="partId" cname="物料号" width="120" align="center"/>
                <EF:EFColumn ename="shopsign" cname="牌号" width="100" align="center"/>
                <EF:EFColumn ename="prodNameCode" cname="品名代码" width="70" align="center"/>
                <EF:EFColumn ename="prodCname" cname="品名名称" width="70" align="center"/>
                <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="70"/>
                <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="140"/>
                <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="70"/>
                <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="140"/>
                <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                             parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
            </EF:EFGrid>
        </EF:EFRegion>
        <div id="processChart" style="width: 100%;height:400px;"></div>
        <EF:EFWindow id="calResult" width="90%" height="60%">
            <EF:EFRegion id="result2" title="工序时间">
                <EF:EFInput ename="inqu2_status-0-relevanceId" cname="关联id" type="hidden"/>
                <EF:EFGrid blockId="result2" autoDraw="no" sort="all" readonly="true" queryMethod="queryResult"
                           isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="procedureCode" cname="工序代码" width="70" align="center"/>
                    <EF:EFColumn ename="procedureName" cname="工序名称" width="70"/>
                    <EF:EFColumn ename="startTime" cname="开始时间" width="150" align="center"/>
                    <EF:EFColumn ename="stopTime" cname="结束时间" width="150" align="center"/>
                    <EF:EFColumn ename="duration" cname="持续时间(秒)" width="75" align="center"/>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="70"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="70"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </EF:EFWindow>
        <EF:EFWindow id="outResult" width="90%" height="60%">
            <EF:EFRegion id="result3" title="生产实绩">
                <EF:EFInput ename="inqu3_status-0-relevanceId" cname="关联id" type="hidden"/>
                <EF:EFGrid blockId="result3" autoDraw="override" rowNo="true" sort="all" readonly="true"
                           queryMethod="queryOutResult" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="packId" cname="投料捆包" width="120" align="center"/>
                    <EF:EFColumn ename="outPackId" cname="产出捆包" width="120" align="center"/>
                    <EF:EFColumn ename="specsDesc" cname="规格描述" width="120"/>
                    <EF:EFColumn ename="partId" cname="物料号" width="120" align="center"/>
                    <EF:EFComboColumn ename="packType" cname="捆包类型" width="70" align="center">
                        <EF:EFOption value="2" label="成品"/>
                        <EF:EFOption value="4" label="余料"/>
                        <EF:EFOption value="6" label="废次材"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="quantity" cname="数量" width="75" sumType="page" align="center"/>
                    <EF:EFColumn ename="netWeight" cname="净重(kg)" width="75" sumType="page" align="center"/>
                    <EF:EFColumn ename="processHour" cname="加工工时(分)" width="120" align="center"/>
                    <EF:EFColumn ename="unitedPackId" cname="并包号" width="120" align="center"/>
                    <EF:EFColumn ename="stackName" cname="堆垛" width="120" align="center"/>
                    <EF:EFColumn ename="liftFlag" cname="吊运标记"/>
                    <EF:EFColumn ename="finishingShuntFlag" cname="精整分流标记"/>
                    <EF:EFColumn ename="recCreator" enable="false" cname="创建人" align="center" width="70"/>
                    <EF:EFColumn ename="recCreatorName" enable="false" cname="创建人姓名" align="center" width="140"/>
                    <EF:EFColumn ename="recCreateTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="70"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="140"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </EF:EFWindow>
        <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    </jsp:body>
</EF:EFPage>