/**
 * Generate time : 2024-12-27 15:00:58
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm0804
 */
public class Tvgdm0804 extends DaoEPBase {

    private String stuffReceivingStatus = " ";        /* 资材领用单状态*/
    private String deptId = " ";        /* 部门*/
    private String deptName = " ";        /* 部门名称*/
    private String faultId = " ";        /* 故障编号*/
    private String voucherNum = " ";        /* 依据凭单*/
    private String warehouseCode = " ";        /* 仓库代码*/
    private String warehouseName = " ";        /* 仓库名称*/
    private String locationId = " ";        /* 库位代码*/
    private String locationName = " ";        /* 库位名称*/
    private String purContractNum = " ";        /* 采购合同号*/
    private String purOrderNum = " ";        /* 采购合同子项号*/
    private String stuffCode = " ";        /* 资材代码*/
    private String stuffName = " ";        /* 资材名称*/
    private String specDesc = " ";        /* 规格*/
    private String stuffUsage = " ";        /* 用途*/
    private String measureId = " ";        /* 计量单位*/
    private BigDecimal usingWgt = new BigDecimal("0");        /* 申请量*/
    private String inventoryUuid = " ";        /* 库存id*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private BigDecimal unitPrice = new BigDecimal("0");        /* 单价 */

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("stuffReceivingStatus");
        eiColumn.setDescName("资材领用单状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deptId");
        eiColumn.setDescName("部门");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deptName");
        eiColumn.setDescName("部门名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultId");
        eiColumn.setDescName("故障编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purContractNum");
        eiColumn.setDescName("采购合同号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purOrderNum");
        eiColumn.setDescName("采购合同子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffCode");
        eiColumn.setDescName("资材代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffName");
        eiColumn.setDescName("资材名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffUsage");
        eiColumn.setDescName("用途");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("measureId");
        eiColumn.setDescName("计量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("usingWgt");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("申请量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inventoryUuid");
        eiColumn.setDescName("库存id");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitPrice");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("单价");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm0804() {
        initMetaData();
    }

    /**
     * get the stuffReceivingStatus - 资材领用单状态
     *
     * @return the stuffReceivingStatus
     */
    public String getStuffReceivingStatus() {
        return this.stuffReceivingStatus;
    }

    /**
     * set the stuffReceivingStatus - 资材领用单状态
     */
    public void setStuffReceivingStatus(String stuffReceivingStatus) {
        this.stuffReceivingStatus = stuffReceivingStatus;
    }

    /**
     * get the deptId - 部门
     *
     * @return the deptId
     */
    public String getDeptId() {
        return this.deptId;
    }

    /**
     * set the deptId - 部门
     */
    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    /**
     * get the deptName - 部门名称
     *
     * @return the deptName
     */
    public String getDeptName() {
        return this.deptName;
    }

    /**
     * set the deptName - 部门名称
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    /**
     * get the faultId - 故障编号
     *
     * @return the faultId
     */
    public String getFaultId() {
        return this.faultId;
    }

    /**
     * set the faultId - 故障编号
     */
    public void setFaultId(String faultId) {
        this.faultId = faultId;
    }

    /**
     * get the voucherNum - 依据凭单
     *
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 依据凭单
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the warehouseCode - 仓库代码
     *
     * @return the warehouseCode
     */
    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    /**
     * set the warehouseCode - 仓库代码
     */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    /**
     * get the warehouseName - 仓库名称
     *
     * @return the warehouseName
     */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
     * set the warehouseName - 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * get the locationId - 库位代码
     *
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locationName - 库位名称
     *
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the purContractNum - 采购合同号
     *
     * @return the purContractNum
     */
    public String getPurContractNum() {
        return this.purContractNum;
    }

    /**
     * set the purContractNum - 采购合同号
     */
    public void setPurContractNum(String purContractNum) {
        this.purContractNum = purContractNum;
    }

    /**
     * get the purOrderNum - 采购合同子项号
     *
     * @return the purOrderNum
     */
    public String getPurOrderNum() {
        return this.purOrderNum;
    }

    /**
     * set the purOrderNum - 采购合同子项号
     */
    public void setPurOrderNum(String purOrderNum) {
        this.purOrderNum = purOrderNum;
    }

    /**
     * get the stuffCode - 资材代码
     *
     * @return the stuffCode
     */
    public String getStuffCode() {
        return this.stuffCode;
    }

    /**
     * set the stuffCode - 资材代码
     */
    public void setStuffCode(String stuffCode) {
        this.stuffCode = stuffCode;
    }

    /**
     * get the stuffName - 资材名称
     *
     * @return the stuffName
     */
    public String getStuffName() {
        return this.stuffName;
    }

    /**
     * set the stuffName - 资材名称
     */
    public void setStuffName(String stuffName) {
        this.stuffName = stuffName;
    }

    /**
     * get the specDesc - 规格
     *
     * @return the specDesc
     */
    public String getSpecDesc() {
        return this.specDesc;
    }

    /**
     * set the specDesc - 规格
     */
    public void setSpecDesc(String specDesc) {
        this.specDesc = specDesc;
    }

    /**
     * get the stuffUsage - 用途
     *
     * @return the stuffUsage
     */
    public String getStuffUsage() {
        return this.stuffUsage;
    }

    /**
     * set the stuffUsage - 用途
     */
    public void setStuffUsage(String stuffUsage) {
        this.stuffUsage = stuffUsage;
    }

    /**
     * get the measureId - 计量单位
     *
     * @return the measureId
     */
    public String getMeasureId() {
        return this.measureId;
    }

    /**
     * set the measureId - 计量单位
     */
    public void setMeasureId(String measureId) {
        this.measureId = measureId;
    }

    /**
     * get the usingWgt - 申请量
     *
     * @return the usingWgt
     */
    public BigDecimal getUsingWgt() {
        return this.usingWgt;
    }

    /**
     * set the usingWgt - 申请量
     */
    public void setUsingWgt(BigDecimal usingWgt) {
        this.usingWgt = usingWgt;
    }

    /**
     * get the inventoryUuid - 库存id
     *
     * @return the inventoryUuid
     */
    public String getInventoryUuid() {
        return this.inventoryUuid;
    }

    /**
     * set the inventoryUuid - 库存id
     */
    public void setInventoryUuid(String inventoryUuid) {
        this.inventoryUuid = inventoryUuid;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the unitPrice - 单价
     *
     * @return the unitPrice
     */
    public BigDecimal getUnitPrice() {
        return this.unitPrice;
    }

    /**
     * set the unitPrice - 单价
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setStuffReceivingStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffReceivingStatus")), stuffReceivingStatus));
        setDeptId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deptId")), deptId));
        setDeptName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deptName")), deptName));
        setFaultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultId")), faultId));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
        setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setPurContractNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("purContractNum")), purContractNum));
        setPurOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("purOrderNum")), purOrderNum));
        setStuffCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffCode")), stuffCode));
        setStuffName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffName")), stuffName));
        setSpecDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specDesc")), specDesc));
        setStuffUsage(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffUsage")), stuffUsage));
        setMeasureId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("measureId")), measureId));
        setUsingWgt(NumberUtils.toBigDecimal(StringUtils.toString(map.get("usingWgt")), usingWgt));
        setUnitPrice(NumberUtils.toBigDecimal(StringUtils.toString(map.get("unitPrice")), unitPrice));
        setInventoryUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inventoryUuid")), inventoryUuid));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("stuffReceivingStatus", StringUtils.toString(stuffReceivingStatus, eiMetadata.getMeta("stuffReceivingStatus")));
        map.put("deptId", StringUtils.toString(deptId, eiMetadata.getMeta("deptId")));
        map.put("deptName", StringUtils.toString(deptName, eiMetadata.getMeta("deptName")));
        map.put("faultId", StringUtils.toString(faultId, eiMetadata.getMeta("faultId")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("warehouseCode", StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
        map.put("warehouseName", StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("purContractNum", StringUtils.toString(purContractNum, eiMetadata.getMeta("purContractNum")));
        map.put("purOrderNum", StringUtils.toString(purOrderNum, eiMetadata.getMeta("purOrderNum")));
        map.put("stuffCode", StringUtils.toString(stuffCode, eiMetadata.getMeta("stuffCode")));
        map.put("stuffName", StringUtils.toString(stuffName, eiMetadata.getMeta("stuffName")));
        map.put("specDesc", StringUtils.toString(specDesc, eiMetadata.getMeta("specDesc")));
        map.put("stuffUsage", StringUtils.toString(stuffUsage, eiMetadata.getMeta("stuffUsage")));
        map.put("measureId", StringUtils.toString(measureId, eiMetadata.getMeta("measureId")));
        map.put("usingWgt", StringUtils.toString(usingWgt, eiMetadata.getMeta("usingWgt")));
        map.put("unitPrice", StringUtils.toString(unitPrice, eiMetadata.getMeta("unitPrice")));
        map.put("inventoryUuid", StringUtils.toString(inventoryUuid, eiMetadata.getMeta("inventoryUuid")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}