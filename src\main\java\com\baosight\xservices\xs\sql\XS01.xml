<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XS01">
    <insert id="insert">
        INSERT INTO ${platSchema}.XS_USER (
        USER_ID,
        LOGIN_NAME,
        PASSWORD,
        STATUS,
        USER_NAME,
        GENDER,
        MOBILE,
        EMAIL,
        USER_TYPE,
        ACCOUNT_EXPIRE_DATE,
        PWD_EXPIRE_DATE,
        IS_LOCKED,
        SORT_INDEX,
        REC_CREATOR,
        REC_CREATE_TIME,
        REC_REVISOR,
        REC_REVISE_TIME,
        PWD_REVISE_DATE,
        PWD_REVISOR,
        ARCHIVE_FLAG,
        USER_GROUP_ENAME,SEG_NO
        ) VALUES (
        #userId#,
        #loginName#,
        #password#,
        #status#,
        #userName#,
        #gender#,
        #mobile#,
        #email#,
        #userType#,
        #accountExpireDate#,
        #pwdExpireDate#,
        #isLocked#,
        #sortIndex#,
        #recCreator#,
        #recCreateTime#,
        #recRevisor#,
        #recReviseTime#,
        #pwdReviseDate#,
        #pwdRevisor#,
        #archiveFlag#,
        #userGroupEname#,
        #segNo#
        )
    </insert>
    <select id="query" resultClass="java.util.HashMap">
        SELECT
        t1.user_id as "userId",
        t1.SEG_NO as "segNo",
        t1.IS_DATA_AUTHOR as "isDataAuthor",
        t1.login_name as "loginName",
        t1.password as "password",
        t1.status as "status",
        t1.user_name as "userName",
        t1.GENDER as "gender",
        t1.mobile as "mobile",
        t1.email as "email",
        t1.user_type as "userType",
        t1.account_expire_date as "accountExpireDate",
        t1.pwd_expire_date as "pwdExpireDate",
        t1.is_locked as "isLocked",
        t1.rec_creator as "recCreator",
        t1.rec_create_time as "recCreateTime",
        t1.rec_revisor as "recRevisor",
        t1.rec_revise_time as "recReviseTime",
        t1.pwd_revise_date as "pwdReviseDate",
        t1.pwd_revisor as "pwdRevisor",
        t1.archive_flag as "archiveFlag",
        t1.USER_GROUP_ENAME as "userGroupEname",
        t2.group_cname as "userGroupCname"
        FROM ${platSchema}.XS_USER t1
        left join ${platSchema}.XS_USER_GROUP t2 on t2.group_ename=t1.USER_GROUP_ENAME
        where t1.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="userId">
            t1.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            t1.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t1.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t1.login_name like ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userGroupEname">
            t1.USER_GROUP_ENAME like ('%$userGroupEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userType">
            t1.user_type = #userType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            t1.status = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isLocked">
            t1.is_locked = #isLocked#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>
    </select>
    <!--
        <select id="queryBy" resultClass="int">
            SELECT
            user_id as "userId",
            user_name as "userName",
            user_type as "userType",
            FROM ${platSchema}.xs_user where 1=1
            <isNotEmpty prepend=" AND " property="userId">
                user_id = #userId#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="userName">
                user_name like ('%$userName$%')
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="loginName">
                login_name like ('%$loginName$%')
            </isNotEmpty>
            <dynamic prepend="ORDER BY">
                <isNotEmpty property="orderBy">
                    $orderBy$
                </isNotEmpty>
            </dynamic>
        </select>
        <select id="countForqueryBy" resultClass="int">
            SELECT count(*) FROM ${platSchema}.xs_user where 1=1
            <isNotEmpty prepend=" AND " property="userId">
                user_id = #userId#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="userName">
                user_name like ('%$userName$%')
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="loginName">
                login_name like ('%$loginName$%')
            </isNotEmpty>
            <dynamic prepend="ORDER BY">
                <isNotEmpty property="orderBy">
                    $orderBy$
                </isNotEmpty>
            </dynamic>
        </select>-->

    <select id="count" resultClass="int">
        SELECT
        count(*)
        FROM ${platSchema}.XS_USER t1
        left join ${platSchema}.XS_USER_GROUP t2 on t2.group_ename=t1.USER_GROUP_ENAME
        where t1.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="userId">
            t1.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t1.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t1.login_name like ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userGroupEname">
            t1.USER_GROUP_ENAME like ('%$userGroupEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userType">
            t1.user_type = #userType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            t1.status = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isLocked">
            t1.is_locked = #isLocked#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>
    </select>

    <update id="update">
        UPDATE ${platSchema}.XS_USER
        <dynamic prepend="set">
            USER_GROUP_ENAME = #userGroupEname#,
            <isNotEmpty prepend="," property="loginName">
                login_name = #loginName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="password">
                PASSWORD = #password#
            </isNotEmpty>
            <isNotEmpty prepend="," property="status">
                status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="," property="userName">
                user_name = #userName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="gender">
                GENDER = #gender#
            </isNotEmpty>
            <isNotEmpty prepend="," property="mobile">
                mobile = #mobile#
            </isNotEmpty>
            <isNotEmpty prepend="," property="email">
                email = #email#
            </isNotEmpty>
            <isNotEmpty prepend="," property="userType">
                user_type = #userType#
            </isNotEmpty>
            <isNotEmpty prepend="," property="accountExpireDate">
                account_expire_date = #accountExpireDate#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pwdExpireDate">
                pwd_expire_date = #pwdExpireDate#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isLocked">
                is_locked = #isLocked#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recRevisor">
                rec_revisor = #recRevisor#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recReviseTime">
                rec_revise_time = #recReviseTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pwdReviseDate">
                pwd_revise_date = #pwdReviseDate#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pwdRevisor">
                pwd_revisor = #pwdRevisor#
            </isNotEmpty>
            <isNotEmpty prepend="," property="archiveFlag">
                archive_flag = #archiveFlag#
            </isNotEmpty>
            <isNotEmpty prepend="," property="segNo">
                SEG_NO = #segNo#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isDataAuthor">
                IS_DATA_AUTHOR = #isDataAuthor#
            </isNotEmpty>
        </dynamic>
        WHERE user_id = #userId#
    </update>

    <delete id="delete">
        DELETE FROM ${platSchema}.XS_USER
        WHERE user_id = #userId#
    </delete>


    <select id="queryUserParentGroups" resultClass="java.util.HashMap">
        SELECT
        ID AS "groupId",
        GROUP_ENAME AS "groupEname",
        GROUP_CNAME as "groupCname"
        FROM ${platSchema}.XS_USER_GROUP WHERE ID IN (
        SELECT DISTINCT PARENT_ID from ${platSchema}.XS_USER_GROUP_MEMBER where MEMBER_TYPE = 'USER' and MEMBER_ID = #userIdForParentGroups#)
    </select>

    <select id="countUserParentGroups" resultClass="int">
        SELECT count(*)
        FROM ${platSchema}.XS_USER_GROUP WHERE ID IN (
        SELECT DISTINCT PARENT_ID from ${platSchema}.XS_USER_GROUP_MEMBER where MEMBER_TYPE = 'USER' and MEMBER_ID = #userIdForParentGroups#)
    </select>

    <select id="queryAuthMemberUsers" resultClass="java.util.HashMap">
        SELECT
        userId as "userId",loginName as "loginName",userName as "userName" FROM(
        SELECT
        USER_ID AS userId,
        LOGIN_NAME AS loginName,
        USER_NAME AS userName
        FROM ${platSchema}.XS_USER
        WHERE USER_ID IN (
        SELECT DISTINCT MEMBER_ID from ${platSchema}.XS_USER_GROUP_MEMBER where MEMBER_TYPE = 'USER' and PARENT_ID = #checkUserParentId#
        )) as xsUser
        WHERE 1=1
        <isNotEmpty prepend="AND" property="sql">
            $sql$
        </isNotEmpty>

    </select>

    <select id="countAuthMemberUsers" resultClass="int">
        SELECT COUNT(*)
        FROM(
        SELECT
        USER_ID AS userId,
        LOGIN_NAME AS loginName,
        USER_NAME AS userName
        FROM ${platSchema}.XS_USER
        WHERE USER_ID IN (
        SELECT DISTINCT MEMBER_ID from ${platSchema}.XS_USER_GROUP_MEMBER where MEMBER_TYPE = 'USER' and PARENT_ID = #checkUserParentId#
        ))
        WHERE 1=1
        <isNotEmpty prepend="AND" property="sql">
            $sql$
        </isNotEmpty>
    </select>

    <insert id="insertWithEhrInfo">
        INSERT INTO ${platSchema}.XS_USER (
        USER_ID,
        LOGIN_NAME,
        PASSWORD,
        STATUS,
        USER_NAME,
        GENDER,
        MOBILE,
        EMAIL,
        USER_TYPE,
        ACCOUNT_EXPIRE_DATE,
        PWD_EXPIRE_DATE,
        IS_LOCKED,
        SORT_INDEX,
        REC_CREATOR,
        REC_CREATE_TIME,
        REC_REVISOR,
        REC_REVISE_TIME,
        PWD_REVISE_DATE,
        PWD_REVISOR,
        ARCHIVE_FLAG,
        USER_GROUP_ENAME,
        JOB_ID,
        JOB_NAME,
        EHR_ORG_ID
        ) VALUES (
        #userId#,
        #loginName#,
        #password#,
        #status#,
        #userName#,
        #gender#,
        #mobile#,
        #email#,
        #userType#,
        #accountExpireDate#,
        #pwdExpireDate#,
        #isLocked#,
        #sortIndex#,
        #recCreator#,
        #recCreateTime#,
        #recRevisor#,
        #recReviseTime#,
        #pwdReviseDate#,
        #pwdRevisor#,
        #archiveFlag#,
        #userGroupEname#,
        #jobId#,
        #jobName#,
        #ehrOrgId#
        )
    </insert>

    <select id="queryWithEhrInfo" resultClass="java.util.HashMap">
        SELECT
        t1.user_id as "userId",
        t1.login_name as "loginName",
        t1.password as "password",
        t1.status as "status",
        t1.user_name as "userName",
        t1.GENDER as "gender",
        t1.mobile as "mobile",
        t1.email as "email",
        t1.user_type as "userType",
        t1.account_expire_date as "accountExpireDate",
        t1.pwd_expire_date as "pwdExpireDate",
        t1.is_locked as "isLocked",
        t1.job_id as "jobId",
        t1.job_name as "jobName",
        t1.ehr_org_id as "ehrOrgId",
        t1.rec_creator as "recCreator",
        t1.rec_create_time as "recCreateTime",
        t1.rec_revisor as "recRevisor",
        t1.rec_revise_time as "recReviseTime",
        t1.pwd_revise_date as "pwdReviseDate",
        t1.pwd_revisor as "pwdRevisor",
        t1.archive_flag as "archiveFlag",
        t1.USER_GROUP_ENAME as "userGroupEname",
        t2.group_cname as "userGroupCname"
        FROM ${platSchema}.XS_USER t1
        left join ${platSchema}.XS_USER_GROUP t2 on t2.group_ename=t1.USER_GROUP_ENAME
        where t1.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="userId">
            t1.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t1.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t1.login_name like ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userGroupEname">
            t1.USER_GROUP_ENAME like ('%$userGroupEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userType">
            t1.user_type = #userType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            t1.status = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isLocked">
            t1.is_locked = #isLocked#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jobId">
            t1.job_id = #jobId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jobName">
            t1.job_name = #jobName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ehrOrgId">
            t1.ehr_org_id = #ehrOrgId#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="countWithEhrInfo" resultClass="int">
        SELECT
        count(*)
        FROM ${platSchema}.XS_USER t1
        left join ${platSchema}.XS_USER_GROUP t2 on t2.group_ename=t1.USER_GROUP_ENAME
        where t1.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="userId">
            t1.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t1.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t1.login_name like ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userGroupEname">
            t1.USER_GROUP_ENAME like ('%$userGroupEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userType">
            t1.user_type = #userType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            t1.status = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isLocked">
            t1.is_locked = #isLocked#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jobId">
            t1.job_id = #jobId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jobName">
            t1.job_name = #jobName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ehrOrgId">
            t1.ehr_org_id = #ehrOrgId#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>
    </select>

    <update id="updateWithEhrInfo">
        UPDATE ${platSchema}.XS_USER
        <dynamic prepend="set">
            USER_GROUP_ENAME = #userGroupEname#,
            <isNotEmpty prepend="," property="loginName">
                login_name = #loginName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="password">
                PASSWORD = #password#
            </isNotEmpty>
            <isNotEmpty prepend="," property="status">
                status = #status#
            </isNotEmpty>
            <isNotEmpty prepend="," property="userName">
                user_name = #userName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="gender">
                GENDER = #gender#
            </isNotEmpty>
            <isNotEmpty prepend="," property="mobile">
                mobile = #mobile#
            </isNotEmpty>
            <isNotEmpty prepend="," property="email">
                email = #email#
            </isNotEmpty>
            <isNotEmpty prepend="," property="userType">
                user_type = #userType#
            </isNotEmpty>
            <isNotEmpty prepend="," property="accountExpireDate">
                account_expire_date = #accountExpireDate#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pwdExpireDate">
                pwd_expire_date = #pwdExpireDate#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isLocked">
                is_locked = #isLocked#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recRevisor">
                rec_revisor = #recRevisor#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recReviseTime">
                rec_revise_time = #recReviseTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pwdReviseDate">
                pwd_revise_date = #pwdReviseDate#
            </isNotEmpty>
            <isNotEmpty prepend="," property="pwdRevisor">
                pwd_revisor = #pwdRevisor#
            </isNotEmpty>
            <isNotEmpty prepend="," property="archiveFlag">
                archive_flag = #archiveFlag#
            </isNotEmpty>
            <isNotEmpty prepend="," property="jobId">
                JOB_ID = #jobId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="jobName">
                job_name = #jobName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="ehrOrgId">
                EHR_ORG_ID = #ehrOrgId#
            </isNotEmpty>
            <isNotEmpty prepend="," property="segNo">
                SEG_NO = #segNo#
            </isNotEmpty>
            <isNotEmpty prepend="," property="isDataAuthor">
                IS_DATA_AUTHOR = #isDataAuthor#
            </isNotEmpty>
        </dynamic>
        WHERE user_id = #userId#
    </update>
</sqlMap>