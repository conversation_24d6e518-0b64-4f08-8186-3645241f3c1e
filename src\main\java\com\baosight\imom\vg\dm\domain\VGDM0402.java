package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0402;

/**
 * 点检计划子项表
 */
public class VGDM0402 extends Tvgdm0402 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0402.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0402.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0402.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0402.update";
    /**
     * 根据点检日期修改
     */
    public static final String UPDATE_BY_DAY = "VGDM0402.updateByDay";
    /**
     * 根据主项修改
     */
    public static final String UPDATE_BY_MAIN = "VGDM0402.updateByMain";
    /**
     * 修改点检性质
     */
    public static final String UPDATE_SPOT_CHECK_NATURE = "VGDM0402.updateSpotCheckNature";
    /**
     * 修改实绩信息
     */
    public static final String UPDATE_ACTUAL = "VGDM0402.updateActual";
    /**
     * 实绩上传
     */
    public static final String UPLOAD = "VGDM0402.upload";
    /**
     * 带主信息查询
     */
    public static final String QUERY_WITH_MAIN = "VGDM0402.queryWithMain";
    /**
     * 带主信息计数
     */
    public static final String COUNT_WITH_MAIN = "VGDM0402.countWithMain";
    /**
     * 大屏查询
     */
    public static final String QUERY_FOR_DP = "VGDM0402.queryForDp";
    /**
     * 大屏查询-周点检完成情况
     */
    public static final String QUERY_WEEK = "VGDM0402.queryWeek";
    /**
     * 查询今日待点检设备
     */
    public static final String QUERY_TODAY_DEVICE = "VGDM0402.queryTodayDevice";
    /**
     * 查询完成率
     */
    public static final String QUERY_DO_RATE = "VGDM0402.queryDoRate";
    public static final String QUERY_DO_RATE_FOR_B1_QUERY = "VGDM0402.queryDoRateForB1Query";
    /**
     * 查询完成率
     */
    public static final String QUERY_DO_RATE2 = "VGDM0402.queryDoRate2";
    public static final String QUERY_DO_RATE2_FOR_B1_QUERY = "VGDM0402.queryDoRate2ForB1Query";
    /**
     * 更新点检标准
     */
    public static final String UPDATE_SPOT_CHECK_STANDARD = "VGDM0402.updateSpotCheckStandard";

    @Override
    public String getCheckStatus() {
        return this.getCheckPlanSubStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }
}
