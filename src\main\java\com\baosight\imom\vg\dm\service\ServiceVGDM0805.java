package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0805;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 月度检修维修进度安排维护
 * @Date : 2025/4/24
 * @Version : 1.0
 */
public class ServiceVGDM0805 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0805.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(MesConstant.Iplat.RESULT2_BLOCK).addBlockMeta(new VGDM0805().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT6_BLOCK).addBlockMeta(new VGDM0805().eiMetadata);
        return inInfo;
    }

    /**
     * 检修项目查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return this.commonQuery(inInfo, MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 检修项目新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        return commonInsert(inInfo, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 数据校验
     *
     * @param vgdm0805 数据
     */
    private void checkData(VGDM0805 vgdm0805) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0805);
    }

    /**
     * 检修项目修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        return commonUpdate(inInfo, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 检修项目删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        return commonDelete(inInfo, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 维修项目查询
     */
    public EiInfo query6(EiInfo inInfo) {
        return this.commonQuery(inInfo, MesConstant.Iplat.INQU6_STATUS_BLOCK, MesConstant.Iplat.RESULT6_BLOCK);
    }

    /**
     * 维修项目新增
     */
    public EiInfo insert6(EiInfo inInfo) {
        return this.commonInsert(inInfo, MesConstant.Iplat.RESULT6_BLOCK);
    }

    /**
     * 维修项目修改
     */
    public EiInfo update6(EiInfo inInfo) {
        return this.commonUpdate(inInfo, MesConstant.Iplat.RESULT6_BLOCK);
    }

    /**
     * 维修项目删除
     */
    public EiInfo delete6(EiInfo inInfo) {
        return this.commonDelete(inInfo, MesConstant.Iplat.RESULT6_BLOCK);
    }

    /**
     * 通用查询
     */
    private EiInfo commonQuery(EiInfo inInfo, String queryBlock, String resultBlock) {
        String overhaulPlanId = inInfo.getCellStr(queryBlock, 0, "overhaulPlanId");
        if (StrUtil.isBlank(overhaulPlanId)) {
            inInfo.setCell(queryBlock, 0, "overhaulPlanId", UUIDUtils.getUUID());
        }
        return super.query(inInfo, VGDM0805.QUERY, null, false, new VGDM0805().eiMetadata,
                queryBlock, resultBlock, resultBlock);
    }

    /**
     * 通用新增
     */
    private EiInfo commonInsert(EiInfo inInfo, String blockId) {
        try {
            EiBlock block = inInfo.getBlock(blockId);
            VGDM0805 vgdm0805 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0805 = new VGDM0805();
                vgdm0805.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0805);
                Map insMap = vgdm0805.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            // 更新数据库
            DaoUtils.insertBatch(dao, VGDM0805.INSERT, block.getRows());
            // 重新排序
            this.reSortData(vgdm0805);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 通用修改
     */
    private EiInfo commonUpdate(EiInfo inInfo, String blockId) {
        try {
            EiBlock block = inInfo.getBlock(blockId);
            VGDM0805 vgdm0805 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0805 = new VGDM0805();
                vgdm0805.fromMap(block.getRow(i));
                this.checkData(vgdm0805);
                vgdm0805.setDelFlag("0");
                Map updMap = vgdm0805.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0805.UPDATE, block.getRows());
            // 重新排序
            this.reSortData(vgdm0805);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 通用删除
     */
    private EiInfo commonDelete(EiInfo inInfo, String blockId) {
        try {
            EiBlock block = inInfo.getBlock(blockId);
            VGDM0805 vgdm0805 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0805 = new VGDM0805();
                vgdm0805.fromMap(block.getRow(i));
                // 设置删除标记
                vgdm0805.setDelFlag("1");
                // 设置修改人
                Map delMap = vgdm0805.toMap();
                RecordUtils.setRevisor(delMap);
                // 数据返回前端
                block.getRows().set(i, delMap);
            }
            // 批量更新
            DaoUtils.updateBatch(dao, VGDM0805.UPD_FOR_DEL, block.getRows());
            // 重新排序
            this.reSortData(vgdm0805);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 重新排序
     *
     * @param vgdm0805 数据
     */
    private void reSortData(VGDM0805 vgdm0805) {
        if (vgdm0805 == null) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put("overhaulPlanId", vgdm0805.getOverhaulPlanId());
        map.put("itemType", vgdm0805.getItemType());
        map.put("segNo", vgdm0805.getSegNo());
        List<VGDM0805> list = dao.query(VGDM0805.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Map> updList = new ArrayList<>();
        int sortIndex = 1;
        for (VGDM0805 temp : list) {
            if (sortIndex != temp.getSortIndex()) {
                temp.setSortIndex(sortIndex);
                updList.add(temp.toMap());
            }
            sortIndex++;
        }
        DaoUtils.updateBatch(dao, VGDM0805.UPDATE, updList);
    }
}
