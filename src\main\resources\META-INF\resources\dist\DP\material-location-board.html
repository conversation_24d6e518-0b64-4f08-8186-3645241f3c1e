<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>重庆宝钢原料库位看板</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="css/bulletinBoard.css" rel="stylesheet" type="text/css">
    <link href="css/material-location-board.css" rel="stylesheet" type="text/css">
</head>

<body>
    <div id="bg">
        <div class="top">
            <div id="logo" class="logo-baosight"></div>
            <h1 class="header">原料库位看板</h1>
            <div class="datetime" id="datetime"></div>
            
            <!-- 添加控制按钮区域 -->
            <div class="control-buttons">
                <button id="refreshBtn" class="control-btn">🔄 刷新数据</button>
                <button id="fullscreenBtn" class="control-btn">🖥️ 全屏</button>
            </div>
        </div>
        
        <div class="board-container">
            <!-- 动态生成区域容器 -->
            <div id="areas-container" class="areas-container">
                <!-- 区域将由JavaScript动态生成 -->
            </div>
        </div>
    </div>

</body>
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/<EMAIL>"></script>
    <script type="text/javascript" src="./../js/config.js"></script>
    <script src="js/material-location-board.js"></script>
</html>