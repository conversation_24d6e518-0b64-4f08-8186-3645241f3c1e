package com.baosight.imom.vg.dm.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.java_websocket.client.WebSocketClient;


import com.alibaba.fastjson.JSONObject;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.common.websocket.ClientManager;
import com.baosight.imom.common.websocket.TagRefreshClient;
import com.baosight.imom.vg.dm.domain.VGDM9901;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;


/**
 * <AUTHOR> yzj
 * @Description : tag数据接收测试
 * @Date : 2024/12/6
 * @Version : 1.0
 */
public class ServiceVGDM99 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM99.class);
    private final ClientManager clientManager = PlatApplicationContext.getApplicationContext()
            .getBean(ClientManager.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM9901().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM9901.QUERY_WITH_TAG, null, false, null,
                null, null, null, VGDM9901.COUNT_WITH_TAG);
    }

    /**
     * 接收websocket消息
     *
     * @see com.baosight.imom.common.websocket.TagRefreshClient
     * onMessage
     */
    public EiInfo receiveWsData(EiInfo inInfo) {
        try {
            JSONObject jsonObject = (JSONObject) inInfo.get("data");
            // tag值数据
            Set<String> tagKeys = jsonObject.keySet();
            List<Map> insList = new ArrayList<>(tagKeys.size());
            for (String tagKey : tagKeys) {
                // 获取tag值
                String valueStr = jsonObject.getString(tagKey);
                // 分割tag值 格式为:毫秒数;数据质量;值;
                String[] values = valueStr.split(";");
                // 获取tagId
                String tagId = tagKey.substring(tagKey.indexOf(".") + 1);
                // 构建插入对象
                VGDM9901 vgdm9901 = new VGDM9901();
                vgdm9901.setTagId(tagId);
                vgdm9901.setTagTime(Long.parseLong(values[0]));
                vgdm9901.setTagValue(values[2]);
                // 转换为Map
                Map insMap = vgdm9901.toMap();
                RecordUtils.setCreatorSys(insMap);
                // 添加至插入列表
                insList.add(insMap);
            }
            // 批量插入
            DaoUtils.insertBatch(dao, VGDM9901.INSERT, insList);
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception e) {
            // 设置失败状态和错误消息
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            List<Map> rows = block.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                throw new PlatException("删除失败，原因[请先选择要删除的记录！]");
            }
            // 删除
            dao.deleteBatch(VGDM9901.DELETE, rows);
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 查看监听状态
     */
    public EiInfo queryClientStatus(EiInfo inInfo) {
        try {
            // 获取业务单元代码前缀
            String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("请先选择业务单元！");
            }
            String segNoPrefix = segNo.substring(0, 2);
            // 获取客户端
            WebSocketClient client = clientManager.getClient(segNoPrefix + ClientManager.ClientType.REFRESH_CLIENT);
            // 默认监听状态
            String clientStatus = ClientManager.ClientStatus.STOP_STATUS;
            // 默认监控点
            String monitorStr = "";
            if (client != null && client.isOpen()) {
                // 设置监听状态
                clientStatus = ClientManager.ClientStatus.START_STATUS;
                // 获取监控点
                monitorStr = String.join(",", ((TagRefreshClient) client).getMonitorList());
            }
            // 返回监听状态
            inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, ClientManager.ClientField.CLIENT_STATUS, clientStatus);
            // 返回监控点
            inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, "monitorStr", monitorStr);
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 添加监控
     */
    public EiInfo addMonitor(EiInfo inInfo) {
        try {
            // 获取tagIds
            List<String> tagIds = (List<String>) inInfo.get("tagIds");
            if (CollectionUtils.isEmpty(tagIds)) {
                throw new PlatException("请选中要添加监控的点位！");
            }
            // 获取业务单元代码
            String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
            String segNoPrefix = segNo.substring(0, 2);
            // 获取客户端
            WebSocketClient client = clientManager.getClient(segNoPrefix + ClientManager.ClientType.REFRESH_CLIENT);
            if (client == null || !client.isOpen()) {
                throw new PlatException(segNoPrefix + "客户端未启动！");
            }
            // 默认监控点
            String monitorStr = "";
            // 发送注册标签
            if (client instanceof TagRefreshClient) {
                // 发送注册标签
                ((TagRefreshClient) client).sendRegisterTags(tagIds);
                // 获取监控点
                monitorStr = String.join(",", ((TagRefreshClient) client).getMonitorList());
            }
            // 返回监控点
            inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, "monitorStr", monitorStr);
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 测试用空方法
     */
    public EiInfo emptyDoSome(EiInfo inInfo) {
        try {
            LOGGER.log("emptyDoSome入参" + inInfo.getAttr());
            log("emptyDoSome入参" + inInfo.getAttr());
            inInfo.set("rtnMsg", "执行成功了");
            inInfo.setMsg("返回成功消息");
        } catch (Exception e) {
            log("emptyDoSome执行报错:" + e.getMessage());
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }
}
