package com.baosight.imom.li.ds.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.*;
import com.baosight.imom.li.rl.dao.LIRL0304;
import com.baosight.imom.li.rl.dao.LIRL0503;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.imom.vi.pm.domain.VIPM0009;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.google.protobuf.StringValue;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线边物流-PDA调用接口
 */
public class ServiceLIDSInterfacesPda extends ServiceBase {

    /**
     * @serviceId S_LI_DS_0008
     * PDA行车实绩补录查询行车作业实绩记录
     * @param inInfo
     * @return
     */
    public EiInfo matchCraneOperationRecords(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //获取传入参数
            Map paramMap = inInfo.getAttr();
            //系统账套
            String segNo = StringUtils.defaultString((String) paramMap.get("segNo"), "");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            //行车编号
            String craneId = StringUtils.defaultString((String) paramMap.get("craneId"), "");
            if (StringUtils.isBlank(craneId)) {
                throw new RuntimeException("传入字段【行车编号】为空！");
            }
            //异常标记
            String abnormalFlag = StringUtils.defaultString((String) paramMap.get("abnormalFlag"), "");
            if (StringUtils.isBlank(abnormalFlag)) {
                throw new RuntimeException("传入字段【异常标记】为空！");
            }
            //起始时间
            String startTime = StringUtils.defaultString((String) paramMap.get("startTime"), "");
            //结束时间
            String endTime = StringUtils.defaultString((String) paramMap.get("endTime"), "");
            //库位代码
            String locationId = StringUtils.defaultString((String) paramMap.get("locationId"), "");
            //分页参数
            int offset = MapUtils.getIntValue(paramMap, "offset", 0);
            int limit = MapUtils.getIntValue(paramMap, "limit", 10);

            //查询行车作业实绩
            HashMap queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("craneId", craneId);
            queryMap.put("startTime", startTime);
            queryMap.put("endTime", endTime);
            queryMap.put("locationId", locationId);

            List<HashMap> lids1201List;
            //传入异常标记为1时
            if ("1".equals(abnormalFlag)) {
                //查询当前行车所有异常实绩数据
                lids1201List = dao.query(LIDS1201.GET_ALL_ABNORMAL_CRANE_PERFORMANCE, queryMap, offset, limit);
                /*//查询该行车最后一次的UWB释放信息，并且捆包号为空的信息，根据此释放信息查找作业实绩
                paramMap.put("actionType", "2");//释放
                List<LIDS1103> lids1103List = dao.query(LIDS1103.GET_LAST_RELEASE_RECORD_BY_CRANE_ID, paramMap);
                if (CollectionUtils.isEmpty(lids1103List)) {
                    throw new RuntimeException("行车编号：" + craneId + ",查询最后一次的UWB释放信息失败,未查到记录！");
                }*/

                /*//根据此释放流水号查询作业实绩,且捆包号为空
                LIDS1103 lids1103 = lids1103List.get(0);
                queryMap.put("outboundSequenceId", lids1103.getReleaseSysId());*/
            } else {
                //查询当前行车所有实绩数据
                lids1201List = dao.query(LIDS1201.GET_ALL_CRANE_PERFORMANCE, queryMap, offset, limit);
            }

            //根据起始终到坐标转换为区域信息
            for (HashMap map : lids1201List) {
                //根据抓取流水号，释放流水号，查询抓取时间，释放时间
                queryMap.clear();
                queryMap.put("segNo", segNo);
                queryMap.put("grabSysId", MapUtils.getString(map, "inboundSequenceId"));
                queryMap.put("releaseSysId", MapUtils.getString(map, "outboundSequenceId"));
                List<HashMap> sysUpTimeList = dao.query("LIDS1103.getSysUpTime", queryMap);
                if (CollectionUtils.isNotEmpty(sysUpTimeList)) {
                    //抓取时间
                    String grabSysUpTime = MapUtils.getString(sysUpTimeList.get(0), "grabSysUpTime");
                    map.put("grabSysUpTime", grabSysUpTime);
                    //释放时间
                    String releaseSysUpTime = MapUtils.getString(sysUpTimeList.get(0), "releaseSysUpTime");
                    map.put("releaseSysUpTime", releaseSysUpTime);
                }
            }

            //返回行车作业实绩集合
            outInfo.set("craneResultBody", lids1201List);

            if(CollectionUtils.isNotEmpty(lids1201List)){
                outInfo.setMsg("查询成功！");
            }else{
                outInfo.setMsg("未查到记录！");
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setMsg(ex.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return outInfo;
    }

    /**
     * 查询行车最后一条异常实绩，并返回实绩库位
     * @serviceId S_LI_DS_0012
     * @param inInfo
     * @return
     */
    public EiInfo queryLocationByCraneId(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //获取传入参数
            Map paramMap = inInfo.getAttr();
            //系统账套
            String segNo = StringUtils.defaultString((String) paramMap.get("segNo"), "");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            //行车编号
            String craneId = StringUtils.defaultString((String) paramMap.get("craneId"), "");
            if (StringUtils.isBlank(craneId)) {
                throw new RuntimeException("传入字段【行车编号】为空！");
            }
            //匹配行车最后一条异常实绩
            Map queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("craneId",craneId);
            List<LIDS1201> lids1201List = dao.query(LIDS1201.GET_LAST_ABNORMAL, queryMap);
            if (CollectionUtils.isEmpty(lids1201List)) {
                throw new RuntimeException("行车作业实绩单号：未查到异常记录！");
            }

            //根据行车实绩单号查询库存表，并返回库位
            queryMap.put("craneResultId",lids1201List.get(0).getCraneResultId());
            List<LIDS0901> lids0901List = dao.query(LIDS0901.QUERY_PACKS_BY_RESULT_ID, queryMap);
            if (CollectionUtils.isNotEmpty(lids0901List)) {
                //返回库位
                outInfo.set("locationId", lids0901List.get(0).getAreaCode());
                outInfo.set("locationName", lids0901List.get(0).getAreaName());
            } else {
                //返回库位
                outInfo.set("locationId", "");
                outInfo.set("locationName", "");
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @ServiceId S_LI_DS_0018
     * 物流精细化卸货确认
     * @param inInfo
     * @return
     */
    public EiInfo confirmUnloadingPDA(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //打印日志
            log("调用PDA卸货确认.confirmUnloadingPDA传入参数：" + inInfo.getAttr());
            System.out.println("调用PDA卸货确认.confirmUnloadingPDA传入参数：" + inInfo.getAttr());

            //获取传入参数
            Map paramMap = inInfo.getAttr();
            //系统账套
            String segNo = StringUtils.defaultString((String) paramMap.get("segNo"), "");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            //行车编号
            String craneId = StringUtils.defaultString((String) paramMap.get("craneId"), "");
            if (StringUtils.isBlank(craneId)) {
                throw new RuntimeException("传入字段【行车编号】为空！");
            }
            /*//行车作业实绩单号
            String craneResultId = StringUtils.defaultString((String) paramMap.get("craneResultId"), "");
            if (StringUtils.isBlank(craneResultId)) {
                throw new RuntimeException("传入字段【行车实绩单号】为空！");
            }*/
            /*//异常标记
            String abnormalFlag = StringUtils.defaultString((String) paramMap.get("abnormalFlag"), "");
            if (StringUtils.isBlank(abnormalFlag)) {
                throw new RuntimeException("传入字段【异常标记】为空！");
            }*/
            //捆包号
            String packId = StringUtils.defaultString((String) paramMap.get("packId"), "");
            if (StringUtils.isBlank(packId)) {
                throw new RuntimeException("传入字段【捆包号】为空！");
            }
            //标签号
            String labelId = StringUtils.defaultString((String) paramMap.get("labelId"), packId);
            //净重
            BigDecimal netWeight = NumberUtils.toBigDecimal(MapUtils.getString(paramMap, "netWeight"), BigDecimal.ZERO);
            //毛重
            BigDecimal grossWeight = NumberUtils.toBigDecimal(inInfo.getString("grossWeight"), BigDecimal.ZERO);

            //校验行车作业实绩(匹配最后一条异常实绩)
            Map queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("craneId",craneId);
            List<LIDS1201> lids1201List = dao.query(LIDS1201.GET_LAST_ABNORMAL, queryMap);
            if (CollectionUtils.isEmpty(lids1201List)) {
                throw new RuntimeException("行车作业实绩单号：未查到异常记录！");
            }

            //若作业实绩为异常，不允许做卸货确认
            LIDS1201 lids1201 = lids1201List.get(0);
            /*if ("1".equals(lids1201.getAbnormalFlag())) {
                throw new RuntimeException("作业实绩单号：" + lids1201.getCraneResultId() + "作业实绩异常，不允许做卸货确认！");
            }*/

            //设置修改人信息为system
            RecordUtils.setRevisorBeanSys(lids1201);
            //查到行车作业实绩，代表该行车作业实绩现在是没有捆包的，那再根据捆包号去查库存表是否存在，若存在，代表前面已有作业实绩操作过，把原库存记录删掉？
            queryMap.clear();
            queryMap.put("segNo", segNo);
            queryMap.put("packId", packId);
            List<LIDS0901> lids0901List = dao.query(LIDS0901.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(lids0901List)) {
                LIDS0901 lids0901 = lids0901List.get(0);
                lids0901.setStatus("00");
                lids0901.setDelFlag(1);
                //设置修改人信息为system
                RecordUtils.setRevisorBeanSys(lids0901);
                //行车实绩单号不为空再修改
                if(StringUtils.isNotBlank(lids0901.getCraneResultId())){
                    dao.update(LIDS0901.UPDATE_INVENTORY_PACK_INFO, lids0901.toMap());
                }
            }

            //根据捆包号查询行车作业清单
            queryMap.clear();
            queryMap.put("segNo", segNo);
            queryMap.put("craneId", craneId);
            queryMap.put("packId", packId);
            queryMap.put("listSource","10");//卸货入库
            List<LIDS1101> lids1101List = dao.query(LIDS1101.GET_WORK_ORDER_LIST_BY_PACK_ID, queryMap);

            //行车作业清单号
            String craneOrderId = "";
            //用来修改的map
            Map updateMap = new HashMap();
            //记录SQL处理条数
            int i;

            if (CollectionUtils.isNotEmpty(lids1101List)) {
                LIDS1101 lids1101 = lids1101List.get(0);
                //赋值行车作业清单号
                craneOrderId = lids1101.getCraneOrderId();
                //更新行车作业实绩的捆包号，行车作业清单号
                lids1201.setCraneOrderId(lids1101.getCraneOrderId());
                i = dao.update(LIDS1201.UPDATE_WORK_ORDER, lids1201);
                if (i != 1) {
                    throw new RuntimeException("更新行车作业实绩主表行车清单失败,传入参数：" + lids1201.toMap() + ",返回：" + i + "条");
                }

                //更新行车作业清单的状态为40(完成)
                updateMap.clear();
                updateMap.put("segNo", segNo);
                updateMap.put("status", "40");
                updateMap.put("craneOrderId", lids1101.getCraneOrderId());
                i = dao.update(LIDS1101.UPDATE_STATUS, updateMap);
                if (i != 1) {
                    throw new RuntimeException("更新行车作业清单状态失败,传入参数：" + updateMap + ",返回：" + i + "条");
                }
                i = dao.update(LIDS1102.UPDATE_STATUS, updateMap);
                if (i != 1) {
                    throw new RuntimeException("更新行车作业清单子表状态失败,传入参数：" + updateMap + ",返回：" + i + "条");
                }
            }

            //去除异常标记
            lids1201.setAbnormalFlag("0");
            //设置状态为40(完成)
            lids1201.setStatus("40");
            //修改异常标记
            i = dao.update(LIDS1201.UPDATE_ABNORMAL_FLAG, lids1201);
            if (i != 1) {
                throw new RuntimeException("更新行车作业实绩主表异常标记失败,传入参数：" + lids1201.toMap() + ",返回：" + i + "条");
            }
            //修改作业实绩子表
            updateMap.put("segNo", segNo);
            updateMap.put("craneResultId", lids1201.getCraneResultId());
            updateMap.put("packId", packId);
            updateMap.put("labelId", labelId);
            updateMap.put("netWeight", netWeight);
            /*updateMap.put("grossWeight", grossWeight);*/
            updateMap.put("status","40");
            //设置修改人信息为system
            RecordUtils.setRevisorSys(updateMap);
            i = dao.update(LIDS1202.UPDATE_CRANE_PERFORMANCE_SUB_TABLE, updateMap);
            if (i != 1) {
                throw new RuntimeException("更新作业实绩子表失败,传入参数：" + updateMap + ",返回：" + i + "条");
            }

            //修改库存捆包信息
            updateMap.clear();
            updateMap.put("segNo", segNo);
            updateMap.put("craneResultId", lids1201.getCraneResultId());
            updateMap.put("craneOrderId", craneOrderId);
            updateMap.put("packId", packId);
            updateMap.put("labelId", labelId);

            //根据捆包号查询装卸货配单子表，捆包号存在就是卷包赋值板卷标记
            queryMap.clear();
            queryMap.put("segNo", segNo);
            queryMap.put("packId", packId);
            queryMap.put("statusSX","1");
            List<LIRL0503> lirl0503List = dao.query(LIRL0503.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(lirl0503List)) {
                updateMap.put("actionFlag", "1");//卷

                //计算外径，并根据外径计算最大Y轴与最小Y轴坐标
                BigDecimal innerDiameter = lirl0503List.get(0).getInnerDiameter();
                BigDecimal prodDensity = lirl0503List.get(0).getProdDensity();
                BigDecimal packNetWeight = lirl0503List.get(0).getNetWeight();
                String specsDesc = lirl0503List.get(0).getSpecsDesc();
                double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), packNetWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                BigDecimal outerDiameter = BigDecimal.valueOf(externalDiameterNumber);
                updateMap.put("outerDiameter", outerDiameter);
                //以Y为中心值，传入计算最大Y,最小Y
                BigDecimal yOffset = outerDiameter.divide(new BigDecimal("20"), 2, RoundingMode.HALF_UP);
                updateMap.put("yOffset", yOffset);
            }else{
                updateMap.put("actionFlag", "0");//板
            }
            updateMap.put("netWeight", netWeight);
            /*updateMap.put("grossWeight", grossWeight);*/
            //设置修改人信息为system
            RecordUtils.setRevisorSys(updateMap);
            i = dao.update(LIDS0901.UPDATE_INVENTORY_PACK_INFO, updateMap);
            if (i != 1) {
                throw new RuntimeException("更新库存捆包信息失败,传入参数：" + updateMap + ",返回：" + i + "条");
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * @ServiceId S_LI_DS_0009
     * 行车作业实绩补录捆包
     * @param inInfo
     * @return
     */
    public EiInfo recordMissingPack(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map paramMap = inInfo.getAttr();

            //系统账套
            String segNo = StringUtils.defaultString((String) paramMap.get("segNo"), "");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            //行车作业实绩单号
            String craneResultId = StringUtils.defaultString((String) paramMap.get("craneResultId"), "");
            if (StringUtils.isBlank(craneResultId)) {
                throw new RuntimeException("传入字段【行车作业实绩单号】为空！");
            }
            //行车编号
            String craneId = StringUtils.defaultString((String) paramMap.get("craneId"), "");
            if (StringUtils.isBlank(craneId)) {
                throw new RuntimeException("传入字段【行车编号】为空！");
            }
            //捆包号
            List<String> packIdList = (List<String>) paramMap.get("packIdList");
            if (CollectionUtils.isEmpty(packIdList)) {
                throw new RuntimeException("传入字段【捆包号】为空！");
            }

            //异常标记
            String abnormalFlag = StringUtils.defaultString((String) paramMap.get("abnormalFlag"), "");
            if (StringUtils.isBlank(abnormalFlag)) {
                throw new RuntimeException("传入字段【异常标记】为空！");
            }

            //厂区代码
            String factoryArea = StringUtils.defaultString((String) paramMap.get("factoryArea"), "");
            if (StringUtils.isBlank(factoryArea)) {
                throw new RuntimeException("传入字段【厂区代码】为空！");
            }

            //厂房代码
            String factoryBuilding = StringUtils.defaultString((String) paramMap.get("factoryBuilding"), "");
            if (StringUtils.isBlank(factoryBuilding)) {
                throw new RuntimeException("传入字段【厂房代码】为空！");
            }

            //净重
            BigDecimal netWeight = new BigDecimal("0");
            //毛重
            BigDecimal grossWeight =new BigDecimal("0");

            //查询行车实绩主表
            List<LIDS1201> lids1201List = dao.query(LIDS1201.QUERY, paramMap);
            if (CollectionUtils.isEmpty(lids1201List)){
                throw new RuntimeException("查询不到对应的行车作业实绩信息！");
            }
            LIDS1201 lids1201 = lids1201List.get(0);
            //根据行车实绩主表的坐标，转换为区域
            Map queryMap = new HashMap();
            queryMap.put("factoryArea", factoryArea);
            queryMap.put("factoryBuilding", factoryBuilding);
            queryMap.put("XPosition", lids1201.getEndxPosition());
            queryMap.put("YPosition", lids1201.getEndyPosition());
            queryMap.put("ZPosition", lids1201.getEndzPosition());

            //根据厂区厂房，XY轴坐标查询所属区域
            Map areaMap = UWBUtils.transitionArea(queryMap, dao);
            //区域类型
            String areaType = StringUtils.defaultString((String) areaMap.get("areaType"));
            //区域代码
            String areaCode = StringUtils.defaultString((String) areaMap.get("areaCode"));
            //区域名称
            String areaName = StringUtils.defaultString((String) areaMap.get("areaName"));
            //传入异常标记为1,本次操作(更新行车作业实绩，更新行车)
            if ("1".equals(abnormalFlag)) {
                packIdList = packIdList.stream()
                        .map(id -> "'" + id + "'")
                        .collect(Collectors.toList());
                //取出捆包号条件
                String packIds = StringUtils.join(packIdList, ",");
                queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("craneResultId", craneResultId);
                queryMap.put("packIds", packIds);

                //根据行车作业实绩单号，传入捆包号集合查询行车作业实绩信息，确认已有的捆包
                List<HashMap> craneResultList = dao.query(LIDS1202.QUERY_CRANE_PERFORMANCE_IN_PACK, queryMap);

                //判断捆包号在查询结果集中是否存在，如果不存在，补录一条新的行车作业实绩子项，并将捆包位置调整为与已有捆包相同
                List<String> existingPackIds = craneResultList.stream()
                        .map(map -> MapUtils.getString(map, "packId"))
                        .collect(Collectors.toList());

                List<LIDS1202> insertLids1202 = new ArrayList<>();
                for (String packId : packIdList) {
                    //去掉单引号
                    String packIdTemp = packId.replace("'", "");
                    //查询卸货配单子表的捆包重量
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", packIdTemp);
                    queryMap.put("statusSX", "1");
                    List<LIRL0503> lirl0503List = dao.query(LIRL0503.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(lirl0503List)) {
                        netWeight = lirl0503List.get(0).getNetWeight();
                    }
                    if (!existingPackIds.contains(packIdTemp)) {
                        Map templateMap = new HashMap();
                        RecordUtils.setCreatorSys(templateMap);
                        // 新增行车作业实绩子项
                        LIDS1202 addLids1202 = new LIDS1202();
                        addLids1202.fromMap(templateMap);
                        addLids1202.setUnitCode(segNo);
                        addLids1202.setSegNo(segNo);
                        addLids1202.setCraneResultId(craneResultId);
                        //行车实绩子项号
                        addLids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{craneResultId, ""}));
                        //新增捆包号
                        addLids1202.setPackId(packIdTemp);
                        //捆包净重
                        addLids1202.setNetWeight(netWeight);
                        //状态
                        addLids1202.setStatus("40");
                        //UUID
                        addLids1202.setUuid(UUIDUtils.getUUID());
                        insertLids1202.add(addLids1202);
                    }
                }

                //实绩子项不存在捆包，新增
                if (CollectionUtils.isNotEmpty(insertLids1202)) {
                    dao.insertBatch(LIDS1202.INSERT, insertLids1202);
                }
                //若原本已存在实绩子项，修改
                Map updateMap = new HashMap();
                updateMap.put("segNo", segNo);
                updateMap.put("craneResultId", craneResultId);
                updateMap.put("status", "40");
                int i;
                if (CollectionUtils.isNotEmpty(craneResultList)) {
                    //设置修改人信息为system
                    RecordUtils.setRevisorSys(updateMap);
                    i = dao.update(LIDS1202.UPDATE_CRANE_PERFORMANCE_SUB_TABLE, updateMap);
                    if (i != 1) {
                        throw new RuntimeException("更新作业实绩子表失败,传入参数：" + updateMap + ",返回：" + i + "条");
                    }
                }

                //更新实绩主表异常标记
                updateMap.put("abnormalFlag", "0");
                i = dao.update(LIDS1201.UPDATE_ABNORMAL_FLAG, updateMap);
                if (i != 1) {
                    throw new RuntimeException("更新作业实绩主表异常标记失败,传入参数：" + updateMap + ",返回：" + i + "条");
                }

                //根据捆包号查询行车作业清单
                queryMap = new HashMap();
                queryMap.clear();
                queryMap.put("segNo", segNo);
                queryMap.put("craneId", craneId);
                queryMap.put("packIds", packIds);
                List<LIDS1102> lids1102List = dao.query(LIDS1102.QUERY_DISTINCT_CRANE_ORDER_ID, queryMap);
                if (CollectionUtils.isNotEmpty(lids1102List)) {
                    if (lids1102List.size() > 1) {
                        throw new RuntimeException("匹配到多个行车作业清单");
                    }
                    LIDS1102 lids1102 = lids1102List.get(0);
                    //更新实绩行车作业清单号
                    updateMap.put("craneOrderId", lids1102.getCraneOrderId());
                    i = dao.update(LIDS1201.UPDATE_WORK_ORDER, updateMap);
                    if (i != 1) {
                        throw new RuntimeException("更新作业实绩主表行车作业清单号失败,传入参数：" + updateMap + ",返回：" + i + "条");
                    }
                    //更新行车作业清单的状态为40(完成)
                    updateMap.clear();
                    updateMap.put("segNo", segNo);
                    updateMap.put("status", "40");
                    updateMap.put("craneOrderId", lids1102.getCraneOrderId());
                    i = dao.update(LIDS1101.UPDATE_STATUS, updateMap);
                    if (i != 1) {
                        throw new RuntimeException("更新行车作业清单状态失败,传入参数：" + updateMap + ",返回：" + i + "条");
                    }
                    i = dao.update(LIDS1102.UPDATE_STATUS, updateMap);
                    if (i != 1) {
                        throw new RuntimeException("更新行车作业清单子表状态失败,传入参数：" + updateMap + ",返回：" + i + "条");
                    }
                }

                //修改库存捆包信息
                List<Map> updatePackList = new ArrayList<>();
                for (String packId : packIdList) {
                    //去掉单引号
                    String packIdTemp = packId.replace("'", "");
                    updateMap = new HashMap();
                    //修改库存捆包信息
                    updateMap.put("segNo", segNo);
                    updateMap.put("craneResultId", craneResultId);
                    updateMap.put("packId", packIdTemp);
                    updateMap.put("labelId", packIdTemp);
                    updateMap.put("areaType", areaType);
                    updateMap.put("areaCode", areaCode);
                    updateMap.put("areaName", areaName);
                    updateMap.put("x_position", lids1201.getEndxPosition());
                    updateMap.put("y_position", lids1201.getEndyPosition());
                    updateMap.put("z_position", lids1201.getEndzPosition());
                    //根据捆包号查询装卸货配单子表，捆包号存在就是卷包赋值板卷标记
                    queryMap = new HashMap();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", packIdTemp);
                    queryMap.put("statusSX", "1");
                    List<LIRL0503> lirl0503List = dao.query(LIRL0503.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(lirl0503List)) {
                        updateMap.put("actionFlag", "1");//卷

                        //计算外径，并根据外径计算最大Y轴与最小Y轴坐标
                        BigDecimal innerDiameter = lirl0503List.get(0).getInnerDiameter();
                        BigDecimal prodDensity = lirl0503List.get(0).getProdDensity();
                        BigDecimal packNetWeight = lirl0503List.get(0).getNetWeight();
                        String specsDesc = lirl0503List.get(0).getSpecsDesc();
                        double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                        double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), packNetWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                        BigDecimal outerDiameter = BigDecimal.valueOf(externalDiameterNumber);
                        updateMap.put("outerDiameter", outerDiameter);
                        //以Y为中心值，传入计算最大Y,最小Y
                        BigDecimal yOffset = outerDiameter.divide(new BigDecimal("20"), 2, RoundingMode.HALF_UP);
                        updateMap.put("yOffset", yOffset);
                    } else {
                        updateMap.put("actionFlag", "0");//板
                    }
                    //设置修改人信息为system
                    RecordUtils.setRevisorSys(updateMap);
                    updatePackList.add(updateMap);
                }

                //更新库存捆包信息
                i = dao.updateBatch(LIDS0901.UPDATE_INVENTORY_PACK_INFO, updatePackList);
                if (i != 1) {
                    throw new RuntimeException("更新库存捆包信息失败,传入参数：" + updatePackList + ",传入" + updatePackList.size() + "条,返回：" + i + "条");
                }
            } else {
                //否则，本次操作传入多个捆包，确认已有捆包时，再将其它捆包修改为与已存在捆包相同的XY位置
                packIdList = packIdList.stream()
                        .map(id -> "'" + id + "'")
                        .collect(Collectors.toList());
                //取出捆包号条件
                String packIds = StringUtils.join(packIdList, ",");
                queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("craneResultId", craneResultId);
                queryMap.put("packIds", packIds);

                //根据行车作业实绩单号，传入捆包号集合查询行车作业实绩信息，确认已有的捆包
                List<HashMap> craneResultList = dao.query(LIDS1202.QUERY_CRANE_PERFORMANCE_IN_PACK, queryMap);
                if (CollectionUtils.isEmpty(craneResultList)) {
                    throw new RuntimeException("未查询到行车作业实绩信息！");
                }


                List<LIDS1202> insertLids1202 = new ArrayList<>();
                //判断捆包号在查询结果集中是否存在，如果不存在，补录一条新的行车作业实绩子项，并将捆包位置调整为与已有捆包相同
                List<String> existingPackIds = craneResultList.stream()
                        .map(map -> MapUtils.getString(map, "packId"))
                        .collect(Collectors.toList());

                for (String packId : packIdList) {
                    //去掉单引号
                    String packIdTemp = packId.replace("'", "");
                    //TODO 查询卸货配单子表的捆包重量
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", packIdTemp);
                    queryMap.put("statusSX", "1");
                    List<LIRL0503> lirl0503List = dao.query(LIRL0503.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(lirl0503List)) {
                        netWeight = lirl0503List.get(0).getNetWeight();
                    }
                    if (!existingPackIds.contains(packIdTemp)) {
                        // 使用craneResultList 中的第一个记录作为模板
                        HashMap templateMap = craneResultList.get(0);
                        // 设置创建人信息
                        RecordUtils.setCreator(templateMap);
                        // 新增行车作业实绩子项
                        LIDS1202 addLids1202 = new LIDS1202();
                        addLids1202.fromMap(templateMap);
                        //行车实绩子项号
                        addLids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{craneResultId, ""}));
                        //新增捆包号
                        addLids1202.setPackId(packIdTemp);
                        //捆包净重
                        addLids1202.setNetWeight(netWeight);
                        //UUID
                        addLids1202.setUuid(UUIDUtils.getUUID());
                        insertLids1202.add(addLids1202);
                    }
                }

                if (CollectionUtils.isNotEmpty(insertLids1202)) {
                    dao.insertBatch(LIDS1202.INSERT, insertLids1202);

                    //计算外径与Y轴上下偏移值
                    BigDecimal outerDiameter = new BigDecimal("0");
                    BigDecimal yOffset = new BigDecimal("0");
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", MapUtils.getString(craneResultList.get(0), "packId"));
                    queryMap.put("statusSX", "1");
                    List<LIRL0503> lirl0503List = dao.query(LIRL0503.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(lirl0503List)) {
                        //计算外径，并根据外径计算最大Y轴与最小Y轴坐标
                        BigDecimal innerDiameter = lirl0503List.get(0).getInnerDiameter();
                        BigDecimal prodDensity = lirl0503List.get(0).getProdDensity();
                        BigDecimal packNetWeight = lirl0503List.get(0).getNetWeight();
                        String specsDesc = lirl0503List.get(0).getSpecsDesc();
                        double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1, specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
                        double externalDiameterNumber = ServiceLIDS0606.calculateOuterDiameter(innerDiameter.doubleValue(), packNetWeight.doubleValue(), width, prodDensity.doubleValue() * 1000);
                        outerDiameter = BigDecimal.valueOf(externalDiameterNumber);
                        yOffset = outerDiameter.divide(new BigDecimal("20"), 2, RoundingMode.HALF_UP);
                    }
                    //将捆包库存位置修改为与已有捆包一致
                    Map updateMap = new HashMap();
                    updateMap.put("segNo", segNo);
                    updateMap.put("craneResultId", craneResultId);
                    updateMap.put("packId", MapUtils.getString(craneResultList.get(0), "packId"));//已有捆包号
                    updateMap.put("packIds", packIds);//未有捆包号
                    if(outerDiameter.compareTo(BigDecimal.ZERO) > 0){
                        updateMap.put("outerDiameter", outerDiameter);
                        updateMap.put("yOffset", yOffset);
                    }
                    dao.update(LIDS0901.UPDATE_PACK_INFO_IS_INVENTORY_POSITION, updateMap);
                }
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 物流精细化，PDA计划入库补录IMOM库存表数据
     */
    public EiInfo generateInventoryData(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String switchValue = new SwitchUtils().getProcessSwitchValue(inInfo.get("segNo").toString(), "IF_INBOUND_RECONCILIATION", dao);
            if ("1".equals(switchValue)) {
                //传入参数
                Map packInventory = inInfo.getAttr();
                //入库明细
                List<Map> packInventoryList = (List<Map>) packInventory.get("rowList");
                //反转列表
                Collections.reverse(packInventoryList);
                EiInfo sendInfo = new EiInfo();
                for (Map packInventoryMap : packInventoryList) {
                    String segNo = MapUtils.getString(packInventoryMap, "segNo");
                    String packId = MapUtils.getString(packInventoryMap, "packId");
                    String craneId = inInfo.getString("craneId");
                    String craneResultId = MapUtils.getString(packInventoryMap, "craneResultId");
                    BigDecimal netWeight = new BigDecimal(MapUtils.getString(packInventoryMap, "netWeight"));
                    //补录
                    Map supplementMap = new HashMap();
                    supplementMap.put("segNo", segNo);
                    supplementMap.put("packId", packId);
                    supplementMap.put("craneId", craneId);
                    supplementMap.put("craneResultId", craneResultId);
                    supplementMap.put("netWeight", netWeight);

                    sendInfo.setAttr(supplementMap);
                    sendInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
                    sendInfo.set(EiConstant.methodName, "confirmUnloadingPDA");
                    outInfo = XLocalManager.call(sendInfo);
                    if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        throw new RuntimeException("生成IMOM库存表数据失败！");
                    }
                }
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("处理成功！");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    /**
     * PDA查询行车作业清单
     */
    public EiInfo PDAQueryCraneOrderId(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            Map attr = inInfo.getAttr();
            //系统账套
            String segNo = StringUtils.defaultString((String) attr.get("segNo"), "");
            if(StringUtils.isBlank(segNo)){
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            int offset = MapUtils.getInteger(attr, "offset", 0);
            int limit = MapUtils.getInteger(attr, "limit", 10);
            int num = super.count("LIDS1101.PDAQueryCraneOrderIdCount",attr);//查询总条数
            List<Map> craneOrderList = this.dao.query("LIDS1101.PDAQueryCraneOrderId",attr,offset,limit);
            outInfo.set("craneOrderList",craneOrderList);
            Map<String, Object> pageQ = new HashMap<>();//分页信息
            pageQ.put("total", num);//总数
            pageQ.put("offset", offset);//当前页
            pageQ.put("limit", limit);//每页记录数
            outInfo.set("pageQ", pageQ);//返回分页数据
            outInfo.setMsg("查询成功");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }


    /**
     * @serviceId S_LI_DS_0013
     * PDA查询厂区区域/库位名称
     */
    public EiInfo queryFactoryArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String type = inInfo.getString("type");
            if (StringUtils.isBlank(type)) {
                throw new RuntimeException("传入字段【类型】为空！");
            }
            //传入区域/库位名称
            String searchQuery = inInfo.getString("searchQuery");

            List<HashMap> returnList = new ArrayList<>();
            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            //查询区域
            if ("0".equals(type)) {
                queryMap.put("areaName", searchQuery);
                queryMap.put("status", "30");
                returnList = dao.query(LIDS0101.QUERY_TO_MAP, queryMap);
            } else if ("1".equals(type)) {//查询库位
                queryMap.put("locationName", searchQuery);
                queryMap.put("status", "10");
                returnList = dao.query(LIDS0601.QUERY_TO_MAP, queryMap);
            }

            if (CollectionUtils.isEmpty(returnList)) {
                outInfo.setMsg("未查到记录");
            } else {
                outInfo.setMsg("查询成功");
            }
            outInfo.set("returnList", returnList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @serviceId S_LI_DS_0014
     * PDA定位区域与库位坐标起始
     */
    public EiInfo areaAndLocationConfirmStartPosPda(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String type = inInfo.getString("type");
            if (StringUtils.isBlank(type)) {
                throw new RuntimeException("传入字段【类型】为空！");
            }
            //行车编号
            String craneId = inInfo.getString("craneId");
            if (StringUtils.isBlank(craneId)) {
                throw new RuntimeException("传入字段【行车编号】为空！");
            }
            //行车名称
            String craneName = inInfo.getString("craneName");
            if (StringUtils.isBlank(craneName)) {
                throw new RuntimeException("传入字段【行车名称】为空！");
            }

            //区域或库位
            List<HashMap> paramList = (List<HashMap>) inInfo.get("paramList");

            Map paramMap = new HashMap();
            paramMap.put("segNo", segNo);
            paramMap.put("craneId", craneId);
            paramMap.put("craneName", craneName);

            //依据行车编号获取卡号
            paramMap.put("cardId", MapUtils.getString(MesConstant.CARD_MAP, craneId));
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
            sendInfo.set("paramMap", paramMap);
            sendInfo = XLocalManager.call(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(sendInfo.getMsg());
            }

            Map resultMap = sendInfo.getMap("resultMap");
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询不到行车实时位置信息！");
            }
            //行车所在坐标
            String XPosition = MapUtils.getString(resultMap, "x_value", "");
            String YPosition = MapUtils.getString(resultMap, "y_value", "");

            paramList.forEach(areaMap -> {
                areaMap.put("xInitialPoint", new BigDecimal(XPosition).intValue());
                areaMap.put("yInitialPoint", new BigDecimal(YPosition).intValue());
            });
            if ("0".equals(type)) {
                //区域
                int count = dao.updateBatch(LIDS0101.UPDATE, paramList);
                if (count != paramList.size()) {
                    throw new PlatException("修改区域终点坐标失败,修改条数与传入条数不一致！");
                }
            } else {
                //库位
                int count = dao.updateBatch(LIDS0601.UPDATE, paramList);
                if (count != paramList.size()) {
                    throw new PlatException("修改库位终点坐标失败,修改条数与传入条数不一致！");
                }
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @serviceId S_LI_DS_0015
     * PDA定位区域与库位坐标终点
     */
    public EiInfo areaAndLocationConfirmEndPosPda(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String type = inInfo.getString("type");
            if (StringUtils.isBlank(type)) {
                throw new RuntimeException("传入字段【类型】为空！");
            }
            //行车编号
            String craneId = inInfo.getString("craneId");
            if (StringUtils.isBlank(craneId)) {
                throw new RuntimeException("传入字段【行车编号】为空！");
            }
            //行车名称
            String craneName = inInfo.getString("craneName");
            if (StringUtils.isBlank(craneName)) {
                throw new RuntimeException("传入字段【行车名称】为空！");
            }

            //区域或库位
            List<HashMap> paramList = (List<HashMap>) inInfo.get("paramList");

            Map paramMap = new HashMap();
            paramMap.put("segNo", segNo);
            paramMap.put("craneId", craneId);
            paramMap.put("craneName", craneName);

            //依据行车编号获取卡号
            paramMap.put("cardId", MapUtils.getString(MesConstant.CARD_MAP, craneId));
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
            sendInfo.set("paramMap", paramMap);
            sendInfo = XLocalManager.call(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(sendInfo.getMsg());
            }

            Map resultMap = sendInfo.getMap("resultMap");
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询不到行车实时位置信息！");
            }
            //行车所在坐标
            String XPosition = MapUtils.getString(resultMap, "x_value", "");
            String YPosition = MapUtils.getString(resultMap, "y_value", "");

            paramList.forEach(areaMap -> {
                areaMap.put("xDestination", new BigDecimal(XPosition).intValue());
                areaMap.put("yDestination", new BigDecimal(YPosition).intValue());
            });
            if ("0".equals(type)) {
                //区域
                int count = dao.updateBatch(LIDS0101.UPDATE, paramList);
                if (count != paramList.size()) {
                    throw new PlatException("修改区域终点坐标失败,修改条数与传入条数不一致！");
                }
            } else {
                //库位
                int count = dao.updateBatch(LIDS0601.UPDATE, paramList);
                if (count != paramList.size()) {
                    throw new PlatException("修改库位终点坐标失败,修改条数与传入条数不一致！");
                }
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    /**
     * @serviceId S_LI_DS_0017
     * 根据传入的装卸点查询行车作业实绩的抓取返回行车编号
     * 根据PDA选择的装卸点以及装卸点配置的行车编号，找默认行车终到坐标是装卸点所在坐标的行车作业实绩，
     * 若没找到，可以继续找其他行车中起始位置是这个装卸点的坐标范围内的行车作业实绩
     */
    public EiInfo queryCraneResultEnd (EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String handPointId = inInfo.getString("handPointId");
            if (StringUtils.isBlank(handPointId)) {
                throw new RuntimeException("传入字段【装卸点代码】为空！");
            }
            HashMap queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("handPointId", handPointId);
            queryMap.put("status", "30");
            String craneId = "";
            HashMap craneIdMap = new HashMap();
            List<Map> craneIdList = new ArrayList<>();
            List<LIRL0304> loadingPointsList = this.dao.query(LIRL0304.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(loadingPointsList)) {
                craneId=loadingPointsList.get(0).getCraneId();
                String[] parts = craneId.split(",");
                for (String part : parts) {
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("craneId", part);
                    queryMap.put("status", "20");
                    List<LIDS1201> positionList = this.dao.query(LIDS1201.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(positionList)) {
                        queryMap.clear();
                        queryMap.put("segNo", segNo);
                        queryMap.put("XPosition", positionList.get(0).getStartXPosition());
                        queryMap.put("YPosition", positionList.get(0).getStartYPosition());
                        queryMap.put("factoryArea", inInfo.getString("factoryArea"));
                        queryMap.put("factoryBuilding", inInfo.getString("factoryBuilding"));
                        //先根据传入的X和Y轴查询是否属于数据装卸点的范围
                        List<LIRL0304> loadingPointRangeList = this.dao.query(LIRL0304.QUERY, queryMap);
                        if (CollectionUtils.isNotEmpty(loadingPointRangeList)) {
                            craneIdMap.put("craneId", loadingPointRangeList.get(0).getCraneId());
                            craneIdList.add(craneIdMap);
                        }
                    }
                }
            } else {
                throw new RuntimeException("传入字段【装卸点代码】未匹配到装卸点！");
            }
            outInfo.set("craneIdList", craneIdList);
            outInfo.setMsg("查询成功");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    /**
     * @serviceId 待补充
     * 发货时PDA传入捆包查询捆包是否有实绩调用
     */
    public EiInfo checkPackagePerformance(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            Map attr = inInfo.getAttr();
            String segNo = inInfo.getString("segNo");
            List<Map> list = (List<Map>)attr.get("list");
            List<HashMap> packList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)){
                for (Map map : list) {
                    //根据捆包号+目的地的坐标属于传入装卸点的行车作业实绩
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo", segNo);
                    queryMap.put("handPointId", map.get("handPointId"));
                    List<LIRL0304> handPointNo = this.dao.query(LIRL0304.QUERY,queryMap);
                    if (CollectionUtils.isNotEmpty(handPointNo)){
                        queryMap.put("packId", map.get("packId"));
                        queryMap.put("xInitialPoint",handPointNo.get(0).getxInitialPoint());
                        queryMap.put("xDestination", handPointNo.get(0).getxDestination());
                        queryMap.put("yInitialPoint", handPointNo.get(0).getyInitialPoint());
                        queryMap.put("yDestination", handPointNo.get(0).getyDestination());
                        String craneId = handPointNo.get(0).getCraneId();
                        String[] parts = craneId.split(",");
                        for (String part : parts) {
                            queryMap.put("craneId", part);
                            List<LIDS1201> craneResult =  this.dao.query("LIDS1201.queryCranePerformanceByPosition",queryMap);
                            if (CollectionUtils.isNotEmpty(craneResult)){
                                HashMap packMap = new HashMap();
                                packMap.put("packId",map.get("packId"));
                                packList.add(packMap);
                            }
                        }
                    }else {
                        throw new RuntimeException("传入装卸点:"+map.get("handPointId")+"未匹配，请检查装卸点是否生效！");
                    }

                }
            }
            outInfo.set("result",packList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败");
        }
        return outInfo;
    }
    /**
     * @serviceId S_LI_DS_0019
     * PDA查询机组代码信息
     */
    public EiInfo queryMachineCode(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            String factoryArea = inInfo.getString("factoryArea");
            String factoryBuilding = inInfo.getString("factoryBuilding");
            String machineCode = inInfo.getString("machineCode");
            String machineName = inInfo.getString("machineName");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if(StringUtils.isBlank(factoryArea)){
                throw new RuntimeException("传入字段【厂区代码】为空！");
            }
            if(StringUtils.isBlank(factoryBuilding)){
                throw new RuntimeException("传入字段【厂房代码】为空！");
            }

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("factoryArea", factoryArea);
            queryMap.put("factoryBuilding", factoryBuilding);
            queryMap.put("machineCode", machineCode);
            queryMap.put("machineName", machineName);
            List<LIDS0701> machineCodeList = this.dao.query(LIDS0701.QUERY_TO_MAP, queryMap);
            if (CollectionUtils.isEmpty(machineCodeList)) {
                outInfo.setMsg("未查到记录");
            } else {
                outInfo.setMsg("查询成功");
            }
            outInfo.set("returnList", machineCodeList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * @serviceId S_LI_DS_0020
     * PDA补捆包机组上料信息
     * (直接把捆包所属区域修改为对应机组上料区)
     */
    public EiInfo updatePackToMachineLoadingArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            log("调用LIDSInterfacesPda.updatePackToMachineLoadingArea更新捆包区域为机组上料区信息传入参数：" + inInfo.getAttr());
            System.out.println("调用LIDSInterfacesPda.updatePackToMachineLoadingArea更新捆包区域为机组上料区信息传入参数：" + inInfo.getAttr());

            String segNo = inInfo.getString("segNo");
            String factoryArea = inInfo.getString("factoryArea");
            String factoryBuilding = inInfo.getString("factoryBuilding");
            String machineCode = inInfo.getString("machineCode");
            String machineName = inInfo.getString("machineName");
            String packId = inInfo.getString("packId");
            String processOrderId = inInfo.getString("processOrderId");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(factoryArea)) {
                throw new RuntimeException("传入字段【厂区代码】为空！");
            }
            if (StringUtils.isBlank(factoryBuilding)) {
                throw new RuntimeException("传入字段【厂房代码】为空！");
            }
            if (StringUtils.isBlank(machineCode)) {
                throw new RuntimeException("传入字段【机组代码】为空！");
            }
            if (StringUtils.isBlank(machineName)) {
                throw new RuntimeException("传入字段【机组名称】为空！");
            }
            if (StringUtils.isBlank(packId)) {
                throw new RuntimeException("传入字段【捆包号】为空！");
            }
            if (StringUtils.isBlank(processOrderId)) {
                throw new RuntimeException("传入字段【工单号】为空！");
            }
            HashMap<String, Object> processMap = new HashMap<>();
            processMap.put("segNo", segNo);
            processMap.put("processOrderId",processOrderId);
            List<VIPM0009> processOrderList = this.dao.query(VIPM0009.QUERY,processMap);
            if (CollectionUtils.isNotEmpty(processOrderList)){
                if ("40".equals(processOrderList.get(0).getProcessDemandProcessStatus())){
                    throw new RuntimeException("捆包所属工单状态已结束，请检查工单号是否为当前生产的工单！");
                }

            }

            Map paramMap = new HashMap();
            paramMap.put("segNo", segNo);
            paramMap.put("factoryArea", factoryArea);
            paramMap.put("factoryBuilding", factoryBuilding);
            paramMap.put("machineCode", machineCode);
            paramMap.put("machineName", machineName);
            paramMap.put("packId", packId);
            paramMap.put("status", "10");//启用

            // 校验设备状态
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("machineCode", machineCode);
            eiInfo.set(EiConstant.serviceName, "VGDMInterfacePda");
            eiInfo.set(EiConstant.methodName, "queryBCDeviceStatus");
            EiInfo rtnInfo = XLocalManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            int flag = (Integer) rtnInfo.get("deviceFlag");
            if (flag < 1) {
                log("设备状态异常，不生成库存");
                outInfo.setMsg("设备状态异常，不生成库存！");
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                return outInfo;
            }

            //查询机组信息
            List<LIDS0701> machineCodeList = this.dao.query(LIDS0701.QUERY, paramMap);
            if (CollectionUtils.isEmpty(machineCodeList)) {
                throw new RuntimeException("传入参数【厂区代码】、【厂房代码】、【机组代码】、【机组名称】未匹配启用机组，请检查参数是否正确！");
            }
            HashMap<String, Object> areaMap = new HashMap<>();
            areaMap.put("segNo", segNo);
            areaMap.put("status", "10");
            areaMap.put("delFlag", "0");
            LIDS0701 lids0701 = machineCodeList.get(0);
            //判断机组优先上料区若不为空，更新捆包位置为优先上料区，否则更新为上料区代码;
            //同时将捆包坐标更新为空(或者更新在上料区范围内?)
            if (StringUtils.isNotBlank(lids0701.getPriorityLoadMaterialArea())) {
                paramMap.put("areaType", "60");//60：机组上料区
                paramMap.put("areaCode", lids0701.getPriorityLoadMaterialArea());
                paramMap.put("areaName", lids0701.getPriorityLoadMaterialAreaName());
                areaMap.put("areaCode", lids0701.getPriorityLoadMaterialArea());
                paramMap.put("xPosition", null);
                paramMap.put("yPosition", null);
                paramMap.put("zPosition", null);
            } else {
                paramMap.put("areaType", "60");//60：机组上料区
                paramMap.put("areaCode", lids0701.getMaterialLoadingArea());
                paramMap.put("areaName", lids0701.getMaterialLoadingAreaName());
                areaMap.put("areaCode", lids0701.getMaterialLoadingArea());
                paramMap.put("xPosition", null);
                paramMap.put("yPosition", null);
                paramMap.put("zPosition", null);
            }
            List <LIDS0901> areaList = this.dao.query(LIDS0901.QUERY,areaMap);
            if (CollectionUtils.isNotEmpty(areaList)) {
                StringBuilder packIdBuilder = new StringBuilder();
                for (LIDS0901 lids0901 : areaList) {
                    String packId1 = lids0901.getPackId(); //
                    packIdBuilder.append(packId1).append(","); // 拼接 PACK_ID，并用逗号分隔
                }
                if (packIdBuilder.length() > 0) {
                    packIdBuilder.setLength(packIdBuilder.length() - 1); // 去掉最后一个多余的逗号
                }
                throw new RuntimeException("捆包号：" + packIdBuilder + "未加工，如需补录，请将捆包号：" + packIdBuilder +"进行退料。");
            }

            //查询实物库存表是否存在该捆包，若不存在，则新增
            Map<String, Object> queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("packId", packId);
            queryMap.put("status", "10");
            queryMap.put("delFlag", "0");
            int count = super.count(LIDS0901.COUNT, queryMap);
            if (count < 1) {
                //新增捆包
                LIDS0901 lids0901 = new LIDS0901();
                lids0901.fromMap(paramMap);
                //查询VIPM0008投料捆包表，获取标签号，重量等字段
                queryMap.put("processOrderId",processOrderId);
                List<VIPM0008> vipm0008List = dao.query(VIPM0008.QUERY, queryMap);
                if (CollectionUtils.isEmpty(vipm0008List)) {
                    throw new RuntimeException("未找到该投料捆包信息：" + packId + ",工单号：" + processOrderId + "!");
                }
                VIPM0008 vipm0008 = vipm0008List.get(0);
                //补充新增实物库存表数据
                lids0901.setUnitCode(vipm0008.getUnitCode());//业务单元代码
                lids0901.setSegNo(vipm0008.getSegNo());//系统账套
                lids0901.setWarehouseCode(vipm0008.getWarehouseCode());//仓库代码
                lids0901.setWarehouseName(vipm0008.getWarehouseName());//仓库名称
                lids0901.setPackId(vipm0008.getPackId());//捆包号
                lids0901.setLabelId(vipm0008.getLabelId());//标签号
                lids0901.setPosDirCode("1");//层数标记
                lids0901.setNetWeight(vipm0008.getNetWeight());//净重
                lids0901.setGrossWeight(vipm0008.getGrossWeight());//毛重
                lids0901.setCraneOperationWeight(vipm0008.getNetWeight());//吊装重量
                lids0901.setQuantity(1);//捆包数量
                lids0901.setActionFlag("1");//板卷标记
                lids0901.setDelFlag(0);//删除标记
                lids0901.setUuid(UUIDUtils.getUUID());//
                RecordUtils.setCreatorBeanSys(lids0901);
                lids0901.setRecCreator(inInfo.getString("userId"));
                lids0901.setRecCreatorName(inInfo.getString("userName"));
                dao.insert(LIDS0901.INSERT, lids0901);
            } else {
                //修改库存捆包信息
                RecordUtils.setRevisorSys(paramMap);
                paramMap.put("recRevisor", inInfo.getString("userId"));
                paramMap.put("recRevisorName", inInfo.getString("userName"));
                count = dao.update(LIDS0901.UPDATE_AREA_BY_PACK_ID, paramMap);
               /* if (count != 1) {
                    throw new RuntimeException("更新库存捆包信息失败,根据传入参数：" + paramMap + ";更新数据为：" + count + "条！");
                }*/
            }


            // 特殊标记自动启动工单
            String autoStart = inInfo.getString("autoStart");
            if (StrUtil.isBlank(autoStart)) {
                autoStart = "0";
            }
            //一厂更新是否下发行车工标记
            if ("F1".equals(factoryBuilding)) {
                Map map = new HashMap();
                map.put("segNo", segNo);
                map.put("packId", packId);
                map.put("ifIssueCraneOperator", "20");
                map.put("processOrderId", processOrderId);
                map.put("recRevisor", "system");
                map.put("recRevisorName", "system");
                map.put("recReviseTime", DateUtil.curDateTimeStr14());
                //自启动工单标记为1时，不修改行车工标记
                if (!"1".equals(autoStart)) {
                    count = dao.update(VIPM0008.UPDATE_ISSUE_CRANE_FLAG, map);
                    if (count != 1) {
                        throw new RuntimeException("更新是否下发行车工标记失败,根据传入参数：" + map + ";更新数据为：" + count + "条！");
                    }
                }

                List<Map> demandMaterailList = new ArrayList<>();
                demandMaterailList.add(map);
                //将结果返回IMC
                EiInfo sendInfo = new EiInfo();
                sendInfo.set("demandMaterailList", demandMaterailList);
                sendInfo.set(EiConstant.serviceName, "LIDSInterfacesImc");
                sendInfo.set(EiConstant.methodName, "confirmIssueCraneInfo");
                sendInfo = XLocalManager.call(sendInfo);
                if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    throw new PlatException(sendInfo.getMsg());
                }
            }

            //特殊标记自动启动工单
            if ("1".equals(autoStart)) {
                log("特殊标记自动启动工单");
                // 校验设备状态
                eiInfo = new EiInfo();
                eiInfo.set("machineCode", machineCode);
                eiInfo.set(EiConstant.serviceName, "VGDMInterfacePda");
                eiInfo.set(EiConstant.methodName, "startOrderPda");
                rtnInfo = XLocalManager.call(eiInfo);
                if (rtnInfo.getStatus() < 0) {
                    throw new PlatException(rtnInfo.getMsg());
                }
            }

            outInfo.setMsg("更新成功！");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("更新失败：" + ex.getMessage());
        }finally {
            // 不论主逻辑是否成功，都尝试插入日志
            try {
                EiInfo sendInfo = inInfo;
                sendInfo.set(EiConstant.serviceName, "LIDSInterfacesPda");
                sendInfo.set(EiConstant.methodName, "insertLog");
                sendInfo.set("logStatus",outInfo.getStatus());
                sendInfo.set("resultOfProcessing",outInfo.getMsg());
                sendInfo = XLocalManager.callNewTx(sendInfo);
                if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    // 插入失败，不处理异常
                }
            } catch (Exception ignored) {
                // 插入失败，不处理异常
            }
        }
        return outInfo;
    }


    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0022
     * PDA机组上料补录查询工单信息
     */
    public EiInfo queryProcessOrder(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            String processOrderId = inInfo.getString("processOrderId");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(processOrderId)) {
                throw new RuntimeException("传入字段【工单号】为空！");
            }

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("processOrderIdLike", processOrderId);
            List<VIPM0009> demandMaterailList = dao.query(VIPM0009.QUERY, queryMap);
            if (CollectionUtils.isEmpty(demandMaterailList)) {
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("未找到工单信息:" + processOrderId);
                return outInfo;
            }

            //转换为List<Map>
            List<Map> mapList = demandMaterailList.stream()
                    .map(VIPM0009::toMap)
                    .collect(Collectors.toList());

            outInfo.set("processOrderList", mapList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败：" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0021
     * PDA机组上料补录根据工单号条件查询对应的投料捆包
     */
    public EiInfo queryPackIdByProcessOrderId(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            String processOrderId = inInfo.getString("processOrderId");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(processOrderId)) {
                throw new RuntimeException("传入字段【工单号】为空！");
            }

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("processOrderId", processOrderId);
            List<VIPM0008> demandMaterailList = dao.query(VIPM0008.QUERY, queryMap);
            if (CollectionUtils.isEmpty(demandMaterailList)) {
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("未找到工单投料捆包信息:" + processOrderId);
                return outInfo;
            }

            //转换为List<Map>
            List<Map> mapList = demandMaterailList.stream()
                    .map(VIPM0008::toMap)
                    .collect(Collectors.toList());

            outInfo.set("demandMaterailList", mapList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败：" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0023
     * PDA上料吊运信息查询
     */
    public EiInfo queryMaterialLiftingInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            String machineCode = inInfo.getString("machineCode");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(machineCode)) {
                throw new RuntimeException("传入字段【机组代码】为空！");
            }

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("machineCode", machineCode);

            List<HashMap> demandMaterailList = dao.query(VIPM0008.QUERY_MATERIAL_LIFTING_INFO, queryMap);

            outInfo.set("demandMaterailList", demandMaterailList);
            if (CollectionUtils.isEmpty(demandMaterailList)) {
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("未找到上料吊运信息:" + machineCode);
            } else {
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("查询成功！");
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败：" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0024
     * PDA上料吊运信息确认
     */
    public EiInfo confirmMaterialLiftingInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //登录人信息
            Map loginUser = RecordUtils.getLoginUser(inInfo);

            String segNo = inInfo.getString("segNo");
            String machineCode = inInfo.getString("machineCode");
            List<HashMap> demandMaterailList = (List<HashMap>) inInfo.get("demandMaterailList");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(machineCode)) {
                throw new RuntimeException("传入字段【机组代码】为空！");
            }
            if (CollectionUtils.isEmpty(demandMaterailList)) {
                throw new RuntimeException("传入【上料捆包信息】为空！");
            }

            for (HashMap map : demandMaterailList) {
                map.put("ifIssueCraneOperator", "20");
                map.put("recRevisor", MapUtils.getString(loginUser, "userId"));
                map.put("recRevisorName", MapUtils.getString(loginUser, "userName"));
                map.put("recReviseTime", DateUtil.curDateTimeStr14());
                dao.update(VIPM0008.UPDATE_ISSUE_CRANE_FLAG, map);
            }

            //将结果返回IMC
            EiInfo sendInfo = new EiInfo();
            sendInfo.set("demandMaterailList", demandMaterailList);
            sendInfo.set(EiConstant.serviceName, "LIDSInterfacesImc");
            sendInfo.set(EiConstant.methodName, "confirmIssueCraneInfo");
            sendInfo = XLocalManager.callNoTx(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(sendInfo.getMsg());
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("处理失败：" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0025
     * 查询吊具信息
     */
    public EiInfo queryHoistingToolInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("segNo",segNo);
            List<LIDS1301> hoistInfo = this.dao.query(LIDS1301.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(hoistInfo)){
                outInfo.set("result",hoistInfo);
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败：" + ex.getMessage());
        }
        return outInfo;
    }
    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0026
     * PDA更换吊具后传给UWB方
     */
    public EiInfo changeHoistingTool(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String hoistId = inInfo.getString("hoistId");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【吊具编号】为空！");
            }
            String hoistWeight = inInfo.getString("hoistWeight");
            HashMap<String, Object> hoistInfo = new HashMap<>();
            hoistInfo.put("segNo",segNo);
            hoistInfo.put("hoistId",hoistId);
            hoistInfo.put("hoistWeight",hoistWeight);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("更换成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("更换失败：" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0027
     * 根据传入的捆包号查询工单号和机组信息
     */
    public EiInfo queryProcessMachineInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            log("由工号"+inInfo.get("userId")+",姓名"+inInfo.get("userName")+"发起上料补录查询，传入参数为：" + inInfo.getAttr());
            System.out.println("由工号"+inInfo.get("userId")+",姓名"+inInfo.get("userName")+"发起上料补录查询，传入参数为：" + inInfo.getAttr());
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String packId = inInfo.getString("packId");
            if (StringUtils.isBlank(packId)) {
                throw new RuntimeException("传入字段【捆包号】为空！");
            }
            HashMap<String, Object> queryMap = new HashMap<>();
            HashMap<String, Object> outMap = new HashMap<>();
            String machineCode = "";
            String machineName = "";
            String processOrderId = "";
            queryMap.put("segNo", segNo);
            queryMap.put("packId", packId);
            List<HashMap<String, Object>> processList = this.dao.query("VIPM0008.queryMaterialProcessInfo", queryMap);
            if (CollectionUtils.isNotEmpty(processList)) {
                machineCode = (String) (processList.get(0).get("machineCode"));
                machineName = (String) (processList.get(0).get("machineName"));
                processOrderId = (String) (processList.get(0).get("processOrderId"));
            }else {
                //查询不到工单则调用工单下发方法，之后重新查询
                inInfo.set("packMap", queryMap);
                inInfo.set("serviceId", "S_VI_PM_1033");
                outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                if (outInfo.getStatus() == EiConstant.STATUS_SUCCESS){
                    List<HashMap<String, Object>> processList1 = this.dao.query("VIPM0008.queryMaterialProcessInfo", queryMap);
                    if (CollectionUtils.isNotEmpty(processList1)){
                        machineCode = (String) (processList1.get(0).get("machineCode"));
                        machineName = (String) (processList1.get(0).get("machineName"));
                        processOrderId = (String) (processList1.get(0).get("processOrderId"));
                    }
                }

            }
            outMap.put("machineCode", machineCode);
            outMap.put("machineName", machineName);
            outMap.put("processOrderId", processOrderId);
            outInfo.set("result", outMap);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败：" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0029
     * 生产实绩采集，捆包上料回退，
     * 区域修改为机组下料区，触发IMC领料回退
     */
    public EiInfo rollbackMaterialLoading(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            log("由工号" + inInfo.get("userId") + ",姓名" + inInfo.get("userName") + "发起机组上料回退，传入参数为：" + inInfo.getAttr());
            System.out.println("由工号" + inInfo.get("userId") + ",姓名" + inInfo.get("userName") + "发起机组上料回退，传入参数为：" + inInfo.getAttr());

            String segNo = inInfo.getString("segNo");
            String factoryArea = inInfo.getString("factoryArea");
            String factoryBuilding = inInfo.getString("factoryBuilding");
            String machineCode = inInfo.getString("machineCode");
            String machineName = inInfo.getString("machineName");
            String packId = inInfo.getString("packId");
            String processOrderId = inInfo.getString("processOrderId");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(factoryArea)) {
                throw new RuntimeException("传入字段【厂区代码】为空！");
            }
            if (StringUtils.isBlank(factoryBuilding)) {
                throw new RuntimeException("传入字段【厂房代码】为空！");
            }
            if (StringUtils.isBlank(machineCode)) {
                throw new RuntimeException("传入字段【机组代码】为空！");
            }
            if (StringUtils.isBlank(machineName)) {
                throw new RuntimeException("传入字段【机组名称】为空！");
            }
            if (StringUtils.isBlank(packId)) {
                throw new RuntimeException("传入字段【捆包号】为空！");
            }
            if (StringUtils.isBlank(processOrderId)) {
                throw new RuntimeException("传入字段【工单号】为空！");
            }

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("segNo", segNo);
            paramMap.put("factoryArea", factoryArea);
            paramMap.put("factoryBuilding", factoryBuilding);
            paramMap.put("machineCode", machineCode);
            paramMap.put("machineName", machineName);
            paramMap.put("packId", packId);
            paramMap.put("status", "10");//启用

            //查询机组信息
            List<LIDS0701> machineCodeList = dao.query(LIDS0701.QUERY, paramMap);
            if (CollectionUtils.isEmpty(machineCodeList)) {
                throw new RuntimeException("传入参数【厂区代码】、【厂房代码】、【机组代码】、【机组名称】未匹配启用机组，请检查参数是否正确！");
            }

            //查询机组信息，将区域修改为机组下料区
            LIDS0701 lids0701 = machineCodeList.get(0);
            paramMap.put("areaType", "63");//63：机组退料区
            paramMap.put("areaCode", lids0701.getRejectionArea());
            paramMap.put("areaName", lids0701.getRejectionAreaName());
            paramMap.put("xPosition", null);
            paramMap.put("yPosition", null);
            paramMap.put("zPosition", null);
            //修改实物库存捆包信息
            RecordUtils.setRevisorSys(paramMap);
            paramMap.put("status", "10");
            paramMap.put("delFlag", "0");
            dao.update(LIDS0901.ROLLBACK_MATERIAL_LOADING, paramMap);

            //删除库存管理备份表数据
            dao.delete(LIDS0902.DELETE, paramMap);


            // 调用生产实绩采集回退
            EiInfo temInfo = new EiInfo();
            temInfo.set("segNo", segNo);
            temInfo.set("packId", packId);
            temInfo.set("machineCode", machineCode);
            temInfo.set(EiConstant.serviceName, "VGDM1002");
            temInfo.set(EiConstant.methodName, "pdaBackPack");
            EiInfo rtnInfo = XLocalManager.call(temInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }

            //TODO 调用IMC领料回退

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("回退成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("回退失败：" + ex.getMessage());
        }
        return outInfo;
    }

    public EiInfo insertLog(EiInfo inInfo){
        String segNo = inInfo.getString("segNo");
        String factoryArea = inInfo.getString("factoryArea");
        String factoryBuilding = inInfo.getString("factoryBuilding");
        String machineCode = inInfo.getString("machineCode");
        String machineName = inInfo.getString("machineName");
        String packId = inInfo.getString("packId");
        String processOrderId = inInfo.getString("processOrderId");
        HashMap<String, Object> logMap = new HashMap<>();
        logMap.put("recCreator", inInfo.getString("userId"));
        logMap.put("recCreatorName", inInfo.getString("userName"));
        logMap.put("recCreateTime", DateUtil.curDateTimeStr14());
        logMap.put("segNo",segNo);
        logMap.put("unitCode",segNo);
        logMap.put("factoryArea",factoryArea);
        logMap.put("factoryBuilding",factoryBuilding);
        logMap.put("machineCode",machineCode);
        logMap.put("machineName",machineName);
        logMap.put("packId",packId);
        logMap.put("processOrderId",processOrderId);
        logMap.put("status",inInfo.get("logStatus"));
        logMap.put("resultOfProcessing",inInfo.get("resultOfProcessing"));
        logMap.put("uuid",UUIDUtils.getUUID());
        this.dao.insert("LIDS0101.insertLog",logMap);
        return inInfo;
    }
    /**
     * @param inInfo
     * @return outInfo
     * @serviceId S_LI_DS_0030
     * 初始化落地捆包信息
     */
    public EiInfo initializePackageInfo(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            Map attr = inInfo.getAttr();
            String segNo = MapUtils.getString(attr,"segNo","");
            if (StringUtils.isBlank(segNo)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少系统账套信息，新增失败");
                return outInfo;
            }
            String packId = MapUtils.getString(attr,"packId","");
            if (StringUtils.isBlank(packId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少捆包号信息，新增失败");
                return outInfo;
            }
            attr.put("uuid",UUIDUtils.getUUID());
            attr.put("unitCode",segNo);
            this.dao.insert("LIDS0901.1501insert",attr);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("新增成功");
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("新增失败：" + e.getMessage());
        }
        return outInfo;
    }
}
