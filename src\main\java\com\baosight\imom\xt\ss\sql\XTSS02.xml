<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="XTSS02">

    <select id="querySequenceBase" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select  t.SEQ_CTR_CLASS  as "seqCtrClass",
                COALESCE(t.seq_style, '00')  as "seqStyle"
        from ${platSchema}.T_SS_SEQUENCE_BASE t
        where 1=1
        <isNotEmpty prepend=" AND " property="seqTypeId">
            t.SEQ_TYPE_ID = #seqTypeId#
        </isNotEmpty>
    </select>

    <select id="querySequence" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select  t.SEQ_SUBSECTIONS     as "segSubsections",
                t.<PERSON>ATE_CYCLE  as "dateCycle",
                t.SEQ_LIMIT  as "seqLimit",
                t.SUBID_LIMIT_LENGTH as "subidLimitLength",
                COALESCE(t.seq_type, '1') as "seqType",
                COALESCE(t.ORACLE_SEQ, '-') as "oracleSeq"
        from ${platSchema}.T_SS_SEQUENCE t
        where 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            t.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="seqTypeId">
            t.SEQ_TYPE_ID = #seqTypeId#
        </isNotEmpty>
    </select>

    <select id="querySequenceSubSeg" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select  t.SUBSECTION_SEQ     as "subsectionSeq",
        t.SUBSECTION_NAME  as "subsectionName",
        t.SUBSECTION_TYPE  as "subsectionType",
        t.SUBSECTION_CONTENT as "subsectionContent",
        t.DATE_TYPE as "dateType",
        t.SUBSECTION_LENGTH as "subsectionLength"
        from ${platSchema}.T_SS_SEQ_SUBSECTION_SEG t
        where 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            t.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="seqTypeId">
            t.SEQ_TYPE_ID = #seqTypeId#
        </isNotEmpty>
        order by t.SUBSECTION_SEQ
    </select>


    <select id="querySequenceGen" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select  t.SEG_NO     as "segNo",
        t.ACCSET_NO as "accsetNo",
        t.SEQ_TYPE_ID  as "seqTypeId",
        t.SEQ_PREFIX  as "lsSequence",
        t.YEAR_OR_MONTH as "yearOrMonth",
        t.CURRENT_SEQ as "currentSeq"
        from ${platSchema}.T_SS_SEQUENCE_GENERATOR t
        where 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            t.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="seqTypeId">
            t.SEQ_TYPE_ID = #seqTypeId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="lsSequence">
            t.SEQ_PREFIX = #lsSequence#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="yearOrMonth">
            t.YEAR_OR_MONTH = #yearOrMonth#
        </isNotEmpty>
    </select>


    <insert id="insertSequenceGen">
        INSERT INTO ${platSchema}.T_SS_SEQUENCE_GENERATOR
        (SEG_NO, <!-- 业务单元 -->
         ACCSET_NO, <!-- 帐套序号 -->
         SEQ_TYPE_ID,<!-- 序列号类型编号 -->
         SEQ_PREFIX,<!-- 序列号无流水号段内容 -->
         YEAR_OR_MONTH,<!-- 年月 -->
         CURRENT_SEQ <!-- 当前值 -->
        )
        VALUES
        (#segNo:VARCHAR#,
         #accSetNo:VARCHAR#,
         #seqTypeId:VARCHAR#,
         #lsSequence:VARCHAR#,
         #yearOrMonth:VARCHAR#,
         #currentSeq:VARCHAR#
        )
    </insert>


    <update id="updateSequenceGen">
        UPDATE ${platSchema}.T_SS_SEQUENCE_GENERATOR t
        SET t.CURRENT_SEQ= t.CURRENT_SEQ + 1 <!-- 当前值+1 -->
        WHERE 1 = 1
          and t.SEG_NO= #segNo# <!-- 业务单元 -->
          and t.ACCSET_NO = #accSetNo# <!-- 帐套序号 -->
          and t.SEQ_TYPE_ID = #seqTypeId#<!-- 序列号类型编号 -->
          and t.SEQ_PREFIX = #lsSequence#<!-- 序列号无流水号段内容 -->
          and t.YEAR_OR_MONTH = #yearOrMonth#<!-- 年月 -->
    </update>


    <!-- 查询业务单元 -->
    <select id="queryTvzbm81" resultClass="com.baosight.imom.xt.ss.domain.XTSS02" parameterClass="java.util.HashMap">

        SELECT
        ID	as "id",  <!-- uuid -->
        SEG_NO	as "segNo",  <!-- 业务单元编码 -->
        SEG_FULL_NAME	as "segFullName",  <!-- 业务单元名称 -->
        FATHER_SEG_NO	as "fatherSegNo",  <!-- 上级业务单元 -->
        SEG_NAME	as "segName",  <!-- 业务单元简称 -->
        ORG_CODE	as "orgCode",  <!-- 对应EHR组织机构码 -->
        COMPANY_CODE	as "companyCode",  <!-- 公司别代码（法人单位编码）,基于公司别扩充 -->
        IF_VIRTUAL_ORG	as "ifVirtualOrg",  <!-- 是否虚拟组织,0真实机构/1虚拟机构 -->
        SUB_PART_1	as "subPart1",  <!-- 预留分段一,参与业务单元代码编码 -->
        CORP_TYPE	as "corpType",  <!-- 法人单位分类 -->
        SEG_STATUS	as "segStatus",  <!-- 业务单元状态：00作废,10锁定(预留),20正常 -->
        SPARE_ATTRIBUTE	as "spareAttribute",  <!-- 备用属性 -->
        INTEGRATION_SEG_NO	as "integrationSegNo",  <!-- 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码 -->
        ACCOUNT_SET	as "accountSet",  <!-- 标财帐套号,法人业务单元必填 -->
        USER_NUM	as "userNum",  <!-- 集团统一客商编码 -->
        REGIONAL_INTEGRATION_SEG_NO	as "regionalIntegrationSegNo",  <!-- 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码 -->
        FACTORY_TYPE	as "factoryType",  <!-- 加工中心细分类,00-非加工中心,10核心工厂 -->
        REMARK	as "remark",  <!-- 备注 -->
        RELATED6_SEG_NO	as "related6SegNo",  <!-- 所属6级业务单元代码 -->
        RELATED3_SEG_NO	as "related3SegNo",  <!-- 所属3级业务单元代码 -->
        RELATED2_SEG_NO	as "related2SegNo",  <!-- 所属2级业务单元代码 -->
        RELATED1_SEG_NO	as "related1SegNo",  <!-- 所属1级业务单元代码 -->
        RELATED0_SEG_NO	as "related0SegNo",  <!-- 所属0级业务单元代码 -->
        CORP_FLAG	as "corpFlag",  <!-- 法人单位标志 0否 1是 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 记录归档标记 -->
        DEL_FLAG	as "delFlag",  <!-- 记录删除标记(默认0 删除1) -->
        TENANT_USER	as "tenantUser",  <!-- 租户 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
        SEG_E_START	as "segEStart",  <!-- 有效期起始 -->
        SEG_E_END	as "segEEnd",  <!-- 有效期截止 -->
        SEG_LEVEL	as "segLevel",  <!-- 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级 -->
        SORT_ID	as "sortId",  <!-- 排序号 -->
        SUB_SORT_ID	as "subSortId",  <!-- 子排序号 -->
        ORG_TYPE	as "orgType" <!-- 机构分类 -->
        FROM ${iplat4j}.tvzbm81
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="id">
            ID = #id#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segFullName">
            SEG_FULL_NAME = #segFullName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segName">
            SEG_NAME = #segName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fatherSegNo">
            FATHER_SEG_NO = #fatherSegNo#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                ID asc
            </isEmpty>
        </dynamic>


    </select>

</sqlMap>