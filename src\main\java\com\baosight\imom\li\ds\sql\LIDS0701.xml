<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-29 15:12:54
       Version :  1.0
    tableName :meli.tlids0701
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     MACHINE_CODE  VARCHAR   NOT NULL,
     MACHINE_NAME  VARCHAR   NOT NULL,
     E_ARCHIVES_NO  VARCHAR   NOT NULL,
     EQUIPMENT_NAME  VARCHAR   NOT NULL,
     PROCESS_CATEGORY  VARCHAR   NOT NULL,
     PROCESS_CATEGORY_NAME  VARCHAR   NOT NULL,
     CROSS_AREA  VARCHAR   NOT NULL,
     CROSS_AREA_NAME  VARCHAR   NOT NULL,
     UNPACK_AREA_ID  VARCHAR   NOT NULL,
     UNPACK_AREA_NAME  VARCHAR   NOT NULL,
     MATERIAL_LOADING_AREA  VARCHAR   NOT NULL,
     MATERIAL_LOADING_AREA_NAME  VARCHAR   NOT NULL,
     MATERIAL_UNLOADING_AREA  VARCHAR   NOT NULL,
     MATERIAL_UNLOADING_AREA_NAME  VARCHAR   NOT NULL,
     REJECTION_AREA  VARCHAR   NOT NULL,
     REJECTION_AREA_NAME  VARCHAR   NOT NULL,
     MOULD_CART  VARCHAR   NOT NULL,
     MOULD_CART_NAME  VARCHAR   NOT NULL,
     PACKAGING_TYPE  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL   primarykey
-->
<sqlMap namespace="LIDS0701">

    <sql id="condition">
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE LIKE '%$machineCode$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineName">
            MACHINE_NAME LIKE '%$machineName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaId">
            UNPACK_AREA_ID = #unpackAreaId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaName">
            UNPACK_AREA_NAME LIKE '%$unpackAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategory">
            PROCESS_CATEGORY  in ($processCategory$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS > '00'
            AND DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0701">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME as "processCategoryName",  <!-- 工序大类名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        UNPACK_AREA_ID as "unpackAreaId",  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME as "unpackAreaName",  <!-- 拆包区名称 -->
        MATERIAL_LOADING_AREA as "materialLoadingArea",  <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME as "materialLoadingAreaName",  <!-- 机组上料区名称 -->
        MATERIAL_UNLOADING_AREA as "materialUnloadingArea",  <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME as "materialUnloadingAreaName",  <!-- 机组下料区名称 -->
        REJECTION_AREA as "rejectionArea",  <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME as "rejectionAreaName",  <!-- 机组退料区名称 -->
        MOULD_CART as "mouldCart",  <!-- 机组模具台车代码 -->
        MOULD_CART_NAME as "mouldCartName",  <!-- 机组模具台车名称 -->
        PACKAGING_TYPE as "packagingType",  <!-- 包装类型 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        PRIORITY_LOAD_MATERIAL_AREA as "priorityLoadMaterialArea",<!--优先上料区-->
        PRIORITY_LOAD_MATERIAL_AREA_NAME as "priorityLoadMaterialAreaName",<!--优先上料区名称-->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName"  <!-- 厂房名称 -->
        FROM meli.tlids0701 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryToMap" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME as "processCategoryName",  <!-- 工序大类名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        UNPACK_AREA_ID as "unpackAreaId",  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME as "unpackAreaName",  <!-- 拆包区名称 -->
        MATERIAL_LOADING_AREA as "materialLoadingArea",  <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME as "materialLoadingAreaName",  <!-- 机组上料区名称 -->
        MATERIAL_UNLOADING_AREA as "materialUnloadingArea",  <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME as "materialUnloadingAreaName",  <!-- 机组下料区名称 -->
        REJECTION_AREA as "rejectionArea",  <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME as "rejectionAreaName",  <!-- 机组退料区名称 -->
        MOULD_CART as "mouldCart",  <!-- 机组模具台车代码 -->
        MOULD_CART_NAME as "mouldCartName",  <!-- 机组模具台车名称 -->
        PACKAGING_TYPE as "packagingType",  <!-- 包装类型 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        PRIORITY_LOAD_MATERIAL_AREA as "priorityLoadMaterialArea",<!--优先上料区-->
        PRIORITY_LOAD_MATERIAL_AREA_NAME as "priorityLoadMaterialAreaName",<!--优先上料区名称-->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName"  <!-- 厂房名称 -->
        FROM meli.tlids0701 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0701 WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--根据UUID用来校验数据状态-->
    <select id="count_uuid" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0701 WHERE 1=1
        <include refid="condition"/>
        AND UUID = #uuid#
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE = #machineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineName">
            MACHINE_NAME = #machineName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="e_archivesNo">
            E_ARCHIVES_NO = #e_archivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME = #equipmentName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategory">
            PROCESS_CATEGORY = #processCategory#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategoryName">
            PROCESS_CATEGORY_NAME = #processCategoryName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME = #crossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaId">
            UNPACK_AREA_ID = #unpackAreaId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaName">
            UNPACK_AREA_NAME = #unpackAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="materialLoadingArea">
            MATERIAL_LOADING_AREA = #materialLoadingArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="materialLoadingAreaName">
            MATERIAL_LOADING_AREA_NAME = #materialLoadingAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="materialUnloadingArea">
            MATERIAL_UNLOADING_AREA = #materialUnloadingArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="materialUnloadingAreaName">
            MATERIAL_UNLOADING_AREA_NAME = #materialUnloadingAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rejectionArea">
            REJECTION_AREA = #rejectionArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rejectionAreaName">
            REJECTION_AREA_NAME = #rejectionAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="mouldCart">
            MOULD_CART = #mouldCart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="mouldCartName">
            MOULD_CART_NAME = #mouldCartName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packagingType">
            PACKAGING_TYPE = #packagingType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids0701 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME,  <!-- 工序大类名称 -->
        CROSS_AREA,  <!-- 跨区编码 -->
        CROSS_AREA_NAME,  <!-- 跨区名称 -->
        UNPACK_AREA_ID,  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME,  <!-- 拆包区名称 -->
        MATERIAL_LOADING_AREA,  <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME,  <!-- 机组上料区名称 -->
        MATERIAL_UNLOADING_AREA,  <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME,  <!-- 机组下料区名称 -->
        REJECTION_AREA,  <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME,  <!-- 机组退料区名称 -->
        MOULD_CART,  <!-- 机组模具台车代码 -->
        MOULD_CART_NAME,  <!-- 机组模具台车名称 -->
        PACKAGING_TYPE,  <!-- 包装类型 -->
        STATUS,  <!-- 状态 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        PRIORITY_LOAD_MATERIAL_AREA,<!--优先上料区-->
        PRIORITY_LOAD_MATERIAL_AREA_NAME,<!--优先上料区名称-->FACTORY_AREA,  <!-- 厂区代码 -->
        FACTORY_AREA_NAME,  <!-- 厂区名称 -->
        FACTORY_BUILDING,  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME  <!-- 厂房名称 -->
        )
        VALUES (#segNo#, #unitCode#, #machineCode#, #machineName#, #e_archivesNo#, #equipmentName#, #processCategory#,
        #processCategoryName#, #crossArea#, #crossAreaName#, #unpackAreaId#, #unpackAreaName#, #materialLoadingArea#,
        #materialLoadingAreaName#, #materialUnloadingArea#, #materialUnloadingAreaName#, #rejectionArea#,
        #rejectionAreaName#, #mouldCart#, #mouldCartName#, #packagingType#, #status#, #recCreator#, #recCreatorName#,
        #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#,
        #uuid#,#priorityLoadMaterialArea#,#priorityLoadMaterialAreaName#,#factoryArea#,
        #factoryAreaName#, #factoryBuilding#, #factoryBuildingName#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids0701 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlids0701
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        MACHINE_NAME = #machineName#,   <!-- 机组名称 -->
        E_ARCHIVES_NO = #e_archivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCESS_CATEGORY = #processCategory#,   <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME = #processCategoryName#,   <!-- 工序大类名称 -->
        CROSS_AREA = #crossArea#,   <!-- 跨区编码 -->
        CROSS_AREA_NAME = #crossAreaName#,   <!-- 跨区名称 -->
        UNPACK_AREA_ID = #unpackAreaId#,   <!-- 拆包区编号 -->
        UNPACK_AREA_NAME = #unpackAreaName#,   <!-- 拆包区名称 -->
        MATERIAL_LOADING_AREA = #materialLoadingArea#,   <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME = #materialLoadingAreaName#,   <!-- 机组上料区名称 -->
        MATERIAL_UNLOADING_AREA = #materialUnloadingArea#,   <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME = #materialUnloadingAreaName#,   <!-- 机组下料区名称 -->
        REJECTION_AREA = #rejectionArea#,   <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME = #rejectionAreaName#,   <!-- 机组退料区名称 -->
        MOULD_CART = #mouldCart#,   <!-- 机组模具台车代码 -->
        MOULD_CART_NAME = #mouldCartName#,   <!-- 机组模具台车名称 -->
        PACKAGING_TYPE = #packagingType#,   <!-- 包装类型 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        PRIORITY_LOAD_MATERIAL_AREA = #priorityLoadMaterialArea#,<!--优先上料区-->
        PRIORITY_LOAD_MATERIAL_AREA_NAME = #priorityLoadMaterialAreaName#,<!--优先上料区名称-->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区代码 -->
        FACTORY_AREA_NAME = #factoryAreaName#,   <!-- 厂区名称 -->
        FACTORY_BUILDING = #factoryBuilding#,   <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME = #factoryBuildingName#   <!-- 厂房名称 -->
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <update id="updateStatus">
        UPDATE meli.tlids0701
        SET
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend="," property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

<!--    根据机组代码查询机组下的默认的仓库和库位信息-->
    <select id="queryLocation" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MATERIAL_LOADING_AREA as "materialLoadingArea",  <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME as "materialLoadingAreaName",  <!-- 机组上料区名称 -->
        PRIORITY_LOAD_MATERIAL_AREA as "priorityLoadMaterialArea",  <!-- 优先上料区代码 -->
        PRIORITY_LOAD_MATERIAL_AREA_NAME as "priorityLoadMaterialAreaName",  <!-- 优先上料区名称 -->
        MATERIAL_UNLOADING_AREA as "materialUnloadingArea",  <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME as "materialUnloadingAreaName",  <!-- 机组下料区名称 -->
        REJECTION_AREA as "rejectionArea",  <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME as "rejectionAreaName",  <!-- 机组退料区名称 -->
        MOULD_CART as "mouldCart",  <!-- 机组模具台车代码 -->
        MOULD_CART_NAME as "mouldCartName",  <!-- 机组模具台车名称 -->
        MATERIAL_WAREHOUSE_CODE as "materialWarehouseCode",  <!-- 默认原料库区 -->
        MATERIAL_LOCATION_ID as "materialLocationId",  <!-- 默认原料库位 -->
        PRODUCT_WAREHOUSE_CODE as "productWarehouseCode",  <!-- 默认成品库区 -->
        PRODUCT_LOCATION_ID as "productLocationId",  <!-- 默认成品库位 -->
        MATERIAL_WAREHOUSE_NAME as "materialWarehouseName",  <!-- 默认原料仓库名称 -->
        MATERIAL_LOCATION_NAME as "materialLocationName",  <!-- 默认原料库位名称 -->
        PRODUCT_WAREHOUSE_NAME as "productWarehouseName",  <!-- 默认成品仓库名称 -->
        PRODUCT_LOCATION_NAME as "productLocationName",  <!-- 默认成品库位名称 -->
        PRIORITY_LOAD_MATERIAL_AREA as "priorityLoadMaterialArea",<!--优先上料区-->
        PRIORITY_LOAD_MATERIAL_AREA_NAME as "priorityLoadMaterialAreaName"<!--优先上料区名称-->
        FROM meli.tlids0701 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
        ORDER BY REC_CREATE_TIME DESC

    </select>
    <!--    根据机组代码查询机组下的拆包区跨区-->
    <select id="queryMachineCodeCrossArea" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        UNPACK_AREA_ID as "unpackAreaId",  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME as "unpackAreaName",  <!-- 拆包区名称 -->
        MATERIAL_LOADING_AREA as "materialLoadingArea",  <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME as "materialLoadingAreaName",  <!-- 机组上料区名称 -->
        MATERIAL_UNLOADING_AREA as "materialUnloadingArea",  <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME as "materialUnloadingAreaName",  <!-- 机组下料区名称 -->
        REJECTION_AREA as "rejectionArea",  <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME as "rejectionAreaName",  <!-- 机组退料区名称 -->
        MOULD_CART as "mouldCart",  <!-- 机组模具台车代码 -->
        MOULD_CART_NAME as "mouldCartName",  <!-- 机组模具台车名称 -->
        (SELECT a.CROSS_AREA
        FROM MELI.TLIDS0501 a
        WHERE t.SEG_NO = a.SEG_NO
        AND t.UNPACK_AREA_ID = a.UNPACK_AREA_ID and a.STATUS > '00' and t.DEL_FLAG='0'  limit 1) as "unpackCrossArea" <!-- 机组拆包区跨区编码 -->
        FROM meli.tlids0701 t
        WHERE t.SEG_NO = #segNo#
        AND t.MACHINE_CODE = #machineCode#
        AND t.STATUS > '00'
        AND t.DEL_FLAG = '0'
        ORDER BY t.REC_CREATE_TIME DESC
    </select>

    <!--    根据传入各区域信息匹配对应的机组-->
    <select id="queryMachineByArea" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME as "processCategoryName",  <!-- 工序大类名称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区编码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        UNPACK_AREA_ID as "unpackAreaId",  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME as "unpackAreaName",  <!-- 拆包区名称 -->
        MATERIAL_LOADING_AREA as "materialLoadingArea",  <!-- 机组上料区代码 -->
        MATERIAL_LOADING_AREA_NAME as "materialLoadingAreaName",  <!-- 机组上料区名称 -->
        MATERIAL_UNLOADING_AREA as "materialUnloadingArea",  <!-- 机组下料区代码 -->
        MATERIAL_UNLOADING_AREA_NAME as "materialUnloadingAreaName",  <!-- 机组下料区名称 -->
        REJECTION_AREA as "rejectionArea",  <!-- 机组退料区代码 -->
        REJECTION_AREA_NAME as "rejectionAreaName",  <!-- 机组退料区名称 -->
        MOULD_CART as "mouldCart",  <!-- 机组模具台车代码 -->
        MOULD_CART_NAME as "mouldCartName",  <!-- 机组模具台车名称 -->
        PACKAGING_TYPE as "packagingType",  <!-- 包装类型 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        PRIORITY_LOAD_MATERIAL_AREA as "priorityLoadMaterialArea",<!--优先上料区-->
        PRIORITY_LOAD_MATERIAL_AREA_NAME as "priorityLoadMaterialAreaName",<!--优先上料区名称-->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName"  <!-- 厂房名称 -->
        FROM meli.tlids0701 t
        WHERE SEG_NO = #segNo#
        AND FACTORY_AREA = #factoryArea#
        AND FACTORY_BUILDING = #factoryBuilding#
        <isNotEmpty prepend="AND" property="crossArea">
            CROSS_AREA LIKE '%$crossArea$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="crossAreaName">
            CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="unpackAreaId">
            UNPACK_AREA_ID LIKE '%$unpackAreaId$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="unpackAreaName">
            UNPACK_AREA_NAME LIKE '%$unpackAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="materialUnloadingArea">
            MATERIAL_UNLOADING_AREA LIKE '%$materialUnloadingArea$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="materialUnloadingAreaName">
            MATERIAL_UNLOADING_AREA_NAME LIKE '%$materialUnloadingAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="rejectionArea">
            REJECTION_AREA LIKE '%$rejectionArea$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="rejectionAreaName">
            REJECTION_AREA_NAME LIKE '%$rejectionAreaName$%'
        </isNotEmpty>
        AND STATUS = '10'
        AND DEL_FLAG = '0'
    </select>
</sqlMap>