$(function () {
    var uploadList = [];
    var uploadIndex = -1;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });
    //查询
    $("#QUERY2").on("click", function (e) {

        const prodTypeId = $("#inqu2_status-0-prodTypeId").val();
        const prodTypeIdName = $("#inqu2_status-0-prodTypeIdName").val();
        var inInfo = new EiInfo();
        if (!IPLAT.isBlankString(prodTypeId)){
            inInfo.set("prodTypeId", prodTypeId);
        }
        if (!IPLAT.isBlankString(prodTypeIdName)){
            inInfo.set("prodTypeIdName", prodTypeIdName);
        }
        EiCommunicator.send("LIRL0318", "query2", inInfo, {
            onSuccess: function (ei) {
                if ("-1" == ei.status) {
                    NotificationUtil(ei);
                } else {
                    prodThirdCodeGrid.setEiInfo(ei);
                }
            },
            onFail: function (ei) {
                NotificationUtil(ei);
                return false;
            }
        });
    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "prodTypeId",
                    enable: true,
                    hidden: false,
                    locked: false,
                    title: "品种附属码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "prodThirdCode",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "三级品种附属码查询"
                            })
                        }
                    }
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // 获取勾选数据，
                // $("#INSERTSAVEM").on("click", function (e) {
                //     if (resultGrid.getCheckedRows().length <= 0) {
                //         NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                //         e.preventDefault();
                //         return false;
                //     }
                //     var info = new EiInfo();
                //     info.setByNode("result");
                //     info.addBlock(resultGrid.getCheckedBlockData());
                //     IPLAT.progress($("body"), true);
                //     EiCommunicator.send("LIRL0318", "insert", info, {
                //         onSuccess: function (ei) {
                //             if ("-1" == ei.status) {
                //                 NotificationUtil({msg: ei.msg}, "error");
                //             } else {
                //                 NotificationUtil({msg: ei.msg}, "sccess");
                //                 resultGrid.dataSource.page(1);
                //                 resultGrid.refresh();
                //             }
                //             IPLAT.progress($("body"), false);
                //         },
                //         onFail: function (ei) {
                //             IPLAT.progress($("body"), false);
                //             NotificationUtil({msg: ei.msg}, "error");
                //             return false;
                //         }
                //     });
                //
                // });
                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0318", "confirm", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0318", "confirmNo", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });

                /**
                 * 删除
                 */
                $("#DELETEN").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0318", "delete", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', '');
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                if (e.field === "voucherNum" && e.model.status !=='') {
                    e.preventDefault()
                }
            },
            /**
             * 数据ajax提交前的回调。
             * @param e
             * e.sender     kendoGrid对象，resultGrid
             * e.type       操作类型 create read update delete
             */
            beforeRequest: function (e) {

            }
        },
        "prodThirdCode": {
            pageable: false,
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                //双击选中前先把双击的数据勾选上
                prodThirdCodeGrid.unCheckAllRows();
                prodThirdCodeGrid.setCheckedRows(e.row);
                //关闭下拉框
                $("#prodThirdCode").data("kendoWindow").center().close();
            }
        }
    };



    /** 弹出框配置 */
    IPLATUI.EFWindow = {
        "prodThirdCode": {
            open: function (e) {
                prodThirdCodeGrid.removeRows(prodThirdCodeGrid.getDataItems());
            },
            close: function (e) {
                var row = prodThirdCodeGrid.getCheckedRows();
                if (row.length > 0) {
                    resultGrid.setCellValue(editorCell.model, "prodTypeId", row[0].prodTypeId);
                    resultGrid.setCellValue(editorCell.model, "prodTypeIdName", row[0].prodTypeDesc);
                }
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
                $("#QUERY").click();
            }
        }
    });
})
