.k-group-indicator .k-button span.k-i-group-delete {
	background-position: -90px -108px;
	margin-top: -4px;
	margin-left: 0px;
}

.k-group-indicator .k-button:hover span.k-i-group-delete{
	background-position: -90px -108px;
	margin-top: -4px;
}
/*箭头样式*/
.k-group-indicator .k-link span.k-i-sarrow-n {
	background-position: -162px -288px;
	/*margin-top: -7px;*/
}
.k-group-indicator .k-link:hover span.k-i-sarrow-n{
	background-position: -162px -288px;
}
/*另一个箭头样式*/
.k-group-indicator .k-link span.k-i-sarrow-s{
	background-position: -180px -288px;
	/*margin-top: -7px;*/
}
.k-group-indicator .k-link:hover span.k-i-sarrow-s{
	background-position:-180px -288px;
}

.i-theme-ant .k-i-collapse{
	background-position: 0px -180px;
	/*margin-top: -7px;*/
}
.i-theme-ant .k-i-expand{
	background-position: 0px -162px;
	/*margin-top: -7px;*/
}
.i-theme-ant .k-si-arrow-n{
	background-position: -126px -162px;
	/*margin-top: -7px;*/
}
.i-theme-ant .k-si-sarrow-s {
	background-position: -126px -144px;
	/*margin-top: -7px;*/
}
.k-i-sarrow-n, .k-si-arrow-n {
	background-position: -126px -162px;
}
.button_style{
	height: 24px;
	color: #FFF;
	border: 1px solid #3088F4;
	background: #3088F4;
	border-radius: 3px;
	position: relative;
	vertical-align: middle;
	box-sizing: border-box;
	margin-right: 10px;
	cursor: pointer;
}
.button_style1{
	height: 24px;
	color: #3088F4;
	border: 1px solid #dbdbdb;
	background: transparent;
	border-radius: 3px;
	position: relative;
	vertical-align: middle;
	box-sizing: border-box;
	margin-right: 10px;
	cursor: pointer;
}
.btn_save{
	margin-left:45%;
	margin-top: 15px;
}