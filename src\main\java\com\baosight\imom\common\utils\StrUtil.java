package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.StringUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 工程项目:.
 * 开发公司:Baosight Software LTD.co Copyright (c) 2022.
 * 类的简介:JSONUtil.
 * 类的描述:字符串常用方法工具类
 * 开发日期:2022/4/21、15:07.
 *
 * <AUTHOR>
 * @version 1.0 （开发版本）.
 * @since 1.8 （JDK版本号）.
 */
public final class StrUtil {

    /**
     * 此类不需要实例化
     */
    private StrUtil() {
    }

    /**
     * 判断一个字符串是否为空，null也会返回true
     *
     * @param str 需要判断的字符串
     * @return 是否为空，null也会返回true
     */
    public static boolean isBlank(String str) {
        return null == str || "".equals(str.trim()) || "null".equals(str.trim());
    }

    /**
     * 判断一个字符串是否不为空
     *
     * @param str 需要判断的字符串
     * @return 是否为空
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 判断一组字符串是否有空值
     *
     * @param strs 需要判断的一组字符串
     * @return 判断结果，只要其中一个字符串为null或者为空，就返回true
     */
    public static boolean hasBlank(String... strs) {
        if (null == strs || 0 == strs.length) {
            return true;
        } else {
            //这种代码如果用java8就会很优雅了
            for (String str : strs) {
                if (isBlank(str)) {
                    return true;
                }
            }
        }
        return false;
    }

	public static String reverse(String str,int length){
		StringBuffer sb=new StringBuffer(str);
		int a = sb.length();
		if(a<length){
			for(int i=0;i<length-a;i++){
				sb.append("0");
			}
		}

		sb=sb.reverse();

		return sb.toString();
	}

    public static String getUUID(){
        return UUID.randomUUID().toString().toUpperCase().replaceAll("-", "");
    }


    /*string准备被填充的字符串；
    padded_length填充之后的字符串长度，也就是该函数返回的字符串长度，
    如果这个数量比原字符串的长度要短，lpad函数将会把字符串截取成从左到右的n个字符;
    pad_string填充字符串，是个可选参数，这个字符串是要粘贴到string的左边，
    如果这个参数未写，lpad函数将会在string的左边粘贴空格*/
    public static String lpad(String str, int padded_length, String pad_string ){
        if(str.length()>=padded_length){
            return str;
        }else{
            String padStr = StringUtils.isNotEmpty(pad_string)?" ":pad_string;
            for(int i=0;i<padded_length - str.length();i++){
                str = padStr + str;
            }
        }

        return str;
    }


    public static String obj2Str(Object obj) {
    	if(obj==null) {
    		return "";
    	}
    	return obj.toString();
    }
    
    /** 
     * 全角转半角函数(DBC case)
     * @param value 
     * @return 
     */  
    public static final String ToDBC(String value) {  
        if(isBlank(value)){  
            return value;  
        }  
        char[] c = value.toCharArray();  
        for (int i = 0; i < c.length; i++) {  
            if (c[i] >= 65281 && c[i] <= 65374) {  
                c[i] = (char) (c[i] - 65248);  
            } else if (c[i] == 12288) { // 空格  
                c[i] = (char) 32;  
            }  
        }  
        return new String(c);  
    }  
  
    /** 
     * 半角转全角函数(SBC case)
     * @param value 
     * @return 
     */  
    public static final String ToSBC(String value) {  
        if(isBlank(value)){  
            return value;  
        }  
        char[] c = value.toCharArray();  
        for (int i = 0; i < c.length; i++) {  
            if (c[i] == 32) {  
                c[i] = (char) 12288;  
            } else if (c[i] < 127) {  
                c[i] = (char) (c[i] + 65248);  
            }  
        }  
        return new String(c);  
    }

    /**
     * 拼接多个字符串，以操作符分隔
     *
     * @param inList 传入的List ,param List里面要拼接的参数 ,operator 用什么符号拼接
     * @return java.util.String
     */
    public static String Splicing(List<HashMap> inList, String param, String operator) {
        String resultStr = "";
        if (!CollectionUtils.isEmpty(inList)) {
            //去重
            Map<Object, Boolean> distinctMap = new HashMap<>();
            List<HashMap> list=inList.stream().filter(i -> distinctMap.putIfAbsent(i.get(param), Boolean.TRUE) == null)
                    .collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            for (HashMap map : list) {
                sb.append(MapUtils.getString(map, param, "")).append(operator);
            }
            resultStr = sb.substring(0, sb.length() - 1);
        }
        return resultStr;
    }

    /**
     * 大写字母下划线转驼峰式
     *
     * @param name
     * @return java.util.String
     */
    public static String camelName(String name) {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty()) {
            // 没必要转换
            return "";
        } else if (!name.contains("_")) {
            // 不含下划线，仅将首字母小写
            return name.substring(0, 1).toLowerCase() + name.substring(1).toLowerCase();
        }
        // 用下划线将原始字符串分割
        String camels[] = name.split("_");
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 处理真正的驼峰片段
            if (result.length() == 0) {
                // 第一个驼峰片段，全部字母都小写
                result.append(camel.toLowerCase());
            } else {
                // 其他的驼峰片段，首字母大写
                result.append(camel.substring(0, 1).toUpperCase());
                result.append(camel.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    /**
     * 判断手机号
     *
     * @param tel
     * @return java.util.String
     */
    public static boolean telBoolean(String tel) {
        boolean matches = tel.matches("^1[3-9]\\d{9}$");//判断手机号
        return matches;
    }

    /**
     * 判断身份证
     *
     * @param Identity
     * @return java.util.String
     */
    public static boolean identityBoolean(String Identity) {
        boolean matches = Identity.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X|x)$");
        return matches;
    }

    /**
     * 判断身份证,手机号
     *
     * @param inInfo
     * @return java.util.EiInfo
     */
    public static EiInfo determineIDcardAndPhone(EiInfo inInfo, String tel, String adminIndtity) {
        //判断手机号
        if (isNotBlank(tel)){
            boolean b = StrUtil.telBoolean(tel);
            if (!b){
                String massage = "手机号不正确请重新输入!";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
        }
        if (isNotBlank(adminIndtity)){
            //判断身份证号
            boolean b1 = StrUtil.identityBoolean(adminIndtity);
            if (!b1){
                String massage = "身份证号不正确请重新输入!";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
        }
        return inInfo;
    }

    /**
     * 追加延期理由,格式为: yyyyMMdd|用户名|延期理由;
     *
     * @param originalRemark 原延期理由
     * @param delayRemark    新延期理由
     * @return 合并后的延期理由
     */
    public static String appendDelayRemark(String originalRemark, String delayRemark) {
        // 构建新的延期记录
        String newRemark = String.format("%s|%s|%s;",
                DateUtil.curDateTimeStr14().substring(0, 8),
                UserSession.getLoginCName(),
                delayRemark);
        // 如果原记录为空直接返回新记录,否则换行拼接
        return StrUtil.isBlank(originalRemark) ?
                newRemark :
                originalRemark + System.lineSeparator() + newRemark;
    }
}