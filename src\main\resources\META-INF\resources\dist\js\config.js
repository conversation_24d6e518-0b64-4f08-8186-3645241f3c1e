
var urlOrigin = window.location.origin;
var ytjServerUrl = `${urlOrigin}/mes/service`;
// 正式
if (urlOrigin.includes('imom-xb.baointl.info')) {
    ytjServerUrl = `${urlOrigin}/imom/service`
// } else if (urlOrigin.includes('test')) {
} else if (urlOrigin.includes('10.70.88.10') || urlOrigin.includes('test')) {
    ytjServerUrl = `${urlOrigin}/service`;
} else {
    ytjServerUrl = `${urlOrigin}/mes/service`;
}


var factoryId = localStorage.getItem("factoryId");
var factoryName = localStorage.getItem("factoryName");
var segNo = localStorage.getItem("segNo");
var key = localStorage.getItem("key");

document.addEventListener('DOMContentLoaded', function() {
    // 判断当前页面是否为主页
    const isHomePage = ['ahbgcnzxh', 'ytjindex', 'ahbgjrzxh', 'bulletinBoard', 'material-location-board', 'bulletinBoard-driver'].filter(s=> window.location.pathname.includes(s)); // 假设主页路径为根路径
    if (isHomePage.length == 0) {
        // 如果不是主页，启动计时器
        let inactivityTimer;
        const redirectTime = 180000; // 3分钟（毫秒）
        // const redirectTime = 5000;

        // 重置定时器函数
        function resetTimer() {
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(redirectPage, redirectTime);
            console.log(redirectTime);
        }

        // 跳转函数
        function redirectPage() {
            returnHome();
        }

        // 监听用户活动事件
        document.addEventListener('mousemove', resetTimer);
        document.addEventListener('keydown', resetTimer);

        // 初始化启动定时器
        resetTimer();
    }
});



function returnHome() {
    const reUrl = `ytjindex.html?segNo=${segNo}&factoryId=${factoryId}&factoryName=${factoryName}`;
    window.location.href =  key ? `${reUrl}&key=${key}` : reUrl; // 这里替换为您的主页URL
}


// 显示加载中的弹出框
function showLoading(title) {
    Swal.fire({
        title: `${title}...`,
        text: '请稍等片刻',
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

// 关闭加载中的弹出框
function closeLoading() {
    Swal.close();
}

