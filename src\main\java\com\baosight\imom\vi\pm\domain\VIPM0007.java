/**
* Generate time : 2024-12-02 10:17:38
* Version : 1.0
*/
package com.baosight.imom.vi.pm.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* VIPM0007
* table comment : 生产需求单产出表
*/
public class VIPM0007 extends DaoEPBase {

                public static final String QUERY = "VIPM0007.query";
                public static final String INSERT = "VIPM0007.insert";
                public static final String UPDATE = "VIPM0007.update";
                public static final String DELETE = "VIPM0007.delete";
                public static final String DELETE_ONE = "VIPM0007.deleteOne";
                private String unitCode = " ";		/* 业务单元代码*/
                private String processOrderId = " ";		/* 生产工单号*/
                private String processDemandId = " ";		/* 生产需求单号*/
                private String processDemandSubId = " ";		/* 生产需求单子项号*/
                private String partId = " ";		/* 物料号*/
                private String prodNameCode = " ";		/* 品名代码*/
                private String prodCname = " ";		/* 品名（中文）*/
                private String specsDesc = " ";		/* 规格描述*/
                private String shopsign = " ";		/* 牌号*/
                private Long productProcessQty = 0L;	/* 产出成品张数*/
                private BigDecimal productProcessWeight = new BigDecimal("0");		/* 产出成品重量*/
                private String customerId = " ";		/* 客户代码*/
                private String customerName = " ";		/* 客户名称*/
                private String orderNum = " ";		/* 销售订单子项号*/
                private String productDemandId = " ";		/* 成品需求单号*/
                private String lackDestinationId = " ";		/* 锁定（去向）单据号*/
                private String processDemandOutputStatus = " ";		/* 生产需求产出表状态*/
                private String remark = " ";		/* 备注*/
                private String contractNum = " ";		/* 销售订单号*/
                private BigDecimal stockUseWeight = new BigDecimal("0");		/* 自由在库库存占用重量*/
                private String stockUseQty = " ";		/* 自由在库库存占用数量*/
                private BigDecimal processUseWeight = new BigDecimal("0");		/* 在制占用重量*/
                private String processUseQty = " ";		/* 在制占用数量*/
                private String preDemandUseQty = " ";		/* 上道需求占用数量*/
                private BigDecimal preDemandUseWeight = new BigDecimal("0");		/* 上道需求占用重量*/
                private String quantityUnit = " ";		/* 数量单位*/
                private String weightUnit = " ";		/* 重量单位*/
                private BigDecimal p_output = new BigDecimal("0");		/* 成品配比*/
                private String suitVoucherNum = " ";		/* 套裁封锁单据号*/
                private String productRemainQty = "0";		/* 加工剩余数量*/
                private BigDecimal productRemainWeight = new BigDecimal("0");		/* 加工剩余重量*/
                private Integer indm = 0;		/* 内径*/
                private String packingTypeCode = " ";		/* 包装方式代码*/
                private String salesPersId = " ";		/* 营销员工号*/
                private String salesPersName = " ";		/* 营销员姓名*/
                private String processDemandQty = " ";		/* 生产需求数量*/
                private BigDecimal processDemandWeight = new BigDecimal("0");		/* 生产需求重量*/
                private String mproviderId = " ";		/* 加工单位代码*/
                private String mproviderName = " ";		/* 加工单位名称*/
                private String agreementId = " ";		/* 加工协议号*/
                private String agreementSubid = " ";		/* 加工协议子项号*/
                private String processFeePriceType = " ";		/* 加工费计价类型*/
                private BigDecimal processFeePrice = new BigDecimal("0");		/* 加工费单价(不含税)*/
                private BigDecimal processFeePriceTax = new BigDecimal("0");		/* 加工费单价(含税)*/
                private BigDecimal taxRate = new BigDecimal("0");		/* 税率*/
                private BigDecimal diameter = new BigDecimal("0");		/* 外径*/
                private String labelForm = " ";		/* 标签格式*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String tenantUser = " ";		/* 租户*/
                private String customerMaterialNumber = " ";		/* 客户材料代码*/
                private String processDemandOutputId = " ";		/* 生产需求单产出表序列号*/
                private String surfaceGrade = " ";		/* 表面等级*/
                private String segNo = " ";		/* 业务账套*/
                private String uuid = " ";		/* ID*/
                private String prodCode = " ";		/* 品种代码*/
                private String processDemandInputId = " ";		/* 投料单据号*/
                private String d_userNum = " ";		/* 分户号*/
                private String d_userName = " ";		/* 分户号名称*/
                private Long productionProcessedQuantity = 0L;	/* 生产已加工数量*/
                private BigDecimal productionProcessedWeight = new BigDecimal("0");		/* 生产已加工重量*/
                private Long processSinglePackQuantity = 0L;	/* 生产单包数量*/
                private BigDecimal processSinglePackWeight = new BigDecimal("0");		/* 生产单包重量*/
                private String estimatedPackagesNumber = " ";		/* 预计包数*/
                private Integer processBaseVersionNum = 0;		/* 加工基准书版本号*/
                private String weightMethod = " ";		/* 计重方式*/
                private String techStandard = " ";		/* 技术标准*/
                private String bstsCoilingOrd = " ";		/* 好面朝向*/
                private String qualityGrade = " ";		/* 质量等级*/
                private String finalFmSpec = " ";		/* 最终成品规格*/
                private String processBaseNo = " ";		/* 加工基准书号*/
                private String prodTypeId = " ";		/* 品种附属码*/
                private String orderTypeCode = " ";		/* 订单性质代码*/
                private Integer innerDim = 0;		/* 内径(mm)*/
                private String dirProcessDemandId = " ";		/* 去向生产需求单号*/
                private String dirProcessDemandSubId = " ";		/* 去向生产需求单子项*/
                private String preProcessDemandOutputSeqId = " ";		/* 上道产出表序列号*/
                private String prodTypeDesc = " ";		/* 品种附属码描述*/
                private String processIfSettle = " ";		/* 生产加工是否结算加工费*/
                private String processFeeCalculateMethod = " ";		/* 加工费计算方式*/
                private String excessStockFlag = " ";		/* 余料标记*/
                private String finishingShuntFlag = " ";		/* 精整分流标记*/
                private String contractPartId = " ";		/* 订单物料号*/
                private String metalBalanceFlag = " ";		/* 金属平衡标志*/
                private String manualNo = " ";		/* 手册编号*/
                private String hsId = " ";		/* 海关HS系统编码*/
                private String processConsignUnit = " ";		/* 加工委托方*/
                private String printOutputRemark = " ";		/* 打印产出物料信息备注*/
                private String finUserNum = " ";		/* 最终用户代码（订单用）*/
                private String finUserName = " ";		/* 最终用户名称*/
                private String handbookId = " ";		/* 手册系统编号*/
                private String customsProductNum = " ";		
                private String f_packId = " ";		/* 父捆包号*/
                private String f_matInnerId = " ";		/* 父捆包管理号*/
                private String nodeCode = " ";		/* 节点号*/
                private String demandRecNo = " ";		/* 需求识别卡号*/
                private Integer demandRecVersionNum = 0;		/* 需求识别卡版本号*/
                private String craftCode = " ";		/* 工艺单号*/
                private Integer craftVersionNum = 0;		/* 工艺单版本号*/
                private String processSeqCode = " ";		/* 加工序码*/
                private String tagendMgrType = " ";		/* 尾料处理方式*/
                private String tradeCode = " ";		/* 贸易方式*/
                private String custPartId = " ";		/* 客户零件号*/
                private String custPartName = " ";		/* 客户零件号名称*/
                private String bracketType = " ";		/* 料架类型*/
                private String ironBracketNo = " ";		/* 料架编号*/
                private BigDecimal maxCoilWt = new BigDecimal(0);		/* 最大卷重*/
                private String isReturnMaterialArea = " ";		/* 是否退回原料库*/
                private String printBatchId = " ";		/* 打印批次号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("unitCode");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandSubId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodNameCode");
        eiColumn.setFieldLength(7);
        eiColumn.setDescName("品名代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCname");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("品名（中文）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("规格描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productProcessQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("产出成品张数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productProcessWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("产出成品重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderNum");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("销售订单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productDemandId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("成品需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lackDestinationId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("锁定（去向）单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandOutputStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产需求产出表状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(2000);
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("contractNum");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("销售订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stockUseWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("自由在库库存占用重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stockUseQty");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("自由在库库存占用数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processUseWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("在制占用重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processUseQty");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("在制占用数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("preDemandUseQty");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("上道需求占用数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("preDemandUseWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("上道需求占用重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantityUnit");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("数量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weightUnit");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("重量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("p_output");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("成品配比");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("suitVoucherNum");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("套裁封锁单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productRemainQty");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工剩余数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productRemainWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工剩余重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("indm");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("内径");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingTypeCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("包装方式代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("salesPersId");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("营销员工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("salesPersName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("营销员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandQty");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("生产需求数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("生产需求重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mproviderId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mproviderName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("加工单位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("agreementId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工协议号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("agreementSubid");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("加工协议子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeePriceType");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("加工费计价类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeePrice");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工费单价(不含税)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeePriceTax");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工费单价(含税)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("taxRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("税率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("diameter");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("外径");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelForm");
        eiColumn.setFieldLength(3);
        eiColumn.setDescName("标签格式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerMaterialNumber");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户材料代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandOutputId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("生产需求单产出表序列号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("surfaceGrade");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("表面等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCode");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("品种代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandInputId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("投料单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNum");
        eiColumn.setFieldLength(11);
        eiColumn.setDescName("分户号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userName");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("分户号名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productionProcessedQuantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("生产已加工数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productionProcessedWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("生产已加工重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSinglePackQuantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("生产单包数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSinglePackWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("生产单包重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("estimatedPackagesNumber");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("预计包数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processBaseVersionNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("加工基准书版本号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weightMethod");
        eiColumn.setFieldLength(3);
        eiColumn.setDescName("计重方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("techStandard");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("技术标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bstsCoilingOrd");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("好面朝向");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityGrade");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("质量等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finalFmSpec");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("最终成品规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processBaseNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工基准书号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderTypeCode");
        eiColumn.setFieldLength(3);
        eiColumn.setDescName("订单性质代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("innerDim");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("内径(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dirProcessDemandId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("去向生产需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dirProcessDemandSubId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("去向生产需求单子项");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("preProcessDemandOutputSeqId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("上道产出表序列号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeDesc");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("品种附属码描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processIfSettle");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("生产加工是否结算加工费");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeeCalculateMethod");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("加工费计算方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("excessStockFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("余料标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finishingShuntFlag");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("精整分流标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("contractPartId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("订单物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("metalBalanceFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("金属平衡标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("manualNo");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("手册编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hsId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("海关HS系统编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processConsignUnit");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("加工委托方");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printOutputRemark");
        eiColumn.setFieldLength(2000);
        eiColumn.setDescName("打印产出物料信息备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserNum");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("最终用户代码（订单用）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("最终用户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("手册系统编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customsProductNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("父捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_matInnerId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("父捆包管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nodeCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("节点号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("demandRecNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("需求识别卡号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("demandRecVersionNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("需求识别卡版本号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craftCode");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("工艺单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craftVersionNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("工艺单版本号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSeqCode");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("加工序码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagendMgrType");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("尾料处理方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("贸易方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartId");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户零件号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartName");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("客户零件号名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bracketType");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("料架类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ironBracketNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("料架编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("maxCoilWt");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("最大卷重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isReturnMaterialArea");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("是否退回原料库");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printBatchId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("打印批次号");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public VIPM0007() {
initMetaData();
}

        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the processOrderId - 生产工单号
        * @return the processOrderId
        */
        public String getProcessOrderId() {
        return this.processOrderId;
        }

        /**
        * set the processOrderId - 生产工单号
        */
        public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
        }
        /**
        * get the processDemandId - 生产需求单号
        * @return the processDemandId
        */
        public String getProcessDemandId() {
        return this.processDemandId;
        }

        /**
        * set the processDemandId - 生产需求单号
        */
        public void setProcessDemandId(String processDemandId) {
        this.processDemandId = processDemandId;
        }
        /**
        * get the processDemandSubId - 生产需求单子项号
        * @return the processDemandSubId
        */
        public String getProcessDemandSubId() {
        return this.processDemandSubId;
        }

        /**
        * set the processDemandSubId - 生产需求单子项号
        */
        public void setProcessDemandSubId(String processDemandSubId) {
        this.processDemandSubId = processDemandSubId;
        }
        /**
        * get the partId - 物料号
        * @return the partId
        */
        public String getPartId() {
        return this.partId;
        }

        /**
        * set the partId - 物料号
        */
        public void setPartId(String partId) {
        this.partId = partId;
        }
        /**
        * get the prodNameCode - 品名代码
        * @return the prodNameCode
        */
        public String getProdNameCode() {
        return this.prodNameCode;
        }

        /**
        * set the prodNameCode - 品名代码
        */
        public void setProdNameCode(String prodNameCode) {
        this.prodNameCode = prodNameCode;
        }
        /**
        * get the prodCname - 品名（中文）
        * @return the prodCname
        */
        public String getProdCname() {
        return this.prodCname;
        }

        /**
        * set the prodCname - 品名（中文）
        */
        public void setProdCname(String prodCname) {
        this.prodCname = prodCname;
        }
        /**
        * get the specsDesc - 规格描述
        * @return the specsDesc
        */
        public String getSpecsDesc() {
        return this.specsDesc;
        }

        /**
        * set the specsDesc - 规格描述
        */
        public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
        }
        /**
        * get the shopsign - 牌号
        * @return the shopsign
        */
        public String getShopsign() {
        return this.shopsign;
        }

        /**
        * set the shopsign - 牌号
        */
        public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
        }
        /**
        * get the productProcessQty - 产出成品张数
        * @return the productProcessQty
        */
        public Long getProductProcessQty() {
        return this.productProcessQty;
        }

        /**
        * set the productProcessQty - 产出成品张数
        */
        public void setProductProcessQty(Long productProcessQty) {
        this.productProcessQty = productProcessQty;
        }
        /**
        * get the productProcessWeight - 产出成品重量
        * @return the productProcessWeight
        */
        public BigDecimal getProductProcessWeight() {
        return this.productProcessWeight;
        }

        /**
        * set the productProcessWeight - 产出成品重量
        */
        public void setProductProcessWeight(BigDecimal productProcessWeight) {
        this.productProcessWeight = productProcessWeight;
        }
        /**
        * get the customerId - 客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 客户名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 客户名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }
        /**
        * get the orderNum - 销售订单子项号
        * @return the orderNum
        */
        public String getOrderNum() {
        return this.orderNum;
        }

        /**
        * set the orderNum - 销售订单子项号
        */
        public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
        }
        /**
        * get the productDemandId - 成品需求单号
        * @return the productDemandId
        */
        public String getProductDemandId() {
        return this.productDemandId;
        }

        /**
        * set the productDemandId - 成品需求单号
        */
        public void setProductDemandId(String productDemandId) {
        this.productDemandId = productDemandId;
        }
        /**
        * get the lackDestinationId - 锁定（去向）单据号
        * @return the lackDestinationId
        */
        public String getLackDestinationId() {
        return this.lackDestinationId;
        }

        /**
        * set the lackDestinationId - 锁定（去向）单据号
        */
        public void setLackDestinationId(String lackDestinationId) {
        this.lackDestinationId = lackDestinationId;
        }
        /**
        * get the processDemandOutputStatus - 生产需求产出表状态
        * @return the processDemandOutputStatus
        */
        public String getProcessDemandOutputStatus() {
        return this.processDemandOutputStatus;
        }

        /**
        * set the processDemandOutputStatus - 生产需求产出表状态
        */
        public void setProcessDemandOutputStatus(String processDemandOutputStatus) {
        this.processDemandOutputStatus = processDemandOutputStatus;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the contractNum - 销售订单号
        * @return the contractNum
        */
        public String getContractNum() {
        return this.contractNum;
        }

        /**
        * set the contractNum - 销售订单号
        */
        public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
        }
        /**
        * get the stockUseWeight - 自由在库库存占用重量
        * @return the stockUseWeight
        */
        public BigDecimal getStockUseWeight() {
        return this.stockUseWeight;
        }

        /**
        * set the stockUseWeight - 自由在库库存占用重量
        */
        public void setStockUseWeight(BigDecimal stockUseWeight) {
        this.stockUseWeight = stockUseWeight;
        }
        /**
        * get the stockUseQty - 自由在库库存占用数量
        * @return the stockUseQty
        */
        public String getStockUseQty() {
        return this.stockUseQty;
        }

        /**
        * set the stockUseQty - 自由在库库存占用数量
        */
        public void setStockUseQty(String stockUseQty) {
        this.stockUseQty = stockUseQty;
        }
        /**
        * get the processUseWeight - 在制占用重量
        * @return the processUseWeight
        */
        public BigDecimal getProcessUseWeight() {
        return this.processUseWeight;
        }

        /**
        * set the processUseWeight - 在制占用重量
        */
        public void setProcessUseWeight(BigDecimal processUseWeight) {
        this.processUseWeight = processUseWeight;
        }
        /**
        * get the processUseQty - 在制占用数量
        * @return the processUseQty
        */
        public String getProcessUseQty() {
        return this.processUseQty;
        }

        /**
        * set the processUseQty - 在制占用数量
        */
        public void setProcessUseQty(String processUseQty) {
        this.processUseQty = processUseQty;
        }
        /**
        * get the preDemandUseQty - 上道需求占用数量
        * @return the preDemandUseQty
        */
        public String getPreDemandUseQty() {
        return this.preDemandUseQty;
        }

        /**
        * set the preDemandUseQty - 上道需求占用数量
        */
        public void setPreDemandUseQty(String preDemandUseQty) {
        this.preDemandUseQty = preDemandUseQty;
        }
        /**
        * get the preDemandUseWeight - 上道需求占用重量
        * @return the preDemandUseWeight
        */
        public BigDecimal getPreDemandUseWeight() {
        return this.preDemandUseWeight;
        }

        /**
        * set the preDemandUseWeight - 上道需求占用重量
        */
        public void setPreDemandUseWeight(BigDecimal preDemandUseWeight) {
        this.preDemandUseWeight = preDemandUseWeight;
        }
        /**
        * get the quantityUnit - 数量单位
        * @return the quantityUnit
        */
        public String getQuantityUnit() {
        return this.quantityUnit;
        }

        /**
        * set the quantityUnit - 数量单位
        */
        public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
        }
        /**
        * get the weightUnit - 重量单位
        * @return the weightUnit
        */
        public String getWeightUnit() {
        return this.weightUnit;
        }

        /**
        * set the weightUnit - 重量单位
        */
        public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
        }
        /**
        * get the p_output - 成品配比
        * @return the p_output
        */
        public BigDecimal getP_output() {
        return this.p_output;
        }

        /**
        * set the p_output - 成品配比
        */
        public void setP_output(BigDecimal p_output) {
        this.p_output = p_output;
        }
        /**
        * get the suitVoucherNum - 套裁封锁单据号
        * @return the suitVoucherNum
        */
        public String getSuitVoucherNum() {
        return this.suitVoucherNum;
        }

        /**
        * set the suitVoucherNum - 套裁封锁单据号
        */
        public void setSuitVoucherNum(String suitVoucherNum) {
        this.suitVoucherNum = suitVoucherNum;
        }
        /**
        * get the productRemainQty - 加工剩余数量
        * @return the productRemainQty
        */
        public String getProductRemainQty() {
        return this.productRemainQty;
        }

        /**
        * set the productRemainQty - 加工剩余数量
        */
        public void setProductRemainQty(String productRemainQty) {
        this.productRemainQty = productRemainQty;
        }
        /**
        * get the productRemainWeight - 加工剩余重量
        * @return the productRemainWeight
        */
        public BigDecimal getProductRemainWeight() {
        return this.productRemainWeight;
        }

        /**
        * set the productRemainWeight - 加工剩余重量
        */
        public void setProductRemainWeight(BigDecimal productRemainWeight) {
        this.productRemainWeight = productRemainWeight;
        }
        /**
        * get the indm - 内径
        * @return the indm
        */
        public Integer getIndm() {
        return this.indm;
        }

        /**
        * set the indm - 内径
        */
        public void setIndm(Integer indm) {
        this.indm = indm;
        }
        /**
        * get the packingTypeCode - 包装方式代码
        * @return the packingTypeCode
        */
        public String getPackingTypeCode() {
        return this.packingTypeCode;
        }

        /**
        * set the packingTypeCode - 包装方式代码
        */
        public void setPackingTypeCode(String packingTypeCode) {
        this.packingTypeCode = packingTypeCode;
        }
        /**
        * get the salesPersId - 营销员工号
        * @return the salesPersId
        */
        public String getSalesPersId() {
        return this.salesPersId;
        }

        /**
        * set the salesPersId - 营销员工号
        */
        public void setSalesPersId(String salesPersId) {
        this.salesPersId = salesPersId;
        }
        /**
        * get the salesPersName - 营销员姓名
        * @return the salesPersName
        */
        public String getSalesPersName() {
        return this.salesPersName;
        }

        /**
        * set the salesPersName - 营销员姓名
        */
        public void setSalesPersName(String salesPersName) {
        this.salesPersName = salesPersName;
        }
        /**
        * get the processDemandQty - 生产需求数量
        * @return the processDemandQty
        */
        public String getProcessDemandQty() {
        return this.processDemandQty;
        }

        /**
        * set the processDemandQty - 生产需求数量
        */
        public void setProcessDemandQty(String processDemandQty) {
        this.processDemandQty = processDemandQty;
        }
        /**
        * get the processDemandWeight - 生产需求重量
        * @return the processDemandWeight
        */
        public BigDecimal getProcessDemandWeight() {
        return this.processDemandWeight;
        }

        /**
        * set the processDemandWeight - 生产需求重量
        */
        public void setProcessDemandWeight(BigDecimal processDemandWeight) {
        this.processDemandWeight = processDemandWeight;
        }
        /**
        * get the mproviderId - 加工单位代码
        * @return the mproviderId
        */
        public String getMproviderId() {
        return this.mproviderId;
        }

        /**
        * set the mproviderId - 加工单位代码
        */
        public void setMproviderId(String mproviderId) {
        this.mproviderId = mproviderId;
        }
        /**
        * get the mproviderName - 加工单位名称
        * @return the mproviderName
        */
        public String getMproviderName() {
        return this.mproviderName;
        }

        /**
        * set the mproviderName - 加工单位名称
        */
        public void setMproviderName(String mproviderName) {
        this.mproviderName = mproviderName;
        }
        /**
        * get the agreementId - 加工协议号
        * @return the agreementId
        */
        public String getAgreementId() {
        return this.agreementId;
        }

        /**
        * set the agreementId - 加工协议号
        */
        public void setAgreementId(String agreementId) {
        this.agreementId = agreementId;
        }
        /**
        * get the agreementSubid - 加工协议子项号
        * @return the agreementSubid
        */
        public String getAgreementSubid() {
        return this.agreementSubid;
        }

        /**
        * set the agreementSubid - 加工协议子项号
        */
        public void setAgreementSubid(String agreementSubid) {
        this.agreementSubid = agreementSubid;
        }
        /**
        * get the processFeePriceType - 加工费计价类型
        * @return the processFeePriceType
        */
        public String getProcessFeePriceType() {
        return this.processFeePriceType;
        }

        /**
        * set the processFeePriceType - 加工费计价类型
        */
        public void setProcessFeePriceType(String processFeePriceType) {
        this.processFeePriceType = processFeePriceType;
        }
        /**
        * get the processFeePrice - 加工费单价(不含税)
        * @return the processFeePrice
        */
        public BigDecimal getProcessFeePrice() {
        return this.processFeePrice;
        }

        /**
        * set the processFeePrice - 加工费单价(不含税)
        */
        public void setProcessFeePrice(BigDecimal processFeePrice) {
        this.processFeePrice = processFeePrice;
        }
        /**
        * get the processFeePriceTax - 加工费单价(含税)
        * @return the processFeePriceTax
        */
        public BigDecimal getProcessFeePriceTax() {
        return this.processFeePriceTax;
        }

        /**
        * set the processFeePriceTax - 加工费单价(含税)
        */
        public void setProcessFeePriceTax(BigDecimal processFeePriceTax) {
        this.processFeePriceTax = processFeePriceTax;
        }
        /**
        * get the taxRate - 税率
        * @return the taxRate
        */
        public BigDecimal getTaxRate() {
        return this.taxRate;
        }

        /**
        * set the taxRate - 税率
        */
        public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
        }
        /**
        * get the diameter - 外径
        * @return the diameter
        */
        public BigDecimal getDiameter() {
        return this.diameter;
        }

        /**
        * set the diameter - 外径
        */
        public void setDiameter(BigDecimal diameter) {
        this.diameter = diameter;
        }
        /**
        * get the labelForm - 标签格式
        * @return the labelForm
        */
        public String getLabelForm() {
        return this.labelForm;
        }

        /**
        * set the labelForm - 标签格式
        */
        public void setLabelForm(String labelForm) {
        this.labelForm = labelForm;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the customerMaterialNumber - 客户材料代码
        * @return the customerMaterialNumber
        */
        public String getCustomerMaterialNumber() {
        return this.customerMaterialNumber;
        }

        /**
        * set the customerMaterialNumber - 客户材料代码
        */
        public void setCustomerMaterialNumber(String customerMaterialNumber) {
        this.customerMaterialNumber = customerMaterialNumber;
        }
        /**
        * get the processDemandOutputId - 生产需求单产出表序列号
        * @return the processDemandOutputId
        */
        public String getProcessDemandOutputId() {
        return this.processDemandOutputId;
        }

        /**
        * set the processDemandOutputId - 生产需求单产出表序列号
        */
        public void setProcessDemandOutputId(String processDemandOutputId) {
        this.processDemandOutputId = processDemandOutputId;
        }
        /**
        * get the surfaceGrade - 表面等级
        * @return the surfaceGrade
        */
        public String getSurfaceGrade() {
        return this.surfaceGrade;
        }

        /**
        * set the surfaceGrade - 表面等级
        */
        public void setSurfaceGrade(String surfaceGrade) {
        this.surfaceGrade = surfaceGrade;
        }
        /**
        * get the segNo - 业务账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the prodCode - 品种代码
        * @return the prodCode
        */
        public String getProdCode() {
        return this.prodCode;
        }

        /**
        * set the prodCode - 品种代码
        */
        public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
        }
        /**
        * get the processDemandInputId - 投料单据号
        * @return the processDemandInputId
        */
        public String getProcessDemandInputId() {
        return this.processDemandInputId;
        }

        /**
        * set the processDemandInputId - 投料单据号
        */
        public void setProcessDemandInputId(String processDemandInputId) {
        this.processDemandInputId = processDemandInputId;
        }
        /**
        * get the d_userNum - 分户号
        * @return the d_userNum
        */
        public String getD_userNum() {
        return this.d_userNum;
        }

        /**
        * set the d_userNum - 分户号
        */
        public void setD_userNum(String d_userNum) {
        this.d_userNum = d_userNum;
        }
        /**
        * get the d_userName - 分户号名称
        * @return the d_userName
        */
        public String getD_userName() {
        return this.d_userName;
        }

        /**
        * set the d_userName - 分户号名称
        */
        public void setD_userName(String d_userName) {
        this.d_userName = d_userName;
        }
        /**
        * get the productionProcessedQuantity - 生产已加工数量
        * @return the productionProcessedQuantity
        */
        public Long getProductionProcessedQuantity() {
        return this.productionProcessedQuantity;
        }

        /**
        * set the productionProcessedQuantity - 生产已加工数量
        */
        public void setProductionProcessedQuantity(Long productionProcessedQuantity) {
        this.productionProcessedQuantity = productionProcessedQuantity;
        }
        /**
        * get the productionProcessedWeight - 生产已加工重量
        * @return the productionProcessedWeight
        */
        public BigDecimal getProductionProcessedWeight() {
        return this.productionProcessedWeight;
        }

        /**
        * set the productionProcessedWeight - 生产已加工重量
        */
        public void setProductionProcessedWeight(BigDecimal productionProcessedWeight) {
        this.productionProcessedWeight = productionProcessedWeight;
        }
        /**
        * get the processSinglePackQuantity - 生产单包数量
        * @return the processSinglePackQuantity
        */
        public Long getProcessSinglePackQuantity() {
        return this.processSinglePackQuantity;
        }

        /**
        * set the processSinglePackQuantity - 生产单包数量
        */
        public void setProcessSinglePackQuantity(Long processSinglePackQuantity) {
        this.processSinglePackQuantity = processSinglePackQuantity;
        }
        /**
        * get the processSinglePackWeight - 生产单包重量
        * @return the processSinglePackWeight
        */
        public BigDecimal getProcessSinglePackWeight() {
        return this.processSinglePackWeight;
        }

        /**
        * set the processSinglePackWeight - 生产单包重量
        */
        public void setProcessSinglePackWeight(BigDecimal processSinglePackWeight) {
        this.processSinglePackWeight = processSinglePackWeight;
        }
        /**
        * get the estimatedPackagesNumber - 预计包数
        * @return the estimatedPackagesNumber
        */
        public String getEstimatedPackagesNumber() {
        return this.estimatedPackagesNumber;
        }

        /**
        * set the estimatedPackagesNumber - 预计包数
        */
        public void setEstimatedPackagesNumber(String estimatedPackagesNumber) {
        this.estimatedPackagesNumber = estimatedPackagesNumber;
        }
        /**
        * get the processBaseVersionNum - 加工基准书版本号
        * @return the processBaseVersionNum
        */
        public Integer getProcessBaseVersionNum() {
        return this.processBaseVersionNum;
        }

        /**
        * set the processBaseVersionNum - 加工基准书版本号
        */
        public void setProcessBaseVersionNum(Integer processBaseVersionNum) {
        this.processBaseVersionNum = processBaseVersionNum;
        }
        /**
        * get the weightMethod - 计重方式
        * @return the weightMethod
        */
        public String getWeightMethod() {
        return this.weightMethod;
        }

        /**
        * set the weightMethod - 计重方式
        */
        public void setWeightMethod(String weightMethod) {
        this.weightMethod = weightMethod;
        }
        /**
        * get the techStandard - 技术标准
        * @return the techStandard
        */
        public String getTechStandard() {
        return this.techStandard;
        }

        /**
        * set the techStandard - 技术标准
        */
        public void setTechStandard(String techStandard) {
        this.techStandard = techStandard;
        }
        /**
        * get the bstsCoilingOrd - 好面朝向
        * @return the bstsCoilingOrd
        */
        public String getBstsCoilingOrd() {
        return this.bstsCoilingOrd;
        }

        /**
        * set the bstsCoilingOrd - 好面朝向
        */
        public void setBstsCoilingOrd(String bstsCoilingOrd) {
        this.bstsCoilingOrd = bstsCoilingOrd;
        }
        /**
        * get the qualityGrade - 质量等级
        * @return the qualityGrade
        */
        public String getQualityGrade() {
        return this.qualityGrade;
        }

        /**
        * set the qualityGrade - 质量等级
        */
        public void setQualityGrade(String qualityGrade) {
        this.qualityGrade = qualityGrade;
        }
        /**
        * get the finalFmSpec - 最终成品规格
        * @return the finalFmSpec
        */
        public String getFinalFmSpec() {
        return this.finalFmSpec;
        }

        /**
        * set the finalFmSpec - 最终成品规格
        */
        public void setFinalFmSpec(String finalFmSpec) {
        this.finalFmSpec = finalFmSpec;
        }
        /**
        * get the processBaseNo - 加工基准书号
        * @return the processBaseNo
        */
        public String getProcessBaseNo() {
        return this.processBaseNo;
        }

        /**
        * set the processBaseNo - 加工基准书号
        */
        public void setProcessBaseNo(String processBaseNo) {
        this.processBaseNo = processBaseNo;
        }
        /**
        * get the prodTypeId - 品种附属码
        * @return the prodTypeId
        */
        public String getProdTypeId() {
        return this.prodTypeId;
        }

        /**
        * set the prodTypeId - 品种附属码
        */
        public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
        }
        /**
        * get the orderTypeCode - 订单性质代码
        * @return the orderTypeCode
        */
        public String getOrderTypeCode() {
        return this.orderTypeCode;
        }

        /**
        * set the orderTypeCode - 订单性质代码
        */
        public void setOrderTypeCode(String orderTypeCode) {
        this.orderTypeCode = orderTypeCode;
        }
        /**
        * get the innerDim - 内径(mm)
        * @return the innerDim
        */
        public Integer getInnerDim() {
        return this.innerDim;
        }

        /**
        * set the innerDim - 内径(mm)
        */
        public void setInnerDim(Integer innerDim) {
        this.innerDim = innerDim;
        }
        /**
        * get the dirProcessDemandId - 去向生产需求单号
        * @return the dirProcessDemandId
        */
        public String getDirProcessDemandId() {
        return this.dirProcessDemandId;
        }

        /**
        * set the dirProcessDemandId - 去向生产需求单号
        */
        public void setDirProcessDemandId(String dirProcessDemandId) {
        this.dirProcessDemandId = dirProcessDemandId;
        }
        /**
        * get the dirProcessDemandSubId - 去向生产需求单子项
        * @return the dirProcessDemandSubId
        */
        public String getDirProcessDemandSubId() {
        return this.dirProcessDemandSubId;
        }

        /**
        * set the dirProcessDemandSubId - 去向生产需求单子项
        */
        public void setDirProcessDemandSubId(String dirProcessDemandSubId) {
        this.dirProcessDemandSubId = dirProcessDemandSubId;
        }
        /**
        * get the preProcessDemandOutputSeqId - 上道产出表序列号
        * @return the preProcessDemandOutputSeqId
        */
        public String getPreProcessDemandOutputSeqId() {
        return this.preProcessDemandOutputSeqId;
        }

        /**
        * set the preProcessDemandOutputSeqId - 上道产出表序列号
        */
        public void setPreProcessDemandOutputSeqId(String preProcessDemandOutputSeqId) {
        this.preProcessDemandOutputSeqId = preProcessDemandOutputSeqId;
        }
        /**
        * get the prodTypeDesc - 品种附属码描述
        * @return the prodTypeDesc
        */
        public String getProdTypeDesc() {
        return this.prodTypeDesc;
        }

        /**
        * set the prodTypeDesc - 品种附属码描述
        */
        public void setProdTypeDesc(String prodTypeDesc) {
        this.prodTypeDesc = prodTypeDesc;
        }
        /**
        * get the processIfSettle - 生产加工是否结算加工费
        * @return the processIfSettle
        */
        public String getProcessIfSettle() {
        return this.processIfSettle;
        }

        /**
        * set the processIfSettle - 生产加工是否结算加工费
        */
        public void setProcessIfSettle(String processIfSettle) {
        this.processIfSettle = processIfSettle;
        }
        /**
        * get the processFeeCalculateMethod - 加工费计算方式
        * @return the processFeeCalculateMethod
        */
        public String getProcessFeeCalculateMethod() {
        return this.processFeeCalculateMethod;
        }

        /**
        * set the processFeeCalculateMethod - 加工费计算方式
        */
        public void setProcessFeeCalculateMethod(String processFeeCalculateMethod) {
        this.processFeeCalculateMethod = processFeeCalculateMethod;
        }
        /**
        * get the excessStockFlag - 余料标记
        * @return the excessStockFlag
        */
        public String getExcessStockFlag() {
        return this.excessStockFlag;
        }

        /**
        * set the excessStockFlag - 余料标记
        */
        public void setExcessStockFlag(String excessStockFlag) {
        this.excessStockFlag = excessStockFlag;
        }
        /**
        * get the finishingShuntFlag - 精整分流标记
        * @return the finishingShuntFlag
        */
        public String getFinishingShuntFlag() {
        return this.finishingShuntFlag;
        }

        /**
        * set the finishingShuntFlag - 精整分流标记
        */
        public void setFinishingShuntFlag(String finishingShuntFlag) {
        this.finishingShuntFlag = finishingShuntFlag;
        }
        /**
        * get the contractPartId - 订单物料号
        * @return the contractPartId
        */
        public String getContractPartId() {
        return this.contractPartId;
        }

        /**
        * set the contractPartId - 订单物料号
        */
        public void setContractPartId(String contractPartId) {
        this.contractPartId = contractPartId;
        }
        /**
        * get the metalBalanceFlag - 金属平衡标志
        * @return the metalBalanceFlag
        */
        public String getMetalBalanceFlag() {
        return this.metalBalanceFlag;
        }

        /**
        * set the metalBalanceFlag - 金属平衡标志
        */
        public void setMetalBalanceFlag(String metalBalanceFlag) {
        this.metalBalanceFlag = metalBalanceFlag;
        }
        /**
        * get the manualNo - 手册编号
        * @return the manualNo
        */
        public String getManualNo() {
        return this.manualNo;
        }

        /**
        * set the manualNo - 手册编号
        */
        public void setManualNo(String manualNo) {
        this.manualNo = manualNo;
        }
        /**
        * get the hsId - 海关HS系统编码
        * @return the hsId
        */
        public String getHsId() {
        return this.hsId;
        }

        /**
        * set the hsId - 海关HS系统编码
        */
        public void setHsId(String hsId) {
        this.hsId = hsId;
        }
        /**
        * get the processConsignUnit - 加工委托方
        * @return the processConsignUnit
        */
        public String getProcessConsignUnit() {
        return this.processConsignUnit;
        }

        /**
        * set the processConsignUnit - 加工委托方
        */
        public void setProcessConsignUnit(String processConsignUnit) {
        this.processConsignUnit = processConsignUnit;
        }
        /**
        * get the printOutputRemark - 打印产出物料信息备注
        * @return the printOutputRemark
        */
        public String getPrintOutputRemark() {
        return this.printOutputRemark;
        }

        /**
        * set the printOutputRemark - 打印产出物料信息备注
        */
        public void setPrintOutputRemark(String printOutputRemark) {
        this.printOutputRemark = printOutputRemark;
        }
        /**
        * get the finUserNum - 最终用户代码（订单用）
        * @return the finUserNum
        */
        public String getFinUserNum() {
        return this.finUserNum;
        }

        /**
        * set the finUserNum - 最终用户代码（订单用）
        */
        public void setFinUserNum(String finUserNum) {
        this.finUserNum = finUserNum;
        }
        /**
        * get the finUserName - 最终用户名称
        * @return the finUserName
        */
        public String getFinUserName() {
        return this.finUserName;
        }

        /**
        * set the finUserName - 最终用户名称
        */
        public void setFinUserName(String finUserName) {
        this.finUserName = finUserName;
        }
        /**
        * get the handbookId - 手册系统编号
        * @return the handbookId
        */
        public String getHandbookId() {
        return this.handbookId;
        }

        /**
        * set the handbookId - 手册系统编号
        */
        public void setHandbookId(String handbookId) {
        this.handbookId = handbookId;
        }
        /**
        * get the customsProductNum 
        * @return the customsProductNum
        */
        public String getCustomsProductNum() {
        return this.customsProductNum;
        }

        /**
        * set the customsProductNum 
        */
        public void setCustomsProductNum(String customsProductNum) {
        this.customsProductNum = customsProductNum;
        }
        /**
        * get the f_packId - 父捆包号
        * @return the f_packId
        */
        public String getF_packId() {
        return this.f_packId;
        }

        /**
        * set the f_packId - 父捆包号
        */
        public void setF_packId(String f_packId) {
        this.f_packId = f_packId;
        }
        /**
        * get the f_matInnerId - 父捆包管理号
        * @return the f_matInnerId
        */
        public String getF_matInnerId() {
        return this.f_matInnerId;
        }

        /**
        * set the f_matInnerId - 父捆包管理号
        */
        public void setF_matInnerId(String f_matInnerId) {
        this.f_matInnerId = f_matInnerId;
        }
        /**
        * get the nodeCode - 节点号
        * @return the nodeCode
        */
        public String getNodeCode() {
        return this.nodeCode;
        }

        /**
        * set the nodeCode - 节点号
        */
        public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        }
        /**
        * get the demandRecNo - 需求识别卡号
        * @return the demandRecNo
        */
        public String getDemandRecNo() {
        return this.demandRecNo;
        }

        /**
        * set the demandRecNo - 需求识别卡号
        */
        public void setDemandRecNo(String demandRecNo) {
        this.demandRecNo = demandRecNo;
        }
        /**
        * get the demandRecVersionNum - 需求识别卡版本号
        * @return the demandRecVersionNum
        */
        public Integer getDemandRecVersionNum() {
        return this.demandRecVersionNum;
        }

        /**
        * set the demandRecVersionNum - 需求识别卡版本号
        */
        public void setDemandRecVersionNum(Integer demandRecVersionNum) {
        this.demandRecVersionNum = demandRecVersionNum;
        }
        /**
        * get the craftCode - 工艺单号
        * @return the craftCode
        */
        public String getCraftCode() {
        return this.craftCode;
        }

        /**
        * set the craftCode - 工艺单号
        */
        public void setCraftCode(String craftCode) {
        this.craftCode = craftCode;
        }
        /**
        * get the craftVersionNum - 工艺单版本号
        * @return the craftVersionNum
        */
        public Integer getCraftVersionNum() {
        return this.craftVersionNum;
        }

        /**
        * set the craftVersionNum - 工艺单版本号
        */
        public void setCraftVersionNum(Integer craftVersionNum) {
        this.craftVersionNum = craftVersionNum;
        }
        /**
        * get the processSeqCode - 加工序码
        * @return the processSeqCode
        */
        public String getProcessSeqCode() {
        return this.processSeqCode;
        }

        /**
        * set the processSeqCode - 加工序码
        */
        public void setProcessSeqCode(String processSeqCode) {
        this.processSeqCode = processSeqCode;
        }
        /**
        * get the tagendMgrType - 尾料处理方式
        * @return the tagendMgrType
        */
        public String getTagendMgrType() {
        return this.tagendMgrType;
        }

        /**
        * set the tagendMgrType - 尾料处理方式
        */
        public void setTagendMgrType(String tagendMgrType) {
        this.tagendMgrType = tagendMgrType;
        }
        /**
        * get the tradeCode - 贸易方式
        * @return the tradeCode
        */
        public String getTradeCode() {
        return this.tradeCode;
        }

        /**
        * set the tradeCode - 贸易方式
        */
        public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
        }
        /**
        * get the custPartId - 客户零件号
        * @return the custPartId
        */
        public String getCustPartId() {
        return this.custPartId;
        }

        /**
        * set the custPartId - 客户零件号
        */
        public void setCustPartId(String custPartId) {
        this.custPartId = custPartId;
        }
        /**
        * get the custPartName - 客户零件号名称
        * @return the custPartName
        */
        public String getCustPartName() {
        return this.custPartName;
        }

        /**
        * set the custPartName - 客户零件号名称
        */
        public void setCustPartName(String custPartName) {
        this.custPartName = custPartName;
        }
        /**
        * get the bracketType - 料架类型
        * @return the bracketType
        */
        public String getBracketType() {
        return this.bracketType;
        }

        /**
        * set the bracketType - 料架类型
        */
        public void setBracketType(String bracketType) {
        this.bracketType = bracketType;
        }
        /**
        * get the ironBracketNo - 料架编号
        * @return the ironBracketNo
        */
        public String getIronBracketNo() {
        return this.ironBracketNo;
        }

        /**
        * set the ironBracketNo - 料架编号
        */
        public void setIronBracketNo(String ironBracketNo) {
        this.ironBracketNo = ironBracketNo;
        }
        /**
        * get the maxCoilWt - 最大卷重
        * @return the maxCoilWt
        */
        public BigDecimal getMaxCoilWt() {
        return this.maxCoilWt;
        }

        /**
        * set the maxCoilWt - 最大卷重
        */
        public void setMaxCoilWt(BigDecimal maxCoilWt) {
        this.maxCoilWt = maxCoilWt;
        }
        /**
        * get the isReturnMaterialArea - 是否退回原料库
        * @return the isReturnMaterialArea
        */
        public String getIsReturnMaterialArea() {
        return this.isReturnMaterialArea;
        }

        /**
        * set the isReturnMaterialArea - 是否退回原料库
        */
        public void setIsReturnMaterialArea(String isReturnMaterialArea) {
        this.isReturnMaterialArea = isReturnMaterialArea;
        }
        /**
        * get the printBatchId - 打印批次号
        * @return the printBatchId
        */
        public String getPrintBatchId() {
        return this.printBatchId;
        }

        /**
        * set the printBatchId - 打印批次号
        */
        public void setPrintBatchId(String printBatchId) {
        this.printBatchId = printBatchId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
                setProcessDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandId")), processDemandId));
                setProcessDemandSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandSubId")), processDemandSubId));
                setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
                setProdNameCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodNameCode")), prodNameCode));
                setProdCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCname")), prodCname));
                setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
                setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
                setProductProcessQty(NumberUtils.toLong(StringUtils.toString(map.get("productProcessQty")), productProcessQty));
                setProductProcessWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productProcessWeight")), productProcessWeight));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderNum")), orderNum));
                setProductDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productDemandId")), productDemandId));
                setLackDestinationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lackDestinationId")), lackDestinationId));
                setProcessDemandOutputStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandOutputStatus")), processDemandOutputStatus));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setContractNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("contractNum")), contractNum));
                setStockUseWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("stockUseWeight")), stockUseWeight));
                setStockUseQty(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stockUseQty")), stockUseQty));
                setProcessUseWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processUseWeight")), processUseWeight));
                setProcessUseQty(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processUseQty")), processUseQty));
                setPreDemandUseQty(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("preDemandUseQty")), preDemandUseQty));
                setPreDemandUseWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("preDemandUseWeight")), preDemandUseWeight));
                setQuantityUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("quantityUnit")), quantityUnit));
                setWeightUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("weightUnit")), weightUnit));
                setP_output(NumberUtils.toBigDecimal(StringUtils.toString(map.get("p_output")), p_output));
                setSuitVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("suitVoucherNum")), suitVoucherNum));
                setProductRemainQty(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productRemainQty")), productRemainQty));
                setProductRemainWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productRemainWeight")), productRemainWeight));
                setIndm(NumberUtils.toInteger(StringUtils.toString(map.get("indm")), indm));
                setPackingTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingTypeCode")), packingTypeCode));
                setSalesPersId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("salesPersId")), salesPersId));
                setSalesPersName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("salesPersName")), salesPersName));
                setProcessDemandQty(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandQty")), processDemandQty));
                setProcessDemandWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processDemandWeight")), processDemandWeight));
                setMproviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mproviderId")), mproviderId));
                setMproviderName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mproviderName")), mproviderName));
                setAgreementId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("agreementId")), agreementId));
                setAgreementSubid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("agreementSubid")), agreementSubid));
                setProcessFeePriceType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFeePriceType")), processFeePriceType));
                setProcessFeePrice(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processFeePrice")), processFeePrice));
                setProcessFeePriceTax(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processFeePriceTax")), processFeePriceTax));
                setTaxRate(NumberUtils.toBigDecimal(StringUtils.toString(map.get("taxRate")), taxRate));
                setDiameter(NumberUtils.toBigDecimal(StringUtils.toString(map.get("diameter")), diameter));
                setLabelForm(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelForm")), labelForm));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setCustomerMaterialNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerMaterialNumber")), customerMaterialNumber));
                setProcessDemandOutputId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandOutputId")), processDemandOutputId));
                setSurfaceGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("surfaceGrade")), surfaceGrade));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setProdCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCode")), prodCode));
                setProcessDemandInputId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandInputId")), processDemandInputId));
                setD_userNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNum")), d_userNum));
                setD_userName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userName")), d_userName));
                setProductionProcessedQuantity(NumberUtils.toLong(StringUtils.toString(map.get("productionProcessedQuantity")), productionProcessedQuantity));
                setProductionProcessedWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productionProcessedWeight")), productionProcessedWeight));
                setProcessSinglePackQuantity(NumberUtils.toLong(StringUtils.toString(map.get("processSinglePackQuantity")), processSinglePackQuantity));
                setProcessSinglePackWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processSinglePackWeight")), processSinglePackWeight));
                setEstimatedPackagesNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("estimatedPackagesNumber")), estimatedPackagesNumber));
                setProcessBaseVersionNum(NumberUtils.toInteger(StringUtils.toString(map.get("processBaseVersionNum")), processBaseVersionNum));
                setWeightMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("weightMethod")), weightMethod));
                setTechStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("techStandard")), techStandard));
                setBstsCoilingOrd(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bstsCoilingOrd")), bstsCoilingOrd));
                setQualityGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("qualityGrade")), qualityGrade));
                setFinalFmSpec(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finalFmSpec")), finalFmSpec));
                setProcessBaseNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processBaseNo")), processBaseNo));
                setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
                setOrderTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderTypeCode")), orderTypeCode));
                setInnerDim(NumberUtils.toInteger(StringUtils.toString(map.get("innerDim")), innerDim));
                setDirProcessDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dirProcessDemandId")), dirProcessDemandId));
                setDirProcessDemandSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dirProcessDemandSubId")), dirProcessDemandSubId));
                setPreProcessDemandOutputSeqId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("preProcessDemandOutputSeqId")), preProcessDemandOutputSeqId));
                setProdTypeDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeDesc")), prodTypeDesc));
                setProcessIfSettle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processIfSettle")), processIfSettle));
                setProcessFeeCalculateMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFeeCalculateMethod")), processFeeCalculateMethod));
                setExcessStockFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("excessStockFlag")), excessStockFlag));
                setFinishingShuntFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finishingShuntFlag")), finishingShuntFlag));
                setContractPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("contractPartId")), contractPartId));
                setMetalBalanceFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("metalBalanceFlag")), metalBalanceFlag));
                setManualNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("manualNo")), manualNo));
                setHsId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hsId")), hsId));
                setProcessConsignUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processConsignUnit")), processConsignUnit));
                setPrintOutputRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printOutputRemark")), printOutputRemark));
                setFinUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserNum")), finUserNum));
                setFinUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserName")), finUserName));
                setHandbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handbookId")), handbookId));
                setCustomsProductNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customsProductNum")), customsProductNum));
                setF_packId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_packId")), f_packId));
                setF_matInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_matInnerId")), f_matInnerId));
                setNodeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nodeCode")), nodeCode));
                setDemandRecNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("demandRecNo")), demandRecNo));
                setDemandRecVersionNum(NumberUtils.toInteger(StringUtils.toString(map.get("demandRecVersionNum")), demandRecVersionNum));
                setCraftCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craftCode")), craftCode));
                setCraftVersionNum(NumberUtils.toInteger(StringUtils.toString(map.get("craftVersionNum")), craftVersionNum));
                setProcessSeqCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSeqCode")), processSeqCode));
                setTagendMgrType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagendMgrType")), tagendMgrType));
                setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
                setCustPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartId")), custPartId));
                setCustPartName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartName")), custPartName));
                setBracketType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bracketType")), bracketType));
                setIronBracketNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ironBracketNo")), ironBracketNo));
                setMaxCoilWt(NumberUtils.toBigDecimal(StringUtils.toString(map.get("maxCoilWt")), maxCoilWt));
                setIsReturnMaterialArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isReturnMaterialArea")), isReturnMaterialArea));
                setPrintBatchId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printBatchId")), printBatchId));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("processOrderId",StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
                map.put("processDemandId",StringUtils.toString(processDemandId, eiMetadata.getMeta("processDemandId")));
                map.put("processDemandSubId",StringUtils.toString(processDemandSubId, eiMetadata.getMeta("processDemandSubId")));
                map.put("partId",StringUtils.toString(partId, eiMetadata.getMeta("partId")));
                map.put("prodNameCode",StringUtils.toString(prodNameCode, eiMetadata.getMeta("prodNameCode")));
                map.put("prodCname",StringUtils.toString(prodCname, eiMetadata.getMeta("prodCname")));
                map.put("specsDesc",StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
                map.put("shopsign",StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
                map.put("productProcessQty",StringUtils.toString(productProcessQty, eiMetadata.getMeta("productProcessQty")));
                map.put("productProcessWeight",StringUtils.toString(productProcessWeight, eiMetadata.getMeta("productProcessWeight")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("orderNum",StringUtils.toString(orderNum, eiMetadata.getMeta("orderNum")));
                map.put("productDemandId",StringUtils.toString(productDemandId, eiMetadata.getMeta("productDemandId")));
                map.put("lackDestinationId",StringUtils.toString(lackDestinationId, eiMetadata.getMeta("lackDestinationId")));
                map.put("processDemandOutputStatus",StringUtils.toString(processDemandOutputStatus, eiMetadata.getMeta("processDemandOutputStatus")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("contractNum",StringUtils.toString(contractNum, eiMetadata.getMeta("contractNum")));
                map.put("stockUseWeight",StringUtils.toString(stockUseWeight, eiMetadata.getMeta("stockUseWeight")));
                map.put("stockUseQty",StringUtils.toString(stockUseQty, eiMetadata.getMeta("stockUseQty")));
                map.put("processUseWeight",StringUtils.toString(processUseWeight, eiMetadata.getMeta("processUseWeight")));
                map.put("processUseQty",StringUtils.toString(processUseQty, eiMetadata.getMeta("processUseQty")));
                map.put("preDemandUseQty",StringUtils.toString(preDemandUseQty, eiMetadata.getMeta("preDemandUseQty")));
                map.put("preDemandUseWeight",StringUtils.toString(preDemandUseWeight, eiMetadata.getMeta("preDemandUseWeight")));
                map.put("quantityUnit",StringUtils.toString(quantityUnit, eiMetadata.getMeta("quantityUnit")));
                map.put("weightUnit",StringUtils.toString(weightUnit, eiMetadata.getMeta("weightUnit")));
                map.put("p_output",StringUtils.toString(p_output, eiMetadata.getMeta("p_output")));
                map.put("suitVoucherNum",StringUtils.toString(suitVoucherNum, eiMetadata.getMeta("suitVoucherNum")));
                map.put("productRemainQty",StringUtils.toString(productRemainQty, eiMetadata.getMeta("productRemainQty")));
                map.put("productRemainWeight",StringUtils.toString(productRemainWeight, eiMetadata.getMeta("productRemainWeight")));
                map.put("indm",StringUtils.toString(indm, eiMetadata.getMeta("indm")));
                map.put("packingTypeCode",StringUtils.toString(packingTypeCode, eiMetadata.getMeta("packingTypeCode")));
                map.put("salesPersId",StringUtils.toString(salesPersId, eiMetadata.getMeta("salesPersId")));
                map.put("salesPersName",StringUtils.toString(salesPersName, eiMetadata.getMeta("salesPersName")));
                map.put("processDemandQty",StringUtils.toString(processDemandQty, eiMetadata.getMeta("processDemandQty")));
                map.put("processDemandWeight",StringUtils.toString(processDemandWeight, eiMetadata.getMeta("processDemandWeight")));
                map.put("mproviderId",StringUtils.toString(mproviderId, eiMetadata.getMeta("mproviderId")));
                map.put("mproviderName",StringUtils.toString(mproviderName, eiMetadata.getMeta("mproviderName")));
                map.put("agreementId",StringUtils.toString(agreementId, eiMetadata.getMeta("agreementId")));
                map.put("agreementSubid",StringUtils.toString(agreementSubid, eiMetadata.getMeta("agreementSubid")));
                map.put("processFeePriceType",StringUtils.toString(processFeePriceType, eiMetadata.getMeta("processFeePriceType")));
                map.put("processFeePrice",StringUtils.toString(processFeePrice, eiMetadata.getMeta("processFeePrice")));
                map.put("processFeePriceTax",StringUtils.toString(processFeePriceTax, eiMetadata.getMeta("processFeePriceTax")));
                map.put("taxRate",StringUtils.toString(taxRate, eiMetadata.getMeta("taxRate")));
                map.put("diameter",StringUtils.toString(diameter, eiMetadata.getMeta("diameter")));
                map.put("labelForm",StringUtils.toString(labelForm, eiMetadata.getMeta("labelForm")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("customerMaterialNumber",StringUtils.toString(customerMaterialNumber, eiMetadata.getMeta("customerMaterialNumber")));
                map.put("processDemandOutputId",StringUtils.toString(processDemandOutputId, eiMetadata.getMeta("processDemandOutputId")));
                map.put("surfaceGrade",StringUtils.toString(surfaceGrade, eiMetadata.getMeta("surfaceGrade")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("prodCode",StringUtils.toString(prodCode, eiMetadata.getMeta("prodCode")));
                map.put("processDemandInputId",StringUtils.toString(processDemandInputId, eiMetadata.getMeta("processDemandInputId")));
                map.put("d_userNum",StringUtils.toString(d_userNum, eiMetadata.getMeta("d_userNum")));
                map.put("d_userName",StringUtils.toString(d_userName, eiMetadata.getMeta("d_userName")));
                map.put("productionProcessedQuantity",StringUtils.toString(productionProcessedQuantity, eiMetadata.getMeta("productionProcessedQuantity")));
                map.put("productionProcessedWeight",StringUtils.toString(productionProcessedWeight, eiMetadata.getMeta("productionProcessedWeight")));
                map.put("processSinglePackQuantity",StringUtils.toString(processSinglePackQuantity, eiMetadata.getMeta("processSinglePackQuantity")));
                map.put("processSinglePackWeight",StringUtils.toString(processSinglePackWeight, eiMetadata.getMeta("processSinglePackWeight")));
                map.put("estimatedPackagesNumber",StringUtils.toString(estimatedPackagesNumber, eiMetadata.getMeta("estimatedPackagesNumber")));
                map.put("processBaseVersionNum",StringUtils.toString(processBaseVersionNum, eiMetadata.getMeta("processBaseVersionNum")));
                map.put("weightMethod",StringUtils.toString(weightMethod, eiMetadata.getMeta("weightMethod")));
                map.put("techStandard",StringUtils.toString(techStandard, eiMetadata.getMeta("techStandard")));
                map.put("bstsCoilingOrd",StringUtils.toString(bstsCoilingOrd, eiMetadata.getMeta("bstsCoilingOrd")));
                map.put("qualityGrade",StringUtils.toString(qualityGrade, eiMetadata.getMeta("qualityGrade")));
                map.put("finalFmSpec",StringUtils.toString(finalFmSpec, eiMetadata.getMeta("finalFmSpec")));
                map.put("processBaseNo",StringUtils.toString(processBaseNo, eiMetadata.getMeta("processBaseNo")));
                map.put("prodTypeId",StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
                map.put("orderTypeCode",StringUtils.toString(orderTypeCode, eiMetadata.getMeta("orderTypeCode")));
                map.put("innerDim",StringUtils.toString(innerDim, eiMetadata.getMeta("innerDim")));
                map.put("dirProcessDemandId",StringUtils.toString(dirProcessDemandId, eiMetadata.getMeta("dirProcessDemandId")));
                map.put("dirProcessDemandSubId",StringUtils.toString(dirProcessDemandSubId, eiMetadata.getMeta("dirProcessDemandSubId")));
                map.put("preProcessDemandOutputSeqId",StringUtils.toString(preProcessDemandOutputSeqId, eiMetadata.getMeta("preProcessDemandOutputSeqId")));
                map.put("prodTypeDesc",StringUtils.toString(prodTypeDesc, eiMetadata.getMeta("prodTypeDesc")));
                map.put("processIfSettle",StringUtils.toString(processIfSettle, eiMetadata.getMeta("processIfSettle")));
                map.put("processFeeCalculateMethod",StringUtils.toString(processFeeCalculateMethod, eiMetadata.getMeta("processFeeCalculateMethod")));
                map.put("excessStockFlag",StringUtils.toString(excessStockFlag, eiMetadata.getMeta("excessStockFlag")));
                map.put("finishingShuntFlag",StringUtils.toString(finishingShuntFlag, eiMetadata.getMeta("finishingShuntFlag")));
                map.put("contractPartId",StringUtils.toString(contractPartId, eiMetadata.getMeta("contractPartId")));
                map.put("metalBalanceFlag",StringUtils.toString(metalBalanceFlag, eiMetadata.getMeta("metalBalanceFlag")));
                map.put("manualNo",StringUtils.toString(manualNo, eiMetadata.getMeta("manualNo")));
                map.put("hsId",StringUtils.toString(hsId, eiMetadata.getMeta("hsId")));
                map.put("processConsignUnit",StringUtils.toString(processConsignUnit, eiMetadata.getMeta("processConsignUnit")));
                map.put("printOutputRemark",StringUtils.toString(printOutputRemark, eiMetadata.getMeta("printOutputRemark")));
                map.put("finUserNum",StringUtils.toString(finUserNum, eiMetadata.getMeta("finUserNum")));
                map.put("finUserName",StringUtils.toString(finUserName, eiMetadata.getMeta("finUserName")));
                map.put("handbookId",StringUtils.toString(handbookId, eiMetadata.getMeta("handbookId")));
                map.put("customsProductNum",StringUtils.toString(customsProductNum, eiMetadata.getMeta("customsProductNum")));
                map.put("f_packId",StringUtils.toString(f_packId, eiMetadata.getMeta("f_packId")));
                map.put("f_matInnerId",StringUtils.toString(f_matInnerId, eiMetadata.getMeta("f_matInnerId")));
                map.put("nodeCode",StringUtils.toString(nodeCode, eiMetadata.getMeta("nodeCode")));
                map.put("demandRecNo",StringUtils.toString(demandRecNo, eiMetadata.getMeta("demandRecNo")));
                map.put("demandRecVersionNum",StringUtils.toString(demandRecVersionNum, eiMetadata.getMeta("demandRecVersionNum")));
                map.put("craftCode",StringUtils.toString(craftCode, eiMetadata.getMeta("craftCode")));
                map.put("craftVersionNum",StringUtils.toString(craftVersionNum, eiMetadata.getMeta("craftVersionNum")));
                map.put("processSeqCode",StringUtils.toString(processSeqCode, eiMetadata.getMeta("processSeqCode")));
                map.put("tagendMgrType",StringUtils.toString(tagendMgrType, eiMetadata.getMeta("tagendMgrType")));
                map.put("tradeCode",StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
                map.put("custPartId",StringUtils.toString(custPartId, eiMetadata.getMeta("custPartId")));
                map.put("custPartName",StringUtils.toString(custPartName, eiMetadata.getMeta("custPartName")));
                map.put("bracketType",StringUtils.toString(bracketType, eiMetadata.getMeta("bracketType")));
                map.put("ironBracketNo",StringUtils.toString(ironBracketNo, eiMetadata.getMeta("ironBracketNo")));
                map.put("maxCoilWt",StringUtils.toString(maxCoilWt, eiMetadata.getMeta("maxCoilWt")));
                map.put("isReturnMaterialArea",StringUtils.toString(isReturnMaterialArea, eiMetadata.getMeta("isReturnMaterialArea")));
                map.put("printBatchId",StringUtils.toString(printBatchId, eiMetadata.getMeta("printBatchId")));

return map;

}
}