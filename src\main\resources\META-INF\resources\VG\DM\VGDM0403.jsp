<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-checkStartDate"
                           endName="inqu_status-0-checkEndDate" readonly="true"
                           startCname="点检日期(起)" endCname="点检日期(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
            <EF:EFSelect ename="inqu_status-0-spotCheckNature" cname="点检性质" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P050"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="完成率统计">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="none" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="checkPlanDate" cname="点检日期" align="center" width="80"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称"/>
            <EF:EFColumn ename="checkPlanDate" cname="点检日期" width="100"/>
            <EF:EFComboColumn ename="spotCheckNature" cname="点检性质" align="center" width="70">
                <EF:EFCodeOption codeName="P050"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="totalNum" cname="总数量" align="right" format="{0}" sumType="page"/>
            <EF:EFColumn ename="notNum" cname="未点检数量" align="right" format="{0}" sumType="page"/>
            <EF:EFColumn ename="doNum" cname="已点检数量" align="right" format="{0}" sumType="page"/>
            <EF:EFColumn ename="doRate" cname="完成率" align="right" format="{0:p}" sumType="page"/>
            <EF:EFColumn ename="eNum" cname="点检异常数量" align="right" format="{0}" sumType="page"/>
            <EF:EFColumn ename="eRate" cname="异常率" align="right" format="{0:p}" sumType="page"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>