<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON>E sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-11-28 13:05:22
   		Version :  1.0
		tableName :meli.tlids0501 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 UNPACK_AREA_ID  VARCHAR   NOT NULL, 
		 UNPACK_AREA_NAME  VARCHAR   NOT NULL, 
		 CROSS_AREA  VARCHAR   NOT NULL, 
		 CROSS_AREA_NAME  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 FACTORY_AREA_NAME  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING_NAME  VARCHAR   NOT NULL, 
		 MAX_CAPACITY  INTEGER   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL   primarykey
	-->
<sqlMap namespace="tlids0501">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids0501">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				UNPACK_AREA_ID	as "unpackAreaId",  <!-- 拆包区编号 -->
				UNPACK_AREA_NAME	as "unpackAreaName",  <!-- 拆包区名称 -->
				CROSS_AREA	as "crossArea",  <!-- 拆包区跨区代码 -->
				CROSS_AREA_NAME	as "crossAreaName",  <!-- 拆包区跨区名称 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区代码 -->
				FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
				FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
				FACTORY_BUILDING_NAME	as "factoryBuildingName",  <!-- 厂房名称 -->
				MAX_CAPACITY	as "maxCapacity",  <!-- 最大容量 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids0501 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0501 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unpackAreaId">
			UNPACK_AREA_ID = #unpackAreaId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unpackAreaName">
			UNPACK_AREA_NAME = #unpackAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="crossArea">
			CROSS_AREA = #crossArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="crossAreaName">
			CROSS_AREA_NAME = #crossAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			FACTORY_BUILDING = #factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuildingName">
			FACTORY_BUILDING_NAME = #factoryBuildingName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="maxCapacity">
			MAX_CAPACITY = #maxCapacity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0501 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										UNPACK_AREA_ID,  <!-- 拆包区编号 -->
										UNPACK_AREA_NAME,  <!-- 拆包区名称 -->
										CROSS_AREA,  <!-- 拆包区跨区代码 -->
										CROSS_AREA_NAME,  <!-- 拆包区跨区名称 -->
										FACTORY_AREA,  <!-- 厂区代码 -->
										FACTORY_AREA_NAME,  <!-- 厂区名称 -->
										FACTORY_BUILDING,  <!-- 厂房代码 -->
										FACTORY_BUILDING_NAME,  <!-- 厂房名称 -->
										MAX_CAPACITY,  <!-- 最大容量 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #unpackAreaId#, #unpackAreaName#, #crossArea#, #crossAreaName#, #factoryArea#, #factoryAreaName#, #factoryBuilding#, #factoryBuildingName#, #maxCapacity#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0501 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlids0501 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					UNPACK_AREA_ID	= #unpackAreaId#,   <!-- 拆包区编号 -->  
					UNPACK_AREA_NAME	= #unpackAreaName#,   <!-- 拆包区名称 -->  
					CROSS_AREA	= #crossArea#,   <!-- 拆包区跨区代码 -->  
					CROSS_AREA_NAME	= #crossAreaName#,   <!-- 拆包区跨区名称 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区代码 -->  
					FACTORY_AREA_NAME	= #factoryAreaName#,   <!-- 厂区名称 -->  
					FACTORY_BUILDING	= #factoryBuilding#,   <!-- 厂房代码 -->  
					FACTORY_BUILDING_NAME	= #factoryBuildingName#,   <!-- 厂房名称 -->  
					MAX_CAPACITY	= #maxCapacity#,   <!-- 最大容量 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
						WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>