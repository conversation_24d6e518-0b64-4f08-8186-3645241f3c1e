package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 这里填写描述
 * 填写修改说明
 *
 * <AUTHOR>
 * @version Revision:v1.0
 * @since Date:2024-08-05 16:00
 */
public class GetSequenceUtils {
    private static final Logger LOG = LoggerFactory.getLogger(GetSequenceUtils.class);

    /**
     * 生成序列号方法
     * @param dao Service调用使用服务类的dao
     * @param segNo 业务单元码
     * @param seqTypeId 序列号类型
     * @param asDate 时间（非必须）
     * @return String
     * <AUTHOR>
     */
    public static String getSequence(Dao dao, String segNo, String seqTypeId,String asDate){
        LOG.info("生成序列-开始:" + segNo + " | " + seqTypeId + " | " + asDate);
        //获取当前时间
        Date ldDate = new Date();
        //传入参数有传入时间取传入时间，没有则取当前时间
        if (StringUtils.isNotBlank(asDate)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            try {
                ldDate =format.parse(asDate) ;
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }else{
            ldDate = new Date();
        }
        String accSetNo ="";
        String sequenceId ="";
        // 序列号计数
        String lsSequence= "";
        // 序列号组合
        String lsPreSequence= "";
        int lnFlowId = 1;
        // 根据传入的序列号类型，取出定义的序列号编制级别
        Map<String,Object> querySeqBaseMap = new HashMap();
        querySeqBaseMap.put("seqTypeId",seqTypeId);
        List<Map<String,String>> seqBaseResultList = dao.query("XTSS02.querySequenceBase",querySeqBaseMap);
        if (seqBaseResultList.size()==1){
            String seqCtrClass = seqBaseResultList.get(0).get("seqCtrClass")==null ?"":seqBaseResultList.get(0).get("seqCtrClass").toString();
            String seqStyle = seqBaseResultList.get(0).get("seqStyle")==null ?"":seqBaseResultList.get(0).get("seqStyle").toString();
            if("A".equals(seqCtrClass)){
                segNo= segNo.substring(0,5);
                accSetNo = "1";
            }else if ("B".equals(seqCtrClass)){
                accSetNo = "";
            }else if ("C".equals(seqCtrClass)){
                segNo= segNo.substring(0,segNo.length()-3);
                accSetNo = "";
            }else if ("D".equals(seqCtrClass)){
                segNo= segNo.substring(0,5);
                accSetNo = "";
            }else{
                segNo= segNo.substring(0,5);
                accSetNo = "";
            }
        }else if (seqBaseResultList.size()>1){
            throw new PlatException("查到多条数据!请检查sequencebase表!");
        }else{
            throw new PlatException("找不到数据!请检查sequencebase表!");
        }
        //取出序列号的分段数、日期循环编码级别、长度限制、子项长度
        String dateCycle ="";
        String lsSequenceTemp ="";
        String lsSequenceTempMon ="";
        String seqType ="1";   //生成序列规则
        String oracleSeq ="-"; //数据库序列号
        int lnSequenceLen = 0 ;
        Map<String,Object> querySeqMap = new HashMap();
        querySeqMap.put("segNo",segNo);
        querySeqMap.put("seqTypeId",seqTypeId);
        List<Map<String,String>> seqResultList = dao.query("XTSS02.querySequence",querySeqMap);
        if (seqResultList.size() == 1 ){
            dateCycle = seqResultList.get(0).get("dateCycle")==null ?"":seqResultList.get(0).get("dateCycle").toString();
            seqType = seqResultList.get(0).get("seqType")==null ?"":seqResultList.get(0).get("seqType").toString();
            oracleSeq = seqResultList.get(0).get("oracleSeq")==null ?"":seqResultList.get(0).get("oracleSeq").toString();
        }else if (seqResultList.size() > 1 ){
            throw new PlatException("找到多条数据!请检查sequence表!");
        }
        else{
            throw new PlatException("找不到数据!请检查sequence表!");
        }
        int lnSeqStrS = 1;
        List<Map<String,String>> seqSubResultList = dao.query("XTSS02.querySequenceSubSeg",querySeqMap);
        for (int i = 0; i < seqSubResultList.size(); i++) {
            Map seqSubResultMap = new HashMap();
            seqSubResultMap = seqSubResultList.get(i);
            String subsectionType = seqSubResultMap.get("subsectionType")==null ?"":seqSubResultMap.get("subsectionType").toString();
            String dateType = seqSubResultMap.get("dateType")==null ?"":seqSubResultMap.get("dateType").toString();
            //判断分段的类型
            if ("12".equals(subsectionType)){
                //12取系统别，目前取业务单元最后两位
                lsSequenceTemp =segNo.substring(segNo.length()-2,2);
            }else if ("02".equals(subsectionType)){
                if("YYYY".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
                    lsSequenceTemp =sdf.format(ldDate);
                }else if("YY".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yy");
                    lsSequenceTemp =sdf.format(ldDate);
                }else if("YYYYMM".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                    lsSequenceTemp =sdf.format(ldDate);
                }else if("YYMM".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyMM");
                    lsSequenceTemp =sdf.format(ldDate);
                }else if("YYYYMMDD".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    lsSequenceTemp =sdf.format(ldDate);
                }else if("YYMMDD".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
                    lsSequenceTemp =sdf.format(ldDate);
                }else if("YYM".equals(dateType)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yy");
                    lsSequenceTemp =sdf.format(ldDate);
                    SimpleDateFormat sdf1 = new SimpleDateFormat("MM");
                    lsSequenceTempMon =sdf1.format(ldDate);
                    if ("0".equals(lsSequenceTempMon.substring(0,1))){
                        lsSequenceTempMon = lsSequenceTempMon.substring(1,1);
                    }else if  ("10".equals(lsSequenceTempMon)){
                        lsSequenceTempMon ="A";
                    }else if  ("11".equals(lsSequenceTempMon)){
                        lsSequenceTempMon ="B";
                    }else {
                        lsSequenceTempMon ="C";
                    }
                    lsSequenceTemp = lsSequenceTemp + lsSequenceTempMon;
                }
            }else if ("03".equals(subsectionType)){
                //如果是03，流水号
                lsSequenceTemp ="FLOWID";
                lnSequenceLen = (Integer) seqSubResultMap.get("subsectionLength");
            }else{
                lsSequenceTemp = seqSubResultMap.get("subsectionContent").toString();
            }
            if (!"11".equals(subsectionType)&&!"02".equals(subsectionType)){
                lsSequence = lsSequence + lsSequenceTemp;
            }
            lsPreSequence = lsPreSequence + lsSequenceTemp;
        }
        //判断序列号取值表中是否有数据
        Map<String,Object> querySeqGenMap = new HashMap();
        querySeqGenMap.put("segNo",segNo);
        querySeqGenMap.put("accSetNo",accSetNo);
        querySeqGenMap.put("seqTypeId",seqTypeId);
        if (StringUtils.isNotBlank(lsSequence)) {
            querySeqGenMap.put("lsSequence",lsSequence);
        }else{
            querySeqGenMap.put("lsSequence","N");
        }
        if("Y".equals(dateCycle)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            querySeqGenMap.put("yearOrMonth",sdf.format(ldDate));
        }else if("M".equals(dateCycle)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            querySeqGenMap.put("yearOrMonth",sdf.format(ldDate));
        }else if("D".equals(dateCycle)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            querySeqGenMap.put("yearOrMonth",sdf.format(ldDate));
        }else{
            querySeqGenMap.put("yearOrMonth","N");
        }
        List<Map<String,String>> seqGenResultList = dao.query("XTSS02.querySequenceGen",querySeqGenMap);
        if (seqGenResultList.size()==0 && !"2".equals(seqType)){
            //没有生成过序列号，插入序列号记录
            querySeqGenMap.put("currentSeq","1");
            dao.insert("XTSS02.insertSequenceGen", querySeqGenMap);
            //序列号为1，后续输出使用
            lnFlowId = 1;
        } else{
            dao.update("XTSS02.updateSequenceGen", querySeqGenMap);
            List<Map<String,String>> seqGenResultRetList = dao.query("XTSS02.querySequenceGen",querySeqGenMap);
            if (seqGenResultRetList.size()==1){
                Map seqGenResultRetMap = new HashMap();
                seqGenResultRetMap = seqGenResultRetList.get(0);
                String lnFlowIdString =seqGenResultRetMap.get("currentSeq").toString();
                lnFlowId = Integer.valueOf(lnFlowIdString);
            }else if  (seqGenResultRetList.size()>1){
                throw new PlatException("找到多条数据!请检查sequenceGen表!");
            }else {
                throw new PlatException("找不到数据!请检查sequenceGen表!");
            }
        }
        //组装单据号
        String lnFlowIdInsert = String.format("%0" + lnSequenceLen + "d", lnFlowId);
        sequenceId = lsPreSequence.replace("FLOWID",lnFlowIdInsert);
        LOG.info("生成序列-结束: " + segNo + " | " + seqTypeId + " | " + asDate + ";生成序列号: " + sequenceId);
        return sequenceId;
    }

}
