/**
 * Generate time : 2024-12-06 9:45:50
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm9901
 */
public class Tvgdm9901 extends DaoEPBase {

    private String tagId = " ";        /* 点位ID*/
    private String tagValue = " ";        /* 点位值*/
    private Long tagTime = 0L;        /* 点位时间*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("tagId");
        eiColumn.setDescName("点位ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagValue");
        eiColumn.setDescName("点位值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagTime");
        eiColumn.setDescName("点位时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm9901() {
        initMetaData();
    }

    /**
     * get the tagId - 点位ID
     *
     * @return the tagId
     */
    public String getTagId() {
        return this.tagId;
    }

    /**
     * set the tagId - 点位ID
     */
    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    /**
     * get the tagValue - 点位值
     *
     * @return the tagValue
     */
    public String getTagValue() {
        return this.tagValue;
    }

    /**
     * set the tagValue - 点位值
     */
    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    /**
     * get the tagTime - 点位时间
     *
     * @return the tagTime
     */
    public Long getTagTime() {
        return this.tagTime;
    }

    /**
     * set the tagTime - 点位时间
     */
    public void setTagTime(Long tagTime) {
        this.tagTime = tagTime;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setTagId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagId")), tagId));
        setTagValue(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagValue")), tagValue));
        setTagTime(NumberUtils.toLong(StringUtils.toString(map.get("tagTime")), tagTime));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("tagId", StringUtils.toString(tagId, eiMetadata.getMeta("tagId")));
        map.put("tagValue", StringUtils.toString(tagValue, eiMetadata.getMeta("tagValue")));
        map.put("tagTime", StringUtils.toString(tagTime, eiMetadata.getMeta("tagTime")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));

        return map;

    }
}