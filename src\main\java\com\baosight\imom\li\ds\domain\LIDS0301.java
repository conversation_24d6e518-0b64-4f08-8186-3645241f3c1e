/**
 * Generate time : 2024-11-27 13:44:37
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0301;

import java.util.Map;

/**
 * 行车管理
 *
 */
public class LIDS0301 extends Tlids0301 {
    public static final String QUERY = "LIDS0301.query";
    public static final String COUNT = "LIDS0301.count";
    public static final String COUNT_UUID = "LIDS0301.count_uuid";
    public static final String INSERT = "LIDS0301.insert";
    public static final String UPDATE = "LIDS0301.update";
    public static final String UPDATE_STATUS = "LIDS0301.updateStatus";
    public static final String DELETE = "LIDS0301.delete";
    //根据跨区查询行车信息
    public static final String QUERY_CRANE_IDS_BY_CROSS_AREA = "LIDS0301.queryCraneIdsByCrossArea";
    public static final String QUERY_BY_CRANE_ID = "LIDS0301.queryByCraneId";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS0301() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}