<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-10-08 10:22:28
   		Version :  1.0
		tableName :${meliSchema}.tlirl0311 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CAR_TRACE_NO  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 HAND_TYPE  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 ID_CARD  VARCHAR   NOT NULL, 
		 DRIVER_NAME  VARCHAR   NOT NULL, 
		 TEL_NUM  VARCHAR   NOT NULL, 
		 RESERVATION_NUMBER  VARCHAR   NOT NULL, 
		 CHECK_DATE  VARCHAR   NOT NULL, 
		 <PERSON>NTER_FACTORY  VARCHAR   NOT NULL, 
		 BEGIN_ENTRUCKING_TIME  VARCHAR   NOT NULL, 
		 COMPLETE_UNINSTALL_TIME  VARCHAR   NOT NULL, 
		 LEAVE_FACTORY_DATE  VARCHAR   NOT NULL, 
		 CUSTOMER_SIGNING_TIME  VARCHAR   NOT NULL, 
		 TARGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 CURRENT_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 UNLOAD_LEAVE_FLAG  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0311">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handType">
			HAND_TYPE = #handType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNoList">
			VEHICLE_NO in
			<iterate property="vehicleNoList" open="("
					 close=")" conjunction=" , ">
				#vehicleNoList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="idCard">
			ID_CARD = #idCard#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="telNum">
			TEL_NUM = #telNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="checkDate">
			CHECK_DATE = #checkDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="enterFactory">
			ENTER_FACTORY = #enterFactory#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="beginEntruckingTime">
			BEGIN_ENTRUCKING_TIME = #beginEntruckingTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="completeUninstallTime">
			COMPLETE_UNINSTALL_TIME = #completeUninstallTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryDate">
			LEAVE_FACTORY_DATE = #leaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerSigningTime">
			CUSTOMER_SIGNING_TIME = #customerSigningTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			CURRENT_HAND_POINT_ID = #currentHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unloadLeaveFlag">
			UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0311">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				STATUS	as "status",  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
				HAND_TYPE	as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				ID_CARD	as "idCard",  <!-- 身份证号 -->
				DRIVER_NAME	as "driverName",  <!-- 驾驶员姓名 -->
				TEL_NUM	as "telNum",  <!-- 手机号 -->
				RESERVATION_NUMBER	as "reservationNumber",  <!-- 车辆预约单号 -->
				CHECK_DATE	as "checkDate",  <!-- 进厂登记时间 -->
				ENTER_FACTORY	as "enterFactory",  <!-- 入厂时间 -->
				BEGIN_ENTRUCKING_TIME	as "beginEntruckingTime",  <!-- 作业开始时间 -->
				COMPLETE_UNINSTALL_TIME	as "completeUninstallTime",  <!-- 作业结束时间 -->
				LEAVE_FACTORY_DATE	as "leaveFactoryDate",  <!-- 出厂时间 -->
				CUSTOMER_SIGNING_TIME	as "customerSigningTime",  <!-- 客户签约时间 -->
				TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点代码 -->
				CURRENT_HAND_POINT_ID	as "currentHandPointId",  <!-- 当前装卸点代码 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区 -->
				UNLOAD_LEAVE_FLAG	as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0311 WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0311 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handType">
			HAND_TYPE = #handType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="idCard">
			ID_CARD = #idCard#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="telNum">
			TEL_NUM = #telNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="checkDate">
			CHECK_DATE = #checkDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="enterFactory">
			ENTER_FACTORY = #enterFactory#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="beginEntruckingTime">
			BEGIN_ENTRUCKING_TIME = #beginEntruckingTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="completeUninstallTime">
			COMPLETE_UNINSTALL_TIME = #completeUninstallTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leaveFactoryDate">
			LEAVE_FACTORY_DATE = #leaveFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerSigningTime">
			CUSTOMER_SIGNING_TIME = #customerSigningTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			CURRENT_HAND_POINT_ID = #currentHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unloadLeaveFlag">
			UNLOAD_LEAVE_FLAG = #unloadLeaveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0311 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										STATUS,  <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
										HAND_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
										VEHICLE_NO,  <!-- 车牌号 -->
										ID_CARD,  <!-- 身份证号 -->
										DRIVER_NAME,  <!-- 驾驶员姓名 -->
										TEL_NUM,  <!-- 手机号 -->
										RESERVATION_NUMBER,  <!-- 车辆预约单号 -->
										CHECK_DATE,  <!-- 进厂登记时间 -->
										ENTER_FACTORY,  <!-- 入厂时间 -->
										BEGIN_ENTRUCKING_TIME,  <!-- 作业开始时间 -->
										COMPLETE_UNINSTALL_TIME,  <!-- 作业结束时间 -->
										LEAVE_FACTORY_DATE,  <!-- 出厂时间 -->
										CUSTOMER_SIGNING_TIME,  <!-- 客户签约时间 -->
										TARGET_HAND_POINT_ID,  <!-- 目标装卸点代码 -->
										CURRENT_HAND_POINT_ID,  <!-- 当前装卸点代码 -->
										FACTORY_AREA,  <!-- 厂区 -->
										FACTORY_AREA_NAME,  <!-- 厂区 -->
										UNLOAD_LEAVE_FLAG,  <!-- 未装离厂标记 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
		EXPECTED_LOADING_TIME
		<isNotEmpty prepend=" , " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO
		</isNotEmpty>
										)		 
	    VALUES (#segNo#, #unitCode#, #carTraceNo#, #status#, #handType#, #vehicleNo#, #idCard#, #driverName#, #telNum#, #reservationNumber#, #checkDate#,
	            #enterFactory#, #beginEntruckingTime#, #completeUninstallTime#, #leaveFactoryDate#, #customerSigningTime#, #targetHandPointId#,
	            #currentHandPointId#, #factoryArea#,#factoryAreaName#, #unloadLeaveFlag#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
	            #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#,#expectedLoadingTime#
		<isNotEmpty prepend=" , " property="allocateVehicleNo">
			#allocateVehicleNo#
		</isNotEmpty>
		)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0311 WHERE 
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0311 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->  
					STATUS	= #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->  
					HAND_TYPE	= #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					ID_CARD	= #idCard#,   <!-- 身份证号 -->  
					DRIVER_NAME	= #driverName#,   <!-- 驾驶员姓名 -->  
					TEL_NUM	= #telNum#,   <!-- 手机号 -->  
					RESERVATION_NUMBER	= #reservationNumber#,   <!-- 车辆预约单号 -->  
					CHECK_DATE	= #checkDate#,   <!-- 进厂登记时间 -->  
					ENTER_FACTORY	= #enterFactory#,   <!-- 入厂时间 -->  
					BEGIN_ENTRUCKING_TIME	= #beginEntruckingTime#,   <!-- 作业开始时间 -->  
					COMPLETE_UNINSTALL_TIME	= #completeUninstallTime#,   <!-- 作业结束时间 -->  
					LEAVE_FACTORY_DATE	= #leaveFactoryDate#,   <!-- 出厂时间 -->  
					CUSTOMER_SIGNING_TIME	= #customerSigningTime#,   <!-- 客户签约时间 -->  
					TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点代码 -->  
					CURRENT_HAND_POINT_ID	= #currentHandPointId#,   <!-- 当前装卸点代码 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					UNLOAD_LEAVE_FLAG	= #unloadLeaveFlag#,   <!-- 未装离厂标记 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->
			WHERE 	
	</update>

	<update id="updateStatusByCarTraceNo">
		UPDATE ${meliSchema}.tlirl0311
		SET
		STATUS	= #status#,   <!-- 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60） -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO	= #segNo#
		and CAR_TRACE_NO = #carTraceNo#
	</update>
  
</sqlMap>