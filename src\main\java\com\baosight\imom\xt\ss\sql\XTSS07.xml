<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XTSS07">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            T9.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            T8.LOGIN_NAME like concat('%',#loginName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            T8.USER_NAME like concat('%',#userName#,'%')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.xt.ss.domain.XTSS07">
        SELECT
        T9.SEG_NO as "segNo",  
        T9.SEG_NAME as "segName",
        T8.LOGIN_NAME as "loginName",
        T8.USER_NAME as "userName",
        T8.MOBILE as "mobile"
        FROM ${platSchema}.XS_USER T8
        left join ${platSchema}.XS_USER_SEGNO T9 ON T8.USER_ID = T9.USER_ID
        WHERE T8.STATUS = '1'
        AND T8.IS_LOCKED = '1'        
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${platSchema}.XS_USER T8
        left join ${platSchema}.XS_USER_SEGNO T9 ON T8.USER_ID = T9.USER_ID
        WHERE T8.STATUS = '1'
        AND T8.IS_LOCKED = '1'        
        <include refid="condition"/>
    </select>

</sqlMap>