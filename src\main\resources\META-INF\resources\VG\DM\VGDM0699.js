$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
                // 转储按钮
                $("#CONVERT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("segNo", $("#inqu_status-0-segNo").val());
                    eiInfo.addBlock(resultGrid.getCheckedBlockData());
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM0699", "convert", function (ei) {
                        // resultGrid.dataSource.page(1);
                    });
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
});
