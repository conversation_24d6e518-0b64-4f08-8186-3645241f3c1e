<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-windowId" cname="弹窗ID" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-closeFlag" cname="关闭弹窗标记" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" type="hidden"/>
        <EF:EFInput ename="inqu_status-0-segNo" cname="帐套" type="hidden"/>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-tagId" cname="点位代码" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-tagDesc" cname="点位名称" placeholder="模糊条件" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-deviceName" cname="分部设备名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="tagId" cname="点位代码" align="center"/>
            <EF:EFColumn ename="tagDesc" cname="点位名称"/>
            <EF:EFColumn ename="scadaName" cname="scada节点"/>
            <EF:EFColumn ename="tagIhdId" cname="点位ID" align="right"/>
        </EF:EFGrid>
    </EF:EFRegion>

</EF:EFPage>