<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css">
    <link rel="stylesheet" type="text/css" href="css/billScan.css" />
    <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />

    <style type="text/css">
        /* 添加样式 */
        .selected-bills-container {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .bill-tag {
            position: relative;
            background: #f0f0f0;
            padding: 8px 25px 8px 12px;
            border-radius: 15px;
            font-size: 14px;
            color: #333;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 删除按钮样式 */
        .bill-tag .remove-tag {
            float: none; /* 覆盖 float */
            width: 20px; /* 调整为固定宽度 */
            height: 20px; /* 调整为固定高度 */
            line-height: 20px; /* 垂直居中 */
            display: inline-flex; /* 使用 flex 布局 */
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中 */
            font-size: 12px; /* 覆盖 font-size */
            color: #999; /* 覆盖 color */
            margin-right: 0; /* 覆盖 margin */
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            border-radius: 50%; /* 圆形背景 */
            background-color: transparent; /* 默认透明背景 */
            transition: background-color 0.2s ease, color 0.2s ease; /* 添加过渡效果 */
        }

        /* 鼠标悬停时删除按钮样式 */
        .bill-tag .remove-tag:hover {
            color: #666; /* 文字颜色变深 */
            background-color: rgba(0, 0, 0, 0.1); /* 浅灰色圆圈背景 */
        }
        .swal2-popup .select-all {
            margin-bottom: 12px;
            padding-bottom: 8px;
        }
        .swal2-popup .select-all strong {
            color: #1890ff;
            font-size: 14px;
        }


    </style>
</head>

<body>
    <div class="wrapper">
        <div class="header">
            <div id="logo" class="logo-baosight"></div>
            <div class="header-return">
                <button class="return-home-btn" onclick="returnHome()">返回主页</button>
            </div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li>1.登记车牌号</li>
                    <li class="arrow"></li>
                    <li class="fontblue">2.选择业务</li>
                    <li class="arrow"></li>
                    <li>3.进厂登记</li>
                </ul>
            </div>
        </div>
        <div class="container">

            <div class="main">
                <div class="information3-zdy" style="padding-bottom: 0;">
                    <ul>
                        <li style="display: flex;">
                            <span class="tag">提单号</span>
                            <div class="ipt-div-btn">
                                <input id="bill_id" class="ipt1" type="text" placeholder="请下拉选择提单号或扫描提单号或手工输入提单号">
                            </div>
                        </li>
                        <li>
                            <!-- 在container div内添加标签容器 -->
                            <div class="selected-bills-container" id="selectedBillsContainer"></div>
                        </li>
                        <li style="display: flex;"><span>装卸类型</span>
                            <div class="ipt-div-btn">
                                <button id="loading" class="loading" value="10" onclick="selected(this.value)">钢材装货</button>
                                <button id="discharge" class="discharge" value="30" onclick="selected(this.value)">钢材卸货+装货</button>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="btn2">
                    <button class="before" onclick="javascript :history.back(-1);">上一步</button>
                    <button class="next" onclick="nextStep();">下一步</button>
                </div>
            </div>
            <div id="keyboard" style="display: none;"></div>
            <div class="div-wxts" style="margin-top: 50px;margin-left: 15%;">
                <span style="color: #FF0000;">*</span>温馨提示：<br />

                1、您可以从列表中<p>选择提单号</p>，您也可以扫描提单上的提单号<p>二维码</p>，或手工<p>输入提单号</p>。<br />
                2、如果您的业务仅涉及<p>提货</p>、请选择<p>“钢材装货”</p>选项。如果您需要先<p>卸下</p>货物再进行<p>装货</p>, 请选择<p>“钢材卸货+装货”</p>选项。
            </div>
        </div>
    </div>
    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/<EMAIL>"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script>
        // 维护选中提单号的数组
        let selectedBills = [];

        const input = document.getElementById("bill_id");
        let handType = '';

        //页面加载将车牌号与装卸货填充
        window.onload = function() {
            const wrapper = document.querySelector('.wrapper');
            wrapper.setAttribute('aria-hidden', 'false');  // 临时移除 aria-hidden
            localStorage.removeItem("hand_big_type");
            localStorage.removeItem("loadingType");
            fetchOptionsFromBackend();
        }

        document.addEventListener('DOMContentLoaded', function() {
            const keys = [
                'BL', 'ZK','1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                '退格', '清空', '确定'
            ];
        
            const keyboard = document.getElementById('keyboard');
            let activeInputField = null;
        
            keys.forEach(function(key) {
                const keyElement = document.createElement('div');
                keyElement.className = 'key';
                keyElement.textContent = key;
                keyboard.appendChild(keyElement);
        
                keyElement.addEventListener('click', function() {
                    if (!activeInputField) { return; }
                    if (key === '退格') {
                        activeInputField.value = activeInputField.value.slice(0, -1);
                        return;
                    }
                    if (key === '清空') {
                        activeInputField.value = '';
                        return;
                    }

                    if (key == '确定') {
                        if (activeInputField.value) {
                            addBill(activeInputField.value);
                        }

                        activeInputField.value = '';
                        activeInputField.focus();  // 确保输入框保持焦点
                        return;
                    }
        
                    activeInputField.value += key;
                    activeInputField.focus();  // 确保输入框保持焦点
        
                });
            });
        
            document.querySelectorAll('.ipt1').forEach(function(ipt2) {
                ipt2.addEventListener('focus', function() {
                    activeInputField = ipt2;
                    const rect = ipt2.getBoundingClientRect();
                    keyboard.style.display = 'flex';
                    keyboard.style.top = (rect.bottom + window.scrollY) - 120 + 'px';
                    keyboard.style.left = rect.left + window.scrollX + 'px';
                });
            });
        
            // 点击其他地方隐藏小键盘
            document.addEventListener('click', function(event) {
                if (!keyboard.contains(event.target) && !event.target.classList.contains('ipt1')) {
                    keyboard.style.display = 'none';
                    activeInputField = null;
                }
            });
        
            // 防止点击键盘时触发隐藏
            keyboard.addEventListener('click', function(event) {
                event.stopPropagation();
            });
        });

        //下一步
        function nextStep() {
            if (selectedBills.length == 0) {
                Swal.fire({
                    title: "请输入提单号",
                    icon: "warning",
                    confirmButtonText: "确定",
                    didClose: () => {  // 弹窗完全关闭后触发
                        document.getElementById('bill_id').focus();
                    }
                });
                return;
            }

            if (!handType) {
                Swal.fire({
                    title: "请选择装卸类型",
                    icon: "warning",
                    confirmButtonText: "确定",
                    didClose: () => {  // 弹窗完全关闭后触发
                        document.getElementById('bill_id').focus();
                    }
                });

                return;
            }

            localStorage.setItem("bill_id", selectedBills.join(','));
            $("#bill_id").val('');
            window.location.href = "intoFactory.html";
        }

        //  10 : 装货  ；20 ： 装货+卸货
        function selected(value, css) {
            // 获取所有按钮并重置背景样式
            const buttons = document.querySelectorAll('.loading, .discharge');
            buttons.forEach(button => {
                button.style.backgroundImage = 'url("img/typebg.png")';
            });

            // 设置选中按钮的背景样式
            const selectedButton = document.querySelector(`button[value="${value}"]`);
            if (selectedButton) {
                selectedButton.style.backgroundImage = 'url("img/typeactive.png")';
            }

            //选择提单时，将装卸货类型保存
            localStorage.setItem("handType", value);
            handType = value;
            setTimeout(() => {
                document.getElementById('bill_id').focus();
            },0);
        }

        // 从后端获取数据
        function fetchOptionsFromBackend() {
            showLoading("查询提单中");

            let param = {
                segNo, //账套
                idCard: localStorage.getItem("idCard"), //司机姓名
                driverName: localStorage.getItem("idName"), //司机姓名
                serviceId: "S_LI_RL_0096",
            };

            $.ajax({
                url: ytjServerUrl, // 后台接口地址
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(param),
                success: (data) => {
                    closeLoading();
                    if (!data || !data.__sys__ || data.__sys__.status == -1) {
                        Swal.fire({
                            title: data.__sys__.msg,
                            icon: "error",
                            confirmButtonText: "确定",
                        });
                        return;
                    }
                    if (data.list.length > 0) {
                        showBillSelectionDialog(data.list);
                    }
                },
                error: () =>
                    Swal.fire({
                        title: "网络异常, 请联系管理员!",
                        icon: "error",
                        confirmButtonText: "确定",
                    }),
            });

        }

        // 显示多选弹窗
        function showBillSelectionDialog(billList) {
            const options = billList.map(bill => {
                const totalWeight = truncateDecimals(bill.totalWeight, 3);
                const totalPackQty = truncateDecimals(bill.totalPackQty, 3);
                return `
        <label style="display: block; margin: 8px 0; cursor: pointer;">
            <input type="checkbox" value="${bill.ladingBillId}"
                   style="margin-right: 8px;"> ${bill.ladingBillId} ${bill.userName}
                   <span style="color: red;">${totalWeight}</span>吨/ <span style="color: red;">${totalPackQty}</span>件
                   <br />
                   始发站: ${bill.ladingSpotAddr}/终到站: ${bill.destSpotName}
                   <br />
                   备注: ${bill.remark}
        </label>
    `;
            }).join('');

            Swal.fire({
                title: '请选择本次提货的提单',
                html: `<div style="max-height: 70vh; overflow-y: auto;text-align: left;">
            <label style="display: block; margin: 8px 0; cursor: pointer; border-bottom: 1px solid #eee; padding-bottom: 8px;">
                <input type="checkbox" class="select-all" style="margin-right: 8px;"> <strong>全选</strong>
            </label>
            ${options}
          </div>`,
                showCancelButton: true,
                width: '80vw',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                didOpen: () => {
                    // 绑定全选功能
                    const popup = Swal.getPopup();
                    const selectAll = popup.querySelector('.select-all');
                    const checkboxes = popup.querySelectorAll('input[type="checkbox"]:not(.select-all)');

                    // 全选按钮点击事件
                    selectAll.addEventListener('click', (e) => {
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = e.target.checked;
                        });
                    });

                    // 单个复选框点击事件（用于联动全选状态）
                    checkboxes.forEach(checkbox => {
                        checkbox.addEventListener('click', () => {
                            const allChecked = [...checkboxes].every(cb => cb.checked);
                            selectAll.checked = allChecked;
                        });
                    });
                },
                preConfirm: () => {
                    const selected = Array.from(
                        Swal.getPopup().querySelectorAll('input[type="checkbox"]:checked:not(#swal2-checkbox, .select-all)')
                    ).map(el => el.value);
                    return selected;
                }
            }).then((result) => {
                if (result.isConfirmed && result.value.length > 0) {
                    selectedBills = [...new Set([...selectedBills, ...result.value])];
                    renderSelectedBills();
                }
            });
        }

        // 渲染已选标签
        function renderSelectedBills() {
            const container = document.getElementById('selectedBillsContainer');
            container.innerHTML = selectedBills.map(bill => `
        <div class="bill-tag">
            ${bill}
            <span class="remove-tag" onclick="removeBill('${bill}')">×</span>
        </div>
    `).join('');
            setTimeout(() => {
                input.focus();
            },0);
        }

        // 添加提单号（自动去重）
        function addBill(billNumber) {
            selectedBills = [...new Set([...selectedBills, billNumber])];
            renderSelectedBills();
        }

        // 删除标签
        function removeBill(billNumber) {
            selectedBills = selectedBills.filter(b => b !== billNumber);
            renderSelectedBills();
        }

        // 手动输入处理
        document.getElementById('bill_id').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const value = e.target.value.trim();
                if (value) {
                    if (!selectedBills.includes(value)) {
                        addBill(value);
                        renderSelectedBills();
                    }
                    e.target.value = '';
                }
            }
        });

        function truncateDecimals(numStr, decimalPlaces) {
            let parts = numStr.split(".");  // 分割整数部分和小数部分
            if (parts.length === 1) return numStr; // 没有小数部分，直接返回
            return parts[0] + "." + parts[1].substring(0, decimalPlaces);
        }

    </script>
</body>

</html>