/**
* Generate time : 2024-11-01 11:00:19
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0406
* 
*/
public class LIRL0406 extends DaoEPBase {
        public static final String QUERY = "LIRL0406.query";
        public static final String QUERY_ALL = "LIRL0406.queryAll";
        public static final String COUNT = "LIRL0406.count";
        public static final String INSERT = "LIRL0406.insert";
        public static final String UPDATE = "LIRL0406.update";
        public static final String UPDATE_BY_HAND_POINT_ID = "LIRL0406.updateByHandPointId";
        public static final String DELETE = "LIRL0406.delete";
        public static final String LINE_UP_AND_CALL_FOR_NUMBERS = "LIRL0406.lineUpAndCallForNumbers";
        public static final String LINE_UP_AND_CALL_FOR_NUMBERS_CQ = "LIRL0406.lineUpAndCallForNumbersCQ";
        public static final String QUERY_QUEUE = "LIRL0406.queryQueue";
        public static final String QUERY_HAND_POINT_ID = "LIRL0406.queryHandPointId";
        public static final String QUERY_HAND_POINT_ID_DRIVER = "LIRL0406.queryHandPointIdDriver";
        public static final String QUERY_LOADING_AND_UNLOADING_TYPES_AND_DATA = "LIRL0406.queryLoadingAndUnloadingTypesAndData";
        public static final String QUERY_LOADING_AND_UNLOADING_TYPES_AND_DATA_CQ = "LIRL0406.queryLoadingAndUnloadingTypesAndDataCQ";
        public static final String QUERY_LOADING_AND_UNLOADING_TYPES_AND_DATA_CQ_DRIVER = "LIRL0406.queryLoadingAndUnloadingTypesAndDataCQDriver";
        public static final String QUERY_OVERT_VEHICLENO = "LIRL0406.queryOvertVehicleno";

                private String segNo = " ";		/* 账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String loadId = " ";		/* 开始装卸货流水号*/
                private String status = " ";		/* 状态（00：撤销，10：新增）*/
                private String loadDate = " ";		/* 开始装卸货日期*/
                private String cancelLoadDate = " ";		/* 撤销开始装卸货时间*/
                private String carTraceNo = " ";		/* 车辆跟踪号*/
                private String dateSource = " ";		/* 数据源（10：MES,20:PDA）*/
                private String voucherNum = " ";		/* 依据凭单号（PAD作业流水号）*/
                private String targetHandPointId = " ";		/* 目标装卸点代码*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String currentHandPointId = " ";		/* 当前装卸点代码*/
                private String factoryArea = " ";		/* 厂区*/
                private String documentType = " ";		/* 单据类型(10普通 20重复)*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String perName = " ";		/* 行车工姓名*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadId");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("开始装卸货流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态（00：撤销，10：新增）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadDate");
        eiColumn.setDescName("开始装卸货日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cancelLoadDate");
        eiColumn.setDescName("撤销开始装卸货时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dateSource");
        eiColumn.setDescName("数据源（10：MES,20:PDA）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单号（PAD作业流水号）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointId");
        eiColumn.setDescName("目标装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointId");
        eiColumn.setDescName("当前装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("documentType");
        eiColumn.setDescName("单据类型(10普通 20重复)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("perName");
        eiColumn.setDescName("行车工姓名");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0406() {
initMetaData();
}

        /**
        * get the segNo - 账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the loadId - 开始装卸货流水号
        * @return the loadId
        */
        public String getLoadId() {
        return this.loadId;
        }

        /**
        * set the loadId - 开始装卸货流水号
        */
        public void setLoadId(String loadId) {
        this.loadId = loadId;
        }
        /**
        * get the status - 状态（00：撤销，10：新增）
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态（00：撤销，10：新增）
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the loadDate - 开始装卸货日期
        * @return the loadDate
        */
        public String getLoadDate() {
        return this.loadDate;
        }

        /**
        * set the loadDate - 开始装卸货日期
        */
        public void setLoadDate(String loadDate) {
        this.loadDate = loadDate;
        }
        /**
        * get the cancelLoadDate - 撤销开始装卸货时间
        * @return the cancelLoadDate
        */
        public String getCancelLoadDate() {
        return this.cancelLoadDate;
        }

        /**
        * set the cancelLoadDate - 撤销开始装卸货时间
        */
        public void setCancelLoadDate(String cancelLoadDate) {
        this.cancelLoadDate = cancelLoadDate;
        }
        /**
        * get the carTraceNo - 车辆跟踪号
        * @return the carTraceNo
        */
        public String getCarTraceNo() {
        return this.carTraceNo;
        }

        /**
        * set the carTraceNo - 车辆跟踪号
        */
        public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
        }
        /**
        * get the dateSource - 数据源（10：MES,20:PDA）
        * @return the dateSource
        */
        public String getDateSource() {
        return this.dateSource;
        }

        /**
        * set the dateSource - 数据源（10：MES,20:PDA）
        */
        public void setDateSource(String dateSource) {
        this.dateSource = dateSource;
        }
        /**
        * get the voucherNum - 依据凭单号（PAD作业流水号）
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 依据凭单号（PAD作业流水号）
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the targetHandPointId - 目标装卸点代码
        * @return the targetHandPointId
        */
        public String getTargetHandPointId() {
        return this.targetHandPointId;
        }

        /**
        * set the targetHandPointId - 目标装卸点代码
        */
        public void setTargetHandPointId(String targetHandPointId) {
        this.targetHandPointId = targetHandPointId;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the currentHandPointId - 当前装卸点代码
        * @return the currentHandPointId
        */
        public String getCurrentHandPointId() {
        return this.currentHandPointId;
        }

        /**
        * set the currentHandPointId - 当前装卸点代码
        */
        public void setCurrentHandPointId(String currentHandPointId) {
        this.currentHandPointId = currentHandPointId;
        }
        /**
        * get the factoryArea - 厂区
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the documentType - 单据类型(10普通 20重复)
        * @return the documentType
        */
        public String getDocumentType() {
        return this.documentType;
        }

        /**
        * set the documentType - 单据类型(10普通 20重复)
        */
        public void setDocumentType(String documentType) {
        this.documentType = documentType;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }

        public String getPerName() {
                return perName;
        }

        public void setPerName(String perName) {
                this.perName = perName;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setLoadId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadId")), loadId));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setLoadDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadDate")), loadDate));
                setCancelLoadDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("cancelLoadDate")), cancelLoadDate));
                setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
                setDateSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dateSource")), dateSource));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setTargetHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointId")), targetHandPointId));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setCurrentHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("currentHandPointId")), currentHandPointId));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setDocumentType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("documentType")), documentType));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setPerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("perName")), perName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("loadId",StringUtils.toString(loadId, eiMetadata.getMeta("loadId")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("loadDate",StringUtils.toString(loadDate, eiMetadata.getMeta("loadDate")));
                map.put("cancelLoadDate",StringUtils.toString(cancelLoadDate, eiMetadata.getMeta("cancelLoadDate")));
                map.put("carTraceNo",StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
                map.put("dateSource",StringUtils.toString(dateSource, eiMetadata.getMeta("dateSource")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("targetHandPointId",StringUtils.toString(targetHandPointId, eiMetadata.getMeta("targetHandPointId")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("currentHandPointId",StringUtils.toString(currentHandPointId, eiMetadata.getMeta("currentHandPointId")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("documentType",StringUtils.toString(documentType, eiMetadata.getMeta("documentType")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("perName",StringUtils.toString(perName, eiMetadata.getMeta("perName")));

return map;

}
}