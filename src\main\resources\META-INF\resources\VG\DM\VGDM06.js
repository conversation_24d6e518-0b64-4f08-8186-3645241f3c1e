$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfo",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 刷新按钮
    $("#REFRESH").on("click", function (e) {
        queryAlarmStatus();
    });
    // 启动监听按钮
    $("#START").on("click", function (e) {
        const info = new EiInfo();
        info.setByNode("inqu");
        info.set("clientType", "alarm");
        IMOMUtil.submitEiInfo(info, "VGDM06", "startClient", function (ei) {
            $("#detail_status-0-clientStatus").val(ei.get("detail_status-0-clientStatus"));
        });
    });
    // 停止监听按钮
    $("#STOP").on("click", function (e) {
        const info = new EiInfo();
        info.setByNode("inqu");
        info.set("clientType", "alarm");
        IMOMUtil.submitEiInfo(info, "VGDM06", "stopClient", function (ei) {
            $("#detail_status-0-clientStatus").val(ei.get("detail_status-0-clientStatus"));
        });
    });
    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
                if (unitInfo.segNo !== "") {
                    queryAlarmStatus();
                }
                // 确认按钮
                $("#CONFIRM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM06", "confirm", true, null, null, false);
                });
                // 故障生成按钮
                $("#FAULT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM06", "addFault", true, null, null, false);
                });
                // 加入设备履历按钮
                $("#ADDRESUME").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM06", "addResume", true, null, null, false);
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({
        afterSelect: function (rows) {
            if (rows.length > 0) {
                queryAlarmStatus();
            }
        }
    }, unitInfo);

    /**
     * 查询BA报警监控状态
     */
    function queryAlarmStatus() {
        const info = new EiInfo();
        info.setByNode("inqu");
        info.set("clientType", "alarm");
        IMOMUtil.submitEiInfo(info, "VGDM06", "queryClientStatus", function (ei) {
            $("#detail_status-0-clientStatus").val(ei.get("detail_status-0-clientStatus"));
        });
    }
});
