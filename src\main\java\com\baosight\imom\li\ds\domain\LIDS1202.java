/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids1202;

import java.util.Map;

/**
* TLIDS1202
* 
*/
public class LIDS1202 extends Tlids1202 {
        public static final String SEQ_ID = "TLIDS1202_SEQ01";
        public static final String QUERY = "LIDS1202.query";
        public static final String COUNT = "LIDS1202.count";
        public static final String INSERT = "LIDS1202.insert";
        public static final String UPDATE = "LIDS1202.update";
        public static final String DELETE = "LIDS1202.delete";
        public static final String UPDATE_CRANE_PERFORMANCE_SUB_TABLE = "LIDS1202.updateCranePerformanceSubTable";

        public static final String RESULT_PACKISNULL = "LIDS1202.resultPackIsNull";

        public static final String UPDATE_STATUS_BY_CRANE_RESULT_ID = "LIDS1202.updateStatusByCraneResultId";
        public static final String QUERY_CRANE_PERFORMANCE_IN_PACK = "LIDS1202.queryCranePerformanceInPack";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS1202() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}