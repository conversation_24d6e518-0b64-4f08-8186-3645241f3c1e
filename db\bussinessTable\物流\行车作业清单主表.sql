create table meli.tlids1101
(
    SEG_NO           varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE        varchar(12)  default ' ' not null comment '业务单元代代码',
    CRANE_ORDER_ID   varchar(32)  default ' ' not null comment '行车作业清单号',
    LIST_SOURCE      varchar(50)  default ' ' not null comment '清单来源',
    VOUCHER_NUM      varchar(20)  default ' ' null comment '依据凭单号',
    CRANE_ID         varchar(20)  default ' ' not null comment '行车编号',
    CRANE_NAME       varchar(200) default ' ' not null comment '行车名称',
    BATCH_NUMBER     varchar(20)  default ' ' null comment '批次号',
    SERIAL_NUMBER    varchar(20)  default ' ' null comment '顺序号',
    MACHINE_CODE     varchar(30)  default ' ' null comment '机组代码',
    MACHINE_NAME     varchar(50)  default ' ' null comment '机组名称',
    UNPACK_AREA_ID   varchar(20)  default ' ' null comment '拆包区编号',
    UNPACK_AREA_NAME varchar(200) default ' ' null comment '拆包区名称',
    MOULD_ID         varchar(50)  default ' ' null comment '模具ID',
    MOULD_NAME       varchar(300) default ' ' null comment '模具名称',
    START_TIME       varchar(17)  default ' ' null comment '作业开始时间',
    END_TIME         varchar(17)  default ' ' null comment '作业结束时间',
    JOB_TIME         varchar(17)  default ' ' null comment '作业时间',
    START_AREA_TYPE  varchar(50)  default ' ' null comment '起始区域类型',
    START_AREA_CODE  varchar(20)  default ' ' null comment '起始区域类型代码',
    START_AREA_NAME  varchar(50)  default ' ' null comment '起始区域类型名称',
    END_AREA_TYPE    varchar(50)  default ' ' null comment '终到区域类型',
    END_AREA_CODE    varchar(20)  default ' ' null comment '终到区域类型代码',
    END_AREA_NAME    varchar(50)  default ' ' null comment '终到区域类型名称',
    STATUS           varchar(2)               null comment '状态',
    REC_CREATOR      varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME  varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR      varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME  varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG     varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER      varchar(10)  default ' ' null comment '租户',
    DEL_FLAG         smallint     default 0   null comment '删除标记',
    UUID             varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids1101_UUID_uindex
        unique (UUID)
)
    comment '行车作业清单主表' collate = utf8_bin;

