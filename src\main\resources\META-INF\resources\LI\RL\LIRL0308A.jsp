<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-carTraceNo" cname="车牌跟踪号" colWidth="3" placeholder="模糊查询"/>
            <EF:EFInput ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3" placeholder="模糊查询"/>

            <EF:EFDatePicker ename="inqu_status-0-statrBeginEntruckingTime" cname="进厂时间起始"  colWidth="3" />
            <EF:EFDatePicker ename="inqu_status-0-endBeginEntruckingTime" cname="进厂时间截止"  colWidth="3" />
            <EF:EFDatePicker ename="inqu_status-0-startCompleteUninstallTime" cname="出库时间起始"  colWidth="3" />
            <EF:EFDatePicker ename="inqu_status-0-endCompleteUninstallTime" cname="出库时间截止"  colWidth="3" />

            <EF:EFDatePicker ename="inqu_status-0-startCompleteUninstallTimeS" cname="入库时间起始"  colWidth="3" />
            <EF:EFDatePicker ename="inqu_status-0-endCompleteUninstallTimeS" cname="入库时间截止"  colWidth="3" />

            <EF:EFInput ename="inqu_status-0-vehicleNoS" cname="类型（卸货、行车工出库、仓库出库）" colWidth="3" placeholder="模糊查询"/>

        </div>

    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no"
                   autoBind="false" isFloat="true" personal="true">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" align="center" enable="false"/>
            <EF:EFComboColumn ename="status" cname="车辆跟踪状态"  align="center"
                              enable="false" width="120">
                <EF:EFCodeOption codeName="P008"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false" />

            <EF:EFColumn ename="driverName" cname="预约时间" align="center" enable="false"/>
            <EF:EFColumn ename="driverTel" cname="进厂时间" align="center" enable="false"/>
            <EF:EFColumn ename="driverIdentity" cname="装卸点" align="center" enable="false"  />
            <EF:EFColumn ename="putinId" cname="开始装卸货时间" align="center" enable="false" sort="false"/>
            <EF:EFColumn ename="putoutId" cname="结束装卸货时间" align="center" enable="false" sort="false"/>
            <EF:EFColumn ename="voucherNum" cname="提单号" align="center" enable="false" sort="false"/>
            <EF:EFColumn ename="packId" cname="入库单号" align="center" enable="false"  width="150" sort="false"/>
            <EF:EFColumn ename="matInnerId" cname="出库单号" align="center" enable="false"  width="150" sort="false"/>
            <EF:EFColumn ename="factoryOrderNum" cname="捆包号" align="center" enable="false" sort="false"/>
            <EF:EFColumn ename="factoryOrderNum" cname="重量" align="center" enable="false" sort="false"/>
            <EF:EFColumn ename="factoryOrderNum" cname="件数" align="center" enable="false" sort="false"/>

        </EF:EFGrid>
    </EF:EFRegion>
    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0001" id="userNum2" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
