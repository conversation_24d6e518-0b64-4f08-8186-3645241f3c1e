/**
 * Generate time : 2024-11-27 16:06:03
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0501;

import java.util.Map;

/**
 * TLIDS0501
 *
 */
public class LIDS0501 extends Tlids0501 {
    public static final String QUERY = "LIDS0501.query";
    public static final String COUNT = "LIDS0501.count";
    public static final String COUNT_UUID = "LIDS0501.count_uuid";
    public static final String INSERT = "LIDS0501.insert";
    public static final String UPDATE = "LIDS0501.update";
    public static final String UPDATE_STATUS = "LIDS0501.updateStatus";
    public static final String DELETE = "LIDS0501.delete";
    public static final String QUERY_BY_CROSS_AREA = "LIDS0501.queryByCrossArea";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS0501() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}