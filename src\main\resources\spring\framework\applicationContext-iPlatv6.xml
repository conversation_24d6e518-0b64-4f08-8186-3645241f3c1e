<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">


    <!--设备采集点位信息缓存-->
    <bean id="tagCacheEntity" class="com.baosight.iplat4j.core.cache.impl.RedisTempleteCache">
        <property name="cacheEname" value="imom:vgdm:tagCache"/><!--指定在redis缓存中的区别依据-->
        <property name="expireTime" value="${iplat.core.cache.redisExpireTime}"/><!--设置超时时间(毫秒)-->
        <property name="redisTemplate" ref="redisTemplate"/>
    </bean>
    <bean id="tagCacheRegistry" class="com.baosight.iplat4j.core.cache.CacheRegistry">
        <property name="cacheKey"
                  value="imom:vgdm:tagCache"/> <!--对应CacheManager.getCache(String cacheName)方法中的cacheName参数-->
        <property name="cache" ref="tagCacheEntity"/>  <!--缓存对象实例-->
    </bean>
</beans>