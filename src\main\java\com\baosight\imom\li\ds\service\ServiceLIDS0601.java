package com.baosight.imom.li.ds.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.li.domain.Tlids0601;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 库位附属信息管理
 */
public class ServiceLIDS0601 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0601().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0601.QUERY, new LIDS0601());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //增加校验,库位代码不能重复
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", segNo);//系统账套
                    queryMap.put("locationId", MapUtils.getString(itemMap, "locationId"));//库位代码
                    int count = super.count(LIDS0601.COUNT_EXISTS, queryMap);
                    if (count > 0) {
                        throw new PlatException(MapUtils.getString(itemMap, "locationId") + "库位代码已存在!");
                    }
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0601.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    if (StringUtils.isBlank(MapUtils.getString(itemMap, "factoryArea")) || StringUtils.isBlank(MapUtils.getString(itemMap, "factoryAreaName"))
                            || StringUtils.isBlank(MapUtils.getString(itemMap, "factoryBuilding")) || StringUtils.isBlank(MapUtils.getString(itemMap, "factoryBuildingName"))) {
                        throw new PlatException("库位："+MapUtils.getString(itemMap, "locationId")+",传入厂区代码、厂区名称、厂房代码、厂房名称为空，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0601.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     *
     * @param inInfo
     * @return
     */
    public EiInfo addLocationDetail(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            EiInfo sendInfo = new EiInfo();
            sendInfo.addBlock(EiConstant.resultBlock).addRows(inInfo.getBlock(EiConstant.resultBlock).getRows());
            sendInfo.set(EiConstant.serviceName, "LIDS0605");
            sendInfo.set(EiConstant.methodName, "insert");
            sendInfo = XLocalManager.call(sendInfo);
            if(sendInfo.getStatus() < EiConstant.STATUS_DEFAULT){
                throw new PlatException(sendInfo.getMsg());
            }
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 同步IMC库位信息
     *
     * @param inInfo
     * @return
     * @serviceId S_LI_DS_0001
     */
    public EiInfo syncStorageLocation(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //查询开关，需要同步IMC库位的账套
            Map queryMap = new HashMap();
            queryMap.put("processSwitchName", "SYNC_IMC_STORAGE_LOCATIONS");
            List<HashMap> syncSegNoList = dao.query("XTSS04.queryList", queryMap);

            //返回提示信息
            StringBuilder returnMsg = new StringBuilder("处理成功");
            for (HashMap segMap : syncSegNoList) {
                String switchValue = MapUtils.getString(segMap, "processSwitchValue", "");
                if (!"1".equals(switchValue)) {
                    continue;
                }
                //调用接口
                EiInfo sendInfo = new EiInfo();
                //服务号
                sendInfo.set(EiConstant.serviceId, "S_UC_PR_0415");
                //系统账套
                String segNo = MapUtils.getString(segMap, "segNo");
                sendInfo.set("segNo", segNo);
                sendInfo = EServiceManager.call(sendInfo, TokenUtils.getXplatToken());
                if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    sendInfo.setStatus(EiConstant.STATUS_FAILURE);
                    throw new PlatException(segNo + "同步库位信息失败：" + sendInfo.getMsg());
                }
                //返回库位信息
                List<Map> locationList = (List<Map>) sendInfo.get(EiConstant.resultBlock);
                log(segNo + "定时同步库位附属信息查询IMC库位信息返回参数：" + locationList);
                // 插入库位信息
                List<LIDS0601> locationAddList = new ArrayList<>();
                List<Map<String, String>> existingLocationKeys = new ArrayList<>();

                // 收集所有需要查询的条件
                for (Map map : locationList) {
                    String warehouseCode = MapUtils.getString(map, "warehouseCode");
                    String locationId = MapUtils.getString(map, "locationId");
                    Map<String, String> keyMap = new HashMap<>();
                    keyMap.put("segNo", segNo);
                    keyMap.put("warehouseCode", warehouseCode);
                    keyMap.put("locationId", locationId);
                    existingLocationKeys.add(keyMap);
                }

                // 分批处理查询条件
                queryMap.clear();
                int batchSize = 500;
                List<Map<String, String>> batchKeys = new ArrayList<>();
                Map<String, Integer> existingLocationMap = new HashMap<>();

                for (Map<String, String> keyMap : existingLocationKeys) {
                    batchKeys.add(keyMap);
                    if (batchKeys.size() >= batchSize) {
                        queryMap.put("locationKeys", batchKeys);
                        List<HashMap<String, Object>> existingLocations = dao.query(LIDS0601.QUERY_EXISTING_LOCATIONS, queryMap);
                        for (HashMap<String, Object> existingLocation : existingLocations) {
                            String warehouseCode = MapUtils.getString(existingLocation, "warehouseCode");
                            String locationId = MapUtils.getString(existingLocation, "locationId");
                            String existingKey = segNo + "_" + warehouseCode + "_" + locationId;
                            existingLocationMap.put(existingKey, 1);
                        }
                        batchKeys.clear();
                    }
                }

                // 处理剩余的条件
                if (!batchKeys.isEmpty()) {
                    queryMap.put("locationKeys", batchKeys);
                    List<HashMap<String, Object>> existingLocations = dao.query(LIDS0601.QUERY_EXISTING_LOCATIONS, queryMap);
                    for (HashMap<String, Object> existingLocation : existingLocations) {
                        String warehouseCode = MapUtils.getString(existingLocation, "warehouseCode");
                        String locationId = MapUtils.getString(existingLocation, "locationId");
                        String existingKey = segNo + "_" + warehouseCode + "_" + locationId;
                        existingLocationMap.put(existingKey, 1);
                    }
                }

                // 处理需要插入的库位
                for (Map map : locationList) {
                    String warehouseCode = MapUtils.getString(map, "warehouseCode");
                    String locationId = MapUtils.getString(map, "locationId");
                    String key = segNo + "_" + warehouseCode + "_" + locationId;
                    if (existingLocationMap.containsKey(key)) {
                        continue;
                    }
                    LIDS0601 lids0601 = new LIDS0601();
                    // 赋值
                    lids0601.setUnitCode(MapUtils.getString(map, "segNo"));
                    lids0601.setSegNo(MapUtils.getString(map, "segNo"));
                    lids0601.setWarehouseCode(warehouseCode);
                    lids0601.setWarehouseName(MapUtils.getString(map, "warehouseName"));
                    lids0601.setLocationId(locationId);
                    lids0601.setLocationName(MapUtils.getString(map, "locationName"));
                    lids0601.setUuid(UUIDUtils.getUUID());
                    lids0601.setRecCreateTime(MapUtils.getString(map,"recCreateTime"));
                    lids0601.setRecCreator(MapUtils.getString(map,"recCreator"));
                    lids0601.setRecCreatorName(MapUtils.getString(map,"recCreatorName"));
                    lids0601.setRecReviseTime(MapUtils.getString(map,"recReviseTime"));
                    lids0601.setRecRevisor(MapUtils.getString(map,"recRevisor"));
                    lids0601.setRecRevisorName(MapUtils.getString(map,"recRevisorName"));
                    lids0601.setStatus("10");
                    //坐标值设置为null
                    lids0601.setxInitialPoint(null);
                    lids0601.setxDestination(null);
                    lids0601.setyInitialPoint(null);
                    lids0601.setyDestination(null);
                    locationAddList.add(lids0601);
                }

                //插入库位附属信息
                if (CollectionUtil.isNotEmpty(locationAddList)) {
                    returnMsg.append(",").append(segNo).append("本次共同步：").append(locationAddList.size()).append("条库位信息!");
                    int count = dao.insertBatch(LIDS0601.INSERT, locationAddList);
                    if(count != locationAddList.size()){
                        throw new PlatException(segNo + "同步库位信息失败：" + "插入库位附属信息失败!");
                    }
                }
                log(segNo + "定时同步库位附属信息插入数据库参数：" + locationAddList);
            }

            outInfo.setMsg(returnMsg.toString());
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("同步库位失败：" + ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 根据行车确定库位起始坐标
     *
     * @param inInfo
     * @return
     */
    public EiInfo confirmStartPos(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);

            //系统账套
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            //行车编号
            String craneId = MapUtils.getString(queryMap, "craneId", "");
            //行车名称
            String craneName = MapUtils.getString(queryMap, "craneName", "");

            if (StringUtils.isBlank(segNo) || StringUtils.isBlank(craneId)) {
                throw new PlatException("系统账套和行车编号不能为空！");
            }

            //依据行车编号获取卡号
            queryMap.put("craneId", MapUtils.getString(MesConstant.CARD_MAP, craneId));
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
            sendInfo.set("paramMap", queryMap);
            sendInfo = XLocalManager.call(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(sendInfo.getMsg());
            }

            Map resultMap = sendInfo.getMap("resultMap");
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询不到行车实时位置信息！");
            }
            //行车所在坐标
            String XPosition = MapUtils.getString(resultMap, "x_value", "");
            String YPosition = MapUtils.getString(resultMap, "y_value", "");

            //修改勾选库位信息的XYZ位置
            List<HashMap> locationList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            locationList.forEach(locationMap -> {
                locationMap.put("xInitialPoint", new BigDecimal(XPosition).intValue());
                locationMap.put("yInitialPoint", new BigDecimal(YPosition).intValue());
            });
            int count = dao.updateBatch(LIDS0601.UPDATE, locationList);
            if (count != locationList.size()) {
                throw new PlatException("修改库位起始坐标失败！");
            }

            outInfo.addBlock(EiConstant.resultBlock).addRows(locationList);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 根据行车确定库位终点坐标
     *
     * @param inInfo
     * @return
     */
    public EiInfo confirmEndPos(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);

            //系统账套
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            //行车编号
            String craneId = MapUtils.getString(queryMap, "craneId", "");
            //行车名称
            String craneName = MapUtils.getString(queryMap, "craneName", "");

            if (StringUtils.isBlank(segNo) || StringUtils.isBlank(craneId)) {
                throw new PlatException("系统账套和行车编号不能为空！");
            }

            //依据行车编号获取卡号
            queryMap.put("craneId", MapUtils.getString(MesConstant.CARD_MAP, craneId));
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
            sendInfo.set("paramMap", queryMap);
            sendInfo = XLocalManager.call(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(sendInfo.getMsg());
            }

            Map resultMap = sendInfo.getMap("resultMap");
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询不到行车实时位置信息！");
            }
            //行车所在坐标
            String XPosition = MapUtils.getString(resultMap, "x_value", "");
            String YPosition = MapUtils.getString(resultMap, "y_value", "");

            //修改勾选库位信息的XYZ位置
            List<HashMap> locationList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            locationList.forEach(locationMap -> {
                locationMap.put("xDestination", new BigDecimal(XPosition).intValue());
                locationMap.put("yDestination", new BigDecimal(YPosition).intValue());
            });
            int count = dao.updateBatch(LIDS0601.UPDATE, locationList);
            if (count != locationList.size()) {
                throw new PlatException("修改库位终点坐标失败！");
            }

            outInfo.addBlock(EiConstant.resultBlock).addRows(locationList);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    /**
     * 库位页面后端导出
     *
     * @param inInfo
     * @return
     */
    public EiInfo postExport(EiInfo inInfo){
        try {
            Map<String,Object> loginMap = new HashMap();
            loginMap.put("userId", UserSession.getUserId());
            loginMap.put("userName",UserSession.getLoginCName());
            loginMap.put("loginName",UserSession.getLoginName());
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            if (StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            List<Map<String,Object>> hashMapList = dao.queryAll(LIDS0601.QUERY_EXPORT, queryBlock);
            inInfo.getBlock("exportColumnBlock").addRows(hashMapList);
            inInfo.getBlock("exportColumnBlock").addBlockMeta(getExportBlockMeta());
            Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, hashMapList, loginMap);
            inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return inInfo;
    }

    public EiBlockMeta getExportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossArea");
        eiColumn.setDescName("跨区编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossAreaName");
        eiColumn.setDescName("跨区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("posDirCode");
        eiColumn.setDescName("层数标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("managementStyle");
        eiColumn.setDescName("管理方式(10点状 20条状)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actionFlag");
        eiColumn.setDescName("板卷标记(0板 1卷)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifPlanFlag");
        eiColumn.setDescName("是否参与库位推荐(0参与 1不参与)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingChannelNo");
        eiColumn.setDescName("装卸通道编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingChannelName");
        eiColumn.setDescName("装卸通道名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingPointNo");
        eiColumn.setDescName("装卸点编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingPointName");
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_initialPoint");
        eiColumn.setDescName("X轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_destination");
        eiColumn.setDescName("X轴终到点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_initialPoint");
        eiColumn.setDescName("Y轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_destination");
        eiColumn.setDescName("Y轴终到点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(启用：10、停用：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specUpper");
        eiColumn.setDescName("宽度上限 单位(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specLower");
        eiColumn.setDescName("宽度下限 单位(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pointLowerLength");
        eiColumn.setDescName("点状库位长度下限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pointUpperLength");
        eiColumn.setDescName("点状库位长度上限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("standFlag");
        eiColumn.setDescName("是否立式库位。0：卧式、1：立式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("c_no");
        eiColumn.setDescName("所在跨区序列号");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }


}
