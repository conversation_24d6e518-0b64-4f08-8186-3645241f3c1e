<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-27 10:03:04
       Version :  1.0
    tableName :meli.tlids0201
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CROSS_AREA  VARCHAR   NOT NULL   primarykey,
     CROSS_AREA_NAME  VARCHAR   NOT NULL,
     FACTORY_AREA  VARCHAR   NOT NULL,
     FACTORY_AREA_NAME  VARCHAR   NOT NULL,
     FACTORY_BUILDING  VARCHAR   NOT NULL,
     FACTORY_BUILDING_NAME  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR
-->
<sqlMap namespace="LIDS0201">

    <sql id="condition">
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA LIKE '%$crossArea$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA LIKE '%$factoryArea$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME LIKE '%$factoryAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING LIKE '%$factoryBuilding$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME LIKE '%$factoryBuildingName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointNo">
            LOADING_POINT_NO LIKE '%$loadingPointNo$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointName">
            LOADING_POINT_NAME LIKE '%$loadingPointName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS > '00'
            AND DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS0201">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        CROSS_AREA as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区代码 -->
        FACTORY_AREA_NAME as "factoryAreaName",  <!-- 厂区名称 -->
        FACTORY_BUILDING as "factoryBuilding",  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME as "factoryBuildingName",  <!-- 厂房名称 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        LOADING_POINT_NO as "loadingPointNo",
        LOADING_POINT_NAME as "loadingPointName"
        FROM meli.tlids0201 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryToMap" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT DISTINCT
        CROSS_AREA as "crossArea",  <!-- 跨区代码 -->
        CROSS_AREA_NAME as "crossAreaName",  <!-- 跨区名称 -->
        REC_CREATE_TIME AS "recCreateTime"
        FROM meli.tlids0201 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                t.REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0201 WHERE 1=1
        <include refid="condition"/>
    </select>

<!--    条件全匹配-->
    <select id="countEqual" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0201 WHERE 1=1
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME = #crossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME = #factoryAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #actoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME = #factoryBuildingName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointNo">
            LOADING_POINT_NO = #loadingPointNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadingPointName">
            LOADING_POINT_NAME = #loadingPointName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS > '00'
            AND DEL_FLAG = '0'
        </isEmpty>
    </select>

    <!--根据UUID用来校验数据状态-->
    <select id="count_uuid" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids0201 WHERE 1=1
        <include refid="condition"/>
        AND UUID = #uuid#
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossAreaName">
            CROSS_AREA_NAME = #crossAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryAreaName">
            FACTORY_AREA_NAME = #factoryAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING = #factoryBuilding#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuildingName">
            FACTORY_BUILDING_NAME = #factoryBuildingName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids0201 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        CROSS_AREA,  <!-- 跨区代码 -->
        CROSS_AREA_NAME,  <!-- 跨区名称 -->
        FACTORY_AREA,  <!-- 厂区代码 -->
        FACTORY_AREA_NAME,  <!-- 厂区名称 -->
        FACTORY_BUILDING,  <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME,  <!-- 厂房名称 -->
        STATUS,  <!-- 状态 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        LOADING_POINT_NO,
        LOADING_POINT_NAME
        )
        VALUES (#segNo#, #unitCode#, #crossArea#, #crossAreaName#, #factoryArea#, #factoryAreaName#, #factoryBuilding#,
        #factoryBuildingName#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#,
        #uuid#,#loadingPointNo#,#loadingPointName#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids0201 WHERE
        CROSS_AREA = #crossArea#
    </delete>

    <update id="update">
        UPDATE meli.tlids0201
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        CROSS_AREA_NAME = #crossAreaName#,   <!-- 跨区名称 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区代码 -->
        FACTORY_AREA_NAME = #factoryAreaName#,   <!-- 厂区名称 -->
        FACTORY_BUILDING = #factoryBuilding#,   <!-- 厂房代码 -->
        FACTORY_BUILDING_NAME = #factoryBuildingName#,   <!-- 厂房名称 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        LOADING_POINT_NO = #loadingPointNo#,<!--装卸点编码-->
        LOADING_POINT_NAME = #loadingPointName#
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND CROSS_AREA = #crossArea#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

    <!--	修改状态-->
    <update id="updateStatus">
        UPDATE meli.tlids0201
        SET
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        <isNotEmpty prepend="," property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND CROSS_AREA = #crossArea#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

<!--    统计跨区是否被引用-->
    <select id="countAllByCrossArea" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT (SELECT COUNT(1)
        FROM meli.tlids0401
        WHERE SEG_NO = #segNo#
        AND FACTORY_AREA = #factoryArea#
        AND FACTORY_AREA_NAME = #factoryAreaName#
        AND FACTORY_BUILDING = #factoryBuilding#
        AND FACTORY_BUILDING_NAME = #factoryBuildingName#
        AND CROSS_AREA LIKE '%$crossArea$%'
        AND CROSS_AREA_NAME LIKE '%$crossAreaName$%'
        AND STATUS > '00'
        AND DEL_FLAG = '0') AS "count0401",
        (SELECT COUNT(1)
        FROM meli.tlids0501
        WHERE SEG_NO = #segNo#
        AND FACTORY_AREA = #factoryArea#
        AND FACTORY_AREA_NAME = #factoryAreaName#
        AND FACTORY_BUILDING = #factoryBuilding#
        AND FACTORY_BUILDING_NAME = #factoryBuildingName#
        AND CROSS_AREA = #crossArea#
        AND CROSS_AREA_NAME = #crossAreaName#
        AND STATUS > '00'
        AND DEL_FLAG = '0') AS "count0501",
        (SELECT COUNT(1)
        FROM meli.tlids0601
        WHERE SEG_NO = #segNo#
        AND FACTORY_AREA = #factoryArea#
        AND FACTORY_AREA_NAME = #factoryAreaName#
        AND FACTORY_BUILDING = #factoryBuilding#
        AND FACTORY_BUILDING_NAME = #factoryBuildingName#
        AND CROSS_AREA = #crossArea#
        AND CROSS_AREA_NAME = #crossAreaName#
        AND STATUS > '00'
        AND DEL_FLAG = '0') AS "count0601",
        (SELECT COUNT(1)
        FROM meli.tlids0701
        WHERE SEG_NO = #segNo#
        AND FACTORY_AREA = #factoryArea#
        AND FACTORY_AREA_NAME = #factoryAreaName#
        AND FACTORY_BUILDING = #factoryBuilding#
        AND FACTORY_BUILDING_NAME = #factoryBuildingName#
        AND CROSS_AREA = #crossArea#
        AND CROSS_AREA_NAME = #crossAreaName#
        AND STATUS > '00'
        AND DEL_FLAG = '0') AS "count0701"
        FROM dual
    </select>

    <!--页面更改装卸点    -->
    <update id="updateLoadingPoint">
        UPDATE meli.tlids0201
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        LOADING_POINT_NO = #loadingPointNo#,<!--装卸点编码-->
        LOADING_POINT_NAME = #loadingPointName#
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND UUID = #uuid#
        AND STATUS > '00'
        AND DEL_FLAG = '0'
    </update>

</sqlMap>