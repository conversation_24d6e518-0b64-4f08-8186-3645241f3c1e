$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    var addsubwindow = $("#ADDSUBWINDOW");

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 100,
                pageSizes:  [100,300,500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings:{
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings:{
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });
                // 获取勾选数据，
                $("#INSERTSAVEN").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0108", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                /**
                 * 使用
                 */
                $("#USE").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("修改失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block","result");
                    IMOMUtil.submitGridsData("result", "LIRL0108", "USE", true, function (e){
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 禁用
                 */
                $("#DISABLE").click(function () {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("禁用失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var eiInfo = new EiInfo();
                    eiInfo.set("block","result");
                    IMOMUtil.submitGridsData("result", "LIRL0108", "DISABLE", true, function (e){
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {

            }
        }
    };
    IPLATUI.EFWindow = {
        //关闭新增子项弹出框时的事件
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
        "unitInfo01": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo01");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = resultGrid.getCheckedRows(),
                        eiInfo = new EiInfo();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = resultGrid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },
    }

});
