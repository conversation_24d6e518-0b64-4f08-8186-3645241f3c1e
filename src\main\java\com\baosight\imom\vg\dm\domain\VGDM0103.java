package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0103;

/**
 * 设备附件信息表
 */
public class VGDM0103 extends Tvgdm0103 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0103.query";
    /**
     * 查询附件版本最大的数据记录
     */
    public static final String QUERY_MAX = "VGDM0103.queryMax";
    /**
     * 查询附件版本最大的数据记录条数
     */
    public static final String COUNT_MAX = "VGDM0103.countMax";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0103.insert";
    /**
     * 逻辑删除
     */
    public static final String UPD_FRO_DEL = "VGDM0103.updForDel";
    /**
     * 查询附件最大版本号
     */
    public static final String QUERY_MAX_VERSION = "VGDM0103.queryMaxVersion";

}
