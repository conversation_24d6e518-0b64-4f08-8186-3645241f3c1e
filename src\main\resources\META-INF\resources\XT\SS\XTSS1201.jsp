<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage>
    <EF:EFRegion id="inqu2" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu2_status-0-windowId" cname="ID" type="hidden"/>

            <EF:EFInput ename="inqu2_status-0-procesName" cname="实例名称" colWidth="3" ratio="4:8" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu2_status-0-nodeName" cname="节点名称" colWidth="3" ratio="4:8" placeholder="模糊条件"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result2" title="查询结果">
        <EF:EFGrid blockId="result2" checkMode="single, cell" serviceName="XTSS12" queryMethod="queryProcessKey"
                   autoBind="false" autoDraw="no">
            <EF:EFColumn ename="processKey" readonly="true" cname="实例ID" enable="false"/>
            <EF:EFColumn ename="procesName" readonly="true" cname="实例名称" enable="false"/>
            <EF:EFColumn ename="nodeKey" readonly="true" cname="节点ID" enable="false"/>
            <EF:EFColumn ename="nodeName" readonly="true" cname="节点名称" enable="false"/>
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>