package com.baosight.imom.vg.dm.service;

import com.baosight.hdsdk.domain.data.HDBasicTag;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.IhdSdkUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR> yzj
 * @Description : ihd数据查询页面后台
 * @Date : 2025/2/16
 * @Version : 1.0
 */
public class ServiceVGDM0398 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0398.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询数据
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
            // 获取点位信息
            String tagId = inInfo.getCellStr(EiConstant.queryBlock, 0, "tagId");
            String tagDesc = inInfo.getCellStr(EiConstant.queryBlock, 0, "tagDesc");
            if (StrUtil.isBlank(tagId) && StrUtil.isBlank(tagDesc)) {
                throw new PlatException("点位代码和点位名称不能同时为空");
            }
            // 是否模糊查询
            boolean fuzzy = inInfo.getCellStr(EiConstant.queryBlock, 0, "fuzzy") != null;
            // resultMap
            List<Map<String, Object>> resultList = new ArrayList<>();
            // 查询点位
            List<HDBasicTag> tagList = IhdSdkUtils.queryTags(segNo, tagId, tagDesc, fuzzy);
            if (CollectionUtils.isNotEmpty(tagList)) {
                for (HDBasicTag tag : tagList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("tagIhdId", tag.getId());
                    map.put("tagId", tag.getName());
                    map.put("tagDesc", tag.getDesc());
                    map.put("tagType", tag.getTagDataType());
                    resultList.add(map);
                }
            }
            EiBlock resultBlock = inInfo.addBlock(EiConstant.resultBlock);
            resultBlock.getRows().clear();
            resultBlock.set(EiConstant.limitStr, resultList.size());
            resultBlock.setRows(resultList);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg("查询成功");
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }
}
