create table TMEDV0107
(
    EXCEPTION_CONTACT_ID     VARCHAR(64)    default ' '    not null comment '异常信息联络单号',
    EXCEPTION_SOURCE         VARCHAR(16)    default ' '    not null comment '异常信息来源',
    DEVICE_CODE              VARCHAR(64)    default ' '    not null comment '分部设备代码',
    DEVICE_NAME              VARCHAR(128)   default ' '    not null comment '分部设备名称',
    E_ARCHIVES_NO            VARCHAR(20)    default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME           VARCHAR(200)   default ' '    not null comment '设备名称',
    CHECK_PLAN_ID            VARCHAR(200)   default ' '    not null comment '点检单号',
    SPOT_CHECK_CONTENT       VARCHAR(256)   default ' '    not null comment '点检内容',
    SPOT_CHECK_METHOD        VARCHAR(16)    default ' '    not null comment '点检方法',
    SPOT_CHECK_STANDARD_TYPE VARCHAR(16)    default ' '    not null comment '点检标准类型',
    JUDGMENT_STANDARD        VARCHAR(200)   default ' '    not null comment '判断标准',
    IS_OVERHAUL_HANDLE       VARCHAR(16)    default ' '    not null comment '是否检修处理',
    TEMPORARY_MEASURES       VARCHAR(512)   default ' '    not null comment '临时措施',
    ACTUALS_REMARK           VARCHAR(512)   default ' '    not null comment '点检实绩',
    HANDLE_MEASURES          VARCHAR(512)   default ' '    not null comment '处理措施',
    PROC_RESULT              VARCHAR(200)   default ' '    not null comment '处理结果',
    SPOT_CHECK_IMPLEMENTE    VARCHAR(32)    default ' '    not null comment '点检实施方',
    EXCEPTION_STATUS         VARCHAR(16)    default ' '    not null comment '点检异常息状态',
    MEASURE_ID               VARCHAR(10)    default ' '    not null comment '计量单位',
    UPPER_LIMIT              DECIMAL(20, 8) default 0      not null comment '上限值',
    LOWER_LIMIT              DECIMAL(20, 8) default 0      not null comment '下限值',
    -- 固定字段
    UUID                     VARCHAR(32)                   NOT NULL COMMENT '唯一编码',
    REC_CREATOR              VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME          VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR              VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME          VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID                VARCHAR(64)    DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG             VARCHAR(1)     DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='点检异常信息表' ENGINE = INNODB
                            DEFAULT CHARSET = UTF8
                            COLLATE UTF8_BIN;


-- 增加ER图外键
ALTER TABLE TMEDV0107 ADD unique KEY (EXCEPTION_CONTACT_ID);
ALTER TABLE TMEDV0107 ADD FOREIGN KEY (DEVICE_CODE) REFERENCES TMEDV0103(DEVICE_CODE);
ALTER TABLE TMEDV0107 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);
ALTER TABLE TMEDV0107 ADD FOREIGN KEY (CHECK_PLAN_ID) REFERENCES TMEDV0105(CHECK_PLAN_ID);