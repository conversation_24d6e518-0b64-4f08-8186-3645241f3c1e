/**
 * Generate time : 2024-12-31 15:35:00
 * Version : 1.0
 */
package com.baosight.imom.common.vi.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvipm0004
 */
public class Tvipm0004 extends DaoEPBase {

    private String machineOfflineSeqId = " ";        /* 机组离线时间序列号*/
    private String machineCode = " ";        /* 机组代码*/
    private String machineName = " ";        /* 机组名称*/
    private BigDecimal thickMin = new BigDecimal("0");        /* 最小厚度*/
    private BigDecimal thickMax = new BigDecimal("0");        /* 最大厚度*/
    private String offlineEvent = " ";        /* 离线项目*/
    private BigDecimal offlineTime = new BigDecimal("0");        /* 离线时间(单位分钟)*/
    private String machineOfflineStatus = " ";        /* 机组离线时状态*/
    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String processCategory = " ";        /* 工序大类代码*/
    private String remark = " ";        /* 备注*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String dataType = "10";        /* 数据类型*/
    private String processOrderId = " ";        /* 生产工单号*/
    private String packId = " ";        /* 原料捆包号*/
    private String inPartId = " ";        /* 原料物料号*/
    private String inSpecsDesc = " ";        /* 原料规格*/
    private BigDecimal netWeight = new BigDecimal("0");        /* 净重*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("machineOfflineSeqId");
        eiColumn.setDescName("机组离线时间序列号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("thickMin");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("最小厚度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("thickMax");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("最大厚度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("offlineEvent");
        eiColumn.setDescName("离线项目");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("offlineTime");
        eiColumn.setType("N");
        eiColumn.setScaleLength(1);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("离线时间(单位分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineOfflineStatus");
        eiColumn.setDescName("机组离线时状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataType");
        eiColumn.setDescName("数据类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("原料捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inPartId");
        eiColumn.setDescName("原料物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inSpecsDesc");
        eiColumn.setDescName("原料规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);
    }

    /**
     * the constructor
     */
    public Tvipm0004() {
        initMetaData();
    }

    /**
     * get the machineOfflineSeqId - 机组离线时间序列号
     *
     * @return the machineOfflineSeqId
     */
    public String getMachineOfflineSeqId() {
        return this.machineOfflineSeqId;
    }

    /**
     * set the machineOfflineSeqId - 机组离线时间序列号
     */
    public void setMachineOfflineSeqId(String machineOfflineSeqId) {
        this.machineOfflineSeqId = machineOfflineSeqId;
    }

    /**
     * get the machineCode - 机组代码
     *
     * @return the machineCode
     */
    public String getMachineCode() {
        return this.machineCode;
    }

    /**
     * set the machineCode - 机组代码
     */
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    /**
     * get the machineName - 机组名称
     *
     * @return the machineName
     */
    public String getMachineName() {
        return this.machineName;
    }

    /**
     * set the machineName - 机组名称
     */
    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    /**
     * get the thickMin - 最小厚度
     *
     * @return the thickMin
     */
    public BigDecimal getThickMin() {
        return this.thickMin;
    }

    /**
     * set the thickMin - 最小厚度
     */
    public void setThickMin(BigDecimal thickMin) {
        this.thickMin = thickMin;
    }

    /**
     * get the thickMax - 最大厚度
     *
     * @return the thickMax
     */
    public BigDecimal getThickMax() {
        return this.thickMax;
    }

    /**
     * set the thickMax - 最大厚度
     */
    public void setThickMax(BigDecimal thickMax) {
        this.thickMax = thickMax;
    }

    /**
     * get the offlineEvent - 离线项目
     *
     * @return the offlineEvent
     */
    public String getOfflineEvent() {
        return this.offlineEvent;
    }

    /**
     * set the offlineEvent - 离线项目
     */
    public void setOfflineEvent(String offlineEvent) {
        this.offlineEvent = offlineEvent;
    }

    /**
     * get the offlineTime - 离线时间(单位分钟)
     *
     * @return the offlineTime
     */
    public BigDecimal getOfflineTime() {
        return this.offlineTime;
    }

    /**
     * set the offlineTime - 离线时间(单位分钟)
     */
    public void setOfflineTime(BigDecimal offlineTime) {
        this.offlineTime = offlineTime;
    }

    /**
     * get the machineOfflineStatus - 机组离线时状态
     *
     * @return the machineOfflineStatus
     */
    public String getMachineOfflineStatus() {
        return this.machineOfflineStatus;
    }

    /**
     * set the machineOfflineStatus - 机组离线时状态
     */
    public void setMachineOfflineStatus(String machineOfflineStatus) {
        this.machineOfflineStatus = machineOfflineStatus;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the processCategory - 工序大类代码
     *
     * @return the processCategory
     */
    public String getProcessCategory() {
        return this.processCategory;
    }

    /**
     * set the processCategory - 工序大类代码
     */
    public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * get the processOrderId - 生产工单号
     *
     * @return the processOrderId
     */
    public String getProcessOrderId() {
        return this.processOrderId;
    }

    /**
     * set the processOrderId - 生产工单号
     */
    public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
    }

    /**
     * get the packId - 原料捆包号
     *
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 原料捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the inPartId - 原料物料号
     *
     * @return the inPartId
     */
    public String getInPartId() {
        return this.inPartId;
    }

    /**
     * set the inPartId - 原料物料号
     */
    public void setInPartId(String inPartId) {
        this.inPartId = inPartId;
    }

    /**
     * get the inSpecsDesc - 原料规格
     *
     * @return the inSpecsDesc
     */
    public String getInSpecsDesc() {
        return this.inSpecsDesc;
    }

    /**
     * set the inSpecsDesc - 原料规格
     */
    public void setInSpecsDesc(String inSpecsDesc) {
        this.inSpecsDesc = inSpecsDesc;
    }

    /**
     * get the netWeight - 净重
     *
     * @return the netWeight
     */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
     * set the netWeight - 净重
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setMachineOfflineSeqId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineOfflineSeqId")), machineOfflineSeqId));
        setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
        setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
        setThickMin(NumberUtils.toBigDecimal(StringUtils.toString(map.get("thickMin")), thickMin));
        setThickMax(NumberUtils.toBigDecimal(StringUtils.toString(map.get("thickMax")), thickMax));
        setOfflineEvent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("offlineEvent")), offlineEvent));
        setOfflineTime(NumberUtils.toBigDecimal(StringUtils.toString(map.get("offlineTime")), offlineTime));
        setMachineOfflineStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineOfflineStatus")), machineOfflineStatus));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setDataType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataType")), dataType));
        setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setInPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inPartId")), inPartId));
        setInSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inSpecsDesc")), inSpecsDesc));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("machineOfflineSeqId", StringUtils.toString(machineOfflineSeqId, eiMetadata.getMeta("machineOfflineSeqId")));
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("machineName", StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("thickMin", StringUtils.toString(thickMin, eiMetadata.getMeta("thickMin")));
        map.put("thickMax", StringUtils.toString(thickMax, eiMetadata.getMeta("thickMax")));
        map.put("offlineEvent", StringUtils.toString(offlineEvent, eiMetadata.getMeta("offlineEvent")));
        map.put("offlineTime", StringUtils.toString(offlineTime, eiMetadata.getMeta("offlineTime")));
        map.put("machineOfflineStatus", StringUtils.toString(machineOfflineStatus, eiMetadata.getMeta("machineOfflineStatus")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("processCategory", StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("dataType", StringUtils.toString(dataType, eiMetadata.getMeta("dataType")));
        map.put("processOrderId", StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("inPartId", StringUtils.toString(inPartId, eiMetadata.getMeta("inPartId")));
        map.put("inSpecsDesc", StringUtils.toString(inSpecsDesc, eiMetadata.getMeta("inSpecsDesc")));
        map.put("netWeight", StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
        return map;

    }
}