/**
* Generate time : 2025-06-18 17:04:58
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* LIDS1104
* 
*/
public class LIDS1104 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String craneId = " ";		/* 行车编号*/
                private String craneName = " ";		/* 行车名称*/
                private String grabSysId = " ";		/* 抓取流水号*/
                private String releaseSysId = " ";		/* 释放流水号*/
                private String x_value = " ";		/* X轴值*/
                private String y_value = " ";		/* Y轴值*/
                private String z_value = " ";		/* Z轴值*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                public static final String INSERT = "LIDS1104.insert";
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneId");
        eiColumn.setDescName("行车编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneName");
        eiColumn.setDescName("行车名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grabSysId");
        eiColumn.setDescName("抓取流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("releaseSysId");
        eiColumn.setDescName("释放流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_value");
        eiColumn.setDescName("X轴值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_value");
        eiColumn.setDescName("Y轴值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("z_value");
        eiColumn.setDescName("Z轴值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIDS1104() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the craneId - 行车编号
        * @return the craneId
        */
        public String getCraneId() {
        return this.craneId;
        }

        /**
        * set the craneId - 行车编号
        */
        public void setCraneId(String craneId) {
        this.craneId = craneId;
        }
        /**
        * get the craneName - 行车名称
        * @return the craneName
        */
        public String getCraneName() {
        return this.craneName;
        }

        /**
        * set the craneName - 行车名称
        */
        public void setCraneName(String craneName) {
        this.craneName = craneName;
        }
        /**
        * get the grabSysId - 抓取流水号
        * @return the grabSysId
        */
        public String getGrabSysId() {
        return this.grabSysId;
        }

        /**
        * set the grabSysId - 抓取流水号
        */
        public void setGrabSysId(String grabSysId) {
        this.grabSysId = grabSysId;
        }
        /**
        * get the releaseSysId - 释放流水号
        * @return the releaseSysId
        */
        public String getReleaseSysId() {
        return this.releaseSysId;
        }

        /**
        * set the releaseSysId - 释放流水号
        */
        public void setReleaseSysId(String releaseSysId) {
        this.releaseSysId = releaseSysId;
        }
        /**
        * get the x_value - X轴值
        * @return the x_value
        */
        public String getX_value() {
        return this.x_value;
        }

        /**
        * set the x_value - X轴值
        */
        public void setX_value(String x_value) {
        this.x_value = x_value;
        }
        /**
        * get the y_value - Y轴值
        * @return the y_value
        */
        public String getY_value() {
        return this.y_value;
        }

        /**
        * set the y_value - Y轴值
        */
        public void setY_value(String y_value) {
        this.y_value = y_value;
        }
        /**
        * get the z_value - Z轴值
        * @return the z_value
        */
        public String getZ_value() {
        return this.z_value;
        }

        /**
        * set the z_value - Z轴值
        */
        public void setZ_value(String z_value) {
        this.z_value = z_value;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setCraneId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneId")), craneId));
                setCraneName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneName")), craneName));
                setGrabSysId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("grabSysId")), grabSysId));
                setReleaseSysId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("releaseSysId")), releaseSysId));
                setX_value(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("x_value")), x_value));
                setY_value(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("y_value")), y_value));
                setZ_value(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("z_value")), z_value));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("craneId",StringUtils.toString(craneId, eiMetadata.getMeta("craneId")));
                map.put("craneName",StringUtils.toString(craneName, eiMetadata.getMeta("craneName")));
                map.put("grabSysId",StringUtils.toString(grabSysId, eiMetadata.getMeta("grabSysId")));
                map.put("releaseSysId",StringUtils.toString(releaseSysId, eiMetadata.getMeta("releaseSysId")));
                map.put("x_value",StringUtils.toString(x_value, eiMetadata.getMeta("x_value")));
                map.put("y_value",StringUtils.toString(y_value, eiMetadata.getMeta("y_value")));
                map.put("z_value",StringUtils.toString(z_value, eiMetadata.getMeta("z_value")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));

return map;

}
}