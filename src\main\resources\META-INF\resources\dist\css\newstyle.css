@charset "UTF-8";

html {
    height: 100%;
}

body {
    margin: 0;
    padding: 0;
    color: #333;
    font-size: 14px;
    font-family: Microsoft YaHei, sans-serif;
    /*background-color: #f4f4f4;*/
    /*background-image: url("../img/bg.png");*/
    /*background-repeat: no-repeat;*/
    background: #e5ebf3;
    background-size: 100% 100%;
    height: 100%;
}

ul,
li {
    list-style: none;
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
}

p {
    padding: 0;
    margin: 0;
}

.wrapper {
    width: 100%;
}

.header {
    width: 100%;
    height: 100px;
    background: #e5ebf3;
}

.logo-baosight {
    width: 272px;
    height: 60px;
    background: url(../img/logo-baosight.png) no-repeat;
    margin: 30px 0 0 32px;
    float: left;
}

.title {
    font-size: 30px;
    height: 100px;
    line-height: 100px;
    margin: 0 50px 0 0;
    float: right
}

.nav {
    width: 100%;
    height: 80px;
    line-height: 80px;
    background: #203890;
    color: #fff;
    float: left;
}

.navbox {
    width: 1150px;
    margin: 0 auto;
}

.navbox li {
    width: 20%;
    height: 80px;
    float: left;
    display: block;
    font-size: 22px;
    text-align: center;
}

.fontblue {
    color: #aff;
}

.container {
    width: 100%;
    margin: 140px auto;
    /*background-image: url("../img/bg.png")*/
    background: #e5ebf3;
}

.arrow {
    background: url(../img/arrow.png) no-repeat 50% 50%;
}

.main {
    width: 100%;
    /* height: 250px; */
    margin-top: -20px;
    display: flex;
    align-content: center;
    justify-content: space-around;
    background: #e5ebf3;
}

.ipt-div-btn {
    display: flex;
    justify-content: space-between;
}

.ipt1 {
    width: 100%;
    height: 50px;
    background-color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

.ipt2 {
    width: 100%;
    height: 50px;
    background-color: #d3d3d3;
    border: 1px solid #ffffff;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

.ipt2-tel {
    width: 100%;
    height: 50px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid #ffffff;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

.ipt2-cq {
    width: 100%;
    height: 50px;
    background-color: #d3d3d3;
    border: 1px solid #ffffff;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    border: 1px solid #ffffff;
}

p {
    color: #1E90FF; font-weight: bold;display: inline;
}

.div-wxts {
    font-size: 14px; color: #666666; margin-top: 100px; text-align: left; margin-left: 25px;
}

.ipt3 {
    width: 70%;
    height: 50px;
    border: 1px solid #ffffff;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    border: 1px solid #ffffff;
}

.btn {
    width: 220px;
    height: 180px;
    float: right;
}

.btn2 {
    display: flex;
    flex-direction: column; /* 设置为垂直排列 */
    justify-content: center; /* 横轴居中 */
    align-items: center; /* 纵轴居中 */
}

.btn2-flex-item {
    margin-bottom: 20px; /* 设置元素间距 */
    text-align: center; /* 文字居中 */
    line-height: 50px; /* 行高与元素高度相同 */
}

.btn button {
    width: 220px;
    height: 80px;
    background-image: url("../img/btnbg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    color: #fff;
    text-indent: 10px;
    font-family: Microsoft Yahei, sans-serif;
    cursor: pointer;
}

.btn button:active {
    background-image: url("../img/btnhover.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.btn2 button {
    width: 220px;
    height: 80px;
    background-image: url("../img/btnbg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    color: #fff;
    text-indent: 10px;
    font-family: Microsoft Yahei, sans-serif;
    cursor: pointer;
}

.btn2 button:active {
    background-image: url("../img/btnhover.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}


.next {
    margin-top: 30px;
}

.number {
    width: 1020px;
    height: 280px;
    margin: 20px 0 20px 40px;
}

.number ul li {
    float: left;
    width: 82px;
    height: 68px;
    line-height: 68px;
    background-image: url("../img/butbg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-align: center;
    margin-top: 2px;
    margin-right: 10px;
}

.number a {
    color: #ffffff;
    font-size: 20px;
}


/************************************************information1 end*/


/************************************************information2 begin*/

#province {
    /*width: 80px;*/
    height: 50px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(102, 102, 102, 1);
    border-radius: 4px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    /*text-align: center;*/
}

#guaProvince {
    /*width: 80px;*/
    height: 50px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(102, 102, 102, 1);
    border-radius: 4px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    /*text-align: center;*/
}

#license {
    width: 338px;
    height: 50px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(102, 102, 102, 1);
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    margin-left: 5px;
}

.loading {
    width: 49%;
    height: 50px;
    background-image: url("../img/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.loading:focus {
    background-image: url("../img/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.vehicleNoList {
    margin-top: 12px;
    width: 49%;
    height: 50px;
    background-image: url("../img/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.vehicleNo{
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: space-between;
    margin-left: 110px;
}
.active{
    background-image: url("../img/typeactive.png");
}

.discharge {
    width: 49%;
    height: 50px;
    background-image: url("../img/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    margin-left: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.discharge:focus {
    background-image: url("../img/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.loadingDis {
    width: 213px;
    height: 50px;
    background-image: url("../img/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    margin-left: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.loadingDis:focus {
    background-image: url("../img/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.finishProduct {
    width: 213px;
    height: 50px;
    background-image: url("../img/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.finishProduct:focus {
    background-image: url("../img/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.rawMaterial {
    width: 213px;
    height: 50px;
    background-image: url("../img/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.rawMaterial:focus {
    background-image: url("../img/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.information2 {
    float: left;
    height: 250px;
    background-image: url("../img/informationbg.jpg");
    background-repeat: no-repeat;
    padding: 55px 25px;
}

.information3-zdy {
    float: left;
    width: 35%;
    background: #30588B;
    padding: 55px 25px;
}

.information3-zdy ul li {
    margin-bottom: 30px;
    margin-left: 10px;
}

.information3-zdy span {
    float: left;
    width: 100px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}

.information4-zdy {
    float: left;
    width: 35%;
    background: #30588B;
    padding: 55px 25px;
    padding-bottom: 0;
}

.information4-zdy ul li {
    margin-bottom: 30px;
    margin-left: 10px;
    display: flex;
}

.information4-zdy span {
    float: left;
    width: 100px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}

.information5-zdy ul li {
    margin-bottom: 30px;
    margin-left: 10px;
}

.information5-zdy span {
    float: left;
    width: 100%;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}

.information5-zdy {
    width: 35%;
    float: left;
    height: 250px;
    background: #30588B;
    padding: 55px 25px;
}


.information2 ul li {
    margin-bottom: 30px;
    margin-left: 10px;
}

.information2 span {
    float: left;
    width: 72px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}

.information3 {
    float: left;
    /*height: 250px;*/
    background-color: #2D5588;
    /* background-repeat: no-repeat; */
    padding: 55px 25px;
    overflow-x: auto;
}
.information3::-webkit-scrollbar {
    display: none;
  }

.information3 ul li {
    margin-bottom: 30px;
    margin-left: 10px;
}

.information3 span {
    float: left;
    width: 80px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}


/************************************************information2 end*/

.information-bill {
    float: left;
    background-image: url("../img/informationbg.jpg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 20px 35px;
}

.information-bill span {
    min-width: 80px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
}

.information-bill .tag {
    float: left;
    margin-right: 10px;
}

 table{
     width: 100%;
     border-collapse: collapse;
     float: left;
     display: none;
}
 tr{
     box-sizing: border-box;
     table-layout: fixed;
     display: table;
     width: 100%;
 }
 table td{
     font-size: 18px;
}
table th, td
table th, td{
    padding: 8px 0;
    text-align: center;
    background-color: white;
    font-size: 20px;
    border: 1px solid black;
}
table th{
    background-color: #f2f2f2;
}
::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
}
::-webkit-scrollbar-thumb {
    background-color: #27314d;
}
table tbody{
    display: inline-block;
    max-width: calc(100% + 8px);
    max-height: 260px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
@media print {
    body * {
        visibility: hidden;
    }
    .printable, .printable * {
        visibility: visible;
    }
    .printable {
        position: absolute;
        left: 0;
        top: 0;
    }
}

/* 隐藏滚动条 */
.printable tbody::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 (Chrome, Safari, Edge) */
}
.printable tbody {
    -ms-overflow-style: none;  /* 隐藏滚动条 (IE 和 Edge) */
    scrollbar-width: none;     /* 隐藏滚动条 (Firefox) */
}

.tips {
    float: right;
    width: 1080px;
    height: 30px;
    margin: -30px auto 10px;
}

.tips p {
    display: block;
    text-align: right;
    margin-right: -10px;
    font-size: 18px;
    font-weight: 400;
    color: #ecb627;
    line-height: 36px;
}

i {
    font-style: normal;
}

.ibox {
    width: 1080px;
    height: 360px;
    background-color: #c3d0e2;
    padding: 40px;
}

.check {
    float: left;
}

.check p {
    font-size: 20px;
    color: #333333;
    margin-bottom: 30px;
}

.check i {
    margin-left: 30px;
}
.tanchuang{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
}
.tanchuang_box{
    float: left;
    background-color: #2D5588;
    width: 800px;
    background-size: 100% 100%;
    padding: 20px 35px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.tanchuang_top{
    display: flex;
    align-items: center;
}
.tanchuang_top div{
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    float: left;
    margin-right: 10px;
    white-space: nowrap;
}
.tanchuang_bottom{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tanchuang_bottom button{
    width: 220px;
    height: 80px;
    background-image: url(../img/btnbg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    color: #fff;
    text-indent: 10px;
    font-family: Microsoft Yahei, sans-serif;
    cursor: pointer;
    margin-top: 20px;
}

/*返回主页按钮*/
.header-return {
    margin-top: 30px; float: right;margin-right: 30px;
}

.return-home-btn {
    display: inline-block;
    padding: 10px 20px;
    font-size: 16px;
    color: #ffffff;
    background-color: #007BFF;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.3s;
}

.return-home-btn:hover {
    background-color: #0056b3;
}
