/**
* Generate time : 2024-12-09 13:34:15
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids0801
* 
*/
public class Tlids0801 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String segName = " ";		/* 业务单元简称*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String warehouseCode = " ";		/* 仓库代码*/
                private String warehouseName = " ";		/* 仓库名称*/
                private String areaType = " ";		/* 区域类型*/
                private String areaCode = " ";		/* 区域代码*/
                private String areaName = " ";		/* 区域名称*/
                private String mouldId = " ";		/* 模具ID*/
                private String mouldName = " ";		/* 模具名称*/
                private String x_position = "0";		/* X轴坐标*/
                private String y_position = "0";		/* Y轴坐标*/
                private String z_position = "0";		/* Z轴坐标*/
                private BigDecimal mouldSpecWeightSum = new BigDecimal(0.00000000);		/* 模具重量*/
                private String posDirCode = " ";		/* 层数标记*/
                private String craneResultId = " ";		/* 行车实绩单号*/
                private String status = " ";		/* 状态*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";		/* ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaType");
        eiColumn.setDescName("区域类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaCode");
        eiColumn.setDescName("区域代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaName");
        eiColumn.setDescName("区域名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldId");
        eiColumn.setDescName("模具ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldName");
        eiColumn.setDescName("模具名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_position");
        eiColumn.setDescName("X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_position");
        eiColumn.setDescName("Y轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("z_position");
        eiColumn.setDescName("Z轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldSpecWeightSum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("模具重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("posDirCode");
        eiColumn.setDescName("层数标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneResultId");
        eiColumn.setDescName("行车实绩单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tlids0801() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the warehouseCode - 仓库代码
        * @return the warehouseCode
        */
        public String getWarehouseCode() {
        return this.warehouseCode;
        }

        /**
        * set the warehouseCode - 仓库代码
        */
        public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
        }
        /**
        * get the warehouseName - 仓库名称
        * @return the warehouseName
        */
        public String getWarehouseName() {
        return this.warehouseName;
        }

        /**
        * set the warehouseName - 仓库名称
        */
        public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        }
        /**
        * get the areaType - 区域类型
        * @return the areaType
        */
        public String getAreaType() {
        return this.areaType;
        }

        /**
        * set the areaType - 区域类型
        */
        public void setAreaType(String areaType) {
        this.areaType = areaType;
        }
        /**
        * get the areaCode - 区域代码
        * @return the areaCode
        */
        public String getAreaCode() {
        return this.areaCode;
        }

        /**
        * set the areaCode - 区域代码
        */
        public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
        }
        /**
        * get the areaName - 区域名称
        * @return the areaName
        */
        public String getAreaName() {
        return this.areaName;
        }

        /**
        * set the areaName - 区域名称
        */
        public void setAreaName(String areaName) {
        this.areaName = areaName;
        }
        /**
        * get the mouldId - 模具ID
        * @return the mouldId
        */
        public String getMouldId() {
        return this.mouldId;
        }

        /**
        * set the mouldId - 模具ID
        */
        public void setMouldId(String mouldId) {
        this.mouldId = mouldId;
        }
        /**
        * get the mouldName - 模具名称
        * @return the mouldName
        */
        public String getMouldName() {
        return this.mouldName;
        }

        /**
        * set the mouldName - 模具名称
        */
        public void setMouldName(String mouldName) {
        this.mouldName = mouldName;
        }
        /**
        * get the x_position - X轴坐标
        * @return the x_position
        */
        public String getX_position() {
        return this.x_position;
        }

        /**
        * set the x_position - X轴坐标
        */
        public void setX_position(String x_position) {
        this.x_position = x_position;
        }
        /**
        * get the y_position - Y轴坐标
        * @return the y_position
        */
        public String getY_position() {
        return this.y_position;
        }

        /**
        * set the y_position - Y轴坐标
        */
        public void setY_position(String y_position) {
        this.y_position = y_position;
        }
        /**
        * get the z_position - Z轴坐标
        * @return the z_position
        */
        public String getZ_position() {
        return this.z_position;
        }

        /**
        * set the z_position - Z轴坐标
        */
        public void setZ_position(String z_position) {
        this.z_position = z_position;
        }
        /**
        * get the packWeight - 模具重量
        * @return the packWeight
        */
        public BigDecimal getMouldSpecWeightSum() {
        return this.mouldSpecWeightSum;
        }

        /**
        * set the packWeight - 模具重量
        */
        public void setMouldSpecWeightSum(BigDecimal mouldSpecWeightSum) {
        this.mouldSpecWeightSum = mouldSpecWeightSum;
        }
        /**
        * get the posDirCode - 层数标记
        * @return the posDirCode
        */
        public String getPosDirCode() {
        return this.posDirCode;
        }

        /**
        * set the posDirCode - 层数标记
        */
        public void setPosDirCode(String posDirCode) {
        this.posDirCode = posDirCode;
        }
        /**
        * get the craneResultId - 行车实绩单号
        * @return the craneResultId
        */
        public String getCraneResultId() {
        return this.craneResultId;
        }

        /**
        * set the craneResultId - 行车实绩单号
        */
        public void setCraneResultId(String craneResultId) {
        this.craneResultId = craneResultId;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }

        public String getSegName() {
                return segName;
        }

        public void setSegName(String segName) {
                this.segName = segName;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
                setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
                setAreaType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaType")), areaType));
                setAreaCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaCode")), areaCode));
                setAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaName")), areaName));
                setMouldId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldId")), mouldId));
                setMouldName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldName")), mouldName));
                setX_position(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("x_position")), x_position));
                setY_position(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("y_position")), y_position));
                setZ_position(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("z_position")), z_position));
                setMouldSpecWeightSum(NumberUtils.toBigDecimal(StringUtils.toString(map.get("mouldSpecWeightSum")), mouldSpecWeightSum));
                setPosDirCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("posDirCode")), posDirCode));
                setCraneResultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneResultId")), craneResultId));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
                map.put("warehouseName",StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
                map.put("areaType",StringUtils.toString(areaType, eiMetadata.getMeta("areaType")));
                map.put("areaCode",StringUtils.toString(areaCode, eiMetadata.getMeta("areaCode")));
                map.put("areaName",StringUtils.toString(areaName, eiMetadata.getMeta("areaName")));
                map.put("mouldId",StringUtils.toString(mouldId, eiMetadata.getMeta("mouldId")));
                map.put("mouldName",StringUtils.toString(mouldName, eiMetadata.getMeta("mouldName")));
                map.put("x_position",StringUtils.toString(x_position, eiMetadata.getMeta("x_position")));
                map.put("y_position",StringUtils.toString(y_position, eiMetadata.getMeta("y_position")));
                map.put("z_position",StringUtils.toString(z_position, eiMetadata.getMeta("z_position")));
                map.put("mouldSpecWeightSum",StringUtils.toString(mouldSpecWeightSum, eiMetadata.getMeta("mouldSpecWeightSum")));
                map.put("posDirCode",StringUtils.toString(posDirCode, eiMetadata.getMeta("posDirCode")));
                map.put("craneResultId",StringUtils.toString(craneResultId, eiMetadata.getMeta("craneResultId")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));

return map;

}
}