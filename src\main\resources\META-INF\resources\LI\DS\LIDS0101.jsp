<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFPopupInput ename="inqu_status-0-factoryArea" cname="厂区代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="factoryAreaInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-factoryAreaName,inqu_status-0-factoryBuilding,inqu_status-0-factoryBuildingName"
                             popupTitle="厂区查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-factoryAreaName" cname="厂区名称" colWidth="3" disabled="true"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-factoryBuilding" cname="厂房代码" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-factoryBuildingName" cname="厂房名称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-areaCode" cname="区域代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-areaName" cname="区域名称" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-areaType" cname="区域类型" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P031"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P032" condition="ITEM_CODE != '20'"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="left" width="100" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="left" width="100" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="left" width="150" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="left" width="150" enable="false"
                             readonly="true" required="true"/>
                <EF:EFComboColumn ename="areaType" cname="区域类型" align="center" width="150" enable="true"
                                  required="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P031"/>
                </EF:EFComboColumn>
                <EF:EFMultiSelectColumn ename="aisleType" cname="装卸货通道类型" align="center" width="200"
                                        enable="true" optionLabel="">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P043"/>
                </EF:EFMultiSelectColumn>
                <EF:EFColumn ename="areaCode" cname="区域代码" align="center" width="150" enable="false"
                             primaryKey="true"/>
                <EF:EFColumn ename="areaName" cname="区域名称" align="left" width="150" enable="true"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="xInitialPoint" cname="X轴起始点" align="left" width="150" enable="true"
                             required="true"/>
                <EF:EFColumn ename="xDestination" cname="X轴终到点" align="left" width="150" enable="true"
                             required="true"/>
                <EF:EFColumn ename="yInitialPoint" cname="Y轴起始点" align="left" width="150" enable="true"
                             required="true"/>
                <EF:EFColumn ename="yDestination" cname="Y轴终到点" align="left" width="150" enable="true"
                             required="true"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFCodeOption codeName="P032" condition="ITEM_CODE != '20'"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--厂区厂房信息弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factoryAreaInfo" width="90%" height="60%"/>
    <%--厂区厂房信息弹窗(表格用)--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factoryAreaInfoGrid" width="90%" height="60%"/>
</EF:EFPage>
