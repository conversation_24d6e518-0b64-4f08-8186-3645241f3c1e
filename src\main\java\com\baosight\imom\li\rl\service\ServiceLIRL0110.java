package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0110;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * @Author: 韩亚宁
 * @Description: ${司机维护部门分类}
 * @Date: 2024/12/19 9:26
 * @Version: 1.0
 */
public class ServiceLIRL0110 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        /*list.add("TEST_TYPE1");
        list.add("TEST_TYPE2");
        EiInfo eiInfo = CodeValueUtils.queryToBlock("QL00000", list, inInfo);*/
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0110().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            outInfo = super.query(inInfo, LIRL0110.QUERY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo1 = MapUtils.getString(hashMap, "segNo");
                String unitCode = MapUtils.getString(hashMap, "unitCode");
                if (org.apache.commons.lang.StringUtils.isBlank(segNo1)||org.apache.commons.lang.StringUtils.isBlank(unitCode)){
                    String massage = "缺少业务单元代码或部门代码不能新增！";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ10";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0110.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo1 = MapUtils.getString(hashMap, "segNo");
                String unitCode = MapUtils.getString(hashMap, "unitCode");
                if (org.apache.commons.lang.StringUtils.isBlank(segNo1)||org.apache.commons.lang.StringUtils.isBlank(unitCode)){
                    String massage = "缺少业务单元代码或部门代码不能新增！";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }

                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0110> query = dao.query(LIRL0110.QUERY, map);
                for (LIRL0110 LIRL0110 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0110);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0110.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0110> query = dao.query(LIRL0110.QUERY, map);
                for (LIRL0110 LIRL0109 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0109);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0110.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRM(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0110> query = dao.query(LIRL0110.QUERY, map);
                for (LIRL0110 lirl0110 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0110);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String applyDept = MapUtils.getString(hashMap, "applyDept", "");
                String subCategory = MapUtils.getString(hashMap, "subCategory", "");
                Map countMap = new HashMap();
                countMap.put("segNo", segNo);
                countMap.put("status", 20);
                countMap.put("applyDept", applyDept);
                countMap.put("subCategory", subCategory);
                countMap.put("delFlag", 0);
                //同一部门同一分类只能存在一条
                int count = super.count(LIRL0110.COUNT, countMap);
                if (count > 0) {
                    String massage = "同一部门同一分管类型只能存在一条确认数据！";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", 20);//状态
                RecordUtils.setRevisor(hashMap);
                dao.update(LIRL0110.UPDATE, hashMap);
            }
            /*inInfo = super.update(inInfo, LIRL0105.UPDATE);*/
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRMNO(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0110> query = dao.query(LIRL0110.QUERY, map);
                for (LIRL0110 lirl0110 : query) {
                    /*EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0105);*/
                    String status = lirl0110.getStatus();
                    //TODO
                    if (!"20".equals(status)) {
                        String massage = MessageCodeConstant.errorMessage.MSG_ERROR_STATUS_CAN_BE_COUNTER_CONFIRMED;
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", 10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0110.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


}