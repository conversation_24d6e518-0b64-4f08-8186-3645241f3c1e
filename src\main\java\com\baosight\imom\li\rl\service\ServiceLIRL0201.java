package com.baosight.imom.li.rl.service;


import cn.hutool.core.lang.hash.Hash;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.rl.dao.LIRL0201;
import com.baosight.imom.li.rl.dao.LIRL0202;
import com.baosight.imom.li.rl.dao.LIRL0304;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.baosight.imom.common.constants.MesConstant.Iplat.RESULT_BLOCK;

/**
 * @Author: 张博翔
 * @Description: ${预约维护表}
 * @Date: 2024/8/19 9:26
 * @Version: 1.0
 */
public class    ServiceLIRL0201 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceLIRL0201.class);

    private static void extracted(List<Map<String,Object>> hashMapList, String b) {
        for (Map hashMap : hashMapList) {
            //判断计算时间为0 给空值
            if ("0".equals(MapUtils.getString(hashMap, "theTimeFromRegistrationToEntry", ""))) {
                hashMap.put("theTimeFromRegistrationToEntry", " ");
            }
            if ("0".equals(MapUtils.getString(hashMap, "enterFactoryCompleteUninstallTime", ""))) {
                hashMap.put("enterFactoryCompleteUninstallTime", " ");
            }
            if ("0".equals(MapUtils.getString(hashMap, "enterFactoryLeaveFactoryDate", ""))) {
                hashMap.put("enterFactoryLeaveFactoryDate", " ");
            }
            if ("1".equals(b)) {
                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "撤销");
                        break;
                    case "20":
                        hashMap.put("status", "生效");
                        break;
                    case "99":
                        hashMap.put("status", "完成");
                        break;
                }
                String isReservation = MapUtils.getString(hashMap, "isReservation", "");
                switch (isReservation) {
                    case "00":
                        hashMap.put("isReservation", "无预约单");
                        break;
                    case "10":
                        hashMap.put("isReservation", "有预约单");
                        break;
                }
                String typeOfHandling = MapUtils.getString(hashMap, "typeOfHandling", "");
                switch (typeOfHandling) {
                    case "10":
                        hashMap.put("typeOfHandling", "钢材装货");
                        break;
                    case "20":
                        hashMap.put("typeOfHandling", "钢材卸货");
                        break;
                    case "30":
                        hashMap.put("typeOfHandling", "钢材卸货+装货");
                        break;
                    case "40":
                        hashMap.put("typeOfHandling", "托盘运输");
                        break;
                    case "50":
                        hashMap.put("typeOfHandling", "资材卸货");
                        break;
                    case "60":
                        hashMap.put("typeOfHandling", "废料提货");
                        break;
                    case "70":
                        hashMap.put("typeOfHandling", "欧冶提货");
                    case "80":
                        hashMap.put("typeOfHandling", "其他物品运输");
                        break;
                }

                String lateEarlyFlag = MapUtils.getString(hashMap, "lateEarlyFlag", "");
                switch (lateEarlyFlag) {
                    case "0":
                        hashMap.put("lateEarlyFlag", "未标记");
                        break;
                    case "10":
                        hashMap.put("lateEarlyFlag", "迟到");
                        break;
                    case "20":
                        hashMap.put("lateEarlyFlag", "早到");
                        break;
                    case "30":
                        hashMap.put("lateEarlyFlag", "正常");
                        break;
                }
                String carTraceStatus = MapUtils.getString(hashMap, "carTraceStatus", "");
                switch (carTraceStatus) {
                    case "00":
                        hashMap.put("carTraceStatus", "撤销");
                        break;
                    case "05":
                        hashMap.put("carTraceStatus", "新增");
                        break;
                    case "10":
                        hashMap.put("carTraceStatus", "进厂登记");
                        break;
                    case "20":
                        hashMap.put("carTraceStatus", "车辆进厂");
                        break;
                    case "30":
                        hashMap.put("carTraceStatus", "开始装卸货");
                        break;
                    case "40":
                        hashMap.put("carTraceStatus", "结束装卸货");
                        break;
                    case "50":
                        hashMap.put("carTraceStatus", "车辆出厂");
                        break;
                    case "60":
                        hashMap.put("carTraceStatus", "车辆签收");
                        break;
                }
            }
        }
    }

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0201().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String segName = MapUtils.getString(queryBlock, "segName", "");
            String status = MapUtils.getString(queryBlock, "status", "");
            if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            String reservationDateRange = MapUtils.getString(queryBlock, "reservationDateRange", "");
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDate", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, 1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDate", format);
            }
            String isHouseCars = MapUtils.getString(queryBlock, "isHouseCars", "");
            if (StringUtils.isNotEmpty(isHouseCars)) {
                if ("10".equals(isHouseCars)) {
                    queryBlock.put("isReservation", 10);
                    queryBlock.put("carTraceNoIsNull", 1);//跟踪单为空
                } else if ("20".equals(isHouseCars)) {
                    queryBlock.put("carTraceStatus", "10");//车辆跟踪状态
                } else if ("30".equals(isHouseCars)) {
                    queryBlock.put("carTraceStatus", "20,30,40");//车辆跟踪状态
                } else if ("40".equals(isHouseCars)) {
                    queryBlock.put("carTraceOutStatus", "50,60");//车辆跟踪状态
                } else if ("50".equals(isHouseCars)) {
                    queryBlock.put("isReservation", 10);
                    queryBlock.put("reservationDate", format);
                    queryBlock.put("lirl0302IsNull", "1");
                } else if ("60".equals(isHouseCars)) {
                    queryBlock.put("leaveFactoryDate", format);
                }
            }
            if ("20+99".equals(status)) {
                ArrayList<String> arrayList = new ArrayList<>();
                arrayList.add("20");
                arrayList.add("99");
                queryBlock.put("status", "");
                queryBlock.put("arrListStatus", arrayList);
            }
            //预约单管理查询
            /*queryBlock.put("isReservation","10");*/
            outInfo = super.query(inInfo, LIRL0201.QUERY, new LIRL0201());
            List<Map<String,Object>> hashMapList = outInfo.getBlock(EiConstant.resultBlock).getRows();
            extracted(hashMapList, "0");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    public EiInfo postExport(EiInfo inInfo) {


        Map<String,Object> loginMap = new HashMap();
        loginMap.put("userId", UserSession.getUserId());
        loginMap.put("userName",UserSession.getLoginCName());
        loginMap.put("loginName",UserSession.getLoginName());

        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String segName = MapUtils.getString(queryBlock, "segName", "");
            String status = MapUtils.getString(queryBlock, "status", "");
            if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            String reservationDateRange = MapUtils.getString(queryBlock, "reservationDateRange", "");
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDate", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, 1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDate", format);
            }
            String isHouseCars = MapUtils.getString(queryBlock, "isHouseCars", "");
            if (StringUtils.isNotEmpty(isHouseCars)) {
                if ("10".equals(isHouseCars)) {
                    queryBlock.put("isReservation", 10);
                    queryBlock.put("carTraceNoIsNull", 1);//跟踪单为空
                } else if ("20".equals(isHouseCars)) {
                    queryBlock.put("carTraceStatus", "10");//车辆跟踪状态
                } else if ("30".equals(isHouseCars)) {
                    queryBlock.put("carTraceStatus", "20,30,40");//车辆跟踪状态
                } else if ("40".equals(isHouseCars)) {
                    queryBlock.put("carTraceOutStatus", "50,60");//车辆跟踪状态
                } else if ("50".equals(isHouseCars)) {
                    queryBlock.put("reservationDate", format);
                    queryBlock.put("lirl0302IsNull", "1");
                } else if ("60".equals(isHouseCars)) {
                    queryBlock.put("leaveFactoryDate", format);
                }
            }
            if ("20+99".equals(status)) {
                ArrayList<String> arrayList = new ArrayList<>();
                arrayList.add("20");
                arrayList.add("99");
                queryBlock.put("status", "");
                queryBlock.put("arrListStatus", arrayList);
            }
            //预约单管理查询
            /*queryBlock.put("isReservation","10");*/
            List<Map<String,Object>> hashMapList = dao.queryAll("LIRL0201.queryExport", queryBlock);
            inInfo.getBlock("exportColumnBlock").addRows(hashMapList);
            inInfo.getBlock("exportColumnBlock").addBlockMeta(getExportBlockMeta());
            if (CollectionUtils.isNotEmpty(hashMapList)){
                extracted(hashMapList,"1");
            }
            Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, hashMapList, loginMap);
            inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return inInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 20);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ0201";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("reservationNumber", uuid);//预约单号
                hashMap.put("uuid", StrUtil.getUUID());//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0201.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0201> query = dao.query(LIRL0201.QUERY, map);
                for (LIRL0201 lirl0201 : query) {
                    /*EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0201);*/
                    if (!"20".equals(lirl0201.getStatus())) {
                        String massage = MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ACTIVE_STATUS;
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0201.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0201> query = dao.query(LIRL0201.QUERY, map);
                for (LIRL0201 lirl0201 : query) {
                    if (!"20".equals(lirl0201.getStatus())) {
                        String massage = MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ACTIVE_STATUS;
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0201.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 审核.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0201> query = dao.query(LIRL0201.QUERY, hashMap);
                for (LIRL0201 lirl0201 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, ConvertUtils.convert(lirl0201.toMap(), LIRL0201.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", MesConstant.Status.K20);//审核状态
                RecordUtils.setRevisor(hashMap);
                //先判断是否存在?存在更新,不存在插入
                Map<Object, Object> hashMapLIRL0306 = new HashMap<>();
                hashMapLIRL0306.put("segNo", MapUtils.getString(hashMap, "segNo"));
                hashMapLIRL0306.put("handPointId", MapUtils.getString(hashMap, "handPointId"));
            }
            inInfo = super.update(inInfo, LIRL0304.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    public EiBlockMeta getExportBlockMeta() {
        EiColumn eiColumn;
        EiBlockMeta eiMetadata = new EiBlockMeta();
        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00：撤销，10：新增)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isReservation");
        eiColumn.setDescName("是否有预约单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("typeOfHandling");
        eiColumn.setDescName("装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setDescName("预约日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setDescName("预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startOfTransport");
        eiColumn.setDescName("运输起始地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purposeOfTransport");
        eiColumn.setDescName("运输目的地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lateEarlyFlag");
        eiColumn.setDescName("迟到早到标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setDescName("进厂登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callDate");
        eiColumn.setDescName("叫号日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callTime");
        eiColumn.setDescName("叫号时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactory");
        eiColumn.setDescName("入厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("beginEntruckingTime");
        eiColumn.setDescName("作业开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("completeUninstallTime");
        eiColumn.setDescName("作业结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("theTimeFromRegistrationToEntry");
        eiColumn.setDescName("登记至进厂时长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactoryCompleteUninstallTime");
        eiColumn.setDescName("进厂至作业完成时长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactoryLeaveFactoryDate");
        eiColumn.setDescName("进厂至出厂时长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceStatus");
        eiColumn.setDescName("状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessTypeName");
        eiColumn.setDescName("装卸业务名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointName");
        eiColumn.setDescName("当前装卸点名称");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("targetHandPointName");
        eiColumn.setDescName("当前装卸点名称");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }

    /***
     * 重庆定时取消预约
     * S_LI_RL_0179
     */
    public EiInfo cancelReservation(EiInfo info) {
        String segNo="JC000000";
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        hashMap.put("status", "20");
        hashMap.put("gtCurrentTime", "20");
        List<HashMap> list = this.dao.query(LIRL0201.QUERY_ALL_RESERVATION_TIME,  hashMap);
        if (CollectionUtils.isNotEmpty(list)){
            try {
                //取消预约
                for (HashMap lirl0201 : list) {
                    lirl0201.put("recRevisor", "system");
                    lirl0201.put("recRevisorName", "system");
                    lirl0201.put("recReviseTime", DateUtil.curDateTimeStr14());
                    lirl0201.put("sysRemark", "超过预约截止时间自动取消："+DateUtil.curDateTimeStr14());
                    lirl0201.put("remark", "超过预约截止时间自动取消："+DateUtil.curDateTimeStr14());
                    lirl0201.put("delFlag", "1");
                    lirl0201.put("status", "00");
                    this.dao.update(LIRL0201.UPDATE_STATUS,  lirl0201);
                }
            } catch (Exception e) {
                info.setStatus(EiConstant.STATUS_FAILURE);
                info.setMsg(e.getMessage());
            }
        }
        return info;
    }
}
