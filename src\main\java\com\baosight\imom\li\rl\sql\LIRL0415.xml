<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-12 19:54:38
   		Version :  1.0
		tableName :meli.tlirl0415 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 INTO_FACTORY_ID  VARCHAR   NOT NULL   primarykey, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 FACTORY_TYPE  VARCHAR, 
		 INTO_FACTORY_DATE  VARCHAR   NOT NULL,
		 CAR_TRACE_NO  VARCHAR   NOT NULL,
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 TARGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0415">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0415">
		SELECT
				SEG_NO	as "segNo",  <!-- 账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				INTO_FACTORY_ID	as "intoFactoryId",  <!-- 进厂临时流水号 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				STATUS	as "status",  <!-- 状态(00撤销 10新增) -->
				FACTORY_TYPE	as "factoryType",  <!-- 10：首次进厂 20：厂内周转 -->
				INTO_FACTORY_DATE	as "intoFactoryDate",  <!-- 进厂时间 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点代码 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0415 WHERE 1=1
		<isNotEmpty prepend=" AND " property="intoFactoryId">
			INTO_FACTORY_ID = #intoFactoryId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO  like concat ('%',#vehicleNo#,'%')
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  INTO_FACTORY_ID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0415 WHERE 1=1
		<isNotEmpty prepend=" AND " property="intoFactoryId">
			INTO_FACTORY_ID = #intoFactoryId#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="intoFactoryId">
			INTO_FACTORY_ID = #intoFactoryId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryType">
			FACTORY_TYPE = #factoryType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="intoFactoryDate">
			INTO_FACTORY_DATE = #intoFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cancelIntoFactoryDate">
			CANCEL_INTO_FACTORY_DATE = #cancelIntoFactoryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0415 (SEG_NO,  <!-- 账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										INTO_FACTORY_ID,  <!-- 进厂临时流水号 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										STATUS,  <!-- 状态(00撤销 10新增) -->
										FACTORY_TYPE,  <!-- 10：首次进厂 20：厂内周转 -->
										INTO_FACTORY_DATE,  <!-- 进厂时间 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										TARGET_HAND_POINT_ID,  <!-- 目标装卸点代码 -->
										FACTORY_AREA,  <!-- 厂区 -->
										FACTORY_AREA_NAME, <!-- 厂区名称 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #intoFactoryId#, #vehicleNo#, #status#, #factoryType#, #intoFactoryDate#, #carTraceNo#, #targetHandPointId#, #factoryArea#,#factoryAreaName#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0415 WHERE 
			INTO_FACTORY_ID = #intoFactoryId#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0415 
		SET 
		SEG_NO	= #segNo#,   <!-- 账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
								VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					STATUS	= #status#,   <!-- 状态(00撤销 10新增) -->  
					FACTORY_TYPE	= #factoryType#,   <!-- 10：首次进厂 20：厂内周转 -->  
					INTO_FACTORY_DATE	= #intoFactoryDate#,   <!-- 进厂时间 -->
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->
					TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点代码 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			INTO_FACTORY_ID = #intoFactoryId#
	</update>

	<update id="updateStatus">
		UPDATE meli.tlirl0415
		SET
		STATUS	= #status#,   <!-- 状态(00撤销 10新增) -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= #delFlag#   <!-- 记录删除标记 -->
		WHERE
		CAR_TRACE_NO	= #carTraceNo#   <!-- 车辆跟踪号 -->
	</update>
  
</sqlMap>