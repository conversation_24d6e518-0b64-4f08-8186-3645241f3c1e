package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.li.rl.dao.LIRL0107;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 韩亚宁
 * @Description: ${车辆预约站点维护}
 * @Date: 2024/8/15 13:30
 * @Version: 1.0
 */
public class ServiceLIRL0107 extends ServiceBase {

    public static final String STATUS = "status"; //状态
    public static final String DEL_FLAG = "delFlag"; //删除标记
    public static final String REMARK = "remark"; //备注
    public static final String UUID = "uuid"; //uuid
    public static final String ZERO = "0"; //uuid
    public static final String ONE = "1"; //uuid

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0107().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0107.QUERY);
        return outInfo;
    }


    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo subQuery(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub_query1_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        EiInfo outInfo = new EiInfo();
        List query = this.dao.query(LIRL0107.QUERY, queryBlock);
        outInfo.addBlock("sub_result1").addBlockMeta(new LIRL0107().eiMetadata);
        outInfo.getBlock("sub_result1").setRows(query);
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();

            for (HashMap hashMap : listHashMap) {
                hashMap.put(STATUS, 10);//状态
                hashMap.put(DEL_FLAG, 0);//记录删除标记
                hashMap.put(REMARK, MapUtils.getString(hashMap, REMARK, "").trim());//备注
                hashMap.put(UUID, StrUtil.getUUID());//UUID
                String sequence = MapUtils.getString(hashMap, "sequence", "").trim();
                if (StringUtils.isNotBlank(sequence)){
                    hashMap.put("sequence",sequence );
                }else {
                    hashMap.put("sequence", 0);
                }
                RecordUtils.setCreator(hashMap);//添加创建人、姓名、时间
            }
            inInfo = super.insert(inInfo, LIRL0107.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0107> query = dao.query(LIRL0107.QUERY_DISTINCT, hashmap);
                for (LIRL0107 lirl0107:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0107);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashmap);//修改创建人、姓名、时间
                String sequence = MapUtils.getString(hashmap, "sequence", "").trim();
                if (StringUtils.isNotBlank(sequence)){
                    hashmap.put("sequence", sequence);
                }else {
                    hashmap.put("sequence", 0);
                }
            }
            inInfo = super.update(inInfo, LIRL0107.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0107> query = dao.query(LIRL0107.QUERY_DISTINCT, hashMap);
                for (LIRL0107 lirl0107:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0107);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K00);//撤销状态
                hashMap.put(DEL_FLAG, ONE);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0107.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0107> query = dao.query(LIRL0107.QUERY_DISTINCT, hashMap);
                for (LIRL0107 lirl0107:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0107);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K20);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0107.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                //todo 抽取公共方法
                List<LIRL0107> query = dao.query(LIRL0107.QUERY_DISTINCT, hashMap);
                for (LIRL0107 lirl0107:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0107);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0107.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
