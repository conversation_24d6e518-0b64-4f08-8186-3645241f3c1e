<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-processSwitchName" cname="系统开关名称" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-processSwitchDesc" cname="系统开关描述" placeholder="模糊条件" colWidth="3"/>
            <EF:EFDatePicker ename="inqu_status-0-recCreateTimeStart" cname="创建日期起始" role="date" colWidth="3"
                             format="yyyyMMdd"/>
            <EF:EFDatePicker ename="inqu_status-0-recCreateTimeEnd" cname="创建日期截止" role="date" colWidth="3"
                             format="yyyyMMdd"/>
        </div>
    </EF:EFRegion>
    <div class="row">
        <div class="col-md-5">
            <EF:EFRegion id="result0" title="开关定义" style="position: relative;">
                <EF:EFGrid blockId="result0" autoBind="false" autoDraw="no" needAuth="true" checkMode="single, cell" isFloat="true" personal="true" sort="all"
                           serviceName="XTSS04" insertMethod="insert2" updateMethod="update2" deleteMethod="delete2" queryMethod="query">
                    <EF:EFColumn ename="uuid" cname="ID" enable="false" width="235" align="left" hidden="true" sort="flase"/>
                    <EF:EFColumn ename="processSwitchName" cname="系统开关名称" required="true" align="left"/>
                    <EF:EFColumn ename="processSwitchDesc" cname="系统开关描述" required="true" align="left"/>
                    <EF:EFColumn ename="processSwitchValueDesc" cname="系统开关值描述" required="true" editType="textarea" align="left"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <div style="position: absolute;top: 6px;right: 80px;">
                <span style="color: #FF0000;font-size: 12px;font-weight: bold;">系统开关值描述书写格式，例：0:关;1:开;</span>
            </div>
        </div>
        <div class="col-md-7">
            <EF:EFRegion id="result" title="开关配置">
                <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" needAuth="true" isFloat="true" personal="true" sort="all"
                           serviceName="XTSS04" insertMethod="insert1" updateMethod="update1" deleteMethod="delete1" queryMethod="queryDetail">
                    <EF:EFColumn ename="uuid" cname="ID" enable="false" width="235" align="left" hidden="true" sort="flase"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" required="true" enable="false" align="left"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" primaryKey="true" enable="false" align="left" sort="flase"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" primaryKey="true" enable="false" align="left" hidden="true" sort="flase"/>
                    <EF:EFColumn ename="processSwitchName" cname="系统开关名称" required="true" align="left"/>
                    <EF:EFColumn ename="processSwitchDesc" cname="系统开关描述" required="true" editType="textarea" align="left"/>
                    <EF:EFColumn ename="processSwitchValue" cname="系统开关值" required="true" align="left"/>
                    <EF:EFColumn ename="processSwitchValueDesc" cname="系统开关值描述" required="true" editType="textarea" align="left"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" enable="false" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" enable="false" align="center"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" displayType="datetime" editType="datetime" width="150"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" enable="false" align="center"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" enable="false" align="center"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" enable="false" align="center"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" displayType="datetime" editType="datetime" width="150"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" enable="false" align="center"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </div>

    <!-- 系统开关值下拉框 -->
    <div id="ef_popup_switchValue" style="display: none">
        <EF:EFGrid blockId="result3" checkMode="single" serviceName="XTSS04" queryMethod="querySwitchValueByName" autoBind="false"
                   autoDraw="no" rowNo="true" enable="false" isFloat="true" height="200">
            <EF:EFColumn ename="processSwitchValue" readonly="true" cname="系统开关值"/>
            <EF:EFColumn ename="processSwitchValueDesc" readonly="true" cname="系统开关值描述"/>
        </EF:EFGrid>
    </div>

    <!-- 系统账套查询弹出框(查询条件中使用的) -->
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <!-- 系统账套查询弹出框(查询结果中使用的) -->
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo2" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
