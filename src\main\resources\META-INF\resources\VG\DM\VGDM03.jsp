<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="scada清单" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-scadaName" cname="名称" placeholder="模糊条件"
                                colWidth="3"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="查询结果">
                <EF:EFGrid blockId="result" autoDraw="no" readonly="true" checkMode="single, row" sort="all">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="scadaId" cname="ID" align="center"/>
                    <EF:EFColumn ename="scadaName" cname="名称"/>
                    <EF:EFColumn ename="scadaDesp" cname="描述"/>
                    <EF:EFColumn ename="scadaPrimaryIp" cname="IP"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="采集点位清单" id="info-2">
            <EF:EFRegion id="inqu2" title="查询条件">
                <EF:EFInput ename="inqu2_status-0-scadaName" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-unitCode" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-segNo" type="hidden"/>
                <div class="row">
                    <EF:EFInput ename="inqu2_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFInput ename="inqu2_status-0-deviceName" cname="分部设备名称" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFInput ename="inqu2_status-0-tagId" cname="点位代码" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFInput ename="inqu2_status-0-tagDesc" cname="点位名称" placeholder="模糊条件" colWidth="3"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="detail" title="批量赋值">
                <div class="row">
                    <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="equipmentInfo" center="true"
                                     ename="detail_status-0-equipmentName" cname="设备名称" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-deviceCode" readonly="true" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false"
                                     containerId="deviceInfo" center="true" ename="detail_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="查询结果">
                <EF:EFGrid blockId="result2" queryMethod="queryTag" autoDraw="no" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center"/>
                    <EF:EFColumn ename="deviceName" cname="分部设备名称"/>
                    <EF:EFColumn ename="tagId" cname="点位代码" width="140" align="center"/>
                    <EF:EFColumn ename="tagDesc" cname="点位名称" width="170"/>
                    <EF:EFColumn ename="tagType" cname="点位类型" width="70" align="center"/>
                    <EF:EFComboColumn ename="dataType" cname="数据类型" align="center">
                        <EF:EFOption value="0" label=" "/>
                        <EF:EFOption value="2" label="16位无符号整型"/>
                        <EF:EFOption value="1" label="16位有符号整型"/>
                        <EF:EFOption value="6" label="32位无符号整型"/>
                        <EF:EFOption value="7" label="32位有符号整型"/>
                        <EF:EFOption value="11" label="8位无符号整型"/>
                        <EF:EFOption value="10" label="8位有符号整型"/>
                        <EF:EFOption value="3" label="单精度浮点型"/>
                        <EF:EFOption value="8" label="双精度浮点型"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="driverType" cname="驱动类型" align="center"/>
                    <EF:EFColumn ename="tagIhdId" cname="点位ID" align="right"/>
                    <EF:EFColumn ename="deviceId" cname="设备ID"/>
                    <EF:EFColumn ename="deviceAddress" cname="设备地址"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
</EF:EFPage>