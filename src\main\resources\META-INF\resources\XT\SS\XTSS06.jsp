<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-configureKey" cname="配置项代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-configureName" cname="配置项名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-configureValue" cname="配置项集值" colWidth="3" placeholder="模糊条件"/>
        </div>

    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" >
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="configureKey" cname="配置项代码" align="center" width="120"/>
                <EF:EFColumn ename="configureName" cname="配置项名称" align="center" width="120"/>
                <EF:EFColumn ename="configureValue" cname="配置项集值" align="center" width="120"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150">
                    <EF:EFOption label="新增" value="10"/>
                    <EF:EFOption label="删除" value="00"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" width="120"/>
                <EF:EFColumn ename="warehouseName" cname="仓库名称" align="center" width="120"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100" primaryKey="true"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </div>
    </EF:EFTab>
    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseCodeInfo" width="90%" height="60%"/>

</EF:EFPage>
