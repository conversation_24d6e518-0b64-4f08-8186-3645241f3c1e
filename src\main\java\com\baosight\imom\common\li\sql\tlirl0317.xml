<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-03-17 16:01:00
   		Version :  1.0
		tableName :meli.tlirl0317 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 TYPE_OF_HANDLING  VARCHAR   NOT NULL, 
		 RESERVATION_IDENTITY  VARCHAR   NOT NULL, 
		 CUSTOMER_ID  VARCHAR   NOT NULL, 
		 CUSTOMER_NAME  VARCHAR   NOT NULL, 
		 SITE_TYPE  VARCHAR, 
		 SITE_NAME  VARCHAR, 
		 REV_START_TIME  VARCHAR, 
		 REV_END_TIME  VARCHAR, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="tlirl0317">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlirl0317">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				STATUS	as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
				TYPE_OF_HANDLING	as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
				RESERVATION_IDENTITY	as "reservationIdentity",  <!-- 预约身份(包含承运商和客户) -->
				CUSTOMER_ID	as "customerId",  <!-- 承运商/客户代码 -->
				CUSTOMER_NAME	as "customerName",  <!-- 承运商/客户名称 -->
				SITE_TYPE	as "siteType",  <!-- 站点类型 -->
				SITE_NAME	as "siteName",  <!-- 站点名称 -->
				REV_START_TIME	as "revStartTime",  <!-- 预约保留时段起 -->
				REV_END_TIME	as "revEndTime",  <!-- 预约保留时段止 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0317 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0317 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="typeOfHandling">
			TYPE_OF_HANDLING = #typeOfHandling#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationIdentity">
			RESERVATION_IDENTITY = #reservationIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="siteType">
			SITE_TYPE = #siteType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="siteName">
			SITE_NAME = #siteName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="revStartTime">
			REV_START_TIME = #revStartTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="revEndTime">
			REV_END_TIME = #revEndTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0317 (SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										STATUS,  <!-- 状态(撤销：00、新增：10、生效：20) -->
										TYPE_OF_HANDLING,  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
										RESERVATION_IDENTITY,  <!-- 预约身份(包含承运商和客户) -->
										CUSTOMER_ID,  <!-- 承运商/客户代码 -->
										CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
										SITE_TYPE,  <!-- 站点类型 -->
										SITE_NAME,  <!-- 站点名称 -->
										REV_START_TIME,  <!-- 预约保留时段起 -->
										REV_END_TIME,  <!-- 预约保留时段止 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #status#, #typeOfHandling#, #reservationIdentity#, #customerId#, #customerName#, #siteType#, #siteName#, #revStartTime#, #revEndTime#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #uuid#, #tenantId#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0317 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0317 
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					STATUS	= #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->  
					TYPE_OF_HANDLING	= #typeOfHandling#,   <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->  
					RESERVATION_IDENTITY	= #reservationIdentity#,   <!-- 预约身份(包含承运商和客户) -->  
					CUSTOMER_ID	= #customerId#,   <!-- 承运商/客户代码 -->  
					CUSTOMER_NAME	= #customerName#,   <!-- 承运商/客户名称 -->  
					SITE_TYPE	= #siteType#,   <!-- 站点类型 -->  
					SITE_NAME	= #siteName#,   <!-- 站点名称 -->  
					REV_START_TIME	= #revStartTime#,   <!-- 预约保留时段起 -->  
					REV_END_TIME	= #revEndTime#,   <!-- 预约保留时段止 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
								TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>