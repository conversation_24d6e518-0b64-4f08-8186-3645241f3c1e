<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             required="true"
                             center="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        />
            <EF:EFInput type="text" ename="inqu_status-0-handPointId" cname="装卸点代码" colWidth="3"
                        ratio="4:8"/>
            <EF:EFInput type="text" ename="inqu_status-0-handPointName" cname="装卸点名称" colWidth="3"
                        ratio="4:8"   />
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P028"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-businessType" cname="业务类型" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P010"/>
            </EF:EFSelect>
            <EF:EFPopupInput ename="inqu_status-0-factoryArea" cname="厂区代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true"
                             containerId="factory1" popupWidth="600" pupupHeight="300" originalInput="true"
                             required="true"
                             center="true"
                             popupTitle="厂区">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-factoryAreaName" cname="厂区名称" align="center" readonly="true" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-factoryBuilding" cname="厂房代码" align="center" readonly="true" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-factoryBuildingName" cname="厂房名称" align="center" readonly="true" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-loadFlag" cname="装货标记" colWidth="3" disabled="true" type="hidden"/>
        </div>
    </EF:EFRegion>
<EF:EFTab id="info" showClose="false">
    <div id="info-1" title="查询结果">
        <div id="result">
        <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" required="true"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="handPointId" cname="装卸点代码" align="center" enable="false"/>
            <EF:EFColumn ename="handPointName" cname="装卸点名称" align="center" />
            <EF:EFColumn ename="loadingChannelId" cname="装卸点通道代码" align="center" enable="false"/>
            <EF:EFColumn ename="loadingChannelName" cname="装卸点通道名称" align="center" enable="false"/>
            <EF:EFColumn ename="factoryArea" cname="厂区代码" align="center" enable="false"/>
            <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" enable="false"/>
            <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="center" enable="false"/>
            <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="center" enable="false"/>
            <EF:EFColumn ename="craneId" cname="行车编码" align="center" enable="false"/>
            <EF:EFColumn ename="craneName" cname="行车名称" align="center" enable="false"/>
            <EF:EFColumn ename="craneId" cname="行车编码" align="center" enable="false"/>
            <EF:EFColumn ename="craneName" cname="行车名称" align="center" enable="false"/>
            <EF:EFColumn ename="xInitialPoint" cname="X轴起始点" align="center" enable="false"/>
            <EF:EFColumn ename="xDestination" cname="X轴终到点" align="center" width="150" enable="false"/>
            <EF:EFColumn ename="yInitialPoint" cname="Y轴起始点" align="center" width="150" enable="false"/>
            <EF:EFColumn ename="yDestination" cname="Y轴起始点" align="center" width="150" enable="false"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                <EF:EFCodeOption codeName="P028"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="handOrder" cname="装卸顺序" align="center" enable="false" >
                <EF:EFOption label="有序" value="10"/>
                <EF:EFOption label="无序" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="orderNumber" cname="顺序号" align="center" enable="false"/>
            <EF:EFColumn ename="vehicleNumer" cname="容纳车辆数" align="center" enable="false"/>
            <EF:EFColumn ename="standardCapacity" cname="标准容量（吨）" align="center" enable="false"/>
            <EF:EFColumn ename="closeReason" cname="停用原因" align="center" enable="false" />
            <EF:EFColumn ename="loadFlag" cname="装货标记" align="center"  enable="false" />
            <EF:EFColumn ename="unloadFlag" cname="卸货标记" align="center"   enable="false"/>
            <EF:EFComboColumn ename="businessType" cname="业务类型" align="center"  enable="false">
                <EF:EFCodeOption codeName="P010"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
        </EF:EFGrid>
        </div>
        </div>
        <div id="info-2" title="详情">
            <div class="row" id="infof1" colWidth="8" style="border:2px solid  #DBEFFF;">
                <EF:EFRegion id="infof1" title="详情">
                    <div class="row">
                        <EF:EFPopupInput ename="infof1_status-0-unitCode" cname="业务单元代码" resizable="true"
                                         readonly="true" colWidth="3" required="true"
                                         containerId="unitInfo02" popupWidth="600" pupupHeight="300" originalInput="true"
                                         center="true" backFillFieldIds="infof1_status-0-segNo"
                                         popupTitle="业务套账查询">
                        </EF:EFPopupInput>
                        <EF:EFInput ename="infof1_status-0-segName" cname="业务单元简称" readonly="true" colWidth="3"
                                    required="true"/>
                        <EF:EFInput ename="infof1_status-0-segNo" cname="系统账套" readonly="true" required="true"
                                    type="hidden"/>
                        <EF:EFInput ename="infof1_status-0-handPointId" cname="装卸点代码" align="center" enable="false" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-handPointName" cname="装卸点名称" align="center" required="true" colWidth="3" />
                    </div>
                    <div class="row">
                        <EF:EFSelect ename="infof1_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                                     valueField="valueField" textField="textField"
                                     template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                            <EF:EFCodeOption codeName="P028"/>
                        </EF:EFSelect>
                        <EF:EFPopupInput ename="infof1_status-0-factoryArea" cname="厂区代码" resizable="true" colWidth="3"
                                         ratio="4:8"
                                         readonly="true"
                                         containerId="factory" popupWidth="600" pupupHeight="300" originalInput="true"
                                         required="true"
                                         center="true"
                                         popupTitle="厂区">
                        </EF:EFPopupInput>
                        <EF:EFInput ename="infof1_status-0-factoryAreaName" cname="厂区名称" align="center" enable="false" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-factoryBuilding" cname="厂房代码" align="center" enable="false" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-factoryBuildingName" cname="厂房名称" align="center" enable="false" colWidth="3"/>
                        <EF:EFPopupInput ename="infof1_status-0-craneId" cname="行车编码" resizable="true" colWidth="3"
                                         ratio="4:8"
                                         readonly="true"
                                         containerId="eArchivesInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                                         required="true"
                                         center="true"
                                         popupTitle="厂区">
                        </EF:EFPopupInput>
                        <EF:EFInput ename="infof1_status-0-craneName" cname="行车名称" align="center" enable="false" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-xInitialPoint" cname="X轴起始点" align="center" enable="true" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-xDestination" cname="X轴终到点" align="center" enable="true" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-yInitialPoint" cname="Y轴起始点" align="center" enable="true" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-yDestination" cname="Y轴终到点" align="center" enable="true" colWidth="3"/>
                        <EF:EFInput ename="infof1_status-0-vehicleNumer" cname="容纳车辆数" align="center" required="true" colWidth="3"/>
                        <EF:EFSelect ename="infof1_status-0-handOrder" cname="装卸顺序" optionLabel="全部" colWidth="3"
                                     valueField="valueField" textField="textField"
                                     template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                            <EF:EFOption label="有序" value="10"/>
                            <EF:EFOption label="无序" value="20"/>
                        </EF:EFSelect>
                        <EF:EFInput ename="infof1_status-0-orderNumber" cname="顺序号" align="center" colWidth="3"/>
                    </div>
                    <div class="row">


                        <EF:EFInput ename="infof1_status-0-standardCapacity" cname="标准容量（吨）" align="center" colWidth="3" />
                        <EF:EFInput ename="infof1_status-0-closeReason" cname="停用原因" align="center"  colWidth="3" />
                        <EF:EFDatePicker format="yyyyMMdd" colWidth="3" readonly="true" maxLength="8"
                                         ename="infof1_status-0-expectedRecoveryTime" cname="预计恢复时间">
                        </EF:EFDatePicker>
                        <EF:EFPopupInput ename="infof1_status-0-loadingChannelId" cname="装卸点通道代码" resizable="true"
                                         readonly="true" colWidth="3"
                                         containerId="channel" popupWidth="600" pupupHeight="300" originalInput="true"
                                         center="true" backFillFieldIds="infof1_status-0-segNo"
                                         popupTitle="装卸点通道查询">
                        </EF:EFPopupInput>
                    </div>
                    <div class="row">


                        <EF:EFInput ename="infof1_status-0-loadingChannelName" cname="装卸点通道名称" readonly="true" colWidth="3"/>
                        <div class="col-md-3">
                            <div style="margin-left: 36%;">
                                <EF:EFInput type="checkbox" ename="infof1_status-0-loadFlag"
                                            id="loadFlagMark"
                                            cname="装货标记" name="checkbox" colWidth="3" ratio="4:8" inline="true"
                                            value="1">
                                </EF:EFInput>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div style="margin-left: 36%;">
                                <EF:EFInput type="checkbox" ename="infof1_status-0-unloadFlag"
                                            id="unloadFlagMark"
                                            cname="卸货标记" name="checkbox" colWidth="3" ratio="4:8" inline="true"
                                            value="1">
                                </EF:EFInput>
                            </div>
                        </div>
                    </div>
<%--                    <div class="row">--%>
<%--                       --%>
<%--                    </div>--%>
                </EF:EFRegion>
            </div>
        </div>
    <div id="info-3" title="装卸点与装卸业务关联">
        <div class="row" id="result1" colWidth="8" style="border:2px solid  #DBEFFF;">
                <EF:EFGrid blockId="result1" autoDraw="no"
                           autoBind="false" isFloat="true" personal="true" sort="all" serviceName="LIRL0304"
                           queryMethod="query"
                >
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
                    <EF:EFColumn ename="uuid" cname="流水号" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="handPointId" cname="装卸点代码"  align="center"  enable="false"/>
                    <EF:EFComboColumn ename="businessType" cname="业务类型" align="center" required="true"
                                      columnTemplate="#=valueField# - #=textField#"  width="150"
                                      itemTemplate="#=valueField# - #=textField#">

                        <EF:EFCodeOption codeName="P010"/>
                    </EF:EFComboColumn>

                    <EF:EFColumn ename="scrapType" cname="可利用材种类"  align="center"  enable="false"/>

                    <EF:EFComboColumn ename="handType" cname="装卸类型" align="center" required="true" hidden="true"
                                      columnTemplate="#=valueField# - #=textField#"
                                      itemTemplate="#=valueField# - #=textField#"
                    >
                        <%--<EF:EFOptions blockId="testType1Block" textField="codeDesc" valueField="codeValue"/>--%>
                        <EF:EFOption label="装货" value="10"/>
                        <EF:EFOption label="卸货" value="20"/>
                        <EF:EFOption label="装货+卸货" value="30"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="status" cname="状态" align="center"  enable="false"
                                      columnTemplate="#=valueField# - #=textField#"  width="150"
                                      itemTemplate="#=valueField# - #=textField#">

                        <EF:EFCodeOption codeName="P028"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="remark" cname="备注" required="false" align="left" editType="textarea" maxlength="500"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="tenantUser" cname="租户"  align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记"  align="center" hidden="true"/>
                </EF:EFGrid>
        </div>
    </div>
    <div id="info-4" title="起始地装卸点配置">
        <div class="row" id="result2" colWidth="8" style="border:2px solid  #DBEFFF;">
            <EF:EFGrid blockId="result2" autoDraw="no"
                       autoBind="false" isFloat="true" personal="true" sort="all" serviceName="LIRL0315"
                       queryMethod="query"
            >
                <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
                <EF:EFColumn ename="uuid" cname="流水号" enable="false" align="center" hidden="true"/>
                <EF:EFColumn ename="handPointId" cname="装卸点代码"  align="center"  enable="false"/>
                <EF:EFColumn ename="siteName" cname="起始地" required="true" align="center" editType="textarea" enable="true"/>

                <EF:EFComboColumn ename="status" cname="状态" align="center"  enable="false"
                                  columnTemplate="#=valueField# - #=textField#"  width="150"
                                  itemTemplate="#=valueField# - #=textField#">

                    <EF:EFCodeOption codeName="P028"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="remark" cname="备注" required="false" align="left" editType="textarea" maxlength="500"/>
                <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                             readonly="true" align="center"/>
                <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                             readonly="true" align="center"/>
                <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                             readonly="true" align="center"/>
                <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                             readonly="true" align="center"/>
                <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                             readonly="true" align="center"/>
                <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                             readonly="true" align="center"/>
                <EF:EFColumn ename="tenantUser" cname="租户"  align="center" hidden="true"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记"  align="center" hidden="true"/>
            </EF:EFGrid>
        </div>
    </div>

</EF:EFTab>

    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo02" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo04" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIDS03" id="channel" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="driving" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factory" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factory1" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0006" id="scrapTypeWin" width="90%" height="60%"></EF:EFWindow>
    <%--行车(设备档案)弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS07" id="eArchivesInfo" width="90%" height="60%"/>

</EF:EFPage>
