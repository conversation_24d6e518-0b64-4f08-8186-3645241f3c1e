$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
    });
    var addFlag = false;
    var updateFlag = false;
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId === "info-2") {
                    // 只判断非新增和修改按钮跳转
                    if (!addFlag && !updateFlag) {
                        const checkedRows = resultGrid.getCheckedRows();
                        const checkRowLength = checkedRows.length;
                        if (checkRowLength !== 1) {
                            NotificationUtil({msg: "请勾选一条检修计划信息"}, "error");
                            e.preventDefault();
                        } else {
                            setDetailData(checkedRows[0]);
                            setReadStatus();
                        }
                    }
                } else {
                    addFlag = false;
                    updateFlag = false;
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        "result": {
            onRowDblClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                setReadStatus();
                tab_Strip.select(1);
            },
            loadComplete: function (grid) {
                // 查看流程图
                $("#FLOWCHART").on("click", function (e) {
                    IMOMUtil.handleFlowchartClick(resultGrid);
                });
                // 新增按钮
                $("#INSERT1").on("click", function (e) {
                    addFlag = true;
                    IPLAT.clearNode(document.getElementById("info-2"));
                    setInsertStatus();
                    tab_Strip.select(1);
                    $("#detail_status-0-unitCode").val(unitInfo.unitCode);
                    IPLAT.EFSelect.value($("#detail_status-0-segNo"), unitInfo.segNo);
                });
                // 修改按钮
                $("#UPDATE1").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能对一行数据进行修改！]"}, "error");
                        return;
                    }
                    const applyStatus = checkedRows[0].applyStatus;
                    if (applyStatus !== "10") {
                        NotificationUtil({msg: "操作失败，原因[只能新增状态数据进行修改！]"}, "error");
                        return;
                    }
                    updateFlag = true;
                    setDetailData(checkedRows[0]);
                    setUpdateStatus();
                    tab_Strip.select(1);
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM09", "submit", true, null, null, false);
                });
                // 取消提交按钮
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM09", "cancel", true, null, null, false);
                });
                // 新增保存按钮
                $("#INSERT_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM09", "insert", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
                // 修改保存按钮
                $("#UPDATE_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM09", "update", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });

            }
        },
        // 附件信息
        "result2": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
            loadComplete: function (grid) {
                // 附件上传按钮
                $("#FILEUPLOAD").click(function () {
                    $("#fileForm").click();
                });
            }
        },
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 详情区域业务单元弹窗
    IMOMUtil.importDetailUnitInfo();
    // 详情区域设备弹窗
    IMOMUtil.importDetailEquipmentInfo();
    // 详情区域IMC库存弹窗
    IMOMUtil.windowTemplate({
        windowId: "stuffInfo",
        notQuery: true,
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#detail_status-0-segNo").val());
            iframejQuery("#inqu_status-0-segNo").val($("#detail_status-0-segNo").val());
            iframejQuery("#inqu_status-0-segName").val($("#detail_status-0-segName").val());
        },
        assignMap: {
            "detail_status-0-stuffCode": "stuffCode",
            "detail_status-0-stuffName": "stuffName",
            "detail_status-0-specDesc": "spec",
            "detail_status-0-unitPrice": "stuffUnitPriceTaxed"
        }
    });
    // 详情区域部门代码弹窗
    IMOMUtil.windowTemplate({
        windowId: "deptInfo",
        _open: function (e, iframejQuery) {
            const segNo = $("#inqu_status-0-segNo").val();
            iframejQuery("#inqu_status-0-segNo").val(segNo);
        },
        assignMap: {
            "detail_status-0-deptId": "segNo",
            "detail_status-0-deptName": "segName"
        }
    });

    /**
     * 设置详情区域新增状态
     */
    function setInsertStatus() {
        // 新增按钮启用
        $("#INSERT_SAVE").attr("disabled", false);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", false);
        $("#detail_status-0-equipmentName").attr("disabled", false);
        $("#detail_status-0-stuffName").attr("disabled", false);
        $("#detail_status-0-deptId").attr("disabled", false);
        setFieldEdit(true);
        // 禁用附件上传
        $("#FILEUPLOAD").css("pointer-events", "none");
        $("#FILEUPLOAD").find("button").attr("disabled", true)
    }

    /**
     * 设置详情区域修改状态
     */
    function setUpdateStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮启用
        $("#UPDATE_SAVE").attr("disabled", false);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", false);
        $("#detail_status-0-stuffName").attr("disabled", false);
        $("#detail_status-0-deptId").attr("disabled", false);
        setFieldEdit(true);
    }

    /**
     * 设置详情区域只读状态
     */
    function setReadStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-stuffName").attr("disabled", true);
        $("#detail_status-0-deptId").attr("disabled", true);
        setFieldEdit(false);
    }

    /**
     * 设置字段是否可编辑
     * @param {boolean} isEdit 是否可编辑
     */
    function setFieldEdit(isEdit) {
        if(isEdit){
            $("#FILEUPLOAD").css("pointer-events", "auto");
            $("#FILEUPLOAD").find("button").attr("disabled", false)
        }else {
            $("#FILEUPLOAD").css("pointer-events", "none");
            $("#FILEUPLOAD").find("button").attr("disabled", true)
        }
        IPLAT.EFSelect.enable($("#detail_status-0-scrapType"), isEdit);
        $("#detail_status-0-applyQty").attr("readonly", !isEdit);
        $("#detail_status-0-scrapReason").attr("readonly", !isEdit);
    }

    /**
     * 设置详情区域内容
     * @param row grid行数据
     */
    function setDetailData(row) {
        // 清除原数据
        IPLAT.clearNode(document.getElementById("info-2"));
        // 将数据回填到详情页
        IMOMUtil.fillNode(row, "detail");
        // 附件查询
        $("#inqu2_status-0-relevanceId").val(row.scrapApplyId);
        $("#inqu2_status-0-segNo").val(row.segNo);
        result2Grid.dataSource.page(1);
    }

    $("#detail_status-0-applyQty").on("input", function () {
        const currentValue = $(this).val();
        const numberValue = Number(currentValue);
        if (IPLAT.isNumber(numberValue)) {
            const price = $("#detail_status-0-unitPrice").val();
            const amountMoney = numberValue * price;
            $("#detail_status-0-amountMoney").val(amountMoney.toFixed(2));
        }
    });

    var relevanceId = "";
    var segNo = "";
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            result2Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM09";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            relevanceId = $("#inqu2_status-0-relevanceId").val();
            segNo = $("#inqu2_status-0-segNo").val();
        }
    });
});
