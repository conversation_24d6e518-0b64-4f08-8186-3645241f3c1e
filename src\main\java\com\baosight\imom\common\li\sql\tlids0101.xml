<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-11-25 9:56:33
   		Version :  1.0
		tableName :meli.tlids0101 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 FACTORY_AREA_NAME  VARCHAR   NOT NULL, 
		 AREA_TYPE  VARCHAR   NOT NULL, 
		 AREA_CODE  VARCHAR   NOT NULL   primarykey, 
		 AREA_NAME  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING_NAME  VARCHAR   NOT NULL, 
		 X_INITIAL_POINT  VARCHAR   NOT NULL, 
		 X_DESTINATION  VARCHAR   NOT NULL, 
		 Y_INITIAL_POINT  VARCHAR   NOT NULL, 
		 Y_DESTINATION  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR
	-->
<sqlMap namespace="tlids0101">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids0101">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区代码 -->
				FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
				AREA_TYPE	as "areaType",  <!-- 区域类型 -->
				AREA_CODE	as "areaCode",  <!-- 区域代码 -->
				AREA_NAME	as "areaName",  <!-- 区域名称 -->
				FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
				FACTORY_BUILDING_NAME	as "factoryBuildingName",  <!-- 厂房名称 -->
				X_INITIAL_POINT	as "x_initialPoint",  <!-- X轴起始点 -->
				X_DESTINATION	as "x_destination",  <!-- X轴终到点 -->
				Y_INITIAL_POINT	as "y_initialPoint",  <!-- Y轴起始点 -->
				Y_DESTINATION	as "y_destination",  <!-- Y轴终到点 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids0101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="areaCode">
			AREA_CODE = #areaCode#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  AREA_CODE asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="areaCode">
			AREA_CODE = #areaCode#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaType">
			AREA_TYPE = #areaType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaCode">
			AREA_CODE = #areaCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaName">
			AREA_NAME = #areaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			FACTORY_BUILDING = #factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuildingName">
			FACTORY_BUILDING_NAME = #factoryBuildingName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_initialPoint">
			X_INITIAL_POINT = #x_initialPoint#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_destination">
			X_DESTINATION = #x_destination#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_initialPoint">
			Y_INITIAL_POINT = #y_initialPoint#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_destination">
			Y_DESTINATION = #y_destination#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0101 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										FACTORY_AREA,  <!-- 厂区代码 -->
										FACTORY_AREA_NAME,  <!-- 厂区名称 -->
										AREA_TYPE,  <!-- 区域类型 -->
										AREA_CODE,  <!-- 区域代码 -->
										AREA_NAME,  <!-- 区域名称 -->
										FACTORY_BUILDING,  <!-- 厂房代码 -->
										FACTORY_BUILDING_NAME,  <!-- 厂房名称 -->
										X_INITIAL_POINT,  <!-- X轴起始点 -->
										X_DESTINATION,  <!-- X轴终到点 -->
										Y_INITIAL_POINT,  <!-- Y轴起始点 -->
										Y_DESTINATION,  <!-- Y轴终到点 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #factoryArea#, #factoryAreaName#, #areaType#, #areaCode#, #areaName#, #factoryBuilding#, #factoryBuildingName#, #x_initialPoint#, #x_destination#, #y_initialPoint#, #y_destination#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0101 WHERE 
			AREA_CODE = #areaCode#
	</delete>

	<update id="update">
		UPDATE meli.tlids0101 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区代码 -->  
					FACTORY_AREA_NAME	= #factoryAreaName#,   <!-- 厂区名称 -->  
					AREA_TYPE	= #areaType#,   <!-- 区域类型 -->  
								AREA_NAME	= #areaName#,   <!-- 区域名称 -->  
					FACTORY_BUILDING	= #factoryBuilding#,   <!-- 厂房代码 -->  
					FACTORY_BUILDING_NAME	= #factoryBuildingName#,   <!-- 厂房名称 -->  
					X_INITIAL_POINT	= #x_initialPoint#,   <!-- X轴起始点 -->  
					X_DESTINATION	= #x_destination#,   <!-- X轴终到点 -->  
					Y_INITIAL_POINT	= #y_initialPoint#,   <!-- Y轴起始点 -->  
					Y_DESTINATION	= #y_destination#,   <!-- Y轴终到点 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					UUID	= #uuid#  <!-- ID -->  
			WHERE 	
			AREA_CODE = #areaCode#
	</update>
  
</sqlMap>