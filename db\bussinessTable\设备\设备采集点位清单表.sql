create table TMEDV0007
(
    TAG_ID          VARCHAR(64)  default ' '    not null comment '点位ID',
    TAG_TYPE        VARCHAR(2)   default ' '    not null comment '点位类型',
    TAG_DESC        VARCHAR(64)  default ' '    not null comment '点位描述',
    DATA_TYPE       VARCHAR(2)   default ' '    not null comment '数据类型',
    DRIVER_TYPE     VARCHAR(32)  default ' '    not null comment '驱动类型',
    DEVICE_ID       VARCHAR(64)  default ' '    not null comment '设备ID',
    DEVICE_ADDRESS  VARCHAR(32)  default ' '    not null comment '设备地址',
    DEVICE_CODE     VARCHAR(64)  default ' '    not null comment '分部设备代码',
    DEVICE_NAME     VARCHAR(128) default ' '    not null comment '分部设备名称',
    E_ARCHIVES_NO   VARCHAR(20)  default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME  VARCHAR(200) default ' '    not null comment '设备名称',
    -- 固定字段
    UUID            VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='设备采集点位清单表' ENGINE = INNODB
                                DEFAULT CHARSET = UTF8
                                COLLATE UTF8_BIN;


-- 增加ER图外键
ALTER TABLE TMEDV0007 ADD unique KEY (TAG_ID);
ALTER TABLE TMEDV0007 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);
ALTER TABLE TMEDV0007 ADD FOREIGN KEY (DEVICE_CODE) REFERENCES TMEDV0103(DEVICE_CODE);