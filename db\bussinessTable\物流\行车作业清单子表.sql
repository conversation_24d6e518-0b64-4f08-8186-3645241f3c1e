create table meli.tlids1102
(
    SEG_NO             varchar(12)    default ' '        not null comment '系统账套',
    UNIT_CODE          varchar(12)    default ' '        not null comment '业务单元代代码',
    CRANE_ORDER_ID     varchar(32)    default ' '        not null comment '行车作业清单号',
    CRANE_ORDER_SUB_ID varchar(32)    default ' '        not null comment '行车作业清单子项号',
    PACK_ID            varchar(50)    default ' '        null comment '捆包号',
    LABEL_ID           varchar(50)    default ' '        null comment '标签号',
    NET_WEIGHT         decimal(20, 8) default 0.00000000 null comment '净重',
    QUANTITY           int            default 0          null comment '数量',
    STATUS             varchar(2)     default ' '        null comment '状态',
    REC_CREATOR        varchar(32)    default ' '        null comment '记录创建人',
    REC_CREATOR_NAME   varchar(100)   default ' '        null comment '记录创建人姓名',
    REC_CREATE_TIME    varchar(17)    default ' '        null comment '记录创建时间',
    REC_REVISOR        varchar(32)    default ' '        null comment '记录修改人',
    REC_REVISOR_NAME   varchar(100)   default ' '        null comment '记录修改人姓名',
    REC_REVISE_TIME    varchar(17)    default ' '        null comment '记录修改时间',
    ARCHIVE_FLAG       varchar(1)     default ' '        null comment '归档标记',
    TENANT_USER        varchar(10)    default ' '        null comment '租户',
    DEL_FLAG           smallint       default 0          null comment '删除标记',
    UUID               varchar(32)    default ' '        not null comment 'ID'
        primary key,
    constraint tlids1102_UUID_uindex
        unique (UUID)
)
    comment '行车作业清单子表' collate = utf8_bin;

