package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0201;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 跨区管理
 */
public class ServiceLIDS0201 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0201().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0201.QUERY, new LIDS0201());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //增加校验,同一厂区的同一厂房下，跨区编码不能重复
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", segNo);//系统账套
                    /*queryMap.put("factoryArea", MapUtils.getString(itemMap, "factoryArea"));//厂区编码
                    queryMap.put("factoryBuilding", MapUtils.getString(itemMap, "factoryBuilding"));//厂房编码*/
                    queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));//跨区编码
                    int count = super.count(LIDS0201.COUNT_EQUAL, queryMap);
                    if (count > 0) {
                        /*throw new PlatException("厂区:" + MapUtils.getString(itemMap, "factoryArea") + ",厂房:" +
                                MapUtils.getString(itemMap, "factoryBuilding") + "同一厂区的同一厂房下，跨区编码不能重复!");*/
                        throw new PlatException("跨区编码"+MapUtils.getString(itemMap, "crossArea")+"不能重复!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0201.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //增加校验,同一厂区的同一厂房下，跨区编码不能重复
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", segNo);//系统账套
                    queryMap.put("factoryArea", MapUtils.getString(itemMap, "factoryArea"));//厂区编码
                    queryMap.put("factoryBuilding", MapUtils.getString(itemMap, "factoryBuilding"));//厂房编码
                    queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));//跨区编码
                    int count = super.count(LIDS0201.COUNT, queryMap);
                    if (count > 1) {
                        throw new PlatException("厂区:" + MapUtils.getString(itemMap, "factoryArea") + ",厂房:" +
                                MapUtils.getString(itemMap, "factoryBuilding") + "同一厂区的同一厂房下，跨区编码不能重复!");
                    }
                    //查询区域代码状态，判断非新增状态数据不可修改
                    queryMap.clear();
                    queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                    /*queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));*/
                    queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                    queryMap.put("status", "10");
                    count = super.count(LIDS0201.COUNT_UUID, queryMap);
                    if (count < 1) {
                        throw new PlatException(MapUtils.getString(itemMap, "crossArea")+"跨区代码状态非新增状态，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0201.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0201.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("跨区代码状态非新增状态，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0201.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo validateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可启用!");
                }
                //查询区域代码状态，判断非新增状态数据不可启用
                Map queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "10");
                int count = super.count(LIDS0201.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("跨区代码状态非新增状态，不可生效!");
                }
                //状态变更为生效
                itemMap.put("status", "20");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0201.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 反生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo deValidateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可反生效!");
                }
                //查询区域代码状态，判断非生效状态
                Map queryMap = new HashMap();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));
                queryMap.put("uuid", MapUtils.getString(itemMap, "uuid"));
                queryMap.put("status", "20");
                int count = super.count(LIDS0201.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("跨区代码状态非生效状态，不可反生效!");
                }

                //校验跨区编码已被引用，不允许再反生效
                queryMap.clear();
                queryMap.put("segNo", MapUtils.getString(itemMap, "segNo"));
                queryMap.put("factoryArea", MapUtils.getString(itemMap, "factoryArea"));
                queryMap.put("factoryAreaName", MapUtils.getString(itemMap, "factoryAreaName"));
                queryMap.put("factoryBuilding", MapUtils.getString(itemMap, "factoryBuilding"));
                queryMap.put("factoryBuildingName", MapUtils.getString(itemMap, "factoryBuildingName"));
                queryMap.put("crossArea", MapUtils.getString(itemMap, "crossArea"));
                queryMap.put("crossAreaName", MapUtils.getString(itemMap, "crossAreaName"));
                //查询跨区是否被引用
                List<HashMap> queryList = dao.query(LIDS0201.COUNT_ALL_BY_CROSS_AREA, queryMap);
                Map countAllMap = queryList.get(0);
                int count0401 = MapUtils.getInt(countAllMap, "count0401");
                int count0501 = MapUtils.getInt(countAllMap, "count0501");
                int count0601 = MapUtils.getInt(countAllMap, "count0601");
                int count0701 = MapUtils.getInt(countAllMap, "count0701");
                if (count0401 > 0) {
                    throw new PlatException("跨区编码已被过跨小车引用，不允许再反生效!");
                } else if (count0501 > 0) {
                    throw new PlatException("跨区编码已被拆包区引用，不允许再反生效!");
                } else if (count0601 > 0) {
                    throw new PlatException("跨区编码已被库位引用，不允许再反生效!");
                } else if (count0701 > 0) {
                    throw new PlatException("跨区编码已被机组附属信息引用，不允许再反生效!");
                }

                //状态变更为新增
                itemMap.put("status", "10");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0201.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行反生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 更改装卸点
     *
     * @param inInfo
     * @return
     */
    public EiInfo updateLoadingPoint(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可修改!");
                }
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            //更改装卸点
            outInfo = super.update(inInfo, LIDS0201.UPDATE_LOADING_POINT);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
