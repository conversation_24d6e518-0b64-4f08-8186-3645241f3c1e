create table TMEDV0106
(
    CHECK_PLAN_ID            VARCHAR(64)    default ' '    not null comment '点检计划主项号',
    CHECK_PLAN_SUB_ID        VARCHAR(64)    default ' '    not null comment '点检计划分档号',
    DEVICE_CODE              VARCHAR(64)    default ' '    not null comment '分部设备代码',
    DEVICE_NAME              VARCHAR(128)   default ' '    not null comment '分部设备名称',
    SPOT_CHECK_CONTENT       VARCHAR(256)   default ' '    not null comment '点检内容',
    SPOT_CHECK_METHOD        VARCHAR(16)    default ' '    not null comment '点检方法',
    DEVICE_CHECK_STATUS      VARCHAR(16)    default ' '    not null comment '设备点检状态',
    IS_PUBLISH               VARCHAR(2)     default ' '    not null comment '是否挂牌',
    SPOT_CHECK_STANDARD_TYPE VARCHAR(16)    default ' '    not null comment '点检标准类型',
    JUDGMENT_STANDARD        VARCHAR(200)   default ' '    not null comment '判断标准',
    SPOT_CHECK_NATURE        VARCHAR(16)    default ' '    not null comment '点检性质',
    SPOT_CHECK_IMPLEMENTE    VARCHAR(16)    default ' '    not null comment '点检实施方',
    MEASURE_ID               VARCHAR(10)    default ' '    not null comment '计量单位',
    UPPER_LIMIT              DECIMAL(20, 8) default 0      not null comment '上限值',
    LOWER_LIMIT              DECIMAL(20, 8) default 0      not null comment '下限值',
    PLAN_SOURCE              VARCHAR(16)    default ' '    not null comment '计划来源',
    CHECK_PLAN_SUB_STATUS    VARCHAR(16)    default ' '    not null comment '点检计划分档状态',
    ACTUALS_REMARK           VARCHAR(512)   default ' '    not null comment '点检实绩信息',
    IS_NORMAL                VARCHAR(16)    default ' '    not null comment '是否异常',
    EXCEPTION_CONTACT_ID     VARCHAR(64)    default ' '    not null comment '异常信息联络单号',
    ACTUALS_REVISOR          VARCHAR(32)    default ' '    not null comment '点检实绩操作人',
    ACTUALS_TIME             VARCHAR(14)    default ' '    not null comment '点检实绩操作时间',
    ACTUALS_REVISOR_NAME     VARCHAR(100)   default ' '    not null comment '点检实绩操作人姓名',
    SPOT_CHECK_STANDARD_ID   VARCHAR(64)    default ' '    not null comment '点检标准编号',
    TAG_ID                   VARCHAR(64)    default ' '    not null comment '自动采集点位',
    -- 固定字段
    UUID                     VARCHAR(32)                   NOT NULL COMMENT '唯一编码',
    REC_CREATOR              VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME          VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR              VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME          VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID                VARCHAR(64)    DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG             VARCHAR(1)     DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)
) COMMENT ='点检计划子表' ENGINE = INNODB
                          DEFAULT CHARSET = UTF8
                          COLLATE UTF8_BIN;


-- 增加ER图外键
ALTER TABLE TMEDV0106 ADD unique KEY (CHECK_PLAN_SUB_ID);
ALTER TABLE TMEDV0106 ADD FOREIGN KEY (DEVICE_CODE) REFERENCES TMEDV0103(DEVICE_CODE);
ALTER TABLE TMEDV0106 ADD FOREIGN KEY (CHECK_PLAN_ID) REFERENCES TMEDV0105(CHECK_PLAN_ID);
ALTER TABLE TMEDV0106 ADD FOREIGN KEY (SPOT_CHECK_STANDARD_ID) REFERENCES TMEDV0104(SPOT_CHECK_STANDARD_ID);