<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-10 9:36:35
   		Version :  1.0
		tableName :meli.tlids0605 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 WPROVIDER_ID  VARCHAR, 
		 LOCATION_ID  VARCHAR, 
		 LOC_VIEW_ID  VARCHAR, 
		 LOC_COLUMN_ID  VARCHAR, 
		 USE_STATUS  VARCHAR   NOT NULL, 
		 LOCATION_TYPE  VARCHAR, 
		 LOCATION_LENGTH  INTEGER, 
		 SPEC_UPPER  INTEGER, 
		 SPEC_LOWER  INTEGER, 
		 X_POINT_START  INTEGER, 
		 X_POINT_END  INTEGER, 
		 Y_POINT_CENTER  INTEGER, 
		 LEFT_VIEW_ID  VARCHAR, 
		 RIGHT_VIEW_ID  VARCHAR, 
		 UP_DOWN_FLAG  VARCHAR, 
		 STAND_FLAG  VARCHAR, 
		 LR_ACCUPY_FLAG  VARCHAR, 
		 JG_LOCK_FLAG  VARCHAR, 
		 UP_FORBIN_FLAG  VARCHAR, 
		 PACK_ID  VARCHAR, 
		 FACTORY_PRODUCT_ID  VARCHAR, 
		 PRODUCT_TYPE_ID  VARCHAR, 
		 SHOPSIGN  VARCHAR, 
		 SPEC  VARCHAR, 
		 INNER_DIAMETER  INTEGER, 
		 OUTER_DIAMETER  INTEGER, 
		 PUTIN_WEIGHT  INTEGER, 
		 REF_WIDTH  INTEGER, 
		 CUSTOMER_ID  VARCHAR, 
		 CONTRACT_ID  VARCHAR, 
		 REMARK  VARCHAR, 
		 LOCATION_NAME  VARCHAR, 
		 FACTORY_AREA  VARCHAR, 
		 CROSS_AREA  VARCHAR, 
		 SURFACE_GRADE  VARCHAR, 
		 C_NO  VARCHAR, 
		 UP_LEFT_VIEW_ID  VARCHAR, 
		 UP_RIGHT_VIEW_ID  VARCHAR, 
		 IDLE_INTERVAL_ID  INTEGER, 
		 AVALIABLE_MIN_LENGTH  INTEGER, 
		 OCCUPIED_FLAG  VARCHAR, 
		 X_POINT_START_ORIGN  INTEGER, 
		 X_POINT_END_ORIGN  INTEGER, 
		 POINT_LOWER_LENGTH  INTEGER, 
		 POINT_UPPER_LENGTH  INTEGER, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR, 
		 FACTORY_BUILDING  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING_NAME  VARCHAR   NOT NULL
	-->
<sqlMap namespace="tlids0605">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.ds.domain.Tlids0605">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				WPROVIDER_ID	as "wproviderId",  <!-- 仓库代码 -->
				LOCATION_ID	as "locationId",  <!-- 库位代码 -->
				LOC_VIEW_ID	as "locViewId",  <!-- 库位精确位置(2位流水码) -->
				LOC_COLUMN_ID	as "locColumnId",  <!-- 库位(跨+列) -->
				USE_STATUS	as "useStatus",  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
				LOCATION_TYPE	as "locationType",  <!-- 库位类型。1：条状、2：点状 -->
				LOCATION_LENGTH	as "locationLength",  <!-- 库位长度 单位(cm) -->
				SPEC_UPPER	as "specUpper",  <!-- 宽度上限 单位(mm) -->
				SPEC_LOWER	as "specLower",  <!-- 宽度下限 单位(mm) -->
				X_POINT_START	as "x_pointStart",  <!-- X轴起始点 -->
				X_POINT_END	as "x_pointEnd",  <!-- X轴结束点 -->
				Y_POINT_CENTER	as "y_pointCenter",  <!-- Y轴 -->
				LEFT_VIEW_ID	as "leftViewId",  <!-- 同层相邻左侧库位精确位置序号（下层库位属性） -->
				RIGHT_VIEW_ID	as "rightViewId",  <!-- 同层相邻右侧库位精确位置序号（下层库位属性） -->
				UP_DOWN_FLAG	as "upDownFlag",  <!-- 上下层标记。1：下层、2：上层 -->
				STAND_FLAG	as "standFlag",  <!-- 是否立式库位。0：卧式、1：立式 -->
				LR_ACCUPY_FLAG	as "lrAccupyFlag",  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
				JG_LOCK_FLAG	as "jgLockFlag",  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
				UP_FORBIN_FLAG	as "upForbinFlag",  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
				PACK_ID	as "packId",  <!-- 捆包号 -->
				FACTORY_PRODUCT_ID	as "factoryProductId",  <!-- 钢厂资源号 -->
				PRODUCT_TYPE_ID	as "productTypeId",  <!-- 品种 -->
				SHOPSIGN	as "shopsign",  <!-- 牌号 -->
				SPEC	as "spec",  <!-- 规格 -->
				INNER_DIAMETER	as "innerDiameter",  <!-- 内径 单位:mm -->
				OUTER_DIAMETER	as "outerDiameter",  <!-- 外径 单位:mm -->
				PUTIN_WEIGHT	as "putinWeight",  <!-- 重量 -->
				REF_WIDTH	as "refWidth",  <!-- 宽度 单位:mm -->
				CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
				CONTRACT_ID	as "contractId",  <!-- 销售合同 -->
				REMARK	as "remark",  <!-- 备注 -->
				LOCATION_NAME	as "locationName",  <!-- 库位名称 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				CROSS_AREA	as "crossArea",  <!-- 跨区 -->
				SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
				C_NO	as "c_no",  <!-- 所在跨区的库位序号 -->
				UP_LEFT_VIEW_ID	as "upLeftViewId",  <!-- 跨层相邻左侧库位精确位置序号（上层库位属性） -->
				UP_RIGHT_VIEW_ID	as "upRightViewId",  <!-- 跨层相邻右侧库位精确位置序号（上层库位属性） -->
				IDLE_INTERVAL_ID	as "idleIntervalId",  <!-- 条状库位连续空闲区间编号 -->
				AVALIABLE_MIN_LENGTH	as "avaliableMinLength",  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
				OCCUPIED_FLAG	as "occupiedFlag",  <!-- 已占用库位标记 -->
				X_POINT_START_ORIGN	as "x_pointStartOrign",  <!-- X轴起始点初始值 -->
				X_POINT_END_ORIGN	as "x_pointEndOrign",  <!-- X轴结束点初始值 -->
				POINT_LOWER_LENGTH	as "pointLowerLength",  <!-- 点状库位长度下限 -->
				POINT_UPPER_LENGTH	as "pointUpperLength",  <!-- 点状库位长度上限 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid",  <!-- ID -->
				FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
				FACTORY_BUILDING_NAME	as "factoryBuildingName" <!-- 厂房名称 -->
		FROM meli.tlids0605 WHERE 1=1
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0605 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="wproviderId">
			WPROVIDER_ID = #wproviderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationId">
			LOCATION_ID = #locationId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locViewId">
			LOC_VIEW_ID = #locViewId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locColumnId">
			LOC_COLUMN_ID = #locColumnId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="useStatus">
			USE_STATUS = #useStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationType">
			LOCATION_TYPE = #locationType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationLength">
			LOCATION_LENGTH = #locationLength#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specUpper">
			SPEC_UPPER = #specUpper#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specLower">
			SPEC_LOWER = #specLower#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_pointStart">
			X_POINT_START = #x_pointStart#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_pointEnd">
			X_POINT_END = #x_pointEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_pointCenter">
			Y_POINT_CENTER = #y_pointCenter#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="leftViewId">
			LEFT_VIEW_ID = #leftViewId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="rightViewId">
			RIGHT_VIEW_ID = #rightViewId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="upDownFlag">
			UP_DOWN_FLAG = #upDownFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="standFlag">
			STAND_FLAG = #standFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="lrAccupyFlag">
			LR_ACCUPY_FLAG = #lrAccupyFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jgLockFlag">
			JG_LOCK_FLAG = #jgLockFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="upForbinFlag">
			UP_FORBIN_FLAG = #upForbinFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryProductId">
			FACTORY_PRODUCT_ID = #factoryProductId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productTypeId">
			PRODUCT_TYPE_ID = #productTypeId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="shopsign">
			SHOPSIGN = #shopsign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="spec">
			SPEC = #spec#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="innerDiameter">
			INNER_DIAMETER = #innerDiameter#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outerDiameter">
			OUTER_DIAMETER = #outerDiameter#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="putinWeight">
			PUTIN_WEIGHT = #putinWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="refWidth">
			REF_WIDTH = #refWidth#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="contractId">
			CONTRACT_ID = #contractId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="locationName">
			LOCATION_NAME = #locationName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="crossArea">
			CROSS_AREA = #crossArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="surfaceGrade">
			SURFACE_GRADE = #surfaceGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="c_no">
			C_NO = #c_no#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="upLeftViewId">
			UP_LEFT_VIEW_ID = #upLeftViewId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="upRightViewId">
			UP_RIGHT_VIEW_ID = #upRightViewId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="idleIntervalId">
			IDLE_INTERVAL_ID = #idleIntervalId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="avaliableMinLength">
			AVALIABLE_MIN_LENGTH = #avaliableMinLength#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="occupiedFlag">
			OCCUPIED_FLAG = #occupiedFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_pointStartOrign">
			X_POINT_START_ORIGN = #x_pointStartOrign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_pointEndOrign">
			X_POINT_END_ORIGN = #x_pointEndOrign#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="pointLowerLength">
			POINT_LOWER_LENGTH = #pointLowerLength#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="pointUpperLength">
			POINT_UPPER_LENGTH = #pointUpperLength#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			FACTORY_BUILDING = #factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuildingName">
			FACTORY_BUILDING_NAME = #factoryBuildingName#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0605 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										WPROVIDER_ID,  <!-- 仓库代码 -->
										LOCATION_ID,  <!-- 库位代码 -->
										LOC_VIEW_ID,  <!-- 库位精确位置(2位流水码) -->
										LOC_COLUMN_ID,  <!-- 库位(跨+列) -->
										USE_STATUS,  <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->
										LOCATION_TYPE,  <!-- 库位类型。1：条状、2：点状 -->
										LOCATION_LENGTH,  <!-- 库位长度 单位(cm) -->
										SPEC_UPPER,  <!-- 宽度上限 单位(mm) -->
										SPEC_LOWER,  <!-- 宽度下限 单位(mm) -->
										X_POINT_START,  <!-- X轴起始点 -->
										X_POINT_END,  <!-- X轴结束点 -->
										Y_POINT_CENTER,  <!-- Y轴 -->
										LEFT_VIEW_ID,  <!-- 同层相邻左侧库位精确位置序号（下层库位属性） -->
										RIGHT_VIEW_ID,  <!-- 同层相邻右侧库位精确位置序号（下层库位属性） -->
										UP_DOWN_FLAG,  <!-- 上下层标记。1：下层、2：上层 -->
										STAND_FLAG,  <!-- 是否立式库位。0：卧式、1：立式 -->
										LR_ACCUPY_FLAG,  <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->
										JG_LOCK_FLAG,  <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->
										UP_FORBIN_FLAG,  <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->
										PACK_ID,  <!-- 捆包号 -->
										FACTORY_PRODUCT_ID,  <!-- 钢厂资源号 -->
										PRODUCT_TYPE_ID,  <!-- 品种 -->
										SHOPSIGN,  <!-- 牌号 -->
										SPEC,  <!-- 规格 -->
										INNER_DIAMETER,  <!-- 内径 单位:mm -->
										OUTER_DIAMETER,  <!-- 外径 单位:mm -->
										PUTIN_WEIGHT,  <!-- 重量 -->
										REF_WIDTH,  <!-- 宽度 单位:mm -->
										CUSTOMER_ID,  <!-- 客户代码 -->
										CONTRACT_ID,  <!-- 销售合同 -->
										REMARK,  <!-- 备注 -->
										LOCATION_NAME,  <!-- 库位名称 -->
										FACTORY_AREA,  <!-- 厂区 -->
										CROSS_AREA,  <!-- 跨区 -->
										SURFACE_GRADE,  <!-- 表面等级 -->
										C_NO,  <!-- 所在跨区的库位序号 -->
										UP_LEFT_VIEW_ID,  <!-- 跨层相邻左侧库位精确位置序号（上层库位属性） -->
										UP_RIGHT_VIEW_ID,  <!-- 跨层相邻右侧库位精确位置序号（上层库位属性） -->
										IDLE_INTERVAL_ID,  <!-- 条状库位连续空闲区间编号 -->
										AVALIABLE_MIN_LENGTH,  <!-- 条状单个库位最小可用长度(单位：厘米cm) -->
										OCCUPIED_FLAG,  <!-- 已占用库位标记 -->
										X_POINT_START_ORIGN,  <!-- X轴起始点初始值 -->
										X_POINT_END_ORIGN,  <!-- X轴结束点初始值 -->
										POINT_LOWER_LENGTH,  <!-- 点状库位长度下限 -->
										POINT_UPPER_LENGTH,  <!-- 点状库位长度上限 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID,  <!-- ID -->
										FACTORY_BUILDING,  <!-- 厂房代码 -->
										FACTORY_BUILDING_NAME  <!-- 厂房名称 -->
										)		 
	    VALUES (#segNo#, #unitCode#, #wproviderId#, #locationId#, #locViewId#, #locColumnId#, #useStatus#, #locationType#, #locationLength#, #specUpper#, #specLower#, #x_pointStart#, #x_pointEnd#, #y_pointCenter#, #leftViewId#, #rightViewId#, #upDownFlag#, #standFlag#, #lrAccupyFlag#, #jgLockFlag#, #upForbinFlag#, #packId#, #factoryProductId#, #productTypeId#, #shopsign#, #spec#, #innerDiameter#, #outerDiameter#, #putinWeight#, #refWidth#, #customerId#, #contractId#, #remark#, #locationName#, #factoryArea#, #crossArea#, #surfaceGrade#, #c_no#, #upLeftViewId#, #upRightViewId#, #idleIntervalId#, #avaliableMinLength#, #occupiedFlag#, #x_pointStartOrign#, #x_pointEndOrign#, #pointLowerLength#, #pointUpperLength#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#, #factoryBuilding#, #factoryBuildingName#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0605 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlids0605 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					WPROVIDER_ID	= #wproviderId#,   <!-- 仓库代码 -->  
					LOCATION_ID	= #locationId#,   <!-- 库位代码 -->  
					LOC_VIEW_ID	= #locViewId#,   <!-- 库位精确位置(2位流水码) -->  
					LOC_COLUMN_ID	= #locColumnId#,   <!-- 库位(跨+列) -->  
					USE_STATUS	= #useStatus#,   <!-- 状态。 10：空闲、20：预占、30：占用、99：禁用 -->  
					LOCATION_TYPE	= #locationType#,   <!-- 库位类型。1：条状、2：点状 -->  
					LOCATION_LENGTH	= #locationLength#,   <!-- 库位长度 单位(cm) -->  
					SPEC_UPPER	= #specUpper#,   <!-- 宽度上限 单位(mm) -->  
					SPEC_LOWER	= #specLower#,   <!-- 宽度下限 单位(mm) -->  
					X_POINT_START	= #x_pointStart#,   <!-- X轴起始点 -->  
					X_POINT_END	= #x_pointEnd#,   <!-- X轴结束点 -->  
					Y_POINT_CENTER	= #y_pointCenter#,   <!-- Y轴 -->  
					LEFT_VIEW_ID	= #leftViewId#,   <!-- 同层相邻左侧库位精确位置序号（下层库位属性） -->  
					RIGHT_VIEW_ID	= #rightViewId#,   <!-- 同层相邻右侧库位精确位置序号（下层库位属性） -->  
					UP_DOWN_FLAG	= #upDownFlag#,   <!-- 上下层标记。1：下层、2：上层 -->  
					STAND_FLAG	= #standFlag#,   <!-- 是否立式库位。0：卧式、1：立式 -->  
					LR_ACCUPY_FLAG	= #lrAccupyFlag#,   <!-- 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷 -->  
					JG_LOCK_FLAG	= #jgLockFlag#,   <!-- 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁 -->  
					UP_FORBIN_FLAG	= #upForbinFlag#,   <!-- 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷 -->  
					PACK_ID	= #packId#,   <!-- 捆包号 -->  
					FACTORY_PRODUCT_ID	= #factoryProductId#,   <!-- 钢厂资源号 -->  
					PRODUCT_TYPE_ID	= #productTypeId#,   <!-- 品种 -->  
					SHOPSIGN	= #shopsign#,   <!-- 牌号 -->  
					SPEC	= #spec#,   <!-- 规格 -->  
					INNER_DIAMETER	= #innerDiameter#,   <!-- 内径 单位:mm -->  
					OUTER_DIAMETER	= #outerDiameter#,   <!-- 外径 单位:mm -->  
					PUTIN_WEIGHT	= #putinWeight#,   <!-- 重量 -->  
					REF_WIDTH	= #refWidth#,   <!-- 宽度 单位:mm -->  
					CUSTOMER_ID	= #customerId#,   <!-- 客户代码 -->  
					CONTRACT_ID	= #contractId#,   <!-- 销售合同 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					LOCATION_NAME	= #locationName#,   <!-- 库位名称 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					CROSS_AREA	= #crossArea#,   <!-- 跨区 -->  
					SURFACE_GRADE	= #surfaceGrade#,   <!-- 表面等级 -->  
					C_NO	= #c_no#,   <!-- 所在跨区的库位序号 -->  
					UP_LEFT_VIEW_ID	= #upLeftViewId#,   <!-- 跨层相邻左侧库位精确位置序号（上层库位属性） -->  
					UP_RIGHT_VIEW_ID	= #upRightViewId#,   <!-- 跨层相邻右侧库位精确位置序号（上层库位属性） -->  
					IDLE_INTERVAL_ID	= #idleIntervalId#,   <!-- 条状库位连续空闲区间编号 -->  
					AVALIABLE_MIN_LENGTH	= #avaliableMinLength#,   <!-- 条状单个库位最小可用长度(单位：厘米cm) -->  
					OCCUPIED_FLAG	= #occupiedFlag#,   <!-- 已占用库位标记 -->  
					X_POINT_START_ORIGN	= #x_pointStartOrign#,   <!-- X轴起始点初始值 -->  
					X_POINT_END_ORIGN	= #x_pointEndOrign#,   <!-- X轴结束点初始值 -->  
					POINT_LOWER_LENGTH	= #pointLowerLength#,   <!-- 点状库位长度下限 -->  
					POINT_UPPER_LENGTH	= #pointUpperLength#,   <!-- 点状库位长度上限 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					UUID	= #uuid#,   <!-- ID -->  
					FACTORY_BUILDING	= #factoryBuilding#,   <!-- 厂房代码 -->  
					FACTORY_BUILDING_NAME	= #factoryBuildingName#  <!-- 厂房名称 -->  
			WHERE 	
	</update>
  
</sqlMap>