<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
    List<Map> customList = new ArrayList();
    Map<String, String> map = new HashMap();
    map.put("name", "USER");
    map.put("desc", "用户");
    customList.add(map);
    Map<String, String> map1 = new HashMap();
    map1.put("name", "USER_GROUP");
    map1.put("desc", "用户组");
    customList.add(map1);
    request.setAttribute("list", customList);
%>
<style>

    .father{
        box-sizing: border-box;
        width: 100%;
        /*height: 100%;*/
        overflow: hidden;
    }
    .leftChild{
        float: left;
        height: 100%;
        width: 25%;
        /*background-color: rgb(123, 231, 235);*/
        resize: horizontal;
        box-sizing: border-box;

    }
    .rightChild{
        float: left;
        height: 100%;
        width: calc(75% - 8px);
        /*background-color: coral;*/
        box-sizing: border-box;
    }
    #line{
        width: 8px;
        height: 100%;
        /*background-color: black;*/
        float: left;
        cursor: e-resize;
    }

</style>
<EF:EFPage prefix="imom">
    <jsp:attribute name="footer">
        <script>
            var ctx = "${ctx}";
        </script>
    </jsp:attribute>
    <jsp:body>
        <div class="father" id = "father">
            <div class="leftChild" id="leftChild">
    <%--
                <EF:EFRegion title="群组成员信息管理" id="tree" fitHeight="true">
    --%>
                <EF:EFRegion  title="视角树" id="orgGroupTree" head="hidden" fitHeight="true">
                    <EF:EFTree bindId="orgGroupsTree" ename="tree_name" textField="text" valueField="label"
                                                       hasChildren="leaf" pid="parent_id" dataSpriteCssClassField="icon"
                                                       serviceName="XSLV0500" methodName="query">
                    </EF:EFTree>
                </EF:EFRegion>
            </div>

            <div id="line"></div>
            <div class="rightChild" id="rightChild">
                <EF:EFRegion title="查询区" id="inqu">
                    <div class="row">
                        <div class="col-xs-3">
                            <div class="form-group">
                                <label class="col-md-5 control-label">
                                       登录账号
                                </label>
                                <div class="col-md-7">
                                    <input name="inqu_status-0-memberEname" data-query="gt"
                                              class="k-textbox input-time query-need"
                                              placeholder="请输入登录账号"/>
                                </div>
                               </div>
                           </div>
                        <div class="col-xs-3">
                            <div class="form-group">
                                <label class="col-md-5 control-label">
                                    用户姓名
                                </label>
                                <div class="col-md-7">
                                    <input name="inqu_status-0-memberName" data-query="gt"
                                           class="k-textbox input-time query-need"
                                           placeholder="请输入成员名称"/>
                                </div>
                            </div>
                        </div>
                        <%--<div class="col-xs-3">
                            <div class="form-group">
                                <label class="col-md-5 control-label">
                                    父节点名称
                                </label>
                                <div class="col-md-7">
                                    <input name="inqu_status-0-parentName" data-query="gt"
                                           class="k-textbox input-time query-need"
                                           placeholder="请输入父节点名称"/>
                                </div>
                            </div>
                        </div>--%>


                            <%--<EF:EFInput cname="成员名称" ename="memberName" row="0" blockId="inqu_status"/>
                                  <EF:EFInput cname="父节点名称" ename="parentName" row="0" blockId="inqu_status"/>--%>
                        <EF:EFInput type="hidden" cname="父节点ID" ename="parentId" row="0" blockId="inqu_status"
                                    name="inqu_status-0-parentId" value=""/>

                        <EF:EFSelect cname="成员类别" colWidth="3" ratio="5:7" blockId="inqu_status" ename="memberType" row="0"
                                     defaultValue="全部">
                            <EF:EFOption label="全部" value=""/>
                            <EF:EFOption label="用户组" value="USER_GROUP"/>
                            <EF:EFOption label="用户" value="USER"/>
                        </EF:EFSelect>
                        <div class="col-xs-3" style="text-align: right" id="inqu_inside"></div>
                    </div>
                </EF:EFRegion>

                <EF:EFRegion title="记录集" id="result" fitHeight="true">
                    <EF:EFInput ename="parentName" type="hidden"/>
                    <EF:EFGrid blockId="result" autoDraw="false">
                        <EF:EFColumn ename="memberId" cname="成员ID" hidden="true"/>
                        <%--<EF:EFPopupColumn ename="memberName" cname="成员名称" resultId="result" popupType="ServiceGrid"
                                          readonly="true" columnEnames="memberEname,memberName,memberTypeName"
                                          serviceName="XS03"
                                          columnCnames="登录账号/群组英文名,用户姓名/群组中文名,成员类别" methodName="queryForGridMember"
                                          valueField="memberName" backFillFieldIds="memberId,memberName,memberType"
                                          backFillColumnIds="memberId,memberName,memberType" style="text-align:left;"/>--%>
                        <EF:EFColumn ename="memberEname" cname="登录账号" readonly="true" locked="true"/>
                        <EF:EFColumn ename="memberName" cname="用户姓名" readonly="true" locked="true" />
                        <EF:EFComboColumn enable="false" ename="memberType" cname="成员类别" sourceName="list" textField="desc"
                                          valueField="name" defaultValue="USER" style="text-align:center;" locked="true"/>
                        <EF:EFColumn ename="parentId" cname="父节点ID" hidden="true"/>
                        <%--<EF:EFPopupColumn ename="parentName" cname="父节点名称"  resultId="result" popupType="ServiceGrid"
                                          readonly="true" columnEnames="parentId,parentName" serviceName="XS03"
                                          columnCnames="父节点ID,父节点名称" methodName="queryForGridParent" valueField="parentId"
                                          backFillFieldIds="parentId,parentName" backFillColumnIds="parentId,parentName"/>--%>
                        <%--<EF:EFColumn ename="parentId" cname="父节点ID" readonly="true"/>--%>
                        <EF:EFColumn ename="parentEname" cname="所属用户组英文名" enable="false" style="text-align:left;"
                                     readonly="true" locked="true"/>
                        <EF:EFColumn ename="parentName" cname="所属用户组" enable="false" style="text-align:left;"
                                     readonly="true" locked="true"/>
                        <EF:EFColumn ename="path" cname="来源" hidden="true"/>
                        <EF:EFColumn ename="sortIndex" cname="排序" style="text-align:right;"/>
                        <EF:EFColumn ename="recCreator" cname="创建人" enable="false" style="text-align:left;"/>
                        <EF:EFColumn ename="recCreateTime" cname="创建时间" enable="false" style="text-align:right;"  editType="datetime"  parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss" displayType="datetime"/>
                        <EF:EFColumn ename="recRevisor" cname="修改人" enable="false" style="text-align:left;"/>
                        <EF:EFColumn ename="recReviseTime" cname="修改时间" enable="false" style="text-align:right;"  editType="datetime"  parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss" displayType="datetime"/>
                        <EF:EFColumn ename="archiveFlag" cname="归档标记" style="text-align:right;"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>


        <EF:EFWindow id="insertUser"  width="70%" top="100px" left="150px">
            <div id="ef_popup_gridB">
                <EF:EFRegion id="inquB" title="查询条件" type="query" efRegionShowClear="true" efRegionSave="true">
                    <div class="row">
                        <EF:EFInput ename="inqu_status-0-insertUserParentId" cname="群组ID" type="hidden"/>


                        <EF:EFPopupInput ename="inqu_status-0-segNo" cname="业务单元代码" colWidth="3"
                                         readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                         center="true" required="true">
                        </EF:EFPopupInput>


                        <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true" required="true"/>


                            <EF:EFInput type="text" ename="inqu_status-0-loginName" cname="登录账号" colWidth="3"/>


                            <EF:EFInput type="text" ename="inqu_status-0-userName" cname="用户姓名" colWidth="3"/>

                        <div class="col-xs-4" style="text-align: right" id="inqub_inside"></div>
                    </div>
                </EF:EFRegion>
                <EF:EFRegion id="resultB" title="记录集">
                    <div class="text-right">
                        <%--<EF:EFButton cname="确定" ename="ef_popup_gridB_commit"></EF:EFButton>--%>
                    </div>
                    <EF:EFGrid blockId="resultB" queryMethod="queryForMemberUsers" autoDraw="false" autoBind="false">
                        <EF:EFColumn ename="segNo" locked="true" cname="业务单元号" readonly="true" width="300"/>
                        <EF:EFColumn ename="segName" locked="true" cname="业务单元名称" readonly="true" width="300"/>
                        <EF:EFColumn ename="userId" locked="true" cname="用户ID" hidden="true" primaryKey="true" />
                        <EF:EFColumn ename="loginName" locked="true" cname="登录账号" readonly="true" width="300"/>
                        <EF:EFColumn ename="userName" cname="用户姓名" readonly="true" width="300"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>

        </EF:EFWindow>
        <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="60%" height="60%"/>
    </jsp:body>

</EF:EFPage>
