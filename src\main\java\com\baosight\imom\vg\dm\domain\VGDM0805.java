package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0805;

/**
 * 月度检修维修进度安排表
 */
public class VGDM0805 extends Tvgdm0805 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0805.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0805.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0805.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0805.update";
    /**
     * 逻辑删除
     */
    public static final String UPD_FOR_DEL = "VGDM0805.updForDel";

}
