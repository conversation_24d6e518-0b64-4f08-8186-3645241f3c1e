/**
 * Generate time : 2024-11-27 16:06:03
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0401;

import java.util.Map;

/**
 * 过跨小车管理
 *
 */
public class LIDS0401 extends Tlids0401 {
    public static final String QUERY = "LIDS0401.query";
    public static final String COUNT = "LIDS0401.count";
    public static final String COUNT_UUID = "LIDS0401.count_uuid";
    public static final String INSERT = "LIDS0401.insert";
    public static final String UPDATE = "LIDS0401.update";
    public static final String UPDATE_STATUS = "LIDS0401.updateStatus";
    public static final String DELETE = "LIDS0401.delete";
    public static final String QUERY_BY_CHANNEL = "LIDS0401.queryByChannel";
    public static final String QUERY_TRANSFER_CAR_ID = "LIDS0401.queryTransferCarId";
    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS0401() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}