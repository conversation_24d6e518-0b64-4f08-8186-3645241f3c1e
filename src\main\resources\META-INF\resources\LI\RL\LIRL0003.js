$(function () {

    $("#QUERY").on("click", function (e) {
        let segNo = $("#inqu_status-0-segNo").val();
        let unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({msg: "请先选择业务单元代码!"}, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });

    //确认按钮逻辑
    $("#userAttrBtn3").on('click', function (e) {
        if (resultGrid.getCheckedRows().length <= 0) {
            NotificationUtil("确认失败，原因[请勾选记录后再进行确认！]", "error");
            e.preventDefault();
            return false;
        }
        let windowId = $("#inqu_status-0-windowId").val();
        if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
            //关闭下拉框
            window.parent[windowId + "Window"].close();
        }
    });

    IPLATUI.EFGrid = {

        "result": {

            columns: [
                {
                    field: "loadFlag",
                    headerTemplate: "装货标记",
                    template: function (e) {
                        if (e.loadFlag == "1") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "70px"
                },
                {
                    field: "unloadFlag",
                    headerTemplate: "卸货标记",
                    template: function (e) {
                        if (e.unloadFlag == "1") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "70px"
                }

            ],
            loadComplete: function (grid) {
                // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            /**
             * 编辑前
             */
            beforeEdit: function (e) {
                e.preventDefault();
                return;
            }
        }
    }
})
;
