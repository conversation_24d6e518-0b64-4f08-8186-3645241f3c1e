$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 监控点弹窗
    IMOMUtil.windowTemplate({
        windowId: "tagInfo",
        _open: function (e, iframejQuery) {
            if (IPLAT.isBlankString(unitInfo.unitCode) || IPLAT.isBlankString(unitInfo.segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitInfo.unitCode);
            iframejQuery("#inqu_status-0-segNo").val(unitInfo.segNo);
            iframejQuery("#inqu_status-0-closeFlag").val("0");
        },
        afterSelect: function (rows, iframejQuery) {
            // 关闭弹窗标记,只有点击确定按钮后关闭才会是1
            const closeFlag = iframejQuery("#inqu_status-0-closeFlag").val();
            if (closeFlag !== "1") {
                return;
            }
            if (rows.length > 0) {
                // 用,拼接tagId
                const tagIds = rows.map((row) => row.tagId).join(",");
                $("#inqu_status-0-tagId").val(tagIds);
                // 点位描述
                const tagDescs = rows.map((row) => row.tagDesc).join(",");
                $("#inqu_status-0-tagDesc").val(tagDescs);
                // 点位ID
                const tagIhdIds = rows.map((row) => row.tagIhdId).join(",");
                $("#inqu_status-0-tagIhdId").val(tagIhdIds);
            }
        }
    });
    IPLATUI.EFGrid = {
        "result": {
            // 隐藏分页栏
            pageable: false
        }
    };
});
