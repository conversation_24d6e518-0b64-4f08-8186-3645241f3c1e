package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.constants.CacheConstant;
import com.baosight.imom.common.vg.domain.Tvgdm0302;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备采集Scada信息表
 */
public class VGDM0302 extends Tvgdm0302 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0302.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0302.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0302.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0302.update";
    /**
     * 删除
     */
    public static final String DELETE = "VGDM0302.delete";

    /**
     * 从数据库查询scada信息
     *
     * @param dao       dao
     * @param scadaName scadaName
     * @return scada信息或null
     */
    private static VGDM0302 queryFromDb(Dao dao, String scadaName) {
        Map<String, String> queryMap = new HashMap<>(1);
        queryMap.put("scadaName", scadaName);
        queryMap.put("delFlag", "0");
        List list = dao.query(QUERY, queryMap);
        if (list != null && !list.isEmpty()) {
            return (VGDM0302) list.get(0);
        }
        return null;
    }

    /**
     * 根据节点名获取scada信息
     * 1.从缓存中取
     * 2.缓存中没有时从数据库取并更新缓存
     * 3.都没有返回null
     *
     * @param dao       dao
     * @param scadaName scadaName
     * @return scada信息
     */
    public static VGDM0302 queryWithCache(Dao dao, String scadaName) {
        Map cache = CacheManager.getCache(CacheConstant.CACHE_TAG);
        Object data = cache.get(scadaName);
        if (data == null) {
            VGDM0302 vgdm0302 = queryFromDb(dao, scadaName);
            if (vgdm0302 == null) {
                return null;
            }
            cache.put(scadaName, vgdm0302);
            return vgdm0302;
        }
        return (VGDM0302) data;
    }
}
