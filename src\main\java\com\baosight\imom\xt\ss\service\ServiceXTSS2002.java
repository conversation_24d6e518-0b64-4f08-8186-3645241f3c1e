package com.baosight.imom.xt.ss.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.xt.ss.domain.XTSS20;
import com.baosight.imom.xt.ss.domain.XTSS21;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

public class ServiceXTSS2002 extends ServiceBase {

    /**
     * 初始化加载
     */
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = super.query(inInfo, "XTSS20.queryMenu");
        return outInfo;


    }
}
