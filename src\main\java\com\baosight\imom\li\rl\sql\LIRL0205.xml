<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-02-07 9:06:15
   		Version :  1.0
		tableName :meli.tlirl0205 
		 RESERVATION_NUMBER  VARCHAR   NOT NULL   primarykey, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 VISIT_UNIT_CODE  VARCHAR   NOT NULL, 
		 VISIT_UNIT_NAME  VARCHAR   NOT NULL, 
		 LEVEL_NUM  INTEGER   NOT NULL, 
		 FVISIT_UNIT_CODE  VARCHAR   NOT NULL, 
		 FVISIT_UNIT_NAME  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0205">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitCode">
			VISIT_UNIT_CODE = #visitUnitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitName">
			VISIT_UNIT_NAME = #visitUnitName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="levelNum">
			LEVEL_NUM = #levelNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="fvisitUnitCode">
			FVISIT_UNIT_CODE = #fvisitUnitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="fvisitUnitName">
			FVISIT_UNIT_NAME = #fvisitUnitName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0205">
		SELECT
				RESERVATION_NUMBER	as "reservationNumber",  <!-- 预约单号 -->
				SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				VISIT_UNIT_CODE	as "visitUnitCode",  <!-- 拜访单位代码 -->
				VISIT_UNIT_NAME	as "visitUnitName",  <!-- 拜访单位 -->
				LEVEL_NUM	as "levelNum",  <!-- 层级 -->
				FVISIT_UNIT_CODE	as "fvisitUnitCode",  <!-- 上级单位代码 -->
				FVISIT_UNIT_NAME	as "fvisitUnitName",  <!-- 上级单位 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 说明备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0205 WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  RESERVATION_NUMBER asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0205 WHERE 1=1
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitCode">
			VISIT_UNIT_CODE = #visitUnitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitName">
			VISIT_UNIT_NAME = #visitUnitName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="levelNum">
			LEVEL_NUM = #levelNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="fvisitUnitCode">
			FVISIT_UNIT_CODE = #fvisitUnitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="fvisitUnitName">
			FVISIT_UNIT_NAME = #fvisitUnitName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0205 (RESERVATION_NUMBER,  <!-- 预约单号 -->
										SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										VISIT_UNIT_CODE,  <!-- 拜访单位代码 -->
										VISIT_UNIT_NAME,  <!-- 拜访单位 -->
										LEVEL_NUM,  <!-- 层级 -->
										FVISIT_UNIT_CODE,  <!-- 上级单位代码 -->
										FVISIT_UNIT_NAME,  <!-- 上级单位 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 说明备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#reservationNumber#, #segNo#, #unitCode#, #visitUnitCode#, #visitUnitName#, #levelNum#, #fvisitUnitCode#, #fvisitUnitName#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0205 WHERE 
			RESERVATION_NUMBER = #reservationNumber#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0205 
		SET 
					SEG_NO	= #segNo#,   <!-- 业务单元代代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					VISIT_UNIT_CODE	= #visitUnitCode#,   <!-- 拜访单位代码 -->  
					VISIT_UNIT_NAME	= #visitUnitName#,   <!-- 拜访单位 -->  
					LEVEL_NUM	= #levelNum#,   <!-- 层级 -->  
					FVISIT_UNIT_CODE	= #fvisitUnitCode#,   <!-- 上级单位代码 -->  
					FVISIT_UNIT_NAME	= #fvisitUnitName#,   <!-- 上级单位 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 说明备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			RESERVATION_NUMBER = #reservationNumber#
	</update>
  
</sqlMap>