<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-11-26 15:32:06
   		Version :  1.0
		tableName :iplat4j.xs_switch
		 SEG_NO  VARCHAR   NOT NULL,
		 UNIT_CODE  VARCHAR   NOT NULL,
		 UUID  VARCHAR   NOT NULL   primarykey,
		 REC_CREATOR  VARCHAR   NOT NULL,
		 REC_CREATOR_NAME  VARCHAR   NOT NULL,
		 REC_CREATE_TIME  VARCHAR   NOT NULL,
		 REC_REVISOR  VARCHAR   NOT NULL,
		 REC_REVISOR_NAME  VARCHAR   NOT NULL,
		 REC_REVISE_TIME  VARCHAR   NOT NULL,
		 ARCHIVE_FLAG  VARCHAR   NOT NULL,
		 DEL_FLAG  SMALLINT   NOT NULL,
		 TENANT_USER  VARCHAR   NOT NULL,
		 PROCESS_SWITCH_NAME  VARCHAR   NOT NULL,
		 PROCESS_SWITCH_DESC  VARCHAR   NOT NULL,
		 PROCESS_SWITCH_VALUE  VARCHAR   NOT NULL,
		 PROCESS_SWITCH_VALUE_DESC  VARCHAR   NOT NULL
	-->
<sqlMap namespace="XTSS04">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSwitchName">
			PROCESS_SWITCH_NAME = #processSwitchName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSwitchDesc">
			PROCESS_SWITCH_DESC LIKE concat('%',#processSwitchDesc#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTimeStart">
			SUBSTR(REC_CREATE_TIME,1,8) &gt;= #recCreateTimeStart#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTimeEnd">
			SUBSTR(REC_CREATE_TIME,1,8) &lt;= #recCreateTimeEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="epassAuthFilter">
			($epassAuthFilter$)
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.xt.ss.domain.XTSS04">
		SELECT
		UUID as "uuid",  <!-- ID -->
		PROCESS_SWITCH_NAME as "processSwitchName",  <!-- 生产开关名称 -->
		PROCESS_SWITCH_DESC as "processSwitchDesc",  <!-- 生产开关描述 -->
		PROCESS_SWITCH_VALUE_DESC as "processSwitchValueDesc", <!-- 生产开关值描述 -->
		REC_CREATE_TIME as "recCreateTime"  <!-- 记录修改时间 -->
		FROM (SELECT UUID,
		PROCESS_SWITCH_NAME,
		PROCESS_SWITCH_DESC,
		PROCESS_SWITCH_VALUE_DESC,
		REC_CREATE_TIME,
		@row_num := IF(@current_process_switch_name = PROCESS_SWITCH_NAME, @row_num + 1, 1) AS row_num,
		@current_process_switch_name := PROCESS_SWITCH_NAME
		FROM ${platSchema}.xs_switch, (SELECT @row_num := 0, @current_process_switch_name := NULL) vars
		WHERE DEL_FLAG = 0
		AND (SEG_NO IS NULL OR TRIM(SEG_NO) = '')
		AND (UNIT_CODE IS NULL OR TRIM(UNIT_CODE) = '')
		ORDER BY PROCESS_SWITCH_NAME, REC_CREATE_TIME ASC
		) t
		WHERE row_num = 1
		<include refid="condition" />
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME DESC, UUID DESC
			</isEmpty>
		</dynamic>
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(1)	FROM
		(
		SELECT UUID,PROCESS_SWITCH_NAME,PROCESS_SWITCH_DESC,PROCESS_SWITCH_VALUE_DESC,REC_CREATE_TIME
		,row_number()over(partition by PROCESS_SWITCH_NAME order by REC_CREATE_TIME asc) as row
		FROM ${platSchema}.xs_switch
		WHERE DEL_FLAG = 0
		AND (SEG_NO IS NULL OR SEG_NO = '')
		AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
		) t
		WHERE row=1
		<include refid="condition" />
	</select>


	<insert id="insert">
		INSERT INTO ${platSchema}.xs_switch
		<dynamic  prepend="(" close=")" >
			<isNotEmpty property="segNo" prepend=",">SEG_NO</isNotEmpty>
			<isNotEmpty property="unitCode" prepend=",">UNIT_CODE</isNotEmpty>
			<isNotEmpty property="uuid" prepend=",">UUID</isNotEmpty>
			<isNotEmpty property="recCreator" prepend=",">REC_CREATOR</isNotEmpty>
			<isNotEmpty property="recCreatorName" prepend=",">REC_CREATOR_NAME</isNotEmpty>
			<isNotEmpty property="recCreateTime" prepend=",">REC_CREATE_TIME</isNotEmpty>
			<isNotEmpty property="recRevisor" prepend=",">REC_REVISOR</isNotEmpty>
			<isNotEmpty property="recRevisorName" prepend=",">REC_REVISOR_NAME</isNotEmpty>
			<isNotEmpty property="recReviseTime" prepend=",">REC_REVISE_TIME</isNotEmpty>
			<isNotEmpty property="archiveFlag" prepend=",">ARCHIVE_FLAG</isNotEmpty>
			<isNotEmpty property="delFlag" prepend=",">DEL_FLAG</isNotEmpty>
			<isNotEmpty property="tenantUser" prepend=",">TENANT_USER</isNotEmpty>
			<isNotEmpty property="processSwitchName" prepend=",">PROCESS_SWITCH_NAME</isNotEmpty>
			<isNotEmpty property="processSwitchDesc" prepend=",">PROCESS_SWITCH_DESC</isNotEmpty>
			<isNotEmpty property="processSwitchValue" prepend=",">PROCESS_SWITCH_VALUE</isNotEmpty>
			<isNotEmpty property="processSwitchValueDesc" prepend=",">PROCESS_SWITCH_VALUE_DESC</isNotEmpty>
		</dynamic>
		VALUES
		<dynamic prepend="(" close=")" >
			<isNotEmpty property="segNo" prepend=",">#segNo#</isNotEmpty>
			<isNotEmpty property="unitCode" prepend=",">#unitCode#</isNotEmpty>
			<isNotEmpty property="uuid" prepend=",">#uuid#</isNotEmpty>
			<isNotEmpty property="recCreator" prepend=",">#recCreator#</isNotEmpty>
			<isNotEmpty property="recCreatorName" prepend=",">#recCreatorName#</isNotEmpty>
			<isNotEmpty property="recCreateTime" prepend=",">#recCreateTime#</isNotEmpty>
			<isNotEmpty property="recRevisor" prepend=",">#recRevisor#</isNotEmpty>
			<isNotEmpty property="recRevisorName" prepend=",">#recRevisorName#</isNotEmpty>
			<isNotEmpty property="recReviseTime" prepend=",">#recReviseTime#</isNotEmpty>
			<isNotEmpty property="archiveFlag" prepend=",">#archiveFlag#</isNotEmpty>
			<isNotEmpty property="delFlag" prepend=",">#delFlag#</isNotEmpty>
			<isNotEmpty property="tenantUser" prepend=",">#tenantUser#</isNotEmpty>
			<isNotEmpty property="processSwitchName" prepend=",">#processSwitchName#</isNotEmpty>
			<isNotEmpty property="processSwitchDesc" prepend=",">#processSwitchDesc#</isNotEmpty>
			<isNotEmpty property="processSwitchValue" prepend=",">#processSwitchValue#</isNotEmpty>
			<isNotEmpty property="processSwitchValueDesc" prepend=",">#processSwitchValueDesc#</isNotEmpty>
		</dynamic>
	</insert>

	<delete id="delete">
		DELETE FROM ${platSchema}.xs_switch WHERE UUID = #uuid# AND UNIT_CODE = #unitCode# AND SEG_NO = #segNo#
	</delete>

	<update id="update">
		UPDATE ${platSchema}.xs_switch
		<dynamic prepend="set">
			<isNotEmpty property="recCreator" prepend=",">REC_CREATOR = #recCreator#</isNotEmpty>
			<isNotEmpty property="recCreatorName" prepend=",">REC_CREATOR_NAME = #recCreatorName#</isNotEmpty>
			<isNotEmpty property="recCreateTime" prepend=",">REC_CREATE_TIME = #recCreateTime#</isNotEmpty>
			<isNotEmpty property="recRevisor" prepend=",">REC_REVISOR= #recRevisor#</isNotEmpty>
			<isNotEmpty property="recRevisorName" prepend=",">REC_REVISOR_NAME = #recRevisorName#</isNotEmpty>
			<isNotEmpty property="recReviseTime" prepend=",">REC_REVISE_TIME = #recReviseTime#</isNotEmpty>
			<isNotEmpty property="archiveFlag" prepend=",">ARCHIVE_FLAG = #archiveFlag#</isNotEmpty>
			<isNotEmpty property="delFlag" prepend=",">DEL_FLAG = #delFlag#</isNotEmpty>
			<isNotEmpty property="tenantUser" prepend=",">TENANT_USER = #tenantUser#</isNotEmpty>
			<isNotEmpty property="processSwitchName" prepend=",">PROCESS_SWITCH_NAME = #processSwitchName#</isNotEmpty>
			<isNotEmpty property="processSwitchDesc" prepend=",">PROCESS_SWITCH_DESC = #processSwitchDesc#</isNotEmpty>
			<isNotEmpty property="processSwitchValue" prepend=",">PROCESS_SWITCH_VALUE = #processSwitchValue#</isNotEmpty>
			<isNotEmpty property="processSwitchValueDesc" prepend=",">PROCESS_SWITCH_VALUE_DESC = #processSwitchValueDesc#</isNotEmpty>
		</dynamic>
		WHERE UUID = #uuid#
		AND UNIT_CODE = #unitCode#
		AND SEG_NO = #segNo#
	</update>

	<select id="queryDetail" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.xt.ss.domain.XTSS04">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		UUID	as "uuid",  <!-- ID -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		TENANT_USER	as "tenantUser",  <!-- 租户 -->
		PROCESS_SWITCH_NAME	as "processSwitchName",  <!-- 生产开关名称 -->
		PROCESS_SWITCH_DESC	as "processSwitchDesc",  <!-- 生产开关描述 -->
		PROCESS_SWITCH_VALUE	as "processSwitchValue",  <!-- 生产开关值 -->
		PROCESS_SWITCH_VALUE_DESC	as "processSwitchValueDesc", <!-- 生产开关值描述 -->
		(
		SELECT DISTINCT a.SEG_NAME FROM IPLAT4J.TVZBM81 a
		WHERE a.DEL_FLAG = 0 AND a.SEG_LEVEL = 0 AND a.SEG_STATUS = '20' AND a.SEG_NO = t.SEG_NO
		) "segName"  <!-- 业务单元简称 -->
		FROM ${platSchema}.xs_switch t WHERE DEL_FLAG = 0
		AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '' AND SEG_NO &lt;&gt; ' ')
		AND (UNIT_CODE IS NOT NULL AND UNIT_CODE &lt;&gt; '' AND UNIT_CODE &lt;&gt; ' ')
		<include refid="condition" />
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME DESC, UUID DESC
			</isEmpty>
		</dynamic>

	</select>

	<select id="queryDetailCount" parameterClass="java.util.HashMap" resultClass="int">
		SELECT COUNT(1)
		FROM ${platSchema}.xs_switch t WHERE DEL_FLAG = 0
		AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '' AND SEG_NO &lt;&gt; ' ')
		AND (UNIT_CODE IS NOT NULL AND UNIT_CODE &lt;&gt; '' AND UNIT_CODE &lt;&gt; ' ')
		AND SEG_NO = #segNo#
		AND UNIT_CODE = #unitCode#
		AND PROCESS_SWITCH_NAME = #processSwitchName#
	</select>

<!--	查询当前开关下开关值是1的数据-->
	<select id="queryList" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		UUID	as "uuid",  <!-- ID -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		TENANT_USER	as "tenantUser",  <!-- 租户 -->
		PROCESS_SWITCH_NAME	as "processSwitchName",  <!-- 生产开关名称 -->
		PROCESS_SWITCH_DESC	as "processSwitchDesc",  <!-- 生产开关描述 -->
		PROCESS_SWITCH_VALUE	as "processSwitchValue",  <!-- 生产开关值 -->
		PROCESS_SWITCH_VALUE_DESC	as "processSwitchValueDesc" <!-- 生产开关值描述 -->
		FROM ${platSchema}.xs_switch WHERE DEL_FLAG = 0
		AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '')
		AND (UNIT_CODE IS NOT NULL AND UNIT_CODE &lt;&gt; '')
		AND PROCESS_SWITCH_NAME like '%$processSwitchName$%'
		AND PROCESS_SWITCH_VALUE = '1'
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME DESC, UUID DESC
			</isEmpty>
		</dynamic>
	</select>

	<select id="countDetail" resultClass="int">
		SELECT COUNT(1) FROM ${platSchema}.xs_switch WHERE DEL_FLAG = 0
		AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '')
		AND (UNIT_CODE IS NOT NULL OR UNIT_CODE &lt;&gt; '')
		<include refid="condition" />
	</select>

	<!-- 根据生产开关名称获取生产开关值,主要数据表:xs_switch_系统开关维护表 -->
	<select id="queryOneSwitchInfo" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.xt.ss.domain.XTSS04">
		SELECT
		UUID	as "uuid",  <!-- ID -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		TENANT_USER	as "tenantUser",  <!-- 租户 -->
		PROCESS_SWITCH_NAME	as "processSwitchName",  <!-- 生产开关名称 -->
		PROCESS_SWITCH_DESC	as "processSwitchDesc",  <!-- 生产开关描述 -->
		PROCESS_SWITCH_VALUE	as "processSwitchValue",  <!-- 生产开关值 -->
		PROCESS_SWITCH_VALUE_DESC	as "processSwitchValueDesc" <!-- 生产开关值描述 -->
		FROM ${platSchema}.xs_switch
		WHERE DEL_FLAG = 0
		AND (SEG_NO IS NULL OR SEG_NO = '')
		AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
		AND PROCESS_SWITCH_NAME = #processSwitchName#
		ORDER BY REC_CREATE_TIME ASC
		limit 1
	</select>

	<!-- 开关定义修改 -->
	<update id="update2">
		UPDATE ${platSchema}.xs_switch
		<dynamic prepend="set">
			<isNotEmpty property="recCreator" prepend=",">REC_CREATOR = #recCreator#</isNotEmpty>
			<isNotEmpty property="recCreatorName" prepend=",">REC_CREATOR_NAME = #recCreatorName#</isNotEmpty>
			<isNotEmpty property="recCreateTime" prepend=",">REC_CREATE_TIME = #recCreateTime#</isNotEmpty>
			<isNotEmpty property="recRevisor" prepend=",">REC_REVISOR= #recRevisor#</isNotEmpty>
			<isNotEmpty property="recRevisorName" prepend=",">REC_REVISOR_NAME = #recRevisorName#</isNotEmpty>
			<isNotEmpty property="recReviseTime" prepend=",">REC_REVISE_TIME = #recReviseTime#</isNotEmpty>
			<isNotEmpty property="archiveFlag" prepend=",">ARCHIVE_FLAG = #archiveFlag#</isNotEmpty>
			<isNotEmpty property="delFlag" prepend=",">DEL_FLAG = #delFlag#</isNotEmpty>
			<isNotEmpty property="tenantUser" prepend=",">TENANT_USER = #tenantUser#</isNotEmpty>
			<isNotEmpty property="processSwitchName" prepend=",">PROCESS_SWITCH_NAME = #processSwitchName#</isNotEmpty>
			<isNotEmpty property="processSwitchDesc" prepend=",">PROCESS_SWITCH_DESC = #processSwitchDesc#</isNotEmpty>
			<isNotEmpty property="processSwitchValueDesc" prepend=",">PROCESS_SWITCH_VALUE_DESC = #processSwitchValueDesc#</isNotEmpty>
		</dynamic>
		WHERE UUID = #uuid#
		AND (SEG_NO IS NULL OR SEG_NO = '')
		AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
	</update>

	<!-- 开关定义删除 -->
	<delete id="delete2">
		DELETE FROM ${platSchema}.xs_switch WHERE UUID = #uuid#
		AND (SEG_NO IS NULL OR SEG_NO = '') AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
	</delete>

	<!-- 查询开关定义下的开关配置总数 -->
	<select id="querySwitchSettingCount" resultClass="int">
		SELECT COUNT(1) FROM ${platSchema}.xs_switch
		WHERE DEL_FLAG = 0
		AND PROCESS_SWITCH_NAME = #processSwitchName#
		AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '')
		AND (UNIT_CODE IS NOT NULL OR UNIT_CODE &lt;&gt; '')
	</select>

	<!-- 查询开关定义的生产开关名称总数 -->
	<select id="queryProcessSwitchNameCount" resultClass="int">
		SELECT COUNT(1) FROM ${platSchema}.xs_switch
		WHERE DEL_FLAG = 0
		AND PROCESS_SWITCH_NAME = #processSwitchName#
		AND (SEG_NO IS NULL OR SEG_NO = '')
		AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
		<isNotEmpty prepend=" AND " property="uuid">
			UUID &lt;&gt; #uuid#
		</isNotEmpty>
	</select>
  
</sqlMap>
