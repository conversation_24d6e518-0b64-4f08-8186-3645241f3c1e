<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-08 9:16:20
       Version :  1.0
    tableName :${meliSchema}.tlirl0106
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     RESERVATION_TIME  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0106">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            a.RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            a.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            a.TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="weekDays">
            a.WEEK_DAYS = #weekDays#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0106">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0106 a WHERE 1=1
        <include refid="condition"/>
<!--        <isNotEmpty prepend="and " property="flag">-->
<!--            NOT EXISTS(SELECT 1-->
<!--            FROM MELI.tlirl0317 tlirl0317-->
<!--            WHERE tlirl0317.STATUS = '20'-->
<!--            AND tlirl0317.SEG_NO = a.SEG_NO-->
<!--            AND a.RESERVATION_TIME BETWEEN tlirl0317.REV_START_TIME AND tlirl0317.REV_END_TIME-->
<!--            and tlirl0317.TYPE_OF_HANDLING=#typeOfHandling#-->
<!--            )-->
<!--        </isNotEmpty>-->
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                reservation_time asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0106">
       select * from ( select
        tlirl0106.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0106.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        tlirl0106.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        tlirl0106.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
        tlirl0106.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        tlirl0106.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        tlirl0106.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        tlirl0106.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        tlirl0106.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        tlirl0106.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        tlirl0106.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        tlirl0106.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        tlirl0106.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        tlirl0106.REMARK as "remark",  <!-- 备注 -->
        tlirl0106.UUID as "uuid",  <!-- uuid -->
        tlirl0106.TENANT_ID as "tenantId" <!-- 租户ID -->
        from meli.tlirl0106 tlirl0106
        where STATUS = '20'
        and SEG_NO = #segNo#
        AND WEEK_DAYS=#weekDays#
        AND  EXISTS(SELECT 1
        FROM MELI.tlirl0317 tlirl0317
        WHERE tlirl0317.STATUS = '20'
        AND tlirl0317.SEG_NO = tlirl0106.SEG_NO
        and SUBSTRING(TRIM(tlirl0106.RESERVATION_TIME), 1, LOCATE('-', TRIM(tlirl0106.RESERVATION_TIME)) + 1) BETWEEN #minTime# AND #maxTime#)
        union
        select
        tlirl0106.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0106.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        tlirl0106.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        tlirl0106.STATUS as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
        tlirl0106.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        tlirl0106.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        tlirl0106.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        tlirl0106.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        tlirl0106.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        tlirl0106.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        tlirl0106.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        tlirl0106.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        tlirl0106.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        tlirl0106.REMARK as "remark",  <!-- 备注 -->
        tlirl0106.UUID as "uuid",  <!-- uuid -->
        tlirl0106.TENANT_ID as "tenantId" <!-- 租户ID -->
        from meli.tlirl0106 tlirl0106
        where STATUS = '20'
        and SEG_NO = #segNo#
        AND WEEK_DAYS=#weekDays#
        AND not  EXISTS(SELECT 1
        FROM MELI.tlirl0317 tlirl0317
        WHERE tlirl0317.STATUS = '20'
        AND tlirl0317.SEG_NO = tlirl0106.SEG_NO
        AND tlirl0106.RESERVATION_TIME BETWEEN tlirl0317.REV_START_TIME AND tlirl0317.REV_END_TIME
        and tlirl0317.TYPE_OF_HANDLING=#typeOfHandling#
        )) A
        order by A.reservationTime asc
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0106 a WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0106 (SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        STATUS,  <!-- 状态(撤销：00、新增：10、生效：20) -->
        RESERVATION_TIME,  <!-- 预约时段 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        UUID  <!-- uuid -->
        )
        VALUES (#segNo#, #unitCode#, #status#, #reservationTime#, #recCreator#, #recCreatorName#, #recCreateTime#,
        #recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#, #remark#, #uuid#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0106 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0106
        SET
        STATUS = #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->
        RESERVATION_TIME = #reservationTime#,   <!-- 预约时段 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#   <!-- 备注 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>