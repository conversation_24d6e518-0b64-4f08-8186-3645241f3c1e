package com.baosight.imom.li.ds.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.*;
import com.baosight.imom.li.rl.dao.LIRL0304;
import com.baosight.imom.li.rl.dao.LIRL0503;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线边物流模块对外相关接口
 *
 * <AUTHOR> 徐攀
 * @Description :对外相关接口
 * @Date : 2024/12/9
 * @Version : 1.0
 */
public class ServiceLIDSInterfaces extends ServiceBase {
    private static final RedisUtil redisUtil = PlatApplicationContext.getBean("redisUtil",RedisUtil.class);
    private String uwbIp = PlatApplicationContext.getProperty("JC.uwbHost"); // IP地址
    private String uwbPort = PlatApplicationContext.getProperty("JC.uwbPort");// 端口号

    /***
     *
     *查询仓库列表(IMC)
     * @param inInfo
     * @return
     */
    public EiInfo queryWarehouseList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //查询条件
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo");
            if (StringUtils.isBlank(segNo)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                log("LIDS01.queryWarehouseList!");
                outInfo.setMsg("传入业务单元代码为空!");
                return outInfo;
            }
            //分页参数
            int limit = inInfo.getInt("limit") == 0 ? 10 : inInfo.getInt("limit");
            int offset = inInfo.getInt("offset");
            inInfo.set("segNo", segNo);
            inInfo.set("stockCode", MapUtils.getString(queryMap, "warehouseCode"));
            inInfo.set("stockName", MapUtils.getString(queryMap, "warehouseName"));
            inInfo.set("warehouseBusinessType", MapUtils.getString(queryMap, "wareHouseBusinessType"));
            inInfo.set("limit", limit);
            inInfo.set("offset", offset);
            inInfo.set(EiConstant.serviceId, "S_UA_CM_0003");
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());//打印日志到elk
            log(DateUtil.getTimeNow(new Date()) + "：" + "仓库信息查询信息传入参数：" + JSONObject.fromObject(queryMap) + "\n" + "仓库信息返回的参数：" + JSON.parseObject(outInfo.getString("messageBody")).get("resultD"));
            //输出到应用日志
            System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "仓库信息查询信息传入参数：" + JSONObject.fromObject(queryMap) + "\n" + "仓库信息返回的参数：" + JSON.parseObject(outInfo.getString("messageBody")).get("resultD"));

            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                throw new PlatException("查询失败：" + outInfo.getMsg());
            }

            try {
                //返回仓库信息
                String messageBody = outInfo.getString("messageBody");
                Map result = JSON.parseObject(messageBody);
                JSONArray resultD = (JSONArray) result.get("resultD");
                List<Map> warehouseList = JSONArray.parseArray(resultD.toJSONString(), Map.class);
                outInfo.addBlock(EiConstant.resultBlock).addRows(warehouseList);
            } catch (NullPointerException e) {
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("未查到记录！");
                return outInfo;
            }
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        return outInfo;
    }


    /**
     * 库存管理对外倒库接口(IMC)
     *
     * @param inInfo
     * @return
     */
    public EiInfo transferInventory(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //传入参数
            List<Map> dataRows = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (CollectionUtils.isEmpty(dataRows)) {
                throw new PlatException("传入数据为空！");
            }

            //发送参数
            List<Map> stockOutDataList = new ArrayList<>();
            for (Map rowMap : dataRows) {
                Map stockOutData = new HashMap();
                String locationId = MapUtils.getString(rowMap, "locationId");
                if (StringUtils.isBlank(locationId)) {
                    locationId = MapUtils.getString(rowMap, "areaCode");
                }
                String locationName = MapUtils.getString(rowMap, "locationName");
                if (StringUtils.isBlank(locationName)) {
                    locationName = MapUtils.getString(rowMap, "areaName");
                }
                if (StringUtils.isBlank(MapUtils.getString(rowMap, "segNo"))) {
                    throw new PlatException("传入系统账套为空！");
                }
                if (StringUtils.isBlank(MapUtils.getString(rowMap, "packId"))) {
                    throw new PlatException("传入捆包号为空！");
                }
                if (StringUtils.isBlank(MapUtils.getString(rowMap, "warehouseCode"))
                        || StringUtils.isBlank(MapUtils.getString(rowMap, "warehouseName"))) {
                    throw new PlatException("传入仓库代码或仓库名称为空！");
                }
                if (StringUtils.isBlank(locationId)
                        || StringUtils.isBlank(locationName)) {
                    throw new PlatException("传入库位代码或库位名称为空！");
                }
                stockOutData.put("segNo", MapUtils.getString(rowMap, "segNo"));
                stockOutData.put("packId", MapUtils.getString(rowMap, "packId"));
                stockOutData.put("warehouseCode", MapUtils.getString(rowMap, "warehouseCode"));
                stockOutData.put("warehouseName", MapUtils.getString(rowMap, "warehouseName"));
                stockOutData.put("locationId", MapUtils.getString(rowMap, "locationId"));
                stockOutData.put("locationName", MapUtils.getString(rowMap, "locationName"));
                stockOutData.put("loginUser", UserSession.getUserId());
                stockOutDataList.add(stockOutData);

                //插入库位变更履历表
                String putoutLocationId = MapUtils.getString(rowMap, "putoutLocationId", "");//转出库位代码
                String putoutLocationName = MapUtils.getString(rowMap, "putoutLocationName", "");//转出库位名称
                String userId = MapUtils.getString(rowMap, "userId", "");//工号
                String userName = MapUtils.getString(rowMap, "userName", "");//姓名
                String craneResultId = MapUtils.getString(rowMap, "craneResultId", "");//行车实绩单号
                BigDecimal netWeight = NumberUtils.toBigDecimal(MapUtils.getString(rowMap, "netWeight", "0"),BigDecimal.ZERO);//重量
                BigDecimal quantity = NumberUtils.toBigDecimal(MapUtils.getString(rowMap, "quantity", "0"),BigDecimal.ZERO);//数量
                stockOutData.put("netWeight", netWeight);
                stockOutData.put("quantity", quantity);
                setLocationChanger(stockOutData, craneResultId, userId, userName, DateUtil.curDateTimeStr14(), putoutLocationId, putoutLocationName);

            }


            //调用倒库实绩接口
            EiInfo sendInfo = new EiInfo();
            sendInfo.set("list", stockOutDataList);
            //设置登录人信息
            Map loginUser = RecordUtils.getLoginUser(inInfo);
            sendInfo.addBlock("loginUser").addRow(loginUser);
            sendInfo.set(EiConstant.serviceId, "S_UE_DS_0040");
            outInfo = EServiceManager.call(sendInfo, TokenUtils.getXplatToken());
            log("调用IMC物流倒库实绩传入参数：" + stockOutDataList + ",调用IMC物流倒库实绩返回参数：" + outInfo.toJSONString());
            System.out.println("调用IMC物流倒库实绩传入参数：" + stockOutDataList + ",调用IMC物流倒库实绩返回参数：" + outInfo.toJSONString());
            if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException("调用IMC物流倒库实绩接口失败：" + outInfo.getMsg());
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 插入履历表，清除捆包库存中的库位，xyz轴，层数标记等字段
     */
    public EiInfo resetBundleDetails(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map queryMap = new HashMap();
        try {
            //系统账套
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new PlatException("传入系统账套为空！");
            }
            //捆包列表
            List<String> packIdList = (List<String>) inInfo.get("packIdList");
            if (CollectionUtils.isEmpty(packIdList)) {
                throw new PlatException("传入捆包号为空！");
            }
            packIdList = packIdList.stream()
                    .map(id -> "'" + id + "'")
                    .collect(Collectors.toList());
            //取出捆包号条件
            String packIds = StringUtils.join(packIdList, ",");
            queryMap.put("segNo", segNo);
            queryMap.put("packIds", packIds);
            queryMap.put("craneResultId",inInfo.getString("craneResultId"));
            queryMap.put("actionFlag",inInfo.getString("actionFlag"));

            //将捆包信息插入库存履历表
            EiInfo sendInfo = new EiInfo();
            sendInfo.set("queryMap", queryMap);
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "inertInterfaceList");
            sendInfo = XLocalManager.callNoTx(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
            }

            //清空当前库存表相关数据
            int count = dao.update(LIDS0903.RESET_PACK_XYZ, queryMap);
            /*if (count != packIdList.size()) {
                throw new PlatException("清空库存表相关数据失败！【修改数据与传入捆包条数不一致】");
            }*/

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }finally {
            log("调用ServiceLIDSInterfaces#resetBundleDetails传入参数：" + queryMap);
            System.out.println("调用ServiceLIDSInterfaces#resetBundleDetails传入参数：" + queryMap);
        }
        return outInfo;
    }

    /**
     * 将捆包信息插入库存履历表
     * @param inInfo
     * @return
     */
    public EiInfo inertInterfaceList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getMap("queryMap");
            log("插入库存履历表：" + queryMap);
            System.out.println("插入库存履历表：" + queryMap);
            //加一个标记，行车成品出库时插入备份表
            String outMark = MapUtils.getString(queryMap, "outMark");
            if (StringUtils.isNotBlank(outMark)) {
                //将当前数据插入库存备份表
                dao.insert(LIDS0902.INERT_INTERFACE_LIST, queryMap);

                //将捆包状态修改为出库，删除标记为1
                queryMap.put("status","30");
                queryMap.put("delFlag","1");
                dao.update(LIDS0901.UPDATE_STATUS, queryMap);
            } else {
                //将当前数据插入库存履历表
                dao.insert(LIDS0903.INERT_INTERFACE_LIST, queryMap);
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * UWB抓取释放信息上传
     */
    public EiInfo uploadUwbInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //打印日志
            log("调用ServiceLIDSInterfaces.uploadUwbInfo传入参数：" + inInfo.getAttr());
            System.out.println("调用ServiceLIDSInterfaces.uploadUwbInfo传入参数：" + inInfo.getAttr());

            //系统账套
            String segNo = inInfo.getString("seg_no");
            //行车编号
            String craneId = inInfo.getString("crane_id");
            //行车名称
            String craneName = inInfo.getString("crane_name");
            //动作类型
            String actionType = inInfo.getString("action_type");
            //抓取流水号
            String grabSysId = inInfo.getString("grab_sys_id");
            //释放流水号
            String releaseSysId = inInfo.getString("release_sys_id");
            //X轴值
            String x_value = inInfo.getString("x_value");
            double num = Double.parseDouble(x_value);
            x_value = String.format("%.2f", num);
            //Y轴值
            String y_value = inInfo.getString("y_value");
            double numY = Double.parseDouble(y_value);
            y_value = String.format("%.2f", numY);
            //Z轴值
            String z_value = inInfo.getString("z_value");
            double numZ = Double.parseDouble(z_value);
            z_value = String.format("%.2f", numZ);
            //重量
            String weight = inInfo.getString("weight");
            double doubleWeight = Double.parseDouble(weight);
            weight = String.format("%.8f", doubleWeight);
            //上传时间
            String uploadTime = inInfo.getString("upload_time");

            //插入Map
            Map paramMap = new HashMap();
            paramMap.put("status", "10");
            paramMap.put("remark", "接收成功");
            //校验参数
            StringBuilder msg = new StringBuilder();
            if (StringUtils.isBlank(segNo)) {
                msg.append("传入字段【系统账套】为空！");
            }
            if (StringUtils.isBlank(craneId) || StringUtils.isBlank(craneName)) {
                msg.append("传入字段【行车编号】或【行车名称】为空！");
            }
            if (StringUtils.isBlank(actionType)) {
                msg.append("传入字段【动作类型】为空！");
            }
            if (StringUtils.isBlank(grabSysId)) {
                msg.append("传入字段【抓取流水号】为空！");
            }
            if ("2".equals(actionType) && StringUtils.isBlank(releaseSysId)) {
                msg.append("动作类型为【释放】，传入字段【释放流水号】不能为空！");
            }
            if (StringUtils.isBlank(x_value)) {
                msg.append("传入字段【X轴值】为空！");
            }
            if (StringUtils.isBlank(y_value)) {
                msg.append("传入字段【Y轴值】为空！");
            }
            if (StringUtils.isBlank(z_value)) {
                msg.append("传入字段【Z轴值】为空！");
            }
            if (StringUtils.isBlank(weight)) {
                msg.append("传入字段【重量】为空！");
            }
            if (StringUtils.isBlank(uploadTime)) {
                msg.append("传入字段【上传时间】为空！");
            }
            //赋值状态与备注
            if (msg.length() > 0) {
                paramMap.put("status", "00");
                paramMap.put("remark", msg.toString());
            }
            paramMap.put("unitCode", StringUtils.defaultString(segNo, ""));
            paramMap.put("segNo", StringUtils.defaultString(segNo, ""));
            paramMap.put("craneId", StringUtils.defaultString(craneId, ""));
            paramMap.put("craneName", StringUtils.defaultString(craneName, ""));
            paramMap.put("actionType", StringUtils.defaultString(actionType, ""));
            paramMap.put("grabSysId", StringUtils.defaultString(grabSysId, ""));
            paramMap.put("releaseSysId", StringUtils.defaultString(releaseSysId, ""));
            paramMap.put("x_value", StringUtils.defaultString(x_value, ""));
            paramMap.put("y_value", StringUtils.defaultString(y_value, ""));
            paramMap.put("z_value", StringUtils.defaultString(z_value, ""));
            paramMap.put("weight", NumberUtils.toBigDecimal(weight, BigDecimal.ZERO));
            paramMap.put("uploadTime", StringUtils.defaultString(uploadTime, DateUtil.curDateTimeStr14()));
            paramMap.put("uuid", UUIDUtils.getUUID());

            //根据XY轴查询库位附属信息
            paramMap.put("XPosition",StringUtils.defaultString(x_value, ""));
            paramMap.put("YPosition",StringUtils.defaultString(y_value, ""));
            /*List<LIDS0601> lids0601s = dao.query(LIDS0601.QUERY_LOCATION_ID, paramMap);
            if (CollectionUtils.isNotEmpty(lids0601s)) {
                LIDS0601 lids0601 = lids0601s.get(0);
                paramMap.put("locationId", lids0601.getLocationId());
                paramMap.put("locationName", lids0601.getLocationName());
            }*/
            // 创建人工号
            paramMap.put("recCreator", "system");
            // 创建人姓名
            paramMap.put("recCreatorName", "system");
            // 创建时间
            paramMap.put("recCreateTime", DateUtil.curDateTimeStr14());
            // 修改人工号
            paramMap.put("recRevisor", "system");
            // 修改人姓名
            paramMap.put("recRevisorName", "system");
            // 修改时间
            paramMap.put("recReviseTime", DateUtil.curDateTimeStr14());

            //根据动作类型，抓取流水号，释放流水号判断接口表中是否已存在数据
            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("craneId", craneId);
            queryMap.put("actionType", actionType);
            queryMap.put("grabSysId", grabSysId);
            queryMap.put("releaseSysId", releaseSysId);
            if (CollectionUtils.isNotEmpty(dao.query(LIDS1103.QUERY_INTERFACE_LIST, queryMap))) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("记录已存在！");
                //返回状态
                outInfo.set("result","1");
                outInfo.set("result_desc","接收成功");
                return outInfo;
            }

            /*//传入抓取信号时，判断相同行车最后一条终到坐标位置与时间差异,若差异小于10秒，则不处理
            if ("1".equals(actionType)) {
                queryMap.clear();
                queryMap.put("segNo", segNo);
                queryMap.put("craneId", craneId);
                queryMap.put("actionType", "2");//查询释放
                queryMap.put("abnormalFlag", "1");//异常标记,时间倒序取一条
                List<LIDS1103> lastReleaseRecordList = dao.query(LIDS1103.GET_LAST_RELEASE_RECORD_BY_CRANE_ID, queryMap);
                if (CollectionUtils.isNotEmpty(lastReleaseRecordList)) {
                    //最新一条释放记录
                    LIDS1103 lastReleaseRecord = lastReleaseRecordList.get(0);
                    String lastUpLoadTime = lastReleaseRecord.getUploadTime();

                    //校验时间差值
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                        Date lastTime = sdf.parse(lastUpLoadTime);
                        Date currentTime = sdf.parse(uploadTime);

                        //计算时间差（毫秒）
                        long timeDifference = Math.abs(currentTime.getTime() - lastTime.getTime());
                        //转换为秒
                        long secondsDifference = timeDifference / 1000;

                        //如果时间差小于10秒，则不处理
                        if (secondsDifference <= 10) {
                            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                            outInfo.setMsg(grabSysId + "与上次操作时间差小于10秒，不处理！");
                            //返回状态
                            outInfo.set("result", "1");
                            outInfo.set("result_desc", "接收成功");
                            return outInfo;
                        }
                    } catch (Exception e) {
                        //时间格式解析异常，继续处理
                        log("时间格式解析异常：" + e.getMessage());
                        System.out.println("时间格式解析异常：" + e.getMessage());
                        paramMap.put("remark", paramMap.get("remark") + "时间格式解析异常(lastUpLoadTime:" + lastUpLoadTime + ";thisUpLoadTime:" + uploadTime + ");");
                    }
                }
            }*/

            //行车作业实绩
            Map craneOrderMap = new HashMap();
            craneOrderMap.put("unitCode", segNo);
            craneOrderMap.put("segNo", segNo);
            craneOrderMap.put("craneId", craneId);
            craneOrderMap.put("craneName", craneName);
            craneOrderMap.put("actionType", actionType);//状态
            craneOrderMap.put("grabSysId", grabSysId);//抓取流水号
            craneOrderMap.put("releaseSysId", releaseSysId);//释放流水号
            craneOrderMap.put("x_value", x_value);//X轴值
            craneOrderMap.put("y_value", y_value);//Y轴值
            craneOrderMap.put("z_value", z_value);//Z轴值
            craneOrderMap.put("weight", weight);//吊装重量
            craneOrderMap.put("recCreator", "system");// 创建人工号
            craneOrderMap.put("recCreatorName", "system");// 创建人姓名
            craneOrderMap.put("recCreateTime", DateUtil.curDateTimeStr14());// 创建时间
            craneOrderMap.put("recRevisor", "system");// 修改人工号
            craneOrderMap.put("recRevisorName", "system");// 修改人姓名
            craneOrderMap.put("recReviseTime", DateUtil.curDateTimeStr14());// 修改时间

            try {
                //主动获取UWB行车当前位置方法插入表中
                HashMap<Object, Object> objectHashMap = new HashMap<>(paramMap);
                // 获取指定 card_id 的最新记录
                EiInfo prtInfo = new EiInfo();
                prtInfo.set("paramMap", paramMap);
                prtInfo = getUwbRealTimeLocation(prtInfo);
                if (prtInfo.getStatus() > EiConstant.STATUS_FAILURE) {
                    Map<String, Object> latestRecord = prtInfo.getMap("resultMap");
                    if (MapUtils.isNotEmpty(latestRecord)) {
                        objectHashMap.put("x_value", NumberUtils.toBigDecimal(latestRecord.get("x_value")));
                        objectHashMap.put("y_value", NumberUtils.toBigDecimal(latestRecord.get("y_value")));
                        objectHashMap.put("z_value", NumberUtils.toBigDecimal(latestRecord.get("z_value")));
                        objectHashMap.put("sendTime", MapUtils.getString(latestRecord, "sendTime"));
                    } else {
                        objectHashMap.put("x_value", 0);
                        objectHashMap.put("y_value", 0);
                        objectHashMap.put("z_value", 0);
                        objectHashMap.put("sendTime", DateUtil.curDateTimeStr14());
                    }
                } else {
                    objectHashMap.put("x_value", 0);
                    objectHashMap.put("y_value", 0);
                    objectHashMap.put("z_value", 0);
                    objectHashMap.put("sendTime", DateUtil.curDateTimeStr14());
                }
                dao.insert(LIDS1104.INSERT, objectHashMap);
            } catch (Exception ex) {
                //打印日志
                log("主动获取UWB实时定位MQTT失败：" + ex.getMessage());
                System.out.println("主动获取UWB实时定位MQTT失败：" + ex.getMessage());
            }

            //调用处理逻辑
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDS1201");
            sendInfo.set("paramMap", craneOrderMap);
            //生成行车作业实绩(抓取)
            if ("1".equals(actionType)) {
                sendInfo.set(EiConstant.methodName, "receiveUWBAndGenerate");
            } else if ("2".equals(actionType)) {
                //更新行车作业实绩(释放)
                sendInfo.set(EiConstant.methodName, "receiveUWBAndUpdate");
            }
            //以新事务管理调用方法，被调用方法内部报错不影响当前方法
            sendInfo = XLocalManager.callNewTx(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_SUCCESS) {
                paramMap.put("status", "00");
                paramMap.put("remark", paramMap.get("remark") + "->" + sendInfo.getMsg());
            }
            //插入接口表
            dao.insert(LIDS1103.INSERT, paramMap);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
            //返回状态
            outInfo.set("result","1");
            outInfo.set("result_desc","接收成功");
        } catch (Exception ex) {
            outInfo.set("result", "0");
            outInfo.set("result_desc", "接收失败：" + ex.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        } finally {
            //打印日志
            log("调用ServiceLIDSInterfaces.uploadUwbInfo返回参数：" + outInfo);
            System.out.println("调用ServiceLIDSInterfaces.uploadUwbInfo返回参数：" + outInfo);
        }
        return outInfo;
    }

    /**
     * 通过传入芯片ID获取UWB最新位置
     */
    public EiInfo getUwbRealTimeLocation(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //获取查询条件
            Map paramMap = inInfo.getMap("paramMap");

            //芯片ID
            String optKey = MapUtils.getString(paramMap, "cardId", "");
            if (StringUtils.isBlank(optKey)) {
                optKey = StringUtils.defaultString((String) paramMap.get("craneId"), "");
                if(StringUtils.isBlank(optKey)){
                    throw new RuntimeException("传入字段【芯片ID】或【行车编号】为空！");
                }
            }

            BigDecimal card_x = new BigDecimal("0");
            BigDecimal card_y = new BigDecimal("0");
            BigDecimal card_z = new BigDecimal("0");
            String sendTime = "";

            // 获取指定 card_id 的最新定位记录
            Map<String, Object> latestRecord = (Map<String, Object>) redisUtil.hGet("uwbLocRecord:latest", optKey);
            //返回结果集
            Map resultMap = new HashMap();
            if (MapUtils.isNotEmpty(latestRecord)) {
                //打印日志
                log("卡号/行车编号" + optKey + "主动获取UWB实时定位MQTT：" + latestRecord);
                System.out.println("卡号" + optKey + "主动获取UWB实时定位MQTT：" + latestRecord);
                card_x = NumberUtils.toBigDecimal(MapUtils.getString(latestRecord, "x_value"));
                card_y = NumberUtils.toBigDecimal(MapUtils.getString(latestRecord, "y_value"));
                card_z = NumberUtils.toBigDecimal(MapUtils.getString(latestRecord, "z_value"));
                sendTime = MapUtils.getString(latestRecord, "ontain_time");

                //ontain_time为空，按四项之前的数据来(人员)
                if(StringUtils.isBlank(sendTime)){
                    //单位是米，需要转换成厘米
                    card_x = NumberUtils.toBigDecimal(MapUtils.getString(latestRecord, "card_x")).multiply(BigDecimal.valueOf(100));
                    card_y = NumberUtils.toBigDecimal(MapUtils.getString(latestRecord, "card_y")).multiply(BigDecimal.valueOf(100));
                    card_z = NumberUtils.toBigDecimal(MapUtils.getString(latestRecord, "card_z")).multiply(BigDecimal.valueOf(100));
                    sendTime = MapUtils.getString(latestRecord, "send_time");
                    //时间戳转为具体时间
                    long timestamp = Long.parseLong(sendTime); // 假设 sendTime 是毫秒级时间戳
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                    sendTime = sdf.format(new Date(timestamp));
                }
            } else {
                //打印日志
                log("卡号" + optKey + "未获取到UWB实时定位MQTT！");
                System.out.println("卡号" + optKey + "未获取到UWB实时定位MQTT！");
            }

            String segNo = MapUtils.getString(paramMap, "segNo", "");
            //查询行车所属厂区厂房，XYZ轴坐标，查询所属区域
            HashMap queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("craneId", optKey);

            //查询行车所属区域
            List<LIDS0301> lids0301List = dao.query(LIDS0301.QUERY_BY_CRANE_ID, queryMap);
            if (CollectionUtils.isNotEmpty(lids0301List)) {
                LIDS0301 lids0301 = lids0301List.get(0);
                //根据XY轴,跨区查询库位附属信息
                queryMap = new HashMap();
                queryMap.put("XPosition", card_x);
                queryMap.put("YPosition", card_y);
                queryMap.put("crossArea", lids0301.getCrossArea());
                List<LIDS0601> lids0601s = dao.query(LIDS0601.QUERY_LOCATION_ID, queryMap);
                if (CollectionUtils.isNotEmpty(lids0601s)) {
                    LIDS0601 lids0601 = lids0601s.get(0);
                    resultMap.put("locationId", lids0601.getLocationId());
                    resultMap.put("locationName", lids0601.getLocationName());
                }
            }


            //返回坐标单位是厘米
            resultMap.put("x_value", card_x.setScale(2, RoundingMode.DOWN));
            resultMap.put("y_value", card_y.setScale(2, RoundingMode.DOWN));
            resultMap.put("z_value", card_z.setScale(2, RoundingMode.DOWN));
            resultMap.put("sendTime", sendTime);

            outInfo.set("resultMap", resultMap);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("获取UWB实时定位失败：" + ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 物流精细化出库自动生成行车作业清单
     */
    public EiInfo autoGenerateCraneOrder(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {

            //系统账套
            String segNo = inInfo.getString("segNo");
            if(StringUtils.isBlank(segNo)){
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            //配车单号
            String allocateVehicleNo = inInfo.getString("allocateVehicleNo");
            if(StringUtils.isBlank(allocateVehicleNo)){
                throw new RuntimeException("传入字段【配车单号】为空！");
            }
            //装卸点编码
            String loadingPointNo = inInfo.getString("loadingPointNo");
            if(StringUtils.isBlank(loadingPointNo)){
                throw new RuntimeException("传入字段【装卸点编码】为空！");
            }

            //根据配车单号查询配车单维护子表(捆包清单)
            Map mainMap = new HashMap();
            mainMap.put("segNo", segNo);
            mainMap.put("allocateVehicleNo", allocateVehicleNo);
            mainMap.put("outPackFlag", "0");
            mainMap.put("voucherNumFlag", "0");
            List<LIRL0503> lirl0503List = dao.query(LIRL0503.QUERY, mainMap);

            if (CollectionUtils.isEmpty(lirl0503List)) {
//                throw new RuntimeException("传入提单捆包明细为空！");
            }

            //同一车辆登记一个批次
            String batchId = SequenceGenerator.getNextSequence("TLIDS_SEQ1103",  new String[]{segNo, ""});
            //生成行车作业清单详细
            for (LIRL0503 lirl0503 : lirl0503List) {
                String packId = lirl0503.getPackId();
                String warehouseCode = lirl0503.getWarehouseCode();
                //查询捆包库存表信息
                Map queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("packId", packId);
                queryMap.put("warehouseCode", warehouseCode);
                List<LIDS0901> lids0901s = dao.query(LIDS0901.QUERY_PACK_MESSAGE, queryMap);
                //一厂生成行车作业清单，不做强关联，若生成失败，仍然可以启动叫号
                if (lids0901s.size() != 1) {
                    continue;
//                    throw new RuntimeException("传入参数：" + queryMap + ",返回：" + lids0901s.size() + "条捆包库存信息！");
                }

                //得到捆包信息，//TODO,库存表里的板卷标记？
                LIDS0901 lids0901 = lids0901s.get(0);

                //判断捆包库位所属厂区是否为一厂
                queryMap.put("locationId",lids0901.getAreaCode());
                queryMap.put("locationName",lids0901.getAreaName());
                int count = super.count(LIDS0601.COUNT_LOCATION_ID, queryMap);
                if(count != 1){
                    continue;
                }

                //查询是否有上层捆包
                List<LIDS0901> thisLayerPackList = new ArrayList<>();
                /**如果是卷，根据X轴的关系，确定上层是否有其他捆包，如果有，则根据捆包生成行车作业清单，一个捆包一个行车作业清单
                 * 查询库存表
                 */
                if ("1".equals(lids0901.getActionFlag())) {
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", packId);
                    queryMap.put("XPosition", lids0901.getX_position());
                    queryMap.put("YPosition", lids0901.getY_position());
                    queryMap.put("warehouseCode", warehouseCode);

                    //查询是否有上层捆包(根据捆包号查询库位附属信息子表相邻上层捆包)
                    thisLayerPackList = dao.query(LIDS0901.QUERY_RELATED_PACKS_BY_ADJACENT_IDS, queryMap);
                } else if ("0".equals(lids0901.getActionFlag())) {
                    /**如果是板，根据XY轴分组，确定上层是否有其他捆包，生成行车作业清单要考虑板的分组
                     * (传入XY坐标，查询所属这个区间)
                     * 查询库存表
                     */
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("XPosition", lids0901.getX_position());
                    queryMap.put("YPosition", lids0901.getY_position());
                    queryMap.put("warehouseCode", warehouseCode);
                    //查询是否有上层捆包(XY坐标内的所有捆包)
                    thisLayerPackList = dao.query(LIDS0901.QUERY_PACKS_BY_XY_COORDINATES, queryMap);
                }
                //存在上层捆包，按上层捆包信息生成行车作业清单(倒库)
                int serialNumber = 0;
                if (CollectionUtils.isNotEmpty(thisLayerPackList)) {
                    outInfo = generateCraneOrder(thisLayerPackList, "50", allocateVehicleNo,loadingPointNo,batchId, serialNumber);
                    if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        throw new RuntimeException(outInfo.getMsg());
                    }
                }

                //生成当前捆包的行车作业清单(成品发货)
                outInfo = generateCraneOrder(lids0901s, "20", allocateVehicleNo,loadingPointNo,batchId, serialNumber);
                if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    throw new RuntimeException(outInfo.getMsg());
                }


            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 传入List,生成行车作业清单
     * @param packList, 传入的捆包集合
     * @param listSource, 清单来源
     * @param voucherNum, 配车单号
     * @param batchNumber, 批次号
     * @param serialNumber, 顺序号
     * @return
     */
    public EiInfo generateCraneOrder(List<LIDS0901> packList,String listSource,String voucherNum,String loadingPointNo,String batchNumber,int serialNumber) {
        EiInfo outInfo = new EiInfo();
        try {
            //行车作业清单主项
            List<LIDS1101> mCraneOrderList = new ArrayList<>();
            //行车作业清单子项
            List<LIDS1102> dCraneOrderList = new ArrayList<>();
            //行车作业清单主项
            String craneOrderId ="";
            //循环所有捆包，生成行车作业清单
            for (LIDS0901 lids0901 : packList) {
                //根据当前库位所属跨区匹配对应行车编号(直接根据库位对应跨区编码匹配对应行车，并拼接返回所有行车编号)
                List<HashMap> cranes = dao.query(LIDS0301.QUERY_CRANE_IDS_BY_CROSS_AREA, lids0901.toMap());
                //拿到拼接后的行车信息
                String craneIds = StringUtils.defaultString((String) cranes.get(0).get("craneIds"), "");
                String craneNames = StringUtils.defaultString((String) cranes.get(0).get("craneNames"), "");
                if (StringUtils.isBlank(craneIds) || StringUtils.isBlank(craneNames)) {
                    throw new RuntimeException("库位代码：" + lids0901.getAreaCode() + "库位名称：" + lids0901.getAreaName() + "，所属跨区未匹配到行车编码！");
                }

                //判断当前捆包是板还是卷(卷：一个行车作业主项对应一个行车作业子项；板：一个行车多页主项对应多条行车作业子项)
                if (StringUtils.isBlank(craneOrderId)) {
                    craneOrderId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", new String[]{lids0901.getSegNo(), ""});
                } else {
                    if ("1".equals(lids0901.getActionFlag())) {
                        craneOrderId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", new String[]{lids0901.getSegNo(), ""});
                    }else if("0".equals(lids0901.getActionFlag())){
                        //板不重新生成作业单号
                    }
                }

                //行车作业清单主项
                LIDS1101 lids1101 = new LIDS1101();
                //业务单元代码
                lids1101.setUnitCode(lids0901.getUnitCode());
                //系统账套
                lids1101.setSegNo(lids0901.getSegNo());
                //行车编号
                lids1101.setCraneId(craneIds);
                //行车名称
                lids1101.setCraneName(craneNames);
                //行车作业清单主项号
                lids1101.setCraneOrderId(craneOrderId);
                //清单来源
                lids1101.setListSource(listSource);
                //依据凭单号
                lids1101.setVoucherNum(voucherNum);
                //批次号
                lids1101.setBatchNumber(batchNumber);
                //顺序号
                lids1101.setSerialNumber(String.valueOf(serialNumber));
                serialNumber++;
                //机组代码
                lids1101.setMachineCode("");
                //机组名称
                lids1101.setMachineName("");
                //拆包区编码
                lids1101.setUnpackAreaId("");
                //拆包区名称
                lids1101.setUnpackAreaName("");
                //模具名称
                lids1101.setMouldId("");
                //模具名称
                lids1101.setMouldName("");
                //作业开始时间
                lids1101.setStartTime("");
                //作业结束时间
                lids1101.setEndTime("");
                //作业时间
                lids1101.setJobTime("");
                //起始区域类型
                lids1101.setStartAreaType("10");
                //起始区域代码
                lids1101.setStartAreaCode(lids0901.getAreaCode());
                //起始区域名称
                lids1101.setStartAreaName(lids0901.getAreaName());
                //不一样终到区域是库区还是发货通道
                if("50".equals(listSource)){
                    //终到区域类型
                    lids1101.setEndAreaType("10");
                    //终到区域代码
                    lids1101.setEndAreaCode("");
                    //终到区域名称
                    lids1101.setEndAreaName("");
                    //调用库位推荐方法，获取推荐库位代码和库位名称
                    if ("1".equals(lids0901.getActionFlag())) {
                        //终到区域调用库位推荐获取
                        HashMap messageBody = new HashMap();
                        messageBody.put("mark", "1");
                        messageBody.put("segNo", lids0901.getSegNo());
                        messageBody.put("packId", lids0901.getPackId());
                        EiInfo sendInfo = new EiInfo();
                        sendInfo.set("messageBody", messageBody);
                        sendInfo.set(EiConstant.serviceName, "LIDS0606");
                        sendInfo.set(EiConstant.methodName, "recommendedStorageLocation");
                        sendInfo = XLocalManager.callNewTx(sendInfo);
                        if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                            log("ServiceLIDSInterfaces.generateCraneOrder调用库位推荐传入参数：" + messageBody + "返回的参数：" + sendInfo.toJSONString());
                            System.out.println("ServiceLIDSInterfaces.generateCraneOrder调用库位推荐传入参数：" + messageBody + "返回的参数：" + sendInfo.toJSONString());
                        }

                        //库位代码与库位名称
                        String locationId = sendInfo.getString("locationId");
                        String locationName = sendInfo.getString("locationName");
                        if (StringUtils.isNotBlank(locationId)) {
                            lids1101.setEndAreaCode(locationId);
                        }
                        if (StringUtils.isNotBlank(locationName)) {
                            lids1101.setEndAreaCode(locationName);
                        }
                    }
                }else{
                    //根据装卸点编码查询装卸通道
                    Map queryMap = new HashMap();
                    queryMap.put("segNo",lids0901.getSegNo());
                    queryMap.put("handPointId",loadingPointNo);
                    List<LIRL0304> lirl0304List = dao.query(LIRL0304.QUERY_HAND_POINT_INFO, queryMap);

                    //装货通道编码
                    String loadingChannelNo = "";
                    //装货通道名称
                    String loadingChannelName = "";
                    if(CollectionUtils.isNotEmpty(lirl0304List)){
                        loadingChannelNo = lirl0304List.get(0).getLoadingChannelId();
                        loadingChannelName = lirl0304List.get(0).getLoadingChannelName();
                    }
                    //终到区域类型
                    lids1101.setEndAreaType("40");
                    //终到区域代码
                    lids1101.setEndAreaCode(loadingChannelNo);
                    //终到区域名称
                    lids1101.setEndAreaName(loadingChannelName);
                }
                //状态
                lids1101.setStatus("10");
                //创建人工号
                lids1101.setRecCreator("system");
                //创建人名称
                lids1101.setRecCreatorName("system");
                //创建时间
                lids1101.setRecCreateTime(DateUtil.curDateTimeStr14());
                //修改人工号
                lids1101.setRecRevisor("system");
                //修改人名称
                lids1101.setRecRevisorName("system");
                //修改时间
                lids1101.setRecReviseTime(DateUtil.curDateTimeStr14());
                //uuid
                lids1101.setUuid(UUIDUtils.getUUID());
                mCraneOrderList.add(lids1101);

                //行车作业清单子项
                LIDS1102 lids1102 = new LIDS1102();
                //业务单元代码
                lids1102.setUnitCode(lids0901.getUnitCode());
                //系统账套
                lids1102.setSegNo(lids0901.getSegNo());
                //行车作业清单主项号
                lids1102.setCraneOrderId(lids1101.getCraneOrderId());
                //行车作业清单子项号
                lids1102.setCraneOrderSubId(SequenceGenerator.getNextSequence("TLIDS_SEQ1102", new String[]{lids1101.getCraneOrderId(), ""}));
                //捆包号
                lids1102.setPackId(lids0901.getPackId());
                //标签号
                lids1102.setLabelId(lids0901.getLabelId());
                //净重
                lids1102.setNetWeight(lids0901.getNetWeight());
                //数量
                lids1102.setQuantity(lids0901.getQuantity());
                //状态
                lids1102.setStatus("10");
                //记录创建人
                lids1102.setRecCreator("system");
                //记录创建人姓名
                lids1102.setRecCreatorName("system");
                //记录创建时间
                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                //记录修改人
                lids1102.setRecRevisor("system");
                //记录修改人姓名
                lids1102.setRecRevisorName("system");
                //记录修改时间
                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                //uuid
                lids1102.setUuid(UUIDUtils.getUUID());
                dCraneOrderList.add(lids1102);

            }
            log("生成行车作业清单主项：" + mCraneOrderList + ",生成行车作业清单子项：" + dCraneOrderList);
            System.out.println("生成行车作业清单主项：" + mCraneOrderList + ",生成行车作业清单子项：" + dCraneOrderList);

            int count = dao.insertBatch(LIDS1101.INSERT, mCraneOrderList);
            if(count != mCraneOrderList.size()){
                throw new PlatException("生成行车作业清单主项失败！");
            }
            count = dao.insertBatch(LIDS1102.INSERT, dCraneOrderList);
            if(count != dCraneOrderList.size()){
                throw new PlatException("生成行车作业清单子项失败！");
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * IMOM接收生产传入并包信息
     * ServiceId:S_LI_DS_0004
     */
     public EiInfo receiveUnitedPackIdInfo(EiInfo inInfo) {
         EiInfo outInfo = new EiInfo();
         try {
             Map attr = inInfo.getAttr();
             List<HashMap> queryList =(List<HashMap>) attr.get("queryList");
             String unitedPackId =(String) attr.get("unitedPackId");
             String flag =(String) attr.get("flag");
             log("接收生产传入并包信息:"+"并包号:" +unitedPackId+",所传捆包:" +queryList);
             System.out.println("接收生产传入并包信息:"+"并包号:" +unitedPackId+",所传捆包:" +queryList);
             if("incoming".equals(flag)){
                 if (CollectionUtils.isNotEmpty(queryList)){
                     for (HashMap packMap : queryList) {
                         packMap.put("status","10");
                         packMap.put("delFlag","0");
                         List<LIDS0901> packList = this.dao.query(LIDS0901.QUERY_PACK_MESSAGE, packMap);
                         if (CollectionUtils.isNotEmpty(packList)){
                             packMap.put("unitedPackId",unitedPackId);
                             packMap.put("recRevisor", "system");
                             packMap.put("recRevisorName", "system");
                             packMap.put("recReviseTime", DateUtils.curDateTimeStr14());
                             this.dao.update(LIDS0901.UPDATE_UNITED_PACK_ID,packMap);
                         }
                     }
                 }
             }else if ("revoke".equals(flag)){
                 if (CollectionUtils.isNotEmpty(queryList)){
                     for (HashMap packMap : queryList) {
                         packMap.put("status","10");
                         packMap.put("delFlag","0");
                         List<LIDS0901> packList = this.dao.query(LIDS0901.QUERY_PACK_MESSAGE, packMap);
                         if (CollectionUtils.isNotEmpty(packList)){
                             packMap.put("unitedPackId","");
                             packMap.put("recRevisor", "system");
                             packMap.put("recRevisorName", "system");
                             packMap.put("recReviseTime", DateUtils.curDateTimeStr14());
                             this.dao.update(LIDS0901.UPDATE_UNITED_PACK_ID,packMap);
                         }
                     }
                 }
             }
             outInfo.setStatus(EiConstant.STATUS_DEFAULT);
             outInfo.setMsg("接收成功");
         }catch(Exception ex){
             outInfo.setStatus(EiConstant.STATUS_DEFAULT);
             outInfo.setMsg("接收失败");
         }
         return outInfo;
    }
    /**
     * 大屏看板查询机组信息
     * ServiceId:S_LI_DS_0028
     */
    public EiInfo queryMachineCodeInfo(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try{
            String segNo = String.valueOf(inInfo.get("segNo"));
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            List<Object> machineList = new ArrayList<>();
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("machineCode", "");
            hashMap.put("machineName", "全部");
            machineList.add(hashMap);
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("segNo",segNo);
            //零件类型
            String partType = String.valueOf(inInfo.get("partType"));
            List<String> warehouseList = new ArrayList<>();
            if (StringUtils.isNotBlank(partType)){
                if ("板材".equals(partType)){
                    queryMap.put("processCategory","'KN','TW','CM','WE','BL','CL','SL'");
                    warehouseList = Arrays.asList("A18113106", "A18113104", "A18113103", "A18113100", "494668006");
                }else if ("零部件".equals(partType)){
                    queryMap.put("processCategory","'PF','SB','TB','HF','HS','LC'");
                    warehouseList = Arrays.asList("A18113105", "494668005");
                }
            }
            List<LIDS0701> machineCodeList = this.dao.query(LIDS0701.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(machineCodeList)){
                for (LIDS0701 lids0701 : machineCodeList) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("machineCode", lids0701.getMachineCode());
                    item.put("machineName", lids0701.getMachineName());
                    machineList.add(item);
                }
            }
            outInfo.set("machineList",machineList);
            outInfo.set("warehouseList",warehouseList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("查询成功");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败");
        }
        return outInfo;
    }

    /**
     * 设置 库位变更人信息
     */
    public void setLocationChanger(Map<String, Object> map, String craneResultId, String operatorCode, String operatorName,
                                   String operateTime, String putoutLocationId, String putoutLocationName) {
        LIDS0904 lids0904 = new LIDS0904();
        lids0904.setSegNo(MapUtils.getString(map, "segNo",""));
        lids0904.setUnitCode(MapUtils.getString(map, "segNo",""));
        lids0904.setPackId(MapUtils.getString(map, "packId",""));
        lids0904.setCraneResultId(craneResultId);
        lids0904.setNetWeight(NumberUtils.toBigDecimal(MapUtils.getString(map, "netWeight"),BigDecimal.ZERO));
        lids0904.setQuantity(NumberUtils.toBigDecimal(MapUtils.getString(map, "quantity"),BigDecimal.ZERO));
        lids0904.setWarehouseCode(MapUtils.getString(map, "warehouseCode",""));
        lids0904.setWarehouseName(MapUtils.getString(map, "warehouseName",""));
        lids0904.setLocationId(MapUtils.getString(map, "locationId",""));
        lids0904.setLocationName(MapUtils.getString(map, "locationName",""));
        lids0904.setUuid(UUIDUtils.getUUID());
        // 变更时间
        lids0904.setRecCreateTime(DateUtils.curDateTimeStr14());
        // 变更人代码
        lids0904.setRecCreator(operatorCode);
        // 变更人名称
        lids0904.setRecCreatorName(operatorName);
        lids0904.setRecRevisor(operatorCode);
        lids0904.setRecReviseTime(operateTime);
        lids0904.setRecRevisorName(operatorName);
        //行吊自动触发的倒库，使用行车实绩单号查询起始区域
        if (StringUtils.isNotBlank(craneResultId)) {
            Map queryMap = new HashMap();
            queryMap.put("segNo", lids0904.getSegNo());
            queryMap.put("craneResultId", craneResultId);
            List<LIDS1201> lids1201List = dao.query(LIDS1201.QUERY, map);
            if (CollectionUtils.isNotEmpty(lids1201List)) {
                lids0904.setPutoutLocationId(lids1201List.get(0).getStartAreaCode());
                lids0904.setPutoutLocationName(lids1201List.get(0).getStartAreaName());
            }
            //UWB
            lids0904.setDataSource("01");
            RecordUtils.setCreatorBeanSys(lids0904);
        } else {
            //PDA倒库
            lids0904.setDataSource("02");
            lids0904.setPutoutLocationId(putoutLocationId);
            lids0904.setPutoutLocationName(putoutLocationName);
        }
        dao.insert(LIDS0904.INSERT, lids0904);
    }
}
