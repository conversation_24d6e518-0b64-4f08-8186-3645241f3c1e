package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.ds.domain.LIDS0101;
import com.baosight.imom.li.ds.domain.LIDS0301;
import com.baosight.imom.li.ds.domain.LIDS0801;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模具库存管理
 */
public class ServiceLIDS0801 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0801().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0801.QUERY, new LIDS0801());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 接收PDA扫描的数据生成模具库存
     * 传入参数 mouldId 模具ID posDirCode 层数标记 segNo 系统账套
     * @param inInfo
     * @return
     */
    public EiInfo creatMouldInventory(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map attr = inInfo.getAttr();
            String mouldId = MapUtils.getString(attr, "mouldId", "");
            if (StringUtils.isBlank(mouldId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少模具信息，模具库存生成失败");
            }
            String segNo = MapUtils.getString(attr, "segNo", "");
            if (StringUtils.isBlank(segNo)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少系统账套信息，模具库存生成失败");
            }
            String craneId = MapUtils.getString(attr, "craneId", "");
            if (StringUtils.isBlank(craneId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少行车编号信息，模具库存生成失败");
            }
            String craneName = MapUtils.getString(attr, "craneName", "");
            if (StringUtils.isBlank(craneName)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少行车名称信息，模具库存生成失败");
            }
            //层数标记不传默认为最下层1
            String posDirCode = MapUtils.getString(attr, "posDirCode", "1");
            String mouldName = MapUtils.getString(attr, "mouldName", "");
            BigDecimal mouldSpecWeightSum = MapUtils.getBigDecimal(attr, "mouldSpecWeightSum", new BigDecimal(0));
            LIDS0801 lids0801 = new LIDS0801();
            lids0801.setSegNo(segNo);
            lids0801.setUnitCode(segNo);
            //模具ID
            lids0801.setMouldId(mouldId);
            //模具名称
            lids0801.setMouldName(mouldName);
            //层数标记
            lids0801.setPosDirCode(posDirCode);
            //模具重量
            lids0801.setMouldSpecWeightSum(mouldSpecWeightSum);
            //区域类型
            lids0801.setAreaType("50");
            String areaCode ="";
            String areaName ="";
            if ("其他".equals(craneName)){
                areaName= MapUtils.getString(attr, "areaName", "");
                lids0801.setX_position("0");
                //Y轴坐标
                lids0801.setY_position("0");
                //Z轴坐标
                lids0801.setZ_position("0");
            }else {
                //调用实时获取UWB方法获取当前的xyz轴坐标，根据xyz轴坐标转换当前的区域代码和区域名称
                EiInfo eiInfo = new EiInfo();
                HashMap paramMap = new HashMap();
                paramMap.put("segNo",segNo);
                paramMap.put("craneId",craneId);
                paramMap.put("craneName",craneName);
                /*//依据行车编号获取卡号
                paramMap.put("cardId", MapUtils.getString(MesConstant.CARD_MAP, craneId));*/
                eiInfo.set("paramMap",paramMap);
                eiInfo.set(EiConstant.serviceName, "LIDSInterfaces");
                eiInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
                outInfo = XLocalManager.callNoTx(eiInfo);
                if (outInfo.getStatus() == EiConstant.STATUS_DEFAULT){
                    Map resultMap =(Map) outInfo.get("resultMap");
                    //X轴坐标
                    lids0801.setX_position(String.valueOf(resultMap.get("x_value")));
                    //Y轴坐标
                    lids0801.setY_position(String.valueOf(resultMap.get("y_value")));
                    //Z轴坐标
                    lids0801.setZ_position(String.valueOf(resultMap.get("z_value")));
                    HashMap hashMap = new HashMap();
                    hashMap.put("segNo", segNo);
                    hashMap.put("factoryArea", MapUtils.getString(attr, "factoryArea", ""));
                    hashMap.put("factoryBuilding", MapUtils.getString(attr, "factoryBuilding", ""));
                    hashMap.put("XPosition", resultMap.get("x_value"));
                    hashMap.put("YPosition", resultMap.get("y_value"));
                    //根据传入的X和Y轴查询区域
                    List<LIDS0101> areaList = dao.query(LIDS0101.QUERY_AREA, hashMap);
                    if (CollectionUtils.isNotEmpty(areaList)){
                        areaCode = areaList.get(0).getAreaCode();
                        areaName = areaList.get(0).getAreaName();
                        lids0801.setAreaType(areaList.get(0).getAreaType());
                    }
                }
            }
            //区域代码
            lids0801.setAreaCode(areaCode);
            //区域名称
            lids0801.setAreaName(areaName);
            //仓库代码
            lids0801.setWarehouseCode(MapUtils.getString(attr, "warehouseCode", ""));
            //仓库名称
            lids0801.setWarehouseName(MapUtils.getString(attr, "warehouseName", ""));
            lids0801.setStatus("10");
            //记录创建人
            lids0801.setRecCreator("system");
            //记录创建人姓名
            lids0801.setRecCreatorName("system");
            //记录创建时间
            lids0801.setRecCreateTime(DateUtils.curDateTimeStr14());
            //记录修改人
            lids0801.setRecRevisor("system");
            //记录修改人姓名
            lids0801.setRecRevisorName("system");
            //记录修改时间
            lids0801.setRecReviseTime(DateUtils.curDateTimeStr14());
            //归档标记
            lids0801.setArchiveFlag("");
            //租户
            lids0801.setTenantUser("");
            //删除标记
            lids0801.setDelFlag(0);
            //ID
            lids0801.setUuid(UUIDUtils.getUUID());
            this.dao.insert(LIDS0801.INSERT,lids0801);
            outInfo.setMsg("入库成功");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
    
}
