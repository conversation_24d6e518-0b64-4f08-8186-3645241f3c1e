package com.baosight.imom.vi.pm.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.ds.domain.*;
import com.baosight.imom.vi.pm.domain.VIPM0007;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.imom.vi.pm.domain.VIPM0009;
import com.baosight.imom.vi.pm.domain.VIPM0601;
import com.baosight.imom.xt.ss.domain.XTSS04;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ServiceVIPMInterfaces extends ServiceBase {

    /**
     * 获取当前时间方法.
     * @return format
     */
    public static String getFormat() {
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = dateFormat.format(l);
        return format;
    }

    /**
     * 接收工单下发信息接口
     * Service:S_VI_PM_0001
     */
    public EiInfo ReceiveWorkOrderInformatio(EiInfo inInfo) {
        //需求单产出信息
        List<HashMap> demandOutputList = new ArrayList();
        //需求单投料捆包信息
        List<HashMap> demandMaterailList = new ArrayList();
        //需求单工单信息
        List<HashMap> demandProcessList = new ArrayList();
        try {
            demandOutputList =(ArrayList) inInfo.get("demandOutputList");
            for (HashMap demandOutputMap : demandOutputList) {
                //接收之前查询表中是否有相同数据，如果有，则删除重新新增
                HashMap queryMap = new HashMap();
                queryMap.put("uuid",demandOutputMap.get("uuid"));
                List<VIPM0007> outputlist = this.dao.query(VIPM0007.QUERY,queryMap);
                if (outputlist!=null && outputlist.size()>0){
                    HashMap deleteMap = new HashMap();
                    deleteMap.put("segNo", demandOutputMap.get("segNo"));
                    deleteMap.put("uuid",demandOutputMap.get("uuid"));
                    dao.delete(VIPM0007.DELETE_ONE,deleteMap);
                }
                dao.insert(VIPM0007.INSERT,demandOutputMap);
            }
            demandMaterailList =(ArrayList) inInfo.get("demandMaterailList");
            for (HashMap demandMaterailMap : demandMaterailList) {
                //接收之前查询表中是否有相同数据，如果有，则删除重新新增
                HashMap queryMap = new HashMap();
                queryMap.put("segNo",demandMaterailMap.get("segNo"));
                queryMap.put("packId",demandMaterailMap.get("packId"));
                queryMap.put("matInnerId",demandMaterailMap.get("matInnerId"));
                List<VIPM0008> materaillist = this.dao.query(VIPM0008.QUERY,queryMap);
                if (materaillist!=null && materaillist.size()>0){
                    HashMap deleteMap = new HashMap();
                    deleteMap.put("segNo", demandMaterailMap.get("segNo"));
                    deleteMap.put("packId",demandMaterailMap.get("packId"));
                    deleteMap.put("matInnerId",demandMaterailMap.get("matInnerId"));
                    dao.delete(VIPM0008.DELETE,deleteMap);
                }
                dao.insert(VIPM0008.INSERT,demandMaterailMap);
            }
            demandProcessList =(ArrayList) inInfo.get("demandProcessList");
            for (HashMap demandProcessMap : demandProcessList) {
                //接收之前查询表中是否有相同数据，如果有，则删除重新新增
                HashMap queryMap = new HashMap();
                queryMap.put("segNo", demandProcessMap.get("segNo"));
                queryMap.put("processOrderId", demandProcessMap.get("processOrderId"));
                queryMap.put("processDemandSubId", demandProcessMap.get("processDemandSubId"));
                List<VIPM0009> processlist = this.dao.query(VIPM0009.QUERY,queryMap);
                if (processlist!=null && processlist.size()>0){
                    HashMap deleteMap = new HashMap();
                    deleteMap.put("segNo", demandProcessMap.get("segNo"));
                    deleteMap.put("processOrderId", demandProcessMap.get("processOrderId"));
                    deleteMap.put("processDemandSubId", demandProcessMap.get("processDemandSubId"));
                    dao.delete(VIPM0009.DELETE,deleteMap);
                }
                dao.insert(VIPM0009.INSERT,demandProcessMap);
            }
            //信息接收后生成行车作业清单
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("demandOutputList",demandOutputList);
            eiInfo.set("demandMaterailList",demandMaterailList);
            eiInfo.set("demandProcessList",demandProcessList);
            String s = eiInfo.toJSONString();
            eiInfo = CreateCraneOrderId(eiInfo);
            if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE){
                //打印日志到elk
                log(getFormat() +"接收工单下发的数据生成行车作业清单失败，接口返回报错："+eiInfo.getMsg());
                //输出到应用日志
                System.out.println(getFormat() +"接收工单下发的数据生成行车作业清单失败，接口返回报错："+eiInfo.getMsg());
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            //打印日志到elk
            log(getFormat() + "："+"接收工单下发传入参数："+s +"\n"+"接收工单下发返回的参数："+inInfo.toJSONString());
            //输出到应用日志
            System.out.println(getFormat() + "："+"接收工单下发传入参数："+s +"\n"+"接收工单下发返回的参数："+inInfo.toJSONString());
            inInfo.setMsg("接收成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return inInfo;
        } catch (Exception ex) {
            inInfo.setMsg("接收失败，"+ex);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
    }

    /**
     * 接收退回需求单信息接口
     * ServiceName：S_VI_PM_0002
     */
    public EiInfo receiveWorkOrderReturnData(EiInfo inInfo) {
        try {
            List<HashMap> orderList = (ArrayList)inInfo.get("orderList");
            for (HashMap orderMap : orderList) {
                HashMap deleteMap = new HashMap();
                deleteMap.put("segNo", orderMap.get("segNo"));
                deleteMap.put("processOrderId", orderMap.get("processOrderId"));
                deleteMap.put("processDemandSubId", orderMap.get("processDemandSubId"));
                dao.delete(VIPM0007.DELETE,deleteMap);
                dao.delete(VIPM0008.DELETE,deleteMap);
                dao.delete(VIPM0009.DELETE,deleteMap);
            }
            inInfo.setMsg("接收成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return inInfo;
        } catch (Exception ex) {
            inInfo.setMsg("接收失败，" + ex);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
    }

    /**
     * 接收工单状态信息变更接口
     * ServiceName：S_VI_PM_0003
     */
    public EiInfo updateOrderStatus(EiInfo inInfo){
        try{
            HashMap<String ,Object> updateMap = new HashMap<>();
            Map orderMap =(Map) inInfo.get("orderMap");
            updateMap.put("segNo",orderMap.get("segNo"));
            updateMap.put("processDemandProcessStatus",orderMap.get("processDemandProcessStatus"));
            updateMap.put("processOrderId",orderMap.get("processOrderId"));
            updateMap.put("recRevisor",orderMap.get("recRevisor"));
            updateMap.put("recRevisorName",orderMap.get("recRevisorName"));
            updateMap.put("recReviseTime", DateUtil.curDateTimeStr14());
            dao.update(VIPM0009.UPDATE_STATUS,updateMap);
            inInfo.setMsg("接收成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return inInfo;
        }catch (Exception ex){
            inInfo.setMsg("接收失败，" + ex);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
    }

    /**
     * 排产下发接收失败时手动触发接收工单信息
     * ServiceName：S_VI_PM_01
     */
    public EiInfo manualTriggerReception(EiInfo inInfo){
        try{
            HashMap queryMap = new HashMap<>();
            queryMap.put("processSwitchName","IF_MANUAL_DATA_RECEPTION");
            List<XTSS04> switchList = this.dao.query(XTSS04.QUERY_LIST,queryMap);
            if (CollectionUtils.isNotEmpty(switchList)){
                for (XTSS04 xtss04 : switchList) {
                    String segNo = xtss04.getSegNo();
                    //根据上线时间获取上线时间之后的数据
                    if ("JC000000".equals(segNo)){
                        inInfo.set("recCreateTime","20241125000000");
                    }else {
                        inInfo.set("recCreateTime","20241205000000");
                    }
                    inInfo.set("segNo", segNo);
                    inInfo.set("serviceId", "S_VI_PM_9013");
                    inInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(getFormat()+"调用成功");
            return inInfo;
        }catch (Exception ex){
            inInfo.setMsg("调用失败，" + ex);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
    }
    /**
     * 待执行状态后的工单封锁捆包时同步MES的投料捆包表。
     * ServiceName：S_VI_PM_0010
     */
    public EiInfo receivePackIdBlock(EiInfo inInfo){
        //需求单投料捆包信息
        List<HashMap> demandMaterailList = new ArrayList();
        try {
            demandMaterailList =(ArrayList) inInfo.get("baleList");
            //blockadeType为79时是领料出库封锁捆包动作
            if ("79".equals(inInfo.get("blockadeFlag"))){
                for (HashMap demandMaterailMap : demandMaterailList) {
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo",demandMaterailMap.get("segNo"));
                    queryMap.put("packId",demandMaterailMap.get("packId"));
                    queryMap.put("matInnerId",demandMaterailMap.get("matInnerId"));
                    List<HashMap> outputlist = this.dao.query(VIPM0008.QUERY,queryMap);
                    if (outputlist!=null && outputlist.size()>0){
                        HashMap deleteMap = new HashMap();
                        deleteMap.put("segNo",demandMaterailMap.get("segNo"));
                        deleteMap.put("packId",demandMaterailMap.get("packId"));
                        deleteMap.put("matInnerId",demandMaterailMap.get("matInnerId"));
                        dao.delete(VIPM0008.DELETE_ONE_BALE,deleteMap);
                    }
                    dao.insert(VIPM0008.INSERT,demandMaterailMap);
                }
                //blockadeType为78时是领料回退撤销捆包动作 需要删除该捆包
            }else if ("78".equals(inInfo.get("blockadeFlag"))){
                for (HashMap demandMaterailMap : demandMaterailList) {
                    HashMap deleteMap = new HashMap();
                    deleteMap.put("segNo",demandMaterailMap.get("segNo"));
                    deleteMap.put("packId",demandMaterailMap.get("packId"));
                    deleteMap.put("matInnerId",demandMaterailMap.get("matInnerId"));
                    dao.delete(VIPM0008.DELETE_ONE_BALE,deleteMap);
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(getFormat() + "调用成功");
            return inInfo;
        } catch (Exception ex) {
            inInfo.setMsg("调用失败，" + ex);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
    }

    /**
     * 采集到上料动作时触发IMC的工单启动和领料出库
     * ServiceName：S_VI_PM_9005
     */
    public EiInfo startOrderAndOutbound(EiInfo inInfo){
        try {
            String statusMsg = "";
            EiInfo eiInfo = new EiInfo();
            String segNo = String.valueOf(inInfo.get("segNo"));
            String packId = String.valueOf(inInfo.get("packId"));
            String processOrderId = String.valueOf(inInfo.get("processOrderId"));
            if (StringUtils.isBlank(segNo)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("缺少账套信息,调用失败");
                return inInfo;
            }
            if (StringUtils.isBlank(packId)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("缺少捆包信息,调用失败");
                return inInfo;
            }
            if (StringUtils.isBlank(processOrderId)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("缺少工单信息,调用失败");
                return inInfo;
            }
            Map queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("packId",packId);
            queryMap.put("processOrderId",processOrderId);
            //查询工单状态是否为启动状态
            List<VIPM0009> outputList = this.dao.query(VIPM0009.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(outputList)){
                //工单状态是启动或结束不需要调用工单启动方法
                if (!"30".equals(outputList.get(0).getProcessDemandProcessStatus())){
                    inInfo.set("segNo", segNo);
                    inInfo.set("processOrderId", processOrderId);
                    inInfo.set("recCreator", "system");
                    inInfo.set("serviceId", "S_VI_PM_9003");
                    inInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                    if (EiConstant.STATUS_DEFAULT ==inInfo.getStatus()){
                        statusMsg=inInfo.getMsg();
                    }
                    //工单启动之后判断当前工单的模具喝下一个工单的模具是否一致 不一致则生成第一个工单从机组模具台车到模具区的行车作业清单，同时生成下个工单从模具区到机组模具台车的行车作业清单
                    String mouldId = outputList.get(0).getMouldId();
                    queryMap.clear();
                    queryMap.put("segNo",segNo);
                    queryMap.put("scheduleStartDate",outputList.get(0).getScheduleStartDate());
                    queryMap.put("machineCode",outputList.get(0).getMachineCode());
                    List<VIPM0009> nextMouldIdList = this.dao.query(VIPM0009.QUERY_NEXT_MOULD_ID,queryMap);
                    if (CollectionUtils.isNotEmpty(nextMouldIdList)){
                        if (!mouldId.equals(nextMouldIdList.get(0).getMouldId())){
                            EiInfo sendInfo = new EiInfo();
                            sendInfo.set("outputList",outputList);
                            sendInfo.set("nextMouldIdList",nextMouldIdList);
                            sendInfo.set(EiConstant.serviceName, "VIPMInterfaces");
                            sendInfo.set(EiConstant.methodName, "receiveMouldCraneOrderId");
                            //生成行车作业清单
                            sendInfo = XLocalManager.callNoTx(sendInfo);
                        }
                    }
                }
                //调用领料出库
                ArrayList packList = new ArrayList();
                HashMap packMap = new HashMap();
                queryMap.clear();
                queryMap.put("segNo",segNo);
                queryMap.put("packId",packId);
                queryMap.put("processOrderId",processOrderId);
                //领料出库前调用IMC下发同步捆包信息
                inInfo.set("packMap", queryMap);
                inInfo.set("serviceId", "S_VI_PM_1033");
                EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                //查询投料捆包信息
                List<VIPM0008> materaillist = this.dao.query(VIPM0008.QUERY,queryMap);
                if (CollectionUtils.isNotEmpty(materaillist)){
                    packMap.put("packId",materaillist.get(0).getPackId());//捆包号
                    packMap.put("matInnerId",materaillist.get(0).getMatInnerId());//材料管理号
                    packMap.put("factoryOrderNum",materaillist.get(0).getFactoryOrderNum());//钢厂订单号
                    packMap.put("warehouseCode",materaillist.get(0).getWarehouseCode());//仓库代码
                    packMap.put("chargePlat","1");//料台号默认为1
                    packList.add(packMap);
                    eiInfo.set("packList",packList);
                    eiInfo.set("segNo",segNo);
                    eiInfo.set("recCreator","system");
                    eiInfo.set("processOrderId",processOrderId);
                    eiInfo.set("processCategory",outputList.get(0).getProcessCategory());
                    eiInfo.set("MES","MES");
                    eiInfo.set("serviceId", "S_VI_PM_9005");
                    eiInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
                }else {
                    inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    inInfo.setMsg(getFormat() + "调用成功,"+statusMsg+",未查到捆包数据");
                    return inInfo;
                }
            }else {
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg(getFormat() + "调用成功,未查到工单数据或工单不在待执行状态之后");
                return inInfo;
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(getFormat() + "调用成功,工单领料返回参数："+eiInfo.getMsg());
            return inInfo;
        } catch (Exception ex) {
            inInfo.setMsg(getFormat() + "调用失败，" + ex);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
    }
    /**
     * 设备采集到生产实绩信息自动传入IMC
     * ServiceName：S_VI_PM_0042
     * 传入数据有：segNo 系统账套、packId 投料捆包号、processOrderId 生产工单号 、 partId 物料号、outPutPackId 产出捆包号 、unitedPackId 并包号
     * posDirCode 层数标记、processHour 加工工时 、netWeight 净重(kg) 、quantity 张数 、packType 捆包类型
     */
    public EiInfo addProcessResult(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try{
            log(getFormat() + "："+"设备采集生成生产实绩传入参数："+ inInfo.getAttr());
            //产出捆包成品信息
            List<Map> productPackResult = new ArrayList<>();
            //投料捆包信息
            List<Map> materialPack = new ArrayList<>();
            //工单工序信息
            Map information = new HashMap<>();
            //投料捆包List
            List<Map> distribution = new ArrayList<>();
            String teamId = "";//班组
            String workingShift = "";//班次
            String teamReportId = "";//班报序号
            String segNo =inInfo.getString("segNo");
            String processOrderId = inInfo.getString("processOrderId");
            if (StringUtils.isBlank(segNo)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少账套信息,调用失败");
                return outInfo;
            }
            if (StringUtils.isBlank(processOrderId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少工单信息,调用失败");
                return outInfo;
            }
            List<Map> packList = (ArrayList)inInfo.get("packList");
            if (CollectionUtils.isEmpty(packList)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少成品捆包信息,调用失败");
                return outInfo;
            }else {
                for (Map packMap : packList) {
                    String packId = MapUtils.getString(packMap,"packId","");
                    String areaCode = MapUtils.getString(packMap,"areaCode","");
                    String partId = MapUtils.getString(packMap,"partId","");
                    String packType = MapUtils.getString(packMap,"packType","");
                    List<Map> multiPackList =(List<Map>) packMap.get("multiPackList");
                    if (StringUtils.isBlank(packId)){
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("缺少捆包信息,调用失败");
                        return outInfo;
                    }
                    if (StringUtils.isBlank(partId)){
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("缺少物料信息,调用失败");
                        return outInfo;
                    }
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo",segNo);
                    queryMap.put("packId",packId);
                    queryMap.put("processOrderId",processOrderId);
                    if (!"3".equals(packType) && !"6".equals(packType)) {
                        queryMap.put("partId",partId);
                    }
                    //成品物料信息表数据
                    List<VIPM0007> outputlist = this.dao.query(VIPM0007.QUERY,queryMap);
                    if (CollectionUtils.isEmpty(outputlist) && "2".equals(packType)){
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("调用失败，缺少产出信息");
                        return outInfo;
                    }
                    VIPM0007 vipm0007 = new VIPM0007();
                    if (CollectionUtils.isNotEmpty(outputlist)){
                         vipm0007 = outputlist.get(0);
                    }
                    //投料捆包表数据
                    List<VIPM0008> materaillist = this.dao.query(VIPM0008.QUERY,queryMap);
                    if (CollectionUtils.isEmpty(materaillist)){
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("调用失败，缺少投料信息");
                        return outInfo;
                    }else {
                        materialPack.add(materaillist.get(0).toMap());
                    }
                    if (multiPackList != null){
                        distribution = multiPackList;
                        materialPack.clear();
                        for (Map map : multiPackList) {
                            HashMap queryPackMap = new HashMap();
                            queryPackMap.put("segNo",segNo);
                            queryPackMap.put("packId",map.get("f_packId"));
                            queryPackMap.put("matInnerId",map.get("f_matInnerId"));
                            List<VIPM0008> queryPackList = this.dao.query(VIPM0008.QUERY,queryPackMap);
                            if (CollectionUtils.isNotEmpty(queryPackList)){
                                materialPack.add(queryPackList.get(0).toMap());
                            }
                        }
                    }
                    VIPM0008 vipm0008 = materaillist.get(0);
                    //工单工序表数据
                    List<VIPM0009> processlist = this.dao.query(VIPM0009.QUERY,queryMap);
                    if (CollectionUtils.isEmpty(processlist)){
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("调用失败，缺少工序信息");
                        return outInfo;
                    }else {
                        information.putAll(processlist.get(0).toMap());
                    }
                    VIPM0009 vipm0009 = processlist.get(0);
                    VIPM0601 vipm0601 = new VIPM0601();
                    //可以从设备采集获取到的数据
                    vipm0601.setSegNo(segNo);
                    vipm0601.setUnitCode(segNo);
                    vipm0601.setProcessOrderId(processOrderId);
                    vipm0601.setPackId(MapUtils.getString(packMap,"outPutPackId",""));//产出捆包号
                    vipm0601.setUnitedPackId(MapUtils.getString(packMap,"unitedPackId",""));//并包号
                    if (StringUtils.isNotBlank(vipm0601.getUnitedPackId())){
                        vipm0601.setUnitedFlag("1");
                    }
                    vipm0601.setProcessHour(MapUtils.getBigDecimal(packMap,"processHour",new BigDecimal(0)));//加工工时
                    vipm0601.setNetWeight(MapUtils.getBigDecimal(packMap,"netWeight",new BigDecimal(0)));//净重
                    vipm0601.setActScWeight(MapUtils.getBigDecimal(packMap,"netWeight",new BigDecimal(0)));//实称重量
                    vipm0601.setQuantity(MapUtils.getInt(packMap,"quantity"));//数量
                    vipm0601.setPartId(partId);//物料号
                    vipm0601.setPackType(packType);//捆包类型
                    //查询机组附属信息的默认库位
                    queryMap.put("machineCode",vipm0009.getMachineCode());
                    List<HashMap> machineLocationList = dao.query(LIDS0701.QUERY_LOCATION,queryMap);
                    String materialWarehouseCode;//默认原料仓库
                    String materialLocationId;//默认原料库位
                    String productWarehouseCode;//默认成品仓库
                    String productLocationId;//默认成品库位
                    String materialWarehouseName;//默认原料仓库名称
                    String materialLocationName;//默认原料库位名称
                    String productWarehouseName;//默认成品仓库名称
                    String productLocationName;//默认成品库位名称
                    String materialUnloadingArea;//机组下料区代码
                    String materialUnloadingAreaName;//机组下料区名称
                    String priorityLoadMaterialArea;//优先上料区代码
                    String priorityLoadMaterialAreaName;//优先上料区名称
                    BigDecimal gauge = new BigDecimal(0); //厚度
                    BigDecimal width =new BigDecimal(0); //宽度
                    BigDecimal length = new BigDecimal(0); //长度
                    String periodId = "";//会计期间
                    //查询不到仓库和库位信息不需要报错，IMC实绩确认时添加校验报错
                    if (CollectionUtils.isEmpty(machineLocationList)){
                         materialWarehouseCode= "";
                         materialLocationId= "";
                         productWarehouseCode= "";
                         productLocationId= "";
                         materialWarehouseName= "";
                         materialLocationName= "";
                         productWarehouseName= "";
                         productLocationName= "";
                         priorityLoadMaterialArea="";
                         priorityLoadMaterialAreaName ="";
                         materialUnloadingArea = "";
                         materialUnloadingAreaName = "";
                    }else {
                        materialWarehouseCode= machineLocationList.get(0).get("materialWarehouseCode").toString();
                        materialLocationId= machineLocationList.get(0).get("materialLocationId").toString();
                        productWarehouseCode= machineLocationList.get(0).get("productWarehouseCode").toString();
                        productLocationId= machineLocationList.get(0).get("productLocationId").toString();
                        materialWarehouseName= machineLocationList.get(0).get("materialWarehouseName").toString();
                        materialLocationName= machineLocationList.get(0).get("materialLocationName").toString();
                        productWarehouseName= machineLocationList.get(0).get("productWarehouseName").toString();
                        productLocationName= machineLocationList.get(0).get("productLocationName").toString();
                        priorityLoadMaterialArea=machineLocationList.get(0).get("priorityLoadMaterialArea").toString();
                        priorityLoadMaterialAreaName =machineLocationList.get(0).get("priorityLoadMaterialAreaName").toString();
                        materialUnloadingArea=machineLocationList.get(0).get("materialUnloadingArea").toString();
                        materialUnloadingAreaName =machineLocationList.get(0).get("materialUnloadingAreaName").toString();
                    }
                    //调用查询班组、班次、班报序号、会计期间、厚度、宽度、长度
                    //班组、班次、班报序号通过当前时间查询排班数据，班次早班对应班组甲组，班次晚班对应班组乙组，根据班组和班次查询班报序号，若未查到则在IMC生成一个班报序号
                    EiInfo orderInfo = new EiInfo();
                    orderInfo.set("segNo",segNo);
                    orderInfo.set("partId",partId);
                    if ("6".equals(packType)){
                        orderInfo.set("partId",vipm0008.getPartId());
                    }
                    orderInfo.set("machineCode",vipm0009.getMachineCode());
                    orderInfo.set("processCategory",vipm0009.getProcessCategory());
                    orderInfo.set("serviceId", "S_VI_PM_0013");
                    orderInfo = EServiceManager.call(orderInfo, TokenUtils.getXplatToken());
                    if (orderInfo.getStatus() == EiConstant.STATUS_SUCCESS){
                        gauge = new BigDecimal(orderInfo.getString("gauge"));
                        width = new BigDecimal(orderInfo.getString("width"));
                        length = new BigDecimal(orderInfo.getString("length"));
                        periodId = orderInfo.getString("periodId");//会计期间
                        teamId = orderInfo.getString("teamId");//班组
                        workingShift = orderInfo.getString("workingShift");//班次
                        teamReportId = orderInfo.getString("teamReportId");//班报序号
                    }
                    //如果捆包类型是2成品，数据从成品信息取，如果捆包类型是4余料，则从原料信息取
                    if ("2".equals(packType)){
                        vipm0601.setSpecsDesc(vipm0007.getSpecsDesc());//规格
                        vipm0601.setSurfaceGrade(vipm0007.getSurfaceGrade());//表面等级
                        vipm0601.setWarehouseCode(productWarehouseCode);//仓库代码
                        vipm0601.setWarehouseName(productWarehouseName);//仓库名称
                        vipm0601.setLocationId(productLocationId);//库位代码
                        vipm0601.setLocationName(productLocationName);//库位名称
                        String bracketType = vipm0007.getBracketType();
                        if (StringUtils.isBlank(bracketType)) {
                            bracketType = "00";
                        }
                        vipm0601.setStoreTool(bracketType);
                        vipm0601.setMaterialRackId(vipm0007.getIronBracketNo());//料架号
                        vipm0601.setProdTypeId(vipm0007.getProdTypeId());//品种附属码
                        vipm0601.setProdTypeDesc(vipm0007.getProdTypeDesc());//品种附属码描述
                        vipm0601.setShopsign(vipm0007.getShopsign());//牌号
                        vipm0601.setContractNum(vipm0007.getContractNum());//销售订单号
                        vipm0601.setOrderNum(vipm0007.getOrderNum());//销售订单子项号
                        vipm0601.setStoreType("30");//存货性质 30成品
                        String packingTypeCode = vipm0007.getPackingTypeCode();
                        if (StringUtils.isBlank(packingTypeCode)) {
                            //成品信息没有包装方式默认为简包装
                            packingTypeCode = "QKJ35";
                        }
                        vipm0601.setPackingTypeCode(packingTypeCode);//包装方式代码
                        vipm0601.setCustomerId(vipm0007.getCustomerId());//客户代码
                        vipm0601.setCustomerName(vipm0007.getCustomerName());//客户名称
                        vipm0601.setD_userNum(vipm0007.getD_userNum());//分户号
                        vipm0601.setD_userName(vipm0007.getD_userName());//分户号名称
                        vipm0601.setSysRemark("IMOM新增成品");//系统备注
                        vipm0601.setOrderTypeCode(vipm0007.getOrderTypeCode());//订单性质代码
                        vipm0601.setContractPartId(vipm0007.getContractPartId());//订单物料号
                        vipm0601.setManualNo(vipm0007.getManualNo());//手册编号
                        vipm0601.setHsId(vipm0007.getHsId());//海关HS系统编码
                        vipm0601.setHandbookId(vipm0007.getHandbookId());//手册系统编号
                        vipm0601.setProcessConsignUnit(vipm0007.getProcessConsignUnit());//加工委托方
                        vipm0601.setCustPartId(vipm0007.getCustPartId());//客户零件号
                        vipm0601.setCustPartName(vipm0007.getCustPartName());//客户零件号名称
                    }else if ("4".equals(packType)){
                        vipm0601.setSpecsDesc(vipm0008.getSpecsDesc());//规格
                        vipm0601.setSurfaceGrade(vipm0008.getSurfaceGrade());//表面等级
                        vipm0601.setWarehouseCode(materialWarehouseCode);//仓库代码
                        vipm0601.setWarehouseName(materialWarehouseName);//仓库名称
                        vipm0601.setLocationId(materialLocationId);//库位代码
                        vipm0601.setLocationName(materialLocationName);//库位名称
                        vipm0601.setStoreTool("");//余料的装载工具默认为空
                        vipm0601.setProdTypeId(vipm0008.getProdTypeId());//品种附属码
                        vipm0601.setProdTypeDesc(vipm0008.getProdTypeDesc());//品种附属码描述
                        vipm0601.setShopsign(vipm0008.getShopsign());//牌号
                        vipm0601.setContractNum("");//销售订单号余料默认为空
                        vipm0601.setOrderNum("");//销售订单子项号余料默认为空
                        vipm0601.setStoreType("10");//存货性质 10原料
                        vipm0601.setPackingTypeCode("");//余料包装方式代码默认为空
                        vipm0601.setCustomerId(vipm0008.getCustomerId());//客户代码
                        vipm0601.setCustomerName(vipm0008.getCustomerName());//客户名称
                        vipm0601.setD_userNum(vipm0008.getD_userNum());//分户号
                        vipm0601.setD_userName(vipm0008.getD_userName());//分户号名称
                        vipm0601.setSysRemark("IMOM新增余料");//系统备注
                        vipm0601.setOrderTypeCode(vipm0008.getOrderTypeCode());//订单性质代码
                        vipm0601.setContractPartId("");//订单物料号
                        vipm0601.setManualNo(vipm0008.getManualNo());//手册编号
                        vipm0601.setHsId(vipm0008.getHsId());//海关HS系统编码
                        vipm0601.setHandbookId(vipm0008.getHandbookId());//手册系统编号
                        vipm0601.setProcessConsignUnit(vipm0008.getProcessConsignUnit());//加工委托方
                        vipm0601.setCustPartId(vipm0008.getCustPartId());//客户零件号
                        vipm0601.setCustPartName(vipm0008.getCustPartName());//客户零件号名称
                    }
                    //贸易方式优先取成品，如果成品没有，则取投料上的
                    String tradeCode = vipm0008.getTradeCode();
                    if (StringUtils.isNotBlank(vipm0007.getTradeCode())){
                        tradeCode = vipm0007.getTradeCode();
                    }
                    //品种代码：拼焊固定取Y2
                    String prodCode = "";
                    if ("TW".equals(vipm0009.getProcessCategory()) || "WT".equals(vipm0009.getProcessCategory()) || "WE".equals(vipm0009.getProcessCategory())){
                        prodCode="Y2";
                    }else {
                        if ("2".equals(packType) || "3".equals(packType)){
                            //成品捆包优先取成品上的品种代码如果没有则取投料上的
                            if (StringUtils.isNotBlank(vipm0007.getProdCode())){
                                prodCode=vipm0007.getProdCode();
                            }else {
                                prodCode=vipm0008.getProdCode();
                            }
                        }else if ("4".equals(packType) || "6".equals(packType)){
                            prodCode=vipm0008.getProdCode();
                        }
                    }
                    if ("".equals(prodCode)){
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("调用失败，投料和成品都没有品种代码");
                        return outInfo;
                    }
                    vipm0601.setProdCode(prodCode);//品种代码
                    vipm0601.setTradeCode(tradeCode);//贸易方式
                    vipm0601.setGauge(gauge);//厚度
                    vipm0601.setWidth(width);//宽度
                    vipm0601.setLength(length);//长度
                    vipm0601.setOriginalPackId(materaillist.get(0).getOriginalPackId());//原始捆包号
                    vipm0601.setQuantity(new Integer(packMap.get("quantity").toString()));//数量
                    vipm0601.setNetWeight(new BigDecimal(packMap.get("netWeight").toString()));//净重
                    vipm0601.setGrossWeight(vipm0601.getNetWeight().add(new BigDecimal(20)));//毛重
                    vipm0601.setTareWt(new BigDecimal(0));//皮重 非股份默认为0
                    vipm0601.setMachineCode(vipm0009.getMachineCode());//机组代码
                    vipm0601.setQualityStatus("0");//质量状态默认未封闭
                    vipm0601.setBlockReason("");//封闭原因默认为空
                    vipm0601.setScrapType("");//利用材种类默认为空
                    vipm0601.setQualityGrade("1");//质量等级默认为正品
                    vipm0601.setMaterialRackId("");//料架号默认为空
                    vipm0601.setBigTypeDesc("");//利用材种类描述默认为空
                    vipm0601.setProcessResultStatus("05");//生产实绩状态默认为新增
                    vipm0601.setWeightUnit("WT"); //重量单位 默认为吨
                    vipm0601.setQuantityUnit("PP");//数量单位 默认为件
                    vipm0601.setProcessDemandId(vipm0009.getProcessDemandId());//生产需求单号
                    vipm0601.setProcessDemandSubId(vipm0009.getProcessDemandSubId());//生产需求单子项号
                    vipm0601.setRemark("");//备注
                    vipm0601.setOthersRemark("");//其他说明
                    vipm0601.setPackingFlag("");//包装标记
                    vipm0601.setFactoryOrderNum(vipm0008.getFactoryOrderNum());//钢厂订单号
                    vipm0601.setProcessCategory(vipm0009.getProcessCategory());//工序大类代码
                    vipm0601.setP_output(vipm0007.getP_output() == null ? new BigDecimal(1) : vipm0007.getP_output());//成品配比
                    vipm0601.setProxyType("10");//类型默认10
                    vipm0601.setIfUploadFinance("0");//是否上传财务
                    vipm0601.setProcessDemandOutputId(vipm0007.getProcessDemandOutputId());//生产需求产出表单据号
                    vipm0601.setOutPackId("");//外部捆包号
                    vipm0601.setPonum("");//PO号
                    vipm0601.setCuttingNum("");//分切序号
                    vipm0601.setPackWeight(vipm0008.getNetWeight());//捆包重量
                    vipm0601.setF_handbookId(vipm0008.getF_handbookId());//父手册号(上层手册号)
                    vipm0601.setM_handbookId(vipm0008.getM_handbookId());//母手册号(最初手册号)
                    vipm0601.setProducingArea(vipm0008.getProducingArea());//产地
                    vipm0601.setMakerNum(vipm0008.getMakerNum());//制造商代码
                    vipm0601.setMakerName(vipm0008.getMakerName());//制造商名称
                    vipm0601.setProviderCode(vipm0008.getProviderCode());//供应商代码
                    vipm0601.setProviderCname(vipm0008.getProviderCname());//供应商名称
                    vipm0601.setHeatNo("");//炉台号
                    vipm0601.setHeatNum(vipm0008.getHeatNum());//炉号
                    vipm0601.setCraftCode(vipm0009.getCraftCode());//工艺单号
                    vipm0601.setProcessSeqId(vipm0009.getProcessSeqId());//工序号
                    vipm0601.setCarTypeCode(vipm0008.getCarTypeCode());//车型代码
                    vipm0601.setVehicleName(vipm0008.getVehicleName());//车型名称
                    vipm0601.setQuota(vipm0008.getQuota());//定额(KG)
                    vipm0601.setPartsCode(vipm0008.getPartsCode());//部件编码
                    vipm0601.setPartsName(vipm0008.getPartsName());//部品名称
                    vipm0601.setPeriodId(periodId);//会计期间
                    vipm0601.setTeamId(teamId);//班组
                    vipm0601.setWorkingShift(workingShift);//班次
                    vipm0601.setTeamReportId(teamReportId);//班报序号
                    if ("3".equals(packType)){
                        //成品废次材
                        vipm0601.setSpecsDesc("0*0*0");//规格
                        vipm0601.setSurfaceGrade(vipm0007.getSurfaceGrade());//表面等级
                        vipm0601.setWarehouseCode(productWarehouseCode);//仓库代码
                        vipm0601.setWarehouseName(productWarehouseName);//仓库名称
                        vipm0601.setLocationId(productLocationId);//库位代码
                        vipm0601.setLocationName(productLocationName);//库位名称
                        vipm0601.setStoreTool("");
                        if ("SL".equals(vipm0009.getProcessCategory())){
                            //纵切工序默认废次材为边丝
                            vipm0601.setScrapType("C01");
                            vipm0601.setBigTypeDesc("边丝");
                            vipm0601.setLocationId("Z4-LY");//库位代码
                            vipm0601.setLocationName("Z4-LY");//库位名称
                        }
                        vipm0601.setProdTypeId(vipm0007.getProdTypeId());//品种附属码
                        vipm0601.setProdTypeDesc(vipm0007.getProdTypeDesc());//品种附属码描述
                        vipm0601.setShopsign(vipm0007.getShopsign());//牌号
                        vipm0601.setContractNum(vipm0007.getContractNum());//销售订单号
                        vipm0601.setOrderNum(vipm0007.getOrderNum());//销售订单子项号
                        vipm0601.setStoreType("30");//存货性质 30成品
                        vipm0601.setPackingTypeCode("");//包装方式代码
                        vipm0601.setCustomerId(vipm0007.getCustomerId());//客户代码
                        vipm0601.setCustomerName(vipm0007.getCustomerName());//客户名称
                        vipm0601.setD_userNum(vipm0007.getD_userNum());//分户号
                        vipm0601.setD_userName(vipm0007.getD_userName());//分户号名称
                        vipm0601.setSysRemark("IMOM新增成品废次材");//系统备注
                        vipm0601.setOrderTypeCode(vipm0007.getOrderTypeCode());//订单性质代码
                        vipm0601.setContractPartId(vipm0007.getContractPartId());//订单物料号
                        vipm0601.setManualNo(vipm0007.getManualNo());//手册编号
                        vipm0601.setHsId(vipm0007.getHsId());//海关HS系统编码
                        vipm0601.setHandbookId(vipm0007.getHandbookId());//手册系统编号
                        vipm0601.setProcessConsignUnit(vipm0007.getProcessConsignUnit());//加工委托方
                        vipm0601.setCustPartId(vipm0007.getCustPartId());//客户零件号
                        vipm0601.setCustPartName(vipm0007.getCustPartName());//客户零件号名称
                        vipm0601.setQualityGrade("4");//质量等级默认可利用材
                    }else if ("6".equals(packType)){
                        //原料废次材
                        vipm0601.setSpecsDesc("0*0*0");//规格
                        vipm0601.setSurfaceGrade(vipm0008.getSurfaceGrade());//表面等级
                        vipm0601.setWarehouseCode(materialWarehouseCode);//仓库代码
                        vipm0601.setWarehouseName(materialWarehouseName);//仓库名称
                        vipm0601.setLocationId(materialLocationId);//库位代码
                        vipm0601.setLocationName(materialLocationName);//库位名称
                        vipm0601.setStoreTool("");//余料的装载工具默认为空
                        vipm0601.setProdTypeId(vipm0008.getProdTypeId());//品种附属码
                        vipm0601.setProdTypeDesc(vipm0008.getProdTypeDesc());//品种附属码描述
                        vipm0601.setShopsign(vipm0008.getShopsign());//牌号
                        vipm0601.setContractNum("");//销售订单号余料默认为空
                        vipm0601.setOrderNum("");//销售订单子项号余料默认为空
                        vipm0601.setStoreType("30");//存货性质
                        vipm0601.setPackingTypeCode("");//废次材包装方式代码默认为空
                        vipm0601.setCustomerId(vipm0008.getCustomerId());//客户代码
                        vipm0601.setCustomerName(vipm0008.getCustomerName());//客户名称
                        vipm0601.setD_userNum(vipm0008.getD_userNum());//分户号
                        vipm0601.setD_userName(vipm0008.getD_userName());//分户号名称
                        vipm0601.setSysRemark("IMOM新增原料废次材");//系统备注
                        vipm0601.setOrderTypeCode(vipm0008.getOrderTypeCode());//订单性质代码
                        vipm0601.setContractPartId("");//订单物料号
                        vipm0601.setManualNo(vipm0008.getManualNo());//手册编号
                        vipm0601.setHsId(vipm0008.getHsId());//海关HS系统编码
                        vipm0601.setHandbookId(vipm0008.getHandbookId());//手册系统编号
                        vipm0601.setProcessConsignUnit(vipm0008.getProcessConsignUnit());//加工委托方
                        vipm0601.setCustPartId(vipm0008.getCustPartId());//客户零件号
                        vipm0601.setCustPartName(vipm0008.getCustPartName());//客户零件号名称
                        vipm0601.setQualityGrade("4");//质量等级默认可利用材
                        if ("SL".equals(vipm0009.getProcessCategory())){
                            if ("FZ230416013".equals(partId)){
                                //边丝
                                vipm0601.setScrapType("C01");
                                vipm0601.setBigTypeDesc("边丝");
                                vipm0601.setLocationId("Z4-LY");//库位代码
                                vipm0601.setLocationName("Z4-LY");//库位名称
                            }else if ("FZ230416002".equals(partId)){
                                //切边余料
                                vipm0601.setScrapType("A02");
                                vipm0601.setBigTypeDesc("切边余料");
                                vipm0601.setLocationId("Z4-LY");//库位代码
                                vipm0601.setLocationName("Z4-LY");//库位名称
                                String specsDesc = vipm0008.getSpecsDesc();
                                String[] parts = specsDesc.split("\\*");
                                String firstPart = parts[0];
                                vipm0601.setSpecsDesc(firstPart+"*0*C");//规格
                            }
                        }
                    }
                    productPackResult.add(vipm0601.toMap());
                    //生成实物库存，根据工序判断产出的是板还是卷（目前不管产出板还是卷都是生成在机组下料区的库存）
                    if ("SL".equals(vipm0009.getProcessCategory())){
                        LIDS0901 lids0901 = new LIDS0901();
                        lids0901.setSegNo(vipm0009.getSegNo());
                        lids0901.setUnitCode(vipm0009.getUnitCode());
                        lids0901.setPackId(MapUtils.getString(packMap,"outPutPackId",""));
                        lids0901.setUnitedPackId(MapUtils.getString(packMap,"unitedPackId",""));
                        lids0901.setLabelId(MapUtils.getString(packMap,"outPutPackId",""));
                        lids0901.setNetWeight(new BigDecimal(packMap.get("netWeight").toString()).divide(new BigDecimal(1000)));
                        lids0901.setGrossWeight(vipm0601.getNetWeight().add(new BigDecimal(20)).divide(new BigDecimal(1000)));
                        lids0901.setCraneOperationWeight(new BigDecimal(packMap.get("netWeight").toString()).divide(new BigDecimal(1000)));
                        lids0901.setQuantity(new Integer(packMap.get("quantity").toString()));//数量
                        lids0901.setPosDirCode("");
                        lids0901.setCraneResultId("");
                        lids0901.setActionFlag("1");
                        lids0901.setInnerOutterPlateFlag("");
                        lids0901.setStatus("10");
                        lids0901.setX_position(MapUtils.getString(packMap,"x_position","0"));
                        lids0901.setY_position(MapUtils.getString(packMap,"y_position","0"));
                        lids0901.setZ_position(MapUtils.getString(packMap,"z_position","0"));
                        lids0901.setRecCreator("mesSystem");
                        lids0901.setRecCreatorName("mesSystem");
                        lids0901.setRecCreateTime(DateUtil.curDateTimeStr14());
                        lids0901.setRecRevisor("mesSystem");
                        lids0901.setRecRevisorName("mesSystem");
                        lids0901.setRecReviseTime(DateUtil.curDateTimeStr14());
                        lids0901.setUuid(UUIDUtils.getUUID());
                        lids0901.setDelFlag(0);
                        if ("2".equals(packType)){
                            lids0901.setWarehouseCode(productWarehouseCode);
                            lids0901.setWarehouseName(productWarehouseName);
                            lids0901.setAreaType("61");
                            lids0901.setAreaCode(areaCode);
                            lids0901.setAreaName("");
                        } if ("4".equals(packType)){
                            lids0901.setWarehouseCode(materialWarehouseCode);
                            lids0901.setWarehouseName(materialWarehouseCode);
                            lids0901.setAreaType("60");
                            //TODO 余料的库存放在那里
                            lids0901.setAreaCode("");
                            lids0901.setAreaName("");
                            lids0901.setOriginalPackId(packId);
                        }
                        if (!"3".equals(packType) && !"6".equals(packType)) {
                            dao.insert(LIDS0901.INSERT, lids0901);
                        }
                    }else {
                        LIDS0901 lids0901 = new LIDS0901();
                        lids0901.setSegNo(vipm0009.getSegNo());
                        lids0901.setUnitCode(vipm0009.getUnitCode());
                        lids0901.setPackId(MapUtils.getString(packMap,"outPutPackId",""));
                        lids0901.setLabelId(MapUtils.getString(packMap,"outPutPackId",""));
                        lids0901.setNetWeight(new BigDecimal(packMap.get("netWeight").toString()).divide(new BigDecimal(1000)));
                        lids0901.setGrossWeight(vipm0601.getNetWeight().add(new BigDecimal(20)).divide(new BigDecimal(1000)));
                        lids0901.setCraneOperationWeight(new BigDecimal(packMap.get("netWeight").toString()).divide(new BigDecimal(1000)));
                        lids0901.setQuantity(new Integer(packMap.get("quantity").toString()));//数量
                        lids0901.setPosDirCode("");
                        lids0901.setCraneResultId("");
                        lids0901.setActionFlag("0");
                        lids0901.setInnerOutterPlateFlag("");
                        lids0901.setStatus("10");
                        lids0901.setX_position(MapUtils.getString(packMap,"x_position","0"));
                        lids0901.setY_position(MapUtils.getString(packMap,"y_position","0"));
                        lids0901.setZ_position(MapUtils.getString(packMap,"z_position","0"));
                        lids0901.setRecCreator("mesSystem");
                        lids0901.setRecCreatorName("mesSystem");
                        lids0901.setRecCreateTime(DateUtil.curDateTimeStr14());
                        lids0901.setRecRevisor("mesSystem");
                        lids0901.setRecRevisorName("mesSystem");
                        lids0901.setRecReviseTime(DateUtil.curDateTimeStr14());
                        lids0901.setUuid(UUIDUtils.getUUID());
                        lids0901.setDelFlag(0);
                        if ("2".equals(packType)){
                            lids0901.setWarehouseCode(productWarehouseCode);
                            lids0901.setWarehouseName(productWarehouseName);
                            lids0901.setAreaType("61");
                            lids0901.setAreaCode(areaCode);
                            lids0901.setAreaName("");
                        } if ("4".equals(packType)){
                            lids0901.setWarehouseCode(materialWarehouseCode);
                            lids0901.setWarehouseName(materialWarehouseCode);
                            lids0901.setAreaType("60");
                            //TODO 余料的库存放在那里
                            lids0901.setAreaCode("");
                            lids0901.setAreaName("");
                            lids0901.setOriginalPackId(packId);
                        }
                        if (!"3".equals(packType) && !"6".equals(packType)) {
                            dao.insert(LIDS0901.INSERT, lids0901);
                        }
                    }
                }
            }
            List<Map<String, Object>> loginUser =new ArrayList<>();
            HashMap loginMap = new HashMap();
            loginMap.put("userId","mesSystem");
            loginMap.put("userName","mesSystem");
            loginMap.put("loginName","mesSystem");
            loginUser.add(loginMap);
            EiBlock eiBlock = new EiBlock("loginUser");
            outInfo.addBlock(eiBlock);
            outInfo.getBlock("loginUser").addRows(loginUser);
            outInfo.set("productPackResult",productPackResult);
            outInfo.set("materialPack",materialPack);
            outInfo.set("information",information);
            outInfo.set("ifSave","1");
            outInfo.set("workingShift",workingShift);
            outInfo.set("teamId",teamId);
            outInfo.set("teamReportId",teamReportId);
            if (CollectionUtils.isNotEmpty(distribution)){
                outInfo.set("distribution",distribution);
            }
            outInfo.set("segNo",segNo);
            outInfo.set("unitCode",segNo);
            outInfo.set("serviceId", "S_VI_PM_0011");
            String inParameter = outInfo.toJSONString();
            outInfo = EServiceManager.call(outInfo, TokenUtils.getXplatToken());
            log(getFormat() + "："+"设备采集生成生产实绩传入参数："+inParameter +"\n"+"设备采集生成生产实绩返回参数："+outInfo.toJSONString());
            //输出到应用日志
            System.out.println(getFormat() + "："+"设备采集生成生产实绩传入参数：" + inParameter+"设备采集生成生产实绩返回参数："+outInfo.toJSONString());
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("调用成功，"+ outInfo.getMsg());
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("调用失败，"+ex);
            return outInfo;
        }
        return outInfo;
    }
    //根据传入数据生成模具更换的行车作业清单
    public EiInfo receiveMouldCraneOrderId(EiInfo eiInfo){
        List<VIPM0009> outputList = eiInfo.get("outputList") == null
                ? Collections.emptyList()
                : (List<VIPM0009>) eiInfo.get("outputList");
        List<VIPM0009> nextMouldIdList = eiInfo.get("nextMouldIdList") == null
                ? Collections.emptyList()
                :(List<VIPM0009>) eiInfo.get("nextMouldIdList");
        if (CollectionUtils.isNotEmpty(outputList)){
            //当前工单模具台车到模具区的行车作业清单
            //主表字段
            LIDS1101 lids1101 = new LIDS1101();
            String[] args = {outputList.get(0).getSegNo().substring(0, 2)};
            String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
            lids1101.setCraneOrderId(applyId);
            lids1101.setSegNo(outputList.get(0).getSegNo());
            lids1101.setUnitCode(outputList.get(0).getSegNo());
            //清单来源 模具更换70
            lids1101.setListSource("70");
            //依据凭单号（生产工单号）
            lids1101.setVoucherNum(outputList.get(0).getProcessOrderId());
            //批次号
            lids1101.setBatchNumber("");
            //顺序号
            lids1101.setSerialNumber("");
            //机组代码
            lids1101.setMachineCode("");
            //机组名称
            lids1101.setMachineName("");
            //拆包区编号
            lids1101.setUnpackAreaId("");
            //拆包区名称
            lids1101.setUnpackAreaName("");
            //模具ID
            lids1101.setMouldId(outputList.get(0).getMouldId());
            //查询模具库存获取模具
            HashMap queryMap = new HashMap();
            queryMap.put("mouldId",outputList.get(0).getMouldId());
            queryMap.put("segNo",outputList.get(0).getSegNo());
            //净重
            BigDecimal netWeight = new BigDecimal("0");
            String mouldName = "";
            List <LIDS0801> mouldList = this.dao.query(LIDS0801.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(mouldList)){
                netWeight = mouldList.get(0).getMouldSpecWeightSum();
                mouldName = mouldList.get(0).getMouldName();
            }
            //模具名称
            lids1101.setMouldName(mouldName);
            //作业开始时间
            lids1101.setStartTime("");
            //作业结束时间
            lids1101.setEndTime("");
            //作业时间
            lids1101.setJobTime("");
            //根据机组对应的跨区，查询当前跨区下的所有行车
            String craneId = "";
            String craneName = "";
            String startAreaCode = "";
            String startAreaName = "";
            String endAreaCode = "";
            String endAreaName = "";
            queryMap.put("machineCode", outputList.get(0).getMachineCode());
            List<LIDS0701> machineList = this.dao.query(LIDS0701.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(machineList)){
                String crossArea = machineList.get(0).getCrossArea();
                startAreaCode = machineList.get(0).getMouldCart();
                startAreaName = machineList.get(0).getMouldCartName();
                queryMap.put("crossArea", crossArea);
                //根据跨区查询厂区厂房
                HashMap queryCrossAreaMap = new HashMap();
                queryCrossAreaMap.put("segNo",outputList.get(0).getSegNo());
                queryCrossAreaMap.put("crossArea",crossArea);
                List<LIDS0201> areaList = this.dao.query(LIDS0201.QUERY,queryCrossAreaMap);
                if (CollectionUtils.isNotEmpty(areaList)) {
                    queryMap.put("factoryArea", areaList.get(0).getFactoryArea());
                    queryMap.put("factoryBuilding", areaList.get(0).getFactoryBuilding());
                    queryMap.put("areaType", "50");
                    List<LIDS0101> mouldAreaList = this.dao.query(LIDS0101.QUERY,queryMap);
                    if (CollectionUtils.isNotEmpty(mouldAreaList)){
                        endAreaCode=mouldAreaList.get(0).getAreaCode();
                        endAreaName=mouldAreaList.get(0).getAreaName();
                    }
                }
                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(craneList)) {
                    craneId = craneList.stream()
                            .map(LIDS0301::getCraneId)
                            .collect(Collectors.joining(","));
                    craneName = craneList.stream()
                            .map(LIDS0301::getCraneName)
                            .collect(Collectors.joining(","));
                }
            }
            //行车编号
            lids1101.setCraneId(craneId);
            //行车名称
            lids1101.setCraneName(craneName);
            //起始区域类型
            lids1101.setStartAreaType("65");
            //起始区域类型代码
            lids1101.setStartAreaCode(startAreaCode);
            //起始区域类型名称
            lids1101.setStartAreaName(startAreaName);
            //终到区域类型
            lids1101.setEndAreaType("50");
            //终到区域类型代码
            lids1101.setEndAreaCode(endAreaCode);
            //终到区域类型名称
            lids1101.setEndAreaName(endAreaName);
            //状态
            lids1101.setStatus("20");
            //记录创建人
            lids1101.setRecCreator("system");
            //记录创建人姓名
            lids1101.setRecCreatorName("system");
            //记录创建时间
            lids1101.setRecCreateTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //记录修改人
            lids1101.setRecRevisor("system");
            //记录修改人姓名
            lids1101.setRecRevisorName("system");
            //记录修改时间
            lids1101.setRecReviseTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //归档标记
            lids1101.setArchiveFlag("");
            //租户
            lids1101.setTenantUser("");
            //删除标记
            lids1101.setDelFlag(0);
            //ID
            lids1101.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1101.INSERT, lids1101);
            LIDS1102 lids1102 = new LIDS1102();
            String[] args1 = {applyId};
            String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
            //行车作业清单子项
            lids1102.setCraneOrderId(applyId);
            lids1102.setSegNo(outputList.get(0).getSegNo());
            lids1102.setUnitCode(outputList.get(0).getUnitCode());
            lids1102.setCraneOrderSubId(applySubId);
            //状态
            lids1102.setStatus("20");
            //捆包号
            lids1102.setPackId("");
            //标签号
            lids1102.setLabelId("");
            //净重
            lids1102.setNetWeight(netWeight);
            //数量
            lids1102.setQuantity(1);
            //记录创建人
            lids1102.setRecCreator("system");
            //记录创建人姓名
            lids1102.setRecCreatorName("system");
            //记录创建时间
            lids1102.setRecCreateTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //记录修改人
            lids1102.setRecRevisor("system");
            //记录修改人姓名
            lids1102.setRecRevisorName("system");
            //记录修改时间
            lids1102.setRecReviseTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //归档标记
            lids1102.setArchiveFlag("");
            //租户
            lids1102.setTenantUser("");
            //删除标记
            lids1102.setDelFlag(0);
            //ID
            lids1102.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1102.INSERT, lids1102);
        }
        if (CollectionUtils.isNotEmpty(nextMouldIdList)){
            //模具区到模具台车的行车作业清单
            //主表字段
            LIDS1101 lids1101 = new LIDS1101();
            String[] args = {nextMouldIdList.get(0).getSegNo().substring(0, 2)};
            String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
            lids1101.setCraneOrderId(applyId);
            lids1101.setSegNo(nextMouldIdList.get(0).getSegNo());
            lids1101.setUnitCode(nextMouldIdList.get(0).getSegNo());
            //清单来源 模具更换70
            lids1101.setListSource("70");
            //依据凭单号（生产工单号）
            lids1101.setVoucherNum(nextMouldIdList.get(0).getProcessOrderId());
            //批次号
            lids1101.setBatchNumber("");
            //顺序号
            lids1101.setSerialNumber("");
            //机组代码
            lids1101.setMachineCode("");
            //机组名称
            lids1101.setMachineName("");
            //拆包区编号
            lids1101.setUnpackAreaId("");
            //拆包区名称
            lids1101.setUnpackAreaName("");
            //模具ID
            lids1101.setMouldId(nextMouldIdList.get(0).getMouldId());
            //查询模具库存获取模具
            HashMap queryMap = new HashMap();
            queryMap.put("mouldId",nextMouldIdList.get(0).getMouldId());
            queryMap.put("segNo",nextMouldIdList.get(0).getSegNo());
            //净重
            BigDecimal netWeight = new BigDecimal("0");
            String mouldName = "";
            String startAreaCode = "";
            String startAreaName = "";
            List <LIDS0801> mouldList = this.dao.query(LIDS0801.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(mouldList)){
                netWeight = mouldList.get(0).getMouldSpecWeightSum();
                mouldName = mouldList.get(0).getMouldName();
                startAreaCode = mouldList.get(0).getAreaCode();
                startAreaName = mouldList.get(0).getAreaName();
            }
            //模具名称
            lids1101.setMouldName(mouldName);
            //作业开始时间
            lids1101.setStartTime("");
            //作业结束时间
            lids1101.setEndTime("");
            //作业时间
            lids1101.setJobTime("");
            //根据机组对应的跨区，查询当前跨区下的所有行车
            String craneId = "";
            String craneName = "";
            String endAreaCode = "";
            String endAreaName = "";
            queryMap.put("machineCode", nextMouldIdList.get(0).getMachineCode());
            List<LIDS0701> machineList = this.dao.query(LIDS0701.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(machineList)){
                String crossArea = machineList.get(0).getCrossArea();
                endAreaCode = machineList.get(0).getMouldCart();
                endAreaName = machineList.get(0).getMouldCartName();
                queryMap.put("crossArea", crossArea);
                //根据跨区查询厂区厂房
                HashMap queryCrossAreaMap = new HashMap();
                queryCrossAreaMap.put("segNo",outputList.get(0).getSegNo());
                queryCrossAreaMap.put("crossArea",crossArea);
                List<LIDS0201> areaList = this.dao.query(LIDS0201.QUERY,queryCrossAreaMap);
                if (CollectionUtils.isNotEmpty(areaList)) {
                    queryMap.put("factoryArea", areaList.get(0).getFactoryArea());
                    queryMap.put("factoryBuilding", areaList.get(0).getFactoryBuilding());
                }
                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(craneList)) {
                    craneId = craneList.stream()
                            .map(LIDS0301::getCraneId)
                            .collect(Collectors.joining(","));
                    craneName = craneList.stream()
                            .map(LIDS0301::getCraneName)
                            .collect(Collectors.joining(","));
                }
            }
            //行车编号
            lids1101.setCraneId(craneId);
            //行车名称
            lids1101.setCraneName(craneName);
            //起始区域类型
            lids1101.setStartAreaType("50");
            //起始区域类型代码
            lids1101.setStartAreaCode(startAreaCode);
            //起始区域类型名称
            lids1101.setStartAreaName(startAreaName);
            //终到区域类型
            lids1101.setEndAreaType("65");
            //终到区域类型代码
            lids1101.setEndAreaCode(endAreaCode);
            //终到区域类型名称
            lids1101.setEndAreaName(endAreaName);
            //状态
            lids1101.setStatus("20");
            //记录创建人
            lids1101.setRecCreator("system");
            //记录创建人姓名
            lids1101.setRecCreatorName("system");
            //记录创建时间
            lids1101.setRecCreateTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //记录修改人
            lids1101.setRecRevisor("system");
            //记录修改人姓名
            lids1101.setRecRevisorName("system");
            //记录修改时间
            lids1101.setRecReviseTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //归档标记
            lids1101.setArchiveFlag("");
            //租户
            lids1101.setTenantUser("");
            //删除标记
            lids1101.setDelFlag(0);
            //ID
            lids1101.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1101.INSERT, lids1101);
            LIDS1102 lids1102 = new LIDS1102();
            String[] args1 = {applyId};
            String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
            //行车作业清单子项
            lids1102.setCraneOrderId(applyId);
            lids1102.setSegNo(nextMouldIdList.get(0).getSegNo());
            lids1102.setUnitCode(nextMouldIdList.get(0).getUnitCode());
            lids1102.setCraneOrderSubId(applySubId);
            //状态
            lids1102.setStatus("20");
            //捆包号
            lids1102.setPackId("");
            //标签号
            lids1102.setLabelId("");
            //净重
            lids1102.setNetWeight(netWeight);
            //数量
            lids1102.setQuantity(1);
            //记录创建人
            lids1102.setRecCreator("system");
            //记录创建人姓名
            lids1102.setRecCreatorName("system");
            //记录创建时间
            lids1102.setRecCreateTime(com.baosight.iplat4j.core.util.DateUtils.curDateTimeStr14());
            //记录修改人
            lids1102.setRecRevisor("system");
            //记录修改人姓名
            lids1102.setRecRevisorName("system");
            //记录修改时间
            lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
            //归档标记
            lids1102.setArchiveFlag("");
            //租户
            lids1102.setTenantUser("");
            //删除标记
            lids1102.setDelFlag(0);
            //ID
            lids1102.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1102.INSERT, lids1102);
        }
        return eiInfo;
    }

    //接收工单下发的数据生成上产上料的行车作业清单
    public EiInfo CreateCraneOrderId(EiInfo eiInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> demandProcessList = (List<HashMap>) eiInfo.get("demandProcessList");
            if (CollectionUtils.isNotEmpty(demandProcessList)) {
                for (HashMap hashMap : demandProcessList) {
                    //判断当前工单是否已经生成过行车作业清单若之前生成过，先将原来的行车作业清单撤销，备注中标识是系统自动撤销(与手工撤销的做区分)
                    HashMap orderMap = new HashMap();
                    String segNo = (String)hashMap.get("segNo");
                    orderMap.put("segNo", segNo);
                    orderMap.put("voucherNum", hashMap.get("processOrderId"));
                    orderMap.put("craneResultId", "craneResultId");
                    List<LIDS1101> processList = this.dao.query(LIDS1101.QUERY_CRANE_ORDER_ID, orderMap);
                    if (CollectionUtils.isNotEmpty(processList)) {
                        for (LIDS1101 lids1101 : processList) {
                            lids1101.setStatus("00");
                            lids1101.setDelFlag(1);
                            lids1101.setRecRevisor("system");
                            lids1101.setRecRevisorName("系统自动撤销");
                            lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                            this.dao.update(LIDS1101.DELETE_FLAG, lids1101);
                            HashMap deleteMap = new HashMap();
                            deleteMap.put("segNo", segNo);
                            deleteMap.put("craneOrderId", lids1101.getCraneOrderId());
                            List<LIDS1102> subList = this.dao.query(LIDS1102.QUERY, deleteMap);
                            if (CollectionUtils.isNotEmpty(subList)) {
                                for (LIDS1102 lids1102 : subList) {
                                    lids1102.setStatus("00");
                                    lids1102.setDelFlag(1);
                                    lids1102.setRecRevisor("system");
                                    lids1102.setRecRevisorName("系统自动撤销");
                                    lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                    this.dao.update(LIDS1102.DELETE_FLAG, lids1102);
                                }
                            }
                        }
                    }
                    //同一个工单同一批次
                    String[] batch = {segNo.substring(0, 2)};
                    String batchId = SequenceGenerator.getNextSequence("TLIDS_SEQ1103", batch);
                    String processOrderId =(String) hashMap.get("processOrderId");
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo", segNo);
                    queryMap.put("processOrderId", processOrderId);
                    //查询当前工单封锁的捆包
                    List<VIPM0008> packIdList = this.dao.query(VIPM0008.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(packIdList)) {
                        for (VIPM0008 vipm0008 : packIdList) {
                            String packId = vipm0008.getPackId();
                            queryMap.clear();
                            queryMap.put("segNo", segNo);
                            queryMap.put("packId", packId);
                            //查询实物库存表获取板卷类型
                            List<LIDS0901> packList = dao.query(LIDS0901.QUERY_PACK_MESSAGE, queryMap);
                            //顺序号
                            Integer SerialNumber = 0;
                            if (CollectionUtils.isNotEmpty(packList)) {
                                //目前只生成一期厂房的的捆包
                                HashMap locationIdMap = new HashMap<>();
                                locationIdMap.put("locationId", packList.get(0).getAreaCode());
                                int count = super.count(LIDS0601.COUNT_LOCATION_ID, locationIdMap);
                                if (count == 1) {
                                    //根据捆包库位找对应的跨区，查询当前跨区下的所有行车
                                    queryMap.put("accurateLocationId", packList.get(0).getAreaCode());
                                    List<LIDS0601> locationList = this.dao.query(LIDS0601.QUERY, queryMap);
                                    String actionFlag = packList.get(0).getActionFlag();
                                    if ("0".equals(actionFlag)) {
                                        //捆包为板时，机组为翻板机、翻卷机、缠绕机不做管理
                                        //捆包为板时，机组非翻板机（目前投料是板的都是非翻板机，翻板的流程目前IMC没有体现）不生成行车作业清单，只接受行车作业实绩
                                    } else if ("1".equals(actionFlag)) {
                                        //捆包为卷时，判断当前捆包是否为下层捆包
                                        if ("1".equals(packList.get(0).getPosDirCode())) {
                                            //捆包为下层，查询捆包上层是否有需要倒库的捆包
                                            queryMap.clear();
                                            queryMap.put("segNo", segNo);
                                            queryMap.put("packId", packId);
                                            queryMap.put("XPosition", packList.get(0).getX_position());
                                            queryMap.put("YPosition", packList.get(0).getY_position());
                                            queryMap.put("warehouseCode", packList.get(0).getWarehouseCode());
                                            queryMap.put("areaCode", packList.get(0).getAreaCode());
                                            List<LIDS0901> stockList = dao.query(LIDS0901.QUERY_RELATED_PACKS_BY_ADJACENT_IDS, queryMap);
                                            if (CollectionUtils.isNotEmpty(stockList)) {
                                                for (LIDS0901 lids0901 : stockList) {
                                                    //查询该捆包是否已经生成倒库的行车作业清单
                                                    HashMap alterationMap = new HashMap();
                                                    alterationMap.put("segNo", segNo);
                                                    alterationMap.put("listSource", "50");
                                                    alterationMap.put("packId", lids0901.getPackId());
                                                    int craneOrder = super.count(LIDS1101.COUNT_CRANE_ORDER, alterationMap);
                                                    if (craneOrder == 0) {
                                                        LIDS1101 lids1101 = new LIDS1101();
                                                        String[] args = {segNo.substring(0, 2)};
                                                        String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101.setCraneOrderId(applyId);
                                                        lids1101.setSegNo(segNo);
                                                        lids1101.setUnitCode(segNo);
                                                        //清单来源 50倒库
                                                        lids1101.setListSource("50");
                                                        //依据凭单号（生产工单号）
                                                        lids1101.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101.setSerialNumber(SerialNumber.toString());
                                                        //机组代码
                                                        lids1101.setMachineCode("");
                                                        //机组名称
                                                        lids1101.setMachineName("");
                                                        //拆包区编号
                                                        lids1101.setUnpackAreaId("");
                                                        //拆包区名称
                                                        lids1101.setUnpackAreaName("");
                                                        //模具ID
                                                        lids1101.setMouldId("");
                                                        //模具名称
                                                        lids1101.setMouldName("");
                                                        //作业开始时间
                                                        lids1101.setStartTime("");
                                                        //作业结束时间
                                                        lids1101.setEndTime("");
                                                        //作业时间
                                                        lids1101.setJobTime("");
                                                        String craneId = "";
                                                        String craneName = "";
                                                        queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList)) {
                                                            craneId = craneList.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName = craneList.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101.setCraneId(craneId);
                                                        //行车名称
                                                        lids1101.setCraneName(craneName);
                                                        //起始区域类型
                                                        lids1101.setStartAreaType("10");
                                                        //起始区域类型代码
                                                        lids1101.setStartAreaCode(lids0901.getAreaCode());
                                                        //起始区域类型名称
                                                        lids1101.setStartAreaName(lids0901.getAreaName());
                                                        //终到区域类型
                                                        lids1101.setEndAreaType("10");
                                                        //终到区域类型代码
                                                        lids1101.setEndAreaCode("");
                                                        //终到区域类型名称
                                                        lids1101.setEndAreaName("");
                                                        //状态
                                                        lids1101.setStatus("20");
                                                        //记录创建人
                                                        lids1101.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101.setArchiveFlag("");
                                                        //租户
                                                        lids1101.setTenantUser("");
                                                        //删除标记
                                                        lids1101.setDelFlag(0);
                                                        //ID
                                                        lids1101.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101);
                                                        LIDS1102 lids1102 = new LIDS1102();
                                                        String[] args1 = {applyId};
                                                        String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                        lids1102.setCraneOrderId(applyId);
                                                        lids1102.setSegNo(segNo);
                                                        lids1102.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102.setCraneOrderSubId(applySubId);
                                                        //状态
                                                        lids1102.setStatus("20");
                                                        //捆包号
                                                        lids1102.setPackId(lids0901.getPackId());
                                                        //标签号
                                                        lids1102.setLabelId(lids0901.getLabelId());
                                                        //净重
                                                        lids1102.setNetWeight(lids0901.getCraneOperationWeight());
                                                        //数量
                                                        lids1102.setQuantity(lids0901.getQuantity());
                                                        //记录创建人
                                                        lids1102.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102.setArchiveFlag("");
                                                        //租户
                                                        lids1102.setTenantUser("");
                                                        //删除标记
                                                        lids1102.setDelFlag(0);
                                                        //ID
                                                        lids1102.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102);
                                                    }

                                                }
                                            }
                                        }
                                        //生成生产上料的行车作业清单
                                        //判断原料库位所在区域与机组拆包区所在区域是否为同一跨
                                        HashMap queryMachineCodeMap = new HashMap();
                                        queryMachineCodeMap.put("segNo", segNo);
                                        queryMachineCodeMap.put("machineCode", hashMap.get("machineCode"));
                                        List<Map> machineCodeCrossArea = this.dao.query(LIDS0701.QUERY_MACHINE_CODE_CROSS_AREA, queryMachineCodeMap);
                                        if (CollectionUtils.isNotEmpty(machineCodeCrossArea)) {
                                            //拆包区跨区
                                            String unpackCrossArea = (String) machineCodeCrossArea.get(0).get("unpackCrossArea");
                                            if (unpackCrossArea.equals(locationList.get(0).getCrossArea())) {
                                                //库位跨区与拆包区跨区一致，生成跨区到拆包区的行车作业清单
                                                LIDS1101 lids1101 = new LIDS1101();
                                                String[] args = {segNo.substring(0, 2)};
                                                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                lids1101.setCraneOrderId(applyId);
                                                lids1101.setSegNo(segNo);
                                                lids1101.setUnitCode(segNo);
                                                //清单来源 30生产上料
                                                lids1101.setListSource("30");
                                                //依据凭单号（生产工单号）
                                                lids1101.setVoucherNum(processOrderId);
                                                //批次号
                                                lids1101.setBatchNumber(batchId);
                                                //顺序号
                                                SerialNumber++;
                                                lids1101.setSerialNumber(SerialNumber.toString());
                                                //机组代码
                                                lids1101.setMachineCode("");
                                                //机组名称
                                                lids1101.setMachineName("");
                                                //拆包区编号
                                                lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //拆包区名称
                                                lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //模具ID
                                                lids1101.setMouldId("");
                                                //模具名称
                                                lids1101.setMouldName("");
                                                //作业开始时间
                                                lids1101.setStartTime("");
                                                //作业结束时间
                                                lids1101.setEndTime("");
                                                //作业时间
                                                lids1101.setJobTime("");
                                                //根据库位找对应的跨区，查询当前跨区下的所有行车
                                                String craneId = "";
                                                String craneName = "";
                                                queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                queryMap.put("craneDuty", "10");
                                                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                if (CollectionUtils.isNotEmpty(craneList)) {
                                                    craneId = craneList.stream()
                                                            .map(LIDS0301::getCraneId)
                                                            .collect(Collectors.joining(","));
                                                    craneName = craneList.stream()
                                                            .map(LIDS0301::getCraneName)
                                                            .collect(Collectors.joining(","));
                                                }
                                                //行车编号
                                                lids1101.setCraneId(craneId);
                                                //行车名称
                                                lids1101.setCraneName(craneName);
                                                //起始区域类型
                                                lids1101.setStartAreaType("10");
                                                //起始区域类型代码
                                                lids1101.setStartAreaCode(packList.get(0).getAreaCode());
                                                //起始区域类型名称
                                                lids1101.setStartAreaName(packList.get(0).getAreaName());
                                                //终到区域类型
                                                lids1101.setEndAreaType("30");
                                                //终到区域类型代码
                                                lids1101.setEndAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //终到区域类型名称
                                                lids1101.setEndAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //状态
                                                lids1101.setStatus("20");
                                                //记录创建人
                                                lids1101.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1101.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1101.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1101.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1101.setArchiveFlag("");
                                                //租户
                                                lids1101.setTenantUser("");
                                                //删除标记
                                                lids1101.setDelFlag(0);
                                                //ID
                                                lids1101.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1101.INSERT, lids1101);
                                                LIDS1102 lids1102 = new LIDS1102();
                                                String[] args1 = {applyId};
                                                String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                lids1102.setCraneOrderId(applyId);
                                                lids1102.setSegNo(segNo);
                                                lids1102.setUnitCode(segNo);
                                                //行车作业清单子项
                                                lids1102.setCraneOrderSubId(applySubId);
                                                //状态
                                                lids1102.setStatus("20");
                                                //捆包号
                                                lids1102.setPackId(packId);
                                                //标签号
                                                lids1102.setLabelId(packList.get(0).getLabelId());
                                                //净重
                                                lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                //数量
                                                lids1102.setQuantity(packList.get(0).getQuantity());
                                                //记录创建人
                                                lids1102.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1102.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1102.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1102.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1102.setArchiveFlag("");
                                                //租户
                                                lids1102.setTenantUser("");
                                                //删除标记
                                                lids1102.setDelFlag(0);
                                                //ID
                                                lids1102.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1102.INSERT, lids1102);
                                            } else {
                                                //原料与拆包区所在跨区不为同一跨 需要生成 原料库位到过跨通道的行车作业清单和过跨通道到拆包区的行车作业清单
                                                //原料库位到过跨通道的行车作业清单
                                                LIDS1101 lids1101 = new LIDS1101();
                                                String[] args = {segNo.substring(0, 2)};
                                                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                lids1101.setCraneOrderId(applyId);
                                                lids1101.setSegNo(segNo);
                                                lids1101.setUnitCode(segNo);
                                                //清单来源 30生产上料
                                                lids1101.setListSource("30");
                                                //依据凭单号（生产工单号）
                                                lids1101.setVoucherNum(processOrderId);
                                                //批次号
                                                lids1101.setBatchNumber(batchId);
                                                //顺序号
                                                SerialNumber++;
                                                lids1101.setSerialNumber(SerialNumber.toString());
                                                //机组代码
                                                lids1101.setMachineCode("");
                                                //机组名称
                                                lids1101.setMachineName("");
                                                //拆包区编号
                                                lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //拆包区名称
                                                lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //模具ID
                                                lids1101.setMouldId("");
                                                //模具名称
                                                lids1101.setMouldName("");
                                                //作业开始时间
                                                lids1101.setStartTime("");
                                                //作业结束时间
                                                lids1101.setEndTime("");
                                                //作业时间
                                                lids1101.setJobTime("");
                                                //根据库位找对应的跨区，查询当前跨区下的所有行车
                                                String craneId = "";
                                                String craneName = "";
                                                queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                queryMap.put("craneDuty", "10");
                                                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                if (CollectionUtils.isNotEmpty(craneList)) {
                                                    craneId = craneList.stream()
                                                            .map(LIDS0301::getCraneId)
                                                            .collect(Collectors.joining(","));
                                                    craneName = craneList.stream()
                                                            .map(LIDS0301::getCraneName)
                                                            .collect(Collectors.joining(","));
                                                }
                                                //行车编号
                                                lids1101.setCraneId(craneId);
                                                //行车名称
                                                lids1101.setCraneName(craneName);
                                                //起始区域类型
                                                lids1101.setStartAreaType("10");
                                                //起始区域类型代码
                                                lids1101.setStartAreaCode(packList.get(0).getAreaCode());
                                                //起始区域类型名称
                                                lids1101.setStartAreaName(packList.get(0).getAreaName());
                                                //终到区域类型
                                                lids1101.setEndAreaType("20");
                                                //根据厂区、厂房、跨区编码查询过跨小车
                                                List<LIDS0401> transferCarIdList = this.dao.query(LIDS0401.QUERY_TRANSFER_CAR_ID, queryMap);
                                                if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                    //终到区域类型代码
                                                    lids1101.setEndAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                    //终到区域类型名称
                                                    lids1101.setEndAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                }
                                                //状态
                                                lids1101.setStatus("20");
                                                //记录创建人
                                                lids1101.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1101.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1101.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1101.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1101.setArchiveFlag("");
                                                //租户
                                                lids1101.setTenantUser("");
                                                //删除标记
                                                lids1101.setDelFlag(0);
                                                //ID
                                                lids1101.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1101.INSERT, lids1101);
                                                LIDS1102 lids1102 = new LIDS1102();
                                                String[] args1 = {applyId};
                                                String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                lids1102.setCraneOrderId(applyId);
                                                lids1102.setSegNo(segNo);
                                                lids1102.setUnitCode(segNo);
                                                //行车作业清单子项
                                                lids1102.setCraneOrderSubId(applySubId);
                                                //状态
                                                lids1102.setStatus("20");
                                                //捆包号
                                                lids1102.setPackId(packId);
                                                //标签号
                                                lids1102.setLabelId(packList.get(0).getLabelId());
                                                //净重
                                                lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                //数量
                                                lids1102.setQuantity(packList.get(0).getQuantity());
                                                //记录创建人
                                                lids1102.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1102.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1102.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1102.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1102.setArchiveFlag("");
                                                //租户
                                                lids1102.setTenantUser("");
                                                //删除标记
                                                lids1102.setDelFlag(0);
                                                //ID
                                                lids1102.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1102.INSERT, lids1102);
                                                //过跨通道到拆包区的行车作业清单
                                                LIDS1101 lids1101a = new LIDS1101();
                                                String applyId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                lids1101a.setCraneOrderId(applyId2);
                                                lids1101a.setSegNo(segNo);
                                                lids1101a.setUnitCode(segNo);
                                                //清单来源 30生产上料
                                                lids1101a.setListSource("30");
                                                //依据凭单号（生产工单号）
                                                lids1101a.setVoucherNum(processOrderId);
                                                //批次号
                                                lids1101a.setBatchNumber(batchId);
                                                //顺序号
                                                SerialNumber++;
                                                lids1101a.setSerialNumber(SerialNumber.toString());
                                                //机组代码
                                                lids1101a.setMachineCode("");
                                                //机组名称
                                                lids1101a.setMachineName("");
                                                //拆包区编号
                                                lids1101a.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //拆包区名称
                                                lids1101a.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //模具ID
                                                lids1101a.setMouldId("");
                                                //模具名称
                                                lids1101a.setMouldName("");
                                                //作业开始时间
                                                lids1101a.setStartTime("");
                                                //作业结束时间
                                                lids1101a.setEndTime("");
                                                //作业时间
                                                lids1101a.setJobTime("");
                                                //根据拆包区找对应的跨区，查询当前跨区下的所有行车
                                                String craneId1 = "";
                                                String craneName1 = "";
                                                queryMap.put("crossArea", unpackCrossArea);
                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                queryMap.put("craneDuty", "10");
                                                List<LIDS0301> craneList1 = this.dao.query(LIDS0301.QUERY, queryMap);
                                                if (CollectionUtils.isNotEmpty(craneList1)) {
                                                    craneId1 = craneList1.stream()
                                                            .map(LIDS0301::getCraneId)
                                                            .collect(Collectors.joining(","));
                                                    craneName1 = craneList1.stream()
                                                            .map(LIDS0301::getCraneName)
                                                            .collect(Collectors.joining(","));
                                                }
                                                //行车编号
                                                lids1101a.setCraneId(craneId1);
                                                //行车名称
                                                lids1101a.setCraneName(craneName1);
                                                //起始区域类型
                                                lids1101a.setStartAreaType("20");
                                                if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                    //终到区域类型代码
                                                    lids1101a.setStartAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                    //终到区域类型名称
                                                    lids1101a.setStartAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                }
                                                //终到区域类型
                                                lids1101a.setEndAreaType("30");
                                                //终到区域类型代码
                                                lids1101a.setEndAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //终到区域类型名称
                                                lids1101a.setEndAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //状态
                                                lids1101a.setStatus("20");
                                                //记录创建人
                                                lids1101a.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1101a.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1101a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1101a.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1101a.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1101a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1101a.setArchiveFlag("");
                                                //租户
                                                lids1101a.setTenantUser("");
                                                //删除标记
                                                lids1101a.setDelFlag(0);
                                                //ID
                                                lids1101a.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1101.INSERT, lids1101a);
                                                LIDS1102 lids1102a = new LIDS1102();
                                                String[] args2 = {applyId2};
                                                String applySubId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args2);
                                                lids1102a.setCraneOrderId(applyId2);
                                                lids1102a.setSegNo(segNo);
                                                lids1102a.setUnitCode(segNo);
                                                //行车作业清单子项
                                                lids1102a.setCraneOrderSubId(applySubId2);
                                                //状态
                                                lids1102a.setStatus("20");
                                                //捆包号
                                                lids1102a.setPackId(packId);
                                                //标签号
                                                lids1102a.setLabelId(packList.get(0).getLabelId());
                                                //净重
                                                lids1102a.setNetWeight(packList.get(0).getNetWeight());
                                                //数量
                                                lids1102a.setQuantity(packList.get(0).getQuantity());
                                                //记录创建人
                                                lids1102a.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1102a.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1102a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1102a.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1102a.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1102a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1102a.setArchiveFlag("");
                                                //租户
                                                lids1102a.setTenantUser("");
                                                //删除标记
                                                lids1102a.setDelFlag(0);
                                                //ID
                                                lids1102a.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1102.INSERT, lids1102a);
                                            }
                                            //判断拆包区与机组所在跨区是否为同一跨
                                            if (unpackCrossArea.equals(machineCodeCrossArea.get(0).get("crossArea"))) {
                                                //机组跨区与拆包区跨区一致，生成拆包区到机组上料区的行车作业清单
                                                LIDS1101 lids1101 = new LIDS1101();
                                                String[] args = {segNo.substring(0, 2)};
                                                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                lids1101.setCraneOrderId(applyId);
                                                lids1101.setSegNo(segNo);
                                                lids1101.setUnitCode(segNo);
                                                //清单来源 30生产上料
                                                lids1101.setListSource("30");
                                                //依据凭单号（生产工单号）
                                                lids1101.setVoucherNum(processOrderId);
                                                //批次号
                                                lids1101.setBatchNumber(batchId);
                                                //顺序号
                                                SerialNumber++;
                                                lids1101.setSerialNumber(SerialNumber.toString());
                                                //机组代码
                                                lids1101.setMachineCode((String) machineCodeCrossArea.get(0).get("machineCode"));
                                                //机组名称
                                                lids1101.setMachineName((String) machineCodeCrossArea.get(0).get("machineName"));
                                                //拆包区编号
                                                lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //拆包区名称
                                                lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //模具ID
                                                lids1101.setMouldId("");
                                                //模具名称
                                                lids1101.setMouldName("");
                                                //作业开始时间
                                                lids1101.setStartTime("");
                                                //作业结束时间
                                                lids1101.setEndTime("");
                                                //作业时间
                                                lids1101.setJobTime("");
                                                //根据拆包区找对应的跨区，查询当前跨区下的所有行车
                                                String craneId = "";
                                                String craneName = "";
                                                queryMap.put("crossArea", unpackCrossArea);
                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                queryMap.put("craneDuty", "10");
                                                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                if (CollectionUtils.isNotEmpty(craneList)) {
                                                    craneId = craneList.stream()
                                                            .map(LIDS0301::getCraneId)
                                                            .collect(Collectors.joining(","));
                                                    craneName = craneList.stream()
                                                            .map(LIDS0301::getCraneName)
                                                            .collect(Collectors.joining(","));
                                                }
                                                //行车编号
                                                lids1101.setCraneId(craneId);
                                                //行车名称
                                                lids1101.setCraneName(craneName);
                                                //起始区域类型 30拆包区
                                                lids1101.setStartAreaType("30");
                                                //起始区域类型代码
                                                lids1101.setStartAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //起始区域类型名称
                                                lids1101.setStartAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //终到区域类型 60 机组上料区
                                                lids1101.setEndAreaType("60");
                                                //终到区域类型代码
                                                lids1101.setEndAreaCode((String) machineCodeCrossArea.get(0).get("materialLoadingArea"));
                                                //终到区域类型名称
                                                lids1101.setEndAreaName((String) machineCodeCrossArea.get(0).get("materialLoadingAreaName"));
                                                //状态
                                                lids1101.setStatus("20");
                                                //记录创建人
                                                lids1101.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1101.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1101.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1101.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1101.setArchiveFlag("");
                                                //租户
                                                lids1101.setTenantUser("");
                                                //删除标记
                                                lids1101.setDelFlag(0);
                                                //ID
                                                lids1101.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1101.INSERT, lids1101);
                                                LIDS1102 lids1102 = new LIDS1102();
                                                String[] args1 = {applyId};
                                                String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                lids1102.setCraneOrderId(applyId);
                                                lids1102.setSegNo(segNo);
                                                lids1102.setUnitCode(segNo);
                                                //行车作业清单子项
                                                lids1102.setCraneOrderSubId(applySubId);
                                                //状态
                                                lids1102.setStatus("20");
                                                //捆包号
                                                lids1102.setPackId(packId);
                                                //标签号
                                                lids1102.setLabelId(packList.get(0).getLabelId());
                                                //净重
                                                lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                //数量
                                                lids1102.setQuantity(packList.get(0).getQuantity());
                                                //记录创建人
                                                lids1102.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1102.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1102.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1102.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1102.setArchiveFlag("");
                                                //租户
                                                lids1102.setTenantUser("");
                                                //删除标记
                                                lids1102.setDelFlag(0);
                                                //ID
                                                lids1102.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1102.INSERT, lids1102);
                                            } else {
                                                //机组跨区与拆包区跨区不一致，生成拆包区到过跨小车的行车作业清单，再生成过跨小车到机组上料区的行车作业清单
                                                //拆包区到过跨小车的行车作业清单
                                                LIDS1101 lids1101 = new LIDS1101();
                                                String[] args = {segNo.substring(0, 2)};
                                                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                lids1101.setCraneOrderId(applyId);
                                                lids1101.setSegNo(segNo);
                                                lids1101.setUnitCode(segNo);
                                                //清单来源 30生产上料
                                                lids1101.setListSource("30");
                                                //依据凭单号（生产工单号）
                                                lids1101.setVoucherNum(processOrderId);
                                                //批次号
                                                lids1101.setBatchNumber(batchId);
                                                //顺序号
                                                SerialNumber++;
                                                lids1101.setSerialNumber(SerialNumber.toString());
                                                //机组代码
                                                lids1101.setMachineCode("");
                                                //机组名称
                                                lids1101.setMachineName("");
                                                //拆包区编号
                                                lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //拆包区名称
                                                lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //模具ID
                                                lids1101.setMouldId("");
                                                //模具名称
                                                lids1101.setMouldName("");
                                                //作业开始时间
                                                lids1101.setStartTime("");
                                                //作业结束时间
                                                lids1101.setEndTime("");
                                                //作业时间
                                                lids1101.setJobTime("");
                                                //根据拆包区找对应的跨区，查询当前跨区下的所有行车
                                                String craneId = "";
                                                String craneName = "";
                                                queryMap.put("crossArea", unpackCrossArea);
                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                queryMap.put("craneDuty", "10");
                                                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                if (CollectionUtils.isNotEmpty(craneList)) {
                                                    craneId = craneList.stream()
                                                            .map(LIDS0301::getCraneId)
                                                            .collect(Collectors.joining(","));
                                                    craneName = craneList.stream()
                                                            .map(LIDS0301::getCraneName)
                                                            .collect(Collectors.joining(","));
                                                }
                                                //行车编号
                                                lids1101.setCraneId(craneId);
                                                //行车名称
                                                lids1101.setCraneName(craneName);
                                                //起始区域类型 30拆包区
                                                lids1101.setStartAreaType("30");
                                                //起始区域类型代码
                                                lids1101.setStartAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                //起始区域类型名称
                                                lids1101.setStartAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                //终到区域类型 60 机组上料区
                                                lids1101.setEndAreaType("20");
                                                //根据厂区、厂房、跨区编码查询过跨小车
                                                List<LIDS0401> transferCarIdList = this.dao.query(LIDS0401.QUERY_TRANSFER_CAR_ID, queryMap);
                                                if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                    //终到区域类型代码
                                                    lids1101.setEndAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                    //终到区域类型名称
                                                    lids1101.setEndAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                }
                                                //状态
                                                lids1101.setStatus("20");
                                                //记录创建人
                                                lids1101.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1101.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1101.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1101.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1101.setArchiveFlag("");
                                                //租户
                                                lids1101.setTenantUser("");
                                                //删除标记
                                                lids1101.setDelFlag(0);
                                                //ID
                                                lids1101.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1101.INSERT, lids1101);
                                                LIDS1102 lids1102 = new LIDS1102();
                                                String[] args1 = {applyId};
                                                String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                lids1102.setCraneOrderId(applyId);
                                                lids1102.setSegNo(segNo);
                                                lids1102.setUnitCode(segNo);
                                                //行车作业清单子项
                                                lids1102.setCraneOrderSubId(applySubId);
                                                //状态
                                                lids1102.setStatus("20");
                                                //捆包号
                                                lids1102.setPackId(packId);
                                                //标签号
                                                lids1102.setLabelId(packList.get(0).getLabelId());
                                                //净重
                                                lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                //数量
                                                lids1102.setQuantity(packList.get(0).getQuantity());
                                                //记录创建人
                                                lids1102.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1102.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1102.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1102.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1102.setArchiveFlag("");
                                                //租户
                                                lids1102.setTenantUser("");
                                                //删除标记
                                                lids1102.setDelFlag(0);
                                                //ID
                                                lids1102.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1102.INSERT, lids1102);
                                                //过跨通道到机组上料区的行车作业清单
                                                LIDS1101 lids1101a = new LIDS1101();
                                                String applyId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                lids1101a.setCraneOrderId(applyId2);
                                                lids1101a.setSegNo(segNo);
                                                lids1101a.setUnitCode(segNo);
                                                //清单来源 30生产上料
                                                lids1101a.setListSource("30");
                                                //依据凭单号（生产工单号）
                                                lids1101a.setVoucherNum(processOrderId);
                                                //批次号
                                                lids1101a.setBatchNumber(batchId);
                                                //顺序号
                                                SerialNumber++;
                                                lids1101a.setSerialNumber(SerialNumber.toString());
                                                lids1101a.setMachineCode((String) machineCodeCrossArea.get(0).get("machineCode"));
                                                //机组名称
                                                lids1101a.setMachineName((String) machineCodeCrossArea.get(0).get("machineName"));
                                                //拆包区编号
                                                lids1101a.setUnpackAreaId("");
                                                //拆包区名称
                                                lids1101a.setUnpackAreaName("");
                                                //模具ID
                                                lids1101a.setMouldId("");
                                                //模具名称
                                                lids1101a.setMouldName("");
                                                //作业开始时间
                                                lids1101a.setStartTime("");
                                                //作业结束时间
                                                lids1101a.setEndTime("");
                                                //作业时间
                                                lids1101a.setJobTime("");
                                                //根据机组找对应的跨区，查询当前跨区下的所有行车
                                                String craneId1 = "";
                                                String craneName1 = "";
                                                queryMap.put("crossArea", machineCodeCrossArea.get(0).get("crossArea"));
                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                queryMap.put("craneDuty", "10");
                                                List<LIDS0301> craneList1 = this.dao.query(LIDS0301.QUERY, queryMap);
                                                if (CollectionUtils.isNotEmpty(craneList1)) {
                                                    craneId1 = craneList1.stream()
                                                            .map(LIDS0301::getCraneId)
                                                            .collect(Collectors.joining(","));
                                                    craneName1 = craneList1.stream()
                                                            .map(LIDS0301::getCraneName)
                                                            .collect(Collectors.joining(","));
                                                }
                                                //行车编号
                                                lids1101a.setCraneId(craneId1);
                                                //行车名称
                                                lids1101a.setCraneName(craneName1);
                                                //起始区域类型
                                                lids1101a.setStartAreaType("20");
                                                if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                    //起始区域类型代码
                                                    lids1101a.setStartAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                    //起始区域类型名称
                                                    lids1101a.setStartAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                }
                                                //终到区域类型
                                                lids1101a.setEndAreaType("60");
                                                //终到区域类型代码
                                                lids1101a.setEndAreaCode((String) machineCodeCrossArea.get(0).get("materialLoadingArea"));
                                                //终到区域类型名称
                                                lids1101a.setEndAreaName((String) machineCodeCrossArea.get(0).get("materialLoadingAreaName"));
                                                //状态
                                                lids1101a.setStatus("20");
                                                //记录创建人
                                                lids1101a.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1101a.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1101a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1101a.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1101a.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1101a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1101a.setArchiveFlag("");
                                                //租户
                                                lids1101a.setTenantUser("");
                                                //删除标记
                                                lids1101a.setDelFlag(0);
                                                //ID
                                                lids1101a.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1101.INSERT, lids1101a);
                                                LIDS1102 lids1102a = new LIDS1102();
                                                String[] args2 = {applyId2};
                                                String applySubId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args2);
                                                lids1102a.setCraneOrderId(applyId2);
                                                lids1102a.setSegNo(segNo);
                                                lids1102a.setUnitCode(segNo);
                                                //行车作业清单子项
                                                lids1102a.setCraneOrderSubId(applySubId2);
                                                //状态
                                                lids1102a.setStatus("20");
                                                //捆包号
                                                lids1102a.setPackId(packId);
                                                //标签号
                                                lids1102a.setLabelId(packList.get(0).getLabelId());
                                                //净重
                                                lids1102a.setNetWeight(packList.get(0).getNetWeight());
                                                //数量
                                                lids1102a.setQuantity(packList.get(0).getQuantity());
                                                //记录创建人
                                                lids1102a.setRecCreator("system");
                                                //记录创建人姓名
                                                lids1102a.setRecCreatorName("system");
                                                //记录创建时间
                                                lids1102a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                //记录修改人
                                                lids1102a.setRecRevisor("system");
                                                //记录修改人姓名
                                                lids1102a.setRecRevisorName("system");
                                                //记录修改时间
                                                lids1102a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                //归档标记
                                                lids1102a.setArchiveFlag("");
                                                //租户
                                                lids1102a.setTenantUser("");
                                                //删除标记
                                                lids1102a.setDelFlag(0);
                                                //ID
                                                lids1102a.setUuid(UUIDUtils.getUUID());
                                                dao.insert(LIDS1102.INSERT, lids1102a);
                                            }
                                        }

                                    }
                                }
                            }

                        }
                    }
                }
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("生成成功");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("生成失败" + ex.getMessage());
            return outInfo;
        }
        return outInfo;
    }
    /**
     * 大屏看板工单信息查询
     * Service:S_VI_PM_0701
     */
    public EiInfo queryOrderInformation(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1020");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }

    /**
     * 根据传入捆包查询套裁信息
     * Service:S_VI_PM_0701
     */
    public EiInfo queryCuttingInfoByPackId(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1024");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }
    /**
     * 根据传入物料号查询单片重
     * Service:S_VI_PM_0702
     */
    public EiInfo queryPieceWt(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1025");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }
    /**
     * 大屏看板工单查询出当班计划量、完成量、完成率、当班原料已加工卷数
     * Service:S_VI_PM_0703
     */
    public EiInfo queryShiftProductionData(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1026");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }

    /**
     * 根据客户代码和系统账套查询每个月的生产实绩
     * Service:S_VI_PM_0704
     */
    public EiInfo queryMonthlyProductionPerformance(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1027");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }

    /**
     * 根据客户代码和系统账套查询6周内的排产计划量,显示时间为周日
     * Service:S_VI_PM_0705
     */
    public EiInfo sixWeekPlanQuantity(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1028");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }

    /**
     * 根据客户代码和系统账套查询6周内的生产实绩
     * Service:S_VI_PM_0706
     */
    public EiInfo sixWeeksOfActualPerformance(EiInfo inInfo) {
        inInfo.set("serviceId", "S_VI_PM_1029");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        return outInfo;
    }

}
