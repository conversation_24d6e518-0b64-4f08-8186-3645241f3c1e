@charset "utf-8";
dl {
    margin: 0;
}

dd {
    margin: 0;
}

dt {
    margin: 0;
}

ul,
li {
    margin: 0;
    padding: 0;
}

body {
    margin: 0;
    padding: 0;
    color: #cccccc;
    font-family: "微软雅黑";
}

#bg {
    width: 100%;
    height: 100vh;
    background-color: #324E73;
}

.top{
    width: 100%;
    height: 11%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.header {
    font-size: 36px;
    color: #FFF;
    text-align: center;
    letter-spacing: 12px;
    padding: 0;
    margin: 0;
}

.chang-arr {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    color: #FFF;
    font-size: 16px;
}

.status-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
}

.status-info div {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.completed span {
    color: #75BD42;  /* 绿色 */
    font-size: 18px;
    font-weight: bold;
}

.remaining span {
    color: #FF0000;  /* 红色 */
    font-size: 18px;
    font-weight: bold;
}

.status-details {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

.status-details div {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-top: 5px;
}

.status-details i {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 5px;
}

.working{
    color: black;
    padding: 2px 6px;
    font-size: 15px;
    background: #75BD42;
}

.pending{
    color: black;
    padding: 2px 6px;
    font-size: 15px;
    background: #FBD603;
}

.table-head {
    border-collapse: separate;
    border-spacing: 5px;
    width: 100%;
    height: 89%;
    table-layout: fixed;
}

.external-tr {
    height: 120px;
}

.vehicles-admitted-sub-div {
    background-color: #033;
    min-width: 25%;
    vertical-align: top;
}

.car-num-head-div {
    display: flex;
    font-weight: bold;
    text-align: center;
    background-color: #0070C0;
    color: #FFFFFF;
    font-size: 20px;
    align-items: center;          /* 垂直居中 */
    justify-content: space-between; /* 把最左和最右分开 */
    height: 35px;                /* 固定头部高度 */
    min-height: 35px;            /* 最小高度 */
}

.not-car-num-head-div {
    font-weight: bold;
    text-align: center;
    background-color: #0070C0;
    color: #FFFFFF;
    font-size: 20px;
    height: 35px;
    line-height: 35px;
}

/* 中间文字占满可用宽度，并居中 */
.center-text {
    flex: 1;                /* 让中间部分自动撑开，达到中间对齐 */
    text-align: center;
}

.table-content {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0px 5px;
    padding: 0px 5px;
}

.table-content tr td{
    width: calc(100% / 3);
    text-align: center;
}

.table-content tr td:first-child {
    border-top-left-radius: 4px; /* 左上角圆角 */
    border-bottom-left-radius: 4px; /* 左下角圆角 */
}

.table-content tr td:last-child {
    border-top-right-radius: 4px; /* 右上角圆角 */
    border-bottom-right-radius: 4px; /* 右下角圆角 */
}

.jiaohao tr{
    color: black;
    padding: 2px 6px;
    font-size: 15px;
    background: #FBD603;
}

.logo-baosight {
    width: 272px;
    height: 60px;
    background: url(../../img/宝钢logo.svg) no-repeat;
    margin: 15px 0 0 0;
    float: left;
}



.div-working-tag, .div-pending-tag {
    border-radius: 10px; /* 圆角 */
    padding: 6px 0px; /* 内边距 */
    line-height: 18px; /* 行高 */
    text-align: center; /* 文本居中 */
    margin: 5px 0px; /* 上下和左右间距 */
    font-size: 15px; /* 字体大小 */
    display: inline-block; /* 内联块 */
    color: black;
    width: 100%;
}

.div-working-tag {
    background: #75BD42;
}

.div-pending-tag {
    background: #FBD603;
}

.data-table {
    border-collapse: separate;
    border-spacing: 5px;
    width: 100%;
    table-layout: fixed;
}

.dengdai-scroll-container {
    overflow: hidden;
    position: relative;
}

.jiaohao-scroll-container {
    overflow: hidden;
    position: relative;
}

.weiyuyue-scroll-container {
    overflow: hidden;
    position: relative;
}

.other-qita-scroll-container {
    overflow: hidden;
    position: relative;
}


.dengdai td {
    position: relative;
    /*padding: 10px;*/
}

.index {
    position: absolute;
    top: -6px;
    right: -6px;
    font-size: 12px;
    color: #fff;  /* 白色字体 */
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 18px;
    width: 18px;
    border-radius: 50%; /* 圆形背景 */
    background-color: #007bff; /* 蓝色背景，可以根据需求调整 */
}

/* 小三角的样式 */
.dengdai td .div-pending-tag {
    position: relative;
}

.dengdai td .div-pending-tag::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background-color: #007bff;  /* 小三角背景色与下标圆形相同 */
    transform: rotate(45deg);  /* 旋转45度，形成三角形 */
    z-index: -1; /* 确保三角形位于下标圆形的背景下 */
}


/** 叫号序号样式 **/
.weiyuyue td {
    position: relative;
    /*padding: 10px;*/
}

/* 小三角的样式 */
.weiyuyue td .div-pending-tag {
    position: relative;
}

.weiyuyue td .div-pending-tag::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background-color: #007bff;  /* 小三角背景色与下标圆形相同 */
    transform: rotate(45deg);  /* 旋转45度，形成三角形 */
    z-index: -1; /* 确保三角形位于下标圆形的背景下 */
}

