/**
 * Generate time : 2024-11-20 8:50:59
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.imom.common.utils.ValidationUtils;
import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Tvgdm0701
 */
public class Tvgdm0701 extends DaoEPBase {

    private String faultId = " ";        /* 故障编号*/
    private String faultStatus = " ";        /* 故障状态*/
    @NotBlank(message = "设备信息不能为空", groups = {ValidationUtils.Group1.class})
    private String eArchivesNo = " ";        /* 设备档案编号*/
    @NotBlank(message = "设备信息不能为空", groups = {ValidationUtils.Group1.class})
    private String equipmentName = " ";        /* 设备名称*/
    @NotBlank(message = "分部设备信息不能为空", groups = {ValidationUtils.Group1.class})
    private String deviceCode = " ";        /* 分部设备代码*/
    @NotBlank(message = "分部设备信息不能为空", groups = {ValidationUtils.Group1.class})
    private String deviceName = " ";        /* 分部设备名称*/
    @NotBlank(message = "故障时间不能为空", groups = {ValidationUtils.Group1.class})
    private String faultStartTime = " ";        /* 故障开始时间*/
    private String voucherNum = " ";        /* 依据凭单*/
    private String faultSource = " ";        /* 故障来源*/
    @NotBlank(message = "故障类型不能为空", groups = {ValidationUtils.Group1.class})
    private String faultType = " ";        /* 故障类型*/
    @NotBlank(message = "故障级别不能为空", groups = {ValidationUtils.Group1.class})
    private String faultLevel = " ";        /* 故障级别*/
    @NotBlank(message = "故障描述不能为空", groups = {ValidationUtils.Group1.class})
    private String faultDesc = " ";        /* 故障描述*/
    @NotBlank(message = "故障结束时间不能为空", groups = {ValidationUtils.Group2.class})
    private String faultEndTime = " ";        /* 故障结束时间*/
    @NotBlank(message = "处理措施不能为空", groups = {ValidationUtils.Group2.class})
    private String handleMeasures = " ";        /* 处理措施*/
    private String causeAnalysis = " ";        /* 原因分析*/
    private String preventMeasure = " ";        /* 预防措施*/
    @NotBlank(message = "是否检修不能为空", groups = {ValidationUtils.Group2.class})
    private String isOverhaul = "0";        /* 是否检修*/
    @NotBlank(message = "施工类别不能为空", groups = {ValidationUtils.Group3.class})
    private String overhaulType = " ";        /* 检修类别*/
    private String outsourcingContactId = " ";        /* 委外联络单号*/
    private String securityMeasures = " ";        /* 安全措施*/
    private String acceptanceCriteria = " ";        /* 检修验收标准*/
    private String overhaulImplementDate = " ";        /* 检修实施日期*/
    @NotNull(message = "检修人数不能为空", groups = {ValidationUtils.Group3.class})
    @Min(value = 1, message = "检修人数必须大于0", groups = {ValidationUtils.Group3.class})
    private BigDecimal actualOverhaulNumber = new BigDecimal("0");        /* 实际检修人数*/
    @NotNull(message = "检修时间不能为空", groups = {ValidationUtils.Group3.class})
    @DecimalMin(value = "0", inclusive = false, message = "检修时间必须大于0", groups = {ValidationUtils.Group3.class})
    private BigDecimal actualOverhaulTime = new BigDecimal("0");        /* 实际检修时间*/
    private String implementManId = " ";        /* 实施人*/
    private String implementManName = " ";        /* 实施人姓名*/
    private String isComplete = " ";        /* 是否完成*/
    private String overhaulLegacyProject = " ";        /* 遗留检修项目*/
    private String isConformStandard = " ";        /* 是否符合标准*/
    private String relevantMeasures = " ";        /* 相关措施*/
    @NotBlank(message = "是否动火不能为空", groups = {ValidationUtils.Group3.class})
    private String isHot = " ";        /* 是否动火*/
    private String hotCardId = " ";        /* 动火证编号*/
    private String offlinePartsGone = " ";        /* 下线零件去向*/
    private String overhaulSuggestions = " ";        /* 问题及建议*/
    @NotBlank(message = "检修总结不能为空", groups = {ValidationUtils.Group3.class})
    private String overhaulSummarize = " ";        /* 检修总结*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("faultId");
        eiColumn.setDescName("故障编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultStatus");
        eiColumn.setDescName("故障状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultStartTime");
        eiColumn.setDescName("故障开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultEndTime");
        eiColumn.setDescName("故障结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultSource");
        eiColumn.setDescName("故障来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultType");
        eiColumn.setDescName("故障类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultLevel");
        eiColumn.setDescName("故障级别");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultDesc");
        eiColumn.setDescName("故障描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handleMeasures");
        eiColumn.setDescName("处理措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("causeAnalysis");
        eiColumn.setDescName("原因分析");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("preventMeasure");
        eiColumn.setDescName("预防措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isOverhaul");
        eiColumn.setDescName("是否检修");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulType");
        eiColumn.setDescName("检修类别");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outsourcingContactId");
        eiColumn.setDescName("委外联络单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("securityMeasures");
        eiColumn.setDescName("安全措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("acceptanceCriteria");
        eiColumn.setDescName("检修验收标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulImplementDate");
        eiColumn.setDescName("检修实施日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualOverhaulNumber");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("实际检修人数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualOverhaulTime");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("实际检修时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("implementManId");
        eiColumn.setDescName("实施人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("implementManName");
        eiColumn.setDescName("实施人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isComplete");
        eiColumn.setDescName("是否完成");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulLegacyProject");
        eiColumn.setDescName("遗留检修项目");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isConformStandard");
        eiColumn.setDescName("是否符合标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("relevantMeasures");
        eiColumn.setDescName("相关措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isHot");
        eiColumn.setDescName("是否动火");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hotCardId");
        eiColumn.setDescName("动火证编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("offlinePartsGone");
        eiColumn.setDescName("下线零件去向");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulSuggestions");
        eiColumn.setDescName("问题及建议");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulSummarize");
        eiColumn.setDescName("检修总结");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm0701() {
        initMetaData();
    }

    /**
     * get the faultId - 故障编号
     *
     * @return the faultId
     */
    public String getFaultId() {
        return this.faultId;
    }

    /**
     * set the faultId - 故障编号
     */
    public void setFaultId(String faultId) {
        this.faultId = faultId;
    }

    /**
     * get the faultStatus - 故障状态
     *
     * @return the faultStatus
     */
    public String getFaultStatus() {
        return this.faultStatus;
    }

    /**
     * set the faultStatus - 故障状态
     */
    public void setFaultStatus(String faultStatus) {
        this.faultStatus = faultStatus;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the faultStartTime - 故障开始时间
     *
     * @return the faultStartTime
     */
    public String getFaultStartTime() {
        return this.faultStartTime;
    }

    /**
     * set the faultStartTime - 故障开始时间
     */
    public void setFaultStartTime(String faultStartTime) {
        this.faultStartTime = faultStartTime;
    }

    /**
     * get the faultEndTime - 故障结束时间
     *
     * @return the faultEndTime
     */
    public String getFaultEndTime() {
        return this.faultEndTime;
    }

    /**
     * set the faultEndTime - 故障结束时间
     */
    public void setFaultEndTime(String faultEndTime) {
        this.faultEndTime = faultEndTime;
    }

    /**
     * get the voucherNum - 依据凭单
     *
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 依据凭单
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the faultSource - 故障来源
     *
     * @return the faultSource
     */
    public String getFaultSource() {
        return this.faultSource;
    }

    /**
     * set the faultSource - 故障来源
     */
    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    /**
     * get the faultType - 故障类型
     *
     * @return the faultType
     */
    public String getFaultType() {
        return this.faultType;
    }

    /**
     * set the faultType - 故障类型
     */
    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }

    /**
     * get the faultLevel - 故障级别
     *
     * @return the faultLevel
     */
    public String getFaultLevel() {
        return this.faultLevel;
    }

    /**
     * set the faultLevel - 故障级别
     */
    public void setFaultLevel(String faultLevel) {
        this.faultLevel = faultLevel;
    }

    /**
     * get the faultDesc - 故障描述
     *
     * @return the faultDesc
     */
    public String getFaultDesc() {
        return this.faultDesc;
    }

    /**
     * set the faultDesc - 故障描述
     */
    public void setFaultDesc(String faultDesc) {
        this.faultDesc = faultDesc;
    }

    /**
     * get the handleMeasures - 处理措施
     *
     * @return the handleMeasures
     */
    public String getHandleMeasures() {
        return this.handleMeasures;
    }

    /**
     * set the handleMeasures - 处理措施
     */
    public void setHandleMeasures(String handleMeasures) {
        this.handleMeasures = handleMeasures;
    }

    /**
     * get the causeAnalysis - 原因分析
     *
     * @return the causeAnalysis
     */
    public String getCauseAnalysis() {
        return this.causeAnalysis;
    }

    /**
     * set the causeAnalysis - 原因分析
     */
    public void setCauseAnalysis(String causeAnalysis) {
        this.causeAnalysis = causeAnalysis;
    }

    /**
     * get the preventMeasure - 预防措施
     *
     * @return the preventMeasure
     */
    public String getPreventMeasure() {
        return this.preventMeasure;
    }

    /**
     * set the preventMeasure - 预防措施
     */
    public void setPreventMeasure(String preventMeasure) {
        this.preventMeasure = preventMeasure;
    }

    /**
     * get the isOverhaul - 是否检修
     *
     * @return the isOverhaul
     */
    public String getIsOverhaul() {
        return this.isOverhaul;
    }

    /**
     * set the isOverhaul - 是否检修
     */
    public void setIsOverhaul(String isOverhaul) {
        this.isOverhaul = isOverhaul;
    }

    /**
     * get the overhaulType - 检修类别
     *
     * @return the overhaulType
     */
    public String getOverhaulType() {
        return this.overhaulType;
    }

    /**
     * set the overhaulType - 检修类别
     */
    public void setOverhaulType(String overhaulType) {
        this.overhaulType = overhaulType;
    }

    /**
     * get the outsourcingContactId - 委外联络单号
     *
     * @return the outsourcingContactId
     */
    public String getOutsourcingContactId() {
        return this.outsourcingContactId;
    }

    /**
     * set the outsourcingContactId - 委外联络单号
     */
    public void setOutsourcingContactId(String outsourcingContactId) {
        this.outsourcingContactId = outsourcingContactId;
    }

    /**
     * get the securityMeasures - 安全措施
     *
     * @return the securityMeasures
     */
    public String getSecurityMeasures() {
        return this.securityMeasures;
    }

    /**
     * set the securityMeasures - 安全措施
     */
    public void setSecurityMeasures(String securityMeasures) {
        this.securityMeasures = securityMeasures;
    }

    /**
     * get the acceptanceCriteria - 检修验收标准
     *
     * @return the acceptanceCriteria
     */
    public String getAcceptanceCriteria() {
        return this.acceptanceCriteria;
    }

    /**
     * set the acceptanceCriteria - 检修验收标准
     */
    public void setAcceptanceCriteria(String acceptanceCriteria) {
        this.acceptanceCriteria = acceptanceCriteria;
    }

    /**
     * get the overhaulImplementDate - 检修实施日期
     *
     * @return the overhaulImplementDate
     */
    public String getOverhaulImplementDate() {
        return this.overhaulImplementDate;
    }

    /**
     * set the overhaulImplementDate - 检修实施日期
     */
    public void setOverhaulImplementDate(String overhaulImplementDate) {
        this.overhaulImplementDate = overhaulImplementDate;
    }

    /**
     * get the actualOverhaulNumber - 实际检修人数
     *
     * @return the actualOverhaulNumber
     */
    public BigDecimal getActualOverhaulNumber() {
        return this.actualOverhaulNumber;
    }

    /**
     * set the actualOverhaulNumber - 实际检修人数
     */
    public void setActualOverhaulNumber(BigDecimal actualOverhaulNumber) {
        this.actualOverhaulNumber = actualOverhaulNumber;
    }

    /**
     * get the actualOverhaulTime - 实际检修时间
     *
     * @return the actualOverhaulTime
     */
    public BigDecimal getActualOverhaulTime() {
        return this.actualOverhaulTime;
    }

    /**
     * set the actualOverhaulTime - 实际检修时间
     */
    public void setActualOverhaulTime(BigDecimal actualOverhaulTime) {
        this.actualOverhaulTime = actualOverhaulTime;
    }

    /**
     * get the implementManId - 实施人
     *
     * @return the implementManId
     */
    public String getImplementManId() {
        return this.implementManId;
    }

    /**
     * set the implementManId - 实施人
     */
    public void setImplementManId(String implementManId) {
        this.implementManId = implementManId;
    }

    /**
     * get the implementManName - 实施人姓名
     *
     * @return the implementManName
     */
    public String getImplementManName() {
        return this.implementManName;
    }

    /**
     * set the implementManName - 实施人姓名
     */
    public void setImplementManName(String implementManName) {
        this.implementManName = implementManName;
    }

    /**
     * get the isComplete - 是否完成
     *
     * @return the isComplete
     */
    public String getIsComplete() {
        return this.isComplete;
    }

    /**
     * set the isComplete - 是否完成
     */
    public void setIsComplete(String isComplete) {
        this.isComplete = isComplete;
    }

    /**
     * get the overhaulLegacyProject - 遗留检修项目
     *
     * @return the overhaulLegacyProject
     */
    public String getOverhaulLegacyProject() {
        return this.overhaulLegacyProject;
    }

    /**
     * set the overhaulLegacyProject - 遗留检修项目
     */
    public void setOverhaulLegacyProject(String overhaulLegacyProject) {
        this.overhaulLegacyProject = overhaulLegacyProject;
    }

    /**
     * get the isConformStandard - 是否符合标准
     *
     * @return the isConformStandard
     */
    public String getIsConformStandard() {
        return this.isConformStandard;
    }

    /**
     * set the isConformStandard - 是否符合标准
     */
    public void setIsConformStandard(String isConformStandard) {
        this.isConformStandard = isConformStandard;
    }

    /**
     * get the relevantMeasures - 相关措施
     *
     * @return the relevantMeasures
     */
    public String getRelevantMeasures() {
        return this.relevantMeasures;
    }

    /**
     * set the relevantMeasures - 相关措施
     */
    public void setRelevantMeasures(String relevantMeasures) {
        this.relevantMeasures = relevantMeasures;
    }

    /**
     * get the isHot - 是否动火
     *
     * @return the isHot
     */
    public String getIsHot() {
        return this.isHot;
    }

    /**
     * set the isHot - 是否动火
     */
    public void setIsHot(String isHot) {
        this.isHot = isHot;
    }

    /**
     * get the hotCardId - 动火证编号
     *
     * @return the hotCardId
     */
    public String getHotCardId() {
        return this.hotCardId;
    }

    /**
     * set the hotCardId - 动火证编号
     */
    public void setHotCardId(String hotCardId) {
        this.hotCardId = hotCardId;
    }

    /**
     * get the offlinePartsGone - 下线零件去向
     *
     * @return the offlinePartsGone
     */
    public String getOfflinePartsGone() {
        return this.offlinePartsGone;
    }

    /**
     * set the offlinePartsGone - 下线零件去向
     */
    public void setOfflinePartsGone(String offlinePartsGone) {
        this.offlinePartsGone = offlinePartsGone;
    }

    /**
     * get the overhaulSuggestions - 问题及建议
     *
     * @return the overhaulSuggestions
     */
    public String getOverhaulSuggestions() {
        return this.overhaulSuggestions;
    }

    /**
     * set the overhaulSuggestions - 问题及建议
     */
    public void setOverhaulSuggestions(String overhaulSuggestions) {
        this.overhaulSuggestions = overhaulSuggestions;
    }

    /**
     * get the overhaulSummarize - 检修总结
     *
     * @return the overhaulSummarize
     */
    public String getOverhaulSummarize() {
        return this.overhaulSummarize;
    }

    /**
     * set the overhaulSummarize - 检修总结
     */
    public void setOverhaulSummarize(String overhaulSummarize) {
        this.overhaulSummarize = overhaulSummarize;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the deviceCode - 分部设备代码
     *
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     *
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setFaultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultId")), faultId));
        setFaultStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultStatus")), faultStatus));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setFaultStartTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultStartTime")), faultStartTime));
        setFaultEndTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultEndTime")), faultEndTime));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setFaultSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultSource")), faultSource));
        setFaultType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultType")), faultType));
        setFaultLevel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultLevel")), faultLevel));
        setFaultDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultDesc")), faultDesc));
        setHandleMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handleMeasures")), handleMeasures));
        setCauseAnalysis(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("causeAnalysis")), causeAnalysis));
        setPreventMeasure(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("preventMeasure")), preventMeasure));
        setIsOverhaul(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isOverhaul")), isOverhaul));
        setOverhaulType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulType")), overhaulType));
        setOutsourcingContactId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outsourcingContactId")), outsourcingContactId));
        setSecurityMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("securityMeasures")), securityMeasures));
        setAcceptanceCriteria(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("acceptanceCriteria")), acceptanceCriteria));
        setOverhaulImplementDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulImplementDate")), overhaulImplementDate));
        setActualOverhaulNumber(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actualOverhaulNumber")), actualOverhaulNumber));
        setActualOverhaulTime(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actualOverhaulTime")), actualOverhaulTime));
        setImplementManId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("implementManId")), implementManId));
        setImplementManName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("implementManName")), implementManName));
        setIsComplete(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isComplete")), isComplete));
        setOverhaulLegacyProject(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulLegacyProject")), overhaulLegacyProject));
        setIsConformStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isConformStandard")), isConformStandard));
        setRelevantMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevantMeasures")), relevantMeasures));
        setIsHot(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isHot")), isHot));
        setHotCardId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hotCardId")), hotCardId));
        setOfflinePartsGone(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("offlinePartsGone")), offlinePartsGone));
        setOverhaulSuggestions(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulSuggestions")), overhaulSuggestions));
        setOverhaulSummarize(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulSummarize")), overhaulSummarize));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("faultId", StringUtils.toString(faultId, eiMetadata.getMeta("faultId")));
        map.put("faultStatus", StringUtils.toString(faultStatus, eiMetadata.getMeta("faultStatus")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("faultStartTime", StringUtils.toString(faultStartTime, eiMetadata.getMeta("faultStartTime")));
        map.put("faultEndTime", StringUtils.toString(faultEndTime, eiMetadata.getMeta("faultEndTime")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("faultSource", StringUtils.toString(faultSource, eiMetadata.getMeta("faultSource")));
        map.put("faultType", StringUtils.toString(faultType, eiMetadata.getMeta("faultType")));
        map.put("faultLevel", StringUtils.toString(faultLevel, eiMetadata.getMeta("faultLevel")));
        map.put("faultDesc", StringUtils.toString(faultDesc, eiMetadata.getMeta("faultDesc")));
        map.put("handleMeasures", StringUtils.toString(handleMeasures, eiMetadata.getMeta("handleMeasures")));
        map.put("causeAnalysis", StringUtils.toString(causeAnalysis, eiMetadata.getMeta("causeAnalysis")));
        map.put("preventMeasure", StringUtils.toString(preventMeasure, eiMetadata.getMeta("preventMeasure")));
        map.put("isOverhaul", StringUtils.toString(isOverhaul, eiMetadata.getMeta("isOverhaul")));
        map.put("overhaulType", StringUtils.toString(overhaulType, eiMetadata.getMeta("overhaulType")));
        map.put("outsourcingContactId", StringUtils.toString(outsourcingContactId, eiMetadata.getMeta("outsourcingContactId")));
        map.put("securityMeasures", StringUtils.toString(securityMeasures, eiMetadata.getMeta("securityMeasures")));
        map.put("acceptanceCriteria", StringUtils.toString(acceptanceCriteria, eiMetadata.getMeta("acceptanceCriteria")));
        map.put("overhaulImplementDate", StringUtils.toString(overhaulImplementDate, eiMetadata.getMeta("overhaulImplementDate")));
        map.put("actualOverhaulNumber", StringUtils.toString(actualOverhaulNumber, eiMetadata.getMeta("actualOverhaulNumber")));
        map.put("actualOverhaulTime", StringUtils.toString(actualOverhaulTime, eiMetadata.getMeta("actualOverhaulTime")));
        map.put("implementManId", StringUtils.toString(implementManId, eiMetadata.getMeta("implementManId")));
        map.put("implementManName", StringUtils.toString(implementManName, eiMetadata.getMeta("implementManName")));
        map.put("isComplete", StringUtils.toString(isComplete, eiMetadata.getMeta("isComplete")));
        map.put("overhaulLegacyProject", StringUtils.toString(overhaulLegacyProject, eiMetadata.getMeta("overhaulLegacyProject")));
        map.put("isConformStandard", StringUtils.toString(isConformStandard, eiMetadata.getMeta("isConformStandard")));
        map.put("relevantMeasures", StringUtils.toString(relevantMeasures, eiMetadata.getMeta("relevantMeasures")));
        map.put("isHot", StringUtils.toString(isHot, eiMetadata.getMeta("isHot")));
        map.put("hotCardId", StringUtils.toString(hotCardId, eiMetadata.getMeta("hotCardId")));
        map.put("offlinePartsGone", StringUtils.toString(offlinePartsGone, eiMetadata.getMeta("offlinePartsGone")));
        map.put("overhaulSuggestions", StringUtils.toString(overhaulSuggestions, eiMetadata.getMeta("overhaulSuggestions")));
        map.put("overhaulSummarize", StringUtils.toString(overhaulSummarize, eiMetadata.getMeta("overhaulSummarize")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));

        return map;

    }
}