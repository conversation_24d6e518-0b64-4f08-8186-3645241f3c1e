<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>


<EF:EFPage>
    <jsp:attribute name="footer">
        <script id="auth-template" type="text/x-kendo-template">

            <span class="k-checkbox-wrapper" role="presentation">

                <input type="checkbox" tabindex="-1" id="_#=item.uid#" class="k-checkbox" value="true" #if (item.isAuth) {# checked #} #/>

                <label for="_#=item.uid#" class="k-checkbox-label">
                </label>
            </span>
        </script>
    </jsp:attribute>
    <jsp:body>
        <div class="row">
            <EF:EFInput type="hidden" ename="checkedIds"/>
            <EF:EFInput type="hidden" ename="oldAuth"/>
            <div class="col-xs-8"></div>
    <%--        确认按钮--%>
            <div class="col-xs-2">
                <button id="commitBtn" type="button" class="k-button">
                    <span class="fa fa-check-square-o" style="font-size:12px;"> 确定</span>
                </button>
            </div>
    <%--        取消按钮--%>
            <div class="col-xs-2">
                <button id="cancelBtn" type="button" class="k-button">
                    <span class="fa fa-minus-square-o" style="font-size:12px;"> 取消</span>
                </button>
            </div>
        </div>
        <div class="row" style="margin: 10px -10px;">
            <div>
                <EF:EFRegion title="系统菜单树"
                             id="tree"
                             fitHeight="false">
                    <EF:EFTree  bindId="groupsTree"
                                ename="node"
                                textField="text"
                                valueField="label"
                                hasChildren="leaf"
                                serviceName="XSLV0202"
                                methodName="query">
                    </EF:EFTree>
                </EF:EFRegion>
            </div>
        </div>
    </jsp:body>
</EF:EFPage>