package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.common.vg.domain.Tvgdm0801;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.ed.util.SequenceGenerator;

import java.util.Map;

/**
 * 检修计划信息表
 */
public class VGDM0801 extends Tvgdm0801 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0801.query";
    /**
     * 智慧圈查询
     */
    public static final String QUERY_FOR_MOBILE = "VGDM0801.queryForMobile";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0801.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0801.insert";
    /**
     * 新增完成数据
     */
    public static final String INSERT_FINISH = "VGDM0801.insertFinish";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0801.update";
    /**
     * 修改状态信息
     */
    public static final String UPDATE_STATUS = "VGDM0801.updateStatus";

    /**
     * 移动审批修改检修计划状态
     */
    public static final String UPDATE_STATUS_FOR_MOBLIE = "VGDM0801.updateStatusForMoblie";
    /**
     * 修改实绩信息
     */
    public static final String UPDATE_ACTUAL = "VGDM0801.updateActual";
    /**
     * 查询计划检修信息
     */
    public static final String QUERY_FOR_SCHEDULE = "VGDM0801.queryForSchedule";
    /**
     * 查询检修计划信息
     */
    public static final String QUERY_FOR_IMC = "VGDM0801.queryForIMC";
    /**
     * 查询设备信息
     */
    public static final String QUERY_EQUIPMENT = "VGDM0801.queryEquipment";
    /**
     * 查询检修计划信息
     */
    public static final String QUERY_FOR_PDA = "VGDM0801.queryForPda";

    @Override
    public String getCheckStatus() {
        return this.getOverhaulPlanStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

    /**
     * 新增数据
     *
     * @param dao dao
     */
    public void insertData(Dao dao) {
        insertData(dao, false, true);
    }

    /**
     * 新增数据
     *
     * @param dao      dao
     * @param isFinish 是否完成
     */
    public void insertData(Dao dao, boolean isFinish) {
        insertData(dao, isFinish, true);
    }

    /**
     * 新增数据
     *
     * @param dao          dao
     * @param isFinish     是否为完成状态
     * @param isSetCreator 是否设置创建人等信息（pda调用时不需要）
     */
    public void insertData(Dao dao, boolean isFinish, boolean isSetCreator) {
        if (isFinish) {
            this.setOverhaulPlanStatus(MesConstant.Status.K40);
        } else {
            this.setOverhaulPlanStatus(MesConstant.Status.K10);
        }
        String[] arr = {this.getSegNo()};
        this.setOverhaulPlanId(SequenceGenerator.getNextSequence(
                SequenceConstant.OVERHAUL_PLAN_ID
                , arr));
        Map data = this.toMap();
        if (isSetCreator) {
            RecordUtils.setCreator(data);
        } else {
            data.put("uuid", UUIDUtils.getUUID());
        }
        if (isFinish) {
            dao.insert(INSERT_FINISH, data);
        } else {
            dao.insert(INSERT, data);
        }
    }
}
