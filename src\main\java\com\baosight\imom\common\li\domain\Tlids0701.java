/**
* Generate time : 2024-11-29 15:12:54
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids0701
* 
*/
public class Tlids0701 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String segName = " ";        /* 业务单元简称*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String machineCode = " ";		/* 机组代码*/
                private String machineName = " ";		/* 机组名称*/
                private String e_archivesNo = " ";		/* 设备档案编号*/
                private String equipmentName = " ";		/* 设备名称*/
                private String processCategory = " ";		/* 工序大类代码*/
                private String processCategoryName = " ";		/* 工序大类名称*/
                private String crossArea = " ";		/* 跨区编码*/
                private String crossAreaName = " ";		/* 跨区名称*/
                private String unpackAreaId = " ";		/* 拆包区编号*/
                private String unpackAreaName = " ";		/* 拆包区名称*/
                private String materialLoadingArea = " ";		/* 机组上料区代码*/
                private String materialLoadingAreaName = " ";		/* 机组上料区名称*/
                private String materialUnloadingArea = " ";		/* 机组下料区代码*/
                private String materialUnloadingAreaName = " ";		/* 机组下料区名称*/
                private String rejectionArea = " ";		/* 机组退料区代码*/
                private String rejectionAreaName = " ";		/* 机组退料区名称*/
                private String mouldCart = " ";		/* 机组模具台车代码*/
                private String mouldCartName = " ";		/* 机组模具台车名称*/
                private String packagingType = " ";		/* 包装类型*/
                private String status = " ";		/* 状态*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";		/* ID*/
                private String priorityLoadMaterialArea = " "; /*优先上料区*/
                private String priorityLoadMaterialAreaName = " ";/*优先上料区名称*/
                private String factoryArea = " ";        /* 厂区代码*/
                private String factoryAreaName = " ";        /* 厂区名称*/
                private String factoryBuilding = " ";        /* 厂房代码*/
                private String factoryBuildingName = " ";        /* 厂房名称*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("e_archivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategoryName");
        eiColumn.setDescName("工序大类名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossArea");
        eiColumn.setDescName("跨区编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossAreaName");
        eiColumn.setDescName("跨区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackAreaId");
        eiColumn.setDescName("拆包区编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unpackAreaName");
        eiColumn.setDescName("拆包区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialLoadingArea");
        eiColumn.setDescName("机组上料区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialLoadingAreaName");
        eiColumn.setDescName("机组上料区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialUnloadingArea");
        eiColumn.setDescName("机组下料区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialUnloadingAreaName");
        eiColumn.setDescName("机组下料区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rejectionArea");
        eiColumn.setDescName("机组退料区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rejectionAreaName");
        eiColumn.setDescName("机组退料区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldCart");
        eiColumn.setDescName("机组模具台车代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldCartName");
        eiColumn.setDescName("机组模具台车名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packagingType");
        eiColumn.setDescName("包装类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("priorityLoadMaterialArea");
        eiColumn.setDescName("优先上料区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("priorityLoadMaterialAreaName");
        eiColumn.setDescName("优先上料区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);

}
/**
* the constructor
*/
public Tlids0701() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the machineCode - 机组代码
        * @return the machineCode
        */
        public String getMachineCode() {
        return this.machineCode;
        }

        /**
        * set the machineCode - 机组代码
        */
        public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
        }
        /**
        * get the machineName - 机组名称
        * @return the machineName
        */
        public String getMachineName() {
        return this.machineName;
        }

        /**
        * set the machineName - 机组名称
        */
        public void setMachineName(String machineName) {
        this.machineName = machineName;
        }
        /**
        * get the e_archivesNo - 设备档案编号
        * @return the e_archivesNo
        */
        public String getE_archivesNo() {
        return this.e_archivesNo;
        }

        /**
        * set the e_archivesNo - 设备档案编号
        */
        public void setE_archivesNo(String e_archivesNo) {
        this.e_archivesNo = e_archivesNo;
        }
        /**
        * get the equipmentName - 设备名称
        * @return the equipmentName
        */
        public String getEquipmentName() {
        return this.equipmentName;
        }

        /**
        * set the equipmentName - 设备名称
        */
        public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
        }
        /**
        * get the processCategory - 工序大类代码
        * @return the processCategory
        */
        public String getProcessCategory() {
        return this.processCategory;
        }

        /**
        * set the processCategory - 工序大类代码
        */
        public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
        }
        /**
        * get the processCategoryName - 工序大类名称
        * @return the processCategoryName
        */
        public String getProcessCategoryName() {
        return this.processCategoryName;
        }

        /**
        * set the processCategoryName - 工序大类名称
        */
        public void setProcessCategoryName(String processCategoryName) {
        this.processCategoryName = processCategoryName;
        }
        /**
        * get the crossArea - 跨区编码
        * @return the crossArea
        */
        public String getCrossArea() {
        return this.crossArea;
        }

        /**
        * set the crossArea - 跨区编码
        */
        public void setCrossArea(String crossArea) {
        this.crossArea = crossArea;
        }
        /**
        * get the crossAreaName - 跨区名称
        * @return the crossAreaName
        */
        public String getCrossAreaName() {
        return this.crossAreaName;
        }

        /**
        * set the crossAreaName - 跨区名称
        */
        public void setCrossAreaName(String crossAreaName) {
        this.crossAreaName = crossAreaName;
        }
        /**
        * get the unpackAreaId - 拆包区编号
        * @return the unpackAreaId
        */
        public String getUnpackAreaId() {
        return this.unpackAreaId;
        }

        /**
        * set the unpackAreaId - 拆包区编号
        */
        public void setUnpackAreaId(String unpackAreaId) {
        this.unpackAreaId = unpackAreaId;
        }
        /**
        * get the unpackAreaName - 拆包区名称
        * @return the unpackAreaName
        */
        public String getUnpackAreaName() {
        return this.unpackAreaName;
        }

        /**
        * set the unpackAreaName - 拆包区名称
        */
        public void setUnpackAreaName(String unpackAreaName) {
        this.unpackAreaName = unpackAreaName;
        }
        /**
        * get the materialLoadingArea - 机组上料区代码
        * @return the materialLoadingArea
        */
        public String getMaterialLoadingArea() {
        return this.materialLoadingArea;
        }

        /**
        * set the materialLoadingArea - 机组上料区代码
        */
        public void setMaterialLoadingArea(String materialLoadingArea) {
        this.materialLoadingArea = materialLoadingArea;
        }
        /**
        * get the materialLoadingAreaName - 机组上料区名称
        * @return the materialLoadingAreaName
        */
        public String getMaterialLoadingAreaName() {
        return this.materialLoadingAreaName;
        }

        /**
        * set the materialLoadingAreaName - 机组上料区名称
        */
        public void setMaterialLoadingAreaName(String materialLoadingAreaName) {
        this.materialLoadingAreaName = materialLoadingAreaName;
        }
        /**
        * get the materialUnloadingArea - 机组下料区代码
        * @return the materialUnloadingArea
        */
        public String getMaterialUnloadingArea() {
        return this.materialUnloadingArea;
        }

        /**
        * set the materialUnloadingArea - 机组下料区代码
        */
        public void setMaterialUnloadingArea(String materialUnloadingArea) {
        this.materialUnloadingArea = materialUnloadingArea;
        }
        /**
        * get the materialUnloadingAreaName - 机组下料区名称
        * @return the materialUnloadingAreaName
        */
        public String getMaterialUnloadingAreaName() {
        return this.materialUnloadingAreaName;
        }

        /**
        * set the materialUnloadingAreaName - 机组下料区名称
        */
        public void setMaterialUnloadingAreaName(String materialUnloadingAreaName) {
        this.materialUnloadingAreaName = materialUnloadingAreaName;
        }
        /**
        * get the rejectionArea - 机组退料区代码
        * @return the rejectionArea
        */
        public String getRejectionArea() {
        return this.rejectionArea;
        }

        /**
        * set the rejectionArea - 机组退料区代码
        */
        public void setRejectionArea(String rejectionArea) {
        this.rejectionArea = rejectionArea;
        }
        /**
        * get the rejectionAreaName - 机组退料区名称
        * @return the rejectionAreaName
        */
        public String getRejectionAreaName() {
        return this.rejectionAreaName;
        }

        /**
        * set the rejectionAreaName - 机组退料区名称
        */
        public void setRejectionAreaName(String rejectionAreaName) {
        this.rejectionAreaName = rejectionAreaName;
        }
        /**
        * get the mouldCart - 机组模具台车代码
        * @return the mouldCart
        */
        public String getMouldCart() {
        return this.mouldCart;
        }

        /**
        * set the mouldCart - 机组模具台车代码
        */
        public void setMouldCart(String mouldCart) {
        this.mouldCart = mouldCart;
        }
        /**
        * get the mouldCartName - 机组模具台车名称
        * @return the mouldCartName
        */
        public String getMouldCartName() {
        return this.mouldCartName;
        }

        /**
        * set the mouldCartName - 机组模具台车名称
        */
        public void setMouldCartName(String mouldCartName) {
        this.mouldCartName = mouldCartName;
        }
        /**
        * get the packagingType - 包装类型
        * @return the packagingType
        */
        public String getPackagingType() {
        return this.packagingType;
        }

        /**
        * set the packagingType - 包装类型
        */
        public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }

        public String getSegName() {
                return segName;
        }

        public void setSegName(String segName) {
                this.segName = segName;
        }

        public String getPriorityLoadMaterialArea() {
                return priorityLoadMaterialArea;
        }

        public void setPriorityLoadMaterialArea(String priorityLoadMaterialArea) {
                this.priorityLoadMaterialArea = priorityLoadMaterialArea;
        }

        public String getPriorityLoadMaterialAreaName() {
                return priorityLoadMaterialAreaName;
        }

        public void setPriorityLoadMaterialAreaName(String priorityLoadMaterialAreaName) {
                this.priorityLoadMaterialAreaName = priorityLoadMaterialAreaName;
        }

        /**
         * get the factoryArea - 厂区代码
         * @return the factoryArea
         */
        public String getFactoryArea() {
                return this.factoryArea;
        }

        /**
         * set the factoryArea - 厂区代码
         */
        public void setFactoryArea(String factoryArea) {
                this.factoryArea = factoryArea;
        }

        /**
         * get the factoryAreaName - 厂区名称
         * @return the factoryAreaName
         */
        public String getFactoryAreaName() {
                return this.factoryAreaName;
        }

        /**
         * set the factoryAreaName - 厂区名称
         */
        public void setFactoryAreaName(String factoryAreaName) {
                this.factoryAreaName = factoryAreaName;
        }

        /**
         * get the factoryBuilding - 厂房代码
         * @return the factoryBuilding
         */
        public String getFactoryBuilding() {
                return this.factoryBuilding;
        }

        /**
         * set the factoryBuilding - 厂房代码
         */
        public void setFactoryBuilding(String factoryBuilding) {
                this.factoryBuilding = factoryBuilding;
        }

        /**
         * get the factoryBuildingName - 厂房名称
         * @return the factoryBuildingName
         */
        public String getFactoryBuildingName() {
                return this.factoryBuildingName;
        }

        /**
         * set the factoryBuildingName - 厂房名称
         */
        public void setFactoryBuildingName(String factoryBuildingName) {
                this.factoryBuildingName = factoryBuildingName;
        }
        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
                setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
                setE_archivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("e_archivesNo")), e_archivesNo));
                setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
                setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
                setProcessCategoryName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategoryName")), processCategoryName));
                setCrossArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossArea")), crossArea));
                setCrossAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossAreaName")), crossAreaName));
                setUnpackAreaId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackAreaId")), unpackAreaId));
                setUnpackAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unpackAreaName")), unpackAreaName));
                setMaterialLoadingArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialLoadingArea")), materialLoadingArea));
                setMaterialLoadingAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialLoadingAreaName")), materialLoadingAreaName));
                setMaterialUnloadingArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialUnloadingArea")), materialUnloadingArea));
                setMaterialUnloadingAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialUnloadingAreaName")), materialUnloadingAreaName));
                setRejectionArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("rejectionArea")), rejectionArea));
                setRejectionAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("rejectionAreaName")), rejectionAreaName));
                setMouldCart(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldCart")), mouldCart));
                setMouldCartName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldCartName")), mouldCartName));
                setPackagingType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packagingType")), packagingType));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setPriorityLoadMaterialArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("priorityLoadMaterialArea")), priorityLoadMaterialArea));
                setPriorityLoadMaterialAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("priorityLoadMaterialAreaName")), priorityLoadMaterialAreaName));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
                setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
                setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("machineCode",StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
                map.put("machineName",StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
                map.put("e_archivesNo",StringUtils.toString(e_archivesNo, eiMetadata.getMeta("e_archivesNo")));
                map.put("equipmentName",StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
                map.put("processCategory",StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
                map.put("processCategoryName",StringUtils.toString(processCategoryName, eiMetadata.getMeta("processCategoryName")));
                map.put("crossArea",StringUtils.toString(crossArea, eiMetadata.getMeta("crossArea")));
                map.put("crossAreaName",StringUtils.toString(crossAreaName, eiMetadata.getMeta("crossAreaName")));
                map.put("unpackAreaId",StringUtils.toString(unpackAreaId, eiMetadata.getMeta("unpackAreaId")));
                map.put("unpackAreaName",StringUtils.toString(unpackAreaName, eiMetadata.getMeta("unpackAreaName")));
                map.put("materialLoadingArea",StringUtils.toString(materialLoadingArea, eiMetadata.getMeta("materialLoadingArea")));
                map.put("materialLoadingAreaName",StringUtils.toString(materialLoadingAreaName, eiMetadata.getMeta("materialLoadingAreaName")));
                map.put("materialUnloadingArea",StringUtils.toString(materialUnloadingArea, eiMetadata.getMeta("materialUnloadingArea")));
                map.put("materialUnloadingAreaName",StringUtils.toString(materialUnloadingAreaName, eiMetadata.getMeta("materialUnloadingAreaName")));
                map.put("rejectionArea",StringUtils.toString(rejectionArea, eiMetadata.getMeta("rejectionArea")));
                map.put("rejectionAreaName",StringUtils.toString(rejectionAreaName, eiMetadata.getMeta("rejectionAreaName")));
                map.put("mouldCart",StringUtils.toString(mouldCart, eiMetadata.getMeta("mouldCart")));
                map.put("mouldCartName",StringUtils.toString(mouldCartName, eiMetadata.getMeta("mouldCartName")));
                map.put("packagingType",StringUtils.toString(packagingType, eiMetadata.getMeta("packagingType")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("priorityLoadMaterialArea",StringUtils.toString(priorityLoadMaterialArea, eiMetadata.getMeta("priorityLoadMaterialArea")));
                map.put("priorityLoadMaterialAreaName",StringUtils.toString(priorityLoadMaterialAreaName, eiMetadata.getMeta("priorityLoadMaterialAreaName")));
                map.put("factoryArea", StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("factoryAreaName", StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
                map.put("factoryBuilding", StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
                map.put("factoryBuildingName", StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));
return map;

}
}