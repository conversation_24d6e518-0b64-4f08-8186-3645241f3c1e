package com.baosight.imom.common.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.imom.vg.dm.domain.AlarmType;
import com.baosight.imom.vg.dm.domain.VGDM0301;
import com.baosight.imom.vg.dm.domain.VGDM0601;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

/**
 * iplat（BA、BC）平台工具类
 *
 * <AUTHOR> 郁在杰
 * @Description :iplat（BA、BC）平台工具类
 * @Date : 2024/11/12
 * @Version : 1.0
 */
public class IplatUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(IplatUtils.class);
    // 成功标记
    private static final String SUCCESS_CODE = "0";
    // 标签名
    private static final String TAG_NAME = "tagName";
    // 描述
    private static final String DESCRIPTION = "description";
    // 错误码
    private static final String ERROR_CODE = "errorCode";
    // 值
    private static final String VALUE = "value";
    // 点位分隔符
    private static final String DOT = ".";
    // 静态Map
    private static final Map<String, String> ALARM_PARAM_MAP = new HashMap<>();
    // 读取值、写值、确认报警、查询历史报警、查询历史报警数量、查询SCADA、查询点位
    private static final String SERVICE_READ_VALUES = "S_BI_DX_04";
    private static final String SERVICE_WRITE_VALUES = "S_BI_DX_09";
    private static final String SERVICE_CONFIRM_ALARM = "S_BI_DX_14";
    public static final String SERVICE_QUERY_ALARM = "S_BI_DX_12";
    public static final String SERVICE_QUERY_ALARM_COUNT = "S_BI_DX_13";
    public static final String SERVICE_QUERY_SCADA = "S_BI_SS_01";
    public static final String SERVICE_QUERY_TAG = "S_BI_SS_02";

    static {
        ALARM_PARAM_MAP.put("alarmTag", "fd_tag");
        ALARM_PARAM_MAP.put("priority", "fd_priority");
        ALARM_PARAM_MAP.put("alarmType", "fd_type");
        ALARM_PARAM_MAP.put("alarmTagValue", "fd_value_content");
        ALARM_PARAM_MAP.put("occurTime", "fd_ocurrtime");
        ALARM_PARAM_MAP.put("recoverTime", "fd_recovertime");
        ALARM_PARAM_MAP.put("alarmState", "fd_status");
        ALARM_PARAM_MAP.put("confirmStatus", "fd_status");
        ALARM_PARAM_MAP.put("nodeName", "fd_node");
        ALARM_PARAM_MAP.put("alarmId", "fd_alarm_id");
        ALARM_PARAM_MAP.put("alarmTagDesc", "fd_tagdescription");
        ALARM_PARAM_MAP.put("confirmor", "fd_confirmperson");
        ALARM_PARAM_MAP.put("confirmTime", "fd_confirmtime");
        ALARM_PARAM_MAP.put("className", "fd_classname");
        ALARM_PARAM_MAP.put("subSysName", "fd_subsystemname");
        ALARM_PARAM_MAP.put("alarmCategory", "fd_category");
        ALARM_PARAM_MAP.put("recoverTagValue", "fd_restore_content");
        ALARM_PARAM_MAP.put("alarmArea1", "fd_firstalarmarea");
        ALARM_PARAM_MAP.put("alarmArea2", "fd_secondalarmarea");
        ALARM_PARAM_MAP.put("alarmArea3", "fd_thirdalarmarea");
        ALARM_PARAM_MAP.put("alarmArea4", "fd_fourthalarmarea");
        ALARM_PARAM_MAP.put("alarmArea5", "fd_fifthalarmarea");
        ALARM_PARAM_MAP.put("alarmArea6", "fd_sixthalarmarea");
        ALARM_PARAM_MAP.put("group", "fd_group");
    }

    /**
     * 读取值
     *
     * @param tagIdList tagId列表
     * @param dao       用于查询tag信息
     * @return 标签值Map key:tagName、tagId,value:value
     */
    public static Map<String, String> readValues(List<String> tagIdList, Dao dao) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return null;
        }
        List<String> tagNameList = new ArrayList<>(tagIdList.size());
        // 转换tagName全称scadaName.tagId
        tagIdList.forEach(tagId -> {
            VGDM0301 data = VGDM0301.queryWithCache(dao, tagId);
            if (data == null) {
                LOGGER.error("tag信息不存在，tagId：{}", tagId);
                return;
            }
            tagNameList.add(data.getScadaName() + DOT + data.getTagId());
        });
        // 读取值
        return readValues(tagNameList);
    }

    /**
     * 读取值
     *
     * @param tagNameList tagName列表,tagName为全称，如iplat_demo.demoUse13
     * @return 标签值Map key:tagName、tagId,value:value
     */
    public static Map<String, String> readValues(List<String> tagNameList) {
        if (CollectionUtils.isEmpty(tagNameList)) {
            return null;
        }
        // 调用读取值服务
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tags", tagNameList);
        String result = callIplatService(eiInfo, SERVICE_READ_VALUES);
        // 将result转换为json数组对象
        JSONArray jsonArray = JSON.parseArray(result);
        // 结果map
        Map<String, String> resultMap = new HashMap<>();
        // 遍历结果
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String errorCode = jsonObject.getString(ERROR_CODE);
            // 不为0，则记录错误码
            if (!SUCCESS_CODE.equals(errorCode)) {
                LOGGER.error("读取值失败，errorCode：{}", errorCode);
                continue;
            }
            // 获取数据
            JSONArray dataArray = jsonObject.getJSONArray(EiConstant.resultBlock);
            for (int j = 0; j < dataArray.size(); j++) {
                JSONObject data = dataArray.getJSONObject(j);
                String tagName = data.getString(TAG_NAME);
                String value = data.getString(VALUE);
                // 全称点位
                resultMap.put(tagName, value);
                // tagId点位 - 如果有分隔符取最后一段，否则使用原值
                resultMap.put(tagName.substring(tagName.lastIndexOf(DOT) + 1), value);
            }
        }
        return resultMap;
    }

    /**
     * 批量写值
     *
     * @param dataMap 数据map key:tagId,value:value
     * @param dao     用于查询tag信息
     * @return 执行失败的点位 key:tagName,value:errorCode
     */
    public static Map<String, String> writeValues(Map<String, String> dataMap, Dao dao) {
        if (dataMap == null || dataMap.isEmpty()) {
            return null;
        }
        List<Map<String, String>> dataList = new ArrayList<>(dataMap.size());
        // 遍历dataMap并构建写值数据
        dataMap.forEach((tagName, value) -> {
            VGDM0301 data = VGDM0301.queryWithCache(dao, tagName);
            if (data == null) {
                LOGGER.error("tag信息不存在，tagName：{}", tagName);
                return;
            }
            Map<String, String> tagData = new HashMap<>(3);
            // 转换tagName全称scadaName.tagId
            tagData.put(TAG_NAME, data.getScadaName() + DOT + data.getTagId());
            tagData.put(DESCRIPTION, data.getTagDesc());
            tagData.put(VALUE, value);
            dataList.add(tagData);
        });
        return writeValues(dataList);
    }

    /**
     * 批量写值
     *
     * @param dataList 数据列表，map参数为tagName、description、value
     * @return 执行失败的点位 key:tagName,value:errorCode
     */
    public static Map<String, String> writeValues(List<Map<String, String>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        // 调用写值服务
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tags", dataList);
        String result = callIplatService(eiInfo, SERVICE_WRITE_VALUES);
        // 将result转换为json数组对象
        JSONArray jsonArray = JSON.parseArray(result);
        // 遍历结果
        Map<String, String> errorMap = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String errorCode = jsonObject.getString(ERROR_CODE);
            // 成功则不处理
            if (SUCCESS_CODE.equals(errorCode)) {
                continue;
            }
            // 获取失败数据
            JSONArray dataArray = jsonObject.getJSONArray(EiConstant.resultBlock);
            for (int j = 0; j < dataArray.size(); j++) {
                JSONObject data = dataArray.getJSONObject(j);
                String tagName = data.getString(TAG_NAME);
                String error = data.getString(ERROR_CODE);
                if (!SUCCESS_CODE.equals(error)) {
                    errorMap.put(tagName, error);
                }
            }
        }
        return errorMap;
    }

    /**
     * 调用iplat相关服务
     *
     * @param eiInfo    请求参数
     * @param serviceId 服务ID
     * @return 返回结果
     */
    private static String callIplatService(EiInfo eiInfo, String serviceId) {
        eiInfo.set(EiConstant.serviceId, serviceId);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (outInfo.getStatus() != 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getString(EiConstant.resultBlock);
    }

    /**
     * 确认报警
     *
     * @param list          报警信息列表
     * @param isConvertType 是否转换报警类型
     */
    public static void confirmAlarm(List<VGDM0601> list, boolean isConvertType) {
        if (CollectionUtils.isEmpty(list)) {
            LogUtils.log("confirmAlarm无待确认报警数据");
            return;
        }
        Map<String, List<Integer>> alarmInfo = new HashMap<>();
        String keyName;
        for (VGDM0601 alarmData : list) {
            keyName = alarmData.getScadaName() + "." + alarmData.getAlarmTag();
            if (!alarmInfo.containsKey(keyName)) {
                alarmInfo.put(keyName, new ArrayList<>());
            }
            if (isConvertType) {
                // 报警类型需转换
                alarmInfo.get(keyName).add(AlarmType.transAlmType(Integer.parseInt(alarmData.getAlarmTypedm())));
            } else {
                alarmInfo.get(keyName).add(Integer.parseInt(alarmData.getAlarmTypedm()));
            }
        }
        String segNoPrefix = list.get(0).getSegNo().substring(0, 2);
        EiInfo info = new EiInfo();
        info.set("szusername", "auto");
        info.set("ackAlmInfo", alarmInfo);
        info.set(EiConstant.serviceId, SERVICE_CONFIRM_ALARM + segNoPrefix);
        EiInfo outInfo = XServiceManager.call(info);
        if (outInfo.getStatus() != 0) {
            LogUtils.error("confirmAlarm确认报警失败:", outInfo.getMsg());
            if (!isConvertType) {
                throw new PlatException(outInfo.getMsg());
            }
        }
    }

    /**
     * 历史报警查询排序条件转换
     *
     * @param param 排序列名
     * @return sql中的列名
     */
    public static String convertHisAlarmParam2SqlParam(String param) {
        return ALARM_PARAM_MAP.getOrDefault(param, "");
    }

    /**
     * 转换报警类型为中文
     *
     * @param type            报警类型
     * @param customAlarmType 自定义报警类型
     * @return 中文报警类型
     */
    public static String transformAlmTypeZh(int type, String customAlarmType) {
        // 处理位报警类型
        if (type >= 101 && type <= 228) {
            return getBitAlarmType(type);
        }
        // 处理普通报警类型
        switch (type) {
            case 1:
                return "低低报警";
            case 2:
                return "低报警";
            case 3:
                return "高报警";
            case 4:
                return "高高报警";
            case 5:
                return "变化率报警";
            case 7:
                return "偏差报警";
            case 26:
                return "0-1报警";
            case 27:
                return "1-0报警";
            case 44:
                return "1状态报警";
            case 45:
                return "0状态报警";
            case 46:
                return "iPlature报警";
            // 自定义报警类型范围
            case 28:
            case 29:
            case 30:
            case 31:
            case 32:
            case 33:
            case 34:
            case 35:
            case 36:
            case 37:
            case 38:
            case 39:
            case 40:
            case 41:
            case 42:
            case 43:
                return customAlarmType + "报警";
            // 其他未知类型
            default:
                return "未知报警";
        }
    }

    /**
     * 获取位报警类型
     *
     * @param bitAndType 位和类型
     * @return 位报警类型
     */
    private static String getBitAlarmType(int bitAndType) {
        // 计算报警类型 (0-3)
        int type = bitAndType % 4;
        // 类型转换
        String[] alarmTypes = {"0状态报警", "0->1报警", "1->0报警", "1状态报警"};
        String alarmType = type >= 0 ? alarmTypes[type] : "";
        // 计算位数
        int bitNumber = (bitAndType - 100) / 4;
        return String.format("第%d位%s", bitNumber, alarmType);
    }

    /**
     * 将javaBean转换为报警信息
     *
     * @param alarms 报警信息
     * @return list
     */
    public static List<VGDM0601> javaBean2Alarm(List<Map> alarms) {
        if (CollectionUtils.isEmpty(alarms)) {
            return new ArrayList<>();
        }
        List<VGDM0601> result = new ArrayList<>();
        alarms.forEach((alarm) -> {
            try {
                // 存储报警值和恢复值信息
                Map aCurrentValue = (Map) alarm.get("aCurrentValue");
                // 存储报警ID信息
                Map almID = (Map<?, ?>) alarm.get("almID");
                VGDM0601 tmp = new VGDM0601();
                tmp.setAlarmId(MapUtils.getStr(almID, "ulAlmSeq"));
                tmp.setScadaName(MapUtils.getStr(alarm, "szNodeName"));
                tmp.setAlarmTag(MapUtils.getStr(alarm, "szTagName"));
                tmp.setAlarmAddress(MapUtils.getStr(alarm, "szNodeName"));
                tmp.setAlarmTypedm(MapUtils.getStr(alarm, "ucType"));
                tmp.setAlarmType(transformAlmTypeZh(MapUtils.getInt(alarm, "ucType"), MapUtils.getStr(alarm, "szEgu")));
                tmp.setAlarmTagDesc(MapUtils.getStr(alarm, "szTagDesc"));
                tmp.setPriority(MapUtils.getInt(alarm, "ucPriority"));
                tmp.setOccurTime(MapUtils.getStr(alarm, "tsOccur"));
                tmp.setConfirmTime(MapUtils.getStr(alarm, "tsConfirm"));
                int status = MapUtils.getInt(alarm, "ucStatus");
                switch (status) {
                    case 0:
                        tmp.setAlarmState("已恢复");
                        tmp.setConfirmStatus("已确认");
                        break;
                    case 1:
                        tmp.setAlarmState("未恢复");
                        tmp.setConfirmStatus("已确认");
                        break;
                    case 2:
                        tmp.setAlarmState("已恢复");
                        tmp.setConfirmStatus("未确认");
                        break;
                    default:
                        tmp.setAlarmState("未恢复");
                        tmp.setConfirmStatus("未确认");
                }
                tmp.setRecoverTime(MapUtils.getStr(alarm, "tsRecover"));
                tmp.setConfirmor(MapUtils.getStr(alarm, "szConfirmPerson"));
                tmp.setAlarmTagValue(MapUtils.getStr(aCurrentValue, "szValue"));
                tmp.setRecoverTagValue(MapUtils.getStr(aCurrentValue, "szRestoreValue"));
                result.add(tmp);
            } catch (Exception var6) {
                throw new PlatException("转换报警信息失败：" + var6.getMessage());
            }
        });
        return result;
    }
}
