package com.baosight.imom.li.rl.service;


import cn.hutool.core.lang.hash.Hash;
import com.baosight.imc.common.annotation.ExportExcel;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DateUtils;
import com.baosight.imom.common.utils.EasyExcelUtil;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.xservices.em.util.SmsSendManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

import static com.ibm.db2.jcc.resources.ResourceKeys.driverName;

/**
 * @Author: 张博翔
 * @Description: ${车辆进厂登记}
 * @Date: 2024/8/19 9:26
 * @Version: 1.0
 */
public class ServiceLIRL0302 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0302().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String segName = MapUtils.getString(queryBlock, "segName", "");
            String status = MapUtils.getString(queryBlock, "status", "");//状态
            String vehicleNo = MapUtils.getString(queryBlock, "vehicleNo", "");//状态
            String businessType = MapUtils.getString(queryBlock, "businessType", "");//状态
            if (StringUtils.isNotBlank(vehicleNo)){
                if (vehicleNo.contains("\n")){
                    String[] split = vehicleNo.split("\n");
                    List<String> strVehicleNo = Arrays.asList(split);
                    queryBlock.put("vehicleNoStr",strVehicleNo);
                    queryBlock.put("vehicleNo","");
                }

            }
            if ("20".equals(businessType)){
                queryBlock.put("businessType","10");
                queryBlock.put("voucherNumN","1");
            }else if ("60".equals(businessType)){
                queryBlock.put("businessType","20");
                queryBlock.put("voucherNumN","1");
            }else if ("40".equals(businessType)){
                queryBlock.put("businessType","30");
                queryBlock.put("voucherNumN","1");
            }else if ("50".equals(businessType)){
                queryBlock.put("businessType","40");
                queryBlock.put("voucherNumN","1");
            }else if ("70".equals(businessType)) {
                queryBlock.put("businessType","50");
                queryBlock.put("voucherNumN","1");
            }else if ("80".equals(businessType)) {
                queryBlock.put("businessType","60");
                queryBlock.put("voucherNumN","1");
            }
            else if ("10".equals(businessType)){
                queryBlock.put("businessType","10");
                queryBlock.put("handType","10");
            }else if ("30".equals(businessType)){
                queryBlock.put("businessType","30");
                queryBlock.put("handType","30");
            }
            if (StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            if ("10".equals(status)){
                queryBlock.put("pendingReview","asc");
            }
            String reservationDateRange = MapUtils.getString(queryBlock, "reservationDateRange", "");
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDate", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, 1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDate", format);
            }


            outInfo = super.query(inInfo, LIRL0302.QUERY_ALL);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    public EiInfo postExport(EiInfo inInfo) {

        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map<String,Object> loginMap = new HashMap();
            loginMap.put("userId",UserSession.getUserId());
            loginMap.put("userName",UserSession.getLoginCName());
            loginMap.put("loginName",UserSession.getLoginName());
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String businessType = MapUtils.getString(queryBlock, "businessType", "");//状态
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            if ("20".equals(businessType)){
                queryBlock.put("businessType","10");
                queryBlock.put("voucherNumN","1");
            }else if ("60".equals(businessType)){
                queryBlock.put("businessType","20");
                queryBlock.put("voucherNumN","1");
            }else if ("40".equals(businessType)){
                queryBlock.put("businessType","30");
                queryBlock.put("voucherNumN","1");
            }else if ("50".equals(businessType)){
                queryBlock.put("businessType","40");
                queryBlock.put("voucherNumN","1");
            }else if ("70".equals(businessType)) {
                queryBlock.put("businessType","50");
                queryBlock.put("voucherNumN","1");
            }else if ("80".equals(businessType)) {
                queryBlock.put("businessType","60");
                queryBlock.put("voucherNumN","1");
            }
            else if ("10".equals(businessType)){
                queryBlock.put("businessType","10");
                queryBlock.put("handType","10");
            }else if ("30".equals(businessType)){
                queryBlock.put("businessType","30");
                queryBlock.put("handType","30");
            }
            String reservationDateRange = MapUtils.getString(queryBlock, "reservationDateRange", "");
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDate", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, 1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDate", format);
            }

            List<Map<String,Object>> hashMapList = dao.queryAll("LIRL0302.queryAllExport", queryBlock);
            inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
            if (CollectionUtils.isNotEmpty(hashMapList)){
                extracted(hashMapList,"1");
            }
            Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, hashMapList, loginMap);
            inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return inInfo;
    }

    private static void extracted(List<Map<String,Object>> hashMapList,String b) {
        for (Map hashMap: hashMapList){
            if ("1".equals(b)){
                String businessType = MapUtils.getString(hashMap, "businessType", "");
                switch (businessType) {
                    case "10":
                        hashMap.put("businessType", "钢材装货");
                        break;
                    case "20":
                        hashMap.put("businessType", "钢材卸货");
                        break;
                    case "30":
                        hashMap.put("businessType", "钢材卸货+装货");
                        break;
                    case "40":
                        hashMap.put("businessType", "托盘运输");
                        break;
                    case "50":
                        hashMap.put("businessType", "资材卸货");
                        break;
                    case "60":
                        hashMap.put("businessType", "废料提货");
                        break;
                    case "70":
                        hashMap.put("businessType", "欧冶提货");
                    case "80":
                        hashMap.put("businessType", "其他物品运输");
                        break;
                }

                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "撤销");
                        break;
                    case "10":
                        hashMap.put("status", "待审核");
                        break;
                    case "20":
                        hashMap.put("status", "审核");
                        break;
                    case "99":
                        hashMap.put("status", "驳回");
                        break;
                }
                String checkSource = MapUtils.getString(hashMap, "checkSource", "");
                switch (checkSource) {
                    case "10":
                        hashMap.put("checkSource", "一体机");
                        break;
                }
            }
        }
    }


    public EiInfo queryExport(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        try {
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String segName = MapUtils.getString(queryBlock, "segName", "");
            if (StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            String reservationDateRange = MapUtils.getString(queryBlock, "reservationDateRange", "");
            //预约范围(只看本日预约、只看明日预约、查看所有预约) 10-本日、20-明日
            if ("10".equals(reservationDateRange)) {
                queryBlock.put("reservationDate", format);
            } else if ("20".equals(reservationDateRange)) {
                cal.add(Calendar.DATE, 1);
                format = dateFormat.format(cal.getTime());
                queryBlock.put("reservationDate", format);
            }
            outInfo = super.query(inInfo, LIRL0302.QUERY_ALL);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /***
     * 车辆进场登记 审批弹窗查询 预约时段信息
     */
    public EiInfo queryAppointmentSlot(EiInfo inInfo) {
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Map queryBlock = inInfo.getBlock("inqu2_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String businessType = MapUtils.getString(queryBlock, "businessType", "");//装卸类型
        String voucherNum = MapUtils.getString(queryBlock, "voucherNum", "");//提单号
        String typeOfHandling = "";
        if (StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        //查询启用中的预约时段
        queryBlock.put("status", 20);
        int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();//当前天是星期几
        switch (dayOfWeek) {
            case 1:
                queryBlock.put("weekDays", "一");
                break;
            case 2:
                queryBlock.put("weekDays", "二");
                break;
            case 3:
                queryBlock.put("weekDays", "三");
                break;
            case 4:
                queryBlock.put("weekDays", "四");
                break;
            case 5:
                queryBlock.put("weekDays", "五");
                break;
            case 6:
                queryBlock.put("weekDays", "六");
                break;
            case 7:
                queryBlock.put("weekDays", "日");
                break;
        }
        inInfo = super.query(inInfo, LIRL0106.QUERY, new LIRL0106(), false, null, "inqu2_status", "result2", "result2");
        //无预约登记：
        //1：登记的业务类型如果是原料卸货则匹配卸货的预约数
        //2：登记的数据如果是有提单的，则匹配装货的预约数
        //3：登记的数据如果是废料提货，则匹配废料提货的预约数
        //4：登记的数据如果是周转架运输，则匹配周转架的预约数
        //5：登记的数据如果是资材卸货，则匹配资材卸货的预约数
        //6：登记的数据如果是欧冶提货，则匹配欧冶提货的预约数
        if (org.apache.commons.lang3.StringUtils.isBlank(voucherNum)){
            /*if ("10".equals(businessType)){
                typeOfHandling = "20";
            }else if ("20".equals(businessType)){
                typeOfHandling = "60";
            }else if ("30".equals(businessType)){
                typeOfHandling = "40";
            }else if ("40".equals(businessType)){
                typeOfHandling = "50";
            }else if ("50".equals(businessType)){
                typeOfHandling = "70";
            }else if ("60".equals(businessType)){
                typeOfHandling = "60";
            }*/
            typeOfHandling = businessType;
        }else {
            typeOfHandling = "10";
        }
        String mergeTags="";
        Long reservationMaxNum=0l;
        //查询车辆预约数管理
        Map queryLIRL0105 = new HashMap();
        queryLIRL0105.put("segNo", segNo);
        queryLIRL0105.put("status", 20);
        queryLIRL0105.put("typeOfHandling", typeOfHandling);
        queryLIRL0105.put("delFlag", 0);
        List<LIRL0105> lirl0105s = dao.query(LIRL0105.QUERY, queryLIRL0105);
        if (CollectionUtils.isNotEmpty(lirl0105s)){
            LIRL0105 lirl0105 = lirl0105s.get(0);
            reservationMaxNum = lirl0105.getReservationMaxNum();
            mergeTags = lirl0105.getMergeTags();//合并分组
        }

        List<String> listTypeOfHandlingString = new ArrayList<>();//分组的装卸类型
        //判断三种业务类型在时段内的最大预约数，要合并到一块考虑。
        if (StrUtil.isNotBlank(mergeTags)){
            //按分组查询车辆预约数管理
            queryLIRL0105 = new HashMap();
            queryLIRL0105.put("segNo", segNo);
            queryLIRL0105.put("status", 20);
            queryLIRL0105.put("mergeTags", mergeTags);
            queryLIRL0105.put("delFlag", 0);
            List<HashMap> queryReservationMaxNum = dao.query(LIRL0105.QUERY_RESERVATION_MAX_NUM, queryLIRL0105);
            if (queryReservationMaxNum.size() > 0){
                HashMap hashMap = queryReservationMaxNum.get(0);
                reservationMaxNum = MapUtils.getLong(hashMap,"reservationMaxNum",0l);
            }else {
                String massage = "未查询到最大预约数信息！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            //查询同分组的装卸类型
            List<HashMap> listTypeOfHandling = dao.query(LIRL0105.QUERY_TYPE_OF_HANDLING, queryLIRL0105);
            for (HashMap hashMap:listTypeOfHandling){
                String typeOfHandling1 = MapUtils.getString(hashMap, "typeOfHandling", "");
                listTypeOfHandlingString.add(typeOfHandling1);
            }

        }
        boolean b = false;
        //查询该预约时间段剩余预约号
        List<HashMap> result2 = inInfo.getBlock("result2").getRows();
        for (HashMap hashMap : result2) {
            String reservationTime = MapUtils.getString(hashMap, "reservationTime", "");//预约时段
            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("reservationDate", format);//当前时间
            queryMap.put("reservationTime", reservationTime);
            if (listTypeOfHandlingString.size()>0){
                queryMap.put("typeOfHandlingList", listTypeOfHandlingString);
            }else {
                queryMap.put("typeOfHandling", typeOfHandling);
            }

            queryMap.put("delFlag", 0);
            //查询当前时段预约人数
            int count = super.count(LIRL0201.COUNT, queryMap);
            long l1 = reservationMaxNum - count;
            hashMap.put("num", l1);
            //判断预约时段剩余预约数是否全部为0
            if (l1 <= 0){
                b = true;
            }
        }
        inInfo.set("b",b);
        return inInfo;
    }

    /***
     * 车辆进场登记 审批弹窗确认
     */
    public EiInfo confirm(EiInfo inInfo) {

        List<HashMap> resultBlock = inInfo.getBlock(EiConstant.resultBlock).getRows();
        List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
        List<HashMap> result3 = (List<HashMap>) inInfo.get("result3");
        String stringDate = DateUtil.curDateTimeStr14();//当前时间
        EiInfo eiInfo = new EiInfo();
        //短信接收人信息
        /*List list2 = new ArrayList();*/
        String mobileNum = "";
        //获取选择的时段
        String reservationTime = "";
        for (HashMap hashMap : listHashMap) {
            reservationTime = MapUtils.getString(hashMap, "reservationTime", "");
            String num = MapUtils.getString(hashMap, "num", "");
            if (StringUtils.isNotEmpty(num)){
                if ("0".equals(num)){
                    //判断其他预约时段是否有剩余的
                    if (ObjectUtils.isNotEmpty(result3)){
                        for (HashMap hashMap1:result3){
                            String num1 = MapUtils.getString(hashMap1, "num", "");
                            if (!"0".equals(num1)){
                                String massage = "当天还有其他时段有剩余号码，可选择其他时段！";
                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                inInfo.setMsg(massage);
                                return inInfo;
                            }
                        }
                    }
                }
            }
        }

        for (HashMap hashMap : resultBlock) {
            String segNo = MapUtils.getString(hashMap, "segNo", "");
            String handType = MapUtils.getString(hashMap, "handType", "");//装卸业务
            String telNum = MapUtils.getString(hashMap, "telNum", "");//<!-- 司机电话 -->
            String driverName = MapUtils.getString(hashMap, "driverName", "");//<!-- 司机名称 -->
            String idCard = MapUtils.getString(hashMap, "idCard", "");//<!-- 司机身份 -->
            String businessType = MapUtils.getString(hashMap, "businessType", "");//<!-- 业务类型 -->
            String voucherNum = MapUtils.getString(hashMap, "voucherNum", "");//<!-- 提单号 -->
            String reservationNumber = MapUtils.getString(hashMap, "reservationNumber", "");//<!-- 预约单号 -->
            String factoryArea = MapUtils.getString(hashMap, "factoryArea", "");//厂区
            String factoryAreaName = MapUtils.getString(hashMap, "factoryAreaName", "");//厂区名称
            String customerName = MapUtils.getString(hashMap, "customerName", "");//客户名称
            String customerId = MapUtils.getString(hashMap, "customerId", "");//客户名称

            mobileNum = telNum;

            /*HashMap hashMap2 = new HashMap<>();
            hashMap2.put("mobile", telNum);
            hashMap2.put("receiverJobNo", driverName);
            hashMap2.put("receiverName", driverName);
            list2.add(hashMap2);*/

            eiInfo.set("segNo", segNo);
            eiInfo.set("voucherNum", MapUtils.getString(hashMap, "voucherNum", ""));
            eiInfo.set("recCreator", MapUtils.getString(hashMap, "recCreator", ""));
            eiInfo.set("idCard", MapUtils.getString(hashMap, "idCard", ""));
            eiInfo.set("checkSource", MapUtils.getString(hashMap, "checkSource", ""));
            eiInfo.set("handType", handType);
            eiInfo.set("remark", MapUtils.getString(hashMap, "remark", ""));
            eiInfo.set("vehicleNo", MapUtils.getString(hashMap, "vehicleNo", ""));
            eiInfo.set("driverName", driverName);
            eiInfo.set("businessType", businessType);
            eiInfo.set("telNum", telNum);
            eiInfo.set("factoryArea", factoryArea);
            eiInfo.set("factoryAreaName", factoryAreaName);

            //查询司机信息获取承运商/客户代码
            Map queryLIRL0102 = new HashMap();
            queryLIRL0102.put("segNo", segNo);
            queryLIRL0102.put("status", "20");
            queryLIRL0102.put("tel", telNum);
            queryLIRL0102.put("driverName", driverName);
            List<HashMap> hashMapList = dao.query(LIRL0102.QUERY, queryLIRL0102);


            //插入车辆预约表
            Map insertLirl0201 = new HashMap();
            insertLirl0201.putAll(hashMap);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(voucherNum)){
                /*typeOfHandling = "10";*/
                insertLirl0201.put("typeOfHandling", 10);//业务类型
                if ("10".equals(handType)){
                    insertLirl0201.put("typeOfHandling","10");
                }else if ("30".equals(handType)) {
                    insertLirl0201.put("typeOfHandling","30");
                }
            }

            insertLirl0201.put("customerId", customerId);//<!-- 承运商/客户代码 -->
            insertLirl0201.put("customerName", customerName);//<!-- 承运商/客户名称 -->

            if (hashMapList.size() > 0) {
                for (HashMap map : hashMapList) {
                    String customerName1 = MapUtils.getString(map, "customerName", "");
                    customerId = MapUtils.getString(map, "customerId", "");
                    if (customerName.equals(customerName1)){
                        insertLirl0201.put("customerId", customerId);//<!-- 承运商/客户代码 -->
                        insertLirl0201.put("customerName", customerName);//<!-- 承运商/客户名称 -->
                    }
                }
            } else {
                if ("10".equals(businessType)||org.apache.commons.lang3.StringUtils.isBlank(businessType)) {
                    if ("10".equals(businessType)){
                        String massage = "该司机信息在安徽宝钢数智物流系统中不存在或不为生效状态，本次允许登记待人工审核。但为了后续进厂可以事先预约，避免等待，请联系所属承运商或客户的管理员（调度员）进行维护。";
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg(massage);
                    }else {
                        String massage = "该司机信息在安徽宝钢数智物流系统中不存在或不为生效状态，请联系所属承运商或客户的管理员（调度员）进行维护！";
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg(massage);
                        return eiInfo;
                    }
                }
            }

            if ("20".equals(businessType)){
                hashMap.put("businessType","10");
            }else if ("60".equals(businessType)){
                hashMap.put("businessType","20");
            }else if ("40".equals(businessType)){
                hashMap.put("businessType","30");
            }else if ("50".equals(businessType)){
                hashMap.put("businessType","40");
            }else if ("70".equals(businessType)) {
                hashMap.put("businessType","50");
            }else if ("80".equals(businessType)){
                hashMap.put("businessType","60");
            }

            insertLirl0201.put("driverTel", telNum);//<!-- 司机电话 -->
            insertLirl0201.put("driverIdentity", idCard);//<!-- 司机身份 -->
            String substring = stringDate.substring(0, 8);
            insertLirl0201.put("reservationDate", substring);//<!-- 预约日期 -->
            insertLirl0201.put("reservationTime", reservationTime);//<!-- 预约时段 -->
            insertLirl0201.put("status", 20);//状态
            insertLirl0201.put("delFlag", 0);//记录删除标记
            insertLirl0201.put("typeOfHandling",businessType);//记录删除标记
            //判断预约单号是否为空
            if (StringUtils.isEmpty(reservationNumber)){
                String strSeqTypeId = "TLIRL_SEQ0201";
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                reservationNumber = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                insertLirl0201.put("reservationNumber", reservationNumber);//预约单号
                insertLirl0201.put("uuid", StrUtil.getUUID());//UUID
            }
            //增加预约单是否有预约单
            insertLirl0201.put("isReservation", "00");
            insertLirl0201.put("visitUnit", " ");
            RecordUtils.setCreator(insertLirl0201);
            LIRL0201 lirl0201 = new LIRL0201();
            lirl0201.fromMap(insertLirl0201);
            dao.insert(LIRL0201.INSERT, lirl0201);

            hashMap.put("status","20");
            hashMap.put("reservationNumber",reservationNumber);
            hashMap.put("reservationDate", substring);//<!-- 预约日期 -->
            hashMap.put("reservationTime", reservationTime);//<!-- 预约时段 -->
            RecordUtils.setRevisor(hashMap);
            //修改车辆登记表
            dao.update(LIRL0302.UPDATE,hashMap);

            eiInfo.set("reservationNumber", reservationNumber);
            eiInfo.set("pageFlag", "1");
        }
        //插入车辆排序表
        eiInfo.set(EiConstant.serviceName, "LIRLInterface");
        eiInfo.set(EiConstant.methodName, "entryRegistration");
        eiInfo = XLocalManager.call(eiInfo);
        if (eiInfo.getStatus() < 0) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            return eiInfo;
        }

        //审批通过发送短信
        //短信内容
        EiInfo outInfo = new EiInfo();
        /*EiInfo vzbmInfo = new EiInfo();
        List list = new ArrayList();
        //发送短信：{0},内容：{1}
        list.add("审批通过");
        list.add("您的登记信息已审核，请耐心等待叫号");

        vzbmInfo.set("params", list);
        vzbmInfo.set("receiver", list2);
        vzbmInfo.set("msgTemplateId", "MT0000000019");//短信登记号
        vzbmInfo.set(EiConstant.serviceId, "S_VI_PM_9067");
        EiInfo outInfo = EServiceManager.call(vzbmInfo, TokenUtils.getXplatToken());*/
        //短信内容
        String content = "您的登记信息已审核，但由于事先未预约，需等待其他已预约司机的装卸货作业完毕方可进厂，请耐心等待叫号。";
        outInfo.set("content",content);
        outInfo.set("mobileNum",mobileNum);
        outInfo = SmsSendManager.sendMobile(outInfo);
        if (outInfo.getStatus() == -1) {
            // 调用失败
            throw new RuntimeException("调用平台短信发送服务报错:" + outInfo.getMsg());
        }
        return outInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            //短信接收人信息
            /*List list2 = new ArrayList();*/
            String mobileNum = "";
            for (HashMap hashMap : listHashMap) {
                String telNum = MapUtils.getString(hashMap, "telNum", "");//<!-- 司机电话 -->
                String driverName = MapUtils.getString(hashMap, "driverName", "");//<!-- 司机名称 -->
                String businessType = MapUtils.getString(hashMap, "businessType", "");//<!-- 司机名称 -->

                if ("20".equals(businessType)){
                    hashMap.put("businessType","10");
                }else if ("60".equals(businessType)){
                    hashMap.put("businessType","20");
                }else if ("40".equals(businessType)){
                    hashMap.put("businessType","30");
                }else if ("50".equals(businessType)){
                    hashMap.put("businessType","40");
                }else if ("70".equals(businessType)){
                    hashMap.put("businessType","50");
                }else if ("80".equals(businessType)){
                    hashMap.put("businessType","60");
                }
                mobileNum = telNum;
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0302> query = dao.query(LIRL0302.QUERY, map);
                for (LIRL0302 lirl0302 : query) {
                    String status = lirl0302.getStatus();
                    //TODO 使用全局变量
                    if (!MesConstant.Status.K10.equals(status)) {
                        String massage = "只能对待审核状态进行修改";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", "99");//记录删除标记
                hashMap.put("delFlag", 0);//记录删除标记
                RecordUtils.setRevisor(hashMap);

                /*HashMap hashMap2 = new HashMap<>();
                hashMap2.put("mobile", telNum);
                hashMap2.put("receiverJobNo", driverName);
                hashMap2.put("receiverName", driverName);
                list2.add(hashMap2);*/
            }
            inInfo = super.update(inInfo, LIRL0302.UPDATE);

            //驳回发送短信
            //短信内容
            /*EiInfo vzbmInfo = new EiInfo();
            List list = new ArrayList();
            //发送短信：{0},内容：{1}
            list.add("驳回");
            list.add("您的登记信息已驳回!");

            vzbmInfo.set("params", list);
            vzbmInfo.set("receiver", list2);
            vzbmInfo.set("msgTemplateId", "MT0000000019");//短信登记号
            vzbmInfo.set(EiConstant.serviceId, "S_VI_PM_9067");
            EiInfo outInfo = EServiceManager.call(vzbmInfo, TokenUtils.getXplatToken());*/
            //短信内容
            EiInfo outInfo = new EiInfo();
            String content = "审批驳回: 您的登记信息已驳回!";
            outInfo.set("content",content);
            outInfo.set("mobileNum",mobileNum);
            outInfo = SmsSendManager.sendMobile(outInfo);
            if (outInfo.getStatus() == -1) {
                // 调用失败
                throw new RuntimeException("调用平台短信发送服务报错:" + outInfo.getMsg());
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 定时任务.
     * 司机到门卫登记后，24小时还未进厂，就自动帮他取消登记。并短信通知，撤销登记的同时删除排队表，叫号表，超时表中的数据
     *
     * @param inInfo
     * @return
     * @Service: S_LI_RL_0088
     */
    public EiInfo scheduledCancellationOfRegistration(EiInfo inInfo) {
        try {
            ServiceLIRLInterface serviceLIRLInterface = new ServiceLIRLInterface();
            Calendar calendar = Calendar.getInstance();
            long l = System.currentTimeMillis(); //获取时间戳效率最高
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHH");
            Date date = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            calendar.add(calendar.DATE, -1);//获取昨天日期
            Date time = calendar.getTime();
            String format1 = dateFormat.format(time);
            //查询当前入场登记车辆跟踪信息
            Map queryLIRL0301 = new HashMap();
            queryLIRL0301.put("checkDateTime",format1);
            queryLIRL0301.put("status",10);
            queryLIRL0301.put("delFlag",0);
            List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
            //删除超过24小时车辆登记，未进厂的车辆登记信息
            for (LIRL0301 lirl0301 : lirl0301s) {
                String in_seg_no = lirl0301.getSegNo();
                String lv_vehicle_id = lirl0301.getVehicleNo();
                String driverName = lirl0301.getDriverName();//司机姓名
                String telNum = lirl0301.getTelNum();//手机号
                String status = lirl0301.getStatus();
                String carTraceNo = lirl0301.getCarTraceNo();
                Integer statusNum = new Integer(status);
                if (statusNum == 10) {
                    //如果车辆已经登记一次，但是车辆只在排队还没有>=叫号，是可以重复登记，将之前的登记数据撤销就行，重新插入一条
                    //取消登记同步撤销预排序数据
                    //删除预排序主表
                    Map deleteLIRL0303 = new HashMap<>();
                    deleteLIRL0303.put("segNo", in_seg_no);
                    deleteLIRL0303.put("vehicleNo", lv_vehicle_id);
                    deleteLIRL0303.put("vehicleId", lv_vehicle_id);
                    deleteLIRL0303.put("carTraceNo", carTraceNo);
                    // 修改人工号
                    deleteLIRL0303.put("recRevisor", driverName);
                    // 修改人姓名
                    deleteLIRL0303.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0303.put("recReviseTime", DateUtil.curDateTimeStr14());
                    //RecordUtils.setRevisor(deleteLIRL0303);
                    dao.delete(LIRL0303.DELETE, deleteLIRL0303);
                    //从装卸子表中删除数据
                    dao.delete(LIRL0305.DELETE, deleteLIRL0303);
                    //如果在超时表 从超时表中删掉
                    dao.delete(LIRL0403.DELETE, deleteLIRL0303);
                    //从车辆排序临时表删除
                    dao.delete(LIRL0310.DELETE, deleteLIRL0303);
                    //删除车辆排序主表
                    Map deleteLIRL0401 = new HashMap();
                    deleteLIRL0401.put("segNo", in_seg_no);
                    deleteLIRL0401.put("carTraceNo", carTraceNo);
                    deleteLIRL0401.put("vehicleNo", lv_vehicle_id);
                    // 修改人工号
                    deleteLIRL0401.put("recRevisor", driverName);
                    // 修改人姓名
                    deleteLIRL0401.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0401.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.delete(LIRL0401.DELETE, deleteLIRL0401);
                    //撤销登记表
                    Map updateLIRL0302 = new HashMap();
                    updateLIRL0302.put("segNo", in_seg_no);
                    updateLIRL0302.put("status", "00");
                    updateLIRL0302.put("sysRemark", "超时未进厂自动撤销");// 备注
                    updateLIRL0302.put("remark", "超时未进厂自动撤销");// 备注
                    updateLIRL0302.put("carTraceNo", carTraceNo);// 车辆追踪号
                    // 修改人工号
                    updateLIRL0302.put("recRevisor", driverName);
                    // 修改人姓名
                    updateLIRL0302.put("recRevisorName", driverName);
                    // 修改时间
                    updateLIRL0302.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.update("LIRL0302.updateTranslation", updateLIRL0302);

                    //撤销车辆跟踪表
                    // 修改人工号
                    lirl0301.setRecRevisor(driverName);
                    // 修改人姓名
                    lirl0301.setRecRevisorName(driverName);
                    // 修改时间
                    lirl0301.setRecReviseTime(DateUtil.curDateTimeStr14());
                    dao.delete(LIRL0301.DELETE, lirl0301);

                    //检查叫号表是否有数据
                    Map queryLIRL0402 = new HashMap();
                    queryLIRL0402.put("segNo", in_seg_no);
                    queryLIRL0402.put("carTraceNo", carTraceNo);
                    queryLIRL0402.put("vehicleNo", lv_vehicle_id);
                    List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
                    if (lirl0402s.size() > 0) {
                        LIRL0402 lirl0402 = lirl0402s.get(0);
                        String lv_min_hand_point = lirl0402.getTargetHandPointId();
                        //如果在叫号表 从叫号表中删掉
                        dao.delete(LIRL0402.DELETE, queryLIRL0402);
                        //叫号表数据删除后，装卸点的进车跟踪要随之改变
                        EiInfo outInfo = serviceLIRLInterface.updateHandPointJobNumber(in_seg_no, lv_min_hand_point, 0, -1, 31);
                        if (outInfo.getStatus() < 0) {
                            return outInfo;
                        }
                    }
                    //删除补录的预约单号
                    Map deleteLIRL0201 = new HashMap();
                    deleteLIRL0201.put("segNo", in_seg_no);
                    deleteLIRL0201.put("reservationNumber",lirl0301.getReservationNumber());
                    // 修改人工号
                    deleteLIRL0201.put("recRevisor", driverName);
                    deleteLIRL0201.put("remark", "超时未登记自动撤销");
                    deleteLIRL0201.put("sysRemark", "超时未登记自动撤销");
                    // 修改人姓名
                    deleteLIRL0201.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0201.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.update(LIRL0201.DELETE_BY_RESERVATION_NUMBER, deleteLIRL0201);

                }  else if (statusNum < 10) {
                    //撤销登记表
                    Map updateLIRL0302 = new HashMap();
                    updateLIRL0302.put("segNo", in_seg_no);
                    updateLIRL0302.put("status", "00");
                    updateLIRL0302.put("sysRemark", "超时未进厂自动撤销");// 备注
                    updateLIRL0302.put("remark", "超时未进厂自动撤销");// 备注
                    updateLIRL0302.put("carTraceNo", carTraceNo);// 车辆追踪号
                    RecordUtils.setRevisor(updateLIRL0302);
                    dao.update("LIRL0302.updateTranslation", updateLIRL0302);

                    //撤销车辆跟踪表
                    Map map = lirl0301.toMap();
                    RecordUtils.setRevisor(map);
                    dao.delete(LIRL0301.DELETE, map);

                    //取消登记同步撤销预排序数据

                    //删除预排序主表
                    Map deleteLIRL0303 = new HashMap<>();
                    deleteLIRL0303.put("segNo", in_seg_no);
                    deleteLIRL0303.put("vehicleNo", lv_vehicle_id);
                    deleteLIRL0303.put("vehicleId", lv_vehicle_id);
                    deleteLIRL0303.put("carTraceNo", carTraceNo);
                    dao.delete(LIRL0303.DELETE, deleteLIRL0303);
                    //从装卸子表中删除数据
                    dao.delete(LIRL0305.DELETE, deleteLIRL0303);
                    //如果在超时表 从超时表中删掉
                    dao.delete(LIRL0403.DELETE, deleteLIRL0303);
                    //从车辆排序临时表删除
                    dao.delete(LIRL0310.DELETE, deleteLIRL0303);

                    //检查叫号表是否有数据
                    Map queryLIRL0402 = new HashMap();
                    queryLIRL0402.put("segNo", in_seg_no);
                    deleteLIRL0303.put("vehicleNo", lv_vehicle_id);
                    queryLIRL0402.put("carTraceNo", carTraceNo);
                    List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
                    if (lirl0402s.size() > 0) {
                        LIRL0402 lirl0402 = lirl0402s.get(0);
                        String lv_min_hand_point = lirl0402.getTargetHandPointId();
                        //如果在叫号表 从叫号表中删掉
                        dao.delete(LIRL0402.DELETE, queryLIRL0402);
                        //叫号表数据删除后，装卸点的进车跟踪要随之改变
                        EiInfo outInfo = serviceLIRLInterface.updateHandPointJobNumber(in_seg_no, lv_min_hand_point, 0, -1, 31);
                        if (outInfo.getStatus() < 0) {
                            return outInfo;
                        }
                    }
                }
                //审批通过发送短信
                //短信内容
                EiInfo outInfo = new EiInfo();
                //短信内容
                String content ="司机为["+driverName+"]的车辆:["+lv_vehicle_id+"]进厂超时:超过24小时还未进厂，您的登记信息已撤销";
                outInfo.set("content",content);
                outInfo.set("mobileNum",telNum);
                outInfo = SmsSendManager.sendMobile(outInfo);
                if (outInfo.getStatus() == -1) {
                    // 调用失败
                    throw new RuntimeException("调用平台短信发送服务报错:" + outInfo.getMsg());
                }
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    public EiBlockMeta getExportBlockMeta() {
        EiColumn eiColumn;
        EiBlockMeta eiMetadata = new EiBlockMeta();
        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkId");
        eiColumn.setDescName("车辆登记流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceStatus");
        eiColumn.setDescName("车辆跟踪号状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00撤销  10待审核 20审核 99驳回)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handType");
        eiColumn.setDescName("装卸类型(10 装 20卸 30装卸)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("idCard");
        eiColumn.setDescName("身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("驾驶员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("telNum");
        eiColumn.setDescName("手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setDescName("车辆预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkSource");
        eiColumn.setDescName("数据来源(10一体机)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("提单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setDescName("业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setDescName("车辆预约时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setDescName("车辆预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setDescName("进厂登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lateEarlyFlag");
        eiColumn.setDescName("迟到早到标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callDate");
        eiColumn.setDescName("叫号日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callTime");
        eiColumn.setDescName("叫号时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }

    /**
     * 定时任务.
     * 预约后，超过预约日期和预约时段固定时间小时还未登记，就自动帮他取消预约。撤销的时候，给司机发一个短信通知，同时LIRL0201备注栏字段写上“超时未登记自动撤销”。
     *
     * @param inInfo
     * @return
     * @Service: S_LI_RL_0101
     */
    public EiInfo scheduledCancelReservation(EiInfo inInfo) {
        try {

            Map<String, String> mapQueue = new HashMap<>();
            mapQueue.put("processSwitchName", "WL_VEHICLE_ORDER_QUENE_SWITCH");
            mapQueue.put("processSwitchValue", "1");
            List<HashMap> list = dao.query(LIRL0100.QUERY_SWITCH, mapQueue);
            for (HashMap hashMap : list) {
                String segNo = MapUtils.getString(hashMap, "segNo");
                // String segNo = "JE000000";
                //查询当前入场登记车辆跟踪信息
                Map queryLIRL0201 = new HashMap();
                queryLIRL0201.put("segNo", segNo);
                queryLIRL0201.put("status", "20");
                //查询固定时间
                //查询配置
                Map querytlirl0314 = new HashMap();
                querytlirl0314.put("segNo",segNo);
                querytlirl0314.put("itemCode","CANCEL_REV_TIME");
                List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
                if (lirl0314s.size()>0){
                   int count = Integer.parseInt(lirl0314s.get(0).getItemCname());
                    queryLIRL0201.put("preTime", count);
                }
                List<HashMap> LIRL0201Map = dao.query(LIRL0201.QUERY_UNREGISTERED, queryLIRL0201);
                //删除超过72预约未登记信息
                for (HashMap lirl0201 : LIRL0201Map) {
                    segNo = MapUtils.getString(lirl0201, "segNo");
                    String  lv_vehicle_id = MapUtils.getString(lirl0201, "vehicleNo");
                    String  driverName = MapUtils.getString(lirl0201, "driverName");
                    String  telNum = MapUtils.getString(lirl0201, "driverTel");
                    String  reservationNumber = MapUtils.getString(lirl0201, "reservationNumber");
                    //删除补录的预约单号
                    Map deleteLIRL0201 = new HashMap();
                    deleteLIRL0201.put("segNo", segNo);
                    deleteLIRL0201.put("reservationNumber", reservationNumber);
                    // 修改人工号
                    deleteLIRL0201.put("recRevisor", driverName);
                    deleteLIRL0201.put("remark", "超时未登记自动撤销");
                    deleteLIRL0201.put("sysRemark", "超时未登记自动撤销");
                    // 修改人姓名
                    deleteLIRL0201.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0201.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.update(LIRL0201.DELETE_BY_RESERVATION, deleteLIRL0201);

                    //审批通过发送短信
                    //短信内容
                    EiInfo outInfo = new EiInfo();
                    //短信内容
                    String content = "司机为[" + driverName + "]的车辆:[" + lv_vehicle_id + "]预约超时:超过72小时还未登记，您的预约信息已撤销";
                    outInfo.set("content", content);
                    outInfo.set("mobileNum", telNum);
                    outInfo = SmsSendManager.sendMobile(outInfo);
                    if (outInfo.getStatus() == -1) {
                        // 调用失败
                        throw new RuntimeException("调用平台短信发送服务报错:" + outInfo.getMsg());
                    }
                }
                // 返回成功状态和消息
                inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
            }
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }

        return inInfo;
    }
}
