$(function (){
    $(function () {
        $("#sub_query_status-0-segName").val(decodeURI($("#sub_query_status-0-segName").val()));
    });
    IPLATUI.EFGrid = {
        "sub_result": {
            /**
             * EFGrid新增后触发的事件
             */
            afterAdd: function (e) {
                $.each(e.items, function (index, item) {
                    item.internalCode = "";
                });
            },
            loadComplete: function (grid) {

                $("#QUERY").on("click", function () {
                    sub_resultGrid.dataSource.page(1);
                });

            },
            //双击选中
            onRowDblClick: function (e) {
                var windowId = $("#sub_query_status-0-windowId").val();
                if (IPLAT.isBlankString(windowId)) {
                    // 设置默认值
                    windowId = "userNum";
                }
                //双击选中前先把双击的数据勾选上
                sub_resultGrid.setCheckedRows(e.row);
                //关闭下拉框
                window.parent[windowId + "Window"].close();
            }
        }
    };
    if(window.parent !== null){
        $("#sub_query_status-0-pageId").val(window.parent.parent.__ei.efFormEname);
    }
});
