<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0402">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanSubStatus">
            CHECK_PLAN_SUB_STATUS = #checkPlanSubStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanId">
            CHECK_PLAN_ID = #checkPlanId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            DEVICE_CODE = #deviceCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanSubId">
            CHECK_PLAN_SUB_ID like concat('%',#checkPlanSubId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0402">
        SELECT
        CHECK_PLAN_ID as "checkPlanId",  <!-- 点检计划主项号 -->
        CHECK_PLAN_SUB_ID as "checkPlanSubId",  <!-- 点检计划分档号 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        SPOT_CHECK_CONTENT as "spotCheckContent",  <!-- 点检内容 -->
        SPOT_CHECK_METHOD as "spotCheckMethod",  <!-- 点检方法 -->
        DEVICE_CHECK_STATUS as "deviceCheckStatus",  <!-- 设备点检状态 -->
        IS_PUBLISH as "isPublish",  <!-- 是否挂牌 -->
        SPOT_CHECK_STANDARD_TYPE as "spotCheckStandardType",  <!-- 点检标准类型 -->
        JUDGMENT_STANDARD as "judgmentStandard",  <!-- 判断标准 -->
        SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        SPOT_CHECK_IMPLEMENTE as "spotCheckImplemente",  <!-- 点检实施方 -->
        MEASURE_ID as "measureId",  <!-- 计量单位 -->
        UPPER_LIMIT as "upperLimit",  <!-- 上限值 -->
        LOWER_LIMIT as "lowerLimit",  <!-- 下限值 -->
        PLAN_SOURCE as "planSource",  <!-- 计划来源 -->
        CHECK_PLAN_SUB_STATUS as "checkPlanSubStatus",  <!-- 点检计划分档状态 -->
        ACTUALS_REMARK as "actualsRemark",  <!-- 点检实绩信息 -->
        IS_NORMAL as "isNormal",  <!-- 是否异常 -->
        EXCEPTION_CONTACT_ID as "exceptionContactId",  <!-- 异常信息联络单号 -->
        ACTUALS_REVISOR as "actualsRevisor",  <!-- 点检实绩操作人 -->
        ACTUALS_TIME as "actualsTime",  <!-- 点检实绩操作时间 -->
        ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 点检实绩操作人姓名 -->
        ACTUALS_SOURCE as "actualsSource",  <!-- 点检实绩来源 -->
        SPOT_CHECK_STANDARD_ID as "spotCheckStandardId",  <!-- 点检标准编号 -->
        TAG_ID as "tagId",  <!-- 自动采集点位 -->
        TAG_DESC as "tagDesc",  <!-- 点位描述 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        IS_PICTURE as "isPicture"  <!-- 是否上传图片 -->
        FROM ${mevgSchema}.TVGDM0402 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                CHECK_PLAN_SUB_ID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0402 WHERE 1=1
        <include refid="condition"/>
    </select>

    <sql id="condition2">
        <isNotEmpty prepend=" AND " property="segNo">
            T.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            T.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            T.UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanSubStatus">
            T.CHECK_PLAN_SUB_STATUS = #checkPlanSubStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanId">
            T.CHECK_PLAN_ID = #checkPlanId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckStandardId">
            T.SPOT_CHECK_STANDARD_ID = #spotCheckStandardId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanSubId">
            T.CHECK_PLAN_SUB_ID like concat('%',#checkPlanSubId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            T.DEVICE_CODE = #deviceCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            T.DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            T1.E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="checkPlanStatus">
            T1.CHECK_PLAN_STATUS = #checkPlanStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notNewlyStatus">
            T1.CHECK_PLAN_STATUS != '10'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="activeFlag">
            T1.CHECK_PLAN_STATUS in ('20','30')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            T.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            T.DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="queryWithMain" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T.CHECK_PLAN_ID as "checkPlanId",  <!-- 点检计划主项号 -->
        T.CHECK_PLAN_SUB_ID as "checkPlanSubId",  <!-- 点检计划分档号 -->
        T.DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        T.DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        T.SPOT_CHECK_CONTENT as "spotCheckContent",  <!-- 点检内容 -->
        T.SPOT_CHECK_METHOD as "spotCheckMethod",  <!-- 点检方法 -->
        T.DEVICE_CHECK_STATUS as "deviceCheckStatus",  <!-- 设备点检状态 -->
        T.IS_PUBLISH as "isPublish",  <!-- 是否挂牌 -->
        T.SPOT_CHECK_STANDARD_TYPE as "spotCheckStandardType",  <!-- 点检标准类型 -->
        T.JUDGMENT_STANDARD as "judgmentStandard",  <!-- 判断标准 -->
        T.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        T.SPOT_CHECK_IMPLEMENTE as "spotCheckImplemente",  <!-- 点检实施方 -->
        T.MEASURE_ID as "measureId",  <!-- 计量单位 -->
        T.UPPER_LIMIT as "upperLimit",  <!-- 上限值 -->
        T.LOWER_LIMIT as "lowerLimit",  <!-- 下限值 -->
        T.PLAN_SOURCE as "planSource",  <!-- 计划来源 -->
        T.CHECK_PLAN_SUB_STATUS as "checkPlanSubStatus",  <!-- 点检计划分档状态 -->
        T.ACTUALS_REMARK as "actualsRemark",  <!-- 点检实绩信息 -->
        T.IS_NORMAL as "isNormal",  <!-- 是否异常 -->
        T.EXCEPTION_CONTACT_ID as "exceptionContactId",  <!-- 异常信息联络单号 -->
        T.ACTUALS_REVISOR as "actualsRevisor",  <!-- 点检实绩操作人 -->
        T.ACTUALS_TIME as "actualsTime",  <!-- 点检实绩操作时间 -->
        T.ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 点检实绩操作人姓名 -->
        T.ACTUALS_SOURCE as "actualsSource",  <!-- 点检实绩来源 -->
        T.SPOT_CHECK_STANDARD_ID as "spotCheckStandardId",  <!-- 点检标准编号 -->
        T.TAG_ID as "tagId",  <!-- 自动采集点位 -->
        T.TAG_DESC as "tagDesc",  <!-- 点位描述 -->
        T.UUID as "uuid",  <!-- 唯一编码 -->
        T.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        T.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        T.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        T.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        T.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        T.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        T.TENANT_ID as "tenantId",  <!-- 租户ID -->
        T.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        T.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        T.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T.UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        T.IS_PICTURE as "isPicture",  <!-- 是否上传图片 -->
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T1.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T1.CHECK_PLAN_DATE as "checkPlanDate",  <!-- 点检日期 -->
        T1.CHECK_PLAN_STATUS as "checkPlanStatus"  <!-- 点检计划主档状态 -->
        FROM ${mevgSchema}.TVGDM0402 T
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T.CHECK_PLAN_ID = T1.CHECK_PLAN_ID
        WHERE 1=1
        <include refid="condition2"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                T1.CHECK_PLAN_DATE DESC,T.DEVICE_CODE asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="countWithMain" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0402 T
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T.CHECK_PLAN_ID = T1.CHECK_PLAN_ID
        WHERE 1=1
        <include refid="condition2"/>
    </select>

    <select id="queryForDp" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T.CHECK_PLAN_ID as "checkPlanId",  <!-- 点检计划主项号 -->
        T.CHECK_PLAN_SUB_ID as "checkPlanSubId",  <!-- 点检计划分档号 -->
        T.DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        T.DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        T.SPOT_CHECK_CONTENT as "spotCheckContent",  <!-- 点检内容 -->
        T.JUDGMENT_STANDARD as "judgmentStandard",  <!-- 判断标准 -->
        T.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        T.CHECK_PLAN_SUB_STATUS as "checkPlanSubStatus",  <!-- 点检计划分档状态 -->
        T.IS_NORMAL as "isNormal",  <!-- 是否异常 -->
        T.ACTUALS_REMARK as "actualsRemark",  <!-- 点检实绩信息 -->
        T.ACTUALS_REVISOR as "actualsRevisor",  <!-- 点检实绩操作人 -->
        T.ACTUALS_TIME as "actualsTime",  <!-- 点检实绩操作时间 -->
        T.ACTUALS_REVISOR_NAME as "actualsRevisorName",  <!-- 点检实绩操作人姓名 -->
        T.ACTUALS_SOURCE as "actualsSource",  <!-- 点检实绩来源 -->
        T.UUID as "uuid",  <!-- 唯一编码 -->
        T.SEG_NO as "segNo",  <!-- 系统帐套 -->
        T.UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        T.IS_PICTURE as "isPicture",  <!-- 是否上传图片 -->
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T1.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T1.CHECK_PLAN_DATE as "checkPlanDate",  <!-- 点检日期 -->
        T1.CHECK_PLAN_STATUS as "checkPlanStatus"  <!-- 点检计划主档状态 -->
        FROM ${mevgSchema}.TVGDM0402 T
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T.CHECK_PLAN_ID = T1.CHECK_PLAN_ID
        WHERE 1=1
        <include refid="condition2"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                T1.CHECK_PLAN_DATE DESC,T.DEVICE_CODE asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="queryTodayDevice" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        T.DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        MAX(T.DEVICE_NAME) as "deviceName",  <!-- 分部设备名称 -->
        MAX(T.UUID) as "uuid"  <!-- 唯一编码 -->
        FROM ${mevgSchema}.TVGDM0402 T
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T.CHECK_PLAN_ID = T1.CHECK_PLAN_ID
        AND T.SEG_NO = T1.SEG_NO
        WHERE 1=1
        AND T.SEG_NO = #segNo#
        AND T.CHECK_PLAN_SUB_STATUS = '10'
        AND T1.E_ARCHIVES_NO = #eArchivesNo#
        AND T1.CHECK_PLAN_DATE = #checkPlanDate#
        AND T1.CHECK_PLAN_STATUS in ('20','30')
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        AND T1.DEL_FLAG = '0'
        AND T.DEL_FLAG = '0'
        GROUP BY T.DEVICE_CODE
        ORDER BY T.DEVICE_CODE
    </select>

    <select id="queryDoRate" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MAX(T2.UUID) as "uuid",  <!-- 唯一编码 -->
        T1.CHECK_PLAN_DATE as "checkPlanDate",
        T2.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        IF(T2.SPOT_CHECK_NATURE='10','操作点检','专业点检') as "spotCheckNatureName",
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(T1.EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        COUNT(T2.UUID) AS "totalNum",  <!-- 总数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='10',1,0)) AS "notNum",  <!-- 未完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20',1,0)) AS "doNum",  <!-- 已完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20' AND T2.IS_NORMAL='1',1,0)) AS "eNum"  <!-- 异常数量 -->
        FROM ${mevgSchema}.TVGDM0402 T2
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T2.CHECK_PLAN_ID=T1.CHECK_PLAN_ID
        AND T2.SEG_NO=T1.SEG_NO
        WHERE T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG ='0'
        AND T1.CHECK_PLAN_STATUS IN ('20','30','40')
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        GROUP BY T2.SPOT_CHECK_NATURE,T1.E_ARCHIVES_NO,t1.CHECK_PLAN_DATE
        order by t1.E_ARCHIVES_NO,T2.SPOT_CHECK_NATURE,t1.CHECK_PLAN_DATE
    </select>

    <select id="queryDoRateForB1Query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDMB101">
        SELECT
        MAX(T2.UUID) as "uuid",  <!-- 唯一编码 -->
        T1.CHECK_PLAN_DATE as "checkPlanDate",
        T2.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        IF(T2.SPOT_CHECK_NATURE='10','操作点检','专业点检') as "spotCheckNatureName",
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(T1.EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        COUNT(T2.UUID) AS "totalNum",  <!-- 总数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='10',1,0)) AS "notNum",  <!-- 未完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20',1,0)) AS "doNum",  <!-- 已完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20' AND T2.IS_NORMAL='1',1,0)) AS "eNum"  <!-- 异常数量 -->
        FROM ${mevgSchema}.TVGDM0402 T2
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T2.CHECK_PLAN_ID=T1.CHECK_PLAN_ID
        AND T2.SEG_NO=T1.SEG_NO
        WHERE T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG ='0'
        AND T1.CHECK_PLAN_STATUS IN ('20','30','40')
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        GROUP BY T2.SPOT_CHECK_NATURE,T1.E_ARCHIVES_NO,t1.CHECK_PLAN_DATE
        order by t1.E_ARCHIVES_NO,T2.SPOT_CHECK_NATURE,t1.CHECK_PLAN_DATE
    </select>

    <select id="queryDoRate2" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT * FROM (SELECT
        MAX(T2.UUID) as "uuid",  <!-- 唯一编码 -->
        T2.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        concat(replace(#checkStartDate#,'-',''),'-',replace(#checkEndDate#,'-','')) as "checkPlanDate",
        IF(T2.SPOT_CHECK_NATURE='10','操作点检','专业点检') as "spotCheckNatureName",
        'ALL' as "eArchivesNo",  <!-- 设备档案编号 -->
        '所有设备' as "equipmentName",  <!-- 设备名称 -->
        COUNT(T2.UUID) AS "totalNum",  <!-- 总数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='10',1,0)) AS "notNum",  <!-- 未完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20',1,0)) AS "doNum",  <!-- 已完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20' AND T2.IS_NORMAL='1',1,0)) AS "eNum"  <!-- 异常数量 -->
        FROM ${mevgSchema}.TVGDM0402 T2
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T2.CHECK_PLAN_ID=T1.CHECK_PLAN_ID
        AND T2.SEG_NO=T1.SEG_NO
        WHERE T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG ='0'
        AND T1.CHECK_PLAN_STATUS IN ('20','30','40')
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        GROUP BY T2.SPOT_CHECK_NATURE) BB
        UNION ALL
        SELECT * FROM (SELECT
        MAX(T2.UUID) as "uuid",  <!-- 唯一编码 -->
        T2.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        concat(replace(#checkStartDate#,'-',''),'-',replace(#checkEndDate#,'-','')) as "checkPlanDate",
        IF(T2.SPOT_CHECK_NATURE='10','操作点检','专业点检') as "spotCheckNatureName",
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(T1.EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        COUNT(T2.UUID) AS "totalNum",  <!-- 总数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='10',1,0)) AS "notNum",  <!-- 未完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20',1,0)) AS "doNum",  <!-- 已完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20' AND T2.IS_NORMAL='1',1,0)) AS "eNum"  <!-- 异常数量 -->
        FROM ${mevgSchema}.TVGDM0402 T2
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T2.CHECK_PLAN_ID=T1.CHECK_PLAN_ID
        AND T2.SEG_NO=T1.SEG_NO
        WHERE T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG ='0'
        AND T1.CHECK_PLAN_STATUS IN ('20','30','40')
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        GROUP BY T2.SPOT_CHECK_NATURE,T1.E_ARCHIVES_NO
        order by t1.E_ARCHIVES_NO) CC
    </select>

    <select id="queryDoRate2ForB1Query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDMB101">
        SELECT * FROM (SELECT
        MAX(T2.UUID) as "uuid",  <!-- 唯一编码 -->
        T2.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        concat(replace(#checkStartDate#,'-',''),'-',replace(#checkEndDate#,'-','')) as "checkPlanDate",
        IF(T2.SPOT_CHECK_NATURE='10','操作点检','专业点检') as "spotCheckNatureName",
        'ALL' as "eArchivesNo",  <!-- 设备档案编号 -->
        '所有设备' as "equipmentName",  <!-- 设备名称 -->
        COUNT(T2.UUID) AS "totalNum",  <!-- 总数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='10',1,0)) AS "notNum",  <!-- 未完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20',1,0)) AS "doNum",  <!-- 已完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20' AND T2.IS_NORMAL='1',1,0)) AS "eNum"  <!-- 异常数量 -->
        FROM ${mevgSchema}.TVGDM0402 T2
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T2.CHECK_PLAN_ID=T1.CHECK_PLAN_ID
        AND T2.SEG_NO=T1.SEG_NO
        WHERE T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG ='0'
        AND T1.CHECK_PLAN_STATUS IN ('20','30','40')
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        GROUP BY T2.SPOT_CHECK_NATURE) BB
        UNION ALL
        SELECT * FROM (SELECT
        MAX(T2.UUID) as "uuid",  <!-- 唯一编码 -->
        T2.SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        concat(replace(#checkStartDate#,'-',''),'-',replace(#checkEndDate#,'-','')) as "checkPlanDate",
        IF(T2.SPOT_CHECK_NATURE='10','操作点检','专业点检') as "spotCheckNatureName",
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(T1.EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        COUNT(T2.UUID) AS "totalNum",  <!-- 总数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='10',1,0)) AS "notNum",  <!-- 未完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20',1,0)) AS "doNum",  <!-- 已完成数量 -->
        SUM(IF(T2.CHECK_PLAN_SUB_STATUS='20' AND T2.IS_NORMAL='1',1,0)) AS "eNum"  <!-- 异常数量 -->
        FROM ${mevgSchema}.TVGDM0402 T2
        LEFT JOIN ${mevgSchema}.TVGDM0401 T1
        ON T2.CHECK_PLAN_ID=T1.CHECK_PLAN_ID
        AND T2.SEG_NO=T1.SEG_NO
        WHERE T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG ='0'
        AND T1.CHECK_PLAN_STATUS IN ('20','30','40')
        <isNotEmpty prepend=" AND " property="checkPlanDate">
            T1.CHECK_PLAN_DATE = #checkPlanDate#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkStartDate">
            T1.CHECK_PLAN_DATE &gt;= replace(#checkStartDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="checkEndDate">
            replace(#checkEndDate#,'-','') &gt;= T1.CHECK_PLAN_DATE
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            T1.SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            T1.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        GROUP BY T2.SPOT_CHECK_NATURE,T1.E_ARCHIVES_NO
        order by t1.E_ARCHIVES_NO) CC
    </select>


    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0402 (CHECK_PLAN_ID,  <!-- 点检计划主项号 -->
        CHECK_PLAN_SUB_ID,  <!-- 点检计划分档号 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        SPOT_CHECK_CONTENT,  <!-- 点检内容 -->
        SPOT_CHECK_METHOD,  <!-- 点检方法 -->
        DEVICE_CHECK_STATUS,  <!-- 设备点检状态 -->
        IS_PUBLISH,  <!-- 是否挂牌 -->
        SPOT_CHECK_STANDARD_TYPE,  <!-- 点检标准类型 -->
        JUDGMENT_STANDARD,  <!-- 判断标准 -->
        SPOT_CHECK_NATURE,  <!-- 点检性质 -->
        SPOT_CHECK_IMPLEMENTE,  <!-- 点检实施方 -->
        MEASURE_ID,  <!-- 计量单位 -->
        UPPER_LIMIT,  <!-- 上限值 -->
        LOWER_LIMIT,  <!-- 下限值 -->
        PLAN_SOURCE,  <!-- 计划来源 -->
        CHECK_PLAN_SUB_STATUS,  <!-- 点检计划分档状态 -->
        SPOT_CHECK_STANDARD_ID,  <!-- 点检标准编号 -->
        TAG_ID,  <!-- 自动采集点位 -->
        TAG_DESC,  <!-- 点位描述 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE, <!-- 业务单元代码 -->
        IS_PICTURE  <!-- 是否上传图片 -->
        )
        VALUES (#checkPlanId#, #checkPlanSubId#, #deviceCode#, #deviceName#, #spotCheckContent#, #spotCheckMethod#,
        #deviceCheckStatus#, #isPublish#, #spotCheckStandardType#, #judgmentStandard#, #spotCheckNature#,
        #spotCheckImplemente#, #measureId#, #upperLimit#, #lowerLimit#, #planSource#, #checkPlanSubStatus#,
        #spotCheckStandardId#, #tagId#, #tagDesc#,
        #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #isPicture#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0402 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0402
        SET
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        SPOT_CHECK_CONTENT = #spotCheckContent#,   <!-- 点检内容 -->
        SPOT_CHECK_METHOD = #spotCheckMethod#,   <!-- 点检方法 -->
        DEVICE_CHECK_STATUS = #deviceCheckStatus#,   <!-- 设备点检状态 -->
        IS_PUBLISH = #isPublish#,   <!-- 是否挂牌 -->
        SPOT_CHECK_STANDARD_TYPE = #spotCheckStandardType#,   <!-- 点检标准类型 -->
        JUDGMENT_STANDARD = #judgmentStandard#,   <!-- 判断标准 -->
        MEASURE_ID = #measureId#,   <!-- 计量单位 -->
        UPPER_LIMIT = #upperLimit#,   <!-- 上限值 -->
        LOWER_LIMIT = #lowerLimit#,   <!-- 下限值 -->
        PLAN_SOURCE = #planSource#,   <!-- 计划来源 -->
        TAG_ID = #tagId#,   <!-- 自动采集点位 -->
        TAG_DESC = #tagDesc#,   <!-- 自动采集点位 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        IS_PICTURE = #isPicture#   <!-- 是否上传图片 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateByMain">
        UPDATE ${mevgSchema}.TVGDM0402
        SET
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        CHECK_PLAN_ID = #checkPlanId#
    </update>

    <update id="updateSpotCheckNature">
        UPDATE ${mevgSchema}.TVGDM0402
        SET
        SPOT_CHECK_NATURE = #spotCheckNature#,   <!-- 点检性质 -->
        SPOT_CHECK_IMPLEMENTE = #spotCheckNature#,   <!-- 点检实施方 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        CHECK_PLAN_ID = #checkPlanId#
    </update>

    <update id="updateActual">
        UPDATE ${mevgSchema}.TVGDM0402
        SET
        CHECK_PLAN_SUB_STATUS = #checkPlanSubStatus#,   <!-- 点检计划分档状态 -->
        ACTUALS_REMARK = #actualsRemark#,   <!-- 点检实绩信息 -->
        IS_NORMAL = #isNormal#,   <!-- 是否异常 -->
        EXCEPTION_CONTACT_ID = #exceptionContactId#,<!--异常单号-->
        ACTUALS_REVISOR = #actualsRevisor#,   <!-- 点检实绩操作人 -->
        ACTUALS_TIME = #actualsTime#,   <!-- 点检实绩操作时间 -->
        ACTUALS_REVISOR_NAME = #actualsRevisorName#,   <!-- 点检实绩操作人姓名 -->
        ACTUALS_SOURCE = #actualsSource#,   <!-- 点检实绩来源 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateByDay">
        UPDATE ${mevgSchema}.TVGDM0402 T2
        SET
        T2.DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        T2.REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        T2.REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        T2.REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        EXISTS (SELECT 1 FROM ${mevgSchema}.TVGDM0401 T1
        WHERE T1.CHECK_PLAN_ID = T2.CHECK_PLAN_ID
        AND T1.DEL_FLAG = '0'
        AND T1.CHECK_PLAN_DATE = #checkPlanDate#
        AND T1.E_ARCHIVES_NO = #eArchivesNo#)
    </update>

    <update id="updateSpotCheckStandard">
        UPDATE ${mevgSchema}.TVGDM0402 T2
        SET
        T2.SPOT_CHECK_CONTENT = #spotCheckContent#,   <!-- 点检内容 -->
        T2.SPOT_CHECK_METHOD = #spotCheckMethod#,   <!-- 点检方法 -->
        T2.DEVICE_CHECK_STATUS = #deviceCheckStatus#,   <!-- 设备点检状态 -->
        T2.IS_PUBLISH = #isPublish#,   <!-- 是否挂牌 -->
        T2.SPOT_CHECK_STANDARD_TYPE = #spotCheckStandardType#,   <!-- 点检标准类型 -->
        T2.JUDGMENT_STANDARD = #judgmentStandard#,   <!-- 判断标准 -->
        T2.IS_PICTURE = #isPicture#,   <!-- 是否上传图片 -->
        T2.MEASURE_ID = #measureId#,   <!-- 计量单位 -->
        T2.UPPER_LIMIT = #upperLimit#,   <!-- 上限值 -->
        T2.LOWER_LIMIT = #lowerLimit#,   <!-- 下限值 -->
        T2.REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        T2.REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        T2.REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        T2.SPOT_CHECK_STANDARD_ID = #spotCheckStandardId#
        AND T2.DEVICE_CODE = #deviceCode#
        AND T2.DEL_FLAG = '0'
        AND T2.CHECK_PLAN_SUB_STATUS = '10'
        AND EXISTS (SELECT 1 FROM ${mevgSchema}.TVGDM0401 T1
        WHERE T1.CHECK_PLAN_ID = T2.CHECK_PLAN_ID
        AND T1.DEL_FLAG = '0'
        AND T1.CHECK_PLAN_STATUS != '40' )
    </update>

</sqlMap>