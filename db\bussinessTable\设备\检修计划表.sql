create table TMEDV0108
(
    OVERHAUL_PLAN_ID        VARCHAR(64)    default ' '    not null comment '检修计划编号',
    E_ARCHIVES_NO           VARCHAR(20)    default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME          VARCHAR(200)   default ' '    not null comment '设备名称',
    OVERHAUL_QUALITY        VARCHAR(16)    default ' '    not null comment '检修性质',
    OVERHAUL_TYPE           VARCHAR(2)     default ' '    not null comment '检修类别',
    OVERHAUL_START_DATE     VARCHAR(17)    default ' '    not null comment '计划检修开始日期',
    OVERHAUL_END_DATE       VARCHAR(17)    default ' '    not null comment '计划检修结束日期',
    OVERHAUL_NUMBER         DECIMAL(20, 8) default 0      not null comment '计划检修人数',
    OVERHAUL_TIME           DECIMAL(20, 8) default 0      not null comment '计划检修时间',
    OVERHAUL_PROJECT        VARCHAR(512)   default ' '    not null comment '计划检修项目',
    EXCEPTION_CONTACT_ID    VARCHAR(64)    default ' '    not null comment '异常联络单号',
    OVERHAUL_SOURCE         VARCHAR(1)     default ' '    not null comment '检修来源',
    OUTSOURCING_CONTACT_ID  VARCHAR(64)    default ' '    not null comment '委外联络单号',
    SECURITY_MEASURES       VARCHAR(200)   default ' '    not null comment '安全措施',
    ACCEPTANCE_CRITERIA     VARCHAR(512)   default ' '    not null comment '检修验收标准',
    IMPLEMENT_MAN_ID        VARCHAR(32)    default ' '    not null comment '实施人',
    IMPLEMENT_MAN_NAME      VARCHAR(128)   default ' '    not null comment '实施人姓名',
    OVERHAUL_PLAN_STATUS    VARCHAR(16)    default ' '    not null comment '检修计划状态',
    OVERHAUL_IMPLEMENT_DATE VARCHAR(20)    default ' '    not null comment '检修实施日期',
    ACTUAL_OVERHAUL_NUMBER  DECIMAL(20, 8) default 0      not null comment '实际检修人数',
    ACTUAL_OVERHAUL_TIME    DECIMAL(20, 8) default 0      not null comment '实际检修时间',
    IS_COMPLETE             VARCHAR(16)    default ' '    not null comment '是否完成',
    OVERHAUL_LEGACY_PROJECT VARCHAR(200)   default ' '    not null comment '遗留检修项目',
    IS_CONFORM_STANDARD     VARCHAR(16)    default ' '    not null comment '是否符合标准',
    RELEVANT_MEASURES       VARCHAR(200)   default ' '    not null comment '相关措施',
    IS_HOT                  VARCHAR(16)    default ' '    not null comment '是否动火',
    HOT_CARD_ID             VARCHAR(20)    default ' '    not null comment '动火证编号',
    OFFLINE_PARTS_GONE      VARCHAR(1)     default ' '    not null comment '下线零件去向',
    ACTUALS_REVISOR         VARCHAR(32)    default ' '    not null comment '检修实绩操作人',
    ACTUALS_TIME            VARCHAR(14)    default ' '    not null comment '检修实绩操作时间',
    ACTUALS_REVISOR_NAME    VARCHAR(100)   default ' '    not null comment '检修实绩操作人姓名',
    VOUCHER_NUM             VARCHAR(60)    default ' '    not null comment '依据凭单',
    APPR_STATUS             VARCHAR(2)     default ' '    not null comment '审批状态',
    ACTUAL_LEGACY_PROJECT   VARCHAR(200)   default ' '    not null comment '实际检修项目',
    -- 固定字段
    UUID                    VARCHAR(32)                   NOT NULL COMMENT '唯一编码',
    REC_CREATOR             VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME         VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR             VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME         VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID               VARCHAR(64)    DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG            VARCHAR(1)     DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='检修计划表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;


-- 增加ER图外键
ALTER TABLE TMEDV0108 ADD unique key(OVERHAUL_PLAN_ID);
ALTER TABLE TMEDV0108 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);
ALTER TABLE TMEDV0108 ADD FOREIGN KEY (EXCEPTION_CONTACT_ID) REFERENCES TMEDV0107(EXCEPTION_CONTACT_ID);