<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css">
    <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />
    <style>
        .btn{
            width: auto;
            flex: 0.5;
            display: flex;
            align-content: center;
            justify-content: space-around;
            height: auto;
        }
        .information-bill{
            flex: 0.4;
        }
        .information-bill ul{
            align-content: center;
            justify-content: space-around;
        }
        .container{
            width: 100%;
            margin: 140px 0px 50px;
            background-image: url("img/bg.png");
        }
        table tbody{
            max-height: 460px;
        }
    </style>
</head>

<body>
    <div class="wrapper" style="height: 100%;">
        <div class="header">
            <div id="logo" class="logo-baosight"></div>
<!--            <div class="title">工贸一体机登记</div>-->
            <div class="header-return">
                <button class="return-home-btn" onclick="returnHome()">返回主页</button>
            </div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li>1.登记车牌号</li>
                    <li class="arrow"></li>
                    <li class="fontblue">2.提单打印</li>
                </ul>
            </div>
        </div>
        <div style="float: left;height: 10px;margin-left: 5%;margin-top: 10px;">
            <p class="div-p-wxts" style="color: #FF0000;">*</p>温馨提示：
            请从列表中选择您需要打印的<p>提单</p>或<p>扫描提单电子版上的提单号二维码</p>/<p>手工输入提单号</p>后点击查询，然后再点击<p>“打印”</p>按钮。接下来，只需稍作等待，打印机就会为您输出所需的文件。
        </div>

        <div class="container">

            <div class="main">
                <div class="information-bill">
                    <ul>
                        <li><span class="tag">查询条件：</span></li>
                        <li style="display: flex;"><span class="tag">提单号</span><input id="bill_id" class="ipt1" type="text" placeholder="请扫描提单号或手工输入提单号"></li>
                    </ul>
                </div>
                <div id="keyboard" style="display: none;"></div>
                <div class="btn">
                    <button id="indexRead" class="btn2-flex-item" onclick="queryData()">查询</button>
                    <button class="btn2-flex-item" onclick="javascript :history.back(-1);">上一步</button>
                    <button class="btn2-flex-item" onclick="nextStep();">打印</button>
                </div>
            </div>
        </div>
        <div style="font-weight: 700;font-size: 25px;">
            <span>总重量: </span><span id="zongZL" style="color: #ff0000">0</span>、
            <span>总数量: </span><span id="zongSL" style="color: #ff0000">0</span>、
            <span>勾选总重量: </span><span id="GXZZL" style="color: #ff0000">0</span>、
            <span>勾选总数量: </span><span id="GXZSL" style="color: #ff0000">0</span>
        </div>
        <table class="printable">
            <thead>
                <tr>
                    <th style="width: 40px;">
                        <input type="checkbox" id="allcheck" name="colors" value="red">
                    </th>
                    <th>提单号</th>
                    <th>提单日期</th>
                    <th>始发站</th>
                    <th>终到站</th>
                    <th>提货重量</th>
                    <th>提货数量</th>
                    <th>客户名称</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/<EMAIL>"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script>
        var bill_id = localStorage.getItem("bill_id"); //提单号
        var dataList = [];

        function toggleCheckboxes(source,zl,sl) {
            var checkboxes = document.querySelectorAll('input[name="colorsList"]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = source.checked;
            }
        }

        function updateSelectAllCheckbox() {
            let allChecked = true;
            var checkboxes = document.querySelectorAll('input[name="colorsList"]');
            for (var i = 0; i < checkboxes.length; i++) {
                if (!checkboxes[i].checked) {
                    allChecked = false;
                    break;
                }
            }
            let sum = 0;
            let qtySum = 0;
            checkboxes.forEach((c,index) => {
                if (c.checked) {
                    const {totalWeight,totalPackQty} = dataList[index];
                    let [integer, decimal = ''] = totalWeight.split('.');
                    decimal = decimal.padEnd(3, '0').substring(0, 3);
                    sum += parseFloat(`${integer}.${decimal}`);

                    let [integerQ, decimalQ = ''] = totalPackQty.split('.');
                    decimalQ = decimalQ.padEnd(3, '0').substring(0, 3);
                    qtySum += parseFloat(`${integerQ}.${decimalQ}`);
                }
            });

            $("#GXZZL")[0].innerText = sum.toFixed(3);
            $("#GXZSL")[0].innerText = qtySum.toFixed(3);

            document.getElementById('allcheck').checked = allChecked;
        }

        $("#bill_id").click(function() {
            $('.virtualkeyboard').show();
        });

        document.addEventListener('DOMContentLoaded', function() {
            const keys = [
                'BL', 'ZK','1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                '退格', '清空'
            ];

            const keyboard = document.getElementById('keyboard');
            let activeInputField = null;

            keys.forEach(function(key) {
                const keyElement = document.createElement('div');
                keyElement.className = 'key';
                keyElement.textContent = key;
                keyboard.appendChild(keyElement);

                keyElement.addEventListener('click', function() {
                    if (!activeInputField) { return; }
                    if (key === '退格') {
                        activeInputField.value = activeInputField.value.slice(0, -1);
                        return;
                    }
                    if (key === '清空') {
                        activeInputField.value = '';
                        return;
                    }

                    activeInputField.value += key;
                    activeInputField.focus();  // 确保输入框保持焦点

                });
            });

            document.querySelectorAll('.ipt1').forEach(function(ipt2) {
                ipt2.addEventListener('focus', function() {
                    activeInputField = ipt2;
                    const rect = ipt2.getBoundingClientRect();
                    keyboard.style.display = 'flex';
                    keyboard.style.top = rect.bottom + window.scrollY + 'px';
                    keyboard.style.left = rect.left + window.scrollX + 'px';
                });
            });

            // 点击其他地方隐藏小键盘
            document.addEventListener('click', function(event) {
                if (!keyboard.contains(event.target) && !event.target.classList.contains('ipt1')) {
                    keyboard.style.display = 'none';
                    activeInputField = null;
                }
            });

            // 防止点击键盘时触发隐藏
            keyboard.addEventListener('click', function(event) {
                event.stopPropagation();
            });
        });


        //页面加载将车牌号与装卸货填充
        window.onload = function() {
            if (bill_id) {
                $("#bill_id").val(bill_id);
            }
            localStorage.removeItem("hand_big_type");
            localStorage.removeItem("loadingType");
            queryData();
        }
        // 查询信息
        function queryData(){
            showLoading("查询中");
            let queryData = {
                segNo:localStorage.getItem("segNo"),//账套
                idCard:localStorage.getItem("idCard"),//身份证号
                driverName:localStorage.getItem("idName"),//驾驶员姓名
                voucherNum:$("#bill_id").val(),//提单号
                serviceId: 'S_LI_RL_0061',
            }
            $.ajax({
                type: 'post',
                contentType: "application/json",
                url: ytjServerUrl,
                cache: false,
                data: JSON.stringify(queryData),
                complete :function(){},
                success: function(data){
                    closeLoading();
                    $('table tbody').html('');
                    if (!data || !data.__sys__ || data.__sys__.status == -1) {
                        Swal.fire({
                            title: data.__sys__.msg,
                            icon: "error",
                            confirmButtonText: "确定",
                        });
                        return;
                    }


                    if (!data.list || data.list.length == 0){
                        Swal.fire({
                            title: "未查询到提货委托的提单",
                            icon: "question",
                            confirmButtonText: "确定",
                        });
                        return;
                    }

                    dataList = data.list.map(r => {
                        const dateString = r.billingTime;
                        const formattedDate = dateString.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
                        r['dateStr'] = formattedDate;
                        return r;
                    });
                    let str = '';
                    for (let i = 0; i < data.list.length; i++) {
                        str += `<tr>
                                    <td style="width: 40px;"><input type="checkbox" name="colorsList" value="${data.list[i].ladingBillId}"></td>
                                    <td>${data.list[i].ladingBillId}</td>
                                    <td>${data.list[i].dateStr}</td>
                                    <td>${data.list[i].ladingSpotName}</td>
                                    <td>${data.list[i].destSpotName}</td>
                                    <td>${data.list[i].totalWeight}</td>
                                    <td>${data.list[i].totalPackQty}</td>
                                    <td>${data.list[i].userName}</td>
                                </tr>`
                    }
                    $('table').show();
                    $('table tbody').append(str);

                    // 总重量
                    let sum = dataList.reduce((acc, item) => {
                        const totalWeight = item.totalWeight;
                        let [integer, decimal = ''] = totalWeight.split('.');
                        decimal = decimal.padEnd(3, '0').substring(0, 3);
                        return acc + parseFloat(`${integer}.${decimal}`);
                    }, 0);
                    const zongZL = sum.toFixed(3);
                    $("#zongZL")[0].innerText = zongZL;

                    // 总数量
                    sum = dataList.reduce((acc, item) => {
                        const totalPackQty = item.totalPackQty;
                        let [integer, decimal = ''] = totalPackQty.split('.');
                        decimal = decimal.padEnd(3, '0').substring(0, 3);
                        return acc + parseFloat(`${integer}.${decimal}`);
                    }, 0);
                    const zongSL = sum.toFixed(3);
                    $("#zongSL")[0].innerText = zongSL;

                    document.getElementById('allcheck').checked = false;
                    document.getElementById('allcheck').addEventListener('click', function() {
                        $("#GXZZL")[0].innerText = this.checked ? zongZL : 0;
                        $("#GXZSL")[0].innerText = this.checked ? zongSL : 0;
                        toggleCheckboxes(this);
                    });

                    var checkboxes = document.querySelectorAll('input[name="colorsList"]');
                    checkboxes.forEach(function(checkbox) {
                        checkbox.addEventListener('click', function() {
                            updateSelectAllCheckbox();
                        });
                    });
                },
                error: () =>
                    Swal.fire({
                        title: "网络异常, 请联系管理员!",
                        icon: "error",
                        confirmButtonText: "确定",
                    }),
            });
        }
        //打印
        function nextStep() {
            let checkboxes = document.querySelectorAll('input[type="checkbox"]:checked:not(#allcheck)')
            let selectedIndices = []
            let str = ''
            checkboxes.forEach(function (checkbox,index){
                if (checkbox.checked) {
                    selectedIndices.push(dataList.find(function(item) {
                        return item.ladingBillId === checkbox.value;
                    }))
                }
            })
            selectedIndices.forEach(function (item, index) {
                // 在除了第一个元素之外的所有元素前添加逗号
                if (index > 0) {
                    str += "','";
                }
                str += item.ladingBillId;
            });
            if(selectedIndices.length == 0){
                Swal.fire({
                    title: "请选择需要打印的提单！",
                    icon: "warning",
                    confirmButtonText: "确定",
                });
                return
            }
            showLoading("打印中");
            const params = {
                segNo:localStorage.getItem("segNo"),//账套
                result: selectedIndices.filter(s => !!s),
                serviceId: 'S_LI_RL_0064',
            }
            $.ajax({
                type: 'post',
                contentType: "application/json",
                url: ytjServerUrl,
                cache: false,
                data: JSON.stringify(params),
                complete :function(){},
                success: function(data){
                    closeLoading();
                    if (!data || !data.__sys__ || data.__sys__.status == -1) {
                        Swal.fire({
                            title: data.__sys__.msg,
                            icon: "error",
                            confirmButtonText: "确定",
                        });
                        return;
                    }

                    if (data.docUrl && data.docUrl.length > 0) {
                        data.docUrl.forEach(d => window.open(d));
                        return;
                    }
                    Swal.fire({
                        title: `正在打印，共有${selectedIndices.length}份单据，请勿遗漏`,
                        icon: "success",
                        showConfirmButton: false,  // 隐藏确认按钮
                        timer: 3000,  // 设置定时器，3秒后自动关闭
                        timerProgressBar: true  // 显示进度条
                    }).then(() => {
                        returnHome();
                    });
                },
                error: () =>
                    Swal.fire({
                        title: "网络异常, 请联系管理员!",
                        icon: "error",
                        confirmButtonText: "确定",
                    }),
            });
        }
    </script>
</body>

</html>