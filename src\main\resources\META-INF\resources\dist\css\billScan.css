/* 设置input和datalist的统一宽度 */
.ipt-div-btn {
    position: relative;
    width: 100%;
}

input[type="text"] {
    width: 100%;  /* 使input框占满父容器宽度 */
    box-sizing: border-box;  /* 包括边框和内边距 */
    padding: 10px;  /* 设置内边距，调整输入框内容位置 */
}

#dropdown-list {
    position: absolute;
    top: 100%;  /* 使下拉框位于input框下方 */
    left: 0;
    width: 100%;  /* 使下拉框与input框同宽 */
    background: white;
    border: 1px solid #ccc;
    max-height: 200px;  /* 控制最大高度，防止下拉框过长 */
    overflow-y: auto;  /* 超出部分显示滚动条 */
    display: none;  /* 默认隐藏 */
    z-index: 1000;
}

#dropdown-list div {
    padding: 10px;
    cursor: pointer;
}

#dropdown-list div:hover {
    background-color: #f0f0f0;  /* hover效果 */
}