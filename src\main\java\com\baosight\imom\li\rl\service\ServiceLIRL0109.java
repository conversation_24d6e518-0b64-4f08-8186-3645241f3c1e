package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0109;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Author: 张博翔
 * @Description: ${预约通知维护}
 * @Date: 2024/8/13 13:26
 * @Version: 1.0
 */
public class ServiceLIRL0109 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        /*list.add("TEST_TYPE1");
        list.add("TEST_TYPE2");
        EiInfo eiInfo = CodeValueUtils.queryToBlock("QL00000", list, inInfo);*/
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0109().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            outInfo = super.query(inInfo, LIRL0109.QUERY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String countdownDuration = MapUtils.getString(hashMap, "countdownDuration", "");
                if (StringUtils.isBlank(countdownDuration)){
                    hashMap.put("countdownDuration", "0");
                }
                String strSeqTypeId = "TLIRL_SEQ09";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0109.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));

                String countdownDuration = MapUtils.getString(hashMap, "countdownDuration", "");
                if (StringUtils.isBlank(countdownDuration)){
                    hashMap.put("countdownDuration", "0");
                }
                List<LIRL0109> query = dao.query(LIRL0109.QUERY_ALL, map);
                for (LIRL0109 LIRL0109 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0109);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0109.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0109> query = dao.query(LIRL0109.QUERY_ALL, map);
                for (LIRL0109 LIRL0109 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0109);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0109.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0109> query = dao.query(LIRL0109.QUERY_ALL, map);
                for (LIRL0109 LIRL0109 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0109);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "20");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0109.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0109> query = dao.query(LIRL0109.QUERY_ALL, map);
                for (LIRL0109 LIRL0109 : query) {
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusConfirmNo(inInfo, LIRL0109);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "10");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0109.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
