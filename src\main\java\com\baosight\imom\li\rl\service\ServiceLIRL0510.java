/**
* Generate time : 2025-07-04 09:28:12
* Version : 1.0
*/
package com.baosight.imom.li.rl.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.li.rl.dao.LIRL0502;
import com.baosight.imom.li.rl.dao.LIRL0503;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtil;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 我的配单查询
 */
public class ServiceLIRL0510 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0502().eiMetadata);
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIRL0502.QUERY_PAGE, new LIRL0502());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 根据配车单查询配车单明细
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySubResult(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIRL0503.QUERY_BY_ALLOCATE_VEHICLE_NO, new LIRL0503(), false, new LIRL0503().eiMetadata, EiConstant.resultBlock, "subResult", "subResult", LIRL0503.COUNT_BY_ALLOCATE_VEHICLE_NO);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 定时任务，每天20:00执行，检查状态不为完成的配车单捆包是否IMC已全部出库，将配车单状态修改为完成
     * jobId:J_LI_0014
     * serviceId:S_LI_RL_1076
     * @return
     */
    public EiInfo autoCompleteOrderIfImcOutStock(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //记录返回信息
            StringBuilder sb = new StringBuilder("本次处理配车单号:");
            //重庆宝钢
            String segNo = "JC000000";
            //查询所有状态不为完成的配车单明细
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);//重庆
            List<LIRL0503> lirl0503s = dao.queryAll(LIRL0503.QUERY_BY_STATUS_NOT_COMPLETE, queryMap);

            //根据配车单号进行分组，每个配车单捆包调一次接口
            Map<String, List<LIRL0503>> groupedData = lirl0503s.stream()
                    .collect(Collectors.groupingBy(LIRL0503::getAllocateVehicleNo));

            //调用IMC查询捆包状态
            for (Map.Entry<String, List<LIRL0503>> entry : groupedData.entrySet()) {
                //配车单号
                String allocateVehicleNo = entry.getKey();
                // 对应的配车单明细列表
                List<LIRL0503> lirl0503List = entry.getValue();
                // 获取所有 packId
                List<String> packIds = lirl0503List.stream()
                        .map(LIRL0503::getPackId)
                        .collect(Collectors.toList());
                // 将 List<String> 以、号拼接
                String packIdStr = String.join("、", packIds);

                // 获取所有 packId
                List<String> matInnerIds = lirl0503List.stream()
                        .map(LIRL0503::getMatInnerId)
                        .collect(Collectors.toList());
                // 将 List<String> 以、号拼接
                String matInnerIdStr = String.join("、", matInnerIds);

                // 在这里对每个配车单号和对应的明细列表进行处理
                EiInfo sendInfo = new EiInfo();
                sendInfo.set("segNo", segNo);
                sendInfo.set("packId", packIdStr); // 捆包号
                sendInfo.set("matInnerId", matInnerIdStr); // 材料管理号
                sendInfo.set("entityStockStatus", "30"); //实物库存状态
                sendInfo.set(EiConstant.serviceId, "S_UE_WR_1001");
                sendInfo.set("clientId", ImcGlobalUtils.CLIENT_ID);
                sendInfo.set("clientSecret", ImcGlobalUtils.CLIENT_SECRET);
                //传入参数拼接打印日志
                String sendParam = sendInfo.toJSONString();
                sendInfo = EServiceManager.call(sendInfo, TokenUtils.getXplatToken());
                //打印日志到elk
                log(DateUtil.getTimeNow(new Date()) + "：" + "J_LI_0014查询渠道库存材料信息传入参数：" + sendParam + "\n" + "查询渠道库存材料信息返回参数：" + sendInfo.toJSONString());
                //输出到应用日志
                System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "J_LI_0014查询渠道库存材料信息传入参数：" + sendParam + "\n" + "查询渠道库存材料信息返回参数：" + sendInfo.toJSONString());

                //返回报错，不处理
                if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    sb.append(allocateVehicleNo).append(":库存返回报错,不处理;");
                    continue;
                }

                //获取返回信息失败，不处理
                String outJSON = sendInfo.getString("messageBody");
                if (StringUtils.isBlank(outJSON)) {
                    sb.append(allocateVehicleNo).append(":库存返回报错,不处理;");
                    continue;
                }

                //获取返回数据
                Map returnMap = JSONObject.fromObject(outJSON);
                List<Map<String, Object>> result = (List<Map<String, Object>>) returnMap.get("result");

                //未返回明细数据,也不继续处理
                if(CollectionUtils.isEmpty(result)){
                    sb.append(allocateVehicleNo).append(":IMC捆包未出库,不处理;");
                    continue;
                }
                //若查询出库捆包返回的条数与配车单明细列表长度一致，则说明所有捆包已出库
                if (result.size() == lirl0503List.size()) {
                    //将配车单主表状态修改为完成
                    Map updateMap = new HashMap<>();
                    updateMap.put("segNo", segNo);
                    updateMap.put("allocateVehicleNo", allocateVehicleNo);
                    updateMap.put("status", "99");
                    updateMap.put("remark", "IMC出库完成,带动IMOM完成！");
                    RecordUtils.setRevisorSys(updateMap);
                    dao.update(LIRL0502.UPDATE_COMPLETE_BY_ALLOCATE_VEHICLE_NO, updateMap);

                    //将配车单明细状态修改为完成
                    dao.update(LIRL0503.UPDATE_COMPLETE_BY_ALLOCATE_VEHICLE_NO, updateMap);

                    //拼接返回信息
                    sb.append(allocateVehicleNo).append(":【全部出库】").append(";");
                } else {
                    //未全部出库，将返回的捆包明细状态修改为完成
                    List<Map<String, Object>> extractedList = result.stream()
                            .map(map -> {
                                Map<String, Object> newMap = new HashMap<>();
                                newMap.put("segNo", map.get("segNo"));
                                newMap.put("packId", map.get("packId"));
                                newMap.put("matInnerId", map.get("matInnerId"));
                                newMap.put("allocateVehicleNo", allocateVehicleNo);
                                newMap.put("status", "99");
                                newMap.put("remark", "IMC出库完成,带动IMOM完成！");
                                RecordUtils.setRevisorSys(newMap);
                                return newMap;
                            })
                            .collect(Collectors.toList());
                    dao.updateBatch(LIRL0503.UPDATE_COMPLETE_BY_ALLOCATE_VEHICLE_NO, extractedList);

                    //拼接返回信息
                    List<String> packIdList = result.stream()
                            .map(map -> map.get("packId"))
                            .filter(Objects::nonNull) // 避免 null 值
                            .map(Object::toString)
                            .collect(Collectors.toList());

                    sb.append(allocateVehicleNo).append(":处理明细【").append(packIdList).append("】;");
                }
            }


            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理完成！" + sb);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}