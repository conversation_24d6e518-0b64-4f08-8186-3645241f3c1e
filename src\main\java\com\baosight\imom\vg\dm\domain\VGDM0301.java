package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.constants.CacheConstant;
import com.baosight.imom.common.vg.domain.Tvgdm0301;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备采集点位清单表
 */
public class VGDM0301 extends Tvgdm0301 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0301.query";
    /**
     * 根据tagId查询
     */
    public static final String QUERY_BY_ID = "VGDM0301.queryById";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0301.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0301.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0301.update";
    /**
     * 修改
     */
    public static final String UPDATE_TAG = "VGDM0301.updateTag";
    /**
     * 删除
     */
    public static final String DELETE = "VGDM0301.delete";

    /**
     * 从数据库查询点位信息
     *
     * @param dao   dao
     * @param tagId tagId
     * @return 点位信息或null
     */
    private static VGDM0301 queryFromDb(Dao dao, String tagId) {
        Map<String, String> queryMap = new HashMap<>(1);
        queryMap.put("tagId", tagId);
        List list = dao.query(QUERY_BY_ID, queryMap);
        if (list != null && !list.isEmpty()) {
            return (VGDM0301) list.get(0);
        }
        return null;
    }

    /**
     * 根据tagId获取点位信息
     * 1.从缓存中取
     * 2.缓存中没有时从数据库取并更新缓存
     * 3.都没有返回null
     *
     * @param dao   dao
     * @param tagId tagId
     * @return 点位信息
     */
    public static VGDM0301 queryWithCache(Dao dao, String tagId) {
        Map cache = CacheManager.getCache(CacheConstant.CACHE_TAG);
        Object data = cache.get(tagId);
        if (data == null) {
            VGDM0301 vgdm0301 = queryFromDb(dao, tagId);
            if (vgdm0301 == null) {
                return null;
            }
            cache.put(tagId, vgdm0301);
            return vgdm0301;
        }
        return (VGDM0301) data;
    }
}
