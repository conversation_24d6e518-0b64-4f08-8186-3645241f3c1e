$(function () {

    IPLATUI.EFGrid = {
        "result": {
            /**
             * EFGrid新增后触发的事件
             */
            afterAdd: function (e) {
                $.each(e.items, function (index, item) {
                    item.internalCode = "";
                });
            },
            loadComplete: function (grid) {

                $("#QUERY").on("click", function () {
                    resultGrid.dataSource.page(1);
                });

            },
            //双击选中
            onRowDblClick: function (e) {
                //获取双击行的数据
                resultGrid.setCheckedRows(e.row);
                var windowId = $("#inqu_status-0-windowId").val();
                //关闭下拉框
                if (IPLAT.isBlankString(windowId)) {
                    windowId = "department";
                }
                //关闭下拉框
                window.parent[windowId + "Window"].close();
            }
        }
    };

});

