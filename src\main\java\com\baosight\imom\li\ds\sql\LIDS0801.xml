<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-09 13:34:15
   		Version :  1.0
		tableName :meli.tlids0801 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 WAREHOUSE_CODE  VARCHAR   NOT NULL, 
		 WAREHOUSE_NAME  VARCHAR   NOT NULL, 
		 AREA_TYPE  VARCHAR   NOT NULL, 
		 AREA_CODE  VARCHAR   NOT NULL, 
		 AREA_NAME  VARCHAR   NOT NULL, 
		 MOULD_ID  VARCHAR   NOT NULL, 
		 MOULD_NAME  VARCHAR   NOT NULL, 
		 X_POSITION  VARCHAR, 
		 Y_POSITION  VARCHAR, 
		 Z_POSITION  VARCHAR, 
		 MOULD_SPEC_WEIGHT_SUM  DECIMAL   NOT NULL,
		 POS_DIR_CODE  VARCHAR, 
		 CRANE_RESULT_ID  VARCHAR, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL   primarykey
	-->
<sqlMap namespace="LIDS0801">

	<sql id="condition">
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME LIKE '%$warehouseName$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldId">
			MOULD_ID LIKE '%$mouldId$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldName">
			MOULD_NAME LIKE '%$mouldName$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isEmpty prepend=" AND " property="status">
			STATUS > '00'
			AND DEL_FLAG = '0'
		</isEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.ds.domain.LIDS0801">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
				"segName", <!-- 业务单元简称 -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
				AREA_TYPE	as "areaType",  <!-- 区域类型 -->
				AREA_CODE	as "areaCode",  <!-- 区域代码 -->
				AREA_NAME	as "areaName",  <!-- 区域名称 -->
				MOULD_ID	as "mouldId",  <!-- 模具ID -->
				MOULD_NAME	as "mouldName",  <!-- 模具名称 -->
				X_POSITION	as "x_position",  <!-- X轴坐标 -->
				Y_POSITION	as "y_position",  <!-- Y轴坐标 -->
				Z_POSITION	as "z_position",  <!-- Z轴坐标 -->
				MOULD_SPEC_WEIGHT_SUM	as "mouldSpecWeightSum",  <!-- 模具重量 -->
				POS_DIR_CODE	as "posDirCode",  <!-- 层数标记 -->
				CRANE_RESULT_ID	as "craneResultId",  <!-- 行车实绩单号 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids0801 t WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			REC_CREATE_TIME DESC
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0801 WHERE 1=1
		<include refid="condition"/>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaType">
			AREA_TYPE = #areaType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaCode">
			AREA_CODE = #areaCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaName">
			AREA_NAME = #areaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldId">
			MOULD_ID = #mouldId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldName">
			MOULD_NAME = #mouldName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_position">
			X_POSITION = #x_position#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_position">
			Y_POSITION = #y_position#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="z_position">
			Z_POSITION = #z_position#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldSpecWeightSum">
			MOULD_SPEC_WEIGHT_SUM = #mouldSpecWeightSum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="posDirCode">
			POS_DIR_CODE = #posDirCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneResultId">
			CRANE_RESULT_ID = #craneResultId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0801 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										WAREHOUSE_NAME,  <!-- 仓库名称 -->
										AREA_TYPE,  <!-- 区域类型 -->
										AREA_CODE,  <!-- 区域代码 -->
										AREA_NAME,  <!-- 区域名称 -->
										MOULD_ID,  <!-- 模具ID -->
										MOULD_NAME,  <!-- 模具名称 -->
										X_POSITION,  <!-- X轴坐标 -->
										Y_POSITION,  <!-- Y轴坐标 -->
										Z_POSITION,  <!-- Z轴坐标 -->
										MOULD_SPEC_WEIGHT_SUM,  <!-- 模具重量 -->
										POS_DIR_CODE,  <!-- 层数标记 -->
										CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #warehouseCode#, #warehouseName#, #areaType#, #areaCode#, #areaName#, #mouldId#, #mouldName#, #x_position#, #y_position#, #z_position#, #mouldSpecWeightSum#, #posDirCode#, #craneResultId#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0801 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlids0801
		SET
		SEG_NO = #segNo#,   <!-- 系统账套 -->
		UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
		WAREHOUSE_CODE = #warehouseCode#,   <!-- 仓库代码 -->
		WAREHOUSE_NAME = #warehouseName#,   <!-- 仓库名称 -->
		AREA_TYPE = #areaType#,   <!-- 区域类型 -->
		AREA_CODE = #areaCode#,   <!-- 区域代码 -->
		AREA_NAME = #areaName#,   <!-- 区域名称 -->
		MOULD_ID = #mouldId#,   <!-- 模具ID -->
		MOULD_NAME = #mouldName#,   <!-- 模具名称 -->
		X_POSITION = #x_position#,   <!-- X轴坐标 -->
		Y_POSITION = #y_position#,   <!-- Y轴坐标 -->
		Z_POSITION = #z_position#,   <!-- Z轴坐标 -->
		MOULD_SPEC_WEIGHT_SUM = #mouldSpecWeightSum#,   <!-- 模具重量 -->
		POS_DIR_CODE = #posDirCode#,   <!-- 层数标记 -->
		CRANE_RESULT_ID = #craneResultId#,   <!-- 行车实绩单号 -->
		STATUS = #status#,   <!-- 状态 -->
		REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
		REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
		REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
		ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
		TENANT_USER = #tenantUser#,   <!-- 租户 -->
		DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
		WHERE
		SEG_NO = #segNo#
		AND MOULD_ID = #mouldId#
		AND UUID = #uuid#
	</update>

<!--	根据XY，区域查询模具-->
	<select id="queryByXY" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.ds.domain.LIDS0801">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
		WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
		WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
		AREA_TYPE	as "areaType",  <!-- 区域类型 -->
		AREA_CODE	as "areaCode",  <!-- 区域代码 -->
		AREA_NAME	as "areaName",  <!-- 区域名称 -->
		MOULD_ID	as "mouldId",  <!-- 模具ID -->
		MOULD_NAME	as "mouldName",  <!-- 模具名称 -->
		X_POSITION	as "x_position",  <!-- X轴坐标 -->
		Y_POSITION	as "y_position",  <!-- Y轴坐标 -->
		Z_POSITION	as "z_position",  <!-- Z轴坐标 -->
		MOULD_SPEC_WEIGHT_SUM	as "mouldSpecWeightSum",  <!-- 模具重量 -->
		POS_DIR_CODE	as "posDirCode",  <!-- 层数标记 -->
		CRANE_RESULT_ID	as "craneResultId",  <!-- 行车实绩单号 -->
		STATUS	as "status",  <!-- 状态 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		TENANT_USER	as "tenantUser",  <!-- 租户 -->
		DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
		UUID	as "uuid" <!-- ID -->
		FROM meli.tlids0801 WHERE 1=1
		AND SEG_NO = #segNo#
		AND X_POSITION = #XPosition#
		AND Y_POSITION = #YPosition#
		<isNotEmpty prepend=" AND " property=" ZPosition ">
			Z_POSITION = #ZPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaType">
			AREA_TYPE = #areaType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaCode">
			AREA_CODE = #areaCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaName">
			AREA_NAME = #areaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldId">
			MOULD_ID = #mouldId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldName">
			MOULD_NAME = #mouldName#
		</isNotEmpty>
	</select>

<!--	行车释放时修改模具库存相关字段-->
	<update id="updateInventoryMouldInfo">
		UPDATE meli.tlids0801
		SET
		AREA_TYPE = #areaType#,   <!-- 区域类型 -->
		AREA_CODE = #areaCode#,   <!-- 区域代码 -->
		AREA_NAME = #areaName#,   <!-- 区域名称 -->
		<isEmpty property="x_position">
			X_POSITION = NULL,   <!-- X轴坐标 -->
		</isEmpty>
		<isEmpty property="y_position">
			Y_POSITION = NULL,   <!-- Y轴坐标 -->
		</isEmpty>
		<isEmpty property="z_position">
			Z_POSITION = NULL,   <!-- Z轴坐标 -->
		</isEmpty>

		<isNotEmpty property="x_position">
			X_POSITION = #x_position#,   <!-- X轴坐标 -->
		</isNotEmpty>
		<isNotEmpty property="y_position">
			Y_POSITION = #y_position#,   <!-- Y轴坐标 -->
		</isNotEmpty>
		<isNotEmpty property="z_position">
			Z_POSITION = #z_position#,   <!-- Z轴坐标 -->
		</isNotEmpty>
		MOULD_SPEC_WEIGHT_SUM = #mouldSpecWeightSum#,   <!-- 模具重量 -->
		POS_DIR_CODE = #posDirCode#,   <!-- 层数标记 -->
		CRANE_RESULT_ID = #craneResultId#,   <!-- 行车实绩单号 -->
		STATUS = #status#,   <!-- 状态 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#   <!-- 删除标记 -->
		</isNotEmpty>
		WHERE
		SEG_NO = #segNo#
		AND MOULD_ID = #mouldId#
		AND STATUS = '10'
		AND DEL_FLAG = '0'
	</update>
</sqlMap>