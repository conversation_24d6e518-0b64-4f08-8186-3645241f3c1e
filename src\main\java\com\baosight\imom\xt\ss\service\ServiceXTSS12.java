package com.baosight.imom.xt.ss.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.xt.ss.domain.XTSS12;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.StringUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * 审批属性配置页面后台
 *
 * @version 1.0 （开发版本）.
 * @since 1.8 （JDK版本号）.
 */
public class ServiceXTSS12 extends ServiceBase {
    public static final String UNIT_RESULT1 = "unitResult1";
    public static final String RESULT_NAME = "resultName";

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0101 初始化页面
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(MesConstant.Iplat.RESULT_BLOCK).addBlockMeta(new XTSS12().eiMetadata);
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS12().eiMetadata);
        return inInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0112 查询功能
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
//        inInfo = ImcCommonUtils.DatasAccessUtil(new String[]{"MPSM0222","QUERY"}, inInfo);
//        if (EiConstant.STATUS_FAILURE == inInfo.getStatus()){
//            //获取公共查询条件失败，直接return
//            return inInfo;
//        }
        EiInfo outInfo = null;
        String appId = String.valueOf(inInfo.getBlock("inqu_status").getRow(0).get("apprId"));
        String appNmae = String.valueOf(inInfo.getBlock("inqu_status").getRow(0).get("apprName"));

        //根据查询审批工号或者姓名，可以显示审批人所对应的所有审批项目
        if (org.apache.commons.lang.StringUtils.isNotBlank(appId) || org.apache.commons.lang.StringUtils.isNotBlank(appNmae)) {
            outInfo = super.query(inInfo, "XTSS12.queryApprId", new XTSS12(), false, new XTSS12().eiMetadata, "inqu_status", "result0", "result0");

        } else {
            outInfo = super.query(inInfo, "XTSS12.query", new XTSS12(), false, new XTSS12().eiMetadata, "inqu_status", "result0", "result0");
        }

        outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS12().eiMetadata);
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0113 查询详情信息功能
     */
    public EiInfo queryDetail(EiInfo inInfo) {
        String processId = inInfo.getString("inqu_status-0-processId");
        if (!StringUtils.isNotEmpty(processId)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("缺少流程ID！");
            return inInfo;
        }
        EiInfo outInfo = super.query(inInfo, "XTSS12.queryDetail", new XTSS12());
        return outInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0114 修改功能
     */
    public EiInfo update1(EiInfo inInfo) {
        EiInfo info = new EiInfo();
        try {
            String messageText = "";
            String processId = "";
            String nodeNo = "";
            String segNo = "";
           List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                // 记录修改人,修改时间
                RecordUtils.setRevisor(hashMap);

                processId = (String) Optional.ofNullable(hashMap.get("processId")).orElse("");
                nodeNo = (String) Optional.ofNullable(hashMap.get("nodeNo")).orElse("");
                String apprId = (String) Optional.ofNullable(hashMap.get("apprId")).orElse("");
                String apprName = (String) Optional.ofNullable(hashMap.get("apprName")).orElse("");
                segNo = (String) Optional.ofNullable(hashMap.get("segNo")).orElse("");
                String uuid = (String) Optional.ofNullable(hashMap.get("uuid")).orElse("");
                String protRoleType = MapUtils.getString(hashMap, "protRoleType", "");
                Map map = new HashMap(16);
                map.put("processId", processId);
                map.put("nodeNo", nodeNo);
                map.put("apprId", apprId);
                map.put("segNo", segNo);
                map.put("uuid", uuid);
                map.put("protRoleType", protRoleType);
                int switchNameCount = super.count("XTSS12.queryProcessNodeKey", map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("同一个业务单元代码的审批人已存在");
                    return inInfo;
                }
            }

            inInfo = super.update(inInfo, "XTSS12.update");
            HashMap queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("processId", processId);
            queryMap.put("nodeNo", nodeNo);
            inInfo.addRow("inqu_status", queryMap);
            info = this.queryDetail(inInfo);
            // 返回成功状态和消息
            info.setStatus(EiConstant.STATUS_DEFAULT);
            info.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            info.setStatus(EiConstant.STATUS_FAILURE);
            info.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return info;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0115 删除功能
     */
    public EiInfo delete1(EiInfo inInfo) {
        try {
            inInfo = super.delete(inInfo, "XTSS12.delete");

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0116 新增功能
     */
    public EiInfo insert1(EiInfo inInfo) {
        EiInfo info = new EiInfo();
        try {
            String messageText = "";
            String processId = "";
            String nodeNo = "";
            String segNo = "";
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //生成UUID唯一编码
                RecordUtils.setCreator(hashMap);

                processId = (String) Optional.ofNullable(hashMap.get("processId")).orElse("");
                nodeNo = (String) Optional.ofNullable(hashMap.get("nodeNo")).orElse("");
                String apprId = (String) Optional.ofNullable(hashMap.get("apprId")).orElse("");
                String apprName = (String) Optional.ofNullable(hashMap.get("apprName")).orElse("");
                segNo = (String) Optional.ofNullable(hashMap.get("segNo")).orElse("");
                String protRoleType = MapUtils.getString(hashMap, "protRoleType", "");
                Map map = new HashMap(16);
                map.put("processId", processId);
                map.put("nodeNo", nodeNo);
                map.put("apprId", apprId);
                map.put("segNo", segNo);
                map.put("protRoleType", protRoleType);
                int switchNameCount = super.count("XTSS12.queryProcessNodeKey", map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("同一个业务单元代码的审批人已存在");
                    return inInfo;
                }
            }

            inInfo = super.insert(inInfo, "XTSS12.insert");

            HashMap queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("processId", processId);
            queryMap.put("nodeNo", nodeNo);
            inInfo.addRow("inqu_status", queryMap);

            info = this.queryDetail(inInfo);
            // 返回成功状态和消息
            info.setStatus(EiConstant.STATUS_DEFAULT);
            info.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            info.setStatus(EiConstant.STATUS_FAILURE);
            info.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return info;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0117 根据生产开关名称获取生产开关值功能
     */
    public EiInfo querySwitchValueByName(EiInfo inInfo) {
        Map map = new HashMap(16);
        String processId = inInfo.get("inqu_status-0-processId").toString();
        map.put("processId", processId);
        List<HashMap> list = new ArrayList<>();

        List<XTSS12> switcInfo = this.dao.query("XTSS12.queryOneSwitchInfo", map);
        if (switcInfo != null && switcInfo.size() > 0) {
            XTSS12 switc = switcInfo.get(0);
           /* String newChar = switc.getProcessSwitchValueDesc().replace("：", ":").replace("；", ";");
            String[] splitStr = newChar.split(";");
            for (int i = 0; i < splitStr.length; i++) {
                HashMap hashMap = new HashMap(16);
                String[] splitValue = splitStr[i].split(":");
                hashMap.put("processSwitchValue", splitValue[0]);
                hashMap.put("processSwitchValueDesc", splitValue[1]);
                list.add(hashMap);
            }*/
        }
        inInfo.addBlock(MesConstant.Iplat.RESULT3_BLOCK).addRows(list);
        inInfo.getBlock(MesConstant.Iplat.RESULT3_BLOCK).set(EiConstant.countStr, list.size());
        inInfo.getBlock(MesConstant.Iplat.RESULT3_BLOCK).set(EiConstant.offsetStr, 0);
        inInfo.getBlock(MesConstant.Iplat.RESULT3_BLOCK).set(EiConstant.limitStr, 10);
        return inInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0118 开关定义修改功能
     */
    public EiInfo update2(EiInfo inInfo) {
        try {
            String messageText = "";
            List<HashMap> listHashMap = inInfo.getBlock(MesConstant.Iplat.RESULT0_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                String processId = (String) Optional.ofNullable(hashMap.get("processId")).orElse("");
                String nodeNo = (String) Optional.ofNullable(hashMap.get("nodeNo")).orElse("");
                Map map = new HashMap(16);
                map.put("processId", processId);
                map.put("nodeNo", nodeNo);
                // 根据开关定义名称获取数据判重
                map.put("uuid", hashMap.get("uuid"));
                int switchNameCount = super.count("XTSS12.queryProcessSwitchNameCount", map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("此审批配置节点ID已存在");
                    return inInfo;
                }
                // 根据开关定义名称获取开关配置总数
                int switchSettingCount = super.count("XTSS12.querySwitchSettingCount", map);
                if (switchSettingCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("数据已存在");
                    return inInfo;
                }


                // 记录修改人,修改时间
                RecordUtils.setRevisor(hashMap);
                dao.update("XTSS12.update2", hashMap);
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0119 开关定义删除功能
     */
    public EiInfo delete2(EiInfo inInfo) {
        try {
            String messageText = "";
            List<HashMap> listHashMap = inInfo.getBlock(MesConstant.Iplat.RESULT0_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                String processId = (String) Optional.ofNullable(hashMap.get("processId")).orElse("");
                String nodeNo = (String) Optional.ofNullable(hashMap.get("nodeNo")).orElse("");
                Map map = new HashMap(16);
                map.put("processId", processId);
                map.put("nodeNo", nodeNo);
                // 根据开关定义名称获取开关配置总数
                int switchSettingCount = super.count("XTSS12.querySwitchSettingCount", map);
                if (switchSettingCount > 0) {
//                    messageText = ErrorMsgUtils.getMassage(new String[]{
//                            "M00000561", "MPSM0222", "update2", "S_VI_SM_0119"
//                    });
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("数据重复");
                    return inInfo;
                }

                dao.delete("XTSS12.delete2", hashMap);
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * @param inInfo
     * @return
     * @Service:S_VI_SM_0120 开关定义新增功能
     */
    public EiInfo insert2(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(MesConstant.Iplat.RESULT0_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                // 根据开关定义名称获取数据判重
                Map map = new HashMap(16);
                String processId = (String) Optional.ofNullable(hashMap.get("processId")).orElse("");
                String nodeNo = (String) Optional.ofNullable(hashMap.get("nodeNo")).orElse("");
                map.put("processId", processId);
                map.put("nodeNo", nodeNo);
                int switchNameCount = super.count("XTSS12.queryProcessSwitchNameCount", map);
                if (switchNameCount > 0) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("此审批配置节点ID已存在");
                    return inInfo;
                }

                RecordUtils.setCreator(hashMap);
                dao.insert("XTSS12.insert", hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 实例id下拉框
     *
     * @param eiInfo
     * @return
     */
    public EiInfo queryProcessKey(EiInfo eiInfo) {
        String resultName = "result2";
        eiInfo.addBlock("inqu2_status");
        eiInfo.addBlock(resultName);
        //后台要用super.query调用
        EiInfo outinInfo = super.query(eiInfo, "XTSS12.queryProcessKey", null, false, null, "inqu2_status", resultName, resultName);
        return outinInfo;
    }

}
