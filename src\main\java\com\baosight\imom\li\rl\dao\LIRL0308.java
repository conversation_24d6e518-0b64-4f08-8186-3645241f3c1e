/**
 * Generate time : 2024-10-29 16:11:38
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0308
 *
 */
public class LIRL0308 extends DaoEPBase {
    public static final String QUERY = "LIRL0308.query";
    public static final String QUERY_PAGE = "LIRL0308.queryPage";
    public static final String QUERY_WEIGHT = "LIRL0308.queryWeight";
    public static final String QUERY_PRINT_COUNT = "LIRL0308.queryPrintCount";
    public static final String QUERY_NO_SIGNATURE_DATA = "LIRL0308.queryNoSignatureData";
    public static final String QUERY_NO_SIGNATURE_COUNT = "LIRL0308.queryNoSignatureCount";
    public static final String QUERY_ALL_SIGNATURE_DATA = "LIRL0308.queryAllSignatureData";
    public static final String QUERY_NOT_EXIST_SIGNATURE_FLAG = "LIRL0308.queryNotExistSignatureFlag";
    public static final String QUERY_INFO = "LIRL0308.queryInfo";
    public static final String COUNT = "LIRL0308.count";
    public static final String QUERY_PUTOUT_ID = "LIRL0308.queryPutoutId";
    public static final String PUTOUT_QUERY = "LIRL0308.putoutQuery";
    public static final String QUERY_POUT_INFO = "LIRL0308.queryPoutInfo";
    public static final String QUERY_VEHICLE_NO_TRACE = "LIRL0308.queryVehicleNoTrace";
    public static final String QUERY_LOADING_PERFORMANCE = "LIRL0308.queryLoadingPerformance";
    public static final String QUERY_LOADING_PERFORMANCE_ALL = "LIRL0308.queryLoadingPerformanceAll";

    //查询导出明细
    public static final String QUERY_EXPROT_DETAILS = "LIRL0308.queryExprotDetails";
    public static final String INSERT = "LIRL0308.insert";
    public static final String UPDATE = "LIRL0308.update";
    public static final String UPDATE_INFO = "LIRL0308.updateInfo";
    public static final String UPDATE_BYCAR_TRACE_NO = "LIRL0308.updateBycarTraceNo";
    public static final String UPDATE_BY_PUTOUT_BY_PACK_INFO = "LIRL0308.updateByPutoutByPackInfo";
    public static final String DELETE = "LIRL0308.delete";
    public static final String QUERY_B_LINFO = "LIRL0308.queryBLinfo";
    public static final String QUERY_P_CINFO = "LIRL0308.queryPCinfo";
    public static final String UPDATE_PRINT_COUNT = "LIRL0308.updatePrintCount";
    public static final String QUERY_ALL_VOUCHER_NUM_PACK = "LIRL0308.queryAllVoucherNumPack";
    public static final String QUERY_TOTAL_QUATITY_WEIGHT = "LIRL0308.queryTotalQuatityWeight";
    public static final String QUERY_TOTAL_QUATITY_WEIGHT_CQ = "LIRL0308.queryTotalQuatityWeightCq";
    public static final String QUERY_TOTAL_QUATITY_WEIGHT_KF = "LIRL0308.queryTotalQuatityWeightKf";
    public static final String QUERY_TOTAL_QUATITY_WEIGHT_KF_PUTIN = "LIRL0308.queryTotalQuatityWeightKfPutIn";
    public static final String UPDATE_VARIANCE_DETAILS = "LIRL0308.updateVarianceDetails";
    private String segNo = " ";        /* 账套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String carTraceNo = " ";        /* 车辆跟踪号*/
    private String putinId = " ";        /* 入库单号（无计划入库为空）*/
    private String putoutId = " ";        /* 出库单号*/
    private String voucherNum = " ";        /* 提单号（多个）*/
    private String driverName = " ";        /* 司机姓名*/
    private String driverTel = " ";        /* 司机手机号*/
    private String driverIdentity = " ";        /* 司机身份证号*/
    private String vehicleNo = " ";        /* 车牌号*/
    private String deliverType = " ";        /* 交货方式（代运、自提）*/
    private String deliverName = " ";        /* 交货方式名称*/
    private String packId = " ";        /* 捆包号*/
    private String matInnerId = " ";        /* 材料管理好（无计划入库为空）*/
    private String factoryOrderNum = " ";        /* 钢厂资源号*/
    private String putInOutFlag = " ";        /* 出入库标记（入库：1，出库：2）*/
    private String prodTypeId = " ";        /* 品种附属码*/
    private String prodTypeName = " ";        /* 品种附属码名称*/
    private String shopsign = " ";        /* 牌号*/
    private String specDesc = " ";        /* 规格*/
    private BigDecimal weight = new BigDecimal(0.000000);        /* 重量*/
    private BigDecimal quantity = new BigDecimal(0.000000);        /* 数量*/
    private String warehouseCode = " ";        /* 仓库代码*/
    private String warehouseName = " ";        /* 仓库名称*/
    private String customerId = " ";        /* 客户代码*/
    private String customerName = " ";        /* 客户名称*/
    private String locationId = " ";        /* 库位代码*/
    private String locationName = " ";        /* 库位名称*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private String putoutDate = " ";        /* 出库日期*/
    private String signatureFlag = "0";        /* 签收标记*/
    private String leaveFactoryDate = " ";        /* 出厂时间*/
    private String finalDestination = " ";        /* 终到站名称*/
    private String deliverTypeName = " ";        /* 交货方式名称*/
    private String signingAddress = " ";        /* 签收地地址*/
    private String finalStationLongitude = " ";        /* 终到站经度*/
    private String finalStationLatitude = " ";        /* 终到站纬度*/
    private String destSpotAddr = " ";        /* 终到站地址*/
    private String perNo = " ";        /* 装货行车工工号*/
    private String perName = " ";        /* 装货行车工姓名*/
    private String loadId = " ";        /* 结束装卸货流水号*/
    private String handPointId = " ";        /* 目标装卸点*/
    private String handPointName = " ";        /* 目标装卸点名称*/

    /**
     * the constructor
     */
    public LIRL0308() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putinId");
        eiColumn.setDescName("入库单号（无计划入库为空）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putoutId");
        eiColumn.setDescName("出库单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("提单号（多个）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliverType");
        eiColumn.setDescName("交货方式（代运、自提）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliverName");
        eiColumn.setDescName("交货方式名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("matInnerId");
        eiColumn.setDescName("材料管理好（无计划入库为空）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryOrderNum");
        eiColumn.setDescName("钢厂资源号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putInOutFlag");
        eiColumn.setDescName("出入库标记（入库：1，出库：2）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeName");
        eiColumn.setDescName("品种附属码名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("putoutDate");
        eiColumn.setDescName("出库日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("signatureFlag");
        eiColumn.setDescName("签收标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finalDestination");
        eiColumn.setDescName("终到站名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliverTypeName");
        eiColumn.setDescName("交货方式名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("signingAddress");
        eiColumn.setDescName("签收地地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finalStationLongitude");
        eiColumn.setDescName("终到站经度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finalStationLatitude");
        eiColumn.setDescName("终到站纬度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("destSpotAddr");
        eiColumn.setDescName("终到站地址");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("perNo");
        eiColumn.setDescName("装货行车工工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("perName");
        eiColumn.setDescName("装货行车工姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadId");
        eiColumn.setDescName("开始装卸货流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointId");
        eiColumn.setDescName("目标装卸点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointName");
        eiColumn.setDescName("目标装卸点名称");
        eiMetadata.addMeta(eiColumn);


    }


    public String getPerNo() {
        return perNo;
    }

    public void setPerNo(String perNo) {
        this.perNo = perNo;
    }

    public String getPerName() {
        return perName;
    }

    public void setPerName(String perName) {
        this.perName = perName;
    }

    public String getDestSpotAddr() {
        return destSpotAddr;
    }

    public void setDestSpotAddr(String destSpotAddr) {
        this.destSpotAddr = destSpotAddr;
    }

    public String getFinalStationLongitude() {
        return finalStationLongitude;
    }

    public void setFinalStationLongitude(String finalStationLongitude) {
        this.finalStationLongitude = finalStationLongitude;
    }

    public String getFinalStationLatitude() {
        return finalStationLatitude;
    }

    public void setFinalStationLatitude(String finalStationLatitude) {
        this.finalStationLatitude = finalStationLatitude;
    }

    /**
     * get the segNo - 账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the carTraceNo - 车辆跟踪号
     * @return the carTraceNo
     */
    public String getCarTraceNo() {
        return this.carTraceNo;
    }

    /**
     * set the carTraceNo - 车辆跟踪号
     */
    public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
    }

    /**
     * get the putinId - 入库单号（无计划入库为空）
     * @return the putinId
     */
    public String getPutinId() {
        return this.putinId;
    }

    /**
     * set the putinId - 入库单号（无计划入库为空）
     */
    public void setPutinId(String putinId) {
        this.putinId = putinId;
    }

    /**
     * get the putoutId - 出库单号
     * @return the putoutId
     */
    public String getPutoutId() {
        return this.putoutId;
    }

    /**
     * set the putoutId - 出库单号
     */
    public void setPutoutId(String putoutId) {
        this.putoutId = putoutId;
    }

    /**
     * get the voucherNum - 提单号（多个）
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 提单号（多个）
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the driverName - 司机姓名
     * @return the driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * set the driverName - 司机姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * get the driverTel - 司机手机号
     * @return the driverTel
     */
    public String getDriverTel() {
        return this.driverTel;
    }

    /**
     * set the driverTel - 司机手机号
     */
    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
    }

    /**
     * get the driverIdentity - 司机身份证号
     * @return the driverIdentity
     */
    public String getDriverIdentity() {
        return this.driverIdentity;
    }

    /**
     * set the driverIdentity - 司机身份证号
     */
    public void setDriverIdentity(String driverIdentity) {
        this.driverIdentity = driverIdentity;
    }

    /**
     * get the vehicleNo - 车牌号
     * @return the vehicleNo
     */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
     * set the vehicleNo - 车牌号
     */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    /**
     * get the deliverType - 交货方式（代运、自提）
     * @return the deliverType
     */
    public String getDeliverType() {
        return this.deliverType;
    }

    /**
     * set the deliverType - 交货方式（代运、自提）
     */
    public void setDeliverType(String deliverType) {
        this.deliverType = deliverType;
    }

    /**
     * get the deliverName - 交货方式名称
     * @return the deliverName
     */
    public String getDeliverName() {
        return this.deliverName;
    }

    /**
     * set the deliverName - 交货方式名称
     */
    public void setDeliverName(String deliverName) {
        this.deliverName = deliverName;
    }

    /**
     * get the packId - 捆包号
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the matInnerId - 材料管理好（无计划入库为空）
     * @return the matInnerId
     */
    public String getMatInnerId() {
        return this.matInnerId;
    }

    /**
     * set the matInnerId - 材料管理好（无计划入库为空）
     */
    public void setMatInnerId(String matInnerId) {
        this.matInnerId = matInnerId;
    }

    /**
     * get the factoryOrderNum - 钢厂资源号
     * @return the factoryOrderNum
     */
    public String getFactoryOrderNum() {
        return this.factoryOrderNum;
    }

    /**
     * set the factoryOrderNum - 钢厂资源号
     */
    public void setFactoryOrderNum(String factoryOrderNum) {
        this.factoryOrderNum = factoryOrderNum;
    }

    /**
     * get the putInOutFlag - 出入库标记（入库：1，出库：2）
     * @return the putInOutFlag
     */
    public String getPutInOutFlag() {
        return this.putInOutFlag;
    }

    /**
     * set the putInOutFlag - 出入库标记（入库：1，出库：2）
     */
    public void setPutInOutFlag(String putInOutFlag) {
        this.putInOutFlag = putInOutFlag;
    }

    /**
     * get the prodTypeId - 品种附属码
     * @return the prodTypeId
     */
    public String getProdTypeId() {
        return this.prodTypeId;
    }

    /**
     * set the prodTypeId - 品种附属码
     */
    public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
    }

    /**
     * get the prodTypeName - 品种附属码名称
     * @return the prodTypeName
     */
    public String getProdTypeName() {
        return this.prodTypeName;
    }

    /**
     * set the prodTypeName - 品种附属码名称
     */
    public void setProdTypeName(String prodTypeName) {
        this.prodTypeName = prodTypeName;
    }

    /**
     * get the shopsign - 牌号
     * @return the shopsign
     */
    public String getShopsign() {
        return this.shopsign;
    }

    /**
     * set the shopsign - 牌号
     */
    public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
    }

    /**
     * get the specDesc - 规格
     * @return the specDesc
     */
    public String getSpecDesc() {
        return this.specDesc;
    }

    /**
     * set the specDesc - 规格
     */
    public void setSpecDesc(String specDesc) {
        this.specDesc = specDesc;
    }

    /**
     * get the weight - 重量
     * @return the weight
     */
    public BigDecimal getWeight() {
        return this.weight;
    }

    /**
     * set the weight - 重量
     */
    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    /**
     * get the quantity - 数量
     * @return the quantity
     */
    public BigDecimal getQuantity() {
        return this.quantity;
    }

    /**
     * set the quantity - 数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * get the warehouseCode - 仓库代码
     * @return the warehouseCode
     */
    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    /**
     * set the warehouseCode - 仓库代码
     */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    /**
     * get the warehouseName - 仓库名称
     * @return the warehouseName
     */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
     * set the warehouseName - 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * get the customerId - 客户代码
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the customerName - 客户名称
     * @return the customerName
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * set the customerName - 客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * get the locationId - 库位代码
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locationName - 库位名称
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPutoutDate() {
        return putoutDate;
    }

    public void setPutoutDate(String putoutDate) {
        this.putoutDate = putoutDate;
    }

    /**
     * get the signatureFlag - 签收标记
     * @return the signatureFlag
     */
    public String getSignatureFlag() {
        return this.signatureFlag;
    }

    /**
     * set the signatureFlag - 签收标记
     */
    public void setSignatureFlag(String signatureFlag) {
        this.signatureFlag = signatureFlag;
    }


    public String getLeaveFactoryDate() {
        return leaveFactoryDate;
    }

    public void setLeaveFactoryDate(String leaveFactoryDate) {
        this.leaveFactoryDate = leaveFactoryDate;
    }

    public String getFinalDestination() {
        return finalDestination;
    }

    public void setFinalDestination(String finalDestination) {
        this.finalDestination = finalDestination;
    }

    public String getDeliverTypeName() {
        return deliverTypeName;
    }

    public void setDeliverTypeName(String deliverTypeName) {
        this.deliverTypeName = deliverTypeName;
    }

    public String getSigningAddress() {
        return signingAddress;
    }

    public void setSigningAddress(String signingAddress) {
        this.signingAddress = signingAddress;
    }

    public String getLoadId() {
        return loadId;
    }

    public void setLoadId(String loadId) {
        this.loadId = loadId;
    }

    public String getHandPointId() {
        return handPointId;
    }

    public void setHandPointId(String handPointId) {
        this.handPointId = handPointId;
    }


    public String getHandPointName() {
        return handPointName;
    }

    public void setHandPointName(String handPointName) {
        this.handPointName = handPointName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
        setPutinId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putinId")), putinId));
        setPutoutId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putoutId")), putoutId));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
        setDriverTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverTel")), driverTel));
        setDriverIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverIdentity")), driverIdentity));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setDeliverType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliverType")), deliverType));
        setDeliverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliverName")), deliverName));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
        setFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryOrderNum")), factoryOrderNum));
        setPutInOutFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putInOutFlag")), putInOutFlag));
        setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
        setProdTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeName")), prodTypeName));
        setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
        setSpecDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specDesc")), specDesc));
        setWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("weight")), weight));
        setQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("quantity")), quantity));
        setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
        setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setPutoutDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putoutDate")), putoutDate));
        setSignatureFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("signatureFlag")), signatureFlag));
        setLeaveFactoryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leaveFactoryDate")), leaveFactoryDate));
        setFinalDestination(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finalDestination")), finalDestination));
        setDeliverTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliverTypeName")), deliverTypeName));
        setSigningAddress(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("signingAddress")), signingAddress));
        setFinalStationLongitude(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finalStationLongitude")), finalStationLongitude));
        setFinalStationLatitude(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finalStationLatitude")), finalStationLatitude));
        setDestSpotAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("destSpotAddr")), destSpotAddr));
        setPerNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("perNo")), perNo));
        setPerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("perName")), perName));
        setLoadId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadId")), loadId));
        setHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointId")), handPointId));
        setHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointName")), handPointName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("carTraceNo", StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
        map.put("putinId", StringUtils.toString(putinId, eiMetadata.getMeta("putinId")));
        map.put("putoutId", StringUtils.toString(putoutId, eiMetadata.getMeta("putoutId")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("driverName", StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
        map.put("driverTel", StringUtils.toString(driverTel, eiMetadata.getMeta("driverTel")));
        map.put("driverIdentity", StringUtils.toString(driverIdentity, eiMetadata.getMeta("driverIdentity")));
        map.put("vehicleNo", StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("deliverType", StringUtils.toString(deliverType, eiMetadata.getMeta("deliverType")));
        map.put("deliverName", StringUtils.toString(deliverName, eiMetadata.getMeta("deliverName")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("matInnerId", StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
        map.put("factoryOrderNum", StringUtils.toString(factoryOrderNum, eiMetadata.getMeta("factoryOrderNum")));
        map.put("putInOutFlag", StringUtils.toString(putInOutFlag, eiMetadata.getMeta("putInOutFlag")));
        map.put("prodTypeId", StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
        map.put("prodTypeName", StringUtils.toString(prodTypeName, eiMetadata.getMeta("prodTypeName")));
        map.put("shopsign", StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
        map.put("specDesc", StringUtils.toString(specDesc, eiMetadata.getMeta("specDesc")));
        map.put("weight", StringUtils.toString(weight, eiMetadata.getMeta("weight")));
        map.put("quantity", StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
        map.put("warehouseCode", StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
        map.put("warehouseName", StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("putoutDate", StringUtils.toString(putoutDate, eiMetadata.getMeta("putoutDate")));
        map.put("signatureFlag", StringUtils.toString(signatureFlag, eiMetadata.getMeta("signatureFlag")));
        map.put("leaveFactoryDate", StringUtils.toString(leaveFactoryDate, eiMetadata.getMeta("leaveFactoryDate")));
        map.put("finalDestination", StringUtils.toString(finalDestination, eiMetadata.getMeta("finalDestination")));
        map.put("deliverTypeName", StringUtils.toString(deliverTypeName, eiMetadata.getMeta("deliverTypeName")));
        map.put("signingAddress", StringUtils.toString(signingAddress, eiMetadata.getMeta("signingAddress")));
        map.put("finalStationLongitude", StringUtils.toString(finalStationLongitude, eiMetadata.getMeta("finalStationLongitude")));
        map.put("finalStationLatitude", StringUtils.toString(finalStationLatitude, eiMetadata.getMeta("finalStationLatitude")));
        map.put("destSpotAddr", StringUtils.toString(destSpotAddr, eiMetadata.getMeta("destSpotAddr")));
        map.put("perNo", StringUtils.toString(perNo, eiMetadata.getMeta("perNo")));
        map.put("perName", StringUtils.toString(perName, eiMetadata.getMeta("perName")));
        map.put("loadId", StringUtils.toString(loadId, eiMetadata.getMeta("loadId")));
        map.put("handPointId", StringUtils.toString(handPointId, eiMetadata.getMeta("handPointId")));
        map.put("handPointName", StringUtils.toString(handPointName, eiMetadata.getMeta("handPointName")));

        return map;

    }
}