package com.baosight.imom.vg.dm.domain;

/**
 * 标识校验状态接口
 * <AUTHOR> 郁在杰
 * @Description : 标识校验状态接口
 * @Date : 2024/12/16
 * @Version : 1.0
 */
public interface CheckStatus {
    /**
     * 获取uuid
     * @return uuid
     */ 
    String getUuid();
    /**
     * 获取状态
     * @return 状态
     */
    String getCheckStatus();
    /**
     * 获取查询sqlId
     * @return sqlId
     */
    String getQuerySqlId();
}
