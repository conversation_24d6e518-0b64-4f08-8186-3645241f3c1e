<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0901">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="scrapType">
            SCRAP_TYPE = #scrapType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="applyStatus">
            APPLY_STATUS = #applyStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="applyStatus">
            APPLY_STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="apprStatus">
            APPR_STATUS = #apprStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stuffCode">
            STUFF_CODE = #stuffCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultId">
            FAULT_ID = #faultId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="scrapApplyId">
            SCRAP_APPLY_ID like concat('%',#scrapApplyId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equalId">
            SCRAP_APPLY_ID = #equalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stuffName">
            STUFF_NAME like concat('%',#stuffName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processInstanceId">
            PROCESS_INSTANCE_ID = #processInstanceId#
        </isNotEmpty>
        <!--根据登录人查询待办-->
        <isNotEmpty property="loginId" prepend="and">
            <isEqual property="apprStatus" compareValue="60">
                <!--待审批-->
                EXISTS(SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0901.PROCESS_INSTANCE_ID
                AND T.ASSIGNEE_ID = #loginId#
                AND T.PROCESS_KEY = 'scrapApplyAudit'
                AND t.STATE = 'open')
            </isEqual>
            <isEqual property="apprStatus" compareValue="70">
                <!--审核通过-->
                EXISTS(SELECT 1
                FROM ${platSchema}.HEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0901.PROCESS_INSTANCE_ID
                AND T.COMPLETER_ID = #loginId#
                and T.PROCESS_KEY = 'scrapApplyAudit'
                and T.APPROVAL_RESULT = 'grant'
                and t.STATE = 'completed'
                )
            </isEqual>
            <isEqual property="apprStatus" compareValue="7X">
                <!--审核驳回-->
                EXISTS(
                SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0901.PROCESS_INSTANCE_ID
                AND T.COMPLETER_ID = #loginId#
                AND T.PROCESS_KEY = 'scrapApplyAudit'
                AND T.APPROVAL_RESULT ='reject'
                AND t.STATE = 'completed'
                )
            </isEqual>
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0901">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        SCRAP_APPLY_ID as "scrapApplyId",  <!-- 报废申请单号 -->
        DEPT_ID as "deptId",  <!-- 部门 -->
        DEPT_NAME as "deptName",  <!-- 部门名称 -->
        STUFF_CODE as "stuffCode",  <!-- 资材代码 -->
        STUFF_NAME as "stuffName",  <!-- 资材名称 -->
        SPEC_DESC as "specDesc",  <!-- 规格 -->
        UNIT_PRICE as "unitPrice",  <!-- 单价 -->
        APPLY_QTY as "applyQty",  <!-- 申请量 -->
        AMOUNT_MONEY as "amountMoney",  <!-- 总金额 -->
        SCRAP_REASON as "scrapReason",  <!-- 报废原因 -->
        SCRAP_TYPE as "scrapType",  <!-- 报废方式 -->
        APPLY_STATUS as "applyStatus",  <!-- 申请状态 -->
        APPR_STATUS as "apprStatus",  <!-- 审批状态 -->
        PROCESS_INSTANCE_ID as "processInstanceId",  <!-- 工作流实例ID -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        FAULT_ID as "faultId",  <!-- 故障单号 -->
        VOUCHER_NUM as "voucherNum"  <!-- 依据凭单 -->
        FROM ${mevgSchema}.TVGDM0901 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0901 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0901 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        SCRAP_APPLY_ID,  <!-- 报废申请单号 -->
        DEPT_ID,  <!-- 部门 -->
        DEPT_NAME,  <!-- 部门名称 -->
        STUFF_CODE,  <!-- 资材代码 -->
        STUFF_NAME,  <!-- 资材名称 -->
        SPEC_DESC,  <!-- 规格 -->
        UNIT_PRICE,  <!-- 单价 -->
        APPLY_QTY,  <!-- 申请量 -->
        AMOUNT_MONEY,  <!-- 总金额 -->
        SCRAP_REASON,  <!-- 报废原因 -->
        SCRAP_TYPE,  <!-- 报废方式 -->
        APPLY_STATUS,  <!-- 申请状态 -->
        APPR_STATUS,  <!-- 审批状态 -->
        PROCESS_INSTANCE_ID,  <!-- 工作流实例ID -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        FAULT_ID,  <!-- 故障单号 -->
        VOUCHER_NUM  <!-- 依据凭单 -->
        )
        VALUES (#eArchivesNo#, #equipmentName#, #scrapApplyId#, #deptId#, #deptName#, #stuffCode#, #stuffName#,
        #specDesc#, #unitPrice#, #applyQty#, #amountMoney#, #scrapReason#, #scrapType#, #applyStatus#, #apprStatus#,
        #processInstanceId#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #faultId#, #voucherNum#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0901 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0901
        SET
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        DEPT_ID = #deptId#,   <!-- 部门 -->
        DEPT_NAME = #deptName#,   <!-- 部门名称 -->
        STUFF_CODE = #stuffCode#,   <!-- 资材代码 -->
        STUFF_NAME = #stuffName#,   <!-- 资材名称 -->
        SPEC_DESC = #specDesc#,   <!-- 规格 -->
        UNIT_PRICE = #unitPrice#,   <!-- 单价 -->
        APPLY_QTY = #applyQty#,   <!-- 申请量 -->
        AMOUNT_MONEY = #amountMoney#,   <!-- 总金额 -->
        SCRAP_REASON = #scrapReason#,   <!-- 报废原因 -->
        SCRAP_TYPE = #scrapType#,   <!-- 报废方式 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        UNIT_CODE = #unitCode#   <!-- 业务单元代码 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateStatus">
        UPDATE ${mevgSchema}.TVGDM0901
        SET
        APPLY_STATUS = #applyStatus#,   <!-- 申请状态 -->
        APPR_STATUS = #apprStatus#,   <!-- 审批状态 -->
        PROCESS_INSTANCE_ID = #processInstanceId#,   <!-- 工作流实例ID -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>