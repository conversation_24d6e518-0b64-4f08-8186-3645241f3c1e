/**
* Generate time : 2024-10-30 9:03:34
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* XsOperationUser
* 
*/
public class XS_OPERATION_USER extends DaoEPBase {
    public static final String QUERY = "XS_OPERATION_USER.query";
    public static final String INSERT = "XS_OPERATION_USER.insert";
    public static final String UPDATE = "XS_OPERATION_USER.update";
    public static final String DELETE = "XS_OPERATION_USER.delete";
                private String uuid = " ";		/* ID*/
                private String segNo = " ";		/* 业务单元代码*/
                private String segCname = " ";		/* 业务单元名称*/
                private String factoryArea = " ";		/* 厂区编码*/
                private String factoryAreaName = " ";		/* 厂区名称*/
                private String warehouseCode = " ";		/* 仓库代码*/
                private String warehouseName = " ";		/* 仓库名称*/
                private String handPointId = " ";		/* 装卸点代码*/
                private String handPointName = " ";		/* 装卸点名称*/
                private String employeeJobNumber = " ";		/* 员工号*/
                private String employeeName = " ";		/* 员工姓名*/
                private String operationType = " ";		/* 操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录\r 20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库 */
                private String sourceSystem = " ";		/* 数据来源(10系统 20车载APP 30员工PDA)*/
                private String remark = " ";		/* 备注*/
                private String recCreator = " ";		/* 创建人*/
                private String recCreatorName = " ";		/* 创建人姓名*/
                private String recCreateTime = " ";		/* 创建时间*/
                private String recRevisor = " ";		/* 修改人*/
                private String recRevisorName = " ";		/* 修改人姓名*/
                private String recReviseTime = " ";		/* 修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String tenantUser = " ";		/* 租户ID*/
                private String unitCode = " ";		/* 业务单元名称*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segCname");
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointId");
        eiColumn.setDescName("装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointName");
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("employeeJobNumber");
        eiColumn.setDescName("员工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("employeeName");
        eiColumn.setDescName("员工姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("operationType");
        eiColumn.setDescName("操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录\r 20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库 ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sourceSystem");
        eiColumn.setDescName("数据来源(10系统 20车载APP 30员工PDA)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public XS_OPERATION_USER() {
initMetaData();
}

        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the segNo - 业务单元代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the segCname - 业务单元名称
        * @return the segCname
        */
        public String getSegCname() {
        return this.segCname;
        }

        /**
        * set the segCname - 业务单元名称
        */
        public void setSegCname(String segCname) {
        this.segCname = segCname;
        }
        /**
        * get the factoryArea - 厂区编码
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区编码
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the factoryAreaName - 厂区名称
        * @return the factoryAreaName
        */
        public String getFactoryAreaName() {
        return this.factoryAreaName;
        }

        /**
        * set the factoryAreaName - 厂区名称
        */
        public void setFactoryAreaName(String factoryAreaName) {
        this.factoryAreaName = factoryAreaName;
        }
        /**
        * get the warehouseCode - 仓库代码
        * @return the warehouseCode
        */
        public String getWarehouseCode() {
        return this.warehouseCode;
        }

        /**
        * set the warehouseCode - 仓库代码
        */
        public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
        }
        /**
        * get the warehouseName - 仓库名称
        * @return the warehouseName
        */
        public String getWarehouseName() {
        return this.warehouseName;
        }

        /**
        * set the warehouseName - 仓库名称
        */
        public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        }
        /**
        * get the handPointId - 装卸点代码
        * @return the handPointId
        */
        public String getHandPointId() {
        return this.handPointId;
        }

        /**
        * set the handPointId - 装卸点代码
        */
        public void setHandPointId(String handPointId) {
        this.handPointId = handPointId;
        }
        /**
        * get the handPointName - 装卸点名称
        * @return the handPointName
        */
        public String getHandPointName() {
        return this.handPointName;
        }

        /**
        * set the handPointName - 装卸点名称
        */
        public void setHandPointName(String handPointName) {
        this.handPointName = handPointName;
        }
        /**
        * get the employeeJobNumber - 员工号
        * @return the employeeJobNumber
        */
        public String getEmployeeJobNumber() {
        return this.employeeJobNumber;
        }

        /**
        * set the employeeJobNumber - 员工号
        */
        public void setEmployeeJobNumber(String employeeJobNumber) {
        this.employeeJobNumber = employeeJobNumber;
        }
        /**
        * get the employeeName - 员工姓名
        * @return the employeeName
        */
        public String getEmployeeName() {
        return this.employeeName;
        }

        /**
        * set the employeeName - 员工姓名
        */
        public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
        }
        /**
        * get the operationType - 操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录\r 20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库 
        * @return the operationType
        */
        public String getOperationType() {
        return this.operationType;
        }

        /**
        * set the operationType - 操作类型：00：登录  01：更换信息(组织、厂区、仓库)  07：退出登录\r 20：人工排队  21：取消排队  30：入库 31：出库 32：装车  33：卸车  34：盘库 35：倒库 
        */
        public void setOperationType(String operationType) {
        this.operationType = operationType;
        }
        /**
        * get the sourceSystem - 数据来源(10系统 20车载APP 30员工PDA)
        * @return the sourceSystem
        */
        public String getSourceSystem() {
        return this.sourceSystem;
        }

        /**
        * set the sourceSystem - 数据来源(10系统 20车载APP 30员工PDA)
        */
        public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the recCreator - 创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户ID
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户ID
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the unitCode - 业务单元名称
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元名称
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setSegCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segCname")), segCname));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
                setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
                setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
                setHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointId")), handPointId));
                setHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointName")), handPointName));
                setEmployeeJobNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("employeeJobNumber")), employeeJobNumber));
                setEmployeeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("employeeName")), employeeName));
                setOperationType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("operationType")), operationType));
                setSourceSystem(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sourceSystem")), sourceSystem));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("segCname",StringUtils.toString(segCname, eiMetadata.getMeta("segCname")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("factoryAreaName",StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
                map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
                map.put("warehouseName",StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
                map.put("handPointId",StringUtils.toString(handPointId, eiMetadata.getMeta("handPointId")));
                map.put("handPointName",StringUtils.toString(handPointName, eiMetadata.getMeta("handPointName")));
                map.put("employeeJobNumber",StringUtils.toString(employeeJobNumber, eiMetadata.getMeta("employeeJobNumber")));
                map.put("employeeName",StringUtils.toString(employeeName, eiMetadata.getMeta("employeeName")));
                map.put("operationType",StringUtils.toString(operationType, eiMetadata.getMeta("operationType")));
                map.put("sourceSystem",StringUtils.toString(sourceSystem, eiMetadata.getMeta("sourceSystem")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

return map;

}
}