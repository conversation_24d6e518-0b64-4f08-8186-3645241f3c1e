$(document).ready(function () {
    initData(); //加载数据

    // 初始化滚动
    initAutoScroll();
    initAutoScrollJiaoHao();
    initAutoScrollWeiyuyue();
    initAutoScrollOther();

});

function initData() {
    const searchObj = new URLSearchParams(window.location.search);
    let queryData = {
        segNo: searchObj.get('segNo') ?? 'KF000000',
        serviceId: 'S_LI_RL_0045',
    };
    //ajax请求数据
    //查询全部接口
    $.ajax({
        type: "post",
        contentType: "application/json",
        url: ytjServerUrl,
        async: false,
        data: JSON.stringify(queryData),
        complete: function () { },
        success: function (data) {
            if (!data || !data.__sys__ || data.__sys__.status == -1) {
                Swal.fire({
                    title: data.__sys__.msg,
                    icon: 'error',
                    confirmButtonText: '确定'
                });
                return;
            }

            const { waitingList, workingList } = data;
            if (waitingList.length > 0) {
                let waitSum = 0;
                waitingList.forEach(w => {
                    waitSum += Number(w.count);
                });
                $('#waitCon')[0].innerText = waitSum;
            }

            if (workingList.length > 0) {
                let workSum = 0;
                workingList.forEach(w => {
                    workSum += Number(w.count);
                });
                $('#workCon')[0].innerText = workSum;
            }

            // 仓库门数据
            allData(data);
            // 已登记待进厂
            queryQueue(data.listWait);
            // 叫号
            lineUpAndCallForNumbers(data.listCallNum);
            // 未预约待审核
            getNotBooked(data.listQueue);
        }
    });

    //页面每隔20秒自动刷新
    slidRow();
}

function allData(data) {
    let { handPointIdList, list, waitCount, queueCount, workingList, waitingList } = data;
    handPointIdList = handPointIdList.filter(h => /\d/.test(h.handPointName));
    // 将handPointIdList进行排序
    handPointIdList.sort((a, b) => {
        // 提取 handPointName 中的数字部分并转为整数
        const numA = parseInt(a.handPointName.match(/\d+/)[0], 10);
        const numB = parseInt(b.handPointName.match(/\d+/)[0], 10);
        return numA - numB;
    });
    const groupedArray = [];
    for (let i = 0; i < handPointIdList.length; i += 3) {
        groupedArray.push(handPointIdList.slice(i, i + 3));
    }

    let handPointIdListIndex = 1;
    let huiShangId = '';
    let otherIdList = data.handPointIdList.filter(h => !/\d/.test(h.handPointName) && !h.handPointName.includes('徽商')).map(h => h.handPointId);
    // 遍历分组，生成 HTML 并插入到 vehiclesDiv
    for (let i = 0; i < groupedArray.length; i++) {
        let strHtml = `<tr class="external-tr">`;
        const curTr = groupedArray[i];
        curTr.forEach(c => {
            const workCountObj = workingList.find(w => w.targetHandPointId == c.handPointId);
            let workCount = workCountObj ? workCountObj.count : '0';

            const waitCountObj = waitingList.find(w => w.targetHandPointId == c.handPointId);
            let waitCount = waitCountObj ? waitCountObj.count : '0';

            strHtml += `
<td class="vehicles-admitted-sub-div">
    <div style="height: 100px;">
        <div class="car-num-head-div">
            <div style="width: 30%">&nbsp;</div>

            <div class="center-text">
                ${c.handPointName}
            </div>
        
            <div class="right-text" style="font-size: 16px;">
                （完成<span style="color: #75BD42;">${workCount}</span>, 
                剩余<span style="color: #ff0000;">${waitCount}</span>）
            </div>
</div>
<table class="table-content number${handPointIdListIndex} data-table">
`;

            // 渲染数据
            let filterData = list.filter(l => l.handPointId == c.handPointId);
            filterData = getSortList(filterData);

            let filterDataGroup = [];
            for (let i = 0; i < filterData.length; i += 2) {
                filterDataGroup.push(filterData.slice(i, i + 2));
            }

            filterDataGroup.forEach(fArr => {
                strHtml += '<tr>';
                fArr.forEach(f => {
                    const className = f.status == '20' ? 'working' : 'pending';
                    const waitingTime = f.hashMap.waitingTime || 0;
                    let customerNameVal = f.hashMap.customerName.trim();
                    const customerName = customerNameVal ? `-${customerNameVal}` : '';

                    let startValue = f.hashMap.startOfTransport;
                    const startOfTransport = startValue ? `-${startValue}` : '';
                    strHtml += `
            <td>
                <div class="div-${className}-tag" ${[10, 20].includes(f.status) && waitingTime >= 120 ? 'style="background: #FF0000;"' : ''}>
                    ${f.hashMap.vehicleNo}-${waitingTime}分</br>
                    ${f.hashMap.businessType || ''}${customerName}${startOfTransport}
                </div>
            </td>`;
                });

                if (fArr.length == 1) {
                    strHtml += '<td></td>';
                }
                strHtml += '</tr>';
            });

            // 主项td闭合
            strHtml += `</table>
                    </div>
                </td>`;
            handPointIdListIndex++;
        });
        // 增加叫号功能
        if (i == 0) {
            // 徽商库
            const huiShangList = data.handPointIdList.filter(h => !/\d/.test(h.handPointName) && h.handPointName.includes('徽商'));
            if (huiShangList && huiShangList.length > 0) {
                const huiShang = huiShangList[0];
                huiShangId = huiShang.handPointId;

                const workCountObj = workingList.find(w => w.targetHandPointId == huiShangId);
                let workCount = workCountObj ? workCountObj.count : '0';

                const waitCountObj = waitingList.find(w => w.targetHandPointId == huiShangId);
                let waitCount = waitCountObj ? waitCountObj.count : '0';


                strHtml += `
                <td class="vehicles-admitted-sub-div" rowspan="2" style="width: 15%;">
                    <div style="height: 240px;">
                        <div class="car-num-head-div">
                                        <div style="width: 10%">&nbsp;</div>

            <div class="center-text">
                ${huiShang.handPointName}
            </div>
        
            <div class="right-text" style="font-size: 16px;">
                （完成<span style="color: #75BD42;">${workCount}</span>, 
                剩余<span style="color: #ff0000;">${waitCount}</span>）
            </div>
</div>
                        <table class="table-content huishang"> </table>
                    </div>
                </td>
            `;
            }

            strHtml += `
    <td class="vehicles-admitted-sub-div" rowspan="1" style="width: 20%;">
        <div style="height: 120px;">
            <div class="not-car-num-head-div">叫号</div>
            <div class="jiaohao-scroll-container" style="height: 18vh; overflow: hidden;">
                <table class="table-content jiaohao"> </table>
            </div>
        </div>
    </td>
`;
        } else if (i == 1) {
            strHtml += `
    <td class="vehicles-admitted-sub-div" rowspan="3" style="width: 15%;">
        <div style="height: 120px;">
            <div class="not-car-num-head-div">已登记待进厂(${waitCount})</div>
            <div class="dengdai-scroll-container" style="height: 60vh; overflow: hidden;">
                <table class="table-content dengdai data-table"></table>
            </div>
        </div>
    </td>
`;

        } else if (i == 2) {
            strHtml += `
            <td class="vehicles-admitted-sub-div" rowspan="1" style="width: 15%;">
                <div style="height: 120px;">
                    <div class="car-num-head-div" id="other-qiTa">其他</div>
                    <div class="other-scroll-container" style="height: 85px; overflow: hidden;">
                    <table class="table-content other"> </table>
        </div>
                </div>
            </td>
        `;
        } else if (i == 3) {
            strHtml += `
                <td class="vehicles-admitted-sub-div" rowspan="1" style="width: 15%;">
                    <div style="height: 120px;">
                        <div class="not-car-num-head-div">未预约待审核(${queueCount})</div>
                        
                         <div class="weiyuyue-scroll-container" style="height: 85px; overflow: hidden;">
                <table class="table-content weiyuyue data-table"> </table>
            </div>
                    </div>
                </td>
            `;

        }

        strHtml += '</tr>';
        $('#vehiclesDiv').append(strHtml); // 将生成的 HTML 插入到表格
    }

    if (otherIdList.length > 0) {
        let otherList = list.filter(l => otherIdList.includes(l.handPointId));
        let workSum = 0;
        let waitSum = 0;
        otherIdList.forEach(otherId => {
            const work = workingList.find(l => l.targetHandPointId == otherId);
            const wait = waitingList.find(l => l.targetHandPointId == otherId);
            if (work) {
                workSum += work.count;
            }
            if (wait) {
                waitSum += wait.count;
            }
        });
        otherList = getSortList(otherList).map(l => l.hashMap);
        $('#other-qiTa')[0].innerHTML = `
                        <div style="width: 10%">&nbsp;</div>

            <div class="center-text">
                其他
            </div>
        
            <div class="right-text" style="font-size: 16px;">
                （完成<span style="color: #75BD42;">${workSum}</span>, 
                剩余<span style="color: #ff0000;">${waitSum}</span>）
            </div>`;
        getOtherData(otherList);
    }

    // 生成徽商库数据
    if (!huiShangId) { return; }
    let huiSList = list.filter(l => l.handPointId == huiShangId);
    huiSList = getSortList(huiSList).map(l => l.hashMap);
    getHuiShang(huiSList);
}

/** 已登记待进厂 */
function queryQueue(data) {
    if (!data) {
        return;
    }

    // addData(data, 10);

    let groupedArray = [];
    for (let i = 0; i < data.length; i += 2) {
        groupedArray.push(data.slice(i, i + 2));
    }

    let str = '';
    groupedArray.forEach((g, rowIndex) => {
        str += '<tr>';
        g.forEach((gc, colIndex) => {
            let startValue = gc.startOfTransport;
            const startOfTransport = startValue ? `-${startValue}` : '';

            str += `
            <td>
            <div class="div-pending-tag">
             <span class="index">${rowIndex * 2 + colIndex + 1}</span>
             <div style=" margin: 0;
  padding: 0;
  line-height: 1.2;
  white-space: nowrap;
">
             ${gc.vehicleNo}-${gc.waitingTime ?? 0}分
                </br>${gc.targetHandPointIdName}-${gc.businessType ?? ''}</br>
                ${startOfTransport}
            </div>
            </div>

            </td>`;
        });

        if (g.length == 1) {
            str += '<td></td>';
        }

        str += '</tr>';
    });
    $('.dengdai').append(str);

    // 数据加载完成后初始化滚动
    initAutoScroll();
}

/** 叫号数据 **/
function lineUpAndCallForNumbers(data) {
    let str = '';
    for (let i = 0; i < data.length; i++) {
        let customerNameVal = data[i].customerName.trim();
        const customerName = customerNameVal ? `-${customerNameVal}` : '';

        let startValue = data[i].startOfTransport;
        const startOfTransport = startValue ? `-${startValue}` : '';


        str += `
            <tr class="pending">
                <td>${data[i].vehicleNo}-${data[i].targetHandPointName}-${data[i].waitingTime ?? 0}分
                </br>${data[i].businessType ?? ''}${customerName}${startOfTransport}</td>
            </tr>
        `;
    }
    $('.jiaohao').append(str);

    initAutoScrollJiaoHao();
}

// 渲染徽商数据
function getHuiShang(data) {
    let str = ''
    for (let i = 0; i < data.length; i++) {
        str += `
            <tr class="pending" style="${data[i].waitingTime >= 120 ? 'background: #FF0000;' : ''}">
                <td>${data[i].vehicleNo}-${data[i].waitingTime ?? 0}分</br>${data[i].businessType}-${data[i].customerName || ''}</td>
            </tr>
        `;
    }
    $('.huishang').append(str);
}

function addData(arr, count) {
    const firstItem = arr[0];  // 获取下标为0的元素
    arr.push(...new Array(count).fill(firstItem));  // 使用 fill() 创建多个相同的元素并添加到数组中
}

/** 其他数据 **/
function getOtherData(data) {
    let str = '';
    for (let i = 0; i < data.length; i++) {
        str += `
            <tr class="pending">
                <td>${data[i].vehicleNo}-${data[i].waitingTime ?? 0}分</br>${data[i].businessType}</td>
            </tr>
        `;
    }
    $('.other').append(str);
    initAutoScrollOther();
}

/** 未预约待审核数据 **/
function getNotBooked(data) {
    let str = ''
    for (let i = 0; i < data.length; i++) {
        str += `
            <tr class="pending">
                <td>${data[i].vehicleNo}-${data[i].waitingTime ?? 0}分</br>${data[i].businessType}</td>
            </tr>
        `;
    }
    $('.weiyuyue').append(str);

    initAutoScrollWeiyuyue();
}

function initAutoScroll() {
    const container = document.querySelector('.dengdai-scroll-container');
    const table = container.querySelector('.dengdai');
    let isHovered = false;
    let animationId;
    let scrollPosition = 0;
    const scrollSpeed = 1; // 每次滚动的像素数，越小越慢
    const scrollInterval = 500; // 滚动间隔时间（毫秒），越大越慢

    function scrollContent() {
        if (!isHovered && table.scrollHeight > container.clientHeight) {
            const maxScroll = table.scrollHeight - container.clientHeight;

            if (scrollPosition >= maxScroll) {
                // 滚动到底部后回到顶部
                scrollPosition = 0;
                container.scrollTop = 0;
            } else {
                // 逐步增加滚动位置
                scrollPosition += scrollSpeed;
                container.scrollTop = scrollPosition;
            }
        }
        animationId = setTimeout(scrollContent, scrollInterval); // 使用 setTimeout 控制频率
    }

    // 鼠标悬停暂停
    container.addEventListener('mouseenter', () => {
        isHovered = true;
        clearTimeout(animationId); // 停止滚动
    });

    // 鼠标离开恢复滚动
    container.addEventListener('mouseleave', () => {
        isHovered = false;
        scrollContent(); // 重新开始滚动
    });

    // 开始滚动
    scrollContent();
}

function initAutoScrollJiaoHao() {
    const container = document.querySelector('.jiaohao-scroll-container');
    const table = container.querySelector('.jiaohao');
    let isHovered = false;
    let animationId;
    let scrollPosition = 0;
    const scrollSpeed = 1; // 每次滚动的像素数，越小越慢
    const scrollInterval = 500; // 滚动间隔时间（毫秒），越大越慢

    function scrollContent() {
        if (!isHovered && table.scrollHeight > container.clientHeight) {
            const maxScroll = table.scrollHeight - container.clientHeight;

            if (scrollPosition >= maxScroll) {
                // 滚动到底部后回到顶部
                scrollPosition = 0;
                container.scrollTop = 0;
            } else {
                // 逐步增加滚动位置
                scrollPosition += scrollSpeed;
                container.scrollTop = scrollPosition;
            }
        }
        animationId = setTimeout(scrollContent, scrollInterval); // 使用 setTimeout 控制频率
    }

    // 鼠标悬停暂停
    container.addEventListener('mouseenter', () => {
        isHovered = true;
        clearTimeout(animationId); // 停止滚动
    });

    // 鼠标离开恢复滚动
    container.addEventListener('mouseleave', () => {
        isHovered = false;
        scrollContent(); // 重新开始滚动
    });

    // 开始滚动
    scrollContent();
}

function initAutoScrollWeiyuyue() {
    const container = document.querySelector('.weiyuyue-scroll-container');
    const table = container.querySelector('.weiyuyue');
    let isHovered = false;
    let animationId;
    let scrollPosition = 0;
    const scrollSpeed = 100; // 每次滚动的像素数，越小越慢
    const scrollInterval = 100; // 滚动间隔时间（毫秒），越大越慢

    function scrollContent() {
        if (!isHovered && table.scrollHeight > container.clientHeight) {
            const maxScroll = table.scrollHeight - container.clientHeight;

            if (scrollPosition >= maxScroll) {
                // 滚动到底部后回到顶部
                scrollPosition = 0;
                container.scrollTop = 0;
            } else {
                // 逐步增加滚动位置
                scrollPosition += scrollSpeed;
                container.scrollTop = scrollPosition;
            }
        }
        animationId = setTimeout(scrollContent, scrollInterval); // 使用 setTimeout 控制频率
    }

    // 鼠标悬停暂停
    container.addEventListener('mouseenter', () => {
        isHovered = true;
        clearTimeout(animationId); // 停止滚动
    });

    // 鼠标离开恢复滚动
    container.addEventListener('mouseleave', () => {
        isHovered = false;
        scrollContent(); // 重新开始滚动
    });

    // 开始滚动
    scrollContent();
}



function initAutoScrollOther() {
    const container = document.querySelector('.other-scroll-container');
    const table = container.querySelector('.other');
    let isHovered = false;
    let animationId;
    let scrollPosition = 0;
    const scrollSpeed = 1; // 每次滚动的像素数，越小越慢
    const scrollInterval = 500; // 滚动间隔时间（毫秒），越大越慢

    function scrollContent() {
        if (!isHovered && table.scrollHeight > container.clientHeight) {
            const maxScroll = table.scrollHeight - container.clientHeight;

            if (scrollPosition >= maxScroll) {
                // 滚动到底部后回到顶部
                scrollPosition = 0;
                container.scrollTop = 0;
            } else {
                // 逐步增加滚动位置
                scrollPosition += scrollSpeed;
                container.scrollTop = scrollPosition;
            }
        }
        animationId = setTimeout(scrollContent, scrollInterval); // 使用 setTimeout 控制频率
    }

    // 鼠标悬停暂停
    container.addEventListener('mouseenter', () => {
        isHovered = true;
        clearTimeout(animationId); // 停止滚动
    });

    // 鼠标离开恢复滚动
    container.addEventListener('mouseleave', () => {
        isHovered = false;
        scrollContent(); // 重新开始滚动
    });

    // 开始滚动
    scrollContent();
}

function getSortList(list) {
    return list.sort((a, b) => {
        // 首先按照status排序，"20"在前，"10"在后
        if (a.status === 20 && b.status !== 20) {
            return -1;
        } else if (a.status !== 20 && b.status === 20) {
            return 1;
        } else {
            // status相同的情况下，按照waitingTime降序排列
            // 处理可能不存在的waitingTime，默认设为-Infinity以确保排在最后
            const aWait = a.hashMap.waitingTime !== undefined ? a.hashMap.waitingTime : -Infinity;
            const bWait = b.hashMap.waitingTime !== undefined ? b.hashMap.waitingTime : -Infinity;
            return bWait - aWait;
        }
    });
}

function slidRow() {
    // setTimeout(function () { location.reload() }, 20000);
}