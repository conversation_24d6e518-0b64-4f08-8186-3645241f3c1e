<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFDateSpan startName="inqu_status-0-startTime" role="datetime"
                           endName="inqu_status-0-endTime" interval="5"
                           startCname="采集时间(起)" endCname="采集时间(止)"
                           ratio="3:3" format="yyyy-MM-dd HH:mm:ss" required="true">
            </EF:EFDateSpan>
        </div>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-tagId" cname="点位代码" colWidth="6"
                             readonly="true" clear="false" containerId="tagInfo" originalInput="true"
                             center="true" required="true" ratio="2:10">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-tagDesc" cname="点位名称" disabled="true" required="true" colWidth="6"
                        ratio="2:10"/>
            <EF:EFInput ename="inqu_status-0-tagIhdId" cname="点位ID" type="hidden"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" isFloat="true">
            <!-- <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/> -->
            <EF:EFColumn ename="tagIhdId" cname="点位ID" align="center"/>
            <EF:EFColumn ename="tagId" cname="点位代码" align="center"/>
            <EF:EFColumn ename="tagDesc" cname="点位描述"/>
            <EF:EFColumn ename="tagValue" cname="点位值" align="right"/>
            <EF:EFColumn ename="tagTime" cname="点位时间" width="170" align="center"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0301" id="tagInfo" width="90%" height="60%"/>
</EF:EFPage>