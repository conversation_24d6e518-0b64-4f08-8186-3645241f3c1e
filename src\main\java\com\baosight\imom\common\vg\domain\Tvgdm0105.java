/**
 * Generate time : 2024-12-12 13:31:53
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.validator.constraints.NotBlank;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm0105
 */
public class Tvgdm0105 extends DaoEPBase {

    @NotBlank(message = "分部设备不能为空")
    private String deviceCode = " ";        /* 分部设备代码*/
    private String deviceName = " ";        /* 分部设备名称*/
    @NotBlank(message = "检修周期不能为空")
    private String overhaulCycleType = " ";        /* 检修周期类型*/
    private Integer cycleDay = 0;        /* 天数*/
    private Integer cycleNum = 0;        /* 加工量*/
    @NotBlank(message = "工器具及备件不能为空")
    private String overhaulTool = " ";        /* 检修工具*/
    @NotBlank(message = "检修详细步骤不能为空")
    private String overhaulStep = " ";        /* 检修步骤*/
    @NotBlank(message = "安全注意事项不能为空")
    private String securityMeasures = " ";        /* 安全措施*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulCycleType");
        eiColumn.setDescName("检修周期类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cycleDay");
        eiColumn.setDescName("天数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cycleNum");
        eiColumn.setDescName("加工量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulTool");
        eiColumn.setDescName("检修工具");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulStep");
        eiColumn.setDescName("检修步骤");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("securityMeasures");
        eiColumn.setDescName("安全措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0105() {
        initMetaData();
    }

    /**
     * get the deviceCode - 分部设备代码
     *
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     *
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the overhaulCycleType - 检修周期类型
     *
     * @return the overhaulCycleType
     */
    public String getOverhaulCycleType() {
        return this.overhaulCycleType;
    }

    /**
     * set the overhaulCycleType - 检修周期类型
     */
    public void setOverhaulCycleType(String overhaulCycleType) {
        this.overhaulCycleType = overhaulCycleType;
    }

    /**
     * get the cycleDay - 天数
     *
     * @return the cycleDay
     */
    public Integer getCycleDay() {
        return this.cycleDay;
    }

    /**
     * set the cycleDay - 天数
     */
    public void setCycleDay(Integer cycleDay) {
        this.cycleDay = cycleDay;
    }

    /**
     * get the cycleNum - 加工量
     *
     * @return the cycleNum
     */
    public Integer getCycleNum() {
        return this.cycleNum;
    }

    /**
     * set the cycleNum - 加工量
     */
    public void setCycleNum(Integer cycleNum) {
        this.cycleNum = cycleNum;
    }

    /**
     * get the overhaulTool - 检修工具
     *
     * @return the overhaulTool
     */
    public String getOverhaulTool() {
        return this.overhaulTool;
    }

    /**
     * set the overhaulTool - 检修工具
     */
    public void setOverhaulTool(String overhaulTool) {
        this.overhaulTool = overhaulTool;
    }

    /**
     * get the overhaulStep - 检修步骤
     *
     * @return the overhaulStep
     */
    public String getOverhaulStep() {
        return this.overhaulStep;
    }

    /**
     * set the overhaulStep - 检修步骤
     */
    public void setOverhaulStep(String overhaulStep) {
        this.overhaulStep = overhaulStep;
    }

    /**
     * get the securityMeasures - 安全措施
     *
     * @return the securityMeasures
     */
    public String getSecurityMeasures() {
        return this.securityMeasures;
    }

    /**
     * set the securityMeasures - 安全措施
     */
    public void setSecurityMeasures(String securityMeasures) {
        this.securityMeasures = securityMeasures;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setOverhaulCycleType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulCycleType")), overhaulCycleType));
        setCycleDay(NumberUtils.toInteger(StringUtils.toString(map.get("cycleDay")), cycleDay));
        setCycleNum(NumberUtils.toInteger(StringUtils.toString(map.get("cycleNum")), cycleNum));
        setOverhaulTool(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulTool")), overhaulTool));
        setOverhaulStep(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulStep")), overhaulStep));
        setSecurityMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("securityMeasures")), securityMeasures));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("overhaulCycleType", StringUtils.toString(overhaulCycleType, eiMetadata.getMeta("overhaulCycleType")));
        map.put("cycleDay", StringUtils.toString(cycleDay, eiMetadata.getMeta("cycleDay")));
        map.put("cycleNum", StringUtils.toString(cycleNum, eiMetadata.getMeta("cycleNum")));
        map.put("overhaulTool", StringUtils.toString(overhaulTool, eiMetadata.getMeta("overhaulTool")));
        map.put("overhaulStep", StringUtils.toString(overhaulStep, eiMetadata.getMeta("overhaulStep")));
        map.put("securityMeasures", StringUtils.toString(securityMeasures, eiMetadata.getMeta("securityMeasures")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}