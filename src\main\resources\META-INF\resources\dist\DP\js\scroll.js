// 无缝滚动功能
function initSmoothScroll(container) {
    const wrapper = container.querySelector('.all-con-wrapper');
    const content = container.querySelector('.all-con-content');
    
    if (!wrapper || !content) return;
    
    // 获取容器和内容的高度
    const containerHeight = container.offsetHeight;
    const contentHeight = content.offsetHeight;
    
    // 只有当内容高度大于容器高度时才进行滚动
    if (contentHeight > containerHeight) {
        // 克隆内容用于无缝滚动
        const clone = content.cloneNode(true);
        wrapper.appendChild(clone);
        
        // 设置滚动时间（每像素500毫秒）
        const scrollTime = Math.max(contentHeight * 0.5, 10); // 每像素0.5秒，最小10秒
        
        // 设置动画
        wrapper.style.animation = `scroll ${scrollTime}s linear infinite`;
        
        // 当动画结束时重置位置
        wrapper.addEventListener('animationend', () => {
            wrapper.style.animation = 'none';
            wrapper.offsetHeight; // 触发重排
            wrapper.style.animation = `scroll ${scrollTime}s linear infinite`;
        });

        // 添加鼠标悬停暂停功能
        container.addEventListener('mouseenter', () => {
            wrapper.style.animationPlayState = 'paused';
        });

        container.addEventListener('mouseleave', () => {
            wrapper.style.animationPlayState = 'running';
        });
    } else {
        // 如果内容不超出，移除可能存在的克隆内容
        const clone = wrapper.querySelector('.all-con-content:last-child');
        if (clone && clone !== content) {
            clone.remove();
        }
        // 确保没有动画
        wrapper.style.animation = 'none';
    }
}

// 初始化所有滚动容器
function initAllScrollContainers() {
    const containers = document.querySelectorAll('.all-con');
    containers.forEach(container => {
        initSmoothScroll(container);
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initAllScrollContainers);

// 监听窗口大小变化，重新计算是否需要滚动
window.addEventListener('resize', () => {
    const containers = document.querySelectorAll('.all-con');
    containers.forEach(container => {
        initSmoothScroll(container);
    });
}); 