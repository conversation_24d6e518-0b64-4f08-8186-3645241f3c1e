$(function () {

    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();


    // 列表业务单元弹窗
/*
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });
*/

    var window1 = $("#REGISTERUSER");
    var window2 = $("#insertGroup");
    var authCopyWindow = $("#authCopy");
    var authMemberWindow = $("#authMember");

    //监听粘贴板导入
    var handleFun;
    var pastedContent;
    document.addEventListener('paste', function (evt) {
        var clipdata = evt.clipboardData || window.clipboardData;
        pastedContent = clipdata.getData('text/plain');
        if (evt.target.id == 'clipContent') {
            clipWindow.close();
            handleFun(pastedContent);
        }
    });

    function RSAEncrypt(str) {
        // Encrypt with the public key...
        var encrypt = new JSEncrypt();
        encrypt.setPublicKey($('#__LOGIN_PUBLICKEY__').val());
        return encrypt.encrypt(str);
    }

    $("#QUERYGROUP").on("click", function (e) {
        resultBGrid.dataSource.page(1);
    });

    $("#QUERYGROUPENAME").on("click",function () {
        resultCGrid.dataSource.page(1);
    });

    $("#QUERYUSER").on("click", function () {
        resultFGrid.dataSource.page(1);
    });

    $(window).load(function () {
        resultGrid.setEiInfo(__eiInfo);
    });
    $(document.body).on("click", "#QUERY", function (e) {
        var loginName = $("[name = 'inqu_status-0-loginName']").val();
        var userName = $("[name = 'inqu_status-0-userName']").val();
        var userGroupEname = $("[name = 'inqu_status-0-userGroupEname']").val();
        $("#inqu_status-0-loginName").val(loginName);
        $("#inqu_status-0-userName").val(userName);
        $("#inqu_status-0-userGroupEname").val(userGroupEname);

        var segNo = $("#inqu_status-0-segNo").val();
        if (segNo == null || segNo == "") {
            IPLAT.alert("请选择业务单元号再进行查询");
            return false;
        }else{
            resultGrid.dataSource.page(1);
        }
    });

    $("#ef_grid_resultD").on("click", ".auth_copy_btn", function (e) {
        var model = resultDGrid.dataItem($(this).closest("tr"));
        var groupId = model.get("groupId");
        var groupEname = model.get("groupEname");
        if (groupEname == 'ADMIN') {
            IPLAT.alert("系统管理员组不能进行权限复制!");
            return;
        }
        var groupCname = model.get("groupCname");
        $("#inqu_status-0-insertUserParentId").val(groupId);
        resultFGrid.dataSource.page(1);
        authCopyWindow.data("kendoWindow").open();
        $("#authCopy_wnd_title").css({"text-align":"justify","font-size":"14px","color":"#FFF"});
        $("#authCopy_wnd_title").html("正在复制: [" + groupCname + "] 的权限信息...");
    });

    $("#ef_grid_resultD").on("click", ".check_member_btn", function (e) {
        var model = resultDGrid.dataItem($(this).closest("tr"));
        var groupId = model.get("groupId");
        var groupCname = model.get("groupCname");
        $("#inqu_status-0-checkUserParentId").val(groupId);
        resultGGrid.dataSource.page(1);
        authMemberWindow.data("kendoWindow").open();
        $("#authMember_wnd_title").css({"text-align":"justify","font-size":"14px","color":"#FFF"});
        $("#authMember_wnd_title").html("正在查看: [" + groupCname + "] 的成员用户...");
    });




    var validator1 = IPLAT.Validator({
        errorTemplate: "<span class='k-widget k-tooltip k-tooltip-validation' style='width: 300px; text-align: left'><span class='k-icon k-i-warning'> </span> #=message#</span>",
        id: "registerUser",
        groupName: "group1"
    });
    var validator2 = IPLAT.Validator({
        errorTemplate: "<span class='k-widget k-tooltip k-tooltip-validation' style='width: 300px; text-align: left'><span class='k-icon k-i-warning'> </span> #=message#</span>",
        id: "groupName",
        groupName: "group2"
    });
    window.validator1 = validator1;
    window.validator2 = validator2;


    $("#inqu_status-0-loginName").change(function () {
        var regex = /^[_a-zA-Z0-9]{1,64}$/;
        var loginName = $(this).val();
        loginName = loginName.trim();
        if (!regex.test(loginName)) {
            return false;
        }
        var inInfo = new EiInfo();
        inInfo.set("loginName", loginName);
        EiCommunicator.send("XS0102", "checkLoginName", inInfo, {
            onSuccess: function (ei) {
                if (-1 == ei.getStatus()) {
                    $("#inqu_status-0-loginName-prompt").attr("style", "color:red");
                    $("#inqu_status-0-loginName-prompt").html("【" + ei.getMsg() + "】");
                    $("#REGISTERNEWUSER").attr("disabled", true);
                } else {
                    $("#inqu_status-0-loginName-prompt").attr("style", "color:blue");
                    $("#inqu_status-0-loginName-prompt").html("【该登录账号可以使用】");
                    $("#REGISTERNEWUSER").attr("disabled", false);
                }
            }, onFail: function (ei) {
                $("#inqu_status-0-loginName-prompt").html("【该登录账号无法使用】");
            }
        })
    });


    $("#inqu_status-0-password").change(function () {
        var checkpasswordSwitch = $("#checkpasswordSwitch").val();
        if ("on" == checkpasswordSwitch) {
            var password = $(this).val();
            password = password.trim();
            var inInfo = new EiInfo();
            inInfo.set("password", password);
            EiCommunicator.send("XS0102", "checkPassword", inInfo, {
                onSuccess: function (ei) {
                    if (-1 == ei.getStatus()) {
                        $("#inqu_status-0-password-prompt").attr("style", "color:red");
                        $("#inqu_status-0-password-prompt").html("【" + ei.getMsg() + "】");
                        $("#REGISTERNEWUSER").attr("disabled", true);
                    }
                    else {
                        $("#inqu_status-0-password-prompt").attr("style", "color:blue");
                        $("#inqu_status-0-password-prompt").html("【" + ei.getMsg() + "】");
                        $("#REGISTERNEWUSER").attr("disabled", false);
                    }

                }, onFail: function (ei) {
                    $("#inqu_status-0-password-prompt").html("【" + ei.getMsg() + "】");
                }
            })
        }
    });

    $("#REGISTERNEWUSER").click(function () {
        var arr = Object.keys(window.validator1._errors);
        if(arr.length > 0) {
            IPLAT.alert("新增失败,请检查填写格式是否正确");
            return false;
        }
        var eiInfo = new EiInfo();
        eiInfo.setByNode("inqua");
        if($('#cryptoPasswordEnable').val()==='on'){
            var segNo = eiInfo.blocks.details.rows[0][0];
            if(null==segNo||""==segNo){
                NotificationUtil({msg: "必须选择业务账套号！"}, "error");
                e.preventDefault();
            }
            var loginName=RSAEncrypt(eiInfo.blocks.details.rows[0][1]);
            var userName=RSAEncrypt(eiInfo.blocks.details.rows[0][2]);
            var rsaPassword=RSAEncrypt(eiInfo.blocks.details.rows[0][3]);
            var rsaRePassword=RSAEncrypt(eiInfo.blocks.details.rows[0][4]);
            eiInfo.blocks.details.rows[0][1]=loginName;
            eiInfo.blocks.details.rows[0][2]=userName;
            eiInfo.blocks.details.rows[0][3]=rsaPassword;
            eiInfo.blocks.details.rows[0][4]=rsaRePassword;
        }
        EiCommunicator.send("XTSS05", "insert", eiInfo, {
            onSuccess: function (ei) {
                if (-1 == ei.getStatus()) {
                    IPLAT.alert(ei.getMsg());
                } else {
                    IPLAT.alert({
                        message: '<b>注册成功！</b>',
                        okFn: function (e) {
                            REGISTERUSERWindow.close();
                            window.refreshQuery();
                        },
                        title: '提示信息'
                    });
                }
            }, onFail: function (ei) {
                IPLAT.alert(ei.getMsg());
                //  alert("注册失败");
            }
        })
    });


    IPLATUI.EFGrid = {

        "result": {
            beforeEdit: function (e) {
                var row = e.row;
                $("#editRow").val(row);
            },
            loadComplete: function (gridInstance) {
                $(".k-grid-REGISTER").on("click", function (e) {
                    window1.data("kendoWindow").open();
                    $("#inqu_status-0-loginName").val("");
                    $("#inqu_status-0-userName").val("");
                    $("#REGISTERUSER_wnd_title").css({"text-align": "justify", "font-size": "14px", "color": "#FFF"});
                    $("#REGISTERUSER_wnd_title").html("正在注册新用户");
                });

                $("#CLIPBOARD").on("click", function (e) {
                    var content = "";
                    if (IPLAT.Browser.isIE) {
                        content = window.clipboardData.getData("Text");
                    } else {
                        clipWindow.center().open();
                        handleFun = postHandle;
                    }

                    function postHandle(content) {
                        // 清空checkBox
                        /*grid.unCheckAllRows();*/
                        var length = grid.getDataItems().length;
                        console.log(grid.getDataItems());
                        if (length > 0) {
                            grid.unCheckAllRows();
                        }
                        // 取得剪切板内容
                        if (IPLAT.isAvailable(content)) {
                            var lines = content.split("\r\n");
                            var model = [];
                            for (var i = 0; i < lines.length; i++) {
                                var m = getRow(lines[i]);
                                m != null && model.push(m);
                            }
                            grid.addRows(model, false, true);

                        }
                    }

                    if (IPLAT.Browser.isIE) {
                        postHandle(content);
                    }
                });

            },

            toolbarConfig: {
                add: false,
                hidden: false
            },
            change:function(){
                var selectRow = this.select();
                var item = this.dataItem(selectRow);
                $("#inqu_status-0-userIdForParentGroups").val(item.userId);
                resultDGrid.dataSource.page(1);
                $("#inqu_status-0-loginNameForAuthInfo").val(item.loginName);
                resultEGrid.dataSource.page(1);
            },
            onDelete: function (e) {
                if (resultGrid.getCheckedRows().length == 0) {
                    IPLAT.alert("请勾选数据!");
                    e.preventDefault();
                }
            }
        },
        "resultB": {
            "exportGrid":false,
            loadComplete: function (grid) {
                $("#ADDGROUP").on("click", function (e) {
                    var checkRows = grid.getCheckedRows();
                    if (checkRows.length > 0) {
                        var model = checkRows[0];
                        var groupEname = model.get("gEname");
                        var groupCname = model.get("gCname");
                        var resultGridModels = resultGrid.getDataItems();
                        var row = $("#editRow").val();
                        var resultGridModel = resultGridModels[row];
                        resultGridModel.set("userGroupEname", groupEname);
                        resultGridModel.set("userGroupCname", groupCname);
                        window2.data("kendoWindow").close();
                    } else {
                        IPLAT.alert("没有选中数据");
                    }
                });
            }
        },
        "resultC": {

            "exportGrid":false,

            loadComplete: function (grid) {
                $("#ADDGROUPENAME").on("click",function(){
                    var checkRows = resultCGrid.getCheckedRows();
                    // checkRows 存储的是选中的列
                    // 获取第一列
                    var model = checkRows[0];
                    if (model) {
                        $("#inqu_status-0-groupName").val(model['groupEname']);
                        $("#inqu_status-0-groupName_textField").val(model['groupEname']);
                        var popupGridWindow = $("#insertUserGroupEname").data("kendoWindow");
                        popupGridWindow.close();
                    } else {
                        IPLAT.alert("没有选中数据");
                    }
                });
            }
        },

        "resultD": {
            columns: [
                {
                    field: "operation",
                    title: "操作",
                    width: 85,
                    headerTemplate: "<span style='display:block;text-align:center;'>操作</span>",
                    template: "<span style='display:block;text-align: center;'><input class='i-btn-sm auth_copy_btn' type='button' value='权限复制'><input class='i-btn-sm check_member_btn' type='button' value='查看成员'></span>"
                }
            ],
        },

        "resultF": {
            loadComplete: function (grid) {
                $("#COPYAUTH").on("click", function (e) {
                    var checkRows = grid.getCheckedRows();
                    if (checkRows.length > 0) {
                        var eiInfo = new EiInfo();
                        var block = new EiBlock("result");
                        block.getBlockMeta().addMeta(new EiColumn("memberId"));
                        block.getBlockMeta().addMeta(new EiColumn("parentId"));
                        block.getBlockMeta().addMeta(new EiColumn("memberType"));
                        var parentId = $("#inqu_status-0-insertUserParentId").val();
                        for(var i = 0; i < checkRows.length; i++){
                            var model = checkRows[i];
                            block.setCell(i, "memberId", model.get("userId"));
                            block.setCell(i, "parentId", parentId);
                            block.setCell(i, "memberType", "USER");
                        }
                        eiInfo.addBlock(block);
                        EiCommunicator.send("XS03", "insert", eiInfo, {
                            onSuccess: function (ei) {
                                if (-1 == ei.getStatus()) {
                                    IPLAT.alert(ei.getMsg());
                                } else {
                                    IPLAT.alert("复制权限成功");
                                }
                            }, onFail: function (ei) {
                                IPLAT.alert(ei.getMsg());
                            }
                        });
                        authCopyWindow.data("kendoWindow").close();
                    } else {
                        IPLAT.alert("没有选中数据");
                    }


                });
            }
        }
    }


    IPLATUI.EFWindow = {
        "clip": {
            open: function (e) {
                document.getElementById("clipContent").value = "";
            }
        },
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    //$("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    //$("#inqua_status-0-segNo").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }
            }
        }
        /*"unitInfo2": {
            // 打开窗口事件
            open: function(e) {
                var $iframe = unitInfo2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo2");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    resultGrid.setCellValue(editorModel,"unitCode", row[0].unitCode);
                    resultGrid.setCellValue(editorModel,"segNo", row[0].segNo);
                    resultGrid.setCellValue(editorModel,"segName", row[0].segName);
                    dataGrid.unCheckAllRows();
                }
            }
        }*/
    }

    function getRow(line) {
        var idField = resultGrid.dataSource.options.schema.model.id, // 主键field name
            fields = resultGrid.dataSource.options.schema.model.fields;

        var BizModel = kendo.data.Model.define({
            id: idField,
            fields: fields,
        });
        var row = {};
        if (IPLAT.isAvailable(line)) {
            var arrs = line.split("\t");
            var mouldManageType ="";

            let segNo = arrs[0];
            if (segNo === "") {
                NotificationUtil({msg: "业务单元代码不可为空!"}, "error");
                return false;
            }

            if (IPLAT.isAvailable(arrs[0])) {
                row.segName = arrs[1];
                row.unitCode = arrs[0];
                row.segNo = arrs[0];
                row.mouldManageType =mouldManageType ;
                row.mouldNo = arrs[3];
                row.mouldName = arrs[4];
                row.fixtureCheckFirm = arrs[5];
                row.specDesc = arrs[6];
                row.wt = arrs[7];
                row.buyDate = arrs[8]
                row.checkDate = arrs[9]
                row.mouldModles = arrs[10];
                row.productName = arrs[11];
                row.managePerson = arrs[12];
                row.userWeld = arrs[13];
                row.remark = arrs[14];

                var inInfo = new EiInfo();
                inInfo.set("row", row);
                var r = new BizModel(row);
                r.dirty = true;
                return r;
            }
        }
    }


});

function refreshQuery() {
    resultGrid.dataSource.page(1);
}



