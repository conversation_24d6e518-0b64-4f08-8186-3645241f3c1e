<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-08-20 10:43:40
   		Version :  1.0
		tableName :meli.tlirl0509 
		 UUID  BIGINT   NOT NULL   primarykey, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 DELIVERY_STATUS  VARCHAR   NOT NULL, 
		 D_USER_NUM  VARCHAR   NOT NULL, 
		 D_USER_NUM_NAME  VARCHAR   NOT NULL, 
		 LADING_BILL_ID  VARCHAR   NOT NULL, 
		 LADING_WEIGHT  DECIMAL, 
		 DISPENSING_QUANTITY  VARCHAR   NOT NULL, 
		 JOB_STATUS  VARCHAR, 
		 CUSTOMER_ID  VARCHAR, 
		 CUSTOMER_NAME  VARCHAR, 
		 STATUS  VARCHAR, 
		 REQUIRE_FINISH_DATE  VARCHAR, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0510">
	<sql id="condition">
            <isNotEmpty prepend=" AND " property="uuid">
                UUID = #uuid#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="segNo">
                SEG_NO = #segNo#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="vehicleNo">
                VEHICLE_NO like concat('%',#vehicleNo#, '%')
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="deliveryStatus">
                DELIVERY_STATUS = #deliveryStatus#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="d_userNum">
                D_USER_NUM = #d_userNum#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="d_userNumName">
                D_USER_NUM_NAME like concat('%',#d_userNumName#, '%')
            </isNotEmpty>

            <isNotEmpty prepend=" AND " property="ladingBillId">
                LADING_BILL_ID like concat('%',#ladingBillId#, '%')
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="ladingWeight">
                LADING_WEIGHT = #ladingWeight#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dispensingQuantity">
                DISPENSING_QUANTITY = #dispensingQuantity#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="jobStatus">
                JOB_STATUS = #jobStatus#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="customerId">
                CUSTOMER_ID = #customerId#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="customerName">
                CUSTOMER_NAME like concat('%',#customerName#, '%')
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">
                STATUS = #status#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="requireFinishDate">
                REQUIRE_FINISH_DATE = #requireFinishDate#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="recCreator">
                REC_CREATOR = #recCreator#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="recCreatorName">
                REC_CREATOR_NAME = #recCreatorName#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="recCreateTime">
                REC_CREATE_TIME = #recCreateTime#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="recRevisor">
                REC_REVISOR = #recRevisor#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="recRevisorName">
                REC_REVISOR_NAME = #recRevisorName#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="recReviseTime">
                REC_REVISE_TIME = #recReviseTime#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="archiveFlag">
                ARCHIVE_FLAG = #archiveFlag#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="delFlag">
                DEL_FLAG = #delFlag#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="remark">
                REMARK = #remark#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="tenantId">
                TENANT_ID = #tenantId#
            </isNotEmpty>
			<isNotEmpty prepend=" and " property="recCreateTimeStart">
				substr(REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
			</isNotEmpty>
			<!--创建时间止-->
			<isNotEmpty prepend=" and " property="recCreateTimeEnd">
				substr(REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
			</isNotEmpty>
	</sql>
	
	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0510">
		SELECT
		UUID as "uuid",  <!-- 主键ID -->
		SEG_NO as "segNo",  <!-- 系统账套 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 t where t.SEG_NO = tlirl0509.SEG_NO and t.DEL_FLAG = 0) as
		"segName",         <!-- 业务单元简称 -->
		VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
		DELIVERY_STATUS as "deliveryStatus",  <!-- 交付状态 -->
		D_USER_NUM as "d_userNum",  <!-- 分户号代码 -->
		D_USER_NUM_NAME as "d_userNumName",  <!-- 分户号简称 -->
		LADING_BILL_ID as "ladingBillId",  <!-- 提单号 -->
		LADING_WEIGHT as "ladingWeight",  <!-- 提单量 -->
		DISPENSING_QUANTITY as "dispensingQuantity",  <!-- 配单量 -->
		JOB_STATUS as "jobStatus",  <!-- 作业状态 -->
		CUSTOMER_ID as "customerId",  <!-- 运输单位代码 -->
		CUSTOMER_NAME as "customerName",  <!-- 运输单位名称 -->
		STATUS as "status",  <!-- 状态：10,20 -->
		REQUIRE_FINISH_DATE as "requireFinishDate",  <!-- 交付时间 -->
		REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR as "recRevisor",  <!-- 记录修改人姓名 -->
		REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人 -->
		REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		REMARK as "remark",  <!-- 备注 -->
		TENANT_ID as "tenantId", <!-- 租户ID -->
		REMAINING_TIME_DISPLAY as "remainingTimeDisplay"
		FROM meli.tlirl0509 WHERE 1=1
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			REC_REVISE_TIME desc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0509 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deliveryStatus">
			DELIVERY_STATUS = #deliveryStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="d_userNum">
			D_USER_NUM = #d_userNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="d_userNumName">
			D_USER_NUM_NAME = #d_userNumName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ladingBillId">
			LADING_BILL_ID = #ladingBillId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ladingWeight">
			LADING_WEIGHT = #ladingWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dispensingQuantity">
			DISPENSING_QUANTITY = #dispensingQuantity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jobStatus">
			JOB_STATUS = #jobStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="requireFinishDate">
			REQUIRE_FINISH_DATE = #requireFinishDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0510 (UUID,  <!-- 主键ID -->
										SEG_NO,  <!-- 系统账套 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										DELIVERY_STATUS,  <!-- 交付状态 -->
										D_USER_NUM,  <!-- 分户号代码 -->
										D_USER_NUM_NAME,  <!-- 分户号简称 -->
										LADING_BILL_ID,  <!-- 提单号 -->
										LADING_WEIGHT,  <!-- 提单量 -->
										DISPENSING_QUANTITY,  <!-- 配单量 -->
										JOB_STATUS,  <!-- 作业状态 -->
										CUSTOMER_ID,  <!-- 运输单位代码 -->
										CUSTOMER_NAME,  <!-- 运输单位名称 -->
										STATUS,  <!-- 状态：10,20 -->
										REQUIRE_FINISH_DATE,  <!-- 交付时间 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人姓名 -->
										REC_REVISOR_NAME,  <!-- 记录修改人 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										TENANT_ID,  <!-- 租户ID -->
		CAR_TRACE_NO,
		ALLOCATE_VEHICLE_NO,
		REMAINING_TIME_DISPLAY
										)		 
	    VALUES (#uuid#, #segNo#, #vehicleNo#, #deliveryStatus#, #d_userNum#, #d_userNumName#, #ladingBillId#, #ladingWeight#, #dispensingQuantity#, #jobStatus#, #customerId#, #customerName#, #status#, #requireFinishDate#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #tenantId#,#carTraceNo#,#allocateVehicleNo#,#remainingTimeDisplay#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0509 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0510
		SET 
					SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					DELIVERY_STATUS	= #deliveryStatus#,   <!-- 交付状态 -->  
					D_USER_NUM	= #d_userNum#,   <!-- 分户号代码 -->  
					D_USER_NUM_NAME	= #d_userNumName#,   <!-- 分户号简称 -->  
					LADING_BILL_ID	= #ladingBillId#,   <!-- 提单号 -->  
					LADING_WEIGHT	= #ladingWeight#,   <!-- 提单量 -->  
					DISPENSING_QUANTITY	= #dispensingQuantity#,   <!-- 配单量 -->  
					JOB_STATUS	= #jobStatus#,   <!-- 作业状态 -->  
					CUSTOMER_ID	= #customerId#,   <!-- 运输单位代码 -->  
					CUSTOMER_NAME	= #customerName#,   <!-- 运输单位名称 -->  
					STATUS	= #status#,   <!-- 状态：10,20 -->  
					REQUIRE_FINISH_DATE	= #requireFinishDate#,   <!-- 交付时间 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人姓名 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>


	<update id="updateStatus">
		UPDATE meli.tlirl0510
		SET
		SEG_NO	= #segNo#,   <!-- 系统账套 -->
		STATUS	= #status#,   <!-- 状态：10,20 -->
		REQUIRE_FINISH_DATE	= #requireFinishDate#,   <!-- 交付时间 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人姓名 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人 -->
		REC_REVISE_TIME	= #recReviseTime#  <!-- 记录修改时间 -->
		WHERE
		UUID = #uuid#
	</update>

	<update id="updateRemainingTimeDisplay">
		UPDATE meli.tlirl0510
		SET
		REQUIRE_FINISH_DATE	= #requireFinishDate#,   <!-- 交付时间 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人姓名 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人 -->
		REC_REVISE_TIME	= #recReviseTime#,  <!-- 记录修改时间 -->
		REMAINING_TIME_DISPLAY=#remainingTimeDisplay#
		<isNotEmpty prepend="," property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend="," property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="," property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="," property="dispensingQuantity">
			DISPENSING_QUANTITY = #dispensingQuantity#
		</isNotEmpty>
		<isNotEmpty prepend="," property="jobStatus">
			JOB_STATUS = #jobStatus#
		</isNotEmpty>
		<isNotEmpty prepend="," property="deliveryStatus">
			DELIVERY_STATUS = #deliveryStatus#
		</isNotEmpty>
		WHERE
		SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="ladingBillId">
			LADING_BILL_ID = #ladingBillId#
		</isNotEmpty>
		and STATUS='20'
		and UUID=#uuid#

	</update>

	<update id="updateStatusC">
		UPDATE meli.tlirl0510
		SET
		STATUS	= #status#   <!-- 状态：10,20 -->
		WHERE
		SEG_NO = #segNo#
		and  CAR_TRACE_NO = #carTraceNo#
	</update>

	<update id="updateD">
		UPDATE meli.tlirl0510
		SET
		STATUS	= #status#,   <!-- 状态：10,20 -->
		REMARK	= #remark#
		WHERE
		SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="ladingBillIdList">
			LADING_BILL_ID IN
			<iterate open="(" close=")" conjunction="," property="ladingBillIdList">
				#ladingBillIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="ladingBillId">
			LADING_BILL_ID = #ladingBillId#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="statusNo">
			STATUS not in ('00','99')
		</isNotEmpty>
	</update>


	<select id="queryAllDelivery" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT main_data.DELIVERY_STATUS as "deliveryStatus",
		main_data.customerName,
		main_data.totalQuantity,
		main_data.jobStatusName,
		main_data.VEHICLE_NO as "vehicleNo",
		main_data.remainingTimeDisplay,
		main_data.ladingBillIds,
		main_data.D_USER_NUM_NAME as "dUserNumName",
		main_data.totalLadingWeight
		FROM (SELECT distinct t509.DELIVERY_STATUS,
		t509.CUSTOMER_NAME               as customerName,
		sum(t503.NET_WEIGHT)             as totalQuantity,
		max(case
		when t509.JOB_STATUS = '10' then '未配单'
		when t509.JOB_STATUS = '20' then '未进厂已配单'
		when t509.JOB_STATUS = '30' then '进厂已配单未启动排队'
		when t509.JOB_STATUS = '40' then '排队中'
		when t509.JOB_STATUS = '50' then '已叫号'
		when t509.JOB_STATUS = '60' then '作业中'
		else '作业完成未出厂'
		end)                         as jobStatusName,
		t509.VEHICLE_NO,
		min(t509.REMAINING_TIME_DISPLAY) as remainingTimeDisplay,
		GROUP_CONCAT(
		DISTINCT t503.VOUCHER_NUM
		ORDER BY t509.REQUIRE_FINISH_DATE ASC, t509.LADING_BILL_ID ASC
		SEPARATOR '/'
		)                                as ladingBillIds,
		max(t509.D_USER_NUM_NAME)        as D_USER_NUM_NAME,
		sum(distinct t503.LADING_WEIGHT) as totalLadingWeight,
		case t509.DELIVERY_STATUS
		when '30' then 2
		when '20' then 3
		when '10' then 4
		else 5
		end                          as status_priority
		FROM meli.tlirl0510 t509
		LEFT JOIN meli.tlirl0503 t503 ON t503.SEG_NO = t509.SEG_NO
		AND t503.ALLOCATE_VEHICLE_NO = t509.ALLOCATE_VEHICLE_NO
		AND t509.STATUS NOT IN ('99', '00')
		WHERE t509.SEG_NO = 'JC000000'
		AND t509.STATUS = '20'
		AND t509.VEHICLE_NO != ''
		AND t509.VEHICLE_NO IS NOT NULL
		GROUP BY t509.VEHICLE_NO, t509.DELIVERY_STATUS, t509.CUSTOMER_NAME

		UNION ALL

		SELECT t509.DELIVERY_STATUS,
		t509.CUSTOMER_NAME                              as customerName,
		t509.DISPENSING_QUANTITY                        as totalQuantity,
		case
		when t509.JOB_STATUS = '10' then '未配单'
		when t509.JOB_STATUS = '20' then '未进厂已配单'
		when t509.JOB_STATUS = '30' then '进厂已配单未启动排队'
		when t509.JOB_STATUS = '40' then '排队中'
		when t509.JOB_STATUS = '50' then '已叫号'
		when t509.JOB_STATUS = '60' then '作业中'
		else '作业完成未出厂'
		end                                         as jobStatusName,
		t509.VEHICLE_NO,
		t509.REMAINING_TIME_DISPLAY                     as remainingTimeDisplay,
		COALESCE(t503.VOUCHER_NUM, t509.LADING_BILL_ID) as ladingBillIds,
		t509.D_USER_NUM_NAME,
		t509.LADING_WEIGHT                              as totalLadingWeight,
		case t509.DELIVERY_STATUS
		when '30' then 2
		when '20' then 3
		when '10' then 4
		else 5
		end                                         as status_priority
		FROM meli.tlirl0510 t509
		LEFT JOIN meli.tlirl0503 t503 ON t503.SEG_NO = t509.SEG_NO
		AND t503.ALLOCATE_VEHICLE_NO = t509.ALLOCATE_VEHICLE_NO
		AND t509.STATUS NOT IN ('99', '00')
		WHERE t509.SEG_NO = 'JC000000'
		AND t509.STATUS = '20'
		AND (t509.VEHICLE_NO = '' OR t509.VEHICLE_NO IS NULL)) main_data
		WHERE main_data.status_priority = (SELECT min(case t.DELIVERY_STATUS
		when '30' then 2
		when '20' then 3
		when '10' then 4
		else 5
		end)
		FROM meli.tlirl0510 t
		WHERE t.SEG_NO = 'JC000000'
		AND t.STATUS = '20'
		AND (
		(main_data.VEHICLE_NO != '' AND main_data.VEHICLE_NO IS NOT NULL AND
		t.VEHICLE_NO = main_data.VEHICLE_NO)
		OR
		(main_data.VEHICLE_NO = '' OR main_data.VEHICLE_NO IS NULL) AND
		t.LADING_BILL_ID = main_data.ladingBillIds
		))
		ORDER BY main_data.status_priority ASC,
		main_data.VEHICLE_NO ASC
	</select>
</sqlMap>