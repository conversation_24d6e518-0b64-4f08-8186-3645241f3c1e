<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-06-18 17:04:58
   		Version :  1.0
		tableName :meli.tlids1104 
		 SEG_NO  VARCHAR, 
		 UNIT_CODE  VARCHAR, 
		 CRANE_ID  VARCHAR, 
		 CRANE_NAME  VARCHAR, 
		 GRAB_SYS_ID  VARCHAR, 
		 RELEASE_SYS_ID  VARCHAR, 
		 X_VALUE  VARCHAR, 
		 Y_VALUE  VARCHAR, 
		 Z_VALUE  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR
	-->
<sqlMap namespace="LIDS1104">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.ds.domain.LIDS1104">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				CRANE_ID	as "craneId",  <!-- 行车编号 -->
				CRANE_NAME	as "craneName",  <!-- 行车名称 -->
				GRAB_SYS_ID	as "grabSysId",  <!-- 抓取流水号 -->
				RELEASE_SYS_ID	as "releaseSysId",  <!-- 释放流水号 -->
				X_VALUE	as "x_value",  <!-- X轴值 -->
				Y_VALUE	as "y_value",  <!-- Y轴值 -->
				Z_VALUE	as "z_value",  <!-- Z轴值 -->
				REC_CREATE_TIME	as "recCreateTime" <!-- 记录创建时间 -->
		FROM meli.tlids1104 WHERE 1=1
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids1104 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="grabSysId">
			GRAB_SYS_ID = #grabSysId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="releaseSysId">
			RELEASE_SYS_ID = #releaseSysId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_value">
			X_VALUE = #x_value#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_value">
			Y_VALUE = #y_value#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="z_value">
			Z_VALUE = #z_value#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids1104 (SEG_NO,  <!-- 系统账套 -->
		UNIT_CODE,  <!-- 业务单元代码 -->
		CRANE_ID,  <!-- 行车编号 -->
		CRANE_NAME,  <!-- 行车名称 -->
		GRAB_SYS_ID,  <!-- 抓取流水号 -->
		RELEASE_SYS_ID,  <!-- 释放流水号 -->
		X_VALUE,  <!-- X轴值 -->
		Y_VALUE,  <!-- Y轴值 -->
		Z_VALUE,  <!-- Z轴值 -->
		REC_CREATE_TIME,  <!-- 记录创建时间 -->
		SEND_TIME
		)
		VALUES (#segNo#, #unitCode#, #craneId#, #craneName#, #grabSysId#, #releaseSysId#, #x_value#, #y_value#,
		#z_value#, #recCreateTime#,#sendTime#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids1104 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlids1104 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					CRANE_ID	= #craneId#,   <!-- 行车编号 -->  
					CRANE_NAME	= #craneName#,   <!-- 行车名称 -->  
					GRAB_SYS_ID	= #grabSysId#,   <!-- 抓取流水号 -->  
					RELEASE_SYS_ID	= #releaseSysId#,   <!-- 释放流水号 -->  
					X_VALUE	= #x_value#,   <!-- X轴值 -->  
					Y_VALUE	= #y_value#,   <!-- Y轴值 -->  
					Z_VALUE	= #z_value#,   <!-- Z轴值 -->  
					REC_CREATE_TIME	= #recCreateTime#  <!-- 记录创建时间 -->  
			WHERE 	
	</update>
  
</sqlMap>