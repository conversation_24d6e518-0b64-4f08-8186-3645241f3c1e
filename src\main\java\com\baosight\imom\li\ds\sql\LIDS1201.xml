<?xml version="1.0" encoding="UTF-8"?>
<!D<PERSON><PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-05 13:10:12
       Version :  1.0
    tableName :meli.tlids1201
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CRANE_RESULT_ID  VARCHAR   NOT NULL,
     CRANE_ORDER_ID  VARCHAR,
     CRANE_ID  VARCHAR   NOT NULL,
     CRANE_NAME  VARCHAR   NOT NULL,
     START_X_POSITION  VARCHAR,
     START_Y_POSITION  VARCHAR,
     START_Z_POSITION  VARCHAR,
     ENDX_POSITION  VARCHAR,
     ENDY_POSITION  VARCHAR,
     ENDZ_POSITION  VARCHAR,
     INBOUND_SEQUENCE_ID  VARCHAR,
     OUTBOUND_SEQUENCE_ID  VARCHAR,
     ABNORMA<PERSON>_FLAG  VARCHAR,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL   primarykey
-->
<sqlMap namespace="LIDS1201">

	<sql id="condition">
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="craneResultId">
			CRANE_RESULT_ID LIKE '%$craneResultId$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneOrderId">
			CRANE_ORDER_ID LIKE '%$craneOrderId$%'
		</isNotEmpty>
        <isNotEmpty prepend=" AND " property="inboundSequenceId">
            INBOUND_SEQUENCE_ID = #inboundSequenceId#
		</isNotEmpty>
        <isNotEmpty prepend=" AND " property="outboundSequenceId">
            OUTBOUND_SEQUENCE_ID = #outboundSequenceId#
        </isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			CRANE_ID LIKE '%$craneId$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			CRANE_NAME LIKE '%$craneName$%'
		</isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTimeStart">
            REC_CREATE_TIME >= #recCreateTimeStart#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTimeEnd">
            REC_CREATE_TIME &lt;= #recCreateTimeEnd#
        </isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isEmpty prepend=" AND " property="status">
			STATUS > '00'
			AND DEL_FLAG = '0'
		</isEmpty>
	</sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1201">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_ORDER_ID as "craneOrderId",  <!-- 行车作业清单号 -->
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        START_X_POSITION as "startXPosition",  <!-- 起始X轴坐标 -->
        START_Y_POSITION as "startYPosition",  <!-- 起始X轴坐标 -->
        START_Z_POSITION as "startZPosition",  <!-- 起始Y轴坐标 -->
        ENDX_POSITION as "endxPosition",  <!-- 终到X轴坐标 -->
        ENDY_POSITION as "endyPosition",  <!-- 终到X轴坐标 -->
        ENDZ_POSITION as "endzPosition",  <!-- 终到Y轴坐标 -->
        INBOUND_SEQUENCE_ID as "inboundSequenceId",  <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID as "outboundSequenceId",  <!-- 释放流水号 -->
        ABNORMAL_FLAG as "abnormalFlag",  <!-- 异常标记 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        START_AREA_TYPE as "startAreaType",  <!-- 起始区域类型 -->
        START_AREA_CODE as "startAreaCode",  <!-- 起始区域类型代码 -->
        START_AREA_NAME as "startAreaName",  <!-- 起始区域类型名称 -->
        END_AREA_TYPE as "endAreaType",  <!-- 终到区域类型 -->
        END_AREA_CODE as "endAreaCode",  <!-- 终到区域类型代码 -->
        END_AREA_NAME as "endAreaName"  <!-- 终到区域类型名称 -->
        FROM meli.tlids1201 t WHERE 1=1
		<include refid="condition"/>
        <isEqual prepend=" AND " property="abnormalFlag" compareValue="1">
            ABNORMAL_FLAG = #abnormalFlag#
        </isEqual>
        <isNotEmpty prepend=" AND " property="packId">
            EXISTS(SELECT 1
            FROM meli.tlids1202 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.CRANE_RESULT_ID = t.CRANE_RESULT_ID
            AND t1.PACK_ID LIKE '%$packId$%'
            AND t1.STATUS > '00')
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
				REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids1201 t WHERE 1=1
		<include refid="condition"/>
        <isNotEmpty prepend=" AND " property="packId">
            EXISTS(SELECT 1
            FROM meli.tlids1202 t1
            WHERE t1.SEG_NO = t.SEG_NO
            AND t1.CRANE_RESULT_ID = t.CRANE_RESULT_ID
            AND t1.PACK_ID LIKE '%$packId$%'
            AND t1.STATUS > '00')
        </isNotEmpty>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneResultId">
            CRANE_RESULT_ID = #craneResultId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneOrderId">
            CRANE_ORDER_ID = #craneOrderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneId">
            CRANE_ID = #craneId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneName">
            CRANE_NAME = #craneName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startXPosition">
            START_X_POSITION = #startXPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startYPosition">
            START_Y_POSITION = #startYPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startZPosition">
            START_Z_POSITION = #startZPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endxPosition">
            ENDX_POSITION = #endxPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endyPosition">
            ENDY_POSITION = #endyPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endzPosition">
            ENDZ_POSITION = #endzPosition#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inboundSequenceId">
            INBOUND_SEQUENCE_ID = #inboundSequenceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outboundSequenceId">
            OUTBOUND_SEQUENCE_ID = #outboundSequenceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="abnormalFlag">
            ABNORMAL_FLAG = #abnormalFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids1201 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
        CRANE_ORDER_ID,  <!-- 行车作业清单号 -->
        CRANE_ID,  <!-- 行车编号 -->
        CRANE_NAME,  <!-- 行车名称 -->
        START_X_POSITION,  <!-- 起始X轴坐标 -->
        START_Y_POSITION,  <!-- 起始X轴坐标 -->
        START_Z_POSITION,  <!-- 起始Y轴坐标 -->
        ENDX_POSITION,  <!-- 终到X轴坐标 -->
        ENDY_POSITION,  <!-- 终到X轴坐标 -->
        ENDZ_POSITION,  <!-- 终到Y轴坐标 -->
        INBOUND_SEQUENCE_ID,  <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID,  <!-- 释放流水号 -->
        ABNORMAL_FLAG,  <!-- 异常标记 -->
        STATUS,  <!-- 状态 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        START_AREA_TYPE,  <!-- 起始区域类型 -->
        START_AREA_CODE,  <!-- 起始区域类型代码 -->
        START_AREA_NAME,  <!-- 起始区域类型名称 -->
        END_AREA_TYPE,  <!-- 终到区域类型 -->
        END_AREA_CODE,  <!-- 终到区域类型代码 -->
        END_AREA_NAME  <!-- 终到区域类型名称 -->
        )
        VALUES (#segNo#, #unitCode#, #craneResultId#, #craneOrderId#, #craneId#, #craneName#, #startXPosition#,
        #startYPosition#, #startZPosition#, #endxPosition#, #endyPosition#, #endzPosition#, #inboundSequenceId#,
        #outboundSequenceId#, #abnormalFlag#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#,#startAreaType#, #startAreaCode#,
        #startAreaName#, #endAreaType#, #endAreaCode#, #endAreaName#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids1201 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlids1201
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        CRANE_RESULT_ID = #craneResultId#,   <!-- 行车实绩单号 -->
        CRANE_ORDER_ID = #craneOrderId#,   <!-- 行车作业清单号 -->
        CRANE_ID = #craneId#,   <!-- 行车编号 -->
        CRANE_NAME = #craneName#,   <!-- 行车名称 -->
        START_X_POSITION = #startXPosition#,   <!-- 起始X轴坐标 -->
        START_Y_POSITION = #startYPosition#,   <!-- 起始X轴坐标 -->
        START_Z_POSITION = #startZPosition#,   <!-- 起始Y轴坐标 -->
        ENDX_POSITION = #endxPosition#,   <!-- 终到X轴坐标 -->
        ENDY_POSITION = #endyPosition#,   <!-- 终到X轴坐标 -->
        ENDZ_POSITION = #endzPosition#,   <!-- 终到Y轴坐标 -->
        START_AREA_TYPE = #startAreaType#,   <!-- 起始区域类型 -->
        START_AREA_CODE = #startAreaCode#,   <!-- 起始区域类型代码 -->
        START_AREA_NAME = #startAreaName#,   <!-- 起始区域类型名称 -->
        END_AREA_TYPE = #endAreaType#,   <!-- 终到区域类型 -->
        END_AREA_CODE = #endAreaCode#,   <!-- 终到区域类型代码 -->
        END_AREA_NAME = #endAreaName#,   <!-- 终到区域类型名称 -->
        INBOUND_SEQUENCE_ID = #inboundSequenceId#,   <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID = #outboundSequenceId#,   <!-- 释放流水号 -->
        ABNORMAL_FLAG = #abnormalFlag#,   <!-- 异常标记 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#  <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <!--    查询当前行车所有实绩-->
    <select id="getAllCranePerformance" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_ORDER_ID as "craneOrderId",  <!-- 行车作业清单号 -->
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        START_X_POSITION as "startXPosition",  <!-- 起始X轴坐标 -->
        START_Y_POSITION as "startYPosition",  <!-- 起始X轴坐标 -->
        START_Z_POSITION as "startZPosition",  <!-- 起始Y轴坐标 -->
        ENDX_POSITION as "endxPosition",  <!-- 终到X轴坐标 -->
        ENDY_POSITION as "endyPosition",  <!-- 终到X轴坐标 -->
        ENDZ_POSITION as "endzPosition",  <!-- 终到Y轴坐标 -->
        INBOUND_SEQUENCE_ID as "inboundSequenceId",  <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID as "outboundSequenceId",  <!-- 释放流水号 -->
        ABNORMAL_FLAG as "abnormalFlag",  <!-- 异常标记 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        START_AREA_TYPE as "startAreaType",  <!-- 起始区域类型 -->
        START_AREA_CODE as "startAreaCode",  <!-- 起始区域类型代码 -->
        START_AREA_NAME as "startAreaName",  <!-- 起始区域类型名称 -->
        END_AREA_TYPE as "endAreaType",  <!-- 终到区域类型 -->
        END_AREA_CODE as "endAreaCode",  <!-- 终到区域类型代码 -->
        END_AREA_NAME as "endAreaName",  <!-- 终到区域类型名称 -->
        (SELECT t2.NET_WEIGHT
        FROM meli.tlids1202 t2
        WHERE t2.SEG_NO = t.SEG_NO
        AND t2.CRANE_RESULT_ID = t.CRANE_RESULT_ID
        AND t2.STATUS = t.STATUS
        AND t2.DEL_FLAG = '0'
        limit 1) AS "craneOperationWeight"
        FROM meli.tlids1201 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_ID = #craneId#
        AND STATUS = '20'
        <isNotEmpty prepend="AND" property="locationId">
            t.END_AREA_CODE LIKE '%$locationId$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime">
            t.REC_CREATE_TIME &gt;= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            t.REC_CREATE_TIME &lt;= #endTime#
        </isNotEmpty>
        ORDER BY REC_CREATE_TIME DESC
    </select>

<!--    更新行车作业实绩的作业清单号-->
    <update id="updateWorkOrder">
        UPDATE meli.tlids1201
        SET
        CRANE_ORDER_ID = #craneOrderId#   <!-- 行车作业清单号 -->
        <isNotEmpty prepend=" , " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        WHERE SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        AND STATUS > '00'
    </update>

    <!--    更新行车作业实绩的异常标记-->
    <update id="updateAbnormalFlag">
        UPDATE meli.tlids1201
        SET
        ABNORMAL_FLAG = #abnormalFlag# <!--异常作业标记-->
        <isNotEmpty prepend=" , " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        WHERE SEG_NO = #segNo#
        AND CRANE_RESULT_ID = #craneResultId#
        AND STATUS > '00'
    </update>


    <!--    根据释放流水号查询行车作业实绩,且捆包号为空-->
    <select id="getLastAbnormal" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1201">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_ORDER_ID as "craneOrderId",  <!-- 行车作业清单号 -->
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        START_X_POSITION as "startXPosition",  <!-- 起始X轴坐标 -->
        START_Y_POSITION as "startYPosition",  <!-- 起始X轴坐标 -->
        START_Z_POSITION as "startZPosition",  <!-- 起始Y轴坐标 -->
        ENDX_POSITION as "endxPosition",  <!-- 终到X轴坐标 -->
        ENDY_POSITION as "endyPosition",  <!-- 终到X轴坐标 -->
        ENDZ_POSITION as "endzPosition",  <!-- 终到Y轴坐标 -->
        INBOUND_SEQUENCE_ID as "inboundSequenceId",  <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID as "outboundSequenceId",  <!-- 释放流水号 -->
        ABNORMAL_FLAG as "abnormalFlag",  <!-- 异常标记 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        (SELECT t2.NET_WEIGHT
        FROM meli.tlids1202 t2
        WHERE t2.SEG_NO = t.SEG_NO
        AND t2.CRANE_RESULT_ID = t.CRANE_RESULT_ID
        AND t2.STATUS = t.STATUS
        AND t2.DEL_FLAG = '0'
        limit 1) AS "craneOperationWeight"
        FROM meli.tlids1201 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_ID = #craneId#
        AND STATUS = '20'
        AND ABNORMAL_FLAG = '1'
        AND EXISTS (
        SELECT 1 FROM meli.tlids1202 tt
        WHERE tt.SEG_NO = t.SEG_NO
        AND tt.CRANE_RESULT_ID = t.CRANE_RESULT_ID
        AND tt.STATUS = t.STATUS
        AND TRIM(IFNULL(tt.PACK_ID,''))) = ''
        ORDER BY REC_CREATE_TIME DESC
    </select>

    <!--    查询行车对应的所有异常实绩-->
    <select id="getAllAbnormalCranePerformance" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_ORDER_ID as "craneOrderId",  <!-- 行车作业清单号 -->
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        START_X_POSITION as "startXPosition",  <!-- 起始X轴坐标 -->
        START_Y_POSITION as "startYPosition",  <!-- 起始X轴坐标 -->
        START_Z_POSITION as "startZPosition",  <!-- 起始Y轴坐标 -->
        ENDX_POSITION as "endxPosition",  <!-- 终到X轴坐标 -->
        ENDY_POSITION as "endyPosition",  <!-- 终到X轴坐标 -->
        ENDZ_POSITION as "endzPosition",  <!-- 终到Y轴坐标 -->
        INBOUND_SEQUENCE_ID as "inboundSequenceId",  <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID as "outboundSequenceId",  <!-- 释放流水号 -->
        ABNORMAL_FLAG as "abnormalFlag",  <!-- 异常标记 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid", <!-- ID -->
        START_AREA_TYPE as "startAreaType",  <!-- 起始区域类型 -->
        START_AREA_CODE as "startAreaCode",  <!-- 起始区域类型代码 -->
        START_AREA_NAME as "startAreaName",  <!-- 起始区域类型名称 -->
        END_AREA_TYPE as "endAreaType",  <!-- 终到区域类型 -->
        END_AREA_CODE as "endAreaCode",  <!-- 终到区域类型代码 -->
        END_AREA_NAME as "endAreaName",  <!-- 终到区域类型名称 -->
        (SELECT t2.NET_WEIGHT
        FROM meli.tlids1202 t2
        WHERE t2.SEG_NO = t.SEG_NO
        AND t2.CRANE_RESULT_ID = t.CRANE_RESULT_ID
        AND t2.STATUS = t.STATUS
        AND t2.DEL_FLAG = '0'
        limit 1) AS "craneOperationWeight"
        FROM meli.tlids1201 t WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_ID = #craneId#
        AND STATUS = '20'
        AND ABNORMAL_FLAG = '1'
        AND EXISTS (
        SELECT 1 FROM meli.tlids1202 tt
        WHERE tt.SEG_NO = t.SEG_NO
        AND tt.CRANE_RESULT_ID = t.CRANE_RESULT_ID
        AND tt.STATUS = t.STATUS
        AND TRIM(IFNULL(tt.PACK_ID,''))) = ''
        <isNotEmpty prepend="AND" property="locationId">
            t.END_AREA_CODE LIKE '%$locationId$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime">
            t.REC_CREATE_TIME &gt;= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            t.REC_CREATE_TIME &lt;= #endTime#
        </isNotEmpty>
        ORDER BY REC_CREATE_TIME DESC
    </select>

    <!--    查询每部行车最后一次行车作业实绩-->
    <select id="getLastOperationPerCrane" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
    SELECT
        t1.CRANE_NAME as "craneName",
        t1.CRANE_ID as "craneId",
        t1.START_X_POSITION as "startXPosition",  <!-- 起始X轴坐标 -->
        t1.START_Y_POSITION as "startYPosition",  <!-- 起始X轴坐标 -->
        t1.START_Z_POSITION as "startZPosition",  <!-- 起始Y轴坐标 -->
        t1.ENDX_POSITION as "endxPosition",  <!-- 终到X轴坐标 -->
        t1.ENDY_POSITION as "endyPosition",  <!-- 终到Y轴坐标 -->
        t1.ENDZ_POSITION as "endzPosition"  <!-- 终到Z轴坐标 -->
    FROM meli.tlids1201 t1
    JOIN (
    SELECT CRANE_NAME, MAX(REC_CREATE_TIME) AS max_time
    FROM meli.tlids1201
        WHERE STATUS='20'
    GROUP BY CRANE_NAME
    ) t2
    ON t1.CRANE_NAME = t2.CRANE_NAME AND t1.REC_CREATE_TIME = t2.max_time
        WHERE STATUS='20'
    </select>

    <select id="queryCranePerformanceByPosition" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1201">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
        CRANE_ORDER_ID as "craneOrderId",  <!-- 行车作业清单号 -->
        CRANE_ID as "craneId",  <!-- 行车编号 -->
        CRANE_NAME as "craneName",  <!-- 行车名称 -->
        START_X_POSITION as "startXPosition",  <!-- 起始X轴坐标 -->
        START_Y_POSITION as "startYPosition",  <!-- 起始X轴坐标 -->
        START_Z_POSITION as "startZPosition",  <!-- 起始Y轴坐标 -->
        ENDX_POSITION as "endxPosition",  <!-- 终到X轴坐标 -->
        ENDY_POSITION as "endyPosition",  <!-- 终到X轴坐标 -->
        ENDZ_POSITION as "endzPosition",  <!-- 终到Y轴坐标 -->
        INBOUND_SEQUENCE_ID as "inboundSequenceId",  <!-- 抓取流水号 -->
        OUTBOUND_SEQUENCE_ID as "outboundSequenceId",  <!-- 释放流水号 -->
        ABNORMAL_FLAG as "abnormalFlag",  <!-- 异常标记 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids1201 t WHERE 1=1
        AND CRANE_ID = #craneId#
        AND SEG_NO = #segNo#
        AND ENDX_POSITION &lt;= #xDestination# + 50
        AND ENDX_POSITION &gt;= #xInitialPoint# - 50
        AND ENDY_POSITION &lt;= #yDestination# + 50
        AND ENDY_POSITION &gt;= #yInitialPoint# - 50
        AND EXISTS (
        SELECT 1 FROM meli.tlids1202 tt
        WHERE tt.SEG_NO = t.SEG_NO
        AND tt.CRANE_RESULT_ID = t.CRANE_RESULT_ID
        AND tt.STATUS = t.STATUS
        AND tt.PACK_ID = #packId#)
    </select>

    <!--    根据时间倒序查询捆包生产实绩时间表,吊运标记未吊运-->
    <select id="queryVgdm1005ByMachineCode" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1005">
        SELECT
        t.RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        t.PACK_ID as "packId",  <!-- 捆包号 -->
        t.OUT_PACK_ID as "outPackId",  <!-- 产出捆包号 -->
        t.PART_ID as "partId",  <!-- 物料号 -->
        t.PACK_TYPE as "packType",  <!-- 捆包类型2成品4余料 -->
        t.UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        t.PROCESS_HOUR as "processHour",  <!-- 加工工时 -->
        t.NET_WEIGHT as "netWeight",  <!-- 净重 -->
        t.QUANTITY as "quantity",  <!-- 数量 -->
        t.UUID as "uuid",  <!-- 唯一编码 -->
        t.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        t.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        t.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        t.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        t.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        t.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        t.TENANT_ID as "tenantId",  <!-- 租户ID -->
        t.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        t.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        t.SEG_NO as "segNo",  <!-- 系统帐套 -->
        t.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        t.SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
        t.STACK_NAME as "stackName",  <!-- 堆垛名称 -->
        t.LIFT_FLAG as "liftFlag",  <!-- 吊运标记 -->
        t.MACHINE_CODE as "machineCode"  <!-- 机组代码 -->
        FROM mevg.tvgdm1005 t
        WHERE t.SEG_NO = #segNo#
        AND t.MACHINE_CODE = #machineCode#
        AND t.STACK_NAME = #stackName#
        AND t.LIFT_FLAG = '0'
        AND (
        (
        (SELECT TRIM(IFNULL(UNITED_PACK_ID, ''))
        FROM mevg.tvgdm1005
        WHERE SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND STACK_NAME = #stackName#
        AND LIFT_FLAG = '0'
        ORDER BY REC_CREATE_TIME DESC
        LIMIT 1) = ''
        AND t.REC_CREATE_TIME = (SELECT MAX(REC_CREATE_TIME)
        FROM mevg.tvgdm1005
        WHERE SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND STACK_NAME = #stackName#
        AND LIFT_FLAG = '0')
        )
        OR
        (
        (SELECT TRIM(IFNULL(UNITED_PACK_ID, ''))
        FROM mevg.tvgdm1005
        WHERE SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND STACK_NAME = #stackName#
        AND LIFT_FLAG = '0'
        ORDER BY REC_CREATE_TIME DESC
        LIMIT 1) &lt;> ''
        AND t.UNITED_PACK_ID = (SELECT UNITED_PACK_ID
        FROM mevg.tvgdm1005
        WHERE SEG_NO = #segNo#
        AND MACHINE_CODE = #machineCode#
        AND STACK_NAME = #stackName#
        AND LIFT_FLAG = '0'
        ORDER BY REC_CREATE_TIME DESC
        LIMIT 1)
        )
        )
        ORDER BY t.REC_CREATE_TIME DESC
    </select>

    <!--修改捆包生产实绩时间表吊运标记    -->
    <update id="updateVgdm1005LiftFlag">
        UPDATE ${mevgSchema}.TVGDM1005
        SET
        LIFT_FLAG = #liftFlag#,   <!-- 吊运标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>
</sqlMap>