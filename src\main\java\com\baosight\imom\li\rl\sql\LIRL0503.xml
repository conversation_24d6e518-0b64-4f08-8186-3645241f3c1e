<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-01-06 10:29:43
   		Version :  1.0
		tableName :meli.tlirl0503 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REM<PERSON>K  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 TENANT_ID  VARCHAR   NOT NULL, 
		 ALLOCATE_VEHICLE_NO  VARCHAR   NOT NULL, 
		 ALLOC_VEHICLE_SEQ  DECIMAL   NOT NULL, 
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 PACK_ID  VARCHAR   NOT NULL, 
		 OUT_PACK_FLAG  VARCHAR   NOT NULL, 
		 WAREHOUSE_CODE  VARCHAR   NOT NULL, 
		 WAREHOUSE_NAME  VARCHAR   NOT NULL, 
		 PUTIN_TYPE  VARCHAR   NOT NULL, 
		 INNER_DIAMETER  DECIMAL   NOT NULL, 
		 PROD_DENSITY  DECIMAL   NOT NULL, 
		 PRODUCT_PROCESS_ID  VARCHAR   NOT NULL, 
		 NET_WEIGHT  DECIMAL   NOT NULL, 
		 CUSTOMER_ID  VARCHAR   NOT NULL, 
		 CUSTOMER_NAME  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0503">
<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusSX">
			STATUS > '00'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocVehicleSeq">
			ALLOC_VEHICLE_SEQ = #allocVehicleSeq#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNumNo">
			VOUCHER_NUM != #voucherNumNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNumLike">
			VOUCHER_NUM LIKE concat('%',#voucherNumLike#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outPackFlag">
			OUT_PACK_FLAG !='1'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="putinType">
			PUTIN_TYPE = #putinType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="innerDiameter">
			INNER_DIAMETER = #innerDiameter#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodDensity">
			PROD_DENSITY = #prodDensity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productProcessId">
			PRODUCT_PROCESS_ID = #productProcessId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="netWeight">
			NET_WEIGHT = #netWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryOrderNum">
			FACTORY_ORDER_NUM = #factoryOrderNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="surfaceGrade">
			SURFACE_GRADE = #surfaceGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specsDesc">
			SPECS_DESC = #specsDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="piceNum">
			PICE_NUM = #piceNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNumFlag">
			VOUCHER_NUM !=' '
		</isNotEmpty>
<!--		<isNotEmpty prepend="AND" property="allocateVehicleNoList">-->
<!--			allocateVehicleNo IN-->
<!--			<iterate property="allocateVehicleNoList" open="(" close=")"-->
<!--					 conjunction=",">-->
<!--				#tagIdList[]#-->
<!--			</iterate>-->
<!--		</isNotEmpty>-->
		<isNotEmpty prepend="and" property="allocateVehicleNoAdd">
			ALLOCATE_VEHICLE_NO IN
			<iterate open="(" close=")" conjunction="," property="allocateVehicleNoAdd">
				#allocateVehicleNoAdd[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="packList">
			PACK_ID IN
			<iterate open="(" close=")" conjunction="," property="packList">
				#packList[]#
			</iterate>
		</isNotEmpty>
</sql>

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0503">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId",  <!-- 租户ID -->
				ALLOCATE_VEHICLE_NO	as "allocateVehicleNo",  <!-- 配车单号 -->
				ALLOC_VEHICLE_SEQ	as "allocVehicleSeq",  <!-- 配车单子项序号 -->
				VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单(卸货:入库计划;装货:提单号) -->
				PACK_ID	as "packId",  <!-- 捆包号 -->
				OUT_PACK_FLAG	as "outPackFlag",  <!-- 自带货标记(0:非自带货;1:自带货) -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
				PUTIN_TYPE	as "putinType",  <!-- 入库类型 -->
				INNER_DIAMETER	as "innerDiameter",  <!-- 内径 -->
				PROD_DENSITY	as "prodDensity",  <!-- 密度 -->
				PRODUCT_PROCESS_ID	as "productProcessId",  <!-- 首道加工工序 -->
				NET_WEIGHT	as "netWeight",  <!-- 重量 -->
				SPECS_DESC as "specsDesc", <!-- 规格 -->
				CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
				CUSTOMER_NAME	as "customerName", <!-- 客户名称 -->
				FACTORY_ORDER_NUM	as "factoryOrderNum",  <!-- 钢厂订单号 -->
				SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
				MAT_INNER_ID	as "matInnerId",  <!-- 材料管理号 -->
				SPECS_DESC	as "specsDesc",  <!-- 规格描述 -->
				TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点代码 -->
				PICE_NUM	as "piceNum",  <!-- 张数 -->
				FACTORY_AREA	as "factoryArea", <!-- 厂区代码 -->
				PROD_TYPE_ID as "prodTypeId", TRADE_CODE as "tradeCode",
		CUST_PART_ID as "custPartId",
		CUST_PART_NAME as "custPartName",
		M_PACK_ID as "mPackId",
		PUR_ORDER_NUM AS "purOrderNum",
		LADING_BILL_REMARK AS "ladingBillRemark",
		LOCATION_NAME as "locationName",
		LOCATION_ID as "locationId",
		tlirl0503.BILLING_METHOD as "billingMethod"
		FROM meli.tlirl0503 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusIn">
			STATUS  in ('20','99')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusD">
			STATUS  in ('20','30')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="noOutPackFlag">
			OUT_PACK_FLAG != '1'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateFormat">
			substr(a.REC_CREATE_TIME,1,8) = substr(replace(#reservationDate#,'-',''),1,8)
		</isNotEmpty>
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>

	</select>


	<select id="query0503" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		SEG_NO	as "segNo",  <!-- 业务单元代码 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		STATUS	as "status",  <!-- 状态 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId",  <!-- 租户ID -->
		ALLOCATE_VEHICLE_NO	as "allocateVehicleNo",  <!-- 配车单号 -->
		ALLOC_VEHICLE_SEQ	as "allocVehicleSeq",  <!-- 配车单子项序号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单(卸货:入库计划;装货:提单号) -->
		PACK_ID	as "packId",  <!-- 捆包号 -->
		OUT_PACK_FLAG	as "outPackFlag",  <!-- 自带货标记(0:非自带货;1:自带货) -->
		WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
		WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
		PUTIN_TYPE	as "putinType",  <!-- 入库类型 -->
		INNER_DIAMETER	as "innerDiameter",  <!-- 内径 -->
		PROD_DENSITY	as "prodDensity",  <!-- 密度 -->
		PRODUCT_PROCESS_ID	as "productProcessId",  <!-- 首道加工工序 -->
		NET_WEIGHT	as "netWeight",  <!-- 重量 -->
		SPECS_DESC as "specsDesc", <!-- 规格 -->
		CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
		CUSTOMER_NAME	as "customerName", <!-- 客户名称 -->
		FACTORY_ORDER_NUM	as "factoryOrderNum",  <!-- 钢厂订单号 -->
		SURFACE_GRADE	as "surfaceGrade",  <!-- 表面等级 -->
		MAT_INNER_ID	as "matInnerId",  <!-- 材料管理号 -->
		SPECS_DESC	as "specsDesc",  <!-- 规格描述 -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点代码 -->
		PICE_NUM	as "piceNum",  <!-- 张数 -->
		FACTORY_AREA	as "factoryArea", <!-- 厂区代码 -->
		PROD_TYPE_ID as "prodTypeId", TRADE_CODE as "tradeCode",
		CUST_PART_ID as "custPartId",
		CUST_PART_NAME as "custPartName",
		M_PACK_ID as "mPackId",
		PUR_ORDER_NUM AS "purOrderNum",
		LADING_BILL_REMARK AS "ladingBillRemark",
		LOCATION_NAME as "locationName",
		LOCATION_ID as "locationId",
		tlirl0503.BILLING_METHOD as "billingMethod",
		FIRST_MACHINE_CODE as "firstMachineCode",
		LOGISTIC_PLAN_TYPE as "logisticPlanType"
		FROM meli.tlirl0503 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusIn">
			STATUS  in ('20','99')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusD">
			STATUS  in ('20','30')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="noOutPackFlag">
			OUT_PACK_FLAG != '1'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateFormat">
			substr(a.REC_CREATE_TIME,1,8) = substr(replace(#reservationDate#,'-',''),1,8)
		</isNotEmpty>
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				UUID asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="queryCount" parameterClass="java.util.HashMap"
			resultClass="int">
		SELECT
		count(1)
		FROM meli.tlirl0503 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusIn">
			STATUS  in ('20','99')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusD">
			STATUS  in ('20','30')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dateFormat">
			substr(a.REC_CREATE_TIME,1,8) = substr(replace(#reservationDate#,'-',''),1,8)
		</isNotEmpty>
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				UUID asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0503 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>

	<select id="queryPackInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select
		tlirl0503.PACK_ID as "packId",
		ifnull(tlirl0503.LOCATION_ID,' ') as "locationId",
		ifnull(tlirl0503.LOCATION_ID,' ') as "imcLocationId"
		from meli.tlirl0502 tlirl0502,
		meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="vehicleNo">
				tlirl0502.VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="voucherNum">
			tlirl0503.VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		and tlirl0502.ALLOC_TYPE = '10'
		and tlirl0502.DEL_FLAG='0'
		and tlirl0503.DEL_FLAG='0'
	</select>


	<select id="queryAllPack" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select PACK_ID as "packId",
		TARGET_HAND_POINT_ID as "targetHandPointId"
		from meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0503.SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="allocateVehicleNoAdd">
			tlirl0503.ALLOCATE_VEHICLE_NO IN
			<iterate open="(" close=")" conjunction="," property="allocateVehicleNoAdd">
				#allocateVehicleNoAdd[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="status">
			tlirl0503.STATUS in ('20','30')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outPackFlag">
			tlirl0503.OUT_PACK_FLAG !='1'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusEq">
			tlirl0503.STATUS ='20'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNumEq">
			tlirl0503.VOUCHER_NUM !=' '
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNoIn">
			tlirl0503.ALLOCATE_VEHICLE_NO =#allocateVehicleNoIn#
		</isNotEmpty>
	</select>

	<select id="queryAllPackNew" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select PACK_ID as "packId",
		TARGET_HAND_POINT_ID as "targetHandPointId"
		from meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0503.SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="allocateVehicleNoAdd">
			tlirl0503.ALLOCATE_VEHICLE_NO IN
			<iterate open="(" close=")" conjunction="," property="allocateVehicleNoAdd">
				#allocateVehicleNoAdd[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="status">
			tlirl0503.STATUS in ('20','30')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outPackFlag">
			tlirl0503.OUT_PACK_FLAG !='1'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusEq">
			tlirl0503.STATUS ='20'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNumEq">
			tlirl0503.VOUCHER_NUM !=' '
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNoIn">
			tlirl0503.ALLOCATE_VEHICLE_NO =#allocateVehicleNoIn#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="nextAlcVehicleNoIn">
			tlirl0503.ALLOCATE_VEHICLE_NO =(select lirl0502.ALLOCATE_VEHICLE_NO
			from meli.tlirl0502 lirl0502
			where 1 = 1
			and lirl0502.SEG_NO = tlirl0503.SEG_NO
			and lirl0502.STATUS = '20'
			and lirl0502.NEXT_ALC_VEHICLE_NO = #nextAlcVehicleNoIn#
			limit 1)
		</isNotEmpty>
	</select>

	<select id="queryAllAllocateVehicleNo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select PACK_ID AS "packId",VOUCHER_NUM as "voucherNum",FACTORY_ORDER_NUM as "factoryOrderNum",SPECS_DESC as "specsDesc",
		LOCATION_ID as "locationId",NET_WEIGHT as "netWeight"
		from meli.tlirl0503
		where 1 = 1
		and SEG_NO = #segNo#
		AND ALLOCATE_VEHICLE_NO =#allocateVehicleNo#
		AND STATUS = '20'
		AND OUT_PACK_FLAG != '1'
		and MAT_INNER_ID != ' '
	</select>


<!--查询装货配单-->
	<select id="queryAllocateVehicleNoInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select
		tlirl0503.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
		tlirl0503.TARGET_HAND_POINT_ID as "targetHandPointId"
		from meli.tlirl0502 tlirl0502,
		meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.SEG_NO = #segNo#
		and tlirl0502.VEHICLE_NO = #vehicleNo#
		<isNotEmpty prepend="and" property="voucherNum">
			tlirl0503.VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		and tlirl0502.ALLOC_TYPE = '10'
		and tlirl0502.DEL_FLAG='0'
		and tlirl0503.DEL_FLAG='0'
		limit 1
	</select>

<!--查询所有配单的装卸点-->
	<select id="queryAllAllocateVehicleNoInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select
		tlirl0503.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
		tlirl0503.TARGET_HAND_POINT_ID as "targetHandPointId",
		(select tlirl0304.HAND_POINT_NAME from meli.tlirl0304 tlirl0304 where 1=1
		and tlirl0304.SEG_NO=tlirl0503.SEG_NO
		and tlirl0304.HAND_POINT_ID=tlirl0503.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS='30') as "handPointName"
		from meli.tlirl0502 tlirl0502,
		meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.SEG_NO = #segNo#
		and tlirl0502.VEHICLE_NO = #vehicleNo#
		<isNotEmpty prepend="and" property="voucherNum">
			tlirl0503.VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		and tlirl0502.ALLOC_TYPE IN ( '10','20')
		and tlirl0502.DEL_FLAG='0'
		and tlirl0503.DEL_FLAG='0'
		and tlirl0503.OUT_PACK_FLAG='0'
		and not exists(select 1
		from meli.tlirl0301 tlirl0301
		where 1 = 1
		and tlirl0301.SEG_NO = tlirl0502.SEG_NO
		and tlirl0301.CAR_TRACE_NO = tlirl0502.CAR_TRACE_NO
		and tlirl0301.VEHICLE_NO = tlirl0502.VEHICLE_NO
		and tlirl0301.CURRENT_HAND_POINT_ID = tlirl0503.TARGET_HAND_POINT_ID)
		order by tlirl0502.ALLOC_TYPE desc ,tlirl0502.REC_CREATE_TIME asc
	</select>

	<!--查询已卸货的捆包明细-->
	<select id="queryAllPackInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select tlirl0502.CAR_TRACE_NO as "carTraceNo",
		tlirl0502.VEHICLE_NO as "vehicleNo",
		tlirl0502.ALLOC_TYPE as "allocType",
		tlirl0502.SEG_NO as "segNo",
		tlirl0503.PACK_ID as "packId"
		from meli.tlirl0502 tlirl0502,
		meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO=tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.CAR_TRACE_NO = #carTraceNo#
		and tlirl0503.PACK_ID = #packId#
		and tlirl0502.DEL_FLAG = '0'
		and tlirl0502.STATUS = '20'
		and tlirl0503.STATUS = '20'
	</select>
	<!--
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocVehicleSeq">
			ALLOC_VEHICLE_SEQ = #allocVehicleSeq#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outPackFlag">
			OUT_PACK_FLAG = #outPackFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="putinType">
			PUTIN_TYPE = #putinType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="innerDiameter">
			INNER_DIAMETER = #innerDiameter#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodDensity">
			PROD_DENSITY = #prodDensity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productProcessId">
			PRODUCT_PROCESS_ID = #productProcessId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="netWeight">
			NET_WEIGHT = #netWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0503 (SEG_NO,  <!-- 业务单元代码 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录创建人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
										ALLOCATE_VEHICLE_NO,  <!-- 配车单号 -->
										ALLOC_VEHICLE_SEQ,  <!-- 配车单子项序号 -->
										VOUCHER_NUM,  <!-- 依据凭单(卸货:入库计划;装货:提单号) -->
										PACK_ID,  <!-- 捆包号 -->
										OUT_PACK_FLAG,  <!-- 自带货标记(0:非自带货;1:自带货) -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										WAREHOUSE_NAME,  <!-- 仓库名称 -->
										PUTIN_TYPE,  <!-- 入库类型 -->
										INNER_DIAMETER,  <!-- 内径 -->
										PROD_DENSITY,  <!-- 密度 -->
										PRODUCT_PROCESS_ID,  <!-- 首道加工工序 -->
										NET_WEIGHT,  <!-- 重量 -->
										CUSTOMER_ID,  <!-- 客户代码 -->
										CUSTOMER_NAME,  <!-- 客户名称 -->
		MAT_INNER_ID,
		SPECS_DESC,
		TARGET_HAND_POINT_ID,
		FACTORY_ORDER_NUM,
		PICE_NUM,
		FACTORY_AREA,
		PROD_TYPE_ID,
		TRADE_CODE,LOCATION_ID,LOCATION_NAME,DELIVERY_TYPE,CUST_PART_ID,
		CUST_PART_NAME,
		M_PACK_ID,
		PUR_ORDER_NUM,
		LADING_BILL_REMARK,
		BILLING_METHOD,FIRST_MACHINE_CODE,LOGISTIC_PLAN_TYPE,LABEL_ID,BATCH_NUM,BATCH_SUB_NUM,PLAN_NET_WEIGHT
		)
	    VALUES (#segNo#, #unitCode#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
		#recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #uuid#, #tenantId#, #allocateVehicleNo#, #allocVehicleSeq#,
		#voucherNum#, #packId#, #outPackFlag#, #warehouseCode#, #warehouseName#, #putinType#, #innerDiameter#,
		#prodDensity#, #productProcessId#, #netWeight#,
		#customerId#, #customerName#
		,#matInnerId#,#specsDesc#,#targetHandPointId#,#factoryOrderNum#,#piceNum#,
		#factoryArea#,#prodTypeId#,#tradeCode#,#locationId#,#locationName#,#deliveryType#,
		#custPartId#,#custPartName#,#mPackId#,#purOrderNum#,#ladingBillRemark#,
		#billingMethod#,#firstMachineCode#,#logisticPlanType#,#labelId#,
		#batchNum#,#batchSubNum#,#planNetWeight#
		)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0503 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0503
		SET
		SEG_NO = #segNo#
		<isNotEmpty prepend=" , " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="allocVehicleSeq">
			ALLOC_VEHICLE_SEQ = #allocVehicleSeq#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="deliveryType">
			DELIVERY_TYPE = #deliveryType#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="outPackFlag">
			OUT_PACK_FLAG = #outPackFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="locationId">
			LOCATION_ID = #locationId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="locationName">
			LOCATION_NAME = #locationName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="putinType">
			PUTIN_TYPE = #putinType#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="innerDiameter">
			INNER_DIAMETER = #innerDiameter#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="prodDensity">
			PROD_DENSITY = #prodDensity#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="productProcessId">
			PRODUCT_PROCESS_ID = #productProcessId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="netWeight">
			NET_WEIGHT = #netWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="factoryOrderNum">
			FACTORY_ORDER_NUM = #factoryOrderNum#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="surfaceGrade">
			SURFACE_GRADE = #surfaceGrade#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="specsDesc">
			SPECS_DESC = #specsDesc#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="targetH,PointId">
			TARGET_H,_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="piceNum">
			PICE_NUM = #piceNum#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="prodTypeId">
			PROD_TYPE_ID = #prodTypeId#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="tradeCode">
			TRADE_CODE = #tradeCode#
		</isNotEmpty>
		WHERE
		UUID = #uuid#
	</update>

	<!--查询已卸货的捆包明细-->
	<select id="queryAllAlocPack" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select tlirl0502.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
		tlirl0502.VEHICLE_NO as "vehicleNo",
		case
		when tlirl0503.OUT_PACK_FLAG = '1' then '1'
		when tlirl0503.STATUS = '20' then '10'
		when tlirl0503.STATUS = '30' then '20' end                                                                                 as "allocType",
		case
		when tlirl0502.ALLOC_TYPE = '10' then '装货'
		when tlirl0502.ALLOC_TYPE = '20'
		then '卸货' end                                                                            as "allocTypeName",
		case
		when tlirl0503.OUT_PACK_FLAG = '1' then '自带货'
		when tlirl0503.STATUS = '20' then '未装卸'
		when tlirl0503.STATUS = '30' then '已装卸' end                                                 as "status",
		tlirl0503.PACK_ID  as "packId",
		tlirl0503.PICE_NUM as "piceNum"
		from meli.tlirl0301 tlirl0301
		,
		meli.tlirl0502 tlirl0502,
		meli.tlirl0503 tlirl0503
		where 1 = 1
		and tlirl0301.SEG_NO=#segNo#
		and tlirl0301.SEG_NO = tlirl0502.SEG_NO
		and tlirl0301.CAR_TRACE_NO = tlirl0502.CAR_TRACE_NO
		and tlirl0301.VEHICLE_NO = tlirl0502.VEHICLE_NO
		and tlirl0301.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO
		and tlirl0301.STATUS in ('20', '30', '40')
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.STATUS = '20'
		and not exists(select 1
		from MELI.tlirl0407 tlirl0407
		where 1 = 1
		and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
		and NEXT_TATGET = '20'
		limit 1)
		order by tlirl0503.ALLOC_VEHICLE_SEQ asc
	</select>

	<update id="updateLocation">
		UPDATE meli.tlirl0503
		SET
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		LOCATION_CODE	= #locationCode#,   <!-- 货位代码 -->
		LOCATION_NAME	= #locationName#  <!-- 货位名称 -->
		WHERE
		UUID = #uuid#
	</update>

	<select id="queryMatchingOrder" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select
		t0502.VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		t0502.ALLOCATE_VEHICLE_NO	as "allocateVehicleNo",  <!-- 配车单号 -->
		t0502.CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		t0503.SEG_NO as "segNo",  <!-- 业务单元代码 -->
		t0503.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
		t0503.STATUS as "status",  <!-- 状态 -->
		t0503.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		t0503.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		t0503.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		t0503.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		t0503.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录创建人姓名 -->
		t0503.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		t0503.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		t0503.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		t0503.REMARK as "remark",  <!-- 备注 -->
		t0503.UUID as "uuid",  <!-- uuid -->
		t0503.TENANT_ID as "tenantId",  <!-- 租户ID -->
		t0503.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",  <!-- 配车单号 -->
		t0503.ALLOC_VEHICLE_SEQ as "allocVehicleSeq",  <!-- 配车单子项序号 -->
		t0503.VOUCHER_NUM as "voucherNum",  <!-- 依据凭单(卸货:入库计划;装货:提单号) -->
		t0503.PACK_ID as "packId",  <!-- 捆包号 -->
		t0503.OUT_PACK_FLAG as "outPackFlag",  <!-- 自带货标记(0:非自带货;1:自带货) -->
		t0503.WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
		t0503.WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
		t0503.PUTIN_TYPE as "putinType",  <!-- 入库类型 -->
		t0503.INNER_DIAMETER as "innerDiameter",  <!-- 内径 -->
		t0503.PROD_DENSITY as "prodDensity",  <!-- 密度 -->
		t0503.PRODUCT_PROCESS_ID as "productProcessId",  <!-- 首道加工工序 -->
		t0503.NET_WEIGHT as "netWeight",  <!-- 重量 -->
		t0503.SPECS_DESC as "specsDesc", <!-- 规格 -->
		t0503.CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
		t0503.CUSTOMER_NAME as "customerName", <!-- 客户名称 -->
		t0503.FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂订单号 -->
		t0503.SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
		t0503.MAT_INNER_ID as "matInnerId",  <!-- 材料管理号 -->
		t0503.SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
		t0503.TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
		t0503.PICE_NUM as "piceNum",  <!-- 张数 -->
		t0503.TRADE_CODE as "tradeCode",  <!-- 贸易方式 -->
		t0503.PROD_TYPE_ID as "prodTypeId",  <!-- 品种 -->
		t0503.FACTORY_AREA as "factoryArea" <!-- 厂区代码 -->
		from meli.tlirl0502 t0502,
		meli.tlirl0503 t0503
		where t0502.SEG_NO = t0503.SEG_NO
		and t0503.SEG_NO = #segNo#
		and t0502.ALLOCATE_VEHICLE_NO = t0503.ALLOCATE_VEHICLE_NO
		and t0502.ALLOC_TYPE = '20'
		and t0503.REC_REVISE_TIME &lt; #currentTime#
		and t0503.REC_REVISE_TIME &gt; #tenMinutesAgoTime#
	</select>

	<!--更改入库计划号-->
	<update id="updateVoucherNum">
		UPDATE meli.tlirl0503
		SET
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		VOUCHER_NUM	= #voucherNum#   <!-- 依据凭单 -->
		WHERE
		UUID = #uuid#
	</update>

	<!-- PDA更改装卸点 -->
	<update id="updateTargetHandPointId">
		UPDATE meli.tlirl0503 t0503
		SET
		t0503.REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		t0503.TARGET_HAND_POINT_ID	= #targetHandPointId#   <!-- 目标装卸点 -->
		WHERE
		t0503.seg_no = #segNo#
		and t0503.TARGET_HAND_POINT_ID	= #oldHandPointId#
		and exists(select 1 from meli.tlirl0502 t0502
		where t0502.SEG_NO = t0503.SEG_NO
		and t0502.ALLOCATE_VEHICLE_NO = t0503.ALLOCATE_VEHICLE_NO
		and t0502.Car_Trace_No = #carTraceNo#)
	</update>

	<!--更新配单子表-->
	<update id="updateStatus">
		UPDATE ${meliSchema}.tlirl0503
		SET
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
		REMARK	= #remark#,  <!-- 备注 -->
		STATUS	= #status#   <!-- 状态 -->
		WHERE 1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO =#allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="allocateVehicleNoAdd">
			ALLOCATE_VEHICLE_NO IN
			<iterate open="(" close=")" conjunction="," property="allocateVehicleNoAdd">
				#allocateVehicleNoAdd[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="allocVehicleSeq">
			ALLOC_VEHICLE_SEQ =#allocVehicleSeq#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="allocateVehicleNoSeqNo">
			ALLOC_VEHICLE_SEQ not IN
			<iterate open="(" close=")" conjunction="," property="allocateVehicleNoSeqNo">
				#allocateVehicleNoSeqNo[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="packIdNo">
			PACK_ID =#packIdNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="packIdNoStr">
			PACK_ID not IN
			<iterate open="(" close=")" conjunction="," property="packIdNoStr">
				#packIdNoStr[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="statusC">
			STATUS =#statusC#
		</isNotEmpty>
	</update>

	<!-- 进厂更新装货配单更改装卸点厂区等信息 -->
	<update id="updateTargetHandPointIdAtEnterFactory">
		UPDATE meli.tlirl0503 t0503
		SET
		t0503.REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		t0503.TARGET_HAND_POINT_ID	= #targetHandPointId#  , <!-- 目标装卸点 -->
		t0503.LOCATION_ID = #locationId#, <!-- 库位代码 -->
		t0503.LOCATION_NAME = #locationName#, <!-- 库位名称 -->
		t0503.WAREHOUSE_CODE = #warehouseCode#, <!-- 仓库代码 -->
		t0503.WAREHOUSE_NAME = #warehouseName# , <!-- 仓库名称 -->
		t0503.FACTORY_AREA = #factoryArea#  <!-- 厂区 -->
		WHERE
		t0503.seg_no = #segNo#
		and t0503.ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		and t0503.ALLOC_VEHICLE_SEQ = #allocVehicleSeq#
	</update>


	<!-- 进厂更新装货配单更改装卸点厂区等信息 -->
	<update id="updateHandPointId">
	UPDATE meli.tlirl0503 t0503
	SET
	t0503.REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
	t0503.TARGET_HAND_POINT_ID	= #targetHandPointId#   <!-- 目标装卸点 -->
	WHERE
	t0503.seg_no = #segNo#
	and t0503.ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
	</update>


	<!-- 进厂更新装货配单更改装卸点厂区等信息 -->
	<update id="updatePackStatus">
		UPDATE meli.tlirl0503 t0503
		SET
		t0503.STATUS = '30'
		<isNotEmpty prepend="," property="locationId">
			t0503.LOCATION_ID = #locationId#
		</isNotEmpty>
		<isNotEmpty prepend="," property="locationName">
			t0503.LOCATION_NAME = #locationName#
		</isNotEmpty>
		<isNotEmpty prepend="," property="damageType">
			t0503.DAMAGE_TYPE = #damageType#
		</isNotEmpty>
		WHERE
		t0503.seg_no = #segNo#
		<isNotEmpty prepend="and" property="allocateVehicleNo">
			t0503.ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
		AND PACK_ID = #packId#
		and STATUS = '20'

	</update>

	<!-- 取消配单 -->
	<update id="deleteByAllocateVehicleNo">
		UPDATE meli.tlirl0503
		SET
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
		REMARK	= #remark#,  <!-- 备注 -->
		STATUS	= #status#   <!-- 状态 -->
		WHERE
		1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
	</update>

	<!-- 取消配单 -->
	<update id="cancelOrderSome">
		UPDATE meli.tlirl0503
		SET
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
		STATUS	= #status#   <!-- 状态 -->
		WHERE
		1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="allocateVehicleNo">
			ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		</isNotEmpty>
	</update>

	<!--查询已卸货的捆包明细-->
	<select id="queryPackIdOne" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select
		SEG_NO,
		UNIT_CODE,
		ALLOCATE_VEHICLE_NO,
		ALLOC_VEHICLE_SEQ,
		STATUS,
		DELIVERY_TYPE,
		VOUCHER_NUM,
		PACK_ID,
		OUT_PACK_FLAG,
		WAREHOUSE_CODE,
		WAREHOUSE_NAME

		from
		meli.tlirl0503
		where
		1=1
		and SEG_NO = #segNo#
		and status = '20'
		and del_flag = '0'
		and pack_id = #packId#
		and VOUCHER_NUM = #voucherNum#
	</select>

	<!--获取未生成计划的捆包-->
	<select id="getPutinPlan" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select tlirl0503.PACK_ID as "packId",tlirl0503.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
		tlirl0503.UUID as "uuid",tlirl0503.ALLOC_VEHICLE_SEQ as "allocVehicleSeq",tlirl0502.VEHICLE_NO as "vehicleNo",tlirl0502.CAR_TRACE_NO AS "carTraceNo"
		from meli.tlirl0502,
		meli.tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.SEG_NO = #segNo#
		AND MAT_INNER_ID = ' '
		AND OUT_PACK_FLAG != '1'
		and tlirl0502.STATUS=tlirl0503.STATUS
		and tlirl0502.STATUS='20'
		and tlirl0502.ALLOC_TYPE = '20'
	</select>

	<select id="getPutinPlanKb" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct tlirl0503.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
		tlirl0502.VEHICLE_NO as "vehicleNo",tlirl0502.CAR_TRACE_NO AS "carTraceNo",
		TIMESTAMPDIFF(MINUTE, STR_TO_DATE((tlirl0502.REC_CREATE_TIME),
		'%Y%m%d%H%i%s'),
		now()) as "waitingTime"
		from meli.tlirl0502,
		meli.tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0502.SEG_NO = #segNo#
		AND MAT_INNER_ID = ' '
		AND OUT_PACK_FLAG != '1'
		and tlirl0502.STATUS=tlirl0503.STATUS
		and tlirl0502.STATUS='20'
		and tlirl0502.CAR_TRACE_NO !=' '
		and tlirl0502.ALLOC_TYPE='20'
		and tlirl0502.NO_PLAN_FLAG is null
	</select>


	<select id="queryBillMethod" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT distinct tlirl0503.SEG_NO              as "segNo",
		tlirl0503.UNIT_CODE           as "unitCode",
		tlirl0503.STATUS              as "status",
		tlirl0503.REC_CREATOR         as "recCreator",
		tlirl0503.REC_CREATOR_NAME    as "recCreatorName",
		tlirl0503.REC_CREATE_TIME     as "recCreateTime",
		tlirl0503.REC_REVISOR         as "recRevisor",
		tlirl0503.REC_REVISOR_NAME    as "recRevisorName",
		tlirl0503.REC_REVISE_TIME     as "recReviseTime",
		tlirl0503.ARCHIVE_FLAG        as "archiveFlag",
		tlirl0503.DEL_FLAG            as "delFlag",
		tlirl0503.REMARK              as "remark",
		tlirl0503.UUID                as "uuid",
		tlirl0503.TENANT_ID           as "tenantId",
		tlirl0503.ALLOCATE_VEHICLE_NO as "allocateVehicleNo",
		tlirl0503.CUSTOMER_ID         AS "customerId",
		tlirl0503.CUSTOMER_NAME       AS "customerName",
		tlirl0503.VOUCHER_NUM as "voucherNum",
		tlirl0502.CAR_TRACE_NO as "carTraceNo",
		tlirl0502.VEHICLE_NO as "vehicleNo",
		tlirl0503.BILLING_METHOD as "billingMethod"
		FROM MELI.tlirl0503,
		meli.tlirl0502
		WHERE 1 = 1
		and tlirl0502.SEG_NO=tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO=tlirl0503.ALLOCATE_VEHICLE_NO
		AND tlirl0503.SEG_NO = #segNo#
		AND tlirl0503.STATUS = '20'
		and BILLING_METHOD = '20'
		and PACK_ID = ''
		ORDER BY tlirl0503.REC_CREATE_TIME desc
	</select>

	<update id="updateBillStatus">
		UPDATE MELI.tlirl0503
		SET STATUS='00',
		DEL_FLAG = '1'
		WHERE SEG_NO = #segNo#
		AND VOUCHER_NUM = #voucherNum#
		and ALLOCATE_VEHICLE_NO=#allocateVehicleNo#
		AND PACK_ID = ''
	</update>


	<!--根据配车单号查询明细数据，配车单号必填-->
	<select id="queryByAllocateVehicleNo" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0503">
		SELECT
		SEG_NO as "segNo",  <!-- 业务单元代码 -->
		UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
		(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		STATUS as "status",  <!-- 状态 -->
		REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME as "recRevisorName",  <!-- 记录创建人姓名 -->
		REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		REMARK as "remark",  <!-- 备注 -->
		UUID as "uuid",  <!-- uuid -->
		TENANT_ID as "tenantId",  <!-- 租户ID -->
		ALLOCATE_VEHICLE_NO as "allocateVehicleNo",  <!-- 配车单号 -->
		ALLOC_VEHICLE_SEQ as "allocVehicleSeq",  <!-- 配车单子项序号 -->
		VOUCHER_NUM as "voucherNum",  <!-- 依据凭单(卸货:入库计划;装货:提单号) -->
		PACK_ID as "packId",  <!-- 捆包号 -->
		OUT_PACK_FLAG as "outPackFlag",  <!-- 自带货标记(0:非自带货;1:自带货) -->
		WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
		WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
		PUTIN_TYPE as "putinType",  <!-- 入库类型 -->
		INNER_DIAMETER as "innerDiameter",  <!-- 内径 -->
		PROD_DENSITY as "prodDensity",  <!-- 密度 -->
		PRODUCT_PROCESS_ID as "productProcessId",  <!-- 首道加工工序 -->
		NET_WEIGHT as "netWeight",  <!-- 重量 -->
		SPECS_DESC as "specsDesc", <!-- 规格 -->
		CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
		CUSTOMER_NAME as "customerName", <!-- 客户名称 -->
		FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂订单号 -->
		SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
		MAT_INNER_ID as "matInnerId",  <!-- 材料管理号 -->
		SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
		TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
		(SELECT HAND_POINT_NAME
		FROM MELI.tlirl0304 tlirl0304
		WHERE tlirl0304.SEG_NO = t.SEG_NO
		AND tlirl0304.HAND_POINT_ID = t.TARGET_HAND_POINT_ID
		AND tlirl0304.STATUS = '30' LIMIT 1) AS "targetHandPointName",
		PICE_NUM as "piceNum",  <!-- 张数 -->
		FACTORY_AREA as "factoryArea", <!-- 厂区代码 -->
		PROD_TYPE_ID as "prodTypeId", TRADE_CODE as "tradeCode",
		CUST_PART_ID as "custPartId",
		CUST_PART_NAME as "custPartName",
		M_PACK_ID as "mPackId",
		PUR_ORDER_NUM AS "purOrderNum",
		LADING_BILL_REMARK AS "ladingBillRemark",
		LOCATION_NAME as "locationName",
		LOCATION_ID as "locationId",
		BILLING_METHOD as "billingMethod", <!--提单类型-->
		FIRST_MACHINE_CODE as "firstMachineCode"
		FROM meli.tlirl0503 t WHERE
		SEG_NO = #segNo#
		AND ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		<isNotEmpty prepend=" AND " property="status">
			<isEqual property="status" compareValue="00">
				STATUS = '00'
				AND DEL_FLAG = 1
			</isEqual>
			<isNotEqual property="status" compareValue="00">
				<isNotEqual property="status" compareValue="99">
					STATUS IN ('20','30','99')
					AND DEL_FLAG = 0
				</isNotEqual>
			</isNotEqual>
		</isNotEmpty>
		<isEmpty prepend=" AND " property="status">
			STATUS IN ('20','30','99')
			AND DEL_FLAG = 0
		</isEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME DESC
			</isEmpty>
		</dynamic>
	</select>

	<!--根据配车单号条数统计	-->
	<select id="countByAllocateVehicleNo" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0503 WHERE
		SEG_NO = #segNo#
		AND ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		<isNotEmpty prepend=" AND " property="status">
			<isEqual property="status" compareValue="00">
				STATUS = '00'
				AND DEL_FLAG = 1
			</isEqual>
			<isNotEqual property="status" compareValue="00">
				<isNotEqual property="status" compareValue="99">
					STATUS IN ('20','30','99')
					AND DEL_FLAG = 0
				</isNotEqual>
			</isNotEqual>
		</isNotEmpty>
		<isEmpty prepend=" AND " property="status">
			STATUS IN ('20','30','99')
			AND DEL_FLAG = 0
		</isEmpty>
	</select>

	<!--查询目前系统中所有状态不为完成的普通提单的配车单捆包-->
	<select id="queryByStatusNotComplete" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0503">
		SELECT
		SEG_NO as "segNo",  <!-- 业务单元代码 -->
		UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
		(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		STATUS as "status",  <!-- 状态 -->
		REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME as "recRevisorName",  <!-- 记录创建人姓名 -->
		REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		REMARK as "remark",  <!-- 备注 -->
		UUID as "uuid",  <!-- uuid -->
		TENANT_ID as "tenantId",  <!-- 租户ID -->
		ALLOCATE_VEHICLE_NO as "allocateVehicleNo",  <!-- 配车单号 -->
		ALLOC_VEHICLE_SEQ as "allocVehicleSeq",  <!-- 配车单子项序号 -->
		VOUCHER_NUM as "voucherNum",  <!-- 依据凭单(卸货:入库计划;装货:提单号) -->
		PACK_ID as "packId",  <!-- 捆包号 -->
		OUT_PACK_FLAG as "outPackFlag",  <!-- 自带货标记(0:非自带货;1:自带货) -->
		WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
		WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
		PUTIN_TYPE as "putinType",  <!-- 入库类型 -->
		INNER_DIAMETER as "innerDiameter",  <!-- 内径 -->
		PROD_DENSITY as "prodDensity",  <!-- 密度 -->
		PRODUCT_PROCESS_ID as "productProcessId",  <!-- 首道加工工序 -->
		NET_WEIGHT as "netWeight",  <!-- 重量 -->
		SPECS_DESC as "specsDesc", <!-- 规格 -->
		CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
		CUSTOMER_NAME as "customerName", <!-- 客户名称 -->
		FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂订单号 -->
		SURFACE_GRADE as "surfaceGrade",  <!-- 表面等级 -->
		MAT_INNER_ID as "matInnerId",  <!-- 材料管理号 -->
		SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
		TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
		PICE_NUM as "piceNum",  <!-- 张数 -->
		FACTORY_AREA as "factoryArea", <!-- 厂区代码 -->
		PROD_TYPE_ID as "prodTypeId", TRADE_CODE as "tradeCode",
		CUST_PART_ID as "custPartId",
		CUST_PART_NAME as "custPartName",
		M_PACK_ID as "mPackId",
		PUR_ORDER_NUM AS "purOrderNum",
		LADING_BILL_REMARK AS "ladingBillRemark",
		LOCATION_NAME as "locationName",
		LOCATION_ID as "locationId",
		BILLING_METHOD as "billingMethod" <!--提单类型-->
		FROM meli.tlirl0503 t WHERE
		SEG_NO = #segNo#
		<!--类型为普通提单
		AND BILLING_METHOD = '10'-->
		<!--当捆包号不为空时,就去判断捆包状态-->
		AND COALESCE(PACK_ID, '') &lt;> ''
		<!--不为撤销与完成状态-->
		AND STATUS NOT IN ('00','99')
		AND DEL_FLAG = 0
		and exists(select 1
		from meli.tlirl0502
		where 1 = 1
		and tlirl0502.SEG_NO = t.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = t.ALLOCATE_VEHICLE_NO
		and tlirl0502.STATUS = t.STATUS
		and tlirl0502.ALLOC_TYPE = '10')
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME DESC
			</isEmpty>
		</dynamic>
	</select>

	<!--更新配车单状态不为完成的明细数据,更新状态为完成，拼接备注-->
	<update id="updateCompleteByAllocateVehicleNo">
		UPDATE meli.tlirl0503
		SET
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
		REMARK = CONCAT(REMARK,';',#remark#),  <!-- 备注 -->
		STATUS = #status#   <!-- 状态 -->
		WHERE SEG_NO = #segNo#
		AND ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		<isNotEmpty prepend="AND" property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="matInnerId">
			MAT_INNER_ID = #matInnerId#
		</isNotEmpty>
		AND STATUS NOT IN ('00','99')
		AND DEL_FLAG = 0
	</update>

<!--	查询最大的配单子项号-->
	<select id="queryMaxallocVehicleS" resultClass="String">
		select max(ALLOC_VEHICLE_SEQ) as "maxSeq"
		from meli.tlirl0503
		where 1 = 1
		and SEG_NO =#segNo#
		AND ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
	</select>

	<select id="querySumWeight" resultClass="String">
		select sum(PLAN_NET_WEIGHT) as "netWeight"
		from meli.tlirl0503
		where 1 = 1
		and SEG_NO =#segNo#
		AND VOUCHER_NUM = #voucherNum#
		<isNotEmpty prepend="and" property="purOrderNum">
			PUR_ORDER_NUM  IN
			<iterate open="(" close=")" conjunction="," property="purOrderNum">
				#purOrderNum[]#
			</iterate>
		</isNotEmpty>
		AND ALLOCATE_VEHICLE_NO = #allocateVehicleNo#
		and PACK_ID =''
		and STATUS ='00'
	</select>

	<!-- 查询已使用的捆包记录 -->
	<select id="queryUsedPacks" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			SEG_NO as "segNo",
			VOUCHER_NUM as "voucherNum",
			PACK_ID as "packId",
			STATUS as "status",
			DEL_FLAG as "delFlag"
		FROM meli.tlirl0503
		WHERE 1 = 1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="billId">
			BATCH_NUM = #billId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ladingBillId">
			VOUCHER_NUM = #ladingBillId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<!-- 只查询有效的记录（未删除且状态不为00） -->
		AND DEL_FLAG = '0'
		AND STATUS != '00'
		<!-- 排除自带货 -->
		AND OUT_PACK_FLAG != '1'
	</select>

	<!-- 查询库位统计信息 -->
	<select id="queryLocationStatistics" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			SEG_NO as "segNo",
			LOCATION_ID as "locationId",
			LOCATION_NAME as "locationName",
			WAREHOUSE_CODE as "warehouseCode",
			WAREHOUSE_NAME as "warehouseName",
			NET_WEIGHT as "netWeight",
			STATUS as "status",
			DEL_FLAG as "delFlag",
			PACK_ID as "packId",
			CUSTOMER_ID as "customerId",
			CUSTOMER_NAME as "customerName",
			MAT_INNER_ID as "matInnerId",
			SPECS_DESC as "specsDesc"
		FROM meli.tlirl0503
		WHERE 1 = 1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaCode">
			LOCATION_ID LIKE concat(#areaCode#, '%')
		</isNotEmpty>
		<!-- 只查询有效的记录（未删除） -->
		AND DEL_FLAG = '0'
		<!-- 排除自带货 -->
		AND OUT_PACK_FLAG != '1'
		<!-- 按库位ID排序 -->
		ORDER BY LOCATION_ID ASC
	</select>
</sqlMap>