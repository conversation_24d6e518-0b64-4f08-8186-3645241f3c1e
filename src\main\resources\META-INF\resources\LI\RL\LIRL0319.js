$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    // TODO 查询 按钮事件
    $("#QUERY").on("click", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        var unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({msg: "请先选择业务单元代码!"}, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });

    $("#SUB_QUERY").on("click", function (e) {
        sub_resultGrid.dataSource.page(1);
    });

    $("#SUB_QUERY1").on("click", function (e) {
        sub_result1Grid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            pageable: {
                pageSize: 10,
                pageSizes:  [10,20,50,100,200,500,1000]
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "customerId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "承运商/客户代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "ADDSUBWINDOW",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "承运商/客户代码"
                            })
                        }
                    }
                },
                {
                    field: "siteName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "站点名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "ADDSUBWINDOW1",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "站点名称"
                            })
                        }
                    }
                },
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // 定义BizModel
                BizModel = kendo.data.Model.define({
                    id: grid.dataSource.options.schema.model.id,
                    fields: grid.dataSource.options.schema.model.fields
                });
                /*粘粘版导入*/
                $("#CLIPBOARD").on("click", function (e) {
                    var content = "";
                    if (IPLAT.Browser.isIE) {
                        content = window.clipboardData.getData("Text");
                        postHandle(content);
                    } else {
                        clipWindow.center().open();
                        handleFun = postHandle;
                    }
                });
                // 获取勾选数据，
                $("#INSERTSAVEM").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0319", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0319", "confirm", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0319", "confirmNo", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });

                /**
                 * 删除
                 */
                $("#DELETEN").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0319", "delete", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            afterEdit: function (e) {
                if (e.field === "reservationIdentity") {
                    if (e.model.reservationIdentity == null){
                        e.model.customerId = ' '
                        e.model.customerName = ' '
                    }
                }
            }
        },
        "sub_result":{
            onRowDblClick: function (e) {
                /*$("#inqu_status-0-userNum").val(e.model.userNum)
                $("#inqu_status-0-userName").val(e.model.chineseUserName)*/
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    varModel.customerId = e.model.userNum;
                    varModel.customerName = e.model.chineseUserName;
                }
                resultGrid.refresh();
                //关闭弹出框
                ADDSUBWINDOWWindow.close();
            }
        },
        "sub_result1":{
            onRowDblClick: function (e) {
                /*$("#inqu_status-0-userNum").val(e.model.userNum)
                $("#inqu_status-0-userName").val(e.model.chineseUserName)*/
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    varModel.siteName = e.model.siteName;
                }
                resultGrid.refresh();
                //关闭弹出框
                ADDSUBWINDOW1Window.close();
            }
        },
    };

    $("#reservationIdentity").on("blur", function (e) {
        //回填到表格中
        var id = e.currentTarget.id;
        var value = e.currentTarget.value;
        if (value==' '){
            resultGrid.setCellValue(row1, "customerId", ' ')
            resultGrid.setCellValue(row1, "customerName", ' ')
        }
    });

    $("#siteType").on("blur", function (e) {
        //回填到表格中
        var id = e.currentTarget.id;
        var value = e.currentTarget.value;
        if (value==' '){
            resultGrid.setCellValue(row1, "siteName", ' ')
        }
    });

    IPLATUI.EFWindow = {
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "unitInfo01": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo01");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = resultGrid.getCheckedRows();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = resultGrid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "ADDSUBWINDOW": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_resultGrid.getDataItems().length > 0) {
                    sub_resultGrid.removeRows(sub_resultGrid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    // let reservationIdentity = varModel.reservationIdentity;
                    // if (IPLAT.isBlankString(reservationIdentity)){
                    //     NotificationUtil({msg:"预约身份(承运商/客户)！"}, "error");
                    //     return;
                    // }
                    IPLAT.EFSelect.value($("#sub_query_status-0-reservationIdentity")," ");
                    $("#sub_query_status-0-unitCode").val(varModel.unitCode);
                    $("#sub_query_status-0-segNo").val(varModel.segNo);
                }
            }
        },
        "ADDSUBWINDOW1": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result1Grid.getDataItems().length > 0) {
                    sub_result1Grid.removeRows(sub_result1Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let siteType = varModel.siteType;
                    if (IPLAT.isBlankString(siteType)){
                        NotificationUtil({msg:"请选择站点类型！"}, "error");
                        return;
                    }
                    IPLAT.EFSelect.value($("#sub_query1_status-0-siteType"),siteType);
                    IPLAT.EFSelect.value($("#sub_query1_status-0-status"),"20");
                    $("#sub_query1_status-0-unitCode").val(varModel.unitCode);
                    $("#sub_query1_status-0-segNo").val(varModel.segNo);
                }
            }
        },
    }


    var BizModel;

    /**
     * 处理粘贴的内容并添加到表格中
     * @param {string} content - 粘贴的内容
     * @returns {void}
     */
    function postHandle(content) {
        if (!content?.trim()) {
            NotificationUtil("粘贴内容不能为空", "warning");
            return;
        }
        try {
            const dataItems = resultGrid.getDataItems();
            if (dataItems?.length) {
                resultGrid.unCheckAllRows();
            }
            const lines = content.split("\r\n").filter((line) => line.trim());
            const validRows = lines.map((line) => getRow(line)).filter((row) => row !== false);
            if (validRows.length > 0) {
                resultGrid.addRows(validRows, false, true);
                // NotificationUtil(`成功导入 ${validRows.length} 条数据`, "success");
            }
        } catch (error) {
            // console.error("处理粘贴内容时出错:", error);
            NotificationUtil("处理粘贴内容时出错", "error");
        }
    }

    /**
     * 映射表
     */
    const FIELD_MAPPINGS = {
        siteType: {
            index: 4,
            label: "站点类型",
            values: new Map([
                ["起始地", "10"],
                ["目的地", "20"]
            ])
        },
    };

    /**
     * 解析粘贴的行数据并创建新的数据行对象
     * @param {string} line 粘贴的单行数据
     * @returns {Object|boolean} 返回创建的数据行对象,或在数据无效时返回false
     */
    function getRow(line) {
        if (!line?.trim()) {
            return false;
        }
        const columns = line.split("\t");
        if (!columns[0]?.trim()) {
            return false;
        }
        try {
            const row = {
                unitCode: columns[0],
                segName: columns[1],
                segNo: columns[0],
                uuid: "",
                status: "",
                siteName: columns[5],
                sequence: columns[6],
                recCreator: "",
                recCreatorName: "",
                recCreateTime: "",
                recRevisor: "",
                recRevisorName: "",
                recReviseTime: "",
                tenantUser: "",
                delFlag: 0
            };
            // 验证和转换映射字段
            for (const [field, config] of Object.entries(FIELD_MAPPINGS)) {
                const inputValue = columns[config.index]?.trim();
                const mappedValue = config.values.get(inputValue);

                if (!mappedValue) {
                    throw new Error(`${config.label}值 "${inputValue}" 不存在！请检查`);
                }
                row[field] = mappedValue;
            }
            const modelInstance = new BizModel(row);
            modelInstance.dirty = true;
            return modelInstance;
        } catch (error) {
            NotificationUtil(`处理行数据失败: ${error.message}`, "error");
            return false;
        }
    }

    // 优化粘贴事件处理
    document.addEventListener("paste", function (evt) {
        if (evt.target.id !== "clipContent") {
            return;
        }
        evt.preventDefault();
        const clipdata = evt.clipboardData || window.clipboardData;
        const content = clipdata.getData("text/plain");
        clipWindow.close();
        handleFun?.(content);
    });

});
