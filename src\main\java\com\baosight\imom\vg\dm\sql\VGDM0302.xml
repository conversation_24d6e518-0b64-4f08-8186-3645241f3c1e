<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0302">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="scadaId">
            SCADA_ID = #scadaId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="scadaName">
            SCADA_NAME = #scadaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="scadaDesp">
            SCADA_DESP like concat('%',#scadaDesp#,'%')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0302">
        SELECT
        SCADA_ID as "scadaId",  <!-- 节点ID -->
        SCADA_NAME as "scadaName",  <!-- 节点名 -->
        SCADA_DESP as "scadaDesp",  <!-- 描述 -->
        SCADA_PRIMARY_IP as "scadaPrimaryIp",  <!-- 主节点ip -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0302 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SCADA_ID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0302 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0302 (SCADA_ID,  <!-- 节点ID -->
        SCADA_NAME,  <!-- 节点名 -->
        SCADA_DESP,  <!-- 描述 -->
        SCADA_PRIMARY_IP,  <!-- 主节点ip -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#scadaId#, #scadaName#, #scadaDesp#, #scadaPrimaryIp#, #uuid#, #recCreator#, #recCreatorName#,
        #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#,
        #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0302 WHERE
        SEG_NO = #segNo#
        AND UNIT_CODE = #unitCode#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0302
        SET
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        SEG_NO = #segNo#
        AND UNIT_CODE = #unitCode#
    </update>

</sqlMap>