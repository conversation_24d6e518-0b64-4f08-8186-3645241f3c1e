/**
 * Generate time : 2024-08-16 9:33:35
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0109 预约通知维护表
 *
 */
public class LIRL0109 extends DaoEPBase {
    public static final String QUERY = "LIRL0109.query";
    public static final String QUERY_TITLE = "LIRL0109.queryTitle";
    public static final String QUERY_ALL = "LIRL0109.queryAll";
    public static final String COUNT = "LIRL0109.count";
    public static final String INSERT = "LIRL0109.insert";
    public static final String UPDATE = "LIRL0109.update";
    public static final String DELETE = "LIRL0109.delete";

    private String segNo = " ";        /* 业务单元代代码*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String status = " ";        /* 状态(撤销：00、新增：10)*/
    private String notificationTitle = " ";        /* 通知标题*/
    private String notificationText = " ";        /* 通知正文*/
    private String startOfValidity = " ";        /* 有效期起始*/
    private String expirationOfValidity = " ";        /* 有效期截止*/
    private String countdownDuration = " ";        /* 倒计时时长(秒)*/
    private String signatureMarking = " ";		/* 一体机签章标记*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/

    private String bold = " "; // 需要加粗的文字
    private String addRed = " "; // 需要加红的文字
    private String addUnderline = " "; // 需要加下划线的文字

    /**
     * the constructor
     */
    public LIRL0109() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("notificationTitle");
        eiColumn.setDescName("通知标题");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("notificationText");
        eiColumn.setDescName("通知正文");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startOfValidity");
        eiColumn.setDescName("有效期起始");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("expirationOfValidity");
        eiColumn.setDescName("有效期截止");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("countdownDuration");
        eiColumn.setDescName("倒计时时长(秒)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("signatureMarking");
        eiColumn.setDescName("一体机签章标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bold");
        eiColumn.setDescName("加粗");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("addRed");
        eiColumn.setDescName("加红");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("addUnderline");
        eiColumn.setDescName("加下划线");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * get the segNo - 业务单元代代码
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 业务单元代代码
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the segName - 业务单元简称
     *
     * @return the segName
     */
    public String getSegName() {
        return this.segName;
    }

    /**
     * set the segName - 业务单元简称
     */
    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the unitCode - 业务单元代代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the status - 状态(撤销：00、新增：10)
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态(撤销：00、新增：10)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the notificationTitle - 通知标题
     * @return the notificationTitle
     */
    public String getNotificationTitle() {
        return this.notificationTitle;
    }

    /**
     * set the notificationTitle - 通知标题
     */
    public void setNotificationTitle(String notificationTitle) {
        this.notificationTitle = notificationTitle;
    }

    /**
     * get the notificationText - 通知正文
     * @return the notificationText
     */
    public String getNotificationText() {
        return this.notificationText;
    }

    /**
     * set the notificationText - 通知正文
     */
    public void setNotificationText(String notificationText) {
        this.notificationText = notificationText;
    }

    /**
     * get the startOfValidity - 有效期起始
     * @return the startOfValidity
     */
    public String getStartOfValidity() {
        return this.startOfValidity;
    }

    /**
     * set the startOfValidity - 有效期起始
     */
    public void setStartOfValidity(String startOfValidity) {
        this.startOfValidity = startOfValidity;
    }

    /**
     * get the expirationOfValidity - 有效期截止
     * @return the expirationOfValidity
     */
    public String getExpirationOfValidity() {
        return this.expirationOfValidity;
    }

    /**
     * set the expirationOfValidity - 有效期截止
     */
    public void setExpirationOfValidity(String expirationOfValidity) {
        this.expirationOfValidity = expirationOfValidity;
    }

    /**
     * get the countdownDuration - 倒计时时长(秒)
     * @return the countdownDuration
     */
    public String getCountdownDuration() {
        return this.countdownDuration;
    }

    /**
     * set the countdownDuration - 倒计时时长(秒)
     */
    public void setCountdownDuration(String countdownDuration) {
        this.countdownDuration = countdownDuration;
    }
    /**
     * get the signatureMarking - 一体机签章标记
     * @return the signatureMarking
     */
    public String getSignatureMarking() {
        return this.signatureMarking;
    }

    /**
     * set the signatureMarking - 一体机签章标记
     */
    public void setSignatureMarking(String signatureMarking) {
        this.signatureMarking = signatureMarking;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the bold - 加粗
     * @return the bold
     */
    public String getBold() {
        return this.bold;
    }

    /**
     * set the bold - 加粗
     */
    public void setBold(String bold) {
        this.bold = bold;
    }

    /**
     * get the addRed - 加红
     * @return the addRed
     */
    public String getAddRed() {
        return this.addRed;
    }

    /**
     * set the addRed - 加红
     */
    public void setAddRed(String addRed) {
        this.addRed = addRed;
    }

    /**
     * get the addUnderline - 加下划线
     * @return the addUnderline
     */
    public String getAddUnderline() {
        return this.addUnderline;
    }

    /**
     * set the addUnderline - 备注
     */
    public void setAddUnderline(String addUnderline) {
        this.addUnderline = addUnderline;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setNotificationTitle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("notificationTitle")), notificationTitle));
        setNotificationText(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("notificationText")), notificationText));
        setStartOfValidity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startOfValidity")), startOfValidity));
        setExpirationOfValidity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("expirationOfValidity")), expirationOfValidity));
        setCountdownDuration(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("countdownDuration")), countdownDuration));
        setSignatureMarking(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("signatureMarking")), signatureMarking));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));

        setBold(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bold")), bold));
        setAddRed(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("addRed")), addRed));
        setAddUnderline(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("addUnderline")), addUnderline));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("notificationTitle", StringUtils.toString(notificationTitle, eiMetadata.getMeta("notificationTitle")));
        map.put("notificationText", StringUtils.toString(notificationText, eiMetadata.getMeta("notificationText")));
        map.put("startOfValidity", StringUtils.toString(startOfValidity, eiMetadata.getMeta("startOfValidity")));
        map.put("expirationOfValidity", StringUtils.toString(expirationOfValidity, eiMetadata.getMeta("expirationOfValidity")));
        map.put("countdownDuration", StringUtils.toString(countdownDuration, eiMetadata.getMeta("countdownDuration")));
        map.put("signatureMarking",StringUtils.toString(signatureMarking, eiMetadata.getMeta("signatureMarking")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));

        map.put("bold", StringUtils.toString(bold, eiMetadata.getMeta("bold")));
        map.put("addRed", StringUtils.toString(addRed, eiMetadata.getMeta("addRed")));
        map.put("addUnderline", StringUtils.toString(addUnderline, eiMetadata.getMeta("addUnderline")));

        return map;

    }
}