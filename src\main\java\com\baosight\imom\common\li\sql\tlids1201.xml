<?xml version="1.0" encoding="UTF-8"?>
<!D<PERSON><PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-05 13:10:12
   		Version :  1.0
		tableName :meli.tlids1201 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CRANE_RESULT_ID  VARCHAR   NOT NULL, 
		 CRANE_ORDER_ID  VARCHAR, 
		 CRANE_ID  VARCHAR   NOT NULL, 
		 CRANE_NAME  VARCHAR   NOT NULL, 
		 START_X_POSITION  VARCHAR, 
		 START_Y_POSITION  VARCHAR, 
		 START_Z_POSITION  VARCHAR, 
		 ENDX_POSITION  VARCHAR, 
		 ENDY_POSITION  VARCHAR, 
		 ENDZ_POSITION  VARCHAR, 
		 INBOUND_SEQUENCE_ID  VARCHAR, 
		 OUTBOUND_SEQUENCE_ID  VARCHAR, 
		 ABNORMA<PERSON>_FLAG  VARCHAR, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL   primarykey
	-->
<sqlMap namespace="tlids1201">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids1201">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				CRANE_RESULT_ID	as "craneResultId",  <!-- 行车实绩单号 -->
				CRANE_ORDER_ID	as "craneOrderId",  <!-- 行车作业清单号 -->
				CRANE_ID	as "craneId",  <!-- 行车编号 -->
				CRANE_NAME	as "craneName",  <!-- 行车名称 -->
				START_X_POSITION	as "startXPosition",  <!-- 起始X轴坐标 -->
				START_Y_POSITION	as "startYPosition",  <!-- 起始X轴坐标 -->
				START_Z_POSITION	as "startZPosition",  <!-- 起始Y轴坐标 -->
				ENDX_POSITION	as "endxPosition",  <!-- 终到X轴坐标 -->
				ENDY_POSITION	as "endyPosition",  <!-- 终到X轴坐标 -->
				ENDZ_POSITION	as "endzPosition",  <!-- 终到Y轴坐标 -->
				INBOUND_SEQUENCE_ID	as "inboundSequenceId",  <!-- 抓取流水号 -->
				OUTBOUND_SEQUENCE_ID	as "outboundSequenceId",  <!-- 释放流水号 -->
				ABNORMAL_FLAG	as "abnormalFlag",  <!-- 异常标记 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids1201 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids1201 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneResultId">
			CRANE_RESULT_ID = #craneResultId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneOrderId">
			CRANE_ORDER_ID = #craneOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startXPosition">
			START_X_POSITION = #startXPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startYPosition">
			START_Y_POSITION = #startYPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startZPosition">
			START_Z_POSITION = #startZPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endxPosition">
			ENDX_POSITION = #endxPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endyPosition">
			ENDY_POSITION = #endyPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endzPosition">
			ENDZ_POSITION = #endzPosition#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="inboundSequenceId">
			INBOUND_SEQUENCE_ID = #inboundSequenceId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outboundSequenceId">
			OUTBOUND_SEQUENCE_ID = #outboundSequenceId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="abnormalFlag">
			ABNORMAL_FLAG = #abnormalFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids1201 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
										CRANE_ORDER_ID,  <!-- 行车作业清单号 -->
										CRANE_ID,  <!-- 行车编号 -->
										CRANE_NAME,  <!-- 行车名称 -->
										START_X_POSITION,  <!-- 起始X轴坐标 -->
										START_Y_POSITION,  <!-- 起始X轴坐标 -->
										START_Z_POSITION,  <!-- 起始Y轴坐标 -->
										ENDX_POSITION,  <!-- 终到X轴坐标 -->
										ENDY_POSITION,  <!-- 终到X轴坐标 -->
										ENDZ_POSITION,  <!-- 终到Y轴坐标 -->
										INBOUND_SEQUENCE_ID,  <!-- 抓取流水号 -->
										OUTBOUND_SEQUENCE_ID,  <!-- 释放流水号 -->
										ABNORMAL_FLAG,  <!-- 异常标记 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #craneResultId#, #craneOrderId#, #craneId#, #craneName#, #startXPosition#, #startYPosition#, #startZPosition#, #endxPosition#, #endyPosition#, #endzPosition#, #inboundSequenceId#, #outboundSequenceId#, #abnormalFlag#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids1201 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlids1201 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					CRANE_RESULT_ID	= #craneResultId#,   <!-- 行车实绩单号 -->  
					CRANE_ORDER_ID	= #craneOrderId#,   <!-- 行车作业清单号 -->  
					CRANE_ID	= #craneId#,   <!-- 行车编号 -->  
					CRANE_NAME	= #craneName#,   <!-- 行车名称 -->  
					START_X_POSITION	= #startXPosition#,   <!-- 起始X轴坐标 -->  
					START_Y_POSITION	= #startYPosition#,   <!-- 起始X轴坐标 -->  
					START_Z_POSITION	= #startZPosition#,   <!-- 起始Y轴坐标 -->  
					ENDX_POSITION	= #endxPosition#,   <!-- 终到X轴坐标 -->  
					ENDY_POSITION	= #endyPosition#,   <!-- 终到X轴坐标 -->  
					ENDZ_POSITION	= #endzPosition#,   <!-- 终到Y轴坐标 -->  
					INBOUND_SEQUENCE_ID	= #inboundSequenceId#,   <!-- 抓取流水号 -->  
					OUTBOUND_SEQUENCE_ID	= #outboundSequenceId#,   <!-- 释放流水号 -->  
					ABNORMAL_FLAG	= #abnormalFlag#,   <!-- 异常标记 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
						WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>