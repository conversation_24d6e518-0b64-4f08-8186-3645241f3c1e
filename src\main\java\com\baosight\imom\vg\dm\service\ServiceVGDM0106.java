package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0101;
import com.baosight.imom.vg.dm.domain.VGDM0102;
import com.baosight.imom.vg.dm.domain.VGDM0106;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 资材领用机组维护
 * @Date : 2024/8/13
 * @Version : 1.0
 */
public class ServiceVGDM0106 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0106.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0106().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0106.QUERY, new VGDM0106());
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            // 用于缓存已查询的设备和分部设备信息
            Map<String, VGDM0101> equipmentCache = new HashMap<>();
            Map<String, VGDM0102> deviceCache = new HashMap<>();
            VGDM0106 vgdm0106;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0106 = new VGDM0106();
                vgdm0106.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0106);
                Map insMap = vgdm0106.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            DaoUtils.insertBatch(dao, VGDM0106.INSERT, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }


    /**
     * 数据校验
     *
     * @param vgdm0106 数据
     */
    private void checkData(VGDM0106 vgdm0106) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0106);
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0106 vgdm0106;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0106 = new VGDM0106();
                vgdm0106.fromMap(block.getRow(i));
                this.checkData(vgdm0106);
                vgdm0106.setDelFlag("0");
                Map updMap = vgdm0106.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0106.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>
     * 修改点检标准状态为删除，删除标记置1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0106 vgdm0106;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0106 = new VGDM0106();
                vgdm0106.fromMap(block.getRow(i));
                // 设置删除标记
                vgdm0106.setDelFlag("1");
                // 设置修改人
                Map delMap = vgdm0106.toMap();
                RecordUtils.setRevisor(delMap);
                // 数据返回前端
                block.getRows().set(i, delMap);
            }
            DaoUtils.updateBatch(dao, VGDM0106.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

}
