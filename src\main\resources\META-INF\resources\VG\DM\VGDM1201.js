$(function () {

    //定义tab的ID
    var tableId = "info-1";

    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                tableId = e.contentElement.id;
            }
        }
    };

    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        if(tableId == "info-1"){
            resultGrid.dataSource.page(1);
        }else{
            result1Grid.dataSource.page(1);
        }
    });
    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.refresh();
            },
            afterEdit: function (e) {
                if (e.field === "annualProcessingVolume" || e.field === "annualProductionRate") {
                    resultGrid.setCellValue(e.model, 'annualProcessingVolume', isNaN(e.model["annualProcessingVolume"]) ? 0 : e.model["annualProcessingVolume"]);
                    resultGrid.setCellValue(e.model, 'annualProductionRate', isNaN(e.model["annualProductionRate"]) ? 0 : e.model["annualProductionRate"]);
                }
            }
        },

        "result1": {
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT1").on("click", function (e) {
                    result1Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result1Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result1", "VGDM1201", "insert1", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result1Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result1", "VGDM1201", "update1", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result1Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result1", "VGDM1201", "delete1", false,
                        function (ei) {
                            result1Grid.removeRows(result1Grid.getCheckedRows());
                        }, null, false);
                });
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                result1Grid.setCellValue(0, 'segNo', segNo);
                result1Grid.setCellValue(0, 'unitCode', unitCode);
                result1Grid.setCellValue(0, 'segName', segName);
                result1Grid.refresh();
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);

});