<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3" value=" " disabled="true"/>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFPopupInput ename="inqu_status-0-warehouseCode" cname="仓库代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="warehouseInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-warehouseName"
                             popupTitle="仓库查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-warehouseName" cname="仓库名称" colWidth="3" disabled="true"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-locationId" cname="库位代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-locationName" cname="库位名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-posDirCode" cname="层数标记" colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-managementStyle" cname="管理方式" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P038"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="启用" value="10"/>
                <EF:EFOption label="停用" value="20"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-ifMaintainPoint" cname="是否维护坐标" align="center" width="150"
                         enable="true" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="是" value="10"/>
                <EF:EFOption label="否" value="20"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true"
                       serviceName="LIDS0601" queryMethod="query">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true" enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="locationId" cname="库位代码" align="center" width="150" primaryKey="true" enable="false"/>
                <EF:EFColumn ename="locationName" cname="库位名称" align="left" width="200" enable="false"/>
                <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="left" width="150" enable="false"/>
                <EF:EFColumn ename="warehouseName" cname="仓库名称" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="ifPlanFlag" cname="是否参与库位推荐" align="center" width="150" enable="false">
                    <EF:EFCodeOption codeName="P037"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="启用" value="10"/>
                    <EF:EFOption label="停用" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="left" width="100" enable="false"
                             readonly="true" required="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="left" width="100" enable="false"
                             readonly="true" required="false"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="left" width="150" enable="false"
                             readonly="true"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="left" width="150" enable="false"
                             readonly="true"/>
                <EF:EFColumn ename="crossArea" cname="跨区编码" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="crossAreaName" cname="跨区名称" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="xInitialPoint" cname="X轴起始点" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="xDestination" cname="X轴终到点" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="yInitialPoint" cname="Y轴起始点" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="yDestination" cname="Y轴终到点" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="posDirCode" cname="层数标记" align="right" width="150" enable="true"/>
                <EF:EFComboColumn ename="managementStyle" cname="管理方式" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P038"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="actionFlag" cname="板卷标记" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P039"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="loadingPointNo" cname="装卸点编码" align="left" width="150" enable="true"/>
                <EF:EFColumn ename="loadingPointName" cname="装卸点名称" align="left" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelNo" cname="装卸通道编码" align="left" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelName" cname="装卸通道名称" align="left" width="150" enable="false"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>

    <%--仓库代码弹窗    --%>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseInfo" width="90%" height="60%"/>
</EF:EFPage>
