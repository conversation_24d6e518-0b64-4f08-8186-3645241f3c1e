<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="特定业务单元管理">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <%--<EF:EFInput ename="inqu_status-0-fatherSegNo" cname="上级业务单元" disabled="false" required="true" colWidth="3"/>--%>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" needAuth="true" readonly="true"
                   rowNo="true" checkMode="single" isFloat="true" >
            <EF:EFColumn ename="segNo" cname="业务单元编号" width="120" enable="false" align="center" required="true" />
            <EF:EFColumn ename="segFullName" cname="业务单元名称" width="120" enable="false" align="center" required="true"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" width="120" enable="false" align="center" required="true"/>
           <%-- <EF:EFColumn ename="anotherName" cname="公司别名" width="120" enable="false" align="center" required="true"/>--%>
           <%-- <EF:EFColumn ename="segRuleStatus" cname="状态" width="120" enable="false" align="center" required="true"/>--%>
            <EF:EFColumn ename="attribute1" cname="附加属性1" width="120"  align="center" required="true"/>
            <EF:EFColumn ename="attribute2" cname="附加属性2" width="120"  align="center"/>
            <EF:EFColumn ename="attribute3" cname="附加属性3" width="120"  align="center"/>
            <EF:EFColumn ename="attribute4" cname="附加属性4" width="120"  align="center"/>
            <EF:EFColumn ename="attribute5" cname="附加属性5" width="120"  align="center"/>
            <EF:EFColumn ename="attribute6" cname="附加属性6" width="120"  align="center"/>
            <EF:EFColumn ename="remark" cname="备注" width="120" align="center" required="true"/>
        </EF:EFGrid>
    </EF:EFRegion>


</EF:EFPage>