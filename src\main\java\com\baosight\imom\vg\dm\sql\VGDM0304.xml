<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0304">
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="scadaName">
            SCADA_NAME = #scadaName#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0304">
        SELECT
        SCADA_NAME as "scadaName",  <!-- 节点名 -->
        DEVICE_ID as "deviceId",  <!-- 设备ID -->
        DEVICE_NAME as "deviceName",  <!-- 设备名称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        SCADA_PRIMARY_IP as "scadaPrimaryIp", <!-- 主节点IP -->
        EQUIPMENT_IHD_ID as "equipmentIhdId", <!-- 设备IHD点位 -->
        STATUS_FLAG as "statusFlag", <!-- 状态标记 -->
        MACHINE_CODE as "machineCode" <!-- 机组代码 -->
        FROM ${mevgSchema}.TVGDM0304 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0304 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryForAlarm" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        GROUP_CONCAT(T1.DEVICE_ID SEPARATOR ',') as "deviceId",  <!-- 设备ID -->
        GROUP_CONCAT(T1.DEVICE_NAME SEPARATOR ',') as "deviceName",  <!-- 设备名称 -->
        T1.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(T1.EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        MAX(T2.HANDLE_USER_ID) as "handleUserId",  <!-- 处理人 -->
        MAX(T2.HANDLE_USER_NAME) as "handleUserName",  <!-- 处理人姓名 -->
        T2.HANDLE_MOBILE as "handleMobile"  <!-- 处理人手机 -->
        FROM ${mevgSchema}.TVGDM0304 T1
        LEFT JOIN ${mevgSchema}.TVGDM0603 T2
        ON T1.SEG_NO=T2.SEG_NO
        AND T1.E_ARCHIVES_NO = T2.E_ARCHIVES_NO
        AND T2.STATUS = '10'
        AND T2.DEL_FLAG = '0'
        WHERE T1.SEG_NO = #segNo#
        AND T1.DEL_FLAG = '0'
        AND T1.DEVICE_ID IN
        <iterate property="deviceIdList" open="(" close=")"
                 conjunction=",">
            #deviceIdList[]#
        </iterate>
        AND T2.HANDLE_MOBILE IS NOT NULL
        GROUP BY T2.HANDLE_MOBILE,T1.E_ARCHIVES_NO
        ORDER BY T2.HANDLE_MOBILE,T1.E_ARCHIVES_NO
    </select>

    <select id="queryForDp" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MAX(SCADA_NAME) as "scadaName",  <!-- 节点名 -->
        GROUP_CONCAT(DEVICE_ID SEPARATOR ',') as "deviceId",  <!-- 设备ID -->
        GROUP_CONCAT(DEVICE_NAME SEPARATOR ',') as "deviceName",  <!-- 设备名称 -->
        MAX(MACHINE_CODE) as "machineCode",  <!-- 机组代码 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        MAX(SCADA_PRIMARY_IP) as "scadaPrimaryIp", <!-- 主节点IP -->
        MAX(EQUIPMENT_IHD_ID) as "equipmentIhdId" <!-- 设备IHD点位 -->
        FROM ${mevgSchema}.TVGDM0304 t WHERE
        DEL_FLAG = '0'
        <isNotEmpty property="statusFlag">
            AND STATUS_FLAG = #statusFlag#
        </isNotEmpty>
        GROUP BY SEG_NO,E_ARCHIVES_NO
        ORDER BY SEG_NO
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0304 (SCADA_NAME,  <!-- 节点名 -->
        DEVICE_ID,  <!-- 设备ID -->
        DEVICE_NAME,  <!-- 设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        SCADA_PRIMARY_IP,  <!-- 主节点IP -->
        EQUIPMENT_IHD_ID,  <!-- 设备IHD点位 -->
        STATUS_FLAG, <!-- 状态标记 -->
        MACHINE_CODE <!-- 组织代码 -->
        )
        VALUES (#scadaName#, #deviceId#, #deviceName#, #eArchivesNo#, #equipmentName#, #uuid#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#,
        #delFlag#, #segNo#, #unitCode#, #scadaPrimaryIp#, #equipmentIhdId#,#statusFlag#,#machineCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0304 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0304
        SET
        SCADA_NAME = #scadaName#,   <!-- 节点名 -->
        DEVICE_ID = #deviceId#,   <!-- 设备ID -->
        DEVICE_NAME = #deviceName#,   <!-- 设备名称 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        SCADA_PRIMARY_IP = #scadaPrimaryIp#,  <!-- 主节点IP -->
        EQUIPMENT_IHD_ID = #equipmentIhdId#,  <!-- 设备IHD点位 -->
        STATUS_FLAG = #statusFlag#, <!-- 状态标记 -->
        MACHINE_CODE = #machineCode# <!-- 组织代码 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>