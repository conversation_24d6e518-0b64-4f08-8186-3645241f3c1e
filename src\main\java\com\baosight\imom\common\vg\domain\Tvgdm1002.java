/**
 * Generate time : 2025-07-04 17:16:53
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.imom.common.utils.StrUtil;
import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm1002
 */
public class Tvgdm1002 extends DaoEPBase {

    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String processCategory = " ";        /* 工序大类代码*/
    private String machineCode = " ";        /* 机组代码*/
    private String machineName = " ";        /* 机组名称*/
    private String processOrderId = " ";        /* 生产工单号*/
    private String packId = " ";        /* 捆包号*/
    private String partId = " ";        /* 物料号*/
    private String specsDesc = " ";        /* 规格描述*/
    private String shopsign = " ";        /* 牌号*/
    private BigDecimal netWeight = new BigDecimal("0");        /* 净重*/
    private String prodNameCode = " ";        /* 品名代码*/
    private String prodCname = " ";        /* 品名名称*/
    private String upPackTime = " ";        /* 上料时间*/
    private String startTime = " ";        /* 开始时间*/
    private String endTime = " ";        /* 结束时间*/
    private String endType = " ";        /* 标记类型0加工完成1退料2余料3换刀*/
    private String relevanceId = " ";        /* 关联ID*/
    private String packStatus = " ";        /* 捆包状态0上料1加工2完成*/
    private String unitedPackId = " ";        /* 并包号*/
    private String currentKnife = "1";        /* 当前排刀号*/
    private String knifeSort = "1";        /* 排刀顺序*/
    private BigDecimal unitedQuantity = new BigDecimal("0");    /* 并包数量*/
    private String unitedStackName = " ";        /* 并包堆垛*/
    private String endStackName = " ";        /* 尾包堆垛*/
    private String finishingShuntFlag = " ";        /* 精整分流标记*/
    private BigDecimal sumUnitedQuantity = new BigDecimal("0");         /* 累计并包量*/
    private String originalPackId = " ";        /* 原始捆包号*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String matInnerId = " ";        /* 材料管理号*/
    private String unitedUuid = " ";        /* 并包单据号*/
    private String tradeCode = " ";        /* 贸易方式*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("规格描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodNameCode");
        eiColumn.setDescName("品名代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCname");
        eiColumn.setDescName("品名名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upPackTime");
        eiColumn.setDescName("上料时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startTime");
        eiColumn.setDescName("开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endTime");
        eiColumn.setDescName("结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endType");
        eiColumn.setDescName("标记类型0加工完成1退料2余料3换刀");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("relevanceId");
        eiColumn.setDescName("关联ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packStatus");
        eiColumn.setDescName("捆包状态0上料1加工2完成");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedPackId");
        eiColumn.setDescName("并包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentKnife");
        eiColumn.setDescName("当前排刀号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("knifeSort");
        eiColumn.setDescName("排刀顺序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedQuantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("并包数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedStackName");
        eiColumn.setDescName("并包堆垛");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endStackName");
        eiColumn.setDescName("尾包堆垛");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finishingShuntFlag");
        eiColumn.setDescName("精整分流标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sumUnitedQuantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("累计并包量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("originalPackId");
        eiColumn.setDescName("原始捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("matInnerId");
        eiColumn.setDescName("材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedUuid");
        eiColumn.setDescName("并包单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setDescName("贸易方式");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm1002() {
        initMetaData();
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the processCategory - 工序大类代码
     *
     * @return the processCategory
     */
    public String getProcessCategory() {
        return this.processCategory;
    }

    /**
     * set the processCategory - 工序大类代码
     */
    public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
    }

    /**
     * get the machineCode - 机组代码
     *
     * @return the machineCode
     */
    public String getMachineCode() {
        return this.machineCode;
    }

    /**
     * set the machineCode - 机组代码
     */
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    /**
     * get the machineName - 机组名称
     *
     * @return the machineName
     */
    public String getMachineName() {
        return this.machineName;
    }

    /**
     * set the machineName - 机组名称
     */
    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    /**
     * get the processOrderId - 生产工单号
     *
     * @return the processOrderId
     */
    public String getProcessOrderId() {
        return this.processOrderId;
    }

    /**
     * set the processOrderId - 生产工单号
     */
    public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
    }

    /**
     * get the packId - 捆包号
     *
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the partId - 物料号
     *
     * @return the partId
     */
    public String getPartId() {
        return this.partId;
    }

    /**
     * set the partId - 物料号
     */
    public void setPartId(String partId) {
        this.partId = partId;
    }

    /**
     * get the specsDesc - 规格描述
     *
     * @return the specsDesc
     */
    public String getSpecsDesc() {
        return this.specsDesc;
    }

    /**
     * set the specsDesc - 规格描述
     */
    public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
    }

    /**
     * get the shopsign - 牌号
     *
     * @return the shopsign
     */
    public String getShopsign() {
        return this.shopsign;
    }

    /**
     * set the shopsign - 牌号
     */
    public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
    }

    /**
     * get the netWeight - 净重
     *
     * @return the netWeight
     */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
     * set the netWeight - 净重
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * get the prodNameCode - 品名代码
     *
     * @return the prodNameCode
     */
    public String getProdNameCode() {
        return this.prodNameCode;
    }

    /**
     * set the prodNameCode - 品名代码
     */
    public void setProdNameCode(String prodNameCode) {
        if (prodNameCode == null) {
            prodNameCode = " ";
        }
        this.prodNameCode = prodNameCode;
    }

    /**
     * get the prodCname - 品名名称
     *
     * @return the prodCname
     */
    public String getProdCname() {
        return this.prodCname;
    }

    /**
     * set the prodCname - 品名名称
     */
    public void setProdCname(String prodCname) {
        if (prodCname == null) {
            prodCname = " ";
        }
        this.prodCname = prodCname;
    }

    /**
     * get the upPackTime - 上料时间
     *
     * @return the upPackTime
     */
    public String getUpPackTime() {
        return this.upPackTime;
    }

    /**
     * set the upPackTime - 上料时间
     */
    public void setUpPackTime(String upPackTime) {
        this.upPackTime = upPackTime;
    }

    /**
     * get the startTime - 开始时间
     *
     * @return the startTime
     */
    public String getStartTime() {
        return this.startTime;
    }

    /**
     * set the startTime - 开始时间
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * get the endTime - 结束时间
     *
     * @return the endTime
     */
    public String getEndTime() {
        return this.endTime;
    }

    /**
     * set the endTime - 结束时间
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    /**
     * get the endType - 标记类型0加工完成1退料2余料3换刀
     *
     * @return the endType
     */
    public String getEndType() {
        return this.endType;
    }

    /**
     * set the endType - 标记类型0加工完成1退料2余料3换刀
     */
    public void setEndType(String endType) {
        this.endType = endType;
    }

    /**
     * get the relevanceId - 关联ID
     *
     * @return the relevanceId
     */
    public String getRelevanceId() {
        return this.relevanceId;
    }

    /**
     * set the relevanceId - 关联ID
     */
    public void setRelevanceId(String relevanceId) {
        this.relevanceId = relevanceId;
    }

    /**
     * get the packStatus - 捆包状态0上料1加工2完成
     *
     * @return the packStatus
     */
    public String getPackStatus() {
        return this.packStatus;
    }

    /**
     * set the packStatus - 捆包状态0上料1加工2完成
     */
    public void setPackStatus(String packStatus) {
        this.packStatus = packStatus;
    }

    /**
     * get the unitedPackId - 并包号
     *
     * @return the unitedPackId
     */
    public String getUnitedPackId() {
        return this.unitedPackId;
    }

    /**
     * set the unitedPackId - 并包号
     */
    public void setUnitedPackId(String unitedPackId) {
        this.unitedPackId = unitedPackId;
    }

    /**
     * get the currentKnife - 当前排刀号
     *
     * @return the currentKnife
     */
    public String getCurrentKnife() {
        return this.currentKnife;
    }

    /**
     * set the currentKnife - 当前排刀号
     */
    public void setCurrentKnife(String currentKnife) {
        this.currentKnife = currentKnife;
    }

    /**
     * get the knifeSort - 排刀顺序
     *
     * @return the knifeSort
     */
    public String getKnifeSort() {
        return this.knifeSort;
    }

    /**
     * set the knifeSort - 排刀顺序
     */
    public void setKnifeSort(String knifeSort) {
        this.knifeSort = knifeSort;
    }

    /**
     * get the unitedQuantity - 并包数量
     *
     * @return the unitedQuantity
     */
    public BigDecimal getUnitedQuantity() {
        return this.unitedQuantity;
    }

    /**
     * set the unitedQuantity - 并包数量
     */
    public void setUnitedQuantity(BigDecimal unitedQuantity) {
        this.unitedQuantity = unitedQuantity;
    }

    /**
     * get the unitedStackName - 并包堆垛
     *
     * @return the unitedStackName
     */
    public String getUnitedStackName() {
        return this.unitedStackName;
    }

    /**
     * set the unitedStackName - 并包堆垛
     */
    public void setUnitedStackName(String unitedStackName) {
        this.unitedStackName = unitedStackName;
    }

    /**
     * get the endStackName - 尾包堆垛
     *
     * @return the endStackName
     */
    public String getEndStackName() {
        return this.endStackName;
    }

    /**
     * set the endStackName - 尾包堆垛
     */
    public void setEndStackName(String endStackName) {
        this.endStackName = endStackName;
    }

    /**
     * get the finishingShuntFlag - 精整分流标记
     *
     * @return the finishingShuntFlag
     */
    public String getFinishingShuntFlag() {
        return this.finishingShuntFlag;
    }

    /**
     * set the finishingShuntFlag - 精整分流标记
     */
    public void setFinishingShuntFlag(String finishingShuntFlag) {
        this.finishingShuntFlag = finishingShuntFlag;
    }

    /**
     * get the sumUnitedQuantity - 累计并包量
     *
     * @return the sumUnitedQuantity
     */
    public BigDecimal getSumUnitedQuantity() {
        return this.sumUnitedQuantity;
    }

    /**
     * set the sumUnitedQuantity - 累计并包量
     */
    public void setSumUnitedQuantity(BigDecimal sumUnitedQuantity) {
        this.sumUnitedQuantity = sumUnitedQuantity;
    }

    /**
     * get the originalPackId - 原始捆包号
     *
     * @return the originalPackId
     */
    public String getOriginalPackId() {
        return this.originalPackId;
    }

    /**
     * set the originalPackId - 原始捆包号
     */
    public void setOriginalPackId(String originalPackId) {
        if (originalPackId == null) {
            originalPackId = " ";
        }
        this.originalPackId = originalPackId;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the matInnerId - 材料管理号
     *
     * @return the matInnerId
     */
    public String getMatInnerId() {
        return this.matInnerId;
    }

    /**
     * set the matInnerId - 材料管理号
     */
    public void setMatInnerId(String matInnerId) {
        if (StrUtil.isBlank(matInnerId)) {
            matInnerId = " ";
        }
        this.matInnerId = matInnerId;
    }

    /**
     * get the unitedUuid - 并包单据号
     *
     * @return the unitedUuid
     */
    public String getUnitedUuid() {
        return this.unitedUuid;
    }

    /**
     * set the unitedUuid - 并包单据号
     */
    public void setUnitedUuid(String unitedUuid) {
        this.unitedUuid = unitedUuid;
    }

    /**
     * get the tradeCode - 贸易方式
     *
     * @return the tradeCode
     */
    public String getTradeCode() {
        return this.tradeCode;
    }

    /**
     * set the tradeCode - 贸易方式
     */
    public void setTradeCode(String tradeCode) {
        if (tradeCode == null) {
            tradeCode = " ";
        }
        this.tradeCode = tradeCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
        setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
        setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
        setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
        setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
        setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
        setProdNameCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodNameCode")), prodNameCode));
        setProdCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCname")), prodCname));
        setUpPackTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("upPackTime")), upPackTime));
        setStartTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startTime")), startTime));
        setEndTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endTime")), endTime));
        setEndType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endType")), endType));
        setRelevanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevanceId")), relevanceId));
        setPackStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packStatus")), packStatus));
        setUnitedPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedPackId")), unitedPackId));
        setCurrentKnife(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("currentKnife")), currentKnife));
        setKnifeSort(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("knifeSort")), knifeSort));
        setUnitedQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("unitedQuantity")), unitedQuantity));
        setUnitedStackName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedStackName")), unitedStackName));
        setEndStackName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endStackName")), endStackName));
        setFinishingShuntFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finishingShuntFlag")), finishingShuntFlag));
        setSumUnitedQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("sumUnitedQuantity")), sumUnitedQuantity));
        setOriginalPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("originalPackId")), originalPackId));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
        setUnitedUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedUuid")), unitedUuid));
        setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {
        Map map = new HashMap();
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("processCategory", StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("machineName", StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("processOrderId", StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("partId", StringUtils.toString(partId, eiMetadata.getMeta("partId")));
        map.put("specsDesc", StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
        map.put("shopsign", StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
        map.put("netWeight", StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
        map.put("prodNameCode", StringUtils.toString(prodNameCode, eiMetadata.getMeta("prodNameCode")));
        map.put("prodCname", StringUtils.toString(prodCname, eiMetadata.getMeta("prodCname")));
        map.put("upPackTime", StringUtils.toString(upPackTime, eiMetadata.getMeta("upPackTime")));
        map.put("startTime", StringUtils.toString(startTime, eiMetadata.getMeta("startTime")));
        map.put("endTime", StringUtils.toString(endTime, eiMetadata.getMeta("endTime")));
        map.put("endType", StringUtils.toString(endType, eiMetadata.getMeta("endType")));
        map.put("relevanceId", StringUtils.toString(relevanceId, eiMetadata.getMeta("relevanceId")));
        map.put("packStatus", StringUtils.toString(packStatus, eiMetadata.getMeta("packStatus")));
        map.put("unitedPackId", StringUtils.toString(unitedPackId, eiMetadata.getMeta("unitedPackId")));
        map.put("currentKnife", StringUtils.toString(currentKnife, eiMetadata.getMeta("currentKnife")));
        map.put("knifeSort", StringUtils.toString(knifeSort, eiMetadata.getMeta("knifeSort")));
        map.put("unitedQuantity", StringUtils.toString(unitedQuantity, eiMetadata.getMeta("unitedQuantity")));
        map.put("unitedStackName", StringUtils.toString(unitedStackName, eiMetadata.getMeta("unitedStackName")));
        map.put("endStackName", StringUtils.toString(endStackName, eiMetadata.getMeta("endStackName")));
        map.put("finishingShuntFlag", StringUtils.toString(finishingShuntFlag, eiMetadata.getMeta("finishingShuntFlag")));
        map.put("sumUnitedQuantity", StringUtils.toString(sumUnitedQuantity, eiMetadata.getMeta("sumUnitedQuantity")));
        map.put("originalPackId", StringUtils.toString(originalPackId, eiMetadata.getMeta("originalPackId")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("matInnerId", StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
        map.put("unitedUuid", StringUtils.toString(unitedUuid, eiMetadata.getMeta("unitedUuid")));
        map.put("tradeCode", StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
        return map;
    }
}