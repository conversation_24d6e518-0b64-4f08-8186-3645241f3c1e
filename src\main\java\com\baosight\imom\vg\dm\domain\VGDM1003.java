package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm1003;

/**
 * 工序时间结果表
 */
public class VGDM1003 extends Tvgdm1003 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM1003.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM1003.count";
    /**
     * 计算总准备时间
     */
    public static final String SUM_PREPARE = "VGDM1003.sumPrepare";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM1003.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM1003.update";
    /**
     * 删除
     */
    public static final String DELETE_BY_RELEVANCE = "VGDM1003.deleteByRelevance";
    /**
     * 逻辑删除
     */
    public static final String UPDATE_BY_RELEVANCE = "VGDM1003.updateByRelevance";
}
