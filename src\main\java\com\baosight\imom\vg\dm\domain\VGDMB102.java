package com.baosight.imom.vg.dm.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 标准工时列信息
 */
public class VGDMB102 extends DaoEPBase {

    /**
     * 机组代码
     */
    private String machineCode = " ";

    /**
     * 机组名称
     */
    private String machineName = " ";

    /**
     * 分条数
     */
    private String stripCount = " ";

    /**
     * 物料号
     */
    private String partId = " ";

    /**
     * 规格
     */
    private String specsDesc = " ";

    /**
     * 品种
     */
    private String prodTypeId = " ";

    /**
     * 品种名称
     */
    private String prodTypeName = " ";

    /**
     * 加工速度(米/分钟)
     */
    private String processSpeed = " ";

    /**
     * sph值(片/h)
     */
    private String sph = " ";

    /**
     * 工单
     */
    private String processOrderId = " ";

    /**
     * 原料捆包号
     */
    private String packId = " ";

    /**
     * 加工速度(米/分钟)
     */
    private String inSpecsDesc = " ";

    /**
     * 原料物料号
     */
    private String inPartId = " ";




    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("machineCode");
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stripCount");
        eiColumn.setDescName("分条数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("品种");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeName");
        eiColumn.setDescName("品种名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSpeed");
        eiColumn.setDescName("加工速度(米/分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSpeed");
        eiColumn.setDescName("加工速度(米/分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sph");
        eiColumn.setDescName("sph值(片/h)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setDescName("工单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("原料捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inSpecsDesc");
        eiColumn.setDescName("原料规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("inPartId");
        eiColumn.setDescName("原料物料号");
        eiMetadata.addMeta(eiColumn);
    }


    /**
     * the constructor
     */
    public VGDMB102() {
        initMetaData();
    }


    /**

 * get the value from Map
 */
public void fromMap(Map map) {
    setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
    setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
    setStripCount(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stripCount")), stripCount));
    setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
    setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
    setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
    setProdTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeName")), prodTypeName));
    setProcessSpeed(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSpeed")), processSpeed));
    setSph(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sph")), sph));
    setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
    setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
    setInSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inSpecsDesc")), inSpecsDesc));
    setInPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("inPartId")), inPartId));
}

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("machineName", StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("stripCount", StringUtils.toString(stripCount, eiMetadata.getMeta("stripCount")));
        map.put("partId", StringUtils.toString(partId, eiMetadata.getMeta("partId")));
        map.put("specsDesc", StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
        map.put("prodTypeId", StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
        map.put("prodTypeName", StringUtils.toString(prodTypeName, eiMetadata.getMeta("prodTypeName")));
        map.put("processSpeed", StringUtils.toString(processSpeed, eiMetadata.getMeta("processSpeed")));
        map.put("sph", StringUtils.toString(sph, eiMetadata.getMeta("sph")));
        map.put("processOrderId", StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("inSpecsDesc", StringUtils.toString(inSpecsDesc, eiMetadata.getMeta("inSpecsDesc")));
        map.put("inPartId", StringUtils.toString(inPartId, eiMetadata.getMeta("inPartId")));
        return map;
    }


    public String getMachineCode() {
        return machineCode;
    }

    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    public String getMachineName() {
        return machineName;
    }

    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    public String getStripCount() {
        return stripCount;
    }

    public void setStripCount(String stripCount) {
        this.stripCount = stripCount;
    }

    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public String getSpecsDesc() {
        return specsDesc;
    }

    public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
    }

    public String getProdTypeId() {
        return prodTypeId;
    }

    public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
    }

    public String getProdTypeName() {
        return prodTypeName;
    }

    public void setProdTypeName(String prodTypeName) {
        this.prodTypeName = prodTypeName;
    }

    public String getProcessSpeed() {
        return processSpeed;
    }

    public void setProcessSpeed(String processSpeed) {
        this.processSpeed = processSpeed;
    }

    public String getSph() {
        return sph;
    }

    public void setSph(String sph) {
        this.sph = sph;
    }

    public String getProcessOrderId() {
        return processOrderId;
    }

    public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
    }

    public String getPackId() {
        return packId;
    }

    public void setPackId(String packId) {
        this.packId = packId;
    }

    public String getInSpecsDesc() {
        return inSpecsDesc;
    }

    public void setInSpecsDesc(String inSpecsDesc) {
        this.inSpecsDesc = inSpecsDesc;
    }

    public String getInPartId() {
        return inPartId;
    }

    public void setInPartId(String inPartId) {
        this.inPartId = inPartId;
    }
}
