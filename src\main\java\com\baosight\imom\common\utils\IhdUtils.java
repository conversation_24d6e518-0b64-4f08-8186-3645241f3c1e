package com.baosight.imom.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * IHD平台工具类 todo 待IHD部署后测试
 *
 * <AUTHOR> 郁在杰
 * @Description :IHD平台工具类
 * @Date : 2024/11/18
 * @Version : 1.0
 */
public class IhdUtils {
    // 数据源相关配置
    private static final String DATA_SOURCE_STR = ".ihd.dataSource";
    private static final String CLIENT_ID_STR = ".ihd.clientId";
    private static final String SECRET_STR = ".ihd.secret";
    // 微服务配置
    private static final String SERVICE_QUERY_HISTORY = "S_BI_HD_05";
    // 缓存各业务单元下IHD配置
    private static final Map<String, IhdConfig> IHD_CONFIG_MAP = new ConcurrentHashMap<>();

    // IHD配置类
    private static class IhdConfig {
        // 数据源
        private final String dataSource;
        // 客户端ID
        private final String clientId;
        // 秘钥
        private final String secret;

        public IhdConfig(String dataSource, String clientId, String secret) {
            this.dataSource = dataSource;
            this.clientId = clientId;
            this.secret = secret;
        }

        public String getDataSource() {
            return dataSource;
        }

        public String getClientId() {
            return clientId;
        }

        public String getSecret() {
            return secret;
        }

        public boolean isValid() {
            return StrUtil.isNotBlank(dataSource) && StrUtil.isNotBlank(clientId) && StrUtil.isNotBlank(secret);
        }
    }

    // 参数常量
    private static class ParamConstants {
        private static final String START_TIME = "startTime";
        private static final String END_TIME = "endTime";
        private static final String MODEL = "model";
        private static final String LIMIT = "limit";
        private static final String CLIENT_ID = "clientId";
        private static final String SECRET = "secret";
        private static final String TAG_NAME_LIST = "tagNameList";
        private static final String DS_TAGS = "dsTags";
        private static final String RESULT_LIST = "resultList";
        private static final String TAG_NAME = "tagName";
        private static final String RECORDS = "records";
        private static final String RECORD_TIME = "recordTime";
        private static final String VALUE = "value";
    }

    /**
     * 获取IHD配置
     *
     * @param segNoPrefix 业务单元代码前缀
     * @return IHD配置
     */
    private static IhdConfig getIhdConfig(String segNoPrefix) {
        // 先从缓存中获取
        IhdConfig config = IHD_CONFIG_MAP.get(segNoPrefix);
        if (config != null) {
            return config;
        }
        // 从配置中获取
        config = new IhdConfig(
                PlatApplicationContext.getProperty(segNoPrefix + DATA_SOURCE_STR),
                PlatApplicationContext.getProperty(segNoPrefix + CLIENT_ID_STR),
                PlatApplicationContext.getProperty(segNoPrefix + SECRET_STR));
        // 如果配置不合法，则抛出异常
        if (!config.isValid()) {
            throw new PlatException("IHD配置不合法，segNoPrefix：" + segNoPrefix);
        }
        // 添加至缓存
        IHD_CONFIG_MAP.put(segNoPrefix, config);
        return config;
    }

    /**
     * 查询历史值
     *
     * @param segNo       业务单元代码
     * @param startTime   开始时间，格式2024-07-31T00:00:00.000
     * @param endTime     结束时间，格式2024-07-31T00:00:00.000
     * @param model       查询类型，1表示向后查(记录时间小于起始时间)，0表示向前查(记录时间大于起始时间)
     * @param limit       查询条数，最大65535
     * @param tagNameList tagName列表(ihd中的tag名称)
     * @param isSort      是否按时间升序排序
     * @return 历史值列表 map中key为tagName, recordTime, value
     */
    public static List<Map<String, Object>> queryHistoryValue(String segNo, String startTime, String endTime, int model,
                                                              int limit, List<String> tagNameList, boolean isSort) {
        // 参数校验
        validateParams(segNo, startTime, endTime, tagNameList, limit);
        // 准备请求参数
        EiInfo eiInfo = prepareRequestParams(segNo, startTime, endTime, model, limit, tagNameList);
        // 调用服务并获取结果
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (outInfo.getStatus() != 0) {
            throw new PlatException(outInfo.getMsg());
        }
        // 解析结果
        List<Map<String, Object>> resultList = parseResponse(outInfo);
        // 排序（如果需要）
        if (isSort && !resultList.isEmpty()) {
            sortResults(resultList);
        }
        return resultList;
    }

    /**
     * 查询所有TAG值并按时间升序排列（最大65535条）
     *
     * @param segNo       业务单元代码
     * @param startTime   开始时间，格式2024-07-31T00:00:00.000
     * @param endTime     结束时间，格式2024-07-31T00:00:00.000
     * @param model       查询类型，1表示向后查，0表示向前查
     * @param tagNameList 标签名列表
     * @return 所有tag列表（最大65535条） map中key为tagName, recordTime, value
     */
    public static List<Map<String, Object>> queryHistoryValue(String segNo, String startTime, String endTime, int model,
                                                              List<String> tagNameList) {
        return queryHistoryValue(segNo, startTime, endTime, model, 65535, tagNameList, true);
    }

    /**
     * 验证输入参数
     */
    private static void validateParams(String segNo, String startTime, String endTime, List<String> tagNameList, int limit) {
        if (StrUtil.isBlank(segNo)) {
            throw new IllegalArgumentException("业务单元代码不能为空");
        }
        try {
            LocalDateTime.parse(startTime, DateUtils.FORMATTER_24);
            LocalDateTime.parse(endTime, DateUtils.FORMATTER_24);
        } catch (Exception e) {
            throw new IllegalArgumentException("时间格式错误，正确格式为: yyyy-MM-dd'T'HH:mm:ss.SSS");
        }
        if (CollectionUtils.isEmpty(tagNameList)) {
            throw new IllegalArgumentException("TAG列表不能为空");
        }
        if (limit <= 0 || limit > 65535) {
            throw new IllegalArgumentException("limit参数必须在1-65535之间");
        }
    }

    /**
     * 准备请求参数
     */
    private static EiInfo prepareRequestParams(String segNo, String startTime, String endTime, int model, int limit,
                                               List<String> tagNameList) {
        String segNoPrefix = segNo.substring(0, 2);
        // 获取IHD配置
        IhdConfig config = getIhdConfig(segNoPrefix);
        // 按数据源设置TAG列表，key为数据源，value为TAGMap
        Map<String, Object> dsTags = new HashMap<>(1);
        Map<String, Object> valueMap = new HashMap<>(3);
        // 设置客户端ID
        valueMap.put(ParamConstants.CLIENT_ID, config.getClientId());
        // 设置秘钥
        valueMap.put(ParamConstants.SECRET, config.getSecret());
        // 设置标签名列表
        valueMap.put(ParamConstants.TAG_NAME_LIST, tagNameList);
        // 设置数据源TAG参数
        dsTags.put(config.getDataSource(), valueMap);
        EiInfo eiInfo = new EiInfo();
        // 设置请求参数
        eiInfo.set(ParamConstants.START_TIME, startTime);
        eiInfo.set(ParamConstants.END_TIME, endTime);
        eiInfo.set(ParamConstants.MODEL, model);
        eiInfo.set(ParamConstants.LIMIT, limit);
        eiInfo.set(ParamConstants.DS_TAGS, dsTags);
        // 设置服务ID
        eiInfo.set(EiConstant.serviceId, SERVICE_QUERY_HISTORY + segNoPrefix);
        return eiInfo;
    }

    /**
     * 解析服务响应
     */
    private static List<Map<String, Object>> parseResponse(EiInfo outInfo) {
        // 获取返回结果
        String resultStr = outInfo.getString(ParamConstants.RESULT_LIST);
        if (StrUtil.isBlank(resultStr)) {
            return Collections.emptyList();
        }
        // 返回结果
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 解析返回结果
        JSONArray jsonArray = JSON.parseArray(resultStr);
        // 预估容量，避免频繁扩容
        int estimatedSize = calculateEstimatedSize(jsonArray);
        resultList = new ArrayList<>(estimatedSize);
        // 遍历返回结果
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            // 获取标签名
            String tagName = jsonObject.getString(ParamConstants.TAG_NAME);
            // 获取记录
            JSONArray records = jsonObject.getJSONArray(ParamConstants.RECORDS);
            // 如果记录为空，则跳过
            if (records.isEmpty()) {
                continue;
            }
            // 遍历记录
            for (int j = 0; j < records.size(); j++) {
                JSONObject record = records.getJSONObject(j);
                Map<String, Object> resultMap = new HashMap<>(3);
                // 设置标签名
                resultMap.put(ParamConstants.TAG_NAME, tagName);
                // 设置记录时间
                resultMap.put(ParamConstants.RECORD_TIME,
                        LocalDateTime.parse(record.getString(ParamConstants.RECORD_TIME), DateUtils.FORMATTER_24));
                // 设置值
                resultMap.put(ParamConstants.VALUE, record.getString(ParamConstants.VALUE));
                // 添加到结果集
                resultList.add(resultMap);
            }
        }
        return resultList;
    }

    /**
     * 计算预估结果集大小
     */
    private static int calculateEstimatedSize(JSONArray jsonArray) {
        int totalSize = 0;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONArray records = jsonObject.getJSONArray(ParamConstants.RECORDS);
            totalSize += records.size();
        }
        return totalSize;
    }

    /**
     * 结果排序
     */
    private static void sortResults(List<Map<String, Object>> resultList) {
        resultList.sort(Comparator.comparing(map -> (LocalDateTime) map.get(ParamConstants.RECORD_TIME)));
    }
}
