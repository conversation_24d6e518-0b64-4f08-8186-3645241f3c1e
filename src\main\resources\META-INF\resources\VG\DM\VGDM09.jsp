<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="清单信息" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-scrapApplyId" cname="申请单号" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                                colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-stuffName" cname="资材名称" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFSelect ename="inqu_status-0-applyStatus" cname="状态" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P016"/>
                    </EF:EFSelect>
                    <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                                   endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                                   startCname="创建时间(起)" endCname="创建时间(止)"
                                   ratio="3:3" format="yyyy-MM-dd">
                    </EF:EFDateSpan>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="查询结果">
                <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="scrapApplyId" cname="申请单号" enable="false" width="110"
                                 align="center"/>
                    <EF:EFComboColumn ename="applyStatus" cname="状态" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P016"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="apprStatus" enable="false" cname="审批状态" align="center" width="70">
                        <EF:EFOption label="审批中" value="60"/>
                        <EF:EFOption label="审核通过" value="70"/>
                        <EF:EFOption label="审核驳回" value="7X"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="70" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="stuffCode" cname="资材代码" align="center" width="100"/>
                    <EF:EFColumn ename="stuffName" cname="资材名称"/>
                    <EF:EFColumn ename="specDesc" cname="规格"/>
                    <EF:EFColumn ename="applyQty" cname="数量" align="right" width="70"/>
                    <EF:EFColumn ename="unitPrice" cname="资材单价" align="right" width="80"/>
                    <EF:EFColumn ename="amountMoney" cname="总金额" align="right" width="80"/>
                    <EF:EFComboColumn ename="scrapType" cname="报废方式" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P062"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="80"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="详细信息" id="info-2">
            <EF:EFRegion id="detail" title="详细信息">
                <EF:EFInput ename="detail_status-0-uuid" cname="UUID" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="unitInfo01" center="true"
                                     ename="detail_status-0-unitCode" cname="业务单元代码" colWidth="3"/>
                    <EF:EFSelect readonly="true" ename="detail_status-0-segNo" cname="业务单元简称" colWidth="3">
                        <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-scrapApplyId" readonly="true" cname="申请单号"
                                colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="equipmentInfo" center="true"
                                     ename="detail_status-0-equipmentName" cname="设备名称" colWidth="3"/>
                    <EF:EFPopupInput ename="detail_status-0-deptId" cname="申请部门" colWidth="3" required="true"
                                     readonly="true" clear="false" containerId="deptInfo" originalInput="true"
                                     center="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="detail_status-0-deptName" cname="部门名称" colWidth="3" readonly="true"/>

                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-stuffCode" readonly="true" cname="资材代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="stuffInfo" center="true"
                                     ename="detail_status-0-stuffName" cname="资材名称" colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-specDesc" readonly="true" cname="型号规格" colWidth="6"
                                ratio="2:10"/>
                </div>
                <div class="row">
                    <EF:EFSelect required="true" ename="detail_status-0-scrapType" cname="报废方式" colWidth="3">
                        <EF:EFCodeOption codeName="P062"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-applyQty" required="true" cname="数量"
                                colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                    <EF:EFInput ename="detail_status-0-unitPrice" readonly="true" cname="资材单价"
                                colWidth="3"/>
                    <EF:EFInput ename="detail_status-0-amountMoney" readonly="true" cname="总金额"
                                colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-scrapReason" cname="报废原因"
                                colWidth="12" placeholder="请输入报废原因"
                                ratio="1:11" type="textarea"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="附件信息">
                <EF:EFInput ename="inqu2_status-0-relevanceId" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-segNo" type="hidden"/>
                <div style="display: none;">
                    <EF:EFInput ename="fileForm" type="file"/>
                    <EF:EFInput ename="fileForm1" type="file"/>
                </div>
                <EF:EFGrid blockId="result2" autoDraw="no" readonly="true"
                           queryMethod="queryFile" sort="all">
                    <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                    <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                    <EF:EFColumn ename="relevanceId" cname="报废申请单号" width="120" align="center"/>
                    <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                    <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                    <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                    <%--                    <EF:EFColumn ename="fifleSize" cname="文件大小"/>--%>
                    <EF:EFColumn ename="fifleType" cname="文件类型"/>
                    <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow id="popup" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS08" id="deptInfo" width="90%" height="80%"/>
    <EF:EFWindow url="${ctx}/web/VFPM0102" id="stuffInfo" width="90%" height="80%"/>
</EF:EFPage>