<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-01-10 13:41:22
   		Version :  1.0
		tableName :meli.tlirl0504 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 PER_NO  VARCHAR   NOT NULL, 
		 PER_TYPE  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey
	-->
<sqlMap namespace="LIRL0504">
	<sql id="conditionPage">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="perNo">
			PER_NO = #perNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="perName">
			PER_NAME = #perName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="perType">
			PER_TYPE = #perType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<!--创建时间起-->
		<isNotEmpty prepend=" and " property="recCreateTimeStart">
			substr(REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
		</isNotEmpty>
		<!--创建时间止-->
		<isNotEmpty prepend=" and " property="recCreateTimeEnd">
			substr(REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0504">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
		        (select SEG_NAME from ${platSchema}.TVZBM81 t where t.SEG_NO = b.SEG_NO and t.DEL_FLAG = 0) as  "segName",         <!-- 业务单元简称 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				PER_NO	as "perNo",  <!-- 人员工号 -->
				PER_TYPE	as "perType",  <!-- 人员类型(叉车工：1、行车工：2) -->
				UUID	as "uuid", <!-- uuid -->
				PER_NAME as "perName",
				STATUS AS "status"
		FROM meli.tlirl0504 b WHERE 1=1
		<include refid="conditionPage"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notPreType">
			PER_TYPE != '3'
		</isNotEmpty>
		<isEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = 0
		</isEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			PER_TYPE desc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0504 WHERE 1=1
		<include refid="conditionPage"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
	</select>
	


	<insert id="insert">
		INSERT INTO meli.tlirl0504
		<dynamic prepend="(" close=")">

			SEG_NO,  <!-- 业务单元代码 -->
			UNIT_CODE,  <!-- 业务单元代码 -->
			REC_CREATOR,  <!-- 记录创建人 -->
			REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
			REC_CREATE_TIME,  <!-- 记录创建时间 -->
			REC_REVISOR,  <!-- 记录修改人 -->
			REC_REVISOR_NAME,  <!-- 记录创建人姓名 -->
			REC_REVISE_TIME,  <!-- 记录修改时间 -->
			ARCHIVE_FLAG,  <!-- 归档标记 -->
			DEL_FLAG,  <!-- 记录删除标记 -->
			REMARK,  <!-- 备注 -->
			PER_NO,  <!-- 人员工号 -->
			PER_TYPE,  <!-- 人员类型(叉车工：1、行车工：2) -->
			UUID ,<!-- uuid -->
			STATUS,
			PER_NAME

		</dynamic>
		VALUES
			(#segNo#, #unitCode#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
			#recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #perNo#,
			#perType#, #uuid#,#status#,#perName#)


	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0504 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0504 
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					PER_NO	= #perNo#,   <!-- 人员工号 -->  
					PER_TYPE	= #perType#   <!-- 人员类型(叉车工：1、行车工：2) -->
					<isNotEmpty prepend=" , " property="status">
						STATUS = #status#
					</isNotEmpty>
					<isNotEmpty prepend=" , " property="perName">
						PER_NAME = #perName#
					</isNotEmpty>
						WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>