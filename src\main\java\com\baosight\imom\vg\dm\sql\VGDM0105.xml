<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0105">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            DEVICE_CODE = #deviceCode#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0105">
        SELECT
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        OVERHAUL_CYCLE_TYPE as "overhaulCycleType",  <!-- 检修周期类型 -->
        CYCLE_DAY as "cycleDay",  <!-- 天数 -->
        CYCLE_NUM as "cycleNum",  <!-- 加工量 -->
        OVERHAUL_TOOL as "overhaulTool",  <!-- 检修工具 -->
        OVERHAUL_STEP as "overhaulStep",  <!-- 检修步骤 -->
        SECURITY_MEASURES as "securityMeasures",  <!-- 安全措施 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0105 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0105 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryForAlarm" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT 
        T2.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        T2.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        T2.DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        T1.DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        T1.CYCLE_DAY as "cycleDay",  <!-- 检修周期天数 -->
        IFNULL(T8.DAYS,999) as "days",  <!-- 最后检修日期距离当前日期天数 -->
        IFNULL(T8.LAST_DAY,'') as "lastDate",  <!-- 最后检修日期 -->
        T1.CYCLE_DAY - IFNULL(T8.DAYS,999) as "diffDays"  <!-- 差异天数 -->
        FROM ${mevgSchema}.TVGDM0105 T1
        LEFT JOIN ${mevgSchema}.TVGDM0102 T2
        ON T1.DEVICE_CODE = T2.DEVICE_CODE
        LEFT JOIN (SELECT DEVICE_CODE,
        MAX(OVERHAUL_END_DATE) AS LAST_DAY,
        DATEDIFF(CURDATE(), SUBSTR(MAX(OVERHAUL_END_DATE), 1, 10)) AS DAYS
        FROM ${mevgSchema}.TVGDM0801 
        WHERE OVERHAUL_PLAN_STATUS !='10'
        AND DEL_FLAG = '0'
        AND SEG_NO = #segNo#
        GROUP BY DEVICE_CODE) T8
        ON T1.DEVICE_CODE=T8.DEVICE_CODE
        WHERE T1.OVERHAUL_CYCLE_TYPE = '10'
        AND T1.SEG_NO = #segNo#
        AND T1.DEL_FLAG = '0'
        AND T1.CYCLE_DAY &gt; 0
        AND T1.CYCLE_DAY - IFNULL(T8.DAYS,999) &gt; 0
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0105 (DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        OVERHAUL_CYCLE_TYPE,  <!-- 检修周期类型 -->
        CYCLE_DAY,  <!-- 天数 -->
        CYCLE_NUM,  <!-- 加工量 -->
        OVERHAUL_TOOL,  <!-- 检修工具 -->
        OVERHAUL_STEP,  <!-- 检修步骤 -->
        SECURITY_MEASURES,  <!-- 安全措施 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#deviceCode#, #deviceName#, #overhaulCycleType#, #cycleDay#, #cycleNum#, #overhaulTool#, #overhaulStep#,
        #securityMeasures#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0105 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0105
        SET
        OVERHAUL_CYCLE_TYPE = #overhaulCycleType#,   <!-- 检修周期类型 -->
        CYCLE_DAY = #cycleDay#,   <!-- 天数 -->
        CYCLE_NUM = #cycleNum#,   <!-- 加工量 -->
        OVERHAUL_TOOL = #overhaulTool#,   <!-- 检修工具 -->
        OVERHAUL_STEP = #overhaulStep#,   <!-- 检修步骤 -->
        SECURITY_MEASURES = #securityMeasures#,   <!-- 安全措施 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>