package com.baosight.imom.li.rl.service;

import com.baosight.imom.li.rl.dao.LIRL0304;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/***
 * 装卸点编码公共下拉框
 */
public class ServiceLIRL0003 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0304().eiMetadata);
        return inInfo;
    }

}
