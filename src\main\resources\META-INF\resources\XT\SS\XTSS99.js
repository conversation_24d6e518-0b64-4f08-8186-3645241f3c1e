$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();

    // 查询按钮
    $("#QUERY").on("click", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }else{
            resultGrid.dataSource.page(1);
        }
    });

    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) {
                // 定义BizModel
                BizModel = kendo.data.Model.define({
                    id: grid.dataSource.options.schema.model.id,
                    fields: grid.dataSource.options.schema.model.fields
                });
                /*粘粘版导入*/
                $("#CLIPBOARD").on("click", function (e) {
                    var content = "";
                    if (IPLAT.Browser.isIE) {
                        content = window.clipboardData.getData("Text");
                        postHandle(content);
                    } else {
                        clipWindow.center().open();
                        handleFun = postHandle;
                    }
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "XTSS99", "submit", true, null, null, false);
                });
            },
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    if (IPLAT.isBlankString(item["segNo"])) {
                        item["segNo"] = $("#inqu_status-0-segNo").val();
                        item["segName"] = $("#inqu_status-0-segName").val()
                    }
                });
            },
        }
    };


    /**
     * 处理粘贴的内容并添加到表格中
     * @param {string} content - 粘贴的内容
     * @returns {void}
     */
    function postHandle(content) {
        if (!content?.trim()) {
            NotificationUtil("粘贴内容不能为空", "warning");
            return;
        }
        try {
            const dataItems = resultGrid.getDataItems();
            if (dataItems?.length) {
                resultGrid.unCheckAllRows();
            }
            const lines = content.split("\r\n").filter((line) => line.trim());
            const validRows = lines.map((line) => getRow(line)).filter((row) => row !== false);
            if (validRows.length > 0) {
                resultGrid.addRows(validRows, false, true);
                // NotificationUtil(`成功导入 ${validRows.length} 条数据`, "success");
            }
        } catch (error) {
            // console.error("处理粘贴内容时出错:", error);
            NotificationUtil("处理粘贴内容时出错", "error");
        }
    }

    var BizModel;
    /**
     * 解析粘贴的行数据并创建新的数据行对象
     * @param {string} line 粘贴的单行数据
     * @returns {Object|boolean} 返回创建的数据行对象,或在数据无效时返回false
     */
    function getRow(line) {
        if (!line?.trim()) {
            return false;
        }
        const columns = line.split("\t");
        if (!columns[0]?.trim()) {
            return false;
        }
        try {
            const row = {
                segNo: columns[0],
                segName: columns[1],
                userId: columns[2],
                userName: columns[3],
                mobile: columns[4]
            };

            const modelInstance = new BizModel(row);
            modelInstance.dirty = true;
            return modelInstance;
        } catch (error) {
            NotificationUtil(`处理行数据失败: ${error.message}`, "error");
            return false;
        }
    }

    // 优化粘贴事件处理
    document.addEventListener("paste", function (evt) {
        if (evt.target.id !== "clipContent") {
            return;
        }
        evt.preventDefault();
        const clipdata = evt.clipboardData || window.clipboardData;
        const content = clipdata.getData("text/plain");
        clipWindow.close();
        handleFun?.(content);
    });


});