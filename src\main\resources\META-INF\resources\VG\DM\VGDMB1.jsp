<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-startDate" required="true"
                           endName="inqu_status-0-endDate" readonly="true"
                           startCname="开始日期" endCname="结束日期"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
            <EF:EFSelect ename="inqu_status-0-dataType" required="true" cname="报表类型" colWidth="3">
                <EF:EFOption label="未选择" value=""/>
                <EF:EFOption label="点检完成率(明细)" value="10"/>
                <EF:EFOption label="点检完成率(汇总)" value="20"/>
<%--                <EF:EFOption label="故障率" value="30"/>--%>
                <EF:EFOption label="标准工时" value="40"/>
                <EF:EFOption label="辅助作业时间" value="50"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>