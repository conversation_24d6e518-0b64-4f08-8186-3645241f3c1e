package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS0102;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 厂区厂房管理
 */
public class ServiceLIDS0102 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0102().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS0102.QUERY, new LIDS0102());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增
     *
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可新增!");
                    }
                    //根据厂区厂房代码匹配，同一厂区下不允许有多个相同的厂房
                    Map queryMap = new HashMap();
                    String factoryArea = MapUtils.getString(itemMap, "factoryArea");
                    String factoryBuilding = MapUtils.getString(itemMap, "factoryBuilding");
                    queryMap.put("segNo", segNo);
                    queryMap.put("factoryArea", factoryArea);
                    queryMap.put("factoryBuilding", factoryBuilding);
                    int count = super.count(LIDS0102.COUNT, queryMap);
                    if (count > 0) {
                        throw new PlatException("厂区代码:" + factoryArea + "，厂房代码:" + factoryBuilding + ",已存在，不可重复维护!");
                    }
                    //状态为新增
                    itemMap.put("status", "10");
                    //设置创建人信息
                    RecordUtils.setCreator(itemMap);
                });
                outInfo = super.insert(inInfo, LIDS0102.INSERT);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(itemMap -> {
                    String segNo = MapUtils.getString(itemMap, "segNo");
                    if (StringUtils.isBlank(segNo)) {
                        throw new PlatException("传入系统账套为空，不可修改!");
                    }
                    //查询厂区状态，判断非新增状态数据不可修改
                    Map queryMap = new HashMap();
                    String factoryArea = MapUtils.getString(itemMap, "factoryArea");
                    String factoryBuilding = MapUtils.getString(itemMap, "factoryBuilding");
                    String uuid = MapUtils.getString(itemMap, "uuid");
                    queryMap.put("segNo", segNo);
                    queryMap.put("status", "10");
                    queryMap.put("uuid", uuid);
                    int count = super.count(LIDS0102.COUNT_UUID, queryMap);
                    if (count < 1) {
                        throw new PlatException("厂区代码:" + factoryArea + "，厂房代码:" + factoryBuilding +",勾选存在状态不为新增的数据，不可修改!");
                    }
                    //判断修改后厂区厂房是否已存在
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("factoryArea", factoryArea);
                    queryMap.put("factoryBuilding", factoryBuilding);
                    count = super.count(LIDS0102.COUNT, queryMap);
                    if (count > 0) {
                        throw new PlatException("厂区:" + factoryArea + ",厂房:" + factoryBuilding + ",已存在，不可修改!");
                    }
                    //设置修改人信息
                    RecordUtils.setRevisor(itemMap);
                });
                outInfo = super.update(inInfo, LIDS0102.UPDATE);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 删除
     *
     * @param inInfo
     * @return
     */
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可删除!");
                }
                //查询区域代码状态，判断非新增状态数据不可删除
                Map queryMap = new HashMap();
                String factoryArea = MapUtils.getString(itemMap, "factoryArea");
                String factoryBuilding = MapUtils.getString(itemMap, "factoryBuilding");
                String uuid = MapUtils.getString(itemMap, "uuid");
                queryMap.put("segNo", segNo);
                queryMap.put("status", "10");
                queryMap.put("uuid", uuid);
                int count = super.count(LIDS0102.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("厂区:" + factoryArea + ",厂房:" + factoryBuilding + ",非新增状态，不可删除!");
                }
                //状态变更为撤销
                itemMap.put("status", "00");
                itemMap.put("delFlag", "1");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0102.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行删除操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo validateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可启用!");
                }
                //查询区域代码状态，判断非新增状态数据不可生效
                Map queryMap = new HashMap();
                String factoryArea = MapUtils.getString(itemMap, "factoryArea");
                String factoryBuilding = MapUtils.getString(itemMap, "factoryBuilding");
                String uuid = MapUtils.getString(itemMap, "uuid");
                queryMap.put("segNo", segNo);
                queryMap.put("status", "10");
                queryMap.put("uuid", uuid);
                int count = super.count(LIDS0102.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("厂区:" + factoryArea + ",厂房:" + factoryBuilding + ",非新增状态，不可生效!");
                }
                //状态变更为生效
                itemMap.put("status", "20");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0102.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 反生效
     *
     * @param inInfo
     * @return
     */
    public EiInfo deValidateCross(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            resultList.forEach(itemMap -> {
                String segNo = MapUtils.getString(itemMap, "segNo");
                if (StringUtils.isBlank(segNo)) {
                    throw new PlatException("传入系统账套为空，不可反生效!");
                }
                //查询区域代码状态，判断非启用状态u
                Map queryMap = new HashMap();
                String factoryArea = MapUtils.getString(itemMap, "factoryArea");
                String factoryBuilding = MapUtils.getString(itemMap, "factoryBuilding");
                String uuid = MapUtils.getString(itemMap, "uuid");
                queryMap.put("segNo", segNo);
                queryMap.put("status", "20");
                queryMap.put("uuid", uuid);
                int count = super.count(LIDS0102.COUNT_UUID, queryMap);
                if (count < 1) {
                    throw new PlatException("厂区:" + factoryArea + ",厂房:" + factoryBuilding + ",非生效状态，不可反生效!");
                }
                //状态变更为新增
                itemMap.put("status", "10");
                //设置修改人信息
                RecordUtils.setRevisor(itemMap);
            });
            outInfo = super.update(inInfo, LIDS0102.UPDATE_STATUS);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("对" + resultList.size() + "条记录执行反生效操作成功!");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
