package com.baosight.imom;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;

@Component
public class CommandLineRunnerImpl implements CommandLineRunner {
    @Override
    public void run(String... args) throws Exception {
        System.out.println("CommandLineRunnerImpl.run");
        // 自动开启报警监听
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceName, "VGDM06");
        inInfo.set(EiConstant.methodName, "autoStartClient");
        EiInfo tempInfo = XLocalManager.call(inInfo);
        if (tempInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            System.out.println("autoStartClient自动开始报警监听失败" + tempInfo.getMsg());
        }
    }
}
