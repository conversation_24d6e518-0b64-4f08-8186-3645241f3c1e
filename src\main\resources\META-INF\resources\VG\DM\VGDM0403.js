$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 汇总查询按钮
    $("#QUERY2").on("click", function (e) {
        IMOMUtil.submitGridsData("result", "VGDM0403", "query2", false,
            function (ei) {
                resultGrid.setEiInfo(ei);
            },
            null, true);
    });
    // 点检日期默认当天查询条件
    const now = new Date();
    $("#inqu_status-0-checkStartDate").val(DateUtils.format(now, "yyyy-MM-dd"));
    $("#inqu_status-0-checkEndDate").val(DateUtils.format(now, "yyyy-MM-dd"));
    // grid配置
    IPLATUI.EFGrid = {
        // 点检计划子项
        "result": {
            // 隐藏分页栏
            pageable: false,
            columns: [
                {
                    field: "totalNum",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "notNum",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "doNum",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "eNum",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "doRate",
                    valueType: "N",//小计设置
                    type: "N",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        const doRate = Number(item["doNum"]) / Number(item["totalNum"]);
                        if (doRate === 0) {
                            return "0%";
                        }
                        return (doRate * 100).toFixed() + "%";
                    }
                },
                {
                    field: "eRate",
                    valueType: "N",//小计设置
                    type: "N",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        if (Number(item["doNum"]) === 0) {
                            return "0%";
                        }
                        const eRate = Number(item["eNum"]) / Number(item["doNum"]);
                        return (eRate * 100).toFixed() + "%";
                    }
                }
            ],
            dataBound: function (e) {
                // 小计行
                var trs = e.sender.element.find(".i-sum-page");
                // 修改表头
                $(trs[0]).text("合计");
                // 总数量
                let totalNum = Number($(trs[1]).text());
                // 完成数量
                let doNum = Number($(trs[3]).text());
                // 完成率
                let doRateTr = $(trs[4]);
                // 计算并保留2位小数
                doRateTr.text((doNum / totalNum * 100).toFixed() + "%");
                // 异常数量
                let eNum = Number($(trs[5]).text());
                // 异常率
                let eRateTr = $(trs[6]);
                eRateTr.text((eNum / doNum * 100).toFixed() + "%");
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();
});
