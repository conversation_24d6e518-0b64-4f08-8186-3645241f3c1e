<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3" value=" " disabled="true"/>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput type="text" ename="inqu_status-0-handPointId" cname="装卸点代码" colWidth="3"
                        ratio="4:8"/>
            <EF:EFInput type="text" ename="inqu_status-0-handPointName" cname="装卸点名称" colWidth="3"
                        ratio="4:8"/>
            <EF:EFSelect ename="inqu_status-0-factoryArea" cname="厂区" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="西安宝钢" value="XABG"/>
                <EF:EFOption label="安徽宝钢" value="AHBG"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P028"/>
            </EF:EFSelect>

            <EF:EFSelect ename="inqu_status-0-businessType" cname="业务类型" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P010"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-loadFlag" cname="装货标记" colWidth="3" disabled="true" type="hidden"/>
        </div>
    </EF:EFRegion>
    <div  id= "BTN3" class="k-window-save k-popup-save" style="position: relative; left: 10px; display: none;" >
        <EF:EFButton cname="确认" ename="userAttrBtn3" layout="1" class="k-button"  />
    </div>
    <div id="result">
        <EF:EFRegion id="result" title="装卸点信息">

            <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" isFloat="true" personal="true"
                       serviceName="LIRL0304" queryMethod="query">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center"/>
                <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                <EF:EFColumn ename="handPointId" cname="装卸点代码" align="center" enable="false"/>
                <EF:EFColumn ename="handPointName" cname="装卸点名称" align="center"/>
                <EF:EFColumn ename="loadingChannelId" cname="装卸点通道代码" align="center" enable="false"/>
                <EF:EFColumn ename="loadingChannelName" cname="装卸点通道名称" align="center" enable="false"/>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="center" enable="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" enable="false"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                    <EF:EFCodeOption codeName="P028"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="handOrder" cname="装卸顺序" align="center" enable="false">
                    <EF:EFOption label="有序" value="10"/>
                    <EF:EFOption label="无序" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="orderNumber" cname="顺序号" align="center" enable="false"/>
                <EF:EFColumn ename="vehicleNumer" cname="容纳车辆数" align="center" enable="false"/>
                <EF:EFColumn ename="standardCapacity" cname="标准容量（吨）" align="center" enable="false"/>
                <EF:EFColumn ename="closeReason" cname="停用原因" align="center" enable="false"/>
                <EF:EFColumn ename="loadFlag" cname="装货标记" align="center" enable="false"/>
                <EF:EFColumn ename="unloadFlag" cname="卸货标记" align="center" enable="false"/>
                <EF:EFComboColumn ename="businessType" cname="业务类型" align="center" enable="false">
                    <EF:EFCodeOption codeName="P010"/>
                </EF:EFComboColumn>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>

    <%--厂区厂房弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factory" width="90%" height="60%"/>
</EF:EFPage>
