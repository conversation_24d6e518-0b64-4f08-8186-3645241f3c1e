package com.baosight.imom.xt.ss.service;

import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.xt.ss.domain.XTSS07;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * 用户信息查询弹窗后台
 *
 * <AUTHOR> 郁在杰
 * @Description :
 * @Date : 2024/12/13
 * @Version : 1.0
 */
public class ServiceXTSS07 extends ServiceBase {

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS07().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
        if (StrUtil.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(MessageCodeConstant.errorMessage.MSG_ERROR_EMPTY_UNIT);
            return inInfo;
        }
        return super.query(inInfo, XTSS07.QUERY, new XTSS07());
    }

}
