<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0104">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID like concat('%',#relevanceId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equalId">
            RELEVANCE_ID = #equalId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceType">
            RELEVANCE_TYPE = #relevanceType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="eventStartTime">
            substr(EVENT_START_TIME,1,8) &gt;= replace(#eventStartTime#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="eventEndTime">
            replace(#eventEndTime#,'-','') &gt;= substr(EVENT_START_TIME,1,8)
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0104">
        SELECT
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        RELEVANCE_TYPE as "relevanceType",  <!-- 关联类型 -->
        RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        EVENT_START_TIME as "eventStartTime",  <!-- 事件开始时间 -->
        EVENT_END_TIME as "eventEndTime",  <!-- 事件结束时间 -->
        EVENT_CONTENT as "eventContent",  <!-- 事件内容 -->
        HANDLE_CONTENT as "handleContent",  <!-- 处理内容 -->
        HANDLE_PERSON as "handlePerson",  <!-- 处理人 -->
        HANDLE_PERSON_NAME as "handlePersonName",  <!-- 处理人姓名 -->
        HANDLE_TIME as "handleTime",  <!-- 处理时间 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0104 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                EVENT_END_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0104 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0104 (DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        RELEVANCE_TYPE,  <!-- 关联类型 -->
        RELEVANCE_ID,  <!-- 关联ID -->
        EVENT_START_TIME,  <!-- 事件开始时间 -->
        EVENT_END_TIME,  <!-- 事件结束时间 -->
        EVENT_CONTENT,  <!-- 事件内容 -->
        HANDLE_CONTENT,  <!-- 处理内容 -->
        HANDLE_PERSON,  <!-- 处理人 -->
        HANDLE_PERSON_NAME,  <!-- 处理人姓名 -->
        HANDLE_TIME,  <!-- 处理时间 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#deviceCode#, #deviceName#, #eArchivesNo#, #equipmentName#, #relevanceType#, #relevanceId#,
        #eventStartTime#, #eventEndTime#, #eventContent#, #handleContent#, #handlePerson#, #handlePersonName#,
        #handleTime#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0104 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0104
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>