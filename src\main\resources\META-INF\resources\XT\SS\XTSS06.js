$(function () {
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [{
                field: "warehouseCode",
                readonly: true,
                hidden: false,
                locked: false,
                title: "仓库代码",
                editor: function (container, param) {
                    // 设置产生弹框model
                    if (container.hasClass("fake-edit")) {
                        container.removeClass("fake-edit");
                    } else {
                        editorModel = param.model;
                        IPLAT.Popup.popupContainer({
                            containerId: "warehouseCodeInfo",
                            textElement: $(container),
                            width: 600,
                            center: true,
                            title: "仓库信息"
                        })
                    }
                }
            }],
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {

            },

            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },

            onDelete: function (e) {
                let rows = e.sender.getCheckedRows();
                for (let i = 0; i < rows.length; i++) {
                    if (rows[i].status !== "10") {
                        NotificationUtil({msg: "勾选数据状态不为新增,不可删除!"}, "error");
                        e.preventDefault();
                        return;
                    }
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //仓库信息
    IMOMUtil.windowTemplate({
        windowId: "warehouseCodeInfo", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-windowId").val("warehouseCodeInfo");

        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                resultGrid.setCellValue(editorModel, "warehouseCode", rows[0].stockCode);
                resultGrid.setCellValue(editorModel, "warehouseName", rows[0].stockName);

            }
        }
    });
})