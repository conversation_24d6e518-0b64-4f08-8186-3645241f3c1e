$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //编辑列名
    let editorField;
    //编辑行
    let editorModel;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "unpackAreaId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "拆包区编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unpackAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "拆包区查询"
                            })
                        }
                    }
                }, {
                    field: "unpackAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "拆包区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unpackAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "拆包区查询"
                            })
                        }
                    }
                },
                {
                    field: "crossArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "跨区编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "跨区编码查询"
                            })
                        }
                    }
                },
                {
                    field: "crossAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "跨区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "跨区编码查询"
                            })
                        }
                    }
                },{
                    field: "priorityLoadMaterialArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "优先上料区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组上料区查询"
                            })
                        }
                    }
                },
                {
                    field: "materialLoadingArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组上料区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组上料区查询"
                            })
                        }
                    }
                }, {
                    field: "materialLoadingAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组上料区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组上料区查询"
                            })
                        }
                    }
                }, {
                    field: "materialUnloadingArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组下料区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组下料区查询"
                            })
                        }
                    }
                }, {
                    field: "materialUnloadingAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组下料区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组下料区查询"
                            })
                        }
                    }
                }, {
                    field: "rejectionArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组退料区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "退料区查询"
                            })
                        }
                    }
                }, {
                    field: "rejectionAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组退料区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "退料区查询"
                            })
                        }
                    }
                }, {
                    field: "mouldCart",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组模具台车代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组模具台车查询"
                            })
                        }
                    }
                }, {
                    field: "mouldCartName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机组模具台车名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorField = param.field;
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "机组模具台车查询"
                            })
                        }
                    }
                },
                {
                    field: "factoryArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂区代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房代码查询"
                            })
                        }
                    }
                },
                {
                    field: "factoryBuilding",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂房代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房代码查询"
                            })
                        }
                    }
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
                if (!e.model.isNew()) {
                    /* /!**
                      * 可以修改的字段有：跨区编码、跨区名称、拆包区编码、拆包区名称
                      *!/
                     if (e.field !== "crossArea" && e.field !== "crossAreaName" && e.field !== "unpackAreaId" && e.field !== "unpackAreaName"
                     || e.field !== "materialLoadingArea" && e.field !== "materialLoadingAreaName" && e.field !== "materialUnloadingArea" && e.field !== "materialUnloadingAreaName"
                         || e.field !== "rejectionArea" && e.field !== "rejectionAreaName" && e.field !== "mouldCart" && e.field !== "mouldCartName") {
                         e.preventDefault();
                         return;
                     }*/
                }
            },
            afterEdit: function (e) {
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //拆包区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "unpackAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                //校验勾选厂区，厂房，跨区必须一致(取第一条的数据)
                let factoryArea = rows[0].factoryArea;
                let factoryBuilding = rows[0].factoryBuilding;
                let crossArea = rows[0].crossArea;
                for (let i = 1; i < rows.length; i++) {
                    if (rows[i].factoryArea !== factoryArea || rows[i].factoryBuilding !== factoryBuilding
                        || rows[i].crossArea !== crossArea) {
                        NotificationUtil({msg: "所勾选的拆包区信息中，厂区、厂房、跨区必须一致!"}, "error");
                        return;
                    }
                }
                //如果勾选多条，跨区信息按,号分隔
                let unpackAreaId = rows.map(c => c.unpackAreaId).join(`,`);
                let unpackAreaName = rows.map(c => c.unpackAreaName).join(`,`);
                resultGrid.setCellValue(editorModel, "unpackAreaId", unpackAreaId);
                resultGrid.setCellValue(editorModel, "unpackAreaName", unpackAreaName);
            }
        }
    });


    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "crossArea", rows[0].crossArea);
                resultGrid.setCellValue(editorModel, "crossAreaName", rows[0].crossAreaName);
            }
        }
    });


    //厂区区域管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossingChannelsInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
            //类型为上料区
            if (editorField === "materialLoadingArea" || editorField === "materialLoadingAreaName" || editorField === "priorityLoadMaterialArea") {
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").value("60");
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").enable(false);
            } else if (editorField === "materialUnloadingArea" || editorField === "materialUnloadingAreaName") {
                //下料区
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").value("61");
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").enable(false);
            } else if (editorField === "rejectionArea" || editorField === "rejectionAreaName") {
                //退料区
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").value("63");
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").enable(false);
            } else {
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").value("65");
                iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").enable(false);
            }

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                //如果勾选多条，区域信息按,号分隔
                let areaCode = rows.map(c =>c.areaCode).join(`,`);
                let areaName = rows.map(c =>c.areaName).join(`,`);

                //类型为上料区
                if (editorField === "materialLoadingArea" || editorField === "materialLoadingAreaName") {
                    resultGrid.setCellValue(editorModel, "materialLoadingArea", areaCode);
                    resultGrid.setCellValue(editorModel, "materialLoadingAreaName", areaName);
                }else if(editorField === "priorityLoadMaterialArea"){
                    resultGrid.setCellValue(editorModel, "priorityLoadMaterialArea", areaCode);
                    resultGrid.setCellValue(editorModel, "priorityLoadMaterialAreaName", areaName);
                } else if (editorField === "materialUnloadingArea" || editorField === "materialUnloadingAreaName") {
                    //下料区
                    resultGrid.setCellValue(editorModel, "materialUnloadingArea", areaCode);
                    resultGrid.setCellValue(editorModel, "materialUnloadingAreaName", areaName);
                } else if (editorField === "rejectionArea" || editorField === "rejectionAreaName") {
                    //退料区
                    resultGrid.setCellValue(editorModel, "rejectionArea", areaCode);
                    resultGrid.setCellValue(editorModel, "rejectionAreaName", areaName);
                } else {
                    //机组模具台车
                    resultGrid.setCellValue(editorModel, "mouldCart", areaCode);
                    resultGrid.setCellValue(editorModel, "mouldCartName", areaName);
                }
            }
        }
    });

    //厂区厂房管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "factoryArea", rows[0].factoryArea);
                resultGrid.setCellValue(editorModel, "factoryAreaName", rows[0].factoryAreaName);
                resultGrid.setCellValue(editorModel, "factoryBuilding", rows[0].factoryBuilding);
                resultGrid.setCellValue(editorModel, "factoryBuildingName", rows[0].factoryBuildingName);
            }
        }
    });

})