<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="账套选择">

        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
        <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                         readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                         center="true" required="true">
        </EF:EFPopupInput>
        <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true" required="true"/>

    </EF:EFRegion>

    <EF:EFRegion id="result" title="用户准入申请">
        <EF:EFGrid blockId="result" autoDraw="no" sort="all">
            <EF:EFColumn ename="segNo" required="true" cname="业务单元代码" align="center" enable="false"/>
            <EF:EFComboColumn ename="segName" enable="false" cname="业务单元简称" align="center" sort="flase"/>
            <EF:EFColumn ename="userId" cname="工号" />
            <EF:EFColumn ename="userName" cname="用户"/>
            <EF:EFColumn ename="mobile" cname="联系方式"/>
        </EF:EFGrid>
    </EF:EFRegion>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow id="clip" title="粘贴板导入" height="40%" width="25%">
        <textarea id="clipContent" name="clipContent" class="json_input" rows="16" style="width: 99%; height: 95%;"
                  spellcheck="false" placeholder="请粘贴"></textarea>
    </EF:EFWindow>
</EF:EFPage>

