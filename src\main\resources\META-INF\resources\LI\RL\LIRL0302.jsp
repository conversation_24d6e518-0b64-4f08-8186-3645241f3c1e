<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd"
                           startCname="创建日期(始)" endCname="创建日期(止)"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3" defaultValue="10"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P009"/>
            </EF:EFSelect>
            <EF:EFDateSpan startName="inqu_status-0-reservationDateStart"
                           endName="inqu_status-0-reservationTimeEnd"
                           startCname="起始预约日期" endCname="截止预约日期"
                           parseFormats="['yyyy-MM-ddHH:mm']"
                           colWidth="3" ratio="3:3"
                           format="yyyy-MM-dd" role="date"/>
            <EF:EFSelect ename="inqu_status-0-reservationDateRange" cname="预约范围" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="只看本日预约" value="10"/>
                <EF:EFOption label="只看明日预约" value="20"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFInput type="textarea" ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3" placeholder="模糊查询"/>
            <EF:EFInput ename="inqu_status-0-voucherNum" cname="提单号" colWidth="3" placeholder="模糊查询"/>
            <EF:EFSelect ename="inqu_status-0-businessType" cname="业务类型" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P007"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0302" queryMethod="query"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="segNo" cname="业务单元代码" align="center"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="unitCode" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="checkId" cname="车辆登记流水号" align="center" enable="false"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                <EF:EFCodeOption codeName="P009"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="handType" cname="装卸类型" align="center" enable="false" hidden="true">
                <EF:EFCodeOption codeName="P046"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="businessType" cname="业务类型" align="center" required="true" enable="false">
                <EF:EFCodeOption codeName="P007"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="customerId" cname="承运商/客户代码" align="center" enable="false" hidden="true"/>
            <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" enable="false"/>
            <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false"/>
            <EF:EFColumn ename="driverName" cname="司机姓名" align="center" enable="false"/>
            <EF:EFColumn ename="telNum" cname="司机手机号" align="center" enable="false"/>
            <EF:EFColumn ename="idCard" cname="司机身份证号" align="center" enable="false"  width="150"/>
            <EF:EFColumn ename="reservationDate" cname="预约日期" align="center" enable="false"/>
            <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>
            <EF:EFColumn ename="checkDate" cname="进厂登记时间" align="center" enable="false" width="150"
                         editType="datetime" displayType="datetime"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"/>
            <EF:EFColumn ename="carTraceNo" cname="车辆跟踪号" align="center" enable="false"/>
            <EF:EFComboColumn ename="carTraceStatus" cname="车辆跟踪单状态" align="center"  enable="false" sort="false">
                <EF:EFCodeOption codeName="P008"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="nextTarget" cname="下一目标" align="center">
                <EF:EFOption label="下一装卸点" value="10"/>
                <EF:EFOption label="离厂" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="reservationNumber" cname="车辆预约单号" align="center" enable="false"/>
            <EF:EFComboColumn ename="checkSource" cname="数据来源" align="center" required="true" enable="false">
                <EF:EFOption label="一体机" value="10"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="voucherNum" cname="提单号" align="center" enable="false"/>
            <EF:EFColumn ename="factoryArea" cname="厂区代码" align="center" enable="false"/>
            <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" enable="false"/>
            <EF:EFColumn ename="remark" cname="备注" required="false" align="left" editType="textarea" maxlength="500" enable="false"/>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="UUID" align="center" hidden="true"/>
            <EF:EFColumn ename="lateEarlyFlag" cname="迟到早到标记" align="center" hidden="true"/>
            <EF:EFColumn ename="callDate" cname="叫号日期" align="center" hidden="true"/>
            <EF:EFColumn ename="callTime" cname="叫号时段" align="center" hidden="true"/>
            <EF:EFColumn ename="factoryArea" cname="厂区" align="center" hidden="true"/>
            <EF:EFColumn ename="archiveFlag" cname="归档标记" align="center" hidden="true"/>
            <EF:EFColumn ename="sysRemark" cname="系统备注" align="center" hidden="true"/>
            <EF:EFColumn ename="tenantId" cname="租户ID" align="center" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>

    <EF:EFWindow id="appointmentSlotInformation" title="预约时段信息" width="60%" height="70%">
        <EF:EFRegion id="inqu2" title="查询条件" hidden="true">
                <EF:EFInput ename="inqu2_status-0-segNo" cname="系统账套" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-handType" cname="装卸类型" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-businessType" cname="业务类型" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-voucherNum" cname="提单号" type="hidden"/>
        </EF:EFRegion>
        <EF:EFRegion id="result2" title="" isFloat="true" width="100%" height="100%">
            <EF:EFGrid blockId="result2" autoDraw="no" serviceName="LIRL0302" queryMethod="queryAppointmentSlot"
                       autoBind="false" isFloat="true" personal="true" sort="all">
                <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>
                <EF:EFColumn ename="num" cname="剩余预约号" enable="false" align="center" sort="flase"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </EF:EFWindow>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
