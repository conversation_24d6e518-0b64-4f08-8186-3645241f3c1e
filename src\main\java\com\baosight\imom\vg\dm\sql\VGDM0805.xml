<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0805">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            UUID != #notUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="overhaulPlanId">
            OVERHAUL_PLAN_ID = #overhaulPlanId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="itemType">
            ITEM_TYPE = #itemType#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0805">
        SELECT
        OVERHAUL_PLAN_ID as "overhaulPlanId",  <!-- 检修计划编号 -->
        ITEM_TYPE as "itemType",  <!-- 项目类型10检修20维修 -->
        SORT_INDEX as "sortIndex",  <!-- 序号 -->
        ITEM_NAME as "itemName",  <!-- 项目名称 -->
        BEGIN_MINUTE as "beginMinute",  <!-- 开始分钟 -->
        END_MINUTE as "endMinute",  <!-- 结束分钟 -->
        RESPONSE_PERSON as "responsePerson",  <!-- 责任人 -->
        ASSIST_PERSON as "assistPerson",  <!-- 协助人 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0805 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                SORT_INDEX asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0805 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0805 (OVERHAUL_PLAN_ID,  <!-- 检修计划编号 -->
        ITEM_TYPE,  <!-- 项目类型10检修20维修 -->
        SORT_INDEX,  <!-- 序号 -->
        ITEM_NAME,  <!-- 项目名称 -->
        BEGIN_MINUTE,  <!-- 开始分钟 -->
        END_MINUTE,  <!-- 结束分钟 -->
        RESPONSE_PERSON,  <!-- 责任人 -->
        ASSIST_PERSON,  <!-- 协助人 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#overhaulPlanId#, #itemType#, #sortIndex#, #itemName#, #beginMinute#, #endMinute#, #responsePerson#,
        #assistPerson#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0805 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0805
        SET
        SORT_INDEX = #sortIndex#,   <!-- 序号 -->
        ITEM_NAME = #itemName#,   <!-- 项目名称 -->
        BEGIN_MINUTE = #beginMinute#,   <!-- 开始分钟 -->
        END_MINUTE = #endMinute#,   <!-- 结束分钟 -->
        RESPONSE_PERSON = #responsePerson#,   <!-- 责任人 -->
        ASSIST_PERSON = #assistPerson#,   <!-- 协助人 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updForDel">
        UPDATE ${mevgSchema}.TVGDM0805
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>
</sqlMap>