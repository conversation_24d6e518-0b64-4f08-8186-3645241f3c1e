$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    // 列表选中的业务单元信息
    var selectUnitInfo = {};
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务单元查询"
                            });
                        }
                    }
                },
                {
                    field: "equipmentName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "设备名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "equipmentInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "设备查询"
                            });
                        }
                    }
                },
                {
                    field: "machineryHandleName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "机械相关处理人姓名",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            fromField = "machineryHandleName";
                            IPLAT.Popup.popupContainer({
                                containerId: "userInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "人员查询"
                            });
                        }
                    }
                },
                {
                    field: "handleUserName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "电气相关处理人姓名",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            fromField = "handleUserName";
                            IPLAT.Popup.popupContainer({
                                containerId: "userInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "人员查询"
                            });
                        }
                    }
                },
                {
                    field: "manageUserName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "管理人姓名",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            fromField = "manageUserName";
                            IPLAT.Popup.popupContainer({
                                containerId: "userInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "人员查询"
                            });
                        }
                    }
                }
            ],
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["uuid"] = " ";
                    item["status"] = "10";
                    if (IPLAT.isBlankString(item["unitCode"])) {
                        item["unitCode"] = unitInfo.unitCode;
                        item["segNo"] = unitInfo.segNo;
                    }
                });
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    // 修改时unitCode(业务单元代码)和equipmentName(设备名称)不可修改
                    if (e.field === "unitCode" || e.field === "equipmentName") {
                        e.preventDefault();
                    }
                    // 删除状态不能修改
                    if (e.model.status === "00") {
                        e.preventDefault();
                    }
                }
            },
            onCheckRow: function (e) {
                if (e.checked === true) {
                    $("#inqu2_status-0-eArchivesNo").val(e.model.eArchivesNo);
                    $("#inqu2_status-0-segNo").val(e.model.segNo);
                    result2Grid.dataSource.page(1);
                } else {
                    $("#inqu2_status-0-eArchivesNo").val("");
                    $("#inqu2_status-0-segNo").val("");
                }
            },
            loadComplete: function (grid) {
                //修改
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0603", "update", true, null, null, false);
                });
                // 启用
                $("#OPEN").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0603", "open", true, null, null, false);
                });
                // 停用
                $("#CLOSE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0603", "close", true, null, null, false);
                });
            }
        },
        "result2": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            loadComplete: function (grid) {
                //修改
                $("#UPDATE2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0603", "update2", true, null, null, false);
                });
                //删除
                $("#DELETE2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0603", "delete2", false,
                        function (ei) {
                            result2Grid.removeRows(result2Grid.getCheckedRows());
                        }, null, false);
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo01",
        notInqu: true,
        afterSelect: function (rows) {
            selectUnitInfo = {};
            if (rows.length > 0) {
                selectUnitInfo = rows[0];
                let rowNums = resultGrid.getCheckedRowsIndex();
                resultGrid.setCellValue(rowNums, "unitCode", rows[0].unitCode);
                resultGrid.setCellValue(rowNums, "segNo", rows[0].segNo);
                resultGrid.setCellValue(rowNums, "eArchivesNo", "");
                resultGrid.setCellValue(rowNums, "equipmentName", "");
                resultGrid.refresh();
            }
        }
    });
    // 列表设备信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            let a = editorModel.unitCode;
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(editorModel.unitCode);
            iframejQuery("#inqu_status-0-segNo").val(editorModel.segNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "eArchivesNo", rows[0].eArchivesNo);
                resultGrid.setCellValue(editorModel, "equipmentName", rows[0].equipmentName);
            }
        }
    });
    // 列表人员信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "userInfo",
        _open: function (e, iframejQuery) {
            let a = editorModel.unitCode;
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            if (selectUnitInfo.segNo) {
                iframejQuery("#inqu_status-0-segNo").val(selectUnitInfo.segNo);
                iframejQuery("#inqu_status-0-segName").val(selectUnitInfo.segName);
            } else {
                iframejQuery("#inqu_status-0-segNo").val(unitInfo.segNo);
                iframejQuery("#inqu_status-0-segName").val(unitInfo.segName);
            }
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                if (fromField === "machineryHandleName") {
                    resultGrid.setCellValue(editorModel, "machineryHandleId", rows[0].loginName);
                    resultGrid.setCellValue(editorModel, "machineryHandleName", rows[0].userName);
                    resultGrid.setCellValue(editorModel, "machineryHandleMobile", rows[0].mobile);
                } else if (fromField === "handleUserName") {
                    resultGrid.setCellValue(editorModel, "handleUserId", rows[0].loginName);
                    resultGrid.setCellValue(editorModel, "handleUserName", rows[0].userName);
                    resultGrid.setCellValue(editorModel, "handleMobile", rows[0].mobile);
                } else if (fromField === "manageUserName") {
                    resultGrid.setCellValue(editorModel, "manageUserId", rows[0].loginName);
                    resultGrid.setCellValue(editorModel, "manageUserName", rows[0].userName);
                    resultGrid.setCellValue(editorModel, "manageMobile", rows[0].mobile);
                }
            }
        }
    });
});
