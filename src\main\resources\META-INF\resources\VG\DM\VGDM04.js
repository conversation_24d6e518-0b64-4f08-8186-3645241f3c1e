$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    // 点检计划查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
    });
    var addFlag = false;
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId === "info-2") {
                    // 只判断非新增和修改按钮跳转
                    if (!addFlag) {
                        const checkedRows = resultGrid.getCheckedRows();
                        const checkRowLength = checkedRows.length;
                        if (checkRowLength !== 1) {
                            NotificationUtil({ msg: "请勾选一条主项信息" }, "error");
                            e.preventDefault();
                        } else {
                            setDetailData(checkedRows[0]);
                            setUpdateStatus();
                        }
                    }
                } else {
                    addFlag = false;
                }
            }
        }
    };
    IPLATUI.EFSelect = {
        //点检性质
        "detail_status-0-spotCheckNature": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                var dataItem = e.dataItem;
                const length = result2Grid.getDataItems().length;
                for (let i = 0; i < length; i++) {
                    result2Grid.setCellValue(i, "spotCheckImplemente", dataItem.valueField);
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        // 主项信息
        "result": {
            onRowDblClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                tab_Strip.select(1);
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT1").on("click", function (e) {
                    addFlag = true;
                    IPLAT.clearNode(document.getElementById("detail"));
                    setInsertStatus();
                    tab_Strip.select(1);
                    $("#detail_status-0-unitCode").val(unitInfo.unitCode);
                    IPLAT.EFSelect.value($("#detail_status-0-segNo"), unitInfo.segNo);
                    // 清空子项
                    result2Grid.removeRows(result2Grid.getDataItems());
                });
                // 修改按钮
                $("#UPDATE1").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({ msg: "操作失败，原因[只能对一行数据进行修改！]" }, "error");
                        return;
                    }
                    const checkPlanStatus = checkedRows[0].checkPlanStatus;
                    if (checkPlanStatus !== "10") {
                        NotificationUtil({ msg: "操作失败，原因[只能新增状态数据进行修改！]" }, "error");
                        return;
                    }
                    setDetailData(checkedRows[0]);
                    setUpdateStatus();
                    tab_Strip.select(1);
                });
                // 删除按钮
                $("#DELETE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData(
                        "result",
                        "VGDM04",
                        "delete",
                        false,
                        function (ei) {
                            if (ei.getStatus() === 0) {
                                // 清除已删除行
                                var rowsIndex = resultGrid.getCheckedRowsIndex();
                                resultGrid.removeRows(rowsIndex);
                            }
                        },
                        null,
                        false
                    );
                });
                // 确认按钮
                $("#CONFIRM1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM04", "confirm", true, null, null, false);
                });
                // 反确认按钮
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM04", "cancel", true, null, null, false);
                });
                // 点检计划刷新按钮
                $("#GENERATE").on("click", function (e) {
                    const a = $("#inqu_status-0-unitCode").val();
                    if (a == null || IPLAT.isBlankString(a)) {
                        NotificationUtil("操作失败，原因[请先选择业务单元代码]", "error");
                        return;
                    }
                    $("#inqu3_status-0-eArchivesNo").val("");
                    $("#inqu3_status-0-equipmentName").val("");
                    planWindow.open().center();
                });
                // 点检计划同步按钮
                $("#planSynchronization").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "inqu3"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#inqu3");
                    IMOMUtil.submitNode(node, "VGDM04", "generateByEquipment", function (ei) {
                        planWindow.close();
                    });
                });
                // 点检计划延期按钮
                $("#DELAYDATE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    const checkedRows = resultGrid.getCheckedRows();
                    for (let i = 0; i < checkedRows.length; i++) {
                        const checkPlanNature = checkedRows[i].spotCheckNature;
                        if (checkPlanNature !== "20") {
                            NotificationUtil("操作失败，原因[只能延期专业点检]", "error");
                            return;
                        }
                    }
                    planDateWindow.open().center();
                });
                // 点检计划延期保存按钮
                $("#planDelayDate").on("click", function (e) {
                    const checkPlanDate = $("#inqu4_status-0-checkPlanDate").val();
                    if (IPLAT.isBlankString(checkPlanDate)) {
                        NotificationUtil("操作失败，原因[点检日期不能为空]", "error");
                        return;
                    }
                    const delayRemark = $("#inqu4_status-0-delayRemark").val();
                    if (IPLAT.isBlankString(delayRemark)) {
                        NotificationUtil("操作失败，原因[延期理由不能为空]", "error");
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("checkPlanDate", checkPlanDate);
                    eiInfo.set("delayRemark", delayRemark);
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("result"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM04", "delayDate", function (ei) {
                        if (ei.getStatus() === 0) {
                            resultGrid.setEiInfo(ei);
                            planDateWindow.close();
                        }
                    });
                });
            }
        },
        // 子项信息
        "result2": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            columns: [
                {
                    field: "deviceName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "分部设备",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "deviceInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "分部设备查询"
                            });
                        }
                    }
                }
            ],
            beforeAdd: function (e) {
                if (!isStatusNew()) {
                    // 通过业务逻辑判断, 控制是否进行新增
                    e.preventDefault();
                }
            },
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["checkPlanSubStatus"] = "10";
                    item["checkPlanSubId"] = " ";
                    item["planSource"] = "1";
                    item["spotCheckImplemente"] = $("#detail_status-0-spotCheckNature").val();
                });
            },
            beforeEdit: function (e) {
                if (!isStatusNew()) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 子项刷新
                $("#QUERY2").on("click", function (e) {
                    result2Grid.dataSource.page(1);
                });
                // 新增保存按钮（只新增主项信息）
                $("#INSERT3").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM04", "insertMain", function (ei) {
                        setDetailData(ei.getBlock("detail_status").getMappedRows()[0], false);
                    });
                });
                // 修改保存按钮（只修改主项信息）
                $("#UPDATE3").on("click", function (e) {
                    if (!isStatusNew()) {
                        return;
                    }
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM04", "updateMain", function (ei) {
                        setDetailData(ei.getBlock("detail_status").getMappedRows()[0], false);
                    });
                });
                // 子项新增按钮
                $("#INSERT2").on("click", function (e) {
                    if (!isStatusNew()) {
                        return;
                    }
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    const info = new EiInfo();
                    info.setByNode("detail");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IMOMUtil.submitEiInfo(info, "VGDM04", "insertSub", function (ei) {
                        result2Grid.setEiInfo(ei);
                    });
                });
                // 子项修改按钮
                $("#UPDATE2").on("click", function (e) {
                    if (!isStatusNew()) {
                        return;
                    }
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    const info = new EiInfo();
                    info.setByNode("detail");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IMOMUtil.submitEiInfo(info, "VGDM04", "updateSub", function (ei) {
                        result2Grid.setEiInfo(ei);
                    });
                });
                // 子项删除按钮
                $("#DELETE2").on("click", function (e) {
                    if (!isStatusNew()) {
                        return;
                    }
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    const info = new EiInfo();
                    info.setByNode("detail");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IMOMUtil.submitEiInfo(info, "VGDM04", "deleteSub", function (ei) {
                        // 清除已删除行
                        var rowsIndex = result2Grid.getCheckedRowsIndex();
                        result2Grid.removeRows(rowsIndex);
                    });
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 详情区域业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo01",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                const row = rows[0];
                $("#detail_status-0-unitCode").val(row.unitCode);
                IPLAT.EFSelect.value($("#detail_status-0-segNo"), row.segNo);
                $("#detail_status-0-eArchivesNo").val("");
                $("#detail_status-0-equipmentName").val("");
            }
        }
    });
    // 详情区域设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            const a = $("#detail_status-0-unitCode").val();
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(a);
            iframejQuery("#inqu_status-0-segNo").val(IPLAT.EFSelect.value($("#detail_status-0-segNo")));
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#detail_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#detail_status-0-equipmentName").val(rows[0].equipmentName);
            }
        }
    });
    // 列表分部设备信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfo",
        _open: function (e, iframejQuery) {
            const a = $("#detail_status-0-eArchivesNo").val();
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(a);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                result2Grid.setCellValue(editorModel, "deviceCode", rows[0].deviceCode);
                result2Grid.setCellValue(editorModel, "deviceName", rows[0].deviceName);
            }
        }
    });
    // 点检计划生成弹窗区域设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo2",
        _open: function (e, iframejQuery) {
            const a = $("#inqu_status-0-unitCode").val();
            iframejQuery("#inqu_status-0-unitCode").val(a);
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu3_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu3_status-0-equipmentName").val(rows[0].equipmentName);
            }
        }
    });

    /**
     * 设置详情区域新增状态
     */
    function setInsertStatus() {
        // 新增按钮启用
        $("#INSERT3").attr("disabled", false);
        // 修改按钮禁用
        $("#UPDATE3").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", false);
        $("#detail_status-0-equipmentName").attr("disabled", false);
        $("#detail_status-0-checkPlanDate").data("kendoDatePicker").enable(true);
        IPLAT.EFSelect.enable($("#detail_status-0-spotCheckNature"), true);
    }

    /**
     * 设置详情区域修改状态（非新增状态）
     */
    function setUpdateStatus() {
        // 新增按钮禁用
        $("#INSERT3").attr("disabled", true);
        // 修改按钮启用
        $("#UPDATE3").attr("disabled", false);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        var checkPlanStatus = IPLAT.EFSelect.value($("#detail_status-0-checkPlanStatus"));
        // 只有新增状态可修改点检日期和点检性质
        if (checkPlanStatus === "10") {
            $("#detail_status-0-checkPlanDate").data("kendoDatePicker").enable(true);
            IPLAT.EFSelect.enable($("#detail_status-0-spotCheckNature"), true);
        } else {
            $("#detail_status-0-checkPlanDate").data("kendoDatePicker").enable(false);
            IPLAT.EFSelect.enable($("#detail_status-0-spotCheckNature"), false);
        }
    }

    /**
     * 设置详情区域内容
     * @param row grid行数据
     * @param queryDetail 是否查询子项
     */
    function setDetailData(row, queryDetail = true) {
        // 清除原数据
        IPLAT.clearNode(document.getElementById("detail"));
        // 将数据回填到详情页
        IMOMUtil.fillNode(row, "detail");
        // 子项查询
        if (queryDetail) {
            result2Grid.dataSource.page(1);
        }
    }

    /**
     * 主项状态是否为新增
     * @returns {boolean} 是/否
     */
    function isStatusNew() {
        const checkPlanStatus = IPLAT.EFSelect.value($("#detail_status-0-checkPlanStatus"));
        if (checkPlanStatus !== "10") {
            NotificationUtil("操作失败，原因[只能操作状态为新增的数据！]", "error");
            return false;
        }
        return true;
    }
});
