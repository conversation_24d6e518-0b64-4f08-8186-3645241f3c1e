/**
 * Generate time : 2024-08-26 10:22:22
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0202 预约黑名单管理表
 */
public class LIRL0202 extends DaoEPBase {
    public static final String QUERY = "LIRL0202.query";
    public static final String COUNT = "LIRL0202.count";
    public static final String INSERT = "LIRL0202.insert";
    public static final String UPDATE = "LIRL0202.update";
    public static final String UPDATE_FORBIDDEN_STATUS = "LIRL0202.updateForbiddenStatus";
    public static final String DELETE = "LIRL0202.delete";

    private String segNo = " ";        /* 业务单元代代码*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String blacklistFlag = " ";        /* 状态(禁用：1，启用：2)*/
    private String reservationIdentity = " ";		/* 预约身份(包含承运商和客户)*/
    private String customerId = " ";        /* 承运商/客户代码*/
    private String customerName = " ";        /* 承运商/客户名称*/
    private String driverName = " ";        /* 司机姓名*/
    private String driverTel = " ";        /* 司机电话*/
    private String driverIdentity = " ";        /* 司机身份*/
    private String vehicleNo = " ";        /* 车牌号*/
    private String reservationTime = " ";        /* 预约时间*/
    private String enterTime = " ";        /* 登记时间*/
    private String forbiddenTime = " ";        /* 禁用时间*/
    private String enableTime = " ";        /* 解禁时间*/
    private String reservationNumber = " ";        /* 预约单号*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/

    /**
     * the constructor
     */
    public LIRL0202() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("blacklistFlag");
        eiColumn.setDescName("状态(禁用：1，启用：2)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationIdentity");
        eiColumn.setDescName("预约身份(包含承运商和客户)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setDescName("预约时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterTime");
        eiColumn.setDescName("登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("forbiddenTime");
        eiColumn.setDescName("禁用时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enableTime");
        eiColumn.setDescName("解禁时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * get the segNo - 业务单元代代码
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 业务单元代代码
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the segName - 业务单元简称
     *
     * @return the segName
     */
    public String getSegName() {
        return this.segName;
    }

    /**
     * set the segName - 业务单元简称
     */
    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the unitCode - 业务单元代代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the blacklistFlag - 状态(禁用：1，启用：2)
     *
     * @return the blacklistFlag
     */
    public String getBlacklistFlag() {
        return this.blacklistFlag;
    }

    /**
     * set the blacklistFlag - 状态(禁用：1，启用：2)
     */
    public void setBlacklistFlag(String blacklistFlag) {
        this.blacklistFlag = blacklistFlag;
    }
    /**
     * get the reservationIdentity - 预约身份(包含承运商和客户)
     * @return the reservationIdentity
     */
    public String getReservationIdentity() {
        return this.reservationIdentity;
    }

    /**
     * set the reservationIdentity - 预约身份(包含承运商和客户)
     */
    public void setReservationIdentity(String reservationIdentity) {
        this.reservationIdentity = reservationIdentity;
    }

    /**
     * get the customerId - 承运商/客户代码
     *
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 承运商/客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the customerName - 承运商/客户名称
     *
     * @return the customerName
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * set the customerName - 承运商/客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * get the driverName - 司机姓名
     *
     * @return the driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * set the driverName - 司机姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * get the driverTel - 司机电话
     *
     * @return the driverTel
     */
    public String getDriverTel() {
        return this.driverTel;
    }

    /**
     * set the driverTel - 司机电话
     */
    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
    }

    /**
     * get the driverIdentity - 司机身份
     *
     * @return the driverIdentity
     */
    public String getDriverIdentity() {
        return this.driverIdentity;
    }

    /**
     * set the driverIdentity - 司机身份
     */
    public void setDriverIdentity(String driverIdentity) {
        this.driverIdentity = driverIdentity;
    }

    /**
     * get the vehicleNo - 车牌号
     *
     * @return the vehicleNo
     */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
     * set the vehicleNo - 车牌号
     */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    /**
     * get the reservationTime - 预约时间
     *
     * @return the reservationTime
     */
    public String getReservationTime() {
        return this.reservationTime;
    }

    /**
     * set the reservationTime - 预约时间
     */
    public void setReservationTime(String reservationTime) {
        this.reservationTime = reservationTime;
    }

    /**
     * get the enterTime - 登记时间
     *
     * @return the enterTime
     */
    public String getEnterTime() {
        return this.enterTime;
    }

    /**
     * set the enterTime - 登记时间
     */
    public void setEnterTime(String enterTime) {
        this.enterTime = enterTime;
    }

    /**
     * get the forbiddenTime - 禁用时间
     *
     * @return the forbiddenTime
     */
    public String getForbiddenTime() {
        return this.forbiddenTime;
    }

    /**
     * set the forbiddenTime - 禁用时间
     */
    public void setForbiddenTime(String forbiddenTime) {
        this.forbiddenTime = forbiddenTime;
    }

    /**
     * get the enableTime - 解禁时间
     *
     * @return the enableTime
     */
    public String getEnableTime() {
        return this.enableTime;
    }

    /**
     * set the enableTime - 解禁时间
     */
    public void setEnableTime(String enableTime) {
        this.enableTime = enableTime;
    }

    /**
     * get the reservationNumber - 预约单号
     *
     * @return the reservationNumber
     */
    public String getReservationNumber() {
        return this.reservationNumber;
    }

    /**
     * set the reservationNumber - 预约单号
     */
    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     *
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setBlacklistFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("blacklistFlag")), blacklistFlag));
        setReservationIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationIdentity")), reservationIdentity));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
        setDriverTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverTel")), driverTel));
        setDriverIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverIdentity")), driverIdentity));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setReservationTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationTime")), reservationTime));
        setEnterTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("enterTime")), enterTime));
        setForbiddenTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("forbiddenTime")), forbiddenTime));
        setEnableTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("enableTime")), enableTime));
        setReservationNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationNumber")), reservationNumber));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("blacklistFlag", StringUtils.toString(blacklistFlag, eiMetadata.getMeta("blacklistFlag")));
        map.put("reservationIdentity",StringUtils.toString(reservationIdentity, eiMetadata.getMeta("reservationIdentity")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("driverName", StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
        map.put("driverTel", StringUtils.toString(driverTel, eiMetadata.getMeta("driverTel")));
        map.put("driverIdentity", StringUtils.toString(driverIdentity, eiMetadata.getMeta("driverIdentity")));
        map.put("vehicleNo", StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("reservationTime", StringUtils.toString(reservationTime, eiMetadata.getMeta("reservationTime")));
        map.put("enterTime", StringUtils.toString(enterTime, eiMetadata.getMeta("enterTime")));
        map.put("forbiddenTime", StringUtils.toString(forbiddenTime, eiMetadata.getMeta("forbiddenTime")));
        map.put("enableTime", StringUtils.toString(enableTime, eiMetadata.getMeta("enableTime")));
        map.put("reservationNumber", StringUtils.toString(reservationNumber, eiMetadata.getMeta("reservationNumber")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));

        return map;

    }
}