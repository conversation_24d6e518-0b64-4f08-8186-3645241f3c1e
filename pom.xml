﻿<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.iplat4j</groupId>
        <artifactId>iplat4j-boot-starter</artifactId>
        <version>*******.2</version>
    </parent>

    <groupId>com.baosight.imom</groupId>
    <artifactId>imom</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <packaging>war</packaging>
    <name>imom</name>

    <properties>
        <skipTests>true</skipTests>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>widget-admin-sdk</artifactId>
            <version>1.0.8-logfix</version>
        </dependency>
        <!--单元测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>eplat-sdk-share-service</artifactId>
            <version>2.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.baosight.imc</groupId>
            <artifactId>imc-common</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-bpm</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-message</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-job</artifactId>
        </dependency>
<!--                <dependency>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                    <version>2.0.0</version>
                </dependency>-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>eplat-sdk-standard</artifactId>
            <version>2.0.0</version>
            <type>pom</type>
        </dependency>


        <!-- lombok -->
<!--        <dependency>-->
<!--            <groupId>org.projectlombok</groupId>-->
<!--            <artifactId>lombok</artifactId>-->
<!--            <scope>annotationProcessor</scope>-->
<!--        </dependency>-->

        <!-- redis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
        </dependency>

        <!-- 日志 -->
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.13</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>
        <!-- easyExcel 导出 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>

        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.16</version>
        </dependency>

        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.7.17</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>5.8.21</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.java-websocket/Java-WebSocket -->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.5.6</version>
        </dependency>

        <!-- http请求工具包依赖 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!--ihd sdk-->
        <dependency>
            <groupId>com.baosight.hdk</groupId>
            <artifactId>hdk</artifactId>
            <version>3.9.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.7.0</version>
        </dependency>

        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.5</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.baosight.iplat</groupId>-->
<!--            <artifactId>iplat-dm</artifactId>-->
<!--            <version>3.14.0</version>-->
<!--        </dependency>-->

    </dependencies>

    <profiles>
        <profile>
            <id>oauth</id>
            <dependencies>
                <dependency>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-security</artifactId>
                    <version>2.2.6</version>
                </dependency>
            </dependencies>
        </profile>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <!--正式环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <profiles.active>local</profiles.active>
            </properties>
            <activation>
                <!-- 设置默认激活这个配置 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!--用以打war包-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <warSourceExcludes>src/main/resources/META-INF/**</warSourceExcludes>
                    <packagingExcludes>WEB-INF/classes/META-INF/**</packagingExcludes>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/resources</directory>
                            <filtering>false</filtering>
                            <targetPath>/</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <repositories>
        <repository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>baocloud-maven</id>
            <name>宝之云的 Maven 库</name>
            <url>http://nexus.baocloud.cn/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>baocloud-maven</id>
            <name>宝之云的 Maven SNAPSHOT库</name>
            <url>http://nexus.baocloud.cn/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
