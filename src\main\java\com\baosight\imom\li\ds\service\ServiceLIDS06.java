package com.baosight.imom.li.ds.service;


import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * 库位编码弹出框查询
 */
public class ServiceLIDS06 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLIDS06.class);

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS0601().eiMetadata);
        return inInfo;
    }

}
