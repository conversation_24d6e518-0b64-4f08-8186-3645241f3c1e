package com.baosight.imom.xt.ss.domain;

/**
 * 业务账套
 * <AUTHOR>
 * @date 20240823
 */
public class XTSS01 {

    /**
     * 查询值集
     */
    public static final String QUERY_CODE_VALUE = "XTSS01.queryCodeValue";
    /**
     * 查询用户信息
     */
    public static final String QUERY_USER_INFO = "XTSS01.queryUserInfo";
    /**
     * 没有账套号的账套查询所有账套
     */
    public static final String QUERY_SEGNO_LIST_BY_BLANK_SEG_NO = "XTSS01.querySegNoListByBlankSegNo";
    /**
     * 通过用户ID查询账套信息
     */
    public static final String QUERY_SEGNO_BY_USER_ID = "XTSS01.querySegNoByUserId";
    /**
     * 查询用户的数据授权的账套
     */
    public static final String QUERY_SEGNO_LIST_BY_AU = "XTSS01.querySegNoListByAu";


    /**
     * 查询账套视图根据父账套
     */
    public static final String QUERY_SEGNO_LIST_VIEW_BY_F = "XTSS01.querySegnoListViewByF";

    /**
     * 查询账套视图根据自身账套
     */
    public static final String QUERY_SEGNO_LIST_VIEW_BY_OWN = "XTSS01.querySegnoListViewByOwn";


}
