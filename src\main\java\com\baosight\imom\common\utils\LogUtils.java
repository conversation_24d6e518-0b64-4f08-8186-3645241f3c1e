package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.log.xeye.PlatEye;

/**
 * ELK日志工具类
 *
 * <AUTHOR> 郁在杰
 * @Description : 参照ServiceBase中log相关实现
 * @Date : 2024/12/3
 * @Version : 1.0
 */
public class LogUtils {
    /**
     * 正常日志
     *
     * @param invokeInfo 调用信息
     */
    public static void log(String invokeInfo) {
        PlatEye.log("0999", invokeInfo, null, "0", "", null);
    }

    /**
     * 错误日志
     *
     * @param invokeInfo 调用信息
     * @param errorMsg   错误信息
     */
    public static void error(String invokeInfo, String errorMsg) {
        PlatEye.log("0999", invokeInfo, null, "-1", errorMsg, null);
    }
}
