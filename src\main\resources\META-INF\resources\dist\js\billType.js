var vehicle_id = localStorage.getItem("vehicle_id"); //车牌号
var Business_type = ''
var Business_type_name = ''
var segNo = localStorage.getItem("segNo");
var allArr = [];


//页面加载将车牌号与装卸货填充
window.onload = function () {

    let htmlStr = '';
    let arr = [{ value: '10', name: '钢材卸货' }, { value: '20', name: '废料提货' },
    { value: '30', name: '托盘运输' },
    // {value:'40',name: '资材卸货'},
    { value: '50', name: '欧冶提货' },
    { value: '60', name: '其他物品运输' },
    ];
    allArr = arr;
    arr.forEach(d => {
        htmlStr += `<button  id="${d.value}" class="vehicleNoList"" value="${d.value}" onclick="selected('${d.value}');">${d.name}</button>`;
    });
    $("#zhuangHuo").html(htmlStr);
    // 有提单清空
    localStorage.removeItem("bill_id");
}


//  10 : 装货  ；20 ： 卸货
function selected(value) {
    allArr.forEach(a => {
        if (a.value == value) {
            Business_type_name = a.name;
            $("#" + value).css("background-image", 'url("img/typeactive.png")');
            localStorage.setItem("loadingType", a.name.includes('卸货') || a.name.includes('退货') ? '00' : '10');
        } else {
            $("#" + a.value).css("background-image", 'url("img/typebg.png")');
        }
    });

    //选择装卸货时，将装卸货类型保存
    Business_type = value;
    localStorage.setItem("Business_type", value);
    localStorage.setItem("Business_type_name", Business_type_name);
}

//下一步
function nextStep() {
    if (!Business_type) {
        swal("请选择装卸货类型！", "", "warning");
        return false;
    }

    window.location.href = "intoFactory.html";
}