package com.baosight.imom.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class RedisUtil {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 设置键值对并设置过期时间（秒）
    public void set(String key, Object value, long expireTime) {
        redisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.SECONDS);
    }

    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 使用 Hash 结构缓存每个 key 的最新记录
     */
    public void hSet(String key, String field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }

    /**
     * 获取 Hash 中指定 key 和 field 的值
     *
     * @param key   Redis Key（例如："uwbLocRecord:latest"）
     * @param field Hash 的字段名（例如："651143"）
     * @return      对应的值，不存在则返回 null
     */
    public Object hGet(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }

    /**
     * 获取 Hash 中的所有记录
     */
    public Map<Object, Object> hGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }
    /**
     * 使用 List 结构缓存每个 key 的最新记录
     */
    public void rightPush(String key, Object value) {
        redisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 获取 List 结构缓存每个 key 的最新记录
     */
    public List<Object> getList(String key, int start, int end) {
        return redisTemplate.opsForList().range(key, start, end);
    }


    /**
     * 设置过期时间（建议在第一次写入时调用）
     */
    public void expireKey(String key, long timeoutSeconds) {
        redisTemplate.expire(key, timeoutSeconds, TimeUnit.SECONDS);
    }

    /**
     * 判断 key 是否存在
     */
    public boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 设置键值对，并且只在第一次写入时设置过期时间
     *
     * @param key        Redis Key
     * @param value      要存储的值
     * @param expireTime 过期时间（秒）
     * @return           是否是首次写入
     */
    public boolean setWithExpireIfFirstSet(String key, Object value, long expireTime) {
        boolean isNew = !exists(key);
        redisTemplate.opsForValue().set(key, value); // 总是写入值
        if (isNew) {
            redisTemplate.expire(key, expireTime, TimeUnit.SECONDS); // 首次写入才设置过期时间
        }
        return isNew;
    }

}
