package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.WorkFlowConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.util.StringUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * <AUTHOR> yzj
 * @Description : 检修实绩清单页面后台
 * @Date : 2024/8/22
 * @Version : 1.0
 */
public class ServiceVGDM0801 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0801.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0801().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK).addBlockMeta(new VGDM0801().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT2_BLOCK).addBlockMeta(new VGDM0802().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        // 只查询未删除数据
        inInfo.setCell(EiConstant.queryBlock, 0, "delFlag", "0");
        // 查询生效后数据（状态大于等于20）
        inInfo.setCell(EiConstant.queryBlock, 0, "minStatus", MesConstant.Status.K20);
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0801.QUERY, new VGDM0801());
    }

    /**
     * 查询附件信息
     */
    public EiInfo queryFile(EiInfo inInfo) {
        String relevanceId = inInfo.getCellStr(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "relevanceId");
        if (StrUtil.isBlank(relevanceId)) {
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("无记录");
            return inInfo;
        }
        return super.query(inInfo, VGDM0802.QUERY, null, false, new VGDM0802().eiMetadata,
                MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 修改检修实绩信息
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0801 vgdm0801 = new VGDM0801();
            vgdm0801.fromMap(block.getRow(0));
            VGDM0801 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ACTIVE_OR_START_STATUS, MesConstant.Status.K20, MesConstant.Status.K30);
            this.checkAndUpdate(vgdm0801, dbData);
            // 更新状态为启动状态
            dbData.setOverhaulPlanStatus(MesConstant.Status.K30);
            // 实绩操作信息
            dbData.setActualsRevisor(UserSession.getLoginName());
            dbData.setActualsRevisorName(UserSession.getLoginCName());
            dbData.setActualsTime(DateUtil.curDateTimeStr14());
            Map updMap = dbData.toMap();
            RecordUtils.setRevisor(updMap);
            dao.update(VGDM0801.UPDATE_ACTUAL, updMap);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验数据,无问题时更新数据
     *
     * @param checkData 待校验数据
     * @param dbData    数据库中数据
     */
    private void checkAndUpdate(VGDM0801 checkData, VGDM0801 dbData) {
        // 使用ValidationUtils进行基础校验
        ValidationUtils.validateEntity(checkData, ValidationUtils.Group1.class);
        // 复制已验证的字段
        dbData.setActualLegacyProject(checkData.getActualLegacyProject());
        dbData.setActualOverhaulNumber(checkData.getActualOverhaulNumber());
        dbData.setActualOverhaulTime(checkData.getActualOverhaulTime());
        dbData.setIsComplete(checkData.getIsComplete());
        dbData.setIsHot(checkData.getIsHot());
        dbData.setIsConformStandard(checkData.getIsConformStandard());
        // 特定业务逻辑校验
        if ("0".equals(checkData.getIsComplete()) && StrUtil.isBlank(checkData.getOverhaulLegacyProject())) {
            throw new PlatException("是否完成为否，待处理及遗留问题不能为空");
        }
        dbData.setOverhaulLegacyProject(checkData.getOverhaulLegacyProject());
        if ("0".equals(checkData.getIsConformStandard()) && StrUtil.isBlank(checkData.getRelevantMeasures())) {
            throw new PlatException("是否符合标准为否，相关措施不能为空");
        }
        dbData.setRelevantMeasures(checkData.getRelevantMeasures());
        dbData.setOverhaulImplementDate(checkData.getOverhaulImplementDate());
        dbData.setOverhaulSuggestions(checkData.getOverhaulSuggestions());
        dbData.setOverhaulSummarize(checkData.getOverhaulSummarize());
    }

    /**
     * 检修实绩确认
     *
     * <p>状态启动->完成，是否完成未否时生成新的检修计划
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            List<String> ids = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_START_STATUS, MesConstant.Status.K30);
                // 数据校验防止提交空数据
                this.checkAndUpdate(dbData, dbData);

                //校验动火文件是否存在
                if(dbData.getIsHot().equals("1")){
                    Boolean fileExist = checkUpdateFile(dbData,"isHot");
                    if (!fileExist) {
                        throw new PlatException("请上传动火文件");
                    }
                }

                // 更新状态为完成状态
                dbData.setOverhaulPlanStatus(MesConstant.Status.K40);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
                // 检修未完成时生成新的检修计划
                if ("0".equals(dbData.getIsComplete())) {
                    VGDM0801 newPlan = new VGDM0801();
                    newPlan.fromMap(updMap);
                    // 依据凭单-原检修单
                    newPlan.setVoucherNum(dbData.getOverhaulPlanId());
                    // 检修来源-检修遗留
                    newPlan.setOverhaulSource("3");
                    // 检修项目-原检修遗留项目
                    newPlan.setOverhaulProject(dbData.getOverhaulLegacyProject());
                    // 计划检修开始日期-当天
                    LocalDate today = LocalDate.now();
                    newPlan.setOverhaulStartDate(today.format(DateTimeFormatter.ISO_LOCAL_DATE) + " 00:00");
                    // 计划检修结束日期-当天+7天
                    newPlan.setOverhaulEndDate(today.plusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE) + " 00:00");
                    // 新增数据
                    newPlan.insertData(dao);
                }
                ids.add(dbData.getOverhaulPlanId());
            }
            // 批量更新
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            // 上传数据至IMC
            VGDM0804.uploadToIMC(dao, ids, null);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CONFIRM);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 检修实绩确认-pda
     *
     * <p>状态启动->完成，是否完成未否时生成新的检修计划
     */
    public EiInfo pdaConfirm(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801 = new VGDM0801();
            List<String> ids = new ArrayList<>();
            vgdm0801.fromMap(block.getRow(0));
            VGDM0801 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ACTIVE_OR_START_STATUS, MesConstant.Status.K20, MesConstant.Status.K30);
            // 数据校验防止提交空数据
            this.checkAndUpdate(vgdm0801, dbData);
            // 修改人信息
            dbData.setActualsRevisor(vgdm0801.getActualsRevisor());
            dbData.setActualsRevisorName(vgdm0801.getActualsRevisorName());
            dbData.setActualsTime(vgdm0801.getActualsTime());
            dbData.setRecRevisor(vgdm0801.getActualsRevisor());
            dbData.setRecRevisorName(vgdm0801.getActualsRevisorName());
            dbData.setRecReviseTime(vgdm0801.getActualsTime());
            // 更新状态为完成状态
            dbData.setOverhaulPlanStatus(MesConstant.Status.K40);
            Map updMap = dbData.toMap();
            // 检修未完成时生成新的检修计划
            if ("0".equals(dbData.getIsComplete())) {
                VGDM0801 newPlan = new VGDM0801();
                newPlan.fromMap(updMap);
                // 依据凭单-原检修单
                newPlan.setVoucherNum(dbData.getOverhaulPlanId());
                // 检修来源-检修遗留
                newPlan.setOverhaulSource("3");
                // 检修项目-原检修遗留项目
                newPlan.setOverhaulProject(dbData.getOverhaulLegacyProject());
                // 计划检修开始日期-当天
                LocalDate today = LocalDate.now();
                newPlan.setOverhaulStartDate(today.format(DateTimeFormatter.ISO_LOCAL_DATE) + " 00:00");
                // 计划检修结束日期-当天+7天
                newPlan.setOverhaulEndDate(today.plusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE) + " 00:00");
                // 新增数据
                newPlan.setRecCreator(dbData.getActualsRevisor());
                newPlan.setRecCreatorName(dbData.getActualsRevisorName());
                newPlan.setRecCreateTime(dbData.getActualsTime());
                newPlan.setRecRevisor(dbData.getActualsRevisor());
                newPlan.setRecRevisorName(dbData.getActualsRevisorName());
                newPlan.setRecReviseTime(dbData.getActualsTime());
                newPlan.setDelFlag("0");
                newPlan.insertData(dao, false, false);
            }
            ids.add(dbData.getOverhaulPlanId());
            // 更新
            dao.update(VGDM0801.UPDATE_ACTUAL, updMap);
            // 上传数据至IMC
            VGDM0804.uploadToIMC(dao, ids, null);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CONFIRM);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 加入设备履历
     */
    public EiInfo addResume(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_COMPLETED_STATUS, MesConstant.Status.K40);
                // 数据返回前端
                block.getRows().set(i, dbData.toMap());
            }
            // 加入设备履历
            VGDM0104.addHistory(block, VGDM0104.RelevanceType.OVERHAUL);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_ADD_RESUME);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 校验更新文件
     */
    private boolean checkUpdateFile(VGDM0801 vgdm0801, String type) {

        VGDM0802 vgdm0802 = new VGDM0802();
        vgdm0802.setRelevanceId(vgdm0801.getOverhaulPlanId());
        if(type.equals("isHot")){
            vgdm0802.setRelevanceType("VGDM08H");
        }else{
            vgdm0802.setRelevanceType("VGDM0801");
        }
        vgdm0802.setSegNo(vgdm0801.getSegNo());
        List fileList = dao.query(VGDM0802.QUERY, vgdm0802.toMap());
        if(CollectionUtils.isNotEmpty(fileList)){
            return true;
        }else{
            return false;
        }

    }


    /**
     * 查询已提交的检修计划
     */
    public EiInfo queryCommited(EiInfo inInfo) {
        return super.query(inInfo, VGDM0801.QUERY_FOR_MOBILE, new VGDM0801());
    }


    /**
     * 审批检修计划
     */
    public EiInfo overhaulPlanConfirm(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        try  {

            List<String> uuids = new ArrayList<>();
            List<Map<String, Object>> paramList = new ArrayList<>();

            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            Map loginUser = inInfo.getBlock("loginUser").getRow(0);
            //Map queryMap = (Map) inInfo.getAttr().get("queryMap");
            //Map loginUser = (Map) inInfo.getAttr().get("loginUser");

            // 工作流相关参数
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.OVERHAUL_PLAN);
            paramMap.put("segNo", StringUtils.defaultIfEmpty(StringUtils.toString(queryMap.get("segNo")), " "));
            paramMap.put("approvalResult", "grant");
            paramMap.put("userId", loginUser.get("userId"));
            paramMap.put("comment", queryMap.get("approvalopinions"));
            paramMap.put("variables", new HashMap<>());
            String taskId = WorkFlowUtils.getTodoTaskForInter(queryMap.get("processId").toString(),loginUser.get("userId").toString());
            paramMap.put("taskId", taskId);

            //更新检修计划相关状态数据
            Map updMap = new HashMap<>();
            updMap.put("overhaulPlanId", queryMap.get("overhaulPlanId"));
            updMap.put("segNo", queryMap.get("segNo"));
            updMap.put("processInstanceId", queryMap.get("processId"));
            updMap.put("recRevisor",  loginUser.get("userId"));
            updMap.put("recRevisorName", loginUser.get("userName"));
            updMap.put("recReviseTime", DateUtils.curDateTimeStr14());


            //审批驳回
            if(queryMap.get("suitCutApproveFlag").equals("00")){
                updMap.put("apprStatus","7X");
                // 状态-生效
                updMap.put("overhaulPlanStatus",MesConstant.Status.K10);
                //更新状态
                dao.update(VGDM0801.UPDATE_STATUS_FOR_MOBLIE, updMap);
                // 批量操作工作流
                WorkFlowUtils.batchAudit(paramList, false);

                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);

            }else if (queryMap.get("suitCutApproveFlag").equals("10")){
                queryMap.put("apprStatus","70");
                // 审批通过
                boolean finishFlag = WorkFlowUtils.addAuditPersons(dao, paramMap);

                // 流程结束后更新状态
                if (finishFlag) {
                    // 状态-生效
                    updMap.put("overhaulPlanStatus",MesConstant.Status.K20);
                    // 审批状态-审批通过
                    updMap.put("apprStatus",MesConstant.Status.K70);
                    // 待发送IMC
                    //uuids.add(dbData.getUuid());

                    paramList.add(paramMap);
                    //更新状态
                    dao.update(VGDM0801.UPDATE_STATUS_FOR_MOBLIE, updMap);

                    // 批量操作工作流
                    WorkFlowUtils.batchAudit(paramList, true);

                    outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    outInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);
                }
            }
        } catch (PlatException ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }



}
