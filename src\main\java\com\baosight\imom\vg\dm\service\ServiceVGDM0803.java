package com.baosight.imom.vg.dm.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.constants.WorkFlowConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0801;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR> yzj
 * @Description : 检修计划审批页面后台
 * @Date : 2024/9/26
 * @Version : 1.0
 */
public class ServiceVGDM0803 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0803.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0801().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        String processInstanceId = inInfo.getString("processInstanceId");
        if (!StrUtil.isBlank(processInstanceId)) {
            // 首页待审批跳转
            Map<String, String> map = new HashMap<>();
            map.put("processInstanceId", processInstanceId);
            map.put("apprStatus", MesConstant.Status.K60);
            map.put("loginId", UserSession.getLoginName());
            List list = dao.query(VGDM0801.QUERY, map);
            inInfo.getBlock(EiConstant.resultBlock).addRows(list);
        }
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        inInfo.setCell(EiConstant.queryBlock, 0, "loginId", UserSession.getLoginName());
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0801.QUERY, new VGDM0801());
    }

    /**
     * 审批通过
     */
    public EiInfo agree(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            List<String> uuids = new ArrayList<>();
            String userId = UserSession.getLoginName();
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                String comment = block.getCellStr(i, "comment");
                if (StrUtil.isBlank(comment)) {
                    comment = "审批通过";
                }
                vgdm0801.fromMap(block.getRow(i));
                // 校验数据状态已提交
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K15);
                // 校验审批状态审批中
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVAL_STATUS, dbData.getApprStatus(), MesConstant.Status.K60);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.OVERHAUL_PLAN);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "grant");
                paramMap.put("userId", userId);
                paramMap.put("comment", comment);
                paramMap.put("variables", new HashMap<>());
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 审批通过
                boolean finishFlag = WorkFlowUtils.addAuditPersons(dao, paramMap);
                // 流程结束后更新状态
                if (finishFlag) {
                    // 状态-生效
                    dbData.setOverhaulPlanStatus(MesConstant.Status.K20);
                    // 审批状态-审批通过
                    dbData.setApprStatus(MesConstant.Status.K70);
                    // 待发送IMC
                    uuids.add(dbData.getUuid());
                }
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 更新数据
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, true);
            // 发送IMC
            sendIMC(uuids, false);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 审批驳回
     */
    public EiInfo reject(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            String userId = UserSession.getLoginName();
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                String comment = block.getCellStr(i, "comment");
                if (StrUtil.isBlank(comment)) {
                    throw new PlatException("审核驳回时审批意见不能为空");
                }
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                // 校验数据状态已提交
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K15);
                // 校验审批状态审批中
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVAL_STATUS, dbData.getApprStatus(), MesConstant.Status.K60);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.OVERHAUL_PLAN);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "reject");
                paramMap.put("userId", userId);
                paramMap.put("comment", comment);
                paramMap.put("variables", new HashMap<>());
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 状态-新增
                dbData.setOverhaulPlanStatus(MesConstant.Status.K10);
                // 审批状态-审批驳回
                dbData.setApprStatus(MesConstant.Status.K7X);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 更新数据
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, false);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 审批撤销
     *
     * <p>对审批通过的数据清空审批信息，状态改为新增。
     */
    public EiInfo cancelAudit(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            List<String> uuids = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                // 校验数据状态生效
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ACTIVE_STATUS, MesConstant.Status.K20);
                // 校验审批状态审批通过
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVED_STATUS, dbData.getApprStatus(), MesConstant.Status.K70);
                // 终止工作流
                WorkFlowUtils.deleteProcess(dbData.getProcessInstanceId());
                // 更新数据
                dbData.setApprStatus(" ");
                dbData.setProcessInstanceId(" ");
                dbData.setOverhaulPlanStatus(MesConstant.Status.K10);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 待发送IMC
                uuids.add(dbData.getUuid());
            }
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE_STATUS, block.getRows());
            // 发送IMC
            sendIMC(uuids, true);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 延期
     */
    public EiInfo delay(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0801 vgdm0801;
            VGDM0801 dbData;
            String overhaulStartDate = inInfo.getString("overhaulStartDate");
            String overhaulEndDate = inInfo.getString("overhaulEndDate");
            String delayRemark = inInfo.getString("delayRemark");
            List<String> uuids = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0801 = new VGDM0801();
                vgdm0801.fromMap(block.getRow(i));
                // 校验状态生效
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0801, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ACTIVE_STATUS, MesConstant.Status.K20);
                // 校验审批状态审批通过
                DaoUtils.compareStr(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_APPROVED_STATUS, dbData.getApprStatus(), MesConstant.Status.K70);
                // 校验并更新延期日期
                checkDelayDate(dbData, overhaulStartDate, overhaulEndDate);
                // 追加延期理由
                dbData.setDelayRemark(StrUtil.appendDelayRemark(dbData.getDelayRemark(), delayRemark));
                // 更新数据
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 待发送IMC
                uuids.add(dbData.getUuid());
            }
            DaoUtils.updateBatch(dao, VGDM0801.UPDATE, block.getRows());
            // 发送IMC
            sendIMC(uuids, false);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_DELAY);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 校验并更新延期日期
     *
     * @param dbData            数据库数据
     * @param overhaulStartDate 延期后开始日期
     * @param overhaulEndDate   延期后结束日期
     */
    private void checkDelayDate(VGDM0801 dbData, String overhaulStartDate, String overhaulEndDate) {
        // 原开始日期
        LocalDateTime originalStartDate = LocalDateTime.parse(dbData.getOverhaulStartDate(), DateUtils.FORMATTER_16);
        // 延期后日期
        LocalDateTime startDate = LocalDateTime.parse(overhaulStartDate, DateUtils.FORMATTER_16);
        LocalDateTime endDate = LocalDateTime.parse(overhaulEndDate, DateUtils.FORMATTER_16);
        // 校验计划检修日期范围
        if (startDate.isAfter(endDate)) {
            throw new PlatException("计划检修日(起)不能晚于计划检修日(止)");
        }
        // 延期后日期必须大于原日期
        if (!originalStartDate.isBefore(startDate)) {
            throw new PlatException("延期后开始日期必须晚于原开始日期");
        }
        // 更新延期日期
        dbData.setOverhaulStartDate(overhaulStartDate);
        dbData.setOverhaulEndDate(overhaulEndDate);
    }

    /**
     * 检修计划发送IMC
     *
     * @param uuids    唯一编码
     * @param isCancel 是否撤销
     */
    private void sendIMC(List<String> uuids, boolean isCancel) {
        if (CollectionUtils.isEmpty(uuids)) {
            return;
        }
        log("待上传数据条数：" + uuids.size());
        Map<String, Object> map = new HashMap<>();
        map.put("uuids", uuids);
        List<Map> list = dao.query(VGDM0801.QUERY_FOR_IMC, map);
        log("上传IMC数据条数：" + list.size());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        EiInfo imcInfo = new EiInfo();
        imcInfo.set("operateType", isCancel ? "00" : "10");
        imcInfo.set("detail", list);
        // 服务ID
        imcInfo.set(EiConstant.serviceId, "S_VI_SM_0137");
        EiInfo rtnInfo = EServiceManager.call(imcInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
    }

    /**
     * 查询月度检修计划，用于IMC月度排产
     *
     * <p>
     * serviceId: S_VG_DM_0005
     */
    public EiInfo queryForSchedule(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            String yearMonth = inInfo.getString("yearMonth");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(yearMonth) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("传入参数不能为空");
            }
            super.log("传入参数:segNo:" + segNo + " yearMonth:" + yearMonth + " eArchivesNo:" + eArchivesNo);
            // 根据月份获取开始日期
            LocalDate startDate = LocalDate.parse(yearMonth + "-01");
            LocalDate endDate = startDate.plusMonths(1);
            // 查询条件
            Map<String, String> map = new HashMap<>();
            map.put("segNo", segNo);
            map.put("startDate", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            map.put("endDate", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            map.put("eArchivesNo", eArchivesNo);
            List list = dao.query(VGDM0801.QUERY_FOR_SCHEDULE, map);
            log("查询结果数：" + list.size());
            inInfo.set("result", list);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_1003);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
