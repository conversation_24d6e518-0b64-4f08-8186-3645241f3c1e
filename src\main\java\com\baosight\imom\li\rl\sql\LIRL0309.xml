<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-26 16:44:59
       Version :  1.0
    tableName :${meliSchema}.tlirl0309
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     HAND_POINT_ID  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     HAND_TYPE  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0309">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0309">
        SELECT
        tlirl0309.SEG_NO as "segNo",  <!-- 系统账套 -->
        tlirl0309.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0309.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        tlirl0309.HAND_POINT_ID as "handPointId",  <!-- 装卸点代码 -->
        tlirl0309.STATUS as "status",  <!-- 状态(00撤销 10新增 20审核 30启动 99停用 ) -->
        tlirl0309.HAND_TYPE as "handType",  <!-- 装卸类型(10 装 20卸 30装卸) -->
        tlirl0309.BUSINESS_TYPE as "businessType",  <!-- 装卸业务类型 -->
        tlirl0309.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        tlirl0309.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        tlirl0309.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        tlirl0309.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        tlirl0309.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        tlirl0309.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        tlirl0309.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        tlirl0309.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        tlirl0309.REMARK as "remark",  <!-- 备注 -->
        tlirl0309.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        tlirl0309.UUID as "uuid",  <!-- uuid -->
        tlirl0309.TENANT_ID as "tenantId", <!-- 租户ID -->
        tlirl0309.SCRAP_TYPE as "scrapType" <!-- 可利用材 -->

        FROM ${meliSchema}.tlirl0309 tlirl0309 WHERE 1=1
        <isNotEmpty prepend=" AND " property="segNo">
            tlirl0309.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId">
            tlirl0309.HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            tlirl0309.HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            tlirl0309.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessType">
            tlirl0309.BUSINESS_TYPE = #businessType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusFlag">
          not  exists(
            select 1 from
            meli.tlirl0315 tlirl0315 where tlirl0315.SEG_NO=tlirl0309.SEG_NO
            AND tlirl0315.HAND_POINT_ID=tlirl0309.HAND_POINT_ID
            AND tlirl0315.STATUS='30'
            )
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                tlirl0309.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0309 WHERE 1=1
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handPointId">
            HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="handType">
            HAND_TYPE = #handType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0309 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        HAND_POINT_ID,  <!-- 装卸点代码 -->
        STATUS,  <!-- 状态(00撤销 10新增 20审核 30启动 99停用 ) -->
        HAND_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
        BUSINESS_TYPE,  <!-- 装卸类型(10 装 20卸 30装卸) -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID,  <!-- 租户ID -->
        SCRAP_TYPE  <!-- 可利用材 -->
        )
        VALUES (#segNo#, #unitCode#, #handPointId#, #status#, #handType#,
        #businessType#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
        #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#, #scrapType#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0309 WHERE
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0309
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        HAND_POINT_ID = #handPointId#,   <!-- 装卸点代码 -->
        STATUS = #status#,   <!-- 状态(00撤销 10新增 20审核 30启动 99停用 ) -->
        HAND_TYPE = #handType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
        BUSINESS_TYPE = #businessType#,   <!-- 装卸类型(10 装 20卸 30装卸) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        UUID = #uuid#,   <!-- uuid -->
        TENANT_ID = #tenantId#,  <!-- 租户ID -->
        SCRAP_TYPE = #scrapType#  <!-- 可利用材 -->
        WHERE 1=1
        and uuid = #uuid#
    </update>

    <update id="updatetLirl0309">
        UPDATE ${meliSchema}.tlirl0309
        SET
        STATUS=#status#,
        DEL_FLAG='0'
        WHERE 1=1
        and SEG_NO = #segNo#
        and HAND_POINT_ID=#handPointId#
    </update>

    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select distinct  tlirl0304.HAND_POINT_ID as "handPointId", tlirl0304.HAND_POINT_NAME as "handPointName"
        from meli.tlirl0304 tlirl0304,
        meli.tlirl0309 tlirl0309
        where 1 = 1
        and tlirl0304.SEG_NO = tlirl0309.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0309.HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0309.STATUS = '30'
        and tlirl0309.BUSINESS_TYPE = '20'
        and tlirl0304.SEG_NO = #segNo#
    </select>
</sqlMap>