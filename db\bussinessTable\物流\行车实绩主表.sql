create table meli.tlids1201
(
    SEG_NO               varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE            varchar(12)  default ' ' not null comment '业务单元代代码',
    CRANE_RESULT_ID      varchar(32)  default ' ' not null comment '行车实绩单号',
    CRANE_ORDER_ID       varchar(32)  default ' ' null comment '行车作业清单号',
    CRANE_ID             varchar(20)  default ' ' not null comment '行车编号',
    CRANE_NAME           varchar(200) default ' ' not null comment '行车名称',
    START_X_POSITION     varchar(20)  default ' ' null comment '起始X轴坐标',
    START_Y_POSITION     varchar(20)  default ' ' null comment '起始X轴坐标',
    START_Z_POSITION     varchar(20)  default ' ' null comment '起始Y轴坐标',
    ENDX_POSITION        varchar(20)  default ' ' null comment '终到X轴坐标',
    ENDY_POSITION        varchar(20)  default ' ' null comment '终到X轴坐标',
    ENDZ_POSITION        varchar(20)  default ' ' null comment '终到Y轴坐标',
    INBOUND_SEQUENCE_ID  varchar(20)  default ' ' null comment '抓取流水号',
    OUTBOUND_SEQUENCE_ID varchar(20)  default ' ' null comment '释放流水号',
    ABNORMAL_FLAG        varchar(2)   default ' ' null comment '异常标记',
    STATUS               varchar(2)   default ' ' not null comment '状态',
    REC_CREATOR          varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME     varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME      varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR          varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME     varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME      varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG         varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER          varchar(10)  default ' ' null comment '租户',
    DEL_FLAG             smallint     default 0   null comment '删除标记',
    UUID                 varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids1201_UUID_uindex
        unique (UUID)
)
    comment '行车实绩主表' collate = utf8_bin;

