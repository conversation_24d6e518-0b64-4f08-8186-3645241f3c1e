package com.baosight.imom.common.utils;

import com.baosight.iplat4j.common.ed.domain.TEDCM01;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.imom.common.constants.BlockConstant;
import com.baosight.imom.common.constants.CodeTypeConstant;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.xt.ss.domain.XTSS01;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * code-value 值集工具类
 *
 * @package com.baosight.imom.common.utils
 * <AUTHOR>
 * @Date 2024-08-05
 */
public class CodeValueUtils {


    private static final Logger LOG = LoggerFactory.getLogger(CodeValueUtils.class);


    /**
     * 查询小代码
     *
     * @param codeset
     * @return List
     * <AUTHOR>
     * @Date 2024-08-05
     * @Version 1.0
     */
    public static List<TEDCM01> queryCodeValue(Dao dao, String codeset, String filter, String condition) {
        List<TEDCM01> codeList = new ArrayList<>();
        //获取codeset的缓存数据
        Map<String, List<TEDCM01>> cache = CacheManager.getCache("iplat.codeset");
        String key;
        if (StringUtils.isNotBlank(filter)) {
            key = codeset + "-" + condition + "-" + filter;
        } else {
            key = codeset + "-" + condition;
        }
        codeList = (List) cache.get(key);
        //如果缓存中不存在小代码数据，则进行数据库查询
        if (codeList == null || CollectionUtils.isEmpty(codeList)) {
            Map map = new HashMap(16);
            map.put("codesetCode", codeset);
            map.put("status", "1");
            map.put("condition", condition);
            map.put("itemCode", filter);
            codeList = dao.query("EDCM01.queryByCodeset", map, 0, -999999);
        }
        return codeList;
    }


    /**
     * 查询code-value至blockId
     *
     * @param segNo
     * @param codeTypeList
     * @param eiInfo
     * @return
     */
    public static EiInfo queryToBlock(String segNo, List<String> codeTypeList, EiInfo eiInfo) {
        //封装查询条件优先查询出结果集
        Map<String, Object> queryMap = new HashMap<>(16);
        queryMap.put(MesConstant.SEG_NO, segNo);
        queryMap.put(CodeTypeConstant.CODE_TYPE_LIST, codeTypeList);
        List<Map<String, Object>> codeValueList = queryCodeValue(queryMap);
        //重新封装数据
        for (String codeType : codeTypeList) {
            //将以全大写，以下划线分割的codeType转为驼峰形式
            String blockId = StrUtil.camelName(codeType).concat("Block");
            //codeValue结果集中取出符合CodeType的list，封装进EiInfo，以codeType的驼峰形式+Block为Key
            List<Map<String, Object>> blockList = codeValueList.stream().filter(s -> s.get("codeType").equals(codeType)).collect(Collectors.toList());
            eiInfo.addBlock(blockId).addRows(blockList);
        }
        return eiInfo;
    }


    /**
     * 调用code-value 微服务查询code-value
     *
     * @param queryMap 可扩展参数 Map
     * @return codeValue 集合
     */
    public static List<Map<String, Object>> queryCodeValue(Map<String, Object> queryMap) {
        EiInfo eiInfo = new EiInfo();
        EiBlock queryBlock = eiInfo.addBlock(BlockConstant.CODE_VALUE_QUERY_BLOCK);
        queryBlock.setAttr(queryMap);

        //调用code-value服务类的查询codeValue服务
        eiInfo.set(EiConstant.serviceName, "XTSS01");
        eiInfo.set(EiConstant.methodName, "queryCodevalue");
        EiInfo outEi = XLocalManager.callNoTx(eiInfo);

        //返回结果集
        EiBlock resultBlock = outEi.getBlock(BlockConstant.CODE_VALUE_RESULT_BLOCK);
        List<Map<String, Object>> rows = resultBlock.getRows();
        return rows;
    }

    /**
     * 获取业务单元信息
     *
     */
    public static EiBlock getUnitBlock(Dao dao) {
        Map<String, String> map = new HashMap<>();
        List segNoAuList = dao.query(XTSS01.QUERY_SEGNO_LIST_BY_BLANK_SEG_NO, map);        
        EiBlock rtnBlock = new EiBlock("unitBlock");
        rtnBlock.addRows(segNoAuList);
        return rtnBlock;
    }
}
