/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids0901;
import com.baosight.imom.common.li.domain.Tlids0904;

import java.util.Map;

/**
* Tlids0904
* 
*/
public class LIDS0904 extends Tlids0904 {
        public static final String QUERY = "LIDS0904.query";
        public static final String COUNT = "LIDS0904.count";
        public static final String COUNT_UUID = "LIDS0904.count_uuid";
        public static final String INSERT = "LIDS0904.insert";
        public static final String UPDATE = "LIDS0904.update";
        public static final String DELETE = "LIDS0904.delete";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0904() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}