package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0101;
import com.baosight.imom.vg.dm.domain.VGDM0102;
import com.baosight.imom.vg.dm.domain.VGDM0103;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 设备档案清单页面后台
 * @Date : 2024/8/8
 * @Version : 1.0
 */
public class ServiceVGDM01 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM01.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0101().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT2_BLOCK).addBlockMeta(new VGDM0102().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT3_BLOCK).addBlockMeta(new VGDM0103().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT4_BLOCK).addBlockMeta(new VGDM0103().eiMetadata);
        // 业务单元
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0101.QUERY, new VGDM0101());
    }

    /**
     * 根据设备档案查询分部设备
     */
    public EiInfo queryDeviceByEquipment(EiInfo inInfo) {
        return super.query(inInfo, VGDM0102.QUERY, null, false, new VGDM0102().eiMetadata,
                MesConstant.Iplat.INQU_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 根据设备档案查询最大版本附件信息
     */
    public EiInfo queryFileByEquipment(EiInfo inInfo) {
        return super.query(inInfo, VGDM0103.QUERY_MAX, null, false, new VGDM0103().eiMetadata,
                MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT3_BLOCK, MesConstant.Iplat.RESULT3_BLOCK,
                VGDM0103.COUNT_MAX);
    }

    /**
     * 根据文件名查询文件历史版本
     */
    public EiInfo queryFileHistory(EiInfo inInfo) {
        return super.query(inInfo, VGDM0103.QUERY, null, false, new VGDM0103().eiMetadata,
                MesConstant.Iplat.INQU1_STATUS_BLOCK, MesConstant.Iplat.RESULT4_BLOCK, MesConstant.Iplat.RESULT4_BLOCK);
    }

    /**
     * 附件上传(ftp)
     */
    public EiInfo fileUpload(EiInfo inInfo) {
        try {
            // 获取前端参数
            CommonsMultipartFile file = (CommonsMultipartFile) inInfo.get("file");
            String eArchivesNo = inInfo.getString("id");
            String fifleType = inInfo.getString("id2");
            // 获取设备信息
            VGDM0101 vgdm0101 = VGDM0101.queryByNo(dao, eArchivesNo, true);
            if (vgdm0101 == null) {
                throw new PlatException("设备档案不存在" + eArchivesNo);
            }
            // 上传文件
            String fileName = file.getOriginalFilename();
            String fileId = UUIDUtils.getUUID();
            // 获取文件后缀
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            // 转换文件名防止文件重复
            String storeName = fileId + suffix;
            // 上传文件-本地
            String downloadUrl = FileUtils.uploadFile(file, "/device/" + vgdm0101.getSegNo());
            // 上传文件-ftp
//            String downloadUrl = FtpUtils.uploadFile(vgdm0101.getSegNo(), file, "device/" + storeName);
//            if (downloadUrl == null) {
//                throw new PlatException("文件上传失败");
//            }
            // 待新增的附件记录
            VGDM0103 vgdm0103 = new VGDM0103();
            vgdm0103.setEArchivesNo(eArchivesNo);
            vgdm0103.setEquipmentName(vgdm0101.getEquipmentName());
            vgdm0103.setUnitCode(vgdm0101.getUnitCode());
            vgdm0103.setSegNo(vgdm0101.getSegNo());
            // 文件信息
            vgdm0103.setUploadFileName(fileName);
            vgdm0103.setFifleType(fifleType);
            vgdm0103.setFifleSize(new BigDecimal(file.getSize()));
            vgdm0103.setFileId(fileId);
            vgdm0103.setUploadFilePath(downloadUrl);
            // 版本
            vgdm0103.setFileVersion(new BigDecimal(getFileVersion(eArchivesNo, fileName, fifleType)));
            // 创建人
            Map data = vgdm0103.toMap();
            RecordUtils.setCreator(data);
            // 新增到数据库
            dao.insert(VGDM0103.INSERT, data);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 获取文件最大版本
     *
     * @param eArchivesNo 设备档案编号
     * @param fileName    文件名
     * @param fifleType   文件类型
     * @return 最大版本号
     */
    public int getFileVersion(String eArchivesNo, String fileName, String fifleType) {
        Map<String, String> map = new HashMap<>();
        map.put("eArchivesNo", eArchivesNo);
        map.put("uploadFileName", fileName);
        map.put("fifleType", fifleType);
        int maxVersion = super.count(VGDM0103.QUERY_MAX_VERSION, map);
        return maxVersion + 1;
    }

    /**
     * 附件删除
     */
    public EiInfo deleteFile(EiInfo inInfo) {
        try {
            EiBlock resultBlock = inInfo.getBlock(MesConstant.Iplat.RESULT4_BLOCK);
            List<Map> updList = new ArrayList<>();
            for (int i = 0; i < resultBlock.getRows().size(); i++) {
                VGDM0103 vgdm0103 = new VGDM0103();
                vgdm0103.fromMap(resultBlock.getRow(i));
                String filePath = vgdm0103.getUploadFilePath().split("8081")[1];
                filePath = "/apps" + filePath;
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
                vgdm0103.setDelFlag("1");
                Map updMap = vgdm0103.toMap();
                RecordUtils.setRevisor(updMap);
                updList.add(updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0103.UPD_FRO_DEL, updList);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * IMC设备档案及分部设备信息同步
     *
     * <p>
     * serviceId:S_VG_DM_0004
     */
    public EiInfo receiveData(EiInfo inInfo) {
        try {
            log("接收参数：" + inInfo.toJSONString());
            List<Map> list = (List<Map>) inInfo.get("equipment");
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("接口参数不能为空");
            }
            // 新增和修改的设备档案信息
            List<Map> insMainList = new ArrayList<>();
            List<Map> updMainList = new ArrayList<>();
            // 新增和修改的分部设备信息
            List<Map> insList = new ArrayList<>();
            List<Map> updList = new ArrayList<>();
            for (Map equipmentMap : list) {
                // 设备档案信息
                VGDM0101 vgdm0101 = new VGDM0101();
                vgdm0101.fromMap(equipmentMap);
                vgdm0101.setEArchivesNo(MapUtils.getString(equipmentMap, "e_archivesNo"));
                if (isExist(vgdm0101.getUuid(), VGDM0101.COUNT_BY_ID)) {
                    updMainList.add(vgdm0101.toMap());
                } else {
                    insMainList.add(vgdm0101.toMap());
                }
                // 遍历jsonArray同步分部设备信息
                List<Map> deviceList = (List<Map>) equipmentMap.get("device");
                if (CollectionUtils.isNotEmpty(deviceList)) {
                    VGDM0102 vgdm0102;
                    for (Map deviceMap : deviceList) {
                        vgdm0102 = new VGDM0102();
                        vgdm0102.fromMap(deviceMap);
                        vgdm0102.setEArchivesNo(vgdm0101.getEArchivesNo());
                        if (isExist(vgdm0102.getUuid(), VGDM0102.COUNT_BY_ID)) {
                            updList.add(vgdm0102.toMap());
                        } else {
                            insList.add(vgdm0102.toMap());
                        }
                    }
                }
            }
            // 批量插入新增的设备档案信息
            DaoUtils.insertBatch(dao, VGDM0101.INSERT, insMainList);
            // 批量更新修改的设备档案信息
            DaoUtils.updateBatch(dao, VGDM0101.UPDATE, updMainList);
            // 批量插入新增的分部设备信息
            DaoUtils.insertBatch(dao, VGDM0102.INSERT, insList);
            // 批量更新修改的分部设备信息
            DaoUtils.updateBatch(dao, VGDM0102.UPDATE, updList);
            // 返回成功
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 判断数据是否存在
     *
     * @param uuid    uuid
     * @param sqlName count语句
     * @return 是否存在
     */
    private boolean isExist(String uuid, String sqlName) {
        if (StrUtil.isBlank(uuid)) {
            throw new PlatException("uuid不能为空");
        }
        Map<String, String> map = new HashMap<>();
        map.put("uuid", uuid);
        int count = super.count(sqlName, map);
        return count > 0;
    }


    /**
     * 修改厂房信息
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0101 vgdm0101;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0101 = new VGDM0101();
                vgdm0101.fromMap(block.getRow(i));
                vgdm0101.setDelFlag("0");
                Map updMap = vgdm0101.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
                super.dao.update(VGDM0101.UPDATE_FACTORY_BUILDING, block.getRow(i));
            }
            //DaoUtils.updateBatch(dao, VGDM0101.UPDATE_FACTORY_BUILDING, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;

    }

    /**
     * IMC设备档案归档
     *
     * <p>
     * serviceId:S_VG_DM_0009
     */
    public EiInfo archiveEquipment(EiInfo inInfo) {
        try {
            log("接收参数：" + inInfo.toJSONString());
            List<Map> list = (List<Map>) inInfo.get("list");
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("接口参数不能为空");
            }
            // 修改的设备档案信息
            List<Map> updList = new ArrayList<>();
            for (Map map : list) {
                String segNo = MapUtils.getString(map, "segNo");
                String eArchivesNo = MapUtils.getString(map, "e_archivesNo");
                String recRevisor = MapUtils.getString(map, "recRevisor");
                String recRevisorName = MapUtils.getString(map, "recRevisorName");
                String recRevisorTime = MapUtils.getString(map, "recReviseTime");
                if (StrUtil.isBlank(segNo)
                        || StrUtil.isBlank(eArchivesNo)
                        || StrUtil.isBlank(recRevisor)
                        || StrUtil.isBlank(recRevisorName)
                        || StrUtil.isBlank(recRevisorTime)) {
                    throw new PlatException("接口参数不能为空");
                }
                map.put("eArchivesNo", eArchivesNo);
                log("修改参数：" + segNo + "|" + eArchivesNo + "|" + recRevisor + "|" + recRevisorName + "|" + recRevisorTime);
                updList.add(map);
            }
            log("待更新数量：" + updList.size());
            // 批量更新修改的设备档案信息
            DaoUtils.updateBatch(dao, VGDM0101.UPDATE_ARCHIVE, updList);
            // 批量更新修改的分部设备信息
            DaoUtils.updateBatch(dao, VGDM0102.UPDATE_ARCHIVE2, updList);
            // 返回成功
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * IMC分部设备归档
     *
     * <p>
     * serviceId:S_VG_DM_0010
     */
    public EiInfo archiveDevice(EiInfo inInfo) {
        try {
            log("接收参数：" + inInfo.toJSONString());
            List<Map> list = (List<Map>) inInfo.get("list");
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("接口参数不能为空");
            }
            // 修改的分部设备信息
            List<Map> updList = new ArrayList<>();
            for (Map map : list) {
                String segNo = MapUtils.getString(map, "segNo");
                String deviceCode = MapUtils.getString(map, "deviceCode");
                String recRevisor = MapUtils.getString(map, "recRevisor");
                String recRevisorName = MapUtils.getString(map, "recRevisorName");
                String recRevisorTime = MapUtils.getString(map, "recReviseTime");
                if (StrUtil.isBlank(segNo)
                        || StrUtil.isBlank(deviceCode)
                        || StrUtil.isBlank(recRevisor)
                        || StrUtil.isBlank(recRevisorName)
                        || StrUtil.isBlank(recRevisorTime)) {
                    throw new PlatException("接口参数不能为空");
                }
                log("修改参数：" + segNo + "|" + deviceCode + "|" + recRevisor + "|" + recRevisorName + "|" + recRevisorTime);
                updList.add(map);
            }
            log("待更新数量：" + updList.size());
            // 批量更新修改的分部设备信息
            DaoUtils.updateBatch(dao, VGDM0102.UPDATE_ARCHIVE, updList);
            // 返回成功
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{e.getMessage()});
        }
        return inInfo;
    }
}
