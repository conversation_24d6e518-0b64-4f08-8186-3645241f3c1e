#任务延时启动，单位：秒
xservices.job.startupDelay=60

org.quartz.scheduler.instanceName = imom_Scheduler
org.quartz.scheduler.instanceId = AUTO
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 10
org.quartz.threadPool.threadPriority = 5
org.quartz.jobStore.misfireThreshold = 60000
#数据库方式持久化定时任务
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.useProperties = false
org.quartz.jobStore.dataSource = appDS
org.quartz.jobStore.tablePrefix = EJ_QRTZ_
#集群模式下设置为true
#org.quartz.jobStore.isClustered = true
org.quartz.jobStore.clusterCheckinInterval = 20000
#此处配置数据库持久化连接数据库相关的信息
org.quartz.dataSource.appDS.driver = com.mysql.cj.jdbc.Driver
org.quartz.dataSource.appDS.URL = jdbc:mysql://*********:3306/iplat4j?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8&autoReconnect=true
org.quartz.dataSource.appDS.user = bggjuser
org.quartz.dataSource.appDS.password = 2h7!n0pA7C6g3c9pX4mE
org.quartz.dataSource.appDS.maxConnections = 30

org.quartz.plugin.logging.class = com.baosight.xservices.ej.job.quartz.JobLoggingPlugin
org.quartz.plugin.logging.tablePrefix = EJ_QRTZ_
org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingTriggerHistoryPlugin