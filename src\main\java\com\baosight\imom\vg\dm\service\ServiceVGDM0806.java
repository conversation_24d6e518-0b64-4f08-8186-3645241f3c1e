package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0806;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 月度检修工器具维护
 * @Date : 2025/4/24
 * @Version : 1.0
 */
public class ServiceVGDM0806 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0806.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(MesConstant.Iplat.RESULT3_BLOCK).addBlockMeta(new VGDM0806().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT4_BLOCK).addBlockMeta(new VGDM0806().eiMetadata);
        return inInfo;
    }

    /**
     * 检修工具查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return this.commonQuery(inInfo, MesConstant.Iplat.INQU3_STATUS_BLOCK, MesConstant.Iplat.RESULT3_BLOCK);
    }

    /**
     * 检修工具新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        return this.commonInsert(inInfo, MesConstant.Iplat.RESULT3_BLOCK);
    }


    /**
     * 数据校验
     *
     * @param vgdm0806 数据
     */
    private void checkData(VGDM0806 vgdm0806) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0806);
    }

    /**
     * 检修工具修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        return this.commonUpdate(inInfo, MesConstant.Iplat.RESULT3_BLOCK);
    }

    /**
     * 检修工具删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        return this.commonDelete(inInfo, MesConstant.Iplat.RESULT3_BLOCK);
    }

    /**
     * 检修资材查询
     */
    public EiInfo query4(EiInfo inInfo) {
        return this.commonQuery(inInfo, MesConstant.Iplat.INQU4_STATUS_BLOCK, MesConstant.Iplat.RESULT4_BLOCK);
    }

    /**
     * 检修资材新增
     */
    public EiInfo insert4(EiInfo inInfo) {
        return this.commonInsert(inInfo, MesConstant.Iplat.RESULT4_BLOCK);
    }

    /**
     * 检修资材修改
     */
    public EiInfo update4(EiInfo inInfo) {
        return this.commonUpdate(inInfo, MesConstant.Iplat.RESULT4_BLOCK);
    }

    /**
     * 检修资材删除
     */
    public EiInfo delete4(EiInfo inInfo) {
        return this.commonDelete(inInfo, MesConstant.Iplat.RESULT4_BLOCK);
    }

    /**
     * 通用查询
     *
     * @param inInfo      输入参数
     * @param queryBlock  查询块
     * @param resultBlock 结果块
     */
    private EiInfo commonQuery(EiInfo inInfo, String queryBlock, String resultBlock) {
        String overhaulPlanId = inInfo.getCellStr(queryBlock, 0, "overhaulPlanId");
        if (StrUtil.isBlank(overhaulPlanId)) {
            inInfo.setCell(queryBlock, 0, "overhaulPlanId", UUIDUtils.getUUID());
        }
        return super.query(inInfo, VGDM0806.QUERY, null, false, new VGDM0806().eiMetadata, queryBlock, resultBlock, resultBlock);
    }

    /**
     * 通用新增
     */
    private EiInfo commonInsert(EiInfo inInfo, String blockId) {
        try {
            EiBlock block = inInfo.getBlock(blockId);
            VGDM0806 vgdm0806 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0806 = new VGDM0806();
                vgdm0806.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0806);
                Map insMap = vgdm0806.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            // 更新数据库
            DaoUtils.insertBatch(dao, VGDM0806.INSERT, block.getRows());
            // 重新排序
            this.reSortData(vgdm0806);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 通用修改
     */
    public EiInfo commonUpdate(EiInfo inInfo, String blockId) {
        try {
            EiBlock block = inInfo.getBlock(blockId);
            VGDM0806 vgdm0806 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0806 = new VGDM0806();
                vgdm0806.fromMap(block.getRow(i));
                this.checkData(vgdm0806);
                vgdm0806.setDelFlag("0");
                Map updMap = vgdm0806.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0806.UPDATE, block.getRows());
            // 重新排序
            this.reSortData(vgdm0806);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 通用删除
     */
    public EiInfo commonDelete(EiInfo inInfo, String blockId) {
        try {
            EiBlock block = inInfo.getBlock(blockId);
            VGDM0806 vgdm0806 = null;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0806 = new VGDM0806();
                vgdm0806.fromMap(block.getRow(i));
                // 设置删除标记
                vgdm0806.setDelFlag("1");
                // 设置修改人
                Map delMap = vgdm0806.toMap();
                RecordUtils.setRevisor(delMap);
                // 数据返回前端
                block.getRows().set(i, delMap);
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0806.UPD_FOR_DEL, block.getRows());
            // 重新排序
            this.reSortData(vgdm0806);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 重新排序
     *
     * @param vgdm0806 数据
     */
    private void reSortData(VGDM0806 vgdm0806) {
        if (vgdm0806 == null) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put("overhaulPlanId", vgdm0806.getOverhaulPlanId());
        map.put("dataType", vgdm0806.getDataType());
        map.put("segNo", vgdm0806.getSegNo());
        List<VGDM0806> list = dao.query(VGDM0806.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Map> updList = new ArrayList<>();
        int sortIndex = 1;
        for (VGDM0806 temp : list) {
            if (sortIndex != temp.getSortIndex()) {
                temp.setSortIndex(sortIndex);
                updList.add(temp.toMap());
            }
            sortIndex++;
        }
        DaoUtils.updateBatch(dao, VGDM0806.UPDATE, updList);
    }
}
