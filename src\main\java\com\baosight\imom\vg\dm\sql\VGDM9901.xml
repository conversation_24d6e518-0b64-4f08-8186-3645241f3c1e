<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM9901">

    <sql id="condition">
        <isNotEmpty prepend="AND" property="tagId">
            a.TAG_ID = #tagId#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="eArchivesNo">
            b.E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime">
            a.TAG_TIME &gt;= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            a.TAG_TIME &lt;= #endTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="tagIdList">
            a.TAG_ID IN
            <iterate property="tagIdList" open="(" close=")"
                     conjunction=",">
                #tagIdList[]#
            </iterate>
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM9901">
        SELECT
        a.TAG_ID as "tagId",  <!-- 点位ID -->
        a.TAG_VALUE as "tagValue",  <!-- 点位值 -->
        a.TAG_TIME as "tagTime",  <!-- 点位时间 -->
        a.UUID as "uuid",  <!-- 唯一编码 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime" <!-- 记录创建时刻 -->
        FROM ${mevgSchema}.TVGDM9901 a
        LEFT JOIN ${mevgSchema}.TVGDM0301 b ON a.TAG_ID = b.TAG_ID
        WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.TAG_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM9901 a
        LEFT JOIN ${mevgSchema}.TVGDM0301 b ON a.TAG_ID = b.TAG_ID
        WHERE 1=1
        <include refid="condition"/>
    </select>

    <sql id="condition2">
        <isNotEmpty prepend="AND" property="tagId">
            a.TAG_ID like concat('%',#tagId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="eArchivesNo">
            b.E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime">
            a.TAG_TIME &gt;= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime">
            a.TAG_TIME &lt;= #endTime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="unitCode">
            b.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="segNo">
            b.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            b.EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            b.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            b.UNIT_CODE = #unitCode#
        </isNotEmpty>
    </sql>

    <select id="queryWithTag" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.TAG_ID as "tagId",  <!-- 点位ID -->
        a.TAG_VALUE as "tagValue",  <!-- 点位值 -->
        a.TAG_TIME as "tagTime",  <!-- 点位时间 -->
        a.UUID as "uuid",  <!-- 唯一编码 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime", <!-- 记录创建时刻 -->
        b.TAG_TYPE as "tagType",  <!-- 点位类型 -->
        b.TAG_DESC as "tagDesc",  <!-- 点位描述 -->
        b.DATA_TYPE as "dataType",  <!-- 数据类型 -->
        b.E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        b.EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        b.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        b.SEG_NO as "segNo"  <!-- 业务单元简称 -->
        FROM ${mevgSchema}.TVGDM9901 a
        LEFT JOIN ${mevgSchema}.TVGDM0301 b ON a.TAG_ID = b.TAG_ID
        WHERE 1=1
        <include refid="condition2"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.TAG_TIME desc
            </isEmpty>
        </dynamic>
    </select>

    <select id="queryForCal" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        TAG_ID as "tagId",  <!-- 点位ID -->
        TAG_VALUE as "tagValue",  <!-- 点位值 -->
        TAG_TIME as "tagTimeMicroSecond"  <!-- 点位时间 -->
        FROM ${mevgSchema}.TVGDM9901
        WHERE REC_CREATE_TIME &gt;= #startTime#
        AND REC_CREATE_TIME &lt;= #endTime#
        AND TAG_ID IN
        <iterate property="tagIds" open="(" close=")"
                 conjunction=",">
            #tagIds[]#
        </iterate>
    </select>

    <select id="countWithTag" resultClass="int">
        SELECT COUNT(*)
        FROM ${mevgSchema}.TVGDM9901 a
        LEFT JOIN ${mevgSchema}.TVGDM0301 b ON a.TAG_ID = b.TAG_ID
        WHERE 1=1
        <include refid="condition2"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM9901 (TAG_ID,  <!-- 点位ID -->
        TAG_VALUE,  <!-- 点位值 -->
        TAG_TIME,  <!-- 点位时间 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME  <!-- 记录创建时刻 -->
        )
        VALUES (#tagId#, #tagValue#, #tagTime#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM9901 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM9901
        SET
        TAG_ID = #tagId#,   <!-- 点位ID -->
        TAG_VALUE = #tagValue#,   <!-- 点位值 -->
        TAG_TIME = #tagTime#  <!-- 点位时间 -->
        WHERE
    </update>

</sqlMap>
