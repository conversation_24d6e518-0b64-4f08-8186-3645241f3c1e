$(function () {


    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();
//选中的排产列表中的生产需求单号(机组调整)
    var selectedList = [];
//选中的排产列表中的（修改保存）
    var selectedLists = [];
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });
    machine(unitInfo.segNo);

    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
        result2Grid.dataSource.page(1);
        result3Grid.dataSource.page(1);
    });
    var addsubwindow = $("#ADDSUBWINDOW");
    window.checkboxOnclick = function (checkbox) {
        if (checkbox.checked == true) {
            $("#gx").prop("checked", false);
        } else {
            $("#gx").prop("checked", true);
        }
    }
    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 100,
                pageSizes: [100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "sfsx",
                    headerTemplate: "是否生效",
                    template: function (e) {
                        if (e.status == "20") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "30px"
                }
            ],
            dataBound: (e) => {
                const childen = e.sender.tbody[0].children; // HTMLCollection
                const rows = Array.from(e.sender.dataSource._data);
                rows.forEach((row, index) => {
                    if (row.status == '20') {
                        childen[index].style.color = '#31be66';
                    }
                });
            },
            onCheckRow: function (e) {

            },
            onCellClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                if (e.field === 'sfsx') {
                    let status = e.model["status"];
                    if ("10" == status){
                        e.model["status"] = "20";
                    }else {
                        e.model["status"] = "10";
                    }
                    var ei = new EiInfo();
                    ei.setByNode("result");
                    ei.addBlock(resultGrid.getCheckedBlockData());
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    eiInfo.set("flag", "20");
                    IMOMUtil.submitGridsData("result", "LIRL0106", "update", true, function (e) {
                        if (e.getStatus() > 0) {
                            NotificationUtil({msg: e.msg}, "success");
                            resultGrid.dataSource.page(1);
                        }else if (e.getStatus() == 0){
                            ei.set("flag","10");
                            IPLAT.confirm({
                                message: '<p>此时段有几辆车预约，确认取消？</p> ',
                                okFn: function (e) {
                                    EiCommunicator.send("LIRL0106", "update", ei, {
                                        onSuccess: function (ei) {
                                            if ("-1" == ei.status) {
                                                NotificationUtil({msg: ei.msg}, "error");
                                                resultGrid.dataSource.page(1);

                                            } else {
                                                resultGrid.removeRows(resultGrid.getCheckedRowsIndex());
                                                NotificationUtil({msg: ei.msg}, "success");
                                                resultGrid.dataSource.page(1);
                                            }
                                        },
                                        onFail: function () {
                                            alert("请求失败");


                                        }
                                    });
                                },
                                cancelFn: function (e) {
                                    resultGrid.dataSource.page(1);
                                },
                                title: '提示框',
                                minWidth: 250
                            });
                        }
                    }, eiInfo);
                }
            },

            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                //只需找到点击的icon，触发click事件即可。
                $.each($(grid.element.find("tr.k-master-row")).find(".k-icon.k-i-expand"),
                    function (i, element) {
                        element.click();
                    });
                $(".k-header.k-grid-toolbar").hide();
                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {
                /**
                 * 保存
                 */
                /*resultGrid.checkAllRows();
                var eiInfo = new EiInfo();
                eiInfo.set("block","result");
                IMOMUtil.submitGridsData("result", "LIRL0106", "update", true, function (e){
                    resultGrid.setEiInfo(e)
                }, eiInfo);*/
            }
        },
        "result2": {
            pageable: {
                pageSize: 100,
                pageSizes: [100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "sfsx",
                    headerTemplate: "是否生效",
                    template: function (e) {
                        if (e.status == "20") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "30px"
                }
            ],
            dataBound: (e) => {
                const childen = e.sender.tbody[0].children; // HTMLCollection
                const rows = Array.from(e.sender.dataSource._data);
                rows.forEach((row, index) => {
                    if (row.status == '20') {
                        childen[index].style.color = '#31be66';
                    }
                });
            },
            onCheckRow: function (e) {

            },
            onCellClick: function (e) {
                result2Grid.unCheckAllRows();
                result2Grid.setCheckedRows(e.row);
                if (e.field === 'sfsx') {
                    let status = e.model["status"];
                    if ("10" == status){
                        e.model["status"] = "20";
                    }else {
                        e.model["status"] = "10";
                    }
                    var ei = new EiInfo();
                    ei.setByNode("result");
                    ei.addBlock(result2Grid.getCheckedBlockData());

                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result2");
                    IMOMUtil.submitGridsData("result2", "LIRL0106", "update2", true, function (e) {
                        if (e.getStatus() > 0) {
                            NotificationUtil({msg: e.msg}, "success");
                            result2Grid.dataSource.page(1);
                        }else if (e.getStatus() == 0){
                            ei.set("flag","10");
                            IPLAT.confirm({
                                message: '<p>此时段有几辆车预约，确认取消？</p> ',
                                okFn: function (e) {
                                    EiCommunicator.send("LIRL0106", "update", ei, {
                                        onSuccess: function (ei) {
                                            if ("-1" == ei.status) {
                                                NotificationUtil({msg: ei.msg}, "error");
                                                result2Grid.dataSource.page(1);

                                            } else {
                                                result2Grid.removeRows(result2Grid.getCheckedRowsIndex());
                                                NotificationUtil({msg: ei.msg}, "success");
                                                result2Grid.dataSource.page(1);
                                            }
                                        },
                                        onFail: function () {
                                            alert("请求失败");
                                            result2Grid.dataSource.page(1);

                                        }
                                    });
                                },
                                cancelFn: function (e) {
                                    result2Grid.dataSource.page(1);
                                },
                                title: '提示框',
                                minWidth: 250
                            });
                        }
                    }, eiInfo);
                }
            },
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                //只需找到点击的icon，触发click事件即可。
                $.each($(grid.element.find("tr.k-master-row")).find(".k-icon.k-i-expand"),
                    function (i, element) {
                        element.click();
                    });
                $(".k-header.k-grid-toolbar").hide();
                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {
                /**
                 * 保存
                 */
                /*resultGrid.checkAllRows();
                var eiInfo = new EiInfo();
                eiInfo.set("block","result");
                IMOMUtil.submitGridsData("result", "LIRL0106", "update", true, function (e){
                    resultGrid.setEiInfo(e)
                }, eiInfo);*/
            }
        },
        "result3": {
            pageable: {
                pageSize: 100,
                pageSizes: [100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "sfsx",
                    headerTemplate: "是否生效",
                    template: function (e) {
                        if (e.status == "20") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "30px"
                }
            ],
            dataBound: (e) => {
                const childen = e.sender.tbody[0].children; // HTMLCollection
                const rows = Array.from(e.sender.dataSource._data);
                rows.forEach((row, index) => {
                    if (row.status == '20') {
                        childen[index].style.color = '#31be66';
                    }
                });
            },
            onCheckRow: function (e) {

            },
            onCellClick: function (e) {
                result3Grid.unCheckAllRows();
                result3Grid.setCheckedRows(e.row);
                if (e.field === 'sfsx') {
                    let status = e.model["status"];
                    if ("10" == status){
                        e.model["status"] = "20";
                    }else {
                        e.model["status"] = "10";
                    }
                    var ei = new EiInfo();
                    ei.setByNode("result");
                    ei.addBlock(result3Grid.getCheckedBlockData());

                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result3");
                    IMOMUtil.submitGridsData("result3", "LIRL0106", "update3", true, function (e) {
                        if (e.getStatus() > 0) {
                            NotificationUtil({msg: e.msg}, "success");
                            result3Grid.dataSource.page(1);
                        }else if (e.getStatus() == 0){
                            ei.set("flag","10");
                            IPLAT.confirm({
                                message: '<p>此时段有几辆车预约，确认取消？</p> ',
                                okFn: function (e) {
                                    EiCommunicator.send("LIRL0106", "update", ei, {
                                        onSuccess: function (ei) {
                                            if ("-1" == ei.status) {
                                                NotificationUtil({msg: ei.msg}, "error");
                                                result3Grid.dataSource.page(1);
                                            } else {
                                                result3Grid.removeRows(result3Grid.getCheckedRowsIndex());
                                                NotificationUtil({msg: ei.msg}, "success");
                                                result3Grid.dataSource.page(1);
                                            }
                                        },
                                        onFail: function () {
                                            alert("请求失败");
                                            result3Grid.dataSource.page(1);
                                        }
                                    });
                                },
                                cancelFn: function (e) {
                                    result3Grid.dataSource.page(1);
                                },
                                title: '提示框',
                                minWidth: 250
                            });
                        }
                    }, eiInfo);
                }
            },
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                //只需找到点击的icon，触发click事件即可。
                $.each($(grid.element.find("tr.k-master-row")).find(".k-icon.k-i-expand"),
                    function (i, element) {
                        element.click();
                    });
                $(".k-header.k-grid-toolbar").hide();
                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {
                /**
                 * 保存
                 */
                /*resultGrid.checkAllRows();
                var eiInfo = new EiInfo();
                eiInfo.set("block","result");
                IMOMUtil.submitGridsData("result", "LIRL0106", "update", true, function (e){
                    resultGrid.setEiInfo(e)
                }, eiInfo);*/
            }
        }
    };

    IPLATUI.EFWindow = {
        //关闭新增子项弹出框时的事件
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
    }

    //获取机组的按钮值
    function machine(segNo) {
        let inInfo = new EiInfo();
        inInfo.set("segNo", segNo);

        var rows = ['周一','周二','周三','周四','周五','周六','周日']
        console.log("按钮值：" + JSON.stringify(rows))
        var gongxu = "";
        $("#gongxu").html(gongxu);
        if (null != rows) {
            for (i = 0; i < rows.length; i++) {
                var machineCodes = rows[i][1];
                if (i == 0) {
                    gongxu = gongxu + '<button class="button_style" type="button" id="' + machineCodes + '";  onclick=' + '"togongxu(\'' + machineCodes + '\')"' + '>' + '周'+machineCodes + '</button>'
                    $("#inqu_status-0-weekDays").val(machineCodes);
                    // resultGrid.dataSource.page(1);
                    selectedList = [];
                    selectedLists = [];
                } else {
                    gongxu = gongxu + '<button class="button_style1" type="button" id="' + machineCodes + '";  onclick=' + '"togongxu(\'' + machineCodes + '\')"' + '>' + '周'+machineCodes + '</button>'
                }
                $("#gongxu").html(gongxu);
            }
        }
    }


});

//机组按钮点击事件
function togongxu(machineCode) {
    // console.log('点击了' )
    $("#inqu_status-0-machineCode").val(machineCode);
    let bb = document.getElementsByClassName('button_style');
    bb[0].classList.add("button_style1");
    bb[0].classList.remove("button_style");
    let aa = document.getElementById(machineCode);
    aa.classList.remove("button_style1");
    aa.classList.add("button_style");
    $("#inqu_status-0-weekDays").val(machineCode);
    resultGrid.dataSource.page(1);
    result2Grid.dataSource.page(1);
    result3Grid.dataSource.page(1);
    selectedList = [];
    selectedLists = [];
}
