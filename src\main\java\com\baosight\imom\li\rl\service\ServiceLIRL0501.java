package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.rl.dao.LIRL0101;
import com.baosight.imom.li.rl.dao.LIRL0501;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 谢徐皓
 * @Description: ${车辆类型管理}
 * @Date: 2024/11/26 16:00
 * @Version: 1.0
 */
public class ServiceLIRL0501 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0501().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        outInfo = super.query(inInfo, LIRL0501.QUERYPAGE);
        return outInfo;
    }


    public EiInfo postExport(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        inInfo.getBlock(EiConstant.resultBlock).set("limit","-999999");
        outInfo = super.query(inInfo, "LIRL0501.querypostExport", new LIRL0501(), false, new LIRL0501().eiMetadata, EiConstant.queryBlock, EiConstant.resultBlock, EiConstant.resultBlock);
        outInfo.removeMeta("result", "tenantId");
        outInfo.removeMeta("result", "delFlag");
        outInfo.removeMeta("result", "archiveFlag");
        outInfo.removeMeta("result", "unitCode");
        outInfo.removeMeta("result", "customerName");
        outInfo.removeMeta("result", "tel");
        outInfo.removeMeta("result", "administrator");
        outInfo.removeMeta("result", "adminIndtity");
        outInfo.removeMeta("result", "identityType");
        outInfo.removeMeta("result", "drUuid");
        outInfo.removeMeta("result", "driverName");
        return outInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo", "");
                //查询数据是否重复
                Map query = new HashMap();
                query.put("vehicleNo", vehicleNo);
                query.put("segNo", segNo);
                query.put("delFlag", 0);
                int count = super.count(LIRL0501.COUNT, query);
                if (count > 0) {
                    hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                    RecordUtils.setRevisor(hashMap);
                    dao.update("LIRL0501.update", hashMap);
                } else {
                    hashMap.put("status", 20);//状态
                    hashMap.put("delFlag", 0);//记录删除标记
                    hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                    hashMap.put("uuid", UUIDHexIdGenerator.generate().toString());//UUID
                    RecordUtils.setCreator(hashMap);
                    dao.insert("LIRL0501.insert", hashMap);
                }
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 启用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 20);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0501.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 禁用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0501.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    public EiInfo queryLease(EiInfo inInfo) {
        String vehicleNo = (String) inInfo.get("vehicleNo");
        String segNo = (String) inInfo.get("segNo");
        HashMap queryMap = new HashMap();
        queryMap.put("vehicleNo", vehicleNo);
        queryMap.put("segNo", segNo);
        List<HashMap> listHashMap = dao.query("LIRL0501.queryAll", queryMap);
        if(listHashMap.size()>0){
            inInfo.set("list",listHashMap);
            inInfo.setMsg("查询成功！");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }else{
            inInfo.setMsg("查询失败！");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }
        return inInfo;
    }

    public EiInfo insertLease(EiInfo inInfo) {
        try {
            String tel = (String) inInfo.get("tel");//手机号
            String administrator = (String) inInfo.get("administrator");//姓名
            String vehicleNo = (String) inInfo.get("vehicleNo");//车牌号
            String remark = (String) inInfo.get("remark");//备注
            HashMap map = new HashMap();
            map.put("segNo", "JC000000");
            map.put("unitCode", "JC000000");
            map.put("vehicleNo", vehicleNo);
            map.put("vehicleType", "1");
            map.put("remark", remark);
            map.put("affiliatedUnit", "30");
            map.put("status", 20);
            map.put("recCreator", tel);
            map.put("recCreatorName", administrator);
            map.put("recRevisor", tel);
            map.put("recRevisorName", administrator);
            map.put("recCreateTime", DateUtil.curDateTimeStr14());// 创建时间
            map.put("recReviseTime", DateUtil.curDateTimeStr14());// 修改时间
            map.put("uuid", UUIDUtils.getUUID());// UUID
            map.put("delFlag", "0");// 删除标记: 0-新增;1-删除;
            map.put("archiveFlag", "0");
            map.put("tenantId", " ");
            map.put("drUuid", " ");
            List<HashMap> listHashMap = dao.query("LIRL0501.querySome", map);
            if(listHashMap.size()>0){
                inInfo.setMsg("添加失败！数据重复");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                return inInfo;
            }
            dao.insert("LIRL0501.insert", map);
            inInfo.setMsg("添加成功！");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception ex){
            inInfo.setMsg(ex.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    public EiInfo deleteLease(EiInfo inInfo) {
        try {
            String tel = (String) inInfo.get("tel");//手机号
            String administrator = (String) inInfo.get("administrator");//姓名
            String vehicleNo = (String) inInfo.get("vehicleNo");//车牌号
            HashMap map = new HashMap();
            map.put("segNo", "JC000000");
            map.put("status", 10);
            map.put("delFlag", 1);
            map.put("vehicleNo", vehicleNo);
            map.put("recRevisor", tel);
            map.put("recRevisorName", administrator);
            map.put("recReviseTime", DateUtil.curDateTimeStr14());// 修改时间
            dao.update("LIRL0501.updateLease", map);
            //同样清空司机信息表
            inInfo.setMsg("删除成功!");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception ex){
            inInfo.setMsg(ex.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }
}
