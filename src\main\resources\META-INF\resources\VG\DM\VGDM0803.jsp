<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFDateSpan startName="inqu_status-0-overhaulStartDate"
                           endName="inqu_status-0-overhaulEndDate" readonly="true"
                           startCname="计划检修日期(起)" endCname="计划检修日期(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                             ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                           startCname="创建时间(起)" endCname="创建时间(止)"
                           ratio="3:3" format="yyyy-MM-dd">
            </EF:EFDateSpan>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-apprStatus" cname="审批状态" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="审批中" value="60"/>
                <EF:EFOption label="审核通过" value="70"/>
                <EF:EFOption label="审核驳回" value="7X"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" autoDraw="no" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" enable="false" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" enable="false" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFComboColumn ename="apprStatus" enable="false" cname="审批状态" align="center" width="70">
                <EF:EFOption label="审批中" value="60"/>
                <EF:EFOption label="审核通过" value="70"/>
                <EF:EFOption label="审核驳回" value="7X"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="comment" cname="审批意见" enable="true" width="120"
                         editType="textarea" maxLength="100"
                         copy="true" sort="flase"/>
            <EF:EFColumn ename="overhaulPlanId" cname="检修计划单号" enable="false" width="130"
                         align="center"/>
            <EF:EFComboColumn ename="overhaulSource" cname="检修来源" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P026"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="voucherNum" cname="依据凭单" width="120" align="center" enable="false"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center" enable="false"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称" enable="false"/>
            <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
            <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
            <EF:EFComboColumn ename="overhaulQuality" cname="检修性质" align="center" width="70"
                              enable="false">
                <EF:EFCodeOption codeName="P024"/>
            </EF:EFComboColumn>
            <EF:EFColumn enable="false" ename="implementManName" cname="实施人" align="center" width="70"/>
            <EF:EFColumn enable="false" ename="overhaulStartDate" cname="计划检修日期(起)" align="center"/>
            <EF:EFColumn enable="false" ename="overhaulEndDate" cname="计划检修日期(止)" align="center"/>
            <EF:EFColumn enable="false" ename="overhaulNumber" cname="计划检修人数" align="right" width="100"/>
            <EF:EFColumn enable="false" ename="outsourcingContactId" cname="委外联络单号" align="center"/>
            <EF:EFColumn enable="false" ename="overhaulProject" cname="计划检修项目"/>
            <EF:EFColumn enable="false" ename="securityMeasures" cname="安全措施"/>
            <EF:EFColumn enable="false" ename="acceptanceCriteria" cname="验收标准"/>
            <EF:EFColumn enable="false" ename="recCreator" cname="创建人" align="center" width="80"/>
            <EF:EFColumn enable="false" ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn enable="false" ename="recCreateTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
            <EF:EFColumn enable="false" ename="recRevisor" cname="修改人" align="center" width="80"/>
            <EF:EFColumn enable="false" ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
            <EF:EFColumn enable="false" ename="recReviseTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow id="overhaulDate" width="90%" height="20%">
        <EF:EFRegion id="inqu4" title="点检计划延期">
            <div class="row">
                <EF:EFDateSpan startName="inqu4_status-0-overhaulStartDate" role="datetime"
                               endName="inqu4_status-0-overhaulEndDate" readonly="true" required="true"
                               startCname="计划检修日期(起)" endCname="计划检修日期(止)" interval="10"
                               ratio="3:3" format="yyyy-MM-dd HH:mm">
                </EF:EFDateSpan>
                <EF:EFButton ename="confirmDealy" cname="延期"/>
            </div>
            <div class="row">
                <EF:EFInput required="true" ename="inqu4_status-0-delayRemark" cname="延期理由"
                            colWidth="12" ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
            </div>
        </EF:EFRegion>
    </EF:EFWindow>
    <EF:EFWindow id="popup" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>