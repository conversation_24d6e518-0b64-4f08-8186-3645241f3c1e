<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-19 11:16:44
       Version :  1.0
    tableName :${meliSchema}.tlirl0201
     RESERVATION_NUMBER  VARCHAR   NOT NULL   primarykey,
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     TYPE_OF_HANDLING  VARCHAR   NOT NULL,
     CUSTOMER_ID  VARCHAR   NOT NULL,
     CUSTOMER_NAME  VARCHAR   NOT NULL,
     DRIVER_NAME  VARCHAR   NOT NULL,
     DRIVER_TEL  VARCHAR   NOT NULL,
     DRIVER_IDENTITY  VARCHAR   NOT NULL,
     VEHIC<PERSON>_NO  VARCHAR   NOT NULL,
     START_OF_TRANSPORT  VARCHAR   NOT NULL,
     PURPOSE_OF_TRANSPORT  VARCHAR   NOT NULL,
     RES<PERSON>VA<PERSON>ON_DATE  VARCHAR   NOT NULL,
     RESERVATION_TIME  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0201">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="reservationNumber">
            a.RESERVATION_NUMBER like concat('%',#reservationNumber#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumberAbs">
            a.RESERVATION_NUMBER = #reservationNumberAbs#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remarkNo">
            a.REMARK not like concat('%',#remarkNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationNumberNo">
            a.RESERVATION_NUMBER not in (#reservationNumberNo#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notStatus">
            a.STATUS != #notStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="typeOfHandling">
            a.TYPE_OF_HANDLING = #typeOfHandling#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            a.CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            a.CUSTOMER_ID = #customerId2#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="arrList">
            a.CUSTOMER_ID IN
            <iterate open="(" close=")" conjunction="," property="arrList">
                #arrList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="and" property="typeOfHandlingList">
            a.TYPE_OF_HANDLING IN
            <iterate open="(" close=")" conjunction="," property="typeOfHandlingList">
                #typeOfHandlingList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="and" property="arrListStatus">
            a.STATUS IN
            <iterate open="(" close=")" conjunction="," property="arrListStatus">
                #arrListStatus[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            a.CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverTel">
            a.DRIVER_TEL like concat('%',#driverTel#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverTelNum">
            a.DRIVER_TEL like concat('%',#driverTelNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverIdentity">
            a.DRIVER_IDENTITY = #driverIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNoCp">
            a.VEHICLE_NO =#vehicleNoCp#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            a.START_OF_TRANSPORT = #startOfTransport#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            a.PURPOSE_OF_TRANSPORT = #purposeOfTransport#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isReservation">
            a.IS_RESERVATION = #isReservation#
        </isNotEmpty>
        <!--<isEmpty prepend=" AND " property="isReservation">
            a.IS_RESERVATION = '10'
        </isEmpty>-->
        <isNotEmpty prepend=" AND " property="reservationDate">
            substr(a.RESERVATION_DATE,1,8) = substr(replace(#reservationDate#,'-',''),1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            a.RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            a.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            a.SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            a.TENANT_ID = #tenantId#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            substr(a.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="reservationTimeEnd">
            substr(a.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationTimeEnd#,'-','')
        </isNotEmpty>
        <!--预约范围-->
        <!--<isNotEmpty prepend=" and " property="reservationDateRange">
            substr(a.RESERVATION_DATE,1,8) <![CDATA[=]]> replace(#reservationDateRange#,'-','')
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="lirl0302IsNull">
            NOT EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.STATUS > '00'
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="leaveFactoryDate">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0311 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and substr(t.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDate#,'-','')
            and t.STATUS > '00'
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNoIsNull">
            NOT EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0301 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.STATUS > '00'
            )
            and not exists (
            select
            1
            from
            ${meliSchema}.tlirl0311 t
            where
            1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.STATUS > '00' )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="flag">
            not exists(
            select 1 from meli.tlirl0203 tlirl0203
            where tlirl0203.SEG_NO=a.SEG_NO
            and tlirl0203.RESERVATION_NUMBER=a.RESERVATION_NUMBER
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceStatus">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0301 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.STATUS in ($carTraceStatus$)
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceOutStatus">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0311 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.STATUS in ($carTraceOutStatus$)
            )
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            (EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0301 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.DEL_FLAG = '0'
            and substr(t.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
            ) or
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0311 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.DEL_FLAG = '0'
            and substr(t.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
            )
                )
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            (EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0301 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.DEL_FLAG = '0'
            and substr(t.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
            ) or EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0311 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.DEL_FLAG = '0'
            and substr(t.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
            )
                )
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0311 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.DEL_FLAG = '0'
            and substr(t.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0311 t
            WHERE 1 = 1
            and t.SEG_NO = a.SEG_NO
            and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and t.DEL_FLAG = '0'
            and substr(t.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="visitUnit">
            VISIT_UNIT like concat('%',#visitUnit#,'%')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0201">
        SELECT
        a.RESERVATION_NUMBER                                                                        as "reservationNumber",
        a.SEG_NO                                                                                    as "segNo",
        (select SEG_NAME
        from iplat4j.TVZBM81
        where SEG_NO = a.SEG_NO
        and DEL_FLAG = 0)                                                                        as "segName",
        a.UNIT_CODE                                                                                 as "unitCode",
        a.STATUS                                                                                    as "status",
        case
        when a.SEG_NO = 'JC000000' THEN
        case
        when a.TYPE_OF_HANDLING = '10' then '90'
        else a.TYPE_OF_HANDLING end
        else a.TYPE_OF_HANDLING end as "typeOfHandling",
        a.CUSTOMER_ID                                                                               as "customerId",
        a.CUSTOMER_NAME                                                                             as "customerName",
        a.DRIVER_NAME                                                                               as "driverName",
        a.DRIVER_TEL                                                                                as "driverTel",
        a.DRIVER_IDENTITY                                                                           as "driverIdentity",
        a.VEHICLE_NO                                                                                as "vehicleNo",
        a.START_OF_TRANSPORT                                                                        as "startOfTransport",
        a.PURPOSE_OF_TRANSPORT                                                                      as "purposeOfTransport",
        a.RESERVATION_DATE                                                                          as "reservationDate",
        a.RESERVATION_TIME                                                                          as "reservationTime",
        a.IS_RESERVATION                                                                            as "isReservation",
        a.REC_CREATOR                                                                               as "recCreator",
        a.REC_CREATOR_NAME                                                                          as "recCreatorName",
        a.REC_CREATE_TIME                                                                           as "recCreateTime",
        a.REC_REVISOR                                                                               as "recRevisor",
        a.REC_REVISOR_NAME                                                                          as "recRevisorName",
        a.REC_REVISE_TIME                                                                           as "recReviseTime",
        a.ARCHIVE_FLAG                                                                              as "archiveFlag",
        a.DEL_FLAG                                                                                  as "delFlag",
        a.REMARK                                                                                    as "remark",
        a.SYS_REMARK                                                                                as "sysRemark",
        a.UUID                                                                                      as "uuid",
        a.TENANT_ID                                                                                 as "tenantId",
        a.VISIT_UNIT as "visitUnit",
        a.IS_SELF_PRODUCED as "isSelfProduced",
        a.SELF_PRODUCED_DESC as "selfProducedDesc",
        ifnull((select t.LATE_EARLY_FLAG
        from MELI.tlirl0302 t
        where t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),
        0)                                                                                   as "lateEarlyFlag",
        ifnull(tlirl0301.CHECK_DATE, tlirl0311.CHECK_DATE)                                          as "checkDate",
        ifnull(ifnull((select QUEUE_DATE
        from MELI.tlirl0402 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        limit 1)), ifnull((select QUEUE_DATE
        from MELI.tlirl0402 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        limit 1)))                          as "callDate",
        ifnull(tlirl0301.ENTER_FACTORY, tlirl0311.ENTER_FACTORY)                                    as "enterFactory",
        ifnull((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where 1 = 1
        and tlirl0406.SEG_NO = a.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1),
        (select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where 1 = 1
        and tlirl0406.SEG_NO = a.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1))                                                                           as "beginEntruckingTime",
        ifnull((select max(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.NEXT_TATGET = '20'
        limit 1),
        (select max(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0407.NEXT_TATGET = '20'
        limit 1))                                                                           as "completeUninstallTime",
        ifnull(tlirl0301.LEAVE_FACTORY_DATE, tlirl0311.LEAVE_FACTORY_DATE)                          as "leaveFactoryDate",
        ifnull(tlirl0301.STATUS, tlirl0311.STATUS)                                                  as "statusWk",
        ifnull(tlirl0301.LEAVE_FACTORY_DATE, tlirl0311.LEAVE_FACTORY_DATE)                          as "leaveFactoryDate",
        case
        when tlirl0301.ENTER_FACTORY != ' ' then
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0)
        else
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0)
        end                                                                                     as "theTimeFromRegistrationToEntry",
        case
        when (select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1) != ' '
        then
        if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s')), 0)
        else
        if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1)), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0311.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0311.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s')), 0)
        end                                                                                     as "enterFactoryLeaveFactoryDate",
        case
        when tlirl0301.LEAVE_FACTORY_DATE != ' ' then
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0)
        else
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')),
        0) end                                                                           as "enterFactoryCompleteUninstallTime",
        ifnull(if(tlirl0301.BEGIN_ENTRUCKING_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s')),
        0),
        ''), if(tlirl0311.BEGIN_ENTRUCKING_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s')),
        0),
        ''))                                                                         "theTimeFromEnteringTheFactoryToTheStartOfTheJob",
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE = 'P007'
        and tedcm01.ITEM_CODE = a.TYPE_OF_HANDLING)                                              as "businessTypeName",
        ifnull(tlirl0301.CAR_TRACE_NO, tlirl0311.CAR_TRACE_NO)                                      as "carTraceNo",
        ifnull(tlirl0301.STATUS, tlirl0311.STATUS)                                                  as "carTraceStatus",
        (select HAND_POINT_NAME
        from meli.tlirl0304 tlirl0304
        where tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID)                           as "currentHandPointName",
        ifnull((select NEXT_TATGET
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        order by REC_CREATE_TIME desc
        limit 1), (select NEXT_TATGET
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = tlirl0311.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        order by REC_CREATE_TIME desc
        limit 1))                                                                as "nextTarget",
        (select HAND_POINT_NAME
        from meli.tlirl0304 tlirl0304
        where tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID)                            as "targetHandPointName"
        FROM MELI.tlirl0201 a
        left join meli.tlirl0301 tlirl0301 on
        a.SEG_NO = tlirl0301.SEG_NO
        and a.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        and a.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0301.STATUS != '00'
        left join meli.tlirl0311 tlirl0311 on
        a.SEG_NO = tlirl0311.SEG_NO and
        a.RESERVATION_NUMBER = tlirl0311.RESERVATION_NUMBER
        and a.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0311.STATUS != '00'
        WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE desc,
                a.RESERVATION_TIME asc,
                a.TYPE_OF_HANDLING asc,
                a.START_OF_TRANSPORT asc,
                a.PURPOSE_OF_TRANSPORT asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryTimeoutData" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0201">
        SELECT
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(00：撤销，10：新增) -->
        a.TYPE_OF_HANDLING as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.START_OF_TRANSPORT as "startOfTransport",  <!-- 运输起始地 -->
        a.PURPOSE_OF_TRANSPORT as "purposeOfTransport",  <!-- 运输目的地 -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.IS_RESERVATION as "isReservation"  <!-- 是否有预约单 -->
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE desc,
                a.RESERVATION_TIME asc,
                a.TYPE_OF_HANDLING asc,
                a.START_OF_TRANSPORT asc,
                a.PURPOSE_OF_TRANSPORT asc
            </isEmpty>
        </dynamic>

    </select>
    <select id="queryReversion" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0201">
        SELECT
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(00：撤销，10：新增) -->
        a.TYPE_OF_HANDLING as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.START_OF_TRANSPORT as "startOfTransport",  <!-- 运输起始地 -->
        a.PURPOSE_OF_TRANSPORT as "purposeOfTransport",  <!-- 运输目的地 -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.IS_RESERVATION	as "isReservation",  <!-- 是否有预约单 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.VISIT_UNIT as "visitUnit",
        ifnull((
        select
        t.LATE_EARLY_FLAG
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),0) as "lateEarlyFlag",
        ifnull((
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "checkDate",
        ifnull((
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0402 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1),(
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0409 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1))
        as "callDate",
        ifnull((
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "enterFactory",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "statusWk",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "beginEntruckingTimeCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryLeaveFactoryDate",
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE =
        'P007'
        and tedcm01.ITEM_CODE = a.TYPE_OF_HANDLING) as "businessTypeName"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE,RESERVATION_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryReversion1" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT tlirl0201.RESERVATION_NUMBER as "reservationNumber"
        FROM MELI.tlirl0201 tlirl0201,
             meli.tlirl0301 tlirl0301
        WHERE 1 = 1
          AND tlirl0201.SEG_NO=tlirl0301.SEG_NO
          AND tlirl0201.RESERVATION_NUMBER=tlirl0301.RESERVATION_NUMBER
          AND tlirl0301.CAR_TRACE_NO=#carTraceNo#
          AND tlirl0201.VEHICLE_NO=tlirl0301.VEHICLE_NO
          and tlirl0201.STATUS='20'
            limit 1
    </select>

    <select id="queryExport" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.RESERVATION_NUMBER                                                                        as "reservationNumber",
        a.SEG_NO                                                                                    as "segNo",
        (select SEG_NAME
        from iplat4j.TVZBM81
        where SEG_NO = a.SEG_NO
        and DEL_FLAG = 0)                                                                        as "segName",
        a.UNIT_CODE                                                                                 as "unitCode",
        a.STATUS                                                                                    as "status",
        a.TYPE_OF_HANDLING                                                                          as "typeOfHandling",
        a.CUSTOMER_ID                                                                               as "customerId",
        a.CUSTOMER_NAME                                                                             as "customerName",
        a.DRIVER_NAME                                                                               as "driverName",
        a.DRIVER_TEL                                                                                as "driverTel",
        a.DRIVER_IDENTITY                                                                           as "driverIdentity",
        a.VEHICLE_NO                                                                                as "vehicleNo",
        a.START_OF_TRANSPORT                                                                        as "startOfTransport",
        a.PURPOSE_OF_TRANSPORT                                                                      as "purposeOfTransport",
        a.RESERVATION_DATE                                                                          as "reservationDate",
        a.RESERVATION_TIME                                                                          as "reservationTime",
        a.IS_RESERVATION                                                                            as "isReservation",
        a.REC_CREATOR                                                                               as "recCreator",
        a.REC_CREATOR_NAME                                                                          as "recCreatorName",
        a.REC_CREATE_TIME                                                                           as "recCreateTime",
        a.REC_REVISOR                                                                               as "recRevisor",
        a.REC_REVISOR_NAME                                                                          as "recRevisorName",
        a.REC_REVISE_TIME                                                                           as "recReviseTime",
        a.ARCHIVE_FLAG                                                                              as "archiveFlag",
        a.DEL_FLAG                                                                                  as "delFlag",
        a.REMARK                                                                                    as "remark",
        a.SYS_REMARK                                                                                as "sysRemark",
        a.UUID                                                                                      as "uuid",
        a.TENANT_ID                                                                                 as "tenantId",
        a.VISIT_UNIT as "visitUnit",
        ifnull((select t.LATE_EARLY_FLAG
        from MELI.tlirl0302 t
        where t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),
        0)                                                                                   as "lateEarlyFlag",
        ifnull(tlirl0301.CHECK_DATE, tlirl0311.CHECK_DATE)                                          as "checkDate",
        ifnull(ifnull((select QUEUE_DATE
        from MELI.tlirl0402 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        limit 1)), ifnull((select QUEUE_DATE
        from MELI.tlirl0402 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        limit 1), (select QUEUE_DATE
        from MELI.tlirl0409 t
        where t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        limit 1)))                          as "callDate",
        ifnull(tlirl0301.ENTER_FACTORY, tlirl0311.ENTER_FACTORY)                                    as "enterFactory",
        ifnull((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where 1 = 1
        and tlirl0406.SEG_NO = a.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1),
        (select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where 1 = 1
        and tlirl0406.SEG_NO = a.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1))                                                                           as "beginEntruckingTime",
        ifnull((select max(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.NEXT_TATGET = '20'
        limit 1),
        (select max(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = a.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0407.NEXT_TATGET = '20'
        limit 1))                                                                           as "completeUninstallTime",
        ifnull(tlirl0301.LEAVE_FACTORY_DATE, tlirl0311.LEAVE_FACTORY_DATE)                          as "leaveFactoryDate",
        ifnull(tlirl0301.STATUS, tlirl0311.STATUS)                                                  as "statusWk",
        ifnull(tlirl0301.LEAVE_FACTORY_DATE, tlirl0311.LEAVE_FACTORY_DATE)                          as "leaveFactoryDate",
        case
        when tlirl0301.ENTER_FACTORY != ' ' then
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0)
        else
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.CHECK_DATE, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0)
        end                                                                                     as "theTimeFromRegistrationToEntry",
        case
        when (select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1) != ' '
        then
        if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1)), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        limit 1), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s')), 0)
        else
        if(
        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1)), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0311.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE((select min(tlirl0406.LOAD_DATE)
        from meli.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1), '%Y%m%d%H%i%s'),
        STR_TO_DATE((select min(tlirl0407.FINISH_LOAD_DATE)
        from meli.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0311.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and NEXT_TATGET = '20'
        limit 1), '%Y%m%d%H%i%s')), 0)
        end                                                                                     as "enterFactoryLeaveFactoryDate",
        case
        when tlirl0301.LEAVE_FACTORY_DATE != ' ' then
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0301.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0)
        else
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(tlirl0311.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')),
        0) end                                                                           as "enterFactoryCompleteUninstallTime",
        ifnull(if(tlirl0301.BEGIN_ENTRUCKING_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0301.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s')),
        0),
        ''), if(tlirl0311.BEGIN_ENTRUCKING_TIME != ' ',
        if((TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s'))) >
        0,
        TIMESTAMPDIFF(minute, STR_TO_DATE(tlirl0311.ENTER_FACTORY, '%Y%m%d%H%i%s'),
        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)
        from MELI.tlirl0406 tlirl0406
        where tlirl0406.SEG_NO = tlirl0311.SEG_NO
        and tlirl0406.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0406.VEHICLE_NO = tlirl0311.VEHICLE_NO
        and tlirl0406.STATUS != '00'
        limit 1)), '%Y%m%d%H%i%s')),
        0),
        ''))                                                                         "theTimeFromEnteringTheFactoryToTheStartOfTheJob",
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE = 'P007'
        and tedcm01.ITEM_CODE = a.TYPE_OF_HANDLING)                                              as "businessTypeName",
        ifnull(tlirl0301.CAR_TRACE_NO, tlirl0311.CAR_TRACE_NO)                                      as "carTraceNo",
        ifnull(tlirl0301.STATUS, tlirl0311.STATUS)                                                  as "carTraceStatus",
        (select HAND_POINT_NAME
        from meli.tlirl0304 tlirl0304
        where tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID)                           as "currentHandPointName",
        ifnull((select NEXT_TATGET
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        order by REC_CREATE_TIME desc
        limit 1), (select NEXT_TATGET
        from meli.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.SEG_NO = tlirl0311.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0311.VEHICLE_NO
        order by REC_CREATE_TIME desc
        limit 1))                                                                as "nextTarget",
        (select HAND_POINT_NAME
        from meli.tlirl0304 tlirl0304
        where tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID)                            as "targetHandPointName"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE,RESERVATION_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <include refid="condition"/>
    </select>


    <select id="queryOverTimeReversion" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0201">
        SELECT
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(00：撤销，10：新增) -->
        a.TYPE_OF_HANDLING as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.START_OF_TRANSPORT as "startOfTransport",  <!-- 运输起始地 -->
        a.PURPOSE_OF_TRANSPORT as "purposeOfTransport",  <!-- 运输目的地 -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        ifnull((
        select
        t.LATE_EARLY_FLAG
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),0) as "lateEarlyFlag",
        ifnull((
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "checkDate",
        ifnull((
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0402 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1),(
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0409 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1))
        as "callDate",
        ifnull((
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "enterFactory",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "statusWk",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "beginEntruckingTimeCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryLeaveFactoryDate",
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE =
        'P007'
        and tedcm01.ITEM_CODE = a.TYPE_OF_HANDLING) as "businessTypeName"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE,RESERVATION_TIME asc
            </isEmpty>
        </dynamic>

    </select>
    <select id="queryUnregistered" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT RESERVATION_NUMBER as "reservationNumber",
               SEG_NO as "segNo",
               DRIVER_NAME as "driverName",
               DRIVER_TEL as "driverTel",
               VEHICLE_NO as "vehicleNo"
        FROM meli.tlirl0201 tlirl0201
        WHERE tlirl0201.SEG_NO = #segNo#
          and tlirl0201.STATUS = '20'
          AND (NOW() > DATE_ADD(
                DATE_FORMAT(STR_TO_DATE(LEFT(CONCAT(tlirl0201.RESERVATION_DATE,
                                            REPLACE(REPLACE(tlirl0201.RESERVATION_TIME, ':', ''), '-', '')), 14),
                                    '%Y%m%d%H%i%s'),
                            '%Y-%m-%d %H:%i:%s'),
                INTERVAL #preTime# HOUR
    ))
          and not exists(select 1
                         from meli.tlirl0301 tlirl0301
                         where tlirl0301.SEG_NO = tlirl0201.SEG_NO
                           and tlirl0301.RESERVATION_NUMBER = tlirl0201.RESERVATION_NUMBER)
        order by tlirl0201.RESERVATION_DATE desc
    </select>
    <!--
        <isNotEmpty prepend=" AND " property="reservationNumber">
            RESERVATION_NUMBER = #reservationNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="typeOfHandling">
            TYPE_OF_HANDLING = #typeOfHandling#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverTel">
            DRIVER_TEL = #driverTel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverIdentity">
            DRIVER_IDENTITY = #driverIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startOfTransport">
            START_OF_TRANSPORT = #startOfTransport#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="purposeOfTransport">
            PURPOSE_OF_TRANSPORT = #purposeOfTransport#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationDate">
            RESERVATION_DATE = #reservationDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationTime">
            RESERVATION_TIME = #reservationTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0201 (RESERVATION_NUMBER,  <!-- 预约单号 -->
        SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        STATUS,  <!-- 状态(00：撤销，10：新增) -->
        TYPE_OF_HANDLING,  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        CUSTOMER_ID,  <!-- 承运商/客户代码 -->
        CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
        DRIVER_NAME,  <!-- 司机姓名 -->
        DRIVER_TEL,  <!-- 司机电话 -->
        DRIVER_IDENTITY,  <!-- 司机身份 -->
        VEHICLE_NO,  <!-- 车牌号 -->
        START_OF_TRANSPORT,  <!-- 运输起始地 -->
        PURPOSE_OF_TRANSPORT,  <!-- 运输目的地 -->
        RESERVATION_DATE,  <!-- 预约日期 -->
        RESERVATION_TIME,  <!-- 预约时段 -->
        IS_RESERVATION,  <!-- 是否有预约单 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID,  <!-- 租户ID -->
        IS_SELF_PRODUCED,
        SELF_PRODUCED_DESC,
        VISIT_UNIT
        )
        VALUES (#reservationNumber#, #segNo#, #unitCode#, #status#, #typeOfHandling#, #customerId#, #customerName#,
        #driverName#, #driverTel#, #driverIdentity#, #vehicleNo#, #startOfTransport#, #purposeOfTransport#,
        #reservationDate#, #reservationTime#, #isReservation#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#,
        #uuid#, #tenantId#,#isSelfProduced#,#selfProducedDesc#, #visitUnit#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0201 WHERE
        RESERVATION_NUMBER = #reservationNumber#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0201
        SET
        SEG_NO = #segNo#,   <!-- 业务单元代代码 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        STATUS = #status#,   <!-- 状态(00：撤销，10：新增) -->
        TYPE_OF_HANDLING = #typeOfHandling#,   <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        CUSTOMER_ID = #customerId#,   <!-- 承运商/客户代码 -->
        CUSTOMER_NAME = #customerName#,   <!-- 承运商/客户名称 -->
        DRIVER_NAME = #driverName#,   <!-- 司机姓名 -->
        DRIVER_TEL = #driverTel#,   <!-- 司机电话 -->
        DRIVER_IDENTITY = #driverIdentity#,   <!-- 司机身份 -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        START_OF_TRANSPORT = #startOfTransport#,   <!-- 运输起始地 -->
        PURPOSE_OF_TRANSPORT = #purposeOfTransport#,   <!-- 运输目的地 -->
        RESERVATION_DATE = #reservationDate#,   <!-- 预约日期 -->
        RESERVATION_TIME = #reservationTime#,   <!-- 预约时段 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        TENANT_ID = #tenantId#,  <!-- 租户ID -->
        IS_SELF_PRODUCED=#isSelfProduced#,
        SELF_PRODUCED_DESC=#selfProducedDesc#
        WHERE
        1=1
          and SEG_NO=#segNo#
          and RESERVATION_NUMBER = #reservationNumber#
    </update>


    <update id="updateByCustomerInfo">
        UPDATE ${meliSchema}.tlirl0201
        SET
        CUSTOMER_ID = #customerId#,   <!-- 承运商/客户代码 -->
        CUSTOMER_NAME = #customerName#  <!-- 承运商/客户名称 -->
        WHERE
        RESERVATION_NUMBER = #reservationNumber#
    </update>

    <update id="deleteByTel">
        UPDATE ${meliSchema}.tlirl0201
        SET
        STATUS = '00',   <!-- 状态(00：撤销，10：新增) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = '1'  <!-- 记录删除标记 -->
        WHERE
        DRIVER_TEL = #driverTel#
        and STATUS = '20'
    </update>

    <update id="deleteByUUID">
        UPDATE ${meliSchema}.tlirl0201
        SET
        STATUS = '00',   <!-- 状态(00：撤销，10：新增) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = '1'  <!-- 记录删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="deleteByReservationNumber">
        UPDATE ${meliSchema}.tlirl0201
        SET
        STATUS = '00',   <!-- 状态(00：撤销，10：新增) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = '1'  <!-- 记录删除标记 -->
        <isNotEmpty prepend=" , " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        and RESERVATION_NUMBER = #reservationNumber#
        and IS_RESERVATION = '00'
    </update>


    <update id="deleteByReservation">
        UPDATE ${meliSchema}.tlirl0201
        SET
        STATUS = '00',   <!-- 状态(00：撤销，10：新增) -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = '1'  <!-- 记录删除标记 -->
        <isNotEmpty prepend=" , " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        and RESERVATION_NUMBER = #reservationNumber#
    </update>

    <select id="countNumByReservationTime" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0201 a WHERE 1=1

    </select>

    <!--更新预约单的状态-->
    <update id="updateStatus">
        UPDATE ${meliSchema}.tlirl0201
        SET
        STATUS = #status#,
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#<!-- 状态(00：撤销，10：新增) -->
        <isNotEmpty prepend=" , " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        WHERE
        SEG_NO = #segNo#
        and
        RESERVATION_NUMBER = #reservationNumber#
    </update>

    <select id="queryVehicleNo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT distinct
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.VEHICLE_NO as "vehicleNo",
        a.DRIVER_TEL  as "driverTel" <!-- 司机手机号 -->
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        AND a.STATUS = '20'
        <include refid="condition"/>
        <isNotEmpty prepend="AND" property="flag">
            NOT EXISTS(SELECT 1
            FROM ${meliSchema}.tlirl0301 tlirl0301
            WHERE 1 = 1
            AND tlirl0301.SEG_NO = a.SEG_NO
            and tlirl0301.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and tlirl0301.STATUS != '00')
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_TIME asc
            </isEmpty>
        </dynamic>
    </select>

<!--卸货配单查询车辆信息-->
    <select id="queryVehicleNoByDistOrder"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select distinct t0301.VEHICLE_NO   as "vehicleNo",
        t0301.HAND_TYPE    as "handType",
        t0301.DRIVER_NAME  as "driverName",
        t0301.TEL_NUM      as "driverTel",
        t0301.CAR_TRACE_NO as "carTraceNo"
        from meli.tlirl0301 t0301,
        meli.tlirl0102 tlirl0102
        where t0301.SEG_NO = #segNo#
        and tlirl0102.SEG_NO = t0301.SEG_NO
        and tlirl0102.TEL = t0301.TEL_NUM
        and tlirl0102.STATUS = '20'
        and t0301.STATUS = '20'   <!-- 查询进厂状态的 -->
        and t0301.TEL_NUM=#tel#
        and t0301.HAND_TYPE in ('20','30')
        AND t0301.TARGET_HAND_POINT_ID =' '
        and exists(select 1
        from meli.tlirl0201 tlirl0201
        where 1 = 1
        and tlirl0201.SEG_NO = t0301.SEG_NO
        and tlirl0201.RESERVATION_NUMBER = t0301.RESERVATION_NUMBER
        and tlirl0201.TYPE_OF_HANDLING in ('20', '30'))
        <isNotEmpty prepend="and" property="customerIdList">
            tlirl0102.CUSTOMER_ID IN
            <iterate open="(" close=")" conjunction="," property="customerIdList">
                #customerIdList[]#
            </iterate>
        </isNotEmpty>
        union
        select distinct t0301.VEHICLE_NO   as "vehicleNo",
        t0301.HAND_TYPE    as "handType",
        t0301.DRIVER_NAME  as "driverName",
        t0301.TEL_NUM      as "driverTel",
        t0301.CAR_TRACE_NO as "carTraceNo"
        from meli.tlirl0301 t0301,
        meli.tlirl0102 tlirl0102
        where t0301.SEG_NO = #segNo#
        and tlirl0102.SEG_NO = t0301.SEG_NO
        and tlirl0102.TEL = t0301.TEL_NUM
        and tlirl0102.STATUS = '20'
        and t0301.TEL_NUM=#tel#
        and exists(select 1
        from meli.tlirl0501 tlirl0501
        where 1 = 1
        and tlirl0501.SEG_NO = t0301.SEG_NO
        and tlirl0501.VEHICLE_NO = t0301.VEHICLE_NO
        and tlirl0501.VEHICLE_TYPE = '1'
        and tlirl0501.STATUS = '20')
        <isNotEmpty prepend="and" property="customerIdList">
            tlirl0102.CUSTOMER_ID IN
            <iterate open="(" close=")" conjunction="," property="customerIdList">
                #customerIdList[]#
            </iterate>
        </isNotEmpty>
        and t0301.STATUS = '20'
        AND t0301.TARGET_HAND_POINT_ID =' '
        union
        select distinct t0301.VEHICLE_NO   as "vehicleNo",
        t0301.HAND_TYPE    as "handType",
        t0301.DRIVER_NAME  as "driverName",
        t0301.TEL_NUM      as "driverTel",
        t0301.CAR_TRACE_NO as "carTraceNo"
        from meli.tlirl0301 t0301,
        meli.tlirl0201 tlirl0201
        where t0301.SEG_NO = #segNo#
        and tlirl0201.SEG_NO = t0301.SEG_NO
        and tlirl0201.DRIVER_TEL = t0301.TEL_NUM
        and tlirl0201.STATUS = '20'
        and t0301.TEL_NUM = #tel#
        AND tlirl0201.CUSTOMER_ID IS NULL
        and t0301.HAND_TYPE in ('20', '30')
        and t0301.STATUS = '20'
        AND t0301.TARGET_HAND_POINT_ID =' '
    </select>


    <select id="queryReversionInfo" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0201">
        SELECT
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(00：撤销，10：新增) -->
        a.TYPE_OF_HANDLING as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.START_OF_TRANSPORT as "startOfTransport",  <!-- 运输起始地 -->
        a.PURPOSE_OF_TRANSPORT as "purposeOfTransport",  <!-- 运输目的地 -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.IS_RESERVATION	as "isReservation",  <!-- 是否有预约单 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        ifnull((
        select
        t.LATE_EARLY_FLAG
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),0) as "lateEarlyFlag",
        ifnull((
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "checkDate",
        ifnull((
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0402 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1),(
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0409 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1))
        as "callDate",
        ifnull((
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "enterFactory",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "statusWk",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "beginEntruckingTimeCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryLeaveFactoryDate",
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE =
        'P007'
        and tedcm01.ITEM_CODE = a.TYPE_OF_HANDLING) as "businessTypeName"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryReversionInfoWechat" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(00：撤销，10：新增) -->
        a.TYPE_OF_HANDLING as "typeOfHandling",  <!-- 装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货) -->
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.START_OF_TRANSPORT as "startOfTransport",  <!-- 运输起始地 -->
        a.PURPOSE_OF_TRANSPORT as "purposeOfTransport",  <!-- 运输目的地 -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.IS_RESERVATION	as "isReservation",  <!-- 是否有预约单 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        ifnull((
        select
        t.LATE_EARLY_FLAG
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),0) as "lateEarlyFlag",
        ifnull((
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "checkDate",
        ifnull((
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0402 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1),(
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0409 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1))
        as "callDate",
        ifnull((
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "enterFactory",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "statusWk",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "leaveFactoryDate",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "beginEntruckingTimeCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ))  as "enterFactoryLeaveFactoryDate",
        (select tedcm01.ITEM_CNAME
        from iplat4j.tedcm01 tedcm01
        where 1 = 1
        and tedcm01.CODESET_CODE =
        'P007'
        and tedcm01.ITEM_CODE = a.TYPE_OF_HANDLING) as "businessTypeName"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
        LIMIT $offset$, $pageSize$
    </select>

    <select id="queryReversionInfoWechatCount" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
            COUNT(1) as count
        FROM MELI.tlirl0201 a
        LEFT JOIN MELI.tlirl0301 t1 ON
        t1.SEG_NO = a.SEG_NO
        AND t1.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        AND t1.DEL_FLAG = '0'
        LEFT JOIN MELI.tlirl0402 t2 ON
        t2.VEHICLE_NO = a.VEHICLE_NO
        AND t2.SEG_NO = a.SEG_NO
        WHERE 1 = 1
        <include refid="condition"/>
    </select>

    <select id="queryHashMap" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.RESERVATION_NUMBER as "reservationNumber",  <!-- 预约单号 -->
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.STATUS as "status",  <!-- 状态(00：撤销，10：新增) -->
        ifnull(( select if(BUSINESS_TYPE = '', null,
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = BUSINESS_TYPE
        and tedcm01.CODESET_CODE = 'P010')) from ${meliSchema}.tlirl0302 b where 1 = 1
        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER and b.DEL_FLAG = '0' limit 1),
        (select ITEM_CNAME from iplat4j.tedcm01 tedcm01
        where tedcm01.ITEM_CODE = TYPE_OF_HANDLING
        and tedcm01.CODESET_CODE = 'P007')) "handTypeName",
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        ifnull(if((
        select
        t.CUSTOMER_NAME
        from
        ${meliSchema}.tlirl0307 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VOUCHER_NUM = (
        select
        t.VOUCHER_NUM
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)
        limit 1) = '',a.CUSTOMER_NAME,
        (
        select
        t.CUSTOMER_NAME
        from
        ${meliSchema}.tlirl0307 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VOUCHER_NUM = (
        select
        t.VOUCHER_NUM
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)
        limit 1)
        ),a.CUSTOMER_NAME) as "customerName",  <!-- 承运商/客户名称 -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机电话 -->
        a.DRIVER_TEL as "telNum",  <!-- 司机电话 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.START_OF_TRANSPORT as "startOfTransport",  <!-- 运输起始地 -->
        a.PURPOSE_OF_TRANSPORT as "purposeOfTransport",  <!-- 运输目的地 -->
        a.RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        STR_TO_DATE(a.RESERVATION_DATE,'%Y%m%d%H%i%s')  as "reservationDate1",  <!-- 预约日期(转换) -->
        a.RESERVATION_TIME as "reservationTime",  <!-- 预约时段 -->
        a.IS_RESERVATION as "isReservation",  <!-- 是否有预约单 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        ifnull((
        select
        t.LATE_EARLY_FLAG
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),0) as "lateEarlyFlag",
        ifnull((
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0302 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.CHECK_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1))  as "checkDate",
        ifnull((
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0402 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1),(
        select
        QUEUE_DATE
        from
        ${meliSchema}.tlirl0409 t
        where
        t.SEG_NO = a.SEG_NO
        and t.DEL_FLAG = '0'
        and t.VEHICLE_NO = a.VEHICLE_NO
        and t.CAR_TRACE_NO = (
        select
        t2.CAR_TRACE_NO
        from
        ${meliSchema}.tlirl0302 t2
        where
        t2.SEG_NO = a.SEG_NO
        and t2.STATUS != '00'
        and t2.VEHICLE_NO = a.VEHICLE_NO
        and t2.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t2.DEL_FLAG = '0'
        limit 1)
        limit 1))
        as "callDate",
        ifnull((
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.ENTER_FACTORY
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "enterFactory",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "leaveFactoryDate",
        ifnull((
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.STATUS
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "statusWk",
        ifnull((
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.BEGIN_ENTRUCKING_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "beginEntruckingTime",
        ifnull((
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.COMPLETE_UNINSTALL_TIME
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "completeUninstallTime",
        ifnull((
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1),(
        select
        t.LEAVE_FACTORY_DATE
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1)) as "leaveFactoryDate",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(BEGIN_ENTRUCKING_TIME,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 )) as "beginEntruckingTimeCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(COMPLETE_UNINSTALL_TIME,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 )) as "enterFactoryCompleteUninstallTime",
        ifnull((
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0301 t
        where
        t.SEG_NO = a.SEG_NO
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 ),(
        select
        if((TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')))>0,
        TIMESTAMPDIFF(minute,
        STR_TO_DATE(CHECK_DATE,
        '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEAVE_FACTORY_DATE,
        '%Y%m%d%H%i%s')),
        0)
        from
        ${meliSchema}.tlirl0311 t
        where
        t.SEG_NO = a.SEG_NO
        and t.STATUS != '00'
        and t.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        and t.DEL_FLAG = '0'
        limit 1 )) as "enterFactoryLeaveFactoryDate",
        a.TYPE_OF_HANDLING as "handType"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <isNotEmpty prepend="and" property="flag1">
            not exists(select 1
            from meli.tlirl0301 tlirl0301
            where tlirl0301.SEG_NO = a.SEG_NO
            and tlirl0301.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and tlirl0301.VEHICLE_NO = a.VEHICLE_NO
            and tlirl0301.STATUS!='00'
            limit 1)
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE asc,
                a.RESERVATION_TIME asc
            </isEmpty>
        </dynamic>

    </select>


    <select id="queryHashMapAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT a.RESERVATION_NUMBER                                                            as "reservationNumber",
        a.DRIVER_NAME as driverName,
        a.DRIVER_TEL as "driverTel",
        a.RESERVATION_DATE as "reservationDate"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        <!--        and a.STATUS !='00'-->
        <include refid="condition"/>
        <isNotEmpty prepend="and" property="flag1">
            not exists(select 1
            from meli.tlirl0301 tlirl0301
            where tlirl0301.SEG_NO = a.SEG_NO
            and tlirl0301.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and tlirl0301.VEHICLE_NO = a.VEHICLE_NO
            and tlirl0301.STATUS!='00'
            limit 1)
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.RESERVATION_DATE asc,
                a.RESERVATION_TIME asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryEnteringTheFactory" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
    select SEG_NO	as "segNo",  <!-- 业务单元代码 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
        reservation_number	as "reservationNumber",  <!-- 预约号 -->
        STATUS	as "status",  <!-- 状态 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
        REMARK	as "remark",  <!-- 备注 -->
        VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
        UUID	as "uuid",  <!-- uuid -->
        TENANT_ID	as "tenantId",  <!-- 租户ID -->
        TYPE_OF_HANDLING          as "typeOfHandling" ,<!-- 装卸业务 -->
        RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        RESERVATION_TIME as "reservationTime",  <!-- 预约时间 -->
        DRIVER_IDENTITY as "driverIdentity",  <!-- 驾驶员身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        DRIVER_TEL as "driverTel",  <!-- 驾驶员手机号 -->
        VEHICLE_TYPE as "vehicleType",
        VISIT_UNIT as "visitUnit"
    from(SELECT
    SEG_NO	,  <!-- 业务单元代码 -->
    UNIT_CODE	,  <!-- 业务单元代码 -->
    reservation_number	,  <!-- 预约号 -->
    STATUS	,  <!-- 状态 -->
    REC_CREATOR	,  <!-- 记录创建人 -->
    REC_CREATOR_NAME	,  <!-- 记录创建人姓名 -->
    REC_CREATE_TIME	,  <!-- 记录创建时间 -->
    REC_REVISOR	,  <!-- 记录修改人 -->
    REC_REVISOR_NAME	,  <!-- 记录创建人姓名 -->
    REC_REVISE_TIME	,  <!-- 记录修改时间 -->
    ARCHIVE_FLAG	,  <!-- 归档标记 -->
    DEL_FLAG	,  <!-- 记录删除标记 -->
    REMARK	,  <!-- 备注 -->
    VEHICLE_NO	,  <!-- 车牌号 -->
    UUID	,  <!-- uuid -->
    TENANT_ID	,  <!-- 租户ID -->
    TYPE_OF_HANDLING          ,<!-- 装卸业务 -->
    RESERVATION_DATE ,  <!-- 预约日期 -->
    RESERVATION_TIME ,  <!-- 预约时间 -->
    DRIVER_IDENTITY ,  <!-- 驾驶员身份证号 -->
    DRIVER_NAME ,  <!-- 驾驶员姓名 -->
    DRIVER_TEL,  <!-- 驾驶员手机号 -->
        '0' as "VEHICLE_TYPE",
        VISIT_UNIT
    FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        and a.STATUS = '20'
        and a.DEL_FLAG = 0
        and substr(a.RESERVATION_DATE,1,8) = DATE_FORMAT(now(), '%Y%m%d')  <!-- 取当天的数据 -->
        and not exists(select 1
        from ${meliSchema}.tlirl0301 t0301
        where t0301.SEG_NO = a.SEG_NO
        and t0301.VEHICLE_NO = a.VEHICLE_NO
        and t0301.DEL_FLAG = 0
        and t0301.STATUS in ('20', '30', '40'))  <!-- 排除已经入厂的车辆 -->
    <include refid="condition"/>
    union
        SELECT
        SEG_NO	,  <!-- 业务单元代码 -->
        UNIT_CODE	,  <!-- 业务单元代码 -->
        reservation_number	,  <!-- 预约号 -->
        STATUS	,  <!-- 状态 -->
        REC_CREATOR	,  <!-- 记录创建人 -->
        REC_CREATOR_NAME	,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	,  <!-- 记录创建时间 -->
        REC_REVISOR	,  <!-- 记录修改人 -->
        REC_REVISOR_NAME	,  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME	,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	,  <!-- 归档标记 -->
        DEL_FLAG	,  <!-- 记录删除标记 -->
        REMARK	,  <!-- 备注 -->
        VEHICLE_NO	,  <!-- 车牌号 -->
        UUID	,  <!-- uuid -->
        TENANT_ID	,  <!-- 租户ID -->
        TYPE_OF_HANDLING          ,<!-- 装卸业务 -->
        RESERVATION_DATE ,  <!-- 预约日期 -->
        RESERVATION_TIME ,  <!-- 预约时间 -->
        DRIVER_IDENTITY ,  <!-- 驾驶员身份证号 -->
        DRIVER_NAME ,  <!-- 驾驶员姓名 -->
        DRIVER_TEL,  <!-- 驾驶员手机号 -->
        '0' as "VEHICLE_TYPE",
        VISIT_UNIT
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        and a.STATUS = '20'
        and a.DEL_FLAG = 0
        and not exists(select 1
        from ${meliSchema}.tlirl0301 t0301
        where t0301.SEG_NO = a.SEG_NO
        and t0301.VEHICLE_NO = a.VEHICLE_NO
        and t0301.DEL_FLAG = 0
        and t0301.STATUS in ('20', '30', '40'))  <!-- 排除已经入厂的车辆 -->
        and  exists(select 1
        FROM meli.tlirl0201 a1
        WHERE 1 = 1
        and a1.STATUS = '20'
        and a1.DEL_FLAG = 0
        and a1.DRIVER_TEL = a.DRIVER_TEL
        and a1.DRIVER_IDENTITY = a.DRIVER_IDENTITY
        and a.RESERVATION_DATE != DATE_FORMAT(now(), '%Y%m%d')) <!-- 排除当天已经预约的 -->
        and a.RESERVATION_NUMBER = (select max(a1.RESERVATION_NUMBER)
        FROM meli.tlirl0201 a1
        WHERE 1 = 1
        and a1.STATUS = '20'
        and a1.DEL_FLAG = 0
        and a1.DRIVER_TEL = a.DRIVER_TEL
        and a1.DRIVER_IDENTITY = a.DRIVER_IDENTITY
        and substr(a.RESERVATION_DATE,1,8) != DATE_FORMAT(now(), '%Y%m%d'))  <!-- 取今天过后已经预约的最后一条记录 -->
        <include refid="condition"/>

    union
        SELECT b.SEG_NO              as SEG_NO,
        b.UNIT_CODE                  as UNIT_CODE,
        ''                           as reservation_number,
        '20'                         as STATUS,
        c.REC_CREATOR                as REC_CREATOR,
        c.REC_CREATOR_NAME           as REC_CREATOR_NAME,
        DATE_FORMAT(now(), '%Y%m%d') as REC_CREATE_TIME ,
        c.REC_REVISOR                as REC_REVISOR,
        c.REC_REVISOR_NAME           as REC_REVISOR_NAME,
        c.REC_REVISE_TIME            as REC_REVISE_TIME,
        c.ARCHIVE_FLAG               as ARCHIVE_FLAG,
        c.DEL_FLAG                   as DEL_FLAG,
        ''                           as REMARK,
        a.VEHICLE_NO                 as VEHICLE_NO,
        c.UUID                       as UUID,
        b.TENANT_ID                  as TENANT_ID,
        ' '                         as TYPE_OF_HANDLING,
        DATE_FORMAT(now(), '%Y%m%d') as RESERVATION_DATE,
        DATE_FORMAT(now(), 'H%i%s')  as RESERVATION_TIME,
        b.DRIVER_IDENTITY as  DRIVER_IDENTITY,
        b.DRIVER_NAME  as DRIVER_NAME,
        b.TEL as DRIVER_TEL,
        a.VEHICLE_TYPE as "VEHICLE_TYPE",
        ' ' as "VISIT_UNIT"
        FROM ${meliSchema}.tlirl0501 a,
        ${meliSchema}.tlirl0102 b,
        ${meliSchema}.tlirl0103 c
        WHERE 1 = 1
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        and a.VEHICLE_TYPE = '1'
        AND a.STATUS = '20'
        AND a.DEL_FLAG = '0'
        and a.SEG_NO = c.SEG_NO
        and a.VEHICLE_NO = c.VEHICLE_NO
        and c.STATUS = '20'
        and c.M_UUID = b.UUID
        and b.DEL_FLAG = 0
        and b.status = '20'
        and c.DEL_FLAG = 0
<!--        and not exists(select 1-->
<!--        from MELI.tlirl0201 t201-->
<!--        where t201.SEG_NO = a.SEG_NO-->
<!--        and t201.VEHICLE_NO= a.VEHICLE_NO-->
<!--        AND a.STATUS = '20'-->
<!--        AND a.DEL_FLAG = '0')  &lt;!&ndash; 排除已经有预约单的车辆 &ndash;&gt;-->
        and not exists(select 1
                         from ${meliSchema}.tlirl0301 t0301
                         where t0301.SEG_NO = a.SEG_NO
                         and t0301.VEHICLE_NO = a.VEHICLE_NO
                         and t0301.DEL_FLAG = 0
                         and t0301.STATUS in ('20', '30', '40'))  <!-- 排除已经入厂的车辆 -->
        ) tt
        where 1=1
            <isNotEmpty prepend=" AND " property="vehicleNo">
                tt.VEHICLE_NO like concat('%', #vehicleNo#,'%')
            </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNoCp">
            tt.VEHICLE_NO =#vehicleNoCp#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                tt.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
    </select>


    <select id="queryEnteringTheFactoryCount" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
       select count(1) as "count"( select SEG_NO as "segNo",  <!-- 业务单元代码 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        reservation_number as "reservationNumber",  <!-- 预约号 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        TYPE_OF_HANDLING as "typeOfHandling" ,<!-- 装卸业务 -->
        RESERVATION_DATE as "reservationDate",  <!-- 预约日期 -->
        RESERVATION_TIME as "reservationTime",  <!-- 预约时间 -->
        DRIVER_IDENTITY as "driverIdentity",  <!-- 驾驶员身份证号 -->
        DRIVER_NAME as "driverName",  <!-- 驾驶员姓名 -->
        DRIVER_TEL as "driverTel",  <!-- 驾驶员手机号 -->
        VEHICLE_TYPE as "vehicleType"
        from(SELECT
        SEG_NO ,  <!-- 业务单元代码 -->
        UNIT_CODE ,  <!-- 业务单元代码 -->
        reservation_number ,  <!-- 预约号 -->
        STATUS ,  <!-- 状态 -->
        REC_CREATOR ,  <!-- 记录创建人 -->
        REC_CREATOR_NAME ,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME ,  <!-- 记录创建时间 -->
        REC_REVISOR ,  <!-- 记录修改人 -->
        REC_REVISOR_NAME ,  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME ,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG ,  <!-- 归档标记 -->
        DEL_FLAG ,  <!-- 记录删除标记 -->
        REMARK ,  <!-- 备注 -->
        VEHICLE_NO ,  <!-- 车牌号 -->
        UUID ,  <!-- uuid -->
        TENANT_ID ,  <!-- 租户ID -->
        TYPE_OF_HANDLING ,<!-- 装卸业务 -->
        RESERVATION_DATE ,  <!-- 预约日期 -->
        RESERVATION_TIME ,  <!-- 预约时间 -->
        DRIVER_IDENTITY ,  <!-- 驾驶员身份证号 -->
        DRIVER_NAME ,  <!-- 驾驶员姓名 -->
        DRIVER_TEL,  <!-- 驾驶员手机号 -->
        '0' as "VEHICLE_TYPE"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        and a.STATUS = '20'
        and a.DEL_FLAG = 0
        and substr(a.RESERVATION_DATE,1,8) = DATE_FORMAT(now(), '%Y%m%d')  <!-- 取当天的数据 -->
        and not exists(select 1
        from ${meliSchema}.tlirl0301 t0301
        where t0301.SEG_NO = a.SEG_NO
        and t0301.VEHICLE_NO = a.VEHICLE_NO
        and t0301.DEL_FLAG = 0
        and t0301.STATUS in ('20', '30', '40'))  <!-- 排除已经入厂的车辆 -->
        and DATE_FORMAT(NOW(), '%Y%m%d%H%i') BETWEEN a.RESERVATION_DATE AND a.RESERVATION_TIME
        <include refid="condition"/>
        union
        SELECT
        SEG_NO ,  <!-- 业务单元代码 -->
        UNIT_CODE ,  <!-- 业务单元代码 -->
        reservation_number ,  <!-- 预约号 -->
        STATUS ,  <!-- 状态 -->
        REC_CREATOR ,  <!-- 记录创建人 -->
        REC_CREATOR_NAME ,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME ,  <!-- 记录创建时间 -->
        REC_REVISOR ,  <!-- 记录修改人 -->
        REC_REVISOR_NAME ,  <!-- 记录创建人姓名 -->
        REC_REVISE_TIME ,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG ,  <!-- 归档标记 -->
        DEL_FLAG ,  <!-- 记录删除标记 -->
        REMARK ,  <!-- 备注 -->
        VEHICLE_NO ,  <!-- 车牌号 -->
        UUID ,  <!-- uuid -->
        TENANT_ID ,  <!-- 租户ID -->
        TYPE_OF_HANDLING ,<!-- 装卸业务 -->
        RESERVATION_DATE ,  <!-- 预约日期 -->
        RESERVATION_TIME ,  <!-- 预约时间 -->
        DRIVER_IDENTITY ,  <!-- 驾驶员身份证号 -->
        DRIVER_NAME ,  <!-- 驾驶员姓名 -->
        DRIVER_TEL,  <!-- 驾驶员手机号 -->
        '0' as "VEHICLE_TYPE"
        FROM ${meliSchema}.tlirl0201 a WHERE 1=1
        and a.STATUS = '20'
        and a.DEL_FLAG = 0
        and not exists(select 1
        from ${meliSchema}.tlirl0301 t0301
        where t0301.SEG_NO = a.SEG_NO
        and t0301.VEHICLE_NO = a.VEHICLE_NO
        and t0301.DEL_FLAG = 0
        and t0301.STATUS in ('20', '30', '40'))  <!-- 排除已经入厂的车辆 -->
        and exists(select 1
        FROM meli.tlirl0201 a1
        WHERE 1 = 1
        and a1.STATUS = '20'
        and a1.DEL_FLAG = 0
        and a1.DRIVER_TEL = a.DRIVER_TEL
        and a1.DRIVER_IDENTITY = a.DRIVER_IDENTITY
        and a.RESERVATION_DATE != DATE_FORMAT(now(), '%Y%m%d')) <!-- 排除当天已经预约的 -->
        and a.RESERVATION_NUMBER = (select max(a1.RESERVATION_NUMBER)
        FROM meli.tlirl0201 a1
        WHERE 1 = 1
        and a1.STATUS = '20'
        and a1.DEL_FLAG = 0
        and a1.DRIVER_TEL = a.DRIVER_TEL
        and a1.DRIVER_IDENTITY = a.DRIVER_IDENTITY
        and substr(a.RESERVATION_DATE,1,8) != DATE_FORMAT(now(), '%Y%m%d'))  <!-- 取今天过后已经预约的最后一条记录 -->
        and DATE_FORMAT(NOW(), '%Y%m%d%H%i') BETWEEN a.RESERVATION_DATE AND a.RESERVATION_TIME
        <include refid="condition"/>

        union
        SELECT b.SEG_NO as SEG_NO,
        b.UNIT_CODE as UNIT_CODE,
        '' as reservation_number,
        '20' as STATUS,
        c.REC_CREATOR as REC_CREATOR,
        c.REC_CREATOR_NAME as REC_CREATOR_NAME,
        DATE_FORMAT(now(), '%Y%m%d') as REC_CREATE_TIME ,
        c.REC_REVISOR as REC_REVISOR,
        c.REC_REVISOR_NAME as REC_REVISOR_NAME,
        c.REC_REVISE_TIME as REC_REVISE_TIME,
        c.ARCHIVE_FLAG as ARCHIVE_FLAG,
        c.DEL_FLAG as DEL_FLAG,
        '' as REMARK,
        a.VEHICLE_NO as VEHICLE_NO,
        c.UUID as UUID,
        b.TENANT_ID as TENANT_ID,
        ' ' as TYPE_OF_HANDLING,
        DATE_FORMAT(now(), '%Y%m%d') as RESERVATION_DATE,
        DATE_FORMAT(now(), 'H%i%s') as RESERVATION_TIME,
        b.DRIVER_IDENTITY as DRIVER_IDENTITY,
        b.DRIVER_NAME as DRIVER_NAME,
        b.TEL as DRIVER_TEL,
        a.VEHICLE_TYPE as "VEHICLE_TYPE"
        FROM ${meliSchema}.tlirl0501 a,
        ${meliSchema}.tlirl0102 b,
        ${meliSchema}.tlirl0103 c
        WHERE 1 = 1
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        and a.VEHICLE_TYPE = '1'
        AND a.STATUS = '20'
        AND a.DEL_FLAG = '0'
        and a.SEG_NO = c.SEG_NO
        and a.VEHICLE_NO = c.VEHICLE_NO
        and c.STATUS = '20'
        and c.M_UUID = b.UUID
        and b.DEL_FLAG = 0
        and b.status = '20'
        and c.DEL_FLAG = 0
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <!--        and not exists(select 1-->
        <!--        from MELI.tlirl0201 t201-->
        <!--        where t201.SEG_NO = a.SEG_NO-->
        <!--        and t201.VEHICLE_NO= a.VEHICLE_NO-->
        <!--        AND a.STATUS = '20'-->
        <!--        AND a.DEL_FLAG = '0')  &lt;!&ndash; 排除已经有预约单的车辆 &ndash;&gt;-->
        and not exists(select 1
        from ${meliSchema}.tlirl0301 t0301
        where t0301.SEG_NO = a.SEG_NO
        and t0301.VEHICLE_NO = a.VEHICLE_NO
        and t0301.DEL_FLAG = 0
        and t0301.STATUS in ('20', '30', '40'))  <!-- 排除已经入厂的车辆 -->
        ) tt
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                tt.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
        )A
    </select>

    <select id="queryAllReservationTime" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT a.RESERVATION_NUMBER                                                            as "reservationNumber",
        a.SEG_NO                                                                        as "segNo",
        (select SEG_NAME from iplat4j.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as "segName",
        a.UNIT_CODE                                                                     as "unitCode",
        a.STATUS                                                                        as "status",
        a.TYPE_OF_HANDLING                                                              as "typeOfHandling",
        a.CUSTOMER_ID                                                                   as "customerId",
        a.CUSTOMER_NAME                                                                 as "customerName",
        a.DRIVER_NAME                                                                   as "driverName",
        a.DRIVER_TEL                                                                    as "driverTel",
        a.DRIVER_IDENTITY                                                               as "driverIdentity",
        a.VEHICLE_NO                                                                    as "vehicleNo",
        a.START_OF_TRANSPORT                                                            as "startOfTransport",
        a.PURPOSE_OF_TRANSPORT                                                          as "purposeOfTransport",
        a.RESERVATION_DATE                                                              as "reservationDate",
        a.RESERVATION_TIME                                                              as "reservationTime",
        a.IS_RESERVATION                                                                as "isReservation",
        a.REC_CREATOR                                                                   as "recCreator",
        a.REC_CREATOR_NAME                                                              as "recCreatorName",
        a.REC_CREATE_TIME                                                               as "recCreateTime",
        a.REC_REVISOR                                                                   as "recRevisor",
        a.REC_REVISOR_NAME                                                              as "recRevisorName",
        a.REC_REVISE_TIME                                                               as "recReviseTime",
        a.ARCHIVE_FLAG                                                                  as "archiveFlag",
        a.DEL_FLAG                                                                      as "delFlag",
        a.REMARK                                                                        as "remark",
        a.SYS_REMARK                                                                    as "sysRemark",
        a.UUID                                                                          as "uuid",
        a.TENANT_ID                                                                     as "tenantId"
        from meli.tlirl0201 a
        WHERE 1 = 1
        AND a.SEG_NO = #segNo#
        and a.STATUS = #status#
        and STR_TO_DATE(a.RESERVATION_TIME, '%Y%m%d%H%i') &lt; now();
    </select>

    <select id="queryVehicleCountCq" resultClass="String">
        select count(1) as "count"
        from meli.tlirl0201
        where 1 = 1
          and SEG_NO = #segNo#
          AND STATUS in ('20', '99')
          AND DATE(STR_TO_DATE(REC_CREATE_TIME, '%Y%m%d%H%i%s')) = CURDATE()
    </select>


    <select id="queryVehicleCountCq1" resultClass="String">
        select TYPE_OF_HANDLING as "typeOfHandling"
        from meli.tlirl0301,
             meli.tlirl0201
        where 1 = 1
          and tlirl0301.SEG_NO = tlirl0201.SEG_NO
          and tlirl0301.RESERVATION_NUMBER = tlirl0201.RESERVATION_NUMBER
          and tlirl0301.VEHICLE_NO = tlirl0201.VEHICLE_NO
          and tlirl0301.CAR_TRACE_NO = #carTraceNo#
          and tlirl0301.STATUS != '00'
  and tlirl0201.STATUS = '20' limit 1
    </select>

</sqlMap>