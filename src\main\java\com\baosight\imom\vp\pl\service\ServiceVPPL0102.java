package com.baosight.imom.vp.pl.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.EasyExcelUtil;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.vp.pl.domain.VPPL0102;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

public class ServiceVPPL0102 extends ServiceBase {
    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VPPL0102().eiMetadata);
        inInfo.addBlock("groupByEmp").addBlockMeta(new VPPL0102().eiMetadata);
        inInfo.addBlock("groupByArea").addBlockMeta(new VPPL0102().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                EiBlock block = inInfo.getBlock("inqu_status");
                if (block.getRows().size() > 0) {
                    HashMap hashMapMap = (HashMap) block.getRows().get(0);
                    //起始生产日期
                    String productDateStart = MapUtils.getString(hashMapMap, "productDateStart", "");
                    if (StringUtils.isNotBlank(productDateStart)) {
                        Date datetime = inputFormat.parse(productDateStart);
                        productDateStart = outputFormat.format(datetime);
                        if (StringUtils.isNotBlank(productDateStart)) {
                            hashMapMap.put("productDateStart", productDateStart);
                        }
                    }
                    //截止生产日期
                    String productDateEnd = MapUtils.getString(hashMapMap, "productDateEnd", "");
                    if (StringUtils.isNotBlank(productDateEnd)) {
                        Date datetime1 = inputFormat.parse(productDateEnd);
                        productDateEnd = outputFormat.format(datetime1);
                        if (StringUtils.isNotBlank(productDateEnd)) {
                            hashMapMap.put("productDateEnd", productDateEnd);
                        }
                    }

                }
                outInfo = super.query(inInfo, VPPL0102.QUERY, null, false, new EiBlockMeta(), EiConstant.queryBlock, EiConstant.resultBlock, EiConstant.resultBlock);
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     *  查询汇总数据(按员工分组)
     * @param inInfo
     * @return
     */
    public EiInfo statisticGroupByEmp(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                EiBlock block = inInfo.getBlock("inqu_status");
                if (block.getRows().size() > 0) {
                    HashMap hashMapMap = (HashMap) block.getRows().get(0);
                    //起始生产日期
                    String productDateStart = MapUtils.getString(hashMapMap, "productDateStart", "");
                    if (StringUtils.isNotBlank(productDateStart)) {
                        Date datetime = inputFormat.parse(productDateStart);
                        productDateStart = outputFormat.format(datetime);
                        if (StringUtils.isNotBlank(productDateStart)) {
                            hashMapMap.put("productDateStart", productDateStart);
                        }
                    }
                    //截止生产日期
                    String productDateEnd = MapUtils.getString(hashMapMap, "productDateEnd", "");
                    if (StringUtils.isNotBlank(productDateEnd)) {
                        Date datetime1 = inputFormat.parse(productDateEnd);
                        productDateEnd = outputFormat.format(datetime1);
                        if (StringUtils.isNotBlank(productDateEnd)) {
                            hashMapMap.put("productDateEnd", productDateEnd);
                        }
                    }

                }

                outInfo = super.query(inInfo, VPPL0102.STATISTIC_GROUP_BY_EMP, null, false,new EiBlockMeta(),EiConstant.queryBlock,"groupByEmp","groupByEmp");
                /*//查询汇总数据(按员工分组)
                List<HashMap> groupByEmpList = dao.query(VPPL0102.STATISTIC_GROUP_BY_EMP, inInfo.getBlock(EiConstant.queryBlock).getRow(0));
                outInfo.addBlock("groupByEmp").addRows(groupByEmpList);*/


            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     *  查询汇总数据(按机组分组)
     * @param inInfo
     * @return
     */
    public EiInfo statisticGroupByArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                EiBlock block = inInfo.getBlock("inqu_status");
                if (block.getRows().size() > 0) {
                    HashMap hashMapMap = (HashMap) block.getRows().get(0);
                    //起始生产日期
                    String productDateStart = MapUtils.getString(hashMapMap, "productDateStart", "");
                    if (StringUtils.isNotBlank(productDateStart)) {
                        Date datetime = inputFormat.parse(productDateStart);
                        productDateStart = outputFormat.format(datetime);
                        if (StringUtils.isNotBlank(productDateStart)) {
                            hashMapMap.put("productDateStart", productDateStart);
                        }
                    }
                    //截止生产日期
                    String productDateEnd = MapUtils.getString(hashMapMap, "productDateEnd", "");
                    if (StringUtils.isNotBlank(productDateEnd)) {
                        Date datetime1 = inputFormat.parse(productDateEnd);
                        productDateEnd = outputFormat.format(datetime1);
                        if (StringUtils.isNotBlank(productDateEnd)) {
                            hashMapMap.put("productDateEnd", productDateEnd);
                        }
                    }

                }

                outInfo = super.query(inInfo, VPPL0102.STATISTIC_GROUP_BY_AREA,null, false,new EiBlockMeta(),EiConstant.queryBlock,"groupByArea","groupByArea");
                /*//查询汇总数据(按区域分组)
                List<HashMap> groupByAreaList = dao.query(VPPL0102.STATISTIC_GROUP_BY_AREA, inInfo.getBlock(EiConstant.queryBlock).getRow(0));
                outInfo.addBlock("groupByArea").addRows(groupByAreaList);*/


            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 查询人员在每个区域中的时间
     *
     * @param inInfo
     * @return
     */
    public EiInfo statisticPersonTimeInArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                EiBlock block = inInfo.getBlock("inqu_status");
                if (block.getRows().size() > 0) {
                    HashMap hashMapMap = (HashMap) block.getRows().get(0);
                    //起始生产日期
                    String productDateStart = MapUtils.getString(hashMapMap, "productDateStart", "");
                    if (StringUtils.isNotBlank(productDateStart)) {
                        Date datetime = inputFormat.parse(productDateStart);
                        productDateStart = outputFormat.format(datetime);
                        if (StringUtils.isNotBlank(productDateStart)) {
                            hashMapMap.put("productDateStart", productDateStart);
                        }
                    }
                    //截止生产日期
                    String productDateEnd = MapUtils.getString(hashMapMap, "productDateEnd", "");
                    if (StringUtils.isNotBlank(productDateEnd)) {
                        Date datetime1 = inputFormat.parse(productDateEnd);
                        productDateEnd = outputFormat.format(datetime1);
                        if (StringUtils.isNotBlank(productDateEnd)) {
                            hashMapMap.put("productDateEnd", productDateEnd);
                        }
                    }

                }
                outInfo = super.query(inInfo, VPPL0102.STATISTIC_PERSON_TIME_IN_AREA, null, false, new EiBlockMeta(), EiConstant.queryBlock, "personTimeInArea", "personTimeInArea");

            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 后端导出
     */
    public EiInfo postExport(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map<String, Object> loginMap = new HashMap();
            loginMap.put("userId", UserSession.getUserId());
            loginMap.put("userName", UserSession.getLoginCName());
            loginMap.put("loginName", UserSession.getLoginName());
            Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            //格式化时间
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            //起始生产日期
            String productDateStart = MapUtils.getString(queryBlock, "productDateStart", "");
            if (StringUtils.isNotBlank(productDateStart)) {
                Date datetime = inputFormat.parse(productDateStart);
                productDateStart = outputFormat.format(datetime);
                if (StringUtils.isNotBlank(productDateStart)) {
                    queryBlock.put("productDateStart", productDateStart);
                }
            }
            //截止生产日期
            String productDateEnd = MapUtils.getString(queryBlock, "productDateEnd", "");
            if (StringUtils.isNotBlank(productDateEnd)) {
                Date datetime1 = inputFormat.parse(productDateEnd);
                productDateEnd = outputFormat.format(datetime1);
                if (StringUtils.isNotBlank(productDateEnd)) {
                    queryBlock.put("productDateEnd", productDateEnd);
                }
            }

            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            if (StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }

            //sheet页
            List<Map<String, Object>> List1 = dao.queryAll(VPPL0102.STATISTIC_GROUP_BY_EMP_EXPORT, queryBlock);
            List<Map<String, Object>> List2 = dao.queryAll(VPPL0102.STATISTIC_GROUP_BY_AREA_EXPORT, queryBlock);
            List<Map<String, Object>> List3 = dao.queryAll(VPPL0102.STATISTIC_PERSON_TIME_IN_AREA_EXPORT, queryBlock);
            List<Map<String, Object>> List4 = dao.queryAll(VPPL0102.QUERY_EXPORT, queryBlock);

            inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(VPPL0102.exportBlockMeta());
            TreeMap sheetMap = new TreeMap();
            sheetMap.put("groupByArea", List2);
            sheetMap.put("personTimeInArea", List3);
            sheetMap.put("result", List4);


            Map resultMap = EasyExcelUtil.export2FileStorageForMutiSheet(inInfo, loginMap, List1, sheetMap);
            outInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);

            outInfo.setMsg("导出成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}
