create table TMEDV0102
(
    MACHINE_CODE     VARCHAR(30)  default ' '    not null comment '机组代码',
    MACHINE_NAME     VARCHAR(50)  default ' '    not null comment '机组名称',
    MACHINE_PROPERTY VARCHAR(1)   default ' '    not null comment '机组属性',
    E_ARCHIVES_NO    VARCHAR(20)  default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME   VARCHAR(200) default ' '    not null comment '设备名称',
    -- 固定字段
    UUID             VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR      VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME  VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR      VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME  VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID        VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG     VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)


) COMMENT ='机组表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;

-- 增加ER图外键
ALTER TABLE TMEDV0102 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);