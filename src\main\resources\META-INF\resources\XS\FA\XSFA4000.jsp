<%@ page contentType="text/html;charset=UTF-8" trimDirectiveWhitespaces="true" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiConstant" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiInfo" %>
<%@ page import="com.baosight.iplat4j.core.service.soa.XLocalManager" %>
<%@ page import="org.springframework.web.multipart.MultipartHttpServletRequest" %>
<%@ page import="org.springframework.web.multipart.commons.CommonsMultipartResolver" %>
<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<%@ page import="com.baosight.iplat4j.core.exception.PlatException" %>

<%
    UserSession.web2Service(request);
    CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();

    String ename = request.getParameter("ename");
    String serviceName = request.getParameter(EiConstant.serviceName);
    String methodName = request.getParameter(EiConstant.methodName);
    // 通用参数，业务代码
    String id = request.getParameter("id");
    String id2 = request.getParameter("id2");
    String segNo = request.getParameter("segNo");
    MultipartHttpServletRequest multipartRequest = multipartResolver.resolveMultipart(request);

    // 反射 serviceName methodName
    EiInfo inInfo = new EiInfo();
    inInfo.set(EiConstant.serviceName, serviceName);
    inInfo.set(EiConstant.methodName, methodName);
    inInfo.set("multipartRequest", multipartRequest);
    inInfo.set("id", id);
    inInfo.set("id2", id2);
    inInfo.set("segNo", segNo);
    inInfo.set("file", multipartRequest.getFileMap().get(ename));

    EiInfo outInfo = XLocalManager.call(inInfo);
    UserSession.getData().clear();
    if (outInfo.getStatus() < 0) {
        throw new PlatException(outInfo.getMsg());
    }
%>
