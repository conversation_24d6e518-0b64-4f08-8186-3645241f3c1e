<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-01 11:00:19
       Version :  1.0
    tableName :${meliSchema}.tlirl0406
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     LOAD_ID  VARCHAR   NOT NULL   primarykey,
     STATUS  VARCHAR   NOT NULL,
     LOAD_DATE  VARCHAR   NOT NULL,
     CANCEL_LOAD_DATE  VARCHAR   NOT NULL,
     CAR_TRACE_NO  VARCHAR   NOT NULL,
     DATE_SOURCE  VARCHAR   NOT NULL,
     VOUCHER_NUM  VARCHAR   NOT NULL,
     TARGET_HAND_POINT_ID  VARCHAR   NOT NULL,
     VEH<PERSON><PERSON>_NO  VARCHAR   NOT NULL,
     CURRENT_HAND_POINT_ID  VARCHAR   NOT NULL,
     FACTORY_AREA  VARCHAR   NOT NULL,
     DOCUMENT_TYPE  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0406">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadId">
            LOAD_ID = #loadId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadDate">
            LOAD_DATE = #loadDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="cancelLoadDate">
            CANCEL_LOAD_DATE = #cancelLoadDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="dateSource">
            DATE_SOURCE = #dateSource#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointId">
            TARGET_HAND_POINT_ID = #targetHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentHandPointId">
            CURRENT_HAND_POINT_ID = #currentHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="documentType">
            DOCUMENT_TYPE = #documentType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0406">
        SELECT
        SEG_NO as "segNo",  <!-- 账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        LOAD_ID as "loadId",  <!-- 开始装卸货流水号 -->
        STATUS as "status",  <!-- 状态（00：撤销，10：新增） -->
        LOAD_DATE as "loadDate",  <!-- 开始装卸货日期 -->
        CANCEL_LOAD_DATE as "cancelLoadDate",  <!-- 撤销开始装卸货时间 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        DATE_SOURCE as "dateSource",  <!-- 数据源（10：MES,20:PDA） -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单号（PAD作业流水号） -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        DOCUMENT_TYPE as "documentType",  <!-- 单据类型(10普通 20重复) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0406 WHERE 1=1
        AND DEL_FLAG='0'
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="loadId">
            LOAD_ID = #loadId#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                LOAD_ID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0406 WHERE 1=1
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="loadId">
            LOAD_ID = #loadId#
        </isNotEmpty>
    </select>

    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0406">
        SELECT
        SEG_NO as "segNo",  <!-- 账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        LOAD_ID as "loadId",  <!-- 开始装卸货流水号 -->
        STATUS as "status",  <!-- 状态（00：撤销，10：新增） -->
        LOAD_DATE as "loadDate",  <!-- 开始装卸货日期 -->
        CANCEL_LOAD_DATE as "cancelLoadDate",  <!-- 撤销开始装卸货时间 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        DATE_SOURCE as "dateSource",  <!-- 数据源（10：MES,20:PDA） -->
        VOUCHER_NUM as "voucherNum",  <!-- 依据凭单号（PAD作业流水号） -->
        TARGET_HAND_POINT_ID as "targetHandPointId",  <!-- 目标装卸点代码 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        CURRENT_HAND_POINT_ID as "currentHandPointId",  <!-- 当前装卸点代码 -->
        FACTORY_AREA as "factoryArea",  <!-- 厂区 -->
        DOCUMENT_TYPE as "documentType",  <!-- 单据类型(10普通 20重复) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0406 WHERE 1=1
        AND DEL_FLAG='0'
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        order by LOAD_DATE desc
    </select>
    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadId">
            LOAD_ID = #loadId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loadDate">
            LOAD_DATE = #loadDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="cancelLoadDate">
            CANCEL_LOAD_DATE = #cancelLoadDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="dateSource">
            DATE_SOURCE = #dateSource#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="targetHandPointId">
            TARGET_HAND_POINT_ID = #targetHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentHandPointId">
            CURRENT_HAND_POINT_ID = #currentHandPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA = #factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="documentType">
            DOCUMENT_TYPE = #documentType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0406 (SEG_NO,  <!-- 账套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        LOAD_ID,  <!-- 开始装卸货流水号 -->
        STATUS,  <!-- 状态（00：撤销，10：新增） -->
        LOAD_DATE,  <!-- 开始装卸货日期 -->
        CANCEL_LOAD_DATE,  <!-- 撤销开始装卸货时间 -->
        CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
        DATE_SOURCE,  <!-- 数据源（10：MES,20:PDA） -->
        VOUCHER_NUM,  <!-- 依据凭单号（PAD作业流水号） -->
        TARGET_HAND_POINT_ID,  <!-- 目标装卸点代码 -->
        VEHICLE_NO,  <!-- 车牌号 -->
        CURRENT_HAND_POINT_ID,  <!-- 当前装卸点代码 -->
        FACTORY_AREA,  <!-- 厂区 -->
        DOCUMENT_TYPE,  <!-- 单据类型(10普通 20重复) -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID,  <!-- 租户ID -->
        PER_NAME
        )
        VALUES (#segNo#, #unitCode#, #loadId#, #status#, #loadDate#, #cancelLoadDate#, #carTraceNo#, #dateSource#,
        #voucherNum#, #targetHandPointId#, #vehicleNo#, #currentHandPointId#, #factoryArea#, #documentType#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
        #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#,#perName#)
    </insert>

    <delete id="delete">
        DELETE
        FROM ${meliSchema}.tlirl0406
        WHERE LOAD_ID = #loadId#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0406
        SET
        SEG_NO = #segNo#,   <!-- 账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        STATUS = #status#,   <!-- 状态（00：撤销，10：新增） -->
        LOAD_DATE = #loadDate#,   <!-- 开始装卸货日期 -->
        CANCEL_LOAD_DATE = #cancelLoadDate#,   <!-- 撤销开始装卸货时间 -->
        CAR_TRACE_NO = #carTraceNo#,   <!-- 车辆跟踪号 -->
        DATE_SOURCE = #dateSource#,   <!-- 数据源（10：MES,20:PDA） -->
        VOUCHER_NUM = #voucherNum#,   <!-- 依据凭单号（PAD作业流水号） -->
        TARGET_HAND_POINT_ID = #targetHandPointId#,   <!-- 目标装卸点代码 -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        CURRENT_HAND_POINT_ID = #currentHandPointId#,   <!-- 当前装卸点代码 -->
        FACTORY_AREA = #factoryArea#,   <!-- 厂区 -->
        DOCUMENT_TYPE = #documentType#,   <!-- 单据类型(10普通 20重复) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        UUID = #uuid#,   <!-- uuid -->
        TENANT_ID = #tenantId#  <!-- 租户ID -->
        WHERE
        LOAD_ID = #loadId#
    </update>


    <update id="updateByHandPointId">
        UPDATE ${meliSchema}.tlirl0406
        SET
        LOAD_DATE = #loadDate#,   <!-- 开始装卸货日期 -->
        DOCUMENT_TYPE = #documentType#,   <!-- 单据类型(10普通 20重复) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        SEG_NO = #segNo#
        and
        CAR_TRACE_NO = #carTraceNo#
        and
        VEHICLE_NO = #vehicleNo#
    </update>

    <select id="queryLoadingAndUnloadingTypesAndData" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select distinct tlirl0301.VEHICLE_NO                       as "vehicleNo",
                        ifnull(tlirl0301.TARGET_HAND_POINT_ID,
                               tlirl0301.CURRENT_HAND_POINT_ID)    as "targetHandPointId",
                        tlirl0301.CURRENT_HAND_POINT_ID            as "currentHandPointId",
                        ifnull(tlirl0301.STATUS, tlirl0301.STATUS) as "status",
                        (select HAND_POINT_NAME
                         from ${meliSchema}.tlirl0304 tlirl0304
                         where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                           and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
                           and tlirl0304.STATUS = '30'
                         limit 1)                                  AS "handPointName",
                        (select HAND_POINT_NAME
                         from ${meliSchema}.tlirl0304 tlirl0304
                         where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                           and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
                           and tlirl0304.STATUS = '30'
                         limit 1)                                  AS "currentHandPointName",
                        REPLACE(if((select tlirl0302.HAND_TYPE
                                    from ${meliSchema}.tlirl0302 tlirl0302
                                    where 1 = 1
                                      and tlirl0302.STATUS > 00
                                      and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO) = '',
                                   (select ITEM_CNAME
                                    from iplat4j.tedcm01 tedcm01
                                    where tedcm01.ITEM_CODE = (select tlirl0201.TYPE_OF_HANDLING
                                                               from ${meliSchema}.tlirl0201 tlirl0201
                                                               where 1 = 1
                                                                 and tlirl0201.STATUS > 00
                                                                 and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER)
                                      and tedcm01.CODESET_CODE = 'P007'),
                                   ifnull(ifnull((select ITEM_CNAME
                                                  from iplat4j.tedcm01 tedcm01
                                                  where tedcm01.ITEM_CODE =
                                                        (select tlirl0201.TYPE_OF_HANDLING
                                                         from ${meliSchema}.tlirl0201 tlirl0201
                                                         where 1 = 1
                                                           and tlirl0201.STATUS = '20'
                                                           and tlirl0201.IS_RESERVATION = '10'
                                                           and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                                           and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                                                         limit 1)
                                                    and tedcm01.CODESET_CODE = 'P007'),
                                                 (select ITEM_CNAME
                                                  from iplat4j.tedcm01 tedcm01
                                                  where tedcm01.ITEM_CODE = (select tlirl0302.HAND_TYPE
                                                                             from ${meliSchema}.tlirl0302 tlirl0302
                                                                             where 1 = 1
                                                                               and tlirl0302.STATUS > 00
                                                                               and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                                                               and tlirl0302.VOUCHER_NUM != '')
                                                    and tedcm01.CODESET_CODE = 'P007')),
                                          (select ITEM_CNAME
                                           from iplat4j.tedcm01 tedcm01
                                           where tedcm01.ITEM_CODE =
                                                 (select BUSINESS_TYPE
                                                  from ${meliSchema}.tlirl0302 tlirl0302
                                                  where 1 = 1
                                                    and tlirl0302.STATUS > 00
                                                    and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                                  limit 1)
                                             and tedcm01.CODESET_CODE = 'P010'))), '钢材卸货+装货', '卸货装货')
                                                                   as "businessType",
            (
            case
            when tlirl0301.STATUS = '20'
            then
            TIMESTAMPDIFF(MINUTE, STR_TO_DATE(ENTER_FACTORY, '%Y%m%d%H%i%s'),
            now())
            when tlirl0301.STATUS = '30'
            then
            TIMESTAMPDIFF(MINUTE, (select STR_TO_DATE(max(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s')
            from MELI.tlirl0406 tlirl0406
            where tlirl0406.SEG_NO = tlirl0301.SEG_NO
            and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO), now())
            else
            case
            when (select tlirl0406.REMARK
            from MELI.tlirl0407 tlirl0406
            where tlirl0406.SEG_NO = tlirl0301.SEG_NO
            and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
            order by tlirl0406.REC_REVISE_TIME desc
            limit 1) !=' ' then
            TIMESTAMPDIFF(MINUTE,
            (select STR_TO_DATE(MAX(tlirl0406.REC_REVISE_TIME), '%Y%m%d%H%i%s')
            from MELI.tlirl0407 tlirl0406
            where tlirl0406.SEG_NO = tlirl0301.SEG_NO
            and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO), now())
            else
            TIMESTAMPDIFF(MINUTE, (select STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
            from MELI.tlirl0407 tlirl0407
            where tlirl0407.SEG_NO = tlirl0301.SEG_NO
            and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
            and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
            now())
            end
            end
            )                                      as "waitingTime",
            ifnull(
            (select tlirl0307.D_USER_NAME
            from meli.tlirl0307 tlirl0307
            where tlirl0307.SEG_NO = tlirl0302.SEG_NO
            and tlirl0307.VOUCHER_NUM = tlirl0302.VOUCHER_NUM
            limit 1)
            ,' ')   as "customerName",
            (select tlirl0201.START_OF_TRANSPORT
            from ${meliSchema}.tlirl0201 tlirl0201
            where tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
            and tlirl0201.TYPE_OF_HANDLING = '20'
                and tlirl0201.STATUS = '20'
                and tlirl0301.STATUS != '00'
            limit 1) as "startOfTransport"
        from ${meliSchema}.tlirl0301 tlirl0301,
            ${meliSchema}.tlirl0302 tlirl0302
        where 1 = 1
          AND tlirl0301.SEG_NO=tlirl0302.SEG_NO
          AND tlirl0301.CAR_TRACE_NO=tlirl0302.CAR_TRACE_NO
          AND tlirl0301.VEHICLE_NO=tlirl0302.VEHICLE_NO
          AND tlirl0301.STATUS != '00'
          AND tlirl0301.SEG_NO = #segNo#
          AND tlirl0302.STATUS != '00'
        ORDER by "waitingTime" DESC
    </select>

    <select id="queryLoadingAndUnloadingTypesAndDataCQ" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select distinct tlirl0301.VEHICLE_NO                             as "vehicleNo",
                        ifnull(CASE WHEN tlirl0301.TARGET_HAND_POINT_ID = ' ' then null end,
                               (select tlirl0401.TARGET_HAND_POINT_ID
                                from meli.tlirl0401 tlirl0401
                                where 1 = 1
                                  and tlirl0401.SEG_NO = tlirl0301.SEG_NO
                                  and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                  and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                   limit 1))                                as "targetHandPointId",
                        tlirl0301.CURRENT_HAND_POINT_ID                  as "currentHandPointId",
                        ifnull(tlirl0301.STATUS, tlirl0301.STATUS)       as "status",
                        ifnull((select HAND_POINT_NAME
                                from MELI.tlirl0304 tlirl0304
                                where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                                  and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
                                  and tlirl0304.STATUS = '30'
                            limit 1), (select HAND_POINT_NAME
                                   from MELI.tlirl0304 tlirl0304
                                   where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                                     and tlirl0304.HAND_POINT_ID = (select tlirl0401.TARGET_HAND_POINT_ID
                                                                    from meli.tlirl0401 tlirl0401
                                                                    where 1 = 1
                                                                      and tlirl0401.SEG_NO = tlirl0301.SEG_NO
                                                                      and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                                                      and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                    limit 1)
                                     and tlirl0304.STATUS = '30'
                                   limit 1))                     AS "handPointName",
                        (select HAND_POINT_NAME
                         from MELI.tlirl0304 tlirl0304
                         where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                           and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
                           and tlirl0304.STATUS = '30'
                            limit 1)                                        AS "currentHandPointName",
                case
                    when tlirl0301.SEG_NO = 'JC000000'
                        THEN
                        CASE
                            WHEN
                                (select TYPE_OF_HANDLING
                                 from meli.tlirl0201 tlirl0201
                                 where 1 = 1
                                   and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                   AND tlirl0201.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                   AND tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER) = '10'
                                THEN '钢材装卸货'
                            else
                                (select ITEM_CNAME
                                 from iplat4j.tedcm01 tedcm01
                                 where tedcm01.ITEM_CODE = (select TYPE_OF_HANDLING
                                                            from meli.tlirl0201 tlirl0201
                                                            where 1 = 1
                                                              and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                                              AND tlirl0201.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                              AND tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER)
                                   and tedcm01.CODESET_CODE = 'P007') end
                    else
                        (select ITEM_CNAME
                         from iplat4j.tedcm01 tedcm01
                         where tedcm01.ITEM_CODE = (select TYPE_OF_HANDLING
                                                    from meli.tlirl0201 tlirl0201
                                                    where 1 = 1
                                                      and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                                      AND tlirl0201.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                      AND tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER)
                           and tedcm01.CODESET_CODE = 'P007') end as "businessType",
                (case
                     when tlirl0301.STATUS = '20' then TIMESTAMPDIFF(MINUTE, STR_TO_DATE((select QUEUE_DATE
                                                                                          from meli.tlirl0401 tlirl0401
                                                                                          where 1 = 1
                                                                                            and tlirl0401.SEG_NO = tlirl0301.SEG_NO
                                                                                            and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                                                                            and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                                          limit 1), '%Y%m%d%H%i%s'),
                                                                     now())
                     when tlirl0301.STATUS = '30' then TIMESTAMPDIFF(MINUTE,
                                                                     (select STR_TO_DATE(max(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s')
                                                                      from MELI.tlirl0406 tlirl0406
                                                                      where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                                                        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                                     now())
                     else case
                              when exists((select QUEUE_DATE
                                           from meli.tlirl0401 tlirl0401
                                           where 1 = 1
                                             and tlirl0401.SEG_NO = tlirl0301.SEG_NO
                                             and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                             and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                           limit 1)) then TIMESTAMPDIFF(MINUTE, STR_TO_DATE((select QUEUE_DATE
                                                                                             from meli.tlirl0401 tlirl0401
                                                                                             where 1 = 1
                                                                                               and tlirl0401.SEG_NO = tlirl0301.SEG_NO
                                                                                               and tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                                                                               and tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                                             limit 1), '%Y%m%d%H%i%s'),
                                                                        now())
                              else TIMESTAMPDIFF(MINUTE,
                                                 (select STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
                                                  from MELI.tlirl0407 tlirl0407
                                                  where tlirl0407.SEG_NO = tlirl0301.SEG_NO
                                                    and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                    and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                 now()) end end) as "waitingTime",
                                                            (select count(distinct PACK_ID)
                 from meli.tlirl0503
                 where 1 = 1
                   and tlirl0503.SEG_NO = tlirl0301.SEG_NO
                   and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0301.ALLOCATE_VEHICLE_NO
                   AND tlirl0503.STATUS not in ( '00','99')
                   and tlirl0503.OUT_PACK_FLAG != '1')             as "packCountAll",
                (select count(distinct PACK_ID)
                 from meli.tlirl0503
                 where 1 = 1
                   and tlirl0503.SEG_NO = tlirl0301.SEG_NO
                   and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0301.ALLOCATE_VEHICLE_NO
                   and tlirl0503.STATUS = '30'
                   and tlirl0503.OUT_PACK_FLAG != '1')             as "packCountRemin"
        from MELI.tlirl0301 tlirl0301
        where 1 = 1
          AND tlirl0301.SEG_NO = 'JC000000'
          AND tlirl0301.STATUS != '00'
          AND NOT EXISTS (SELECT 1
            FROM MELI.tlirl0201 tlirl0201
            WHERE tlirl0201.SEG_NO = tlirl0301.SEG_NO
          AND tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
          AND tlirl0201.VISIT_UNIT = '杭州宝伟')
        union
        select distinct tlirl0401.VEHICLE_NO                  as "vehicleNo",
                        TARGET_HAND_POINT_ID                  as "targetHandPointId",
                        (select tlirl0301.CURRENT_HAND_POINT_ID
                         from meli.tlirl0301 tlirl0301
                         where 1 = 1
                           and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                           and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
                                                                 limit 1)                             as "currentHandPointId",
                (select tlirl0301.STATUS
                 from meli.tlirl0301 tlirl0301
                 where 1 = 1
                   and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                   and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
                 limit 1)                             as "status",
                (select HAND_POINT_NAME
                 from MELI.tlirl0304 tlirl0304
                 where tlirl0304.SEG_NO = tlirl0401.SEG_NO
                   and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
                   and tlirl0304.STATUS = '30'
                 limit 1)
                                                      AS "handPointName",
                null                                  AS "currentHandPointName",
                case
                    when
                        tlirl0401.SEG_NO = 'JC000000' then
                        case
                            when (select TYPE_OF_HANDLING
                                  from meli.tlirl0201 tlirl0201
                                  where 1 = 1
                                    and tlirl0201.SEG_NO = tlirl0401.SEG_NO
                                    AND tlirl0201.VEHICLE_NO = tlirl0401.VEHICLE_NO
                                    AND tlirl0201.RESERVATION_NUMBER = (select tlirl0301.RESERVATION_NUMBER
                                                                        from meli.tlirl0301 tlirl0301
                                                                        where 1 = 1
                                                                          and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                                                                          and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
                                                                        limit 1)) =
                                 '10' then '钢材装卸货'
                            else (select ITEM_CNAME
                                  from iplat4j.tedcm01 tedcm01
                                  where tedcm01.ITEM_CODE = (select TYPE_OF_HANDLING
                                                             from meli.tlirl0201 tlirl0201
                                                             where 1 = 1
                                                               and tlirl0201.SEG_NO = tlirl0401.SEG_NO
                                                               AND tlirl0201.VEHICLE_NO = tlirl0401.VEHICLE_NO
                                                               AND tlirl0201.RESERVATION_NUMBER =
                                                                   (select tlirl0301.RESERVATION_NUMBER
                                                                    from meli.tlirl0301 tlirl0301
                                                                    where 1 = 1
                                                                      and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                                                                      and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
                                                                    limit 1))
                                    and tedcm01.CODESET_CODE = 'P007') end
                    else (select ITEM_CNAME
                          from iplat4j.tedcm01 tedcm01
                          where tedcm01.ITEM_CODE = (select TYPE_OF_HANDLING
                                                     from meli.tlirl0201 tlirl0201
                                                     where 1 = 1
                                                       and tlirl0201.SEG_NO = tlirl0401.SEG_NO
                                                       AND tlirl0201.VEHICLE_NO = tlirl0401.VEHICLE_NO
                                                       AND tlirl0201.RESERVATION_NUMBER =
                                                           (select tlirl0301.RESERVATION_NUMBER
                                                            from meli.tlirl0301 tlirl0301
                                                            where 1 = 1
                                                              and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                                                              and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
                                                            limit 1))
                            and tedcm01.CODESET_CODE = 'P007') end                      as "businessType",
                (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(QUEUE_DATE, '%Y%m%d%H%i%s'),
                               now()))                as "waitingTime",
                                                               (select count(distinct PACK_ID)
                 from meli.tlirl0503
                 where 1 = 1
                   and tlirl0503.SEG_NO = tlirl0401.SEG_NO
                   and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0401.VOUCHER_NUM
                    AND tlirl0503.STATUS not in ( '00','99')
                   and tlirl0503.OUT_PACK_FLAG != '1')                                  as "packCountAll",
                (select count(distinct PACK_ID)
                 from meli.tlirl0503
                 where 1 = 1
                   and tlirl0503.SEG_NO = tlirl0401.SEG_NO
                   and tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0401.VOUCHER_NUM
                   and tlirl0503.STATUS = '30'
                   and tlirl0503.OUT_PACK_FLAG != '1')                                  as "packCountRemin"
        from MELI.tlirl0401 tlirl0401
        where 1 = 1
          AND tlirl0401.SEG_NO = 'JC000000'
        ORDER by "waitingTime" desc
    </select>

    <select id="queryLoadingAndUnloadingTypesAndDataCQDriver" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT DISTINCT a.VEHICLE_NO                                                          as vehicleNo,
        COALESCE(NULLIF(a.TARGET_HAND_POINT_ID, ' '), b.TARGET_HAND_POINT_ID) AS handPointId,
        COALESCE(NULLIF(a.TARGET_HAND_POINT_ID, ' '), b.TARGET_HAND_POINT_ID) as "targetHandPointId",
        a.CURRENT_HAND_POINT_ID                                               as "currentHandPointId",
        a.STATUS                                                              as "status",
        COALESCE(c.HAND_POINT_NAME, d.HAND_POINT_NAME)                        AS "handPointName",
        e.HAND_POINT_NAME                                                     AS "currentHandPointName",
        CASE
        WHEN a.SEG_NO = 'JC000000'
        THEN CASE WHEN f.TYPE_OF_HANDLING = '10' THEN '钢材装卸货' ELSE g.ITEM_CNAME END
        ELSE g.ITEM_CNAME END                                             as "businessType",
        b.QUEUE_DATE                                                          AS queueTime,
        (SELECT COUNT(1)
        FROM meli.tlirl0503
        WHERE tlirl0503.SEG_NO = a.SEG_NO
        AND tlirl0503.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
        AND tlirl0503.STATUS = '20'
        AND tlirl0503.OUT_PACK_FLAG != '1') * 5                            AS waitingTime,
        (SELECT IFNULL(SUM(sub.waitingTime), 0)
        FROM (SELECT COALESCE(NULLIF(tlirl0301.TARGET_HAND_POINT_ID, ' '),
        tlirl0401.TARGET_HAND_POINT_ID)   AS handPointId,
        (SELECT COUNT(1)
        FROM meli.tlirl0503
        WHERE tlirl0503.SEG_NO = tlirl0301.SEG_NO
        AND tlirl0503.ALLOCATE_VEHICLE_NO = tlirl0301.ALLOCATE_VEHICLE_NO
        AND tlirl0503.STATUS = '20'
        AND tlirl0503.OUT_PACK_FLAG != '1') * 5 AS waitingTime,
        tlirl0301.VEHICLE_NO                       as vehicleNo,
        COALESCE(tlirl0401.QUEUE_DATE, tlirl0301.REC_CREATE_TIME) as queueTime
        FROM MELI.tlirl0301
        LEFT JOIN meli.tlirl0401 tlirl0401 ON (tlirl0401.SEG_NO = tlirl0301.SEG_NO AND
        tlirl0401.CAR_TRACE_NO =
        tlirl0301.CAR_TRACE_NO AND
        tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO)
        WHERE tlirl0301.SEG_NO = 'JC000000'
        AND tlirl0301.STATUS != '00'
        AND NOT EXISTS (SELECT 1
        FROM MELI.tlirl0201 tlirl0201
        WHERE tlirl0201.SEG_NO = tlirl0301.SEG_NO
        AND tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
        AND tlirl0201.VISIT_UNIT = '杭州宝伟')) sub
        WHERE sub.handPointId = COALESCE(NULLIF(a.TARGET_HAND_POINT_ID, ' '), b.TARGET_HAND_POINT_ID)
        AND (sub.queueTime &lt; COALESCE(b.QUEUE_DATE, a.REC_CREATE_TIME)
        OR (sub.queueTime = COALESCE(b.QUEUE_DATE, a.REC_CREATE_TIME)
        AND sub.vehicleNo &lt;= a.VEHICLE_NO)))                      AS totalWaitingTime,
            (SELECT COUNT(1)
        FROM meli.tlirl0503
        WHERE tlirl0503.SEG_NO = a.SEG_NO
          AND tlirl0503.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
          AND tlirl0503.STATUS not in ( '00','99')
          AND tlirl0503.OUT_PACK_FLAG != '1') as "packCountAll",
            (SELECT COUNT(1)
        FROM meli.tlirl0503
        WHERE tlirl0503.SEG_NO = a.SEG_NO
          AND tlirl0503.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
          AND tlirl0503.STATUS = '30'
          AND tlirl0503.OUT_PACK_FLAG != '1') as "packCountRemin"
        FROM MELI.tlirl0301 a
        LEFT JOIN meli.tlirl0401 b
        ON (b.SEG_NO = a.SEG_NO AND b.CAR_TRACE_NO = a.CAR_TRACE_NO AND b.VEHICLE_NO = a.VEHICLE_NO)
        LEFT JOIN MELI.tlirl0304 c
        ON (c.SEG_NO = a.SEG_NO AND c.HAND_POINT_ID = a.TARGET_HAND_POINT_ID AND c.STATUS = '30')
        LEFT JOIN MELI.tlirl0304 d
        ON (d.SEG_NO = a.SEG_NO AND d.HAND_POINT_ID = b.TARGET_HAND_POINT_ID AND d.STATUS = '30')
        LEFT JOIN MELI.tlirl0304 e
        ON (e.SEG_NO = a.SEG_NO AND e.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID AND e.STATUS = '30')
        LEFT JOIN meli.tlirl0201 f ON (f.SEG_NO = a.SEG_NO AND f.VEHICLE_NO = a.VEHICLE_NO AND
        f.RESERVATION_NUMBER = a.RESERVATION_NUMBER)
        LEFT JOIN iplat4j.tedcm01 g ON (g.ITEM_CODE = f.TYPE_OF_HANDLING AND g.CODESET_CODE = 'P007')
        WHERE a.SEG_NO = 'JC000000'
        AND a.STATUS not in ('00','50')
        AND NOT EXISTS (SELECT 1
        FROM MELI.tlirl0201 tlirl0201
        WHERE tlirl0201.SEG_NO = a.SEG_NO
        AND tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        AND tlirl0201.VISIT_UNIT = '杭州宝伟')
        AND (a.STATUS != '20' OR a.TARGET_HAND_POINT_ID = ' ')
        ORDER BY totalWaitingTime, waitingTime DESC
    </select>

    <select id="queryHandPointIdDriver" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select HAND_POINT_NAME AS "handPointName",
               HAND_POINT_ID   AS "handPointId",
               FACTORY_AREA   AS "factoryArea",
               FACTORY_AREA_NAME   AS "factoryAreaName",
               FACTORY_BUILDING   AS "factoryBuilding",
               FACTORY_BUILDING_NAME   AS "factoryBuildingName"
        from ${meliSchema}.tlirl0304 tlirl0304
        where 1 = 1
          AND STATUS = '30'
          AND SEG_NO = #segNo#
        GROUP BY handPointName,handPointId
    </select>

    <select id="queryHandPointId" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select HAND_POINT_NAME AS "handPointName",
               HAND_POINT_ID   AS "handPointId",
               FACTORY_AREA   AS "factoryArea",
               FACTORY_AREA_NAME   AS "factoryAreaName",
               FACTORY_BUILDING   AS "factoryBuilding",
               FACTORY_BUILDING_NAME   AS "factoryBuildingName"
        from ${meliSchema}.tlirl0304 tlirl0304
        where 1 = 1
          AND STATUS = '30'
          AND SEG_NO = #segNo#
        GROUP BY handPointName,handPointId
    </select>

    <select id="queryQueue" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select distinct tlirl0401.VEHICLE_NO                   as "vehicleNo",
                        tlirl0401.TARGET_HAND_POINT_ID         as "targetHandPointId",
                        (select HAND_POINT_NAME
                         from ${meliSchema}.tlirl0304 tlirl0304
                         where tlirl0304.SEG_NO = tlirl0401.SEG_NO
                           and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
                           and tlirl0304.STATUS = '30'
                         limit 1)                              as "handPointName",
                        (select ITEM_CNAME
                         from iplat4j.tedcm01 tedcm01
                         where tedcm01.ITEM_CODE = (select HAND_TYPE
                                                    from ${meliSchema}.tlirl0404 tlirl0304
                                                    where tlirl0304.SEG_NO = tlirl0401.SEG_NO
                                                      and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
                                                    limit 1)
                           and tedcm01.CODESET_CODE = 'P007')  as "handType",
                        ROUND(((select BEGIN_ENTRUCKING_TIME
                                from ${meliSchema}.tlirl0301 tlirl0301
                                where 1 = 1
                                  and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                                  and tlirl0301.CAR_TRACE_NO =
                                      tlirl0401.CAR_TRACE_NO
                                  and tlirl0301.VEHICLE_NO =
                                      tlirl0401.VEHICLE_NO
                                limit 1) - (select ENTER_FACTORY
                                            from ${meliSchema}.tlirl0301 tlirl0301
                                            where 1 = 1
                                              and tlirl0301.SEG_NO = tlirl0401.SEG_NO
                                              and tlirl0301.CAR_TRACE_NO =
                                                  tlirl0401.CAR_TRACE_NO
                                              and tlirl0301.VEHICLE_NO =
                                                  tlirl0401.VEHICLE_NO
                                            limit 1)) / 60, 0) as "waitingTime"
        from ${meliSchema}.tlirl0401 tlirl0401
        where 1 = 1
          and SEG_NO = #segNo#
    </select>

    <select id="lineUpAndCallForNumbers" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT *
        FROM (select tlirl0402.TARGET_HAND_POINT_ID                                       as "targetHandPointId",
                     (select HAND_POINT_NAME
                      from ${meliSchema}.tlirl0304 t
                      where t.SEG_NO = tlirl0402.SEG_NO
                        and t.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID)             as "targetHandPointName",
                     tlirl0402.VEHICLE_NO                                                 as "vehicleNo",
                     REPLACE(ifnull(ifnull((select ITEM_CNAME
                                            from iplat4j.tedcm01 tedcm01
                                            where tedcm01.ITEM_CODE =
                                                  (select tlirl0201.TYPE_OF_HANDLING
                                                   from ${meliSchema}.tlirl0201 tlirl0201
                                                   where 1 = 1
                                                     and tlirl0201.STATUS = '20'
                                                     and tlirl0201.IS_RESERVATION = '10'
                                                     and tlirl0201.SEG_NO = tlirl0402.SEG_NO
                                                     and tlirl0201.RESERVATION_NUMBER =
                                                         (select tlirl0301.RESERVATION_NUMBER
                                                          from ${meliSchema}.tlirl0301 tlirl0301
                                                          where 1 = 1
                                                            and tlirl0301.SEG_NO = tlirl0402.SEG_NO
                                                            and tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                            and tlirl0301.STATUS > 00
                                                          limit 1)
                                                   limit 1)
                                              and tedcm01.CODESET_CODE = 'P007'),
                                           (select ITEM_CNAME
                                            from iplat4j.tedcm01 tedcm01
                                            where tedcm01.ITEM_CODE =
                                                  (select tlirl0302.HAND_TYPE
                                                   from ${meliSchema}.tlirl0302 tlirl0302
                                                   where 1 = 1
                                                     and tlirl0302.STATUS > 00
                                                     and tlirl0302.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                     and tlirl0302.VOUCHER_NUM != ''
                                                   limit 1)
                                              and tedcm01.CODESET_CODE = 'P007')), (select ITEM_CNAME
                                                                                    from iplat4j.tedcm01 tedcm01
                                                                                    where tedcm01.ITEM_CODE =
                                                                                          (select BUSINESS_TYPE
                                                                                           from ${meliSchema}.tlirl0302 tlirl0302
                                                                                           where 1 = 1
                                                                                             and tlirl0302.STATUS > 00
                                                                                             and tlirl0302.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                                                           limit 1)
                                                                                      and tedcm01.CODESET_CODE = 'P010')),
                             '钢材卸货+装货', '卸货装货')                                 as "businessType",
                     tlirl0402.REC_CREATE_TIME                                            as "recCreateTime",
                     TIMESTAMPDIFF(MINUTE, STR_TO_DATE(tlirl0402.REC_CREATE_TIME, '%Y%m%d%H%i%s'), now()) as "waitingTime",
                     ifnull(
                         (select tlirl0307.D_USER_NAME
                         from meli.tlirl0307 tlirl0307
                         where tlirl0307.SEG_NO = tlirl0402.SEG_NO
                         and tlirl0307.VOUCHER_NUM = tlirl0402.VOUCHER_NUM
                         limit 1)
                         ,' ')   as "customerName",
                     (SELECT tlirl0201.RESERVATION_NUMBER
                      FROM ${meliSchema}.tlirl0201 tlirl0201
                      WHERE tlirl0201.RESERVATION_NUMBER =
                            (SELECT tlirl0301.RESERVATION_NUMBER
                             FROM ${meliSchema}.tlirl0301 tlirl0301
                             WHERE tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                               AND tlirl0301.STATUS != '00'
                      LIMIT 1)
            AND tlirl0201.STATUS = '20'
            LIMIT 1) AS "reservationNumber"
              from ${meliSchema}.tlirl0402 tlirl0402,
                   ${meliSchema}.tlirl0305 tlirl0305
              where 1 = 1
                and tlirl0402.SEG_NO = tlirl0305.SEG_NO
                and tlirl0402.VEHICLE_NO = tlirl0305.VEHICLE_ID
                and tlirl0402.TARGET_HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                and tlirl0402.CAR_TRACE_NO = tlirl0305.CAR_TRACE_NO
                and tlirl0402.SEG_NO = #segNo#
                and tlirl0402.DEL_FLAG = '0'

              union

              select tlirl0402.TARGET_HAND_POINT_ID                                       as "targetHandPointId",
                     (select HAND_POINT_NAME
                      from ${meliSchema}.tlirl0304 t
                      where t.SEG_NO = tlirl0402.SEG_NO
                        and t.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID)             as "targetHandPointName",
                     tlirl0402.VEHICLE_NO                                                 as "vehicleNo",
                     REPLACE(ifnull(ifnull((select ITEM_CNAME
                                            from iplat4j.tedcm01 tedcm01
                                            where tedcm01.ITEM_CODE =
                                                  (select tlirl0201.TYPE_OF_HANDLING
                                                   from ${meliSchema}.tlirl0201 tlirl0201
                                                   where 1 = 1
                                                     and tlirl0201.STATUS = '20'
                                                     and tlirl0201.IS_RESERVATION = '10'
                                                     and tlirl0201.SEG_NO = tlirl0402.SEG_NO
                                                     and tlirl0201.RESERVATION_NUMBER =
                                                         (select tlirl0301.RESERVATION_NUMBER
                                                          from ${meliSchema}.tlirl0301 tlirl0301
                                                          where 1 = 1
                                                            and tlirl0301.SEG_NO = tlirl0402.SEG_NO
                                                            and tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                            and tlirl0301.STATUS > 00
                                                          limit 1)
                                                   limit 1)
                                              and tedcm01.CODESET_CODE = 'P007'),
                                           (select ITEM_CNAME
                                            from iplat4j.tedcm01 tedcm01
                                            where tedcm01.ITEM_CODE =
                                                  (select tlirl0302.HAND_TYPE
                                                   from ${meliSchema}.tlirl0302 tlirl0302
                                                   where 1 = 1
                                                     and tlirl0302.STATUS > 00
                                                     and tlirl0302.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                     and tlirl0302.VOUCHER_NUM != ''
                                                   limit 1)
                                              and tedcm01.CODESET_CODE = 'P007')), (select ITEM_CNAME
                                                                                    from iplat4j.tedcm01 tedcm01
                                                                                    where tedcm01.ITEM_CODE =
                                                                                          (select BUSINESS_TYPE
                                                                                           from ${meliSchema}.tlirl0302 tlirl0302
                                                                                           where 1 = 1
                                                                                             and tlirl0302.STATUS > 00
                                                                                             and tlirl0302.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                                                           limit 1)
                                                                                      and tedcm01.CODESET_CODE = 'P010')),
                             '钢材卸货+装货', '卸货装货')                                 as "businessType",
                     tlirl0402.REC_CREATE_TIME                                            AS "recCreateTime",
                     TIMESTAMPDIFF(MINUTE, STR_TO_DATE((select tlirl0301.CHECK_DATE
                                                        from ${meliSchema}.tlirl0301 tlirl0301
                                                        where 1 = 1
                                                          and tlirl0301.SEG_NO = tlirl0402.SEG_NO
                                                          and tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                          and tlirl0301.STATUS > 00
                                                        limit 1), '%Y%m%d%H%i%s'), now()) as "waitingTime",
                     ifnull(
                             (select tlirl0307.D_USER_NAME
                              from meli.tlirl0307 tlirl0307
                              where tlirl0307.SEG_NO = tlirl0402.SEG_NO
                                and tlirl0307.VOUCHER_NUM = tlirl0402.VOUCHER_NUM
                                 limit 1)
                         ,' ')   as "customerName",
                     (SELECT tlirl0201.RESERVATION_NUMBER
                      FROM ${meliSchema}.tlirl0201 tlirl0201
                      WHERE tlirl0201.RESERVATION_NUMBER =
                            (SELECT tlirl0301.RESERVATION_NUMBER
                             FROM ${meliSchema}.tlirl0301 tlirl0301
                             WHERE tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                               AND tlirl0301.STATUS != '00'
                      LIMIT 1)
                      AND tlirl0201.STATUS = '20'
                      LIMIT 1) AS "reservationNumber"
              from ${meliSchema}.tlirl0402 tlirl0402,
                   ${meliSchema}.tlirl0404 tlirl0305
              where 1 = 1
                and tlirl0402.SEG_NO = tlirl0305.SEG_NO
                and tlirl0402.VEHICLE_NO = tlirl0305.VEHICLE_NO
                and tlirl0402.CAR_TRACE_NO = tlirl0305.CAR_TRACE_NO
                and tlirl0402.TARGET_HAND_POINT_ID = tlirl0305.HAND_POINT_ID
                and tlirl0402.SEG_NO = #segNo#
                and tlirl0402.DEL_FLAG = '0') tlirl0402
        order by tlirl0402.recCreateTime asc

    </select>
    <!--重庆看板-->
    <select id="lineUpAndCallForNumbersCQ" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT *
        FROM (select tlirl0402.TARGET_HAND_POINT_ID                                                       as "targetHandPointId",
                     (select HAND_POINT_NAME
                      from MELI.tlirl0304 t
                      where t.SEG_NO = tlirl0402.SEG_NO
                        and t.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID)                             as "targetHandPointName",
                     tlirl0402.VEHICLE_NO                                                                 as "vehicleNo",
                     (select ITEM_CNAME
                      from iplat4j.tedcm01 tedcm01
                      where tedcm01.ITEM_CODE =(select TYPE_OF_HANDLING
                      from meli.tlirl0201 tlirl0201
                      where 1 = 1
                        and tlirl0201.SEG_NO = tlirl0402.SEG_NO
                        AND tlirl0201.VEHICLE_NO = tlirl0402.VEHICLE_NO
                        AND tlirl0201.RESERVATION_NUMBER = (SELECT tlirl0301.RESERVATION_NUMBER
                                                            from meli.tlirl0301 tlirl0301
                                                            where 1 = 1
                                                              and tlirl0301.SEG_NO = tlirl0402.SEG_NO
                                                              and tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                              and tlirl0301.VEHICLE_NO = tlirl0402.VEHICLE_NO
                                                                                                             limit 1))
                                                                                                             and tedcm01.CODESET_CODE = 'P007')                                     as "businessType",
             tlirl0402.REC_CREATE_TIME                                                            as "recCreateTime",
             TIMESTAMPDIFF(MINUTE, STR_TO_DATE(tlirl0402.REC_CREATE_TIME, '%Y%m%d%H%i%s'), now()) as "waitingTime"

        from MELI.tlirl0402 tlirl0402
        where 1 = 1
          and tlirl0402.SEG_NO = #segNo#
          and tlirl0402.DEL_FLAG = '0'
          and exists(SELECT 1
            from meli.tlirl0301 tlirl0301
            where 1 = 1
          and tlirl0301.SEG_NO = tlirl0402.SEG_NO
          and tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
          and tlirl0301.VEHICLE_NO = tlirl0402.VEHICLE_NO
            )
            ) tlirl0402
        order by tlirl0402.recCreateTime asc
    </select>
    <select id="queryOvertVehicleno" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select * from (select distinct tlirl0301.VEHICLE_NO                       as "vehicleNo",
                                       ifnull(tlirl0301.TARGET_HAND_POINT_ID,
                                              tlirl0301.CURRENT_HAND_POINT_ID)    as "targetHandPointId",
                                       tlirl0301.CURRENT_HAND_POINT_ID            as "currentHandPointId",
                                       ifnull(tlirl0301.STATUS, tlirl0301.STATUS) as "status",
                                       (select HAND_POINT_NAME
                                        from MELI.tlirl0304 tlirl0304
                                        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                                          and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
                                          and tlirl0304.STATUS = '30'
                                                                                     limit 1) AS "handPointName",
                      (select HAND_POINT_NAME
                       from MELI.tlirl0304 tlirl0304
                       where tlirl0304.SEG_NO = tlirl0301.SEG_NO
                         and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
                         and tlirl0304.STATUS = '30' limit 1) AS "currentHandPointName",
                      (case when tlirl0301.STATUS = '20' then TIMESTAMPDIFF(MINUTE, STR_TO_DATE(ENTER_FACTORY, '%Y%m%d%H%i%s'),
                                                                     now())
                     when tlirl0301.STATUS = '30' then TIMESTAMPDIFF(MINUTE,
                                                                     (select STR_TO_DATE(max(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s')
                                                                      from MELI.tlirl0406 tlirl0406
                                                                      where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                                                        and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                        and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                                     now())
                     else case
                              when (select tlirl0406.REMARK
                                    from MELI.tlirl0407 tlirl0406
                                    where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                      and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                      and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                    order by tlirl0406.REC_REVISE_TIME desc
                                    limit 1) != ' ' then TIMESTAMPDIFF(MINUTE,
                                                                       (select STR_TO_DATE(MAX(tlirl0406.REC_REVISE_TIME), '%Y%m%d%H%i%s')
                                                                        from MELI.tlirl0407 tlirl0406
                                                                        where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                                                          and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                          and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                                       now())
                              else TIMESTAMPDIFF(MINUTE,
                                                 (select STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
                                                  from MELI.tlirl0407 tlirl0407
                                                  where tlirl0407.SEG_NO = tlirl0301.SEG_NO
                                                    and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                    and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                 now()) end end) as "waitingTime",
                      REPLACE(if((select tlirl0302.HAND_TYPE
                                  from ${meliSchema}.tlirl0302 tlirl0302
                                  where 1 = 1
                                    and tlirl0302.STATUS > 00
                                    and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO) = '',
                                 (select ITEM_CNAME
                                  from iplat4j.tedcm01 tedcm01
                                  where tedcm01.ITEM_CODE = (select tlirl0201.TYPE_OF_HANDLING
                                                             from ${meliSchema}.tlirl0201 tlirl0201
                                                             where 1 = 1
                                                               and tlirl0201.STATUS > 00
                                                               and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER)
                                    and tedcm01.CODESET_CODE = 'P007'),
                                 ifnull(ifnull((select ITEM_CNAME
                                                from iplat4j.tedcm01 tedcm01
                                                where tedcm01.ITEM_CODE =
                                                      (select tlirl0201.TYPE_OF_HANDLING
                                                       from ${meliSchema}.tlirl0201 tlirl0201
                                                       where 1 = 1
                                                         and tlirl0201.STATUS = '20'
                                                         and tlirl0201.IS_RESERVATION = '10'
                                                         and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                                         and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                                     limit 1)
                                                    and tedcm01.CODESET_CODE = 'P007'),
                                                 (select ITEM_CNAME
                                                  from iplat4j.tedcm01 tedcm01
                                                  where tedcm01.ITEM_CODE = (select tlirl0302.HAND_TYPE
                                                                             from ${meliSchema}.tlirl0302 tlirl0302
                                                                             where 1 = 1
                                                                               and tlirl0302.STATUS > 00
                                                                               and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                                                               and tlirl0302.VOUCHER_NUM != '')
                                                    and tedcm01.CODESET_CODE = 'P007')),
                                        (select ITEM_CNAME
                                         from iplat4j.tedcm01 tedcm01
                                         where tedcm01.ITEM_CODE =
                                               (select BUSINESS_TYPE
                                                from ${meliSchema}.tlirl0302 tlirl0302
                                                where 1 = 1
                                                  and tlirl0302.STATUS > 00
                                                  and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                            limit 1)
                                             and tedcm01.CODESET_CODE = 'P010'))), '钢材卸货+装货', '卸货装货')
                          as "businessType",
                      REPLACE(if((select tlirl0302.HAND_TYPE
                                  from MELI.tlirl0302 tlirl0302
                                  where 1 = 1
                                    and tlirl0302.STATUS > 00
                                    and tlirl0302.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO) = '', (select tlirl0201.TYPE_OF_HANDLING
                                                                                                from MELI.tlirl0201 tlirl0201
                                                                                                where 1 = 1
                                                                                                  and tlirl0201.STATUS > 00
                                                                                                  and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER),
                                 ifnull(ifnull((select tlirl0201.TYPE_OF_HANDLING
                                                from MELI.tlirl0201 tlirl0201
                                                where 1 = 1
                                                  and tlirl0201.STATUS = '20'
                                                  and tlirl0201.IS_RESERVATION = '10'
                                                  and tlirl0201.SEG_NO = tlirl0301.SEG_NO
                                                  and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                                     limit 1), tlirl0301.HAND_TYPE),
                                        BUSINESS_TYPE)), '30',
                              '10')                                                                     as "businessTypeCode",
                      ifnull(
                              (select tlirl0307.D_USER_NAME
                               from meli.tlirl0307 tlirl0307
                               where tlirl0307.SEG_NO = tlirl0302.SEG_NO
                                 and tlirl0307.VOUCHER_NUM = tlirl0302.VOUCHER_NUM
                                  limit 1)
            ,' ')   as "customerName",
                      (select tlirl0201.START_OF_TRANSPORT
                       from ${meliSchema}.tlirl0201 tlirl0201
                       where tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
                         and tlirl0201.TYPE_OF_HANDLING = '20'
                         and tlirl0201.STATUS = '20'
                         and tlirl0301.STATUS != '00'
            limit 1) as "startOfTransport"
            from MELI.tlirl0301 tlirl0301,
            MELI.tlirl0302 tlirl0302
        where 1 = 1
          AND tlirl0301.SEG_NO = tlirl0302.SEG_NO
          AND tlirl0301.CAR_TRACE_NO = tlirl0302.CAR_TRACE_NO
          AND tlirl0301.VEHICLE_NO = tlirl0302.VEHICLE_NO
          AND tlirl0301.STATUS in ('20'
            , '30'
            , '40')
          AND (tlirl0301.TARGET_HAND_POINT_ID !=' '
           or tlirl0301.CURRENT_HAND_POINT_ID !=' ')
          AND tlirl0301.SEG_NO = #segNo#
          AND tlirl0302.STATUS != '00'
        ORDER by "waitingTime" DESC
            ) A
        where 1=1
          and A.waitingTime > #timer#
    </select>
</sqlMap>