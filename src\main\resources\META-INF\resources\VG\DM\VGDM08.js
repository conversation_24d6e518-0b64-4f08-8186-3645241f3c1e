$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfoMainQuery",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });
    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
    });
    var addFlag = false;
    var updateFlag = false;
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId === "info-2") {
                    // 只判断非新增和修改按钮跳转
                    if (!addFlag && !updateFlag) {
                        const checkedRows = resultGrid.getCheckedRows();
                        const checkRowLength = checkedRows.length;
                        if (checkRowLength !== 1) {
                            NotificationUtil({msg: "请勾选一条检修计划信息"}, "error");
                            e.preventDefault();
                        } else {
                            setDetailData(checkedRows[0]);
                            setReadStatus();
                        }
                    }
                } else {
                    addFlag = false;
                    updateFlag = false;
                }
                // 异常信息
                if (tableId === "info-3") {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "请勾选一条检修计划信息"}, "error");
                        e.preventDefault();
                    } else {
                        // 清除原数据
                        IPLAT.clearNode(document.getElementById("detail2"));
                        // 没有异常信息时返回
                        const exceptionContactId = checkedRows[0].exceptionContactId;
                        if (IPLAT.isBlankString(exceptionContactId)) {
                            return;
                        }
                        const eiInfo = new EiInfo();
                        eiInfo.set("exceptionContactId", exceptionContactId);
                        IMOMUtil.submitEiInfo(eiInfo, "VGDM08", "queryExceptionContact", function (ei) {
                            var detail2 = document.getElementById("detail2");
                            IPLAT.fillNode(detail2, ei);
                        });
                    }
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        "result": {
            onRowDblClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                setReadStatus();
                tab_Strip.select(1);
            },
            loadComplete: function (grid) {
                // 查看流程图
                $("#FLOWCHART").on("click", function (e) {
                    IMOMUtil.handleFlowchartClick(resultGrid);
                });
                // 新增按钮
                $("#INSERT1").on("click", function (e) {
                    addFlag = true;
                    IPLAT.clearNode(document.getElementById("info-2"));
                    setInsertStatus();
                    tab_Strip.select(1);
                    $("#detail_status-0-unitCode").val(unitInfo.unitCode);
                    IPLAT.EFSelect.value($("#detail_status-0-segNo"), unitInfo.segNo);
                    $("#inqu2_status-0-segNo").val(unitInfo.segNo);
                    $("#inqu2_status-0-overhaulPlanId").val('');
                    $("#inqu3_status-0-segNo").val(unitInfo.segNo);
                    $("#inqu3_status-0-overhaulPlanId").val('');
                    $("#inqu4_status-0-segNo").val(unitInfo.segNo);
                    $("#inqu4_status-0-overhaulPlanId").val('');
                    $("#inqu5_status-0-segNo").val(unitInfo.segNo);
                    $("#inqu5_status-0-overhaulPlanId").val('');
                    $("#inqu6_status-0-segNo").val(unitInfo.segNo);
                    $("#inqu6_status-0-overhaulPlanId").val('');
                    $("#inqu7_status-0-segNo").val(unitInfo.segNo);
                    $("#inqu7_status-0-overhaulPlanId").val('');
                });
                // 修改按钮
                $("#UPDATE1").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能对一行数据进行修改！]"}, "error");
                        return;
                    }
                    const overhaulPlanStatus = checkedRows[0].overhaulPlanStatus;
                    if (overhaulPlanStatus !== "10") {
                        NotificationUtil({msg: "操作失败，原因[只能新增状态数据进行修改！]"}, "error");
                        return;
                    }
                    updateFlag = true;
                    setDetailData(checkedRows[0]);
                    setUpdateStatus();
                    tab_Strip.select(1);
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM08", "submit", true, null, null, false);
                });
                // 取消提交按钮
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM08", "cancel", true, null, null, false);
                });
                // 新增保存按钮
                $("#INSERT_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM08", "insert", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
                // 修改保存按钮
                $("#UPDATE_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM08", "update", function (ei) {
                        tab_Strip.select(0);
                        resultGrid.dataSource.page(1);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
                // 打印按钮
                $("#PRINT").on("click", function (e) {
                    const rows = resultGrid.getCheckedRows();
                    const rowLength = rows.length;
                    if (rowLength < 1) {
                        NotificationUtil({msg: "请勾选要打印的检修计划信息"}, "error");
                        return;
                    }
                    let ids = "";
                    for (let i = 0; i < rowLength; i++) {
                        if (i > 0) {
                            ids += ",";
                        }
                        ids += "'" + rows[i].overhaulPlanId + "'";
                    }
                    const url = reportAddress + "VGDMR001" + ".cpt&op=write&ids=" + ids;
                    window.open(url);
                });
                // 月度计划按钮
                $("#PRINTM").on("click", function (e) {
                    const rows = resultGrid.getCheckedRows();
                    const rowLength = rows.length;
                    if (rowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能选择一条数据打印！]"}, "error");
                        return;
                    }
                    const overhaulQuality=rows[0].overhaulQuality;
                    if(overhaulQuality!=='20'){
                        NotificationUtil({msg: "操作失败，原因[只能选择月度计划打印！]"}, "error");
                        return;
                    }
                    const overhaulPlanId=rows[0].overhaulPlanId;
                    const url = reportAddress + "/VGDM/VGDMR002" + ".cpt&overhaulPlanId=" + overhaulPlanId;
                    window.open(url);
                });
            }
        },
        // 检修项目
        "result2": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                let maxSortIndex = getMaxSortIndex(result2Grid);
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["itemType"] = "10";
                    item["overhaulPlanId"] = $("#detail_status-0-overhaulPlanId").val();
                    item["segNo"] = $("#detail_status-0-unitCode").val();
                    item["unitCode"] = item["segNo"];
                    item["sortIndex"] = ++maxSortIndex;
                });
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT2").on("click", function (e) {
                    result2Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0805", "insert", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0805", "update", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE2").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result2Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result2", "VGDM0805", "delete", false,
                        function (ei) {
                            result2Grid.removeRows(result2Grid.getCheckedRows());
                        }, null, false);
                });
            }
        },
        // 检修工具
        "result3": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                let maxSortIndex = getMaxSortIndex(result3Grid);
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["dataType"] = "10";
                    item["overhaulPlanId"] = $("#detail_status-0-overhaulPlanId").val();
                    item["segNo"] = $("#detail_status-0-unitCode").val();
                    item["unitCode"] = item["segNo"];
                    item["sortIndex"] = ++maxSortIndex;
                });
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT3").on("click", function (e) {
                    result3Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE3").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result3Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result3", "VGDM0806", "insert", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE3").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result3Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result3", "VGDM0806", "update", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE3").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result3Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result3", "VGDM0806", "delete", false,
                        function (ei) {
                            result3Grid.removeRows(result3Grid.getCheckedRows());
                        }, null, false);
                });
            }
        },
        // 检修资材
        "result4": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                let maxSortIndex = getMaxSortIndex(result4Grid);
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["dataType"] = "20";
                    item["overhaulPlanId"] = $("#detail_status-0-overhaulPlanId").val();
                    item["segNo"] = $("#detail_status-0-unitCode").val();
                    item["unitCode"] = item["segNo"];
                    item["sortIndex"] = ++maxSortIndex;
                });
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT4").on("click", function (e) {
                    result4Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE4").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result4Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result4", "VGDM0806", "insert4", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE4").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result4Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result4", "VGDM0806", "update4", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE4").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result4Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result4", "VGDM0806", "delete4", false,
                        function (ei) {
                            result4Grid.removeRows(result4Grid.getCheckedRows());
                        }, null, false);
                });
            }
        },
        // 定期检查项目
        "result5": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            columns: [
                {
                    field: "pictureOperate",
                    template: function (e) {
                        var viewHref = '';
                        if (!IPLAT.isBlankString(e.pictureUrl)) {
                            viewHref = '<a href="' + e.pictureUrl + '" target="_blank">查看</a>';
                        }
                        return viewHref;
                    }
                }
            ],
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                let maxSortIndex = getMaxSortIndex(result5Grid);
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["overhaulPlanId"] = $("#detail_status-0-overhaulPlanId").val();
                    item["segNo"] = $("#detail_status-0-unitCode").val();
                    item["unitCode"] = item["segNo"];
                    item["pictureUrl"] = '';
                    item["pictureOperate"] = '';
                    item["sortIndex"] = ++maxSortIndex;
                });
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT5").on("click", function (e) {
                    result5Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE5").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result5Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result5", "VGDM0807", "insert", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE5").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result5Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result5", "VGDM0807", "update", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE5").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result5Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result5", "VGDM0807", "delete", false,
                        function (ei) {
                            result7Grid.removeRows(result7Grid.getCheckedRows());
                        }, null, false);
                });
                // 上传附件
                $("#UPLOADFILE5").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(result5Grid)) {
                        return;
                    }
                    relevanceId = generateCompactUUID();
                    // console.log(relevanceId);
                    $("#fileForm").click();
                });
            }
        },
        // 维修项目
        "result6": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                let maxSortIndex = getMaxSortIndex(result6Grid);
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["itemType"] = "20";
                    item["overhaulPlanId"] = $("#detail_status-0-overhaulPlanId").val();
                    item["segNo"] = $("#detail_status-0-unitCode").val();
                    item["unitCode"] = item["segNo"];
                    item["sortIndex"] = ++maxSortIndex;
                });
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT6").on("click", function (e) {
                    result6Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE6").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result6Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result6", "VGDM0805", "insert6", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE6").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result6Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result6", "VGDM0805", "update6", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE6").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result6Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result6", "VGDM0805", "delete6", false,
                        function (ei) {
                            result6Grid.removeRows(result6Grid.getCheckedRows());
                        }, null, false);
                });
            }
        },
        // 给油脂记录
        "result7": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["overhaulPlanId"] = $("#detail_status-0-overhaulPlanId").val();
                    item["segNo"] = $("#detail_status-0-unitCode").val();
                    item["unitCode"] = item["segNo"];
                });
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 新增按钮
                $("#INSERT7").on("click", function (e) {
                    result7Grid.addRow();
                });
                // 新增保存按钮
                $("#INSERTSAVE7").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result7Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result7", "VGDM0808", "insert", true,
                        null, null, false);
                });
                // 修改按钮
                $("#UPDATE7").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result7Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result7", "VGDM0808", "update", true,
                        null, null, false);
                });
                // 删除按钮
                $("#DELETE7").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result7Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result7", "VGDM0808", "delete", false,
                        function (ei) {
                            result7Grid.removeRows(result7Grid.getCheckedRows());
                        }, null, false);
                });
            }
        }
    };
    IPLATUI.EFSelect = {
        //施工类别
        "detail_status-0-overhaulType": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                const dataItem = e.dataItem;
                // 10-自修. 20-委外.
                if (dataItem.valueField === "10") {
                    //设置实施人为设备人员
                    $("#detail_status-0-implementManName").val(loginCName);
                }
                if (dataItem.valueField === "20") {
                    //设置实施人为委外人员
                    $("#detail_status-0-implementManName").val("委外人员");
                }
            }
        },
        // 检修性质
        "detail_status-0-overhaulQuality": {
            // 点击下拉选项时触发
            select: function (e) {
                //获取勾选值
                const dataItem = e.dataItem;
                // 20 月度检修
                if (dataItem.valueField === "20") {
                    // 显示月度检修明细
                    setMonthShow(true)
                } else {
                    // 隐藏月度检修明细
                    setMonthShow(false)
                }
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 详情区域业务单元弹窗
    IMOMUtil.importDetailUnitInfo();
    // 详情区域设备弹窗
    IMOMUtil.importDetailEquipmentInfo();
    // 详情区域分部设备弹窗
    IMOMUtil.importDetailDeviceInfo();

    // 月度计划相关按钮
    const planButtons = ["#INSERT2", "#INSERTSAVE2", "#UPDATE2", "#DELETE2",
        "#INSERT3", "#INSERTSAVE3", "#UPDATE3", "#DELETE3",
        "#INSERT4", "#INSERTSAVE4", "#UPDATE4", "#DELETE4",
        "#INSERT5", "#INSERTSAVE5", "#UPDATE5", "#DELETE5", "#UPLOADFILE5",
        "#INSERT6", "#INSERTSAVE6", "#UPDATE6", "#DELETE6",
        "#INSERT7", "#INSERTSAVE7", "#UPDATE7", "#DELETE7",
    ];

    /**
     * 设置月度计划详情是否显示
     * @param flag true:显示 false:隐藏
     */
    function setMonthShow(flag) {
        if (flag) {
            $("#result2").show();
            $("#result3").show();
            $("#result4").show();
            $("#result5").show();
            $("#result6").show();
            $("#result7").show();
        } else {
            $("#result2").hide();
            $("#result3").hide();
            $("#result4").hide();
            $("#result5").hide();
            $("#result6").hide();
            $("#result7").hide();
        }
    }

    /**
     * 设置详情区域新增状态
     */
    function setInsertStatus() {
        // 新增按钮启用
        $("#INSERT_SAVE").attr("disabled", false);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", false);
        $("#detail_status-0-equipmentName").attr("disabled", false);
        $("#detail_status-0-deviceName").attr("disabled", false);
        setFieldEdit(true);
        // 检修来源设置为手工新增
        IPLAT.EFSelect.value($("#detail_status-0-overhaulSource"), "1");
        // 月度计划相关按钮
        setButtonEdit(planButtons, false);
        // 清除检修计划进度信息
        if (result2Grid.getDataItems().length > 0) {
            result2Grid.removeRows(result2Grid.getDataItems());
        }
        // 清除检修工具清单信息
        if (result3Grid.getDataItems().length > 0) {
            result3Grid.removeRows(result3Grid.getDataItems());
        }
        // 清除检修资材信息
        if (result4Grid.getDataItems().length > 0) {
            result4Grid.removeRows(result4Grid.getDataItems());
        }
        // 清除检查项目信息
        if (result5Grid.getDataItems().length > 0) {
            result5Grid.removeRows(result5Grid.getDataItems());
        }
        // 清除维修项目信息
        if (result6Grid.getDataItems().length > 0) {
            result6Grid.removeRows(result6Grid.getDataItems());
        }
        // 清除给油脂信息
        if (result7Grid.getDataItems().length > 0) {
            result7Grid.removeRows(result7Grid.getDataItems());
        }
    }

    /**
     * 设置详情区域修改状态
     */
    function setUpdateStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮启用
        $("#UPDATE_SAVE").attr("disabled", false);
        // 检修来源判断设备是否可修改
        const overhaulSource = IPLAT.EFSelect.value($("#detail_status-0-overhaulSource"));
        const isEdit = overhaulSource === "1";
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", !isEdit);
        $("#detail_status-0-deviceName").attr("disabled", !isEdit);
        setFieldEdit(true);
        // 月度计划相关按钮
        setButtonEdit(planButtons, true);
    }

    /**
     * 设置详情区域只读状态
     */
    function setReadStatus() {
        // 新增按钮禁用
        $("#INSERT_SAVE").attr("disabled", true);
        // 修改按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 各字段状态
        $("#detail_status-0-unitCode").attr("disabled", true);
        $("#detail_status-0-equipmentName").attr("disabled", true);
        $("#detail_status-0-deviceName").attr("disabled", true);
        setFieldEdit(false);
        // 月度计划相关按钮
        setButtonEdit(planButtons, false);
    }

    /**
     * 设置字段是否可编辑
     * @param {boolean} isEdit 是否可编辑
     */
    function setFieldEdit(isEdit) {
        IPLAT.EFSelect.enable($("#detail_status-0-overhaulQuality"), isEdit);
        IPLAT.EFSelect.enable($("#detail_status-0-overhaulType"), isEdit);
        $("#detail_status-0-overhaulStartDate").data("kendoDateTimePicker").enable(isEdit);
        $("#detail_status-0-overhaulEndDate").data("kendoDateTimePicker").enable(isEdit);
        $("#detail_status-0-outsourcingContactId").attr("readonly", !isEdit);
        $("#detail_status-0-overhaulNumber").attr("readonly", !isEdit);
        $("#detail_status-0-overhaulProject").attr("readonly", !isEdit);
        $("#detail_status-0-securityMeasures").attr("readonly", !isEdit);
        $("#detail_status-0-acceptanceCriteria").attr("readonly", !isEdit);
    }

    /**
     * 设置按钮是否可编辑
     * @param {Array} buttons 按钮列表
     * @param {boolean} isEdit 是否可编辑
     */
    function setButtonEdit(buttons, isEdit) {
        $.each(buttons, function (index, item) {
            if (isEdit) {
                $(item).css("pointer-events", "auto");
                $(item).find("button").attr("disabled", false);
            } else {
                $(item).css("pointer-events", "none");
                $(item).find("button").attr("disabled", true);
            }
        })
    }

    /**
     * 设置详情区域内容
     * @param row grid行数据
     */
    function setDetailData(row) {
        // 清除原数据
        IPLAT.clearNode(document.getElementById("info-2"));
        // 将数据回填到详情页
        IMOMUtil.fillNode(row, "detail");
        $("#inqu2_status-0-segNo").val(row.segNo);
        $("#inqu2_status-0-overhaulPlanId").val(row.overhaulPlanId);
        $("#inqu2_status-0-itemType").val("10");
        $("#inqu3_status-0-segNo").val(row.segNo);
        $("#inqu3_status-0-overhaulPlanId").val(row.overhaulPlanId);
        $("#inqu3_status-0-dataType").val("10");
        $("#inqu4_status-0-segNo").val(row.segNo);
        $("#inqu4_status-0-overhaulPlanId").val(row.overhaulPlanId);
        $("#inqu4_status-0-dataType").val("20");
        $("#inqu5_status-0-segNo").val(row.segNo);
        $("#inqu5_status-0-overhaulPlanId").val(row.overhaulPlanId);
        $("#inqu6_status-0-segNo").val(row.segNo);
        $("#inqu6_status-0-overhaulPlanId").val(row.overhaulPlanId);
        $("#inqu6_status-0-itemType").val("20");
        $("#inqu7_status-0-segNo").val(row.segNo);
        $("#inqu7_status-0-overhaulPlanId").val(row.overhaulPlanId);
        // 详情显示
        const overhaulQuality = IPLAT.EFSelect.value($("#detail_status-0-overhaulQuality"));
        if (overhaulQuality === "20") {
            setMonthShow(true)
            result2Grid.dataSource.page(1);
            result3Grid.dataSource.page(1);
            result4Grid.dataSource.page(1);
            result5Grid.dataSource.page(1);
            result6Grid.dataSource.page(1);
            result7Grid.dataSource.page(1);
        } else {
            setMonthShow(false)
        }
    }

    var relevanceId = "";
    var segNo = "";
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            // console.log("上传后：" + relevanceId);
            backPicUrl();
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM0807";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            segNo = $("#inqu7_status-0-segNo").val();
        }
    });

    /**
     * 反写图片地址到表格
     */
    function backPicUrl() {
        const eiInfo = new EiInfo();
        eiInfo.set("relevanceId", relevanceId);
        eiInfo.set("segNo", segNo);
        eiInfo.set("relevanceType", "VGDM0807");
        IMOMUtil.submitEiInfo(eiInfo, "VGDM0802", "queryUrl", function (ei) {
            const rows = result5Grid.getCheckedRows();
            result5Grid.setCellValue(rows[0], "pictureUrl", ei.get("downloadUrl"));
            result5Grid.setCellValue(rows[0], "pictureOperate", "1");
        });
    }

    /**
     * 生成uuid
     * @returns {string} uuid
     */
    function generateCompactUUID() {
        return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * 获取grid最多sortIndex
     * @param grid grid
     * @returns {number} 当前最大值
     */
    function getMaxSortIndex(grid) {
        const rows = grid.getDataItems();
        let maxSortIndex = 0;
        if (rows.length > 0) {
            for (let i = 0; i < rows.length; i++) {
                let sortIndex = Number(rows[i].sortIndex);
                if (sortIndex > maxSortIndex) {
                    maxSortIndex = sortIndex;
                }
            }
        }
        return maxSortIndex;
    }
});
