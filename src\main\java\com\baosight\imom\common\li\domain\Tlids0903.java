/**
* Generate time : 2024-12-18 16:32:33
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids0903
* 
*/
public class Tlids0903 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String warehouseCode = " ";		/* 仓库代码*/
                private String warehouseName = " ";		/* 仓库名称*/
                private String areaType = " ";		/* 区域类型*/
                private String areaCode = " ";		/* 区域代码/库位代码*/
                private String areaName = " ";		/* 区域名称/库位名称*/
                private String packId = " ";		/* 捆包号*/
                private String unitedPackId = " ";		/* 并包号*/
                private String labelId = " ";		/* 标签号*/
                private BigDecimal netWeight = new BigDecimal(0.00000000);		/* 净重*/
                private BigDecimal grossWeight = new BigDecimal(0.00000000);		/* 毛重*/
                private BigDecimal craneOperationWeight = new BigDecimal(0.00000000);		/* 吊装重量*/
                private Integer quantity = Integer.valueOf(0);		/* 数量*/
                private String posDirCode = " ";		/* 层数标记*/
                private String craneResultId = " ";		/* 行车实绩单号*/
                private String actionFlag = " ";		/* 板卷标记(0板 1卷)*/
                private String innerOutterPlateFlag = " ";		/* 内外版标记*/
                private String status = " ";		/* 状态*/
                private String x_position = "0";		/* X轴坐标*/
                private String y_position = "0";		/* X轴坐标*/
                private String z_position = "0";		/* Y轴坐标*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";		/* ID*/
                private String resumeCreationTime = " ";		/* 履历表插入时间*/
                private String originalPackId = " ";		/* 原料捆包号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaType");
        eiColumn.setDescName("区域类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaCode");
        eiColumn.setDescName("区域代码/库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaName");
        eiColumn.setDescName("区域名称/库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedPackId");
        eiColumn.setDescName("并包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setDescName("标签号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grossWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("毛重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneOperationWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("吊装重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("posDirCode");
        eiColumn.setDescName("层数标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneResultId");
        eiColumn.setDescName("行车实绩单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actionFlag");
        eiColumn.setDescName("板卷标记(0板 1卷)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("innerOutterPlateFlag");
        eiColumn.setDescName("内外版标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_position");
        eiColumn.setDescName("X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_position");
        eiColumn.setDescName("X轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("z_position");
        eiColumn.setDescName("Y轴坐标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("resumeCreationTime");
        eiColumn.setDescName("履历表插入时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("originalPackId");
        eiColumn.setDescName("原料捆包号");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tlids0903() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the warehouseCode - 仓库代码
        * @return the warehouseCode
        */
        public String getWarehouseCode() {
        return this.warehouseCode;
        }

        /**
        * set the warehouseCode - 仓库代码
        */
        public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
        }
        /**
        * get the warehouseName - 仓库名称
        * @return the warehouseName
        */
        public String getWarehouseName() {
        return this.warehouseName;
        }

        /**
        * set the warehouseName - 仓库名称
        */
        public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        }
        /**
        * get the areaType - 区域类型
        * @return the areaType
        */
        public String getAreaType() {
        return this.areaType;
        }

        /**
        * set the areaType - 区域类型
        */
        public void setAreaType(String areaType) {
        this.areaType = areaType;
        }
        /**
        * get the areaCode - 区域代码/库位代码
        * @return the areaCode
        */
        public String getAreaCode() {
        return this.areaCode;
        }

        /**
        * set the areaCode - 区域代码/库位代码
        */
        public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
        }
        /**
        * get the areaName - 区域名称/库位名称
        * @return the areaName
        */
        public String getAreaName() {
        return this.areaName;
        }

        /**
        * set the areaName - 区域名称/库位名称
        */
        public void setAreaName(String areaName) {
        this.areaName = areaName;
        }
        /**
        * get the packId - 捆包号
        * @return the packId
        */
        public String getPackId() {
        return this.packId;
        }

        /**
        * set the packId - 捆包号
        */
        public void setPackId(String packId) {
        this.packId = packId;
        }
        /**
        * get the unitedPackId - 并包号
        * @return the unitedPackId
        */
        public String getUnitedPackId() {
        return this.unitedPackId;
        }

        /**
        * set the unitedPackId - 并包号
        */
        public void setUnitedPackId(String unitedPackId) {
        this.unitedPackId = unitedPackId;
        }
        /**
        * get the labelId - 标签号
        * @return the labelId
        */
        public String getLabelId() {
        return this.labelId;
        }

        /**
        * set the labelId - 标签号
        */
        public void setLabelId(String labelId) {
        this.labelId = labelId;
        }
        /**
        * get the netWeight - 净重
        * @return the netWeight
        */
        public BigDecimal getNetWeight() {
        return this.netWeight;
        }

        /**
        * set the netWeight - 净重
        */
        public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
        }
        /**
        * get the grossWeight - 毛重
        * @return the grossWeight
        */
        public BigDecimal getGrossWeight() {
        return this.grossWeight;
        }

        /**
        * set the grossWeight - 毛重
        */
        public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
        }
        /**
        * get the craneOperationWeight - 吊装重量
        * @return the craneOperationWeight
        */
        public BigDecimal getCraneOperationWeight() {
        return this.craneOperationWeight;
        }

        /**
        * set the craneOperationWeight - 吊装重量
        */
        public void setCraneOperationWeight(BigDecimal craneOperationWeight) {
        this.craneOperationWeight = craneOperationWeight;
        }
        /**
        * get the quantity - 数量
        * @return the quantity
        */
        public Integer getQuantity() {
        return this.quantity;
        }

        /**
        * set the quantity - 数量
        */
        public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        }
        /**
        * get the posDirCode - 层数标记
        * @return the posDirCode
        */
        public String getPosDirCode() {
        return this.posDirCode;
        }

        /**
        * set the posDirCode - 层数标记
        */
        public void setPosDirCode(String posDirCode) {
        this.posDirCode = posDirCode;
        }
        /**
        * get the craneResultId - 行车实绩单号
        * @return the craneResultId
        */
        public String getCraneResultId() {
        return this.craneResultId;
        }

        /**
        * set the craneResultId - 行车实绩单号
        */
        public void setCraneResultId(String craneResultId) {
        this.craneResultId = craneResultId;
        }
        /**
        * get the actionFlag - 板卷标记(0板 1卷)
        * @return the actionFlag
        */
        public String getActionFlag() {
        return this.actionFlag;
        }

        /**
        * set the actionFlag - 板卷标记(0板 1卷)
        */
        public void setActionFlag(String actionFlag) {
        this.actionFlag = actionFlag;
        }
        /**
        * get the innerOutterPlateFlag - 内外版标记
        * @return the innerOutterPlateFlag
        */
        public String getInnerOutterPlateFlag() {
        return this.innerOutterPlateFlag;
        }

        /**
        * set the innerOutterPlateFlag - 内外版标记
        */
        public void setInnerOutterPlateFlag(String innerOutterPlateFlag) {
        this.innerOutterPlateFlag = innerOutterPlateFlag;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the x_position - X轴坐标
        * @return the x_position
        */
        public String getX_position() {
        return this.x_position;
        }

        /**
        * set the x_position - X轴坐标
        */
        public void setX_position(String x_position) {
        this.x_position = x_position;
        }
        /**
        * get the y_position - X轴坐标
        * @return the y_position
        */
        public String getY_position() {
        return this.y_position;
        }

        /**
        * set the y_position - X轴坐标
        */
        public void setY_position(String y_position) {
        this.y_position = y_position;
        }
        /**
        * get the z_position - Y轴坐标
        * @return the z_position
        */
        public String getZ_position() {
        return this.z_position;
        }

        /**
        * set the z_position - Y轴坐标
        */
        public void setZ_position(String z_position) {
        this.z_position = z_position;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the resumeCreationTime - 履历表插入时间
        * @return the resumeCreationTime
        */
        public String getResumeCreationTime() {
        return this.resumeCreationTime;
        }

        /**
        * set the resumeCreationTime - 履历表插入时间
        */
        public void setResumeCreationTime(String resumeCreationTime) {
        this.resumeCreationTime = resumeCreationTime;
        }

        public String getOriginalPackId() {
                return originalPackId;
        }

        public void setOriginalPackId(String originalPackId) {
                this.originalPackId = originalPackId;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
                setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
                setAreaType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaType")), areaType));
                setAreaCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaCode")), areaCode));
                setAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("areaName")), areaName));
                setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
                setUnitedPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedPackId")), unitedPackId));
                setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));
                setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
                setGrossWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("grossWeight")), grossWeight));
                setCraneOperationWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("craneOperationWeight")), craneOperationWeight));
                setQuantity(NumberUtils.toInteger(StringUtils.toString(map.get("quantity")), quantity));
                setPosDirCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("posDirCode")), posDirCode));
                setCraneResultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneResultId")), craneResultId));
                setActionFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actionFlag")), actionFlag));
                setInnerOutterPlateFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("innerOutterPlateFlag")), innerOutterPlateFlag));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setX_position(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("x_position")), x_position));
                setY_position(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("y_position")), y_position));
                setZ_position(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("z_position")), z_position));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setResumeCreationTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("resumeCreationTime")), resumeCreationTime));
                setOriginalPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("originalPackId")), originalPackId));

}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
                map.put("warehouseName",StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
                map.put("areaType",StringUtils.toString(areaType, eiMetadata.getMeta("areaType")));
                map.put("areaCode",StringUtils.toString(areaCode, eiMetadata.getMeta("areaCode")));
                map.put("areaName",StringUtils.toString(areaName, eiMetadata.getMeta("areaName")));
                map.put("packId",StringUtils.toString(packId, eiMetadata.getMeta("packId")));
                map.put("unitedPackId",StringUtils.toString(unitedPackId, eiMetadata.getMeta("unitedPackId")));
                map.put("labelId",StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));
                map.put("netWeight",StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
                map.put("grossWeight",StringUtils.toString(grossWeight, eiMetadata.getMeta("grossWeight")));
                map.put("craneOperationWeight",StringUtils.toString(craneOperationWeight, eiMetadata.getMeta("craneOperationWeight")));
                map.put("quantity",StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
                map.put("posDirCode",StringUtils.toString(posDirCode, eiMetadata.getMeta("posDirCode")));
                map.put("craneResultId",StringUtils.toString(craneResultId, eiMetadata.getMeta("craneResultId")));
                map.put("actionFlag",StringUtils.toString(actionFlag, eiMetadata.getMeta("actionFlag")));
                map.put("innerOutterPlateFlag",StringUtils.toString(innerOutterPlateFlag, eiMetadata.getMeta("innerOutterPlateFlag")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("x_position",StringUtils.toString(x_position, eiMetadata.getMeta("x_position")));
                map.put("y_position",StringUtils.toString(y_position, eiMetadata.getMeta("y_position")));
                map.put("z_position",StringUtils.toString(z_position, eiMetadata.getMeta("z_position")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("resumeCreationTime",StringUtils.toString(resumeCreationTime, eiMetadata.getMeta("resumeCreationTime")));
                map.put("originalPackId",StringUtils.toString(originalPackId, eiMetadata.getMeta("originalPackId")));


        return map;

}
}