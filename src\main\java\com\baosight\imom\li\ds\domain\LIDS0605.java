/**
 * Generate time : 2024-11-28 14:53:59
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Tlids0603
 */
public class LIDS0605 extends DaoEPBase {
    public static final String QUERY = "LIDS0605.query";
    public static final String COUNT = "LIDS0605.count";
    public static final String COUNTNUM = "LIDS0605.countNum";
    public static final String INSERT = "LIDS0605.insert";
    public static final String UPDATE = "LIDS0605.update";
    public static final String DELETE = "LIDS0605.delete";
    private String segNo = " ";        /* 系统账套*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String wproviderId = " ";        /* 仓库代码*/
    private String locationId = " ";        /* 库位代码*/
    private String locViewPackId = " ";        /* 库位捆包号(2位流水码)*/
    private String locColumnId = " ";        /* 库位(跨+列)*/
    private String useStatus = " ";        /* 状态。 10：空闲、20：预占、30：占用、99：禁用*/
    private String locationType = " ";        /* 库位类型。(10点状 20条状)*/
    private BigDecimal locationLength = new BigDecimal("0");        /* 库位长度 单位(cm)*/
    private BigDecimal specUpper = new BigDecimal("0");        /* 宽度上限 单位(mm)*/
    private BigDecimal specLower = new BigDecimal("0");        /* 宽度下限 单位(mm)*/
    private BigDecimal x_pointStart = new BigDecimal("0");        /* X轴起始点*/
    private BigDecimal x_pointEnd = new BigDecimal("0");        /* X轴结束点*/
    private BigDecimal y_pointCenter = new BigDecimal("0");        /* Y轴*/
    private String leftViewPackId = " ";        /* 同层相邻左侧库位捆包号（下层库位属性）*/
    private String rightViewPcakId = " ";        /* 同层相邻右侧库位捆包号（下层库位属性）*/
    private String upDownFlag = " ";        /* 上下层标记。1：下层、2：上层*/
    private String standFlag = " ";        /* 是否立式库位。0：卧式、1：立式*/
    private String lrAccupyFlag = " ";        /* 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷*/
    private String jgLockFlag = " ";        /* 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁*/
    private String upForbinFlag = " ";        /* 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷*/
    private String packId = " ";        /* 捆包号*/
    private String factoryProductId = " ";        /* 钢厂资源号*/
    private String productTypeId = " ";        /* 品种*/
    private String shopsign = " ";        /* 牌号*/
    private String spec = " ";        /* 规格*/
    private BigDecimal innerDiameter = new BigDecimal("0");        /* 内径 单位:mm*/
    private BigDecimal outerDiameter = new BigDecimal("0");        /* 外径 单位:mm*/
    private BigDecimal putinWeight = new BigDecimal("0");        /* 重量*/
    private BigDecimal refWidth = new BigDecimal("0");        /* 宽度 单位:mm*/
    private String customerId = " ";        /* 客户代码*/
    private String contractId = " ";        /* 销售合同*/
    private String remark = " ";        /* 备注*/
    private String locationName = " ";        /* 库位名称*/
    private String factoryArea = " ";        /* 厂区*/
    private String crossArea = " ";        /* 跨区*/
    private String surfaceGrade = " ";        /* 表面等级*/
    private String c_no = " ";        /* 所在跨区的库位序号*/
    private String upLeftViewPackId = " ";        /* 跨层相邻左侧库位捆包号（上层库位属性）*/
    private String upRightViewPackId = " ";        /* 跨层相邻右侧库位捆包号（上层库位属性）*/
    private BigDecimal idleIntervalId = new BigDecimal("0");        /* 条状库位连续空闲区间编号*/
    private BigDecimal avaliableMinLength = new BigDecimal("0");        /* 条状单个库位最小可用长度(单位：厘米cm)*/
    private String occupiedFlag = " ";        /* 已占用库位标记*/
    private BigDecimal x_pointStartOrign = new BigDecimal("0");        /* X轴起始点初始值*/
    private BigDecimal x_pointEndOrign = new BigDecimal("0");        /* X轴结束点初始值*/
    private BigDecimal pointLowerLength = new BigDecimal("0");        /* 点状库位长度下限*/
    private BigDecimal pointUpperLength = new BigDecimal("0");        /* 点状库位长度上限*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private String archiveFlag = " ";        /* 归档标记*/
    private String tenantUser = " ";        /* 租户*/
    private Integer delFlag = Integer.valueOf(0);        /* 删除标记*/
    private String uuid = " ";        /* ID*/
    private String factoryBuilding = " ";        /* 厂房代码*/
    private String factoryBuildingName = " ";        /* 厂房名称*/

    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wproviderId");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locViewPackId");
        eiColumn.setDescName("库位捆包号(2位流水码)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locColumnId");
        eiColumn.setDescName("库位(跨+列)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("useStatus");
        eiColumn.setDescName("状态。 10：空闲、20：预占、30：占用、99：禁用");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationType");
        eiColumn.setDescName("库位类型。(10点状 20条状)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("库位长度 单位(cm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specUpper");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("宽度上限 单位(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specLower");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("宽度下限 单位(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_pointStart");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("X轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_pointEnd");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("X轴结束点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_pointCenter");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("Y轴");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leftViewPackId");
        eiColumn.setDescName("同层相邻左侧库位捆包号（下层库位属性）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rightViewPcakId");
        eiColumn.setDescName("同层相邻右侧库位捆包号（下层库位属性）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upDownFlag");
        eiColumn.setDescName("上下层标记。1：下层、2：上层");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("standFlag");
        eiColumn.setDescName("是否立式库位。0：卧式、1：立式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lrAccupyFlag");
        eiColumn.setDescName("下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jgLockFlag");
        eiColumn.setDescName("下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upForbinFlag");
        eiColumn.setDescName("下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryProductId");
        eiColumn.setDescName("钢厂资源号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productTypeId");
        eiColumn.setDescName("品种");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spec");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("innerDiameter");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("内径 单位:mm");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outerDiameter");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("外径 单位:mm");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putinWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("refWidth");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("宽度 单位:mm");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("contractId");
        eiColumn.setDescName("销售合同");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossArea");
        eiColumn.setDescName("跨区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("surfaceGrade");
        eiColumn.setDescName("表面等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("c_no");
        eiColumn.setDescName("所在跨区的库位序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upLeftViewPackId");
        eiColumn.setDescName("跨层相邻左侧库位捆包号（上层库位属性）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upRightViewPackId");
        eiColumn.setDescName("跨层相邻右侧库位捆包号（上层库位属性）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("idleIntervalId");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("条状库位连续空闲区间编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("avaliableMinLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("条状单个库位最小可用长度(单位：厘米cm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("occupiedFlag");
        eiColumn.setDescName("已占用库位标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_pointStartOrign");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("X轴起始点初始值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_pointEndOrign");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("X轴结束点初始值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pointLowerLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("点状库位长度下限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pointUpperLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("点状库位长度上限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public LIDS0605() {
        initMetaData();
    }

    /**
     * get the segNo - 系统账套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the wproviderId - 仓库代码
     *
     * @return the wproviderId
     */
    public String getWproviderId() {
        return this.wproviderId;
    }

    /**
     * set the wproviderId - 仓库代码
     */
    public void setWproviderId(String wproviderId) {
        this.wproviderId = wproviderId;
    }

    /**
     * get the locationId - 库位代码
     *
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locViewPackId - 库位捆包号(2位流水码)
     *
     * @return the locViewPackId
     */
    public String getLocViewPackId() {
        return this.locViewPackId;
    }

    /**
     * set the locViewPackId - 库位捆包号(2位流水码)
     */
    public void setLocViewPackId(String locViewPackId) {
        this.locViewPackId = locViewPackId;
    }

    /**
     * get the locColumnId - 库位(跨+列)
     *
     * @return the locColumnId
     */
    public String getLocColumnId() {
        return this.locColumnId;
    }

    /**
     * set the locColumnId - 库位(跨+列)
     */
    public void setLocColumnId(String locColumnId) {
        this.locColumnId = locColumnId;
    }

    /**
     * get the useStatus - 状态。 10：空闲、20：预占、30：占用、99：禁用
     *
     * @return the useStatus
     */
    public String getUseStatus() {
        return this.useStatus;
    }

    /**
     * set the useStatus - 状态。 10：空闲、20：预占、30：占用、99：禁用
     */
    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    /**
     * get the locationType - 库位类型。(10点状 20条状)
     *
     * @return the locationType
     */
    public String getLocationType() {
        return this.locationType;
    }

    /**
     * set the locationType - 库位类型。(10点状 20条状)
     */
    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    /**
     * get the locationLength - 库位长度 单位(cm)
     *
     * @return the locationLength
     */
    public BigDecimal getLocationLength() {
        return this.locationLength;
    }

    /**
     * set the locationLength - 库位长度 单位(cm)
     */
    public void setLocationLength(BigDecimal locationLength) {
        this.locationLength = locationLength;
    }

    /**
     * get the specUpper - 宽度上限 单位(mm)
     *
     * @return the specUpper
     */
    public BigDecimal getSpecUpper() {
        return this.specUpper;
    }

    /**
     * set the specUpper - 宽度上限 单位(mm)
     */
    public void setSpecUpper(BigDecimal specUpper) {
        this.specUpper = specUpper;
    }

    /**
     * get the specLower - 宽度下限 单位(mm)
     *
     * @return the specLower
     */
    public BigDecimal getSpecLower() {
        return this.specLower;
    }

    /**
     * set the specLower - 宽度下限 单位(mm)
     */
    public void setSpecLower(BigDecimal specLower) {
        this.specLower = specLower;
    }

    /**
     * get the x_pointStart - X轴起始点
     *
     * @return the x_pointStart
     */
    public BigDecimal getX_pointStart() {
        return this.x_pointStart;
    }

    /**
     * set the x_pointStart - X轴起始点
     */
    public void setX_pointStart(BigDecimal x_pointStart) {
        this.x_pointStart = x_pointStart;
    }

    /**
     * get the x_pointEnd - X轴结束点
     *
     * @return the x_pointEnd
     */
    public BigDecimal getX_pointEnd() {
        return this.x_pointEnd;
    }

    /**
     * set the x_pointEnd - X轴结束点
     */
    public void setX_pointEnd(BigDecimal x_pointEnd) {
        this.x_pointEnd = x_pointEnd;
    }

    /**
     * get the y_pointCenter - Y轴
     *
     * @return the y_pointCenter
     */
    public BigDecimal getY_pointCenter() {
        return this.y_pointCenter;
    }

    /**
     * set the y_pointCenter - Y轴
     */
    public void setY_pointCenter(BigDecimal y_pointCenter) {
        this.y_pointCenter = y_pointCenter;
    }

    /**
     * get the leftViewPackId - 同层相邻左侧库位捆包号（下层库位属性）
     *
     * @return the leftViewPackId
     */
    public String getLeftViewPackId() {
        return this.leftViewPackId;
    }

    /**
     * set the leftViewPackId - 同层相邻左侧库位捆包号（下层库位属性）
     */
    public void setLeftViewPackId(String leftViewPackId) {
        this.leftViewPackId = leftViewPackId;
    }

    /**
     * get the rightViewPcakId - 同层相邻右侧库位捆包号（下层库位属性）
     *
     * @return the rightViewPcakId
     */
    public String getRightViewPcakId() {
        return this.rightViewPcakId;
    }

    /**
     * set the rightViewPcakId - 同层相邻右侧库位捆包号（下层库位属性）
     */
    public void setRightViewPcakId(String rightViewPcakId) {
        this.rightViewPcakId = rightViewPcakId;
    }

    /**
     * get the upDownFlag - 上下层标记。1：下层、2：上层
     *
     * @return the upDownFlag
     */
    public String getUpDownFlag() {
        return this.upDownFlag;
    }

    /**
     * set the upDownFlag - 上下层标记。1：下层、2：上层
     */
    public void setUpDownFlag(String upDownFlag) {
        this.upDownFlag = upDownFlag;
    }

    /**
     * get the standFlag - 是否立式库位。0：卧式、1：立式
     *
     * @return the standFlag
     */
    public String getStandFlag() {
        return this.standFlag;
    }

    /**
     * set the standFlag - 是否立式库位。0：卧式、1：立式
     */
    public void setStandFlag(String standFlag) {
        this.standFlag = standFlag;
    }

    /**
     * get the lrAccupyFlag - 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷
     *
     * @return the lrAccupyFlag
     */
    public String getLrAccupyFlag() {
        return this.lrAccupyFlag;
    }

    /**
     * set the lrAccupyFlag - 下层库位空闲且同层相邻左/右至少一侧有卷（下层库位属性）。1:至少一侧有卷；0:两侧无卷
     */
    public void setLrAccupyFlag(String lrAccupyFlag) {
        this.lrAccupyFlag = lrAccupyFlag;
    }

    /**
     * get the jgLockFlag - 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁
     *
     * @return the jgLockFlag
     */
    public String getJgLockFlag() {
        return this.jgLockFlag;
    }

    /**
     * set the jgLockFlag - 下层卷是否已下生产计划/工单（上层库位属性）。1:已计划封锁；0:未计划封锁
     */
    public void setJgLockFlag(String jgLockFlag) {
        this.jgLockFlag = jgLockFlag;
    }

    /**
     * get the upForbinFlag - 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷
     *
     * @return the upForbinFlag
     */
    public String getUpForbinFlag() {
        return this.upForbinFlag;
    }

    /**
     * set the upForbinFlag - 下层外板卷时不容许放卷（上层库位属性）。1:上层库位不容放卷；0:上层库位容许放卷
     */
    public void setUpForbinFlag(String upForbinFlag) {
        this.upForbinFlag = upForbinFlag;
    }

    /**
     * get the packId - 捆包号
     *
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the factoryProductId - 钢厂资源号
     *
     * @return the factoryProductId
     */
    public String getFactoryProductId() {
        return this.factoryProductId;
    }

    /**
     * set the factoryProductId - 钢厂资源号
     */
    public void setFactoryProductId(String factoryProductId) {
        this.factoryProductId = factoryProductId;
    }

    /**
     * get the productTypeId - 品种
     *
     * @return the productTypeId
     */
    public String getProductTypeId() {
        return this.productTypeId;
    }

    /**
     * set the productTypeId - 品种
     */
    public void setProductTypeId(String productTypeId) {
        this.productTypeId = productTypeId;
    }

    /**
     * get the shopsign - 牌号
     *
     * @return the shopsign
     */
    public String getShopsign() {
        return this.shopsign;
    }

    /**
     * set the shopsign - 牌号
     */
    public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
    }

    /**
     * get the spec - 规格
     *
     * @return the spec
     */
    public String getSpec() {
        return this.spec;
    }

    /**
     * set the spec - 规格
     */
    public void setSpec(String spec) {
        this.spec = spec;
    }

    /**
     * get the innerDiameter - 内径 单位:mm
     *
     * @return the innerDiameter
     */
    public BigDecimal getInnerDiameter() {
        return this.innerDiameter;
    }

    /**
     * set the innerDiameter - 内径 单位:mm
     */
    public void setInnerDiameter(BigDecimal innerDiameter) {
        this.innerDiameter = innerDiameter;
    }

    /**
     * get the outerDiameter - 外径 单位:mm
     *
     * @return the outerDiameter
     */
    public BigDecimal getOuterDiameter() {
        return this.outerDiameter;
    }

    /**
     * set the outerDiameter - 外径 单位:mm
     */
    public void setOuterDiameter(BigDecimal outerDiameter) {
        this.outerDiameter = outerDiameter;
    }

    /**
     * get the putinWeight - 重量
     *
     * @return the putinWeight
     */
    public BigDecimal getPutinWeight() {
        return this.putinWeight;
    }

    /**
     * set the putinWeight - 重量
     */
    public void setPutinWeight(BigDecimal putinWeight) {
        this.putinWeight = putinWeight;
    }

    /**
     * get the refWidth - 宽度 单位:mm
     *
     * @return the refWidth
     */
    public BigDecimal getRefWidth() {
        return this.refWidth;
    }

    /**
     * set the refWidth - 宽度 单位:mm
     */
    public void setRefWidth(BigDecimal refWidth) {
        this.refWidth = refWidth;
    }

    /**
     * get the customerId - 客户代码
     *
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the contractId - 销售合同
     *
     * @return the contractId
     */
    public String getContractId() {
        return this.contractId;
    }

    /**
     * set the contractId - 销售合同
     */
    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the locationName - 库位名称
     *
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the factoryArea - 厂区
     *
     * @return the factoryArea
     */
    public String getFactoryArea() {
        return this.factoryArea;
    }

    /**
     * set the factoryArea - 厂区
     */
    public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
    }

    /**
     * get the crossArea - 跨区
     *
     * @return the crossArea
     */
    public String getCrossArea() {
        return this.crossArea;
    }

    /**
     * set the crossArea - 跨区
     */
    public void setCrossArea(String crossArea) {
        this.crossArea = crossArea;
    }

    /**
     * get the surfaceGrade - 表面等级
     *
     * @return the surfaceGrade
     */
    public String getSurfaceGrade() {
        return this.surfaceGrade;
    }

    /**
     * set the surfaceGrade - 表面等级
     */
    public void setSurfaceGrade(String surfaceGrade) {
        this.surfaceGrade = surfaceGrade;
    }

    /**
     * get the c_no - 所在跨区的库位序号
     *
     * @return the c_no
     */
    public String getC_no() {
        return this.c_no;
    }

    /**
     * set the c_no - 所在跨区的库位序号
     */
    public void setC_no(String c_no) {
        this.c_no = c_no;
    }

    /**
     * get the upLeftViewPackId - 跨层相邻左侧库位捆包号（上层库位属性）
     *
     * @return the upLeftViewPackId
     */
    public String getUpLeftViewPackId() {
        return this.upLeftViewPackId;
    }

    /**
     * set the upLeftViewPackId - 跨层相邻左侧库位捆包号（上层库位属性）
     */
    public void setUpLeftViewPackId(String upLeftViewPackId) {
        this.upLeftViewPackId = upLeftViewPackId;
    }

    /**
     * get the upRightViewPackId - 跨层相邻右侧库位捆包号（上层库位属性）
     *
     * @return the upRightViewPackId
     */
    public String getUpRightViewPackId() {
        return this.upRightViewPackId;
    }

    /**
     * set the upRightViewPackId - 跨层相邻右侧库位捆包号（上层库位属性）
     */
    public void setUpRightViewPackId(String upRightViewPackId) {
        this.upRightViewPackId = upRightViewPackId;
    }

    /**
     * get the idleIntervalId - 条状库位连续空闲区间编号
     *
     * @return the idleIntervalId
     */
    public BigDecimal getIdleIntervalId() {
        return this.idleIntervalId;
    }

    /**
     * set the idleIntervalId - 条状库位连续空闲区间编号
     */
    public void setIdleIntervalId(BigDecimal idleIntervalId) {
        this.idleIntervalId = idleIntervalId;
    }

    /**
     * get the avaliableMinLength - 条状单个库位最小可用长度(单位：厘米cm)
     *
     * @return the avaliableMinLength
     */
    public BigDecimal getAvaliableMinLength() {
        return this.avaliableMinLength;
    }

    /**
     * set the avaliableMinLength - 条状单个库位最小可用长度(单位：厘米cm)
     */
    public void setAvaliableMinLength(BigDecimal avaliableMinLength) {
        this.avaliableMinLength = avaliableMinLength;
    }

    /**
     * get the occupiedFlag - 已占用库位标记
     *
     * @return the occupiedFlag
     */
    public String getOccupiedFlag() {
        return this.occupiedFlag;
    }

    /**
     * set the occupiedFlag - 已占用库位标记
     */
    public void setOccupiedFlag(String occupiedFlag) {
        this.occupiedFlag = occupiedFlag;
    }

    /**
     * get the x_pointStartOrign - X轴起始点初始值
     *
     * @return the x_pointStartOrign
     */
    public BigDecimal getX_pointStartOrign() {
        return this.x_pointStartOrign;
    }

    /**
     * set the x_pointStartOrign - X轴起始点初始值
     */
    public void setX_pointStartOrign(BigDecimal x_pointStartOrign) {
        this.x_pointStartOrign = x_pointStartOrign;
    }

    /**
     * get the x_pointEndOrign - X轴结束点初始值
     *
     * @return the x_pointEndOrign
     */
    public BigDecimal getX_pointEndOrign() {
        return this.x_pointEndOrign;
    }

    /**
     * set the x_pointEndOrign - X轴结束点初始值
     */
    public void setX_pointEndOrign(BigDecimal x_pointEndOrign) {
        this.x_pointEndOrign = x_pointEndOrign;
    }

    /**
     * get the pointLowerLength - 点状库位长度下限
     *
     * @return the pointLowerLength
     */
    public BigDecimal getPointLowerLength() {
        return this.pointLowerLength;
    }

    /**
     * set the pointLowerLength - 点状库位长度下限
     */
    public void setPointLowerLength(BigDecimal pointLowerLength) {
        this.pointLowerLength = pointLowerLength;
    }

    /**
     * get the pointUpperLength - 点状库位长度上限
     *
     * @return the pointUpperLength
     */
    public BigDecimal getPointUpperLength() {
        return this.pointUpperLength;
    }

    /**
     * set the pointUpperLength - 点状库位长度上限
     */
    public void setPointUpperLength(BigDecimal pointUpperLength) {
        this.pointUpperLength = pointUpperLength;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the tenantUser - 租户
     *
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the uuid - ID
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the factoryBuilding - 厂房代码
     *
     * @return the factoryBuilding
     */
    public String getFactoryBuilding() {
        return this.factoryBuilding;
    }

    /**
     * set the factoryBuilding - 厂房代码
     */
    public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
    }

    /**
     * get the factoryBuildingName - 厂房名称
     *
     * @return the factoryBuildingName
     */
    public String getFactoryBuildingName() {
        return this.factoryBuildingName;
    }

    /**
     * set the factoryBuildingName - 厂房名称
     */
    public void setFactoryBuildingName(String factoryBuildingName) {
        this.factoryBuildingName = factoryBuildingName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setWproviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wproviderId")), wproviderId));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocViewPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locViewPackId")), locViewPackId));
        setLocColumnId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locColumnId")), locColumnId));
        setUseStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("useStatus")), useStatus));
        setLocationType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationType")), locationType));
        setLocationLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("locationLength")), locationLength));
        setSpecUpper(NumberUtils.toBigDecimal(StringUtils.toString(map.get("specUpper")), specUpper));
        setSpecLower(NumberUtils.toBigDecimal(StringUtils.toString(map.get("specLower")), specLower));
        setX_pointStart(NumberUtils.toBigDecimal(StringUtils.toString(map.get("x_pointStart")), x_pointStart));
        setX_pointEnd(NumberUtils.toBigDecimal(StringUtils.toString(map.get("x_pointEnd")), x_pointEnd));
        setY_pointCenter(NumberUtils.toBigDecimal(StringUtils.toString(map.get("y_pointCenter")), y_pointCenter));
        setLeftViewPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leftViewPackId")), leftViewPackId));
        setRightViewPcakId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("rightViewPcakId")), rightViewPcakId));
        setUpDownFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("upDownFlag")), upDownFlag));
        setStandFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("standFlag")), standFlag));
        setLrAccupyFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lrAccupyFlag")), lrAccupyFlag));
        setJgLockFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jgLockFlag")), jgLockFlag));
        setUpForbinFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("upForbinFlag")), upForbinFlag));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setFactoryProductId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryProductId")), factoryProductId));
        setProductTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productTypeId")), productTypeId));
        setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
        setSpec(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spec")), spec));
        setInnerDiameter(NumberUtils.toBigDecimal(StringUtils.toString(map.get("innerDiameter")), innerDiameter));
        setOuterDiameter(NumberUtils.toBigDecimal(StringUtils.toString(map.get("outerDiameter")), outerDiameter));
        setPutinWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("putinWeight")), putinWeight));
        setRefWidth(NumberUtils.toBigDecimal(StringUtils.toString(map.get("refWidth")), refWidth));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setContractId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("contractId")), contractId));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
        setCrossArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossArea")), crossArea));
        setSurfaceGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("surfaceGrade")), surfaceGrade));
        setC_no(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("c_no")), c_no));
        setUpLeftViewPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("upLeftViewPackId")), upLeftViewPackId));
        setUpRightViewPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("upRightViewPackId")), upRightViewPackId));
        setIdleIntervalId(NumberUtils.toBigDecimal(StringUtils.toString(map.get("idleIntervalId")), idleIntervalId));
        setAvaliableMinLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("avaliableMinLength")), avaliableMinLength));
        setOccupiedFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("occupiedFlag")), occupiedFlag));
        setX_pointStartOrign(NumberUtils.toBigDecimal(StringUtils.toString(map.get("x_pointStartOrign")), x_pointStartOrign));
        setX_pointEndOrign(NumberUtils.toBigDecimal(StringUtils.toString(map.get("x_pointEndOrign")), x_pointEndOrign));
        setPointLowerLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("pointLowerLength")), pointLowerLength));
        setPointUpperLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("pointUpperLength")), pointUpperLength));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
        setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("wproviderId", StringUtils.toString(wproviderId, eiMetadata.getMeta("wproviderId")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locViewPackId", StringUtils.toString(locViewPackId, eiMetadata.getMeta("locViewPackId")));
        map.put("locColumnId", StringUtils.toString(locColumnId, eiMetadata.getMeta("locColumnId")));
        map.put("useStatus", StringUtils.toString(useStatus, eiMetadata.getMeta("useStatus")));
        map.put("locationType", StringUtils.toString(locationType, eiMetadata.getMeta("locationType")));
        map.put("locationLength", StringUtils.toString(locationLength, eiMetadata.getMeta("locationLength")));
        map.put("specUpper", StringUtils.toString(specUpper, eiMetadata.getMeta("specUpper")));
        map.put("specLower", StringUtils.toString(specLower, eiMetadata.getMeta("specLower")));
        map.put("x_pointStart", StringUtils.toString(x_pointStart, eiMetadata.getMeta("x_pointStart")));
        map.put("x_pointEnd", StringUtils.toString(x_pointEnd, eiMetadata.getMeta("x_pointEnd")));
        map.put("y_pointCenter", StringUtils.toString(y_pointCenter, eiMetadata.getMeta("y_pointCenter")));
        map.put("leftViewPackId", StringUtils.toString(leftViewPackId, eiMetadata.getMeta("leftViewPackId")));
        map.put("rightViewPcakId", StringUtils.toString(rightViewPcakId, eiMetadata.getMeta("rightViewPcakId")));
        map.put("upDownFlag", StringUtils.toString(upDownFlag, eiMetadata.getMeta("upDownFlag")));
        map.put("standFlag", StringUtils.toString(standFlag, eiMetadata.getMeta("standFlag")));
        map.put("lrAccupyFlag", StringUtils.toString(lrAccupyFlag, eiMetadata.getMeta("lrAccupyFlag")));
        map.put("jgLockFlag", StringUtils.toString(jgLockFlag, eiMetadata.getMeta("jgLockFlag")));
        map.put("upForbinFlag", StringUtils.toString(upForbinFlag, eiMetadata.getMeta("upForbinFlag")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("factoryProductId", StringUtils.toString(factoryProductId, eiMetadata.getMeta("factoryProductId")));
        map.put("productTypeId", StringUtils.toString(productTypeId, eiMetadata.getMeta("productTypeId")));
        map.put("shopsign", StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
        map.put("spec", StringUtils.toString(spec, eiMetadata.getMeta("spec")));
        map.put("innerDiameter", StringUtils.toString(innerDiameter, eiMetadata.getMeta("innerDiameter")));
        map.put("outerDiameter", StringUtils.toString(outerDiameter, eiMetadata.getMeta("outerDiameter")));
        map.put("putinWeight", StringUtils.toString(putinWeight, eiMetadata.getMeta("putinWeight")));
        map.put("refWidth", StringUtils.toString(refWidth, eiMetadata.getMeta("refWidth")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("contractId", StringUtils.toString(contractId, eiMetadata.getMeta("contractId")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("factoryArea", StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
        map.put("crossArea", StringUtils.toString(crossArea, eiMetadata.getMeta("crossArea")));
        map.put("surfaceGrade", StringUtils.toString(surfaceGrade, eiMetadata.getMeta("surfaceGrade")));
        map.put("c_no", StringUtils.toString(c_no, eiMetadata.getMeta("c_no")));
        map.put("upLeftViewPackId", StringUtils.toString(upLeftViewPackId, eiMetadata.getMeta("upLeftViewPackId")));
        map.put("upRightViewPackId", StringUtils.toString(upRightViewPackId, eiMetadata.getMeta("upRightViewPackId")));
        map.put("idleIntervalId", StringUtils.toString(idleIntervalId, eiMetadata.getMeta("idleIntervalId")));
        map.put("avaliableMinLength", StringUtils.toString(avaliableMinLength, eiMetadata.getMeta("avaliableMinLength")));
        map.put("occupiedFlag", StringUtils.toString(occupiedFlag, eiMetadata.getMeta("occupiedFlag")));
        map.put("x_pointStartOrign", StringUtils.toString(x_pointStartOrign, eiMetadata.getMeta("x_pointStartOrign")));
        map.put("x_pointEndOrign", StringUtils.toString(x_pointEndOrign, eiMetadata.getMeta("x_pointEndOrign")));
        map.put("pointLowerLength", StringUtils.toString(pointLowerLength, eiMetadata.getMeta("pointLowerLength")));
        map.put("pointUpperLength", StringUtils.toString(pointUpperLength, eiMetadata.getMeta("pointUpperLength")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("factoryBuilding", StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
        map.put("factoryBuildingName", StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));

        return map;

    }
}