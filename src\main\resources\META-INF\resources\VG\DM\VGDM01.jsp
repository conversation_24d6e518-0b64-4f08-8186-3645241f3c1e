<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="设备档案清单" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu-0-eArchivesNo" type="hidden"/>
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
<%--                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFInput ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件"
                                colWidth="3"/>--%>

                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false"
                                     containerId="equipmentInfo" center="true"
                                     ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>
<%--                    <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false"
                                     containerId="deviceInfo" center="true" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>--%>
                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-makerName" cname="制造商" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFInput ename="inqu_status-0-fixedAssetNumber" cname="固定资产代码" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFSelect ename="inqu_status-0-equipmentType" cname="设备类型" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P015"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="inqu_status-0-equipmentStatus" cname="状态" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P013"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-factoryBuilding" readonly="true" cname="厂房代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false"
                                     containerId="factoryAreaInfo" center="true"
                                     ename="inqu_status-0-factoryBuildingName" cname="厂房名称" colWidth="3"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="设备清单">
                <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="100" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFComboColumn ename="equipmentStatus" cname="状态" width="70" align="center">
                        <EF:EFCodeOption codeName="P013"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="equipmentType" cname="设备类型" width="70" align="center">
                        <EF:EFCodeOption codeName="P015"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="processCategory" cname="工序大类代码" width="100" align="center"/>
                    <EF:EFColumn ename="processCategoryName" cname="工序大类名称" width="100" align="center"/>
                    <EF:EFColumn ename="processCategorySub" cname="工序小类代码" width="100" align="center"/>
                    <EF:EFColumn ename="processCategorySubName" cname="工序小类名称" width="100" align="center"/>
                    <EF:EFColumn ename="designProductionCapacityWei" cname="设计产能(万吨/年)" width="130"
                                 align="right"/>
                    <EF:EFColumn ename="designProductionCapacityNum" cname="设计产能(万片/年)" width="130"
                                 align="right"/>
                    <%-- <EF:EFColumn ename="equipmentProducingArea" cname="设备产地"/> --%>
                    <EF:EFColumn ename="makerName" cname="制造商名称"/>
                    <EF:EFColumn ename="equipmentCommissioningDate" cname="设备投产日期"/>
                    <EF:EFColumn ename="fixedAssetNumber" cname="固定资产编号" width="120" align="center"/>
                    <EF:EFColumn ename="factoryBuilding" cname="厂房代码" width="120" align="center"/>
                    <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" width="120" align="center"/>
                    <%-- <EF:EFComboColumn ename="collectFlag" cname="采集标记" width="70" align="center">
                        <EF:EFCodeOption codeName="mes.vg.collectFlag"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="archiveAlterDesc" cname="设备档案变更说明" width="200"/> --%>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="分部设备清单" id="info-2">
            <EF:EFRegion id="result2" title="分部设备清单">
                <EF:EFGrid blockId="result2" autoDraw="no" readonly="true" checkMode="single, cell"
                           queryMethod="queryDeviceByEquipment" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="100" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="deviceId" cname="分部设备序号" width="110" align="center"/>
                    <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center"/>
                    <EF:EFColumn ename="deviceName" cname="分部设备名称"/>
                    <EF:EFComboColumn ename="deviceStatus" cname="状态" width="70" align="center">
                        <EF:EFCodeOption codeName="P013"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="equipmentType" cname="设备类型" width="70" align="center">
                        <EF:EFCodeOption codeName="P015"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="makerName" cname="制造商名称"/>
                    <EF:EFColumn ename="useDate" cname="使用日期"/>
                    <EF:EFColumn ename="drawingNum" cname="图号"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFRegion id="detail" title="检修配置">
                <EF:EFInput ename="detail-0-uuid" cname="UUID" type="hidden"/>
                <EF:EFInput ename="detail-0-deviceCode" cname="分部设备代码" type="hidden"/>
                <div class="row">
                    <EF:EFSelect required="true" ename="detail-0-overhaulCycleType" cname="检修周期"
                                 colWidth="3">
                        <EF:EFOption value="10" label="按天数"/>
                        <EF:EFOption value="20" label="按加工量"/>
                    </EF:EFSelect>
                    <div id="dayDiv">
                        <EF:EFInput ename="detail-0-cycleDay" required="true" cname="天数" validateGroupName="group1"
                                    colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                    </div>
                    <div id="numDiv">
                        <EF:EFInput ename="detail-0-cycleNum" required="true" cname="加工量" validateGroupName="group2"
                                    colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                    </div>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail-0-overhaulTool" cname="工器具及备件"
                                colWidth="12"
                                ratio="1:11" type="textarea"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail-0-overhaulStep" cname="检修详细步骤"
                                colWidth="12" rows="10"
                                ratio="1:11" type="textarea"/>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail-0-securityMeasures" cname="安全注意事项" colWidth="12"
                                ratio="1:11" type="textarea" rows="5"/>
                </div>
            </EF:EFRegion>
        </div>
        <div title="设备文档管理" id="info-3">
            <div style="display: none;">
                <EF:EFInput ename="fileForm" type="file"/>
            </div>
            <EF:EFRegion id="inqu2" title="查询条件">
                <div class="row">
                    <EF:EFSelect required="true" ename="inqu2_status-0-fifleType" cname="文件类型" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                    >
                        <EF:EFCodeOption codeName="P014"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="inqu2_status-0-eArchivesNo" type="hidden"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result3" title="设备文档">
                <EF:EFGrid blockId="result3" autoDraw="no" readonly="true"
                           queryMethod="queryFileByEquipment" sort="all">
                    <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="uploadFileName" cname="文档名称"/>
                    <EF:EFColumn ename="fifleSize" cname="大小" align="right"/>
                    <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                    <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                    <EF:EFColumn ename="fileVersion" cname="版本" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFWindow id="popData" width="90%" height="60%">
                <EF:EFInput ename="inqu1_status-0-eArchivesNo" type="hidden"/>
                <EF:EFInput ename="inqu1_status-0-uploadFileName" type="hidden"/>
                <EF:EFInput ename="inqu1_status-0-fifleType" type="hidden"/>
                <EF:EFRegion id="result4" title="文档历史版本">
                    <EF:EFGrid blockId="result4" autoDraw="no" readonly="true"
                               queryMethod="queryFileHistory" deleteMethod="deleteFile" sort="all">
                        <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                        <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                        <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                        <EF:EFColumn ename="uploadFileName" cname="文档名称"/>
                        <EF:EFColumn ename="fifleSize" cname="大小" align="right"/>
                        <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                        <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
                        <EF:EFColumn ename="fileVersion" cname="版本" align="center"/>
                        <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                        <EF:EFColumn ename="recCreateTime" editType="datetime"
                                     parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                     cname="上传时间"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </EF:EFWindow>
        </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <%--厂区厂房信息弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factoryAreaInfo" width="90%" height="60%"/>
</EF:EFPage>