/**
 * Generate time : 2024-09-13 13:12:24
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import com.baosight.iplat4j.core.util.NumberUtils;

/**
 * Tvgdm0301
 */
public class Tvgdm0301 extends DaoEPBase {

    private String tagId = " ";        /* 点位ID*/
    private String tagType = " ";        /* 点位类型*/
    private String tagDesc = " ";        /* 点位描述*/
    private String dataType = " ";        /* 数据类型*/
    private String driverType = " ";        /* 驱动类型*/
    private String deviceId = " ";        /* 设备ID*/
    private String deviceAddress = " ";        /* 设备地址*/
    private String deviceCode = " ";        /* 分部设备代码*/
    private String deviceName = " ";        /* 分部设备名称*/
    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String alarmClass = " ";        /* 报警分类*/
    private String scadaName = " ";        /* 节点名*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private Integer tagIhdId = 0;        /* 点位数据库id*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("tagId");
        eiColumn.setDescName("点位ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagType");
        eiColumn.setDescName("点位类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagDesc");
        eiColumn.setDescName("点位描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataType");
        eiColumn.setDescName("数据类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverType");
        eiColumn.setDescName("驱动类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceId");
        eiColumn.setDescName("设备ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceAddress");
        eiColumn.setDescName("设备地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmClass");
        eiColumn.setDescName("报警分类");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scadaName");
        eiColumn.setDescName("节点名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagIhdId");
        eiColumn.setType("N");
        eiColumn.setDescName("点位数据库id");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm0301() {
        initMetaData();
    }

    /**
     * get the tagId - 点位ID
     *
     * @return the tagId
     */
    public String getTagId() {
        return this.tagId;
    }

    /**
     * set the tagId - 点位ID
     */
    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    /**
     * get the tagType - 点位类型
     *
     * @return the tagType
     */
    public String getTagType() {
        return this.tagType;
    }

    /**
     * set the tagType - 点位类型
     */
    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    /**
     * get the tagDesc - 点位描述
     *
     * @return the tagDesc
     */
    public String getTagDesc() {
        return this.tagDesc;
    }

    /**
     * set the tagDesc - 点位描述
     */
    public void setTagDesc(String tagDesc) {
        this.tagDesc = tagDesc;
    }

    /**
     * get the dataType - 数据类型
     *
     * @return the dataType
     */
    public String getDataType() {
        return this.dataType;
    }

    /**
     * set the dataType - 数据类型
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * get the driverType - 驱动类型
     *
     * @return the driverType
     */
    public String getDriverType() {
        return this.driverType;
    }

    /**
     * set the driverType - 驱动类型
     */
    public void setDriverType(String driverType) {
        this.driverType = driverType;
    }

    /**
     * get the deviceId - 设备ID
     *
     * @return the deviceId
     */
    public String getDeviceId() {
        return this.deviceId;
    }

    /**
     * set the deviceId - 设备ID
     */
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    /**
     * get the deviceAddress - 设备地址
     *
     * @return the deviceAddress
     */
    public String getDeviceAddress() {
        return this.deviceAddress;
    }

    /**
     * set the deviceAddress - 设备地址
     */
    public void setDeviceAddress(String deviceAddress) {
        this.deviceAddress = deviceAddress;
    }

    /**
     * get the deviceCode - 分部设备代码
     *
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     *
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the alarmClass - 报警分类
     *
     * @return the alarmClass
     */
    public String getAlarmClass() {
        return this.alarmClass;
    }

    /**
     * set the alarmClass - 报警分类
     */
    public void setAlarmClass(String alarmClass) {
        this.alarmClass = alarmClass;
    }

    /**
     * get the scadaName - 节点名
     *
     * @return the scadaName
     */
    public String getScadaName() {
        return this.scadaName;
    }

    /**
     * set the scadaName - 节点名
     */
    public void setScadaName(String scadaName) {
        this.scadaName = scadaName;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the tagIhdId - 点位数据库id
     *
     * @return the tagIhdId
     */
    public Integer getTagIhdId() {
        return this.tagIhdId;
    }

    /**
     * set the tagIhdId - 点位数据库id
     */
    public void setTagIhdId(Integer tagIhdId) {
        this.tagIhdId = tagIhdId;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setTagId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagId")), tagId));
        setTagType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagType")), tagType));
        setTagDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagDesc")), tagDesc));
        setDataType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataType")), dataType));
        setDriverType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverType")), driverType));
        setDeviceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceId")), deviceId));
        setDeviceAddress(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceAddress")), deviceAddress));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setAlarmClass(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmClass")), alarmClass));
        setScadaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scadaName")), scadaName));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setTagIhdId(NumberUtils.toInteger(StringUtils.toString(map.get("tagIhdId")), tagIhdId));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("tagId", StringUtils.toString(tagId, eiMetadata.getMeta("tagId")));
        map.put("tagType", StringUtils.toString(tagType, eiMetadata.getMeta("tagType")));
        map.put("tagDesc", StringUtils.toString(tagDesc, eiMetadata.getMeta("tagDesc")));
        map.put("dataType", StringUtils.toString(dataType, eiMetadata.getMeta("dataType")));
        map.put("driverType", StringUtils.toString(driverType, eiMetadata.getMeta("driverType")));
        map.put("deviceId", StringUtils.toString(deviceId, eiMetadata.getMeta("deviceId")));
        map.put("deviceAddress", StringUtils.toString(deviceAddress, eiMetadata.getMeta("deviceAddress")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("alarmClass", StringUtils.toString(alarmClass, eiMetadata.getMeta("alarmClass")));
        map.put("scadaName", StringUtils.toString(scadaName, eiMetadata.getMeta("scadaName")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("tagIhdId", StringUtils.toString(tagIhdId, eiMetadata.getMeta("tagIhdId")));

        return map;

    }
}