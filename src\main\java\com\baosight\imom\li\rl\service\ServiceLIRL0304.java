package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baosight.imom.common.constants.MesConstant.Iplat.RESULT_BLOCK;


/**
 * @Author: 韩亚宁
 * @Description: ${厂内装卸点管理}
 * @Date: 2024/8/26 09:10
 * @Version: 1.0
 */
public class ServiceLIRL0304 extends ServiceBase {

    public static final String STATUS = "status"; //状态
    public static final String DEL_FLAG = "delFlag"; //删除标记
    public static final String REMARK = "remark"; //备注
    public static final String UUID = "uuid"; //uuid
    public static final String ZERO = "0"; //uuid
    public static final String ONE = "1"; //uuid
    public static final String INFOF1_STATUS = "infof1_status"; //uuid
    public static final String HAND_POINT_ID = "handPointId"; //uuid
    public static final String LOAD_FLAG = "loadFlag"; //uuid
    public static final String UNLOAD_FLAG = "unloadFlag"; //uuid
    public static final String EXPECTED_RECOVERY_TIME = "expectedRecoveryTime"; //uuid
    public static final String SEQ_NUM = "seqNum"; //uuid
    public static final String SYS_REMARK = "sysRemark"; //uuid
    public static final String CAR_TRACE_NO = "carTraceNo"; //uuid
    public static final String ORDER_NUMBER = "orderNumber"; //uuid
    public static final String VEHICLE_NUMER = "vehicleNumer"; //uuid
    public static final String STANDARD_CAPACITY = "standardCapacity"; //uuid

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock("result1").addBlockMeta(new LIRL0309().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0304.QUERY);
        return outInfo;
    }



    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(INFOF1_STATUS).getRows();
            for (HashMap hashMap : listHashMap) {
                String strSeqTypeId = "TMELI0304_SEQ01";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                String orderNumber = MapUtils.getString(hashMap, ORDER_NUMBER, "");
                String vehicleNumer = MapUtils.getString(hashMap, VEHICLE_NUMER, "");
                String standardCapacity = MapUtils.getString(hashMap, STANDARD_CAPACITY, "");
                String loadFlag = MapUtils.getString(hashMap, LOAD_FLAG, "");
                String unloadFlag = MapUtils.getString(hashMap, UNLOAD_FLAG, "");
                String xInitialPoint = MapUtils.getString(hashMap, "xInitialPoint", "");
                String xDestination = MapUtils.getString(hashMap, "xDestination", "");
                String yInitialPoint = MapUtils.getString(hashMap, "yInitialPoint", "");
                String yDestination = MapUtils.getString(hashMap, "yDestination", "");
                String[] args = {segNo, "ZXD", "", ""};
                //装卸点
                String handPointId = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                String uuid  = UUIDHexIdGenerator.generate().toString();

                hashMap.put(ORDER_NUMBER, orderNumber);//顺序号
                hashMap.put(VEHICLE_NUMER, vehicleNumer);//车辆容纳数

                if (StringUtils.isBlank(loadFlag)&&StringUtils.isBlank(unloadFlag)){
                    String massage = "装货或者卸货标记不能为空!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                if (StringUtils.isBlank(orderNumber)){
                    hashMap.put(ORDER_NUMBER, "0");//顺序号
                }

                if (StringUtils.isBlank(vehicleNumer)){
                    hashMap.put(VEHICLE_NUMER, "0");//车辆容纳数
                }
                if (StringUtils.isBlank(standardCapacity)){
                    hashMap.put(STANDARD_CAPACITY, "0");//车辆容纳数
                }else {
                    hashMap.put(STANDARD_CAPACITY, standardCapacity);//车辆容纳数
                }

                if (StringUtils.isBlank(loadFlag)){
                    hashMap.put(LOAD_FLAG, " ");//装货标记
                }else {
                    hashMap.put(LOAD_FLAG, loadFlag);//装货标记
                }

                if (StringUtils.isBlank(unloadFlag)){
                    hashMap.put(UNLOAD_FLAG, " ");//卸货标记
                }else {
                    hashMap.put(UNLOAD_FLAG, unloadFlag);//卸货标记
                }

                if (StringUtils.isBlank(xInitialPoint)){
                    hashMap.put("xInitialPoint", 0);//车辆容纳数
                }else {
                    hashMap.put("xInitialPoint", xInitialPoint);//车辆容纳数
                }

                if (StringUtils.isBlank(xDestination)){
                    hashMap.put("xDestination", 0);//车辆容纳数
                }else {
                    hashMap.put("xDestination", xInitialPoint);//车辆容纳数
                }

                if (StringUtils.isBlank(yInitialPoint)){
                    hashMap.put("yInitialPoint", 0);//车辆容纳数
                }else {
                    hashMap.put("yInitialPoint", yInitialPoint);//车辆容纳数
                }

                if (StringUtils.isBlank(yDestination)){
                    hashMap.put("yDestination", 0);//车辆容纳数
                }else {
                    hashMap.put("yDestination", yDestination);//车辆容纳数
                }

                hashMap.put(CAR_TRACE_NO, "");//车辆跟踪号
                hashMap.put(STATUS, 10);//状态
                hashMap.put(DEL_FLAG, 0);//记录删除标记
                hashMap.put(REMARK, MapUtils.getString(hashMap, REMARK, "").trim());//备注
                hashMap.put(HAND_POINT_ID, handPointId);//装卸点
                hashMap.put(EXPECTED_RECOVERY_TIME, "");//预计恢复时间
                hashMap.put(SEQ_NUM, "");//预计恢复时间
                hashMap.put(UUID,uuid);//uuid
                hashMap.put(SYS_REMARK,"");//uuid
                //添加创建人、姓名、时间
                RecordUtils.setCreator(hashMap);
                this.dao.insert( LIRL0304.INSERT,hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(INFOF1_STATUS).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0304> query = dao.query(LIRL0304.QUERY, hashmap);
                for (LIRL0304 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded1(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String loadFlag = MapUtils.getString(hashmap, "loadFlag");
                String status = MapUtils.getString(hashmap, "status");//状态
                String unloadFlag = MapUtils.getString(hashmap, "unloadFlag");
                String xInitialPoint = MapUtils.getString(hashmap, "xInitialPoint", "");
                String xDestination = MapUtils.getString(hashmap, "xDestination", "");
                String yInitialPoint = MapUtils.getString(hashmap, "yInitialPoint", "");
                String yDestination = MapUtils.getString(hashmap, "yDestination", "");
                if (StringUtils.isBlank(loadFlag)&&StringUtils.isBlank(unloadFlag)){
                    String massage = "装货或者卸货标记不能为空!!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                if (StringUtils.isBlank(loadFlag)){
                    hashmap.put("loadFlag"," ");
                }

                if (StringUtils.isBlank(unloadFlag)){
                    hashmap.put("unloadFlag"," ");
                }

                if (StringUtils.isBlank(xInitialPoint)){
                    hashmap.put("xInitialPoint", 0);//车辆容纳数
                }else {
                    hashmap.put("xInitialPoint", xInitialPoint);//车辆容纳数
                }

                if (StringUtils.isBlank(xDestination)){
                    hashmap.put("xDestination", 0);//车辆容纳数
                }else {
                    hashmap.put("xDestination", xInitialPoint);//车辆容纳数
                }

                if (StringUtils.isBlank(yInitialPoint)){
                    hashmap.put("yInitialPoint", 0);//车辆容纳数
                }else {
                    hashmap.put("yInitialPoint", yInitialPoint);//车辆容纳数
                }

                if (StringUtils.isBlank(yDestination)){
                    hashmap.put("yDestination", 0);//车辆容纳数
                }else {
                    hashmap.put("yDestination", yDestination);//车辆容纳数
                }
                RecordUtils.setRevisor(hashmap);//修改创建人、姓名、时间
                if ("30".equals(status)){
                    this.dao.update(LIRL0304.UPDATE_ENABLE_DATA,hashmap);
                }else {
                    this.dao.update(LIRL0304.UPDATE,hashmap);
                }
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0304> query = dao.query(LIRL0304.QUERY, hashMap);
                for (LIRL0304 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K00);//撤销状态
                hashMap.put(DEL_FLAG, ONE);//记录删除标记
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0309.UPDATET_LIRL0309,hashMap);
                updateLirl0306(hashMap,"1");

                HashMap<String, String> hashMapLIRL0309 = new HashMap<>();
                hashMapLIRL0309.put("segNo", MapUtils.getString(hashMap,"segNo"));
                hashMapLIRL0309.put("handPointId", MapUtils.getString(hashMap,"handPointId"));
                hashMapLIRL0309.put("status","00");
                RecordUtils.setRevisor(hashMapLIRL0309);
                this.dao.update(LIRL0309.UPDATET_LIRL0309,hashMapLIRL0309);
                this.dao.update(LIRL0315.UPDATET_LIRL0315,hashMapLIRL0309);
            }
            inInfo = super.update(inInfo, LIRL0304.UPDATE);
            //插销关系表数据
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 审核.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0304> query = dao.query(LIRL0304.QUERY, hashMap);
                for (LIRL0304 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K20);//审核状态
                RecordUtils.setRevisor(hashMap);
                //先判断是否存在?存在更新,不存在插入
                Map<Object, Object> hashMapLIRL0306 = new HashMap<>();
                hashMapLIRL0306.put("segNo",MapUtils.getString(hashMap,"segNo"));
                hashMapLIRL0306.put("handPointId",MapUtils.getString(hashMap,"handPointId"));
                List<LIRL0306> queryLIRL0306 = this.dao.query(LIRL0306.QUERY, hashMapLIRL0306);
                if (queryLIRL0306.size()<=0&& CollectionUtils.isEmpty(queryLIRL0306)){
                    //同步插入装卸状态跟踪表数据
                    hashMap.put("uuid", UUIDUtils.getUUID());
                    hashMap.put("handPointId", MapUtils.getString(hashMap,"handPointId"));
                    hashMap.put("status", "20");//进车
                    hashMap.put("currentJobNumber", new BigDecimal(0));//
                    hashMap.put("preJobNumber", new BigDecimal(0));//
                    hashMap.put("delFlag", 0);//
                    this.dao.insert(LIRL0306.INSERT,hashMap);
                }else {
                    updateLirl0306(hashMap,"0");
                }

            }
            inInfo = super.update(inInfo, LIRL0304.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /***
     * 更新装点写跟踪表状态
     * @param hashMap
     */
    private void updateLirl0306(HashMap hashMap,String delFlag) {
        //同步更新装卸状态跟踪表数据
        hashMap.put("handPointId", MapUtils.getString(hashMap,"handPointId"));
        // hashMap.put("status", "30");//暂停进车
        hashMap.put("delFlag", delFlag);//删除
        this.dao.update(LIRL0306.UPDATE_LIRL0306, hashMap);
    }

    /**
     * 反审核.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0304> query = dao.query(LIRL0304.QUERY, hashMap);
                for (LIRL0304 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusConfirmNo(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K10);//新增状态
                RecordUtils.setRevisor(hashMap);
                updateLirl0306(hashMap,"1");
            }
            inInfo = super.update(inInfo, LIRL0304.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
    /**
     * 启用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo enable(EiInfo inInfo) {
        try {
                List<HashMap> listHashMap = inInfo.getBlock(RESULT_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0304> query = dao.query(LIRL0304.QUERY, hashMap);
                for (LIRL0304 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusEanbledDisabled(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K30);//启用状态
                RecordUtils.setRevisor(hashMap);
                //启用所有装卸点的业务类型,起始地配置
                HashMap<String, String> hashMapLIRL0309 = new HashMap<>();
                hashMapLIRL0309.put("segNo", MapUtils.getString(hashMap,"segNo"));
                hashMapLIRL0309.put("handPointId", MapUtils.getString(hashMap,"handPointId"));
                hashMapLIRL0309.put("status","30");
                RecordUtils.setRevisor(hashMapLIRL0309);
                this.dao.update(LIRL0309.UPDATET_LIRL0309,hashMapLIRL0309);
                this.dao.update(LIRL0315.UPDATET_LIRL0315,hashMapLIRL0309);
            }
            inInfo = super.update(inInfo, LIRL0304.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 停用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo disable(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT_BLOCK).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0304> query = dao.query(LIRL0304.QUERY, hashMap);
                for (LIRL0304 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereEnabledStatusDisabled(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K99);//停用状态
                RecordUtils.setRevisor(hashMap);
                // updateLirl0306(hashMap,"1");
                //判断该装卸点是否有未离场的情况
                HashMap LIRL0301Map = new HashMap<>();
                LIRL0301Map.putAll(hashMap);
                List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0304.QUERY_NO_FACTORY_HAND_POINT, LIRL0301Map);
                if (CollectionUtils.isNotEmpty(queryLIRL0301)&&queryLIRL0301.size()>0){
                    String massage = MessageCodeConstant.errorMessage.MSG_ERROR_HAND_POINT_NO_FACTORY;
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                HashMap<String, String> hashMapLIRL0309 = new HashMap<>();
                hashMapLIRL0309.put("segNo", MapUtils.getString(hashMap,"segNo"));
                hashMapLIRL0309.put("handPointId", MapUtils.getString(hashMap,"handPointId"));
                hashMapLIRL0309.put("status","99");
                RecordUtils.setRevisor(hashMapLIRL0309);
                this.dao.update(LIRL0309.UPDATET_LIRL0309,hashMapLIRL0309);
                this.dao.update(LIRL0315.UPDATET_LIRL0315,hashMapLIRL0309);
            }
            inInfo = super.update(inInfo, LIRL0304.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
    /**查询装卸点信息*/
    /**S_LI_RL_0157*/
    public EiInfo queryAllHandPointId(EiInfo inInfo) {
        try {
            String segNo = (String) inInfo.get("segNo");
            String businessType = (String) inInfo.get("businessType");
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("businessType", businessType);
            List<HashMap> someList = new ArrayList<>();
            List<HashMap> query = this.dao.query("LIRL0304.queryAllHandPointId", hashMap);
            if (query.size() > 0){
                for(HashMap hashMap1:query){
                    String scrapType = MapUtils.getString(hashMap1,"scrapType");
                    if(StringUtils.isNotBlank(scrapType)){
                        String[] split = scrapType.split(";");
                        for (String vehicleNoStr : split) {
                            HashMap vehicleNoMap = new HashMap();
                            vehicleNoMap.put("scrapType",vehicleNoStr.split(":")[0]);
                            vehicleNoMap.put("scrapTypeName",vehicleNoStr.split(":")[1]);
                            vehicleNoMap.put("segNo",hashMap1.get("segNo"));
                            vehicleNoMap.put("handPointId",hashMap1.get("handPointId"));
                            vehicleNoMap.put("handPointName",hashMap1.get("handPointName"));
                            someList.add(vehicleNoMap);
                        }
                    }
                }
                inInfo.set("someList",someList);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("查询成功！");
                return inInfo;
            }else {
                inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                inInfo.setMsg("未查询到数据！");
                return inInfo;
            }
        }catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(ex.getMessage());
            return inInfo;
        }
    }
}
