$(function () {
    // TODO 查询 按钮事件
    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();
    var tab_Info ="";
    //是否新增
    var if_add = false;
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //模板下载按钮
    $("#DOWNLOAD").on("click", function () {
        window.open(IPLATUI.CONTEXT_PATH + "/LI/RL/fileTemplate/司机车牌基础信息导入模板.xls", "newwindow");
    });
    //得到tab组件对象
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Info = $("#info").data("kendoTabStrip");
    });

    $("#QUERY").on("click", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        var unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({msg: "请先选择业务单元代码!"}, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });

    $("#SUB_QUERY").on("click", function (e) {
        sub_resultGrid.dataSource.page(1);
    });

    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                var tabId = e.contentElement.id;
                switch (tabId) {
                    case 'info-1':
                        //禁用保存按钮
                        $("#SAVE").attr("disabled", true);
                        //是否新增
                        if_add = false;
                        var eiInfo = new EiInfo();
                        //将查询资材表格设空
                        var block = buttonsetchildGrid.getEiBlock();
                        if (block != null) {
                            block.setRows([]);
                            eiInfo.addBlock(block);
                            buttonsetchildGrid.setEiInfo(eiInfo);
                        }
                        IPLAT.EFPopupInput.readonly($("#add_status-0-pageId"), true);
                        IPLAT.EFPopupInput.enable($("#add_status-0-pageId"), false);

                        // IPLAT.EFPopupInput.readonly($("#add_status-0-applyDept"), true);
                        // IPLAT.EFPopupInput.enable($("#add_status-0-applyDept"), false);
                        IPLAT.EFPopupInput.enable($("#add_status-0-unitCode"), false);
                        IPLAT.EFPopupInput.readonly($("#add_status-0-unitCode"), true);

                        IPLAT.EFInput.readonly($("#add_status-0-driverName"), true);
                        IPLAT.EFInput.readonly($("#add_status-0-tel"), true);
                        IPLAT.EFInput.readonly($("#add_status-0-driverIdentity"), true);
                        //resultGrid.dataSource.page(1);
                        return false;
                    case 'info-2':
                        if (if_add) {
                            return false;
                        } else {
                            var model = null;
                            if (resultGrid.getCheckedRows().length <= 0 && resultGrid.getSelectedRows().length <= 0) {
                                NotificationUtil("操作失败，原因[请勾选一条记录再进行查看！]", "error");
                                e.preventDefault();
                                return false;
                            } else if (resultGrid.getCheckedRows().length > 0) {
                                model = resultGrid.getCheckedRows()[0];
                            } else {
                                model = resultGrid.getSelectedRows()[0];
                            }

                            $("#add_status-0-uuid").val(model["pageId"]);
                            IPLAT.EFInput.readonly($("#add_status-0-uuid"), true);

                            IPLAT.EFPopupInput.value($("#add_status-0-unitCode"), model["unitCode"]);
                            IPLAT.EFInput.value($("#add_status-0-segNo"), model["segNo"]);
                            IPLAT.EFInput.value($("#add_status-0-segName"), model["segName"]);

                            IPLAT.EFSelect.value($("#add_status-0-status"), model["status"]);
                            IPLAT.EFSelect.readonly($("#add_status-0-status"), true);

                            IPLAT.EFInput.value($("#add_status-0-driverName"), model["driverName"]);
                            IPLAT.EFInput.value($("#add_status-0-tel"), model["tel"]);
                            IPLAT.EFInput.value($("#add_status-0-driverIdentity"), model["driverIdentity"]);

                            IPLAT.EFInput.readonly($("#add_status-0-driverName"), true);
                            IPLAT.EFInput.readonly($("#add_status-0-tel"), true);
                            IPLAT.EFInput.readonly($("#add_status-0-driverIdentity"), true);


                            IPLAT.EFPopupInput.value($("#add_status-0-customerId"), model["customerId"]);
                            IPLAT.EFInput.value($("#add_status-0-customerName"), model["customerName"]);

                            IPLAT.EFPopupInput.enable($("#add_status-0-customerId"), false);
                            IPLAT.EFPopupInput.readonly($("#add_status-0-customerId"), true);



                            // //填充子项
                            // var eiInfo = new EiInfo();
                            // //设置主项信息
                            // eiInfo.setByNode("add");
                            // eiInfo.addBlock(buttonsetchildGrid.getQueryInfo().getBlock("buttonsetchild"));
                            // IMCUtil.submitGridsData("buttonsetchild", "LIRL0102", "queryDriverChildShow", true);

                            var info = new EiInfo();
                            info.setByNode("result");
                            info.addBlock(resultGrid.getCheckedBlockData());
                            IPLAT.progress($("body"), true);
                            EiCommunicator.send("LIRL0102", "queryDriverChildShow", info, {
                                onSuccess: function (ei) {
                                    if ("-1" == ei.status) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                    } else {
                                        NotificationUtil({msg: ei.msg}, "sccess");
                                        // buttonsetchildGrid.dataSource.page(1);
                                        // buttonsetchildGrid.refresh();
                                        buttonsetchildGrid.setEiInfo(ei);

                                    }
                                    IPLAT.progress($("body"), false);
                                },
                                onFail: function (ei) {
                                    IPLAT.progress($("body"), false);
                                    NotificationUtil({msg: ei.msg}, "error");
                                    return false;
                                }
                            });
                            //使子项按钮不可用
                            $("[class='i-btn-lg  k-grid-INSERTMSAVE']").attr("style", "display:none;");  //隐藏
                            $("[class='i-btn-lg  k-grid-DELETESAVE']").attr("style", "display:none;");  //隐藏
                            $("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");  //隐藏
                            $("[class='i-btn-lg  k-grid-CANCELSAVE']").attr("style", "display:none;");  //隐藏
                            // $("#CANCELSON").attr("disabled", true);
                            return;
                        }
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings:{
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings:{
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            // columns: [
            //     {
            //         field: "customerId",
            //         enable: true,
            //         readonly: true,
            //         hidden: false,
            //         locked: false,
            //         title: "承运商/客户代码",
            //         editor: function (container, param) {
            //             // 设置产生弹框model
            //             if (container.hasClass("fake-edit")) {
            //                 container.removeClass("fake-edit");
            //             } else {
            //                 editorModel = param.model;
            //                 IPLAT.Popup.popupContainer({
            //                     containerId: "ADDSUBWINDOW",
            //                     textElement: $(container),
            //                     width: 600,
            //                     center: true,
            //                     title: "承运商/客户代码"
            //                 })
            //             }
            //         }
            //     }
            // ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                /**
                 * 大数据后端导出
                 */
                $("#EXPORTEXCEL").on("click",function () {
                    let segNo = $("#inqu_status-0-segNo").val();

                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil("请在查询条件区域内选择相应的[业务单元]！", "error");
                        return;
                    }

                    var fileName = segNo+"车辆司机信息维护" + ".xlsx";

                    if(resultGrid.getDataItems().length > 0) {
                        let exportEi = new EiInfo();
                        exportEi.setByNode("inqu");
                        IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                        exportEi.set("exportColumnBlock", 'fileName', fileName);
                        IMOMUtil.callService({
                            service: "LIRL0102",
                            method: "postExport",
                            eiInfo: exportEi,
                            showProgress: true,
                            async: true,
                            callback: function (ei) {
                                if (ei.status > -1) {
                                    let docUrl = ei.getBlock("excelDoc").get("docUrl");
                                    window.open(docUrl);
                                }
                            }
                        });
                    }
                });
                // 获取勾选数据，
                //点击新增按钮事件
                $("#INSERTSAVEM1").on("click", function (e) {
                    //将子项详情表格设空
                    var block = buttonsetchildGrid.getBlockData();
                    var eiInfo = new EiInfo();
                    if (block != null) {
                        block.setRows([]);
                        eiInfo.addBlock(block);
                        buttonsetchildGrid.setEiInfo(eiInfo);
                    }

                    if_add = true;
                    var tabStrip = $("#info").data("kendoTabStrip");
                    tabStrip.select(1);
                    //详情页解除保存按钮禁用
                    $("#SAVE").attr("disabled", false);
                    //使子项按钮可用

                    $("#add_status-0-type").val("insert");  //操作类型

                    $("#add_status-0-uuid").val("");
                    IPLAT.EFInput.readonly($("#add_status-0-uuid"), true);

                    IPLAT.EFPopupInput.enable($("#add_status-0-unitCode"), true);
                    IPLAT.EFPopupInput.readonly($("#add_status-0-unitCode"), true);
                    with (unitInfo) {
                        $("#add_status-0-unitCode").val(unitCode);
                        $("#add_status-0-segNo").val(segNo);
                        $("#add_status-0-segName").val(segName);
                    }
                    IPLAT.EFSelect.value($("#add_status-0-status"), "10");
                    IPLAT.EFSelect.readonly($("#add_status-0-status"), true);

                    IPLAT.EFPopupInput.value($("#add_status-0-customerId"), "");
                    IPLAT.EFPopupInput.value($("#add_status-0-customerName"), "");
                    IPLAT.EFPopupInput.readonly($("#add_status-0-customerId"), false);
                    IPLAT.EFPopupInput.enable($("#add_status-0-customerId"), true);

                    IPLAT.EFPopupInput.value($("#add_status-0-driverName"), "");
                    IPLAT.EFPopupInput.readonly($("#add_status-0-driverName"), false);
                    IPLAT.EFPopupInput.enable($("#add_status-0-driverName"), true);

                    IPLAT.EFPopupInput.value($("#add_status-0-tel"), "");
                    IPLAT.EFPopupInput.readonly($("#add_status-0-tel"), false);
                    IPLAT.EFPopupInput.enable($("#add_status-0-tel"), true);

                    IPLAT.EFPopupInput.value($("#add_status-0-driverIdentity"), "");
                    IPLAT.EFPopupInput.readonly($("#add_status-0-driverIdentity"), false);
                    IPLAT.EFPopupInput.enable($("#add_status-0-driverIdentity"), true);
                    $("[class='i-btn-lg  k-grid-INSERTMSAVE']").attr("style", "display:block;");  //隐藏
                    $("[class='i-btn-lg  k-grid-DELETESAVE']").attr("style", "display:block;");  //隐藏
                    $("[class='i-btn-lg  k-grid-add']").attr("style", "display:block;");  //隐藏
                    $("[class='i-btn-lg  k-grid-CANCELSAVE']").attr("style", "display:block;");  //隐藏

                });
                // 获取勾选数据，
                //点击修改按钮事件
                $("#UPDATESAVE1").on("click", function (e) {
                    var checkedRowLength = resultGrid.getCheckedRows().length;
                    if (resultGrid.getCheckedRows().length <= 0 && resultGrid.getSelectedRows().length <= 0) {
                        NotificationUtil("操作失败，原因[请至少选中一条数据进行修改！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    if (checkedRowLength > 1) {
                        NotificationUtil("操作失败，原因[只能对一条数据进行修改！]", "error");
                        e.preventDefault();
                        return false;
                    }

                    var checkedRows = resultGrid.getCheckedRows();
                    var model = checkedRows[0];
                    if ("10" != model["status"]) {
                        IPLAT.alert({
                            message: '<b>当前主项状态不为新增，不可修改！</b>',
                            title: '提示信息'
                        });
                        return;
                    }
                    //跳转到详情页
                    tab_Info.select(1);
                    $("#add_status-0-type").val("update");  //操作类型
                    //详情页解除保存按钮禁用
                    $("#SAVE").attr("disabled", false);
                    //使字段可修改
                    IPLAT.EFPopupInput.enable($("#add_status-0-unitCode"), false);
                    IPLAT.EFPopupInput.readonly($("#add_status-0-unitCode"), true);
                    IPLAT.EFPopupInput.readonly($("#add_status-0-uuid"), true);
                    IPLAT.EFPopupInput.enable($("#add_status-0-uuid"), false);
                    IPLAT.EFPopupInput.readonly($("#add_status-0-customerId"), false);
                    IPLAT.EFPopupInput.enable($("#add_status-0-customerId"), true);
                    IPLAT.EFInput.readonly($("#add_status-0-driverName"), false);
                    IPLAT.EFInput.readonly($("#add_status-0-tel"), false);
                    IPLAT.EFInput.readonly($("#add_status-0-driverIdentity"), false);
                    //解除按钮禁用
                    $("#CANCELSON").attr("disabled", false);

                    $("[class='i-btn-lg  k-grid-INSERTMSAVE']").attr("style", "display:block;");  //隐藏
                    $("[class='i-btn-lg  k-grid-DELETESAVE']").attr("style", "display:block;");  //隐藏
                    $("[class='i-btn-lg  k-grid-add']").attr("style", "display:block;");  //隐藏
                    $("[class='i-btn-lg  k-grid-CANCELSAVE']").attr("style", "display:block;");  //隐藏
                });

                // 获取勾选数据，
                $("#DELETE1").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("删除失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0102", "delete", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                // 获取勾选数据，
                $("#CONFIRM").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("删除失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0102", "confirm", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });

                // 反确认
                $("#CONFIRMNO").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("删除失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0102", "confirmno", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });

                });
                //取消
                $("#CANCEL").on("click", function (e) {
                    // resultGrid.dataSource.page(1);
                    tab_Info.select(0);
                    resultGrid.dataSource.page(1);
                    if (resultGrid.getCheckedRows().length > 0) {
                        //这里搞个取消全选
                        resultGrid.unCheckAllRows();
                    }
                    //NotificationUtil({msg: "取消!"}, "info");
                });

                var validator=IPLAT.Validator({
                    id: "add"
                });
                /**
                 * 保存主子项信息
                 */
                $("#SAVE").on("click", function (e) {
                    IPLAT.confirm({
                        message: '<b>保存当前信息？</b>',
                        okFn: function () {
                            //全选子项表格数据
                            // if (buttonsetchildGrid.getDataItems().length > 0) {
                            //     buttonsetchildGrid.checkAllRows();
                            // }
                            //校验主项信息
                            var unitCode = $("#add_status-0-unitCode").val();//业务单元代码
                            var segNo = $("#add_status-0-segNo").val();//系统账套代码
                            var segName = $("#add_status-0-segName").val();//业务单元简称
                            var driverIdentity = $("#add_status-0-driverIdentity").val();//业务单元简称
                            var tel = $("#add_status-0-tel").val();//业务单元简称

                            if (!validator.validate()) {
                                IPLAT.NotificationUtil("主项数据填写有误，请检查修改后保存数据！", "error");
                                return;
                            }
                            validateIDCard(driverIdentity);
                            if (IPLAT.isBlankString(unitCode)) {
                                NotificationUtil("操作失败，原因[业务单元代码和系统账套为空！]", "error");
                                return;
                            }
                            if (IPLAT.isBlankString(segNo)) {
                                NotificationUtil("操作失败，原因[业务单元代码和系统账套为空！]", "error");
                                return;
                            }
                            if (IPLAT.isBlankString(segName)) {
                                NotificationUtil("操作失败，原因[业务单元简称为空！]", "error");
                                return;
                            }

                            /*得到表格数据*/
                            var dataR = buttonsetchildGrid.getDataItems();
                            /*数据是否为空*/
                            if (dataR.length <= 0) {
                                NotificationUtil("无子项信息不能保存", "error");
                                return;
                            }
                            var eiInfo = new EiInfo();
                            eiInfo.setByNode("add");
                            // eiInfo.set("applyNum", $("#uuid").val());
                            eiInfo.addBlock(buttonsetchildGrid.getCheckedBlockData());
                            /*能到这就算胜利*/
                            IPLAT.progress($("body"), true);
                            //新增主子项信息
                            EiCommunicator.send("LIRL0102", "insert", eiInfo, {
                                onSuccess: function (ei) {
                                    if (-1 == ei.getStatus()) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        IPLAT.progress($("body"), false);
                                    } else {
                                        IPLAT.progress($("body"), false);
                                        //输入框禁用
                                        IPLAT.EFPopupInput.enable($("#add_status-0-unitCode"), false);
                                        IPLAT.EFPopupInput.readonly($("#add_status-0-unitCode"), true);

                                        //按钮禁用
                                        $("#SAVE").attr("disabled", true);
                                        $("#INSERTM").attr("disabled", true);
                                        // $("#DELETECHILD").attr("disabled", true);
                                        $("#CANCELSAVE").attr("disabled", true);
                                        // IPLAT.alert({
                                        //     message: '<b>操作成功！</b>',
                                        //     title: '提示信息'
                                        // });

                                        resultGrid.dataSource.page(1);
                                        tab_Info.select(0);
                                        buttonsetchildGrid.setEiInfo(ei);
                                    }
                                }, onFail: function (ei) {
                                    IPLAT.alert(ei.getMsg());
                                }
                            });
                        },
                        //取消保存执行
                        cancelFn: function () {
                            if (buttonsetchildGrid.getCheckedRows().length > 0) {
                                //这里搞个取消全选
                                buttonsetchildGrid.unCheckAllRows();
                            }
                            NotificationUtil({msg: "取消!"}, "info");
                        }
                    })
                });


                IPLAT.FileUploader({
                    id: "fileForm",
                    ename: "fileForm",
                    serviceName: "LIRL0102",
                    methodName: "importExcel",
                    success: function (data) {
                        //转eiInfo
                        var e = Json2EiInfo.prototype.parseJsonObject(data.response);
                        if (e.getStatus() < 0) {
                            NotificationUtil(e.getMsg(),e.getDetailMsg(), "error");
                            NotificationUtil({msg:e.getMsg(),detailMsg:e.getDetailMsg()},"error");
                        } else {
                            if(e.getStatus() == 0){
                                NotificationUtil({msg:e.getMsg(),detailMsg:e.getDetailMsg()},"error");
                            }else{
                                NotificationUtil({msg:e.getMsg(),detailMsg:e.getDetailMsg()},"success");
                            }
                            insertImportWindow.close();
                            // IPLAT.EFInput.value( $("#inqu_status-0-isQueryVersion") , "N");
                            // resultGrid.dataSource.page(1);
                        }
                    },
                    onFail: function (data) {
                        NotificationUtil(e.getMsg(),e.getDetailMsg(),"error");
                    }
                });

                $("#IMPORT").on("click", function (e) {
                    insertImportWindow.open().center();
                });
            },
        },
        // "sub_result":{
        //     onRowDblClick: function (e) {
        //         /*$("#inqu_status-0-userNum").val(e.model.userNum)
        //         $("#inqu_status-0-userName").val(e.model.chineseUserName)*/
        //         var checkRows = resultGrid.getCheckedRows();
        //         for (i = 0; i < checkRows.length; i++) {
        //             var varModel = resultGrid.getCheckedRows()[i];
        //             varModel.customerId = e.model.userNum;
        //             varModel.customerName = e.model.chineseUserName;
        //         }
        //         resultGrid.refresh();
        //         //关闭弹出框
        //         ADDSUBWINDOWWindow.close();
        //     }
        // },
        "buttonsetchild":{
            loadComplete: function (grid) {


                // 新增开关定义
                $("#INSERTM").on("click", function (e) {
                    grid.addRow();
                });

                /**
                 * 取消对子项部分的操作
                 */

                $("#CANCELSAVE").on("click", function (e) {
                    grid.cancelChanges();
                });
                $("#DELETESAVE").on("click", function (e) {
                    if (buttonsetchildGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("删除失败，原因[至少勾选一条数据进行删除！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    $("#add_status-0-type").val("update");  //操作类型
                    var info = new EiInfo();
                    info.setByNode("buttonsetchild");
                    info.addBlock(buttonsetchildGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    //子项数据被勾选的条数
                    var checkedRowLength = buttonsetchildGrid.getCheckedRows().length;
                    var allRowsLength = buttonsetchildGrid.getDataItems().length;
                    var isDelAll = false;

                    //判断是否有行被选中
                    if (checkedRowLength <= 0) {
                        NotificationUtil({msg: "没有行被选中!"}, "error");
                        return;
                    } else {
                        //信息提示框
                        IPLAT.confirm({
                            message: '<b>确定删除该子项吗？</b>',
                            //信息确认框确认事件
                            okFn: function () {
                                if (checkedRowLength == allRowsLength) {
                                    isDelAll = true;
                                }
                                //加载动画
                                IPLAT.progress($("body"), true);
                                //删除子项信息
                                EiCommunicator.send("LIRL0102", "deleteSave", info, {
                                    //调用成功回调
                                    onSuccess: function (ei) {
                                        if ("-1" == ei.status) {
                                            NotificationUtil({msg: ei.msg}, "error");
                                            //关闭加载动画
                                            IPLAT.progress($("body"), false);
                                        } else {
                                            //将后端返回的子项信息设置入表格
                                            buttonsetchildGrid.removeRows(buttonsetchildGrid.getCheckedRows());
                                            /*关闭加载动画*/
                                            IPLAT.progress($("body"), false);
                                            if (isDelAll==true) {
                                                resultGrid.dataSource.page(1);
                                                tab_Info.select(0);
                                            }else {
                                                buttonsetchildGrid.refresh();
                                            }
                                            NotificationUtil({msg: ei.msg}, "success");
                                        }
                                    },
                                    /*调用失败回调*/
                                    onFail: function (ei) {
                                        NotificationUtil({msg: ei.msg}, "error");
                                        return false;
                                    }
                                });
                            },
                            /*信息确认框取消事件*/
                            cancelFn: function () {
                                NotificationUtil({msg: "取消!"}, "info");
                            }
                        });

                    }
                    // }

                });
            },
            beforeEdit: function (e) {
                e.model["uuid"]="";
            }
        },
    };
    IPLATUI.EFPopupInput = {
        "inqu_status-0-customerId": {
            clearInput: function (e) {
                $("#inqu_status-0-customerId").val('');
                $("#inqu_status-0-customerName").val('');
            }
        }
    };
    function validateIDCard(idCard) {
        var reg = /^[1 - 9]\d{5}(18|19|20)\d{2}(0[1 - 9]|1[0 - 2])(0[1 - 9]|[12]\d|3[01])\d{3}[0 - 9Xx]$/;
    }
    IPLATUI.EFWindow = {
        "unitInfo": {
            open: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu_status-0-windowId").val($("unitInfo"));
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();
                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                }
            }
        },
        "unitInfo1": {
            open: function (e) {
                var $iframe = unitInfo1Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu_status-0-windowId").val($("unitInfo"));
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo1Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();
                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                }
            }
        },
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                var segNo=$("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)){
                    NotificationUtil({msg:"请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId").val(row[0].userNum);
                    $("#inqu_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "userNum2": {
        // 打开窗口事件
        open: function (e) {
            var $iframe = userNum2Window.element.children("iframe");
            // 子窗口中的jQuery对象
            var iframejQuery = $iframe[0].contentWindow.$;
            // 把EFWindow的id传入到子窗口input框中
            iframejQuery("#sub_query_status-0-windowId").val("userNum2");
            var segNo=$("#inqu_status-0-segNo").val();
            if (IPLAT.isBlankString(segNo)){
                NotificationUtil({msg:"请先选择系统账套！"}, "error");
                return;
            }
            iframejQuery("#sub_query_status-0-segNo").val(segNo);
            iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
        },
        // 关闭窗口事件
        close: function (e) {
            var $iframe = userNum2Window.element.children("iframe");
            // 子窗口中的jQuery对象
            var iframejQuery = $iframe[0].contentWindow.$;

            // $iframe[0].contentWindow.resultGrid
            var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
            //
            // // 也可以使用如下的方式获取dataGrid
            // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

            var row = dataGrid.getCheckedRows();

            if (row.length > 0) {

                $("#inqu_status-0-customerId2").val(row[0].userNum);
                $("#inqu_status-0-customerName2").val(row[0].chineseUserName);

            }
            // 清空弹出框内容
            if (dataGrid.getDataItems().length > 0) {
                dataGrid.removeRows(dataGrid.getDataItems());
            }
        }
    },


        "userNum3": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNum3Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum3");
                var segNo=$("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)){
                    NotificationUtil({msg:"请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNum3Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#add_status-0-customerId").val(row[0].userNum);
                    $("#add_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        }
        // ,
        // "ADDSUBWINDOW": {
        //     // 打开窗口事件
        //     open: function (e) {
        //         // 清空弹出框内容
        //         if (sub_resultGrid.getDataItems().length > 0) {
        //             sub_resultGrid.removeRows(sub_resultGrid.getDataItems());
        //         }
        //         var checkRows = resultGrid.getCheckedRows();
        //         for (i = 0; i < checkRows.length; i++) {
        //             var varModel = resultGrid.getCheckedRows()[i];
        //             let reservationIdentity = '10';
        //             if (IPLAT.isBlankString(reservationIdentity)) {
        //                 NotificationUtil({msg: "预约身份(承运商/客户)！"}, "error");
        //                 return;
        //             }
        //             IPLAT.EFSelect.value($("#sub_query_status-0-reservationIdentity"), reservationIdentity);
        //             $("#sub_query_status-0-unitCode").val(varModel.unitCode);
        //             $("#sub_query_status-0-segNo").val(varModel.segNo);
        //         }
        //     }
        // },


}
});
