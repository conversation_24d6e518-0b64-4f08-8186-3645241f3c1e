//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baosight.iplat4j.core.web.export;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.transform.TransformConstants;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

public class XlsInfoExporter implements InfoExporter {
    private static final Logger logger = LoggerFactory.getLogger(XlsInfoExporter.class);
    private int formatType = -1;
    private int rowLimit = -1;

    public XlsInfoExporter() {
    }

    public int getFormatType() {
        return this.formatType;
    }

    public void setFormatType(int formatType) {
        this.formatType = formatType;
    }

    public int getRowLimit() {
        return this.rowLimit;
    }

    public void setRowLimit(int rowLimit) {
        this.rowLimit = rowLimit;
    }

    public Object exportData(EiBlock block) {
        HSSFWorkbook wb = new HSSFWorkbook();
        return this.exportData(block, wb, 0);
    }

    public HSSFWorkbook exportData(EiBlock block, HSSFWorkbook wb, int order) {
        if (wb != null && block != null) {
            Map styles = this.initStyles(wb, (short)8);
            int pageNum = '\uffff';
            int pages = block.getRowCount() % pageNum >= 0 ? block.getRowCount() / pageNum + 1 : block.getRowCount() / pageNum;
            String blockId = block.getBlockMeta().getBlockId();
            EiBlockMeta metas = block.getBlockMeta();
            List<Map<String, String>> records = block.getRows();

            for(int i = 0; i < pages; ++i) {
                EiBlock iblock = new EiBlock(blockId + i);
                iblock.addBlockMeta(metas);
                List<Map<String, String>> iList = new ArrayList();
                int nums = Math.min((i + 1) * pageNum, records.size());

                for(int j = i * pageNum; j < nums; ++j) {
                    iList.add(records.get(j));
                }

                iblock.addRows(iList);
                if (block.get(EiConstant.rowLimitStr) != null) {
                    iblock.set(EiConstant.rowLimitStr, block.get(EiConstant.rowLimitStr));
                }

                HSSFSheet sheet;
                if (i > 0) {
                    sheet = wb.createSheet(blockId + i);
                } else {
                    sheet = wb.createSheet(blockId);
                }

                HSSFPrintSetup ps = sheet.getPrintSetup();
                sheet.setAutobreaks(true);
                ps.setFitHeight((short)1);
                ps.setFitWidth((short)1);
                Map attr = (Map)Optional.ofNullable(block.getAttr()).orElse(Collections.emptyMap());
                Map groupColumns = (Map)Optional.ofNullable((Map)attr.get("groupColumns")).orElse(Collections.EMPTY_MAP);
                Map groupColumnsCname = (Map)Optional.ofNullable((Map)attr.get("groupColumnsCname")).orElse(Collections.EMPTY_MAP);
                List groupedColumns = (List)Optional.ofNullable((List)attr.get("groupedColumns")).orElse(Collections.EMPTY_LIST);
                if (groupColumns.size() <= 0) {
                    styles.put("range", false);
                } else {
                    HSSFRow row = sheet.createRow(0);
                    int c = 0;

                    for(Iterator var22 = metas.getMetas().keySet().iterator(); var22.hasNext(); ++c) {
                        Object o = var22.next();
                        HSSFCell hssfCell = row.createCell(c);
                        hssfCell.setCellStyle((HSSFCellStyle)styles.get("titleStyle"));
                        hssfCell.setCellType(1);
                        hssfCell.setCellValue("");
                        sheet.setColumnWidth(hssfCell.getColumnIndex(), (short)5000);
                        if (!groupedColumns.contains(o)) {
                            CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 1, c, c);
                            sheet.addMergedRegion(cellRangeAddress);
                            hssfCell.setCellValue(((EiColumn)metas.getMetas().get(o)).getCname());
                        }
                    }

                    groupColumns.forEach((k, v) -> {
                        List cols = (List)v;
                        String first = (String)cols.get(0);
                        String last = (String)cols.get(cols.size() - 1);
                        int first_index = this.keyIndexInMap(metas.getMetas(), first);
                        int last_index = this.keyIndexInMap(metas.getMetas(), last);
                        String range_title = (String)groupColumnsCname.get(k);
                        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, first_index, last_index);
                        HSSFCell hssfCell = row.createCell(first_index);
                        hssfCell.setCellValue(range_title);
                        hssfCell.setCellStyle((HSSFCellStyle)styles.get("titleStyle"));
                        hssfCell.setCellType(1);
                        sheet.addMergedRegion(cellRangeAddress);
                    });
                    styles.put("range", true);
                }

                this._export(iblock, sheet, styles);
            }

            return wb;
        } else {
            return null;
        }
    }

    private void _export(EiBlock block, HSSFSheet sheet, Map styles) {
        short cellnum = 0;
        int titleRow = (Boolean)styles.remove("range") ? 1 : 0;
        HSSFRow row = sheet.createRow(titleRow);
        EiBlockMeta blockmeta = block.getBlockMeta();
        String srowLimit = block.getString(EiConstant.rowLimitStr);
        Map metas = blockmeta.getMetas();
        int formatType;
        if (this.formatType != -1) {
            formatType = this.formatType;
        } else {
            formatType = TransformConstants.formatType;
        }

        int rowLimit;
        if (StringUtils.isNotBlank(srowLimit)) {
            try {
                rowLimit = Integer.parseInt(srowLimit);
            } catch (NumberFormatException var21) {
                if (this.rowLimit != -1) {
                    rowLimit = this.rowLimit;
                } else {
                    rowLimit = 1000;
                }
            }
        } else if (this.rowLimit != -1) {
            rowLimit = this.rowLimit;
        } else {
            rowLimit = 1000;
        }

        for(Iterator var12 = metas.keySet().iterator(); var12.hasNext(); ++cellnum) {
            Object o = var12.next();
            EiColumn column = blockmeta.getMeta(o.toString());
            HSSFCell hssfCell = row.createCell(cellnum);
            hssfCell.setCellStyle((HSSFCellStyle)styles.get("titleStyle"));
            hssfCell.setCellType(1);
            hssfCell.setCellValue(column.getCname());
            sheet.setColumnWidth(hssfCell.getColumnIndex(), (short)5000);
        }

        int size = block.getRowCount();
        if (size > rowLimit) {
            logger.warn("数据行超过最大上限" + rowLimit + ",多余的数据行将被舍弃，数据块－" + block.getBlockMeta().getBlockId());
            size = rowLimit;
        }

        HSSFDataFormat format = sheet.getWorkbook().createDataFormat();

        for(int i = 0; i < size; ++i) {
            row = sheet.createRow(i + titleRow + 1);
            cellnum = 0;

            for(Iterator var25 = metas.keySet().iterator(); var25.hasNext(); ++cellnum) {
                Object o = var25.next();
                HSSFCell hssfCell = row.createCell(cellnum);
                EiColumn column = blockmeta.getMeta(o.toString());
                if ("N".equals(column.getType())) {
                    hssfCell.setCellStyle((HSSFCellStyle)styles.get("numericStyle"));
                    hssfCell.setCellType(0);
                    Object objValue = block.getCellStr(i, column.getEname());
                    Double dval = null;
                    if (objValue != null) {
                        dval = NumberUtils.todouble(objValue);
                    }

                    hssfCell.setCellValue(dval);
                    hssfCell.getCellStyle().setAlignment(HorizontalAlignment.forInt((short)3));
                } else {
                    hssfCell.setCellStyle((HSSFCellStyle)styles.get("textStyle"));
                    hssfCell.setCellType(1);
                    hssfCell.setCellValue(ExportUtils.parseXLS(block.getCellStr(i, column.getEname())));
                }
            }
        }

    }

    private String getFormatStr(int scaleLength) {
        String formatStr = com.baosight.iplat4j.core.util.StringUtils.getFormat(1, scaleLength);
        formatStr = "#,##" + formatStr;
        return formatStr;
    }

    private Map initStyles(HSSFWorkbook wb, short fontHeight) {
        Map result = new HashMap();
        HSSFCellStyle titleStyle = wb.createCellStyle();
        HSSFCellStyle textStyle = wb.createCellStyle();
        HSSFCellStyle boldStyle = wb.createCellStyle();
        HSSFCellStyle numericStyle = wb.createCellStyle();
        HSSFCellStyle numericStyleBold = wb.createCellStyle();
        result.put("titleStyle", titleStyle);
        result.put("textStyle", textStyle);
        result.put("boldStyle", boldStyle);
        result.put("numericStyle", numericStyle);
        result.put("numericStyleBold", numericStyleBold);
        HSSFFont font = wb.createFont();
        font.setBold(false);
        font.setColor((short)8);
        font.setFontName("Arial");
        font.setFontHeightInPoints(fontHeight);
        HSSFFont fontBold = wb.createFont();
        fontBold.setBold(true);
        fontBold.setColor((short)8);
        fontBold.setFontName("Arial");
        fontBold.setFontHeightInPoints(fontHeight);
        numericStyle.setFont(font);
        numericStyle.setAlignment(HorizontalAlignment.forInt((short)3));
        numericStyleBold.setFont(fontBold);
        numericStyleBold.setAlignment(HorizontalAlignment.forInt((short)3));
        titleStyle.setFont(font);
        titleStyle.setFillForegroundColor((short)22);
        titleStyle.setFillPattern(FillPatternType.forInt((short)1));
        titleStyle.setBorderBottom(BorderStyle.valueOf((short)1));
        titleStyle.setBottomBorderColor((short)8);
        titleStyle.setBorderLeft(BorderStyle.valueOf((short)1));
        titleStyle.setLeftBorderColor((short)8);
        titleStyle.setBorderRight(BorderStyle.valueOf((short)1));
        titleStyle.setRightBorderColor((short)8);
        titleStyle.setBorderTop(BorderStyle.valueOf((short)1));
        titleStyle.setTopBorderColor((short)8);
        titleStyle.setAlignment(HorizontalAlignment.forInt((short)2));
        titleStyle.setVerticalAlignment(VerticalAlignment.forInt((short)1));
        textStyle.setFont(font);
        textStyle.setWrapText(true);
        boldStyle.setFont(fontBold);
        boldStyle.setWrapText(true);
        return result;
    }

    private int keyIndexInMap(Map map, Object key) {
        Object[] set = map.keySet().toArray();

        for(int i = 0; i < set.length; ++i) {
            if (set[i].equals(key)) {
                return i;
            }
        }

        return -1;
    }
}
