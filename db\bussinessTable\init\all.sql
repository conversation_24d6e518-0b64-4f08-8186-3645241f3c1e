-- 设备工艺参数主表
create table MEDV0001(
                         SEG_NO VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '账套',
                         MACHINE_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '机组代码',
                         MACHINE_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '机组名称',
                         PARAM_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '参数代码',
                         PARAM_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '参数名称',
                         PARAM_DESC VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '参数描述',
                         -- AUTO_FLAG INT(1) DEFAULT ' ' NOT NULL COMMENT '是否自动读取',
                         -- TAG_ID  VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT 'PLC点位ID',
                         PROD_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '品种',
                         PROD_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '品种名称',
                         SHOPSIGN VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '牌号',
                         SPEC_DESC VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '规格',
                         STATUS VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '状态',
                         PARAM_VERSION INTEGER NOT NULL COMMENT '参数版本号',
    -- 固定字段
                         UUID    VARCHAR(32) NOT NULL COMMENT '唯一编码',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         primary key (SEG_NO,MACHINE_CODE,PARAM_CODE)

)COMMENT='设备工艺参数主表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


-- 设备工艺参数子表
create table MEDV0002(

                         SEG_NO VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '账套',
                         MACHINE_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '机组代码',
                         MACHINE_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '机组名称',
                         PARAM_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '参数代码',
                         PARAM_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '参数名称',
                         PARAM_DETAIL_CODE VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '具体参数代码',
                         PARAM_DETAIL_NAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '具体参数名称',
                         PARAM_DETAIL_VALUE VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '具体参数值',
                         AUTO_FLAG INT(1) DEFAULT ' ' NOT NULL COMMENT '是否自动读取',
                         TAG_ID  VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT 'PLC点位ID',

    -- 固定字段
                         UUID    VARCHAR(32) NOT NULL COMMENT '唯一编码',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         TENANT_ID VARCHAR(64) DEFAULT '0' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                             primary key (SEG_NO,MACHINE_CODE,PARAM_CODE,PARAM_DETAIL_CODE)
)COMMENT='设备工艺参数子表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


