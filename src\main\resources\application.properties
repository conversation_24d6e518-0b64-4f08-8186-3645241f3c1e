spring.profiles.active=@profile.active@

spring.mvc.servlet.path=/
logging.level.com.baosight=info
spring.main.allow-bean-definition-overriding=true
server.port=8080
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**

projectName=imom
componentEname=imom
projectEnv=dev
moduleName=imom

platSchema=iplat4j
#生产
meviSchema=MEVI
#设备
mevgSchema=MEVG
#物流
meliSchema=MELI
#能源
meveSchema=MEVE
#人员
mevpSchema=MEVP
#资材
mevfSchema=MEVF


#"Baosteel Group" unicode encoding in Chinese, and ANY Chinese should be unicode encoded in this file.
enterpriseName=\u5b9d\u4fe1\u8f6f\u4ef6\u4f9b\u5e94\u94fe\u8f6f\u4ef6\u4e8b\u4e1a\u90e8
customerName=\u4e0a\u6d77\u5b9d\u94a2\u56fd\u9645\u7ecf\u6d4e\u8d38\u6613\u6709\u9650\u516c\u53f8


datasource.type=dbcp
#mysql mysql 设置现在是local设置
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=************************************************************************************************************************************
jdbc.username=bggjuser
jdbc.password=&X0KX2bQ
jdbc.minIdle=2
jdbc.maxActive=50
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00



configEx=iplat4j;xservices;


pool.maxTotalConnect=100
pool.maxConnectPerRoute=100
pool.connectTimeout=60000
pool.readTimeout=120000
pool.connectionRequestTimout=5000
pool.keepAliveTime=10

mqttListenerSwitch= true