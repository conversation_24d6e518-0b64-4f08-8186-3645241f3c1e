$(function () {
    IPLATUI.EFTree = {
        "groupsTree": {
            ROOT: {label: "root", text: "菜单视角授权", leaf: true},
            select: function (e) {
            },
            query: function (postEiInfo, model) {
                if (!IPLAT.isUndefined(window.parent.permissionControl)
                    && typeof (window.parent.permissionControl) === "function") {
                    window.parent.permissionControl(postEiInfo);
                }
                return postEiInfo;
            },
            checkboxes: true,
            checkboxes: {  // 配置结点前多选框,可直接配置checkboxes: true
                template: kendo.template($("#auth-template").html()),
                checkChildren: true  // 是否连同子结点一起勾选
            },
            check: onCheck  // 勾选触发事件配置
        }
    };


    $("#commitBtn").on("click", function () {
        //获取id为groupsTree
        var tree = $("#groupsTree").data("kendoTreeView");
        //获取tree的结点
        var nodes = tree.dataSource.view();

        var checkedList = [];
        haveAuthNodes(nodes, checkedList);
        var checkedIds = checkedList.join(",");
        var eiInfo = new EiInfo();
        eiInfo.set("newAuth", checkedIds);
        var inquBlock = new EiBlock("inqu_status");
        eiInfo.addBlock(inquBlock);
        window.parent.addResInfo(eiInfo);
        EiCommunicator.send("XSLV0202", "insertAuth", eiInfo, {
            onSuccess: function (ei) {
                IPLAT.alert({
                    message: ei.getMsg(),
                    okFn: function (e) {
                        if (ei.getStatus() > -1) {
                            if(!IPLAT.isUndefined(window.parent.refreshAuthCallback)
                                && typeof(window.parent.refreshAuthCallback)==="function"){
                                window.parent.refreshAuthCallback();
                            }else{
                                window.parent.$("#objectWindow").data("kendoWindow").close();
                            }
                        }
                    },
                    title: '提示信息'
                });
            }, onFail: function (ei) {
                IPLAT.alert(ei.getMsg());
            }
        });
    });

    $("#cancelBtn").on("click", function () {
        $("#commitBtn").attr("disabled", true);
        window.parent.$("iframe")[0].contentWindow.location.reload();
    });

    /**
     *
     * @param nodes
     * @param checkedList
     * @param notCheckedList
     */
    function haveAuthNodes(nodes, checkedList) {
        for (var i = 0; i < nodes.length; i++) {
            var text = nodes[i].text;
            var flag = true;
            if (flag) {
                if (nodes[i].hasChildren) {
                    haveAuthNodes(nodes[i].children.view(), checkedList);
                }else{
                    if (nodes[i].checked == true) {
                        if (nodes[i].isAuth == null) {
                            checkedList.push(nodes[i].id);
                        }
                    }
                }
            }
        }
    }


    function checkedNodeIds(nodes, checkedNodes) {
        for (var i = 0; i < nodes.length; i++) {
            if (nodes[i].checked) {
                checkedNodes.push(nodes[i].id);
            }

            if (nodes[i].hasChildren) {
                checkedNodeIds(nodes[i].children.view(), checkedNodes);
            }
        }
    }

    function onCheck(e) {

        var checkedNodes = [],
            treeView = $("#groupsTree").data("kendoTreeView"),
            checkedIds;

        checkedNodeIds(treeView.dataSource.view(), checkedNodes);

        if (checkedNodes.length > 0) {
            checkedIds = checkedNodes.join(",");
        } else {
            checkedIds = "无节点被勾选";
        }

        $("#checkedIds").val(checkedIds);
    }
});
