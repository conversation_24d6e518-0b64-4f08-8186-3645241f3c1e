<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<c:set var="loginCName" value='<%=UserSession.getLoginCName()%>'/>
<script>
    var loginCName = "${loginCName}";
</script>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="清单信息" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-faultId" cname="故障单号" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFSelect ename="inqu_status-0-faultSource" cname="故障来源" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P053"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfoMainQuery" center="true"
                                     ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

                    <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false"
                                     containerId="deviceInfoMainQuery" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>

                </div>
                <div class="row">
                    <EF:EFSelect ename="inqu_status-0-faultStatus" cname="状态" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P053"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="inqu_status-0-faultType" cname="故障类型" colWidth="3"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                                 optionLabel="{valueField:'', textField:'全部'}">
                        <EF:EFCodeOption codeName="P055"/>
                    </EF:EFSelect>
                    <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                                   endName="inqu_status-0-recCreateTimeEnd" readonly="true"
                                   startCname="创建时间(起)" endCname="创建时间(止)"
                                   ratio="3:3" format="yyyy-MM-dd">
                    </EF:EFDateSpan>
                    <EF:EFDateSpan startName="inqu_status-0-faultStartTime"
                                   endName="inqu_status-0-faultEndTime" readonly="true"
                                   startCname="故障日期(起)" endCname="故障日期(止)"
                                   ratio="3:3" format="yyyy-MM-dd">
                    </EF:EFDateSpan>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="查询结果">
                <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
                    <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="faultId" cname="故障单号" enable="false" width="120"
                                 align="center"/>
                    <EF:EFComboColumn ename="faultStatus" cname="状态" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P053"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="faultSource" cname="故障来源" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P054"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="voucherNum" cname="依据凭单" width="130" align="center" enable="false"/>
                    <EF:EFColumn ename="eArchivesNo" cname="设备代码" align="center"/>
                    <EF:EFColumn ename="equipmentName" cname="设备名称"/>
                    <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
                    <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
                    <EF:EFComboColumn ename="faultType" cname="故障类型" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P055"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="faultLevel" cname="故障级别" align="center" width="70"
                                      enable="false">
                        <EF:EFCodeOption codeName="P056"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="faultDesc" cname="故障描述" align="center" width="150"/>
                    <EF:EFColumn ename="faultStartTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="故障开始时间"/>
                    <EF:EFColumn ename="faultEndTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                                 cname="故障结束时间"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="100"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="详细信息" id="info-2">
            <EF:EFRegion id="detail" title="基础信息">
                <EF:EFInput ename="detail_status-0-uuid" cname="UUID" type="hidden"/>
                <EF:EFInput ename="detail_status-0-faultStatus" cname="状态" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="unitInfo01" center="true"
                                     ename="detail_status-0-unitCode" cname="业务单元代码" colWidth="3"/>
                    <EF:EFSelect readonly="true" ename="detail_status-0-segNo" cname="业务单元简称" colWidth="3">
                        <EF:EFOptions blockId="unitBlock" valueField="segNo" textField="segName"/>
                    </EF:EFSelect>
                    <EF:EFInput ename="detail_status-0-eArchivesNo" readonly="true" cname="设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="equipmentInfo" center="true"
                                     ename="detail_status-0-equipmentName" cname="设备名称" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="detail_status-0-deviceCode" readonly="true" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" readonly="true" clear="false" required="true"
                                     containerId="deviceInfo" center="true" ename="detail_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>
                    <EF:EFDatePicker ename="detail_status-0-faultStartTime" role="datetime" required="true"
                                     cname="故障开始时间" colWidth="3" parseFormats="['yyyyMMddHHmmss']"
                                     dateFormat="yyyy-MM-dd HH:mm:ss"/>
                    <EF:EFSelect readonly="true" ename="detail_status-0-faultSource" cname="故障来源" colWidth="3">
                        <EF:EFCodeOption codeName="P054"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFSelect required="true" ename="detail_status-0-faultType" cname="故障类型"
                                 colWidth="3">
                        <EF:EFCodeOption codeName="P055"/>
                    </EF:EFSelect>
                    <EF:EFSelect required="true" ename="detail_status-0-faultLevel" cname="故障级别"
                                 colWidth="3">
                        <EF:EFCodeOption codeName="P056"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFInput required="true" ename="detail_status-0-faultDesc" cname="故障描述" colWidth="12"
                                ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="detail2" title="处理信息">
                <div id="area1">
                    <div class="row">
                        <EF:EFDatePicker ename="detail_status-0-faultEndTime" role="datetime" required="true"
                                         cname="故障结束时间" colWidth="3" parseFormats="['yyyyMMddHHmmss']"
                                         dateFormat="yyyy-MM-dd HH:mm:ss"/>
                        <EF:EFSelect required="true" ename="detail_status-0-isOverhaul" cname="是否检修"
                                     colWidth="3">
                            <EF:EFOption value="1" label="是"/>
                            <EF:EFOption value="0" label="否"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row">
                        <EF:EFInput required="true" ename="detail_status-0-handleMeasures" cname="处理措施"
                                    colWidth="12"
                                    ratio="1:11" type="textarea" minLength="1" maxLength="512"/>
                    </div>
                </div>
                <div id="area2">
                    <div class="row">
                        <EF:EFSelect required="true" ename="detail_status-0-overhaulType" cname="施工类别"
                                     colWidth="3">
                            <EF:EFCodeOption codeName="P025"/>
                        </EF:EFSelect>
                        <EF:EFInput required="true" ename="detail_status-0-implementManName" readonly="true"
                                    cname="实施人"
                                    colWidth="3"/>
                        <EF:EFInput ename="detail_status-0-outsourcingContactId" readonly="true" cname="委外联络单号"
                                    colWidth="3"/>
                    </div>
                    <div class="row">
                        <EF:EFDatePicker ename="detail_status-0-overhaulImplementDate" required="true"
                                         cname="检修实施日期" colWidth="3"/>
                        <EF:EFInput ename="detail_status-0-actualOverhaulNumber" required="true" cname="检修人数"
                                    colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                        <EF:EFInput ename="detail_status-0-actualOverhaulTime" required="true" cname="检修耗时(min)"
                                    colWidth="3" data-rules="positive_integer" placeholder="必须输入数字"/>
                    </div>
                    <div class="row">
                        <EF:EFSelect ename="detail_status-0-offlinePartsGone" cname="下线零件去向" colWidth="3">
                            <EF:EFOption label="" value=""/>
                            <EF:EFCodeOption codeName="P027"/>
                        </EF:EFSelect>
                        <EF:EFSelect required="true" ename="detail_status-0-isHot" cname="是否动火"
                                     colWidth="3">
                            <EF:EFOption value="1" label="是"/>
                            <EF:EFOption value="0" label="否"/>
                        </EF:EFSelect>
                        <EF:EFButton ename="uploadHot" cname="上传动火手续"/>
                    </div>
                    <div class="row">
                        <EF:EFInput ename="detail_status-0-overhaulSuggestions" cname="主要问题及建议"
                                    colWidth="12" ratio="1:11" type="textarea"/>
                    </div>
                    <div class="row">
                        <EF:EFInput ename="detail_status-0-overhaulLegacyProject" cname="待处理及遗留问题"
                                    colWidth="12" ratio="1:11" type="textarea"/>
                    </div>
                    <div class="row">
                        <EF:EFInput required="true" ename="detail_status-0-overhaulSummarize" cname="综合评价及总结"
                                    colWidth="12" ratio="1:11" type="textarea"/>
                    </div>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="inqu_status2" title="查询条件" hidden="hidden">
                <EF:EFInput ename="inqu_status2-0-faultId" cname="故障单号" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-unitCode" cname="业务单元代码" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-segNo" cname="系统套账" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-segName" cname="业务单元简称" type="hidden"/>
                <EF:EFInput ename="inqu_status2-0-eArchivesNo" cname="设备代码" type="hidden"/>
            </EF:EFRegion>
            <EF:EFRegion id="zc_detail" title="资材备件领用">
                <EF:EFGrid blockId="zc_detail" autoDraw="no" serviceName="VGDM0804" sort="all"
                           queryMethod="query" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" primaryKey="true" hidden="true"/>
                    <EF:EFColumn ename="inventoryUuid" cname="库存uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" readonly="true"
                                 hidden="true" enable="false" align="center"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" readonly="true"
                                 hidden="true" enable="false" align="center"/>
                    <EF:EFComboColumn ename="stuffReceivingStatus" cname="状态" enable="false" align="center"
                                      textField="textField" valueField="valueField" width="70">
                        <EF:EFCodeOption codeName="P061"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="stuffCode" cname="资材代码" readonly="true"
                                 enable="false" align="center"/>
                    <EF:EFColumn ename="stuffName" cname="资材名称" align="center" enable="false" width="120"/>
                    <EF:EFColumn ename="specDesc" cname="规格" readonly="true"
                                 enable="false" width="150"/>
                    <EF:EFColumn ename="usingWgt" cname="申请量" required="true" width="70"
                                 align="center"/>
                    <EF:EFColumn ename="measureId" cname="计量单位" readonly="true" width="70"
                                 enable="false" align="center"/>
                    <EF:EFColumn ename="stuffUsage" cname="用途" readonly="true" width="200"
                                 enable="false"/>
                    <EF:EFColumn ename="purContractNum" cname="采购合同号" readonly="true" width="160" align="center"
                                 enable="false"/>
                    <EF:EFColumn ename="purOrderNum" cname="采购合同子项号" readonly="true" width="160" align="center"
                                 enable="false"/>
                    <EF:EFColumn ename="locationId" cname="库位代码" enable="false" width="100" align="center"
                                 hidden="true"/>
                    <EF:EFColumn ename="locationName" cname="库位名称" enable="false" width="100" align="left"
                                 hidden="true"/>
                    <EF:EFColumn ename="warehouseCode" cname="仓库代码" readonly="true"
                                 enable="false" align="center" width="90"/>
                    <EF:EFColumn ename="warehouseName" cname="仓库名称" readonly="true"
                                 enable="false" align="center" width="200"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="80"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="附件信息">
                <EF:EFInput ename="inqu2_status-0-relevanceId" type="hidden"/>
                <EF:EFInput ename="inqu2_status-0-segNo" type="hidden"/>
                <div style="display: none;">
                    <EF:EFInput ename="fileForm" type="file"/>
                    <EF:EFInput ename="fileForm1" type="file"/>
                </div>
                <EF:EFGrid blockId="result2" autoDraw="no" readonly="true"
                           serviceName="VGDM0801" queryMethod="queryFile" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="id" hidden="true"/>
                    <EF:EFColumn ename="fileId" cname="fileId" hidden="true"/>
                    <EF:EFColumn ename="relevanceId" cname="故障单号" width="120" align="center"/>
                    <EF:EFComboColumn ename="relevanceType" cname="附件类型" enable="false" align="center" width="70">
                        <EF:EFOption label="故障相关" value="VGDM07"/>
                        <EF:EFOption label="动火手续" value="VGDM07H"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="uploadFileName" cname="文件名称"/>
                    <EF:EFColumn ename="uploadFilePath" cname="文件路径" hidden="true"/>
                    <EF:EFColumn ename="uploadFilePathD" cname="文件路径"/>
<%--                    <EF:EFColumn ename="fifleSize" cname="文件大小"/>--%>
                    <EF:EFColumn ename="fifleType" cname="文件类型"/>
                    <EF:EFColumn ename="recCreatorName" cname="上传人"/>
                    <EF:EFColumn ename="recCreateTime" editType="datetime"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="上传时间"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VFPM0101" id="inventoryInfo" width="90%" height="60%" title="资材库存查询"/>

    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfoMainQuery" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfoMainQuery" width="90%" height="60%"/>
</EF:EFPage>
