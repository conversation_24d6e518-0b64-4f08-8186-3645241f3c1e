<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-07 22:39:41
   		Version :  1.0
		tableName :${platSchema}.xs_pda_menu 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 SEG_NAME  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 MENU_NAME  VARCHAR   NOT NULL, 
		 MENU_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="XTSS20">

	<select id="queryUserTeam" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		t2.ORG_ID as "orgId",  <!-- 组织ID -->
		t1.ORG_CNAME as "orgCname",  <!-- 组织编码 -->
		t1.ORG_ENAME as "orgEname",  <!-- 组织名称 -->
		u1.ID as "userGroupId",  <!-- 用户组ID -->
		u1.GROUP_ENAME as "groupEname",  <!--用户组英文名-->
		u1.GROUP_CNAME as "groupCname",  <!--用户组中文名-->
		u2.GROUP_ENAME as "parentGroupEname",  <!--用户组英文名-->
		u2.GROUP_CNAME as "parentGroupCname",  <!--用户组中文名-->
		u1.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
		u1.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
		t2.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
		t2.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		t2.ARCHIVE_FLAG as "archiveFlag" <!-- 归档标记 -->
		FROM ${platSchema}.TXSOG03 t2
		LEFT JOIN ${platSchema}.XS_USER_GROUP u1 ON u1.ID=t2.USER_GROUP_ID
		LEFT JOIN ${platSchema}.TXSOG01 t1 ON t1.ORG_ID = t2.ORG_ID
		LEFT JOIN ${platSchema}.XS_USER_GROUP_MEMBER rela on u1.ID = rela.MEMBER_ID
		LEFT JOIN ${platSchema}.XS_USER_GROUP u2 on u2.ID = rela.PARENT_ID
		where 1=1
		<isNotEmpty prepend=" AND " property="segName">
			t1.ORG_CNAME = #segName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupEname">
			u1.GROUP_ENAME like ('%$groupEname$%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupCname">
			u1.GROUP_CNAME like ('%$groupCname$%')
		</isNotEmpty>
	</select>

	<select id="queryUserGroup" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT
		t1.member_id as "memberId",
		t1.parent_id as "parentId",
		t1.member_type as "memberType",
		CASE t1.MEMBER_TYPE
		WHEN 'USER_GROUP' THEN t2.GROUP_CNAME
		ELSE t3.USER_NAME
		END AS "memberName",
		CASE t1.MEMBER_TYPE
		WHEN 'USER_GROUP' THEN t2.GROUP_ENAME
		ELSE t3.LOGIN_NAME
		END AS "memberEname",
		t4.GROUP_ENAME as "parentEname",
		t4.GROUP_CNAME as "parentName",
		t1.sort_index as "sortIndex",
		t1.path as "path",
		t1.rec_creator as "recCreator",
		t1.rec_create_time as "recCreateTime",
		t1.rec_revisor as "recRevisor",
		t1.rec_revise_time as "recReviseTime",
		t1.archive_flag as "archiveFlag"
		FROM ${platSchema}.XS_USER_GROUP_MEMBER t1
		LEFT JOIN ${platSchema}.XS_USER_GROUP t2 ON t1.MEMBER_ID=t2.ID
		LEFT JOIN ${platSchema}.XS_USER t3 ON t1.MEMBER_ID = t3.USER_ID
		LEFT JOIN ${platSchema}.XS_USER_GROUP t4 ON t1.PARENT_ID=t4.ID
		where 1=1
		<isNotEmpty prepend=" AND " property="userId">
			t1.member_id = #userId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			t4.GROUP_ENAME like concat('%',#segNo#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="parentId">
			t1.parent_id = #parentId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="memberEname">
			(t2.group_ename like ('%$memberEname$%') or t3.login_name like ('%$memberEname$%'))
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="memberName">
			(t2.group_cname like ('%$memberName$%') or t3.user_name like ('%$memberName$%'))
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="parentName">
			t4.group_cname like ('%$parentName$%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="parentEname">
			t4.group_ename like ('%$parentEname$%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="memberType">
			t1.member_type = #memberType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sqlCondition">
			t3.user_group_ename $sqlCondition$
		</isNotEmpty>
		<!--<isNotEmpty prepend=" AND " property="loginName">
            t1.parent_id in (<include refid="getAllManagerGroupsByLoginNameDetailIncludeManagerGroups"/>)
        </isNotEmpty>-->
		ORDER BY t1.SORT_INDEX,t1.MEMBER_ID,t1.PARENT_ID
	</select>

	<select id="query" parameterClass="java.util.Map"
			resultClass="com.baosight.imom.xt.ss.domain.XTSS20">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		SEG_NAME	as "segName",
		UUID	as "uuid",
		REC_CREATOR	as "recCreator",
		REC_CREATOR_NAME	as "recCreatorName",
		REC_REVISOR	as "recRevisor",
		REC_REVISOR_NAME	as "recRevisorName",
		REC_REVISE_TIME	as "recReviseTime",
		DEL_FLAG	as "delFlag",
		TENANT_USER	as "tenantUser",
		USER_PERMISSION	as "userPermission",
		GROUP_ENAME	as "groupEname",
		USER_GROUP_ID	as "userGroupId",
		GROUP_CNAME	as "groupCname",
		REC_CREATE_TIME	as "recCreateTime"
		FROM ${platSchema}.xs_pda_user_permission WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userPermission">
			USER_PERMISSION = #userPermission#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupEname">
			GROUP_ENAME = #groupEname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userGroupId">		
			USER_GROUP_ID = #userGroupId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupCname">
			GROUP_CNAME like concat('%',#groupCname#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segName">
			SEG_NAME = #segName#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				UUID asc
			</isEmpty>
		</dynamic>
	</select>

	<select id="queryPDA" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		SEG_NAME	as "segName",
		UUID	as "uuid",
		REC_CREATOR	as "recCreator",
		REC_CREATOR_NAME	as "recCreatorName",
		REC_REVISOR	as "recRevisor",
		REC_REVISOR_NAME	as "recRevisorName",
		REC_REVISE_TIME	as "recReviseTime",
		DEL_FLAG	as "delFlag",
		TENANT_USER	as "tenantUser",
		USER_PERMISSION	as "userPermission",
		GROUP_ENAME	as "groupEname",
		USER_GROUP_ID	as "userGroupId",
		GROUP_CNAME	as "groupCname",
		REC_CREATE_TIME	as "recCreateTime"
		FROM ${platSchema}.xs_pda_user_permission WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userPermission">
			USER_PERMISSION = #userPermission#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupEname">
			GROUP_ENAME = #groupEname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userGroupId">
			USER_GROUP_ID = #userGroupId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupCname">
			GROUP_CNAME = #groupCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segName">
			SEG_NAME = #segName#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				UUID asc
			</isEmpty>
		</dynamic>
	</select>

	<select id="queryMenu" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				SEG_NAME	as "segName",  
				UUID	as "uuid",  
				REC_CREATOR	as "recCreator",  
				REC_CREATOR_NAME	as "recCreatorName",  
				REC_REVISOR	as "recRevisor",  
				REC_REVISOR_NAME	as "recRevisorName",  
				REC_REVISE_TIME	as "recReviseTime",  
				DEL_FLAG	as "delFlag",  
				TENANT_USER	as "tenantUser",  
				MENU_NAME	as "menuName",  <!-- 菜单名称 -->
				MENU_ID	as "menuId" <!-- 菜单ID -->
		FROM ${platSchema}.xs_pda_menu WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="menuId">
			MENU_ID = #menuId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="menuName">
			MENU_NAME like concat('%',#menuName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${platSchema}.xs_pda_user_permission WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userPermission">
			USER_PERMISSION = #userPermission#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupEname">
			GROUP_ENAME = #groupEname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userGroupId">
			USER_GROUP_ID = #userGroupId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="groupCname">
			GROUP_CNAME like concat('%',#groupCname#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segName">
			SEG_NAME = #segName#
		</isNotEmpty>
	</select>

	<insert id="insert">
		INSERT INTO ${platSchema}.xs_pda_user_permission (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										SEG_NAME,
										UUID,
										REC_CREATOR,
										REC_CREATOR_NAME,
										REC_REVISOR,
										REC_REVISOR_NAME,
										REC_REVISE_TIME,
										DEL_FLAG,
										TENANT_USER,
		USER_PERMISSION,
		GROUP_ENAME,
		USER_GROUP_ID,
		GROUP_CNAME,
		REC_CREATE_TIME)
	    VALUES (#segNo#, #unitCode#, #segName#, #uuid#, #recCreator#, #recCreatorName#,
		#recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#, #tenantUser#, #userPermission#, #groupEname#,
		#userGroupId#,#groupCname#,#recCreateTime#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${platSchema}.xs_pda_user_permission WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${platSchema}.xs_pda_user_permission
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					SEG_NAME	= #segName#,   
					REC_CREATOR	= #recCreator#,
					REC_CREATOR_NAME	= #recCreatorName#,   
					REC_REVISOR	= #recRevisor#,   
					REC_REVISOR_NAME	= #recRevisorName#,   
					REC_REVISE_TIME	= #recReviseTime#,   
					DEL_FLAG	= #delFlag#,   
					TENANT_USER	= #tenantUser#,   
					USER_PERMISSION	= #userPermission#,
					GROUP_ENAME	= #groupEname#,
					USER_GROUP_ID	= #userGroupId#,
					GROUP_CNAME	= #groupCname#,
					REC_CREATE_TIME	= #recCreateTime#
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>