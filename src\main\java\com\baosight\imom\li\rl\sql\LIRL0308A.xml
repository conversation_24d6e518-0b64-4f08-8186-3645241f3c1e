<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-10-29 16:11:38
       Version :  1.0
    tableName :${meliSchema}.tlirl0308
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CAR_TRACE_NO  VARCHAR   NOT NULL,
     PUTIN_ID  VARCHAR   NOT NULL,
     PUTOUT_ID  VARCHAR   NOT NULL,
     VOUCHER_NUM  VARCHAR   NOT NULL,
     DRIVER_NAME  VARCHAR   NOT NULL,
     DRIVER_TEL  VARCHAR   NOT NULL,
     DRIVER_IDENTITY  VARCHAR   NOT NULL,
     VEHICLE_NO  VARCHAR,
     DELIVER_TYPE  VARCHAR   NOT NULL,
     DELIVER_NAME  VARCHAR,
     PACK_ID  VARCHAR   NOT NULL,
     MAT_INNER_ID  VARCHAR   NOT NULL,
     FACTORY_ORDER_NUM  VARCHAR   NOT NULL,
     PUT_IN_OUT_FLAG  VARCHAR   NOT NULL,
     PROD_TYPE_ID  VARCHAR   NOT NULL,
     PROD_TYPE_NAME  VARCHAR   NOT NULL,
     SHOPSIGN  VARCHAR   NOT NULL,
     SPEC_DESC  VARCHAR   NOT NULL,
     WEIGHT  DECIMAL   NOT NULL,
     QUANTITY  DECIMAL   NOT NULL,
     WAREHOUSE_CODE  VARCHAR   NOT NULL,
     WAREHOUSE_NAME  VARCHAR   NOT NULL,
     CUSTOMER_ID  VARCHAR   NOT NULL,
     CUSTOMER_NAME  VARCHAR   NOT NULL,
     LOCATION_ID  VARCHAR   NOT NULL,
     LOCATION_NAME  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     SYS_REMARK  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0308">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            CAR_TRACE_NO = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putinId">
            PUTIN_ID = #putinId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutId">
            PUTOUT_ID = #putoutId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM = #voucherNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNumMH">
            VOUCHER_NUM like concat('%',#voucherNumMH#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNoList">
            VEHICLE_NO in
            <iterate property="vehicleNoList" open="("
                     close=")" conjunction=" , ">
                #vehicleNoList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutIdNotNull">
            PUTOUT_ID is not null
            AND PUTOUT_ID != ""
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            DRIVER_NAME = #driverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverTel">
            DRIVER_TEL = #driverTel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverIdentity">
            DRIVER_IDENTITY = #driverIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deliverType">
            DELIVER_TYPE = #deliverType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deliverName">
            DELIVER_NAME = #deliverName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="matInnerId">
            MAT_INNER_ID = #matInnerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryOrderNum">
            FACTORY_ORDER_NUM = #factoryOrderNum#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putInOutFlag">
            PUT_IN_OUT_FLAG = #putInOutFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="prodTypeId">
            PROD_TYPE_ID = #prodTypeId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="prodTypeName">
            PROD_TYPE_NAME = #prodTypeName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="shopsign">
            SHOPSIGN = #shopsign#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="specDesc">
            SPEC_DESC = #specDesc#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="weight">
            WEIGHT = #weight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="quantity">
            QUANTITY = #quantity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseName">
            WAREHOUSE_NAME = #warehouseName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationName">
            LOCATION_NAME = #locationName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sysRemark">
            SYS_REMARK = #sysRemark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="signatureFlag">
            SIGNATURE_FLAG = #signatureFlag#
        </isNotEmpty>
    </sql>


    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0308">
        SELECT
        SEG_NO as "segNo",  <!-- 账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        PUTIN_ID as "putinId",  <!-- 入库单号（无计划入库为空） -->
        PUTOUT_ID as "putoutId",  <!-- 出库单号 -->
        VOUCHER_NUM as "voucherNum",  <!-- 提单号（多个） -->
        DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        DRIVER_TEL as "driverTel",  <!-- 司机手机号 -->
        DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        DELIVER_TYPE as "deliverType",  <!-- 交货方式（代运、自提） -->
        DELIVER_NAME as "deliverName",  <!-- 交货方式名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        MAT_INNER_ID as "matInnerId",  <!-- 材料管理好（无计划入库为空） -->
        FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂资源号 -->
        PUT_IN_OUT_FLAG as "putInOutFlag",  <!-- 出入库标记（入库：1，出库：2） -->
        PROD_TYPE_ID as "prodTypeId",  <!-- 品种附属码 -->
        PROD_TYPE_NAME as "prodTypeName",  <!-- 品种附属码名称 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC_DESC as "specDesc",  <!-- 规格 -->
        WEIGHT as "weight",  <!-- 重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CUSTOMER_NAME as "customerName",  <!-- 客户名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        PUTOUT_DATE  as "putoutDate", <!--出库日期-->
        SIGNATURE_FLAG	as "signatureFlag",  <!-- 签收标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and DEL_FLAG='0'
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutIdStr">
            PUTOUT_ID in
            <iterate property="putoutIdStr" open="("
                     close=")" conjunction=" , ">
                #putoutIdStr[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packIdStr">
            PACK_ID in
            <iterate property="packIdStr" open="("
                     close=")" conjunction=" , ">
                #packIdStr[]#
            </iterate>
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and DEL_FLAG='0'
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </select>


    <select id="queryPrintCount" resultClass="String">
        SELECT PRINT_COUNT AS "printCount" FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and DEL_FLAG='0'
        <include refid="condition"/>
    </select>

    <select id="queryInfo" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0308">
        SELECT
        SEG_NO as "segNo",  <!-- 账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        PUTIN_ID as "putinId",  <!-- 入库单号（无计划入库为空） -->
        PUTOUT_ID as "putoutId",  <!-- 出库单号 -->
        VOUCHER_NUM as "voucherNum",  <!-- 提单号（多个） -->
        DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        DRIVER_TEL as "driverTel",  <!-- 司机手机号 -->
        DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        DELIVER_TYPE as "deliverType",  <!-- 交货方式（代运、自提） -->
        DELIVER_NAME as "deliverName",  <!-- 交货方式名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        MAT_INNER_ID as "matInnerId",  <!-- 材料管理好（无计划入库为空） -->
        FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂资源号 -->
        PUT_IN_OUT_FLAG as "putInOutFlag",  <!-- 出入库标记（入库：1，出库：2） -->
        PROD_TYPE_ID as "prodTypeId",  <!-- 品种附属码 -->
        PROD_TYPE_NAME as "prodTypeName",  <!-- 品种附属码名称 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC_DESC as "specDesc",  <!-- 规格 -->
        WEIGHT as "weight",  <!-- 重量 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CUSTOMER_NAME as "customerName",  <!-- 客户名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        PUTOUT_DATE  as "putoutDate", <!--出库日期-->
        case when SIGNATURE_FLAG='0' then '未签收' else '已签收' end	as "signatureFlag",  <!-- 签收标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId" ,<!-- 租户ID -->
        (select tlirl0311.LEAVE_FACTORY_DATE
        from meli.tlirl0311 tlirl0311
        where tlirl0308.SEG_NO = tlirl0311.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0311.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO = tlirl0311.VEHICLE_NO
        limit 1) as "leaveFactoryDate"
        FROM ${meliSchema}.tlirl0308  tlirl0308 WHERE 1=1
        and DEL_FLAG='0'
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryVehicleNoTrace" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
        select tlirl0301.SEG_NO                                                    as "segNo",
        tlirl0301.UNIT_CODE                                                 as "unitCode",
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0301.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        tlirl0301.CAR_TRACE_NO                                              as "carTraceNo",
        tlirl0301.STATUS                                              as "status",
        (select PUTIN_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "putinId",
        (select PUTOUT_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1) as "putoutId",
        (select VOUCHER_NUM
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "voucherNum",
        tlirl0301.DRIVER_NAME                                               as "driverName",
        tlirl0301.TEL_NUM                                                   as "driverTel",
        tlirl0301.ID_CARD                                                   as "driverIdentity",
        tlirl0301.VEHICLE_NO                                                as "vehicleNo",
        (select PACK_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "packId",
        (select MAT_INNER_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "matInnerId",

        (select FACTORY_ORDER_NUM
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "factoryOrderNum",

        (select PUT_IN_OUT_FLAG
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "putInOutFlag",
        (select PROD_TYPE_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "prodTypeId",

        (select PROD_TYPE_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "prodTypeName",

        (select SHOPSIGN
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "shopsign",

        (select SPEC_DESC
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "specDesc",

        (select WEIGHT
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "weight",

        (select QUANTITY
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "quantity",

        (select QUANTITY
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "quantity",
        (select WAREHOUSE_CODE
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "warehouseCode",

        (select WAREHOUSE_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "warehouseName",


        (select CUSTOMER_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "customerId",

        (select CUSTOMER_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "customerName",

        (select LOCATION_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "locationId",

        (select LOCATION_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "locationName",
        tlirl0301.REC_CREATOR                                               as "recCreator",
        tlirl0301.REC_CREATOR_NAME                                          as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                                           as "recCreateTime",
        tlirl0301.REC_REVISOR                                               as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                                          as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                                           as "recReviseTime",
        tlirl0301.ARCHIVE_FLAG                                              as "archiveFlag",
        tlirl0301.DEL_FLAG                                                  as "delFlag",
        tlirl0301.REMARK                                                    as "remark",
        tlirl0301.SYS_REMARK                                                as "sysRemark",
        tlirl0301.UUID                                                      as "uuid",
        tlirl0301.TENANT_ID                                                 as "tenantId",
        tlirl0301.STATUS                                                    as "status",
        tlirl0301.HAND_TYPE                                                    "handType",
        tlirl0301.TARGET_HAND_POINT_ID                                      as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                    as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                        "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                    as "currentHandPointName",
        tlirl0301.BEGIN_ENTRUCKING_TIME                                        "beginEntruckingTime",
        tlirl0301.COMPLETE_UNINSTALL_TIME                                   as "completeUninstallTime",
        tlirl0301.FACTORY_AREA                                              as "factoryArea",
        tlirl0301.FACTORY_AREA_NAME                                              as "factoryAreaName",
        (select tlirl0407.NEXT_TATGET
        from ${meliSchema}.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID = (select max(tlirl0407.FINISH_LOAD_ID)
        from ${meliSchema}.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00')) as "nextTarget"
        from ${meliSchema}.tlirl0301 tlirl0301
        where 1 = 1
        and tlirl0301.SEG_NO = #segNo#

        <isNotEmpty  prepend="and" property="carTraceNo">
            tlirl0301.CAR_TRACE_NO like concat('%',#carTraceNo#,'%')
        </isNotEmpty>

        <isNotEmpty  prepend="and" property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty  prepend="and" property="status">
            <isEqual property="status" compareValue="A0">
                tlirl0301.STATUS in ('20','30','40')
            </isEqual>
            <isNotEqual property="status" compareValue="A0">
                tlirl0301.STATUS =#status#
            </isNotEqual>
        </isNotEmpty>

        <isEmpty  prepend="and" property="status">
            tlirl0301.STATUS != '00'
        </isEmpty>
        <isNotEmpty  prepend="and" property="factoryArea">
            tlirl0301.FACTORY_AREA =#factoryArea#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startTime">
            substr(tlirl0301.REC_CREATE_TIME,1,8) >= replace(#startTime#,'-','')
        </isNotEmpty>
        <!--创建时间起始-->
        <isNotEmpty prepend=" and " property="endTime">
            substr(tlirl0301.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#endTime#,'-','')
        </isNotEmpty>

        <!--作业开始时间-->
        <isNotEmpty prepend=" and " property="startBeginEntruckingTime">
            substr(tlirl0301.BEGIN_ENTRUCKING_TIME,1,8) >= replace(#startBeginEntruckingTime#,'-','')
        </isNotEmpty>
        <!--作业结束时间-->
        <isNotEmpty prepend=" and " property="endBeginEntruckingTime">
            substr(tlirl0301.BEGIN_ENTRUCKING_TIME,1,8) <![CDATA[<=]]> replace(#endBeginEntruckingTime#,'-','')
        </isNotEmpty>


        <!--作业开始时间-->
        <isNotEmpty prepend=" and " property="startBeginEntruckingTime">
            substr(tlirl0301.COMPLETE_UNINSTALL_TIME,1,8) >= replace(#startCompleteUninstallTime#,'-','')
        </isNotEmpty>
        <!--作业结束时间-->
        <isNotEmpty prepend=" and " property="endCompleteUninstallTime">
            substr(tlirl0301.COMPLETE_UNINSTALL_TIME,1,8) <![CDATA[<=]]> replace(#endCompleteUninstallTime#,'-','')
        </isNotEmpty>


        UNION

        select tlirl0301.SEG_NO                                                    as "segNo",
        tlirl0301.UNIT_CODE                                                 as "unitCode",
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0301.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        tlirl0301.CAR_TRACE_NO                                              as "carTraceNo",
        tlirl0301.STATUS                                              as "status",
        (select PUTIN_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "putinId",
        (select PUTOUT_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1) as "putoutId",
        (select VOUCHER_NUM
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "voucherNum",
        tlirl0301.DRIVER_NAME                                               as "driverName",
        tlirl0301.TEL_NUM                                                   as "driverTel",
        tlirl0301.ID_CARD                                                   as "driverIdentity",
        tlirl0301.VEHICLE_NO                                                as "vehicleNo",
        (select PACK_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "packId",
        (select MAT_INNER_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "matInnerId",

        (select FACTORY_ORDER_NUM
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "factoryOrderNum",

        (select PUT_IN_OUT_FLAG
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "putInOutFlag",
        (select PROD_TYPE_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "prodTypeId",

        (select PROD_TYPE_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "prodTypeName",

        (select SHOPSIGN
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "shopsign",

        (select SPEC_DESC
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "specDesc",

        (select WEIGHT
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "weight",

        (select QUANTITY
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "quantity",

        (select QUANTITY
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "quantity",
        (select WAREHOUSE_CODE
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "warehouseCode",

        (select WAREHOUSE_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "warehouseName",


        (select CUSTOMER_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "customerId",

        (select CUSTOMER_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "customerName",

        (select LOCATION_ID
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "locationId",

        (select LOCATION_NAME
        from ${meliSchema}.tlirl0308 tlirl0308
        where tlirl0308.SEG_NO = tlirl0301.SEG_NO
        and tlirl0308.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0308.VEHICLE_NO=tlirl0301.VEHICLE_NO
        limit 1)                                                           as "locationName",
        tlirl0301.REC_CREATOR                                               as "recCreator",
        tlirl0301.REC_CREATOR_NAME                                          as "recCreatorName",
        tlirl0301.REC_CREATE_TIME                                           as "recCreateTime",
        tlirl0301.REC_REVISOR                                               as "recRevisor",
        tlirl0301.REC_REVISOR_NAME                                          as "recRevisorName",
        tlirl0301.REC_REVISE_TIME                                           as "recReviseTime",
        tlirl0301.ARCHIVE_FLAG                                              as "archiveFlag",
        tlirl0301.DEL_FLAG                                                  as "delFlag",
        tlirl0301.REMARK                                                    as "remark",
        tlirl0301.SYS_REMARK                                                as "sysRemark",
        tlirl0301.UUID                                                      as "uuid",
        tlirl0301.TENANT_ID                                                 as "tenantId",
        tlirl0301.STATUS                                                    as "status",
        tlirl0301.HAND_TYPE                                                    "handType",
        tlirl0301.TARGET_HAND_POINT_ID                                      as "targetHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                    as "targetHandPointName",
        tlirl0301.CURRENT_HAND_POINT_ID                                        "currentHandPointId",
        (select tlirl0304.HAND_POINT_NAME
        from ${meliSchema}.tlirl0304 tlirl0304
        where tlirl0304.SEG_NO = tlirl0301.SEG_NO
        and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
        and tlirl0304.STATUS = '30'
        and tlirl0304.DEL_FLAG = '0')                                    as "currentHandPointName",
        tlirl0301.BEGIN_ENTRUCKING_TIME                                        "beginEntruckingTime",
        tlirl0301.COMPLETE_UNINSTALL_TIME                                   as "completeUninstallTime",
        tlirl0301.FACTORY_AREA                                              as "factoryArea",
        tlirl0301.FACTORY_AREA_NAME                                              as "factoryAreaName",
        (select tlirl0407.NEXT_TATGET
        from ${meliSchema}.tlirl0407 tlirl0407
        where tlirl0407.SEG_NO = tlirl0301.SEG_NO
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00'
        and tlirl0407.FINISH_LOAD_ID = (select max(tlirl0407.FINISH_LOAD_ID)
        from ${meliSchema}.tlirl0407 tlirl0407
        where 1 = 1
        and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
        and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
        and tlirl0407.STATUS != '00')) as "nextTarget"
        from ${meliSchema}.tlirl0311 tlirl0301
        where 1 = 1
        and SEG_NO=#segNo#
        <isNotEmpty  prepend="and" property="carTraceNo">
            tlirl0301.CAR_TRACE_NO like concat('%',#carTraceNo#,'%')
        </isNotEmpty>
        <isNotEmpty  prepend="and" property="vehicleNo">
            tlirl0301.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty  prepend=" and " property="status">
            <isEqual property="status" compareValue="A0">
                tlirl0301.STATUS in ('20','30','40')
            </isEqual>
            <isNotEqual property="status" compareValue="A0">
                tlirl0301.STATUS =#status#
            </isNotEqual>
        </isNotEmpty>
        <isNotEmpty  prepend="and" property="factoryArea">
            tlirl0301.FACTORY_AREA =#factoryArea#
        </isNotEmpty>

        <isNotEmpty prepend=" and " property="startTime">
            substr(tlirl0301.REC_CREATE_TIME,1,8) >= replace(#startTime#,'-','')
        </isNotEmpty>
        <!--创建时间起始-->
        <isNotEmpty prepend=" and " property="endTime">
            substr(tlirl0301.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#endTime#,'-','')
        </isNotEmpty>

        <!--作业开始时间-->
        <isNotEmpty prepend=" and " property="startBeginEntruckingTime">
            substr(tlirl0301.BEGIN_ENTRUCKING_TIME,1,8) >= replace(#startBeginEntruckingTime#,'-','')
        </isNotEmpty>
        <!--作业结束时间-->
        <isNotEmpty prepend=" and " property="endBeginEntruckingTime">
            substr(tlirl0301.BEGIN_ENTRUCKING_TIME,1,8) <![CDATA[<=]]> replace(#endBeginEntruckingTime#,'-','')
        </isNotEmpty>


        <!--作业开始时间-->
        <isNotEmpty prepend=" and " property="startBeginEntruckingTime">
            substr(tlirl0301.COMPLETE_UNINSTALL_TIME,1,8) >= replace(#startCompleteUninstallTime#,'-','')
        </isNotEmpty>
        <!--作业结束时间-->
        <isNotEmpty prepend=" and " property="endCompleteUninstallTime">
            substr(tlirl0301.COMPLETE_UNINSTALL_TIME,1,8) <![CDATA[<=]]> replace(#endCompleteUninstallTime#,'-','')
        </isNotEmpty>

        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                recCreateTime desc
            </isEmpty>
        </dynamic>

    </select>

<!--    查询出库明细-->
    <select id="putoutQuery" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0308">
        SELECT
        SEG_NO as "segNo",  <!-- 账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        PUTIN_ID as "putinId",  <!-- 入库单号（无计划入库为空） -->
        PUTOUT_ID as "putoutId",  <!-- 出库单号 -->
        VOUCHER_NUM as "voucherNum",  <!-- 提单号（多个） -->
        DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        DRIVER_TEL as "driverTel",  <!-- 司机手机号 -->
        DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
        VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        DELIVER_TYPE as "deliverType",  <!-- 交货方式（代运、自提） -->
        DELIVER_NAME as "deliverName",  <!-- 交货方式名称 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        MAT_INNER_ID as "matInnerId",  <!-- 材料管理好（无计划入库为空） -->
        FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂资源号 -->
        PUT_IN_OUT_FLAG as "putInOutFlag",  <!-- 出入库标记（入库：1，出库：2） -->
        PROD_TYPE_ID as "prodTypeId",  <!-- 品种附属码 -->
        PROD_TYPE_NAME as "prodTypeName",  <!-- 品种附属码名称 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        SPEC_DESC as "specDesc",  <!-- 规格 -->
       sum(WEIGHT)            as "weight",  <!-- 重量 -->
       sum(QUANTITY)          as "quantity", <!-- 数量 -->
        WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        CUSTOMER_NAME as "customerName",  <!-- 客户名称 -->
        LOCATION_ID as "locationId",  <!-- 库位代码 -->
        LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        REMARK as "remark",  <!-- 备注 -->
        SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        UUID as "uuid",  <!-- uuid -->
        TENANT_ID as "tenantId", <!-- 租户ID -->
        PUTOUT_DATE  as "putoutDate", <!--出库日期-->
        FINAL_DESTINATION as "finalDestination"
        FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and PUTOUT_ID !=' '
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="idCard">
            DRIVER_IDENTITY = #idCard#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM  like concat('%',#voucherNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            DRIVER_TEL  like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutDate">
            substr(PUTOUT_DATE,1,8) = substr(replace(#putoutDate#,'-',''),1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packStr">
            PACK_ID IN
            <iterate property="packStr" open="("
                     close=")" conjunction=" , ">
                #packStr[]#
            </iterate>
        </isNotEmpty>
        GROUP BY PUTOUT_ID
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryPoutInfo" parameterClass="java.util.HashMap"  resultClass="com.baosight.imom.li.rl.dao.LIRL0308">
        SELECT SEG_NO            as "segNo",
               UNIT_CODE         as "unitCode",
               CAR_TRACE_NO      as "carTraceNo",
               PUTOUT_ID         as "putoutId",
               VOUCHER_NUM       as "voucherNum",
               DRIVER_NAME       as "driverName",
               DRIVER_TEL        as "driverTel",
               DRIVER_IDENTITY   as "driverIdentity",
               VEHICLE_NO        as "vehicleNo",
               PACK_ID           as "packId",
               sum(WEIGHT)       as "weight",
               sum(QUANTITY)     as "quantity",
               WAREHOUSE_CODE   as "warehouseCode",
               WAREHOUSE_NAME   as "warehouseName",
               PUTOUT_DATE       as "putoutDate",
               FINAL_DESTINATION as "finalDestination",
        max(CUSTOMER_ID) as "customerId",
        max(CUSTOMER_NAME) as "customerName",
        REC_CREATE_TIME as "recCreateTime"
        FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and PUTOUT_ID !=' '
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="idCard">
            DRIVER_IDENTITY = #idCard#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM  like concat('%',#voucherNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            DRIVER_TEL  like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutDate">
            substr(PUTOUT_DATE,1,8) = substr(replace(#putoutDate#,'-',''),1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packStr">
            PACK_ID IN
            <iterate property="packStr" open="("
                     close=")" conjunction=" , ">
                #packStr[]#
            </iterate>
        </isNotEmpty>
        GROUP BY PUTOUT_ID,SEG_NO  ,
            UNIT_CODE,
            CAR_TRACE_NO,
            PUTOUT_ID,
            VOUCHER_NUM,
            DRIVER_NAME,
            DRIVER_TEL,
            DRIVER_IDENTITY,
            VEHICLE_NO,
            PACK_ID,
            WAREHOUSE_CODE,
            WAREHOUSE_NAME,
            PUTOUT_DATE,
            FINAL_DESTINATION,
            REC_CREATE_TIME
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>
    </select>

    <select id="queryNotExistSignatureFlag"
            resultClass="int">
        SELECT count(*)
        FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and DEL_FLAG='0'
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutIdStr">
            PUTOUT_ID in
            <iterate property="putoutIdStr" open="("
                     close=")" conjunction=" , ">
                #putoutIdStr[]#
            </iterate>
        </isNotEmpty>
           and SIGNATURE_FLAG !='1'
    </select>

<!--查询未签收的捆包重量、数量-->
    <select id="queryNoSignatureData"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select sum(tlirl0308.QUANTITY) as "sumQuantity",sum(tlirl0308.WEIGHT) as "sumWeight"
        from meli.tlirl0308 tlirl0308
        where 1=1
        and DEL_FLAG='0'
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packStr">
            PACK_ID NOT IN
            <iterate property="packStr" open="("
                     close=")" conjunction=" , ">
                #packStr[]#
            </iterate>
        </isNotEmpty>
    </select>


    <select id="queryNoSignatureCount"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select PACK_ID AS "packId"
        from meli.tlirl0308 tlirl0308
        where 1=1
        and DEL_FLAG='0'
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packStr">
            PACK_ID NOT IN
            <iterate property="packStr" open="("
                     close=")" conjunction=" , ">
                #packStr[]#
            </iterate>
        </isNotEmpty>
    </select>

    <select id="queryAllSignatureData"
            parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select sum(tlirl0308.QUANTITY) as "sumQuantity",sum(tlirl0308.WEIGHT) as "sumWeight"
        from meli.tlirl0308 tlirl0308
        where 1=1
        and DEL_FLAG='0'
        and SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
    </select>
    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0308 (SEG_NO,  <!-- 账套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
        PUTIN_ID,  <!-- 入库单号（无计划入库为空） -->
        PUTOUT_ID,  <!-- 出库单号 -->
        VOUCHER_NUM,  <!-- 提单号（多个） -->
        DRIVER_NAME,  <!-- 司机姓名 -->
        DRIVER_TEL,  <!-- 司机手机号 -->
        DRIVER_IDENTITY,  <!-- 司机身份证号 -->
        VEHICLE_NO,  <!-- 车牌号 -->
        DELIVER_TYPE,  <!-- 交货方式（代运、自提） -->
        DELIVER_NAME,  <!-- 交货方式名称 -->
        PACK_ID,  <!-- 捆包号 -->
        MAT_INNER_ID,  <!-- 材料管理好（无计划入库为空） -->
        FACTORY_ORDER_NUM,  <!-- 钢厂资源号 -->
        PUT_IN_OUT_FLAG,  <!-- 出入库标记（入库：1，出库：2） -->
        PROD_TYPE_ID,  <!-- 品种附属码 -->
        PROD_TYPE_NAME,  <!-- 品种附属码名称 -->
        SHOPSIGN,  <!-- 牌号 -->
        SPEC_DESC,  <!-- 规格 -->
        WEIGHT,  <!-- 重量 -->
        QUANTITY,  <!-- 数量 -->
        WAREHOUSE_CODE,  <!-- 仓库代码 -->
        WAREHOUSE_NAME,  <!-- 仓库名称 -->
        CUSTOMER_ID,  <!-- 客户代码 -->
        CUSTOMER_NAME,  <!-- 客户名称 -->
        LOCATION_ID,  <!-- 库位代码 -->
        LOCATION_NAME,  <!-- 库位名称 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        SYS_REMARK,  <!-- 系统备注 -->
        UUID,  <!-- uuid -->
        TENANT_ID,  <!-- 租户ID -->
        PUTOUT_DATE,
        SIGNATURE_FLAG,  <!-- 签收标记 -->
        FINAL_DESTINATION,FINAL_STATION_LONGITUDE,FINAL_STATION_LATITUDE,DEST_SPOT_ADDR,PER_NO,PER_NAME
        )
        VALUES (#segNo#, #unitCode#, #carTraceNo#, #putinId#, #putoutId#, #voucherNum#, #driverName#, #driverTel#,
        #driverIdentity#, #vehicleNo#, #deliverType#, #deliverName#, #packId#, #matInnerId#, #factoryOrderNum#,
        #putInOutFlag#, #prodTypeId#, #prodTypeName#, #shopsign#, #specDesc#, #weight#, #quantity#, #warehouseCode#,
        #warehouseName#, #customerId#, #customerName#, #locationId#, #locationName#, #recCreator#, #recCreatorName#,
        #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#,
        #sysRemark#, #uuid#, #tenantId#,#putoutDate#, #signatureFlag#,#finalDestination#,#finalStationLongitude#,#finalStationLatitude#,
                #destSpotAddr#,#perNo#,#perName#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0308 WHERE
        UUID = #uuid#
    </delete>

    <select id="queryPCinfo" parameterClass="java.util.HashMap" resultClass="String">
        select PACK_ID as "packId"
        from meli.tlirl0308
        where 1 = 1
          and SEG_NO = #segNo#
          AND DELIVER_TYPE='20'
          and PUT_IN_OUT_FLAG='20'
          and CAR_TRACE_NO=#carTraceNo#
    </select>

    <select id="queryBLinfo" parameterClass="java.util.HashMap" resultClass="String">
        select VOUCHER_NUM as "voucherNum"
        from meli.tlirl0308
        where 1 = 1
          and SEG_NO = #segNo#
          AND DELIVER_TYPE='20'
          and PUT_IN_OUT_FLAG='20'
          and CAR_TRACE_NO=#carTraceNo#
    </select>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0308
        SET
        SEG_NO = #segNo#,   <!-- 账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        CAR_TRACE_NO = #carTraceNo#,   <!-- 车辆跟踪号 -->
        PUTIN_ID = #putinId#,   <!-- 入库单号（无计划入库为空） -->
        PUTOUT_ID = #putoutId#,   <!-- 出库单号 -->
        VOUCHER_NUM = #voucherNum#,   <!-- 提单号（多个） -->
        DRIVER_NAME = #driverName#,   <!-- 司机姓名 -->
        DRIVER_TEL = #driverTel#,   <!-- 司机手机号 -->
        DRIVER_IDENTITY = #driverIdentity#,   <!-- 司机身份证号 -->
        VEHICLE_NO = #vehicleNo#,   <!-- 车牌号 -->
        DELIVER_TYPE = #deliverType#,   <!-- 交货方式（代运、自提） -->
        DELIVER_NAME = #deliverName#,   <!-- 交货方式名称 -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        MAT_INNER_ID = #matInnerId#,   <!-- 材料管理好（无计划入库为空） -->
        FACTORY_ORDER_NUM = #factoryOrderNum#,   <!-- 钢厂资源号 -->
        PUT_IN_OUT_FLAG = #putInOutFlag#,   <!-- 出入库标记（入库：1，出库：2） -->
        PROD_TYPE_ID = #prodTypeId#,   <!-- 品种附属码 -->
        PROD_TYPE_NAME = #prodTypeName#,   <!-- 品种附属码名称 -->
        SHOPSIGN = #shopsign#,   <!-- 牌号 -->
        SPEC_DESC = #specDesc#,   <!-- 规格 -->
        WEIGHT = #weight#,   <!-- 重量 -->
        QUANTITY = #quantity#,   <!-- 数量 -->
        WAREHOUSE_CODE = #warehouseCode#,   <!-- 仓库代码 -->
        WAREHOUSE_NAME = #warehouseName#,   <!-- 仓库名称 -->
        CUSTOMER_ID = #customerId#,   <!-- 客户代码 -->
        CUSTOMER_NAME = #customerName#,   <!-- 客户名称 -->
        LOCATION_ID = #locationId#,   <!-- 库位代码 -->
        LOCATION_NAME = #locationName#,   <!-- 库位名称 -->
        SIGNATURE_FLAG	= #signatureFlag#,   <!-- 签收标记 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#,   <!-- 备注 -->
        SYS_REMARK = #sysRemark#,   <!-- 系统备注 -->
        TENANT_ID = #tenantId#  <!-- 租户ID -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateInfo">
        UPDATE ${meliSchema}.tlirl0308
        SET
        PUTIN_ID = #putinId#,   <!-- 入库单号（无计划入库为空） -->
        MAT_INNER_ID = #matInnerId#,   <!-- 材料管理好（无计划入库为空） -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        FACTORY_ORDER_NUM=#factoryOrderNum#,
        PROD_TYPE_ID=#prodTypeId#,
        PROD_TYPE_NAME=#prodTypeName#,
        SHOPSIGN=#shopsign#,
        SPEC_DESC=#specDesc#,
        WEIGHT=#weight#,
        QUANTITY=#quantity#,
        CUSTOMER_ID=#customerId#,
        CUSTOMER_NAME=#customerName#,
        WAREHOUSE_CODE=#warehouseCode#,
        WAREHOUSE_NAME=#warehouseName#,
        MAT_INNER_ID=#matInnerId#
        WHERE
        CAR_TRACE_NO = #carTraceNo#
        and
        UUID=#uuid#
        and
        PACK_ID=#packId#
    </update>

    <select id="queryPutoutId" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        PUTOUT_ID as "putoutId"  <!-- 出库单号 -->
        FROM ${meliSchema}.tlirl0308 WHERE 1=1
        and DEL_FLAG='0'
        <include refid="condition"/>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <update id="updateBycarTraceNo">
        UPDATE ${meliSchema}.tlirl0308
        SET
        SIGNATURE_FLAG	= #signatureFlag#,   <!-- 签收标记 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
        WHERE
        SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutIdStr">
            PUTOUT_ID in
            <iterate property="putoutIdStr" open="("
                     close=")" conjunction=" , ">
                #putoutIdStr[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutId">
            PUTOUT_ID = #putoutId#
        </isNotEmpty>
    </update>


    <update id="updateByPutoutByPackInfo">
        UPDATE ${meliSchema}.tlirl0308
        SET
        SIGNATURE_FLAG	= #signatureFlag#,
        SIGN_OFF_TIME	= #signOffTime#,   <!-- 签收时间 -->
        FINAL_STATION_LONGITUDE	= #finalStationLongitude#,   <!-- 终到站经度 -->
        FINAL_STATION_LATITUDE	= #finalStationLatitude#,   <!-- 终到站纬度 -->
        SIGNING_LOCATION_LONGITUDE	= #signingLocationLongitude#,   <!-- 签收地经度 -->
        SIGNING_LOCATION_LATITUDE	= #signingLocationLatitude#,   <!-- 签收地纬度-->
        LONGITUDE_LATITUDE_CHECK	= #longitudeLatitudeCheck#,   <!-- 经纬度校验-->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        SIGNING_ADDRESS =#signingAddress#
        WHERE
        SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="putoutIdStr">
            PUTOUT_ID in
            <iterate property="putoutIdStr" open="("
                     close=")" conjunction=" , ">
                #putoutIdStr[]#
            </iterate>
        </isNotEmpty>
         <isNotEmpty prepend=" AND " property="packIdStr">
            PACK_ID in
            <iterate property="packIdStr" open="("
                     close=")" conjunction=" , ">
                #packIdStr[]#
            </iterate>
        </isNotEmpty>
        AND CAR_TRACE_NO = #carTraceNo#
    </update>

    <select id="queryLoadingPerformance" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.SEG_NO as "segNo",  <!-- 账套 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        a.CAR_TRACE_NO as "carTraceNo",  <!-- 车辆跟踪号 -->
        a.PUTIN_ID as "putinId",  <!-- 入库单号（无计划入库为空） -->
        a.PUTOUT_ID as "putoutId",  <!-- 出库单号 -->
        a.VOUCHER_NUM as "voucherNum",  <!-- 提单号（多个） -->
        a.DRIVER_NAME as "driverName",  <!-- 司机姓名 -->
        a.DRIVER_TEL as "driverTel",  <!-- 司机手机号 -->
        a.DRIVER_IDENTITY as "driverIdentity",  <!-- 司机身份证号 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.DELIVER_TYPE as "deliverType",  <!-- 交货方式（代运、自提） -->
        a.DELIVER_NAME as "deliverName",  <!-- 交货方式名称 -->
        a.PACK_ID as "packId",  <!-- 捆包号 -->
        a.MAT_INNER_ID as "matInnerId",  <!-- 材料管理好（无计划入库为空） -->
        a.FACTORY_ORDER_NUM as "factoryOrderNum",  <!-- 钢厂资源号 -->
        a.PUT_IN_OUT_FLAG as "putInOutFlag",  <!-- 出入库标记（入库：1，出库：2） -->
        a.PROD_TYPE_ID as "prodTypeId",  <!-- 品种附属码 -->
        a.PROD_TYPE_NAME as "prodTypeName",  <!-- 品种附属码名称 -->
        a.SHOPSIGN as "shopsign",  <!-- 牌号 -->
        a.SPEC_DESC as "specDesc",  <!-- 规格 -->
        a.WEIGHT as "weight",  <!-- 重量 -->
        a.QUANTITY as "quantity",  <!-- 数量 -->
        a.WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
        a.WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
        a.CUSTOMER_ID as "customerId",  <!-- 客户代码 -->
        (select tlirl0302.CUSTOMER_NAME
        from meli.tlirl0302 tlirl0302
        where tlirl0302.SEG_NO = a.SEG_NO
        and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0302.VEHICLE_NO = a.VEHICLE_NO
        limit 1)                                                                       as "customerName", <!-- 客户名称 -->
        a.LOCATION_ID as "locationId",  <!-- 库位代码 -->
        a.LOCATION_NAME as "locationName",  <!-- 库位名称 -->
        a.PUTOUT_DATE  as "putoutDate", <!--出库日期-->
        a.SIGNATURE_FLAG	as "signatureFlag",  <!-- 签收标记 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.FINAL_DESTINATION as "finalDestination",
        a.VARIANCE_DETAILS as "varianceDetails",/*差异明细*/
        a.PER_NAME AS "perName",
        a.PER_NO AS "perNo"
        FROM ${meliSchema}.tlirl0308 a WHERE 1=1
        and DEL_FLAG='0'
        AND SEG_NO = #segNo#
        AND CAR_TRACE_NO = #carTraceNo#
        <isNotEmpty prepend=" AND " property="tab">
            PUTOUT_ID != ''
            AND PUTOUT_ID IS NOT NULL
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATOR asc
            </isEmpty>
        </dynamic>

    </select>


    <update id="updatePrintCount">
        UPDATE ${meliSchema}.tlirl0308
        SET
        PRINT_COUNT = #printCount#
        WHERE
        SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="putoutId">
            PUTOUT_ID in
            <iterate property="putoutId" open="("
                     close=")" conjunction=" , ">
                #putoutId[]#
            </iterate>
        </isNotEmpty>
        AND DEL_FLAG = 0
    </update>


    <update id="updateVarianceDetails">
        UPDATE ${meliSchema}.tlirl0308
        SET
        VARIANCE_DETAILS = #varianceDetails#  <!-- 入库单号（无计划入库为空） -->
        WHERE
        CAR_TRACE_NO = #carTraceNo#
        <isNotEmpty prepend=" AND " property="putoutIdS">
            PUTOUT_ID !=' '
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="putoutIdH">
            PUTOUT_ID =' '
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="differentPackIds">
            PACK_ID in
            <iterate property="differentPackIds" open="("
                     close=")" conjunction=" , ">
                #differentPackIds[]#
            </iterate>
        </isNotEmpty>
    </update>
</sqlMap>