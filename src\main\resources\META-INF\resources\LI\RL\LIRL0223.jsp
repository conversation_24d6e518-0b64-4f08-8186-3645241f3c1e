<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
<%--            <EF:EFInput ename="inqu_status-0-carTraceNo" cname="车牌跟踪号" colWidth="3" placeholder="模糊查询"/>--%>
<%--            <EF:EFInput ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3" placeholder="模糊查询"/>--%>
            <EF:EFSelect ename="inqu_status-0-putInOutFlag" cname="出入库标记" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFOption label="入库" value="10"/>
                <EF:EFOption label="出库" value="20"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-perName" cname="行车工姓名" colWidth="3" placeholder="模糊查询"/>
        </div>
<%--        <div class="row">--%>
<%--            <EF:EFDatePicker ename="inqu_status-0-statrBeginEntruckingTime" cname="作业开始时间起始"  colWidth="3" />--%>
<%--            <EF:EFDatePicker ename="inqu_status-0-endBeginEntruckingTime" cname="作业开始时间截止"  colWidth="3" />--%>
<%--            <EF:EFDatePicker ename="inqu_status-0-startCompleteUninstallTime" cname="作业结束时间起始"  colWidth="3" />--%>
<%--            <EF:EFDatePicker ename="inqu_status-0-endCompleteUninstallTime" cname="作业结束时间截止"  colWidth="3" />--%>
<%--        </div>--%>

        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-handPointId" cname="装卸点编码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="handPointInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="装卸点查询">
            </EF:EFPopupInput>
            <EF:EFDatePicker ename="inqu_status-0-handPointName" cname="装卸点名称"  colWidth="3" disabled="true" />
            <EF:EFDatePicker ename="inqu_status-0-startTime" cname="装卸/卸货开始日期"  colWidth="3" />
            <EF:EFDatePicker ename="inqu_status-0-endTime" cname="装卸/卸货结束日期"  colWidth="3" />
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoDraw="override"
                   autoBind="false" isFloat="true" personal="true" sumType="all">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="totalQuantity" cname="装货/卸货总数量" align="center" enable="false"/>
            <EF:EFColumn ename="totalWeight" cname="装货/卸货总重量" align="center" enable="false"/>
            <EF:EFComboColumn ename="putInOutFlag" cname="装卸货标记" align="center" sort="false" >
                <EF:EFOption label="卸货" value="10"/>
                <EF:EFOption label="装货" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="perName" cname="行车工姓名" align="center" enable="false"/>
            <EF:EFColumn ename="handPointId" cname="装卸点编码" align="center" enable="false"/>
            <EF:EFColumn ename="handPointName" cname="装卸点名称" align="center" enable="false"/>
<%--            <EF:EFColumn ename="recCreateTime" cname="装卸/卸货日期" align="center" enable="false"/>--%>
<%--            <EF:EFComboColumn ename="status" cname="车辆跟踪状态"  align="center"--%>
<%--                              enable="false" width="120">--%>
<%--                <EF:EFCodeOption codeName="P008"/>--%>
<%--            </EF:EFComboColumn>--%>
<%--            <EF:EFColumn ename="driverName" cname="司机姓名" align="center" enable="false"/>--%>
<%--            <EF:EFColumn ename="driverTel" cname="司机手机号" align="center" enable="false"/>--%>
<%--            <EF:EFColumn ename="driverIdentity" cname="司机身份证号" align="center" enable="false"  width="150"/>--%>
<%--            <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" enable="false" />--%>
<%--            <EF:EFColumn ename="perName" cname="行车工姓名" align="center" enable="false" />--%>
<%--            <EF:EFColumn ename="putinId" cname="入库单号" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="putoutId" cname="出库单号" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="voucherNum" cname="提单号" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="packId" cname="捆包号" align="center" enable="false"  width="150" sort="false"/>--%>
<%--            <EF:EFColumn ename="matInnerId" cname="材料管理号" align="center" enable="false"  width="150" sort="false"/>--%>
<%--            <EF:EFColumn ename="factoryOrderNum" cname="钢厂资源号" align="center" enable="false" sort="false"/>--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="putInOutFlag" cname="出入库标记" align="center" enable="false"/>&ndash;%&gt;--%>
<%--
<%--            <EF:EFColumn ename="prodTypeId" cname="品种附属码" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="prodTypeName" cname="品种附属码名称" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="shopsign" cname="牌号" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="specDesc" cname="规格" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="weight" cname="重量" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="quantity" cname="数量" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="warehouseName" cname="仓库名称" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="customerId" cname="客户代码" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="customerName" cname="客户名称" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="locationId" cname="库位代码" align="center" enable="false" sort="false"/>--%>
<%--            <EF:EFColumn ename="locationName" cname="库位名称" align="center" enable="false" sort="false"/>--%>
<%--&lt;%&ndash;            <EF:EFComboColumn ename="handType" cname="装卸类型"  align="center" hidden="true"&ndash;%&gt;--%>
<%--&lt;%&ndash;                              enable="false" width="120">&ndash;%&gt;--%>
<%--&lt;%&ndash;                <EF:EFCodeOption codeName="P007"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;            </EF:EFComboColumn>&ndash;%&gt;--%>
<%--            <EF:EFColumn ename="handPointName" cname="目标装卸点名称" align="center" enable="false"/>--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="currentHandPointId" cname="当前装卸点" align="center" enable="false"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="currentHandPointName" cname="当前装卸点名称" align="center" enable="false"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="beginEntruckingTime" cname="作业开始时间" align="center" enable="false"&ndash;%&gt;--%>
<%--&lt;%&ndash;                         editType="datetime" displayType="datetime"  width="150"&ndash;%&gt;--%>
<%--&lt;%&ndash;                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"&ndash;%&gt;--%>
<%--&lt;%&ndash;            />&ndash;%&gt;--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="completeUninstallTime" cname="作业结束时间" align="center" enable="false"&ndash;%&gt;--%>
<%--&lt;%&ndash;                         editType="datetime" displayType="datetime"  width="150"&ndash;%&gt;--%>
<%--&lt;%&ndash;                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy/MM/dd HH:mm:ss"&ndash;%&gt;--%>
<%--&lt;%&ndash;            />&ndash;%&gt;--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="factoryArea" cname="厂区编码" align="center" enable="false"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;            <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" enable="false"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;            <EF:EFComboColumn ename="nextTarget" cname="下一目标" align="center"  >&ndash;%&gt;--%>
<%--&lt;%&ndash;                <EF:EFOption label="下一装卸点" value="10"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;                <EF:EFOption label="离厂" value="20"/>&ndash;%&gt;--%>
<%--&lt;%&ndash;            </EF:EFComboColumn>&ndash;%&gt;--%>
<%--            <EF:EFColumn ename="remark" cname="备注" align="center" enable="false"/>--%>
<%--            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"--%>
<%--                         readonly="true" align="center"/>--%>
<%--            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"--%>
<%--                         readonly="true" align="center"/>--%>
<%--            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"--%>
<%--                         readonly="true" align="center"/>--%>
<%--            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"--%>
<%--                         readonly="true" align="center"/>--%>
<%--            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"--%>
<%--                         readonly="true" align="center"/>--%>
<%--            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"--%>
<%--                         readonly="true" align="center"/>--%>
<%--            <EF:EFColumn ename="uuid" cname="ID" align="center" hidden="true"/>--%>
<%--            <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>--%>
<%--            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>--%>
        </EF:EFGrid>
    </EF:EFRegion>

    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0001" id="userNum2" width="90%" height="60%"></EF:EFWindow>
    <%--厂内装卸点弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIRL0003" id="handPointInfo" width="90%" height="60%"/>
</EF:EFPage>
