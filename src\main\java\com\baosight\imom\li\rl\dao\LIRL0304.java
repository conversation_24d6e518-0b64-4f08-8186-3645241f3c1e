/**
* Generate time : 2024-08-26 9:06:18
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0304 装卸点维护主表
* 
*/
public class LIRL0304 extends DaoEPBase {
        public static final String QUERY = "LIRL0304.query";
        public static final String QUERY_HAND_POINT_NAME = "LIRL0304.queryHandPointName";
        public static final String QUERY_HAND_POINT_INFO = "LIRL0304.queryHandPointInfo";
        public static final String QUERY_HAND_POINT_INFO1 = "LIRL0304.queryHandPointInfo1";
        public static final String QUERY_HAND_POINT_INFO_ALL = "LIRL0304.queryHandPointInfoAll";
        public static final String QUERY_NO_FACTORY_HAND_POINT = "LIRL0304.queryNoFactoryHandPoint";
        public static final String COUNT = "LIRL0304.count";
        public static final String INSERT = "LIRL0304.insert";
        public static final String UPDATE = "LIRL0304.update";
        public static final String UPDATE_ENABLE_DATA = "LIRL0304.updateEnableData";
        public static final String DELETE = "LIRL0304.delete";
        public static final String QUERY_VEHICLE_NUMER = "LIRL0304.queryVehicleNumer";
        public static final String QUERY_WAIT_CALL = "LIRL0304.queryWaitCall";
        public static final String QUERY_WAIT_ALL_CALL = "LIRL0304.queryWaitAllCall";
        public static final String QUERY_WAIT_ALL_CALL_HAND_POINT_ID = "LIRL0304.queryWaitAllCallHandPointId";
        public static final String QUERY_OTHER_HAND_POINT = "LIRL0402.queryOtherHandPoint";
        public static final String QUERY_WAIT_ALL_CALL_HAND_ID = "LIRL0304.queryWaitAllCallHandId";
        public static final String QUERY_ALL_LADING_INFO = "LIRL0304.queryAllLadingInfo";
        private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String segName = " ";		/* 业务单元简称*/
                private String handPointId = " ";		/* 装卸点代码*/
                private String handPointName = " ";		/* 装卸点名称*/
                private String loadingChannelId = " ";		/* 装卸点通道代码*/
                private String loadingChannelName = " ";		/* 装卸点通道名称*/
                private String status = " ";		/* 装卸点状态*/
                private String handOrder = " ";		/* 装卸顺序*/
                private String carTraceNo = " ";		/* 车辆跟踪号*/
                private String orderNumber = " ";		/* 顺序号*/
                private String vehicleNumer = " ";		/* 车辆容纳数*/
                private String loadFlag = " ";		/* 装货标记*/
                private String unloadFlag = " ";		/* 卸货标记*/
                private String factoryArea = " ";		/* 厂区*/
                private String factoryAreaName = " ";		/* 厂区名称*/
                private String closeReason = " ";		/* 停用原因*/
                private String expectedRecoveryTime = " ";		/* 预计恢复时间*/
                private String standardCapacity = " ";		/* 标准容量（吨）*/
                private String seqNum = " ";		/* 系统顺序号*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String businessType = " ";		/* 租户ID*/
                private String callCount = " ";		/* 叫号次数*/
                private String factoryBuilding = " ";		/* 厂房代码*/
                private String factoryBuildingName = " ";		/* 厂房名称*/
                private String craneId = " ";		/* 行车编码*/
                private String craneName = " ";		/* 行车名称*/
                private String xInitialPoint = null;        /* X轴起始点*/
                private String xDestination = null;        /* X轴终到点*/
                private String yInitialPoint = null;        /* Y轴起始点*/
                private String yDestination = null;        /* Y轴终到点*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointId");
        eiColumn.setDescName("装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointName");
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingChannelId");
        eiColumn.setDescName("装卸点通道代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingChannelName");
        eiColumn.setDescName("装卸点通道名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("装卸点状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handOrder");
        eiColumn.setDescName("装卸顺序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderNumber");
        eiColumn.setDescName("顺序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNumer");
        eiColumn.setDescName("车辆容纳数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadFlag");
        eiColumn.setDescName("装货标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unloadFlag");
        eiColumn.setDescName("卸货标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("closeReason");
        eiColumn.setDescName("停用原因");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("expectedRecoveryTime");
        eiColumn.setDescName("预计恢复时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("standardCapacity");
        eiColumn.setDescName("标准容量（吨）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("seqNum");
        eiColumn.setDescName("系统顺序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("businessType");
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callCount");
        eiColumn.setDescName("叫号次数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneId");
        eiColumn.setDescName("行车编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneName");
        eiColumn.setDescName("行车编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("xInitialPoint");
        eiColumn.setDescName("X轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("xDestination");
        eiColumn.setDescName("X轴终到点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("yInitialPoint");
        eiColumn.setDescName("Y轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("yDestination");
        eiColumn.setDescName("Y轴终到点");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0304() {
initMetaData();
}

        public String getFactoryBuilding() {
                return factoryBuilding;
        }

        public void setFactoryBuilding(String factoryBuilding) {
                this.factoryBuilding = factoryBuilding;
        }

        public String getFactoryBuildingName() {
                return factoryBuildingName;
        }

        public void setFactoryBuildingName(String factoryBuildingName) {
                this.factoryBuildingName = factoryBuildingName;
        }

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }

        public String getSegName() {
                return segName;
        }

        public void setSegName(String segName) {
                this.segName = segName;
        }

        /**
        * get the handPointId - 装卸点代码
        * @return the handPointId
        */
        public String getHandPointId() {
        return this.handPointId;
        }

        /**
        * set the handPointId - 装卸点代码
        */
        public void setHandPointId(String handPointId) {
        this.handPointId = handPointId;
        }
        /**
        * get the handPointName - 装卸点名称
        * @return the handPointName
        */
        public String getHandPointName() {
        return this.handPointName;
        }

        /**
        * set the handPointName - 装卸点名称
        */
        public void setHandPointName(String handPointName) {
        this.handPointName = handPointName;
        }

        /**
         * get the loadingChannelId - 装卸点通道代码
         * @return the loadingChannelId
         */
        public String getLoadingChannelId() {
                return this.loadingChannelId;
        }

        /**
         * set the loadingChannelId - 装卸点通道代码
         */
        public void setLoadingChannelId(String loadingChannelId) {
                this.loadingChannelId = loadingChannelId;
        }
        /**
         * get the loadingChannelName - 装卸点通道名称
         * @return the loadingChannelName
         */
        public String getLoadingChannelName() {
                return this.loadingChannelName;
        }

        /**
         * set the loadingChannelName - 装卸点通道名称
         */
        public void setLoadingChannelName(String loadingChannelName) {
                this.loadingChannelName = loadingChannelName;
        }


        public String getStatus() {
                return status;
        }

        public void setStatus(String status) {
                this.status = status;
        }

        /**
        * get the handOrder - 装卸顺序
        * @return the handOrder
        */
        public String getHandOrder() {
        return this.handOrder;
        }

        /**
        * set the handOrder - 装卸顺序
        */
        public void setHandOrder(String handOrder) {
        this.handOrder = handOrder;
        }
        /**
        * get the carTraceNo - 车辆跟踪号
        * @return the carTraceNo
        */
        public String getCarTraceNo() {
        return this.carTraceNo;
        }

        /**
        * set the carTraceNo - 车辆跟踪号
        */
        public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
        }
        /**
        * get the orderNumber - 顺序号
        * @return the orderNumber
        */
        public String getOrderNumber() {
        return this.orderNumber;
        }

        /**
        * set the orderNumber - 顺序号
        */
        public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
        }
        /**
        * get the vehicleNumer - 车辆容纳数
        * @return the vehicleNumer
        */
        public String getVehicleNumer() {
        return this.vehicleNumer;
        }

        /**
        * set the vehicleNumer - 车辆容纳数
        */
        public void setVehicleNumer(String vehicleNumer) {
        this.vehicleNumer = vehicleNumer;
        }
        /**
        * get the loadFlag - 装货标记
        * @return the loadFlag
        */
        public String getLoadFlag() {
        return this.loadFlag;
        }

        /**
        * set the loadFlag - 装货标记
        */
        public void setLoadFlag(String loadFlag) {
        this.loadFlag = loadFlag;
        }
        /**
        * get the unloadFlag - 卸货标记
        * @return the unloadFlag
        */
        public String getUnloadFlag() {
        return this.unloadFlag;
        }

        /**
        * set the unloadFlag - 卸货标记
        */
        public void setUnloadFlag(String unloadFlag) {
        this.unloadFlag = unloadFlag;
        }
        /**
        * get the factoryArea - 厂区
        * @return the factoryArea
        */
        public String getFactoryArea() {
        return this.factoryArea;
        }

        /**
        * set the factoryArea - 厂区
        */
        public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
        }
        /**
        * get the closeReason - 停用原因
        * @return the closeReason
        */
        public String getCloseReason() {
        return this.closeReason;
        }

        /**
        * set the closeReason - 停用原因
        */
        public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
        }
        /**
        * get the expectedRecoveryTime - 预计恢复时间
        * @return the expectedRecoveryTime
        */
        public String getExpectedRecoveryTime() {
        return this.expectedRecoveryTime;
        }

        /**
        * set the expectedRecoveryTime - 预计恢复时间
        */
        public void setExpectedRecoveryTime(String expectedRecoveryTime) {
        this.expectedRecoveryTime = expectedRecoveryTime;
        }
        /**
        * get the standardCapacity - 标准容量（吨）
        * @return the standardCapacity
        */
        public String getStandardCapacity() {
        return this.standardCapacity;
        }

        /**
        * set the standardCapacity - 标准容量（吨）
        */
        public void setStandardCapacity(String standardCapacity) {
        this.standardCapacity = standardCapacity;
        }
        /**
        * get the seqNum - 系统顺序号
        * @return the seqNum
        */
        public String getSeqNum() {
        return this.seqNum;
        }

        /**
        * set the seqNum - 系统顺序号
        */
        public void setSeqNum(String seqNum) {
        this.seqNum = seqNum;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }

        public String getBusinessType() {
                return businessType;
        }

        public void setBusinessType(String businessType) {
                this.businessType = businessType;
        }

        public String getFactoryAreaName() {
                return factoryAreaName;
        }

        public void setFactoryAreaName(String factoryAreaName) {
                this.factoryAreaName = factoryAreaName;
        }

        public String getCallCount() {
                return callCount;
        }

        public void setCallCount(String callCount) {
                this.callCount = callCount;
        }

        public String getCraneId() {
                return craneId;
        }

        public void setCraneId(String craneId) {
                this.craneId = craneId;
        }

        public String getCraneName() {
                return craneName;
        }

        public void setCraneName(String craneName) {
                this.craneName = craneName;
        }

        public String getxInitialPoint() {
                return xInitialPoint;
        }

        public void setxInitialPoint(String xInitialPoint) {
                this.xInitialPoint = xInitialPoint;
        }

        public String getxDestination() {
                return xDestination;
        }

        public void setxDestination(String xDestination) {
                this.xDestination = xDestination;
        }

        public String getyInitialPoint() {
                return yInitialPoint;
        }

        public void setyInitialPoint(String yInitialPoint) {
                this.yInitialPoint = yInitialPoint;
        }

        public String getyDestination() {
                return yDestination;
        }

        public void setyDestination(String yDestination) {
                this.yDestination = yDestination;
        }

        /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointId")), handPointId));
                setHandPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointName")), handPointName));
                setLoadingChannelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingChannelId")), loadingChannelId));
                setLoadingChannelName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingChannelName")), loadingChannelName));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setHandOrder(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handOrder")), handOrder));
                setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
                setOrderNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderNumber")), orderNumber));
                setVehicleNumer(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNumer")), vehicleNumer));
                setLoadFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadFlag")), loadFlag));
                setUnloadFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unloadFlag")), unloadFlag));
                setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
                setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
                setCloseReason(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("closeReason")), closeReason));
                setExpectedRecoveryTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("expectedRecoveryTime")), expectedRecoveryTime));
                setStandardCapacity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("standardCapacity")), standardCapacity));
                setSeqNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("seqNum")), seqNum));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessType")), businessType));
                setCallCount(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("callCount")), callCount));
                setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
                setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
                setCraneId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneId")), craneId));
                setCraneName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneName")), craneName));
        setxInitialPoint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("xInitialPoint")), xInitialPoint));
        setxDestination(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("xDestination")), xDestination));
        setyInitialPoint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("yInitialPoint")), yInitialPoint));
        setyDestination(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("yDestination")), yDestination));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("handPointId",StringUtils.toString(handPointId, eiMetadata.getMeta("handPointId")));
                map.put("handPointName",StringUtils.toString(handPointName, eiMetadata.getMeta("handPointName")));
                map.put("loadingChannelId",StringUtils.toString(loadingChannelId, eiMetadata.getMeta("loadingChannelId")));
                map.put("loadingChannelName",StringUtils.toString(loadingChannelName, eiMetadata.getMeta("loadingChannelName")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("handOrder",StringUtils.toString(handOrder, eiMetadata.getMeta("handOrder")));
                map.put("carTraceNo",StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
                map.put("orderNumber",StringUtils.toString(orderNumber, eiMetadata.getMeta("orderNumber")));
                map.put("vehicleNumer",StringUtils.toString(vehicleNumer, eiMetadata.getMeta("vehicleNumer")));
                map.put("loadFlag",StringUtils.toString(loadFlag, eiMetadata.getMeta("loadFlag")));
                map.put("unloadFlag",StringUtils.toString(unloadFlag, eiMetadata.getMeta("unloadFlag")));
                map.put("factoryArea",StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
                map.put("factoryAreaName",StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
                map.put("closeReason",StringUtils.toString(closeReason, eiMetadata.getMeta("closeReason")));
                map.put("expectedRecoveryTime",StringUtils.toString(expectedRecoveryTime, eiMetadata.getMeta("expectedRecoveryTime")));
                map.put("standardCapacity",StringUtils.toString(standardCapacity, eiMetadata.getMeta("standardCapacity")));
                map.put("seqNum",StringUtils.toString(seqNum, eiMetadata.getMeta("seqNum")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("businessType",StringUtils.toString(businessType, eiMetadata.getMeta("businessType")));
                map.put("callCount",StringUtils.toString(callCount, eiMetadata.getMeta("callCount")));
                map.put("factoryBuilding",StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
                map.put("factoryBuildingName",StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));
                map.put("craneId",StringUtils.toString(craneId, eiMetadata.getMeta("craneId")));
                map.put("craneName",StringUtils.toString(craneName, eiMetadata.getMeta("craneName")));
                map.put("xInitialPoint", StringUtils.toString(xInitialPoint, eiMetadata.getMeta("xInitialPoint")));
                map.put("xDestination", StringUtils.toString(xDestination, eiMetadata.getMeta("xDestination")));
                map.put("yInitialPoint", StringUtils.toString(yInitialPoint, eiMetadata.getMeta("yInitialPoint")));
                map.put("yDestination", StringUtils.toString(yDestination, eiMetadata.getMeta("yDestination")));

return map;

}
}