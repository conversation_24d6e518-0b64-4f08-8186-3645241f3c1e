/**
* Generate time : 2024-10-14 10:47:54
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0801;

import java.util.Map;

/**
* Tlids0801
* 
*/
public class LIDS0801 extends Tlids0801 {
        public static final String QUERY = "LIDS0801.query";
        public static final String COUNT = "LIDS0801.count";
        public static final String COUNT_UUID = "LIDS0801.count_uuid";
        public static final String INSERT = "LIDS0801.insert";
        public static final String UPDATE = "LIDS0801.update";
        public static final String DELETE = "LIDS0801.delete";
        public static final String QUERY_BY_XY = "LIDS0801.queryByXY";
        public static final String UPDATE_INVENTORY_MOULD_INFO = "LIDS0801.updateInventoryMouldInfo";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0801() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}