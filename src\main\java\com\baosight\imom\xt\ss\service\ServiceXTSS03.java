package com.baosight.imom.xt.ss.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.xt.ss.domain.XTSS03;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20240820
 * 特定业务单元管理
 */
public class ServiceXTSS03 extends ServiceBase {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceXTSS03.class);


    /**
     * 页面初始化加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new XTSS03().eiMetadata);

        return inInfo;
    }

    /**
     * 查询 - 业务单元查询
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, XTSS03.QUERY, new XTSS03());
    }


    /**
     * 新增特定业务账套关系
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
        for (HashMap hashmap : listHashMap) {
            hashmap.put("status", 20);//状态
            hashmap.put("delFlag", 0);//记录删除标记

            hashmap.put("remark", MapUtils.getString(hashmap, "remark", "").trim());//备注
            hashmap.put("id", StrUtil.getUUID());//UUID
            hashmap.put("recCreate", UserSession.getUserId());//创建人
            hashmap.put("recCreateTime", DateUtil.curDateTimeStr14());//创建人时间
            hashmap.put("recRevise", UserSession.getUserId());//修改人
            hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
        }
        inInfo = super.insert(inInfo, XTSS03.INSERT);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);

        return inInfo;

    }

    /**
     * 更新特定业务账套关系
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo update(EiInfo inInfo) {

        List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
        for (HashMap hashmap : listHashMap) {

            //后台查询状态判断
            List<XTSS03> xtss03List = dao.query(XTSS03.QUERY, hashmap);
            if(CollectionUtils.isNotEmpty(xtss03List)){
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }else{
                String massage = MessageCodeConstant.errorMessage.MSG_ERROR_NO_DATE_UPDATE;
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            inInfo = super.update(inInfo, XTSS03.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        }
        return inInfo;

    }
    @Override
    public EiInfo delete(EiInfo inInfo) {
        return inInfo;
    }
}
