$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfoMainQuery",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });

    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务单元查询"
                            });
                        }
                    }
                },
                {
                    field: "equipmentName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "设备名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "equipmentInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "设备查询"
                            });
                        }
                    }
                },
                {
                    field: "deviceName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "分部设备",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "deviceInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "分部设备查询"
                            });
                        }
                    }
                }
            ],
            //点击最左侧新增按钮时触发的事件
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["spotCheckStatus"] = "10";
                    item["spotCheckStandardId"] = " ";
                    item["apprStatus"] = "";
                    item["processInstanceId"] = "";
                    if (IPLAT.isBlankString(item["unitCode"])) {
                        item["unitCode"] = unitInfo.unitCode;
                        item["segNo"] = unitInfo.segNo;
                    }
                });
            },
            beforeEdit: function (e) {
                if (e.model.spotCheckStatus !== "10") {
                    e.preventDefault();
                }
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {
                    // 修改时unitCode(业务单元代码)和segNo(系统账套)不可修改
                    if (e.field === "unitCode") {
                        e.preventDefault();
                    }
                }
            },
            afterEdit: function (e) {
                // 点检性质自动赋值实施方
                if (e.field === "spotCheckNature") {
                    resultGrid.setCellValue(e.row, "spotCheckImplemente", e.model[e.field]);
                }
            },
            loadComplete: function (grid) {
                // 定义BizModel
                BizModel = kendo.data.Model.define({
                    id: grid.dataSource.options.schema.model.id,
                    fields: grid.dataSource.options.schema.model.fields
                });
                /*粘粘版导入*/
                $("#CLIPBOARD").on("click", function (e) {
                    var content = "";
                    if (IPLAT.Browser.isIE) {
                        content = window.clipboardData.getData("Text");
                        postHandle(content);
                    } else {
                        clipWindow.center().open();
                        handleFun = postHandle;
                    }
                });
                // 查看流程图
                $("#FLOWCHART").on("click", function (e) {
                    IMOMUtil.handleFlowchartClick(resultGrid);
                });
                //修改
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM02", "update", true, null, null, false);
                });
                // 提交按钮
                $("#SUBMIT").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM02", "submit", true, null, null, false);
                });
                // 取消提交
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM02", "cancel", true, null, null, false);
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo01",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                resultGrid.setCellValue(rowNums, "unitCode", rows[0].unitCode);
                resultGrid.setCellValue(rowNums, "segNo", rows[0].segNo);
                resultGrid.setCellValue(rowNums, "eArchivesNo", "");
                resultGrid.setCellValue(rowNums, "equipmentName", "");
                resultGrid.setCellValue(rowNums, "deviceCode", "");
                resultGrid.setCellValue(rowNums, "deviceName", "");
                resultGrid.refresh();
            }
        }
    });
    // 列表设备信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfo",
        _open: function (e, iframejQuery) {
            let a = editorModel.unitCode;
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-unitCode").val(editorModel.unitCode);
            iframejQuery("#inqu_status-0-segNo").val(editorModel.segNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "eArchivesNo", rows[0].eArchivesNo);
                resultGrid.setCellValue(editorModel, "equipmentName", rows[0].equipmentName);
                resultGrid.setCellValue(editorModel, "deviceCode", "");
                resultGrid.setCellValue(editorModel, "deviceName", "");
            }
        }
    });
    // 列表分部设备信息弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfo",
        _open: function (e, iframejQuery) {
            let a = editorModel.eArchivesNo;
            if (a == null || IPLAT.isBlankString(a)) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return false;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(editorModel.eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "deviceCode", rows[0].deviceCode);
                resultGrid.setCellValue(editorModel, "deviceName", rows[0].deviceName);
            }
        }
    });

    /**
     * 映射表
     */
    const FIELD_MAPPINGS = {
        deviceCheckStatus: {
            index: 9,
            label: "设备状态",
            values: new Map([
                ["停止", "10"],
                ["运行", "20"],
                ["不限定", "30"]
            ])
        },
        isPublish: {
            index: 10,
            label: "是否挂牌",
            values: new Map([
                ["是", "1"],
                ["否", "0"]
            ])
        },
        spotCheckMethod: {
            index: 12,
            label: "点检方法",
            values: new Map([
                ["五感", "10"],
                ["简易仪器", "20"],
                ["精密点检", "30"],
                ["自动采集", "40"]
            ])
        },
        spotCheckStandardType: {
            index: 13,
            label: "标准类型",
            values: new Map([
                ["定性", "10"],
                ["定量", "20"]
            ])
        },
        isPicture: {
            index: 15,
            label: "是否上传照片",
            values: new Map([
                ["是", "1"],
                ["否", "0"]
            ])
        },
        spotCheckNature: {
            index: 16,
            label: "点检性质",
            values: new Map([
                ["操作点检", "10"],
                ["专业点检", "20"]
            ])
        },
        spotCheckImplemente: {
            index: 17,
            label: "实施方",
            values: new Map([
                ["生产人员", "10"],
                ["设备人员", "20"]
            ])
        }
    };

    var BizModel;

    /**
     * 处理粘贴的内容并添加到表格中
     * @param {string} content - 粘贴的内容
     * @returns {void}
     */
    function postHandle(content) {
        if (!content?.trim()) {
            NotificationUtil("粘贴内容不能为空", "warning");
            return;
        }
        try {
            const dataItems = resultGrid.getDataItems();
            if (dataItems?.length) {
                resultGrid.unCheckAllRows();
            }
            const lines = content.split("\r\n").filter((line) => line.trim());
            const validRows = lines.map((line) => getRow(line)).filter((row) => row !== false);
            if (validRows.length > 0) {
                resultGrid.addRows(validRows, false, true);
                // NotificationUtil(`成功导入 ${validRows.length} 条数据`, "success");
            }
        } catch (error) {
            // console.error("处理粘贴内容时出错:", error);
            NotificationUtil("处理粘贴内容时出错", "error");
        }
    }

    /**
     * 解析粘贴的行数据并创建新的数据行对象
     * @param {string} line 粘贴的单行数据
     * @returns {Object|boolean} 返回创建的数据行对象,或在数据无效时返回false
     */
    function getRow(line) {
        if (!line?.trim()) {
            return false;
        }
        const columns = line.split("\t");
        if (!columns[0]?.trim()) {
            return false;
        }
        try {
            const row = {
                unitCode: columns[0],
                segNo: columns[0],
                spotCheckStatus: "10",
                apprStatus: "",
                spotCheckStandardId: "",
                eArchivesNo: columns[5],
                equipmentName: columns[6],
                deviceCode: columns[7],
                deviceName: columns[8],
                spotCheckContent: columns[11],
                judgmentStandard: columns[14],
                benchmarkDate: columns[18],
                spotCheckCycle: columns[19],
                measureId: columns[20],
                lowerLimit: columns[21],
                upperLimit: columns[22],
                recCreator: "",
                recCreatorName: "",
                recCreateTime: "",
                recRevisor: "",
                recRevisorName: "",
                recReviseTime: "",
                uuid: "",
                processInstanceId: ""
            };
            // 验证和转换映射字段
            for (const [field, config] of Object.entries(FIELD_MAPPINGS)) {
                const inputValue = columns[config.index]?.trim();
                const mappedValue = config.values.get(inputValue);

                if (!mappedValue) {
                    throw new Error(`${config.label}值 "${inputValue}" 不存在！请检查`);
                }
                row[field] = mappedValue;
            }
            const modelInstance = new BizModel(row);
            modelInstance.dirty = true;
            return modelInstance;
        } catch (error) {
            NotificationUtil(`处理行数据失败: ${error.message}`, "error");
            return false;
        }
    }

    // 优化粘贴事件处理
    document.addEventListener("paste", function (evt) {
        if (evt.target.id !== "clipContent") {
            return;
        }
        evt.preventDefault();
        const clipdata = evt.clipboardData || window.clipboardData;
        const content = clipdata.getData("text/plain");
        clipWindow.close();
        handleFun?.(content);
    });
});