create table TMEDV0103
(
    DEVICE_ID       VARCHAR(64)  default ' '    not null comment '分部设备序号',
    DEVICE_CODE     VARCHAR(64)  default ' '    not null comment '分部设备代码',
    DEVICE_NAME     VARCHAR(128) default ' '    not null comment '分部设备名称',
    E_ARCHIVES_NO   VARCHAR(20)  default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME  VARCHAR(200) default ' '    not null comment '设备名称',
    EQUIPMENT_TYPE  VARCHAR(1)   default ' '    not null comment '设备类型',
    MAKER_NAME      VARCHAR(60)  default ' '    not null comment '制造商名称',
    USE_DATE        VARCHAR(16)  default ' '    not null comment '使用日期',
    DRAWING_NUM     VARCHAR(5)   default ' '    not null comment '图号',
    -- 固定字段
    UUID            VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)
) COMMENT ='分部设备表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;


-- 增加ER图外键
ALTER TABLE TMEDV0103 ADD unique KEY (DEVICE_CODE);
ALTER TABLE TMEDV0103 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);