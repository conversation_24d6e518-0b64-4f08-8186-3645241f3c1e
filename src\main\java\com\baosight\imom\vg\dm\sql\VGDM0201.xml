<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="VGDM0201">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckStatus">
            SPOT_CHECK_STATUS = #spotCheckStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="spotCheckStatus">
            SPOT_CHECK_STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckStandardType">
            SPOT_CHECK_STANDARD_TYPE = #spotCheckStandardType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckNature">
            SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="spotCheckStandardId">
            SPOT_CHECK_STANDARD_ID like concat('%',#spotCheckStandardId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="apprStatus">
            APPR_STATUS = #apprStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processInstanceId">
            PROCESS_INSTANCE_ID = #processInstanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processInstanceIdList">
            PROCESS_INSTANCE_ID in
            <iterate property="processInstanceIdList" open="(" close=")"
                     conjunction=",">
                #processInstanceIdList[]#
            </iterate>
        </isNotEmpty>
        <!--根据登录人查询待办-->
        <isNotEmpty property="loginId" prepend="and">
            <isEqual property="apprStatus" compareValue="60">
                <!--待审批-->
                EXISTS(SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0201.PROCESS_INSTANCE_ID
                AND T.ASSIGNEE_ID = #loginId#
                AND T.PROCESS_KEY = 'spotCheckStandardAud'
                AND t.STATE = 'open')
            </isEqual>
            <isEqual property="apprStatus" compareValue="70">
                <!--审核通过-->
                EXISTS(SELECT 1
                FROM ${platSchema}.HEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0201.PROCESS_INSTANCE_ID
                AND T.COMPLETER_ID = #loginId#
                and T.PROCESS_KEY = 'spotCheckStandardAud'
                and T.APPROVAL_RESULT = 'grant'
                and t.STATE = 'completed'
                )
            </isEqual>
            <isEqual property="apprStatus" compareValue="7X">
                <!--审核驳回-->
                EXISTS(
                SELECT 1
                FROM ${platSchema}.TEWPT00 T
                WHERE T.PROCESS_INSTANCE_ID = ${mevgSchema}.TVGDM0201.PROCESS_INSTANCE_ID
                AND T.COMPLETER_ID = #loginId#
                AND T.PROCESS_KEY = 'spotCheckStandardAud'
                AND T.APPROVAL_RESULT ='reject'
                AND t.STATE = 'completed'
                )
            </isEqual>
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0201">
        SELECT
        SPOT_CHECK_STANDARD_ID as "spotCheckStandardId",  <!-- 点检标准编号 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        DEVICE_CHECK_STATUS as "deviceCheckStatus",  <!-- 设备状态 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备代码 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        SPOT_CHECK_CONTENT as "spotCheckContent",  <!-- 点检内容 -->
        SPOT_CHECK_METHOD as "spotCheckMethod",  <!-- 点检方法 -->
        IS_PUBLISH as "isPublish",  <!-- 是否挂牌 -->
        SPOT_CHECK_STANDARD_TYPE as "spotCheckStandardType",  <!-- 标准类型 -->
        JUDGMENT_STANDARD as "judgmentStandard",  <!-- 判断标准 -->
        BENCHMARK_DATE as "benchmarkDate",  <!-- 基准日期 -->
        SPOT_CHECK_CYCLE as "spotCheckCycle",  <!-- 点检周期 -->
        SPOT_CHECK_NATURE as "spotCheckNature",  <!-- 点检性质 -->
        SPOT_CHECK_IMPLEMENTE as "spotCheckImplemente",  <!-- 实施方 -->
        MEASURE_ID as "measureId",  <!-- 计量单位 -->
        UPPER_LIMIT as "upperLimit",  <!-- 上限值 -->
        LOWER_LIMIT as "lowerLimit",  <!-- 下限值 -->
        SPOT_CHECK_STATUS as "spotCheckStatus",  <!-- 点检标准状态 -->
        TAG_ID as "tagId",  <!-- 点位ID -->
        TAG_DESC as "tagDesc",  <!-- 点位描述 -->
        IS_PICTURE as "isPicture",  <!-- 是否上传图片 -->
        PROCESS_INSTANCE_ID as "processInstanceId",  <!-- 工作流实例ID -->
        APPR_STATUS as "apprStatus",  <!-- 审批状态 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${mevgSchema}.TVGDM0201 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_REVISE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0201 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="countRepeat" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0201 WHERE 1=1
        <isNotEmpty property="deviceCode">
            AND DEVICE_CODE = #deviceCode#
        </isNotEmpty>
        <isNotEmpty property="eArchivesNo">
            AND E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty property="spotCheckContent">
            AND SPOT_CHECK_CONTENT = #spotCheckContent#
        </isNotEmpty>
        <isNotEmpty property="spotCheckMethod">
            AND SPOT_CHECK_METHOD = #spotCheckMethod#
        </isNotEmpty>
        <isNotEmpty property="isPublish">
            AND IS_PUBLISH = #isPublish#
        </isNotEmpty>
        <isNotEmpty property="spotCheckStandardType">
            AND SPOT_CHECK_STANDARD_TYPE = #spotCheckStandardType#
        </isNotEmpty>
        <isNotEmpty property="judgmentStandard">
            AND JUDGMENT_STANDARD = #judgmentStandard#
        </isNotEmpty>
        <isNotEmpty property="benchmarkDate">
            AND BENCHMARK_DATE = #benchmarkDate#
        </isNotEmpty>
        <isNotEmpty property="spotCheckCycle">
            AND SPOT_CHECK_CYCLE = #spotCheckCycle#
        </isNotEmpty>
        <isNotEmpty property="spotCheckNature">
            AND SPOT_CHECK_NATURE = #spotCheckNature#
        </isNotEmpty>
        <isNotEmpty property="spotCheckImplemente">
            AND SPOT_CHECK_IMPLEMENTE = #spotCheckImplemente#
        </isNotEmpty>
        <isNotEmpty property="measureId">
            AND MEASURE_ID = #measureId#
        </isNotEmpty>
        <isNotEmpty property="upperLimit">
            AND UPPER_LIMIT = #upperLimit#
        </isNotEmpty>
        <isNotEmpty property="lowerLimit">
            AND LOWER_LIMIT = #lowerLimit#
        </isNotEmpty>
        <isNotEmpty property="tagId">
            AND TAG_ID = #tagId#
        </isNotEmpty>
        AND SEG_NO = #segNo# and UNIT_CODE = #unitCode#
        AND DEL_FLAG = '0'
        AND SPOT_CHECK_STATUS > '00'
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0201 (SPOT_CHECK_STANDARD_ID,  <!-- 点检标准编号 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        DEVICE_CHECK_STATUS,  <!-- 设备状态 -->
        E_ARCHIVES_NO,  <!-- 设备代码 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        SPOT_CHECK_CONTENT,  <!-- 点检内容 -->
        SPOT_CHECK_METHOD,  <!-- 点检方法 -->
        IS_PUBLISH,  <!-- 是否挂牌 -->
        SPOT_CHECK_STANDARD_TYPE,  <!-- 标准类型 -->
        JUDGMENT_STANDARD,  <!-- 判断标准 -->
        BENCHMARK_DATE,  <!-- 基准日期 -->
        SPOT_CHECK_CYCLE,  <!-- 点检周期 -->
        SPOT_CHECK_NATURE,  <!-- 点检性质 -->
        SPOT_CHECK_IMPLEMENTE,  <!-- 实施方 -->
        MEASURE_ID,  <!-- 计量单位 -->
        UPPER_LIMIT,  <!-- 上限值 -->
        LOWER_LIMIT,  <!-- 下限值 -->
        SPOT_CHECK_STATUS,  <!-- 点检标准状态 -->
        TAG_ID,  <!-- 点位ID -->
        TAG_DESC,  <!-- 点位描述 -->
        IS_PICTURE,  <!-- 是否上传图片 -->
        PROCESS_INSTANCE_ID,  <!-- 工作流实例ID -->
        APPR_STATUS,  <!-- 审批状态 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#spotCheckStandardId#, #deviceCode#, #deviceName#, #deviceCheckStatus#, #eArchivesNo#, #equipmentName#,
        #spotCheckContent#, #spotCheckMethod#, #isPublish#, #spotCheckStandardType#, #judgmentStandard#,
        #benchmarkDate#, #spotCheckCycle#, #spotCheckNature#, #spotCheckImplemente#, #measureId#, #upperLimit#,
        #lowerLimit#, #spotCheckStatus#, #tagId#, #tagDesc#, #isPicture#, #processInstanceId#, #apprStatus#, #uuid#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#,
        #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0201 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0201
        SET
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        DEVICE_CHECK_STATUS = #deviceCheckStatus#,   <!-- 设备状态 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备代码 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        SPOT_CHECK_CONTENT = #spotCheckContent#,   <!-- 点检内容 -->
        SPOT_CHECK_METHOD = #spotCheckMethod#,   <!-- 点检方法 -->
        IS_PUBLISH = #isPublish#,   <!-- 是否挂牌 -->
        SPOT_CHECK_STANDARD_TYPE = #spotCheckStandardType#,   <!-- 标准类型 -->
        JUDGMENT_STANDARD = #judgmentStandard#,   <!-- 判断标准 -->
        BENCHMARK_DATE = #benchmarkDate#,   <!-- 基准日期 -->
        SPOT_CHECK_CYCLE = #spotCheckCycle#,   <!-- 点检周期 -->
        SPOT_CHECK_NATURE = #spotCheckNature#,   <!-- 点检性质 -->
        SPOT_CHECK_IMPLEMENTE = #spotCheckImplemente#,   <!-- 实施方 -->
        MEASURE_ID = #measureId#,   <!-- 计量单位 -->
        UPPER_LIMIT = #upperLimit#,   <!-- 上限值 -->
        LOWER_LIMIT = #lowerLimit#,   <!-- 下限值 -->
        SPOT_CHECK_STATUS = #spotCheckStatus#,   <!-- 点检标准状态 -->
        TAG_ID = #tagId#,   <!-- 点位ID -->
        TAG_DESC = #tagDesc#,   <!-- 点位描述 -->
        IS_PICTURE = #isPicture#,   <!-- 是否上传图片 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateStatus">
        UPDATE ${mevgSchema}.TVGDM0201
        SET
        SPOT_CHECK_STATUS = #spotCheckStatus#,   <!-- 点检标准状态 -->
        PROCESS_INSTANCE_ID = #processInstanceId#,   <!-- 工作流实例ID -->
        APPR_STATUS = #apprStatus#,   <!-- 审批状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>
</sqlMap>