$(function () {
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        const validator = IPLAT.Validator({
            id: "inqu"
        });
        if (!validator.validate()) {
            return;
        }
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "wlStockQty",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        return Number(item["wlStockQty"]) - Number(item["usingWgt"]);
                    }
                }
            ],
            loadComplete: function (grid) {
                // 确定按钮
                $("#SUB_ENTER").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    if (checkedRows.length === 0) {
                        NotificationUtil({msg: "操作失败，原因[请先选择库存!]"}, "error");
                        return;
                    }
                    // 获取父表grid
                    const parentGrid = window.parent.zc_detailGrid;
                    var list = parentGrid.getDataItems();
                    // 检查是否存在重复数据
                    let hasDuplicate = false;
                    $.each(checkedRows, function (index, value) {
                        // 根据库存id判断数据重复
                        if (list.some((item) => item.inventoryUuid === value.uuid)) {
                            NotificationUtil({msg: "资材【" + value.stuffCode + "-" + value.stuffName + "】已存在，请勿重复添加！"}, "error");
                            hasDuplicate = true;
                            return false; // 中断$.each循环
                        }
                    });

                    // 有重复则直接返回,不执行后续添加操作
                    if (hasDuplicate) {
                        return;
                    }
                    // 定义BizModel
                    var BizModel = kendo.data.Model.define({
                        id: parentGrid.dataSource.options.schema.model.id,
                        fields: parentGrid.dataSource.options.schema.model.fields
                    });
                    const validRows = [];
                    // 添加数据
                    $.each(checkedRows, function (index, value) {
                        const row = {};
                        // 遍历父表字段并赋值相同字段
                        $.each(BizModel.fields, function (key, value2) {
                            if (!IPLAT.isUndefined(value[key])) {
                                row[key] = value[key];
                            } else {
                                row[key] = "";
                            }
                        });
                        // 赋值子表字段
                        row.inventoryUuid = value.uuid;
                        row.specDesc = value.spec;
                        row.purContractNum = value.stuffContractId;
                        row.purOrderNum = value.stuffContractSubid;
                        row.unitPrice = value.stuffUnitPriceTaxed;
                        row.usingWgt = 0;//Number(value.wlStockQty) - Number(value.usingWgt);
                        row.recCreator = "";
                        row.recCreatorName = "";
                        row.recCreateTime = "";
                        row.recRevisor = "";
                        row.recRevisorName = "";
                        row.recReviseTime = "";
                        row.deptId = $("#inqu_status-0-deptId").val();
                        row.deptName = $("#inqu_status-0-deptName").val();
                        // 创建BizModel实例
                        const modelInstance = new BizModel(row);
                        modelInstance.dirty = true;
                        validRows.push(modelInstance);
                    });
                    if (validRows.length > 0) {
                        parentGrid.addRows(validRows, false, true);
                    }
                    // 关闭弹窗
                    window.parent["inventoryInfoWindow"].close();
                });
                // 退出按钮
                $("#SUB_EXIT").on("click", function (e) {
                    // 关闭弹窗
                    window.parent["inventoryInfoWindow"].close();
                });
            }
        }
    };

    // 查询条件区域库区代码弹窗
    IMOMUtil.windowTemplate({
        windowId: "warehouseInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            const segNo = $("#inqu_status-0-segNo").val();
            const segName = $("#inqu_status-0-segName").val();
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-segName").val(segName);
            // 指定资材库
            iframejQuery("#inqu_status-0-wareHouseBusinessType").val("80");
        },
        assignMap: {
            "inqu_status-0-warehouseCode": "stockCode",
            "inqu_status-0-warehouseName": "stockName"
        }
    });
    // 查询条件区域部门代码弹窗
    IMOMUtil.windowTemplate({
        windowId: "deptInfo",
        _open: function (e, iframejQuery) {
            const segNo = $("#inqu_status-0-segNo").val();
            iframejQuery("#inqu_status-0-segNo").val(segNo);
        },
        assignMap: {
            "inqu_status-0-deptId": "segNo",
            "inqu_status-0-deptName": "segName"
        }
    });
});
