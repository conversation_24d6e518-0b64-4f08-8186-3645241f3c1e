/**
* Generate time : 2025-01-14 22:39:41
* Version : 1.0
*/
$(function () {

    //查询按钮事件
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) { 
                // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            },
            /**
             * 双击选中用户组
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            /**
             * 分页配置
             */
            pageable: {
                pageSize: 10,
                pageSizes: [10, 20, 50, 100]
            }
        }
    }

}); 