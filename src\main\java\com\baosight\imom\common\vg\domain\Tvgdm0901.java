/**
 * Generate time : 2025-04-22 17:43:53
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;

/**
 * Tvgdm0901
 */
public class Tvgdm0901 extends DaoEPBase {

    @NotBlank(message = "设备不能为空")
    private String eArchivesNo = " ";        /* 设备档案编号*/
    @NotBlank(message = "设备不能为空")
    private String equipmentName = " ";        /* 设备名称*/
    private String scrapApplyId = " ";        /* 报废申请单号*/
    @NotBlank(message = "申请部门不能为空")
    private String deptId = " ";        /* 部门*/
    @NotBlank(message = "申请部门不能为空")
    private String deptName = " ";        /* 部门名称*/
    @NotBlank(message = "资材代码不能为空")
    private String stuffCode = " ";        /* 资材代码*/
    @NotBlank(message = "资材名称不能为空")
    private String stuffName = " ";        /* 资材名称*/
    private String specDesc = " ";        /* 规格*/
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "单价必须大于0")
    private BigDecimal unitPrice = new BigDecimal("0");        /* 单价*/
    @NotNull(message = "数量不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "数量必须大于0")
    private BigDecimal applyQty = new BigDecimal("0");        /* 申请量*/
    private BigDecimal amountMoney = new BigDecimal("0");        /* 总金额*/
    @NotBlank(message = "报废原因不能为空")
    private String scrapReason = " ";        /* 报废原因*/
    @NotBlank(message = "报废方式不能为空")
    private String scrapType = " ";        /* 报废方式*/
    private String applyStatus = " ";        /* 申请状态*/
    private String apprStatus = " ";        /* 审批状态*/
    private String processInstanceId = " ";        /* 工作流实例ID*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String faultId = " ";        /* 故障单号*/
    private String voucherNum = " ";        /* 依据凭单*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapApplyId");
        eiColumn.setDescName("报废申请单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deptId");
        eiColumn.setDescName("部门");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deptName");
        eiColumn.setDescName("部门名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffCode");
        eiColumn.setDescName("资材代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stuffName");
        eiColumn.setDescName("资材名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitPrice");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("单价");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("applyQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("申请量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("amountMoney");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("总金额");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapReason");
        eiColumn.setDescName("报废原因");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapType");
        eiColumn.setDescName("报废方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("applyStatus");
        eiColumn.setDescName("申请状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprStatus");
        eiColumn.setDescName("审批状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processInstanceId");
        eiColumn.setDescName("工作流实例ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("faultId");
        eiColumn.setDescName("故障单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单");
        eiMetadata.addMeta(eiColumn);
    }

    /**
     * the constructor
     */
    public Tvgdm0901() {
        initMetaData();
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the scrapApplyId - 报废申请单号
     *
     * @return the scrapApplyId
     */
    public String getScrapApplyId() {
        return this.scrapApplyId;
    }

    /**
     * set the scrapApplyId - 报废申请单号
     */
    public void setScrapApplyId(String scrapApplyId) {
        this.scrapApplyId = scrapApplyId;
    }

    /**
     * get the deptId - 部门
     *
     * @return the deptId
     */
    public String getDeptId() {
        return this.deptId;
    }

    /**
     * set the deptId - 部门
     */
    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    /**
     * get the deptName - 部门名称
     *
     * @return the deptName
     */
    public String getDeptName() {
        return this.deptName;
    }

    /**
     * set the deptName - 部门名称
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    /**
     * get the stuffCode - 资材代码
     *
     * @return the stuffCode
     */
    public String getStuffCode() {
        return this.stuffCode;
    }

    /**
     * set the stuffCode - 资材代码
     */
    public void setStuffCode(String stuffCode) {
        this.stuffCode = stuffCode;
    }

    /**
     * get the stuffName - 资材名称
     *
     * @return the stuffName
     */
    public String getStuffName() {
        return this.stuffName;
    }

    /**
     * set the stuffName - 资材名称
     */
    public void setStuffName(String stuffName) {
        this.stuffName = stuffName;
    }

    /**
     * get the specDesc - 规格
     *
     * @return the specDesc
     */
    public String getSpecDesc() {
        return this.specDesc;
    }

    /**
     * set the specDesc - 规格
     */
    public void setSpecDesc(String specDesc) {
        this.specDesc = specDesc;
    }

    /**
     * get the unitPrice - 单价
     *
     * @return the unitPrice
     */
    public BigDecimal getUnitPrice() {
        return this.unitPrice;
    }

    /**
     * set the unitPrice - 单价
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * get the applyQty - 申请量
     *
     * @return the applyQty
     */
    public BigDecimal getApplyQty() {
        return this.applyQty;
    }

    /**
     * set the applyQty - 申请量
     */
    public void setApplyQty(BigDecimal applyQty) {
        this.applyQty = applyQty;
    }

    /**
     * get the amountMoney - 总金额
     *
     * @return the amountMoney
     */
    public BigDecimal getAmountMoney() {
        return this.amountMoney;
    }

    /**
     * set the amountMoney - 总金额
     */
    public void setAmountMoney(BigDecimal amountMoney) {
        this.amountMoney = amountMoney;
    }

    /**
     * get the scrapReason - 报废原因
     *
     * @return the scrapReason
     */
    public String getScrapReason() {
        return this.scrapReason;
    }

    /**
     * set the scrapReason - 报废原因
     */
    public void setScrapReason(String scrapReason) {
        this.scrapReason = scrapReason;
    }

    /**
     * get the scrapType - 报废方式
     *
     * @return the scrapType
     */
    public String getScrapType() {
        return this.scrapType;
    }

    /**
     * set the scrapType - 报废方式
     */
    public void setScrapType(String scrapType) {
        this.scrapType = scrapType;
    }

    /**
     * get the applyStatus - 申请状态
     *
     * @return the applyStatus
     */
    public String getApplyStatus() {
        return this.applyStatus;
    }

    /**
     * set the applyStatus - 申请状态
     */
    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    /**
     * get the apprStatus - 审批状态
     *
     * @return the apprStatus
     */
    public String getApprStatus() {
        return this.apprStatus;
    }

    /**
     * set the apprStatus - 审批状态
     */
    public void setApprStatus(String apprStatus) {
        this.apprStatus = apprStatus;
    }

    /**
     * get the processInstanceId - 工作流实例ID
     *
     * @return the processInstanceId
     */
    public String getProcessInstanceId() {
        return this.processInstanceId;
    }

    /**
     * set the processInstanceId - 工作流实例ID
     */
    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the faultId - 故障单号
     *
     * @return the faultId
     */
    public String getFaultId() {
        return this.faultId;
    }

    /**
     * set the faultId - 故障单号
     */
    public void setFaultId(String faultId) {
        this.faultId = faultId;
    }

    /**
     * get the voucherNum - 依据凭单
     *
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 依据凭单
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setScrapApplyId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapApplyId")), scrapApplyId));
        setDeptId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deptId")), deptId));
        setDeptName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deptName")), deptName));
        setStuffCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffCode")), stuffCode));
        setStuffName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stuffName")), stuffName));
        setSpecDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specDesc")), specDesc));
        setUnitPrice(NumberUtils.toBigDecimal(StringUtils.toString(map.get("unitPrice")), unitPrice));
        setApplyQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("applyQty")), applyQty));
        setAmountMoney(NumberUtils.toBigDecimal(StringUtils.toString(map.get("amountMoney")), amountMoney));
        setScrapReason(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapReason")), scrapReason));
        setScrapType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapType")), scrapType));
        setApplyStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("applyStatus")), applyStatus));
        setApprStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprStatus")), apprStatus));
        setProcessInstanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processInstanceId")), processInstanceId));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setFaultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("faultId")), faultId));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("scrapApplyId", StringUtils.toString(scrapApplyId, eiMetadata.getMeta("scrapApplyId")));
        map.put("deptId", StringUtils.toString(deptId, eiMetadata.getMeta("deptId")));
        map.put("deptName", StringUtils.toString(deptName, eiMetadata.getMeta("deptName")));
        map.put("stuffCode", StringUtils.toString(stuffCode, eiMetadata.getMeta("stuffCode")));
        map.put("stuffName", StringUtils.toString(stuffName, eiMetadata.getMeta("stuffName")));
        map.put("specDesc", StringUtils.toString(specDesc, eiMetadata.getMeta("specDesc")));
        map.put("unitPrice", StringUtils.toString(unitPrice, eiMetadata.getMeta("unitPrice")));
        map.put("applyQty", StringUtils.toString(applyQty, eiMetadata.getMeta("applyQty")));
        map.put("amountMoney", StringUtils.toString(amountMoney, eiMetadata.getMeta("amountMoney")));
        map.put("scrapReason", StringUtils.toString(scrapReason, eiMetadata.getMeta("scrapReason")));
        map.put("scrapType", StringUtils.toString(scrapType, eiMetadata.getMeta("scrapType")));
        map.put("applyStatus", StringUtils.toString(applyStatus, eiMetadata.getMeta("applyStatus")));
        map.put("apprStatus", StringUtils.toString(apprStatus, eiMetadata.getMeta("apprStatus")));
        map.put("processInstanceId", StringUtils.toString(processInstanceId, eiMetadata.getMeta("processInstanceId")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("faultId", StringUtils.toString(faultId, eiMetadata.getMeta("faultId")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));

        return map;

    }
}