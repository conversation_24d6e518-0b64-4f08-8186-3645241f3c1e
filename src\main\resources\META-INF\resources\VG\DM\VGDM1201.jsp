<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage prefix="imom">

    <jsp:attribute name="header">
        <style>
            .kendo-xplat-INSERT1{
                float: left;
            }
        </style>
    </jsp:attribute>
    <jsp:body>
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-particularYear" cname="年份" colWidth="3"
                        />
        </div>
    </EF:EFRegion>
    <EF:EFTab id="info">
    <div title="年度加工量&达产率" id="info-1">
    <EF:EFRegion id="result">
        <EF:EFGrid blockId="result" id="result" autoBind="false" autoDraw="no" isFloat="true">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName" enable="false"/>
            <EF:EFColumn ename="particularYear" cname="年份" align="right" width="100" format="{0}"
                         readonly="true" required="true"
                         onkeyup="if(isNaN(value))execCommand('undo')"/>
            <EF:EFColumn ename="annualProcessingVolume" cname="年度加工量" align="right" width="100" format="{0:n6}"
                         readonly="false" required="true"
                         onkeyup="if(isNaN(value))execCommand('undo')"/>
            <EF:EFColumn ename="annualProductionRate" cname="年度达产率%" align="right" width="120" format="{0:n2}"
                         readonly="false" required="true"
                         onkeyup="if(isNaN(value))execCommand('undo')"/>
            <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
            <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                         enable="false"/>
            <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
            <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
            <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                         enable="false"/>
            <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                         enable="true" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>

        </EF:EFGrid>
    </EF:EFRegion>
    </div>
    <div title="区域数据指标" id="info-2">
        <EF:EFRegion id="result1">
            <EF:EFGrid blockId="result1" id="result1" autoBind="false" autoDraw="no" isFloat="true">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false"/>
                <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                                  blockName="unitBlock" valueField="segNo" textField="segName" enable="false"/>
                <EF:EFColumn ename="particularYear" cname="年份" align="right" width="100" format="{0}"
                             readonly="true" required="true"
                            />
                <EF:EFColumn ename="deptName" cname="部门" align="center" width="100" format="{0:n6}"
                             readonly="false" required="true"/>
                <EF:EFColumn ename="steelManufacturingCostAccum" cname="吨钢制造累计费用" align="right" width="150" format="{0:n2}"
                             readonly="false" required="true"
                            />
                <EF:EFColumn ename="manufacturingCostCompletion" cname="吨钢制造费用完成度%" align="right" width="160" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="productionRateAccumulated" cname="年度累计达产率%" align="right" width="120" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="productionCapacityCompletion" cname="达产率完成度%" align="right" width="150" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="oeeEquipmentRateAccumulated" cname="累计OEE设备综合利用率%" align="right" width="160" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="oeeEquipmentRate" cname="OEE设备综合利用率%" align="right" width="160" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="qualityDisputeAccumulated" cname="质量异议PPM累计值" align="right" width="150" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="qualityDisputeCompletion" cname="质量异议完成度%" align="right" width="150" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="managementImprovementRate" cname="工厂管理提升%" align="right" width="150" format="{0:n2}"
                             readonly="false" required="true"
                             />
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>

            </EF:EFGrid>
        </EF:EFRegion>

    </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    </jsp:body>
</EF:EFPage>