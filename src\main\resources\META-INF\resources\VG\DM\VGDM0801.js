$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfoMainQuery",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });
    var tab_Strip;
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Strip = $("#info").data("kendoTabStrip");
    });
    var updateFlag = false;
    // 检修实绩信息查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                const tableId = e.contentElement.id;
                if (tableId === "info-2") {
                    // 只判断非新增和修改按钮跳转
                    if (!updateFlag) {
                        const checkedRows = resultGrid.getCheckedRows();
                        const checkRowLength = checkedRows.length;
                        if (checkRowLength !== 1) {
                            NotificationUtil({msg: "请勾选一条检修实绩信息"}, "error");
                            e.preventDefault();
                        } else {
                            setDetailData(checkedRows[0]);
                            setReadStatus();
                        }
                    }
                } else {
                    updateFlag = false;
                }
            }
        }
    };
    IPLATUI.EFGrid = {
        // 检修实绩信息
        "result": {
            onRowDblClick: function (e) {
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                setReadStatus();
                tab_Strip.select(1);
            },
            loadComplete: function (grid) {
                // 修改按钮
                $("#UPDATE").on("click", function (e) {
                    const checkedRows = resultGrid.getCheckedRows();
                    const checkRowLength = checkedRows.length;
                    if (checkRowLength !== 1) {
                        NotificationUtil({msg: "操作失败，原因[只能对一行数据进行修改！]"}, "error");
                        return;
                    }
                    const overhaulPlanStatus = checkedRows[0].overhaulPlanStatus;
                    if (overhaulPlanStatus !== "20" && overhaulPlanStatus !== "30") {
                        NotificationUtil({msg: "操作失败，原因[只能对生效或启动状态数据进行修改！]"}, "error");
                        return;
                    }
                    updateFlag = true;
                    setDetailData(checkedRows[0]);
                    setUpdateStatus();
                    tab_Strip.select(1);
                });
                // 实绩确认
                $("#CONFIRM1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    const checkedRows = resultGrid.getCheckedRows();
                    // 检修单号列表
                    let idList = [];
                    for (let i = 0; i < checkedRows.length; i++) {
                        idList.push(checkedRows[i].overhaulPlanId);
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("relevanceType", "13");
                    eiInfo.set("idList", idList);
                    // 查询备品备件领用申请数量
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "queryApplyCount", function (ei) {
                        const rowCount = ei.get("rowCount");
                        if (rowCount === 0) {
                            IPLAT.confirm({
                                message: "未维护资材备件领用信息，是否直接确认?",
                                okFn: function (e) {
                                    IMOMUtil.submitGridsData("result", "VGDM0801", "confirm", true, null, null, false);
                                },
                                title: "提示"
                            });
                            return;
                        }
                        IMOMUtil.submitGridsData("result", "VGDM0801", "confirm", true, null, null, false);
                    });
                });
                // 加入设备履历按钮
                $("#ADDRESUME").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0801", "addResume", true, null, null, false);
                });
                // 修改保存按钮
                $("#UPDATE_SAVE").on("click", function (e) {
                    const validator = IPLAT.Validator({
                        id: "detail"
                    });
                    if (!validator.validate()) {
                        return;
                    }
                    const node = $("#detail");
                    IMOMUtil.submitNode(node, "VGDM0801", "update", function (ei) {
                        resultGrid.dataSource.page(1);
                        tab_Strip.select(0);
                        NotificationUtil({msg: ei.msg}, "success");
                    });
                });
            }
        },
        // 附件信息
        "result2": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
            loadComplete: function (grid) {
                // 附件上传按钮
                $("#FILEUPLOAD").click(function () {
                    $("#fileForm").click();
                });
                // 动火证附件上传按钮
                $("#uploadHot").click(function () {
                    $("#fileForm1").click();
                });
            }
        },
        // 定期检查项目
        "result5": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            columns: [
                {
                    field: "pictureOperate",
                    template: function (e) {
                        var viewHref = '';
                        if (!IPLAT.isBlankString(e.pictureUrl)) {
                            viewHref = '<a href="' + e.pictureUrl + '" target="_blank">查看</a>';
                        }
                        return viewHref;
                    }
                }
            ],
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 修改按钮
                $("#UPDATE5").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result5Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result5", "VGDM0807", "updateActuals", true,
                        null, null, false);
                });

            }
        },
        // 维修项目
        "result6": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
        },
        // 给油脂记录
        "result7": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            beforeEdit: function (e) {
                // 检修计划修改时才允许编辑
                if (!e.model.isNew() && !updateFlag) {
                    e.preventDefault();
                }
            },
            loadComplete: function (grid) {
                // 修改按钮
                $("#UPDATE7").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(result7Grid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result7", "VGDM0808", "updateRecord", true,
                        null, null, false);
                });
            }
        },
        // 资材领用申请明细
        "zc_detail": {
            onAdd: function (e) {
                // 设置默认值
                $.each(e.items, function (index, item) {
                    item["stuffReceivingStatus"] = "10";
                    item["voucherNum"] = $("#inqu_status2-0-voucherNum").val();
                    item["unitCode"] = $("#inqu_status2-0-unitCode").val();
                    item["segNo"] = $("#inqu_status2-0-segNo").val();
                });
            },
            beforeEdit: function (e) {
                // 控制只有新增状态可修改
                if (e.field === "usingWgt") {
                    if (e.model.stuffReceivingStatus !== "10") {
                        e.preventDefault();
                    }
                }
            },
            loadComplete: function (grid) {
                // 选择库存按钮
                $("#APPLYMATERIALS").on("click", function (e) {
                    const row = resultGrid.getCheckedRows()[0];
                    if (row.overhaulPlanStatus !== "20" && row.overhaulPlanStatus !== "30") {
                        NotificationUtil({msg: "操作失败，原因[只能对生效或启动状态数据进行操作]"}, "error");
                        return;
                    }
                    // 查询IMC库存
                    inventoryInfoWindow.center().open();
                });
                // 新增按钮
                $("#INSERTM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("relevanceType", "13");
                    eiInfo.set("voucherNum", $("#inqu_status2-0-voucherNum").val());
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "insert", function (ei) {
                        zc_detailGrid.dataSource.page(1);
                    });
                });
                // 修改按钮
                $("#UPDATEM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("relevanceType", "13");
                    eiInfo.set("voucherNum", $("#inqu_status2-0-voucherNum").val());
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "update", function (ei) {
                        zc_detailGrid.dataSource.page(1);
                    });
                });
                // 删除按钮
                $("#DELETEM").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    IPLAT.confirm({
                        title: "删除",
                        message: "确认删除此数据么?",
                        okFn: function (e) {
                            const eiInfo = new EiInfo();
                            eiInfo.set("relevanceType", "13");
                            eiInfo.set("voucherNum", $("#inqu_status2-0-voucherNum").val());
                            eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                            IMOMUtil.submitEiInfo(eiInfo, "VGDM0804", "delete", function (ei) {
                                zc_detailGrid.dataSource.page(1);
                            });
                        }
                    });
                });
                // 生成报废申请
                $("#ADDSCRAP").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(zc_detailGrid)) {
                        return;
                    }
                    const eiInfo = new EiInfo();
                    eiInfo.set("segNo", $("#detail_status-0-unitCode").val());
                    eiInfo.set("eArchivesNo", $("#detail_status-0-eArchivesNo").val());
                    eiInfo.set("equipmentName", $("#detail_status-0-equipmentName").val());
                    eiInfo.addBlock(IMOMUtil.checkedRows2Block("zc_detail"));
                    IMOMUtil.submitEiInfo(eiInfo, "VGDM09", "insertFromStuff", function (ei) {
                        // zc_detailGrid.dataSource.page(1);
                    });
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();
    // 详情区域IMC库存弹窗
    IMOMUtil.windowTemplate({
        windowId: "inventoryInfo",
        notQuery: true,
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status2-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status2-0-segNo").val());
            iframejQuery("#inqu_status-0-segName").val($("#inqu_status2-0-segName").val());
        },
        afterSelect: function (rows) {
        }
    });

    /**
     * 设置月度计划详情是否显示
     * @param flag true:显示 false:隐藏
     */
    function setMonthShow(flag) {
        if (flag) {
            $("#result5").show();
            $("#result6").show();
            $("#result7").show();
        } else {
            $("#result5").hide();
            $("#result6").hide();
            $("#result7").hide();
        }
    }

    /**
     * 设置详情区域修改状态
     */
    function setUpdateStatus() {
        // 保存按钮启用
        $("#UPDATE_SAVE").attr("disabled", false);
        // 动火证附件上传启用
        $("#uploadHot").attr("disabled", false);
        // 相关按钮启用
        setButtonEdit(true);
        // 各字段状态
        IPLAT.EFSelect.enable($("#detail_status-0-isComplete"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-offlinePartsGone"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-isHot"), true);
        IPLAT.EFSelect.enable($("#detail_status-0-isConformStandard"), true);
        $("#detail_status-0-overhaulImplementDate").data("kendoDatePicker").enable(true);
        $("#detail_status-0-actualLegacyProject").attr("readonly", false);
        $("#detail_status-0-actualOverhaulNumber").attr("readonly", false);
        $("#detail_status-0-actualOverhaulTime").attr("readonly", false);
        $("#detail_status-0-overhaulLegacyProject").attr("readonly", false);
        $("#detail_status-0-hotCardId").attr("readonly", false);
        $("#detail_status-0-relevantMeasures").attr("readonly", false);
        $("#detail_status-0-overhaulSuggestions").attr("readonly", false);
        $("#detail_status-0-overhaulSummarize").attr("readonly", false);

    }

    /**
     * 设置详情区域只读状态
     */
    function setReadStatus() {
        // 保存按钮禁用
        $("#UPDATE_SAVE").attr("disabled", true);
        // 动火证附件上传禁用
        $("#uploadHot").attr("disabled", true);
        // 资材相关按钮禁用
        setButtonEdit(false);
        // 各字段状态
        IPLAT.EFSelect.enable($("#detail_status-0-isComplete"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-offlinePartsGone"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-isHot"), false);
        IPLAT.EFSelect.enable($("#detail_status-0-isConformStandard"), false);
        $("#detail_status-0-overhaulImplementDate").data("kendoDatePicker").enable(false);
        $("#detail_status-0-actualLegacyProject").attr("readonly", true);
        $("#detail_status-0-actualOverhaulNumber").attr("readonly", true);
        $("#detail_status-0-actualOverhaulTime").attr("readonly", true);
        $("#detail_status-0-overhaulLegacyProject").attr("readonly", true);
        $("#detail_status-0-hotCardId").attr("readonly", true);
        $("#detail_status-0-relevantMeasures").attr("readonly", true);
        $("#detail_status-0-overhaulSuggestions").attr("readonly", true);
        $("#detail_status-0-overhaulSummarize").attr("readonly", true);
    }

    // 月度计划相关按钮
    const planButtons = ["#APPLYMATERIALS", "#INSERTM", "#UPDATEM",
        "#DELETEM", "#UPDATE5", "#UPDATE7"
    ];

    /**
     * 设置按钮是否可编辑
     * @param {boolean} isEdit 是否可编辑
     */
    function setButtonEdit(isEdit) {
        $.each(planButtons, function (index, item) {
            if (isEdit) {
                $(item).css("pointer-events", "auto");
                $(item).find("button").attr("disabled", false);
            } else {
                $(item).css("pointer-events", "none");
                $(item).find("button").attr("disabled", true);
            }
        })
    }

    /**
     * 设置详情区域内容
     * @param row grid行数据
     */
    function setDetailData(row) {
        // 清除原数据
        IPLAT.clearNode(document.getElementById("info-2"));
        // 将数据回填到详情页
        IMOMUtil.fillNode(row, "detail");
        // 附件查询
        $("#inqu2_status-0-relevanceId").val(row.overhaulPlanId);
        $("#inqu2_status-0-segNo").val(row.segNo);
        result2Grid.dataSource.page(1);
        // 设置查询条件
        $("#inqu_status2-0-eArchivesNo").val(row.eArchivesNo);
        $("#inqu_status2-0-unitCode").val(row.unitCode);
        $("#inqu_status2-0-segNo").val(row.segNo);
        $("#inqu_status2-0-voucherNum").val(row.overhaulPlanId);
        // 业务单元简称特殊处理
        const rowIndex = resultGrid.getCheckedRowsIndex()[0];
        const segNoLabel = `result-${rowIndex}-segNo`;
        const segName = resultGrid.getDisplayEiInfo().get(segNoLabel);
        $("#inqu_status2-0-segName").val(segName);
        // 查询资材领用申请明细
        zc_detailGrid.dataSource.page(1);
        // 检修项目信息
        $("#inqu5_status-0-segNo").val(row.segNo);
        $("#inqu5_status-0-overhaulPlanId").val(row.overhaulPlanId);
        // 维修项目信息
        $("#inqu6_status-0-segNo").val(row.segNo);
        $("#inqu6_status-0-overhaulPlanId").val(row.overhaulPlanId);
        $("#inqu6_status-0-itemType").val("20");
        // 給油脂记录
        $("#inqu7_status-0-segNo").val(row.segNo);
        $("#inqu7_status-0-overhaulPlanId").val(row.overhaulPlanId);
        // 详情显示
        const overhaulQuality = row.overhaulQuality;
        if (overhaulQuality === "20") {
            setMonthShow(true)
            result5Grid.dataSource.page(1);
            result6Grid.dataSource.page(1);
            result7Grid.dataSource.page(1);
        } else {
            setMonthShow(false)
        }
    }

    var relevanceId = "";
    var segNo = "";
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            result2Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM0801";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            relevanceId = $("#inqu2_status-0-relevanceId").val();
            segNo = $("#inqu2_status-0-segNo").val();
        }
    });

    IPLAT.FileUploader({
        id: "fileForm1",
        ename: "fileForm1",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            result2Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm1" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM08H";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            relevanceId = $("#inqu2_status-0-relevanceId").val();
            segNo = $("#inqu2_status-0-segNo").val();
        }
    });
});
