package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0808;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 月度检修给油脂记录维护
 * @Date : 2025/4/24
 * @Version : 1.0
 */
public class ServiceVGDM0808 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0808.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(MesConstant.Iplat.RESULT7_BLOCK).addBlockMeta(new VGDM0808().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        String overhaulPlanId = inInfo.getCellStr(MesConstant.Iplat.INQU7_STATUS_BLOCK, 0, "overhaulPlanId");
        if (StrUtil.isBlank(overhaulPlanId)) {
            inInfo.setCell(MesConstant.Iplat.INQU7_STATUS_BLOCK, 0, "overhaulPlanId", UUIDUtils.getUUID());
        }
        return super.query(inInfo, VGDM0808.QUERY, null, false, new VGDM0808().eiMetadata,
                MesConstant.Iplat.INQU7_STATUS_BLOCK, MesConstant.Iplat.RESULT7_BLOCK, MesConstant.Iplat.RESULT7_BLOCK);
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT7_BLOCK);
            VGDM0808 vgdm0808;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0808 = new VGDM0808();
                vgdm0808.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0808);
                Map insMap = vgdm0808.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            DaoUtils.insertBatch(dao, VGDM0808.INSERT, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }


    /**
     * 数据校验
     *
     * @param vgdm0808 数据
     */
    private void checkData(VGDM0808 vgdm0808) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0808);
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT7_BLOCK);
            VGDM0808 vgdm0808;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0808 = new VGDM0808();
                vgdm0808.fromMap(block.getRow(i));
                this.checkData(vgdm0808);
                vgdm0808.setDelFlag("0");
                Map updMap = vgdm0808.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0808.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>
     * 修改点检标准状态为删除，删除标记置1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT7_BLOCK);
            VGDM0808 vgdm0808;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0808 = new VGDM0808();
                vgdm0808.fromMap(block.getRow(i));
                // 设置删除标记
                vgdm0808.setDelFlag("1");
                // 设置修改人
                Map delMap = vgdm0808.toMap();
                RecordUtils.setRevisor(delMap);
                // 数据返回前端
                block.getRows().set(i, delMap);
            }
            DaoUtils.updateBatch(dao, VGDM0808.UPD_FOR_DEL, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }



    /**
     * 修改记录
     */
    public EiInfo updateRecord(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT7_BLOCK);
            VGDM0808 vgdm0808;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0808 = new VGDM0808();
                vgdm0808.fromMap(block.getRow(i));
                Map updMap = vgdm0808.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0808.UPDATE_RECORD, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
