/**
* Generate time : 2024-12-02 10:17:53
* Version : 1.0
*/
package com.baosight.imom.vi.pm.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* VIPM0008
* table comment : 投料捆包表
*/
public class VIPM0008 extends DaoEPBase {

                public static final String QUERY = "VIPM0008.query";
                public static final String COUNT = "VIPM0008.count";
                public static final String INSERT = "VIPM0008.insert";
                public static final String UPDATE = "VIPM0008.update";
                public static final String DELETE = "VIPM0008.delete";
                public static final String DELETE_ONE_BALE = "VIPM0008.deleteOneBale";

                public static final String UPDATE_ISSUE_CRANE_FLAG = "VIPM0008.updateIssueCraneFlag";
                public static final String QUERY_MATERIAL_LIFTING_INFO = "VIPM0008.queryMaterialLiftingInfo";
                private String unitCode = " ";		/* 业务单元代码*/
                private String processOrderId = " ";		/* 生产工单号*/
                private String processDemandId = " ";		/* 生产需求单号*/
                private String processDemandSubId = " ";		/* 生产需求单子项号*/
                private String partId = " ";		/* 物料号*/
                private String specsDesc = " ";		/* 规格描述*/
                private String shopsign = " ";		/* 牌号*/
                private String packId = " ";		/* 捆包号*/
                private String matInnerId = " ";		/* 材料管理号*/
                private Long packQty = 0L;	/* 捆包数量*/
                private BigDecimal netWeight = new BigDecimal("0");		/* 净重*/
                private String prodNameCode = " ";		/* 品名代码*/
                private String prodCname = " ";		/* 品名名称*/
                private String factoryOrderNum = " ";		/* 钢厂订单号*/
                private String processDemandMaterailStatus = " ";		/* 生产需求单投料捆包状态*/
                private String voucherNum = " ";		/* 依据凭单*/
                private String auditFlag = " ";		/* 审核标记*/
                private String warehouseCode = " ";		/* 仓库代码*/
                private String warehouseName = " ";		/* 仓库名称*/
                private String locationId = " ";		/* 库位代码*/
                private String locationName = " ";		/* 库位名称*/
                private String f_packId = " ";		/* 父捆包号*/
                private String f_matInnerId = " ";		/* 父捆包号材料管理号*/
                private String m_packId = " ";		/* 母捆包号*/
                private String m_matInnerId = " ";		/* 母捆包号管理号*/
                private String packStatus = " ";		/* 捆包状态*/
                private BigDecimal yield = new BigDecimal("0");		/* 成材率*/
                private String producingAreaDesc = " ";		/* 产地名称*/
                private String remark = " ";		/* 备注*/
                private String materialLock = " ";		/* 原料封锁标记*/
                private String heatNum = " ";		/* 炉号*/
                private String chargePlat = " ";		/* 料台号*/
                private String canReturnMaterial = " ";		/* 是否可以退料*/
                private String sortId = " ";		/* 顺序号*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String tenantUser = " ";		/* 租户*/
                private String processDemandMaterailId = " ";		/* 生产需求投料捆包表单据号*/
                private String segNo = " ";		/* 业务账套*/
                private String uuid = " ";		/* ID*/
                private String prodCode = " ";		/* 品种代码*/
                private String storeType = " ";		/* 存货性质*/
                private String orderTypeCode = " ";		/* 订单性质代码(是否QZP)*/
                private String customerId = " ";		/* 客户代码*/
                private String customerName = " ";		/* 客户名称*/
                private Long f_packQty = 0L;	/* 父捆包件数*/
                private BigDecimal f_packWeight = new BigDecimal("0");		/* 父捆包重量*/
                private String f_packFactoryOrderNum = " ";		/* 父捆包钢厂订单号*/
                private Long m_packQty = 0L;	/* 母捆包件数*/
                private BigDecimal m_packWeight = new BigDecimal("0");		/* 母捆包重量*/
                private String m_packFactoryOrderNum = " ";		/* 母捆包钢厂订单号*/
                private String finishingShuntFlag = " ";		/* 精整分流标记*/
                private String d_userNum = " ";		/* 分户号*/
                private String d_userName = " ";		/* 分户号简称*/
                private String onWayFlag = " ";		/* 在途标记*/
                private String putoutId = " ";		/* 出库单号*/
                private String entityPutoutDate = " ";		/* 实物出库时间*/
                private String periodId = " ";		/* 会计期间*/
                private String teamId = " ";		/* 班组*/
                private String workingShift = " ";		/* 班次*/
                private String teamReportId = " ";		/* 班报序号*/
                private String proxyType = " ";		/* 类型*/
                private String ifUploadFinance = " ";		/* 是否上传财务*/
                private String tradeCode = " ";		/* 贸易方式*/
                private String qualityGrade = " ";		/* 质量等级*/
                private String qualityStatus = " ";		/* 质量状态*/
                private String packingTypeCode = " ";		/* 包装方式代码*/
                private String quantityUnit = " ";		/* 数量单位*/
                private String weightUnit = " ";		/* 重量单位*/
                private String prodTypeId = " ";		/* 品种附属码*/
                private String auditorId = " ";		/* 审核人工号*/
                private String auditorName = " ";		/* 审核人姓名*/
                private String auditTime = " ";		/* 审核时间*/
                private String auditStatus = " ";		/* 审核状态*/
                private String auditOpnion = " ";		/* 审核意见*/
                private String prodTypeDesc = " ";		/* 品种附属码描述*/
                private String scrapType = " ";		/* 利用材种类*/
                private String materialRackId = " ";		/* 料架号*/
                private BigDecimal grossWeight = new BigDecimal("0");		/* 毛重*/
                private String wlAccountMark = " ";		/* 物流对账标记*/
                private String clAccountMark = " ";		/* 财务对账标记*/
                private String labelId = " ";		/* 标签号*/
                private String firstPutinDate = " ";		/* 最初入库时间*/
                private String transferFlag = " ";		/* 转换标记*/
                private String techStandard = " ";		/* 技术标准*/
                private String packTransferFlag = " ";		/* 捆包转移标记*/
                private String manualNo = " ";		/* 手册编号*/
                private String hsId = " ";		/* 海关HS系统编码*/
                private String processConsignUnit = " ";		/* 加工委托方（股份委托加工来源）*/
                private String originalPackId = " ";		/* 原始捆包号*/
                private String apprStatus = " ";		/* 审批状态*/
                private String processId = " ";		/* 流程ID*/
                private String handbookId = " ";		/* 手册系统编号*/
                private String m_handbookId = " ";		/* 母手册号(最初手册号)*/
                private String f_handbookId = " ";		/* 父手册号(上层手册号)*/
                private String customsProductNum = " ";		
                private String surfaceGrade = " ";		/* 表面等级*/
                private String finUserId = " ";		/* 最终用户代码*/
                private String finUserName = " ";		/* 最终用户名称*/
                private String custPartId = " ";		/* 客户零件号*/
                private String custPartName = " ";		/* 客户零件号名称*/
                private String producingArea = " ";		/* 产地*/
                private String makerNum = " ";		/* 制造商代码*/
                private String makerName = " ";		/* 制造商名称*/
                private String providerCode = " ";		/* 供应商代码*/
                private String providerCname = " ";		/* 供应商名称*/
                private String custProviderId = " ";		/* 客户供料商代码*/
                private String productDesc = " ";		/* 产品描述*/
                private String bigTypeDesc = " ";		/* 利用材种类描述*/
                private BigDecimal actualMaterialWeight = new BigDecimal("0");		/* 实绩领料重量*/
                private String ifSynchronizationWl = " ";		/* 是否同步物流标记*/
                private String wmsSendFlag = "";		/* 立体库WMS发送标记 1表示已发送，其他表示未发送*/
                private String wmsCxFlag = "";		/* 立体库WMS发送撤销标记 1表示已发送，其他表示未发送*/
                private String propertyResult = " ";		/* 性能判定结果(1合格;2不合格;0或者空表示未判定)*/
                private String jpSign = "0";		/* 精品标记*/
                private String jpEmbarkationSign = "0";		/* 精品上机标记*/
                private String partsCode = "";		/* 部件编码*/
                private String partsName = "";		/* 部品名称*/
                private String carTypeCode = "";		/* 车型代码*/
                private String vehicleName = "";		/* 车型名称*/
                private BigDecimal quota = new BigDecimal(0);		/* 定额(KG)*/
                private String jpDebatchingSign = " ";		/* 精品退料标记*/
                private String jpFinishedProductSign = " ";		/* 精品成品标记*/
                private String printBatchId = " ";		/* 打印批次号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("unitCode");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandSubId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("规格描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("matInnerId");
        eiColumn.setFieldLength(80);
        eiColumn.setDescName("材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packQty");
        eiColumn.setDescName("捆包数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodNameCode");
        eiColumn.setFieldLength(7);
        eiColumn.setDescName("品名代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCname");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("品名名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryOrderNum");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("钢厂订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandMaterailStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产需求单投料捆包状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("依据凭单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("审核标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("父捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_matInnerId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("父捆包号材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("母捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_matInnerId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("母捆包号管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packStatus");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("捆包状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("yield");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(18);
        eiColumn.setDescName("成材率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("producingAreaDesc");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("产地名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialLock");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("原料封锁标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("heatNum");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("炉号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("chargePlat");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("料台号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("canReturnMaterial");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("是否可以退料");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortId");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("顺序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandMaterailId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("生产需求投料捆包表单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCode");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("品种代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeType");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("存货性质");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderTypeCode");
        eiColumn.setFieldLength(3);
        eiColumn.setDescName("订单性质代码(是否QZP)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_packQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("父捆包件数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_packWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("父捆包重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_packFactoryOrderNum");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("父捆包钢厂订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_packQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("母捆包件数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_packWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("母捆包重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_packFactoryOrderNum");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("母捆包钢厂订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finishingShuntFlag");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("精整分流标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNum");
        eiColumn.setFieldLength(11);
        eiColumn.setDescName("分户号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userName");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("分户号简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("onWayFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("在途标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putoutId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("出库单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("entityPutoutDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("实物出库时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("periodId");
        eiColumn.setFieldLength(6);
        eiColumn.setDescName("会计期间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamReportId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("班报序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("proxyType");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifUploadFinance");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否上传财务");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("贸易方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityGrade");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("质量等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("质量状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingTypeCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("包装方式代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantityUnit");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("数量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weightUnit");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("重量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditorId");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("审核人工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("审核人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("审核时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditStatus");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("审核状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditOpnion");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("审核意见");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeDesc");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("品种附属码描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapType");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("利用材种类");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialRackId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("料架号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grossWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("毛重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wlAccountMark");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("物流对账标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("clAccountMark");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("财务对账标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("标签号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("firstPutinDate");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("最初入库时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("transferFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("转换标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("techStandard");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("技术标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packTransferFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("捆包转移标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("manualNo");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("手册编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hsId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("海关HS系统编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processConsignUnit");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("加工委托方（股份委托加工来源）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("originalPackId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("原始捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprStatus");
        eiColumn.setFieldLength(16);
        eiColumn.setDescName("审批状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("流程ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("手册系统编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("母手册号(最初手册号)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("父手册号(上层手册号)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customsProductNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("surfaceGrade");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("表面等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("最终用户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("最终用户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartId");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户零件号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartName");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("客户零件号名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("producingArea");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("产地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("makerNum");
        eiColumn.setFieldLength(6);
        eiColumn.setDescName("制造商代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("makerName");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("制造商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("providerCode");
        eiColumn.setFieldLength(6);
        eiColumn.setDescName("供应商代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("providerCname");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("供应商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custProviderId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("客户供料商代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productDesc");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("产品描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bigTypeDesc");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("利用材种类描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualMaterialWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("实绩领料重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifSynchronizationWl");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否同步物流标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wmsSendFlag");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("立体库WMS发送标记 1表示已发送，其他表示未发送");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wmsCxFlag");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("立体库WMS发送撤销标记 1表示已发送，其他表示未发送");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("propertyResult");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("性能判定结果(1合格;2不合格;0或者空表示未判定)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jpSign");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("精品标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jpEmbarkationSign");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("精品上机标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partsCode");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("部件编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partsName");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("部品名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTypeCode");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("车型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleName");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("车型名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quota");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("定额(KG)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jpDebatchingSign");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("精品退料标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jpFinishedProductSign");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("精品成品标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printBatchId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("打印批次号");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public VIPM0008() {
initMetaData();
}

        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the processOrderId - 生产工单号
        * @return the processOrderId
        */
        public String getProcessOrderId() {
        return this.processOrderId;
        }

        /**
        * set the processOrderId - 生产工单号
        */
        public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
        }
        /**
        * get the processDemandId - 生产需求单号
        * @return the processDemandId
        */
        public String getProcessDemandId() {
        return this.processDemandId;
        }

        /**
        * set the processDemandId - 生产需求单号
        */
        public void setProcessDemandId(String processDemandId) {
        this.processDemandId = processDemandId;
        }
        /**
        * get the processDemandSubId - 生产需求单子项号
        * @return the processDemandSubId
        */
        public String getProcessDemandSubId() {
        return this.processDemandSubId;
        }

        /**
        * set the processDemandSubId - 生产需求单子项号
        */
        public void setProcessDemandSubId(String processDemandSubId) {
        this.processDemandSubId = processDemandSubId;
        }
        /**
        * get the partId - 物料号
        * @return the partId
        */
        public String getPartId() {
        return this.partId;
        }

        /**
        * set the partId - 物料号
        */
        public void setPartId(String partId) {
        this.partId = partId;
        }
        /**
        * get the specsDesc - 规格描述
        * @return the specsDesc
        */
        public String getSpecsDesc() {
        return this.specsDesc;
        }

        /**
        * set the specsDesc - 规格描述
        */
        public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
        }
        /**
        * get the shopsign - 牌号
        * @return the shopsign
        */
        public String getShopsign() {
        return this.shopsign;
        }

        /**
        * set the shopsign - 牌号
        */
        public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
        }
        /**
        * get the packId - 捆包号
        * @return the packId
        */
        public String getPackId() {
        return this.packId;
        }

        /**
        * set the packId - 捆包号
        */
        public void setPackId(String packId) {
        this.packId = packId;
        }
        /**
        * get the matInnerId - 材料管理号
        * @return the matInnerId
        */
        public String getMatInnerId() {
        return this.matInnerId;
        }

        /**
        * set the matInnerId - 材料管理号
        */
        public void setMatInnerId(String matInnerId) {
        this.matInnerId = matInnerId;
        }
        /**
        * get the packQty - 捆包数量
        * @return the packQty
        */
        public Long getPackQty() {
        return this.packQty;
        }

        /**
        * set the packQty - 捆包数量
        */
        public void setPackQty(Long packQty) {
        this.packQty = packQty;
        }
        /**
        * get the netWeight - 净重
        * @return the netWeight
        */
        public BigDecimal getNetWeight() {
        return this.netWeight;
        }

        /**
        * set the netWeight - 净重
        */
        public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
        }
        /**
        * get the prodNameCode - 品名代码
        * @return the prodNameCode
        */
        public String getProdNameCode() {
        return this.prodNameCode;
        }

        /**
        * set the prodNameCode - 品名代码
        */
        public void setProdNameCode(String prodNameCode) {
        this.prodNameCode = prodNameCode;
        }
        /**
        * get the prodCname - 品名名称
        * @return the prodCname
        */
        public String getProdCname() {
        return this.prodCname;
        }

        /**
        * set the prodCname - 品名名称
        */
        public void setProdCname(String prodCname) {
        this.prodCname = prodCname;
        }
        /**
        * get the factoryOrderNum - 钢厂订单号
        * @return the factoryOrderNum
        */
        public String getFactoryOrderNum() {
        return this.factoryOrderNum;
        }

        /**
        * set the factoryOrderNum - 钢厂订单号
        */
        public void setFactoryOrderNum(String factoryOrderNum) {
        this.factoryOrderNum = factoryOrderNum;
        }
        /**
        * get the processDemandMaterailStatus - 生产需求单投料捆包状态
        * @return the processDemandMaterailStatus
        */
        public String getProcessDemandMaterailStatus() {
        return this.processDemandMaterailStatus;
        }

        /**
        * set the processDemandMaterailStatus - 生产需求单投料捆包状态
        */
        public void setProcessDemandMaterailStatus(String processDemandMaterailStatus) {
        this.processDemandMaterailStatus = processDemandMaterailStatus;
        }
        /**
        * get the voucherNum - 依据凭单
        * @return the voucherNum
        */
        public String getVoucherNum() {
        return this.voucherNum;
        }

        /**
        * set the voucherNum - 依据凭单
        */
        public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
        }
        /**
        * get the auditFlag - 审核标记
        * @return the auditFlag
        */
        public String getAuditFlag() {
        return this.auditFlag;
        }

        /**
        * set the auditFlag - 审核标记
        */
        public void setAuditFlag(String auditFlag) {
        this.auditFlag = auditFlag;
        }
        /**
        * get the warehouseCode - 仓库代码
        * @return the warehouseCode
        */
        public String getWarehouseCode() {
        return this.warehouseCode;
        }

        /**
        * set the warehouseCode - 仓库代码
        */
        public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
        }
        /**
        * get the warehouseName - 仓库名称
        * @return the warehouseName
        */
        public String getWarehouseName() {
        return this.warehouseName;
        }

        /**
        * set the warehouseName - 仓库名称
        */
        public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        }
        /**
        * get the locationId - 库位代码
        * @return the locationId
        */
        public String getLocationId() {
        return this.locationId;
        }

        /**
        * set the locationId - 库位代码
        */
        public void setLocationId(String locationId) {
        this.locationId = locationId;
        }
        /**
        * get the locationName - 库位名称
        * @return the locationName
        */
        public String getLocationName() {
        return this.locationName;
        }

        /**
        * set the locationName - 库位名称
        */
        public void setLocationName(String locationName) {
        this.locationName = locationName;
        }
        /**
        * get the f_packId - 父捆包号
        * @return the f_packId
        */
        public String getF_packId() {
        return this.f_packId;
        }

        /**
        * set the f_packId - 父捆包号
        */
        public void setF_packId(String f_packId) {
        this.f_packId = f_packId;
        }
        /**
        * get the f_matInnerId - 父捆包号材料管理号
        * @return the f_matInnerId
        */
        public String getF_matInnerId() {
        return this.f_matInnerId;
        }

        /**
        * set the f_matInnerId - 父捆包号材料管理号
        */
        public void setF_matInnerId(String f_matInnerId) {
        this.f_matInnerId = f_matInnerId;
        }
        /**
        * get the m_packId - 母捆包号
        * @return the m_packId
        */
        public String getM_packId() {
        return this.m_packId;
        }

        /**
        * set the m_packId - 母捆包号
        */
        public void setM_packId(String m_packId) {
        this.m_packId = m_packId;
        }
        /**
        * get the m_matInnerId - 母捆包号管理号
        * @return the m_matInnerId
        */
        public String getM_matInnerId() {
        return this.m_matInnerId;
        }

        /**
        * set the m_matInnerId - 母捆包号管理号
        */
        public void setM_matInnerId(String m_matInnerId) {
        this.m_matInnerId = m_matInnerId;
        }
        /**
        * get the packStatus - 捆包状态
        * @return the packStatus
        */
        public String getPackStatus() {
        return this.packStatus;
        }

        /**
        * set the packStatus - 捆包状态
        */
        public void setPackStatus(String packStatus) {
        this.packStatus = packStatus;
        }
        /**
        * get the yield - 成材率
        * @return the yield
        */
        public BigDecimal getYield() {
        return this.yield;
        }

        /**
        * set the yield - 成材率
        */
        public void setYield(BigDecimal yield) {
        this.yield = yield;
        }
        /**
        * get the producingAreaDesc - 产地名称
        * @return the producingAreaDesc
        */
        public String getProducingAreaDesc() {
        return this.producingAreaDesc;
        }

        /**
        * set the producingAreaDesc - 产地名称
        */
        public void setProducingAreaDesc(String producingAreaDesc) {
        this.producingAreaDesc = producingAreaDesc;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the materialLock - 原料封锁标记
        * @return the materialLock
        */
        public String getMaterialLock() {
        return this.materialLock;
        }

        /**
        * set the materialLock - 原料封锁标记
        */
        public void setMaterialLock(String materialLock) {
        this.materialLock = materialLock;
        }
        /**
        * get the heatNum - 炉号
        * @return the heatNum
        */
        public String getHeatNum() {
        return this.heatNum;
        }

        /**
        * set the heatNum - 炉号
        */
        public void setHeatNum(String heatNum) {
        this.heatNum = heatNum;
        }
        /**
        * get the chargePlat - 料台号
        * @return the chargePlat
        */
        public String getChargePlat() {
        return this.chargePlat;
        }

        /**
        * set the chargePlat - 料台号
        */
        public void setChargePlat(String chargePlat) {
        this.chargePlat = chargePlat;
        }
        /**
        * get the canReturnMaterial - 是否可以退料
        * @return the canReturnMaterial
        */
        public String getCanReturnMaterial() {
        return this.canReturnMaterial;
        }

        /**
        * set the canReturnMaterial - 是否可以退料
        */
        public void setCanReturnMaterial(String canReturnMaterial) {
        this.canReturnMaterial = canReturnMaterial;
        }
        /**
        * get the sortId - 顺序号
        * @return the sortId
        */
        public String getSortId() {
        return this.sortId;
        }

        /**
        * set the sortId - 顺序号
        */
        public void setSortId(String sortId) {
        this.sortId = sortId;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the processDemandMaterailId - 生产需求投料捆包表单据号
        * @return the processDemandMaterailId
        */
        public String getProcessDemandMaterailId() {
        return this.processDemandMaterailId;
        }

        /**
        * set the processDemandMaterailId - 生产需求投料捆包表单据号
        */
        public void setProcessDemandMaterailId(String processDemandMaterailId) {
        this.processDemandMaterailId = processDemandMaterailId;
        }
        /**
        * get the segNo - 业务账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the prodCode - 品种代码
        * @return the prodCode
        */
        public String getProdCode() {
        return this.prodCode;
        }

        /**
        * set the prodCode - 品种代码
        */
        public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
        }
        /**
        * get the storeType - 存货性质
        * @return the storeType
        */
        public String getStoreType() {
        return this.storeType;
        }

        /**
        * set the storeType - 存货性质
        */
        public void setStoreType(String storeType) {
        this.storeType = storeType;
        }
        /**
        * get the orderTypeCode - 订单性质代码(是否QZP)
        * @return the orderTypeCode
        */
        public String getOrderTypeCode() {
        return this.orderTypeCode;
        }

        /**
        * set the orderTypeCode - 订单性质代码(是否QZP)
        */
        public void setOrderTypeCode(String orderTypeCode) {
        this.orderTypeCode = orderTypeCode;
        }
        /**
        * get the customerId - 客户代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 客户代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 客户名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 客户名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }
        /**
        * get the f_packQty - 父捆包件数
        * @return the f_packQty
        */
        public Long getF_packQty() {
        return this.f_packQty;
        }

        /**
        * set the f_packQty - 父捆包件数
        */
        public void setF_packQty(Long f_packQty) {
        this.f_packQty = f_packQty;
        }
        /**
        * get the f_packWeight - 父捆包重量
        * @return the f_packWeight
        */
        public BigDecimal getF_packWeight() {
        return this.f_packWeight;
        }

        /**
        * set the f_packWeight - 父捆包重量
        */
        public void setF_packWeight(BigDecimal f_packWeight) {
        this.f_packWeight = f_packWeight;
        }
        /**
        * get the f_packFactoryOrderNum - 父捆包钢厂订单号
        * @return the f_packFactoryOrderNum
        */
        public String getF_packFactoryOrderNum() {
        return this.f_packFactoryOrderNum;
        }

        /**
        * set the f_packFactoryOrderNum - 父捆包钢厂订单号
        */
        public void setF_packFactoryOrderNum(String f_packFactoryOrderNum) {
        this.f_packFactoryOrderNum = f_packFactoryOrderNum;
        }
        /**
        * get the m_packQty - 母捆包件数
        * @return the m_packQty
        */
        public Long getM_packQty() {
        return this.m_packQty;
        }

        /**
        * set the m_packQty - 母捆包件数
        */
        public void setM_packQty(Long m_packQty) {
        this.m_packQty = m_packQty;
        }
        /**
        * get the m_packWeight - 母捆包重量
        * @return the m_packWeight
        */
        public BigDecimal getM_packWeight() {
        return this.m_packWeight;
        }

        /**
        * set the m_packWeight - 母捆包重量
        */
        public void setM_packWeight(BigDecimal m_packWeight) {
        this.m_packWeight = m_packWeight;
        }
        /**
        * get the m_packFactoryOrderNum - 母捆包钢厂订单号
        * @return the m_packFactoryOrderNum
        */
        public String getM_packFactoryOrderNum() {
        return this.m_packFactoryOrderNum;
        }

        /**
        * set the m_packFactoryOrderNum - 母捆包钢厂订单号
        */
        public void setM_packFactoryOrderNum(String m_packFactoryOrderNum) {
        this.m_packFactoryOrderNum = m_packFactoryOrderNum;
        }
        /**
        * get the finishingShuntFlag - 精整分流标记
        * @return the finishingShuntFlag
        */
        public String getFinishingShuntFlag() {
        return this.finishingShuntFlag;
        }

        /**
        * set the finishingShuntFlag - 精整分流标记
        */
        public void setFinishingShuntFlag(String finishingShuntFlag) {
        this.finishingShuntFlag = finishingShuntFlag;
        }
        /**
        * get the d_userNum - 分户号
        * @return the d_userNum
        */
        public String getD_userNum() {
        return this.d_userNum;
        }

        /**
        * set the d_userNum - 分户号
        */
        public void setD_userNum(String d_userNum) {
        this.d_userNum = d_userNum;
        }
        /**
        * get the d_userName - 分户号简称
        * @return the d_userName
        */
        public String getD_userName() {
        return this.d_userName;
        }

        /**
        * set the d_userName - 分户号简称
        */
        public void setD_userName(String d_userName) {
        this.d_userName = d_userName;
        }
        /**
        * get the onWayFlag - 在途标记
        * @return the onWayFlag
        */
        public String getOnWayFlag() {
        return this.onWayFlag;
        }

        /**
        * set the onWayFlag - 在途标记
        */
        public void setOnWayFlag(String onWayFlag) {
        this.onWayFlag = onWayFlag;
        }
        /**
        * get the putoutId - 出库单号
        * @return the putoutId
        */
        public String getPutoutId() {
        return this.putoutId;
        }

        /**
        * set the putoutId - 出库单号
        */
        public void setPutoutId(String putoutId) {
        this.putoutId = putoutId;
        }
        /**
        * get the entityPutoutDate - 实物出库时间
        * @return the entityPutoutDate
        */
        public String getEntityPutoutDate() {
        return this.entityPutoutDate;
        }

        /**
        * set the entityPutoutDate - 实物出库时间
        */
        public void setEntityPutoutDate(String entityPutoutDate) {
        this.entityPutoutDate = entityPutoutDate;
        }
        /**
        * get the periodId - 会计期间
        * @return the periodId
        */
        public String getPeriodId() {
        return this.periodId;
        }

        /**
        * set the periodId - 会计期间
        */
        public void setPeriodId(String periodId) {
        this.periodId = periodId;
        }
        /**
        * get the teamId - 班组
        * @return the teamId
        */
        public String getTeamId() {
        return this.teamId;
        }

        /**
        * set the teamId - 班组
        */
        public void setTeamId(String teamId) {
        this.teamId = teamId;
        }
        /**
        * get the workingShift - 班次
        * @return the workingShift
        */
        public String getWorkingShift() {
        return this.workingShift;
        }

        /**
        * set the workingShift - 班次
        */
        public void setWorkingShift(String workingShift) {
        this.workingShift = workingShift;
        }
        /**
        * get the teamReportId - 班报序号
        * @return the teamReportId
        */
        public String getTeamReportId() {
        return this.teamReportId;
        }

        /**
        * set the teamReportId - 班报序号
        */
        public void setTeamReportId(String teamReportId) {
        this.teamReportId = teamReportId;
        }
        /**
        * get the proxyType - 类型
        * @return the proxyType
        */
        public String getProxyType() {
        return this.proxyType;
        }

        /**
        * set the proxyType - 类型
        */
        public void setProxyType(String proxyType) {
        this.proxyType = proxyType;
        }
        /**
        * get the ifUploadFinance - 是否上传财务
        * @return the ifUploadFinance
        */
        public String getIfUploadFinance() {
        return this.ifUploadFinance;
        }

        /**
        * set the ifUploadFinance - 是否上传财务
        */
        public void setIfUploadFinance(String ifUploadFinance) {
        this.ifUploadFinance = ifUploadFinance;
        }
        /**
        * get the tradeCode - 贸易方式
        * @return the tradeCode
        */
        public String getTradeCode() {
        return this.tradeCode;
        }

        /**
        * set the tradeCode - 贸易方式
        */
        public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
        }
        /**
        * get the qualityGrade - 质量等级
        * @return the qualityGrade
        */
        public String getQualityGrade() {
        return this.qualityGrade;
        }

        /**
        * set the qualityGrade - 质量等级
        */
        public void setQualityGrade(String qualityGrade) {
        this.qualityGrade = qualityGrade;
        }
        /**
        * get the qualityStatus - 质量状态
        * @return the qualityStatus
        */
        public String getQualityStatus() {
        return this.qualityStatus;
        }

        /**
        * set the qualityStatus - 质量状态
        */
        public void setQualityStatus(String qualityStatus) {
        this.qualityStatus = qualityStatus;
        }
        /**
        * get the packingTypeCode - 包装方式代码
        * @return the packingTypeCode
        */
        public String getPackingTypeCode() {
        return this.packingTypeCode;
        }

        /**
        * set the packingTypeCode - 包装方式代码
        */
        public void setPackingTypeCode(String packingTypeCode) {
        this.packingTypeCode = packingTypeCode;
        }
        /**
        * get the quantityUnit - 数量单位
        * @return the quantityUnit
        */
        public String getQuantityUnit() {
        return this.quantityUnit;
        }

        /**
        * set the quantityUnit - 数量单位
        */
        public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
        }
        /**
        * get the weightUnit - 重量单位
        * @return the weightUnit
        */
        public String getWeightUnit() {
        return this.weightUnit;
        }

        /**
        * set the weightUnit - 重量单位
        */
        public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
        }
        /**
        * get the prodTypeId - 品种附属码
        * @return the prodTypeId
        */
        public String getProdTypeId() {
        return this.prodTypeId;
        }

        /**
        * set the prodTypeId - 品种附属码
        */
        public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
        }
        /**
        * get the auditorId - 审核人工号
        * @return the auditorId
        */
        public String getAuditorId() {
        return this.auditorId;
        }

        /**
        * set the auditorId - 审核人工号
        */
        public void setAuditorId(String auditorId) {
        this.auditorId = auditorId;
        }
        /**
        * get the auditorName - 审核人姓名
        * @return the auditorName
        */
        public String getAuditorName() {
        return this.auditorName;
        }

        /**
        * set the auditorName - 审核人姓名
        */
        public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
        }
        /**
        * get the auditTime - 审核时间
        * @return the auditTime
        */
        public String getAuditTime() {
        return this.auditTime;
        }

        /**
        * set the auditTime - 审核时间
        */
        public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
        }
        /**
        * get the auditStatus - 审核状态
        * @return the auditStatus
        */
        public String getAuditStatus() {
        return this.auditStatus;
        }

        /**
        * set the auditStatus - 审核状态
        */
        public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
        }
        /**
        * get the auditOpnion - 审核意见
        * @return the auditOpnion
        */
        public String getAuditOpnion() {
        return this.auditOpnion;
        }

        /**
        * set the auditOpnion - 审核意见
        */
        public void setAuditOpnion(String auditOpnion) {
        this.auditOpnion = auditOpnion;
        }
        /**
        * get the prodTypeDesc - 品种附属码描述
        * @return the prodTypeDesc
        */
        public String getProdTypeDesc() {
        return this.prodTypeDesc;
        }

        /**
        * set the prodTypeDesc - 品种附属码描述
        */
        public void setProdTypeDesc(String prodTypeDesc) {
        this.prodTypeDesc = prodTypeDesc;
        }
        /**
        * get the scrapType - 利用材种类
        * @return the scrapType
        */
        public String getScrapType() {
        return this.scrapType;
        }

        /**
        * set the scrapType - 利用材种类
        */
        public void setScrapType(String scrapType) {
        this.scrapType = scrapType;
        }
        /**
        * get the materialRackId - 料架号
        * @return the materialRackId
        */
        public String getMaterialRackId() {
        return this.materialRackId;
        }

        /**
        * set the materialRackId - 料架号
        */
        public void setMaterialRackId(String materialRackId) {
        this.materialRackId = materialRackId;
        }
        /**
        * get the grossWeight - 毛重
        * @return the grossWeight
        */
        public BigDecimal getGrossWeight() {
        return this.grossWeight;
        }

        /**
        * set the grossWeight - 毛重
        */
        public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
        }
        /**
        * get the wlAccountMark - 物流对账标记
        * @return the wlAccountMark
        */
        public String getWlAccountMark() {
        return this.wlAccountMark;
        }

        /**
        * set the wlAccountMark - 物流对账标记
        */
        public void setWlAccountMark(String wlAccountMark) {
        this.wlAccountMark = wlAccountMark;
        }
        /**
        * get the clAccountMark - 财务对账标记
        * @return the clAccountMark
        */
        public String getClAccountMark() {
        return this.clAccountMark;
        }

        /**
        * set the clAccountMark - 财务对账标记
        */
        public void setClAccountMark(String clAccountMark) {
        this.clAccountMark = clAccountMark;
        }
        /**
        * get the labelId - 标签号
        * @return the labelId
        */
        public String getLabelId() {
        return this.labelId;
        }

        /**
        * set the labelId - 标签号
        */
        public void setLabelId(String labelId) {
        this.labelId = labelId;
        }
        /**
        * get the firstPutinDate - 最初入库时间
        * @return the firstPutinDate
        */
        public String getFirstPutinDate() {
        return this.firstPutinDate;
        }

        /**
        * set the firstPutinDate - 最初入库时间
        */
        public void setFirstPutinDate(String firstPutinDate) {
        this.firstPutinDate = firstPutinDate;
        }
        /**
        * get the transferFlag - 转换标记
        * @return the transferFlag
        */
        public String getTransferFlag() {
        return this.transferFlag;
        }

        /**
        * set the transferFlag - 转换标记
        */
        public void setTransferFlag(String transferFlag) {
        this.transferFlag = transferFlag;
        }
        /**
        * get the techStandard - 技术标准
        * @return the techStandard
        */
        public String getTechStandard() {
        return this.techStandard;
        }

        /**
        * set the techStandard - 技术标准
        */
        public void setTechStandard(String techStandard) {
        this.techStandard = techStandard;
        }
        /**
        * get the packTransferFlag - 捆包转移标记
        * @return the packTransferFlag
        */
        public String getPackTransferFlag() {
        return this.packTransferFlag;
        }

        /**
        * set the packTransferFlag - 捆包转移标记
        */
        public void setPackTransferFlag(String packTransferFlag) {
        this.packTransferFlag = packTransferFlag;
        }
        /**
        * get the manualNo - 手册编号
        * @return the manualNo
        */
        public String getManualNo() {
        return this.manualNo;
        }

        /**
        * set the manualNo - 手册编号
        */
        public void setManualNo(String manualNo) {
        this.manualNo = manualNo;
        }
        /**
        * get the hsId - 海关HS系统编码
        * @return the hsId
        */
        public String getHsId() {
        return this.hsId;
        }

        /**
        * set the hsId - 海关HS系统编码
        */
        public void setHsId(String hsId) {
        this.hsId = hsId;
        }
        /**
        * get the processConsignUnit - 加工委托方（股份委托加工来源）
        * @return the processConsignUnit
        */
        public String getProcessConsignUnit() {
        return this.processConsignUnit;
        }

        /**
        * set the processConsignUnit - 加工委托方（股份委托加工来源）
        */
        public void setProcessConsignUnit(String processConsignUnit) {
        this.processConsignUnit = processConsignUnit;
        }
        /**
        * get the originalPackId - 原始捆包号
        * @return the originalPackId
        */
        public String getOriginalPackId() {
        return this.originalPackId;
        }

        /**
        * set the originalPackId - 原始捆包号
        */
        public void setOriginalPackId(String originalPackId) {
        this.originalPackId = originalPackId;
        }
        /**
        * get the apprStatus - 审批状态
        * @return the apprStatus
        */
        public String getApprStatus() {
        return this.apprStatus;
        }

        /**
        * set the apprStatus - 审批状态
        */
        public void setApprStatus(String apprStatus) {
        this.apprStatus = apprStatus;
        }
        /**
        * get the processId - 流程ID
        * @return the processId
        */
        public String getProcessId() {
        return this.processId;
        }

        /**
        * set the processId - 流程ID
        */
        public void setProcessId(String processId) {
        this.processId = processId;
        }
        /**
        * get the handbookId - 手册系统编号
        * @return the handbookId
        */
        public String getHandbookId() {
        return this.handbookId;
        }

        /**
        * set the handbookId - 手册系统编号
        */
        public void setHandbookId(String handbookId) {
        this.handbookId = handbookId;
        }
        /**
        * get the m_handbookId - 母手册号(最初手册号)
        * @return the m_handbookId
        */
        public String getM_handbookId() {
        return this.m_handbookId;
        }

        /**
        * set the m_handbookId - 母手册号(最初手册号)
        */
        public void setM_handbookId(String m_handbookId) {
        this.m_handbookId = m_handbookId;
        }
        /**
        * get the f_handbookId - 父手册号(上层手册号)
        * @return the f_handbookId
        */
        public String getF_handbookId() {
        return this.f_handbookId;
        }

        /**
        * set the f_handbookId - 父手册号(上层手册号)
        */
        public void setF_handbookId(String f_handbookId) {
        this.f_handbookId = f_handbookId;
        }
        /**
        * get the customsProductNum 
        * @return the customsProductNum
        */
        public String getCustomsProductNum() {
        return this.customsProductNum;
        }

        /**
        * set the customsProductNum 
        */
        public void setCustomsProductNum(String customsProductNum) {
        this.customsProductNum = customsProductNum;
        }
        /**
        * get the surfaceGrade - 表面等级
        * @return the surfaceGrade
        */
        public String getSurfaceGrade() {
        return this.surfaceGrade;
        }

        /**
        * set the surfaceGrade - 表面等级
        */
        public void setSurfaceGrade(String surfaceGrade) {
        this.surfaceGrade = surfaceGrade;
        }
        /**
        * get the finUserId - 最终用户代码
        * @return the finUserId
        */
        public String getFinUserId() {
        return this.finUserId;
        }

        /**
        * set the finUserId - 最终用户代码
        */
        public void setFinUserId(String finUserId) {
        this.finUserId = finUserId;
        }
        /**
        * get the finUserName - 最终用户名称
        * @return the finUserName
        */
        public String getFinUserName() {
        return this.finUserName;
        }

        /**
        * set the finUserName - 最终用户名称
        */
        public void setFinUserName(String finUserName) {
        this.finUserName = finUserName;
        }
        /**
        * get the custPartId - 客户零件号
        * @return the custPartId
        */
        public String getCustPartId() {
        return this.custPartId;
        }

        /**
        * set the custPartId - 客户零件号
        */
        public void setCustPartId(String custPartId) {
        this.custPartId = custPartId;
        }
        /**
        * get the custPartName - 客户零件号名称
        * @return the custPartName
        */
        public String getCustPartName() {
        return this.custPartName;
        }

        /**
        * set the custPartName - 客户零件号名称
        */
        public void setCustPartName(String custPartName) {
        this.custPartName = custPartName;
        }
        /**
        * get the producingArea - 产地
        * @return the producingArea
        */
        public String getProducingArea() {
        return this.producingArea;
        }

        /**
        * set the producingArea - 产地
        */
        public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
        }
        /**
        * get the makerNum - 制造商代码
        * @return the makerNum
        */
        public String getMakerNum() {
        return this.makerNum;
        }

        /**
        * set the makerNum - 制造商代码
        */
        public void setMakerNum(String makerNum) {
        this.makerNum = makerNum;
        }
        /**
        * get the makerName - 制造商名称
        * @return the makerName
        */
        public String getMakerName() {
        return this.makerName;
        }

        /**
        * set the makerName - 制造商名称
        */
        public void setMakerName(String makerName) {
        this.makerName = makerName;
        }
        /**
        * get the providerCode - 供应商代码
        * @return the providerCode
        */
        public String getProviderCode() {
        return this.providerCode;
        }

        /**
        * set the providerCode - 供应商代码
        */
        public void setProviderCode(String providerCode) {
        this.providerCode = providerCode;
        }
        /**
        * get the providerCname - 供应商名称
        * @return the providerCname
        */
        public String getProviderCname() {
        return this.providerCname;
        }

        /**
        * set the providerCname - 供应商名称
        */
        public void setProviderCname(String providerCname) {
        this.providerCname = providerCname;
        }
        /**
        * get the custProviderId - 客户供料商代码
        * @return the custProviderId
        */
        public String getCustProviderId() {
        return this.custProviderId;
        }

        /**
        * set the custProviderId - 客户供料商代码
        */
        public void setCustProviderId(String custProviderId) {
        this.custProviderId = custProviderId;
        }
        /**
        * get the productDesc - 产品描述
        * @return the productDesc
        */
        public String getProductDesc() {
        return this.productDesc;
        }

        /**
        * set the productDesc - 产品描述
        */
        public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
        }
        /**
        * get the bigTypeDesc - 利用材种类描述
        * @return the bigTypeDesc
        */
        public String getBigTypeDesc() {
        return this.bigTypeDesc;
        }

        /**
        * set the bigTypeDesc - 利用材种类描述
        */
        public void setBigTypeDesc(String bigTypeDesc) {
        this.bigTypeDesc = bigTypeDesc;
        }
        /**
        * get the actualMaterialWeight - 实绩领料重量
        * @return the actualMaterialWeight
        */
        public BigDecimal getActualMaterialWeight() {
        return this.actualMaterialWeight;
        }

        /**
        * set the actualMaterialWeight - 实绩领料重量
        */
        public void setActualMaterialWeight(BigDecimal actualMaterialWeight) {
        this.actualMaterialWeight = actualMaterialWeight;
        }
        /**
        * get the ifSynchronizationWl - 是否同步物流标记
        * @return the ifSynchronizationWl
        */
        public String getIfSynchronizationWl() {
        return this.ifSynchronizationWl;
        }

        /**
        * set the ifSynchronizationWl - 是否同步物流标记
        */
        public void setIfSynchronizationWl(String ifSynchronizationWl) {
        this.ifSynchronizationWl = ifSynchronizationWl;
        }
        /**
        * get the wmsSendFlag - 立体库WMS发送标记 1表示已发送，其他表示未发送
        * @return the wmsSendFlag
        */
        public String getWmsSendFlag() {
        return this.wmsSendFlag;
        }

        /**
        * set the wmsSendFlag - 立体库WMS发送标记 1表示已发送，其他表示未发送
        */
        public void setWmsSendFlag(String wmsSendFlag) {
        this.wmsSendFlag = wmsSendFlag;
        }
        /**
        * get the wmsCxFlag - 立体库WMS发送撤销标记 1表示已发送，其他表示未发送
        * @return the wmsCxFlag
        */
        public String getWmsCxFlag() {
        return this.wmsCxFlag;
        }

        /**
        * set the wmsCxFlag - 立体库WMS发送撤销标记 1表示已发送，其他表示未发送
        */
        public void setWmsCxFlag(String wmsCxFlag) {
        this.wmsCxFlag = wmsCxFlag;
        }
        /**
        * get the propertyResult - 性能判定结果(1合格;2不合格;0或者空表示未判定)
        * @return the propertyResult
        */
        public String getPropertyResult() {
        return this.propertyResult;
        }

        /**
        * set the propertyResult - 性能判定结果(1合格;2不合格;0或者空表示未判定)
        */
        public void setPropertyResult(String propertyResult) {
        this.propertyResult = propertyResult;
        }
        /**
        * get the jpSign - 精品标记
        * @return the jpSign
        */
        public String getJpSign() {
        return this.jpSign;
        }

        /**
        * set the jpSign - 精品标记
        */
        public void setJpSign(String jpSign) {
        this.jpSign = jpSign;
        }
        /**
        * get the jpEmbarkationSign - 精品上机标记
        * @return the jpEmbarkationSign
        */
        public String getJpEmbarkationSign() {
        return this.jpEmbarkationSign;
        }

        /**
        * set the jpEmbarkationSign - 精品上机标记
        */
        public void setJpEmbarkationSign(String jpEmbarkationSign) {
        this.jpEmbarkationSign = jpEmbarkationSign;
        }
        /**
        * get the partsCode - 部件编码
        * @return the partsCode
        */
        public String getPartsCode() {
        return this.partsCode;
        }

        /**
        * set the partsCode - 部件编码
        */
        public void setPartsCode(String partsCode) {
        this.partsCode = partsCode;
        }
        /**
        * get the partsName - 部品名称
        * @return the partsName
        */
        public String getPartsName() {
        return this.partsName;
        }

        /**
        * set the partsName - 部品名称
        */
        public void setPartsName(String partsName) {
        this.partsName = partsName;
        }
        /**
        * get the carTypeCode - 车型代码
        * @return the carTypeCode
        */
        public String getCarTypeCode() {
        return this.carTypeCode;
        }

        /**
        * set the carTypeCode - 车型代码
        */
        public void setCarTypeCode(String carTypeCode) {
        this.carTypeCode = carTypeCode;
        }
        /**
        * get the vehicleName - 车型名称
        * @return the vehicleName
        */
        public String getVehicleName() {
        return this.vehicleName;
        }

        /**
        * set the vehicleName - 车型名称
        */
        public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
        }
        /**
        * get the quota - 定额(KG)
        * @return the quota
        */
        public BigDecimal getQuota() {
        return this.quota;
        }

        /**
        * set the quota - 定额(KG)
        */
        public void setQuota(BigDecimal quota) {
        this.quota = quota;
        }
        /**
        * get the jpDebatchingSign - 精品退料标记
        * @return the jpDebatchingSign
        */
        public String getJpDebatchingSign() {
        return this.jpDebatchingSign;
        }

        /**
        * set the jpDebatchingSign - 精品退料标记
        */
        public void setJpDebatchingSign(String jpDebatchingSign) {
        this.jpDebatchingSign = jpDebatchingSign;
        }
        /**
        * get the jpFinishedProductSign - 精品成品标记
        * @return the jpFinishedProductSign
        */
        public String getJpFinishedProductSign() {
        return this.jpFinishedProductSign;
        }

        /**
        * set the jpFinishedProductSign - 精品成品标记
        */
        public void setJpFinishedProductSign(String jpFinishedProductSign) {
        this.jpFinishedProductSign = jpFinishedProductSign;
        }
        /**
        * get the printBatchId - 打印批次号
        * @return the printBatchId
        */
        public String getPrintBatchId() {
        return this.printBatchId;
        }

        /**
        * set the printBatchId - 打印批次号
        */
        public void setPrintBatchId(String printBatchId) {
        this.printBatchId = printBatchId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
                setProcessDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandId")), processDemandId));
                setProcessDemandSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandSubId")), processDemandSubId));
                setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
                setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
                setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
                setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
                setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
                setPackQty(NumberUtils.toLong(StringUtils.toString(map.get("packQty")), packQty));
                setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
                setProdNameCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodNameCode")), prodNameCode));
                setProdCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCname")), prodCname));
                setFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryOrderNum")), factoryOrderNum));
                setProcessDemandMaterailStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandMaterailStatus")), processDemandMaterailStatus));
                setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
                setAuditFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditFlag")), auditFlag));
                setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
                setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
                setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
                setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
                setF_packId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_packId")), f_packId));
                setF_matInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_matInnerId")), f_matInnerId));
                setM_packId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_packId")), m_packId));
                setM_matInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_matInnerId")), m_matInnerId));
                setPackStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packStatus")), packStatus));
                setYield(NumberUtils.toBigDecimal(StringUtils.toString(map.get("yield")), yield));
                setProducingAreaDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("producingAreaDesc")), producingAreaDesc));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setMaterialLock(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialLock")), materialLock));
                setHeatNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("heatNum")), heatNum));
                setChargePlat(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("chargePlat")), chargePlat));
                setCanReturnMaterial(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("canReturnMaterial")), canReturnMaterial));
                setSortId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sortId")), sortId));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setProcessDemandMaterailId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandMaterailId")), processDemandMaterailId));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setProdCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCode")), prodCode));
                setStoreType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeType")), storeType));
                setOrderTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderTypeCode")), orderTypeCode));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setF_packQty(NumberUtils.toLong(StringUtils.toString(map.get("f_packQty")), f_packQty));
                setF_packWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("f_packWeight")), f_packWeight));
                setF_packFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_packFactoryOrderNum")), f_packFactoryOrderNum));
                setM_packQty(NumberUtils.toLong(StringUtils.toString(map.get("m_packQty")), m_packQty));
                setM_packWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("m_packWeight")), m_packWeight));
                setM_packFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_packFactoryOrderNum")), m_packFactoryOrderNum));
                setFinishingShuntFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finishingShuntFlag")), finishingShuntFlag));
                setD_userNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNum")), d_userNum));
                setD_userName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userName")), d_userName));
                setOnWayFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("onWayFlag")), onWayFlag));
                setPutoutId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putoutId")), putoutId));
                setEntityPutoutDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("entityPutoutDate")), entityPutoutDate));
                setPeriodId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("periodId")), periodId));
                setTeamId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teamId")), teamId));
                setWorkingShift(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workingShift")), workingShift));
                setTeamReportId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teamReportId")), teamReportId));
                setProxyType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("proxyType")), proxyType));
                setIfUploadFinance(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifUploadFinance")), ifUploadFinance));
                setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
                setQualityGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("qualityGrade")), qualityGrade));
                setQualityStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("qualityStatus")), qualityStatus));
                setPackingTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingTypeCode")), packingTypeCode));
                setQuantityUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("quantityUnit")), quantityUnit));
                setWeightUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("weightUnit")), weightUnit));
                setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
                setAuditorId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditorId")), auditorId));
                setAuditorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditorName")), auditorName));
                setAuditTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditTime")), auditTime));
                setAuditStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditStatus")), auditStatus));
                setAuditOpnion(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditOpnion")), auditOpnion));
                setProdTypeDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeDesc")), prodTypeDesc));
                setScrapType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapType")), scrapType));
                setMaterialRackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialRackId")), materialRackId));
                setGrossWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("grossWeight")), grossWeight));
                setWlAccountMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wlAccountMark")), wlAccountMark));
                setClAccountMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("clAccountMark")), clAccountMark));
                setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));
                setFirstPutinDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("firstPutinDate")), firstPutinDate));
                setTransferFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("transferFlag")), transferFlag));
                setTechStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("techStandard")), techStandard));
                setPackTransferFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packTransferFlag")), packTransferFlag));
                setManualNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("manualNo")), manualNo));
                setHsId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hsId")), hsId));
                setProcessConsignUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processConsignUnit")), processConsignUnit));
                setOriginalPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("originalPackId")), originalPackId));
                setApprStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprStatus")), apprStatus));
                setProcessId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processId")), processId));
                setHandbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handbookId")), handbookId));
                setM_handbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_handbookId")), m_handbookId));
                setF_handbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_handbookId")), f_handbookId));
                setCustomsProductNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customsProductNum")), customsProductNum));
                setSurfaceGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("surfaceGrade")), surfaceGrade));
                setFinUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserId")), finUserId));
                setFinUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserName")), finUserName));
                setCustPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartId")), custPartId));
                setCustPartName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartName")), custPartName));
                setProducingArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("producingArea")), producingArea));
                setMakerNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("makerNum")), makerNum));
                setMakerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("makerName")), makerName));
                setProviderCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("providerCode")), providerCode));
                setProviderCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("providerCname")), providerCname));
                setCustProviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custProviderId")), custProviderId));
                setProductDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productDesc")), productDesc));
                setBigTypeDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bigTypeDesc")), bigTypeDesc));
                setActualMaterialWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actualMaterialWeight")), actualMaterialWeight));
                setIfSynchronizationWl(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifSynchronizationWl")), ifSynchronizationWl));
                setWmsSendFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wmsSendFlag")), wmsSendFlag));
                setWmsCxFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wmsCxFlag")), wmsCxFlag));
                setPropertyResult(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("propertyResult")), propertyResult));
                setJpSign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jpSign")), jpSign));
                setJpEmbarkationSign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jpEmbarkationSign")), jpEmbarkationSign));
                setPartsCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partsCode")), partsCode));
                setPartsName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partsName")), partsName));
                setCarTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTypeCode")), carTypeCode));
                setVehicleName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleName")), vehicleName));
                setQuota(NumberUtils.toBigDecimal(StringUtils.toString(map.get("quota")), quota));
                setJpDebatchingSign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jpDebatchingSign")), jpDebatchingSign));
                setJpFinishedProductSign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jpFinishedProductSign")), jpFinishedProductSign));
                setPrintBatchId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printBatchId")), printBatchId));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("processOrderId",StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
                map.put("processDemandId",StringUtils.toString(processDemandId, eiMetadata.getMeta("processDemandId")));
                map.put("processDemandSubId",StringUtils.toString(processDemandSubId, eiMetadata.getMeta("processDemandSubId")));
                map.put("partId",StringUtils.toString(partId, eiMetadata.getMeta("partId")));
                map.put("specsDesc",StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
                map.put("shopsign",StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
                map.put("packId",StringUtils.toString(packId, eiMetadata.getMeta("packId")));
                map.put("matInnerId",StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
                map.put("packQty",StringUtils.toString(packQty, eiMetadata.getMeta("packQty")));
                map.put("netWeight",StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
                map.put("prodNameCode",StringUtils.toString(prodNameCode, eiMetadata.getMeta("prodNameCode")));
                map.put("prodCname",StringUtils.toString(prodCname, eiMetadata.getMeta("prodCname")));
                map.put("factoryOrderNum",StringUtils.toString(factoryOrderNum, eiMetadata.getMeta("factoryOrderNum")));
                map.put("processDemandMaterailStatus",StringUtils.toString(processDemandMaterailStatus, eiMetadata.getMeta("processDemandMaterailStatus")));
                map.put("voucherNum",StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
                map.put("auditFlag",StringUtils.toString(auditFlag, eiMetadata.getMeta("auditFlag")));
                map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
                map.put("warehouseName",StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
                map.put("locationId",StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
                map.put("locationName",StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
                map.put("f_packId",StringUtils.toString(f_packId, eiMetadata.getMeta("f_packId")));
                map.put("f_matInnerId",StringUtils.toString(f_matInnerId, eiMetadata.getMeta("f_matInnerId")));
                map.put("m_packId",StringUtils.toString(m_packId, eiMetadata.getMeta("m_packId")));
                map.put("m_matInnerId",StringUtils.toString(m_matInnerId, eiMetadata.getMeta("m_matInnerId")));
                map.put("packStatus",StringUtils.toString(packStatus, eiMetadata.getMeta("packStatus")));
                map.put("yield",StringUtils.toString(yield, eiMetadata.getMeta("yield")));
                map.put("producingAreaDesc",StringUtils.toString(producingAreaDesc, eiMetadata.getMeta("producingAreaDesc")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("materialLock",StringUtils.toString(materialLock, eiMetadata.getMeta("materialLock")));
                map.put("heatNum",StringUtils.toString(heatNum, eiMetadata.getMeta("heatNum")));
                map.put("chargePlat",StringUtils.toString(chargePlat, eiMetadata.getMeta("chargePlat")));
                map.put("canReturnMaterial",StringUtils.toString(canReturnMaterial, eiMetadata.getMeta("canReturnMaterial")));
                map.put("sortId",StringUtils.toString(sortId, eiMetadata.getMeta("sortId")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("processDemandMaterailId",StringUtils.toString(processDemandMaterailId, eiMetadata.getMeta("processDemandMaterailId")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("prodCode",StringUtils.toString(prodCode, eiMetadata.getMeta("prodCode")));
                map.put("storeType",StringUtils.toString(storeType, eiMetadata.getMeta("storeType")));
                map.put("orderTypeCode",StringUtils.toString(orderTypeCode, eiMetadata.getMeta("orderTypeCode")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("f_packQty",StringUtils.toString(f_packQty, eiMetadata.getMeta("f_packQty")));
                map.put("f_packWeight",StringUtils.toString(f_packWeight, eiMetadata.getMeta("f_packWeight")));
                map.put("f_packFactoryOrderNum",StringUtils.toString(f_packFactoryOrderNum, eiMetadata.getMeta("f_packFactoryOrderNum")));
                map.put("m_packQty",StringUtils.toString(m_packQty, eiMetadata.getMeta("m_packQty")));
                map.put("m_packWeight",StringUtils.toString(m_packWeight, eiMetadata.getMeta("m_packWeight")));
                map.put("m_packFactoryOrderNum",StringUtils.toString(m_packFactoryOrderNum, eiMetadata.getMeta("m_packFactoryOrderNum")));
                map.put("finishingShuntFlag",StringUtils.toString(finishingShuntFlag, eiMetadata.getMeta("finishingShuntFlag")));
                map.put("d_userNum",StringUtils.toString(d_userNum, eiMetadata.getMeta("d_userNum")));
                map.put("d_userName",StringUtils.toString(d_userName, eiMetadata.getMeta("d_userName")));
                map.put("onWayFlag",StringUtils.toString(onWayFlag, eiMetadata.getMeta("onWayFlag")));
                map.put("putoutId",StringUtils.toString(putoutId, eiMetadata.getMeta("putoutId")));
                map.put("entityPutoutDate",StringUtils.toString(entityPutoutDate, eiMetadata.getMeta("entityPutoutDate")));
                map.put("periodId",StringUtils.toString(periodId, eiMetadata.getMeta("periodId")));
                map.put("teamId",StringUtils.toString(teamId, eiMetadata.getMeta("teamId")));
                map.put("workingShift",StringUtils.toString(workingShift, eiMetadata.getMeta("workingShift")));
                map.put("teamReportId",StringUtils.toString(teamReportId, eiMetadata.getMeta("teamReportId")));
                map.put("proxyType",StringUtils.toString(proxyType, eiMetadata.getMeta("proxyType")));
                map.put("ifUploadFinance",StringUtils.toString(ifUploadFinance, eiMetadata.getMeta("ifUploadFinance")));
                map.put("tradeCode",StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
                map.put("qualityGrade",StringUtils.toString(qualityGrade, eiMetadata.getMeta("qualityGrade")));
                map.put("qualityStatus",StringUtils.toString(qualityStatus, eiMetadata.getMeta("qualityStatus")));
                map.put("packingTypeCode",StringUtils.toString(packingTypeCode, eiMetadata.getMeta("packingTypeCode")));
                map.put("quantityUnit",StringUtils.toString(quantityUnit, eiMetadata.getMeta("quantityUnit")));
                map.put("weightUnit",StringUtils.toString(weightUnit, eiMetadata.getMeta("weightUnit")));
                map.put("prodTypeId",StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
                map.put("auditorId",StringUtils.toString(auditorId, eiMetadata.getMeta("auditorId")));
                map.put("auditorName",StringUtils.toString(auditorName, eiMetadata.getMeta("auditorName")));
                map.put("auditTime",StringUtils.toString(auditTime, eiMetadata.getMeta("auditTime")));
                map.put("auditStatus",StringUtils.toString(auditStatus, eiMetadata.getMeta("auditStatus")));
                map.put("auditOpnion",StringUtils.toString(auditOpnion, eiMetadata.getMeta("auditOpnion")));
                map.put("prodTypeDesc",StringUtils.toString(prodTypeDesc, eiMetadata.getMeta("prodTypeDesc")));
                map.put("scrapType",StringUtils.toString(scrapType, eiMetadata.getMeta("scrapType")));
                map.put("materialRackId",StringUtils.toString(materialRackId, eiMetadata.getMeta("materialRackId")));
                map.put("grossWeight",StringUtils.toString(grossWeight, eiMetadata.getMeta("grossWeight")));
                map.put("wlAccountMark",StringUtils.toString(wlAccountMark, eiMetadata.getMeta("wlAccountMark")));
                map.put("clAccountMark",StringUtils.toString(clAccountMark, eiMetadata.getMeta("clAccountMark")));
                map.put("labelId",StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));
                map.put("firstPutinDate",StringUtils.toString(firstPutinDate, eiMetadata.getMeta("firstPutinDate")));
                map.put("transferFlag",StringUtils.toString(transferFlag, eiMetadata.getMeta("transferFlag")));
                map.put("techStandard",StringUtils.toString(techStandard, eiMetadata.getMeta("techStandard")));
                map.put("packTransferFlag",StringUtils.toString(packTransferFlag, eiMetadata.getMeta("packTransferFlag")));
                map.put("manualNo",StringUtils.toString(manualNo, eiMetadata.getMeta("manualNo")));
                map.put("hsId",StringUtils.toString(hsId, eiMetadata.getMeta("hsId")));
                map.put("processConsignUnit",StringUtils.toString(processConsignUnit, eiMetadata.getMeta("processConsignUnit")));
                map.put("originalPackId",StringUtils.toString(originalPackId, eiMetadata.getMeta("originalPackId")));
                map.put("apprStatus",StringUtils.toString(apprStatus, eiMetadata.getMeta("apprStatus")));
                map.put("processId",StringUtils.toString(processId, eiMetadata.getMeta("processId")));
                map.put("handbookId",StringUtils.toString(handbookId, eiMetadata.getMeta("handbookId")));
                map.put("m_handbookId",StringUtils.toString(m_handbookId, eiMetadata.getMeta("m_handbookId")));
                map.put("f_handbookId",StringUtils.toString(f_handbookId, eiMetadata.getMeta("f_handbookId")));
                map.put("customsProductNum",StringUtils.toString(customsProductNum, eiMetadata.getMeta("customsProductNum")));
                map.put("surfaceGrade",StringUtils.toString(surfaceGrade, eiMetadata.getMeta("surfaceGrade")));
                map.put("finUserId",StringUtils.toString(finUserId, eiMetadata.getMeta("finUserId")));
                map.put("finUserName",StringUtils.toString(finUserName, eiMetadata.getMeta("finUserName")));
                map.put("custPartId",StringUtils.toString(custPartId, eiMetadata.getMeta("custPartId")));
                map.put("custPartName",StringUtils.toString(custPartName, eiMetadata.getMeta("custPartName")));
                map.put("producingArea",StringUtils.toString(producingArea, eiMetadata.getMeta("producingArea")));
                map.put("makerNum",StringUtils.toString(makerNum, eiMetadata.getMeta("makerNum")));
                map.put("makerName",StringUtils.toString(makerName, eiMetadata.getMeta("makerName")));
                map.put("providerCode",StringUtils.toString(providerCode, eiMetadata.getMeta("providerCode")));
                map.put("providerCname",StringUtils.toString(providerCname, eiMetadata.getMeta("providerCname")));
                map.put("custProviderId",StringUtils.toString(custProviderId, eiMetadata.getMeta("custProviderId")));
                map.put("productDesc",StringUtils.toString(productDesc, eiMetadata.getMeta("productDesc")));
                map.put("bigTypeDesc",StringUtils.toString(bigTypeDesc, eiMetadata.getMeta("bigTypeDesc")));
                map.put("actualMaterialWeight",StringUtils.toString(actualMaterialWeight, eiMetadata.getMeta("actualMaterialWeight")));
                map.put("ifSynchronizationWl",StringUtils.toString(ifSynchronizationWl, eiMetadata.getMeta("ifSynchronizationWl")));
                map.put("wmsSendFlag",StringUtils.toString(wmsSendFlag, eiMetadata.getMeta("wmsSendFlag")));
                map.put("wmsCxFlag",StringUtils.toString(wmsCxFlag, eiMetadata.getMeta("wmsCxFlag")));
                map.put("propertyResult",StringUtils.toString(propertyResult, eiMetadata.getMeta("propertyResult")));
                map.put("jpSign",StringUtils.toString(jpSign, eiMetadata.getMeta("jpSign")));
                map.put("jpEmbarkationSign",StringUtils.toString(jpEmbarkationSign, eiMetadata.getMeta("jpEmbarkationSign")));
                map.put("partsCode",StringUtils.toString(partsCode, eiMetadata.getMeta("partsCode")));
                map.put("partsName",StringUtils.toString(partsName, eiMetadata.getMeta("partsName")));
                map.put("carTypeCode",StringUtils.toString(carTypeCode, eiMetadata.getMeta("carTypeCode")));
                map.put("vehicleName",StringUtils.toString(vehicleName, eiMetadata.getMeta("vehicleName")));
                map.put("quota",StringUtils.toString(quota, eiMetadata.getMeta("quota")));
                map.put("jpDebatchingSign",StringUtils.toString(jpDebatchingSign, eiMetadata.getMeta("jpDebatchingSign")));
                map.put("jpFinishedProductSign",StringUtils.toString(jpFinishedProductSign, eiMetadata.getMeta("jpFinishedProductSign")));
                map.put("printBatchId",StringUtils.toString(printBatchId, eiMetadata.getMeta("printBatchId")));

return map;

}
}