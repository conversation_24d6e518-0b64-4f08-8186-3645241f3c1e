<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-05 14:44:45
       Version :  1.0
    tableName :meli.tlids1102
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CRANE_ORDER_ID  VARCHAR   NOT NULL,
     CRANE_ORDER_SUB_ID  VARCHAR   NOT NULL,
     PACK_ID  VARCHAR,
     LABEL_ID  VARCHAR,
     NET_WEIGHT  DECIMAL,
     QUANTITY  INTEGER,
     STATUS  VARCHAR,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL   primarykey
-->
<sqlMap namespace="LIDS1102">

    <sql id="condition">
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="craneOrderId">
            CRANE_ORDER_ID = #craneOrderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS > '00'
            AND DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1102">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        CRANE_ORDER_ID as "craneOrderId",  <!-- 行车作业清单号 -->
        CRANE_ORDER_SUB_ID as "craneOrderSubId",  <!-- 行车作业清单子项号 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        LABEL_ID as "labelId",  <!-- 标签号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        QUANTITY as "quantity",  <!-- 数量 -->
        STATUS as "status",  <!-- 状态 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids1102 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids1102 WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneOrderId">
            CRANE_ORDER_ID = #craneOrderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="craneOrderSubId">
            CRANE_ORDER_SUB_ID = #craneOrderSubId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="labelId">
            LABEL_ID = #labelId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="netWeight">
            NET_WEIGHT = #netWeight#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="quantity">
            QUANTITY = #quantity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids1102 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        CRANE_ORDER_ID,  <!-- 行车作业清单号 -->
        CRANE_ORDER_SUB_ID,  <!-- 行车作业清单子项号 -->
        PACK_ID,  <!-- 捆包号 -->
        LABEL_ID,  <!-- 标签号 -->
        NET_WEIGHT,  <!-- 净重 -->
        QUANTITY,  <!-- 数量 -->
        STATUS,  <!-- 状态 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID  <!-- ID -->
        )
        VALUES (#segNo#, #unitCode#, #craneOrderId#, #craneOrderSubId#, #packId#, #labelId#, #netWeight#, #quantity#,
        #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#,
        #archiveFlag#, #tenantUser#, #delFlag#, #uuid#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids1102 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlids1102
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        CRANE_ORDER_ID = #craneOrderId#,   <!-- 行车作业清单号 -->
        CRANE_ORDER_SUB_ID = #craneOrderSubId#,   <!-- 行车作业清单子项号 -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        LABEL_ID = #labelId#,   <!-- 标签号 -->
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        QUANTITY = #quantity#,   <!-- 数量 -->
        STATUS = #status#,   <!-- 状态 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <!--    修改行车作业清单子表状态-->
    <update id="updateStatus">
        UPDATE meli.tlids1102
        SET
        STATUS = #status#   <!-- 状态 -->
        <isNotEmpty prepend=" , " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="delFlag">
            DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        </isNotEmpty>
        WHERE 1=1
        AND SEG_NO = #segNo#
        AND CRANE_ORDER_ID = #craneOrderId#
        <isNotEmpty prepend=" AND " property="craneOrderSubId">
            CRANE_ORDER_SUB_ID = #craneOrderSubId#
        </isNotEmpty>
    </update>

    <update id="deleteFlag">
        UPDATE meli.tlids1102
        SET
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <!--    查询，根据行车作业清单主项去重-->
    <select id="queryDistinctCraneOrderId" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1102">
        SELECT DISTINCT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        CRANE_ORDER_ID as "craneOrderId"  <!-- 行车作业清单号 -->
        FROM meli.tlids1102 t WHERE 1=1
        AND t.SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="packId">
            t.PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packIds">
            t.PACK_ID IN ($packIds$)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="craneId">
            EXISTS (
            SELECT 1 FROM
            meli.tlids1101 t2
            WHERE t2.SEG_NO = t.SEG_NO
            AND t2.CRANE_ORDER_ID = t.CRANE_ORDER_ID
            AND t2.CRANE_ID = #craneId#
            AND t2.STATUS > '00')
        </isNotEmpty>
        AND t.STATUS > '00'
        AND t.DEL_FLAG= '0'
    </select>
</sqlMap>