create table meli.tlids0101
(
    SEG_NO                varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE             varchar(12)  default ' ' not null comment '业务单元代代码',
    FACTORY_AREA          varchar(20)  default ' ' not null comment '厂区代码',
    FACTORY_AREA_NAME     varchar(50)  default ' ' not null comment '厂区名称',
    AREA_TYPE             varchar(50)  default ' ' not null comment '区域类型',
    AREA_CODE             varchar(20)  default ' ' not null comment '区域代码',
    AREA_NAME             varchar(50)  default ' ' not null comment '区域名称',
    FACTORY_BUILDING      varchar(20)  default ' ' not null comment '厂房代码',
    FACTORY_BUILDING_NAME varchar(32)  default ' ' not null comment '厂房名称',
    X_INITIAL_POINT       varchar(20)  default ' ' not null comment 'X轴起始点',
    X_DESTINATION         varchar(20)  default ' ' not null comment 'X轴终到点',
    Y_INITIAL_POINT       varchar(20)  default ' ' not null comment 'Y轴起始点',
    Y_DESTINATION         varchar(20)  default ' ' not null comment 'Y轴终到点',
    AISLE_TYPE            varchar(32)  default ' ' null comment '装卸货通道类型',
    STATUS                varchar(2)   default ' ' not null comment '状态',
    REC_CREATOR           varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME      varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME       varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR           varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME      varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME       varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG          varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER           varchar(10)  default ' ' null comment '租户',
    DEL_FLAG              smallint     default 0   null comment '删除标记',
    UUID                  varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids0101_UUID_uindex
        unique (UUID)
)
    comment '厂区区域管理表' collate = utf8_bin;

