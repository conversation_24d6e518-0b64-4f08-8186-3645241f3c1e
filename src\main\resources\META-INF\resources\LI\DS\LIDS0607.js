$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();
    //编辑行
    let editorModel;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                let tabId = e.contentElement.id;
                if (tabId == 'info-1') {
                    $("#inqu_status-0-crossArea").val('')
                }
                if (tabId == 'info-2') {
                    if (IMOMUtil.checkOneSelect(resultGrid)) {
                        let rows = resultGrid.getCheckedRows();
                        $("#inqu_status-0-crossArea").val(rows[0].crossArea)
                        queryLitter();
                    } else {
                        e.preventDefault();
                        return false;
                    }


                }

            }
        }
    };

    function queryLitter() {
        var info = new EiInfo();
        var checkedRows = resultGrid.getCheckedRows();
        var model = checkedRows[0];
        $("#inqu_status-0-unitCode").val(model.unitCode);
        $("#inqu_status-0-segNo").val(model.segNo);
        $("#inqu_status-0-pollingSchemeNumber").val(model.pollingSchemeNumber);
        info.addBlock(result2Grid.getQueryInfo().getBlock("result2"));
        IPLAT.progress($("result2"), true);
        IMOMUtil.submitGridsData("result2", "LIDS0607", "queryLitter", true, function (ei) {
            if (ei.getStatus() >= 0) {
                result2Grid.setEiInfo(ei);
                result2Grid.refresh();
            }
        });

    };

    IPLATUI.EFGrid = {
        "result": {
             loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

            }, afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }

            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            }
        },
        "result2": {
            onSave: function (e) {
                console.log(e);
            },
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                var checkedRows = resultGrid.getCheckedRows();
                var model = checkedRows[0];
                result2Grid.setCellValue(0, 'segNo', model.segNo);
                result2Grid.setCellValue(0, 'unitCode', model.unitCode);
                result2Grid.setCellValue(0, 'segName', model.segName);
                result2Grid.setCellValue(0, 'pollingSchemeNumber', model.pollingSchemeNumber);//轮询方案编号
                result2Grid.setCellValue(0, 'benchmarkFactoryArea', model.benchmarkFactoryArea);//基准厂区
                result2Grid.setCellValue(0, 'benchmarkFactoryBuilding', model.benchmarkFactoryBuilding);//基准厂房
                result2Grid.setCellValue(0, 'benchmarkCrossRegional', model.benchmarkCrossRegional);//基准跨区
                result2Grid.setCellValue(0, 'warehouseCode', model.warehouseCode);//仓库代码
                result2Grid.refresh();
            }
        }
    }


    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo", notInqu: true, afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "warehouseCodeInfo", notInqu: true, afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });


    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "warehouseCodeInfo", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-windowId").val("warehouseCodeInfo");

        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                resultGrid.setCellValue(editorModel, "warehouseCode", rows[0].stockCode);
            }
        }
    });

    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
            //隐藏按钮
            iframejQuery("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-insertsave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-updatesave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-VALIDATE']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-DEVALIDATION']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-delete']").attr("style", "display:none;");
        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                resultGrid.setCellValue(editorModel, "factoryArea", rows[0].factoryArea);
                resultGrid.setCellValue(editorModel, "factoryBuilding", rows[0].factoryBuilding);
                resultGrid.setCellValue(editorModel, "crossArea", rows[0].crossArea);

            }
        }
    });

        //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo2", _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
            //隐藏按钮
            iframejQuery("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-insertsave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-updatesave-changes']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-VALIDATE']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-DEVALIDATION']").attr("style", "display:none;");
            iframejQuery("[class='i-btn-lg  k-grid-delete']").attr("style", "display:none;");

        }, afterSelect: function (rows) {
            if (rows.length > 0) {
                if (rows.length > 1) {
                    NotificationUtil({msg: "只能勾选一条数据进行操作！"}, "error");
                    return false;
                }
                result2Grid.setCellValue(editorModel, "factoryBuilding", rows[0].factoryBuilding);
                result2Grid.setCellValue(editorModel, "factoryArea", rows[0].factoryArea);
                result2Grid.setCellValue(editorModel, "crossArea", rows[0].crossArea);
            }
        }
    });


})