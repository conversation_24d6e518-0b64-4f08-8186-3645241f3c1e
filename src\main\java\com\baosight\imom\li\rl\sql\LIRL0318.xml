<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-03-26 13:15:51
   		Version :  1.0
		tableName :meli.tlirl0318 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 PROD_TYPE_ID  VARCHAR   NOT NULL, 
		 PROD_TYPE_ID_NAME  VARCHAR   NOT NULL, 
		 COIL_SHEET_FLAG  VARCHAR   NOT NULL, 
		 REVE<PERSON>E_DURATION_SECONDS  INTEGER   NOT NULL, 
		 SEARCH_DURATION_SECONDS  INTEGER   NOT NULL, 
		 TOOL_CHANGE_DURATION_SECONDS  INTEGER   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0318">

	
	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeId">
			PROD_TYPE_ID like concat ('%',#prodTypeId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeIdName">
			PROD_TYPE_ID_NAME like concat ('%',#prodTypeIdName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="coilSheetFlag">
			COIL_SHEET_FLAG = #coilSheetFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reverseDurationSeconds">
			REVERSE_DURATION_SECONDS = #reverseDurationSeconds#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="searchDurationSeconds">
			SEARCH_DURATION_SECONDS = #searchDurationSeconds#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="toolChangeDurationSeconds">
			TOOL_CHANGE_DURATION_SECONDS = #toolChangeDurationSeconds#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>
	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0318">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
			(select SEG_NAME from ${platSchema}.TVZBM81 t where t.SEG_NO = tlirl0318.SEG_NO and t.DEL_FLAG = 0) as  "segName",
				STATUS	as "status",  <!-- 状态(撤销：00、新增：10、生效：20) -->
				PROD_TYPE_ID	as "prodTypeId",  <!-- 三级品种附属码 -->
				PROD_TYPE_ID_NAME	as "prodTypeIdName",  <!-- 三级品种附属码名称 -->
				COIL_SHEET_FLAG	as "coilSheetFlag",  <!-- 板卷标记（10 卷，20 板） -->
				REVERSE_DURATION_SECONDS	as "reverseDurationSeconds",  <!-- 倒车时间 -->
				SEARCH_DURATION_SECONDS	as "searchDurationSeconds",  <!-- 找货时间 -->
				TOOL_CHANGE_DURATION_SECONDS	as "toolChangeDurationSeconds",  <!-- 切换吊具时间 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0318 tlirl0318 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="statusNo">
			STATUS != '00'
		</isNotEmpty>
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0318 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeId">
			PROD_TYPE_ID = #prodTypeId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prodTypeIdName">
			PROD_TYPE_ID_NAME = #prodTypeIdName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="coilSheetFlag">
			COIL_SHEET_FLAG = #coilSheetFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reverseDurationSeconds">
			REVERSE_DURATION_SECONDS = #reverseDurationSeconds#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="searchDurationSeconds">
			SEARCH_DURATION_SECONDS = #searchDurationSeconds#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="toolChangeDurationSeconds">
			TOOL_CHANGE_DURATION_SECONDS = #toolChangeDurationSeconds#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0318 (SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										STATUS,  <!-- 状态(撤销：00、新增：10、生效：20) -->
										PROD_TYPE_ID,  <!-- 三级品种附属码 -->
										PROD_TYPE_ID_NAME,  <!-- 三级品种附属码名称 -->
										COIL_SHEET_FLAG,  <!-- 板卷标记（10 卷，20 板） -->
										REVERSE_DURATION_SECONDS,  <!-- 倒车时间 -->
										SEARCH_DURATION_SECONDS,  <!-- 找货时间 -->
										TOOL_CHANGE_DURATION_SECONDS,  <!-- 切换吊具时间 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #status#, #prodTypeId#, #prodTypeIdName#, #coilSheetFlag#, #reverseDurationSeconds#, #searchDurationSeconds#, #toolChangeDurationSeconds#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #uuid#, #tenantId#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0318 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0318 
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					STATUS	= #status#,   <!-- 状态(撤销：00、新增：10、生效：20) -->  
					PROD_TYPE_ID	= #prodTypeId#,   <!-- 三级品种附属码 -->  
					PROD_TYPE_ID_NAME	= #prodTypeIdName#,   <!-- 三级品种附属码名称 -->  
					COIL_SHEET_FLAG	= #coilSheetFlag#,   <!-- 板卷标记（10 卷，20 板） -->  
					REVERSE_DURATION_SECONDS	= #reverseDurationSeconds#,   <!-- 倒车时间 -->  
					SEARCH_DURATION_SECONDS	= #searchDurationSeconds#,   <!-- 找货时间 -->  
					TOOL_CHANGE_DURATION_SECONDS	= #toolChangeDurationSeconds#,   <!-- 切换吊具时间 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
								TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>