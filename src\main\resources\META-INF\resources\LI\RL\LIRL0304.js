$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //编辑行
    let editorModel;
    var segNo = $("#inqu_status-0-segNo").val();
    var unitCode = $("#inqu_status-0-unitCode").val();
    // TODO 查询 按钮事件
    $("#QUERY").on("click", function (e) {
        segNo = $("#inqu_status-0-segNo").val();
        unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({msg: "请先选择业务单元代码!"}, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });
    var tab_Info;
    //是否新增
    var if_add = false;
    //是否修改
    var if_update = false;
    //得到tab组件对象
    $(window).load(function () {
        // 获取tab实例对象 确保控件已经初始化
        tab_Info = $("#info").data("kendoTabStrip");
        //* 通过当前登录人回填业务单元代码
        //* 并返回业务单元信息对象
    });

    /**
     *取消操作
     */
    $("#CANCEL").on("click", function (e) {
        //是否新增
        if_add = false;
        //是否修改
        if_update = false;
        //tab跳转
        tab_Info.select(0);
        if (resultGrid.getCheckedRows().length > 0) {
            resultGrid.unCheckAllRows();
        }
    });

    $("#SAVE").on("click", function (e) {
        //校验数据必填项
        var unitCode = $("#infof1_status-0-unitCode").val();
        if (IPLAT.isBlankString(unitCode)) {
            NotificationUtil({msg: "业务单元代码不能为空!"}, "error");
            return;
        }

        var handPointName = $("#infof1_status-0-handPointName").val();
        if (IPLAT.isBlankString(handPointName)) {
            NotificationUtil({msg: "装卸点名称不能为空!"}, "error");
            return;
        }


        var factoryArea = $("#infof1_status-0-factoryArea").val();
        if (IPLAT.isBlankString(factoryArea)) {
            NotificationUtil({msg: "厂区不能为空!"}, "error");
            return;
        }


        var vehicleNumer = $("#infof1_status-0-vehicleNumer").val();
        if (IPLAT.isBlankString(vehicleNumer)) {
            NotificationUtil({msg: "车辆最大容纳数不能为空!"}, "error");
            return;
        }

        // var loadFlag = $("#infof1_status-0-loadFlag").val();
        // var unloadFlag = $("#infof1_status-0-unloadFlag").val();
        // if (IPLAT.isBlankString(loadFlag)||IPLAT.isBlankString(unloadFlag)) {
        //     NotificationUtil({msg: "装卸或者卸货标记不能为空!"}, "error");
        //     return;
        // }

        var handPointName = $("#infof1_status-0-handOrder").val();
        if ("10"==handPointName) {
            var orderNumber = $("#infof1_status-0-orderNumber").val();
            if (IPLAT.isBlankString(orderNumber)) {
                NotificationUtil({msg: "装卸顺序为有序时,顺序号不能为空!"}, "error");
                return;
            }
        }
        /*保存当前信息*/
        IPLAT.progress($("body"), true);
        var eiInfo = new EiInfo();
        eiInfo.setByNode("infof1");
        eiInfo.set("handPointId", $("#infof1_status-0-handPointId").val());
        var type;

        //判断当前操作时新增还是修改
        var handPointId = $("#infof1_status-0-handPointId").val();
        if (IPLAT.isBlankString(handPointId)) {
            type = "insert"
        } else {
            type = "update"
        }
        EiCommunicator.send("LIRL0304", type, eiInfo, {
            onSuccess: function (ei) {
                if (-1 == ei.getStatus()) {
                    NotificationUtil({msg: ei.msg}, "error");
                    IPLAT.progress($("body"), false);
                } else {
                    IPLAT.alert({
                        message: '<b>操作成功！</b>',
                        title: '提示信息'
                    });
                    if_add = false;
                    var infof1 = document.getElementById("infof1");
                    IPLAT.fillNode(infof1, ei);
                    IPLAT.progress($("body"), false);
                    tab_Info.select(0);
                    resultGrid.dataSource.page(1);
                }
            }, onFail: function (ei) {
                IPLAT.alert(ei.getMsg());
            }
        });

    })

    //查询业务装卸点对应的业务类型
    function queryHandPoint() {
        var model = resultGrid.getCheckedRows()[0];
        var segNo = model["segNo"];

        if (IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "原因[请先选择主项业务单元代码！]"}, "error")
            return;
        }
        var eiInfo = new EiInfo();
        IPLAT.progress($("body"), false);
        eiInfo.addBlock(resultGrid.getCheckedBlockData());
        EiCommunicator.send("LIRL0309", "query", eiInfo, {
            onSuccess: function (ei) {
                if ("-1" == ei.status) {
                    NotificationUtil(ei);
                } else {
                    ei.setMsg("查询成功！");
                    NotificationUtil({msg: ei.msg}, "success");
                        result1Grid.setEiInfo(ei);
                }
                IPLAT.progress($("body"), false);
            },
            onFail: function (ei) {
                IPLAT.progress($("body"), false);
                // NotificationUtil({msg: ei.msg}, "error");
                NotificationUtil(ei);
                return false;
            }
        }, {async: false});
        var detail = document.getElementById("info-3");
        IPLAT.clearNode(detail);
    }

    //查询装卸点配置站点
    function querySiteName() {

        if (IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "原因[请先选择主项业务单元代码！]"}, "error")
            return;
        }
        var eiInfo = new EiInfo();
        IPLAT.progress($("body"), false);
        eiInfo.addBlock(resultGrid.getCheckedBlockData());
        EiCommunicator.send("LIRL0315", "query", eiInfo, {
            onSuccess: function (ei) {
                if ("-1" == ei.status) {
                    NotificationUtil(ei);
                } else {
                    ei.setMsg("查询成功！");
                    NotificationUtil({msg: ei.msg}, "success");
                    result2Grid.setEiInfo(ei);
                }
                IPLAT.progress($("body"), false);
            },
            onFail: function (ei) {
                IPLAT.progress($("body"), false);
                // NotificationUtil({msg: ei.msg}, "error");
                NotificationUtil(ei);
                return false;
            }
        }, {async: false});
        var detail = document.getElementById("info-4");
        IPLAT.clearNode(detail);
    }

    /**
     * tab页事件
     * @type
     */
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                //得到tab当前页
                var tabId = e.contentElement.id;
                switch (tabId) {
                    case 'info-1':
                        if (if_add || if_update) {
                            NotificationUtil({msg: "请先保存数据或取消!"}, "error");
                            e.preventDefault();
                            return;
                        }
                        //重置表格
                        /*var block = resultGrid.getBlockData();*/
                        /*var eiInfo = new EiInfo();
                        if (block != null) {
                            block.setRows([]);
                            eiInfo.addBlock(block);
                            resultGrid.setEiInfo(eiInfo);
                        }*/
                        //禁用按钮
                        $("#CANCEL").attr("disabled", true);
                        /*if (unitCode!="") {
                            resultGrid.dataSource.page(1);
                        }*/
                        return;
                    case 'info-2':
                        var model = null;
                        //此处未选择数据进入详情
                        if (if_add || resultGrid.getCheckedRows().length <= 0 && resultGrid.getSelectedRows().length <= 0) {
                            //将主项信息初始化

                            IPLAT.EFPopupInput.enable($("#infof1_status-0-unitCode"), false);
                            $("#infof1_status-0-unitCode").val("");

                            $("#infof1_status-0-segName").val("");
                            IPLAT.EFInput.readonly($("#infof1_status-0-segName"), true);

                            $("#infof1_status-0-segNo").val("");
                            IPLAT.EFInput.readonly($("#infof1_status-0-segNo"), true);

                            IPLAT.EFSelect.value($("#infof1_status-0-status"), "10");
                            IPLAT.EFSelect.readonly($("#infof1_status-0-status"), true);

                            // IPLAT.EFSelect.value($("#infof1_status-0-factoryArea"), " ");
                            // IPLAT.EFSelect.readonly($("#infof1_status-0-factoryArea"), true);
                            disabledInfo(true);
                            disabledButton(true);
                            break;
                        } else if (resultGrid.getSelectedRows().length <= 0) {
                            //勾选的主项信息
                            model = resultGrid.getCheckedRows()[0];
                        } else {
                            //选中的主项信息
                            model = resultGrid.getSelectedRows()[0];
                        }
                        //加载动画
                        // IPLAT.progress($("body"), true);
                        //设置主项信息
                        $("#infof1_status-0-handPointId").val(model["handPointId"]);
                        $("#infof1_status-0-handPointName").val(model["handPointName"]);
                        $("#infof1_status-0-loadingChannelId").val(model["loadingChannelId"]);
                        $("#infof1_status-0-loadingChannelName").val(model["loadingChannelName"]);
                        $("#infof1_status-0-drivingCode").val(model["drivingCode"]);
                        $("#infof1_status-0-drivingName").val(model["drivingName"]);
                        $("#infof1_status-0-handOrder").val(model["handOrder"]);
                        $("#infof1_status-0-orderNumber").val(model["orderNumber"]);
                        $("#infof1_status-0-vehicleNumer").val(model["vehicleNumer"]);
                        $("#infof1_status-0-standardCapacity").val(model["standardCapacity"]);
                        $("#infof1_status-0-closeReason").val(model["closeReason"]);
                        $("#infof1_status-0-loadFlag").val(model["loadFlag"]);
                        if (model["loadFlag"] == "1"){
                            $("#loadFlagMark").prop("checked",true);
                        }else {
                            $("#loadFlagMark").prop("checked",false);
                        }
                        if (model["unloadFlag"] == "1") {
                            $("#unloadFlagMark").prop("checked",true);
                        }else {
                            $("#unloadFlagMark").prop("checked",false);
                        }
                        IPLAT.EFPopupInput.enable($("#infof1_status-0-unitCode"), false);
                        $("#infof1_status-0-unitCode").val(model["unitCode"]);

                        $("#infof1_status-0-segNo").val(model["segNo"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-segNo"), true);

                        $("#infof1_status-0-segName").val(model["segName"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-segName"), true);

                        IPLAT.EFSelect.value($("#infof1_status-0-status"), model["status"]);
                        IPLAT.EFSelect.readonly($("#infof1_status-0-status"), true);
                        $("#infof1_status-0-factoryArea").val(model["factoryArea"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-factoryArea"), true);

                        $("#infof1_status-0-factoryAreaName").val(model["factoryAreaName"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-factoryAreaName"), true);
                        $("#infof1_status-0-factoryBuilding").val(model["factoryBuilding"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuilding"), true);
                        $("#infof1_status-0-factoryBuildingName").val(model["factoryBuildingName"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuildingName"), true);
                        $("#infof1_status-0-craneId").val(model["craneId"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-craneId"), true);
                        $("#infof1_status-0-craneName").val(model["craneName"]);
                        IPLAT.EFInput.readonly($("#infof1_status-0-craneName"), true);
                        // IPLAT.EFSelect.value($("#infof1_status-0-factoryArea"), model["factoryArea"]);
                        // IPLAT.EFSelect.readonly($("#infof1_status-0-factoryArea"), true);

                        disabledInfo1(true);
                        //使子项相关按钮禁用
                        disabledButton(true);
                        return;
                    case 'info-3':
                        var checkedRows = resultGrid.getCheckedRows();
                        var length = checkedRows.length;
                        if (length > 1) {
                            NotificationUtil({msg: "操作失败，原因[只能勾选一条数据！]"}, "error");
                            e.preventDefault(); //阻止切换第二个tab
                            return;
                        }
                        if (resultGrid.getCheckedRows().length <= 0) {
                            //是否选中
                            NotificationUtil("操作失败，请选择一条记录再进行修改!", "error");
                            e.preventDefault();
                            return;
                        }
                        var status = checkedRows[0].status;
                        if ('30'!=status){
                            NotificationUtil("操作失败，原因[只能操作启用状态的装卸点！]", "error");
                            e.preventDefault();
                            return;
                        }
                        //查询装卸点对应的装卸类型信息
                        queryHandPoint();
                        result1Grid.refresh();

                    case 'info-4':
                        var checkedRows = resultGrid.getCheckedRows();
                        var length = checkedRows.length;
                        if (length > 1) {
                            NotificationUtil({msg: "操作失败，原因[只能勾选一条数据！]"}, "error");
                            e.preventDefault(); //阻止切换第二个tab
                            return;
                        }
                        if (resultGrid.getCheckedRows().length <= 0) {
                            //是否选中
                            NotificationUtil("操作失败，请选择一条记录再进行修改!", "error");
                            e.preventDefault();
                            return;
                        }
                        var status = checkedRows[0].status;
                        if ('30'!=status){
                            NotificationUtil("操作失败，原因[只能操作启用状态的装卸点！]", "error");
                            e.preventDefault();
                            return;
                        }
                        //查询装卸点对应的装卸类型信息
                        querySiteName();
                        result2Grid.refresh();

                }


            }
        }
    };

    function disabledInfo1(falg, ifFlag) {
        // IPLAT.EFSelect.readonly($("#infof1_status-0-factoryArea"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-handPointId"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-handPointName"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-handOrder"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-orderNumber"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-vehicleNumer"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-standardCapacity"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-closeReason"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-factoryAreaName"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuilding"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuildingName"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-craneId"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-craneName"), true);
    }

    function disabledInfo2() {
        // IPLAT.EFSelect.readonly($("#infof1_status-0-factoryArea"), falg);
        IPLAT.EFInput.readonly($("#infof1_status-0-handPointId"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-handPointName"), false);
        IPLAT.EFSelect.readonly($("#infof1_status-0-handOrder"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-orderNumber"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-vehicleNumer"), false);
        IPLAT.EFInput.readonly($("#infof1_status-0-standardCapacity"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-closeReason"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-factoryAreaName"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuilding"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuildingName"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-craneId"), true);
        IPLAT.EFInput.readonly($("#infof1_status-0-craneName"), true);

        IPLAT.EFDatePicker.readonly($("#infof1_status-0-expectedRecoveryTime"), true);
    }

    function disabledInfo(falg, ifFlag) {
        // IPLAT.EFSelect.value($("#infof1_status-0-factoryArea"), " ");
        // IPLAT.EFSelect.readonly($("#infof1_status-0-factoryArea"), falg);

        $("#infof1_status-0-handPointId").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-handPointId"), ifFlag);

        $("#infof1_status-0-handPointName").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-handPointName"), falg);

        $("#infof1_status-0-handOrder").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-handOrder"), falg);

        $("#infof1_status-0-orderNumber").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-orderNumber"), falg);

        $("#infof1_status-0-vehicleNumer").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-vehicleNumer"), falg);

        $("#infof1_status-0-standardCapacity").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-standardCapacity"), falg);

        $("#infof1_status-0-closeReason").val("");
        IPLAT.EFInput.readonly($("#infof1_status-0-closeReason"), falg);

        $("#infof1_status-0-expectedRecoveryTime").data("kendoDatePicker").enable(!falg);

    }

    function disabledButton(flag) {
        $("#CANCEL").attr("disabled", flag);
        $("#SAVE").attr("disabled", flag);

    }

    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo01",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },

                {
                    field: "loadFlag",
                    headerTemplate: "装货标记",
                    template: function (e) {
                        if (e.loadFlag == "1") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "70px"
                },
                {
                    field: "unloadFlag",
                    headerTemplate: "卸货标记",
                    template: function (e) {
                        if (e.unloadFlag == "1") {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" checked="true" />'*/
                            return '<span>\u2714</span>'
                        } else {
                            /*return '<input type="checkbox" class="kendo-check-box check-one" style="text-align: center" />'*/
                            return '<span></span>'
                        }
                    },
                    resizable: false,
                    enable: false,
                    readonly: true,
                    position: "front",
                    width: "70px"
                },
                {
                    field: "craneId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "行车编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "eArchivesInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "行车编码查询"
                            })
                        }
                    }
                },

            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                // 获取勾选数据，
                $("#INSERTSAVEM").on("click", function (e) {
                    var eiInfo = new EiInfo();
                    //新增状态为true
                    if_add = true;
                    //将子项详情表格设空
                    var block = resultGrid.getBlockData();
                    if (block != null) {
                        block.setRows([]);
                        eiInfo.addBlock(block);
                        resultGrid.setEiInfo(eiInfo);
                    }

                    //切换到详情页
                    tab_Info.select(1);
                    //赋值
                    $("#infof1_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                    $("#infof1_status-0-segNo").val($("#inqu_status-0-segNo").val());
                    $("#infof1_status-0-segName").val($("#inqu_status-0-segName").val());
                    $("#infof1_status-0-factoryArea").val();
                    $("#infof1_status-0-factoryAreaName").val();
                    $("#infof1_status-0-factoryBuilding").val();
                    $("#infof1_status-0-factoryBuildingName").val();
                    $("#infof1_status-0-craneId").val();
                    $("#infof1_status-0-craneName").val();
                    IPLAT.EFSelect.value($("#infof1_status-0-status"), "10");
                    IPLAT.EFSelect.readonly($("#infof1_status-0-status"), true);
                    IPLAT.EFPopupInput.enable($("#infof1_status-0-unitCode"), true);
                    IPLAT.EFInput.readonly($("#infof1_status-0-factoryAreaName"), true);
                    IPLAT.EFInput.readonly($("#infof1_status-0-factoryAreaName"), true);
                    IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuilding"), true);
                    IPLAT.EFInput.readonly($("#infof1_status-0-factoryBuildingName"), true);
                    IPLAT.EFInput.readonly($("#infof1_status-0-craneName"), true);

                    disabledInfo(false, true);
                    //放开按钮
                    disabledButton(false);
                    $("#loadFlagMark").prop("checked",false);
                    $("#unloadFlagMark").prop("checked",false);
                });


                // 获取勾选数据，
                $("#UPDATESAVEM").on("click", function (e) {
                    var flag=false;
                    var model;
                    var handPointId = $("#infof1_status-0-handPointId").val();
                    var checkedRows = resultGrid.getCheckedRows();
                    if (resultGrid.getCheckedRows().length <= 0 && resultGrid.getSelectedRows().length <= 0) {
                        //是否选中
                        NotificationUtil("操作失败，请选择一条记录再进行修改!", "error");
                        e.preventDefault();
                        return;
                    } else if (resultGrid.getSelectedRows().length = 1) {
                        //勾选的主项信息
                        model = resultGrid.getCheckedRows()[0];
                    }

                    if (checkedRows.length > 1) {
                        IPLAT.alert("只能对一行数据进行修改!");
                        return;
                    }
                    if (model["status"] == "10"){
                        var flag=true;
                    }else if(model["status"] == "30"){
                        var flag=true;
                    }

                    /*判断当前主项状态*/
                    if (flag==false) {
                        IPLAT.alert("当前状态不为新增或者启动，不可修改！!");
                        return;
                    }

                    //切换到详情页
                    tab_Info.select(1);
                    disabledInfo1(false, true);
                    //放开按钮
                    disabledButton(false);
                    if(model["status"] == "30"){
                        disabledInfo2();
                    }
                });
                /**
                 * 确认
                 */
                $("#CONFIRM").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0304", "confirm", true, function (e) {
                        if (e.status==-1){
                            resultGrid.setEiInfo(e);
                        }else {
                            resultGrid.setEiInfo(e);
                            resultGrid.dataSource.page(1);
                        }
                    }, eiInfo);
                });
                /**
                 * 反确认
                 */
                $("#CONFIRMNO").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0304", "confirmNo", true, function (e) {
                        if (e.status==-1){
                            resultGrid.setEiInfo(e);
                        }else {
                            resultGrid.setEiInfo(e);
                            resultGrid.dataSource.page(1);
                        }
                    }, eiInfo);
                });
                /**
                 * 启用
                 */
                $("#ENABLE").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0304", "enable", true, function (e) {
                        if (e.status==-1){
                            resultGrid.setEiInfo(e);
                        }else {
                            resultGrid.setEiInfo(e);
                            resultGrid.dataSource.page(1);
                        }
                    }, eiInfo);
                });

                /**
                 * 停用
                 */
                $("#DISABLE").click(function () {
                    var eiInfo = new EiInfo();
                    let closeReason = $("#infof1_status-0-closeReason").val();
                    let expectedRecoveryTime = $("#infof1_status-0-expectedRecoveryTime").val();
                    // if (closeReason==""||expectedRecoveryTime==""){
                    //     IPLAT.alert("请检查选中数据是否填写停用原因或预计恢复时间，不允许停用!");
                    //     return;
                    // }
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0304", "disable", true, function (e) {
                        if (e.status==-1){
                            resultGrid.setEiInfo(e);
                        }else {
                            resultGrid.setEiInfo(e);
                            resultGrid.dataSource.page(1);
                        }
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            /**
             * 编辑前
             */
            beforeEdit: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if(!IPLAT.isBlankString(windowId)){
                    e.preventDefault();
                    return;
                }
            }
        },
        "result1": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo04",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                },
                {
                    field: "scrapType",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "可利用材种类",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "scrapTypeWin",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "可利用材种类查询"
                            })
                        }
                    }
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

                /**
                 * 新增
                 */
                $("#INSERTSAVEC").click(function () {
                    if (result1Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result1");
                    info.addBlock(result1Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0309", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                // result1Grid.dataSource.page(1);
                                queryHandPoint();
                                result1Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                })

                /**
                 * 修改
                 */
                $("#UPDATESAVEC").click(function () {
                    if (result1Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行修改！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result1");
                    info.addBlock(result1Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0309", "update", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                // result1Grid.dataSource.page(1);
                                queryHandPoint();
                                result1Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                })
                /**
                 * 删除
                 */
                $("#DELETEC").click(function () {
                    if (result1Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行删除！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result1");
                    info.addBlock(result1Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0309", "delete", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                // result1Grid.dataSource.page(1);
                                queryHandPoint();
                                result1Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                })
                /**
                 * 启用
                 */
                $("#ENABLE1").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result1");
                    IMOMUtil.submitGridsData("result1", "LIRL0309", "enable", true, function (e) {
                        if (e.status==-1){
                            result1Grid.setEiInfo(e);
                        }else {
                            result1Grid.setEiInfo(e);
                            queryHandPoint();
                            result1Grid.refresh();
                        }
                    }, eiInfo);
                });

                /**
                 * 停用
                 */
                $("#DISABLE1").click(function () {
                    var eiInfo = new EiInfo();
                    // if (closeReason==""||expectedRecoveryTime==""){
                    //     IPLAT.alert("请检查选中数据是否填写停用原因或预计恢复时间，不允许停用!");
                    //     return;
                    // }
                    eiInfo.set("block", "result1");
                    IMOMUtil.submitGridsData("result1", "LIRL0309", "disable", true, function (e) {
                        if (e.status==-1){
                            result1Grid.setEiInfo(e);
                        }else {
                            result1Grid.setEiInfo(e);
                            queryHandPoint();
                            result1Grid.refresh();
                        }
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            /**
             * EFGrid新增行之前触发的事件，可以根据业务逻辑控制是否进行新增
             * @param e 事件对象
             * e.sender Grid对象
             * e.preventDefault 阻止事件发生
             */
            // beforeAdd: function (e) {
            //     var logic = true;
            //     if (logic) {// 通过业务逻辑判断, 控制是否进行新增
            //         var checkRows = resultGrid.getCheckedRows();
            //         for (i = 0; i < checkRows.length; i++) {
            //             var varModel = resultGrid.getCheckedRows()[i];
            //             result1Grid.setCellValue(0, "handPointId", varModel.handPointId);
            //             result1Grid.setCellValue(0, "unitCode", varModel.unitCode);
            //             result1Grid.setCellValue(0, "segName", varModel.segName);
            //         }
            //     }
            //     console.log("beforeAdd");
            // },

            beforeEdit: function (e) {
                console.log("正准备编辑第" + e.row + "行数据");
                // if (e.field === "handPointId") {
                // 判断单元格 field 禁止编辑
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    result1Grid.setCellValue(0, "handPointId", varModel.handPointId);
                    result1Grid.setCellValue(0, "unitCode", varModel.unitCode);
                    result1Grid.setCellValue(0, "segName", varModel.segName);
                }
                // }
            }
        },
        "result2": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: false,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
            },
            columns: [
                {
                    field: "unitCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "业务单元代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "unitInfo04",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "业务套账查询"
                            })
                        }
                    }
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件

                /**
                 * 新增
                 */
                $("#INSERTSAVE2").click(function () {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行保存！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result2");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0315", "insert", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                // result1Grid.dataSource.page(1);
                                querySiteName();
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                })

                /**
                 * 修改
                 */
                $("#UPDATESAVE2").click(function () {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行修改！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result2");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0315", "update", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                // result1Grid.dataSource.page(1);
                                querySiteName();
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                })
                /**
                 * 删除
                 */
                $("#DELETEC2").click(function () {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("新增失败，原因[请勾选记录后再进行删除！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result2");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0315", "delete", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                // result1Grid.dataSource.page(1);
                                querySiteName();
                                result2Grid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                })
                /**
                 * 启用
                 */
                $("#ENABLE2").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result2");
                    IMOMUtil.submitGridsData("result2", "LIRL0315", "enable", true, function (e) {
                        if (e.status==-1){
                            result2Grid.setEiInfo(e);
                        }else {
                            result2Grid.setEiInfo(e);
                            querySiteName();
                            result2Grid.refresh();
                        }
                    }, eiInfo);
                });

                /**
                 * 停用
                 */
                $("#DISABLE2").click(function () {
                    var eiInfo = new EiInfo();
                    // if (closeReason==""||expectedRecoveryTime==""){
                    //     IPLAT.alert("请检查选中数据是否填写停用原因或预计恢复时间，不允许停用!");
                    //     return;
                    // }
                    eiInfo.set("block", "result2");
                    IMOMUtil.submitGridsData("result2", "LIRL0315", "disable", true, function (e) {
                        if (e.status==-1){
                            result2Grid.setEiInfo(e);
                        }else {
                            result2Grid.setEiInfo(e);
                            querySiteName();
                            result2Grid.refresh();
                        }
                    }, eiInfo);
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                console.log("正准备编辑第" + e.row + "行数据");
                // if (e.field === "handPointId") {
                // 判断单元格 field 禁止编辑
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    result2Grid.setCellValue(0, "handPointId", varModel.handPointId);
                    result2Grid.setCellValue(0, "unitCode", varModel.unitCode);
                    result2Grid.setCellValue(0, "segName", varModel.segName);
                }
                // }
            }
        }
    };
    IPLATUI.EFPopupInput = {
        "inqu_status-0-factoryArea": {
            clearInput: function (e) {
                $("#inqu_status-0-factoryArea").val('');
                $("#inqu_status-0-factoryAreaName").val('');
                $("#inqu_status-0-factoryBuilding").val('');
                $("#inqu_status-0-factoryBuildingName").val('');
            }
        }
    };
    IPLATUI.EFWindow = {
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "unitInfo01": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo01");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo01Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = resultGrid.getCheckedRows();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = resultGrid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },

        "unitInfo02": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo02Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo02");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo02Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result1")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    $("#infof1_status-0-unitCode").val(row[0].unitCode);
                    $("#infof1_status-0-segNo").val(row[0].segNo);
                    $("#infof1_status-0-segName").val(row[0].segName);
                    resultGrid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },
        "unitInfo04": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = unitInfo04Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("unitInfo04");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfo04Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    var checkRows = result1Grid.getCheckedRows();
                    for (i = 0; i < checkRows.length; i++) {
                        var varModel = result1Grid.getCheckedRows()[i];
                        varModel.unitCode = row[0].unitCode;
                        varModel.segNo = row[0].segNo;
                        varModel.segName = row[0].segName;
                    }
                    result1Grid.refresh();
                    dataGrid.unCheckAllRows();
                }
            }
        },

        "factory": {
            // 打开窗口事件
            open: function (e) {

                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                    NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                    e.preventDefault();
                    return;
                }
                var $iframe = factoryWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu_status-0-windowId").val("factory");
                iframejQuery("#inqu_status-0-segNo").val(segNo);
                iframejQuery("#inqu_status-0-unitCode").val(unitCode);
                iframejQuery("#inqu_status-0-segName").val(segName);
                iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
                iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
                iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
                // //状态为启用
                // iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("10");
                // iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
                //隐藏按钮
                // iframejQuery("[class='i-btn-lg  k-grid-INSERT']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-insertsave-changes']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-updatesave-changes']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-VALIDATE']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-DEVALIDATION']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-CONFIRM']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-delete']").attr("style", "display:none;");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = factoryWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    $("#infof1_status-0-factoryArea").val(row[0].factoryArea);
                    $("#infof1_status-0-factoryAreaName").val(row[0].factoryAreaName);
                    $("#infof1_status-0-factoryBuilding").val(row[0].factoryBuilding);
                    $("#infof1_status-0-factoryBuildingName").val(row[0].factoryBuildingName);
                }
            }
        },

        "scrapTypeWin":{
            // 打开窗口事件
            open: function (e) {
                var $iframe = scrapTypeWinWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu2_status-0-windowId").val("scrapTypeWin");
            },
            close: function (e) {
                var $iframe = scrapTypeWinWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result").data("kendoGrid");
                // 也可以使用如下的方式获取dataGrid
                var row = dataGrid.getCheckedRows();
                if (row.length > 0) {
                    let strScrapType = "";
                    for (i = 0; i < row.length; i++) {
                        let varModel = row[i];
                        let scrapType = varModel["scrapType"];
                        let bigTypeDesc = varModel["bigTypeDesc"];
                        //判断strScrapType是否为空
                        if (strScrapType == "") {
                            strScrapType = scrapType+":"+ bigTypeDesc;
                        } else {
                            strScrapType = strScrapType + ";" + scrapType+":"+ bigTypeDesc;
                        }
                    }
                    let checkRows = result1Grid.getCheckedRows();
                    if (checkRows.length > 0) {
                        for (i = 0; i < checkRows.length; i++) {
                            let varModel = checkRows[i];
                            varModel.scrapType = strScrapType
                        }
                        result1Grid.refresh();
                     }
                }else{
                    let checkRows = result1Grid.getCheckedRows();
                    if (checkRows.length > 0) {
                        for (i = 0; i < checkRows.length; i++) {
                            let varModel = checkRows[i];
                            varModel.scrapType = "";
                        }
                        result1Grid.refresh();
                    }
                }
            }
        },




        "factory1": {
            // 打开窗口事件
            open: function (e) {

                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                    NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                    e.preventDefault();
                    return;
                }
                var $iframe = factory1Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#inqu_status-0-windowId").val("factory1");
                iframejQuery("#inqu_status-0-segNo").val(segNo);
                iframejQuery("#inqu_status-0-unitCode").val(unitCode);
                iframejQuery("#inqu_status-0-segName").val(segName);
                iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
                iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
                iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
                // //状态为启用
                // iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("10");
                // iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
                //隐藏按钮
                // iframejQuery("[class='i-btn-lg  k-grid-INSERT']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-insertsave-changes']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-updatesave-changes']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-add']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-VALIDATE']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-DEVALIDATION']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-CONFIRM']").attr("style", "display:none;");
                // iframejQuery("[class='i-btn-lg  k-grid-delete']").attr("style", "display:none;");
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = factory1Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    $("#inqu_status-0-factoryArea").val(row[0].factoryArea);
                    $("#inqu_status-0-factoryAreaName").val(row[0].factoryAreaName);
                    $("#inqu_status-0-factoryBuilding").val(row[0].factoryBuilding);
                    $("#inqu_status-0-factoryBuildingName").val(row[0].factoryBuildingName);
                }
            }
        },}
    //厂区区域管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "channel",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-factoryArea").val($("#infof1_status-0-factoryArea").val());
            iframejQuery("#inqu_status-0-factoryAreaName").val($("#infof1_status-0-factoryAreaName").val());
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
            //类型为过跨通道
            iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").value("40");
            iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#infof1_status-0-loadingChannelId").val(rows[0].areaCode);
                $("#infof1_status-0-loadingChannelName").val(rows[0].areaName);
            }
        }
    });

    //设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "eArchivesInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            // iframejQuery("#inqu_status-0-processCategory").val("LE");//起重设备
            iframejQuery("#inqu_status-0-processCategorySub").val("HC");//行车

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {

                let strScrapType = "";
                let strScrapTypeName = "";

                for (let i = 0; i < rows.length; i++) {
                    let varModel = rows[i];
                    let scrapType = varModel["craneId"];
                    let bigTypeDesc = varModel["craneName"];
                    //判断strScrapType是否为空
                    if (strScrapType == "") {
                        strScrapType = scrapType;
                        strScrapTypeName = bigTypeDesc;
                    } else {
                        strScrapType = strScrapType + "," + scrapType;
                        strScrapTypeName = strScrapTypeName + "," + bigTypeDesc;
                    }
                }

                $("#infof1_status-0-craneId").val(strScrapType);
                $("#infof1_status-0-craneName").val(strScrapTypeName);
            }
        }
    });

});
