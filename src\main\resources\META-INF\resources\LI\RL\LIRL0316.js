$(function () {
    var uploadList = [];
    var uploadIndex = -1;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });
    $("#QUERYFILE").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        const row = resultGrid.getCheckedRows();
        if (row.length > 0){
            let voucherNum =row[0].voucherNum;
            var inInfo = new EiInfo();
            inInfo.set("voucherNum", voucherNum);
            EiCommunicator.send("LIRL0316", "queryFile", inInfo, {
                onSuccess: function (ei) {
                    if ("-1" == ei.status) {
                        NotificationUtil(ei);
                    }else {
                        AttachmentDetailsGrid.setEiInfo(ei);
                    }
                },
                onFail: function (ei) {
                    NotificationUtil(ei);
                    return false;
                }
            })
        }

    });
    $("#QUERYDELEGATE").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        const voucherNum = $("#inqu2_status-0-voucherNum").val();
        const driverName = $("#inqu2_status-0-driverName").val();
        const driverTel = $("#inqu2_status-0-driverTel").val();
        const driverIdentity = $("#inqu2_status-0-driverIdentity").val();
        var inInfo = new EiInfo();
        inInfo.set("segNo", segNo);
        if (!IPLAT.isBlankString(voucherNum)){
            inInfo.set("voucherNum", voucherNum);
        }
        if (!IPLAT.isBlankString(driverName)){
            inInfo.set("driverName", driverName);
        }
        if (!IPLAT.isBlankString(driverTel)){
            inInfo.set("driverTel", driverTel);
        }
        if (!IPLAT.isBlankString(voucherNum)){
            inInfo.set("driverIdentity", driverIdentity);
        }
        EiCommunicator.send("LIRL0316", "queryDelegate", inInfo, {
            onSuccess: function (ei) {
                if ("-1" == ei.status) {
                    NotificationUtil(ei);
                } else {
                    billOfLadingGrid.setEiInfo(ei);
                }
            },
            onFail: function (ei) {
                NotificationUtil(ei);
                return false;
            }
        });
    });

    $("#QUERY2").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        var inInfo = new EiInfo();
        inInfo.set("segNo", segNo);
        inInfo.set("status", "20");
        EiCommunicator.send("LIRL0506", "query2", inInfo, {
            onSuccess: function (ei) {
                if ("-1" == ei.status) {
                    NotificationUtil(ei);
                } else {
                    attachmentPrintRGrid.setEiInfo(ei);
                }
            },
            onFail: function (ei) {
                NotificationUtil(ei);
                return false;
            }
        });
    });
    $("#FILEDELETE").on("click", function (e) {

        const row = AttachmentDetailsGrid.getCheckedRows();
        if (row.length > 0){
            var inInfo = new EiInfo();
            inInfo.setByNode("AttachmentDetails");
            inInfo.addBlock(AttachmentDetailsGrid.getCheckedBlockData());
            EiCommunicator.send("LIRL0316", "deleteFile", inInfo, {
                onSuccess: function (ei) {
                    if ("-1" == ei.status) {
                        NotificationUtil(ei);
                    }else {
                        AttachmentDetailsGrid.setEiInfo(ei);
                        $("#QUERYFILE").click();
                    }
                },
                onFail: function (ei) {
                    NotificationUtil(ei);
                    return false;
                }
            })
        }else {
            NotificationUtil({msg: "至少勾选一条数据!"}, "error");
            return;
        }

    });
    $("#FILEDUPDATE").on("click", function (e) {

        const row = AttachmentDetailsGrid.getCheckedRows();
        if (row.length > 0){
            var inInfo = new EiInfo();
            inInfo.setByNode("AttachmentDetails");
            inInfo.addBlock(AttachmentDetailsGrid.getCheckedBlockData());
            EiCommunicator.send("LIRL0316", "updateFile", inInfo, {
                onSuccess: function (ei) {
                    if ("-1" == ei.status) {
                        NotificationUtil(ei);
                    }else {
                        AttachmentDetailsGrid.setEiInfo(ei);
                        $("#QUERYFILE").click();
                    }
                },
                onFail: function (ei) {
                    NotificationUtil(ei);
                    return false;
                }
            })
        }else {
            NotificationUtil({msg: "至少勾选一条数据!"}, "error");
            return;
        }

    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: (e) => {
                        const eUploadList = e.uploadFilePath?.split(';');
                        uploadList.push(eUploadList);
                        uploadIndex++;
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return `<a id="upload-${uploadIndex}">下载</a>`
                        } else {
                            return "";
                        }
                    }
                },
                {
                    field: "voucherNum",
                    enable: true,
                    hidden: false,
                    locked: false,
                    title: "提单委托",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "billOfLading",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "提单委托查询"
                            })
                        }
                    }
                },
                {
                    field: "attachmentPrint",
                    enable: true,
                    hidden: false,
                    locked: false,
                    title: "附件打印机(针式打印机/A4打印机)",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorCell = param;
                            IPLAT.Popup.popupContainer({
                                containerId: "attachmentPrintR",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "打印机类型查询"
                            })
                        }
                    }
                }
            ],
            //表格按钮
            toolbarConfig: {
                buttons: [
                    {
                        name: "FILEUPLOAD",
                        text: "附件上传",
                        layout: "3",
                        click: function () {
                            if (resultGrid.getCheckedRows().length <= 0) {
                                IPLAT.alert({
                                    title: "提示信息",
                                    message: "请先选择一行数据进行附件上传操作！"
                                });
                                return;
                            }
                            $("#fileForm").click();
                        }
                    },
                    {
                        name:"FILEVERSION",
                        text:"附件明细",
                        layout: "3",
                        click: function () {
                            if (resultGrid.getCheckedRows().length <= 0) {
                                IPLAT.alert({
                                    title: "提示信息",
                                    message: "请先选择一行数据查看附件明细！"
                                });
                                return;
                            }
                            $("#AttachmentDetails").data("kendoWindow").center().open();
                        }
                    }
                ]
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', '');
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                if (e.field === "voucherNum" && e.model.status !=='') {
                    e.preventDefault()
                }
            },
            dataBound: (e) => {
                uploadList.forEach((uploadArr, index) => {

                    console.log(uploadArr);
                    $(`#upload-${index}`).on('click', () => {

                        uploadArr.forEach(upload => {
                            console.log(upload, '下载')
                            window.open(upload, '_blank');
                        })
                    });
                });
            },
        },
        "AttachmentDetails": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ]
        },
        "billOfLading": {
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                //双击选中前先把双击的数据勾选上
                billOfLadingGrid.unCheckAllRows();
                billOfLadingGrid.setCheckedRows(e.row);
                //关闭下拉框
                $("#billOfLading").data("kendoWindow").center().close();
            }
        },
        "attachmentPrintR": {
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                //双击选中前先把双击的数据勾选上
                attachmentPrintRGrid.unCheckAllRows();
                attachmentPrintRGrid.setCheckedRows(e.row);
                //关闭下拉框
                $("#attachmentPrintR").data("kendoWindow").center().close();
            }
        }
    };



    /** 弹出框配置 */
    IPLATUI.EFWindow = {
        "AttachmentDetails": {
            open: function (e) {
                $("#QUERYFILE").click();
            }
        },
        "billOfLading": {
            open: function (e) {
                billOfLadingGrid.removeRows(billOfLadingGrid.getDataItems());
            },
            close: function (e) {
                var row = billOfLadingGrid.getCheckedRows();
                if (row.length > 0) {
                    resultGrid.setCellValue(editorCell.model, "voucherNum", row[0].ladingBillId);
                    resultGrid.setCellValue(editorCell.model, "vehicleNo", row[0].vehicleNo);
                    resultGrid.setCellValue(editorCell.model, "driverName", row[0].driver);
                    resultGrid.setCellValue(editorCell.model, "driverTel", row[0].driverPhone);
                    resultGrid.setCellValue(editorCell.model, "driverIdentity", row[0].driverId);
                }
            }
        },
        "attachmentPrintR": {
            open: function (e) {
                attachmentPrintRGrid.removeRows(attachmentPrintRGrid.getDataItems());
            },
            close: function (e) {
                var row = attachmentPrintRGrid.getCheckedRows();

                if (row.length > 0) {
                    var attachmentPrintName='';
                    if (row[0].printerType==10){
                        attachmentPrintName='一体机内置A4';
                    }else if (row[0].printerType==20){
                        attachmentPrintName='一体机内置针打';
                    }else if (row[0].printerType==30){
                        attachmentPrintName='仓库办公室A4';
                    }else if (row[0].printerType==40){
                        attachmentPrintName='仓库办公室针打';
                    }
                    resultGrid.setCellValue(editorCell.model, "attachmentPrint", attachmentPrintName);
                    resultGrid.setCellValue(editorCell.model, "printIp", row[0].printerIpAddr);
                }
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
                $("#QUERY").click();
            }
        }
    });

    // 附件上传相关
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "LIRLInterfaces",
        methodName: "fileUpload",
        callback: function (e) {
            resultGrid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/LI/RL/LIRL0316S.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "LIRL0316" +
                "&methodName=" +
                "fileUpload" +
                "&segNo=" +
                resultGrid.getCheckedRows()[0].segNo +
                "&id=" +
                resultGrid.getCheckedRows()[0].voucherNum +
                "&type=4";
        }
    });
})
