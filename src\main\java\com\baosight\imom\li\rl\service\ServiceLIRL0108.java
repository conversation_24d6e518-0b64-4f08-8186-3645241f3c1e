package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0108;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * @Author: 张博翔
 * @Description: ${取消预约时长维护}
 * @Date: 2024/8/13 13:26
 * @Version: 1.0
 */
public class ServiceLIRL0108 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        /*list.add("TEST_TYPE1");
        list.add("TEST_TYPE2");
        EiInfo eiInfo = CodeValueUtils.queryToBlock("QL00000", list, inInfo);*/
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0108().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            outInfo = super.query(inInfo, LIRL0108.QUERY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ08";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0108.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0108> query = dao.query(LIRL0108.QUERY_ALL, map);
                for (LIRL0108 lirl0108 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0108);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0108.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0108> query = dao.query(LIRL0108.QUERY_ALL, map);
                for (LIRL0108 lirl0108 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0108);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0108.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 使用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo USE(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0108> query = dao.query(LIRL0108.QUERY_ALL, map);
                for (LIRL0108 lirl0108 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0108);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                Map countMap = new HashMap();
                countMap.put("segNo", segNo);
                countMap.put("status", 20);
                countMap.put("delFlag", 0);
                //同一装卸业务只能有一条确认状态的预约最大数。
                int count = super.count(LIRL0108.COUNT, countMap);
                if (count > 0) {
                    String massage = "同一账套下只能有一条使用状态的数据！";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", "20");//状态
                RecordUtils.setRevisor(hashMap);
                dao.update(LIRL0108.UPDATE, hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 禁用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo DISABLE(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0108> query = dao.query(LIRL0108.QUERY_ALL, map);
                for (LIRL0108 lirl0108 : query) {
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusConfirmNo(inInfo, lirl0108);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "10");//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0108.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
