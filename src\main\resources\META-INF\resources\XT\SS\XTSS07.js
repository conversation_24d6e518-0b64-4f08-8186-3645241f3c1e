$(function () {
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            //双击选中
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (IPLAT.isBlankString(windowId)) {
                    // 设置默认值
                    windowId = "userInfo";
                }
                //双击选中前先把双击的数据勾选上
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                //关闭下拉框
                window.parent[windowId + "Window"].close();
            }
        }
    };
});