package com.baosight.imom.xt.ss.service;


import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.BlockConstant;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.xt.ss.domain.XTSS01;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-08-06
 * code-value 服务类
 */
public class ServiceXTSS01 extends ServiceBase {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceXTSS01.class);

    /**
     * 根据入参的Eiinfo中的codeValueQueryBlock作为条件查询值集列表，允许多参数查询
     * <AUTHOR>
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo queryCodevalue(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        Map<String, Object> conditionMap = inInfo.getBlock(BlockConstant.CODE_VALUE_QUERY_BLOCK).getAttr();
        List<Map<String,Object>> resultList = dao.query(XTSS01.QUERY_CODE_VALUE,conditionMap);
        outInfo.addBlock(BlockConstant.CODE_VALUE_RESULT_BLOCK).addRows(resultList);
        return outInfo;
    }

    /**
     * @description 查询系统账套信息
     * @param eiInfo
     * @return com.baosight.iplat4j.core.ei.EiInfo
     * @throws
     */
    public EiInfo queryUnit(EiInfo eiInfo){
        EiInfo outInfo = new EiInfo();
//        List<Map> segNoList = new ArrayList<>();
        String strUnitCode = eiInfo.getString("inqu2_status-0-unitCode");
        String strSegFullName = eiInfo.getString("inqu2_status-0-segFullName");
        String strSegName = eiInfo.getString("inqu2_status-0-segName");
        //获取登录人的工号
        String userId = "";
        String flag = eiInfo.getString("flag");
        if ("PDA".equals(flag)){
            userId = eiInfo.getString("userId");
        }else {
            userId = UserSession.getUserId();
        }
        HashMap<String,String> userMap = new HashMap();
        userMap.put("userId", userId);
        userMap.put("segNo", strUnitCode);
        userMap.put("segFullName", strSegFullName);
        userMap.put("segName", strSegName);

        //优先查询用户是否有数据授权权限
        List<Map<String, String>> userList = dao.query(XTSS01.QUERY_USER_INFO, userMap);
        //如果没有查到该用户信息则报错
        if(CollectionUtils.isEmpty(userList)){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("未查到该用户的业务账套信息，请联系管理员！");
            return outInfo;
        }else{
            Map<String,String> user = userList.get(0);
            //如果用户没有配置业务账套号则查询全部账套
            if(StrUtil.isBlank(user.get(MesConstant.SEG_NO))){
                List<Map<String, Object>> segNoAuList = dao.query(XTSS01.QUERY_SEGNO_LIST_BY_BLANK_SEG_NO, userMap);
                segNoAuList = segNoAuList.stream().distinct().collect(Collectors.toList());
                eiInfo.addRows(BlockConstant.RESULT2, segNoAuList);
            }else{
                if(StrUtil.isBlank(userMap.get("segNo"))){
                    userMap.put("segNo",user.get("segNo"));
                }
                //若用户配置了业务账套号则去查视图根据F_SEG_NO进行查询下属的账套号
                //判断是否有跨账套数据权限
                if("1".equals(user.get("isDataAuthor"))){
                    //如果配置了跨账套权限则去视图查询配置的所有的
                    List<Map<String, Object>> segNoAuList = dao.query(XTSS01.QUERY_SEGNO_LIST_VIEW_BY_F,userMap);
                    eiInfo.addRows(BlockConstant.RESULT2, segNoAuList);
                }else{
                    //如果没有配置跨数据权限，则只查询到自身账套的数据
                    List<Map<String, Object>> segNoAuList = dao.query(XTSS01.QUERY_SEGNO_LIST_VIEW_BY_OWN,userMap);
                    eiInfo.addRows(BlockConstant.RESULT2, segNoAuList);
                }

                /*//查询出该用户当前账套与下层级的账套，条件是用户有数据授权
                List<Map<String, Object>> segNoAuList = dao.query(XTSS01.QUERY_SEGNO_BY_USER_ID, userMap);
                //判断是否有跨账套数据权限
                if("1".equals(user.get("isDataAuthor"))){
                    //若有，则递归查询子账套
                    segNoAuList.addAll(this.queryTreeSegNo(segNoAuList));
                    segNoAuList = segNoAuList.stream().distinct().collect(Collectors.toList());
                    eiInfo.addRows(BlockConstant.RESULT2, segNoAuList);
                }else{
                    segNoAuList = segNoAuList.stream().distinct().collect(Collectors.toList());
                    eiInfo.addRows(BlockConstant.RESULT2, segNoAuList);
                }*/
            }
        }
        return eiInfo;
    }


    /**
     * 递归查询账套树
     * @param segAuList
     * @return
     */
    private List<Map<String,Object>> queryTreeSegNo(List<Map<String,Object>> segAuList){
        //如果入参为空 则直接返回
        if(CollectionUtils.isEmpty(segAuList)){
            return segAuList;
        }else{
            List<Map<String,Object>> reduceList = new ArrayList<>();
            for(Map<String,Object> segNoMap : segAuList){
                List<Map<String,Object>> auList = dao.query(XTSS01.QUERY_SEGNO_LIST_BY_AU, segNoMap);
                if(CollectionUtils.isNotEmpty(auList)){
                    reduceList.addAll(auList);
                    //递归查询
                    reduceList.addAll(this.queryTreeSegNo(auList));
                }
            }
            return reduceList;

        }
    }

    /**
     * 查询登录用户的账套信息
     *
     * @param inInfo inInfo
     * @return 返回inqu_status块中数据用于前端赋值默认查询条件
     */
    public EiInfo queryForInqu(EiInfo inInfo) {
        EiInfo outInfo = this.queryUnit(inInfo);
        if (outInfo.getStatus() != EiConstant.STATUS_FAILURE) {
            outInfo.getBlocks().put(EiConstant.queryBlock, outInfo.getBlock(BlockConstant.RESULT2));
            outInfo.getBlocks().remove(BlockConstant.RESULT2);
        }
        return outInfo;
    }
}
