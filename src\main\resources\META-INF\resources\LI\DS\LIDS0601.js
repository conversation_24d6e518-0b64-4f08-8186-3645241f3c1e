$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //编辑行
    let editorModel;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            pageable:{
                pageSize:10,
                pageSizes:[10,50,100,1000]
            },
            columns: [
                {
                    field: "loadingPointNo",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "装卸点编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "handPointInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂内装卸点查询"
                            })
                        }
                    }
                },
                {
                    field: "factoryBuilding",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "厂房代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "factoryAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "厂区厂房代码查询"
                            })
                        }
                    }
                }
                ,{
                    field: "warehouseCode",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "仓库代码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "warehouseInfoGrid",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "仓库代码查询"
                            })
                        }
                    }
                },
                {
                    field: "crossArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "跨区编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "跨区编码查询"
                            })
                        }
                    }
                },
                {
                    field: "xInitialPoint",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "xDestination",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "yInitialPoint",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "yDestination",
                    valueType: "N",//小计设置
                    type: "N"
                }
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                //生成库位详细信息
                $("#ADD_LOCATION_DETAIL").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "10" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为启用,不可生成详细信息!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0601", "addLocationDetail", true, null, null, false);
                });

                //定位起始坐标
                $("#CONFIRM_START_POS").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    //判断当前查询条件行车是否有值,若为空打开行车弹窗，选择行车
                    const craneId = $("#inqu_status-0-craneId").val();
                    const craneName = $("#inqu_status-0-craneName").val();
                    if(IPLAT.isBlankString(craneId) && IPLAT.isBlankString(craneName)){
                        craneInfoWindow.open().center();
                    }else{
                        IMOMUtil.submitGridsData("result", "LIDS0601", "confirmStartPos", true, null, null, true);
                    }
                });

                //定位终点坐标
                $("#CONFIRM_END_POS").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    //判断当前查询条件行车是否有值,若为空打开行车弹窗，选择行车
                    const craneId = $("#inqu_status-0-craneId").val();
                    const craneName = $("#inqu_status-0-craneName").val();
                    if(IPLAT.isBlankString(craneId) && IPLAT.isBlankString(craneName)){
                        craneInfoWindow.open().center();
                    }else{
                        IMOMUtil.submitGridsData("result", "LIDS0601", "confirmEndPos", true, null, null, true);
                    }
                });
                /**
                 * 大数据后端导出
                 */
                $("#EXPORTEXCEL").on("click", function () {
                    let segNo = $("#inqu_status-0-segNo").val();
                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil("请在查询条件区域内选择相应的[业务单元]！", "error");
                        return;
                    }
                    var fileName = segNo + "库位附属信息" + ".xlsx";
                    let exportEi = new EiInfo();
                    exportEi.setByNode("inqu");
                    IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                    exportEi.set("exportColumnBlock", 'fileName', fileName);
                    IMOMUtil.callService({
                        service: "LIDS0601",
                        method: "postExport",
                        eiInfo: exportEi,
                        showProgress: true,
                        async: true,
                        callback: function (ei) {
                            if (ei.status > -1) {
                                let docUrl = ei.getBlock("excelDoc").get("docUrl");
                                window.open(docUrl);
                            }
                        }
                    });
                });
            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
                if (!e.model.isNew()) {
                    /**
                     * 可以修改的字段有：跨区编码、跨区名称、X轴起始点、X轴结束点、Y轴起始点、Y轴结束点、层数标记,装卸点，厂区厂房,管理方式,半卷标记,是否参与库位推荐
                     */
                    if (e.field !== "crossArea" && e.field !== "xInitialPoint" && e.field !== "xDestination"
                        && e.field !== "yInitialPoint" && e.field !== "yDestination" && e.field !== "posDirCode" && e.field !== "loadingPointNo"
                        && e.field !== "factoryBuilding"  && e.field !== "managementStyle" && e.field !== "c_no" && e.field !== "standFlag" && e.field !== "c_no"
                        && e.field !== "pointUpperLength" && e.field !== "pointLowerLength" && e.field !== "specUpper" && e.field !== "specLower"
                        && e.field !== "managementStyle" && e.field !== "actionFlag" && e.field !== "ifPlanFlag"
                        && e.field !== "endPoint" && e.field !== "endPoint" && e.field !== "endPoint"
                    ) {
                        e.preventDefault();
                        return;
                    }
                }
            },
            afterEdit: function (e) {
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    //厂内装卸点管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "handPointInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-loadFlag").val("1");
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            /*if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "loadingChannelNo", rows[0].loadingChannelId);
                resultGrid.setCellValue(editorModel, "loadingChannelName", rows[0].loadingChannelName);
                resultGrid.setCellValue(editorModel, "loadingPointNo", rows[0].handPointId);
                resultGrid.setCellValue(editorModel, "loadingPointName", rows[0].handPointName);
            }*/
            var checkedRows = resultGrid.getCheckedRows();
            if (checkedRows.length > 0 && rows.length > 0) {
                for(let i=0;i<checkedRows.length;i++){
                    resultGrid.setCellValue(checkedRows[i], "loadingChannelNo", rows[0].loadingChannelId);
                    resultGrid.setCellValue(checkedRows[i], "loadingChannelName", rows[0].loadingChannelName);
                    resultGrid.setCellValue(checkedRows[i], "loadingPointNo", rows[0].handPointId);
                    resultGrid.setCellValue(checkedRows[i], "loadingPointName", rows[0].handPointName);
                }
            }
        }
    });


    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            /*if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "crossArea", rows[0].crossArea);
                resultGrid.setCellValue(editorModel, "crossAreaName", rows[0].crossAreaName);
            }*/
            var checkedRows = resultGrid.getCheckedRows();
            if (checkedRows.length > 0 && rows.length > 0) {
                for(let i=0;i<checkedRows.length;i++){
                    resultGrid.setCellValue(checkedRows[i], "crossArea", rows[0].crossArea);
                    resultGrid.setCellValue(checkedRows[i], "crossAreaName", rows[0].crossAreaName);
                }
            }

        }
    });

    //厂区厂房管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            var checkedRows = resultGrid.getCheckedRows();
            if (checkedRows.length > 0 && rows.length > 0) {
                for(let i=0;i<checkedRows.length;i++){
                    resultGrid.setCellValue(checkedRows[i], "factoryArea", rows[0].factoryArea);
                    resultGrid.setCellValue(checkedRows[i], "factoryAreaName", rows[0].factoryAreaName);
                    resultGrid.setCellValue(checkedRows[i], "factoryBuilding", rows[0].factoryBuilding);
                    resultGrid.setCellValue(checkedRows[i], "factoryBuildingName", rows[0].factoryBuildingName);
                }
            }
        }
    });

    //仓库弹窗
    IMOMUtil.windowTemplate({
        windowId: "warehouseInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-warehouseCode").val(rows[0].stockCode);
                $("#inqu_status-0-warehouseName").val(rows[0].stockName);
            }
        }
    });

    //仓库弹窗
    IMOMUtil.windowTemplate({
        windowId: "warehouseInfoGrid",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "warehouseCode", rows[0].stockCode);
                resultGrid.setCellValue(editorModel, "warehouseName", rows[0].stockName);
            }
        }
    });

    //行车编码弹窗
    IMOMUtil.windowTemplate({
        windowId: "craneInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-craneId").val(rows[0].craneId);
                $("#inqu_status-0-craneName").val(rows[0].craneName);
                $("#inqu_crane").attr("style", "display:block;");
                // $("#craneMsg").text("当前选择行车：" +rows[0].craneId+"-"+rows[0].craneName+";");
            }
        }
    });
})