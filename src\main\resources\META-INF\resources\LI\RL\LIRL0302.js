$(function () {

    //获取当前登录人对应的业务单元
    var unitInfo = IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

    var addsubwindow = $("#ADDSUBWINDOW");

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 100,
                pageSizes: [100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: true,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
                beforeExport: function (gridInstance) {
                    var segNo = IPLAT.EFInput.value($('#inqu_status-0-segNo'));
                    if (IPLAT.isBlankString(segNo)) {
                        IPLAT.alert("请选择业务单元进行查询操作");
                        return false;
                    }
                    return gridInstance.getDataItems().length > 0; // false不执行导出，true时执行导出
                },
                // exportServiceName: "LIRL0302", // 配置导出服务名，默认和grid的serviceName相同
                // exportMethodName: "query",    // 配置导出方法名，默认和grid的queryMethod相同
                // exportFileName: function (gridInstance) {
                //     // 导出的文件名包含时间戳 yyyyMMddHHmmss
                //     return "result" + kendo.toString(new Date(), IPLAT.FORMAT.DATE_14);
                // },
                // exportMode: "after", //导出模式
                // exportFileType: "xls",  // 默认值是xls
                // exportBlockId: "result"  // 默认值和blockId相同，导出的EiInfo中的指定数据块
                // exportFileName: function (gridInstance) {
                //     // 导出的文件名包含时间戳 yyyyMMddHHmmss
                //     return "result" + kendo.toString(new Date(), IPLAT.FORMAT.DATE_14);
                // },
                // queryEiInfo: function (gridInstance) {
                //     let queryInfo = gridInstance.getQueryInfo();
                //     IMOMUtil.setPreciseExportColumnBlock(queryInfo, gridInstance);
                //     queryInfo.set('exportInfoBlock', 'service', 'LIRL0302.query');
                //     queryInfo.set('exportInfoBlock', 'valueSet', 'yes');
                //     return queryInfo;
                // },
                // exportServiceName: "ExportUtils",
                // exportMethodName: "excelDown",
                // exportMode: "after", //导出模式
                // exportFileType: "xls",  // 默认值是xls
                // exportBlockId: "result"
            },
            columns: [],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                /**
                 * 大数据后端导出
                 */
                $("#EXPORTEXCEL").on("click",function () {
                    let segNo = $("#inqu_status-0-segNo").val();

                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil("请在查询条件区域内选择相应的[业务单元]！", "error");
                        return;
                    }

                    var fileName = segNo+"车辆登记管理" + ".xlsx";

                    if(resultGrid.getDataItems().length > 0) {
                        let exportEi = new EiInfo();
                        exportEi.setByNode("inqu");
                        IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                        exportEi.set("exportColumnBlock", 'fileName', fileName);
                        IMOMUtil.callService({
                            service: "LIRL0302",
                            method: "postExport",
                            eiInfo: exportEi,
                            showProgress: true,
                            async: true,
                            callback: function (ei) {
                                if (ei.status > -1) {
                                    let docUrl = ei.getBlock("excelDoc").get("docUrl");
                                    window.open(docUrl);
                                }
                            }
                        });
                    }
                });

                // TODO 查询 按钮事件
                $("#QUERY").on("click", function (e) {
                    resultGrid.dataSource.page(1);
                });

                // 获取勾选数据，
                $("#CONFIRM").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("审核失败，原因[请勾选记录后再进行审核！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    if (resultGrid.getCheckedRows().length > 1) {
                        NotificationUtil("审核失败，原因[只能选择一条数据审批！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var varModel = resultGrid.getCheckedRows()[0];
                    if ("10" != varModel.status) {
                        NotificationUtil({msg: "原因[只有待审核状态的数据才能审核！]"}, "error")
                        e.preventDefault();
                        return false;
                    }
                    $("#inqu2_status-0-segNo").val(varModel.segNo);
                    $("#inqu2_status-0-handType").val(varModel.handType);
                    $("#inqu2_status-0-businessType").val(varModel.businessType);
                    $("#inqu2_status-0-voucherNum").val(varModel.voucherNum);
                    var inInfo = new EiInfo();
                    IPLAT.progress($("body"), false);
                    inInfo.setByNode("inqu2");
                    EiCommunicator.send("LIRL0302", "queryAppointmentSlot", inInfo, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                console.log(ei);
                                let b = ei.extAttr.b;
                                if (b) {
                                    //弹出框
                                    IPLAT.confirm({
                                        message: '<p>当天所有时段剩余号码都为0，请确认是否继续预约当前时段</p> ',
                                        okFn: function (e) {
                                            $("#appointmentSlotInformation").data("kendoWindow").center().open();
                                            result2Grid.dataSource.page(1);
                                        },
                                        title: '提示框',
                                        minWidth: 250
                                    });
                                    NotificationUtil({msg: ei.msg}, "sccess");
                                } else {
                                    $("#appointmentSlotInformation").data("kendoWindow").center().open();
                                    result2Grid.dataSource.page(1);
                                }
                            }
                            IPLAT.progress($("body"), false)
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                });
                // 获取勾选数据，
                $("#DELETEREVOKE").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("驳回失败，原因[请勾选记录后再进行审核！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRLInterface", "revokeTheRegistrationAndEnterTheFactory", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                });

                // 获取勾选数据，
                $("#REJECTED").on("click", function (e) {
                    if (resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("驳回失败，原因[请勾选记录后再进行审核！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.setByNode("result");
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0302", "delete", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                });
            },
            onSave: function (e) {
                var checkRows = e.sender.getCheckedRows();
                if (checkRows.length > 0) {
                    $.each(checkRows, function (index, item) {
                        //新增或者修改时判断某些字段不能为空
                        /*if (IPLAT.isBlankString(item.pageId)) {
                            e.preventDefault();
                            NotificationUtil({msg: "页面号不能为空，请检查!"}, "error");
                            return false;
                        }*/
                    });
                }
            },
            beforeEdit: function (e) {
                // 判断当前行是不是新增的行
                if (!e.model.isNew()) {

                }
            },
            afterEdit: function (e) {

            }
        },
        "result2": {
            pageable: {
                pageSize: 100,
                pageSizes: [100, 300, 500]
            },
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
            },
            columns: [],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                $("#QUERY2").on("click", function (e) {
                    result2Grid.dataSource.page(1);
                });

                // 获取勾选数据，
                $("#CONFIRM2").on("click", function (e) {
                    if (result2Grid.getCheckedRows().length <= 0) {
                        NotificationUtil("审核失败，原因[请勾选记录后再进行确认！]", "error");
                        e.preventDefault();
                        return false;
                    }
                    var info = new EiInfo();
                    info.set("result3", result2Grid.getDataItems());
                    info.setByNode("result2");
                    info.setByNode("result");
                    info.addBlock(result2Grid.getCheckedBlockData());
                    info.addBlock(resultGrid.getCheckedBlockData());
                    IPLAT.progress($("body"), true);
                    EiCommunicator.send("LIRL0302", "confirm", info, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil({msg: ei.msg}, "error");
                            } else {
                                NotificationUtil({msg: ei.msg}, "sccess");
                                resultGrid.dataSource.page(1);
                                resultGrid.refresh();
                                result2Grid.dataSource.page(1);
                                result2Grid.refresh();

                            }
                            var windowId = "appointmentSlotInformation";
                            //关闭下拉框
                            window.parent[windowId + "Window"].close();
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            NotificationUtil({msg: ei.msg}, "error");
                            return false;
                        }
                    });
                });
            },
        }
    };
    IPLATUI.EFWindow = {
        //关闭新增子项弹出框时的事件
        "unitInfo": {
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                    dataGrid.unCheckAllRows();
                }


            }
        },
        "appointmentSlotInformation": {
            // 打开窗口事件
            open: function (e) {

            },
            // 关闭窗口事件
            close: function (e) {

            }
        },
    }

});
