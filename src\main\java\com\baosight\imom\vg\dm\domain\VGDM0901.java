package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0901;

/**
 * 资材备件报废申请表
 */
public class VGDM0901 extends Tvgdm0901 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0901.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0901.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0901.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0901.update";
    /**
     * 修改状态信息
     */
    public static final String UPDATE_STATUS = "VGDM0901.updateStatus";

    @Override
    public String getCheckStatus() {
        return this.getApplyStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

}
