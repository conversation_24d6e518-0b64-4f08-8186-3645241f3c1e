package com.baosight.imom.vg.dm.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.vg.dm.domain.VGDM0104;
import com.baosight.imom.vg.dm.domain.VGDM0104.RelevanceType;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * <AUTHOR> yzj
 * @Description : 设备履历页面后台
 * @Date : 2024/11/22
 * @Version : 1.0
 */
public class ServiceVGDM0104 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0104.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0104().eiMetadata);
        // 业务单元
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0104.QUERY, new VGDM0104());
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0104 vgdm0104;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0104 = new VGDM0104();
                vgdm0104.fromMap(block.getRow(i));
                // 校验数据是否存在
                Map<String, String> countMap = new HashMap<>();
                countMap.put("uuid", vgdm0104.getUuid());
                int count = super.count(VGDM0104.COUNT, countMap);
                if (count == 0) {
                    throw new PlatException(vgdm0104.getUuid() + "数据不存在");
                }
                // 删除数据
                vgdm0104.setDelFlag("1");
                Map delMap = vgdm0104.toMap();
                RecordUtils.setRevisor(delMap);
                block.getRows().set(i, delMap);
            }
            // 批量更新
            DaoUtils.updateBatch(dao, VGDM0104.UPDATE, block.getRows());
            // 返回成功信息
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 加入设备履历
     * <p>
     * 内部调用，非前端页面调用
     */
    public EiInfo addResume(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            // 获取数据来源
            String relevanceType = inInfo.getString("relevanceType");
            VGDM0104 history;
            List<Map> insList = new ArrayList<>();
            List<String> checkList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                Map map = block.getRow(i);
                history = new VGDM0104();
                history.fromMap(map);
                history.setRelevanceType(relevanceType);
                // 根据数据来源对应赋值
                switch (relevanceType) {
                    case RelevanceType.EXCEPTION:
                        history.setRelevanceId(MapUtils.getStr(map, "exceptionContactId"));
                        history.setEventContent(MapUtils.getStr(map, "actualsRemark"));
                        history.setEventStartTime(MapUtils.getStr(map, "recCreateTime"));
                        history.setHandleContent(MapUtils.getStr(map, "handleMeasures"));
                        history.setHandlePerson(MapUtils.getStr(map, "recRevisor"));
                        history.setHandlePersonName(MapUtils.getStr(map, "recRevisorName"));
                        history.setHandleTime(MapUtils.getStr(map, "recReviseTime"));
                        break;
                    case RelevanceType.ALARM:
                        history.setRelevanceId(MapUtils.getStr(map, "alarmId"));
                        history.setEventContent(MapUtils.getStr(map, "alarmTagDesc")
                                + MapUtils.getStr(map, "alarmType"));
                        history.setEventStartTime(MapUtils.getStr(map, "occurTime")
                                .replaceAll("[\\-: ]", "")
                                .substring(0, 14));
                        history.setHandleContent("确认报警");
                        history.setHandlePerson(MapUtils.getStr(map, "confirmor"));
                        history.setHandlePersonName(MapUtils.getStr(map, "confirmorName"));
                        history.setHandleTime(MapUtils.getStr(map, "confirmTime"));
                        break;
                    case RelevanceType.FAULT:
                        history.setRelevanceId(MapUtils.getStr(map, "faultId"));
                        history.setEventContent(MapUtils.getStr(map, "faultDesc"));
                        history.setEventStartTime(MapUtils.getStr(map, "faultStartTime"));
                        history.setEventEndTime(MapUtils.getStr(map, "faultEndTime"));
                        history.setHandleContent(MapUtils.getStr(map, "handleMeasures"));
                        history.setHandlePerson(MapUtils.getStr(map, "recRevisor"));
                        history.setHandlePersonName(MapUtils.getStr(map, "recRevisorName"));
                        history.setHandleTime(MapUtils.getStr(map, "recReviseTime"));
                        break;
                    case RelevanceType.OVERHAUL:
                        history.setRelevanceId(MapUtils.getStr(map, "overhaulPlanId"));
                        history.setEventContent(MapUtils.getStr(map, "overhaulProject"));
                        history.setEventStartTime(MapUtils.getStr(map, "overhaulImplementDate")
                                .replaceAll("[\\-: ]", "")
                                + "000000");
                        history.setHandleContent(MapUtils.getStr(map, "actualLegacyProject"));
                        history.setHandlePerson(MapUtils.getStr(map, "actualsRevisor"));
                        history.setHandlePersonName(MapUtils.getStr(map, "actualsRevisorName"));
                        history.setHandleTime(MapUtils.getStr(map, "actualsTime"));
                        break;
                    default:
                        throw new PlatException(relevanceType + "数据来源错误");
                }
                // 校验数据重复
                String key = history.getRelevanceType() + history.getRelevanceId();
                if (checkList.contains(key)) {
                    throw new PlatException(history.getRelevanceId() + "已加入履历，不能重复添加");
                }
                checkList.add(key);
                // 校验数据库中数据是否已存在
                Map<String, String> countMap = new HashMap<>();
                countMap.put("equalId", history.getRelevanceId());
                countMap.put("relevanceType", history.getRelevanceType());
                int count = super.count(VGDM0104.COUNT, countMap);
                if (count > 0) {
                    throw new PlatException(history.getRelevanceId() + "已加入履历，不能重复添加");
                }
                // 转换为map
                Map insMap = history.toMap();
                RecordUtils.setCreator(insMap);
                insList.add(insMap);
            }
            // 插入数据
            DaoUtils.insertBatch(dao, VGDM0104.INSERT, insList);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
