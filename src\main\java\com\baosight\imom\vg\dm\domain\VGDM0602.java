package com.baosight.imom.vg.dm.domain;

import java.util.Map;

import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.vg.domain.Tvgdm0602;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.ed.util.SequenceGenerator;

/**
 * 设备报警知识库表
 */
public class VGDM0602 extends Tvgdm0602 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0602.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0602.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0602.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0602.update";

    /**
     * 从故障表创建
     */
    public void createFromFault(Dao dao, Map faultMap) {
        VGDM0602 vgdm0602 = new VGDM0602();
        vgdm0602.fromMap(faultMap);
        vgdm0602.setVoucherNum(faultMap.get("faultId").toString());
        String[] arr = {vgdm0602.getSegNo()};
        vgdm0602.setCaseId(SequenceGenerator.getNextSequence(SequenceConstant.CASE_ID, arr));
        Map insMap = vgdm0602.toMap();
        RecordUtils.setCreator(insMap);
        dao.insert(INSERT, insMap);
    }
}
