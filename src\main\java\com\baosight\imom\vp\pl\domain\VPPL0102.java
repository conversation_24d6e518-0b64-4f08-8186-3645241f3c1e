package com.baosight.imom.vp.pl.domain;

import com.baosight.imom.common.vp.domain.Tvppl0102;
import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;

import java.util.Map;

public class VPPL0102 extends Tvppl0102 {
    public static final String SEQ_ID = "TVPPL_SEQ01";

    public static final String QUERY = "VPPL0102.query";
    public static final String COUNT = "VPPL0102.count";
    public static final String QUERY_EXPORT = "VPPL0102.queryExport";
    public static final String STATISTIC_GROUP_BY_EMP_EXPORT = "VPPL0102.statisticGroupByEmpExport";
    public static final String STATISTIC_GROUP_BY_AREA_EXPORT = "VPPL0102.statisticGroupByAreaExport";
    public static final String STATISTIC_PERSON_TIME_IN_AREA_EXPORT = "VPPL0102.statisticPersonTimeInAreaExport";
    public static final String COUNT_EQUAL = "VPPL0102.countEqual";
    public static final String SUM_TOTAL = "VPPL0102.sumTotal";
    public static final String INSERT = "VPPL0102.insert";
    public static final String UPDATE = "VPPL0102.update";
    public static final String UPDATE_BY_UUID = "VPPL0102.updateByUuId";
    public static final String DELETE = "VPPL0102.delete";
    public static final String UPDATE_STATUS = "VPPL0102.updateStatus";
    public static final String QUERY_ORDER_BY_TIME = "VPPL0102.queryOrderByTime";
    public static final String COUNT_CREW_VISITS_TODAY = "VPPL0102.countCrewVisitsToday";
    public static final String COUNT_SHIFT_PERSONNEL_BY_MACHINE = "VPPL0102.countShiftPersonnelByMachine";
    public static final String STATISTIC_GROUP_BY_EMP = "VPPL0102.statisticGroupByEmp";
    public static final String STATISTIC_GROUP_BY_AREA = "VPPL0102.statisticGroupByArea";
    public static final String STATISTIC_PERSON_TIME_IN_AREA = "VPPL0102.statisticPersonTimeInArea";
    public static final String QUERY_CHIP_ID_IN_AREA = "VPPL0102.queryChipIdInArea";
    public static final String QUERY_EMP_ACTIVE = "VPPL0102.queryEmpActive";


    public static final String QUERY_PREVIOUS_SHIFT_ENTRY_COUNT = "VPPL0102.queryPreviousShiftEntryCount";
    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public VPPL0102() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }

    public static EiBlockMeta exportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("chipId");
        eiColumn.setDescName("芯片ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("empNo");
        eiColumn.setDescName("员工工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("staffName");
        eiColumn.setDescName("人员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaName");
        eiColumn.setDescName("进出区域");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actionType");
        eiColumn.setDescName("动作类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingHours");
        eiColumn.setDescName("有效工作时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingTime");
        eiColumn.setDescName("记录时间");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }

    public static EiBlockMeta exportBlockMetaTwo() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingDate");
        eiColumn.setDescName("当班日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("staffName");
        eiColumn.setDescName("人员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("postResponsibility");
        eiColumn.setDescName("人员岗位职责");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaName");
        eiColumn.setDescName("进出区域");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalWorkingHours");
        eiColumn.setDescName("有效工作时间(H)");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }

    public static EiBlockMeta exportBlockMetaThree() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingDate");
        eiColumn.setDescName("当班日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("areaName");
        eiColumn.setDescName("区域名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("staffName");
        eiColumn.setDescName("人员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("totalWorkingHours");
        eiColumn.setDescName("有效工作时间(H)");
        eiMetadata.addMeta(eiColumn);

        return eiMetadata;

    }
}
