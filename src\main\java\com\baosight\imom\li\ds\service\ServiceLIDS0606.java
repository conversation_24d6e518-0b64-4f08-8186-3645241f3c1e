package com.baosight.imom.li.ds.service;

import com.baosight.bpm.util.StringUtil;

import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.ds.domain.LIDS0605;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 库位附属信息管理
 */
public class ServiceLIDS0606 extends ServiceBase {
    /*
     * public static void main(String[] args) {
     * EiInfo inInfo = new EiInfo();
     * Map<String, Object> messageBody = new HashMap<>();
     */
    /*
     * productProcessId 首道加工工序
     * tradeCode 业务类型
     * prodDensity 密度
     * prodTypeId 品种代码
     * d_userNum 客户代码
     * warehouseCode 仓库代码
     * innerDiameter 内径
     * specsDesc 规格
     * packId 捆包号
     * matInnerId 材料管理号
     * segNo 账套
     * netWeight 捆包重量
     * factoryOrderNum 工厂订单号
     *//*
        * messageBody.put("netWeight", "100");
        * messageBody.put("segNo", "JC000000");
        * messageBody.put("tradeCode", "0001");
        * messageBody.put("prodDensity", "7.85");
        * messageBody.put("prodTypeId", "0001");
        * messageBody.put("d_userNum", "0001");
        * messageBody.put("warehouseCode", "0001");
        * messageBody.put("innerDiameter", "100");
        * messageBody.put("specsDesc", "1.2*1200*C");
        * messageBody.put("packId", "CS202412180001");
        * messageBody.put("matInnerId", "CS202412180001123");
        * messageBody.put("factoryOrderNum", "0001");
        * inInfo.setAttr(messageBody);
        * EiInfo outInfo = recommendedStorageLocation(inInfo);
        * 
        * }
        */

    /**
     * 库位推荐逻辑
     * S_LI_DS_0606
     *
     * @param inInfo
     * @return
     */
    public EiInfo recommendedStorageLocation(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map messageBody = (Map) inInfo.get("messageBody");
            // 校验参数:内径，密度，首道加工工序，贸易方式，账套，仓库，客户代码
            /*
             * productProcessId 首道加工工序
             * tradeCode 业务类型
             * prodDensity 密度
             * prodTypeId 品种代码
             * d_userNum 客户代码
             * warehouseCode 仓库代码
             * innerDiameter 内径
             * specsDesc 规格
             * packId 捆包号
             * matInnerId 材料管理号
             * segNo 账套
             * netWeight 捆包重量
             * factoryOrderNum 工厂订单号
             * d_userNum 客户代码
             */
            // 标记
            String netWeight = null;
            String segNo = null;
            String tradeCode = null;
            String prodDensity = null;
            String warehouseCode = null;
            String specsDesc = null;
            String packId = null;
            String factoryOrderNum = null;
            String innerDiameter = null;
            String prodTypeId = null;
            String d_userNum = null;
            String productProcessId = null;
            // 入库标记，1采购入库，2转库入库
            String storageMark = MapUtils.getString(messageBody, "storageMark");
            // 首道加工机组
            String firstMachineCode = MapUtils.getString(messageBody, "firstMachineCode");

            // 标记
            String mark = MapUtils.getString(messageBody, "mark");
            String originalPackId = MapUtils.getString(messageBody, "originalPackId");
            // String
            if (!StringUtil.isNotEmpty(mark)) {
                netWeight = MapUtils.getString(messageBody, "netWeight");
                if (!StringUtil.isNotEmpty(netWeight)) {
                    throw new RuntimeException("参数错误,捆包重量为空(netWeight):" + netWeight);
                }
                segNo = MapUtils.getString(messageBody, "segNo");
                if (!StringUtil.isNotEmpty(segNo)) {
                    throw new RuntimeException("参数错误,系统账套为空(segNo):" + segNo);
                }
                tradeCode = MapUtils.getString(messageBody, "tradeCode");
                if (StringUtil.isEmpty(tradeCode)) {
                    throw new RuntimeException("参数错误,业务类型为空(tradeCode):" + tradeCode);
                }
                prodDensity = MapUtils.getString(messageBody, "prodDensity");
                if (StringUtil.isEmpty(prodDensity)) {
                    throw new RuntimeException("参数错误,密度为空(prodDensity):" + prodDensity);
                }
                warehouseCode = MapUtils.getString(messageBody, "warehouseCode");
                if (StringUtil.isEmpty(warehouseCode)) {
                    throw new RuntimeException("参数错误,仓库为空(warehouseCode):" + warehouseCode);
                }
                specsDesc = MapUtils.getString(messageBody, "specsDesc");
                if (StringUtil.isEmpty(specsDesc)) {
                    throw new RuntimeException("参数错误,规格为空(specsDesc):" + specsDesc);
                }
                packId = MapUtils.getString(messageBody, "packId");
                if (StringUtil.isEmpty(packId)) {
                    throw new RuntimeException("参数错误,捆包号为空(packId):" + packId);
                }
                factoryOrderNum = MapUtils.getString(messageBody, "factoryOrderNum");
                if (StringUtil.isEmpty(factoryOrderNum)) {
                    throw new RuntimeException("参数错误,材料管理号为空(factoryOrderNum):" + factoryOrderNum);
                }
                innerDiameter = MapUtils.getString(messageBody, "innerDiameter");
                if (StringUtil.isEmpty(innerDiameter)) {
                    throw new RuntimeException("参数错误,内径为空(innerDiameter):" + innerDiameter);
                }
                prodTypeId = MapUtils.getString(messageBody, "prodTypeId");
                if (StringUtil.isEmpty(prodTypeId)) {
                    throw new RuntimeException("参数错误,品种代码为空(prodTypeId):" + prodTypeId);
                }
                d_userNum = MapUtils.getString(messageBody, "d_userNum");
                if (StringUtil.isEmpty(d_userNum)) {
                    throw new RuntimeException("参数错误,客户代码为空(d_userNum):" + d_userNum);
                }
                productProcessId = MapUtils.getString(messageBody, "productProcessId");
            } else {
                List<HashMap> tlirl0503List;
                Map<String, Object> messageBody1 = new HashMap<>();
                if (StringUtils.isNotBlank(originalPackId)) {
                    messageBody1.put("packId", originalPackId);
                    messageBody1.put("segNo", messageBody.get("segNo"));
                    tlirl0503List = this.dao.query("LIDS0606.queryTlirl0503", messageBody1);
                } else {
                    tlirl0503List = this.dao.query("LIDS0606.queryTlirl0503", messageBody);
                }
                if (tlirl0503List.size() == 0) {
                    throw new RuntimeException("参数错误,未查询到贸易方式信息(tlirl0503List):" + tlirl0503List);
                }
                // 取出贸易方式信息
                Map tlirl0503 = tlirl0503List.get(0);
                /// originalPackId原始捆包号，取规格客户信息
                if (StringUtils.isNotBlank(originalPackId)) {
                    netWeight = MapUtils.getString(messageBody, "netWeight");
                    // 取出捆包号信息
                    packId = MapUtils.getString(messageBody, "packId");
                } else {
                    netWeight = MapUtils.getString(tlirl0503, "netWeight");
                    // 取出捆包号信息
                    packId = MapUtils.getString(tlirl0503, "packId");
                }
                // 取出首道加工工序信息
                productProcessId = MapUtils.getString(tlirl0503, "productProcessId");
                // 取出账套信息
                segNo = MapUtils.getString(tlirl0503, "segNo");
                // 取出业务类型信息
                tradeCode = "";
                // 取出密度信息
                prodDensity = MapUtils.getString(tlirl0503, "prodDensity");
                // 取出仓库信息
                warehouseCode = MapUtils.getString(tlirl0503, "warehouseCode");
                // 取出规格信息
                specsDesc = MapUtils.getString(tlirl0503, "specsDesc");
                // 取出工厂订单号信息
                factoryOrderNum = MapUtils.getString(tlirl0503, "factoryOrderNum");
                // 取出内径信息
                innerDiameter = MapUtils.getString(tlirl0503, "innerDiameter");
                // 取出品种代码信息
                prodTypeId = "";
                // 取出客户代码信息
                d_userNum = MapUtils.getString(tlirl0503, "customerId");
                // 重量
            }

            // 先进行校验，该捆包是否以存在库位，避免重复推荐
            HashMap<String, Object> params = new HashMap<>();
            params.put("packId", packId);
            params.put("factoryOrderNum", factoryOrderNum);
            params.put("segNo", segNo);
            // 查询有效的库位占用记录
            List<HashMap> list = this.dao.query("LIDS0606.query", params);
            if (list.size() > 0) {
                /// 进行库位释放操作
                // 调用捆包释放
                EiInfo releaseInfo = new EiInfo();
                Map<String, Object> releaseMessageBody = new HashMap<>();
                releaseMessageBody.put("packId", packId);
                releaseMessageBody.put("warehouseCode", list.get(0).get("wproviderId"));
                releaseMessageBody.put("factoryArea", list.get(0).get("factoryArea"));
                releaseMessageBody.put("crossArea", list.get(0).get("crossArea"));
                releaseMessageBody.put("factoryBuilding", list.get(0).get("factoryBuilding"));
                releaseInfo.set("messageBody", releaseMessageBody);
                warehouseRelease(releaseInfo);
                /*
                 * //存在库位占用记录，不推荐
                 * outInfo.setStatus(EiConstant.STATUS_FAILURE);
                 * outInfo.setMsg("该捆包号已存在库位占用记录，不推荐,库位代码为:" + list.get(0).get("locationId"));
                 * return outInfo;
                 */
            }
            // 宽 英文
            // 判断expression 是否存在两个星号
            if (specsDesc.indexOf('*') == -1 || specsDesc.indexOf('*', specsDesc.indexOf('*') + 1) == -1) {
                System.out.println("表达式中没有两个星号");
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("宽度截取失败，请检查规格是否正确！");
                return outInfo;
            }
            // 截取 expression 第一个*跟第二个*之间的字符串
            // System.out.println(specsDesc.substring(specsDesc.indexOf('*') + 1,
            // specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
            double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1,
                    specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
            // 转换重量
            double weight = Double.parseDouble(netWeight) * 1000;
            // 计算外径：根据内径计算外径 单位：毫米
            double externalDiameterNumber = calculateOuterDiameter(Double.parseDouble(innerDiameter), weight, width,
                    Double.parseDouble(prodDensity) * 1000);
            // 判断卷外径是否为空
            if (externalDiameterNumber == 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("卷外径计算失败！");
                return outInfo;
            }
            // externalDiameter 向上取整
            int externalDiameter = (int) Math.ceil(externalDiameterNumber);

            boolean isLargeVolume = false;
            // 判断是否为超大卷，根据重量，外径 进行判断 查询配置表数值；
            // 超大卷重量 吨
            String weightRestrict = switchValue(segNo, "OVERWEIGHT_ROLLS", warehouseCode);
            // 超大卷外径 mm
            String externalDiameterRestrict = switchValue(segNo, "SUPER_LARGE_VOLUME", warehouseCode);

            // 判断weightRestrict，externalDiameterRestrict是否为空，如果为空，则不进行超大卷处理，直接推荐库位
            if (!StringUtils.isEmpty(weightRestrict)) {
                // 比较weightRestrict，跟netWeight的大小，如果netWeight大于weightRestrict，正常推荐库位，否则进行超大卷处理
                if (Double.parseDouble(netWeight) >= Double.parseDouble(weightRestrict)) {
                    isLargeVolume = true;
                }
            }
            if (!StringUtils.isEmpty(externalDiameterRestrict)) {
                if (externalDiameter >= Double.parseDouble(externalDiameterRestrict)) {
                    isLargeVolume = true;
                }
            }

            // 外径偏差量 mm
            String outerDiameterDeviation = switchValue(segNo, "OUTER_DIAMETER_DEVIATION", warehouseCode);
            // 重量偏差量
            String weightDeviation = switchValue(segNo, "WEIGHT_DEVIATION", warehouseCode);
            // 宽度偏差量 mm
            String widthDeviation = switchValue(segNo, "WIDTH_DEVIATION", warehouseCode);
            // 下层卷间隔距离最大
            String intervalDistanceMax = switchValue(segNo, "INTERVAL_DISTANCE_MAX", warehouseCode);
            String intervalDistanceMini = switchValue(segNo, "INTERVAL_DISTANCE_MINI", warehouseCode);

            if (isLargeVolume) {
                // 超大卷处理逻辑
                /**
                 * 参数
                 * segNo 账套
                 * warehouseCode 仓库代码
                 * tradeCode 业务类型
                 * factoryArea 厂区
                 * factoryBuilding 厂房
                 * crossArea 跨区
                 * netWeight 捆包重量
                 * width 卷材宽度
                 * externalDiameter 卷外径
                 */
                String factoryArea = "";
                String factoryBuilding = "";
                String crossArea = "";
                // 查询超大库区配置表
                HashMap paramsLarge = new HashMap<>();
                paramsLarge.put("segNo", segNo);
                paramsLarge.put("warehouseCode", warehouseCode);
                // 查询生效的配置信息
                List<HashMap> listLarge = this.dao.query("LIDS0606.queryLarge", paramsLarge);
                if (listLarge.size() > 0) {
                    // 取跨区，厂区，厂房
                    factoryArea = (String) listLarge.get(0).get("factoryArea");
                    factoryBuilding = (String) listLarge.get(0).get("factoryBuilding");
                    crossArea = (String) listLarge.get(0).get("crossArea");
                } else {
                    // 不存在配置信息，不推荐
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("超大卷库区配置表不存在，不推荐！");
                    return outInfo;
                }
                Map<String, Object> map = locationMatching(segNo, warehouseCode, tradeCode, factoryOrderNum, netWeight,
                        width, externalDiameter, outerDiameterDeviation, weightDeviation, widthDeviation,
                        intervalDistanceMax, intervalDistanceMini, factoryArea, factoryBuilding, crossArea);
                // 判断map是否为空，为空则不推荐库位
                if (MapUtils.isEmpty(map)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("推荐库位处理失败！");
                    return outInfo;
                } else {
                    map.put("superLarge", false);
                    map.put("packId", packId);
                    map.put("factoryOrderNum", factoryOrderNum);
                    map.put("netWeight", netWeight);
                    map.put("width", width);
                    map.put("externalDiameter", externalDiameter);
                    map.put("innerDiameter", innerDiameter);
                    map.put("specsDesc", specsDesc);
                    map.put("segNo", segNo);
                    map.put("warehouseCode", warehouseCode);
                    map.put("tradeCode", tradeCode);
                    map.put("factoryArea", factoryArea);
                    map.put("factoryBuilding", factoryBuilding);
                    map.put("crossArea", crossArea);
                    map.put("productProcessId", productProcessId);
                    map.put("d_userNum", d_userNum);
                    map.put("prodTypeId", prodTypeId);
                    map.put("intervalDistanceMax", intervalDistanceMax);
                    map.put("intervalDistanceMini", intervalDistanceMini);

                    outInfo.setAttr(map);
                    // 调用库位信息更新接口
                    EiInfo eiInfo = occupy(map);
                    if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("库位占用失败！");
                        return outInfo;
                    }
                    outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    outInfo.setMsg("推荐库位成功！");
                    return outInfo;
                }

            } else {
                // 正常推荐库位（）
                // 先匹配厂区分配表，匹配不到数据根据仓库匹配等待车辆最少的跨区
                // 根据客户，品种，规格，首道加工工序，客户，业务类型 查询厂区配置表，
                Map map = new HashMap();
                map.put("segNo", segNo);
                map.put("warehouseCode", warehouseCode);
                map.put("tradeCode", tradeCode);
                map.put("packId", packId);
                map.put("factoryOrderNum", factoryOrderNum);
                map.put("netWeight", netWeight);
                map.put("width", width);
                map.put("externalDiameter", externalDiameter);
                map.put("innerDiameter", innerDiameter);
                map.put("superLarge", true);
                map.put("prodTypeId", prodTypeId);
                map.put("intervalDistanceMax", intervalDistanceMax);
                map.put("intervalDistanceMini", intervalDistanceMini);
                map.put("d_userNum", d_userNum);
                map.put("specsDesc", specsDesc);
                map.put("productProcessId", productProcessId);
                map.put("firstMachineCode", firstMachineCode);
                // 根据机组进行匹配，匹配不到，去掉机组 进行匹配
                List<HashMap> plantConfigurationList = new ArrayList<>();
                plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea", map);
                if (plantConfigurationList.size() == 0) {
                    map.put("firstMachineCode", "");
                    plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea", map);
                    if (plantConfigurationList.size() == 0) {
                        throw new RuntimeException("未匹配到符合条件的库位信息！");
                    }
                }
                map.put("d_userNum", d_userNum);
                map.put("specsDesc", specsDesc);
                map.put("productProcessId", productProcessId);
                Map<String, Object> locationMap = new HashMap<>();
                // 取厂区，厂房，跨区
                for (HashMap plantConfig : plantConfigurationList) {
                    String factoryArea = (String) plantConfig.get("factoryArea");
                    String factoryBuilding = (String) plantConfig.get("factoryBuilding");
                    String crossArea = (String) plantConfig.get("crossArea");
                    // 匹配库位
                    locationMap = normalLocationMatching(segNo, warehouseCode, tradeCode, factoryOrderNum, netWeight,
                            width, externalDiameter, outerDiameterDeviation, weightDeviation, widthDeviation,
                            intervalDistanceMax, intervalDistanceMini, factoryArea, factoryBuilding, crossArea,
                            storageMark);
                    if (MapUtils.isNotEmpty(locationMap)) {
                        break;
                    }
                }
                if (MapUtils.isEmpty(locationMap)) {
                    // 匹配不到,根据当前信息 查询轮询方案进行再次匹配
                    for (HashMap plantConfig : plantConfigurationList) {
                        String factoryAreaHost = (String) plantConfig.get("factoryArea");
                        String factoryBuildingHost = (String) plantConfig.get("factoryBuilding");
                        String crossAreaHost = (String) plantConfig.get("crossArea");
                        String pollingSchemeNumber = (String) plantConfig.get("pollingSchemeNumber");
                        if (StringUtils.isNotBlank(pollingSchemeNumber)) {
                            // 根据轮询方案进行再次匹配 查询轮询方案
                            HashMap pollingScheme = new HashMap<>();
                            pollingScheme.put("segNo", segNo);
                            pollingScheme.put("pollingSchemeNumber", pollingSchemeNumber);
                            pollingScheme.put("factoryArea", factoryAreaHost);
                            pollingScheme.put("factoryBuilding", factoryBuildingHost);
                            pollingScheme.put("crossArea", crossAreaHost);
                            List<HashMap> pollingSchemeList = this.dao.query("LIDS0606.queryPollingScheme",
                                    pollingScheme);
                            if (pollingSchemeList.size() > 0) {
                                for (HashMap polling : pollingSchemeList) {
                                    String factoryArea = (String) polling.get("factoryArea");
                                    String factoryBuilding = (String) polling.get("factoryBuilding");
                                    String crossArea = (String) polling.get("crossArea");
                                    locationMap = normalLocationMatching(segNo, warehouseCode, tradeCode,
                                            factoryOrderNum, netWeight, width, externalDiameter, outerDiameterDeviation,
                                            weightDeviation, widthDeviation, intervalDistanceMax, intervalDistanceMini,
                                            factoryArea, factoryBuilding, crossArea, storageMark);
                                    if (MapUtils.isNotEmpty(locationMap)) {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if (MapUtils.isEmpty(locationMap)) {
                        throw new RuntimeException("未匹配到符合条件的库位信息！");
                    } else {
                        locationMap.putAll(map);
                        EiInfo eiInfo = occupy(locationMap);
                        if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("库位占用失败！");
                            return outInfo;
                        }

                        outInfo.setAttr(locationMap);
                        return outInfo;
                    }
                } else {
                    // 库位占用逻辑
                    locationMap.putAll(map);
                    EiInfo eiInfo = occupy(locationMap);
                    if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("库位占用失败！");
                        return outInfo;
                    }

                    outInfo.setAttr(locationMap);
                    return outInfo;
                }
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 库位占用
     *
     * @param inInfo
     * @return
     */
    public EiInfo occupyingStorageSpace(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map messageBody = (Map) inInfo.get("messageBody");
            String segNo = MapUtils.getString(messageBody, "segNo");
            if (StringUtil.isEmpty(segNo)) {
                throw new RuntimeException("账套号不能为空！");
            }
            String warehouseCode = MapUtils.getString(messageBody, "warehouseCode");
            if (StringUtil.isEmpty(warehouseCode)) {
                throw new RuntimeException("仓库代码不能为空！");
            }
            String packId = MapUtils.getString(messageBody, "packId");
            if (StringUtil.isEmpty(packId)) {
                throw new RuntimeException("包装号不能为空！");
            }
            String factoryBuilding = MapUtils.getString(messageBody, "factoryBuilding");
            if (StringUtil.isEmpty(factoryBuilding)) {
                throw new RuntimeException("厂房不能为空！");
            }
            String crossArea = MapUtils.getString(messageBody, "crossArea");
            if (StringUtil.isEmpty(crossArea)) {
                throw new RuntimeException("跨区不能为空！");
            }
            String factoryArea = MapUtils.getString(messageBody, "factoryArea");
            if (StringUtil.isEmpty(factoryArea)) {
                throw new RuntimeException("厂区不能为空！");
            }
            HashMap<String, Object> params = new HashMap<>();
            params.put("segNo", segNo);
            params.put("warehouseCode", warehouseCode);
            params.put("factoryBuilding", factoryBuilding);
            params.put("crossArea", crossArea);
            params.put("factoryArea", factoryArea);
            params.put("packId", packId);
            RecordUtils.setRevisor(params);
            // 更改捆包占用状态
            this.dao.update("LIDS0606.updatePackStatus", params);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("库位占用成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    // 计算外径 方法
    /*
     * innerDiameter 内径 单位：毫米
     * weight 卷材重量 单位：千克
     * width 卷材宽度 单位：毫米
     * density 卷材密度 单位：千克/立方米
     */
    public static double calculateOuterDiameter(double innerDiameter, double weight, double width, double density) {
        try {
            double innerDiameterMeters = innerDiameter / 1000.0; // 内径转换为米
            double widthMeters = width / 1000.0; // 宽度转换为米
            // 计算外径
            // 公式：外径 = 根号[(4*重量)/(π*宽度*密度) + 内径^2]
            double outerDiameterMeters = Math
                    .sqrt((4 * weight) / (Math.PI * widthMeters * density) + Math.pow(innerDiameterMeters, 2));
            return outerDiameterMeters * 1000.0; // 将外径转换回毫米
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    // 计算外径 方法
    /*
     * segNo 账套
     * key 键值
     * warehouseCode 仓库代码
     */
    public String switchValue(String segNo, String key, String warehouseCode) {
        try {
            String value = "";
            HashMap params = new HashMap<>();
            params.put("segNo", segNo);
            params.put("key", key);
            params.put("warehouseCode", warehouseCode);
            List<HashMap> list = this.dao.query("LIDS0606.querySwitchValue", params);
            if (list.size() > 0) {
                value = (String) list.get(0).get("configureValue");
            }
            return value;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    // 超大卷处理逻辑（条状）
    /*
     * segNo 账套
     * warehouseCode 仓库代码
     * tradeCode 业务类型
     * factoryArea 厂区
     * factoryBuilding 厂房
     * crossArea 跨区
     * netWeight 捆包重量
     * width 卷材宽度
     * externalDiameter 卷外径
     * outerDiameterDeviation 外径偏差量
     * weightDeviation 重量偏差量
     * widthDeviation 宽度偏差量
     * intervalDistance 下层卷间隔距离
     */
    public Map<String, Object> locationMatching(String segNo, String warehouseCode, String tradeCode,
            String factoryOrderNum, String netWeight, double width, double externalDiameter,
            String outerDiameterDeviation, String weightDeviation, String widthDeviation, String intervalDistanceMax,
            String intervalDistanceMini, String factoryArea, String factoryBuilding, String crossArea) {
        try {
            // 根据 仓库，厂房，跨区，宽度上限限，匹配满足条件的库位信息 根据库位，连续空闲区间，仓库进行去重，去重后的数据计算连续空闲区间跟外径进行对比是否能放下卷，
            // 再根据序号码，库位顺序进行排序
            HashMap<String, Object> mapLocation = new HashMap<>();
            HashMap<String, Object> params = new HashMap<>();
            params.put("segNo", segNo);
            params.put("warehouseCode", warehouseCode);
            params.put("tradeCode", tradeCode);
            params.put("factoryOrderNum", factoryOrderNum);
            params.put("netWeight", netWeight);
            params.put("width", width);
            params.put("externalDiameter", externalDiameter);// 卷外径
            params.put("outerDiameterDeviation", outerDiameterDeviation);
            params.put("weightDeviation", weightDeviation);
            params.put("widthDeviation", widthDeviation);
            params.put("intervalDistanceMax", Integer.parseInt(intervalDistanceMax));// 下层卷间隔距离
            params.put("intervalDistanceMini", Integer.parseInt(intervalDistanceMini));// 下层卷间隔距离

            params.put("factoryArea", factoryArea);
            params.put("factoryBuilding", factoryBuilding);
            params.put("crossArea", crossArea);
            // 只查询条状库位（目前只有条状库位）
            List<HashMap> list = this.dao.query("LIDS0606.queryMatching", params);// 重新连续可用区间长度是否满足条件 当前跨下
            if (list.size() > 0) {
                // 在进行过滤数据
                for (HashMap map : list) {
                    // 根据库位id查询当前库位下可用的区间位置
                    params.put("locationId", map.get("location_id"));
                    List<HashMap> listLitter = this.dao.query("LIDS0606.queryMatching", params);// 重新连续可用区间长度是否满足条件 当前库位
                    if (listLitter.size() > 0) {
                        HashMap<String, Object> mapLocationOrther = new HashMap<>();
                        // 默认第一个信息进行返回，后续进行校验 满足条件更新信息
                        BigDecimal middle = new BigDecimal("0");
                        // 默认靠左放
                        // mapLocation.put("rightViewId", (String)
                        // listLitter.get(0).get("right_view_id"));
                        for (HashMap mapLitter : listLitter) {
                            mapLocationOrther.put("standFlag", listLitter.get(0).get("stand_flag"));// 库位代码
                            mapLocationOrther.put("locationId", listLitter.get(0).get("location_id"));// 库位代码
                            mapLocationOrther.put("idleIntervalId", listLitter.get(0).get("idle_interval_id"));// 连续空闲区间编号
                            mapLocationOrther.put("externalDiameter", externalDiameter);
                            mapLocationOrther.put("warehouseCode", warehouseCode);
                            mapLocationOrther.put("locationType", "20"); // 默认条状库位
                            mapLocationOrther.put("updateFlag", "1"); // 默认下层 超大卷没有上层

                            mapLocationOrther.put("pointLowerLength", mapLitter.get("pointLowerLength")); // 上下限制
                            mapLocationOrther.put("pointUpperLength", mapLitter.get("pointUpperLength"));
                            mapLocationOrther.put("specUpper", mapLitter.get("specUpper"));
                            mapLocationOrther.put("specLower", mapLitter.get("specLower"));

                            // 判断当前循环是否最后一次,最后一次循环都不满足条件
                            // 根据库位id查询当前库位下可用的区间位置
                            // 当连续空闲区间左右两边是否为占用
                            List listQuery = new ArrayList<>();
                            BigDecimal freeLength = new BigDecimal(String.valueOf(mapLitter.get("free_length")));
                            String leftViewPackId = (String) mapLitter.get("left_view_pack_id");
                            String rightViewPackId = (String) mapLitter.get("right_view_pack_id");
                            if (StringUtils.isEmpty(leftViewPackId) && StringUtils.isEmpty(rightViewPackId)) {
                                // middle = Integer.valueOf((String) mapLitter.get("x_point_start")) + (int)
                                // externalDiameter / 2;
                                middle = new BigDecimal(String.valueOf(mapLitter.get("x_point_start")))
                                        .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                                mapLocationOrther.put("middle", middle);
                                return mapLocationOrther;
                            }
                            // 声明剩余可用长度
                            BigDecimal remainingLength = new BigDecimal("0");
                            // 判断当前区间是否刚好放下卷
                            if (StringUtils.isEmpty(rightViewPackId)) {
                                remainingLength = freeLength.subtract(new BigDecimal(externalDiameter));
                            } else {
                                remainingLength = freeLength.subtract(new BigDecimal(externalDiameter))
                                        .subtract(new BigDecimal(intervalDistanceMini));
                            }
                            params.put("locationViewId", mapLitter.get("location_id"));
                            // 空闲位置大于 当前卷的外径 判断remainingLength是否大于0
                            if (remainingLength.compareTo(BigDecimal.ZERO) == 1) {
                                if (StringUtils.isNotBlank(String.valueOf(mapLitter.get("left_view_pack_id")))) {
                                    listQuery.clear();
                                    listQuery.add(String.valueOf(mapLitter.get("left_view_pack_id")));
                                    params.put("listQuery", listQuery);// 最小库位编号
                                    boolean isAdjacent = checkAdjacent(params);
                                    if (isAdjacent) {
                                        // 靠左放 当前x轴开始+externalDiameter/2 得到中间坐标点进行返回
                                        // 求中间坐标
                                        // middle = Integer.valueOf((String) mapLitter.get("x_point_start")) + (int)
                                        // externalDiameter / 2;
                                        middle = new BigDecimal(String.valueOf(mapLitter.get("x_point_start")))
                                                .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                                        mapLocationOrther.put("middle", middle);
                                        mapLocationOrther.put("leftViewPackId", leftViewPackId);
                                        mapLocationOrther.put("rightViewPackId", "");
                                        mapLocationOrther.put("near", "left");
                                        return mapLocationOrther;
                                    }
                                } else if (StringUtils.isEmpty(String.valueOf(mapLitter.get("right_view_pack_id")))) {
                                    // 清空listQuery
                                    listQuery.clear();
                                    // 添加右边库位编号
                                    listQuery.add(String.valueOf(mapLitter.get("right_view_pack_id")));
                                    boolean isAdjacentRight = checkAdjacent(params);
                                    if (isAdjacentRight) {
                                        // 靠右放 当前x轴开始+externalDiameter/2 得到中间坐标点进行返回
                                        // 求中间坐标
                                        middle = new BigDecimal(String.valueOf(mapLitter.get("x_point_end")))
                                                .subtract(new BigDecimal(String.valueOf(intervalDistanceMini)))
                                                .subtract(new BigDecimal(String.valueOf(externalDiameter / 2)));
                                        mapLocationOrther.put("middle", middle);
                                        mapLocationOrther.put("rightViewPackId", rightViewPackId);
                                        mapLocationOrther.put("leftViewPackId", "");
                                        mapLocationOrther.put("near", "right");
                                        return mapLocationOrther;
                                    }
                                } else {
                                    // middle = Integer.valueOf((String) mapLitter.get("x_point_start")) + (int)
                                    // externalDiameter / 2;
                                    middle = new BigDecimal(String.valueOf(mapLitter.get("x_point_start")))
                                            .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                                    mapLocationOrther.put("middle", middle);
                                    mapLocationOrther.put("leftViewPackId", "");
                                    mapLocationOrther.put("rightViewPackId", "");
                                    mapLocationOrther.put("near", "left");

                                }
                            } else
                            // 当前位置刚好放下卷
                            if (remainingLength.compareTo(BigDecimal.ZERO) == 0) {
                                if (StringUtils.isEmpty(String.valueOf(mapLitter.get("right_view_id")))) {
                                    listQuery.add(String.valueOf(mapLitter.get("right_view_id")));
                                }
                                if (StringUtils.isEmpty(String.valueOf(mapLitter.get("left_view_pack_id")))) {
                                    listQuery.add(String.valueOf(mapLitter.get("left_view_pack_id")));
                                }
                                boolean isAdjacent = checkAdjacent(params);
                                if (isAdjacent) {
                                    // middle = Integer.valueOf((String) mapLitter.get("x_point_start")) + (int)
                                    // externalDiameter / 2;
                                    middle = new BigDecimal(String.valueOf(mapLitter.get("x_point_start")))
                                            .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                                    mapLocationOrther.put("rightViewPackId", rightViewPackId);
                                    mapLocationOrther.put("leftViewPackId", leftViewPackId);
                                    mapLocationOrther.put("middle", middle);
                                    mapLocationOrther.put("near", "");
                                    return mapLocationOrther;
                                }
                            }
                        }
                    }
                }
            }
            return mapLocation;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    // 条状正常卷匹配逻辑
    /*
     * segNo 账套
     * warehouseCode 仓库代码
     * tradeCode 业务类型
     * factoryArea 厂区
     * factoryBuilding 厂房
     * crossArea 跨区
     * netWeight 捆包重量
     * width 卷材宽度
     * externalDiameter 卷外径
     * outerDiameterDeviation 外径偏差量
     * weightDeviation 重量偏差量
     * widthDeviation 宽度偏差量
     * intervalDistance 下层卷间隔距离
     */
    public Map<String, Object> normalLocationMatching(String segNo, String warehouseCode, String tradeCode,
            String factoryOrderNum, String netWeight, double width, double externalDiameter,
            String outerDiameterDeviation, String weightDeviation, String widthDeviation, String intervalDistanceMax,
            String intervalDistanceMini, String factoryArea, String factoryBuilding, String crossArea,
            String storageMark) {
        try {
            // 根据 仓库，厂房，跨区，宽度上限限，匹配满足条件的库位信息 根据库位，连续空闲区间，仓库进行去重，去重后的数据计算连续空闲区间跟外径进行对比是否能放下卷，
            // 再根据序号码，库位顺序进行排序
            HashMap<String, Object> mapLocationNormal = new HashMap<>();
            HashMap<String, Object> params = new HashMap<>();
            params.put("segNo", segNo);
            params.put("warehouseCode", warehouseCode);
            params.put("tradeCode", tradeCode);
            params.put("factoryOrderNum", factoryOrderNum);
            params.put("netWeight", netWeight);
            params.put("width", width);
            params.put("externalDiameter", externalDiameter);// 卷外径
            params.put("outerDiameterDeviation", outerDiameterDeviation);
            params.put("weightDeviation", weightDeviation);// 重量偏差量
            params.put("widthDeviation", widthDeviation);
            params.put("intervalDistanceMax", Integer.parseInt(intervalDistanceMax));// 下层卷间隔距离
            params.put("intervalDistanceMini", Integer.parseInt(intervalDistanceMini));// 下层卷间隔距离

            // factoryArea去除空格
            params.put("factoryArea", factoryArea.replaceAll(" ", ""));
            params.put("factoryBuilding", factoryBuilding.replaceAll(" ", ""));
            params.put("crossArea", crossArea.replaceAll(" ", ""));

            BigDecimal middle = new BigDecimal("0");
            // 根据 仓库，厂房，跨区，宽度上限限，匹配满足条件的库位信息 根据库位，连续空闲区间，仓库进行去重，去重后的数据计算连续空闲区间跟外径进行对比是否能放下卷，
            // 查询上层满足条件的库位信息；（优先上层匹配）
            List<HashMap> listUp = this.dao.query("LIDS0606.queryMatchingUp", params);
            if (listUp.size() > 0) {
                // 满足条件直接返回库位信息。
                // 拿到当前库位左侧下层的x轴结束坐标，右侧下层的x轴开始坐标 计算中间点坐标,计算重量差异，外径差异
                for (HashMap map : listUp) {
                    // 设置默认参数
                    mapLocationNormal.put("locationId", map.get("location_id"));// 库位代码
                    mapLocationNormal.put("locationName", map.get("location_name"));// 库位名称

                    // mapLocationNormal.put("idleIntervalId",
                    // map.get("idle_interval_id"));//连续空闲区间编号
                    mapLocationNormal.put("updateFlag", "2");// 连续空闲区间编号
                    mapLocationNormal.put("specUpper", map.get("specUpper"));
                    mapLocationNormal.put("specLower", map.get("specLower"));
                    mapLocationNormal.put("pointLowerLength", map.get("pointLowerLength")); // 上下限制
                    mapLocationNormal.put("pointUpperLength", map.get("pointUpperLength"));
                    mapLocationNormal.put("factoryArea", map.get("factoryArea"));
                    mapLocationNormal.put("factoryBuilding", map.get("factoryBuilding"));
                    mapLocationNormal.put("crossArea", map.get("crossArea"));

                    // 计算中间坐标
                    // 中间坐标 = 左侧X轴结束+（右侧X轴开始-左侧X轴结束 /2）
                    BigDecimal leftXPointEnd = new BigDecimal(String.valueOf(map.get("left_x_point_end")));
                    BigDecimal rightXPointStart = new BigDecimal(String.valueOf(map.get("right_x_point_start")));
                    // 计算中间位置
                    middle = leftXPointEnd.add(rightXPointStart.subtract(leftXPointEnd).divide(new BigDecimal("2"), 2,
                            BigDecimal.ROUND_HALF_UP));
                    mapLocationNormal.put("middle", middle);
                    mapLocationNormal.put("leftViewPackId", map.get("left_view_pack_id"));
                    mapLocationNormal.put("rightViewPackId", map.get("right_view_pack_id"));
                    mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));
                }
            }

            if (mapLocationNormal.size() == 0) {
                // 两侧有卷
                List<HashMap> listDownTwo = this.dao.query("LIDS0606.queryMatchingDownTwo", params);// 查询到下层符合条件的库位,优先查询两侧有卷的数据
                if (listDownTwo.size() > 0) {
                    for (HashMap map : listDownTwo) {
                        map.put("externalDiameter", externalDiameter);
                        if ("1".equals(storageMark)) {
                            Boolean a = occupancyRatio(map);
                            if (a) {
                                continue;
                            }
                        }
                        // 设置默认参数
                        mapLocationNormal.put("locationId", map.get("location_id"));// 库位代码
                        mapLocationNormal.put("locationName", map.get("location_name"));// 库位名称

                        mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));// 连续空闲区间编号
                        mapLocationNormal.put("updateFlag", "1");// 连续空闲区间编号
                        mapLocationNormal.put("specUpper", map.get("specUpper"));
                        mapLocationNormal.put("specLower", map.get("specLower"));
                        mapLocationNormal.put("pointLowerLength", map.get("pointLowerLength")); // 上下限制
                        mapLocationNormal.put("pointUpperLength", map.get("pointUpperLength"));
                        mapLocationNormal.put("factoryArea", map.get("factoryArea"));
                        mapLocationNormal.put("factoryBuilding", map.get("factoryBuilding")); // 上下限制
                        mapLocationNormal.put("crossArea", map.get("crossArea"));
                        mapLocationNormal.put("standFlag,", map.get("stand_flag"));

                        BigDecimal freeLength = new BigDecimal(String.valueOf(map.get("free_length")));
                        // 可用长度减去卷的外径，得到剩余可用长度
                        BigDecimal remainingLength = freeLength.subtract(new BigDecimal(externalDiameter))
                                .subtract(new BigDecimal(intervalDistanceMini));
                        // 计算中间坐标
                        middle = new BigDecimal(String.valueOf(map.get("x_point_start")))
                                .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                        mapLocationNormal.put("middle", middle);
                        mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));

                        // remainingLength > 0 靠左放 还有剩余空闲长度，返回左侧捆包编号，没有剩余距离，返回左侧右侧捆包编号
                        if (remainingLength.compareTo(BigDecimal.ZERO) == 1) {
                            mapLocationNormal.put("leftViewPackId", map.get("left_view_pack_id"));
                            mapLocationNormal.put("rightViewPackId", "");
                        } else {
                            mapLocationNormal.put("leftViewPackId", map.get("left_view_pack_id"));
                            mapLocationNormal.put("rightViewPackId", map.get("right_view_pack_id"));
                        }
                        if (mapLocationNormal.size() > 0) {
                            // 停止循环
                            break;
                        }
                    }
                }
            }
            // 判断mapLocationNormal是否为空，为空则继续查询下层数据
            if (mapLocationNormal.size() == 0) {
                // 一侧有卷
                List<HashMap> listDownOne = this.dao.query("LIDS0606.queryMatchingDownOne", params);// 查询一侧有卷的数据
                if (listDownOne.size() > 0) {
                    for (HashMap map : listDownOne) {
                        map.put("externalDiameter", externalDiameter);
                        if ("1".equals(storageMark)) {
                            Boolean a = occupancyRatio(map);
                            if (a) {
                                continue;
                            }
                        }
                        // 设置默认参数
                        mapLocationNormal.put("locationId", map.get("location_id"));// 库位代码
                        mapLocationNormal.put("locationName", map.get("location_name"));// 库位名称
                        mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));// 连续空闲区间编号
                        mapLocationNormal.put("updateFlag", "1");// 连续空闲区间编号
                        mapLocationNormal.put("specUpper", map.get("specUpper"));
                        mapLocationNormal.put("specLower", map.get("specLower"));
                        mapLocationNormal.put("pointLowerLength", map.get("pointLowerLength")); // 上下限制
                        mapLocationNormal.put("pointUpperLength", map.get("pointUpperLength"));
                        mapLocationNormal.put("factoryArea", map.get("factoryArea"));
                        mapLocationNormal.put("factoryBuilding", map.get("factoryBuilding")); // 上下限制
                        mapLocationNormal.put("crossArea", map.get("crossArea"));
                        mapLocationNormal.put("standFlag,", map.get("stand_flag"));

                        BigDecimal freeLength = new BigDecimal(String.valueOf(map.get("free_length")));// 可用长度

                        String leftViewPackId = (String) map.get("left_view_pack_id");// 左侧捆包编号
                        String rightViewPackId = (String) map.get("right_view_pack_id");// 右侧捆包编号
                        if (StringUtils.isNotBlank(rightViewPackId)) {
                            // 靠右放
                            // 计算中间位置
                            middle = new BigDecimal(String.valueOf(map.get("x_point_end")))
                                    .subtract(new BigDecimal(String.valueOf(intervalDistanceMini)))
                                    .subtract(new BigDecimal(String.valueOf(externalDiameter / 2)));
                            mapLocationNormal.put("middle", middle);
                            mapLocationNormal.put("rightViewPackId", rightViewPackId);
                            mapLocationNormal.put("leftViewPackId", "");
                        }
                        if (StringUtils.isNotBlank(leftViewPackId)) {
                            // 靠左放
                            // 计算中间位置
                            middle = new BigDecimal(String.valueOf(map.get("x_point_start")))
                                    .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                            mapLocationNormal.put("middle", middle);
                            mapLocationNormal.put("leftViewPackId", leftViewPackId);
                            mapLocationNormal.put("rightViewPackId", "");
                            mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));
                        }
                        if (mapLocationNormal.size() > 0) {
                            // 停止循环
                            break;
                        }
                    }

                }
            }
            if (mapLocationNormal.size() == 0) {
                // 两侧无卷
                List<HashMap> listDown = this.dao.query("LIDS0606.queryMatchingDown", params);// 查询两侧无卷的数据
                if (listDown.size() > 0) {
                    // 靠左放
                    for (HashMap map : listDown) {
                        map.put("externalDiameter", externalDiameter);
                        if ("1".equals(storageMark)) {
                            Boolean a = occupancyRatio(map);
                            if (a) {
                                continue;
                            }
                        }
                        // 设置默认参数
                        mapLocationNormal.put("locationId", map.get("location_id"));// 库位代码
                        mapLocationNormal.put("locationName", map.get("location_name"));// 库位名称

                        mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));// 连续空闲区间编号
                        mapLocationNormal.put("updateFlag", "1");// 连续空闲区间编号
                        mapLocationNormal.put("specUpper", map.get("specUpper"));
                        mapLocationNormal.put("specLower", map.get("specLower"));
                        mapLocationNormal.put("pointLowerLength", map.get("pointLowerLength")); // 上下限制
                        mapLocationNormal.put("pointUpperLength", map.get("pointUpperLength"));
                        mapLocationNormal.put("standFlag,", map.get("stand_flag"));
                        mapLocationNormal.put("factoryArea", map.get("factoryArea"));
                        mapLocationNormal.put("factoryBuilding", map.get("factoryBuilding")); // 上下限制
                        mapLocationNormal.put("crossArea", map.get("crossArea"));
                        BigDecimal freeLength = new BigDecimal(String.valueOf(map.get("free_length")));// 可用长度
                        // 计算中间位置
                        middle = new BigDecimal(String.valueOf(map.get("x_point_start")))
                                .add(new BigDecimal(String.valueOf(externalDiameter / 2)));
                        mapLocationNormal.put("middle", middle);
                        mapLocationNormal.put("rightViewPackId", "");
                        mapLocationNormal.put("leftViewPackId", "");
                        mapLocationNormal.put("idleIntervalId", map.get("idle_interval_id"));

                        if (mapLocationNormal.size() > 0) {
                            // 停止循环
                            break;
                        }
                    }

                }
            }

            return mapLocationNormal;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /* 计算库位占用百分比 */
    public boolean occupancyRatio(HashMap params) {
        try {
            String externalDiameter = String.valueOf(params.get("externalDiameter"));
            String locationId = (String) params.get("location_id");
            String segNo = (String) params.get("segNo");
            String factoryArea = (String) params.get("factoryArea");
            String factoryBuilding = (String) params.get("factoryBuilding");
            String crossArea = (String) params.get("crossArea");
            Map<String, Object> params1 = new HashMap<>();
            params1.put("segNo", segNo);
            params1.put("locationId", locationId);
            params1.put("externalDiameter", externalDiameter);
            params1.put("factoryArea", factoryArea);
            params1.put("factoryBuilding", factoryBuilding);
            params1.put("crossArea", crossArea);
            List<HashMap> listUpLength = this.dao.query("LIDS0606.queryMatchingUpLength", params1);
            // 查询0605表当前库位总占用长度
            List<HashMap> listUpOccupiedLength = this.dao.query("LIDS0606.queryMatchingUpOccupiedLength", params1);
            if (listUpLength.size() > 0) {
                if (listUpOccupiedLength.size() > 0) {
                    double totalLength = Double.parseDouble(String.valueOf(listUpLength.get(0).get("total_length")));
                    double occupiedLength = Double
                            .parseDouble(String.valueOf(listUpOccupiedLength.get(0).get("occupied_length")));
                    occupiedLength += Double.parseDouble(String.valueOf(externalDiameter));
                    if (occupiedLength / (totalLength * 10) > 0.8) {
                        // 占用百分比大于80% 不进行当前库位推荐
                        return true;
                    }
                } else {
                    double totalLength = Double.parseDouble(String.valueOf(listUpLength.get(0).get("total_length")));
                    double occupiedLength = 0.00;
                    occupiedLength += Double.parseDouble(String.valueOf(externalDiameter));
                    if (occupiedLength / (totalLength * 10) > 0.8) {
                        // 占用百分比大于80% 不进行当前库位推荐
                        return true;
                    }
                }

            }
            return false;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*
     * 库位占用逻辑处理
     */
    public EiInfo occupy(Map<String, Object> messageBody) {
        try {
            EiInfo eiInfo = new EiInfo();
            // 库位信息更新接口
            HashMap<String, Object> params = new HashMap<>();
            Object idleIntervalValue = messageBody.get("idleIntervalId");
            BigDecimal idleIntervalId = idleIntervalValue != null ? new BigDecimal(idleIntervalValue.toString())
                    : BigDecimal.ZERO; // 或者根据需要使用其他默认值
            // 更新当前连续空闲区间的X轴开始结束，左侧存在捆包更新左侧捆包的 左侧捆包，当前连续空闲区间的左侧捆包，右侧存在捆包更新右侧捆包的
            // 右侧捆包，当前连续空闲区间的右侧捆包
            // BigDecimal idleIntervalId = new
            // BigDecimal(String.valueOf(messageBody.get("idleIntervalId") !=
            // null));//连续konan空闲区间编号
            String locationId = (String) messageBody.get("locationId");// 库位编号
            String segNo = (String) messageBody.get("segNo");// 账套
            String leftViewPackId = (String) messageBody.get("leftViewPackId");// 左侧捆包编号
            String rightViewPackId = (String) messageBody.get("rightViewPackId");// 右侧捆包编号
            BigDecimal externalDiameter = new BigDecimal(String.valueOf(messageBody.get("externalDiameter")));// 卷外径
            String packId = (String) messageBody.get("packId");// 捆包编号
            String factoryOrderNum = (String) messageBody.get("factoryOrderNum");// 工厂订单号
            BigDecimal netWeight = new BigDecimal(String.valueOf(messageBody.get("netWeight")));// 捆包重量
            BigDecimal width = new BigDecimal(String.valueOf(messageBody.get("width")));// 卷材宽度
            BigDecimal innerDiameter = new BigDecimal(String.valueOf(messageBody.get("innerDiameter")));// 内径
            String specsDesc = (String) messageBody.get("specsDesc");// 规格描述
            String factoryArea = (String) messageBody.get("factoryArea");// 厂区
            String factoryBuilding = (String) messageBody.get("factoryBuilding");// 厂房
            String crossArea = (String) messageBody.get("crossArea");// 跨区
            String productProcessId = (String) messageBody.get("productProcessId");// 产品工序号
            String d_userNum = (String) messageBody.get("d_userNum");// 登记人工号
            String prodTypeId = (String) messageBody.get("prodTypeId");// 产品类型编号
            BigDecimal intervalDistanceMini = new BigDecimal(String.valueOf(messageBody.get("intervalDistanceMini")));// 下层卷间隔距离
            BigDecimal intervalDistanceMax = new BigDecimal(String.valueOf(messageBody.get("intervalDistanceMax")));// 下层卷间隔距离

            String standFlag = (String) messageBody.get("standFlag");// 0卧式1立式
            String warehouseCode = (String) messageBody.get("warehouseCode");// 仓库代码
            String locationType = (String) messageBody.get("locationType");// 库位类型
            String updateFlag = (String) messageBody.get("updateFlag"); // 1下层，2上层
            BigDecimal specUpper = new BigDecimal(String.valueOf(messageBody.get("specUpper"))); // 1下层，2上层
            BigDecimal specLower = new BigDecimal(String.valueOf(messageBody.get("specLower")));
            BigDecimal pointLowerLength = new BigDecimal(String.valueOf(messageBody.get("pointLowerLength")));
            BigDecimal pointUpperLength = new BigDecimal(String.valueOf(messageBody.get("pointUpperLength")));

            Map locationMap = new HashMap();
            locationMap.put("segNo", segNo);
            locationMap.put("wproviderId", warehouseCode);
            locationMap.put("locationId", locationId);
            locationMap.put("specUpper", specUpper);
            locationMap.put("specLower", specLower);
            locationMap.put("factoryArea", factoryArea);
            locationMap.put("crossArea", crossArea);
            locationMap.put("factoryBuilding", factoryBuilding);
            locationMap.put("pointLowerLength", pointLowerLength);
            locationMap.put("pointUpperLength", pointUpperLength);

            boolean superLarge = messageBody.get("superLarge") != null;
            if ("1".equals(updateFlag)) {
                HashMap<String, Object> paramsIdleInterval = new HashMap<>();
                paramsIdleInterval.put("segNo", segNo);
                paramsIdleInterval.put("locViewPackId", packId);
                paramsIdleInterval.put("factoryArea", factoryArea);
                paramsIdleInterval.put("factoryBuilding", factoryBuilding);
                paramsIdleInterval.put("crossArea", crossArea);
                paramsIdleInterval.put("locationId", locationId);
                paramsIdleInterval.put("idleIntervalId", idleIntervalId);
                paramsIdleInterval.put("warehouseCode", warehouseCode);
                List<HashMap> listIdleInterval = this.dao.query("LIDS0606.queryIdleInterval", paramsIdleInterval);// 查询连续空闲区间信息
                BigDecimal xs = new BigDecimal("0");
                BigDecimal xe = new BigDecimal("0");
                BigDecimal remainingLength = new BigDecimal("0");
                if (listIdleInterval.size() > 0) {
                    // 新增上层库位参数信息
                    // X轴开始
                    xs = new BigDecimal(String.valueOf(listIdleInterval.get(0).get("x_point_start")));
                    // X轴结束
                    // xe = (String) ;
                    xe = new BigDecimal(String.valueOf(listIdleInterval.get(0).get("x_point_end")));
                    // 连续空闲区间存在，判断当前卷是否把连续空闲区完全占用
                    BigDecimal freeLength = new BigDecimal(String.valueOf(listIdleInterval.get(0).get("free_length")));
                    // 可用长度减去卷的外径，得到剩余可用长度
                    if (StringUtils.isNotBlank(rightViewPackId)) {
                        // remainingLength = freeLength - Integer.parseInt(externalDiameter) -
                        // Integer.parseInt(intervalDistance);
                        remainingLength = freeLength.subtract(externalDiameter).subtract(intervalDistanceMini);
                    } else {
                        remainingLength = freeLength.subtract(externalDiameter);
                    }
                } else {
                    throw new RuntimeException("连续空闲区间不存在");
                }

                // 新增一条占用信息
                LIDS0605 lids0605 = new LIDS0605();
                // 左右都为空，靠左放，新增一条占用信息
                lids0605.setLocViewPackId(packId);// 捆包编号
                // lids0605.setX_pointEnd(String.valueOf(Integer.parseInt(xs) +
                // Integer.parseInt(externalDiameter) +
                // Integer.parseInt(intervalDistance)));//X轴结束
                // lids0605.setX_pointEnd(new BigDecimal(xs).add(new
                // BigDecimal(externalDiameter).add(new BigDecimal(intervalDistance))));//X轴结束
                lids0605.setX_pointEnd(xs.add(externalDiameter).add(intervalDistanceMini));// X轴结束
                lids0605.setX_pointStart(xs);// X轴开始
                lids0605.setOuterDiameter(externalDiameter);// 卷外径
                lids0605.setInnerDiameter(innerDiameter);// 内径
                lids0605.setSpec(specsDesc);// 规格描述
                lids0605.setPutinWeight(netWeight);// 捆包重量
                lids0605.setLocationType("20");
                lids0605.setUpDownFlag(updateFlag);
                lids0605.setRefWidth(width);// 卷材宽度
                lids0605.setLocationId(locationId);// 库位编号
                lids0605.setSegNo(segNo);// 账套
                lids0605.setUnitCode(segNo);// 账套
                lids0605.setUseStatus("20");// 占用状态
                lids0605.setLeftViewPackId(leftViewPackId);// 左侧捆包编号
                lids0605.setRightViewPcakId(rightViewPackId);// 右侧捆包编号
                lids0605.setFactoryArea(factoryArea);// 厂区
                lids0605.setFactoryBuilding(factoryBuilding);// 厂房
                lids0605.setCrossArea(crossArea);// 跨区
                lids0605.setProductTypeId(prodTypeId);// 产品类型编号
                lids0605.setCustomerId(d_userNum);//
                lids0605.setStandFlag(standFlag);//
                lids0605.setWproviderId(warehouseCode);
                lids0605.setSpecUpper(specUpper);// 上下限制
                lids0605.setSpecLower(specLower);//
                lids0605.setPointLowerLength(pointLowerLength);//
                lids0605.setPointUpperLength(pointUpperLength);//
                lids0605.setFactoryProductId(factoryOrderNum);// 工厂订单号
                // 设置创建人信息
                lids0605.setRecCreator(UserSession.getUserId());
                // 创建人姓名
                lids0605.setRecCreatorName(UserSession.getLoginCName());
                // 创建时间
                lids0605.setRecCreateTime(DateUtil.curDateTimeStr14());
                // 修改人工号
                lids0605.setRecRevisor(UserSession.getUserId());
                // 修改人姓名
                lids0605.setRecRevisorName(UserSession.getLoginCName());
                // 修改时间
                lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
                // UUID
                lids0605.setUuid(UUIDUtils.getUUID());
                // 删除标记: 0-新增;1-删除;
                lids0605.setDelFlag(0);
                lids0605.setArchiveFlag("0");
                lids0605.setTenantId(" ");
                this.dao.insert("LIDS0605.insert", lids0605);// 新增一条占用信息
                if (StringUtils.isEmpty(leftViewPackId) && StringUtils.isEmpty(rightViewPackId)) {
                    // 更新当前库位的连续空闲区间信息
                    // 根据账套，厂区，厂房，跨区，库位编号，连续空闲区间，查询连续空闲区间信息
                    // 判断当前卷是否把连续空闲区完全占用，完全占用，连续空闲区间更新为删除状态
                    // 应该只能查出一条数据，查询多条库位连续空闲区间编号有问题
                    // 可用长度减去卷的外径，得到剩余可用长度
                    if (remainingLength.compareTo(BigDecimal.ZERO) == 0) {
                        // 当前卷把连续空闲区完全占用，连续空闲区间更新为删除状态
                        this.dao.update("LIDS0606.deleteIdleInterval", paramsIdleInterval);// 连续空闲区间更新为删除状态
                    } else {
                        // 当前卷不把连续空闲区完全占用，连续空闲区间X轴开始结束更新
                        // paramsIdleInterval.put("x_point_start", Integer.parseInt(xs) +
                        // Integer.parseInt(externalDiameter) + Integer.parseInt(intervalDistance));
                        paramsIdleInterval.put("x_point_start", xs.add(externalDiameter).add(intervalDistanceMini));

                        paramsIdleInterval.put("x_point_end", xe);
                        paramsIdleInterval.put("leftViewPackId", packId);

                        this.dao.update("LIDS0606.updateIdleInterval", paramsIdleInterval);// 连续空闲区间X轴开始结束更新
                    }
                } else if (StringUtils.isNotBlank(leftViewPackId) && StringUtils.isNotBlank(rightViewPackId)) {
                    // 左右都不为空，更新左右捆包信息，新增一条占用信息，再更新连续空闲区间的x轴开始结束，判断新增左右上层库位
                    // 先更新左右两个捆包的左右相邻捆包
                    paramsIdleInterval.put("rightViewPackId", "");
                    paramsIdleInterval.put("leftViewPackId", "");

                    paramsIdleInterval.put("locationPackId", leftViewPackId);
                    paramsIdleInterval.put("rightViewPackIdNow", packId);
                    paramsIdleInterval.put("leftViewPackIdNow", "");

                    this.dao.update("LIDS0606.updateAdjacentPack", paramsIdleInterval);// 先更新当前捆包的左侧捆包

                    paramsIdleInterval.put("locationPackId", rightViewPackId);
                    paramsIdleInterval.put("rightViewPackIdNow", "");

                    paramsIdleInterval.put("leftViewPackIdNow", packId);
                    this.dao.update("LIDS0606.updateAdjacentPack", paramsIdleInterval);// 先更新当前捆包的右侧捆包

                    if (remainingLength.compareTo(BigDecimal.ZERO) == 0) {
                        // 当前卷把连续空闲区完全占用，连续空闲区间更新为删除状态
                        this.dao.update("LIDS0606.deleteIdleInterval", paramsIdleInterval);// 连续空闲区间更新为删除状态
                        if (superLarge) {
                            locationMap.put("upLeftViewPackId", leftViewPackId);
                            locationMap.put("upRightViewPackId", packId);
                            boolean resultLeft = addUpperIdle(locationMap);

                            locationMap.put("upLeftViewPackId", packId);
                            locationMap.put("upRightViewPackId", rightViewPackId);
                            boolean resultRight = addUpperIdle(locationMap);
                        }

                    } else {
                        // 当前卷不把连续空闲区完全占用，连续空闲区间X轴开始结束更新
                        // paramsIdleInterval.put("x_point_start", Integer.parseInt(xs) +
                        // Integer.parseInt(externalDiameter) + Integer.parseInt(intervalDistance));
                        paramsIdleInterval.put("x_point_start", xs.add(externalDiameter).add(intervalDistanceMini));
                        paramsIdleInterval.put("x_point_end", xe);
                        paramsIdleInterval.put("leftViewPackId", packId);
                        paramsIdleInterval.put("rightViewPackId", rightViewPackId);
                        this.dao.update("LIDS0606.updateIdleInterval", paramsIdleInterval);// 连续空闲区间X轴开始结束更新
                        // 新增上层库位1个
                        if (superLarge) {
                            if ("0".equals(standFlag)) {
                                locationMap.put("upLeftViewPackId", leftViewPackId);
                                locationMap.put("upRightViewPackId", packId);
                                boolean resultLeft = addUpperIdle(locationMap);
                            }
                        }
                    }
                } else if (!StringUtils.isEmpty(leftViewPackId)) {
                    // 左侧不为空，更新左侧捆包信息，新增一条占用信息,再更新连续空闲区间的x轴开始结束，判断新增左上层库位
                    paramsIdleInterval.put("locationPackId", leftViewPackId);
                    paramsIdleInterval.put("rightViewPackIdNow", packId);
                    this.dao.update("LIDS0606.updateAdjacentPack", paramsIdleInterval);// 先更新当前捆包的左侧捆包
                    if (superLarge) {
                        // 判断当前卷上层是否可以放卷
                        // 符合条件，新增一个上层空闲库位信息
                        locationMap.put("upLeftViewPackId", leftViewPackId);
                        locationMap.put("upRightViewPackId", packId);

                        boolean result = addUpperIdle(locationMap);
                    }
                    if (remainingLength.compareTo(BigDecimal.ZERO) == 0) {
                        // 当前卷把连续空闲区完全占用，连续空闲区间更新为删除状态
                        this.dao.update("LIDS0606.deleteIdleInterval", paramsIdleInterval);// 连续空闲区间更新为删除状态
                    } else {
                        // 当前卷不把连续空闲区完全占用，连续空闲区间X轴开始结束更新
                        paramsIdleInterval.put("leftViewPackId", packId);
                        paramsIdleInterval.put("rightViewPackId", "");
                        paramsIdleInterval.put("x_point_start", xs.add(externalDiameter).add(intervalDistanceMini));
                        paramsIdleInterval.put("x_point_end", xe);
                        this.dao.update("LIDS0606.updateIdleInterval", paramsIdleInterval);// 连续空闲区间X轴开始结束更新
                    }

                } else if (!StringUtils.isEmpty(rightViewPackId)) {
                    // 右侧不为空，更新右侧捆包信息，新增一条占用信息,再更新连续空闲区间的x轴开始结束，判断新增右上层库位
                    paramsIdleInterval.put("locationPackId", rightViewPackId);
                    paramsIdleInterval.put("leftViewPackIdNow", packId);
                    this.dao.update("LIDS0606.updateAdjacentPack", paramsIdleInterval);// 先更新当前捆包的右侧捆包
                    // 新增当前占用区间的右侧上层库位信息
                    if (superLarge) {
                        // 符合条件，新增一个上层空闲库位信息
                        // 判断当前卷上层是否可以放卷
                        // 符合条件，新增一个上层空闲库位信息
                        if ("0".equals(standFlag)) {
                            locationMap.put("upLeftViewPackId", packId);
                            locationMap.put("upRightViewPackId", rightViewPackId);
                            boolean result = addUpperIdle(locationMap);
                        }

                    }

                    if (remainingLength.compareTo(BigDecimal.ZERO) == 0) {
                        // 当前卷把连续空闲区完全占用，连续空闲区间更新为删除状态
                        this.dao.update("LIDS0606.deleteIdleInterval", paramsIdleInterval);// 连续空闲区间更新为删除状态
                    } else {
                        // 当前卷不把连续空闲区完全占用，连续空闲区间X轴开始结束更新
                        paramsIdleInterval.put("x_point_start", xs);
                        paramsIdleInterval.put("x_point_end",
                                xe.subtract(externalDiameter).subtract(intervalDistanceMini));
                        paramsIdleInterval.put("leftViewPackId", packId);
                        this.dao.update("LIDS0606.updateIdleInterval", paramsIdleInterval);// 连续空闲区间X轴开始结束更新
                    }
                } else {
                    // 报错!
                    throw new RuntimeException("数据处理异常！");
                }
            } else {
                // 更新捆包信息到上层库位信息
                Map<String, Object> locationMapUp = new HashMap();
                locationMapUp.put("locViewPackId", packId);
                locationMapUp.put("outerDiameter", externalDiameter);
                locationMapUp.put("innerDiameter", innerDiameter);
                // 规格
                locationMapUp.put("spec", specsDesc);
                // 重量
                locationMapUp.put("putinWeight", netWeight);
                // 宽度
                locationMapUp.put("refWidth", width);
                // 占用状态
                locationMapUp.put("useStatus", "20");
                locationMapUp.put("productTypeId", prodTypeId);
                locationMapUp.put("customerId", d_userNum);
                locationMapUp.put("factoryProductId", factoryOrderNum);
                RecordUtils.setRevisor(locationMapUp);

                // 修改条件
                // 仓库
                locationMapUp.put("segNo", segNo);
                locationMapUp.put("wproviderId", warehouseCode);
                locationMapUp.put("factoryArea", factoryArea);
                locationMapUp.put("crossArea", crossArea);
                locationMapUp.put("factoryBuilding", factoryBuilding);
                // 左右捆包号
                locationMapUp.put("upLeftViewPackId", leftViewPackId);
                locationMapUp.put("upRightViewPackId", rightViewPackId);
                // 库位编号
                locationMapUp.put("locationId", locationId);
                this.dao.update("LIDS0606.updatePackInfo", locationMapUp);// 更新捆包信息到上层库位信息

            }
            return eiInfo;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /*
     * 判断当前库位的左右是否满足占用条件
     */
    public boolean checkAdjacent(HashMap params) {
        try {
            List<HashMap> listAdjacent = this.dao.query("LIDS0606.queryAdjacent", params);// 查询左右相邻被占用库位的捆包重量
            if (listAdjacent.size() > 0) {
                for (HashMap mapAdjacent : listAdjacent) {
                    // 判断左右两个卷的重量，外径,重量是否符合偏差值
                    BigDecimal weightZy = new BigDecimal(String.valueOf(mapAdjacent.get("putinWeight")));
                    BigDecimal externalDiameterDeviation = new BigDecimal(
                            String.valueOf(mapAdjacent.get("outerDiameter")));
                    // 比较weightDeviation跟netWeight的大小 判断重量差异
                    // 如果netWeight小于weightDeviation，则不符合，继续查询下一个库位
                    // double weightCy = Double.parseDouble(weightZy) - Double.parseDouble((String)
                    // params.get("netWeight"));
                    BigDecimal weightCy = weightZy.subtract(new BigDecimal(String.valueOf(params.get("netWeight"))));

                    if (weightCy.compareTo(BigDecimal.ZERO) == -1) {
                        weightCy = weightCy;
                    }
                    // 判断weightDeviation 是否 > = weightCy
                    if (new BigDecimal(String.valueOf(params.get("weightDeviation"))).compareTo(weightCy) == -1) {
                        return false;
                    }
                    // 计算外径差异
                    // double externalDiameterCy = Double.parseDouble(externalDiameterDeviation) -
                    // (double) params.get("externalDiameter");
                    BigDecimal externalDiameterCy = externalDiameterDeviation
                            .subtract(new BigDecimal(String.valueOf(params.get("externalDiameter"))));
                    if (externalDiameterCy.compareTo(BigDecimal.ZERO) < 0) {
                        externalDiameterCy = externalDiameterCy;
                    }
                    // 判断outerDiameterDeviation 是否 > = externalDiameterCy
                    if (new BigDecimal(String.valueOf(params.get("outerDiameterDeviation")))
                            .compareTo(externalDiameterCy) == -1) {
                        // 外径差异较大，不符合，继续查询下一个库位
                        return false;
                    }
                    /*
                     * if (Double.parseDouble(String.valueOf(params.get("outerDiameterDeviation")))
                     * < externalDiameterCy) {
                     * //外径差异较大，不符合，继续查询下一个库位
                     * return false;
                     * }
                     */
                }
            }
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /*
     * 新增上层空闲库位
     */
    public boolean addUpperIdle(Map params) {
        try {
            Map<String, Object> mapAll = new HashMap<>();
            mapAll.put("segNo", params.get("segNo"));
            mapAll.put("unitCode", params.get("segNo"));
            mapAll.put("wproviderId", params.get("wproviderId"));
            mapAll.put("locationId", params.get("locationId"));
            mapAll.put("locViewPackId", "");
            mapAll.put("useStatus", "10");
            mapAll.put("locationType", "20");// 库位类型
            mapAll.put("specUpper", params.get("specUpper"));
            mapAll.put("specLower", params.get("specLower"));
            mapAll.put("upDownFlag", "2");
            mapAll.put("standFlag", "0");
            mapAll.put("jgLockFlag", "0");
            mapAll.put("locAtionName", params.get("locationName"));// 库位名称
            mapAll.put("factoryArea", params.get("factoryArea"));
            mapAll.put("crossArea", params.get("crossArea"));
            mapAll.put("upLeftViewPackId", params.get("upLeftViewPackId"));
            mapAll.put("upRightViewPackId", params.get("upRightViewPackId"));
            mapAll.put("pointLowerLength", params.get("pointLowerLength"));
            mapAll.put("pointUpperLength", params.get("pointUpperLength"));
            mapAll.put("factoryBuilding", params.get("factoryBuilding"));
            // 状态为新增
            // itemMap.put("status", "10");
            // 设置创建人信息
            RecordUtils.setCreator(mapAll);
            this.dao.insert("LIDS0606.insertUpperIdle", mapAll);// 新增一条上层空闲库位信息
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 库位释放逻辑
     *
     * @param inInfo
     * @return
     */
    public EiInfo warehouseRelease(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map messageBody = (Map) inInfo.get("messageBody");
            String segNo = (String) messageBody.get("segNo");
            String warehouseCode = (String) messageBody.get("warehouseCode");
            String factoryArea = (String) messageBody.get("factoryArea");
            String crossArea = (String) messageBody.get("crossArea");
            String factoryBuilding = (String) messageBody.get("factoryBuilding");
            String locationId = (String) messageBody.get("locationId");
            String packId = (String) messageBody.get("packId");
            String updateFlag = "";// 上下层标记
            // 先判断当前捆包是否占用有库位信息
            Map<String, Object> params = new HashMap<>();
            params.put("packId", packId);
            params.put("warehouseCode", warehouseCode);
            params.put("segNo", segNo);

            List<HashMap> locationMapList = this.dao.query("LIDS0606.queryPackInfo", params);
            if (locationMapList.size() == 0) {
                throw new RuntimeException("当前捆包未占用任何库位信息！");
            }
            params.put("locationId", locationMapList.get(0).get("locationId"));
            params.put("crossArea", locationMapList.get(0).get("crossArea"));
            params.put("factoryArea", locationMapList.get(0).get("factoryArea"));
            params.put("factoryBuilding", locationMapList.get(0).get("factoryBuilding"));
            // 判断当前捆包是上层下层
            if (locationMapList.size() > 0) {
                updateFlag = (String) locationMapList.get(0).get("upDownFlag");
            }
            // 上层：直接释放上层库位信息
            if (updateFlag.equals("2")) {
                // 释放上层库位信息
                // 删除当前占用信息，新增上层空闲位置信息
                this.dao.update("LIDS0606.deleteLocation", params);// 删除库位信息
                // 新增一条上层空闲库位信息
                HashMap locationMap = new HashMap<>();
                locationMap = locationMapList.get(0);
                locationMap.put("locViewPackId", "");
                locationMap.put("outerDiameter", 0);
                locationMap.put("innerDiameter", 0);
                // 规格
                locationMap.put("spec", "");
                // 重量
                locationMap.put("putinWeight", 0);
                // 宽度
                locationMap.put("refWidth", 0);
                // 占用状态
                locationMap.put("useStatus", "10");
                locationMap.put("productTypeId", "");
                locationMap.put("customerId", "");
                locationMap.put("factoryProductId", "");
                RecordUtils.setCreator(locationMap);
                this.dao.insert("LIDS0606.insertUpperIdle", locationMap);// 新增一条上层空闲库位信息
            } else {
                // 下层：判断上层是否有占用信息，有禁止释放，无释放下层库位信息
                List<Map> locationMapListUp = this.dao.query("LIDS0606.queryPackInfoUp", params);
                if (locationMapListUp.size() > 0) {
                    throw new RuntimeException(
                            "上层有占用信息，捆包号为：" + locationMapListUp.get(0).get("locViewPackId") + "，禁止释放！");
                } else {
                    // 释放下层库位信息
                    // locationMapList 当前释放捆包信息
                    // 查询当前释放的库位信息 左侧，右侧 是否存在连续空闲区间，有则更新，无则新增 先查询左侧
                    List<Map> idleIntervalListLeft = this.dao.query("LIDS0606.queryIdleIntervalLeft", params);
                    // 查询右侧空闲区间
                    List<Map> idleIntervalListRight = this.dao.query("LIDS0606.queryIdleIntervalRight", params);
                    if (idleIntervalListLeft.size() > 0 && idleIntervalListRight.size() > 0) {
                        // 左右都存在空闲区间，更新左侧空闲区间,右侧删除
                        // 左侧开始 = idleIntervalListLeft.get(0).get("x_point_start")
                        // 左侧结束 = idleIntervalListRight.get(0).get("x_point_end")
                        // 更新左侧空闲区间
                        HashMap idleIntervalLeft = new HashMap();
                        idleIntervalLeft.putAll(idleIntervalListLeft.get(0));
                        idleIntervalLeft.put("xPointEnd", idleIntervalListRight.get(0).get("x_point_end"));
                        // 更新当前空闲区间的右侧相邻捆包号为 右侧空闲区间的捆包号
                        idleIntervalLeft.put("rightViewPackId", idleIntervalListRight.get(0).get("rightViewPackId"));
                        this.dao.update("LIDS0606.updateIdleIntervalLeft", idleIntervalLeft);
                        // 删除右侧空闲区间
                        HashMap idleIntervalRight = new HashMap();
                        idleIntervalRight.putAll(idleIntervalListRight.get(0));
                        this.dao.update("LIDS0606.deleteIdleIntervalRight", idleIntervalRight);
                        // 删除当前占用区间
                        this.dao.update("LIDS0606.deleteLocation", params);// 删除库位信息
                        this.dao.update("LIDS0606.deletePackRight", params);
                    } else if (idleIntervalListLeft.size() > 0) {
                        // 左侧存在空闲区间，更新左侧空闲区间
                        // 左侧开始 = idleIntervalListLeft.get(0).get("x_point_start")
                        // 左侧结束 = locationMapList.get(0).get("x_point_end")
                        // 更新左侧空闲区间
                        HashMap idleIntervalLeft = new HashMap();
                        idleIntervalLeft.putAll(idleIntervalListLeft.get(0));
                        idleIntervalLeft.put("xPointEnd", locationMapList.get(0).get("x_point_end"));
                        this.dao.update("LIDS0606.updateIdleIntervalLeft", idleIntervalLeft);
                        // 删除当前占用区间
                        this.dao.update("LIDS0606.deleteLocation", params);// 删除库位信息
                        // 删除上层空闲位置
                        this.dao.update("LIDS0606.deleteLocationUp", params);// 删除库位信息

                    } else if (idleIntervalListRight.size() > 0) {
                        // 右侧存在空闲区间，更新右侧空闲区间
                        // 右侧开始 = locationMapList.get(0).get("x_point_start")
                        // 右侧结束 = idleIntervalListRight.get(0).get("x_point_end")
                        // 更新右侧空闲区间
                        HashMap idleIntervalLeft = new HashMap();
                        idleIntervalLeft.putAll(idleIntervalListRight.get(0));
                        idleIntervalLeft.put("xPointStart", locationMapList.get(0).get("x_point_start"));
                        this.dao.update("LIDS0606.updateIdleIntervalRight", idleIntervalLeft);
                        // 删除当前占用区间
                        this.dao.update("LIDS0606.deleteLocation", params);
                        // 删除上层空闲位置
                        this.dao.update("LIDS0606.deleteLocationUp", params);
                        this.dao.update("LIDS0606.deletePackLeft", params);
                    } else {
                        // 左右都不存在空闲区间，新增一条空闲区间
                        // 左侧开始 = locationMapList.get(0).get("x_point_start")
                        // 左侧结束 = locationMapList.get(0).get("x_point_end")
                        // 排序查询所有空闲区间
                        List<Map> idleIntervalList = this.dao.query("LIDS0606.queryIdleInterval", params);
                        // 根据idleIntervalList 的idle_interval_id排序 获取idle_interval_id中缺少的数字，没有缺少取最大+1
                        int[] numbers = new int[idleIntervalList.size()];
                        for (int i = 0; i < idleIntervalList.size(); i++) {
                            BigDecimal number = new BigDecimal(
                                    idleIntervalList.get(i).get("idle_interval_id").toString());
                            int intValue = number.intValue();
                            numbers[i] = intValue;
                        }
                        int missingNumber = findMissingNumber(numbers);
                        System.out.println(missingNumber);
                        // missingNumber新的空闲区间编号
                        // 新增空闲区间
                        HashMap idleInterval = new HashMap();
                        idleInterval.putAll(locationMapList.get(0));
                        idleInterval.put("locViewPackId", "");
                        idleInterval.put("idleIntervalId", missingNumber);

                        idleInterval.put("outerDiameter", 0);
                        idleInterval.put("innerDiameter", 0);
                        // 规格
                        idleInterval.put("spec", "");
                        // 重量
                        idleInterval.put("putinWeight", 0);
                        // 宽度
                        idleInterval.put("refWidth", 0);
                        // 占用状态
                        idleInterval.put("useStatus", "10");
                        idleInterval.put("productTypeId", "");
                        idleInterval.put("customerId", "");
                        idleInterval.put("factoryProductId", "");
                        RecordUtils.setCreator(idleInterval);
                        this.dao.insert("LIDS0606.insertUpperIdle", idleInterval);
                        // 删除上层空闲位置
                        this.dao.update("LIDS0606.deleteLocationUp", params);// 删除库位信息
                        // 删除当前占用区间
                        this.dao.update("LIDS0606.deleteLocation", params);
                        // 清空当前捆包左右捆包的左右捆包信息
                        this.dao.update("LIDS0606.deletePackLeft", params);
                        this.dao.update("LIDS0606.deletePackRight", params);
                    }
                }
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    public static int findMissingNumber(int[] numbers) {
        Arrays.sort(numbers);
        for (int i = 0; i < numbers.length; i++) {
            // 判断当前索引+1与数组中的数字是否相等
            if (numbers[i] != i + 1) {
                return i + 1; // 返回缺少的数字
            }
        }
        // 如果没有缺少的数字，可以返回下一个数字
        return numbers.length + 1;
    }

    /**
     * 捆包指定占用逻辑
     *
     * @param inInfo
     * @return
     */
    public EiInfo occupyCalibration(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map messageBody = (Map) inInfo.get("messageBody");
            String segNo = (String) messageBody.get("segNo");
            String warehouseCode = (String) messageBody.get("warehouseCode");
            String factoryArea = (String) messageBody.get("factoryArea");
            String crossArea = (String) messageBody.get("crossArea");
            String factoryBuilding = (String) messageBody.get("factoryBuilding");
            String locationId = (String) messageBody.get("locationId");
            String packId = (String) messageBody.get("packId");
            String xPoint = (String) messageBody.get("xPoint");
            String intervalDistanceMax = switchValue(segNo, "INTERVAL_DISTANCE_MAX", warehouseCode);
            String intervalDistanceMini = switchValue(segNo, "INTERVAL_DISTANCE_MINI", warehouseCode);

            String originalPackId = MapUtils.getString(messageBody, "originalPackId");
            String netWeight = MapUtils.getString(messageBody, "netWeight");

            String updateFlag = "";// 上下层标记
            // 先判断当前捆包是否占用有库位信息
            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(originalPackId)) {
                params.put("packId", originalPackId);
            } else {
                params.put("packId", packId);
            }
            params.put("warehouseCode", warehouseCode);
            params.put("segNo", segNo);
            params.put("factoryArea", factoryArea);
            params.put("crossArea", crossArea);
            params.put("factoryBuilding", factoryBuilding);

            Double innerDiameter;
            Double weight;
            Double prodDensity;
            String specsDesc = "";
            // 中间坐标
            // 获取捆包内径 查询tlirl0503表，未查询到数据默认"0" 目前进行提示报错
            List<Map> tlirl0503List = this.dao.query("LIDS0606.queryTlirl0503", params);
            params.put("packId", packId);
            if (tlirl0503List.size() > 0) {
                // 获取捆包内径
                innerDiameter = tlirl0503List.get(0).get("innerDiameter") == null ? 0
                        : Double.parseDouble(tlirl0503List.get(0).get("innerDiameter").toString());
                if (StringUtils.isNotBlank(originalPackId)) {
                    weight = Double.parseDouble(netWeight);
                } else {
                    // 获取捆包重量
                    weight = tlirl0503List.get(0).get("netWeight") == null ? 0
                            : Double.parseDouble(tlirl0503List.get(0).get("netWeight").toString());
                }
                // 获取捆包宽度
                specsDesc = (String) tlirl0503List.get(0).get("specsDesc");
                // 获取产品密度
                prodDensity = tlirl0503List.get(0).get("prodDensity") == null ? 0
                        : Double.parseDouble(tlirl0503List.get(0).get("prodDensity").toString());
            } else {
                // 未查询到数据，默认0
                throw new RuntimeException("未查询到捆包内径，请联系管理员！");
            }
            // 宽 英文
            // 判断expression 是否存在两个星号
            if (specsDesc.indexOf('*') == -1 || specsDesc.indexOf('*', specsDesc.indexOf('*') + 1) == -1) {
                System.out.println("表达式中没有两个星号");
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("宽度截取失败，请检查规格是否正确！");
                return outInfo;
            }
            // 截取 expression 第一个*跟第二个*之间的字符串
            // System.out.println(specsDesc.substring(specsDesc.indexOf('*') + 1,
            // specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
            double width = Double.parseDouble(specsDesc.substring(specsDesc.indexOf('*') + 1,
                    specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
            // 计算外径
            double externalDiameterNumber = calculateOuterDiameter(innerDiameter, weight, width, prodDensity * 1000);
            // 判断卷外径是否为空
            if (externalDiameterNumber == 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("卷外径计算失败！");
                return outInfo;
            }
            // externalDiameter 向上取整
            int externalDiameter = (int) Math.ceil(externalDiameterNumber);
            // isLargeVolume 是否为超大卷 false不是超大卷，true超大卷
            Boolean isLargeVolume = false;
            // 判断是否为超大卷，根据重量，外径 进行判断 查询配置表数值；
            // 超大卷阈值外径
            String weightRestrict = switchValue(segNo, "SUPER_LARGE_VOLUME", warehouseCode);
            // 超大卷阈值重量
            String externalDiameterRestrict = switchValue(segNo, "OVERWEIGHT_ROLLS", warehouseCode);
            // 判断weightRestrict，externalDiameterRestrict是否为空，如果为空，则不进行超大卷处理，直接推荐库位
            if (!StringUtils.isEmpty(weightRestrict)) {
                // 比较weightRestrict，跟netWeight的大小，如果netWeight大于weightRestrict，正常推荐库位，否则进行超大卷处理
                if (weight >= Double.parseDouble(weightRestrict)) {
                    isLargeVolume = true;
                }
            }
            if (!StringUtils.isEmpty(externalDiameterRestrict)) {
                if (externalDiameter >= Integer.parseInt(externalDiameterRestrict)) {
                    isLargeVolume = true;
                }
            }
            // 查询当前指定位置是否被占用
            /*
             * List<HashMap> locationMapList1 = this.dao.query("LIDS0606.queryLocation",
             * params);
             * if (locationMapList1.size() > 0) {
             * throw new RuntimeException("当前位置已被占用！");
             * }
             */
            // 查询当前捆包是否存在占用信息
            /*
             * List<HashMap> locationMapList = this.dao.query("LIDS0606.queryPackInfo",
             * params);
             */
            // 查询当前位置下层是否为占用，为占用，判段上层是否为占用，上层位置定位（当前坐标占用距离X轴开始近判断左侧，距离X轴结束近，判断右侧，未有上层库位，提示报错）
            params.put("locationId", locationId);
            params.put("xPoint", xPoint);// 中间坐标
            params.put("intervalDistanceMax", intervalDistanceMax);
            params.put("intervalDistanceMini", intervalDistanceMini);

            // 查询当前位置下层是否存在占用，查询空闲，预占，占用数据
            List<HashMap> locationMapListOccupy = this.dao.query("LIDS0606.locationMapListOccupy", params);

            if (locationMapListOccupy.size() > 0) {
                // locationMapListOccupy.get(0).get("x_point_start")转Double 接收
                Double xPointStart = Double.parseDouble(locationMapListOccupy.get(0).get("x_pointStart").toString());
                Double xPointEnd = Double.parseDouble(locationMapListOccupy.get(0).get("x_pointEnd").toString());

                // 当前位置不为空闲，占用上层库位
                if (locationMapListOccupy.get(0).get("useStatus").equals("30")) {
                    // 判断是否为超大卷，超大卷不存在上层库位直接报错
                    if (isLargeVolume) {
                        throw new RuntimeException("当前下层库位已被占用，超大卷上层库位不可用！");
                    }
                    // 判断当前位置是否在下层占用区间内
                    if (Double.parseDouble(xPoint) - xPointStart > xPointEnd - Double.parseDouble(xPoint)) {
                        // 查询当前库位的右侧上层空闲位置
                        params.put("rightViewPackId", locationMapListOccupy.get(0).get("packId").toString());
                    } else {
                        // 查询当前库位的左侧上层空闲位置
                        params.put("leftViewPackId", locationMapListOccupy.get(0).get("packId").toString());
                    }
                    // 查询空闲上层库位
                    List<HashMap> locationMapListUp = this.dao.query("LIDS0606.locationMapListUp", params);
                    if (locationMapListUp.size() > 0) {
                        // 更新库位占用信息
                        Map<String, Object> locationMap = new HashMap<>();
                        // locationMap = tlirl0503List.get(0);
                        locationMap.put("locViewPackId", (String) tlirl0503List.get(0).get("packId"));
                        locationMap.put("factoryProductId", (String) tlirl0503List.get(0).get("factoryOrderNum"));
                        locationMap.put("spec", (String) tlirl0503List.get(0).get("specsDesc"));
                        locationMap.put("innerDiameter", (String) tlirl0503List.get(0).get("innerDiameter"));// 内径
                        locationMap.put("outerDiameter", externalDiameter);// 外径
                        locationMap.put("putinWeight", weight);// 重量
                        locationMap.put("refWidth", width);
                        locationMap.put("customerId", (String) tlirl0503List.get(0).get("customerId"));
                        locationMap.put("useStatus", "20");
                        locationMap.put("wproviderId", (String) locationMapListUp.get(0).get("wproviderId"));
                        locationMap.put("factoryArea", (String) locationMapListUp.get(0).get("factoryArea"));
                        locationMap.put("crossArea", (String) locationMapListUp.get(0).get("crossArea"));
                        locationMap.put("factoryBuilding", (String) locationMapListUp.get(0).get("factoryBuilding"));
                        locationMap.put("locationId", (String) locationMapListUp.get(0).get("locationId"));
                        locationMap.put("upLeftViewPackId", (String) locationMapListUp.get(0).get("upLeftViewPackId"));
                        locationMap.put("upRightViewPackId",
                                (String) locationMapListUp.get(0).get("upRightViewPackId"));
                        RecordUtils.setRevisor(locationMap);
                        this.dao.update("LIDS0606.updateupperLayer", locationMap);// 更新库位信息
                    } else {
                        throw new RuntimeException("当前位置无空闲库位！");
                    }
                } /// 当前位置为空闲，占用当前库位
                else if (locationMapListOccupy.get(0).get("useStatus").equals("10")
                        || locationMapListOccupy.get(0).get("useStatus").equals("20")) {
                    if (locationMapListOccupy.get(0).get("useStatus").equals("20")) {
                        // 调用捆包释放
                        EiInfo releaseInfo = new EiInfo();
                        Map<String, Object> releaseMessageBody = new HashMap<>();
                        releaseMessageBody.put("packId", locationMapListOccupy.get(0).get("packId"));
                        releaseMessageBody.put("warehouseCode", locationMapListOccupy.get(0).get("wproviderId"));
                        releaseMessageBody.put("factoryArea", locationMapListOccupy.get(0).get("factoryArea"));
                        releaseMessageBody.put("crossArea", locationMapListOccupy.get(0).get("crossArea"));
                        releaseMessageBody.put("factoryBuilding", locationMapListOccupy.get(0).get("factoryBuilding"));
                        releaseInfo.set("messageBody", releaseMessageBody);
                        warehouseRelease(releaseInfo);
                    }
                    params.put("idleIntervalId", locationMapListOccupy.get(0).get("idleIntervalId"));// 状态更新为预占用
                    // 先判断当前下层空闲位置是否能放下当前捆包，如果不能放下，提示报错
                    // 当前中间坐标+-externalDiameterNumber(捆包外径)/2
                    // 与下层占用区间的X轴开始，X轴结束比较，如果中间坐标在下层占用区间内，提示报错 +间隔
                    double xPointEndDouble = Double.parseDouble(xPoint) + externalDiameter / 2;
                    double xPointStartDouble = Double.parseDouble(xPoint) - externalDiameter / 2;
                    // 取当前位置的右侧占用，以及左侧占用捆包号
                    String rightViewPackId = (String) locationMapListOccupy.get(0).get("rightViewPackId");
                    String leftViewPackId = (String) locationMapListOccupy.get(0).get("leftViewPackId");
                    // 判断捆包号是否为空
                    if (StringUtils.isEmpty(rightViewPackId) && StringUtils.isEmpty(leftViewPackId)) {
                        if (xPointEndDouble > xPointEnd || xPointStartDouble < xPointStart) {
                            throw new RuntimeException("当前位置不在下层占用区间内！");
                        }
                    } else {
                        // 根据xPointEndDouble，xPointStartDouble 查询当前区间是否存在30占用状态的库位，存在，提示报错
                        // 不存在 释放这个区间所有预占用的捆包信息
                        params.put("xPointEndDouble", xPointEndDouble);
                        params.put("xPointStartDouble", xPointStartDouble);
                        List<HashMap> locationMapListOccupy1 = this.dao.query("LIDS0606.locationMapListOccupy1",
                                params);
                        if (locationMapListOccupy1.size() > 0) {
                            for (HashMap locationMap : locationMapListOccupy1) {
                                if (locationMap.get("useStatus").equals("30")) {
                                    throw new RuntimeException("当前位置不可用！");
                                } else if (locationMap.get("useStatus").equals("20")) {
                                    // 调用捆包释放
                                    EiInfo releaseInfo = new EiInfo();
                                    Map<String, Object> releaseMessageBody = new HashMap<>();
                                    releaseMessageBody.put("packId", locationMap.get("packId"));
                                    releaseMessageBody.put("warehouseCode", locationMap.get("wproviderId"));
                                    releaseMessageBody.put("factoryArea", locationMap.get("factoryArea"));
                                    releaseMessageBody.put("crossArea", locationMap.get("crossArea"));
                                    releaseMessageBody.put("factoryBuilding", locationMap.get("factoryBuilding"));
                                    releaseInfo.set("messageBody", releaseMessageBody);
                                    warehouseRelease(releaseInfo);
                                }
                            }
                        }
                    }
                    // startDifference 大于0 左侧还有剩余区间，endDifference 大于0 右侧还有剩余区间 都=0刚好能放下
                    Double startDifference = Math.ceil(xPointStartDouble) - xPointStart;
                    Double endDifference = 0.00;
                    if (StringUtils.isNotBlank(rightViewPackId)) {
                        xPointEndDouble = Math.ceil(xPointEndDouble + Double.parseDouble(intervalDistanceMini));
                        endDifference = xPointEnd - Math.ceil(xPointEndDouble);
                    } else {
                        endDifference = xPointEnd - Math.ceil(xPointEndDouble);
                    }
                    // 判断左侧是否还存在空闲区间，以及右侧是否还存在空闲区间
                    Map<String, Object> params1 = new HashMap<>();
                    // params1.put("packId", packId);
                    params1.put("x_pointStart", xPointStartDouble);
                    params1.put("x_pointEnd", xPointEndDouble);
                    params1.put("locationType", "20");
                    params1.put("idleIntervalId", locationMapListOccupy.get(0).get("idleIntervalId"));
                    params1.put("upDownFlag", locationMapListOccupy.get(0).get("upDownFlag"));
                    params1.put("locViewPackId", tlirl0503List.get(0).get("packId"));
                    params1.put("leftViewPackId", locationMapListOccupy.get(0).get("leftViewPackId"));
                    params1.put("rightViewPcakId", locationMapListOccupy.get(0).get("rightViewPackId"));
                    params1.put("rightViewPackId", locationMapListOccupy.get(0).get("rightViewPackId"));
                    params1.put("factoryProductId", tlirl0503List.get(0).get("factoryOrderNum"));
                    params1.put("spec", tlirl0503List.get(0).get("specsDesc"));
                    params1.put("innerDiameter", tlirl0503List.get(0).get("innerDiameter"));// 内径
                    params1.put("outerDiameter", externalDiameter);// 外径
                    params1.put("putinWeight", weight);// 重量
                    params1.put("refWidth", width);
                    params1.put("customerId", tlirl0503List.get(0).get("customerId"));
                    params1.put("specUpper", locationMapListOccupy.get(0).get("specUpper"));
                    params1.put("specLower", locationMapListOccupy.get(0).get("specLower"));
                    params1.put("locationName", locationMapListOccupy.get(0).get("locationName"));
                    params1.put("pointLowerLength", locationMapListOccupy.get(0).get("pointLowerLength"));
                    params1.put("pointUpperLength", locationMapListOccupy.get(0).get("pointUpperLength"));
                    params1.put("wproviderId", warehouseCode);
                    params1.put("warehouseCode", warehouseCode);
                    params1.put("factoryArea", factoryArea);
                    params1.put("crossArea", crossArea);
                    params1.put("factoryBuilding", factoryBuilding);
                    params1.put("locationId", locationId);
                    params1.put("segNo", locationMapListOccupy.get(0).get("segNo"));
                    params1.put("unitCode", locationMapListOccupy.get(0).get("segNo"));
                    params1.put("standFlag", locationMapListOccupy.get(0).get("standFlag"));
                    params1.put("factoryBuildingName", locationMapListOccupy.get(0).get("factoryBuildingName"));
                    if (startDifference == 0 && endDifference == 0) {
                        // 当前位置刚好能放下当前捆包，更新当前库位信息为占用，捆包信息更新上去
                        // 删除空闲区间
                        this.dao.delete("LIDS0606.deleteIdleInterval", params1);// 删除空闲区间
                        // 新增一条占用信息
                        params1.put("useStatus", "30");// 状态更新为占用
                        params1.put("idleIntervalId", 0);
                        RecordUtils.setCreator(params1);
                        this.dao.insert("LIDS0605.insert", params1);// 新增一条占用信息

                        // 更新左侧库位信息
                        if (StringUtils.isNotBlank(leftViewPackId.trim())) {
                            params1.put("rightViewPackIdNow", packId);
                            params1.put("locationPackId", leftViewPackId);
                            params1.put("leftViewPackId", "");
                            this.dao.update("LIDS0606.updateAdjacentPack", params1);
                            params1.put("leftViewPackId", locationMapListOccupy.get(0).get("leftViewPackId"));
                        }
                        // 更新右侧库位信息
                        if (StringUtils.isNotBlank(rightViewPackId.trim())) {
                            params1.put("leftViewPackIdNow", packId);
                            params1.put("locationPackId", rightViewPackId);
                            params1.put("rightViewPackId", "");
                            this.dao.update("LIDS0606.updateAdjacentPack", params1);
                            params1.put("rightViewPackId", locationMapListOccupy.get(0).get("rightViewPackId"));

                        }
                        // 判断是否为超大卷,超大卷判断逻辑
                        if (!isLargeVolume) {
                            // 判断当前空闲区间的左右两侧是否存在捆包，存在 新增上层库位
                            if (StringUtils.isNotBlank(leftViewPackId.trim())) {
                                // 新增左侧上层库位
                                params1.put("upRightViewPackId", packId);
                                params1.put("upLeftViewPackId", leftViewPackId);
                                addUpperIdle(params1);
                            }
                            if (StringUtils.isNotBlank(rightViewPackId.trim())) {
                                params1.put("upLeftViewPackId", packId);
                                params1.put("upRightViewPackId", rightViewPackId);
                                // 新增右侧上层库位
                                addUpperIdle(params1);
                            }
                        }
                    }
                    // 两边都有剩余空间
                    if (startDifference > 0 && endDifference > 0) {
                        // 新增一条占用信息
                        params1.put("useStatus", "30");// 状态更新为占用
                        params1.put("leftViewPackId", "");
                        params1.put("rightViewPcakId", "");
                        params1.put("idleIntervalId", 0);
                        RecordUtils.setCreator(params1);
                        this.dao.insert("LIDS0605.insert", params1);// 新增一条占用信息
                        // 修改左侧空闲区间的X轴结束坐标（当前捆包的X轴开始坐标），以及右侧捆包号
                        params1.put("rightViewPackId", packId);
                        params1.put("idleIntervalId", locationMapListOccupy.get(0).get("idleIntervalId"));
                        params1.put("x_pointEnd", xPointStartDouble);
                        this.dao.update("LIDS0606.updateIdleIntervalLeft", params1);
                        // 新增右侧空闲区间，右侧空闲区间 左侧捆包号为当前捆包号，右侧捆包号为rightViewPackId
                        params.put("leftViewPackId", packId);
                        params.put("rightViewPackId", locationMapListOccupy.get(0).get("rightViewPackId"));
                        params.put("xPointStart", xPointEndDouble);
                        params.put("xPointEnd", xPointEnd);
                        addIdleSpace(params);
                    }
                    // 左侧有剩余空间
                    if (startDifference > 0 && endDifference == 0) {
                        // 新增占用信息
                        params1.put("useStatus", "30");// 状态更新为占用
                        params1.put("leftViewPackId", "");
                        params1.put("rightViewPcakId", "");
                        params1.put("idleIntervalId", 0);
                        RecordUtils.setCreator(params1);
                        this.dao.insert("LIDS0605.insert", params1);// 新增一条占用信息
                        // 修改空闲区间的X轴结束坐标为当前捆包的X轴开始坐标，以及右侧捆包号
                        params1.put("rightViewPackIdNow", packId);
                        params1.put("idleIntervalId", locationMapListOccupy.get(0).get("idleIntervalId"));
                        params1.put("xPointEnd", xPointStartDouble);
                        this.dao.update("LIDS0606.updateIdleIntervalLeft", params1);
                        // 判断右侧是否存在捆包信息
                        if (StringUtils.isNotBlank(rightViewPackId)) {
                            // 更新右侧捆包信息，左侧捆包号为当前捆包号
                            params1.put("leftViewPackIdNow", packId);
                            params1.put("locationPackId", rightViewPackId);
                            params1.put("rightViewPackId", "");
                            this.dao.update("LIDS0606.updateAdjacentPack", params1);
                            params1.put("rightViewPackId", locationMapListOccupy.get(0).get("rightViewPackId"));
                            // 判断是否为超大卷
                            if (!isLargeVolume) {
                                params1.put("upLeftViewPackId", packId);
                                params1.put("upRightViewPackId", rightViewPackId);
                                // 新增右侧上层空闲库位
                                addUpperIdle(params1);
                            }
                        }
                    }
                    // 右侧有剩余空间
                    if (startDifference == 0 && endDifference > 0) {
                        // 新增占用信息
                        params1.put("useStatus", "30");// 状态更新为占用
                        params1.put("leftViewPackId", "");
                        params1.put("rightViewPcakId", "");
                        params1.put("idleIntervalId", 0);
                        RecordUtils.setCreator(params1);
                        this.dao.insert("LIDS0605.insert", params1);// 新增一条占用信息
                        params1.put("leftViewPackIdNow", packId);
                        params1.put("idleIntervalId", locationMapListOccupy.get(0).get("idleIntervalId"));
                        params1.put("xPointStart", xPointEndDouble);
                        // 修改空闲区间的X轴开始坐标为当前捆包的X轴结束坐标，以及左侧捆包号
                        this.dao.update("LIDS0606.updateIdleIntervalRight", params1);
                        // 判断左侧是否存在捆包信息
                        if (StringUtils.isNotBlank(leftViewPackId)) {
                            // 更新左侧捆包信息，右侧捆包号为当前捆包号
                            params1.put("rightViewPackIdNow", packId);
                            params1.put("locationPackId", leftViewPackId);
                            params1.put("leftViewPackId", "");
                            this.dao.update("LIDS0606.updateAdjacentPack", params1);
                            params1.put("leftViewPackId", locationMapListOccupy.get(0).get("leftViewPackId"));
                            // 判断是否为超大卷
                            if (!isLargeVolume) {
                                // 判断是否符合 规定差值
                                // 新增左侧上层空闲库位
                                params1.put("upRightViewPackId", packId);
                                params1.put("upLeftViewPackId", leftViewPackId);
                                addUpperIdle(params1);
                            }
                        }
                    }
                } else {
                    throw new RuntimeException("当前坐标位置不可用！");
                }
            } else {
                throw new RuntimeException("未查询到坐标位置！");
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 新增上层空闲库位
     */
    public boolean addIdleSpace(Map params) {
        try {
            // 查询当前空闲区间
            List<HashMap> locationMapList = this.dao.query("LIDS0606.queryPackInfoAll", params);
            params.put("idleIntervalId", "");
            List<Map> idleIntervalList = this.dao.query("LIDS0606.queryIdleInterval", params);
            // 根据idleIntervalList 的idle_interval_id排序 获取idle_interval_id中缺少的数字，没有缺少取最大+1
            int[] numbers = new int[idleIntervalList.size()];
            for (int i = 0; i < idleIntervalList.size(); i++) {
                BigDecimal number = new BigDecimal(idleIntervalList.get(i).get("idle_interval_id").toString());
                int intValue = number.intValue();
                numbers[i] = intValue;
            }
            int missingNumber = findMissingNumber(numbers);
            System.out.println(missingNumber);
            // missingNumber新的空闲区间编号
            // 新增空闲区间
            HashMap idleInterval = new HashMap();
            idleInterval.putAll(locationMapList.get(0));
            idleInterval.put("leftViewPackId", params.get("leftViewPackId"));
            idleInterval.put("rightViewPackId", params.get("rightViewPackId"));
            idleInterval.put("x_pointStart", params.get("xPointStart"));
            idleInterval.put("x_pointEnd", params.get("xPointEnd"));

            idleInterval.put("locViewPackId", "");
            idleInterval.put("idleIntervalId", missingNumber);
            idleInterval.put("outerDiameter", 0);
            idleInterval.put("innerDiameter", 0);
            // 规格
            idleInterval.put("spec", "");
            // 重量
            idleInterval.put("putinWeight", 0);
            // 宽度
            idleInterval.put("refWidth", 0);
            // 占用状态
            idleInterval.put("useStatus", "10");
            idleInterval.put("productTypeId", "");
            idleInterval.put("customerId", "");
            idleInterval.put("factoryProductId", "");
            RecordUtils.setCreator(idleInterval);
            this.dao.insert("LIDS0606.insertUpperIdle", idleInterval);
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

    }

    // ... existing code ...

    /**
     * 库位推荐逻辑（不推荐上层库位，无左右相邻捆包判断）
     * S_LI_DS_0606_NOUPPER
     *
     * @param inInfo 输入参数
     * @return 推荐结果
     */
    public EiInfo recommendedStorageLocationWithoutUpper(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            // 复用现有参数校验逻辑
            Map messageBody = (Map) inInfo.get("messageBody");
            String netWeight = MapUtils.getString(messageBody, "netWeight");
            String segNo = MapUtils.getString(messageBody, "segNo");
            String tradeCode = MapUtils.getString(messageBody, "tradeCode");
            String prodDensity = MapUtils.getString(messageBody, "prodDensity");
            String warehouseCode = MapUtils.getString(messageBody, "warehouseCode");
            String specsDesc = MapUtils.getString(messageBody, "specsDesc");
            String packId = MapUtils.getString(messageBody, "packId");
            String factoryOrderNum = MapUtils.getString(messageBody, "factoryOrderNum");
            String innerDiameter = MapUtils.getString(messageBody, "innerDiameter");
            String prodTypeId = MapUtils.getString(messageBody, "prodTypeId");
            String d_userNum = MapUtils.getString(messageBody, "d_userNum");
            String productProcessId = MapUtils.getString(messageBody, "productProcessId");
            String firstMachineCode = MapUtils.getString(messageBody, "firstMachineCode");
            String xPoint = MapUtils.getString(messageBody, "xPoint");
            // 入库标记，1采购入库，2转库入库
            String storageMark = MapUtils.getString(messageBody, "storageMark");
            if ("82".equals(storageMark)){
                storageMark="2";
            }else {
                storageMark="1";
            }
            // 校验捆包是否已存在库位（复用现有逻辑）
            HashMap<String, Object> params = new HashMap<>();
            params.put("packId", packId);
            params.put("factoryOrderNum", factoryOrderNum);
            params.put("segNo", segNo);
            List<HashMap> existingLocations = this.dao.query("LIDS0606.query", params);
            if (existingLocations.size()>0) {
                warehouseRelease(buildReleaseInfo(existingLocations.get(0))); // 复用释放逻辑
            }
            // 新增机组，首道加工工序字段，用于查询厂区厂房跨区
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("segNo", segNo);
            queryParams.put("warehouseCode", warehouseCode);
            queryParams.put("productProcessId", productProcessId); // 首道加工工序
            queryParams.put("firstMachineCode", firstMachineCode); // 机组
            queryParams.put("storageMark", storageMark); // 类型

            // 计算卷外径（复用现有逻辑）
            double width = extractWidthFromSpecs(specsDesc);
            double weight = Double.parseDouble(netWeight) * 1000;
            double externalDiameterNumber = calculateOuterDiameter(
                    Double.parseDouble(innerDiameter),
                    weight,
                    width,
                    Double.parseDouble(prodDensity) * 1000);
            int externalDiameter = (int) Math.ceil(externalDiameterNumber);

            boolean isLargeVolume = false;
            // 判断是否为超大卷，根据重量，外径 进行判断 查询配置表数值；
            // 超大卷重量 吨
            String weightRestrict = switchValue(segNo, "OVERWEIGHT_ROLLS", warehouseCode);
            // 超大卷外径 mm
            String externalDiameterRestrict = switchValue(segNo, "SUPER_LARGE_VOLUME", warehouseCode);

            // 判断weightRestrict，externalDiameterRestrict是否为空，如果为空，则不进行超大卷处理，直接推荐库位
            if (!StringUtils.isEmpty(weightRestrict)) {
                // 比较weightRestrict，跟netWeight的大小，如果netWeight大于weightRestrict，正常推荐库位，否则进行超大卷处理
                if (Double.parseDouble(netWeight) >= Double.parseDouble(weightRestrict)) {
                    isLargeVolume = true;
                }
            }
            if (!StringUtils.isEmpty(externalDiameterRestrict)) {
                if (externalDiameter >= Double.parseDouble(externalDiameterRestrict)) {
                    isLargeVolume = true;
                }
            }

            /*判断是否为超大卷*/
            if (isLargeVolume) {
                // 超大卷处理逻辑
                String factoryArea = "";
                String factoryBuilding = "";
                String crossArea = "";
                // 查询超大库区配置表
                HashMap paramsLarge = new HashMap<>();
                paramsLarge.put("segNo", segNo);
                paramsLarge.put("warehouseCode", warehouseCode);
                // 查询生效的配置信息
                List<HashMap> listLarge = this.dao.query("LIDS0606.queryLarge", paramsLarge);
                if (listLarge.size() > 0) {
                    // 取跨区，厂区，厂房
                    factoryArea = (String) listLarge.get(0).get("factoryArea");
                    factoryBuilding = (String) listLarge.get(0).get("factoryBuilding");
                    crossArea = (String) listLarge.get(0).get("crossArea");
                } else {
                    // 不存在配置信息，不推荐
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("超大卷库区配置表不存在，不推荐！");
                    return outInfo;
                }
                // 新逻辑：不推荐上层库位，无相邻判断
                Map<String, Object> locationParams = new HashMap<>();
                locationParams.put("segNo", segNo);
                locationParams.put("warehouseCode", warehouseCode);
                locationParams.put("factoryBuilding", factoryBuilding);
                locationParams.put("crossArea", crossArea);
                locationParams.put("width", width);
                locationParams.put("externalDiameter", externalDiameter);
                // 添加必要的查询条件参数
                locationParams.put("locationType", "20");  // 库位类型
                locationParams.put("upDownFlag", "1");    // 上下层标记
                locationParams.put("useStatus", "10");    // 使用状态
                String intervalDistanceMini = switchValue(segNo, "INTERVAL_DISTANCE_MINI", warehouseCode);
                locationParams.put("intervalDistance", intervalDistanceMini); // 间隔距离
                // 查询非上层可用库位
                List<HashMap> availableLocations = this.dao.query("LIDS0606.queryNonUpperLocation", locationParams);
                if(availableLocations.isEmpty()){
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("无符合条件的库位");
                    return outInfo;
                }else{
                    // 选择第一个可用库位
                    Map<String, Object> selectedLocation = availableLocations.get(0);
                    Map map = constructCommonLocationParameterMap(selectedLocation);
                    String availableLength = (String) map.get("availableLength");

                    // 预占用库位
                    Map<String, Object> messageBodyNew = new HashMap<>();
                    messageBodyNew = selectedLocation;  // 保留原始库位信息

                    // 根据方法上下文填入相对应的值
                    messageBodyNew.put("packId", packId);
                    messageBodyNew.put("segNo", segNo);
                    messageBodyNew.put("warehouseCode", warehouseCode);
                    messageBodyNew.put("locationId", selectedLocation.get("locationId"));
                    messageBodyNew.put("availableLength", availableLength);
                    messageBodyNew.put("externalDiameter", String.valueOf(externalDiameter));
                    messageBodyNew.put("innerDiameter", innerDiameter);
                    messageBodyNew.put("specsDesc", specsDesc);
                    messageBodyNew.put("netWeight", String.valueOf(netWeight));
                    messageBodyNew.put("width", String.valueOf(width));
                    messageBodyNew.put("factoryArea", factoryArea);
                    messageBodyNew.put("factoryBuilding", factoryBuilding);
                    messageBodyNew.put("crossArea", crossArea);
                    messageBodyNew.put("prodTypeId", prodTypeId);
                    messageBodyNew.put("d_userNum", d_userNum);
                    messageBodyNew.put("factoryOrderNum", factoryOrderNum);

                    EiInfo einInfo = new EiInfo();
                    einInfo.set("messageBody", messageBodyNew);
                    outInfo = preOccupyLocation(einInfo);
                    if (outInfo.getStatus() != EiConstant.STATUS_SUCCESS) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("库位占用失败：" + outInfo.getMsg());
                        return outInfo;
                    }
                }


            }
            else{
                /*Map<String, Object> queryParams = new HashMap<>();
                queryParams.put("segNo", segNo);
                queryParams.put("warehouseCode", warehouseCode);
                queryParams.put("productProcessId", productProcessId); // 首道加工工序
                queryParams.put("firstMachineCode", firstMachineCode); // 机组*/

                // 查询相对应的厂区厂房跨区信息，需确保数据库映射 LIDS0606.queryFactoryAreaByMachineAndProcess 存在
                //判断首道机组是否为空，工序是否为空
                List<HashMap> plantConfigurationList = new ArrayList<>();
                if(StringUtils.isNotBlank((String) queryParams.get("firstMachineCode"))){
                    if("2".equals(storageMark)){
                        queryParams.put("productProcessId",null);
                        queryParams.put("unloadingType","2");
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea", queryParams);
                    }else{
                        queryParams.put("productProcessId",null);
                        queryParams.put("unloadingType",null);
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                    }
                    //根据首道机组未匹配到跨区
                    if(plantConfigurationList.size() == 0){
                        //queryParams.put("firstMachineCode",null);
                        queryParams.put("productProcessId",null);
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                    }
                    if(plantConfigurationList.size() == 0){
                        queryParams.put("firstMachineCode",null);
                        queryParams.put("productProcessId",null);
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                    }
                }else if(StringUtils.isNotBlank((String) queryParams.get("productProcessId"))){
                    queryParams.put("firstMachineCode",null);
                    if("2".equals(storageMark)){
                        queryParams.put("productProcessId",productProcessId);
                        queryParams.put("unloadingType","2");
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea", queryParams);
                    }else{
                        queryParams.put("productProcessId",productProcessId);
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                    }
                    if(plantConfigurationList.size() ==0){
                        queryParams.put("firstMachineCode",null);
                        //queryParams.put("productProcessId",null);
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                    }
                    if(plantConfigurationList.size() ==0){
                        queryParams.put("firstMachineCode",null);
                        queryParams.put("productProcessId",null);
                        plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                    }
                }else{
                    queryParams.put("firstMachineCode",null);
                    queryParams.put("productProcessId",null);
                    plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea2", queryParams);
                }
                //plantConfigurationList = this.dao.query("LIDS0606.queryFactoryArea", queryParams);
                if (plantConfigurationList.isEmpty()) {
                    // 若未查询到结果，可根据业务需求进行相应处理，这里简单抛出异常
                    throw new RuntimeException("根据机组、首道加工工序、账套和仓库未查询到对应的厂区厂房跨区信息");
                }
                List<HashMap> availableLocations = new ArrayList<>();
                String factoryAreaHost = "";
                String factoryBuildingHost = "";
                String crossAreaHost = "";
                String pollingSchemeNumber = "";
                String factoryArea = "";
                String factoryBuilding = "";
                String crossArea = "";
                String unloadingType = "";
                String crossAreaName = "";

                for(HashMap hashMap:plantConfigurationList){
                    // 取第一个查询结果中的厂区，厂房，跨区信息
                    HashMap plantConfig = hashMap;
                    factoryArea = (String) plantConfig.get("factoryArea");
                    factoryBuilding = (String) plantConfig.get("factoryBuilding");
                    crossArea = (String) plantConfig.get("crossArea");
                    unloadingType = (String) plantConfig.get("unloadingType");
                    crossAreaName = (String) plantConfig.get("crossAreaName");
                    // 新逻辑：不推荐上层库位，无相邻判断
                    Map<String, Object> locationParams = new HashMap<>();
                    locationParams.put("segNo", segNo);
                    locationParams.put("warehouseCode", warehouseCode);
                    locationParams.put("factoryBuilding", factoryBuilding);
                    locationParams.put("crossArea", crossArea);
                    locationParams.put("width", width);
                    locationParams.put("externalDiameter", externalDiameter);
                    // 添加必要的查询条件参数
                    locationParams.put("locationType", "20");  // 库位类型
                    locationParams.put("upDownFlag", "1");    // 上下层标记
                    locationParams.put("useStatus", "10");    // 使用状态
                    if("1".equals(storageMark)){
                        locationParams.put("storageMark", storageMark);   // 入库类型
                    }
                    String intervalDistanceMini = switchValue(segNo, "INTERVAL_DISTANCE_MINI", warehouseCode);
                    locationParams.put("intervalDistance", intervalDistanceMini); // 间隔距离
                    // 查询非上层可用库位
                    availableLocations = this.dao.query("LIDS0606.queryNonUpperLocation", locationParams);
                    if (availableLocations.isEmpty()) {
                        //转库入库进行库位推荐 未匹配到符合条件的库位 直接返回当前跨区
                        if("2".equals(unloadingType)){
                            // 将成功占用的库位信息返回
                            Map<String, Object> resultMap = new HashMap<>();
                            resultMap.put("packId", packId);
                            resultMap.put("locationId", crossArea);
                            resultMap.put("locationName", crossAreaName);
                            resultMap.put("factoryArea", factoryArea);
                            resultMap.put("factoryBuilding", factoryBuilding);
                            resultMap.put("crossArea", crossArea);
                            resultMap.put("warehouseCode", warehouseCode);
                            outInfo.setAttr(resultMap);
                            return outInfo;

                        }
                        // 匹配不到,根据当前信息 查询轮询方案进行再次匹配
                        for (HashMap plantConfig2 : plantConfigurationList) {
                             factoryAreaHost = (String) plantConfig2.get("factoryArea");
                             factoryBuildingHost = (String) plantConfig2.get("factoryBuilding");
                             crossAreaHost = (String) plantConfig2.get("crossArea");
                             pollingSchemeNumber = (String) plantConfig2.get("pollingSchemeNumber");
                            if (StringUtils.isNotBlank(pollingSchemeNumber)) {
                                // 根据轮询方案进行再次匹配 查询轮询方案
                                HashMap pollingScheme = new HashMap<>();
                                pollingScheme.put("segNo", segNo);
                                pollingScheme.put("pollingSchemeNumber", pollingSchemeNumber);
                                pollingScheme.put("factoryArea", factoryAreaHost);
                                pollingScheme.put("factoryBuilding", factoryBuildingHost);
                                pollingScheme.put("crossArea", crossAreaHost);

                                List<HashMap> pollingSchemeList = this.dao.query("LIDS0606.queryPollingScheme",
                                        pollingScheme);
                                if (pollingSchemeList.size() > 0) {
                                    for(HashMap pollingSchemeHost : pollingSchemeList){
                                        locationParams.put("factoryBuilding", MapUtils.getString(pollingSchemeHost, "factoryBuilding"));
                                        locationParams.put("factoryBuilding", MapUtils.getString(pollingSchemeHost, "factoryBuilding"));
                                        locationParams.put("crossArea", MapUtils.getString(pollingSchemeHost, "crossArea"));
                                        availableLocations = this.dao.query("LIDS0606.queryNonUpperLocation", locationParams);
                                        if(availableLocations.size() > 0){
                                            break;
                                        }
                                    }

                                }else{
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg("未查询到轮询方案，推荐失败");
                                    return outInfo;
                                }
                            }else{
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("无符合条件的非上层库位");
                                return outInfo;
                            }

                        }
                        if(availableLocations.isEmpty()){
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("无符合条件的非上层库位");
                            return outInfo;
                        }

                    }
                }
                // 选择第一个可用库位
                Map<String, Object> selectedLocation = availableLocations.get(0);
                Map map = constructCommonLocationParameterMap(selectedLocation);
                String availableLength = (String) map.get("availableLength");
                // 预占用库位
                Map<String, Object> messageBodyNew = new HashMap<>();
                messageBodyNew = selectedLocation;  // 保留原始库位信息

                // 根据方法上下文填入相对应的值
                messageBodyNew.put("packId", packId);
                messageBodyNew.put("segNo", segNo);
                messageBodyNew.put("warehouseCode", warehouseCode);
                messageBodyNew.put("locationId", selectedLocation.get("locationId"));
                messageBodyNew.put("availableLength", availableLength);
                messageBodyNew.put("externalDiameter", String.valueOf(externalDiameter));
                messageBodyNew.put("innerDiameter", String.valueOf(innerDiameter));
                messageBodyNew.put("specsDesc", specsDesc);
                messageBodyNew.put("netWeight", String.valueOf(netWeight));
                messageBodyNew.put("width", String.valueOf(width));
                messageBodyNew.put("factoryArea", factoryArea);
                messageBodyNew.put("factoryBuilding", factoryBuilding);
                messageBodyNew.put("crossArea", crossArea);
                messageBodyNew.put("prodTypeId", prodTypeId);
                messageBodyNew.put("d_userNum", d_userNum);
                messageBodyNew.put("factoryOrderNum", factoryOrderNum);

                EiInfo einInfo = new EiInfo();
                einInfo.set("messageBody", messageBodyNew);
                outInfo = preOccupyLocation(einInfo);
                if (outInfo.getStatus() != EiConstant.STATUS_SUCCESS) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("库位占用失败：" + outInfo.getMsg());
                    return outInfo;
                }
                //occupyResult.setAttr(map);
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("库位推荐成功");
                return outInfo;
            }

            //occupyResult.setAttr(map);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("库位推荐成功");
            return outInfo;
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("库位推荐异常：" + e.getMessage());
            return outInfo;
        }
    }

    /**
     * 库位预占用方法
     * 根据传入的参数预占用库位，并记录相关信息
     *
     * @param inInfo 包含预占用所需参数的 EiInfo 对象
     * @return 返回包含操作结果信息的 EiInfo 对象
     */
    public EiInfo preOccupyLocation(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            // 从输入的 EiInfo 中获取预占用所需的参数
            Map<String, Object> messageBody = (Map) inInfo.get("messageBody");
            
            // 必要参数验证
            String packId = MapUtils.getString(messageBody, "packId");
            if (StringUtils.isEmpty(packId)) {
                throw new RuntimeException("参数错误,捆包号为空(packId)");
            }
            
            String segNo = MapUtils.getString(messageBody, "segNo");
            if (StringUtils.isEmpty(segNo)) {
                throw new RuntimeException("参数错误,系统账套为空(segNo)");
            }
            
            String warehouseCode = MapUtils.getString(messageBody, "warehouseCode");
            if (StringUtils.isEmpty(warehouseCode)) {
                throw new RuntimeException("参数错误,仓库代码为空(warehouseCode)");
            }
            
            String locationId = MapUtils.getString(messageBody, "locationId");
            if (StringUtils.isEmpty(locationId)) {
                throw new RuntimeException("参数错误,库位代码为空(locationId)");
            }
            
            String factoryArea = MapUtils.getString(messageBody, "factoryArea");
            if (StringUtils.isEmpty(factoryArea)) {
                throw new RuntimeException("参数错误,厂区为空(factoryArea)");
            }
            
            String factoryBuilding = MapUtils.getString(messageBody, "factoryBuilding");
            if (StringUtils.isEmpty(factoryBuilding)) {
                throw new RuntimeException("参数错误,厂房为空(factoryBuilding)");
            }
            
            String crossArea = MapUtils.getString(messageBody, "crossArea");
            if (StringUtils.isEmpty(crossArea)) {
                throw new RuntimeException("参数错误,跨区为空(crossArea)");
            }
            
            // 获取其他参数(非必须)
            String updateFlag = MapUtils.getString(messageBody, "updateFlag", "1"); // 默认下层
            String standFlag = MapUtils.getString(messageBody, "standFlag", "0"); // 默认卧式
            String factoryOrderNum = MapUtils.getString(messageBody, "factoryOrderNum", "");
            String prodTypeId = MapUtils.getString(messageBody, "prodTypeId", "");
            String d_userNum = MapUtils.getString(messageBody, "d_userNum", "");
            String specsDesc = MapUtils.getString(messageBody, "specsDesc", "");
            String locationType = MapUtils.getString(messageBody, "locationType", "20"); // 默认条状库位
            String locColumnId = MapUtils.getString(messageBody, "locColumnId", "");
            String locationName = MapUtils.getString(messageBody, "locationName", "");
            String surfaceGrade = MapUtils.getString(messageBody, "surfaceGrade", "");
            String c_no = MapUtils.getString(messageBody, "c_no", "");
            String contractId = MapUtils.getString(messageBody, "contractId", "");
            String remark = MapUtils.getString(messageBody, "remark", "");
            String shopsign = MapUtils.getString(messageBody, "shopsign", "");
            // 数值类型参数处理
            BigDecimal externalDiameter = getBigDecimalFromMap(messageBody, "externalDiameter");
            BigDecimal innerDiameter = getBigDecimalFromMap(messageBody, "innerDiameter");
            BigDecimal netWeight = getBigDecimalFromMap(messageBody, "netWeight");
            BigDecimal width = getBigDecimalFromMap(messageBody, "width");
            BigDecimal specUpper = getBigDecimalFromMap(messageBody, "specUpper");
            BigDecimal specLower = getBigDecimalFromMap(messageBody, "specLower");
            BigDecimal pointLowerLength = getBigDecimalFromMap(messageBody, "pointLowerLength");
            BigDecimal pointUpperLength = getBigDecimalFromMap(messageBody, "pointUpperLength");
            BigDecimal idleIntervalId = getBigDecimalFromMap(messageBody, "idleIntervalId");
            BigDecimal locationLength = getBigDecimalFromMap(messageBody, "locationLength");
            BigDecimal avaliableMinLength = getBigDecimalFromMap(messageBody, "avaliableMinLength");
            // X轴坐标相关参数
            BigDecimal x_pointStart = getBigDecimalFromMap(messageBody, "x_pointStart");
            BigDecimal x_pointEnd = getBigDecimalFromMap(messageBody, "x_pointEnd");
            BigDecimal y_pointCenter = getBigDecimalFromMap(messageBody, "y_pointCenter");
            BigDecimal x_pointStartOrign = getBigDecimalFromMap(messageBody, "x_pointStartOrign");
            BigDecimal x_pointEndOrign = getBigDecimalFromMap(messageBody, "x_pointEndOrign");
            // 检查是否已有占用信息
            HashMap<String, Object> checkParams = new HashMap<>();
            checkParams.put("packId", packId);
            checkParams.put("factoryOrderNum", factoryOrderNum);
            checkParams.put("segNo", segNo);
            
            List<HashMap> existingLocations = this.dao.query("LIDS0606.query", checkParams);
            if (!existingLocations.isEmpty()) {
                   // 已有占用，先释放
                EiInfo releaseInfo = new EiInfo();
                Map<String, Object> releaseMessageBody = new HashMap<>();
                List<Map<String, Object>> releaseMessageBodyList = new ArrayList<>();
                Map<String, Object> releaseMessageBodyMap = new HashMap<>();
                releaseMessageBodyMap.put("segNo", segNo);                    // 系统账套
                releaseMessageBodyMap.put("packId", packId);                  // 捆包号
                releaseMessageBodyMap.put("warehouseCodeSend", warehouseCode);    // 仓库代码
                releaseMessageBodyMap.put("factoryOrderNum", factoryOrderNum);    // 仓库代码
                releaseMessageBodyMap.put("operateTypeSend", "10");    // 仓库代码
                releaseMessageBodyMap.put("spec", specsDesc);    // 仓库代码

                releaseMessageBodyList.add(releaseMessageBodyMap);
                releaseInfo.set("messageBody", releaseMessageBodyList);
                // 调用释放方法并判断返回值
                EiInfo releaseResult = releaseLocation(releaseInfo);
                if (releaseResult.getStatus() == EiConstant.STATUS_FAILURE) {
                    // 释放失败，抛出异常
                    throw new RuntimeException("库位占用失败，捆包已有占用信息释放失败: " + releaseResult.getMsg());
                }
            }
            
            // 新增一条占用信息
            LIDS0605 lids0605 = new LIDS0605();
            
            // 基本信息
            lids0605.setSegNo(segNo); // 系统账套
            lids0605.setUnitCode(segNo); // 业务单元代码
            lids0605.setWproviderId(warehouseCode); // 仓库代码
            lids0605.setLocationId(locationId); // 库位代码
            lids0605.setLocViewPackId(packId); // 捆包编号
            lids0605.setLocColumnId(locColumnId); // 库位(跨+列)
            lids0605.setPackId(packId); // 捆包号
            lids0605.setFactoryProductId(factoryOrderNum); // 工厂订单号/钢厂资源号
            
            // 状态信息
            lids0605.setUseStatus("20"); // 状态：20-预占
            lids0605.setLocationType(locationType); // 库位类型：20-条状
            lids0605.setUpDownFlag(updateFlag); // 上下层标记：1-下层、2-上层
            lids0605.setStandFlag(standFlag); // 是否立式：0-卧式、1-立式
            
            // 坐标和位置信息
            lids0605.setX_pointStart(x_pointStart); // X轴开始
            lids0605.setX_pointEnd(x_pointEnd); // X轴结束
            lids0605.setY_pointCenter(y_pointCenter); // Y轴中心
            lids0605.setX_pointStartOrign(x_pointStartOrign); // X轴起始点初始值
            lids0605.setX_pointEndOrign(x_pointEndOrign); // X轴结束点初始值
            lids0605.setLocationLength(locationLength); // 库位长度
            
            // 相邻捆包信息
            lids0605.setLeftViewPackId(MapUtils.getString(messageBody, "leftViewPackId", "")); // 左侧捆包号
            lids0605.setRightViewPcakId(MapUtils.getString(messageBody, "rightViewPackId", "")); // 右侧捆包号
            lids0605.setUpLeftViewPackId(MapUtils.getString(messageBody, "upLeftViewPackId", "")); // 上层左侧捆包号
            lids0605.setUpRightViewPackId(MapUtils.getString(messageBody, "upRightViewPackId", "")); // 上层右侧捆包号
            
            // 库位规格信息
            lids0605.setSpecUpper(specUpper); // 宽度上限
            lids0605.setSpecLower(specLower); // 宽度下限
            lids0605.setPointLowerLength(pointLowerLength); // 点状库位长度下限
            lids0605.setPointUpperLength(pointUpperLength); // 点状库位长度上限
            lids0605.setAvaliableMinLength(avaliableMinLength); // 条状单个库位最小可用长度
            
            // 产品信息
            lids0605.setProductTypeId(prodTypeId); // 产品类型编号
            lids0605.setCustomerId(d_userNum); // 客户代码
            lids0605.setInnerDiameter(innerDiameter); // 内径
            lids0605.setOuterDiameter(externalDiameter); // 外径
            lids0605.setPutinWeight(netWeight); // 重量
            lids0605.setRefWidth(width); // 宽度
            lids0605.setSpec(specsDesc); // 规格
            lids0605.setShopsign(shopsign); // 牌号
            lids0605.setContractId(contractId); // 销售合同
            
            // 位置信息
            lids0605.setFactoryArea(factoryArea); // 厂区
            lids0605.setFactoryBuilding(factoryBuilding); // 厂房
            lids0605.setCrossArea(crossArea); // 跨区
            lids0605.setLocationName(locationName); // 库位名称
            lids0605.setSurfaceGrade(surfaceGrade); // 表面等级
            lids0605.setC_no(c_no); // 所在跨区的库位序号
            
            // 空闲区间信息
            lids0605.setIdleIntervalId(idleIntervalId); // 条状库位连续空闲区间编号
            lids0605.setOccupiedFlag(MapUtils.getString(messageBody, "occupiedFlag", "0")); // 已占用库位标记
            
            // 其他标记
            lids0605.setLrAccupyFlag(MapUtils.getString(messageBody, "lrAccupyFlag", "0")); // 下层库位空闲且同层相邻左/右至少一侧有卷
            lids0605.setJgLockFlag(MapUtils.getString(messageBody, "jgLockFlag", "0")); // 下层卷是否已下生产计划/工单
            lids0605.setUpForbinFlag(MapUtils.getString(messageBody, "upForbinFlag", "0")); // 下层外板卷时不容许放卷
            
            // 审计信息
            lids0605.setRecCreator(UserSession.getUserId());
            lids0605.setRecCreatorName(UserSession.getLoginCName());
            lids0605.setRecCreateTime(DateUtil.curDateTimeStr14());
            lids0605.setRecRevisor(UserSession.getUserId());
            lids0605.setRecRevisorName(UserSession.getLoginCName());
            lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
            
            // 其他必要字段
            lids0605.setUuid(UUIDUtils.getUUID());
            lids0605.setDelFlag(0);
            lids0605.setArchiveFlag("0");
            lids0605.setTenantId(" ");
            lids0605.setRemark(remark);
            
            // 插入数据库
            this.dao.insert("LIDS0605.insert", lids0605);
            
            // 操作成功，设置状态和消息
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("库位预占用成功");
            
            // 将成功占用的库位信息返回
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("packId", packId);
            resultMap.put("locationId", locationId);
            resultMap.put("factoryArea", factoryArea);
            resultMap.put("factoryBuilding", factoryBuilding);
            resultMap.put("crossArea", crossArea);
            resultMap.put("warehouseCode", warehouseCode);
            outInfo.setAttr(resultMap);
            
        } catch (Exception e) {
            // 捕获异常并设置错误信息
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("库位预占用过程中出现异常: " + e.getMessage());
            System.err.println("库位预占用过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        return outInfo;
    }




    /**
     * 从Map中安全地获取BigDecimal值
     * @param map 源Map
     * @param key 键名
     * @return BigDecimal值，如果不存在则返回BigDecimal.ZERO
     */
    private BigDecimal getBigDecimalFromMap(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(String.valueOf(value));
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    // 以下为辅助方法（可根据实际情况调整或复用现有方法）
    private EiInfo buildReleaseInfo(Map location) {
        EiInfo releaseInfo = new EiInfo();
        Map<String, Object> releaseBody = new HashMap<>();
        releaseBody.put("packId", location.get("packId"));
        releaseBody.put("warehouseCode", location.get("wproviderId"));
        releaseBody.put("factoryArea", location.get("factoryArea"));
        releaseBody.put("crossArea", location.get("crossArea"));
        releaseBody.put("factoryBuilding", location.get("factoryBuilding"));
        releaseInfo.set("messageBody", releaseBody);
        return releaseInfo;
    }

    /**
     * 构建通用库位参数映射
     * 该方法从输入映射中提取通用库位相关参数，并构建一个新的映射返回。
     *
     * @param inputMap 包含各种库位相关参数的输入映射
     * @return 包含通用库位参数的新映射
     */
    private Map<String, Object> constructCommonLocationParameterMap(Map<String, Object> inputMap) {
        Map<String, Object> map = new HashMap<>();
        map.put("packId", inputMap.get("packId"));
        map.put("factoryOrderNum", inputMap.get("factoryOrderNum"));
        map.put("netWeight", inputMap.get("netWeight"));
        map.put("width", inputMap.get("width"));
        map.put("externalDiameter", inputMap.get("externalDiameter"));
        map.put("innerDiameter", inputMap.get("innerDiameter"));
        map.put("specsDesc", inputMap.get("specsDesc"));
        map.put("segNo", inputMap.get("segNo"));
        map.put("warehouseCode", inputMap.get("warehouseCode"));
        map.put("tradeCode", inputMap.get("tradeCode"));
        map.put("productProcessId", inputMap.get("productProcessId"));
        map.put("d_userNum", inputMap.get("d_userNum"));
        map.put("prodTypeId", inputMap.get("prodTypeId"));
        return map;
    }

    /**
     * 从规格描述字符串中提取宽度信息。
     * 该方法假设规格描述字符串中包含两个星号，宽度信息位于第一个星号和第二个星号之间。
     *
     * @param specsDesc 规格描述字符串，预期格式包含两个星号，例如 "1.2*1200*C"。
     * @return 从规格描述中提取的宽度值，以双精度浮点数表示。
     * @throws RuntimeException 如果规格描述字符串中不包含两个星号，抛出此异常，表示规格格式错误，无法提取宽度。
     */
    private double extractWidthFromSpecs(String specsDesc) {
        // 检查规格描述字符串中是否包含两个星号
        if (specsDesc.indexOf('*') == -1 || specsDesc.indexOf('*', specsDesc.indexOf('*') + 1) == -1) {
            // 若不包含两个星号，抛出异常提示规格格式错误
            throw new RuntimeException("规格格式错误，无法提取宽度");
        }
        // 提取第一个星号和第二个星号之间的字符串，并将其转换为双精度浮点数返回
        return Double.parseDouble(specsDesc.substring(
                specsDesc.indexOf('*') + 1,
                specsDesc.indexOf('*', specsDesc.indexOf('*') + 1)));
    }


    /**
     * 库位释放方法
     * 删除捆包占用库位信息，设置删除标记为1
     *S_LI_DS_0007 共享服务号
     * @param inInfo 包含库位释放所需参数的 EiInfo 对象
     * @return 返回包含操作结果信息的 EiInfo 对象
     */
    public EiInfo releaseLocation(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            // 1. 获取请求参数
            List<Map<String, Object>> messageBody = (List<Map<String, Object>>) inInfo.get("messageBody");
            if (messageBody == null) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("参数错误，请求体为空");
                return outInfo;
            }
            List<HashMap> locationListAll = new ArrayList<>();//删除集合
            List<HashMap> locationListAllBlack = new ArrayList<>();//撤销集合
            for(Map<String, Object> map : messageBody){
                // 2. 获取必填参数
                String segNo = MapUtils.getString(map, "segNo");
                String packId = MapUtils.getString(map, "packId");
                String warehouseCode = MapUtils.getString(map, "warehouseCodeSend");
                String factoryOrderNum = MapUtils.getString(map, "factoryOrderNum");
                String operateType = MapUtils.getString(map, "operateTypeSend");
                String spec = MapUtils.getString(map, "spec");
                // 3. 验证必填参数
                if (StringUtils.isEmpty(segNo)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，账套为空(segNo)");
                    return outInfo;
                }
                if (StringUtils.isEmpty(packId)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，捆包号为空(packId)");
                    return outInfo;
                }
                if (StringUtils.isEmpty(warehouseCode)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，仓库代码为空(warehouseCode)");
                    return outInfo;
                }
                if (StringUtils.isEmpty(factoryOrderNum)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，钢厂订单号为空(factoryOrderNum)");
                    return outInfo;
                }

                // 4. 查询该捆包所占用的库位信息
                HashMap<String, Object> queryParams = new HashMap<>();
                queryParams.put("packId", packId);
                queryParams.put("segNo", segNo);
                queryParams.put("warehouseCode", warehouseCode);
                queryParams.put("delFlag", 0); // 只查询未删除的记录
                queryParams.put("factoryOrderNum", factoryOrderNum);
                if("00".equals(operateType)){
                    queryParams.put("spec", spec);
                    List<HashMap> locationListBlack = this.dao.query("LIDS0606.queryBlackOne", queryParams);
                    if (locationListBlack.size()>0) {
                        locationListAllBlack.add(locationListBlack.get(0));
                    }
                }else{
                    List<HashMap> locationList = this.dao.query("LIDS0606.query", queryParams);
                    if (locationList.size()>0) {
                        locationListAll.add(locationList.get(0));

                    }
                }
            }
            // 5. 更新库位状态为删除
            for (HashMap location : locationListAll) {
                LIDS0605 lids0605 = new LIDS0605();
                lids0605.setLocationId(MapUtils.getString(location, "locationId"));
                lids0605.setLocViewPackId(MapUtils.getString(location, "locViewPackId"));
                lids0605.setFactoryProductId(MapUtils.getString(location, "factoryProductId"));
                lids0605.setSegNo(MapUtils.getString(location, "segNo"));
                lids0605.setWproviderId(MapUtils.getString(location, "wproviderId"));
                lids0605.setDelFlag(1); // 设置删除标记
                lids0605.setRecRevisor(UserSession.getUserId());
                lids0605.setRecRevisorName(UserSession.getLoginCName());
                lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
                this.dao.update("LIDS0606.deleteIdleIntervalTwo", lids0605);
            }
            // 5. 更新库位状态为正常 撤销
            for (HashMap location : locationListAllBlack) {
                //查询当前捆包是否存在占用数据
                List<HashMap> locationList = this.dao.query("LIDS0606.queryBlack", location);
                if(locationList.size()>0){
                    continue;
                }
                LIDS0605 lids0605 = new LIDS0605();
                lids0605.setLocationId(MapUtils.getString(location, "locationId"));
                lids0605.setLocViewPackId(MapUtils.getString(location, "locViewPackId"));
                lids0605.setFactoryProductId(MapUtils.getString(location, "factoryProductId"));
                lids0605.setSegNo(MapUtils.getString(location, "segNo"));
                lids0605.setWproviderId(MapUtils.getString(location, "wproviderId"));
                lids0605.setRecRevisor(UserSession.getUserId());
                lids0605.setRecRevisorName(UserSession.getLoginCName());
                lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
                this.dao.update("LIDS0606.deleteIdleIntervalBlack", lids0605);
            }

            // 6. 返回成功信息
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("库位释放成功");

        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("库位释放失败：" + e.getMessage());
            System.err.println("库位释放过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        return outInfo;
    }

    /**
     * 更新库位状态为占用
     * 将指定库位的状态更新为占用状态(30)
     *
     * @param inInfo 包含库位信息的 EiInfo 对象
     * @return 返回包含操作结果信息的 EiInfo 对象
     */
    public EiInfo updateLocationToOccupied(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            // 1. 获取请求参数
            List<Map<String, Object>> messageBody = (List<Map<String, Object>>) inInfo.get("messageBody");
            if (messageBody == null) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("参数错误，请求体为空");
                return outInfo;
            }
            for(Map<String, Object> message : messageBody){
                // 2. 获取必填参数
                String segNo = MapUtils.getString(message, "segNo");
                String packId = MapUtils.getString(message, "packId");
                String warehouseCode = MapUtils.getString(message, "warehouseCode");
                String locationId = MapUtils.getString(message, "locationId");
                //钢厂订单号
                String factoryOrderNum = MapUtils.getString(message, "factoryOrderNum");
                //内径
                String innerDiameter = MapUtils.getString(message, "innerDiameter");
                String specsDesc = MapUtils.getString(message, "specsDesc");
                String netWeight = MapUtils.getString(message, "netWeight");
                String prodDensity = MapUtils.getString(message, "prodDensity");
                String tradeCode = MapUtils.getString(message, "tradeCode");
                String prodTypeId = MapUtils.getString(message, "prodTypeId");
                String d_userNum = MapUtils.getString(message, "d_userNum");
                String productProcessId = MapUtils.getString(message, "productProcessId");
                String firstMachineCode = MapUtils.getString(message, "firstMachineCode");
                String shopsign = MapUtils.getString(message, "shopsign", "");
                // 3. 验证必填参数
                if (StringUtils.isEmpty(segNo)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，账套为空(segNo)");
                    return outInfo;
                }
                if (StringUtils.isEmpty(packId)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，捆包号为空(packId)");
                    return outInfo;
                }
                if (StringUtils.isEmpty(warehouseCode)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，仓库代码为空(warehouseCode)");
                    return outInfo;
                }
                if (StringUtils.isEmpty(locationId)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("参数错误，库位代码为空(locationId)");
                    return outInfo;
                }
                // 4. 查询库位信息
                HashMap<String, Object> queryParams = new HashMap<>();
                queryParams.put("packId", packId);
                queryParams.put("segNo", segNo);
                queryParams.put("warehouseCode", warehouseCode);
                queryParams.put("locationId", locationId);
                queryParams.put("delFlag", 0); // 只查询未删除的记录
                List<HashMap> locationList = this.dao.query("LIDS0606.query", queryParams);
                if (locationList.isEmpty()) {
                    //未查询到捆包预占用信息 当前库位
                    //根据捆包查询预占用信息
                    //查询到释放出来
                    queryParams.put("locationId", "");
                    queryParams.put("factoryOrderNum", factoryOrderNum);
                    List<HashMap> locationList2 = this.dao.query("LIDS0606.query", queryParams);
                    if(locationList2.size()>0){
                        for(HashMap hashMap:locationList2){
                            //释放占用信息
                            LIDS0605 lids0605 = new LIDS0605();
                            lids0605.setLocationId(MapUtils.getString(hashMap, "locationId"));
                            lids0605.setLocViewPackId(MapUtils.getString(hashMap, "locViewPackId"));
                            lids0605.setFactoryProductId(MapUtils.getString(hashMap, "factoryProductId"));
                            lids0605.setSegNo(MapUtils.getString(hashMap, "segNo"));
                            lids0605.setWproviderId(MapUtils.getString(hashMap, "wproviderId"));
                            lids0605.setDelFlag(1); // 设置删除标记
                            lids0605.setRecRevisor(UserSession.getUserId());
                            lids0605.setRecRevisorName(UserSession.getLoginCName());
                            lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
                            this.dao.update("LIDS0606.deleteIdleIntervalTwo", lids0605);
                        }
                    }
                    //新增一条占用信息 查询当前库位信息
                    HashMap hashMap = new HashMap();
                    hashMap.put("segNo", segNo);
                    hashMap.put("wproviderId", warehouseCode);
                    hashMap.put("locationId", locationId);
                    List<HashMap> list = this.dao.query("LIDS0606.queryListOne", hashMap);
                    if(list.size()>0){
                        HashMap hashMapOne = new HashMap();
                        hashMapOne = list.get(0);
                        // 计算卷外径（复用现有逻辑）
                        double width = extractWidthFromSpecs(specsDesc);
                        double weight = Double.parseDouble(netWeight) * 1000;
                        double externalDiameterNumber = calculateOuterDiameter(
                                Double.parseDouble(innerDiameter),
                                weight,
                                width,
                                Double.parseDouble(prodDensity) * 1000);
                        int externalDiameter = (int) Math.ceil(externalDiameterNumber);
                        //放入当前捆包信息
                        hashMapOne.put("packId", packId);
                        hashMapOne.put("locViewPackId", packId);
                        hashMapOne.put("factoryProductId", factoryOrderNum);
                        hashMapOne.put("useStatus", 30);
                        hashMapOne.put("productTypeId", prodTypeId);
                        hashMapOne.put("shopsign", shopsign);
                        hashMapOne.put("spec", specsDesc);
                        hashMapOne.put("innerDiameter", innerDiameter);
                        hashMapOne.put("outerDiameter", externalDiameter);
                        hashMapOne.put("putinWeight", netWeight);
                        hashMapOne.put("refWidth", width);
                        hashMapOne.put("customerId", d_userNum);
                        hashMapOne.put("recCreator",UserSession.getUserId());
                        hashMapOne.put("recCreatorName",UserSession.getLoginCName());
                        hashMapOne.put("recCreateTime",DateUtil.curDateTimeStr14());
                        hashMapOne.put("recRevisor",UserSession.getUserId());
                        hashMapOne.put("recRevisorName",UserSession.getLoginCName());
                        hashMapOne.put("recReviseTime",DateUtil.curDateTimeStr14());
                        // 其他必要字段
                        hashMapOne.put("uuid",UUIDUtils.getUUID());
                        this.dao.insert("LIDS0605.insert", hashMapOne);
                    }else{
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("库位状态更新失败!");
                        return outInfo;
                    }

                }
                // 5. 更新库位状态为占用
                for (HashMap location : locationList) {
                    LIDS0605 lids0605 = new LIDS0605();
                    lids0605.setPackId(packId);
                    lids0605.setSegNo(segNo);
                    lids0605.setWproviderId(warehouseCode);
                    lids0605.setLocationId(locationId);
                    lids0605.setUseStatus("30"); // 设置状态为占用
                    lids0605.setRecRevisor(UserSession.getUserId());
                    lids0605.setRecRevisorName(UserSession.getLoginCName());
                    lids0605.setRecReviseTime(DateUtil.curDateTimeStr14());
                    this.dao.update("LIDS0606.updatePackStatus", lids0605);
                }


            }
            // 6. 返回成功信息
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("库位状态更新为占用成功");

        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("库位状态更新失败：" + e.getMessage());
            System.err.println("库位状态更新过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        return outInfo;
    }

}
