<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XTSS12">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="protRoleType">
            PROT_ROLE_TYPE = #protRoleType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processId">
            PROCESS_ID = #processId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="nodeNo">
            NODE_NO = #nodeNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configName">
            CONFIG_NAME LIKE '%'||#configName#||'%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="nodeName">
            NODE_NAME LIKE '%'||#nodeName#||'%'
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.xt.ss.domain.XTSS12">
        SELECT UUID as "uuid",
        CONFIG_NAME as "configName",
        PROT_ROLE_TYPE as "protRoleType",
        PROCESS_ID as "processID",
        REC_CREATE_TIME as "recCreateTime",
        NODE_NO as "nodeNo",
        NODE_NAME as "nodeName"
        FROM ${platSchema}.TVISM0116
        WHERE DEL_FLAG = 0
        AND (SEG_NO IS NULL OR SEG_NO = '')
        AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                PROCESS_ID DESC
            </isEmpty>
        </dynamic>


    </select>

    <select id="queryApprId" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.xt.ss.domain.XTSS12">
        SELECT UUID as "uuid",
        CONFIG_NAME as "configName",
        PROT_ROLE_TYPE as "protRoleType",
        PROCESS_ID as "processID",
        REC_CREATE_TIME as "recCreateTime",
        NODE_NO as "nodeNo",
        NODE_NAME as "nodeName"
        FROM ${platSchema}.TVISM0116
        WHERE DEL_FLAG = 0
        and SEG_NO=#segNo#
        <isNotEmpty prepend=" AND " property="apprId">
            APPR_ID = #apprId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="apprName">
            APPR_NAME = #apprName#
        </isNotEmpty>
    </select>

    <!-- 开关定义删除 -->
    <delete id="delete2">
        DELETE FROM ${platSchema}.TVISM0116 WHERE UUID = #uuid#
        AND (SEG_NO IS NULL OR SEG_NO = '') AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
    </delete>
    <select id="count" resultClass="int">
        SELECT count(1) FROM
        FROM ${platSchema}.TVISM0116
        WHERE DEL_FLAG = 0
        AND (SEG_NO IS NULL OR SEG_NO = '')
        AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${platSchema}.TVISM0116
        <dynamic prepend="(" close=")">
            <isNotEmpty property="segNo" prepend=",">SEG_NO</isNotEmpty>
            <isNotEmpty property="unitCode" prepend=",">UNIT_CODE</isNotEmpty>
            <isNotEmpty property="uuid" prepend=",">UUID</isNotEmpty>
            <isNotEmpty property="recCreator" prepend=",">REC_CREATOR</isNotEmpty>
            <isNotEmpty property="recCreatorName" prepend=",">REC_CREATOR_NAME</isNotEmpty>
            <isNotEmpty property="recCreateTime" prepend=",">REC_CREATE_TIME</isNotEmpty>
            <isNotEmpty property="recRevisor" prepend=",">REC_REVISOR</isNotEmpty>
            <isNotEmpty property="recRevisorName" prepend=",">REC_REVISOR_NAME</isNotEmpty>
            <isNotEmpty property="recReviseTime" prepend=",">REC_REVISE_TIME</isNotEmpty>
            <isNotEmpty property="delFlag" prepend=",">DEL_FLAG</isNotEmpty>
            <isNotEmpty property="tenantUser" prepend=",">TENANT_USER</isNotEmpty>
            <isNotEmpty property="processId" prepend=",">PROCESS_ID</isNotEmpty>
            <isNotEmpty property="configName" prepend=",">CONFIG_NAME</isNotEmpty>
            <isNotEmpty property="nodeNo" prepend=",">NODE_NO</isNotEmpty>
            <isNotEmpty property="protRoleType" prepend=",">PROT_ROLE_TYPE</isNotEmpty>
            <isNotEmpty property="apprId" prepend=",">APPR_ID</isNotEmpty>
            <isNotEmpty property="apprName" prepend=",">APPR_NAME</isNotEmpty>
            <isNotEmpty property="nodeName" prepend=",">NODE_NAME</isNotEmpty>


        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotEmpty property="segNo" prepend=",">#segNo#</isNotEmpty>
            <isNotEmpty property="unitCode" prepend=",">#unitCode#</isNotEmpty>
            <isNotEmpty property="uuid" prepend=",">#uuid#</isNotEmpty>
            <isNotEmpty property="recCreator" prepend=",">#recCreator#</isNotEmpty>
            <isNotEmpty property="recCreatorName" prepend=",">#recCreatorName#</isNotEmpty>
            <isNotEmpty property="recCreateTime" prepend=",">#recCreateTime#</isNotEmpty>
            <isNotEmpty property="recRevisor" prepend=",">#recRevisor#</isNotEmpty>
            <isNotEmpty property="recRevisorName" prepend=",">#recRevisorName#</isNotEmpty>
            <isNotEmpty property="recReviseTime" prepend=",">#recReviseTime#</isNotEmpty>
            <isNotEmpty property="delFlag" prepend=",">#delFlag#</isNotEmpty>
            <isNotEmpty property="tenantUser" prepend=",">#tenantUser#</isNotEmpty>
            <isNotEmpty property="processId" prepend=",">#processId#</isNotEmpty>
            <isNotEmpty property="configName" prepend=",">#configName#</isNotEmpty>
            <isNotEmpty property="nodeNo" prepend=",">#nodeNo#</isNotEmpty>
            <isNotEmpty property="protRoleType" prepend=",">#protRoleType#</isNotEmpty>
            <isNotEmpty property="apprId" prepend=",">#apprId#</isNotEmpty>
            <isNotEmpty property="apprName" prepend=",">#apprName#</isNotEmpty>
            <isNotEmpty property="nodeName" prepend=",">#nodeName#</isNotEmpty>
        </dynamic>
    </insert>

    <delete id="delete">
        DELETE FROM ${platSchema}.TVISM0116 WHERE UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${platSchema}.TVISM0116
        <dynamic prepend="set">
            <isNotEmpty property="recRevisor" prepend=",">REC_REVISOR= #recRevisor#</isNotEmpty>
            <isNotEmpty property="recRevisorName" prepend=",">REC_REVISOR_NAME = #recRevisorName#</isNotEmpty>
            <isNotEmpty property="recReviseTime" prepend=",">REC_REVISE_TIME = #recReviseTime#</isNotEmpty>
            <isNotEmpty property="protRoleType" prepend=",">PROT_ROLE_TYPE = #protRoleType#</isNotEmpty>
        </dynamic>
        ,PROCESS_ID = #processId#,   <!-- 流程ID -->
        CONFIG_NAME = #configName#,   <!-- 流程名称 -->
        NODE_NO = #nodeNo#,   <!-- 节点号 -->
        APPR_ID = #apprId#,   <!-- 审批人工号 -->
        APPR_NAME = #apprName#,   <!-- 审批人姓名 -->
        NODE_NAME = #nodeName#  <!-- 节点名称 -->
        WHERE UUID = #uuid#
        AND SEG_NO = #segNo#
    </update>

    <update id="update2">
        UPDATE ${platSchema}.TVISM0116
        <dynamic prepend="set">
            <isNotEmpty property="recRevisor" prepend=",">REC_REVISOR= #recRevisor#</isNotEmpty>
            <isNotEmpty property="recRevisorName" prepend=",">REC_REVISOR_NAME = #recRevisorName#</isNotEmpty>
            <isNotEmpty property="recReviseTime" prepend=",">REC_REVISE_TIME = #recReviseTime#</isNotEmpty>
        </dynamic>
        ,PROCESS_ID = #processId#,   <!-- 流程ID -->
        CONFIG_NAME = #configName#,   <!-- 流程名称 -->
        NODE_NO = #nodeNo#,   <!-- 节点号 -->
        NODE_NAME = #nodeName#  <!-- 节点名称 -->

        WHERE UUID = #uuid#
        AND (SEG_NO IS NULL OR SEG_NO = '')
        AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
    </update>

    <select id="querySwitchSettingCount" resultClass="int">
        SELECT COUNT(1) FROM ${platSchema}.TVISM0116
        WHERE DEL_FLAG = 0
        AND PROCESS_ID = #processId#
        AND NODE_NO = #nodeNo#
        AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '')
        AND (UNIT_CODE IS NOT NULL OR UNIT_CODE &lt;&gt; '')
    </select>

    <select id="queryProcessSwitchNameCount" resultClass="int">
        SELECT COUNT(1) FROM ${platSchema}.TVISM0116
        WHERE DEL_FLAG = 0
        AND PROCESS_ID = #processId#
        AND NODE_NO = #nodeNo#
        AND (SEG_NO IS NULL OR SEG_NO = '')
        AND (UNIT_CODE IS NULL OR UNIT_CODE = '')
        <isNotEmpty prepend=" AND " property="uuid">
            UUID &lt;&gt; #uuid#
        </isNotEmpty>
    </select>


    <select id="queryProcessNodeKey" resultClass="int">

        SELECT count(1)
        FROM ${platSchema}.TVISM0116
        WHERE DEL_FLAG = 0
        AND PROCESS_ID = #processId#
        AND NODE_NO = #nodeNo#
        AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '')
        AND (UNIT_CODE IS NOT NULL OR UNIT_CODE &lt;&gt; '')
        and SEG_NO=#segNo#
        and APPR_ID=#apprId#
        <isNotEmpty prepend=" AND " property="uuid">
            UUID &lt;&gt; #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="protRoleType">
            PROT_ROLE_TYPE = #protRoleType#
        </isNotEmpty>
    </select>

    <select id="queryDetail" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.xt.ss.domain.XTSS12">

        select SEG_NO as "segNo",
        UNIT_CODE as "unitCode",
        REC_CREATOR as "recCreator",
        REC_CREATOR_NAME as "recCreatorName",
        REC_CREATE_TIME as "recCreateTime",
        REC_REVISOR as "recRevisor",
        REC_REVISOR_NAME as "recRevisorName",
        REC_REVISE_TIME as "recReviseTime",
        TENANT_USER as "tenantUser",
        DEL_FLAG as "delFlag",
        UUID as "uuid",
        PROCESS_ID as "processId",
        CONFIG_NAME as "configName",
        NODE_NO as "nodeNo",
        PROT_ROLE_TYPE as "protRoleType",
        APPR_ID as "apprId",
        APPR_NAME as "apprName",
        NODE_NAME as "nodeName",
        (SELECT DISTINCT a.SEG_NAME
        FROM ${platSchema}.TVZBM81 a
        WHERE a.DEL_FLAG = 0
        AND a.SEG_LEVEL = 0
        AND a.SEG_STATUS = '20'
        AND a.SEG_NO = t.SEG_NO) "segName"
        from ${platSchema}.TVISM0116 t
        where DEL_FLAG = 0
        AND (SEG_NO IS NOT NULL AND SEG_NO &lt;&gt; '')
        AND (UNIT_CODE IS NOT NULL OR UNIT_CODE &lt;&gt; '')
        AND SEG_NO=#segNo#
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC, UUID DESC
            </isEmpty>
        </dynamic>
    </select>


    <select id="queryProcessKey" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
        select t.PROCESS_KEY as "processKey",
        t.PROCESS_NAME as "procesName",
        s.NODE_KEY as "nodeKey",
        s.NODE_NAME as "nodeName"
        from ${platSchema}.TEWPD01 t,
        ${platSchema}.TEWPD03 s,
        (select t.PROCESS_KEY, max(CAST(t.PROCESS_VERSION as signed)) as PROCESS_VERSION
        from ${platSchema}.TEWPD01 t
        group by t.PROCESS_KEY) tt
        where t.PROCESS_DEF_ID = s.PROCESS_DEF_ID
        and tt.PROCESS_KEY = t.PROCESS_KEY
        and tt.PROCESS_VERSION = t.PROCESS_VERSION
        and s.NODE_TYPE = 'Manual'
        and s.NODE_NAME != '编辑'
        <isNotEmpty prepend=" AND " property="procesName">
            t.PROCESS_NAME like ('%$procesName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="nodeName">
            s.NODE_NAME like ('%$nodeName$%')
        </isNotEmpty>
    </select>


    <!--查询审批人信息-->
    <select id="queryForAudit" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select t.APPR_ID "userId",
        t.APPR_NAME "userName"
        from ${platSchema}.TVISM0116 t
        where t.SEG_NO = #segNo#
        and t.PROCESS_ID = #processId#
        AND t.NODE_NO = #nodeNo#
        <isNotEmpty prepend=" AND " property="protRoleType">
            t.PROT_ROLE_TYPE = #protRoleType#
        </isNotEmpty>
    </select>
</sqlMap>