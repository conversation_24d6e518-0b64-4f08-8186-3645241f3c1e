package com.baosight.imom.vg.dm.service;

import com.baosight.imom.vg.dm.domain.VGDM0801;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.ValidationUtils;
import com.baosight.imom.vg.dm.domain.VGDM0104;
import com.baosight.imom.vg.dm.domain.VGDM0501;
import com.baosight.imom.vg.dm.domain.VGDM0701;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> yzj
 * @Description : 点检异常清单页面后台
 * @Date : 2024/8/19
 * @Version : 1.0
 */
public class ServiceVGDM05 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM05.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0501().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK).addBlockMeta(new VGDM0501().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0501.QUERY, new VGDM0501());
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0501 vgdm0501 = new VGDM0501();
            vgdm0501.fromMap(block.getRow(0));
            // 数据校验
            this.checkData(vgdm0501);
            // 默认字段
            vgdm0501.insertData(dao, true);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验点检异常信息
     *
     * @param vgdm0501 点检异常信息
     */
    private void checkData(VGDM0501 vgdm0501) {
        // 使用ValidationUtils进行基本字段校验
        ValidationUtils.validateEntity(vgdm0501);
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0501 vgdm0501 = new VGDM0501();
            vgdm0501.fromMap(block.getRow(0));
            VGDM0501 dbVgdm0501 = DaoUtils.queryAndCheckStatus(dao, vgdm0501, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
            this.checkData(vgdm0501);
            // 可修改字段
            dbVgdm0501.setHandleMeasures(vgdm0501.getHandleMeasures());
            dbVgdm0501.setProcResult(vgdm0501.getProcResult());
            dbVgdm0501.setIsOverhaulHandle("");
            dbVgdm0501.setIsFaultHandle("");
            dbVgdm0501.setTemporaryMeasures("");
            // 异常来源为30操作新增或40专业新增时可修改字段
            if ("30".equals(dbVgdm0501.getExceptionSource())
                    || "40".equals(dbVgdm0501.getExceptionSource())) {
                dbVgdm0501.setExceptionSource(vgdm0501.getExceptionSource());
                dbVgdm0501.setSpotCheckImplemente(vgdm0501.getSpotCheckImplemente());
                dbVgdm0501.setSpotCheckContent(vgdm0501.getSpotCheckContent());
                dbVgdm0501.setJudgmentStandard(vgdm0501.getJudgmentStandard());
                dbVgdm0501.setSpotCheckMethod(vgdm0501.getSpotCheckMethod());
                dbVgdm0501.setSpotCheckStandardType(vgdm0501.getSpotCheckStandardType());
                dbVgdm0501.setActualsRemark(vgdm0501.getActualsRemark());
                dbVgdm0501.setMeasureId(" ");
                dbVgdm0501.setLowerLimit(BigDecimal.ZERO);
                dbVgdm0501.setUpperLimit(BigDecimal.ZERO);
                // 标准类型为20定量时更新计量信息
                if ("20".equals(vgdm0501.getSpotCheckStandardType())) {
                    dbVgdm0501.setMeasureId(vgdm0501.getMeasureId());
                    dbVgdm0501.setLowerLimit(vgdm0501.getLowerLimit());
                    dbVgdm0501.setUpperLimit(vgdm0501.getUpperLimit());
                }
            }
            // 实施方为20设备人员时更新临时措施、是否检修
            if ("20".equals(dbVgdm0501.getSpotCheckImplemente())) {
                if ("1".equals(vgdm0501.getIsOverhaulHandle())
                        && "1".equals(vgdm0501.getIsFaultHandle())) {
                    throw new PlatException("是否检修处理和是否故障处理不能同时为是");
                }
                dbVgdm0501.setIsOverhaulHandle(vgdm0501.getIsOverhaulHandle());
                dbVgdm0501.setIsFaultHandle(vgdm0501.getIsFaultHandle());
                dbVgdm0501.setTemporaryMeasures(vgdm0501.getTemporaryMeasures());
            }
            // 赋值通用字段
            Map data = dbVgdm0501.toMap();
            RecordUtils.setRevisor(data);
            dao.update(VGDM0501.UPDATE, data);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0501 frontData;
            VGDM0501 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0501();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                // 异常来源为操作点检或专业点检的数据不能删除
                if ("10".equals(dbData.getExceptionSource())
                        || "20".equals(dbData.getExceptionSource())) {
                    throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ARTIFICIAL);
                }
                dbData.setExceptionStatus(MesConstant.Status.K00);
                dbData.setDelFlag("1");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0501.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 提交
     *
     * <p>状态新增->已提交
     */
    public EiInfo submit(EiInfo inInfo) {
        return this.updateStatus(inInfo
                , MesConstant.Status.K10
                , MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS
                , MesConstant.Status.K20
                , MessageCodeConstant.successMessage.MSG_SUCCESS_SUBMIT
                , true);
    }

    /**
     * 取消提交
     *
     * <p>状态已提交->新增
     */
    public EiInfo cancelSubmit(EiInfo inInfo) {
        return this.updateStatus(inInfo
                , MesConstant.Status.K20
                , MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS
                , MesConstant.Status.K10
                , MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL
                , false);
    }

    /**
     * 更新状态
     *
     * @param inInfo      前端参数
     * @param checkStatus 待校验状态
     * @param errMsg      状态不一致报错信息
     * @param updStatus   修改后状态
     * @param successMsg  成功信息
     * @param isCheck     是否校验数据
     * @return inInfo
     */
    private EiInfo updateStatus(EiInfo inInfo, String checkStatus, String errMsg, String updStatus, String successMsg, boolean isCheck) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0501 frontData;
            VGDM0501 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0501();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, errMsg, checkStatus);
                // 校验数据（提交时防止提交空数据）
                if (isCheck) {
                    this.checkData(dbData);
                }
                dbData.setExceptionStatus(updStatus);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0501.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(successMsg);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 直接确认
     *
     * <p>状态新增->确认
     * 是否检修为是时生成检修计划，是否故障为是时生成故障信息
     */
    public EiInfo confirm(EiInfo inInfo) {
        return this.confirmInfo(inInfo, MesConstant.Status.K10, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS);
    }

    /**
     * 审核通过
     *
     * <p>状态已提交->确认
     * 是否检修为是时生成检修计划，是否故障为是时生成故障信息
     */
    public EiInfo agree(EiInfo inInfo) {
        return this.confirmInfo(inInfo, MesConstant.Status.K20, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS);
    }

    /**
     * 确认
     * <p>
     * 校验原状态并改为确认状态
     * 是否检修为是时生成检修计划，是否故障为是时生成故障信息
     *
     * @param inInfo      前端参数
     * @param checkStatus 待校验状态
     * @param errMsg      状态不一致报错信息
     * @return inInfo
     */
    private EiInfo confirmInfo(EiInfo inInfo, String checkStatus, String errMsg) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0501 frontData;
            VGDM0501 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0501();
                frontData.fromMap(block.getRow(i));
                // 校验状态
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, errMsg, checkStatus);
                // 改为确认状态
                dbData.setExceptionStatus(MesConstant.Status.K30);
                // 生成检修计划
                this.createOverhaul(dbData);
                // 生成故障信息
                this.createFault(dbData);
                // 待更新数据
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            // 更新数据
            DaoUtils.updateBatch(dao, VGDM0501.UPDATE, block.getRows());
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 创建检修计划
     *
     * @param dbData 点检异常信息
     */
    private void createOverhaul(VGDM0501 dbData) {
        if (!"1".equals(dbData.getIsOverhaulHandle())) {
            return;
        }
        VGDM0801 vgdm0801 = new VGDM0801();
        vgdm0801.setUnitCode(dbData.getUnitCode());
        vgdm0801.setSegNo(dbData.getSegNo());
        // 设备信息
        vgdm0801.setEArchivesNo(dbData.getEArchivesNo());
        vgdm0801.setEquipmentName(dbData.getEquipmentName());
        vgdm0801.setDeviceCode(dbData.getDeviceCode());
        vgdm0801.setDeviceName(dbData.getDeviceName());
        // 关联单据
        vgdm0801.setExceptionContactId(dbData.getExceptionContactId());
        vgdm0801.setVoucherNum(dbData.getExceptionContactId());
        // 检修来源-异常
        vgdm0801.setOverhaulSource("2");
        // 新增数据
        vgdm0801.insertData(dao);
    }

    /**
     * 创建故障信息
     *
     * @param dbData 点检异常信息
     */
    private void createFault(VGDM0501 dbData) {
        if (!"1".equals(dbData.getIsFaultHandle())) {
            return;
        }
        VGDM0701 vgdm0701 = new VGDM0701();
        vgdm0701.setUnitCode(dbData.getUnitCode());
        vgdm0701.setSegNo(dbData.getSegNo());
        // 设备信息
        vgdm0701.setEArchivesNo(dbData.getEArchivesNo());
        vgdm0701.setEquipmentName(dbData.getEquipmentName());
        vgdm0701.setDeviceCode(dbData.getDeviceCode());
        vgdm0701.setDeviceName(dbData.getDeviceName());
        // 关联单据
        vgdm0701.setVoucherNum(dbData.getExceptionContactId());
        // 故障描述
        vgdm0701.setFaultDesc(dbData.getActualsRemark());
        // 故障开始时间
        vgdm0701.setFaultStartTime(dbData.getRecCreateTime());
        // 故障来源-异常
        vgdm0701.setFaultSource("3");
        // 新增数据
        vgdm0701.insertData(dao);
    }

    /**
     * 审核驳回
     *
     * <p>状态已提交->新增
     */
    public EiInfo reject(EiInfo inInfo) {
        return this.updateStatus(inInfo
                , MesConstant.Status.K20
                , MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS
                , MesConstant.Status.K10
                , MessageCodeConstant.successMessage.MSG_SUCCESS_AUDIT
                , false);
    }

    /**
     * 确认退回
     *
     * <p>状态确认->新增
     * 有检修计划或故障时不允许退回
     */
    public EiInfo cancelConfirm(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0501 frontData;
            VGDM0501 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0501();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CONFIRM_STATUS, MesConstant.Status.K30);
                // 校验是否已有检修计划或故障
                this.checkAfter(dbData.getExceptionContactId());
                // 更新状态
                dbData.setExceptionStatus(MesConstant.Status.K10);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0501.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_UPLOAD);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 校验异常信息是否已有检修计划生成
     *
     * @param exceptionContactId 异常单号
     */
    private void checkAfter(String exceptionContactId) {
        Map<String, String> countMap = new HashMap<>();
        countMap.put("exceptionContactId", exceptionContactId);
        countMap.put("delFlag", "0");
        int a = super.count(VGDM0801.COUNT, countMap);
        if (a > 0) {
            throw new PlatException("该异常已生成检修计划，无法退回");
        }
        // 查询故障信息
        countMap.put("voucherNum", exceptionContactId);
        int b = super.count(VGDM0701.COUNT, countMap);
        if (b > 0) {
            throw new PlatException("该异常已生成故障信息，无法退回");
        }
    }

    /**
     * 转设备
     *
     * <p>“异常来源”为“操作新增”、“操作点检”；“实施方”为“生产人员”进行转设备人员处理；
     * 新增状态才容许操作
     */
    public EiInfo toDevice(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0501 frontData;
            VGDM0501 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0501();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                // 校验异常来源是否为操作新增或操作点检
                DaoUtils.compareStr("只能对异常来源为操作新增或操作点检的数据进行操作", dbData.getExceptionSource(), "30", "10");
                // 校验实施方是否为生产人员
                DaoUtils.compareStr("只能对实施方为生产人员的数据进行操作", dbData.getSpotCheckImplemente(), "10");
                // 实施方改为设备人员
                dbData.setSpotCheckImplemente("20");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0501.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 加入设备履历
     */
    public EiInfo addResume(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0501 frontData;
            VGDM0501 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0501();
                frontData.fromMap(block.getRow(i));
                // 校验状态
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CONFIRM_STATUS, MesConstant.Status.K30);
                // 数据返回前端
                block.getRows().set(i, dbData.toMap());
            }
            // 加入设备履历
            VGDM0104.addHistory(block, VGDM0104.RelevanceType.EXCEPTION);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_ADD_RESUME);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
