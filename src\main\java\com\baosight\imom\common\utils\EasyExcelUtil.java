package com.baosight.imom.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.*;

/**
 * EasyExcel 导出工具类
 *
 * <AUTHOR>
 * @version Revision:v1.0
 * @since Date:2024-03-04 16:28
 */
public class EasyExcelUtil {

    private static final Logger log = LoggerFactory.getLogger(EasyExcelUtil.class);

    public static Map<String, String> export2FileStorage(List<String> titleList, List<String> titleColList,
                                                         List<Map<String, Object>> exportDataMapList, String fileName,
                                                         Map<String, Object> loginMap){
        long startTime = System.currentTimeMillis();
        log.info("EasyExcel导出开始:" + exportDataMapList.size() + " | " + loginMap);
        List<List<Object>> dataList = EasyExcelUtil.listMap2ListObj(exportDataMapList, titleColList);
        ByteArrayOutputStream bos = EasyExcelUtil.createWorkbookStream(titleList, dataList);
        Map<String, String> docMap = EasyExcelExportUtil.exprotExcel(new ByteArrayInputStream(bos.toByteArray()), fileName, loginMap);
        log.info("EasyExcel导出结束:" + docMap + ", ct:" + (System.currentTimeMillis() - startTime) / 1000d + "s");
        return docMap;
    }

    public static Map<String, String> export2FileStorage(EiInfo inInfo, List<Map<String, Object>> exportDataMapList, Map<String, Object> loginMap){
        long startTime = System.currentTimeMillis();
        log.info("EasyExcel导出开始:" + exportDataMapList.size() + " | " + loginMap);
        ExportCol exportCol = EasyExcelUtil.parseExportCol(inInfo);
        List<List<Object>> dataList = EasyExcelUtil.listMap2ListObj(exportDataMapList, exportCol.getTitleColList());
        ByteArrayOutputStream bos = EasyExcelUtil.createWorkbookStream(exportCol.getTitleList(), dataList);
        Map<String, String> docMap = EasyExcelExportUtil.exprotExcel(new ByteArrayInputStream(bos.toByteArray()), exportCol.getFileName(), loginMap);
        log.info("EasyExcel导出结束:" + docMap + ", ct:" + (System.currentTimeMillis() - startTime) / 1000d + "s");
        return docMap;
    }

    public static Map<String, String> export2FileStorageForClent(EiInfo inInfo, List<Map<String, Object>> exportDataMapList, Map<String, Object> loginMap){
        long startTime = System.currentTimeMillis();
        log.info("EasyExcel导出开始:" + exportDataMapList.size() + " | " + loginMap);
        ExportCol exportCol = EasyExcelUtil.parseExportCol(inInfo);
        List<List<Object>> dataList = EasyExcelUtil.listMap2ListObj(exportDataMapList, exportCol.getTitleColList());
        ByteArrayOutputStream bos = EasyExcelUtil.createWorkbookStream(exportCol.getTitleList(), dataList);
        FileItem fileItem = EasyExcelExportUtil.createFileItem(new ByteArrayInputStream(bos.toByteArray()), exportCol.getFileName());
        MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
        Map<String, String> docMap = new HashMap<>();
        String downloadUrl = "";
        try{
            downloadUrl =  FileUtils.uploadFileByOriginalName(new CommonsMultipartFile(fileItem), "/pageDownload");
        }catch (Exception e){
            throw new RuntimeException("文件生成失败");
        }
        docMap.put("docUrl",downloadUrl);
        return docMap;
    }

    /**
     * 需要导出多个sheet页
     * @param inInfo
     * @param exportDataMapList
     * @param loginMap
     * @return
     */
    public static Map<String, String> export2FileStorageForMutiSheet(EiInfo inInfo, Map<String, Object> loginMap,List<Map<String, Object>> exportDataMapList,Map<String,List> sheetDateMap){
        long startTime = System.currentTimeMillis();
        log.info("EasyExcel多sheet导出开始:" + exportDataMapList.size() + " | " + loginMap);
        
        try {
            //获取第一个sheet的block，并且不能为空
            ExportCol exportCol = EasyExcelUtil.parseExportCol(inInfo);
            log.info("第一个sheet配置 - 标题列: " + exportCol.getTitleList() + ", 数据列: " + exportCol.getTitleColList());

            //子sheet的 block
            List<Map<String,ExportCol>> exportColListMap = new ArrayList<>();
            //获取多个子项sheet的block
            Map<String,EiBlock> exportColumnBlocks = inInfo.getBlocks();
            Map<String,String> sheetNameMap = new HashMap();
            sheetNameMap.put("exportColumnBlock", (String) exportColumnBlocks.get("exportColumnBlock").get("sheetName"));
            TreeMap<String,ExportCol> exportColsheetMap = new TreeMap<>();
            for (String key :exportColumnBlocks.keySet()){
                if(key.contains("exportColumnBlockSheet")){
                    Map exportColMap = new HashMap();
                    ExportCol exportCol1 = EasyExcelUtil.parseExportMutiCol(exportColumnBlocks.get(key));
                    exportColMap.put(key.replace("exportColumnBlockSheet",""),exportCol1);
                    exportColsheetMap.put(key.replace("exportColumnBlockSheet",""),exportCol1);
                    sheetNameMap.put(key.replace("exportColumnBlockSheet",""), (String) exportColumnBlocks.get(key).get("sheetName"));
                    exportColListMap.add(exportColMap);
                    log.info("子sheet配置 - " + key.replace("exportColumnBlockSheet","") + " - 标题列: " + exportCol1.getTitleList() + ", 数据列: " + exportCol1.getTitleColList());
                }
            }
            
            // 调用调试方法
            debugExportData(exportDataMapList, sheetDateMap, exportCol, exportColsheetMap);
            
            //第一页的sheet
            List<List<Object>> dataList = EasyExcelUtil.listMap2ListObj(exportDataMapList, exportCol.getTitleColList());
            log.info("第一个sheet数据转换完成，原始数据行数: " + exportDataMapList.size() + ", 转换后数据行数: " + dataList.size());

            TreeMap<String,List<List<Object>>> sheetExprotMap = new TreeMap<>();
            //处理子sheet
            for (Map<String,ExportCol> exportColMap : exportColListMap){
                String keyset = exportColMap.keySet().iterator().next();
                ExportCol exportCol1 = exportColMap.get(keyset);
                List<Map<String, Object>> sheetData = (List<Map<String, Object>>) sheetDateMap.get(keyset);
                log.info("处理子sheet: " + keyset + ", 原始数据行数: " + (sheetData != null ? sheetData.size() : 0));
                if (sheetData != null && !sheetData.isEmpty()) {
                    List<List<Object>> dataList1 = EasyExcelUtil.listMap2ListObj(sheetData, exportCol1.getTitleColList());
                    sheetExprotMap.put(keyset,dataList1);
                    log.info("处理子sheet: " + keyset + ", 数据行数: " + dataList1.size());
                } else {
                    log.warn("子sheet数据为空: " + keyset);
                }
            }

            ByteArrayOutputStream bos = EasyExcelUtil.createWorkbookStreamMutiSheet(exportCol.getTitleList(), dataList,sheetExprotMap,exportColsheetMap,sheetNameMap);
            FileItem fileItem = EasyExcelExportUtil.createFileItem(new ByteArrayInputStream(bos.toByteArray()), exportCol.getFileName());
            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            Map<String, String> docMap = new HashMap<>();
            String downloadUrl = "";
            try{
                downloadUrl =  FileUtils.uploadFileByOriginalName(new CommonsMultipartFile(fileItem), "/pageDownload");
            }catch (Exception e){
                log.error("文件上传失败", e);
                throw new RuntimeException("文件生成失败: " + e.getMessage(), e);
            }
            docMap.put("docUrl",downloadUrl);
            log.info("EasyExcel多sheet导出结束:" + docMap + ", ct:" + (System.currentTimeMillis() - startTime) / 1000d + "s");
            return docMap;
        } catch (Exception e) {
            log.error("EasyExcel多sheet导出失败", e);
            throw new RuntimeException("Excel导出失败: " + e.getMessage(), e);
        }
    }

    public static ExportCol parseExportCol(EiInfo inInfo){
        // 获取导出列
        EiBlock exportColumnBlock = inInfo.getBlock("exportColumnBlock");
        if (exportColumnBlock == null){
            throw new RuntimeException("导出列未配置");
        }
        String fileName = exportColumnBlock.getString("fileName");
        List<Map<String, Object>> columns = (List<Map<String, Object>>) exportColumnBlock.get("columns");
        columns.sort( (o1, o2) -> MapUtils.getInteger(o1, "idx").compareTo(MapUtils.getInteger(o2, "idx")) );
        List<String> titleList = new ArrayList<>();
        List<String> titleColList = new ArrayList<>();
        for (Map<String, Object> column : columns) {
            String colName = MapUtils.getString(column, "column");
            String title = MapUtils.getString(column, "title");
            titleList.add(title);
            titleColList.add(colName);
        }
        return new ExportCol(titleList, titleColList, fileName);
    }

    /**
     * 多个sheet页
     * @param eiBlock
     * @return
     */
    public static ExportCol parseExportMutiCol(EiBlock eiBlock){
        // 获取导出列
        //第一个的block为第一个sheet页
        if (eiBlock == null){
            throw new RuntimeException("导出列未配置");
        }
        List<Map<String, Object>> columns = (List<Map<String, Object>>) eiBlock.get("columns");
        columns.sort( (o1, o2) -> MapUtils.getInteger(o1, "idx").compareTo(MapUtils.getInteger(o2, "idx")) );
        List<String> titleList = new ArrayList<>();
        List<String> titleColList = new ArrayList<>();
        for (Map<String, Object> column : columns) {
            String colName = MapUtils.getString(column, "column");
            String title = MapUtils.getString(column, "title");
            titleList.add(title);
            titleColList.add(colName);
        }
        return new ExportCol(titleList, titleColList);
    }

    /**
     * @param listMap      数据集合
     * @param titleColList 要导出的属性
     * @return 要导出的 value 集合
     */
    public static List<List<Object>> listMap2ListObj(List<Map<String, Object>> listMap, List<String> titleColList) {
        List<List<Object>> dataList = new ArrayList<>();
        if (listMap == null || listMap.size() == 0 || titleColList == null || titleColList.size() == 0){
            log.warn("数据转换参数为空 - listMap: " + (listMap == null ? "null" : listMap.size()) + 
                    ", titleColList: " + (titleColList == null ? "null" : titleColList.size()));
            return dataList;
        }
        
        log.info("开始数据转换 - 原始数据行数: " + listMap.size() + ", 导出列数: " + titleColList.size() + ", 导出列: " + titleColList);
        
        for (int i = 0; i < listMap.size(); i++) {
            Map<String, Object> map = listMap.get(i);
            List<Object> dataRow = new ArrayList<>();
            for (String col : titleColList) {
                Object value = map.get(col);
                dataRow.add(value);
                if (value == null) {
                    log.debug("第" + (i+1) + "行，列'" + col + "'值为null");
                }
            }
            dataList.add(dataRow);
            
            // 每处理100行记录一次日志
            if ((i + 1) % 100 == 0) {
                log.info("已处理 " + (i + 1) + " 行数据");
            }
        }
        
        log.info("数据转换完成 - 转换后数据行数: " + dataList.size());
        return dataList;
    }


    /**
     * @param headList    excel 头行
     * @param dataList    数据集合
     * @param sheetName   sheet 名称
     * @param outFilePath 输出文件位置
     */
    public static void createWorkbook(List<String> headList, List<List<Object>> dataList, String sheetName, String outFilePath) {
        // 构造表头
        List<List<String>> head = new ArrayList<>();
        for (String s : headList) {
            head.add(Collections.singletonList(s));
        }
        // 数据
        ExcelWriter excelWriter = EasyExcel
                .write(outFilePath)
                .head(head)
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        excelWriter.write(dataList, writeSheet);
        excelWriter.finish();
    }

    public static ByteArrayOutputStream createWorkbookStream(List<String> headList, List<List<Object>> dataList) {
        return createWorkbookStream(headList, dataList, "Sheet1");
    }

    public static ByteArrayOutputStream createWorkbookStream(List<String> headList, List<List<Object>> dataList, String sheetName) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            // 构造表头
            List<List<String>> firstHead = new ArrayList<>();
            for (String s : headList) {
                firstHead.add(Collections.singletonList(s));
            }
            // 数据
            ExcelWriter excelWriter = EasyExcel
                    .write(bos)
                    .head(firstHead)
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
            excelWriter.write(dataList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            try {
                bos.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            throw e;
        }
        return bos;
    }


    /**
     * 导出多个sheet
     * @param headList
     * @param dataList
     * @return
     */
    public static ByteArrayOutputStream createWorkbookStreamMutiSheet(List<String> headList, List<List<Object>> dataList,Map<String,List<List<Object>>> sheetMap,Map<String,ExportCol> sheetHeadMap,Map sheetNameMap) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            log.info("开始创建多sheet Excel文件 - 第一个sheet标题数: " + headList.size() + ", 数据行数: " + dataList.size());
            
            // 构造首页表头
            List<List<String>> firstHead = new ArrayList<>();
            for (String s : headList) {
                firstHead.add(Collections.singletonList(s));
            }

            // 数据
            ExcelWriter excelWriter = EasyExcel.write(bos).build();
            String sheetName = sheetNameMap.get("exportColumnBlock").toString();
            // 第一个sheet
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(firstHead).build();
            excelWriter.write(dataList, writeSheet);
            log.info("第一个sheet写入完成 - 数据行数: " + dataList.size());
            
            // 处理其他sheet
            int sheetIndex = 2;
            for(Map.Entry<String,List<List<Object>>> entry:sheetMap.entrySet()){
                String key = entry.getKey();
                ExportCol exportCol = sheetHeadMap.get(key);
                if (exportCol != null && exportCol.getTitleList() != null) {
                    List<List<String>> sheetHead = new ArrayList<>();
                    for (String s : exportCol.getTitleList()) {
                        sheetHead.add(Collections.singletonList(s));
                    }
                    
                    String sheetPageName = sheetNameMap.get(key).toString();
                    WriteSheet writeSheets = EasyExcel.writerSheet(sheetPageName).head(sheetHead).build();
                    excelWriter.write(entry.getValue(), writeSheets);
                    log.info("子sheet '" + sheetPageName + "' 写入完成 - 数据行数: " + entry.getValue().size());
                    sheetIndex++;
                } else {
                    log.warn("子sheet配置无效 - key: " + key + ", exportCol: " + (exportCol == null ? "null" : "titleList为null"));
                }
            }
            excelWriter.finish();
            log.info("Excel文件创建完成，总sheet数: " + sheetIndex);
        } catch (Exception e) {
            try {
                bos.close();
            } catch (Exception ex) {
                log.error("关闭ByteArrayOutputStream失败", ex);
            }
            log.error("创建多sheet Excel文件失败", e);
            throw new RuntimeException("创建Excel文件失败: " + e.getMessage(), e);
        }
        return bos;
    }

    /**
     * 调试方法：检查数据源和配置信息
     * @param exportDataMapList 主数据源
     * @param sheetDateMap 子sheet数据源
     * @param exportCol 主sheet配置
     * @param exportColsheetMap 子sheet配置
     */
    public static void debugExportData(List<Map<String, Object>> exportDataMapList, Map<String,List> sheetDateMap, 
                                     ExportCol exportCol, Map<String,ExportCol> exportColsheetMap) {
        log.info("=== 导出数据调试信息 ===");
        log.info("主数据源行数: " + (exportDataMapList != null ? exportDataMapList.size() : 0));
        if (exportDataMapList != null && !exportDataMapList.isEmpty()) {
            log.info("主数据源第一行数据: " + exportDataMapList.get(0));
            log.info("主数据源所有字段: " + exportDataMapList.get(0).keySet());
        }
        
        log.info("主sheet配置 - 标题列: " + exportCol.getTitleList());
        log.info("主sheet配置 - 数据列: " + exportCol.getTitleColList());
        
        log.info("子sheet数据源信息:");
        if (sheetDateMap != null) {
            for (Map.Entry<String, List> entry : sheetDateMap.entrySet()) {
                log.info("  " + entry.getKey() + ": " + (entry.getValue() != null ? entry.getValue().size() : 0) + " 行");
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    log.info("    第一行数据: " + entry.getValue().get(0));
                }
            }
        }
        
        log.info("子sheet配置信息:");
        if (exportColsheetMap != null) {
            for (Map.Entry<String, ExportCol> entry : exportColsheetMap.entrySet()) {
                log.info("  " + entry.getKey() + ": 标题列=" + entry.getValue().getTitleList() + ", 数据列=" + entry.getValue().getTitleColList());
            }
        }
        log.info("=== 调试信息结束 ===");
    }

    /**
     * @param workbookPath xlsx 位置
     * @param sheetName    sheet 名字 eg.Sheet1
     */
    public static List<Map<Integer, Object>> readWorkbook(String workbookPath, String sheetName) {
        List<Map<Integer, Object>> dataList = new ArrayList<>();
        ZipSecureFile.setMaxEntrySize(Integer.MAX_VALUE);
        EasyExcel.read(new File(workbookPath), new AnalysisEventListener<Map<Integer, Object>>() {
                    @Override
                    public void invoke(Map<Integer, Object> rowData, AnalysisContext analysisContext) {
                        dataList.add(rowData);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        int i = 1;
                    }
                })
                .excelType(ExcelTypeEnum.XLSX)
                .sheet(sheetName)
                .doRead();
        return dataList;
    }

    public static class ExportCol{
        private List<String> titleList;
        private List<String> titleColList;
        private String fileName;

        public ExportCol(List<String> titleList, List<String> titleColList) {
            this.titleList = titleList;
            this.titleColList = titleColList;
        }

        public ExportCol(List<String> titleList, List<String> titleColList, String fileName) {
            this.titleList = titleList;
            this.titleColList = titleColList;
            this.fileName = fileName;
        }

        public List<String> getTitleList() {
            return titleList;
        }

        public List<String> getTitleColList() {
            return titleColList;
        }

        public String getFileName() {
            if (fileName == null){
                return "export.xlsx";
            }
            return fileName;
        }
    }

}
