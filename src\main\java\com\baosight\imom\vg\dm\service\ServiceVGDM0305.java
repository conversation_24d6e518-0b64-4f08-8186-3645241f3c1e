package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0305;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * <AUTHOR> yzj
 * @Description : 离线时间查询
 * @Date : 2024/11/21
 * @Version : 1.0
 */
public class ServiceVGDM0305 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0305.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0305().eiMetadata);
        // 业务单元
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0305.QUERY_OFFLINE);
//        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0305.QUERY, new VGDM0305());
    }
}
