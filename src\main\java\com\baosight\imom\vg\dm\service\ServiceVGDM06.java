package com.baosight.imom.vg.dm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.common.websocket.ClientManager;
import com.baosight.imom.common.websocket.TagAlarmClient;
import com.baosight.imom.common.websocket.TagRefreshClient;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.imom.xt.ss.domain.XTSS04;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.MessageCodeConstant;

import org.java_websocket.client.WebSocketClient;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;


/**
 * <AUTHOR> yzj
 * @Description : 设备报警履历页面后台
 * @Date : 2024/8/26
 * @Version : 1.0
 */
public class ServiceVGDM06 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM06.class);
    private final ClientManager clientManager = PlatApplicationContext.getApplicationContext()
            .getBean(ClientManager.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0601().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0601.QUERY, new VGDM0601());
    }

    /**
     * 确认报警
     *
     * <p>更新设备报警信息表中确认状态、确认人相关信息.
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0601 vgdm0601;
            VGDM0601 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0601 = new VGDM0601();
                vgdm0601.fromMap(block.getRow(i));
                // 校验未确认状态
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0601, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_UN_CONFIRM_STATUS, "未确认");
                // 更新确认状态
                dbData.setConfirmStatus("已确认");
                dbData.setConfirmor(UserSession.getLoginName());
                dbData.setConfirmorName(UserSession.getLoginCName());
                dbData.setConfirmTime(DateUtil.curDateTimeStr14());
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0601.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CONFIRM);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 接收报警信息
     *
     * <p>接收BA通过websocket发送的报警信息，
     * websocket连接时设置BA只推送未确认状态的数据.
     */
    public EiInfo receiveWsData(EiInfo inInfo) {
        try {
            JSONObject jsonObject = (JSONObject) inInfo.get("data");
            JSONArray array = jsonObject.getJSONArray("rows");
            if (CollectionUtils.isEmpty(array)) {
                log("receiveWsData无可用数据");
                return inInfo;
            }
            JSONObject object;
            VGDM0601 alarmData;
            VGDM0601 dbData;
            VGDM0301 tagInfo;
            String time = DateUtil.curDateTimeStr14();
            // 已恢复数据，需自动确认
            List<VGDM0601> confirmList = new ArrayList<>();
            // 新增报警list
            List<Map> insertList = new ArrayList<>();
            // 修改报警list
            List<Map> updateList = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                object = array.getJSONObject(i);
                alarmData = object.toJavaObject(VGDM0601.class);
                // 返回参数中的报警id字段与数据库不一致
                alarmData.setAlarmId(object.getString("alarmID"));
                log("receiveWsData收到报警id:" + alarmData.getAlarmId() + "点位：" + alarmData.getAlarmTag());
                alarmData.setScadaName(object.getString("nodeName"));
                // 查询tag信息用于获取业务单元信息
                tagInfo = VGDM0301.queryWithCache(dao, alarmData.getAlarmTag());
                if (tagInfo == null) {
                    logError("receiveWsData:点位不存在", "接收到报警信息中tag不存在");
                    continue;
                }
                // 根据报警id判断数据库是否有数据，有则修改无则新增
                String dbAlarmId = tagInfo.getSegNo().substring(0, 2) + alarmData.getAlarmId();
                alarmData.setAlarmId(dbAlarmId);
                dbData = VGDM0601.queryByAlarmId(dao, dbAlarmId);
                if (dbData != null) {
                    log("receiveWsData数据库已有");
                    // 数据库已有且未恢复的数据不处理
//                    if ("未恢复".equals(alarmData.getAlarmState())) {
//                        log("receiveWsData未恢复不处理");
//                        continue;
//                    }
                    alarmData.setSegNo(dbData.getSegNo());
                    alarmData.setUnitCode(dbData.getUnitCode());
                    alarmData.setUuid(dbData.getUuid());
                    alarmData.setRecReviseTime(time);
                    alarmData.setRecRevisor(MesConstant.APP_CODE);
                    alarmData.setRecRevisorName(MesConstant.APP_CODE);
                    updateList.add(alarmData.toMap());
                } else {
                    // 新增报警-来自tag信息
                    alarmData.copyFromAlarm(tagInfo);
                    alarmData.setConfirmStatus("未确认");
                    alarmData.setConfirmor(" ");
                    alarmData.setConfirmTime(" ");
                    alarmData.setConfirmorName(" ");
                    // 其他信息
                    alarmData.setUuid(UUIDUtils.getUUID());
                    log("receiveWsData新增报警:" + alarmData.getUuid());
                    alarmData.setRecCreateTime(time);
                    alarmData.setRecCreator(MesConstant.APP_CODE);
                    alarmData.setRecCreatorName(MesConstant.APP_CODE);
                    insertList.add(alarmData.toMap());
                }
                // 未确认已恢复的报警自动确认防止重复推送报警信息
                if ("已恢复".equals(alarmData.getAlarmState())) {
                    log("receiveWsData已恢复，准备自动确认");
                    confirmList.add(alarmData);
                }
            }
            DaoUtils.insertBatch(dao, VGDM0601.INSERT, insertList);
            DaoUtils.updateBatch(dao, VGDM0601.UPDATE_ALARM, updateList);
            // 自动确认报警信息
            IplatUtils.confirmAlarm(confirmList, true);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 批量发送短信
     *
     * @param alarmDataList 报警数据列表
     * @param msgTemplateId 短信模板id
     */
    private void sendMessageBatch(List<Map> alarmDataList, String msgTemplateId) {
        if (CollectionUtils.isEmpty(alarmDataList)) {
            LOGGER.info("报警数据列表为空,不发送短信");
            log("报警数据列表为空,不发送短信");
            return;
        }
        String segNo = MapUtils.getStr(alarmDataList.get(0), "segNo");
        String switchValue = (new SwitchUtils()).getProcessSwitchValue(segNo, "IF_ALARM_MESSAGE", dao);
        log("短信配置开关：" + switchValue);
        if (!"1".equals(switchValue)) {
            return;
        }
        log("准备发送设备报警短信，模板：" + msgTemplateId);
        // 循环发送短信
        for (Map alarmMap : alarmDataList) {
            VGDM0601 alarmData = new VGDM0601();
            alarmData.fromMap(alarmMap);
            HashMap paramMap = createMsgParam(alarmData);
            // 调用接口发送短信
            MessageUtils.sendMessage(paramMap, msgTemplateId);
        }
    }

    /**
     * 创建短信参数
     *
     * @param alarmData 报警数据
     * @return HashMap
     */
    private HashMap createMsgParam(VGDM0601 alarmData) {
        HashMap paramMap = new HashMap<>();
        // 接收人手机号
        paramMap.put("param1", alarmData.getAlarmAddress());
        log("发送短信至：" + alarmData.getAlarmAddress());
        // 接收人姓名
        paramMap.put("param2", alarmData.getScadaName());
        // 其他参数-一直到param10
        paramMap.put("param3", alarmData.getAlarmId());
        paramMap.put("param4", alarmData.getEArchivesNo());
        paramMap.put("param5", alarmData.getEquipmentName());
        paramMap.put("param6", alarmData.getAlarmTagDesc());
        paramMap.put("param7", alarmData.getAlarmType());
        return paramMap;
    }


    /**
     * 自动发送短信-设备管理人员
     */
    public EiInfo autoPushHandle(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            log("传入参数：" + segNo);
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            List<Map> list = dao.query(VGDM0601.QUERY_FOR_PUSH, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                log("无待发送数据");
                return inInfo;
            }
            List<Map> sendList = new ArrayList<>();
            for (Map alarmMap : list) {
                // 校验是否发送短信
                VGDM0601 alarm = this.checkPushHandle(alarmMap);
                if (alarm != null) {
                    alarm.setPushHandleFlag("1");
                    alarm.setPushHandleTime(DateUtil.curDateTimeStr14());
                    Map updMap = alarm.toMap();
                    RecordUtils.setRevisorSys(updMap);
                    sendList.add(updMap);
                }
            }
            // 批量更新发送标记
            DaoUtils.updateBatch(dao, VGDM0601.UPDATE_PUSH, sendList);
            // 调用imc配置短信模板发送管理人短信
            this.sendMessageBatch(sendList, "MT0000001017");
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 自动发送短信-设备管理人员
     */
    public EiInfo autoPushManage(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            log("传入参数：" + segNo);
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            // 查询待通知管理人数据
            List<Map> manageList = dao.query(VGDM0601.QUERY_FOR_MANAGE_PUSH, queryMap);
            if (CollectionUtils.isEmpty(manageList)) {
                return inInfo;
            }
            // 更新发送标记
            for (Map alarmMap : manageList) {
                alarmMap.put("pushManageFlag", "1");
                alarmMap.put("pushManageTime", DateUtil.curDateTimeStr14());
                RecordUtils.setRevisorSys(alarmMap);
            }
            DaoUtils.updateBatch(dao, VGDM0601.UPDATE_PUSH, manageList);
            // 调用imc配置短信模板发送管理人短信
            this.sendMessageBatch(manageList, "MT0000001017");
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 校验是否需发送处理人
     *
     * @param alarmMap 数据
     * @return 是则返回报警信息否则返回null
     */
    private VGDM0601 checkPushHandle(Map alarmMap) {
        // 报警信息
        VGDM0601 alarm = new VGDM0601();
        alarm.fromMap(alarmMap);
        // 通知配置
        VGDM0603 config = new VGDM0603();
        config.fromMap(alarmMap);
        // 通知规则
        VGDM0604 rule = new VGDM0604();
        rule.fromMap(alarmMap);
        if ("10".equals(rule.getHandleType())) {
            log("机械相关");
            alarm.setAlarmAddress(config.getMachineryHandleMobile());
            alarm.setScadaName(config.getMachineryHandleName());
        } else if ("20".equals(rule.getHandleType())) {
            log("电气相关");
            // 借用报警地址存储手机号
            alarm.setAlarmAddress(config.getHandleMobile());
            // 借用scada存储姓名
            alarm.setScadaName(config.getHandleUserName());
        } else {
            log("未知处理人类型：" + rule.getHandleType());
            return null;
        }
        // 报警时间
        log("报警信息：" + alarm.getAlarmId() + "报警时间：" + alarm.getOccurTime() + "重复次数：" + alarm.getRepeatCount());
        if ("10".equals(rule.getPushConfigType())) {
            log("按持续时间规则值：" + rule.getPushConfigValue());
            // 计算now和alarmTime的分钟差
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime alarmTime = LocalDateTime.parse(alarm.getOccurTime(), DateUtils.FORMATTER_23);
            long diffMinutes = ChronoUnit.MINUTES.between(alarmTime, now);
            log("时间差（分钟）：" + diffMinutes);
            if (diffMinutes >= rule.getPushConfigValue()) {
                log("满足时间要求，准备发送短信");
                return alarm;
            }
        } else if ("20".equals(rule.getPushConfigType())) {
            log("按重复次数规则值：" + rule.getPushConfigValue());
            if (alarm.getRepeatCount() >= rule.getPushConfigValue()) {
                log("满足重复次数要求，准备发送短信");
                return alarm;
            }
        } else {
            log("不支持的规则类型：" + rule.getPushConfigType());
        }
        return null;
    }


    /**
     * 获取前端传入的业务单元代码前缀（前2位）
     * 校验业务单元代码和客户端类型是否正确
     */
    private String getSegNoPrefix(EiInfo inInfo) {
        // 获取业务单元代码
        String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
        if (StrUtil.isBlank(segNo)) {
            throw new PlatException("业务单元代码不能为空");
        }
        // 获取客户端类型
        String clientType = inInfo.getString(ClientManager.ClientField.CLIENT_TYPE);
        if (StrUtil.isBlank(clientType)) {
            throw new PlatException("客户端类型不能为空");
        }
        if (!ClientManager.ClientType.ALARM_CLIENT.equals(clientType)
                && !ClientManager.ClientType.REFRESH_CLIENT.equals(clientType)) {
            throw new PlatException("客户端类型错误:" + clientType);
        }
        // 返回业务单元代码前缀
        return segNo.substring(0, 2);
    }

    /**
     * 查看监听状态
     */
    public EiInfo queryClientStatus(EiInfo inInfo) {
        try {
            // 获取业务单元代码前缀
            String segNoPrefix = this.getSegNoPrefix(inInfo);
            // 获取客户端类型
            String clientType = inInfo.getString(ClientManager.ClientField.CLIENT_TYPE);
            // 获取客户端
            WebSocketClient client = clientManager.getClient(segNoPrefix + clientType);
            // 设置监听状态
            String clientStatus = ClientManager.ClientStatus.STOP_STATUS;
            if (client != null && client.isOpen()) {
                // 设置监听状态
                clientStatus = ClientManager.ClientStatus.START_STATUS;
            }
            // 返回监听状态
            inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, ClientManager.ClientField.CLIENT_STATUS, clientStatus);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 开始监听
     */
    public EiInfo startClient(EiInfo inInfo) {
        try {
            // 获取业务单元代码前缀
            String segNoPrefix = this.getSegNoPrefix(inInfo);
            // 获取客户端类型
            String clientType = inInfo.getString(ClientManager.ClientField.CLIENT_TYPE);
            // 移除现有连接
            clientManager.removeClient(segNoPrefix + clientType);
            // 创建新的连接
            WebSocketClient client = null;
            if (clientType.equals(ClientManager.ClientType.ALARM_CLIENT)) {
                // 创建报警客户端
                client = TagAlarmClient.newClient(segNoPrefix);
            } else if (clientType.equals(ClientManager.ClientType.REFRESH_CLIENT)) {
                // 创建数据采集客户端
                client = TagRefreshClient.newClient(segNoPrefix);
            } else {
                throw new PlatException("客户端类型错误:" + clientType);
            }
            // 连接
            client.connect();
            // 缓存新的连接
            clientManager.addClient(segNoPrefix + clientType, client);
            // 返回监听状态
            inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, ClientManager.ClientField.CLIENT_STATUS, ClientManager.ClientStatus.START_STATUS);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg("开始监听成功");
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 停止监听
     */
    public EiInfo stopClient(EiInfo inInfo) {
        try {
            // 获取业务单元代码前缀
            String segNoPrefix = this.getSegNoPrefix(inInfo);
            // 获取客户端类型
            String clientType = inInfo.getString(ClientManager.ClientField.CLIENT_TYPE);
            // 移除客户端
            clientManager.removeClient(segNoPrefix + clientType);
            // 返回监听状态
            inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, ClientManager.ClientField.CLIENT_STATUS, ClientManager.ClientStatus.STOP_STATUS);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg("停止监听成功");
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 自动开始监听
     */
    public EiInfo autoStartClient(EiInfo inInfo) {
        try {
            LOGGER.log("autoStartClient自动开始报警监听-------------------");
            log("autoStartClient自动开始报警监听-------------------");
            // 获取业务单元代码前缀
            Map<String, String> map = new HashMap<>();
            map.put("processSwitchName", "IS_AUTO_OPEN_ALARM");
            List<Map> list = dao.query(XTSS04.QUERY_LIST, map);
            if (CollectionUtils.isEmpty(list)) {
                LOGGER.log("autoStartClient没有找到报警监听开关配置");
                log("autoStartClient没有找到报警监听开关配置");
                return inInfo;
            }
            for (Map switchMap : list) {
                String segNo = MapUtils.getStr(switchMap, "segNo");
                LOGGER.log("autoStartClient准备启动报警监听，业务单元代码：" + segNo);
                log("autoStartClient准备启动报警监听，业务单元代码：" + segNo);
                // 获取业务单元代码前缀
                String segNoPrefix = segNo.substring(0, 2);
                // 移除现有连接
                clientManager.removeClient(segNoPrefix + ClientManager.ClientType.ALARM_CLIENT);
                // 获取客户端
                WebSocketClient client = TagAlarmClient.newClient(segNoPrefix);
                client.connect();
                // 缓存新的连接
                clientManager.addClient(segNoPrefix + ClientManager.ClientType.ALARM_CLIENT, client);
                LOGGER.log("autoStartClient业务单元代码：" + segNo + "，报警监听开启成功");
                log("autoStartClient业务单元代码：" + segNo + "，报警监听开启成功");
            }
            LOGGER.log("autoStartClient开始报警监听完成---------------");
            log("autoStartClient开始报警监听完成---------------");
            // 返回状态
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 故障生成
     */
    public EiInfo addFault(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0601 frontData;
            VGDM0601 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0601();
                frontData.fromMap(block.getRow(i));
                // 校验已确认状态
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CONFIRM_STATUS, "已确认");
                // 校验是否已生成故障
                if (!"0".equals(dbData.getIsFault())) {
                    throw new PlatException("故障已生成，无法重复生成");
                }
                // 更新故障标记
                dbData.setIsFault("1");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
                // 生成故障信息
                VGDM0701 vgdm0701 = new VGDM0701();
                vgdm0701.fromMap(updMap);
                vgdm0701.setVoucherNum(dbData.getAlarmId());
                vgdm0701.setFaultDesc(dbData.getAlarmTagDesc() + dbData.getAlarmType());
                vgdm0701.setFaultStartTime(dbData.getOccurTime()
                        .replaceAll("[\\-: ]", "")
                        .substring(0, 14));
                if (!StrUtil.isBlank(dbData.getRecoverTime())) {
                    vgdm0701.setFaultEndTime(dbData.getRecoverTime()
                            .replaceAll("[\\-: ]", "")
                            .substring(0, 14));
                }
                vgdm0701.setFaultSource("2");
                vgdm0701.insertData(dao);
            }
            // 更新故障标记
            DaoUtils.updateBatch(dao, VGDM0601.UPDATE, block.getRows());
            // 返回前端
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 加入设备履历
     */
    public EiInfo addResume(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0601 frontData;
            VGDM0601 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0601();
                frontData.fromMap(block.getRow(i));
                // 校验已确认状态
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CONFIRM_STATUS, "已确认");
                // 数据返回前端
                block.getRows().set(i, dbData.toMap());
            }
            // 加入设备履历
            VGDM0104.addHistory(block, VGDM0104.RelevanceType.ALARM);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_ADD_RESUME);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 设备离线短信提醒
     */
    public EiInfo autoDeviceStatusCheck(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            String messageFlag = inInfo.getString("messageFlag");
            if (StrUtil.isBlank(messageFlag)) {
                messageFlag = "0";
            }
            log("传入参数：" + segNo + "|" + messageFlag);
            // 查询scada信息
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            List<VGDM0302> list = dao.query(VGDM0302.QUERY, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException(segNo + "无scada配置信息");
            }
            // 查询设备连接状态
            VGDM0302 scada = list.get(0);
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("scadaName", scada.getScadaName());
            eiInfo.set("scadaIp", scada.getScadaPrimaryIp());
            eiInfo.set(EiConstant.serviceId, "S_BI_DX_36");
            EiInfo rtnInfo = XServiceManager.call(eiInfo);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            String returnValue = rtnInfo.getString("result");
            log(scada.getScadaName() + "设备状态：" + returnValue);
            JSONArray jsonArray = JSON.parseArray(returnValue);
            List<String> deviceNameList = new ArrayList<>();
            List<Map> insList = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String deviceName = jsonObject.getString("name");
                int deviceStatus = jsonObject.getIntValue("status");
                log(deviceName + "|" + deviceStatus);
                if (deviceStatus == 0) {
                    log("设备" + deviceName + "离线");
                    deviceNameList.add(deviceName);
                }
                VGDM0305 vgdm0305 = new VGDM0305();
                vgdm0305.setSegNo(segNo);
                vgdm0305.setUnitCode(segNo);
                vgdm0305.setDeviceId(deviceName);
                vgdm0305.setDeviceStatus(String.valueOf(deviceStatus));
                Map insMap = vgdm0305.toMap();
                RecordUtils.setCreatorSys(insMap);
                insList.add(insMap);
            }
            DaoUtils.insertBatch(dao, VGDM0305.INSERT, insList);
            if (CollectionUtils.isEmpty(deviceNameList)) {
                inInfo.setMsg("无离线设备");
                return inInfo;
            }
            String switchValue = (new SwitchUtils()).getProcessSwitchValue(segNo, "IF_OFFLINE_MESSAGE", dao);
            log("离线通知配置开关：" + switchValue + "|" + messageFlag);
            if (!"1".equals(switchValue) || !"1".equals(messageFlag)) {
                inInfo.setMsg("开关未开启");
                return inInfo;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("segNo", segNo);
            map.put("deviceIdList", deviceNameList);
            List mapList = dao.query(VGDM0304.QUERY_FOR_ALARM, map);
            if (CollectionUtils.isEmpty(mapList)) {
                inInfo.setMsg("未找到离线设备对应处理人配置");
                return inInfo;
            }
            // 构建短信通知
            Map<String, Map> mobileMap = new HashMap<>();
            for (int i = 0; i < mapList.size(); i++) {
                Map map1 = (Map) mapList.get(i);
                String handleMobile = map1.get("handleMobile").toString();
                String handleUserName = map1.get("handleUserName").toString();
                String equipmentName = map1.get("equipmentName").toString();
                String deviceName = map1.get("deviceName").toString();
                // 短信通知参数
                Map paramMap;
                if (mobileMap.containsKey(handleMobile)) {
                    paramMap = mobileMap.get(handleMobile);
                    paramMap.put("param3", paramMap.get("param3") + "," + equipmentName + deviceName);
                } else {
                    paramMap = new HashMap<>();
                    paramMap.put("param1", handleMobile);
                    paramMap.put("param2", handleUserName);
                    paramMap.put("param3", equipmentName + deviceName);
                    mobileMap.put(handleMobile, paramMap);
                }
            }
            for (Map.Entry<String, Map> entry : mobileMap.entrySet()) {
                String msg = entry.getValue().get("param3") + "已离线，请及时处理";
                String mobile = entry.getKey();
                log(mobile + "|" + msg);
                entry.getValue().put("param3", msg);
                MessageUtils.sendMessage((HashMap) entry.getValue(), "MT0000001025");
            }
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 设备报警恢复
     */
    public EiInfo autoCloseAlarm(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            log("传入参数：" + segNo);
            String segNoPrefix = segNo.substring(0, 2);
            // 查询scada信息
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            List<VGDM0302> list = dao.query(VGDM0302.QUERY, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException(segNo + "无scada配置信息");
            }
            // 查询设备连接状态
            VGDM0302 scada = list.get(0);
            // 查找未恢复报警
            Map<String, Object> map1 = new HashMap<>();
            map1.put("segNo", segNo);
            map1.put("alarmState", "未恢复");
            map1.put("scadaName", scada.getScadaName());
            List<VGDM0601> alarmList = dao.query(VGDM0601.QUERY, map1);
            if (CollectionUtils.isEmpty(alarmList)) {
                inInfo.setMsg("无未恢复报警信息");
                return inInfo;
            }
            log("未恢复报警数量：" + alarmList.size());
            // 拼接查询条件
            Set<String> alarmTagSet = new HashSet<>();
            String startTime = DateUtil.curDateTimeStr14();
            String endTime = "";
            for (VGDM0601 alarmInfo : alarmList) {
                alarmTagSet.add(alarmInfo.getAlarmTag());
                startTime = alarmInfo.getOccurTime().substring(0, 19);
                if (endTime.isEmpty()) {
                    endTime = alarmInfo.getOccurTime().substring(0, 19);
                }
            }
            // 结束时间取最近一条报警时间+8小时
            endTime = LocalDateTime.parse(endTime, DateUtils.FORMATTER_19).plusHours(8).format(DateUtils.FORMATTER_19);
            // 点位按;分割
            String alarmTag = String.join(";", alarmTagSet);
            log("待查询点位：" + alarmTag + "时间：" + startTime + "-" + endTime);
            Map<String, Object> map2 = new HashMap<>();
            map2.put("time_begin", startTime);
            map2.put("time_end", endTime);
            map2.put("tag_name", alarmTag);
            map2.put("alarm_status", "0");
            // 接口参数
            EiInfo info = new EiInfo();
            // scada节点名
            info.set("scadaName", scada.getScadaName());
            // 查询类型
            info.set("queryType", "protobuf");
            // 查询条件
            info.set("queryHisAlmInfo", map2);
            // 查询总报警数量
            info.set("alarmCount", false);
            info.set("fromIndex", 0);
            info.set("totalNumber", 2000);
            info.set(EiConstant.serviceId, IplatUtils.SERVICE_QUERY_ALARM_COUNT + segNoPrefix);
            EiInfo rtnInfo = XServiceManager.call(info);
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            List alarms = (List) rtnInfo.get("alarms");
            // 转换数据结构
            List<VGDM0601> rtnList = IplatUtils.javaBean2Alarm(alarms);
            log("查询结果数量：" + rtnList.size());
            if (CollectionUtils.isEmpty(rtnList)) {
                inInfo.setMsg("无已恢复报警信息");
                return inInfo;
            }
            List<Map> updList = new ArrayList<>();
            for (VGDM0601 rtnAlarm : rtnList) {
                // 获取报警ID
                String alarmId = segNoPrefix + rtnAlarm.getAlarmId();
                for (VGDM0601 alarmInfo : alarmList) {
                    // 报警id相同时更新报警信息
                    if (alarmId.equals(alarmInfo.getAlarmId())) {
                        log("更新：" + alarmInfo.getAlarmId());
                        alarmInfo.setRecoverTagValue(rtnAlarm.getRecoverTagValue());
                        alarmInfo.setRecoverTime(rtnAlarm.getRecoverTime());
                        alarmInfo.setAlarmState(rtnAlarm.getAlarmState());
                        Map updMap = alarmInfo.toMap();
                        updList.add(updMap);
                    }
                }
            }
            // 批量更新
            log("更新数量：" + updList.size());
            DaoUtils.updateBatch(dao, VGDM0601.UPDATE_ALARM, updList);
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
