<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="LIRL0505">
	<sql id="conditionPage">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sendType">
			SEND_TYPE = #sendType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="perNo">
			PER_NO = #perNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="perName">
			PER_NAME = #perName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="perTel">
			PER_TEL = #perTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="timeStart">
			TIME_START = #timeStart#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="timeEnd">
			TIME_END = #timeEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="typeOfHandling">
			TYPE_OF_HANDLING = #typeOfHandling#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="timeoutThreshold">
			TIMEOUT_THRESHOLD = #timeoutThreshold#
		</isNotEmpty>
		<!--创建时间起-->
		<isNotEmpty prepend=" and " property="recCreateTimeStart">
			substr(REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
		</isNotEmpty>
		<!--创建时间止-->
		<isNotEmpty prepend=" and " property="recCreateTimeEnd">
			substr(REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0505">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
		        (select SEG_NAME from ${platSchema}.TVZBM81 t where t.SEG_NO = b.SEG_NO and t.DEL_FLAG = 0) as  "segName",         <!-- 业务单元简称 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
		        SEND_TYPE	as "sendType",  <!-- 发送类型 -->
				PER_NO	as "perNo",  <!-- 人员工号 -->
				PER_NAME	as "perName",  <!-- 人员姓名 -->
		        per_tel	as "perTel",  <!-- 人员电话 -->
		        work_days as "workDays",  <!-- 一周排班天数 -->
				TIME_START	as "timeStart",  <!-- 上班时间 -->
				TIME_END	as "timeEnd",  <!-- 下班时间 -->
		        type_of_handling as "typeOfHandling",  <!-- 业务类型 -->
		        timeout_threshold as "timeoutThreshold",<!-- 超时阈值 -->
		        loading_point_no as "loadingPointNo",  <!-- 装卸点代码 -->
		        loading_point_name as "loadingPointName",  <!-- 装卸点名称 -->
		        door_charge_flag as "doorChargeFlag",  <!-- 门长标记 -->
		        STATUS	as "status",  <!-- 状态 -->
				UUID	as "uuid" <!-- uuid -->
		FROM meli.tlirl0505 b WHERE 1=1
		<include refid="conditionPage"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			LOADING_POINT_NO like concat('%',#targetHandPointId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notTargetHandPointId">
			LOADING_POINT_NO =''
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="TypeOfHandling">
			TYPE_OF_HANDLING = #TypeOfHandling#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="waitingTime">
			TIMEOUT_THRESHOLD = #waitingTime#
		</isNotEmpty>
		<isEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = 0
		</isEmpty>
		<isNotEmpty	prepend="and" property="dayOfWeek">
			current_time between TIME_START and TIME_END
			AND b.WORK_DAYS like concat('%',#dayOfWeek#,'%')
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0505 WHERE 1=1
		<include refid="conditionPage"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
	</select>
	


	<insert id="insert">
		INSERT INTO meli.tlirl0505 (SEG_NO,  <!-- 业务单元代码 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录创建人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
		                                SEND_TYPE,  <!-- 发送类型 -->
										PER_NO,  <!-- 人员工号 -->
										PER_NAME,  <!-- 人员姓名 -->
										PER_TEL,  <!-- 人员电话 -->
		                                WORK_DAYS,  <!-- 一周排班天数 -->
										TIME_START,  <!-- 开始时间 -->
										TIME_END,  <!-- 结束时间 -->
		                                TYPE_OF_HANDLING,  <!-- 业务类型 -->
		                                TIMEOUT_THRESHOLD,  <!-- 超时阈值 -->
		                                LOADING_POINT_NO,  <!-- 装卸点代码 -->
		                                LOADING_POINT_NAME,  <!-- 装卸点名称 -->
		                                DOOR_CHARGE_FLAG,  <!-- 门长标记 -->
		                                STATUS,  <!-- 状态 -->
										UUID  <!-- uuid -->
										)		 
	    VALUES (#segNo#, #unitCode#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sendType#, #perNo#, #perName#, #perTel#,#workDays#, #timeStart#, #timeEnd#,#typeOfHandling#,#timeoutThreshold#,	#loadingPointNo#, #loadingPointName#, #doorChargeFlag#,	#status#, #uuid#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0505 WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0505
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->
		            SEND_TYPE	= #sendType#,   <!-- 发送类型 -->
					PER_NAME	= #perName#,   <!-- 人员姓名 -->
					PER_TEL	= #perTel#,   <!-- 人员电话 -->
		            WORK_DAYS	= #workDays#,   <!-- 一周排班天数 -->
					TIME_START	= #timeStart#,   <!-- 开始时间 -->
					TIME_END	= #timeEnd# ,  <!-- 结束时间 -->
		            TYPE_OF_HANDLING	= #typeOfHandling#,   <!-- 业务类型 -->
		            TIMEOUT_THRESHOLD	= #timeoutThreshold#,   <!-- 超时阈值 -->
		            loading_point_no	=	#loadingPointNo#,   <!-- 装卸点代码 -->
		            loading_point_name	=	#loadingPointName#,	   <!-- 装卸点名称 -->
		            DOOR_CHARGE_FLAG	= #doorChargeFlag#,   <!-- 门长标记 -->
		            STATUS	= #status#  <!-- 状态 -->
						WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>