<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-19 13:34:48
   		Version :  1.0
		tableName :meli.tlirl0313 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR, 
		 CUSTOMER_ID  VARCHAR, 
		 CUSTOMER_NAME  VARCHAR, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 REMARK  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0313">
	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId2">
			CUSTOMER_ID = #customerId2#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName2">
			CUSTOMER_NAME = #customerName2#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0313">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
				(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
				"segName", <!-- 业务单元简称 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				STATUS	as "status",  <!-- 状态 -->
				CUSTOMER_ID	as "customerId",  <!-- 客户代码 -->
				CUSTOMER_NAME	as "customerName",  <!-- 客户名称 -->
				UUID	as "uuid",  <!-- uuid -->
				REMARK	as "remark",  <!-- 备注 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建责任者 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改责任者 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				TENANT_ID	as "tenantId",  <!-- 租户ID -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag" <!-- 删除标记 -->
		FROM meli.tlirl0313 a WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0313 WHERE 1=1
		<include refid="condition"/>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0313 (SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										STATUS,  <!-- 状态 -->
										CUSTOMER_ID,  <!-- 客户代码 -->
										CUSTOMER_NAME,  <!-- 客户名称 -->
										UUID,  <!-- uuid -->
										REMARK,  <!-- 备注 -->
										REC_CREATOR,  <!-- 记录创建责任者 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改责任者 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										TENANT_ID,  <!-- 租户ID -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG  <!-- 删除标记 -->
										)		 
	    VALUES (#segNo#, #unitCode#, #status#, #customerId#, #customerName#, #uuid#, #remark#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0313 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0313 
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					CUSTOMER_ID	= #customerId#,   <!-- 客户代码 -->  
					CUSTOMER_NAME	= #customerName#,   <!-- 客户名称 -->  
								REMARK	= #remark#,   <!-- 备注 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建责任者 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改责任者 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					TENANT_ID	= #tenantId#,   <!-- 租户ID -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#  <!-- 删除标记 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>