<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XSLVOrg">
    <sql id="getLevelManagerResource">
        select a.ORG_ID,a.ORG_ENAME,a.ORG_CNAME,
        case when a.ORG_ID = #orgId# then 'currentNode'
        else 'parentNode' end as NODE_TYPE, tset.NODE_SET
        from ${platSchema}.TXSOG01 a , (select ${platSchema}.queryOrgPath(#orgId#) as NODE_SET from dual) tset
        WHERE FIND_IN_SET(a.ORG_ID, NODE_SET)
    </sql>

    <select id="queryLevelManagerResource" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        <include refid="getLevelManagerResource"/>
        SELECT
        org.ORG_ID as orgId,
        org.ORG_ENAME as orgEname,
        org.ORG_CNAME as orgCname,
        org.NODE_TYPE as nodeType,
        l.OBJECT_TYPE as objectType,
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_ENAME
        ELSE r.RESOURCE_ENAME
        END AS "objectEname",
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_CNAME
        ELSE
        CASE r.TYPE WHEN 'PAGE' THEN f.FORM_CNAME
        WHEN 'BUTTON' THEN b.BUTTON_CNAME
        ELSE r.RESOURCE_ENAME END
        END AS "objectName"
        FROM
        (
            <include refid="getLevelManagerResource"/>
        ) org
        join ${platSchema}.TXSLV02 l on org.ORG_ID = l.ORG_ID
        join ${platSchema}.TXSLV01 lm on lm.ORG_ID = org.ORG_ID AND lm.ORG_ADM_ID = #orgAdmId#
        LEFT OUTER JOIN ${platSchema}.XS_RESOURCE_GROUP rg ON l.OBJECT_ID = rg.ID AND l.OBJECT_TYPE = 'RESOURCE_GROUP'
        LEFT OUTER JOIN ${platSchema}.XS_RESOURCE r ON l.OBJECT_ID = r.ID AND l.OBJECT_TYPE = 'RESOURCE'
        LEFT OUTER JOIN ${platSchema}.TEDFA00 f on r.TYPE = 'PAGE' AND r.RESOURCE_ENAME = f.FORM_ENAME
        LEFT OUTER JOIN ${platSchema}.TEDFA01 b on r.TYPE = 'BUTTON' AND r.RESOURCE_ENAME = b.FORM_ENAME||b.BUTTON_ENAME
        where 1=1
    </select>



    <select id="queryLevelManagerResourceMenu" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        org.ORG_ID as orgId,
        org.ORG_ENAME as orgEname,
        org.ORG_CNAME as orgCname,
        org.NODE_TYPE as nodeType,
        l.OBJECT_TYPE as objectType,
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_ENAME
        ELSE r.RESOURCE_ENAME
        END AS "objectEname",
        CASE l.OBJECT_TYPE WHEN 'RESOURCE_GROUP' THEN rg.RESOURCE_GROUP_CNAME
        ELSE
        CASE r.TYPE WHEN 'PAGE' THEN f.FORM_CNAME
        WHEN 'BUTTON' THEN b.BUTTON_CNAME
        ELSE r.RESOURCE_ENAME
        END AS "objectName"
        FROM
        (
        <include refid="getLevelManagerResource"/>
        ) org join ${platSchema}.TXSLV02 l on org.ORG_ID = l.ORG_ID
        join ${platSchema}.TXSLV01 lm on lm.ORG_ID = org.ORG_ID AND lm.ORG_ADM_ID = #orgAdmId#
        JOIN ${platSchema}.XS_RESOURCE r ON l.OBJECT_ID = r.ID AND l.OBJECT_TYPE = 'RESOURCE'
        LEFT OUTER JOIN ${platSchema}.TEDFA00 f on r.TYPE = 'PAGE' AND r.RESOURCE_ENAME = f.FORM_ENAME
        LEFT OUTER JOIN ${platSchema}.TEDFA01 b on r.TYPE = 'BUTTON' AND r.RESOURCE_ENAME = b.FORM_ENAME||b.BUTTON_ENAME
        where 1=1
    </select>


    <!--查询哪些组织机构是否有管理权限，支持模糊匹配查询，返回有行则有管理权限，否则没有权限-->
    <select id="queryOrgWithLevelPermission" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        org.ORI_ORG_ID as "label",
        org.ORI_ORG_ENAME as "orgEname",
        org.ORI_ORG_CNAME as "text",
        'leaf',
        lm.ORG_ID as "levelParentOrgId",
        lm.ORG_PERM_MANAGER as "orgPermManager",  <!-- 维护分级管理员权限 -->
        lm.ORG_PERM_RES_RANGE as "orgPermResRange",  <!-- 分级资源授权范围管理权限 -->
        lm.ORG_PERM_ORG_MAPPING as "orgPermOrgMapping",  <!-- 组织机构映射权限 -->
        lm.ORG_PERM_USER_GROUP as "orgPermUserGroup",  <!-- 用户组维护权限 -->
        lm.ORG_PERM_USER_GROUP_MEMBRER as "orgPermUserGroupMembrer",  <!-- 用户组成员维护权限 -->
        lm.ORG_PERM_AUTH as "orgPermAuth",  <!-- 授权关系维护权限 -->
        lm.ORG_PERM_ORG as "orgPermOrg" <!-- 组织机构维护权限 -->
        FROM
        (
            select
                a.ORG_ID as ORI_ORG_ID,
                a.ORG_ENAME as ORI_ORG_ENAME,
                a.ORG_CNAME as ORI_ORG_CNAME,
                ${platSchema}.queryOrgPath(a.ORG_ID) as NODE_SET
            from  ${platSchema}.TXSOG01 a where 1=1
        <isNotEmpty prepend=" AND " property="orgCname">
            a.ORG_CNAME LIKE ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgEname">
            a.ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            a.ORG_ID = #orgId#
        </isNotEmpty>
        )
        org , ${platSchema}.TXSLV01 lm
        where find_in_set(lm.org_id,org.NODE_SET)
        AND lm.ORG_ADM_ID = #userId#
        <!-- 分级管理员、分级授权资源范围、组织机构维护权限只能划分到子组织，其余可以划分当前管理组织 -->
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            lm.ORG_PERM_MANAGER = '1' AND org.ORI_ORG_ID != lm.ORG_ID
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            lm.ORG_PERM_RES_RANGE = '1' AND org.ORI_ORG_ID != lm.ORG_ID
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            lm.ORG_PERM_ORG = '1' AND org.ORI_ORG_ID != lm.ORG_ID
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            lm.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            lm.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            lm.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            lm.ORG_PERM_AUTH = '1'
        </isNotEmpty>
    </select>

    <select id="search" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        org.ORI_ORG_ID as "label",
        org.ORI_ORG_ENAME as "orgEname",
        org.ORI_ORG_CNAME as "text",
        "leaf"
        FROM
        (
        select
        a.ORG_ID as ORI_ORG_ID,
        a.ORG_ENAME as ORI_ORG_ENAME,
        a.ORG_CNAME as ORI_ORG_CNAME,
        ${platSchema}.queryOrgPath(a.ORG_ID) as NODE_SET
        from  ${platSchema}.TXSOG01 a where 1=1
        <isNotEmpty prepend=" AND " property="orgCname">
            a.ORG_CNAME LIKE ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgEname">
            a.ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            a.ORG_ID = #orgId#
        </isNotEmpty>
        )
        org , ${platSchema}.TXSLV01 lm
        where find_in_set(lm.org_id,org.NODE_SET) and lm.ORG_ADM_ID = #userId#
        <!-- 分级管理员、分级授权资源范围、组织机构维护权限只能划分到子组织，其余可以划分当前管理组织 -->
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            lm.ORG_PERM_MANAGER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            lm.ORG_PERM_RES_RANGE = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            lm.ORG_PERM_ORG = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            lm.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            lm.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            lm.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            lm.ORG_PERM_AUTH = '1'
        </isNotEmpty>
    </select>

    <select id="expandPath" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">

        select  a.ORG_ID as "label",
        subString(NODE_PATH,LOCATE(a.ORG_ID,NODE_PATH),length(NODE_PATH)-LOCATE(a.ORG_ID,NODE_PATH)+1) as "path"
        from
        (select  replace(queryOrgPath(#orgId#),',$','')  as NODE_PATH from dual)
        tpath,  ${platSchema}.TXSLV01 a where find_in_set(a.ORG_ID , NODE_PATH)
        and
         a.ORG_ADM_ID = #userId#
        <isNotEmpty prepend=" AND " property="orgPermManager"> <!-- 维护分级管理员权限 -->
            a.ORG_PERM_MANAGER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermResRange"> <!-- 分级资源授权范围管理权限 -->
            a.ORG_PERM_RES_RANGE = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrg"><!-- 组织机构维护权限 -->
            a.ORG_PERM_ORG = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermOrgMapping"> <!-- 组织机构映射 -->
            a.ORG_PERM_ORG_MAPPING = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroup"> <!-- 用户组维护权限 -->
            a.ORG_PERM_USER_GROUP = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermUserGroupMembrer"><!-- 用户组成员维护权限 -->
            a.ORG_PERM_USER_GROUP_MEMBRER = '1'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgPermAuth"><!-- 授权关系维护权限 -->
            a.ORG_PERM_AUTH = '1'
        </isNotEmpty>
        ORDER BY LEVEL ASC
    </select>


</sqlMap>