/**
 * Generate time : 2024-09-19 14:30:59
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.Min;

/**
 * Tvgdm0201
 *
 */
public class Tvgdm0201 extends DaoEPBase {

    private String spotCheckStandardId = " ";        /* 点检标准编号*/
    @NotBlank(message = "设备信息不能为空")
    private String eArchivesNo = " ";        /* 设备代码*/
    @NotBlank(message = "设备信息不能为空")
    private String equipmentName = " ";        /* 设备名称*/
    @NotBlank(message = "分部设备不能为空")
    private String deviceCode = " ";        /* 分部设备代码*/
    @NotBlank(message = "分部设备不能为空")
    private String deviceName = " ";        /* 分部设备名称*/
    @NotBlank(message = "设备状态不能为空")
    private String deviceCheckStatus = " ";        /* 设备状态*/
    @NotBlank(message = "点检内容不能为空")
    private String spotCheckContent = " ";        /* 点检内容*/
    @NotBlank(message = "点检方法不能为空")
    private String spotCheckMethod = " ";        /* 点检方法*/
    @NotBlank(message = "是否挂牌不能为空")
    private String isPublish = " ";        /* 是否挂牌*/
    @NotBlank(message = "点检标准类型不能为空")
    private String spotCheckStandardType = " ";        /* 标准类型*/
    private String judgmentStandard = " ";        /* 判断标准*/
    @NotBlank(message = "基准日期不能为空")
    private String benchmarkDate = " ";        /* 基准日期*/
    @NotBlank(message = "点检周期不能为空")
    @Min(value = 1, message = "点检周期必须大于0")
    private String spotCheckCycle = " ";        /* 点检周期*/
    @NotBlank(message = "点检性质不能为空")
    private String spotCheckNature = " ";        /* 点检性质*/
    @NotBlank(message = "实施方不能为空")
    private String spotCheckImplemente = " ";        /* 实施方*/
    private String measureId = " ";        /* 计量单位*/
    private BigDecimal upperLimit = new BigDecimal("0");        /* 上限值*/
    private BigDecimal lowerLimit = new BigDecimal("0");        /* 下限值*/
    private String spotCheckStatus = " ";        /* 点检标准状态*/
    private String tagId = " ";        /* 点位ID*/
    private String tagDesc = " ";        /* 点位描述*/
    @NotBlank(message = "是否上传照片不能为空")
    private String isPicture = "1";        /* 是否上传图片*/
    private String processInstanceId = " ";        /* 工作流实例ID*/
    private String apprStatus = " ";        /* 审批状态*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("spotCheckStandardId");
        eiColumn.setDescName("点检标准编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCheckStatus");
        eiColumn.setDescName("设备状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckContent");
        eiColumn.setDescName("点检内容");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckMethod");
        eiColumn.setDescName("点检方法");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isPublish");
        eiColumn.setDescName("是否挂牌");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckStandardType");
        eiColumn.setDescName("标准类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("judgmentStandard");
        eiColumn.setDescName("判断标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("benchmarkDate");
        eiColumn.setDescName("基准日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckCycle");
        eiColumn.setDescName("点检周期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckNature");
        eiColumn.setDescName("点检性质");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckImplemente");
        eiColumn.setDescName("实施方");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("measureId");
        eiColumn.setDescName("计量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upperLimit");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("上限值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lowerLimit");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("下限值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckStatus");
        eiColumn.setDescName("点检标准状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagId");
        eiColumn.setDescName("点位ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tagDesc");
        eiColumn.setDescName("点位描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isPicture");
        eiColumn.setDescName("是否上传图片");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processInstanceId");
        eiColumn.setDescName("工作流实例ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprStatus");
        eiColumn.setDescName("审批状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0201() {
        initMetaData();
    }

    /**
     * get the spotCheckStandardId - 点检标准编号
     * @return the spotCheckStandardId
     */
    public String getSpotCheckStandardId() {
        return this.spotCheckStandardId;
    }

    /**
     * set the spotCheckStandardId - 点检标准编号
     */
    public void setSpotCheckStandardId(String spotCheckStandardId) {
        this.spotCheckStandardId = spotCheckStandardId;
    }

    /**
     * get the deviceCode - 分部设备代码
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the deviceCheckStatus - 设备状态
     * @return the deviceCheckStatus
     */
    public String getDeviceCheckStatus() {
        return this.deviceCheckStatus;
    }

    /**
     * set the deviceCheckStatus - 设备状态
     */
    public void setDeviceCheckStatus(String deviceCheckStatus) {
        this.deviceCheckStatus = deviceCheckStatus;
    }

    /**
     * get the eArchivesNo - 设备代码
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备代码
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the spotCheckContent - 点检内容
     * @return the spotCheckContent
     */
    public String getSpotCheckContent() {
        return this.spotCheckContent;
    }

    /**
     * set the spotCheckContent - 点检内容
     */
    public void setSpotCheckContent(String spotCheckContent) {
        this.spotCheckContent = spotCheckContent;
    }

    /**
     * get the spotCheckMethod - 点检方法
     * @return the spotCheckMethod
     */
    public String getSpotCheckMethod() {
        return this.spotCheckMethod;
    }

    /**
     * set the spotCheckMethod - 点检方法
     */
    public void setSpotCheckMethod(String spotCheckMethod) {
        this.spotCheckMethod = spotCheckMethod;
    }

    /**
     * get the isPublish - 是否挂牌
     * @return the isPublish
     */
    public String getIsPublish() {
        return this.isPublish;
    }

    /**
     * set the isPublish - 是否挂牌
     */
    public void setIsPublish(String isPublish) {
        this.isPublish = isPublish;
    }

    /**
     * get the spotCheckStandardType - 标准类型
     * @return the spotCheckStandardType
     */
    public String getSpotCheckStandardType() {
        return this.spotCheckStandardType;
    }

    /**
     * set the spotCheckStandardType - 标准类型
     */
    public void setSpotCheckStandardType(String spotCheckStandardType) {
        this.spotCheckStandardType = spotCheckStandardType;
    }

    /**
     * get the judgmentStandard - 判断标准
     * @return the judgmentStandard
     */
    public String getJudgmentStandard() {
        return this.judgmentStandard;
    }

    /**
     * set the judgmentStandard - 判断标准
     */
    public void setJudgmentStandard(String judgmentStandard) {
        this.judgmentStandard = judgmentStandard;
    }

    /**
     * get the benchmarkDate - 基准日期
     * @return the benchmarkDate
     */
    public String getBenchmarkDate() {
        return this.benchmarkDate;
    }

    /**
     * set the benchmarkDate - 基准日期
     */
    public void setBenchmarkDate(String benchmarkDate) {
        this.benchmarkDate = benchmarkDate;
    }

    /**
     * get the spotCheckCycle - 点检周期
     * @return the spotCheckCycle
     */
    public String getSpotCheckCycle() {
        return this.spotCheckCycle;
    }

    /**
     * set the spotCheckCycle - 点检周期
     */
    public void setSpotCheckCycle(String spotCheckCycle) {
        this.spotCheckCycle = spotCheckCycle;
    }

    /**
     * get the spotCheckNature - 点检性质
     * @return the spotCheckNature
     */
    public String getSpotCheckNature() {
        return this.spotCheckNature;
    }

    /**
     * set the spotCheckNature - 点检性质
     */
    public void setSpotCheckNature(String spotCheckNature) {
        this.spotCheckNature = spotCheckNature;
    }

    /**
     * get the spotCheckImplemente - 实施方
     * @return the spotCheckImplemente
     */
    public String getSpotCheckImplemente() {
        return this.spotCheckImplemente;
    }

    /**
     * set the spotCheckImplemente - 实施方
     */
    public void setSpotCheckImplemente(String spotCheckImplemente) {
        this.spotCheckImplemente = spotCheckImplemente;
    }

    /**
     * get the measureId - 计量单位
     * @return the measureId
     */
    public String getMeasureId() {
        return this.measureId;
    }

    /**
     * set the measureId - 计量单位
     */
    public void setMeasureId(String measureId) {
        this.measureId = measureId;
    }

    /**
     * get the upperLimit - 上限值
     * @return the upperLimit
     */
    public BigDecimal getUpperLimit() {
        return this.upperLimit;
    }

    /**
     * set the upperLimit - 上限值
     */
    public void setUpperLimit(BigDecimal upperLimit) {
        this.upperLimit = upperLimit;
    }

    /**
     * get the lowerLimit - 下限值
     * @return the lowerLimit
     */
    public BigDecimal getLowerLimit() {
        return this.lowerLimit;
    }

    /**
     * set the lowerLimit - 下限值
     */
    public void setLowerLimit(BigDecimal lowerLimit) {
        this.lowerLimit = lowerLimit;
    }

    /**
     * get the spotCheckStatus - 点检标准状态
     * @return the spotCheckStatus
     */
    public String getSpotCheckStatus() {
        return this.spotCheckStatus;
    }

    /**
     * set the spotCheckStatus - 点检标准状态
     */
    public void setSpotCheckStatus(String spotCheckStatus) {
        this.spotCheckStatus = spotCheckStatus;
    }

    /**
     * get the tagId - 点位ID
     * @return the tagId
     */
    public String getTagId() {
        return this.tagId;
    }

    /**
     * set the tagId - 点位ID
     */
    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    /**
     * get the tagDesc - 点位描述
     * @return the tagDesc
     */
    public String getTagDesc() {
        return this.tagDesc;
    }

    /**
     * set the tagDesc - 点位描述
     */
    public void setTagDesc(String tagDesc) {
        this.tagDesc = tagDesc;
    }

    /**
     * get the isPicture - 是否上传图片
     * @return the isPicture
     */
    public String getIsPicture() {
        return this.isPicture;
    }

    /**
     * set the isPicture - 是否上传图片
     */
    public void setIsPicture(String isPicture) {
        this.isPicture = isPicture;
    }

    /**
     * get the processInstanceId - 工作流实例ID
     * @return the processInstanceId
     */
    public String getProcessInstanceId() {
        return this.processInstanceId;
    }

    /**
     * set the processInstanceId - 工作流实例ID
     */
    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    /**
     * get the apprStatus - 审批状态
     * @return the apprStatus
     */
    public String getApprStatus() {
        return this.apprStatus;
    }

    /**
     * set the apprStatus - 审批状态
     */
    public void setApprStatus(String apprStatus) {
        this.apprStatus = apprStatus;
    }

    /**
     * get the uuid - 唯一编码
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSpotCheckStandardId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckStandardId")), spotCheckStandardId));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setDeviceCheckStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCheckStatus")), deviceCheckStatus));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setSpotCheckContent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckContent")), spotCheckContent));
        setSpotCheckMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckMethod")), spotCheckMethod));
        setIsPublish(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isPublish")), isPublish));
        setSpotCheckStandardType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckStandardType")), spotCheckStandardType));
        setJudgmentStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("judgmentStandard")), judgmentStandard));
        setBenchmarkDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("benchmarkDate")), benchmarkDate));
        setSpotCheckCycle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckCycle")), spotCheckCycle));
        setSpotCheckNature(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckNature")), spotCheckNature));
        setSpotCheckImplemente(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckImplemente")), spotCheckImplemente));
        setMeasureId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("measureId")), measureId));
        setUpperLimit(NumberUtils.toBigDecimal(StringUtils.toString(map.get("upperLimit")), upperLimit));
        setLowerLimit(NumberUtils.toBigDecimal(StringUtils.toString(map.get("lowerLimit")), lowerLimit));
        setSpotCheckStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckStatus")), spotCheckStatus));
        setTagId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagId")), tagId));
        setTagDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tagDesc")), tagDesc));
        setIsPicture(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isPicture")), isPicture));
        setProcessInstanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processInstanceId")), processInstanceId));
        setApprStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprStatus")), apprStatus));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("spotCheckStandardId", StringUtils.toString(spotCheckStandardId, eiMetadata.getMeta("spotCheckStandardId")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("deviceCheckStatus", StringUtils.toString(deviceCheckStatus, eiMetadata.getMeta("deviceCheckStatus")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("spotCheckContent", StringUtils.toString(spotCheckContent, eiMetadata.getMeta("spotCheckContent")));
        map.put("spotCheckMethod", StringUtils.toString(spotCheckMethod, eiMetadata.getMeta("spotCheckMethod")));
        map.put("isPublish", StringUtils.toString(isPublish, eiMetadata.getMeta("isPublish")));
        map.put("spotCheckStandardType", StringUtils.toString(spotCheckStandardType, eiMetadata.getMeta("spotCheckStandardType")));
        map.put("judgmentStandard", StringUtils.toString(judgmentStandard, eiMetadata.getMeta("judgmentStandard")));
        map.put("benchmarkDate", StringUtils.toString(benchmarkDate, eiMetadata.getMeta("benchmarkDate")));
        map.put("spotCheckCycle", StringUtils.toString(spotCheckCycle, eiMetadata.getMeta("spotCheckCycle")));
        map.put("spotCheckNature", StringUtils.toString(spotCheckNature, eiMetadata.getMeta("spotCheckNature")));
        map.put("spotCheckImplemente", StringUtils.toString(spotCheckImplemente, eiMetadata.getMeta("spotCheckImplemente")));
        map.put("measureId", StringUtils.toString(measureId, eiMetadata.getMeta("measureId")));
        map.put("upperLimit", StringUtils.toString(upperLimit, eiMetadata.getMeta("upperLimit")));
        map.put("lowerLimit", StringUtils.toString(lowerLimit, eiMetadata.getMeta("lowerLimit")));
        map.put("spotCheckStatus", StringUtils.toString(spotCheckStatus, eiMetadata.getMeta("spotCheckStatus")));
        map.put("tagId", StringUtils.toString(tagId, eiMetadata.getMeta("tagId")));
        map.put("tagDesc", StringUtils.toString(tagDesc, eiMetadata.getMeta("tagDesc")));
        map.put("isPicture", StringUtils.toString(isPicture, eiMetadata.getMeta("isPicture")));
        map.put("processInstanceId", StringUtils.toString(processInstanceId, eiMetadata.getMeta("processInstanceId")));
        map.put("apprStatus", StringUtils.toString(apprStatus, eiMetadata.getMeta("apprStatus")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}