/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids0901;

import java.util.Map;

/**
* Tlids0901
* 
*/
public class LIDS0901 extends Tlids0901 {
        public static final String QUERY = "LIDS0901.query";
        public static final String COUNT = "LIDS0901.count";
        public static final String COUNT_UUID = "LIDS0901.count_uuid";
        public static final String INSERT = "LIDS0901.insert";
        public static final String UPDATE = "LIDS0901.update";
        public static final String DELETE = "LIDS0901.delete";
        public static final String QUERY_X_POSITION = "LIDS0901.queryXPosition";
        public static final String UPDATE_INVENTORY_PACK_INFO = "LIDS0901.updateInventoryPackInfo";
        public static final String QUERY_RELATED_PACKS_BY_ADJACENT_IDS = "LIDS0901.queryRelatedPacksByAdjacentIds";
        public static final String QUERY_PACKS_BY_XY_COORDINATES = "LIDS0901.queryPacksByXyCoordinates";
        public static final String QUERY_PACKS_BY_AREA = "LIDS0901.queryPacksByArea";
        public static final String QUERY_PACKS_BY_RESULT_ID = "LIDS0901.queryPacksByResultId";
        public static final String QUERY_MAX_POS_DIR_CODE_AREA = "LIDS0901.queryMaxPosDirCodeArea";
        public static final String QUERY_PACK_MESSAGE = "LIDS0901.queryPackMessage";

        public static final String UPDATE_STATUS = "LIDS0901.updateStatus";
        public static final String UPDATE_UNITED_PACK_ID = "LIDS0901.updateUnitedPackId";
        public static final String UPDATE_PACK_INFO_IS_INVENTORY_POSITION = "LIDS0901.updatePackInfoIsInventoryPosition";
        public static final String UPDATE_AREA_BY_PACK_ID = "LIDS0901.updateAreaByPackId";
        public static final String QUERY_LOCATION_OCCUPY_C_Q = "LIDS0901.queryLocationOccupyCQ";
        public static final String QUERY_LOCATION_OCCUPY_C_Q_SUM = "LIDS0901.queryLocationOccupyCQSum";

        //修改状态为30：出库的实物库存
        public static final String ROLLBACK_MATERIAL_LOADING = "LIDS0901.rollbackMaterialLoading";

        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS0901() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}