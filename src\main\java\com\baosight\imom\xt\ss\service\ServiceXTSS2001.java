/**
* Generate time : 2025-01-14 22:39:41
* Version : 1.0
*/
package com.baosight.imom.xt.ss.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.xservices.xs.domain.XS02;

/**
 * XTSS2001 用户组选择服务
 * 复用现有的用户组查询逻辑
 */
public class ServiceXTSS2001 extends ServiceBase {

    /**
     * 初始化加载
     */
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = super.query(inInfo, "XTSS20.queryUserTeam");
        return outInfo;
    }
}
