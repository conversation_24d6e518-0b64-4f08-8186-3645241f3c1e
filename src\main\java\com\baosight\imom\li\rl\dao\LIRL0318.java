/**
* Generate time : 2025-03-26 13:15:51
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0318
* 
*/
public class LIRL0318 extends DaoEPBase {
    public static final String QUERY = "LIRL0318.query";
    public static final String COUNT = "LIRL0318.count";
    public static final String INSERT = "LIRL0318.insert";
    public static final String UPDATE = "LIRL0318.update";
    public static final String DELETE = "LIRL0318.delete";
                private String segNo = " ";		/* 业务单元代代码*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String segName = " ";		/* 业务单元简称*/
                private String status = " ";		/* 状态(撤销：00、新增：10、生效：20)*/
                private String prodTypeId = " ";		/* 三级品种附属码*/
                private String prodTypeIdName = " ";		/* 三级品种附属码名称*/
                private String coilSheetFlag = " ";		/* 板卷标记（10 卷，20 板）*/
                private Integer reverseDurationSeconds = Integer.valueOf(0);		/* 倒车时间*/
                private Integer searchDurationSeconds = Integer.valueOf(0);		/* 找货时间*/
                private Integer toolChangeDurationSeconds = Integer.valueOf(0);		/* 切换吊具时间*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元代简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10、生效：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setDescName("三级品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeIdName");
        eiColumn.setDescName("三级品种附属码名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("coilSheetFlag");
        eiColumn.setDescName("板卷标记（10 卷，20 板）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reverseDurationSeconds");
        eiColumn.setDescName("倒车时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("searchDurationSeconds");
        eiColumn.setDescName("找货时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("toolChangeDurationSeconds");
        eiColumn.setDescName("切换吊具时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0318() {
initMetaData();
}

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
        * get the segNo - 业务单元代代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the status - 状态(撤销：00、新增：10、生效：20)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(撤销：00、新增：10、生效：20)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the prodTypeId - 三级品种附属码
        * @return the prodTypeId
        */
        public String getProdTypeId() {
        return this.prodTypeId;
        }

        /**
        * set the prodTypeId - 三级品种附属码
        */
        public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
        }
        /**
        * get the prodTypeIdName - 三级品种附属码名称
        * @return the prodTypeIdName
        */
        public String getProdTypeIdName() {
        return this.prodTypeIdName;
        }

        /**
        * set the prodTypeIdName - 三级品种附属码名称
        */
        public void setProdTypeIdName(String prodTypeIdName) {
        this.prodTypeIdName = prodTypeIdName;
        }
        /**
        * get the coilSheetFlag - 板卷标记（10 卷，20 板）
        * @return the coilSheetFlag
        */
        public String getCoilSheetFlag() {
        return this.coilSheetFlag;
        }

        /**
        * set the coilSheetFlag - 板卷标记（10 卷，20 板）
        */
        public void setCoilSheetFlag(String coilSheetFlag) {
        this.coilSheetFlag = coilSheetFlag;
        }
        /**
        * get the reverseDurationSeconds - 倒车时间
        * @return the reverseDurationSeconds
        */
        public Integer getReverseDurationSeconds() {
        return this.reverseDurationSeconds;
        }

        /**
        * set the reverseDurationSeconds - 倒车时间
        */
        public void setReverseDurationSeconds(Integer reverseDurationSeconds) {
        this.reverseDurationSeconds = reverseDurationSeconds;
        }
        /**
        * get the searchDurationSeconds - 找货时间
        * @return the searchDurationSeconds
        */
        public Integer getSearchDurationSeconds() {
        return this.searchDurationSeconds;
        }

        /**
        * set the searchDurationSeconds - 找货时间
        */
        public void setSearchDurationSeconds(Integer searchDurationSeconds) {
        this.searchDurationSeconds = searchDurationSeconds;
        }
        /**
        * get the toolChangeDurationSeconds - 切换吊具时间
        * @return the toolChangeDurationSeconds
        */
        public Integer getToolChangeDurationSeconds() {
        return this.toolChangeDurationSeconds;
        }

        /**
        * set the toolChangeDurationSeconds - 切换吊具时间
        */
        public void setToolChangeDurationSeconds(Integer toolChangeDurationSeconds) {
        this.toolChangeDurationSeconds = toolChangeDurationSeconds;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
                setProdTypeIdName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeIdName")), prodTypeIdName));
                setCoilSheetFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("coilSheetFlag")), coilSheetFlag));
                setReverseDurationSeconds(NumberUtils.toInteger(StringUtils.toString(map.get("reverseDurationSeconds")), reverseDurationSeconds));
                setSearchDurationSeconds(NumberUtils.toInteger(StringUtils.toString(map.get("searchDurationSeconds")), searchDurationSeconds));
                setToolChangeDurationSeconds(NumberUtils.toInteger(StringUtils.toString(map.get("toolChangeDurationSeconds")), toolChangeDurationSeconds));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("prodTypeId",StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
                map.put("prodTypeIdName",StringUtils.toString(prodTypeIdName, eiMetadata.getMeta("prodTypeIdName")));
                map.put("coilSheetFlag",StringUtils.toString(coilSheetFlag, eiMetadata.getMeta("coilSheetFlag")));
                map.put("reverseDurationSeconds",StringUtils.toString(reverseDurationSeconds, eiMetadata.getMeta("reverseDurationSeconds")));
                map.put("searchDurationSeconds",StringUtils.toString(searchDurationSeconds, eiMetadata.getMeta("searchDurationSeconds")));
                map.put("toolChangeDurationSeconds",StringUtils.toString(toolChangeDurationSeconds, eiMetadata.getMeta("toolChangeDurationSeconds")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));

return map;

}
}