<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1003">

    <sql id="queryCondition">
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="procedureCode">
            PROCEDURE_CODE = #procedureCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="slQuery">
            PROCEDURE_CODE in ('SL','KJ')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startTime">
            substr(START_TIME,1,19) &gt;= #startTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endTime">
            substr(STOP_TIME,1,19) &lt;= #endTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1003">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCEDURE_CODE as "procedureCode",  <!-- 工序代码 -->
        PROCEDURE_NAME as "procedureName",  <!-- 工序名称 -->
        START_TIME as "startTime",  <!-- 开始时间 -->
        STOP_TIME as "stopTime",  <!-- 结束时间 -->
        DURATION as "duration",  <!-- 持续时间 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        RELEVANCE_ID as "relevanceId" <!-- 关联ID -->
        FROM ${mevgSchema}.TVGDM1003 WHERE 1=1
        <include refid="queryCondition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                E_ARCHIVES_NO,START_TIME
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1003 WHERE 1=1
        <include refid="queryCondition"/>
    </select>

    <select id="sumPrepare" resultClass="int">
        SELECT
        IFNULL(sum(DURATION),0) as "duration"  <!-- 持续时间 -->
        FROM ${mevgSchema}.TVGDM1003 WHERE
        DEL_FLAG = '0'
        AND SEG_NO = #segNo#
        AND RELEVANCE_ID = #relevanceId#
        AND PROCEDURE_CODE not in ('ZDYX','XL')
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1003 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCEDURE_CODE,  <!-- 工序代码 -->
        PROCEDURE_NAME,  <!-- 工序名称 -->
        START_TIME,  <!-- 开始时间 -->
        STOP_TIME,  <!-- 结束时间 -->
        DURATION,  <!-- 持续时间 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        RELEVANCE_ID  <!-- 关联ID -->
        )
        VALUES (#eArchivesNo#, #equipmentName#, #procedureCode#, #procedureName#, #startTime#, #stopTime#, #duration#,
        #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #relevanceId#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1003 WHERE
        UUID = #uuid#
    </delete>

    <delete id="deleteByRelevance">
        DELETE FROM ${mevgSchema}.TVGDM1003 WHERE
        RELEVANCE_ID = #relevanceId#
    </delete>

    <update id="updateByRelevance">
        UPDATE ${mevgSchema}.TVGDM1003
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = '1'   <!-- 删除标记 -->
        WHERE
        RELEVANCE_ID = #relevanceId#
        AND DEL_FLAG = '0'
    </update>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1003
        SET
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCEDURE_CODE = #procedureCode#,   <!-- 工序代码 -->
        PROCEDURE_NAME = #procedureName#,   <!-- 工序名称 -->
        START_TIME = #startTime#,   <!-- 开始时间 -->
        STOP_TIME = #stopTime#,   <!-- 结束时间 -->
        DURATION = #duration#,   <!-- 持续时间 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        RELEVANCE_ID = #relevanceId#  <!-- 关联ID -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>