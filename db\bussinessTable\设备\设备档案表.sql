create table TMEDV0101
(
    E_ARCHIVES_NO                  VARCHAR(20)    default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME                 VARCHAR(200)   default ' '    not null comment '设备名称',
    EQUIPMENT_TYPE                 VARCHAR(1)     default ' '    not null comment '设备类型',
    PROCESS_CATEGORY               VARCHAR(20)    default ' '    not null comment '工序大类代码',
    PROCESS_CATEGORY_NAME          VARCHAR(50)    default ' '    not null comment '工序大类名称',
    PROCESS_CATEGORY_SUB           VARCHAR(20)    default ' '    not null comment '工序小类代码',
    PROCESS_CATEGORY_SUB_NAME      VARCHAR(50)    default ' '    not null comment '工序小类名称',
    DESIGN_PRODUCTION_CAPACITY_WEI DECIMAL(20, 8) default 0      not null comment '设计产能（万吨/年）',
    DESIGN_PRODUCTION_CAPACITY_NUM DECIMAL(20, 8) default 0      not null comment '设计产能（万片/年）',
    EQUIPMENT_PRODUCING_AREA       VARCHAR(200)   default ' '    not null comment '设备产地',
    MAKER_NAME                     VARCHAR(60)    default ' '    not null comment '制造商名称',
    EQUIPMENT_COMMISSIONING_DATE   VARCHAR(17)    default ' '    not null comment '设备投产日期',
    FIXED_ASSET_NUMBER             VARCHAR(200)   default ' '    not null comment '固定资产编号',
    COLLECT_FLAG                   VARCHAR(1)     default ' '    not null comment '采集标记',
    EQUIPMENT_STATUS               VARCHAR(20)    default ' '    not null comment '设备状态',
    ARCHIVE_ALTER_DESC             VARCHAR(200)   default ' '    not null comment '设备档案变更说明',
    -- 固定字段
    UUID                           VARCHAR(32)                   NOT NULL COMMENT '唯一编码',
    REC_CREATOR                    VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME                VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR                    VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME                VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID                      VARCHAR(64)    DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG                   VARCHAR(1)     DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID),
    unique KEY (E_ARCHIVES_NO)
) COMMENT ='设备档案表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;