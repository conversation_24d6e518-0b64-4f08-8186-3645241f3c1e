<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFPopupInput ename="inqu_status-0-customerId" cname="承运商代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="userNum" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true"
                             popupTitle="承运商/客户代码查询">
            </EF:EFPopupInput>
            <EF:EFInput type="text" ename="inqu_status-0-customerName" cname="承运商名称" colWidth="3"
                        ratio="4:8" disabled="true" />
            <EF:EFPopupInput ename="inqu_status-0-customerId2" cname="客户代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-customerName2"
                             containerId="userNum2" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true"
                             popupTitle="客户代码查询">
            </EF:EFPopupInput>
            <EF:EFInput type="text" ename="inqu_status-0-customerName2" cname="客户代码名称" colWidth="3"
                        ratio="4:8" disabled="true" />
            <EF:EFInput ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3" placeholder="模糊查询"/>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                         valueField="valueField" textField="textField"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                <EF:EFCodeOption codeName="P003"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFInput type="text" ename="inqu_status-0-driverName" cname="司机姓名" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
            <EF:EFInput type="text" ename="inqu_status-0-driverIdentity" cname="司机身份证号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
            <EF:EFInput type="text" ename="inqu_status-0-tel" cname="手机号" colWidth="3"
                        placeholder="模糊查询"
                        ratio="4:8"/>
        </div>
    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
    <div id="info-1" title="列表">
        <div id="result">
        <EF:EFGrid blockId="result" autoDraw="false"
                   autoBind="false" isFloat="true" personal="true" sort="all">
            <EF:EFColumn ename="segNo" cname="业务单元代码" align="center" enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
            <EF:EFColumn ename="unitCode" cname="系统账套" enable="false" align="center" hidden="true"/>
            <EF:EFColumn ename="pageId" cname="司机申请单号" align="center" enable="false"/>
            <EF:EFComboColumn ename="status" cname="状态"  align="center"
                              enable="false" width="120">
                <EF:EFCodeOption codeName="P003"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="customerId" cname="承运商/客户代码" align="center" enable="false"/>
            <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" enable="false"/>
            <EF:EFColumn ename="driverName" cname="司机姓名" align="center" enable="false" />
            <EF:EFColumn ename="tel" cname="司机手机号" align="center" enable="false" />
            <EF:EFColumn ename="driverIdentity" cname="司机身份证号" align="center"  enable="false"/>
            <EF:EFColumn ename="remark" cname="备注" align="center" enable="false"/>
            <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                         readonly="true" align="center"/>
            <EF:EFColumn ename="uuid" cname="ID" align="center" hidden="true"/>
            <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
            <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" />
        </EF:EFGrid>
        </div>
    </div>
        <div id="info-2" id="add" title="详情">
            <div class="row" id="detail" colWidth="8" style="border:2px solid  #DBEFFF;">
                <EF:EFRegion id="add" title="主项详情">
                    <EF:EFInput ename="add_status-0-type" cname="操作类型" type="hidden"/>

                    <div class="row">
                        <EF:EFPopupInput ename="add_status-0-unitCode" cname="业务单元代码"  required="true" resizable="true"  readonly="true"
                                         containerId="unitInfo1" popupWidth="600" pupupHeight="300" originalInput="true" center="true" backFillFieldIds="add_status-0-segNo,add_status-0-segName"
                                         popupTitle="业务套账查询"  colWidth="3">
                        </EF:EFPopupInput>
                        <EF:EFInput ename="add_status-0-segNo" cname="系统账套" disabled="true"  colWidth="3" type="hidden"/>
                        <EF:EFInput ename="add_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
                        <EF:EFInput ename="add_status-0-uuid" cname="司机申请单号"  colWidth="3"/>
                        <EF:EFSelect autoWidth="true" ename="add_status-0-status" cname="状态" colWidth="3">
                            <EF:EFCodeOption codeName="P003"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row">
                        <EF:EFPopupInput ename="add_status-0-customerId" cname="客户/承运商代码" resizable="true"  colWidth="3"
                                         disabled="true" readonly="true"
                                         ratio="4:8" originalInput="true"
                                         backFillFieldIds="infof1_status-0-recCreatorName"
                                         containerId="userNum3" popupWidth="800" pupupHeight="300"
                                         popupTitle="客户信息" center="true">
                        </EF:EFPopupInput>
                        <EF:EFInput ename="add_status-0-customerName" readOnly="true" colWidth="3" cname="客户/承运商名称"
                                    />
                        <EF:EFInput ename="add_status-0-driverName" readOnly="true" colWidth="3" cname="司机姓名"/>
                        <EF:EFInput ename="add_status-0-tel" readOnly="true" colWidth="3" cname="司机手机号"
                                    data-regex="/^1[3-9]\d{9}$/"
                                    data-errorPrompt="请输入正确的手机号！"
                        />
                        <EF:EFInput ename="add_status-0-driverIdentity" readOnly="true" colWidth="3" cname="司机身份证号"
                        data-regex="/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/"
                        data-errorPrompt="请输入正确的身份证号！"
                        />
                    </div>
                </EF:EFRegion>
            </div>
            <div class="row"  id="buttonsetchild" colWidth="8" style="border:2px solid  #DBEFFF;">
                <EF:EFRegion id="buttonsetchild" title="子项详情">
                    <EF:EFGrid isFloat="true"   blockId="buttonsetchild"  queryMethod="queryDriverChildShow" autoBind="false" autoDraw="override"   >
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true" width="135"  align="center" enable="false" locked="true"/>
                        <EF:EFColumn ename="segNo" cname="系统账套" hidden="true" width="135"  align="center" enable="false" locked="true"/>
                        <EF:EFColumn ename="unitCode" cname="业务单元代码" hidden="true" width="135"  align="center" enable="false" locked="true"/>
                        <EF:EFColumn ename="m_uuid" cname="司机申请单号"  width="135"  align="center" enable="false" locked="true"/>
                        <EF:EFColumn ename="vehicleNo" cname="车牌号" width="135"  align="center" enable="true"
                                     data-regex="/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/"
                                     data-errorprompt="请输入正确的车牌号！"
                                     />
                        <EF:EFComboColumn ename="status" cname="状态" width="135" align="center" enable="false"
                                          textField="textField" valueField="valueField">
                            <EF:EFCodeOption codeName="P003"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="remark" cname="备注" width="230"  align="left" maxLength="250" editType="textarea"/>
                        <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                     readonly="true" align="center"/>
                        <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                     readonly="true" align="center"/>
                        <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                     readonly="true" align="center"/>
                        <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                     readonly="true" align="center"/>
                        <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                     readonly="true" align="center"/>
                        <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                     readonly="true" align="center"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </div>
    </EF:EFTab>
    <%--    Excel导入弹出框--%>
    <EF:EFWindow id="insertImport" height="60%" width="60%">
        <%--    渲染弹出的Grid--%>
        <EF:EFRegion id="excelImport" title="司机信息导入Excel" style="height:95%">
            <EF:EFInput ename="fileForm" type="file" ratio="4:16" colWidth="16"/>
        </EF:EFRegion>
    </EF:EFWindow>
<%--    <EF:EFWindow id="Import" title="Excel导入" height="20%" width="40%" refresh="true" lazyload="true">--%>
<%--        <EF:EFRegion id="ExcelImport" title="司机信息Excel导入">--%>
<%--            <div style="display: none;">--%>
<%--                <EF:EFInput ename="fileForm2" type="file" colWidth="8"/>--%>
<%--            </div>--%>
<%--            <div style="text-align: center;">--%>
<%--                <button id="beginImport" class='custom i-btn-lg'>导入Excel</button>--%>
<%--            </div>--%>
<%--        </EF:EFRegion>--%>
<%--    </EF:EFWindow>--%>

<%--    <EF:EFWindow id="Excelresult" title="导入Excel数据预览" height="70%" width="85%" refresh="true" lazyload="true">--%>
<%--        <EF:EFRegion id="t0" title="导入Excel数据预览">--%>
<%--            <div class="row" style="width: 102.4%">--%>
<%--                <EF:EFGrid blockId="result3" autoBind="false" autoDraw="no" needAuth="true" checkMode="single,row" isFloat="true" height="350">--%>
<%--                    <EF:EFColumn ename="segNo" cname="系统账套"  enable="false" align="center"  hidden="true"/>--%>
<%--                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false"  hidden="true"/>--%>
<%--                    <EF:EFColumn ename="mouldNo" cname="模具编号"  width="100" enable="false" />--%>
<%--                    <EF:EFColumn ename="mouldName" cname="模具名称" width="130" enable="false" />--%>
<%--                    <EF:EFColumn ename="mouldUseCount" cname="模具总冲次数(次)" width="150" format="{0:N0}" enable="false" align="right"/>--%>
<%--                    <EF:EFColumn ename="mouldCenMouldUseCount" cname="模具当前冲次数(次)" width="150"  format="{0:N0}" enable="false" align="right"/>--%>
<%--                    <EF:EFColumn ename="machineCode" cname="机组代码"  width="100" />--%>
<%--                    <EF:EFColumn ename="machineName" cname="机组名称"  width="100" enable="false"/>--%>
<%--                    <EF:EFColumn ename="stepDistance" cname="步距(mm)"  width="100" enable="false" />--%>
<%--                    <EF:EFColumn ename="mouldCustomId" cname="所属客户" width="100"  align="center"/>--%>
<%--                    <EF:EFColumn ename="customerName" cname="客户名称" width="130"  align="center"  enable="false"/>--%>
<%--                    <EF:EFColumn ename="dUserNum" cname="分户号" width="130"  align="center"/>--%>
<%--                    <EF:EFColumn ename="dUserName" cname="分户号简称" width="130"  align="center" enable="false"/>--%>
<%--                    <EF:EFColumn ename="mouldFactory" cname="模具厂家" width="100"  enable="false"/>--%>
<%--                    <EF:EFColumn ename="mouldModles" cname="车型" width="100"  enable="false"/>--%>
<%--                    <EF:EFColumn ename="specDesc" cname="材料规格" width="140"  enable="false"/>--%>
<%--                    <EF:EFColumn ename="toolId" cname="工装号" width="100"  enable="false"/>--%>
<%--                    <EF:EFColumn ename="stuffCode" cname="资材号" width="130"  align="center"/>--%>
<%--                    <EF:EFColumn ename="mouldSaleAmount" cname="销售金额" width="130"  align="center"  enable="false" format="{0:N2}"/>--%>
<%--                    <EF:EFColumn ename="mouldSpecWidth" cname="模具外形(宽mm)" width="120"  format="{0:N2}" align="right"  enable="false"/>--%>
<%--                </EF:EFGrid>--%>
<%--            </div>--%>
<%--            <div style="text-align: center;margin-top: 50px">--%>
<%--                <button id="confirmImport" class='custom i-btn-lg'>解析导入</button>--%>
<%--            </div>--%>
<%--        </EF:EFRegion>--%>
<%--    </EF:EFWindow>--%>
    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo1" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0001" id="userNum2" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0000" id="userNum3" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
