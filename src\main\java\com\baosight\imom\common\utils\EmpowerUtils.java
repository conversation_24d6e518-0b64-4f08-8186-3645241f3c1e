package com.baosight.imom.common.utils;

import com.baosight.imc.common.utils.AuthorityUtil;
import com.baosight.imc.interfaces.fs.service.ServiceFSDA0400;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;

public class EmpowerUtils {

    /**
     * 获取授权资源语句
     *
     * @param pageId
     * @param buttonId
     * @return
     */
    public static String getEmpowerRsc(String pageId, String buttonId) {
        String empowerFlag = PlatApplicationContext.getProperty("empowerFlag");

        if ("1".equals(empowerFlag)) {
            String epassAuthFilter = AuthorityUtil.getDataAccessRestrictSQL(ServiceFSDA0400.getBizRestrictIdByForm(pageId, buttonId));
            return epassAuthFilter;
        } else {
            return "";
        }
    }

    public static String getEmpowerRsc(String userId, String pageId, String buttonId) {
        String empowerFlag = PlatApplicationContext.getProperty("empowerFlag");

        if ("1".equals(empowerFlag)) {
            String epassAuthFilter = AuthorityUtil.getDataAccessRestrictSQL(userId, ServiceFSDA0400.getBizRestrictIdByForm(pageId, buttonId));
            return epassAuthFilter;
        } else {
            return "";
        }
    }
}
