/**
 * Generate time : 2024-10-22 9:45:18
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import javax.validation.constraints.Min;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

/**
 * Tvgdm1101
 */
public class Tvgdm1101 extends DaoEPBase {

    @NotBlank(message = "设备信息不能为空")
    private String eArchivesNo = " ";        /* 设备档案编号*/
    @NotBlank(message = "设备信息不能为空")
    private String equipmentName = " ";        /* 设备名称*/
    @NotBlank(message = "工序代码不能为空")
    private String procedureCode = " ";        /* 工序代码*/
    @NotBlank(message = "工序名称不能为空")
    private String procedureName = " ";        /* 工序名称*/
    @NotBlank(message = "开始时间规则不能为空")
    private String startTimeRule = " ";        /* 开始时间规则*/
    @NotBlank(message = "结束时间规则不能为空")
    private String stopTimeRule = " ";        /* 结束时间规则*/
    private String ihdStartRule = " ";        /* IHD开始时间规则*/
    private String ihdStopRule = " ";        /* IHD结束时间规则*/
    @NotBlank(message = "多次标记不能为空")
    private String multiFlag = "0";        /* 多次标记*/
    private String ruleStatus = " ";        /* 规则状态*/
    @Min(value = 1, message = "排序不能小于1")
    private Integer sortIndex = 1;        /* 排序*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String remark = " ";        /* 备注*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("procedureCode");
        eiColumn.setDescName("工序代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("procedureName");
        eiColumn.setDescName("工序名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startTimeRule");
        eiColumn.setDescName("开始时间规则");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stopTimeRule");
        eiColumn.setDescName("结束时间规则");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ihdStartRule");
        eiColumn.setDescName("IHD开始时间规则");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ihdStopRule");
        eiColumn.setDescName("IHD结束时间规则");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ruleStatus");
        eiColumn.setDescName("规则状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortIndex");
        eiColumn.setDescName("排序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("multiFlag");
        eiColumn.setDescName("多次标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm1101() {
        initMetaData();
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the procedureCode - 工序代码
     *
     * @return the procedureCode
     */
    public String getProcedureCode() {
        return this.procedureCode;
    }

    /**
     * set the procedureCode - 工序代码
     */
    public void setProcedureCode(String procedureCode) {
        this.procedureCode = procedureCode;
    }

    /**
     * get the procedureName - 工序名称
     *
     * @return the procedureName
     */
    public String getProcedureName() {
        return this.procedureName;
    }

    /**
     * set the procedureName - 工序名称
     */
    public void setProcedureName(String procedureName) {
        this.procedureName = procedureName;
    }

    /**
     * get the startTimeRule - 开始时间规则
     *
     * @return the startTimeRule
     */
    public String getStartTimeRule() {
        return this.startTimeRule;
    }

    /**
     * set the startTimeRule - 开始时间规则
     */
    public void setStartTimeRule(String startTimeRule) {
        this.startTimeRule = startTimeRule;
    }

    /**
     * get the stopTimeRule - 结束时间规则
     *
     * @return the stopTimeRule
     */
    public String getStopTimeRule() {
        return this.stopTimeRule;
    }

    /**
     * set the stopTimeRule - 结束时间规则
     */
    public void setStopTimeRule(String stopTimeRule) {
        this.stopTimeRule = stopTimeRule;
    }

    /**
     * get the ruleStatus - 规则状态
     *
     * @return the ruleStatus
     */
    public String getRuleStatus() {
        return this.ruleStatus;
    }

    /**
     * set the ruleStatus - 规则状态
     */
    public void setRuleStatus(String ruleStatus) {
        this.ruleStatus = ruleStatus;
    }

    /**
     * get the sortIndex - 排序
     *
     * @return the sortIndex
     */
    public Integer getSortIndex() {
        return this.sortIndex;
    }

    /**
     * set the sortIndex - 排序
     */
    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }

    public String getIhdStartRule() {
        return ihdStartRule;
    }

    public void setIhdStartRule(String ihdStartRule) {
        this.ihdStartRule = ihdStartRule;
    }

    public String getIhdStopRule() {
        return ihdStopRule;
    }

    public void setIhdStopRule(String ihdStopRule) {
        this.ihdStopRule = ihdStopRule;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the multiFlag - 多次标记
     *
     * @return the multiFlag
     */
    public String getMultiFlag() {
        return this.multiFlag;
    }

    /**
     * set the multiFlag - 多次标记
     */
    public void setMultiFlag(String multiFlag) {
        this.multiFlag = multiFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setProcedureCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("procedureCode")), procedureCode));
        setProcedureName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("procedureName")), procedureName));
        setStartTimeRule(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startTimeRule")), startTimeRule));
        setStopTimeRule(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stopTimeRule")), stopTimeRule));
        setIhdStartRule(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ihdStartRule")), ihdStartRule));
        setIhdStopRule(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ihdStopRule")), ihdStopRule));
        setRuleStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ruleStatus")), ruleStatus));
        setSortIndex(NumberUtils.toInteger(StringUtils.toString(map.get("sortIndex")), sortIndex));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setMultiFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("multiFlag")), multiFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("procedureCode", StringUtils.toString(procedureCode, eiMetadata.getMeta("procedureCode")));
        map.put("procedureName", StringUtils.toString(procedureName, eiMetadata.getMeta("procedureName")));
        map.put("startTimeRule", StringUtils.toString(startTimeRule, eiMetadata.getMeta("startTimeRule")));
        map.put("stopTimeRule", StringUtils.toString(stopTimeRule, eiMetadata.getMeta("stopTimeRule")));
        map.put("ihdStartRule", StringUtils.toString(ihdStartRule, eiMetadata.getMeta("ihdStartRule")));
        map.put("ihdStopRule", StringUtils.toString(ihdStopRule, eiMetadata.getMeta("ihdStopRule")));
        map.put("ruleStatus", StringUtils.toString(ruleStatus, eiMetadata.getMeta("ruleStatus")));
        map.put("sortIndex", StringUtils.toString(sortIndex, eiMetadata.getMeta("sortIndex")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("multiFlag", StringUtils.toString(multiFlag, eiMetadata.getMeta("multiFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));

        return map;

    }
}
