package com.baosight.imom.common.utils;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.spire.doc.interfaces.IField;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工程项目:.
 * 开发公司:Baosight Software LTD.co Copyright (c) 2022.
 * 类的简介:自动回填通用字段类-RecordUtils.
 * 类的描述:创建人工号、创建人姓名、创建时间等通用字段回填类.
 * 开发日期:2022年3月9日 上午9:00:00.
 *
 * <AUTHOR> TEL:17762608719 （开发人）.
 * @version 1.0 （开发版本）.
 * @since 1.8 （JDK版本号）.
 */
public class RecordUtils {

    private static final Logger logger = LoggerFactory.getLogger(RecordUtils.class);

    /**
     * 设置创建人信息
     * 创建人工号、创建人姓名、创建时间、内码(是否新增)
     *
     * @param map
     */
    public static void setCreator(Map map) {
        try {
            // 创建人工号
            map.put("recCreator", UserSession.getUserId());
            if (StringUtils.isBlank(UserSession.getUserId())){
                map.put("recCreator", MesConstant.SYSTEM);
            }

            // 创建人姓名
            map.put("recCreatorName", UserSession.getLoginCName());

            if (StringUtils.isBlank(UserSession.getLoginCName())){
                map.put("recCreatorName", MesConstant.SYSTEM);
            }

            // 创建时间
            map.put("recCreateTime", DateUtil.curDateTimeStr14());

            // 修改人工号
            map.put("recRevisor", UserSession.getUserId());
            if (StringUtils.isBlank(UserSession.getUserId())){
                map.put("recRevisor", MesConstant.SYSTEM);
            }
            // 修改人姓名
            map.put("recRevisorName", UserSession.getLoginCName());

            if (StringUtils.isBlank(UserSession.getLoginCName())){
                map.put("recRevisorName", MesConstant.SYSTEM);
            }

            // 修改时间
            map.put("recReviseTime", DateUtil.curDateTimeStr14());

            // UUID
            map.put("uuid", UUIDUtils.getUUID());

            // 删除标记: 0-新增;1-删除;
            map.put("delFlag", "0");
            map.put("archiveFlag", "0");
            map.put("tenantId", " ");

        } catch (PlatException ex) {
            logger.info("error", ex);
        }
    }


    /**
     * 叫号设置创建人信息
     * 创建人工号、创建人姓名、创建时间、内码(是否新增)
     *
     * @param map
     */
    public static void setCreatorBYCall(Map map) {
        try {

            map.put("recCreator", "system");

            map.put("recCreatorName", "system");

            map.put("recRevisor", "system");

            map.put("recRevisorName", "system");

            // 创建时间
            map.put("recCreateTime", DateUtil.curDateTimeStr14());

            // 修改时间
            map.put("recReviseTime", DateUtil.curDateTimeStr14());

            // UUID
            map.put("uuid", UUIDUtils.getUUID());

            // 删除标记: 0-新增;1-删除;
            map.put("delFlag", "0");
            map.put("archiveFlag", "0");
            map.put("tenantId", " ");

        } catch (PlatException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * 设置创建人信息,无session信息时设置为默认字段
     * 创建人工号、创建人姓名、创建时间、内码(是否新增)
     *
     * @param map
     */
    public static void setCreatorSys(Map map) {
        try {
            String userId = UserSession.getUserId();
            String userName = UserSession.getLoginCName();
            String date = DateUtil.curDateTimeStr14();
            if (userId == null || userName == null) {
                userId = MesConstant.SYSTEM;
                userName = userId;
            }
            // 创建人工号
            map.put("recCreator", userId);

            // 创建人姓名
            map.put("recCreatorName", userName);

            // 创建时间
            map.put("recCreateTime", date);

            // 修改人工号
            map.put("recRevisor", userId);

            // 修改人姓名
            map.put("recRevisorName", userName);

            // 修改时间
            map.put("recReviseTime", date);

            // UUID
            map.put("uuid", UUIDUtils.getUUID());

            // 删除标记: 0-新增;1-删除;
            map.put("delFlag", "0");
            map.put("archiveFlag", "0");
            map.put("tenantId", " ");

        } catch (PlatException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * 设置修改人信息(是否变更修改人岗位信息).
     * 修改人工号、修改人姓名、修改时间
     *
     * @param map
     */
    public static void setRevisor(Map map) {
        try {
            // 修改人工号
            map.put("recRevisor", UserSession.getUserId());
            if (StringUtils.isBlank(UserSession.getLoginCName())){
                map.put("recRevisor", MesConstant.SYSTEM);
            }

            // 修改人姓名
            map.put("recRevisorName", UserSession.getLoginCName());
            if (StringUtils.isBlank(UserSession.getLoginCName())){
                map.put("recRevisorName", MesConstant.SYSTEM);
            }

            // 修改时间
            map.put("recReviseTime", DateUtil.curDateTimeStr14());

            map.put("archiveFlag", "0");
            map.put("tenantId", " ");

        } catch (PlatException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * 设置修改人信息
     * 修改人工号、修改人姓名、修改时间
     *
     * @param map
     */
    public static void setRevisorSys(Map map) {
        try {
            String userId = UserSession.getUserId();
            String userName = UserSession.getLoginCName();
            String date = DateUtil.curDateTimeStr14();
            if (userId == null || userName == null) {
                userId = MesConstant.SYSTEM;
                userName = userId;
            }
            // 修改人工号
            map.put("recRevisor", userId);

            // 修改人姓名
            map.put("recRevisorName", userName);

            // 修改时间
            map.put("recReviseTime", date);

            map.put("archiveFlag", "0");
            map.put("tenantId", " ");

        } catch (PlatException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * 设置修改人信息(是否变更修改人岗位信息).
     * 修改人工号、修改人姓名、修改时间
     *
     * @param map
     */
    public static void setRevisorByCall(Map map) {
        try {
            // 修改人工号
            map.put("recRevisor", "system");

            // 修改人姓名
            map.put("recRevisorName", "system");

            // 修改时间
            map.put("recReviseTime", DateUtil.curDateTimeStr14());

            map.put("archiveFlag", "0");
            map.put("tenantId", " ");

        } catch (PlatException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * @return List<String>
     * @throws
     * @Service
     * @description 查询多条
     */
    public static List<String> queryMultipleItems(String arr) {
        List<String> stringList = new ArrayList<>();
        String replace = arr.replace("\n", ",");
        String[] arrList = replace.split(",");
        stringList.addAll(Arrays.asList(arrList));
        return stringList;
    }

    /**
     * 去除 String[] 数组中的空字符串("")
     *
     * @param arr 源数组
     * @return 操作后的新列表 List<>
     */
    public static List<String> removeArraysEmpty(String[] arr) {
        return Arrays.stream(arr).filter(s -> !"".equals(s)).collect(Collectors.toList());
    }

    /**
     * 生成IMC用户信息block
     *
     * @return EiBlock
     */
    public static EiBlock createLoginUserBlock() {
        String loginName = UserSession.getUserId();
        String userName = UserSession.getLoginCName();
        if (loginName == null || userName == null) {
            loginName = MesConstant.APP_CODE;
            userName = loginName;
        }
        EiBlock eiBlock = new EiBlock(MesConstant.LOGIN_USER);
        Map<String, Object> loginUser = new HashMap<>();
        loginUser.put(MesConstant.LOGIN_NAME, loginName);
        loginUser.put(MesConstant.USER_NAME, userName);
        eiBlock.addRow(loginUser);
        return eiBlock;
    }

    /**
     * 设置创建人信息
     * 创建人工号、创建人姓名、创建时间
     * 修改人工号、修改人姓名、修改时间
     *
     * @param bean
     */
    public static void setCreatorBeanSys(DaoEPBase bean) {
        try {
            // 创建人工号
            BeanUtils.setProperty(bean, "recCreator", "system");

            // 创建人姓名
            BeanUtils.setProperty(bean, "recCreatorName", "system");

            // 创建时间
            BeanUtils.setProperty(bean, "recCreateTime", DateUtil.curDateTimeStr14());

            // 修改人工号
            BeanUtils.setProperty(bean, "recRevisor", "system");

            // 修改人姓名
            BeanUtils.setProperty(bean, "recRevisorName", "system");

            // 修改时间
            BeanUtils.setProperty(bean, "recReviseTime", DateUtil.curDateTimeStr14());
        } catch (InvocationTargetException ex) {
            logger.info("error", ex);
        } catch (IllegalAccessException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * 设置修改人信息
     * 修改人工号、修改人姓名、修改时间
     *
     * @param bean
     */
    public static void setRevisorBeanSys(DaoEPBase bean) {
        try {
            // 修改人工号
            BeanUtils.setProperty(bean, "recRevisor", "system");

            // 修改人姓名
            BeanUtils.setProperty(bean, "recRevisorName", "system");

            // 修改时间
            BeanUtils.setProperty(bean, "recReviseTime", DateUtil.curDateTimeStr14());
        } catch (InvocationTargetException ex) {
            logger.info("error", ex);
        } catch (IllegalAccessException ex) {
            logger.info("error", ex);
        }
    }

    /**
     * 获取登录人信息
     * UserSession -> inInfo -> system
     *
     * @param
     */
    public static Map getLoginUser(EiInfo inInfo) {
        Map loginUser = new HashMap();
        try {
            String loginName = UserSession.getLoginName();
            String userId = UserSession.getUserId();
            String userName = UserSession.getLoginCName();
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(loginName) || StringUtils.isBlank(userName)) {
                loginName = inInfo.getString("userId");
                userId = inInfo.getString("userId");
                userName = inInfo.getString("userName");
                if (StringUtils.isBlank(userId) || StringUtils.isBlank(loginName) || StringUtils.isBlank(userName)) {
                    userId = MesConstant.SYSTEM;
                    userName = userId;
                    loginName = userId;
                }
            }
            loginUser.put("loginName", loginName);
            loginUser.put("userId", userId);
            loginUser.put("userName", userName);
        } catch (Exception ex) {
            loginUser.put("loginName", MesConstant.SYSTEM);
            loginUser.put("userId", MesConstant.SYSTEM);
            loginUser.put("userName", MesConstant.SYSTEM);
        }
        return loginUser;
    }
}
