package com.baosight.imom.vg.dm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.hdsdk.domain.data.HDRecord;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.common.websocket.TagRefreshClient;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.imom.vi.pm.domain.VIPM0009;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备管理模块大屏相关接口
 *
 * <AUTHOR> 郁在杰
 * @Description :大屏相关接口
 * @Date : 2025/3/17
 * @Version : 1.0
 */
public class ServiceVGDMInterfaceDp extends ServiceBase {

    /**
     * 查询当日点检计划信息
     * <p>
     * serviceId:S_VG_DM_0006
     */
    public EiInfo queryTodaySpotCheck(EiInfo inInfo) {
        try {
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("系统账套或设备代码为空");
            }
            log("查询点检参数：" + segNo + "|" + eArchivesNo);
            // 点检日期时获取当前日期
            String checkPlanDate = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            log("点检日期：" + checkPlanDate);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            queryMap.put("checkPlanDate", checkPlanDate);
            // 主项状态标记-非新增状态
            queryMap.put("notNewlyStatus", segNo);
            // 查询并返回列表
            List<Map> list = dao.query(VGDM0402.QUERY_FOR_DP, queryMap);
            inInfo.set("list", list);
            // 查询并返回待点检数
            queryMap.put("checkPlanSubStatus", "10");
            int count = super.count(VGDM0402.COUNT_WITH_MAIN, queryMap);
            inInfo.set("finishStatus", count > 0 ? -1 : 1);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询分部设备点位监控信息
     * <p>
     * serviceId:S_VG_DM_0007
     */
    public EiInfo queryDeviceMonitor(EiInfo inInfo) {
        try {
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            String deviceCode = inInfo.getString("deviceCode");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo) || StrUtil.isBlank(deviceCode)) {
                throw new PlatException("系统账套或设备代码为空");
            }
            log("查询设备参数：" + segNo + "|" + eArchivesNo + "|" + deviceCode);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            queryMap.put("deviceCode", deviceCode);
            queryMap.put("bigScreenFlag", "1");
            List<VGDM0303> list = dao.query(VGDM0303.QUERY, queryMap);
            List<String> tagIds = new ArrayList<>();
            for (VGDM0303 item : list) {
                tagIds.add(item.getScadaName() + "." + item.getTagId());
            }
            // 查询点位监控信息
            Map<String, String> resultMap = IplatUtils.readValues(tagIds);
            // 转换数据结构
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (VGDM0303 item : list) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("tagDesc", item.getTagDesc());
                dataMap.put("tagId", item.getTagId());
                dataMap.put("measureId", item.getMeasureId());
                dataMap.put("upperLimit", item.getUpperLimit());
                dataMap.put("lowerLimit", item.getLowerLimit());
                dataMap.put("value", resultMap.get(item.getTagId()));
                resultList.add(dataMap);
            }
            inInfo.set("result", resultList);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询设备关键点位监控信息
     * <p>
     * serviceId:S_VG_DM_0008
     */
    public EiInfo queryDeviceKeyMonitor(EiInfo inInfo) {
        try {
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("系统账套或设备代码为空");
            }
            log("查询设备参数：" + segNo + "|" + eArchivesNo);
            // 构建查询条件
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            queryMap.put("bigScreenKeyFlag", "1");
            List<VGDM0303> list = dao.query(VGDM0303.QUERY, queryMap);
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (VGDM0303 item : list) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("tagDesc", item.getTagDesc());
                dataMap.put("measureId", item.getMeasureId());
                dataMap.put("upperLimit", item.getUpperLimit());
                dataMap.put("lowerLimit", item.getLowerLimit());
                dataMap.put("tagId", item.getScadaName() + "." + item.getTagId());
                dataMap.put("deviceCode", item.getDeviceCode());
                resultList.add(dataMap);
            }
            inInfo.set("result", resultList);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 获取BA实时报警WebSocketCookie
     * <p>
     * serviceId:S_VG_DM_0011
     */
    public EiInfo getBaCookie(EiInfo inInfo) {
        try {
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("系统账套为空");
            }
            String cookie = TagRefreshClient.getCookie(segNo.substring(0, 2));
            inInfo.set("cookie", cookie);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询设备故障平均完成时间
     * <p>
     * serviceId:S_VG_DM_0019
     */
    public EiInfo queryFaultFinishTime(EiInfo inInfo) {
        try {
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("系统账套或设备代码为空");
            }
            String time = DateUtil.curDateTimeStr14();
            log("查询设备参数：" + segNo + "|" + eArchivesNo + "|" + time);
            Map<String, Integer> resultMap = new HashMap<>();
            resultMap.put("day", this.queryAvgTime(segNo, eArchivesNo, time.substring(0, 8)));
            resultMap.put("month", this.queryAvgTime(segNo, eArchivesNo, time.substring(0, 6)));
            resultMap.put("year", this.queryAvgTime(segNo, eArchivesNo, time.substring(0, 4)));
            // 返回
            inInfo.set("resultMap", resultMap);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询设备故障平均完成时间
     *
     * @param segNo       系统账套
     * @param eArchivesNo 设备代码
     * @param startTime   开始时间
     * @return 平均时间 s
     */
    private int queryAvgTime(String segNo, String eArchivesNo, String startTime) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("segNo", segNo);
        queryMap.put("eArchivesNo", eArchivesNo);
        queryMap.put("startTime", startTime);
        List<Map> list = dao.query(VGDM0701.QUERY_AVG_FINISH, queryMap);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        Map map = list.get(0);
        int totalSeconds = MapUtils.getInt(map, "totalSeconds");
        int count = MapUtils.getInt(map, "count");
        if (totalSeconds == 0 || count < 1) {
            return 0;
        }
        return totalSeconds / count;
    }

    /**
     * 查询设备故障平均间隔时间
     * <p>
     * serviceId:S_VG_DM_0020
     */
    public EiInfo queryFaultInterval(EiInfo inInfo) {
        try {
            // 获取查询条件
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            if (StrUtil.isBlank(segNo) || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("系统账套或设备代码为空");
            }
            String time = DateUtil.curDateTimeStr14();
            log("查询设备参数：" + segNo + "|" + eArchivesNo + "|" + time);
            Map<String, Integer> resultMap = new HashMap<>();
            resultMap.put("day", this.queryInterval(segNo, eArchivesNo, time.substring(0, 8)));
            resultMap.put("month", this.queryInterval(segNo, eArchivesNo, time.substring(0, 6)));
            resultMap.put("year", this.queryInterval(segNo, eArchivesNo, time.substring(0, 4)));
            // 返回
            inInfo.set("resultMap", resultMap);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询设备故障平均完成时间
     *
     * @param segNo       系统账套
     * @param eArchivesNo 设备代码
     * @param startTime   开始时间
     * @return 平均时间 s
     */
    private int queryInterval(String segNo, String eArchivesNo, String startTime) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("segNo", segNo);
        queryMap.put("eArchivesNo", eArchivesNo);
        queryMap.put("startTime", startTime);
        queryMap.put("orderBy", "FAULT_START_TIME ASC");
        List<VGDM0701> list = dao.query(VGDM0701.QUERY, queryMap);
        if (CollectionUtils.isEmpty(list) || list.size() < 2) {
            return 0;
        }
        VGDM0701 lastFault = list.get(0);
        long sumSeconds = 0;
        int count = 0;
        for (int i = 1; i < list.size(); i++) {
            VGDM0701 curFault = list.get(i);
            // 上一故障的结束时间
            LocalDateTime start = LocalDateTime.parse(lastFault.getFaultEndTime(), DateUtils.FORMATTER_14);
            // 本次故障开始时间
            LocalDateTime end = LocalDateTime.parse(curFault.getFaultStartTime(), DateUtils.FORMATTER_14);
            // 秒
            long seconds = ChronoUnit.SECONDS.between(start, end);
            sumSeconds += seconds;
            count++;
        }
        return (int) (sumSeconds / count);
    }


    /**
     * 通用调用IMC转发接口
     * <p>
     * serviceId:S_VG_DM_0099
     */
    public EiInfo commonCallIMC(EiInfo inInfo) {

        try {
            log("通用调用IMC转发接口-接收参数：" + inInfo.toJSONString());
            String serviceId = inInfo.getString("tranServiceId");
            //服务号
            inInfo.set(EiConstant.serviceId, serviceId);
            inInfo.set("clientId", "imomgfoPd2J6ZLcR6HqW");
            inInfo.set("clientSecret", "09F0ACC8FFD11EDA8F62B6B8FC471A8A");
            EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());

            //针对大数据的接口特殊处理返回
            if ("D_A_BG00YX00_JG_GM_0041_15".equals(serviceId)) {
                EiInfo outInfo1 = new EiInfo();
                EiBlock reusltBlock = outInfo.getBlock("result");
                List<Map<String, Object>> resultList = reusltBlock.getRows();
                List<Map<String, Object>> returnList = convertKeysToUpperCase(resultList);
                outInfo1.set("result", returnList);
                return outInfo1;
                //JSONObject jsonBlock = reusltBlock.toJSON();
            }

            if ("S_UC_EW_1239".equals(serviceId)) {
                EiInfo outInfo1 = new EiInfo();
                List<Map> resultList = (List<Map>) outInfo.get("result");
                List<Map> returnList = new ArrayList<>();
                // 将resultList中的数据根据showDay进行分组
                if (resultList != null && !resultList.isEmpty()) {
                    Map<Object, List<Map>> groupedMap = resultList.stream()
                            .collect(Collectors.groupingBy(m -> m.get("showDay")));
                    //outInfo1.set("result", groupedMap);


                    // 遍历groupedMap
                    for (Map.Entry<Object, List<Map>> entry : groupedMap.entrySet()) {
                        Map returnMap = new HashMap();
                        Object showDay = entry.getKey();
                        List<Map> groupList = entry.getValue();
                        // 这里可以根据需要对每个分组进行处理
                        // 例如打印日志或做其他业务处理
                        // log("showDay: " + showDay + "，分组数据条数: " + (groupList != null ? groupList.size() : 0));
                        returnMap.put("showDay", showDay);
                        int incount = 0;
                        int outcount = 0;
                        String segNo = (String) groupList.get(0).get("segNo");
                        for (Map group : groupList) {
                            int inCount1 = Integer.parseInt(group.get("inCount").toString());
                            int outCount1 = Integer.parseInt(group.get("outCount").toString());
                            incount = incount + inCount1;
                            outcount = outcount + outCount1;
                        }
                        returnMap.put("inCount", incount);
                        returnMap.put("outCount", outcount);
                        returnList.add(returnMap);
                    }
                    //retrunList按照showDay升序排
                    // retrunList按照showDay升序排
                    returnList.sort(Comparator.comparing(m -> m.get("showDay").toString()));
                    outInfo1.set("result", returnList);
                }
                return outInfo1;
            }

            return outInfo;
        } catch (Exception ex) {
            System.out.println("通用调用IMC转发接口-异常：" + ex.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }


    }

    /**
     * 批量转换Map的key为大写
     *
     * @param list
     * @return
     */
    public static List<Map<String, Object>> convertKeysToUpperCase(List<Map<String, Object>> list) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (list == null || list.isEmpty()) {
            return resultList;
        }
        for (Map<String, Object> map : list) {
            Map<String, Object> newMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                newMap.put(entry.getKey().toUpperCase(), entry.getValue()); // 转为大写[1,9](@ref)
            }
            resultList.add(newMap);
        }
        return resultList;
    }


    /**
     * 大屏查询年度加工量&达产率
     * S_VG_DM_0021
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryProcessingVolumeAndReachability(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        try {
            Map condition = new HashMap();
            condition.put("segNo", inInfo.get("segNo"));
            List<Map> resultList = dao.query(VGDM1201.QUERY_FOR_SCREEN, condition);
            outInfo.set("result", resultList);

        } catch (Exception ex) {
            System.out.println("查询设备参数-异常：" + ex.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 大屏查询年度全部数据
     * S_VG_DM_0027
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryAllVolume(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        try {
            Map condition = new HashMap();
            List<Map> resultList = dao.query(VGDM1201.QUERY_FOR_SCREEN_TWO, condition);
            outInfo.set("result", resultList);

        } catch (Exception ex) {
            System.out.println("年度全部数据-异常：" + ex.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 大屏查询当天OEE&故障率
     * S_VG_DM_0022
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryOEEAndFaultRate(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        String segNo = inInfo.getString("segNo");
        String ehArchivesNo = inInfo.getString("eArchivesNo");


        try {

            Map deviceCondition = new HashMap();
            deviceCondition.put("segNo", segNo);
            deviceCondition.put("eArchivesNo", ehArchivesNo);

            //查询当天的故障时间
            LocalDateTime startTime = LocalDate.now().atStartOfDay();
            deviceCondition.put("faultTime", cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
            List<Map> faultList = dao.query(VGDM0701.QUERY_DAY_FALUT_TIME, deviceCondition);
            BigDecimal faultTime = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(faultList)) {
                faultTime = BigDecimal.valueOf((Double) faultList.get(0).get("faultTime"));
            }
            outInfo.set("dayFaultTime", faultTime);

            //查询当天的OEE
            EiInfo oeeInfo = new EiInfo();
            oeeInfo.set("segNo", segNo);
            oeeInfo.set("e_archivesNo", ehArchivesNo);
            oeeInfo.set(EiConstant.serviceId, "S_VI_PM_1032");
            EiInfo imcOutInfo = EServiceManager.call(oeeInfo, TokenUtils.getXplatToken());
            if (imcOutInfo.getStatus() == EiConstant.STATUS_SUCCESS) {
                String oee = imcOutInfo.getString("OEE");
                String QR = imcOutInfo.getString("QR");
                String PER = imcOutInfo.getString("PER");
                //oee
                outInfo.set("dayOee", imcOutInfo.getString("OEE"));
                //合格品率
                outInfo.set("dayQr", imcOutInfo.getString("QR"));
                //性能开动率
                outInfo.set("dayPer", imcOutInfo.getString("PER"));
                //时间开动率
                outInfo.set("dayPet", imcOutInfo.getString("PET"));
            }
        } catch (Exception ex) {
            System.out.println("查询设备参数-异常：" + ex.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }

        return outInfo;
    }


    /**
     * 提供给IMC 查询设备运行时间
     * S_VG_DM_0022
     *
     * @param inInfo
     * @return
     */
    public EiInfo querDeviceRunTime(EiInfo inInfo) {

        EiInfo outinfo = new EiInfo();
        try {

            String segNo = inInfo.getString("segNo");
            String ehArchivesNo = inInfo.getString("eArchivesNo");
            //纵切线机
            if (ehArchivesNo.equals("52SL05")) {
                LocalDateTime startTime = LocalDate.now().atStartOfDay();
                LocalDateTime endTime = LocalDateTime.now();
                List tagIds = new ArrayList<>();
                tagIds.add("312");
                List<Map> tagValues = IhdSdkUtils.querySortedHisRecord(segNo, tagIds, cn.hutool.core.date.DateUtil.date(startTime), cn.hutool.core.date.DateUtil.date(endTime));
                int countSecond = CalculateUtils.calcuelateTagRangeTime(tagValues, "1");
                outinfo.set("deviceRuntime", countSecond / 60);
                //System.out.println("设备运行时间：" + countSecond);
            } else if (ehArchivesNo.equals("52CL01")) {
                LocalDateTime startTime = LocalDate.now().atStartOfDay();
                LocalDateTime endTime = LocalDateTime.now();
                List tagIds = new ArrayList<>();
                //油泵A运转
                tagIds.add("1667");
                List<Map> tagValues = IhdSdkUtils.querySortedHisRecord(segNo, tagIds, cn.hutool.core.date.DateUtil.date(startTime), cn.hutool.core.date.DateUtil.date(endTime));
                int countSecond = CalculateUtils.calcuelateTagRangeTime(tagValues, "1");
                outinfo.set("deviceRuntime", countSecond / 60);
            } else if (ehArchivesNo.equals("52CL02")) {
                LocalDateTime startTime = LocalDate.now().atStartOfDay();
                LocalDateTime endTime = LocalDateTime.now();
                List tagIds = new ArrayList<>();
                //液压站点位
                tagIds.add("293");
                List<Map> tagValues = IhdSdkUtils.querySortedHisRecord(segNo, tagIds, cn.hutool.core.date.DateUtil.date(startTime), cn.hutool.core.date.DateUtil.date(endTime));
                int countSecond = CalculateUtils.calcuelateTagRangeTime(tagValues, "1");
                outinfo.set("deviceRuntime", countSecond / 60);
            } else if (ehArchivesNo.equals("52BL01")) {
                LocalDateTime startTime = LocalDate.now().atStartOfDay();
                LocalDateTime endTime = LocalDateTime.now();
                List tagIds = new ArrayList<>();
                //M2.7
                tagIds.add("2083");
                List<Map> tagValues = IhdSdkUtils.querySortedHisRecord(segNo, tagIds, cn.hutool.core.date.DateUtil.date(startTime), cn.hutool.core.date.DateUtil.date(endTime));
                int countSecond = CalculateUtils.calcuelateTagRangeTime(tagValues, "1");
                outinfo.set("deviceRuntime", countSecond / 60);

            }


        } catch (Exception ex) {
            System.out.println("查询设备参数-异常：" + ex.getMessage());
            outinfo.setStatus(EiConstant.STATUS_FAILURE);
            outinfo.setMsg(ex.getMessage());
        }

        return outinfo;
    }

    /**
     * 查询设备报警状态 0未报警1有报警
     * <p>
     * serviceId:S_VG_DM_0025
     */
    public EiInfo queryDeviceAlarmStatus(EiInfo inInfo) {
        try {
            Map<String, String> map = new HashMap<>();
            List<Map> list = dao.query(VGDM0304.QUERY_FOR_DP, map);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("无配置信息");
            }
            log("待统计设备数：" + list.size());
            List<Map> rtnList = new ArrayList<>();
            map.put("alarmState", "未恢复");
            for (Map deviceMap : list) {
                String eArchivesNo = deviceMap.get("eArchivesNo").toString();
                String scadaName = deviceMap.get("scadaName").toString();
                String segNo = deviceMap.get("segNo").toString();
                map.put("eArchivesNo", eArchivesNo);
                map.put("scadaName", scadaName);
                map.put("segNo", segNo);
                List<VGDM0601> alarmList = dao.query(VGDM0601.QUERY, map);
                if (CollectionUtils.isEmpty(alarmList)) {
                    log("设备：" + eArchivesNo + "无未恢复报警信息");
                    deviceMap.put("alarmState", "0");
                    deviceMap.put("alarmInfo", "");
                } else {
                    log("设备：" + eArchivesNo + "报警数：" + alarmList.size());
                    StringBuilder sb = new StringBuilder();
                    for (VGDM0601 alarmInfo : alarmList) {
                        sb.append(alarmInfo.getAlarmTagDesc())
                                .append("-").append(alarmInfo.getAlarmType())
                                .append(":").append(alarmInfo.getAlarmTagValue())
                                .append(";");
                    }
                    deviceMap.put("alarmState", "1");
                    deviceMap.put("alarmInfo", sb.toString());
                }
                deviceMap.remove("scadaName");
                deviceMap.remove("deviceId");
                deviceMap.remove("deviceName");
                deviceMap.remove("scadaPrimaryIp");
                deviceMap.remove("equipmentIhdId");
                rtnList.add(deviceMap);
            }
            // 返回
            inInfo.set("list", rtnList);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询设备报警状态  0离线1暂停2运行
     * <p>
     * serviceId:S_VG_DM_0026
     */
    public EiInfo queryDeviceRunStatus(EiInfo inInfo) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("statusFlag", "1");
            List<Map> list = dao.query(VGDM0304.QUERY_FOR_DP, map);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("无配置信息");
            }
            log("待统计设备数：" + list.size());
            // 查询设备是否离线
            List<Map> rtnList = new ArrayList<>();
            Map<String, Map<String, String>> offlineMap = new HashMap<>();
            for (Map deviceMap : list) {
                String eArchivesNo = deviceMap.get("eArchivesNo").toString();
                String machineCode = deviceMap.get("machineCode").toString();
                String deviceId = deviceMap.get("deviceId").toString();
                String scadaName = deviceMap.get("scadaName").toString();
                String segNo = deviceMap.get("segNo").toString();
                String ip = deviceMap.get("scadaPrimaryIp").toString();
                String ihdId = deviceMap.get("equipmentIhdId").toString();
                log("设备：" + eArchivesNo + "查询设备状态,参数" + segNo + "|" + deviceId + "|" + scadaName + "|" + ip + "|" + ihdId);
                if (!offlineMap.containsKey(scadaName)) {
                    offlineMap.put(scadaName, queryScadaStatus(scadaName, ip));
                }
                String deviceStatus = checkDeviceStatus(deviceId, offlineMap.get(scadaName));
                if ("1".equals(deviceStatus)) {
                    deviceStatus = checkDeviceRun(segNo, ihdId);
                }
                deviceMap.put("deviceStatus", deviceStatus);
                deviceMap.put("processOrderId", queryProcessOrder(segNo, machineCode));
                deviceMap.remove("scadaName");
                deviceMap.remove("deviceId");
                deviceMap.remove("deviceName");
                deviceMap.remove("scadaPrimaryIp");
                deviceMap.remove("equipmentIhdId");
                rtnList.add(deviceMap);
            }
            // 返回
            inInfo.set("list", rtnList);
            // 返回成功标记
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 查询机组当前工单
     *
     * @param segNo       账套
     * @param machineCode 机组代码
     * @return 工单号
     */
    private String queryProcessOrder(String segNo, String machineCode) {
        log("机组：" + machineCode + "查询工单");
        String processOrderId = "";
        Map<String, String> map1 = new HashMap<>();
        map1.put("segNo", segNo);
        map1.put("machineCode", machineCode);
        map1.put("activeFlag", "1");
        map1.put("orderBy", "REC_CREATE_TIME DESC");
        List<VGDM1002> list = dao.query(VGDM1002.QUERY, map1);
        if (CollectionUtils.isNotEmpty(list)) {
            log("设备作业查询到工单：" + list.get(0).getProcessOrderId());
            processOrderId = list.get(0).getProcessOrderId();
            return processOrderId;
        }
        log("查询工单信息2");
        map1.put("processDemandProcessStatus", "30");
        List<VIPM0009> list1 = dao.query(VIPM0009.QUERY, map1);
        if (CollectionUtils.isNotEmpty(list1)) {
            log("查询到工单：" + list1.get(0).getProcessOrderId());
            processOrderId = list1.get(0).getProcessOrderId();
            return processOrderId;
        }
        log("未查询到工单信息");
        return processOrderId;
    }

    /**
     * 获取设备运行状态
     *
     * @param segNo 账套
     * @param ihdId 点位
     * @return 1暂停 2运行
     */
    private String checkDeviceRun(String segNo, String ihdId) throws Exception {
        log("运行状态查询:" + ihdId);
        if (StrUtil.isBlank(ihdId)) {
            return "2";
        }
        HDRecord record = IhdSdkUtils.querySnapshot(segNo, ihdId);
        if (record != null && "0".equals(record.getValueStr())) {
            return "1";
        }
        return "2";
    }

    /**
     * 获取设备在线状态
     *
     * @param deviceId  设备id，以,分割
     * @param statusMap 设备状态map
     * @return 0离线 1在线
     */
    private String checkDeviceStatus(String deviceId, Map<String, String> statusMap) {
        log("设备：" + deviceId + "在线状态查询");
        String[] idArr = deviceId.split(",");
        String deviceStatus = "1";
        for (String id : idArr) {
            String status = MapUtils.getStr(statusMap, id);
            if ("0".equals(status)) {
                deviceStatus = "0";
                break;
            }
        }
        log("在线状态：" + deviceStatus);
        return deviceStatus;
    }

    /**
     * 查询scada设备状态
     */
    private Map<String, String> queryScadaStatus(String scadaName, String ip) {
        log("查询设备在线状态：" + scadaName + "|" + ip);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("scadaName", scadaName);
        eiInfo.set("scadaIp", ip);
        eiInfo.set(EiConstant.serviceId, "S_BI_DX_36");
        EiInfo rtnInfo = XServiceManager.call(eiInfo);
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
        String returnValue = rtnInfo.getString("result");
        log("1设备在线状态：" + returnValue);
        JSONArray jsonArray = JSON.parseArray(returnValue);
        Map<String, String> rtnMap = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String deviceName = jsonObject.getString("name");
            int deviceStatus = jsonObject.getIntValue("status");
            rtnMap.put(deviceName, deviceStatus + "");
        }
        log("2设备在线状态：" + rtnMap);
        return rtnMap;
    }
}

