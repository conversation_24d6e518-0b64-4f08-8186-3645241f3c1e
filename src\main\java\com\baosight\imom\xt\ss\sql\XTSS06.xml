<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-12 15:06:22
       Version :  1.0
    tableName :meli.csaaaaaaaa
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     CONFIGURE_KEY  VARCHAR   NOT NULL,
     CONFIGURE_NAME  VARCHAR   NOT NULL,
     CONFIGURE_VALUE  VARCHAR,
     STATUS  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL
-->
<sqlMap namespace="XTSS06">
    <sql id="platSchema">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configureKey">
            CONFIGURE_KEY = #configureKey#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configureName">
            CONFIGURE_NAME = #configureName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configureValue">
            CONFIGURE_VALUE = #configureValue#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.xt.ss.domain.XTSS06">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        CONFIGURE_KEY as "configureKey",  <!-- 配置项代码 -->
        CONFIGURE_NAME as "configureName",  <!-- 配置项名称 -->
        CONFIGURE_VALUE as "configureValue",  <!-- 配置项集值 -->
        WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
        WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
        STATUS as "status",  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM ${platSchema}.XS_CONFIGURATION_INFORMATION WHERE 1=1 and DEL_FLAG = 0
        <include refid="platSchema"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${platSchema}.XS_CONFIGURATION_INFORMATION WHERE 1=1
        and DEL_FLAG = 0
        <isNotEmpty prepend=" AND " property="configureKey">
            CONFIGURE_KEY = #configureKey#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID != #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="warehouseCode">
            WAREHOUSE_CODE = #warehouseCode#
        </isNotEmpty>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configureKey">
            CONFIGURE_KEY = #configureKey#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configureName">
            CONFIGURE_NAME = #configureName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="configureValue">
            CONFIGURE_VALUE = #configureValue#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${platSchema}.XS_CONFIGURATION_INFORMATION (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        CONFIGURE_KEY,  <!-- 配置项代码 -->
        CONFIGURE_NAME,  <!-- 配置项名称 -->
        CONFIGURE_VALUE,  <!-- 配置项集值 -->
        STATUS,  <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID,  <!-- ID -->
        WAREHOUSE_CODE, <!-- 仓库代码 -->
        WAREHOUSE_NAME  <!-- 仓库名称 -->
        )
        VALUES (#segNo#, #unitCode#, #configureKey#, #configureName#, #configureValue#, #status#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#,
        #delFlag#, #uuid#,#warehouseCode#,#warehouseName#)
    </insert>

    <delete id="delete">
        UPDATE ${platSchema}.XS_CONFIGURATION_INFORMATION
        SET
        STATUS	= "00",   <!-- 状态(删除：00，新增：10) -->
        REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG	= "1"   <!-- 删除标记 -->
        WHERE 1=1 and UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${platSchema}.XS_CONFIGURATION_INFORMATION
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        CONFIGURE_KEY = #configureKey#,   <!-- 配置项代码 -->
        CONFIGURE_NAME = #configureName#,   <!-- 配置项名称 -->
        CONFIGURE_VALUE = #configureValue#,   <!-- 配置项集值 -->
        STATUS = #status#,   <!-- 状态(启用：10、停用：20) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        WAREHOUSE_CODE = #warehouseCode#, <!-- 仓库代码 -->
        WAREHOUSE_NAME = #warehouseName#, <!-- 仓库代码 -->
        UUID = #uuid#  <!-- ID -->
        WHERE UUID = #uuid#
    </update>

</sqlMap>