<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>工贸一体机</title>
        <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
        <link rel="stylesheet" type="text/css" href="css/sweetalert.css" />
        <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />
        <link rel="stylesheet" type="text/css" href="css/sweetalert2.min.css" />
        <style>
            #telKeyboard {
                display: none;
                position: absolute;
                flex-wrap: wrap;
                width: 60%;
                margin-top: 10px;
                background-color: white;
                border: 1px solid #ccc;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }

            .information3-zdy ul {
                display: flex;
                flex-direction: column;
                gap: 20px; /* 行间距 */
            }

            .row {
                margin-bottom: 0;
                display: flex;
                justify-content: space-between;
                gap: 20px; /* 列间距 */
            }

            .column {
                flex: 1; /* 每列占据相同宽度 */
            }

            .ipt-div-btn {
                display: flex;
                justify-content: space-between;
                gap: 10px;
            }
        </style>
        <script src="js/<EMAIL>"></script>
    </head>

    <body>
        <div class="wrapper">
            <div class="header">
                <div id="logo" class="logo-baosight"></div>
                <div class="header-return">
                    <button class="return-home-btn" onclick="returnHome()">返回主页</button>
                </div>
            </div>
            <div class="nav">
                <div class="navbox">
                    <ul>
                        <li class="fontblue">1.登记车牌号</li>
                        <li class="arrow"></li>
                        <li id="isTypeSpan">2.选择业务</li>
                        <li class="arrow"></li>
                        <li>3.进厂登记</li>
                    </ul>
                </div>
            </div>
            <div class="container" style="overflow-y: auto; max-height: calc(100vh - 200px); padding: 10px">
                <div class="main">
                    <div class="information3-zdy" style="padding-bottom: 0; width: 50%">
                        <ul>
                            <li class="row">
                                <div class="column" style="margin-bottom: 0">
                                    <span>车牌号</span>
                                    <div class="ipt-div-btn">
                                        <input id="province" type="text" class="ipt2" placeholder="请选择车牌号或手工输入车牌号" readonly />
                                    </div>
                                    <div class="vehicleNo"></div>
                                </div>
                                <div class="column">
                                    <span>手机号</span>
                                    <div class="ipt-div-btn">
                                        <input id="driverTel" type="tel" class="ipt2-tel" placeholder="请选择手机号或手工输入手机号" readonly />
                                    </div>
                                </div>
                            </li>
                            <li class="row" style="margin-bottom: 30px">
                                <div class="column">
                                    <span>提&nbsp;&nbsp;&nbsp;&nbsp;单</span>
                                    <div class="ipt-div-btn">
                                        <button id="loading" class="loading" value="10" onclick="selected(this.value)">有提单</button>
                                        <button id="discharge" class="discharge" value="20" onclick="selected(this.value)">无提单</button>
                                    </div>
                                </div>
                                <div class="column">
                                    <span>厂&nbsp;&nbsp;&nbsp;&nbsp;区</span>
                                    <div class="ipt-div-btn">
                                        <input id="factory_id" class="ipt2-cq" readonly="readonly" />
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div id="keyboard" style="display: none"></div>
                    <div id="telKeyboard" style="display: none"></div>
                    <div class="btn2">
                        <button class="btn2-flex-item" onclick="javascript :history.back(-1);">上一步</button>
                        <button class="btn2-flex-item" onclick="nextStep();">下一步</button>
                    </div>
                </div>

                <div class="div-wxts" style="margin-top: 50px; margin-left: 15%">
                    <span style="color: #ff0000">*</span>温馨提示：<br />
                    1、若
                    <p>已</p>
                    事先小程序预约，可直接选择车牌号，若
                    <p>无</p>
                    事先小程序预约，可手工
                    <p>输入车牌号</p>
                    。<br />
                    2、如果您的业务为
                    <p>钢材装货</p>
                    、
                    <p>钢材卸货+装货</p>
                    ，请选择
                    <p>“有提单”</p>
                    选项。<br />
                    如果您的业务为
                    <p>钢材卸货</p>
                    、
                    <p>托盘运输</p>
                    、
                    <p>废料提货</p>
                    、
                    <p>欧冶提货</p>
                    、
                    <p>资材卸货</p>
                    、
                    <p>其他物品运输</p>
                    ，请选择
                    <p>“无提单”</p>
                    。<br />
                    如果您的业务为
                    <p>废料提货</p>
                    ，但已经
                    <p>持有提单电子版</p>
                    或
                    <p>纸质版</p>
                    或
                    <p>已知晓提单号</p>
                    ，请选择
                    <p>“有提单”</p>
                    选项。
                </div>
            </div>
        </div>
        <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
        <script type="text/javascript" src="js/<EMAIL>"></script>
        <script type="text/javascript" src="js/config.js"></script>
        <script type="text/javascript" src="js/selfServiceMachineNext.js"></script>
    </body>
</html>
