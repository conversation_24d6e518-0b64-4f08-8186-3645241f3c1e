package com.baosight.imom.xt.ss.service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * 部门信息弹窗后台
 *
 * <AUTHOR> 郁在杰
 * @Description :
 * @Date : 2024/12/13
 * @Version : 1.0
 */
public class ServiceXTSS08 extends ServiceBase {

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询
     * <p>
     * serviceId: S_UC_EW_0765
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            // 1. 参数验证
            String segNo = validateAndGetSegNo(inInfo);
            // 2. 调用接口查询数据
            List<HashMap> returnList = queryDepartmentData(inInfo, segNo);
            // 3. 根据前端条件过滤
            returnList = filterDepartmentList(returnList, inInfo);
            // 4. 构建返回结果
            return buildQueryResult(inInfo, returnList);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            return inInfo;
        }
    }

    /**
     * 验证并获取账套
     */
    private String validateAndGetSegNo(EiInfo inInfo) throws PlatException {
        String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
        if (StrUtil.isBlank(segNo)) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_EMPTY_UNIT);
        }
        return segNo;
    }

    /**
     * 调用接口查询部门数据
     * <p>
     * serviceId: S_UC_EW_0765
     */
    private List<HashMap> queryDepartmentData(EiInfo inInfo, String segNo) throws PlatException {
        inInfo.set("related0SegNo", segNo);
        inInfo.set(EiConstant.serviceId, "S_UC_EW_0765");
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getImomToken());
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            throw new PlatException(outInfo.getMsg());
        }
        return (List<HashMap>) outInfo.get("returnList");
    }

    /**
     * 根据条件过滤部门列表
     */
    private List<HashMap> filterDepartmentList(List<HashMap> returnList, EiInfo inInfo) {
        if (CollectionUtils.isEmpty(returnList)) {
            return returnList;
        }
        String applyDept = inInfo.getCellStr(EiConstant.queryBlock, 0, "applyDept");
        String applyDeptName = inInfo.getCellStr(EiConstant.queryBlock, 0, "applyDeptName");
        // 合并过滤条件
        return returnList.stream()
                .filter(map -> (StrUtil.isBlank(applyDept) || map.get("segNo").toString().contains(applyDept))
                        && (StrUtil.isBlank(applyDeptName) || map.get("segName").toString().contains(applyDeptName)))
                .collect(Collectors.toList());
    }

    /**
     * 构建查询结果
     */
    private EiInfo buildQueryResult(EiInfo inInfo, List<HashMap> returnList) {
        if (CollectionUtils.isEmpty(returnList)) {
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_1001);
            return inInfo;
        }
        inInfo.addBlock(EiConstant.resultBlock).addRows(returnList);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_1003);
        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        return inInfo;
    }

}
