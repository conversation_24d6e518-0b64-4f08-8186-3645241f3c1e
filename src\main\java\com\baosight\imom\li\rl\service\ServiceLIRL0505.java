package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0109;
import com.baosight.imom.li.rl.dao.LIRL0504;
import com.baosight.imom.li.rl.dao.LIRL0505;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 谢徐皓
 * @Description: ${发送短信人员信息管理}
 * @Date: 2025/2/25 16:00
 * @Version: 1.0
 */
public class ServiceLIRL0505 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0504().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        outInfo = super.query(inInfo, LIRL0505.QUERY);
        return outInfo;
    }
    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                String perTel = MapUtils.getString(hashMap, "perTel", "");//人员手机号
                String sendType = MapUtils.getString(hashMap, "sendType", "");//发送类型
                String timeoutThreshold = MapUtils.getString(hashMap, "timeoutThreshold", "");//发送类型
                String typeOfHandling = MapUtils.getString(hashMap, "typeOfHandling", "");//发送类型
                if (StringUtils.isBlank(timeoutThreshold)){
                    String massage = "超时阈值不能为空!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }else {
                    hashMap.put("timeoutThreshold", Integer.parseInt(timeoutThreshold));
                }
                //查询数据是否重复
                Map query = new HashMap();
                query.put("segNo", segNo);
                query.put("perTel", perTel);
                query.put("sendType", sendType);
                query.put("typeOfHandling", typeOfHandling);
                query.put("timeoutThreshold", timeoutThreshold);
                query.put("delFlag", 0);
                int count = super.count(LIRL0505.COUNT, query);
                if (count > 0) {
                    String massage = "相同发送类型的手机号数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                String uuid = UUIDHexIdGenerator.generate().toString();
                hashMap.put("uuid", uuid);//UUID
                // 创建人工号
                hashMap.put("recCreator", UserSession.getUserId());
                // 创建人姓名
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", UserSession.getUserId());
                // 修改人姓名
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                hashMap.put("delFlag", "0");
                hashMap.put("archiveFlag", "0");
                hashMap.put("status", "10");
            }
            inInfo = super.insert(inInfo, LIRL0505.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                String perTel = MapUtils.getString(hashMap, "perTel", "");
                String sendType = MapUtils.getString(hashMap, "sendType", "");//人员类型
                String timeoutThreshold = MapUtils.getString(hashMap, "timeoutThreshold", "");//发送类型
                String typeOfHandling = MapUtils.getString(hashMap, "typeOfHandling", "");//发送类型
                if (StringUtils.isBlank(timeoutThreshold)){
                    String massage = "超时阈值不能为空!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                //查询数据是否重复
                Map queryCount = new HashMap();
                queryCount.put("segNo",segNo);
                queryCount.put("perTel",perTel);
                queryCount.put("delFlag", 0);
                queryCount.put("sendType", sendType);
                queryCount.put("typeOfHandling", typeOfHandling);
                queryCount.put("notUuid", uuid);
                queryCount.put("timeoutThreshold", timeoutThreshold);
                int count = super.count(LIRL0505.COUNT, queryCount);
                if (count>0){
                    String massage = "相同发送类型的手机号数据已存在!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0505> query = dao.query(LIRL0505.QUERY, map);
                for (LIRL0505 LIRL0505 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0505);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0505.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                String uuid = MapUtils.getString(hashMap, "uuid", "");
                if (org.apache.commons.lang.StringUtils.isBlank(uuid)) {
                    String massage = "记录不存在,不能删除!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0505> query = dao.query(LIRL0505.QUERY, map);
                for (LIRL0505 LIRL0505 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0505);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("delFlag", 1);//记录删除标记
                hashMap.put("status", "00");//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0505.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0505> query = dao.query(LIRL0505.QUERY, map);
                for (LIRL0505 LIRL0505 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0505);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "20");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0505.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNo(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                map.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                List<LIRL0505> query = dao.query(LIRL0505.QUERY, map);
                for (LIRL0505 LIRL0505 : query) {
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusConfirmNo(inInfo, LIRL0505);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "10");
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0505.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

}
