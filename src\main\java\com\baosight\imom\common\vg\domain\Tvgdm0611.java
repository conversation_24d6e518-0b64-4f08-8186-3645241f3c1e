/**
 * Generate time : 2025-07-30 13:24:51
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tvgdm0611
 *
 */
public class Tvgdm0611 extends DaoEPBase {

    private String alarmId = " ";        /* 报警ID*/
    private String scadaName = " ";        /* scada节点名*/
    private String alarmTag = " ";        /* 报警点*/
    private String alarmTagDesc = " ";        /* 报警点描述*/
    private String alarmAddress = " ";        /* 报警地址*/
    private String confirmStatus = " ";        /* 确认状态*/
    private String confirmor = " ";        /* 确认人*/
    private String confirmorName = " ";        /* 确认人姓名*/
    private String confirmTime = " ";        /* 确认时间*/
    private String occurTime = " ";        /* 发生时间*/
    private String recoverTime = " ";        /* 恢复时间*/
    private String alarmType = " ";        /* 报警类型*/
    private String alarmTagValue = " ";        /* 报警值*/
    private String recoverTagValue = " ";        /* 恢复值*/
    private Integer priority = 0;        /* 优先级*/
    private String alarmState = " ";        /* 报警状态*/
    private Integer repeatCount = 0;        /* 重复报警次数*/
    private String alarmTypedm = " ";        /* 报警类型代码*/
    private String deviceCode = " ";        /* 分部设备代码*/
    private String deviceName = " ";        /* 分部设备名称*/
    private String eArchivesNo = " ";        /* 设备档案编号*/
    private String equipmentName = " ";        /* 设备名称*/
    private String isFault = "0";        /* 是否故障*/
    private String pushHandleFlag = "0";        /* 推送处理标记*/
    private String pushHandleTime = " ";        /* 推送处理时间*/
    private String pushManageFlag = "0";        /* 推送管理标记*/
    private String pushManageTime = " ";        /* 推送管理时间*/
    private String archiveTime = " ";        /* 归档时间*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("alarmId");
        eiColumn.setDescName("报警ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scadaName");
        eiColumn.setDescName("scada节点名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmTag");
        eiColumn.setDescName("报警点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmTagDesc");
        eiColumn.setDescName("报警点描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmAddress");
        eiColumn.setDescName("报警地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("confirmStatus");
        eiColumn.setDescName("确认状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("confirmor");
        eiColumn.setDescName("确认人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("confirmorName");
        eiColumn.setDescName("确认人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("confirmTime");
        eiColumn.setDescName("确认时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("occurTime");
        eiColumn.setDescName("发生时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recoverTime");
        eiColumn.setDescName("恢复时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmType");
        eiColumn.setDescName("报警类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmTagValue");
        eiColumn.setDescName("报警值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recoverTagValue");
        eiColumn.setDescName("恢复值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("priority");
        eiColumn.setDescName("优先级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmState");
        eiColumn.setDescName("报警状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("repeatCount");
        eiColumn.setDescName("重复报警次数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("alarmTypedm");
        eiColumn.setDescName("报警类型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isFault");
        eiColumn.setDescName("是否故障");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pushHandleFlag");
        eiColumn.setDescName("推送处理标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pushHandleTime");
        eiColumn.setDescName("推送处理时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pushManageFlag");
        eiColumn.setDescName("推送管理标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pushManageTime");
        eiColumn.setDescName("推送管理时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveTime");
        eiColumn.setDescName("归档时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0611() {
        initMetaData();
    }

    /**
     * get the alarmId - 报警ID
     * @return the alarmId
     */
    public String getAlarmId() {
        return this.alarmId;
    }

    /**
     * set the alarmId - 报警ID
     */
    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }

    /**
     * get the scadaName - scada节点名
     * @return the scadaName
     */
    public String getScadaName() {
        return this.scadaName;
    }

    /**
     * set the scadaName - scada节点名
     */
    public void setScadaName(String scadaName) {
        this.scadaName = scadaName;
    }

    /**
     * get the alarmTag - 报警点
     * @return the alarmTag
     */
    public String getAlarmTag() {
        return this.alarmTag;
    }

    /**
     * set the alarmTag - 报警点
     */
    public void setAlarmTag(String alarmTag) {
        this.alarmTag = alarmTag;
    }

    /**
     * get the alarmTagDesc - 报警点描述
     * @return the alarmTagDesc
     */
    public String getAlarmTagDesc() {
        return this.alarmTagDesc;
    }

    /**
     * set the alarmTagDesc - 报警点描述
     */
    public void setAlarmTagDesc(String alarmTagDesc) {
        this.alarmTagDesc = alarmTagDesc;
    }

    /**
     * get the alarmAddress - 报警地址
     * @return the alarmAddress
     */
    public String getAlarmAddress() {
        return this.alarmAddress;
    }

    /**
     * set the alarmAddress - 报警地址
     */
    public void setAlarmAddress(String alarmAddress) {
        this.alarmAddress = alarmAddress;
    }

    /**
     * get the confirmStatus - 确认状态
     * @return the confirmStatus
     */
    public String getConfirmStatus() {
        return this.confirmStatus;
    }

    /**
     * set the confirmStatus - 确认状态
     */
    public void setConfirmStatus(String confirmStatus) {
        this.confirmStatus = confirmStatus;
    }

    /**
     * get the confirmor - 确认人
     * @return the confirmor
     */
    public String getConfirmor() {
        return this.confirmor;
    }

    /**
     * set the confirmor - 确认人
     */
    public void setConfirmor(String confirmor) {
        this.confirmor = confirmor;
    }

    /**
     * get the confirmorName - 确认人姓名
     * @return the confirmorName
     */
    public String getConfirmorName() {
        return this.confirmorName;
    }

    /**
     * set the confirmorName - 确认人姓名
     */
    public void setConfirmorName(String confirmorName) {
        this.confirmorName = confirmorName;
    }

    /**
     * get the confirmTime - 确认时间
     * @return the confirmTime
     */
    public String getConfirmTime() {
        return this.confirmTime;
    }

    /**
     * set the confirmTime - 确认时间
     */
    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    /**
     * get the occurTime - 发生时间
     * @return the occurTime
     */
    public String getOccurTime() {
        return this.occurTime;
    }

    /**
     * set the occurTime - 发生时间
     */
    public void setOccurTime(String occurTime) {
        this.occurTime = occurTime;
    }

    /**
     * get the recoverTime - 恢复时间
     * @return the recoverTime
     */
    public String getRecoverTime() {
        return this.recoverTime;
    }

    /**
     * set the recoverTime - 恢复时间
     */
    public void setRecoverTime(String recoverTime) {
        this.recoverTime = recoverTime;
    }

    /**
     * get the alarmType - 报警类型
     * @return the alarmType
     */
    public String getAlarmType() {
        return this.alarmType;
    }

    /**
     * set the alarmType - 报警类型
     */
    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    /**
     * get the alarmTagValue - 报警值
     * @return the alarmTagValue
     */
    public String getAlarmTagValue() {
        return this.alarmTagValue;
    }

    /**
     * set the alarmTagValue - 报警值
     */
    public void setAlarmTagValue(String alarmTagValue) {
        this.alarmTagValue = alarmTagValue;
    }

    /**
     * get the recoverTagValue - 恢复值
     * @return the recoverTagValue
     */
    public String getRecoverTagValue() {
        return this.recoverTagValue;
    }

    /**
     * set the recoverTagValue - 恢复值
     */
    public void setRecoverTagValue(String recoverTagValue) {
        this.recoverTagValue = recoverTagValue;
    }

    /**
     * get the priority - 优先级
     * @return the priority
     */
    public Integer getPriority() {
        return this.priority;
    }

    /**
     * set the priority - 优先级
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * get the alarmState - 报警状态
     * @return the alarmState
     */
    public String getAlarmState() {
        return this.alarmState;
    }

    /**
     * set the alarmState - 报警状态
     */
    public void setAlarmState(String alarmState) {
        this.alarmState = alarmState;
    }

    /**
     * get the repeatCount - 重复报警次数
     * @return the repeatCount
     */
    public Integer getRepeatCount() {
        return this.repeatCount;
    }

    /**
     * set the repeatCount - 重复报警次数
     */
    public void setRepeatCount(Integer repeatCount) {
        this.repeatCount = repeatCount;
    }

    /**
     * get the alarmTypedm - 报警类型代码
     * @return the alarmTypedm
     */
    public String getAlarmTypedm() {
        return this.alarmTypedm;
    }

    /**
     * set the alarmTypedm - 报警类型代码
     */
    public void setAlarmTypedm(String alarmTypedm) {
        this.alarmTypedm = alarmTypedm;
    }

    /**
     * get the deviceCode - 分部设备代码
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the isFault - 是否故障
     * @return the isFault
     */
    public String getIsFault() {
        return this.isFault;
    }

    /**
     * set the isFault - 是否故障
     */
    public void setIsFault(String isFault) {
        this.isFault = isFault;
    }

    /**
     * get the pushHandleFlag - 推送处理标记
     * @return the pushHandleFlag
     */
    public String getPushHandleFlag() {
        return this.pushHandleFlag;
    }

    /**
     * set the pushHandleFlag - 推送处理标记
     */
    public void setPushHandleFlag(String pushHandleFlag) {
        this.pushHandleFlag = pushHandleFlag;
    }

    /**
     * get the pushHandleTime - 推送处理时间
     * @return the pushHandleTime
     */
    public String getPushHandleTime() {
        return this.pushHandleTime;
    }

    /**
     * set the pushHandleTime - 推送处理时间
     */
    public void setPushHandleTime(String pushHandleTime) {
        this.pushHandleTime = pushHandleTime;
    }

    /**
     * get the pushManageFlag - 推送管理标记
     * @return the pushManageFlag
     */
    public String getPushManageFlag() {
        return this.pushManageFlag;
    }

    /**
     * set the pushManageFlag - 推送管理标记
     */
    public void setPushManageFlag(String pushManageFlag) {
        this.pushManageFlag = pushManageFlag;
    }

    /**
     * get the pushManageTime - 推送管理时间
     * @return the pushManageTime
     */
    public String getPushManageTime() {
        return this.pushManageTime;
    }

    /**
     * set the pushManageTime - 推送管理时间
     */
    public void setPushManageTime(String pushManageTime) {
        this.pushManageTime = pushManageTime;
    }

    /**
     * get the archiveTime - 归档时间
     * @return the archiveTime
     */
    public String getArchiveTime() {
        return this.archiveTime;
    }

    /**
     * set the archiveTime - 归档时间
     */
    public void setArchiveTime(String archiveTime) {
        this.archiveTime = archiveTime;
    }

    /**
     * get the uuid - 唯一编码
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {
        setAlarmId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmId")), alarmId));
        setScadaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scadaName")), scadaName));
        setAlarmTag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmTag")), alarmTag));
        setAlarmTagDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmTagDesc")), alarmTagDesc));
        setAlarmAddress(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmAddress")), alarmAddress));
        setConfirmStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("confirmStatus")), confirmStatus));
        setConfirmor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("confirmor")), confirmor));
        setConfirmorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("confirmorName")), confirmorName));
        setConfirmTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("confirmTime")), confirmTime));
        setOccurTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("occurTime")), occurTime));
        setRecoverTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recoverTime")), recoverTime));
        setAlarmType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmType")), alarmType));
        setAlarmTagValue(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmTagValue")), alarmTagValue));
        setRecoverTagValue(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recoverTagValue")), recoverTagValue));
        setPriority(NumberUtils.toInteger(StringUtils.toString(map.get("priority")), priority));
        setAlarmState(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmState")), alarmState));
        setRepeatCount(NumberUtils.toInteger(StringUtils.toString(map.get("repeatCount")), repeatCount));
        setAlarmTypedm(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("alarmTypedm")), alarmTypedm));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setIsFault(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isFault")), isFault));
        setPushHandleFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pushHandleFlag")), pushHandleFlag));
        setPushHandleTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pushHandleTime")), pushHandleTime));
        setPushManageFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pushManageFlag")), pushManageFlag));
        setPushManageTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pushManageTime")), pushManageTime));
        setArchiveTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveTime")), archiveTime));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {
        Map map = new HashMap();
        map.put("alarmId", StringUtils.toString(alarmId, eiMetadata.getMeta("alarmId")));
        map.put("scadaName", StringUtils.toString(scadaName, eiMetadata.getMeta("scadaName")));
        map.put("alarmTag", StringUtils.toString(alarmTag, eiMetadata.getMeta("alarmTag")));
        map.put("alarmTagDesc", StringUtils.toString(alarmTagDesc, eiMetadata.getMeta("alarmTagDesc")));
        map.put("alarmAddress", StringUtils.toString(alarmAddress, eiMetadata.getMeta("alarmAddress")));
        map.put("confirmStatus", StringUtils.toString(confirmStatus, eiMetadata.getMeta("confirmStatus")));
        map.put("confirmor", StringUtils.toString(confirmor, eiMetadata.getMeta("confirmor")));
        map.put("confirmorName", StringUtils.toString(confirmorName, eiMetadata.getMeta("confirmorName")));
        map.put("confirmTime", StringUtils.toString(confirmTime, eiMetadata.getMeta("confirmTime")));
        map.put("occurTime", StringUtils.toString(occurTime, eiMetadata.getMeta("occurTime")));
        map.put("recoverTime", StringUtils.toString(recoverTime, eiMetadata.getMeta("recoverTime")));
        map.put("alarmType", StringUtils.toString(alarmType, eiMetadata.getMeta("alarmType")));
        map.put("alarmTagValue", StringUtils.toString(alarmTagValue, eiMetadata.getMeta("alarmTagValue")));
        map.put("recoverTagValue", StringUtils.toString(recoverTagValue, eiMetadata.getMeta("recoverTagValue")));
        map.put("priority", StringUtils.toString(priority, eiMetadata.getMeta("priority")));
        map.put("alarmState", StringUtils.toString(alarmState, eiMetadata.getMeta("alarmState")));
        map.put("repeatCount", StringUtils.toString(repeatCount, eiMetadata.getMeta("repeatCount")));
        map.put("alarmTypedm", StringUtils.toString(alarmTypedm, eiMetadata.getMeta("alarmTypedm")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("isFault", StringUtils.toString(isFault, eiMetadata.getMeta("isFault")));
        map.put("pushHandleFlag", StringUtils.toString(pushHandleFlag, eiMetadata.getMeta("pushHandleFlag")));
        map.put("pushHandleTime", StringUtils.toString(pushHandleTime, eiMetadata.getMeta("pushHandleTime")));
        map.put("pushManageFlag", StringUtils.toString(pushManageFlag, eiMetadata.getMeta("pushManageFlag")));
        map.put("pushManageTime", StringUtils.toString(pushManageTime, eiMetadata.getMeta("pushManageTime")));
        map.put("archiveTime", StringUtils.toString(archiveTime, eiMetadata.getMeta("archiveTime")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        return map;
    }
}