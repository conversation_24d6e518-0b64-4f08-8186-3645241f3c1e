package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.xservices.em.util.SmsSendManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Author: 张博翔
 * @Description: ${车辆进出厂综合查询}
 * @Date: 2024/8/13 13:26
 * @Version: 1.0
 */
public class ServiceLIRL0405 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0108().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            outInfo = super.query(inInfo, LIRL0108.QUERY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ08";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                RecordUtils.setCreator(hashMap);
            }
            inInfo = super.insert(inInfo, LIRL0108.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0108> query = dao.query(LIRL0108.QUERY, map);
                for (LIRL0108 lirl0108 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0108);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0108.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result3").getRows();
            for (HashMap hashMap : listHashMap){
                EiInfo outInfo = new EiInfo();
                String vehicleNo = MapUtils.getString(hashMap,"vehicleNo","");;//车牌号
                String stringDate = DateUtil.curDateTimeStr14();//修改时间
                String segNo = MapUtils.getString(hashMap,"segNo","");
                String checkId = MapUtils.getString(hashMap,"checkId","");//车辆流水号
                String carTraceNo = MapUtils.getString(hashMap,"carTraceNo","");//车辆跟踪号
                String lirl0302status = MapUtils.getString(hashMap,"status","");//车辆登记单状态
                if (StringUtils.isBlank(carTraceNo)){
                    String massage = "该数据未进厂登记,请使用登记驳回!";
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg(massage);
                    return outInfo;
                }
                if (!"10".equals(lirl0302status)){
                    String massage = "只有登记进厂车辆，可以取消登记!";
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg(massage);
                    return outInfo;
                }
                Map queryMap = new HashMap();
                // queryMap.put("segNo", segNo);
                // queryMap.put("carTraceNo", carTraceNo);
                // queryMap.put("checkId", checkId);
                // queryMap.put("delFlag", 0);
                // List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryMap);
                // if (lirl0302s.size() > 0) {
                //     LIRL0302 lirl0302 = lirl0302s.get(0);
                //     vehicleNo = lirl0302.getVehicleNo();
                //     carTraceNo = lirl0302.getCarTraceNo();
                // }
                queryMap = new HashMap();
                queryMap.put("segNo", segNo);
                queryMap.put("vehicleNo", vehicleNo);
                queryMap.put("carTraceNo", carTraceNo);
                queryMap.put("delFlag", 0);
                List<HashMap> lirl0301s = this.dao.query("LIRL0301.queryDuplicateRegistration", queryMap);
                for (HashMap hashMap2 : lirl0301s) {
                    String status = MapUtils.getString(hashMap2, "status", "");
                    String lirl0301carTraceNo = MapUtils.getString(hashMap2, "carTraceNo", "");// 车辆追踪号
                    Integer statusNum = new Integer(status);
                    if (statusNum == 10) {
                        //如果车辆已经登记一次，但是车辆只在排队还没有>=叫号，是可以重复登记，将之前的登记数据撤销就行，重新插入一条
                        //取消登记同步撤销预排序数据
                        //删除预排序主表
                        Map deleteLIRL0303 = new HashMap<>();
                        deleteLIRL0303.put("segNo", segNo);
                        // deleteLIRL0303.put("vehicleNo", vehicleNo);
                        // deleteLIRL0303.put("vehicleId", vehicleNo);
                        deleteLIRL0303.put("carTraceNo", lirl0301carTraceNo);
                        RecordUtils.setRevisor(deleteLIRL0303);
                        dao.delete(LIRL0303.DELETE, deleteLIRL0303);
                        //从装卸子表中删除数据
                        dao.delete(LIRL0305.DELETE, deleteLIRL0303);
                        //如果在超时表 从超时表中删掉
                        dao.delete(LIRL0403.DELETE, deleteLIRL0303);
                        //从车辆排序临时表删除
                        dao.delete(LIRL0310.DELETE, deleteLIRL0303);

                        //撤销车辆跟踪表
                        RecordUtils.setRevisor(hashMap2);
                        dao.delete(LIRL0301.DELETE, hashMap2);

                        //删除车辆排序主表
                        Map deleteLIRL0401 = new HashMap();
                        deleteLIRL0401.put("segNo",segNo);
                        deleteLIRL0401.put("carTraceNo",lirl0301carTraceNo);
                        // deleteLIRL0401.put("vehicleNo",vehicleNo);
                        dao.delete(LIRL0401.DELETE,deleteLIRL0401);

                        //删除车辆排序主表
                        Map deleteLIRL0403 = new HashMap();
                        deleteLIRL0403.put("segNo",segNo);
                        deleteLIRL0403.put("carTraceNo",lirl0301carTraceNo);
                        // deleteLIRL0403.put("vehicleNo",vehicleNo);
                        dao.delete(LIRL0403.DELETE,deleteLIRL0403);

                        //撤销登记表
                        Map updateLIRL0302 = new HashMap();
                        updateLIRL0302.put("segNo", segNo);
                        updateLIRL0302.put("status", "00");
                        RecordUtils.setRevisor(updateLIRL0302);
                        updateLIRL0302.put("sysRemark", "车辆重复登记,撤销此次登记");// 备注
                        updateLIRL0302.put("carTraceNo", lirl0301carTraceNo);// 车辆追踪号
                        dao.update("LIRL0302.updateTranslation", updateLIRL0302);

                        //检查叫号表是否有数据
                        Map queryLIRL0402 = new HashMap();
                        queryLIRL0402.put("segNo",segNo);
                        // queryLIRL0402.put("vehicleNo",vehicleNo);
                        queryLIRL0402.put("carTraceNo",lirl0301carTraceNo);
                        List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
                        if (lirl0402s.size() > 0) {
                            LIRL0402 lirl0402 = lirl0402s.get(0);
                            String lv_min_hand_point = lirl0402.getTargetHandPointId();
                            //如果在叫号表 从叫号表中删掉
                            dao.delete(LIRL0402.DELETE, queryLIRL0402);
                            //叫号表数据删除后，装卸点的进车跟踪要随之改变
                            outInfo = new ServiceLIRLInterface().updateHandPointJobNumber(segNo, lv_min_hand_point, 0, -1, 31);
                            if (outInfo.getStatus() < 0) {
                                return outInfo;
                            }
                        }
                        //删除补录的预约单号
                        Map deleteLIRL0201 = new HashMap();
                        deleteLIRL0201.put("segNo",segNo);
                        deleteLIRL0201.put("reservationNumber",MapUtils.getString(hashMap2,"reservationNumber",""));
                        RecordUtils.setRevisor(deleteLIRL0201);
                        dao.update(LIRL0201.DELETE_BY_RESERVATION_NUMBER,deleteLIRL0201);

                    } else if (statusNum > 10 && statusNum < 50) {
                        String massage = "车辆登记号:"+checkId+"车牌号:" + vehicleNo + "已进厂,不能撤销登记!";
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg(massage);
                        return outInfo;
                    }
                }
            }

            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * S_UC_PR_020801
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryEnterFactoryCarList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            queryMap.put("status", "10");
            queryMap.put("delFlag", "0");
            outInfo = super.query(inInfo, LIRL0301.QUERY_ENTER_FACTORY_CAR_LIST);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * S_UC_PR_020801
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryPutoutCarList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            queryMap.put("statusListStr", "'40'");
            queryMap.put("delFlag", "0");
            queryMap.put("factoryFlag","no");
            outInfo = super.query(inInfo, LIRL0301.QUERY, new LIRL0301(), false, null, EiConstant.queryBlock, "result2", "result2");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * S_UC_PR_020801
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySingInCarList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            queryMap.put("status", "10");
            queryMap.put("delFlag", "0");
            outInfo = super.query(inInfo, LIRL0301.QUERY_SING_IN_CAR_LIST, new LIRL0301(), false, null, EiConstant.queryBlock, "result3", "result3");

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }
}
