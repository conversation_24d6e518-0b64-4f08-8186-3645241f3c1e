package com.baosight.imom.vf.pm.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * <AUTHOR> yzj
 * @Description : 资材领用申请页面后台
 * @Date : 2024/11/27
 * @Version : 1.0
 */
public class ServiceVFPM0101 extends ServiceBase {

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 库存查询
     * <p>
     * serviceId: S_VF_PM_9028
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            String deptId = inInfo.getCellStr(EiConstant.queryBlock, 0, "deptId");
            if (StrUtil.isBlank(deptId)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("缺少领用部门不能查询!");
                return inInfo;
            }
            // 分页条件
            EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
            // 查询条件
            String warehouseCode = inInfo.getCellStr(EiConstant.queryBlock, 0, "warehouseCode");
            String stuffCode = inInfo.getCellStr(EiConstant.queryBlock, 0, "stuffCode");
            String stuffName = inInfo.getCellStr(EiConstant.queryBlock, 0, "stuffName");
            String stuffModel= inInfo.getCellStr(EiConstant.queryBlock, 0, "stuffModel");
            // 查询参数
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("segNo", inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo"));
            eiInfo.set("warehouseCode", warehouseCode);
            eiInfo.set("stuffCode", stuffCode);
            eiInfo.set("stuffName", stuffName);
            eiInfo.set("stuffModel", stuffModel);
            // 分页参数
            eiInfo.set(EiConstant.offsetStr, resultBlock.get(EiConstant.offsetStr));
            eiInfo.set(EiConstant.limitStr, resultBlock.get(EiConstant.limitStr));
            // 服务ID
            eiInfo.set(EiConstant.serviceId, "S_VF_PM_9028");
            EiInfo rtnInfo = EServiceManager.call(eiInfo, TokenUtils.getImomToken());
            if (rtnInfo.getStatus() < 0) {
                throw new PlatException(rtnInfo.getMsg());
            }
            // 设置返回结果
            inInfo.setBlock(rtnInfo.getBlock(EiConstant.resultBlock));
            // 前端消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_1003);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }
}
