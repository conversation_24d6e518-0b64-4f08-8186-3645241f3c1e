/**
 * Generate time : 2024-09-04 14:39:25
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0301 车辆跟踪表
 *
 */
public class LIRL0301 extends DaoEPBase {
    public static final String QUERY = "LIRL0301.query";
    public static final String QUERY_USER = "LIRL0301.queryUser";
    public static final String QUREY_VEHICLE_LIST_CC = "LIRL0301.qureyVehicleListCC";
    public static final String QUREY_VEHICLE_LIST_HC = "LIRL0301.qureyVehicleListHC";
    public static final String QUERY_LADING_INFO = "LIRL0301.queryLadingInfo";
    public static final String QUERY_LADING_VEHICLE = "LIRL0301.queryLadingVehicle";
    public static final String QUERY_K_BDATA = "LIRL0301.queryKBdata";
    public static final String QUERY_K_BDATA_COUNT = "LIRL0301.queryKBdataCount";
    public static final String QUERY_VEHICLE = "LIRL0301.queryVehicle";
    public static final String QUERY_VEHICLE_C_Q = "LIRL0301.queryVehicleCQ";
    public static final String QUERY_NEXT_TARGET_POINT = "LIRL0301.queryNextTargetPoint";
    public static final String COUNT = "LIRL0301.count";
    public static final String INSERT = "LIRL0301.insert";
    public static final String UPDATE = "LIRL0301.update";
    public static final String BACK_ALLOCATE_VEHICLE_NO = "LIRL0301.backAllocateVehicleNo";
    public static final String UPDATE_TARGET_HAND_POINT = "LIRL0301.updateTargetHandPoint";
    public static final String DELETE = "LIRL0301.delete";
    public static final String DELETE_STATUS = "LIRL0301.deleteStatus";
    public static final String QUERY_HAND_POINT_OCCUPY = "LIRL0301.queryHandPointOccupy";
    public static final String QUERY_ENTER_FACTORY_CAR_LIST = "LIRL0301.queryEnterFactoryCarList";
    public static final String QUERY_SING_IN_CAR_LIST = "LIRL0301.querySingInCarList";
    public static final String UPDATE_LIRL0301JC = "LIRL0301.updateLIRL0301JC";
    public static final String UPDATE_LIRL0301CC = "LIRL0301.updateLIRL0301CC";
    public static final String UPDATE_LIRL0301TARGET_HAND_POINT_ID = "LIRL0301.updateLIRL0301TargetHandPointId";
    public static final String UPDATE_LIRL0301JSZX = "LIRL0301.updateLIRL0301JSZX";
    public static final String UPDATE_LIRL0301JSZX2 = "LIRL0301.updateLIRL0301JSZX2";
    public static final String REVOKE_LIRL0301BY_CAR_TRACE_NO = "LIRL0301.revokeLIRL0301ByCarTraceNo";
    public static final String QUERY_RIGHT_HAND_POINT_ID = "LIRL0301.queryRightHandPointId";
    public static final String TODAY_LOADING_AND_UNLOADING_KANBAN = "LIRL0301.todayLoadingAndUnloadingKanban";
    public static final String QUERY_COUNT_BY_HAND_POINT = "LIRL0301.queryCountByHandPoint";
    public static final String QUERY_SITE_NAME = "LIRL0301.querySiteName2";
    public static final String QUERY11 = "LIRL0301.querySiteName4";
    public static final String QUERY12 = "LIRL0301.querySiteName3";
    public static final String QUERY_RESERVATION_DATE = "LIRL0301.queryReservationDate";
    public static final String QUERY_DRIVER_INFO = "LIRL0301.queryDriverInfo";
    public static final String QUERY_VEHICLE_COUNT_CQ = "LIRL0301.queryVehicleCountCq";
    public static final String QUERY_PUTOUT_CAR = "LIRL0301.queryPutoutCar";
    public static final String QUERY_LOADING_CAR = "LIRL0301.queryLoadingCar";
    public static final String UPDATE_ALLOCATE_VEHICLE_NO = "LIRL0301.updateAllocateVehicleNo";
    public static final String QUERY_CALL_NO = "LIRL0301.queryCallNo";
    public static final String QUERY_WASTE_VEHICLE_NO = "LIRL0301.queryWasteVehicleNo";
    public static final String UPDATE_L_I_R_L0301_PAUSE = "LIRL0301.updateLIRL0301Pause";
    public static final String QUERY_HAND_POINT_EXZIST = "LIRL0301.queryHandPointExzist";
    public static final String QUERY_SPOT_VEHICLE = "LIRL0301.querySpotVehicle";
    public static final String UPDATE_TEL_BY_CAR = "LIRL0301.updateTelByCar";

    private String segNo = " ";        /* 系统账套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String carTraceNo = " ";        /* 车辆跟踪号*/
    private String status = " ";        /* 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）*/
    private String handType = " ";        /* 装卸类型(10 装 20卸 30装卸)*/
    private String vehicleNo = " ";        /* 车牌号*/
    private String idCard = " ";        /* 身份证号*/
    private String driverName = " ";        /* 驾驶员姓名*/
    private String telNum = " ";        /* 手机号*/
    private String reservationNumber = " ";        /* 车辆预约单号*/
    private String checkDate = " ";        /* 进厂登记时间*/
    private String enterFactory = " ";        /* 入厂时间*/
    private String beginEntruckingTime = " ";        /* 作业开始时间*/
    private String completeUninstallTime = " ";        /* 作业结束时间*/
    private String leaveFactoryDate = " ";        /* 出厂时间*/
    private String customerSigningTime = " ";        /* 客户签约时间*/
    private String targetHandPointId = " ";        /* 目标装卸点代码*/
    private String currentHandPointId = " ";        /* 当前装卸点代码*/
    private String factoryArea = " ";        /* 厂区*/
    private String factoryAreaName = " ";        /* 厂区名称*/
    private String unloadLeaveFlag = " ";        /* 未装离厂标记*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private String handPointId = " ";        /* 装卸点代码*/
    private String factoryType = " ";        /* 10：首次进厂 20：厂内周转*/
    private String targetHandPointIdName = " ";        /* 目标装卸点名称*/
    private String currentHandPointIdName = " ";        /* 当前装卸点名称*/
    private String startOfTransport = " ";        /* 起始地*/
    private String expectedLoadingTime = " ";        /* 预计装货时间*/
    private String typeOfHandling = " ";        /* 装卸类型*/
    private String allocateVehicleNo = " ";        /* 配车单号*/
    private String callTime = " ";        /* 第一次叫号时间*/
    private String ifPause = " ";        /* 是否暂停*/
    private String pauseTime = " ";        /* 暂停时间*/

    /**
     * the constructor
     */
    public LIRL0301() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handType");
        eiColumn.setDescName("装卸类型(10 装 20卸 30装卸)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("idCard");
        eiColumn.setDescName("身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("驾驶员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("telNum");
        eiColumn.setDescName("手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setDescName("车辆预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setDescName("进厂登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactory");
        eiColumn.setDescName("入厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("beginEntruckingTime");
        eiColumn.setDescName("作业开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("completeUninstallTime");
        eiColumn.setDescName("作业结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerSigningTime");
        eiColumn.setDescName("客户签约时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointId");
        eiColumn.setDescName("目标装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointId");
        eiColumn.setDescName("当前装卸点代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unloadLeaveFlag");
        eiColumn.setDescName("未装离厂标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handPointId");
        eiColumn.setDescName("装卸点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryType");
        eiColumn.setDescName("10：首次进厂 20：厂内周转");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointIdName");
        eiColumn.setDescName("目标装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointIdName");
        eiColumn.setDescName("当前装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startOfTransport");
        eiColumn.setDescName("起始地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("expectedLoadingTime");
        eiColumn.setDescName("预计装货时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("typeOfHandling");
        eiColumn.setDescName("装卸类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocateVehicleNo");
        eiColumn.setDescName("配车单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callTime");
        eiColumn.setDescName("第一次叫号时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifPause");
        eiColumn.setDescName("是否暂停");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pauseTime");
        eiColumn.setDescName("暂停时间");
        eiMetadata.addMeta(eiColumn);

    }

    public String getExpectedLoadingTime() {
        return expectedLoadingTime;
    }

    public void setExpectedLoadingTime(String expectedLoadingTime) {
        this.expectedLoadingTime = expectedLoadingTime;
    }

    public String getStartOfTransport() {
        return startOfTransport;
    }

    public void setStartOfTransport(String startOfTransport) {
        this.startOfTransport = startOfTransport;
    }

    /**
     * get the segNo - 系统账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the carTraceNo - 车辆跟踪号
     * @return the carTraceNo
     */
    public String getCarTraceNo() {
        return this.carTraceNo;
    }

    /**
     * set the carTraceNo - 车辆跟踪号
     */
    public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
    }

    /**
     * get the status - 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态（00撤销、生成05、进厂登记10、车辆进厂20、开始装卸货30、结束装卸货40、车辆出厂50、车辆签收60）
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the handType - 装卸类型(10 装 20卸 30装卸)
     * @return the handType
     */
    public String getHandType() {
        return this.handType;
    }

    /**
     * set the handType - 装卸类型(10 装 20卸 30装卸)
     */
    public void setHandType(String handType) {
        this.handType = handType;
    }

    /**
     * get the vehicleNo - 车牌号
     * @return the vehicleNo
     */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
     * set the vehicleNo - 车牌号
     */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    /**
     * get the idCard - 身份证号
     * @return the idCard
     */
    public String getIdCard() {
        return this.idCard;
    }

    /**
     * set the idCard - 身份证号
     */
    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    /**
     * get the driverName - 驾驶员姓名
     * @return the driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * set the driverName - 驾驶员姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * get the telNum - 手机号
     * @return the telNum
     */
    public String getTelNum() {
        return this.telNum;
    }

    /**
     * set the telNum - 手机号
     */
    public void setTelNum(String telNum) {
        this.telNum = telNum;
    }

    /**
     * get the reservationNumber - 车辆预约单号
     * @return the reservationNumber
     */
    public String getReservationNumber() {
        return this.reservationNumber;
    }

    /**
     * set the reservationNumber - 车辆预约单号
     */
    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    /**
     * get the checkDate - 进厂登记时间
     * @return the checkDate
     */
    public String getCheckDate() {
        return this.checkDate;
    }

    /**
     * set the checkDate - 进厂登记时间
     */
    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    /**
     * get the enterFactory - 入厂时间
     * @return the enterFactory
     */
    public String getEnterFactory() {
        return this.enterFactory;
    }

    /**
     * set the enterFactory - 入厂时间
     */
    public void setEnterFactory(String enterFactory) {
        this.enterFactory = enterFactory;
    }

    /**
     * get the beginEntruckingTime - 作业开始时间
     * @return the beginEntruckingTime
     */
    public String getBeginEntruckingTime() {
        return this.beginEntruckingTime;
    }

    /**
     * set the beginEntruckingTime - 作业开始时间
     */
    public void setBeginEntruckingTime(String beginEntruckingTime) {
        this.beginEntruckingTime = beginEntruckingTime;
    }

    /**
     * get the completeUninstallTime - 作业结束时间
     * @return the completeUninstallTime
     */
    public String getCompleteUninstallTime() {
        return this.completeUninstallTime;
    }

    /**
     * set the completeUninstallTime - 作业结束时间
     */
    public void setCompleteUninstallTime(String completeUninstallTime) {
        this.completeUninstallTime = completeUninstallTime;
    }

    /**
     * get the leaveFactoryDate - 出厂时间
     * @return the leaveFactoryDate
     */
    public String getLeaveFactoryDate() {
        return this.leaveFactoryDate;
    }

    /**
     * set the leaveFactoryDate - 出厂时间
     */
    public void setLeaveFactoryDate(String leaveFactoryDate) {
        this.leaveFactoryDate = leaveFactoryDate;
    }

    /**
     * get the customerSigningTime - 客户签约时间
     * @return the customerSigningTime
     */
    public String getCustomerSigningTime() {
        return this.customerSigningTime;
    }

    /**
     * set the customerSigningTime - 客户签约时间
     */
    public void setCustomerSigningTime(String customerSigningTime) {
        this.customerSigningTime = customerSigningTime;
    }

    /**
     * get the targetHandPointId - 目标装卸点代码
     * @return the targetHandPointId
     */
    public String getTargetHandPointId() {
        return this.targetHandPointId;
    }

    /**
     * set the targetHandPointId - 目标装卸点代码
     */
    public void setTargetHandPointId(String targetHandPointId) {
        this.targetHandPointId = targetHandPointId;
    }

    /**
     * get the currentHandPointId - 当前装卸点代码
     * @return the currentHandPointId
     */
    public String getCurrentHandPointId() {
        return this.currentHandPointId;
    }

    /**
     * set the currentHandPointId - 当前装卸点代码
     */
    public void setCurrentHandPointId(String currentHandPointId) {
        this.currentHandPointId = currentHandPointId;
    }

    /**
     * get the factoryArea - 厂区
     * @return the factoryArea
     */
    public String getFactoryArea() {
        return this.factoryArea;
    }

    /**
     * set the factoryArea - 厂区
     */
    public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
    }

    /**
     * get the unloadLeaveFlag - 未装离厂标记
     * @return the unloadLeaveFlag
     */
    public String getUnloadLeaveFlag() {
        return this.unloadLeaveFlag;
    }

    /**
     * set the unloadLeaveFlag - 未装离厂标记
     */
    public void setUnloadLeaveFlag(String unloadLeaveFlag) {
        this.unloadLeaveFlag = unloadLeaveFlag;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getHandPointId() {
        return handPointId;
    }

    public void setHandPointId(String handPointId) {
        this.handPointId = handPointId;
    }

    /**
     * get the factoryType - 10：首次进厂 20：厂内周转
     * @return the factoryType
     */
    public String getFactoryType() {
        return this.factoryType;
    }

    /**
     * set the factoryType - 10：首次进厂 20：厂内周转
     */
    public void setFactoryType(String factoryType) {
        this.factoryType = factoryType;
    }

    public String getFactoryAreaName() {
        return factoryAreaName;
    }

    public void setFactoryAreaName(String factoryAreaName) {
        this.factoryAreaName = factoryAreaName;
    }
    /**
     * get the targetHandPointIdName - 目标装卸点名称
     * @return the targetHandPointIdName
     */
    public String getTargetHandPointIdName() {
        return this.targetHandPointIdName;
    }

    /**
     * set the targetHandPointIdName - 目标装卸点名称
     */
    public void setTargetHandPointIdName(String targetHandPointIdName) {
        this.targetHandPointIdName = targetHandPointIdName;
    }
    /**
     * get the currentHandPointIdName - 当前装卸点名称
     * @return the currentHandPointIdName
     */
    public String getCurrentHandPointIdName() {
        return this.currentHandPointIdName;
    }

    /**
     * set the currentHandPointIdName - 当前装卸点名称
     */
    public void setCurrentHandPointIdName(String currentHandPointIdName) {
        this.currentHandPointIdName = currentHandPointIdName;
    }

    public String getTypeOfHandling() {
        return typeOfHandling;
    }

    public void setTypeOfHandling(String typeOfHandling) {
        this.typeOfHandling = typeOfHandling;
    }

    public String getAllocateVehicleNo() {
        return allocateVehicleNo;
    }

    public void setAllocateVehicleNo(String allocateVehicleNo) {
        this.allocateVehicleNo = allocateVehicleNo;
    }

    public String getCallTime() {
        return callTime;
    }

    public void setCallTime(String callTime) {
        this.callTime = callTime;
    }

    public String getIfPause() {
        return ifPause;
    }

    public void setIfPause(String ifPause) {
        this.ifPause = ifPause;
    }

    public String getPauseTime() {
        return pauseTime;
    }

    public void setPauseTime(String pauseTime) {
        this.pauseTime = pauseTime;
    }

    /**
     *
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setHandType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handType")), handType));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setIdCard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("idCard")), idCard));
        setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
        setTelNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("telNum")), telNum));
        setReservationNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationNumber")), reservationNumber));
        setCheckDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkDate")), checkDate));
        setEnterFactory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("enterFactory")), enterFactory));
        setBeginEntruckingTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("beginEntruckingTime")), beginEntruckingTime));
        setCompleteUninstallTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("completeUninstallTime")), completeUninstallTime));
        setLeaveFactoryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leaveFactoryDate")), leaveFactoryDate));
        setCustomerSigningTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerSigningTime")), customerSigningTime));
        setTargetHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointId")), targetHandPointId));
        setCurrentHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("currentHandPointId")), currentHandPointId));
        setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
        setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
        setUnloadLeaveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unloadLeaveFlag")), unloadLeaveFlag));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setHandPointId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handPointId")), handPointId));
        setFactoryType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryType")), factoryType));
        setTargetHandPointIdName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("targetHandPointIdName")), targetHandPointIdName));
        setCurrentHandPointIdName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("currentHandPointIdName")), currentHandPointIdName));
        setStartOfTransport(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("startOfTransport")), startOfTransport));
        setExpectedLoadingTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("expectedLoadingTime")), expectedLoadingTime));
        setTypeOfHandling(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("typeOfHandling")), typeOfHandling));
        setAllocateVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("allocateVehicleNo")), allocateVehicleNo));
        setCallTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("callTime")), callTime));
        setIfPause(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifPause")), ifPause));
        setPauseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pauseTime")), pauseTime));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("carTraceNo", StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("handType", StringUtils.toString(handType, eiMetadata.getMeta("handType")));
        map.put("vehicleNo", StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("idCard", StringUtils.toString(idCard, eiMetadata.getMeta("idCard")));
        map.put("driverName", StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
        map.put("telNum", StringUtils.toString(telNum, eiMetadata.getMeta("telNum")));
        map.put("reservationNumber", StringUtils.toString(reservationNumber, eiMetadata.getMeta("reservationNumber")));
        map.put("checkDate", StringUtils.toString(checkDate, eiMetadata.getMeta("checkDate")));
        map.put("enterFactory", StringUtils.toString(enterFactory, eiMetadata.getMeta("enterFactory")));
        map.put("beginEntruckingTime", StringUtils.toString(beginEntruckingTime, eiMetadata.getMeta("beginEntruckingTime")));
        map.put("completeUninstallTime", StringUtils.toString(completeUninstallTime, eiMetadata.getMeta("completeUninstallTime")));
        map.put("leaveFactoryDate", StringUtils.toString(leaveFactoryDate, eiMetadata.getMeta("leaveFactoryDate")));
        map.put("customerSigningTime", StringUtils.toString(customerSigningTime, eiMetadata.getMeta("customerSigningTime")));
        map.put("targetHandPointId", StringUtils.toString(targetHandPointId, eiMetadata.getMeta("targetHandPointId")));
        map.put("currentHandPointId", StringUtils.toString(currentHandPointId, eiMetadata.getMeta("currentHandPointId")));
        map.put("factoryArea", StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
        map.put("factoryAreaName", StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
        map.put("unloadLeaveFlag", StringUtils.toString(unloadLeaveFlag, eiMetadata.getMeta("unloadLeaveFlag")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("handPointId", StringUtils.toString(handPointId, eiMetadata.getMeta("handPointId")));
        map.put("factoryType", StringUtils.toString(factoryType, eiMetadata.getMeta("factoryType")));
        map.put("targetHandPointIdName", StringUtils.toString(targetHandPointIdName, eiMetadata.getMeta("targetHandPointIdName")));
        map.put("currentHandPointIdName", StringUtils.toString(currentHandPointIdName, eiMetadata.getMeta("currentHandPointIdName")));
        map.put("startOfTransport", StringUtils.toString(startOfTransport, eiMetadata.getMeta("startOfTransport")));
        map.put("expectedLoadingTime", StringUtils.toString(expectedLoadingTime, eiMetadata.getMeta("expectedLoadingTime")));
        map.put("typeOfHandling", StringUtils.toString(typeOfHandling, eiMetadata.getMeta("typeOfHandling")));
        map.put("allocateVehicleNo", StringUtils.toString(allocateVehicleNo, eiMetadata.getMeta("allocateVehicleNo")));
        map.put("callTime", StringUtils.toString(callTime, eiMetadata.getMeta("callTime")));
        map.put("ifPause", StringUtils.toString(ifPause, eiMetadata.getMeta("ifPause")));
        map.put("pauseTime", StringUtils.toString(pauseTime, eiMetadata.getMeta("pauseTime")));

        return map;

    }
}