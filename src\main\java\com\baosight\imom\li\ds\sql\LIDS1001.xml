<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-05 11:18:04
       Version :  1.0
    tableName :meli.tlids1001
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     UNPACK_ORDER_ID  VARCHAR   NOT NULL,
     PACK_ID  VARCHAR,
     UNPACK_AREA_ID  VARCHAR   NOT NULL,
     UNPACK_AREA_NAME  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     ARRIVAL_TIME  VARCHAR,
     DEPARTURE_TIME  VARCHAR,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR   NOT NULL   primarykey
-->
<sqlMap namespace="LIDS1001">

    <sql id="condition">
        AND SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="unpackOrderId">
            UNPACK_ORDER_ID LIKE '%$unpackOrderId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID LIKE '%$packId$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaId">
            UNPACK_AREA_ID = #unpackAreaId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaName">
            UNPACK_AREA_NAME LIKE '%$unpackAreaName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            STATUS > '00'
            AND DEL_FLAG = '0'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.ds.domain.LIDS1001">
        SELECT
        SEG_NO as "segNo",  <!-- 系统账套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
        UNPACK_ORDER_ID as "unpackOrderId",  <!-- 拆包作业清单号 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        UNPACK_AREA_ID as "unpackAreaId",  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME as "unpackAreaName",  <!-- 拆包区名称 -->
        STATUS as "status",  <!-- 状态(10到达、20离开) -->
        ARRIVAL_TIME as "arrivalTime",  <!-- 到达时间 -->
        DEPARTURE_TIME as "departureTime",  <!-- 离开时间 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        TENANT_USER as "tenantUser",  <!-- 租户 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        UUID as "uuid" <!-- ID -->
        FROM meli.tlids1001 t WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM meli.tlids1001 WHERE 1=1
        <include refid="condition"/>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackOrderId">
            UNPACK_ORDER_ID = #unpackOrderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaId">
            UNPACK_AREA_ID = #unpackAreaId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unpackAreaName">
            UNPACK_AREA_NAME = #unpackAreaName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="arrivalTime">
            ARRIVAL_TIME = #arrivalTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="departureTime">
            DEPARTURE_TIME = #departureTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantUser">
            TENANT_USER = #tenantUser#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlids1001 (SEG_NO,  <!-- 系统账套 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        UNPACK_ORDER_ID,  <!-- 拆包作业清单号 -->
        PACK_ID,  <!-- 捆包号 -->
        UNPACK_AREA_ID,  <!-- 拆包区编号 -->
        UNPACK_AREA_NAME,  <!-- 拆包区名称 -->
        STATUS,  <!-- 状态(10到达、20离开) -->
        ARRIVAL_TIME,  <!-- 到达时间 -->
        DEPARTURE_TIME,  <!-- 离开时间 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        TENANT_USER,  <!-- 租户 -->
        DEL_FLAG,  <!-- 删除标记 -->
        UUID  <!-- ID -->
        )
        VALUES (#segNo#, #unitCode#, #unpackOrderId#, #packId#, #unpackAreaId#, #unpackAreaName#, #status#,
        #arrivalTime#, #departureTime#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlids1001 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlids1001
        SET
        SEG_NO = #segNo#,   <!-- 系统账套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代代码 -->
        UNPACK_ORDER_ID = #unpackOrderId#,   <!-- 拆包作业清单号 -->
        PACK_ID = #packId#,   <!-- 捆包号 -->
        UNPACK_AREA_ID = #unpackAreaId#,   <!-- 拆包区编号 -->
        UNPACK_AREA_NAME = #unpackAreaName#,   <!-- 拆包区名称 -->
        STATUS = #status#,   <!-- 状态(10到达、20离开) -->
        ARRIVAL_TIME = #arrivalTime#,   <!-- 到达时间 -->
        DEPARTURE_TIME = #departureTime#,   <!-- 离开时间 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        TENANT_USER = #tenantUser#,   <!-- 租户 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateDepartureTime">
        UPDATE meli.tlids1001
        SET DEPARTURE_TIME = #departureTime#
        <isNotEmpty prepend=" , " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        WHERE SEG_NO = #segNo#
        AND UNPACK_ORDER_ID = #unpackOrderId#
    </update>

</sqlMap>