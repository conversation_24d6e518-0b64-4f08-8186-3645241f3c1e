package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.constants.WorkFlowConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0802;
import com.baosight.imom.vg.dm.domain.VGDM0804;
import com.baosight.imom.vg.dm.domain.VGDM0901;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> yzj
 * @Description : 资材备件报废申请页面后台
 * @Date : 2025/4/22
 * @Version : 1.0
 */
public class ServiceVGDM09 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM09.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0901().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0901.QUERY, new VGDM0901());
    }

    /**
     * 查询附件信息
     */
    public EiInfo queryFile(EiInfo inInfo) {
        String relevanceId = inInfo.getCellStr(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "relevanceId");
        if (StrUtil.isBlank(relevanceId)) {
            inInfo.setCell(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "relevanceId", UUIDUtils.getUUID());
        }
        return super.query(inInfo, VGDM0802.QUERY, null, false, new VGDM0802().eiMetadata,
                MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0901 vgdm0901 = new VGDM0901();
            vgdm0901.fromMap(block.getRow(0));
            // 数据校验
            vgdm0901.setUuid("");
            this.checkData(vgdm0901);
            // 新增数据
            vgdm0901.setApplyStatus(MesConstant.Status.K10);
            String[] arr = {vgdm0901.getSegNo()};
            vgdm0901.setScrapApplyId(SequenceGenerator.getNextSequence(
                    SequenceConstant.SCRAP_APPLY_ID
                    , arr));
            Map insMap = vgdm0901.toMap();
            RecordUtils.setCreator(insMap);
            dao.insert(VGDM0901.INSERT, insMap);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验点检异常信息
     *
     * @param vgdm0901 点检异常信息
     */
    private void checkData(VGDM0901 vgdm0901) {
        // 使用ValidationUtils进行基础校验
        ValidationUtils.validateEntity(vgdm0901);
        // 重新计算金额
        vgdm0901.setAmountMoney(vgdm0901.getApplyQty().multiply(vgdm0901.getUnitPrice()).setScale(2, RoundingMode.HALF_UP));
        // 附件校验
        if (StrUtil.isNotBlank(vgdm0901.getUuid())) {
            int count = VGDM0802.countFiles(dao, vgdm0901.getScrapApplyId(), "VGDM09", vgdm0901.getSegNo());
            if (count < 1) {
                throw new PlatException("必须上传附件信息");
            }
        }
    }

    /**
     * 从资材备件领用申请新增
     */
    public EiInfo insertFromStuff(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.ZC_DETAIL);
            String eArchivesNo = inInfo.getString("eArchivesNo");
            String equipmentName = inInfo.getString("equipmentName");
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo) ||
                    StrUtil.isBlank(eArchivesNo)
                    || StrUtil.isBlank(equipmentName)) {
                throw new PlatException("业务单元代码或设备信息不能为空");
            }
            // 批量生成流水号
            String[] arr = {segNo};
            List<String> ids = SequenceGenerator.getNextSequenceList(block.getRowCount(),
                    SequenceConstant.SCRAP_APPLY_ID, arr);
            // 待新增数据
            List<Map> insList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                // 资材领用申请信息
                VGDM0804 vgdm0804 = new VGDM0804();
                vgdm0804.fromMap(block.getRow(i));
                // 报废申请信息
                VGDM0901 vgdm0901 = new VGDM0901();
                vgdm0901.fromMap(block.getRow(i));
                // 数据校验
                this.checkRepeat(vgdm0901);
                // 数量
                vgdm0901.setApplyQty(vgdm0804.getUsingWgt());
                // 金额
                vgdm0901.setAmountMoney(vgdm0901.getApplyQty()
                        .multiply(vgdm0901.getUnitPrice())
                        .setScale(2, RoundingMode.HALF_UP));
                // 设备信息
                vgdm0901.setEArchivesNo(eArchivesNo);
                vgdm0901.setEquipmentName(equipmentName);
                // 状态
                vgdm0901.setApplyStatus(MesConstant.Status.K10);
                vgdm0901.setScrapApplyId(ids.get(i));
                // 待新增数据
                Map insMap = vgdm0901.toMap();
                RecordUtils.setCreator(insMap);
                insList.add(insMap);
            }
            // 新增
            DaoUtils.insertBatch(dao, VGDM0901.INSERT, insList);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验数据重复
     *
     * @param vgdm0901 报废申请信息
     */
    private void checkRepeat(VGDM0901 vgdm0901) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("segNo", vgdm0901.getSegNo());
        queryMap.put("stuffCode", vgdm0901.getStuffCode());
        queryMap.put("faultId", vgdm0901.getFaultId());
        queryMap.put("voucherNum", vgdm0901.getVoucherNum());
        List aa = dao.query(VGDM0901.COUNT, queryMap);
        if ((Integer) aa.get(0) > 0) {
            throw new PlatException(vgdm0901.getStuffName() + "已有报废申请");
        }
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0901 vgdm0901 = new VGDM0901();
            vgdm0901.fromMap(block.getRow(0));
            VGDM0901 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0901, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
            this.checkData(vgdm0901);
            // 可修改字段
            dbData.setEArchivesNo(vgdm0901.getEArchivesNo());
            dbData.setEquipmentName(vgdm0901.getEquipmentName());
            dbData.setDeptId(vgdm0901.getDeptId());
            dbData.setDeptName(vgdm0901.getDeptName());
            dbData.setStuffCode(vgdm0901.getStuffCode());
            dbData.setStuffName(vgdm0901.getStuffName());
            dbData.setSpecDesc(vgdm0901.getSpecDesc());
            dbData.setApplyQty(vgdm0901.getApplyQty());
            dbData.setUnitPrice(vgdm0901.getUnitPrice());
            dbData.setAmountMoney(vgdm0901.getAmountMoney());
            dbData.setScrapReason(vgdm0901.getScrapReason());
            dbData.setScrapType(vgdm0901.getScrapType());
            // 赋值通用字段
            Map data = dbData.toMap();
            RecordUtils.setRevisor(data);
            dao.update(VGDM0901.UPDATE, data);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>删除标记改为1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0901 frontData;
            VGDM0901 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0901();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                dbData.setApplyStatus(MesConstant.Status.K00);
                dbData.setDelFlag("1");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0901.UPDATE_STATUS, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 提交审核
     */
    public EiInfo submit(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0901 vgdm0901;
            VGDM0901 dbData;
            String userId = UserSession.getLoginName();
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0901 = new VGDM0901();
                vgdm0901.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0901, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                // 校验数据防止提交空数据
                this.checkData(dbData);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.SCRAP_APPLY);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "");
                paramMap.put("userId", userId);
                paramMap.put("comment", "提交审批");
                paramMap.put("variables", new HashMap<>());
                // 判断流程实例ID，空时启动新流程，不空时提交流程
                if (StrUtil.isBlank(dbData.getProcessInstanceId())) {
                    String subject = "资材报废申请审批:" + vgdm0901.getScrapApplyId();
                    String processInstanceId = WorkFlowUtils.start(dbData.getSegNo(),
                            WorkFlowConstant.processKey.SCRAP_APPLY
                            , dbData.getUuid(), subject, null);
                    dbData.setProcessInstanceId(processInstanceId);
                }
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 获取下一节点审批人
                WorkFlowUtils.addAuditPersons(dao, paramMap);
                // 状态-已提交
                dbData.setApplyStatus(MesConstant.Status.K20);
                // 审批状态-审批中
                dbData.setApprStatus(MesConstant.Status.K60);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, true);
            // 批量更新数据
            DaoUtils.updateBatch(dao, VGDM0901.UPDATE_STATUS, block.getRows());
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_SUBMIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 取消提交
     *
     * <p>终止工作流，清空审批信息
     */
    public EiInfo cancel(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0901 vgdm0901;
            VGDM0901 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0901 = new VGDM0901();
                vgdm0901.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0901, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K20);
                // 终止工作流
                WorkFlowUtils.deleteProcess(dbData.getProcessInstanceId());
                // 状态-新增
                dbData.setApplyStatus(MesConstant.Status.K10);
                // 流程实例-空
                dbData.setProcessInstanceId(" ");
                // 审批状态-空
                dbData.setApprStatus(" ");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0901.UPDATE_STATUS, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
