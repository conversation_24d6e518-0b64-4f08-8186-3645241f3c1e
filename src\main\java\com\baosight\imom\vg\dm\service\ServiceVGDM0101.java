package com.baosight.imom.vg.dm.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.vg.dm.domain.VGDM0101;

/**
 * <AUTHOR> yzj
 * @Description : 设备档案查询弹窗页面后台
 * @Date : 2024/8/28
 * @Version : 1.0
 */
public class ServiceVGDM0101 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0101.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0101().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        // 只查询生效状态设备
        inInfo.setCell(EiConstant.queryBlock, 0, "equipmentStatus", MesConstant.Status.K30);
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0101.QUERY, new VGDM0101());
    }

}
