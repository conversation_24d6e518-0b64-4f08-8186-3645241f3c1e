package com.baosight.xservices.xs.lv.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.xservices.xs.domain.XS01;
import com.baosight.xservices.xs.domain.XS03;
import com.baosight.xservices.xs.util.LoginUserDetails;
import com.baosight.xservices.xs.util.UserSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ServiceXSLV05 extends ServiceEPBase {
    private static Logger logger = LogManager.getLogger(com.baosight.xservices.xs.lv.service.ServiceXSLV05.class);
    private final String KEY_ENABLE_USER_FILTER = "xservices.security.level.enableUserFilter";

    public ServiceXSLV05() {
    }

    public EiInfo initLoad(EiInfo inInfo) {
        String manageSwitch = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.manage.switch"), "off");
        String isAdmin = "admin";
        if (!"off".equals(manageSwitch)) {
            String loginName = UserSession.getUser().getUsername();
            isAdmin = LoginUserDetails.isUserAdmin(loginName) ? "admin" : "notAdmin";
        }

        EiInfo outInfo = super.initLoad(inInfo, new XS03());
        outInfo.set("isAdmin", isAdmin);
        return outInfo;
    }

    public EiInfo insert(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        EiBlock eiBlock = inInfo.getBlock(EiConstant.resultBlock);

        for(int i = 0; i < eiBlock.getRowCount(); ++i) {
            Map<String, Object> inInfoRowMap = eiBlock.getRow(i);
            inInfoRowMap.put("recCreator", UserSession.getUser().getUsername());
            inInfoRowMap.put("recCreateTime", DateUtils.curDateTimeStr14());
        }

        eiInfo.addBlock(inInfo.getBlock(EiConstant.resultBlock));
        eiInfo.set(EiConstant.serviceId, "S_XS_21");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return outInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = super.query(inInfo, "XS03.query", new XS03());
        return outInfo;
    }

    public EiInfo getFilterMemberUsers(EiInfo inInfo) {
        EiBlock resultBlock;
        if (null == inInfo.getBlock("result") && null != inInfo.getBlock("resultB")) {
            resultBlock = new EiBlock("result");
            resultBlock.setAttr(inInfo.getBlock("resultB").getAttr());
            inInfo.setBlock(resultBlock);
        }

        resultBlock = null;
        EiInfo resultInfo;
        if ("on".equals(StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.level.enableUserFilter"), "off"))) {
            resultInfo = super.query(inInfo, "XSLV05.queryForMemberUsersNew", new XS01());
        } else {
            resultInfo = super.query(inInfo, "XS03.queryForMemberUsers", new XS01());
        }

        EiBlock resultBBlock = new EiBlock("resultB");
        resultBBlock.setAttr(resultInfo.getBlock("result").getAttr());
        resultBBlock.setRows(resultInfo.getBlock("result").getRows());
        resultInfo.setBlock(resultBBlock);
        resultInfo.setMsg("");
        return resultInfo;
    }

    public EiInfo queryForMemberUsers(EiInfo inInfo) {
        return this.getFilterMemberUsers(inInfo);
    }

    public EiInfo update(EiInfo inInfo) {
        StringBuffer buffer = new StringBuffer();
        StringBuffer detail = new StringBuffer();
        List updatedMemberList = new ArrayList();
        EiBlock eiBlock = inInfo.getBlock("result");
        int rowCount = eiBlock.getRowCount();

        for(int i = 0; i < rowCount; ++i) {
            Map<String, Object> inInfoRowMap = eiBlock.getRow(i);
            inInfoRowMap.put("recRevisor", UserSession.getUser().getUsername());
            inInfoRowMap.put("recReviseTime", DateUtils.curDateTimeStr14());

            try {
                this.dao.update("XS03.update", inInfoRowMap);
                buffer.append("更新成员:" + inInfo.getBlock("result").getCell(i, "memberName") + " 父节点:" + eiBlock.getCell(i, "parentName") + " 的记录成功\n");
                String memberId = inInfoRowMap.get("memberId").toString();
                String parentId = inInfoRowMap.get("parentId").toString();
                Map map = new HashMap();
                map.put("memberId", memberId);
                map.put("parentId", parentId);
                List updateMemberList = this.dao.query("XS03.query", map);
                Map updateMemberMap = (Map)updateMemberList.get(0);
                updatedMemberList.add(updateMemberMap);
            } catch (Exception var14) {
                buffer.append("更新成员:" + inInfo.getBlock("result").getCell(i, "memberName") + " 父节点:" + eiBlock.getCell(i, "parentName") + " 的记录失败\n");
                inInfo.setStatus(-1);
                detail.append(var14.getCause().toString());
                inInfo.setMsg(buffer.toString());
                inInfo.setDetailMsg(detail.toString());
                logger.error(var14.getCause().getMessage());
            }
        }

        if (inInfo.getStatus() != -1) {
            inInfo.setStatus(1);
        }

        inInfo.setMsg(buffer.toString());
        inInfo.setDetailMsg(detail.toString());
        if (updatedMemberList.size() > 0) {
            eiBlock.setRows(updatedMemberList);
        }

        return inInfo;
    }

    public EiInfo delete(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        EiBlock eiBlock = inInfo.getBlock(EiConstant.resultBlock);

        for(int i = 0; i < eiBlock.getRowCount(); ++i) {
            Map<String, Object> inInfoRowMap = eiBlock.getRow(i);
            inInfoRowMap.put("recRevisor", UserSession.getUser().getUsername());
            inInfoRowMap.put("recReviseTime", DateUtils.curDateTimeStr14());
        }

        eiInfo.addBlock(inInfo.getBlock(EiConstant.resultBlock));
        eiInfo.set(EiConstant.serviceId, "S_XS_22");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return outInfo;
    }

    public int getAllRecordCount(StringBuffer buffer, Map param) {
        try {
            List aa = this.dao.query("XS03.count", param);
            int count = (Integer)aa.get(0);
            return count;
        } catch (Exception var5) {
            buffer.append(var5.getMessage()).append("\n");
            return -1;
        }
    }

    private String getSubjectName(String memberType, String memberId) {
        Map map = new HashMap();
        String subjectName = null;
        List member = null;
        if ("USER".equals(memberType)) {
            map.put("userId", memberId);
            member = this.dao.query("XS01.query", map);
            subjectName = ((Map)member.get(0)).get("loginName").toString();
        } else if ("USER_GROUP".equals(memberType)) {
            map.put("groupId", memberId);
            member = this.dao.query("XS02.query", map);
            subjectName = ((Map)member.get(0)).get("groupEname").toString();
        }

        return subjectName;
    }
}
