package com.baosight.imom.common.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class FileConverter {
    /**
     * 将Base64编码字符串转换为指定文件类型的文件
     *
     * @param base64String Base64编码字符串，例如 "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/..."
     * @param filePath     要保存的文件路径（包含文件名及扩展名），例如 "C:/pictures/output.jpg" 或 "/home/<USER>/output.png"
     * @return 转换后生成的File对象，如果转换过程出现错误则返回 null
     */
    public static File base64ToFile(String base64String, String filePath) {
        // 验证文件路径是否有效
        if (filePath == null || filePath.trim().isEmpty()) {
            System.err.println("文件路径不能为空");
            return null;
        }

        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir!= null &&!parentDir.exists()) {
            // 如果父目录不存在，则创建父目录及其所需的上级目录结构
            if (!parentDir.mkdirs()) {
                System.err.println("无法创建文件所在目录");
                return null;
            }
        }

        try {
            // 提取Base64编码部分（去除头部等无关信息），先验证Base64字符串格式（以常见的图片Base64格式为例，可按需调整验证逻辑）
            if (!base64String.contains(",")) {
                System.err.println("Base64字符串格式不正确");
                return null;
            }
            String encodedData = base64String.split(",")[1];

            // 对Base64编码数据进行解码，先转换为字节数组
            byte[] decodedBytes = Base64.getDecoder().decode(encodedData.getBytes(StandardCharsets.UTF_8));

            try (FileOutputStream fos = new FileOutputStream(file)) {
                // 将解码后的字节数组写入文件
                fos.write(decodedBytes);
            }

            return file;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

}
