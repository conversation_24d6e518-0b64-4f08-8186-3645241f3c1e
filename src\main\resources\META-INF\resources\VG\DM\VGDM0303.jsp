<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<script src="${ctx}/common/js/echarts.min.js"></script>

<EF:EFPage prefix="imom">
    <EF:EFTab id="info">
        <div title="点位清单" id="info-1">
            <EF:EFRegion id="inqu" title="查询条件">
                <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                                     readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                                     center="true" required="true">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfo" center="true"
                                     ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>
                </div>
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
                    <EF:EFPopupInput originalInput="true" clear="false"
                                     containerId="deviceInfo" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                                     cname="分部设备名称" colWidth="3"/>
                    <EF:EFInput ename="inqu_status-0-tagId" cname="点位代码" placeholder="模糊条件"
                                colWidth="3"/>
                    <EF:EFInput ename="inqu_status-0-tagDesc" cname="点位名称" placeholder="模糊条件" colWidth="3"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="查询结果">
                <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" sort="all" isFloat="true">
                    <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                    <EF:EFColumn ename="unitCode" enable="false" cname="业务单元代码" width="100" align="center"/>
                    <EF:EFComboColumn ename="segNo" enable="false" cname="业务单元简称" width="100" align="center" sort="flase"
                                      blockName="unitBlock" valueField="segNo" textField="segName"/>
                    <EF:EFColumn ename="eArchivesNo" enable="false" cname="设备代码" width="70" align="center"/>
                    <EF:EFColumn ename="equipmentName" enable="false" cname="设备名称" width="100"/>
                    <%-- <EF:EFColumn ename="deviceCode" enable="false" cname="分部设备代码" width="110" align="center"/>
                    <EF:EFColumn ename="deviceName" enable="false" cname="分部设备名称"/> --%>
                    <EF:EFColumn ename="tagId" enable="false" cname="点位代码" width="140" align="center"/>
                    <EF:EFColumn ename="tagDesc" enable="false" cname="点位名称" width="170"/>
                    <EF:EFComboColumn ename="measureId" required="true" cname="单位" width="70" align="center">
                        <EF:EFOption label="mm/s" value="mm/s"/>
                        <EF:EFOption label="A" value="A"/>
                        <EF:EFOption label="℃" value="℃"/>
                        <EF:EFOption label="bar" value="bar"/>
                        <EF:EFOption label="cm" value="cm"/>
                        <EF:EFOption label="%" value="%"/>
                        <EF:EFOption label="Mpa" value="Mpa"/>
                        <EF:EFOption label=" " value=" "/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="lowerLimit" required="true" cname="下限值" width="100" align="right"/>
                    <EF:EFColumn ename="upperLimit" required="true" cname="上限值" width="100" align="right"/>
                    <EF:EFComboColumn ename="bigScreenFlag" required="true" cname="大屏设备跟随" align="center" width="110">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="bigScreenKeyFlag" required="true" cname="大屏关键点位" align="center" width="110">
                        <EF:EFOption value="1" label="是"/>
                        <EF:EFOption value="0" label="否"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="recRevisor" enable="false" cname="修改人" align="center" width="100"/>
                    <EF:EFColumn ename="recRevisorName" enable="false" cname="修改人姓名" align="center" width="100"/>
                    <EF:EFColumn ename="recReviseTime" enable="false" editType="datetime" width="140"
                                 parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
                    <EF:EFColumn ename="tagIhdId" cname="点位ID" align="right" width="70" enable="false"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div title="监控信息" id="info-2">
            <EF:EFRegion id="inqu2" title="查询条件">
                <div class="row">
                    <EF:EFInput ename="inqu2_status-0-tags" cname="监控点" colWidth="12"
                                ratio="1:11" disabled="true"/>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="col-xs-4 control-label"><span
                                    class="i-input-required">*</span>数据类型</label>
                            <div class="col-xs-8">
                                <EF:EFInput ename="inqu2_status-0-queryType" value="1" inline="true" cname="实时数据"
                                            type="radio"/>
                                <EF:EFInput ename="inqu2_status-0-queryType" value="2" inline="true" cname="历史数据"
                                            type="radio"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" id="timeDiv">
                    <EF:EFSelect ename="inqu2_status-0-refreshType" value="5" cname="刷新频率" required="true"
                                 colWidth="3">
                        <EF:EFOption value="5" label="5秒"/>
                        <EF:EFOption value="10" label="10秒"/>
                        <EF:EFOption value="30" label="30秒"/>
                    </EF:EFSelect>
                </div>
                <div class="row" id="historyDiv">
                    <EF:EFDateSpan startName="inqu2_status-0-startTime" role="datetime"
                                   endName="inqu2_status-0-endTime" required="true"
                                   startCname="采集时间(起)" endCname="采集时间(止)"
                                   ratio="3:3" format="yyyy-MM-dd HH:mm:ss" interval="10">
                    </EF:EFDateSpan>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="图表">
            </EF:EFRegion>
        </div>
    </EF:EFTab>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
</EF:EFPage>