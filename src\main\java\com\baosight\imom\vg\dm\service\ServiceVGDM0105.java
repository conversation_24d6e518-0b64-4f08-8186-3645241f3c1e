package com.baosight.imom.vg.dm.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.DateUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.utils.ValidationUtils;
import com.baosight.imom.vg.dm.domain.VGDM0102;
import com.baosight.imom.vg.dm.domain.VGDM0105;
import com.baosight.imom.vg.dm.domain.VGDM0601;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

/**
 * 分部设备检修配置后台
 *
 * <AUTHOR> 郁在杰
 * @Description :
 * @Date : 2024/12/12
 * @Version : 1.0
 */
public class ServiceVGDM0105 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0105.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(MesConstant.Iplat.DETAIL_BLOCK).setBlockMeta(new VGDM0105().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            String deviceCode = inInfo.getString("deviceCode");
            if (StrUtil.isBlank(deviceCode)) {
                throw new Exception("分部设备代码不能为空");
            }
            Map<String, String> map = new HashMap<>();
            map.put("deviceCode", deviceCode);
            List<VGDM0105> list = dao.query(VGDM0105.QUERY, map);
            inInfo.addBlock(MesConstant.Iplat.DETAIL_BLOCK).setBlockMeta(new VGDM0105().eiMetadata);
            inInfo.getBlock(MesConstant.Iplat.DETAIL_BLOCK).setRows(list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 保存
     *
     * <p>根据分部设备代码查询分部设备检修配置信息，如果存在则更新，不存在则新增
     */
    public EiInfo save(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_BLOCK);
            VGDM0105 vgdm0105 = new VGDM0105();
            vgdm0105.fromMap(block.getRow(0));
            // 校验
            checkData(vgdm0105);
            // 分部设备信息
            VGDM0102 device = VGDM0102.queryByNo(dao, vgdm0105.getDeviceCode());
            if (device == null) {
                throw new Exception("分部设备信息不存在");
            }
            // 查询数据库中数据
            VGDM0105 dbData = VGDM0105.queryByDeviceCode(dao, vgdm0105.getDeviceCode());
            if (dbData != null) {
                // 更新
                Map updateMap = vgdm0105.toMap();
                RecordUtils.setRevisor(updateMap);
                // 赋值
                updateMap.put("uuid", dbData.getUuid());
                // 更新数据库
                dao.update(VGDM0105.UPDATE, updateMap);
                // 返回前端
                block.getRows().set(0, updateMap);
            } else {
                // 新增
                vgdm0105.setDeviceCode(device.getDeviceCode());
                vgdm0105.setDeviceName(device.getDeviceName());
                vgdm0105.setSegNo(device.getSegNo());
                vgdm0105.setUnitCode(device.getUnitCode());
                Map insMap = vgdm0105.toMap();
                RecordUtils.setCreator(insMap);
                // 新增数据库
                dao.insert(VGDM0105.INSERT, insMap);
                // 返回前端
                block.getRows().set(0, insMap);
            }
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg("保存成功");
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 校验数据
     */
    private void checkData(VGDM0105 vgdm0105) {
        // 基础校验
        ValidationUtils.validateEntity(vgdm0105);
        // 周期
        if ("10".equals(vgdm0105.getOverhaulCycleType())) {
            if (vgdm0105.getCycleDay() < 1) {
                throw new PlatException("检修周期按天数时天数不能为空");
            }
            vgdm0105.setCycleNum(0);
        }
        if ("20".equals(vgdm0105.getOverhaulCycleType())) {
            if (vgdm0105.getCycleNum() < 1) {
                throw new PlatException("检修周期按加工量时加工量不能为空");
            }
            vgdm0105.setCycleDay(0);
        }
    }

    /**
     * 根据检修周期自动生成预警
     *
     * <p>超过检修周期天数未生成检修计划时报警
     */
    public EiInfo autoAlarm(EiInfo inInfo) {
        try {
            // 获取输入参数
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("账套不能为空");
            }
            Map<String, String> map = new HashMap<>();
            map.put("segNo", segNo);
            // 查询数据库中数据
            List<Map> list = dao.query(VGDM0105.QUERY_FOR_ALARM, map);
            if (CollectionUtils.isNotEmpty(list)) {
                List<Map> insList = new ArrayList<>();
                List<Map> updList = new ArrayList<>();
                VGDM0601 vgdm0601;
                for (Map item : list) {
                    // 查询是否有未确认报警
                    Map<String, Object> checkMap = new HashMap<>();
                    checkMap.put("segNo", segNo);
                    checkMap.put("deviceCode", item.get("deviceCode"));
                    checkMap.put("setAlarmTypedm", "-1");
                    checkMap.put("confirmStatus", "未确认");
                    List<VGDM0601> alarmList = dao.query(VGDM0601.QUERY, checkMap);
                    if (CollectionUtils.isNotEmpty(alarmList)) {
                        vgdm0601 = alarmList.get(0);
                        vgdm0601.setRepeatCount(vgdm0601.getRepeatCount() + 1);
                        // 更新数据库
                        Map updMap = vgdm0601.toMap();
                        RecordUtils.setRevisorSys(updMap);
                        updList.add(updMap);
                    } else {
                        // 生成报警
                        vgdm0601 = new VGDM0601();
                        vgdm0601.setDeviceCode(item.get("deviceCode").toString());
                        vgdm0601.setDeviceName(item.get("deviceName").toString());
                        vgdm0601.setEArchivesNo(item.get("eArchivesNo").toString());
                        vgdm0601.setEquipmentName(item.get("equipmentName").toString());
                        vgdm0601.setAlarmTypedm("-1");
                        vgdm0601.setAlarmTagDesc("长期未检修");
                        vgdm0601.setAlarmTag("VGDM0105");
                        vgdm0601.setRepeatCount(1);
                        String[] arr = {segNo};
                        vgdm0601.setAlarmId(SequenceGenerator.getNextSequence(SequenceConstant.ALARM_ID, arr));
                        vgdm0601.setConfirmStatus("未确认");
                        vgdm0601.setOccurTime(DateUtils.FORMATTER_23.format(LocalDateTime.now()));
                        vgdm0601.setSegNo(segNo);
                        vgdm0601.setUnitCode(segNo);
                        // 新增数据库
                        Map insMap = vgdm0601.toMap();
                        RecordUtils.setCreatorSys(insMap);
                        insList.add(insMap);
                    }
                }
                // 批量新增
                DaoUtils.insertBatch(dao, VGDM0601.INSERT, insList);
                // 批量更新
                DaoUtils.updateBatch(dao, VGDM0601.UPDATE_ALARM, updList);
            }
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }
}
