<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-factoryArea" cname="厂区代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryAreaName" cname="厂区名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryBuilding" cname="厂房代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-factoryBuildingName" cname="厂房名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-crossArea" cname="跨区代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-crossAreaName" cname="跨区名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-firstProcessingProcedure" cname="原卷首道加工工序" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-prodcode" cname="品种代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-specsDesc" cname="原料规格" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-wproviderId" cname="仓库代码" colWidth="3" placeholder="模糊条件"/>

        </div>

    </EF:EFRegion>
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true"  >
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="crossArea" cname="跨区代码" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="crossAreaName" cname="跨区名称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="firstMachineCode" cname="首道加工机组" align="center" width="120"/>

                <EF:EFComboColumn ename="unloadingType" cname="卸货类型" align="center" width="150">
                    <EF:EFOption label="全部" value=""/>
                    <EF:EFOption label="采购" value="1"/>
                    <EF:EFOption label="转库" value="2"/>
                </EF:EFComboColumn>

                <EF:EFColumn ename="firstProcessingProcedure" cname="原卷首道加工工序" align="center" width="120"/>
                <EF:EFColumn ename="businessType" cname="贸易方式" align="center" width="120"/>
                <EF:EFColumn ename="prodcode" cname="品种代码" align="center" width="120"/>
                <EF:EFColumn ename="specsDesc" cname="原料规格" align="center" width="120"/>
                <EF:EFColumn ename="pollingSchemeNumber" cname="轮询方案编号" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="wproviderId" cname="仓库" align="center" width="200" required="true"/>
                <%--<EF:EFColumn ename="status" cname="状态" align="center" width="120" enable="false"/>--%>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="新增" value="10"/>
                    <EF:EFOption label="删除" value="00"/>
                </EF:EFComboColumn>
            </EF:EFGrid>
        </div>
    </EF:EFTab>
    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--区域代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="crossingChannelsInfo" width="90%" height="60%"/>
    <%--跨区编码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseCodeInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/LIDS0602" id="pollingSchemeNumberInfo" width="90%" height="60%"/>

</EF:EFPage>
