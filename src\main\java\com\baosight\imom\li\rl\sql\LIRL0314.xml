<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-01-14 10:40:16
   		Version :  1.0
		tableName :meli.tlirl0314 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 SEG_NO  VARCHAR, 
		 SEG_CNAME  VARCHAR, 
		 FACTORY_AREA  VARCHAR, 
		 FACTORY_AREA_NAME  VARCHAR, 
		 WAREHOUSE_CODE  VARCHAR, 
		 WAREHOUSE_NAME  VARCHAR, 
		 ITEM_CODE  VARCHAR, 
		 ITEM_CNAME  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 TENANT_USER  VARCHAR, 
		 UNIT_CODE  VARCHAR
	-->
<sqlMap namespace="LIRL0314">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segCname">
			SEG_CNAME = #segCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCode">
			ITEM_CODE = #itemCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCname">
			ITEM_CNAME = #itemCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0314">
		SELECT
				UUID	as "uuid",  <!-- ID -->
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
				SEG_CNAME	as "segCname",  <!-- 业务单元名称 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区编码 -->
				FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库编码 -->
				WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
				ITEM_CODE	as "itemCode",  <!-- 值集编码 -->
				ITEM_CNAME	as "itemCname",  <!-- 值集名称 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 记录归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记(默认0 删除1) -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				UNIT_CODE	as "unitCode" <!-- 单元代码 -->
		FROM meli.tlirl0314 WHERE 1=1
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0314 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segCname">
			SEG_CNAME = #segCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCode">
			ITEM_CODE = #itemCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCname">
			ITEM_CNAME = #itemCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0314 (UUID,  <!-- ID -->
										SEG_NO,  <!-- 业务单元代码 -->
										SEG_CNAME,  <!-- 业务单元名称 -->
										FACTORY_AREA,  <!-- 厂区编码 -->
										FACTORY_AREA_NAME,  <!-- 厂区名称 -->
										WAREHOUSE_CODE,  <!-- 仓库编码 -->
										WAREHOUSE_NAME,  <!-- 仓库名称 -->
										ITEM_CODE,  <!-- 值集编码 -->
										ITEM_CNAME,  <!-- 值集名称 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 记录归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记(默认0 删除1) -->
										TENANT_USER,  <!-- 租户 -->
										UNIT_CODE  <!-- 单元代码 -->
										)		 
	    VALUES (#uuid#, #segNo#, #segCname#, #factoryArea#, #factoryAreaName#, #warehouseCode#, #warehouseName#, #itemCode#, #itemCname#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #tenantUser#, #unitCode#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0314 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0314 
		SET 
					SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					SEG_CNAME	= #segCname#,   <!-- 业务单元名称 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区编码 -->  
					FACTORY_AREA_NAME	= #factoryAreaName#,   <!-- 厂区名称 -->  
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库编码 -->  
					WAREHOUSE_NAME	= #warehouseName#,   <!-- 仓库名称 -->  
					ITEM_CODE	= #itemCode#,   <!-- 值集编码 -->  
					ITEM_CNAME	= #itemCname#,   <!-- 值集名称 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 记录归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记(默认0 删除1) -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					UNIT_CODE	= #unitCode#  <!-- 单元代码 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>