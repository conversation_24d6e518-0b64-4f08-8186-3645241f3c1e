package com.baosight.imom.common.kafkaMq;

import com.baosight.imom.common.utils.RedisUtil;
import com.baosight.imom.common.utils.SwitchUtils;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.xeye.util.PlatGenerator;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.util.Map;

public class Callback implements MqttCallback {

    private Dao dao = PlatApplicationContext.getBean("dao", Dao.class);
    private static final RedisUtil redisUtil = PlatApplicationContext.getBean("redisUtil",RedisUtil.class);
    private static final Logger log = LoggerFactory.getLogger(Callback.class);

    /**
     * MQTT 断开连接会执行此方法
     *
     * 方法说明：当客户端与 MQTT 服务器之间的连接丢失时，此方法被调用。
     * @param throwable 表示导致连接丢失的原因，通常为一个 Throwable 对象
     */
    @Override
    public void connectionLost(Throwable throwable) {
        log.info("断开了MQTT连接 ：{}", throwable.getMessage());
        log.error(throwable.getMessage(), throwable);
    }

    /**
     * publish发布成功后会执行到这里
     *
     * 方法说明：当一个消息的交付完成并且所有必要的确认都已收到时，此方法被调用。
     * 注意事项：
     * 对于 QoS 0 消息，在消息被网络层接收后会调用此方法。
     * 对于 QoS 1 消息，在接收到 PUBACK 包后调用此方法。
     * 对于 QoS 2 消息，在接收到 PUBCOMP 包后调用此方法。
     * @param iMqttDeliveryToken the delivery token associated with the message.
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        log.info("发布消息成功");
    }


    /**
     * subscribe订阅后得到的消息会执行到这里
     *
     * 方法说明：当从服务器接收到一条新消息时，此方法被调用
     * 注意事项：
     * 在此方法中抛出任何异常将会导致客户端关闭，并且未确认的消息可能会被重新发送。
     * 如果在此方法执行期间有其他消息到达，它们将被缓存直到此方法返回。
     * @param topic 消息发布的主题名称
     * @param message 实际的消息内容，类型为 MqttMessage
     * @throws Exception
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        try {
            //  TODO    此处可以将订阅得到的消息进行业务处理、数据存储
            String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
            /*log.info("收到来自 " + topic + " 的消息：{}", payload);
            System.out.println("收到来自 " + topic + " 的消息：{}" + payload);*/


            //接收行车抓取释放信息
            if (topic.contains("/publish_data/weight_card/")) {
                //是否处理行车抓取释放信息
                String switchValue = new SwitchUtils().getProcessSwitchValue("JC000000", "IF_ENABLE_UWB_MQTT_LISTENER_CRANE", dao);
                if ("1".equals(switchValue)) {
                    //开始时间
                    long startTime = System.currentTimeMillis();
                    //转换成Map
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map attrMap = objectMapper.readValue(payload, Map.class);
                    attrMap.remove(EiConstant.serviceId);
                    log.info("转换后的map" + attrMap);
                    System.out.println("转换后的map" + attrMap);

                    //处理抓取释放信息接收
                    EiInfo sendInfo = new EiInfo();
                    //设置链路ID
                    String traceId = PlatGenerator.generate().toString();
                    sendInfo.setTraceId(traceId);

                    sendInfo.setAttr(attrMap);
                    sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
                    sendInfo.set(EiConstant.methodName, "uploadUwbInfo");
                    sendInfo = XLocalManager.callNewTx(sendInfo);

                    //结束时间
                    long endTime = System.currentTimeMillis();
                    log.info("mq处理行车抓取释放实绩耗时：" + (endTime - startTime) + "ms");
                    System.out.println("mq处理行车抓取释放实绩耗时：" + (endTime - startTime) + "ms");
                    if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        log.info("消息处理失败:" + sendInfo.getMsg());
                        System.out.println("消息处理失败:" + sendInfo.getMsg());
                    } else {
                        log.info("处理成功:" + sendInfo.getMsg());
                        System.out.println("处理成功:" + sendInfo.getMsg());
                    }
                }
            }

            //接受定位标签卡实时位置，保存到Redis缓存，时效一小时
            if (topic.contains("/LocXYZ/")) {
                //转换成Map
                ObjectMapper objectMapper = new ObjectMapper();
                LinkedList<Map> attrList = objectMapper.readValue(payload, LinkedList.class);
                //System.err.println(attrList);
                // 遍历每条记录，按 card_id 更新 Redis Hash
                for (Map record : attrList) {
                    Object cardIdObj = record.get("card_id");
                    if (cardIdObj == null) continue;

                    String cardId = cardIdObj.toString();

                    //判断标记，有效数据再更新
                    Boolean validFlag = (Boolean) record.get("valid_flag");
                    if (validFlag) {
                        // 将当前记录写入 Redis Hash，key: uwbLocRecord:latest，field: card_id
                        redisUtil.hSet("uwbLocRecord:latest", cardId, record);
                    /*// 2. 存入 List，保留历史记录
                    redisUtil.rightPush("uwbLocRecord:list", record);
                    redisUtil.expireKey("uwbLocRecord:list", 86400); // 保留一天*/// 设置过期时间（首次写入设置一次即可）
                        redisUtil.expireKey("uwbLocRecord:latest", 3600); // 1小时
                    }
                }
            }


            /*//接收人员区域进出信息
            if (topic.contains("/publish_data/in_area/") || topic.contains("/publish_data/out_area/")) {
                //是否处理人员区域进出信息
                String switchValue = new SwitchUtils().getProcessSwitchValue("JC000000", "IF_ENABLE_UWB_MQTT_LISTENER", dao);
                if ("1".equals(switchValue)) {
                    //开始时间
                    long startTime = System.currentTimeMillis();
                    //转换成Map
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map attrMap = objectMapper.readValue(payload, Map.class);
                    attrMap.remove(EiConstant.serviceId);
                    log.info("转换后的map" + attrMap);
                    System.out.println("转换后的map" + attrMap);

                    //处理人员区域进出信息
                    EiInfo sendInfo = new EiInfo();
                    //设置链路ID
                    String traceId = PlatGenerator.generate().toString();
                    sendInfo.setTraceId(traceId);

                    sendInfo.setAttr(attrMap);
                    sendInfo.set(EiConstant.serviceName, "VPPLInterfaces");
                    sendInfo.set(EiConstant.methodName, "receiveUWBPersonLocation");
                    sendInfo = XLocalManager.callNewTx(sendInfo);

                    //结束时间
                    long endTime = System.currentTimeMillis();
                    log.info("mq处理人员进出区域信息推送耗时：" + (endTime - startTime) + "ms");
                    System.out.println("mq处理人员进出区域信息推送耗时：" + (endTime - startTime) + "ms");
                    if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        log.info("消息处理失败:" + sendInfo.getMsg());
                        System.out.println("消息处理失败:" + sendInfo.getMsg());
                    } else {
                        log.info("消息处理成功:" + sendInfo.getMsg());
                        System.out.println("消息处理成功:" + sendInfo.getMsg());
                    }
                }
            }*/

        } catch (Exception ex) {
            log.info("监听" + topic + "消息处理失败:" + ex.getMessage());
            System.out.println("监听" + topic + "消息处理失败:" + ex.getMessage());
        }
    }

}
