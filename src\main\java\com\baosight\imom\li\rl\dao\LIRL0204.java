/**
* Generate time : 2025-02-07 9:06:11
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0204
* 
*/
public class LIRL0204 extends DaoEPBase {

        public static final String QUERY = "LIRL0204.query";
        public static final String COUNT = "LIRL0204.count";
        public static final String INSERT = "LIRL0204.insert";
        public static final String UPDATE = "LIRL0204.update";
        public static final String DELETE = "LIRL0204.delete";
                private String reservationNumber = " ";		/* 预约单号*/
                private String segNo = " ";		/* 业务单元代代码*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String status = " ";		/* 状态(00：撤销，20：生效，99：完成)*/
                private String visitUnitCode = " ";		/* 拜访单位代码*/
                private String visitUnitName = " ";		/* 拜访单位*/
                private String visitorName = " ";		/* 访客姓名*/
                private String visitorTel = " ";		/* 访客电话*/
                private String visitorIdentity = " ";		/* 访客身份*/
                private String visitorGender = " ";		/* 访客性别*/
                private String reservationDate = " ";		/* 预约日期*/
                private String reservationTime = " ";		/* 预约时段*/
                private String reservationDateEnd = " ";		/* 预约截止日期*/
                private String reservationTimeEnd = " ";		/* 预约截止时段*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 说明备注*/
                private String sysRemark = " ";		/* 系统备注*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00：撤销，20：生效，99：完成)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitUnitCode");
        eiColumn.setDescName("拜访单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitUnitName");
        eiColumn.setDescName("拜访单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitorName");
        eiColumn.setDescName("访客姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitorTel");
        eiColumn.setDescName("访客电话");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitorIdentity");
        eiColumn.setDescName("访客身份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitorGender");
        eiColumn.setDescName("访客性别");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setDescName("预约日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setDescName("预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDateEnd");
        eiColumn.setDescName("预约截止日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTimeEnd");
        eiColumn.setDescName("预约截止时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("说明备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0204() {
initMetaData();
}

        /**
        * get the reservationNumber - 预约单号
        * @return the reservationNumber
        */
        public String getReservationNumber() {
        return this.reservationNumber;
        }

        /**
        * set the reservationNumber - 预约单号
        */
        public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
        }
        /**
        * get the segNo - 业务单元代代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the status - 状态(00：撤销，20：生效，99：完成)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(00：撤销，20：生效，99：完成)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the visitUnitCode - 拜访单位代码
        * @return the visitUnitCode
        */
        public String getVisitUnitCode() {
        return this.visitUnitCode;
        }

        /**
        * set the visitUnitCode - 拜访单位代码
        */
        public void setVisitUnitCode(String visitUnitCode) {
        this.visitUnitCode = visitUnitCode;
        }
        /**
        * get the visitUnitName - 拜访单位
        * @return the visitUnitName
        */
        public String getVisitUnitName() {
        return this.visitUnitName;
        }

        /**
        * set the visitUnitName - 拜访单位
        */
        public void setVisitUnitName(String visitUnitName) {
        this.visitUnitName = visitUnitName;
        }
        /**
        * get the visitorName - 访客姓名
        * @return the visitorName
        */
        public String getVisitorName() {
        return this.visitorName;
        }

        /**
        * set the visitorName - 访客姓名
        */
        public void setVisitorName(String visitorName) {
        this.visitorName = visitorName;
        }
        /**
        * get the visitorTel - 访客电话
        * @return the visitorTel
        */
        public String getVisitorTel() {
        return this.visitorTel;
        }

        /**
        * set the visitorTel - 访客电话
        */
        public void setVisitorTel(String visitorTel) {
        this.visitorTel = visitorTel;
        }
        /**
        * get the visitorIdentity - 访客身份
        * @return the visitorIdentity
        */
        public String getVisitorIdentity() {
        return this.visitorIdentity;
        }

        /**
        * set the visitorIdentity - 访客身份
        */
        public void setVisitorIdentity(String visitorIdentity) {
        this.visitorIdentity = visitorIdentity;
        }
        /**
        * get the visitorGender - 访客性别
        * @return the visitorGender
        */
        public String getVisitorGender() {
        return this.visitorGender;
        }

        /**
        * set the visitorGender - 访客性别
        */
        public void setVisitorGender(String visitorGender) {
        this.visitorGender = visitorGender;
        }
        /**
        * get the reservationDate - 预约日期
        * @return the reservationDate
        */
        public String getReservationDate() {
        return this.reservationDate;
        }

        /**
        * set the reservationDate - 预约日期
        */
        public void setReservationDate(String reservationDate) {
        this.reservationDate = reservationDate;
        }
        /**
        * get the reservationTime - 预约时段
        * @return the reservationTime
        */
        public String getReservationTime() {
        return this.reservationTime;
        }

        /**
        * set the reservationTime - 预约时段
        */
        public void setReservationTime(String reservationTime) {
        this.reservationTime = reservationTime;
        }
        /**
        * get the reservationDateEnd - 预约截止日期
        * @return the reservationDateEnd
        */
        public String getReservationDateEnd() {
        return this.reservationDateEnd;
        }

        /**
        * set the reservationDateEnd - 预约截止日期
        */
        public void setReservationDateEnd(String reservationDateEnd) {
        this.reservationDateEnd = reservationDateEnd;
        }
        /**
        * get the reservationTimeEnd - 预约截止时段
        * @return the reservationTimeEnd
        */
        public String getReservationTimeEnd() {
        return this.reservationTimeEnd;
        }

        /**
        * set the reservationTimeEnd - 预约截止时段
        */
        public void setReservationTimeEnd(String reservationTimeEnd) {
        this.reservationTimeEnd = reservationTimeEnd;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 说明备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 说明备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the sysRemark - 系统备注
        * @return the sysRemark
        */
        public String getSysRemark() {
        return this.sysRemark;
        }

        /**
        * set the sysRemark - 系统备注
        */
        public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setReservationNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationNumber")), reservationNumber));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setVisitUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitUnitCode")), visitUnitCode));
                setVisitUnitName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitUnitName")), visitUnitName));
                setVisitorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitorName")), visitorName));
                setVisitorTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitorTel")), visitorTel));
                setVisitorIdentity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitorIdentity")), visitorIdentity));
                setVisitorGender(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("visitorGender")), visitorGender));
                setReservationDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationDate")), reservationDate));
                setReservationTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationTime")), reservationTime));
                setReservationDateEnd(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationDateEnd")), reservationDateEnd));
                setReservationTimeEnd(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationTimeEnd")), reservationTimeEnd));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("reservationNumber",StringUtils.toString(reservationNumber, eiMetadata.getMeta("reservationNumber")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("visitUnitCode",StringUtils.toString(visitUnitCode, eiMetadata.getMeta("visitUnitCode")));
                map.put("visitUnitName",StringUtils.toString(visitUnitName, eiMetadata.getMeta("visitUnitName")));
                map.put("visitorName",StringUtils.toString(visitorName, eiMetadata.getMeta("visitorName")));
                map.put("visitorTel",StringUtils.toString(visitorTel, eiMetadata.getMeta("visitorTel")));
                map.put("visitorIdentity",StringUtils.toString(visitorIdentity, eiMetadata.getMeta("visitorIdentity")));
                map.put("visitorGender",StringUtils.toString(visitorGender, eiMetadata.getMeta("visitorGender")));
                map.put("reservationDate",StringUtils.toString(reservationDate, eiMetadata.getMeta("reservationDate")));
                map.put("reservationTime",StringUtils.toString(reservationTime, eiMetadata.getMeta("reservationTime")));
                map.put("reservationDateEnd",StringUtils.toString(reservationDateEnd, eiMetadata.getMeta("reservationDateEnd")));
                map.put("reservationTimeEnd",StringUtils.toString(reservationTimeEnd, eiMetadata.getMeta("reservationTimeEnd")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("sysRemark",StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));

return map;

}
}