/**
 * Generate time : 2024-09-26 9:54:04
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.imom.common.utils.ValidationUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Tvgdm0801
 */
public class Tvgdm0801 extends DaoEPBase {

    private String overhaulPlanId = " ";        /* 检修计划编号*/
    @NotBlank(message = "设备信息不能为空")
    private String eArchivesNo = " ";        /* 设备档案编号*/

    private String e_archivesNo = " ";        /* 设备档案编号（移动端审批使用）*/
    @NotBlank(message = "设备信息不能为空")
    private String equipmentName = " ";        /* 设备名称*/
    private String deviceCode = " ";        /* 分部设备代码*/
    private String deviceName = " ";        /* 分部设备名称*/
    @NotBlank(message = "检修性质不能为空")
    private String overhaulQuality = " ";        /* 检修性质*/
    @NotBlank(message = "施工类别不能为空")
    private String overhaulType = " ";        /* 检修类别*/
    @NotBlank(message = "计划检修日(起)不能为空")
    private String overhaulStartDate = " ";        /* 计划检修开始日期*/
    @NotBlank(message = "计划检修日(止)不能为空")
    private String overhaulEndDate = " ";        /* 计划检修结束日期*/
    @NotNull(message = "计划检修人数不能为空")
    @Min(value = 1, message = "计划检修人数必须大于0")
    private BigDecimal overhaulNumber = new BigDecimal("0");        /* 计划检修人数*/
    private BigDecimal overhaulTime = new BigDecimal("0");        /* 计划检修时间*/
    @NotBlank(message = "计划检修项目不能为空")
    private String overhaulProject = " ";        /* 计划检修项目*/
    private String exceptionContactId = " ";        /* 异常联络单号*/
    private String overhaulSource = " ";        /* 检修来源*/
    private String outsourcingContactId = " ";        /* 委外联络单号*/
    @NotBlank(message = "安全措施不能为空")
    private String securityMeasures = " ";        /* 安全措施*/
    @NotBlank(message = "验收标准不能为空")
    private String acceptanceCriteria = " ";        /* 检修验收标准*/
    private String implementManId = " ";        /* 实施人*/
    private String implementManName = " ";        /* 实施人姓名*/
    private String overhaulPlanStatus = " ";        /* 检修计划状态*/
    private String overhaulImplementDate = " ";        /* 检修实施日期*/
    @NotNull(message = "实际检修人数不能为空", groups = {ValidationUtils.Group1.class})
    @Min(value = 1, message = "实际检修人数必须大于0", groups = {ValidationUtils.Group1.class})
    private BigDecimal actualOverhaulNumber = new BigDecimal("0");        /* 实际检修人数*/
    @NotNull(message = "实际检修时间不能为空", groups = {ValidationUtils.Group1.class})
    @DecimalMin(value = "0", inclusive = false, message = "实际检修时间必须大于0", groups = {ValidationUtils.Group1.class})
    private BigDecimal actualOverhaulTime = new BigDecimal("0");        /* 实际检修时间*/
    @NotBlank(message = "是否完成不能为空", groups = {ValidationUtils.Group1.class})
    private String isComplete = " ";        /* 是否完成*/
    private String overhaulLegacyProject = " ";        /* 遗留检修项目*/
    @NotBlank(message = "是否符合标准不能为空", groups = {ValidationUtils.Group1.class})
    private String isConformStandard = " ";        /* 是否符合标准*/
    private String relevantMeasures = " ";        /* 相关措施*/
    @NotBlank(message = "是否动火不能为空", groups = {ValidationUtils.Group1.class})
    private String isHot = " ";        /* 是否动火*/
    private String hotCardId = " ";        /* 动火证编号*/
    private String offlinePartsGone = " ";        /* 下线零件去向*/
    private String actualsRevisor = " ";        /* 检修实绩操作人*/
    private String actualsTime = " ";        /* 检修实绩操作时间*/
    private String actualsRevisorName = " ";        /* 检修实绩操作人姓名*/
    private String voucherNum = " ";        /* 依据凭单*/
    private String apprStatus = " ";        /* 审批状态*/
    @NotBlank(message = "实际检修项目不能为空", groups = {ValidationUtils.Group1.class})
    private String actualLegacyProject = " ";        /* 实际检修项目*/
    private String processInstanceId = " ";        /* 工作流实例ID*/
    private String processId = " ";        /* 工作流实例ID*/
    private String overhaulSuggestions = " ";        /* 问题及建议*/
    @NotBlank(message = "检修总结不能为空", groups = {ValidationUtils.Group1.class})
    private String overhaulSummarize = " ";        /* 检修总结*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    private String unitName = " ";        /* 业务单元名称*/
    private String delayRemark = " ";        /* 延期备注 */

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("overhaulPlanId");
        eiColumn.setDescName("检修计划编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("e_archivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulQuality");
        eiColumn.setDescName("检修性质");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulType");
        eiColumn.setDescName("检修类别");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulStartDate");
        eiColumn.setDescName("计划检修开始日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulEndDate");
        eiColumn.setDescName("计划检修结束日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulNumber");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("计划检修人数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulTime");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("计划检修时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulProject");
        eiColumn.setDescName("计划检修项目");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("exceptionContactId");
        eiColumn.setDescName("异常联络单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulSource");
        eiColumn.setDescName("检修来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outsourcingContactId");
        eiColumn.setDescName("委外联络单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("securityMeasures");
        eiColumn.setDescName("安全措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("acceptanceCriteria");
        eiColumn.setDescName("检修验收标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("implementManId");
        eiColumn.setDescName("实施人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("implementManName");
        eiColumn.setDescName("实施人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulPlanStatus");
        eiColumn.setDescName("检修计划状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulImplementDate");
        eiColumn.setDescName("检修实施日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualOverhaulNumber");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("实际检修人数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualOverhaulTime");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("实际检修时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isComplete");
        eiColumn.setDescName("是否完成");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulLegacyProject");
        eiColumn.setDescName("遗留检修项目");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isConformStandard");
        eiColumn.setDescName("是否符合标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("relevantMeasures");
        eiColumn.setDescName("相关措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isHot");
        eiColumn.setDescName("是否动火");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hotCardId");
        eiColumn.setDescName("动火证编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("offlinePartsGone");
        eiColumn.setDescName("下线零件去向");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsRevisor");
        eiColumn.setDescName("检修实绩操作人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsTime");
        eiColumn.setDescName("检修实绩操作时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsRevisorName");
        eiColumn.setDescName("检修实绩操作人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("依据凭单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprStatus");
        eiColumn.setDescName("审批状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualLegacyProject");
        eiColumn.setDescName("实际检修项目");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processInstanceId");
        eiColumn.setDescName("工作流实例ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processId");
        eiColumn.setDescName("工作流实例ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulSuggestions");
        eiColumn.setDescName("问题及建议");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("overhaulSummarize");
        eiColumn.setDescName("检修总结");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitName");
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delayRemark");
        eiColumn.setDescName("延期备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm0801() {
        initMetaData();
    }

    /**
     * get the overhaulPlanId - 检修计划编号
     *
     * @return the overhaulPlanId
     */
    public String getOverhaulPlanId() {
        return this.overhaulPlanId;
    }

    /**
     * set the overhaulPlanId - 检修计划编号
     */
    public void setOverhaulPlanId(String overhaulPlanId) {
        this.overhaulPlanId = overhaulPlanId;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the overhaulQuality - 检修性质
     *
     * @return the overhaulQuality
     */
    public String getOverhaulQuality() {
        return this.overhaulQuality;
    }

    /**
     * set the overhaulQuality - 检修性质
     */
    public void setOverhaulQuality(String overhaulQuality) {
        this.overhaulQuality = overhaulQuality;
    }

    /**
     * get the overhaulType - 检修类别
     *
     * @return the overhaulType
     */
    public String getOverhaulType() {
        return this.overhaulType;
    }

    /**
     * set the overhaulType - 检修类别
     */
    public void setOverhaulType(String overhaulType) {
        this.overhaulType = overhaulType;
    }

    /**
     * get the overhaulStartDate - 计划检修开始日期
     *
     * @return the overhaulStartDate
     */
    public String getOverhaulStartDate() {
        return this.overhaulStartDate;
    }

    /**
     * set the overhaulStartDate - 计划检修开始日期
     */
    public void setOverhaulStartDate(String overhaulStartDate) {
        this.overhaulStartDate = overhaulStartDate;
    }

    /**
     * get the overhaulEndDate - 计划检修结束日期
     *
     * @return the overhaulEndDate
     */
    public String getOverhaulEndDate() {
        return this.overhaulEndDate;
    }

    /**
     * set the overhaulEndDate - 计划检修结束日期
     */
    public void setOverhaulEndDate(String overhaulEndDate) {
        this.overhaulEndDate = overhaulEndDate;
    }

    /**
     * get the overhaulNumber - 计划检修人数
     *
     * @return the overhaulNumber
     */
    public BigDecimal getOverhaulNumber() {
        return this.overhaulNumber;
    }

    /**
     * set the overhaulNumber - 计划检修人数
     */
    public void setOverhaulNumber(BigDecimal overhaulNumber) {
        this.overhaulNumber = overhaulNumber;
    }

    /**
     * get the overhaulTime - 计划检修时间
     *
     * @return the overhaulTime
     */
    public BigDecimal getOverhaulTime() {
        return this.overhaulTime;
    }

    /**
     * set the overhaulTime - 计划检修时间
     */
    public void setOverhaulTime(BigDecimal overhaulTime) {
        this.overhaulTime = overhaulTime;
    }

    /**
     * get the overhaulProject - 计划检修项目
     *
     * @return the overhaulProject
     */
    public String getOverhaulProject() {
        return this.overhaulProject;
    }

    /**
     * set the overhaulProject - 计划检修项目
     */
    public void setOverhaulProject(String overhaulProject) {
        this.overhaulProject = overhaulProject;
    }

    /**
     * get the exceptionContactId - 异常联络单号
     *
     * @return the exceptionContactId
     */
    public String getExceptionContactId() {
        return this.exceptionContactId;
    }

    /**
     * set the exceptionContactId - 异常联络单号
     */
    public void setExceptionContactId(String exceptionContactId) {
        this.exceptionContactId = exceptionContactId;
    }

    /**
     * get the overhaulSource - 检修来源
     *
     * @return the overhaulSource
     */
    public String getOverhaulSource() {
        return this.overhaulSource;
    }

    /**
     * set the overhaulSource - 检修来源
     */
    public void setOverhaulSource(String overhaulSource) {
        this.overhaulSource = overhaulSource;
    }

    /**
     * get the outsourcingContactId - 委外联络单号
     *
     * @return the outsourcingContactId
     */
    public String getOutsourcingContactId() {
        return this.outsourcingContactId;
    }

    /**
     * set the outsourcingContactId - 委外联络单号
     */
    public void setOutsourcingContactId(String outsourcingContactId) {
        this.outsourcingContactId = outsourcingContactId;
    }

    /**
     * get the securityMeasures - 安全措施
     *
     * @return the securityMeasures
     */
    public String getSecurityMeasures() {
        return this.securityMeasures;
    }

    /**
     * set the securityMeasures - 安全措施
     */
    public void setSecurityMeasures(String securityMeasures) {
        this.securityMeasures = securityMeasures;
    }

    /**
     * get the acceptanceCriteria - 检修验收标准
     *
     * @return the acceptanceCriteria
     */
    public String getAcceptanceCriteria() {
        return this.acceptanceCriteria;
    }

    /**
     * set the acceptanceCriteria - 检修验收标准
     */
    public void setAcceptanceCriteria(String acceptanceCriteria) {
        this.acceptanceCriteria = acceptanceCriteria;
    }

    /**
     * get the implementManId - 实施人
     *
     * @return the implementManId
     */
    public String getImplementManId() {
        return this.implementManId;
    }

    /**
     * set the implementManId - 实施人
     */
    public void setImplementManId(String implementManId) {
        this.implementManId = implementManId;
    }

    /**
     * get the implementManName - 实施人姓名
     *
     * @return the implementManName
     */
    public String getImplementManName() {
        return this.implementManName;
    }

    /**
     * set the implementManName - 实施人姓名
     */
    public void setImplementManName(String implementManName) {
        this.implementManName = implementManName;
    }

    /**
     * get the overhaulPlanStatus - 检修计划状态
     *
     * @return the overhaulPlanStatus
     */
    public String getOverhaulPlanStatus() {
        return this.overhaulPlanStatus;
    }

    /**
     * set the overhaulPlanStatus - 检修计划状态
     */
    public void setOverhaulPlanStatus(String overhaulPlanStatus) {
        this.overhaulPlanStatus = overhaulPlanStatus;
    }

    /**
     * get the overhaulImplementDate - 检修实施日期
     *
     * @return the overhaulImplementDate
     */
    public String getOverhaulImplementDate() {
        return this.overhaulImplementDate;
    }

    /**
     * set the overhaulImplementDate - 检修实施日期
     */
    public void setOverhaulImplementDate(String overhaulImplementDate) {
        this.overhaulImplementDate = overhaulImplementDate;
    }

    /**
     * get the actualOverhaulNumber - 实际检修人数
     *
     * @return the actualOverhaulNumber
     */
    public BigDecimal getActualOverhaulNumber() {
        return this.actualOverhaulNumber;
    }

    /**
     * set the actualOverhaulNumber - 实际检修人数
     */
    public void setActualOverhaulNumber(BigDecimal actualOverhaulNumber) {
        this.actualOverhaulNumber = actualOverhaulNumber;
    }

    /**
     * get the actualOverhaulTime - 实际检修时间
     *
     * @return the actualOverhaulTime
     */
    public BigDecimal getActualOverhaulTime() {
        return this.actualOverhaulTime;
    }

    /**
     * set the actualOverhaulTime - 实际检修时间
     */
    public void setActualOverhaulTime(BigDecimal actualOverhaulTime) {
        this.actualOverhaulTime = actualOverhaulTime;
    }

    /**
     * get the isComplete - 是否完成
     *
     * @return the isComplete
     */
    public String getIsComplete() {
        return this.isComplete;
    }

    /**
     * set the isComplete - 是否完成
     */
    public void setIsComplete(String isComplete) {
        this.isComplete = isComplete;
    }

    /**
     * get the overhaulLegacyProject - 遗留检修项目
     *
     * @return the overhaulLegacyProject
     */
    public String getOverhaulLegacyProject() {
        return this.overhaulLegacyProject;
    }

    /**
     * set the overhaulLegacyProject - 遗留检修项目
     */
    public void setOverhaulLegacyProject(String overhaulLegacyProject) {
        this.overhaulLegacyProject = overhaulLegacyProject;
    }

    /**
     * get the isConformStandard - 是否符合标准
     *
     * @return the isConformStandard
     */
    public String getIsConformStandard() {
        return this.isConformStandard;
    }

    /**
     * set the isConformStandard - 是否符合标准
     */
    public void setIsConformStandard(String isConformStandard) {
        this.isConformStandard = isConformStandard;
    }

    /**
     * get the relevantMeasures - 相关措施
     *
     * @return the relevantMeasures
     */
    public String getRelevantMeasures() {
        return this.relevantMeasures;
    }

    /**
     * set the relevantMeasures - 相关措施
     */
    public void setRelevantMeasures(String relevantMeasures) {
        this.relevantMeasures = relevantMeasures;
    }

    /**
     * get the isHot - 是否动火
     *
     * @return the isHot
     */
    public String getIsHot() {
        return this.isHot;
    }

    /**
     * set the isHot - 是否动火
     */
    public void setIsHot(String isHot) {
        this.isHot = isHot;
    }

    /**
     * get the hotCardId - 动火证编号
     *
     * @return the hotCardId
     */
    public String getHotCardId() {
        return this.hotCardId;
    }

    /**
     * set the hotCardId - 动火证编号
     */
    public void setHotCardId(String hotCardId) {
        this.hotCardId = hotCardId;
    }

    /**
     * get the offlinePartsGone - 下线零件去向
     *
     * @return the offlinePartsGone
     */
    public String getOfflinePartsGone() {
        return this.offlinePartsGone;
    }

    /**
     * set the offlinePartsGone - 下线零件去向
     */
    public void setOfflinePartsGone(String offlinePartsGone) {
        this.offlinePartsGone = offlinePartsGone;
    }

    /**
     * get the actualsRevisor - 检修实绩操作人
     *
     * @return the actualsRevisor
     */
    public String getActualsRevisor() {
        return this.actualsRevisor;
    }

    /**
     * set the actualsRevisor - 检修实绩操作人
     */
    public void setActualsRevisor(String actualsRevisor) {
        this.actualsRevisor = actualsRevisor;
    }

    /**
     * get the actualsTime - 检修实绩操作时间
     *
     * @return the actualsTime
     */
    public String getActualsTime() {
        return this.actualsTime;
    }

    /**
     * set the actualsTime - 检修实绩操作时间
     */
    public void setActualsTime(String actualsTime) {
        this.actualsTime = actualsTime;
    }

    /**
     * get the actualsRevisorName - 检修实绩操作人姓名
     *
     * @return the actualsRevisorName
     */
    public String getActualsRevisorName() {
        return this.actualsRevisorName;
    }

    /**
     * set the actualsRevisorName - 检修实绩操作人姓名
     */
    public void setActualsRevisorName(String actualsRevisorName) {
        this.actualsRevisorName = actualsRevisorName;
    }

    /**
     * get the voucherNum - 依据凭单
     *
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 依据凭单
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the apprStatus - 审批状态
     *
     * @return the apprStatus
     */
    public String getApprStatus() {
        return this.apprStatus;
    }

    /**
     * set the apprStatus - 审批状态
     */
    public void setApprStatus(String apprStatus) {
        this.apprStatus = apprStatus;
    }

    /**
     * get the actualLegacyProject - 实际检修项目
     *
     * @return the actualLegacyProject
     */
    public String getActualLegacyProject() {
        return this.actualLegacyProject;
    }

    /**
     * set the actualLegacyProject - 实际检修项目
     */
    public void setActualLegacyProject(String actualLegacyProject) {
        this.actualLegacyProject = actualLegacyProject;
    }

    /**
     * get the processInstanceId - 工作流实例ID
     *
     * @return the processInstanceId
     */
    public String getProcessInstanceId() {
        return this.processInstanceId;
    }

    /**
     * set the processInstanceId - 工作流实例ID
     */
    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    /**
     * get the overhaulSuggestions - 问题及建议
     *
     * @return the overhaulSuggestions
     */
    public String getOverhaulSuggestions() {
        return this.overhaulSuggestions;
    }

    /**
     * set the overhaulSuggestions - 问题及建议
     */
    public void setOverhaulSuggestions(String overhaulSuggestions) {
        this.overhaulSuggestions = overhaulSuggestions;
    }

    /**
     * get the overhaulSummarize - 检修总结
     *
     * @return the overhaulSummarize
     */
    public String getOverhaulSummarize() {
        return this.overhaulSummarize;
    }

    /**
     * set the overhaulSummarize - 检修总结
     */
    public void setOverhaulSummarize(String overhaulSummarize) {
        this.overhaulSummarize = overhaulSummarize;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the delayRemark - 延期备注
     *
     * @return the delayRemark
     */
    public String getDelayRemark() {
        return this.delayRemark;
    }

    /**
     * set the delayRemark - 延期备注
     */
    public void setDelayRemark(String delayRemark) {
        this.delayRemark = delayRemark;
    }

    /**
     * get the deviceCode - 分部设备代码
     *
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     *
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setOverhaulPlanId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulPlanId")), overhaulPlanId));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setOverhaulQuality(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulQuality")), overhaulQuality));
        setOverhaulType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulType")), overhaulType));
        setOverhaulStartDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulStartDate")), overhaulStartDate));
        setOverhaulEndDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulEndDate")), overhaulEndDate));
        setOverhaulNumber(NumberUtils.toBigDecimal(StringUtils.toString(map.get("overhaulNumber")), overhaulNumber));
        setOverhaulTime(NumberUtils.toBigDecimal(StringUtils.toString(map.get("overhaulTime")), overhaulTime));
        setOverhaulProject(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulProject")), overhaulProject));
        setExceptionContactId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("exceptionContactId")), exceptionContactId));
        setOverhaulSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulSource")), overhaulSource));
        setOutsourcingContactId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outsourcingContactId")), outsourcingContactId));
        setSecurityMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("securityMeasures")), securityMeasures));
        setAcceptanceCriteria(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("acceptanceCriteria")), acceptanceCriteria));
        setImplementManId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("implementManId")), implementManId));
        setImplementManName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("implementManName")), implementManName));
        setOverhaulPlanStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulPlanStatus")), overhaulPlanStatus));
        setOverhaulImplementDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulImplementDate")), overhaulImplementDate));
        setActualOverhaulNumber(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actualOverhaulNumber")), actualOverhaulNumber));
        setActualOverhaulTime(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actualOverhaulTime")), actualOverhaulTime));
        setIsComplete(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isComplete")), isComplete));
        setOverhaulLegacyProject(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulLegacyProject")), overhaulLegacyProject));
        setIsConformStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isConformStandard")), isConformStandard));
        setRelevantMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("relevantMeasures")), relevantMeasures));
        setIsHot(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isHot")), isHot));
        setHotCardId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hotCardId")), hotCardId));
        setOfflinePartsGone(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("offlinePartsGone")), offlinePartsGone));
        setActualsRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsRevisor")), actualsRevisor));
        setActualsTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsTime")), actualsTime));
        setActualsRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsRevisorName")), actualsRevisorName));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setApprStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprStatus")), apprStatus));
        setActualLegacyProject(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualLegacyProject")), actualLegacyProject));
        setProcessInstanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processInstanceId")), processInstanceId));
        setOverhaulSuggestions(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulSuggestions")), overhaulSuggestions));
        setOverhaulSummarize(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulSummarize")), overhaulSummarize));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setDelayRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delayRemark")), delayRemark));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setProcessId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processId")), processId));
        setE_archivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("e_archivesNo")), e_archivesNo));
        setUnitName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitName")), unitName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("overhaulPlanId", StringUtils.toString(overhaulPlanId, eiMetadata.getMeta("overhaulPlanId")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("e_archivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("e_archivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("overhaulQuality", StringUtils.toString(overhaulQuality, eiMetadata.getMeta("overhaulQuality")));
        map.put("overhaulType", StringUtils.toString(overhaulType, eiMetadata.getMeta("overhaulType")));
        map.put("overhaulStartDate", StringUtils.toString(overhaulStartDate, eiMetadata.getMeta("overhaulStartDate")));
        map.put("overhaulEndDate", StringUtils.toString(overhaulEndDate, eiMetadata.getMeta("overhaulEndDate")));
        map.put("overhaulNumber", StringUtils.toString(overhaulNumber, eiMetadata.getMeta("overhaulNumber")));
        map.put("overhaulTime", StringUtils.toString(overhaulTime, eiMetadata.getMeta("overhaulTime")));
        map.put("overhaulProject", StringUtils.toString(overhaulProject, eiMetadata.getMeta("overhaulProject")));
        map.put("exceptionContactId", StringUtils.toString(exceptionContactId, eiMetadata.getMeta("exceptionContactId")));
        map.put("overhaulSource", StringUtils.toString(overhaulSource, eiMetadata.getMeta("overhaulSource")));
        map.put("outsourcingContactId", StringUtils.toString(outsourcingContactId, eiMetadata.getMeta("outsourcingContactId")));
        map.put("securityMeasures", StringUtils.toString(securityMeasures, eiMetadata.getMeta("securityMeasures")));
        map.put("acceptanceCriteria", StringUtils.toString(acceptanceCriteria, eiMetadata.getMeta("acceptanceCriteria")));
        map.put("implementManId", StringUtils.toString(implementManId, eiMetadata.getMeta("implementManId")));
        map.put("implementManName", StringUtils.toString(implementManName, eiMetadata.getMeta("implementManName")));
        map.put("overhaulPlanStatus", StringUtils.toString(overhaulPlanStatus, eiMetadata.getMeta("overhaulPlanStatus")));
        map.put("overhaulImplementDate", StringUtils.toString(overhaulImplementDate, eiMetadata.getMeta("overhaulImplementDate")));
        map.put("actualOverhaulNumber", StringUtils.toString(actualOverhaulNumber, eiMetadata.getMeta("actualOverhaulNumber")));
        map.put("actualOverhaulTime", StringUtils.toString(actualOverhaulTime, eiMetadata.getMeta("actualOverhaulTime")));
        map.put("isComplete", StringUtils.toString(isComplete, eiMetadata.getMeta("isComplete")));
        map.put("overhaulLegacyProject", StringUtils.toString(overhaulLegacyProject, eiMetadata.getMeta("overhaulLegacyProject")));
        map.put("isConformStandard", StringUtils.toString(isConformStandard, eiMetadata.getMeta("isConformStandard")));
        map.put("relevantMeasures", StringUtils.toString(relevantMeasures, eiMetadata.getMeta("relevantMeasures")));
        map.put("isHot", StringUtils.toString(isHot, eiMetadata.getMeta("isHot")));
        map.put("hotCardId", StringUtils.toString(hotCardId, eiMetadata.getMeta("hotCardId")));
        map.put("offlinePartsGone", StringUtils.toString(offlinePartsGone, eiMetadata.getMeta("offlinePartsGone")));
        map.put("actualsRevisor", StringUtils.toString(actualsRevisor, eiMetadata.getMeta("actualsRevisor")));
        map.put("actualsTime", StringUtils.toString(actualsTime, eiMetadata.getMeta("actualsTime")));
        map.put("actualsRevisorName", StringUtils.toString(actualsRevisorName, eiMetadata.getMeta("actualsRevisorName")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("apprStatus", StringUtils.toString(apprStatus, eiMetadata.getMeta("apprStatus")));
        map.put("actualLegacyProject", StringUtils.toString(actualLegacyProject, eiMetadata.getMeta("actualLegacyProject")));
        map.put("processInstanceId", StringUtils.toString(processInstanceId, eiMetadata.getMeta("processInstanceId")));
        map.put("overhaulSuggestions", StringUtils.toString(overhaulSuggestions, eiMetadata.getMeta("overhaulSuggestions")));
        map.put("overhaulSummarize", StringUtils.toString(overhaulSummarize, eiMetadata.getMeta("overhaulSummarize")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("unitName", StringUtils.toString(unitCode, eiMetadata.getMeta("unitName")));
        map.put("delayRemark", StringUtils.toString(delayRemark, eiMetadata.getMeta("delayRemark")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("processId", StringUtils.toString(processId, eiMetadata.getMeta("processId")));
        return map;

    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getE_archivesNo() {
        return e_archivesNo;
    }

    public void setE_archivesNo(String e_archivesNo) {
        this.e_archivesNo = e_archivesNo;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}