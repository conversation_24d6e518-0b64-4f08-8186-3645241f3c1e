<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<%@ page import="com.baosight.xservices.xs.constants.LoginConstants" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<script src="${ctx}/iplatui/js/jsencrypt.js"></script>

<%
    String checkpasswordSwitch = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.default.password.switch"), "on");
    request.setAttribute("checkpasswordSwitch", checkpasswordSwitch);
    if ("on".equals(checkpasswordSwitch)) {
        String passwordTip = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.checkpassword.desc"), "密码必须包含英文及数字");
        request.setAttribute("passwordTip", passwordTip);
    } else {
        request.setAttribute("passwordTip", "密码由不超过255位的英文字母或者数字字符或下划线组成。");
    }
    String passwordRegex = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.checkpassword.regex"), "^(?=.*?[a-zA-Z])(?=.*?[0-9]).{1,}$");
    String passwordDesc = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("xservices.security.checkpassword.desc"), "密码必须包含英文及数字");
    String loginPublicKey = LoginConstants.loginRsaPublicKey;
    String cryptoPasswordEnable = LoginConstants.cryptoPasswordEnable;
%>
<c:set var="pwdRgx" value="<%=passwordRegex%>"/>
<c:set var="pwdDesc" value="<%=passwordDesc%>"/>
<c:set var="login_PublicKey" value="<%=loginPublicKey%>"/>
<c:set var="crypto_PasswordEnable" value="<%=cryptoPasswordEnable%>"/>
<EF:EFPage title = "用户管理">
    <EF:EFInput ename="__LOGIN_PUBLICKEY__" value="${login_PublicKey}" type="hidden"/>
    <EF:EFInput ename="cryptoPasswordEnable" value="${crypto_PasswordEnable}" type="hidden"/>
    <EF:EFRegion id="inqu" title="查询条件" type="query" efRegionShowClear="true" efRegionSave="true">
        <div class="row">
            <div class="col-xs-3">
                <div class="form-group">
                    <label class="col-md-4 control-label">
                        登录账号
                    </label>
                    <div class="col-md-8">
                        <input name="inqu_status-0-loginName" data-query="gt" class="k-textbox input-time query-need"
                               placeholder="请输入登录账号">
                    </div>
                </div>
            </div>
            <div class="col-xs-3">
                <div class="form-group">
                    <label class="col-md-4 control-label">
                        用户姓名
                    </label>
                    <div class="col-md-8">
                        <input name="inqu_status-0-userName" data-query="gt" class="k-textbox input-time query-need"
                               placeholder="请输入用户姓名">
                    </div>
                </div>
            </div>
            <EF:EFSelect cname="用户类别" blockId="inqu_status" colWidth="3" ename="userType" row="0" defaultValue="全部">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="xservices.xs.userType" textField="label" valueField="value"/>
            </EF:EFSelect>

            <EF:EFSelect cname="是否锁定" blockId="inqu_status" colWidth="3" ename="isLocked" row="0" defaultValue="全部">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="xservices.xs.userIsLocked" textField="label" valueField="value"/>
            </EF:EFSelect>

            <div class="col-xs-3">
                <div class="form-group">
                    <label class="col-md-4 control-label">
                        用户组编码
                    </label>
                    <div class="col-md-8">
                        <input name="inqu_status-0-userGroupEname" data-query="gt"
                               class="k-textbox input-time query-need"
                               placeholder="请输入用户组编码">
                    </div>
                </div>
            </div>

            <div class="col-xs-9" style="text-align: right" id="inqu_inside"></div>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" autoDraw="false">
            <EF:EFColumn ename="userId" cname="用户ID" readonly="false" primaryKey="true" hidden="true"/>
            <EF:EFColumn ename="segNo" cname="业务单元号" style="text-align:left;" readonly="true"/>
            <EF:EFColumn ename="password" cname="用户密码" hidden="true"/>
            <EF:EFColumn ename="loginName" cname="登录账号" readonly="true" locked="true" style="text-align:left;"/>
            <%--<EF:EFColumn ename="status" cname="状态" hidden="true"/>--%>
            <EF:EFComboColumn ename="status" cname="状态" style="text-align:center;" hidden="true">
                <EF:EFCodeOption codeName="xservices.xs.userStatus" textField="label" valueField="value"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="userName" cname="用户姓名" style="text-align:left;"/>
            <EF:EFComboColumn ename="isLocked" cname="是否锁定" style="text-align:center;">
                <EF:EFCodeOption codeName="xservices.xs.userIsLocked" textField="label" valueField="value"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="status" cname="用户状态" style="text-align:center;">
                <EF:EFCodeOption codeName="xservices.xs.userStatus" textField="label" valueField="value"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="gender" cname="性别" valueField="valueField" textField="textField" columnTemplate="#=textField#" align="center">
                <EF:EFCodeOption codeName="xservices.xs.sex" />
            </EF:EFComboColumn>
            <EF:EFColumn ename="mobile" style="text-align:right;" cname="手机" data-rules="mobile_phone"/>
            <EF:EFColumn ename="email" cname="邮箱" data-rules="email" style="text-align:left;"/>
            <EF:EFComboColumn ename="userType" cname="用户类别" style="text-align:center;">
                <EF:EFCodeOption codeName="xservices.xs.userType" textField="label" valueField="value"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="accountExpireDate" style="text-align:right;" cname="账号过期时间" editType="date"
                         displayType="date" parseFormats="['yyyyMMdd','yyyy-MM-dd']" dateFormat="yyyy-MM-dd"/>
            <EF:EFColumn ename="pwdExpireDate" style="text-align:right;" cname="密码过期时间" editType="date"
                         displayType="date" parseFormats="['yyyyMMdd','yyyy-MM-dd']" dateFormat="yyyy-MM-dd"
                         readonly="true"/>
            <EF:EFColumn ename="recCreator" cname="创建人" enable="false" style="text-align:left;"/>
            <EF:EFColumn ename="recCreateTime" style="text-align:right;" cname="创建时间" editType="datetime"
                         parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                         displayType="datetime" readonly="true"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" enable="false" style="text-align:left;"/>
            <EF:EFColumn ename="recReviseTime" style="text-align:right;" cname="修改时间" editType="datetime"
                         parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                         displayType="datetime" readonly="true"/>
            <EF:EFColumn ename="pwdReviseDate" cname="密码修改时间" readonly="true" editType="datetime"
                         parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" displayType="datetime" enable="false"
                         style="text-align:right;"/>
            <EF:EFColumn ename="pwdRevisor" cname="密码修改人" enable="false" style="text-align:left;"/>
            <EF:EFColumn ename="archiveFlag" cname="归档标记" style="text-align:right;"/>
        </EF:EFGrid>

        <EF:EFWindow id="insertGroup" top="100px" left="280px" width="58%" height="75%">
            <div id="ef_popup_gridB">
                <EF:EFRegion id="inquB" title="查询条件" type="query" efRegionShowClear="true" efRegionSave="true">
                    <div class="row">
                        <div class="col-xs-2 control-label">
                            <span>用户组编码</span>
                        </div>
                        <div class="col-xs-2">
                            <EF:EFInput ename="inqu_status-0-gEname" cname="用户组编码" inline="true"/>
                        </div>
                        <div class="col-xs-3 control-label">
                            <span>用户组中文名</span>
                        </div>
                        <div class="col-xs-2">
                            <EF:EFInput ename="inqu_status-0-gCname" cname="用户组中文名" inline="true"/>
                        </div>
                        <div class="col-xs-3" style="text-align: right" id="inqub_inside"></div>
                    </div>
                </EF:EFRegion>
                <EF:EFRegion id="resultB" title="记录集">
                    <EF:EFGrid checkMode="single" serviceName="XS01" blockId="resultB" queryMethod="queryGroup"
                               autoDraw="false">
                        <EF:EFColumn ename="groupId" locked="true" cname="群组ID" hidden="true" primaryKey="true"/>
                        <EF:EFColumn ename="gEname" cname="群组英文名" readonly="true" width="300"/>
                        <EF:EFColumn ename="gCname" cname="群组中文名" readonly="true" width="300"/>
                    </EF:EFGrid>
                </EF:EFRegion>
            </div>
        </EF:EFWindow>


        <EF:EFWindow id="REGISTERUSER" height="60%" width="62%" top="100px" left="250px">
            <EF:EFRegion id="inqua" title="注册新用户">
                <div id="registerUser">



 <%--                       <EF:EFPopupInput blockId="details" ename="segNo" cname="业务单元代码" resizable="true" colWidth="3"
                                         ratio="4:8"
                                         readonly="true" backFillFieldIds="inqua_status-0-segNo"
                                         containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                                         center="true"
                                         popupTitle="业务套账查询">
                        </EF:EFPopupInput>
                        </br>
                        <EF:EFInput ename="inqua_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
                        </br>
                        <EF:EFInput ename="inqua_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
                        </br>--%>
                    <div class="row">
                       <div class="col-xs-1 control-label" style="text-align:right">
                            <span></span>
                        </div>
                        <div>
                            <EF:EFPopupInput blockId="details" ename="segNo" cname="业务单元号" row="0" colWidth="3"
                                        data-errorPrompt="对不起,业务单元账套不能超过32个字符"
                                        validateGroupName="group1" inline="true" containerId="unitInfo"
                                             popupWidth="600" pupupHeight="300" originalInput="true"
                                             center="true" required="true"
                            >
                            </EF:EFPopupInput>
                        </div>
<%--                        <div class="col-xs-7">
                            <span id="details-0-segNo-prompt">【业务单元账套必选。】</span>
                        </div>--%>
                    </div>
                    </br>
                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>登录账号</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFInput blockId="details" cname="登录账号" ename="loginName" row="0"
                                        data-regex="/^[_a-zA-Z0-9]{1,64}$/"
                                        data-errorPrompt="对不起,登录账号只能是不超过64位的英文字母或者数字字符或下划线"
                                        validateGroupName="group1" required="required" inline="true"/>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-loginName-prompt">【登录账号只能由不超过64位的英文字母或者数字字符或者下划线组成。】</span>
                        </div>
                    </div>
                    </br>


                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>用户姓名</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFInput blockId="details" cname="用户姓名" ename="userName" row="0"
                                        trim="false" data-regex="/^[^\x22\x27]+$/"
                                        data-errorPrompt="用户姓名应该由至少一个，最多128个字节大小的字符组成，并且不能包含控制字符和单，双引号"
                                        validateGroupName="group1" inline="true"/>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-userName-prompt">【用户姓名应该由至少一个，最多128个字节大小的字符组成，并且不能包含控制字符和单，双引号。】</span>
                        </div>
                    </div>
                    </br>

                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>登录密码</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFInput type="password" blockId="details" cname="登录密码" ename="password"
                                        row="0" validateGroupName="group1"
                                        trim="false" data-regex="/${pwdRgx}/"
                                        data-errorPrompt="对不起,${pwdDesc}"
                                        inline="true"/>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-password-prompt">【${passwordTip}】</span>
                        </div>
                    </div>
                    </br>

                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>确认密码</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFInput type="password" blockId="details" cname="确认密码" ename="rePass" row="0"
                                        trim="false" data-regex="/${pwdRgx}/" validateGroupName="group1"
                                        data-errorPrompt="对不起,${pwdDesc}"
                                        inline="true"/>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-rePass-prompt">【请再次输入登录密码】</span>
                        </div>
                    </div>
                    </br>

                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>手机号码</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFInput blockId="details" cname="手机号码" ename="mobile" row="0" trim="false"
                                        data-regex="/^1\d{10}$/" data-errorPrompt="手机号码只能是1开头的11位数字"
                                        validateGroupName="group1" inline="true"/>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-mobile-prompt">【请输入手机号码】</span>
                        </div>
                    </div>
                    </br>

                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>电子邮件</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFInput blockId="details" cname="电子邮件" ename="email" row="0" trim="false"
                                        required="false"
                                        data-rules="email" validateGroupName="group1" inline="true"/>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-email-prompt">【请输入电子邮件】</span>
                        </div>
                    </div>
                    </br>

                    <div class="row">
                        <div class="col-xs-2 control-label" style="text-align:right">
                            <span>性别</span>
                        </div>
                        <div class="col-xs-3">
                            <EF:EFSelect ename="details-0-gender" cname="性别"
                                         valueTemplate="#=textField#" inline="true"
                                         style="width: 80%;">
                                <EF:EFCodeOption codeName="xservices.xs.sex"/>
                            </EF:EFSelect>
                        </div>
                        <div class="col-xs-7">
                            <span id="details-0-sex-prompt">【请输入性别】</span>
                        </div>
                    </div>

                    </br>
                </div>
                <%-- <div class="row" id="groupName">
                     <div class="col-xs-2 control-label" style="text-align:right">
                         <span>用户组编码</span>
                     </div>
                     <div class="col-xs-3">
                         <EF:EFInput blockId="inqu_status" cname="所在用户组编码" ename="groupName" row="0" trim="false" required="false"
                                     data-regex="/^[a-zA-Z0-9]{1,32}$/"  data-errorPrompt="用户组编码只能是32位以内的字母与数字" validateGroupName="group2" inline="true"/>
                     </div>
                     <div class="col-xs-7">
                         <span id="inqu_status-0-groupName-prompt">【(可不填写)请输入用户所在的用户组编码】</span>
                     </div>
                 </div>
                 </br>--%>

                <%--<div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-groupName" cname="用户组编码" resizable="true" colWidth="5"
                                     readonly="true" containerId="insertUserGroupEname" textField="groupName"
                                     popupWidth="700"
                                     popupTitle="请选择用户组" ratio="5:6">
                    </EF:EFPopupInput>
                    <div class="col-xs-7">
                        【可不填】
                    </div>
                </div>--%>

                <%-- <div class="row">
                     <div class="col-md-9" style="text-align: right" id="inqua_inside"></div>
                 </div>--%>

                <%--<EF:EFButton cname="确定" ename="REGISTERNEWUSER"/>--%>
            </EF:EFRegion>


        </EF:EFWindow>
    </EF:EFRegion>

    <div id="insertUserGroupEname" style="display: none">
        <EF:EFRegion id="inquC" title="查询条件" type="query" efRegionShowClear="true" efRegionSave="true">
            <div class="row">
                <div class="col-xs-2 control-label">
                    <span>用户组编码</span>
                </div>
                <div class="col-xs-2">
                    <EF:EFInput ename="inqu_status-0-groupEname" cname="用户组编码" inline="true"/>
                </div>
                <div class="col-xs-3 control-label">
                    <span>用户组中文名</span>
                </div>
                <div class="col-xs-2">
                    <EF:EFInput ename="inqu_status-0-groupCname" cname="用户组中文名" inline="true"/>
                </div>
                <div class="col-xs-3" style="text-align: right" id="inquc_inside"></div>
            </div>
        </EF:EFRegion>
        <EF:EFRegion id="resultC" title="记录集">
            <EF:EFGrid checkMode="single" serviceName="XS0102" blockId="resultC" queryMethod="getUserGroupEname"
                       autoDraw="false">
                <EF:EFColumn ename="groupEname" cname="用户组编码" readonly="true"/>
                <EF:EFColumn ename="groupCname" cname="用户组中文名" readonly="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>
    <input type="hidden" id="editRow"/>

    <EF:EFTab id="authInfo" showClose="false">
        <div title="所属用户组">
            <EF:EFInput blockId="inqu_status" row="0" ename="userIdForParentGroups" cname="登录账号" type="hidden"/>
            <EF:EFGrid blockId="resultD" enable="false" queryMethod="queryUserParentGroups" autoDraw="false">
                <EF:EFColumn ename="groupId" locked="true" cname="群组ID" hidden="true" primaryKey="true"/>
                <EF:EFColumn ename="groupEname" cname="群组英文名" readonly="true" width="300"/>
                <EF:EFColumn ename="groupCname" cname="群组中文名" readonly="true" width="300"/>
            </EF:EFGrid>
        </div>
        <div title="权限信息">
            <EF:EFInput blockId="inqu_status" row="0" ename="loginNameForAuthInfo" cname="登录账号" type="hidden"/>
            <EF:EFGrid blockId="resultE" queryMethod="queryUserAuthInfo" autoDraw="false" enable="false"
                       filterable="true">
                <EF:EFColumn ename="resourceEname" cname="资源英文名" style="text-align:left;" readonly="true" locked="true"
                             width="300"/>
                <EF:EFColumn ename="resourceCname" cname="资源中文名" style="text-align:left;" readonly="true" width="300"/>
            </EF:EFGrid>
        </div>
    </EF:EFTab>

    <EF:EFWindow id="authCopy" width="58%" top="100px" left="280px">
        <div id="ef_popup_gridF">
            <EF:EFRegion id="inquF" title="查询条件" type="query" efRegionShowClear="true" efRegionSave="true">
                <div class="row">
                    <EF:EFInput ename="inqu_status-0-insertUserParentId" cname="群组ID" type="hidden"/>
                    <div class="col-xs-2 control-label">
                        <span>登录账号</span>
                    </div>
                    <div class="col-xs-2">
                        <EF:EFInput ename="inqu_status-0-authCopyLoginName" cname="登录账号" inline="true"/>
                    </div>
                    <div class="col-xs-2 control-label">
                        <span>用户姓名</span>
                    </div>
                    <div class="col-xs-2">
                        <EF:EFInput ename="inqu_status-0-authCopyUserName" cname="用户姓名" inline="true"/>
                    </div>
                    <div class="col-xs-4" style="text-align: right" id="inquf_inside"></div>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="resultF" title="记录集">
                <EF:EFGrid blockId="resultF" queryMethod="queryForMemberUsers" autoDraw="false">
                    <EF:EFColumn ename="userId" locked="true" cname="用户ID" hidden="true" primaryKey="true"/>
                    <EF:EFColumn ename="loginName" locked="true" cname="登录账号" readonly="true" width="300"/>
                    <EF:EFColumn ename="userName" cname="用户姓名" readonly="true" width="200"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFWindow>


    <EF:EFWindow id="authMember" width="58%" top="100px" left="280px" height="50%">
        <div id="ef_popup_gridG">
            <EF:EFInput ename="inqu_status-0-checkUserParentId" cname="群组ID" type="hidden"/>
            <EF:EFRegion id="resultG" title="记录集">
                <EF:EFGrid blockId="resultG" queryMethod="queryForAuthMemberUsers" autoDraw="false" filterable="true"
                           enable="false">
                    <EF:EFColumn ename="userId" locked="true" cname="用户ID" hidden="true" primaryKey="true"/>
                    <EF:EFColumn ename="loginName" locked="true" cname="登录账号" readonly="true" width="300"/>
                    <EF:EFColumn ename="userName" cname="用户姓名" readonly="true" width="200"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFWindow>

    <input type="hidden" id="checkpasswordSwitch" value="${checkpasswordSwitch}"/>

    <div style="display:none;">
        <EF:EFWindow id="clip" title="粘贴板导入" height="20%" width="25%">
            <textarea id="clipContent" name="clipContent" class="json_input" rows="16" style="width: 99%; height: 95%;"
                      spellcheck="false" placeholder="请粘贴"></textarea>
        </EF:EFWindow>
    </div>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="68%" height="60%"></EF:EFWindow>

</EF:EFPage>
