$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();



    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {
                //生效(状态不为新增，不可生效)
                $("#VALIDATE").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "10" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为新增,不可生效!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0102", "validateCross", true, null, null, false);
                });

                //反生效(状态不为生效，不可反生效)
                $("#DEVALIDATION").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    let rows = resultGrid.getCheckedRows();
                    for (let i = 0; i < rows.length; i++) {
                        if (rows[i].status !== "20" || rows[i].isNew()) {
                            NotificationUtil({msg: "勾选数据状态不为生效,不可反生效!"}, "error");
                            return;
                        }
                    }
                    IMOMUtil.submitGridsData("result", "LIDS0102", "deValidateCross", true, null, null, false);
                });

                //弹框编号为空时，隐藏确认回填按钮
                let windowId = $("#inqu_status-0-windowId").val();
                if(IPLAT.isBlankString(windowId)){
                    $("#CONFIRM").hide();
                }
                //确认回填
                $("#CONFIRM").on("click", function (e) {
                    let windowId = $("#inqu_status-0-windowId").val();
                    if (!IPLAT.isBlankString(windowId)) {
                        //判断是否勾选数据
                        let checkRows = resultGrid.getCheckedRows();
                        if(checkRows.length < 1){
                            NotificationUtil({msg: "未勾选需要回填的数据，不可点击确认!"}, "error");
                            return;
                        }
                        //校验勾选厂区，厂房必须一致(取第一条的数据)
                        let factoryArea = checkRows[0].factoryArea;
                        let factoryBuilding = checkRows[0].factoryBuilding;
                        for (let i = 1; i < checkRows.length; i++) {
                            if (checkRows[i].factoryArea !== factoryArea || checkRows[i].factoryBuilding !== factoryBuilding) {
                                NotificationUtil({msg: "所勾选的信息中，厂区厂房不一致!"}, "error");
                                return;
                            }
                        }
                        //关闭下拉框
                        window.parent[windowId + "Window"].close();
                    }
                });
            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
                /**
                 * 状态不为新增时，不允许编辑
                 */
                if(e.model.status !== "10" && !e.model.isNew()){
                    e.preventDefault();
                    return;
                }
            },
            onDelete: function (e) {
                let rows = e.sender.getCheckedRows();
                for (let i = 0; i < rows.length; i++) {
                    if (rows[i].status !== "10") {
                        NotificationUtil({msg: "勾选数据状态不为新增,不可删除!"}, "error");
                        e.preventDefault();
                        return;
                    }
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

})