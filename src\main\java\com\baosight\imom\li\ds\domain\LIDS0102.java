package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0102;

import java.util.Map;

/**
 * 厂区厂房管理
 */
public class LIDS0102 extends Tlids0102 {
    public static final String QUERY = "LIDS0102.query";
    public static final String QUERY_TO_MAP = "LIDS0102.queryToMap";
    public static final String QUERY_FACTORY = "LIDS0102.queryFactory";
    public static final String COUNT = "LIDS0102.count";
    public static final String COUNT_UUID = "LIDS0102.count_uuid";
    public static final String INSERT = "LIDS0102.insert";
    public static final String UPDATE = "LIDS0102.update";
    public static final String UPDATE_STATUS = "LIDS0102.updateStatus";
    public static final String DELETE = "LIDS0102.delete";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS0102() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }

}
