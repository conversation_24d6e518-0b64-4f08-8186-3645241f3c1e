/**
* Generate time : 2025-05-29 9:34:18
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids1301
* 
*/
public class LIDS1301 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String hoistType = " ";		/* 吊具类型*/
                private String hoistId = " ";		/* 吊具编号*/
                private BigDecimal hoistWeight = new BigDecimal(0.000);		/* 吊具重量*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                public static final String QUERY = "LIDS1301.query";
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hoistType");
        eiColumn.setDescName("吊具类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hoistId");
        eiColumn.setDescName("吊具编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hoistWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("吊具重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIDS1301() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the hoistType - 吊具类型
        * @return the hoistType
        */
        public String getHoistType() {
        return this.hoistType;
        }

        /**
        * set the hoistType - 吊具类型
        */
        public void setHoistType(String hoistType) {
        this.hoistType = hoistType;
        }
        /**
        * get the hoistId - 吊具编号
        * @return the hoistId
        */
        public String getHoistId() {
        return this.hoistId;
        }

        /**
        * set the hoistId - 吊具编号
        */
        public void setHoistId(String hoistId) {
        this.hoistId = hoistId;
        }
        /**
        * get the hoistWeight - 吊具重量
        * @return the hoistWeight
        */
        public BigDecimal getHoistWeight() {
        return this.hoistWeight;
        }

        /**
        * set the hoistWeight - 吊具重量
        */
        public void setHoistWeight(BigDecimal hoistWeight) {
        this.hoistWeight = hoistWeight;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setHoistType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hoistType")), hoistType));
                setHoistId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hoistId")), hoistId));
                setHoistWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("hoistWeight")), hoistWeight));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("hoistType",StringUtils.toString(hoistType, eiMetadata.getMeta("hoistType")));
                map.put("hoistId",StringUtils.toString(hoistId, eiMetadata.getMeta("hoistId")));
                map.put("hoistWeight",StringUtils.toString(hoistWeight, eiMetadata.getMeta("hoistWeight")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));

return map;

}
}