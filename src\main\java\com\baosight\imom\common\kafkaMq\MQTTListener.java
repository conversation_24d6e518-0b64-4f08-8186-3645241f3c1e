package com.baosight.imom.common.kafkaMq;

import com.baosight.imom.common.utils.SwitchUtils;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
public class MQTTListener implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger log = LoggerFactory.getLogger(Callback.class);

    private final MQTTConnect server;

    //监听开关
    private final String listenerOpen = PlatApplicationContext.getProperty("mqttListenerSwitch");

    @Autowired
    public MQTTListener(MQTTConnect server) {
        this.server = server;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        try {
            if ("true".equals(listenerOpen)) {
                server.start();
                server.sub("/LocXYZ/#", 2);//实时获取所有定位标签位置
                server.sub("/publish_data/weight_card/#", 2);//监听行车抓取释放事件
                /*server.sub("/publish_data/in_area/#", 2);//监听人员进入区域事件
                server.sub("/publish_data/out_area/#", 2);//监听人员离开区域事件*/
                log.info("-----------消息订阅成功-----------");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("-----------消息订阅失败-----------");
        }
    }

}
