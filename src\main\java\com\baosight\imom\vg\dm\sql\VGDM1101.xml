<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1101">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="procedureName">
            PROCEDURE_NAME like concat('%',#procedureName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ruleStatus">
            RULE_STATUS = #ruleStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="ruleStatus">
            RULE_STATUS != '00'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1101">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCEDURE_CODE as "procedureCode",  <!-- 工序代码 -->
        PROCEDURE_NAME as "procedureName",  <!-- 工序名称 -->
        START_TIME_RULE as "startTimeRule",  <!-- 开始时间规则 -->
        STOP_TIME_RULE as "stopTimeRule",  <!-- 结束时间规则 -->
        RULE_STATUS as "ruleStatus",  <!-- 规则状态 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        MULTI_FLAG as "multiFlag",  <!-- 多次标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        IHD_START_RULE as "ihdStartRule",  <!-- IHD开始时间规则 -->
        IHD_STOP_RULE as "ihdStopRule",  <!-- IHD结束时间规则 -->
        REMARK as "remark"  <!-- 备注 -->
        FROM ${mevgSchema}.TVGDM1101 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                E_ARCHIVES_NO,SORT_INDEX asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1101 WHERE 1=1
        <include refid="condition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1101 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCEDURE_CODE,  <!-- 工序代码 -->
        PROCEDURE_NAME,  <!-- 工序名称 -->
        START_TIME_RULE,  <!-- 开始时间规则 -->
        STOP_TIME_RULE,  <!-- 结束时间规则 -->
        RULE_STATUS,  <!-- 规则状态 -->
        SORT_INDEX,  <!-- 排序 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        MULTI_FLAG,  <!-- 多次标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        IHD_START_RULE,  <!-- IHD开始时间规则 -->
        IHD_STOP_RULE,  <!-- IHD结束时间规则 -->
        REMARK  <!-- 备注 -->
        )
        VALUES (#eArchivesNo#, #equipmentName#, #procedureCode#, #procedureName#, #startTimeRule#, #stopTimeRule#,
        #ruleStatus#, #sortIndex#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #multiFlag#, #segNo#, #unitCode#,
        #ihdStartRule#, #ihdStopRule#, #remark#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1101 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1101
        SET
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCEDURE_CODE = #procedureCode#,   <!-- 工序代码 -->
        PROCEDURE_NAME = #procedureName#,   <!-- 工序名称 -->
        START_TIME_RULE = #startTimeRule#,   <!-- 开始时间规则 -->
        STOP_TIME_RULE = #stopTimeRule#,   <!-- 结束时间规则 -->
        RULE_STATUS = #ruleStatus#,   <!-- 规则状态 -->
        SORT_INDEX = #sortIndex#,   <!-- 排序 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        MULTI_FLAG = #multiFlag#,   <!-- 多次标记 -->
        IHD_START_RULE = #ihdStartRule#,   <!-- IHD开始时间规则 -->
        IHD_STOP_RULE = #ihdStopRule#,   <!-- IHD结束时间规则 -->
        REMARK = #remark#   <!-- 备注 -->
        WHERE
        UUID = #uuid#
    </update>


</sqlMap>
