/**
* Generate time : 2025-03-24 13:41:22
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
* TLIRL0506
* 
*/
public class LIRL0506 extends DaoEPBase {

                public static final String QUERY = "LIRL0506.query";
                public static final String COUNT = "LIRL0506.count";
                public static final String UPDATE = "LIRL0506.update";
                public static final String INSERT = "LIRL0506.insert";
                private String segNo = " ";		/* 业务单元代码*/
                private String segName = "";        /* 业务单元简称*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录创建人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String billPrintType = "";		/* 单据类型*/
                private String printerIpAddr = " ";		/* 打印机IP地址*/
                private String printerPort = " ";		/* 打印机端口号*/
                private String printerType = " ";		/* 打印机类型*/
                private String status = " ";		/* 状态*/
                private String uuid = " ";		/* uuid*/
                private String factoryBuilding  = " ";		/* 厂房*/
                private String factoryBuildingName  = " ";		/* 厂房名称*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billPrintType");
        eiColumn.setDescName("单据类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printerIpAddr");
        eiColumn.setDescName("打印机IP地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printerPort");
        eiColumn.setDescName("打印机端口号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printerType");
        eiColumn.setDescName("打印机类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("厂房");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0506() {
initMetaData();
}

        /**
        * get the segNo - 业务单元代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
         * get the segName - 业务单元简称
        *
        * @return the segName
        */
        public String getSegName() {
        return this.segName;
        }

        /**
         * set the segName - 业务单元简称
        */
        public void setSegName(String segName) {
        this.segName = segName;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录创建人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录创建人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }

        /**
        * get the billPrintType - 账单打印类型
        * @return the billPrintType
        */
        public String getBillPrintType() {
        return this.billPrintType;
        }

        /**
         * set the billPrintType - 账单打印类型
         */
        public void setBillPrintType(String billPrintType) {
             this.billPrintType = billPrintType;
        }

    /**
     * get the printerIpAddr - 打印机IP地址
     * @return the printerIpAddr
     */
    public String getPrinterIpAddr() {
        return this.printerIpAddr;
    }

    /**
     * set the printerIpAddr - 打印机IP地址
     */
    public void setPrinterIpAddr(String printerIpAddr) {
        this.printerIpAddr = printerIpAddr;
    }

    /**
     * get the printerPort - 打印机端口
     * @return the printerPort
     */

    public String getPrinterPort() {
        return this.printerPort;
    }

    /**
     * set the printerPort - 打印机端口
     */
    public void setPrinterPort(String printerPort) {
        this.printerPort = printerPort;
    }

    /**
     * get the printerType - 打印机类型
     * @return the printerType
     */
    public String getPrinterType() {
        return this.printerType;
    }

    /**
     * set the printerType - 打印机类型
     */
    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    /**
     * get the status - 状态
         * @return the status
         */
        public String getStatus() {return this.status;}

        /**
        * set the status - 状态
        */
        public void setStatus(String status) { this.status = status;    }

         /**
             * get the uuid - uuid
          * @return the uuid
            */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }

    public String getFactoryBuilding() {
        return factoryBuilding;
    }

    public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
    }

    public String getFactoryBuildingName() {
        return factoryBuildingName;
    }

    public void setFactoryBuildingName(String factoryBuildingName) {
        this.factoryBuildingName = factoryBuildingName;
    }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setBillPrintType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billPrintType")), billPrintType));
                setPrinterIpAddr(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printerIpAddr")), printerIpAddr));
                setPrinterPort(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printerPort")), printerPort));
                setPrinterType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printerType")), printerType));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
                setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("billPrintType",StringUtils.toString(billPrintType, eiMetadata.getMeta("billPrintType")))  ;
                map.put("printerIpAddr",StringUtils.toString(printerIpAddr, eiMetadata.getMeta("printerIpAddr")));
                map.put("printerPort",StringUtils.toString(printerPort, eiMetadata.getMeta("printerPort")));
                map.put("printerType",StringUtils.toString(printerType, eiMetadata.getMeta("printerType")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("factoryBuilding",StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
                map.put("factoryBuildingName",StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));

return map;

}
}