package com.baosight.imom.vg.dm.domain;

import com.baosight.imom.common.vg.domain.Tvgdm0802;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 附件信息表
 */
public class VGDM0802 extends Tvgdm0802 {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0802.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0802.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0802.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0802.update";

    /**
     * 统计附件数量
     *
     * @param dao           dao
     * @param relevanceId   关联id
     * @param relevanceType 附件类型
     * @param segNo         账套
     * @return 附件数
     */
    public static int countFiles(Dao dao, String relevanceId, String relevanceType, String segNo) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("relevanceId", relevanceId);
        queryMap.put("relevanceType", relevanceType);
        queryMap.put("segNo", segNo);
        List aa = dao.query(COUNT, queryMap);
        return (Integer) aa.get(0);
    }
}
