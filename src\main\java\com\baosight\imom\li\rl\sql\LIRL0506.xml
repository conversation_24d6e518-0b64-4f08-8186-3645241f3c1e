<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="LIRL0506">
	<sql id="conditionPage">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="billPrintType">
			bill_print_type = #billPrintType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printerIpAddr">
			printer_ip_addr = #printerIpAddr#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printerPort">
			printer_port = #printerPort#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printerType">
			printer_type = #printerType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<!--创建时间起-->
		<isNotEmpty prepend=" and " property="recCreateTimeStart">
			substr(REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
		</isNotEmpty>
		<!--创建时间止-->
		<isNotEmpty prepend=" and " property="recCreateTimeEnd">
			substr(REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
		</isNotEmpty>

		<isNotEmpty prepend=" AND " property="factoryBuilding">
			FACTORY_BUILDING = #factoryBuilding#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0506">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
		        (select SEG_NAME from ${platSchema}.TVZBM81 t where t.SEG_NO = b.SEG_NO and t.DEL_FLAG = 0) as  "segName",         <!-- 业务单元简称 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录创建人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
		        bill_print_type as "billPrintType", <!-- 单据类型 -->
		        printer_ip_addr as "printerIpAddr", <!-- 打印机IP地址 -->
		        printer_port as "printerPort", <!-- 打印机端口 -->
		        printer_type as "printerType", <!-- 打印机类型 -->
		        STATUS	as "status",  <!-- 状态 -->
				UUID	as "uuid", <!-- uuid -->
		FACTORY_BUILDING as "factoryBuilding",
		FACTORY_BUILDING_NAME as "factoryBuildingName"
		FROM meli.tLIRL0506 b WHERE 1=1
		<include refid="conditionPage"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
		<isEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = 0
		</isEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tLIRL0506 WHERE 1=1
		<include refid="conditionPage"/>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="notUuid">
			UUID != #notUuid#
		</isNotEmpty>
	</select>
	


	<insert id="insert">
		INSERT INTO meli.tLIRL0506 (SEG_NO,  <!-- 业务单元代码 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录创建人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
		                                bill_print_type, <!-- 单据类型 -->
		                                printer_ip_addr, <!-- 打印机IP地址 -->
		                                printer_port, <!-- 打印机端口 -->
		                                printer_type,  <!-- 打印机类型 -->
		                                STATUS,  <!-- 状态 -->
										UUID,  <!-- uuid -->
		FACTORY_BUILDING,
		FACTORY_BUILDING_NAME
										)		 
	    VALUES (#segNo#, #unitCode#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
		#recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #billPrintType#,#printerIpAddr#,#printerPort#,#printerType#,
		#status#, #uuid#,#factoryBuilding#,#factoryBuildingName#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tLIRL0506 WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tLIRL0506
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录创建人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->
					bill_print_type = #billPrintType#, <!-- 单据类型 -->
					printer_ip_addr = #printerIpAddr#, <!-- 打印机IP地址 -->
					printer_port = #printerPort#, <!-- 打印机端口 -->
			  		printer_type = #printerType#, <!-- 打印机类型 -->
		            STATUS	= #status#,  <!-- 状态 -->
		FACTORY_BUILDING = #factoryBuilding#,
		FACTORY_BUILDING_NAME= #factoryBuildingName#
						WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>