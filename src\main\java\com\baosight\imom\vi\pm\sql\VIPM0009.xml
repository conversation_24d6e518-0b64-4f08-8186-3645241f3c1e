<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-02 10:18:08
   		Version :  1.0
		table comment : 生产需求单工序表
		tableName :${meviSchema}.TVIPM0009 
		 UNIT_CODE  VARCHAR, 
		 PROCESS_ORDER_ID  VARCHAR, 
		 PROCESS_DEMAND_ID  VARCHAR   NOT NULL   primarykey, 
		 PROCESS_DEMAND_SUB_ID  VARCHAR   NOT NULL   primarykey, 
		 PROCESS_CATEGORY  VARCHAR, 
		 AFTER_P_ORDER_ID  VARCHAR, 
		 BUSINESS_TYPE  VARCHAR, 
		 MACHINE_CODE  VARCHAR, 
		 KNIFE_ID  VARCHAR, 
		 SCHEDULE_START_DATE  VARCHAR, 
		 SCHEDULE_END_DATE  VARCHAR, 
		 PROCESS_DEMAND_PROCESS_STATUS  VARCHAR   NOT NULL   primarykey, 
		 PROCESS_FEE_PRICE_TYPE  VARCHAR, 
		 MPROVIDER_ID  VARCHAR, 
		 MPROVIDER_NAME  VARCHAR, 
		 AGREEMENT_ID  VARCHAR, 
		 AGREEMENT_SUBID  VARCHAR, 
		 TAX_RATE  DECIMAL, 
		 PROCESS_FEE_PRICE  DECIMAL, 
		 PROCESS_FEE_PRICE_TAX  DECIMAL, 
		 REMARK  VARCHAR, 
		 CAN_RETURN_MATERIAL  VARCHAR, 
		 SCHEDULE_SERIAL_NUM  VARCHAR, 
		 PRODUCTION_LOCK  VARCHAR, 
		 PROCESS_TIME  BIGINT   NOT NULL, 
		 PREPARE_TIME  BIGINT   NOT NULL, 
		 DELIVERY_TIME  BIGINT   NOT NULL, 
		 IF_FIRST  VARCHAR, 
		 IF_ROLL  VARCHAR, 
		 AUDITOR_ID  VARCHAR, 
		 AUDIT_TIME  VARCHAR, 
		 SCHEDULE_SERIAL_SUBNUM  DECIMAL, 
		 DELIVERY_DATE  VARCHAR, 
		 PROCESS_FIN_START_DATE  VARCHAR, 
		 PROCESS_FIN_END_DATE  VARCHAR, 
		 PRODUCT_REMAIN_QTY  VARCHAR, 
		 PRODUCT_REMAIN_WEIGHT  DECIMAL, 
		 SCHEDULE_LOCK_FLAG  VARCHAR, 
		 MATERIAL_RATE  DECIMAL, 
		 IF_MATERAIL  VARCHAR, 
		 IF_ARRANGE_ONLY  VARCHAR, 
		 PRE_PROCESS_DEMAND_SUB_ID  VARCHAR, 
		 PROCESS_IF_SETTLE_FINISH  VARCHAR, 
		 PROCESS_IF_SETTLE  VARCHAR, 
		 SUIT_VOUCHER_NUM  VARCHAR, 
		 DATA_SOURCE  VARCHAR, 
		 PRINT_BATCH_ID  VARCHAR, 
		 RAW_MATERIAL_TRANSFER_TIME  DECIMAL, 
		 COMBINE_NO  VARCHAR, 
		 SYN_FLAG  VARCHAR, 
		 ALLOC_TIME  BIGINT   NOT NULL, 
		 SCHEDULE_ISSUE_DATE  VARCHAR, 
		 SCHEDULE_ISSUE_PERSON  VARCHAR, 
		 OUTPROCESS_REASONS  VARCHAR, 
		 DELIVERY_DATE_REMARK  VARCHAR, 
		 SPECIAL_REMARK  VARCHAR, 
		 IF_CHECK_ASSAY  VARCHAR, 
		 YIELD  DECIMAL, 
		 THEORETICAL_IMPULSE_TIMES  DECIMAL, 
		 CRAFT_CODE  VARCHAR, 
		 MOULD_ID  VARCHAR, 
		 ACTUAL_IMPULSE_TIMES  BIGINT, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 TENANT_USER  VARCHAR, 
		 PROCESS_FEE_CALCULATE_METHOD  VARCHAR, 
		 SEG_NO  VARCHAR   NOT NULL   primarykey, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 AGREEMENT_MANAGE_TYPE  VARCHAR, 
		 IF_PLAN_FLAG  VARCHAR, 
		 CREATE_PERSON  VARCHAR, 
		 PROCESS_SPEED  DECIMAL, 
		 AUDITOR_NAME  VARCHAR, 
		 PREVIOUS_PROCESS_ORDER_ID  VARCHAR, 
		 NEXT_PROCESS_ORDER_ID  VARCHAR, 
		 IF_NEED_SUIT_CUT  VARCHAR, 
		 TRADE_CODE  VARCHAR, 
		 PROCESS_RATE  DECIMAL, 
		 PROCESS_ID  VARCHAR, 
		 AUDIT_OPNION  VARCHAR, 
		 BUSINESS_FROM  VARCHAR, 
		 TEAM_ID  VARCHAR, 
		 WORKING_SHIFT  VARCHAR, 
		 SPH  BIGINT, 
		 SGM_FLAG  VARCHAR, 
		 AUDIT_FLAG  VARCHAR, 
		 APPR_STATUS  VARCHAR, 
		 OUT_APPR_STATUS  VARCHAR, 
		 OUT_AUDIT_FLAG  VARCHAR, 
		 OUT_AUDIT_OPNION  VARCHAR, 
		 OUT_AUDIT_TIME  VARCHAR, 
		 OUT_AUDITOR_ID  VARCHAR, 
		 OUT_AUDITOR_NAME  VARCHAR, 
		 OUT_PROCESS_ID  VARCHAR, 
		 IF_SUBSTIT_AUDIT  VARCHAR, 
		 IF_YIELD_AUDIT  VARCHAR, 
		 IF_OUTPROCESS_AUDIT  VARCHAR, 
		 PROCESS_SYN_TRIGGER_TYPE  VARCHAR, 
		 IF_SYN  VARCHAR, 
		 PROCESS_SYN_RECEIVE  VARCHAR, 
		 PROCESS_SEQ_ID  DECIMAL, 
		 IF_NO_PROCESSING_AGREEMENT  VARCHAR, 
		 ORDER_CONFIRM_TIME  VARCHAR, 
		 PLAN_RATE  DECIMAL, 
		 SYN_SETTLE_FLAG  VARCHAR, 
		 KNIFE_PRINT_BATCH_ID  VARCHAR
	-->
<sqlMap namespace="VIPM0009">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.vi.pm.domain.VIPM0009">
		SELECT
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
				"segName", <!-- 业务单元简称 -->
				PROCESS_ORDER_ID	as "processOrderId",  <!-- 生产工单号 -->
				PROCESS_DEMAND_ID	as "processDemandId",  <!-- 生产需求单号 -->
				PROCESS_DEMAND_SUB_ID	as "processDemandSubId",  <!-- 生产需求单子项号 -->
				PROCESS_CATEGORY	as "processCategory",  <!-- 工序大类代码 -->
				AFTER_P_ORDER_ID	as "afterPOrderId",  <!-- 后道工单号 -->
				BUSINESS_TYPE	as "businessType",  <!-- 业务类型 -->
				MACHINE_CODE	as "machineCode",  <!-- 机组代码 -->
				KNIFE_ID	as "knifeId",  <!-- 排刀号 -->
				SCHEDULE_START_DATE	as "scheduleStartDate",  <!-- 排产开始时间 -->
				SCHEDULE_END_DATE	as "scheduleEndDate",  <!-- 排产结束时间 -->
				PROCESS_DEMAND_PROCESS_STATUS	as "processDemandProcessStatus",  <!-- 生产需求单工序表状态 -->
				PROCESS_FEE_PRICE_TYPE	as "processFeePriceType",  <!-- 加工费计价类型 -->
				MPROVIDER_ID	as "mproviderId",  <!-- 加工单位代码 -->
				MPROVIDER_NAME	as "mproviderName",  <!-- 加工单位名称 -->
				AGREEMENT_ID	as "agreementId",  <!-- 加工协议号 -->
				AGREEMENT_SUBID	as "agreementSubid",  <!-- 加工协议子项号 -->
				TAX_RATE	as "taxRate",  <!-- 税率 -->
				PROCESS_FEE_PRICE	as "processFeePrice",  <!-- 加工费单价(不含税) -->
				PROCESS_FEE_PRICE_TAX	as "processFeePriceTax",  <!-- 加工费单价(含税) -->
				REMARK	as "remark",  <!-- 备注 -->
				CAN_RETURN_MATERIAL	as "canReturnMaterial",  <!-- 是否可以退料 -->
				SCHEDULE_SERIAL_NUM	as "scheduleSerialNum",  <!-- 排产序号 -->
				PRODUCTION_LOCK	as "productionLock",  <!-- 封锁产出品标记 -->
				PROCESS_TIME	as "processTime",  <!-- 生产时间(分钟) -->
				PREPARE_TIME	as "prepareTime",  <!-- 准备时间(分钟) -->
				DELIVERY_TIME	as "deliveryTime",  <!-- 交接时间(分钟) -->
				IF_FIRST	as "ifFirst",  <!-- 是否首道 -->
				IF_ROLL	as "ifRoll",  <!-- 原料形状 -->
				AUDITOR_ID	as "auditorId",  <!-- 审核人工号 -->
				AUDIT_TIME	as "auditTime",  <!-- 审核时间 -->
				SCHEDULE_SERIAL_SUBNUM	as "scheduleSerialSubnum",  <!-- 排产子序号 -->
				DELIVERY_DATE	as "deliveryDate",  <!-- 加工完成时间 -->
				PROCESS_FIN_START_DATE	as "processFinStartDate",  <!-- 生产实际开始时间 -->
				PROCESS_FIN_END_DATE	as "processFinEndDate",  <!-- 生产实际结束时间 -->
				PRODUCT_REMAIN_QTY	as "productRemainQty",  <!-- 加工剩余数量 -->
				PRODUCT_REMAIN_WEIGHT	as "productRemainWeight",  <!-- 加工剩余重量 -->
				SCHEDULE_LOCK_FLAG	as "scheduleLockFlag",  <!-- 排产锁定标记 -->
				MATERIAL_RATE	as "materialRate",  <!-- 原料成材率 -->
				IF_MATERAIL	as "ifMaterail",  <!-- 是否强制指定原料卷 -->
				IF_ARRANGE_ONLY	as "ifArrangeOnly",  <!-- 是否单排刀 -->
				PRE_PROCESS_DEMAND_SUB_ID	as "preProcessDemandSubId",  <!-- 上一道生产需求单子项号 -->
				PROCESS_IF_SETTLE_FINISH	as "processIfSettleFinish",  <!-- 生产加工是否结算完成 -->
				PROCESS_IF_SETTLE	as "processIfSettle",  <!-- 生产加工是否结算加工费 -->
				SUIT_VOUCHER_NUM	as "suitVoucherNum",  <!-- 套裁封锁单据号 -->
				DATA_SOURCE	as "dataSource",  <!-- 数据来源 -->
				PRINT_BATCH_ID	as "printBatchId",  <!-- 打印批次号 -->
				RAW_MATERIAL_TRANSFER_TIME	as "rawMaterialTransferTime",  <!-- 原料转运时间 -->
				COMBINE_NO	as "combineNo",  <!-- 归并号 -->
				SYN_FLAG	as "synFlag",  <!-- 协同标记 -->
				ALLOC_TIME	as "allocTime",  <!-- 分配时间（分钟） -->
				SCHEDULE_ISSUE_DATE	as "scheduleIssueDate",  <!-- 排产下发时间 -->
				SCHEDULE_ISSUE_PERSON	as "scheduleIssuePerson",  <!-- 排产下发人 -->
				OUTPROCESS_REASONS	as "outprocessReasons",  <!-- 委外原因 -->
				DELIVERY_DATE_REMARK	as "deliveryDateRemark",  <!-- 交期备注 -->
				SPECIAL_REMARK	as "specialRemark",  <!-- 特殊备注 -->
				IF_CHECK_ASSAY	as "ifCheckAssay",  <!-- 是否检化验 -->
				YIELD	as "yield",  <!-- 成材率 -->
				THEORETICAL_IMPULSE_TIMES	as "theoreticalImpulseTimes",  <!-- 理论冲次数 -->
				CRAFT_CODE	as "craftCode",  <!-- 工艺单号 -->
				MOULD_ID	as "mouldId",  <!-- 模具ID -->
				ACTUAL_IMPULSE_TIMES	as "actualImpulseTimes",  <!-- 实际冲次数 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				PROCESS_FEE_CALCULATE_METHOD	as "processFeeCalculateMethod",  
				SEG_NO	as "segNo",  <!-- 业务账套 -->
				UUID	as "uuid",  <!-- ID -->
				AGREEMENT_MANAGE_TYPE	as "agreementManageType",  <!-- 委外协议管理方式 -->
				IF_PLAN_FLAG	as "ifPlanFlag",  <!-- 是否参与排产 -->
				CREATE_PERSON	as "createPerson",  <!-- 制单人代码 -->
				PROCESS_SPEED	as "processSpeed",  <!-- 加工速度(米/分钟) -->
				AUDITOR_NAME	as "auditorName",  <!-- 审核人姓名 -->
				PREVIOUS_PROCESS_ORDER_ID	as "previousProcessOrderId",  <!-- 上道工序/工单 -->
				NEXT_PROCESS_ORDER_ID	as "nextProcessOrderId",  <!-- 下道工序/工单 -->
				IF_NEED_SUIT_CUT	as "ifNeedSuitCut",  <!-- 是否需要套裁方案 -->
				TRADE_CODE	as "tradeCode",  <!-- 贸易方式 -->
				PROCESS_RATE	as "processRate",  <!-- 合格品率 -->
				PROCESS_ID	as "processId",  <!-- 流程ID -->
				AUDIT_OPNION	as "auditOpnion",  <!-- 审核意见 -->
				BUSINESS_FROM	as "businessFrom",  <!-- 业务来源 -->
				TEAM_ID	as "teamId",  <!-- 班组 -->
				WORKING_SHIFT	as "workingShift",  <!-- 班次 -->
				SPH	as "sph",  <!-- sph每小时加工片数(落料和精剪） -->
				SGM_FLAG	as "sgmFlag",  <!-- SGM标记 -->
				AUDIT_FLAG	as "auditFlag",  <!-- 审核标记 -->
				APPR_STATUS	as "apprStatus",  <!-- 审批状态 -->
				OUT_APPR_STATUS	as "outApprStatus",  <!-- 委外审批状态 -->
				OUT_AUDIT_FLAG	as "outAuditFlag",  <!-- 委外审核标记 -->
				OUT_AUDIT_OPNION	as "outAuditOpnion",  <!-- 委外审核意见 -->
				OUT_AUDIT_TIME	as "outAuditTime",  <!-- 委外审核时间 -->
				OUT_AUDITOR_ID	as "outAuditorId",  <!-- 委外审核人工号 -->
				OUT_AUDITOR_NAME	as "outAuditorName",  <!-- 委外审核人姓名 -->
				OUT_PROCESS_ID	as "outProcessId",  <!-- 委外流程ID -->
				IF_SUBSTIT_AUDIT	as "ifSubstitAudit",  <!-- 是否需要替代审批 -->
				IF_YIELD_AUDIT	as "ifYieldAudit",  <!-- 是否需要成材率审批 -->
				IF_OUTPROCESS_AUDIT	as "ifOutprocessAudit",  <!-- 是否需要委外工单审批 -->
				PROCESS_SYN_TRIGGER_TYPE	as "processSynTriggerType",  <!-- 协同触发方式 -->
				IF_SYN	as "ifSyn",  <!-- 是否协同 -->
				PROCESS_SYN_RECEIVE	as "processSynReceive",  <!-- 协同接收方 -->
				PROCESS_SEQ_ID	as "processSeqId",  <!-- 加工顺序 -->
				IF_NO_PROCESSING_AGREEMENT	as "ifNoProcessingAgreement",  <!-- 无加工协议（委外） -->
				ORDER_CONFIRM_TIME	as "orderConfirmTime",  <!-- 生产工单确认时间 -->
				PLAN_RATE	as "planRate",  <!-- 工艺成材率 -->
				SYN_SETTLE_FLAG	as "synSettleFlag",  <!-- 委托方结算标记 -->
				KNIFE_PRINT_BATCH_ID	as "knifePrintBatchId" <!-- 机组组刀打印批次号 -->
		FROM ${meviSchema}.TVIPM0009 t WHERE 1=1
		<isNotEmpty prepend=" AND " property="processDemandId">
			PROCESS_DEMAND_ID = #processDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandSubId">
			PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandProcessStatus">
			PROCESS_DEMAND_PROCESS_STATUS = #processDemandProcessStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderIdLike">
			PROCESS_ORDER_ID LIKE '%$processOrderIdLike$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="machineCode">
			MACHINE_CODE = #machineCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentTime">
			SCHEDULE_START_DATE &gt;= #currentTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="thirtySixHoursFromNow">
			SCHEDULE_START_DATE &lt;= #thirtySixHoursFromNow#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="machineCodes">
			MACHINE_CODE in ($machineCodes$)
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			SCHEDULE_END_DATE asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meviSchema}.TVIPM0009 WHERE 1=1
		<isNotEmpty prepend=" AND " property="processDemandId">
			PROCESS_DEMAND_ID = #processDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandSubId">
			PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandProcessStatus">
			PROCESS_DEMAND_PROCESS_STATUS = #processDemandProcessStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processOrderId">
			PROCESS_ORDER_ID = #processOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandId">
			PROCESS_DEMAND_ID = #processDemandId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandSubId">
			PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processCategory">
			PROCESS_CATEGORY = #processCategory#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="afterPOrderId">
			AFTER_P_ORDER_ID = #afterPOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessType">
			BUSINESS_TYPE = #businessType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="machineCode">
			MACHINE_CODE = #machineCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="knifeId">
			KNIFE_ID = #knifeId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleStartDate">
			SCHEDULE_START_DATE = #scheduleStartDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleEndDate">
			SCHEDULE_END_DATE = #scheduleEndDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processDemandProcessStatus">
			PROCESS_DEMAND_PROCESS_STATUS = #processDemandProcessStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeePriceType">
			PROCESS_FEE_PRICE_TYPE = #processFeePriceType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mproviderId">
			MPROVIDER_ID = #mproviderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mproviderName">
			MPROVIDER_NAME = #mproviderName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="agreementId">
			AGREEMENT_ID = #agreementId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="agreementSubid">
			AGREEMENT_SUBID = #agreementSubid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="taxRate">
			TAX_RATE = #taxRate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeePrice">
			PROCESS_FEE_PRICE = #processFeePrice#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeePriceTax">
			PROCESS_FEE_PRICE_TAX = #processFeePriceTax#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="canReturnMaterial">
			CAN_RETURN_MATERIAL = #canReturnMaterial#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleSerialNum">
			SCHEDULE_SERIAL_NUM = #scheduleSerialNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productionLock">
			PRODUCTION_LOCK = #productionLock#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processTime">
			PROCESS_TIME = #processTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="prepareTime">
			PREPARE_TIME = #prepareTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deliveryTime">
			DELIVERY_TIME = #deliveryTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifFirst">
			IF_FIRST = #ifFirst#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifRoll">
			IF_ROLL = #ifRoll#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditorId">
			AUDITOR_ID = #auditorId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditTime">
			AUDIT_TIME = #auditTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleSerialSubnum">
			SCHEDULE_SERIAL_SUBNUM = #scheduleSerialSubnum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deliveryDate">
			DELIVERY_DATE = #deliveryDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFinStartDate">
			PROCESS_FIN_START_DATE = #processFinStartDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFinEndDate">
			PROCESS_FIN_END_DATE = #processFinEndDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productRemainQty">
			PRODUCT_REMAIN_QTY = #productRemainQty#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="productRemainWeight">
			PRODUCT_REMAIN_WEIGHT = #productRemainWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleLockFlag">
			SCHEDULE_LOCK_FLAG = #scheduleLockFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="materialRate">
			MATERIAL_RATE = #materialRate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifMaterail">
			IF_MATERAIL = #ifMaterail#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifArrangeOnly">
			IF_ARRANGE_ONLY = #ifArrangeOnly#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="preProcessDemandSubId">
			PRE_PROCESS_DEMAND_SUB_ID = #preProcessDemandSubId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processIfSettleFinish">
			PROCESS_IF_SETTLE_FINISH = #processIfSettleFinish#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processIfSettle">
			PROCESS_IF_SETTLE = #processIfSettle#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="suitVoucherNum">
			SUIT_VOUCHER_NUM = #suitVoucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dataSource">
			DATA_SOURCE = #dataSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="printBatchId">
			PRINT_BATCH_ID = #printBatchId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="rawMaterialTransferTime">
			RAW_MATERIAL_TRANSFER_TIME = #rawMaterialTransferTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="combineNo">
			COMBINE_NO = #combineNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="synFlag">
			SYN_FLAG = #synFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocTime">
			ALLOC_TIME = #allocTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleIssueDate">
			SCHEDULE_ISSUE_DATE = #scheduleIssueDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scheduleIssuePerson">
			SCHEDULE_ISSUE_PERSON = #scheduleIssuePerson#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outprocessReasons">
			OUTPROCESS_REASONS = #outprocessReasons#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deliveryDateRemark">
			DELIVERY_DATE_REMARK = #deliveryDateRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="specialRemark">
			SPECIAL_REMARK = #specialRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifCheckAssay">
			IF_CHECK_ASSAY = #ifCheckAssay#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="yield">
			YIELD = #yield#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="theoreticalImpulseTimes">
			THEORETICAL_IMPULSE_TIMES = #theoreticalImpulseTimes#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craftCode">
			CRAFT_CODE = #craftCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldId">
			MOULD_ID = #mouldId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="actualImpulseTimes">
			ACTUAL_IMPULSE_TIMES = #actualImpulseTimes#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processFeeCalculateMethod">
			PROCESS_FEE_CALCULATE_METHOD = #processFeeCalculateMethod#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="agreementManageType">
			AGREEMENT_MANAGE_TYPE = #agreementManageType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifPlanFlag">
			IF_PLAN_FLAG = #ifPlanFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="createPerson">
			CREATE_PERSON = #createPerson#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSpeed">
			PROCESS_SPEED = #processSpeed#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditorName">
			AUDITOR_NAME = #auditorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="previousProcessOrderId">
			PREVIOUS_PROCESS_ORDER_ID = #previousProcessOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="nextProcessOrderId">
			NEXT_PROCESS_ORDER_ID = #nextProcessOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifNeedSuitCut">
			IF_NEED_SUIT_CUT = #ifNeedSuitCut#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tradeCode">
			TRADE_CODE = #tradeCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processRate">
			PROCESS_RATE = #processRate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processId">
			PROCESS_ID = #processId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditOpnion">
			AUDIT_OPNION = #auditOpnion#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessFrom">
			BUSINESS_FROM = #businessFrom#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="teamId">
			TEAM_ID = #teamId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="workingShift">
			WORKING_SHIFT = #workingShift#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sph">
			SPH = #sph#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sgmFlag">
			SGM_FLAG = #sgmFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auditFlag">
			AUDIT_FLAG = #auditFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="apprStatus">
			APPR_STATUS = #apprStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outApprStatus">
			OUT_APPR_STATUS = #outApprStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outAuditFlag">
			OUT_AUDIT_FLAG = #outAuditFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outAuditOpnion">
			OUT_AUDIT_OPNION = #outAuditOpnion#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outAuditTime">
			OUT_AUDIT_TIME = #outAuditTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outAuditorId">
			OUT_AUDITOR_ID = #outAuditorId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outAuditorName">
			OUT_AUDITOR_NAME = #outAuditorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="outProcessId">
			OUT_PROCESS_ID = #outProcessId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifSubstitAudit">
			IF_SUBSTIT_AUDIT = #ifSubstitAudit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifYieldAudit">
			IF_YIELD_AUDIT = #ifYieldAudit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifOutprocessAudit">
			IF_OUTPROCESS_AUDIT = #ifOutprocessAudit#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSynTriggerType">
			PROCESS_SYN_TRIGGER_TYPE = #processSynTriggerType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifSyn">
			IF_SYN = #ifSyn#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSynReceive">
			PROCESS_SYN_RECEIVE = #processSynReceive#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processSeqId">
			PROCESS_SEQ_ID = #processSeqId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ifNoProcessingAgreement">
			IF_NO_PROCESSING_AGREEMENT = #ifNoProcessingAgreement#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orderConfirmTime">
			ORDER_CONFIRM_TIME = #orderConfirmTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="planRate">
			PLAN_RATE = #planRate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="synSettleFlag">
			SYN_SETTLE_FLAG = #synSettleFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="knifePrintBatchId">
			KNIFE_PRINT_BATCH_ID = #knifePrintBatchId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meviSchema}.TVIPM0009 (UNIT_CODE,  <!-- 业务单元代码 -->
										PROCESS_ORDER_ID,  <!-- 生产工单号 -->
										PROCESS_DEMAND_ID,  <!-- 生产需求单号 -->
										PROCESS_DEMAND_SUB_ID,  <!-- 生产需求单子项号 -->
										PROCESS_CATEGORY,  <!-- 工序大类代码 -->
										AFTER_P_ORDER_ID,  <!-- 后道工单号 -->
										BUSINESS_TYPE,  <!-- 业务类型 -->
										MACHINE_CODE,  <!-- 机组代码 -->
										KNIFE_ID,  <!-- 排刀号 -->
										SCHEDULE_START_DATE,  <!-- 排产开始时间 -->
										SCHEDULE_END_DATE,  <!-- 排产结束时间 -->
										PROCESS_DEMAND_PROCESS_STATUS,  <!-- 生产需求单工序表状态 -->
										PROCESS_FEE_PRICE_TYPE,  <!-- 加工费计价类型 -->
										MPROVIDER_ID,  <!-- 加工单位代码 -->
										MPROVIDER_NAME,  <!-- 加工单位名称 -->
										AGREEMENT_ID,  <!-- 加工协议号 -->
										AGREEMENT_SUBID,  <!-- 加工协议子项号 -->
										TAX_RATE,  <!-- 税率 -->
										PROCESS_FEE_PRICE,  <!-- 加工费单价(不含税) -->
										PROCESS_FEE_PRICE_TAX,  <!-- 加工费单价(含税) -->
										REMARK,  <!-- 备注 -->
										CAN_RETURN_MATERIAL,  <!-- 是否可以退料 -->
										SCHEDULE_SERIAL_NUM,  <!-- 排产序号 -->
										PRODUCTION_LOCK,  <!-- 封锁产出品标记 -->
										PROCESS_TIME,  <!-- 生产时间(分钟) -->
										PREPARE_TIME,  <!-- 准备时间(分钟) -->
										DELIVERY_TIME,  <!-- 交接时间(分钟) -->
										IF_FIRST,  <!-- 是否首道 -->
										IF_ROLL,  <!-- 原料形状 -->
										AUDITOR_ID,  <!-- 审核人工号 -->
										AUDIT_TIME,  <!-- 审核时间 -->
										SCHEDULE_SERIAL_SUBNUM,  <!-- 排产子序号 -->
										DELIVERY_DATE,  <!-- 加工完成时间 -->
										PROCESS_FIN_START_DATE,  <!-- 生产实际开始时间 -->
										PROCESS_FIN_END_DATE,  <!-- 生产实际结束时间 -->
										PRODUCT_REMAIN_QTY,  <!-- 加工剩余数量 -->
										PRODUCT_REMAIN_WEIGHT,  <!-- 加工剩余重量 -->
										SCHEDULE_LOCK_FLAG,  <!-- 排产锁定标记 -->
										MATERIAL_RATE,  <!-- 原料成材率 -->
										IF_MATERAIL,  <!-- 是否强制指定原料卷 -->
										IF_ARRANGE_ONLY,  <!-- 是否单排刀 -->
										PRE_PROCESS_DEMAND_SUB_ID,  <!-- 上一道生产需求单子项号 -->
										PROCESS_IF_SETTLE_FINISH,  <!-- 生产加工是否结算完成 -->
										PROCESS_IF_SETTLE,  <!-- 生产加工是否结算加工费 -->
										SUIT_VOUCHER_NUM,  <!-- 套裁封锁单据号 -->
										DATA_SOURCE,  <!-- 数据来源 -->
										PRINT_BATCH_ID,  <!-- 打印批次号 -->
										RAW_MATERIAL_TRANSFER_TIME,  <!-- 原料转运时间 -->
										COMBINE_NO,  <!-- 归并号 -->
										SYN_FLAG,  <!-- 协同标记 -->
										ALLOC_TIME,  <!-- 分配时间（分钟） -->
										SCHEDULE_ISSUE_DATE,  <!-- 排产下发时间 -->
										SCHEDULE_ISSUE_PERSON,  <!-- 排产下发人 -->
										OUTPROCESS_REASONS,  <!-- 委外原因 -->
										DELIVERY_DATE_REMARK,  <!-- 交期备注 -->
										SPECIAL_REMARK,  <!-- 特殊备注 -->
										IF_CHECK_ASSAY,  <!-- 是否检化验 -->
										YIELD,  <!-- 成材率 -->
										THEORETICAL_IMPULSE_TIMES,  <!-- 理论冲次数 -->
										CRAFT_CODE,  <!-- 工艺单号 -->
										MOULD_ID,  <!-- 模具ID -->
										ACTUAL_IMPULSE_TIMES,  <!-- 实际冲次数 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										TENANT_USER,  <!-- 租户 -->
										PROCESS_FEE_CALCULATE_METHOD,
										SEG_NO,  <!-- 业务账套 -->
										UUID,  <!-- ID -->
										AGREEMENT_MANAGE_TYPE,  <!-- 委外协议管理方式 -->
										IF_PLAN_FLAG,  <!-- 是否参与排产 -->
										CREATE_PERSON,  <!-- 制单人代码 -->
										PROCESS_SPEED,  <!-- 加工速度(米/分钟) -->
										AUDITOR_NAME,  <!-- 审核人姓名 -->
										PREVIOUS_PROCESS_ORDER_ID,  <!-- 上道工序/工单 -->
										NEXT_PROCESS_ORDER_ID,  <!-- 下道工序/工单 -->
										IF_NEED_SUIT_CUT,  <!-- 是否需要套裁方案 -->
										TRADE_CODE,  <!-- 贸易方式 -->
										PROCESS_RATE,  <!-- 合格品率 -->
										PROCESS_ID,  <!-- 流程ID -->
										AUDIT_OPNION,  <!-- 审核意见 -->
										BUSINESS_FROM,  <!-- 业务来源 -->
										TEAM_ID,  <!-- 班组 -->
										WORKING_SHIFT,  <!-- 班次 -->
										SPH,  <!-- sph每小时加工片数(落料和精剪） -->
										SGM_FLAG,  <!-- SGM标记 -->
										AUDIT_FLAG,  <!-- 审核标记 -->
										APPR_STATUS,  <!-- 审批状态 -->
										OUT_APPR_STATUS,  <!-- 委外审批状态 -->
										OUT_AUDIT_FLAG,  <!-- 委外审核标记 -->
										OUT_AUDIT_OPNION,  <!-- 委外审核意见 -->
										OUT_AUDIT_TIME,  <!-- 委外审核时间 -->
										OUT_AUDITOR_ID,  <!-- 委外审核人工号 -->
										OUT_AUDITOR_NAME,  <!-- 委外审核人姓名 -->
										OUT_PROCESS_ID,  <!-- 委外流程ID -->
										IF_SUBSTIT_AUDIT,  <!-- 是否需要替代审批 -->
										IF_YIELD_AUDIT,  <!-- 是否需要成材率审批 -->
										IF_OUTPROCESS_AUDIT,  <!-- 是否需要委外工单审批 -->
										PROCESS_SYN_TRIGGER_TYPE,  <!-- 协同触发方式 -->
										IF_SYN,  <!-- 是否协同 -->
										PROCESS_SYN_RECEIVE,  <!-- 协同接收方 -->
										PROCESS_SEQ_ID,  <!-- 加工顺序 -->
										IF_NO_PROCESSING_AGREEMENT,  <!-- 无加工协议（委外） -->
										ORDER_CONFIRM_TIME,  <!-- 生产工单确认时间 -->
										PLAN_RATE,  <!-- 工艺成材率 -->
										SYN_SETTLE_FLAG,  <!-- 委托方结算标记 -->
										KNIFE_PRINT_BATCH_ID  <!-- 机组组刀打印批次号 -->
										)		 
	    VALUES (#unitCode:VARCHAR#, #processOrderId:VARCHAR#, #processDemandId:VARCHAR#, #processDemandSubId:VARCHAR#, #processCategory:VARCHAR#, #afterPOrderId:VARCHAR#, #businessType:VARCHAR#, #machineCode:VARCHAR#, #knifeId:VARCHAR#, #scheduleStartDate:VARCHAR#, #scheduleEndDate:VARCHAR#, #processDemandProcessStatus:VARCHAR#, #processFeePriceType:VARCHAR#, #mproviderId:VARCHAR#, #mproviderName:VARCHAR#, #agreementId:VARCHAR#, #agreementSubid:VARCHAR#, #taxRate:NUMERIC#, #processFeePrice:NUMERIC#, #processFeePriceTax:NUMERIC#, #remark:VARCHAR#, #canReturnMaterial:VARCHAR#, #scheduleSerialNum:VARCHAR#, #productionLock:VARCHAR#, #processTime#, #prepareTime#, #deliveryTime#, #ifFirst:VARCHAR#, #ifRoll:VARCHAR#, #auditorId:VARCHAR#, #auditTime:VARCHAR#, #scheduleSerialSubnum:NUMERIC#, #deliveryDate:VARCHAR#, #processFinStartDate:VARCHAR#, #processFinEndDate:VARCHAR#, #productRemainQty:VARCHAR#, #productRemainWeight:NUMERIC#, #scheduleLockFlag:VARCHAR#, #materialRate:NUMERIC#, #ifMaterail:VARCHAR#, #ifArrangeOnly:VARCHAR#, #preProcessDemandSubId:VARCHAR#, #processIfSettleFinish:VARCHAR#, #processIfSettle:VARCHAR#, #suitVoucherNum:VARCHAR#, #dataSource:VARCHAR#, #printBatchId:VARCHAR#, #rawMaterialTransferTime:NUMERIC#, #combineNo:VARCHAR#, #synFlag:VARCHAR#, #allocTime#, #scheduleIssueDate:VARCHAR#, #scheduleIssuePerson:VARCHAR#, #outprocessReasons:VARCHAR#, #deliveryDateRemark:VARCHAR#, #specialRemark:VARCHAR#, #ifCheckAssay:VARCHAR#, #yield:NUMERIC#, #theoreticalImpulseTimes:NUMERIC#, #craftCode:VARCHAR#, #mouldId:VARCHAR#, #actualImpulseTimes#, #recCreator:VARCHAR#, #recCreatorName:VARCHAR#, #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recRevisorName:VARCHAR#, #recReviseTime:VARCHAR#, #archiveFlag:VARCHAR#, #delFlag#, #tenantUser:VARCHAR#, #processFeeCalculateMethod:VARCHAR#, #segNo:VARCHAR#, #uuid:VARCHAR#, #agreementManageType:VARCHAR#, #ifPlanFlag:VARCHAR#, #createPerson:VARCHAR#, #processSpeed:NUMERIC#, #auditorName:VARCHAR#, #previousProcessOrderId:VARCHAR#, #nextProcessOrderId:VARCHAR#, #ifNeedSuitCut:VARCHAR#, #tradeCode:VARCHAR#, #processRate:NUMERIC#, #processId:VARCHAR#, #auditOpnion:VARCHAR#, #businessFrom:VARCHAR#, #teamId:VARCHAR#, #workingShift:VARCHAR#, #sph#, #sgmFlag:VARCHAR#, #auditFlag:VARCHAR#, #apprStatus:VARCHAR#, #outApprStatus:VARCHAR#, #outAuditFlag:VARCHAR#, #outAuditOpnion:VARCHAR#, #outAuditTime:VARCHAR#, #outAuditorId:VARCHAR#, #outAuditorName:VARCHAR#, #outProcessId:VARCHAR#, #ifSubstitAudit:VARCHAR#, #ifYieldAudit:VARCHAR#, #ifOutprocessAudit:VARCHAR#, #processSynTriggerType:VARCHAR#, #ifSyn:VARCHAR#, #processSynReceive:VARCHAR#, #processSeqId:NUMERIC#, #ifNoProcessingAgreement:VARCHAR#, #orderConfirmTime:VARCHAR#, #planRate:NUMERIC#, #synSettleFlag:VARCHAR#, #knifePrintBatchId:VARCHAR#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meviSchema}.TVIPM0009 WHERE
		SEG_NO = #segNo#
		AND PROCESS_DEMAND_SUB_ID = #processDemandSubId#
		AND PROCESS_ORDER_ID = #processOrderId#
	</delete>

	<update id="update">
		UPDATE ${meviSchema}.TVIPM0009 
		SET 
		UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					PROCESS_ORDER_ID	= #processOrderId#,   <!-- 生产工单号 -->
											PROCESS_CATEGORY	= #processCategory#,   <!-- 工序大类代码 -->
					AFTER_P_ORDER_ID	= #afterPOrderId#,   <!-- 后道工单号 -->
					BUSINESS_TYPE	= #businessType#,   <!-- 业务类型 -->
					MACHINE_CODE	= #machineCode#,   <!-- 机组代码 -->
					KNIFE_ID	= #knifeId#,   <!-- 排刀号 -->
					SCHEDULE_START_DATE	= #scheduleStartDate#,   <!-- 排产开始时间 -->
					SCHEDULE_END_DATE	= #scheduleEndDate#,   <!-- 排产结束时间 -->
								PROCESS_FEE_PRICE_TYPE	= #processFeePriceType#,   <!-- 加工费计价类型 -->
					MPROVIDER_ID	= #mproviderId#,   <!-- 加工单位代码 -->
					MPROVIDER_NAME	= #mproviderName#,   <!-- 加工单位名称 -->
					AGREEMENT_ID	= #agreementId#,   <!-- 加工协议号 -->
					AGREEMENT_SUBID	= #agreementSubid#,   <!-- 加工协议子项号 -->
					TAX_RATE	= #taxRate:NUMERIC#,   <!-- 税率 -->
					PROCESS_FEE_PRICE	= #processFeePrice:NUMERIC#,   <!-- 加工费单价(不含税) -->
					PROCESS_FEE_PRICE_TAX	= #processFeePriceTax:NUMERIC#,   <!-- 加工费单价(含税) -->
					REMARK	= #remark#,   <!-- 备注 -->
					CAN_RETURN_MATERIAL	= #canReturnMaterial#,   <!-- 是否可以退料 -->
					SCHEDULE_SERIAL_NUM	= #scheduleSerialNum#,   <!-- 排产序号 -->
					PRODUCTION_LOCK	= #productionLock#,   <!-- 封锁产出品标记 -->
					PROCESS_TIME	= #processTime#,   <!-- 生产时间(分钟) -->
					PREPARE_TIME	= #prepareTime#,   <!-- 准备时间(分钟) -->
					DELIVERY_TIME	= #deliveryTime#,   <!-- 交接时间(分钟) -->
					IF_FIRST	= #ifFirst#,   <!-- 是否首道 -->
					IF_ROLL	= #ifRoll#,   <!-- 原料形状 -->
					AUDITOR_ID	= #auditorId#,   <!-- 审核人工号 -->
					AUDIT_TIME	= #auditTime#,   <!-- 审核时间 -->
					SCHEDULE_SERIAL_SUBNUM	= #scheduleSerialSubnum:NUMERIC#,   <!-- 排产子序号 -->
					DELIVERY_DATE	= #deliveryDate#,   <!-- 加工完成时间 -->
					PROCESS_FIN_START_DATE	= #processFinStartDate#,   <!-- 生产实际开始时间 -->
					PROCESS_FIN_END_DATE	= #processFinEndDate#,   <!-- 生产实际结束时间 -->
					PRODUCT_REMAIN_QTY	= #productRemainQty#,   <!-- 加工剩余数量 -->
					PRODUCT_REMAIN_WEIGHT	= #productRemainWeight:NUMERIC#,   <!-- 加工剩余重量 -->
					SCHEDULE_LOCK_FLAG	= #scheduleLockFlag#,   <!-- 排产锁定标记 -->
					MATERIAL_RATE	= #materialRate:NUMERIC#,   <!-- 原料成材率 -->
					IF_MATERAIL	= #ifMaterail#,   <!-- 是否强制指定原料卷 -->
					IF_ARRANGE_ONLY	= #ifArrangeOnly#,   <!-- 是否单排刀 -->
					PRE_PROCESS_DEMAND_SUB_ID	= #preProcessDemandSubId#,   <!-- 上一道生产需求单子项号 -->
					PROCESS_IF_SETTLE_FINISH	= #processIfSettleFinish#,   <!-- 生产加工是否结算完成 -->
					PROCESS_IF_SETTLE	= #processIfSettle#,   <!-- 生产加工是否结算加工费 -->
					SUIT_VOUCHER_NUM	= #suitVoucherNum#,   <!-- 套裁封锁单据号 -->
					DATA_SOURCE	= #dataSource#,   <!-- 数据来源 -->
					PRINT_BATCH_ID	= #printBatchId#,   <!-- 打印批次号 -->
					RAW_MATERIAL_TRANSFER_TIME	= #rawMaterialTransferTime:NUMERIC#,   <!-- 原料转运时间 -->
					COMBINE_NO	= #combineNo#,   <!-- 归并号 -->
					SYN_FLAG	= #synFlag#,   <!-- 协同标记 -->
					ALLOC_TIME	= #allocTime#,   <!-- 分配时间（分钟） -->
					SCHEDULE_ISSUE_DATE	= #scheduleIssueDate#,   <!-- 排产下发时间 -->
					SCHEDULE_ISSUE_PERSON	= #scheduleIssuePerson#,   <!-- 排产下发人 -->
					OUTPROCESS_REASONS	= #outprocessReasons#,   <!-- 委外原因 -->
					DELIVERY_DATE_REMARK	= #deliveryDateRemark#,   <!-- 交期备注 -->
					SPECIAL_REMARK	= #specialRemark#,   <!-- 特殊备注 -->
					IF_CHECK_ASSAY	= #ifCheckAssay#,   <!-- 是否检化验 -->
					YIELD	= #yield:NUMERIC#,   <!-- 成材率 -->
					THEORETICAL_IMPULSE_TIMES	= #theoreticalImpulseTimes:NUMERIC#,   <!-- 理论冲次数 -->
					CRAFT_CODE	= #craftCode#,   <!-- 工艺单号 -->
					MOULD_ID	= #mouldId#,   <!-- 模具ID -->
					ACTUAL_IMPULSE_TIMES	= #actualImpulseTimes#,   <!-- 实际冲次数 -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->
					PROCESS_FEE_CALCULATE_METHOD	= #processFeeCalculateMethod#, 
											AGREEMENT_MANAGE_TYPE	= #agreementManageType#,   <!-- 委外协议管理方式 -->
					IF_PLAN_FLAG	= #ifPlanFlag#,   <!-- 是否参与排产 -->
					CREATE_PERSON	= #createPerson#,   <!-- 制单人代码 -->
					PROCESS_SPEED	= #processSpeed:NUMERIC#,   <!-- 加工速度(米/分钟) -->
					AUDITOR_NAME	= #auditorName#,   <!-- 审核人姓名 -->
					PREVIOUS_PROCESS_ORDER_ID	= #previousProcessOrderId#,   <!-- 上道工序/工单 -->
					NEXT_PROCESS_ORDER_ID	= #nextProcessOrderId#,   <!-- 下道工序/工单 -->
					IF_NEED_SUIT_CUT	= #ifNeedSuitCut#,   <!-- 是否需要套裁方案 -->
					TRADE_CODE	= #tradeCode#,   <!-- 贸易方式 -->
					PROCESS_RATE	= #processRate:NUMERIC#,   <!-- 合格品率 -->
					PROCESS_ID	= #processId#,   <!-- 流程ID -->
					AUDIT_OPNION	= #auditOpnion#,   <!-- 审核意见 -->
					BUSINESS_FROM	= #businessFrom#,   <!-- 业务来源 -->
					TEAM_ID	= #teamId#,   <!-- 班组 -->
					WORKING_SHIFT	= #workingShift#,   <!-- 班次 -->
					SPH	= #sph#,   <!-- sph每小时加工片数(落料和精剪） -->
					SGM_FLAG	= #sgmFlag#,   <!-- SGM标记 -->
					AUDIT_FLAG	= #auditFlag#,   <!-- 审核标记 -->
					APPR_STATUS	= #apprStatus#,   <!-- 审批状态 -->
					OUT_APPR_STATUS	= #outApprStatus#,   <!-- 委外审批状态 -->
					OUT_AUDIT_FLAG	= #outAuditFlag#,   <!-- 委外审核标记 -->
					OUT_AUDIT_OPNION	= #outAuditOpnion#,   <!-- 委外审核意见 -->
					OUT_AUDIT_TIME	= #outAuditTime#,   <!-- 委外审核时间 -->
					OUT_AUDITOR_ID	= #outAuditorId#,   <!-- 委外审核人工号 -->
					OUT_AUDITOR_NAME	= #outAuditorName#,   <!-- 委外审核人姓名 -->
					OUT_PROCESS_ID	= #outProcessId#,   <!-- 委外流程ID -->
					IF_SUBSTIT_AUDIT	= #ifSubstitAudit#,   <!-- 是否需要替代审批 -->
					IF_YIELD_AUDIT	= #ifYieldAudit#,   <!-- 是否需要成材率审批 -->
					IF_OUTPROCESS_AUDIT	= #ifOutprocessAudit#,   <!-- 是否需要委外工单审批 -->
					PROCESS_SYN_TRIGGER_TYPE	= #processSynTriggerType#,   <!-- 协同触发方式 -->
					IF_SYN	= #ifSyn#,   <!-- 是否协同 -->
					PROCESS_SYN_RECEIVE	= #processSynReceive#,   <!-- 协同接收方 -->
					PROCESS_SEQ_ID	= #processSeqId:NUMERIC#,   <!-- 加工顺序 -->
					IF_NO_PROCESSING_AGREEMENT	= #ifNoProcessingAgreement#,   <!-- 无加工协议（委外） -->
					ORDER_CONFIRM_TIME	= #orderConfirmTime#,   <!-- 生产工单确认时间 -->
					PLAN_RATE	= #planRate:NUMERIC#,   <!-- 工艺成材率 -->
					SYN_SETTLE_FLAG	= #synSettleFlag#,   <!-- 委托方结算标记 -->
					KNIFE_PRINT_BATCH_ID	= #knifePrintBatchId#  <!-- 机组组刀打印批次号 -->
			WHERE 	
			PROCESS_DEMAND_ID = #processDemandId# AND 
			PROCESS_DEMAND_SUB_ID = #processDemandSubId# AND 
			PROCESS_DEMAND_PROCESS_STATUS = #processDemandProcessStatus# AND 
			SEG_NO = #segNo# AND 
			UUID = #uuid#
	</update>

	<update id="updateStatus">
		UPDATE ${meviSchema}.TVIPM0009
		SET
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		PROCESS_DEMAND_PROCESS_STATUS = #processDemandProcessStatus# <!-- 需求单工序表状态 -->
		WHERE
		PROCESS_ORDER_ID = #processOrderId#
		AND SEG_NO = #segNo#
	</update>

	<select id="queryNextMouldId" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.vi.pm.domain.VIPM0009">
		select UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		PROCESS_ORDER_ID	as "processOrderId",  <!-- 生产工单号 -->
		PROCESS_DEMAND_ID	as "processDemandId",  <!-- 生产需求单号 -->
		PROCESS_DEMAND_SUB_ID	as "processDemandSubId",  <!-- 生产需求单子项号 -->
		PROCESS_CATEGORY	as "processCategory",  <!-- 工序大类代码 -->
		AFTER_P_ORDER_ID	as "afterPOrderId",  <!-- 后道工单号 -->
		BUSINESS_TYPE	as "businessType",  <!-- 业务类型 -->
		MACHINE_CODE	as "machineCode",  <!-- 机组代码 -->
		KNIFE_ID	as "knifeId",  <!-- 排刀号 -->
		SCHEDULE_START_DATE	as "scheduleStartDate",  <!-- 排产开始时间 -->
		SCHEDULE_END_DATE	as "scheduleEndDate",  <!-- 排产结束时间 -->
		PROCESS_DEMAND_PROCESS_STATUS	as "processDemandProcessStatus",  <!-- 生产需求单工序表状态 -->
		PROCESS_FEE_PRICE_TYPE	as "processFeePriceType",  <!-- 加工费计价类型 -->
		MPROVIDER_ID	as "mproviderId",  <!-- 加工单位代码 -->
		MPROVIDER_NAME	as "mproviderName",  <!-- 加工单位名称 -->
		AGREEMENT_ID	as "agreementId",  <!-- 加工协议号 -->
		AGREEMENT_SUBID	as "agreementSubid",  <!-- 加工协议子项号 -->
		TAX_RATE	as "taxRate",  <!-- 税率 -->
		PROCESS_FEE_PRICE	as "processFeePrice",  <!-- 加工费单价(不含税) -->
		PROCESS_FEE_PRICE_TAX	as "processFeePriceTax",  <!-- 加工费单价(含税) -->
		REMARK	as "remark",  <!-- 备注 -->
		CAN_RETURN_MATERIAL	as "canReturnMaterial",  <!-- 是否可以退料 -->
		SCHEDULE_SERIAL_NUM	as "scheduleSerialNum",  <!-- 排产序号 -->
		PRODUCTION_LOCK	as "productionLock",  <!-- 封锁产出品标记 -->
		PROCESS_TIME	as "processTime",  <!-- 生产时间(分钟) -->
		PREPARE_TIME	as "prepareTime",  <!-- 准备时间(分钟) -->
		DELIVERY_TIME	as "deliveryTime",  <!-- 交接时间(分钟) -->
		IF_FIRST	as "ifFirst",  <!-- 是否首道 -->
		IF_ROLL	as "ifRoll",  <!-- 原料形状 -->
		AUDITOR_ID	as "auditorId",  <!-- 审核人工号 -->
		AUDIT_TIME	as "auditTime",  <!-- 审核时间 -->
		SCHEDULE_SERIAL_SUBNUM	as "scheduleSerialSubnum",  <!-- 排产子序号 -->
		DELIVERY_DATE	as "deliveryDate",  <!-- 加工完成时间 -->
		PROCESS_FIN_START_DATE	as "processFinStartDate",  <!-- 生产实际开始时间 -->
		PROCESS_FIN_END_DATE	as "processFinEndDate",  <!-- 生产实际结束时间 -->
		PRODUCT_REMAIN_QTY	as "productRemainQty",  <!-- 加工剩余数量 -->
		PRODUCT_REMAIN_WEIGHT	as "productRemainWeight",  <!-- 加工剩余重量 -->
		SCHEDULE_LOCK_FLAG	as "scheduleLockFlag",  <!-- 排产锁定标记 -->
		MATERIAL_RATE	as "materialRate",  <!-- 原料成材率 -->
		IF_MATERAIL	as "ifMaterail",  <!-- 是否强制指定原料卷 -->
		IF_ARRANGE_ONLY	as "ifArrangeOnly",  <!-- 是否单排刀 -->
		PRE_PROCESS_DEMAND_SUB_ID	as "preProcessDemandSubId",  <!-- 上一道生产需求单子项号 -->
		PROCESS_IF_SETTLE_FINISH	as "processIfSettleFinish",  <!-- 生产加工是否结算完成 -->
		PROCESS_IF_SETTLE	as "processIfSettle",  <!-- 生产加工是否结算加工费 -->
		SUIT_VOUCHER_NUM	as "suitVoucherNum",  <!-- 套裁封锁单据号 -->
		DATA_SOURCE	as "dataSource",  <!-- 数据来源 -->
		PRINT_BATCH_ID	as "printBatchId",  <!-- 打印批次号 -->
		RAW_MATERIAL_TRANSFER_TIME	as "rawMaterialTransferTime",  <!-- 原料转运时间 -->
		COMBINE_NO	as "combineNo",  <!-- 归并号 -->
		SYN_FLAG	as "synFlag",  <!-- 协同标记 -->
		ALLOC_TIME	as "allocTime",  <!-- 分配时间（分钟） -->
		SCHEDULE_ISSUE_DATE	as "scheduleIssueDate",  <!-- 排产下发时间 -->
		SCHEDULE_ISSUE_PERSON	as "scheduleIssuePerson",  <!-- 排产下发人 -->
		OUTPROCESS_REASONS	as "outprocessReasons",  <!-- 委外原因 -->
		DELIVERY_DATE_REMARK	as "deliveryDateRemark",  <!-- 交期备注 -->
		SPECIAL_REMARK	as "specialRemark",  <!-- 特殊备注 -->
		IF_CHECK_ASSAY	as "ifCheckAssay",  <!-- 是否检化验 -->
		YIELD	as "yield",  <!-- 成材率 -->
		THEORETICAL_IMPULSE_TIMES	as "theoreticalImpulseTimes",  <!-- 理论冲次数 -->
		CRAFT_CODE	as "craftCode",  <!-- 工艺单号 -->
		MOULD_ID	as "mouldId",  <!-- 模具ID -->
		ACTUAL_IMPULSE_TIMES	as "actualImpulseTimes",  <!-- 实际冲次数 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		TENANT_USER	as "tenantUser",  <!-- 租户 -->
		PROCESS_FEE_CALCULATE_METHOD	as "processFeeCalculateMethod",
		SEG_NO	as "segNo",  <!-- 业务账套 -->
		UUID	as "uuid",  <!-- ID -->
		AGREEMENT_MANAGE_TYPE	as "agreementManageType",  <!-- 委外协议管理方式 -->
		IF_PLAN_FLAG	as "ifPlanFlag",  <!-- 是否参与排产 -->
		CREATE_PERSON	as "createPerson",  <!-- 制单人代码 -->
		PROCESS_SPEED	as "processSpeed",  <!-- 加工速度(米/分钟) -->
		AUDITOR_NAME	as "auditorName",  <!-- 审核人姓名 -->
		PREVIOUS_PROCESS_ORDER_ID	as "previousProcessOrderId",  <!-- 上道工序/工单 -->
		NEXT_PROCESS_ORDER_ID	as "nextProcessOrderId",  <!-- 下道工序/工单 -->
		IF_NEED_SUIT_CUT	as "ifNeedSuitCut",  <!-- 是否需要套裁方案 -->
		TRADE_CODE	as "tradeCode",  <!-- 贸易方式 -->
		PROCESS_RATE	as "processRate",  <!-- 合格品率 -->
		PROCESS_ID	as "processId",  <!-- 流程ID -->
		AUDIT_OPNION	as "auditOpnion",  <!-- 审核意见 -->
		BUSINESS_FROM	as "businessFrom",  <!-- 业务来源 -->
		TEAM_ID	as "teamId",  <!-- 班组 -->
		WORKING_SHIFT	as "workingShift",  <!-- 班次 -->
		SPH	as "sph",  <!-- sph每小时加工片数(落料和精剪） -->
		SGM_FLAG	as "sgmFlag",  <!-- SGM标记 -->
		AUDIT_FLAG	as "auditFlag",  <!-- 审核标记 -->
		APPR_STATUS	as "apprStatus",  <!-- 审批状态 -->
		OUT_APPR_STATUS	as "outApprStatus",  <!-- 委外审批状态 -->
		OUT_AUDIT_FLAG	as "outAuditFlag",  <!-- 委外审核标记 -->
		OUT_AUDIT_OPNION	as "outAuditOpnion",  <!-- 委外审核意见 -->
		OUT_AUDIT_TIME	as "outAuditTime",  <!-- 委外审核时间 -->
		OUT_AUDITOR_ID	as "outAuditorId",  <!-- 委外审核人工号 -->
		OUT_AUDITOR_NAME	as "outAuditorName",  <!-- 委外审核人姓名 -->
		OUT_PROCESS_ID	as "outProcessId",  <!-- 委外流程ID -->
		IF_SUBSTIT_AUDIT	as "ifSubstitAudit",  <!-- 是否需要替代审批 -->
		IF_YIELD_AUDIT	as "ifYieldAudit",  <!-- 是否需要成材率审批 -->
		IF_OUTPROCESS_AUDIT	as "ifOutprocessAudit",  <!-- 是否需要委外工单审批 -->
		PROCESS_SYN_TRIGGER_TYPE	as "processSynTriggerType",  <!-- 协同触发方式 -->
		IF_SYN	as "ifSyn",  <!-- 是否协同 -->
		PROCESS_SYN_RECEIVE	as "processSynReceive",  <!-- 协同接收方 -->
		PROCESS_SEQ_ID	as "processSeqId",  <!-- 加工顺序 -->
		IF_NO_PROCESSING_AGREEMENT	as "ifNoProcessingAgreement",  <!-- 无加工协议（委外） -->
		ORDER_CONFIRM_TIME	as "orderConfirmTime",  <!-- 生产工单确认时间 -->
		PLAN_RATE	as "planRate",  <!-- 工艺成材率 -->
		SYN_SETTLE_FLAG	as "synSettleFlag",  <!-- 委托方结算标记 -->
		KNIFE_PRINT_BATCH_ID	as "knifePrintBatchId" <!-- 机组组刀打印批次号 -->
		FROM MEVI.TVIPM0009 t
		WHERE t.SEG_NO = #segNo#
		AND t.PROCESS_DEMAND_PROCESS_STATUS != '40'
		AND t.MACHINE_CODE	= #machineCode#
		and (t.PROCESS_DEMAND_PROCESS_STATUS in ('15', '20', '30') or (t.PROCESS_DEMAND_PROCESS_STATUS = '35' and
		(select sum(IFNULL(op.PRODUCT_PROCESS_WEIGHT, 0)) -
		sum(IFNULL(op.PRODUCTION_PROCESSED_WEIGHT, 0))
		from MEVI.TVIPM0007 op
		where op.seg_no = t.seg_no
		and op.process_demand_sub_id = t.process_demand_sub_id
		and op.process_demand_id = t.process_demand_id
		and op.process_demand_output_status&lt;&gt;'00') > 0))
		AND t.SCHEDULE_SERIAL_NUM IS NOT NULL
		AND t.SCHEDULE_SERIAL_SUBNUM IS NOT NULL
		AND t.SCHEDULE_START_DATE IS NOT NULL
		AND t.SCHEDULE_SERIAL_SUBNUM >= 1
		AND t.SCHEDULE_START_DATE > #scheduleStartDate#
		AND IFNULL(t.SCHEDULE_LOCK_FLAG, '0')&lt;&gt;'1'
		AND EXISTS(SELECT 1
		FROM MEVI.TVIPM0007 o
		WHERE o.seg_no = t.seg_no
		AND o.PROCESS_DEMAND_SUB_ID = t.PROCESS_DEMAND_SUB_ID
		AND o.PRODUCT_PROCESS_WEIGHT > 0
		AND o.PROCESS_DEMAND_OUTPUT_STATUS > '00')
		ORDER BY SUBSTR(t.SCHEDULE_START_DATE, 1, 8), t.PROCESS_DEMAND_SUB_ID asc;
	</select>
  
</sqlMap>