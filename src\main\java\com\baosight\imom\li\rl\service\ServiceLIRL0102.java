package com.baosight.imom.li.rl.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imc.common.utils.ImcServiceBase;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.imom.common.constants.MesConstant;
import net.sf.json.JSONObject;
import org.apache.axiom.om.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ResponseHandler;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @Author: 韩亚宁
 * @Description: ${车辆司机信息维护}
 * @Date: 2024/8/14 09:37
 * @Version: 1.0
 */
public class ServiceLIRL0102 extends ServiceBase {
    //60 分钟
    private static int INFO_CALL_OUTT_IME = 1000*60*60;
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0102().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0102.SUB_QUERY_ALL);
        return outInfo;
    }



    public EiInfo postExport(EiInfo inInfo) {
        Map<String,Object> loginMap = new HashMap();
        loginMap.put("userId",UserSession.getUserId());
        loginMap.put("userName",UserSession.getLoginCName());
        loginMap.put("loginName",UserSession.getLoginName());
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        List<Map<String,Object>> hashMapList = dao.queryAll("LIRL0102.subQueryAll", queryBlock);
        //inInfo.addBlock("exportColumnBlock").addBlockMeta(getExportBlockMeta());
        if (CollectionUtils.isNotEmpty(hashMapList)){
            extracted(hashMapList,"1");
        }
        Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, hashMapList, loginMap);
        inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        return inInfo;
    }


    public void extracted(List<Map<String,Object>> hashMapList,String b) {
        for (Map hashMap: hashMapList){
            if ("1".equals(b)){
                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "撤销");
                        break;
                    case "10":
                        hashMap.put("status", "新增");
                        break;
                    case "20":
                        hashMap.put("status", "生效");
                        break;

                }
            }
        }
    }
    public EiBlockMeta getExportBlockMeta() {
        EiColumn eiColumn;

        EiBlockMeta eiMetadata = new EiBlockMeta();
        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(撤销：00、新增：10、生效：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationIdentity");
        eiColumn.setDescName("司机身份");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverIdentity");
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("承运商/客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tel");
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);
        return eiMetadata;

    }
    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            // String methodNmae = (String) inInfo.get("methodName");
            List<HashMap> listHashMap = inInfo.getBlock("add_status").getRows();

                for (HashMap hashmap : listHashMap) {

                    String segNo = MapUtils.getString(hashmap, "segNo", "");
                    String customerId = MapUtils.getString(hashmap, "customerId", "");
                    String customerName = MapUtils.getString(hashmap, "customerName", "");
                    String type = MapUtils.getString(hashmap, "type", "");
                    String pageId = MapUtils.getString(hashmap, "uuid", "");
                    String tel = MapUtils.getString(hashmap, "tel", "");
                    if ("insert".equals(type)){
                        // String tel = MapUtils.getString(hashmap, "tel", "");
                        pageId = StrUtil.getUUID();
                        hashmap.put("status", 10);//状态
                        hashmap.put("delFlag", 0);//记录删除标记

                        hashmap.put("remark", MapUtils.getString(hashmap, "remark", "").trim());//备注
                        hashmap.put("uuid", pageId);//UUID
                        RecordUtils.setCreator(hashmap);
                        //判断客户预约身份是否存在
                        HashMap<Object, Object> hashMap = new HashMap<>();
                        hashMap.put("segNo",segNo);
                        hashMap.put("customerId",customerId);
                        hashMap.put("customerName",customerName);
                        hashMap.put("status","20");

                        int count = super.count(LIRL0101.COUNT_CUSTOMER_INFO, hashMap);
                        if (count <= 0) {
                            String massage = MapUtils.getString(hashmap, "customerName", "").trim() + "预约身份信息不存在,不能维护司机!";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg(massage);
                            return inInfo;
                        }
                        //司机信息去重
                        inInfo = distinctDriverInfo(inInfo, hashmap);
                        if (inInfo.getStatus()==-1){
                            return inInfo;
                        }
                        //预约身份维护是否控制部门组织
                        String ifAllocationOrganization = new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALLOCATION_ORGANIZATION", dao);
                        if ("1".equals(ifAllocationOrganization)) {
                            //校验客户、承运商信息
                            EiInfo outInfo = verifyCustomerInfo(inInfo, segNo, customerId);
                            if (outInfo.getStatus() == -1) {
                                return outInfo;
                            }
                        }

                        //遍历子项
                        List<HashMap> buttonsetchild = inInfo.getBlock("buttonsetchild").getRows();
                        if (CollectionUtils.isNotEmpty(buttonsetchild)) {
                            //插入车牌数据
                            for (HashMap hashmap1 : buttonsetchild) {

                                HashMap queryMap = new HashMap<>();
                                queryMap.put("delFlag", 0);
                                queryMap.put("tel",tel);
                                queryMap.put("customerId", customerId);
                                queryMap.put("segNo", segNo);
                                //批量查询车牌号
                                queryMap.put("vehicleNo", hashmap1.get("vehicleNo"));
                                //查询承运商和客户下有几条
                                count = super.count("LIRL0103.countByCustomerId", queryMap);
                                if (count >= 1) {
                                    String massage = customerName + "已存在司机:" + "" + ",车牌号为:" + hashmap1.get("vehicleNo") + "的信息!";
                                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    inInfo.setMsg(massage);
                                    return inInfo;
                                }
                                String vehicleNo1 = MapUtils.getString(hashmap1, "vehicleNo");
                                hashmap1.put("uuid", StrUtil.getUUID());
                                hashmap1.put("segNo", segNo);
                                hashmap1.put("unitCode", segNo);
                                hashmap1.put("vehicleNo", vehicleNo1);
                                hashmap1.put("m_uuid", pageId);
                                hashmap1.put("status","10");
                                RecordUtils.setCreator(hashmap1);
                                this.dao.insert(LIRL0103.INSERT, hashmap1);
                                hashmap.put("uuid", pageId);

                            }
                        }
                        this.dao.insert(LIRL0102.INSERT, hashmap);
                    }else {
                        // String tel = MapUtils.getString(hashmap, "tel", "");
                        String uuid = pageId;
                        hashmap.put("status", 10);//状态
                        hashmap.put("delFlag", 0);//记录删除标记

                        hashmap.put("remark", MapUtils.getString(hashmap, "remark", "").trim());//备注
                        hashmap.put("uuid", uuid);//UUID
                        hashmap.put("pageId", pageId);//UUID
                        RecordUtils.setCreator(hashmap);

                        //判断客户预约身份是否存在
                        HashMap<Object, Object> hashMap = new HashMap<>();
                        hashMap.put("segNo",segNo);
                        hashMap.put("customerId",customerId);
                        //判断客户预约身份是否存在
                        int count = super.count(LIRL0101.COUNT_CUSTOMER_INFO, hashMap);
                        if (count <= 0) {
                            String massage = MapUtils.getString(hashmap, "customerName", "").trim() + "预约身份信息不存在,不能维护司机!";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg(massage);
                            return inInfo;
                        }
                        //预约身份维护是否控制部门组织
                        String ifAllocationOrganization = new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALLOCATION_ORGANIZATION", dao);
                        if ("1".equals(ifAllocationOrganization)) {
                            //校验客户、承运商信息
                            EiInfo outInfo = verifyCustomerInfo(inInfo, segNo, customerId);
                            if (outInfo.getStatus() == -1) {
                                return outInfo;
                            }
                        }
                        //遍历子项
                        List<HashMap> buttonsetchild = inInfo.getBlock("buttonsetchild").getRows();
                        if (CollectionUtils.isNotEmpty(buttonsetchild)) {
                            //插入车牌数据
                            for (HashMap hashmap1 : buttonsetchild) {
                                String vehicleNo1 = MapUtils.getString(hashmap1, "vehicleNo");
                                String uuidChild = MapUtils.getString(hashmap1, "uuid");

                                if (StringUtils.isBlank(uuidChild)){
                                    HashMap queryMap = new HashMap<>();
                                    queryMap.put("delFlag", 0);
                                    queryMap.put("customerId", customerId);
                                    queryMap.put("segNo", segNo);
                                    queryMap.put("vehicleNo", vehicleNo1);
                                    queryMap.put("tel", tel);
                                    count = super.count("LIRL0103.countByCustomerId", queryMap);
                                    if (count >= 1) {
                                        String massage = customerName + "已存在司机:" + "" + ",车牌号为:" + vehicleNo1 + "的信息!";
                                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        inInfo.setMsg(massage);
                                        return inInfo;
                                    }
                                    hashmap1.put("uuid", StrUtil.getUUID());
                                    hashmap1.put("segNo", segNo);
                                    hashmap1.put("unitCode", segNo);
                                    hashmap1.put("vehicleNo", vehicleNo1);
                                    hashmap1.put("m_uuid",pageId);
                                    hashmap1.put("status","10");
                                    RecordUtils.setCreator(hashmap1);
                                    this.dao.insert(LIRL0103.INSERT, hashmap1);
                                }else {

                                    hashmap1.put("segNo", segNo);
                                    hashmap1.put("vehicleNo", MapUtils.getString(hashmap1, "vehicleNo"));
                                    hashmap1.put("delFlag", 0);
                                    RecordUtils.setRevisor(hashmap1);
                                    this.dao.update(LIRL0103.UPDATE, hashmap1);
                                }

                            }
                        }
                        hashmap.put("uuid","");
                        this.dao.update(LIRL0102.UPDATE, hashmap);
                    }

                }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 验证客户信息是否正确
     * @param inInfo
     * @param segNo
     * @param customerId
     * @return
     */
    public EiInfo verifyCustomerInfo(EiInfo inInfo, String segNo, String customerId) {
        //判断运输管理人员只能维护承运商的，内销人员只能维护客户的
        //根据账套、客户编号查询是否是内销、运输管理人员
        //根据工号账套、把部门查询出来
        if (StringUtils.isBlank(segNo)){
            segNo = (String) inInfo.get("segNo");
        }

        if (StringUtils.isBlank(customerId)){
            customerId = (String) inInfo.get("customerId");
        }

        EiInfo outInfo = new EiInfo();
        List<HashMap> returnList=new ArrayList<>();
        String reservationIdentity="";//预约身份
        String userId = UserSession.getUserId();
        String userName = UserSession.getLoginCName();
        List<String> userIdList = new ArrayList<>();
        userIdList.add(userId);
        inInfo.set("related0SegNo", segNo);
        inInfo.set("empNos",userIdList );
        inInfo.set(EiConstant.serviceId,"S_UC_EW_0765");
        try {
            outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;

            }
            returnList = (List<HashMap>) outInfo.get("returnList");
            outInfo.addBlock("sub_result").addRows(returnList);
            outInfo.addBlock(EiConstant.resultBlock).addRows(returnList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }
        if (CollectionUtils.isEmpty(returnList)){
            String massage = "当前登录人:"+userName + "部门组织不存在!";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        //根据customerId判断是承运商还是客户
        HashMap<String, String> hashMapLIRL0101 = new HashMap<>();
        hashMapLIRL0101.put("segNo", segNo);
        hashMapLIRL0101.put("status","20");
        hashMapLIRL0101.put("customerId", customerId);
        hashMapLIRL0101.put("administrator", userName);
        List<LIRL0101> queryLIRL0101 = this.dao.query(LIRL0101.QUERY, hashMapLIRL0101);
        if (CollectionUtils.isNotEmpty(queryLIRL0101)){
            reservationIdentity = queryLIRL0101.get(0).getReservationIdentity();
        }
        for (HashMap hashMap : returnList) {
            String applyDeptName = MapUtils.getString(hashMap, "segName", "");
            String applyDept = MapUtils.getString(hashMap, "segNo", "");
            String related0SegNo = MapUtils.getString(hashMap, "related0SegNo", "");
            if (related0SegNo.equals(segNo)) {
                HashMap<String, String> hashMapLirl0110 = new HashMap<>();
                hashMapLirl0110.put("segNo", segNo);
                hashMapLirl0110.put("status", "20");
                hashMapLirl0110.put("applyDept", applyDept);
                if ("10".equals(reservationIdentity)) {
                    hashMapLirl0110.put("subCategory", "10");
                    List<LIRL0110> queryLIRL0110 = dao.query(LIRL0110.QUERY, hashMapLirl0110);
                    if (CollectionUtils.isEmpty(queryLIRL0110)) {
                        String massage = "当前登录人:" + userName + "不属于客户管理人员，无法维护客户的司机！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                } else if ("20".equals(reservationIdentity)) {
                    hashMapLirl0110.put("subCategory", "20");
                    List<LIRL0110> queryLIRL0110 = dao.query(LIRL0110.QUERY, hashMapLirl0110);
                    if (CollectionUtils.isEmpty(queryLIRL0110)) {
                        String massage = "当前登录人:" + userName + "不属于客户管理人员，无法维护客户的司机！";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
            }else {
                String massage = "当前登录人:"+userName + "部门组织不存在!";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
        }
        return inInfo;
    }

    /***
     * 司机信息去重
     * @param inInfo
     * @param hashmap
     * @return
     */
    private EiInfo distinctDriverInfo(EiInfo inInfo, HashMap hashmap) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo",MapUtils.getString(hashmap, "segNo", ""));
        hashMap.put("customerId",MapUtils.getString(hashmap, "customerId", ""));
        hashMap.put("tel",MapUtils.getString(hashmap, "tel", ""));
        //去重
        int count = super.count(LIRL0102.COUNT_DRIVER_INFO, hashMap);
        if (count>=1) {
            String massage =  MapUtils.getString(hashmap, "customerName", "").trim()+"已存在该司机信息！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<HashMap> query = dao.query(LIRL0102.SUB_QUERY, hashmap);
                for (HashMap lirl0102:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, convert(lirl0102, LIRL0102.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashmap);

                //判断客户预约身份是否存在
                int count = this.dao.count(LIRL0101.COUNT_CUSTOMER_INFO, hashmap);
                if(count<=0) {
                    String massage =  MapUtils.getString(hashmap, "customerName", "").trim()+"预约身份信息不存在,不能维护司机!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }

                inInfo = distinctDriverInfo(inInfo, hashmap);
                if (inInfo.getStatus()<0){
                    return inInfo;
                }
            }
            inInfo = super.update(inInfo, LIRL0102.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<HashMap> query = dao.query(LIRL0102.SUB_QUERY_ALL, hashmap);
                for (HashMap lirl0102:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, convert(lirl0102, LIRL0102.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", "00");//记录删除标记
                hashmap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashmap);

                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("segNo", MapUtils.getString(hashmap, "segNo", ""));
                hashMap.put("unitCode", MapUtils.getString(hashmap, "segNo", ""));
                hashMap.put("m_uuid", MapUtils.getString(hashmap, "pageId", ""));
                hashMap.put("status", "00");
                hashMap.put("remark", " ");
                hashMap.put("delFlag", "1");
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0103.UPDATE_VEHICLE_NO_ALL, hashMap);

            }
            inInfo = super.update(inInfo, LIRL0102.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                String pageId = MapUtils.getString(hashmap, "pageId");
                //后台查询状态判断
                List<HashMap> query = dao.query(LIRL0102.SUB_QUERY_ALL, hashmap);
                for (HashMap lirl0102 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, convert(lirl0102, LIRL0102.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", 20);//状态
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
                RecordUtils.setRevisor(hashmap);
                //生效子项
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("segNo", MapUtils.getString(hashmap, "segNo", ""));
                hashMap.put("unitCode", MapUtils.getString(hashmap, "segNo", ""));
                hashMap.put("m_uuid", MapUtils.getString(hashmap, "pageId", ""));
                hashMap.put("status", "20");
                hashMap.put("remark", " ");
                hashMap.put("delFlag", "0");
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0103.UPDATE_VEHICLE_NO_ALL, hashMap);
                String ifVehicleImport = new SwitchUtils().getProcessSwitchValue((String) hashmap.get("segNo"), "IF_VEHICLE_IMPORT", dao);
                //插入车辆类型表
                String vehicleNo = MapUtils.getString(hashmap, "vehicleNo");
                //判断账套
                //遍历子项
                if ("JC000000".equals(MapUtils.getString(hashmap, "segNo", ""))) {
                    if (vehicleNo.contains(",")) {
                        String[] split = vehicleNo.split(",");
                        for (String vehicleNoStr : split) {
                            //去空格
                            vehicleNoStr = vehicleNoStr.trim();
                            //校验车牌号
                            boolean b = validateLicensePlate(vehicleNoStr);
                            if (!b) {
                                String massage = "车牌号格式不正确!";
                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                inInfo.setMsg(massage);
                                throw new RuntimeException(massage);
                            }
                            if ("1".equals(ifVehicleImport)) {
                                HashMap<Object, Object> hashmap2 = new HashMap<>();
                                RecordUtils.setCreator(hashmap2);
                                hashmap2.put("status", "20");
                                hashmap2.put("vehicleType", "1");
                                hashmap2.put("vehicleNo", vehicleNoStr);
                                hashmap2.put("unitCode", MapUtils.getString(hashmap, "segNo", ""));
                                hashmap2.put("segNo", MapUtils.getString(hashmap, "segNo", ""));
                                hashmap2.put("remark", MapUtils.getString(hashmap, "remark", ""));
                                hashmap2.put("drUuid", pageId);

                                this.dao.insert("LIRL0501.insert", hashmap2);
                            }
                        }
                    } else {
                        //校验车牌号
                        boolean b = validateLicensePlate(vehicleNo);
                        if (!b) {
                            String massage = "车牌号格式不正确!";
                            throw new RuntimeException(massage);
                        }
                        if ("1".equals(ifVehicleImport)) {
                            HashMap<Object, Object> hashmap2 = new HashMap<>();
                            RecordUtils.setCreator(hashmap2);
                            hashmap2.put("status", "20");
                            hashmap2.put("vehicleType", "1");
                            hashmap2.put("vehicleNo", vehicleNo);
                            hashmap2.put("unitCode", MapUtils.getString(hashmap, "segNo", ""));
                            hashmap2.put("segNo", MapUtils.getString(hashmap, "segNo", ""));
                            hashmap2.put("remark", MapUtils.getString(hashmap, "remark", ""));
                            hashmap2.put("drUuid", pageId);
                            this.dao.insert("LIRL0501.insert", hashmap2);
                        }
                    }
                }
            }
            inInfo = super.update(inInfo, LIRL0102.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmno(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                String tel = MapUtils.getString(hashmap, "tel");
                String segNo = MapUtils.getString(hashmap, "segNo");
                //后台查询状态判断
                List<HashMap> query = dao.query(LIRL0102.SUB_QUERY_ALL, hashmap);
                for (HashMap lirl0102:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, convert(lirl0102, LIRL0102.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", 10);//状态
                RecordUtils.setRevisor(hashmap);

                //查询司机若司机有登记之后的任务还未完成，则不允许删除司机，给与相应提示“此司机正在有作业任务，不可删除”；
                // 若司机有预约未登记，则删除时自动取消预约，并给与提示“此司机有预约任务，系统自动取消”。
                Map queryMap = new HashMap<>();
                queryMap.put("tel",tel);
                queryMap.put("segNo",segNo);
                queryMap.put("delFlag",0);
                int count = super.count("LIRL0301.countByTel", queryMap);
                if (count > 0){
                    String massage = "手机号"+tel+"的司机信息有登记之后的任务还未完成,不能反确认!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }

                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("segNo", MapUtils.getString(hashmap, "segNo", ""));
                hashMap.put("unitCode", MapUtils.getString(hashmap, "segNo", ""));
                hashMap.put("m_uuid", MapUtils.getString(hashmap, "pageId", ""));
                hashMap.put("status", "10");
                hashMap.put("remark", " ");
                hashMap.put("delFlag", "0");
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0103.UPDATE_VEHICLE_NO_ALL, hashMap);

                //删除完成后查询司机预约单
                queryMap = new HashMap<>();
                queryMap.put("delFlag",0);
                queryMap.put("driverTel",tel);
                //若司机有预约未登记，则删除时自动取消预约，并给与提示“此司机有预约任务，系统自动取消”
                int count1 = super.count(LIRL0201.COUNT, queryMap);
                if (count1 > 0){
                    hashMap.put("driverTel", tel);
                    dao.update("LIRL0201.deleteByTel",hashMap);
                    String massage = "手机号"+tel+"的此司机有预约任务，系统自动取消!";
                    inInfo.setMsg(massage);
                }
            }
            inInfo = super.update(inInfo, LIRL0102.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 承运商和客户调用IMC客商的服务.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo subQuery(EiInfo inInfo) {
        try {
            //购买方税号
            EiInfo outInfo = new EiInfo();
            EiInfo enterInfo = new EiInfo();
            JSONObject jsonObject = new JSONObject();
            Map queryMap = inInfo.getBlock("sub_query_status").getRow(0);
            Map hashMap = inInfo.getBlock("sub_result").getAttr();
            int limit = MapUtils.getInteger(hashMap, "limit");
            int offset = MapUtils.getInteger(hashMap, "offset");
            String userNum = (String) queryMap.get("userNum");
            if (StringUtils.isNotEmpty(userNum)) {
                enterInfo.set("userNum", userNum);
            }
            String chineseUserName = (String) queryMap.get("chineseUserName");
            if (StringUtils.isNotEmpty(chineseUserName)) {
                enterInfo.set("chineseUserName", chineseUserName);
            }
            /*enterInfo.set("limit", limit);
            enterInfo.set("offset", offset);
            enterInfo.set(EiConstant.serviceId, "S_UN_BI_0017");
            String xplatToken = ImcGlobalUtils.getToken();
            outInfo = EServiceManager.call(enterInfo, xplatToken);
            List list = outInfo.getBlock(EiConstant.resultBlock).getRows();
            if (CollectionUtils.isNotEmpty(list)) {
                // 返回成功状态和消息
                inInfo.setStatus(EiConstant.STATUS_DEFAULT);
                inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
                inInfo.setRows("sub_result",list);
                return inInfo;
            } else {
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsgByKey(MesConstant.EPResource.EP_1001);
            }*/
            PrintWriter out = null;
            BufferedReader in = null;
            StringBuilder result = new StringBuilder();
            //下面五个参数是本服务的参数
            //shareServiceId参数是调用的必填参数  shareServiceId为需要调用的共享服务ID
            jsonObject.put("shareServiceId", "S_UN_BI_0017");
            //resProjectEname参数是调用的必填参数，resProjectEname为本次调用方的项目英文名
            jsonObject.put("resProjectEname", "elim");
            //resAppEname参数是调用的必填参数，resAppEname为本次调用方的应用英文名
            jsonObject.put("resAppEname", "elim-bc");
            String returnValue = "";
            CloseableHttpClient httpClient = HttpClients.createDefault();
            ResponseHandler<String> responseHandler = new BasicResponseHandler();
            //传入接口参数
            String url = "http://eplattest.baogang.info/service/S_UN_BI_0017";
            System.out.println(jsonObject);
            String urlNameString = url;
            URL realUrl = new URL(urlNameString);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty("contentType", "UTF-8");
            conn.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            String xplatToken = ImcGlobalUtils.getToken();
            conn.setRequestProperty("Xplat-Token", xplatToken); // 设置发送数据的token
            conn.setRequestProperty("token", xplatToken); // 设置发送数据的token
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(jsonObject);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 将map转换为实体类
     *
     * @param map
     * @param entityClass
     * @return
     */
    public static <T> T convert(HashMap<String, Object> map, Class<T> entityClass) {
        try {
            T entity = entityClass.getDeclaredConstructor().newInstance();
            Field[] fields = entityClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (map.containsKey(fieldName)) {
                    Object value = map.get(fieldName);
                    if (value!= null && field.getType().isAssignableFrom(value.getClass())) {
                        field.set(entity, value);
                    }
                }
            }
            return entity;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     *  司机车牌信息查询
     * @param eiInfo
     * @return
     */
    public EiInfo queryDriverChildShow(EiInfo eiInfo){
        List<HashMap> result = eiInfo.getBlock("result").getRows();
        if (CollectionUtils.isNotEmpty(result)){
            String pageId = MapUtils.getString(result.get(0), "pageId");
            String segNo = MapUtils.getString(result.get(0), "segNo");
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("m_uuid",pageId);
            hashMap.put("statusDel","00");
            //查找司机车牌信息
            List<Map> list = dao.query(LIRL0103.QUERY, hashMap);
            eiInfo.addBlock("buttonsetchild").addRows(list);
        }
        return eiInfo;
    }


    /**
     * 删除子项.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo deleteSave(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("buttonsetchild").getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0103> query = dao.query(LIRL0103.QUERY, hashmap);
                for (LIRL0103 lirl0103:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0103);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", "00");//记录删除标记
                hashmap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashmap);
                this.dao.update(LIRL0103.UPDATE, hashmap);
                //判断是不是所有的子项都被删除了？
                hashmap.put("uuid","");
                hashmap.put("vehicleNo","");
                hashmap.put("statusDel","00");
                List<LIRL0103> queryLIRL0103 = this.dao.query(LIRL0103.QUERY, hashmap);
                if (CollectionUtils.isEmpty(queryLIRL0103)){
                    //主项撤销
                    hashmap.put("status","00");
                    hashmap.put("delFlag", 1);//记录删除标记
                    hashmap.put("uuid",MapUtils.getString(hashmap,"m_uuid"));
                    this.dao.update(LIRL0102.UPDATE,hashmap);
                }
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 导入司机信息
     * @param inInfo
     * @ServiceId S_UR_PB_2015
     * @return
     */
    public EiInfo importExcel(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        EiInfo info = new EiInfo();
        List result = new ArrayList();
        try {
            String[] keys = new String[]{"segNo", "customerId", "customerName", "driverName", "tel", "driverIdentity"
                    , "vehicleNo"};
            MultipartFile file = (MultipartFile) inInfo.get("file");
            if (file != null) {
                InputStream in = file.getInputStream();
                //获取第0行的列数和数据
                String[][] data = getDataByInputStream(in, 0);
                //从第一行开始获取数据
                for (int i = 1; i < data.length; i++) {
                    Map map = new HashMap();
                    for (int j = 0; j < keys.length; j++) {
                        map.put(keys[j], data[i][j]);
                    }
                    result.add(map);
                }
            }
            info.addBlock(EiConstant.resultBlock).setRows(result);
        } catch (Exception e) {
            outInfo.setMsg(e.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        EiInfo out = new EiInfo();
        StringBuffer erorMessage = new StringBuffer();
        Map<String, Object> userInfo = new HashMap(16);
        userInfo.put("loginName", UserSession.getLoginName());
        userInfo.put("userId", UserSession.getUserId());
        userInfo.put("userName", UserSession.getLoginCName());

        List<HashMap> result1 = info.getBlock("result").getRows();
            //对应到excel行号
            // info.getBlock(EiConstant.resultBlock).getRows().clear();
            info.getBlock(EiConstant.resultBlock).addRow(result1);
            info.set(EiConstant.serviceName, "LIRL0102");
            info.set(EiConstant.methodName, "importInsert");
            out = XLocalManager.callNoTx(info);
            if (out.getStatus() < 0) {
                erorMessage.append(out.getMsg() + "\r\n");
            }
        if (erorMessage.length() > 0) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(erorMessage.toString());
            outInfo.setDetailMsg(erorMessage.toString());
        } else {
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("导入成功");
        }
        // outInfo.addBlock(EiConstant.resultBlock).setRows(result);
        // outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0102().eiMetadata);
        return outInfo;
    }

    public static String[][] getDataByInputStream(InputStream ins, int ignoreRows) throws FileNotFoundException, IOException {
        List<String[]> result = new ArrayList();
        int rowSize = 0;
        BufferedInputStream in = new BufferedInputStream(ins);
        POIFSFileSystem fs = new POIFSFileSystem(in);
        HSSFWorkbook wb = new HSSFWorkbook(fs);
        HSSFCell cell = null;
        //2023年10月26日 导入只解析第一个sheet页的数据
        /*for(int sheetIndex = 0; sheetIndex < wb.getNumberOfSheets(); ++sheetIndex) {*/
        HSSFSheet st = wb.getSheetAt(0);

        for(int rowIndex = ignoreRows; rowIndex <= st.getLastRowNum(); ++rowIndex) {
            HSSFRow row = st.getRow(rowIndex);
            if (row != null) {
                int tempRowSize = row.getLastCellNum() + 1;
                if (tempRowSize > rowSize) {
                    rowSize = tempRowSize;
                }

                String[] values = new String[rowSize];
                Arrays.fill(values, "");
                boolean hasValue = false;

                for(short columnIndex = 0; columnIndex <= row.getLastCellNum(); ++columnIndex) {
                    String value = "";
                    cell = row.getCell(columnIndex);
                    if (cell != null) {
                        switch (cell.getCellType()) {
                            case 0:
                                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                                    Date date = cell.getDateCellValue();
                                    if (date != null) {
                                        value = (new SimpleDateFormat("yyyy-MM-dd")).format(date);
                                    } else {
                                        value = "";
                                    }
                                } else {
                                    value = cell.getNumericCellValue() + "";
                                    String rightValue = value.substring(value.indexOf(".") + 1);
                                    if(new BigDecimal(rightValue).compareTo(BigDecimal.ZERO) == 0){
                                        value = value.substring(0,value.indexOf("."));
                                    }
                                }
                                break;
                            case 1:
                                value = cell.getStringCellValue();
                                break;
                            case 2:
                                try{
                                    value = cell.getNumericCellValue() + "";
                                }catch(Exception e){
                                    value = cell.getStringCellValue();
                                }
                            case 3:
                                break;
                            case 4:
                                value = cell.getBooleanCellValue() ? "Y" : "N";
                                break;
                            case 5:
                                value = "";
                                break;
                            default:
                                value = "";
                        }
                    }

                    if (columnIndex == 0 && "".equals(value.trim())) {
                        break;
                    }

                    values[columnIndex] = rightTrim(value);
                    hasValue = true;
                }

                if (hasValue) {
                    result.add(values);
                }
            }
        }
        /*}*/

        in.close();
        String[][] returnArray = new String[result.size()][rowSize];

        for(int i = 0; i < returnArray.length; ++i) {
            returnArray[i] = (String[])((String[])result.get(i));
        }

        return returnArray;
    }


    private static String rightTrim(String str) {
        if (str == null) {
            return "";
        } else {
            int length = str.length();

            for(int i = length - 1; i >= 0 && str.charAt(i) == ' '; --i) {
                --length;
            }

            return str.substring(0, length);
        }
    }


    /**
     * 导入新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo importInsert(EiInfo inInfo) {
        try {
            // String methodNmae = (String) inInfo.get("methodName");
            List<HashMap> listHashMap = inInfo.getBlock("result").getRows();
            String empty1 = (String) listHashMap.get(0).get("empty");
            if ("true".equals(empty1)){
                // 异常返回失败状态和消息
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("导入模版中的数据不能为空!");
                return inInfo;
            }

            StringBuffer erorMessage = new StringBuffer();
            int num=1;
            for (HashMap hashmap : listHashMap) {
                String segNo = MapUtils.getString(hashmap, "segNo", "");

                String empty = MapUtils.getString(hashmap, "empty", "");
                String customerId = MapUtils.getString(hashmap, "customerId", "");
                String customerName = MapUtils.getString(hashmap, "customerName", "");
                String tel = MapUtils.getString(hashmap, "tel", "");
                String driverIdentity = MapUtils.getString(hashmap, "driverIdentity", "");
                String vehicleNo = MapUtils.getString(hashmap, "vehicleNo", "");
                if ("false".equals(empty)){
                    continue;
                }

                //校验账套是否存在
                int  count1 = super.count("LIRL0102.querytvzbm81", hashmap);
                if (count1 == 0){
                    throw new RuntimeException("第"+num+"行数据，账套不存在，请检查账套是否正确！");
                }
                // String tel = MapUtils.getString(hashmap, "tel", "");
                String uuid = StrUtil.getUUID();
                hashmap.put("status", 10);//状态
                hashmap.put("delFlag", 0);//记录删除标记
                hashmap.put("unitCode", segNo);
                hashmap.put("remark", MapUtils.getString(hashmap, "remark", "").trim());//备注
                RecordUtils.setCreator(hashmap);
                hashmap.put("uuid", uuid);//UUID
                hashmap.put("reservationIdentity", "30");//预约身份：司机

                //校验手机号、身份证号

                BigDecimal bigDecimal = new BigDecimal(tel);
                tel=bigDecimal.toPlainString();
                hashmap.put("tel",tel);
                boolean telBol = validatePhoneNumber(bigDecimal.toPlainString());
                boolean driverIdentityBol = validateIDNumber(driverIdentity);
                if (!telBol){
                    String massage =  "手机号格式不正确!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("第"+num+"行:"+ massage+ "\r\n");
                    throw new RuntimeException("第"+num+"行:"+ massage+ "\r\n");
                }

                if (!driverIdentityBol){
                    String massage = "身份证号格式不正确!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("第"+num+"行:"+ massage+ "\r\n");
                    throw new RuntimeException("第"+num+"行:"+ massage+ "\r\n");
                }

                //判断客户预约身份是否存在
                HashMap<Object, Object> hashMap = new HashMap<>();
                hashMap.put("segNo", segNo);
                hashMap.put("customerId", customerId);
                hashMap.put("customerName", customerName);
                hashMap.put("status", "20");

                int count = super.count(LIRL0101.COUNT_CUSTOMER_INFO, hashMap);
                if (count <= 0) {
                    String massage = MapUtils.getString(hashmap, "customerName", "").trim() + "预约身份信息不存在,不能维护司机!";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    throw new RuntimeException(massage);
                }
                //司机信息去重
                inInfo = distinctDriverInfo(inInfo, hashmap);
                if (inInfo.getStatus() == -1) {
                    throw new RuntimeException(erorMessage.append("第"+num+"行:"+ inInfo.getMsg()+ "\r\n").toString());
                }
                //预约身份维护是否控制部门组织
                String ifAllocationOrganization = new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALLOCATION_ORGANIZATION", dao);
                if ("1".equals(ifAllocationOrganization)) {
                    //校验客户、承运商信息
                    EiInfo outInfo = verifyCustomerInfo(inInfo, segNo, customerId);
                    if (outInfo.getStatus() == -1) {
                        return outInfo;
                    }
                }
                //遍历子项
                if (vehicleNo.contains(",")){
                    String[] split = vehicleNo.split(",");
                    for (String vehicleNoStr : split) {
                        //校验车牌号
                        boolean b = validateLicensePlate(vehicleNoStr);
                        if (!b){
                            String massage = "车牌号格式不正确!";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg(massage);
                            throw new RuntimeException(massage);
                        }
                        HashMap queryMap = new HashMap<>();
                        queryMap.put("delFlag", 0);
                        // queryMap.put("tel",tel);
                        queryMap.put("customerId", customerId);
                        queryMap.put("segNo", segNo);
                        //批量查询车牌号
                        queryMap.put("vehicleNo", vehicleNoStr);
                        queryMap.put("tel", tel);
                        //查询承运商和客户下有几条
                        count = super.count("LIRL0103.countByCustomerId", queryMap);
                        if (count >= 1) {
                            String massage = customerName + "已存在司机:" + "" + ",车牌号为:" + vehicleNoStr + "的信息!";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg("第"+num+"行:"+ massage+ "\r\n");
                            return inInfo;
                        }
                        HashMap<Object, Object> hashmap1 = new HashMap<>();
                        hashmap1.put("uuid", StrUtil.getUUID());
                        hashmap1.put("segNo", segNo);
                        hashmap1.put("unitCode", segNo);
                        hashmap1.put("vehicleNo", vehicleNoStr);
                        hashmap1.put("m_uuid", uuid);
                        hashmap1.put("status", "10");
                        hashmap1.put("remark", " ");
                        RecordUtils.setCreator(hashmap1);
                        this.dao.insert(LIRL0103.INSERT, hashmap1);
                    }
                }else {

                    //校验车牌号
                    boolean b = validateLicensePlate(vehicleNo);
                    if (!b){
                        String massage ="车牌号格式不正确!";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg("第"+num+"行:"+ massage+ "\r\n");
                        throw new RuntimeException("第"+num+"行:"+ massage+ "\r\n");
                    }
                    HashMap queryMap = new HashMap<>();
                    queryMap.put("delFlag", 0);
                    // queryMap.put("tel",tel);
                    queryMap.put("customerId", customerId);
                    queryMap.put("segNo", segNo);
                    //批量查询车牌号
                    queryMap.put("vehicleNo", vehicleNo);
                    queryMap.put("tel", tel);
                    //查询承运商和客户下有几条
                    count = super.count("LIRL0103.countByCustomerId", queryMap);
                    if (count >= 1) {
                        String massage = customerName + "已存在司机:" + "" + ",车牌号为:" + vehicleNo + "的信息!";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg("第"+num+"行:"+ massage+ "\r\n");
                        throw new RuntimeException("第"+num+"行:"+ massage+ "\r\n");
                    }

                    HashMap<Object, Object> hashmap1 = new HashMap<>();
                    hashmap1.put("uuid", StrUtil.getUUID());
                    hashmap1.put("segNo", segNo);
                    hashmap1.put("unitCode", segNo);
                    hashmap1.put("vehicleNo", vehicleNo);
                    hashmap1.put("status", "10");
                    hashmap1.put("remark", " ");
                    hashmap1.put("m_uuid", uuid);
                    RecordUtils.setCreator(hashmap1);
                    this.dao.insert(LIRL0103.INSERT, hashmap1);
                }

                this.dao.insert(LIRL0102.INSERT, hashmap);
                num++;
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
            return inInfo;
    }

    // 校验手机号的方法
    public static boolean validatePhoneNumber(String phoneNumber) {
        // 手机号的正则表达式，以 1 开头，第二位为 3-9，后面跟 9 位数字
        String regex = "^1[3-9]\\d{9}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }

    // 校验身份证号的方法
    public static boolean validateIDNumber(String idNumber) {
        // 身份证号的正则表达式，简单示例，仅考虑 18 位数字
        String regex = "^(\\d{15}$|^\\d{17}([0-9]|X|x))$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(idNumber);
        return matcher.matches();
    }

    // 校验车牌号的方法
    public static boolean validateLicensePlate(String licensePlate) {
        // 车牌号的正则表达式，简单示例，以汉字开头，后面跟字母和数字的组合
        String regex =  "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]" +
                "[A-Z]" +
                "[A-Z0-9]{4,5}" +
                "[挂]?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(licensePlate);
        return matcher.matches();
    }
}
