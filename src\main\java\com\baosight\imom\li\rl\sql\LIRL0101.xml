<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-08-05 16:36:23
       Version :  1.0
    tableName :${meliSchema}.tlirl0101
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     STATUS  VARCHAR   NOT NULL,
     RESERVATION_IDENTITY  VARCHAR   NOT NULL,
     CUSTOMER_ID  VARCHAR   NOT NULL,
     CUSTOMER_NAME  VARCHAR   NOT NULL,
     TEL  VARCHAR   NOT NULL,
     ADMINISTRATOR  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  SMALLINT   NOT NULL,
     DEL_FLAG  SMALLINT   NOT NULL,
     REMARK  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0101">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID like concat('%',#uuid#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            a.UUID != #notUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="reservationIdentity">
            a.RESERVATION_IDENTITY = #reservationIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            a.CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            a.CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            a.CUSTOMER_ID = #customerId2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            a.CUSTOMER_NAME = #customerName2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tel">
            a.TEL like concat('%',#tel#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="administrator">
            a.ADMINISTRATOR like concat('%',#administrator#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="adminIndtity">
            a.ADMIN_INDTITY = #adminIndtity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            a.ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            a.TENANT_ID = #tenantId#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(a.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(a.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0101">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.STATUS as "status",  <!-- 状态 -->
        a.RESERVATION_IDENTITY as "reservationIdentity",  <!-- 预约身份(包含承运商和客户) -->
        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.TEL as "tel",  <!-- 管理员手机号 -->
        a.ADMINISTRATOR as "administrator",  <!-- 管理员姓名 -->
        a.ADMIN_INDTITY	as "adminIndtity",  <!-- 管理员身份证号 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0101 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="querypostExport" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UUID as "uuid",  <!-- uuid -->
        <!--a.STATUS as "status",-->  <!-- 状态 -->

        case when a.STATUS ='00' then "撤销"
        when a.STATUS ='10' then "新增"
        when a.STATUS ='20' then "确认"
        else "无" end as "status",

        <!--a.RESERVATION_IDENTITY as "reservationIdentity",-->  <!-- 预约身份(包含承运商和客户) -->
        case when
        a.STATUS ='10' then "客户"
        when a.STATUS ='20' then "承运商"
        when a.STATUS ='30' then "租赁单位"
        else "无" end as "reservationIdentity",

        a.CUSTOMER_ID as "customerId",  <!-- 承运商/客户代码 -->
        a.CUSTOMER_NAME as "customerName",  <!-- 承运商/客户名称 -->
        a.TEL as "tel",  <!-- 管理员手机号 -->
        a.ADMINISTRATOR as "administrator",  <!-- 管理员姓名 -->
        a.ADMIN_INDTITY	as "adminIndtity",  <!-- 管理员身份证号 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.TENANT_ID as "tenantId" <!-- 租户ID -->
        FROM ${meliSchema}.tlirl0101 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0101 a WHERE 1=1
        <include refid="condition"/>
    </select>

<!--    新增司机判断管理员的身份信息是否存在-->
    <select id="countCustomerInfo" resultClass="int">
        SELECT COUNT(1) FROM ${meliSchema}.tlirl0101 a WHERE 1=1
        and
        SEG_NO=#segNo#
        <isNotEmpty prepend=" AND " property="customerId">
            a.CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            a.CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        and status='20'
    </select>



    <select id="queryDriverInfo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
        DISTINCT
        tlirl0101.SEG_NO as "segNo",
            tlirl0101.CUSTOMER_ID as "customerId"
        , tlirl0101.CUSTOMER_NAME as "customerName"
        , tlirl0102.DRIVER_NAME as "driverName"
        , tlirl0102.DRIVER_IDENTITY as "driverIdentity",
        tlirl0102.TEL as "driverTel",
        tlirl0102.UUID as "uuid"
        from ${meliSchema}.tlirl0101 tlirl0101,
        ${meliSchema}.tlirl0102 tlirl0102
        where tlirl0101.SEG_NO = tlirl0102.SEG_NO
        and tlirl0101.CUSTOMER_ID = tlirl0102.CUSTOMER_ID
        and tlirl0101.CUSTOMER_NAME = tlirl0102.CUSTOMER_NAME
        and tlirl0101.STATUS = '20'
        and tlirl0101.SEG_NO = #segNo#
        and tlirl0101.CUSTOMER_ID = #customerId#
        <isNotEmpty prepend="and " property="customerName">
            tlirl0101.CUSTOMER_NAME like concat('%',#customerName#,'%')
        </isNotEmpty>

        <isNotEmpty prepend="and " property="driverName">
            tlirl0102.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
<!--        and tlirl0101.CUSTOMER_NAME = #customerName#-->
        and tlirl0101.RESERVATION_IDENTITY = #reservationIdentity#
        and tlirl0101.DEL_FLAG = '0'
        and tlirl0102.DEL_FLAG = '0'
        and tlirl0102.STATUS = '20'
    </select>
    <!--
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reservationIdentity">
            RESERVATION_IDENTITY = #reservationIdentity#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            CUSTOMER_ID = #customerId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            CUSTOMER_NAME = #customerName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tel">
            TEL = #tel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="administrator">
            ADMINISTRATOR = #administrator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0101 (SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        UUID,  <!-- uuid -->
        STATUS,  <!-- 状态 -->
        RESERVATION_IDENTITY,  <!-- 预约身份(包含承运商和客户) -->
        CUSTOMER_ID,  <!-- 承运商/客户代码 -->
        CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
        TEL,  <!-- 管理员手机号 -->
        ADMINISTRATOR,  <!-- 管理员姓名 -->
        ADMIN_INDTITY,  <!-- 管理员身份证号 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK  <!-- 备注 -->
        )
        VALUES (#segNo#, #unitCode#, #uuid#, #status#, #reservationIdentity#, #customerId#, #customerName#, #tel#,
        #administrator#, #adminIndtity#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#,#delFlag#,
        #remark#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meliSchema}.tlirl0101 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${meliSchema}.tlirl0101
        SET
        STATUS = #status#,   <!-- 状态 -->
        RESERVATION_IDENTITY = #reservationIdentity#,   <!-- 预约身份(包含承运商和客户) -->
        CUSTOMER_ID = #customerId#,   <!-- 承运商/客户代码 -->
        CUSTOMER_NAME = #customerName#,   <!-- 承运商/客户名称 -->
        TEL = #tel#,   <!-- 管理员手机号 -->
        ADMINISTRATOR = #administrator#,   <!-- 管理员姓名 -->
        ADMIN_INDTITY	= #adminIndtity#,   <!-- 管理员身份证号 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#   <!-- 备注 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>