$(function () {
    // TODO 查询 按钮事件
    //获取当前登录人对应的业务单元
    var unitInfo =  IMOMUtil.fillUnitInfo();

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });



    $("#QUERY").on("click", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        var unitCode = $("#inqu_status-0-unitCode").val();
        if (IPLAT.isBlankString(segNo) || IPLAT.isBlankString(unitCode)) {
            e.preventDefault();
            NotificationUtil({msg: "请先选择业务单元代码!"}, "error");
            return false;
        }
        resultGrid.dataSource.page(1);
    });

    $("#SUB_QUERY").on("click", function (e) {
        sub_resultGrid.dataSource.page(1);
    });

    $("#SUB_QUERY2").on("click", function (e) {
        sub_result2Grid.dataSource.page(1);
    });
    $("#SUB_QUERY3").on("click", function (e) {
        sub_result3Grid.dataSource.page(1);
    });
    $("#SUB_QUERY4").on("click", function (e) {
        sub_result4Grid.dataSource.page(1);
    });
    $("#SUB_QUERY5").on("click", function (e) {
        sub_result5Grid.dataSource.page(1);
    });
    IPLATUI.EFTab = {
        "info": {
            select: function (e) {
                //隐藏存档按钮
                var tableId = e.contentElement.id;
                if (tableId == 'info-1') {
                    /*$("#inqu_status-0-agreementId").val("");
                    IPLAT.EFPopupInput.enable($("#inqu_status-0-unitCode"), true);
                    IPLAT.EFInput.readonly($("#inqu_status-0-agreementId"), false);
                    if_add = false;*/
                }
                if (tableId == 'info-2') {
                    var model = null;
                    if (resultGrid.getSelectedRows().length <= 0 && resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("操作失败，原因[请勾选一条记录再进行查看！]", "error");
                        e.preventDefault();
                        return false;
                    } else if (resultGrid.getCheckedRows().length > 1) {
                        NotificationUtil("操作失败，原因[只能查看一条数据！]", "error");
                        e.preventDefault(); //阻止切换第二个tab
                        return;
                    }
                    model = resultGrid.getCheckedRows()[0];
                    var segNo = model["segNo"];
                    var carTraceNo = model["carTraceNo"];
                    $("#inqu2_status-0-carTraceNo").val(carTraceNo);
                    $("#inqu2_status-0-segNo").val(segNo);
                    $("#inqu2_status-0-unitCode").val(segNo);
                    $("#inqu2_status-0-tab").val("2");

                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil({msg: "原因[请先选择主项业务单元代码！]"}, "error")
                        return;
                    }
                    var eiInfo = new EiInfo();
                    IPLAT.progress($("body"), false);
                    eiInfo.setByNode("inqu2");
                    eiInfo.addBlock(resultGrid.getCheckedBlockData());
                    EiCommunicator.send("LIRL0308", "queryLoadingPerformance", eiInfo, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                NotificationUtil(ei);
                            } else {
                                ei.setMsg("查询成功！");
                                NotificationUtil({msg: ei.msg}, "success");
                                result2Grid.setEiInfo(ei);
                            }
                            IPLAT.progress($("body"), false);
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            // NotificationUtil({msg: ei.msg}, "error");
                            NotificationUtil(ei);
                            return false;
                        }
                    }, {async: false});
                }
                if (tableId == 'info-3') {
                    var model = null;
                    if (resultGrid.getSelectedRows().length <= 0 && resultGrid.getCheckedRows().length <= 0) {
                        NotificationUtil("操作失败，原因[请勾选一条记录再进行查看！]", "error");
                        e.preventDefault();
                        return false;
                    } else if (resultGrid.getCheckedRows().length > 1) {
                        NotificationUtil("操作失败，原因[只能查看一条数据！]", "error");
                        e.preventDefault(); //阻止切换第二个tab
                        return;
                    }
                    model = resultGrid.getCheckedRows()[0];
                    var segNo = model["segNo"];
                    var carTraceNo = model["carTraceNo"];
                    $("#inqu2_status-0-carTraceNo").val(carTraceNo);
                    $("#inqu2_status-0-segNo").val(segNo);
                    $("#inqu2_status-0-unitCode").val(segNo);
                    $("#inqu2_status-0-tab").val("3");
                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil({msg: "原因[请先选择主项业务单元代码！]"}, "error")
                        return;
                    }
                    var eiInfo = new EiInfo();
                    IPLAT.progress($("body"), false);
                    eiInfo.setByNode("inqu2");
                    eiInfo.addBlock(resultGrid.getCheckedBlockData());
                    EiCommunicator.send("LIRL0308", "queryLoadingPerformance", eiInfo, {
                        onSuccess: function (ei) {
                            if ("-1" == ei.status) {
                                IPLAT.progress($("body"), false);
                                NotificationUtil(ei);
                                return false;

                            } else {
                                ei.setMsg("查询成功！");
                                NotificationUtil({msg: ei.msg}, "success");
                                IPLAT.progress($("body"), false);
                                result3Grid.setEiInfo(ei);
                                result4Grid.setEiInfo(ei);
                            }
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false);
                            // NotificationUtil({msg: ei.msg}, "error");
                            NotificationUtil(ei);
                            return false;
                        }
                    }, {async: false});
                }
            }
        }
    };

    IPLATUI.EFGrid = {
        "result": {
            "exportGrid": {
                //配置前端页面导出
                frontExportSettings: {
                    isShow: true,//是否显示
                    name: "前端导出",//名字
                    sort: 1//排序，数字小的，排前面
                },
                //配置后端服务导出
                afterExportSettings: {
                    isShow: true,
                    name: "后端导出",
                    sort: 2
                },
                /**
                 * 导出前的事件
                 *
                 * @param gridInstance kendoGrid对象
                 * @return {boolean} 是否执行导出的逻辑
                 */
                beforeExport: (gridInstance) => {
                    let segNo = IPLAT.EFInput.value($('#inqu_status-0-segNo'));
                    if (IPLAT.isBlankString(segNo)) {
                        IPLAT.alert("请选择业务单元进行查询操作");
                        return false;
                    }
                    return gridInstance.getDataItems().length > 0; // false不执行导出，true时执行导出
                },
                exportServiceName: "LIRL0220", // 配置导出服务名，默认和grid的serviceName相同
                exportMethodName: "postExport",    // 配置导出方法名，默认和grid的queryMethod相同
                exportFileName: (gridInstance) => {
                    // 导出的文件名包含时间戳 yyyyMMddHHmmss
                    return "result" + kendo.toString(new Date(), IPLAT.FORMAT.DATE_14);
                },
                exportMode: "after", //导出模式
                exportFileType: "xls",  // 默认值是xls
                exportBlockId: "result"  // 默认值和blockId相同，导出的EiInfo中的指定数据块
            },
            columns: [
                {
                    field: "signForAttachments",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "签收附件",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "签收附件"
                            })
                        }
                    },
                    template: function (e) {
                        return '<span style="color:blue" >查看</span>'
                    },
                },
                {
                    field: "signForAttachments3",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "安全告知签章附件",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW3",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "安全告知签章附件"
                            })
                        }
                    },
                    template: function (e) {
                        console.log(e);
                        return '<span style="color:blue" >查看</span>'
                    },
                },
                {
                    field: "totalOperationTimeMinutes",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "装卸点总时长（分钟）",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW6",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "装卸点总时长（分钟）"
                            })
                        }
                    },
                    template: function (e) {
                        let val = $("#inqu_status-0-unitCode").val();
                        if (val == "JC000000"){
                            return `<span style="color:blue" >${e.totalOperationTimeMinutes}</span>`
                        }else {
                            return '<span style="color:blue" >查看</span>'
                        }
                    },
                },
                {
                    field: "signForAttachments5",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "出厂照片",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW5",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "出厂照片"
                            })
                        }
                    },
                    template: function (e) {
                        return '<span style="color:blue" >查看</span>'
                    },
                },

                {
                    field: "putInQuatityAttachments",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "入库质量去确认书附件",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW7",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "入库质量去确认书附件"
                            })
                        }
                    },
                    template: function (e) {
                        return '<span style="color:blue" >查看</span>'
                    },
                },
            ],
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
                $("#EXPORTEXCEL").on("click",function () {
                    let segNo = $("#inqu_status-0-segNo").val();

                    if (IPLAT.isBlankString(segNo)) {
                        NotificationUtil("请在查询条件区域内选择相应的[业务单元]！", "error");
                        return;
                    }

                    var fileName = segNo+"厂内物流作业查询" + ".xlsx";

                    if(resultGrid.getDataItems().length > 0) {
                        let exportEi = new EiInfo();
                        exportEi.setByNode("inqu");
                        IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                        IMOMUtil.setExportSheetColumnBlock(exportEi, result2Grid);
                        IMOMUtil.setExportSheetColumnBlock(exportEi, result3Grid);
                        IMOMUtil.setExportSheetColumnBlock(exportEi, result4Grid);
                        exportEi.set("exportColumnBlock", 'fileName', fileName);
                        exportEi.set("exportColumnBlock", 'sheetName', "厂内物流作业");
                        exportEi.set("exportColumnBlockSheet"+result2Grid.options.blockId, 'sheetName', "装载实绩");
                        exportEi.set("exportColumnBlockSheet"+result3Grid.options.blockId, 'sheetName', "行车工出库校验");
                        exportEi.set("exportColumnBlockSheet"+result4Grid.options.blockId, 'sheetName', "仓库出库校验");
                        IMOMUtil.callService({
                            service: "LIRL0220",
                            method: "postExport",
                            eiInfo: exportEi,
                            showProgress: true,
                            async: true,
                            callback: function (ei) {
                                if (ei.status > -1) {
                                    let docUrl = ei.getBlock("excelDoc").get("docUrl");
                                    window.open(docUrl);
                                }
                            }
                        });
                    }
                });


                // 导出按钮点击事件
/*                $("#EXPORTEXCEL").on("click", function () {
                    // 检查是否有选中的主表数据
                    if (resultGrid.getCheckedRows().length === 0) {
                        NotificationUtil("请至少选择一条车次记录进行导出", "warning");
                        return;
                    }

                    let segNo = $("#inqu_status-0-segNo").val();
                    // 准备导出请求
                    var exportEi = new EiInfo();
                    exportEi.setByNode("inqu");

                    // 添加选中的主表车次号列表
                    var checkedRows = resultGrid.getCheckedRows();
                    var carTraceNos = [];
                    for (var i = 0; i < checkedRows.length; i++) {
                        carTraceNos.push(checkedRows[i].carTraceNo);
                    }
                    var fileName = segNo+"厂内物流作业查询" + ".xlsx";
                    exportEi.set("carTraceNos", carTraceNos);
                    IMOMUtil.setExportColumnBlock(exportEi, resultGrid);
                    exportEi.set("exportColumnBlock", 'fileName', fileName);
                    exportEi.set("exportColumnBlock", 'masterKey', carTraceNos);
                    exportEi.set("exportColumnBlock", 'detailKey', carTraceNos);

                    // 调用导出服务
                    IPLAT.progress($("body"), true); // 显示进度条
                    EiCommunicator.send("LIRL0220", "exportLogisticsDetail", exportEi, {
                        onSuccess: function (ei) {
                            IPLAT.progress($("body"), false); // 隐藏进度条
                            if (ei.status > -1) {
                                var docUrl = ei.getBlock("excelDoc").get("docUrl");
                                window.open(docUrl);
                            } else {
                                NotificationUtil(ei.msg, "error");
                            }
                        },
                        onFail: function (ei) {
                            IPLAT.progress($("body"), false); // 隐藏进度条
                            NotificationUtil(ei.msg, "error");
                        }
                    });
                });*/

                $("#PRINT_QUALITY").on("click",function () {
                    // var eiInfo = new EiInfo();
                    // eiInfo.set("block", "result");
                    // IMOMUtil.submitGridsData("result", "LIRL0220", "printPutinQualityConfirm", true, function (e) {
                    //     resultGrid.setEiInfo(e)
                    // }, eiInfo);
                    var checkedRows = resultGrid.getCheckedRows();
                    if (checkedRows.length > 0){
                        for (let i = 0; i < checkedRows.length; i++) {
                            var segNo = checkedRows[i].segNo;
                            var allocateVehicleNo = checkedRows[i].allocateVehicleNo;
                            if (allocateVehicleNo==' ' || allocateVehicleNo==null|| allocateVehicleNo==undefined){
                                IPLAT.NotificationUtil("当前车辆没有配单信息,无法打印!","error");
                                return false;
                            }
                            var reportAddress =  IMOMUtil.getReportAddress();
                            var reportId = "VGDM/VGDMR001";
                            var url = reportAddress + reportId + ".cpt&allocateVehicleNo=" + allocateVehicleNo + "&segNo=" + segNo+"&format=PDF";
                            window.open(url);
                        }
                    }else {
                        IPLAT.NotificationUtil("未勾选信息,无法打印!","error");
                        return false;
                    }
                });
                /**
                 * 发送短息验证码
                 */
                $("#QUERY_SEND_CODE").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0220", "querySendCode", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });

                /**
                 * 运输计划返单
                 */
                $("#UPDATE_BACK").click(function () {
                    var eiInfo = new EiInfo();
                    eiInfo.set("block", "result");
                    IMOMUtil.submitGridsData("result", "LIRL0220", "createTransportPlan", true, function (e) {
                        resultGrid.setEiInfo(e)
                    }, eiInfo);
                });
            }
        },
        "sub_result": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },
        "sub_result2": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },
        "sub_result3": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },
        "sub_result4": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },

        "sub_result5": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },
        "sub_result7": {
            columns: [
                {
                    field: "uploadFilePath",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
        },
        "result2": {
            columns: [
                {
                    field: "signForAttachments2",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "签收附件",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW2",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "签收附件"
                            })
                        }
                    },
                    template: function (e) {
                        return '<span style="color:blue" >查看</span>'
                    },
                },
                {
                    field: "signForAttachments4",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "安全告知签章附件",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "FILEWINDOW4",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "安全告知签章附件"
                            })
                        }
                    },
                    template: function (e) {
                        return '<span style="color:blue" >查看</span>'
                    },
                },
            ],
            /**
             * 数据ajax提交前的回调。
             * @param e
             * e.sender     kendoGrid对象，resultGrid
             * e.type       操作类型 create read update delete
             */
            beforeRequest: function (e) {
                var model = null;
                if (resultGrid.getSelectedRows().length <= 0 && resultGrid.getCheckedRows().length <= 0) {
                    NotificationUtil("操作失败，原因[请勾选一条记录再进行查看！]", "error");
                    e.preventDefault();
                    return false;
                } else if (resultGrid.getCheckedRows().length > 1) {
                    NotificationUtil("操作失败，原因[只能查看一条数据！]", "error");
                    e.preventDefault(); //阻止切换第二个tab
                    return;
                }
                model = resultGrid.getCheckedRows()[0];
                var segNo = model["segNo"];
                var carTraceNo = model["carTraceNo"];
                $("#inqu2_status-0-carTraceNo").val(carTraceNo);
                $("#inqu2_status-0-segNo").val(segNo);
                $("#inqu2_status-0-unitCode").val(segNo);
            }
        },
        // "result3": {
        //     /**
        //      * 数据ajax提交前的回调。
        //      * @param e
        //      * e.sender     kendoGrid对象，resultGrid
        //      * e.type       操作类型 create read update delete
        //      */
        //     beforeRequest: function (e) {
        //         var model = null;
        //         if (resultGrid.getSelectedRows().length <= 0 && resultGrid.getCheckedRows().length <= 0) {
        //             NotificationUtil("操作失败，原因[请勾选一条记录再进行查看！]", "error");
        //             e.preventDefault();
        //             return false;
        //         } else if (resultGrid.getCheckedRows().length > 1) {
        //             NotificationUtil("操作失败，原因[只能查看一条数据！]", "error");
        //             e.preventDefault(); //阻止切换第二个tab
        //             return;
        //         }
        //         e.preventDefault();
        //         model = resultGrid.getCheckedRows()[0];
        //         var segNo = model["segNo"];
        //         var carTraceNo = model["carTraceNo"];
        //         $("#inqu2_status-0-carTraceNo").val(carTraceNo);
        //         $("#inqu2_status-0-segNo").val(segNo);
        //         $("#inqu2_status-0-unitCode").val(segNo);
        //         $("#inqu2_status-0-tab").val(3);
        //
        //         var eiInfo = new EiInfo();
        //         IPLAT.progress($("body"), false);
        //         eiInfo.setByNode("inqu2");
        //         eiInfo.addBlock(resultGrid.getCheckedBlockData());
        //         EiCommunicator.send("LIRL0308", "queryLoadingPerformance", eiInfo, {
        //             onSuccess: function (ei) {
        //                 if ("-1" == ei.status) {
        //                     IPLAT.progress($("body"), false);
        //                     NotificationUtil(ei);
        //                     return false;
        //
        //                 } else {
        //                     ei.setMsg("查询成功！");
        //                     NotificationUtil({msg: ei.msg}, "success");
        //                     IPLAT.progress($("body"), false);
        //                     result3Grid.setEiInfo(ei);
        //                 }
        //             },
        //             onFail: function (ei) {
        //                 IPLAT.progress($("body"), false);
        //                 // NotificationUtil({msg: ei.msg}, "error");
        //                 NotificationUtil(ei);
        //                 return false;
        //             }
        //         }, {async: false});
        //     }
        // }
        "result4": {
            columns: [
                {
                    field: "weight",
                    valueType: "N",//小计设置
                    type: "N"
                },
                {
                    field: "quantity",
                    valueType: "N",//小计设置
                    type: "N"
                }
            ],
        }
    };
    IPLATUI.EFPopupInput = {
        "inqu_status-0-customerId": {
            clearInput: function (e) {
                $("#inqu_status-0-customerId").val('');
                $("#inqu_status-0-customerName").val('');
            }
        }
    };
    IPLATUI.EFWindow = {
        "unitInfo": {
            open: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                /*iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
                iframejQuery("#inqu_status-0-segName").val($("#inqu_status-0-segName").val());*/
                iframejQuery("#inqu_status-0-windowId").val($("unitInfo"));
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = unitInfoWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_result2").data("kendoGrid");

                // 也可以使用如下的方式获取dataGrid
                var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();
                if (row.length > 0) {
                    unitInfo = row[0];
                    $("#inqu_status-0-unitCode").val(row[0].unitCode);
                    $("#inqu_status-0-segNo").val(row[0].segNo);
                    $("#inqu_status-0-segName").val(row[0].segName);
                }
            }
        },
        "userNum": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNumWindow.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId").val(row[0].userNum);
                    $("#inqu_status-0-customerName").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "userNum2": {
            // 打开窗口事件
            open: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;
                // 把EFWindow的id传入到子窗口input框中
                iframejQuery("#sub_query_status-0-windowId").val("userNum2");
                var segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请先选择系统账套！"}, "error");
                    return;
                }
                iframejQuery("#sub_query_status-0-segNo").val(segNo);
                iframejQuery("#sub_query_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
                iframejQuery("#sub_query_status-0-segName").val($("#inqu_status-0-segName").val());
            },
            // 关闭窗口事件
            close: function (e) {
                var $iframe = userNum2Window.element.children("iframe");
                // 子窗口中的jQuery对象
                var iframejQuery = $iframe[0].contentWindow.$;

                // $iframe[0].contentWindow.resultGrid
                var dataGrid = iframejQuery("#ef_grid_sub_result").data("kendoGrid");
                //
                // // 也可以使用如下的方式获取dataGrid
                // var dataGrid = iframejQuery.data($iframe.contents().find("#ef_grid_result2")[0], "kendoGrid");

                var row = dataGrid.getCheckedRows();

                if (row.length > 0) {

                    $("#inqu_status-0-customerId2").val(row[0].userNum);
                    $("#inqu_status-0-customerName2").val(row[0].chineseUserName);

                }
                // 清空弹出框内容
                if (dataGrid.getDataItems().length > 0) {
                    dataGrid.removeRows(dataGrid.getDataItems());
                }
            }
        },
        "FILEWINDOW": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_resultGrid.getDataItems().length > 0) {
                    sub_resultGrid.removeRows(sub_resultGrid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let carTraceNo = varModel.carTraceNo;
                    if (IPLAT.isBlankString(carTraceNo)) {
                        NotificationUtil({msg: "车辆跟踪号为空！"}, "error");
                        return;
                    }
                    $("#sub_query_status-0-relevanceId").val(varModel.carTraceNo);
                    $("#sub_query_status-0-segNo").val(varModel.segNo);
                }
                sub_resultGrid.dataSource.page(1);
            }
        },
        "FILEWINDOW2": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result2Grid.getDataItems().length > 0) {
                    sub_result2Grid.removeRows(sub_result2Grid.getDataItems());
                }
                var checkRows = result2Grid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = result2Grid.getCheckedRows()[i];
                    let packId = varModel.packId;
                    if (IPLAT.isBlankString(packId)) {
                        NotificationUtil({msg: "捆包号为空！"}, "error");
                        return;
                    }
                    $("#sub2_query_status-0-packId").val(varModel.packId);
                    $("#sub2_query_status-0-segNo").val(varModel.segNo);
                }
                sub_result2Grid.dataSource.page(1);
            }
        },
        "FILEWINDOW3": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result3Grid.getDataItems().length > 0) {
                    sub_result3Grid.removeRows(sub_result3Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let carTraceNo = varModel.carTraceNo;
                    if (IPLAT.isBlankString(carTraceNo)) {
                        NotificationUtil({msg: "车辆跟踪号为空！"}, "error");
                        return;
                    }
                    $("#sub3_query_status-0-relevanceId").val(varModel.carTraceNo);
                    $("#sub3_query_status-0-driverName").val(varModel.driverName);
                    $("#sub3_query_status-0-driverTel").val(varModel.telNum);
                    $("#sub3_query_status-0-driverIdentity").val(varModel.idCard);
                    $("#sub3_query_status-0-segNo").val(varModel.segNo);

                }
                sub_result3Grid.dataSource.page(1);
            }
        },
        "FILEWINDOW4": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result4Grid.getDataItems().length > 0) {
                    sub_result4Grid.removeRows(sub_result4Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let carTraceNo = varModel.carTraceNo;
                    if (IPLAT.isBlankString(carTraceNo)) {
                        NotificationUtil({msg: "车辆跟踪号为空！"}, "error");
                        return;
                    }
                    $("#sub4_query_status-0-relevanceId").val(carTraceNo);
                    $("#sub4_query_status-0-driverName").val(varModel.driverName);
                    $("#sub4_query_status-0-driverTel").val(varModel.telNum);
                    $("#sub4_query_status-0-driverIdentity").val(varModel.idCard);
                    $("#sub4_query_status-0-segNo").val(varModel.segNo);
                }
                sub_result4Grid.dataSource.page(1);
            }
        },

        "FILEWINDOW5": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result5Grid.getDataItems().length > 0) {
                    sub_result5Grid.removeRows(sub_result5Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let carTraceNo = varModel.carTraceNo;
                    if (IPLAT.isBlankString(carTraceNo)) {
                        NotificationUtil({msg: "车辆跟踪号为空！"}, "error");
                        return;
                    }
                    $("#sub5_query_status-0-carTraceNo").val(carTraceNo);
                    $("#sub5_query_status-0-vehicleNo").val(varModel.vehicleNo);
                    $("#sub5_query_status-0-segNo").val(varModel.segNo);
                }
                sub_result5Grid.dataSource.page(1);
            }
        },
        "FILEWINDOW6": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result6Grid.getDataItems().length > 0) {
                    sub_result6Grid.removeRows(sub_result6Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let carTraceNo = varModel.carTraceNo;
                    if (IPLAT.isBlankString(carTraceNo)) {
                        NotificationUtil({msg: "车辆跟踪号为空！"}, "error");
                        return;
                    }
                    $("#sub6_query_status-0-carTraceNo").val(varModel.carTraceNo);
                    $("#sub6_query_status-0-vehicleNo").val(varModel.carTraceNo);
                    $("#sub6_query_status-0-segNo").val(varModel.segNo);
                }
                sub_result6Grid.dataSource.page(1);
            }
        },
        "FILEWINDOW7": {
            // 打开窗口事件
            open: function (e) {
                // 清空弹出框内容
                if (sub_result7Grid.getDataItems().length > 0) {
                    sub_result7Grid.removeRows(sub_result7Grid.getDataItems());
                }
                var checkRows = resultGrid.getCheckedRows();
                for (i = 0; i < checkRows.length; i++) {
                    var varModel = resultGrid.getCheckedRows()[i];
                    let carTraceNo = varModel.carTraceNo;
                    if (IPLAT.isBlankString(carTraceNo)) {
                        NotificationUtil({msg: "车辆跟踪号为空！"}, "error");
                        return;
                    }
                    $("#sub7_query_status-0-carTraceNo").val(carTraceNo);
                    $("#sub7_query_status-0-vehicleNo").val(varModel.vehicleNo);
                    $("#sub7_query_status-0-segNo").val(varModel.segNo);
                }
                sub_result7Grid.dataSource.page(1);
            }
        },
    }

});
