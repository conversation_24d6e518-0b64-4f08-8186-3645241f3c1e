$(function () {
    var isAdmin = false;
    $(window).load(function () {
        setButtonDisable(true);
        //resultGrid.setEiInfo(__eiInfo);
        isAdmin = "admin" == __eiInfo.get("isAdmin");
    });

    $("#QUERY").on("click", function (e) {
        var orgId = $("#inqu_status-0-orgId").val();
        if (orgId == "" || orgId == null||"root" == orgId) {
            IPLAT.alert("查询前需要选择组织机构!");
        } else {
            resultGrid.dataSource.page(1);
        }
    });

    IPLATUI.EFTree = {
        "categoryTree": {
            select: function (e) {
                var _data = this.dataItem(e.node);
                var labelValue = _data.label;
                var textValue = _data.text;
                $("#inqu_status-0-orgId").val(labelValue);
                $("#inqu_status-0-orgCname").val(textValue);
                $("#inqu_status-0-orgType").val(_data.type);
                resultGrid.dataSource.page(1);
                var needDisable = false;
                if(isAdmin){
                    needDisable = _data.level()<1;
                }else{
                    needDisable = _data.level()<2;
                }
                if(needDisable){
                    setButtonDisable(true);
                }else{
                    setButtonDisable(false);
                }
            }
        }
    };

    var setButtonDisable = function(b){
        $(".k-grid-add").attr("disabled", b);
        $(".k-grid-delete").attr("disabled", b);
    }

    $("#QUERYUSER").on("click", function (e) {
        resultBGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            dataBinding: function (e) {
            },
            beforeAdd: function (e) {
                e.preventDefault();
                var orgId = $("#inqu_status-0-orgId").val();
                var orgCnameValue = $("#inqu_status-0-orgCname").val();
                var orgType = $("#inqu_status-0-orgType").val();
                if (orgId == "" || orgId == null || orgType == "node") {
                    IPLAT.alert("请选择一个组织机构!");
                } else {
                    if (orgCnameValue == "组织机构") {
                        IPLAT.alert("请选择一个组织机构!");
                    } else {
                        // $("#ef_grid_resultB").data("kendoGrid").dataSource.page(1);
                        for(var i = 0; i< window.$("iframe").length;i++){
                            if(i !== e._index){
                                window.$("iframe")[i].contentWindow.location.reload();
                            }
                        }
                        // 设置弹出窗口的宽高
                        $("#insertUser").data("kendoWindow").setOptions({
                            width: 1036,
                            height: 560
                        });

                        $("#insertUser").data("kendoWindow").open().center();
                        $("#insertUser_wnd_title").css({"text-align": "justify", "font-size": "14px", "color": "#FFF"});
                        $("#insertUser_wnd_title").html("正在为: [" + orgCnameValue + "] 添加成员用户");
                    }
                }
            }
        },
    };

    var dataSource = new kendo.data.DataSource({
        transport: {
            read: {
                url: IPLATUI.CONTEXT_PATH + "/service/XSLV0200/search",
                type: 'POST',
                dataType: "json",
                contentType: "application/json"
            },
            parameterMap: function (options, operation) {
                var info = new EiInfo();
                info.set("inqu_status-0-orgCname", $("#filterText").val());
                return info.toJSONString();
            }
        },
        schema: {
            data: function (response) {
                ajaxEi = EiInfo.parseJSONObject(response);
                return ajaxEi.getBlock("result").getMappedRows();
            }
        },
        pageSize: 10,
        serverFiltering: true
    });



    $("#filterText").kendoAutoComplete({
        dataSource: dataSource,
        minLength: 1,
        template: '#:orgEname#-#:text#',
        enforceMinLength: true,
        select: function (e) {
            var _data = e.dataItem || {};
            var tree = $('#categoryTree').data('kendoTreeView');
            expandtree(tree, _data);
        },
        dataTextField: "text"
    });

    /**
     * 展开树
     */
    var expandtree = function (tree, data) {
        if (!data) {
            return;
        }
        var info = new EiInfo();
        info.set("inqu_status-0-orgId", data.label);
        // 查找点击分类的树路径
        EiCommunicator.send("XSLV0100", "expandPath", info, {
            // 服务调用成功后的回调函数 onSuccess
            onSuccess: function (eiInfo) {
                var datas = eiInfo.getBlock('result').getMappedRows();
                var path = [];
                for (var item = 0; item < datas.length; item++) {
                    path.push(item.LABEL);
                }
                path = path.reverse();
                path.splice(1, 0, "root1");
                var treeOrgId = path[path.length - 1];
                // 根据树路径逐级展开分类树
                tree.expandPath(path, function () {
                    // 展开成功后选中对应的树节点
                    var barDataItem = tree.dataSource.get(treeOrgId);
                    var barElement = tree.findByUid(barDataItem.uid);
                    tree.select(barElement);

                    var orgCname = barDataItem.text;

                    $("#inqu_status-0-orgId").val(treeOrgId);
                    $("#inqu_status-0-orgCname").val(orgCname);
                    resultGrid.dataSource.page(1);
                });
            },
            // 服务调用失败后的回调函数 onFail
            // errorMsg 是平台格式化后的异常消息， status， e的含义和$.ajax的含义相同，请参考jQuery文档
            onFail: function (errorMsg, status, e) {
                // 调用发生异常
                NotificationUtil(errorMsg, "error");
            }
        });
    };

});
var addResInfo =  function(postInfo){
    permissionControl(postInfo);
    postInfo.set('addResService',"XSLV02");
    postInfo.set('addResMethod',"insert");
};

var addResource = function (eiInfo) {
    var block = eiInfo.getBlock("result");
    block.getBlockMeta().addMeta(new EiColumn("orgId"));
    var rows = block.getRows();
    for (var i = 0; i < rows.length; i++) {
        block.setCell(i, "orgId", $("#inqu_status-0-orgId").val());
    }
    EiCommunicator.send("XSLV02", "insert", eiInfo, {
        onSuccess: function (ei) {
            IPLAT.alert({
                message: ei.getMsg(),
                okFn: function (e) {
                    if (ei.getStatus() > -1) {
                        refreshAuthCallback();
                    }
                },
                title: '提示信息'
            });
        }, onFail: function (ei) {
            IPLAT.alert(ei.getMsg());

        }
    });
};

var permissionControl = function(postInfo){
    var block = postInfo.getBlock("inqu_status");
    block.getBlockMeta().addMeta(new EiColumn("orgId"));
    block.getBlockMeta().addMeta(new EiColumn("currentOrgId"));
    block.getBlockMeta().addMeta(new EiColumn("permissionType"));
    postInfo.set('inqu_status-0-orgId', $("#inqu_status-0-orgId").val());
    postInfo.set('inqu_status-0-currentOrgId', $("#inqu_status-0-orgId").val());
    postInfo.set('inqu_status-0-permissionType', "orgPermResRange");
};

var refreshAuthCallback = function () {
    resultGrid.dataSource.page(1);
    $("#insertUser").data("kendoWindow").close();
};