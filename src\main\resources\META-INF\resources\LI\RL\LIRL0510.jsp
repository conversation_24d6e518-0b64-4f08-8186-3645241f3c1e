<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-allocateVehicleNo" cname="配车单号" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-vehicleNo" cname="车牌号" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-packId" cname="捆包号" colWidth="3" placeholder="模糊条件"/>
            <EF:EFPopupInput ename="inqu_status-0-warehouseCode" cname="仓库代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="warehouseInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-warehouseName"
                             popupTitle="仓库查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-warehouseName" cname="仓库名称" colWidth="3" disabled="true"/>

            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P006"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-customerId" cname="客户代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-customerName"
                             containerId="userNum" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true"
                             popupTitle="客户代码查询">
            </EF:EFPopupInput>
            <EF:EFInput type="text" ename="inqu_status-0-customerName" cname="客户代码名称" colWidth="3"
                        ratio="4:8" disabled="true"/>
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd"
                           startCname="起始创建时间" endCname="截止创建时间"
                           parseFormats="['yyyyMMddHHmm']"
                           colWidth="3" ratio="3:3" readonly="true"
                           format="yyyyMMddHHmm" role="datetime" interval="15"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-voucherNum" cname="依据凭单" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-carTraceNo" cname="车辆管理号" colWidth="3" placeholder="模糊条件"/>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="配车单信息">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true"
                       checkMode="single,row">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="vehicleNo" cname="车牌号" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="allocateVehicleNo" cname="配车单号" align="center" width="150"
                             primaryKey="true" enable="false"/>
                <EF:EFColumn ename="customerId" cname="配车单位代码" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="customerName" cname="配车单位名称" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="allocType" cname="配单类型" align="center" width="150" enable="false">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P007"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="status" cname="配单状态" align="center" width="150" enable="false">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P006"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="carTraceNo" cname="车辆管理号" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="showFlag" cname="展示标记" align="center" width="150" enable="false">
                    <EF:EFOption label="" value=""/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="estimatedTimeOfArrival" cname="预计到达时间" align="center" width="200"
                             enable="false"/>
                <EF:EFColumn ename="allocMes" cname="配单结果" align="left" width="200" enable="false"/>
                <EF:EFColumn ename="proformaVoucherNum" cname="形式提单无实物提单号" align="center" width="200"
                             enable="false"/>
                <EF:EFColumn ename="emergencyDeliveryTime" cname="紧急发货时间" align="center" width="200"
                             enable="false"/>
                <EF:EFColumn ename="ladingBillRemark" cname="形式提单备注" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="nextAlcVehicleNo" cname="下级配单号" align="left" width="200" enable="false"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>

    <EF:EFRegion id="subResult" title="配车单明细">
        <EF:EFGrid isFloat="true" id="subResult" blockId="subResult" autoBind="false" autoDraw="no" needAuth="true"
                   queryMethod="querySubResult">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                         required="true"
                         enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
            <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                         enable="false" hidden="true"/>
            <EF:EFColumn ename="allocateVehicleNo" cname="配车单号" align="center" width="150"
                         primaryKey="true" enable="false"/>
            <EF:EFColumn ename="allocVehicleSeq" cname="配车单子项序号" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="voucherNum" cname="(卸货:入库计划;装货:提单号)" align="center" width="200"
                         enable="false"/>
            <EF:EFColumn ename="packId" cname="捆包号" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="targetHandPointId" cname="目标装卸点代码" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="targetHandPointName" cname="目标装卸点名称" align="center" width="200" enable="false"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                <EF:EFOption label="" value=""/>
                <EF:EFCodeOption codeName="P006"/>
                <EF:EFOption label="生效" value="30"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="outPackFlag" cname="自带货标记(0:非自带货;1:自带货)" align="center" width="150"
                              enable="false">
                <EF:EFOption label="" value=""/>
                <EF:EFOption label="非自带货" value="0"/>
                <EF:EFOption label="自带货" value="1"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="warehouseName" cname="仓库名称" align="left" width="200" enable="false"/>
            <EF:EFComboColumn ename="putinType" cname="入库类型" align="center" width="150" enable="false">
                <EF:EFOption label="" value=""/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="innerDiameter" cname="内径" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="prodDensity" cname="密度" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="productProcessId" cname="首道加工工序" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="netWeight" cname="重量" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="specsDesc" cname="规格" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="customerId" cname="客户代码" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="customerName" cname="客户名称" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="factoryOrderNum" cname="钢厂订单号" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="surfaceGrade" cname="表面等级" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="piceNum" cname="张数" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="factoryArea" cname="厂区代码" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="prodTypeId" cname="品种附属码" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="tradeCode" cname="贸易方式" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="custPartId" cname="客户零件号" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="custPartName" cname="客户零件名称" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="mPackId" cname="母捆包号" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="purOrderNum" cname="销售订单号" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="ladingBillRemark" cname="提单备注" align="left" width="200" enable="false"/>
            <EF:EFComboColumn ename="billingMethod" cname="提单类型" align="center" width="150" enable="false">
                <EF:EFOption label="" value=""/>
                <EF:EFOption label="普通" value="10"/>
                <EF:EFOption label="形式" value="20"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="firstMachineCode" cname="首道机组" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
            <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                         enable="false"/>
            <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
            <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
            <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                         enable="false"/>
            <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                         enable="true" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>

    <%--仓库代码弹窗    --%>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseInfo" width="90%" height="60%"/>
    <%--客户代码弹窗    --%>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"/>

</EF:EFPage>
