<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-20 10:03:11
   		Version :  1.0
		tableName :iplat4j.tvzbm8101 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 ID  VARCHAR   NOT NULL   primarykey, 
		 RULE_ID  VARCHAR   NOT NULL, 
		 SORT_ID  VARCHAR   NOT NULL, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 SEG_FULL_NAME  VARCHAR   NOT NULL, 
		 SEG_NAME  VARCHAR   NOT NULL, 
		 ANOTHER_NAME  VARCHAR   NOT NULL, 
		 ATTRIBUTE_1  VARCHAR   NOT NULL, 
		 ATTRIBUTE_2  VARCHAR   NOT NULL, 
		 ATTRIBUTE_3  VARCHAR   NOT NULL, 
		 ATTRIBUTE_4  VARCHAR   NOT NULL, 
		 ATTRIBUTE_5  VARCHAR   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SEG_RULE_STATUS  VARCHAR   NOT NULL, 
		 ATTRIBUTE_6  VARCHAR   NOT NULL
	-->
<sqlMap namespace="tvzbm8101">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.xt.domain.Tvzbm8101">
		SELECT
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 记录归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记(默认0 删除1) -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				ID	as "id",  <!-- UUID -->
				RULE_ID	as "ruleId",  <!-- 业务类型编号 -->
				SORT_ID	as "sortId",  <!-- 排序号 -->
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
				SEG_FULL_NAME	as "segFullName",  <!-- 业务单元名称 -->
				SEG_NAME	as "segName",  <!-- 业务单元简称 -->
				ANOTHER_NAME	as "anotherName",  <!-- 别名 -->
				ATTRIBUTE_1	as "attribute1",  <!-- 附加属性1 -->
				ATTRIBUTE_2	as "attribute2",  <!-- 附加属性2 -->
				ATTRIBUTE_3	as "attribute3",  <!-- 附加属性3 -->
				ATTRIBUTE_4	as "attribute4",  <!-- 附加属性4 -->
				ATTRIBUTE_5	as "attribute5",  <!-- 附加属性5 -->
				REMARK	as "remark",  <!-- 备注 -->
				SEG_RULE_STATUS	as "segRuleStatus",  <!-- 状态 00-作废，20有效 -->
				ATTRIBUTE_6	as "attribute6" <!-- 附加属性6 -->
		FROM iplat4j.tvzbm8101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  ID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM iplat4j.tvzbm8101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ruleId">
			RULE_ID = #ruleId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sortId">
			SORT_ID = #sortId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segFullName">
			SEG_FULL_NAME = #segFullName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segName">
			SEG_NAME = #segName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="anotherName">
			ANOTHER_NAME = #anotherName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute1">
			ATTRIBUTE_1 = #attribute1#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute2">
			ATTRIBUTE_2 = #attribute2#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute3">
			ATTRIBUTE_3 = #attribute3#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute4">
			ATTRIBUTE_4 = #attribute4#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute5">
			ATTRIBUTE_5 = #attribute5#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segRuleStatus">
			SEG_RULE_STATUS = #segRuleStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute6">
			ATTRIBUTE_6 = #attribute6#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO iplat4j.tvzbm8101 (REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 记录归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记(默认0 删除1) -->
										TENANT_USER,  <!-- 租户 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										ID,  <!-- UUID -->
										RULE_ID,  <!-- 业务类型编号 -->
										SORT_ID,  <!-- 排序号 -->
										SEG_NO,  <!-- 业务单元代码 -->
										SEG_FULL_NAME,  <!-- 业务单元名称 -->
										SEG_NAME,  <!-- 业务单元简称 -->
										ANOTHER_NAME,  <!-- 别名 -->
										ATTRIBUTE_1,  <!-- 附加属性1 -->
										ATTRIBUTE_2,  <!-- 附加属性2 -->
										ATTRIBUTE_3,  <!-- 附加属性3 -->
										ATTRIBUTE_4,  <!-- 附加属性4 -->
										ATTRIBUTE_5,  <!-- 附加属性5 -->
										REMARK,  <!-- 备注 -->
										SEG_RULE_STATUS,  <!-- 状态 00-作废，20有效 -->
										ATTRIBUTE_6  <!-- 附加属性6 -->
										)		 
	    VALUES (#recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #tenantUser#, #unitCode#, #id#, #ruleId#, #sortId#, #segNo#, #segFullName#, #segName#, #anotherName#, #attribute1#, #attribute2#, #attribute3#, #attribute4#, #attribute5#, #remark#, #segRuleStatus#, #attribute6#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM iplat4j.tvzbm8101 WHERE 
			ID = #id#
	</delete>

	<update id="update">
		UPDATE iplat4j.tvzbm8101 
		SET 
		REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 记录归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记(默认0 删除1) -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
								RULE_ID	= #ruleId#,   <!-- 业务类型编号 -->  
					SORT_ID	= #sortId#,   <!-- 排序号 -->  
					SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					SEG_FULL_NAME	= #segFullName#,   <!-- 业务单元名称 -->  
					SEG_NAME	= #segName#,   <!-- 业务单元简称 -->  
					ANOTHER_NAME	= #anotherName#,   <!-- 别名 -->  
					ATTRIBUTE_1	= #attribute1#,   <!-- 附加属性1 -->  
					ATTRIBUTE_2	= #attribute2#,   <!-- 附加属性2 -->  
					ATTRIBUTE_3	= #attribute3#,   <!-- 附加属性3 -->  
					ATTRIBUTE_4	= #attribute4#,   <!-- 附加属性4 -->  
					ATTRIBUTE_5	= #attribute5#,   <!-- 附加属性5 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SEG_RULE_STATUS	= #segRuleStatus#,   <!-- 状态 00-作废，20有效 -->  
					ATTRIBUTE_6	= #attribute6#  <!-- 附加属性6 -->  
			WHERE 	
			ID = #id#
	</update>
  
</sqlMap>