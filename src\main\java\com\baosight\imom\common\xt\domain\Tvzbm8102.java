/**
* Generate time : 2024-08-14 14:15:02
* Version : 1.0
*/
package com.baosight.imom.common.xt.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tvzbm8102
* 
*/
public class Tvzbm8102 extends DaoEPBase {

                private String roleId = " ";		/* 角色代码*/
                private String sortId = " ";		/* 排序号*/
                private String segNo = " ";		/* 业务单元代码*/
                private String teleNum = " ";		/* 手机号*/
                private String e_mail = " ";		/* 邮箱*/
                private String id = " ";		/* UUID*/
                private String ruleId = " ";		/* 业务类型编号*/
                private String empNo = " ";		/* 工号*/
                private String name = " ";		/* 姓名*/
                private String additiveFlag = " ";		/* 附加标记*/
                private String segRoleStatus = " ";		/* 员工状态 00-作废，20有效*/
                private String additiveType = " ";		/* 员工附加类型,10正常,20兼职,21挂职*/
                private String attribute1 = " ";		/* 附加属性1*/
                private String attribute2 = " ";		/* 附加属性2*/
                private String attribute3 = " ";		/* 附加属性3*/
                private String attribute4 = " ";		/* 附加属性4*/
                private String attribute5 = " ";		/* 附加属性5*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 记录归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记(默认0 删除1)*/
                private String tenantUser = " ";		/* 租户*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String segFullName = " ";		/* 业务单元名称*/
                private String segName = " ";		/* 业务单元简称*/
                private String remark = " ";		/* 备注*/
                private String uploadPicture = " ";		/* 头像地址*/
                private String defaltSegNo = " ";		/* 默认账套*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("roleId");
        eiColumn.setDescName("角色代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortId");
        eiColumn.setDescName("排序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teleNum");
        eiColumn.setDescName("手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("e_mail");
        eiColumn.setDescName("邮箱");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("id");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("UUID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ruleId");
        eiColumn.setDescName("业务类型编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("empNo");
        eiColumn.setDescName("工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("name");
        eiColumn.setDescName("姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("additiveFlag");
        eiColumn.setDescName("附加标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segRoleStatus");
        eiColumn.setDescName("员工状态 00-作废，20有效");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("additiveType");
        eiColumn.setDescName("员工附加类型,10正常,20兼职,21挂职");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attribute1");
        eiColumn.setDescName("附加属性1");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attribute2");
        eiColumn.setDescName("附加属性2");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attribute3");
        eiColumn.setDescName("附加属性3");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attribute4");
        eiColumn.setDescName("附加属性4");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("attribute5");
        eiColumn.setDescName("附加属性5");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("记录归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记(默认0 删除1)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segFullName");
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uploadPicture");
        eiColumn.setDescName("头像地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("defaltSegNo");
        eiColumn.setDescName("默认账套");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tvzbm8102() {
initMetaData();
}

        /**
        * get the roleId - 角色代码
        * @return the roleId
        */
        public String getRoleId() {
        return this.roleId;
        }

        /**
        * set the roleId - 角色代码
        */
        public void setRoleId(String roleId) {
        this.roleId = roleId;
        }
        /**
        * get the sortId - 排序号
        * @return the sortId
        */
        public String getSortId() {
        return this.sortId;
        }

        /**
        * set the sortId - 排序号
        */
        public void setSortId(String sortId) {
        this.sortId = sortId;
        }
        /**
        * get the segNo - 业务单元代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the teleNum - 手机号
        * @return the teleNum
        */
        public String getTeleNum() {
        return this.teleNum;
        }

        /**
        * set the teleNum - 手机号
        */
        public void setTeleNum(String teleNum) {
        this.teleNum = teleNum;
        }
        /**
        * get the e_mail - 邮箱
        * @return the e_mail
        */
        public String getE_mail() {
        return this.e_mail;
        }

        /**
        * set the e_mail - 邮箱
        */
        public void setE_mail(String e_mail) {
        this.e_mail = e_mail;
        }
        /**
        * get the id - UUID
        * @return the id
        */
        public String getId() {
        return this.id;
        }

        /**
        * set the id - UUID
        */
        public void setId(String id) {
        this.id = id;
        }
        /**
        * get the ruleId - 业务类型编号
        * @return the ruleId
        */
        public String getRuleId() {
        return this.ruleId;
        }

        /**
        * set the ruleId - 业务类型编号
        */
        public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
        }
        /**
        * get the empNo - 工号
        * @return the empNo
        */
        public String getEmpNo() {
        return this.empNo;
        }

        /**
        * set the empNo - 工号
        */
        public void setEmpNo(String empNo) {
        this.empNo = empNo;
        }
        /**
        * get the name - 姓名
        * @return the name
        */
        public String getName() {
        return this.name;
        }

        /**
        * set the name - 姓名
        */
        public void setName(String name) {
        this.name = name;
        }
        /**
        * get the additiveFlag - 附加标记
        * @return the additiveFlag
        */
        public String getAdditiveFlag() {
        return this.additiveFlag;
        }

        /**
        * set the additiveFlag - 附加标记
        */
        public void setAdditiveFlag(String additiveFlag) {
        this.additiveFlag = additiveFlag;
        }
        /**
        * get the segRoleStatus - 员工状态 00-作废，20有效
        * @return the segRoleStatus
        */
        public String getSegRoleStatus() {
        return this.segRoleStatus;
        }

        /**
        * set the segRoleStatus - 员工状态 00-作废，20有效
        */
        public void setSegRoleStatus(String segRoleStatus) {
        this.segRoleStatus = segRoleStatus;
        }
        /**
        * get the additiveType - 员工附加类型,10正常,20兼职,21挂职
        * @return the additiveType
        */
        public String getAdditiveType() {
        return this.additiveType;
        }

        /**
        * set the additiveType - 员工附加类型,10正常,20兼职,21挂职
        */
        public void setAdditiveType(String additiveType) {
        this.additiveType = additiveType;
        }
        /**
        * get the attribute1 - 附加属性1
        * @return the attribute1
        */
        public String getAttribute1() {
        return this.attribute1;
        }

        /**
        * set the attribute1 - 附加属性1
        */
        public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
        }
        /**
        * get the attribute2 - 附加属性2
        * @return the attribute2
        */
        public String getAttribute2() {
        return this.attribute2;
        }

        /**
        * set the attribute2 - 附加属性2
        */
        public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
        }
        /**
        * get the attribute3 - 附加属性3
        * @return the attribute3
        */
        public String getAttribute3() {
        return this.attribute3;
        }

        /**
        * set the attribute3 - 附加属性3
        */
        public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
        }
        /**
        * get the attribute4 - 附加属性4
        * @return the attribute4
        */
        public String getAttribute4() {
        return this.attribute4;
        }

        /**
        * set the attribute4 - 附加属性4
        */
        public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
        }
        /**
        * get the attribute5 - 附加属性5
        * @return the attribute5
        */
        public String getAttribute5() {
        return this.attribute5;
        }

        /**
        * set the attribute5 - 附加属性5
        */
        public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 记录归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 记录归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记(默认0 删除1)
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记(默认0 删除1)
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the segFullName - 业务单元名称
        * @return the segFullName
        */
        public String getSegFullName() {
        return this.segFullName;
        }

        /**
        * set the segFullName - 业务单元名称
        */
        public void setSegFullName(String segFullName) {
        this.segFullName = segFullName;
        }
        /**
        * get the segName - 业务单元简称
        * @return the segName
        */
        public String getSegName() {
        return this.segName;
        }

        /**
        * set the segName - 业务单元简称
        */
        public void setSegName(String segName) {
        this.segName = segName;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the uploadPicture - 头像地址
        * @return the uploadPicture
        */
        public String getUploadPicture() {
        return this.uploadPicture;
        }

        /**
        * set the uploadPicture - 头像地址
        */
        public void setUploadPicture(String uploadPicture) {
        this.uploadPicture = uploadPicture;
        }
        /**
        * get the defaltSegNo - 默认账套
        * @return the defaltSegNo
        */
        public String getDefaltSegNo() {
        return this.defaltSegNo;
        }

        /**
        * set the defaltSegNo - 默认账套
        */
        public void setDefaltSegNo(String defaltSegNo) {
        this.defaltSegNo = defaltSegNo;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setRoleId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("roleId")), roleId));
                setSortId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sortId")), sortId));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setTeleNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teleNum")), teleNum));
                setE_mail(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("e_mail")), e_mail));
                setId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("id")), id));
                setRuleId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ruleId")), ruleId));
                setEmpNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("empNo")), empNo));
                setName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("name")), name));
                setAdditiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("additiveFlag")), additiveFlag));
                setSegRoleStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segRoleStatus")), segRoleStatus));
                setAdditiveType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("additiveType")), additiveType));
                setAttribute1(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attribute1")), attribute1));
                setAttribute2(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attribute2")), attribute2));
                setAttribute3(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attribute3")), attribute3));
                setAttribute4(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attribute4")), attribute4));
                setAttribute5(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("attribute5")), attribute5));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegFullName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segFullName")), segFullName));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setUploadPicture(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uploadPicture")), uploadPicture));
                setDefaltSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("defaltSegNo")), defaltSegNo));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("roleId",StringUtils.toString(roleId, eiMetadata.getMeta("roleId")));
                map.put("sortId",StringUtils.toString(sortId, eiMetadata.getMeta("sortId")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("teleNum",StringUtils.toString(teleNum, eiMetadata.getMeta("teleNum")));
                map.put("e_mail",StringUtils.toString(e_mail, eiMetadata.getMeta("e_mail")));
                map.put("id",StringUtils.toString(id, eiMetadata.getMeta("id")));
                map.put("ruleId",StringUtils.toString(ruleId, eiMetadata.getMeta("ruleId")));
                map.put("empNo",StringUtils.toString(empNo, eiMetadata.getMeta("empNo")));
                map.put("name",StringUtils.toString(name, eiMetadata.getMeta("name")));
                map.put("additiveFlag",StringUtils.toString(additiveFlag, eiMetadata.getMeta("additiveFlag")));
                map.put("segRoleStatus",StringUtils.toString(segRoleStatus, eiMetadata.getMeta("segRoleStatus")));
                map.put("additiveType",StringUtils.toString(additiveType, eiMetadata.getMeta("additiveType")));
                map.put("attribute1",StringUtils.toString(attribute1, eiMetadata.getMeta("attribute1")));
                map.put("attribute2",StringUtils.toString(attribute2, eiMetadata.getMeta("attribute2")));
                map.put("attribute3",StringUtils.toString(attribute3, eiMetadata.getMeta("attribute3")));
                map.put("attribute4",StringUtils.toString(attribute4, eiMetadata.getMeta("attribute4")));
                map.put("attribute5",StringUtils.toString(attribute5, eiMetadata.getMeta("attribute5")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("segFullName",StringUtils.toString(segFullName, eiMetadata.getMeta("segFullName")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("uploadPicture",StringUtils.toString(uploadPicture, eiMetadata.getMeta("uploadPicture")));
                map.put("defaltSegNo",StringUtils.toString(defaltSegNo, eiMetadata.getMeta("defaltSegNo")));

return map;

}
}