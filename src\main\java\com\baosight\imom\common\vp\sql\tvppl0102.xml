<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-01-21 11:21:57
   		Version :  1.0
		tableName :mevp.tvppl0102 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CHIP_ID  VARCHAR   NOT NULL, 
		 STAFF_NAME  VARCHAR, 
		 AREA_NAME  VARCHAR   NOT NULL, 
		 ACTION_TYPE  VARCHAR, 
		 WORKING_HOURS  VARCHAR, 
		 WORKING_TIME  VARCHAR, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="tvppl0102">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.vp.domain.Tvppl0102">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				CHIP_ID	as "chipId",  <!-- 芯片ID -->
				STAFF_NAME	as "staffName",  <!-- 人员姓名 -->
				AREA_NAME	as "areaName",  <!-- 区域名称 -->
				ACTION_TYPE	as "actionType",  <!-- 动作类型(1、进入 2、离开) -->
				WORKING_HOURS	as "workingHours",  <!-- 有效工作时间 -->
				WORKING_TIME	as "workingTime",  <!-- 操作时间 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM mevp.tvppl0102 WHERE 1=1
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM mevp.tvppl0102 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="chipId">
			CHIP_ID = #chipId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="staffName">
			STAFF_NAME = #staffName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaName">
			AREA_NAME = #areaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="actionType">
			ACTION_TYPE = #actionType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="workingHours">
			WORKING_HOURS = #workingHours#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="workingTime">
			WORKING_TIME = #workingTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO mevp.tvppl0102 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										CHIP_ID,  <!-- 芯片ID -->
										STAFF_NAME,  <!-- 人员姓名 -->
										AREA_NAME,  <!-- 区域名称 -->
										ACTION_TYPE,  <!-- 动作类型(1、进入 2、离开) -->
										WORKING_HOURS,  <!-- 有效工作时间 -->
										WORKING_TIME,  <!-- 操作时间 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #chipId#, #staffName#, #areaName#, #actionType#, #workingHours#, #workingTime#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM mevp.tvppl0102 WHERE 
	</delete>

	<update id="update">
		UPDATE mevp.tvppl0102 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					CHIP_ID	= #chipId#,   <!-- 芯片ID -->  
					STAFF_NAME	= #staffName#,   <!-- 人员姓名 -->  
					AREA_NAME	= #areaName#,   <!-- 区域名称 -->  
					ACTION_TYPE	= #actionType#,   <!-- 动作类型(1、进入 2、离开) -->  
					WORKING_HOURS	= #workingHours#,   <!-- 有效工作时间 -->  
					WORKING_TIME	= #workingTime#,   <!-- 操作时间 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					UUID	= #uuid#  <!-- ID -->  
			WHERE 	
	</update>
  
</sqlMap>