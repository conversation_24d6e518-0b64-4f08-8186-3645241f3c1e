<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-14 14:15:02
   		Version :  1.0
		tableName :iplat4j.tvzbm8102 
		 ROLE_ID  VARCHAR   NOT NULL, 
		 SORT_ID  VARCHAR   NOT NULL, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 TELE_NUM  VARCHAR   NOT NULL, 
		 E_MAIL  VARCHAR   NOT NULL, 
		 ID  VARCHAR   NOT NULL   primarykey, 
		 RULE_ID  VARCHAR   NOT NULL, 
		 EMP_NO  VARCHAR   NOT NULL, 
		 NAME  VARCHAR   NOT NULL, 
		 ADDITIVE_FLAG  VARCHAR   NOT NULL, 
		 SEG_ROLE_STATUS  VARCHAR   NOT NULL, 
		 ADDITIVE_TYPE  VARCHAR   NOT NULL, 
		 ATTRIBUTE_1  VARCHAR   NOT NULL, 
		 ATTRIBUTE_2  VARCHAR   NOT NULL, 
		 ATTRIBUTE_3  VARCHAR   NOT NULL, 
		 ATTRIBUTE_4  VARCHAR   NOT NULL, 
		 ATTRIBUTE_5  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 SEG_FULL_NAME  VARCHAR   NOT NULL, 
		 SEG_NAME  VARCHAR   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 UPLOAD_PICTURE  VARCHAR   NOT NULL, 
		 DEFALT_SEG_NO  VARCHAR   NOT NULL
	-->
<sqlMap namespace="tvzbm8102">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.xt.domain.Tvzbm8102">
		SELECT
				ROLE_ID	as "roleId",  <!-- 角色代码 -->
				SORT_ID	as "sortId",  <!-- 排序号 -->
				SEG_NO	as "segNo",  <!-- 业务单元代码 -->
				TELE_NUM	as "teleNum",  <!-- 手机号 -->
				E_MAIL	as "e_mail",  <!-- 邮箱 -->
				ID	as "id",  <!-- UUID -->
				RULE_ID	as "ruleId",  <!-- 业务类型编号 -->
				EMP_NO	as "empNo",  <!-- 工号 -->
				NAME	as "name",  <!-- 姓名 -->
				ADDITIVE_FLAG	as "additiveFlag",  <!-- 附加标记 -->
				SEG_ROLE_STATUS	as "segRoleStatus",  <!-- 员工状态 00-作废，20有效 -->
				ADDITIVE_TYPE	as "additiveType",  <!-- 员工附加类型,10正常,20兼职,21挂职 -->
				ATTRIBUTE_1	as "attribute1",  <!-- 附加属性1 -->
				ATTRIBUTE_2	as "attribute2",  <!-- 附加属性2 -->
				ATTRIBUTE_3	as "attribute3",  <!-- 附加属性3 -->
				ATTRIBUTE_4	as "attribute4",  <!-- 附加属性4 -->
				ATTRIBUTE_5	as "attribute5",  <!-- 附加属性5 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 记录归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记(默认0 删除1) -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				SEG_FULL_NAME	as "segFullName",  <!-- 业务单元名称 -->
				SEG_NAME	as "segName",  <!-- 业务单元简称 -->
				REMARK	as "remark",  <!-- 备注 -->
				UPLOAD_PICTURE	as "uploadPicture",  <!-- 头像地址 -->
				DEFALT_SEG_NO	as "defaltSegNo" <!-- 默认账套 -->
		FROM iplat4j.tvzbm8102 WHERE 1=1
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  ID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM iplat4j.tvzbm8102 WHERE 1=1
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="roleId">
			ROLE_ID = #roleId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sortId">
			SORT_ID = #sortId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="teleNum">
			TELE_NUM = #teleNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="e_mail">
			E_MAIL = #e_mail#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="id">
			ID = #id#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ruleId">
			RULE_ID = #ruleId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="empNo">
			EMP_NO = #empNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="name">
			NAME = #name#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="additiveFlag">
			ADDITIVE_FLAG = #additiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segRoleStatus">
			SEG_ROLE_STATUS = #segRoleStatus#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="additiveType">
			ADDITIVE_TYPE = #additiveType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute1">
			ATTRIBUTE_1 = #attribute1#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute2">
			ATTRIBUTE_2 = #attribute2#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute3">
			ATTRIBUTE_3 = #attribute3#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute4">
			ATTRIBUTE_4 = #attribute4#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attribute5">
			ATTRIBUTE_5 = #attribute5#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segFullName">
			SEG_FULL_NAME = #segFullName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segName">
			SEG_NAME = #segName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uploadPicture">
			UPLOAD_PICTURE = #uploadPicture#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="defaltSegNo">
			DEFALT_SEG_NO = #defaltSegNo#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO iplat4j.tvzbm8102 (ROLE_ID,  <!-- 角色代码 -->
										SORT_ID,  <!-- 排序号 -->
										SEG_NO,  <!-- 业务单元代码 -->
										TELE_NUM,  <!-- 手机号 -->
										E_MAIL,  <!-- 邮箱 -->
										ID,  <!-- UUID -->
										RULE_ID,  <!-- 业务类型编号 -->
										EMP_NO,  <!-- 工号 -->
										NAME,  <!-- 姓名 -->
										ADDITIVE_FLAG,  <!-- 附加标记 -->
										SEG_ROLE_STATUS,  <!-- 员工状态 00-作废，20有效 -->
										ADDITIVE_TYPE,  <!-- 员工附加类型,10正常,20兼职,21挂职 -->
										ATTRIBUTE_1,  <!-- 附加属性1 -->
										ATTRIBUTE_2,  <!-- 附加属性2 -->
										ATTRIBUTE_3,  <!-- 附加属性3 -->
										ATTRIBUTE_4,  <!-- 附加属性4 -->
										ATTRIBUTE_5,  <!-- 附加属性5 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 记录归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记(默认0 删除1) -->
										TENANT_USER,  <!-- 租户 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										SEG_FULL_NAME,  <!-- 业务单元名称 -->
										SEG_NAME,  <!-- 业务单元简称 -->
										REMARK,  <!-- 备注 -->
										UPLOAD_PICTURE,  <!-- 头像地址 -->
										DEFALT_SEG_NO  <!-- 默认账套 -->
										)		 
	    VALUES (#roleId#, #sortId#, #segNo#, #teleNum#, #e_mail#, #id#, #ruleId#, #empNo#, #name#, #additiveFlag#, #segRoleStatus#, #additiveType#, #attribute1#, #attribute2#, #attribute3#, #attribute4#, #attribute5#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #tenantUser#, #unitCode#, #segFullName#, #segName#, #remark#, #uploadPicture#, #defaltSegNo#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM iplat4j.tvzbm8102 WHERE 
			ID = #id#
	</delete>

	<update id="update">
		UPDATE iplat4j.tvzbm8102 
		SET 
		ROLE_ID	= #roleId#,   <!-- 角色代码 -->  
					SORT_ID	= #sortId#,   <!-- 排序号 -->  
					SEG_NO	= #segNo#,   <!-- 业务单元代码 -->  
					TELE_NUM	= #teleNum#,   <!-- 手机号 -->  
					E_MAIL	= #e_mail#,   <!-- 邮箱 -->  
								RULE_ID	= #ruleId#,   <!-- 业务类型编号 -->  
					EMP_NO	= #empNo#,   <!-- 工号 -->  
					NAME	= #name#,   <!-- 姓名 -->  
					ADDITIVE_FLAG	= #additiveFlag#,   <!-- 附加标记 -->  
					SEG_ROLE_STATUS	= #segRoleStatus#,   <!-- 员工状态 00-作废，20有效 -->  
					ADDITIVE_TYPE	= #additiveType#,   <!-- 员工附加类型,10正常,20兼职,21挂职 -->  
					ATTRIBUTE_1	= #attribute1#,   <!-- 附加属性1 -->  
					ATTRIBUTE_2	= #attribute2#,   <!-- 附加属性2 -->  
					ATTRIBUTE_3	= #attribute3#,   <!-- 附加属性3 -->  
					ATTRIBUTE_4	= #attribute4#,   <!-- 附加属性4 -->  
					ATTRIBUTE_5	= #attribute5#,   <!-- 附加属性5 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 记录归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记(默认0 删除1) -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					SEG_FULL_NAME	= #segFullName#,   <!-- 业务单元名称 -->  
					SEG_NAME	= #segName#,   <!-- 业务单元简称 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					UPLOAD_PICTURE	= #uploadPicture#,   <!-- 头像地址 -->  
					DEFALT_SEG_NO	= #defaltSegNo#  <!-- 默认账套 -->  
			WHERE 	
			ID = #id#
	</update>
  
</sqlMap>