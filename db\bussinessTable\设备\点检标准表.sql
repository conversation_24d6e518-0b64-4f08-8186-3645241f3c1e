create table TMEDV0104
(
    SPOT_CHECK_STANDARD_ID   VARCHAR(20)    default ' '    not null comment '点检标准编号',
    DEVICE_CODE              VARCHAR(64)    default ' '    not null comment '分部设备代码',
    DEVICE_NAME              VARCHAR(128)   default ' '    not null comment '分部设备名称',
    DEVICE_CHECK_STATUS      VARCHAR(16)    default ' '    not null comment '设备状态',
    E_ARCHIVES_NO            VARCHAR(20)    default ' '    not null comment '设备代码',
    EQUIPMENT_NAME           VARCHAR(200)   default ' '    not null comment '设备名称',
    SPOT_CHECK_CONTENT       VARCHAR(200)   default ' '    not null comment '点检内容',
    SPOT_CHECK_METHOD        VARCHAR(10)    default ' '    not null comment '点检方法',
    IS_PUBLISH               VARCHAR(2)     default ' '    not null comment '是否挂牌',
    SPOT_CHECK_STANDARD_TYPE VARCHAR(10)    default ' '    not null comment '标准类型',
    JUDGMENT_STANDARD        VARCHAR(200)   default ' '    not null comment '判断标准',
    BENCHMARK_DATE           VARCHAR(14)    default ' '    not null comment '基准日期',
    SPOT_CHECK_CYCLE         VARCHAR(20)    default ' '    not null comment '点检周期',
    SPOT_CHECK_NATURE        VARCHAR(2)     default ' '    not null comment '点检性质',
    SPOT_CHECK_IMPLEMENTE    VARCHAR(2)     default ' '    not null comment '实施方',
    MEASURE_ID               VARCHAR(10)    default ' '    not null comment '计量单位',
    UPPER_LIMIT              DECIMAL(20, 8) default 0      not null comment '上限值',
    LOWER_LIMIT              DECIMAL(20, 8) default 0      not null comment '下限值',
    SPOT_CHECK_STATUS        VARCHAR(10)    default ' '    not null comment '点检标准状态',
    TAG_ID                   VARCHAR(64)    default ' '    not null comment '自动采集点位',
    -- 固定字段
    UUID                     VARCHAR(32)                   NOT NULL COMMENT '唯一编码',
    REC_CREATOR              VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME          VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR              VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME          VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID                VARCHAR(64)    DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG             VARCHAR(1)     DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='点检标准表' ENGINE = INNODB
                        DEFAULT CHARSET = UTF8
                        COLLATE UTF8_BIN;

-- 增加ER图外键
ALTER TABLE TMEDV0104 ADD unique KEY (SPOT_CHECK_STANDARD_ID);
ALTER TABLE TMEDV0104 ADD FOREIGN KEY (DEVICE_CODE) REFERENCES TMEDV0103(DEVICE_CODE);
ALTER TABLE TMEDV0104 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);