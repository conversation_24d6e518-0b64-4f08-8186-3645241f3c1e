package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.constants.WorkFlowConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.vg.dm.domain.VGDM0101;
import com.baosight.imom.vg.dm.domain.VGDM0102;
import com.baosight.imom.vg.dm.domain.VGDM0201;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * <AUTHOR> yzj
 * @Description : 点检标准清单页面后台
 * @Date : 2024/8/13
 * @Version : 1.0
 */
public class ServiceVGDM02 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM02.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0201().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0201.QUERY, new VGDM0201());
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            // 用于缓存已查询的设备和分部设备信息
            Map<String, VGDM0101> equipmentCache = new HashMap<>();
            Map<String, VGDM0102> deviceCache = new HashMap<>();
            VGDM0201 vgdm0201;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0201);
                // 设备和分部设备校验
                this.validateEquipmentAndDevice(vgdm0201, equipmentCache, deviceCache);
                // 数据重复性校验
                this.checkDataRepeat(vgdm0201);
                // id生成
                String[] arr = {vgdm0201.getSegNo()};
                vgdm0201.setSpotCheckStandardId(
                        SequenceGenerator.getNextSequence(SequenceConstant.SPOT_CHECK_STANDARD_ID, arr));
                // 状态-10新增
                vgdm0201.setSpotCheckStatus(MesConstant.Status.K10);
                vgdm0201.setProcessInstanceId(" ");
                vgdm0201.setApprStatus(" ");
                Map insMap = vgdm0201.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            DaoUtils.insertBatch(dao, VGDM0201.INSERT, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 数据重复性校验
     *
     * @param vgdm0201 数据
     */
    private void checkDataRepeat(VGDM0201 vgdm0201) {
        int count = super.count(VGDM0201.COUNT_REPEAT, vgdm0201.toMap());
        if (count > 0) {
            throw new PlatException("存在相同的数据,不能新增");
        }
    }

    /**
     * 校验业务单元、设备、分部设备、采集点位是否存在并互相匹配
     *
     * @param vgdm0201       点检标准信息
     * @param equipmentCache 设备信息缓存
     * @param deviceCache    分部设备信息缓存
     * @throws PlatException 如果验证失败
     */
    private void validateEquipmentAndDevice(VGDM0201 vgdm0201,
                                            Map<String, VGDM0101> equipmentCache,
                                            Map<String, VGDM0102> deviceCache) {
        // 检查设备信息
        VGDM0101 equipment = equipmentCache.computeIfAbsent(vgdm0201.getEArchivesNo(),
                eArchivesNo -> VGDM0101.queryByNo(dao, eArchivesNo));
        if (equipment == null || !MesConstant.Status.K30.equals(equipment.getEquipmentStatus())) {
            throw new PlatException("设备信息不存在或设备状态不可用");
        }
        if (!equipment.getUnitCode().equals(vgdm0201.getUnitCode())) {
            throw new PlatException("设备信息业务单元不一致");
        }
        vgdm0201.setSegNo(equipment.getSegNo());
        vgdm0201.setEquipmentName(equipment.getEquipmentName());
        // 检查分部设备信息
        VGDM0102 device = deviceCache.computeIfAbsent(vgdm0201.getDeviceCode(),
                deviceCode -> VGDM0102.queryByNo(dao, deviceCode));
        if (device == null
                || !MesConstant.Status.K30.equals(device.getDeviceStatus())
                || !equipment.getEArchivesNo().equals(device.getEArchivesNo())) {
            throw new PlatException("分部设备信息不存在或分部设备信息与设备信息不一致");
        }
        vgdm0201.setDeviceName(device.getDeviceName());
    }

    /**
     * 数据校验
     *
     * @param vgdm0201 数据
     */
    private void checkData(VGDM0201 vgdm0201) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0201);
        // 使用通用方法进行特定的校验
        ValidationUtils.validateSpotCheckData(
                vgdm0201.getSpotCheckStandardType(),
                vgdm0201.getMeasureId(),
                vgdm0201.getUpperLimit(),
                vgdm0201.getLowerLimit(),
                vgdm0201.getJudgmentStandard());
        // 基准日期格式化
        vgdm0201.setBenchmarkDate(vgdm0201.getBenchmarkDate().replaceAll("-", ""));
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                DaoUtils.queryAndCheckStatus(dao, vgdm0201,
                        MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS,
                        MesConstant.Status.K10);
                this.checkData(vgdm0201);
                vgdm0201.setDelFlag("0");
                Map updMap = vgdm0201.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>
     * 修改点检标准状态为删除，删除标记置1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            VGDM0201 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                // 查询数据并校验状态
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0201,
                        MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS,
                        MesConstant.Status.K10);
                // 设置删除标记
                dbData.setDelFlag("1");
                // 设置状态
                dbData.setSpotCheckStatus(MesConstant.Status.K00);
                // 设置修改人
                Map delMap = dbData.toMap();
                RecordUtils.setRevisor(delMap);
                // 数据返回前端
                block.getRows().set(i, delMap);
            }
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 提交审核
     */
    public EiInfo submit(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            VGDM0201 dbData;
            String userId = UserSession.getLoginName();
            List<Map<String, Object>> paramList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0201,
                        MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS,
                        MesConstant.Status.K10);
                // 工作流相关参数
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("processDefinitionKey", WorkFlowConstant.processKey.SPOT_CHECK_STANDARD);
                paramMap.put("segNo", dbData.getSegNo());
                paramMap.put("approvalResult", "");
                paramMap.put("userId", userId);
                paramMap.put("comment", "提交审批");
                paramMap.put("variables", new HashMap<>());
                // 判断流程实例ID，空时启动新流程，不空时提交流程
                if (StrUtil.isBlank(dbData.getProcessInstanceId())) {
                    String subject = "点检标准待审批:" + vgdm0201.getSpotCheckStandardId();
                    String processInstanceId = WorkFlowUtils.start(dbData.getSegNo(),
                            WorkFlowConstant.processKey.SPOT_CHECK_STANDARD
                            , dbData.getUuid(), subject, null);
                    dbData.setProcessInstanceId(processInstanceId);
                }
                String taskId = WorkFlowUtils.getTodoTask(dbData.getProcessInstanceId());
                paramMap.put("taskId", taskId);
                // 获取下一节点审批人
                WorkFlowUtils.addAuditPersons(dao, paramMap);
                // 状态-已提交
                dbData.setSpotCheckStatus(MesConstant.Status.K20);
                // 审批状态-审批中
                dbData.setApprStatus(MesConstant.Status.K60);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
                // 工作流变量
                paramList.add(paramMap);
            }
            // 批量操作工作流
            WorkFlowUtils.batchAudit(paramList, true);
            // 批量更新数据
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE_STATUS, block.getRows());
            // 返回前端
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_SUBMIT);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 取消提交
     *
     * <p>
     * 终止工作流，清空审批信息
     */
    public EiInfo cancel(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0201 vgdm0201;
            VGDM0201 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0201 = new VGDM0201();
                vgdm0201.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0201,
                        MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS,
                        MesConstant.Status.K20);
                // 终止工作流
                WorkFlowUtils.deleteProcess(dbData.getProcessInstanceId());
                // 状态-新增
                dbData.setSpotCheckStatus(MesConstant.Status.K10);
                // 流程实例-空
                dbData.setProcessInstanceId(" ");
                // 审批状态-空
                dbData.setApprStatus(" ");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0201.UPDATE_STATUS, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
