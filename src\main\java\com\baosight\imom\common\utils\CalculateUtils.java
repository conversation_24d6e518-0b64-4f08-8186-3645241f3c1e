package com.baosight.imom.common.utils;

import com.baosight.imom.vg.dm.domain.ProcedureRule;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工序时间计算工具类
 *
 * <AUTHOR> 郁在杰
 * @Description :工序时间计算工具类
 * @Date : 2024/10/22
 * @Version : 1.0
 */
public class CalculateUtils {
    public static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Logger LOGGER = LoggerFactory.getLogger(CalculateUtils.class);

    /**
     * 评估条件
     *
     * @param rule            要评估的条件
     * @param latestTagValues 信号值映射
     * @return 如果条件满足返回true，否则返回false
     */
    public static boolean evaluateRule(ProcedureRule rule, Map<String, Double> latestTagValues) {
        if (StrUtil.isBlank(rule.getConditionType())) {
            // 简单条件
            return evaluateSimpleRule(rule, latestTagValues);
        }
        if ("AND".equals(rule.getConditionType())) {
            // 所有子条件都必须满足
            return rule.getSubRules().stream()
                    .allMatch(subRule -> evaluateRule(subRule, latestTagValues));
        } else if ("OR".equals(rule.getConditionType())) {
            // 至少一个子条件满足
            return rule.getSubRules().stream()
                    .anyMatch(subRule -> evaluateRule(subRule, latestTagValues));
        } else if ("SORT".equals(rule.getConditionType())) {
            // 按顺序满足
            return evaluateSortRule(rule, latestTagValues);
        }
        return false;
    }

    /**
     * 评估排序条件
     *
     * @param rule            要评估的条件
     * @param latestTagValues 信号值映射
     * @return 如果所有子条件按顺序满足则返回true，否则返回false
     */
    public static boolean evaluateSortRule(ProcedureRule rule, Map<String, Double> latestTagValues) {
        // 遍历所有子条件
        for (ProcedureRule subRule : rule.getSubRules()) {
            // 如果子条件已经被检查过，继续下一个
            if (subRule.isCheck()) {
                continue;
            }
            // 评估当前子条件
            if (evaluateRule(subRule, latestTagValues)) {
                // 如果满足，标记为已检查
                subRule.setCheck(true);
            } else {
                // 如果不满足，整个排序条件失败
                return false;
            }
        }
        // 所有子条件都满足，返回true
        return true;
    }

    /**
     * 评估简单条件
     *
     * @param rule            要评估的简单条件
     * @param latestTagValues 包含信号值的映射
     * @return 如果条件满足返回true，否则返回false
     */
    private static boolean evaluateSimpleRule(ProcedureRule rule, Map<String, Double> latestTagValues) {
        Double latestValue = latestTagValues.get(rule.getField());
        if (latestValue == null) {
            return false; // 如果缺少所需的信号类型,条件不满足
        }
        switch (rule.getOperator()) {
            case ">":
                return latestValue > rule.getValue();
            case "<":
                return latestValue < rule.getValue();
            case ">=":
                return latestValue >= rule.getValue();
            case "<=":
                return latestValue <= rule.getValue();
            case "=":
                return latestValue.equals(rule.getValue());
            default:
                return false;
        }
    }


    /**
     * 计算事件的开始和结束时间
     *
     * @param tagValues 设备信号列表
     * @param startRule 开始条件
     * @param stopRule  结束条件
     * @return 结束时间索引
     */
    public static int calculateEventTimes(LocalDateTime[] eventTimes,List<Map> tagValues, ProcedureRule startRule,
                                                      ProcedureRule stopRule) {
        Long startMills = null;
        Long endMills = null;
        Long potentialStartMills = null;
        Map<String, Double> latestTagValues = new HashMap<>();
        int resultIndex = -1;
        for (int i = 0; i < tagValues.size(); i++) {
            Map tagValueMap = tagValues.get(i);
            String tagId = tagValueMap.get("tagIhdId").toString();
            Double tagValue = Double.parseDouble(tagValueMap.get("tagValue").toString());
            Long tagTime = Long.parseLong(tagValueMap.get("tagTimeMicroSecond").toString());
            // 更新最新的信号值
            latestTagValues.put(tagId, tagValue);
//            LOGGER.info("信号判断：" + tagId + "|" + tagValue + "|" + tagTime);
//            LogUtils.log("信号判断：" + tagId + "|" + tagValue + "|" + tagTime);
            if (startMills == null) {
                // 寻找事件开始时间
                if (evaluateRule(startRule, latestTagValues)) {
                    if (potentialStartMills == null) {
                        potentialStartMills = tagTime;
                        LOGGER.info("potentialStartTime:" + potentialStartMills);
                        LogUtils.log("potentialStartTime:" + potentialStartMills);
                    }
                    // 检查是否满足持续时间要求
                    long duration = (tagTime - potentialStartMills) / 1000;
                    if (duration >= startRule.getDuration()) {
                        startMills = potentialStartMills;
                        LOGGER.info("startTime:" + startMills);
                        LogUtils.log("startTime:" + startMills);
                    }
                } else {
                    potentialStartMills = null;
                }
            } else {
                // 结束规则为空不处理
                if (stopRule.getConditionType() == null) {
                    break;
                }
                // 寻找事件结束时间
                if (evaluateRule(stopRule, latestTagValues)) {
                    endMills = tagTime;
                    LOGGER.info("endTime:" + endMills);
                    LogUtils.log("endTime:" + endMills);
                    resultIndex = i;
                    break;
                }
            }
        }
        LogUtils.log("resultIndex:" + resultIndex);
        LocalDateTime startTime = startMills == null ? null : LocalDateTime.ofInstant(
                Instant.ofEpochMilli(startMills),
                ZoneId.systemDefault());
        LocalDateTime endTime = endMills == null ? null : LocalDateTime.ofInstant(
                Instant.ofEpochMilli(endMills),
                ZoneId.systemDefault());
        eventTimes[0]=startTime;
        eventTimes[1]=endTime;
        return resultIndex;
    }


    /**
     * 计算标签区间时间 值都是1 或者都是0的值
     *
     * @param tagValues 设备信号列表
     * @return 标签区间时间
     */
    public static int calcuelateTagRangeTime(List<Map> tagValues, String tagZhi) {

        int countSecond = 0;
        for (int i = 0; i < tagValues.size(); i++) {
            Map tagValueMap = tagValues.get(i);
            String tagId = tagValueMap.get("tagIhdId").toString();
            String tagValue = tagValueMap.get("tagValue").toString();
            if (tagValue.equals(tagZhi)) {
                countSecond++;
            }
        }
        return countSecond;

    }
}
