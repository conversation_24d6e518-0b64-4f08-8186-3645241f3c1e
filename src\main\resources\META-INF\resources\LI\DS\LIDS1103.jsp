<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFPopupInput ename="inqu_status-0-craneId" cname="行车编号" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="craneInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-craneName"
                             popupTitle="行车查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-craneName" cname="行车名称" colWidth="3" disabled="true"/>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="craneId" cname="行车编号" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="craneName" cname="行车名称" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="x_value" cname="X轴" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="y_value" cname="Y轴" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="z_value" cname="Z轴" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="remark" cname="备注" align="center" width="150" enable="true" editType="textarea"/>
                <EF:EFColumn ename="uuid" cname="uuid" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--行车(设备档案)弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS07" id="craneInfo" width="90%" height="60%"/>

</EF:EFPage>
