package com.baosight.imom.vp.pl.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vp.pl.domain.VPPL0101;
import com.baosight.imom.vp.pl.domain.VPPL0102;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class ServiceVPPLInterfaces  extends ServiceBase {
    private String uwbIp = PlatApplicationContext.getProperty("JC.uwbHost"); // IP地址
    private String uwbPort = PlatApplicationContext.getProperty("JC.uwbPort");// 端口号

    /**
     * 推送区域信息给UWB
     */
    public EiInfo pushAreaInfoToUWB(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            String areaCode = inInfo.getString("areaCode");
            String areaName = inInfo.getString("areaName");
            String areaType = inInfo.getString("areaType");
            String areaStatus = inInfo.getString("areaStatus");
            String xInitialPoint = inInfo.getString("xInitialPoint");		/* X轴起始点*/
            String xDestination = inInfo.getString("xDestination");		/* X轴终到点*/
            String yInitialPoint = inInfo.getString("yInitialPoint");		/* Y轴起始点*/
            String yDestination = inInfo.getString("yDestination");		/* Y轴终到点*/

            //区域坐标点
            StringBuilder areaPoints = new StringBuilder();
            areaPoints.append(xInitialPoint).append(",").append(yInitialPoint);
            areaPoints.append(" ");
            areaPoints.append(xDestination).append(",").append(yInitialPoint);
            areaPoints.append(" ");
            areaPoints.append(xDestination).append(",").append(yDestination);
            areaPoints.append(" ");
            areaPoints.append(xInitialPoint).append(",").append(yDestination);

            JSONObject areaInfo = new JSONObject();
            areaInfo.put("name", areaName);//区域名称
            areaInfo.put("shape", 1);//区域形状[1非圆形，2圆形]
            areaInfo.put("is_use", 1);//是否使用区域(必填)
            areaInfo.put("area_type", 0);//
            areaInfo.put("area_group", 0);//
            areaInfo.put("area_dict", new HashMap<>());//自定义字典相关内容
            areaInfo.put("alarm_rule_list", new int[]{});//
            areaInfo.put("area_template_id", 1);//
            areaInfo.put("area_type_option_id", 5);//
            areaInfo.put("area_style", "rgba(255, 0, 0, 0.43)");//rgba(0, 50, 250, 0.43)
            areaInfo.put("relative_start", 0);//生效高度
            areaInfo.put("relative_end", 10);//生效高度
            areaInfo.put("is_show", 1);//属性 1显示，0不显示(必填)
            areaInfo.put("is_gps", 0);//是否为GPS区域[0-UWB区域,1-GPS区域](必填)
            areaInfo.put("area", String.valueOf(areaPoints));//区域坐标点，以顺时针方式将点的xy坐标拼接为字符串，xy间以逗号分隔， 点之间以空格分隔，包含终点，格式形如"x1,y1 x2,y2 x3,y3 x1,y1"(必填)
            areaInfo.put("type", 1);//区域类型[1普通,2考勤区域,3电子点名区域,9第一区域,10第二区域,11摄像机区域,12巡检点区域,13障碍物区域,14活动区域,15盲区]
            areaInfo.put("floor_id", 1);//楼层id

            String url = "http://" + uwbIp + ":" + uwbPort + "/EHCommon/area/area/addArea";
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Content-Type", "application/json");
                httpPost.setHeader("Authorization", UWBUtils.getUwbToken());

                // 设置请求体
                StringEntity entity = new StringEntity(areaInfo.toString(), "UTF-8");
                httpPost.setEntity(entity);

                // 执行请求
                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    HttpEntity responseEntity = response.getEntity();
                    if (responseEntity != null) {
                        String responseString = EntityUtils.toString(responseEntity);
                        if (response.getStatusLine().getStatusCode() != 200) {
                            throw new PlatException("Http接口响应码错误：" + responseString);
                        }
                        // 解析JSON响应
                        JSONObject jsonResponse = JSONObject.fromObject(responseString);

                        // 业务状态判断
                        if (jsonResponse.getInt("type") != 1) {
                            throw new PlatException("接口调用失败: " + jsonResponse.getString("result"));
                        }
                    }
                }
            }

            if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(outInfo.getMsg());
            }
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 接收UWB人员位置推送接口
     */
    public EiInfo receiveUWBPersonLocation(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //打印日志
            log("调用ServiceVPPLInterfaces.receiveUWBPersonLocation传入参数：" + inInfo.getAttr());
            System.out.println("调用ServiceVPPLInterfaces.receiveUWBPersonLocation传入参数：" + inInfo.getAttr());


            String segNo = inInfo.getString("seg_no");//系统账套
            String id = inInfo.getString("ID");//芯片ID
            String areaName = inInfo.getString("area_name");//区域名称
            String actionType = inInfo.getString("action_type");//动作类型
            String operateTime = inInfo.getString("operate_time");//操作时间

            if(StringUtils.isBlank(segNo)){
                throw new PlatException("系统账套不能为空！");
            }
            if (StringUtils.isBlank(id)){
                throw new PlatException("芯片ID不能为空！");
            }
            if (StringUtils.isBlank(areaName)){
                throw new PlatException("区域名称不能为空！");
            }
            if (StringUtils.isBlank(actionType)){
                throw new PlatException("动作类型不能为空！");
            }
            if(StringUtils.isBlank(operateTime)){
                throw new PlatException("操作时间不能为空！");
            }

            //根据芯片ID查询人员信息
            Map queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("chipId",id);
            List<VPPL0101> personList = dao.query(VPPL0101.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(personList)) {
                VPPL0101 person = personList.get(0);
                String jobScope = person.getJobScope();
                String postResponsibility = person.getPostResponsibility();
                /**
                 * TODO 若人员进入不属于该人员岗位职责的区域，第一次记录不计算有效时间，后续如果在进入区域，则记录有效时间（包含第一条）
                 */
                //如果进入区域为J4,Z4,且人员职责也属于J4,Z4避免重复记录
                Set<String> validAreas = new HashSet<>(Arrays.asList("J4", "Z4"));
                if (validAreas.contains(areaName)) {
                    log("接收人员进出信息,芯片：" + person.getChipId() + ",工号：" + person.getEmpNo() + "进入" + areaName + "与A跨重复，不记录！");
                    outInfo.setMsg("接收人员进出信息,芯片：" + person.getChipId() + ",工号： " + person.getEmpNo() + "进入" + areaName + "与A跨重复，不记录！");
                    outInfo.set("result", "1");
                    outInfo.set("result_desc", "接收成功");
                    return outInfo;
                }
                //如果传入区域为一期厂房(仓库)，且人员职责不为行车工，则不接受此区域的数据
                if ("一期厂房(仓库)".equals(areaName)) {
                    if (!"HC".equals(postResponsibility)) {
                        log("接收人员进出信息,芯片：" + person.getChipId() + ",工号：" + person.getEmpNo() + "进入" + areaName + "职责不为行车工，不记录！");
                        outInfo.setMsg("接收人员进出信息,芯片：" + person.getChipId() + ",工号： " + person.getEmpNo() + "进入" + areaName + "职责不为行车工，不记录！");
                        outInfo.set("result", "1");
                        outInfo.set("result_desc", "接收成功");
                        return outInfo;
                    }
                }
                //行车工只接受传入区域为一期厂房(仓库)的数据
                if ("HC".equals(postResponsibility)){
                    if (!"一期厂房(仓库)".equals(areaName)){
                        log("接收人员进出信息,芯片：" + person.getChipId() + ",工号：" + person.getEmpNo() + "进入" + areaName + "职责为行车工,区域不为一期厂房，不记录！");
                        outInfo.setMsg("接收人员进出信息,芯片：" + person.getChipId() + ",工号： " + person.getEmpNo() + "进入" + areaName + "职责为行车工,区域不为一期厂房，不记录！");
                        outInfo.set("result", "1");
                        outInfo.set("result_desc", "接收成功");
                        return outInfo;
                    }
                }

                //进入吊钩区域不算时间
                if ("12行车吊钩".equals(areaName) || "5号行车吊钩区域".equals(areaName)
                        || "3号行车吊钩区域".equals(areaName) || "1号行车吊钩区域".equals(areaName)) {
                    log("接收人员进出信息,芯片：" + person.getChipId() + ",工号：" + person.getEmpNo() + "进入" + areaName + "，不记录！");
                    outInfo.setMsg("接收人员进出信息,芯片：" + person.getChipId() + ",工号： " + person.getEmpNo() + "进入" + areaName + "，不记录！");
                    outInfo.set("result", "1");
                    outInfo.set("result_desc", "接收成功");
                    return outInfo;
                }

                //若进入的是A跨，根据岗位职责区分是哪个机组记录
                if (areaName.equals("A跨")) {
                    if (validAreas.contains(jobScope)) {
                        areaName = jobScope;
                    }
                    /*if (!validAreas.contains(jobScope)) {
                        log("接收人员进出A跨信息,芯片：" + person.getChipId() + ",工号：" + person.getEmpNo() + "未匹配到对应的岗位职责范围！");
                        outInfo.setMsg("接收人员进出A跨信息,芯片：" + person.getChipId() + ",工号： " + person.getEmpNo() + "未匹配到对应的岗位职责范围！");
                        outInfo.set("result", "1");
                        outInfo.set("result_desc", "接收成功");
                        return outInfo;
                    }*/
                }


                /*//岗位范围
                String jobScope = person.getJobScope();
                if (jobScope.contains(areaName)){*/
                    //插入员工工时统计表
                    VPPL0102 vppl0102 = new VPPL0102();
                    vppl0102.setSegNo(person.getSegNo());
                    vppl0102.setChipId(person.getChipId());
                    vppl0102.setUnitCode(person.getUnitCode());
                    vppl0102.setEmpNo(person.getEmpNo());
                    vppl0102.setStaffName(person.getStaffName());
                    vppl0102.setAreaName(areaName);
                    vppl0102.setActionType(actionType);
                    vppl0102.setWorkingHours(new BigDecimal("0"));
                    vppl0102.setWorkingTime(operateTime);
                    vppl0102.setRecCreator("system");
                    vppl0102.setRecCreatorName("system");
                    vppl0102.setRecCreateTime(DateUtil.curDateTimeStr14());
                    vppl0102.setRecRevisor("system");
                    vppl0102.setRecRevisorName("system");
                    vppl0102.setRecReviseTime(DateUtil.curDateTimeStr14());
                    vppl0102.setStatus("10");
                    vppl0102.setUuid(UUIDUtils.getUUID());

                    //根据当前传入时间计算工时所属日期与班次(离开时的所属日期与班次，记最后一次进入的记录)
                    Map<String, String> map = calculateWorkingDateAndShift(operateTime);
                    vppl0102.setWorkingDate(MapUtils.getString(map, "workingDate"));
                    vppl0102.setWorkingShift(MapUtils.getString(map, "workingShift"));

                    //判断当前数据在数据库中是否已存在
                    queryMap.clear();
                    queryMap.put("segNo", vppl0102.getSegNo());
                    queryMap.put("chipId", vppl0102.getChipId());
                    queryMap.put("actionType", vppl0102.getActionType());
                    queryMap.put("workingTime", vppl0102.getWorkingTime());
                    queryMap.put("areaName",vppl0102.getAreaName());
                    int count = super.count(VPPL0102.COUNT_EQUAL, queryMap);
                    if(count > 0){
                        outInfo.setMsg("记录已存在");
                        //返回状态
                        outInfo.set("result","1");
                        outInfo.set("result_desc","接收成功");
                        return outInfo;
                    }
                    /*进入时，以上次离开区域开始计算离开时间
                    1.若离开时间大于2个小时，判断为上一个班次结束，不统计本次离开时间
                    2.若离开时间小于30秒，也不统计本次离开时间
                    */
                    if ("1".equals(actionType)) {
                        queryMap.put("staffName", vppl0102.getStaffName());
                        //不使用区域条件
                        queryMap.put("areaName", "");
                        List<VPPL0102> list = dao.query(VPPL0102.QUERY_ORDER_BY_TIME, queryMap);
                        if(CollectionUtils.isNotEmpty(list)){
                            VPPL0102 vppl01022 = list.get(0);

                            //上次离开时间
                            String lastLeaveTime = vppl01022.getWorkingTime();
                            //本次进入时间
                            String enterTime = vppl0102.getWorkingTime();

                            //计算时长(离开时间)
                            BigDecimal diffHours = calculateHoursDifference(lastLeaveTime, enterTime);
                            if (diffHours.compareTo(new BigDecimal("2")) < 1 && diffHours.compareTo(new BigDecimal("0.008")) > 0) {
                                vppl0102.setDepartureHours(diffHours);
                            }
                        }

                        //进入时，生成流水号字段
                        vppl0102.setSerialNumber(SequenceGenerator.getNextSequence(VPPL0102.SEQ_ID, new String[]{segNo, ""}));
                    }
                    //根据，离开传入的操作时间减进入的操作时间，计算出时长
                    if ("2".equals(actionType)) {
                        queryMap.put("staffName", vppl0102.getStaffName());
                        queryMap.put("areaName", vppl0102.getAreaName());
                        List<VPPL0102> list = dao.query(VPPL0102.QUERY_ORDER_BY_TIME, queryMap);
                        if (CollectionUtils.isNotEmpty(list)) {
                            VPPL0102 vppl01021 = list.get(0);

                            //进入机组区域的时间
                            String startTime = vppl01021.getWorkingTime();
                            //离开机组区域的时间
                            String endTime = vppl0102.getWorkingTime();

                            //计算时长(有效工时)
                            BigDecimal diffHours = calculateHoursDifference(startTime, endTime);
                            vppl0102.setWorkingHours(diffHours);

                            /*区域有效工时小于30分钟且本次离开区域不属于员工岗位范围的，将本次离开的工时计算到员工岗位里
                             * 将进入区域地址也改为员工岗位范围
                             * */
                            if (vppl0102.getWorkingHours().compareTo(new BigDecimal("0.5")) < 1
                                    && !person.getJobScope().equals(vppl0102.getAreaName())) {

                                //调整前所属区域
                                String oldAreaName = vppl01021.getAreaName();
                                //调整后所属区域
                                String nowAreaName = person.getJobScope();
                                //修改进入动作的区域
                                RecordUtils.setRevisorBeanSys(vppl01021);
                                vppl01021.setAreaName(person.getJobScope());
                                vppl01021.setRecRevisorName("人员岗位区域调整，由：" + oldAreaName + "至" + nowAreaName);
                                dao.update(VPPL0102.UPDATE_BY_UUID, vppl01021);

                                //同时将本次离开的区域也设置为员工岗位范围
                                vppl0102.setAreaName(person.getJobScope());
                                vppl0102.setRecRevisorName("人员岗位区域调整由" + oldAreaName + "至" + nowAreaName);
                            }

                            //如果进入时记录的班次与离开记录的班次不一致，
                            if (!vppl0102.getWorkingShift().equals(vppl01021.getWorkingShift())) {
                                Map countMap = new HashMap();
                                countMap.put("segNo", vppl01021.getSegNo());
                                countMap.put("chipId", vppl01021.getChipId());
                                countMap.put("workingDate", vppl01021.getWorkingDate());
                                countMap.put("workingShift", vppl01021.getWorkingShift());
                                countMap.put("actionType", "1");
                                List<VPPL0102> list1 = dao.query(VPPL0102.QUERY_PREVIOUS_SHIFT_ENTRY_COUNT, countMap);
                                // 判断当前班次是否为早班、前一班次是否为晚班
                                boolean isEarlyNowAndWasNight = "10".equals(vppl0102.getWorkingShift()) && "30".equals(vppl01021.getWorkingShift());
                                // 判断当前班次是否为晚班、前一班次是否为早班
                                boolean isNightNowAndWasEarly = "30".equals(vppl0102.getWorkingShift()) && "10".equals(vppl01021.getWorkingShift());

                                if ((isEarlyNowAndWasNight || isNightNowAndWasEarly) && CollectionUtils.isNotEmpty(list1)) {
                                    if (list1.size() > 1) {
                                        // 多次进入，归属前一次班次
                                        vppl0102.setWorkingDate(vppl01021.getWorkingDate());
                                        vppl0102.setWorkingShift(vppl01021.getWorkingShift());
                                    } else {
                                        //调整前所属班次
                                        String oldWorkingShift = vppl01021.getWorkingDate() + "-" + vppl01021.getWorkingShift();
                                        //调整后所属班次
                                        String nowWorkingShift = vppl0102.getWorkingDate() + "-" + vppl0102.getWorkingShift();
                                        // 单次进入，归属当前班次
                                        vppl01021.setWorkingDate(vppl0102.getWorkingDate());
                                        vppl01021.setWorkingShift(vppl0102.getWorkingShift());
                                        RecordUtils.setRevisorBeanSys(vppl01021);
                                        vppl01021.setRecRevisorName("调整班次，由" + oldWorkingShift + "调整至" + nowWorkingShift);
                                        dao.update(VPPL0102.UPDATE_BY_UUID, vppl01021);
                                    }
                                } else {
                                    // 默认：以进入记录的班次为准
                                    vppl0102.setWorkingDate(vppl01021.getWorkingDate());
                                    vppl0102.setWorkingShift(vppl01021.getWorkingShift());
                                }
                            } else {
                                // 班次一致：直接使用进入记录的班次信息
                                vppl0102.setWorkingDate(vppl01021.getWorkingDate());
                                vppl0102.setWorkingShift(vppl01021.getWorkingShift());
                            }

                            //流水号，与进入一致
                            vppl0102.setSerialNumber(vppl01021.getSerialNumber());
                        }

                    }
                    dao.insert(VPPL0102.INSERT, vppl0102);
                /*}*/
            }

            outInfo.set("result","1");
            outInfo.set("result_desc","接收成功");

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("接收成功！");
        }catch (Exception ex){
            outInfo.set("result","0");
            outInfo.set("result_desc",ex.getMessage());

            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }finally {
            //打印日志
            log("调用ServiceVPPLInterfaces.receiveUWBPersonLocation返回参数：" + outInfo.getMsg());
            System.out.println("调用ServiceVPPLInterfaces.receiveUWBPersonLocation返回参数：" + outInfo.getMsg());
        }
        return outInfo;
    }

    /**
     * 微服务
     * @serviceId:S_VP_PL_0001
     * @jobId:J_VP_0001
     * 定时任务推送机组当班人次
     * @param inInfo
     * @return
     */
    public EiInfo pushShiftPersonnelCount(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try{
            //获取当前时间
            String nowTime = DateUtil.curDateTimeStr14();

            //设置开始和结束时间若当前时间为早上8点则开始时间为前一天晚上20点，若当前时间为晚上20点则开始时间为当天早上8点
            String startTime = "";
            String endTime = "";
            // 使用 DateTimeFormatter 解析 nowTime
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime nowDateTime = LocalDateTime.parse(nowTime, formatter);
            if (nowDateTime.getHour() == 8) {
                startTime = nowDateTime.minusDays(1).withHour(20).withMinute(0).withSecond(0).format(formatter);
                endTime = nowDateTime.withHour(8).withMinute(0).withSecond(0).format(formatter);
            } else if (nowDateTime.getHour() == 20) {
                startTime = nowDateTime.withHour(8).withMinute(0).withSecond(0).format(formatter);
                endTime = nowDateTime.withHour(20).withMinute(0).withSecond(0).format(formatter);
            }

            Map<String,Object> timeMap = new HashMap();
            timeMap.put("segNo", "JC000000");
            timeMap.put("startTime", startTime);
            timeMap.put("endTime", endTime);

            //计算传入时间范围的有效工作时间
            String workTime = calculateWorkTime(startTime, endTime);
            if (StringUtils.isBlank(workTime) || !StringUtils.isNumeric(workTime)) {
                throw new PlatException("计算有效工时不能为空或非法值：" + workTime);
            }

            double workHour = Double.parseDouble(workTime);
            if (workHour <= 0) {
                throw new PlatException("计算有效工时必须大于 0");
            }
            timeMap.put("workTime", workHour);

            //统计每个机组的当班人次
            List<HashMap> list = dao.query(VPPL0102.COUNT_CREW_VISITS_TODAY, timeMap);
            if(CollectionUtils.isEmpty(list)){
                throw new RuntimeException("未查询到当班人次！");
            }

            for (HashMap itemMap : list) {
                itemMap.put("segNo", "JC000000");
                itemMap.put("teamReportDate",nowTime.substring(0, 8));
                itemMap.put("machineCode",MapUtils.getString(itemMap,"areaName"));
                itemMap.put("teamId",MapUtils.getString(itemMap,"teamId"));
                itemMap.put("workingShift",MapUtils.getString(itemMap,"workingShift"));
                itemMap.put("dutyQuantity",MapUtils.getString(itemMap,"dutyQuantity"));
            }

            //返回统计信息
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceId, "S_VG_CM_0091");
            sendInfo.set("dList", list);
            outInfo = EServiceManager.call(sendInfo, TokenUtils.getXplatToken());
            log(nowTime + ":定时推送IMC机组当班人数接口传入参数：" + list + ",定时推送IMC机组当班人数接口返回参数：" + outInfo.toJSONString());
            System.out.println(nowTime + ":定时推送IMC机组当班人数接口传入参数：" + list + ",定时推送IMC机组当班人数接口返回参数：" + outInfo.toJSONString());
            if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException("调用IMC推送机组当班人次接口失败：" + outInfo.getMsg());
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("推送成功！");
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 共享服务
     * @ServiceId S_VP_PL_0001
     * IMC调用接口，依据传入时间范围计算班报人次
     *
     * @param inInfo
     * @return
     */
    public EiInfo calculateShiftPersonnelByTimeRange(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //打印日志
            log("IMC班报获取班报人数传入参数S_VP_PL_0001：" + inInfo.getAttr());
            System.out.println("IMC班报获取班报人数传入参数S_VP_PL_0001：" + inInfo.getAttr());


            String segNo = inInfo.getString("segNo");
            String areaName = inInfo.getString("machineCode");
            String workingShift = inInfo.getString("workingShift");
            String teamReportDate = inInfo.getString("teamReportDate");
            String startTime = inInfo.getString("dutyStartTime");
            String endTime = inInfo.getString("dutyEndTime");

            if (StringUtils.isBlank(segNo)) {
                throw new PlatException("系统账套不能为空！");
            }
            if (StringUtils.isBlank(areaName)) {
                throw new PlatException("机组编码不能为空！");
            }
            if (StringUtils.isBlank(teamReportDate)) {
                throw new PlatException("班报日期不能为空！");
            }
            if (StringUtils.isBlank(workingShift)) {
                throw new PlatException("班次不能为空！");
            }
            if (StringUtils.isBlank(startTime)) {
                throw new PlatException("开始时间不能为空！");
            }else if (startTime.length() == 12) {
                startTime += "00"; // 补全秒字段
            }
            if (StringUtils.isBlank(endTime)) {
                throw new PlatException("结束时间不能为空！");
            }else if (endTime.length() == 12) {
                endTime += "00"; // 补全秒字段
            }

            Map<String, Object> timeMap = new HashMap();
            timeMap.put("segNo", segNo);
            timeMap.put("areaName", areaName);
            timeMap.put("workingDate", teamReportDate);
            timeMap.put("workingShift", workingShift);
            timeMap.put("startTime", startTime);
            timeMap.put("endTime", endTime);

            //计算传入时间范围的有效工作时间
            String workTime = calculateWorkTime(startTime, endTime);
            if (StringUtils.isBlank(workTime) || !StringUtils.isNumeric(workTime)) {
                throw new PlatException("计算有效工时不能为空或非法值：" + workTime);
            }

            double workHour = Double.parseDouble(workTime);
            if (workHour <= 0) {
                throw new PlatException("计算有效工时必须大于 0");
            }
            timeMap.put("workTime", workHour);

            //统计每个机组的当班人次
            List<HashMap> list = dao.query(VPPL0102.COUNT_SHIFT_PERSONNEL_BY_MACHINE, timeMap);
            if(CollectionUtils.isEmpty(list)){
                throw new RuntimeException("未查询到当班人次！");
            }

            //机组当班人数
            String dutyQuantity = MapUtils.getString(list.get(0), "dutyQuantity");
            outInfo.set("dutyQuantity", StringUtils.isNotBlank(dutyQuantity) ? dutyQuantity : "0");

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("查询成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }finally {
            //打印日志
            log("IMC班报获取班报人数返回参数S_VP_PL_0001：" + outInfo.getAttr());
            System.out.println("IMC班报获取班报人数返回参数S_VP_PL_0001：" + outInfo.getAttr());
        }
        return outInfo;
    }

    /**
     * @param inInfo 传入机组(区域)返回区域当前时间存在的人员坐标
     * @serviceId S_VP_PL_0003
     */
    public EiInfo getPersonnelLocationInArea(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new RuntimeException("传入字段【系统账套】为空！");
            }
            String areaName = inInfo.getString("machineCode");
            if (StringUtils.isBlank(areaName)) {
                throw new RuntimeException("传入字段【机组(区域名称)】为空！");
            }

            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("areaName", areaName);

            //查询区域当前时间存在的员工芯片ID集合
            List<HashMap> chipInfoList = dao.query(VPPL0102.QUERY_CHIP_ID_IN_AREA, queryMap);
            //循环获取员工芯片ID对应的坐标信息
            if (CollectionUtils.isNotEmpty(chipInfoList)) {
                for (HashMap chipMap : chipInfoList) {
                    //芯片卡号
                    String cardId = MapUtils.getString(chipMap, "chipId");
                    Map paramMap = new HashMap();
                    paramMap.put("cardId", cardId);

                    //获取实时坐标
                    EiInfo sendInfo = new EiInfo();
                    sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
                    sendInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
                    sendInfo.set("paramMap", paramMap);
                    sendInfo = XLocalManager.call(sendInfo);
                    if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        throw new PlatException(sendInfo.getMsg());
                    }

                    paramMap = sendInfo.getMap("resultMap");
                    if (MapUtils.isEmpty(paramMap)) {
                        throw new PlatException(cardId + "未查询到实时坐标信息，请确认是否离线！");
                    }
                    //员工卡号所在坐标
                    String XPosition = MapUtils.getString(paramMap, "x_value", "0");
                    String YPosition = MapUtils.getString(paramMap, "y_value", "0");

                    chipMap.put("areaName", areaName);
                    chipMap.put("XPosition", XPosition);
                    chipMap.put("YPosition", YPosition);
                }
            }

            outInfo.set("result", chipInfoList);
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("查询成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 计算两个时间点之间的有效工作时间（小时），自动扣除指定的非工作时间段。
     *
     * <p>默认扣除以下时间段（每天）：
     * <ul>
     *     <li>11:00 - 12:00</li>
     *     <li>17:00 - 18:00</li>
     *     <li>23:00 - 00:00（跨天也支持）</li>
     *     <li>06:00 - 07:00</li>
     * </ul>
     *
     * @param startTime 开始时间，格式为 "yyyyMMddHHmmss"
     * @param endTime   结束时间，格式为 "yyyyMMddHHmmss"
     * @return          有效工作时间（字符串形式，单位：小时）
     *                  如果开始时间大于结束时间，则返回提示："开始时间不能晚于结束时间"
     */
    public String calculateWorkTime(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime start = LocalDateTime.parse(startTime, formatter);
        LocalDateTime end = LocalDateTime.parse(endTime, formatter);

        // 校验时间合法性
        if (start.isAfter(end)) {
            throw new PlatException("开始时间不能晚于结束时间");
        }

        // 总分钟数
        long totalMinutes = Duration.between(start, end).toMinutes();

        // 定义需要排除的时间段
        List<LocalTime> excludePeriodsStart = new ArrayList<>(Arrays.asList(
                LocalTime.of(11, 0),
                LocalTime.of(17, 0),
                LocalTime.of(23, 0),
                LocalTime.of(6, 0)
        ));

        List<LocalTime> excludePeriodsEnd = new ArrayList<>(Arrays.asList(
                LocalTime.of(12, 0),
                LocalTime.of(18, 0),
                LocalTime.MIDNIGHT,
                LocalTime.of(7, 0)
        ));

        int daysBetween = (int) Duration.between(start.toLocalDate().atStartOfDay(), end.toLocalDate().atStartOfDay()).toDays();
        int overlapCount = 0;

        for (int i = 0; i <= daysBetween; i++) {
            LocalDate currentDate = start.toLocalDate().plusDays(i);

            for (int j = 0; j < excludePeriodsStart.size(); j++) {
                LocalTime periodStart = excludePeriodsStart.get(j);
                LocalTime periodEnd = excludePeriodsEnd.get(j);

                LocalDateTime periodStartDT = LocalDateTime.of(currentDate, periodStart);
                LocalDateTime periodEndDT;

                // 处理跨天的情况，比如 23:00 - 00:00
                if (periodEnd.isBefore(periodStart)) {
                    periodEndDT = LocalDateTime.of(currentDate.plusDays(1), periodEnd);
                } else {
                    periodEndDT = LocalDateTime.of(currentDate, periodEnd);
                }

                if (end.isBefore(periodStartDT) || start.isAfter(periodEndDT)) {
                    continue; // 不相交
                }

                // 计算实际重叠的时间段
                LocalDateTime actualStart = start.isAfter(periodStartDT) ? start : periodStartDT;
                LocalDateTime actualEnd = end.isBefore(periodEndDT) ? end : periodEndDT;

                long overlapMinutes = Duration.between(actualStart, actualEnd).toMinutes();
                if (overlapMinutes > 0) {
                    overlapCount += Math.min((overlapMinutes + 59) / 60, 1); // 每个时间段最多扣除一小时
                }
            }
        }

        // 返回字符串格式结果
        long workHours = (totalMinutes / 60) - overlapCount;
        return String.valueOf(workHours);
    }

    /**
     * 根据传入的操作时间（operateTime）计算对应的工时日期（workingDate）和班次（workingShift）。
     *
     * <p>时间区间定义如下：</p>
     * <ul>
     *     <li><strong>早班（WorkingShift = "20"）</strong>：当日 08:00 ~ 次日 08:00</li>
     *     <li><strong>晚班（WorkingShift = "30"）</strong>：当日 20:00 ~ 次日 08:00</li>
     * </ul>
     *
     * <p>WorkingDate 计算逻辑：</p>
     * <ul>
     *     <li>如果 operateTime 在 08:00:00 到 20:00:00 之间，则 workingDate 为当天。</li>
     *     <li>如果 operateTime 在 20:00:00 到次日 08:00:00 之间，则 workingDate 为前一天（晚班归属上一日）。</li>
     * </ul>
     *
     * @param operateTime 操作时间，格式为 "yyyyMMddHHmmss"
     * @return 返回一个 Map，包含两个键：
     *         <ul>
     *             <li>"workingDate" - 工时日期，格式为 "yyyyMMdd"</li>
     *             <li>"workingShift" - 班次编码，"10" 表示早班，"30" 表示晚班</li>
     *         </ul>
     */
    public Map<String, String> calculateWorkingDateAndShift(String operateTime) {
        // operateTime 格式：yyyyMMddHHmmss
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime dateTime = LocalDateTime.parse(operateTime, formatter);

        LocalTime time = dateTime.toLocalTime();
        LocalDate date = dateTime.toLocalDate();

        String workingShift;
        LocalDate workingDate;

        if (time.isAfter(LocalTime.of(8, 0)) && time.isBefore(LocalTime.of(20, 0))) {
            // 早班：08:00 ~ 20:00
            workingShift = "10";
            workingDate = date;
        } else {
            // 晚班：20:00 ~ 次日 08:00
            workingShift = "30";
            if (time.isBefore(LocalTime.of(8, 0))) {
                // 属于前一天的晚班
                workingDate = date.minusDays(1);
            } else {
                workingDate = date;
            }
        }

        Map<String, String> result = new HashMap<>();
        result.put("workingDate", workingDate.format(DateTimeFormatter.BASIC_ISO_DATE));
        result.put("workingShift", workingShift);
        return result;
    }

    /**
     * 根据传入时间，计算小时差
     */
    public BigDecimal calculateHoursDifference(String startTime, String endTime) {
        BigDecimal diffHours = new BigDecimal("0");
        try {

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);

            long startMillis = startDate.getTime();
            long endMillis = endDate.getTime();

            long diffMillis = endMillis - startMillis;

            // 计算秒数差
            long diffSeconds = diffMillis / 1000;
            System.out.println("时间差（秒）：" + diffSeconds);

            // 计算分钟数差
            long diffMinutes = diffMillis / (1000 * 60);
            System.out.println("时间差（分钟）：" + diffMinutes);

            // 计算小时数差
            diffHours = new BigDecimal(diffMillis).divide(new BigDecimal(String.valueOf(1000 * 60 * 60)), 3, RoundingMode.HALF_UP);
            System.out.println("时间差（小时）：" + diffHours);

        } catch (Exception e) {
            return diffHours;
        }
        return diffHours;
    }

    /**
     * 定时任务,每天早上08：00与晚上20:00执行
     * jobId J_VP_0001
     * serviceId S_VP_PL_0004
     * 判断在早班或晚班的结束时间如果人员有推送进入没有推送离开时，自动讲班次结束时间作为离开时间计算人员的有效工时
     * (早班与晚班结束时间，若有效工时统计表中存在最后一次进入且还未离开的记录，自动插入一条班次结束时间的离开记录)
     */
    public EiInfo autoCompleteWorkTimeAtShiftEnd(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //先查询员工有效工时统计表最后一条记录为进入还未离开的数据(账套默认重庆宝钢-JC000000)
            List<VPPL0102> records = dao.queryAll(VPPL0102.QUERY_EMP_ACTIVE, new HashMap<>());
            if (CollectionUtils.isEmpty(records)) {
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("没有需要补全的记录！");
                return outInfo;
            }

            // 设置当前时间
            String operateTime = DateUtil.curDateTimeStr14();

            //记录错误信息
            StringBuilder errMsg = new StringBuilder();

            for (VPPL0102 record : records) {
                //操作时间，根据最后一条记录的班次确认
                String workingShift = record.getWorkingShift();
                //班次不为空,
                if (StringUtils.isBlank(workingShift)) {
                    //其他不做处理
                    continue;
                }
                //补录数据
                EiInfo inEi = new EiInfo();
                inEi.set("seg_no", record.getSegNo());
                inEi.set("ID", record.getChipId());
                inEi.set("area_name", record.getAreaName());
                inEi.set("action_type", "2");
                inEi.set("operate_time", operateTime);
                try {
                    inEi = receiveUWBPersonLocation(inEi);
                    if (inEi.getStatus() < EiConstant.STATUS_DEFAULT) {
                        throw new RuntimeException("处理失败：" + inEi.getMsg());
                    }
                } catch (Exception ex) {
                    errMsg.append(record.getChipId()).append("-").append(record.getAreaName()).append(":")
                            .append(inEi.getMsg()).append(";");
                }
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理完成！" + errMsg);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 定时任务，每天早上8点执行
     * jobId J_VP_0002
     * serviceId S_VP_PL_0005
     * 获取人员芯片当前电量，若芯片电量低于20%，则发送短信至对应人员
     */
    public EiInfo checkChipBatteryAndSendAlert(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //记录信息
            StringBuilder errMsg = new StringBuilder();

            String url = "http://" + uwbIp + ":" + uwbPort + "/EHCommon/device/deviceCard/getCard";
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Content-Type", "application/json");
                httpPost.setHeader("Authorization", UWBUtils.getUwbToken());

                // 设置请求体
                StringEntity entity = new StringEntity("", "UTF-8");
                httpPost.setEntity(entity);

                JSONObject jsonResponse = new JSONObject();
                // 执行请求
                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    HttpEntity responseEntity = response.getEntity();
                    if (responseEntity != null) {
                        String responseString = EntityUtils.toString(responseEntity);
                        if (response.getStatusLine().getStatusCode() != 200) {
                            throw new PlatException("Http接口响应码错误：" + responseString);
                        }
                        // 解析JSON响应
                        jsonResponse = JSONObject.fromObject(responseString);

                        // 业务状态判断
                        if (jsonResponse.getInt("type") != 1) {
                            throw new PlatException("接口调用失败: " + jsonResponse.getString("result"));
                        }
                    }
                }

                //获取返回数据
                Map resultMap = (Map) jsonResponse.get("result");
                List<Map> resultList = (List<Map>) resultMap.get("data");
                if (CollectionUtils.isEmpty(resultList)) {
                    throw new RuntimeException("未获取到人员标签信息！");
                }



                //将卡号，电量封装成一个集合，逐一比较
                Map<String, Object> cardPowerMap = new HashMap<>();
                for (Map data : resultList) {
                    //卡号
                    String cardId = MapUtils.getString(data, "card_id");
                    //电量
                    int power = NumberUtils.toInteger(MapUtils.getString(data, "power"), 0);
                    //电量小于20%，后续发送短信提示
                    if (power <= 20) {
                        cardPowerMap.put(cardId, power);
                    }
                }

                //根据卡号，查询人员电话等信息
                if (MapUtils.isNotEmpty(cardPowerMap)) {
                    String cardIds = cardPowerMap.keySet().stream()
                            .map(id -> "'" + id + "'")
                            .collect(Collectors.joining(","));

                    Map queryMap = new HashMap<>();
                    queryMap.put("segNo", "JC000000");
                    queryMap.put("cardIds", cardIds);
                    List<VPPL0101> records = dao.query(VPPL0101.QUERY, queryMap);

                    //循环发送短信
                    for (VPPL0101 record : records) {
                        try{
                            String phoneNumber = record.getPhoneNumber();
                            if (StringUtils.isBlank(phoneNumber)) {
                                throw new PlatException(record.getStaffName() + ",手机号码为空！");
                            }
                            HashMap<String, Object> messageMap = new HashMap<>();
                            messageMap.put("param1", phoneNumber);//电话号码
                            messageMap.put("param2", record.getStaffName());//姓名
                            messageMap.put("param3", cardPowerMap.get(record.getChipId()));//电量
                            MessageUtils.sendMessage(messageMap, "MT0000001022");
                        }catch (Exception ex){
                            errMsg.append(ex.getMessage()).append(";");
                        }
                    }
                }

            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("处理完成！" + errMsg);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

}
