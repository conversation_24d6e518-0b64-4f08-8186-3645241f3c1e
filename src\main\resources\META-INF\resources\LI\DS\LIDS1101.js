$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            loadComplete: function (grid) { // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            },
            beforeAdd: function (e) {
            },
            afterAdd: function (e) {
            },
            beforeEdit: function (e) {
                //不可编辑
                e.preventDefault();
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
            },
            afterEdit: function (e) {
            },
            onCheckRow: function (e) {
                if(e.checked){
                    //勾选，查询
                    let eiInfo = new EiInfo();
                    eiInfo.setByNode("inqu");
                    eiInfo.addBlock(resultGrid.getCheckedBlockData());
                    IMOMUtil.submitGridsData("subResult", "LIDS1101", "querySubResult", false, function (ei) {
                        subResultGrid.setEiInfo(ei);
                        subResultGrid.refresh();
                    }, eiInfo, false);
                }else{
                    //取消勾选，清空子项表格
                    subResultGrid.removeRows(subResultGrid.getDataItems());
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });

})