<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-tag_name" cname="TAG点" placeholder="多个用;分隔"
                        colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-alarm_status" cname="报警状态" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="未恢复未确认" value="3"/>
                <EF:EFOption label="未恢复已确认" value="1"/>
                <EF:EFOption label="已恢复未确认" value="2"/>
                <EF:EFOption label="已恢复已确认" value="0"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-time_begin" role="datetime"
                           endName="inqu_status-0-time_end" interval="5"
                           startCname="报警时间(起)" endCname="报警时间(止)"
                           ratio="3:3" format="yyyy-MM-dd HH:mm:ss">
            </EF:EFDateSpan>
            <EF:EFSelect ename="inqu_status-0-alarm_type" cname="报警类型" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="低低报警" value="0"/>
                <EF:EFOption label="低报警" value="2"/>
                <EF:EFOption label="高报警" value="3"/>
                <EF:EFOption label="高高报警" value="4"/>
            </EF:EFSelect>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="single" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="alarmId" cname="报警编号" align="center" width="80"/>
            <EF:EFColumn ename="confirmStatus" cname="确认状态" align="center" width="70"/>
            <EF:EFColumn ename="alarmState" cname="报警状态" align="center" width="70"/>
            <EF:EFColumn ename="alarmTag" cname="TAG名"/>
            <%-- <EF:EFColumn ename="alarmTagDesc" cname="TAG描述"/> --%>
            <EF:EFColumn ename="alarmTypedm" cname="报警类型代码" hidden="true"/>
            <EF:EFColumn ename="alarmType" cname="报警类型" align="center" width="70"/>
            <EF:EFColumn ename="alarmTagValue" cname="报警值"/>
            <EF:EFColumn ename="occurTime" cname="发生时间" width="160" align="center"/>
            <EF:EFColumn ename="priority" cname="优先级" align="center" width="70"/>
            <EF:EFColumn ename="recoverTagValue" cname="恢复值"/>
            <EF:EFColumn ename="recoverTime" cname="恢复时间" width="160" align="center"/>
            <EF:EFColumn ename="confirmor" cname="确认人" align="center" width="70"/>
            <EF:EFColumn ename="confirmTime" width="160" align="center" cname="确认时间"/>
            <EF:EFColumn ename="scadaName" cname="scada名称" align="center" width="90" sort="flase"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>
