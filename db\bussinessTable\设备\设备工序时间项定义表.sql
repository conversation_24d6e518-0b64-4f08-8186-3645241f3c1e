create table TMEDV0009
(
    E_ARCHIVES_NO   VARCHAR(20)  default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME  VARCHAR(200) default ' '    not null comment '设备名称',
    PROCEDURE_ID    VARCHAR(32)  default ' '    not null comment '工序段ID',
    PROCEDURE_NAME  VARCHAR(64)  default ' '    not null comment '工序名称',
    EVENT_TYPE   VARCHAR(20) DEFAULT ' ' NOT NULL  COMMENT 'IMC时间类型',
    -- 固定字段
    UUID            VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID)

) COMMENT ='工序时间项定义表' ENGINE = INNODB
                              DEFAULT CHARSET = UTF8
                              COLLATE UTF8_BIN;

-- 增加ER图外键
ALTER TABLE TMEDV0009 ADD unique KEY (PROCEDURE_ID);
ALTER TABLE TMEDV0009 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);