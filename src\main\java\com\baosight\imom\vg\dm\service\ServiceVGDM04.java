package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.constants.SequenceConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0101;
import com.baosight.imom.vg.dm.domain.VGDM0201;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.vg.dm.domain.VGDM0401;
import com.baosight.imom.vg.dm.domain.VGDM0402;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR> yzj
 * @Description : 点检计划管理页面后台
 * @Date : 2024/8/15
 * @Version : 1.0
 */
public class ServiceVGDM04 extends ServiceBase {
    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0401().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT2_BLOCK).addBlockMeta(new VGDM0402().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0401.QUERY, new VGDM0401());
    }

    /**
     * 根据点检计划主项查询点检计划子项
     */
    public EiInfo querySubByMain(EiInfo inInfo) {
        String eArchivesNo = inInfo.getCellStr(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, "eArchivesNo");
        if (StrUtil.isBlank(eArchivesNo)) {
            inInfo.setMsg("请先维护主信息");
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
        // 清除干扰条件
        inInfo.setCell(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0, "uuid", "");
        return super.query(inInfo, VGDM0402.QUERY, null, false, new VGDM0402().eiMetadata,
                MesConstant.Iplat.DETAIL_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK,
                MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 删除点检计划主项及子项信息
     *
     * <p>
     * 删除标记改为1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0401 vgdm0401;
            List<Map> delList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0401 = new VGDM0401();
                vgdm0401.fromMap(block.getRow(i));
                DaoUtils.queryAndCheckStatus(dao, vgdm0401, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                vgdm0401.setDelFlag("1");
                Map delMap = vgdm0401.toMap();
                RecordUtils.setRevisor(delMap);
                delList.add(delMap);
            }
            // 批量删除主项
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE, delList);
            // 批量删除子项
            DaoUtils.updateBatch(dao, VGDM0402.UPDATE_BY_MAIN, delList);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认点检计划
     *
     * <p>
     * 状态从新增改为确认
     */
    public EiInfo confirm(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0401 vgdm0401;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0401 = new VGDM0401();
                vgdm0401.fromMap(block.getRow(i));
                VGDM0401 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0401, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                // 校验子项数量
                Map<String, String> countMap = new HashMap<>();
                countMap.put("checkPlanId", dbData.getCheckPlanId());
                if (super.count(VGDM0402.COUNT, countMap) < 1) {
                    throw new PlatException("子项没有点检计划不能确认!");
                }
                // 状态改为确认
                dbData.setCheckPlanStatus(MesConstant.Status.K20);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CONFIRM);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 取消确认点检计划
     *
     * <p>
     * 状态从确认改为新增
     */
    public EiInfo cancel(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0401 vgdm0401;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0401 = new VGDM0401();
                vgdm0401.fromMap(block.getRow(i));
                VGDM0401 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0401, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CONFIRM_STATUS, MesConstant.Status.K20);
                dbData.setCheckPlanStatus(MesConstant.Status.K10);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_UN_CONFIRM);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 新增点检计划主项信息
     */
    public EiInfo insertMain(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0401 vgdm0401 = new VGDM0401();
            vgdm0401.fromMap(block.getRow(0));
            // 校验数据
            ValidationUtils.validateEntity(vgdm0401, ValidationUtils.Group1.class);
            // 默认字段赋值
            vgdm0401.setCheckPlanStatus(MesConstant.Status.K10);
            vgdm0401.setCheckPlanId(SequenceGenerator.getNextSequence(SequenceConstant.CHECK_PLAN_ID,
                    new String[]{vgdm0401.getSegNo()}));
            Map insMap = vgdm0401.toMap();
            RecordUtils.setCreator(insMap);
            dao.insert(VGDM0401.INSERT, insMap);
            block.getRows().clear();
            block.addRow(insMap);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改点检计划主项信息
     */
    public EiInfo updateMain(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0401 vgdm0401 = new VGDM0401();
            vgdm0401.fromMap(block.getRow(0));
            VGDM0401 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0401, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
            // 校验数据
            ValidationUtils.validateEntity(vgdm0401, ValidationUtils.Group2.class);
            // 修改字段
            dbData.setCheckPlanDate(vgdm0401.getCheckPlanDate());
            // 是否修改了点检性质
            boolean isUpdateSpotCheckNature = false;
            if (!dbData.getSpotCheckNature().equals(vgdm0401.getSpotCheckNature())) {
                dbData.setSpotCheckNature(vgdm0401.getSpotCheckNature());
                isUpdateSpotCheckNature = true;
            }
            Map updMap = dbData.toMap();
            RecordUtils.setRevisor(updMap);
            dao.update(VGDM0401.UPDATE, updMap);
            // 修改了主项点检性质需同步修改子项点检性质
            if (isUpdateSpotCheckNature) {
                dao.update(VGDM0402.UPDATE_SPOT_CHECK_NATURE, updMap);
            }
            block.getRows().clear();
            block.addRow(updMap);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 新增点检计划子项信息
     */
    public EiInfo insertSub(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            // 获取并校验主项信息
            VGDM0401 mainData = this.getWithCheck(inInfo);
            // 批量生成子项号
            List<String> ids = SequenceGenerator.getNextSequenceList(block.getRowCount(),
                    SequenceConstant.CHECK_PLAN_SUB_ID, new String[]{mainData.getCheckPlanId()});
            VGDM0402 vgdm0402;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0402 = new VGDM0402();
                vgdm0402.fromMap(block.getRow(i));
                this.checkSubData(vgdm0402);
                // 赋值主项信息
                vgdm0402.setUnitCode(mainData.getUnitCode());
                vgdm0402.setSegNo(mainData.getSegNo());
                vgdm0402.setCheckPlanId(mainData.getCheckPlanId());
                vgdm0402.setSpotCheckNature(mainData.getSpotCheckNature());
                vgdm0402.setSpotCheckImplemente(vgdm0402.getSpotCheckNature());
                // 子项信息
                vgdm0402.setCheckPlanSubId(ids.get(i));
                // 子项状态为未点检
                vgdm0402.setCheckPlanSubStatus(MesConstant.Status.K10);
                // 来源为手工新增
                vgdm0402.setPlanSource("1");
                Map insMap = vgdm0402.toMap();
                RecordUtils.setCreator(insMap);
                block.getRows().set(i, insMap);
            }
            DaoUtils.insertBatch(dao, VGDM0402.INSERT, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改点检计划子项信息
     */
    public EiInfo updateSub(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            // 获取并校验主项信息
            this.getWithCheck(inInfo);
            VGDM0402 vgdm0402;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0402 = new VGDM0402();
                vgdm0402.fromMap(block.getRow(i));
                this.checkSubData(vgdm0402);
                DaoUtils.queryAndCheckStatus(dao, vgdm0402, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NOT_CHECK_STATUS, MesConstant.Status.K10);
                vgdm0402.setDelFlag("0");
                Map updMap = vgdm0402.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0402.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除点检计划子项信息
     */
    public EiInfo deleteSub(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            // 获取并校验主项信息
            this.getWithCheck(inInfo);
            VGDM0402 vgdm0402;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0402 = new VGDM0402();
                vgdm0402.fromMap(block.getRow(i));
                VGDM0402 dbSub = DaoUtils.queryAndCheckStatus(dao, vgdm0402, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NOT_CHECK_STATUS, MesConstant.Status.K10);
                dbSub.setDelFlag("1");
                Map delMap = dbSub.toMap();
                RecordUtils.setRevisor(delMap);
                dao.update(VGDM0402.UPDATE, delMap);
            }
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 获取并校验前端传入的主项信息是否为新增状态
     *
     * @param info EiInfo
     * @return 数据库中主项信息
     */
    private VGDM0401 getWithCheck(EiInfo info) {
        VGDM0401 mainData = new VGDM0401();
        // 获取主项信息
        mainData.fromMap(info.getRow(MesConstant.Iplat.DETAIL_STATUS_BLOCK, 0));
        // 校验状态并返回
        mainData = DaoUtils.queryAndCheckStatus(dao, mainData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
        return mainData;
    }

    /**
     * 校验子项数据
     *
     * @param vgdm0402 待校验数据
     */
    private void checkSubData(VGDM0402 vgdm0402) {
        // 使用ValidationUtils进行基本的非空验证
        ValidationUtils.validateEntity(vgdm0402);
        // 使用通用方法进行特定的校验
        ValidationUtils.validateSpotCheckData(
                vgdm0402.getSpotCheckStandardType(),
                vgdm0402.getMeasureId(),
                vgdm0402.getUpperLimit(),
                vgdm0402.getLowerLimit(),
                vgdm0402.getJudgmentStandard()
        );
    }

    /**
     * 自动生成点检计划
     *
     * <p>自动生成下月所有有效设备的点检计划。
     * serviceId: S_VG_DM_01
     *
     * <p>生成流程:
     * <p>1. 获取所有状态为"有效"的设备清单
     * <p>2. 遍历每个设备,根据其点检标准生成下月点检计划
     * <p>3. 对于每个设备:
     * <ul>
     *   <li>生产设备需校验是否有排班信息
     *   <li>根据点检标准的基准日期和周期判断是否生成计划
     *   <li>辅助设备的专业点检仅在工作日生成
     *   <li>避免重复生成已存在的计划
     * </ul>
     *
     * @see #generatePlan(VGDM0101, LocalDate) 具体生成逻辑
     */
    public EiInfo autoGenerate(EiInfo inInfo) {
        try {
            // 获取输入参数
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("账套不能为空");
            }
            // 获取所有有效设备信息
            Map<String, String> map1 = new HashMap<>();
            map1.put("equipmentStatus", MesConstant.Status.K30);
            map1.put("segNo", segNo);
            List<VGDM0101> equipmentList = dao.query(VGDM0101.QUERY, map1);
            if (CollectionUtils.isEmpty(equipmentList)) {
                log("没有可用的设备信息");
                return inInfo;
            }
            log("待生成设备数：" + equipmentList.size());
            // 生成日期为下月
            LocalDate nextMonth = LocalDate.now().plusMonths(1);
            // 循环设备信息
            for (VGDM0101 equipment : equipmentList) {
                log("开始生成设备[" + equipment.getEArchivesNo() + "][" + equipment.getEquipmentName() + "]的点检计划");
                String errMsg = this.generatePlan(equipment, nextMonth);
                if (errMsg != null) {
                    log(equipment.getEArchivesNo(), errMsg);
                }
            }
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 根据设备信息生成点检计划
     *
     * <p>根据前端传入的设备编号和计划月份,为指定设备生成点检计划。
     *
     * <p>生成规则:
     * <ul>
     *   <li>只能生成当月或下月的点检计划
     *   <li>设备必须存在且状态为"有效"
     *   <li>按照设备关联的点检标准生成计划
     * </ul>
     *
     * @see #generatePlan(VGDM0101, LocalDate) 具体生成逻辑
     */
    public EiInfo generateByEquipment(EiInfo inInfo) {
        try {
            String eArchivesNo = inInfo.getCellStr(MesConstant.Iplat.INQU3_STATUS_BLOCK, 0, "eArchivesNo");
            String workingDate = inInfo.getCellStr(MesConstant.Iplat.INQU3_STATUS_BLOCK, 0, "workingDate");
            if (StrUtil.isBlank(eArchivesNo) || StrUtil.isBlank(workingDate)) {
                throw new PlatException("设备信息和计划月份不能为空");
            }
            // 获取设备信息
            VGDM0101 equipment = VGDM0101.queryByNo(dao, eArchivesNo);
            if (equipment == null || !MesConstant.Status.K30.equals(equipment.getEquipmentStatus())) {
                throw new PlatException("设备信息不存在");
            }
            // 计划月份必须为当月或下月
            LocalDate monthDate = LocalDate.now();
            String thisMonth = monthDate.format(DateUtils.FORMATTER_6);
            if (!thisMonth.equals(workingDate)) {
                // 下月
                monthDate = monthDate.plusMonths(1);
                String nextMonth = monthDate.format(DateUtils.FORMATTER_6);
                if (!nextMonth.equals(workingDate)) {
                    throw new PlatException("计划月份必须为当月或下月");
                }
            }
            // 生成点检计划
            String errMsg = this.generatePlan(equipment, monthDate);
            if (errMsg != null) {
                throw new PlatException(errMsg);
            }
            // 返回状态
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 点检计划延期
     */
    public EiInfo delayDate(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0401 mainData;
            String checkPlanDate = inInfo.getString("checkPlanDate");
            String delayRemark = inInfo.getString("delayRemark");
            for (int i = 0; i < block.getRowCount(); i++) {
                mainData = new VGDM0401();
                mainData.fromMap(block.getRow(i));
                // 校验状态
                VGDM0401 dbMain = DaoUtils.queryAndCheckStatus(dao, mainData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_CONFIRM_STATUS, MesConstant.Status.K20);
                // 点检性质必须为20专业点检
                if (!"20".equals(dbMain.getSpotCheckNature())) {
                    throw new PlatException("只能延期专业点检");
                }
                // 校验延期日期
                this.checkDelayDate(dbMain.getCheckPlanDate(), checkPlanDate);
                // 更新延期日期
                dbMain.setCheckPlanDate(checkPlanDate);
                // 追加延期理由
                dbMain.setDelayRemark(StrUtil.appendDelayRemark(dbMain.getDelayRemark(), delayRemark));
                // 更新日期
                Map updateMap = dbMain.toMap();
                RecordUtils.setRevisor(updateMap);
                block.getRows().set(i, updateMap);
            }
            DaoUtils.updateBatch(dao, VGDM0401.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_DELAY);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 校验延期日期,延期后日期必须大于原日期且在7天内
     *
     * @param originalDate 原日期
     * @param delayDate    延期日期
     */
    private void checkDelayDate(String originalDate, String delayDate) {
        LocalDate original = LocalDate.parse(originalDate, DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate delay = LocalDate.parse(delayDate, DateTimeFormatter.BASIC_ISO_DATE);
        if (!original.isBefore(delay)) {
            throw new PlatException("延期后点检日期必须晚于原点检日期");
        }
        if (ChronoUnit.DAYS.between(original, delay) > 7) {
            throw new PlatException("延期日期必须在7天内");
        }
    }

    /**
     * 根据设备信息生成点检计划
     *
     * <p>为指定设备在指定月份生成点检计划,需满足以下生成规则:
     * <ul>
     *   <li>生产设备必须有对应的排班信息
     *   <li>根据点检标准的基准日期和点检周期判断是否需要生成计划
     *   <li>辅助设备的专业点检仅在工作日生成计划
     *   <li>避免重复生成:
     *     <ul>
     *       <li>按设备和点检日期判断主项是否已存在
     *       <li>按设备、点检日期、点检标准编号判断子项是否已存在
     *     </ul>
     * </ul>
     *
     * @param equipment 设备信息
     * @param monthDate 计划日期
     * @return 错误信息, 成功返回null
     */
    private String generatePlan(VGDM0101 equipment, LocalDate monthDate) throws Exception {
        // 设备类型为生产设备时校验是否有对应排班
        List<String> sheduleList = new ArrayList<>();
        boolean isProduction = "1".equals(equipment.getEquipmentType());
        if (isProduction) {
            sheduleList = getSchedule(equipment, monthDate);
            if (CollectionUtils.isEmpty(sheduleList)) {
                return "未查询到设备下排班信息";
            }
        }
        // 获取设备对应点检标准
        List<VGDM0201> standardList = getValidStandards(equipment.getEArchivesNo());
        if (CollectionUtils.isEmpty(standardList)) {
            return "未查询到设备下点检标准信息";
        }
        // 设置为月第一天并获取当月天数
        monthDate = monthDate.withDayOfMonth(1);
        int days = monthDate.lengthOfMonth();
        // 获取指定月份的工作日信息
        Map<String, String> workingDayMap = getWorkingDayMap(monthDate);
        // 待新增主项和子项
        List<Map> mainList = new ArrayList<>();
        List<Map> subList = new ArrayList<>();
        // 当前日期
        LocalDate nowDate = LocalDate.now();
        // 按日期生成计划
        for (int i = 0; i < days; i++) {
            // 待生成点检日期
            LocalDate dayDate = monthDate.plusDays(i);
            // 早于当前日期时跳过
            if (dayDate.isBefore(nowDate)) {
                continue;
            }
            // 点检日期
            String checkPlanDate = dayDate.format(DateTimeFormatter.BASIC_ISO_DATE);
            // 生产设备无排班时跳过
            if (isProduction && !sheduleList.contains(checkPlanDate)) {
                continue;
            }
            // 获取当天已存在的计划
            List<VGDM0401> existingPlans = getExistingPlans(equipment.getEArchivesNo(), checkPlanDate);
            // 按点检性质分组生成子项
            Map<String, List<Map>> natureSubMap = generateSubPlans(
                    equipment, standardList, dayDate, checkPlanDate, workingDayMap);
            // 生成主项并关联子项
            generateMainAndLinkSubs(equipment, checkPlanDate, existingPlans,
                    natureSubMap, mainList, subList);
        }
        // 批量新增主项和子项信息
        DaoUtils.insertBatch(dao, VGDM0401.INSERT, mainList);
        DaoUtils.insertBatch(dao, VGDM0402.INSERT, subList);
        return null;
    }

    /**
     * 获取设备对应的有效的点检标准
     *
     * @param eArchivesNo 设备编号
     */
    private List<VGDM0201> getValidStandards(String eArchivesNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("eArchivesNo", eArchivesNo);
        map.put("spotCheckStatus", MesConstant.Status.K30);
        return dao.query(VGDM0201.QUERY, map);
    }

    /**
     * 获取已存在的新增状态的点检计划主项
     *
     * @param eArchivesNo   设备编号
     * @param checkPlanDate 点检日期
     */
    private List<VGDM0401> getExistingPlans(String eArchivesNo, String checkPlanDate) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("eArchivesNo", eArchivesNo);
        queryMap.put("checkPlanDate", checkPlanDate);
        queryMap.put("checkPlanStatus", MesConstant.Status.K10);
        return dao.query(VGDM0401.QUERY, queryMap);
    }

    /**
     * 获取指定月份的工作日信息
     * <p>
     * serviceId: S_BE_AT_12
     *
     * @param monthDate 指定日期
     * @return 工作日信息key为yyyyMMdd日期，value为0-工作日，1-周末，2-节假日，3-未录入
     */
    private Map<String, String> getWorkingDayMap(LocalDate monthDate) {
        Map<String, String> resultMap = new HashMap<>();
        // 获取年份月份
        String yearMonth = monthDate.format(DateUtils.FORMATTER_6);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("year", yearMonth.substring(0, 4));
        eiInfo.set("month", yearMonth.substring(4, 6));
        eiInfo.set(EiConstant.serviceId, "S_BE_AT_12");
        log("工作日查询参数：" + yearMonth);
        // 调用服务
        EiInfo rtnInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
        if (rtnInfo.getStatus() < 0) {
            // throw new PlatException(rtnInfo.getMsg());
            logError(yearMonth, rtnInfo.getMsg());
            return resultMap;
        }
        List<Map<String, Object>> result = (List<Map<String, Object>>) rtnInfo.get("result");
        if (CollectionUtils.isNotEmpty(result)) {
            for (Map<String, Object> row : result) {
                resultMap.put(MapUtils.getStr(row, "workDate"), MapUtils.getStr(row, "flag"));
            }
        }
        return resultMap;
    }

    /**
     * 获取设备排班信息
     * <p>
     * serviceId: S_VI_PM_0012
     *
     * @param equipment 设备信息
     * @param monthDate 日期
     */
    private List<String> getSchedule(VGDM0101 equipment, LocalDate monthDate) {
        List<String> resultList = new ArrayList<>();
        // 获取年份月份
        String yearMonth = monthDate.format(DateUtils.FORMATTER_7);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("segNo", equipment.getSegNo());
        eiInfo.set("e_archivesNo", equipment.getEArchivesNo());
        eiInfo.set("month", yearMonth);
        eiInfo.set(EiConstant.serviceId, "S_VI_PM_0012");
        // 调用共享服务
        log("排班查询参数：" + equipment.getSegNo() + "|" + equipment.getEArchivesNo() + "|" + yearMonth);
        EiInfo rtnInfo = EServiceManager.call(eiInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
//            throw new PlatException(rtnInfo.getMsg());
            logError(equipment.getEArchivesNo(), rtnInfo.getMsg());
            return resultList;
        }
        List<Map<String, Object>> result = (List<Map<String, Object>>) rtnInfo.get("scheduling");
        if (CollectionUtils.isNotEmpty(result)) {
            for (Map<String, Object> row : result) {
                String rtnDay = MapUtils.getStr(row, "monthlySchedulingTodayDate");
                if (StrUtil.isNotBlank(rtnDay)) {
                    resultList.add(rtnDay.replaceAll("-", ""));
                }
            }
        }
        return resultList;
    }

    /**
     * 生成子项计划并按点检性质分组
     *
     * @param equipment     设备信息
     * @param standardList  点检标准信息
     * @param dayDate       当前日期
     * @param checkPlanDate 点检日期
     * @param workingDayMap 工作日信息
     * @return 按点检性质分组，key为点检性质，value为子项信息
     */
    private Map<String, List<Map>> generateSubPlans(VGDM0101 equipment,
                                                    List<VGDM0201> standardList, LocalDate dayDate,
                                                    String checkPlanDate, Map<String, String> workingDayMap) {
        // 按点检性质分组，key为点检性质，value为子项信息
        Map<String, List<Map>> natureSubMap = new HashMap<>();
        // 遍历点检标准
        for (VGDM0201 standard : standardList) {
            // 检查是否需要生成计划
            if (!shouldGeneratePlan(standard, dayDate, checkPlanDate, equipment, workingDayMap)) {
                continue;
            }
            // 生成子项
            VGDM0402 subPlan = createSubPlan(standard);
            Map insMap = subPlan.toMap();
            RecordUtils.setCreatorSys(insMap);
            // 按点检性质分组
            String nature = standard.getSpotCheckNature();
            natureSubMap.computeIfAbsent(nature, k -> new ArrayList<>()).add(insMap);
        }
        return natureSubMap;
    }

    /**
     * 检查是否需要生成计划
     *
     * @param standard      点检标准信息
     * @param dayDate       当前日期
     * @param checkPlanDate 点检日期
     * @param equipment     设备信息
     * @param workingDayMap 工作日信息
     * @return 是否需要生成计划
     */
    private boolean shouldGeneratePlan(VGDM0201 standard, LocalDate dayDate,
                                       String checkPlanDate, VGDM0101 equipment,
                                       Map<String, String> workingDayMap) {
        // 设备类型为辅助设备且点检性质为专业点检时只在工作日生成点检计划
        if ("2".equals(equipment.getEquipmentType())
                && "20".equals(standard.getSpotCheckNature())) {
            if (!"0".equals(MapUtils.getStr(workingDayMap, checkPlanDate))) {
                return false;
            }
        }
        // 检查点检周期
        LocalDate benchDate = LocalDate.parse(standard.getBenchmarkDate().substring(0, 8),
                DateTimeFormatter.BASIC_ISO_DATE);
        int cycle = Integer.parseInt(standard.getSpotCheckCycle());
        long diffDays = ChronoUnit.DAYS.between(benchDate, dayDate);
        if (diffDays % cycle != 0) {
            return false;
        }
        // 检查是否已存在
        Map<String, String> countMap = new HashMap<>();
        countMap.put("checkPlanDate", checkPlanDate);
        countMap.put("spotCheckStandardId", standard.getSpotCheckStandardId());
        countMap.put("eArchivesNo", equipment.getEArchivesNo());
        return super.count(VGDM0402.COUNT_WITH_MAIN, countMap) == 0;
    }

    /**
     * 生成子项计划
     *
     * @param standard 点检标准信息
     * @return 子项信息
     */
    private VGDM0402 createSubPlan(VGDM0201 standard) {
        VGDM0402 subPlan = new VGDM0402();
        // 从点检标准获取的信息
        subPlan.fromMap(standard.toMap());
        // 其他字段
        subPlan.setCheckPlanSubStatus(MesConstant.Status.K10);
        subPlan.setPlanSource("2");
        return subPlan;
    }

    /**
     * 生成主项并关联子项
     *
     * @param equipment     设备信息
     * @param checkPlanDate 点检日期
     * @param existingPlans 已存在的主项信息
     * @param natureSubMap  按点检性质分组的子项信息
     * @param mainList      待新增主项信息
     * @param subList       待新增子项信息
     */
    private void generateMainAndLinkSubs(VGDM0101 equipment, String checkPlanDate,
                                         List<VGDM0401> existingPlans, Map<String, List<Map>> natureSubMap,
                                         List<Map> mainList, List<Map> subList) {
        if (natureSubMap.isEmpty()) {
            return;
        }
        // 遍历点检性质
        for (String nature : natureSubMap.keySet()) {
            // 生成主项
            VGDM0401 checkPlan = createMainPlan(equipment, checkPlanDate, existingPlans, nature, mainList);
            // 设置子项关联的主项ID
            List<Map> detailList = natureSubMap.get(nature);
            List<String> idList = SequenceGenerator.getNextSequenceList(detailList.size(),
                    SequenceConstant.CHECK_PLAN_SUB_ID, new String[]{checkPlan.getCheckPlanId()});
            for (int k = 0; k < detailList.size(); k++) {
                Map subMap = detailList.get(k);
                subMap.put("checkPlanId", checkPlan.getCheckPlanId());
                subMap.put("checkPlanSubId", idList.get(k));
            }
            subList.addAll(detailList);
        }
    }

    /**
     * 生成主项
     *
     * @param equipment     设备信息
     * @param checkPlanDate 点检日期
     * @param existingPlans 已存在的主项信息
     * @param nature        点检性质
     * @param mainList      待新增主项信息
     * @return 主项信息
     */
    private VGDM0401 createMainPlan(VGDM0101 equipment, String checkPlanDate,
                                    List<VGDM0401> existingPlans, String nature, List<Map> mainList) {
        // 生成主项
        VGDM0401 checkPlan = null;
        // 根据点检性质判断是否存在
        for (VGDM0401 plan : existingPlans) {
            if (nature.equals(plan.getSpotCheckNature())) {
                checkPlan = plan;
                break;
            }
        }
        // 未存在则生成主项
        if (checkPlan == null) {
            checkPlan = new VGDM0401();
            checkPlan.setEArchivesNo(equipment.getEArchivesNo());
            checkPlan.setEquipmentName(equipment.getEquipmentName());
            checkPlan.setSegNo(equipment.getSegNo());
            checkPlan.setUnitCode(equipment.getUnitCode());
            checkPlan.setCheckPlanDate(checkPlanDate);
            checkPlan.setCheckPlanStatus(MesConstant.Status.K20);
            checkPlan.setCheckPlanId(SequenceGenerator.getNextSequence(SequenceConstant.CHECK_PLAN_ID,
                    new String[]{checkPlan.getSegNo()}));
            // 设置点检性质
            checkPlan.setSpotCheckNature(nature);
            Map insMap = checkPlan.toMap();
            RecordUtils.setCreatorSys(insMap);
            mainList.add(insMap);
        }
        return checkPlan;
    }

    /**
     * 按日期更新点检计划
     * <p>
     * serviceId: S_VG_DM_0003
     *
     * @param inInfo 入参
     * @return 出参
     */
    public EiInfo updateByDate(EiInfo inInfo) {
        try {
            // 获取入参
            String checkPlanDate = inInfo.getString("checkPlanDate");
            String operation = inInfo.getString("operation");
            String segNo = inInfo.getString("segNo");
            String eArchivesNo = inInfo.getString("eArchivesNo");
            super.log(String.format("入参:checkPlanDate=%s, operation=%s, segNo=%s, eArchivesNo=%s",
                    checkPlanDate, operation, segNo, eArchivesNo));
            if (StrUtil.isBlank(checkPlanDate)
                    || StrUtil.isBlank(operation)
                    || StrUtil.isBlank(segNo)
                    || StrUtil.isBlank(eArchivesNo)) {
                throw new PlatException("日期、操作类型、业务单元和设备编号不能为空");
            }
            // 校验日期格式为yyyyMMdd
            LocalDate dayDate;
            try {
                dayDate = LocalDate.parse(checkPlanDate, DateTimeFormatter.BASIC_ISO_DATE);
            } catch (Exception e) {
                throw new PlatException("日期格式错误");
            }
            // 校验操作类型
            if (!Arrays.asList("1", "2").contains(operation)) {
                throw new PlatException("操作类型错误");
            }
            // 获取设备信息
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("eArchivesNo", eArchivesNo);
            queryMap.put("equipmentStatus", MesConstant.Status.K30);
            List<VGDM0101> equipmentList = dao.query(VGDM0101.QUERY, queryMap);
            if (CollectionUtils.isEmpty(equipmentList)) {
                throw new PlatException("未查询到有效设备信息");
            }
            VGDM0101 equipment = equipmentList.get(0);
            // 设备类型为生产设备
            if (!"1".equals(equipment.getEquipmentType())) {
                throw new PlatException("非生产设备不支持按日期更新点检计划");
            }
            // 查询点检计划数
            Map<String, String> countMap = new HashMap<>();
            countMap.put("eArchivesNo", equipment.getEArchivesNo());
            countMap.put("checkPlanDate", checkPlanDate);
            int count = super.count(VGDM0402.COUNT_WITH_MAIN, countMap);
            super.log(String.format("已有点检计划数:count=%d", count));
            // 根据操作类型执行相应操作
            if ("1".equals(operation) && count == 0) {
                super.log("生成点检计划");
                // 生成点检计划
                generateByDay(equipment, dayDate, checkPlanDate);
            }
            if ("2".equals(operation) && count > 0) {
                super.log("删除点检计划");
                // 删除点检计划
                deleteByDay(equipment, checkPlanDate);
            }
            inInfo.setMsg("操作成功");
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 根据点检日期生成点检计划
     *
     * @param equipment     设备信息
     * @param checkPlanDate 点检日期
     * @param dayDate       日期
     */
    private void generateByDay(VGDM0101 equipment, LocalDate dayDate, String checkPlanDate) {
        // 获取点检标准
        List<VGDM0201> standardList = getValidStandards(equipment.getEArchivesNo());
        // 工作日信息-生产设备不需要判断，空map
        Map<String, String> workingDayMap = new HashMap<>();
        // 待新增主项和子项
        List<Map> mainList = new ArrayList<>();
        List<Map> subList = new ArrayList<>();
        // 获取当天已存在的计划
        List<VGDM0401> existingPlans = getExistingPlans(equipment.getEArchivesNo(), checkPlanDate);
        // 按点检性质分组生成子项
        Map<String, List<Map>> natureSubMap = generateSubPlans(
                equipment, standardList, dayDate, checkPlanDate, workingDayMap);
        // 生成主项并关联子项
        generateMainAndLinkSubs(equipment, checkPlanDate, existingPlans,
                natureSubMap, mainList, subList);
        // 批量新增主项和子项信息
        DaoUtils.insertBatch(dao, VGDM0401.INSERT, mainList);
        DaoUtils.insertBatch(dao, VGDM0402.INSERT, subList);
    }

    /**
     * 根据点检日期删除点检计划
     *
     * @param equipment     设备信息
     * @param checkPlanDate 点检日期
     */
    private void deleteByDay(VGDM0101 equipment, String checkPlanDate) {
        Map<String, String> updMap = new HashMap<>();
        updMap.put("delFlag", "1");
        updMap.put("checkPlanDate", checkPlanDate);
        updMap.put("eArchivesNo", equipment.getEArchivesNo());
        RecordUtils.setRevisorSys(updMap);
        // 删除子项
        dao.update(VGDM0402.UPDATE_BY_DAY, updMap);
        // 删除主项
        dao.update(VGDM0401.UPDATE_BY_DAY, updMap);
    }
}