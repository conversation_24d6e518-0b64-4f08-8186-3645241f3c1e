package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;

/**
 * 工程项目:.
 * 开发公司:Baosight Software LTD.co Copyright (c) 2022.
 * 类的简介:序列号生成工具类-DateUtils.
 * 类的描述:序列号生成公共方法.
 * 开发日期:2022年5月17日 上午9:00:00.
 * <AUTHOR> TEL:17762608719 （开发人）.
 * @version 1.0 （开发版本）.
 * @since 1.8 （JDK版本号）.
 */
public final class UUIDUtils {

    private UUIDUtils() {

    }

    /**
     * 序列生成方法.
     * @return
     */
    public static String getUUID(){
            //获取生成器单例对象
            UUIDHexIdGenerator idGenerator = UUIDHexIdGenerator.getInstance();
            //生成UUID唯一编码
            String strUUID = idGenerator.generate().toString();

        return strUUID;
    }

}