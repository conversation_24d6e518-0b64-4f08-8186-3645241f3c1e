/**
 * Generate time : 2024-08-29 11:01:44
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

/**
 * Tvgdm0501
 */
public class Tvgdm0501 extends DaoEPBase {

    private String exceptionContactId = " ";        /* 异常信息联络单号*/
    @NotBlank(message = "异常来源不能为空")
    private String exceptionSource = " ";        /* 异常信息来源*/
    @NotBlank(message = "分部设备不能为空")
    private String deviceCode = " ";        /* 分部设备代码*/
    @NotBlank(message = "分部设备不能为空")
    private String deviceName = " ";        /* 分部设备名称*/
    @NotBlank(message = "设备信息不能为空")
    private String eArchivesNo = " ";        /* 设备档案编号*/
    @NotBlank(message = "设备信息不能为��")
    private String equipmentName = " ";        /* 设备名称*/
    @NotBlank(message = "点检内容不能为空")
    private String spotCheckContent = " ";        /* 点检内容*/
    @NotBlank(message = "点检方法不能为空")
    private String spotCheckMethod = " ";        /* 点检方法*/
    @NotBlank(message = "标准类型不能为空")
    private String spotCheckStandardType = " ";        /* 点检标准类型*/
    @NotBlank(message = "判断标准不能为空")
    private String judgmentStandard = " ";        /* 判断标准*/
    private String isOverhaulHandle = " ";        /* 是否检修处理*/
    private String temporaryMeasures = " ";        /* 临时措施*/
    @NotBlank(message = "点检实绩不能为空")
    private String actualsRemark = " ";        /* 点检实绩*/
    @NotBlank(message = "处理措施不能为空")
    private String handleMeasures = " ";        /* 处理措施*/
    @NotBlank(message = "处理结果不能为空")
    private String procResult = " ";        /* 处理结果*/
    private String spotCheckImplemente = " ";        /* 点检实施方*/
    private String exceptionStatus = " ";        /* 点检异常息状态*/
    private String measureId = " ";        /* 计量单位*/
    private BigDecimal upperLimit = new BigDecimal("0");        /* 上限值*/
    private BigDecimal lowerLimit = new BigDecimal("0");        /* 下限值*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = "BDAS";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String isFaultHandle = " ";        /* 是否故障处理 */

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("exceptionContactId");
        eiColumn.setDescName("异常信息联络单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("exceptionSource");
        eiColumn.setDescName("异常信息来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceCode");
        eiColumn.setDescName("分部设备代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deviceName");
        eiColumn.setDescName("分部设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("eArchivesNo");
        eiColumn.setDescName("设备档案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("equipmentName");
        eiColumn.setDescName("设备名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckContent");
        eiColumn.setDescName("点检内容");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckMethod");
        eiColumn.setDescName("点检方法");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckStandardType");
        eiColumn.setDescName("点检标准类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("judgmentStandard");
        eiColumn.setDescName("判断标准");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isOverhaulHandle");
        eiColumn.setDescName("是否检修处理");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("temporaryMeasures");
        eiColumn.setDescName("临时措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualsRemark");
        eiColumn.setDescName("点检实绩");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handleMeasures");
        eiColumn.setDescName("处理措施");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("procResult");
        eiColumn.setDescName("处理结果");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spotCheckImplemente");
        eiColumn.setDescName("点检实施方");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("exceptionStatus");
        eiColumn.setDescName("点检异常息状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("measureId");
        eiColumn.setDescName("计量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("upperLimit");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("上限值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lowerLimit");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("下限值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isFaultHandle");
        eiColumn.setDescName("是否故障处理");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tvgdm0501() {
        initMetaData();
    }

    /**
     * get the exceptionContactId - 异常信息联络单号
     *
     * @return the exceptionContactId
     */
    public String getExceptionContactId() {
        return this.exceptionContactId;
    }

    /**
     * set the exceptionContactId - 异常信息联络单号
     */
    public void setExceptionContactId(String exceptionContactId) {
        this.exceptionContactId = exceptionContactId;
    }

    /**
     * get the exceptionSource - 异常信息来源
     *
     * @return the exceptionSource
     */
    public String getExceptionSource() {
        return this.exceptionSource;
    }

    /**
     * set the exceptionSource - 异常信息来源
     */
    public void setExceptionSource(String exceptionSource) {
        this.exceptionSource = exceptionSource;
    }

    /**
     * get the deviceCode - 分部设备代码
     *
     * @return the deviceCode
     */
    public String getDeviceCode() {
        return this.deviceCode;
    }

    /**
     * set the deviceCode - 分部设备代码
     */
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    /**
     * get the deviceName - 分部设备名称
     *
     * @return the deviceName
     */
    public String getDeviceName() {
        return this.deviceName;
    }

    /**
     * set the deviceName - 分部设备名称
     */
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * get the eArchivesNo - 设备档案编号
     *
     * @return the eArchivesNo
     */
    public String getEArchivesNo() {
        return this.eArchivesNo;
    }

    /**
     * set the eArchivesNo - 设备档案编号
     */
    public void setEArchivesNo(String eArchivesNo) {
        this.eArchivesNo = eArchivesNo;
    }

    /**
     * get the equipmentName - 设备名称
     *
     * @return the equipmentName
     */
    public String getEquipmentName() {
        return this.equipmentName;
    }

    /**
     * set the equipmentName - 设备名称
     */
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    /**
     * get the spotCheckContent - 点检内容
     *
     * @return the spotCheckContent
     */
    public String getSpotCheckContent() {
        return this.spotCheckContent;
    }

    /**
     * set the spotCheckContent - 点检内容
     */
    public void setSpotCheckContent(String spotCheckContent) {
        this.spotCheckContent = spotCheckContent;
    }

    /**
     * get the spotCheckMethod - 点检方法
     *
     * @return the spotCheckMethod
     */
    public String getSpotCheckMethod() {
        return this.spotCheckMethod;
    }

    /**
     * set the spotCheckMethod - 点检方法
     */
    public void setSpotCheckMethod(String spotCheckMethod) {
        this.spotCheckMethod = spotCheckMethod;
    }

    /**
     * get the spotCheckStandardType - 点检标准类型
     *
     * @return the spotCheckStandardType
     */
    public String getSpotCheckStandardType() {
        return this.spotCheckStandardType;
    }

    /**
     * set the spotCheckStandardType - 点检标准类型
     */
    public void setSpotCheckStandardType(String spotCheckStandardType) {
        this.spotCheckStandardType = spotCheckStandardType;
    }

    /**
     * get the judgmentStandard - 判断标准
     *
     * @return the judgmentStandard
     */
    public String getJudgmentStandard() {
        return this.judgmentStandard;
    }

    /**
     * set the judgmentStandard - 判断标准
     */
    public void setJudgmentStandard(String judgmentStandard) {
        this.judgmentStandard = judgmentStandard;
    }

    /**
     * get the isOverhaulHandle - 是否检修处理
     *
     * @return the isOverhaulHandle
     */
    public String getIsOverhaulHandle() {
        return this.isOverhaulHandle;
    }

    /**
     * set the isOverhaulHandle - 是否检修处理
     */
    public void setIsOverhaulHandle(String isOverhaulHandle) {
        this.isOverhaulHandle = isOverhaulHandle;
    }

    /**
     * get the temporaryMeasures - 临时措施
     *
     * @return the temporaryMeasures
     */
    public String getTemporaryMeasures() {
        return this.temporaryMeasures;
    }

    /**
     * set the temporaryMeasures - 临时措施
     */
    public void setTemporaryMeasures(String temporaryMeasures) {
        this.temporaryMeasures = temporaryMeasures;
    }

    /**
     * get the actualsRemark - 点检实绩
     *
     * @return the actualsRemark
     */
    public String getActualsRemark() {
        return this.actualsRemark;
    }

    /**
     * set the actualsRemark - 点检实绩
     */
    public void setActualsRemark(String actualsRemark) {
        this.actualsRemark = actualsRemark;
    }

    /**
     * get the handleMeasures - 处理措施
     *
     * @return the handleMeasures
     */
    public String getHandleMeasures() {
        return this.handleMeasures;
    }

    /**
     * set the handleMeasures - 处理措施
     */
    public void setHandleMeasures(String handleMeasures) {
        this.handleMeasures = handleMeasures;
    }

    /**
     * get the procResult - 处理结果
     *
     * @return the procResult
     */
    public String getProcResult() {
        return this.procResult;
    }

    /**
     * set the procResult - 处理结果
     */
    public void setProcResult(String procResult) {
        this.procResult = procResult;
    }

    /**
     * get the spotCheckImplemente - 点检实施方
     *
     * @return the spotCheckImplemente
     */
    public String getSpotCheckImplemente() {
        return this.spotCheckImplemente;
    }

    /**
     * set the spotCheckImplemente - 点检实施方
     */
    public void setSpotCheckImplemente(String spotCheckImplemente) {
        this.spotCheckImplemente = spotCheckImplemente;
    }

    /**
     * get the exceptionStatus - 点检异常息状态
     *
     * @return the exceptionStatus
     */
    public String getExceptionStatus() {
        return this.exceptionStatus;
    }

    /**
     * set the exceptionStatus - 点检异常息状态
     */
    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }

    /**
     * get the measureId - 计量单位
     *
     * @return the measureId
     */
    public String getMeasureId() {
        return this.measureId;
    }

    /**
     * set the measureId - 计量单位
     */
    public void setMeasureId(String measureId) {
        this.measureId = measureId;
    }

    /**
     * get the upperLimit - 上限值
     *
     * @return the upperLimit
     */
    public BigDecimal getUpperLimit() {
        return this.upperLimit;
    }

    /**
     * set the upperLimit - 上限值
     */
    public void setUpperLimit(BigDecimal upperLimit) {
        this.upperLimit = upperLimit;
    }

    /**
     * get the lowerLimit - 下限值
     *
     * @return the lowerLimit
     */
    public BigDecimal getLowerLimit() {
        return this.lowerLimit;
    }

    /**
     * set the lowerLimit - 下限值
     */
    public void setLowerLimit(BigDecimal lowerLimit) {
        this.lowerLimit = lowerLimit;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the isFaultHandle - 是否故障处理
     *
     * @return the isFaultHandle
     */
    public String getIsFaultHandle() {
        return this.isFaultHandle;
    }

    /**
     * set the isFaultHandle - 是否故障处理
     */
    public void setIsFaultHandle(String isFaultHandle) {
        this.isFaultHandle = isFaultHandle;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setExceptionContactId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("exceptionContactId")), exceptionContactId));
        setExceptionSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("exceptionSource")), exceptionSource));
        setDeviceCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceCode")), deviceCode));
        setDeviceName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deviceName")), deviceName));
        setEArchivesNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("eArchivesNo")), eArchivesNo));
        setEquipmentName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("equipmentName")), equipmentName));
        setSpotCheckContent(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckContent")), spotCheckContent));
        setSpotCheckMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckMethod")), spotCheckMethod));
        setSpotCheckStandardType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckStandardType")), spotCheckStandardType));
        setJudgmentStandard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("judgmentStandard")), judgmentStandard));
        setIsOverhaulHandle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isOverhaulHandle")), isOverhaulHandle));
        setTemporaryMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("temporaryMeasures")), temporaryMeasures));
        setActualsRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actualsRemark")), actualsRemark));
        setHandleMeasures(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handleMeasures")), handleMeasures));
        setProcResult(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("procResult")), procResult));
        setSpotCheckImplemente(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spotCheckImplemente")), spotCheckImplemente));
        setExceptionStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("exceptionStatus")), exceptionStatus));
        setMeasureId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("measureId")), measureId));
        setUpperLimit(NumberUtils.toBigDecimal(StringUtils.toString(map.get("upperLimit")), upperLimit));
        setLowerLimit(NumberUtils.toBigDecimal(StringUtils.toString(map.get("lowerLimit")), lowerLimit));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setIsFaultHandle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isFaultHandle")), isFaultHandle));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("exceptionContactId", StringUtils.toString(exceptionContactId, eiMetadata.getMeta("exceptionContactId")));
        map.put("exceptionSource", StringUtils.toString(exceptionSource, eiMetadata.getMeta("exceptionSource")));
        map.put("deviceCode", StringUtils.toString(deviceCode, eiMetadata.getMeta("deviceCode")));
        map.put("deviceName", StringUtils.toString(deviceName, eiMetadata.getMeta("deviceName")));
        map.put("eArchivesNo", StringUtils.toString(eArchivesNo, eiMetadata.getMeta("eArchivesNo")));
        map.put("equipmentName", StringUtils.toString(equipmentName, eiMetadata.getMeta("equipmentName")));
        map.put("spotCheckContent", StringUtils.toString(spotCheckContent, eiMetadata.getMeta("spotCheckContent")));
        map.put("spotCheckMethod", StringUtils.toString(spotCheckMethod, eiMetadata.getMeta("spotCheckMethod")));
        map.put("spotCheckStandardType", StringUtils.toString(spotCheckStandardType, eiMetadata.getMeta("spotCheckStandardType")));
        map.put("judgmentStandard", StringUtils.toString(judgmentStandard, eiMetadata.getMeta("judgmentStandard")));
        map.put("isOverhaulHandle", StringUtils.toString(isOverhaulHandle, eiMetadata.getMeta("isOverhaulHandle")));
        map.put("temporaryMeasures", StringUtils.toString(temporaryMeasures, eiMetadata.getMeta("temporaryMeasures")));
        map.put("actualsRemark", StringUtils.toString(actualsRemark, eiMetadata.getMeta("actualsRemark")));
        map.put("handleMeasures", StringUtils.toString(handleMeasures, eiMetadata.getMeta("handleMeasures")));
        map.put("procResult", StringUtils.toString(procResult, eiMetadata.getMeta("procResult")));
        map.put("spotCheckImplemente", StringUtils.toString(spotCheckImplemente, eiMetadata.getMeta("spotCheckImplemente")));
        map.put("exceptionStatus", StringUtils.toString(exceptionStatus, eiMetadata.getMeta("exceptionStatus")));
        map.put("measureId", StringUtils.toString(measureId, eiMetadata.getMeta("measureId")));
        map.put("upperLimit", StringUtils.toString(upperLimit, eiMetadata.getMeta("upperLimit")));
        map.put("lowerLimit", StringUtils.toString(lowerLimit, eiMetadata.getMeta("lowerLimit")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("isFaultHandle", StringUtils.toString(isFaultHandle, eiMetadata.getMeta("isFaultHandle")));

        return map;

    }
}