drop table TMEDV0005;

create table TMEDV0005
(
    E_ARCHIVES_NO   VARCHAR(20)    default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME  VARCHAR(200)   default ' '    not null comment '设备名称',
    -- add 班组 班次
    PACK_ID         VARCHAR(20)    default ' '    not null comment '捆包号',
    PART_ID         VARCHAR(20)    default ' '    not null comment '物料号',
    PROCESS_ORDER_ID         VARCHAR(32)    default ' '    not null comment '工单号',
    PROCEDURE_ID    VARCHAR(32)    default ' '    not null comment '工序段ID',
    PROCEDURE_NAME  VARCHAR(64)    default ' '    not null comment '工序名称',
    PROCEDURE_TIME  DECIMAL(20, 8) default 0      not null comment '工序时间',
    START_TIME  DECIMAL(20, 8) default 0      not null comment '开始时间',
    STOP_TIME  DECIMAL(20, 8) default 0      not null comment '结束时间',
    FAULT_FLAG  INT DEFAULT 0 NOT NULL  COMMENT '异常flag',
    TIME_TYPE  INT  NOT NULL COMMENT '时间类型',
    WEIGHT DECIMAL(2,2) not null comment '权重',
    -- 固定字段
    UUID            VARCHAR(32)                   NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)    DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)    DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)    DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)     DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID),
    FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO),
    FOREIGN KEY (PROCEDURE_ID) REFERENCES TMEDV0009(PROCEDURE_ID)

) COMMENT ='标准工时履历表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;