<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-18 16:32:33
   		Version :  1.0
		tableName :meli.tlids0903 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 WAREHOUSE_CODE  VARCHAR, 
		 WAREHOUSE_NAME  VARCHAR, 
		 AREA_TYPE  VARCHAR, 
		 AREA_CODE  VARCHAR, 
		 AREA_NAME  VARCHAR, 
		 PACK_ID  VARCHAR, 
		 UNITED_PACK_ID  VARCHAR, 
		 LABEL_ID  VARCHAR, 
		 NET_WEIGHT  DECIMAL, 
		 GRO<PERSON>_WEIGHT  DECIMAL, 
		 CRANE_OPERATION_WEIGHT  DECIMAL, 
		 QUANTITY  INTEGER, 
		 POS_DIR_CODE  VARCHAR, 
		 CR<PERSON>E_RESULT_ID  VARCHAR, 
		 ACTION_FLAG  VARCHAR, 
		 INNER_OUTTER_PLATE_FLAG  VARCHAR, 
		 STATUS  VARCHAR   NOT NULL, 
		 X_POSITION  VARCHAR, 
		 Y_POSITION  VARCHAR, 
		 Z_POSITION  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL, 
		 RESUME_CREATION_TIME  VARCHAR
	-->
<sqlMap namespace="LIDS0903">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.ds.domain.LIDS0903">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				WAREHOUSE_CODE	as "warehouseCode",  <!-- 仓库代码 -->
				WAREHOUSE_NAME	as "warehouseName",  <!-- 仓库名称 -->
				AREA_TYPE	as "areaType",  <!-- 区域类型 -->
				AREA_CODE	as "areaCode",  <!-- 区域代码/库位代码 -->
				AREA_NAME	as "areaName",  <!-- 区域名称/库位名称 -->
				PACK_ID	as "packId",  <!-- 捆包号 -->
				UNITED_PACK_ID	as "unitedPackId",  <!-- 并包号 -->
				LABEL_ID	as "labelId",  <!-- 标签号 -->
				NET_WEIGHT	as "netWeight",  <!-- 净重 -->
				GROSS_WEIGHT	as "grossWeight",  <!-- 毛重 -->
				CRANE_OPERATION_WEIGHT	as "craneOperationWeight",  <!-- 吊装重量 -->
				QUANTITY	as "quantity",  <!-- 数量 -->
				POS_DIR_CODE	as "posDirCode",  <!-- 层数标记 -->
				CRANE_RESULT_ID	as "craneResultId",  <!-- 行车实绩单号 -->
				ACTION_FLAG	as "actionFlag",  <!-- 板卷标记(0板 1卷) -->
				INNER_OUTTER_PLATE_FLAG	as "innerOutterPlateFlag",  <!-- 内外版标记 -->
				STATUS	as "status",  <!-- 状态 -->
				X_POSITION	as "x_position",  <!-- X轴坐标 -->
				Y_POSITION	as "y_position",  <!-- X轴坐标 -->
				Z_POSITION	as "z_position",  <!-- Y轴坐标 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid",  <!-- ID -->
				RESUME_CREATION_TIME	as "resumeCreationTime" <!-- 履历表插入时间 -->
		FROM meli.tlids0903 WHERE 1=1
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0903 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			WAREHOUSE_CODE = #warehouseCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseName">
			WAREHOUSE_NAME = #warehouseName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaType">
			AREA_TYPE = #areaType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaCode">
			AREA_CODE = #areaCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="areaName">
			AREA_NAME = #areaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="packId">
			PACK_ID = #packId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitedPackId">
			UNITED_PACK_ID = #unitedPackId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="labelId">
			LABEL_ID = #labelId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="netWeight">
			NET_WEIGHT = #netWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="grossWeight">
			GROSS_WEIGHT = #grossWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneOperationWeight">
			CRANE_OPERATION_WEIGHT = #craneOperationWeight#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="quantity">
			QUANTITY = #quantity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="posDirCode">
			POS_DIR_CODE = #posDirCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneResultId">
			CRANE_RESULT_ID = #craneResultId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="actionFlag">
			ACTION_FLAG = #actionFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="innerOutterPlateFlag">
			INNER_OUTTER_PLATE_FLAG = #innerOutterPlateFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="x_position">
			X_POSITION = #x_position#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="y_position">
			Y_POSITION = #y_position#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="z_position">
			Z_POSITION = #z_position#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="resumeCreationTime">
			RESUME_CREATION_TIME = #resumeCreationTime#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0903 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										WAREHOUSE_CODE,  <!-- 仓库代码 -->
										WAREHOUSE_NAME,  <!-- 仓库名称 -->
										AREA_TYPE,  <!-- 区域类型 -->
										AREA_CODE,  <!-- 区域代码/库位代码 -->
										AREA_NAME,  <!-- 区域名称/库位名称 -->
										PACK_ID,  <!-- 捆包号 -->
										UNITED_PACK_ID,  <!-- 并包号 -->
										LABEL_ID,  <!-- 标签号 -->
										NET_WEIGHT,  <!-- 净重 -->
										GROSS_WEIGHT,  <!-- 毛重 -->
										CRANE_OPERATION_WEIGHT,  <!-- 吊装重量 -->
										QUANTITY,  <!-- 数量 -->
										POS_DIR_CODE,  <!-- 层数标记 -->
										CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
										ACTION_FLAG,  <!-- 板卷标记(0板 1卷) -->
										INNER_OUTTER_PLATE_FLAG,  <!-- 内外版标记 -->
										STATUS,  <!-- 状态 -->
										X_POSITION,  <!-- X轴坐标 -->
										Y_POSITION,  <!-- X轴坐标 -->
										Z_POSITION,  <!-- Y轴坐标 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID,  <!-- ID -->
										RESUME_CREATION_TIME  <!-- 履历表插入时间 -->
										)		 
	    VALUES (#segNo#, #unitCode#, #warehouseCode#, #warehouseName#, #areaType#, #areaCode#, #areaName#, #packId#, #unitedPackId#, #labelId#, #netWeight#, #grossWeight#, #craneOperationWeight#, #quantity#, #posDirCode#, #craneResultId#, #actionFlag#, #innerOutterPlateFlag#, #status#, #x_position#, #y_position#, #z_position#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#, #resumeCreationTime#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0903 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlids0903 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库代码 -->  
					WAREHOUSE_NAME	= #warehouseName#,   <!-- 仓库名称 -->  
					AREA_TYPE	= #areaType#,   <!-- 区域类型 -->  
					AREA_CODE	= #areaCode#,   <!-- 区域代码/库位代码 -->  
					AREA_NAME	= #areaName#,   <!-- 区域名称/库位名称 -->  
					PACK_ID	= #packId#,   <!-- 捆包号 -->  
					UNITED_PACK_ID	= #unitedPackId#,   <!-- 并包号 -->  
					LABEL_ID	= #labelId#,   <!-- 标签号 -->  
					NET_WEIGHT	= #netWeight#,   <!-- 净重 -->  
					GROSS_WEIGHT	= #grossWeight#,   <!-- 毛重 -->  
					CRANE_OPERATION_WEIGHT	= #craneOperationWeight#,   <!-- 吊装重量 -->  
					QUANTITY	= #quantity#,   <!-- 数量 -->  
					POS_DIR_CODE	= #posDirCode#,   <!-- 层数标记 -->  
					CRANE_RESULT_ID	= #craneResultId#,   <!-- 行车实绩单号 -->  
					ACTION_FLAG	= #actionFlag#,   <!-- 板卷标记(0板 1卷) -->  
					INNER_OUTTER_PLATE_FLAG	= #innerOutterPlateFlag#,   <!-- 内外版标记 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					X_POSITION	= #x_position#,   <!-- X轴坐标 -->  
					Y_POSITION	= #y_position#,   <!-- X轴坐标 -->  
					Z_POSITION	= #z_position#,   <!-- Y轴坐标 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					UUID	= #uuid#,   <!-- ID -->  
					RESUME_CREATION_TIME	= #resumeCreationTime#  <!-- 履历表插入时间 -->  
			WHERE 	
	</update>


	<!--	根据传入捆包号，插入库存表原数据-->
	<insert id="inert0903InterfaceList">
		INSERT INTO meli.tlids0903
		SELECT *,DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')
		FROM meli.tlids0901
		WHERE SEG_NO = #segNo#
		AND pack_id IN ($packIds$)
		<isEmpty property="packIds">
			AND 1 = 0 <!-- 永远不满足的条件 -->
		</isEmpty>
		AND STATUS = '10'
		AND DEL_FLAG = '0'
	</insert>

	<!--清空库存捆包对应的区域类型，区域/库位，层数标记，xyz轴信息-->
	<update id="resetPackXyz" parameterClass="java.util.HashMap">
		UPDATE meli.tlids0901 t1
		JOIN (
		SELECT
		UUID,
		<isEqual property="actionFlag" compareValue="0">
			@row_number := IF(@initialized IS NULL,
			IF(@row_number := 1, @initialized := 1, 1),
			@row_number + 1) AS new_pos_dir_code
		</isEqual>
		<isEqual property="actionFlag" compareValue="1">
			NULL AS new_pos_dir_code
		</isEqual>
		FROM (
		SELECT UUID
		FROM meli.tlids0901
		WHERE SEG_NO = #segNo#
		AND pack_id IN ($packIds$)
		AND STATUS = '10'
		AND DEL_FLAG = '0'
		ORDER BY POS_DIR_CODE
		) sub
		<isEqual property="actionFlag" compareValue="0">
			JOIN (SELECT @row_number := 0, @initialized := NULL) init
		</isEqual>
		) t2 ON t1.UUID = t2.UUID
		SET
		t1.AREA_TYPE = '',
		t1.AREA_CODE = '',
		t1.AREA_NAME = '',
		<isEqual property="actionFlag" compareValue="0">
			t1.POS_DIR_CODE = t2.new_pos_dir_code
		</isEqual>
		<isEqual property="actionFlag" compareValue="1">
			t1.POS_DIR_CODE = ''
		</isEqual>,
		t1.x_position = NULL,
		t1.y_position = NULL,
		t1.z_position = NULL
		<isNotEmpty prepend="," property="craneResultId">
			t1.CRANE_RESULT_ID = #craneResultId#
		</isNotEmpty>
		WHERE
		t1.SEG_NO = #segNo#
		AND t1.pack_id IN ($packIds$)
		AND t1.STATUS = '10'
		AND t1.DEL_FLAG = '0'
	</update>

</sqlMap>