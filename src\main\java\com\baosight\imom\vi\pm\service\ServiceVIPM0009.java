package com.baosight.imom.vi.pm.service;

import com.baosight.imom.common.utils.*;
import com.baosight.imom.vi.pm.domain.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import org.apache.commons.collections.CollectionUtils;


import java.util.*;
import java.util.stream.Collectors;

public class ServiceVIPM0009 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VIPM0009().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, VIPM0009.QUERY, new VIPM0009());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 勾选工单生成行车作业清单
     *
     * @param inInfo
     * @return outInfo
     */
    public EiInfo createCraneOrderFromLoading(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<Map> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
            if (CollectionUtils.isEmpty(resultList)) {
                throw new PlatException("请勾选工单！");
            }

            StringBuffer erorMessage = new StringBuffer();
            //循环生成行车作业清单
            for (Map map : resultList) {
                Map queryMap = new HashMap();
                queryMap.put("segNo", map.get("segNo"));
                queryMap.put("processOrderId", map.get("processOrderId"));
                queryMap.put("processDemandId", map.get("processDemandId"));
                queryMap.put("processDemandSubId", map.get("processDemandSubId"));
                //查询产出表数据
                List<VIPM0007> demandOutputList = dao.query(VIPM0007.QUERY, queryMap);
                if(CollectionUtils.isEmpty(demandOutputList)){
                 throw new PlatException("未找到工单产出信息:" + map.get("processOrderId"));
                }
                //查询投料表数据
                List<VIPM0008> demandMaterailList = dao.query(VIPM0008.QUERY, queryMap);
                if(CollectionUtils.isEmpty(demandMaterailList)){
                    throw new PlatException("未找到工单投料信息:" + map.get("processOrderId"));
                }
                //查询工单数据
                List<VIPM0009> demandProcessList = dao.query(VIPM0009.QUERY, queryMap);
                if(CollectionUtils.isEmpty(demandProcessList)){
                    throw new PlatException("未找到工单信息:" + map.get("processOrderId"));
                }

                EiInfo sendInfo = new EiInfo();
                sendInfo.set("demandOutputList", demandOutputList.stream()
                        .map(VIPM0007::toMap)
                        .collect(Collectors.toList()));
                sendInfo.set("demandMaterailList", demandMaterailList.stream()
                        .map(VIPM0008::toMap)
                        .collect(Collectors.toList()));
                sendInfo.set("demandProcessList", demandProcessList.stream()
                        .map(VIPM0009::toMap)
                        .collect(Collectors.toList()));
                sendInfo.set(EiConstant.serviceName, "VIPMInterfaces");
                sendInfo.set(EiConstant.methodName, "CreateCraneOrderId");
                outInfo = XLocalManager.callNoTx(sendInfo);
                if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    erorMessage.append("工单：").append(map.get("processOrderId")).append("生成行车作业清单失败,原因：").append(outInfo.getMsg()).append("\r\n");
                    //打印日志到elk
                    log("VIPM0009选择工单的数据生成行车作业清单失败，接口返回报错：" + outInfo.getMsg());
                    //输出到应用日志
                    System.out.println("VIPM0009选择工单数据生成行车作业清单失败，接口返回报错：" + outInfo.getMsg());
                }
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("生成成功！" + erorMessage);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
