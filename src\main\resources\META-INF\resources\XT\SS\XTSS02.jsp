<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="业务单元管理">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-segNo" cname="业务单元代码" disabled="false" required="true" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-segFullName" cname="业务单元名称" disabled="false" required="true" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" disabled="false" required="true" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-fatherSegNo" cname="上级业务单元" disabled="false" required="true" colWidth="3"/>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="结果集">
        <EF:EFGrid blockId="result" autoBind="false" autoDraw="no" needAuth="true" readonly="true"
                   rowNo="true" checkMode="single" isFloat="true" >
            <EF:EFColumn ename="segNo" cname="业务单元编号" width="120" enable="false" align="center" required="true" />
            <EF:EFColumn ename="segFullName" cname="业务单元名称" width="120" enable="false" align="center" required="true"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" width="120" enable="false" align="center" required="true"/>
            <%--<EF:EFColumn ename="segLevel" cname="业务单元类型" width="120" enable="false" align="center" required="true" />--%>
            <EF:EFComboColumn ename="segLevel" cname="业务单元类型" defaultValue="10" align="center"
                              enable="false" width="120">
                <EF:EFCodeOption codeName="S001"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="companyCode" cname="公司别代码" width="120" enable="false" align="center" required="true"/>
            <EF:EFColumn ename="fatherSegNo" cname="上级业务单元" width="120" enable="false" align="center" required="true"/>
<%--            <EF:EFColumn ename="ifVirtualOrg" cname="真实/虚拟组织" width="120" enable="false" align="center" required="true"/>--%>
            <EF:EFComboColumn ename="ifVirtualOrg" cname="真实/虚拟组织" defaultValue="10" align="center"
                              enable="false" width="120">
                <EF:EFCodeOption codeName="S001"/>
            </EF:EFComboColumn>
           <%-- <EF:EFColumn ename="segStatus" cname="业务单元状态" width="120" enable="false" align="center" required="true"/>--%>
            <EF:EFComboColumn ename="segStatus" cname="业务单元状态" defaultValue="10" align="center"
                              enable="false" width="120">
                <EF:EFCodeOption codeName="S001"/>
            </EF:EFComboColumn>
            <EF:EFComboColumn ename="factoryType" cname="加工中心细分类" defaultValue="10" align="center"
                              enable="false" width="120">
                <EF:EFCodeOption codeName="S001"/>
            </EF:EFComboColumn>
<%--            <EF:EFColumn ename="factoryType" cname="加工中心细分类" width="120" enable="false" align="center" required="true"/>--%>
        </EF:EFGrid>
    </EF:EFRegion>


</EF:EFPage>