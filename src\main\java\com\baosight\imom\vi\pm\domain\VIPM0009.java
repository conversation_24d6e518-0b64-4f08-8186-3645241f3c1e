/**
* Generate time : 2024-12-02 10:18:08
* Version : 1.0
*/
package com.baosight.imom.vi.pm.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* VIPM0009
* table comment : 生产需求单工序表
*/
public class VIPM0009 extends DaoEPBase {

                public static final String QUERY = "VIPM0009.query";
                public static final String INSERT = "VIPM0009.insert";
                public static final String UPDATE = "VIPM0009.update";
                public static final String UPDATE_STATUS = "VIPM0009.updateStatus";/* 修改工单状态*/
                public static final String DELETE = "VIPM0009.delete";
                public static final String QUERY_NEXT_MOULD_ID = "VIPM0009.queryNextMouldId";
                private String unitCode = " ";		/* 业务单元代码*/
                private String segName = " ";		/* 业务单元简称*/
                private String processOrderId = " ";		/* 生产工单号*/
                private String processDemandId = " ";		/* 生产需求单号*/
                private String processDemandSubId = " ";		/* 生产需求单子项号*/
                private String processCategory = " ";		/* 工序大类代码*/
                private String afterPOrderId = " ";		/* 后道工单号*/
                private String businessType = " ";		/* 业务类型*/
                private String machineCode = " ";		/* 机组代码*/
                private String knifeId = " ";		/* 排刀号*/
                private String scheduleStartDate = " ";		/* 排产开始时间*/
                private String scheduleEndDate = " ";		/* 排产结束时间*/
                private String processDemandProcessStatus = " ";		/* 生产需求单工序表状态*/
                private String processFeePriceType = " ";		/* 加工费计价类型*/
                private String mproviderId = " ";		/* 加工单位代码*/
                private String mproviderName = " ";		/* 加工单位名称*/
                private String agreementId = " ";		/* 加工协议号*/
                private String agreementSubid = " ";		/* 加工协议子项号*/
                private BigDecimal taxRate = new BigDecimal("0");		/* 税率*/
                private BigDecimal processFeePrice = new BigDecimal("0");		/* 加工费单价(不含税)*/
                private BigDecimal processFeePriceTax = new BigDecimal("0");		/* 加工费单价(含税)*/
                private String remark = " ";		/* 备注*/
                private String canReturnMaterial = " ";		/* 是否可以退料*/
                private String scheduleSerialNum = " ";		/* 排产序号*/
                private String productionLock = " ";		/* 封锁产出品标记*/
                private Long processTime = 0L;	/* 生产时间(分钟)*/
                private Long prepareTime = 0L;	/* 准备时间(分钟)*/
                private Long deliveryTime = 0L;	/* 交接时间(分钟)*/
                private String ifFirst = " ";		/* 是否首道*/
                private String ifRoll = " ";		/* 原料形状*/
                private String auditorId = " ";		/* 审核人工号*/
                private String auditTime = " ";		/* 审核时间*/
                private Long scheduleSerialSubnum = -1L;		/* 排产子序号*/
                private String deliveryDate = " ";		/* 加工完成时间*/
                private String processFinStartDate = " ";		/* 生产实际开始时间*/
                private String processFinEndDate = " ";		/* 生产实际结束时间*/
                private String productRemainQty = " ";		/* 加工剩余数量*/
                private BigDecimal productRemainWeight = new BigDecimal("0");		/* 加工剩余重量*/
                private String scheduleLockFlag = " ";		/* 排产锁定标记*/
                private Integer materialRate = Integer.valueOf(0);		/* 原料成材率*/
                private String ifMaterail = " ";		/* 是否强制指定原料卷*/
                private String ifArrangeOnly = " ";		/* 是否单排刀*/
                private String preProcessDemandSubId = " ";		/* 上一道生产需求单子项号*/
                private String processIfSettleFinish = " ";		/* 生产加工是否结算完成*/
                private String processIfSettle = " ";		/* 生产加工是否结算加工费*/
                private String suitVoucherNum = " ";		/* 套裁封锁单据号*/
                private String dataSource = " ";		/* 数据来源*/
                private String printBatchId = " ";		/* 打印批次号*/
                private Integer rawMaterialTransferTime = Integer.valueOf(0);		/* 原料转运时间*/
                private String combineNo = " ";		/* 归并号*/
                private String synFlag = " ";		/* 协同标记*/
                private Long allocTime = 0L;	/* 分配时间（分钟）*/
                private String scheduleIssueDate = " ";		/* 排产下发时间*/
                private String scheduleIssuePerson = " ";		/* 排产下发人*/
                private String outprocessReasons = " ";		/* 委外原因*/
                private String deliveryDateRemark = " ";		/* 交期备注*/
                private String specialRemark = " ";		/* 特殊备注*/
                private String ifCheckAssay = " ";		/* 是否检化验*/
                private Integer yield = Integer.valueOf(0);		/* 成材率*/
                private Long theoreticalImpulseTimes = 0L;	/* 理论冲次数*/
                private String craftCode = " ";		/* 工艺单号*/
                private String mouldId = " ";		/* 模具ID*/
                private Long actualImpulseTimes = 0L;	/* 实际冲次数*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String tenantUser = " ";		/* 租户*/
                private String processFeeCalculateMethod = " ";		
                private String segNo = " ";		/* 业务账套*/
                private String uuid = " ";		/* ID*/
                private String agreementManageType = " ";		/* 委外协议管理方式*/
                private String ifPlanFlag = " ";		/* 是否参与排产*/
                private String createPerson = " ";		/* 制单人代码*/
                private Integer processSpeed = Integer.valueOf(0);		/* 加工速度(米/分钟)*/
                private String auditorName = " ";		/* 审核人姓名*/
                private String previousProcessOrderId = " ";		/* 上道工序/工单*/
                private String nextProcessOrderId = " ";		/* 下道工序/工单*/
                private String ifNeedSuitCut = " ";		/* 是否需要套裁方案*/
                private String tradeCode = " ";		/* 贸易方式*/
                private Integer processRate = Integer.valueOf(0);		/* 合格品率*/
                private String processId = " ";		/* 流程ID*/
                private String auditOpnion = " ";		/* 审核意见*/
                private String businessFrom = "00";		/* 业务来源*/
                private String teamId = " ";		/* 班组*/
                private String workingShift = " ";		/* 班次*/
                private Long sph = 0L;	/* sph每小时加工片数(落料和精剪）*/
                private String sgmFlag = " ";		/* SGM标记*/
                private String auditFlag = " ";		/* 审核标记*/
                private String apprStatus = " ";		/* 审批状态*/
                private String outApprStatus = "";		/* 委外审批状态*/
                private String outAuditFlag = "";		/* 委外审核标记*/
                private String outAuditOpnion = "";		/* 委外审核意见*/
                private String outAuditTime = "";		/* 委外审核时间*/
                private String outAuditorId = "";		/* 委外审核人工号*/
                private String outAuditorName = "";		/* 委外审核人姓名*/
                private String outProcessId = "";		/* 委外流程ID*/
                private String ifSubstitAudit = " ";		/* 是否需要替代审批*/
                private String ifYieldAudit = " ";		/* 是否需要成材率审批*/
                private String ifOutprocessAudit = " ";		/* 是否需要委外工单审批*/
                private String processSynTriggerType = " ";		/* 协同触发方式*/
                private String ifSyn = " ";		/* 是否协同*/
                private String processSynReceive = " ";		/* 协同接收方*/
                private Integer processSeqId = 0;		/* 加工顺序*/
                private String ifNoProcessingAgreement = " ";		/* 无加工协议（委外）*/
                private String orderConfirmTime = "";		/* 生产工单确认时间*/
                private BigDecimal planRate = new BigDecimal("0");		/* 工艺成材率*/
                private String synSettleFlag = " ";		/* 委托方结算标记*/
                private String knifePrintBatchId = " ";		/* 机组组刀打印批次号*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("unitCode");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandId");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandSubId");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("afterPOrderId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("后道工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("knifeId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("排刀号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleStartDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("排产开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleEndDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("排产结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandProcessStatus");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产需求单工序表状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeePriceType");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("加工费计价类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mproviderId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mproviderName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("加工单位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("agreementId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工协议号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("agreementSubid");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("加工协议子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("taxRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("税率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeePrice");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工费单价(不含税)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeePriceTax");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工费单价(含税)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(3000);
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("canReturnMaterial");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("是否可以退料");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleSerialNum");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("排产序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productionLock");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("封锁产出品标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processTime");
        eiColumn.setDescName("生产时间(分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prepareTime");
        eiColumn.setDescName("准备时间(分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryTime");
        eiColumn.setDescName("交接时间(分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifFirst");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否首道");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifRoll");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("原料形状");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditorId");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("审核人工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("审核时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleSerialSubnum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("排产子序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryDate");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("加工完成时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFinStartDate");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("生产实际开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFinEndDate");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("生产实际结束时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productRemainQty");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工剩余数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productRemainWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工剩余重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleLockFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("排产锁定标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("原料成材率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifMaterail");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("是否强制指定原料卷");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifArrangeOnly");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否单排刀");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("preProcessDemandSubId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("上一道生产需求单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processIfSettleFinish");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("生产加工是否结算完成");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processIfSettle");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("生产加工是否结算加工费");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("suitVoucherNum");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("套裁封锁单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataSource");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("数据来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("printBatchId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("打印批次号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rawMaterialTransferTime");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("原料转运时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("combineNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("归并号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("synFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("协同标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocTime");
        eiColumn.setDescName("分配时间（分钟）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleIssueDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("排产下发时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scheduleIssuePerson");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("排产下发人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outprocessReasons");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("委外原因");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryDateRemark");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("交期备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specialRemark");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("特殊备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifCheckAssay");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否检化验");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("yield");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("成材率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("theoreticalImpulseTimes");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("理论冲次数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craftCode");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("工艺单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mouldId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("模具ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actualImpulseTimes");
        eiColumn.setDescName("实际冲次数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processFeeCalculateMethod");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName(" ");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("agreementManageType");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("委外协议管理方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifPlanFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否参与排产");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("createPerson");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("制单人代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSpeed");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("加工速度(米/分钟)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("审核人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("previousProcessOrderId");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("上道工序/工单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nextProcessOrderId");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("下道工序/工单");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifNeedSuitCut");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("是否需要套裁方案");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("贸易方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("合格品率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("流程ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditOpnion");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("审核意见");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessFrom");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("业务来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sph");
        eiColumn.setDescName("sph每小时加工片数(落料和精剪）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sgmFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("SGM标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("auditFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("审核标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("apprStatus");
        eiColumn.setFieldLength(16);
        eiColumn.setDescName("审批状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outApprStatus");
        eiColumn.setFieldLength(16);
        eiColumn.setDescName("委外审批状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outAuditFlag");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("委外审核标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outAuditOpnion");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("委外审核意见");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outAuditTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("委外审核时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outAuditorId");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("委外审核人工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outAuditorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("委外审核人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outProcessId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("委外流程ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifSubstitAudit");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否需要替代审批");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifYieldAudit");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否需要成材率审批");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifOutprocessAudit");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否需要委外工单审批");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSynTriggerType");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("协同触发方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifSyn");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否协同");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSynReceive");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("协同接收方");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSeqId");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("加工顺序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifNoProcessingAgreement");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("无加工协议（委外）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderConfirmTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("生产工单确认时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("planRate");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("工艺成材率");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("synSettleFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("委托方结算标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("knifePrintBatchId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("机组组刀打印批次号");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public VIPM0009() {
initMetaData();
}

        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the processOrderId - 生产工单号
        * @return the processOrderId
        */
        public String getProcessOrderId() {
        return this.processOrderId;
        }

        /**
        * set the processOrderId - 生产工单号
        */
        public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
        }
        /**
        * get the processDemandId - 生产需求单号
        * @return the processDemandId
        */
        public String getProcessDemandId() {
        return this.processDemandId;
        }

        /**
        * set the processDemandId - 生产需求单号
        */
        public void setProcessDemandId(String processDemandId) {
        this.processDemandId = processDemandId;
        }
        /**
        * get the processDemandSubId - 生产需求单子项号
        * @return the processDemandSubId
        */
        public String getProcessDemandSubId() {
        return this.processDemandSubId;
        }

        /**
        * set the processDemandSubId - 生产需求单子项号
        */
        public void setProcessDemandSubId(String processDemandSubId) {
        this.processDemandSubId = processDemandSubId;
        }
        /**
        * get the processCategory - 工序大类代码
        * @return the processCategory
        */
        public String getProcessCategory() {
        return this.processCategory;
        }

        /**
        * set the processCategory - 工序大类代码
        */
        public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
        }
        /**
        * get the afterPOrderId - 后道工单号
        * @return the afterPOrderId
        */
        public String getAfterPOrderId() {
        return this.afterPOrderId;
        }

        /**
        * set the afterPOrderId - 后道工单号
        */
        public void setAfterPOrderId(String afterPOrderId) {
        this.afterPOrderId = afterPOrderId;
        }
        /**
        * get the businessType - 业务类型
        * @return the businessType
        */
        public String getBusinessType() {
        return this.businessType;
        }

        /**
        * set the businessType - 业务类型
        */
        public void setBusinessType(String businessType) {
        this.businessType = businessType;
        }
        /**
        * get the machineCode - 机组代码
        * @return the machineCode
        */
        public String getMachineCode() {
        return this.machineCode;
        }

        /**
        * set the machineCode - 机组代码
        */
        public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
        }
        /**
        * get the knifeId - 排刀号
        * @return the knifeId
        */
        public String getKnifeId() {
        return this.knifeId;
        }

        /**
        * set the knifeId - 排刀号
        */
        public void setKnifeId(String knifeId) {
        this.knifeId = knifeId;
        }
        /**
        * get the scheduleStartDate - 排产开始时间
        * @return the scheduleStartDate
        */
        public String getScheduleStartDate() {
        return this.scheduleStartDate;
        }

        /**
        * set the scheduleStartDate - 排产开始时间
        */
        public void setScheduleStartDate(String scheduleStartDate) {
        this.scheduleStartDate = scheduleStartDate;
        }
        /**
        * get the scheduleEndDate - 排产结束时间
        * @return the scheduleEndDate
        */
        public String getScheduleEndDate() {
        return this.scheduleEndDate;
        }

        /**
        * set the scheduleEndDate - 排产结束时间
        */
        public void setScheduleEndDate(String scheduleEndDate) {
        this.scheduleEndDate = scheduleEndDate;
        }
        /**
        * get the processDemandProcessStatus - 生产需求单工序表状态
        * @return the processDemandProcessStatus
        */
        public String getProcessDemandProcessStatus() {
        return this.processDemandProcessStatus;
        }

        /**
        * set the processDemandProcessStatus - 生产需求单工序表状态
        */
        public void setProcessDemandProcessStatus(String processDemandProcessStatus) {
        this.processDemandProcessStatus = processDemandProcessStatus;
        }
        /**
        * get the processFeePriceType - 加工费计价类型
        * @return the processFeePriceType
        */
        public String getProcessFeePriceType() {
        return this.processFeePriceType;
        }

        /**
        * set the processFeePriceType - 加工费计价类型
        */
        public void setProcessFeePriceType(String processFeePriceType) {
        this.processFeePriceType = processFeePriceType;
        }
        /**
        * get the mproviderId - 加工单位代码
        * @return the mproviderId
        */
        public String getMproviderId() {
        return this.mproviderId;
        }

        /**
        * set the mproviderId - 加工单位代码
        */
        public void setMproviderId(String mproviderId) {
        this.mproviderId = mproviderId;
        }
        /**
        * get the mproviderName - 加工单位名称
        * @return the mproviderName
        */
        public String getMproviderName() {
        return this.mproviderName;
        }

        /**
        * set the mproviderName - 加工单位名称
        */
        public void setMproviderName(String mproviderName) {
        this.mproviderName = mproviderName;
        }
        /**
        * get the agreementId - 加工协议号
        * @return the agreementId
        */
        public String getAgreementId() {
        return this.agreementId;
        }

        /**
        * set the agreementId - 加工协议号
        */
        public void setAgreementId(String agreementId) {
        this.agreementId = agreementId;
        }
        /**
        * get the agreementSubid - 加工协议子项号
        * @return the agreementSubid
        */
        public String getAgreementSubid() {
        return this.agreementSubid;
        }

        /**
        * set the agreementSubid - 加工协议子项号
        */
        public void setAgreementSubid(String agreementSubid) {
        this.agreementSubid = agreementSubid;
        }
        /**
        * get the taxRate - 税率
        * @return the taxRate
        */
        public BigDecimal getTaxRate() {
        return this.taxRate;
        }

        /**
        * set the taxRate - 税率
        */
        public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
        }
        /**
        * get the processFeePrice - 加工费单价(不含税)
        * @return the processFeePrice
        */
        public BigDecimal getProcessFeePrice() {
        return this.processFeePrice;
        }

        /**
        * set the processFeePrice - 加工费单价(不含税)
        */
        public void setProcessFeePrice(BigDecimal processFeePrice) {
        this.processFeePrice = processFeePrice;
        }
        /**
        * get the processFeePriceTax - 加工费单价(含税)
        * @return the processFeePriceTax
        */
        public BigDecimal getProcessFeePriceTax() {
        return this.processFeePriceTax;
        }

        /**
        * set the processFeePriceTax - 加工费单价(含税)
        */
        public void setProcessFeePriceTax(BigDecimal processFeePriceTax) {
        this.processFeePriceTax = processFeePriceTax;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the canReturnMaterial - 是否可以退料
        * @return the canReturnMaterial
        */
        public String getCanReturnMaterial() {
        return this.canReturnMaterial;
        }

        /**
        * set the canReturnMaterial - 是否可以退料
        */
        public void setCanReturnMaterial(String canReturnMaterial) {
        this.canReturnMaterial = canReturnMaterial;
        }
        /**
        * get the scheduleSerialNum - 排产序号
        * @return the scheduleSerialNum
        */
        public String getScheduleSerialNum() {
        return this.scheduleSerialNum;
        }

        /**
        * set the scheduleSerialNum - 排产序号
        */
        public void setScheduleSerialNum(String scheduleSerialNum) {
        this.scheduleSerialNum = scheduleSerialNum;
        }
        /**
        * get the productionLock - 封锁产出品标记
        * @return the productionLock
        */
        public String getProductionLock() {
        return this.productionLock;
        }

        /**
        * set the productionLock - 封锁产出品标记
        */
        public void setProductionLock(String productionLock) {
        this.productionLock = productionLock;
        }
        /**
        * get the processTime - 生产时间(分钟)
        * @return the processTime
        */
        public Long getProcessTime() {
        return this.processTime;
        }

        /**
        * set the processTime - 生产时间(分钟)
        */
        public void setProcessTime(Long processTime) {
        this.processTime = processTime;
        }
        /**
        * get the prepareTime - 准备时间(分钟)
        * @return the prepareTime
        */
        public Long getPrepareTime() {
        return this.prepareTime;
        }

        /**
        * set the prepareTime - 准备时间(分钟)
        */
        public void setPrepareTime(Long prepareTime) {
        this.prepareTime = prepareTime;
        }
        /**
        * get the deliveryTime - 交接时间(分钟)
        * @return the deliveryTime
        */
        public Long getDeliveryTime() {
        return this.deliveryTime;
        }

        /**
        * set the deliveryTime - 交接时间(分钟)
        */
        public void setDeliveryTime(Long deliveryTime) {
        this.deliveryTime = deliveryTime;
        }
        /**
        * get the ifFirst - 是否首道
        * @return the ifFirst
        */
        public String getIfFirst() {
        return this.ifFirst;
        }

        /**
        * set the ifFirst - 是否首道
        */
        public void setIfFirst(String ifFirst) {
        this.ifFirst = ifFirst;
        }
        /**
        * get the ifRoll - 原料形状
        * @return the ifRoll
        */
        public String getIfRoll() {
        return this.ifRoll;
        }

        /**
        * set the ifRoll - 原料形状
        */
        public void setIfRoll(String ifRoll) {
        this.ifRoll = ifRoll;
        }
        /**
        * get the auditorId - 审核人工号
        * @return the auditorId
        */
        public String getAuditorId() {
        return this.auditorId;
        }

        /**
        * set the auditorId - 审核人工号
        */
        public void setAuditorId(String auditorId) {
        this.auditorId = auditorId;
        }
        /**
        * get the auditTime - 审核时间
        * @return the auditTime
        */
        public String getAuditTime() {
        return this.auditTime;
        }

        /**
        * set the auditTime - 审核时间
        */
        public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
        }
        /**
        * get the scheduleSerialSubnum - 排产子序号
        * @return the scheduleSerialSubnum
        */
        public Long getScheduleSerialSubnum() {
        return this.scheduleSerialSubnum;
        }

        /**
        * set the scheduleSerialSubnum - 排产子序号
        */
        public void setScheduleSerialSubnum(Long scheduleSerialSubnum) {
        this.scheduleSerialSubnum = scheduleSerialSubnum;
        }
        /**
        * get the deliveryDate - 加工完成时间
        * @return the deliveryDate
        */
        public String getDeliveryDate() {
        return this.deliveryDate;
        }

        /**
        * set the deliveryDate - 加工完成时间
        */
        public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
        }
        /**
        * get the processFinStartDate - 生产实际开始时间
        * @return the processFinStartDate
        */
        public String getProcessFinStartDate() {
        return this.processFinStartDate;
        }

        /**
        * set the processFinStartDate - 生产实际开始时间
        */
        public void setProcessFinStartDate(String processFinStartDate) {
        this.processFinStartDate = processFinStartDate;
        }
        /**
        * get the processFinEndDate - 生产实际结束时间
        * @return the processFinEndDate
        */
        public String getProcessFinEndDate() {
        return this.processFinEndDate;
        }

        /**
        * set the processFinEndDate - 生产实际结束时间
        */
        public void setProcessFinEndDate(String processFinEndDate) {
        this.processFinEndDate = processFinEndDate;
        }
        /**
        * get the productRemainQty - 加工剩余数量
        * @return the productRemainQty
        */
        public String getProductRemainQty() {
        return this.productRemainQty;
        }

        /**
        * set the productRemainQty - 加工剩余数量
        */
        public void setProductRemainQty(String productRemainQty) {
        this.productRemainQty = productRemainQty;
        }
        /**
        * get the productRemainWeight - 加工剩余重量
        * @return the productRemainWeight
        */
        public BigDecimal getProductRemainWeight() {
        return this.productRemainWeight;
        }

        /**
        * set the productRemainWeight - 加工剩余重量
        */
        public void setProductRemainWeight(BigDecimal productRemainWeight) {
        this.productRemainWeight = productRemainWeight;
        }
        /**
        * get the scheduleLockFlag - 排产锁定标记
        * @return the scheduleLockFlag
        */
        public String getScheduleLockFlag() {
        return this.scheduleLockFlag;
        }

        /**
        * set the scheduleLockFlag - 排产锁定标记
        */
        public void setScheduleLockFlag(String scheduleLockFlag) {
        this.scheduleLockFlag = scheduleLockFlag;
        }
        /**
        * get the materialRate - 原料成材率
        * @return the materialRate
        */
        public Integer getMaterialRate() {
        return this.materialRate;
        }

        /**
        * set the materialRate - 原料成材率
        */
        public void setMaterialRate(Integer materialRate) {
        this.materialRate = materialRate;
        }
        /**
        * get the ifMaterail - 是否强制指定原料卷
        * @return the ifMaterail
        */
        public String getIfMaterail() {
        return this.ifMaterail;
        }

        /**
        * set the ifMaterail - 是否强制指定原料卷
        */
        public void setIfMaterail(String ifMaterail) {
        this.ifMaterail = ifMaterail;
        }
        /**
        * get the ifArrangeOnly - 是否单排刀
        * @return the ifArrangeOnly
        */
        public String getIfArrangeOnly() {
        return this.ifArrangeOnly;
        }

        /**
        * set the ifArrangeOnly - 是否单排刀
        */
        public void setIfArrangeOnly(String ifArrangeOnly) {
        this.ifArrangeOnly = ifArrangeOnly;
        }
        /**
        * get the preProcessDemandSubId - 上一道生产需求单子项号
        * @return the preProcessDemandSubId
        */
        public String getPreProcessDemandSubId() {
        return this.preProcessDemandSubId;
        }

        /**
        * set the preProcessDemandSubId - 上一道生产需求单子项号
        */
        public void setPreProcessDemandSubId(String preProcessDemandSubId) {
        this.preProcessDemandSubId = preProcessDemandSubId;
        }
        /**
        * get the processIfSettleFinish - 生产加工是否结算完成
        * @return the processIfSettleFinish
        */
        public String getProcessIfSettleFinish() {
        return this.processIfSettleFinish;
        }

        /**
        * set the processIfSettleFinish - 生产加工是否结算完成
        */
        public void setProcessIfSettleFinish(String processIfSettleFinish) {
        this.processIfSettleFinish = processIfSettleFinish;
        }
        /**
        * get the processIfSettle - 生产加工是否结算加工费
        * @return the processIfSettle
        */
        public String getProcessIfSettle() {
        return this.processIfSettle;
        }

        /**
        * set the processIfSettle - 生产加工是否结算加工费
        */
        public void setProcessIfSettle(String processIfSettle) {
        this.processIfSettle = processIfSettle;
        }
        /**
        * get the suitVoucherNum - 套裁封锁单据号
        * @return the suitVoucherNum
        */
        public String getSuitVoucherNum() {
        return this.suitVoucherNum;
        }

        /**
        * set the suitVoucherNum - 套裁封锁单据号
        */
        public void setSuitVoucherNum(String suitVoucherNum) {
        this.suitVoucherNum = suitVoucherNum;
        }
        /**
        * get the dataSource - 数据来源
        * @return the dataSource
        */
        public String getDataSource() {
        return this.dataSource;
        }

        /**
        * set the dataSource - 数据来源
        */
        public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
        }
        /**
        * get the printBatchId - 打印批次号
        * @return the printBatchId
        */
        public String getPrintBatchId() {
        return this.printBatchId;
        }

        /**
        * set the printBatchId - 打印批次号
        */
        public void setPrintBatchId(String printBatchId) {
        this.printBatchId = printBatchId;
        }
        /**
        * get the rawMaterialTransferTime - 原料转运时间
        * @return the rawMaterialTransferTime
        */
        public Integer getRawMaterialTransferTime() {
        return this.rawMaterialTransferTime;
        }

        /**
        * set the rawMaterialTransferTime - 原料转运时间
        */
        public void setRawMaterialTransferTime(Integer rawMaterialTransferTime) {
        this.rawMaterialTransferTime = rawMaterialTransferTime;
        }
        /**
        * get the combineNo - 归并号
        * @return the combineNo
        */
        public String getCombineNo() {
        return this.combineNo;
        }

        /**
        * set the combineNo - 归并号
        */
        public void setCombineNo(String combineNo) {
        this.combineNo = combineNo;
        }
        /**
        * get the synFlag - 协同标记
        * @return the synFlag
        */
        public String getSynFlag() {
        return this.synFlag;
        }

        /**
        * set the synFlag - 协同标记
        */
        public void setSynFlag(String synFlag) {
        this.synFlag = synFlag;
        }
        /**
        * get the allocTime - 分配时间（分钟）
        * @return the allocTime
        */
        public Long getAllocTime() {
        return this.allocTime;
        }

        /**
        * set the allocTime - 分配时间（分钟）
        */
        public void setAllocTime(Long allocTime) {
        this.allocTime = allocTime;
        }
        /**
        * get the scheduleIssueDate - 排产下发时间
        * @return the scheduleIssueDate
        */
        public String getScheduleIssueDate() {
        return this.scheduleIssueDate;
        }

        /**
        * set the scheduleIssueDate - 排产下发时间
        */
        public void setScheduleIssueDate(String scheduleIssueDate) {
        this.scheduleIssueDate = scheduleIssueDate;
        }
        /**
        * get the scheduleIssuePerson - 排产下发人
        * @return the scheduleIssuePerson
        */
        public String getScheduleIssuePerson() {
        return this.scheduleIssuePerson;
        }

        /**
        * set the scheduleIssuePerson - 排产下发人
        */
        public void setScheduleIssuePerson(String scheduleIssuePerson) {
        this.scheduleIssuePerson = scheduleIssuePerson;
        }
        /**
        * get the outprocessReasons - 委外原因
        * @return the outprocessReasons
        */
        public String getOutprocessReasons() {
        return this.outprocessReasons;
        }

        /**
        * set the outprocessReasons - 委外原因
        */
        public void setOutprocessReasons(String outprocessReasons) {
        this.outprocessReasons = outprocessReasons;
        }
        /**
        * get the deliveryDateRemark - 交期备注
        * @return the deliveryDateRemark
        */
        public String getDeliveryDateRemark() {
        return this.deliveryDateRemark;
        }

        /**
        * set the deliveryDateRemark - 交期备注
        */
        public void setDeliveryDateRemark(String deliveryDateRemark) {
        this.deliveryDateRemark = deliveryDateRemark;
        }
        /**
        * get the specialRemark - 特殊备注
        * @return the specialRemark
        */
        public String getSpecialRemark() {
        return this.specialRemark;
        }

        /**
        * set the specialRemark - 特殊备注
        */
        public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
        }
        /**
        * get the ifCheckAssay - 是否检化验
        * @return the ifCheckAssay
        */
        public String getIfCheckAssay() {
        return this.ifCheckAssay;
        }

        /**
        * set the ifCheckAssay - 是否检化验
        */
        public void setIfCheckAssay(String ifCheckAssay) {
        this.ifCheckAssay = ifCheckAssay;
        }
        /**
        * get the yield - 成材率
        * @return the yield
        */
        public Integer getYield() {
        return this.yield;
        }

        /**
        * set the yield - 成材率
        */
        public void setYield(Integer yield) {
        this.yield = yield;
        }
        /**
        * get the theoreticalImpulseTimes - 理论冲次数
        * @return the theoreticalImpulseTimes
        */
        public Long getTheoreticalImpulseTimes() {
        return this.theoreticalImpulseTimes;
        }

        /**
        * set the theoreticalImpulseTimes - 理论冲次数
        */
        public void setTheoreticalImpulseTimes(Long theoreticalImpulseTimes) {
        this.theoreticalImpulseTimes = theoreticalImpulseTimes;
        }
        /**
        * get the craftCode - 工艺单号
        * @return the craftCode
        */
        public String getCraftCode() {
        return this.craftCode;
        }

        /**
        * set the craftCode - 工艺单号
        */
        public void setCraftCode(String craftCode) {
        this.craftCode = craftCode;
        }
        /**
        * get the mouldId - 模具ID
        * @return the mouldId
        */
        public String getMouldId() {
        return this.mouldId;
        }

        /**
        * set the mouldId - 模具ID
        */
        public void setMouldId(String mouldId) {
        this.mouldId = mouldId;
        }
        /**
        * get the actualImpulseTimes - 实际冲次数
        * @return the actualImpulseTimes
        */
        public Long getActualImpulseTimes() {
        return this.actualImpulseTimes;
        }

        /**
        * set the actualImpulseTimes - 实际冲次数
        */
        public void setActualImpulseTimes(Long actualImpulseTimes) {
        this.actualImpulseTimes = actualImpulseTimes;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the processFeeCalculateMethod 
        * @return the processFeeCalculateMethod
        */
        public String getProcessFeeCalculateMethod() {
        return this.processFeeCalculateMethod;
        }

        /**
        * set the processFeeCalculateMethod 
        */
        public void setProcessFeeCalculateMethod(String processFeeCalculateMethod) {
        this.processFeeCalculateMethod = processFeeCalculateMethod;
        }
        /**
        * get the segNo - 业务账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the agreementManageType - 委外协议管理方式
        * @return the agreementManageType
        */
        public String getAgreementManageType() {
        return this.agreementManageType;
        }

        /**
        * set the agreementManageType - 委外协议管理方式
        */
        public void setAgreementManageType(String agreementManageType) {
        this.agreementManageType = agreementManageType;
        }
        /**
        * get the ifPlanFlag - 是否参与排产
        * @return the ifPlanFlag
        */
        public String getIfPlanFlag() {
        return this.ifPlanFlag;
        }

        /**
        * set the ifPlanFlag - 是否参与排产
        */
        public void setIfPlanFlag(String ifPlanFlag) {
        this.ifPlanFlag = ifPlanFlag;
        }
        /**
        * get the createPerson - 制单人代码
        * @return the createPerson
        */
        public String getCreatePerson() {
        return this.createPerson;
        }

        /**
        * set the createPerson - 制单人代码
        */
        public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
        }
        /**
        * get the processSpeed - 加工速度(米/分钟)
        * @return the processSpeed
        */
        public Integer getProcessSpeed() {
        return this.processSpeed;
        }

        /**
        * set the processSpeed - 加工速度(米/分钟)
        */
        public void setProcessSpeed(Integer processSpeed) {
        this.processSpeed = processSpeed;
        }
        /**
        * get the auditorName - 审核人姓名
        * @return the auditorName
        */
        public String getAuditorName() {
        return this.auditorName;
        }

        /**
        * set the auditorName - 审核人姓名
        */
        public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
        }
        /**
        * get the previousProcessOrderId - 上道工序/工单
        * @return the previousProcessOrderId
        */
        public String getPreviousProcessOrderId() {
        return this.previousProcessOrderId;
        }

        /**
        * set the previousProcessOrderId - 上道工序/工单
        */
        public void setPreviousProcessOrderId(String previousProcessOrderId) {
        this.previousProcessOrderId = previousProcessOrderId;
        }
        /**
        * get the nextProcessOrderId - 下道工序/工单
        * @return the nextProcessOrderId
        */
        public String getNextProcessOrderId() {
        return this.nextProcessOrderId;
        }

        /**
        * set the nextProcessOrderId - 下道工序/工单
        */
        public void setNextProcessOrderId(String nextProcessOrderId) {
        this.nextProcessOrderId = nextProcessOrderId;
        }
        /**
        * get the ifNeedSuitCut - 是否需要套裁方案
        * @return the ifNeedSuitCut
        */
        public String getIfNeedSuitCut() {
        return this.ifNeedSuitCut;
        }

        /**
        * set the ifNeedSuitCut - 是否需要套裁方案
        */
        public void setIfNeedSuitCut(String ifNeedSuitCut) {
        this.ifNeedSuitCut = ifNeedSuitCut;
        }
        /**
        * get the tradeCode - 贸易方式
        * @return the tradeCode
        */
        public String getTradeCode() {
        return this.tradeCode;
        }

        /**
        * set the tradeCode - 贸易方式
        */
        public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
        }
        /**
        * get the processRate - 合格品率
        * @return the processRate
        */
        public Integer getProcessRate() {
        return this.processRate;
        }

        /**
        * set the processRate - 合格品率
        */
        public void setProcessRate(Integer processRate) {
        this.processRate = processRate;
        }
        /**
        * get the processId - 流程ID
        * @return the processId
        */
        public String getProcessId() {
        return this.processId;
        }

        /**
        * set the processId - 流程ID
        */
        public void setProcessId(String processId) {
        this.processId = processId;
        }
        /**
        * get the auditOpnion - 审核意见
        * @return the auditOpnion
        */
        public String getAuditOpnion() {
        return this.auditOpnion;
        }

        /**
        * set the auditOpnion - 审核意见
        */
        public void setAuditOpnion(String auditOpnion) {
        this.auditOpnion = auditOpnion;
        }
        /**
        * get the businessFrom - 业务来源
        * @return the businessFrom
        */
        public String getBusinessFrom() {
        return this.businessFrom;
        }

        /**
        * set the businessFrom - 业务来源
        */
        public void setBusinessFrom(String businessFrom) {
        this.businessFrom = businessFrom;
        }
        /**
        * get the teamId - 班组
        * @return the teamId
        */
        public String getTeamId() {
        return this.teamId;
        }

        /**
        * set the teamId - 班组
        */
        public void setTeamId(String teamId) {
        this.teamId = teamId;
        }
        /**
        * get the workingShift - 班次
        * @return the workingShift
        */
        public String getWorkingShift() {
        return this.workingShift;
        }

        /**
        * set the workingShift - 班次
        */
        public void setWorkingShift(String workingShift) {
        this.workingShift = workingShift;
        }
        /**
        * get the sph - sph每小时加工片数(落料和精剪）
        * @return the sph
        */
        public Long getSph() {
        return this.sph;
        }

        /**
        * set the sph - sph每小时加工片数(落料和精剪）
        */
        public void setSph(Long sph) {
        this.sph = sph;
        }
        /**
        * get the sgmFlag - SGM标记
        * @return the sgmFlag
        */
        public String getSgmFlag() {
        return this.sgmFlag;
        }

        /**
        * set the sgmFlag - SGM标记
        */
        public void setSgmFlag(String sgmFlag) {
        this.sgmFlag = sgmFlag;
        }
        /**
        * get the auditFlag - 审核标记
        * @return the auditFlag
        */
        public String getAuditFlag() {
        return this.auditFlag;
        }

        /**
        * set the auditFlag - 审核标记
        */
        public void setAuditFlag(String auditFlag) {
        this.auditFlag = auditFlag;
        }
        /**
        * get the apprStatus - 审批状态
        * @return the apprStatus
        */
        public String getApprStatus() {
        return this.apprStatus;
        }

        /**
        * set the apprStatus - 审批状态
        */
        public void setApprStatus(String apprStatus) {
        this.apprStatus = apprStatus;
        }
        /**
        * get the outApprStatus - 委外审批状态
        * @return the outApprStatus
        */
        public String getOutApprStatus() {
        return this.outApprStatus;
        }

        /**
        * set the outApprStatus - 委外审批状态
        */
        public void setOutApprStatus(String outApprStatus) {
        this.outApprStatus = outApprStatus;
        }
        /**
        * get the outAuditFlag - 委外审核标记
        * @return the outAuditFlag
        */
        public String getOutAuditFlag() {
        return this.outAuditFlag;
        }

        /**
        * set the outAuditFlag - 委外审核标记
        */
        public void setOutAuditFlag(String outAuditFlag) {
        this.outAuditFlag = outAuditFlag;
        }
        /**
        * get the outAuditOpnion - 委外审核意见
        * @return the outAuditOpnion
        */
        public String getOutAuditOpnion() {
        return this.outAuditOpnion;
        }

        /**
        * set the outAuditOpnion - 委外审核意见
        */
        public void setOutAuditOpnion(String outAuditOpnion) {
        this.outAuditOpnion = outAuditOpnion;
        }
        /**
        * get the outAuditTime - 委外审核时间
        * @return the outAuditTime
        */
        public String getOutAuditTime() {
        return this.outAuditTime;
        }

        /**
        * set the outAuditTime - 委外审核时间
        */
        public void setOutAuditTime(String outAuditTime) {
        this.outAuditTime = outAuditTime;
        }
        /**
        * get the outAuditorId - 委外审核人工号
        * @return the outAuditorId
        */
        public String getOutAuditorId() {
        return this.outAuditorId;
        }

        /**
        * set the outAuditorId - 委外审核人工号
        */
        public void setOutAuditorId(String outAuditorId) {
        this.outAuditorId = outAuditorId;
        }
        /**
        * get the outAuditorName - 委外审核人姓名
        * @return the outAuditorName
        */
        public String getOutAuditorName() {
        return this.outAuditorName;
        }

        /**
        * set the outAuditorName - 委外审核人姓名
        */
        public void setOutAuditorName(String outAuditorName) {
        this.outAuditorName = outAuditorName;
        }
        /**
        * get the outProcessId - 委外流程ID
        * @return the outProcessId
        */
        public String getOutProcessId() {
        return this.outProcessId;
        }

        /**
        * set the outProcessId - 委外流程ID
        */
        public void setOutProcessId(String outProcessId) {
        this.outProcessId = outProcessId;
        }
        /**
        * get the ifSubstitAudit - 是否需要替代审批
        * @return the ifSubstitAudit
        */
        public String getIfSubstitAudit() {
        return this.ifSubstitAudit;
        }

        /**
        * set the ifSubstitAudit - 是否需要替代审批
        */
        public void setIfSubstitAudit(String ifSubstitAudit) {
        this.ifSubstitAudit = ifSubstitAudit;
        }
        /**
        * get the ifYieldAudit - 是否需要成材率审批
        * @return the ifYieldAudit
        */
        public String getIfYieldAudit() {
        return this.ifYieldAudit;
        }

        /**
        * set the ifYieldAudit - 是否需要成材率审批
        */
        public void setIfYieldAudit(String ifYieldAudit) {
        this.ifYieldAudit = ifYieldAudit;
        }
        /**
        * get the ifOutprocessAudit - 是否需要委外工单审批
        * @return the ifOutprocessAudit
        */
        public String getIfOutprocessAudit() {
        return this.ifOutprocessAudit;
        }

        /**
        * set the ifOutprocessAudit - 是否需要委外工单审批
        */
        public void setIfOutprocessAudit(String ifOutprocessAudit) {
        this.ifOutprocessAudit = ifOutprocessAudit;
        }
        /**
        * get the processSynTriggerType - 协同触发方式
        * @return the processSynTriggerType
        */
        public String getProcessSynTriggerType() {
        return this.processSynTriggerType;
        }

        /**
        * set the processSynTriggerType - 协同触发方式
        */
        public void setProcessSynTriggerType(String processSynTriggerType) {
        this.processSynTriggerType = processSynTriggerType;
        }
        /**
        * get the ifSyn - 是否协同
        * @return the ifSyn
        */
        public String getIfSyn() {
        return this.ifSyn;
        }

        /**
        * set the ifSyn - 是否协同
        */
        public void setIfSyn(String ifSyn) {
        this.ifSyn = ifSyn;
        }
        /**
        * get the processSynReceive - 协同接收方
        * @return the processSynReceive
        */
        public String getProcessSynReceive() {
        return this.processSynReceive;
        }

        /**
        * set the processSynReceive - 协同接收方
        */
        public void setProcessSynReceive(String processSynReceive) {
        this.processSynReceive = processSynReceive;
        }
        /**
        * get the processSeqId - 加工顺序
        * @return the processSeqId
        */
        public Integer getProcessSeqId() {
        return this.processSeqId;
        }

        /**
        * set the processSeqId - 加工顺序
        */
        public void setProcessSeqId(Integer processSeqId) {
        this.processSeqId = processSeqId;
        }
        /**
        * get the ifNoProcessingAgreement - 无加工协议（委外）
        * @return the ifNoProcessingAgreement
        */
        public String getIfNoProcessingAgreement() {
        return this.ifNoProcessingAgreement;
        }

        /**
        * set the ifNoProcessingAgreement - 无加工协议（委外）
        */
        public void setIfNoProcessingAgreement(String ifNoProcessingAgreement) {
        this.ifNoProcessingAgreement = ifNoProcessingAgreement;
        }
        /**
        * get the orderConfirmTime - 生产工单确认时间
        * @return the orderConfirmTime
        */
        public String getOrderConfirmTime() {
        return this.orderConfirmTime;
        }

        /**
        * set the orderConfirmTime - 生产工单确认时间
        */
        public void setOrderConfirmTime(String orderConfirmTime) {
        this.orderConfirmTime = orderConfirmTime;
        }
        /**
        * get the planRate - 工艺成材率
        * @return the planRate
        */
        public BigDecimal getPlanRate() {
        return this.planRate;
        }

        /**
        * set the planRate - 工艺成材率
        */
        public void setPlanRate(BigDecimal planRate) {
        this.planRate = planRate;
        }
        /**
        * get the synSettleFlag - 委托方结算标记
        * @return the synSettleFlag
        */
        public String getSynSettleFlag() {
        return this.synSettleFlag;
        }

        /**
        * set the synSettleFlag - 委托方结算标记
        */
        public void setSynSettleFlag(String synSettleFlag) {
        this.synSettleFlag = synSettleFlag;
        }
        /**
        * get the knifePrintBatchId - 机组组刀打印批次号
        * @return the knifePrintBatchId
        */
        public String getKnifePrintBatchId() {
        return this.knifePrintBatchId;
        }

        /**
        * set the knifePrintBatchId - 机组组刀打印批次号
        */
        public void setKnifePrintBatchId(String knifePrintBatchId) {
        this.knifePrintBatchId = knifePrintBatchId;
        }

        public String getSegName() {
            return segName;
        }

        public void setSegName(String segName) {
            this.segName = segName;
        }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
                setProcessDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandId")), processDemandId));
                setProcessDemandSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandSubId")), processDemandSubId));
                setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
                setAfterPOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("afterPOrderId")), afterPOrderId));
                setBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessType")), businessType));
                setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
                setKnifeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("knifeId")), knifeId));
                setScheduleStartDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scheduleStartDate")), scheduleStartDate));
                setScheduleEndDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scheduleEndDate")), scheduleEndDate));
                setProcessDemandProcessStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandProcessStatus")), processDemandProcessStatus));
                setProcessFeePriceType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFeePriceType")), processFeePriceType));
                setMproviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mproviderId")), mproviderId));
                setMproviderName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mproviderName")), mproviderName));
                setAgreementId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("agreementId")), agreementId));
                setAgreementSubid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("agreementSubid")), agreementSubid));
                setTaxRate(NumberUtils.toBigDecimal(StringUtils.toString(map.get("taxRate")), taxRate));
                setProcessFeePrice(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processFeePrice")), processFeePrice));
                setProcessFeePriceTax(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processFeePriceTax")), processFeePriceTax));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setCanReturnMaterial(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("canReturnMaterial")), canReturnMaterial));
                setScheduleSerialNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scheduleSerialNum")), scheduleSerialNum));
                setProductionLock(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productionLock")), productionLock));
                setProcessTime(NumberUtils.toLong(StringUtils.toString(map.get("processTime")), processTime));
                setPrepareTime(NumberUtils.toLong(StringUtils.toString(map.get("prepareTime")), prepareTime));
                setDeliveryTime(NumberUtils.toLong(StringUtils.toString(map.get("deliveryTime")), deliveryTime));
                setIfFirst(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifFirst")), ifFirst));
                setIfRoll(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifRoll")), ifRoll));
                setAuditorId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditorId")), auditorId));
                setAuditTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditTime")), auditTime));
                setScheduleSerialSubnum(NumberUtils.toLong(StringUtils.toString(map.get("scheduleSerialSubnum")), scheduleSerialSubnum));
                setDeliveryDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryDate")), deliveryDate));
                setProcessFinStartDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFinStartDate")), processFinStartDate));
                setProcessFinEndDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFinEndDate")), processFinEndDate));
                setProductRemainQty(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productRemainQty")), productRemainQty));
                setProductRemainWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("productRemainWeight")), productRemainWeight));
                setScheduleLockFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scheduleLockFlag")), scheduleLockFlag));
                setMaterialRate(NumberUtils.toInteger(StringUtils.toString(map.get("materialRate")), materialRate));
                setIfMaterail(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifMaterail")), ifMaterail));
                setIfArrangeOnly(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifArrangeOnly")), ifArrangeOnly));
                setPreProcessDemandSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("preProcessDemandSubId")), preProcessDemandSubId));
                setProcessIfSettleFinish(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processIfSettleFinish")), processIfSettleFinish));
                setProcessIfSettle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processIfSettle")), processIfSettle));
                setSuitVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("suitVoucherNum")), suitVoucherNum));
                setDataSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataSource")), dataSource));
                setPrintBatchId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("printBatchId")), printBatchId));
                setRawMaterialTransferTime(NumberUtils.toInteger(StringUtils.toString(map.get("rawMaterialTransferTime")), rawMaterialTransferTime));
                setCombineNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("combineNo")), combineNo));
                setSynFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("synFlag")), synFlag));
                setAllocTime(NumberUtils.toLong(StringUtils.toString(map.get("allocTime")), allocTime));
                setScheduleIssueDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scheduleIssueDate")), scheduleIssueDate));
                setScheduleIssuePerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scheduleIssuePerson")), scheduleIssuePerson));
                setOutprocessReasons(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outprocessReasons")), outprocessReasons));
                setDeliveryDateRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryDateRemark")), deliveryDateRemark));
                setSpecialRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specialRemark")), specialRemark));
                setIfCheckAssay(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifCheckAssay")), ifCheckAssay));
                setYield(NumberUtils.toInteger(StringUtils.toString(map.get("yield")), yield));
                setTheoreticalImpulseTimes(NumberUtils.toLong(StringUtils.toString(map.get("theoreticalImpulseTimes")), theoreticalImpulseTimes));
                setCraftCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craftCode")), craftCode));
                setMouldId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("mouldId")), mouldId));
                setActualImpulseTimes(NumberUtils.toLong(StringUtils.toString(map.get("actualImpulseTimes")), actualImpulseTimes));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setProcessFeeCalculateMethod(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processFeeCalculateMethod")), processFeeCalculateMethod));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setAgreementManageType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("agreementManageType")), agreementManageType));
                setIfPlanFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifPlanFlag")), ifPlanFlag));
                setCreatePerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("createPerson")), createPerson));
                setProcessSpeed(NumberUtils.toInteger(StringUtils.toString(map.get("processSpeed")), processSpeed));
                setAuditorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditorName")), auditorName));
                setPreviousProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("previousProcessOrderId")), previousProcessOrderId));
                setNextProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nextProcessOrderId")), nextProcessOrderId));
                setIfNeedSuitCut(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifNeedSuitCut")), ifNeedSuitCut));
                setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
                setProcessRate(NumberUtils.toInteger(StringUtils.toString(map.get("processRate")), processRate));
                setProcessId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processId")), processId));
                setAuditOpnion(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditOpnion")), auditOpnion));
                setBusinessFrom(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessFrom")), businessFrom));
                setTeamId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teamId")), teamId));
                setWorkingShift(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workingShift")), workingShift));
                setSph(NumberUtils.toLong(StringUtils.toString(map.get("sph")), sph));
                setSgmFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sgmFlag")), sgmFlag));
                setAuditFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("auditFlag")), auditFlag));
                setApprStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("apprStatus")), apprStatus));
                setOutApprStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outApprStatus")), outApprStatus));
                setOutAuditFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outAuditFlag")), outAuditFlag));
                setOutAuditOpnion(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outAuditOpnion")), outAuditOpnion));
                setOutAuditTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outAuditTime")), outAuditTime));
                setOutAuditorId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outAuditorId")), outAuditorId));
                setOutAuditorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outAuditorName")), outAuditorName));
                setOutProcessId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outProcessId")), outProcessId));
                setIfSubstitAudit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifSubstitAudit")), ifSubstitAudit));
                setIfYieldAudit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifYieldAudit")), ifYieldAudit));
                setIfOutprocessAudit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifOutprocessAudit")), ifOutprocessAudit));
                setProcessSynTriggerType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSynTriggerType")), processSynTriggerType));
                setIfSyn(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifSyn")), ifSyn));
                setProcessSynReceive(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSynReceive")), processSynReceive));
                setProcessSeqId(NumberUtils.toInteger(StringUtils.toString(map.get("processSeqId")), processSeqId));
                setIfNoProcessingAgreement(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifNoProcessingAgreement")), ifNoProcessingAgreement));
                setOrderConfirmTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderConfirmTime")), orderConfirmTime));
                setPlanRate(NumberUtils.toBigDecimal(StringUtils.toString(map.get("planRate")), planRate));
                setSynSettleFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("synSettleFlag")), synSettleFlag));
                setKnifePrintBatchId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("knifePrintBatchId")), knifePrintBatchId));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("processOrderId",StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
                map.put("processDemandId",StringUtils.toString(processDemandId, eiMetadata.getMeta("processDemandId")));
                map.put("processDemandSubId",StringUtils.toString(processDemandSubId, eiMetadata.getMeta("processDemandSubId")));
                map.put("processCategory",StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
                map.put("afterPOrderId",StringUtils.toString(afterPOrderId, eiMetadata.getMeta("afterPOrderId")));
                map.put("businessType",StringUtils.toString(businessType, eiMetadata.getMeta("businessType")));
                map.put("machineCode",StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
                map.put("knifeId",StringUtils.toString(knifeId, eiMetadata.getMeta("knifeId")));
                map.put("scheduleStartDate",StringUtils.toString(scheduleStartDate, eiMetadata.getMeta("scheduleStartDate")));
                map.put("scheduleEndDate",StringUtils.toString(scheduleEndDate, eiMetadata.getMeta("scheduleEndDate")));
                map.put("processDemandProcessStatus",StringUtils.toString(processDemandProcessStatus, eiMetadata.getMeta("processDemandProcessStatus")));
                map.put("processFeePriceType",StringUtils.toString(processFeePriceType, eiMetadata.getMeta("processFeePriceType")));
                map.put("mproviderId",StringUtils.toString(mproviderId, eiMetadata.getMeta("mproviderId")));
                map.put("mproviderName",StringUtils.toString(mproviderName, eiMetadata.getMeta("mproviderName")));
                map.put("agreementId",StringUtils.toString(agreementId, eiMetadata.getMeta("agreementId")));
                map.put("agreementSubid",StringUtils.toString(agreementSubid, eiMetadata.getMeta("agreementSubid")));
                map.put("taxRate",StringUtils.toString(taxRate, eiMetadata.getMeta("taxRate")));
                map.put("processFeePrice",StringUtils.toString(processFeePrice, eiMetadata.getMeta("processFeePrice")));
                map.put("processFeePriceTax",StringUtils.toString(processFeePriceTax, eiMetadata.getMeta("processFeePriceTax")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("canReturnMaterial",StringUtils.toString(canReturnMaterial, eiMetadata.getMeta("canReturnMaterial")));
                map.put("scheduleSerialNum",StringUtils.toString(scheduleSerialNum, eiMetadata.getMeta("scheduleSerialNum")));
                map.put("productionLock",StringUtils.toString(productionLock, eiMetadata.getMeta("productionLock")));
                map.put("processTime",StringUtils.toString(processTime, eiMetadata.getMeta("processTime")));
                map.put("prepareTime",StringUtils.toString(prepareTime, eiMetadata.getMeta("prepareTime")));
                map.put("deliveryTime",StringUtils.toString(deliveryTime, eiMetadata.getMeta("deliveryTime")));
                map.put("ifFirst",StringUtils.toString(ifFirst, eiMetadata.getMeta("ifFirst")));
                map.put("ifRoll",StringUtils.toString(ifRoll, eiMetadata.getMeta("ifRoll")));
                map.put("auditorId",StringUtils.toString(auditorId, eiMetadata.getMeta("auditorId")));
                map.put("auditTime",StringUtils.toString(auditTime, eiMetadata.getMeta("auditTime")));
                map.put("scheduleSerialSubnum",StringUtils.toString(scheduleSerialSubnum, eiMetadata.getMeta("scheduleSerialSubnum")));
                map.put("deliveryDate",StringUtils.toString(deliveryDate, eiMetadata.getMeta("deliveryDate")));
                map.put("processFinStartDate",StringUtils.toString(processFinStartDate, eiMetadata.getMeta("processFinStartDate")));
                map.put("processFinEndDate",StringUtils.toString(processFinEndDate, eiMetadata.getMeta("processFinEndDate")));
                map.put("productRemainQty",StringUtils.toString(productRemainQty, eiMetadata.getMeta("productRemainQty")));
                map.put("productRemainWeight",StringUtils.toString(productRemainWeight, eiMetadata.getMeta("productRemainWeight")));
                map.put("scheduleLockFlag",StringUtils.toString(scheduleLockFlag, eiMetadata.getMeta("scheduleLockFlag")));
                map.put("materialRate",StringUtils.toString(materialRate, eiMetadata.getMeta("materialRate")));
                map.put("ifMaterail",StringUtils.toString(ifMaterail, eiMetadata.getMeta("ifMaterail")));
                map.put("ifArrangeOnly",StringUtils.toString(ifArrangeOnly, eiMetadata.getMeta("ifArrangeOnly")));
                map.put("preProcessDemandSubId",StringUtils.toString(preProcessDemandSubId, eiMetadata.getMeta("preProcessDemandSubId")));
                map.put("processIfSettleFinish",StringUtils.toString(processIfSettleFinish, eiMetadata.getMeta("processIfSettleFinish")));
                map.put("processIfSettle",StringUtils.toString(processIfSettle, eiMetadata.getMeta("processIfSettle")));
                map.put("suitVoucherNum",StringUtils.toString(suitVoucherNum, eiMetadata.getMeta("suitVoucherNum")));
                map.put("dataSource",StringUtils.toString(dataSource, eiMetadata.getMeta("dataSource")));
                map.put("printBatchId",StringUtils.toString(printBatchId, eiMetadata.getMeta("printBatchId")));
                map.put("rawMaterialTransferTime",StringUtils.toString(rawMaterialTransferTime, eiMetadata.getMeta("rawMaterialTransferTime")));
                map.put("combineNo",StringUtils.toString(combineNo, eiMetadata.getMeta("combineNo")));
                map.put("synFlag",StringUtils.toString(synFlag, eiMetadata.getMeta("synFlag")));
                map.put("allocTime",StringUtils.toString(allocTime, eiMetadata.getMeta("allocTime")));
                map.put("scheduleIssueDate",StringUtils.toString(scheduleIssueDate, eiMetadata.getMeta("scheduleIssueDate")));
                map.put("scheduleIssuePerson",StringUtils.toString(scheduleIssuePerson, eiMetadata.getMeta("scheduleIssuePerson")));
                map.put("outprocessReasons",StringUtils.toString(outprocessReasons, eiMetadata.getMeta("outprocessReasons")));
                map.put("deliveryDateRemark",StringUtils.toString(deliveryDateRemark, eiMetadata.getMeta("deliveryDateRemark")));
                map.put("specialRemark",StringUtils.toString(specialRemark, eiMetadata.getMeta("specialRemark")));
                map.put("ifCheckAssay",StringUtils.toString(ifCheckAssay, eiMetadata.getMeta("ifCheckAssay")));
                map.put("yield",StringUtils.toString(yield, eiMetadata.getMeta("yield")));
                map.put("theoreticalImpulseTimes",StringUtils.toString(theoreticalImpulseTimes, eiMetadata.getMeta("theoreticalImpulseTimes")));
                map.put("craftCode",StringUtils.toString(craftCode, eiMetadata.getMeta("craftCode")));
                map.put("mouldId",StringUtils.toString(mouldId, eiMetadata.getMeta("mouldId")));
                map.put("actualImpulseTimes",StringUtils.toString(actualImpulseTimes, eiMetadata.getMeta("actualImpulseTimes")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("processFeeCalculateMethod",StringUtils.toString(processFeeCalculateMethod, eiMetadata.getMeta("processFeeCalculateMethod")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("agreementManageType",StringUtils.toString(agreementManageType, eiMetadata.getMeta("agreementManageType")));
                map.put("ifPlanFlag",StringUtils.toString(ifPlanFlag, eiMetadata.getMeta("ifPlanFlag")));
                map.put("createPerson",StringUtils.toString(createPerson, eiMetadata.getMeta("createPerson")));
                map.put("processSpeed",StringUtils.toString(processSpeed, eiMetadata.getMeta("processSpeed")));
                map.put("auditorName",StringUtils.toString(auditorName, eiMetadata.getMeta("auditorName")));
                map.put("previousProcessOrderId",StringUtils.toString(previousProcessOrderId, eiMetadata.getMeta("previousProcessOrderId")));
                map.put("nextProcessOrderId",StringUtils.toString(nextProcessOrderId, eiMetadata.getMeta("nextProcessOrderId")));
                map.put("ifNeedSuitCut",StringUtils.toString(ifNeedSuitCut, eiMetadata.getMeta("ifNeedSuitCut")));
                map.put("tradeCode",StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
                map.put("processRate",StringUtils.toString(processRate, eiMetadata.getMeta("processRate")));
                map.put("processId",StringUtils.toString(processId, eiMetadata.getMeta("processId")));
                map.put("auditOpnion",StringUtils.toString(auditOpnion, eiMetadata.getMeta("auditOpnion")));
                map.put("businessFrom",StringUtils.toString(businessFrom, eiMetadata.getMeta("businessFrom")));
                map.put("teamId",StringUtils.toString(teamId, eiMetadata.getMeta("teamId")));
                map.put("workingShift",StringUtils.toString(workingShift, eiMetadata.getMeta("workingShift")));
                map.put("sph",StringUtils.toString(sph, eiMetadata.getMeta("sph")));
                map.put("sgmFlag",StringUtils.toString(sgmFlag, eiMetadata.getMeta("sgmFlag")));
                map.put("auditFlag",StringUtils.toString(auditFlag, eiMetadata.getMeta("auditFlag")));
                map.put("apprStatus",StringUtils.toString(apprStatus, eiMetadata.getMeta("apprStatus")));
                map.put("outApprStatus",StringUtils.toString(outApprStatus, eiMetadata.getMeta("outApprStatus")));
                map.put("outAuditFlag",StringUtils.toString(outAuditFlag, eiMetadata.getMeta("outAuditFlag")));
                map.put("outAuditOpnion",StringUtils.toString(outAuditOpnion, eiMetadata.getMeta("outAuditOpnion")));
                map.put("outAuditTime",StringUtils.toString(outAuditTime, eiMetadata.getMeta("outAuditTime")));
                map.put("outAuditorId",StringUtils.toString(outAuditorId, eiMetadata.getMeta("outAuditorId")));
                map.put("outAuditorName",StringUtils.toString(outAuditorName, eiMetadata.getMeta("outAuditorName")));
                map.put("outProcessId",StringUtils.toString(outProcessId, eiMetadata.getMeta("outProcessId")));
                map.put("ifSubstitAudit",StringUtils.toString(ifSubstitAudit, eiMetadata.getMeta("ifSubstitAudit")));
                map.put("ifYieldAudit",StringUtils.toString(ifYieldAudit, eiMetadata.getMeta("ifYieldAudit")));
                map.put("ifOutprocessAudit",StringUtils.toString(ifOutprocessAudit, eiMetadata.getMeta("ifOutprocessAudit")));
                map.put("processSynTriggerType",StringUtils.toString(processSynTriggerType, eiMetadata.getMeta("processSynTriggerType")));
                map.put("ifSyn",StringUtils.toString(ifSyn, eiMetadata.getMeta("ifSyn")));
                map.put("processSynReceive",StringUtils.toString(processSynReceive, eiMetadata.getMeta("processSynReceive")));
                map.put("processSeqId",StringUtils.toString(processSeqId, eiMetadata.getMeta("processSeqId")));
                map.put("ifNoProcessingAgreement",StringUtils.toString(ifNoProcessingAgreement, eiMetadata.getMeta("ifNoProcessingAgreement")));
                map.put("orderConfirmTime",StringUtils.toString(orderConfirmTime, eiMetadata.getMeta("orderConfirmTime")));
                map.put("planRate",StringUtils.toString(planRate, eiMetadata.getMeta("planRate")));
                map.put("synSettleFlag",StringUtils.toString(synSettleFlag, eiMetadata.getMeta("synSettleFlag")));
                map.put("knifePrintBatchId",StringUtils.toString(knifePrintBatchId, eiMetadata.getMeta("knifePrintBatchId")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));

return map;

}
}