package com.baosight.imom.common.utils;


import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FileUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

public class Base64ToMultipartFileConverter {

    public static CommonsMultipartFile base64ToMultipartFile(String base64String) throws IOException {
        // 从Base64字符串中获取文件的字节数据
        byte[] fileBytes = Base64.decodeBase64(base64String);

        // 创建一个临时文件
        File tempFile = File.createTempFile("temp", null);

        // 将字节数据写入临时文件
        try (OutputStream outputStream = new FileOutputStream(tempFile)) {
            outputStream.write(fileBytes);
        }

        // 创建一个FileItemFactory
        FileItemFactory factory = new DiskFileItemFactory();

        // 使用FileItemFactory创建一个FileItem，并直接设置内容
        FileItem fileItem = factory.createItem("file", "application/octet-stream", false, tempFile.getName());
        fileItem.getOutputStream().write(FileUtils.readFileToByteArray(tempFile));

        // 创建CommonsMultipartFile对象
        CommonsMultipartFile multipartFile = new CommonsMultipartFile(fileItem);

        // 删除临时文件
        tempFile.delete();

        return multipartFile;
    }

    public static CommonsMultipartFile convertToMultipartFile(String base64Data) throws IOException {
        // 提取Base64编码部分的字符串
        String base64Image = base64Data.split(",")[1];

        // 解码Base64字符串为字节数组
        byte[] imageBytes = Base64.decodeBase64(base64Image);

        // 创建一个临时文件
        File tempFile = File.createTempFile("temp", ".png");

        // 将字节数组写入临时文件
        try (OutputStream outputStream = new FileOutputStream(tempFile)) {
            outputStream.write(imageBytes);
        }

        // 创建一个FileItemFactory
        FileItemFactory factory = new DiskFileItemFactory();

        // 使用FileItemFactory创建一个FileItem
        FileItem fileItem = factory.createItem("file", "image/png", false, tempFile.getName());

        // 设置文件内容
        /*fileItem.setContent(FileUtils.readFileToByteArray(tempFile));*/
        // 通过输出流将文件内容写入FileItem
        try (OutputStream outputStream = fileItem.getOutputStream()) {
            outputStream.write(FileUtils.readFileToByteArray(tempFile));
        }

        // 创建CommonsMultipartFile对象
        CommonsMultipartFile multipartFile = new CommonsMultipartFile(fileItem);

        // 删除临时文件（可根据实际需求决定是否删除，这里先删除）
        tempFile.delete();

        return multipartFile;
    }

    public static CommonsMultipartFile convertPDFToMultipartFile(String pdfFilePath) throws IOException {
        File pdfFile = new File(pdfFilePath);
        FileItemFactory factory = new DiskFileItemFactory();
        FileItem item = factory.createItem("file", "application/pdf", false, pdfFile.getName());

        try (FileInputStream inputStream = new FileInputStream(pdfFile)) {
            byte[] buffer = new byte[(int) pdfFile.length()];
            inputStream.read(buffer);
            item.getOutputStream().write(buffer);
        }

        return new CommonsMultipartFile(item);
    }

    // 将Base64编码字符串转换为java.awt.Image（具体为BufferedImage）格式的方法
    public static BufferedImage base64StringToAWTImage(String base64ImageString) throws IOException {
        // 去除Base64编码字符串中可能包含的数据头（如data:image/png;base64,等），只保留纯Base64编码数据部分
        String base64Data = extractBase64Data(base64ImageString);

        // 使用Java 8及以后版本自带的Base64解码器将Base64编码数据解码为字节数组
        byte[] imageBytes = java.util.Base64.getDecoder().decode(base64Data);

        // 创建字节数组输入流，用于后续读取图片字节数据
        ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);

        // 使用ImageIO读取字节流并转换为BufferedImage对象（它是java.awt.Image的一种实现）
        return ImageIO.read(bis);
    }

    // 将Base64编码字符串转换为com.itextpdf.text.Image格式的方法，用于插入PDF等操作
    public static Image base64StringToITextImage(String base64ImageString) throws IOException, DocumentException {
        BufferedImage awtImage = base64StringToAWTImage(base64ImageString);

        // 将BufferedImage对象转换为字节数组输出流，方便后续操作
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ImageIO.write(awtImage, getImageFormat(base64ImageString), bos);

        // 将字节数组输出流中的数据转换为字节数组
        byte[] imageBytes = bos.toByteArray();

        // 使用iText库的Image.getInstance方法将字节数组转换为com.itextpdf.text.Image对象
        return Image.getInstance(imageBytes);
    }

    // 根据Base64编码字符串尝试推测图片格式（简单根据前缀信息判断，可进一步完善准确性）
    private static String getImageFormat(String base64ImageString) {
        if (base64ImageString.contains("image/jpeg")) {
            return "jpg";
        } else if (base64ImageString.contains("image/png")) {
            return "png";
        }
        return "png"; // 默认返回png格式，可根据实际需求调整
    }

    // 辅助方法，用于提取Base64编码数据部分，去除前缀信息（简单按逗号分隔取后半部分，可根据实际情况完善更准确的逻辑）
    private static String extractBase64Data(String base64ImageString) {
        if (base64ImageString.contains(",")) {
            return base64ImageString.split(",")[1];
        }
        return base64ImageString;
    }


}