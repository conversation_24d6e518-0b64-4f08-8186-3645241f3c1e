<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM1002">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE = #machineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packStatus">
            PACK_STATUS = #packStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processOrderId">
            PROCESS_ORDER_ID = #processOrderId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packId">
            PACK_ID = #packId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%', #equipmentName#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packIdStr">
            PACK_ID like concat('%', #packIdStr#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processOrderIdStr">
            PROCESS_ORDER_ID like concat('%', #processOrderIdStr#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="partIdStr">
            PART_ID like concat('%', #partIdStr#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startTime">
            substr(START_TIME,1,8) &gt;= replace(#startTime#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="endTime">
            replace(#endTime#,'-','') &gt;= substr(END_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="minCreateTime">
            #minCreateTime# &gt;= REC_CREATE_TIME
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="activeFlag">
            PACK_STATUS != '2'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="notUuid">
            UUID != #notUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitedUuid">
            UNITED_UUID = #unitedUuid#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM1002">
        SELECT
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        PROCESS_ORDER_ID as "processOrderId",  <!-- 生产工单号 -->
        PACK_ID as "packId",  <!-- 捆包号 -->
        SPECS_DESC as "specsDesc",  <!-- 规格描述 -->
        SHOPSIGN as "shopsign",  <!-- 牌号 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        PROD_NAME_CODE as "prodNameCode",  <!-- 品名代码 -->
        PROD_CNAME as "prodCname",  <!-- 品名名称 -->
        START_TIME as "startTime",  <!-- 开始时间 -->
        END_TIME as "endTime",  <!-- 结束时间 -->
        END_TYPE as "endType",  <!-- 结束类型0加工完成1退料2余料 -->
        RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        PART_ID as "partId",  <!-- 物料号 -->
        UP_PACK_TIME as "upPackTime",  <!-- 上料时间 -->
        PACK_STATUS as "packStatus",  <!-- 捆包状态 -->
        UNITED_PACK_ID as "unitedPackId",  <!-- 并包号 -->
        CURRENT_KNIFE as "currentKnife",  <!-- 当前刀号 -->
        KNIFE_SORT as "knifeSort",  <!-- 排刀顺序 -->
        UNITED_QUANTITY as "unitedQuantity", <!-- 并包数量 -->
        SUM_UNITED_QUANTITY as "sumUnitedQuantity", <!-- 累计并包量 -->
        ORIGINAL_PACK_ID as "originalPackId",  <!-- 原始捆包号 -->
        UNITED_STACK_NAME as "unitedStackName", <!-- 并包堆垛 -->
        END_STACK_NAME as "endStackName", <!-- 尾包堆垛 -->
        FINISHING_SHUNT_FLAG as "finishingShuntFlag", <!-- 精整分流标记 -->
        MAT_INNER_ID as "matInnerId",  <!-- 材料管理号 -->
        UNITED_UUID as "unitedUuid",  <!-- 并包单据号 -->
        TRADE_CODE as "tradeCode"  <!-- 贸易方式 -->
        FROM ${mevgSchema}.TVGDM1002 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                START_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM1002 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="countResult" resultClass="int">
        SELECT COUNT(T2.UUID) FROM ${mevgSchema}.TVGDM1002 T2
        WHERE 1=1
        AND T2.PROCESS_ORDER_ID = #processOrderId#
        AND T2.E_ARCHIVES_NO = #eArchivesNo#
        AND T2.SEG_NO = #segNo#
        AND T2.DEL_FLAG = '0'
        AND EXISTS(SELECT 1 FROM ${mevgSchema}.TVGDM1005 T5
        WHERE T5.RELEVANCE_ID = T2.UUID
        AND T5.DEL_FLAG='0' )
    </select>

    <select id="countNotDo" resultClass="int">
        SELECT COUNT(*) FROM ${meviSchema}.TVIPM0008
        WHERE DEL_FLAG = 0
        AND SEG_NO = #segNo#
        AND PROCESS_ORDER_ID = #processOrderId#
        AND PACK_ID NOT IN
        (SELECT PACK_ID FROM ${mevgSchema}.TVGDM1002
        WHERE DEL_FLAG = '0'
        AND SEG_NO = #segNo#
        AND PROCESS_ORDER_ID = #processOrderId#
        AND PACK_STATUS != '0' )
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM1002 (E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        PROCESS_ORDER_ID,  <!-- 生产工单号 -->
        PACK_ID,  <!-- 捆包号 -->
        SPECS_DESC,  <!-- 规格描述 -->
        SHOPSIGN,  <!-- 牌号 -->
        NET_WEIGHT,  <!-- 净重 -->
        PROD_NAME_CODE,  <!-- 品名代码 -->
        PROD_CNAME,  <!-- 品名名称 -->
        START_TIME,  <!-- 开始时间 -->
        END_TIME,  <!-- 结束时间 -->
        END_TYPE,  <!-- 结束类型0加工完成1退料2余料 -->
        RELEVANCE_ID,  <!-- 关联ID -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE, <!-- 业务单元代码 -->
        PART_ID,  <!-- 物料号 -->
        UP_PACK_TIME,  <!-- 上料时间 -->
        PACK_STATUS,  <!-- 捆包状态 -->
        UNITED_PACK_ID,  <!-- 并包号 -->
        CURRENT_KNIFE,  <!-- 当前刀号 -->
        KNIFE_SORT,  <!-- 排刀顺序 -->
        UNITED_QUANTITY, <!-- 并包数量 -->
        SUM_UNITED_QUANTITY, <!-- 累计并包量 -->
        ORIGINAL_PACK_ID,  <!-- 原始捆包号 -->
        UNITED_STACK_NAME, <!-- 并包堆垛 -->
        END_STACK_NAME, <!-- 尾包堆垛 -->
        FINISHING_SHUNT_FLAG, <!-- 精整分流标记 -->
        MAT_INNER_ID,
        UNITED_UUID,
        TRADE_CODE
        )
        VALUES (#eArchivesNo#, #equipmentName#, #processCategory#, #machineCode#, #machineName#, #processOrderId#,
        #packId#, #specsDesc#, #shopsign#, #netWeight#, #prodNameCode#, #prodCname#, #startTime#, #endTime#, #endType#,
        #relevanceId#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #partId#, #upPackTime#,
        #packStatus#, #unitedPackId#, #currentKnife#,#knifeSort#,#unitedQuantity#,#sumUnitedQuantity#,
        #originalPackId#,#unitedStackName#,#endStackName#,#finishingShuntFlag#,#matInnerId#,#unitedUuid#,#tradeCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM1002 WHERE
        UUID = #uuid#
    </delete>

    <update id="updForDel">
        UPDATE ${mevgSchema}.TVGDM1002
        SET
        DEL_FLAG = #delFlag#,  <!-- 删除标记 -->
        REC_REVISOR = #recRevisor#,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#  <!-- 记录修改时刻 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateUpTime">
        UPDATE ${mevgSchema}.TVGDM1002
        SET
        UP_PACK_TIME = #upPackTime#   <!-- 上料时间 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM1002
        SET
        END_TIME = #endTime#,   <!-- 结束时间 -->
        END_TYPE = #endType#,   <!-- 结束类型0加工完成1退料2余料 -->
        UNITED_PACK_ID = #unitedPackId#,   <!-- 并包号 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        START_TIME = #startTime#,   <!-- 开始时间 -->
        PACK_STATUS = #packStatus#,   <!-- 捆包状态 -->
        CURRENT_KNIFE = #currentKnife#,   <!-- 当前刀号 -->
        UNITED_QUANTITY = #unitedQuantity#, <!-- 并包数量 -->
        SUM_UNITED_QUANTITY = #sumUnitedQuantity#, <!-- 累计并包量 -->
        UNITED_STACK_NAME = #unitedStackName#, <!-- 并包堆垛 -->
        END_STACK_NAME = #endStackName#, <!-- 尾包堆垛 -->
        UNITED_UUID = #unitedUuid# <!-- 并包单据号 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>