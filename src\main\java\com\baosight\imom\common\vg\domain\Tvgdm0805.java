/**
 * Generate time : 2025-04-24 9:41:11
 * Version : 1.0
 */
package com.baosight.imom.common.vg.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * Tvgdm0805
 */
public class Tvgdm0805 extends DaoEPBase {

    private String overhaulPlanId = " ";        /* 检修计划编号*/
    private String itemType = " ";        /* 项目类型10检修20维修*/
    @NotNull(message = "序号不能为空")
    private Integer sortIndex = 1;        /* 序号*/
    @NotBlank(message = "项目名称不能为空")
    private String itemName = " ";        /* 项目名称*/
    @NotBlank(message = "开始时间不能为空")
    private String beginMinute = " ";        /* 开始分钟*/
    @NotBlank(message = "结束时间不能为空")
    private String endMinute = " ";        /* 结束分钟*/
    private String responsePerson = " ";        /* 责任人*/
    private String assistPerson = " ";        /* 协助人*/
    private String uuid = " ";        /* 唯一编码*/
    private String recCreator = " ";        /* 记录创建责任者*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时刻*/
    private String recRevisor = " ";        /* 记录修改责任者*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时刻*/
    private String tenantId = " ";        /* 租户ID*/
    private String archiveFlag = "0";        /* 归档标记*/
    private String delFlag = "0";        /* 删除标记*/
    private String segNo = " ";        /* 系统帐套*/
    private String unitCode = " ";        /* 业务单元代码*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("overhaulPlanId");
        eiColumn.setDescName("检修计划编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("itemType");
        eiColumn.setDescName("项目类型10检修20维修");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortIndex");
        eiColumn.setDescName("序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("itemName");
        eiColumn.setDescName("项目名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("beginMinute");
        eiColumn.setDescName("开始分钟");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endMinute");
        eiColumn.setDescName("结束分钟");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("responsePerson");
        eiColumn.setDescName("责任人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("assistPerson");
        eiColumn.setDescName("协助人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("唯一编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改责任者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时刻");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统帐套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tvgdm0805() {
        initMetaData();
    }

    /**
     * get the overhaulPlanId - 检修计划编号
     *
     * @return the overhaulPlanId
     */
    public String getOverhaulPlanId() {
        return this.overhaulPlanId;
    }

    /**
     * set the overhaulPlanId - 检修计划编号
     */
    public void setOverhaulPlanId(String overhaulPlanId) {
        this.overhaulPlanId = overhaulPlanId;
    }

    /**
     * get the itemType - 项目类型10检修20维修
     *
     * @return the itemType
     */
    public String getItemType() {
        return this.itemType;
    }

    /**
     * set the itemType - 项目类型10检修20维修
     */
    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    /**
     * get the sortIndex - 序号
     *
     * @return the sortIndex
     */
    public Integer getSortIndex() {
        return this.sortIndex;
    }

    /**
     * set the sortIndex - 序号
     */
    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }

    /**
     * get the itemName - 项目名称
     *
     * @return the itemName
     */
    public String getItemName() {
        return this.itemName;
    }

    /**
     * set the itemName - 项目名称
     */
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    /**
     * get the beginMinute - 开始分钟
     *
     * @return the beginMinute
     */
    public String getBeginMinute() {
        return this.beginMinute;
    }

    /**
     * set the beginMinute - 开始分钟
     */
    public void setBeginMinute(String beginMinute) {
        this.beginMinute = beginMinute;
    }

    /**
     * get the endMinute - 结束分钟
     *
     * @return the endMinute
     */
    public String getEndMinute() {
        return this.endMinute;
    }

    /**
     * set the endMinute - 结束分钟
     */
    public void setEndMinute(String endMinute) {
        this.endMinute = endMinute;
    }

    /**
     * get the responsePerson - 责任人
     *
     * @return the responsePerson
     */
    public String getResponsePerson() {
        return this.responsePerson;
    }

    /**
     * set the responsePerson - 责任人
     */
    public void setResponsePerson(String responsePerson) {
        this.responsePerson = responsePerson;
    }

    /**
     * get the assistPerson - 协助人
     *
     * @return the assistPerson
     */
    public String getAssistPerson() {
        return this.assistPerson;
    }

    /**
     * set the assistPerson - 协助人
     */
    public void setAssistPerson(String assistPerson) {
        this.assistPerson = assistPerson;
    }

    /**
     * get the uuid - 唯一编码
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - 唯一编码
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建责任者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建责任者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时刻
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时刻
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改责任者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改责任者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时刻
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时刻
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the tenantId - 租户ID
     *
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 删除标记
     *
     * @return the delFlag
     */
    public String getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the segNo - 系统帐套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统帐套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setOverhaulPlanId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("overhaulPlanId")), overhaulPlanId));
        setItemType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("itemType")), itemType));
        setSortIndex(NumberUtils.toInteger(StringUtils.toString(map.get("sortIndex")), sortIndex));
        setItemName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("itemName")), itemName));
        setBeginMinute(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("beginMinute")), beginMinute));
        setEndMinute(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endMinute")), endMinute));
        setResponsePerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("responsePerson")), responsePerson));
        setAssistPerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("assistPerson")), assistPerson));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("delFlag")), delFlag));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("overhaulPlanId", StringUtils.toString(overhaulPlanId, eiMetadata.getMeta("overhaulPlanId")));
        map.put("itemType", StringUtils.toString(itemType, eiMetadata.getMeta("itemType")));
        map.put("sortIndex", StringUtils.toString(sortIndex, eiMetadata.getMeta("sortIndex")));
        map.put("itemName", StringUtils.toString(itemName, eiMetadata.getMeta("itemName")));
        map.put("beginMinute", StringUtils.toString(beginMinute, eiMetadata.getMeta("beginMinute")));
        map.put("endMinute", StringUtils.toString(endMinute, eiMetadata.getMeta("endMinute")));
        map.put("responsePerson", StringUtils.toString(responsePerson, eiMetadata.getMeta("responsePerson")));
        map.put("assistPerson", StringUtils.toString(assistPerson, eiMetadata.getMeta("assistPerson")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));

        return map;

    }
}