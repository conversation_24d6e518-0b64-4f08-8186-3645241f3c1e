package com.baosight.imom.common.utils;

import com.baosight.hdjni.HDErrcode;
import com.baosight.hdsdk.HDConnectionFactory;
import com.baosight.hdsdk.HDDataProvider;
import com.baosight.hdsdk.HDServerFactory;
import com.baosight.hdsdk.HDTagManager;
import com.baosight.hdsdk.common.HDQueryRelation;
import com.baosight.hdsdk.common.HDQueryTagCondition;
import com.baosight.hdsdk.domain.data.HDBasicTag;
import com.baosight.hdsdk.domain.data.HDDataConnection;
import com.baosight.hdsdk.domain.data.HDDataServer;
import com.baosight.hdsdk.domain.data.HDRecord;
import com.baosight.hdsdk.exception.HDSdkException;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;

import cn.hutool.core.util.NumberUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.baosight.hdsdk.common.HDTagDataType.*;

/**
 * IHD平台工具类（基于java sdk）
 *
 * <AUTHOR> 郁在杰
 * @Description :IHD平台工具类
 * @Date : 2025/02/12
 * @Version : 1.0
 */
public class IhdSdkUtils {
    // 数据源相关配置
    private static final String SERVER_IP_STR = ".ihd.serverIp";
    private static final String SERVER_PORT_STR = ".ihd.serverPort";
    private static final String USER_NAME_STR = ".ihd.userName";
    private static final String PASSWORD_STR = ".ihd.password";
    // 缓存各业务单元下IHD配置
    private static final Map<String, IhdConfig> IHD_CONFIG_MAP = new ConcurrentHashMap<>();

    // IHD配置类
    private static class IhdConfig {
        private final String serverIp;
        private final int serverPort;
        private final String userName;
        private final String password;

        public IhdConfig(String serverIp, int serverPort, String userName, String password) {
            this.serverIp = serverIp;
            this.serverPort = serverPort;
            this.userName = userName;
            this.password = password;
        }

        public String getServerIp() {
            return serverIp;
        }

        public int getServerPort() {
            return serverPort;
        }

        public String getUserName() {
            return userName;
        }

        public String getPassword() {
            return password;
        }

        public boolean isValid() {
            return StrUtil.isNotBlank(serverIp) && serverPort > 0 && StrUtil.isNotBlank(userName) && StrUtil.isNotBlank(password);
        }
    }

    /**
     * 获取IHD配置
     *
     * @param segNoPrefix 业务单元代码前缀
     * @return IHD配置
     */
    private static IhdConfig getIhdConfig(String segNoPrefix) {
        // 先从缓存中获取
        IhdConfig config = IHD_CONFIG_MAP.get(segNoPrefix);
        if (config != null) {
            return config;
        }
        // 从配置中获取
        config = new IhdConfig(
                PlatApplicationContext.getProperty(segNoPrefix + SERVER_IP_STR),
                Integer.parseInt(PlatApplicationContext.getProperty(segNoPrefix + SERVER_PORT_STR)),
                PlatApplicationContext.getProperty(segNoPrefix + USER_NAME_STR),
                PlatApplicationContext.getProperty(segNoPrefix + PASSWORD_STR));
        // 如果配置不合法，则抛出异常
        if (!config.isValid()) {
            throw new PlatException("IHD配置不合法，segNoPrefix：" + segNoPrefix);
        }
        // 添加至缓存
        IHD_CONFIG_MAP.put(segNoPrefix, config);
        return config;
    }

    /**
     * 获取IHD配置
     *
     * @param segNo 业务单元代码
     * @return IHD配置
     */
    private static HDDataConnection getConnection(String segNo) throws HDSdkException {
        // 获取IHD配置
        String segNoPrefix = segNo.substring(0, 2);
        IhdConfig config = getIhdConfig(segNoPrefix);
        LogUtils.log("获取ihd连接getConnection" + segNoPrefix);
        // 获取ihd连接
        HDDataServer server = HDServerFactory.getHDDataServer(config.getServerIp(), config.getServerPort());
        HDDataConnection connection = HDConnectionFactory.getHDDataConnection(server, config.getUserName(), config.getPassword());
        LogUtils.log("获取连接成功");
        return connection;
    }

    /**
     * 查询tag
     *
     * @param segNo   业务单元代码
     * @param tagId   节点代码
     * @param tagName 节点名称
     * @param fuzzy   是否模糊查询
     * @return tag列表
     */
    public static List<HDBasicTag> queryTags(String segNo, String tagId, String tagName, boolean fuzzy) throws HDSdkException {
        // 定义ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 获取tag管理器
        HDTagManager tagManager = connection.getTagManager();
        // 查询条件
        List<HDQueryTagCondition> conditions = new ArrayList<>();
        long returnPropMask = 397313L;
        long nExtPropMask = 0L;
        // 点位代码查询条件
        HDQueryTagCondition nameCondition = new HDQueryTagCondition();
        nameCondition.setTagPropID((byte) 0);
        // 点位名称查询条件
        HDQueryTagCondition descCondition = new HDQueryTagCondition();
        descCondition.setTagPropID((byte) 12);
        // 模糊查询
        if (fuzzy) {
            nameCondition.setQueryRelation(HDQueryRelation.LIKE);
            nameCondition.setQueryValue("*" + tagId + "*");
            conditions.add(nameCondition);
            descCondition.setQueryRelation(HDQueryRelation.LIKE);
            descCondition.setQueryValue("*" + tagName + "*");
            conditions.add(descCondition);
        } else {
            if (StrUtil.isNotBlank(tagId)) {
                nameCondition.setQueryRelation(HDQueryRelation.EQUAL);
                nameCondition.setQueryValue(tagId);
                conditions.add(nameCondition);
            }
            if (StrUtil.isNotBlank(tagName)) {
                descCondition.setQueryRelation(HDQueryRelation.EQUAL);
                descCondition.setQueryValue(tagName);
                conditions.add(descCondition);
            }
        }
        // 获取tag
        List<HDBasicTag> tags = tagManager.queryBasicTagsByConds(conditions, returnPropMask, nExtPropMask);
        // 关闭连接
        connection.dispose();
        // 返回tags
        return tags;
    }

    /**
     * 根据节点代码（tagId）查询ihd中对应的节点id（tagIhdId）
     *
     * @param segNo     业务单元代码
     * @param tagIdList tagId列表
     * @return tagIdMap map中key为tagId, value为tagIhdId
     * @throws HDSdkException ihd异常
     */
    public static Map<String, Integer> queryTagIhdId(String segNo, List<String> tagIdList) throws HDSdkException {
        // 参数校验
        validateParams(segNo, tagIdList);
        // 定义返回结果
        Map<String, Integer> tagIdMap = new HashMap<>();
        // 获取ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 获取tag管理器
        HDTagManager tagManager = connection.getTagManager();
        // 接口参数用于判断是否有异常
        HDErrcode hdErrcode = new HDErrcode();
        // 查询tagId
        List<ImmutablePair<Integer, Integer>> result = tagManager.getTagIDsByTagNames(tagIdList, hdErrcode);
        if (hdErrcode.Value != 0) {
            LogUtils.log("调用接口getTagIDsByTagNames失败err:" + hdErrcode.Value);
        }
        // 关闭连接
        connection.dispose();
        // 遍历tagIdList，将tagId和tagIhdId放入map中
        for (int i = 0; i < tagIdList.size(); i++) {
            if (hdErrcode.Value != 0 && result.get(i).getRight() != 0) {
                LogUtils.log("ihd中未找到对应id,err:" + result.get(i).getRight() + "|tagName:" + tagIdList.get(i));
                continue;
            }
            tagIdMap.put(tagIdList.get(i), result.get(i).getLeft());
        }
        return tagIdMap;
    }

    /**
     * 验证参数
     *
     * @param segNo     业务单元代码
     * @param tagIdList 点位代码列表
     */
    private static void validateParams(String segNo, List<String> tagIdList) {
        if (StrUtil.isBlank(segNo)) {
            throw new IllegalArgumentException("业务单元代码不能为空");
        }
        if (CollectionUtils.isEmpty(tagIdList)) {
            throw new IllegalArgumentException("点位代码列表不能为空");
        }
    }

    /**
     * 查询某个时间范围的点位值（每个点位最大65535条）
     *
     * @param segNo        业务单元代码
     * @param tagIhdIdList 点位ID列表(tagId为ihd中的编号，原始类型为int需转为string)
     * @param startTime    开始时间
     * @param endTime      结束时间（为空时，查询每个点位从开始时间往后的1000条）
     * @return 历史值列表 按tagTime升序排序
     * @throws HDSdkException ihd异常
     */
    public static List<Map> querySortedHisRecord(String segNo, List<String> tagIhdIdList, Date startTime, Date endTime) throws HDSdkException {
        // 参数校验是否未范围查询
        boolean isRange = validateParams(segNo, startTime, endTime, tagIhdIdList);
        // 获取ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 定义返回结果
        List<Map> resultList = new ArrayList<>();
        // 获取数据管理器
        HDDataProvider dataProvider = connection.getDataProvider();
        for (String tagIhdId : tagIhdIdList) {
            // 根据查询类型调用不同接口
            List<HDRecord> records = isRange ?
                    dataProvider.queryTagHisRawRecords(Integer.parseInt(tagIhdId), startTime, endTime)
                    : dataProvider.queryTagRawRecordsByCount(Integer.parseInt(tagIhdId), startTime, false, 1000);
            if (CollectionUtils.isNotEmpty(records)) {
                for (HDRecord record : records) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("tagIhdId", tagIhdId);
                    map.put("tagValue", record.getValueStr());
                    long microSecond = record.getSecond() * 1000L + (long) record.getMicroSecond();
                    map.put("tagTimeMicroSecond", microSecond);
                    resultList.add(map);
                }
            }
        }
        // 关闭连接
        connection.dispose();
        // 排序
        if (CollectionUtils.isNotEmpty(resultList)) {
            // 遍历list中map,按tagTimeMicroSecond升序排序
            resultList.sort(Comparator.comparing(map -> (Long) map.get("tagTimeMicroSecond")));
        }
        return resultList;
    }

    /**
     * 查询某个时间范围的点位值（每个点位最大65535条）
     *
     * @param segNo     业务单元代码
     * @param tagIhdId  点位ID(tagId为ihd中的编号，原始类型为int需转为string)
     * @param startTime 开始时间
     * @param endTime   结束时间（为空时，查询每个点位从开始时间往后的1000条）
     * @return 历史值列表 按tagTime升序排序
     * @throws HDSdkException ihd异常
     */
    public static List<HDRecord> querySortedRecord(String segNo, String tagIhdId, Date startTime, Date endTime) throws HDSdkException {
        // 获取ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 获取数据管理器
        HDDataProvider dataProvider = connection.getDataProvider();
        // 根据查询类型调用不同接口
        List<HDRecord> records =
                dataProvider.queryTagHisRawRecords(Integer.parseInt(tagIhdId), startTime, endTime);
        // 关闭连接
        connection.dispose();
        return records;
    }

    /**
     * 查询某个时间范围的点位值（每个点位最大65535条）
     *
     * @param segNo        业务单元代码
     * @param tagIhdIdList 点位ID列表(tagIhdId为ihd中的编号，原始类型为int需转为string)
     * @param startTime    开始时间
     * @param endTime      结束时间（为空时，查询每个点位从开始时间往后的1000条）
     * @return 历史值列表 map中key为tagIhdId, value为历史值列表,历史值列表中key为tagTime、tagValue
     * @throws HDSdkException ihd异常
     */
    public static Map<String, List<Map<String, Object>>> queryHisValue(String segNo, List<String> tagIhdIdList, Date startTime, Date endTime) throws HDSdkException {
        // 参数校验是否未范围查询
        boolean isRange = validateParams(segNo, startTime, endTime, tagIhdIdList);
        // 获取ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 定义返回结果
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        // 获取tag管理器
        HDDataProvider dataProvider = connection.getDataProvider();
        for (String tagIhdId : tagIhdIdList) {
            // 根据查询类型调用不同接口
            List<HDRecord> records = isRange ?
                    dataProvider.queryTagHisRawRecords(Integer.parseInt(tagIhdId), startTime, endTime)
                    : dataProvider.queryTagRawRecordsByCount(Integer.parseInt(tagIhdId), startTime, false, 1000);
            for (HDRecord record : records) {
                Map<String, Object> map = new HashMap<>();
                // 时间格式yyyy-MM-dd HH:mm:ss
                map.put("tagTime", record.getTimeStampStr().substring(0, 19));
                // 值转为double
                if (record.getDataType() != STRING && record.getDataType() != BLOB) {
                    map.put("tagValue", Double.parseDouble(record.getValueStr()));
                } else {
                    // 文本类型保留原值
                    map.put("tagValue", record.getValueStr());
                }
                resultMap.computeIfAbsent(tagIhdId, k -> new ArrayList<>()).add(map);
            }
        }
        // 关闭连接
        connection.dispose();
        // 返回结果
        return resultMap;
    }

    /**
     * 验证输入参数
     *
     * @param segNo        业务单元代码
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param tagIhdIdList 点位ID列表
     * @return 是否为范围查询
     */
    private static boolean validateParams(String segNo, Date startTime, Date endTime, List<String> tagIhdIdList) {
        if (StrUtil.isBlank(segNo)) {
            throw new IllegalArgumentException("业务单元代码不能为空");
        }
        if (startTime == null) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        if (CollectionUtils.isEmpty(tagIhdIdList)) {
            throw new IllegalArgumentException("点位ID列表不能为空");
        }
        for (String tagIhdId : tagIhdIdList) {
            if (StrUtil.isBlank(tagIhdId)) {
                throw new IllegalArgumentException("点位ID不能为空");
            }
            if (!NumberUtil.isInteger(tagIhdId)) {
                throw new IllegalArgumentException("点位ID必须为数字");
            }
        }
        LogUtils.log("查询历史值参数" + segNo + "|" + startTime + "|" + endTime + "|" + tagIhdIdList);
        if (endTime == null) {
            return false;
        }
        if (endTime.before(startTime)) {
            throw new IllegalArgumentException("结束时间不能小于开始时间");
        }
        return true;
    }

    /**
     * 查询点位当前值
     *
     * @param segNo    业务单元代码
     * @param tagIhdId 点位ID(tagId为ihd中的编号，原始类型为int需转为string)
     * @return 当前值
     * @throws HDSdkException ihd异常
     */
    public static HDRecord querySnapshot(String segNo, String tagIhdId) throws HDSdkException {
        // 获取ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 获取数据管理器
        HDDataProvider dataProvider = connection.getDataProvider();
        HDRecord record = dataProvider.querySnapshotByTagID(Integer.parseInt(tagIhdId));
        // 关闭连接
        connection.dispose();
        // 返回
        return record;
    }

    /**
     * 查询某个时间点最近的点位
     *
     * @param segNo      业务单元代码
     * @param tagIhdId   点位ID(ihd中的编号，原始类型为int需转为string)
     * @param startTime  开始时间
     * @param beforeTime 向前还是向后查询；true-向后查询(查询出的记录时间小于起始时间)；false-向前查询(记录时间大于起始时间)
     * @return 点位记录或null
     * @throws Exception 异常
     */
    public static HDRecord querySingle(String segNo, String tagIhdId, String startTime, boolean beforeTime) throws Exception {
        // 根据查询类型调用不同接口
        List<HDRecord> records =
                queryWithCount(segNo, tagIhdId, startTime, beforeTime, 1);
        if (CollectionUtils.isEmpty(records)) {
            LogUtils.log("tag查询结果为空");
            HDRecord temp = new HDRecord();
            temp.setValueStr("0");
            return temp;
        }
        return records.get(0);
    }

    /**
     * 查询某个时间点最近的点位
     *
     * @param segNo      业务单元代码
     * @param tagIhdId   点位ID(ihd中的编号，原始类型为int需转为string)
     * @param startTime  开始时间yyyyMMddHHmmss
     * @param beforeTime 向前还是向后查询；true-向后查询(查询出的记录时间小于起始时间)；false-向前查询(记录时间大于起始时间)
     * @param count      查询记录数
     * @return 点位记录或null
     * @throws Exception 异常
     */
    public static List<HDRecord> queryWithCount(String segNo, String tagIhdId, String startTime, boolean beforeTime, int count) throws Exception {
        LogUtils.log("查询固定数量点位：" + segNo + "|" + tagIhdId + "|" + startTime + "|" + beforeTime + "|" + count);
        // 获取ihd连接
        HDDataConnection connection = getConnection(segNo);
        // 获取数据管理器
        HDDataProvider dataProvider = connection.getDataProvider();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date start = sdf.parse(startTime);
        // 按数量查询
        List<HDRecord> records =
                dataProvider.queryTagRawRecordsByCount(Integer.parseInt(tagIhdId), start, beforeTime, count);
        // 关闭连接
        connection.dispose();
        return records;
    }
}
