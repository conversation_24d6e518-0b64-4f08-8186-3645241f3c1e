$(function () {
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        const validator = IPLAT.Validator({
            id: "inqu"
        });
        if (!validator.validate()) {
            return;
        }
        $("#inqu_status-0-deptId").val('ddd');
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "wlStockQty",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        return Number(item["wlStockQty"]) - Number(item["usingWgt"]);
                    }
                }
            ],
            //双击选中
            onRowDblClick: function (e) {
                var windowId = $("#inqu_status-0-windowId").val();
                if (IPLAT.isBlankString(windowId)) {
                    // 设置默认值
                    windowId = "stuffInfo";
                }
                //双击选中前先把双击的数据勾选上
                resultGrid.unCheckAllRows();
                resultGrid.setCheckedRows(e.row);
                //关闭下拉框
                window.parent[windowId + "Window"].close();
            }
        }
    };

    // 查询条件区域库区代码弹窗
    IMOMUtil.windowTemplate({
        windowId: "warehouseInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            const segNo = $("#inqu_status-0-segNo").val();
            const segName = $("#inqu_status-0-segName").val();
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-segName").val(segName);
            // 指定资材库
            iframejQuery("#inqu_status-0-wareHouseBusinessType").val("80");
        },
        assignMap: {
            "inqu_status-0-warehouseCode": "stockCode",
            "inqu_status-0-warehouseName": "stockName"
        }
    });

});
