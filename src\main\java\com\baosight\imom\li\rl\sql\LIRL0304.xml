<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-08-26 9:06:18
   		Version :  1.0
		tableName :${meliSchema}.tlirl0304
		 SEG_NO  VARCHAR   NOT NULL,
		 UNIT_CODE  VARCHAR   NOT NULL,
		 HAND_POINT_ID  VARCHAR   NOT NULL,
		 HAND_POINT_NAME  VARCHAR   NOT NULL,
		 HAND_ORDER  VARCHAR   NOT NULL,
		 CAR_TRACE_NO  VARCHAR   NOT NULL,
		 ORDER_NUMBER  VARCHAR   NOT NULL,
		 VEHICLE_NUMER  VARCHAR   NOT NULL,
		 LOAD_FLAG  VARCHAR   NOT NULL,
		 UNLOAD_FLAG  VARCHAR   NOT NULL,
		 FACTORY_AREA  VARCHAR   NOT NULL,
		 CLOSE_REASON  VARCHAR   NOT NULL,
		 EXPECTED_RECOVERY_TIME  VARCHAR   NOT NULL,
		 STANDARD_CAPACITY  VARCHAR   NOT NULL,
		 SEQ_NUM  VARCHAR   NOT NULL,
		 REC_CREATOR  VARCHAR   NOT NULL,
		 REC_CREATOR_NAME  VARCHAR   NOT NULL,
		 REC_CREATE_TIME  VARCHAR   NOT NULL,
		 REC_REVISOR  VARCHAR   NOT NULL,
		 REC_REVISOR_NAME  VARCHAR   NOT NULL,
		 REC_REVISE_TIME  VARCHAR   NOT NULL,
		 ARCHIVE_FLAG  SMALLINT   NOT NULL,
		 DEL_FLAG  SMALLINT   NOT NULL,
		 REMARK  VARCHAR   NOT NULL,
		 SYS_REMARK  VARCHAR   NOT NULL,
		 UUID  VARCHAR   NOT NULL,
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0304">

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0304">
		SELECT
				tlirl0304.SEG_NO	as "segNo",  <!-- 系统账套 -->
				tlirl0304.UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as
			     "segName", <!-- 业务单元简称 -->
				tlirl0304.HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
				tlirl0304.HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
				tlirl0304.LOADING_CHANNEL_ID as "loadingChannelId",
				tlirl0304.LOADING_CHANNEL_NAME as "loadingChannelName",
				tlirl0304.STATUS	as "status",  <!-- 装卸点名称 -->
				tlirl0304.HAND_ORDER	as "handOrder",  <!-- 装卸顺序 -->
				tlirl0304.CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				tlirl0304.ORDER_NUMBER	as "orderNumber",  <!-- 顺序号 -->
				tlirl0304.VEHICLE_NUMER	as "vehicleNumer",  <!-- 车辆容纳数 -->
				tlirl0304.LOAD_FLAG	as "loadFlag",  <!-- 装货标记 -->
				tlirl0304.UNLOAD_FLAG	as "unloadFlag",  <!-- 卸货标记 -->
				tlirl0304.FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				tlirl0304.FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
				tlirl0304.CLOSE_REASON	as "closeReason",  <!-- 停用原因 -->
				tlirl0304.EXPECTED_RECOVERY_TIME	as "expectedRecoveryTime",  <!-- 预计恢复时间 -->
				tlirl0304.STANDARD_CAPACITY	as "standardCapacity",  <!-- 标准容量（吨） -->
				tlirl0304.SEQ_NUM	as "seqNum",  <!-- 系统顺序号 -->
				tlirl0304.REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				tlirl0304.REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				tlirl0304.REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0304.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0304.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0304.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0304.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0304.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0304.REMARK as "remark",  <!-- 备注 -->
		tlirl0304.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
		tlirl0304.UUID as "uuid",  <!-- uuid -->
		tlirl0304.TENANT_ID as "tenantId",
		(select BUSINESS_TYPE from ${meliSchema}.tlirl0309 tlirl0309 where 1=1 and tlirl0309.SEG_NO= tlirl0304.SEG_NO and
		tlirl0309.HAND_POINT_ID=tlirl0304.HAND_POINT_ID
		and tlirl0309.DEL_FLAG='0' limit 1
		) as "businessType",<!-- 业务类型 -->
		tlirl0304.FACTORY_BUILDING as "factoryBuilding",
		tlirl0304.FACTORY_BUILDING_NAME as "factoryBuildingName",
		tlirl0304.CRANE_ID as "craneId",
		tlirl0304.CRANE_NAME as "craneName",
		X_INITIAL_POINT as "xInitialPoint",  <!-- X轴起始点 -->
		X_DESTINATION as "xDestination",  <!-- X轴终到点 -->
		Y_INITIAL_POINT as "yInitialPoint",  <!-- Y轴起始点 -->
		Y_DESTINATION as "yDestination"  <!-- Y轴终到点 -->
		FROM ${meliSchema}.tlirl0304  tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0304.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0304.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			tlirl0304.HAND_POINT_ID like concat('%',#handPointId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			tlirl0304.HAND_POINT_NAME like concat('%',#handPointName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			tlirl0304.CRANE_ID like concat('%',#craneId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			tlirl0304.CRANE_NAME like concat('%',#craneName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			tlirl0304.FACTORY_AREA =#factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			tlirl0304.FACTORY_BUILDING =#factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="loadFlag">
			tlirl0304.LOAD_FLAG =#loadFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="loadingChannelId">
			LOADING_CHANNEL_ID = #loadingChannelId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="XPosition">
			tlirl0304.X_INITIAL_POINT - 15 &lt;= #XPosition#<!--X坐标值-->
			AND tlirl0304.X_DESTINATION + 15 &gt;= #XPosition#<!--X坐标值-->
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="YPosition">
			tlirl0304.Y_INITIAL_POINT - 15 &lt;= #YPosition#<!--Y坐标值-->
			AND tlirl0304.Y_DESTINATION + 15 &gt;= #YPosition#<!--Y坐标值-->
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessType">
			exists(select 1 from ${meliSchema}.tlirl0309 tlirl0309
			where 1=1 and tlirl0309.SEG_NO= tlirl0304.SEG_NO and tlirl0309.HAND_POINT_ID=tlirl0304.HAND_POINT_ID
			and tlirl0309.BUSINESS_TYPE=#businessType#
			and tlirl0309.DEL_FLAG='0')
		</isNotEmpty>
		<isNotEmpty prepend="and" property="handPointIdList">
			tlirl0304.HAND_POINT_ID IN
			<iterate open="(" close=")" conjunction="," property="handPointIdList">
				#handPointIdList[]#
			</iterate>
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
			<isEmpty property="orderBy">
				tlirl0304.REC_CREATE_TIME desc
			</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="loadingChannelId">
			LOADING_CHANNEL_ID = #loadingChannelId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
	</select>


	<select id="queryHandPointInfo" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0304">
		SELECT
		tlirl0304.SEG_NO	as "segNo",  <!-- 系统账套 -->
		tlirl0304.UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0304.HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
		tlirl0304.HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
		tlirl0304.LOADING_CHANNEL_ID as "loadingChannelId",
		tlirl0304.LOADING_CHANNEL_NAME as "loadingChannelName",
		tlirl0304.STATUS	as "status",  <!-- 装卸点名称 -->
		tlirl0304.HAND_ORDER	as "handOrder",  <!-- 装卸顺序 -->
		tlirl0304.CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		tlirl0304.ORDER_NUMBER	as "orderNumber",  <!-- 顺序号 -->
		tlirl0304.VEHICLE_NUMER	as "vehicleNumer",  <!-- 车辆容纳数 -->
		tlirl0304.LOAD_FLAG	as "loadFlag",  <!-- 装货标记 -->
		tlirl0304.UNLOAD_FLAG	as "unloadFlag",  <!-- 卸货标记 -->
		tlirl0304.FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		tlirl0304.FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
		tlirl0304.CLOSE_REASON	as "closeReason",  <!-- 停用原因 -->
		tlirl0304.EXPECTED_RECOVERY_TIME	as "expectedRecoveryTime",  <!-- 预计恢复时间 -->
		tlirl0304.STANDARD_CAPACITY	as "standardCapacity",  <!-- 标准容量（吨） -->
		tlirl0304.SEQ_NUM	as "seqNum",  <!-- 系统顺序号 -->
		tlirl0304.REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		tlirl0304.REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0304.REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0304.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0304.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0304.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0304.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0304.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0304.REMARK as "remark",  <!-- 备注 -->
		tlirl0304.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
		tlirl0304.UUID as "uuid",  <!-- uuid -->
		tlirl0304.TENANT_ID as "tenantId",
		tlirl0304.FACTORY_BUILDING as "factoryBuilding",
		tlirl0304.FACTORY_BUILDING_NAME as "factoryBuildingName"
		FROM ${meliSchema}.tlirl0304  tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0304.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0304.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			tlirl0304.HAND_POINT_ID like concat('%',#handPointId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			tlirl0304.HAND_POINT_NAME like concat('%',#handPointName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			tlirl0304.FACTORY_AREA =#factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			tlirl0304.FACTORY_BUILDING =#factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="handPointIdList">
			tlirl0304.HAND_POINT_ID IN
			<iterate open="(" close=")" conjunction="," property="handPointIdList">
				#handPointIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			exists(select 1
			from meli.tlirl0410 tlirl0410
			where 1 = 1
			and tlirl0410.SEG_NO = tlirl0304.SEG_NO
			and tlirl0410.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
			and tlirl0410.VEHICLE_NO =#vehicleNo#
			and tlirl0410.CAR_TRACE_NO= #carTraceNo#
			<isNotEmpty property="noAllocateVehicleNo" prepend="and ">
				tlirl0410.VOUCHER_NUM != #noAllocateVehicleNo#
			</isNotEmpty>
			and tlirl0304.STATUS = '30')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="warehouseCode">
			tlirl0304.FACTORY_BUILDING in (
			(select DISTINCT tlids0601.FACTORY_BUILDING
			from meli.tlids0601 tlids0601
			where 1 = 1
			and tlirl0304.SEG_NO = tlids0601.SEG_NO
			and WAREHOUSE_CODE = #warehouseCode#))
		</isNotEmpty>
<!--		<isNotEmpty  prepend=" AND " property="carTraceNo">-->
<!--			not exists(select 1-->
<!--			from meli.tlirl0301 tlirl0301-->
<!--			where 1 = 1-->
<!--			and tlirl0301.SEG_NO = tlirl0304.SEG_NO-->
<!--			and tlirl0301.CURRENT_HAND_POINT_ID = tlirl0304.HAND_POINT_ID-->
<!--			and tlirl0301.VEHICLE_NO =#vehicleNo#-->
<!--			and tlirl0301.CAR_TRACE_NO = #carTraceNo#-->
<!--			and tlirl0301.STATUS = '30')-->
<!--		</isNotEmpty>-->
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				tlirl0304.HAND_POINT_NAME asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="queryHandPointInfo1" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0304">
		SELECT
		tlirl0304.SEG_NO	as "segNo",  <!-- 系统账套 -->
		tlirl0304.UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0304.HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
		tlirl0304.HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
		tlirl0304.LOADING_CHANNEL_ID as "loadingChannelId",
		tlirl0304.LOADING_CHANNEL_NAME as "loadingChannelName",
		tlirl0304.STATUS	as "status",  <!-- 装卸点名称 -->
		tlirl0304.HAND_ORDER	as "handOrder",  <!-- 装卸顺序 -->
		tlirl0304.CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		tlirl0304.ORDER_NUMBER	as "orderNumber",  <!-- 顺序号 -->
		tlirl0304.VEHICLE_NUMER	as "vehicleNumer",  <!-- 车辆容纳数 -->
		tlirl0304.LOAD_FLAG	as "loadFlag",  <!-- 装货标记 -->
		tlirl0304.UNLOAD_FLAG	as "unloadFlag",  <!-- 卸货标记 -->
		tlirl0304.FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		tlirl0304.FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
		tlirl0304.CLOSE_REASON	as "closeReason",  <!-- 停用原因 -->
		tlirl0304.EXPECTED_RECOVERY_TIME	as "expectedRecoveryTime",  <!-- 预计恢复时间 -->
		tlirl0304.STANDARD_CAPACITY	as "standardCapacity",  <!-- 标准容量（吨） -->
		tlirl0304.SEQ_NUM	as "seqNum",  <!-- 系统顺序号 -->
		tlirl0304.REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		tlirl0304.REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0304.REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0304.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0304.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0304.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0304.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0304.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0304.REMARK as "remark",  <!-- 备注 -->
		tlirl0304.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
		tlirl0304.UUID as "uuid",  <!-- uuid -->
		tlirl0304.TENANT_ID as "tenantId",
		tlirl0304.FACTORY_BUILDING as "factoryBuilding",
		tlirl0304.FACTORY_BUILDING_NAME as "factoryBuildingName"
		FROM ${meliSchema}.tlirl0304  tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0304.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0304.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			tlirl0304.HAND_POINT_ID like concat('%',#handPointId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			tlirl0304.HAND_POINT_NAME like concat('%',#handPointName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			tlirl0304.FACTORY_AREA =#factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			tlirl0304.FACTORY_BUILDING =#factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="handPointIdList">
			tlirl0304.HAND_POINT_ID IN
			<iterate open="(" close=")" conjunction="," property="handPointIdList">
				#handPointIdList[]#
			</iterate>
		</isNotEmpty>
		<!--		<isNotEmpty  prepend=" AND " property="carTraceNo">-->
		<!--			not exists(select 1-->
		<!--			from meli.tlirl0301 tlirl0301-->
		<!--			where 1 = 1-->
		<!--			and tlirl0301.SEG_NO = tlirl0304.SEG_NO-->
		<!--			and tlirl0301.CURRENT_HAND_POINT_ID = tlirl0304.HAND_POINT_ID-->
		<!--			and tlirl0301.VEHICLE_NO =#vehicleNo#-->
		<!--			and tlirl0301.CAR_TRACE_NO = #carTraceNo#-->
		<!--			and tlirl0301.STATUS = '30')-->
		<!--		</isNotEmpty>-->
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				tlirl0304.HAND_POINT_NAME asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="queryHandPointInfoAll" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0304">
		SELECT
		tlirl0304.SEG_NO	as "segNo",  <!-- 系统账套 -->
		tlirl0304.UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0304.HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
		tlirl0304.HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
		tlirl0304.LOADING_CHANNEL_ID as "loadingChannelId",
		tlirl0304.LOADING_CHANNEL_NAME as "loadingChannelName",
		tlirl0304.STATUS	as "status",  <!-- 装卸点名称 -->
		tlirl0304.HAND_ORDER	as "handOrder",  <!-- 装卸顺序 -->
		tlirl0304.CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		tlirl0304.ORDER_NUMBER	as "orderNumber",  <!-- 顺序号 -->
		tlirl0304.VEHICLE_NUMER	as "vehicleNumer",  <!-- 车辆容纳数 -->
		tlirl0304.LOAD_FLAG	as "loadFlag",  <!-- 装货标记 -->
		tlirl0304.UNLOAD_FLAG	as "unloadFlag",  <!-- 卸货标记 -->
		tlirl0304.FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		tlirl0304.FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
		tlirl0304.CLOSE_REASON	as "closeReason",  <!-- 停用原因 -->
		tlirl0304.EXPECTED_RECOVERY_TIME	as "expectedRecoveryTime",  <!-- 预计恢复时间 -->
		tlirl0304.STANDARD_CAPACITY	as "standardCapacity",  <!-- 标准容量（吨） -->
		tlirl0304.SEQ_NUM	as "seqNum",  <!-- 系统顺序号 -->
		tlirl0304.REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		tlirl0304.REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0304.REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0304.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0304.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0304.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0304.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0304.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0304.REMARK as "remark",  <!-- 备注 -->
		tlirl0304.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
		tlirl0304.UUID as "uuid",  <!-- uuid -->
		tlirl0304.TENANT_ID as "tenantId",
		tlirl0304.FACTORY_BUILDING as "factoryBuilding",
		tlirl0304.FACTORY_BUILDING_NAME as "factoryBuildingName"
		FROM ${meliSchema}.tlirl0304  tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0304.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0304.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="handPointIdList">
			tlirl0304.HAND_POINT_ID IN
			<iterate open="(" close=")" conjunction="," property="handPointIdList">
				#handPointIdList[]#
			</iterate>
		</isNotEmpty>
		union
		SELECT
		tlirl0304.SEG_NO	as "segNo",  <!-- 系统账套 -->
		tlirl0304.UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0304.HAND_POINT_ID	as "handPointId",  <!-- 装卸点代码 -->
		tlirl0304.HAND_POINT_NAME	as "handPointName",  <!-- 装卸点名称 -->
		tlirl0304.LOADING_CHANNEL_ID as "loadingChannelId",
		tlirl0304.LOADING_CHANNEL_NAME as "loadingChannelName",
		tlirl0304.STATUS	as "status",  <!-- 装卸点名称 -->
		tlirl0304.HAND_ORDER	as "handOrder",  <!-- 装卸顺序 -->
		tlirl0304.CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		tlirl0304.ORDER_NUMBER	as "orderNumber",  <!-- 顺序号 -->
		tlirl0304.VEHICLE_NUMER	as "vehicleNumer",  <!-- 车辆容纳数 -->
		tlirl0304.LOAD_FLAG	as "loadFlag",  <!-- 装货标记 -->
		tlirl0304.UNLOAD_FLAG	as "unloadFlag",  <!-- 卸货标记 -->
		tlirl0304.FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		tlirl0304.FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
		tlirl0304.CLOSE_REASON	as "closeReason",  <!-- 停用原因 -->
		tlirl0304.EXPECTED_RECOVERY_TIME	as "expectedRecoveryTime",  <!-- 预计恢复时间 -->
		tlirl0304.STANDARD_CAPACITY	as "standardCapacity",  <!-- 标准容量（吨） -->
		tlirl0304.SEQ_NUM	as "seqNum",  <!-- 系统顺序号 -->
		tlirl0304.REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		tlirl0304.REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		tlirl0304.REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		tlirl0304.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		tlirl0304.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		tlirl0304.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		tlirl0304.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		tlirl0304.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
		tlirl0304.REMARK as "remark",  <!-- 备注 -->
		tlirl0304.SYS_REMARK as "sysRemark",  <!-- 系统备注 -->
		tlirl0304.UUID as "uuid",  <!-- uuid -->
		tlirl0304.TENANT_ID as "tenantId",
		tlirl0304.FACTORY_BUILDING as "factoryBuilding",
		tlirl0304.FACTORY_BUILDING_NAME as "factoryBuildingName"
		FROM ${meliSchema}.tlirl0304  tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0304.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0304.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			tlirl0304.HAND_POINT_ID like concat('%',#handPointId#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			tlirl0304.HAND_POINT_NAME like concat('%',#handPointName#,'%')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			tlirl0304.FACTORY_AREA =#factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			tlirl0304.FACTORY_BUILDING =#factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="handPointIdList">
			tlirl0304.HAND_POINT_ID IN
			<iterate open="(" close=")" conjunction="," property="handPointIdList">
				#handPointIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			exists(select 1
			from meli.tlirl0410 tlirl0410
			where 1 = 1
			and tlirl0410.SEG_NO = tlirl0304.SEG_NO
			and tlirl0410.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
			and tlirl0410.VEHICLE_NO =#vehicleNo#
			and tlirl0410.CAR_TRACE_NO= #carTraceNo#
			<isNotEmpty property="noAllocateVehicleNo" prepend="and ">
				tlirl0410.VOUCHER_NUM != #noAllocateVehicleNo#
			</isNotEmpty>
			and tlirl0304.STATUS = '30')
		</isNotEmpty>
		<isNotEmpty  prepend=" AND " property="carTraceNo">
			not exists(select 1
			from meli.tlirl0301 tlirl0301
			where 1 = 1
			and tlirl0301.SEG_NO = tlirl0304.SEG_NO
			and tlirl0301.CURRENT_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
			and tlirl0301.VEHICLE_NO =#vehicleNo#
			and tlirl0301.CAR_TRACE_NO = #carTraceNo#
			and tlirl0301.STATUS = '30')
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				"handPointName" asc
			</isEmpty>
		</dynamic>

	</select>
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointName">
			HAND_POINT_NAME = #handPointName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handOrder">
			HAND_ORDER = #handOrder#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="orderNumber">
			ORDER_NUMBER = #orderNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNumer">
			VEHICLE_NUMER = #vehicleNumer#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="loadFlag">
			LOAD_FLAG = #loadFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unloadFlag">
			UNLOAD_FLAG = #unloadFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="closeReason">
			CLOSE_REASON = #closeReason#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="expectedRecoveryTime">
			EXPECTED_RECOVERY_TIME = #expectedRecoveryTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="standardCapacity">
			STANDARD_CAPACITY = #standardCapacity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="seqNum">
			SEQ_NUM = #seqNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0304 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										HAND_POINT_ID,  <!-- 装卸点代码 -->
										HAND_POINT_NAME,  <!-- 装卸点名称 -->
										STATUS,  <!-- 装卸点名称 -->
										HAND_ORDER,  <!-- 装卸顺序 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										ORDER_NUMBER,  <!-- 顺序号 -->
										VEHICLE_NUMER,  <!-- 车辆容纳数 -->
										LOAD_FLAG,  <!-- 装货标记 -->
										UNLOAD_FLAG,  <!-- 卸货标记 -->
										FACTORY_AREA,  <!-- 厂区 -->
										FACTORY_AREA_NAME,  <!-- 厂区名称 -->
										CLOSE_REASON,  <!-- 停用原因 -->
										EXPECTED_RECOVERY_TIME,  <!-- 预计恢复时间 -->
										STANDARD_CAPACITY,  <!-- 标准容量（吨） -->
										SEQ_NUM,  <!-- 系统顺序号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
		LOADING_CHANNEL_ID,
		LOADING_CHANNEL_NAME,
		FACTORY_BUILDING,
		FACTORY_BUILDING_NAME,
		CRANE_ID,
		CRANE_NAME,
		X_INITIAL_POINT,
		X_DESTINATION,
		Y_INITIAL_POINT,
		Y_DESTINATION
										)
	    VALUES (#segNo#, #unitCode#, #handPointId#, #handPointName#,#status#, #handOrder#, #carTraceNo#,
		#orderNumber#, #vehicleNumer#, #loadFlag#, #unloadFlag#, #factoryArea#, #factoryAreaName#,#closeReason#,
		#expectedRecoveryTime#, #standardCapacity#, #seqNum#, #recCreator#, #recCreatorName#,
		#recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#,
		#remark#, #sysRemark#, #uuid#, #tenantId#,#loadingChannelId#,#loadingChannelName#,
		#factoryBuilding#,#factoryBuildingName#,#craneId#,#craneName#,#xInitialPoint#,#xDestination#,#yInitialPoint#,#yDestination#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0304 WHERE 
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0304 
		SET
			SEG_NO = #segNo#
		<isNotEmpty prepend="," property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend="," property="handPointId">
			HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend="," property="handPointName">
			HAND_POINT_NAME = #handPointName#
		</isNotEmpty>
		<isNotEmpty prepend="," property="loadingChannelId">
			LOADING_CHANNEL_ID = #loadingChannelId#
		</isNotEmpty>
		<isNotEmpty prepend="," property="loadingChannelName">
			LOADING_CHANNEL_NAME = #loadingChannelName#
		</isNotEmpty>
		<isNotEmpty prepend="," property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend="," property="handOrder">
			HAND_ORDER = #handOrder#
		</isNotEmpty>
		<isNotEmpty prepend="," property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend="," property="orderNumber">
			ORDER_NUMBER = #orderNumber#
		</isNotEmpty>
		<isNotEmpty prepend="," property="vehicleNumer">
			VEHICLE_NUMER = #vehicleNumer#
		</isNotEmpty>
		<isNotEmpty prepend="," property="loadFlag">
			LOAD_FLAG = #loadFlag#
		</isNotEmpty>
		<isNotEmpty prepend="," property="unloadFlag">
			UNLOAD_FLAG = #unloadFlag#
		</isNotEmpty>
		<isNotEmpty prepend="," property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend="," property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend="," property="factoryBuilding">
			FACTORY_BUILDING = #factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend="," property="factoryBuildingName">
			FACTORY_BUILDING_NAME = #factoryBuildingName#
		</isNotEmpty>
		<isNotEmpty prepend="," property="closeReason">
			CLOSE_REASON = #closeReason#
		</isNotEmpty>
		<isNotEmpty prepend="," property="expectedRecoveryTime">
			EXPECTED_RECOVERY_TIME = #expectedRecoveryTime#
		</isNotEmpty>
		<isNotEmpty prepend="," property="standardCapacity">
			STANDARD_CAPACITY = #standardCapacity#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="seqNum">
			SEQ_NUM = #seqNum#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend="," property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend="," property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend="," property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty><isNotEmpty prepend="," property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="xInitialPoint">
			X_INITIAL_POINT = #xInitialPoint#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="xDestination">
			X_DESTINATION = #xDestination#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="yInitialPoint">
			Y_INITIAL_POINT = #yInitialPoint#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="yDestination">
			Y_DESTINATION = #yDestination#
		</isNotEmpty>
		WHERE 1=1 AND
		SEG_NO = #segNo# AND
		HAND_POINT_ID = #handPointId#
	</update>


	<update id="updateEnableData">
		UPDATE ${meliSchema}.tlirl0304
		SET
		SEG_NO = #segNo#
		<isNotEmpty prepend="," property="vehicleNumer">
			VEHICLE_NUMER = #vehicleNumer#
		</isNotEmpty>
		<isNotEmpty prepend="," property="handPointName">
			HAND_POINT_NAME = #handPointName#
		</isNotEmpty>
		<isNotEmpty prepend="," property="loadFlag">
			LOAD_FLAG = #loadFlag#
		</isNotEmpty>
		<isNotEmpty prepend="," property="unloadFlag">
			UNLOAD_FLAG = #unloadFlag#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=","  property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend="," property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend="," property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty><isNotEmpty prepend="," property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="xInitialPoint">
			X_INITIAL_POINT = #xInitialPoint#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="xDestination">
			X_DESTINATION = #xDestination#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="yInitialPoint">
			Y_INITIAL_POINT = #yInitialPoint#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="yDestination">
			Y_DESTINATION = #yDestination#
		</isNotEmpty>
		WHERE 1=1 AND
		SEG_NO = #segNo# AND
		HAND_POINT_ID = #handPointId#
	</update>
	
	<!--查询车辆最大容纳数最大-->
	<select id="queryVehicleNumer" parameterClass="java.util.HashMap"
		resultClass="java.util.HashMap">
		select ifnull(tlirl0304.VEHICLE_NUMER,'1') as "vehicleNumer"
		from ${meliSchema}.tlirl0304 tlirl0304
		where SEG_NO=#segNo#
		and HAND_POINT_ID=#handPointId#
	</select>

	<!--装卸点停用查询是否有未离厂的数据-->
	<select id="queryNoFactoryHandPoint" parameterClass="java.util.HashMap"
		resultClass="java.util.HashMap">
		select tlirl0301.TARGET_HAND_POINT_ID as "targetHandPointId"
		from ${meliSchema}.tlirl0301 tlirl0301,
		${meliSchema}.tlirl0408 tlirl0408
		where 1 = 1
		and tlirl0301.DEL_FLAG = '0'
		and tlirl0301.SEG_NO=#segNo#
		and (tlirl0301.TARGET_HAND_POINT_ID=#handPointId# or tlirl0301.CURRENT_HAND_POINT_ID = #handPointId#)
		and tlirl0301.STATUS not in ('00','50','60')
	</select>

	<select id="queryHandPointName" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		tlirl0304.SEG_NO as "segNo",  <!-- 系统账套 -->
		tlirl0304.UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		tlirl0304.HAND_POINT_ID as "handPointId",  <!-- 装卸点代码 -->
		tlirl0304.HAND_POINT_NAME as "handPointName"  <!-- 装卸点名称 -->
		FROM ${meliSchema}.tlirl0304 tlirl0304 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			tlirl0304.SEG_NO =#segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			tlirl0304.STATUS =#status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			tlirl0304.HAND_POINT_ID =#handPointId#
		</isNotEmpty>
	</select>

	<select id="queryAllHandPointId" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select
		t.SEG_NO as "segNo",
		(select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = t.SEG_NO and DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		t.HAND_POINT_ID as "handPointId",
		(select HAND_POINT_NAME from meli.tlirl0304 s
		where s.SEG_NO = t.SEG_NO and t.HAND_POINT_ID= s.HAND_POINT_ID
		and s.DEL_FLAG = 0 limit 1) as "handPointName",
		t.SCRAP_TYPE as "scrapType"
		from
		meli.tlirl0309 t
		where
		t.SEG_NO = #segNo#
		and t.BUSINESS_TYPE= #businessType#
		and t.STATUS = '30'
	</select>


	<select id="queryWaitCall" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select DISTINCT t1.CAR_TRACE_NO as "carTraceNo",
		t1.SEG_NO       as "segNo",
		t1.VEHICLE_NO   as "vehicleNo",
		t3.min_priority AS "priorityLevel",
		t4.min_queNumber as "queNumber"
		from meli.tlirl0401 t1
		join meli.tlirl0304 t2 on t1.SEG_NO = t2.SEG_NO and t1.TARGET_HAND_POINT_ID = t2.HAND_POINT_ID
		join (select CAR_TRACE_NO, VEHICLE_NO, min(CAST(PRIORITY_LEVEL AS UNSIGNED)) as min_priority
		from meli.tlirl0401
		where SEG_NO = 'JC000000'
		<isNotEmpty prepend="and " property="handPointId">
			TARGET_HAND_POINT_ID=#handPointId#
		</isNotEmpty>
		group by CAR_TRACE_NO, VEHICLE_NO) t3
		on t1.CAR_TRACE_NO = t3.CAR_TRACE_NO and t1.VEHICLE_NO = t3.VEHICLE_NO
		join (select CAR_TRACE_NO, VEHICLE_NO, min(CAST(QUEUE_NUMBER AS UNSIGNED)) as min_queNumber
		from meli.tlirl0401
		where SEG_NO = 'JC000000'
		<isNotEmpty prepend="and " property="handPointId">
			TARGET_HAND_POINT_ID=#handPointId#
		</isNotEmpty>
		group by CAR_TRACE_NO, VEHICLE_NO) t4
		on t1.CAR_TRACE_NO = t4.CAR_TRACE_NO and t1.VEHICLE_NO = t4.VEHICLE_NO
		where
		t2.FACTORY_AREA=#factoryArea#
		AND t2.FACTORY_BUILDING=#factoryBuilding#
		and t1.SEG_NO = #segNo#
		and t2.STATUS = '30'
		<isNotEmpty prepend="and " property="handPointId">
			t1.TARGET_HAND_POINT_ID=#handPointId#
		</isNotEmpty>
		order by t4.min_queNumber,t3.min_priority asc
	</select>


	<select id="queryWaitAllCallHandPointId"
			resultClass="String">
		select distinct tlirl0304.HAND_POINT_NAME as "handPointName"
		from meli.tlirl0401 tlirl0401,
		meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0401.SEG_NO = tlirl0304.SEG_NO
		and tlirl0401.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		and tlirl0401.SEG_NO = #segNo#
		AND tlirl0401.CAR_TRACE_NO = #carTraceNo#
		AND tlirl0401.VEHICLE_NO = #vehicleNo#
		and exists(select 1
		from meli.tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		and tlirl0304.FACTORY_AREA=#factoryArea#
		AND FACTORY_BUILDING=#factoryBuilding#
		)
		order by HAND_POINT_NAME ASC
	</select>

	<select id="queryWaitAllHandPointId"
			resultClass="String">
		select distinct tlirl0304.HAND_POINT_NAME as "handPointName"
		from meli.tlirl0401 tlirl0401,
		meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0401.SEG_NO = tlirl0304.SEG_NO
		and tlirl0401.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		and tlirl0401.SEG_NO = #segNo#
		AND tlirl0401.CAR_TRACE_NO = #carTraceNo#
		AND tlirl0401.VEHICLE_NO = #vehicleNo#
		and exists(select 1
		from meli.tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		and tlirl0304.FACTORY_AREA=#factoryArea#
		AND FACTORY_BUILDING=#factoryBuilding#
		)
		order by HAND_POINT_NAME ASC
	</select>


	<select id="queryWaitAllCall" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT distinct TARGET_HAND_POINT_ID AS "handPointId",
		CAR_TRACE_NO         as "carTraceNo",
		VEHICLE_NO           as "vehicleNo",
		(select ALLOCATE_VEHICLE_NO
		from meli.tlirl0301
		where 1 = 1
		and tlirl0301.SEG_NO = tlirl0401.SEG_NO
		AND tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		AND ALLOCATE_VEHICLE_NO != ' '
		LIMIT 1)          as "allocateVehicleNo",
		(select HAND_POINT_NAME
		from meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		limit 1)            as "handPointName"
		FROM meli.tlirl0401
		where 1 = 1
		and SEG_NO = #segNo#
		AND CAR_TRACE_NO = #carTraceNo#
		AND VEHICLE_NO = #vehicleNo#
		and exists(select 1
		from meli.tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		and tlirl0304.FACTORY_AREA=#factoryArea#
		AND FACTORY_BUILDING=#factoryBuilding#
		)
		order by TARGET_HAND_POINT_ID ASC
	</select>


	<select id="queryWaitAllCallHandId"
			resultClass="String">
		select (select HAND_POINT_NAME
		from meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		limit 1)            as "handPointName"
		FROM meli.tlirl0401
		where 1 = 1
		and SEG_NO = #segNo#
		AND CAR_TRACE_NO = #carTraceNo#
		AND VEHICLE_NO = #vehicleNo#
		and exists(select 1
		from meli.tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		and tlirl0304.FACTORY_AREA=#factoryArea#
		AND tlirl0304.FACTORY_BUILDING=#factoryBuilding#
		)
		order by TARGET_HAND_POINT_ID ASC
	</select>

<!--其他装卸点查询-->
	<select id="queryOtherHandPoint" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT tlirl0304.SEG_NO                                                                        as "segNo",
		tlirl0304.UNIT_CODE                                                                     as "unitCode",
		(select SEG_NAME from iplat4j.TVZBM81 where SEG_NO = tlirl0304.SEG_NO and DEL_FLAG = 0) as "segName",
		tlirl0304.HAND_POINT_ID                                                                 as "handPointId",
		tlirl0304.HAND_POINT_NAME                                                               as "handPointName",
		tlirl0304.LOADING_CHANNEL_ID                                                            as "loadingChannelId",
		tlirl0304.LOADING_CHANNEL_NAME                                                          as "loadingChannelName",
		tlirl0304.STATUS                                                                        as "status",
		tlirl0304.HAND_ORDER                                                                    as "handOrder",
		tlirl0304.CAR_TRACE_NO                                                                  as "carTraceNo",
		tlirl0304.ORDER_NUMBER                                                                  as "orderNumber",
		tlirl0304.VEHICLE_NUMER                                                                 as "vehicleNumer",
		tlirl0304.LOAD_FLAG                                                                     as "loadFlag",
		tlirl0304.UNLOAD_FLAG                                                                   as "unloadFlag",
		tlirl0304.FACTORY_AREA                                                                  as "factoryArea",
		tlirl0304.FACTORY_AREA_NAME                                                             as "factoryAreaName",
		tlirl0304.CLOSE_REASON                                                                  as "closeReason",
		tlirl0304.EXPECTED_RECOVERY_TIME                                                        as "expectedRecoveryTime",
		tlirl0304.STANDARD_CAPACITY                                                             as "standardCapacity",
		tlirl0304.SEQ_NUM                                                                       as "seqNum",
		tlirl0304.REC_CREATOR                                                                   as "recCreator",
		tlirl0304.REC_CREATOR_NAME                                                              as "recCreatorName",
		tlirl0304.REC_CREATE_TIME                                                               as "recCreateTime",
		tlirl0304.REC_REVISOR                                                                   as "recRevisor",
		tlirl0304.REC_REVISOR_NAME                                                              as "recRevisorName",
		tlirl0304.REC_REVISE_TIME                                                               as "recReviseTime",
		tlirl0304.ARCHIVE_FLAG                                                                  as "archiveFlag",
		tlirl0304.DEL_FLAG                                                                      as "delFlag",
		tlirl0304.REMARK                                                                        as "remark",
		tlirl0304.SYS_REMARK                                                                    as "sysRemark",
		tlirl0304.UUID                                                                          as "uuid",
		tlirl0304.TENANT_ID                                                                     as "tenantId",
		tlirl0304.FACTORY_BUILDING                                                              as "factoryBuilding",
		tlirl0304.FACTORY_BUILDING_NAME                                                         as "factoryBuildingName"
		FROM MELI.tlirl0304 tlirl0304
		WHERE 1 = 1
		AND tlirl0304.SEG_NO = #segNo#
		AND tlirl0304.STATUS = '30'
		and not exists(select 1
		from meli.tlirl0410 tlirl0410
		where 1 = 1
		and tlirl0410.SEG_NO = tlirl0304.SEG_NO
		and tlirl0410.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
		and tlirl0410.VEHICLE_NO = #vehicleNo#
		and tlirl0410.CAR_TRACE_NO=#carTraceNo#
		and tlirl0304.STATUS = '30')
		ORDER BY tlirl0304.HAND_POINT_NAME asc
	</select>


	<select id="queryAllLadingInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT a.status as "status",a.targetHandPointId,COUNT(1) as "count" FROM (select
		TARGET_HAND_POINT_ID AS "targetHandPointId", '20' as "status",VEHICLE_NO as "vehicleNo"
		from meli.tlirl0402
		where 1 = 1
		and SEG_NO = 'KF000000'
		AND DEL_FLAG = '0'
		union
		select CURRENT_HAND_POINT_ID AS "targetHandPointId", '30' as "status",VEHICLE_NO as "vehicleNo"
		from meli.tlirl0301
		where 1 = 1
		and SEG_NO = 'KF000000'
		and STATUS = '30'
		AND DEL_FLAG = '0'
		union
		select tlirl0301.TARGET_HAND_POINT_ID AS "targetHandPointId", '20' as "status",VEHICLE_NO as "vehicleNo"
		from meli.tlirl0301
		where 1 = 1
		and SEG_NO = 'KF000000'
		and STATUS = '40'
		and TARGET_HAND_POINT_ID != ' '
		AND DEL_FLAG = '0'
		UNION
		select tlirl0301.TARGET_HAND_POINT_ID AS "targetHandPointId", '20' as "status",VEHICLE_NO as "vehicleNo"
		from meli.tlirl0301
		where 1 = 1
		and SEG_NO = 'KF000000'
		and STATUS = '20'
		and TARGET_HAND_POINT_ID != ' '
		AND DEL_FLAG = '0') a
		GROUP BY A.targetHandPointId,status
		order by A.targetHandPointId asc
	</select>

	<!-- 批量查询装卸点作业状态 -->
	<select id="QUERY_WORKING_POINTS_BATCH" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT 
			lirl0304.HAND_POINT_NAME as "handPointName",
			lirl0304.HAND_POINT_ID as "handPointId",
			COUNT(lirl0301.VEHICLE_NO) as "workingVehicleCount"
		FROM ${meliSchema}.tlirl0304 lirl0304
		LEFT JOIN ${meliSchema}.tlirl0301 lirl0301 
			ON lirl0301.SEG_NO = lirl0304.SEG_NO 
			AND lirl0301.TARGET_HAND_POINT_ID = lirl0304.HAND_POINT_ID
			AND lirl0301.STATUS = '30'
			AND lirl0301.DEL_FLAG = '0'
		WHERE lirl0304.SEG_NO = #segNo#
		AND lirl0304.STATUS = #status#
		AND lirl0304.HAND_POINT_NAME IN 
		<iterate property="handPointNames" open="(" close=")" conjunction=",">
			#handPointNames[]#
		</iterate>
		AND lirl0304.DEL_FLAG = '0'
		GROUP BY lirl0304.HAND_POINT_NAME, lirl0304.HAND_POINT_ID
	</select>

</sqlMap>