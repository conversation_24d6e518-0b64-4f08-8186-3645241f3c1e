@charset "UTF-8";

/* CSS Document */

html {
    height: 100%;
}

body {
    background-color: #e5ebf3;
    width: 98%;
    height: 100%;
    margin: 0;
    margin-top: -1%;
    padding: 0 1%;
}

.top {
    width: 98%;
    height: 120px;
    position: absolute;
    top: 0;
    background: url(../img/linebg.png) repeat-x;
    border-right: 1px solid #e5ebf3;
}

.logo-baosight {
    width: 272px;
    height: 60px;
    background: url(../img/logo-baosight.png) no-repeat;
    margin: 30px 0 0 0px;
    float: left;
}

.content {
    width: 100%;
    height: 100%;
}

.entrance {
    height: 100%;
    background-image: url(../img/ahbg-bj.jpg);
    background-repeat: no-repeat;
    background-size: contain;
}

.sel {
float: right;
width: 40%;
height: 100%;
background-color: #203890;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.cho {
    margin-top: 10em;
    text-align: center;
width: 450px;
}

.cho button {
width: 300px;
height: 100px;
border: 1px solid rgba(255, 255, 255, 0.5);
background-color: rgba(255, 255, 255, 0.2);
outline-style: none;
margin-bottom: 70px;
color: #ffffff;
    text-align: center;
font-size: 30px;
}

.instructions {
    position: absolute;
    bottom: 2%;
    left: 1%;
    right: 40%;
    background-color: rgba(32, 57, 144, 0.50);
    /* padding: 20px; */
    padding-left: 20px;
    border-radius: 10px;
    line-height: 1.8;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

    font-family: PingFangSC-Regular;
    font-size: 20px;
    color: #FFFFFF;
    letter-spacing: 0;
    font-weight: 400;
}

.instructions span {
    font-family: PingFangSC-Medium;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 48px;
    font-weight: 500;
    margin-left: 8px;
}

.instructions ol {
    margin-block-start: 0;
    margin-block-end: 0;
    padding-left: 20px;
}

.instructions li {
    margin-bottom: 10px;
    font-size: 12px;
}
