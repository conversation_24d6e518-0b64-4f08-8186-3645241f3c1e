<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-09-10 9:41:06
   		Version :  1.0
		tableName :${meliSchema}.tlirl0401
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 QUEUE_NUMBER  VARCHAR   NOT NULL, 
		 CAR_TRACE_NO  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 PRIORITY_LEVEL  VARCHAR   NOT NULL, 
		 QUEUE_DATE  VARCHAR   NOT NULL, 
		 TARGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0401">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
				PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
				QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
				TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		ORDER BY CAR_TRACE_NO,QUEUE_NUMBER ASC

	</select>

	<select id="queryAlternateCall" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		ORDER BY QUEUE_NUMBER ASC

	</select>

	<select id="queryInfo" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId", <!-- 租户ID -->
		(SELECT HAND_POINT_NAME
		FROM MELI.tlirl0304 tlirl0304
		WHERE 1 = 1
		AND tlirl0304.SEG_NO = tlirl0401.SEG_NO
		AND tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
		AND tlirl0304.STATUS = '30'
		LIMIT 1)                      AS "handPointName"
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		and CAR_TRACE_NO=#carTraceNo#
		and VEHICLE_NO=#vehicleNo#
		<isNotEmpty prepend="and " property="targetHandPointId">
			TARGET_HAND_POINT_ID=#targetHandPointId#
		</isNotEmpty>
		ORDER BY QUEUE_NUMBER ASC

	</select>

	<select id="queryHandPointId" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		select * from (SELECT SEG_NO               as "segNo",
							  UNIT_CODE            as "unitCode",
							  QUEUE_NUMBER         as "queueNumber",
							  CAR_TRACE_NO         as "carTraceNo",
							  VEHICLE_NO           as "vehicleNo",
							  VOUCHER_NUM          as "voucherNum",
							  PRIORITY_LEVEL       as "priorityLevel",
							  QUEUE_DATE           as "queueDate",
							  TARGET_HAND_POINT_ID as "targetHandPointId",
							  FACTORY_AREA         as "factoryArea",
							  REC_CREATOR          as "recCreator",
							  REC_CREATOR_NAME     as "recCreatorName",
							  REC_CREATE_TIME      as "recCreateTime",
							  REC_REVISOR          as "recRevisor",
							  REC_REVISOR_NAME     as "recRevisorName",
							  REC_REVISE_TIME      as "recReviseTime",
							  ARCHIVE_FLAG         as "archiveFlag",
							  DEL_FLAG             as "delFlag",
							  REMARK               as "remark",
							  SYS_REMARK           as "sysRemark",
							  UUID                 as "uuid",
							  TENANT_ID            as "tenantId"
					   FROM meli.tlirl0401
					   WHERE 1 = 1
						 and SEG_NO = #segNo#
						 and CAR_TRACE_NO = #carTraceNo#
						 and VEHICLE_NO = #vehicleNo#
					   union
					   SELECT SEG_NO               as "segNo",
							  UNIT_CODE            as "unitCode",
							  QUEUE_NUMBER         as "queueNumber",
							  CAR_TRACE_NO         as "carTraceNo",
							  VEHICLE_NO           as "vehicleNo",
							  VOUCHER_NUM          as "voucherNum",
							  PRIORITY_LEVEL       as "priorityLevel",
							  QUEUE_DATE           as "queueDate",
							  TARGET_HAND_POINT_ID as "targetHandPointId",
							  FACTORY_AREA         as "factoryArea",
							  REC_CREATOR          as "recCreator",
							  REC_CREATOR_NAME     as "recCreatorName",
							  REC_CREATE_TIME      as "recCreateTime",
							  REC_REVISOR          as "recRevisor",
							  REC_REVISOR_NAME     as "recRevisorName",
							  REC_REVISE_TIME      as "recReviseTime",
							  ARCHIVE_FLAG         as "archiveFlag",
							  DEL_FLAG             as "delFlag",
							  REMARK               as "remark",
							  SYS_REMARK           as "sysRemark",
							  UUID                 as "uuid",
							  TENANT_ID            as "tenantId"
					   FROM meli.tlirl0303
					   WHERE 1 = 1
						 and SEG_NO = #segNo#
						 and CAR_TRACE_NO = #carTraceNo#
						 and VEHICLE_NO = #vehicleNo#    ) A
		order by a.queueNumber asc
	</select>



	<select id="queryQueueNumber"
			resultClass="String">
		SELECT
		QUEUE_NUMBER	as "queueNumber"  <!-- 顺序号 -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		ORDER BY QUEUE_NUMBER desc

	</select>


<!--	查询排队表中的数据支持强制叫号-->
	<select id="queryQueueData" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select * from (SELECT tlirl0401.SEG_NO               as "segNo",
							  tlirl0401.UNIT_CODE            as "unitCode",
							  tlirl0401.QUEUE_NUMBER         as "queueNumber",
							  tlirl0401.CAR_TRACE_NO         as "carTraceNo",
							  tlirl0401.VEHICLE_NO           as "vehicleNo",
							  tlirl0401.VOUCHER_NUM          as "voucherNum",
							  tlirl0401.PRIORITY_LEVEL       as "priorityLevel",
							  tlirl0401.QUEUE_DATE           as "queueDate",
							  tlirl0401.TARGET_HAND_POINT_ID as "targetHandPointId",
							  (SELECT HAND_POINT_NAME
							   FROM MELI.tlirl0304 tlirl0304
							   WHERE 1 = 1
								 AND tlirl0304.SEG_NO = tlirl0401.SEG_NO
								 AND tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
								 AND tlirl0304.STATUS = '30'
																LIMIT 1)                      AS "targetHandPointName",
					  tlirl0401.FACTORY_AREA         as "factoryArea",
					  tlirl0401.REC_CREATOR          as "recCreator",
					  tlirl0401.REC_CREATOR_NAME     as "recCreatorName",
					  tlirl0401.REC_CREATE_TIME      as "recCreateTime",
					  tlirl0401.REC_REVISOR          as "recRevisor",
					  tlirl0401.REC_REVISOR_NAME     as "recRevisorName",
					  tlirl0401.REC_REVISE_TIME      as "recReviseTime",
					  tlirl0401.ARCHIVE_FLAG         as "archiveFlag",
					  tlirl0401.DEL_FLAG             as "delFlag",
					  tlirl0401.REMARK               as "remark",
					  tlirl0401.SYS_REMARK           as "sysRemark",
					  tlirl0401.UUID                 as "uuid",
					  tlirl0401.TENANT_ID            as "tenantId",
					  tlirl0301.CHECK_DATE           AS "checkDate"
			FROM MELI.tlirl0401 tlirl0401,
     MELI.tlirl0301 tlirl0301
		WHERE 1 = 1
		  and tlirl0401.SEG_NO = #segNo#
		  AND tlirl0401.SEG_NO = tlirl0301.SEG_NO
		  AND tlirl0401.VEHICLE_NO = tlirl0301.VEHICLE_NO
		  AND tlirl0401.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		  and tlirl0301.STATUS != '00'
		union
		SELECT tlirl0303.SEG_NO               as "segNo",
			   tlirl0303.UNIT_CODE            as "unitCode",
			   tlirl0303.QUEUE_NUMBER         as "queueNumber",
			   tlirl0303.CAR_TRACE_NO         as "carTraceNo",
			   tlirl0303.VEHICLE_NO           as "vehicleNo",
			   tlirl0303.VOUCHER_NUM          as "voucherNum",
			   tlirl0303.PRIORITY_LEVEL       as "priorityLevel",
			   tlirl0303.QUEUE_DATE           as "queueDate",
			   tlirl0303.TARGET_HAND_POINT_ID as "targetHandPointId",
			   (SELECT HAND_POINT_NAME
				FROM MELI.tlirl0304 tlirl0304
				WHERE 1 = 1
				  AND tlirl0304.SEG_NO = tlirl0303.SEG_NO
				  AND tlirl0304.HAND_POINT_ID = tlirl0303.TARGET_HAND_POINT_ID
				  AND tlirl0304.STATUS = '30'
												 LIMIT 1)                      AS "targetHandPointName",
       tlirl0303.FACTORY_AREA         as "factoryArea",
       tlirl0303.REC_CREATOR          as "recCreator",
       tlirl0303.REC_CREATOR_NAME     as "recCreatorName",
       tlirl0303.REC_CREATE_TIME      as "recCreateTime",
       tlirl0303.REC_REVISOR          as "recRevisor",
       tlirl0303.REC_REVISOR_NAME     as "recRevisorName",
       tlirl0303.REC_REVISE_TIME      as "recReviseTime",
       tlirl0303.ARCHIVE_FLAG         as "archiveFlag",
       tlirl0303.DEL_FLAG             as "delFlag",
       tlirl0303.REMARK               as "remark",
       tlirl0303.SYS_REMARK           as "sysRemark",
       tlirl0303.UUID                 as "uuid",
       tlirl0303.TENANT_ID            as "tenantId",
       tlirl0301.CHECK_DATE           AS "checkDate"
		FROM MELI.tlirl0303 tlirl0303,
			MELI.tlirl0301 tlirl0301
		WHERE 1 = 1
		  and tlirl0303.SEG_NO = #segNo#
		  AND tlirl0303.SEG_NO = tlirl0301.SEG_NO
		  AND tlirl0303.VEHICLE_NO = tlirl0301.VEHICLE_NO
		  AND tlirl0303.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		  and tlirl0301.STATUS != '00' ) A
		ORDER BY  A.checkDate asc
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0401 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
	</select>

	<select id="queryMaxNumber" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT IFNULL(max(QUEUE_NUMBER+0),0) as "maxQueueNumber"
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
	</select>

	<select id="queryHandPointName"
			parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			TARGET_HAND_POINT_ID as "handPointId",
			(select * from meli.tlirl0304 tlirl0304 where tlirl0304.SEG_NO=tlirl0401.SEG_NO
													  and tlirl0304.HAND_POINT_ID=tlirl0401.TARGET_HAND_POINT_ID
													  and tlirl0304.STATUS='30') as "handPointName"
		FROM meli.tlirl0401 tlirl0401 WHERE 1=1
		AND SEG_NO = #segNo#
		AND CAR_TRACE_NO=#carTraceNo#
        AND VEHICLE_NO=#vehicleNo#
		ORDER BY QUEUE_NUMBER desc
	</select>

	<select id="queryLeavingTheFactoryWithoutLoading"
			parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select VEHICLE_NO   as "vehicleNo",
			   CAR_TRACE_NO as "carTraceNo",
			   (select HAND_POINT_NAME
				from meli.tlirl0304 tlirl0304
				where 1 = 1
				  and tlirl0304.SEG_NO = tlirl0401.SEG_NO
				  and tlirl0304.HAND_POINT_ID = tlirl0401.TARGET_HAND_POINT_ID
				  and tlirl0304.STATUS = '30'
							   limit 1)    as "handPointName",
       '排队'       as "flag",
       TARGET_HAND_POINT_ID as "handPointId"
		from meli.tlirl0401 tlirl0401
		where SEG_NO = #segNo#
		  and DEL_FLAG = '0'
		union
		select VEHICLE_NO   as "vehicleNo",
			   CAR_TRACE_NO as "carTraceNo",
			   (select HAND_POINT_NAME
				from meli.tlirl0304 tlirl0304
				where 1 = 1
				  and tlirl0304.SEG_NO = tlirl0301.SEG_NO
				  and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
				  and tlirl0304.STATUS = '30'
							   limit 1)    as "handPointName",
       '等待作业'   as "flag",
		TARGET_HAND_POINT_ID as "handPointId"
		from meli.tlirl0301 tlirl0301
		where SEG_NO = #segNo#
		  and DEL_FLAG = '0'
		  and STATUS in ('20', '40')
		  and TARGET_HAND_POINT_ID != ' '
	</select>

	<select id="queryLeavingTheFactoryWithoutLoadingCq"
			parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select distinct *
		from (select VEHICLE_NO   as "vehicleNo",
					 CAR_TRACE_NO as "carTraceNo",
					 '排队'       as "flag"
			  from meli.tlirl0401 tlirl0401
			  where SEG_NO = #segNo#
				and DEL_FLAG = '0'
			  union
			  select VEHICLE_NO   as "vehicleNo",
					 CAR_TRACE_NO as "carTraceNo",
					 '等待作业'   as "flag"
			  from meli.tlirl0301 tlirl0301
			  where SEG_NO = #segNo#
				and DEL_FLAG = '0'
				and STATUS in ('20', '40')
				and TARGET_HAND_POINT_ID != ' '
			  union
			  select VEHICLE_NO as "vehicleNo", CAR_TRACE_NO as "carTraceNo", '排队' as "flag"
			  from meli.tlirl0301 tlirl0301
			  where SEG_NO = 'JC000000'
				and DEL_FLAG = '0'
				and STATUS = '20'
				and TARGET_HAND_POINT_ID = ' '
					   )A
		order by a.carTraceNo asc
	</select>


	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueNumber">
			QUEUE_NUMBER = #queueNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="priorityLevel">
			PRIORITY_LEVEL = #priorityLevel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueDate">
			QUEUE_DATE = #queueDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0401 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										QUEUE_NUMBER,  <!-- 顺序号 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										VOUCHER_NUM,  <!-- 提单号 -->
										PRIORITY_LEVEL,  <!-- 优先级（默认99） -->
										QUEUE_DATE,  <!-- 排序时间（当前时间） -->
										TARGET_HAND_POINT_ID,  <!-- 目标装卸点 -->
										FACTORY_AREA,  <!-- 厂区 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID  <!-- uuid -->
										)		 
	    VALUES (#segNo#, #unitCode#, #queueNumber#, #carTraceNo#, #vehicleNo#, #voucherNum#, #priorityLevel#, #queueDate#, #targetHandPointId#, #factoryArea#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #delFlag#, #remark#, #sysRemark#, #uuid#)
	</insert>

	<insert id="insertBack">
		INSERT INTO ${meliSchema}.tlirl0410 (SEG_NO,  <!-- 系统账套 -->
		UNIT_CODE,  <!-- 业务单元代码 -->
		QUEUE_NUMBER,  <!-- 顺序号 -->
		CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
		VEHICLE_NO,  <!-- 车牌号 -->
		VOUCHER_NUM,  <!-- 提单号 -->
		PRIORITY_LEVEL,  <!-- 优先级（默认99） -->
		QUEUE_DATE,  <!-- 排序时间（当前时间） -->
		BACK_DATE,  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID,  <!-- 目标装卸点 -->
		FACTORY_AREA,  <!-- 厂区 -->
		REC_CREATOR,  <!-- 记录创建人 -->
		REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME,  <!-- 记录创建时间 -->
		REC_REVISOR,  <!-- 记录修改人 -->
		REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME,  <!-- 记录修改时间 -->
		DEL_FLAG,  <!-- 记录删除标记 -->
		REMARK,  <!-- 备注 -->
		SYS_REMARK,  <!-- 系统备注 -->
		UUID  <!-- uuid -->
		)
		VALUES (#segNo#, #unitCode#, #queueNumber#, #carTraceNo#, #vehicleNo#, #voucherNum#,
		        #priorityLevel#, #queueDate#,#backDate#, #targetHandPointId#, #factoryArea#, #recCreator#,
		        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#,
		        #delFlag#, #remark#, #sysRemark#, #uuid#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0401
		       WHERE  1=1
		         and SEG_NO=#segNo#
		         and CAR_TRACE_NO	= #carTraceNo#
				<isNotEmpty prepend=" AND " property="vehicleNo">
					VEHICLE_NO = #vehicleNo#
				</isNotEmpty>
				<isNotEmpty prepend=" AND " property="voucherNum">
					VOUCHER_NUM = #voucherNum#
				</isNotEmpty>
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0401
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					QUEUE_NUMBER	= #queueNumber#,   <!-- 顺序号 -->  
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					VOUCHER_NUM	= #voucherNum#,   <!-- 提单号 -->  
					PRIORITY_LEVEL	= #priorityLevel#,   <!-- 优先级（默认99） -->  
					QUEUE_DATE	= #queueDate#,   <!-- 排序时间（当前时间） -->  
					TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
	</update>

	<select id="queryHandPointCount" resultClass="int">
		select count(1)
		from ${meliSchema}.tlirl0401 tlirl0401,
		${meliSchema}.tlirl0305 tlirl0305
		where 1=1
		and tlirl0401.SEG_NO=tlirl0305.SEG_NO
		and tlirl0401.CAR_TRACE_NO=tlirl0305.CAR_TRACE_NO
		and tlirl0401.VEHICLE_NO=tlirl0305.VEHICLE_ID
		and tlirl0305.SEG_NO=#segNo#
		and tlirl0305.HAND_POINT_ID=#hand_point_id#
	</select>

	<select id="queryMaxQueueNumber" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		ifnull(MAX(QUEUE_NUMBER),0)+1	as "maxQueueNumber" <!-- 最大顺序号 -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
	</select>

	<select id="queryMinQueueNumber" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		ifnull(Min(QUEUE_NUMBER),0) 	as "minQueueNumber" <!-- 最小顺序号 -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
	</select>


	<select id="queryMinQueueNumberPriorityLevel" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		ifnull(Min(QUEUE_NUMBER),0) as "minQueueNumber" <!-- 最小顺序号 -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		and PRIORITY_LEVEL='1'
	</select>

	<select id="queryExistVoucherNum" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		and CAR_TRACE_NO=#carTraceNo#
		and VEHICLE_NO=#vehicleNo#
		<isNotEmpty prepend=" AND " property="voucherNum">
				VOUCHER_NUM=#voucherNum#
		</isNotEmpty>

	</select>

	<select id="queryQueueByhandPointId" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT *
		FROM (SELECT tlirl0301.VEHICLE_NO                                                     as "vehicleNo",
					 '30'                                                                     as "status",
					 tlirl0301.CAR_TRACE_NO                                                   AS "carTraceNo",
					 tlirl0301.CURRENT_HAND_POINT_ID                                          AS "handPointId",
					 (select HAND_POINT_NAME
					  from meli.tlirl0304 tlirl0304
					  where 1 = 1
						and tlirl0304.HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID
						and status = '30'
																								 limit 1)                                                                as "handPointName",
			 (select TYPE_OF_HANDLING
			  from meli.tlirl0201 tlirl0201
			  where 1 = 1
				and tlirl0201.SEG_NO = tlirl0301.SEG_NO
				and tlirl0201.VEHICLE_NO = tlirl0301.VEHICLE_NO
				and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
				and tlirl0201.STATUS = '20'
				  limit 1)                                                                as "typeOfHandling",
			 (select min(QUEUE_NUMBER)
			  from meli.tlirl0410 tlirl0410
			  where tlirl0410.SEG_NO = tlirl0301.SEG_NO
				and tlirl0410.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
				and tlirl0410.VEHICLE_NO = tlirl0301.VEHICLE_NO
				and tlirl0410.TARGET_HAND_POINT_ID = tlirl0301.CURRENT_HAND_POINT_ID) as "queueNumber",
			 (case
				 when tlirl0301.STATUS = '20' then TIMESTAMPDIFF(MINUTE,
                                                                  STR_TO_DATE(ENTER_FACTORY, '%Y%m%d%H%i%s'),
                                                                  now())
                  when tlirl0301.STATUS = '30' then TIMESTAMPDIFF(MINUTE,
                                                                  (select STR_TO_DATE(max(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s')
                                                                   from MELI.tlirl0406 tlirl0406
                                                                   where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                                                     and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                     and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                                  now())
                  else case
                           when (select tlirl0406.REMARK
                                 from MELI.tlirl0407 tlirl0406
                                 where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                   and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                   and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                                 order by tlirl0406.REC_REVISE_TIME desc
                                 limit 1) != ' ' then TIMESTAMPDIFF(MINUTE,
                                                                    (select STR_TO_DATE(MAX(tlirl0406.REC_REVISE_TIME), '%Y%m%d%H%i%s')
                                                                     from MELI.tlirl0407 tlirl0406
                                                                     where tlirl0406.SEG_NO = tlirl0301.SEG_NO
                                                                       and tlirl0406.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                                       and tlirl0406.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                                                    now())
                           else TIMESTAMPDIFF(MINUTE,
                                              (select STR_TO_DATE(MAX(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
                                               from MELI.tlirl0407 tlirl0407
                                               where tlirl0407.SEG_NO = tlirl0301.SEG_NO
                                                 and tlirl0407.VEHICLE_NO = tlirl0301.VEHICLE_NO
                                                 and tlirl0407.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO),
                                              now()) end end)                         as "waitingTime",
			 (select EMERGENCY_DELIVERY_TIME
			  from meli.tlirl0502 tlirl0502
			  where 1 = 1
				and tlirl0502.SEG_NO = tlirl0301.SEG_NO
				and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
				and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
				and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0301.ALLOCATE_VEHICLE_NO
				  limit 1)                                                                as "emergencyDeliveryTime"
			FROM MELI.tlirl0301 tlirl0301
		WHERE 1 = 1
		  AND tlirl0301.SEG_NO = #segNo#
		<isNotEmpty prepend="and " property="handPointId" >
			tlirl0301.CURRENT_HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		  AND tlirl0301.STATUS = '30'
		union
		select DISTINCT tlirl0402.VEHICLE_NO           as "vehicleNo",
						'20'                           as "status",
						tlirl0402.CAR_TRACE_NO         as "carTraceNo",
						tlirl0402.TARGET_HAND_POINT_ID as "handPointId",
						(select HAND_POINT_NAME
						 from meli.tlirl0304 tlirl0304
						 where 1 = 1
						   and tlirl0304.SEG_NO = tlirl0402.SEG_NO
						   and tlirl0304.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID
						   and tlirl0304.STATUS = '30'
														  limit 1)                      as "handPointName",
                      (select TYPE_OF_HANDLING
                       from meli.tlirl0201 tlirl0201
                       where 1 = 1
                         and tlirl0201.SEG_NO = tlirl0402.SEG_NO
                         and tlirl0201.VEHICLE_NO = tlirl0402.VEHICLE_NO
                         and tlirl0201.RESERVATION_NUMBER = (select tlirl0301.RESERVATION_NUMBER
                                                             from meli.tlirl0301 tlirl0301
                                                             where 1 = 1
                                                               and tlirl0301.SEG_NO = tlirl0402.SEG_NO
                                                               and tlirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
                                                               and tlirl0301.VEHICLE_NO = tlirl0402.VEHICLE_NO
                                                             limit 1))
                                                     as "typeOfHandling",
                      tlirl0402.QUEUE_NUMBER         as "queueNumber",
                      TIMESTAMPDIFF(MINUTE,
                                    (tlirl0402.REC_CREATE_TIME),
                                    now())           as "waitingTime",
                      (select EMERGENCY_DELIVERY_TIME
                       from meli.tlirl0502 tlirl0502
                       where 1 = 1
                         and tlirl0502.SEG_NO = tlirl0301.SEG_NO
                         and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
                         and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
                         and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0301.ALLOCATE_VEHICLE_NO
                       limit 1)                      as "emergencyDeliveryTime"

		from meli.tlirl0402 tlirl0402,
			meli.tlirl0301 tlirl0301
		where 1 = 1
		  and tlirl0402.SEG_NO =#segNo#
		  and tlirl0402.SEG_NO = tlirl0301.SEG_NO
		  and tlirl0402.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		  and tlirl0402.VEHICLE_NO = tlirl0301.VEHICLE_NO
		<isNotEmpty prepend="and " property="handPointId" >
			tlirl0402.TARGET_HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		and exists(select 1
		from meli.tlirl0402 lirl0402
		where 1 = 1
		and lirl0402.SEG_NO = tlirl0301.SEG_NO
		AND lirl0402.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		AND lirl0402.VEHICLE_NO = tlirl0301.VEHICLE_NO
		LIMIT 1)
		union
		select DISTINCT tlirl0301.VEHICLE_NO                                                    as "vehicleNo",
		'40'                                                                    as "status",
		tlirl0301.CAR_TRACE_NO                                                  as "carTraceNo",
		tlirl0301.TARGET_HAND_POINT_ID                                                    as "handPointId",
		(select HAND_POINT_NAME
		from meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0301.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		limit 1)                                                               as "handPointName",
		(select TYPE_OF_HANDLING
		from meli.tlirl0201 tlirl0201
		where 1 = 1
		and tlirl0201.SEG_NO = tlirl0301.SEG_NO
		and tlirl0201.VEHICLE_NO = tlirl0301.VEHICLE_NO
		and tlirl0201.RESERVATION_NUMBER = tlirl0301.RESERVATION_NUMBER
		and tlirl0201.STATUS = '20'
		limit 1)                                                               as "typeOfHandling",
		(select QUEUE_NUMBER
		from meli.tlirl0402 tlirl0410
		where tlirl0410.SEG_NO = tlirl0301.SEG_NO
		and tlirl0410.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		and tlirl0410.VEHICLE_NO = tlirl0301.VEHICLE_NO
		and tlirl0410.TARGET_HAND_POINT_ID = tlirl0301.TARGET_HAND_POINT_ID) as "queueNumber",
		TIMESTAMPDIFF(MINUTE, (select tlirl0502.REC_CREATE_TIME
		from meli.tlirl0502 tlirl0502
		where tlirl0502.SEG_NO = tlirl0301.SEG_NO
		and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
		and tlirl0502.ALLOC_TYPE='20'
		limit 1
		),
		now())                                                    as "waitingTime",
		(select EMERGENCY_DELIVERY_TIME
		from meli.tlirl0502 tlirl0502
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0301.SEG_NO
		and tlirl0502.CAR_TRACE_NO = tlirl0301.CAR_TRACE_NO
		and tlirl0502.VEHICLE_NO = tlirl0301.VEHICLE_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0301.ALLOCATE_VEHICLE_NO
		limit 1)                                                               as "emergencyDeliveryTime"
		from meli.tlirl0301 tlirl0301,
		meli.tlirl0502,
		meli.tlirl0503
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0503.SEG_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = tlirl0503.ALLOCATE_VEHICLE_NO
		and tlirl0301.SEG_NO = tlirl0502.SEG_NO
		and tlirl0301.CAR_TRACE_NO = tlirl0502.CAR_TRACE_NO
		and tlirl0502.SEG_NO = #segNo#
		AND MAT_INNER_ID = ' '
		AND OUT_PACK_FLAG != '1'
		and tlirl0502.STATUS = tlirl0503.STATUS
		and tlirl0502.STATUS = '20'
		and tlirl0502.ALLOC_TYPE = '20'
		and tlirl0503.TARGET_HAND_POINT_ID = ' '
		and tlirl0503.STATUS = '20'
		and tlirl0503.STATUS != '00'
		and tlirl0502.NO_PLAN_FLAG is null
		union
		select DISTINCT tlirl0401.VEHICLE_NO                                       as "vehicleNo",
						'10'                                                       as "status",
						tlirl0401.CAR_TRACE_NO                                     as "carTraceNo",
						GROUP_CONCAT(tlirl0401.TARGET_HAND_POINT_ID SEPARATOR ',') as "handPointId",
						GROUP_CONCAT(tlirl0304.HAND_POINT_NAME SEPARATOR ',')      as "handPointId",
						(select TYPE_OF_HANDLING
						 from meli.tlirl0201 tlirl0201
						 where 1 = 1
						   and tlirl0201.SEG_NO = tlirl0401.SEG_NO
						   and tlirl0201.VEHICLE_NO = tlirl0401.VEHICLE_NO
						   and tlirl0201.RESERVATION_NUMBER = (select tlirl0301.RESERVATION_NUMBER
															   from meli.tlirl0301 tlirl0301
															   where 1 = 1
																 and tlirl0301.SEG_NO = tlirl0401.SEG_NO
																 and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
																 and tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
																					  limit 1))
                                                                                 as "typeOfHandling",
                      (select min(tlirl0401.QUEUE_NUMBER)
		from meli.tlirl0401 tlirl04011
		where 1 = 1
		  and tlirl04011.SEG_NO
			= tlirl0401.SEG_NO
		  and tlirl04011.CAR_TRACE_NO
			= tlirl0401.CAR_TRACE_NO
		  and tlirl04011.VEHICLE_NO
			= tlirl0401.VEHICLE_NO
			limit 1)                                                  as "queueNumber",
			TIMESTAMPDIFF(MINUTE,
			(select min(tlirl0401.QUEUE_DATE)
			from meli.tlirl0401 tlirl04011
			where 1 = 1
		  and tlirl04011.SEG_NO
			= tlirl0401.SEG_NO
		  and tlirl04011.CAR_TRACE_NO
			= tlirl0401.CAR_TRACE_NO
		  and tlirl04011.VEHICLE_NO
			= tlirl0401.VEHICLE_NO
			limit 1),
			now())                                       as "waitingTime",
			(select EMERGENCY_DELIVERY_TIME
		from meli.tlirl0502 tlirl0502
		where 1 = 1
		  and tlirl0502.SEG_NO = tlirl0401.SEG_NO
		  and tlirl0502.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		  and tlirl0502.VEHICLE_NO = tlirl0401.VEHICLE_NO
		  and tlirl0502.ALLOCATE_VEHICLE_NO = (select max(tlirl0401.VOUCHER_NUM)
			from meli.tlirl0401 tlirl04011
			where 1 = 1
		  and tlirl04011.SEG_NO
			= tlirl0401.SEG_NO
		  and tlirl04011.CAR_TRACE_NO
			= tlirl0401.CAR_TRACE_NO
		  and tlirl04011.VEHICLE_NO
			= tlirl0401.VEHICLE_NO
			limit 1)
		  and tlirl0502.ALLOC_TYPE = '10'
			limit 1)                                                  as "emergencyDeliveryTime"

		from meli.tlirl0401 tlirl0401,
			meli.tlirl0304 tlirl0304
		where 1 = 1
		  and tlirl0401.SEG_NO = tlirl0304.SEG_NO
		  and tlirl0401.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
		<isNotEmpty prepend="and " property="handPointId" >
			tlirl0401.TARGET_HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		  and tlirl0401.SEG_NO =#segNo#
		  and tlirl0304.STATUS = '30'
		group by tlirl0401.CAR_TRACE_NO, VEHICLE_NO) A
		ORDER BY status,queueNumber asc
	</select>


	<select id="queryQueueByhandPointIdAll" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select DISTINCT tlirl0401.VEHICLE_NO                                       as "vehicleNo",
		'10'                                                       as "status",
		tlirl0401.CAR_TRACE_NO                                     as "carTraceNo",
		GROUP_CONCAT(tlirl0401.TARGET_HAND_POINT_ID SEPARATOR ',') as "handPointId",
		GROUP_CONCAT(tlirl0304.HAND_POINT_NAME SEPARATOR ',')      as "handPointId",
		(select TYPE_OF_HANDLING
		from meli.tlirl0201 tlirl0201
		where 1 = 1
		and tlirl0201.SEG_NO = tlirl0401.SEG_NO
		and tlirl0201.VEHICLE_NO = tlirl0401.VEHICLE_NO
		and tlirl0201.RESERVATION_NUMBER = (select tlirl0301.RESERVATION_NUMBER
		from meli.tlirl0301 tlirl0301
		where 1 = 1
		and tlirl0301.SEG_NO = tlirl0401.SEG_NO
		and tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		and tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
		limit 1))           as "typeOfHandling",
		(select min(tlirl0401.QUEUE_NUMBER)
		from meli.tlirl0401 tlirl04011
		where 1 = 1
		and tlirl04011.SEG_NO = tlirl0401.SEG_NO
		and tlirl04011.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		and tlirl04011.VEHICLE_NO = tlirl0401.VEHICLE_NO
		limit 1)                                                  as "queueNumber",
		TIMESTAMPDIFF(MINUTE, (select min(tlirl0401.QUEUE_DATE)
		from meli.tlirl0401 tlirl04011
		where 1 = 1
		and tlirl04011.SEG_NO = tlirl0401.SEG_NO
		and tlirl04011.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		and tlirl04011.VEHICLE_NO = tlirl0401.VEHICLE_NO
		limit 1), now())                    as "waitingTime",
		(select EMERGENCY_DELIVERY_TIME
		from meli.tlirl0502 tlirl0502
		where 1 = 1
		and tlirl0502.SEG_NO = tlirl0401.SEG_NO
		and tlirl0502.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		and tlirl0502.VEHICLE_NO = tlirl0401.VEHICLE_NO
		and tlirl0502.ALLOCATE_VEHICLE_NO = (select max(tlirl0401.VOUCHER_NUM)
		from meli.tlirl0401 tlirl04011
		where 1 = 1
		and tlirl04011.SEG_NO = tlirl0401.SEG_NO
		and tlirl04011.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		and tlirl04011.VEHICLE_NO = tlirl0401.VEHICLE_NO
		limit 1)
		and tlirl0502.ALLOC_TYPE = '10'
		limit 1)                                                  as "emergencyDeliveryTime"
		from meli.tlirl0401 tlirl0401,
		meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0401.SEG_NO = tlirl0304.SEG_NO
		and tlirl0401.TARGET_HAND_POINT_ID = tlirl0304.HAND_POINT_ID
		<isNotEmpty prepend="and " property="handPointId" >
			tlirl0401.TARGET_HAND_POINT_ID = #handPointId#
		</isNotEmpty>
		and tlirl0401.SEG_NO =#segNo#
		and tlirl0304.STATUS = '30'
		group by tlirl0401.CAR_TRACE_NO, VEHICLE_NO
		order by queueNumber asc
	</select>

	<update id="updateQueueByhandPointId">
		UPDATE ${meliSchema}.tlirl0401
		SET
		QUEUE_NUMBER	= #queueNumber#,   <!-- 排队序列 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE SEG_NO = #segNo#
		AND CAR_TRACE_NO=#carTraceNo#
		AND VEHICLE_NO=#vehicleNo#
		AND TARGET_HAND_POINT_ID=#handPointId#
	</update>

	<select id="querySortVehicles" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		and VEHICLE_NO= #vehicleNo#
		and VOUCHER_NUM = #allocateVehicleNo#
		and CAR_TRACE_NO = #carTraceNo#
		and STR_TO_DATE(REC_CREATOR_NAME, '%Y%m%d%H%i%s') > STR_TO_DATE(#twoHoursAgoTime#, '%Y%m%d%H%i%s')
		ORDER BY CAR_TRACE_NO,QUEUE_NUMBER ASC

	</select>

	<select id="queryByVeNo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId", <!-- 租户ID -->
		(select tlirl0301.DRIVER_NAME from meli.tlirl0301 tlirl0301 where 1=1 and tlirl0301.SEG_NO=tlirl0401.SEG_NO
		and tlirl0301.CAR_TRACE_NO=tlirl0401.CAR_TRACE_NO
		and tlirl0301.VEHICLE_NO=tlirl0401.VEHICLE_NO
		limit 1) as "driverName",
		(select tlirl0301.TEL_NUM from meli.tlirl0301 tlirl0301 where 1=1 and tlirl0301.SEG_NO=tlirl0401.SEG_NO
		and tlirl0301.CAR_TRACE_NO=tlirl0401.CAR_TRACE_NO
		and tlirl0301.VEHICLE_NO=tlirl0401.VEHICLE_NO
		limit 1) as "driverTel",
		(select tlirl0304.HAND_POINT_NAME from meli.tlirl0304 tlirl0304 where 1=1 and tlirl0304.SEG_NO=tlirl0401.SEG_NO
		and tlirl0304.HAND_POINT_ID=tlirl0401.TARGET_HAND_POINT_ID and tlirl0304.DEL_FLAG=0
		and tlirl0304.STATUS='30' LIMIT 1 ) AS "handPointName"
		FROM ${meliSchema}.tlirl0410 tlirl0401 WHERE 1=1
		and tlirl0401.SEG_NO	= #segNo#
		and tlirl0401.CAR_TRACE_NO=#carTraceNo#
		and tlirl0401.VEHICLE_NO=#vehicleNo#
		and tlirl0401.VOUCHER_NUM=#voucherNum#
		ORDER BY QUEUE_NUMBER ASC
	</select>

	<update id="updateHandPointIdCQ">
		UPDATE ${meliSchema}.tlirl0401
		SET
		TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO	= #segNo# and CAR_TRACE_NO	= #carTraceNo#
		<isNotEmpty prepend=" and " property="voucherNum">
			VOUCHER_NUM= #allocateVehicleNo#
		</isNotEmpty>
		and
		TARGET_HAND_POINT_ID = #oldHandPointId#
	</update>
	<!--撤销排队-->
	<delete id="cancelQueue">
		delete from ${meliSchema}.tlirl0401<!-- 记录修改时间 -->
		WHERE
		SEG_NO	= #segNo#
		and VEHICLE_NO	= #vehicleNo#
		and VOUCHER_NUM	= #allocateVehicleNo#
	</delete>

	<!--取消配单-->
	<update id="deleteByAllocateVehicleNo">
		UPDATE ${meliSchema}.tlirl0401
		SET
		DEL_FLAG	= 1,   <!-- 记录删除标记 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#  <!-- 记录修改时间 -->
		WHERE
		1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="allocateVehicleNo">
			VOUCHER_NUM = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
	</update>

	<select id="queryHandPointInfo" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		<isNotEmpty prepend="and" property="carTraceNo">
			CAR_TRACE_NO= #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="vehicleNo">
			VEHICLE_NO= #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="allocateVehicleNo">
			VOUCHER_NUM = #allocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="noAllocateVehicleNo">
			VOUCHER_NUM != #noAllocateVehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="handPointId">
			TARGET_HAND_POINT_ID in
			<iterate property="handPointId" open="("
					 close=")" conjunction=" , ">
				#handPointId[]#
			</iterate>
		</isNotEmpty>

	</select>

	<select id="queryForceVehicle" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select DISTINCT tlirl0401.SEG_NO as "segNo",
						tlirl0401.CAR_TRACE_NO as "carTraceNo",
						tlirl0401.VOUCHER_NUM as "allocateVehicleNo",
						tlirl0401.VEHICLE_NO as "vehicleNo"
		from meli.tlirl0401 tlirl0401,
			 meli.tlirl0301
		where 1 = 1
		  and tlirl0301.SEG_NO = tlirl0401.SEG_NO
		  AND tlirl0301.SEG_NO = tlirl0401.SEG_NO
		  AND tlirl0301.CAR_TRACE_NO = tlirl0401.CAR_TRACE_NO
		  AND tlirl0301.VEHICLE_NO = tlirl0401.VEHICLE_NO
		  AND tlirl0301.ALLOCATE_VEHICLE_NO = tlirl0401.VOUCHER_NUM
		  and tlirl0401.VOUCHER_NUM != ''
  and tlirl0401.SEG_NO = #segNo#
		union
		select DISTINCT tlirl0401.SEG_NO as "segNo",
						tlirl0401.CAR_TRACE_NO as "carTraceNo",
						tlirl0401.VOUCHER_NUM as "allocateVehicleNo",
						tlirl0401.VEHICLE_NO as "vehicleNo"
		from meli.tlirl0401 tlirl0401
		where 1 = 1
		  and SEG_NO = #segNo#
		  and tlirl0401.VOUCHER_NUM = ''

	</select>


	<select id="queryAllProHandPointId" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select count(1) as  countSum ,TARGET_HAND_POINT_ID as "handPointId" from
			(select CAR_TRACE_NO, VEHICLE_NO, TARGET_HAND_POINT_ID
			from meli.tlirl0401
			where 1 = 1
			and SEG_NO=#segNo#
			<isNotEmpty prepend=" AND " property="handPointId">
				TARGET_HAND_POINT_ID in
				<iterate property="handPointId" open="("
						 close=")" conjunction=" , ">
					#handPointId[]#
				</iterate>
			</isNotEmpty>
			union
			select CAR_TRACE_NO, VEHICLE_NO, TARGET_HAND_POINT_ID
			from meli.tlirl0402
			where 1 = 1
			and SEG_NO=#segNo#
			<isNotEmpty prepend=" AND " property="handPointId">
				TARGET_HAND_POINT_ID in
				<iterate property="handPointId" open="("
						 close=")" conjunction=" , ">
					#handPointId[]#
				</iterate>
			</isNotEmpty>
			union
			select CAR_TRACE_NO, VEHICLE_NO, TARGET_HAND_POINT_ID
			from meli.tlirl0301
			where 1 = 1
			and SEG_NO=#segNo#
		<isNotEmpty prepend=" AND " property="handPointId">
			TARGET_HAND_POINT_ID in
			<iterate property="handPointId" open="("
					 close=")" conjunction=" , ">
				#handPointId[]#
			</iterate>
		</isNotEmpty>) a
		group by TARGET_HAND_POINT_ID
		order by countSum asc
	</select>

	<select id="queryVehicleCountCq" resultClass="String">
		select count(1) as "count"
		from (select distinct VEHICLE_NO
			  from meli.tlirl0401
			  where 1 = 1
				and SEG_NO =#segNo#
			 ) A
	</select>


	<select id="queryV" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0401">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM ${meliSchema}.tlirl0401 WHERE 1=1
		and SEG_NO	= #segNo#
		and CAR_TRACE_NO=#carTraceNo#
		and VOUCHER_NUM!=#allocateVehicleNo#
		and VEHICLE_NO=#vehicleNo#
		ORDER BY CAR_TRACE_NO,QUEUE_NUMBER ASC

	</select>

	<update id="updateVoucherNum">
		UPDATE ${meliSchema}.tlirl0401
		SET VOUCHER_NUM=#allocateVehicleNo#
		WHERE SEG_NO=#segNo#
		and CAR_TRACE_NO=#carTraceNo#
	</update>

	<select id="queryMinQueueNumberForRule" resultClass="String">
		select min(QUEUE_NUMBER) as "queueNumber",
			   VEHICLE_NO as "vehicleNo"
		from meli.tlirl0401
		where 1 = 1
		  and SEG_NO = #segNo#
		  and TARGET_HAND_POINT_ID = #targetHandPointId#
		  group by VEHICLE_NO
	</select>
</sqlMap>