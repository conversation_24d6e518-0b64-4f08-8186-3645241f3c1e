<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFTab id="info" showClose="false">
        <div id="info-1" title="按装卸业务维护">
            <EF:EFRegion id="inqu" title="查询条件">
                <div class="row">
                    <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                                     ratio="4:8"
                                     readonly="true" backFillFieldIds="inqu_status-0-segNo"
                                     containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                                     center="true" required="true"
                                     popupTitle="业务套账查询">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true"
                                type="hidden"/>
                    <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFSelect ename="inqu_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                                 valueField="valueField" textField="textField"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                        <EF:EFOption label="撤销" value="00"/>
                        <EF:EFOption label="新增" value="10"/>
                        <EF:EFOption label="确认" value="20"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="inqu_status-0-typeOfHandling" cname="装卸业务" optionLabel="全部" colWidth="3"
                                 valueField="valueField" textField="textField"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                        <EF:EFCodeOption codeName="P007"/>
                        <%--<EF:EFOption label="装货" value="10"/>
                        <EF:EFOption label="卸货" value="20"/>
                        <EF:EFOption label="卸货+装货" value="30"/>
                        <EF:EFOption label="周转架" value="40"/>
                        <EF:EFOption label="资材卸货" value="50"/>
                        <EF:EFOption label="废料提货" value="60"/>--%>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                                   endName="inqu_status-0-recCreateTimeEnd"
                                   startCname="创建日期(始)" endCname="创建日期(止)"
                                   parseFormats="['yyyy-MM-ddHH:mm']"
                                   colWidth="3" ratio="3:3"
                                   format="yyyy-MM-dd" role="date"/>
                    <EF:EFInput type="text" ename="inqu_status-0-uuid" cname="流水号" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result" title="结果集">
                <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0105" queryMethod="query"
                           autoBind="false" isFloat="true" personal="true" sort="all">
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false" required="true"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false"/>
                    <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                        <%--<EF:EFOptions blockId="testType1Block" textField="codeDesc" valueField="codeValue"/>--%>
                        <EF:EFOption label="撤销" value="00"/>
                        <EF:EFOption label="新增" value="10"/>
                        <EF:EFOption label="确认" value="20"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="typeOfHandling" cname="装卸业务" width="200" align="center" required="true">
                        <EF:EFCodeOption codeName="P007"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="reservationIdentity" cname="预约身份(承运商/客户)" align="center"
                                      required="true" width="160" hidden="true">
                        <EF:EFCodeOption codeName="P002"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="customerId" cname="承运商/客户代码" align="center" required="true" width="160"
                                 hidden="true"/>
                    <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" required="true"
                                 enable="false" width="160" hidden="true"/>
                    <EF:EFColumn ename="reservationMaxNum" cname="预约最大数(每时段)" align="center" required="true" width="160" />
                    <EF:EFColumn ename="reservationMaxNumDay" cname="预约最大数(每日)" align="center" />
                    <EF:EFComboColumn ename="mergeTags" cname="合并分组" align="center">
                        <EF:EFOption label="" value=""/>
                        <EF:EFOption label="1" value="1"/>
                        <EF:EFOption label="2" value="2"/>
                        <EF:EFOption label="3" value="3"/>
                        <EF:EFOption label="4" value="4"/>
                        <EF:EFOption label="5" value="5"/>
                        <EF:EFOption label="6" value="6"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="remark" cname="备注" required="false" align="left" editType="textarea"
                                 maxlength="500"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div id="info-2" title="按承运商/客户维护">
            <EF:EFRegion id="inqu2" title="查询条件">
                <div class="row">
                    <EF:EFPopupInput ename="inqu2_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                                     ratio="4:8"
                                     readonly="true" backFillFieldIds="inqu2_status-0-segNo"
                                     containerId="unitInfo2" popupWidth="600" pupupHeight="300" originalInput="true"
                                     center="true" required="true"
                                     popupTitle="业务套账查询">
                    </EF:EFPopupInput>
                    <EF:EFInput ename="inqu2_status-0-segNo" cname="系统账套" colWidth="3" disabled="true"
                                type="hidden"/>
                    <EF:EFInput ename="inqu2_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                                required="true"/>
                    <EF:EFSelect ename="inqu2_status-0-status" cname="状态" optionLabel="全部" colWidth="3"
                                 valueField="valueField" textField="textField"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                        <EF:EFOption label="撤销" value="00"/>
                        <EF:EFOption label="新增" value="10"/>
                        <EF:EFOption label="确认" value="20"/>
                    </EF:EFSelect>
                    <EF:EFSelect ename="inqu2_status-0-reservationIdentity" cname="预约身份(客户/承运商)"
                                 optionLabel="全部" colWidth="3"
                                 valueField="valueField" textField="textField"
                                 template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                        <EF:EFCodeOption codeName="P002"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFPopupInput ename="inqu2_status-0-customerId" cname="承运商" resizable="true" colWidth="3"
                                     ratio="4:8"
                                     readonly="true" backFillFieldIds="inqu2_status-0-customerName"
                                     containerId="userNum" popupWidth="600" pupupHeight="300" originalInput="true"
                                     center="true"
                                     popupTitle="承运商查询">
                    </EF:EFPopupInput>
                    <EF:EFInput type="text" ename="inqu2_status-0-customerName" cname="承运商名称" colWidth="3"
                                ratio="4:8" disabled="true"/>
                    <EF:EFPopupInput ename="inqu2_status-0-customerId2" cname="客户代码" resizable="true"
                                     colWidth="3"
                                     ratio="4:8"
                                     readonly="true" backFillFieldIds="inqu2_status-0-customerName2"
                                     containerId="userNum2" popupWidth="600" pupupHeight="300" originalInput="true"
                                     center="true"
                                     popupTitle="客户代码查询">
                    </EF:EFPopupInput>
                    <EF:EFInput type="text" ename="inqu2_status-0-customerName2" cname="客户代码名称" colWidth="3"
                                ratio="4:8" disabled="true"/>
                </div>
                <div class="row">
                    <EF:EFDateSpan startName="inqu2_status-0-recCreateTimeStart"
                                   endName="inqu2_status-0-recCreateTimeEnd"
                                   startCname="创建日期(始)" endCname="创建日期(止)"
                                   parseFormats="['yyyy-MM-ddHH:mm']"
                                   colWidth="3" ratio="3:3"
                                   format="yyyy-MM-dd" role="date"/>
                    <EF:EFInput type="text" ename="inqu2_status-0-uuid" cname="流水号" colWidth="3"
                                placeholder="模糊查询"
                                ratio="4:8"/>
                </div>
            </EF:EFRegion>
            <EF:EFRegion id="result2" title="结果集">
                <EF:EFGrid blockId="result2" autoDraw="no" serviceName="LIRL0105" queryMethod="query2"
                           autoBind="false" isFloat="true" personal="true" sort="all">
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false" required="true"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false"/>
                    <EF:EFComboColumn ename="status" cname="状态" align="center" enable="false">
                        <%--<EF:EFOptions blockId="testType1Block" textField="codeDesc" valueField="codeValue"/>--%>
                        <EF:EFOption label="撤销" value="00"/>
                        <EF:EFOption label="新增" value="10"/>
                        <EF:EFOption label="确认" value="20"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="typeOfHandling" cname="装卸业务" align="center" required="true"
                                      hidden="true">
                        <EF:EFCodeOption codeName="P007"/>
                    </EF:EFComboColumn>
                    <EF:EFComboColumn ename="reservationIdentity" cname="预约身份(承运商/客户)" align="center"
                                      required="true" width="160">
                        <EF:EFCodeOption codeName="P002"/>
                    </EF:EFComboColumn>
                    <EF:EFColumn ename="customerId" cname="承运商/客户代码" align="center" required="true" width="160"/>
                    <EF:EFColumn ename="customerName" cname="承运商/客户名称" align="center" required="true"
                                 enable="false" width="160"/>
                    <EF:EFColumn ename="reservationMaxNum" cname="预约最大数(每日)" align="center" required="true"/>
                    <EF:EFColumn ename="remark" cname="备注" required="false" align="left" editType="textarea"
                                 maxlength="500"/>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center"/>
                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
    </EF:EFTab>

    <EF:EFWindow id="ADDSUBWINDOW" url="" width="90%" height="60%" top="70%" left="170px" refresh="true" lazyload="true"
                 title="客商代码查询">
        <div title="客商信息查询">
            <EF:EFRegion id="sub_query" title="查询条件">
                <EF:EFInput ename="sub_query_status-0-userNum" cname="客商代码" placeholder="模糊条件"/>
                <EF:EFInput ename="sub_query_status-0-chineseUserName" cname="客商名称" placeholder="模糊条件"/>
                <EF:EFSelect ename="sub_query_status-0-reservationIdentity" cname="预约身份(承运商/客户)"
                             optionLabel="全部" colWidth="3" enable="false"
                             valueField="valueField" textField="textField"
                             template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#">
                    <EF:EFCodeOption codeName="P002"/>
                </EF:EFSelect>
                <EF:EFInput ename="sub_query_status-0-unitCode" cname="业务单元代码" colWidth="3" disabled="true" type="hidden"/>
                <EF:EFInput ename="sub_query_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            </EF:EFRegion>
        </div>
        <EF:EFRegion id="sub_result" title="结果集">
            <EF:EFGrid isFloat="true" blockId="sub_result" autoBind="false" autoDraw="false" serviceName="LIRL0101"
                       queryMethod="subQuery">
                <EF:EFColumn ename="userNum" cname="客商代码" enable="false" align="center" width="120"/>
                <EF:EFColumn ename="chineseUserName" cname="客商名称" enable="false" align="center" width="150"/>
            </EF:EFGrid>
        </EF:EFRegion>

    </EF:EFWindow>

    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo2" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo01" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo02" width="90%" height="60%"></EF:EFWindow>
    <%--    客户承运商--%>
    <EF:EFWindow url="${ctx}/web/LIRL0002" id="userNum" width="90%" height="60%"></EF:EFWindow>
    <EF:EFWindow url="${ctx}/web/LIRL0001" id="userNum2" width="90%" height="60%"></EF:EFWindow>
</EF:EFPage>
