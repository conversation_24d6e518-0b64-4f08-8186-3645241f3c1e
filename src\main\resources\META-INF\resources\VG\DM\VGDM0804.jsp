<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-likeFaultId" cname="故障单号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-likeVoucherNum" cname="检修单号" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-stuffReceivingStatus" cname="状态" colWidth="3"
                         template="#=valueField#-#=textField#" valueTemplate="#=valueField#-#=textField#"
                         optionLabel="{valueField:'', textField:'全部'}">
                <EF:EFCodeOption codeName="P061"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-stuffCode" cname="资材代码" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-stuffName" cname="资材名称" placeholder="模糊条件"
                        colWidth="3"/>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" autoDraw="no" readonly="true" sort="all" queryMethod="queryAll" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFComboColumn ename="stuffReceivingStatus" cname="状态" enable="false" align="center"
                              textField="textField" valueField="valueField" width="70">
                <EF:EFCodeOption codeName="P061"/>
            </EF:EFComboColumn>
            <%-- <EF:EFColumn ename="inventoryUuid" cname="库存uuid" width="120" align="center"/> --%>
            <EF:EFColumn ename="faultId" cname="故障单号" width="120" align="center"/>
            <EF:EFColumn ename="voucherNum" cname="检修单号" width="125" align="center"/>
            <EF:EFColumn ename="stuffCode" cname="资材代码" readonly="true"
                         enable="false" align="center"/>
            <EF:EFColumn ename="stuffName" cname="资材名称" align="center" enable="false" width="100"/>
            <EF:EFColumn ename="specDesc" cname="规格" readonly="true"
                         enable="false" width="200"/>
            <EF:EFColumn ename="usingWgt" cname="申请量" width="70"
                         align="center"/>
            <EF:EFColumn ename="measureId" cname="计量单位" readonly="true" width="70"
                         enable="false" align="center"/>
            <EF:EFColumn ename="stuffUsage" cname="用途" readonly="true" width="200"
                         enable="false"/>
            <EF:EFColumn ename="warehouseCode" cname="仓库代码" readonly="true"
                         enable="false" align="center" width="90"/>
            <EF:EFColumn ename="warehouseName" cname="仓库名称" readonly="true"
                         enable="false" align="center" width="200"/>
            <EF:EFColumn ename="purContractNum" cname="采购合同号" readonly="true" width="160" align="center"
                         enable="false"/>
            <EF:EFColumn ename="purOrderNum" cname="采购合同子项号" readonly="true" width="160" align="center"
                         enable="false"/>
            <EF:EFColumn ename="locationId" cname="库位代码" enable="false" width="120" align="center"/>
            <EF:EFColumn ename="locationName" cname="库位名称" enable="false" width="160" align="left"/>
            <EF:EFColumn ename="deptId" cname="领用部门" enable="false" width="100" align="center"/>
            <EF:EFColumn ename="deptName" cname="部门名称" enable="false" width="130" align="center"/>
            <EF:EFColumn ename="recCreator" cname="创建人" align="center" width="80"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recCreateTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="创建时间"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" align="center" width="80"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" width="100"/>
            <EF:EFColumn ename="recReviseTime" editType="datetime" width="140"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="修改时间"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
</EF:EFPage>