<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-11-27 13:44:37
   		Version :  1.0
		tableName :meli.tlids0301 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CRANE_ID  VARCHAR   NOT NULL, 
		 CRANE_NAME  VARCHAR   NOT NULL, 
		 CROSS_AREA  VARCHAR   NOT NULL, 
		 CROSS_AREA_NAME  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 FACTORY_AREA_NAME  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING  VARCHAR   NOT NULL, 
		 FACTORY_BUILDING_NAME  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR
	-->
<sqlMap namespace="tlids0301">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids0301">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				CRANE_ID	as "craneId",  <!-- 行车编号 -->
				CRANE_NAME	as "craneName",  <!-- 行车名称 -->
				CROSS_AREA	as "crossArea",  <!-- 跨区代码 -->
				CROSS_AREA_NAME	as "crossAreaName",  <!-- 跨区名称 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区代码 -->
				FACTORY_AREA_NAME	as "factoryAreaName",  <!-- 厂区名称 -->
				FACTORY_BUILDING	as "factoryBuilding",  <!-- 厂房代码 -->
				FACTORY_BUILDING_NAME	as "factoryBuildingName",  <!-- 厂房名称 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids0301 WHERE 1=1
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids0301 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="crossArea">
			CROSS_AREA = #crossArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="crossAreaName">
			CROSS_AREA_NAME = #crossAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryAreaName">
			FACTORY_AREA_NAME = #factoryAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuilding">
			FACTORY_BUILDING = #factoryBuilding#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryBuildingName">
			FACTORY_BUILDING_NAME = #factoryBuildingName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids0301 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										CRANE_ID,  <!-- 行车编号 -->
										CRANE_NAME,  <!-- 行车名称 -->
										CROSS_AREA,  <!-- 跨区代码 -->
										CROSS_AREA_NAME,  <!-- 跨区名称 -->
										FACTORY_AREA,  <!-- 厂区代码 -->
										FACTORY_AREA_NAME,  <!-- 厂区名称 -->
										FACTORY_BUILDING,  <!-- 厂房代码 -->
										FACTORY_BUILDING_NAME,  <!-- 厂房名称 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo:VARCHAR#, #unitCode:VARCHAR#, #craneId:VARCHAR#, #craneName:VARCHAR#, #crossArea:VARCHAR#, #crossAreaName:VARCHAR#, #factoryArea:VARCHAR#, #factoryAreaName:VARCHAR#, #factoryBuilding:VARCHAR#, #factoryBuildingName:VARCHAR#, #status:VARCHAR#, #recCreator:VARCHAR#, #recCreatorName:VARCHAR#, #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recRevisorName:VARCHAR#, #recReviseTime:VARCHAR#, #archiveFlag:VARCHAR#, #tenantUser:VARCHAR#, #delFlag#, #uuid:VARCHAR#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids0301 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlids0301 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->
					CRANE_ID	= #craneId#,   <!-- 行车编号 -->
					CRANE_NAME	= #craneName#,   <!-- 行车名称 -->
					CROSS_AREA	= #crossArea#,   <!-- 跨区代码 -->
					CROSS_AREA_NAME	= #crossAreaName#,   <!-- 跨区名称 -->
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区代码 -->
					FACTORY_AREA_NAME	= #factoryAreaName#,   <!-- 厂区名称 -->
					FACTORY_BUILDING	= #factoryBuilding#,   <!-- 厂房代码 -->
					FACTORY_BUILDING_NAME	= #factoryBuildingName#,   <!-- 厂房名称 -->
					STATUS	= #status#,   <!-- 状态 -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->
					UUID	= #uuid#  <!-- ID -->
			WHERE 	
	</update>
  
</sqlMap>