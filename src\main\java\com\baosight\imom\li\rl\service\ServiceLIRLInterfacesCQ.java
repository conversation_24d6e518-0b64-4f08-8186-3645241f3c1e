package com.baosight.imom.li.rl.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.li.domain.Tlirl0597;
import com.baosight.imom.common.li.domain.Tlirl0598;
import com.baosight.imom.common.li.domain.Tlirl0599;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.el.lang.ELArithmetic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.imom.li.rl.service.ServiceLIRLInterfacePda.STATUS_UPDATE_TOKEN;

/***
 * 重庆小程序共享接口
 */
public class ServiceLIRLInterfacesCQ extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLIRLInterfacesCQ.class);


    // 日期格式化工具
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat DATE_FORMAT_14 = new SimpleDateFormat("yyyyMMddHHmmss");

    public static <K, V> List<HashMap<K, V>> removeDuplicates(List<HashMap> list) {
        List<HashMap<K, V>> result = new ArrayList<>();
        for (HashMap<K, V> map : list) {
            if (!result.contains(map)) {
                result.add(map);
            }
        }
        return result;
    }

    /***
     * 查询装货配单的提单信息
     *	S_LI_RL_0075
     */
    public EiInfo queryLoadingWithBillOfLading(EiInfo inInfo) {

        List<HashMap> resultList = new ArrayList<>();

        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//业务单元
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空!");
            return inInfo;
        }
        //int kdCount = 0;
        //int zkCount = 0;
        int kdRemainPage = 0;
        Integer limit = inInfo.getInt("limit");
        Integer kdOffset = inInfo.getInt("kdOffset");
        String reservationIdentity = (String) inInfo.get("reservationIdentity");//司机身份
        String ladingSpotAddr = (String) inInfo.get("ladingSpotAddr");//终到站地址
        List<String> customerId = (List<String>) inInfo.get("customerId");
        for(int i=0;i<customerId.size();i++){
            if(null==customerId.get(i)){
                customerId.remove(i);
            }
        }
        String ladingBillIdEq = (String) inInfo.get("ladingBillId");
        String ladingBillRemark = (String) inInfo.get("ladingBillRemark");
        List<String> listV = new ArrayList<>();
        listV.add("A18113105");
        listV.add("*********");
        listV.add("494668005");
        EiInfo eiInfo1 = new EiInfo();
        eiInfo1.set("segNo", segNo);
        //开单的分页需要数据正确才能正确分页，因此也是全查
        eiInfo1.set("limit", "-999999");
        eiInfo1.set("offset", "0");
        eiInfo1.set("ladingBillRemark", ladingBillRemark); // 提单备注
        eiInfo1.set("ladingBillStatus", "50");//生效
        eiInfo1.set("ladingSpotIds", listV);//生效
        if ("10".equals(reservationIdentity)) {
            eiInfo1.set("userNumIn", customerId);
            eiInfo1.set("deliveryType", "10");
        } else if ("20".equals(reservationIdentity)){
            if(CollectionUtils.isEmpty(customerId)){
                eiInfo1.set("tproviderIdIn", customerId);
            }else{
                eiInfo1.set("tproviderIdIn", CollectionUtils.isNotEmpty(customerId) ? customerId : " ");
            }
            eiInfo1.set("deliveryType", "20");
        }

        if (StringUtils.isNotBlank(ladingBillIdEq)) {
            eiInfo1.set("ladingBillIdEq", ladingBillIdEq);
        }

        if (StringUtils.isNotBlank(ladingSpotAddr)) {
            eiInfo1.set("destSpotNameEq", ladingSpotAddr);
        }
        List<HashMap> outRusultList = new ArrayList<>();

        //开始处理开单数据
        List<HashMap> kdResultList = new ArrayList<>();
        if (!"70".equals(reservationIdentity)) {
            eiInfo1.set(EiConstant.serviceId, "S_UV_SL_9018");
            //调post请求
            EiInfo outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == -1) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                throw new RuntimeException(outInfo.getMsg());
            }
            //开单返回的总数量
            //kdCount = (int) outInfo.get("count");
            List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
            List<HashMap> result2 = (List<HashMap>) outInfo.get("result2");
            List<HashMap> result1 = (List<HashMap>) outInfo.get("result1");
            if(CollectionUtils.isNotEmpty(rusult)){
                Map kdResultMap = this.kdPagedell(rusult, result2,result1, limit, kdOffset, segNo);
                if(kdResultMap!=null){
                    //最终返回的转库单结果集
                    kdResultList = (List<HashMap>) kdResultMap.get("kdList");
                    //返回的还剩余的页数以及现在的页数
                    if(CollectionUtils.isNotEmpty(kdResultList)){
                        kdOffset = (int) kdResultMap.get("kdOffset");
                        kdRemainPage = (int) kdResultMap.get("kdRemainPage");
                        resultList.addAll(kdResultList);
                    }
                }
            }

        }

        inInfo.set("kdOffset",kdOffset);
        inInfo.set("kdRemainPage",kdRemainPage);
        //将开单的数据中已经装车配单完成的单子去除
        //List<HashMap> kdResultList = this.removeAlreadyAllocatedVehicles(outRusultList);
        //开单结果集数据
        //int kdResultCount = kdResultList.size();


        //前端传的开单的页数
        int zkOffset = inInfo.getInt("zkOffset");
        int zkRemainPage = 0;
        //开始处理转库单数据
        List<HashMap> zkResultList = new ArrayList<>();
        if ("20".equals(reservationIdentity)||"70".equals(reservationIdentity)||"40".equals(reservationIdentity)) {


            List<String> statusS = new ArrayList<>();
            statusS.add("10");
            statusS.add("20");
            String customerIdP = " ";
            if(CollectionUtils.isNotEmpty(customerId)){
                customerIdP = customerId.get(0);
            }

            //查询转库单
            EiInfo eiInfo2 = new EiInfo();
            eiInfo2.set("segNo", segNo);
            eiInfo2.set("transBillTypeS", statusS);
            eiInfo2.set("packPutOutFlag", "0");
            //物流的接口没有分页，所以这里设置-999999 全部查询，在IMOM代码中进行分页
            eiInfo2.set("limit", -999999);
            eiInfo2.set("offset", "0");
            if ("20".equals(reservationIdentity)){
                eiInfo2.set("tproviderId", customerIdP);
                eiInfo2.set("deliveryType", "20");
            }else if ("70".equals(reservationIdentity)){
                eiInfo2.set("mproviderIdS", customerId);
            }
            eiInfo2.set("loadingSpotIdS", listV);
            if (StringUtils.isNotBlank(ladingBillIdEq)) {
                eiInfo2.set("transBillId", ladingBillIdEq);
            }
            if (StringUtils.isNotBlank(ladingSpotAddr)) {
                eiInfo2.set("destSpotName", ladingSpotAddr);
            }
            eiInfo2.set(EiConstant.serviceId, "S_UC_EP_0044");
            logger.info("调用物流中心查询转库单接口: {}", eiInfo2.toJSONString());
            //调post请求
            EiInfo outInfo = EServiceManager.call(eiInfo2, TokenUtils.getXplatToken());
            //zkCount = outInfo.getInt("totalCount");
            List<HashMap> rusultZK = (List<HashMap>) outInfo.get("result");
            //将转库单数据进行处理
            if(CollectionUtils.isNotEmpty(rusultZK)){
                Map zkResultMap = this.zkPagedell(rusultZK,limit,zkOffset,segNo);
                if(zkResultMap!=null){
                    //最终返回的转库单结果集
                    zkResultList = (List<HashMap>) zkResultMap.get("zkList");
                    if(CollectionUtils.isNotEmpty(zkResultList)){
                        //返回的还剩余的页数以及现在的页数
                        zkOffset = (int) zkResultMap.get("zkOffset");
                        zkRemainPage = (int) zkResultMap.get("zkRemainPage");
                        resultList.addAll(zkResultList);
                    }
                }
            }

        }
        //开单与转库的单子汇总条数
        //int totalCount = kdCount + zkCount;
        // 根据limit进行分页，计算总页数

        inInfo.set("zkOffset",zkOffset);
        inInfo.set("zkRemainPage",zkRemainPage);

        inInfo.set("result", resultList);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        return inInfo;
    }

    /**
     * 去除已经装车配单的数据
     */
    private List<HashMap> removeAlreadyAllocatedVehicles(List<HashMap> vehicleList) {
        //已经添加装车配单的数据不再显示出来
        List<HashMap> outRusultList2 = new ArrayList<>();
        for (HashMap hashMap : vehicleList) {
            String ladingBillId = MapUtils.getString(hashMap, "ladingBillId", "");
            List<HashMap> packList = (List<HashMap>) hashMap.get("packList");
            String billMethod = (String) hashMap.get("billingMethod");
            if (CollectionUtils.isNotEmpty(packList)) {
                List<HashMap> packList2 = new ArrayList<>();
                // 使用多线程处理packList
                int threadCount = 5; // 线程数可根据实际情况调整
                int dataSize = packList.size();
                int chunkSize = (dataSize + threadCount - 1) / threadCount;
                List<List<HashMap>> partitions = new ArrayList<>();
                for (int i = 0; i < threadCount; i++) {
                    int start = i * chunkSize;
                    int end = Math.min(start + chunkSize, dataSize);
                    if (start < end) {
                        partitions.add(packList.subList(start, end));
                    }
                }

                List<HashMap> packList2Sync = Collections.synchronizedList(new ArrayList<>());
                List<Thread> threads = new ArrayList<>();

                for (List<HashMap> part : partitions) {
                    Thread t = new Thread(() -> {
                        for (HashMap hashMap1 : part) {
                            String packId = MapUtils.getString(hashMap1, "packId", "");
                            String matInnerId = MapUtils.getString(hashMap1, "matInnerId", "");
                            Map queryLIRL0503 = new HashMap();
                            queryLIRL0503.put("packId", packId);
                            queryLIRL0503.put("matInnerId", matInnerId);
                            queryLIRL0503.put("voucherNum", ladingBillId);
                            queryLIRL0503.put("statusSX", "10");
                            //查询配车单子表已有的数据
                                List<Integer> lirl0503s = dao.query(LIRL0503.QUERY_COUNT, queryLIRL0503);
                                if (lirl0503s.get(0) > 0) {
                                    System.out.println(lirl0503s.toString());
                                    if (StringUtils.isBlank(packId)){
                                        //形提
                                        packList2Sync.add(hashMap1);
                                    }
                                } else {
                                    packList2Sync.add(hashMap1);
                                }
                        }
                    });
                    threads.add(t);
                    t.start();
                }

                // 等待所有线程执行完毕
                for (Thread t : threads) {
                    try {
                        t.join();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        // 可以记录日志
                    }
                }

                packList2.addAll(packList2Sync);
                if (packList2.size() > 0) {
                    hashMap.put("packList", packList2);
                    outRusultList2.add(hashMap);
                }
            } else if (!"10".equals(billMethod)) {
                outRusultList2.add(hashMap);
            }
        }
        return outRusultList2;
    }

    /**
     * 开单数据分页处理
     */
    public Map kdPagedell(List<HashMap> rusult,List<HashMap> result2,List<HashMap> result1,int limit,int offset,String segNo){

        Map returnMap = new HashMap();
        //全部的页数
        int totalPage = (rusult.size() + limit - 1) / limit;

        if(offset>=totalPage){
            return null;
        }
        //返回的结果集
        List<HashMap> resultList = new ArrayList<>();

        List<HashMap> returnList = new ArrayList<>();
        offset = offset - 1;
        if (CollectionUtils.isNotEmpty(rusult)) {
            do {

                offset = offset+1;
                if(offset==totalPage){
                    break;
                }

                returnMap.put("kdOffset",offset);

                List<HashMap> rusultData = new ArrayList<>();

                // 对开单的结果集rusult进行分页处理
                if (rusult != null && rusult.size() > 0) {
                    int total = rusult.size();
                    int start = offset < 0 ? 0 : offset * limit;
                    int end = start + limit;
                    if (end > total || limit < 0) {
                        end = total;
                    }
                    rusultData = rusult.subList(start, end);
                }


                for (HashMap hashMapRusult : rusultData) {
                    List<HashMap> hashMap2List = new ArrayList<>();
                    String deliveryType = MapUtils.getString(hashMapRusult, "deliveryType", "");//交货方式（10：自提、20：代运）
                    //提单创建人
                    String recCreator = MapUtils.getString(hashMapRusult, "recCreator", "");//交货方式（10：自提、20：代运）
                    String userNum = MapUtils.getString(hashMapRusult, "userNum", "");
                    String userName = MapUtils.getString(hashMapRusult, "userName", "");
                    String billingMethod = MapUtils.getString(hashMapRusult, "billingMethod", "");//开单方式 10-按捆包 20-按重量 30-按件数
                    String ladingBillId = MapUtils.getString(hashMapRusult, "ladingBillId", "");//提单号
                    String ladingSpotId = MapUtils.getString(hashMapRusult, "ladingSpotId", "");//始发站(/仓库)代码
                    String ladingSpotName = MapUtils.getString(hashMapRusult, "ladingSpotName", "");//始发站(/仓库)名称
                    String destSpotId = MapUtils.getString(hashMapRusult, "destSpotId", "");//终到站(/仓库)代码
                    String destSpotName = MapUtils.getString(hashMapRusult, "destSpotName", "");//终到站(/仓库)名称
                    String totalWeight = MapUtils.getString(hashMapRusult, "totalWeight", "");//总重量
                    String totalPackQty = MapUtils.getString(hashMapRusult, "totalPackQty", "");//捆包总数量
                    String customerName = MapUtils.getString(hashMapRusult, "userName", "");//捆包总数量
                    String emergencyDeliveryTime = MapUtils.getString(hashMapRusult, "emergencyDeliveryTime", "");//紧急交货时间
                    String ladingSpotAddr = MapUtils.getString(hashMapRusult, "ladingSpotAddr", "");//紧急交货时间
                    String remark = MapUtils.getString(hashMapRusult, "remark", "");//提单备注
                    String orderNum = MapUtils.getString(hashMapRusult, "orderNum", "");//提单备注
                    String requireFinishDate = MapUtils.getString(hashMapRusult, "requireFinishDate", "");//提单备注
                    HashMap outRusult = new HashMap();
                    outRusult.put("ladingBillId", ladingBillId);
                    outRusult.put("ladingSpotName", ladingSpotName);
                    outRusult.put("destSpotName", destSpotName);
                    outRusult.put("totalWeight", totalWeight);
                    outRusult.put("totalPackQty", totalPackQty);
                    outRusult.put("billingMethod", billingMethod);
                    outRusult.put("deliveryType", deliveryType);
                    outRusult.put("emergencyDeliveryTime", emergencyDeliveryTime);
                    outRusult.put("segNo", segNo);
                    outRusult.put("ladingSpotAddr", ladingSpotAddr);
                    outRusult.put("ladingBillRemark", remark);
                    outRusult.put("customerName", customerName);
                    outRusult.put("customerId", userNum);
                    outRusult.put("ladingSpotId", ladingSpotId);
                    outRusult.put("requireFinishDate", requireFinishDate);
                    //按捆包普通提单 按量形式提单
                    if (!"10".equals(billingMethod)) {
                        for (HashMap hashMap2 : result1) {
                            String ladingBillId2 = MapUtils.getString(hashMap2, "ladingBillId", "");
                            hashMap2.put("ladingBillRemark", remark);
                            if (ladingBillId.equals(ladingBillId2)) {
                                hashMap2List.add(hashMap2);
                            }
                        }
                        outRusult.put("packList", hashMap2List);
                    } else {

                        for (HashMap hashMap2 : result2) {
                            String retrunStatus = MapUtils.getString(hashMap2, "returnStatus", "");//库位代码
                            if ("Y".equals(retrunStatus)) {
                                //已出库的
                                continue;
                            }
                            String ladingBillId2 = MapUtils.getString(hashMap2, "ladingBillId", "");
                            hashMap2.put("ladingBillRemark", remark);
                            if (ladingBillId.equals(ladingBillId2)) {
                                hashMap2List.add(hashMap2);
                            }
                        }
                        outRusult.put("packList", hashMap2List);
                    }
                    resultList.add(outRusult);
                }
                //去重的数据
                resultList = this.removeAlreadyAllocatedVehicles(resultList);

            } while (resultList.size()<10);
            //还剩余几页
            returnMap.put("kdRemainPage", totalPage-offset-1);
        }
        returnMap.put("kdList",resultList);
        return returnMap;


    }

    /**
     * 转库单数据分页处理
     * @param rusultZK
     * @param limit
     * @param offset
     * @return
     */
    public Map zkPagedell(List<HashMap> rusultZK,int limit,int offset,String segNo){
        Map returnMap = new HashMap();
        //全部的页数
        int totalPage = (rusultZK.size() + limit - 1) / limit;
        //返回的结果集
        List<HashMap> resultList = new ArrayList<>();

        List<HashMap> returnList = new ArrayList<>();
        offset = offset - 1;
        do {
            offset = offset+1;
            if(offset==totalPage){
                break;
            }

            returnMap.put("zkOffset",offset);

            List<HashMap> result2 = new ArrayList<>();
            // 对rusultZK进行分页处理
            if (rusultZK != null && rusultZK.size() > 0) {
                int total = rusultZK.size();
                int start = offset < 0 ? 0 : offset*limit;
                int end = start + limit;
                if (end > total || limit < 0) {
                    end = total;
                }
                result2 = rusultZK.subList(start, end);
            }
            int resultZK = rusultZK.size();
            if (CollectionUtils.isNotEmpty(result2)) {
                // 使用三个线程处理result2集合
                int threadCount = 3;
                int dataSize = result2.size();
                int chunkSize = (dataSize + threadCount - 1) / threadCount;
                List<List<HashMap>> partitions = new ArrayList<>();
                for (int i = 0; i < threadCount; i++) {
                    int start = i * chunkSize;
                    int end = Math.min(start + chunkSize, dataSize);
                    if (start < end) {
                        partitions.add(result2.subList(start, end));
                    }
                }

                List<HashMap> threadResultList = Collections.synchronizedList(new ArrayList<>());
                List<Thread> threads = new ArrayList<>();

                for (List<HashMap> part : partitions) {
                    Thread t = new Thread(() -> {
                        for (HashMap hashMap45 : part) {
                            String ladingSpotName = MapUtils.getString(hashMap45, "ladingSpotName", "");
                            String destSpotName = MapUtils.getString(hashMap45, "destSpotName", "");
                            String sumNetWeight = MapUtils.getString(hashMap45, "sumNetWeight", "");
                            String sumNtotalQty = MapUtils.getString(hashMap45, "sumNtotalQty", "");
                            String transBillId = MapUtils.getString(hashMap45, "transBillId", "");
                            String deliveryType = MapUtils.getString(hashMap45, "deliveryType", "");
                            String customerName = MapUtils.getString(hashMap45, "customerName", "");
                            String userNum = MapUtils.getString(hashMap45, "userNum", "");
                            String ladingSpotId = MapUtils.getString(hashMap45, "ladingSpotId", "");
                            HashMap outRusult = new HashMap();
                            outRusult.put("ladingBillId", transBillId);
                            outRusult.put("ladingSpotName", ladingSpotName);
                            outRusult.put("destSpotName", destSpotName);
                            outRusult.put("totalWeight", sumNetWeight);
                            outRusult.put("totalPackQty", sumNtotalQty);
                            outRusult.put("deliveryType", deliveryType);
                            outRusult.put("segNo", segNo);
                            outRusult.put("customerName", customerName);
                            outRusult.put("customerId", userNum);
                            outRusult.put("ladingSpotId", ladingSpotId);
                            // 查询转库单
                            EiInfo eiInfo3 = new EiInfo();
                            eiInfo3.set("segNo", segNo);
                            eiInfo3.set("transBillId", transBillId);
                            eiInfo3.set("transBillType", "10");
                            eiInfo3.set("packPutOutFlag", "0");
                            eiInfo3.set("limit", -999999);
                            eiInfo3.set("offset", "0");
                            eiInfo3.set(EiConstant.serviceId, "S_UC_EP_0045");
                            // 调post请求
                            EiInfo outInfo = EServiceManager.call(eiInfo3, TokenUtils.getXplatToken());
                            if (outInfo.getStatus() == -1) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("调用物流中心返回报错:" + outInfo.getMsg());
                                // 不抛异常，继续处理
                            }
                            List<HashMap> rusult1 = (List<HashMap>) outInfo.get("result");
                            if (CollectionUtils.isNotEmpty(rusult1)) {
                                if (rusult1 != null && rusult1.size() > 0) {
                                    for (HashMap hashMap : rusult1) {
                                        String transBillIdSon = MapUtils.getString(hashMap, "transBillId", "");
                                        hashMap.put("ladingBillId", transBillIdSon);
                                    }
                                    outRusult.put("packList", rusult1);
                                }
                                threadResultList.add(outRusult);
                            }
                        }
                    });
                    threads.add(t);
                    t.start();
                }

                // 等待所有线程执行完毕
                for (Thread t : threads) {
                    try {
                        t.join();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        // 可以记录日志
                    }
                }

                returnList.addAll(threadResultList);
            }
            //去重的数据
            resultList = this.removeAlreadyAllocatedVehicles(returnList);
        }while (resultList.size()<10);
        //还剩余几页
        returnMap.put("zkRemainPage", totalPage-offset-1);
        returnMap.put("zkList",resultList);
        return returnMap;
    }


    /***
     * 查询车辆司机信息
     *	S_LI_RL_0076
     */
    public EiInfo queryVehicle(EiInfo inInfo) {
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//业务单元
        List<String> customerId = (List<String>) inInfo.get("customerId");
        String tel = (String) inInfo.get("tel");
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空!");
            return inInfo;
        }
        Map queryVehicle = new HashMap();
        queryVehicle.put("segNo", segNo);
        if (CollectionUtils.isNotEmpty(customerId)){
            String customerIdP = customerId.get(0);
            if (StringUtils.isNotBlank(customerIdP)){
                queryVehicle.put("customerId", customerId);
                queryVehicle.put("tel", tel);
            }
        }
        List<HashMap> vehicleList = dao.query(LIRL0103.QUERY_VEHICLE_NO2, queryVehicle);
        inInfo.set("vehicleList", vehicleList);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        return inInfo;
    }

    /***
     * 新增装货配单
     *	S_LI_RL_0077
     */
    public EiInfo addAMatchingOrder(EiInfo inInfo) {
        List<HashMap> rusult = (List<HashMap>) inInfo.get("result");
        List<HashMap> rusult2 = (List<HashMap>) inInfo.get("result2");
        String vehicleNo = "";
        String time = "";
        String driverName = "";
        String tel = "";
        String driverTel = "";
        String businessType = ""; //业务类型
        List<String> targetHandPointIdList = new ArrayList<>();
        String recCreator = "";
        String recCreatorName = "";
        String customerId = "";
        String customerName = "";
        String carTraceNo = "";
        String entryFlag = (String) inInfo.get("entryFlag");
        String nextAlcVehicleNo="";
        String  allocateVehicleNoNew="";
        if (rusult2.size() > 0) {
            HashMap hashMap = rusult2.get(0);
            vehicleNo = MapUtils.getString(hashMap, "vehicleNo");
            time = MapUtils.getString(hashMap, "time");
            businessType = MapUtils.getString(hashMap, "businessType");
            driverName = MapUtils.getString(hashMap, "driverName");
            tel = MapUtils.getString(hashMap, "tel");
            driverTel = MapUtils.getString(hashMap, "driverTel");
            recCreator = MapUtils.getString(hashMap, "tel", "");//创建人
            recCreatorName = MapUtils.getString(hashMap, "administrator", "");//创建人姓名
            customerId = MapUtils.getString(hashMap, "customerId", "");//创建人姓名
            customerName = MapUtils.getString(hashMap, "customerName", "");//创建人姓名

        }
        String segNo = MapUtils.getString(rusult2.get(0), "segNo", "");

        String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(segNo, "IF_PERMIT_RESERVATION", dao);
        if ("1".equals(ifRemotePrint)){
            //开关打开不允许预约
            String massage = "此功能暂不可使用，静待通知!";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        String targetHandPointId = "";
        Map insertLIRL0502 = new HashMap();
        insertLIRL0502.put("segNo", segNo);
        insertLIRL0502.put("unitCode", segNo);
        insertLIRL0502.put("vehicleNo", vehicleNo);//车牌号
        insertLIRL0502.put("status", 20);
        insertLIRL0502.put("driverName", driverName);
        insertLIRL0502.put("driverTel", driverTel);
        //配车单流水号
        Date date = new Date(System.currentTimeMillis());
        String strSeqTypeId = "TMELI0502_SEQ01";
        String[] args = {segNo.substring(0, 2), date.toString(), ""};
        String allocateVehicleNo = SequenceGenerator.getNextSequence(strSeqTypeId, args); //配车单流水号
        insertLIRL0502.put("allocateVehicleNo", allocateVehicleNo);//配车单号
        insertLIRL0502.put("allocType", 10);//配车单类型
        if (StringUtils.isNotBlank(businessType)) {
            insertLIRL0502.put("allocType", 30);
        }
        insertLIRL0502.put("carTraceNo", carTraceNo);//车辆管理号
        insertLIRL0502.put("showFlag", 0);//展示标记
        insertLIRL0502.put("nextAlcVehicleNo", "");//下级配单号
        insertLIRL0502.put("estimatedTimeOfArrival", time);//预计到达时间
        // 创建人工号
        insertLIRL0502.put("recCreator", recCreator);
        // 创建人姓名
        insertLIRL0502.put("recCreatorName", recCreatorName);
        // 创建时间
        insertLIRL0502.put("recCreateTime", DateUtil.curDateTimeStr14());
        // 修改人工号
        insertLIRL0502.put("recRevisor", recCreator);
        // 修改人姓名
        insertLIRL0502.put("recRevisorName", recCreatorName);
        // 修改时间
        insertLIRL0502.put("recReviseTime", DateUtil.curDateTimeStr14());
        // UUID
        insertLIRL0502.put("uuid", UUIDUtils.getUUID());
        insertLIRL0502.put("customerId", customerId);
        insertLIRL0502.put("customerName", customerName);
        insertLIRL0502.put("allocMes", "");
        // 删除标记: 0-新增;1-删除;
        insertLIRL0502.put("delFlag", "0");
        insertLIRL0502.put("archiveFlag", "0");
        insertLIRL0502.put("tenantId", " ");
        insertLIRL0502.put("remark", " ");

        List<String> billList = new ArrayList<>();
        List<String> billOrderNum = new ArrayList<>();
        List<String> billOrderNumSum = new ArrayList<>();
        List<String> billSpecsDesc = new ArrayList<>();
        List<String> remarkList = new ArrayList<>();
        //批量插入
        List<Map> listBatch = new ArrayList<>();

        String emergencyDeliveryTimeNew="";
        EiInfo outInfo = new EiInfo();
        try {
            //明细新增
            if (rusult.size() > 0) {
                for (HashMap hashMap : rusult) {
                    String billingMethod = MapUtils.getString(hashMap, "billingMethod", "");
                    String emergencyDeliveryTime = MapUtils.getString(hashMap, "emergencyDeliveryTime", "");
                    String ladingBillRemark = MapUtils.getString(hashMap, "ladingBillRemark", "");
                    String voucherNum = MapUtils.getString(hashMap, "ladingBillId", "");
                    String ladingSpotId = MapUtils.getString(hashMap, "ladingSpotId", "");
                    String ladingSpotName = MapUtils.getString(hashMap, "ladingSpotName", "");
                    String requireFinishDate = MapUtils.getString(hashMap, "requireFinishDate", "");
                    customerId = MapUtils.getString(hashMap, "customerId", "");
                    customerName = MapUtils.getString(hashMap, "customerName", "");
                    if (StringUtils.isBlank(voucherNum)){
                        voucherNum=MapUtils.getString(hashMap, "transBillId", "");
                    }
                    String totalWeight = MapUtils.getString(hashMap, "totalWeight", "");
                    if (StringUtils.isNotBlank(emergencyDeliveryTime)){
                        emergencyDeliveryTimeNew=emergencyDeliveryTime;
                    }
                    List<HashMap> packList = (List<HashMap>) hashMap.get("packList");

                    //形式无实物库存
                    if (!"10".equals(billingMethod)&&voucherNum.startsWith("BL")) {
                        billList.add(voucherNum);
                        remarkList.add(ladingBillRemark);
                        //调物流服务拿到查询提货单重量件数信息
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", segNo);
                        eiInfo1.set("warehouseCode", "*********");
                        List<String> ladingBillIdList = new ArrayList<>();
                        ladingBillIdList.add(voucherNum);
                        eiInfo1.set("ladingBillIdList", ladingBillIdList);
                        Boolean isRefreshToken = Boolean.FALSE;
                        String tokenRefresh = "";
                        if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
                            isRefreshToken = Boolean.TRUE;
                            tokenRefresh = outInfo.get("accessToken").toString();
                        }
                        eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        Map attr = outInfo.getAttr();
                        Map result = MapUtils.getMap(attr, "result");
                        List<Map> packList1 = (List) result.get("packList");
                        boolean flag=false;
                        if (CollectionUtils.isNotEmpty(packList1)){
                        for (Map packListDi : packList1) {
                            String packId = MapUtils.getString(packListDi, "packId", "");
                            String warehouseCode = MapUtils.getString(packListDi, "warehouseCode", "");
                            String warehouseName = MapUtils.getString(packListDi, "warehouseName", "");
                            String locationId = MapUtils.getString(packListDi, "locationId", "");
                            String locationName = MapUtils.getString(packListDi, "locationName", "");
                            targetHandPointId = MapUtils.getString(packListDi, "targetHandPointId", "");
                            String deliveryType = MapUtils.getString(packListDi, "deliveryType", "");
                            String m_packId = MapUtils.getString(packListDi, "m_packId", "");
                            String custPartId = MapUtils.getString(packListDi, "custPartId", "");
                            String custPartName = MapUtils.getString(packListDi, "custPartName", "");
                            String purOrderNum = MapUtils.getString(packListDi, "orderNum", "");
                            String labelId = MapUtils.getString(packListDi, "labelId", "");
                            //判断是否是同一个销售订单子项号
                            if (CollectionUtils.isNotEmpty(packList)){
                                billOrderNum = packList.stream().distinct().map(map -> (String)map.get("orderNum")).collect(Collectors.toList());
                                billSpecsDesc = packList.stream().distinct().map(map -> (String)map.get("specsDesc")).collect(Collectors.toList());
                                for (HashMap map : packList) {
                                    String orderNum = (String) map.get("orderNum");
                                        Map insertLIRL0503 = new HashMap();
                                        insertLIRL0503.put("segNo", segNo);
                                        insertLIRL0503.put("unitCode", segNo);
                                        insertLIRL0503.put("status", 20);
                                        insertLIRL0503.put("billingMethod", billingMethod);
                                        insertLIRL0503.put("locationName", locationName);
                                        insertLIRL0503.put("labelId", labelId);
                                        String[] arg = {allocateVehicleNo, "", "", ""};
                                        String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                        insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                        insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);
                                        insertLIRL0503.put("voucherNum", voucherNum);
                                        insertLIRL0503.put("packId", packId);
                                        insertLIRL0503.put("outPackFlag", "");
                                        insertLIRL0503.put("warehouseCode", warehouseCode);
                                        insertLIRL0503.put("warehouseName", warehouseName);
                                        insertLIRL0503.put("deliveryType", deliveryType);
                                        insertLIRL0503.put("mPackId", m_packId);
                                        insertLIRL0503.put("custPartId", custPartId);
                                        insertLIRL0503.put("custPartName", custPartName);
                                        insertLIRL0503.put("putinType", MapUtils.getString(packListDi, "putinType", ""));
                                        insertLIRL0503.put("innerDiameter", 0);
                                        insertLIRL0503.put("prodDensity", 0);
                                        insertLIRL0503.put("productProcessId", "");
                                        insertLIRL0503.put("netWeight", MapUtils.getString(packListDi, "netWeight", ""));
                                        insertLIRL0503.put("customerId", customerId);
                                        insertLIRL0503.put("customerName", customerName);
                                        insertLIRL0503.put("matInnerId", MapUtils.getString(packListDi, "matInnerId", ""));
                                        insertLIRL0503.put("specsDesc", MapUtils.getString(packListDi, "specsDesc", ""));
                                        insertLIRL0503.put("piceNum", MapUtils.getString(packListDi, "pieceNum", ""));
                                        insertLIRL0503.put("factoryOrderNum", MapUtils.getString(packListDi, "factoryOrderNum", ""));
                                        insertLIRL0503.put("locationId", MapUtils.getString(packListDi, "locationId", ""));
                                        insertLIRL0503.put("locationName", MapUtils.getString(packListDi, "locationName", ""));
                                        insertLIRL0503.put("prodTypeId", StringUtils.isNotBlank(MapUtils.getString(packListDi, "prodTypeId", "")) ? MapUtils.getString(packListDi, "prodTypeId", "") : " ");
                                        insertLIRL0503.put("factoryArea", StringUtils.isNotBlank(MapUtils.getString(packListDi, "factoryArea", "")) ? MapUtils.getString(packListDi, "factoryArea", "") : "");
                                        insertLIRL0503.put("tradeCode", StringUtils.isNotBlank(MapUtils.getString(packListDi, "tradeCode", "")) ? MapUtils.getString(packListDi, "tradeCode", "") : " ");
                                        //查询渠道库存对应的装卸点信息
                                        //根据库位代码查询库位附属信息表获取装卸点
                                        //查询装卸点发送短信通知
                                        Map queryLIDS0601 = new HashMap();
                                        queryLIDS0601.put("segNo", segNo);
                                        queryLIDS0601.put("warehouseCode", warehouseCode);
                                        queryLIDS0601.put("locationId", locationId);
                                        queryLIDS0601.put("useStatus", 10);
                                        //查询库位附属信息表
                                        List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                        if (lids0601s.size() > 0) {
                                            LIDS0601 lids0601 = lids0601s.get(0);
                                            targetHandPointId = lids0601.getLoadingPointNo();
                                            targetHandPointIdList.add(targetHandPointId);
                                            insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                            if (StringUtils.isBlank(targetHandPointId)) {
                                                String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                inInfo.setMsg(massage);
                                                throw new RuntimeException(massage);
                                            }
                                            //判断装卸点是否启用
                                            Map queryLIRL0304 = new HashMap();
                                            queryLIRL0304.put("segNo", segNo);
                                            queryLIRL0304.put("handPointId", targetHandPointId);
                                            queryLIRL0304.put("status", "30");
                                            int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                            if (count < 0) {
                                                String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                inInfo.setMsg(massage);
                                                throw new RuntimeException(massage);
                                            }
                                        } else {
                                            String massage = "查询不到库位附属信息";
                                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            inInfo.setMsg(massage);
                                            throw new RuntimeException(massage);
                                        }

                                        // 创建人工号
                                        insertLIRL0503.put("recCreator", recCreator);
                                        // 创建人姓名
                                        insertLIRL0503.put("recCreatorName", recCreatorName);
                                        // 创建时间
                                        insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                        // 修改人工号
                                        insertLIRL0503.put("recRevisor", recCreator);
                                        // 修改人姓名
                                        insertLIRL0503.put("recRevisorName", recCreatorName);
                                        // 修改时间
                                        insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                        // UUID
                                        insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                        // 删除标记: 0-新增;1-删除;
                                        insertLIRL0503.put("delFlag", "0");
                                        insertLIRL0503.put("archiveFlag", "0");
                                        insertLIRL0503.put("tenantId", " ");
                                        insertLIRL0503.put("remark", " ");
                                        insertLIRL0503.put("purOrderNum", purOrderNum);
                                        insertLIRL0503.put("ladingBillRemark", ladingBillRemark);
                                        insertLIRL0503.put("requireFinishDate", requireFinishDate);
                                    insertLIRL0503.put("ladingWeight", totalWeight);
                                    listBatch.add(insertLIRL0503);
                                        flag=true;
                                }
                                billOrderNumSum.addAll(billOrderNum);
                            }
                            if (!flag){
                                billOrderNum = packList.stream().distinct().map(map -> (String)map.get("orderNum")).collect(Collectors.toList());
                                billSpecsDesc = packList.stream().distinct().map(map -> (String)map.get("specsDesc")).collect(Collectors.toList());
                                for (HashMap map : packList) {
                                    BigDecimal planNetWeight = convertToBigDecimal(map.get("planNetWeight"));
                                    String orderNum = (String) map.get("orderNum");
                                    String specsDesc = (String) map.get("specsDesc");
                                    Map insertLIRL0503 = new HashMap();
                                    insertLIRL0503.put("segNo", segNo);
                                    insertLIRL0503.put("unitCode", segNo);
                                    insertLIRL0503.put("status", 20);
                                    insertLIRL0503.put("billingMethod", "20");
                                    String[] arg = {allocateVehicleNo, "", "", ""};
                                    String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                    insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                    insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);

                                    insertLIRL0503.put("voucherNum", voucherNum);
                                    insertLIRL0503.put("packId", " ");
                                    insertLIRL0503.put("outPackFlag", " ");
                                    insertLIRL0503.put("warehouseCode", ladingSpotId);
                                    insertLIRL0503.put("deliveryType", " ");
                                    insertLIRL0503.put("mPackId", " ");
                                    insertLIRL0503.put("custPartId", " ");
                                    insertLIRL0503.put("custPartName", " ");
                                    insertLIRL0503.put("warehouseName", ladingSpotName);
                                    insertLIRL0503.put("putinType", " ");
                                    insertLIRL0503.put("innerDiameter", 0);
                                    insertLIRL0503.put("prodDensity", 0);
                                    insertLIRL0503.put("productProcessId", "");
                                    insertLIRL0503.put("netWeight", totalWeight);
                                    insertLIRL0503.put("customerId", customerId);
                                    insertLIRL0503.put("customerName", customerName);
                                    insertLIRL0503.put("matInnerId", " ");
                                    insertLIRL0503.put("specsDesc", specsDesc);
                                    insertLIRL0503.put("piceNum", 0);
                                    insertLIRL0503.put("factoryOrderNum", " ");
                                    insertLIRL0503.put("locationId", " ");
                                    insertLIRL0503.put("locationName"," ");
                                    insertLIRL0503.put("prodTypeId", " ");
                                    insertLIRL0503.put("factoryArea"," ");
                                    insertLIRL0503.put("tradeCode", " ");

                                    insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                    // 创建人工号
                                    insertLIRL0503.put("recCreator", recCreator);
                                    // 创建人姓名
                                    insertLIRL0503.put("recCreatorName", recCreatorName);
                                    // 创建时间
                                    insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                    // 修改人工号
                                    insertLIRL0503.put("recRevisor", recCreator);
                                    // 修改人姓名
                                    insertLIRL0503.put("recRevisorName", recCreatorName);
                                    // 修改时间
                                    insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                    // UUID
                                    insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                    // 删除标记: 0-新增;1-删除;
                                    insertLIRL0503.put("delFlag", "0");
                                    insertLIRL0503.put("archiveFlag", "0");
                                    insertLIRL0503.put("tenantId", " ");
                                    insertLIRL0503.put("remark", " ");
                                    insertLIRL0503.put("purOrderNum",orderNum);
                                    insertLIRL0503.put("ladingBillRemark", ladingBillRemark);
                                    insertLIRL0503.put("planNetWeight", String.valueOf(planNetWeight));
                                    insertLIRL0503.put("requireFinishDate", requireFinishDate);
                                    insertLIRL0503.put("ladingWeight", totalWeight);
                                    listBatch.add(insertLIRL0503);
                                }
                                billOrderNumSum.addAll(billOrderNum);
                            }
                        }
                        }else {
                            if (CollectionUtils.isNotEmpty(packList)){
                                billOrderNum = packList.stream().distinct().map(map -> (String)map.get("orderNum")).collect(Collectors.toList());
                                billSpecsDesc = packList.stream().distinct().map(map -> (String)map.get("specsDesc")).collect(Collectors.toList());
                                for (HashMap map : packList) {
                                    BigDecimal planNetWeight = convertToBigDecimal(map.get("planNetWeight"));
                                    String orderNum = (String) map.get("orderNum");
                                    String specsDesc = (String) map.get("specsDesc");
                                    Map insertLIRL0503 = new HashMap();
                                    insertLIRL0503.put("segNo", segNo);
                                    insertLIRL0503.put("unitCode", segNo);
                                    insertLIRL0503.put("status", 20);
                                    insertLIRL0503.put("billingMethod", "20");
                                    String[] arg = {allocateVehicleNo, "", "", ""};
                                    String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                    insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                    insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);

                                    insertLIRL0503.put("voucherNum", voucherNum);
                                    insertLIRL0503.put("packId", " ");
                                    insertLIRL0503.put("outPackFlag", " ");
                                    insertLIRL0503.put("warehouseCode", ladingSpotId);
                                    insertLIRL0503.put("deliveryType", " ");
                                    insertLIRL0503.put("mPackId", " ");
                                    insertLIRL0503.put("custPartId", " ");
                                    insertLIRL0503.put("custPartName", " ");
                                    insertLIRL0503.put("warehouseName", ladingSpotName);
                                    insertLIRL0503.put("putinType", " ");
                                    insertLIRL0503.put("innerDiameter", 0);
                                    insertLIRL0503.put("prodDensity", 0);
                                    insertLIRL0503.put("productProcessId", "");
                                    insertLIRL0503.put("netWeight", totalWeight);
                                    insertLIRL0503.put("customerId", customerId);
                                    insertLIRL0503.put("customerName", customerName);
                                    insertLIRL0503.put("matInnerId", " ");
                                    insertLIRL0503.put("specsDesc", specsDesc);
                                    insertLIRL0503.put("piceNum", 0);
                                    insertLIRL0503.put("factoryOrderNum", " ");
                                    insertLIRL0503.put("locationId", " ");
                                    insertLIRL0503.put("locationName"," ");
                                    insertLIRL0503.put("prodTypeId", " ");
                                    insertLIRL0503.put("factoryArea"," ");
                                    insertLIRL0503.put("tradeCode", " ");

                                    insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                    // 创建人工号
                                    insertLIRL0503.put("recCreator", recCreator);
                                    // 创建人姓名
                                    insertLIRL0503.put("recCreatorName", recCreatorName);
                                    // 创建时间
                                    insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                    // 修改人工号
                                    insertLIRL0503.put("recRevisor", recCreator);
                                    // 修改人姓名
                                    insertLIRL0503.put("recRevisorName", recCreatorName);
                                    // 修改时间
                                    insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                    // UUID
                                    insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                    // 删除标记: 0-新增;1-删除;
                                    insertLIRL0503.put("delFlag", "0");
                                    insertLIRL0503.put("archiveFlag", "0");
                                    insertLIRL0503.put("tenantId", " ");
                                    insertLIRL0503.put("remark", " ");
                                    insertLIRL0503.put("purOrderNum",orderNum);
                                    insertLIRL0503.put("ladingBillRemark", ladingBillRemark);
                                    insertLIRL0503.put("planNetWeight", String.valueOf(planNetWeight));
                                    insertLIRL0503.put("requireFinishDate", requireFinishDate);
                                    insertLIRL0503.put("ladingWeight", totalWeight);
                                    listBatch.add(insertLIRL0503);
                                }
                                billOrderNumSum.addAll(billOrderNum);
                            }
                        }
                    }else {
                        if (packList.size() > 0) {
                            for (HashMap hashMap1 : packList) {
                                String packId = MapUtils.getString(hashMap1, "packId", "");
                                String warehouseCode = MapUtils.getString(hashMap1, "warehouseCode", "");
                                String locationId = MapUtils.getString(hashMap1, "locationId", "");
                                String locationName = MapUtils.getString(hashMap1, "locationName", "");
                                targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                                String deliveryType = MapUtils.getString(hashMap1, "deliveryType", "");
                                String m_packId = MapUtils.getString(hashMap1, "m_packId", "");
                                String custPartId = MapUtils.getString(hashMap1, "custPartId", "");
                                String custPartName = MapUtils.getString(hashMap1, "custPartName", "");
                                String purOrderNum = MapUtils.getString(hashMap1, "orderNum", "");
                                String labelId = MapUtils.getString(hashMap1, "labelId", "");

                                //校验捆包是否已进行配单
                                HashMap queryLIRL0503 = new HashMap();
                                queryLIRL0503.put("packId", packId);
                                queryLIRL0503.put("voucherNum", MapUtils.getString(hashMap1, "ladingBillId", ""));
                                queryLIRL0503.put("segNo", segNo);
                                List<HashMap> lirl0503s = dao.query("LIRL0503.queryPackIdOne", queryLIRL0503);
                                if (lirl0503s.size() > 0) {
                                    throw new RuntimeException("该捆包已进行配单，不能再次配单!");
                                }
                                String warehouseName = "";
                                Map insertLIRL0503 = new HashMap();
                                insertLIRL0503.put("segNo", segNo);
                                insertLIRL0503.put("unitCode", segNo);
                                insertLIRL0503.put("status", 20);
                                insertLIRL0503.put("billingMethod", billingMethod);
                                insertLIRL0503.put("locationName", locationName);
                                insertLIRL0503.put("labelId", labelId);

                                //流水号
                                String[] arg = {allocateVehicleNo, "", "", ""};
                                String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);

                                insertLIRL0503.put("voucherNum", voucherNum);
                                insertLIRL0503.put("packId", packId);
                                insertLIRL0503.put("outPackFlag", "");
                                insertLIRL0503.put("warehouseCode", warehouseCode);
                                insertLIRL0503.put("deliveryType", deliveryType);
                                insertLIRL0503.put("mPackId", m_packId);
                                insertLIRL0503.put("custPartId", custPartId);
                                insertLIRL0503.put("custPartName", custPartName);
                                //查询捆包库位
                                EiInfo eiInfo = new EiInfo();
                                eiInfo.set("segNo", segNo);
                                eiInfo.set("packId", packId);
                                eiInfo.set(EiConstant.serviceId, "S_UE_WR_1002");
                                eiInfo.set("clientId", ImcGlobalUtils.CLIENT_ID);
                                eiInfo.set("clientSecret", ImcGlobalUtils.CLIENT_SECRET);
                                outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
                                String outJSON = outInfo.getString("messageBody");
                                if (EiConstant.STATUS_FAILURE == outInfo.getStatus()) {
                                    eiInfo.setMsg("查询渠道库存材料信息，原因：" + outInfo.getMsg());
                                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    return eiInfo;
                                }
                                if (StringUtils.isEmpty(outJSON)) {
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg("提单无匹配库存，不允许登记!");
                                    return outInfo;
                                }
                                //打印日志到elk
                                log(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(eiInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                                //输出到应用日志
                                System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(eiInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                                if (StringUtils.isNotEmpty(outJSON)) {
                                    Map map2 = JSONObject.fromObject(outJSON);
                                    List<Map> list1 = (List) map2.get("result");
                                    if (list1 != null && list1.size() > 0) {
                                        Map map = list1.get(0);
                                        insertLIRL0503.put("warehouseName", MapUtils.getString(map, "warehouseName", ""));
                                        insertLIRL0503.put("putinType", MapUtils.getString(map, "putinType", ""));
                                        insertLIRL0503.put("innerDiameter", MapUtils.getString(map, "innerDiameter", ""));
                                        insertLIRL0503.put("prodDensity", 0);
                                        insertLIRL0503.put("productProcessId", "");
                                        insertLIRL0503.put("netWeight", MapUtils.getString(map, "netWeight", ""));
                                        insertLIRL0503.put("customerId", MapUtils.getString(map, "customerId", ""));
                                        insertLIRL0503.put("customerName", MapUtils.getString(map, "customerName", ""));
                                        insertLIRL0503.put("matInnerId", MapUtils.getString(map, "matInnerId", ""));
                                        insertLIRL0503.put("specsDesc", MapUtils.getString(map, "specsDesc", ""));
                                        insertLIRL0503.put("piceNum", MapUtils.getString(map, "pieceNum", ""));
                                        insertLIRL0503.put("factoryOrderNum", MapUtils.getString(map, "factoryOrderNum", ""));
                                        insertLIRL0503.put("locationId", MapUtils.getString(map, "locationId", ""));
                                        insertLIRL0503.put("locationName", MapUtils.getString(map, "locationName", ""));
                                        insertLIRL0503.put("prodTypeId", StringUtils.isNotBlank(MapUtils.getString(map, "prodTypeId", "")) ? MapUtils.getString(map, "prodTypeId", "") : " ");
                                        insertLIRL0503.put("factoryArea", StringUtils.isNotBlank(MapUtils.getString(map, "factoryArea", "")) ? MapUtils.getString(map, "factoryArea", "") : "");
                                        insertLIRL0503.put("tradeCode", StringUtils.isNotBlank(MapUtils.getString(map, "tradeCode", "")) ? MapUtils.getString(map, "tradeCode", "") : " ");
                                        //查询渠道库存对应的装卸点信息
                                        //根据库位代码查询库位附属信息表获取装卸点
                                        //查询装卸点发送短信通知
                                        Map queryLIDS0601 = new HashMap();
                                        queryLIDS0601.put("segNo", segNo);
                                        queryLIDS0601.put("warehouseCode", warehouseCode);
                                        queryLIDS0601.put("locationId", locationId);
                                        queryLIDS0601.put("useStatus", 10);
                                        //查询库位附属信息表
                                        List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                        if (lids0601s.size() > 0) {
                                            LIDS0601 lids0601 = lids0601s.get(0);
                                            targetHandPointId = lids0601.getLoadingPointNo();
                                            targetHandPointIdList.add(targetHandPointId);
                                            insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                            if (StringUtils.isBlank(targetHandPointId)) {
                                                String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                inInfo.setMsg(massage);
                                                throw new RuntimeException(massage);
                                            }
                                            //判断装卸点是否启用
                                            Map queryLIRL0304 = new HashMap();
                                            queryLIRL0304.put("segNo", segNo);
                                            queryLIRL0304.put("handPointId", targetHandPointId);
                                            queryLIRL0304.put("status", "30");
                                            int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                            if (count < 0) {
                                                String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                inInfo.setMsg(massage);
                                                throw new RuntimeException(massage);
                                            }
                                        } else {
                                            String massage = "查询不到库位附属信息";
                                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            inInfo.setMsg(massage);
                                            throw new RuntimeException(massage);
                                        }
                                    } else {
                                        inInfo.setMsg("未查询到渠道库存材料信息，原因：" + outInfo.getMsg());
                                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        return inInfo;
                                    }
                                }
                                // 创建人工号
                                insertLIRL0503.put("recCreator", recCreator);
                                // 创建人姓名
                                insertLIRL0503.put("recCreatorName", recCreatorName);
                                // 创建时间
                                insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                // 修改人工号
                                insertLIRL0503.put("recRevisor", recCreator);
                                // 修改人姓名
                                insertLIRL0503.put("recRevisorName", recCreatorName);
                                // 修改时间
                                insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                // UUID
                                insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                // 删除标记: 0-新增;1-删除;
                                insertLIRL0503.put("delFlag", "0");
                                insertLIRL0503.put("archiveFlag", "0");
                                insertLIRL0503.put("tenantId", " ");
                                insertLIRL0503.put("remark", " ");
                                insertLIRL0503.put("purOrderNum", purOrderNum);
                                insertLIRL0503.put("ladingBillRemark", ladingBillRemark);
                                insertLIRL0503.put("requireFinishDate", requireFinishDate);
                                insertLIRL0503.put("ladingWeight", totalWeight);
                                listBatch.add(insertLIRL0503);
                            }
                        }
                    }
                    insertLIRL0502.put("emergencyDeliveryTime", StringUtils.isNotEmpty(emergencyDeliveryTime)?emergencyDeliveryTime:" ");
                }
                //插入子项
                if (CollectionUtils.isNotEmpty(listBatch)){
                    // 在批量插入前进行去重处理
                    List<Map> uniqueListBatch = removeDuplicateData(listBatch);
                    this.dao.insertBatch(LIRL0503.INSERT, uniqueListBatch);
                }
                if (CollectionUtils.isNotEmpty(billList)){
                    billOrderNumSum = billOrderNumSum.stream().distinct().collect(Collectors.toList());
                    billSpecsDesc = billSpecsDesc.stream().distinct().collect(Collectors.toList());
                    insertLIRL0502.put("proformaVoucherNum",String.join(",", billList));
                    insertLIRL0502.put("ladingBillRemark",String.join(",", remarkList));
                    insertLIRL0502.put("proformaOrderNum",String.join(",", billOrderNumSum));
                    insertLIRL0502.put("proformaSpecsDesc",String.join(",", billSpecsDesc));
                }

                 if (StringUtils.isNotBlank(allocateVehicleNoNew) && "10".equals(entryFlag)) {
                    dao.insert(LIRL0502.INSERT, insertLIRL0502);
                    //更新卸货单的下一级配单号
                    HashMap<String, Object> hashMap4 = new HashMap<>();
                    hashMap4.put("segNo", segNo);
                    hashMap4.put("carTraceNo", carTraceNo);
                    hashMap4.put("allocateVehicleNoNew", allocateVehicleNoNew);
                    hashMap4.put("nextAlcVehicleNo", allocateVehicleNo);
                    this.dao.update(LIRL0502.UPDATE, hashMap4);
                }else {
                     this.dao.insert(LIRL0502.INSERT, insertLIRL0502);
                }
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (RuntimeException e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    public static BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof Integer) {
            return new BigDecimal((Integer) value);
        }

        if (value instanceof Long) {
            return new BigDecimal((Long) value);
        }

        if (value instanceof Double) {
            return new BigDecimal((Double) value);
        }

        if (value instanceof Float) {
            return new BigDecimal((Float) value);
        }

        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("无法将字符串转换为BigDecimal: " + value, e);
            }
        }

        // 如果都不匹配，尝试toString()转换
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无法将对象转换为BigDecimal: " + value + " (类型: " + value.getClass().getSimpleName() + ")", e);
        }
    }


    /**
     * 移除重复数据
     * @param list 需要去重的列表
     * @return 去重后的列表
     */
    private List<Map> removeDuplicateData(List<Map> list) {
        Set<String> seen = new HashSet<>();
        List<Map> uniqueList = new ArrayList<>();
        for (Map item : list) {
            String key = item.get("voucherNum") + "_" + item.get("packId");
            if (!seen.contains(key)) {
                uniqueList.add(item);
                seen.add(key);
            }
        }
        return uniqueList;
    }


    /***
     * 根据优先级生成序号
     * @param emergencyDeliveryTimeNew
     * @param segNo
     * @return
     */
    private int getQueueNumberStr(String emergencyDeliveryTimeNew, String segNo) {
        int queueNumberStr;
        HashMap<String, Object> queryQueMap = new HashMap<>();
        queryQueMap.put("segNo", segNo);
        if (StringUtils.isNotBlank(emergencyDeliveryTimeNew)){
            //取排序表最小数
            //判断是否有多个紧急单号车辆
            List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMinQueueNumberPriorityLevel", queryQueMap);
            if (CollectionUtils.isNotEmpty(queryQueReslist)){
                HashMap queryQueRes = queryQueReslist.get(0);
//                queueNumberStr= (Integer) queryQueRes.get("minQueueNumber");
                queueNumberStr= MapUtils.getInteger(queryQueRes, "minQueueNumber");
                if (queueNumberStr==0){
                    queueNumberStr=0;
                }else {
                    queueNumberStr=queueNumberStr+1;
                }
            }else {
                queryQueReslist = dao.query("LIRL0401.queryMinQueueNumber", queryQueMap);
                HashMap queryQueRes = queryQueReslist.get(0);
                Integer queueNumberStr1= MapUtils.getInteger(queryQueRes, "minQueueNumber");
                if (queueNumberStr1==0){
                    queueNumberStr=0;
                }else {
                    queueNumberStr=queueNumberStr1-1;
                }
            }
        }else {
            List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMaxQueueNumber", queryQueMap);
            HashMap queryQueRes = queryQueReslist.get(0);
            queueNumberStr = MapUtils.getInteger(queryQueRes, "maxQueueNumber");
        }
        return queueNumberStr;
    }

    /***
     * 查询待审批的预约单
     *	S_LI_RL_0083
     */
    public EiInfo queryAppointmentsPendingApproval(EiInfo inInfo) {
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//业务单元
        String limit = (String) inInfo.get("limit");
        String offset = (String) inInfo.get("offset");
        Integer intLimit = Integer.valueOf(limit);
        Integer intOffset = Integer.valueOf(offset);
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空!");
            return inInfo;
        }
        Map queryLIRL0201 = new HashMap();
        queryLIRL0201.put("segNo", segNo);
        queryLIRL0201.put("status", 10);
        List<HashMap> vehicleList = dao.query(LIRL0201.QUERY, queryLIRL0201, intOffset, intLimit);
        inInfo.set("result", vehicleList);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        return inInfo;
    }

    /***
     * 审批预约单
     *	S_LI_RL_0084
     */
    public EiInfo approveAppointmentOrders(EiInfo inInfo) {
        List<HashMap> rusult = (List<HashMap>) inInfo.get("result");
        String recCreator = (String) inInfo.get("recCreator");
        String recCreatorName = (String) inInfo.get("recCreatorName");
        if (rusult.size() > 0) {
            for (HashMap hashMap : rusult) {
                //修改预约单为生效状态
                String reservationNumber = MapUtils.getString(hashMap, "reservationNumber", "");
                Map updateLIRL0201 = new HashMap();
                updateLIRL0201.put("segNo", MapUtils.getString(hashMap, "segNo", ""));
                updateLIRL0201.put("reservationNumber", reservationNumber);
                updateLIRL0201.put("status", 20);
                updateLIRL0201.put("delFlag", 0);
                updateLIRL0201.put("recRevisor", recCreator);
                updateLIRL0201.put("recRevisorName", recCreatorName);
                updateLIRL0201.put("recReviseTime", DateUtil.curDateTimeStr14());
                dao.update(LIRL0201.UPDATE_STATUS, hashMap);
            }
        }
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        return inInfo;
    }

    /***
     * 创建人员预约单
     *	S_LI_RL_0090
     */
    public EiInfo createAnAppointment(EiInfo inInfo) {
        List<HashMap> rusult = (List<HashMap>) inInfo.get("result");
        if (rusult.size() > 0) {
            String reservationNumber = "";
            String segNo = "";
            for (HashMap hashMap : rusult) {
                segNo = MapUtils.getString(hashMap, "segNo", "");
                String visitUnitCode = MapUtils.getString(hashMap, "visitUnitCode", "");//拜访单位代码
                String visitUnitName = MapUtils.getString(hashMap, "visitUnitName", "");//拜访单位
                String visitorName = MapUtils.getString(hashMap, "visitorName", "");//访客姓名
                String visitorTel = MapUtils.getString(hashMap, "visitorTel", "");//访客电话
                String visitorIdentity = MapUtils.getString(hashMap, "visitorIdentity", "");//访客身份证
                String visitorGender = MapUtils.getString(hashMap, "visitorGender", "");//访客性别
                String reservationDate = MapUtils.getString(hashMap, "reservationDate", "");//预约时间
                String reservationDateEnd = MapUtils.getString(hashMap, "reservationDateEnd", "");//截止时间
                String remark = MapUtils.getString(hashMap, "remark", "");//说明备注
                String recCreator = MapUtils.getString(hashMap, "recCreator", "");//创建人
                String recCreatorName = MapUtils.getString(hashMap, "recCreatorName", "");//创建人姓名
                // 解析给定的日期时间字符串
                LocalDateTime dateTime = LocalDateTime.parse(reservationDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                // 定义目标格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                // 格式化日期时间
                reservationDate = dateTime.format(formatter);
                hashMap.put("reservationDate", reservationDate);
                // 解析给定的日期时间字符串
                dateTime = LocalDateTime.parse(reservationDateEnd, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                // 定义目标格式
                formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                // 格式化日期时间
                reservationDateEnd = dateTime.format(formatter);
                hashMap.put("reservationDateEnd", reservationDateEnd);
                String strSeqTypeId = "TLIRL_SEQ0204";
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                reservationNumber = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("unitCode", segNo);
                hashMap.put("reservationNumber", reservationNumber);
                hashMap.put("uuid", StrUtil.getUUID());//UUID
                hashMap.put("status", 10);
                // 创建人工号
                hashMap.put("recCreator", recCreator);
                // 创建人姓名
                hashMap.put("recCreatorName", recCreatorName);
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", recCreator);
                // 修改人姓名
                hashMap.put("recRevisorName", recCreatorName);
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                LIRL0204 lirl0204 = new LIRL0204();
                lirl0204.fromMap(hashMap);
                dao.insert(LIRL0204.INSERT, lirl0204);
            }
            try {
                // 获取前端参数
                List<String> stringList = (List<String>) inInfo.get("fileList");
                List<String> urlList = new ArrayList<>();
                // List<String> listUrl = new ArrayList<>();
                for (int i = 0; i < stringList.size(); i++) {
                    String base64String = stringList.get(i);
                    CommonsMultipartFile file = Base64ToMultipartFileConverter.convertToMultipartFile(base64String);
                    String fileName = file.getOriginalFilename();
                    if (StrUtil.isBlank(fileName)) {
                        fileName = "";
                    }
                    // 生成文件ID
                    String fileId = StrUtil.getUUID();
                    // 获取文件后缀
                    String suffix = fileName.substring(fileName.lastIndexOf("."));
                    String downloadUrl = FileUtils.uploadFile(file);
                    if (downloadUrl == null) {
                        throw new PlatException("文件上传失败");
                    }
                    // 待新增的附件记录
                    LIRL0312 lirl0312 = new LIRL0312();
                    lirl0312.setRelevanceId(reservationNumber);
                    // 文件信息
                    lirl0312.setUploadFileName(fileName);
                    lirl0312.setFifleType(suffix);
                    lirl0312.setFifleSize(new BigDecimal(file.getSize()));
                    lirl0312.setFileId(fileId);
                    // 设置文件下载路径
                    lirl0312.setUploadFilePath(downloadUrl);
                    lirl0312.setRecCreator("System");
                    lirl0312.setRecCreatorName("System");
                    lirl0312.setRecCreateTime(DateUtil.curDateTimeStr14());
                    lirl0312.setRecRevisor("System");
                    lirl0312.setRecRevisorName("System");
                    lirl0312.setRecReviseTime(DateUtil.curDateTimeStr14());
                    lirl0312.setSegNo(segNo);
                    lirl0312.setUnitCode(segNo);
                    lirl0312.setUuid(StrUtil.getUUID());
                    lirl0312.setRelevanceType("人员预约单");
                    lirl0312.setSignatureMark("0");
                    Map insMap = lirl0312.toMap();
                    dao.insert(LIRL0312.INSERT, insMap);
                    urlList.add(downloadUrl);
                }
                inInfo.set("list", urlList);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("附件上传成功!");
            } catch (Exception e) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(e.getMessage());
            }
        }
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        return inInfo;
    }

    /***
     * 查询拜访单位信息
     *	S_LI_RL_0094
     */
    public EiInfo queryInformationByTel(EiInfo inInfo) {
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//业务单元
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空!");
            return inInfo;
        }
        Map queryLIRL0205 = new HashMap();
        queryLIRL0205.put("segNo", segNo);
        List<LIRL0205> lirl0205s = dao.query(LIRL0205.QUERY, queryLIRL0205);
        // 找到顶级部门（parentId为null的部门）
        LIRL0205 topDepartment = null;
        for (LIRL0205 dept : lirl0205s) {
            if (StringUtils.isEmpty(dept.getFvisitUnitCode())) {
                topDepartment = dept;
                break;
            }
        }
        if (topDepartment != null) {
            Map topDepartmentMap = topDepartment.toMap();
            List<Map> deptTree = getDeptTree(lirl0205s, topDepartmentMap);
            topDepartmentMap.put("eiMetadata", "");
            topDepartmentMap.put("children", deptTree);
            inInfo.set("visitUnit", topDepartmentMap);
        }
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        return inInfo;
    }

    // 将平级集合组装为层级集合的方法
    private List<Map> getDeptTree(List<LIRL0205> currEntDepts, Map department) {
        List<Map> children = new ArrayList<>();
        for (LIRL0205 dept : currEntDepts) {
            // 判断当前部门的父ID是否等于传入部门的ID，相等则为子部门
            if (StringUtils.isNotEmpty(dept.getFvisitUnitCode()) && dept.getFvisitUnitCode().equals(MapUtils.getString(department, "visitUnitCode", ""))) {
                // 递归组装子部门的子部门
                Map map = dept.toMap();
                List<Map> deptTree = getDeptTree(currEntDepts, map);
                map.put("eiMetadata", "");
                map.put("children", deptTree);
                children.add(map);
            }
        }
        department.put("children", children);
        return children;
    }

    /***
     * 配单合并
     *	S_LI_RL_0099
     */
    public EiInfo consolidationOfOrders(EiInfo inInfo) {
        List<HashMap> rusult = (List<HashMap>) inInfo.get("result");
        String segNo = "";
        List<String> allocateVehicleNoList = new ArrayList<>();
        //
        if (rusult.size() > 0) {
            for (HashMap hashMap : rusult) {
                segNo = MapUtils.getString(hashMap, "segNo", "");
                String allocateVehicleNo = MapUtils.getString(hashMap, "allocateVehicleNo", "");//配单号
                String recCreator = MapUtils.getString(hashMap, "recCreator", "");//创建人
                String recCreatorName = MapUtils.getString(hashMap, "recCreatorName", "");//创建人姓名
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo", "");//车牌号
                allocateVehicleNoList.add(allocateVehicleNo);
            }
        }
        //配单信息维护子表
        Map queryLIRL0503 = new HashMap();
        queryLIRL0503.put("segNo", segNo);
        queryLIRL0503.put("allocateVehicleNoList", allocateVehicleNoList);
        queryLIRL0503.put("status", "20");
        List<LIRL0503> lirl0503s = dao.query(LIRL0503.QUERY, queryLIRL0503);
        for (LIRL0503 lirl0503 : lirl0503s) {
            String customerId = lirl0503.getCustomerId();
        }
        //判断承运商信息是否一致
        boolean allIdsSame = checkAllCustomerIdsSame(lirl0503s);
        if (allIdsSame) {

        } else {

        }
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        return inInfo;
    }

    /***
     * 查询待审核的预约单
     *	S_LI_RL_0139
     */
    public EiInfo queryNotReviewReservationNumber(EiInfo inInfo) {
//        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//业务单元
//        String reservationNumber = (String) inInfo.get("reservationNumber");
//        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
//                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
//            inInfo.setStatus(EiConstant.STATUS_FAILURE);
//            inInfo.setMsg("账套不能为空!");
//            return inInfo;
//        }
//        Map queryReservationNumber = new HashMap();
//        queryReservationNumber.put("segNo", segNo);
//        queryReservationNumber.put("reservationNumberAbs", reservationNumber);
//        queryReservationNumber.put("status", "10");
//        List<HashMap> reservationNumberList = dao.query(LIRL0201.QUERY_HASH_MAP, queryReservationNumber);
//        inInfo.set("reservationNumberList", reservationNumberList);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        return inInfo;
    }

    /**
     * 审核预约单
     * S_LI_RL_0140
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNotReviewReservationNumber(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = (List<HashMap>) inInfo.get("result");
            String recCreator = (String) inInfo.get("recCreator");
            String recCreatorName = (String) inInfo.get("recCreatorName");
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0201> query = dao.query(LIRL0201.QUERY, hashMap);
                for (LIRL0201 lirl0201 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, ConvertUtils.convert(lirl0201.toMap(), LIRL0201.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", MesConstant.Status.K20);//审核状态
                hashMap.put("recRevisor", recCreator);
                hashMap.put("recRevisorName", recCreatorName);
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                dao.update(LIRL0201.UPDATE_STATUS, hashMap);
            }
            //inInfo = super.update(inInfo, LIRL0201.UPDATE_STATUS);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("审核预约单成功！");
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    public static boolean checkAllCustomerIdsSame(List<LIRL0503> lirl0503s) {
        if (lirl0503s == null || lirl0503s.isEmpty()) {
            return true; // 空列表认为所有元素相同
        }

        String firstCustomerId = lirl0503s.get(0).getCustomerId();
        for (int i = 1; i < lirl0503s.size(); i++) {
            String currentCustomerId = lirl0503s.get(i).getCustomerId();
            if (firstCustomerId == null) {
                if (currentCustomerId != null) {
                    return false;
                }
            } else if (!firstCustomerId.equals(currentCustomerId)) {
                return false;
            }
        }
        return true;
    }

    /***
     * 配单合并查询
     * serviced S_LI_RL_0142
     */
    public EiInfo queryMergeAllocateVehicleNo(EiInfo inInfo) {
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);
        String tel = (String) inInfo.get("driverTel");
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空!");
            return inInfo;
        }
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo", segNo);
        objectObjectHashMap.put("status", "20");
        objectObjectHashMap.put("carTraceNoNull", "10");
        objectObjectHashMap.put("allocType", "10");
        objectObjectHashMap.put("tel", tel);
        List<HashMap> queryLIRL0502 = this.dao.query(LIRL0502.QUERY_ALL, objectObjectHashMap);
        inInfo.set("list", queryLIRL0502);
        return inInfo;
    }

    /***
     * 配单合并
     *
     * serviced S_LI_RL_0141
     */
    public EiInfo addAMatchingOrderMeg(EiInfo inInfo) {
        List<HashMap> list = (List<HashMap>) inInfo.get("result");
        if (CollectionUtils.isEmpty(list)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("配单明细不能为空！");
            return inInfo;
        }
        //创建人、创建人姓名
//        String recCreator = (String) inInfo.get("recCreator");
        String recCreator = "admin";
        String recCreatorName = "admin";
        String driverName="";
        String driverTel="";
//        String recCreatorName = (String) inInfo.get("recCreatorName");
        String customerId = "";
        String vehicleNo = "";
        String segNo = "";
        int count = 0;
        List<String> allocateVehicleNoAdd = new ArrayList<>();
        String allocateVehicleNoNew = "";
        List<String> carTraceNoList = new ArrayList<>();
        try {
            for (HashMap hashMap : list) {
                String customerIdOld = MapUtils.getString(hashMap, "customerId");
                String vehicleNoOld = MapUtils.getString(hashMap, "vehicleNo");
                driverName = MapUtils.getString(hashMap, "driverName");
                driverTel = MapUtils.getString(hashMap, "driverTel");
                segNo = MapUtils.getString(hashMap, "segNo");
                String allocateVehicleNo = MapUtils.getString(hashMap, "allocateVehicleNo");
                String carTraceNo = MapUtils.getString(hashMap, "carTraceNo");
                allocateVehicleNoAdd.add(allocateVehicleNo);
                if (StringUtils.isNotBlank(customerId)) {
                    if (StringUtils.isNotBlank(vehicleNo)) {
                        if (!vehicleNo.equals(vehicleNoOld)) {
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg("该承运商下的车辆信息不一致不能合并!");
                            throw new RuntimeException("该承运商下的车辆信息不一致不能合并!");
                        }
                    }
                }
                //判断车辆是否已进厂
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("segNo",segNo);
                stringObjectHashMap.put("vehicleNo",vehicleNoOld);
                stringObjectHashMap.put("statusNo","00");
                List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, stringObjectHashMap);
                if (CollectionUtils.isNotEmpty(queryLIRL0301)){
                    String status = queryLIRL0301.get(0).getStatus();
                    String targetHandPointId = queryLIRL0301.get(0).getTargetHandPointId();
                    if ("40".equals(status)&&StringUtils.isBlank(targetHandPointId)){
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg("车辆已操作离厂,不能配单合并！");
                    }
                    carTraceNo = queryLIRL0301.get(0).getCarTraceNo();
                }
                carTraceNoList.add(carTraceNo);
                customerId = customerIdOld;
                vehicleNo = vehicleNoOld;
                //生成配单主项
                if (count == 0) {
                    //配车单流水号
                    Date date = new Date(System.currentTimeMillis());
                    String strSeqTypeId = "TMELI0502_SEQ01";
                    String[] args = {segNo.substring(0, 2), date.toString(), ""};
                    allocateVehicleNoNew = SequenceGenerator.getNextSequence(strSeqTypeId, args); //配车单流水号
                    hashMap.put("allocateVehicleNo", allocateVehicleNoNew);
                    hashMap.put("recCreator", driverTel);
                    hashMap.put("recCreatorName", driverName);
                    hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                    hashMap.put("recRevisor", driverTel);
                    hashMap.put("recRevisorName", driverName);
                    hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                    hashMap.put("allocMes", " ");
                    hashMap.put("nextAlcVehicleNo", " ");
                    hashMap.put("uuid", UUIDUtils.getUUID());
                    hashMap.put("carTraceNo", carTraceNo);
                    hashMap.put("remark", "配单合并："+DateUtil.curDateTimeStr14());
                    this.dao.insert(LIRL0502.INSERT, hashMap);
                }
                count++;
            }
//            查询当前车辆信息
            //查询配单明细进行合并
            HashMap<Object, Object> hashmap = new HashMap<>();
            hashmap.put("segNo", segNo);
            hashmap.put("allocateVehicleNoAdd", allocateVehicleNoAdd);
            hashmap.put("status", "20");
            List<LIRL0503> queryLIRL0503 = this.dao.query(LIRL0503.QUERY, hashmap);
            if (CollectionUtils.isNotEmpty(queryLIRL0503)) {
                for (LIRL0503 lirl0503 : queryLIRL0503) {
                    String targetHandPointId = lirl0503.getTargetHandPointId();
                    if (StringUtils.isBlank(targetHandPointId)){
                            segNo = lirl0503.getSegNo();//业务单元
                            String packId = lirl0503.getPackId();//捆包号
                            String matInnerId = lirl0503.getMatInnerId();//材料管理号
                            //查询捆包库位
                            if (org.apache.commons.lang3.StringUtils.isBlank(packId)){
                                continue;
                            }
                            //查询捆包库位
                            EiInfo packInfo = new EiInfo();
                            packInfo.set("segNo", segNo);
                            packInfo.set("packId", packId);
                            packInfo.set(EiConstant.serviceId, "S_UE_WR_1002");
                            packInfo.set("clientId", ImcGlobalUtils.CLIENT_ID);
                            packInfo.set("clientSecret", ImcGlobalUtils.CLIENT_SECRET);
                            EiInfo outInfo = EServiceManager.call(packInfo, TokenUtils.getXplatToken());
                            String outJSON = outInfo.getString("messageBody");
                            if (EiConstant.STATUS_FAILURE == outInfo.getStatus()) {
                                packInfo.setMsg("查询渠道库存材料信息，原因：" + outInfo.getMsg());
                                packInfo.setStatus(EiConstant.STATUS_FAILURE);
                            }
                            if (org.apache.commons.lang3.StringUtils.isEmpty(outJSON)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("车辆配单信息无匹配库存，不允许配单合并!");
                            }
                            //打印日志到elk
                            log(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + net.sf.json.JSONObject.fromObject(packInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                            //输出到应用日志
                            System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + net.sf.json.JSONObject.fromObject(inInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                            if (org.apache.commons.lang3.StringUtils.isNotEmpty(outJSON)) {
                                Map map2 = net.sf.json.JSONObject.fromObject(outJSON);
                                List<Map> list1 = (List) map2.get("result");
                                if (list1 != null && list1.size() > 0) {
                                    Map map = list1.get(0);
                                    String locationId = MapUtils.getString(map, "locationId", "");
                                    String locationName = MapUtils.getString(map, "locationName", "");
                                    String warehouseCode = MapUtils.getString(map, "warehouseCode", "");
                                    String warehouseName = MapUtils.getString(map, "warehouseName", "");
                                    //根据库位代码查询库位附属信息表获取装卸点
                                    Map queryLIDS0601 = new HashMap();
                                    queryLIDS0601.put("segNo", segNo);
                                    queryLIDS0601.put("warehouseCode", warehouseCode);
                                    queryLIDS0601.put("locationId", locationId);
                                    queryLIDS0601.put("useStatus", 10);
                                    //查询库位附属信息表
                                    List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                    if (lids0601s.size() > 0) {
                                        LIDS0601 lids0601 = lids0601s.get(0);
                                        targetHandPointId = lids0601.getLoadingPointNo();
                                        String loadingChannelNo = lids0601.getLoadingChannelNo();//装卸点通道
                                        String factoryArea = lids0601.getFactoryArea();

                                        if (org.apache.commons.lang3.StringUtils.isBlank(targetHandPointId)) {
                                            String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            outInfo.setMsg(massage);
                                            return outInfo;
                                        }
                                        //判断装卸点是否启用
                                        Map queryLIRL0304 = new HashMap();
                                        queryLIRL0304.put("segNo", segNo);
                                        queryLIRL0304.put("handPointId", targetHandPointId);
                                        queryLIRL0304.put("loadingChannelNo", loadingChannelNo);
                                        queryLIRL0304.put("status", "30");
                                        int count1 = super.count(LIRL0304.COUNT, queryLIRL0304);
                                        if (count1 <= 0) {
                                            String massage = "根据车辆配单信息未找到对应的装卸点，请及时联系仓库人员！";
                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            outInfo.setMsg(massage);
                                            return outInfo;
                                        }
                                        //回写厂区、装卸点、仓库、库位信息
                                        lirl0503.setFactoryArea(factoryArea); //厂区
                                        lirl0503.setTargetHandPointId(targetHandPointId); //装卸点
                                        lirl0503.setLocationId(locationId); //库位代码
                                        lirl0503.setLocationName(locationName); //库位名称
                                        lirl0503.setWarehouseCode(warehouseCode); //仓库代码
                                        lirl0503.setWarehouseName(warehouseName); //仓库名称
                                        lirl0503.setRecReviseTime(DateUtil.curDateTimeStr14()); //修改时间
                                        lirl0503.setRemark("配单合并："+DateUtil.curDateTimeStr14()); //修改时间
                                    } else {
                                        String massage = "查询不到库位附属信息";
                                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        outInfo.setMsg(massage);
                                    }
                                } else {
                                    packInfo.setMsg("未查询到渠道库存材料信息，原因：" + outInfo.getMsg());
                                    packInfo.setStatus(EiConstant.STATUS_FAILURE);
                                }
                            }
                    }
                    String[] arg = {allocateVehicleNoNew, "", "", ""};
                    String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                    lirl0503.setAllocVehicleSeq(tmeli0503Seq01);
                    lirl0503.setAllocateVehicleNo(allocateVehicleNoNew);
                    lirl0503.setRecCreator(driverTel);
                    lirl0503.setRecCreatorName(driverName);
                    lirl0503.setRecCreateTime(DateUtil.curDateTimeStr14());
                    lirl0503.setRecRevisor(driverTel);
                    lirl0503.setRecRevisorName(driverName);
                    lirl0503.setRecReviseTime(DateUtil.curDateTimeStr14());
                    lirl0503.setUuid(UUIDUtils.getUUID());
//                    lirl0503.setProdTypeId(StringUtils.is);
//                    lirl0503.setTradeCode(" ");
                    Map map = lirl0503.toMap();
                    this.dao.insert(LIRL0503.INSERT, map);
                }
            }

            //车辆进厂补单需要添加到新的捆包信息排队中

            //更新配单明细状态
            hashmap.put("status", "00");
            hashmap.put("delFlag", "1");
            hashmap.put("recRevisor", driverTel);
            hashmap.put("recRevisorName", driverName);
            hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());
            hashmap.put("remark", "配单合并，新配单号：" + allocateVehicleNoNew);
            hashmap.put("uuid", UUIDUtils.getUUID());
            this.dao.update(LIRL0502.UPDATE_STATUS, hashmap);
            this.dao.update(LIRL0503.UPDATE_STATUS, hashmap);
            if (CollectionUtils.isNotEmpty(carTraceNoList)){
                List<String> collect = carTraceNoList.stream().distinct().collect(Collectors.toList());
                //根据车辆跟踪号回写配单号
                HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
                stringObjectHashMap1.put("segNo",segNo);
                stringObjectHashMap1.put("carTraceNo",collect.get(0));
                stringObjectHashMap1.put("allocateVehicleNo",allocateVehicleNoNew);
                this.dao.update(LIRL0301.BACK_ALLOCATE_VEHICLE_NO,stringObjectHashMap1);

                //更新排队子表
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("segNo",segNo);
                stringObjectHashMap.put("carTraceNo",collect.get(0));
                stringObjectHashMap.put("allocateVehicleNo",allocateVehicleNoNew);
                this.dao.update(LIRL0401.UPDATE_VOUCHER_NUM,stringObjectHashMap);
            }


            //更新车辆跟踪信息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("配单合并成功！");
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 查询待审核的人员预约单
     *	S_LI_RL_0143
     */
    public EiInfo queryPersonReservationNumber(EiInfo inInfo) {
//        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//业务单元
//        String reservationNumber = (String) inInfo.get("reservationNumber");
//        String status = (String) inInfo.get("status");
//        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
//                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
//            inInfo.setStatus(EiConstant.STATUS_FAILURE);
//            inInfo.setMsg("账套不能为空!");
//            return inInfo;
//        }
//        Map queryReservationNumber = new HashMap();
//        queryReservationNumber.put("segNo", segNo);
//        queryReservationNumber.put("reservationNumber", reservationNumber);
//        queryReservationNumber.put("status", status);
//        List<HashMap> reservationNumberList = dao.query("LIRL0204.queryApp", queryReservationNumber);
//        inInfo.set("reservationNumberList", reservationNumberList);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        return inInfo;
    }

    /**
     * 审核人员预约单
     * S_LI_RL_0144
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo confirmNotReviewPersonReservationNumber(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = (List<HashMap>) inInfo.get("result");
            String recCreator = (String) inInfo.get("recCreator");
            String recCreatorName = (String) inInfo.get("recCreatorName");
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0201> query = dao.query(LIRL0201.QUERY, hashMap);
                for (LIRL0201 lirl0201 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, ConvertUtils.convert(lirl0201.toMap(), LIRL0201.class));
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", MesConstant.Status.K20);//审核状态
                hashMap.put("recRevisor", recCreator);
                hashMap.put("recRevisorName", recCreatorName);
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                hashMap.put("delFlag", 0);
                dao.update("LIRL0204.updateStatus", hashMap);
            }
            //inInfo = super.update(inInfo, LIRL0201.UPDATE_STATUS);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("审核预约单成功！");
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /***
     * 修改配单
     *
     *  S_LI_RL_0156
     * @param inInfo
     * @return
     */
    public EiInfo addAllocateVehicleItem(EiInfo inInfo) {
        try {
            EiInfo outInfo2 = new EiInfo();
            String segNo = (String) inInfo.get(MesConstant.SEG_NO);
            String allocateVehicleNo = (String) inInfo.get("allocateVehicleNo");
            String recCreator = (String) inInfo.get("recCreator");
            String recCreatorName = (String) inInfo.get("recCreatorName");
            System.out.println(recCreator + "--------" + recCreatorName);
            //判断车辆是否已经进厂
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("segNo", segNo);
            stringObjectHashMap.put("allocateVehicleNo", allocateVehicleNo);
            stringObjectHashMap.put("allocType", "10");
            List<LIRL0502> query = this.dao.query(LIRL0502.QUERY, stringObjectHashMap);
            if (CollectionUtils.isNotEmpty(query)){
                String carTraceNo = query.get(0).getCarTraceNo();
                String status = query.get(0).getStatus();
                if (StringUtils.isNotBlank(carTraceNo)){
                    outInfo2.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo2.setMsg("车辆已经进厂，不能修改配单！");
                    return outInfo2;
                }
                if (!"20".equals(status)){
                    outInfo2.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo2.setMsg("只有生效状态的配单才可以修改配单！");
                    return outInfo2;
                }
            }
            //判断配单状态
            List<HashMap> rusult = (List<HashMap>) inInfo.get("result");
            if (rusult.size() > 0) {
                for (HashMap hashMap : rusult) {
                    List<HashMap> packList = (List<HashMap>) hashMap.get("packList");
                    if (packList.size() > 0) {
                        for (HashMap hashMap1 : packList) {
                            String packId = MapUtils.getString(hashMap1, "packId", "");
                            String warehouseCode = MapUtils.getString(hashMap1, "warehouseCode", "");
                            String targetHandPointId = MapUtils.getString(hashMap1, "targetHandPointId", "");
                            String m_packId = MapUtils.getString(hashMap1, "m_packId", "");
                            String custPartId = MapUtils.getString(hashMap1, "custPartId", "");
                            String custPartName = MapUtils.getString(hashMap1, "custPartName", "");
                            String contractNum = MapUtils.getString(hashMap1, "contractNum", "");
                            String orderNum = MapUtils.getString(hashMap1, "orderNum", "");
                            //校验捆包是否已进行配单
                            HashMap queryLIRL0503 = new HashMap();
                            queryLIRL0503.put("packId", packId);
                            //queryLIRL0503.put("allocateVehicleNo", allocateVehicleNo);
                            queryLIRL0503.put("segNo", segNo);
                            List<HashMap> lirl0503s = dao.query("LIRL0503.queryPackIdOne", queryLIRL0503);
                            if (lirl0503s.size() > 0) {
                                throw new RuntimeException("该捆包已进行配单，不能再次配单!");
                            }

                            HashMap insertLIRL0503 = new HashMap();
                            insertLIRL0503.put("segNo", segNo);
                            insertLIRL0503.put("unitCode", segNo);
                            insertLIRL0503.put("status", 20);
                            insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);
                            String[] arg = {allocateVehicleNo, "", "", ""};
                            String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                            insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                            insertLIRL0503.put("voucherNum", MapUtils.getString(hashMap1, "ladingBillId", ""));
                            insertLIRL0503.put("packId", packId);
                            insertLIRL0503.put("outPackFlag", "");
                            insertLIRL0503.put("warehouseCode", warehouseCode);
                            insertLIRL0503.put("mPackId", m_packId);
                            insertLIRL0503.put("custPartId", custPartId);
                            insertLIRL0503.put("custPartName", custPartName);
                            //查询捆包库位
                            EiInfo eiInfo = new EiInfo();
                            eiInfo.set("segNo", segNo);
                            eiInfo.set("packId", packId);
                            eiInfo.set(EiConstant.serviceId, "S_UE_WR_1002");
                            eiInfo.set("clientId", ImcGlobalUtils.CLIENT_ID);
                            eiInfo.set("clientSecret", ImcGlobalUtils.CLIENT_SECRET);
                            EiInfo outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
                            String outJSON = outInfo.getString("messageBody");
                            if (EiConstant.STATUS_FAILURE == outInfo.getStatus()) {
                                eiInfo.setMsg("查询渠道库存材料信息，原因：" + outInfo.getMsg());
                                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                                return eiInfo;
                            }
                            if (StringUtils.isEmpty(outJSON)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("提单无匹配库存，不允许登记!");
                                return outInfo;
                            }
                            //打印日志到elk
                            log(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(eiInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                            //输出到应用日志
                            System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(eiInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                            if (StringUtils.isNotEmpty(outJSON)) {
                                Map map2 = JSONObject.fromObject(outJSON);
                                List<Map> list1 = (List) map2.get("result");
                                if (list1 != null && list1.size() > 0) {
                                    Map map = list1.get(0);
                                    insertLIRL0503.put("warehouseName", MapUtils.getString(map, "warehouseName", ""));
                                    insertLIRL0503.put("putinType", MapUtils.getString(map, "putinType", ""));
                                    insertLIRL0503.put("innerDiameter", MapUtils.getString(map, "innerDiameter", ""));
                                    insertLIRL0503.put("prodDensity", 0);
                                    insertLIRL0503.put("productProcessId", "");
                                    insertLIRL0503.put("netWeight", MapUtils.getString(map, "netWeight", ""));
                                    insertLIRL0503.put("customerId", MapUtils.getString(map, "customerId", ""));
                                    insertLIRL0503.put("customerName", MapUtils.getString(map, "customerName", ""));
                                    insertLIRL0503.put("matInnerId", MapUtils.getString(map, "matInnerId", ""));
                                    insertLIRL0503.put("specsDesc", MapUtils.getString(map, "specsDesc", ""));
                                    insertLIRL0503.put("piceNum", MapUtils.getString(map, "pieceNum", ""));
                                    insertLIRL0503.put("factoryOrderNum", MapUtils.getString(map, "factoryOrderNum", ""));
                                    insertLIRL0503.put("locationId", MapUtils.getString(map, "locationId", ""));
                                    insertLIRL0503.put("locationName", MapUtils.getString(map, "locationName", ""));
                                    insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                } else {
                                    inInfo.setMsg("未查询到渠道库存材料信息，原因：" + outInfo.getMsg());
                                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    return inInfo;
                                }
                            }

                            // 创建人工号
                            insertLIRL0503.put("recCreator", recCreator);
                            // 创建人姓名
                            insertLIRL0503.put("recCreatorName", recCreatorName);
                            // 创建时间
                            insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                            // 修改人工号
                            insertLIRL0503.put("recRevisor", recCreator);
                            // 修改人姓名
                            insertLIRL0503.put("recRevisorName", recCreatorName);
                            // 修改时间
                            insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                            // UUID
                            insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                            // 删除标记: 0-新增;1-删除;
                            insertLIRL0503.put("delFlag", "0");
                            insertLIRL0503.put("archiveFlag", "0");
                            insertLIRL0503.put("tenantId", " ");
                            insertLIRL0503.put("remark", " ");
                            dao.insert(LIRL0503.INSERT, insertLIRL0503);
                            System.out.println(insertLIRL0503);
                        }
                    }
                }
            }
            outInfo2.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo2.setMsg("配单成功!");
            return outInfo2;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /***
     * 针对于装货配单是形式提单无实物的需要5分钟一次推荐装卸点
     * S_LI_RL_0160
     */
    public EiInfo getLoadPoint(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String segNo = "JC000000";
        try {
            Map<String, Object> lirl0502Map = new HashMap<>();
            lirl0502Map.put("segNo", segNo);
            lirl0502Map.put("noProformaVoucherNum", segNo);
            lirl0502Map.put("status", "20");
//            lirl0502Map.put("isNotStatus", "20");
            List<LIRL0502> query = this.dao.query(LIRL0502.QUERY, lirl0502Map);
            if (CollectionUtils.isNotEmpty(query)) {
                for (LIRL0502 lirl0502 : query) {
                    List<String> orderNums = new ArrayList<>();
                    BigDecimal sumNetWeight = BigDecimal.ZERO;
                    sumNetWeight=BigDecimal.ZERO;
                    List<String> lists = new ArrayList<>(); //组装装卸点集合
                    String proformaVoucherNum = lirl0502.getProformaVoucherNum();
                    String proformaOrderNum = lirl0502.getProformaOrderNum();
                    String carTraceNo = lirl0502.getCarTraceNo();
                    String vehicleNo = lirl0502.getVehicleNo();
                    String allocateVehicleNo = lirl0502.getAllocateVehicleNo();
                    String ladingBillRemark = lirl0502.getLadingBillRemark();
                    String customerId = lirl0502.getCustomerId();
                    String customerName = lirl0502.getCustomerName();
                    String driverTel = lirl0502.getDriverTel();
                    //查询车辆是临时车还是常驻车
                    Map queryLIRL0501 = new HashMap();
                    queryLIRL0501.put("status", "20");
                    queryLIRL0501.put("vehicleNo", vehicleNo);
                    queryLIRL0501.put("delFlag", 0);
                    queryLIRL0501.put("vehicleType","1");
                    List<LIRL0501> lirl0501s = dao.query(LIRL0501.QUERY, queryLIRL0501);
                    if (proformaVoucherNum.contains(",")){
                        //拆分
                        String[] split = proformaVoucherNum.split(",");
                        String[] splitOrderNum = proformaOrderNum.split(",");
                        int sum=0;
                        for (String voucherNum : split) {
                            if (StringUtils.isNotEmpty(voucherNum)) {
                                String[] split1 = ladingBillRemark.split(";");
                                String ladingRemark = split1[sum];
                                if (StringUtils.isNotBlank(carTraceNo)) {


                                    Map insertLIRL0503 = new HashMap();
                                    insertLIRL0503.put("segNo", segNo);
                                    insertLIRL0503.put("unitCode", segNo);
                                    insertLIRL0503.put("status", 20);
                                    insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);
                                    //调用提单服务查询捆包明细推荐装卸点
                                    //调物流服务拿到查询提货单重量件数信息

                                    //删除单条明细
                                    HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                                    stringObjectHashMap.put("segNo", segNo);
                                    stringObjectHashMap.put("voucherNum", voucherNum);
                                    stringObjectHashMap.put("status", "20");
                                    stringObjectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                                    this.dao.update(LIRL0503.UPDATE_BILL_STATUS, stringObjectHashMap);

                                    EiInfo eiInfo1 = new EiInfo();
                                    eiInfo1.set("segNo", segNo);
                                    List<String> ladingBillIdList = new ArrayList<>();
                                    ladingBillIdList.add(voucherNum);
                                    eiInfo1.set("ladingBillIdList", ladingBillIdList);
                                    eiInfo1.set("warehouseCode", "*********");
                                    eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                                    //调post请求
                                    EiInfo info= EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                                    Map attr = info.getAttr();
                                    Map result = MapUtils.getMap(attr, "result");
                                    List<Map> packList = (List) result.get("packList");
                                    if (CollectionUtils.isNotEmpty(packList)) {
                                        for (Map map2 : packList) {

                                        //装卸点、装卸点名、厂区编码、厂区名称
                                        String packId = MapUtils.getString(map2, "packId", "");//捆包号
                                        String matInnerId = MapUtils.getString(map2, "matInnerId", "");//材料管理号
                                        String locationId = MapUtils.getString(map2, "locationId", "");//库位代码
                                        String specsDesc = MapUtils.getString(map2, "specsDesc", "");
                                        String prodTypeId = MapUtils.getString(map2, "prodTypeId", "");
                                        String warehouseCode = MapUtils.getString(map2, "warehouseCode", "");//仓库代码
                                        String custPartId = MapUtils.getString(map2, "custPartId", "");//客户零件号
                                        String custPartName = MapUtils.getString(map2, "custPartName", "");//客户零件名称
                                        String m_packId = MapUtils.getString(map2, "mPackId", "");//母卷号
                                        String qualityGrade = MapUtils.getString(map2, "qualityGrade", "");//质量等级
                                        String netWeight = MapUtils.getString(map2, "netWeight", "");//质量等级
                                        String orderNum = MapUtils.getString(map2, "orderNum", "");//质量等级

                                        //判断此捆包是否已经增加过了
                                        HashMap<String, Object> lirl0503Map = new HashMap<>();
                                        lirl0503Map.put("segNo", segNo);
                                        lirl0503Map.put("packId", packId);
                                        lirl0503Map.put("voucherNum", voucherNum);
                                        lirl0503Map.put("allocateVehicleNo", allocateVehicleNo);
                                        lirl0503Map.put("status", "20");
                                        List<LIRL0503> query1 = this.dao.query(LIRL0503.QUERY, lirl0503Map);
                                        if (CollectionUtils.isNotEmpty(query1)) {
                                            continue;
                                        }
                                            //如果是同一个规格进行补捆包
                                            for (String orderNumNew : splitOrderNum) {
                                                if (!orderNumNew.equals(orderNum)) {
                                                    continue;
                                                } else {
                                                    //根据库位代码查询库位附属信息表获取装卸点
                                                    Map queryLIDS0601 = new HashMap();
                                                    queryLIDS0601.put("segNo", segNo);
                                                    queryLIDS0601.put("warehouseCode", warehouseCode);
                                                    queryLIDS0601.put("locationId", locationId);
                                                    queryLIDS0601.put("useStatus", 10);
                                                    String targetHandPointId = "";
                                                    //查询库位附属信息表
                                                    List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                                    if (lids0601s.size() > 0) {
                                                        LIDS0601 lids0601 = lids0601s.get(0);
                                                        targetHandPointId = lids0601.getLoadingPointNo();
                                                        if (StringUtils.isBlank(targetHandPointId)) {
                                                            String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                            outInfo.setMsg(massage);
                                                        }
                                                        //判断装卸点是否启用
                                                        Map queryLIRL0304 = new HashMap();
                                                        queryLIRL0304.put("segNo", segNo);
                                                        queryLIRL0304.put("handPointId", targetHandPointId);
                                                        queryLIRL0304.put("status", "30");
                                                        int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                                        if (count < 0) {
                                                            String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                            outInfo.setMsg(massage);
                                                            return outInfo;
                                                        }
                                                        if (lirl0501s.size() <= 0) {
                                                            HashMap<String, Object> hashMapLirl0401 = new HashMap<>();
                                                            hashMapLirl0401.put("segNo", segNo);
                                                            hashMapLirl0401.put("targetHandPointId", targetHandPointId);
                                                            hashMapLirl0401.put("carTraceNo", carTraceNo);
                                                            hashMapLirl0401.put("vehicleNo", vehicleNo);
                                                            List<LIRL0401> lirl0401s = this.dao.query(LIRL0401.QUERY_HAND_POINT_INFO, hashMapLirl0401);
                                                            if (CollectionUtils.isEmpty(lirl0401s)&&!"4".equals(qualityGrade)) {
                                                                HashMap<String, Object> hashMapLirl0402 = new HashMap<>();
                                                                //取排序表最大数
                                                                HashMap queryQueMap = new HashMap();
                                                                queryQueMap.put("segNo", segNo);
                                                                List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMaxQueueNumber", queryQueMap);
                                                                HashMap queryQueRes = queryQueReslist.get(0);
                                                                String queueNumberStr = String.valueOf(queryQueRes.get("maxQueueNumber"));
                                                                Integer queueNumber = Integer.valueOf(queueNumberStr);
                                                                //插入叫号表
                                                                hashMapLirl0402.put("uuid", UUIDUtils.getUUID());
                                                                hashMapLirl0402.put("segNo", segNo);
                                                                hashMapLirl0402.put("unitCode", segNo);
                                                                hashMapLirl0402.put("queueNumber", queueNumber);
                                                                hashMapLirl0402.put("carTraceNo", carTraceNo);
                                                                hashMapLirl0402.put("vehicleNo", vehicleNo);
                                                                hashMapLirl0402.put("voucherNum", " ");
                                                                hashMapLirl0402.put("priorityLevel", "99");
                                                                hashMapLirl0402.put("queueDate", DateUtil.curDateTimeStr14());
                                                                hashMapLirl0402.put("targetHandPointId", targetHandPointId);
                                                                hashMapLirl0402.put("factoryArea", " ");
                                                                hashMapLirl0402.put("remark", DateUtil.curDateTimeStr14().concat(":排队叫号"));
                                                                hashMapLirl0402.put("sysRemark", DateUtil.curDateTimeStr14().concat(":排队叫号"));
                                                                hashMapLirl0402.put("recCreator", "admin");
                                                                hashMapLirl0402.put("recCreatorName", "admin");
                                                                hashMapLirl0402.put("recCreateTime", DateUtil.curDateTimeStr14());
                                                                hashMapLirl0402.put("recRevisor", "admin");
                                                                hashMapLirl0402.put("recRevisorName", "admin");
                                                                hashMapLirl0402.put("recReviseTime", DateUtil.curDateTimeStr14());
                                                                hashMapLirl0402.put("archiveFlag", "0");
                                                                hashMapLirl0402.put("tenantId", " ");
                                                                hashMapLirl0402.put("delFlag", "0");
                                                                //判断车辆是否被叫号
                                                                //判断已叫号车辆不允许再次启动排队
                                                                if (!verfiyData(inInfo, segNo, vehicleNo, carTraceNo, allocateVehicleNo)) {
                                                                    this.dao.insert(LIRL0401.INSERT, hashMapLirl0402);
                                                                }
                                                                lists.add(targetHandPointId);
                                                            }
                                                        }
                                                    }
                                                    String[] arg = {allocateVehicleNo, "", "", ""};
                                                    String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                                    insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                                    //插入配单明细
                                                    insertLIRL0503.put("voucherNum", voucherNum);
                                                    insertLIRL0503.put("packId", packId);
                                                    insertLIRL0503.put("outPackFlag", "");
                                                    insertLIRL0503.put("warehouseCode", warehouseCode);
                                                    insertLIRL0503.put("deliveryType", "20");
                                                    insertLIRL0503.put("warehouseName", MapUtils.getString(map2, "warehouseName", ""));
                                                    insertLIRL0503.put("putinType", MapUtils.getString(map2, "putinType", ""));
                                                    insertLIRL0503.put("productProcessId", "");
                                                    insertLIRL0503.put("netWeight", MapUtils.getString(map2, "netWeight", ""));
                                                    insertLIRL0503.put("customerId", customerId);
                                                    insertLIRL0503.put("customerName", customerName);
                                                    insertLIRL0503.put("matInnerId", MapUtils.getString(map2, "matInnerId", ""));
                                                    insertLIRL0503.put("specsDesc", MapUtils.getString(map2, "specsDesc", ""));
                                                    insertLIRL0503.put("piceNum", MapUtils.getString(map2, "pieceNum", ""));
                                                    insertLIRL0503.put("factoryOrderNum", MapUtils.getString(map2, "factoryOrderNum", ""));
                                                    insertLIRL0503.put("locationId", MapUtils.getString(map2, "locationId", ""));
                                                    insertLIRL0503.put("locationName", MapUtils.getString(map2, "locationName", ""));
                                                    // 创建人工号
                                                    insertLIRL0503.put("recCreator", "system");
                                                    // 创建人姓名
                                                    insertLIRL0503.put("recCreatorName", "system");
                                                    // 创建时间
                                                    insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                                    // 修改人工号
                                                    insertLIRL0503.put("recRevisor", "system");
                                                    // 修改人姓名
                                                    insertLIRL0503.put("recRevisorName", "system");
                                                    // 修改时间
                                                    insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                                    // UUID
                                                    insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                                    // 删除标记: 0-新增;1-删除;
                                                    insertLIRL0503.put("delFlag", "0");
                                                    insertLIRL0503.put("archiveFlag", "0");
                                                    insertLIRL0503.put("tenantId", " ");
                                                    insertLIRL0503.put("remark", "形式提单定时任务自动补充配单明细:" + DateUtil.curDateTimeStr14());
                                                    insertLIRL0503.put("custPartName", custPartName);
                                                    insertLIRL0503.put("custPartId", custPartId);
                                                    insertLIRL0503.put("mPackId", m_packId);
                                                    insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                                    insertLIRL0503.put("innerDiameter", MapUtils.getDouble(map2, "innerDiameter", 0d));
                                                    insertLIRL0503.put("prodDensity", 0);
                                                    insertLIRL0503.put("ladingBillRemark", ladingRemark);
                                                    insertLIRL0503.put("billingMethod", "20");
                                                    dao.insert(LIRL0503.INSERT, insertLIRL0503);

                                                    //删除单条明细
                                                    HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
                                                    stringObjectHashMap1.put("segNo", segNo);
                                                    stringObjectHashMap1.put("voucherNum", proformaVoucherNum);
                                                    stringObjectHashMap1.put("allocateVehicleNo", allocateVehicleNo);
                                                    stringObjectHashMap1.put("status", "20");
                                                    this.dao.update(LIRL0503.UPDATE_BILL_STATUS, stringObjectHashMap1);
                                                    sumNetWeight=  sumNetWeight.add(new BigDecimal(netWeight));
                                                    orderNums.add(orderNumNew);
                                                }
                                            }
                                        }
                                    }
                                    //查询车辆跟踪数据获取手机号
                                    HashMap<Object, Object> objectHashMap = new HashMap<>();
                                    objectHashMap.put("segNo", segNo);
                                    objectHashMap.put("carTraceNo", carTraceNo);
                                    objectHashMap.put("vehicleNo", vehicleNo);
                                    List<LIRL0301> lirl0301List = this.dao.query(LIRL0301.QUERY, objectHashMap);
                                    if (CollectionUtils.isNotEmpty(lirl0301List) && CollectionUtils.isNotEmpty(lists)) {
                                        List<String> handList = lists.stream().distinct().collect(Collectors.toList());
                                        String handPointName = "";
                                        //根据车辆跟踪号、车牌号、账套查询司机信息
                                        HashMap<String, Object> hashMap = new HashMap<>();
                                        hashMap.put("segNo", segNo);
                                        hashMap.put("handPointIdList", handList);
                                        //查询装卸点名称
                                        List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                                        if (queryLIRL0304.size() > 0) {
                                            handPointName = queryLIRL0304.stream().map(LIRL0304::getHandPointName).collect(Collectors.joining(","));
                                        }
                                        //通知短信
                                        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                                        objectObjectHashMap.put("param1", lirl0301List.get(0).getTelNum());
                                        objectObjectHashMap.put("param2", lirl0301List.get(0).getDriverName());
                                        objectObjectHashMap.put("param3", vehicleNo);
                                        objectObjectHashMap.put("param4", handPointName);
                                        MessageUtils.sendMessage(objectObjectHashMap, "MT0000001016");
                                    }
                                    //更新数据
                                } else {

                                    Map insertLIRL0503 = new HashMap();
                                    insertLIRL0503.put("segNo", segNo);
                                    insertLIRL0503.put("unitCode", segNo);
                                    insertLIRL0503.put("status", 20);
                                    insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);
                                    //调用提单服务查询捆包明细推荐装卸点
                                    //调物流服务拿到查询提货单重量件数信息
                                    EiInfo eiInfo1 = new EiInfo();
                                    eiInfo1.set("segNo", segNo);
                                    List<String> ladingBillIdList = new ArrayList<>();
                                    ladingBillIdList.add(voucherNum);
                                    eiInfo1.set("ladingBillIdList", ladingBillIdList);
                                    eiInfo1.set("warehouseCode", "*********");
                                    eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                                    //调post请求
                                    EiInfo info = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                                    Map attr = info.getAttr();
                                    Map result = MapUtils.getMap(attr, "result");
                                    List<Map> packList = (List) result.get("packList");
                                    if (CollectionUtils.isNotEmpty(packList)) {
                                        for (Map map2 : packList) {
                                            //装卸点、装卸点名、厂区编码、厂区名称
                                            String packId = MapUtils.getString(map2, "packId", "");//捆包号
                                            String matInnerId = MapUtils.getString(map2, "matInnerId", "");//材料管理号
                                            String locationId = MapUtils.getString(map2, "locationId", "");//库位代码
                                            String specsDesc = MapUtils.getString(map2, "specsDesc", "");
                                            String prodTypeId = MapUtils.getString(map2, "prodTypeId", "");
                                            String warehouseCode = MapUtils.getString(map2, "warehouseCode", "");//仓库代码
                                            String custPartId = MapUtils.getString(map2, "custPartId", "");//客户零件号
                                            String custPartName = MapUtils.getString(map2, "custPartName", "");//客户零件名称
                                            String m_packId = MapUtils.getString(map2, "mPackId", "");//母卷号
                                            String qualityGrade = MapUtils.getString(map2, "qualityGrade", "");//母卷号
                                            String netWeight = MapUtils.getString(map2, "netWeight", "");//质量等级
                                            String orderNum = MapUtils.getString(map2, "orderNum", "");//质量等级
                                            //判断此捆包是否已经增加过了
                                            HashMap<String, Object> lirl0503Map = new HashMap<>();
                                            lirl0503Map.put("segNo", segNo);
                                            lirl0503Map.put("packId", packId);
                                            lirl0503Map.put("voucherNum", voucherNum);
                                            lirl0503Map.put("allocateVehicleNo", allocateVehicleNo);
                                            lirl0503Map.put("status", "20");
                                            List<LIRL0503> query1 = this.dao.query(LIRL0503.QUERY, lirl0503Map);
                                            if (CollectionUtils.isNotEmpty(query1)) {
                                                continue;
                                            }
                                            for (String s : splitOrderNum) {
                                                if (s.equals(orderNum)){
                                                    //根据库位代码查询库位附属信息表获取装卸点
                                                    Map queryLIDS0601 = new HashMap();
                                                    queryLIDS0601.put("segNo", segNo);
                                                    queryLIDS0601.put("warehouseCode", warehouseCode);
                                                    queryLIDS0601.put("locationId", locationId);
                                                    queryLIDS0601.put("useStatus", 10);
                                                    String targetHandPointId = "";
                                                    //查询库位附属信息表
                                                    List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                                    if (lids0601s.size() > 0) {
                                                        targetHandPointId = getTargetHandPointId(lids0601s, outInfo, segNo);
                                                        //插入配单明细
                                                    }
                                                    String[] arg = {allocateVehicleNo, "", "", ""};
                                                    String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                                    insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                                    insert0503(voucherNum, map2, insertLIRL0503, packId, warehouseCode, customerId, customerName, custPartName, custPartId, m_packId, targetHandPointId, ladingRemark);

                                                    //删除单条明细
                                                    HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                                                    stringObjectHashMap.put("segNo", segNo);
                                                    stringObjectHashMap.put("voucherNum", voucherNum);
                                                    stringObjectHashMap.put("status", "20");
                                                    stringObjectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                                                    this.dao.update(LIRL0503.UPDATE_BILL_STATUS, stringObjectHashMap);

                                                    sumNetWeight = sumNetWeight.add(new BigDecimal(netWeight));
                                                    orderNums.add(s);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }else {
                        if (StringUtils.isNotEmpty(proformaVoucherNum)) {
                            if (StringUtils.isNotBlank(carTraceNo)) {

                                Map insertLIRL0503 = new HashMap();
                                insertLIRL0503.put("segNo", segNo);
                                insertLIRL0503.put("unitCode", segNo);
                                insertLIRL0503.put("status", 20);
                                insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);

                                //调用提单服务查询捆包明细推荐装卸点
                                //调物流服务拿到查询提货单重量件数信息

                                EiInfo eiInfo1 = new EiInfo();
                                eiInfo1.set("segNo", segNo);
                                List<String> ladingBillIdList = new ArrayList<>();
                                ladingBillIdList.add(proformaVoucherNum);
                                eiInfo1.set("ladingBillIdList", ladingBillIdList);
                                eiInfo1.set("warehouseCode", "*********");
                                eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                                //调post请求
                                EiInfo info = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                                Map attr = info.getAttr();
                                Map result = MapUtils.getMap(attr, "result");
                                List<Map> packList = (List) result.get("packList");
                                if (CollectionUtils.isNotEmpty(packList)) {
                                    for (Map map2 : packList) {

                                        //装卸点、装卸点名、厂区编码、厂区名称
                                        String packId = MapUtils.getString(map2, "packId", "");//捆包号
                                        String matInnerId = MapUtils.getString(map2, "matInnerId", "");//材料管理号
                                        String locationId = MapUtils.getString(map2, "locationId", "");//库位代码
                                        String specsDesc = MapUtils.getString(map2, "specsDesc", "");
                                        String prodTypeId = MapUtils.getString(map2, "prodTypeId", "");
                                        String warehouseCode = MapUtils.getString(map2, "warehouseCode", "");//仓库代码
                                        String custPartId = MapUtils.getString(map2, "custPartId", "");//客户零件号
                                        String custPartName = MapUtils.getString(map2, "custPartName", "");//客户零件名称
                                        String m_packId = MapUtils.getString(map2, "mPackId", "");//母卷号
                                        String qualityGrade = MapUtils.getString(map2, "qualityGrade", "");//母卷号
                                        String netWeight = MapUtils.getString(map2, "netWeight", "");//质量等级
                                        String orderNum = MapUtils.getString(map2, "orderNum", "");//质量等级
                                        //判断此捆包是否已经增加过了
                                        HashMap<String, Object> lirl0503Map = new HashMap<>();
                                        lirl0503Map.put("segNo", segNo);
                                        lirl0503Map.put("packId", packId);
                                        lirl0503Map.put("voucherNum", proformaVoucherNum);
                                        lirl0503Map.put("allocateVehicleNo", allocateVehicleNo);
                                        lirl0503Map.put("status", "20");
                                        List<LIRL0503> query1 = this.dao.query(LIRL0503.QUERY, lirl0503Map);
                                        if (CollectionUtils.isNotEmpty(query1)) {
                                            continue;
                                        }

                                        String[] splitOrderNum = proformaOrderNum.split(",");
                                        for (String orderNumNew : splitOrderNum) {
                                            if (!orderNumNew.equals(orderNum)){
                                                continue;
                                            }
                                            //根据库位代码查询库位附属信息表获取装卸点
                                            Map queryLIDS0601 = new HashMap();
                                            queryLIDS0601.put("segNo", segNo);
                                            queryLIDS0601.put("warehouseCode", warehouseCode);
                                            queryLIDS0601.put("locationId", locationId);
                                            queryLIDS0601.put("useStatus", 10);
                                            String targetHandPointId = "";
                                            //查询库位附属信息表
                                            List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                            if (lids0601s.size() > 0) {
                                                LIDS0601 lids0601 = lids0601s.get(0);
                                                targetHandPointId = lids0601.getLoadingPointNo();
                                                if (StringUtils.isBlank(targetHandPointId)) {
                                                    String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                    outInfo.setMsg(massage);
                                                }
                                                //判断装卸点是否启用
                                                Map queryLIRL0304 = new HashMap();
                                                queryLIRL0304.put("segNo", segNo);
                                                queryLIRL0304.put("handPointId", targetHandPointId);
                                                queryLIRL0304.put("status", "30");
                                                int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                                if (count < 0) {
                                                    String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                    outInfo.setMsg(massage);
                                                }
                                                if (lirl0501s.size() <= 0) {
                                                    HashMap<String, Object> hashMapLirl0401 = new HashMap<>();
                                                    hashMapLirl0401.put("segNo", segNo);
                                                    hashMapLirl0401.put("targetHandPointId", targetHandPointId);
                                                    hashMapLirl0401.put("carTraceNo", carTraceNo);
                                                    hashMapLirl0401.put("vehicleNo", vehicleNo);
                                                    List<LIRL0401> lirl0401s = this.dao.query(LIRL0401.QUERY_HAND_POINT_INFO, hashMapLirl0401);
                                                    if (CollectionUtils.isEmpty(lirl0401s)&&!"4".equals(qualityGrade)) {

                                                        HashMap<String, Object> hashMapLirl0402 = new HashMap<>();
                                                        //取排序表最大数
                                                        HashMap queryQueMap = new HashMap();
                                                        queryQueMap.put("segNo", segNo);
                                                        List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMaxQueueNumber", queryQueMap);
                                                        HashMap queryQueRes = queryQueReslist.get(0);
                                                        String queueNumberStr = String.valueOf(queryQueRes.get("maxQueueNumber"));
                                                        Integer queueNumber = Integer.valueOf(queueNumberStr);
                                                        //插入叫号表
                                                        hashMapLirl0402.put("uuid", UUIDUtils.getUUID());
                                                        hashMapLirl0402.put("segNo", segNo);
                                                        hashMapLirl0402.put("unitCode", segNo);
                                                        hashMapLirl0402.put("queueNumber", queueNumber);
                                                        hashMapLirl0402.put("carTraceNo", carTraceNo);
                                                        hashMapLirl0402.put("vehicleNo", vehicleNo);
                                                        hashMapLirl0402.put("voucherNum", " ");
                                                        hashMapLirl0402.put("priorityLevel", "99");
                                                        hashMapLirl0402.put("queueDate", DateUtil.curDateTimeStr14());
                                                        hashMapLirl0402.put("targetHandPointId", targetHandPointId);
                                                        hashMapLirl0402.put("factoryArea", " ");
                                                        hashMapLirl0402.put("remark", DateUtil.curDateTimeStr14().concat(":排队叫号"));
                                                        hashMapLirl0402.put("sysRemark", DateUtil.curDateTimeStr14().concat(":排队叫号"));
                                                        hashMapLirl0402.put("recCreator", "admin");
                                                        hashMapLirl0402.put("recCreatorName", "admin");
                                                        hashMapLirl0402.put("recCreateTime", DateUtil.curDateTimeStr14());
                                                        hashMapLirl0402.put("recRevisor", "admin");
                                                        hashMapLirl0402.put("recRevisorName", "admin");
                                                        hashMapLirl0402.put("recReviseTime", DateUtil.curDateTimeStr14());
                                                        hashMapLirl0402.put("archiveFlag", "0");
                                                        hashMapLirl0402.put("tenantId", " ");
                                                        hashMapLirl0402.put("delFlag", "0");
                                                        if (!verfiyData(inInfo, segNo, vehicleNo, carTraceNo, allocateVehicleNo)){
                                                            this.dao.insert(LIRL0401.INSERT, hashMapLirl0402);
                                                        }
                                                        lists.add(targetHandPointId);
                                                    }
                                                }
                                            }
                                            String[] arg = {allocateVehicleNo, "", "", ""};
                                            String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                            insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                            //插入配单明细
                                            insertLIRL0503.put("voucherNum", proformaVoucherNum);
                                            insertLIRL0503.put("packId", packId);
                                            insertLIRL0503.put("outPackFlag", "");
                                            insertLIRL0503.put("warehouseCode", warehouseCode);
                                            insertLIRL0503.put("deliveryType", "20");
                                            insertLIRL0503.put("warehouseName", MapUtils.getString(map2, "warehouseName", ""));
                                            insertLIRL0503.put("putinType", MapUtils.getString(map2, "putinType", ""));
                                            insertLIRL0503.put("productProcessId", "");
                                            insertLIRL0503.put("netWeight", MapUtils.getString(map2, "netWeight", ""));
                                            insertLIRL0503.put("customerId", customerId);
                                            insertLIRL0503.put("customerName", customerName);
                                            insertLIRL0503.put("matInnerId", MapUtils.getString(map2, "matInnerId", ""));
                                            insertLIRL0503.put("specsDesc", MapUtils.getString(map2, "specsDesc", ""));
                                            insertLIRL0503.put("piceNum", MapUtils.getString(map2, "pieceNum", ""));
                                            insertLIRL0503.put("factoryOrderNum", MapUtils.getString(map2, "factoryOrderNum", ""));
                                            insertLIRL0503.put("locationId", MapUtils.getString(map2, "locationId", ""));
                                            insertLIRL0503.put("locationName", MapUtils.getString(map2, "locationName", ""));
                                            // 创建人工号
                                            insertLIRL0503.put("recCreator", "system");
                                            // 创建人姓名
                                            insertLIRL0503.put("recCreatorName", "system");
                                            // 创建时间
                                            insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                            // 修改人工号
                                            insertLIRL0503.put("recRevisor", "system");
                                            // 修改人姓名
                                            insertLIRL0503.put("recRevisorName", "system");
                                            // 修改时间
                                            insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                            // UUID
                                            insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                            // 删除标记: 0-新增;1-删除;
                                            insertLIRL0503.put("delFlag", "0");
                                            insertLIRL0503.put("archiveFlag", "0");
                                            insertLIRL0503.put("tenantId", " ");
                                            insertLIRL0503.put("remark", "形式提单定时任务自动补充配单明细:" + DateUtil.curDateTimeStr14());
                                            insertLIRL0503.put("custPartName", custPartName);
                                            insertLIRL0503.put("custPartId", custPartId);
                                            insertLIRL0503.put("mPackId", m_packId);
                                            insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                            insertLIRL0503.put("innerDiameter", MapUtils.getDouble(map2, "innerDiameter", 0d));
                                            insertLIRL0503.put("prodDensity", 0);
                                            insertLIRL0503.put("ladingBillRemark", ladingBillRemark);
                                            insertLIRL0503.put("billingMethod", "20");
                                            dao.insert(LIRL0503.INSERT, insertLIRL0503);

                                            sumNetWeight= sumNetWeight.add(new BigDecimal(netWeight));
                                            orderNums.add(orderNumNew);
                                            //删除单条明细
                                            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                                            stringObjectHashMap.put("segNo", segNo);
                                            stringObjectHashMap.put("voucherNum", proformaVoucherNum);
                                            stringObjectHashMap.put("status", "20");
                                            stringObjectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                                            this.dao.update(LIRL0503.UPDATE_BILL_STATUS, stringObjectHashMap);
                                        }

                                    }
                                }

                                //查询车辆跟踪数据获取手机号
                                HashMap<Object, Object> objectHashMap = new HashMap<>();
                                objectHashMap.put("segNo", segNo);
                                objectHashMap.put("carTraceNo", carTraceNo);
                                objectHashMap.put("vehicleNo", vehicleNo);
                                List<LIRL0301> lirl0301List = this.dao.query(LIRL0301.QUERY, objectHashMap);
                                if (CollectionUtils.isNotEmpty(lirl0301List) && CollectionUtils.isNotEmpty(lists)) {
                                    List<String> handList = lists.stream().distinct().collect(Collectors.toList());
                                    String handPointName = "";
                                    //根据车辆跟踪号、车牌号、账套查询司机信息
                                    HashMap<String, Object> hashMap = new HashMap<>();
                                    hashMap.put("segNo", segNo);
                                    hashMap.put("handPointIdList", handList);
                                    //查询装卸点名称
                                    List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap);
                                    if (queryLIRL0304.size() > 0) {
                                        handPointName = queryLIRL0304.stream().map(LIRL0304::getHandPointName).collect(Collectors.joining(","));
                                    }
                                    //通知短信
                                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                                    objectObjectHashMap.put("param1", lirl0301List.get(0).getTelNum());
                                    objectObjectHashMap.put("param2", lirl0301List.get(0).getDriverName());
                                    objectObjectHashMap.put("param3", vehicleNo);
                                    objectObjectHashMap.put("param4", handPointName);
                                    MessageUtils.sendMessage(objectObjectHashMap, "MT0000001016");
                                }
                                //更新数据
                            } else {

                                Map insertLIRL0503 = new HashMap();
                                insertLIRL0503.put("segNo", segNo);
                                insertLIRL0503.put("unitCode", segNo);
                                insertLIRL0503.put("status", 20);
                                insertLIRL0503.put("allocateVehicleNo", allocateVehicleNo);

                                //调用提单服务查询捆包明细推荐装卸点
                                //调物流服务拿到查询提货单重量件数信息
                                EiInfo eiInfo1 = new EiInfo();
                                eiInfo1.set("segNo", segNo);
                                List<String> ladingBillIdList = new ArrayList<>();
                                ladingBillIdList.add(proformaVoucherNum);
                                eiInfo1.set("ladingBillIdList", ladingBillIdList);
                                eiInfo1.set("warehouseCode", "*********");
                                eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                                //调post请求
                                EiInfo info = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                                Map attr = info.getAttr();
                                Map result = MapUtils.getMap(attr, "result");
                                List<Map> packList = (List) result.get("packList");
                                if (CollectionUtils.isNotEmpty(packList)) {
                                    for (Map map2 : packList) {

                                        //装卸点、装卸点名、厂区编码、厂区名称
                                        String packId = MapUtils.getString(map2, "packId", "");//捆包号
                                        String matInnerId = MapUtils.getString(map2, "matInnerId", "");//材料管理号
                                        String locationId = MapUtils.getString(map2, "locationId", "");//库位代码
                                        String specsDesc = MapUtils.getString(map2, "specsDesc", "");
                                        String prodTypeId = MapUtils.getString(map2, "prodTypeId", "");
                                        String warehouseCode = MapUtils.getString(map2, "warehouseCode", "");//仓库代码
                                        String custPartId = MapUtils.getString(map2, "custPartId", "");//客户零件号
                                        String custPartName = MapUtils.getString(map2, "custPartName", "");//客户零件名称
                                        String m_packId = MapUtils.getString(map2, "mPackId", "");//母卷号
                                        String netWeight = MapUtils.getString(map2, "netWeight", "");//质量等级
                                        String orderNum = MapUtils.getString(map2, "orderNum", "");//质量等级
                                        //判断此捆包是否已经增加过了
                                        HashMap<String, Object> lirl0503Map = new HashMap<>();
                                        lirl0503Map.put("segNo", segNo);
                                        lirl0503Map.put("packId", packId);
                                        lirl0503Map.put("voucherNum", proformaVoucherNum);
                                        lirl0503Map.put("allocateVehicleNo", allocateVehicleNo);
                                        lirl0503Map.put("status", "20");
                                        List<LIRL0503> query1 = this.dao.query(LIRL0503.QUERY, lirl0503Map);
                                        if (CollectionUtils.isNotEmpty(query1)) {
                                            continue;
                                        }
                                        String[] splitOrderNum = proformaOrderNum.split(",");
                                        for (String orderNumNew : splitOrderNum) {
                                            if (!orderNumNew.equals(orderNum)){
                                                continue;
                                            }
                                            //根据库位代码查询库位附属信息表获取装卸点
                                            Map queryLIDS0601 = new HashMap();
                                            queryLIDS0601.put("segNo", segNo);
                                            queryLIDS0601.put("warehouseCode", warehouseCode);
                                            queryLIDS0601.put("locationId", locationId);
                                            queryLIDS0601.put("useStatus", 10);
                                            String targetHandPointId = "";
                                            //查询库位附属信息表
                                            List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                            if (lids0601s.size() > 0) {
                                                LIDS0601 lids0601 = lids0601s.get(0);
                                                targetHandPointId = lids0601.getLoadingPointNo();
                                                if (StringUtils.isBlank(targetHandPointId)) {
                                                    String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                    outInfo.setMsg(massage);
                                                }
                                                //判断装卸点是否启用
                                                Map queryLIRL0304 = new HashMap();
                                                queryLIRL0304.put("segNo", segNo);
                                                queryLIRL0304.put("handPointId", targetHandPointId);
                                                queryLIRL0304.put("status", "30");
                                                int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                                if (count < 0) {
                                                    String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                    outInfo.setMsg(massage);
                                                }
                                                String[] arg = {allocateVehicleNo, "", "", ""};
                                                String tmeli0503Seq01 = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);
                                                insertLIRL0503.put("allocVehicleSeq", tmeli0503Seq01);
                                                //插入配单明细
                                                insertLIRL0503.put("voucherNum", proformaVoucherNum);
                                                insertLIRL0503.put("packId", packId);
                                                insertLIRL0503.put("outPackFlag", "");
                                                insertLIRL0503.put("warehouseCode", warehouseCode);
                                                insertLIRL0503.put("deliveryType", "20");
                                                insertLIRL0503.put("warehouseName", MapUtils.getString(map2, "warehouseName", ""));
                                                insertLIRL0503.put("putinType", MapUtils.getString(map2, "putinType", ""));
                                                insertLIRL0503.put("productProcessId", "");
                                                insertLIRL0503.put("netWeight", MapUtils.getString(map2, "netWeight", ""));
                                                insertLIRL0503.put("customerId", customerId);
                                                insertLIRL0503.put("customerName", customerName);
                                                insertLIRL0503.put("matInnerId", MapUtils.getString(map2, "matInnerId", ""));
                                                insertLIRL0503.put("specsDesc", MapUtils.getString(map2, "specsDesc", ""));
                                                insertLIRL0503.put("piceNum", MapUtils.getString(map2, "pieceNum", ""));
                                                insertLIRL0503.put("factoryOrderNum", MapUtils.getString(map2, "factoryOrderNum", ""));
                                                insertLIRL0503.put("locationId", MapUtils.getString(map2, "locationId", ""));
                                                insertLIRL0503.put("locationName", MapUtils.getString(map2, "locationName", ""));
                                                // 创建人工号
                                                insertLIRL0503.put("recCreator", "system");
                                                // 创建人姓名
                                                insertLIRL0503.put("recCreatorName", "system");
                                                // 创建时间
                                                insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
                                                // 修改人工号
                                                insertLIRL0503.put("recRevisor", "system");
                                                // 修改人姓名
                                                insertLIRL0503.put("recRevisorName", "system");
                                                // 修改时间
                                                insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
                                                // UUID
                                                insertLIRL0503.put("uuid", UUIDUtils.getUUID());
                                                // 删除标记: 0-新增;1-删除;
                                                insertLIRL0503.put("delFlag", "0");
                                                insertLIRL0503.put("archiveFlag", "0");
                                                insertLIRL0503.put("tenantId", " ");
                                                insertLIRL0503.put("remark", "形式提单定时任务自动补充配单明细:" + DateUtil.curDateTimeStr14());
                                                insertLIRL0503.put("custPartName", custPartName);
                                                insertLIRL0503.put("custPartId", custPartId);
                                                insertLIRL0503.put("mPackId", m_packId);
                                                insertLIRL0503.put("targetHandPointId", targetHandPointId);
                                                insertLIRL0503.put("innerDiameter", MapUtils.getDouble(map2, "innerDiameter", 0d));
                                                insertLIRL0503.put("prodDensity", 0);
                                                insertLIRL0503.put("ladingBillRemark", ladingBillRemark);
                                                insertLIRL0503.put("billingMethod", "20");
                                                dao.insert(LIRL0503.INSERT, insertLIRL0503);

                                                //删除单条明细
                                                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                                                stringObjectHashMap.put("segNo", segNo);
                                                stringObjectHashMap.put("voucherNum", proformaVoucherNum);
                                                stringObjectHashMap.put("status", "20");
                                                stringObjectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                                                this.dao.update(LIRL0503.UPDATE_BILL_STATUS, stringObjectHashMap);
                                                sumNetWeight= sumNetWeight.add(new BigDecimal(netWeight));
                                                orderNums.add(orderNumNew);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //形式提单补捆包时，当库存量达到计划量80%，发送短信给司机，提醒内容改成
                    //尊敬的司机师傅您好：
                    //形式提单bl111计划量为【XX】，当前已产生库存【XX】，请您根据实际情况自行决定是否启动排队。感谢配合！
                    //查询提单总量
                    HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                    stringObjectHashMap.put("segNo", segNo);
                    stringObjectHashMap.put("voucherNum", proformaVoucherNum);
                    stringObjectHashMap.put("allocateVehicleNo", allocateVehicleNo);
                    stringObjectHashMap.put("purOrderNum", orderNums.stream().distinct().collect(Collectors.toList()));
                    stringObjectHashMap.put("packId", "");
                    List<String> queryLIRL0503 = this.dao.query(LIRL0503.QUERY_SUM_WEIGHT, stringObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0503)){
                            String netWeight = queryLIRL0503.get(0);
                            BigDecimal netWeightP = new BigDecimal(netWeight);
                            //当库存量达到计划量80%
                            if (netWeightP != null && sumNetWeight != null) {
                                BigDecimal eightyPercent = netWeightP.multiply(new BigDecimal("0.8"));
                                boolean isOver = sumNetWeight.compareTo(eightyPercent) > 0;
                                // 处理逻辑
                                if (isOver) {
                                    //发送短信
                                    //查询车辆跟踪数据获取手机号
                                    HashMap<Object, Object> objectHashMap = new HashMap<>();
                                    objectHashMap.put("segNo", segNo);
                                    objectHashMap.put("carTraceNo", carTraceNo);
                                    objectHashMap.put("vehicleNo", vehicleNo);
                                    List<LIRL0301> lirl0301List = this.dao.query(LIRL0301.QUERY, objectHashMap);
                                    if (CollectionUtils.isNotEmpty(lirl0301List)) {
                                        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                                        objectObjectHashMap.put("param1", StringUtils.isBlank(lirl0301List.get(0).getTelNum())?lirl0301List.get(0).getTelNum():driverTel);
                                        objectObjectHashMap.put("param2", proformaVoucherNum);
                                        objectObjectHashMap.put("param3",netWeightP);
                                        objectObjectHashMap.put("param4",sumNetWeight.setScale(3, RoundingMode.HALF_UP));
                                        MessageUtils.sendMessage(objectObjectHashMap, "MT0000001027");
                                    }else {
                                        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                                        objectObjectHashMap.put("param1", driverTel);
                                        objectObjectHashMap.put("param2", proformaVoucherNum);
                                        objectObjectHashMap.put("param3",netWeightP);
                                        objectObjectHashMap.put("param4",sumNetWeight.setScale(3, RoundingMode.HALF_UP));
                                        MessageUtils.sendMessage(objectObjectHashMap, "MT0000001027");
                                    }
                                }
                            }
                    }
                    sumNetWeight.multiply(new BigDecimal("0"));
                }
            }
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    private boolean verfiyData(EiInfo inInfo, String segNo, String vehicleNo, String carTraceNo, String allocateVehicleNo) {
        Map<String, String> hashMapLirl0302 = new HashMap<>();
        hashMapLirl0302.put("segNo", segNo);
        hashMapLirl0302.put("vehicleNo", vehicleNo);
        //查询车辆跟踪信息
        hashMapLirl0302.put("carTraceNo", carTraceNo);
        hashMapLirl0302.put("voucherNum", allocateVehicleNo);
        List<LIRL0402> queryLIRL0402 = this.dao.query(LIRL0402.QUERY, hashMapLirl0302);
        if (CollectionUtils.isNotEmpty(queryLIRL0402)){
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("车辆:"+ vehicleNo +"已被叫号！");
            return true;
        }

        //车辆是否是正在作业
        Map<String, String> hashMapLirl0301 = new HashMap<>();
        hashMapLirl0301.put("segNo", segNo);
        hashMapLirl0301.put("vehicleNo", vehicleNo);
        hashMapLirl0301.put("carTraceNo", carTraceNo);
        List<LIRL0301> lirl0301 = this.dao.query(LIRL0301.QUERY, hashMapLirl0301);
        if (CollectionUtils.isNotEmpty(lirl0301)){
            String status = lirl0301.get(0).getStatus();
            if ("30".equals(status)){
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("车辆:"+ vehicleNo +"车辆正在作业！");
                return true;
            }
        }
        return false;
    }

    private void insert0503(String voucherNum, Map map2, Map insertLIRL0503, String packId, String warehouseCode, String customerId, String customerName, String custPartName, String custPartId, String m_packId, String targetHandPointId, String ladingRemark) {

        insertLIRL0503.put("voucherNum", voucherNum);
        insertLIRL0503.put("packId", packId);
        insertLIRL0503.put("outPackFlag", "");
        insertLIRL0503.put("warehouseCode", warehouseCode);
        insertLIRL0503.put("deliveryType", "20");
        insertLIRL0503.put("warehouseName", MapUtils.getString(map2, "warehouseName", ""));
        insertLIRL0503.put("putinType", MapUtils.getString(map2, "putinType", ""));
        insertLIRL0503.put("productProcessId", "");
        insertLIRL0503.put("netWeight", MapUtils.getString(map2, "netWeight", ""));
        insertLIRL0503.put("customerId", customerId);
        insertLIRL0503.put("customerName", customerName);
        insertLIRL0503.put("matInnerId", MapUtils.getString(map2, "matInnerId", ""));
        insertLIRL0503.put("specsDesc", MapUtils.getString(map2, "specsDesc", ""));
        insertLIRL0503.put("piceNum", MapUtils.getString(map2, "pieceNum", ""));
        insertLIRL0503.put("factoryOrderNum", MapUtils.getString(map2, "factoryOrderNum", ""));
        insertLIRL0503.put("locationId", MapUtils.getString(map2, "locationId", ""));
        insertLIRL0503.put("locationName", MapUtils.getString(map2, "locationName", ""));
        // 创建人工号
        insertLIRL0503.put("recCreator", "system");
        // 创建人姓名
        insertLIRL0503.put("recCreatorName", "system");
        // 创建时间
        insertLIRL0503.put("recCreateTime", DateUtil.curDateTimeStr14());
        // 修改人工号
        insertLIRL0503.put("recRevisor", "system");
        // 修改人姓名
        insertLIRL0503.put("recRevisorName", "system");
        // 修改时间
        insertLIRL0503.put("recReviseTime", DateUtil.curDateTimeStr14());
        // UUID
        insertLIRL0503.put("uuid", UUIDUtils.getUUID());
        // 删除标记: 0-新增;1-删除;
        insertLIRL0503.put("delFlag", "0");
        insertLIRL0503.put("archiveFlag", "0");
        insertLIRL0503.put("tenantId", " ");
        insertLIRL0503.put("remark", "形式提单定时任务自动补充配单明细:" + DateUtil.curDateTimeStr14());
        insertLIRL0503.put("custPartName", custPartName);
        insertLIRL0503.put("custPartId", custPartId);
        insertLIRL0503.put("mPackId", m_packId);
        insertLIRL0503.put("targetHandPointId", targetHandPointId);
        insertLIRL0503.put("innerDiameter", MapUtils.getDouble(map2, "innerDiameter", 0d));
        insertLIRL0503.put("prodDensity", 0);
        insertLIRL0503.put("ladingBillRemark", ladingRemark);
        insertLIRL0503.put("billingMethod", "20");
        dao.insert(LIRL0503.INSERT, insertLIRL0503);
    }

    private String getTargetHandPointId(List<LIDS0601> lids0601s, EiInfo outInfo, String segNo) {
        String targetHandPointId;
        LIDS0601 lids0601 = lids0601s.get(0);
        targetHandPointId = lids0601.getLoadingPointNo();
        if (StringUtils.isBlank(targetHandPointId)) {
            String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(massage);
        }
        //判断装卸点是否启用
        Map queryLIRL0304 = new HashMap();
        queryLIRL0304.put("segNo", segNo);
        queryLIRL0304.put("handPointId", targetHandPointId);
        queryLIRL0304.put("status", "30");
        int count = super.count(LIRL0304.COUNT, queryLIRL0304);
        if (count < 0) {
            String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(massage);
        }
        return targetHandPointId;
    }

    /***
     * 装货配单，紧急交货配单根据紧急交货时间和紧急预警时间作比较
     *
     */
    public EiInfo queryEmergencyInfo(EiInfo inInfo) {
        try {
            String segNo = "JC000000";
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);

            List<HashMap> lirl0502List = this.dao.query(LIRL0502.QUERY_EMERGENCY_INFO, hashMap);
            if (CollectionUtils.isNotEmpty(lirl0502List)) {

                // 获取当前时间
                String currentTime = DateUtil.curDateTimeStr14();

                for (HashMap lirl0502 : lirl0502List) {
                    String carTraceNo = (String) lirl0502.get("carTraceNo");
                    String emergencyDeliveryTime = (String) lirl0502.get("emergencyDeliveryTime");
                    String customerId = (String) lirl0502.get("customerId");
                    Long packMin = (Long) lirl0502.get("packMin");//装货捆包总时长
                    Integer turnaroundTime=0;
                    String locationNames = (String) lirl0502.get("locationNames");//装货捆包总时长
                    if (StringUtils.isNotBlank(locationNames)) {
                        if (locationNames.contains(",")) {
                            String[] split = locationNames.split(",");
                            List<String> list = new ArrayList<>(Arrays.asList(split));
                            //根据库位查询厂区
                            HashMap<Object, Object> hashMap1 = new HashMap<>();
                            hashMap1.put("segNo", segNo);
                            hashMap1.put("locationNameList", list);
                            hashMap1.put("status", "10");
                            List<LIDS0601> query = this.dao.query(LIDS0601.QUERY, hashMap1);
                            if (CollectionUtils.isNotEmpty(query)) {
                                List<String> collect = query.stream().distinct().map(LIDS0601::getFactoryBuilding).collect(Collectors.toList());
                                if (collect.size() == 2) {
                                    turnaroundTime = 10;
                                } else if (collect.size() == 3) {
                                    turnaroundTime = 20;
                                } else if (collect.size() == 4) {
                                    turnaroundTime = 30;
                                }
                            }
                        }
                    }
                    //预警时间分钟
                    Integer warningTime= 120+30+packMin.intValue()+30+turnaroundTime;
//                    Integer warningTime= 203;
                    String warningTimeStr = String.valueOf(warningTime);

                    // 根据emergencyDeliveryTime紧急交货时间和当前时间比对是否达到warningTime预警条件
                    if (StringUtils.isNotBlank(emergencyDeliveryTime) && StringUtils.isNotBlank(warningTimeStr)) {

                        // 计算紧急交货时间与当前时间的差值（分钟）
                        long timeDifferenceMinutes = calculateTimeDifference(currentTime, emergencyDeliveryTime);

                        // 将预警时间转换为分钟数（假设warningTime格式为小时，如"2"表示2小时）
                        int warningTimeMinutes = Integer.parseInt(warningTimeStr);

                        // 判断是否达到预警条件
                        if (timeDifferenceMinutes <= warningTimeMinutes&&timeDifferenceMinutes > 0&&timeDifferenceMinutes ==(warningTimeMinutes-2)) {
                            // 达到预警条件，执行预警处理
                            processEmergencyWarning(lirl0502, timeDifferenceMinutes, warningTimeMinutes,emergencyDeliveryTime);

                            logger.info("紧急交货预警: 车辆跟踪号={}, 客户={}, 剩余时间={}分钟, 预警时间={}分钟",
                                    carTraceNo, customerId, timeDifferenceMinutes, warningTimeMinutes);
                        }

                    }
                }
            }

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("紧急交货信息检查完成");

        } catch (Exception e) {
            logger.error("查询紧急交货信息异常", e);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询紧急交货信息失败: " + e.getMessage());
        }

        return inInfo;
    }

    /**
     * 计算两个时间之间的差值（分钟）
     *
     * @param currentTime 当前时间（格式：yyyyMMddHHmmss）
     * @param targetTime 目标时间（格式：yyyyMMddHHmmss）
     * @return 时间差值（分钟），正数表示目标时间在当前时间之后，负数表示已过期
     */
    private long calculateTimeDifference(String currentTime, String targetTime) {
        try {
            // 确保时间格式正确
            if (currentTime.length() != 14 || targetTime.length() != 14) {
                throw new IllegalArgumentException("时间格式错误，应为yyyyMMddHHmmss格式");
            }

            // 使用SimpleDateFormat解析时间
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
            java.util.Date current = sdf.parse(currentTime);
            java.util.Date target = sdf.parse(targetTime);

            // 计算时间差（毫秒）
            long diffMillis =   target.getTime()-current.getTime();

            // 转换为分钟
            return diffMillis / (1000 * 60);

        } catch (Exception e) {
            logger.error("时间差值计算异常: currentTime={}, targetTime={}", currentTime, targetTime, e);
            return 0;
        }
    }

    /**
     * 处理紧急交货预警
     *
     * @param lirl0502              配单信息
     * @param remainingMinutes      剩余分钟数
     * @param warningMinutes        预警分钟数
     * @param emergencyDeliveryTime
     */
    private void processEmergencyWarning(HashMap lirl0502, long remainingMinutes, int warningMinutes, String emergencyDeliveryTime) {
        try {
            String carTraceNo = (String) lirl0502.get("carTraceNo");
            String vehicleNo = (String) lirl0502.get("vehicleNo");
            String customerName = (String) lirl0502.get("customerId");
            String voucherNums = (String) lirl0502.get("voucherNums");//提单
            String specsDescs = (String) lirl0502.get("specsDescs");//规格
            String segNo = (String) lirl0502.get("segNo");
            String driverTel = (String) lirl0502.get("recCreator");
            String driverName = (String) lirl0502.get("recCreatorName");
            // 发送预警短信给司机
            HashMap<Object, Object> smsParams = new HashMap<>();
            smsParams.put("param1", driverTel);
            smsParams.put("param2", vehicleNo);
            smsParams.put("param3", voucherNums);
            smsParams.put("param4", specsDescs);
            smsParams.put("param5", DateTimeUtil.convertToYearMonthDayHour(emergencyDeliveryTime.substring(0,emergencyDeliveryTime.length()-2)));

            // 调用短信发送服务（需要根据实际的短信模板ID）
            MessageUtils.sendMessage(smsParams, "MT0000001021"); // 紧急交货预警模板

            logger.info("已发送紧急交货预警短信: 司机={}, 车牌={}, 剩余时间={}分钟",
                    driverName, vehicleNo, remainingMinutes);

        } catch (Exception e) {
            logger.error("处理紧急交货预警异常: carTraceNo={}", lirl0502.get("carTraceNo"), e);
        }
    }

    /**
     * 处理紧急交货超时
     *
     * @param lirl0502 配单信息
     * @param overtimeMinutes 超时分钟数
     */
    private void processEmergencyOvertime(LIRL0502 lirl0502, long overtimeMinutes) {
        try {
            String carTraceNo = lirl0502.getCarTraceNo();
            String vehicleNo = lirl0502.getVehicleNo();
            String customerName = lirl0502.getCustomerName();

            // 查询车辆和司机信息
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("segNo", lirl0502.getSegNo());
            queryMap.put("carTraceNo", carTraceNo);
            queryMap.put("vehicleNo", vehicleNo);

            List<LIRL0301> vehicleList = this.dao.query(LIRL0301.QUERY, queryMap);

            if (CollectionUtils.isNotEmpty(vehicleList)) {
                LIRL0301 vehicleInfo = vehicleList.get(0);
                String driverName = vehicleInfo.getDriverName();
                String driverTel = vehicleInfo.getTelNum();

                // 发送超时通知短信
                HashMap<Object, Object> smsParams = new HashMap<>();
                smsParams.put("param1", driverTel);
                smsParams.put("param2", driverName);
                smsParams.put("param3", vehicleNo);
                smsParams.put("param4", customerName);
                smsParams.put("param5", String.valueOf(overtimeMinutes));

                // 调用短信发送服务
                MessageUtils.sendMessage(smsParams, "MT0000001014"); // 紧急交货超时模板

                logger.warn("已发送紧急交货超时通知: 司机={}, 车牌={}, 超时={}分钟",
                        driverName, vehicleNo, overtimeMinutes);
            }

            // 可以在这里添加其他超时处理逻辑，如：
            // 1. 标记配单为超时状态
            // 2. 通知管理人员
            // 3. 自动调整优先级等

        } catch (Exception e) {
            logger.error("处理紧急交货超时异常: carTraceNo={}", lirl0502.getCarTraceNo(), e);
        }
    }


    /***
     * 查询装货配单的提单信息
     *	S_LI_RL_0075
     */
    public EiInfo queryLoadingWithBillOfLadingNEW(EiInfo inInfo) {

        //司机身份 区分客户与承运商
        String reservationIdentity = (String) inInfo.get("reservationIdentity");
        //返回参数列表
        List<Map> outRusultList = new ArrayList<>();

        EiInfo eiInfoParam = checkEiinfoData(inInfo);
        //分页参数
        Integer limit = (Integer) inInfo.get("limit");
        Integer offset = (Integer) inInfo.get("offset");
        //调用开单中心
        callOpenLadingBill(eiInfoParam);

        //如果是承运商身份则调用物流中心
        if ("20".equals(reservationIdentity)) {
            callLogisticsCenter(inInfo);
        }


        Map conditionInfo = new HashMap<>();
        conditionInfo.put("limit",limit);
        conditionInfo.put("offset",offset);
        conditionInfo.put("segNo",(String) inInfo.get(MesConstant.SEG_NO));
        List<Map> result = this.dao.query("tlirl0599.querLadingBillId", conditionInfo);
        //进行数据拆分
        Map LadingBillMap = getLadingBillInfo(result);
        //形式提单
        List<Map> formLadingBills = (List<Map>) LadingBillMap.get("formLadingBillInfo");
        //普通提单
        List<Map> ladingBills = (List<Map>) LadingBillMap.get("ladingBillInfo");

        //处理普通提单数据
        for(Map ladingBill : ladingBills){

        }


        /*if (CollectionUtils.isNotEmpty(rusult)) {
            for (HashMap hashMapRusult : rusult) {
                String deliveryType = MapUtils.getString(hashMapRusult, "deliveryType", "");//交货方式（10：自提、20：代运）
                //提单创建人
                String recCreator = MapUtils.getString(hashMapRusult, "recCreator", "");//交货方式（10：自提、20：代运）
                String userNum = MapUtils.getString(hashMapRusult, "userNum", "");
                String userName = MapUtils.getString(hashMapRusult, "userName", "");
                String billingMethod = MapUtils.getString(hashMapRusult, "billingMethod", "");//开单方式 10-按捆包 20-按重量 30-按件数
                String ladingBillId = MapUtils.getString(hashMapRusult, "ladingBillId", "");//提单号
                String ladingSpotId = MapUtils.getString(hashMapRusult, "ladingSpotId", "");//始发站(/仓库)代码
                String ladingSpotName = MapUtils.getString(hashMapRusult, "ladingSpotName", "");//始发站(/仓库)名称
                String destSpotId = MapUtils.getString(hashMapRusult, "destSpotId", "");//终到站(/仓库)代码
                String destSpotName = MapUtils.getString(hashMapRusult, "destSpotName", "");//终到站(/仓库)名称
                String totalWeight = MapUtils.getString(hashMapRusult, "totalWeight", "");//总重量
                String totalPackQty = MapUtils.getString(hashMapRusult, "totalPackQty", "");//捆包总数量

                String emergencyDeliveryTime = MapUtils.getString(hashMapRusult, "emergencyDeliveryTime", "");//紧急交货时间
                ladingSpotAddr = MapUtils.getString(hashMapRusult, "ladingSpotAddr", "");//紧急交货时间
                String remark = MapUtils.getString(hashMapRusult, "remark", "");//提单备注
                String orderNum = MapUtils.getString(hashMapRusult, "orderNum", "");//提单备注
                HashMap outRusult = new HashMap();
                outRusult.put("ladingBillId", ladingBillId);
                outRusult.put("ladingSpotName", ladingSpotName);
                outRusult.put("destSpotName", destSpotName);
                outRusult.put("totalWeight", totalWeight);
                outRusult.put("totalPackQty", totalPackQty);
                outRusult.put("billingMethod", billingMethod);
                outRusult.put("deliveryType", deliveryType);
                outRusult.put("emergencyDeliveryTime", emergencyDeliveryTime);
                outRusult.put("segNo", segNo);
                outRusult.put("ladingSpotAddr", ladingSpotAddr);
                outRusult.put("ladingBillRemark", remark);
                //按捆包普通提单 按量形式提单
                if (!"10".equals(billingMethod)) {

                    //调物流服务拿到查询提货单重量件数信息
                    eiInfo1 = new EiInfo();
                    eiInfo1.set("segNo", segNo);
                    eiInfo1.set("warehouseCode", ladingSpotId);
                    List<String> ladingBillIdList = new ArrayList<>();
                    ladingBillIdList.add(ladingBillId);
                    eiInfo1.set("ladingBillIdList", ladingBillIdList);
                    Boolean isRefreshToken = Boolean.FALSE;
                    String tokenRefresh = "";
                    if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
                        isRefreshToken = Boolean.TRUE;
                        tokenRefresh = outInfo.get("accessToken").toString();
                    }
                    eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                    //调post请求
                    outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                    Map attr = outInfo.getAttr();
                    Map result = MapUtils.getMap(attr, "result");
                    List<Map> packList = (List) result.get("packList");
                    //去重
                    List<Map> packListDis = new ArrayList<>();

                    for (Map packListDi : packList) {
                        String packId = MapUtils.getString(packListDi, "packId", "");
                        String matInnerId = MapUtils.getString(packListDi, "matInnerId", "");
                        String netWeight = MapUtils.getString(packListDi, "netWeight", "");
                        packListDi.put("ladingBillMark",remark);
                        BigDecimal bigDecimalN = new BigDecimal(netWeight);
                        bigDecimalAdd = bigDecimalAdd.add(bigDecimalN);
                        Map queryLIRL0503 = new HashMap();
                        queryLIRL0503.put("packId", packId);
                        queryLIRL0503.put("matInnerId", matInnerId);
                        queryLIRL0503.put("voucherNum", ladingBillId);
                        queryLIRL0503.put("statusSX", "10");
                        //查询配车单子表已有的数据
                        List<LIRL0503> lirl0503s = dao.query(LIRL0503.QUERY, queryLIRL0503);
                        if (lirl0503s.size() < 0) {
                            packListDis.add(packListDi);
                        }
                    }
                    outRusult.put("packList", packListDis);
                } else {
                    List<HashMap> hashMap2List = new ArrayList<>();
                    for (HashMap hashMap2 : result2) {
                        String retrunStatus = MapUtils.getString(hashMap2, "returnStatus", "");//库位代码
                        if ("Y".equals(retrunStatus)) {
                            //已出库的
                            continue;
                        }
                        String ladingBillId2 = MapUtils.getString(hashMap2, "ladingBillId", "");
                        hashMap2.put("ladingBillRemark",  remark);
                        if (ladingBillId.equals(ladingBillId2)) {
                            String packId = MapUtils.getString(hashMap2, "packId", "");
                            String matInnerId = MapUtils.getString(hashMap2, "matInnerId", "");
                            Map queryLIRL0503 = new HashMap();
                            queryLIRL0503.put("packId", packId);
                            queryLIRL0503.put("matInnerId", matInnerId);
                            queryLIRL0503.put("voucherNum", ladingBillId);
                            queryLIRL0503.put("statusSX", "10");
                            //查询配车单子表已有的数据
                            List<LIRL0503> lirl0503s = dao.query(LIRL0503.QUERY, queryLIRL0503);
                            if (lirl0503s.size() <= 0) {
                                hashMap2List.add(hashMap2);
                            }
                        }
                    }
                    outRusult.put("packList", hashMap2List);
                }
                outRusultList.add(outRusult);
            }
        }*/

        /*if ("20".equals(reservationIdentity)) {
            String customerIdP = customerId.get(0);
            //查询转库单
            EiInfo eiInfo2 = new EiInfo();
            eiInfo2.set("segNo", segNo);
//            eiInfo2.set("transBillType", "10");
            eiInfo2.set("packPutOutFlag", "0");
            eiInfo2.set("tproviderId", customerIdP);
            eiInfo2.set("deliveryType", "20");
            if (StringUtils.isNotBlank(ladingBillIdEq)) {
                eiInfo2.set("transBillId", ladingBillIdEq);
            }
            eiInfo2.set(EiConstant.serviceId, "S_UC_EP_0044");
            //调post请求
            outInfo = EServiceManager.call(eiInfo2, TokenUtils.getXplatToken());
            List<HashMap> rusultZK = (List<HashMap>) outInfo.get("result");
            if (CollectionUtils.isNotEmpty(rusultZK)) {
                for (HashMap hashMap45 : rusultZK) {
                    String ladingSpotName = MapUtils.getString(hashMap45, "ladingSpotName", "");//捆包号
                    String destSpotName = MapUtils.getString(hashMap45, "destSpotName", "");//捆包号
                    String sumNetWeight = MapUtils.getString(hashMap45, "sumNetWeight", "");//捆包号
                    String sumNtotalQty = MapUtils.getString(hashMap45, "sumNtotalQty", "");//捆包号
                    String transBillId = MapUtils.getString(hashMap45, "transBillId", "");//捆包号
                    String deliveryType = MapUtils.getString(hashMap45, "deliveryType", "");//捆包号
                    HashMap outRusult = new HashMap();
                    outRusult.put("ladingBillId", transBillId);
                    outRusult.put("ladingSpotName", ladingSpotName);
                    outRusult.put("destSpotName", destSpotName);
                    outRusult.put("totalWeight", sumNetWeight);
                    outRusult.put("totalPackQty", sumNtotalQty);
                    outRusult.put("deliveryType", deliveryType);
                    outRusult.put("segNo", segNo);
                    //查询转库单
                    EiInfo eiInfo3 = new EiInfo();
                    eiInfo3.set("segNo", segNo);
                    eiInfo3.set("segNo", transBillId);
                    eiInfo3.set("transBillType", "10");
                    eiInfo3.set("packPutOutFlag", "0");
                    eiInfo3.set(EiConstant.serviceId, "S_UC_EP_0045");
                    //调post请求
                    outInfo = EServiceManager.call(eiInfo3, TokenUtils.getXplatToken());
                    if (outInfo.getStatus() == -1) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("调用物流中心返回报错:" + outInfo.getMsg());
                        throw new RuntimeException(outInfo.getMsg());
                    }
                    List<HashMap> rusult1 = (List<HashMap>) outInfo.get("result");
                    if (CollectionUtils.isEmpty(rusult1) && rusult.size() <= 0) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("未匹配到转库单信息!");
                        return outInfo;
                    }
                    List<HashMap> hashMap2List = new ArrayList<>();
                    if (rusult1 != null && rusult1.size() > 0) {
                        for (HashMap hashMap : rusult1) {
                            String packId = MapUtils.getString(hashMap, "packId", "");
                            String matInnerId = MapUtils.getString(hashMap, "matInnerId", "");
                            Map queryLIRL0503 = new HashMap();
                            queryLIRL0503.put("packId", packId);
                            queryLIRL0503.put("matInnerId", matInnerId);
                            queryLIRL0503.put("voucherNum", transBillId);
                            queryLIRL0503.put("statusSX", "10");
                            //查询配车单子表已有的数据
                            List<LIRL0503> lirl0503s = dao.query(LIRL0503.QUERY, queryLIRL0503);
                            if (lirl0503s.size() <= 0) {
                                hashMap2List.add(hashMap);
                            }
                        }
                        outRusult.put("packList", hashMap2List);
                    }
                    outRusultList.add(outRusult);
                }
            }
        }*/
        //已经添加装车配单的数据不再显示出来
        /*List<HashMap> outRusultList2 = new ArrayList<>();
        for (HashMap hashMap : outRusultList) {
            String ladingBillId = MapUtils.getString(hashMap, "ladingBillId", "");
            List<HashMap> packList = (List<HashMap>) hashMap.get("packList");
            String billMethod = (String) hashMap.get("billingMethod");
            if (packList.size() > 0) {
                List<HashMap> packList2 = new ArrayList<>();
                for (HashMap hashMap1 : packList) {
                    String packId = MapUtils.getString(hashMap1, "packId", "");
                    String matInnerId = MapUtils.getString(hashMap1, "matInnerId", "");
                    Map queryLIRL0503 = new HashMap();
                    queryLIRL0503.put("packId", packId);
                    queryLIRL0503.put("matInnerId", matInnerId);
                    queryLIRL0503.put("voucherNum", ladingBillId);
                    queryLIRL0503.put("statusSX", "10");
                    //查询配车单子表已有的数据
                    List<LIRL0503> lirl0503s = dao.query(LIRL0503.QUERY, queryLIRL0503);
                    if (lirl0503s.size() > 0) {
                        System.out.println(lirl0503s.toString());
                    } else {
                        packList2.add(hashMap1);
                    }
                }
                if (packList2.size() > 0) {
                    hashMap.put("packList", packList2);
                    outRusultList2.add(hashMap);
                }
            } else if (!"10".equals(billMethod)) {
//                Map queryLIRL0503 = new HashMap();
//                queryLIRL0503.put("proformaVoucherNum", ladingBillId);
//                queryLIRL0503.put("segNo", segNo);
//                queryLIRL0503.put("statusIn", "20");
//                //查询配车单子表已有的数据
//                List<LIRL0502> lirl0503s = dao.query(LIRL0502.QUERY, queryLIRL0503);
//                if (lirl0503s.size() > 0) {
//                    System.out.println(lirl0503s.toString());
//                } else {
                outRusultList2.add(hashMap);
//                }

            }
        }*/
        inInfo.set("result", 1);
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsgByKey(MesConstant.EPResource.EP_2008);
        return new EiInfo();
    }

    /**
     * 校验EiInfo的入参，并且封装调用开单的参数
     * @param eiInfo
     * @return
     */
    private EiInfo checkEiinfoData(EiInfo inInfo){
        //返回开单的调用eiInfo
        EiInfo outInfo = new EiInfo();
        //业务单元
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);
        //司机身份
        String reservationIdentity = (String) inInfo.get("reservationIdentity");
        //终到站地址
        String ladingSpotAddr = (String) inInfo.get("ladingSpotAddr");
        List<String> customerId = (List<String>) inInfo.get("customerId");
        String ladingBillIdEq = (String) inInfo.get("ladingBillId");
        String ladingBillRemark = (String) inInfo.get("ladingBillRemark");

        //校验账套是否为空
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)
                || org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            throw new PlatException("账套不能为空!");
        }
        List<String> listV = new ArrayList<>();
        listV.add("A18113105");
        listV.add("*********");

        //封装参数
        outInfo.set("segNo", segNo);
        //提单备注
        outInfo.set("ladingBillRemark", ladingBillRemark);
        //生效
        outInfo.set("ladingBillStatus", "50");
        outInfo.set("ladingSpotIds", listV);
        if ("10".equals(reservationIdentity)) {
            outInfo.set("userNumIn", customerId);
        } else {
            outInfo.set("tproviderIdIn", customerId);
        }
        outInfo.set("ladingBillIdEq",StringUtils.isNotBlank(ladingBillIdEq) ? ladingBillIdEq : "");
        outInfo.set("destSpotNameEq",StringUtils.isNotBlank(ladingSpotAddr) ? ladingSpotAddr : "");
        return outInfo;
    }

    /**
     * 调用开单中心的接口，并且将返回的单子数据存进临时表
     * @param inInfo
     * @return
     */
    private void callOpenLadingBill(EiInfo inInfo) {
        inInfo.set(EiConstant.serviceId, "S_UV_SL_9018");
        //调post请求
        EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
        if (outInfo.getStatus() == -1) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
            throw new PlatException(outInfo.getMsg());
        }
        List<HashMap> outRusultList = new ArrayList<>();
        //主项数据
        List<HashMap> result = (List<HashMap>) outInfo.get("result");
        if(CollectionUtils.isEmpty(result)){
            throw new PlatException("开单中心暂无需要配单的信息!请联系管理人员");
        }

        //处理开单主项数据
        Map<String,String> countCondition = new HashMap<>();
        countCondition.put("segNo", (String) inInfo.get("segNo"));
        dao.delete("tlirl0599.delete", countCondition);
        //List count = this.dao.query("tlirl0599.count", countCondition);
        //如果开单数量与数据库的开单数量不一致，则进行批量插入
        //if((Integer)count.get(0)!=result.size()){
            List<Tlirl0599> tlirl0599s = new ArrayList<>();
            for (HashMap hashMap : result) {
                Tlirl0599 tlirl0599 = new Tlirl0599();
                tlirl0599.fromMap(hashMap);
                countCondition.put("uuid",tlirl0599.getUuid());
                tlirl0599s.add(tlirl0599);

            }
            //批量插入开单信息
            dao.insertBatch("tlirl0599.insert",tlirl0599s);
        //}


        //处理开单子项捆包数据
        dao.delete("tlirl0598.delete", countCondition);
        List<HashMap> result2 = (List<HashMap>) outInfo.get("result2");
        List<Tlirl0598> tlirl0598s = new ArrayList<>();
        for(HashMap hashMap : result2){
            Map packCondition = new HashMap();
            Tlirl0598 tlirl0598 = new Tlirl0598();
            tlirl0598.fromMap(hashMap);
            packCondition.put("segNo", (String) inInfo.get("segNo"));
            packCondition.put("uuid", tlirl0598.getUuid());
            tlirl0598s.add(tlirl0598);
        }
        dao.insertBatch("tlirl0598.insert", tlirl0598s);

        //System.out.println("提单查询返回："+ JSONArray.toJSONString(result2.get(0)));
        //BigDecimal bigDecimalAdd = new BigDecimal("0");

    }

    /**
     * 提单数据拆分，根据不同的提单类型进行数据处理
     * @param inInfo
     * @return
     */
    private Map<String,List> getLadingBillInfo(List<Map> billList){

        Map ladingBillList = new HashMap();
        //普通提单列表
        List ladingBillInfo = new ArrayList<>();
        //形式提单列表
        List formLadingBillInfo = new ArrayList<>();

        for(Map bill:billList){
            if(bill.get("billingMethod").equals("10")){
                ladingBillInfo.add(bill);
            }else{
                formLadingBillInfo.add(bill);
            }
        }
        //形式提单
        ladingBillList.put("formLadingBillInfo", formLadingBillInfo);
        //普通提单
        ladingBillList.put("ladingBillInfo", ladingBillInfo);

        return ladingBillList;
    }

    /**
     * 调用物流中心接口查询转库单
     * @param inInfo
     * @return
     */
    public void callLogisticsCenter(EiInfo inInfo) {
        Integer limit = (Integer) inInfo.get("limit");
        Integer offset = (Integer) inInfo.get("offset");
        String reservationIdentity = (String) inInfo.get("reservationIdentity");//司机身份
        String ladingSpotAddr = (String) inInfo.get("ladingSpotAddr");//终到站地址
        List<String> customerId = (List<String>) inInfo.get("customerId");
        String ladingBillIdEq = (String) inInfo.get("ladingBillId");
        String ladingBillRemark = (String) inInfo.get("ladingBillRemark");
        String segNo = (String) inInfo.get("segNo");
        String customerIdP = customerId.get(0);
        //查询转库单
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("segNo", segNo);
//            eiInfo2.set("transBillType", "10");
        eiInfo.set("packPutOutFlag", "0");
        eiInfo.set("tproviderId", customerIdP);
        eiInfo.set("deliveryType", "20");
        eiInfo.set("limit", "-999999");
        if (StringUtils.isNotBlank(ladingBillIdEq)) {
            eiInfo.set("transBillId", ladingBillIdEq);
        }
        eiInfo.set(EiConstant.serviceId, "S_UC_EP_0044");
        //调post请求
        EiInfo outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
        Map deleteCondition = new HashMap();
        deleteCondition.put("segNo", segNo);
        dao.delete("tlirl0597.delete", deleteCondition);
        List<HashMap> resultZKs = (List<HashMap>) outInfo.get("result");
        List<Tlirl0597> tlirl0597s = new ArrayList<>();
        for(HashMap resultZK : resultZKs){
            Tlirl0597 tlirl0597 = new Tlirl0597();
            tlirl0597.fromMap(resultZK);
            tlirl0597s.add(tlirl0597);
        }
        dao.insertBatch("tlirl0597.insert", tlirl0597s);
    }

    /***
     * 从第一次叫号开始，如果超过10分钟，司机还没来，就自动往后延一号；
     * 但是10分钟之内行车工可以自己定叫号几次，
     * 算10分钟永远以第一次叫号为准
     */
    public EiInfo callNumberRule(EiInfo inInfo) {
        String segNo = (String) inInfo.get("segNo");
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)){
            segNo="JC000000";
        }
        List<HashMap> query = new ArrayList<>();
        try {
            int timM=0;
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode","CALL_TIME");
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                timM = Integer.parseInt(lirl0314s.get(0).getItemCname());
            }
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("status", "20");
            hashMap.put("timM", timM);
            query = this.dao.query(LIRL0301.QUERY_CALL_NO, hashMap);
            if (CollectionUtils.isNotEmpty(query)) {
                for (HashMap map : query) {
                    String carTraceNo = MapUtils.getString(map, "carTraceNo", "");
                    String segNo1 = MapUtils.getString(map, "segNo", "");
                    String vehicleNo = MapUtils.getString(map, "vehicleNo", "");
                    String targetHandPointId = MapUtils.getString(map, "targetHandPointId", "");
                    String allocateVehicleNo = MapUtils.getString(map, "allocateVehicleNo", "");
                    if (StringUtils.isNotBlank(targetHandPointId)) {
                        //根据装卸点顺序号queueNumber
                        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("segNo", segNo1);
                        stringObjectHashMap.put("targetHandPointId", targetHandPointId);
                        List<HashMap> query1 = this.dao.query(LIRL0401.QUERY_MIN_QUEUE_NUMBER_FOR_RULE, stringObjectHashMap);
                        int minQueueNumber = 0;
                        if (CollectionUtils.isNotEmpty(query1)) {
                            String queueNumber = String.valueOf(query1.get(0));
                            minQueueNumber = Integer.parseInt(queueNumber);
                            //清除叫号表，
                            //插入排队表
                            HashMap<String, Object> stringObjectHashMap2 = new HashMap<>();
                            stringObjectHashMap2.put("segNo", segNo1);
                            stringObjectHashMap2.put("carTraceNo", carTraceNo);
                            stringObjectHashMap2.put("vehicleNo", vehicleNo);
                            List<LIRL0402> query2 = this.dao.query(LIRL0402.QUERY, stringObjectHashMap);
                            if (CollectionUtils.isNotEmpty(query2)) {
                                String factory = "";
                                if (StringUtils.isNotBlank(targetHandPointId)){
                                    Map queryLIRL03011 = new HashMap();
                                    queryLIRL03011.put("segNo",segNo);
                                    queryLIRL03011.put("handPointId",targetHandPointId);
                                    queryLIRL03011.put("status","30");
                                    List<LIRL0304> queryLIRL03041 = this.dao.query(LIRL0304.QUERY, queryLIRL03011);
                                    if (CollectionUtils.isEmpty(queryLIRL03041)){
                                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        inInfo.setMsg("装卸点没有厂区！");
                                        return inInfo;
                                    }
                                    factory=queryLIRL03041.get(0).getFactoryBuilding();
                                }

                                LIRL0402 lirl0402 = query2.get(0);
                                String currTime = DateUtil.curDateTimeStr14();
                                lirl0402.setQueueNumber(minQueueNumber + 1);
                                lirl0402.setUuid(UUIDUtils.getUUID());
                                lirl0402.setRecCreator("system");
                                lirl0402.setRecCreatorName("system");
                                lirl0402.setRecCreateTime(currTime);
                                lirl0402.setQueueDate(currTime);
                                lirl0402.setRecRevisor("system");
                                lirl0402.setRecRevisorName("system");
                                lirl0402.setRecReviseTime(currTime);
                                lirl0402.setVehicleNo(vehicleNo);
                                lirl0402.setVoucherNum(allocateVehicleNo);
                                lirl0402.setFactoryArea(factory);
                                Map map1 = lirl0402.toMap();
                                this.dao.insert(LIRL0401.INSERT, map1);
                                //清除叫号表
                                this.dao.delete(LIRL0402.DELETE, stringObjectHashMap2);
                                //修改车辆跟踪表
                                HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
                                stringObjectHashMap1.put("carTraceNo", carTraceNo);
                                stringObjectHashMap1.put("segNo", segNo);
                                List<LIRL0301> query3 = this.dao.query(LIRL0301.QUERY, stringObjectHashMap1);
                                if (CollectionUtils.isNotEmpty(query3)) {
                                    String status = query3.get(0).getStatus();
                                    if ("20".equals(status)) {
                                        this.dao.update("LIRL0301.backAll", stringObjectHashMap1);
                                    }
                                }
                            }
                        }

                    }
                }
            }
//
//            if (true){
//                throw new RuntimeException("测试中’‘’‘’‘’‘’");
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        inInfo.set("list", query);
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /***
     * 定时查询配单中提单是否已撤销
     * @ServiceId S_LI_RL_0194
     * @param inInfo
     */
    public EiInfo queryLadingBillIsCancel(EiInfo inInfo) {
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);
        if (org.apache.commons.lang3.StringUtils.isBlank(segNo)) {
            segNo = "JC000000";
        }
        List<HashMap> list = new ArrayList<>();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        List<HashMap> queryLIRL0502 = this.dao.query(LIRL0502.QUERY_ALL_VOUCHER_NUM_ALLOCATE, hashMap);
        if (CollectionUtils.isNotEmpty(queryLIRL0502)) {
            //查询提单是否被撤销
            for (HashMap map : queryLIRL0502) {
                String voucherNum = (String) map.get("voucherNum");
                String allocateVehicleNo = (String) map.get("allocateVehicleNo");
                String carTraceNo = (String) map.get("carTraceNo");
                if (voucherNum.startsWith("BL")) {
                    hashMap.put("segNo", segNo);
                    hashMap.put("ladingBillId", voucherNum);
                    hashMap.put("ladingBillStatus", "00");
                    //先查询提单是否存在，不存在调服务写入车辆登记提单信息表
                    inInfo.set(EiConstant.serviceId, "S_UV_SL_9020");//查询提单服务
                    inInfo.set("main", hashMap);
                    EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                    if (outInfo.getStatus() == -1) {
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
                        throw new RuntimeException(inInfo.getMsg());
                    }
                    list = (List<HashMap>) outInfo.get("result");
                }else if (voucherNum.startsWith("ZK")){
                    //查询转库单
                    EiInfo eiInfo2 = new EiInfo();
                    eiInfo2.set("segNo", segNo);
                    eiInfo2.set("transBillTypeS", "00");
                    eiInfo2.set("packPutOutFlag", "0");
                    eiInfo2.set("transBillId", voucherNum);
                    eiInfo2.set(EiConstant.serviceId, "S_UC_EP_0044");
                    EiInfo outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                    if (outInfo.getStatus() == -1) {
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
                        throw new RuntimeException(inInfo.getMsg());
                    }
                    list = (List<HashMap>) outInfo.get("result");
                }
                if (CollectionUtils.isNotEmpty(list)) try {
                    {
                        //判断配车单下是否只有一个撤销的提单
                        HashMap<String, Object> hashMap0503 = new HashMap<>();
                        hashMap0503.put("segNo", segNo);
                        hashMap0503.put("allocateVehicleNo", allocateVehicleNo);
                        hashMap0503.put("voucherNumNo", voucherNum);
                        List<LIRL0503> query = this.dao.query(LIRL0503.QUERY, hashMap0503);
                        if (CollectionUtils.isNotEmpty(query)) {
                            //撤下子项配单即可
                            HashMap hashMapR = new HashMap();
                            hashMapR.put("status", "00");
                            hashMapR.put("segNo", segNo);
                            hashMapR.put("allocateVehicleNo", allocateVehicleNo);
                            hashMapR.put("voucherNum", voucherNum);
                            hashMapR.put("delFlag", "1");
                            RecordUtils.setRevisor(hashMapR);
                            hashMapR.put("remark", "提单撤销同时撤销配单:" + DateUtil.curDateTimeStr14());
                            this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR);
                        } else {
                            //撤销整个配单主子项、排队信息、清空车辆跟踪配单信息
                            HashMap hashMapR = new HashMap();
                            hashMapR.put("status", "00");
                            hashMapR.put("segNo", segNo);
                            hashMapR.put("allocateVehicleNo", allocateVehicleNo);
                            hashMapR.put("delFlag", "1");
                            RecordUtils.setRevisor(hashMapR);
                            hashMapR.put("remark", "提单撤销同时撤销配单:" + DateUtil.curDateTimeStr14());
                            this.dao.update(LIRL0502.UPDATE_STATUS, hashMapR);
                            this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR);
                            //撤销排队信息
                            hashMapR.put("carTraceNo", carTraceNo);
                            hashMapR.put("voucherNum", voucherNum);
                            if (StringUtils.isNotBlank(carTraceNo)) {
                                this.dao.delete(LIRL0401.DELETE, hashMapR);
                                this.dao.delete(LIRL0402.DELETE, hashMapR);
                                //清空车辆配单信息
                                this.dao.update(LIRL0301.UPDATE_ALLOCATE_VEHICLE_NO, hashMapR);
                            }
                        }
                    }
                } catch (Exception e) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("提单撤销报错:" + e.getMessage());
                    return inInfo;
                }
            }
        }
        return inInfo;
    }

    /***
     * 现货交易配单功能
     * @serviceId S_LI_RL_0195
     */
    public EiInfo querySpotTradeOrder(EiInfo inInfo) {
        EiInfo info = new EiInfo();
        String segNo = (String) inInfo.get("segNo");
        String billId = (String) inInfo.get("ladingBillId");//批次号

        if (StringUtils.isBlank(segNo)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空！");
            return inInfo;
        }


        //模糊查询
        if (StringUtils.isNotBlank(billId)){
            info.set("billId",billId);
            if (billId.startsWith("BL")){
                info.set("ladingBillId",billId);
                info.set("billId","");
            }
        }

        info.set("segNo",segNo);

        try {
            info.set(EiConstant.serviceId, "S_UC_PR_0442");
            //调post请求
            EiInfo outInfo = EServiceManager.call(info, TokenUtils.getXplatToken());
            if (outInfo.getStatus() == -1) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
//                throw new RuntimeException(outInfo.getMsg());
            }

            List<HashMap> list = (List<HashMap>) outInfo.get("result");
            if (CollectionUtils.isNotEmpty(list)) {
                // 按billId分组统计
                Map<String, BillGroupInfo> billGroupMap = groupByBillId(list);

                // 处理每个账单组
                List<Map<String, Object>> orderList = new ArrayList<>();
                List<Map<String, Object>> subList = new ArrayList<>();
                for (Map.Entry<String, BillGroupInfo> entry : billGroupMap.entrySet()) {
                    BillGroupInfo groupInfo = entry.getValue();

                    //判断批次单号是否已被用过，排除
                    //根据billId,ladingBillId,packId查询0503表是否已经被用过，如果用完直接跳过，没被用完只展示剩余的总数和总重量以及明细
                    BillGroupInfo remainingGroupInfo = filterUsedRecords(segNo, groupInfo);
                    // 如果还有剩余可用数据，则构建主单
                    if (remainingGroupInfo.getPackIds().size() > 0) {
                        // 构建主单信息
                        Map<String, Object> mainOrder = buildMainOrder(segNo, remainingGroupInfo);

                        // 处理明细并添加到主单下
                        List<Map<String, Object>> detailList = processOrderDetails(segNo, remainingGroupInfo);
                        mainOrder.put("packList", detailList);

                        orderList.add(mainOrder);
                    }
                }

                // 返回处理结果
                inInfo.set("List", orderList);
                inInfo.set("totalBillCount", billGroupMap.size());
                inInfo.set("totalPackCount", list.size());

                // 计算总重量
                BigDecimal totalWeight = billGroupMap.values().stream()
                        .map(BillGroupInfo::getTotalWeight)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                inInfo.set("totalWeight", totalWeight);

                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("现货交易配单处理成功，共处理" + billGroupMap.size() + "个账单，" + list.size() + "个捆包");

            } else {
                List<HashMap> hashMaps = new ArrayList<>();
                // 返回处理结果
                inInfo.set("List", hashMaps);
                info.setStatus(EiConstant.STATUS_DEFAULT);
            }

        } catch (RuntimeException e) {
            logger.error("现货交易配单处理异常", e);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("现货交易配单处理失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("现货交易配单处理异常", e);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("现货交易配单处理失败: " + e.getMessage());
        }
        return inInfo;
    }

    /**
     * 按billId分组统计
     */
    private Map<String, BillGroupInfo> groupByBillId(List<HashMap> list) {
        Map<String, BillGroupInfo> billGroupMap = new HashMap<>();

        for (HashMap item : list) {
            String billId = MapUtils.getString(item, "billId", "");
            String packId = MapUtils.getString(item, "packId", "");
            BigDecimal netWeight = new BigDecimal(MapUtils.getString(item, "netWeight", "0"));

            // 如果billId不存在，创建新的分组信息
            if (!billGroupMap.containsKey(billId)) {
                BillGroupInfo groupInfo = new BillGroupInfo();
                groupInfo.setBillId(billId);
                groupInfo.setPackIds(new HashSet<>());
                groupInfo.setTotalWeight(BigDecimal.ZERO);
                groupInfo.setPackDetails(new ArrayList<>());
                billGroupMap.put(billId, groupInfo);
            }

            // 添加到对应的分组
            BillGroupInfo groupInfo = billGroupMap.get(billId);
            groupInfo.getPackIds().add(packId);
            groupInfo.setTotalWeight(groupInfo.getTotalWeight().add(netWeight));
            groupInfo.getPackDetails().add(item);
        }

        return billGroupMap;
    }

    /**
     * 过滤已使用的记录，获取剩余可用数据
     */
    private BillGroupInfo filterUsedRecords(String segNo, BillGroupInfo originalGroupInfo) {
        BillGroupInfo remainingGroupInfo = new BillGroupInfo();
        remainingGroupInfo.setBillId(originalGroupInfo.getBillId());
        remainingGroupInfo.setPackIds(new HashSet<>());
        remainingGroupInfo.setTotalWeight(BigDecimal.ZERO);
        remainingGroupInfo.setPackDetails(new ArrayList<>());

        for (HashMap packDetail : originalGroupInfo.getPackDetails()) {
            String billId = MapUtils.getString(packDetail, "billId", "");
            String ladingBillId = MapUtils.getString(packDetail, "ladingBillId", "");
            String packId = MapUtils.getString(packDetail, "packId", "");

            // 查询0503表是否已被使用
            boolean isUsed = checkIfPackUsed(segNo, billId, ladingBillId, packId);

            if (!isUsed) {
                // 未使用，添加到剩余数据中
                remainingGroupInfo.getPackIds().add(packId);
                BigDecimal netWeight = new BigDecimal(MapUtils.getString(packDetail, "netWeight", "0"));
                remainingGroupInfo.setTotalWeight(remainingGroupInfo.getTotalWeight().add(netWeight));
                remainingGroupInfo.getPackDetails().add(packDetail);

                logger.info("捆包可用: billId={}, ladingBillId={}, packId={}", billId, ladingBillId, packId);
            } else {
                logger.info("捆包已使用，跳过: billId={}, ladingBillId={}, packId={}", billId, ladingBillId, packId);
            }
        }

        return remainingGroupInfo;
    }

    /**
     * 检查捆包是否已被使用
     */
    private boolean checkIfPackUsed(String segNo, String billId, String ladingBillId, String packId) {
        try {
            // 构建查询条件
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("segNo", segNo);
            queryParams.put("billId", billId);
            queryParams.put("ladingBillId", ladingBillId);
            queryParams.put("packId", packId);

            // 查询0503表
            List<HashMap> usedRecords = dao.query("LIRL0503.queryUsedPacks", queryParams);

            // 如果有记录，说明已被使用
            return usedRecords != null && usedRecords.size() > 0;

        } catch (Exception e) {
            logger.error("查询捆包使用情况异常: segNo={}, billId={}, ladingBillId={}, packId={}",
                    segNo, billId, ladingBillId, packId, e);
            // 查询异常时，为了安全起见，认为已被使用
            return true;
        }
    }

    /**
     * 构建主单信息
     */
    private Map<String, Object> buildMainOrder(String segNo, BillGroupInfo groupInfo) {
        Map<String, Object> mainOrder = new HashMap<>();

        mainOrder.put("segNo", segNo);
        mainOrder.put("billId", groupInfo.getBillId());
        mainOrder.put("packCount", groupInfo.getPackIds().size());
        mainOrder.put("totalWeight", groupInfo.getTotalWeight());

        return mainOrder;
    }

    /**
     * 处理订单明细
     */
    private List<Map<String, Object>> processOrderDetails(String segNo, BillGroupInfo groupInfo) {
        List<Map<String, Object>> detailList = new ArrayList<>();
        for (HashMap packDetail : groupInfo.getPackDetails()) {
            try {
                detailList.add(packDetail);
            } catch (Exception e) {
                logger.error("处理捆包明细异常: packId={}, billId={}",
                        MapUtils.getString(packDetail, "packId", ""), groupInfo.getBillId(), e);
                throw new RuntimeException("处理捆包明细失败: " + e.getMessage());
            }
        }
        return detailList;
    }

    /**
     * 账单分组信息类
     */
    private static class BillGroupInfo {
        private String billId;
        private Set<String> packIds;
        private BigDecimal totalWeight;
        private List<HashMap> packDetails;

        public BillGroupInfo() {
            this.packIds = new HashSet<>();
            this.totalWeight = BigDecimal.ZERO;
            this.packDetails = new ArrayList<>();
        }

        // Getter和Setter方法
        public String getBillId() {
            return billId;
        }

        public void setBillId(String billId) {
            this.billId = billId;
        }

        public Set<String> getPackIds() {
            return packIds;
        }

        public void setPackIds(Set<String> packIds) {
            this.packIds = packIds;
        }

        public BigDecimal getTotalWeight() {
            return totalWeight;
        }

        public void setTotalWeight(BigDecimal totalWeight) {
            this.totalWeight = totalWeight;
        }

        public List<HashMap> getPackDetails() {
            return packDetails;
        }

        public void setPackDetails(List<HashMap> packDetails) {
            this.packDetails = packDetails;
        }
    }

    /***
     * 添加现货交易配单
     *
     * @ServiceId S_LI_RL_0196
     */
    public EiInfo addSpotTradeOrder(EiInfo inInfo) {
        List<HashMap> list = (List<HashMap>) inInfo.get("list");
        List<HashMap> rusult2 = (List<HashMap>) inInfo.get("result2");
        String vehicleNo = "";
        String time = "";
        String driverName = "";
        String tel = "";
        List<String> targetHandPointIdList = new ArrayList<>();
        String recCreator = "";
        String recCreatorName = "";
        String customerId = "";
        String customerName = "";
        String carTraceNo = "";
        String entryFlag = (String) inInfo.get("entryFlag");
        String nextAlcVehicleNo = "";
        String allocateVehicleNoNew = "";
        String segNo = (String) inInfo.get("segNo");

        if (StringUtils.isBlank(segNo)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("账套不能为空！");
            return inInfo;
        }

        if (rusult2.size() > 0) {
            HashMap hashMap = rusult2.get(0);
            vehicleNo = MapUtils.getString(hashMap, "vehicleNo");
            time = MapUtils.getString(hashMap, "time");
            driverName = MapUtils.getString(hashMap, "driverName");
            tel = MapUtils.getString(hashMap, "driverTel");
            recCreator = MapUtils.getString(hashMap, "driverTel", "");//创建人
            recCreatorName = MapUtils.getString(hashMap, "driverName", "");//创建人姓名
            carTraceNo = MapUtils.getString(hashMap, "carTraceNo", "");//车辆跟踪号
        }

        if (CollectionUtils.isEmpty(list)) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("请勾选明细!");
            return inInfo;
        }

        try {
            // 生成配车单号
            //配车单流水号
            Date date = new Date(System.currentTimeMillis());
            String strSeqTypeId = "TMELI0502_SEQ01";
            String[] args = {segNo.substring(0, 2), date.toString(), ""};
            allocateVehicleNoNew = SequenceGenerator.getNextSequence(strSeqTypeId, args); //配车单流水号

            // 生成车辆跟踪号（如果没有的话）
            if (StringUtils.isBlank(carTraceNo)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("车辆没有进厂，不能新增配单!");
                return inInfo;
            }

            //针对进厂车辆，同一辆车只能做一次现货配单
            HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
            stringObjectHashMap1.put("vehicleNo", vehicleNo);
            stringObjectHashMap1.put("segNo", segNo);
            stringObjectHashMap1.put("status", "20");
            stringObjectHashMap1.put("allocType", "30");
            List<LIRL0502> query1 = this.dao.query(LIRL0502.QUERY, stringObjectHashMap1);
            if (CollectionUtils.isNotEmpty(query1)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("同一辆车只能做一次现货配单,如需再次配单，请先操作取消配单！");
                return inInfo;
            }


            for (HashMap entry : list) {
                String billId = (String) entry.get("billId");
                // 插入主项0502表
                Map<String, Object> mainOrder = buildMainOrder0502(segNo, allocateVehicleNoNew, vehicleNo,
                        carTraceNo,  recCreator, recCreatorName, entryFlag);

                dao.insert(LIRL0502.INSERT, mainOrder);
                logger.info("插入主项0502表成功: allocateVehicleNo={}",
                        allocateVehicleNoNew);

                //明细
                List<HashMap> detailList = (List<HashMap>) entry.get("packList");
                // 插入子项0503表
                for (int i = 0; i < detailList.size(); i++) {
                    HashMap detail = detailList.get(i);

                    // 生成配车单子项序号
                    String[] arg = {allocateVehicleNoNew, "", "", ""};
                    String allocVehicleSeq = SequenceGenerator.getNextSequence("TMELI0503_SEQ01", arg);

                    Map<String, Object> detailOrder = buildDetailOrder0503(segNo, allocateVehicleNoNew,
                            allocVehicleSeq, detail, recCreator, recCreatorName);

                    dao.insert(LIRL0503.INSERT, detailOrder);
                    logger.info("插入子项0503表成功: packId={}, allocVehicleSeq={}",
                            detail.get("packId"), allocVehicleSeq);
                }
            }

            // 返回成功结果
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("现货交易配单创建成功，配车单号: " + allocateVehicleNoNew);
            inInfo.set("allocateVehicleNo", allocateVehicleNoNew);
//            inInfo.set("carTraceNo", carTraceNo);
//            inInfo.set("totalPackCount", list.size());
//
//            // 计算总重量
//            BigDecimal totalWeight = list.stream()
//                .map(item -> new BigDecimal(MapUtils.getString(item, "netWeight", "0")))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//            inInfo.set("totalWeight", totalWeight);

        } catch (Exception e) {
            logger.error("现货交易配单创建异常", e);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("现货交易配单创建失败: " + e.getMessage());
        }

        return inInfo;
    }

    /**
     * 构建主项0502表数据
     */
    private Map<String, Object> buildMainOrder0502(String segNo, String allocateVehicleNo, String vehicleNo,
                                                   String carTraceNo,
                                                   String recCreator, String recCreatorName, String entryFlag) {
        Map<String, Object> mainOrder = new HashMap<>();

        mainOrder.put("segNo", segNo);
        mainOrder.put("unitCode", segNo);
        mainOrder.put("allocateVehicleNo", allocateVehicleNo);
        mainOrder.put("vehicleNo", vehicleNo);
        mainOrder.put("carTraceNo", carTraceNo);
        mainOrder.put("allocType", "30"); // 装货类型
        mainOrder.put("status", "20"); // 配单状态
        mainOrder.put("entryFlag", entryFlag);
        mainOrder.put("recCreator", recCreator);
        mainOrder.put("recCreatorName", recCreatorName);
        mainOrder.put("recCreateTime", DateUtil.curDateTimeStr14());
        mainOrder.put("recRevisor", recCreator);
        mainOrder.put("recRevisorName", recCreatorName);
        mainOrder.put("recReviseTime", DateUtil.curDateTimeStr14());
        mainOrder.put("uuid", UUIDUtils.getUUID());
        mainOrder.put("delFlag", "0");
        mainOrder.put("archiveFlag", "0");
        mainOrder.put("tenantId", " ");
        mainOrder.put("remark", "现货交易配单");

        // 添加其他必需字段
        mainOrder.put("showFlag", "1");
        mainOrder.put("nextAlcVehicleNo", "");
        mainOrder.put("estimatedTimeOfArrival", "");
        mainOrder.put("allocMes", "");
        mainOrder.put("customerId", "");
        mainOrder.put("customerName", "");
        mainOrder.put("proformaVoucherNum", "");
        mainOrder.put("emergencyDeliveryTime", "");
        mainOrder.put("ladingBillRemark", "");
        mainOrder.put("noPlanFlag", "");
        mainOrder.put("driverName", "");
        mainOrder.put("driverTel", "");
        mainOrder.put("proformaOrderNum", "");
        mainOrder.put("proformaSpecsDesc", "");

        return mainOrder;
    }

    /**
     * 构建子项0503表数据
     */
    private Map<String, Object> buildDetailOrder0503(String segNo, String allocateVehicleNo,
                                                     String allocVehicleSeq, HashMap detail,
                                                     String recCreator, String recCreatorName) {
        Map<String, Object> detailOrder = new HashMap<>();

        String packId = MapUtils.getString(detail, "packId", "");
        String warehouseCode = MapUtils.getString(detail, "warehouseCode", "");
        String locationId = MapUtils.getString(detail, "locationId", "");
        String locationName = MapUtils.getString(detail, "locationName", "");
        String netWeight = MapUtils.getString(detail, "packWeight", "0");
        String customerId = MapUtils.getString(detail, "customerId", "");
        String customerName = MapUtils.getString(detail, "customerName", "");
        String matInnerId = MapUtils.getString(detail, "matInnerId", "");
        String specsDesc = MapUtils.getString(detail, "specsDesc", "");
        String pieceNum = MapUtils.getString(detail, "quantity", "1");
        String prodTypeId = MapUtils.getString(detail, "prodTypeId", "");
        String warehouseName = MapUtils.getString(detail, "warehouseName", "");
        String billId = MapUtils.getString(detail, "billId", "");
        String billSubId = MapUtils.getString(detail, "billSubid", "");
        String ladingBillId = MapUtils.getString(detail, "ladingBillId", "");
        String orderNo = MapUtils.getString(detail, "orderNo", "");
        String factoryProductId = MapUtils.getString(detail, "factoryProductId", "");
        String targetHandPointId = getHandPointId(segNo, warehouseCode, locationId);
        detailOrder.put("segNo", segNo);
        detailOrder.put("unitCode", segNo);
        detailOrder.put("status", "20");
        detailOrder.put("allocateVehicleNo", allocateVehicleNo);
        detailOrder.put("allocVehicleSeq", allocVehicleSeq);
        detailOrder.put("voucherNum", ladingBillId); // 提单号
        detailOrder.put("packId", packId);
        detailOrder.put("outPackFlag", "0"); // 非自带货
        detailOrder.put("warehouseCode", warehouseCode);
        detailOrder.put("warehouseName", warehouseName);
        detailOrder.put("locationId", locationId);
        detailOrder.put("locationName", locationName);
        detailOrder.put("netWeight", netWeight);
        detailOrder.put("customerId", customerId);
        detailOrder.put("customerName", customerName);
        detailOrder.put("matInnerId", matInnerId);
        detailOrder.put("specsDesc", specsDesc);
        detailOrder.put("pieceNum", pieceNum);
        detailOrder.put("prodTypeId", prodTypeId);
        detailOrder.put("targetHandPointId", targetHandPointId);
        detailOrder.put("recCreator", recCreator);
        detailOrder.put("recCreatorName", recCreatorName);
        detailOrder.put("recCreateTime", DateUtil.curDateTimeStr14());
        detailOrder.put("recRevisor", recCreator);
        detailOrder.put("recRevisorName", recCreatorName);
        detailOrder.put("recReviseTime", DateUtil.curDateTimeStr14());
        detailOrder.put("uuid", UUIDUtils.getUUID());
        detailOrder.put("delFlag", "0");
        detailOrder.put("archiveFlag", "0");
        detailOrder.put("tenantId", " ");
        detailOrder.put("remark", "现货交易配单明细");
        detailOrder.put("batchSubNum", billSubId);
        detailOrder.put("batchNum", billId);
        detailOrder.put("putinType", " ");
        detailOrder.put("innerDiameter", 610);
        detailOrder.put("prodDensity", 7.85);
        detailOrder.put("productProcessId", " ");
        detailOrder.put("purOrderNum", orderNo);
        detailOrder.put("factoryOrderNum", factoryProductId);

        return detailOrder;
    }

    private String getHandPointId(String segNo, String warehouseCode, String locationId) {
        String targetHandPointId;
        //查询装卸点发送短信通知
        Map queryLIDS0601 = new HashMap();
        queryLIDS0601.put("segNo", segNo);
        queryLIDS0601.put("warehouseCode", warehouseCode);
        queryLIDS0601.put("locationId", locationId);
        queryLIDS0601.put("useStatus", 10);
        //查询库位附属信息表
        List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
        if (lids0601s.size() > 0) {
            LIDS0601 lids0601 = lids0601s.get(0);
            targetHandPointId = lids0601.getLoadingPointNo();
            if (StringUtils.isBlank(targetHandPointId)) {
                String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                throw new RuntimeException(massage);
            }
            //判断装卸点是否启用
            Map queryLIRL0304 = new HashMap();
            queryLIRL0304.put("segNo", segNo);
            queryLIRL0304.put("handPointId", targetHandPointId);
            queryLIRL0304.put("status", "30");
            int count = super.count(LIRL0304.COUNT, queryLIRL0304);
            if (count < 0) {
                String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                throw new RuntimeException(massage);
            }
        } else {
            String massage = "查询不到库位附属信息";
            throw new RuntimeException(massage);
        }
        return targetHandPointId;
    }

    /***
     * 获取现货交易配单车牌信息
     * @serviceId S_LI_RL_0199
     */
    public EiInfo getSpotTradeOrder(EiInfo inInfo) {
        HashMap<String, Object> hashMap = new HashMap<>();
        String segNo = (String) inInfo.get(MesConstant.SEG_NO);//身份类型
        String driverTel = (String) inInfo.get("driverTel");//手机号
        hashMap.put(MesConstant.SEG_NO, segNo);
        hashMap.put("driverTel", driverTel);
        hashMap.put("delFlag",0);
        List<Map> list = this.dao.query(LIRL0301.QUERY_SPOT_VEHICLE, hashMap);
        inInfo.set("result", list);
        return inInfo;
    }




    /***
     * S_LI_RL_0201
     * 厂内物流跟踪预警系统 - 根据提单交付时间判断是否进入预警状态
     * 需求:
     * 1. 主机厂及有明确交付时间客户，开单时必须选择交付时间，未选择的不计入厂内物流跟踪
     * 2. 距离截止交付时间4小时开始预警状态
     * 3. 作业中距离交付时间截止3小时呈紧急状态
     * 4. 未出厂距离交付截止时间2小时呈特急状态
     */
    public EiInfo queryDeliveryTime(EiInfo eiInfo) {
        try {
            String segNo = (String) eiInfo.get("segNo");
            if (StringUtils.isBlank(segNo)){
                segNo="JC000000";
            }
            EiInfo inInfo = new EiInfo();
            Map<String, String> hashMapV = new HashMap<>();
            hashMapV.put("segNo",segNo);
            hashMapV.put("ladingBillStatus","50");
            hashMapV.put("limit", "-999999");
            hashMapV.put("offset", "0");
            // 查询提单服务
            inInfo.set(EiConstant.serviceId, "S_UV_SL_9020");
            inInfo.set("main", hashMapV);
            EiInfo outInfo = EServiceManager.call(inInfo,ImcGlobalUtils.getToken());

            if (outInfo.getStatus() == -1) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
                throw new RuntimeException(eiInfo.getMsg());
            }


            @SuppressWarnings("unchecked")
            List<HashMap<String, Object>> list = (List<HashMap<String, Object>>) outInfo.get("result");
            String finalSegNo = segNo;
            if (CollectionUtils.isNotEmpty(list)) {
                // 1. 排除requireFinishDate为空的集合（不计入厂内物流跟踪）
                List<HashMap<String, Object>> filteredList = list.stream()
                        .filter(item -> {
                            String requireFinishDate = (String) item.get("requireFinishDate");
                            String ladingBillId = (String) item.get("ladingBillId"); // 提单号
                            String totalWeight = (String) item.get("totalWeight"); // 总重量

                            // 排除交付时间为空的
                            if (requireFinishDate == null || StringUtils.isBlank(requireFinishDate.toString())) {
                                return false;
                            }

                            // 9. 形式提货单达到95%出库量则自动剔除预警
                            if (ladingBillId != null && totalWeight != null) {
                                HashMap<String, Object> map0308 = new HashMap<>();
                                map0308.put("segNo", finalSegNo);
                                map0308.put("voucherNum", ladingBillId);
                                @SuppressWarnings("unchecked")
                                List<String> query0308 = this.dao.query(LIRL0308.QUERY_WEIGHT_CQ, map0308);
                                if (query0308.size()>0){
                                    BigDecimal quantity = new BigDecimal(query0308.get(0));//出库量
                                    BigDecimal totalWeightB = new BigDecimal(totalWeight);//提单总重
                                    // 计算出库率
                                    if (totalWeightB.compareTo(BigDecimal.ZERO) > 0) {
                                        double rate = quantity.divide(totalWeightB, 4, BigDecimal.ROUND_HALF_UP)
                                                .multiply(new BigDecimal(100)).doubleValue();
                                        if (rate >= 95.0) {
                                            return false; // 出库率达到95%，剔除预警
                                        }
                                    }
                                }
                            }
                            return true;
                        })
                        .collect(Collectors.toList());
                // 处理预警逻辑，按车辆分组处理一车多单情况
                List<HashMap<String, Object>> alertResultList = new ArrayList<>();

                for (HashMap<String, Object> item : filteredList) {
                    //查询厂内物流跟踪预警信息表提单是否修改过时间
                    String requireFinishDate = (String) item.get("requireFinishDate");
                    String ladingBillId = (String) item.get("ladingBillId"); // 提单号
                    String totalWeight = (String) item.get("totalWeight"); // 总重量
                    String deliveryType = (String) item.get("deliveryType"); // 交货方式
                    //查询厂内物流跟踪预警信息表提单是否修改过时间
                    HashMap<String, Object> map0509 = new HashMap<>();
                    map0509.put("segNo", finalSegNo);
                    map0509.put("ladingBillId", ladingBillId);
                    List<LIRL0509> query = this.dao.query(LIRL0509.QUERY,map0509);
                    if (CollectionUtils.isNotEmpty( query)){
                        String status = query.get(0).getStatus();
                        if ("99".equals(status)){
                            requireFinishDate=query.get(0).getRequireFinishDate();
                        }
                    }
                    String tproviderId="";
                    String tproviderName="";
                    String d_userNum = (String) item.get("d_userNum"); // 分户号
                    String d_userName = (String) item.get("d_userName"); // 分户名称
                    if ("10".equals(deliveryType)){
                        tproviderId ="自提"; // 承运商代码
                        tproviderName = "自提";
                    }else {
                        tproviderId = (String) item.get("tproviderId"); // 承运商代码
                        tproviderName = (String) item.get("tproviderName"); // 承运商名称
                    }

                    try {
                        // 计算剩余时间
                        Date finishDate = parseDate(requireFinishDate);
                        Date currentDate = new Date();
                        long diffInMillis = finishDate.getTime() - currentDate.getTime();
                        long remainingHours = diffInMillis / (60 * 60 * 1000);


                        // 计算详细的小时和分钟 (如: 3:10)
                        long totalMinutes = diffInMillis / (60 * 1000);
                        long hours = totalMinutes / 60;
                        long minutes = totalMinutes % 60;
                        String remainingTimeDisplay = hours + ":" + String.format("%02d", minutes);

                        // 查询配单信息
                        HashMap<String, Object> map0503 = new HashMap<>();
                        map0503.put("segNo", finalSegNo);
                        map0503.put("voucherNum", ladingBillId);
                        map0503.put("status", "20");
                        @SuppressWarnings("unchecked")
                        List<HashMap<String, Object>> queryLIRL0503 = this.dao.query(LIRL0503.QUERY_ALL_VOUCHER_NUM, map0503);

                        String deliveryStatus = "";
                        String workStatus = "";
                        String vehicleNo = "";
                        String transportUnit = "";
                        String carTraceNo = "";
                        String allocateVehicleNo = "";
                        BigDecimal netWeight = new BigDecimal(0);

                        if (CollectionUtils.isNotEmpty(queryLIRL0503)) {
                            // 已配单
                            HashMap<String, Object> vehicleInfo = queryLIRL0503.get(0);
                            carTraceNo = (String) vehicleInfo.get("carTraceNo");
                            vehicleNo = (String) vehicleInfo.get("vehicleNo");
                            transportUnit = (String) vehicleInfo.get("customerId");
                            netWeight = convertToBigDecimal(vehicleInfo.get("netWeight"));
                            allocateVehicleNo = (String)vehicleInfo.get("allocateVehicleNo");

                            // 查询车辆状态（排队、叫号、作业中）
                            String vehicleStatusResult = getVehicleWorkStatus(finalSegNo, carTraceNo);
                            workStatus = vehicleStatusResult;
                            if ("排队中".equals(vehicleStatusResult)) {
                                deliveryStatus = getDeliveryStatusByTime(remainingHours, "QUEUED");
                            } else if ("叫号中".equals(vehicleStatusResult)) {
                                deliveryStatus = getDeliveryStatusByTime(remainingHours, "CALLING");
                            } else if ("作业中".equals(vehicleStatusResult)) {
                                deliveryStatus = getDeliveryStatusByTime(remainingHours, "WORKING");
                            } else if ("进厂已配单未启动排队".equals(vehicleStatusResult)) {
                                deliveryStatus = getDeliveryStatusByTime(remainingHours, "FACTORY_ASSIGNED");
                            } else if ("未进厂已配单".equals(vehicleStatusResult)) {
                                deliveryStatus = getDeliveryStatusByTime(remainingHours, "NOT_FACTORY_ASSIGNED");
                            }

                        } else {
                            workStatus="未配单";
                            deliveryStatus = getDeliveryStatusByTime(remainingHours, "NOT_ASSIGNED");
                            vehicleNo = "";
                            transportUnit = "";

                        }

                        if (StringUtils.isNotBlank(carTraceNo)) {
                            // 检查是否已出厂
                            boolean isOutFactory = checkVehicleOutFactory(finalSegNo, vehicleNo, carTraceNo);
                            if (!isOutFactory && remainingHours <= 2) {
                                deliveryStatus = "特急"; // 未出厂且小于2小时为特急
                                workStatus="作业完成未出厂";
                            }
                        }

                        // 记录到跟踪表
                        recordToTrackingTable(finalSegNo, ladingBillId, vehicleNo, deliveryStatus,
                                d_userNum,d_userName,tproviderId,tproviderName, totalWeight, transportUnit, requireFinishDate, workStatus,netWeight,allocateVehicleNo,carTraceNo,remainingTimeDisplay);


                    } catch (Exception e) {
                        logger.error("处理提单预警失败，提单号: {}", ladingBillId, e);
                    }
                }
                eiInfo.set("alertList", alertResultList);
//                eiInfo.set("statusGroupMap", statusGroupMap);
                eiInfo.set("totalCount", alertResultList.size());
                eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }

        } catch (Exception e) {
            logger.error("厂内物流跟踪预警查询失败", e);
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
        }
        return eiInfo;
    }

    /**
     * 日期解析工具方法 - 智能识别多种时间格式
     */
    private Date parseDate(String dateStr) throws ParseException {
        if (StringUtils.isBlank(dateStr)) {
            throw new ParseException("日期字符串为空", 0);
        }

        // 去除空格
        dateStr = dateStr.trim();

        // 判断是14位数字格式 (yyyyMMddHHmmss) 还是标准格式
        if (dateStr.matches("\\d{14}")) {
            // 14位数字格式：20250816000000
            return DATE_FORMAT_14.parse(dateStr);
        } else if (dateStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
            // 标准格式：2025-08-16 00:00:00
            return DATE_FORMAT.parse(dateStr);
        } else {
            // 尝试其他可能的格式
            try {
                // 先尝试14位格式
                return DATE_FORMAT_14.parse(dateStr);
            } catch (ParseException e1) {
                try {
                    // 再尝试标准格式
                    return DATE_FORMAT.parse(dateStr);
                } catch (ParseException e2) {
                    throw new ParseException("无法解析日期格式: " + dateStr + "，支持格式: yyyyMMddHHmmss 或 yyyy-MM-dd HH:mm:ss", 0);
                }
            }
        }
    }

    /**
     * 根据剩余时间和作业状态判断交付状态
     */
    private String getDeliveryStatusByTime(long remainingHours, String workType) {
        if (remainingHours <= 4&&remainingHours>=2) {
            if ("NOT_ASSIGNED".equals(workType) ||"QUEUED".equals(workType)||"NOT_FACTORY_ASSIGNED".equals(workType)||"FACTORY_ASSIGNED".equals(workType)||"NOT_QUEUED".equals(workType)) {
                return "预警";
            }else {
                if ("WORKING".equals(workType)||"CALLING".equals(workType)) {
                    return "紧急";
                }
            }
        } else if (remainingHours <= 4){
            if (remainingHours<=4&&remainingHours>=3){
                if ("NOT_ASSIGNED".equals(workType) ||"QUEUED".equals(workType)||"NOT_FACTORY_ASSIGNED".equals(workType)||"FACTORY_ASSIGNED".equals(workType)) {
                    return "预警";
                }
            }else{
                if ("QUEUED".equals(workType)||"NOT_FACTORY_ASSIGNED".equals(workType)||"FACTORY_ASSIGNED".equals(workType)){
                    return "预警";
                }
                if ("CALLING".equals(workType)) {
                    return "紧急";
                }
                if ("WORKING".equals(workType) || "NOT_ASSIGNED".equals(workType)) {
                    return "特急";
                }
            }
        }
        return "正常";
    }

    /**
     * 获取车辆作业状态
     */
    private String getVehicleWorkStatus(String segNo, String carTraceNo) {
        try {
            // 查询车辆在排队表中的状态
            HashMap<String, Object> queueQuery = new HashMap<>();
            queueQuery.put("segNo", segNo);
            queueQuery.put("carTraceNo", carTraceNo);

            if (StringUtils.isNotBlank(carTraceNo)) {
                // 查询LIRL0401排队表
                @SuppressWarnings("unchecked")
                List<HashMap<String, Object>> queueList = this.dao.query(LIRL0401.QUERY_INFO, queueQuery);
                if (CollectionUtils.isNotEmpty(queueList)) {
                    return "排队中";
                }

                // 查询LIRL0402叫号表
                @SuppressWarnings("unchecked")
                List<LIRL0402> callList = this.dao.query(LIRL0402.QUERY, queueQuery);
                if (CollectionUtils.isNotEmpty(callList)) {
                    return "叫号中";
                }

                // 查询LIRL0301车辆跟踪表判断是否作业中
                HashMap<String, Object> traceQuery = new HashMap<>();
                traceQuery.put("segNo", segNo);
                traceQuery.put("carTraceNo", carTraceNo);
                @SuppressWarnings("unchecked")
                List<LIRL0301> traceList = this.dao.query(LIRL0301.QUERY, traceQuery);
                if (CollectionUtils.isNotEmpty(traceList)) {
                    String status = (String) traceList.get(0).getStatus();
                    if ("30".equals(status)) {
                        return "作业中";
                    } else if ("40".equals(status)) {
                        //判断是否装卸完成
                        List<LIRL0407> query = this.dao.query(LIRL0407.QUERY, traceQuery);
                        if (CollectionUtils.isNotEmpty(query)) {
                            String nextTatget = query.get(0).getNextTatget();
                            if ("20".equals(nextTatget)) {
                                return "作业完成未出厂";
                            }
                        }
                    } else {
                        return "排队中";
                    }
                }
                //判断是否进厂
                HashMap<String, Object> query0301 = new HashMap<>();
                query0301.put("segNo", segNo);
                query0301.put("carTraceNo", carTraceNo);
                @SuppressWarnings("unchecked")
                List<LIRL0301> result = this.dao.query(LIRL0301.QUERY, query0301);
                if (CollectionUtils.isNotEmpty(result) && StringUtils.isNotBlank(carTraceNo)) {
                    return "进厂已配单未启动排队";
                } else {
                    return "未进厂已配单";
                }
            }else {
                return "未进厂已配单";
            }
        } catch (Exception e) {
            logger.error("获取车辆作业状态失败，carTraceNo: {}", carTraceNo, e);
            return "状态未知";
        }
    }

    /**
     * 检查车辆是否已出厂
     */
    private boolean checkVehicleOutFactory(String segNo, String vehicleNo, String carTraceNo) {
        if (StringUtils.isBlank(vehicleNo)) {
            return false;
        }

        try {
            HashMap<String, Object> query = new HashMap<>();
            query.put("segNo", segNo);
            query.put("vehicleNo", vehicleNo);
            query.put("carTraceNo", carTraceNo);
            @SuppressWarnings("unchecked")
            List<HashMap<String, Object>> result = this.dao.query(LIRL0311.QUERY, query);

            if (CollectionUtils.isNotEmpty(result)&&StringUtils.isNotBlank(carTraceNo)) {
                String status = (String) result.get(0).get("status");
                return "50".equals(status); // 50为车辆出厂状态
            }
            return false;
        } catch (Exception e) {
            logger.error("检查车辆出厂状态失败，vehicleNo: {}", vehicleNo, e);
            return false;
        }
    }

    /**
     * 记录到跟踪表(LIRL0509)
     */
    private void recordToTrackingTable(String segNo, String ladingBillId, String vehicleNo,
                                       String deliveryStatus, String d_userNum, String d_userName,
                                       String tproviderId, String tproviderName, String totalWeight, String transportUnit, String requireFinishDate, String workStatus, BigDecimal netWeight, String allocateVehicleNo, String carTraceNo, String remainingTimeDisplay) {

        try {
            // 只记录预警、紧急、特急、已超期状态的数据
            if (!"预警".equals(deliveryStatus) && !"紧急".equals(deliveryStatus) &&
                    !"特急".equals(deliveryStatus)) {
                return;
            }

            // 查询是否已存在记录
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("ladingBillId", ladingBillId);
            queryMap.put("vehicleNo", vehicleNo);
            queryMap.put("status", "20");
//            queryMap.put("deliveryStatus", getDeliveryStatusCode(deliveryStatus));

            @SuppressWarnings("unchecked")
            List<LIRL0509> existingRecords = this.dao.query(LIRL0509.QUERY, queryMap);

            if (CollectionUtils.isEmpty(existingRecords)) {
                // 插入新记录
                HashMap<String, Object> insertMap = new HashMap<>();
                insertMap.put("segNo", segNo);
                insertMap.put("ladingBillId", ladingBillId);
                insertMap.put("vehicleNo", vehicleNo);
                insertMap.put("customerName", tproviderName);
                insertMap.put("customerId", tproviderId);
                insertMap.put("d_userNum", d_userNum);
                insertMap.put("d_userNumName", d_userName);
                insertMap.put("ladingWeight", new BigDecimal(totalWeight));
                insertMap.put("dispensingQuantity", netWeight);
                insertMap.put("requireFinishDate", requireFinishDate);
                String jobStatusCode = getJobStatusCode(workStatus);
                insertMap.put("jobStatus", jobStatusCode);
                insertMap.put("deliveryStatus", getDeliveryStatusCode(deliveryStatus));
                insertMap.put("recCreator","system");
                insertMap.put("recCreatorName","系统管理员");
                insertMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                insertMap.put("recRevisor","system");
                insertMap.put("recRevisorName","系统管理员");
                insertMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                insertMap.put("archiveFlag","0");
                insertMap.put("delFlag","0");
                insertMap.put("remark","");
                insertMap.put("uuid", UUIDUtils.getUUID());
                insertMap.put("status", "20");//生效
                insertMap.put("tenantId", " ");//生效
                insertMap.put("carTraceNo", carTraceNo);//生效
                insertMap.put("allocateVehicleNo", allocateVehicleNo);//生效
                insertMap.put("remainingTimeDisplay", remainingTimeDisplay);//生效
                if (!"00".equals(jobStatusCode)) {
                    this.dao.insert(LIRL0509.INSERT, insertMap);
                }
                logger.info("新增厂内物流跟踪记录 - 提单号: {}, 车牌: {}, 状态: {}", ladingBillId, vehicleNo, deliveryStatus);
            }else {
                //实时修改截止时间
                HashMap<String, Object> insertMap = new HashMap<>();
                insertMap.put("segNo", segNo);
                insertMap.put("ladingBillId", ladingBillId);
                insertMap.put("vehicleNo", vehicleNo);
                insertMap.put("requireFinishDate", requireFinishDate);
                insertMap.put("deliveryStatus", getDeliveryStatusCode(deliveryStatus));
                insertMap.put("recRevisor","system");
                insertMap.put("recRevisorName","系统管理员");
                insertMap.put("recReviseTime",DateUtil.curDateTimeStr14());
                insertMap.put("archiveFlag","0");
                insertMap.put("delFlag","0");
                insertMap.put("remark","");
                insertMap.put("carTraceNo", carTraceNo);//生效
                insertMap.put("allocateVehicleNo", allocateVehicleNo);//生效
                insertMap.put("remainingTimeDisplay", remainingTimeDisplay);//生效
                insertMap.put("uuid",existingRecords.get(0).getUuid());//生效
                String jobStatusCode = getJobStatusCode(workStatus);
                insertMap.put("jobStatus", jobStatusCode);
                this.dao.update(LIRL0509.UPDATE_REMAINING_TIME_DISPLAY, insertMap);
                logger.info("修改厂内物流跟踪记录 - 提单号: {}, 车牌: {}, 状态: {}", ladingBillId, vehicleNo, deliveryStatus);
            }

        } catch (Exception e) {
            logger.error("记录跟踪表失败，提单号: {}", ladingBillId, e);
        }
    }

    /**
     * 获取交付状态代码
     */
    private String getDeliveryStatusCode(String deliveryStatus) {
        switch (deliveryStatus) {
            case "预警": return "10";
            case "紧急": return "20";
            case "特急": return "30";
            case "已超期": return "40";
            default: return "00";
        }
    }

    /**
     * 获取交付状态代码
     */
    private String getJobStatusCode(String deliveryStatus) {
        switch (deliveryStatus) {
            case "未配单": return "10";
            case "未进厂已配单": return "20";
            case "进厂已配单未启动排队": return "30";
            case "排队中": return "40";
            case "已叫号": return "50";
            case "作业中": return "60";
            case "作业完成未出厂": return "70";
            default: return "00";
        }
    }
}