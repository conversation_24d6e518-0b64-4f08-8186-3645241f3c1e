/**
* Generate time : 2024-11-28 14:53:59
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids0602
* 
*/
public class Tlids0602 extends DaoEPBase {

                private String segNo = " ";		/* 系统账套*/
                private String unitCode = " ";		/* 业务单元代代码*/
                private String benchmarkFactoryArea = " ";		/* 基准厂区*/
                private String pollingFactoryArea = " ";		/* 轮询厂区*/
                private String status = " ";		/* 状态(启用：10、停用：20)*/
                private String warehouseCode = " ";		/* 仓库代码*/
                private String benchmarkCrossRegional = " ";		/* 基准跨区*/
                private String pollingAcrossRegions = " ";		/* 轮询跨区*/
                private String pollingSchemeNumber = " ";		/* 轮询方案编号*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 归档标记*/
                private String tenantUser = " ";		/* 租户*/
                private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
                private String uuid = " ";		/* ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("benchmarkFactoryArea");
        eiColumn.setDescName("基准厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pollingFactoryArea");
        eiColumn.setDescName("轮询厂区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(启用：10、停用：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("benchmarkCrossRegional");
        eiColumn.setDescName("基准跨区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pollingAcrossRegions");
        eiColumn.setDescName("轮询跨区");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pollingSchemeNumber");
        eiColumn.setDescName("轮询方案编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tlids0602() {
initMetaData();
}

        /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the benchmarkFactoryArea - 基准厂区
        * @return the benchmarkFactoryArea
        */
        public String getBenchmarkFactoryArea() {
        return this.benchmarkFactoryArea;
        }

        /**
        * set the benchmarkFactoryArea - 基准厂区
        */
        public void setBenchmarkFactoryArea(String benchmarkFactoryArea) {
        this.benchmarkFactoryArea = benchmarkFactoryArea;
        }
        /**
        * get the pollingFactoryArea - 轮询厂区
        * @return the pollingFactoryArea
        */
        public String getPollingFactoryArea() {
        return this.pollingFactoryArea;
        }

        /**
        * set the pollingFactoryArea - 轮询厂区
        */
        public void setPollingFactoryArea(String pollingFactoryArea) {
        this.pollingFactoryArea = pollingFactoryArea;
        }
        /**
        * get the status - 状态(启用：10、停用：20)
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态(启用：10、停用：20)
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the warehouseCode - 仓库代码
        * @return the warehouseCode
        */
        public String getWarehouseCode() {
        return this.warehouseCode;
        }

        /**
        * set the warehouseCode - 仓库代码
        */
        public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
        }
        /**
        * get the benchmarkCrossRegional - 基准跨区
        * @return the benchmarkCrossRegional
        */
        public String getBenchmarkCrossRegional() {
        return this.benchmarkCrossRegional;
        }

        /**
        * set the benchmarkCrossRegional - 基准跨区
        */
        public void setBenchmarkCrossRegional(String benchmarkCrossRegional) {
        this.benchmarkCrossRegional = benchmarkCrossRegional;
        }
        /**
        * get the pollingAcrossRegions - 轮询跨区
        * @return the pollingAcrossRegions
        */
        public String getPollingAcrossRegions() {
        return this.pollingAcrossRegions;
        }

        /**
        * set the pollingAcrossRegions - 轮询跨区
        */
        public void setPollingAcrossRegions(String pollingAcrossRegions) {
        this.pollingAcrossRegions = pollingAcrossRegions;
        }
        /**
        * get the pollingSchemeNumber - 轮询方案编号
        * @return the pollingSchemeNumber
        */
        public String getPollingSchemeNumber() {
        return this.pollingSchemeNumber;
        }

        /**
        * set the pollingSchemeNumber - 轮询方案编号
        */
        public void setPollingSchemeNumber(String pollingSchemeNumber) {
        this.pollingSchemeNumber = pollingSchemeNumber;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the delFlag - 删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the uuid - ID
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - ID
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setBenchmarkFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("benchmarkFactoryArea")), benchmarkFactoryArea));
                setPollingFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pollingFactoryArea")), pollingFactoryArea));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
                setBenchmarkCrossRegional(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("benchmarkCrossRegional")), benchmarkCrossRegional));
                setPollingAcrossRegions(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pollingAcrossRegions")), pollingAcrossRegions));
                setPollingSchemeNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pollingSchemeNumber")), pollingSchemeNumber));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("benchmarkFactoryArea",StringUtils.toString(benchmarkFactoryArea, eiMetadata.getMeta("benchmarkFactoryArea")));
                map.put("pollingFactoryArea",StringUtils.toString(pollingFactoryArea, eiMetadata.getMeta("pollingFactoryArea")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
                map.put("benchmarkCrossRegional",StringUtils.toString(benchmarkCrossRegional, eiMetadata.getMeta("benchmarkCrossRegional")));
                map.put("pollingAcrossRegions",StringUtils.toString(pollingAcrossRegions, eiMetadata.getMeta("pollingAcrossRegions")));
                map.put("pollingSchemeNumber",StringUtils.toString(pollingSchemeNumber, eiMetadata.getMeta("pollingSchemeNumber")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));

return map;

}
}