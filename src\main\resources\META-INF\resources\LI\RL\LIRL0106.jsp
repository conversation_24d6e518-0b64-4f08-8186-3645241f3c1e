<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
<jsp:attribute name="header">
           <link rel="stylesheet" href="${ctx}/common/css/LIRL0106.css">
    </jsp:attribute>
    <jsp:body>
    <div style="display: flex; ">
        <div style="padding: 10px;" id="gongxu">
        </div>
    </div>
    <div id="loading" class="k-loading-mask" style="width: 100%; height: 100%; top: 40px; left: 0px;display: none;z-index:9999999">
        <span class="k-loading-text">Loading...</span>
    </div>
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8"
                             readonly="true" backFillFieldIds="inqu_status-0-segNo"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" required="true"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-weekDays" cname="星期几" colWidth="3" disabled="true"
                        required="true" type="hidden"/>
        </div>
    </EF:EFRegion>
    <div class="row">
        <div class="col-md-4">
            <EF:EFRegion id="result" title="结果集">
                <EF:EFGrid blockId="result" autoDraw="no" serviceName="LIRL0106" queryMethod="query"
                           autoBind="false" isFloat="true" personal="true" sort="all">
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>
                    <EF:EFColumn ename="status" cname="状态" align="left" enable="false" hidden="true" />
                    <EF:EFColumn ename="sfsx" cname="是否生效" align="left" enable="false"/>
                    <%--<EF:EFComboColumn ename="status" cname="状态" align="center">
                        &lt;%&ndash;<EF:EFOptions blockId="testType1Block" textField="codeDesc" valueField="codeValue"/>&ndash;%&gt;
                        <EF:EFOption label="" value="10"/>
                        <EF:EFOption label="\u2714" value="20"/>
                    </EF:EFComboColumn>--%>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="remark" cname="备注" required="false" align="left" hidden="true"/>
                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div class="col-md-4">
            <EF:EFRegion id="result2" title="结果集">
                <EF:EFGrid blockId="result2" autoDraw="no" serviceName="LIRL0106" queryMethod="query2"
                           autoBind="false" isFloat="true" personal="true" sort="all">
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>
                    <EF:EFColumn ename="status" cname="状态" align="left" enable="false" hidden="true" />
                    <EF:EFColumn ename="sfsx" cname="是否生效" align="left" enable="false"/>
                    <%--<EF:EFComboColumn ename="status" cname="状态" align="center">
                        &lt;%&ndash;<EF:EFOptions blockId="testType1Block" textField="codeDesc" valueField="codeValue"/>&ndash;%&gt;
                        <EF:EFOption label="" value="10"/>
                        <EF:EFOption label="\u2714" value="20"/>
                    </EF:EFComboColumn>--%>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="remark" cname="备注" required="false" align="left" hidden="true"/>
                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>
        <div class="col-md-4">
            <EF:EFRegion id="result3" title="结果集">
                <EF:EFGrid blockId="result3" autoDraw="no" serviceName="LIRL0106" queryMethod="query3"
                           autoBind="false" isFloat="true" personal="true" sort="all">
                    <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="segName" cname="业务单元简称" enable="false" align="center" sort="flase" hidden="true"/>
                    <EF:EFColumn ename="segNo" cname="系统账套" enable="false" align="center" hidden="true"/>
                    <EF:EFColumn ename="uuid" cname="流水号" align="center" enable="false" hidden="true"/>
                    <EF:EFColumn ename="reservationTime" cname="预约时段" align="center" enable="false"/>
                    <EF:EFColumn ename="status" cname="状态" align="left" enable="false" hidden="true" />
                    <EF:EFColumn ename="sfsx" cname="是否生效" align="left" enable="false"/>
                    <%--<EF:EFComboColumn ename="status" cname="状态" align="center">
                        &lt;%&ndash;<EF:EFOptions blockId="testType1Block" textField="codeDesc" valueField="codeValue"/>&ndash;%&gt;
                        <EF:EFOption label="" value="10"/>
                        <EF:EFOption label="\u2714" value="20"/>
                    </EF:EFComboColumn>--%>
                    <EF:EFColumn ename="recCreator" cname="创建人" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recCreatorName" cname="创建人姓名" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recCreateTime" cname="创建时间" width="150" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recRevisor" cname="修改人" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recRevisorName" cname="修改人姓名" width="100" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="recReviseTime" cname="修改时间" width="150" enable="false"
                                 readonly="true" align="center" hidden="true"/>
                    <EF:EFColumn ename="remark" cname="备注" required="false" align="left" hidden="true"/>
                    <EF:EFColumn ename="tenantUser" cname="租户" align="center" hidden="true"/>
                    <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" hidden="true"/>
                </EF:EFGrid>
            </EF:EFRegion>
        </div>

    </div>


    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"></EF:EFWindow>
    </jsp:body>
</EF:EFPage>
