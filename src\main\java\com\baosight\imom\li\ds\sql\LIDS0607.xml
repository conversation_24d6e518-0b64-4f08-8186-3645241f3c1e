<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-28 14:53:59
       Version :  1.0
    tableName :meli.tlids0602
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL,
     BENCHMARK_FACTORY_AREA  VARCHAR   NOT NULL,
     POLLING_FACTORY_AREA  VARCHAR   NOT NULL,
     STATUS  VARCHAR   NOT NULL,
     WAREHOUSE_CODE  VARCHAR,
     <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CROSS_REGIONAL  VARCHAR,
     POLLING_ACROSS_REGIONS  VARCHAR,
     POLLING_SCHEME_NUMBER  VARCHAR,
     REC_CREATOR  VARCHAR,
     REC_CREATOR_NAME  VARCHAR,
     REC_CREATE_TIME  VARCHAR,
     REC_REVISOR  VARCHAR,
     REC_REVISOR_NAME  VARCHAR,
     REC_REVISE_TIME  VARCHAR,
     ARCHIVE_FLAG  VARCHAR,
     TENANT_USER  VARCHAR,
     DEL_FLAG  SMALLINT,
     UUID  VARCHAR
-->
<sqlMap namespace="LIDS0607">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MAX(SEG_NO) AS segNo,
        MAX(UNIT_CODE) AS unitCode,
        MAX(WAREHOUSE_CODE) AS warehouseCode,
        MAX(WAREHOUSE_NAME) AS warehouseName,
        MAX(FACTORY_AREA) AS factoryArea,
        MAX(FACTORY_AREA_NAME) AS factoryAreaName,
        MAX(FACTORY_BUILDING) AS factoryBuilding,
        MAX(FACTORY_BUILDING_NAME) AS factoryBuildingName,
        CROSS_AREA as crossArea,
        MAX(CROSS_AREA_NAME) AS crossAreaName,
        ROUND(SUM(X_DESTINATION - X_INITIAL_POINT) / 1.5 / 100) AS beExpectedTo,

        (select
        count(*)
        from
        meli.tlids0605 s
        where
        1=1
        and SEG_NO = 'JC000000'
        and DEL_FLAG = '0'
        and USE_STATUS in ('20','30')
        and s.CROSS_AREA = t.CROSS_AREA) as current,

        ROUND(SUM(X_DESTINATION - X_INITIAL_POINT) / 1.5 / 100) - (select
        count(*)
        from
        meli.tlids0605 s
        where
        1=1
        and SEG_NO = t.SEG_NO
        and DEL_FLAG = '0'
        and USE_STATUS in ('20','30')
        and s.CROSS_AREA = t.CROSS_AREA) as remaining

        FROM
        meli.tlids0601 t
        WHERE
        SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA LIKE '%factoryArea%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING LIKE '%factoryBuilding%'
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA LIKE '%crossArea%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        AND IF_PLAN_FLAG = '0'
        and DEL_FLAG = '0'
        GROUP BY
        CROSS_AREA
    </select>

    <select id="count" resultClass="int">
        SELECT
        COUNT(DISTINCT CROSS_AREA)
        FROM
        meli.tlids0601 t
        WHERE
        SEG_NO = 'JC000000'
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA LIKE '%factoryArea%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING LIKE '%factoryBuilding%'
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA LIKE '%crossArea%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        AND IF_PLAN_FLAG = '0'
        and DEL_FLAG = '0'
    </select>

    <select id="queryLitter" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MAX(SEG_NO) AS segNo,
        MAX(UNIT_CODE) AS unitCode,
        MAX(WAREHOUSE_CODE) AS warehouseCode,
        MAX(WAREHOUSE_NAME) AS warehouseName,
        MAX(FACTORY_AREA) AS factoryArea,
        MAX(FACTORY_AREA_NAME) AS factoryAreaName,
        MAX(FACTORY_BUILDING) AS factoryBuilding,
        MAX(FACTORY_BUILDING_NAME) AS factoryBuildingName,
        MAX(CROSS_AREA) as crossArea,
        MAX(CROSS_AREA_NAME) AS crossAreaName,
        MAX(LOCATION_ID) AS locationId,
        MAX(LOCATION_NAME) AS locationName,
        ROUND(SUM(X_DESTINATION - X_INITIAL_POINT) / 1.5 / 100) AS beExpectedTo,
        (select
        count(*)
        from
        meli.tlids0605 s
        where
        1=1
        and SEG_NO = 'JC000000'
        and DEL_FLAG = '0'
        and USE_STATUS in ('20','30')
        and s.LOCATION_ID = t.LOCATION_ID) as current,


        ROUND(SUM(X_DESTINATION - X_INITIAL_POINT) / 1.5 / 100) - (select
        count(*)
        from
        meli.tlids0605 s
        where
        1=1
        and SEG_NO = #segNo#
        and DEL_FLAG = '0'
        and USE_STATUS in ('20','30')
        and s.LOCATION_ID = t.LOCATION_ID) as remaining
        FROM
        meli.tlids0601 t
        WHERE
        SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA LIKE '%factoryArea%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING LIKE '%factoryBuilding%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        AND IF_PLAN_FLAG = '0'
        and DEL_FLAG = '0'
        GROUP BY
        LOCATION_ID
    </select>

    <select id="countLitter" resultClass="int">
        SELECT
        COUNT(DISTINCT LOCATION_ID)
        FROM
        meli.tlids0601 t
        WHERE
        SEG_NO = #segNo#
        <isNotEmpty prepend=" AND " property="factoryArea">
            FACTORY_AREA LIKE '%factoryArea%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="factoryBuilding">
            FACTORY_BUILDING LIKE '%factoryBuilding%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="crossArea">
            CROSS_AREA = #crossArea#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="locationId">
            LOCATION_ID = #locationId#
        </isNotEmpty>
        AND IF_PLAN_FLAG = '0'
        and DEL_FLAG = '0'

    </select>
</sqlMap>