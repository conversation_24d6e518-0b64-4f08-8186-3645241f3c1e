package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.ds.domain.*;
import com.baosight.imom.li.rl.dao.LIRL0101;
import com.baosight.imom.li.rl.dao.LIRL0304;
import com.baosight.imom.li.rl.dao.LIRL0503;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.imom.vi.pm.domain.VIPM0009;
import com.baosight.imom.xt.ss.domain.XTSS04;
import com.baosight.imom.xt.ss.domain.XTSS06;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import sun.security.jca.ServiceId;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.stream.Collectors;

/**
 * 行车作业清单管理
 */
public class ServiceLIDS1101 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS1101().eiMetadata);
        inInfo.addBlock("subResult").addBlockMeta(new LIDS1102().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS1101.QUERY, new LIDS1101());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 查询作业实绩子项
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySubResult(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS1102.QUERY, new LIDS1102(), false, new LIDS1102().eiMetadata, EiConstant.resultBlock, "subResult", "subResult");
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 卸货入库时车辆叫号生成行作业清单
     *
     * @param inInfo
     * @return outInfo
     */
    public EiInfo WarehousingCreateCraneOrderId(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //根据配单号+账套+依据凭单号不为空的条件查询meli.tlirl0503 配单信息维护子表可查出叫号车辆上有那些需要生成行车作业清单的捆包
            Map attr = inInfo.getAttr();
            String segNo = MapUtils.getString(attr, "segNo", "");
            //配车单号
            String allocateVehicleNo = MapUtils.getString(attr, "allocateVehicleNo", "");
            //装卸点编码
            String loadingPointNo = MapUtils.getString(attr, "loadingPointNo", "");
            HashMap queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("allocateVehicleNo", allocateVehicleNo);
            queryMap.put("outPackFlag", "0");
            queryMap.put("voucherNumFlag", "0");
            List<LIRL0503> packList = this.dao.query("LIRL0503.query", queryMap);
            String[] batch = {segNo.substring(0, 2)};
            //同一配车单一个批次
            String batchId = SequenceGenerator.getNextSequence("TLIDS_SEQ1103", batch);
            for (LIRL0503 lirl0503 : packList) {
                 Map packMap = lirl0503.toMap();
                //主表字段
                LIDS1101 lids1101 = new LIDS1101();
                String[] args = {segNo.substring(0, 2)};
                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                lids1101.setCraneOrderId(applyId);
                lids1101.setSegNo((String) packMap.get("segNo"));
                lids1101.setUnitCode((String) packMap.get("unitCode"));
                //清单来源 10卸货入库
                lids1101.setListSource("10");
                //依据凭单号（车辆跟踪号）
                lids1101.setVoucherNum(MapUtils.getString(attr, "carTraceNo", ""));
                //批次号
                lids1101.setBatchNumber(batchId);
                //顺序号
                lids1101.setSerialNumber("");
                //机组代码
                lids1101.setMachineCode("");
                //机组名称
                lids1101.setMachineName("");
                //拆包区编号
                lids1101.setUnpackAreaId("");
                //拆包区名称
                lids1101.setUnpackAreaName("");
                //模具ID
                lids1101.setMouldId("");
                //模具名称
                lids1101.setMouldName("");
                //作业开始时间
                lids1101.setStartTime("");
                //作业结束时间
                lids1101.setEndTime("");
                //作业时间
                lids1101.setJobTime("");
                //根据库位推荐的库位找对应的跨区，查询当前跨区下的所有行车
                String craneId = "";
                String craneName = "";
                String locationId = "";
                String locationName = "";
                //净重
                BigDecimal netWeight = new BigDecimal("0");
                String packId = MapUtils.getString(packMap, "packId", "");
                queryMap.put("packId", packId);
                List<LIDS0605> crossAreaList = this.dao.query(LIDS0605.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(crossAreaList)) {
                    locationId = crossAreaList.get(0).getLocationId();
                    locationName = crossAreaList.get(0).getLocationName();
                    netWeight = crossAreaList.get(0).getPutinWeight();
                    queryMap.put("crossArea", crossAreaList.get(0).getCrossArea());
                    queryMap.put("factoryArea", crossAreaList.get(0).getFactoryArea());
                    queryMap.put("factoryBuilding", crossAreaList.get(0).getFactoryBuilding());
                    queryMap.put("craneDuty", "10");
                    List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(craneList)) {
                        craneId = craneList.stream()
                                .map(LIDS0301::getCraneId)
                                .collect(Collectors.joining(","));
                        craneName = craneList.stream()
                                .map(LIDS0301::getCraneName)
                                .collect(Collectors.joining(","));
                    }
                }
                //行车编号
                lids1101.setCraneId(craneId);
                //行车名称
                lids1101.setCraneName(craneName);
                String startAreaCode = "";
                String startAreaName = "";
                //根据叫号表的装卸点查询装卸通道
                queryMap.put("handPointId", loadingPointNo);
                List<LIRL0304> handPointList = this.dao.query(LIRL0304.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(handPointList)) {
                    startAreaCode=handPointList.get(0).getLoadingChannelId();
                    startAreaName=handPointList.get(0).getLoadingChannelName();
                }
                //起始区域类型
                lids1101.setStartAreaType("40");
                //起始区域类型代码
                lids1101.setStartAreaCode(startAreaCode);
                //起始区域类型名称
                lids1101.setStartAreaName(startAreaName);
                //终到区域类型
                lids1101.setEndAreaType("10");
                //终到区域类型代码
                lids1101.setEndAreaCode(locationId);
                //终到区域类型名称
                lids1101.setEndAreaName(locationName);
                //判断当前卸货入库的捆包的是否已经生成过行车作业清单
                HashMap orderMap = new HashMap();
                orderMap.put("segNo", segNo);
                orderMap.put("packId", packId);
                orderMap.put("listSource", "10");
                orderMap.put("craneResultId", "craneResultId");
                List<LIDS1101> processList = this.dao.query(LIDS1101.QUERY_CRANE_ORDER_ID, orderMap);
                if (CollectionUtils.isNotEmpty(processList)) {
                    for (LIDS1101 lids1101s : processList) {
                        lids1101s.setStatus("00");
                        lids1101s.setDelFlag(1);
                        lids1101s.setRecRevisor("system");
                        lids1101s.setRecRevisorName("系统自动撤销");
                        lids1101s.setRecReviseTime(DateUtils.curDateTimeStr14());
                        this.dao.update(LIDS1101.DELETE_FLAG, lids1101s);
                        HashMap deleteMap = new HashMap();
                        deleteMap.put("segNo", segNo);
                        deleteMap.put("craneOrderId", lids1101s.getCraneOrderId());
                        List<LIDS1102> subList = this.dao.query(LIDS1102.QUERY, deleteMap);
                        if (CollectionUtils.isNotEmpty(subList)) {
                            for (LIDS1102 lids1102 : subList) {
                                lids1102.setStatus("00");
                                lids1102.setDelFlag(1);
                                lids1102.setRecRevisor("system");
                                lids1102.setRecRevisorName("系统自动撤销");
                                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                this.dao.update(LIDS1102.DELETE_FLAG, lids1102);
                            }
                        }
                    }
                }
                //状态
                lids1101.setStatus("20");
                //记录创建人
                lids1101.setRecCreator("system");
                //记录创建人姓名
                lids1101.setRecCreatorName("system");
                //记录创建时间
                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                //记录修改人
                lids1101.setRecRevisor("system");
                //记录修改人姓名
                lids1101.setRecRevisorName("system");
                //记录修改时间
                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                //归档标记
                lids1101.setArchiveFlag("");
                //租户
                lids1101.setTenantUser("");
                //删除标记
                lids1101.setDelFlag(0);
                //ID
                lids1101.setUuid(UUIDUtils.getUUID());
                dao.insert(LIDS1101.INSERT, lids1101);
                LIDS1102 lids1102 = new LIDS1102();
                String[] args1 = {applyId};
                String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                //行车作业清单子项
                lids1102.setCraneOrderId(applyId);
                lids1102.setSegNo(segNo);
                lids1102.setUnitCode(segNo);
                lids1102.setCraneOrderSubId(applySubId);
                //状态
                lids1102.setStatus("20");
                //捆包号
                lids1102.setPackId(packId);
                //入库计划获取不到标签号，暂时为空
                lids1102.setLabelId("");
                //净重
                lids1102.setNetWeight(netWeight);
                //数量
                lids1102.setQuantity(1);
                //记录创建人
                lids1102.setRecCreator("system");
                //记录创建人姓名
                lids1102.setRecCreatorName("system");
                //记录创建时间
                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                //记录修改人
                lids1102.setRecRevisor("system");
                //记录修改人姓名
                lids1102.setRecRevisorName("system");
                //记录修改时间
                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                //归档标记
                lids1102.setArchiveFlag("");
                //租户
                lids1102.setTenantUser("");
                //删除标记
                lids1102.setDelFlag(0);
                //ID
                lids1102.setUuid(UUIDUtils.getUUID());
                dao.insert(LIDS1102.INSERT, lids1102);
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            return outInfo;
        }
        return outInfo;
    }

    /**
     * 每天早上七点生成排产日期为36小时内的工单生成行车作业清单
     *
     * @param inInfo
     * @return outInfo
     */
    public EiInfo schedulingCreateCraneOrderId(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //根据当前时间查询大于等于当前时间并小于等于36小时后的时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String currentTime = LocalDateTime.now().format(formatter);
            //当前时间
            LocalDateTime dateTime = LocalDateTime.parse(currentTime, formatter);
            LocalDateTime futureDateTime = dateTime.plusHours(36);
            //36小时后
            String thirtySixHoursFromNow = futureDateTime.format(formatter);
            //查询排产自动生成行车作业清单的开关信息
            HashMap queryMap = new HashMap<>();
            queryMap.put("processSwitchName", "IF_CRETA_CRANE_ORDER_ID");
            List<HashMap> switchList = this.dao.query(XTSS04.QUERY_LIST, queryMap);
            if (CollectionUtils.isNotEmpty(switchList)) {
                for (HashMap xtss04 : switchList) {
                    String segNo = MapUtils.getString(xtss04, "segNo", "");
                    //查询该账套下参与自动生成行车作业清单的机组信息
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("configureKey", "MACHINE_CODE");
                    List<XTSS06> machineCodeList = this.dao.query(XTSS06.QUERY, queryMap);
                    String machineCode = "";
                    if (CollectionUtils.isNotEmpty(machineCodeList)) {
                        machineCode = machineCodeList.stream()
                                .map(item -> String.format("'%s'", item.getConfigureValue()))
                                .collect(Collectors.joining(","));
                    }
                    if (StringUtils.isNotBlank(machineCode)) {
                        queryMap.put("machineCodes", machineCode);
                    }
                    queryMap.put("currentTime", currentTime);
                    queryMap.put("thirtySixHoursFromNow", thirtySixHoursFromNow);
                    //根据账套和机组查询排产时间为36小时之内的工单
                    List<VIPM0009> processOrderIdList = this.dao.query(VIPM0009.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(processOrderIdList)) {
                        for (VIPM0009 vipm0009 : processOrderIdList) {
                            //判断当前工单是否已经生成过行车作业清单若之前生成过，先将原来的行车作业清单撤销，备注中标识是系统自动撤销(与手工撤销的做区分)
                            HashMap orderMap = new HashMap();
                            orderMap.put("segNo", segNo);
                            orderMap.put("voucherNum", vipm0009.getProcessOrderId());
                            orderMap.put("craneResultId", "craneResultId");
                            List<LIDS1101> processList = this.dao.query(LIDS1101.QUERY_CRANE_ORDER_ID, orderMap);
                            if (CollectionUtils.isNotEmpty(processList)) {
                                for (LIDS1101 lids1101 : processList) {
                                    lids1101.setStatus("00");
                                    lids1101.setDelFlag(1);
                                    lids1101.setRecRevisor("system");
                                    lids1101.setRecRevisorName("系统自动撤销");
                                    lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                    this.dao.update(LIDS1101.DELETE_FLAG, lids1101);
                                    HashMap deleteMap = new HashMap();
                                    deleteMap.put("segNo", segNo);
                                    deleteMap.put("craneOrderId", lids1101.getCraneOrderId());
                                    List<LIDS1102> subList = this.dao.query(LIDS1102.QUERY, deleteMap);
                                    if (CollectionUtils.isNotEmpty(subList)) {
                                        for (LIDS1102 lids1102 : subList) {
                                            lids1102.setStatus("00");
                                            lids1102.setDelFlag(1);
                                            lids1102.setRecRevisor("system");
                                            lids1102.setRecRevisorName("系统自动撤销");
                                            lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                            this.dao.update(LIDS1102.DELETE_FLAG, lids1102);
                                        }
                                    }
                                }
                            }
                            //同一个工单同一批次
                            String[] batch = {segNo.substring(0, 2)};
                            String batchId = SequenceGenerator.getNextSequence("TLIDS_SEQ1103", batch);
                            String processOrderId = vipm0009.getProcessOrderId();
                            queryMap.clear();
                            queryMap.put("segNo", segNo);
                            queryMap.put("processOrderId", processOrderId);
                            //查询当前工单封锁的捆包
                            List<VIPM0008> packIdList = this.dao.query(VIPM0008.QUERY, queryMap);
                            if (CollectionUtils.isNotEmpty(packIdList)) {
                                for (VIPM0008 vipm0008 : packIdList) {
                                    String packId = vipm0008.getPackId();
                                    queryMap.clear();
                                    queryMap.put("segNo", segNo);
                                    queryMap.put("packId", packId);
                                    //查询实物库存表获取板卷类型
                                    List<LIDS0901> packList = dao.query(LIDS0901.QUERY_PACK_MESSAGE, queryMap);
                                    //顺序号
                                    Integer SerialNumber = 0;
                                    if (CollectionUtils.isNotEmpty(packList)) {
                                        //目前只生成一期厂房的的捆包
                                        HashMap locationIdMap = new HashMap<>();
                                        locationIdMap.put("locationId", packList.get(0).getAreaCode());
                                        int count = super.count(LIDS0601.COUNT_LOCATION_ID, locationIdMap);
                                        if (count == 1) {
                                            //根据捆包库位找对应的跨区，查询当前跨区下的所有行车
                                            queryMap.put("accurateLocationId", packList.get(0).getAreaCode());
                                            List<LIDS0601> locationList = this.dao.query(LIDS0601.QUERY, queryMap);
                                            String actionFlag = packList.get(0).getActionFlag();
                                            if ("0".equals(actionFlag)) {
                                                //捆包为板时，机组为翻板机、翻卷机、缠绕机不做管理
                                                //捆包为板时，机组非翻板机（目前投料是板的都是非翻板机，翻板的流程目前IMC没有体现）不生成行车作业清单，只接受行车作业实绩
                                            } else if ("1".equals(actionFlag)) {
                                                //捆包为卷时，判断当前捆包是否为下层捆包
                                                if ("1".equals(packList.get(0).getPosDirCode())) {
                                                    //捆包为下层，查询捆包上层是否有需要倒库的捆包
                                                    queryMap.clear();
                                                    queryMap.put("segNo", segNo);
                                                    queryMap.put("packId", packId);
                                                    queryMap.put("XPosition", packList.get(0).getX_position());
                                                    queryMap.put("YPosition", packList.get(0).getY_position());
                                                    queryMap.put("warehouseCode", packList.get(0).getWarehouseCode());
                                                    queryMap.put("areaCode", packList.get(0).getAreaCode());
                                                    List<LIDS0901> stockList = dao.query(LIDS0901.QUERY_RELATED_PACKS_BY_ADJACENT_IDS, queryMap);
                                                    if (CollectionUtils.isNotEmpty(stockList)) {
                                                        for (LIDS0901 lids0901 : stockList) {
                                                            //查询该捆包是否已经生成倒库的行车作业清单
                                                            HashMap alterationMap = new HashMap();
                                                            alterationMap.put("segNo", segNo);
                                                            alterationMap.put("listSource", "50");
                                                            alterationMap.put("packId", lids0901.getPackId());
                                                            int craneOrder = super.count(LIDS1101.COUNT_CRANE_ORDER, alterationMap);
                                                            if (craneOrder == 0) {
                                                                LIDS1101 lids1101 = new LIDS1101();
                                                                String[] args = {segNo.substring(0, 2)};
                                                                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                                lids1101.setCraneOrderId(applyId);
                                                                lids1101.setSegNo(segNo);
                                                                lids1101.setUnitCode(segNo);
                                                                //清单来源 50倒库
                                                                lids1101.setListSource("50");
                                                                //依据凭单号（生产工单号）
                                                                lids1101.setVoucherNum(processOrderId);
                                                                //批次号
                                                                lids1101.setBatchNumber(batchId);
                                                                //顺序号
                                                                SerialNumber++;
                                                                lids1101.setSerialNumber(SerialNumber.toString());
                                                                //机组代码
                                                                lids1101.setMachineCode("");
                                                                //机组名称
                                                                lids1101.setMachineName("");
                                                                //拆包区编号
                                                                lids1101.setUnpackAreaId("");
                                                                //拆包区名称
                                                                lids1101.setUnpackAreaName("");
                                                                //模具ID
                                                                lids1101.setMouldId("");
                                                                //模具名称
                                                                lids1101.setMouldName("");
                                                                //作业开始时间
                                                                lids1101.setStartTime("");
                                                                //作业结束时间
                                                                lids1101.setEndTime("");
                                                                //作业时间
                                                                lids1101.setJobTime("");
                                                                String craneId = "";
                                                                String craneName = "";
                                                                queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                                queryMap.put("craneDuty", "10");
                                                                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                                if (CollectionUtils.isNotEmpty(craneList)) {
                                                                    craneId = craneList.stream()
                                                                            .map(LIDS0301::getCraneId)
                                                                            .collect(Collectors.joining(","));
                                                                    craneName = craneList.stream()
                                                                            .map(LIDS0301::getCraneName)
                                                                            .collect(Collectors.joining(","));
                                                                }
                                                                //行车编号
                                                                lids1101.setCraneId(craneId);
                                                                //行车名称
                                                                lids1101.setCraneName(craneName);
                                                                //起始区域类型
                                                                lids1101.setStartAreaType("10");
                                                                //起始区域类型代码
                                                                lids1101.setStartAreaCode(lids0901.getAreaCode());
                                                                //起始区域类型名称
                                                                lids1101.setStartAreaName(lids0901.getAreaName());
                                                                //终到区域类型
                                                                lids1101.setEndAreaType("10");
                                                                //终到区域调用库位推荐获取
                                                                HashMap messageBody = new HashMap();
                                                                messageBody.put("mark","1");
                                                                messageBody.put("segNo",segNo);
                                                                messageBody.put("packId",packId);
                                                                EiInfo importInfo = new EiInfo();
                                                                importInfo.set("messageBody",messageBody);
                                                                importInfo.set(EiConstant.serviceName, "LIDS0606");
                                                                importInfo.set(EiConstant.methodName, "recommendedStorageLocation");
                                                                importInfo = XLocalManager.callNoTx(importInfo);
                                                                String endAreaCode = "";
                                                                String endAreaName = "";
                                                                if (importInfo.getStatus() == EiConstant.STATUS_DEFAULT){
                                                                    endAreaCode =(String) importInfo.getAttr().get("locationId");
                                                                    endAreaName =(String) importInfo.getAttr().get("locationName");
                                                                }else {
                                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                                    outInfo.setMsg("调用库位推荐接口失败，" + importInfo.getMsg());
                                                                    return outInfo;
                                                                }
                                                                //终到区域类型代码
                                                                lids1101.setEndAreaCode(endAreaCode);
                                                                //终到区域类型名称
                                                                lids1101.setEndAreaName(endAreaName);
                                                                //状态
                                                                lids1101.setStatus("20");
                                                                //记录创建人
                                                                lids1101.setRecCreator("system");
                                                                //记录创建人姓名
                                                                lids1101.setRecCreatorName("system");
                                                                //记录创建时间
                                                                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                                //记录修改人
                                                                lids1101.setRecRevisor("system");
                                                                //记录修改人姓名
                                                                lids1101.setRecRevisorName("system");
                                                                //记录修改时间
                                                                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                                //归档标记
                                                                lids1101.setArchiveFlag("");
                                                                //租户
                                                                lids1101.setTenantUser("");
                                                                //删除标记
                                                                lids1101.setDelFlag(0);
                                                                //ID
                                                                lids1101.setUuid(UUIDUtils.getUUID());
                                                                dao.insert(LIDS1101.INSERT, lids1101);
                                                                LIDS1102 lids1102 = new LIDS1102();
                                                                String[] args1 = {applyId};
                                                                String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                                lids1102.setCraneOrderId(applyId);
                                                                lids1102.setSegNo(segNo);
                                                                lids1102.setUnitCode(segNo);
                                                                //行车作业清单子项
                                                                lids1102.setCraneOrderSubId(applySubId);
                                                                //状态
                                                                lids1102.setStatus("20");
                                                                //捆包号
                                                                lids1102.setPackId(lids0901.getPackId());
                                                                //标签号
                                                                lids1102.setLabelId(lids0901.getLabelId());
                                                                //净重
                                                                lids1102.setNetWeight(lids0901.getCraneOperationWeight());
                                                                //数量
                                                                lids1102.setQuantity(lids0901.getQuantity());
                                                                //记录创建人
                                                                lids1102.setRecCreator("system");
                                                                //记录创建人姓名
                                                                lids1102.setRecCreatorName("system");
                                                                //记录创建时间
                                                                lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                                //记录修改人
                                                                lids1102.setRecRevisor("system");
                                                                //记录修改人姓名
                                                                lids1102.setRecRevisorName("system");
                                                                //记录修改时间
                                                                lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                                //归档标记
                                                                lids1102.setArchiveFlag("");
                                                                //租户
                                                                lids1102.setTenantUser("");
                                                                //删除标记
                                                                lids1102.setDelFlag(0);
                                                                //ID
                                                                lids1102.setUuid(UUIDUtils.getUUID());
                                                                dao.insert(LIDS1102.INSERT, lids1102);
                                                            }

                                                        }
                                                    }
                                                }
                                                //生成生产上料的行车作业清单
                                                //判断原料库位所在区域与机组拆包区所在区域是否为同一跨
                                                HashMap queryMachineCodeMap = new HashMap();
                                                queryMachineCodeMap.put("segNo", segNo);
                                                queryMachineCodeMap.put("machineCode", vipm0009.getMachineCode());
                                                List<Map> machineCodeCrossArea = this.dao.query(LIDS0701.QUERY_MACHINE_CODE_CROSS_AREA, queryMachineCodeMap);
                                                if (CollectionUtils.isNotEmpty(machineCodeCrossArea)) {
                                                    //拆包区跨区
                                                    String unpackCrossArea = (String) machineCodeCrossArea.get(0).get("unpackCrossArea");
                                                    if (unpackCrossArea.equals(locationList.get(0).getCrossArea())) {
                                                        //库位跨区与拆包区跨区一致，生成跨区到拆包区的行车作业清单
                                                        LIDS1101 lids1101 = new LIDS1101();
                                                        String[] args = {segNo.substring(0, 2)};
                                                        String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101.setCraneOrderId(applyId);
                                                        lids1101.setSegNo(segNo);
                                                        lids1101.setUnitCode(segNo);
                                                        //清单来源 30生产上料
                                                        lids1101.setListSource("30");
                                                        //依据凭单号（生产工单号）
                                                        lids1101.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101.setSerialNumber(SerialNumber.toString());
                                                        //机组代码
                                                        lids1101.setMachineCode("");
                                                        //机组名称
                                                        lids1101.setMachineName("");
                                                        //拆包区编号
                                                        lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //拆包区名称
                                                        lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //模具ID
                                                        lids1101.setMouldId("");
                                                        //模具名称
                                                        lids1101.setMouldName("");
                                                        //作业开始时间
                                                        lids1101.setStartTime("");
                                                        //作业结束时间
                                                        lids1101.setEndTime("");
                                                        //作业时间
                                                        lids1101.setJobTime("");
                                                        //根据库位找对应的跨区，查询当前跨区下的所有行车
                                                        String craneId = "";
                                                        String craneName = "";
                                                        queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList)) {
                                                            craneId = craneList.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName = craneList.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101.setCraneId(craneId);
                                                        //行车名称
                                                        lids1101.setCraneName(craneName);
                                                        //起始区域类型
                                                        lids1101.setStartAreaType("10");
                                                        //起始区域类型代码
                                                        lids1101.setStartAreaCode(packList.get(0).getAreaCode());
                                                        //起始区域类型名称
                                                        lids1101.setStartAreaName(packList.get(0).getAreaName());
                                                        //终到区域类型
                                                        lids1101.setEndAreaType("30");
                                                        //终到区域类型代码
                                                        lids1101.setEndAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //终到区域类型名称
                                                        lids1101.setEndAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //状态
                                                        lids1101.setStatus("20");
                                                        //记录创建人
                                                        lids1101.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101.setArchiveFlag("");
                                                        //租户
                                                        lids1101.setTenantUser("");
                                                        //删除标记
                                                        lids1101.setDelFlag(0);
                                                        //ID
                                                        lids1101.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101);
                                                        LIDS1102 lids1102 = new LIDS1102();
                                                        String[] args1 = {applyId};
                                                        String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                        lids1102.setCraneOrderId(applyId);
                                                        lids1102.setSegNo(segNo);
                                                        lids1102.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102.setCraneOrderSubId(applySubId);
                                                        //状态
                                                        lids1102.setStatus("20");
                                                        //捆包号
                                                        lids1102.setPackId(packId);
                                                        //标签号
                                                        lids1102.setLabelId(packList.get(0).getLabelId());
                                                        //净重
                                                        lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                        //数量
                                                        lids1102.setQuantity(packList.get(0).getQuantity());
                                                        //记录创建人
                                                        lids1102.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102.setArchiveFlag("");
                                                        //租户
                                                        lids1102.setTenantUser("");
                                                        //删除标记
                                                        lids1102.setDelFlag(0);
                                                        //ID
                                                        lids1102.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102);
                                                    } else {
                                                        //原料与拆包区所在跨区不为同一跨 需要生成 原料库位到过跨通道的行车作业清单和过跨通道到拆包区的行车作业清单
                                                        //原料库位到过跨通道的行车作业清单
                                                        LIDS1101 lids1101 = new LIDS1101();
                                                        String[] args = {segNo.substring(0, 2)};
                                                        String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101.setCraneOrderId(applyId);
                                                        lids1101.setSegNo(segNo);
                                                        lids1101.setUnitCode(segNo);
                                                        //清单来源 30生产上料
                                                        lids1101.setListSource("30");
                                                        //依据凭单号（生产工单号）
                                                        lids1101.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101.setSerialNumber(SerialNumber.toString());
                                                        //机组代码
                                                        lids1101.setMachineCode("");
                                                        //机组名称
                                                        lids1101.setMachineName("");
                                                        //拆包区编号
                                                        lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //拆包区名称
                                                        lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //模具ID
                                                        lids1101.setMouldId("");
                                                        //模具名称
                                                        lids1101.setMouldName("");
                                                        //作业开始时间
                                                        lids1101.setStartTime("");
                                                        //作业结束时间
                                                        lids1101.setEndTime("");
                                                        //作业时间
                                                        lids1101.setJobTime("");
                                                        //根据库位找对应的跨区，查询当前跨区下的所有行车
                                                        String craneId = "";
                                                        String craneName = "";
                                                        queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList)) {
                                                            craneId = craneList.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName = craneList.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101.setCraneId(craneId);
                                                        //行车名称
                                                        lids1101.setCraneName(craneName);
                                                        //起始区域类型
                                                        lids1101.setStartAreaType("10");
                                                        //起始区域类型代码
                                                        lids1101.setStartAreaCode(packList.get(0).getAreaCode());
                                                        //起始区域类型名称
                                                        lids1101.setStartAreaName(packList.get(0).getAreaName());
                                                        //终到区域类型
                                                        lids1101.setEndAreaType("20");
                                                        //根据厂区、厂房、跨区编码查询过跨小车
                                                        List<LIDS0401> transferCarIdList = this.dao.query(LIDS0401.QUERY_TRANSFER_CAR_ID, queryMap);
                                                        if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                            //终到区域类型代码
                                                            lids1101.setEndAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                            //终到区域类型名称
                                                            lids1101.setEndAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                        }
                                                        //状态
                                                        lids1101.setStatus("20");
                                                        //记录创建人
                                                        lids1101.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101.setArchiveFlag("");
                                                        //租户
                                                        lids1101.setTenantUser("");
                                                        //删除标记
                                                        lids1101.setDelFlag(0);
                                                        //ID
                                                        lids1101.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101);
                                                        LIDS1102 lids1102 = new LIDS1102();
                                                        String[] args1 = {applyId};
                                                        String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                        lids1102.setCraneOrderId(applyId);
                                                        lids1102.setSegNo(segNo);
                                                        lids1102.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102.setCraneOrderSubId(applySubId);
                                                        //状态
                                                        lids1102.setStatus("20");
                                                        //捆包号
                                                        lids1102.setPackId(packId);
                                                        //标签号
                                                        lids1102.setLabelId(packList.get(0).getLabelId());
                                                        //净重
                                                        lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                        //数量
                                                        lids1102.setQuantity(packList.get(0).getQuantity());
                                                        //记录创建人
                                                        lids1102.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102.setArchiveFlag("");
                                                        //租户
                                                        lids1102.setTenantUser("");
                                                        //删除标记
                                                        lids1102.setDelFlag(0);
                                                        //ID
                                                        lids1102.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102);
                                                        //过跨通道到拆包区的行车作业清单
                                                        LIDS1101 lids1101a = new LIDS1101();
                                                        String applyId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101a.setCraneOrderId(applyId2);
                                                        lids1101a.setSegNo(segNo);
                                                        lids1101a.setUnitCode(segNo);
                                                        //清单来源 30生产上料
                                                        lids1101a.setListSource("30");
                                                        //依据凭单号（生产工单号）
                                                        lids1101a.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101a.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101a.setSerialNumber(SerialNumber.toString());
                                                        //机组代码
                                                        lids1101a.setMachineCode("");
                                                        //机组名称
                                                        lids1101a.setMachineName("");
                                                        //拆包区编号
                                                        lids1101a.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //拆包区名称
                                                        lids1101a.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //模具ID
                                                        lids1101a.setMouldId("");
                                                        //模具名称
                                                        lids1101a.setMouldName("");
                                                        //作业开始时间
                                                        lids1101a.setStartTime("");
                                                        //作业结束时间
                                                        lids1101a.setEndTime("");
                                                        //作业时间
                                                        lids1101a.setJobTime("");
                                                        //根据拆包区找对应的跨区，查询当前跨区下的所有行车
                                                        String craneId1 = "";
                                                        String craneName1 = "";
                                                        queryMap.put("crossArea", unpackCrossArea);
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList1 = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList1)) {
                                                            craneId1 = craneList1.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName1 = craneList1.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101a.setCraneId(craneId1);
                                                        //行车名称
                                                        lids1101a.setCraneName(craneName1);
                                                        //起始区域类型
                                                        lids1101a.setStartAreaType("20");
                                                        if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                            //终到区域类型代码
                                                            lids1101a.setStartAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                            //终到区域类型名称
                                                            lids1101a.setStartAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                        }
                                                        //终到区域类型
                                                        lids1101a.setEndAreaType("30");
                                                        //终到区域类型代码
                                                        lids1101a.setEndAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //终到区域类型名称
                                                        lids1101a.setEndAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //状态
                                                        lids1101a.setStatus("20");
                                                        //记录创建人
                                                        lids1101a.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101a.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101a.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101a.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101a.setArchiveFlag("");
                                                        //租户
                                                        lids1101a.setTenantUser("");
                                                        //删除标记
                                                        lids1101a.setDelFlag(0);
                                                        //ID
                                                        lids1101a.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101a);
                                                        LIDS1102 lids1102a = new LIDS1102();
                                                        String[] args2 = {applyId2};
                                                        String applySubId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args2);
                                                        lids1102a.setCraneOrderId(applyId2);
                                                        lids1102a.setSegNo(segNo);
                                                        lids1102a.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102a.setCraneOrderSubId(applySubId2);
                                                        //状态
                                                        lids1102a.setStatus("20");
                                                        //捆包号
                                                        lids1102a.setPackId(packId);
                                                        //标签号
                                                        lids1102a.setLabelId(packList.get(0).getLabelId());
                                                        //净重
                                                        lids1102a.setNetWeight(packList.get(0).getNetWeight());
                                                        //数量
                                                        lids1102a.setQuantity(packList.get(0).getQuantity());
                                                        //记录创建人
                                                        lids1102a.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102a.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102a.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102a.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102a.setArchiveFlag("");
                                                        //租户
                                                        lids1102a.setTenantUser("");
                                                        //删除标记
                                                        lids1102a.setDelFlag(0);
                                                        //ID
                                                        lids1102a.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102a);
                                                    }
                                                    //判断拆包区与机组所在跨区是否为同一跨
                                                    if (unpackCrossArea.equals(machineCodeCrossArea.get(0).get("crossArea"))) {
                                                        //机组跨区与拆包区跨区一致，生成拆包区到机组上料区的行车作业清单
                                                        LIDS1101 lids1101 = new LIDS1101();
                                                        String[] args = {segNo.substring(0, 2)};
                                                        String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101.setCraneOrderId(applyId);
                                                        lids1101.setSegNo(segNo);
                                                        lids1101.setUnitCode(segNo);
                                                        //清单来源 30生产上料
                                                        lids1101.setListSource("30");
                                                        //依据凭单号（生产工单号）
                                                        lids1101.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101.setSerialNumber(SerialNumber.toString());
                                                        //机组代码
                                                        lids1101.setMachineCode((String) machineCodeCrossArea.get(0).get("machineCode"));
                                                        //机组名称
                                                        lids1101.setMachineName((String) machineCodeCrossArea.get(0).get("machineName"));
                                                        //拆包区编号
                                                        lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //拆包区名称
                                                        lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //模具ID
                                                        lids1101.setMouldId("");
                                                        //模具名称
                                                        lids1101.setMouldName("");
                                                        //作业开始时间
                                                        lids1101.setStartTime("");
                                                        //作业结束时间
                                                        lids1101.setEndTime("");
                                                        //作业时间
                                                        lids1101.setJobTime("");
                                                        //根据拆包区找对应的跨区，查询当前跨区下的所有行车
                                                        String craneId = "";
                                                        String craneName = "";
                                                        queryMap.put("crossArea", unpackCrossArea);
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList)) {
                                                            craneId = craneList.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName = craneList.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101.setCraneId(craneId);
                                                        //行车名称
                                                        lids1101.setCraneName(craneName);
                                                        //起始区域类型 30拆包区
                                                        lids1101.setStartAreaType("30");
                                                        //起始区域类型代码
                                                        lids1101.setStartAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //起始区域类型名称
                                                        lids1101.setStartAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //终到区域类型 60 机组上料区
                                                        lids1101.setEndAreaType("60");
                                                        //终到区域类型代码
                                                        lids1101.setEndAreaCode((String) machineCodeCrossArea.get(0).get("materialLoadingArea"));
                                                        //终到区域类型名称
                                                        lids1101.setEndAreaName((String) machineCodeCrossArea.get(0).get("materialLoadingAreaName"));
                                                        //状态
                                                        lids1101.setStatus("20");
                                                        //记录创建人
                                                        lids1101.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101.setArchiveFlag("");
                                                        //租户
                                                        lids1101.setTenantUser("");
                                                        //删除标记
                                                        lids1101.setDelFlag(0);
                                                        //ID
                                                        lids1101.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101);
                                                        LIDS1102 lids1102 = new LIDS1102();
                                                        String[] args1 = {applyId};
                                                        String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                        lids1102.setCraneOrderId(applyId);
                                                        lids1102.setSegNo(segNo);
                                                        lids1102.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102.setCraneOrderSubId(applySubId);
                                                        //状态
                                                        lids1102.setStatus("20");
                                                        //捆包号
                                                        lids1102.setPackId(packId);
                                                        //标签号
                                                        lids1102.setLabelId(packList.get(0).getLabelId());
                                                        //净重
                                                        lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                        //数量
                                                        lids1102.setQuantity(packList.get(0).getQuantity());
                                                        //记录创建人
                                                        lids1102.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102.setArchiveFlag("");
                                                        //租户
                                                        lids1102.setTenantUser("");
                                                        //删除标记
                                                        lids1102.setDelFlag(0);
                                                        //ID
                                                        lids1102.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102);
                                                    } else {
                                                        //机组跨区与拆包区跨区不一致，生成拆包区到过跨小车的行车作业清单，再生成过跨小车到机组上料区的行车作业清单
                                                        //拆包区到过跨小车的行车作业清单
                                                        LIDS1101 lids1101 = new LIDS1101();
                                                        String[] args = {segNo.substring(0, 2)};
                                                        String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101.setCraneOrderId(applyId);
                                                        lids1101.setSegNo(segNo);
                                                        lids1101.setUnitCode(segNo);
                                                        //清单来源 30生产上料
                                                        lids1101.setListSource("30");
                                                        //依据凭单号（生产工单号）
                                                        lids1101.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101.setSerialNumber(SerialNumber.toString());
                                                        //机组代码
                                                        lids1101.setMachineCode("");
                                                        //机组名称
                                                        lids1101.setMachineName("");
                                                        //拆包区编号
                                                        lids1101.setUnpackAreaId((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //拆包区名称
                                                        lids1101.setUnpackAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //模具ID
                                                        lids1101.setMouldId("");
                                                        //模具名称
                                                        lids1101.setMouldName("");
                                                        //作业开始时间
                                                        lids1101.setStartTime("");
                                                        //作业结束时间
                                                        lids1101.setEndTime("");
                                                        //作业时间
                                                        lids1101.setJobTime("");
                                                        //根据拆包区找对应的跨区，查询当前跨区下的所有行车
                                                        String craneId = "";
                                                        String craneName = "";
                                                        queryMap.put("crossArea", unpackCrossArea);
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList)) {
                                                            craneId = craneList.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName = craneList.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101.setCraneId(craneId);
                                                        //行车名称
                                                        lids1101.setCraneName(craneName);
                                                        //起始区域类型 30拆包区
                                                        lids1101.setStartAreaType("30");
                                                        //起始区域类型代码
                                                        lids1101.setStartAreaCode((String) machineCodeCrossArea.get(0).get("unpackAreaId"));
                                                        //起始区域类型名称
                                                        lids1101.setStartAreaName((String) machineCodeCrossArea.get(0).get("unpackAreaName"));
                                                        //终到区域类型 60 机组上料区
                                                        lids1101.setEndAreaType("20");
                                                        //根据厂区、厂房、跨区编码查询过跨小车
                                                        List<LIDS0401> transferCarIdList = this.dao.query(LIDS0401.QUERY_TRANSFER_CAR_ID, queryMap);
                                                        if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                            //终到区域类型代码
                                                            lids1101.setEndAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                            //终到区域类型名称
                                                            lids1101.setEndAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                        }
                                                        //状态
                                                        lids1101.setStatus("20");
                                                        //记录创建人
                                                        lids1101.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101.setArchiveFlag("");
                                                        //租户
                                                        lids1101.setTenantUser("");
                                                        //删除标记
                                                        lids1101.setDelFlag(0);
                                                        //ID
                                                        lids1101.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101);
                                                        LIDS1102 lids1102 = new LIDS1102();
                                                        String[] args1 = {applyId};
                                                        String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                                        lids1102.setCraneOrderId(applyId);
                                                        lids1102.setSegNo(segNo);
                                                        lids1102.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102.setCraneOrderSubId(applySubId);
                                                        //状态
                                                        lids1102.setStatus("20");
                                                        //捆包号
                                                        lids1102.setPackId(packId);
                                                        //标签号
                                                        lids1102.setLabelId(packList.get(0).getLabelId());
                                                        //净重
                                                        lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                                        //数量
                                                        lids1102.setQuantity(packList.get(0).getQuantity());
                                                        //记录创建人
                                                        lids1102.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102.setArchiveFlag("");
                                                        //租户
                                                        lids1102.setTenantUser("");
                                                        //删除标记
                                                        lids1102.setDelFlag(0);
                                                        //ID
                                                        lids1102.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102);
                                                        //过跨通道到机组上料区的行车作业清单
                                                        LIDS1101 lids1101a = new LIDS1101();
                                                        String applyId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                                        lids1101a.setCraneOrderId(applyId2);
                                                        lids1101a.setSegNo(segNo);
                                                        lids1101a.setUnitCode(segNo);
                                                        //清单来源 30生产上料
                                                        lids1101a.setListSource("30");
                                                        //依据凭单号（生产工单号）
                                                        lids1101a.setVoucherNum(processOrderId);
                                                        //批次号
                                                        lids1101a.setBatchNumber(batchId);
                                                        //顺序号
                                                        SerialNumber++;
                                                        lids1101a.setSerialNumber(SerialNumber.toString());
                                                        lids1101a.setMachineCode((String) machineCodeCrossArea.get(0).get("machineCode"));
                                                        //机组名称
                                                        lids1101a.setMachineName((String) machineCodeCrossArea.get(0).get("machineName"));
                                                        //拆包区编号
                                                        lids1101a.setUnpackAreaId("");
                                                        //拆包区名称
                                                        lids1101a.setUnpackAreaName("");
                                                        //模具ID
                                                        lids1101a.setMouldId("");
                                                        //模具名称
                                                        lids1101a.setMouldName("");
                                                        //作业开始时间
                                                        lids1101a.setStartTime("");
                                                        //作业结束时间
                                                        lids1101a.setEndTime("");
                                                        //作业时间
                                                        lids1101a.setJobTime("");
                                                        //根据机组找对应的跨区，查询当前跨区下的所有行车
                                                        String craneId1 = "";
                                                        String craneName1 = "";
                                                        queryMap.put("crossArea", machineCodeCrossArea.get(0).get("crossArea"));
                                                        queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                                        queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                                        queryMap.put("craneDuty", "10");
                                                        List<LIDS0301> craneList1 = this.dao.query(LIDS0301.QUERY, queryMap);
                                                        if (CollectionUtils.isNotEmpty(craneList1)) {
                                                            craneId1 = craneList1.stream()
                                                                    .map(LIDS0301::getCraneId)
                                                                    .collect(Collectors.joining(","));
                                                            craneName1 = craneList1.stream()
                                                                    .map(LIDS0301::getCraneName)
                                                                    .collect(Collectors.joining(","));
                                                        }
                                                        //行车编号
                                                        lids1101a.setCraneId(craneId1);
                                                        //行车名称
                                                        lids1101a.setCraneName(craneName1);
                                                        //起始区域类型
                                                        lids1101a.setStartAreaType("20");
                                                        if (CollectionUtils.isNotEmpty(transferCarIdList)) {
                                                            //起始区域类型代码
                                                            lids1101a.setStartAreaCode(transferCarIdList.get(0).getCrossingChannels());
                                                            //起始区域类型名称
                                                            lids1101a.setStartAreaName(transferCarIdList.get(0).getCrossingChannelsName());
                                                        }
                                                        //终到区域类型
                                                        lids1101a.setEndAreaType("60");
                                                        //终到区域类型代码
                                                        lids1101a.setEndAreaCode((String) machineCodeCrossArea.get(0).get("materialLoadingArea"));
                                                        //终到区域类型名称
                                                        lids1101a.setEndAreaName((String) machineCodeCrossArea.get(0).get("materialLoadingAreaName"));
                                                        //状态
                                                        lids1101a.setStatus("20");
                                                        //记录创建人
                                                        lids1101a.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1101a.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1101a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1101a.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1101a.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1101a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1101a.setArchiveFlag("");
                                                        //租户
                                                        lids1101a.setTenantUser("");
                                                        //删除标记
                                                        lids1101a.setDelFlag(0);
                                                        //ID
                                                        lids1101a.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1101.INSERT, lids1101a);
                                                        LIDS1102 lids1102a = new LIDS1102();
                                                        String[] args2 = {applyId2};
                                                        String applySubId2 = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args2);
                                                        lids1102a.setCraneOrderId(applyId2);
                                                        lids1102a.setSegNo(segNo);
                                                        lids1102a.setUnitCode(segNo);
                                                        //行车作业清单子项
                                                        lids1102a.setCraneOrderSubId(applySubId2);
                                                        //状态
                                                        lids1102a.setStatus("20");
                                                        //捆包号
                                                        lids1102a.setPackId(packId);
                                                        //标签号
                                                        lids1102a.setLabelId(packList.get(0).getLabelId());
                                                        //净重
                                                        lids1102a.setNetWeight(packList.get(0).getNetWeight());
                                                        //数量
                                                        lids1102a.setQuantity(packList.get(0).getQuantity());
                                                        //记录创建人
                                                        lids1102a.setRecCreator("system");
                                                        //记录创建人姓名
                                                        lids1102a.setRecCreatorName("system");
                                                        //记录创建时间
                                                        lids1102a.setRecCreateTime(DateUtils.curDateTimeStr14());
                                                        //记录修改人
                                                        lids1102a.setRecRevisor("system");
                                                        //记录修改人姓名
                                                        lids1102a.setRecRevisorName("system");
                                                        //记录修改时间
                                                        lids1102a.setRecReviseTime(DateUtils.curDateTimeStr14());
                                                        //归档标记
                                                        lids1102a.setArchiveFlag("");
                                                        //租户
                                                        lids1102a.setTenantUser("");
                                                        //删除标记
                                                        lids1102a.setDelFlag(0);
                                                        //ID
                                                        lids1102a.setUuid(UUIDUtils.getUUID());
                                                        dao.insert(LIDS1102.INSERT, lids1102a);
                                                    }
                                                }

                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
            return outInfo;
        }
        return outInfo;
    }

    /**
     * 厂内转运生成行车作业清单
     *
     * @param inInfo
     * @return outInfo
     * @serviceId:S_LI_DS_0003
     */
    public EiInfo transferCreateCraneOrderId(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //接收转库单信息
            List<HashMap> dList = (List<HashMap>) inInfo.get("detail");
            String segNo = inInfo.getString("segNo");
            //转运单号
            String transId = MapUtils.getString(inInfo.getAttr(), "transId", "");
            String[] batch = {segNo.substring(0, 2)};
            String batchId = SequenceGenerator.getNextSequence("TLIDS_SEQ1103", batch);
            if(CollectionUtils.isNotEmpty(dList)){
                for (HashMap hashMap : dList){
                    //顺序号
                    Integer SerialNumber = 0;
                    //捆包原始库位
                    String packOldLocationId = (String) hashMap.get("packOldLocationId");
                    //捆包新库位
                    String packNewLocationId = (String) hashMap.get("packNewLocationId");
                    String packId = MapUtils.getString(hashMap, "packId", "");
                    if (StringUtils.isBlank(packId)) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("缺少捆包信息，生成失败");
                        return outInfo;
                    }
                    if (StringUtils.isBlank(transId)) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("缺少转运单号信息，生成失败");
                        return outInfo;
                    }
                    HashMap queryMap = new HashMap();
                    queryMap.put("segNo", segNo);
                    queryMap.put("locationId", packOldLocationId);
                    //判断捆包当前库位是否在一期厂房
                    int count = super.count(LIDS0601.COUNT_LOCATION_ID, queryMap);
                    if (count == 1) {
                        //当前库位在一期厂房生成当前库区到发货通道的行车作业清单，如果捆包上层有卷，则生成倒库的行车作业清单
                        //判断投料捆包的板卷类型
                        queryMap.put("packId", packId);
                        //查询实物库存表获取板卷类型
                        List<LIDS0901> packList = dao.query(LIDS0901.QUERY_PACK_MESSAGE, queryMap);
                        if (CollectionUtils.isNotEmpty(packList)) {
                            //根据捆包库位找对应的跨区，查询当前跨区下的所有行车
                            queryMap.put("accurateLocationId", packList.get(0).getAreaCode());
                            List<LIDS0601> locationList = this.dao.query(LIDS0601.QUERY, queryMap);
                            String actionFlag = packList.get(0).getActionFlag();
                            if ("0".equals(actionFlag)) {
                                //捆包为板时，根据库存的xy轴查询当前xy下有多少
                            } else if ("1".equals(actionFlag)) {
                                //捆包为卷时，判断当前捆包是否为下层捆包
                                if ("1".equals(packList.get(0).getPosDirCode())) {
                                    //捆包为下层，查询捆包上层是否有需要倒库的捆包
                                    queryMap.clear();
                                    queryMap.put("segNo", segNo);
                                    queryMap.put("packId", packId);
                                    queryMap.put("XPosition", packList.get(0).getX_position());
                                    queryMap.put("YPosition", packList.get(0).getY_position());
                                    queryMap.put("warehouseCode", packList.get(0).getWarehouseCode());
                                    queryMap.put("locationId", packList.get(0).getAreaCode());
                                    List<LIDS0901> stockList = dao.query(LIDS0901.QUERY_RELATED_PACKS_BY_ADJACENT_IDS, queryMap);
                                    if (CollectionUtils.isNotEmpty(stockList)) {
                                        for (LIDS0901 lids0901 : stockList) {
                                            LIDS1101 lids1101 = new LIDS1101();
                                            String[] args = {segNo.substring(0, 2)};
                                            String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                            lids1101.setCraneOrderId(applyId);
                                            lids1101.setSegNo(segNo);
                                            lids1101.setUnitCode(segNo);
                                            //清单来源 50倒库
                                            lids1101.setListSource("50");
                                            //依据凭单号（转运单号）
                                            lids1101.setVoucherNum(transId);
                                            //批次号
                                            lids1101.setBatchNumber(batchId);
                                            //顺序号
                                            SerialNumber++;
                                            lids1101.setSerialNumber(SerialNumber.toString());
                                            //机组代码
                                            lids1101.setMachineCode("");
                                            //机组名称
                                            lids1101.setMachineName("");
                                            //拆包区编号
                                            lids1101.setUnpackAreaId("");
                                            //拆包区名称
                                            lids1101.setUnpackAreaName("");
                                            //模具ID
                                            lids1101.setMouldId("");
                                            //模具名称
                                            lids1101.setMouldName("");
                                            //作业开始时间
                                            lids1101.setStartTime("");
                                            //作业结束时间
                                            lids1101.setEndTime("");
                                            //作业时间
                                            lids1101.setJobTime("");
                                            //根据捆包库位找对应的跨区，查询当前跨区下的所有行车
                                            String craneId = "";
                                            String craneName = "";
                                            queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                            queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                            queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                            List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                            if (CollectionUtils.isNotEmpty(craneList)) {
                                                craneId = craneList.stream()
                                                        .map(LIDS0301::getCraneId)
                                                        .collect(Collectors.joining(","));
                                                craneName = craneList.stream()
                                                        .map(LIDS0301::getCraneName)
                                                        .collect(Collectors.joining(","));
                                            }
                                            //行车编号
                                            lids1101.setCraneId(craneId);
                                            //行车名称
                                            lids1101.setCraneName(craneName);
                                            //起始区域类型
                                            lids1101.setStartAreaType("10");
                                            //起始区域类型代码
                                            lids1101.setStartAreaCode(lids0901.getAreaCode());
                                            //起始区域类型名称
                                            lids1101.setStartAreaName(lids0901.getAreaName());
                                            //终到区域类型
                                            lids1101.setEndAreaType("10");
                                            //终到区域调用库位推荐获取
                                            HashMap messageBody = new HashMap();
                                            messageBody.put("mark","1");
                                            messageBody.put("segNo",segNo);
                                            messageBody.put("packId",lids0901.getPackId());
                                            EiInfo importInfo = new EiInfo();
                                            importInfo.set("messageBody",messageBody);
                                            importInfo.set(EiConstant.serviceName, "LIDS0606");
                                            importInfo.set(EiConstant.methodName, "recommendedStorageLocation");
                                            importInfo = XLocalManager.callNoTx(importInfo);
                                            String endAreaCode = "";
                                            String endAreaName = "";
                                            if (importInfo.getStatus() == EiConstant.STATUS_DEFAULT){
                                                endAreaCode =(String) importInfo.getAttr().get("locationId");
                                                endAreaName =(String) importInfo.getAttr().get("locationName");
                                            }else {
                                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                outInfo.setMsg("调用库位推荐接口失败，" + importInfo.getMsg());
                                                return outInfo;
                                            }
                                            //终到区域类型代码
                                            lids1101.setEndAreaCode(endAreaCode);
                                            //终到区域类型名称
                                            lids1101.setEndAreaName(endAreaName);
                                            //状态
                                            lids1101.setStatus("20");
                                            //记录创建人
                                            lids1101.setRecCreator("system");
                                            //记录创建人姓名
                                            lids1101.setRecCreatorName("system");
                                            //记录创建时间
                                            lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                            //记录修改人
                                            lids1101.setRecRevisor("system");
                                            //记录修改人姓名
                                            lids1101.setRecRevisorName("system");
                                            //记录修改时间
                                            lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                            //归档标记
                                            lids1101.setArchiveFlag("");
                                            //租户
                                            lids1101.setTenantUser("");
                                            //删除标记
                                            lids1101.setDelFlag(0);
                                            //ID
                                            lids1101.setUuid(UUIDUtils.getUUID());
                                            dao.insert(LIDS1101.INSERT, lids1101);
                                            LIDS1102 lids1102 = new LIDS1102();
                                            String[] args1 = {applyId};
                                            String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                            lids1102.setCraneOrderId(applyId);
                                            lids1102.setSegNo(segNo);
                                            lids1102.setUnitCode(segNo);
                                            //行车作业清单子项
                                            lids1102.setCraneOrderSubId(applySubId);
                                            //状态
                                            lids1102.setStatus("20");
                                            //捆包号
                                            lids1102.setPackId(lids0901.getPackId());
                                            //标签号
                                            lids1102.setLabelId(lids0901.getLabelId());
                                            //净重
                                            lids1102.setNetWeight(lids0901.getCraneOperationWeight());
                                            //数量
                                            lids1102.setQuantity(lids0901.getQuantity());
                                            //记录创建人
                                            lids1102.setRecCreator("system");
                                            //记录创建人姓名
                                            lids1102.setRecCreatorName("system");
                                            //记录创建时间
                                            lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                            //记录修改人
                                            lids1102.setRecRevisor("system");
                                            //记录修改人姓名
                                            lids1102.setRecRevisorName("system");
                                            //记录修改时间
                                            lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                            //归档标记
                                            lids1102.setArchiveFlag("");
                                            //租户
                                            lids1102.setTenantUser("");
                                            //删除标记
                                            lids1102.setDelFlag(0);
                                            //ID
                                            lids1102.setUuid(UUIDUtils.getUUID());
                                            dao.insert(LIDS1102.INSERT, lids1102);
                                        }
                                    }
                                }
                                //生成从库区到通道的行车作业清单
                                LIDS1101 lids1101 = new LIDS1101();
                                String[] args = {segNo.substring(0, 2)};
                                String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                                lids1101.setCraneOrderId(applyId);
                                lids1101.setSegNo(segNo);
                                lids1101.setUnitCode(segNo);
                                //清单来源 60厂内转运
                                lids1101.setListSource("60");
                                //依据凭单号（转运单号）
                                lids1101.setVoucherNum(transId);
                                //批次号
                                lids1101.setBatchNumber(batchId);
                                //顺序号
                                SerialNumber++;
                                lids1101.setSerialNumber(SerialNumber.toString());
                                //机组代码
                                lids1101.setMachineCode("");
                                //机组名称
                                lids1101.setMachineName("");
                                //拆包区编号
                                lids1101.setUnpackAreaId("");
                                //拆包区名称
                                lids1101.setUnpackAreaName("");
                                //模具ID
                                lids1101.setMouldId("");
                                //模具名称
                                lids1101.setMouldName("");
                                //作业开始时间
                                lids1101.setStartTime("");
                                //作业结束时间
                                lids1101.setEndTime("");
                                //作业时间
                                lids1101.setJobTime("");
                                //根据捆包库位找对应的跨区，查询当前跨区下的所有行车
                                String craneId = "";
                                String craneName = "";
                                queryMap.put("crossArea", locationList.get(0).getCrossArea());
                                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                                if (CollectionUtils.isNotEmpty(craneList)) {
                                    craneId = craneList.stream()
                                            .map(LIDS0301::getCraneId)
                                            .collect(Collectors.joining(","));
                                    craneName = craneList.stream()
                                            .map(LIDS0301::getCraneName)
                                            .collect(Collectors.joining(","));
                                }
                                //行车编号
                                lids1101.setCraneId(craneId);
                                //行车名称
                                lids1101.setCraneName(craneName);
                                //起始区域类型
                                lids1101.setStartAreaType("10");
                                //起始区域类型代码
                                lids1101.setStartAreaCode(packList.get(0).getAreaCode());
                                //起始区域类型名称
                                lids1101.setStartAreaName(packList.get(0).getAreaName());
                                //终到区域类型
                                lids1101.setEndAreaType("40");
                                queryMap.clear();
                                queryMap.put("segNo",segNo);
                                //一厂的厂区代码和厂房代码为目前为写死，正式上线时需要变更
                                queryMap.put("factoryArea", "CQBG");
                                queryMap.put("factoryBuilding", "F1");
                                queryMap.put("areaType","40");
                                List<LIDS0101> aisleList = this.dao.query(LIDS0101.QUERY,queryMap);
                                if (CollectionUtils.isNotEmpty(aisleList)){
                                    //终到区域类型代码
                                    lids1101.setEndAreaCode(aisleList.get(0).getAreaCode());
                                    //终到区域类型名称
                                    lids1101.setEndAreaName(aisleList.get(0).getAreaName());
                                }
                                //状态
                                lids1101.setStatus("20");
                                //记录创建人
                                lids1101.setRecCreator("system");
                                //记录创建人姓名
                                lids1101.setRecCreatorName("system");
                                //记录创建时间
                                lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                                //记录修改人
                                lids1101.setRecRevisor("system");
                                //记录修改人姓名
                                lids1101.setRecRevisorName("system");
                                //记录修改时间
                                lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                                //归档标记
                                lids1101.setArchiveFlag("");
                                //租户
                                lids1101.setTenantUser("");
                                //删除标记
                                lids1101.setDelFlag(0);
                                //ID
                                lids1101.setUuid(UUIDUtils.getUUID());
                                //新增之前查询如果当前捆包已经生成起始和终到与该条行车作业清单一致，则不在生成
                                queryMap.clear();
                                queryMap.put("segNo",segNo);
                                queryMap.put("packId",packId);
                                queryMap.put("startAreaCode",lids1101.getStartAreaCode());
                                queryMap.put("endAreaCode",lids1101.getEndAreaCode());
                                queryMap.put("listSource",lids1101.getListSource());
                                int repetitionOrder = super.count(LIDS1101.QUERY_REPETITION_ORDER, queryMap);
                                if (repetitionOrder == 0){
                                    dao.insert(LIDS1101.INSERT, lids1101);
                                    LIDS1102 lids1102 = new LIDS1102();
                                    String[] args1 = {applyId};
                                    String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                                    lids1102.setCraneOrderId(applyId);
                                    lids1102.setSegNo(segNo);
                                    lids1102.setUnitCode(segNo);
                                    //行车作业清单子项
                                    lids1102.setCraneOrderSubId(applySubId);
                                    //状态
                                    lids1102.setStatus("20");
                                    //捆包号
                                    lids1102.setPackId(packId);
                                    //标签号
                                    lids1102.setLabelId(packList.get(0).getLabelId());
                                    //净重
                                    lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
                                    //数量
                                    lids1102.setQuantity(packList.get(0).getQuantity());
                                    //记录创建人
                                    lids1102.setRecCreator("system");
                                    //记录创建人姓名
                                    lids1102.setRecCreatorName("system");
                                    //记录创建时间
                                    lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                                    //记录修改人
                                    lids1102.setRecRevisor("system");
                                    //记录修改人姓名
                                    lids1102.setRecRevisorName("system");
                                    //记录修改时间
                                    lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                                    //归档标记
                                    lids1102.setArchiveFlag("");
                                    //租户
                                    lids1102.setTenantUser("");
                                    //删除标记
                                    lids1102.setDelFlag(0);
                                    //ID
                                    lids1102.setUuid(UUIDUtils.getUUID());
                                    dao.insert(LIDS1102.INSERT, lids1102);
                                }
                            }
                        }
                    }
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("packId", packId);
                    queryMap.put("accurateLocationId", packNewLocationId);
                    //一厂的厂区代码和厂房代码为目前为写死，正式上线时需要变更
                    queryMap.put("factoryArea", "CQBG");
                    queryMap.put("factoryBuilding", "F1");
                    //判断捆包新库位是否在一期厂房
                    List<LIDS0601> newCount = this.dao.query(LIDS0601.QUERY, queryMap);
                    if (CollectionUtils.isNotEmpty(newCount)) {
                        //生成装卸货通道到新库位的行车作业清单
                        LIDS1101 lids1101 = new LIDS1101();
                        String[] args = {segNo.substring(0, 2)};
                        String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
                        lids1101.setCraneOrderId(applyId);
                        lids1101.setSegNo(segNo);
                        lids1101.setUnitCode(segNo);
                        //清单来源 60厂内转运
                        lids1101.setListSource("60");
                        //依据凭单号（转运单号）
                        lids1101.setVoucherNum(transId);
                        //批次号
                        lids1101.setBatchNumber(batchId);
                        //顺序号
                        SerialNumber++;
                        lids1101.setSerialNumber(SerialNumber.toString());
                        //机组代码
                        lids1101.setMachineCode("");
                        //机组名称
                        lids1101.setMachineName("");
                        //拆包区编号
                        lids1101.setUnpackAreaId("");
                        //拆包区名称
                        lids1101.setUnpackAreaName("");
                        //模具ID
                        lids1101.setMouldId("");
                        //模具名称
                        lids1101.setMouldName("");
                        //作业开始时间
                        lids1101.setStartTime("");
                        //作业结束时间
                        lids1101.setEndTime("");
                        //作业时间
                        lids1101.setJobTime("");
                        //根据捆包新库位找对应的跨区，查询当前跨区下的所有行车
                        String craneId = "";
                        String craneName = "";
                        queryMap.put("crossArea", newCount.get(0).getCrossArea());
                        queryMap.put("factoryArea", newCount.get(0).getFactoryArea());
                        queryMap.put("factoryBuilding", newCount.get(0).getFactoryBuilding());
                        List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                        if (CollectionUtils.isNotEmpty(craneList)) {
                            craneId = craneList.stream()
                                    .map(LIDS0301::getCraneId)
                                    .collect(Collectors.joining(","));
                            craneName = craneList.stream()
                                    .map(LIDS0301::getCraneName)
                                    .collect(Collectors.joining(","));
                        }
                        //行车编号
                        lids1101.setCraneId(craneId);
                        //行车名称
                        lids1101.setCraneName(craneName);
                        //起始区域类型
                        lids1101.setStartAreaType("40");
                        queryMap.clear();
                        queryMap.put("segNo",segNo);
                        //一厂的厂区代码和厂房代码为目前为写死，正式上线时需要变更
                        queryMap.put("factoryArea", "CQBG");
                        queryMap.put("factoryBuilding", "F1");
                        queryMap.put("areaType","40");
                        List<LIDS0101> aisleList = this.dao.query(LIDS0101.QUERY,queryMap);
                        if (CollectionUtils.isNotEmpty(aisleList)){
                            //起始区域类型代码
                            lids1101.setStartAreaCode(aisleList.get(0).getAreaCode());
                            //起始区域类型名称
                            lids1101.setStartAreaName(aisleList.get(0).getAreaName());
                        }
                        //终到区域类型
                        lids1101.setEndAreaType("10");
                        //终到区域类型代码
                        lids1101.setEndAreaCode(newCount.get(0).getLocationId());
                        //终到区域类型名称
                        lids1101.setEndAreaName(newCount.get(0).getLocationName());
                        //状态
                        lids1101.setStatus("20");
                        //记录创建人
                        lids1101.setRecCreator("system");
                        //记录创建人姓名
                        lids1101.setRecCreatorName("system");
                        //记录创建时间
                        lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
                        //记录修改人
                        lids1101.setRecRevisor("system");
                        //记录修改人姓名
                        lids1101.setRecRevisorName("system");
                        //记录修改时间
                        lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
                        //归档标记
                        lids1101.setArchiveFlag("");
                        //租户
                        lids1101.setTenantUser("");
                        //删除标记
                        lids1101.setDelFlag(0);
                        //ID
                        lids1101.setUuid(UUIDUtils.getUUID());
                        //新增之前查询如果当前捆包已经生成起始和终到与该条行车作业清单一致，则不在生成
                        queryMap.clear();
                        queryMap.put("segNo",segNo);
                        queryMap.put("packId",packId);
                        queryMap.put("startAreaCode",lids1101.getStartAreaCode());
                        queryMap.put("endAreaCode",lids1101.getEndAreaCode());
                        queryMap.put("listSource",lids1101.getListSource());
                        int repetitionOrder = super.count(LIDS1101.QUERY_REPETITION_ORDER, queryMap);
                        if (repetitionOrder == 0){
                            dao.insert(LIDS1101.INSERT, lids1101);
                            LIDS1102 lids1102 = new LIDS1102();
                            String[] args1 = {applyId};
                            String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
                            lids1102.setCraneOrderId(applyId);
                            lids1102.setSegNo(segNo);
                            lids1102.setUnitCode(segNo);
                            //行车作业清单子项
                            lids1102.setCraneOrderSubId(applySubId);
                            //状态
                            lids1102.setStatus("20");
                            //捆包号
                            lids1102.setPackId(packId);
                            queryMap.clear();
                            queryMap.put("segNo", segNo);
                            queryMap.put("packId", packId);
                            List<VIPM0008> packList = this.dao.query(VIPM0008.QUERY, queryMap);
                            if (CollectionUtils.isEmpty(packList)) {
                                //标签号
                                lids1102.setLabelId("");
                                //净重
                                lids1102.setNetWeight(new BigDecimal(0));
                                //数量
                                lids1102.setQuantity(1);
                            } else {
                                //标签号
                                lids1102.setLabelId(packList.get(0).getLabelId());
                                //净重
                                lids1102.setNetWeight(packList.get(0).getNetWeight());
                                //数量
                                lids1102.setQuantity(1);
                            }
                            //记录创建人
                            lids1102.setRecCreator("system");
                            //记录创建人姓名
                            lids1102.setRecCreatorName("system");
                            //记录创建时间
                            lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
                            //记录修改人
                            lids1102.setRecRevisor("system");
                            //记录修改人姓名
                            lids1102.setRecRevisorName("system");
                            //记录修改时间
                            lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
                            //归档标记
                            lids1102.setArchiveFlag("");
                            //租户
                            lids1102.setTenantUser("");
                            //删除标记
                            lids1102.setDelFlag(0);
                            //ID
                            lids1102.setUuid(UUIDUtils.getUUID());
                            dao.insert(LIDS1102.INSERT, lids1102);
                        }
                    }
                }
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("生成成功");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("生成失败" + ex.getMessage());
            return outInfo;
        }
        return outInfo;
    }
    /**
     * 成品入库生成行车作业清单
     *机组余料到鞍座时，或者退料到鞍座时，触发生成从鞍座位置到退卷区的行车作业清单
     * @param inInfo
     * @return outInfo
     */
   public EiInfo  finishedProductStorage(EiInfo inInfo){
       EiInfo outInfo = new EiInfo();
       try {
           String segNo = inInfo.getString("segNo");
           String processOrderId = inInfo.getString("processOrderId");
           String packId = inInfo.getString("packId");
           String machineCode = inInfo.getString("machineCode");
           LIDS1101 lids1101 = new LIDS1101();
           String[] args = {segNo.substring(0, 2)};
           String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
           lids1101.setCraneOrderId(applyId);
           lids1101.setSegNo(segNo);
           lids1101.setUnitCode(segNo);
           //清单来源 40加工退卷
           lids1101.setListSource("40");
           //依据凭单号（生产工单号）
           lids1101.setVoucherNum(processOrderId);
           HashMap queryMap = new HashMap();
           queryMap.put("segNo",segNo);
           queryMap.put("voucherNum",processOrderId);
           //查询当前工单该捆包的批次号
           List<LIDS1101> craneOrderIdList = this.dao.query(LIDS1101.QUERY_CRANE_ORDER_ID,queryMap);
           if (CollectionUtils.isNotEmpty(craneOrderIdList)){
               //批次号
               lids1101.setBatchNumber(craneOrderIdList.get(0).getBatchNumber());
           }else {
               //批次号
               lids1101.setBatchNumber("");
           }
           //顺序号
           lids1101.setSerialNumber("");
           //机组代码
           lids1101.setMachineCode(machineCode);
           queryMap.put("machineCode",machineCode);
           List<LIDS0701> machineCodeList = this.dao.query(LIDS0701.QUERY,queryMap);
           if (CollectionUtils.isNotEmpty(machineCodeList)){
               //机组名称
               lids1101.setMachineName(machineCodeList.get(0).getMachineName());
               queryMap.put("crossArea", machineCodeList.get(0).getCrossArea());
           }
           //机组名称
           lids1101.setMachineName("");
           //拆包区编号
           lids1101.setUnpackAreaId("");
           //拆包区名称
           lids1101.setUnpackAreaName("");
           //模具ID
           lids1101.setMouldId("");
           //模具名称
           lids1101.setMouldName("");
           //作业开始时间
           lids1101.setStartTime("");
           //作业结束时间
           lids1101.setEndTime("");
           //作业时间
           lids1101.setJobTime("");
           //根据拆包区找对应的跨区，查询当前跨区下的所有行车
           String craneId = "";
           String craneName = "";
           queryMap.put("factoryArea", inInfo.getString("factoryArea"));
           queryMap.put("factoryBuilding", inInfo.getString("factoryBuilding"));
           List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
           if (CollectionUtils.isNotEmpty(craneList)) {
               craneId = craneList.stream()
                       .map(LIDS0301::getCraneId)
                       .collect(Collectors.joining(","));
               craneName = craneList.stream()
                       .map(LIDS0301::getCraneName)
                       .collect(Collectors.joining(","));
           }
           //行车编号
           lids1101.setCraneId(craneId);
           //行车名称
           lids1101.setCraneName(craneName);
           //起始区域类型 60 机组上料区
           lids1101.setStartAreaType("60");
           //起始区域类型代码
           lids1101.setStartAreaCode(machineCodeList.get(0).getMaterialLoadingArea());
           //起始区域类型名称
           lids1101.setStartAreaName(machineCodeList.get(0).getMaterialLoadingAreaName());
           //终到区域类型 63 机组退卷区
           lids1101.setEndAreaType("63");
           //终到区域类型代码
           lids1101.setEndAreaCode(machineCodeList.get(0).getRejectionArea());
           //终到区域类型名称
           lids1101.setEndAreaName(machineCodeList.get(0).getRejectionAreaName());
           //状态
           lids1101.setStatus("20");
           //记录创建人
           lids1101.setRecCreator("system");
           //记录创建人姓名
           lids1101.setRecCreatorName("system");
           //记录创建时间
           lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
           //记录修改人
           lids1101.setRecRevisor("system");
           //记录修改人姓名
           lids1101.setRecRevisorName("system");
           //记录修改时间
           lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
           //归档标记
           lids1101.setArchiveFlag("");
           //租户
           lids1101.setTenantUser("");
           //删除标记
           lids1101.setDelFlag(0);
           //ID
           lids1101.setUuid(UUIDUtils.getUUID());
           dao.insert(LIDS1101.INSERT, lids1101);
           LIDS1102 lids1102 = new LIDS1102();
           String[] args1 = {applyId};
           String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
           lids1102.setCraneOrderId(applyId);
           lids1102.setSegNo(segNo);
           lids1102.setUnitCode(segNo);
           //行车作业清单子项
           lids1102.setCraneOrderSubId(applySubId);
           //状态
           lids1102.setStatus("20");
           //捆包号
           lids1102.setPackId(packId);
           queryMap.clear();
           queryMap.put("segNo",segNo);
           queryMap.put("packId",packId);
           List<LIDS0901> packList = this.dao.query(LIDS0901.QUERY_PACK_MESSAGE, queryMap);
           //标签号
           lids1102.setLabelId(packList.get(0).getLabelId());
           //净重
           lids1102.setNetWeight(packList.get(0).getCraneOperationWeight());
           //数量
           lids1102.setQuantity(packList.get(0).getQuantity());
           //记录创建人
           lids1102.setRecCreator("system");
           //记录创建人姓名
           lids1102.setRecCreatorName("system");
           //记录创建时间
           lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
           //记录修改人
           lids1102.setRecRevisor("system");
           //记录修改人姓名
           lids1102.setRecRevisorName("system");
           //记录修改时间
           lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
           //归档标记
           lids1102.setArchiveFlag("");
           //租户
           lids1102.setTenantUser("");
           //删除标记
           lids1102.setDelFlag(0);
           //ID
           lids1102.setUuid(UUIDUtils.getUUID());
           dao.insert(LIDS1102.INSERT, lids1102);
           outInfo.setStatus(EiConstant.STATUS_SUCCESS);
           outInfo.setMsg("生成成功");
       }catch (Exception ex){
           outInfo.setStatus(EiConstant.STATUS_FAILURE);
           outInfo.setMsg("生成失败" + ex.getMessage());
       }
       return outInfo;
   }
    /**
     * 成品入库生成行车作业清单
     *从退卷区抓取时，生成一个倒库的作业清单，目的区域根据库位推荐指定
     * 根据传入xyz判断是机组退卷区，根据机组退卷区查询出机组信息，根据捆包号查询出工单信息和实物库存信息
     * @param inInfo
     * @return outInfo
     */
    public EiInfo  finishedProductStorage1(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try{
            //传入参数有行车编号、行车名称、系统账套、捆包号
            Map attr = inInfo.getAttr();
            String segNo = MapUtils.getString(attr,"segNo","");
            String packId = MapUtils.getString(attr,"packId","");
            String craneId = MapUtils.getString(attr,"craneId","");
            String craneName = MapUtils.getString(attr,"craneName","");
            String processOrderId = "";
            if (StringUtils.isBlank(packId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少捆包信息,调用失败");
                return outInfo;
            }
            HashMap queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            queryMap.put("packId",packId);
            List<VIPM0008> vipm0008 = this.dao.query(VIPM0008.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(vipm0008)){
                processOrderId =vipm0008.get(0).getProcessOrderId();
            }
            LIDS1101 lids1101 = new LIDS1101();
            String[] args = {segNo.substring(0, 2)};
            String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
            lids1101.setCraneOrderId(applyId);
            lids1101.setSegNo(segNo);
            lids1101.setUnitCode(segNo);
            //清单来源 50倒库
            lids1101.setListSource("50");
            //依据凭单号（生产工单号）
            lids1101.setVoucherNum(processOrderId);
            queryMap.clear();
            queryMap.put("segNo",segNo);
            queryMap.put("voucherNum",processOrderId);
            //查询当前工单该捆包的批次号
            List<LIDS1101> craneOrderIdList = this.dao.query(LIDS1101.QUERY_CRANE_ORDER_ID,queryMap);
            if (CollectionUtils.isNotEmpty(craneOrderIdList)){
                //批次号
                lids1101.setBatchNumber(craneOrderIdList.get(0).getBatchNumber());
            }else {
                lids1101.setBatchNumber("");
            }
            //顺序号
            lids1101.setSerialNumber("");
            //机组代码
            lids1101.setMachineCode("");
            //机组名称
            lids1101.setMachineName("");
            //拆包区编号
            lids1101.setUnpackAreaId("");
            //拆包区名称
            lids1101.setUnpackAreaName("");
            //模具ID
            lids1101.setMouldId("");
            //模具名称
            lids1101.setMouldName("");
            //作业开始时间
            lids1101.setStartTime("");
            //作业结束时间
            lids1101.setEndTime("");
            //作业时间
            lids1101.setJobTime("");
            //行车编号
            lids1101.setCraneId(craneId);
            //行车名称
            lids1101.setCraneName(craneName);
            //起始区域类型
            lids1101.setStartAreaType("10");
            //查询库存获取当前实物库存位置
            queryMap.clear();
            queryMap.put("segNo",segNo);
            queryMap.put("packId",packId);
            String labelId = "";
            String startAreaCode = "";
            String startAreaName = "";
            String startAreaType = "";
            BigDecimal netWeight = new BigDecimal(0);
            Integer quantity = 0;
            String originalPackId = "";
            List<LIDS0901> packList = this.dao.query(LIDS0901.QUERY_PACK_MESSAGE,queryMap);
            if (CollectionUtils.isNotEmpty(packList)){
                labelId = packList.get(0).getLabelId();
                startAreaCode =packList.get(0).getAreaCode();
                startAreaName =packList.get(0).getAreaName();
                netWeight =packList.get(0).getCraneOperationWeight();
                quantity=packList.get(0).getQuantity();
                originalPackId = packList.get(0).getOriginalPackId();
                startAreaType = packList.get(0).getAreaType();
            }
            //起始区域类型代码
            lids1101.setStartAreaCode(startAreaCode);
            //起始区域类型名称
            lids1101.setStartAreaName(startAreaName);
            //终到区域类型
            lids1101.setEndAreaType(startAreaType);
            //终到区域调用库位推荐获取
            HashMap messageBody = new HashMap();
            messageBody.put("mark","1");
            messageBody.put("segNo",segNo);
            messageBody.put("packId",packId);
            //判断抓取捆包是余料捆包时，传入标记和捆包重量。判断条件为库存的原料捆包号不为空
            if (StringUtils.isNotBlank(originalPackId)) {
                messageBody.put("originalPackId", originalPackId);
                messageBody.put("netWeight", netWeight);
            }
            EiInfo importInfo = new EiInfo();
            importInfo.set("messageBody",messageBody);
            importInfo.set(EiConstant.serviceName, "LIDS0606");
            importInfo.set(EiConstant.methodName, "recommendedStorageLocation");
            importInfo = XLocalManager.callNoTx(importInfo);
            String endAreaCode = "";
            String endAreaName = "";
            if (importInfo.getStatus() == EiConstant.STATUS_DEFAULT){
                    endAreaCode =(String) importInfo.getAttr().get("locationId");
                    endAreaName =(String) importInfo.getAttr().get("locationName");
            }else {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("调用库位推荐接口失败，" + importInfo.getMsg());
                return outInfo;
            }
            //终到区域类型代码
            lids1101.setEndAreaCode(endAreaCode);
            //终到区域类型名称
            lids1101.setEndAreaName(endAreaName);
            //状态
            lids1101.setStatus("20");
            //记录创建人
            lids1101.setRecCreator("system");
            //记录创建人姓名
            lids1101.setRecCreatorName("system");
            //记录创建时间
            lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
            //记录修改人
            lids1101.setRecRevisor("system");
            //记录修改人姓名
            lids1101.setRecRevisorName("system");
            //记录修改时间
            lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
            //归档标记
            lids1101.setArchiveFlag("");
            //租户
            lids1101.setTenantUser("");
            //删除标记
            lids1101.setDelFlag(0);
            //ID
            lids1101.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1101.INSERT, lids1101);
            LIDS1102 lids1102 = new LIDS1102();
            String[] args1 = {applyId};
            String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
            lids1102.setCraneOrderId(applyId);
            lids1102.setSegNo(segNo);
            lids1102.setUnitCode(segNo);
            //行车作业清单子项
            lids1102.setCraneOrderSubId(applySubId);
            //状态
            lids1102.setStatus("20");
            //捆包号
            lids1102.setPackId(packId);
            //标签号
            lids1102.setLabelId(labelId);
            //净重
            lids1102.setNetWeight(netWeight);
            //数量
            lids1102.setQuantity(quantity);
            //记录创建人
            lids1102.setRecCreator("system");
            //记录创建人姓名
            lids1102.setRecCreatorName("system");
            //记录创建时间
            lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
            //记录修改人
            lids1102.setRecRevisor("system");
            //记录修改人姓名
            lids1102.setRecRevisorName("system");
            //记录修改时间
            lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
            //归档标记
            lids1102.setArchiveFlag("");
            //租户
            lids1102.setTenantUser("");
            //删除标记
            lids1102.setDelFlag(0);
            //ID
            lids1102.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1102.INSERT, lids1102);
            outInfo.setMsg("生成成功");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("生成失败" + ex.getMessage());
        }
        return outInfo;
    }

    /**
     * PDA扫描生成一期厂房的行车作业清单
     * 调用库位推荐方法
     * @param inInfo
     * @return outInfo
     */
        public EiInfo PDAFinishedProduct(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            Map attr = inInfo.getAttr();
            String segNo = MapUtils.getString(attr,"segNo","");
            String packId = MapUtils.getString(attr,"packId","");//捆包号
            String[] batch = {segNo.substring(0, 2)};
            String batchId = SequenceGenerator.getNextSequence("TLIDS_SEQ1103", batch);
            if (StringUtils.isBlank(packId)){
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("缺少捆包信息,调用失败");
                return outInfo;
            }
            LIDS1101 lids1101 = new LIDS1101();
            String[] args = {segNo.substring(0, 2)};
            String applyId = SequenceGenerator.getNextSequence("TLIDS_SEQ1101", args);
            lids1101.setCraneOrderId(applyId);
            lids1101.setSegNo(segNo);
            lids1101.setUnitCode(segNo);
            //清单来源 60 厂内转运
            lids1101.setListSource("60");
            //依据凭单号
            lids1101.setVoucherNum("");
            //批次号
            lids1101.setBatchNumber(batchId);
            //顺序号
            lids1101.setSerialNumber("1");
            //机组代码
            lids1101.setMachineCode("");
            //机组名称
            lids1101.setMachineName("");
            //拆包区编号
            lids1101.setUnpackAreaId("");
            //拆包区名称
            lids1101.setUnpackAreaName("");
            //模具ID
            lids1101.setMouldId("");
            //模具名称
            lids1101.setMouldName("");
            //作业开始时间
            lids1101.setStartTime("");
            //作业结束时间
            lids1101.setEndTime("");
            //作业时间
            lids1101.setJobTime("");
            //起始区域类型
            lids1101.setStartAreaType("40");
            HashMap queryMap = new HashMap();
            queryMap.put("segNo",segNo);
            //一厂的厂区代码和厂房代码为目前为写死，正式上线时需要变更
            queryMap.put("factoryArea", "CQBG");
            queryMap.put("factoryBuilding", "F1");
            queryMap.put("areaType","40");
            List<LIDS0101> aisleList = this.dao.query(LIDS0101.QUERY,queryMap);
            if (CollectionUtils.isNotEmpty(aisleList)){
                //起始区域类型代码
                lids1101.setStartAreaCode(aisleList.get(0).getAreaCode());
                //起始区域类型名称
                lids1101.setStartAreaName(aisleList.get(0).getAreaName());
            }
            //终到区域类型
            lids1101.setEndAreaType("10");
            //终到区域调用库位推荐获取
            HashMap messageBody = new HashMap();
            messageBody.put("mark","1");
            messageBody.put("segNo",segNo);
            messageBody.put("packId",packId);
            EiInfo importInfo = new EiInfo();
            importInfo.set("messageBody",messageBody);
            importInfo.set(EiConstant.serviceName, "LIDS0606");
            importInfo.set(EiConstant.methodName, "recommendedStorageLocation");
            importInfo = XLocalManager.callNoTx(importInfo);
            String endAreaCode = "";
            String endAreaName = "";
            if (importInfo.getStatus() == EiConstant.STATUS_DEFAULT){
                endAreaCode =(String) importInfo.getAttr().get("locationId");
                endAreaName =(String) importInfo.getAttr().get("locationName");
            }else {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("调用库位推荐接口失败，" + importInfo.getMsg());
                return outInfo;
            }
            //终到区域类型代码
            lids1101.setEndAreaCode(endAreaCode);
            //终到区域类型名称
            lids1101.setEndAreaName(endAreaName);
            //行车根据库位推荐的库位查找行车
            queryMap.clear();
            queryMap.put("segNo",segNo);
            queryMap.put("accurateLocationId",endAreaCode);
            String craneId = "";
            String craneName = "";
            List<LIDS0601> locationList = this.dao.query(LIDS0601.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(locationList)){
                queryMap.put("crossArea", locationList.get(0).getCrossArea());
                queryMap.put("factoryArea", locationList.get(0).getFactoryArea());
                queryMap.put("factoryBuilding", locationList.get(0).getFactoryBuilding());
                List<LIDS0301> craneList = this.dao.query(LIDS0301.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(craneList)) {
                    craneId = craneList.stream()
                            .map(LIDS0301::getCraneId)
                            .collect(Collectors.joining(","));
                    craneName = craneList.stream()
                            .map(LIDS0301::getCraneName)
                            .collect(Collectors.joining(","));
                }
            }
            //行车编号
            lids1101.setCraneId(craneId);
            //行车名称
            lids1101.setCraneName(craneName);
            //状态
            lids1101.setStatus("20");
            //记录创建人
            lids1101.setRecCreator("system");
            //记录创建人姓名
            lids1101.setRecCreatorName("system");
            //记录创建时间
            lids1101.setRecCreateTime(DateUtils.curDateTimeStr14());
            //记录修改人
            lids1101.setRecRevisor("system");
            //记录修改人姓名
            lids1101.setRecRevisorName("system");
            //记录修改时间
            lids1101.setRecReviseTime(DateUtils.curDateTimeStr14());
            //归档标记
            lids1101.setArchiveFlag("");
            //租户
            lids1101.setTenantUser("");
            //删除标记
            lids1101.setDelFlag(0);
            //ID
            lids1101.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1101.INSERT, lids1101);
            LIDS1102 lids1102 = new LIDS1102();
            String[] args1 = {applyId};
            String applySubId = SequenceGenerator.getNextSequence("TLIDS_SEQ1102", args1);
            lids1102.setCraneOrderId(applyId);
            lids1102.setSegNo(segNo);
            lids1102.setUnitCode(segNo);
            //行车作业清单子项
            lids1102.setCraneOrderSubId(applySubId);
            //状态
            lids1102.setStatus("20");
            //捆包号
            lids1102.setPackId(packId);
            BigDecimal netWeight = new BigDecimal(0);
            Integer quantity = 0;
            queryMap.clear();
            queryMap.put("segNo",segNo);
            queryMap.put("packId",packId);
            List<LIRL0503> packList =dao.query(LIRL0503.QUERY, queryMap);
            if (CollectionUtils.isNotEmpty(packList)){
                 netWeight = packList.get(0).getNetWeight();
                 quantity = Integer.valueOf(packList.get(0).getPiceNum());
            }
            //标签号
            lids1102.setLabelId("");
            //净重
            lids1102.setNetWeight(netWeight);
            //数量
            lids1102.setQuantity(quantity);
            //记录创建人
            lids1102.setRecCreator("system");
            //记录创建人姓名
            lids1102.setRecCreatorName("system");
            //记录创建时间
            lids1102.setRecCreateTime(DateUtils.curDateTimeStr14());
            //记录修改人
            lids1102.setRecRevisor("system");
            //记录修改人姓名
            lids1102.setRecRevisorName("system");
            //记录修改时间
            lids1102.setRecReviseTime(DateUtils.curDateTimeStr14());
            //归档标记
            lids1102.setArchiveFlag("");
            //租户
            lids1102.setTenantUser("");
            //删除标记
            lids1102.setDelFlag(0);
            //ID
            lids1102.setUuid(UUIDUtils.getUUID());
            dao.insert(LIDS1102.INSERT, lids1102);
            outInfo.setMsg("生成成功");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception ex){
            outInfo.setMsg("生成失败" + ex.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return outInfo;
    }
}
