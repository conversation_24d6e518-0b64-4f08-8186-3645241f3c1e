package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.DateUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;


/**
 * <AUTHOR> yzj
 * @Description : 设备报警履历页面后台
 * @Date : 2024/8/26
 * @Version : 1.0
 */
public class ServiceVGDM0611 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0611.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 设备报警自动备份
     */
    public EiInfo autoBackAlarm(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            log("传入参数：" + segNo);
            LocalDateTime now = LocalDateTime.now();
            String archiveTime = DateUtils.FORMATTER_14.format(now);
            LocalDateTime minTime = now.minusDays(2);
            String minTimeStr = DateUtils.FORMATTER_14.format(minTime);
            log("备份时间：" + archiveTime + "查询时间：" + minTimeStr);
            // 查询scada信息
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            List<VGDM0302> list = dao.query(VGDM0302.QUERY, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException(segNo + "无scada配置信息");
            }
            VGDM0302 scada = list.get(0);
            log("scada名称：" + scada.getScadaName());
            Map<String, Object> map = new HashMap<>();
            map.put("segNo", segNo);
            map.put("scadaName", scada.getScadaName());
            map.put("archiveTime", archiveTime);
            map.put("minTime", minTimeStr);
            dao.insert(VGDM0611.INSERT_FROM_ALARM, map);
            log("备份成功，删除原始数据");
            int a = dao.delete(VGDM0601.DELETE_FOR_BACK, map);
            log("删除成功" + a);
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
