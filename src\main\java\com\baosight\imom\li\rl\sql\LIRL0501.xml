<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-27 10:36:23
       Version :  1.0
    # 车辆类型管理表
create table meli.tlirl0501
(
    SEG_NO              VARCHAR(12)  DEFAULT ' ' not null comment '业务单元代码',
    UNIT_CODE           VARCHAR(12)  DEFAULT ' ' not null comment '业务单元代码',
    STATUS              varchar(2)   DEFAULT ' ' not null comment '启用禁用标记(禁用：00、启用：10)',
    REC_CREATOR         varchar(255) DEFAULT ' ' not null comment '记录创建人',
    REC_CREATOR_NAME    varchar(255) DEFAULT ' ' not null comment '记录创建人姓名',
    REC_CREATE_TIME     varchar(14)  DEFAULT ' ' not null comment '记录创建时间',
    REC_REVISOR         varchar(255) DEFAULT ' ' not null comment '记录修改人',
    REC_REVISOR_NAME    varchar(255) DEFAULT ' ' not null comment '记录创建人姓名',
    REC_REVISE_TIME     varchar(14)  DEFAULT ' ' not null comment '记录修改时间',
    ARCHIVE_FLAG        smallint     DEFAULT 0   not null comment '归档标记',
    DEL_FLAG            smallint     DEFAULT 0   not null comment '记录删除标记',
    REMARK              varchar(255) DEFAULT ' ' not null comment '备注',
    VEHICLE_NO          varchar(20)  default ' ' not null comment '车牌号',
    VEHICLE_TYPE        varchar(20)  default '0' not null comment '车辆类型(常驻车：1、临时车：0)',
    UUID                varchar(32)  DEFAULT ' ' not null comment 'uuid',
    TENANT_ID           varchar(255) DEFAULT ' ' not null comment '租户ID',
    primary key (UUID)
)
    comment '车辆类型管理表' ENGINE = INNODB
                               DEFAULT CHARSET = UTF8
                               collate = utf8_bin;
-->
<sqlMap namespace="LIRL0501">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            a.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID like concat('%',#uuid#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO LIKE concat ('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            a.TENANT_ID = #tenantId#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(a.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(a.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleType">
            a.VEHICLE_TYPE  = #vehicleType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNoCp">
            a.VEHICLE_NO  =#vehicleNoCp#
        </isNotEmpty>
    </sql>

    <sql id="conditionPage">
        <isNotEmpty prepend=" AND " property="segNo">
            b.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            b.UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            a.UUID like concat('%',#uuid#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS = #status#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
           ( a.VEHICLE_NO LIKE concat ('%',#vehicleNo#,'%') or  b.VEHICLE_NO LIKE concat ('%',#vehicleNo#,'%') )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            a.REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            a.REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            a.REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            a.REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            a.REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            a.REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="affiliatedUnit">
            a.AFFILIATED_UNIT = #affiliatedUnit#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="remark">
            a.REMARK = #remark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            b.TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleType">
            IFNULL(a.VEHICLE_TYPE , '0') = #vehicleType#
        </isNotEmpty>
        <!--创建时间起-->
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(a.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <!--创建时间止-->
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            substr(a.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')
        </isNotEmpty>
    </sql>

    <select id="queryPage" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0501">
        SELECT b.SEG_NO                                                                        as "segNo",           <!-- 业务单元代代码 -->
        b.UNIT_CODE                                                                     as "unitCode",        <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 t where t.SEG_NO = b.SEG_NO and t.DEL_FLAG = 0) as
        "segName",         <!-- 业务单元简称 -->
        a.UUID                                                                          as "uuid",            <!-- uuid -->

        case when a.AFFILIATED_UNIT ='20' then "20"
        else "10" end as "affiliatedUnit",

        IFNULL(a.STATUS, '20')                                                          as "status",          <!-- 状态 -->
        b.VEHICLE_NO                                                                    as "vehicleNo",       <!-- 车牌号 -->

        IFNULL(a.VEHICLE_TYPE ,'0')                                                                as "vehicleType",     <!-- 车辆类型 -->
        IFNULL(a.REC_CREATOR, max(b.REC_CREATOR))                                       as "recCreator",      <!-- 记录创建人 -->
        IFNULL(a.REC_CREATOR_NAME, max(b.REC_CREATOR_NAME))                             as "recCreatorName",  <!-- 记录创建人姓名 -->
        IFNULL(a.REC_CREATE_TIME, max(b.REC_CREATE_TIME))                               as "recCreateTime",   <!-- 记录创建时间 -->
        a.REC_REVISOR                                                                   as "recRevisor",      <!-- 记录修改人 -->
        a.REC_REVISOR_NAME                                                              as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME                                                               as "recReviseTime",   <!-- 记录修改时间 -->
        IFNULL(a.ARCHIVE_FLAG, max(b.ARCHIVE_FLAG))                                     as "archiveFlag",     <!-- 归档标记 -->
        IFNULL(a.DEL_FLAG, 0)                                                           as "delFlag",         <!-- 记录删除标记 -->
        a.REMARK                                                                        as "remark",          <!-- 备注 -->
        IFNULL(a.TENANT_ID, max(b.TENANT_ID))                                           as "tenantId",         <!-- 租户ID -->
        IFNULL(a.DR_UUID,max(b.M_UUID)) as "drUuid"
        FROM ${meliSchema}.tlirl0103 b
        left join ${meliSchema}.tlirl0501 a on a.SEG_NO = b.seg_no and a.VEHICLE_NO = b.VEHICLE_NO
        WHERE 1=1
        <include refid="conditionPage"/>
        group by b.SEG_NO, b.UNIT_CODE,a.UUID, b.UNIT_CODE, b.VEHICLE_NO
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>
<!--    <select id="queryPage" parameterClass="java.util.HashMap"-->
<!--            resultClass="com.baosight.imom.li.rl.dao.LIRL0501">-->
<!--        SELECT distinct tlirl0102.SEG_NO as "segNo",-->
<!--        tlirl0102.UNIT_CODE as "unitCode",-->
<!--        (SELECT SEG_NAME-->
<!--        FROM iplat4j.TVZBM81 t-->
<!--        WHERE t.SEG_NO = tlirl0102.SEG_NO-->
<!--        AND t.DEL_FLAG = 0)                                              AS "segName",-->
<!--        tlirl0501.UUID                                                      AS "uuid",-->
<!--        IFNULL(tlirl0501.STATUS, '20')                                      AS "status",-->
<!--        tlirl0103.VEHICLE_NO                                                AS "vehicleNo",-->
<!--        IFNULL(tlirl0501.VEHICLE_TYPE, '0')                                 AS "vehicleType",-->
<!--        IFNULL(tlirl0501.REC_CREATOR, MAX(tlirl0102.REC_CREATOR))           AS "recCreator",-->
<!--        IFNULL(tlirl0501.REC_CREATOR_NAME, MAX(tlirl0102.REC_CREATOR_NAME)) AS "recCreatorName",-->
<!--        IFNULL(tlirl0501.REC_CREATE_TIME, MAX(tlirl0102.REC_CREATE_TIME))   AS "recCreateTime",-->
<!--        tlirl0501.REC_REVISOR                                               AS "recRevisor",-->
<!--        tlirl0501.REC_REVISOR_NAME                                          AS "recRevisorName",-->
<!--        tlirl0501.REC_REVISE_TIME                                           AS "recReviseTime",-->
<!--        IFNULL(tlirl0501.ARCHIVE_FLAG, MAX(tlirl0102.ARCHIVE_FLAG))         AS "archiveFlag",-->
<!--        IFNULL(tlirl0501.DEL_FLAG, 0)                                       AS "delFlag",-->
<!--        tlirl0501.REMARK                                                    AS "remark",-->
<!--        IFNULL(tlirl0501.TENANT_ID, MAX(tlirl0102.TENANT_ID))               AS "tenantId",-->
<!--        IFNULL(tlirl0501.DR_UUID, tlirl0102.UUID)                           AS "drUuid",-->
<!--        tlirl0102.DRIVER_NAME as "drvierName"-->
<!--        FROM meli.tlirl0102 tlirl0102-->
<!--        LEFT JOIN meli.tlirl0103-->
<!--        ON tlirl0103.SEG_NO = tlirl0102.SEG_NO-->
<!--        AND tlirl0103.M_UUID = tlirl0102.UUID-->
<!--        AND tlirl0102.STATUS = '20'-->
<!--        LEFT JOIN meli.tlirl0501 tlirl0501-->
<!--        ON tlirl0501.SEG_NO = tlirl0102.SEG_NO-->
<!--        AND tlirl0501.VEHICLE_NO = tlirl0103.VEHICLE_NO-->
<!--        WHERE tlirl0102.SEG_NO = #segNo#-->
<!--        and tlirl0102.status = '20'-->
<!--        <isNotEmpty prepend=" AND " property="vehicleNo">-->
<!--            tlirl0103.VEHICLE_NO LIKE concat ('%',#vehicleNo#,'%')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="vehicleType">-->
<!--            tlirl0501.VEHICLE_TYPE LIKE concat ('%',#vehicleNo#,'%')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="status">-->
<!--            tlirl0501.STATUS = #status#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="recCreateTimeStart">-->
<!--            substr(tlirl0501.REC_CREATE_TIME,1,8) >= replace(#recCreateTimeStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;创建时间止&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="recCreateTimeEnd">-->
<!--            substr(tlirl0501.REC_CREATE_TIME,1,8) <![CDATA[<=]]> replace(#recCreateTimeEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        GROUP BY tlirl0102.SEG_NO,-->
<!--        tlirl0102.UNIT_CODE,-->
<!--        tlirl0501.UUID,-->
<!--        tlirl0501.STATUS,-->
<!--        tlirl0103.VEHICLE_NO,-->
<!--        tlirl0501.VEHICLE_TYPE,-->
<!--        tlirl0501.REC_CREATOR,-->
<!--        tlirl0501.REC_CREATOR_NAME,-->
<!--        tlirl0501.REC_CREATE_TIME,-->
<!--        tlirl0501.REC_REVISOR,-->
<!--        tlirl0501.REC_REVISOR_NAME,-->
<!--        tlirl0501.REC_REVISE_TIME,-->
<!--        tlirl0501.ARCHIVE_FLAG,-->
<!--        tlirl0501.DEL_FLAG,-->
<!--        tlirl0501.REMARK,-->
<!--        tlirl0501.TENANT_ID,-->
<!--        tlirl0501.DR_UUID,-->
<!--        tlirl0102.UUID-->
<!--    </select>-->


    <select id="queryAllLoading" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT SEG_NO                         as "segNo",
        REC_CREATE_TIME                as "recCreateTime",
        ALLOCATE_VEHICLE_NO            as "allocateVehicleNo",
        ALLOC_TYPE                     as "allocType",
        CAR_TRACE_NO                   as "carTraceNo",
        CUSTOMER_NAME                  AS "customerName",
        (SELECT SPECS_DESC
        FROM MELI.TLIRL0503 LIRL0503
        WHERE 1 = 1
        AND LIRL0503.SEG_NO = tlirl0502.SEG_NO
        AND LIRL0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO
        and LIRL0503.STATUS = '20'
        LIMIT 1)                      AS "specsDesc",
        (SELECT SUM(NET_WEIGHT)
        FROM MELI.TLIRL0503 LIRL0503
        WHERE 1 = 1
        AND LIRL0503.SEG_NO = tlirl0502.SEG_NO
        AND LIRL0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO
        and LIRL0503.STATUS = '20'
        LIMIT 1)                      AS "sumWeight",
        (SELECT count(1)
        FROM MELI.TLIRL0503 LIRL0503
        WHERE 1 = 1
        AND LIRL0503.SEG_NO = tlirl0502.SEG_NO
        AND LIRL0503.ALLOCATE_VEHICLE_NO = tlirl0502.ALLOCATE_VEHICLE_NO
        and LIRL0503.STATUS = '20') AS "sumPiceNum"
        FROM MELI.tlirl0502 tlirl0502
        WHERE 1 = 1
        AND tlirl0502.VEHICLE_NO = #vehicleNo#
        AND tlirl0502.ALLOC_TYPE = '10'
        and tlirl0502.SEG_NO = 'JC000000'
        and tlirl0502.STATUS = '20'
        ORDER BY REC_CREATE_TIME asc

    </select>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0501">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.STATUS as "status",  <!-- 状态 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.VEHICLE_TYPE as "vehicleType",  <!-- 车辆类型 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.DR_UUID as "drUuid"
        FROM ${meliSchema}.tlirl0501 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>


    <select id="querypostExport" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0501">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = a.SEG_NO and DEL_FLAG = 0) as
        "segName", <!-- 业务单元简称 -->
        a.UUID as "uuid",  <!-- uuid -->
        <!--a.STATUS as "status", --> <!-- 状态 -->

        case when a.STATUS ='10' then "禁用"
        when a.STATUS ='20' then "启用"
        else "无" end as "status",

        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
       <!-- a.VEHICLE_TYPE as "vehicleType",-->  <!-- 车辆类型 -->

        case when a.VEHICLE_TYPE ='1' then "常驻车"
        when a.VEHICLE_TYPE ='0' then "临时车"
        else "无" end as "vehicleType",

        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.DR_UUID as "drUuid"
        FROM ${meliSchema}.tlirl0501 a WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meliSchema}.tlirl0501 a WHERE 1=1
        <include refid="condition"/>
    </select>



    <insert id="insert">
        INSERT INTO ${meliSchema}.tlirl0501 (SEG_NO,  <!-- 业务单元代代码 -->
        UNIT_CODE,  <!-- 业务单元代代码 -->
        UUID,  <!-- uuid -->
        STATUS,  <!-- 状态 -->
        VEHICLE_NO ,   <!-- 车牌号 -->
        VEHICLE_TYPE ,   <!-- 车辆类型 -->
        REC_CREATOR,  <!-- 记录创建人 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时间 -->
        REC_REVISOR,  <!-- 记录修改人 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时间 -->
        DEL_FLAG,  <!-- 记录删除标记 -->
        REMARK,  <!-- 备注 -->
        DR_UUID,
        AFFILIATED_UNIT
        )
        VALUES (#segNo#, #unitCode#, #uuid#, #status#, #vehicleNo#,#vehicleType#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#,#delFlag#,
        #remark#,#drUuid#,#affiliatedUnit#)
    </insert>



    <update id="update">
        UPDATE ${meliSchema}.tlirl0501
        SET
        STATUS = #status#,   <!-- 状态 -->
        VEHICLE_TYPE = #vehicleType#,   <!-- 车辆类型 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建人 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时间 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#,   <!-- 记录删除标记 -->
        REMARK = #remark#   <!-- 备注 -->
        WHERE
        VEHICLE_NO = #vehicleNo#
    </update>


    <select id="querySome" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0501">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.STATUS as "status",  <!-- 状态 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.VEHICLE_TYPE as "vehicleType",  <!-- 车辆类型 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.DR_UUID as "drUuid"
        FROM ${meliSchema}.tlirl0501 a WHERE 1=1
        and a.seg_no = #segNo#
        and a.VEHICLE_NO = #vehicleNo#
        and a.status = '20'
        and a.DEL_FLAG = 0
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>


    <select id="queryAll" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.SEG_NO as "segNo",  <!-- 业务单元代代码 -->
        a.UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
        a.UUID as "uuid",  <!-- uuid -->
        a.STATUS as "status",  <!-- 状态 -->
        a.VEHICLE_NO as "vehicleNo",  <!-- 车牌号 -->
        a.VEHICLE_TYPE as "vehicleType",  <!-- 车辆类型 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
        a.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
        a.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        a.DEL_FLAG as "delFlag",  <!-- 记录删除标记 -->
        a.REMARK as "remark",  <!-- 备注 -->
        a.TENANT_ID as "tenantId", <!-- 租户ID -->
        a.DR_UUID as "drUuid"
        FROM ${meliSchema}.tlirl0501 a
        WHERE 1=1
        and a.seg_no = #segNo#
        and a.AFFILIATED_UNIT = 30
        and a.DEL_FLAG = '0'
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO LIKE concat ('%',#vehicleNo#,'%')
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                a.REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>


    <update id="updateLease">
        UPDATE ${meliSchema}.tlirl0501
        SET
        STATUS = #status#,   <!-- 状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
        DEL_FLAG = #delFlag#  <!-- 记录删除标记 -->
        WHERE
        SEG_NO = #segNo#
        AND VEHICLE_NO = #vehicleNo#
    </update>
</sqlMap>