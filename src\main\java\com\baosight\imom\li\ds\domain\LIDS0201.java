package com.baosight.imom.li.ds.domain;

import com.baosight.imom.common.li.domain.Tlids0201;

import java.util.Map;

/**
 * 跨区管理
 */
public class LIDS0201 extends Tlids0201 {
    public static final String QUERY = "LIDS0201.query";
    public static final String QUERY_TO_MAP = "LIDS0201.queryToMap";
    public static final String COUNT = "LIDS0201.count";
    public static final String COUNT_UUID = "LIDS0201.count_uuid";
    public static final String INSERT = "LIDS0201.insert";
    public static final String UPDATE = "LIDS0201.update";
    public static final String UPDATE_STATUS = "LIDS0201.updateStatus";
    public static final String DELETE = "LIDS0201.delete";
    public static final String COUNT_EQUAL = "LIDS0201.countEqual";
    public static final String COUNT_ALL_BY_CROSS_AREA = "LIDS0201.countAllByCrossArea";
    public static final String UPDATE_LOADING_POINT = "LIDS0201.updateLoadingPoint";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS0201() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }

}
