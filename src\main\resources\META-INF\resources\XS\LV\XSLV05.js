$(function () {
    var window1 = $("#insertUser");
    $(window).load(function () {
        resultGrid.setEiInfo(__eiInfo);
        setButtonDisable(true);
    });

    $("#QUERYGROUP").on("click", function (e) {
        $("#ef_grid_resultA").data("kendoGrid").dataSource.page(1);
    });
   /* $("#QUERYUSER").on("click", function (e) {
        $("#ef_grid_resultB").data("kendoGrid").dataSource.page(1);
    });*/


    var setButtonDisable = function(b){
        $(".k-grid-add").attr("disabled", b);
        $(".k-grid-delete").attr("disabled", b);
    }

    /*根据ID 若是组织机构返回false*/
    var isOrg= function(queryParentId){
        var inInfo = new EiInfo();
        inInfo.set("orgId",queryParentId);
        var result=true;
        EiCommunicator.send("XSOG01", "queryByOrgId", inInfo, {
            onSuccess: function (ei) {
                if ( ei.getStatus() < 1) {
                    result = false;
                }
            }, onFail: function (ei) {
                IPLAT.alert(ei.getMsg());
                result = false;
            }
        }, {async: false});
        return result;
    }

    $(document.body).on("click", "#QUERY", function (e) {
        var queryParentId = $("#inqu_status-0-parentId").val();
        if (queryParentId == null || queryParentId == "") {
            IPLAT.alert("请选择左侧用户组树节点后再进行查询");
            return false;
        }
        if(isOrg(queryParentId)) {
            resultGrid.dataSource.page(1);
        }else {
            IPLAT.alert("当前选择的是组织机构，请先选择用户组!");
        }
    });

    //新增用户查询增加查询条件，必须增加业务单元号
    $(document.body).on("click", "#QUERYUSER", function (e) {
        var segNo = $("#inqu_status-0-segNo").val();
        if (segNo == null || segNo == "") {
            IPLAT.alert("请选择业务单元号再进行查询");
            return false;
        }else{
            resultBGrid.dataSource.page(1);
        }

    });



    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();

    IPLATUI.EFTree = {
        "orgGroupsTree": {
            select: function (e) {
                var _data = this.dataItem(e.node);
                $("#inqu_status-0-parentId").val(_data.label);
                $("#inqu_status-0-memberType").val('USER');
                $("#parentName").val(_data.text);
                if(!(_data.leaf == 0)){
                    setButtonDisable(true);
                    return false;
                }
                if(!isOrg(_data.label)){
                    setButtonDisable(true);
                    return false;
                }
                setButtonDisable(false);
                resultGrid.dataSource.page(1);
            }
        }
    };

    IPLATUI.EFGrid = {
        "result": {
            beforeAdd: function (e) {
                e.preventDefault();
                var parentId = $("#inqu_status-0-parentId").val();
                if (parentId == "" || parentId == null) {
                    IPLAT.alert("请在左侧用户组树选择父节点!");
                } else {
                    if (parentId == 'root') {
                        IPLAT.alert("[用户组管理]下只能添加[用户组]!");
                    } else {
                        if(!isOrg(parentId)){
                            IPLAT.alert("请在左侧组织机构用户组视角树选择用户组!");
                            return false;
                        }
                        var parentName = $("#parentName").val();
                        $("#inqu_status-0-insertUserParentId").val(parentId);
                        //$("#ef_grid_resultB").data("kendoGrid").dataSource.page(1);
                        window1.data("kendoWindow").open();
                        $("#insertUser_wnd_title").css({"text-align": "justify", "font-size": "14px", "color": "#FFF"});
                        $("#insertUser_wnd_title").html("正在为: [" + parentName + "] 添加成员用户");

                    }
                }
            },
            onSave: function (e) {
                var rows = resultGrid.getCheckedRows();
                var parentId = $("#inqu_status-0-parentId").val();
                for (var j = 0; j < rows.length; j++) {
                    var rowModel = rows[j];
                    if (rowModel.isNew()) {
                        rowModel.set("parentId", parentId);
                    }
                }
            },

            dataBinding: function (e) {
                var parentName = $("#parentName").val();
                for (var i = 0, length = e.items.length; i < length; i++) {
                    var model = e.items[i];
                    if (model.isNew()) {
                        model.parentName = parentName;
                    }
                }
            },
            loadComplete: function (grid) {

            },
            onDelete: function (e) {
                if (resultGrid.getCheckedRows().length == 0) {
                    IPLAT.alert("请勾选数据!");
                    e.preventDefault();
                }
            }
        },

        "resultB": {
            "exportGrid": false,
            loadComplete: function (grid) {
                $("#ADDUSER").on("click", function (e) {
                    var checkRows = grid.getCheckedRows();
                    if (checkRows.length > 0) {
                        var eiInfo = new EiInfo();
                        var block = new EiBlock("result");
                        block.getBlockMeta().addMeta(new EiColumn("memberId"));
                        block.getBlockMeta().addMeta(new EiColumn("parentId"));
                        block.getBlockMeta().addMeta(new EiColumn("memberType"));
                        var parentId = $("#inqu_status-0-parentId").val();
                        for (var i = 0; i < checkRows.length; i++) {
                            var model = checkRows[i];
                            block.setCell(i, "memberId", model.get("userId"));
                            block.setCell(i, "parentId", parentId);
                            block.setCell(i, "memberType", "USER");
                        }
                        eiInfo.addBlock(block);
                        EiCommunicator.send("XSLV05", "insert", eiInfo, {
                            onSuccess: function (ei) {
                                if (-1 == ei.getStatus()) {
                                    IPLAT.alert(ei.getMsg());
                                } else {
                                    IPLAT.alert(ei.getMsg());
                                    $("#inqu_status-0-loginName").val("");
                                    $("#inqu_status-0-userName").val("");
                                    resultGrid.dataSource.page(1);
                                }
                            }, onFail: function (ei) {
                                IPLAT.alert(ei.getMsg());
                            }
                        });
                        window1.data("kendoWindow").close();
                    } else {
                        IPLAT.alert("没有选中数据!");
                    }
                });
            }
        }
    };

    var dataSource = new kendo.data.DataSource({
        transport: {
            read: {
                url: IPLATUI.CONTEXT_PATH + "/service/XS0500/search",
                type: 'POST',
                dataType: "json",
                contentType: "application/json"
            },
            parameterMap: function (options, operation) {
                var info = new EiInfo();
                info.set("inqu_status-0-groupText",$("#filterText").val());
                return info.toJSONString();
            }
        },
        schema: {
            data: function (response) {
                ajaxEi = EiInfo.parseJSONObject(response);
                return ajaxEi.getBlock("result").getMappedRows();
            }
        },
        pageSize: 30,
        serverFiltering: true
    });


    $("#filterText").kendoAutoComplete({
        dataSource: dataSource,
        minLength: 1,
        template: '#:parentText#-#:text#',
        enforceMinLength: true,
        select: function(e) {
            var _data = e.dataItem || {};
            var tree = $('#groupsTree').data('kendoTreeView');
            expandtree(tree, _data);
        },
        dataTextField: "text"
    });

    /**
     * 展开树
     */
    var expandtree = function(tree, data) {
        if(!data) {
            return;
        }
        var info = new EiInfo();
        info.set("inqu_status-0-label", data.label);
        info.set("inqu_status-0-parentId", data.parentId);
        // 查找点击分类的树路径
        EiCommunicator.send("XSLV0500","expandPath", info, {
            // 服务调用成功后的回调函数 onSuccess
            onSuccess: function(eiInfo){
                var datas = eiInfo.getBlock('result').getMappedRows();
                var path = [];
                for (var i = 0; i < datas.length; i++) {
                    path.push(datas[i].MEMBER_ID);
                }
                path = path.reverse();
                var treeOrgId = path[path.length-1];
                // 根据树路径逐级展开分类树
                tree.expandPath(path, function() {
                    // 展开成功后选中对应的树节点
                    var barDataItem = tree.dataSource.get(treeOrgId);
                    var barElement = tree.findByUid(barDataItem.uid);
                    tree.select(barElement);

                    $("#inqu_status-0-parentId").val(treeOrgId);
                    $("#inqu_status-0-memberType").val('USER');
                    $("#parentName").val(data.text);
                    resultGrid.dataSource.page(1);
                });
            },
            // 服务调用失败后的回调函数 onFail
            // errorMsg 是平台格式化后的异常消息， status， e的含义和$.ajax的含义相同，请参考jQuery文档
            onFail: function(errorMsg, status, e) {
                // 调用发生异常
                NotificationUtil(errorMsg, "error");
            }
        });
    };

    $("#father").css("height",document.body.clientHeight-$("#ef_form_head").outerHeight()-16);
    $("#orggrouptree").css("height","100%");
    $("#result").css("height",$("#rightChild").outerHeight()-$("#inqu").outerHeight()-8);
    var leftChild = document.getElementById('leftChild');
    var oLine = document.getElementById('line');
    var rightChild = document.getElementById('rightChild');
    oLine.onmousedown = function(ev){
        var iEvent = ev||event;
        var dx = iEvent.clientX;//当你第一次单击的时候，存储x轴的坐标。//相对于浏览器窗口
        var leftWidth = leftChild.offsetWidth;
        var rightWidth = rightChild.offsetWidth;
        document.onmousemove = function(ev){
            var iEvent = ev||event;
            var diff = iEvent.clientX - dx;//移动的距离（向左滑时为负数,右滑时为正数）
            if(100 < (leftWidth + diff)  &&  100 < (rightWidth - diff)){
                //两个div的最小宽度均为100px
                leftChild.style.width = (leftWidth + diff) +'px';
                rightChild.style.width = (rightWidth - diff) +'px';
            }
        };
        document.onmouseup=function(){
            document.onmousedown = null;
            document.onmousemove = null;
        };
        return false;
    }
});