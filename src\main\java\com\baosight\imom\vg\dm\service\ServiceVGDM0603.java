package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0301;
import com.baosight.imom.vg.dm.domain.VGDM0603;
import com.baosight.imom.vg.dm.domain.VGDM0604;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报警通知配置页面后台
 *
 * <AUTHOR> 郁在杰
 * @Description :
 * @Date : 2024/12/12
 * @Version : 1.0
 */
public class ServiceVGDM0603 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0603.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0603().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0603.QUERY, new VGDM0603());
    }

    /**
     * 查询明细
     */
    public EiInfo queryDetail(EiInfo inInfo) {
        String eArchivesNo = inInfo.getCellStr(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "eArchivesNo");
        if (StrUtil.isBlank(eArchivesNo)) {
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("无记录");
            return inInfo;
        }
        return super.query(inInfo, VGDM0604.QUERY, null, false, new VGDM0604().eiMetadata,
                MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            // 用于缓存已查询的设备和分部设备信息
            VGDM0603 vgdm0603;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0603 = new VGDM0603();
                vgdm0603.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0603);
                // 数据重复性校验
                vgdm0603.setUuid("");
                this.checkDataRepeat(vgdm0603);
                // 转换为map
                Map insMap = vgdm0603.toMap();
                // 设置创建人
                RecordUtils.setCreator(insMap);
                // 设置行
                block.getRows().set(i, insMap);
            }
            // 插入
            DaoUtils.insertBatch(dao, VGDM0603.INSERT, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 数据重复性校验
     *
     * @param vgdm0603 数据
     */
    private void checkDataRepeat(VGDM0603 vgdm0603) {
        Map<String, Object> map = new HashMap<>();
        map.put("delFlag", "0");
        map.put("eArchivesNo", vgdm0603.getEArchivesNo());
        map.put("uuid", vgdm0603.getUuid());
        int count = super.count(VGDM0603.COUNT_REPEAT, map);
        if (count > 0) {
            throw new PlatException("设备[" + vgdm0603.getEquipmentName() + "]配置已存在");
        }
    }

    /**
     * 数据校验
     *
     * @param vgdm0603 数据
     */
    private void checkData(VGDM0603 vgdm0603) {
        // 使用ValidationUtils进行基本的非空和数值校验
        ValidationUtils.validateEntity(vgdm0603);
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0603 vgdm0603;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0603 = new VGDM0603();
                vgdm0603.fromMap(block.getRow(i));
                // 基础校验
                this.checkData(vgdm0603);
                // 数据重复性校验
                this.checkDataRepeat(vgdm0603);
                // 默认状态
                vgdm0603.setStatus(MesConstant.Status.K10);
                vgdm0603.setDelFlag("0");
                // 转换为map
                Map updMap = vgdm0603.toMap();
                // 设置修改人
                RecordUtils.setRevisor(updMap);
                // 更新
                block.getRows().set(i, updMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM0603.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除
     *
     * <p>删除标记置1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0603 vgdm0603;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0603 = new VGDM0603();
                vgdm0603.fromMap(block.getRow(i));
                // 删除标记
                vgdm0603.setStatus(MesConstant.Status.K00);
                vgdm0603.setDelFlag("1");
                // 转换为map
                Map delMap = vgdm0603.toMap();
                // 设置修改人
                RecordUtils.setRevisor(delMap);
                // 设置行
                block.getRows().set(i, delMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM0603.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 启用
     */
    public EiInfo open(EiInfo inInfo) {
        return this.updateStatus(inInfo, MesConstant.Status.K20, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_DISABLE_STATUS, MesConstant.Status.K10);
    }

    /**
     * 停用
     */
    public EiInfo close(EiInfo inInfo) {
        return this.updateStatus(inInfo, MesConstant.Status.K10, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_ENABLE_STATUS, MesConstant.Status.K20);
    }

    /**
     * 更新状态
     *
     * @param inInfo       请求信息
     * @param sourceStatus 源状态
     * @param errMsg       错误信息
     * @param targetStatus 目标状态
     * @return 返回信息
     */
    private EiInfo updateStatus(EiInfo inInfo, String sourceStatus, String errMsg, String targetStatus) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0603 vgdm0603;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0603 = new VGDM0603();
                vgdm0603.fromMap(block.getRow(i));
                // 状态校验
                VGDM0603 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0603, errMsg, sourceStatus);
                // 状态
                dbData.setStatus(targetStatus);
                // 转换为map
                Map updMap = dbData.toMap();
                // 设置修改人
                RecordUtils.setRevisor(updMap);
                // 设置行
                block.getRows().set(i, updMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM0603.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 添加至报警规则
     * <p>
     * 从TVGDM0301表数据转换到TVGDM0604表中
     * 前端页面为VGDM03页面
     */
    public EiInfo convert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0301 vgdm0301;
            VGDM0604 vgdm0604;
            List<Map> insList = new ArrayList<>();
            List<String> uuidList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                // 查询点位
                String tagId = block.getCellStr(i, "tagId");
                vgdm0301 = VGDM0301.queryWithCache(dao, tagId);
                if (vgdm0301 == null) {
                    throw new PlatException("点位不存在");
                }
                // 去重
                if (uuidList.contains(vgdm0301.getUuid())) {
                    continue;
                }
                uuidList.add(vgdm0301.getUuid());
                // 转换
                vgdm0604 = new VGDM0604();
                vgdm0604.fromMap(vgdm0301.toMap());
                // 校验数据是否已存在
                checkExist(vgdm0604);
                // 添加到插入列表
                Map insMap = vgdm0604.toMap();
                RecordUtils.setCreator(insMap);
                insList.add(insMap);
                // 返回前端
                block.getRows().set(i, vgdm0301.toMap());
            }
            // 批量插入
            DaoUtils.insertBatch(dao, VGDM0604.INSERT, insList);
            // 设置返回信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception e) {
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 校验数据是否已存在
     */
    private void checkExist(VGDM0604 vgdm0604) {
        Map<String, String> map = new HashMap<>();
        map.put("equalId", vgdm0604.getTagId());
        map.put("segNo", vgdm0604.getSegNo());
        map.put("unitCode", vgdm0604.getUnitCode());
        int count = super.count(VGDM0604.COUNT, map);
        if (count > 0) {
            throw new PlatException("数据已存在,不能重复添加");
        }
    }

    /**
     * 修改明细
     */
    public EiInfo update2(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0604 vgdm0604;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0604 = new VGDM0604();
                vgdm0604.fromMap(block.getRow(i));
                // 基础校验
                ValidationUtils.validateEntity(vgdm0604);
                // 默认状态
                vgdm0604.setDelFlag("0");
                // 转换为map
                Map updMap = vgdm0604.toMap();
                // 设置修改人
                RecordUtils.setRevisor(updMap);
                // 更新
                block.getRows().set(i, updMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM0604.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 删除明细
     */
    public EiInfo delete2(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0604 vgdm0604;
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0604 = new VGDM0604();
                vgdm0604.fromMap(block.getRow(i));
                // 默认状态
                vgdm0604.setDelFlag("1");
                // 转换为map
                Map updMap = vgdm0604.toMap();
                // 设置修改人
                RecordUtils.setRevisor(updMap);
                // 更新
                block.getRows().set(i, updMap);
            }
            // 更新
            DaoUtils.updateBatch(dao, VGDM0604.UPDATE, block.getRows());
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
