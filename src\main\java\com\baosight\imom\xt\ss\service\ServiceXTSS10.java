package com.baosight.imom.xt.ss.service;


import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 首页待办任务、已办任务相关接口,首页查询兼容中文修改
 */
public class ServiceXTSS10 extends ServiceBase {

    private static final Logger logger = LogManager.getLogger(ServiceXTSS10.class);

    /**
     * 获取登录用户待办任务列表
     */
    public EiInfo getTask(EiInfo inInfo) {
        try {
            EiInfo info = new EiInfo();
            info.set("loginName", UserSession.getLoginName());
            info.set(EiConstant.serviceId, "S_EW_70");
            EiInfo outInfo = XServiceManager.call(info);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List<Map> list = (List<Map>) outInfo.get("result");
            List<Map> newList = new ArrayList<>();
            for (Map map : list) {
                String taskDefKey = map.get("taskDefKey").toString();
                // 排除开始节点
                if ("Manual1".equals(taskDefKey)) {
                    continue;
                }
                newList.add(map);
            }
            inInfo.set("result", newList);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 获取登录用户已办任务列表
     */
    public EiInfo getHistoryTask(EiInfo inInfo) {
        try {
            EiInfo info = new EiInfo();
            info.set("userId", UserSession.getLoginName());
            info.set(EiConstant.serviceId, "S_EW_72");
            EiInfo outInfo = XServiceManager.call(info);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            return outInfo;
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }


    /**
     * 页面查询修改成双语查询
     * @param eiInfo
     * @return
     */
    public EiInfo getPageList(EiInfo eiInfo) {

        String pageEname = eiInfo.getCellStr("inqu_status", 0, "form_ename");
        String msg = "";
        int status = 0;
        List list = null;
        EiInfo outInfo  = new EiInfo();

        try {
            if (null != pageEname) {
                pageEname = pageEname.toUpperCase();
                if (pageEname.indexOf("'") != -1) {
                    pageEname = pageEname.replaceAll("'", "");
                }
                Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]+");

                Map<String, String> map2 = new HashMap();
                map2.put("module_ename_1", "");
                if(pattern.matcher(pageEname).matches()){
                    map2.put("form_cname", pageEname);
                }else{
                    map2.put("form_ename", pageEname);
                }
                list = this.dao.query("EDFA00.query", map2);
            } else {
                msg = "请输入要查询的页面号";
            }
        } catch (Exception var7) {
            status = -1;
            msg = var7.getMessage();
        }


        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(list);
        outInfo.addBlock(eiBlock);
        return outInfo;
    }
}
