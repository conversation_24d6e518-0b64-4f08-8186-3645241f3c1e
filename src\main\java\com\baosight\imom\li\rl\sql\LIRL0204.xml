<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYP<PERSON> sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-02-07 9:06:11
   		Version :  1.0
		tableName :meli.tlirl0204 
		 RESERVATION_NUMBER  VARCHAR   NOT NULL   primarykey, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 STATUS  VARCHAR   NOT NULL, 
		 VISIT_UNIT_CODE  VARCHAR   NOT NULL, 
		 VISIT_UNIT_NAME  VARCHAR   NOT NULL, 
		 VISITOR_NAME  VARCHAR   NOT NULL, 
		 VISITOR_TEL  VARCHAR   NOT NULL, 
		 VISITOR_IDENTITY  VARCHAR   NOT NULL, 
		 VISITOR_GENDER  VARCHAR   NOT NULL, 
		 RESERVATION_DATE  VARCHAR   NOT NULL, 
		 RESERVATION_TIME  VARCHAR   NOT NULL, 
		 RESERVATION_DATE_END  VARCHAR   NOT NULL, 
		 RESERVATION_TIME_END  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0204">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitCode">
			VISIT_UNIT_CODE = #visitUnitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitName">
			VISIT_UNIT_NAME = #visitUnitName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorName">
			VISITOR_NAME = #visitorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorTel">
			VISITOR_TEL = #visitorTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorIdentity">
			VISITOR_IDENTITY = #visitorIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorGender">
			VISITOR_GENDER = #visitorGender#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationDate">
			RESERVATION_DATE = #reservationDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationTime">
			RESERVATION_TIME = #reservationTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationDateEnd">
			RESERVATION_DATE_END = #reservationDateEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationTimeEnd">
			RESERVATION_TIME_END = #reservationTimeEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0204">
		SELECT
				RESERVATION_NUMBER	as "reservationNumber",  <!-- 预约单号 -->
				SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				STATUS	as "status",  <!-- 状态(00：撤销，20：生效，99：完成) -->
				VISIT_UNIT_CODE	as "visitUnitCode",  <!-- 拜访单位代码 -->
				VISIT_UNIT_NAME	as "visitUnitName",  <!-- 拜访单位 -->
				VISITOR_NAME	as "visitorName",  <!-- 访客姓名 -->
				VISITOR_TEL	as "visitorTel",  <!-- 访客电话 -->
				VISITOR_IDENTITY	as "visitorIdentity",  <!-- 访客身份 -->
				VISITOR_GENDER	as "visitorGender",  <!-- 访客性别 -->
				RESERVATION_DATE	as "reservationDate",  <!-- 预约日期 -->
				RESERVATION_TIME	as "reservationTime",  <!-- 预约时段 -->
				RESERVATION_DATE_END	as "reservationDateEnd",  <!-- 预约截止日期 -->
				RESERVATION_TIME_END	as "reservationTimeEnd",  <!-- 预约截止时段 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 说明备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0204 WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  RESERVATION_NUMBER asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0204 WHERE 1=1
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="reservationNumber">
			RESERVATION_NUMBER = #reservationNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitCode">
			VISIT_UNIT_CODE = #visitUnitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitUnitName">
			VISIT_UNIT_NAME = #visitUnitName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorName">
			VISITOR_NAME = #visitorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorTel">
			VISITOR_TEL = #visitorTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorIdentity">
			VISITOR_IDENTITY = #visitorIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="visitorGender">
			VISITOR_GENDER = #visitorGender#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationDate">
			RESERVATION_DATE = #reservationDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationTime">
			RESERVATION_TIME = #reservationTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationDateEnd">
			RESERVATION_DATE_END = #reservationDateEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="reservationTimeEnd">
			RESERVATION_TIME_END = #reservationTimeEnd#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0204 (RESERVATION_NUMBER,  <!-- 预约单号 -->
										SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										STATUS,  <!-- 状态(00：撤销，20：生效，99：完成) -->
										VISIT_UNIT_CODE,  <!-- 拜访单位代码 -->
										VISIT_UNIT_NAME,  <!-- 拜访单位 -->
										VISITOR_NAME,  <!-- 访客姓名 -->
										VISITOR_TEL,  <!-- 访客电话 -->
										VISITOR_IDENTITY,  <!-- 访客身份 -->
										VISITOR_GENDER,  <!-- 访客性别 -->
										RESERVATION_DATE,  <!-- 预约日期 -->
										RESERVATION_TIME,  <!-- 预约时段 -->
										RESERVATION_DATE_END,  <!-- 预约截止日期 -->
										RESERVATION_TIME_END,  <!-- 预约截止时段 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 说明备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID  <!-- 租户ID -->
										)		 
	    VALUES (#reservationNumber#, #segNo#, #unitCode#, #status#, #visitUnitCode#, #visitUnitName#, #visitorName#, #visitorTel#, #visitorIdentity#, #visitorGender#, #reservationDate#, #reservationTime#, #reservationDateEnd#, #reservationTimeEnd#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0204 WHERE 
			RESERVATION_NUMBER = #reservationNumber#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0204 
		SET 
					SEG_NO	= #segNo#,   <!-- 业务单元代代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					STATUS	= #status#,   <!-- 状态(00：撤销，20：生效，99：完成) -->  
					VISIT_UNIT_CODE	= #visitUnitCode#,   <!-- 拜访单位代码 -->  
					VISIT_UNIT_NAME	= #visitUnitName#,   <!-- 拜访单位 -->  
					VISITOR_NAME	= #visitorName#,   <!-- 访客姓名 -->  
					VISITOR_TEL	= #visitorTel#,   <!-- 访客电话 -->  
					VISITOR_IDENTITY	= #visitorIdentity#,   <!-- 访客身份 -->  
					VISITOR_GENDER	= #visitorGender#,   <!-- 访客性别 -->  
					RESERVATION_DATE	= #reservationDate#,   <!-- 预约日期 -->  
					RESERVATION_TIME	= #reservationTime#,   <!-- 预约时段 -->  
					RESERVATION_DATE_END	= #reservationDateEnd#,   <!-- 预约截止日期 -->  
					RESERVATION_TIME_END	= #reservationTimeEnd#,   <!-- 预约截止时段 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 说明备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			RESERVATION_NUMBER = #reservationNumber#
	</update>

	<select id="queryApp" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		RESERVATION_NUMBER	as "reservationNumber",  <!-- 预约单号 -->
		SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
		STATUS	as "status",  <!-- 状态(00：撤销，20：生效，99：完成) -->
		VISIT_UNIT_CODE	as "visitUnitCode",  <!-- 拜访单位代码 -->
		VISIT_UNIT_NAME	as "visitUnitName",  <!-- 拜访单位 -->
		VISITOR_NAME	as "visitorName",  <!-- 访客姓名 -->
		VISITOR_TEL	as "visitorTel",  <!-- 访客电话 -->
		VISITOR_IDENTITY	as "visitorIdentity",  <!-- 访客身份 -->
		VISITOR_GENDER	as "visitorGender",  <!-- 访客性别 -->
		RESERVATION_DATE	as "reservationDate",  <!-- 预约日期 -->
		RESERVATION_TIME	as "reservationTime",  <!-- 预约时段 -->
		RESERVATION_DATE_END	as "reservationDateEnd",  <!-- 预约截止日期 -->
		RESERVATION_TIME_END	as "reservationTimeEnd",  <!-- 预约截止时段 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 说明备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0204 t WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				RESERVATION_NUMBER asc
			</isEmpty>
		</dynamic>

	</select>

	<update id="updateStatus">
		UPDATE meli.tlirl0204
		SET
		STATUS = #status#,
		REC_REVISOR = #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG = #delFlag#<!-- 状态(00：撤销，10：新增) -->
		WHERE 1=1
		and  SEG_NO	= #segNo#
		and RESERVATION_NUMBER = #reservationNumber#
	</update>

</sqlMap>