create table TMEDV0003
(
    E_ARCHIVES_NO   VARCHAR(20)  default ' '    not null comment '设备档案编号',
    EQUIPMENT_NAME  VARCHAR(200) default ' '    not null comment '设备名称',
    PARAM_CODE      VARCHAR(20)  default ' '    not null comment '参数代码',
    PARAM_NAME      VARCHAR(32)  default ' '    not null comment '参数名称',
    PARAM_DESC      VARCHAR(64)  default ' '    not null comment '参数描述',
    AUTO_FLAG       VARCHAR(1)   default ' '    not null comment '是否自动读取',
    TAG_ID          VARCHAR(64)  default ' '    not null comment '节点名称',
    -- 固定字段
    UUID            VARCHAR(32)                 NOT NULL COMMENT '唯一编码',
    REC_CREATOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR     VARCHAR(16)  DEFAULT ' '    NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL COMMENT '记录修改时刻',
    TENANT_ID       VARCHAR(64)  DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG    VARCHAR(1)   DEFAULT '0'    NOT NULL COMMENT '归档标记',
    primary key (UUID),
    FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO),
    FOREIGN KEY (TAG_ID) REFERENCES TMEDV0007(TAG_ID)



) COMMENT ='工艺参数配置表' ENGINE = INNODB
                      DEFAULT CHARSET = UTF8
                      COLLATE UTF8_BIN;


-- 增加ER图外键
ALTER TABLE TMEDV0003 ADD unique KEY (PARAM_CODE);
ALTER TABLE TMEDV0003 ADD FOREIGN KEY (E_ARCHIVES_NO) REFERENCES TMEDV0101(E_ARCHIVES_NO);
ALTER TABLE TMEDV0003 ADD FOREIGN KEY (TAG_ID) REFERENCES TMEDV0007(TAG_ID);