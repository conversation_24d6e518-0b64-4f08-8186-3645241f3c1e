package com.baosight.imom.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Utils {

    /**
     * MD5加密工具方法
     *
     * @param input 明文字符串
     * @return 32位小写MD5加密字符串
     * @throws RuntimeException 如果加密失败
     */
    public static String encrypt(String input) {
        if (input == null) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        try {
            // 1. 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 2. 计算哈希值（字节数组）
            byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 3. 将字节数组转为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : bytes) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}