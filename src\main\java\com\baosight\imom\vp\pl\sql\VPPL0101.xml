<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-01-20 10:54:54
   		Version :  1.0
		tableName :mevp.tvppl0101 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CHIP_ID  VARCHAR, 
		 STAFF_NAME  VARCHAR, 
		 TEAM_ID  VARCHAR, 
		 WORKING_SHIFT  VARCHAR, 
		 WORKING_HOURS  VARCHAR, 
		 POST_RESPONSIBILITY  VARCHAR, 
		 STATUS  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="VPPL0101">

	<sql id="condition">

		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="chipId">
			CHIP_ID like  '%$chipId$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cardIds">
			CHIP_ID IN ($cardIds$)
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="staffName">
			STAFF_NAME like  '%$staffName$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="teamId">
			TEAM_ID = #teamId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="workingShift">
			WORKING_SHIFT = #workingShift#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="workingHours">
			WORKING_HOURS = #workingHours#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="postResponsibility">
			POST_RESPONSIBILITY = #postResponsibility#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jobScope">
			JOB_SCOPE = #jobScope#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.vp.pl.domain.VPPL0101">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as "segName", <!-- 业务单元简称 -->
				CHIP_ID	as "chipId",  <!-- 芯片ID -->
		        EMP_NO as "empNo",<!--员工工号-->
				STAFF_NAME	as "staffName",  <!-- 人员姓名 -->
				TEAM_ID	as "teamId",  <!-- 班组 -->
				WORKING_SHIFT	as "workingShift",  <!-- 班次 -->
				WORKING_HOURS	as "workingHours",  <!-- 当班时间 -->
				POST_RESPONSIBILITY	as "postResponsibility",  <!-- 人员岗位责任 -->
				JOB_SCOPE as "jobScope",<!--岗位范围-->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid", <!-- ID -->
				PHONE_NUMBER as "phoneNumber"<!--联系电话-->
		FROM mevp.tvppl0101 t WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
			<isEmpty property="orderBy">
				DEL_FLAG , REC_CREATE_TIME  DESC
			</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*)
		FROM mevp.tvppl0101
		WHERE  SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="chipId">
			CHIP_ID =  #chipId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
	</select>


	<insert id="insert">
		INSERT INTO mevp.tvppl0101 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										CHIP_ID,  <!-- 芯片ID -->
		 								EMP_NO,<!--员工工号-->
										STAFF_NAME,  <!-- 人员姓名 -->
										TEAM_ID,  <!-- 班组 -->
										WORKING_SHIFT,  <!-- 班次 -->
										WORKING_HOURS,  <!-- 当班时间 -->
										POST_RESPONSIBILITY,  <!-- 人员岗位责任 -->
										JOB_SCOPE,<!--岗位范围-->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID,  <!-- ID -->
										PHONE_NUMBER
										)		 
	    VALUES (#segNo#, #unitCode#, #chipId#,#empNo#,#staffName#, #teamId#, #workingShift#, #workingHours#, #postResponsibility#,#jobScope#,#status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#, #phoneNumber#)
	</insert>
  
	<delete id="delete">
		DELETE FROM mevp.tvppl0101 WHERE 
	</delete>

	<update id="update">
		UPDATE mevp.tvppl0101 
		SET
					CHIP_ID	= #chipId#,   <!-- 芯片ID -->
					EMP_NO = #empNo#,<!--员工工号-->
					STAFF_NAME	= #staffName#,   <!-- 人员姓名 -->  
					TEAM_ID	= #teamId#,   <!-- 班组 -->  
					WORKING_SHIFT	= #workingShift#,   <!-- 班次 -->  
					WORKING_HOURS	= #workingHours#,   <!-- 当班时间 -->  
					POST_RESPONSIBILITY	= #postResponsibility#,   <!-- 人员岗位责任 -->
					JOB_SCOPE = #jobScope#,<!--岗位范围-->
					STATUS	= #status#,   <!-- 状态 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					UUID	= #uuid#,  <!-- ID -->
					PHONE_NUMBER = #phoneNumber#
			WHERE 	UUID	= #uuid#
	</update>

	<update id="updateStatus">
		UPDATE mevp.tvppl0101
		SET
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		STATUS	= #status#,   <!-- 状态 -->
		DEL_FLAG = #delFlag#  <!-- 删除标记 -->
		WHERE UUID	= #uuid#
	</update>

	<!--前台明细导出	-->
	<select id="queryExport" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		SEG_NO as "segNo",  <!-- 系统账套 -->
		UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		CHIP_ID as "chipId",  <!-- 芯片ID -->
		EMP_NO as "empNo",<!--员工工号-->
		STAFF_NAME as "staffName",  <!-- 人员姓名 -->
		CASE
		WHEN TEAM_ID = '10' THEN '甲班'
		WHEN TEAM_ID = '20' THEN '乙班'
		WHEN TEAM_ID = '30' THEN '丙班'
		WHEN TEAM_ID = '40' THEN '丁班'
		ELSE '未知'
		END as "teamId",  <!-- 班组 -->
		CASE
		WHEN WORKING_SHIFT = '10' THEN '早班'
		WHEN WORKING_SHIFT = '20' THEN '中班'
		WHEN WORKING_SHIFT = '30' THEN '晚班'
		ELSE '未知'
		END as "workingShift",  <!-- 班次 -->
		WORKING_HOURS as "workingHours",  <!-- 当班时间 -->
		CASE
		WHEN POST_RESPONSIBILITY = 'HC' THEN '行车工'
		WHEN POST_RESPONSIBILITY = 'JZ' THEN '机组操作工'
		ELSE '未知'
		END as "postResponsibility",  <!-- 人员岗位责任 -->
		CASE
		WHEN JOB_SCOPE = 'L1' THEN '2050落料机组'
		WHEN JOB_SCOPE = 'Z4' THEN '1650高强钢纵切机组'
		WHEN JOB_SCOPE = 'J4' THEN '800横切线'
		WHEN JOB_SCOPE = 'H1' THEN '1650横切机组'
		ELSE '未知'
		END as "jobScope",<!--岗位范围-->
		(SELECT ITEM_CNAME
		FROM iplat4j.TEDCM01 t1
		WHERE t1.ITEM_CODE = t.STATUS
		AND t1.CODESET_CODE = 'P032') as "status",  <!-- 状态 -->
		REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		TENANT_USER as "tenantUser",  <!-- 租户 -->
		DEL_FLAG as "delFlag",  <!-- 删除标记 -->
		UUID as "uuid", <!-- ID -->
		PHONE_NUMBER as "phoneNumber"
		FROM mevp.tvppl0101 t WHERE 1=1
		AND SEG_NO = #segNo#
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				DEL_FLAG , REC_CREATE_TIME DESC
			</isEmpty>
		</dynamic>
	</select>
</sqlMap>