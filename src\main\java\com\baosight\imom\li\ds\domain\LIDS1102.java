/**
 * Generate time : 2024-10-14 10:47:43
 * Version : 1.0
 */
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids1102;

import java.util.Map;

/**
 * TLIDS1102
 *
 */
public class LIDS1102 extends Tlids1102 {
    public static final String QUERY = "LIDS1102.query";
    public static final String COUNT = "LIDS1102.count";
    public static final String COUNT_UUID = "LIDS1102.count_uuid";
    public static final String INSERT = "LIDS1102.insert";
    public static final String UPDATE = "LIDS1102.update";
    public static final String DELETE = "LIDS1102.delete";
    public static final String DELETE_FLAG = "LIDS1102.deleteFlag";

    public static final String QUERY_DISTINCT_CRANE_ORDER_ID = "LIDS1102.queryDistinctCraneOrderId";
    public static final String UPDATE_STATUS = "LIDS1102.updateStatus";

    @Override
    public void initMetaData() {
        super.initMetaData();
    }

    /**
     * the constructor
     */
    public LIDS1102() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        super.fromMap(map);
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = super.toMap();
        return map;
    }
}