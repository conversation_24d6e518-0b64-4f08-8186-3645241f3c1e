<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="tvgdm0611">

    <select id="query" parameterClass="java.util.Map"
            resultClass="com.baosight.imom.common.vg.domain.Tvgdm0611">
        SELECT
        ALARM_ID as "alarmId",  <!-- 报警ID -->
        SCADA_NAME as "scadaName",  <!-- scada节点名 -->
        ALARM_TAG as "alarmTag",  <!-- 报警点 -->
        ALARM_TAG_DESC as "alarmTagDesc",  <!-- 报警点描述 -->
        ALARM_ADDRESS as "alarmAddress",  <!-- 报警地址 -->
        CONFIRM_STATUS as "confirmStatus",  <!-- 确认状态 -->
        CONFIRMOR as "confirmor",  <!-- 确认人 -->
        CONFIRMOR_NAME as "confirmorName",  <!-- 确认人姓名 -->
        CONFIRM_TIME as "confirmTime",  <!-- 确认时间 -->
        OCCUR_TIME as "occurTime",  <!-- 发生时间 -->
        RECOVER_TIME as "recoverTime",  <!-- 恢复时间 -->
        ALARM_TYPE as "alarmType",  <!-- 报警类型 -->
        ALARM_TAG_VALUE as "alarmTagValue",  <!-- 报警值 -->
        RECOVER_TAG_VALUE as "recoverTagValue",  <!-- 恢复值 -->
        PRIORITY as "priority",  <!-- 优先级 -->
        ALARM_STATE as "alarmState",  <!-- 报警状态 -->
        REPEAT_COUNT as "repeatCount",  <!-- 重复报警次数 -->
        ALARM_TYPEDM as "alarmTypedm",  <!-- 报警类型代码 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        E_ARCHIVES_NO as "e_archivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        IS_FAULT as "isFault",  <!-- 是否故障 -->
        PUSH_HANDLE_FLAG as "pushHandleFlag",  <!-- 推送处理标记 -->
        PUSH_HANDLE_TIME as "pushHandleTime",  <!-- 推送处理时间 -->
        PUSH_MANAGE_FLAG as "pushManageFlag",  <!-- 推送管理标记 -->
        PUSH_MANAGE_TIME as "pushManageTime",  <!-- 推送管理时间 -->
        ARCHIVE_TIME as "archiveTime",  <!-- 归档时间 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM ${projectSchema}.tvgdm0611 WHERE 1=1
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${projectSchema}.tvgdm0611 WHERE 1=1
    </select>

    <insert id="insert">
        INSERT INTO ${projectSchema}.tvgdm0611 (ALARM_ID,  <!-- 报警ID -->
        SCADA_NAME,  <!-- scada节点名 -->
        ALARM_TAG,  <!-- 报警点 -->
        ALARM_TAG_DESC,  <!-- 报警点描述 -->
        ALARM_ADDRESS,  <!-- 报警地址 -->
        CONFIRM_STATUS,  <!-- 确认状态 -->
        CONFIRMOR,  <!-- 确认人 -->
        CONFIRMOR_NAME,  <!-- 确认人姓名 -->
        CONFIRM_TIME,  <!-- 确认时间 -->
        OCCUR_TIME,  <!-- 发生时间 -->
        RECOVER_TIME,  <!-- 恢复时间 -->
        ALARM_TYPE,  <!-- 报警类型 -->
        ALARM_TAG_VALUE,  <!-- 报警值 -->
        RECOVER_TAG_VALUE,  <!-- 恢复值 -->
        PRIORITY,  <!-- 优先级 -->
        ALARM_STATE,  <!-- 报警状态 -->
        REPEAT_COUNT,  <!-- 重复报警次数 -->
        ALARM_TYPEDM,  <!-- 报警类型代码 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        IS_FAULT,  <!-- 是否故障 -->
        PUSH_HANDLE_FLAG,  <!-- 推送处理标记 -->
        PUSH_HANDLE_TIME,  <!-- 推送处理时间 -->
        PUSH_MANAGE_FLAG,  <!-- 推送管理标记 -->
        PUSH_MANAGE_TIME,  <!-- 推送管理时间 -->
        ARCHIVE_TIME,  <!-- 归档时间 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#alarmId#, #scadaName#, #alarmTag#, #alarmTagDesc#, #alarmAddress#, #confirmStatus#, #confirmor#,
        #confirmorName#, #confirmTime#, #occurTime#, #recoverTime#, #alarmType#, #alarmTagValue#, #recoverTagValue#,
        #priority#, #alarmState#, #repeatCount#, #alarmTypedm#, #deviceCode#, #deviceName#, #e_archivesNo#,
        #equipmentName#, #isFault#, #pushHandleFlag#, #pushHandleTime#, #pushManageFlag#, #pushManageTime#,
        #archiveTime#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM ${projectSchema}.tvgdm0611 WHERE
    </delete>

    <update id="update">
        UPDATE ${projectSchema}.tvgdm0611
        SET
        ALARM_ID = #alarmId#,   <!-- 报警ID -->
        SCADA_NAME = #scadaName#,   <!-- scada节点名 -->
        ALARM_TAG = #alarmTag#,   <!-- 报警点 -->
        ALARM_TAG_DESC = #alarmTagDesc#,   <!-- 报警点描述 -->
        ALARM_ADDRESS = #alarmAddress#,   <!-- 报警地址 -->
        CONFIRM_STATUS = #confirmStatus#,   <!-- 确认状态 -->
        CONFIRMOR = #confirmor#,   <!-- 确认人 -->
        CONFIRMOR_NAME = #confirmorName#,   <!-- 确认人姓名 -->
        CONFIRM_TIME = #confirmTime#,   <!-- 确认时间 -->
        OCCUR_TIME = #occurTime#,   <!-- 发生时间 -->
        RECOVER_TIME = #recoverTime#,   <!-- 恢复时间 -->
        ALARM_TYPE = #alarmType#,   <!-- 报警类型 -->
        ALARM_TAG_VALUE = #alarmTagValue#,   <!-- 报警值 -->
        RECOVER_TAG_VALUE = #recoverTagValue#,   <!-- 恢复值 -->
        PRIORITY = #priority#,   <!-- 优先级 -->
        ALARM_STATE = #alarmState#,   <!-- 报警状态 -->
        REPEAT_COUNT = #repeatCount#,   <!-- 重复报警次数 -->
        ALARM_TYPEDM = #alarmTypedm#,   <!-- 报警类型代码 -->
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        E_ARCHIVES_NO = #e_archivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        IS_FAULT = #isFault#,   <!-- 是否故障 -->
        PUSH_HANDLE_FLAG = #pushHandleFlag#,   <!-- 推送处理标记 -->
        PUSH_HANDLE_TIME = #pushHandleTime#,   <!-- 推送处理时间 -->
        PUSH_MANAGE_FLAG = #pushManageFlag#,   <!-- 推送管理标记 -->
        PUSH_MANAGE_TIME = #pushManageTime#,   <!-- 推送管理时间 -->
        ARCHIVE_TIME = #archiveTime#,   <!-- 归档时间 -->
        UUID = #uuid#,   <!-- 唯一编码 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#  <!-- 业务单元代码 -->
        WHERE
    </update>

</sqlMap>