<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFInput ename="inqu_status-0-craneOrderId" cname="行车作业清单号" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-voucherNum" cname="依据凭单号" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-craneId" cname="行车编号" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-craneName" cname="行车名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFSelect ename="inqu_status-0-listSource" cname="清单来源" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P042"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P041"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFDateSpan startName="inqu_status-0-recCreateTimeStart"
                           endName="inqu_status-0-recCreateTimeEnd"
                           startCname="起始创建时间" endCname="截止创建时间"
                           parseFormats="['yyyyMMddHHmm']"
                           colWidth="3" ratio="3:3" readonly="true"
                           format="yyyyMMddHHmm" role="datetime" interval="15"/>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="行车作业清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" checkMode="single,row">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="craneOrderId" cname="行车作业清单号" align="center" width="150"
                             primaryKey="true" enable="false"/>
                <EF:EFComboColumn ename="listSource" cname="清单来源" align="center" width="150" enable="false">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P042"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="voucherNum" cname="依据凭单号" align="left" width="200" enable="false"/>
                <EF:EFColumn ename="craneId" cname="行车编号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="craneName" cname="行车名称" align="left" width="200" enable="false"/>
                <EF:EFColumn ename="batchNumber" cname="批次号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="serialNumber" cname="顺序号" align="center" width="200" enable="false"/>
                <EF:EFComboColumn ename="startAreaType" cname="起始区域类型" align="center" width="150" enable="true"
                                  required="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P031"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="startAreaCode" cname="起始区域代码" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="startAreaName" cname="起始区域名称" align="left" width="200" enable="false"/>
                <EF:EFComboColumn ename="endAreaType" cname="终到区域类型" align="center" width="150" enable="true"
                                  required="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P031"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="endAreaCode" cname="终到区域代码" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="endAreaName" cname="终到区域类型名称" align="left" width="200" enable="false"/>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P041"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="machineCode" cname="机组代码" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="machineName" cname="机组名称" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="unpackAreaId" cname="拆包区编号" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="unpackAreaName" cname="拆包区名称" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="mouldId" cname="模具ID" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="mouldName" cname="模具名称" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="startTime" cname="作业开始时间" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="endTime" cname="作业结束时间" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="jobTime" cname="作业时间" align="center" width="200" enable="false"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>

    <EF:EFRegion id="subResult" title="行车作业子项清单">
        <EF:EFGrid isFloat="true" id="subResult" blockId="subResult" autoBind="false" autoDraw="no" needAuth="true"
        queryMethod="querySubResult">
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120"
                         required="true"
                         enable="false"/>
            <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
            <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100"
                         enable="false" hidden="true"/>
            <EF:EFColumn ename="craneOrderId" cname="行车作业清单号" align="center" width="150"
                         primaryKey="true" enable="false"/>
            <EF:EFColumn ename="craneOrderSubId" cname="行车作业清单子项号" align="left" width="200" enable="false"/>
            <EF:EFColumn ename="packId" cname="捆包号" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="labelId" cname="标签号" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="netWeight" cname="吊装重量(吨)" align="center" width="200" enable="false"/>
            <EF:EFColumn ename="quantity" cname="数量" align="center" width="200" enable="false"/>
            <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                <EF:EFOption label="" value=""/>
                <EF:EFCodeOption codeName="P041"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="recCreator" cname="记录创建人" align="left" width="100" enable="false"/>
            <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="left" width="120"
                         enable="false"/>
            <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
            <EF:EFColumn ename="recRevisor" cname="记录修改人" align="left" width="100" enable="false"/>
            <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="left" width="120"
                         enable="false"/>
            <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
            <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100"
                         enable="true" hidden="true"/>
            <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
        </EF:EFGrid>
    </EF:EFRegion>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>

</EF:EFPage>
