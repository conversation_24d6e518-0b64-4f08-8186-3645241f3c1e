package com.baosight.imom.vg.dm.domain;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.vg.domain.Tvgdm0603;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

/**
 * 设备报警信息表
 */
public class VGDM0603 extends Tvgdm0603 implements CheckStatus{
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0603.query";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0603.count";
    /**
     * 查询条数
     */
    public static final String COUNT_REPEAT = "VGDM0603.countRepeat";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0603.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0603.update";

    @Override
    public String getCheckStatus() {
        return getStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }
    /**
     * 根据设备档案编号查询
     */
    public static VGDM0603 queryByEArchivesNo(Dao dao, String eArchivesNo) {
        if (StrUtil.isBlank(eArchivesNo)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("eArchivesNo", eArchivesNo);
        map.put("delFlag", "0");
        map.put("status", MesConstant.Status.K10);
        List<VGDM0603> list = dao.query(QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }
}
