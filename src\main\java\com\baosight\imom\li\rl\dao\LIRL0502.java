/**
* Generate time : 2025-01-06 10:29:36
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0502
* 
*/
public class LIRL0502 extends DaoEPBase {
    public static final String QUERY_PAGE = "LIRL0502.queryPage";
    public static final String QUERY = "LIRL0502.query";
    public static final String QUERY_ALL_VOUCHER_NUM = "LIRL0502.queryAllVoucherNum";
    public static final String QUERY_ALL_VOUCHER_NUM_PACK = "LIRL0502.queryAllVoucherNumPack";
    public static final String QUERY_ALL_VOUCHER_NUM_ALL_PACK = "LIRL0502.queryAllVoucherNumAllPack";
    public static final String QUERY_ALL = "LIRL0502.queryAll";
    public static final String QUERY_ENTERING_THE_FACTORY = "LIRL0502.queryEnteringTheFactory";
    public static final String COUNT = "LIRL0502.count";
    public static final String UPDATE = "LIRL0502.update";
    public static final String UPDATE_CAR_TRACE_NO = "LIRL0502.updateCarTraceNo";
    public static final String INSERT = "LIRL0502.insert";
    public static final String UPDATE_STATUS = "LIRL0502.updateStatus";
    public static final String QUERY_ALL_LOADING = "LIRL0501.queryAllLoading";
    public static final String QUERY_EMERGENCY_INFO = "LIRL0502.queryEmergencyInfo";
    public static final String UPDATE_STATUS_ALLOCATE_VEHICLE_NO = "LIRL0502.updateStatusAllocateVehicleNo";
    public static final String UPDATE_STATUS_ALLOCATE = "LIRL0502.updateStatusAllocate";
    public static final String QUERY_PACK_DATA_FOR_KANBAN_CQ = "LIRL0502.queryPackDataForKanbanCQ";
    //更新配车单状态不为完成的数据,更新状态为完成，拼接备注
    public static final String UPDATE_COMPLETE_BY_ALLOCATE_VEHICLE_NO = "LIRL0502.updateCompleteByAllocateVehicleNo";
    public static final String UPDATE_CAR_NO = "LIRL0502.updateCarNo";
    public static final String QUERY_ALL_VOUCHER_NUM_ALLOCATE = "LIRL0502.queryAllVoucherNumAllocate";
                private String segNo = " ";		/* 业务单元代码*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String segName = " ";        /* 业务单元简称*/
                private String status = " ";		/* 状态*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录创建人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String uuid = " ";		/* uuid*/
                private String tenantId = " ";		/* 租户ID*/
                private String allocateVehicleNo = " ";		/* 配车单号*/
                private String allocType = " ";		/* 配单类型*/
                private String carTraceNo = " ";		/* 车辆管理号*/
                private String nextAlcVehicleNo = " ";		/* 下级配单号*/
                private Integer showFlag = Integer.valueOf(0);		/* 展示标记*/
                private String estimatedTimeOfArrival = " ";		/* 预计到达时间*/
                private String allocMes = " ";		/* 配单结果*/
                private String customerId = " ";		/* 客户代码*/
                private String customerName = " ";		/* 客户名称*/
                private String proformaVoucherNum = " ";		/* 形式提单无实物提单号*/
                private String emergencyDeliveryTime = " ";		/* 紧急发货时间*/
                private String ladingBillRemark = " ";		/* 形式提单备注*/
                private String noPlanFlag = " ";		/* 无计划标记*/
                private String driverName = " ";		/* 无计划标记*/
                private String driverTel = " ";		/* 无计划标记*/
                private String proformaOrderNum = " ";		/* 销售订单子项号*/
                private String proformaSpecsDesc = " ";		/* 规格*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocateVehicleNo");
        eiColumn.setDescName("配车单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocType");
        eiColumn.setDescName("配单类型");
        eiMetadata.addMeta(eiColumn);



        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nextAlcVehicleNo");
        eiColumn.setDescName("下级配单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("showFlag");
        eiColumn.setDescName("展示标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("estimatedTimeOfArrival");
        eiColumn.setDescName("预计到达时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocMes");
        eiColumn.setDescName("配单结果");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("proformaVoucherNum");
        eiColumn.setDescName("形式提单无实物提单号");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("emergencyDeliveryTime");
        eiColumn.setDescName("紧急发货时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillRemark");
        eiColumn.setDescName("形式提单备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("noPlanFlag");
        eiColumn.setDescName("无计划标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverTel");
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("proformaOrderNum");
        eiColumn.setDescName("销售订单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("proformaSpecsDesc");
        eiColumn.setDescName("规格");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0502() {
initMetaData();
}

        /**
        * get the segNo - 业务单元代码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元代码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the status - 状态
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录创建人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录创建人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the uuid - uuid
        * @return the uuid
        */
        public String getUuid() {
        return this.uuid;
        }

        /**
        * set the uuid - uuid
        */
        public void setUuid(String uuid) {
        this.uuid = uuid;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }
        /**
        * get the allocateVehicleNo - 配车单号
        * @return the allocateVehicleNo
        */
        public String getAllocateVehicleNo() {
        return this.allocateVehicleNo;
        }

        /**
        * set the allocateVehicleNo - 配车单号
        */
        public void setAllocateVehicleNo(String allocateVehicleNo) {
        this.allocateVehicleNo = allocateVehicleNo;
        }
        /**
        * get the allocType - 配单类型
        * @return the allocType
        */
        public String getAllocType() {
        return this.allocType;
        }

        /**
        * set the allocType - 配单类型
        */
        public void setAllocType(String allocType) {
        this.allocType = allocType;
        }
        /**
        * get the carTraceNo - 车辆管理号
        * @return the carTraceNo
        */
        public String getCarTraceNo() {
        return this.carTraceNo;
        }

        /**
        * set the carTraceNo - 车辆管理号
        */
        public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
        }
        /**
        * get the showFlag - 展示标记
        * @return the showFlag
        */
        public Integer getShowFlag() {
        return this.showFlag;
        }

        /**
        * set the showFlag - 展示标记
        */
        public void setShowFlag(Integer showFlag) {
        this.showFlag = showFlag;
        }


        public String getNextAlcVehicleNo() {
            return nextAlcVehicleNo;
        }

        public void setNextAlcVehicleNo(String nextAlcVehicleNo) {
            this.nextAlcVehicleNo = nextAlcVehicleNo;
        }
        /**
         * get the estimatedTimeOfArrival - 预计到达时间
         * @return the estimatedTimeOfArrival
         */
        public String getEstimatedTimeOfArrival() {
            return this.estimatedTimeOfArrival;
        }

        /**
         * set the estimatedTimeOfArrival - 预计到达时间
         */
        public void setEstimatedTimeOfArrival(String estimatedTimeOfArrival) {
            this.estimatedTimeOfArrival = estimatedTimeOfArrival;
        }

        /**
         * get the allocMes - 配车信息
         * @return the allocMes
         */
        public String getAllocMes() {
            return this.allocMes;
        }
        /**
         * set the allocMes - 配车信息
         */
        public void setAllocMes(String allocMes) {
            this.allocMes = allocMes;
        }


    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getProformaVoucherNum() {
        return proformaVoucherNum;
    }

    public void setProformaVoucherNum(String proformaVoucherNum) {
        this.proformaVoucherNum = proformaVoucherNum;
    }

    public String getEmergencyDeliveryTime() {
        return emergencyDeliveryTime;
    }

    public void setEmergencyDeliveryTime(String emergencyDeliveryTime) {
        this.emergencyDeliveryTime = emergencyDeliveryTime;
    }

    public String getLadingBillRemark() {
        return ladingBillRemark;
    }

    public void setLadingBillRemark(String ladingBillRemark) {
        this.ladingBillRemark = ladingBillRemark;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }


    public String getNoPlanFlag() {
        return noPlanFlag;
    }

    public void setNoPlanFlag(String noPlanFlag) {
        this.noPlanFlag = noPlanFlag;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverTel() {
        return driverTel;
    }

    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel;
    }

    public String getProformaOrderNum() {
        return proformaOrderNum;
    }

    public void setProformaOrderNum(String proformaOrderNum) {
        this.proformaOrderNum = proformaOrderNum;
    }

    public String getProformaSpecsDesc() {
        return proformaSpecsDesc;
    }

    public void setProformaSpecsDesc(String proformaSpecsDesc) {
        this.proformaSpecsDesc = proformaSpecsDesc;
    }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setAllocateVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("allocateVehicleNo")), allocateVehicleNo));
                setAllocType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("allocType")), allocType));
                setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
                setNextAlcVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nextAlcVehicleNo")), nextAlcVehicleNo));
                setShowFlag(NumberUtils.toInteger(StringUtils.toString(map.get("showFlag")), showFlag));
                setEstimatedTimeOfArrival(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("estimatedTimeOfArrival")), estimatedTimeOfArrival));
                setAllocMes(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("allocMes")), allocMes));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setProformaVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("proformaVoucherNum")), proformaVoucherNum));
                setEmergencyDeliveryTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("emergencyDeliveryTime")), emergencyDeliveryTime));
                setLadingBillRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillRemark")), ladingBillRemark));
                setNoPlanFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("noPlanFlag")), noPlanFlag));
                setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
                setDriverTel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverTel")), driverTel));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("allocateVehicleNo",StringUtils.toString(allocateVehicleNo, eiMetadata.getMeta("allocateVehicleNo")));
                map.put("allocType",StringUtils.toString(allocType, eiMetadata.getMeta("allocType")));
                map.put("carTraceNo",StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
                map.put("nextAlcVehicleNo",StringUtils.toString(nextAlcVehicleNo, eiMetadata.getMeta("nextAlcVehicleNo")));
                map.put("showFlag",StringUtils.toString(showFlag, eiMetadata.getMeta("showFlag")));
                map.put("estimatedTimeOfArrival",StringUtils.toString(estimatedTimeOfArrival, eiMetadata.getMeta("estimatedTimeOfArrival")));
                map.put("allocMes",StringUtils.toString(allocMes, eiMetadata.getMeta("allocMes")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("proformaVoucherNum",StringUtils.toString(proformaVoucherNum, eiMetadata.getMeta("proformaVoucherNum")));
                map.put("emergencyDeliveryTime",StringUtils.toString(emergencyDeliveryTime, eiMetadata.getMeta("emergencyDeliveryTime")));
                map.put("ladingBillRemark",StringUtils.toString(ladingBillRemark, eiMetadata.getMeta("ladingBillRemark")));
                map.put("noPlanFlag",StringUtils.toString(noPlanFlag, eiMetadata.getMeta("noPlanFlag")));
                map.put("driverTel",StringUtils.toString(driverTel, eiMetadata.getMeta("driverTel")));
                map.put("driverName",StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
return map;

}
}