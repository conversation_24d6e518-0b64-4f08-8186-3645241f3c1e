package com.baosight.imom.common.constants;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title MessageCodeConstant.java
 * @package com.baosight.imom.common.constants
 * @Description 常量类(定义电文号 & 业务常量 & 返回信息)
 * @date 2022年03月21日 15:04
 */
public class MessageCodeConstant {

    /**
     * 统一错误信息常量
     * 以MSG_ERROR_ 为开头
     */
    public class errorMessage {

        public static final String MSG_ERROR_EMPTY_UNIT = "缺少业务单元代码和系统账套不能查询!";
        public static final String MSG_ERROR_ = "";
        public static final String MSG_ERROR_NEWLY_ADDED_STATUS = "只能对新增状态进行修改!";
        public static final String MSG_ERROR_NEWLY_ADDED_AND_ENABLE_STATUS = "只能对新增或者启用状态进行修改!";
        public static final String MSG_ERROR_CONFIRMATION_ADDED_STATUS = "只能对确认状态进行反确认!";
        public static final String MSG_ERROR_NOT_EXIST = "数据不存在";
        public static final String MSG_ERROR_ONLY_CHECK_STATUS = "只能操作已点检状态数据";
        public static final String MSG_ERROR_ONLY_NOT_CHECK_STATUS = "只能操作未点检状态数据";
        public static final String MSG_ERROR_ONLY_NOT_UPLOAD = "只能操作未上传数据";
        public static final String MSG_ERROR_UPLOAD = "上传失败";
        public static final String MSG_ERROR_ACTIVE_OR_START_STATUS = "只能对生效或启动状态进行操作!";
        public static final String MSG_ERROR_ONLY_START_STATUS = "只能对启动状态进行操作!";
        public static final String MSG_ERROR_ONLY_NEWLY_STATUS = "只能对新增状态进行操作!";
        public static final String MSG_ERROR_ONLY_SUBMIT_STATUS = "只能对已提交状态进行操作!";
        public static final String MSG_ERROR_ONLY_APPROVAL_STATUS = "只能对审批中状态进行操作!";
        public static final String MSG_ERROR_ONLY_APPROVED_STATUS = "只能对审批通过状态进行操作!";
        public static final String MSG_ERROR_ONLY_ACTIVE_STATUS = "只能对生效状态进行操作!";
        public static final String MSG_ERROR_ONLY_ENABLE_STATUS = "只能对启用状态进行操作!";
        public static final String MSG_ERROR_ONLY_DISABLE_STATUS = "只能对停用状态进行操作!";
        public static final String MSG_ERROR_ONLY_ARTIFICIAL = "只能对异常来源为操作新增或专业新增的数据进行操作!";
        public static final String MSG_ERROR_ONLY_CONFIRM_STATUS = "只能对确认状态进行操作!";
        public static final String MSG_ERROR_ONLY_COMPLETED_STATUS = "只能对完成状态进行操作!";
        public static final String MSG_ERROR_ONLY_UN_CONFIRM_STATUS = "只能对未确认状态进行操作!";
        public static final String MSG_ERROR_ONLY_FINISH_STATUS = "只能对完成状态进行操作!";
        public static final String MSG_ERROR_ACTUALS_EMPTY = "点检实绩内容为空";

        /**
         * 校验更新状态时已有数据已经被删除了，但是页面还未刷新的数据不一致信息
         */
        public static final String MSG_ERROR_NO_DATE_UPDATE = "需要修改的数据已经被删除，请刷新后再试！";
        public static final String MSG_ERROR_EXAMINE_ENABLE_STATUS = "只能对审核状态的数据进行启用!";
        public static final String MSG_ERROR_EXAMINE_DIS_ENABLE_STATUS = "只能对确认状态的数据进行反确认!";
        public static final String MSG_ERROR_EXAMINE_ENABLE_DISABLED_STATUS = "只能对审核状态或者停用的数据进行启用!";
        public static final String MSG_ERROR_EXAMINE_INSERT_DISABLED_STATUS = "只能对新增状态或者停用的数据进行启用!";
        public static final String MSG_ERROR_STATUS_CAN_BE_COUNTER_CONFIRMED = "只能对确认状态的数据进行反确认!";
        public static final String MSG_ERROR_DISABLED_STATUS = "只能对启用状态的数据进行进行停用!";
        public static final String MSG_ERROR_CONFIRM_STATUS_CONFRIM_NO = "只能对审核状态的数据进行进行取消审核!";
        public static final String MSG_ERROR_WHETHER_TO_USE = "只能对使用状态的数据进行禁用!";


        /**
         *
         * 校验装卸点
         */
        public static final String MSG_ERROR_HAND_POINT_NO_FACTORY = "被引用且还有装卸点还有未离场的数据不能停用！";



    }

    /**
     * 统一成功信息常量
     * 以 MSG_SUCCESS_ 为开头
     */
    public class successMessage {
        public static final String MSG_SUCCESS_UPLOAD = "上传成功";
        public static final String MSG_SUCCESS_ADD_RESUME = "加入设备履历成功";
        public static final String MSG_SUCCESS_SUBMIT = "提交成功";
        public static final String MSG_SUCCESS_CANCEL = "取消成功";
        public static final String MSG_SUCCESS_AUDIT = "审核成功";
        public static final String MSG_SUCCESS_CLEAR = "清空成功";
        public static final String MSG_SUCCESS_CONFIRM = "确认成功";
        public static final String MSG_SUCCESS_UN_CONFIRM = "反确认成功";
        public static final String MSG_SUCCESS_GENERATE = "生成成功";
        public static final String MSG_SUCCESS_DELAY = "延期成功";
        public static final String MSG_SUCCESS_OPERATE = "操作成功";
    }

}
