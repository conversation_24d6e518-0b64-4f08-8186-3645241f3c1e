<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0602">

    <sql id="queryCondition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="caseId">
            CASE_ID like concat('%',#caseId#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME like concat('%',#equipmentName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME = #deviceName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultType">
            FAULT_TYPE = #faultType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultLevel">
            FAULT_LEVEL = #faultLevel#
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="voucherNum">
            VOUCHER_NUM like concat('%',#voucherNum#,'%')
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0602">
        SELECT
        CASE_ID as "caseId",  <!-- 案例编号 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        FAULT_TYPE as "faultType",  <!-- 故障类型 -->
        FAULT_LEVEL as "faultLevel",  <!-- 故障级别 -->
        FAULT_DESC as "faultDesc",  <!-- 故障描述 -->
        HANDLE_MEASURES as "handleMeasures",  <!-- 处理措施 -->
        IS_OVERHAUL as "isOverhaul",  <!-- 是否检修 -->
        OVERHAUL_TYPE as "overhaulType",  <!-- 检修类别 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode",  <!-- 业务单元代码 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        VOUCHER_NUM as "voucherNum"  <!-- 依据凭单 -->
        FROM ${mevgSchema}.TVGDM0602 WHERE 1=1
        <include refid="queryCondition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME DESC
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0602 WHERE 1=1
        <include refid="queryCondition"/>
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0602 (CASE_ID,  <!-- 案例编号 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        FAULT_TYPE,  <!-- 故障类型 -->
        FAULT_LEVEL,  <!-- 故障级别 -->
        FAULT_DESC,  <!-- 故障描述 -->
        HANDLE_MEASURES,  <!-- 处理措施 -->
        IS_OVERHAUL,  <!-- 是否检修 -->
        OVERHAUL_TYPE,  <!-- 检修类别 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        VOUCHER_NUM  <!-- 依据凭单 -->
        )
        VALUES (#caseId#, #eArchivesNo#, #equipmentName#, #faultType#, #faultLevel#, #faultDesc#, #handleMeasures#,
        #isOverhaul#, #overhaulType#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #deviceCode#,
        #deviceName#, #voucherNum#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0602 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0602
        SET
        CASE_ID = #caseId#,   <!-- 案例编号 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        FAULT_TYPE = #faultType#,   <!-- 故障类型 -->
        FAULT_LEVEL = #faultLevel#,   <!-- 故障级别 -->
        FAULT_DESC = #faultDesc#,   <!-- 故障描述 -->
        HANDLE_MEASURES = #handleMeasures#,   <!-- 处理措施 -->
        IS_OVERHAUL = #isOverhaul#,   <!-- 是否检修 -->
        OVERHAUL_TYPE = #overhaulType#,   <!-- 检修类别 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,   <!-- 业务单元代码 -->
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        VOUCHER_NUM = #voucherNum#   <!-- 依据凭单 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>