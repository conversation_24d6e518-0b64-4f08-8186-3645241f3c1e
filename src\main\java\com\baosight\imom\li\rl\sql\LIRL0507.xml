<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-30 10:59:11
   		Version :  1.0
		tableName :meli.tlirl0507 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL   primarykey, 
		 STATUS  VARCHAR   NOT NULL, 
		 CUSTOMER_ID  VARCHAR   NOT NULL, 
		 CUSTOMER_NAME  VARCHAR   NOT NULL, 
		 DRIVER_TEL  VARCHAR   NOT NULL, 
		 DRIVER_NAME  VARCHAR   NOT NULL, 
		 DRIVER_IDENTITY  VARCHAR   NOT NULL, 
		 LADING_BILL_ID  VARCHAR   NOT NULL, 
		 TRANS_PLAN_ID  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0507">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0507">
		SELECT
				SEG_NO	as "segNo",  <!-- 业务单元代代码 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				UUID	as "uuid",  <!-- uuid -->
				STATUS	as "status",  <!-- 状态(撤销：00、新增：10、确认：20) -->
				CUSTOMER_ID	as "customerId",  <!-- 承运商/客户代码 -->
				CUSTOMER_NAME	as "customerName",  <!-- 承运商/客户名称 -->
				DRIVER_TEL	as "driverTel",  <!-- 司机手机号 -->
				DRIVER_NAME	as "driverName",  <!-- 司机姓名 -->
				DRIVER_IDENTITY	as "driverIdentity",  <!-- 司机身份证号 -->
				LADING_BILL_ID	as "ladingBillId",  <!-- 提单号 -->
				TRANS_PLAN_ID	as "transPlanId",  <!-- 物流运输计划号 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人姓名 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				TENANT_ID	as "tenantId" <!-- 租户ID -->
		FROM meli.tlirl0507 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>


	<select id="queryAll" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		max(UNIT_CODE) as "unitCode",  <!-- 业务单元代代码 -->
		max(TRANS_PLAN_ID) as "transPlanId",  <!-- 承运商/客户代码 -->
		max(LADING_BILL_ID) as "ladingBillId",  <!-- 承运商/客户名称 -->
		max(CUSTOMER_ID) as "providerId",  <!-- 司机手机号 -->
		max(CUSTOMER_NAME) as "providerName",  <!-- 司机姓名 -->
		max(DRIVER_NAME) as "driName",  <!-- 司机身份证号 -->
		max(DRIVER_TEL) as "driPhone",  <!-- 提单号 -->
		max(DRIVER_IDENTITY) as "driIdNumber",  <!-- 物流运输计划号 -->
		'10' as "traceStatus"
		FROM meli.tlirl0507 WHERE 1=1
		AND SEG_NO = #segNo#
		AND STATUS = '10'
		GROUP BY TRANS_PLAN_ID
		ORDER BY  max(REC_CREATE_TIME) DESC

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0507 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerId">
			CUSTOMER_ID = #customerId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customerName">
			CUSTOMER_NAME = #customerName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			DRIVER_TEL = #driverTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="ladingBillId">
			LADING_BILL_ID = #ladingBillId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="transPlanId">
			TRANS_PLAN_ID = #transPlanId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0507 (SEG_NO,  <!-- 业务单元代代码 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										UUID,  <!-- uuid -->
										STATUS,  <!-- 状态(撤销：00、新增：10、确认：20) -->
										CUSTOMER_ID,  <!-- 承运商/客户代码 -->
										CUSTOMER_NAME,  <!-- 承运商/客户名称 -->
										DRIVER_TEL,  <!-- 司机手机号 -->
										DRIVER_NAME,  <!-- 司机姓名 -->
										DRIVER_IDENTITY,  <!-- 司机身份证号 -->
										LADING_BILL_ID,  <!-- 提单号 -->
										TRANS_PLAN_ID,  <!-- 物流运输计划号 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人姓名 -->
										REC_REVISOR_NAME,  <!-- 记录修改人 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										TENANT_ID,  <!-- 租户ID -->
		ERR_MSG
										)		 
	    VALUES (#segNo#, #unitCode#, #uuid#, #status#, #customerId#, #customerName#, #driverTel#, #driverName#, #driverIdentity#, #ladingBillId#, #transPlanId#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #tenantId#,#errMsg#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0507 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0507 
		SET 
		SEG_NO	= #segNo#,   <!-- 业务单元代代码 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
								STATUS	= #status#,   <!-- 状态(撤销：00、新增：10、确认：20) -->  
					CUSTOMER_ID	= #customerId#,   <!-- 承运商/客户代码 -->  
					CUSTOMER_NAME	= #customerName#,   <!-- 承运商/客户名称 -->  
					DRIVER_TEL	= #driverTel#,   <!-- 司机手机号 -->  
					DRIVER_NAME	= #driverName#,   <!-- 司机姓名 -->  
					DRIVER_IDENTITY	= #driverIdentity#,   <!-- 司机身份证号 -->  
					LADING_BILL_ID	= #ladingBillId#,   <!-- 提单号 -->  
					TRANS_PLAN_ID	= #transPlanId#,   <!-- 物流运输计划号 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人姓名 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					TENANT_ID	= #tenantId#  <!-- 租户ID -->  
			WHERE 	
			UUID = #uuid#
	</update>

	<update id="updateStatus">
		UPDATE meli.tlirl0507
		SET
		STATUS = #status#
		WHERE 1=1
		and SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="transPlanIdList">
			TRANS_PLAN_ID IN
			<iterate open="(" close=")" conjunction="," property="transPlanIdList">
				#transPlanIdList[]#
			</iterate>
		</isNotEmpty>
	</update>
  
</sqlMap>