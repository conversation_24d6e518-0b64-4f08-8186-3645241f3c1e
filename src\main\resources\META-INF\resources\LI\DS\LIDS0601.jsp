<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">

    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-windowId" cname="windowId" colWidth="3" disabled="true" type="hidden"/>
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             required="true"
                             containerId="unitInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-segNo,inqu_status-0-segName"
                             popupTitle="业务套账查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" colWidth="3" value=" " disabled="true"
                        type="hidden"/>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"/>
            <EF:EFPopupInput ename="inqu_status-0-warehouseCode" cname="仓库代码" resizable="true" colWidth="3"
                             ratio="4:8" readonly="true"
                             containerId="warehouseInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                             center="true" backFillFieldIds="inqu_status-0-warehouseName"
                             popupTitle="仓库查询">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-warehouseName" cname="仓库名称" colWidth="3" disabled="true"/>
        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-locationId" cname="库位代码" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-locationName" cname="库位名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-posDirCode" cname="层数标记" colWidth="3"/>
            <EF:EFSelect ename="inqu_status-0-managementStyle" cname="管理方式" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P038"/>
            </EF:EFSelect>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-status" cname="状态" align="center" width="150" enable="true"
                         colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="启用" value="10"/>
                <EF:EFOption label="停用" value="20"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-ifMaintainPoint" cname="是否维护坐标" align="center" width="150"
                         enable="true" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="是" value="10"/>
                <EF:EFOption label="否" value="20"/>
            </EF:EFSelect>
            <EF:EFInput ename="inqu_status-0-factoryAreaName" cname="厂区名称" colWidth="3" placeholder="模糊条件"/>
            <EF:EFInput ename="inqu_status-0-crossAreaName" cname="跨区名称" colWidth="3" placeholder="模糊条件"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-ifPlanFlag" cname="是否参与库位推荐" align="center" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFCodeOption codeName="P037"/>
            </EF:EFSelect>
            <div id="inqu_crane" style="display: none">
                <EF:EFPopupInput ename="inqu_status-0-craneId" cname="行车编号" resizable="true" colWidth="3"
                                 ratio="4:8" readonly="true"
                                 containerId="craneInfo" popupWidth="600" pupupHeight="300" originalInput="true"
                                 center="true" backFillFieldIds="inqu_status-0-craneName"
                                 popupTitle="行车查询">
                </EF:EFPopupInput>
                <EF:EFInput ename="inqu_status-0-craneName" cname="行车名称" colWidth="3" disabled="true"/>
            </div>
        </div>
    </EF:EFRegion>
    <div id="result">
        <EF:EFRegion id="result" title="清单">
            <EF:EFGrid isFloat="true" id="result" blockId="result" autoBind="false" autoDraw="no" needAuth="true" sort="all">
                <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center" width="120" primaryKey="true"
                             required="true"
                             enable="false"/>
                <EF:EFColumn ename="segName" cname="业务单元简称" align="center" width="120" enable="false"/>
                <EF:EFColumn ename="segNo" cname="系统账套" align="center" width="100" primaryKey="true"
                             enable="false" hidden="true"/>
                <EF:EFColumn ename="locationId" cname="库位代码" align="center" width="150"
                             required="true"
                             primaryKey="true" enable="true"/>
                <EF:EFColumn ename="locationName" cname="库位名称" align="center" width="200" enable="true"
                             required="true"/>
                <EF:EFColumn ename="warehouseCode" cname="仓库代码" align="center" width="150" enable="true"
                             required="true"/>
                <EF:EFColumn ename="warehouseName" cname="仓库名称" align="center" width="200" enable="false"
                             required="true"/>
                <EF:EFComboColumn ename="ifPlanFlag" cname="是否参与库位推荐" align="center" width="150" enable="true"
                                  required="true">
                    <EF:EFCodeOption codeName="P037"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="status" cname="状态" align="center" width="150" enable="false">
                    <EF:EFOption label="启用" value="10"/>
                    <EF:EFOption label="停用" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="factoryArea" cname="厂区代码" align="center" width="100" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="factoryAreaName" cname="厂区名称" align="center" width="100" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="factoryBuilding" cname="厂房代码" align="center" width="150" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="factoryBuildingName" cname="厂房名称" align="center" width="150" enable="false"
                             readonly="true" required="true"/>
                <EF:EFColumn ename="crossArea" cname="跨区编码" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="crossAreaName" cname="跨区名称" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingPointNo" cname="装卸点编码" align="center" width="150" enable="true"/>
                <EF:EFColumn ename="loadingPointName" cname="装卸点名称" align="center" width="150" enable="false"/>
                <EF:EFComboColumn ename="endPoint" cname="终点标记" align="center" width="150" enable="true">
                    <EF:EFOption label="否" value="10"/>
                    <EF:EFOption label="是" value="20"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="loadingChannelNo" cname="装卸通道编码" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="loadingChannelName" cname="装卸通道名称" align="center" width="150" enable="false"/>
                <EF:EFColumn ename="xInitialPoint" cname="X轴起始点" align="center" width="150" enable="true"
                             defaultValue="0"
                             data-regex="/^[0-9]\\\d{0,11}(\\\.\\\d{1,2})?$|^0(\\\.\\\d{1,2})?$/"
                             data-errorprompt="请输入长度为12以内的正数,小数位最大为2位"
                             onkeyup="if(isNaN(value))execCommand('undo')"/>
                <EF:EFColumn ename="xDestination" cname="X轴终到点" align="center" width="150" enable="true"
                             defaultValue="0"
                             data-regex="/^[0-9]\\\d{0,11}(\\\.\\\d{1,2})?$|^0(\\\.\\\d{1,2})?$/"
                             data-errorprompt="请输入长度为12以内的正数,小数位最大为2位"
                             onkeyup="if(isNaN(value))execCommand('undo')"/>
                <EF:EFColumn ename="yInitialPoint" cname="Y轴起始点" align="center" width="150" enable="true"
                             defaultValue="0"
                             data-regex="/^[0-9]\\\d{0,11}(\\\.\\\d{1,2})?$|^0(\\\.\\\d{1,2})?$/"
                             data-errorprompt="请输入长度为12以内的正数,小数位最大为2位"
                             onkeyup="if(isNaN(value))execCommand('undo')"/>
                <EF:EFColumn ename="yDestination" cname="Y轴终到点" align="center" width="150" enable="true"
                             defaultValue="0"
                             data-regex="/^[0-9]\\\d{0,11}(\\\.\\\d{1,2})?$|^0(\\\.\\\d{1,2})?$/"
                             data-errorprompt="请输入长度为12以内的正数,小数位最大为2位"
                             onkeyup="if(isNaN(value))execCommand('undo')"/>
                <EF:EFColumn ename="posDirCode" cname="层数标记" align="center" width="150" enable="true"/>
                <EF:EFComboColumn ename="managementStyle" cname="管理方式" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P038"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="actionFlag" cname="板卷标记" align="center" width="150" enable="true">
                    <EF:EFOption label="" value=""/>
                    <EF:EFCodeOption codeName="P039"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="c_no" cname="所在跨区序列号" align="center" width="150"/>
                <EF:EFComboColumn ename="standFlag" cname="是否立式库位" align="center" width="150" enable="false">
                    <EF:EFOption label="卧式" value="0"/>
                    <EF:EFOption label="立式" value="1"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="pointUpperLength" cname="点状库位长度上限(cm)" align="right" width="150"/>
                <EF:EFColumn ename="pointLowerLength" cname="点状库位长度下限(cm)" align="right" width="150"/>
                <EF:EFColumn ename="specUpper" cname="宽度上限(cm)" align="right" width="150"/>
                <EF:EFColumn ename="specLower" cname="宽度下限(cm)" align="right" width="150"/>
                <EF:EFColumn ename="recCreator" cname="记录创建人" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="recCreatorName" cname="记录创建人姓名" align="center" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recCreateTime" cname="记录创建时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="recRevisor" cname="记录修改人" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="recRevisorName" cname="记录修改人姓名" align="center" width="120"
                             enable="false"/>
                <EF:EFColumn ename="recReviseTime" cname="记录修改时间" align="center" width="180" enable="false"/>
                <EF:EFColumn ename="delFlag" cname="记录删除标记" align="center" width="100" primaryKey="true"
                             enable="true" hidden="true"/>
                <EF:EFColumn ename="uuid" cname="UUID" align="center" width="120" enable="true" hidden="true"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>


    <%--业务单元代码弹窗--%>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <%--跨区编码弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS04" id="crossAreaInfo" width="90%" height="60%"/>
    <%--厂内装卸点弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIRL0003" id="handPointInfo" width="90%" height="60%"/>
    <%--仓库代码弹窗    --%>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseInfo" width="90%" height="60%"/>
    <%--仓库代码弹窗(表格用)    --%>
    <EF:EFWindow url="${ctx}/web/LIDS01" id="warehouseInfoGrid" width="90%" height="60%"/>
    <%--厂区厂房信息弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS02" id="factoryAreaInfo" width="90%" height="60%"/>
    <%--行车(设备档案)弹窗--%>
    <EF:EFWindow url="${ctx}/web/LIDS07" id="craneInfo" width="90%" height="60%"/>
</EF:EFPage>
