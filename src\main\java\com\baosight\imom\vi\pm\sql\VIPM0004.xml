<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VIPM0004">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="dataType">
            DATA_TYPE = #dataType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE = #machineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineName">
            MACHINE_NAME like concat('%', #machineName#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="partIdStr">
            PART_ID like concat('%', #partIdStr#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="packIdStr">
            PACK_ID like concat('%', #packIdStr#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processOrderIdStr">
            PROCESS_ORDER_ID like concat('%', #processOrderIdStr#, '%')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeStart">
            substr(REC_CREATE_TIME,1,8) &gt;= replace(#recCreateTimeStart#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="recCreateTimeEnd">
            replace(#recCreateTimeEnd#,'-','') &gt;= substr(REC_CREATE_TIME,1,8)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="reportFlag">
            OFFLINE_TIME > 0
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vi.pm.domain.VIPM0004">
        SELECT
        MACHINE_OFFLINE_SEQ_ID as "machineOfflineSeqId",  <!-- 机组离线时间序列号 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        THICK_MIN as "thickMin",  <!-- 最小厚度 -->
        THICK_MAX as "thickMax",  <!-- 最大厚度 -->
        OFFLINE_EVENT as "offlineEvent",  <!-- 离线项目 -->
        OFFLINE_TIME as "offlineTime",  <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS as "machineOfflineStatus",  <!-- 机组离线时状态 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_ORDER_ID as "processOrderId",  <!-- 生产工单号 -->
        PACK_ID as "packId",  <!-- 原料捆包号 -->
        IN_PART_ID as "inPartId",  <!-- 原料物料号 -->
        IN_SPECS_DESC as "inSpecsDesc",  <!-- 原料规格 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        REMARK as "remark",  <!-- 备注 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        DATA_TYPE as "dataType" <!-- 数据类型 -->
        FROM ${meviSchema}.TVIPM0004 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="queryForB1" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDMB103">
        SELECT
        MACHINE_OFFLINE_SEQ_ID as "machineOfflineSeqId",  <!-- 机组离线时间序列号 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        THICK_MIN as "thickMin",  <!-- 最小厚度 -->
        THICK_MAX as "thickMax",  <!-- 最大厚度 -->
        OFFLINE_EVENT as "offlineEvent",  <!-- 离线项目 -->
        OFFLINE_TIME as "offlineTime",  <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS as "machineOfflineStatus",  <!-- 机组离线时状态 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_ORDER_ID as "processOrderId",  <!-- 生产工单号 -->
        PACK_ID as "packId",  <!-- 原料捆包号 -->
        IN_PART_ID as "inPartId",  <!-- 原料物料号 -->
        IN_SPECS_DESC as "inSpecsDesc",  <!-- 原料规格 -->
        NET_WEIGHT as "netWeight",  <!-- 净重 -->
        REMARK as "remark",  <!-- 备注 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        DATA_TYPE as "dataType" <!-- 数据类型 -->
        FROM ${meviSchema}.TVIPM0004 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${meviSchema}.TVIPM0004 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="queryForAvg" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MAX(MACHINE_NAME) as "machineName",  <!-- 机组名称 -->
        OFFLINE_EVENT as "offlineEvent",  <!-- 离线项目 -->
        SUM(OFFLINE_TIME) as "offlineTime",  <!-- 离线时间(单位分钟) -->
        MAX(E_ARCHIVES_NO) as "eArchivesNo",  <!-- 设备档案编号 -->
        MAX(EQUIPMENT_NAME) as "equipmentName",  <!-- 设备名称 -->
        MAX(PROCESS_CATEGORY) as "processCategory",  <!-- 工序大类代码 -->
        COUNT(UUID) as "rowCount"
        FROM ${meviSchema}.TVIPM0004
        WHERE SEG_NO = #segNo#
        AND MACHINE_OFFLINE_STATUS = '10'
        AND DEL_FLAG = '0'
        AND DATA_TYPE = '10'
        AND OFFLINE_TIME > 0
        AND REC_CREATE_TIME &gt;= #startTime#
        group by MACHINE_CODE,OFFLINE_EVENT
    </select>

    <insert id="insert">
        INSERT INTO ${meviSchema}.TVIPM0004 (MACHINE_OFFLINE_SEQ_ID,  <!-- 机组离线时间序列号 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        THICK_MIN,  <!-- 最小厚度 -->
        THICK_MAX,  <!-- 最大厚度 -->
        OFFLINE_EVENT,  <!-- 离线项目 -->
        OFFLINE_TIME,  <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS,  <!-- 机组离线时状态 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        PROCESS_ORDER_ID,  <!-- 生产工单号 -->
        PACK_ID,  <!-- 原料捆包号 -->
        IN_PART_ID,  <!-- 原料物料号 -->
        IN_SPECS_DESC,  <!-- 原料规格 -->
        NET_WEIGHT,  <!-- 净重 -->
        REMARK,  <!-- 备注 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        DATA_TYPE  <!-- 数据类型 -->
        )
        VALUES (#machineOfflineSeqId#, #machineCode#, #machineName#, #thickMin#, #thickMax#, #offlineEvent#,
        #offlineTime#, #machineOfflineStatus#, #eArchivesNo#, #equipmentName#, #processCategory#, #processOrderId#, #packId#, #inPartId#, #inSpecsDesc#, #netWeight#, #remark#, #uuid#,
        #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#,
        #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #dataType#)
    </insert>

    <delete id="delete">
        DELETE FROM ${meviSchema}.TVIPM0004 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${meviSchema}.TVIPM0004
        SET
        MACHINE_OFFLINE_SEQ_ID = #machineOfflineSeqId#,   <!-- 机组离线时间序列号 -->
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        MACHINE_NAME = #machineName#,   <!-- 机组名称 -->
        THICK_MIN = #thickMin#,   <!-- 最小厚度 -->
        THICK_MAX = #thickMax#,   <!-- 最大厚度 -->
        OFFLINE_EVENT = #offlineEvent#,   <!-- 离线项目 -->
        OFFLINE_TIME = #offlineTime#,   <!-- 离线时间(单位分钟) -->
        MACHINE_OFFLINE_STATUS = #machineOfflineStatus#,   <!-- 机组离线时状态 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCESS_CATEGORY = #processCategory#,   <!-- 工序大类代码 -->
        PROCESS_ORDER_ID = #processOrderId#,   <!-- 生产工单号 -->
        PACK_ID = #packId#,   <!-- 原料捆包号 -->
        IN_PART_ID = #inPartId#,   <!-- 原料物料号 -->
        IN_SPECS_DESC = #inSpecsDesc#,   <!-- 原料规格 -->
        NET_WEIGHT = #netWeight#,   <!-- 净重 -->
        REMARK = #remark#,   <!-- 备注 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        DATA_TYPE = #dataType#   <!-- 数据类型 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updForDel">
        UPDATE ${meviSchema}.TVIPM0004
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        DEL_FLAG = #delFlag#   <!-- 删除标记 -->
        WHERE
        MACHINE_OFFLINE_SEQ_ID = #machineOfflineSeqId#
        AND SEG_NO = #segNo#
        AND DEL_FLAG = '0'
    </update>

</sqlMap>