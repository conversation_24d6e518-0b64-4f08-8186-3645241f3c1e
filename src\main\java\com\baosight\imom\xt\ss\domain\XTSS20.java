/**
* Generate time : 2025-07-07 22:39:41
* Version : 1.0
*/
package com.baosight.imom.xt.ss.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Txspdamenu
* 
*/
public class XTSS20 extends DaoEPBase {

    private String segNo = " ";		/* 系统账套*/
    private String unitCode = " ";		/* 业务单元代码*/
    private String segName = " ";		
    private String uuid = " ";		
    private String recCreator = " ";		
    private String recCreatorName = " ";		
    private String recRevisor = " ";		
    private String recRevisorName = " ";		
    private String recReviseTime = " ";		
    private Integer delFlag = Integer.valueOf(0);		
    private String tenantUser = " ";
    private String userPermission = " "; // 用户权限
    private String groupEname = " "; // 用户组英文名
    private String userGroupId = " "; // 用户组id
    private String groupCname = " "; // 用户组英文名
    private String recCreateTime = " ";

    /**
    * initialize the metadata
    */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiColumn.setFieldLength(30);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiColumn.setFieldLength(32);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userPermission");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("PDA菜单权限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("groupEname");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("群组英文名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userGroupId");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("群组ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("groupCname");
        eiColumn.setFieldLength(128);
        eiColumn.setDescName("群组中文名");
        eiMetadata.addMeta(eiColumn);

    }

    /**
    * the constructor
    */
    public XTSS20() {
        initMetaData();
    }

    public String getUserGroupId() {
        return this.userGroupId;
    }
    public void setUserGroupId(String userGroupId) {
        this.userGroupId = userGroupId;
    }

    public String getGroupCname() {
        return this.groupCname;
    }
    public void setGroupCname(String groupCname) {
        this.groupCname = groupCname;
    }

    public String getRecCreateTime() {
        return this.recCreateTime;
    }
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
    * get the segNo - 系统账套
    * @return the segNo
    */
    public String getSegNo() {
        return this.segNo;
    }

    /**
    * set the segNo - 系统账套
    */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }
    /**
    * get the unitCode - 业务单元代码
    * @return the unitCode
    */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
    * set the unitCode - 业务单元代码
    */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
    /**
    * get the segName 
    * @return the segName
    */
    public String getSegName() {
        return this.segName;
    }

    /**
    * set the segName 
    */
    public void setSegName(String segName) {
        this.segName = segName;
    }
    /**
    * get the uuid 
    * @return the uuid
    */
    public String getUuid() {
        return this.uuid;
    }

    /**
    * set the uuid 
    */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    /**
    * get the recCreator 
    * @return the recCreator
    */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
    * set the recCreator 
    */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }
    /**
    * get the recCreatorName 
    * @return the recCreatorName
    */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
    * set the recCreatorName 
    */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }
    /**
    * get the recRevisor 
    * @return the recRevisor
    */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
    * set the recRevisor 
    */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }
    /**
    * get the recRevisorName 
    * @return the recRevisorName
    */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
    * set the recRevisorName 
    */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }
    /**
    * get the recReviseTime 
    * @return the recReviseTime
    */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
    * set the recReviseTime 
    */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }
    /**
    * get the delFlag 
    * @return the delFlag
    */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
    * set the delFlag 
    */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
    /**
    * get the tenantUser 
    * @return the tenantUser
    */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
    * set the tenantUser 
    */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
    * get the menuName - 菜单名称
    * @return the menuName
    */
    public String getUserPermission() {
        return this.userPermission;
    }

    /**
    * set the menuName - 菜单名称
    */
    public void setUserPermission(String userPermission) {
        this.userPermission = userPermission;
    }
    /**
    * get the menuId - 菜单ID
    * @return the menuId
    */
    public String getGroupEname() {
        return this.groupEname;
    }

    /**
    * set the menuId - 菜单ID
    */
    public void setGroupEname(String groupEname) {
        this.groupEname = groupEname;
    }

    /**
    * get the value from Map
    */
    public void fromMap(Map map) {
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setUserPermission(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userPermission")), userPermission));
        setGroupEname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("groupEname")), groupEname));
        setUserGroupId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userGroupId")), userGroupId));
        setGroupCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("groupCname")), groupCname));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
    }

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("userPermission",StringUtils.toString(userPermission, eiMetadata.getMeta("userPermission")));
        map.put("groupEname",StringUtils.toString(groupEname, eiMetadata.getMeta("groupEname")));
        map.put("userGroupId",StringUtils.toString(userGroupId, eiMetadata.getMeta("userGroupId")));
        map.put("groupCname",StringUtils.toString(groupCname, eiMetadata.getMeta("groupCname")));
        map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        return map;
    }
}