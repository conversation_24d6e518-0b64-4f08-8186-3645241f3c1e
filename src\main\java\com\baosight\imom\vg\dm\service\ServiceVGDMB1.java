package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.EasyExcelUtil;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.vg.dm.domain.VGDM0402;
import com.baosight.imom.vi.pm.domain.VIPM0004;
import com.baosight.imom.vi.pm.domain.VIPM0005;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections.CollectionUtils;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备相关报表后台
 *
 * <AUTHOR> 郁在杰
 * @Description :设备相关报表后台
 * @Date : 2025/7/17
 * @Version : 1.0
 */
public class ServiceVGDMB1 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDMB1.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 生成报表
     */
    public EiInfo createReport(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            Map params = inInfo.getRow(EiConstant.queryBlock, 0);
            String segNo = MapUtils.getStr(params, "segNo");
            String startDate = MapUtils.getStr(params, "startDate");
            String endDate = MapUtils.getStr(params, "endDate");
            String dataType = MapUtils.getStr(params, "dataType");
            if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate) || StrUtil.isBlank(dataType)) {
                throw new PlatException("报表参数不能为空");
            }
            log("生成报表参数：" + segNo + "|" + startDate + "|" + endDate + "|" + dataType);
            switch (dataType) {
                case "10":
                case "20":
                    // 点检完成率
                    this.spotCheckReport(inInfo, segNo, startDate, endDate, dataType);
                    break;
//                case "30":
//                    // 故障率
//                    break;
                case "40":
                    // 标准工时
                    this.standardHourReport(inInfo, segNo, startDate, endDate);
                    break;
                case "50":
                    // 辅助作业时间
                    this.offlineReport(inInfo, segNo, startDate, endDate);
                    break;
                default:
                    throw new PlatException("未知的报表类型：" + dataType);
            }
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 生成报表
     *
     * @param inInfo   inInfo
     * @param fileName 文件名
     * @param columns  列信息
     * @param dataList 数据信息
     */
    private void generateReport(EiInfo inInfo,
                                String fileName,
                                List<Map<String, Object>> columns,
                                List<Map<String, Object>> dataList) {
        // 列名信息
        EiBlock exportColumnBlock = inInfo.addBlock("exportColumnBlock");
        exportColumnBlock.set("fileName", fileName);
        exportColumnBlock.set("columns", columns);
        // 登录人信息
        Map<String, Object> loginMap = new HashMap<>();
        loginMap.put("userId", UserSession.getUserId());
        loginMap.put("userName", UserSession.getLoginCName());
        loginMap.put("loginName", UserSession.getLoginName());
        // 导出
        Map resultMap = EasyExcelUtil.export2FileStorageForClent(inInfo, dataList, loginMap);
        inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
    }

    /**
     * 点检完成率报表
     *
     * @param inInfo    inInfo
     * @param segNo     账套
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param dataType  类型
     */
    private void spotCheckReport(EiInfo inInfo, String segNo, String startDate, String endDate, String dataType) {
        log("点检完成率报表" + dataType);
        Map<String, String> map = new HashMap<>();
        map.put("segNo", segNo);
        map.put("checkStartDate", startDate);
        map.put("checkEndDate", endDate);
        String sqlId = VGDM0402.QUERY_DO_RATE;
        String fileName = segNo + "点检完成率(明细).xlsx";
        if ("20".equals(dataType)) {
            sqlId = VGDM0402.QUERY_DO_RATE2;
            fileName = segNo + "点检完成率(汇总).xlsx";
        }
        List<Map<String, Object>> list = dao.query(sqlId, map);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException("无可用报表数据");
        }
        // 生成报表
        this.generateReport(inInfo, fileName, spotCheckColumns(), list);
    }

    /**
     * 标准工时报表
     *
     * @param inInfo    inInfo
     * @param segNo     账套
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    private void standardHourReport(EiInfo inInfo, String segNo, String startDate, String endDate) {
        log("标准工时报表");
        Map<String, String> map = new HashMap<>();
        map.put("segNo", segNo);
        map.put("recCreateTimeStart", startDate);
        map.put("recCreateTimeEnd", endDate);
        map.put("dataType", "10");
        map.put("reportFlag", "1");
        map.put("orderBy", "MACHINE_CODE,PROCESS_SPEED desc,SPH desc");
        List<VIPM0005> list = dao.query(VIPM0005.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException("无可用报表数据");
        }
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (VIPM0005 vipm0005 : list) {
            Map tempMap = vipm0005.toMap();
            tempMap.put("processSpeed", vipm0005.getProcessSpeed());
            tempMap.put("sph", vipm0005.getSph());
            dataList.add(tempMap);
        }
        // 生成报表
        String fileName = segNo + "标准工时.xlsx";
        this.generateReport(inInfo, fileName, standardHourColumns(), dataList);
    }

    /**
     * 辅助时间报表
     *
     * @param inInfo    inInfo
     * @param segNo     账套
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    private void offlineReport(EiInfo inInfo, String segNo, String startDate, String endDate) {
        log("辅助时间报表");
        Map<String, String> map = new HashMap<>();
        map.put("segNo", segNo);
        map.put("recCreateTimeStart", startDate);
        map.put("recCreateTimeEnd", endDate);
        map.put("dataType", "10");
        map.put("reportFlag", "1");
        map.put("orderBy", "MACHINE_CODE,PROCESS_ORDER_ID,PACK_ID");
        List<VIPM0004> list = dao.query(VIPM0004.QUERY, map);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException("无可用报表数据");
        }
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (VIPM0004 vipm0004 : list) {
            Map tempMap = vipm0004.toMap();
            tempMap.put("offlineTime", vipm0004.getOfflineTime());
            dataList.add(tempMap);
        }
        // 生成报表
        String fileName = segNo + "辅助作业时间.xlsx";
        this.generateReport(inInfo, fileName, offlineColumns(), dataList);
    }

    /**
     * 点检完成率列信息
     *
     * @return 列信息
     */
    private List<Map<String, Object>> spotCheckColumns() {
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(new HashMap<String, Object>() {{
            put("idx", 1);
            put("title", "点检性质");
            put("column", "spotCheckNatureName");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 2);
            put("title", "设备档案编号");
            put("column", "eArchivesNo");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 3);
            put("title", "设备名称");
            put("column", "equipmentName");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 4);
            put("title", "点检日期");
            put("column", "checkPlanDate");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 5);
            put("title", "总数量");
            put("column", "totalNum");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 6);
            put("title", "未完成数量");
            put("column", "notNum");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 7);
            put("title", "已完成数量");
            put("column", "doNum");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 8);
            put("title", "异常数量");
            put("column", "eNum");
        }});
        return list;
    }

    /**
     * 标准工时列信息
     *
     * @return 列信息
     */
    private List<Map<String, Object>> standardHourColumns() {
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(new HashMap<String, Object>() {{
            put("idx", 1);
            put("title", "机组代码");
            put("column", "machineCode");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 2);
            put("title", "机组名称");
            put("column", "machineName");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 3);
            put("title", "分条数");
            put("column", "stripCount");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 4);
            put("title", "物料号");
            put("column", "partId");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 5);
            put("title", "规格");
            put("column", "specsDesc");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 6);
            put("title", "品种");
            put("column", "prodTypeId");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 7);
            put("title", "品种名称");
            put("column", "prodTypeName");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 8);
            put("title", "加工速度(米/分钟)");
            put("column", "processSpeed");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 9);
            put("title", "sph值(片/h)");
            put("column", "sph");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 10);
            put("title", "工单");
            put("column", "processOrderId");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 11);
            put("title", "原料捆包号");
            put("column", "packId");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 12);
            put("title", "原料规格");
            put("column", "inSpecsDesc");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 13);
            put("title", "原料物料号");
            put("column", "inPartId");
        }});
        return list;
    }

    /**
     * 辅助作业时间列信息
     *
     * @return 列信息
     */
    private List<Map<String, Object>> offlineColumns() {
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(new HashMap<String, Object>() {{
            put("idx", 1);
            put("title", "机组代码");
            put("column", "machineCode");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 2);
            put("title", "机组名称");
            put("column", "machineName");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 3);
            put("title", "作业项");
            put("column", "offlineEvent");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 4);
            put("title", "作业时间");
            put("column", "offlineTime");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 5);
            put("title", "工单");
            put("column", "processOrderId");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 6);
            put("title", "原料捆包号");
            put("column", "packId");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 7);
            put("title", "原料规格");
            put("column", "inSpecsDesc");
        }});
        list.add(new HashMap<String, Object>() {{
            put("idx", 8);
            put("title", "原料物料号");
            put("column", "inPartId");
        }});
        return list;
    }
}
