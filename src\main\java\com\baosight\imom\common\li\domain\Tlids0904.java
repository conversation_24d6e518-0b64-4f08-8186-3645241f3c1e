/**
* Generate time : 2025-07-11 09:17:25
* Version : 1.0
*/
package com.baosight.imom.common.li.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlids0904
* 
*/
public class Tlids0904 extends DaoEPBase {

    private String segNo = " ";		/* 系统账套*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = " ";		/* 业务单元代代码*/
    private String packId = " ";		/* 捆包号*/
    private BigDecimal netWeight = new BigDecimal(0.00000000);		/* 净重*/
    private BigDecimal grossWeight = new BigDecimal(0.00000000);		/* 毛重*/
    private BigDecimal quantity = new BigDecimal(0);		/* 件数*/
    private String dataSource = " ";		/* 数据来源*/
    private String craneResultId = " ";		/* 行车实绩单号*/
    private String warehouseCode = " ";		/* 仓库代码*/
    private String warehouseName = " ";		/* 仓库名称*/
    private String locationId = " ";		/* 库位代码*/
    private String locationName = " ";		/* 库位名称*/
    private String putoutLocationId = " ";		/* 转出库位代码*/
    private String putoutLocationName = " ";		/* 转出库位名称*/
    private String recCreator = " ";		/* 记录创建人*/
    private String recCreatorName = " ";		/* 记录创建人姓名*/
    private String recCreateTime = " ";		/* 记录创建时间*/
    private String recRevisor = " ";		/* 记录修改人*/
    private String recRevisorName = " ";		/* 记录修改人姓名*/
    private String recReviseTime = " ";		/* 记录修改时间*/
    private String archiveFlag = " ";		/* 归档标记*/
    private String tenantUser = " ";		/* 租户*/
    private Integer delFlag = Integer.valueOf(0);		/* 删除标记*/
    private String remaek = " ";		/* 备注*/
    private String uuid = " ";		/* ID*/
    private String labelId = " ";		/* 标签号*/

    /**
    * initialize the metadata
    */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grossWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("毛重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("件数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataSource");
        eiColumn.setDescName("数据来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneResultId");
        eiColumn.setDescName("行车实绩单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putoutLocationId");
        eiColumn.setDescName("转出库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putoutLocationName");
        eiColumn.setDescName("转出库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remaek");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setDescName("标签号");
        eiMetadata.addMeta(eiColumn);


    }

    /**
    * the constructor
    */
    public Tlids0904() {
        initMetaData();
    }

    /**
    * get the segNo - 系统账套
    * @return the segNo
    */
    public String getSegNo() {
        return this.segNo;
    }

    /**
    * set the segNo - 系统账套
    */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }
    /**
    * get the unitCode - 业务单元代代码
    * @return the unitCode
    */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
    * set the unitCode - 业务单元代代码
    */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
    /**
    * get the packId - 捆包号
    * @return the packId
    */
    public String getPackId() {
        return this.packId;
    }

    /**
    * set the packId - 捆包号
    */
    public void setPackId(String packId) {
        this.packId = packId;
    }
    /**
    * get the netWeight - 净重
    * @return the netWeight
    */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
    * set the netWeight - 净重
    */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }
    /**
    * get the grossWeight - 毛重
    * @return the grossWeight
    */
    public BigDecimal getGrossWeight() {
        return this.grossWeight;
    }

    /**
    * set the grossWeight - 毛重
    */
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }
    /**
    * get the quantity - 件数
    * @return the quantity
    */
    public BigDecimal getQuantity() {
        return this.quantity;
    }

    /**
    * set the quantity - 件数
    */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    /**
    * get the dataSource - 数据来源
    * @return the dataSource
    */
    public String getDataSource() {
        return this.dataSource;
    }

    /**
    * set the dataSource - 数据来源
    */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
    /**
    * get the craneResultId - 行车实绩单号
    * @return the craneResultId
    */
    public String getCraneResultId() {
        return this.craneResultId;
    }

    /**
    * set the craneResultId - 行车实绩单号
    */
    public void setCraneResultId(String craneResultId) {
        this.craneResultId = craneResultId;
    }
    /**
    * get the warehouseCode - 仓库代码
    * @return the warehouseCode
    */
    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    /**
    * set the warehouseCode - 仓库代码
    */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }
    /**
    * get the warehouseName - 仓库名称
    * @return the warehouseName
    */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
    * set the warehouseName - 仓库名称
    */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
    /**
    * get the locationId - 库位代码
    * @return the locationId
    */
    public String getLocationId() {
        return this.locationId;
    }

    /**
    * set the locationId - 库位代码
    */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }
    /**
    * get the locationName - 库位名称
    * @return the locationName
    */
    public String getLocationName() {
        return this.locationName;
    }

    /**
    * set the locationName - 库位名称
    */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
    /**
    * get the putoutLocationId - 转出库位代码
    * @return the putoutLocationId
    */
    public String getPutoutLocationId() {
        return this.putoutLocationId;
    }

    /**
    * set the putoutLocationId - 转出库位代码
    */
    public void setPutoutLocationId(String putoutLocationId) {
        this.putoutLocationId = putoutLocationId;
    }
    /**
    * get the putoutLocationName - 转出库位名称
    * @return the putoutLocationName
    */
    public String getPutoutLocationName() {
        return this.putoutLocationName;
    }

    /**
    * set the putoutLocationName - 转出库位名称
    */
    public void setPutoutLocationName(String putoutLocationName) {
        this.putoutLocationName = putoutLocationName;
    }
    /**
    * get the recCreator - 记录创建人
    * @return the recCreator
    */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
    * set the recCreator - 记录创建人
    */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }
    /**
    * get the recCreatorName - 记录创建人姓名
    * @return the recCreatorName
    */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
    * set the recCreatorName - 记录创建人姓名
    */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }
    /**
    * get the recCreateTime - 记录创建时间
    * @return the recCreateTime
    */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
    * set the recCreateTime - 记录创建时间
    */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }
    /**
    * get the recRevisor - 记录修改人
    * @return the recRevisor
    */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
    * set the recRevisor - 记录修改人
    */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }
    /**
    * get the recRevisorName - 记录修改人姓名
    * @return the recRevisorName
    */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
    * set the recRevisorName - 记录修改人姓名
    */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }
    /**
    * get the recReviseTime - 记录修改时间
    * @return the recReviseTime
    */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
    * set the recReviseTime - 记录修改时间
    */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }
    /**
    * get the archiveFlag - 归档标记
    * @return the archiveFlag
    */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
    * set the archiveFlag - 归档标记
    */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }
    /**
    * get the tenantUser - 租户
    * @return the tenantUser
    */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
    * set the tenantUser - 租户
    */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }
    /**
    * get the delFlag - 删除标记
    * @return the delFlag
    */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
    * set the delFlag - 删除标记
    */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
    /**
    * get the remaek - 备注
    * @return the remaek
    */
    public String getRemaek() {
        return this.remaek;
    }

    /**
    * set the remaek - 备注
    */
    public void setRemaek(String remaek) {
        this.remaek = remaek;
    }
    /**
    * get the uuid - ID
    * @return the uuid
    */
    public String getUuid() {
        return this.uuid;
    }

    /**
    * set the uuid - ID
    */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    /**
    * get the labelId - 标签号
    * @return the labelId
    */
    public String getLabelId() {
        return this.labelId;
    }

    /**
    * set the labelId - 标签号
    */
    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
    * get the value from Map
    */
    public void fromMap(Map map) {
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
        setGrossWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("grossWeight")), grossWeight));
        setQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("quantity")), quantity));
        setDataSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataSource")), dataSource));
        setCraneResultId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneResultId")), craneResultId));
        setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
        setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setPutoutLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putoutLocationId")), putoutLocationId));
        setPutoutLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putoutLocationName")), putoutLocationName));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemaek(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remaek")), remaek));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));
    }

    /**
    * set the value to Map
    */
    public Map toMap() {
        Map map = new HashMap();
        map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("packId",StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("netWeight",StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
        map.put("grossWeight",StringUtils.toString(grossWeight, eiMetadata.getMeta("grossWeight")));
        map.put("quantity",StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
        map.put("dataSource",StringUtils.toString(dataSource, eiMetadata.getMeta("dataSource")));
        map.put("craneResultId",StringUtils.toString(craneResultId, eiMetadata.getMeta("craneResultId")));
        map.put("warehouseCode",StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
        map.put("warehouseName",StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
        map.put("locationId",StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName",StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("putoutLocationId",StringUtils.toString(putoutLocationId, eiMetadata.getMeta("putoutLocationId")));
        map.put("putoutLocationName",StringUtils.toString(putoutLocationName, eiMetadata.getMeta("putoutLocationName")));
        map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remaek",StringUtils.toString(remaek, eiMetadata.getMeta("remaek")));
        map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("labelId",StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));
        return map;
    }
}