<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-08 21:40:27
   		Version :  1.0
		tableName :meli.tlirl0411 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CAR_TRACE_NO  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 TARGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 <PERSON><PERSON><PERSON>K  VARCHAR, 
		 SYS_REMARK  VARCHAR, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL, 
		 DRIVER_NAME  VARCHAR   NOT NULL, 
		 DRIVER_TEL  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0411">

	<sql id="condition">
    <isNotEmpty prepend=" AND " property="segNo">
        SEG_NO = #segNo#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="unitCode">
        UNIT_CODE = #unitCode#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="carTraceNo">
        CAR_TRACE_NO = #carTraceNo#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="vehicleNo">
        VEHICLE_NO = #vehicleNo#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="targetHandPointId">
        TARGET_HAND_POINT_ID = #targetHandPointId#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="recCreator">
        REC_CREATOR = #recCreator#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="recCreatorName">
        REC_CREATOR_NAME = #recCreatorName#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="recCreateTime">
        REC_CREATE_TIME = #recCreateTime#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="recRevisor">
        REC_REVISOR = #recRevisor#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="recRevisorName">
        REC_REVISOR_NAME = #recRevisorName#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="recReviseTime">
        REC_REVISE_TIME = #recReviseTime#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="archiveFlag">
        ARCHIVE_FLAG = #archiveFlag#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="delFlag">
        DEL_FLAG = #delFlag#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="remark">
        REMARK = #remark#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="sysRemark">
        SYS_REMARK = #sysRemark#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="uuid">
        UUID = #uuid#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="tenantId">
        TENANT_ID = #tenantId#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="driverName">
        DRIVER_NAME = #driverName#
    </isNotEmpty>
    <isNotEmpty prepend=" AND " property="driverTel">
        DRIVER_TEL = #driverTel#
    </isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0411">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId",  <!-- 租户ID -->
				DRIVER_NAME	as "driverName",  <!-- 司机姓名 -->
				DRIVER_TEL	as "driverTel", <!-- 司机手机号 -->
		STATUS as "status" <!-- 状态 -->
		FROM meli.tlirl0411 WHERE 1=1
		and DEL_FLAG = 0
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="statusList">
			STATUS IN
			<iterate property="statusList" open="(" close=")" conjunction=",">
				#statusList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointIdList">
			TARGET_HAND_POINT_ID IN
			<iterate property="targetHandPointIdList" open="(" close=")" conjunction=",">
				#targetHandPointIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			DRIVER_TEL = #driverTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			TARGET_HAND_POINT_ID != #currentHandPointId#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>

	<select id="queryAll" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0411">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		TARGET_HAND_POINT_ID	as "handPointId",  <!-- 目标装卸点 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId",  <!-- 租户ID -->
		DRIVER_NAME	as "driverName",  <!-- 司机姓名 -->
		DRIVER_TEL	as "driverTel", <!-- 司机手机号 -->
		STATUS as "status", <!-- 状态 -->
		(select HAND_POINT_NAME
		from meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0411.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0411.TARGET_HAND_POINT_ID
		AND tlirl0304.STATUS = '30') as "handPointName"
		FROM meli.tlirl0411 WHERE 1=1
		and DEL_FLAG = 0
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="statusList">
			STATUS IN
			<iterate property="statusList" open="(" close=")" conjunction=",">
				#statusList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointIdList">
			TARGET_HAND_POINT_ID IN
			<iterate property="targetHandPointIdList" open="(" close=")" conjunction=",">
				#targetHandPointIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			DRIVER_TEL = #driverTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			TARGET_HAND_POINT_ID != #currentHandPointId#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0411 WHERE 1=1
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			DRIVER_TEL = #driverTel#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0411 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										TARGET_HAND_POINT_ID,  <!-- 目标装卸点 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
										DRIVER_NAME,  <!-- 司机姓名 -->
										DRIVER_TEL,  <!-- 司机手机号 -->
		STATUS <!-- 状态 -->
										)		 
	    VALUES (#segNo#, #unitCode#, #carTraceNo#, #vehicleNo#, #targetHandPointId#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#, #driverName#, #driverTel#, #status#)
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlirl0411 WHERE 
	</delete>

	<update id="update">
		UPDATE meli.tlirl0411 
		<dynamic prepend="SET">
			<isNotEmpty prepend="," property="segNo">
				SEG_NO = #segNo#   <!-- 系统账套 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="unitCode">
				UNIT_CODE = #unitCode#   <!-- 业务单元代码 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="carTraceNo">
				CAR_TRACE_NO = #carTraceNo#   <!-- 车辆跟踪号 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="vehicleNo">
				VEHICLE_NO = #vehicleNo#   <!-- 车牌号 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="targetHandPointId">
				TARGET_HAND_POINT_ID = #targetHandPointId#   <!-- 目标装卸点 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="recCreator">
				REC_CREATOR = #recCreator#   <!-- 记录创建人 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="recCreatorName">
				REC_CREATOR_NAME = #recCreatorName#   <!-- 记录创建人姓名 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="recCreateTime">
				REC_CREATE_TIME = #recCreateTime#   <!-- 记录创建时间 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="recRevisor">
				REC_REVISOR = #recRevisor#   <!-- 记录修改人 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="recRevisorName">
				REC_REVISOR_NAME = #recRevisorName#   <!-- 记录修改人姓名 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="recReviseTime">
				REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时间 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="archiveFlag">
				ARCHIVE_FLAG = #archiveFlag#   <!-- 归档标记 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="delFlag">
				DEL_FLAG = #delFlag#   <!-- 记录删除标记 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="remark">
				REMARK = #remark#   <!-- 备注 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="sysRemark">
				SYS_REMARK = #sysRemark#   <!-- 系统备注 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="tenantId">
				TENANT_ID = #tenantId#   <!-- 租户ID -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="driverName">
				DRIVER_NAME = #driverName#   <!-- 司机姓名 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="driverTel">
				DRIVER_TEL = #driverTel#   <!-- 司机手机号 -->
			</isNotEmpty>
			<isNotEmpty prepend="," property="status">
				STATUS = #status#   <!-- 状态 -->
			</isNotEmpty>
		</dynamic>
		WHERE UUID = #uuid#
	</update>

	<update id="updateStatus">
		UPDATE meli.tlirl0411 SET
			STATUS = #status#
		WHERE 1=1
		<isNotEmpty prepend=" AND " property="segNo">
						SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
						CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
	</update>

	<select id="queryAllHandPoint" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT SEG_NO            as "segNo",
		CAR_TRACE_NO      as "carTraceNo",
		VEHICLE_NO        as "vehicleNo",
		GROUP_CONCAT(DISTINCT TARGET_HAND_POINT_ID ORDER BY TARGET_HAND_POINT_ID SEPARATOR
		',') as "handPointId",
		GROUP_CONCAT(DISTINCT (select HAND_POINT_NAME
		from meli.tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0411.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0411.TARGET_HAND_POINT_ID) ORDER BY
		(select HAND_POINT_NAME
		from meli.tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0411.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0411.TARGET_HAND_POINT_ID) SEPARATOR
		',') as "handPointName",
		DRIVER_NAME       as "driverName",
		DRIVER_TEL        AS "driverTel",
		max(STATUS) as "status",
		max(REC_CREATE_TIME) as "recCreateTime"
		FROM meli.tlirl0411
		WHERE 1 = 1
		AND DEL_FLAG = 0
		<include refid="condition"/>
		<isNotEmpty prepend=" AND " property="statusList">
			STATUS IN
			<iterate property="statusList" open="(" close=")" conjunction=",">
				#statusList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointIdList">
			TARGET_HAND_POINT_ID IN
			<iterate property="targetHandPointIdList" open="(" close=")" conjunction=",">
				#targetHandPointIdList[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			DRIVER_TEL = #driverTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentHandPointId">
			TARGET_HAND_POINT_ID != #currentHandPointId#
		</isNotEmpty>
		GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO, DRIVER_NAME, DRIVER_TEL
	</select>
  
</sqlMap>