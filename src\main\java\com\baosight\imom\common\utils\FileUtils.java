package com.baosight.imom.common.utils;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.PageContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Set;

/**
 * 附件上传下载通用工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since Date:2024-08-23 16:52
 */
public class FileUtils {
    /**
     * 附件上传默认路径
     */
    private static final String ROOT_PATH = PlatApplicationContext.getProperty("docRootDir");
    private static final String DOWNLOAD_PATH = PlatApplicationContext.getProperty("docDownloadPath");

    /**
     * 上传文件至指定目录
     *
     * @param file     文件
     * @param filePath 存储目录（"/"开始结尾不要有"/"，如"/device"）
     * @return 文件下载地址
     * @throws Exception 异常
     */
    public static String uploadFile(CommonsMultipartFile file,
            String filePath) throws Exception {
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            fileName = "";
        }
        String storePath = ROOT_PATH;
        if (StrUtil.isNotBlank(filePath)) {
            storePath = ROOT_PATH + filePath;
        }
        String fileId = StrUtil.getUUID();
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        String storeName = storePath + "/" + fileId + suffix;
        File dirFile = new File(storePath);
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        file.transferTo(new File(storeName));
        try {
            String os = System.getProperty("os.name").toLowerCase();
            LogUtils.log("操作系统: " + os);
            // 如果是Linux系统，设置文件权限
            if (!os.contains("windows")) {
                Set<PosixFilePermission> permissions = PosixFilePermissions.fromString("rw-r--r--");
                Files.setPosixFilePermissions(Paths.get(storeName), permissions);
            }
        } catch (Exception e) {
            LogUtils.error("设置文件权限失败:", e.getMessage());
            throw new PlatException("设置文件权限失败:" + e.getMessage());
        }
        return DOWNLOAD_PATH + filePath + "/" + fileId + suffix;
    }

    /**
     * 上传文件至指定目录
     *
     * @param file     文件
     * @param filePath 存储目录（"/"开始结尾不要有"/"，如"/device"）
     * @return 文件下载地址
     * @throws Exception 异常
     */
    public static String uploadFileByOriginalName(CommonsMultipartFile file,
                                    String filePath) throws Exception {
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            fileName = "";
        }
        String storePath = ROOT_PATH;
        if (StrUtil.isNotBlank(filePath)) {
            storePath = ROOT_PATH + filePath;
        }
        String fileId = StrUtil.getUUID();
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        String storeName = storePath + "/" + fileName;
        File dirFile = new File(storePath);
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        file.transferTo(new File(storeName));
        try {
            String os = System.getProperty("os.name").toLowerCase();
            LogUtils.log("操作系统: " + os);
            // 如果是Linux系统，设置文件权限
            if (!os.contains("windows")) {
                Set<PosixFilePermission> permissions = PosixFilePermissions.fromString("rw-r--r--");
                Files.setPosixFilePermissions(Paths.get(storeName), permissions);
            }
        } catch (Exception e) {
            LogUtils.error("设置文件权限失败:", e.getMessage());
            throw new PlatException("设置文件权限失败:" + e.getMessage());
        }
        return DOWNLOAD_PATH + filePath + "/" + fileName;
    }

    /**
     * 上传文件至默认路径
     *
     * @param file 文件
     * @return 文件下载地址
     * @throws Exception 异常
     */
    public static String uploadFile(CommonsMultipartFile file) throws Exception {
        return uploadFile(file, "");
    }

    /**
     * 下载附件（前端页面调用）
     * 需传入参数：
     * id:id标识
     * serviceName:查询服务名
     * methodName:查询方法名
     * service返回参数：
     * filePath:文件路径
     * docName:下载显示的文件名
     *
     * @param pageContext PageContext
     * @throws Exception 异常
     */
    public static void downloadFile(PageContext pageContext) throws Exception {
        HttpServletResponse response = (HttpServletResponse) pageContext.getResponse();
        HttpServletRequest request = (HttpServletRequest) pageContext.getRequest();
        String id = request.getParameter("id");
        String serviceName = request.getParameter(EiConstant.serviceName);
        String methodName = request.getParameter(EiConstant.methodName);
        if (StrUtil.isBlank(id) || StrUtil.isBlank(serviceName) || StrUtil.isBlank(methodName)) {
            throw new PlatException("传入参数为空，需传入：id、serviceName、methodName");
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceName, serviceName);
        inInfo.set(EiConstant.methodName, methodName);
        inInfo.set("id", id);
        EiInfo outInfo = XLocalManager.call(inInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        String filePath = outInfo.getString("filePath");
        String docName = outInfo.getString("docName");
        if (StrUtil.isBlank(filePath) || StrUtil.isBlank(docName)) {
            throw new PlatException("service返回参数为空，需返回：filePath、docName");
        }
        httpDownload(response, filePath, docName);
    }

    /**
     * http下载
     *
     * @param response         response
     * @param filePath         文件地址
     * @param downloadFileName 文件名
     * @throws Exception 异常
     */
    private static void httpDownload(HttpServletResponse response, String filePath, String downloadFileName)
            throws Exception {
        File file = new File(filePath);
        FileInputStream fileInputStream = new FileInputStream(file);
        Throwable var6 = null;
        try {
            OutputStream outputStream = response.getOutputStream();
            Throwable var8 = null;
            try {
                downloadFileName = com.baosight.iplat4j.eu.dm.util.FileUtils.getEncodeFileName(downloadFileName);
                response.reset();
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + downloadFileName);
                byte[] buffer = new byte[1024];
                int i;
                while ((i = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, i);
                }
            } catch (Throwable var32) {
                var8 = var32;
                throw var32;
            } finally {
                if (outputStream != null) {
                    if (var8 != null) {
                        try {
                            outputStream.close();
                        } catch (Throwable var31) {
                            var8.addSuppressed(var31);
                        }
                    } else {
                        outputStream.close();
                    }
                }
            }
        } catch (Throwable var34) {
            var6 = var34;
            throw var34;
        } finally {
            if (var6 != null) {
                try {
                    fileInputStream.close();
                } catch (Throwable var30) {
                    var6.addSuppressed(var30);
                }
            } else {
                fileInputStream.close();
            }
        }
    }
}
