create table meli.tlids0801
(
    SEG_NO           varchar(12)    default ' '        not null comment '系统账套',
    UNIT_CODE        varchar(12)    default ' '        not null comment '业务单元代代码',
    WAREHOUSE_CODE   varchar(20)    default ' '        not null comment '仓库代码',
    WAREHOUSE_NAME   varchar(20)    default ' '        not null comment '仓库名称',
    AREA_TYPE        varchar(50)    default ' '        not null comment '区域类型',
    AREA_CODE        varchar(20)    default ' '        not null comment '区域代码',
    AREA_NAME        varchar(50)    default ' '        not null comment '区域名称',
    MOULD_ID         varchar(50)    default ' '        not null comment '模具ID',
    MOULD_NAME       varchar(300)   default ' '        not null comment '模具名称',
    X_POSITION       varchar(20)    default ' '        null comment 'X轴坐标',
    Y_POSITION       varchar(20)    default ' '        null comment 'X轴坐标',
    Z_POSITION       varchar(20)    default ' '        null comment 'Y轴坐标',
    PACK_WEIGHT      decimal(20, 8) default 0.00000000 not null comment '模具重量',
    POS_DIR_CODE     varchar(2)     default ' '        null comment '层数标记',
    CRANE_RESULT_ID  varchar(32)    default ' '        null comment '行车实绩单号',
    STATUS           varchar(2)     default ' '        not null comment '状态',
    REC_CREATOR      varchar(32)    default ' '        null comment '记录创建人',
    REC_CREATOR_NAME varchar(100)   default ' '        null comment '记录创建人姓名',
    REC_CREATE_TIME  varchar(17)    default ' '        null comment '记录创建时间',
    REC_REVISOR      varchar(32)    default ' '        null comment '记录修改人',
    REC_REVISOR_NAME varchar(100)   default ' '        null comment '记录修改人姓名',
    REC_REVISE_TIME  varchar(17)    default ' '        null comment '记录修改时间',
    ARCHIVE_FLAG     varchar(1)     default ' '        null comment '归档标记',
    TENANT_USER      varchar(10)    default ' '        null comment '租户',
    DEL_FLAG         smallint       default 0          null comment '删除标记',
    UUID             varchar(32)    default ' '        not null comment 'ID'
        primary key,
    constraint tlids0801_UUID_uindex
        unique (UUID)
)
    comment '模具库存管理表' collate = utf8_bin;

