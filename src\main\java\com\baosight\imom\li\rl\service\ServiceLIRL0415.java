package com.baosight.imom.li.rl.service;


import cfca.org.slf4j.Logger;
import cfca.org.slf4j.LoggerFactory;
import cn.hutool.core.lang.hash.Hash;
import com.alibaba.fastjson.JSON;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.li.ds.domain.LIDS1101;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.xservices.em.util.SmsSendManager;
import com.spire.doc.interfaces.IField;
import com.spire.ms.System.Collections.Specialized.CollectionsUtil;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 张博翔
 * @Description: ${车辆进出厂综合查询}
 * @Date: 2024/8/13 13:26
 * @Version: 1.0
 */
public class ServiceLIRL0415 extends ServiceBase {


    private static final Logger logger = LoggerFactory.getLogger(ServiceLIRL0415.class);
    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0108().eiMetadata);
        return inInfo;
    }

    /**
     * S_UC_PR_020801
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryEnterFactoryCarList(EiInfo inInfo) {
            EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            String vehicleNo = MapUtils.getString(queryMap, "vehicleNo", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            queryMap.put("status", "20");
            queryMap.put("delFlag", "0");
            outInfo = super.query(inInfo, LIRL0201.QUERY_ENTERING_THE_FACTORY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * S_UC_PR_020801
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryPutoutCarList(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            String segNo = MapUtils.getString(queryMap, "segNo", "");
            String segName = MapUtils.getString(queryMap, "segName", "");
            String vehicleNoY = MapUtils.getString(queryMap, "vehicleNo", "");
            if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
                String massage = "缺少业务单元代码不能查询！";
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg(massage);
                return inInfo;
            }
            queryMap.put("statusListStr", "40");
            queryMap.put("delFlag", "0");
            queryMap.put("vehicleNoY", vehicleNoY);
            queryMap.put("vehicleNo", "");
            outInfo = super.query(inInfo, LIRL0301.QUERY_PUTOUT_CAR, new LIRL0301(), false, null, EiConstant.queryBlock, "result2", "result2");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 重庆车辆进厂
     *
     * @param inInfo
     * @return
     */
    public EiInfo vehicleIntoFactoryFlagCQ(EiInfo inInfo) {
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);//当前日期
        List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
        EiInfo outInfo = new EiInfo();
        String stringDate = DateUtil.curDateTimeStr14();//修改时间
        Map in_modi_person = new HashMap();
        //1.装货预约，查询当天唯一装货配单，如果只有一个配车单，插到叫号表，并发送短信，
        // 如果多个配单跳过，到现场自己手动启动排队。车辆跟踪号为空时回写车辆跟踪号，进厂进入排队，通知司机
        //2.卸货预约，放行，插入车辆跟踪表，不插排队表
        //3.装卸货预约，查询是否有装货配单，暂不回写车辆跟踪号，进厂插入车辆跟踪表
        //4.其余业务类型40,50,60,70,80，根据业务类型查询装卸点，直接插入进厂
        //常驻车不受限制

        for (HashMap hashMap : listHashMap) {
            String in_seg_no = MapUtils.getString(hashMap, "segNo", "");//系统账套号
            String in_vehicle_no = MapUtils.getString(hashMap, "vehicleNo", "");//车牌号
            String driverIdentity = MapUtils.getString(hashMap, "driverIdentity", "");//司机身份证号
            String driverName = MapUtils.getString(hashMap, "driverName", "");//司机姓名
            String driverTel = MapUtils.getString(hashMap, "driverTel", "");//司机电话
            String factoryType = MapUtils.getString(hashMap, "factoryType", "");//10：首次进厂 20：厂内周转
            String typeOfHandling = MapUtils.getString(hashMap, "typeOfHandling", "");//装卸业务（10：装货配单，20：卸货配单）
            String reservationNumber = MapUtils.getString(hashMap, "reservationNumber", "");//预约单号
            String dataSource = MapUtils.getString(hashMap, "dateSource", "");//数据源
            String visitUnit = MapUtils.getString(hashMap, "visitUnit", "");//拜访单位
            String userId = UserSession.getUserId();//创建人
            String loginCName = UserSession.getLoginCName();// 创建人姓名
            String msgTargetHandPointId = ""; //记录插入的装卸点
            String msgTargetHandPointName = "";//记录插入的装卸点名称
            String msgFactoryArea = "";//记录插入的厂区
            String msgFactoryAreaName = "";//记录插入的厂区名称
            String loadingChannelNo = "";//装卸通道
            in_modi_person.put("userId", userId);
            in_modi_person.put("loginCName", loginCName);
            //增加车辆类型标记
            String vehicleType = "0";;//车辆类型
            if (StringUtils.isBlank(dataSource)){
                dataSource = "10";
            }
            List<String> handPointList = new ArrayList<>();
            //查询车辆是临时车还是常驻车
            Map queryLIRL0501 = new HashMap();
            queryLIRL0501.put("status", "20");
            queryLIRL0501.put("vehicleNo", in_vehicle_no);
            queryLIRL0501.put("delFlag", 0);
            queryLIRL0501.put("vehicleType","1");
            List<LIRL0501> lirl0501s = dao.query(LIRL0501.QUERY, queryLIRL0501);
            if (lirl0501s.size() > 0) {//常驻车
                vehicleType ="1";
            }else{//临时车
                vehicleType ="0";
            }
            if ("1".equals(vehicleType)){
                //查询当天的是否有未完成的装货配单任务
                Map queryLIRL0502 = new HashMap();
                queryLIRL0502.put("segNo", in_seg_no);
                queryLIRL0502.put("vehicleNo", in_vehicle_no);
                queryLIRL0502.put("estimatedTimeOfArrival", format);
                queryLIRL0502.put("allocType", 10);
                queryLIRL0502.put("delFlag", 0);
                queryLIRL0502.put("status", "20");
                List<LIRL0502> lirl0502s = dao.query(LIRL0502.QUERY, queryLIRL0502);

                //生成车辆跟踪单数据
                Map insertLIRL0301 = new HashMap();
                insertLIRL0301.put("segNo", in_seg_no);
                insertLIRL0301.put("unitCode", in_seg_no);
                String strSeqTypeId = "TLIRL_SEQ0301";//车辆跟踪流水号
                Date date = new Date(System.currentTimeMillis());
                String[] args = {in_seg_no.substring(0, 2), date.toString(), ""};
                String carTraceNo = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                String allocateVehicleNo="";
                //判断司机信息
                if (StringUtils.isBlank(driverTel)||StringUtils.isBlank(driverName)){
                    //根据车牌号查询司机信息
                    String drUuid = lirl0501s.get(0).getDrUuid();
                    //查询0102
                    HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                    stringObjectHashMap.put("segNo", in_seg_no);
                    stringObjectHashMap.put("uuid", drUuid);
                    stringObjectHashMap.put("status","20");
                    List<HashMap> query = this.dao.query(LIRL0102.SUB_QUERY, stringObjectHashMap);
                    if (CollectionUtils.isNotEmpty( query)){
                        driverTel= (String) query.get(0).get("tel");
                        driverName= (String) query.get(0).get("driverName");
                        driverIdentity= (String) query.get(0).get("driverIdentity");
                    }
                }
                insertLIRL0301.put("carTraceNo", carTraceNo);
                insertLIRL0301.put("status", 20);
                insertLIRL0301.put("vehicleNo", in_vehicle_no);
                insertLIRL0301.put("idCard", driverIdentity);
                insertLIRL0301.put("driverName", driverName);
                insertLIRL0301.put("telNum", driverTel);
                insertLIRL0301.put("reservationNumber", reservationNumber);
                insertLIRL0301.put("checkDate", format);
                insertLIRL0301.put("enterFactory", format);
                insertLIRL0301.put("uuid", StrUtil.getUUID());// uuid
                insertLIRL0301.put("targetHandPointId", " ");
                insertLIRL0301.put("factoryArea", msgFactoryArea);
                insertLIRL0301.put("factoryAreaName", msgFactoryAreaName);
                insertLIRL0301.put("allocateVehicleNo", " ");
                //插入车辆跟踪单数据
                if (lirl0502s.size()>0){
                    insertLIRL0301.put("handType", "10");
                }else {
                    insertLIRL0301.put("handType", " ");
                }
                RecordUtils.setCreator(insertLIRL0301);
                LIRL0301 lirl0301 = new LIRL0301();
                lirl0301.fromMap(insertLIRL0301);
                //首次进厂
                String intoFactoryId = "";//车辆进厂流水号
                //先根据传过来的车牌号来看下这辆车是否已经进厂但是未出厂
                Map queryLIRL0301 = new HashMap();
                queryLIRL0301.put("segNo", in_seg_no);
                queryLIRL0301.put("vehicleNo", in_vehicle_no);
                queryLIRL0301.put("carTraceNoNot", carTraceNo);
                queryLIRL0301.put("statusListStr", "'20','30','40'");
                queryLIRL0301.put("delFlag", "0");
                int count = super.count(LIRL0301.COUNT, queryLIRL0301);
                if (count > 0) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("该车辆已经进厂未出厂，无法再次进厂");
                    throw new RuntimeException(outInfo.getMsg());
                }
                dao.insert(LIRL0301.INSERT, lirl0301);
                //可进厂
                //查询的跟踪表的数据添加到进厂表中
                queryLIRL0301.put("statusListStr", "");
                queryLIRL0301.put("carTraceNo", carTraceNo);
                List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
                if (lirl0301s.size() > 0) {
                    lirl0301 = lirl0301s.get(0);
                    Map insertLIRL0405 = new HashMap();
                    strSeqTypeId = "TLIRL_SEQ0405";//车辆进厂流水号
                    intoFactoryId = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                    insertLIRL0405.put("segNo", lirl0301.getSegNo());
                    insertLIRL0405.put("unitCode", lirl0301.getUnitCode());
                    insertLIRL0405.put("intoFactoryId", intoFactoryId);
                    insertLIRL0405.put("vehicleNo", lirl0301.getVehicleNo());
                    insertLIRL0405.put("status", "10");
                    insertLIRL0405.put("intoFactoryDate", stringDate);
                    insertLIRL0405.put("cancelIntoFactoryDate", "");
                    insertLIRL0405.put("carTraceNo", lirl0301.getCarTraceNo());
                    insertLIRL0405.put("dateSource", "10");//来源写死10mes
                    insertLIRL0405.put("targetHandPointId", lirl0301.getTargetHandPointId());
                    insertLIRL0405.put("factoryArea", lirl0301.getFactoryArea());
                    insertLIRL0405.put("forceFlag", "0");
                    insertLIRL0405.put("handType", lirl0301.getHandType());
                    RecordUtils.setCreator(insertLIRL0405);
                    insertLIRL0405.put("delFlag", "0");// 记录删除标记
                    insertLIRL0405.put("uuid", StrUtil.getUUID());// uuid
                    insertLIRL0405.put("factoryType", "10");// 10：首次进厂 20：厂内周转
                    LIRL0405 lirl0405 = new LIRL0405();
                    lirl0405.fromMap(insertLIRL0405);
                    dao.insert(LIRL0405.INSERT, lirl0405);
                } else {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("查询不到车辆追踪单信息");
                    throw new RuntimeException(outInfo.getMsg());
                }
                //修改跟踪表的状态
                Map updateLIRL03011 = new HashMap();
                updateLIRL03011.put("segNo", in_seg_no);
                updateLIRL03011.put("vehicleNo", in_vehicle_no);
                updateLIRL03011.put("carTraceNo", carTraceNo);
                updateLIRL03011.put("status", "20");
                updateLIRL03011.put("delFlag", "0");
                RecordUtils.setRevisor(updateLIRL03011);
                updateLIRL03011.put("recReviseTime", stringDate);// 修改时间
                updateLIRL03011.put("enterFactory", stringDate);// 入厂时间
                dao.update(LIRL0301.UPDATE_LIRL0301JC, updateLIRL03011);
                // 返回成功状态和消息
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("车辆进厂: 车辆进厂成功!");
                return outInfo;
            }else {
                    //生成车辆跟踪单数据
                    Map insertLIRL0301 = new HashMap();
                    insertLIRL0301.put("segNo", in_seg_no);
                    insertLIRL0301.put("unitCode", in_seg_no);
                    String strSeqTypeId = "TLIRL_SEQ0301";//车辆跟踪流水号
                    Date date = new Date(System.currentTimeMillis());
                    String[] args = {in_seg_no.substring(0, 2), date.toString(), ""};
                    String carTraceNo = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                    insertLIRL0301.put("carTraceNo", carTraceNo);
                    insertLIRL0301.put("status", 20);
                    String handType = "";
                    if ("40".equals(typeOfHandling)) {
                        handType = "40";
                    } else if ("50".equals(typeOfHandling)) {
                        handType = "20";
                    } else if ("60".equals(typeOfHandling)) {
                        handType = "10";
                    } else if ("30".equals(typeOfHandling)) {
                        handType = "30";
                    } else if ("70".equals(typeOfHandling)) {
                        handType = "10";
                    }else if ("20".equals(typeOfHandling)) {
                        handType = "20";
                    }else if ("10".equals(typeOfHandling)) {
                        handType = "10";
                    }
                    insertLIRL0301.put("handType", handType);
                    insertLIRL0301.put("vehicleNo", in_vehicle_no);
                    insertLIRL0301.put("idCard", driverIdentity);
                    insertLIRL0301.put("driverName", driverName);
                    insertLIRL0301.put("telNum", driverTel);
                    insertLIRL0301.put("reservationNumber", reservationNumber);
                    insertLIRL0301.put("checkDate", format);
                    insertLIRL0301.put("enterFactory", format);
                    insertLIRL0301.put("uuid", StrUtil.getUUID());// uuid
                    insertLIRL0301.put("targetHandPointId", " ");
                    insertLIRL0301.put("factoryArea", msgFactoryArea);
                    insertLIRL0301.put("factoryAreaName", msgFactoryAreaName);
                    insertLIRL0301.put("allocateVehicleNo", " ");
                    RecordUtils.setCreator(insertLIRL0301);
                    LIRL0301 lirl0301 = new LIRL0301();
                    lirl0301.fromMap(insertLIRL0301);
                    //首次进厂
                    String intoFactoryId = "";//车辆进厂流水号
                    //先根据传过来的车牌号来看下这辆车是否已经进厂但是未出厂
                    Map queryLIRL0301 = new HashMap();
                    queryLIRL0301.put("segNo", in_seg_no);
                    queryLIRL0301.put("vehicleNo", in_vehicle_no);
                    queryLIRL0301.put("carTraceNoNot", carTraceNo);
                    queryLIRL0301.put("statusListStr", "'20','30','40'");
                    queryLIRL0301.put("delFlag", "0");
                    int count = super.count(LIRL0301.COUNT, queryLIRL0301);
                    if (count > 0) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该车辆已经进厂未出厂，无法再次进厂");
                        throw new RuntimeException(outInfo.getMsg());
                    }
                    //插入车辆跟踪单数据
                    dao.insert(LIRL0301.INSERT, lirl0301);
                    //可进厂
                    //查询的跟踪表的数据添加到进厂表中
                    queryLIRL0301.put("statusListStr", "");
                    queryLIRL0301.put("carTraceNo", carTraceNo);
                    List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
                    if (lirl0301s.size() > 0) {
                        lirl0301 = lirl0301s.get(0);
                        Map insertLIRL0405 = new HashMap();
                        strSeqTypeId = "TLIRL_SEQ0405";//车辆进厂流水号
                        intoFactoryId = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                        insertLIRL0405.put("segNo", lirl0301.getSegNo());
                        insertLIRL0405.put("unitCode", lirl0301.getUnitCode());
                        insertLIRL0405.put("intoFactoryId", intoFactoryId);
                        insertLIRL0405.put("vehicleNo", lirl0301.getVehicleNo());
                        insertLIRL0405.put("status", "10");
                        insertLIRL0405.put("intoFactoryDate", stringDate);
                        insertLIRL0405.put("cancelIntoFactoryDate", "");
                        insertLIRL0405.put("carTraceNo", lirl0301.getCarTraceNo());
                        insertLIRL0405.put("dateSource", dataSource);//来源写死10mes
                        insertLIRL0405.put("targetHandPointId", lirl0301.getTargetHandPointId());
                        insertLIRL0405.put("factoryArea", lirl0301.getFactoryArea());
                        insertLIRL0405.put("forceFlag", "0");
                        insertLIRL0405.put("handType", lirl0301.getHandType());
                        RecordUtils.setCreator(insertLIRL0405);
                        insertLIRL0405.put("delFlag", "0");// 记录删除标记
                        insertLIRL0405.put("uuid", StrUtil.getUUID());// uuid
                        insertLIRL0405.put("factoryType", "10");// 10：首次进厂 20：厂内周转
                        LIRL0405 lirl0405 = new LIRL0405();
                        lirl0405.fromMap(insertLIRL0405);
                        dao.insert(LIRL0405.INSERT, lirl0405);
                    } else {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("查询不到车辆追踪单信息");
                        throw new RuntimeException(outInfo.getMsg());
                    }

                    if ("重庆宝钢".equals(visitUnit)) {
                        //增加开关
//                        DOES_TRANSPORTATION_AUTOMATICALLY_ENTER_THE_QUEUE
                        String doesTransportationAutomaticallyEnterTheQueue = new SwitchUtils().getProcessSwitchValue(in_seg_no, "DOES_TRANSPORTATION_AUTOMATICALLY_ENTER_THE_QUEUE", dao);
                        if ("1".equals(doesTransportationAutomaticallyEnterTheQueue)) {
                            if ("40".equals(typeOfHandling)) {
                                String in_business_type = "";
                                if ("40".equals(typeOfHandling)) {
                                    in_business_type = "30";
                                } else if ("80".equals(typeOfHandling)) {
                                    in_business_type = "60";
                                }
                                //查询装卸点对照表
                                HashMap queryMap = new HashMap<>();
                                queryMap.put("segNo", in_seg_no);
                                queryMap.put("businessType", in_business_type);
                                queryMap.put("status", 30);
                                queryMap.put("delFlag", 0);
//                queryMap.put("statusFlag", 10);
                                List<String> targetHandPointIdList = new ArrayList<>();
                                List<LIRL0309> lirl0309s = this.dao.query(LIRL0309.QUERY, queryMap);
                                if (lirl0309s.size() > 0) {
                                    //查询对应装卸点是否存在
                                    List<String> stringList = new ArrayList<>();
                                    for (LIRL0309 lirl0309 : lirl0309s) {
                                        stringList.add(lirl0309.getHandPointId());
                                        msgTargetHandPointId = lirl0309.getHandPointId();
                                        String handTypeTwo = lirl0309.getHandType();
                                        targetHandPointIdList.add(msgTargetHandPointId);
                                    }
                                    queryMap.put("status", 30);
                                    queryMap.put("handPointIdList", targetHandPointIdList);
                                    List<LIRL0304> lirl0304s = this.dao.query(LIRL0304.QUERY, queryMap);
                                    if (lirl0304s.size() > 0) {
                                        msgTargetHandPointName = (String) lirl0304s.get(0).getHandPointName();
                                    }
                                } else {
                                    String massage = "没匹配到有效的装卸点，请联系工作人员进行处理！";
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg(massage);
                                    return outInfo;
                                }

                                //插入排队表
                                for (String handPointId : targetHandPointIdList) {
                                    //排队表
                                    LIRL0401 lirl0401 = new LIRL0401();
                                    lirl0401.setSegNo(in_seg_no);
                                    lirl0401.setUnitCode(in_seg_no);
                                    lirl0401.setUuid(UUIDUtils.getUUID());
                                    //取排序表最大数
                                    HashMap queryQueMap = new HashMap();
                                    queryQueMap.put("segNo", in_seg_no);
                                    List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMaxQueueNumber", queryQueMap);
                                    HashMap queryQueRes = queryQueReslist.get(0);
                                    String queueNumberStr = String.valueOf(queryQueRes.get("maxQueueNumber"));
                                    Integer queueNumber = 0;
                                    queueNumber = new Integer(queueNumberStr);

                                    lirl0401.setQueueNumber(queueNumber);
                                    lirl0401.setCarTraceNo(carTraceNo);
                                    lirl0401.setVehicleNo(in_vehicle_no);
                                    lirl0401.setVoucherNum(" "); //放配单号
                                    lirl0401.setPriorityLevel("99");
                                    lirl0401.setQueueDate(DateUtil.curDateTimeStr14());
                                    lirl0401.setTargetHandPointId(handPointId);
                                    lirl0401.setFactoryArea(msgFactoryArea);
                                    lirl0401.setDelFlag(Integer.valueOf(0));
                                    lirl0401.setSysRemark("车辆进厂自动启动排队");
                                    Map map = lirl0401.toMap();
                                    lirl0401 = JSON.parseObject(JSON.toJSONString(map), LIRL0401.class);
                                    RecordUtils.setCreator(lirl0401.toMap());
                                    this.dao.insert(LIRL0401.INSERT, lirl0401);
                                }

                                String handPointName = "";
                                HashMap<String, Object> hashMap1 = new HashMap<>();
                                hashMap1.put("segNo", in_seg_no);
                                hashMap1.put("handPointIdList", targetHandPointIdList.stream().distinct().collect(Collectors.toList()));
                                //查询装卸点名称
                                List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO, hashMap1);
                                if (queryLIRL0304.size() > 0) {
                                    handPointName = queryLIRL0304.stream().map(LIRL0304::getHandPointName).collect(Collectors.joining(","));
                                }
                                //短信内容
                                outInfo = new EiInfo();
                                HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                                objectObjectHashMap.put("param1", driverTel);
                                objectObjectHashMap.put("param2", driverName);
                                objectObjectHashMap.put("param3", in_vehicle_no);
                                objectObjectHashMap.put("param4", handPointName);
                                MessageUtils.sendMessage(objectObjectHashMap, "MT0000001016");
                            }
                        }
                    }
                    //修改跟踪表的状态
                    Map updateLIRL0301 = new HashMap();
                    updateLIRL0301.put("segNo", in_seg_no);
                    updateLIRL0301.put("vehicleNo", in_vehicle_no);
                    updateLIRL0301.put("carTraceNo", carTraceNo);
                    updateLIRL0301.put("status", "20");
                    updateLIRL0301.put("delFlag", "0");
                    RecordUtils.setRevisor(updateLIRL0301);
                    updateLIRL0301.put("recReviseTime", stringDate);// 修改时间
                    updateLIRL0301.put("enterFactory", stringDate);// 入厂时间
                    dao.update(LIRL0301.UPDATE_LIRL0301JC, updateLIRL0301);
            }
        }
        // 返回成功状态和消息
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("车辆进厂: 车辆进厂成功!");
        return outInfo;
    }

    /***
     * 车辆出厂
     * @param eiInfo
     * @return
     */
    public EiInfo vehicleLeaveFactory(EiInfo eiInfo) {

        try {
            List<HashMap> result2 = eiInfo.getBlock("result2").getRows();
            for (HashMap hashMap : result2) {
                String segNo = MapUtils.getString(hashMap, "segNo");
                String carTraceNo = MapUtils.getString(hashMap, "carTraceNo");
                String vehicleNo = MapUtils.getString(hashMap, "vehicleNo");
                String typeOfHandling = MapUtils.getString(hashMap, "typeOfHandling");
                String allocateVehicleNo1= MapUtils.getString(hashMap, "allocateVehicleNo");
                String dateSource= MapUtils.getString(hashMap, "dateSource");
                if (StringUtils.isBlank(dateSource)){
                    dateSource="10";
                }
                if (StringUtils.isBlank(allocateVehicleNo1)){
                    //根据车辆跟踪号查询配单信息
                    HashMap<Object, Object> hashMap1 = new HashMap<>();
                    hashMap1.put("segNo", segNo);
                    hashMap1.put("carTraceNo", carTraceNo);
                    hashMap1.put("vehicleNo",vehicleNo);
                    hashMap1.put("status","20");
                    List<LIRL0502> query = this.dao.query(LIRL0502.QUERY, hashMap1);
                    if (CollectionUtils.isNotEmpty(query)){
                        allocateVehicleNo1=query.get(0).getAllocateVehicleNo();
                    }
                }
                String stringDate = DateUtil.curDateTimeStr14();//出厂时间
                Date date = new Date(System.currentTimeMillis());
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("segNo", segNo);
                stringObjectHashMap.put("carTraceNo", carTraceNo);
                stringObjectHashMap.put("vehicleNo", vehicleNo);
                //查询出来跟踪表中的数据插入到出厂维护表中
                List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, stringObjectHashMap);
                if (queryLIRL0301.size() <= 0) {
                    String massage = "车辆跟踪表中数据不存在！";
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(massage);
                    throw new RuntimeException(massage);
                }
                //出厂流水号
                String strSeqTypeId = "TMELI0408_SEQ01";
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String leaveFactoryId = SequenceGenerator.getNextSequence(strSeqTypeId, args); //出厂流水号
//                String vehicleNo = queryLIRL0301.get(0).getVehicleNo();
                String factoryArea = queryLIRL0301.get(0).getFactoryArea();

                String handType = queryLIRL0301.get(0).getHandType();
                eiInfo.set("leaveFactoryId",leaveFactoryId);
                hashMap.put("leaveFactoryId", leaveFactoryId);
                hashMap.put("vehicleNo", vehicleNo);
                hashMap.put("factoryArea", factoryArea);
                hashMap.put("status", MesConstant.Status.K10);
                hashMap.put("leaveFactoryDate", stringDate);
                hashMap.put("uuid", UUIDUtils.getUUID());
                RecordUtils.setCreator(hashMap);
                LIRL0408 lirl0408 = new LIRL0408();
                lirl0408.fromMap(hashMap);
                lirl0408.setDateSource(dateSource);
//                this.dao.insert(LIRL0408.INSERT, lirl0408);

                // 修改跟踪表的状态
                HashMap<String, String> hashMapLirl0301 = new HashMap<>();
                hashMapLirl0301.put("segNo", segNo);
                hashMapLirl0301.put("carTraceNo", carTraceNo);
                hashMapLirl0301.put("leaveFactoryDate", stringDate);
                hashMapLirl0301.put("delFlag", "0");
                hashMapLirl0301.put("status", "50");
                RecordUtils.setRevisor(hashMapLirl0301);
//                this.dao.update(LIRL0301.UPDATE_LIRL0301CC, hashMapLirl0301);

                //删除排队表中的信息
                HashMap<String, Object> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("segNo",segNo);
                stringStringHashMap.put("carTraceNo",carTraceNo);
                stringStringHashMap.put("vehicleNo",vehicleNo);
                //删除排队表
                this.dao.delete(LIRL0401.DELETE, stringStringHashMap);
                //删除叫号表
                this.dao.delete(LIRL0402.DELETE, stringStringHashMap);
                //删除超时表
                this.dao.delete(LIRL0403.DELETE, stringStringHashMap);
                //把出厂数据插入发货看板车辆跟踪管理备份表
                /*
                自提：
                如果客户自提，直接扔到备份表
                代运：
                客户签收后之后才能插入备份表
                如果业务类型：装卸货直接插入备份表
                */
                String identityType = "";
                String reservationNumber = queryLIRL0301.get(0).getReservationNumber();
                if ("30".equals(handType)) {
                    //直接插入备份表
                    LIRL0301 lirl0301 = queryLIRL0301.get(0);
                    Map map = lirl0301.toMap();
                    map.put("status", "50");
                    map.put("leaveFactoryDate", stringDate);
                    map.put("uuid", UUIDUtils.getUUID());
                    RecordUtils.setCreator(map);
                    this.dao.insert(LIRL0311.INSERT, map);
                }else {
                    //查看是自提还是代运
                    String driverName = queryLIRL0301.get(0).getDriverName();
                    String telNum = queryLIRL0301.get(0).getTelNum();
                    HashMap<String, String> hashMapLIRL0102 = new HashMap<>();
                    hashMapLIRL0102.put("segNo", segNo);
                    hashMapLIRL0102.put("reservationNumber", reservationNumber);
                    hashMapLIRL0102.put("driverName", driverName);
                    hashMapLIRL0102.put("telNum", telNum);
                    List<LIRL0101> queryLIRL0101 = this.dao.query(LIRL0101.QUERY, hashMapLIRL0102);
                    if (queryLIRL0101.size() > 0) {
                        identityType = queryLIRL0101.get(0).getIdentityType();
                        if (MesConstant.Status.K10.equals(identityType)) {
                            //自提，直接扔到备份表
                            LIRL0301 lirl0301 = queryLIRL0301.get(0);
                            Map map = lirl0301.toMap();
                            if ("F1".equals(factoryArea)){
                                map.put("factoryAreaName", "一期厂房");
                            }else if ("F2".equals(factoryArea)){
                                map.put("factoryAreaName", "二期厂房");
                            }else if ("F3".equals(factoryArea)){
                                map.put("factoryAreaName", "三期厂房");
                            }else if ("F4".equals(factoryArea)){
                                map.put("factoryAreaName", "四期厂房");
                            }
                            map.put("status", "50");
                            map.put("leaveFactoryDate", stringDate);
                            map.put("uuid", UUIDUtils.getUUID());
                            map.put("expectedLoadingTime", StringUtils.isNotEmpty((String)map.get("expectedLoadingTime"))?(String)map.get("expectedLoadingTime"):" ");
                            RecordUtils.setCreator(map);
                            this.dao.insert(LIRL0311.INSERT, map);
                        } else {
                            //代运，查看是否客户签收，签收后插入备份表
                            //小程序签收时,出库单信息同步插入车辆跟踪表备份表
                            // 查询车辆跟踪
                            // String status = queryLIRL0101.get(0).getStatus();
                            //自提，直接扔到备份表
                            LIRL0301 lirl0301 = queryLIRL0301.get(0);
                            Map map = lirl0301.toMap();
                            map.put("status", "50");
                            map.put("leaveFactoryDate", stringDate);
                            map.put("uuid", UUIDUtils.getUUID());
                            if ("F1".equals(factoryArea)){
                                map.put("factoryAreaName", "一期厂房");
                            }else if ("F2".equals(factoryArea)){
                                map.put("factoryAreaName", "二期厂房");
                            }else if ("F3".equals(factoryArea)){
                                map.put("factoryAreaName", "三期厂房");
                            }else if ("F4".equals(factoryArea)){
                                map.put("factoryAreaName", "四期厂房");
                            }
                            map.put("expectedLoadingTime", StringUtils.isNotEmpty((String)map.get("expectedLoadingTime"))?(String)map.get("expectedLoadingTime"):" ");
                            RecordUtils.setCreator(map);
                            this.dao.insert(LIRL0311.INSERT, map);

                        }
                    }
                }
                //删除车辆跟踪表
                Map<Object, Object> hashMapLIRL0301 = new HashMap<>();
                hashMapLIRL0301.put("segNo", segNo);
                hashMapLIRL0301.put("carTraceNo", carTraceNo);
                RecordUtils.setRevisor(hashMapLIRL0301);
                this.dao.delete(LIRL0301.DELETE, hashMapLIRL0301);

                //更新车辆预约表状态为完成
                Map updateLIRL0201 = new HashMap();
                updateLIRL0201.put("segNo", segNo);
                updateLIRL0201.put("reservationNumber", reservationNumber);
                updateLIRL0201.put("status", "99");
                updateLIRL0201.put("recReviseTime", stringDate);
                updateLIRL0201.put("delFlag", 0);
                RecordUtils.setRevisor(updateLIRL0201);
                dao.update(LIRL0201.UPDATE_STATUS, updateLIRL0201);

                //出厂废料提货置为完成
                hashMapLIRL0301.put("status", "99");
                this.dao.update(LIRL0411.UPDATE_STATUS,hashMapLIRL0301);
                //有配单
                if (StringUtils.isNotBlank(allocateVehicleNo1)) {
//                if("10".equals(typeOfHandling)||"30".equals(typeOfHandling)) {
                    //调用查询捆包明细服务S_LI_RL_0041
                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                    objectObjectHashMap.put("segNo", segNo);
                    objectObjectHashMap.put("carTraceNo", carTraceNo);
                    objectObjectHashMap.put("status", "20");
                    //如果是卸货+装货，针对于装货配单没有装的捆包撤回装完的99置为完成，同时配单主项置为完成状态
                    //卸货配单同样完成
                    List<LIRL0502> queryLIRL0502 = this.dao.query(LIRL0502.QUERY, objectObjectHashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0502)) {
                        for (LIRL0502 lirl0502 : queryLIRL0502) {
                            String allocType = lirl0502.getAllocType();
                            String allocateVehicleNo = lirl0502.getAllocateVehicleNo();
                            List<String> packIdSre = new ArrayList<>();
                            if ("10".equals(allocType)) {
                                List<String> objects = new ArrayList<>();
                                objects.add(allocateVehicleNo);
                                //装货配单
                                HashMap<Object, Object> hashMapLirl0502 = new HashMap<>();
                                hashMapLirl0502.put("allocateVehicleNoAdd", objects);
                                hashMapLirl0502.put("outPackFlag", "0");//去除自带货
                                hashMapLirl0502.put("voucherNumFlag", "0"); //去除无计划
                                hashMapLirl0502.put("statusEq", "20"); //去除无计划
                                hashMapLirl0502.put("segNo", segNo); //去除无计划
                                //判断卸货配单是否全部卸完
                                List<HashMap> query = this.dao.query(LIRL0503.QUERY_ALL_PACK, hashMapLirl0502);
                                if (query.size() <= 0) {
                                    //已装完成
                                    HashMap hashMapR = new HashMap();
                                    hashMapR.put("status", "99");
                                    hashMapR.put("segNo", segNo);
                                    hashMapR.put("allocateVehicleNoAdd", objects);
                                    hashMapR.put("delFlag", "0");
                                    RecordUtils.setRevisor(hashMapR);
                                    hashMapR.put("remark", "出厂配单完成:" + DateUtil.curDateTimeStr14());
                                    this.dao.update(LIRL0502.UPDATE_STATUS, hashMapR);
                                    this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR);
                                } else {

                                    //未装
                                    for (HashMap map : query) {
                                        HashMap hashMapR = new HashMap();
                                        hashMapR.put("segNo", segNo);
                                        hashMapR.put("packIdNo", map.get("packId"));
                                        hashMapR.put("allocateVehicleNo", allocateVehicleNo);
                                        RecordUtils.setRevisor(hashMapR);
                                        hashMapR.put("remark", "出厂配单完成:" + DateUtil.curDateTimeStr14());
                                        hashMapR.put("status", "00");
                                        hashMapR.put("delFlag", "1");
                                        packIdSre.add((String) map.get("packId"));
                                        this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR);
                                    }
                                    //
                                    //已装完成
                                    HashMap hashMapR = new HashMap();
                                    hashMapR.put("status", "99");
                                    hashMapR.put("segNo", segNo);
                                    hashMapR.put("allocateVehicleNo", allocateVehicleNo);
                                    hashMapR.put("delFlag", "0");
                                    hashMapR.put("packIdNoStr", packIdSre);
                                    RecordUtils.setRevisor(hashMapR);
                                    hashMapR.put("remark", "出厂配单完成:" + DateUtil.curDateTimeStr14());
                                    this.dao.update(LIRL0502.UPDATE_STATUS, hashMapR);
                                    this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR);

                                    //只要是30的全部置为99
                                    //已装完成
                                    HashMap hashMapR30 = new HashMap();
                                    hashMapR30.put("status", "99");
                                    hashMapR30.put("segNo", segNo);
                                    hashMapR30.put("allocateVehicleNo", allocateVehicleNo);
                                    hashMapR30.put("delFlag", "0");
                                    hashMapR30.put("statusC", "30");
                                    RecordUtils.setRevisor(hashMapR30);
                                    hashMapR30.put("remark", "出厂配单完成:" + DateUtil.curDateTimeStr14());
                                    this.dao.update(LIRL0502.UPDATE_STATUS, hashMapR30);
                                    this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR30);

                                }
                            } else if ("20".equals(allocType)) {
                                List<String> collect = new ArrayList<>();
                                collect.add(allocateVehicleNo);
                                //
                                //已装完成
                                HashMap hashMapR = new HashMap();
                                hashMapR.put("status", "99");
                                hashMapR.put("segNo", segNo);
                                hashMapR.put("allocateVehicleNoAdd", collect);
                                hashMapR.put("delFlag", "0");
                                hashMapR.put("packIdNoStr", packIdSre);
                                RecordUtils.setRevisor(hashMapR);
                                hashMapR.put("remark", "出厂配单完成:" + DateUtil.curDateTimeStr14());
                                this.dao.update(LIRL0502.UPDATE_STATUS, hashMapR);
                                this.dao.update(LIRL0503.UPDATE_STATUS, hashMapR);
                            }
                        }
                    }
                }
                this.dao.insert(LIRL0408.INSERT, lirl0408);
                eiInfo.set("leaveFactoryId", leaveFactoryId);
                eiInfo.set("carTraceNo", carTraceNo);
            }
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }
        return eiInfo;
    }


    /***
     * 根据优先级生成序号
     * @param emergencyDeliveryTimeNew
     * @param segNo
     * @return
     */
    public int getQueueNumberStr(String emergencyDeliveryTimeNew, String segNo) {
        int queueNumberStr;
        HashMap<String, Object> queryQueMap = new HashMap<>();
        queryQueMap.put("segNo", segNo);
        if (StringUtils.isNotBlank(emergencyDeliveryTimeNew)){
            //取排序表最小数
            //判断是否有多个紧急单号车辆
            List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMinQueueNumberPriorityLevel", queryQueMap);
            if (CollectionUtils.isNotEmpty(queryQueReslist)){
                HashMap queryQueRes = queryQueReslist.get(0);
//                queueNumberStr= (Integer) queryQueRes.get("minQueueNumber");
                queueNumberStr= MapUtils.getInteger(queryQueRes, "minQueueNumber");
                if (queueNumberStr==0){
                    queueNumberStr=0;
                }else {
                    queueNumberStr=queueNumberStr+1;
                }
            }else {
                queryQueReslist = dao.query("LIRL0401.queryMinQueueNumber", queryQueMap);
                HashMap queryQueRes = queryQueReslist.get(0);
                Integer queueNumberStr1= MapUtils.getInteger(queryQueRes, "minQueueNumber");
                if (queueNumberStr1==0){
                    queueNumberStr=0;
                }else {
                    queueNumberStr=queueNumberStr1-1;
                }
            }
        }else {
            List<HashMap> queryQueReslist = dao.query("LIRL0401.queryMaxQueueNumber", queryQueMap);
            HashMap queryQueRes = queryQueReslist.get(0);
            queueNumberStr = MapUtils.getInteger(queryQueRes, "maxQueueNumber");
        }
        return queueNumberStr;
    }
}
