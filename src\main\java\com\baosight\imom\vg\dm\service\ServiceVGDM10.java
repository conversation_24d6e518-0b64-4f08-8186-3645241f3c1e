package com.baosight.imom.vg.dm.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.imom.vi.pm.domain.VIPM0004;
import com.baosight.imom.vi.pm.domain.VIPM0005;
import com.baosight.imom.vi.pm.domain.VIPM0007;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;


/**
 * <AUTHOR> yzj
 * @Description : 设备运行时相关服务
 * @Date : 2025/02/23
 * @Version : 1.0
 */
public class ServiceVGDM10 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM10.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM1002().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT3_BLOCK).addBlockMeta(new VGDM1005().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM1002.QUERY, new VGDM1002());
    }

    /**
     * 根据主项查询工序时间
     */
    public EiInfo queryResult(EiInfo inInfo) {
        String relevanceId = inInfo.getString("inqu2_status-0-relevanceId");
        if (StrUtil.isBlank(relevanceId)) {
            return inInfo;
        }
        return super.query(inInfo, VGDM1003.QUERY, null, false, new VGDM1003().eiMetadata,
                MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 根据主项查询产出实绩
     */
    public EiInfo queryOutResult(EiInfo inInfo) {
        String relevanceId = inInfo.getString("inqu3_status-0-relevanceId");
        if (StrUtil.isBlank(relevanceId)) {
            return inInfo;
        }
        return super.query(inInfo, VGDM1005.QUERY, null, false, new VGDM1005().eiMetadata,
                MesConstant.Iplat.INQU3_STATUS_BLOCK, MesConstant.Iplat.RESULT3_BLOCK, MesConstant.Iplat.RESULT3_BLOCK);
    }

    /**
     * 计算工序时间
     */
    public EiInfo calculate(EiInfo inInfo) {
        try {
            VGDM1002 vgdm1002 = new VGDM1002();
            vgdm1002.fromMap(inInfo.getBlock(EiConstant.resultBlock).getRow(0));
            if (StrUtil.isBlank(vgdm1002.getUpPackTime())
                    || StrUtil.isBlank(vgdm1002.getEndTime())) {
                throw new PlatException("开始时间和结束时间不能为空");
            }
            LOGGER.info("准备计算:" + vgdm1002.getEArchivesNo() + "|" + vgdm1002.getUpPackTime() + "|" + vgdm1002.getEndTime());
            log("准备计算:" + vgdm1002.getEArchivesNo() + "|" + vgdm1002.getUpPackTime() + "|" + vgdm1002.getEndTime());
            // 特殊标记，用于判断是否计算标准工时和离线时间
            String simpleFlag = inInfo.getString("simpleFlag");
            boolean isSimple = StrUtil.isNotBlank(simpleFlag);
            log("简单计算标记:" + simpleFlag);
            // 查询配置信息
            Map<String, String> map = new HashMap<>();
            map.put("eArchivesNo", vgdm1002.getEArchivesNo());
            map.put("segNo", vgdm1002.getSegNo());
            map.put("ruleStatus", MesConstant.Status.K20);
            List<VGDM1101> configList = dao.query(VGDM1101.QUERY, map);
            if (CollectionUtils.isEmpty(configList)) {
                throw new PlatException(vgdm1002.getEArchivesNo() + "无配置信息");
            }
            // 删除上次计算结果
            Map<String, String> params = new HashMap<>();
            RecordUtils.setRevisorSys(params);
            params.put("relevanceId", vgdm1002.getUuid());
            dao.update(VGDM1003.UPDATE_BY_RELEVANCE, params);
            // 待新增数据
            List<Map> insList = new ArrayList<>();
            // 作业时间范围
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            Date startDate = sdf.parse(vgdm1002.getUpPackTime());
            Date endDate = sdf.parse(vgdm1002.getEndTime());
            // 标准工时计算
            long workSeconds = 0;
            // 上一次计算的顺序，用于判断是否将上次的结束时间作为本次开始时间
            int lastSortIndex = configList.get(0).getSortIndex();
            Date lastEndDate = startDate;
            // 遍历结果块中的每一行数据
            for (VGDM1101 ruleConfig : configList) {
                LOGGER.info("准备计算工序:" + ruleConfig.getProcedureCode());
                log("准备计算工序:" + ruleConfig.getProcedureCode() + "|" + ruleConfig.getSortIndex());
                // 纵切简单标记只计算上料和开卷时间
                if (isSimple && !"SL".equals(ruleConfig.getProcedureCode())
                        && !"KJ".equals(ruleConfig.getProcedureCode())) {
                    log("简单计算跳过");
                    continue;
                }
                // 当前工序晚于上一工序时以上一工序结束时间作为本次查询时间
                if (ruleConfig.getSortIndex() > lastSortIndex) {
                    startDate = lastEndDate;
                    log("设置开始时间:" + startDate);
                }
                lastSortIndex = ruleConfig.getSortIndex();
                // 计算工序时间
                VGDM1003 result = this.calProcedure(ruleConfig, vgdm1002, insList, startDate, endDate);
                if (StrUtil.isNotBlank(result.getStopTime())) {
                    String lastEnd = result.getStopTime().replaceAll("[\\-: ]", "").substring(0, 14);
                    lastEndDate = sdf.parse(lastEnd);
                }
                // 自动运行工序时间参与计算标准工时
                if ("ZDYX".equals(result.getProcedureCode())) {
                    workSeconds += result.getDuration();
                    log("存在自动运行时间s：" + result.getDuration());
                }
            }
            // 批量插入数据
            DaoUtils.insertBatch(dao, VGDM1003.INSERT, insList);
            // 计算标准工时
            if (!isSimple && workSeconds > 0) {
                this.calWorkHour(vgdm1002, workSeconds);
            }
            if (!isSimple) {
                // 计算离线时间
                this.calOffline(vgdm1002, insList);
            }
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception e) {
            // 设置失败状态和错误消息
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 计算工序时间
     *
     * @param ruleConfig 规则
     * @param vgdm1002   投料捆包信息
     * @param insList    待插入数据
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @return 工序时间
     * @throws Exception 异常
     */
    private VGDM1003 calProcedure(VGDM1101 ruleConfig
            , VGDM1002 vgdm1002, List<Map> insList, Date startDate, Date endDate) throws Exception {
        LOGGER.info("开始时间：" + startDate + "结束时间" + endDate);
        log("开始时间：" + startDate + "结束时间" + endDate);
        // 工序时间结果
        VGDM1003 result = new VGDM1003();
        result.fromMap(ruleConfig.toMap());
        result.setRelevanceId(vgdm1002.getUuid());
        result.setDuration(0);
        // 解析开始和结束时间规则
        ProcedureRule startRule = CalculateUtils.objectMapper
                .readValue(ruleConfig.getIhdStartRule(), ProcedureRule.class);
        // 结束时间规则特殊处理
        ProcedureRule stopRule = new ProcedureRule();
        if (!"END".equals(ruleConfig.getIhdStopRule())) {
            stopRule = CalculateUtils.objectMapper
                    .readValue(ruleConfig.getIhdStopRule(), ProcedureRule.class);
        }
        // 涉及tag点信息
        Set<String> tagIds = startRule.getAllField();
        tagIds.addAll(stopRule.getAllField());
        LOGGER.info("涉及tag:" + tagIds);
        log("涉及tag:" + tagIds);
        if (CollectionUtils.isEmpty(tagIds)) {
            throw new PlatException(ruleConfig.getProcedureCode() + "规则中无有效点位");
        }
        // 获取点位数据
        List<Map> tagValues = IhdSdkUtils.querySortedHisRecord(ruleConfig.getSegNo(), new ArrayList<>(tagIds), startDate, endDate);
        if (CollectionUtils.isEmpty(tagValues)) {
            throw new PlatException(ruleConfig.getProcedureCode() + "未查询到有效点位数据");
        }
        // 计算事件时间
        LocalDateTime[] eventTimes = new LocalDateTime[2];
        int endIndex = CalculateUtils.calculateEventTimes(eventTimes, tagValues, startRule,
                stopRule);
        // 结束时间特殊处理
        if ("END".equals(ruleConfig.getIhdStopRule())) {
            eventTimes[1] = LocalDateTime.parse(vgdm1002.getEndTime(), DateUtils.FORMATTER_14);
        }
        // 更新时间
        boolean updFlag = this.updProcedureResult(result, insList, eventTimes);
        if (!updFlag) {
            return result;
        }
        // 重复计算逻辑
        if ("1".equals(ruleConfig.getMultiFlag()) && endIndex > 0) {
            int startIndex = endIndex + 1;
            while (startIndex < tagValues.size()) {
                log("重复计算，开始索引：" + startIndex);
                List<Map> subList = tagValues.subList(startIndex, tagValues.size());
                int temIndex = CalculateUtils.calculateEventTimes(eventTimes, subList, startRule,
                        stopRule);
                updFlag = this.updProcedureResult(result, insList, eventTimes);
                if (!updFlag || temIndex < 0) {
                    return result;
                }
                startIndex = startIndex + temIndex + 1;
            }
        }
        return result;
    }

    /**
     * 更新工序时间
     *
     * @param result     工序时间
     * @param insList    待新增数据
     * @param eventTimes 时间结果
     * @return 是否为有效数据
     */
    private boolean updProcedureResult(VGDM1003 result, List<Map> insList, LocalDateTime[] eventTimes) {
        if (eventTimes[0] != null) {
            log("结果，开始时间：" + eventTimes[0]);
            result.setStartTime(DateUtils.FORMATTER_23.format(eventTimes[0]));
        }
        if (eventTimes[1] != null) {
            log("结果，结束时间：" + eventTimes[1]);
            result.setStopTime(DateUtils.FORMATTER_23.format(eventTimes[1]));
        }
        // 获取相差时间秒
        long duration = 0;
        if (eventTimes[0] != null && eventTimes[1] != null) {
            duration = ChronoUnit.SECONDS.between(eventTimes[0], eventTimes[1]) + 1;
            log("结果，持续时间：" + duration);
        }
        int temDuration = result.getDuration() + (int) duration;
        result.setDuration((int) duration);
        // 开始时间结束时间都为空时不新增
        if (eventTimes[0] == null && eventTimes[1] == null) {
            result.setDuration(temDuration);
            return false;
        }
        // 准备插入数据
        Map insMap = result.toMap();
        RecordUtils.setCreatorSys(insMap);
        insList.add(insMap);
        // 累计持续时间方便后续统计总时间
        result.setDuration(temDuration);
        return true;
    }

    /**
     * 计算加工工时
     */
    public EiInfo calWorkHour(EiInfo inInfo) {
        try {
            VGDM1002 vgdm1002 = new VGDM1002();
            vgdm1002.fromMap(inInfo.getBlock(EiConstant.resultBlock).getRow(0));
            if (vgdm1002.checkIsSL()) {
                throw new PlatException("纵切不支持");
            }
            //计算
            vgdm1002.calCLWorkHour(dao);
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception e) {
            // 设置失败状态和错误消息
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 绘图
     * <p>
     * 根据捆包作业信息查询对应的工序时间信息
     */
    public EiInfo draw(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            List<Map> rows = block.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                throw new PlatException("未找到对应工序时间信息");
            }
            Map<String, String> queryMap = new HashMap<>();
            EiBlock rtnBlock = inInfo.addBlock(MesConstant.Iplat.RESULT3_BLOCK);
            for (Map row : rows) {
                VGDM1002 vgdm1002 = new VGDM1002();
                vgdm1002.fromMap(row);
                if (StrUtil.isBlank(vgdm1002.getUuid())) {
                    continue;
                }
                // 查询工序时间信息
                queryMap.put("relevanceId", vgdm1002.getUuid());
                List<VGDM1003> timeList = dao.query(VGDM1003.QUERY, queryMap);
                if (CollectionUtils.isEmpty(timeList)) {
                    continue;
                }
                for (VGDM1003 timeResult : timeList) {
                    timeResult.setEArchivesNo(vgdm1002.getPackId());
                    timeResult.setEquipmentName(vgdm1002.getPackId());
                }
                rtnBlock.addRows(timeList);
            }
            inInfo.setMsgByKey(MesConstant.EPResource.EP_1003);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 手工结束
     */
    public EiInfo forceEnd(EiInfo inInfo) {
        try {
            VGDM1002 vgdm1002 = new VGDM1002();
            vgdm1002.fromMap(inInfo.getBlock(EiConstant.resultBlock).getRow(0));
            if (!StrUtil.isBlank(vgdm1002.getEndTime())) {
                throw new PlatException("捆包已结束");
            }
            vgdm1002.setEndTime(DateUtil.curDateTimeStr14());
            vgdm1002.setPackStatus("2");
            vgdm1002.setEndType("9");
            Map updMap = vgdm1002.toMap();
            RecordUtils.setRevisor(updMap);
            dao.update(VGDM1002.UPDATE, updMap);
            inInfo.getBlock(EiConstant.resultBlock).getRows().set(0, updMap);
            // 设置成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception e) {
            // 设置失败状态和错误消息
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 离线时间
     */
    public void calOffline(VGDM1002 vgdm1002, List<Map> timeList) {
        if (CollectionUtils.isEmpty(timeList)) {
            log("无离线时间数据");
            return;
        }
        // 删除原数据
        log("删除上次计算结果");
        Map<String, Object> delMap = new HashMap<>();
        delMap.put("machineOfflineSeqId", vgdm1002.getUuid());
        delMap.put("segNo", vgdm1002.getSegNo());
        delMap.put("delFlag", "1");
        RecordUtils.setRevisorSys(delMap);
        dao.update(VIPM0004.UPD_FOR_DEL, delMap);
        log("准备计算:" + vgdm1002.getEArchivesNo() + "离线时间");
        List<Map> insList = new ArrayList<>();
        for (int i = 0; i < timeList.size(); i++) {
            VGDM1003 vgdm1003 = new VGDM1003();
            vgdm1003.fromMap(timeList.get(i));
            // 自动运行时间忽略
            if ("ZDYX".equals(vgdm1003.getProcedureCode())
                    || vgdm1003.getDuration() <= 0) {
                continue;
            }
            VIPM0004 vipm0004 = new VIPM0004();
            // 基础信息
            vipm0004.fromMap(vgdm1002.toMap());
            vipm0004.setMachineOfflineSeqId(vgdm1002.getUuid());
            vipm0004.setMachineOfflineStatus("10");
            vipm0004.setInPartId(vgdm1002.getPartId());
            vipm0004.setInSpecsDesc(vgdm1002.getSpecsDesc());
            // 离线时间 -
            BigDecimal minutes = BigDecimal.valueOf(vgdm1003.getDuration())
                    .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
            log("离线事件：" + vgdm1003.getProcedureName() + "时间(m):" + minutes);
            vipm0004.setOfflineTime(minutes);
            // 离线事件
            vipm0004.setOfflineEvent(vgdm1003.getProcedureName());
            // 待新增数据
            Map insMap = vipm0004.toMap();
            RecordUtils.setCreatorSys(insMap);
            // 待新增数据
            insList.add(insMap);
        }
        // 批量插入
        DaoUtils.insertBatch(dao, VIPM0004.INSERT, insList);
        // 发送IMC
//        this.sendIMC(vgdm1002.getSegNo(), "S_VI_SM_0136", "eventDetail", insList);
    }

    /**
     * 计算标准工时
     */
    public void calWorkHour(VGDM1002 vgdm1002, long workSeconds) {
        // 删除原数据
        log("删除上次计算结果");
        Map<String, Object> delMap = new HashMap<>();
        delMap.put("standardHoursSeqId", vgdm1002.getUuid());
        delMap.put("segNo", vgdm1002.getSegNo());
        delMap.put("delFlag", "1");
        RecordUtils.setRevisorSys(delMap);
        dao.update(VIPM0005.UPD_FOR_DEL, delMap);
        log("准备计算:" + vgdm1002.getEArchivesNo() + "|" + workSeconds);
        long minutes = (workSeconds / 60) + 1;
        log("加工时间(分)：" + minutes);
        List<Map> insList = new ArrayList<>();
        VIPM0005 vipm0005 = new VIPM0005();
        vipm0005.fromMap(vgdm1002.toMap());
        // 产出信息
        VIPM0007 outPack = this.getCLOutPack(vgdm1002);
        // 基础信息
        vipm0005.setStandardHoursSeqId(vgdm1002.getUuid());
        vipm0005.setProcessCategoryName(vgdm1002.getProcessCategory());
        vipm0005.setStandardWorkingHoursStatus("10");
        vipm0005.setProdTypeId(vgdm1002.getProdNameCode());
        vipm0005.setProdTypeName(vgdm1002.getProdCname());
        vipm0005.setInPartId(vgdm1002.getPartId());
        vipm0005.setInSpecsDesc(vgdm1002.getSpecsDesc());
        // 默认横切速度0，纵切分条1
        vipm0005.setSph(BigDecimal.ZERO);
        vipm0005.setStripCount(1);
        vipm0005.setProcessSpeed(BigDecimal.ZERO);
        // 产出量（纵切的分条数)
        long totalCount = getOutCount(vgdm1002);
        if (vgdm1002.checkIsSL()) {
            // 纵切分条数
            vipm0005.setStripCount((int) totalCount);
            // 纵切加工速度 m/分钟
            vipm0005.setProcessSpeed(vgdm1002.calPackLength()
                    .divide(new BigDecimal(minutes)
                            , 2, RoundingMode.HALF_UP));
        } else {
            long hours = (minutes / 60) + 1;
            log("加工时间h:" + hours);
            BigDecimal sumQuantity = VGDM1005.querySumOutResult(dao,
                    vgdm1002.getUuid(),
                    true);
            // 横切落料加工速度 片/h
            vipm0005.setSph(sumQuantity
                    .divide(new BigDecimal(hours)
                            , 0, RoundingMode.HALF_UP));
            // 更新产出信息
            vipm0005.setPartId(outPack.getPartId());
            vipm0005.setSpecsDesc(outPack.getSpecsDesc());
            vipm0005.setSurfaceGrade(outPack.getSurfaceGrade());
            vipm0005.setProdTypeId(outPack.getProdTypeId());
            vipm0005.setProdTypeName(outPack.getProdTypeDesc());
            vipm0005.setProcessMark(outPack.getFinishingShuntFlag());
        }
        // 待新增数据
        Map insMap = vipm0005.toMap();
        RecordUtils.setCreatorSys(insMap);
        // 字段转换-分条数
        insMap.put("lines", vipm0005.getStripCount());
        // 待新增数据
        insList.add(insMap);
        // 批量插入
        DaoUtils.insertBatch(dao, VIPM0005.INSERT, insList);
        // 发送IMC
//        this.sendIMC(vgdm1002.getSegNo(), "S_VI_SM_0135", "sendDetail", insList);
    }

    /**
     * 发送IMC
     *
     * @param segNo     账套
     * @param serviceId 服务id
     * @param paramName 参数名
     * @param insList   待发送数据
     */
    private void sendIMC(String segNo, String serviceId, String paramName, List<Map> insList) {
        if (CollectionUtils.isEmpty(insList)) {
            return;
        }
        String switchValue = (new SwitchUtils()).getProcessSwitchValue(segNo, "IF_SEND_STANDARD_HOUR_IMC", dao);
        log("配置开关：" + switchValue);
        if (!"1".equals(switchValue)) {
            return;
        }
        // 上传IMC
        EiInfo imcInfo = new EiInfo();
        imcInfo.set(paramName, insList);
        // 服务ID
        imcInfo.set(EiConstant.serviceId, serviceId);
        EiInfo rtnInfo = EServiceManager.call(imcInfo, TokenUtils.getImomToken());
        if (rtnInfo.getStatus() < 0) {
            throw new PlatException(rtnInfo.getMsg());
        }
    }

    /**
     * 获取产出数
     *
     * @param vgdm1002 捆包加工信息
     * @return 纵切分条数
     */
    private Long getOutCount(VGDM1002 vgdm1002) {
        // 查询产出信息
        List<VIPM0007> outPackList = new ArrayList<>();
        boolean isKnife = vgdm1002.queryOutPack(dao, outPackList, false);
        // 投料捆包数
        int totalInCount = 1;
        if (!isKnife) {
            totalInCount = vgdm1002.queryInCount(dao);
        }
        long count = 0;
        for (VIPM0007 outPack : outPackList) {
            count = count + outPack.getProductProcessQty() / totalInCount;
        }
        log("产出数量：" + count);
        return count;
    }

    /**
     * 标准工时平均计算
     */
    public EiInfo autoAvgStandardHour(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            log("传入参数：" + segNo);
            LocalDateTime startTime = LocalDateTime.now().minusMonths(1);
            String timeStr = DateUtils.FORMATTER_14.format(startTime);
            log("开始时间：" + timeStr);
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("startTime", timeStr);
            List<Map> list = dao.query(VIPM0005.QUERY_FOR_AVG, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("没有可处理的数据");
            }
            log("待处理数据：" + list.size());
            List insList = new ArrayList<>();
            VIPM0005 vipm0005;
            String uuid = UUIDUtils.getUUID();
            for (Map map : list) {
                vipm0005 = new VIPM0005();
                vipm0005.fromMap(map);
                BigDecimal rowCount = MapUtils.getBigDecimal(map, "rowCount");
                // 纵切平均速度
                if ("SL".equals(vipm0005.getProcessCategory())) {
                    vipm0005.setProcessSpeed(vipm0005.getProcessSpeed()
                            .divide(rowCount, 2, RoundingMode.HALF_UP));
                } else {
                    vipm0005.setSph(vipm0005.getSph()
                            .divide(rowCount, 0, RoundingMode.HALF_UP));
                    vipm0005.setProcessSpeed(BigDecimal.ZERO);
                }
                vipm0005.setSegNo(segNo);
                vipm0005.setUnitCode(segNo);
                vipm0005.setStandardHoursSeqId(uuid);
                vipm0005.setStandardWorkingHoursStatus("10");
                vipm0005.setDataType("20");
                Map insMap = vipm0005.toMap();
                RecordUtils.setCreatorSys(insMap);
                // 字段转换-分条数
                insMap.put("lines", vipm0005.getStripCount());
                insList.add(insMap);
            }
            // 批量插入
            DaoUtils.insertBatch(dao, VIPM0005.INSERT, insList);
            // 上传IMC
            this.sendIMC(segNo, "S_VI_SM_0135", "sendDetail", insList);
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 离线时间平均计算
     */
    public EiInfo autoAvgOffline(EiInfo inInfo) {
        try {
            String segNo = inInfo.getString("segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            log("传入参数：" + segNo);
            LocalDateTime startTime = LocalDateTime.now().minusMonths(1);
            String timeStr = DateUtils.FORMATTER_14.format(startTime);
            log("开始时间：" + timeStr);
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("startTime", timeStr);
            List<Map> list = dao.query(VIPM0004.QUERY_FOR_AVG, queryMap);
            if (CollectionUtils.isEmpty(list)) {
                throw new PlatException("没有可处理的数据");
            }
            log("待处理数据：" + list.size());
            List insList = new ArrayList<>();
            VIPM0004 vipm0004;
            String uuid = UUIDUtils.getUUID();
            for (Map map : list) {
                vipm0004 = new VIPM0004();
                vipm0004.fromMap(map);
                BigDecimal rowCount = MapUtils.getBigDecimal(map, "rowCount");
                // 离线时间
                vipm0004.setOfflineTime(vipm0004.getOfflineTime()
                        .divide(rowCount, 2, RoundingMode.HALF_UP));
                // 默认字段
                vipm0004.setSegNo(segNo);
                vipm0004.setUnitCode(segNo);
                vipm0004.setMachineOfflineSeqId(uuid);
                vipm0004.setMachineOfflineStatus("10");
                vipm0004.setDataType("20");
                Map insMap = vipm0004.toMap();
                RecordUtils.setCreatorSys(insMap);
                insList.add(insMap);
            }
            // 批量插入
            DaoUtils.insertBatch(dao, VIPM0004.INSERT, insList);
            // 上传IMC
            this.sendIMC(segNo, "S_VI_SM_0136", "eventDetail", insList);
            // 返回消息
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_OPERATE);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }


    /**
     * 非纵切获取产出信息
     *
     * @param inPack 投料信息
     * @return 产出信息
     */
    private VIPM0007 getCLOutPack(VGDM1002 inPack) {
        // 获取理论产出数据
        List<VIPM0007> outPackList = new ArrayList<>();
        inPack.queryOutPack(dao, outPackList);
        // 返回第一个规格
        return outPackList.get(0);
    }
}
