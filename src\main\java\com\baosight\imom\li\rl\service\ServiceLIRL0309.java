package com.baosight.imom.li.rl.service;


import com.baosight.bpm.util.UUIDUtil;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.rl.dao.LIRL0301;
import com.baosight.imom.li.rl.dao.LIRL0304;
import com.baosight.imom.li.rl.dao.LIRL0315;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0309;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baosight.imom.common.constants.MesConstant.Iplat.RESULT_BLOCK;


/**
 * @Author: 韩亚宁
 * @Description: ${厂内装卸点管理}
 * @Date: 2024/8/26 09:10
 * @Version: 1.0
 */
public class ServiceLIRL0309 extends ServiceBase {

    public static final String STATUS = "status"; //状态
    public static final String DEL_FLAG = "delFlag"; //删除标记
    public static final String REMARK = "remark"; //备注
    public static final String UUID = "uuid"; //uuid
    public static final String ZERO = "0"; //uuid
    public static final String ONE = "1"; //uuid
    public static final String RESULT1 = "result1"; //uuid
    public static final String RESULT = "result"; //uuid
    public static final String HAND_POINT_ID = "handPointId"; //uuid
    public static final String LOAD_FLAG = "loadFlag"; //uuid
    public static final String UNLOAD_FLAG = "unloadFlag"; //uuid
    public static final String EXPECTED_RECOVERY_TIME = "expectedRecoveryTime"; //uuid
    public static final String SEQ_NUM = "seqNum"; //uuid
    public static final String SYS_REMARK = "sysRemark"; //uuid
    public static final String CAR_TRACE_NO = "carTraceNo"; //uuid

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(RESULT1).addBlockMeta(new LIRL0309().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        Map listHashMap = (HashMap) inInfo.getBlock(RESULT).getRow(0);
        listHashMap.put("status","");
        listHashMap.put("businessType","");
        List<LIRL0309> list = this.dao.query(LIRL0309.QUERY, listHashMap);
        eiInfo.addBlock(RESULT1).addBlockMeta(new LIRL0309().eiMetadata);
        eiInfo.getBlock(RESULT1).setRows(list);
        return eiInfo;
    }
    
    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT1).getRows();
            for (HashMap hashMap : listHashMap) {
                String businessType = MapUtils.getString(hashMap, "businessType");
                String unitCode = MapUtils.getString(hashMap, "unitCode");
                if ("10".equals(businessType)){
                    hashMap.put("handType", "20");//车辆跟踪号
                }else if ("20".equals(businessType)){
                    hashMap.put("handType", "10");//车辆跟踪号
                }else if ("30".equals(businessType)){
                    hashMap.put("handType", "40");//车辆跟踪号
                }else if ("40".equals(businessType)){
                    hashMap.put("handType", "20");
                }else if ("50".equals(businessType)){
                    hashMap.put("handType", "10");
                }else if ("60".equals(businessType)){
                    hashMap.put("handType", "20");
                }
                //装卸点
                String uuid  = UUIDUtil.genId();
                hashMap.put(CAR_TRACE_NO, "");//车辆跟踪号
                hashMap.put(STATUS, 10);//状态
                hashMap.put(DEL_FLAG, 0);//记录删除标记
                hashMap.put(REMARK, MapUtils.getString(hashMap, REMARK, "").trim());//备注
                hashMap.put(HAND_POINT_ID, MapUtils.getString(hashMap, HAND_POINT_ID, "").trim());//装卸点
                hashMap.put(LOAD_FLAG, "");//装货标记
                hashMap.put(UNLOAD_FLAG, "");//卸货标记
                hashMap.put(EXPECTED_RECOVERY_TIME, "");//预计恢复时间
                hashMap.put(SEQ_NUM, "");//预计恢复时间
                hashMap.put(UUID, UUIDUtils.getUUID());//uuid
                hashMap.put(SYS_REMARK,"");//uuid
                hashMap.put("segNo",unitCode);//uuid
                //添加创建人、姓名、时间
                RecordUtils.setCreator(hashMap);
                this.dao.insert( LIRL0309.INSERT,hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT1).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0309> query = dao.query(LIRL0309.QUERY, hashmap);
                for (LIRL0309 LIRL0309:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0309);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String businessType = MapUtils.getString(hashmap, "businessType");
                if ("10".equals(businessType)){
                    hashmap.put("handType", "20");//车辆跟踪号
                }else if ("20".equals(businessType)){
                    hashmap.put("handType", "10");//车辆跟踪号
                }else if ("30".equals(businessType)){
                    hashmap.put("handType", "20");//车辆跟踪号
                }else if ("40".equals(businessType)){
                    hashmap.put("handType", "20");
                }else if ("50".equals(businessType)){
                    hashmap.put("handType", "10");
                }
                hashmap.put("sysRemark", " ");//记录删除标记
                RecordUtils.setRevisor(hashmap);//修改创建人、姓名、时间
                this.dao.update(LIRL0309.UPDATE,hashmap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT1).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0309> query = dao.query(LIRL0309.QUERY, hashMap);
                for (LIRL0309 LIRL0309:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0309);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K00);//撤销状态
                hashMap.put(DEL_FLAG, ONE);//记录删除标记
                hashMap.put("sysRemark", " ");//记录删除标记
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0309.UPDATE,hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 启用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo enable(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT1).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0309> query = dao.query(LIRL0309.QUERY, hashMap);
                for (LIRL0309 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusEanbledDisabled1(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K30);//启用状态
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0309.UPDATE,hashMap);
            }
            // inInfo = super.update(inInfo, LIRL0309.UPDATE,RESULT1);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 停用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo disable(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT1).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0309> query = dao.query(LIRL0309.QUERY, hashMap);
                for (LIRL0309 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereEnabledStatusDisabled(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K99);//停用状态
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0309.UPDATE,hashMap);
            }
            // inInfo = super.update(inInfo, LIRL0309.UPDATE,RESULT1);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
