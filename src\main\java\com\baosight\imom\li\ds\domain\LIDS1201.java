/**
* Generate time : 2024-10-14 10:47:43
* Version : 1.0
*/
package com.baosight.imom.li.ds.domain;
import com.baosight.imom.common.li.domain.Tlids1201;

import java.util.Map;

/**
* TLIDS1201
* 
*/
public class LIDS1201 extends Tlids1201 {
        public static final String SEQ_ID = "TLIDS1201_SEQ01";
        public static final String QUERY = "LIDS1201.query";
        public static final String COUNT = "LIDS1201.count";
        public static final String INSERT = "LIDS1201.insert";
        public static final String UPDATE = "LIDS1201.update";
        public static final String DELETE = "LIDS1201.delete";
        public static final String GET_ALL_CRANE_PERFORMANCE = "LIDS1201.getAllCranePerformance";//查询行车对应的所有行车实绩
        public static final String GET_ALL_ABNORMAL_CRANE_PERFORMANCE = "LIDS1201.getAllAbnormalCranePerformance";//查询行车对应的所有异常实绩

        public static final String UPDATE_WORK_ORDER = "LIDS1201.updateWorkOrder";//更新行车作业清单
        public static final String UPDATE_ABNORMAL_FLAG = "LIDS1201.updateAbnormalFlag";//更新异常标识
        public static final String GET_LAST_ABNORMAL = "LIDS1201.getLastAbnormal";//查询最后一条异常实绩

        public static final String QUERY_VGDM1005_BY_MACHINE_CODE = "LIDS1201.queryVgdm1005ByMachineCode";//查询捆包生产实绩时间表最后一条未吊运的数据
        public static final String UPDATE_VGDM1005_LIFT_FLAG = "LIDS1201.updateVgdm1005LiftFlag";//修改捆包生产实绩时间表吊运标记
        @Override
        public void initMetaData() {
                super.initMetaData();
        }

        /**
         * the constructor
         */
        public LIDS1201() {
                initMetaData();
        }

        /**
         * get the value from Map
         */
        @Override
        public void fromMap(Map map) {
                super.fromMap(map);
        }

        /**
         * set the value to Map
         */
        @Override
        public Map toMap() {
                Map map = super.toMap();
                return map;
        }
}