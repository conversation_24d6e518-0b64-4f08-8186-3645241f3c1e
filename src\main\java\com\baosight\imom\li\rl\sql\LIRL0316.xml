<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-03-11 11:04:15
   		Version :  1.0
		tableName :meli.tlirl0316
		 SEG_NO  VARCHAR   NOT NULL,
		 UNIT_CODE  VARCHAR   NOT NULL,
		 STATUS  VARCHAR   NOT NULL,
		 VOUCHER_NUM  VARCHAR   NOT NULL,
		 VEHICLE_NO  VARCHAR   NOT NULL,
		 DRIVER_NAME  VARCHAR   NOT NULL,
		 DRIVER_TEL  VARCHAR   NOT NULL,
		 DRIVER_IDENTITY  VARCHAR   NOT NULL,
		 ATTACHMENT_CHAPTER_MARK  VARCHAR   NOT NULL,
		 ATTACHMENT_PRINT  VARCHAR   NOT NULL,
		 REC_CREATOR  VARCHAR   NOT NULL,
		 REC_CREATOR_NAME  VARCHAR   NOT NULL,
		 REC_CREATE_TIME  VARCHAR   NOT NULL,
		 REC_REVISOR  VARCHAR   NOT NULL,
		 REC_REVISOR_NAME  VARCHAR   NOT NULL,
		 REC_REVISE_TIME  VARCHAR   NOT NULL,
		 ARCHIVE_FLAG  SMALLINT   NOT NULL,
		 DEL_FLAG  SMALLINT   NOT NULL,
		 REMARK  VARCHAR,
		 SYS_REMARK  VARCHAR,
		 UUID  VARCHAR   NOT NULL   primarykey,
		 TENANT_ID  VARCHAR
	-->
<sqlMap namespace="LIRL0316">

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0316">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
				"segName", <!-- 业务单元简称 -->
				STATUS	as "status",  <!-- 状态(00撤销 10新增 20确认 99 反确认) -->
				VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				DRIVER_NAME	as "driverName",  <!-- 司机姓名 -->
				DRIVER_TEL	as "driverTel",  <!-- 司机手机号 -->
				DRIVER_IDENTITY	as "driverIdentity",  <!-- 司机身份证号 -->
				ATTACHMENT_CHAPTER_MARK	as "attachmentChapterMark",  <!-- 附件加盖发货章标记(0 不加盖，1，加盖) -->
				case
				when ATTACHMENT_PRINT = '10' then '一体机内置A4'
				when ATTACHMENT_PRINT = '20' then '一体机内置针打'
				when ATTACHMENT_PRINT = '30' then '仓库办公室A4'
				when ATTACHMENT_PRINT = '40' then '仓库办公室针打'
				else ' ' end as "attachmentPrint",  <!-- 附件打印机(针式打印机/A4打印机) -->
				REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				( SELECT GROUP_CONCAT(tt.UPLOAD_FILE_PATH SEPARATOR ';')
				FROM MELI.tlirl0312 tt
				WHERE tt.SEG_NO = t.SEG_NO AND tt.RELEVANCE_ID = t.VOUCHER_NUM ) AS `uploadFilePath`, <!-- 文件路径 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId", <!-- 租户ID -->
				PRINT_IP as "printIp"
		FROM meli.tlirl0316 t WHERE 1=1
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlirl0316 WHERE 1=1
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		AND VOUCHER_NUM = #voucherNum#
		AND STATUS = #status#
	</select>

	<!--
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverName">
			DRIVER_NAME = #driverName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverTel">
			DRIVER_TEL = #driverTel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="driverIdentity">
			DRIVER_IDENTITY = #driverIdentity#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attachmentChapterMark">
			ATTACHMENT_CHAPTER_MARK = #attachmentChapterMark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="attachmentPrint">
			ATTACHMENT_PRINT = #attachmentPrint#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlirl0316 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										STATUS,  <!-- 状态(00撤销 10新增 20确认 99 反确认) -->
										VOUCHER_NUM,  <!-- 提单号 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										DRIVER_NAME,  <!-- 司机姓名 -->
										DRIVER_TEL,  <!-- 司机手机号 -->
										DRIVER_IDENTITY,  <!-- 司机身份证号 -->
										ATTACHMENT_CHAPTER_MARK,  <!-- 附件加盖发货章标记(0 不加盖，1，加盖) -->
										ATTACHMENT_PRINT,  <!-- 附件打印机(针式打印机/A4打印机) -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
										PRINT_IP
										)
	    VALUES (#segNo#, #unitCode#, #status#, #voucherNum#, #vehicleNo#, #driverName#, #driverTel#, #driverIdentity#, #attachmentChapterMark#,
		#attachmentPrint#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
		#delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#,#printIp#)
	</insert>

	<delete id="delete">
		DELETE FROM meli.tlirl0316 WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlirl0316
		SET
		SEG_NO	= #segNo#,   <!-- 系统账套 -->
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					STATUS	= #status#,   <!-- 状态(00撤销 10新增 20确认 99 反确认) -->
					VOUCHER_NUM	= #voucherNum#,   <!-- 提单号 -->
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->
					DRIVER_NAME	= #driverName#,   <!-- 司机姓名 -->
					DRIVER_TEL	= #driverTel#,   <!-- 司机手机号 -->
					DRIVER_IDENTITY	= #driverIdentity#,   <!-- 司机身份证号 -->
					ATTACHMENT_CHAPTER_MARK	= #attachmentChapterMark#,   <!-- 附件加盖发货章标记(0 不加盖，1，加盖) -->
					ATTACHMENT_PRINT	= #attachmentPrint#,   <!-- 附件打印机(针式打印机/A4打印机) -->
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->
					REMARK	= #remark#,   <!-- 备注 -->
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->
								TENANT_ID	= #tenantId#,  <!-- 租户ID -->
					PRINT_IP	= #printIp#  <!-- 租户ID -->
			WHERE
			UUID = #uuid#
	</update>

	<update id="updateStatus">
		UPDATE meli.tlirl0316
		SET
		STATUS	= #status#,   <!-- 状态(00撤销 10新增 20确认 99 反确认) -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
		DEL_FLAG	= #delFlag#   <!-- 记录删除标记 -->
		WHERE
		UUID = #uuid#
	</update>

	<select id="queryFile" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0312">
		SELECT
		RELEVANCE_ID	as "relevanceId",  <!-- 关联ID -->
		UPLOAD_FILE_PATH as "uploadFilePath",  <!-- 文件路径 -->
		UPLOAD_FILE_NAME as "uploadFileName",  <!-- 文件名称 -->
		UUID as "uuid",
		ATTACHMENT_PRINT as "attachmentPrint"
		FROM meli.tlirl0312 t WHERE 1=1
		AND RELEVANCE_ID = #voucherNum#
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				UUID asc
			</isEmpty>
		</dynamic>

	</select>

	<select id="queryFilePath" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select tlirl0316.VOUCHER_NUM as "voucherNum", tlirl0312.ATTACHMENT_PRINT as "attachmentPrint", tlirl0312.UPLOAD_FILE_PATH as "uploadFilePath"
		from meli.tlirl0316 tlirl0316
		left join meli.tlirl0312 on
		tlirl0316.SEG_NO = tlirl0312.SEG_NO
		and tlirl0312.RELEVANCE_ID in (tlirl0316.VOUCHER_NUM)
		where tlirl0316.SEG_NO = #segNo#
		<isNotEmpty prepend="and" property="voucherNum">
			tlirl0316.VOUCHER_NUM IN
			<iterate open="(" close=")" conjunction="," property="voucherNum">
				#voucherNum[]#
			</iterate>
		</isNotEmpty>

	</select>

</sqlMap>