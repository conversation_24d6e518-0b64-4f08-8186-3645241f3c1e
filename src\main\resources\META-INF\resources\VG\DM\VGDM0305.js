$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();

    // 创建时间默认当天查询条件
    const now = new Date();
    $("#inqu_status-0-recCreateTimeStart").val(DateUtils.format(now, "yyyy-MM-dd"));
    $("#inqu_status-0-recCreateTimeEnd").val(DateUtils.format(now, "yyyy-MM-dd"));
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();
});
