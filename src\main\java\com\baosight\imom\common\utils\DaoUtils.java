package com.baosight.imom.common.utils;

import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.data.ibatis.dao.SqlMapDao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.vg.dm.domain.CheckStatus;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;

/**
 * 这里填写描述
 * 填写修改说明
 *
 * <AUTHOR>
 * @version Revision:v1.0
 * @since Date:2022-09-28 16:52
 */
public class DaoUtils {
    private static final Logger LOG = LoggerFactory.getLogger(DaoUtils.class);
    public static int PAGE_SIZE = SqlMapDao.MAX_QUERY_COUNT;

    /**
     * 根据平台要求每次最多 1000 条
     * 分页查询全部数据
     * */
    @SuppressWarnings("all")
    public static List queryAllByPage(Dao dao, String sqlId, Map param){
        LOG.info("分页查询全部-开始:" + sqlId + " | " + param);
        int total = dao.count(sqlId, param);
        List allData = new ArrayList(total);
        int page = (int) Math.ceil((double) total / PAGE_SIZE);
        LOG.info("分页查询全部-查询:" + sqlId + " | 共 " + total + " 笔记录, " + page + " 页");
        for (int i = 0; i < page; i++) {
            int offset = i * PAGE_SIZE;
            int limit = PAGE_SIZE;
            List list = dao.query(sqlId, param, offset, limit);
            allData.addAll(list);
        }
        return allData;
    }

    /**
     * 分页查询 - 不 count
     * */
    @SuppressWarnings("all")
    public static List queryAll(Dao dao, String sqlId, Map param){
        LOG.info("分页查询全部-开始:" + sqlId + " | " + param);
        List allData = new ArrayList();
        int i = 0;
        boolean next = false;
        do {
            LOG.info("分页查询全部-查询 " + sqlId + ", 第 " + (i + 1) + "页.");
            int offset = i * PAGE_SIZE;
            int limit = PAGE_SIZE;
            List list = dao.query(sqlId, param, offset, limit);
            allData.addAll(list);
            next = (list != null && list.size() == limit) ? true : false;
            i++;
        } while (next);
        LOG.info("分页查询全部-查询 " + sqlId + ", 共 " + allData.size() + " 笔记录.");
        return allData;
    }

    /**
     * 查询单个结果
     *
     * @param dao         dao
     * @param sqlId       sqlId
     * @param param       参数
     * @param resultClass 返回数据类型
     * @param <T>         返回数据类型
     * @return T 类型对象或者 null
     */
    @SuppressWarnings("all")
    public static <T> T queryOne(Dao dao, String sqlId, Map param, Class<T> resultClass, T defaultVal) {
        List<T> list = dao.query(sqlId, param);
        if (list != null) {
            if (list.size() == 1) {
                return list.get(0) == null ? defaultVal : list.get(0);
            }
            if (list.size() > 1){
                throw new PlatException("期望一行, 返回多行记录:" + sqlId + " | " + param);
            }
        }
        return defaultVal;
    }

    /**
     * 查询返回 map
     * */
    @SuppressWarnings("all")
    public static Map queryMap(Dao dao, String sqlId, Map param){
        return queryOne(dao, sqlId, param, Map.class, null);
    }

    /**
     * 查询返回 BigDecimal
     * */
    @SuppressWarnings("all")
    public static BigDecimal queryBigDecimal(Dao dao, String sqlId, Map param){
        return queryOne(dao, sqlId, param, BigDecimal.class, null);
    }
    @SuppressWarnings("all")
    public static BigDecimal queryBigDecimal(Dao dao, String sqlId, Map param, BigDecimal defaultVal){
        return queryOne(dao, sqlId, param, BigDecimal.class, defaultVal);
    }

    /**
     * 查询返回 Integer
     * */
    @SuppressWarnings("all")
    public static Integer queryInt(Dao dao, String sqlId, Map param){
        return queryOne(dao, sqlId, param, Integer.class, null);
    }

    @SuppressWarnings("all")
    public static Long queryLong(Dao dao, String sqlId, Map param){
        return queryOne(dao, sqlId, param, Long.class, null);
    }

    @SuppressWarnings("all")
    public static String queryString(Dao dao, String sqlId, Map param){
        return queryOne(dao, sqlId, param, String.class, null);
    }

    @SuppressWarnings("all")
    public static int insertBatch(Dao dao, String sqlId, List dataList) {
        try {
            if (dataList != null && !dataList.isEmpty()) {
                LogUtils.log("批量写入开始, sqlId: " + sqlId + " size: " + dataList.size());
                LOG.info("批量写入开始, sqlId: " + sqlId + " size: " + dataList.size());
                return dao.insertBatch(sqlId, dataList);
            }
        } catch (Exception e) {
            LOG.error("批量写入失败:" + sqlId + " | " + JSON.toJSONString(dataList));
            throw e;
        }
        return 0;
    }

    @SuppressWarnings("all")
    public static int updateBatch(Dao dao, String sqlId, List dataList){
        try {
            if (dataList != null && !dataList.isEmpty()){
                LogUtils.log("批量修改开始, sqlId: " + sqlId + " size: " + dataList.size());
                LOG.info("批量修改开始, sqlId: " + sqlId + " size: " + dataList.size());
                return dao.updateBatch(sqlId, dataList);
            }
        } catch (Exception e){
            LOG.error("批量修改失败:" + sqlId + " | " + JSON.toJSONString(dataList));
            throw e;
        }
        return 0;
    }

    /**
     * 字符串转为 in sql格式
     * S01,Z01,S02,Z02  ===>  'S01','Z01','S02','Z02'
     * */
    public static String splitInQuery(String str){
        if (str == null){
            return null;
        }
        if ("".equals(str)){
            return str;
        }
        StringBuilder strBuilder = new StringBuilder();
        String[] splits = str.split(",");
        for (String split : splits) {
            if (strBuilder.length() > 0){
                strBuilder.append(",");
            }
            strBuilder.append("'").append(split).append("'");
        }
        return strBuilder.toString();
    }

    /**
     * 指定属性, 调用 splitInQuery() 转为 in sql 格式
     * */
    public static EiInfo propToSplitQuery(EiInfo eiInfo, String block, String prop){
        String originVal = eiInfo.getCellStr(block, 0, prop);
        if (null == originVal){
            return eiInfo;
        }
        String inQueryVal = splitInQuery(originVal);
        eiInfo.setCell(block, 0, prop, inQueryVal);
        return eiInfo;
    }

    /**
     * 在新事务中执行方法
     *
     * @param consumer 自定义事件
     * @param eiInfo   eiInfo
     */
    public static void executeNewTx(Consumer<EiInfo> consumer, EiInfo eiInfo){
        PlatformTransactionManager transactionManager = null;
        Map<String, PlatformTransactionManager> beansOfType = PlatApplicationContext.getBeansOfType(PlatformTransactionManager.class);
        if (beansOfType.size() > 0){
            Set<String> keySet = beansOfType.keySet();
            for (String key : keySet) {
                transactionManager = beansOfType.get(key);
            }
        }

        if (transactionManager != null){
            TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW));
            consumer.accept(eiInfo);
            transactionManager.commit(transaction);
        } else {
            consumer.accept(eiInfo);
        }
    }

    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereNewStatusAdded(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        //TODO 使用全局变量
        if (!MesConstant.Status.K10.equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_NEWLY_ADDED_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }


    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereNewStatusAdded1(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        //TODO 使用全局变量
        if (MesConstant.Status.K20.equals(status)||MesConstant.Status.K99.equals(status)||MesConstant.Status.K00.equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_NEWLY_ADDED_AND_ENABLE_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereConfirmStatusAdded(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        //TODO
        if (!"20".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_EXAMINE_DIS_ENABLE_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo whetherToUse(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        //TODO
        if (!"20".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_WHETHER_TO_USE;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereConfirmStatusConfirmNo(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        if (!"20".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_CONFIRM_STATUS_CONFRIM_NO;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }


    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereConfirmStatusEanbled(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        if (!"20".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_EXAMINE_ENABLE_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereConfirmStatusEanbled1(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        if (!"30".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_EXAMINE_ENABLE_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereConfirmStatusEanbledDisabled(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        if ("10".equals(status)||"30".equals(status)||"00".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_EXAMINE_ENABLE_DISABLED_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }


    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereConfirmStatusEanbledDisabled1(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        if ("20".equals(status)||"00".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_EXAMINE_INSERT_DISABLED_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }


    /**
     *
     * @param bean 查询出的数据
     * @param inInfo   eiInfo
     */
    public static EiInfo isThereEnabledStatusDisabled(EiInfo inInfo, DaoEPBase bean) {
        Map map = bean.toMap();
        String status = MapUtils.getString(map, "status", "");
        if (!"30".equals(status)) {
            String massage = MessageCodeConstant.errorMessage.MSG_ERROR_DISABLED_STATUS;
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        return inInfo;
    }

    /**
     * 校验查询条件中业务单元和帐套信息是否为空
     *
     * @param info 查询条件
     * @return true/false
     */
    public static boolean isEmptyUnit(EiInfo info) {
        String unitCode = info.getCellStr(EiConstant.queryBlock, 0, "unitCode");
        String segNo = info.getCellStr(EiConstant.queryBlock, 0, "segNo");
        if (StrUtil.isBlank(unitCode) || StrUtil.isBlank(segNo)) {
            // 更新状态信息用于返回前端
            info.setStatus(EiConstant.STATUS_FAILURE);
            info.setMsg(MessageCodeConstant.errorMessage.MSG_ERROR_EMPTY_UNIT);
            return true;
        }
        return false;
    }

    /**
     * 比较状态值是否匹配
     * 
     * @param errorMsg 错误提示信息,为空时使用默认提示
     * @param sourceStr 待比较的状态值
     * @param targetStr 目标状态值数组
     * @throws PlatException 当状态值不匹配时抛出异常
     */
    public static void compareStr(String errorMsg, String sourceStr, String... targetStr) {
        // 参数校验
        if (StrUtil.isBlank(sourceStr)) {
            throw new PlatException("sourceStr不能为空");
        }
        if (targetStr == null || targetStr.length == 0) {
            throw new PlatException("targetStr不能为空");
        }        
        // 设置默认错误信息
        String msg = StrUtil.isBlank(errorMsg) ? "数据状态不一致" : errorMsg;        
        // 使用 Arrays.asList().contains() 判断状态是否匹配
        if (!Arrays.asList(targetStr).contains(sourceStr)) {
            throw new PlatException(msg);
        }
    }

    /**
     * 查询数据并校验状态
     * 
     * @param dao dao 
     * @param source 查询数据
     * @param errorMsg 错误信息
     * @param targetStatus 目标状态值数组
     * @return 数据库中数据
     */
    public static <T extends CheckStatus> T queryAndCheckStatus(Dao dao, T source, String errorMsg, String... targetStatus) {
        if (StrUtil.isBlank(source.getUuid())) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
        }
        Map<String, String> map = new HashMap<>();
        map.put("uuid", source.getUuid());
        map.put("delFlag", "0");
        List list = dao.query(source.getQuerySqlId(), map);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
        }
        T dbData = (T) list.get(0);     
        // 比较状态值是否匹配
        compareStr(errorMsg, dbData.getCheckStatus(), targetStatus);
        return dbData;
    }
}
