package com.baosight.imom.me.dv.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.common.utils.CodeValueUtils;

import java.util.ArrayList;
import java.util.List;

public class ServiceMEDV01 extends ServiceBase {


    public EiInfo calu(EiInfo eiInfo){

        List<String> list = new ArrayList<>();
        list.add("TEST_TYPE1");
        list.add("TEST_TYPE2");

        EiInfo outInfo = CodeValueUtils.queryToBlock("QL00000",list,eiInfo);

        //List<Map<String,Object>> test = CodeValueUtils.queryCodeValue("QL00000","TEST_TYPE1");

        return outInfo;

    }

}
