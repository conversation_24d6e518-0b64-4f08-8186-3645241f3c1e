/**
* Generate time : 2024-08-14 14:07:59
* Version : 1.0
*/
package com.baosight.imom.common.xt.domain;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tvzbm81
* 
*/
public class Tvzbm81 extends DaoEPBase {

                private String id = " ";		/* uuid*/
                private String segNo = " ";		/* 业务单元编码*/
                private String segFullName = " ";		/* 业务单元名称*/
                private String fatherSegNo = " ";		/* 上级业务单元*/
                private String segName = " ";		/* 业务单元简称*/
                private String orgCode = " ";		/* 对应EHR组织机构码*/
                private String companyCode = " ";		/* 公司别代码（法人单位编码）,基于公司别扩充*/
                private String ifVirtualOrg = " ";		/* 是否虚拟组织,0真实机构/1虚拟机构*/
                private String subPart1 = " ";		/* 预留分段一,参与业务单元代码编码*/
                private String corpType = " ";		/* 法人单位分类*/
                private String segStatus = " ";		/* 业务单元状态：00作废,10锁定(预留),20正常*/
                private String spareAttribute = " ";		/* 备用属性*/
                private String integrationSegNo = " ";		/* 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码*/
                private String accountSet = " ";		/* 标财帐套号,法人业务单元必填*/
                private String userNum = " ";		/* 集团统一客商编码*/
                private String regionalIntegrationSegNo = " ";		/* 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码*/
                private String factoryType = " ";		/* 加工中心细分类,00-非加工中心,10核心工厂*/
                private String remark = " ";		/* 备注*/
                private String related6SegNo = " ";		/* 所属6级业务单元代码*/
                private String related3SegNo = " ";		/* 所属3级业务单元代码*/
                private String related2SegNo = " ";		/* 所属2级业务单元代码*/
                private String related1SegNo = " ";		/* 所属1级业务单元代码*/
                private String related0SegNo = " ";		/* 所属0级业务单元代码*/
                private String corpFlag = " ";		/* 法人单位标志 0否 1是*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人*/
                private String recRevisorName = " ";		/* 记录修改人姓名*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private String archiveFlag = " ";		/* 记录归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记(默认0 删除1)*/
                private String tenantUser = " ";		/* 租户*/
                private String unitCode = " ";		/* 业务单元代码*/
                private String segEStart = " ";		/* 有效期起始*/
                private String segEEnd = " ";		/* 有效期截止*/
                private String segLevel = " ";		/* 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级*/
                private String sortId = " ";		/* 排序号*/
                private String subSortId = " ";		/* 子排序号*/
                private String orgType = " ";		/* 机构分类*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("id");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("业务单元编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segFullName");
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("fatherSegNo");
        eiColumn.setDescName("上级业务单元");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orgCode");
        eiColumn.setDescName("对应EHR组织机构码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("companyCode");
        eiColumn.setDescName("公司别代码（法人单位编码）,基于公司别扩充");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifVirtualOrg");
        eiColumn.setDescName("是否虚拟组织,0真实机构/1虚拟机构");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("subPart1");
        eiColumn.setDescName("预留分段一,参与业务单元代码编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("corpType");
        eiColumn.setDescName("法人单位分类");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segStatus");
        eiColumn.setDescName("业务单元状态：00作废,10锁定(预留),20正常");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spareAttribute");
        eiColumn.setDescName("备用属性");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("integrationSegNo");
        eiColumn.setDescName("一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("accountSet");
        eiColumn.setDescName("标财帐套号,法人业务单元必填");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("userNum");
        eiColumn.setDescName("集团统一客商编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("regionalIntegrationSegNo");
        eiColumn.setDescName("区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryType");
        eiColumn.setDescName("加工中心细分类,00-非加工中心,10核心工厂");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("related6SegNo");
        eiColumn.setDescName("所属6级业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("related3SegNo");
        eiColumn.setDescName("所属3级业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("related2SegNo");
        eiColumn.setDescName("所属2级业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("related1SegNo");
        eiColumn.setDescName("所属1级业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("related0SegNo");
        eiColumn.setDescName("所属0级业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("corpFlag");
        eiColumn.setDescName("法人单位标志 0否 1是");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("记录归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记(默认0 删除1)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segEStart");
        eiColumn.setDescName("有效期起始");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segEEnd");
        eiColumn.setDescName("有效期截止");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segLevel");
        eiColumn.setDescName("业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sortId");
        eiColumn.setDescName("排序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("subSortId");
        eiColumn.setDescName("子排序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orgType");
        eiColumn.setDescName("机构分类");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public Tvzbm81() {
initMetaData();
}

        /**
        * get the id - uuid
        * @return the id
        */
        public String getId() {
        return this.id;
        }

        /**
        * set the id - uuid
        */
        public void setId(String id) {
        this.id = id;
        }
        /**
        * get the segNo - 业务单元编码
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 业务单元编码
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the segFullName - 业务单元名称
        * @return the segFullName
        */
        public String getSegFullName() {
        return this.segFullName;
        }

        /**
        * set the segFullName - 业务单元名称
        */
        public void setSegFullName(String segFullName) {
        this.segFullName = segFullName;
        }
        /**
        * get the fatherSegNo - 上级业务单元
        * @return the fatherSegNo
        */
        public String getFatherSegNo() {
        return this.fatherSegNo;
        }

        /**
        * set the fatherSegNo - 上级业务单元
        */
        public void setFatherSegNo(String fatherSegNo) {
        this.fatherSegNo = fatherSegNo;
        }
        /**
        * get the segName - 业务单元简称
        * @return the segName
        */
        public String getSegName() {
        return this.segName;
        }

        /**
        * set the segName - 业务单元简称
        */
        public void setSegName(String segName) {
        this.segName = segName;
        }
        /**
        * get the orgCode - 对应EHR组织机构码
        * @return the orgCode
        */
        public String getOrgCode() {
        return this.orgCode;
        }

        /**
        * set the orgCode - 对应EHR组织机构码
        */
        public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        }
        /**
        * get the companyCode - 公司别代码（法人单位编码）,基于公司别扩充
        * @return the companyCode
        */
        public String getCompanyCode() {
        return this.companyCode;
        }

        /**
        * set the companyCode - 公司别代码（法人单位编码）,基于公司别扩充
        */
        public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        }
        /**
        * get the ifVirtualOrg - 是否虚拟组织,0真实机构/1虚拟机构
        * @return the ifVirtualOrg
        */
        public String getIfVirtualOrg() {
        return this.ifVirtualOrg;
        }

        /**
        * set the ifVirtualOrg - 是否虚拟组织,0真实机构/1虚拟机构
        */
        public void setIfVirtualOrg(String ifVirtualOrg) {
        this.ifVirtualOrg = ifVirtualOrg;
        }
        /**
        * get the subPart1 - 预留分段一,参与业务单元代码编码
        * @return the subPart1
        */
        public String getSubPart1() {
        return this.subPart1;
        }

        /**
        * set the subPart1 - 预留分段一,参与业务单元代码编码
        */
        public void setSubPart1(String subPart1) {
        this.subPart1 = subPart1;
        }
        /**
        * get the corpType - 法人单位分类
        * @return the corpType
        */
        public String getCorpType() {
        return this.corpType;
        }

        /**
        * set the corpType - 法人单位分类
        */
        public void setCorpType(String corpType) {
        this.corpType = corpType;
        }
        /**
        * get the segStatus - 业务单元状态：00作废,10锁定(预留),20正常
        * @return the segStatus
        */
        public String getSegStatus() {
        return this.segStatus;
        }

        /**
        * set the segStatus - 业务单元状态：00作废,10锁定(预留),20正常
        */
        public void setSegStatus(String segStatus) {
        this.segStatus = segStatus;
        }
        /**
        * get the spareAttribute - 备用属性
        * @return the spareAttribute
        */
        public String getSpareAttribute() {
        return this.spareAttribute;
        }

        /**
        * set the spareAttribute - 备用属性
        */
        public void setSpareAttribute(String spareAttribute) {
        this.spareAttribute = spareAttribute;
        }
        /**
        * get the integrationSegNo - 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码
        * @return the integrationSegNo
        */
        public String getIntegrationSegNo() {
        return this.integrationSegNo;
        }

        /**
        * set the integrationSegNo - 一体化业务单元代码,维护一体化业务单元代码,如天津一体化均维护天津宝钢的业务单元代码,非一体化单位,此处默认为自身业务单元代码
        */
        public void setIntegrationSegNo(String integrationSegNo) {
        this.integrationSegNo = integrationSegNo;
        }
        /**
        * get the accountSet - 标财帐套号,法人业务单元必填
        * @return the accountSet
        */
        public String getAccountSet() {
        return this.accountSet;
        }

        /**
        * set the accountSet - 标财帐套号,法人业务单元必填
        */
        public void setAccountSet(String accountSet) {
        this.accountSet = accountSet;
        }
        /**
        * get the userNum - 集团统一客商编码
        * @return the userNum
        */
        public String getUserNum() {
        return this.userNum;
        }

        /**
        * set the userNum - 集团统一客商编码
        */
        public void setUserNum(String userNum) {
        this.userNum = userNum;
        }
        /**
        * get the regionalIntegrationSegNo - 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码
        * @return the regionalIntegrationSegNo
        */
        public String getRegionalIntegrationSegNo() {
        return this.regionalIntegrationSegNo;
        }

        /**
        * set the regionalIntegrationSegNo - 区域一体化业务单元代码,维护对应区域一体化核心公司的业务单元代码,如非区域一体化管理,默认为自身业务单元代码
        */
        public void setRegionalIntegrationSegNo(String regionalIntegrationSegNo) {
        this.regionalIntegrationSegNo = regionalIntegrationSegNo;
        }
        /**
        * get the factoryType - 加工中心细分类,00-非加工中心,10核心工厂
        * @return the factoryType
        */
        public String getFactoryType() {
        return this.factoryType;
        }

        /**
        * set the factoryType - 加工中心细分类,00-非加工中心,10核心工厂
        */
        public void setFactoryType(String factoryType) {
        this.factoryType = factoryType;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the related6SegNo - 所属6级业务单元代码
        * @return the related6SegNo
        */
        public String getRelated6SegNo() {
        return this.related6SegNo;
        }

        /**
        * set the related6SegNo - 所属6级业务单元代码
        */
        public void setRelated6SegNo(String related6SegNo) {
        this.related6SegNo = related6SegNo;
        }
        /**
        * get the related3SegNo - 所属3级业务单元代码
        * @return the related3SegNo
        */
        public String getRelated3SegNo() {
        return this.related3SegNo;
        }

        /**
        * set the related3SegNo - 所属3级业务单元代码
        */
        public void setRelated3SegNo(String related3SegNo) {
        this.related3SegNo = related3SegNo;
        }
        /**
        * get the related2SegNo - 所属2级业务单元代码
        * @return the related2SegNo
        */
        public String getRelated2SegNo() {
        return this.related2SegNo;
        }

        /**
        * set the related2SegNo - 所属2级业务单元代码
        */
        public void setRelated2SegNo(String related2SegNo) {
        this.related2SegNo = related2SegNo;
        }
        /**
        * get the related1SegNo - 所属1级业务单元代码
        * @return the related1SegNo
        */
        public String getRelated1SegNo() {
        return this.related1SegNo;
        }

        /**
        * set the related1SegNo - 所属1级业务单元代码
        */
        public void setRelated1SegNo(String related1SegNo) {
        this.related1SegNo = related1SegNo;
        }
        /**
        * get the related0SegNo - 所属0级业务单元代码
        * @return the related0SegNo
        */
        public String getRelated0SegNo() {
        return this.related0SegNo;
        }

        /**
        * set the related0SegNo - 所属0级业务单元代码
        */
        public void setRelated0SegNo(String related0SegNo) {
        this.related0SegNo = related0SegNo;
        }
        /**
        * get the corpFlag - 法人单位标志 0否 1是
        * @return the corpFlag
        */
        public String getCorpFlag() {
        return this.corpFlag;
        }

        /**
        * set the corpFlag - 法人单位标志 0否 1是
        */
        public void setCorpFlag(String corpFlag) {
        this.corpFlag = corpFlag;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人姓名
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人姓名
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 记录归档标记
        * @return the archiveFlag
        */
        public String getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 记录归档标记
        */
        public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记(默认0 删除1)
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记(默认0 删除1)
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the tenantUser - 租户
        * @return the tenantUser
        */
        public String getTenantUser() {
        return this.tenantUser;
        }

        /**
        * set the tenantUser - 租户
        */
        public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
        }
        /**
        * get the unitCode - 业务单元代码
        * @return the unitCode
        */
        public String getUnitCode() {
        return this.unitCode;
        }

        /**
        * set the unitCode - 业务单元代码
        */
        public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        }
        /**
        * get the segEStart - 有效期起始
        * @return the segEStart
        */
        public String getSegEStart() {
        return this.segEStart;
        }

        /**
        * set the segEStart - 有效期起始
        */
        public void setSegEStart(String segEStart) {
        this.segEStart = segEStart;
        }
        /**
        * get the segEEnd - 有效期截止
        * @return the segEEnd
        */
        public String getSegEEnd() {
        return this.segEEnd;
        }

        /**
        * set the segEEnd - 有效期截止
        */
        public void setSegEEnd(String segEEnd) {
        this.segEEnd = segEEnd;
        }
        /**
        * get the segLevel - 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级
        * @return the segLevel
        */
        public String getSegLevel() {
        return this.segLevel;
        }

        /**
        * set the segLevel - 业务单元类型:0-公司级,1-营销中心/事业部/分公司级,2-厂/部门级,3-科室级,6-作业区级,7-班组/机组级
        */
        public void setSegLevel(String segLevel) {
        this.segLevel = segLevel;
        }
        /**
        * get the sortId - 排序号
        * @return the sortId
        */
        public String getSortId() {
        return this.sortId;
        }

        /**
        * set the sortId - 排序号
        */
        public void setSortId(String sortId) {
        this.sortId = sortId;
        }
        /**
        * get the subSortId - 子排序号
        * @return the subSortId
        */
        public String getSubSortId() {
        return this.subSortId;
        }

        /**
        * set the subSortId - 子排序号
        */
        public void setSubSortId(String subSortId) {
        this.subSortId = subSortId;
        }
        /**
        * get the orgType - 机构分类
        * @return the orgType
        */
        public String getOrgType() {
        return this.orgType;
        }

        /**
        * set the orgType - 机构分类
        */
        public void setOrgType(String orgType) {
        this.orgType = orgType;
        }
/**
* get the value from Map
*/
public void fromMap(Map map) {

                setId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("id")), id));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setSegFullName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segFullName")), segFullName));
                setFatherSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("fatherSegNo")), fatherSegNo));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setOrgCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orgCode")), orgCode));
                setCompanyCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("companyCode")), companyCode));
                setIfVirtualOrg(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifVirtualOrg")), ifVirtualOrg));
                setSubPart1(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("subPart1")), subPart1));
                setCorpType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("corpType")), corpType));
                setSegStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segStatus")), segStatus));
                setSpareAttribute(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("spareAttribute")), spareAttribute));
                setIntegrationSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("integrationSegNo")), integrationSegNo));
                setAccountSet(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("accountSet")), accountSet));
                setUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("userNum")), userNum));
                setRegionalIntegrationSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("regionalIntegrationSegNo")), regionalIntegrationSegNo));
                setFactoryType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryType")), factoryType));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setRelated6SegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("related6SegNo")), related6SegNo));
                setRelated3SegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("related3SegNo")), related3SegNo));
                setRelated2SegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("related2SegNo")), related2SegNo));
                setRelated1SegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("related1SegNo")), related1SegNo));
                setRelated0SegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("related0SegNo")), related0SegNo));
                setCorpFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("corpFlag")), corpFlag));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
                setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
                setSegEStart(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segEStart")), segEStart));
                setSegEEnd(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segEEnd")), segEEnd));
                setSegLevel(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segLevel")), segLevel));
                setSortId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sortId")), sortId));
                setSubSortId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("subSortId")), subSortId));
                setOrgType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orgType")), orgType));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("id",StringUtils.toString(id, eiMetadata.getMeta("id")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("segFullName",StringUtils.toString(segFullName, eiMetadata.getMeta("segFullName")));
                map.put("fatherSegNo",StringUtils.toString(fatherSegNo, eiMetadata.getMeta("fatherSegNo")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("orgCode",StringUtils.toString(orgCode, eiMetadata.getMeta("orgCode")));
                map.put("companyCode",StringUtils.toString(companyCode, eiMetadata.getMeta("companyCode")));
                map.put("ifVirtualOrg",StringUtils.toString(ifVirtualOrg, eiMetadata.getMeta("ifVirtualOrg")));
                map.put("subPart1",StringUtils.toString(subPart1, eiMetadata.getMeta("subPart1")));
                map.put("corpType",StringUtils.toString(corpType, eiMetadata.getMeta("corpType")));
                map.put("segStatus",StringUtils.toString(segStatus, eiMetadata.getMeta("segStatus")));
                map.put("spareAttribute",StringUtils.toString(spareAttribute, eiMetadata.getMeta("spareAttribute")));
                map.put("integrationSegNo",StringUtils.toString(integrationSegNo, eiMetadata.getMeta("integrationSegNo")));
                map.put("accountSet",StringUtils.toString(accountSet, eiMetadata.getMeta("accountSet")));
                map.put("userNum",StringUtils.toString(userNum, eiMetadata.getMeta("userNum")));
                map.put("regionalIntegrationSegNo",StringUtils.toString(regionalIntegrationSegNo, eiMetadata.getMeta("regionalIntegrationSegNo")));
                map.put("factoryType",StringUtils.toString(factoryType, eiMetadata.getMeta("factoryType")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("related6SegNo",StringUtils.toString(related6SegNo, eiMetadata.getMeta("related6SegNo")));
                map.put("related3SegNo",StringUtils.toString(related3SegNo, eiMetadata.getMeta("related3SegNo")));
                map.put("related2SegNo",StringUtils.toString(related2SegNo, eiMetadata.getMeta("related2SegNo")));
                map.put("related1SegNo",StringUtils.toString(related1SegNo, eiMetadata.getMeta("related1SegNo")));
                map.put("related0SegNo",StringUtils.toString(related0SegNo, eiMetadata.getMeta("related0SegNo")));
                map.put("corpFlag",StringUtils.toString(corpFlag, eiMetadata.getMeta("corpFlag")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("tenantUser",StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
                map.put("unitCode",StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
                map.put("segEStart",StringUtils.toString(segEStart, eiMetadata.getMeta("segEStart")));
                map.put("segEEnd",StringUtils.toString(segEEnd, eiMetadata.getMeta("segEEnd")));
                map.put("segLevel",StringUtils.toString(segLevel, eiMetadata.getMeta("segLevel")));
                map.put("sortId",StringUtils.toString(sortId, eiMetadata.getMeta("sortId")));
                map.put("subSortId",StringUtils.toString(subSortId, eiMetadata.getMeta("subSortId")));
                map.put("orgType",StringUtils.toString(orgType, eiMetadata.getMeta("orgType")));

return map;

}
}