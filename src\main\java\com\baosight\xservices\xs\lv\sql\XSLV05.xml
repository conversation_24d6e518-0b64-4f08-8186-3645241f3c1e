<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XSLV05">
    <select id="query" resultClass="java.util.HashMap">
        <!--<isNotEmpty prepend="" property="loginName">
            <include refid="getAllManagerGroupsByLoginNameIncludeManagerGroups"/>
        </isNotEmpty>-->
        SELECT
        t1.member_id as "memberId",
        t1.parent_id as "parentId",
        t1.member_type as "memberType",
        CASE t1.MEMBER_TYPE
        WHEN 'USER_GROUP' THEN t2.GROUP_CNAME
        ELSE t3.USER_NAME
        END AS "memberName",
        CASE t1.MEMBER_TYPE
        WHEN 'USER_GROUP' THEN t2.GROUP_ENAME
        ELSE t3.LOGIN_NAME
        END AS "memberEname",
        t4.GROUP_ENAME as "parentEname",
        t4.GROUP_CNAME as "parentName",
        t1.sort_index as "sortIndex",
        t1.path as "path",
        t1.rec_creator as "recCreator",
        t1.rec_create_time as "recCreateTime",
        t1.rec_revisor as "recRevisor",
        t1.rec_revise_time as "recReviseTime",
        t1.archive_flag as "archiveFlag"
        FROM ${platSchema}.XS_USER_GROUP_MEMBER t1
        LEFT JOIN ${platSchema}.XS_USER_GROUP t2 ON t1.MEMBER_ID=t2.ID
        LEFT JOIN ${platSchema}.XS_USER t3 ON t1.MEMBER_ID = t3.USER_ID
        LEFT JOIN ${platSchema}.XS_USER_GROUP t4 ON t1.PARENT_ID=t4.ID
        where 1=1
        <isNotEmpty prepend=" AND " property="memberId">
            t1.member_id = #memberId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentId">
            t1.parent_id = #parentId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="memberEname">
            (t2.group_ename like ('%$memberEname$%') or t3.login_name like ('%$memberEname$%'))
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="memberName">
            (t2.group_cname like ('%$memberName$%') or t3.user_name like ('%$memberName$%'))
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentName">
            t4.group_cname like ('%$parentName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentEname">
            t4.group_ename like ('%$parentEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="memberType">
            t1.member_type = #memberType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sqlCondition">
            t3.user_group_ename $sqlCondition$
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="loginName">
            t1.parent_id in (<include refid="getAllManagerGroupsByLoginNameDetailIncludeManagerGroups"/>)
        </isNotEmpty>-->
        ORDER BY t1.SORT_INDEX,t1.MEMBER_ID,t1.PARENT_ID
    </select>

    <select id="count" resultClass="int">
        <!-- <isNotEmpty prepend="" property="loginName">
             <include refid="getAllManagerGroupsByLoginNameIncludeManagerGroups"/>
         </isNotEmpty>-->
        SELECT count(*) FROM ${platSchema}.XS_USER_GROUP_MEMBER t1
        LEFT JOIN ${platSchema}.XS_USER_GROUP t2 ON t1.MEMBER_ID=t2.ID
        LEFT JOIN ${platSchema}.XS_USER t3 ON t1.MEMBER_ID = t3.USER_ID
        LEFT JOIN ${platSchema}.XS_USER_GROUP t4 ON t1.PARENT_ID=t4.ID
        where 1=1
        <isNotEmpty prepend=" AND " property="memberId">
            member_id = #memberId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentId">
            parent_id = #parentId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="memberName">
            (t2.group_cname like ('%$memberName$%') or t3.user_name like ('%$memberName$%'))
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentName">
            t4.group_cname like ('%$parentName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentEname">
            t4.group_ename like ('%$parentEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="memberType">
            member_type = #memberType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sqlCondition">
            t3.user_group_ename $sqlCondition$
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="loginName">
            t1.parent_id in (<include refid="getAllManagerGroupsByLoginNameDetailIncludeManagerGroups"/>)
        </isNotEmpty>-->
    </select>

    <update id="update">
        UPDATE ${platSchema}.XS_USER_GROUP_MEMBER
        <dynamic prepend="set">
            <isNotEmpty prepend="," property="memberType">
                member_type = #memberType#
            </isNotEmpty>
            <isNotEmpty prepend="," property="sortIndex">
                sort_index = #sortIndex#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recCreator">
                rec_creator = #recCreator#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recCreateTime">
                rec_create_time = #recCreateTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recRevisor">
                rec_revisor = #recRevisor#
            </isNotEmpty>
            <isNotEmpty prepend="," property="recReviseTime">
                rec_revise_time = #recReviseTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="archiveFlag">
                archive_flag = #archiveFlag#
            </isNotEmpty>
        </dynamic>
        WHERE member_id = #memberId#
        and parent_id = #parentId#
    </update>

    <delete id="delete">
        DELETE FROM ${platSchema}.XS_USER_GROUP_MEMBER
        WHERE member_id = #memberId#
        and parent_id = #parentId#
    </delete>

    <insert id="insert">
        INSERT INTO ${platSchema}.XS_USER_GROUP_MEMBER (
        MEMBER_ID,
        PARENT_ID,
        MEMBER_TYPE,
        SORT_INDEX,
        PATH,
        REC_CREATOR,
        REC_CREATE_TIME,
        REC_REVISOR,
        REC_REVISE_TIME,
        ARCHIVE_FLAG
        ) VALUES (
        #memberId#,
        #parentId#,
        #memberType#,
        #sortIndex#,
        #path#,
        #recCreator#,
        #recCreateTime#,
        #recRevisor#,
        #recReviseTime#,
        #archiveFlag#
        )
    </insert>

    <sql id="queryForMemberUsersP1">
        select
        t5.user_id as "userId",
        t5.login_name as "loginName",
        t5.user_name as "userName",
        t5.USER_GROUP_ENAME as "userGroupEname"
        from ${platSchema}.TXSLV03 t1
        join ${platSchema}.TXSHR01 t2 on t1.HROG_CODE = t2.ORG_CODE
        join ${platSchema}.TXSOG01 t3 on t2.ORG_CODE = t3.ORG_ENAME
        join ${platSchema}.TXSOG02 t4 on t3.ORG_ID = t4.ORG_ID
        join ${platSchema}.XS_USER t5 on t4.USER_ID = t5.USER_ID
        join ${platSchema}.TXSOG03 t6 on t6.ORG_ID = t1.ORG_ID
        join ${platSchema}.XS_USER_GROUP t7 on t7.ID = t6.USER_GROUP_ID
        where 1=1  and t5.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="insertUserParentId">
            t5.USER_ID NOT IN (SELECT DISTINCT MEMBER_ID
            FROM ${platSchema}.XS_USER_GROUP_MEMBER
            WHERE MEMBER_TYPE = 'USER' and PARENT_ID = #insertUserParentId# and MEMBER_ID is not null)
            AND t7.ID =  #insertUserParentId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            t5.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t5.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t5.login_name like ('%$loginName$%')
        </isNotEmpty>
    </sql>

    <sql id="queryForMemberUsersP2">
        select
        t8.user_id as "userId",
        t8.login_name as "loginName",
        t8.user_name as "userName",
        t8.USER_GROUP_ENAME as "userGroupEname"
        from ${platSchema}.XS_USER t8
        join ${platSchema}.TXSOG02 t9 on t8.USER_ID = t9.USER_ID
        join ${platSchema}.TXSOG03 t10 on t9.ORG_ID = t10.ORG_ID
        join ${platSchema}.XS_USER_GROUP t11 on t11.ID = t10.USER_GROUP_ID
        where 1=1  and t8.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="insertUserParentId">
            t8.USER_ID NOT IN (SELECT DISTINCT MEMBER_ID
            FROM ${platSchema}.XS_USER_GROUP_MEMBER
            WHERE MEMBER_TYPE = 'USER' and PARENT_ID = #insertUserParentId# and MEMBER_ID is not null)
            AND t11.ID =  #insertUserParentId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            t8.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t8.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t8.login_name like ('%$loginName$%')
        </isNotEmpty>
    </sql>

    <sql id="queryForMemberUsersP3">
        select
        t15.user_id as "userId",
        t15.login_name as "loginName",
        t15.user_name as "userName",
        t15.USER_GROUP_ENAME as "userGroupEname"
        from ${platSchema}.TXSLV03 t12
        join ${platSchema}.TXSHR01 t13 on t12.HROG_CODE = t13.ORG_CODE
        join ${platSchema}.TXSHR04 t14 on t13.ORG_CODE = t14.ORG_CODE
        join ${platSchema}.XS_USER t15 on t14.EMP_CODE = t15.LOGIN_NAME
        join ${platSchema}.TXSOG03 t16 on t12.ORG_ID = t16.ORG_ID
        join ${platSchema}.XS_USER_GROUP t17 on t17.ID = t16.USER_GROUP_ID
        where 1=1  and t15.login_name != 'admin'
        <isNotEmpty prepend=" AND " property="insertUserParentId">
            t15.USER_ID NOT IN (SELECT DISTINCT MEMBER_ID
            FROM ${platSchema}.XS_USER_GROUP_MEMBER
            WHERE MEMBER_TYPE = 'USER' and PARENT_ID = #insertUserParentId# and MEMBER_ID is not null)
            AND t17.ID =  #insertUserParentId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            t15.user_id = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t15.user_name like ('%$userName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            t15.login_name like ('%$loginName$%')
        </isNotEmpty>
    </sql>

    <select id="queryForMemberUsers" resultClass="java.util.HashMap">
        <include refid="queryForMemberUsersP1"/>
        union
        <include refid="queryForMemberUsersP2"/>
        union
        <include refid="queryForMemberUsersP3"/>
        order by "loginName"
    </select>

    <select id="queryForMemberUsersNew" resultClass="java.util.HashMap">
        select
        t9.SEG_NO as "segNo",
        t9.SEG_NAME as "segName",
        t8.user_id as "userId",
        t8.login_name as "loginName",
        t8.user_name as "userName",
        t8.USER_GROUP_ENAME as "userGroupEname"
        from ${platSchema}.XS_USER t8
        left join ${platSchema}.xs_user_segno t9 on t8.USER_ID = t9.USER_ID
        where 1=1
          and   t8.status = '1'
        <isNotEmpty prepend=" AND " property="segNo">
            t9.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            t8.USER_NAME like concat('%',#userName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            t8.USER_ID = like concat('%',#userId#,'%')
        </isNotEmpty>


    </select>

    <select id="countForMemberUsers" resultClass="java.util.HashMap">
        select
        count(1)
        from
        (
        <include refid="queryForMemberUsersP1"/>
        union
        <include refid="queryForMemberUsersP2"/>
        union
        <include refid="queryForMemberUsersP3"/>
        )
    </select>
</sqlMap>
