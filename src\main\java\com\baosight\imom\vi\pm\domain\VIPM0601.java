/**
 * Generate time : 2022-08-01 15:41:11
 * Version : 1.0
 */
package com.baosight.imom.vi.pm.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * VIPM0601
 * table comment : 生产工单实绩表
 */
public class VIPM0601 extends DaoEPBase {
    public static final String QUERY = "VIPM0601.query";
    public static final String INSERT_PRODUCTION = "VIPM0601.insertComplementDiffBundle";
    public static final String UPDATE_STATUS = "VIPM0601.updateStatus";

    public static final String QUERY_CONFIRMING_RESULT = "VIPM0601.queryConfirmingResult";
    public static final String QUERY_CONFIRMING_RESULTHIS = "VIPM0601.queryConfirmingResultHis";

    public static final String QUERY_PROCESS_RESULT_PACK = "VIPM0601.queryProcessResultPack";
    public static final String QUERY_PROCESS_RESULT_PACKGF = "VIPM0601.queryProcessResultPackGF";
    public static final String QUERY_PROCESS_RESULT_INFO_MAP = "VIPM0601.queryProcessResultInfoMap";
    public static final String UPDATE_TG_PROCESS_RESULT_REVOKE = "VIPM0601.updateTgProcessResultRevoke";
    public static final String UPDATE_PROCESS_ORDER_PERFOMANCE = "VIPM0601.updateProcessOrderPerfomance";
    public static final String UPDATE_TG_PROCESS_RESULT_REVOKES = "VIPM0601.updateTgProcessResultRevokes";
    public static final String QUERY_FINISHED_BALES = "VIPM0601.queryFinishedBales";
    public static final String QUERY_INVENTORY_LABEL_INFORMATION = "VIPM0601.queryInventoryLabelInformation";

    public static final String IF_PROCESS_RESULT_NOT_RESERVED_BIT = "IF_PROCESS_RESULT_NOT_RESERVED_BIT";
    public static final String IF_SL_AUTO_NET_EIGHT = "IF_SL_AUTO_NET_EIGHT";

    public static final String QUERINQU_STATUS = "querinqu_status";
    public static final String MATERIAL_PACK = "materialPack";
    public static final String PRODUCT_RESULT = "productResult";


    private String segNo = "";        /* 系统账套*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = "";        /* 业务单元代码*/
    private String packId = "";        /* 捆包号*/
    private String factoryOrderNum = "";        /* 钢厂订单号*/
    private String packType = "";        /* 捆包类型*/
    private String partId = "";        /* 物料号*/
    private String packingTypeCode = "";        /* 包装方式代码*/
    private String packingTypeName = "";        /* 包装方式名称*/
    private BigDecimal netWeight = new BigDecimal(0);        /* 净重*/
    private BigDecimal grossWeight = new BigDecimal(0);        /* 毛重*/
    private Integer quantity = Integer.valueOf(0);        /* 数量*/
    private String processOrderId = "";        /* 生产工单号*/
    private String processDemandId = "";        /* 生产需求单号*/
    private String processDemandSubId = "";        /* 生产需求单子项号*/
    private String productDemandId = "";        /* 成品需求单号*/
    private String teamId = "";        /* 班组*/
    private String workingShift = "";        /* 班次*/
    private String processCategory = "";        /* 工序大类代码*/
    private String machineCode = "";        /*机组代码 */
    private String qualityGrade = "";        /* 质量等级*/
    private String scrapType = "";        /* 利用材种类*/
    private BigDecimal processHour = new BigDecimal(0);        /* 加工工时*/
    private String prodNameCode = "";        /* 品名代码*/
    private String prodCname = "";        /* 品名（中文）*/
    private String shopsign = "";        /* 牌号*/
    private String specsDesc = "";        /* 规格描述*/
    private String unitedFlag = "";        /* 并包标记*/
    private String unitedPackId = "";        /* 并包号*/
    private String quantityUnit = "";        /* 数量单位*/
    private String weightUnit = "";        /* 重量单位*/
    private String businessType = "";        /* 业务类型*/
    private String processResultStatus = "";        /* 生产实绩状态*/
    private String remark = "";        /* 备注*/
    private String operateBatchNo = "";        /* 操作批次号*/
    private String contractNum = "";        /* 销售订单号*/
    private String orderNum = "";        /* 销售订单子项号*/
    private String storeType = "";        /* 存货性质*/
    private BigDecimal gauge = new BigDecimal(0);        /* 厚度*/
    private BigDecimal width = new BigDecimal(0);        /* 宽度*/
    private BigDecimal length = new BigDecimal(0);        /* 长度*/
    private String storeTool = "";        /* 装载工具*/
    private String warehouseCode = "";        /* 仓库代码*/
    private String warehouseName = "";        /* 仓库名称*/
    private String locationId = "";        /* 库位代码*/
    private String locationName = "";        /* 库位名称*/
    private String qualityStatus = "";        /* 质量状态*/
    private String blockReason = "";        /* 封锁原因*/
    private String sysRemark = "";        /* 系统备注*/
    private String labelPrintDate = "";        /* 标签打印时间*/
    private String labelPrintPerson = "";        /* 标签打印用户*/
    private String stampSeqId = "";        /* 冲压实绩批次号*/
    private String ifSample = "";        /* 是否取样*/
    private BigDecimal coverPlateQty = new BigDecimal(0);        /* 盖板数量*/
    private String packingFlag = "";        /* 包装标记*/
    private String packingDate = "";        /* 包装日期*/
    private String packingPerson = "";        /* 包装人员*/
    private String dataSource = "";        /* 数据来源*/
    private String teamReportId = "";        /* 班报序号*/
    private String recCreator = "";        /* 记录创建人*/
    private String recCreatorName = "";        /* 记录创建人姓名*/
    private String recCreateTime = "";        /* 记录创建时间*/
    private String recRevisor = "";        /* 记录修改人*/
    private String recRevisorName = "";        /* 记录修改人姓名*/
    private String recRevisorTime = "";        /* 记录修改人时间*/
    private String archiveFlag = "";        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String tenantUser = "";        /* 租户*/
    private String uuid = "";        /* ID*/
    private String ifWasteMaterialBalanceFinish = " ";        /* 废次材平衡是否已经做 1代表已经做平衡，其他表示没有做平衡*/
    private String wasteMaterialBalanceId = " ";        /* 废次材平衡单据号*/
    private String actPutinTime = " ";        /* 实际入库时间*/
    private String putinId = " ";        /* 入库单号*/
    private String periodId = " ";        /* 会计期间*/
    private String proxyType = " ";        /* 类型*/
    private String ifUploadFinance = " ";        /* 是否上传财务*/
    private String matInnerId = " ";        /* 材料管理号*/
    private String prodCode = " ";        /* 品种代码*/
    private String materialRackClassifyNumber = " ";        /* 料架分类编号*/
    private String materialRackId = " ";        /* 料架号*/
    private String customerId = " ";        /* 客户代码*/
    private String customerName = " ";        /* 客户名称*/
    private String d_userNum = " ";        /* 分户号*/
    private String d_userName = " ";        /* 分户号简称*/
    private String tradeCode = " ";        /* 贸易方式*/
    private String prodTypeId = " ";        /* 品种附属码*/
    private String clAccountMark = " ";        /* 财务对账标记*/

    private BigDecimal p_output = new BigDecimal("0");           /* 成品配比 lpp新增*/
    private String prodTypeDesc = " ";        /* 品种附属码描述*/
    private String salesPersNo = " ";        /* 营销员*/
    private BigDecimal pieceWt = new BigDecimal("0");        /* 单片重*/
    private String labelId = " ";        /* 标签号*/
    private String wlAccountMark = "";/*物流对账标记*/
    private BigDecimal packWeight = new BigDecimal("0");           /* 捆包重量*/
    private String unitName = "";           /* 业务单元名称*/

    private String f_packId = "";        /* 父捆包号*/
    private String f_matInnerId = "";        /* 父捆包号材料管理号*/
    private String m_packId = "";        /* 母捆包号*/
    private String m_matInnerId = "";        /* 母捆包号管理号*/
    private String outPackId = "";/*外部捆包号*/
    private String bigTypeDesc = "";/*利用材种类描述*/
    private String processDemandOutputId = "";/*生产需求产出表单据号*/
    private String processResultPackFrom = "";/*生产实绩捆包来源*/

    private String processBaseNo = " ";        /* 加工基准书号*/
    private String processBaseVersionNum = " ";        /* 版本号*/
    private String orderTypeCode = ""; /*订单性质代码*/
    private String scrapOriginType = ""; /*废次材生产来源*/
    private String contractPartId = ""; /*订单物料号*/
    private String splitOrginPackId = ""; /*分选原始捆包号*/
    private String processConsignUnit = ""; /*加工委托方(股份委托加工来源)*/
    private String manualNo = "";/*手册编号*/
    private String hsId = "";/*海关HS系统编码*/
    private String originalPackId = "";/*原始捆包号*/
    private String handbookId = "";/*手册系统编码*/
    private String m_handbookId = "";/*母手册号*/
    private String f_handbookId = "";/*父手册号*/
    private String cuttingNum = "";/*分切序号*/

    private String recReviseTime = "";        /* 记录修改人时间*/
    private String processResultRelationStatus = "";        /* 捆包表状态*/
    private String surfaceGrade="";//表面等级

    private String finUserId="";//最终用户代码
    private String finUserName="";//最终用户名称

    private String custPartId="";//客户零件号
    private String custPartName="";//客户零件号名称

    private String demandRecNo = " ";		/* 需求识别卡号*/
    private BigDecimal demandRecVersionNum = new BigDecimal("0");		/* 需求识别卡版本号*/
    private String craftCode = "";		/* 工艺单号*/
    private BigDecimal craftVersionNum = new BigDecimal("0");		/* 工艺单版本号*/
    private String processSeqCode = " ";		/* 加工序码*/
    private String producingArea = ""; /* 产地*/
    private String makerNum =  "";/* 制造商代码*/
    private String makerName = "";/* 制造商名称*/
    private String heatNum = "";/* 炉号*/
    private String heatNo = "";/* 炉台号*/
    private String custProviderId = "";/* 客户供料商代码*/
    private String isEntry = "";/* 是否已录入*/
    private String productDesc = "";		/* 产品描述*/

    private String TW_ORIGINAL_PACK_ID = "";/* 拼焊原始捆包号*/
    private BigDecimal ylQuantity = new BigDecimal(0);   /* 原料张数 */
    private String ylPartId = ""; /* 原料物料号 */
    private String entityPutoutDate = ""; /* 出库日期 */
    private String productDate = ""; /* 加工日期 */
    private String processDemandMaterailStatus = "";        /* 生产需求单投料捆包状态*/
    private String processDemandMaterailStatusName = "";        /* 生产需求单投料捆包状态名称*/
    private String rmSpecsDesc = " ";        /* 原料规格*/
    private BigDecimal mNetWeight = new BigDecimal(0);        /* 母捆包重量 */
    private String machineName = "";        /*机组名称 */
    private String unitedPackIdBb = "";        /*并包捆包号 */
    private String providerCode ="";//供应商代码
    private String providerCname ="";//供应商名称

    private String wtMethodCode ="";//计重方式
    private BigDecimal theoryWeight = new BigDecimal(0);//理论重量
    private BigDecimal actScWeight = new BigDecimal(0);//实称重量

    private BigDecimal theoryWeight2 = new BigDecimal(0);//理论重量2

    private BigDecimal outMatOuterDia = new BigDecimal(0);//直径
    private String defectType ="";
    private String lockCode ="";
    private String clientChargePlat ="";//客户料号（东莞用）
    private String clientOrderNum ="";//客户订单号（东莞用）
    private BigDecimal tareWt = new BigDecimal(0);//皮重

    private String propertyResult ="";//性能判定结果(1合格;2不合格;0或者空表示未判定)

    private Integer processSeqId = 0;//加工顺序(特指常熟宝升检化验的工序序号)

    private String aluminumPlateTime = "";		/* 铝板热处理时间*/

    private String customerMaterialNumber = "";//客户材料代码

    private String sendSampleFlag = ""; /* 送样标记*/
    private String openshelfNo = "";		/* 木托架编号*/
    private String woodSeed = "";		/* 木种*/

    private String zWood = "";        /* 纵木横截面*/

    private BigDecimal zWoodLength = new BigDecimal(0);		/* 纵木截面长*/

    private BigDecimal zWoodWidth = new BigDecimal(0);		/* 纵木截面宽*/

    private BigDecimal zWoodActualLength = new BigDecimal(0);		/* 纵木实绩长度*/

    private Integer zWoodQty = Integer.valueOf(0);		/* 纵木根数*/

    private String hWood = "";        /* 横木横截面*/

    private BigDecimal hWoodLength = new BigDecimal(0);		/* 横木截面长*/

    private BigDecimal hWoodWidth = new BigDecimal(0);		/* 横木截面宽*/

    private BigDecimal hWoodActualLength = new BigDecimal(0);		/* 横木实绩长度*/

    private Integer hWoodQty = Integer.valueOf(0);		/* 横木根数*/
    private String limitedWeight  = "";		/* 限制重量*/
    private String othersRemark = "";        /* 其他说明*/
    private String jpDebatchingSign = "";
    private String jpFinishedProductSign = "";


    private String vehicleName = " ";        /*车型名称*/

    private String partsCode = "";		/* 部件编码*/
    private String partsName = "";		/* 部品名称*/
    private String carTypeCode = "";		/* 车型代码*/
    private BigDecimal quota = new BigDecimal(0);		/* 定额(KG)*/

    private String ladingSpotId = "";   /* 起始仓库*/

    private String ponum = "";        /* PO号*/

    private String tailPackageFlag=""; /* 股份加工业务尾包标记*/

    private String materialRackClassifyName=""; /* 料架分类名称*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("materialRackClassifyName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("料架分类名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tailPackageFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("股份加工业务尾包标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sendSampleFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("送样标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processResultRelationStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产实绩关系表状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processResultPackFrom");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产实绩捆包来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("父捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_matInnerId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("父捆包号材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("母捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_matInnerId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("母捆包号管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("业务单元名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("clAccountMark");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("财务对账标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pieceWt");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("单片重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryOrderNum");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("钢厂订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packType");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("捆包类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingTypeCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("包装方式代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingTypeName");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("包装方式名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("netWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("净重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grossWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("毛重");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("tareWt");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("皮重");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(0);
        eiColumn.setDescName("数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processOrderId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产工单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandSubId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("生产需求单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productDemandId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("成品需求单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamId");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("班组");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("workingShift");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("班次");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processCategory");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("工序大类代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineCode");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("机组代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityGrade");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("质量等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapType");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("利用材种类");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processHour");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("加工工时");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodNameCode");
        eiColumn.setFieldLength(7);
        eiColumn.setDescName("品名代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCname");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("品名（中文）");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("shopsign");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specsDesc");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("规格描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("并包标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedPackId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("并包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quantityUnit");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("数量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weightUnit");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("重量单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processResultStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产实绩状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("othersRemark");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("其他说明");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("operateBatchNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("操作批次号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("contractNum");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("销售订单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderNum");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("销售订单子项号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeType");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("存货性质");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("gauge");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("厚度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("width");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("宽度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("length");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("长度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("storeTool");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("装载工具");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("qualityStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("质量状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("blockReason");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("封锁原因");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelPrintDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("标签打印时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelPrintPerson");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("标签打印用户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("stampSeqId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("冲压实绩批次号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifSample");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("是否取样");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("coverPlateQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("盖板数量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("包装标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("包装日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packingPerson");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("包装人员");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dataSource");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("数据来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("teamReportId");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("班报序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("记录修改人时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setFieldLength(32);
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifWasteMaterialBalanceFinish");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("废次材平衡是否已经做 1代表已经做平衡，其他表示没有做平衡");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wasteMaterialBalanceId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("废次材平衡单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actPutinTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("实际入库时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("putinId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("入库单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("periodId");
        eiColumn.setFieldLength(6);
        eiColumn.setDescName("会计期间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("proxyType");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifUploadFinance");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否上传财务");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("matInnerId");
        eiColumn.setFieldLength(80);
        eiColumn.setDescName("材料管理号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodCode");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("品种代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialRackClassifyNumber");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("料架分类编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("materialRackId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("料架号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNum");
        eiColumn.setFieldLength(11);
        eiColumn.setDescName("分户号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userName");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("分户号简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tradeCode");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("贸易方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeId");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("品种附属码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("p_output");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("成品配比");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("prodTypeDesc");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("品种附属码描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("salesPersNo");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("营销员");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("labelId");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("标签号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wlAccountMark");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("物流对账标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("packWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("捆包重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outPackId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("外部捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("bigTypeDesc");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("利用材种类描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandOutputId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("生产需求产出表单据号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processBaseNo");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("加工基准书号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processBaseVersionNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("版本号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("orderTypeCode");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("订单性质代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("scrapOriginType");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("废次材生产来源");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("contractPartId");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("订单物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("splitOrginPackId");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("分选原始捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processConsignUnit");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("加工委托方(股份委托加工来源)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("manualNo");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("手册编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hsId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("海关HS系统编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("originalPackId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("原始捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("手册系统编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("f_handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("父手册号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("m_handbookId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("母手册号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cuttingNum");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("分切序号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("surfaceGrade");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("表面等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("最终用户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("finUserName");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("最终用户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("demandRecNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("需求识别卡号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("demandRecVersionNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("需求识别卡版本号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craftCode");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("工艺单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craftVersionNum");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("工艺单版本号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSeqCode");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("加工序码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartId");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("客户零件号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custPartName");
        eiColumn.setFieldLength(300);
        eiColumn.setDescName("客户零件号名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("producingArea");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("产地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("makerNum");
        eiColumn.setFieldLength(6);
        eiColumn.setDescName("制造商代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("makerName");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("制造商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("heatNum");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("炉号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("heatNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("炉台号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("custProviderId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("客户供料商代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isEntry");
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("isEntry");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productDesc");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("产品描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("TW_ORIGINAL_PACK_ID");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("拼焊原始捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ylQuantity");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("原料张数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ylPartId");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("原料物料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("entityPutoutDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("实物出库时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("productDate");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("加工日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandMaterailStatus");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产需求单投料捆包状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processDemandMaterailStatusName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("生产需求单投料捆包状态名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("rmSpecsDesc");
        eiColumn.setFieldLength(100);
        eiColumn.setDescName("原料规格");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("mNetWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("母捆包重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("machineName");
        eiColumn.setFieldLength(128);
        eiColumn.setDescName("机组名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitedPackIdBb");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("并包捆包号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("providerCode");
        eiColumn.setFieldLength(6);
        eiColumn.setDescName("供应商代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("providerCname");
        eiColumn.setFieldLength(60);
        eiColumn.setDescName("供应商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wtMethodCode");
        eiColumn.setFieldLength(4);
        eiColumn.setDescName("计重方式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("theoryWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("理论重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("theoryWeight2");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("理论重量2");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actScWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("实称重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("outMatOuterDia");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("直径");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("defectType");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("股份封闭原因代码");
        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("lockCode");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("股份缺陷代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("clientChargePlat");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("客户料号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("clientOrderNum");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("客户订单号");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("propertyResult");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("性能判定结果");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("processSeqId");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(5);
        eiColumn.setDescName("加工顺序");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("aluminumPlateTime");
        eiColumn.setFieldLength(17);
        eiColumn.setDescName("铝板热处理时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerMaterialNumber");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("客户材料代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("openshelfNo");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("木托架编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("woodSeed");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("木种");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("zWood");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("纵木横截面");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("zWoodLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("纵木截面长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("zWoodWidth");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("纵木截面宽");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("zWoodActualLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("纵木实绩长度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("zWoodQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(0);
        eiColumn.setDescName("纵木根数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hWood");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("横木横截面");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hWoodLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("横木截面长");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hWoodWidth");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("横木截面宽");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hWoodActualLength");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("横木实绩长度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("hWoodQty");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(0);
        eiColumn.setDescName("横木根数");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("limitedWeight");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("限制长度");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jpDebatchingSign");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("精品退卷标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jpFinishedProductSign");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("精品成品标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleName");
        eiColumn.setDescName("车型名称");
        eiColumn.setFieldLength(500);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partsCode");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("部件编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("partsName");
        eiColumn.setFieldLength(500);
        eiColumn.setDescName("部品名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTypeCode");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("车型代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("quota");
        eiColumn.setType("N");
        eiColumn.setScaleLength(8);
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("定额(KG)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingSpotId");
        eiColumn.setDescName("起始仓库");
        eiColumn.setFieldLength(20);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ponum");
        eiColumn.setFieldLength(50);
        eiColumn.setDescName("PO号");
        eiMetadata.addMeta(eiColumn);
    }



    /**
     * the constructor
     */
    public VIPM0601() {
        initMetaData();
    }

    public String getMaterialRackClassifyName() {
        return materialRackClassifyName;
    }

    public void setMaterialRackClassifyName(String materialRackClassifyName) {
        this.materialRackClassifyName = materialRackClassifyName;
    }

    public String getTailPackageFlag() {
        return tailPackageFlag;
    }

    public void setTailPackageFlag(String tailPackageFlag) {
        this.tailPackageFlag = tailPackageFlag;
    }

    public String getSendSampleFlag() {
        return sendSampleFlag;
    }

    public void setSendSampleFlag(String sendSampleFlag) {
        this.sendSampleFlag = sendSampleFlag;
    }

    public String getRecReviseTime() {
        return recReviseTime;
    }

    public String getLimitedWeight() {
        return limitedWeight;
    }

    public void setLimitedWeight(String limitedWeight) {
        this.limitedWeight = limitedWeight;
    }

    @Override
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    public String getProcessResultRelationStatus() {
        return processResultRelationStatus;
    }

    public void setProcessResultRelationStatus(String processResultRelationStatus) {
        this.processResultRelationStatus = processResultRelationStatus;
    }

    public String getProcessResultPackFrom() {
        return processResultPackFrom;
    }

    public void setProcessResultPackFrom(String processResultPackFrom) {
        this.processResultPackFrom = processResultPackFrom;
    }

    public BigDecimal getmNetWeight() {
        return mNetWeight;
    }

    public void setmNetWeight(BigDecimal mNetWeight) {
        this.mNetWeight = mNetWeight;
    }

    public BigDecimal getTareWt() {
        return tareWt;
    }

    public void setTareWt(BigDecimal tareWt) {
        this.tareWt = tareWt;
    }

    public String getF_packId() {
        return f_packId;
    }

    public void setF_packId(String f_packId) {
        this.f_packId = f_packId;
    }

    public String getF_matInnerId() {
        return f_matInnerId;
    }

    public void setF_matInnerId(String f_matInnerId) {
        this.f_matInnerId = f_matInnerId;
    }

    public String getM_packId() {
        return m_packId;
    }

    public void setM_packId(String m_packId) {
        this.m_packId = m_packId;
    }

    public String getM_matInnerId() {
        return m_matInnerId;
    }

    public void setM_matInnerId(String m_matInnerId) {
        this.m_matInnerId = m_matInnerId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    public String getClAccountMark() {
        return clAccountMark;
    }

    public void setClAccountMark(String clAccountMark) {
        this.clAccountMark = clAccountMark;
    }

    public String getUnitedPackIdBb() {
        return unitedPackIdBb;
    }

    public void setUnitedPackIdBb(String unitedPackIdBb) {
        this.unitedPackIdBb = unitedPackIdBb;
    }

    public String getProviderCode() {
        return providerCode;
    }

    public void setProviderCode(String providerCode) {
        this.providerCode = providerCode;
    }

    public String getProviderCname() {
        return providerCname;
    }

    public void setProviderCname(String providerCname) {
        this.providerCname = providerCname;
    }

    public String getWtMethodCode() {
        return wtMethodCode;
    }

    public void setWtMethodCode(String wtMethodCode) {
        this.wtMethodCode = wtMethodCode;
    }

    public BigDecimal getTheoryWeight() {
        return theoryWeight;
    }

    public void setTheoryWeight(BigDecimal theoryWeight) {
        this.theoryWeight = theoryWeight;
    }

    public BigDecimal getActScWeight() {
        return actScWeight;
    }

    public void setActScWeight(BigDecimal actScWeight) {
        this.actScWeight = actScWeight;
    }

    /**
     * get the segNo - 系统账套
     *
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the packId - 捆包号
     *
     * @return the packId
     */
    public String getPackId() {
        return this.packId;
    }

    /**
     * set the packId - 捆包号
     */
    public void setPackId(String packId) {
        this.packId = packId;
    }

    /**
     * get the factoryOrderNum - 钢厂订单号
     *
     * @return the factoryOrderNum
     */
    public String getFactoryOrderNum() {
        return this.factoryOrderNum;
    }

    /**
     * set the factoryOrderNum - 钢厂订单号
     */
    public void setFactoryOrderNum(String factoryOrderNum) {
        this.factoryOrderNum = factoryOrderNum;
    }

    /**
     * get the packType - 捆包类型
     *
     * @return the packType
     */
    public String getPackType() {
        return this.packType;
    }

    /**
     * set the packType - 捆包类型
     */
    public void setPackType(String packType) {
        this.packType = packType;
    }

    /**
     * get the partId - 物料号
     *
     * @return the partId
     */
    public String getPartId() {
        return this.partId;
    }

    /**
     * set the partId - 物料号
     */
    public void setPartId(String partId) {
        this.partId = partId;
    }

    /**
     * get the packingTypeCode - 包装方式代码
     *
     * @return the packingTypeCode
     */
    public String getPackingTypeCode() {
        return this.packingTypeCode;
    }

    /**
     * set the packingTypeCode - 包装方式代码
     */
    public void setPackingTypeCode(String packingTypeCode) {
        this.packingTypeCode = packingTypeCode;
    }

    /**
     * get the packingTypeName - 包装方式名称
     *
     * @return the packingTypeName
     */
    public String getPackingTypeName() {
        return this.packingTypeName;
    }

    /**
     * set the packingTypeName - 包装方式代码
     */
    public void setPackingTypeName(String packingTypeName) {
        this.packingTypeName = packingTypeName;
    }

    /**
     * get the netWeight - 净重
     *
     * @return the netWeight
     */
    public BigDecimal getNetWeight() {
        return this.netWeight;
    }

    /**
     * set the netWeight - 净重
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * get the grossWeight - 毛重
     *
     * @return the grossWeight
     */
    public BigDecimal getGrossWeight() {
        return this.grossWeight;
    }

    /**
     * set the grossWeight - 毛重
     */
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    /**
     * get the quantity - 数量
     *
     * @return the quantity
     */
    public Integer getQuantity() {
        return this.quantity;
    }

    /**
     * set the quantity - 数量
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * get the processOrderId - 生产工单号
     *
     * @return the processOrderId
     */
    public String getProcessOrderId() {
        return this.processOrderId;
    }

    /**
     * set the processOrderId - 生产工单号
     */
    public void setProcessOrderId(String processOrderId) {
        this.processOrderId = processOrderId;
    }

    /**
     * get the processDemandId - 生产需求单号
     *
     * @return the processDemandId
     */
    public String getProcessDemandId() {
        return this.processDemandId;
    }

    /**
     * set the processDemandId - 生产需求单号
     */
    public void setProcessDemandId(String processDemandId) {
        this.processDemandId = processDemandId;
    }

    /**
     * get the processDemandSubId - 生产需求单子项号
     *
     * @return the processDemandSubId
     */
    public String getProcessDemandSubId() {
        return this.processDemandSubId;
    }

    /**
     * set the processDemandSubId - 生产需求单子项号
     */
    public void setProcessDemandSubId(String processDemandSubId) {
        this.processDemandSubId = processDemandSubId;
    }

    /**
     * get the productDemandId - 成品需求单号
     *
     * @return the productDemandId
     */
    public String getProductDemandId() {
        return this.productDemandId;
    }

    /**
     * set the productDemandId - 成品需求单号
     */
    public void setProductDemandId(String productDemandId) {
        this.productDemandId = productDemandId;
    }

    /**
     * get the teamId - 班组
     *
     * @return the teamId
     */
    public String getTeamId() {
        return this.teamId;
    }

    /**
     * set the teamId - 班组
     */
    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    /**
     * get the workingShift - 班次
     *
     * @return the workingShift
     */
    public String getWorkingShift() {
        return this.workingShift;
    }

    /**
     * set the workingShift - 班次
     */
    public void setWorkingShift(String workingShift) {
        this.workingShift = workingShift;
    }

    /**
     * get the processCategory - 工序大类代码
     *
     * @return the processCategory
     */
    public String getProcessCategory() {
        return this.processCategory;
    }

    /**
     * set the processCategory - 工序大类代码
     */
    public void setProcessCategory(String processCategory) {
        this.processCategory = processCategory;
    }

    /**
     * get the machineCode - 机组代码
     *
     * @return the machineCode
     */
    public String getMachineCode() {
        return this.machineCode;
    }

    /**
     * set the machineCode - 机组代码
     */
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }

    /**
     * get the qualityGrade - 质量等级
     *
     * @return the qualityGrade
     */
    public String getQualityGrade() {
        return this.qualityGrade;
    }

    /**
     * set the qualityGrade - 质量等级
     */
    public void setQualityGrade(String qualityGrade) {
        this.qualityGrade = qualityGrade;
    }

    /**
     * get the scrapType - 利用材种类
     *
     * @return the scrapType
     */
    public String getScrapType() {
        return this.scrapType;
    }

    /**
     * set the scrapType - 利用材种类
     */
    public void setScrapType(String scrapType) {
        this.scrapType = scrapType;
    }

    /**
     * get the processHour - 加工工时
     *
     * @return the processHour
     */
    public BigDecimal getProcessHour() {
        return this.processHour;
    }

    /**
     * set the processHour - 加工工时
     */
    public void setProcessHour(BigDecimal processHour) {
        this.processHour = processHour;
    }

    /**
     * get the prodNameCode - 品名代码
     *
     * @return the prodNameCode
     */
    public String getProdNameCode() {
        return this.prodNameCode;
    }

    /**
     * set the prodNameCode - 品名代码
     */
    public void setProdNameCode(String prodNameCode) {
        this.prodNameCode = prodNameCode;
    }

    /**
     * get the prodCname - 品名（中文）
     *
     * @return the prodCname
     */
    public String getProdCname() {
        return this.prodCname;
    }

    /**
     * set the prodCname - 品名（中文）
     */
    public void setProdCname(String prodCname) {
        this.prodCname = prodCname;
    }

    /**
     * get the shopsign - 牌号
     *
     * @return the shopsign
     */
    public String getShopsign() {
        return this.shopsign;
    }

    /**
     * set the shopsign - 牌号
     */
    public void setShopsign(String shopsign) {
        this.shopsign = shopsign;
    }

    /**
     * get the specsDesc - 规格描述
     *
     * @return the specsDesc
     */
    public String getSpecsDesc() {
        return this.specsDesc;
    }

    /**
     * set the specsDesc - 规格描述
     */
    public void setSpecsDesc(String specsDesc) {
        this.specsDesc = specsDesc;
    }

    /**
     * get the unitedFlag - 并包标记
     *
     * @return the unitedFlag
     */
    public String getUnitedFlag() {
        return this.unitedFlag;
    }

    /**
     * set the unitedFlag - 并包标记
     */
    public void setUnitedFlag(String unitedFlag) {
        this.unitedFlag = unitedFlag;
    }

    /**
     * get the unitedPackId - 并包号
     *
     * @return the unitedPackId
     */
    public String getUnitedPackId() {
        return this.unitedPackId;
    }

    /**
     * set the unitedPackId - 并包号
     */
    public void setUnitedPackId(String unitedPackId) {
        this.unitedPackId = unitedPackId;
    }

    /**
     * get the quantityUnit - 数量单位
     *
     * @return the quantityUnit
     */
    public String getQuantityUnit() {
        return this.quantityUnit;
    }

    /**
     * set the quantityUnit - 数量单位
     */
    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    /**
     * get the weightUnit - 重量单位
     *
     * @return the weightUnit
     */
    public String getWeightUnit() {
        return this.weightUnit;
    }

    /**
     * set the weightUnit - 重量单位
     */
    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    /**
     * get the businessType - 业务类型
     *
     * @return the businessType
     */
    public String getBusinessType() {
        return this.businessType;
    }

    /**
     * set the businessType - 业务类型
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * get the processResultStatus - 生产实绩状态
     *
     * @return the processResultStatus
     */
    public String getProcessResultStatus() {
        return this.processResultStatus;
    }

    /**
     * set the processResultStatus - 生产实绩状态
     */
    public void setProcessResultStatus(String processResultStatus) {
        this.processResultStatus = processResultStatus;
    }

    /**
     * get the remark - 备注
     *
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the operateBatchNo - 操作批次号
     *
     * @return the operateBatchNo
     */
    public String getOperateBatchNo() {
        return this.operateBatchNo;
    }

    /**
     * set the operateBatchNo - 操作批次号
     */
    public void setOperateBatchNo(String operateBatchNo) {
        this.operateBatchNo = operateBatchNo;
    }

    /**
     * get the contractNum - 销售订单号
     *
     * @return the contractNum
     */
    public String getContractNum() {
        return this.contractNum;
    }

    /**
     * set the contractNum - 销售订单号
     */
    public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
    }

    /**
     * get the orderNum - 销售订单子项号
     *
     * @return the orderNum
     */
    public String getOrderNum() {
        return this.orderNum;
    }

    /**
     * set the orderNum - 销售订单子项号
     */
    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    /**
     * get the storeType - 存货性质
     *
     * @return the storeType
     */
    public String getStoreType() {
        return this.storeType;
    }

    /**
     * set the storeType - 存货性质
     */
    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    /**
     * get the gauge - 厚度
     *
     * @return the gauge
     */
    public BigDecimal getGauge() {
        return this.gauge;
    }

    /**
     * set the gauge - 厚度
     */
    public void setGauge(BigDecimal gauge) {
        this.gauge = gauge;
    }

    /**
     * get the width - 宽度
     *
     * @return the width
     */
    public BigDecimal getWidth() {
        return this.width;
    }

    /**
     * set the width - 宽度
     */
    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    /**
     * get the length - 长度
     *
     * @return the length
     */
    public BigDecimal getLength() {
        return this.length;
    }

    /**
     * set the length - 长度
     */
    public void setLength(BigDecimal length) {
        this.length = length;
    }

    /**
     * get the storeTool - 装载工具
     *
     * @return the storeTool
     */
    public String getStoreTool() {
        return this.storeTool;
    }

    /**
     * set the storeTool - 装载工具
     */
    public void setStoreTool(String storeTool) {
        this.storeTool = storeTool;
    }

    /**
     * get the warehouseCode - 仓库代码
     *
     * @return the warehouseCode
     */
    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    /**
     * set the warehouseCode - 仓库代码
     */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    /**
     * get the warehouseName - 仓库名称
     *
     * @return the warehouseName
     */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
     * set the warehouseName - 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * get the locationId - 库位代码
     *
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locationName - 库位名称
     *
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the qualityStatus - 质量状态
     *
     * @return the qualityStatus
     */
    public String getQualityStatus() {
        return this.qualityStatus;
    }

    /**
     * set the qualityStatus - 质量状态
     */
    public void setQualityStatus(String qualityStatus) {
        this.qualityStatus = qualityStatus;
    }

    /**
     * get the blockReason - 封锁原因
     *
     * @return the blockReason
     */
    public String getBlockReason() {
        return this.blockReason;
    }

    /**
     * set the blockReason - 封锁原因
     */
    public void setBlockReason(String blockReason) {
        this.blockReason = blockReason;
    }

    /**
     * get the sysRemark - 系统备注
     *
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the labelPrintDate - 标签打印时间
     *
     * @return the labelPrintDate
     */
    public String getLabelPrintDate() {
        return this.labelPrintDate;
    }

    /**
     * set the labelPrintDate - 标签打印时间
     */
    public void setLabelPrintDate(String labelPrintDate) {
        this.labelPrintDate = labelPrintDate;
    }

    /**
     * get the labelPrintPerson - 标签打印用户
     *
     * @return the labelPrintPerson
     */
    public String getLabelPrintPerson() {
        return this.labelPrintPerson;
    }

    /**
     * set the labelPrintPerson - 标签打印用户
     */
    public void setLabelPrintPerson(String labelPrintPerson) {
        this.labelPrintPerson = labelPrintPerson;
    }

    /**
     * get the stampSeqId - 冲压实绩批次号
     *
     * @return the stampSeqId
     */
    public String getStampSeqId() {
        return this.stampSeqId;
    }

    /**
     * set the stampSeqId - 冲压实绩批次号
     */
    public void setStampSeqId(String stampSeqId) {
        this.stampSeqId = stampSeqId;
    }

    /**
     * get the ifSample - 是否取样
     *
     * @return the ifSample
     */
    public String getIfSample() {
        return this.ifSample;
    }

    /**
     * set the ifSample - 是否取样
     */
    public void setIfSample(String ifSample) {
        this.ifSample = ifSample;
    }

    /**
     * get the coverPlateQty - 盖板数量
     *
     * @return the coverPlateQty
     */
    public BigDecimal getCoverPlateQty() {
        return this.coverPlateQty;
    }

    /**
     * set the coverPlateQty - 盖板数量
     */
    public void setCoverPlateQty(BigDecimal coverPlateQty) {
        this.coverPlateQty = coverPlateQty;
    }

    /**
     * get the packingFlag - 包装标记
     *
     * @return the packingFlag
     */
    public String getPackingFlag() {
        return this.packingFlag;
    }

    /**
     * set the packingFlag - 包装标记
     */
    public void setPackingFlag(String packingFlag) {
        this.packingFlag = packingFlag;
    }

    /**
     * get the packingDate - 包装日期
     *
     * @return the packingDate
     */
    public String getPackingDate() {
        return this.packingDate;
    }

    /**
     * set the packingDate - 包装日期
     */
    public void setPackingDate(String packingDate) {
        this.packingDate = packingDate;
    }

    /**
     * get the packingPerson - 包装人员
     *
     * @return the packingPerson
     */
    public String getPackingPerson() {
        return this.packingPerson;
    }

    /**
     * set the packingPerson - 包装人员
     */
    public void setPackingPerson(String packingPerson) {
        this.packingPerson = packingPerson;
    }

    /**
     * get the dataSource - 数据来源
     *
     * @return the dataSource
     */
    public String getDataSource() {
        return this.dataSource;
    }

    /**
     * set the dataSource - 数据来源
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * get the teamReportId - 班报序号
     *
     * @return the teamReportId
     */
    public String getTeamReportId() {
        return this.teamReportId;
    }

    /**
     * set the teamReportId - 班报序号
     */
    public void setTeamReportId(String teamReportId) {
        this.teamReportId = teamReportId;
    }

    /**
     * get the recCreator - 记录创建人
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     *
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     *
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recRevisorTime - 记录修改人时间
     *
     * @return the recRevisorTime
     */
    public String getRecRevisorTime() {
        return this.recRevisorTime;
    }

    /**
     * set the recRevisorTime - 记录修改人时间
     */
    public void setRecRevisorTime(String recRevisorTime) {
        this.recRevisorTime = recRevisorTime;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     *
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the tenantUser - 租户
     *
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the uuid - ID
     *
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the ifWasteMaterialBalanceFinish - 废次材平衡是否已经做 1代表已经做平衡，其他表示没有做平衡
     *
     * @return the ifWasteMaterialBalanceFinish
     */
    public String getIfWasteMaterialBalanceFinish() {
        return this.ifWasteMaterialBalanceFinish;
    }

    /**
     * set the ifWasteMaterialBalanceFinish - 废次材平衡是否已经做 1代表已经做平衡，其他表示没有做平衡
     */
    public void setIfWasteMaterialBalanceFinish(String ifWasteMaterialBalanceFinish) {
        this.ifWasteMaterialBalanceFinish = ifWasteMaterialBalanceFinish;
    }

    /**
     * get the wasteMaterialBalanceId - 废次材平衡单据号
     *
     * @return the wasteMaterialBalanceId
     */
    public String getWasteMaterialBalanceId() {
        return this.wasteMaterialBalanceId;
    }

    /**
     * set the wasteMaterialBalanceId - 废次材平衡单据号
     */
    public void setWasteMaterialBalanceId(String wasteMaterialBalanceId) {
        this.wasteMaterialBalanceId = wasteMaterialBalanceId;
    }

    /**
     * get the actPutinTime - 实际入库时间
     *
     * @return the actPutinTime
     */
    public String getActPutinTime() {
        return this.actPutinTime;
    }

    /**
     * set the actPutinTime - 实际入库时间
     */
    public void setActPutinTime(String actPutinTime) {
        this.actPutinTime = actPutinTime;
    }

    /**
     * get the putinId - 入库单号
     *
     * @return the putinId
     */
    public String getPutinId() {
        return this.putinId;
    }

    /**
     * set the putinId - 入库单号
     */
    public void setPutinId(String putinId) {
        this.putinId = putinId;
    }

    /**
     * get the periodId - 会计期间
     *
     * @return the periodId
     */
    public String getPeriodId() {
        return this.periodId;
    }

    /**
     * set the periodId - 会计期间
     */
    public void setPeriodId(String periodId) {
        this.periodId = periodId;
    }

    /**
     * get the proxyType - 类型
     *
     * @return the proxyType
     */
    public String getProxyType() {
        return this.proxyType;
    }

    /**
     * set the proxyType - 类型
     */
    public void setProxyType(String proxyType) {
        this.proxyType = proxyType;
    }

    /**
     * get the ifUploadFinance - 是否上传财务
     *
     * @return the ifUploadFinance
     */
    public String getIfUploadFinance() {
        return this.ifUploadFinance;
    }

    /**
     * set the ifUploadFinance - 是否上传财务
     */
    public void setIfUploadFinance(String ifUploadFinance) {
        this.ifUploadFinance = ifUploadFinance;
    }

    /**
     * get the matInnerId - 材料管理号
     *
     * @return the matInnerId
     */
    public String getMatInnerId() {
        return this.matInnerId;
    }

    /**
     * set the matInnerId - 材料管理号
     */
    public void setMatInnerId(String matInnerId) {
        this.matInnerId = matInnerId;
    }

    /**
     * get the prodCode - 品种代码
     *
     * @return the prodCode
     */
    public String getProdCode() {
        return this.prodCode;
    }

    /**
     * set the prodCode - 品种代码
     */
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    /**
     * get the materialRackClassifyNumber - 料架分类编号
     *
     * @return the materialRackClassifyNumber
     */
    public String getMaterialRackClassifyNumber() {
        return this.materialRackClassifyNumber;
    }

    /**
     * set the materialRackClassifyNumber - 料架分类编号
     */
    public void setMaterialRackClassifyNumber(String materialRackClassifyNumber) {
        this.materialRackClassifyNumber = materialRackClassifyNumber;
    }

    /**
     * get the materialRackId - 料架号
     *
     * @return the materialRackId
     */
    public String getMaterialRackId() {
        return this.materialRackId;
    }

    /**
     * set the materialRackId - 料架号
     */
    public void setMaterialRackId(String materialRackId) {
        this.materialRackId = materialRackId;
    }

    /**
     * get the customerId - 客户代码
     *
     * @return the customerId
     */
    public String getCustomerId() {
        return this.customerId;
    }

    /**
     * set the customerId - 客户代码
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * get the customerName - 客户名称
     *
     * @return the customerName
     */
    public String getCustomerName() {
        return this.customerName;
    }

    /**
     * set the customerName - 客户名称
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * get the d_userNum - 分户号
     *
     * @return the d_userNum
     */
    public String getD_userNum() {
        return this.d_userNum;
    }

    /**
     * set the d_userNum - 分户号
     */
    public void setD_userNum(String d_userNum) {
        this.d_userNum = d_userNum;
    }

    /**
     * get the d_userName - 分户号简称
     *
     * @return the d_userName
     */
    public String getD_userName() {
        return this.d_userName;
    }

    /**
     * set the d_userName - 分户号简称
     */
    public void setD_userName(String d_userName) {
        this.d_userName = d_userName;
    }

    public BigDecimal getP_output() {
        return p_output;
    }

    public void setP_output(BigDecimal p_output) {
        this.p_output = p_output;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getProdTypeId() {
        return prodTypeId;
    }

    public void setProdTypeId(String prodTypeId) {
        this.prodTypeId = prodTypeId;
    }

    public String getSalesPersNo() {
        return salesPersNo;
    }

    public void setSalesPersNo(String salesPersNo) {
        this.salesPersNo = salesPersNo;
    }

    public String getProdTypeDesc() {
        return prodTypeDesc;
    }

    public void setProdTypeDesc(String prodTypeDesc) {
        this.prodTypeDesc = prodTypeDesc;
    }

    public void setPieceWt(BigDecimal pieceWt) {
        this.pieceWt = pieceWt;
    }

    public BigDecimal getPieceWt() {
        return pieceWt;
    }

    public String getLabelId() {
        return labelId;
    }

    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }

    public String getWlAccountMark() {
        return wlAccountMark;
    }

    public void setWlAccountMark(String wlAccountMark) {
        this.wlAccountMark = wlAccountMark;
    }

    public BigDecimal getPackWeight() {
        return packWeight;
    }

    public void setPackWeight(BigDecimal packWeight) {
        this.packWeight = packWeight;
    }

    public String getOutPackId() {
        return outPackId;
    }

    public void setOutPackId(String outPackId) {
        this.outPackId = outPackId;
    }

    public String getBigTypeDesc() {
        return bigTypeDesc;
    }

    public void setBigTypeDesc(String bigTypeDesc) {
        this.bigTypeDesc = bigTypeDesc;
    }

    public String getProcessDemandOutputId() {
        return processDemandOutputId;
    }

    public void setProcessDemandOutputId(String processDemandOutputId) {
        this.processDemandOutputId = processDemandOutputId;
    }

    public String getProcessBaseNo() {
        return processBaseNo;
    }

    public void setProcessBaseNo(String processBaseNo) {
        this.processBaseNo = processBaseNo;
    }

    public String getProcessBaseVersionNum() {
        return processBaseVersionNum;
    }

    public void setProcessBaseVersionNum(String processBaseVersionNum) {
        this.processBaseVersionNum = processBaseVersionNum;
    }

    public String getOrderTypeCode() {
        return orderTypeCode;
    }

    public void setOrderTypeCode(String orderTypeCode) {
        this.orderTypeCode = orderTypeCode;
    }

    public String getScrapOriginType() {
        return scrapOriginType;
    }

    public void setScrapOriginType(String scrapOriginType) {
        this.scrapOriginType = scrapOriginType;
    }

    public String getContractPartId() {
        return contractPartId;
    }

    public void setContractPartId(String contractPartId) {
        this.contractPartId = contractPartId;
    }

    public String getSplitOrginPackId() {
        return splitOrginPackId;
    }

    public void setSplitOrginPackId(String splitOrginPackId) {
        this.splitOrginPackId = splitOrginPackId;
    }

    public String getProcessConsignUnit() {
        return processConsignUnit;
    }

    public void setProcessConsignUnit(String processConsignUnit) {
        this.processConsignUnit = processConsignUnit;
    }

    public String getManualNo() {
        return manualNo;
    }

    public void setManualNo(String manualNo) {
        this.manualNo = manualNo;
    }

    public String getOriginalPackId() {
        return originalPackId;
    }

    public void setOriginalPackId(String originalPackId) {
        this.originalPackId = originalPackId;
    }

    public String getHsId() {
        return hsId;
    }

    public void setHsId(String hsId) {
        this.hsId = hsId;
    }

    public String getHandbookId() {
        return handbookId;
    }

    public void setHandbookId(String handbookId) {
        this.handbookId = handbookId;
    }

    public String getM_handbookId() {
        return m_handbookId;
    }

    public void setM_handbookId(String m_handbookId) {
        this.m_handbookId = m_handbookId;
    }

    public String getF_handbookId() {
        return f_handbookId;
    }

    public void setF_handbookId(String f_handbookId) {
        this.f_handbookId = f_handbookId;
    }

    public String getCuttingNum() {
        return cuttingNum;
    }

    public void setCuttingNum(String cuttingNum) {
        this.cuttingNum = cuttingNum;
    }

    public String getSurfaceGrade() {
        return surfaceGrade;
    }

    public void setSurfaceGrade(String surfaceGrade) {
        this.surfaceGrade = surfaceGrade;
    }

    public String getFinUserId() {
        return finUserId;
    }

    public void setFinUserId(String finUserId) {
        this.finUserId = finUserId;
    }

    public String getFinUserName() {
        return finUserName;
    }

    public void setFinUserName(String finUserName) {
        this.finUserName = finUserName;
    }

    public String getDemandRecNo() {
        return demandRecNo;
    }

    public void setDemandRecNo(String demandRecNo) {
        this.demandRecNo = demandRecNo;
    }

    public BigDecimal getDemandRecVersionNum() {
        return demandRecVersionNum;
    }

    public void setDemandRecVersionNum(BigDecimal demandRecVersionNum) {
        this.demandRecVersionNum = demandRecVersionNum;
    }

    public String getCraftCode() {
        return craftCode;
    }

    public void setCraftCode(String craftCode) {
        this.craftCode = craftCode;
    }

    public BigDecimal getCraftVersionNum() {
        return craftVersionNum;
    }

    public void setCraftVersionNum(BigDecimal craftVersionNum) {
        this.craftVersionNum = craftVersionNum;
    }

    public String getProcessSeqCode() {
        return processSeqCode;
    }

    public void setProcessSeqCode(String processSeqCode) {
        this.processSeqCode = processSeqCode;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public String getMakerNum() {
        return makerNum;
    }

    public void setMakerNum(String makerNum) {
        this.makerNum = makerNum;
    }

    public String getMakerName() {
        return makerName;
    }

    public void setMakerName(String makerName) {
        this.makerName = makerName;
    }

    public String getHeatNum() {
        return heatNum;
    }

    public void setHeatNum(String heatNum) {
        this.heatNum = heatNum;
    }

    public String getHeatNo() {
        return heatNo;
    }

    public void setHeatNo(String heatNo) {
        this.heatNo = heatNo;
    }

    public String getCustProviderId() {
        return custProviderId;
    }

    public void setCustProviderId(String custProviderId) {
        this.custProviderId = custProviderId;
    }

    public String getIsEntry() {
        return isEntry;
    }

    public void setIsEntry(String isEntry) {
        this.isEntry = isEntry;
    }
    /**
     * get the productDesc - 产品描述
     * @return the productDesc
     */
    public String getProductDesc() {
        return this.productDesc;
    }

    /**
     * set the productDesc - 产品描述
     */
    public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
    }

    public String getTW_ORIGINAL_PACK_ID() {
        return TW_ORIGINAL_PACK_ID;
    }

    public void setTW_ORIGINAL_PACK_ID(String TW_ORIGINAL_PACK_ID) {
        this.TW_ORIGINAL_PACK_ID = TW_ORIGINAL_PACK_ID;
    }

    /**
     * get the ylQuantity - 原料张数
     * @return the ylQuantity
     */
    public BigDecimal getYlQuantity() {
        return this.ylQuantity;
    }

    /**
     * set the ylQuantity - 原料张数
     */
    public void setYlQuantity(BigDecimal ylQuantity) {
        this.ylQuantity = ylQuantity;
    }

    /**
     * get the ylPartId - 原料物料号
     * @return the ylPartId
     */
    public String getYlPartId() {
        return this.ylPartId;
    }

    /**
     * set the ylPartId - 原料物料号
     */
    public void setYlPartId(String ylPartId) {
        this.ylPartId = ylPartId;
    }

    public String getEntityPutoutDate() {
        return entityPutoutDate;
    }

    public void setEntityPutoutDate(String entityPutoutDate) {
        this.entityPutoutDate = entityPutoutDate;
    }

    public String getProductDate() {
        return productDate;
    }

    public void setProductDate(String productDate) {
        this.productDate = productDate;
    }

    /**
     * get the processDemandMaterailStatus - 生产需求单投料捆包状态
     *
     * @return the processDemandMaterailStatus
     */
    public String getProcessDemandMaterailStatus() {
        return this.processDemandMaterailStatus;
    }

    /**
     * set the processDemandMaterailStatus - 生产需求单投料捆包状态
     */
    public void setProcessDemandMaterailStatus(String processDemandMaterailStatus) {
        this.processDemandMaterailStatus = processDemandMaterailStatus;
    }


    /**
     * get the processDemandMaterailStatusName - 生产需求单投料捆包状态
     *
     * @return the processDemandMaterailStatusName
     */
    public String getProcessDemandMaterailStatusName() {
        return this.processDemandMaterailStatusName;
    }

    /**
     * set the processDemandMaterailStatusName - 生产需求单投料捆包状态
     */
    public void setProcessDemandMaterailStatusName(String processDemandMaterailStatusName) {
        this.processDemandMaterailStatusName = processDemandMaterailStatusName;
    }

    /**
     * get the rmSpecsDesc - 原料规格
     *
     * @return the rmSpecsDesc
     */
    public String getRmSpecsDesc() {
        return this.rmSpecsDesc;
    }

    /**
     * set the rmSpecsDesc - 原料规格
     */
    public void setRmSpecsDesc(String rmSpecsDesc) {
        this.rmSpecsDesc = rmSpecsDesc;
    }

    /**
     * get the mNetWeight - 母捆包重量
     *
     * @return the mNetWeight
     */
    public BigDecimal getMNetWeight() {
        return this.mNetWeight;
    }

    /**
     * set the mNetWeight - 净重
     */
    public void setMNetWeight(BigDecimal mNetWeight) {
        this.mNetWeight = mNetWeight;
    }

    /**
     * get the machineName - 机组名称
     * @return the machineName
     */
    public String getMachineName() {
        return this.machineName;
    }

    /**
     * set the machineName - 机组名称
     */
    public void setMachineName(String machineName) {
        this.machineName = machineName;
    }

    public BigDecimal getTheoryWeight2() {
        return theoryWeight2;
    }

    public void setTheoryWeight2(BigDecimal theoryWeight2) {
        this.theoryWeight2 = theoryWeight2;
    }

    public BigDecimal getOutMatOuterDia() {
        return outMatOuterDia;
    }

    public void setOutMatOuterDia(BigDecimal outMatOuterDia) {
        this.outMatOuterDia = outMatOuterDia;
    }

    public String getDefectType() {
        return defectType;
    }

    public void setDefectType(String defectType) {
        this.defectType = defectType;
    }

    public String getLockCode() {
        return lockCode;
    }

    public void setLockCode(String lockCode) {
        this.lockCode = lockCode;
    }

    public String getClientChargePlat() {
        return clientChargePlat;
    }

    public void setClientChargePlat(String clientChargePlat) {
        this.clientChargePlat = clientChargePlat;
    }

    public String getClientOrderNum() {
        return clientOrderNum;
    }

    public void setClientOrderNum(String clientOrderNum) {
        this.clientOrderNum = clientOrderNum;
    }

    public String getPropertyResult() {
        return propertyResult;
    }

    public void setPropertyResult(String propertyResult) {
        this.propertyResult = propertyResult;
    }

    public Integer getProcessSeqId() {
        return this.processSeqId;
    }

    public void setProcessSeqId(Integer processSeqId) {
        this.processSeqId = processSeqId;
    }

    public String getAluminumPlateTime() {
        return aluminumPlateTime;
    }

    public void setAluminumPlateTime(String aluminumPlateTime) {
        this.aluminumPlateTime = aluminumPlateTime;
    }

    public String getCustomerMaterialNumber() {
        return this.customerMaterialNumber;
    }

    public void setCustomerMaterialNumber(String customerMaterialNumber) {
        this.customerMaterialNumber = customerMaterialNumber;
    }

    public String getWoodSeed() {
        return woodSeed;
    }

    public void setWoodSeed(String woodSeed) {
        this.woodSeed = woodSeed;
    }

    public String getzWood() {
        return zWood;
    }

    public void setzWood(String zWood) {
        this.zWood = zWood;
    }

    public BigDecimal getzWoodLength() {
        return zWoodLength;
    }

    public void setzWoodLength(BigDecimal zWoodLength) {
        this.zWoodLength = zWoodLength;
    }

    public BigDecimal getzWoodWidth() {
        return zWoodWidth;
    }

    public void setzWoodWidth(BigDecimal zWoodWidth) {
        this.zWoodWidth = zWoodWidth;
    }

    public BigDecimal getzWoodActualLength() {
        return zWoodActualLength;
    }

    public void setzWoodActualLength(BigDecimal zWoodActualLength) {
        this.zWoodActualLength = zWoodActualLength;
    }

    public Integer getzWoodQty() {
        return zWoodQty;
    }

    public void setzWoodQty(Integer zWoodQty) {
        this.zWoodQty = zWoodQty;
    }

    public String gethWood() {
        return hWood;
    }

    public void sethWood(String hWood) {
        this.hWood = hWood;
    }

    public BigDecimal gethWoodLength() {
        return hWoodLength;
    }

    public void sethWoodLength(BigDecimal hWoodLength) {
        this.hWoodLength = hWoodLength;
    }

    public BigDecimal gethWoodWidth() {
        return hWoodWidth;
    }

    public void sethWoodWidth(BigDecimal hWoodWidth) {
        this.hWoodWidth = hWoodWidth;
    }

    public BigDecimal gethWoodActualLength() {
        return hWoodActualLength;
    }

    public void sethWoodActualLength(BigDecimal hWoodActualLength) {
        this.hWoodActualLength = hWoodActualLength;
    }

    public Integer gethWoodQty() {
        return hWoodQty;
    }

    public void sethWoodQty(Integer hWoodQty) {
        this.hWoodQty = hWoodQty;
    }

    public String getOpenshelfNo() {
        return openshelfNo;
    }

    public void setOpenshelfNo(String openshelfNo) {
        this.openshelfNo = openshelfNo;
    }

    /**
     * get the othersRemark - 其他说明
     *
     * @return the othersRemark
     */
    public String getOthersRemark() {
        return this.othersRemark;
    }

    /**
     * set the othersRemark - 其他说明
     */
    public void setOthersRemark(String othersRemark) {
        this.othersRemark = othersRemark;
    }

    public String getJpDebatchingSign() {
        return this.jpDebatchingSign;
    }

    public void setJpDebatchingSign(String jpDebatchingSign) {
        this.jpDebatchingSign = jpDebatchingSign;
    }

    public String getJpFinishedProductSign() {
        return this.jpFinishedProductSign;
    }

    public void setJpFinishedProductSign(String jpFinishedProductSign) {
        this.jpFinishedProductSign = jpFinishedProductSign;
    }


    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getPartsCode() {
        return partsCode;
    }

    public void setPartsCode(String partsCode) {
        this.partsCode = partsCode;
    }

    public String getPartsName() {
        return partsName;
    }

    public void setPartsName(String partsName) {
        this.partsName = partsName;
    }

    public String getCarTypeCode() {
        return carTypeCode;
    }

    public void setCarTypeCode(String carTypeCode) {
        this.carTypeCode = carTypeCode;
    }

    public BigDecimal getQuota() {
        return quota;
    }

    public void setQuota(BigDecimal quota) {
        this.quota = quota;
    }

    public String getLadingSpotId() {
        return ladingSpotId;
    }

    public void setLadingSpotId(String ladingSpotId) {
        this.ladingSpotId = ladingSpotId;
    }

    /**
     * get the ponum - PO号
     *
     * @return the ponum
     */
    public String getPonum() {
        return this.ponum;
    }

    /**
     * set the ponum - PO号
     */
    public void setPonum(String ponum) {
        this.ponum = ponum;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {
        setSendSampleFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sendSampleFlag")), sendSampleFlag));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setProcessResultRelationStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processResultRelationStatus")), processResultRelationStatus));
        setProcessResultPackFrom(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processResultPackFrom")), processResultPackFrom));
        setF_packId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_packId")), f_packId));
        setF_matInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_matInnerId")), f_matInnerId));
        setM_packId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_packId")), m_packId));
        setM_matInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_matInnerId")), m_matInnerId));
        setUnitName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitName")), unitName));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setClAccountMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("clAccountMark")), clAccountMark));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packId")), packId));
        setFactoryOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryOrderNum")), factoryOrderNum));
        setPackType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packType")), packType));
        setPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partId")), partId));
        setPackingTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingTypeCode")), packingTypeCode));
        setPackingTypeName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingTypeName")), packingTypeName));
        setNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("netWeight")), netWeight));
        setGrossWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("grossWeight")), grossWeight));
        setQuantity(NumberUtils.toInteger(StringUtils.toString(map.get("quantity")), quantity));
        setProcessOrderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processOrderId")), processOrderId));
        setProcessDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandId")), processDemandId));
        setProcessDemandSubId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandSubId")), processDemandSubId));
        setProductDemandId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productDemandId")), productDemandId));
        setTeamId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teamId")), teamId));
        setWorkingShift(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("workingShift")), workingShift));
        setProcessCategory(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processCategory")), processCategory));
        setMachineCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineCode")), machineCode));
        setQualityGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("qualityGrade")), qualityGrade));
        setScrapType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapType")), scrapType));
        setProcessHour(NumberUtils.toBigDecimal(StringUtils.toString(map.get("processHour")), processHour));
        setProdNameCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodNameCode")), prodNameCode));
        setProdCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCname")), prodCname));
        setShopsign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("shopsign")), shopsign));
        setSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specsDesc")), specsDesc));
        setUnitedFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedFlag")), unitedFlag));
        setUnitedPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedPackId")), unitedPackId));
        setQuantityUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("quantityUnit")), quantityUnit));
        setWeightUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("weightUnit")), weightUnit));
        setBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessType")), businessType));
        setProcessResultStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processResultStatus")), processResultStatus));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setOperateBatchNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("operateBatchNo")), operateBatchNo));
        setContractNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("contractNum")), contractNum));
        setOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderNum")), orderNum));
        setStoreType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeType")), storeType));
        setGauge(NumberUtils.toBigDecimal(StringUtils.toString(map.get("gauge")), gauge));
        setWidth(NumberUtils.toBigDecimal(StringUtils.toString(map.get("width")), width));
        setLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("length")), length));
        setStoreTool(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("storeTool")), storeTool));
        setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
        setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setQualityStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("qualityStatus")), qualityStatus));
        setBlockReason(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("blockReason")), blockReason));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setLabelPrintDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelPrintDate")), labelPrintDate));
        setLabelPrintPerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelPrintPerson")), labelPrintPerson));
        setStampSeqId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("stampSeqId")), stampSeqId));
        setIfSample(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifSample")), ifSample));
        setCoverPlateQty(NumberUtils.toBigDecimal(StringUtils.toString(map.get("coverPlateQty")), coverPlateQty));
        setPackingFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingFlag")), packingFlag));
        setPackingDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingDate")), packingDate));
        setPackingPerson(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("packingPerson")), packingPerson));
        setDataSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dataSource")), dataSource));
        setTeamReportId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("teamReportId")), teamReportId));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecRevisorTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorTime")), recRevisorTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setIfWasteMaterialBalanceFinish(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifWasteMaterialBalanceFinish")), ifWasteMaterialBalanceFinish));
        setWasteMaterialBalanceId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wasteMaterialBalanceId")), wasteMaterialBalanceId));
        setActPutinTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actPutinTime")), actPutinTime));
        setPutinId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("putinId")), putinId));
        setPeriodId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("periodId")), periodId));
        setProxyType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("proxyType")), proxyType));
        setIfUploadFinance(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifUploadFinance")), ifUploadFinance));
        setMatInnerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("matInnerId")), matInnerId));
        setProdCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodCode")), prodCode));
        setMaterialRackClassifyNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialRackClassifyNumber")), materialRackClassifyNumber));
        setMaterialRackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialRackId")), materialRackId));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setD_userNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNum")), d_userNum));
        setD_userName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userName")), d_userName));

        setTradeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tradeCode")), tradeCode));
        setProdTypeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeId")), prodTypeId));
        setP_output(NumberUtils.toBigDecimal(StringUtils.toString(map.get("p_output")), p_output));
        setProdTypeDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("prodTypeDesc")), prodTypeDesc));
        setSalesPersNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("salesPersNo")), salesPersNo));
        setPieceWt(NumberUtils.toBigDecimal(StringUtils.toString(map.get("pieceWt")), pieceWt));
        setLabelId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("labelId")), labelId));
        setWlAccountMark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wlAccountMark")), wlAccountMark));
        setPackWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("packWeight")), packWeight));
        setOutPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("outPackId")), outPackId));
        setBigTypeDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("bigTypeDesc")), bigTypeDesc));
        setProcessDemandOutputId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandOutputId")), processDemandOutputId));
        setProcessBaseNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processBaseNo")), processBaseNo));
        setProcessBaseVersionNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processBaseVersionNum")), processBaseVersionNum));
        setOrderTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("orderTypeCode")), orderTypeCode));
        setScrapOriginType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("scrapOriginType")), scrapOriginType));
        setContractPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("contractPartId")), contractPartId));
        setSplitOrginPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("splitOrginPackId")), splitOrginPackId));
        setProcessConsignUnit(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processConsignUnit")), processConsignUnit));
        setManualNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("manualNo")), manualNo));
        setHsId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hsId")), hsId));
        setOriginalPackId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("originalPackId")), originalPackId));
        setHandbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handbookId")), handbookId));
        setF_handbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("f_handbookId")), f_handbookId));
        setM_handbookId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("m_handbookId")), m_handbookId));
        setCuttingNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("cuttingNum")), cuttingNum));
        setSurfaceGrade(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("surfaceGrade")), surfaceGrade));
        setFinUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserId")), finUserId));
        setFinUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("finUserName")), finUserName));

        setDemandRecNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("demandRecNo")), demandRecNo));
        setDemandRecVersionNum(NumberUtils.toBigDecimal(StringUtils.toString(map.get("demandRecVersionNum")), demandRecVersionNum));
        setCraftCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craftCode")), craftCode));
        setCraftVersionNum(NumberUtils.toBigDecimal(StringUtils.toString(map.get("craftVersionNum")), craftVersionNum));
        setProcessSeqCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processSeqCode")), processSeqCode));
        setCustPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartId")), custPartId));
        setCustPartName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custPartName")), custPartName));
        setProducingArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("producingArea")), producingArea));
        setMakerNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("makerNum")), makerNum));
        setMakerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("makerName")), makerName));
        setHeatNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("heatNum")), heatNum));
        setHeatNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("heatNo")), heatNo));
        setCustProviderId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("custProviderId")), custProviderId));
        setIsEntry(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isEntry")), isEntry));
        setProductDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productDesc")), productDesc));
        setTW_ORIGINAL_PACK_ID(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("TW_ORIGINAL_PACK_ID")), TW_ORIGINAL_PACK_ID));

        setYlQuantity(NumberUtils.toBigDecimal(StringUtils.toString(map.get("ylQuantity")), ylQuantity));
        setYlPartId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ylPartId")), ylPartId));
        setEntityPutoutDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("entityPutoutDate")), entityPutoutDate));
        setProductDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("productDate")), productDate));
        setProcessDemandMaterailStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandMaterailStatus")), processDemandMaterailStatus));
        setProcessDemandMaterailStatusName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("processDemandMaterailStatusName")), processDemandMaterailStatusName));
        setRmSpecsDesc(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("rmSpecsDesc")), rmSpecsDesc));
        setMNetWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("mNetWeight")), mNetWeight));
        setTareWt(NumberUtils.toBigDecimal(StringUtils.toString(map.get("tareWt")), tareWt));
        setMachineName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("machineName")), machineName));
        setUnitedPackIdBb(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitedPackIdBb")), unitedPackIdBb));

        setProviderCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("providerCode")), providerCode));
        setProviderCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("providerCname")), providerCname));

        setWtMethodCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wtMethodCode")), wtMethodCode));
        setTheoryWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("theoryWeight")), theoryWeight));
        setTheoryWeight2(NumberUtils.toBigDecimal(StringUtils.toString(map.get("theoryWeight2")), theoryWeight2));
        setActScWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("actScWeight")), actScWeight));
        setOutMatOuterDia(NumberUtils.toBigDecimal(StringUtils.toString(map.get("outMatOuterDia")), outMatOuterDia));
        setDefectType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("defectType")), defectType));
        setLockCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lockCode")), lockCode));
        setClientChargePlat(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("clientChargePlat")), clientChargePlat));
        setClientOrderNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("clientOrderNum")), clientOrderNum));
        setPropertyResult(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("propertyResult")), propertyResult));
        setProcessSeqId(NumberUtils.toInteger(StringUtils.toString(map.get("processSeqId")), processSeqId));
        setAluminumPlateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("aluminumPlateTime")), aluminumPlateTime));
        setCustomerMaterialNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerMaterialNumber")), customerMaterialNumber));
        setOpenshelfNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("openshelfNo")), openshelfNo));
        setWoodSeed(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("woodSeed")), woodSeed));
        setzWood(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("zWood")), zWood));
        setLimitedWeight(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("limitedWeight")), limitedWeight));
        setzWoodLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("zWoodLength")), zWoodLength));
        setzWoodWidth(NumberUtils.toBigDecimal(StringUtils.toString(map.get("zWoodWidth")), zWoodWidth));
        setzWoodActualLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("zWoodActualLength")), zWoodActualLength));
        setzWoodQty(NumberUtils.toInteger(StringUtils.toString(map.get("zWoodQty")), zWoodQty));
        sethWood(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("hWood")), hWood));
        sethWoodLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("hWoodLength")), hWoodLength));
        sethWoodWidth(NumberUtils.toBigDecimal(StringUtils.toString(map.get("hWoodWidth")), hWoodWidth));
        sethWoodActualLength(NumberUtils.toBigDecimal(StringUtils.toString(map.get("hWoodActualLength")), hWoodActualLength));
        sethWoodQty(NumberUtils.toInteger(StringUtils.toString(map.get("hWoodQty")), hWoodQty));
        setOthersRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("othersRemark")), othersRemark));
        setJpDebatchingSign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jpDebatchingSign")), jpDebatchingSign));
        setJpFinishedProductSign(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jpFinishedProductSign")), jpFinishedProductSign));
        setVehicleName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleName")), vehicleName));
        setPartsCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partsCode")), partsCode));
        setPartsName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("partsName")), partsName));
        setCarTypeCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTypeCode")), carTypeCode));
        setQuota(NumberUtils.toBigDecimal(StringUtils.toString(map.get("quota")), quota));
        setLadingSpotId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingSpotId")), ladingSpotId));
        setPonum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ponum")), ponum));
        setTailPackageFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tailPackageFlag")), tailPackageFlag));
        setMaterialRackClassifyName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("materialRackClassifyName")), materialRackClassifyName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("sendSampleFlag", StringUtils.toString(sendSampleFlag, eiMetadata.getMeta("sendSampleFlag")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("processResultRelationStatus", StringUtils.toString(processResultRelationStatus, eiMetadata.getMeta("processResultRelationStatus")));
        map.put("processResultPackFrom", StringUtils.toString(processResultPackFrom, eiMetadata.getMeta("processResultPackFrom")));
        map.put("f_packId", StringUtils.toString(f_packId, eiMetadata.getMeta("f_packId")));
        map.put("f_matInnerId", StringUtils.toString(f_matInnerId, eiMetadata.getMeta("f_matInnerId")));
        map.put("m_packId", StringUtils.toString(m_packId, eiMetadata.getMeta("m_packId")));
        map.put("m_matInnerId", StringUtils.toString(m_matInnerId, eiMetadata.getMeta("m_matInnerId")));
        map.put("unitName", StringUtils.toString(unitName, eiMetadata.getMeta("unitName")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("clAccountMark", StringUtils.toString(clAccountMark, eiMetadata.getMeta("clAccountMark")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("packId", StringUtils.toString(packId, eiMetadata.getMeta("packId")));
        map.put("factoryOrderNum", StringUtils.toString(factoryOrderNum, eiMetadata.getMeta("factoryOrderNum")));
        map.put("packType", StringUtils.toString(packType, eiMetadata.getMeta("packType")));
        map.put("partId", StringUtils.toString(partId, eiMetadata.getMeta("partId")));
        map.put("packingTypeCode", StringUtils.toString(packingTypeCode, eiMetadata.getMeta("packingTypeCode")));
        map.put("packingTypeName", StringUtils.toString(packingTypeName, eiMetadata.getMeta("packingTypeName")));
        map.put("netWeight", StringUtils.toString(netWeight, eiMetadata.getMeta("netWeight")));
        map.put("grossWeight", StringUtils.toString(grossWeight, eiMetadata.getMeta("grossWeight")));
        map.put("tareWt", StringUtils.toString(tareWt, eiMetadata.getMeta("tareWt")));
        map.put("quantity", StringUtils.toString(quantity, eiMetadata.getMeta("quantity")));
        map.put("processOrderId", StringUtils.toString(processOrderId, eiMetadata.getMeta("processOrderId")));
        map.put("processDemandId", StringUtils.toString(processDemandId, eiMetadata.getMeta("processDemandId")));
        map.put("processDemandSubId", StringUtils.toString(processDemandSubId, eiMetadata.getMeta("processDemandSubId")));
        map.put("productDemandId", StringUtils.toString(productDemandId, eiMetadata.getMeta("productDemandId")));
        map.put("teamId", StringUtils.toString(teamId, eiMetadata.getMeta("teamId")));
        map.put("workingShift", StringUtils.toString(workingShift, eiMetadata.getMeta("workingShift")));
        map.put("processCategory", StringUtils.toString(processCategory, eiMetadata.getMeta("processCategory")));
        map.put("machineCode", StringUtils.toString(machineCode, eiMetadata.getMeta("machineCode")));
        map.put("qualityGrade", StringUtils.toString(qualityGrade, eiMetadata.getMeta("qualityGrade")));
        map.put("scrapType", StringUtils.toString(scrapType, eiMetadata.getMeta("scrapType")));
        map.put("processHour", StringUtils.toString(processHour, eiMetadata.getMeta("processHour")));
        map.put("prodNameCode", StringUtils.toString(prodNameCode, eiMetadata.getMeta("prodNameCode")));
        map.put("prodCname", StringUtils.toString(prodCname, eiMetadata.getMeta("prodCname")));
        map.put("shopsign", StringUtils.toString(shopsign, eiMetadata.getMeta("shopsign")));
        map.put("specsDesc", StringUtils.toString(specsDesc, eiMetadata.getMeta("specsDesc")));
        map.put("unitedFlag", StringUtils.toString(unitedFlag, eiMetadata.getMeta("unitedFlag")));
        map.put("unitedPackId", StringUtils.toString(unitedPackId, eiMetadata.getMeta("unitedPackId")));
        map.put("quantityUnit", StringUtils.toString(quantityUnit, eiMetadata.getMeta("quantityUnit")));
        map.put("weightUnit", StringUtils.toString(weightUnit, eiMetadata.getMeta("weightUnit")));
        map.put("businessType", StringUtils.toString(businessType, eiMetadata.getMeta("businessType")));
        map.put("processResultStatus", StringUtils.toString(processResultStatus, eiMetadata.getMeta("processResultStatus")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("operateBatchNo", StringUtils.toString(operateBatchNo, eiMetadata.getMeta("operateBatchNo")));
        map.put("contractNum", StringUtils.toString(contractNum, eiMetadata.getMeta("contractNum")));
        map.put("orderNum", StringUtils.toString(orderNum, eiMetadata.getMeta("orderNum")));
        map.put("storeType", StringUtils.toString(storeType, eiMetadata.getMeta("storeType")));
        map.put("gauge", StringUtils.toString(gauge, eiMetadata.getMeta("gauge")));
        map.put("width", StringUtils.toString(width, eiMetadata.getMeta("width")));
        map.put("length", StringUtils.toString(length, eiMetadata.getMeta("length")));
        map.put("storeTool", StringUtils.toString(storeTool, eiMetadata.getMeta("storeTool")));
        map.put("warehouseCode", StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
        map.put("warehouseName", StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("qualityStatus", StringUtils.toString(qualityStatus, eiMetadata.getMeta("qualityStatus")));
        map.put("blockReason", StringUtils.toString(blockReason, eiMetadata.getMeta("blockReason")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("labelPrintDate", StringUtils.toString(labelPrintDate, eiMetadata.getMeta("labelPrintDate")));
        map.put("labelPrintPerson", StringUtils.toString(labelPrintPerson, eiMetadata.getMeta("labelPrintPerson")));
        map.put("stampSeqId", StringUtils.toString(stampSeqId, eiMetadata.getMeta("stampSeqId")));
        map.put("ifSample", StringUtils.toString(ifSample, eiMetadata.getMeta("ifSample")));
        map.put("coverPlateQty", StringUtils.toString(coverPlateQty, eiMetadata.getMeta("coverPlateQty")));
        map.put("packingFlag", StringUtils.toString(packingFlag, eiMetadata.getMeta("packingFlag")));
        map.put("packingDate", StringUtils.toString(packingDate, eiMetadata.getMeta("packingDate")));
        map.put("packingPerson", StringUtils.toString(packingPerson, eiMetadata.getMeta("packingPerson")));
        map.put("dataSource", StringUtils.toString(dataSource, eiMetadata.getMeta("dataSource")));
        map.put("teamReportId", StringUtils.toString(teamReportId, eiMetadata.getMeta("teamReportId")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recRevisorTime", StringUtils.toString(recRevisorTime, eiMetadata.getMeta("recRevisorTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("ifWasteMaterialBalanceFinish", StringUtils.toString(ifWasteMaterialBalanceFinish, eiMetadata.getMeta("ifWasteMaterialBalanceFinish")));
        map.put("wasteMaterialBalanceId", StringUtils.toString(wasteMaterialBalanceId, eiMetadata.getMeta("wasteMaterialBalanceId")));
        map.put("actPutinTime", StringUtils.toString(actPutinTime, eiMetadata.getMeta("actPutinTime")));
        map.put("putinId", StringUtils.toString(putinId, eiMetadata.getMeta("putinId")));
        map.put("periodId", StringUtils.toString(periodId, eiMetadata.getMeta("periodId")));
        map.put("proxyType", StringUtils.toString(proxyType, eiMetadata.getMeta("proxyType")));
        map.put("ifUploadFinance", StringUtils.toString(ifUploadFinance, eiMetadata.getMeta("ifUploadFinance")));
        map.put("matInnerId", StringUtils.toString(matInnerId, eiMetadata.getMeta("matInnerId")));
        map.put("prodCode", StringUtils.toString(prodCode, eiMetadata.getMeta("prodCode")));
        map.put("materialRackClassifyNumber", StringUtils.toString(materialRackClassifyNumber, eiMetadata.getMeta("materialRackClassifyNumber")));
        map.put("materialRackId", StringUtils.toString(materialRackId, eiMetadata.getMeta("materialRackId")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("d_userNum", StringUtils.toString(d_userNum, eiMetadata.getMeta("d_userNum")));
        map.put("d_userName", StringUtils.toString(d_userName, eiMetadata.getMeta("d_userName")));

        map.put("tradeCode", StringUtils.toString(tradeCode, eiMetadata.getMeta("tradeCode")));
        map.put("prodTypeId", StringUtils.toString(prodTypeId, eiMetadata.getMeta("prodTypeId")));
        map.put("p_output", StringUtils.toString(p_output, eiMetadata.getMeta("p_output")));
        map.put("prodTypeDesc", StringUtils.toString(prodTypeDesc, eiMetadata.getMeta("prodTypeDesc")));
        map.put("salesPersNo", StringUtils.toString(salesPersNo, eiMetadata.getMeta("salesPersNo")));
        map.put("pieceWt", StringUtils.toString(pieceWt, eiMetadata.getMeta("pieceWt")));
        map.put("labelId", StringUtils.toString(labelId, eiMetadata.getMeta("labelId")));
        map.put("wlAccountMark", StringUtils.toString(wlAccountMark, eiMetadata.getMeta("wlAccountMark")));
        map.put("packWeight", StringUtils.toString(packWeight, eiMetadata.getMeta("packWeight")));
        map.put("outPackId", StringUtils.toString(outPackId, eiMetadata.getMeta("outPackId")));
        map.put("bigTypeDesc", StringUtils.toString(bigTypeDesc, eiMetadata.getMeta("bigTypeDesc")));
        map.put("processDemandOutputId", StringUtils.toString(processDemandOutputId, eiMetadata.getMeta("processDemandOutputId")));
        map.put("processBaseNo", StringUtils.toString(processBaseNo, eiMetadata.getMeta("processBaseNo")));
        map.put("processBaseVersionNum", StringUtils.toString(processBaseVersionNum, eiMetadata.getMeta("processBaseVersionNum")));
        map.put("orderTypeCode", StringUtils.toString(orderTypeCode, eiMetadata.getMeta("orderTypeCode")));
        map.put("scrapOriginType", StringUtils.toString(scrapOriginType, eiMetadata.getMeta("scrapOriginType")));
        map.put("contractPartId", StringUtils.toString(contractPartId, eiMetadata.getMeta("contractPartId")));
        map.put("splitOrginPackId", StringUtils.toString(splitOrginPackId, eiMetadata.getMeta("splitOrginPackId")));
        map.put("processConsignUnit", StringUtils.toString(processConsignUnit, eiMetadata.getMeta("processConsignUnit")));
        map.put("manualNo", StringUtils.toString(manualNo, eiMetadata.getMeta("manualNo")));
        map.put("hsId", StringUtils.toString(hsId, eiMetadata.getMeta("hsId")));
        map.put("originalPackId", StringUtils.toString(originalPackId, eiMetadata.getMeta("originalPackId")));
        map.put("handbookId", StringUtils.toString(handbookId, eiMetadata.getMeta("handbookId")));
        map.put("f_handbookId", StringUtils.toString(f_handbookId, eiMetadata.getMeta("f_handbookId")));
        map.put("m_handbookId", StringUtils.toString(m_handbookId, eiMetadata.getMeta("m_handbookId")));
        map.put("cuttingNum", StringUtils.toString(cuttingNum, eiMetadata.getMeta("cuttingNum")));
        map.put("surfaceGrade",StringUtils.toString(surfaceGrade, eiMetadata.getMeta("surfaceGrade")));
        map.put("finUserId",StringUtils.toString(finUserId, eiMetadata.getMeta("finUserId")));
        map.put("finUserName",StringUtils.toString(finUserName, eiMetadata.getMeta("finUserName")));

        map.put("demandRecNo",StringUtils.toString(demandRecNo, eiMetadata.getMeta("demandRecNo")));
        map.put("demandRecVersionNum",StringUtils.toString(demandRecVersionNum, eiMetadata.getMeta("demandRecVersionNum")));
        map.put("craftCode",StringUtils.toString(craftCode, eiMetadata.getMeta("craftCode")));
        map.put("craftVersionNum",StringUtils.toString(craftVersionNum, eiMetadata.getMeta("craftVersionNum")));
        map.put("processSeqCode",StringUtils.toString(processSeqCode, eiMetadata.getMeta("processSeqCode")));
        map.put("custPartId",StringUtils.toString(custPartId, eiMetadata.getMeta("custPartId")));
        map.put("custPartName",StringUtils.toString(custPartName, eiMetadata.getMeta("custPartName")));
        map.put("producingArea",StringUtils.toString(producingArea, eiMetadata.getMeta("producingArea")));
        map.put("makerNum",StringUtils.toString(makerNum, eiMetadata.getMeta("makerNum")));
        map.put("makerName",StringUtils.toString(makerName, eiMetadata.getMeta("makerName")));
        map.put("heatNum",StringUtils.toString(heatNum, eiMetadata.getMeta("heatNum")));
        map.put("heatNo",StringUtils.toString(heatNo, eiMetadata.getMeta("heatNo")));
        map.put("custProviderId",StringUtils.toString(custProviderId, eiMetadata.getMeta("custProviderId")));
        map.put("isEntry",StringUtils.toString(isEntry, eiMetadata.getMeta("isEntry")));
        map.put("productDesc",StringUtils.toString(productDesc, eiMetadata.getMeta("productDesc")));
        map.put("TW_ORIGINAL_PACK_ID",StringUtils.toString(TW_ORIGINAL_PACK_ID, eiMetadata.getMeta("TW_ORIGINAL_PACK_ID")));

        map.put("ylQuantity", StringUtils.toString(ylQuantity, eiMetadata.getMeta("ylQuantity")));
        map.put("ylPartId", StringUtils.toString(ylPartId, eiMetadata.getMeta("ylPartId")));
        map.put("entityPutoutDate", StringUtils.toString(entityPutoutDate, eiMetadata.getMeta("entityPutoutDate")));
        map.put("productDate", StringUtils.toString(productDate, eiMetadata.getMeta("productDate")));
        map.put("processDemandMaterailStatus", StringUtils.toString(processDemandMaterailStatus, eiMetadata.getMeta("processDemandMaterailStatus")));
        map.put("processDemandMaterailStatusName", StringUtils.toString(processDemandMaterailStatusName, eiMetadata.getMeta("processDemandMaterailStatusName")));
        map.put("rmSpecsDesc", StringUtils.toString(rmSpecsDesc, eiMetadata.getMeta("rmSpecsDesc")));
        map.put("mNetWeight", StringUtils.toString(mNetWeight, eiMetadata.getMeta("mNetWeight")));
        map.put("machineName",StringUtils.toString(machineName, eiMetadata.getMeta("machineName")));
        map.put("unitedPackIdBb",StringUtils.toString(unitedPackIdBb, eiMetadata.getMeta("unitedPackIdBb")));
        map.put("providerCode",StringUtils.toString(providerCode, eiMetadata.getMeta("providerCode")));
        map.put("providerCname",StringUtils.toString(providerCname, eiMetadata.getMeta("providerCname")));

        map.put("wtMethodCode",StringUtils.toString(wtMethodCode, eiMetadata.getMeta("wtMethodCode")));
        map.put("theoryWeight",StringUtils.toString(theoryWeight, eiMetadata.getMeta("theoryWeight")));
        map.put("theoryWeight2",StringUtils.toString(theoryWeight2, eiMetadata.getMeta("theoryWeight2")));
        map.put("actScWeight",StringUtils.toString(actScWeight, eiMetadata.getMeta("actScWeight")));
        map.put("outMatOuterDia",StringUtils.toString(outMatOuterDia, eiMetadata.getMeta("outMatOuterDia")));
        map.put("lockCode",StringUtils.toString(lockCode, eiMetadata.getMeta("lockCode")));
        map.put("defectType",StringUtils.toString(defectType, eiMetadata.getMeta("defectType")));
        map.put("clientChargePlat",StringUtils.toString(clientChargePlat, eiMetadata.getMeta("clientChargePlat")));
        map.put("clientOrderNum",StringUtils.toString(clientOrderNum, eiMetadata.getMeta("clientOrderNum")));
        map.put("propertyResult",StringUtils.toString(propertyResult, eiMetadata.getMeta("propertyResult")));
        map.put("processSeqId", StringUtils.toString(processSeqId, eiMetadata.getMeta("processSeqId")));
        map.put("aluminumPlateTime",StringUtils.toString(aluminumPlateTime, eiMetadata.getMeta("aluminumPlateTime")));
        map.put("customerMaterialNumber", StringUtils.toString(customerMaterialNumber, eiMetadata.getMeta("customerMaterialNumber")));
        map.put("openshelfNo", StringUtils.toString(openshelfNo, eiMetadata.getMeta("openshelfNo")));
        map.put("woodSeed", StringUtils.toString(woodSeed, eiMetadata.getMeta("woodSeed")));
        map.put("zWood", StringUtils.toString(zWood, eiMetadata.getMeta("zWood")));
        map.put("limitedWeight", StringUtils.toString(limitedWeight, eiMetadata.getMeta("limitedWeight")));
        map.put("zWoodLength",StringUtils.toString(zWoodLength, eiMetadata.getMeta("zWoodLength")));
        map.put("zWoodWidth",StringUtils.toString(zWoodWidth, eiMetadata.getMeta("zWoodWidth")));
        map.put("zWoodActualLength",StringUtils.toString(zWoodActualLength, eiMetadata.getMeta("zWoodActualLength")));
        map.put("zWoodQty",StringUtils.toString(zWoodQty, eiMetadata.getMeta("zWoodQty")));
        map.put("hWood", StringUtils.toString(hWood, eiMetadata.getMeta("hWood")));
        map.put("hWoodLength",StringUtils.toString(hWoodLength, eiMetadata.getMeta("hWoodLength")));
        map.put("hWoodWidth",StringUtils.toString(hWoodWidth, eiMetadata.getMeta("hWoodWidth")));
        map.put("hWoodActualLength",StringUtils.toString(hWoodActualLength, eiMetadata.getMeta("hWoodActualLength")));
        map.put("hWoodQty",StringUtils.toString(hWoodQty, eiMetadata.getMeta("hWoodQty")));
        map.put("othersRemark", StringUtils.toString(othersRemark, eiMetadata.getMeta("othersRemark")));
        map.put("jpDebatchingSign", StringUtils.toString(jpDebatchingSign, eiMetadata.getMeta("jpDebatchingSign")));
        map.put("jpFinishedProductSign", StringUtils.toString(jpFinishedProductSign, eiMetadata.getMeta("jpFinishedProductSign")));
        map.put("vehicleName", StringUtils.toString(vehicleName, eiMetadata.getMeta("vehicleName")));
        map.put("partsCode",StringUtils.toString(partsCode, eiMetadata.getMeta("partsCode")));
        map.put("partsName",StringUtils.toString(partsName, eiMetadata.getMeta("partsName")));
        map.put("carTypeCode",StringUtils.toString(carTypeCode, eiMetadata.getMeta("carTypeCode")));
        map.put("quota",StringUtils.toString(quota, eiMetadata.getMeta("quota")));
        map.put("ladingSpotId", StringUtils.toString(ladingSpotId, eiMetadata.getMeta("ladingSpotId")));
        map.put("ponum", StringUtils.toString(ponum, eiMetadata.getMeta("ponum")));
        map.put("tailPackageFlag", StringUtils.toString(tailPackageFlag, eiMetadata.getMeta("tailPackageFlag")));
        map.put("materialRackClassifyName", StringUtils.toString(materialRackClassifyName, eiMetadata.getMeta("materialRackClassifyName")));
        return map;
    }

    public String getCustPartName() {
        return custPartName;
    }

    public void setCustPartName(String custPartName) {
        this.custPartName = custPartName;
    }

    public String getCustPartId() {
        return custPartId;
    }

    public void setCustPartId(String custPartId) {
        this.custPartId = custPartId;
    }
}
