package com.baosight.imom.common.utils;

import com.baosight.iplat4j.eu.dm.PlatFileUploadUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 使用EasyExcel 生成excel文件 上传S3 ftp 服务器
 *
 * <AUTHOR>
 * @DATE 20240116
 */
public class EasyExcelExportUtil {

    private static final Logger log = LoggerFactory.getLogger(EasyExcelExportUtil.class);

    /**
     * @param inputStream workbook 流
     * @param fileName    文件名称
     * @param mapLogin    登陆人 map
     * @return {docUrl: 文档地址, docId: 文档ID, fileSize: 文件大小}
     */
    public static Map<String, String> exprotExcel(InputStream inputStream, String fileName, Map mapLogin) {
        Map<String, String> returnMap = new HashMap<>();
        Map<String, String> param = new HashMap<>();
        param.put("groupId", "VCdownlad");//文件组标识
        param.put("userId", MapUtils.getString(mapLogin, "loginName", "VCSystem"));            // 创建人
        //param.put("configPathDefine", configPathDefine);      // system后面扩展的路径
        param.put("fileName", fileName);
        FileItem fileItem = createFileItem(inputStream, fileName);
        MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
        try {
            //上传S3
            returnMap = PlatFileUploadUtils.uploadFile(multipartFile, param);
            return returnMap;
        } catch (Exception e) {
            throw new RuntimeException("上传S3报错" + e.getMessage());
        }
    }


    /**
     * FileItem类对象创建
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public static FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            //log.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件转换失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    throw new IllegalArgumentException("文件流输出关闭失败");

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    throw new IllegalArgumentException("文件流输入关闭失败");
                }
            }
        }

        return item;
    }


    /**
     * 生成文件流示例
     */
/*    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    List<Tvcca0010Excel> listMap = dao.queryAll(VCMN08.QUERY_EXECL_BY_EASY_EXCEL, mapQuery);
            EasyExcel.write(byteArrayOutputStream, Tvcca0010Excel.class).
    sheet("销售截止数据导出")
                    .doWrite(listMap);
    //System.out.println(byteArrayOutputStream.size());
    InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    String fileName = strSegNo +"销售截止数据导出"+ System.currentTimeMillis()+".xlsx";*/

}
