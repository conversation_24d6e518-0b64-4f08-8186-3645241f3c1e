<?xml version="1.0" encoding="UTF-8"?>
<!DOC<PERSON><PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-12-05 14:44:45
   		Version :  1.0
		tableName :meli.tlids1101 
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 CRANE_ORDER_ID  VARCHAR   NOT NULL, 
		 LIST_SOURCE  VARCHAR   NOT NULL, 
		 VOUCHER_NUM  VARCHAR, 
		 CRANE_ID  VARCHAR   NOT NULL, 
		 CRANE_NAME  VARCHAR   NOT NULL, 
		 BATCH_NUMBER  VARCHAR, 
		 SERIAL_NUMBER  VARCHAR, 
		 MACHINE_CODE  VARCHAR, 
		 MACHINE_NAME  VARCHAR, 
		 UNPACK_AREA_ID  VARCHAR, 
		 UNPACK_AREA_NAME  VARCHAR, 
		 MOULD_ID  VARCHAR, 
		 MOULD_NAME  VARCHAR, 
		 START_TIME  VARCHAR, 
		 END_TIME  VARCHAR, 
		 JO<PERSON>_TIME  VARCHAR, 
		 START_AREA_TYPE  VARCHAR, 
		 START_AREA_CODE  VARCHAR, 
		 START_AREA_NAME  VARCHAR, 
		 END_AREA_TYPE  VARCHAR, 
		 END_AREA_CODE  VARCHAR, 
		 END_AREA_NAME  VARCHAR, 
		 STATUS  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATOR_NAME  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISOR_NAME  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 TENANT_USER  VARCHAR, 
		 DEL_FLAG  SMALLINT, 
		 UUID  VARCHAR   NOT NULL   primarykey
	-->
<sqlMap namespace="tlids1101">

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.common.li.domain.Tlids1101">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代代码 -->
				CRANE_ORDER_ID	as "craneOrderId",  <!-- 行车作业清单号 -->
				LIST_SOURCE	as "listSource",  <!-- 清单来源 -->
				VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单号 -->
				CRANE_ID	as "craneId",  <!-- 行车编号 -->
				CRANE_NAME	as "craneName",  <!-- 行车名称 -->
				BATCH_NUMBER	as "batchNumber",  <!-- 批次号 -->
				SERIAL_NUMBER	as "serialNumber",  <!-- 顺序号 -->
				MACHINE_CODE	as "machineCode",  <!-- 机组代码 -->
				MACHINE_NAME	as "machineName",  <!-- 机组名称 -->
				UNPACK_AREA_ID	as "unpackAreaId",  <!-- 拆包区编号 -->
				UNPACK_AREA_NAME	as "unpackAreaName",  <!-- 拆包区名称 -->
				MOULD_ID	as "mouldId",  <!-- 模具ID -->
				MOULD_NAME	as "mouldName",  <!-- 模具名称 -->
				START_TIME	as "startTime",  <!-- 作业开始时间 -->
				END_TIME	as "endTime",  <!-- 作业结束时间 -->
				JOB_TIME	as "jobTime",  <!-- 作业时间 -->
				START_AREA_TYPE	as "startAreaType",  <!-- 起始区域类型 -->
				START_AREA_CODE	as "startAreaCode",  <!-- 起始区域类型代码 -->
				START_AREA_NAME	as "startAreaName",  <!-- 起始区域类型名称 -->
				END_AREA_TYPE	as "endAreaType",  <!-- 终到区域类型 -->
				END_AREA_CODE	as "endAreaCode",  <!-- 终到区域类型代码 -->
				END_AREA_NAME	as "endAreaName",  <!-- 终到区域类型名称 -->
				STATUS	as "status",  <!-- 状态 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				DEL_FLAG	as "delFlag",  <!-- 删除标记 -->
				UUID	as "uuid" <!-- ID -->
		FROM meli.tlids1101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
    		  UUID asc
		</isEmpty>
  		</dynamic>
			
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM meli.tlids1101 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneOrderId">
			CRANE_ORDER_ID = #craneOrderId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="listSource">
			LIST_SOURCE = #listSource#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneId">
			CRANE_ID = #craneId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="craneName">
			CRANE_NAME = #craneName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="batchNumber">
			BATCH_NUMBER = #batchNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="serialNumber">
			SERIAL_NUMBER = #serialNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="machineCode">
			MACHINE_CODE = #machineCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="machineName">
			MACHINE_NAME = #machineName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unpackAreaId">
			UNPACK_AREA_ID = #unpackAreaId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unpackAreaName">
			UNPACK_AREA_NAME = #unpackAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldId">
			MOULD_ID = #mouldId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mouldName">
			MOULD_NAME = #mouldName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startTime">
			START_TIME = #startTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endTime">
			END_TIME = #endTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="jobTime">
			JOB_TIME = #jobTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startAreaType">
			START_AREA_TYPE = #startAreaType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startAreaCode">
			START_AREA_CODE = #startAreaCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="startAreaName">
			START_AREA_NAME = #startAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endAreaType">
			END_AREA_TYPE = #endAreaType#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endAreaCode">
			END_AREA_CODE = #endAreaCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="endAreaName">
			END_AREA_NAME = #endAreaName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO meli.tlids1101 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代代码 -->
										CRANE_ORDER_ID,  <!-- 行车作业清单号 -->
										LIST_SOURCE,  <!-- 清单来源 -->
										VOUCHER_NUM,  <!-- 依据凭单号 -->
										CRANE_ID,  <!-- 行车编号 -->
										CRANE_NAME,  <!-- 行车名称 -->
										BATCH_NUMBER,  <!-- 批次号 -->
										SERIAL_NUMBER,  <!-- 顺序号 -->
										MACHINE_CODE,  <!-- 机组代码 -->
										MACHINE_NAME,  <!-- 机组名称 -->
										UNPACK_AREA_ID,  <!-- 拆包区编号 -->
										UNPACK_AREA_NAME,  <!-- 拆包区名称 -->
										MOULD_ID,  <!-- 模具ID -->
										MOULD_NAME,  <!-- 模具名称 -->
										START_TIME,  <!-- 作业开始时间 -->
										END_TIME,  <!-- 作业结束时间 -->
										JOB_TIME,  <!-- 作业时间 -->
										START_AREA_TYPE,  <!-- 起始区域类型 -->
										START_AREA_CODE,  <!-- 起始区域类型代码 -->
										START_AREA_NAME,  <!-- 起始区域类型名称 -->
										END_AREA_TYPE,  <!-- 终到区域类型 -->
										END_AREA_CODE,  <!-- 终到区域类型代码 -->
										END_AREA_NAME,  <!-- 终到区域类型名称 -->
										STATUS,  <!-- 状态 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										TENANT_USER,  <!-- 租户 -->
										DEL_FLAG,  <!-- 删除标记 -->
										UUID  <!-- ID -->
										)		 
	    VALUES (#segNo#, #unitCode#, #craneOrderId#, #listSource#, #voucherNum#, #craneId#, #craneName#, #batchNumber#, #serialNumber#, #machineCode#, #machineName#, #unpackAreaId#, #unpackAreaName#, #mouldId#, #mouldName#, #startTime#, #endTime#, #jobTime#, #startAreaType#, #startAreaCode#, #startAreaName#, #endAreaType#, #endAreaCode#, #endAreaName#, #status#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #tenantUser#, #delFlag#, #uuid#) 
	</insert>
  
	<delete id="delete">
		DELETE FROM meli.tlids1101 WHERE 
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE meli.tlids1101 
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					CRANE_ORDER_ID	= #craneOrderId#,   <!-- 行车作业清单号 -->  
					LIST_SOURCE	= #listSource#,   <!-- 清单来源 -->  
					VOUCHER_NUM	= #voucherNum#,   <!-- 依据凭单号 -->  
					CRANE_ID	= #craneId#,   <!-- 行车编号 -->  
					CRANE_NAME	= #craneName#,   <!-- 行车名称 -->  
					BATCH_NUMBER	= #batchNumber#,   <!-- 批次号 -->  
					SERIAL_NUMBER	= #serialNumber#,   <!-- 顺序号 -->  
					MACHINE_CODE	= #machineCode#,   <!-- 机组代码 -->  
					MACHINE_NAME	= #machineName#,   <!-- 机组名称 -->  
					UNPACK_AREA_ID	= #unpackAreaId#,   <!-- 拆包区编号 -->  
					UNPACK_AREA_NAME	= #unpackAreaName#,   <!-- 拆包区名称 -->  
					MOULD_ID	= #mouldId#,   <!-- 模具ID -->  
					MOULD_NAME	= #mouldName#,   <!-- 模具名称 -->  
					START_TIME	= #startTime#,   <!-- 作业开始时间 -->  
					END_TIME	= #endTime#,   <!-- 作业结束时间 -->  
					JOB_TIME	= #jobTime#,   <!-- 作业时间 -->  
					START_AREA_TYPE	= #startAreaType#,   <!-- 起始区域类型 -->  
					START_AREA_CODE	= #startAreaCode#,   <!-- 起始区域类型代码 -->  
					START_AREA_NAME	= #startAreaName#,   <!-- 起始区域类型名称 -->  
					END_AREA_TYPE	= #endAreaType#,   <!-- 终到区域类型 -->  
					END_AREA_CODE	= #endAreaCode#,   <!-- 终到区域类型代码 -->  
					END_AREA_NAME	= #endAreaName#,   <!-- 终到区域类型名称 -->  
					STATUS	= #status#,   <!-- 状态 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
						WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>