package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.DateUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.StrUtil;
import com.baosight.imom.common.utils.ValidationUtils;
import com.baosight.imom.vg.dm.domain.*;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> yzj
 * @Description : 故障信息页面后台
 * @Date : 2024/10/8
 * @Version : 1.0
 */
public class ServiceVGDM07 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM07.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0701().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK).addBlockMeta(new VGDM0701().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0701.QUERY, new VGDM0701());
    }

    /**
     * 新增
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0701 vgdm0701 = new VGDM0701();
            vgdm0701.fromMap(block.getRow(0));
            // 数据校验
            this.checkData(vgdm0701);
            // 默认字段
            vgdm0701.insertData(dao);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 校验故障信息
     *
     * @param vgdm0701 故障信息
     */
    private void checkData(VGDM0701 vgdm0701) {
        // 使用ValidationUtils进行基础字段校验
        ValidationUtils.validateEntity(vgdm0701, ValidationUtils.Group1.class);
        // 故障时间范围
        String startTime = vgdm0701.getFaultStartTime();
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, startTime.length() > 14 ? DateUtils.FORMATTER_19 : DateUtils.FORMATTER_14);
        vgdm0701.setFaultStartTime(startDateTime.format(DateUtils.FORMATTER_14));
    }

    /**
     * 修改
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0701 vgdm0701 = new VGDM0701();
            vgdm0701.fromMap(block.getRow(0));
            VGDM0701 dbData = DaoUtils.queryAndCheckStatus(dao, vgdm0701, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
            this.checkData(vgdm0701);
            // 可修改字段
            dbData.setFaultType(vgdm0701.getFaultType());
            dbData.setFaultLevel(vgdm0701.getFaultLevel());
            // 故障来源为1手工新增时，可修改故障开始时间、故障描述
            if ("1".equals(vgdm0701.getFaultSource())) {
                dbData.setFaultStartTime(vgdm0701.getFaultStartTime());
                dbData.setFaultDesc(vgdm0701.getFaultDesc());
            }
            // 赋值通用字段
            Map data = dbData.toMap();
            RecordUtils.setRevisor(data);
            dao.update(VGDM0701.UPDATE, data);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0701 frontData;
            VGDM0701 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0701();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS, MesConstant.Status.K10);
                dbData.setFaultStatus(MesConstant.Status.K00);
                dbData.setDelFlag("1");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0701.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 提交
     *
     * <p>状态新增->已提交
     */
    public EiInfo submit(EiInfo inInfo) {
        return this.updateStatus(inInfo
                , MesConstant.Status.K10
                , MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_NEWLY_STATUS
                , MesConstant.Status.K20
                , MessageCodeConstant.successMessage.MSG_SUCCESS_SUBMIT
                , true);
    }

    /**
     * 取消提交
     *
     * <p>状态已提交->新增
     */
    public EiInfo cancelSubmit(EiInfo inInfo) {
        return this.updateStatus(inInfo
                , MesConstant.Status.K20
                , MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS
                , MesConstant.Status.K10
                , MessageCodeConstant.successMessage.MSG_SUCCESS_CANCEL
                , false);
    }

    /**
     * 更新状态
     *
     * @param inInfo      前端参数
     * @param checkStatus 待校验状态
     * @param errMsg      状态不一致报错信息
     * @param updStatus   修改后状态
     * @param successMsg  成功信息
     * @param isCheck     是否校验数据
     * @return inInfo
     */
    private EiInfo updateStatus(EiInfo inInfo, String checkStatus, String errMsg, String updStatus, String successMsg, boolean isCheck) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0701 frontData;
            VGDM0701 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0701();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, errMsg, checkStatus);
                // 校验数据（提交时防止提交空数据）
                if (isCheck) {
                    this.checkData(dbData);
                }
                dbData.setFaultStatus(updStatus);
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                // 数据返回前端
                block.getRows().set(i, updMap);
            }
            DaoUtils.updateBatch(dao, VGDM0701.UPDATE, block.getRows());
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(successMsg);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 完成
     *
     * <p>状态确认->完成
     * 是否检修为是时生成检修计划
     */
    public EiInfo finish(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.DETAIL_STATUS_BLOCK);
            VGDM0701 frontData = new VGDM0701();
            frontData.fromMap(block.getRow(0));
            // 校验状态
            VGDM0701 dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_SUBMIT_STATUS, MesConstant.Status.K20);
            // 校验数据
            this.checkFinish(frontData, dbData);

            //校验动火文件是否存在
            if(frontData.getIsHot().equals("1")){
                Boolean fileExist = checkUpdateFile(dbData,"isHot");
                if (!fileExist) {
                    throw new PlatException("请上传动火文件");
                }
            }
            //校验检修文件是否存在 如果加工类型是委外，则必须要有文件
            if(frontData.getOverhaulType().equals("20")){
                Boolean fileExist = checkUpdateFile(dbData,"isOverhauType");
                if (!fileExist) {
                    throw new PlatException("请上传安全交底文件");
                }
            }


            // 状态-完成
            dbData.setFaultStatus(MesConstant.Status.K40);
            // 更新数据
            Map updMap = dbData.toMap();
            RecordUtils.setRevisor(updMap);
            dao.update(VGDM0701.UPDATE, updMap);
            // 生成检修计划
            if ("1".equals(dbData.getIsOverhaul())) {
                createOverhaulPlan(dbData, updMap);
            }
            // 上传IMC
            VGDM0804.uploadToIMC(dao, null, Collections.singletonList(dbData.getFaultId()));
            // 生成设备报警知识库
            VGDM0602 vgdm0602 = new VGDM0602();
            vgdm0602.createFromFault(dao, updMap);
            // 成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CONFIRM);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 生成检修计划
     *
     * @param dbData 数据库中数据
     * @param data   更新后数据
     */
    private void createOverhaulPlan(VGDM0701 dbData, Map data) {
        VGDM0801 vgdm0801 = new VGDM0801();
        vgdm0801.fromMap(data);
        // 关联单据
        vgdm0801.setVoucherNum(dbData.getFaultId());
        // 检修来源-故障
        vgdm0801.setOverhaulSource("4");
        // 检修性质-临时检修
        vgdm0801.setOverhaulQuality("10");
        // 计划检修日期
        LocalDateTime startDateTime = LocalDateTime.parse(dbData.getFaultStartTime(), DateUtils.FORMATTER_14);
        vgdm0801.setOverhaulStartDate(startDateTime.format(DateUtils.FORMATTER_16));
        LocalDateTime endDateTime = LocalDateTime.parse(dbData.getFaultEndTime(), DateUtils.FORMATTER_14);
        vgdm0801.setOverhaulEndDate(endDateTime.format(DateUtils.FORMATTER_16));
        // 计划检修时间-计划检修时间分钟差值
        vgdm0801.setOverhaulTime(BigDecimal.valueOf(ChronoUnit.MINUTES.between(startDateTime, endDateTime)));
        // 计划检修人数-实际检修人数
        vgdm0801.setOverhaulNumber(dbData.getActualOverhaulNumber());
        // 计划检修项目-故障描述
        vgdm0801.setOverhaulProject(dbData.getFaultDesc());
        // 实际检修项目-处理措施
        vgdm0801.setActualLegacyProject(dbData.getHandleMeasures());
        // 是否完成-是
        vgdm0801.setIsComplete("1");
        // 是否符合标准-是
        vgdm0801.setIsConformStandard("1");
        // 实绩操作人信息
        vgdm0801.setActualsRevisor(vgdm0801.getRecRevisor());
        vgdm0801.setActualsRevisorName(vgdm0801.getRecRevisorName());
        vgdm0801.setActualsTime(vgdm0801.getRecReviseTime());
        // 新增数据
        vgdm0801.insertData(dao, true);
        // 更新资材领用
        Map<String, Object> updMap = new HashMap<>();
        updMap.put("voucherNum", vgdm0801.getOverhaulPlanId());
        updMap.put("faultId", dbData.getFaultId());
        dao.update(VGDM0804.UPDATE_VOUCHER_NUM, updMap);
    }

    /**
     * 校验完成数据
     *
     * @param vgdm0701 故障信息
     * @param dbData   数据库中数据
     */
    private void checkFinish(VGDM0701 vgdm0701, VGDM0701 dbData) {
        // 校验基础字段
        ValidationUtils.validateEntity(vgdm0701, ValidationUtils.Group2.class);
        dbData.setFaultEndTime(vgdm0701.getFaultEndTime());
        // 故障时间范围校验
        String startTime = dbData.getFaultStartTime();
        String endTime = vgdm0701.getFaultEndTime();
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, DateUtils.FORMATTER_14);
        LocalDateTime endDateTime = LocalDateTime.parse(endTime, endTime.length() > 14 ? DateUtils.FORMATTER_19 : DateUtils.FORMATTER_14);
        if (startDateTime.isAfter(endDateTime)) {
            throw new PlatException("故障结束时间不能早于故障开始时间");
        }
        // 赋值结束时间
        dbData.setFaultEndTime(endDateTime.format(DateUtils.FORMATTER_14));
        // 赋值通用字段
        dbData.setIsOverhaul(vgdm0701.getIsOverhaul());
        dbData.setHandleMeasures(vgdm0701.getHandleMeasures());
        // 检修相关字段校验
        if ("1".equals(vgdm0701.getIsOverhaul())) {
            // 校验检修字段
            ValidationUtils.validateEntity(vgdm0701, ValidationUtils.Group3.class);
            // 赋值检修字段
            dbData.setOverhaulType(vgdm0701.getOverhaulType());
            dbData.setImplementManName(vgdm0701.getImplementManName());
            dbData.setOutsourcingContactId(vgdm0701.getOutsourcingContactId());
            dbData.setOverhaulImplementDate(vgdm0701.getOverhaulImplementDate());
            dbData.setActualOverhaulNumber(vgdm0701.getActualOverhaulNumber());
            dbData.setActualOverhaulTime(vgdm0701.getActualOverhaulTime());
            dbData.setOfflinePartsGone(vgdm0701.getOfflinePartsGone());
            dbData.setIsHot(vgdm0701.getIsHot());
            dbData.setHotCardId(vgdm0701.getHotCardId());
        }
    }

    /**
     * 校验更新文件
     */
    private boolean checkUpdateFile(VGDM0701 vgdm0701,String type) {

        VGDM0802 vgdm0802 = new VGDM0802();
        vgdm0802.setRelevanceId(vgdm0701.getFaultId());
        if(type.equals("isHot")){
            vgdm0802.setRelevanceType("VGDM07H");
        }else{
            vgdm0802.setRelevanceType("VGDM07");
        }
        vgdm0802.setSegNo(vgdm0701.getSegNo());
        List fileList = dao.query(VGDM0802.QUERY, vgdm0802.toMap());
        if(CollectionUtils.isNotEmpty(fileList)){
            return true;
        }else{
            return false;
        }

    }


    /**
     * 加入设备履历
     */
    public EiInfo addResume(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
            VGDM0701 frontData;
            VGDM0701 dbData;
            for (int i = 0; i < block.getRowCount(); i++) {
                frontData = new VGDM0701();
                frontData.fromMap(block.getRow(i));
                dbData = DaoUtils.queryAndCheckStatus(dao, frontData, MessageCodeConstant.errorMessage.MSG_ERROR_ONLY_FINISH_STATUS, MesConstant.Status.K40);
                // 数据返回前端
                block.getRows().set(i, dbData.toMap());
            }
            // 加入设备履历
            VGDM0104.addHistory(block, VGDM0104.RelevanceType.FAULT);
            // 返回成功信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_ADD_RESUME);
        } catch (PlatException ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
