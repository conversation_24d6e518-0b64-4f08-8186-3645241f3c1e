package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.*;
import com.baosight.imom.vg.dm.domain.VGDM1005;
import com.baosight.imom.vi.pm.domain.VIPM0008;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 行车实绩清单管理
 */
public class ServiceLIDS1201 extends ServiceBase {


    private Gson gson = new Gson();

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS1201().eiMetadata);
        inInfo.addBlock("subResult").addBlockMeta(new LIDS1202().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS1201.QUERY, new LIDS1201());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 查询作业实绩子项
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySubResult(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS1202.QUERY, new LIDS1202(), false, new LIDS1202().eiMetadata, EiConstant.resultBlock, "subResult", "subResult");
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 接收UWB抓取信息，生成行车作业实绩
     *
     * @param inInfo
     * @return
     */
    public EiInfo receiveUWBAndGenerate(EiInfo inInfo) {
        //todo 800飞剪一次性抓取多个捆包，到现场需要确定它的抓取方式
        EiInfo outInfo = new EiInfo();
        //行车作业实绩主项
        LIDS1201 lids1201 = new LIDS1201();
        //行车作业实绩子表
        List<LIDS1202> insertBatchList = new ArrayList<>();
        try {
            // 获取参数
            Map paramMap = inInfo.getMap("paramMap");
            //吊装重量
            BigDecimal craneOperationWeight = NumberUtils.toBigDecimal(paramMap.get("weight"), BigDecimal.ZERO);
            lids1201.fromMap(paramMap);
            //行车实绩单号
            lids1201.setCraneResultId(SequenceGenerator.getNextSequence(LIDS1201.SEQ_ID, new String[]{lids1201.getSegNo(), ""}));
            //抓取流水号
            lids1201.setInboundSequenceId(StringUtils.defaultString((String) paramMap.get("grabSysId"), ""));
            //起始X轴坐标
            lids1201.setStartXPosition(StringUtils.defaultString((String) paramMap.get("x_value"), ""));
            //起始Y轴坐标
            lids1201.setStartYPosition(StringUtils.defaultString((String) paramMap.get("y_value"), ""));
            //起始Z轴坐标
            lids1201.setStartZPosition(StringUtils.defaultString((String) paramMap.get("z_value"), ""));
            //uuid
            lids1201.setUuid(UUIDUtils.getUUID());
            //状态为10(作业开始)
            lids1201.setStatus("10");

            //行车抓取的捆包
            List<String> packIdList = new ArrayList<>();

            /**
             * 先根据行车编号确定厂区厂房
             */
            //查询行车所属厂区厂房，XYZ轴坐标，查询所属区域
            HashMap queryMap = new HashMap();
            queryMap.put("segNo", lids1201.getSegNo());
            queryMap.put("craneId", lids1201.getCraneId());

            //查询行车所属区域
            List<LIDS0301> lids0301List = dao.query(LIDS0301.QUERY_BY_CRANE_ID, queryMap);
            if (CollectionUtils.isEmpty(lids0301List)) {
                throw new PlatException(queryMap.get("craneId") + ",查询行车所属厂区厂房失败,未查到记录！");
            }
            LIDS0301 lids0301 = lids0301List.get(0);
            queryMap.put("factoryArea", lids0301.getFactoryArea());
            queryMap.put("factoryBuilding", lids0301.getFactoryBuilding());
            queryMap.put("XPosition", lids1201.getStartXPosition());
            queryMap.put("YPosition", lids1201.getStartYPosition());
            queryMap.put("ZPosition", lids1201.getStartZPosition());

            //根据厂区厂房，XY轴坐标查询所属区域
            Map areaMap = UWBUtils.transitionArea(queryMap, dao);
            //区域类型
            String areaType = StringUtils.defaultString((String) areaMap.get("areaType"));
            //区域代码
            String areaCode = StringUtils.defaultString((String) areaMap.get("areaCode"));
            //区域名称
            String areaName = StringUtils.defaultString((String) areaMap.get("areaName"));
            //通道类型
            String aisleType = StringUtils.defaultString((String) areaMap.get("aisleType"));

            //设置行车作业实绩抓取起始区域代码
            lids1201.setStartAreaType(areaType);
            lids1201.setStartAreaCode(areaCode);
            lids1201.setStartAreaName(areaName);
            //区域类型-不为卸货通道 TODO ？查库存捆包
            Boolean checkAreaType = !"40".equals(areaType) && StringUtils.isBlank(aisleType);
            if (checkAreaType) {
                //库区，查询库位附属信息表
                if ("10".equals(areaType)) {
                    //根据XY轴坐标转换为库位附属信息
                    Map locationMap = UWBUtils.transitionLocation(queryMap, dao);
                    //区域类型
                    queryMap.put("areaType", areaType);
                    //仓库代码
                    queryMap.put("warehouseCode", locationMap.get("warehouseCode"));
                    //区域(库位)代码
                    queryMap.put("locationId", locationMap.get("locationId"));
                    //区域(库位)代码
                    queryMap.put("areaCode", locationMap.get("locationId"));

                    //若抓取区域为库位，设置行车作业实绩抓取起始区域代码为具体库位
                    lids1201.setStartAreaType(areaType);
                    lids1201.setStartAreaCode(MapUtils.getString(locationMap, "locationId"));
                    lids1201.setStartAreaName(MapUtils.getString(locationMap, "locationName"));
                } else {
                    //仓库代码
                    queryMap.put("warehouseCode", "");
                    //区域类型
                    queryMap.put("areaType", areaType);
                    //区域(库位)代码
                    queryMap.put("locationId", areaCode);
                    //区域(库位)代码
                    queryMap.put("areaCode", areaCode);
                }

                //抓取区域为机组模具台车或模具区
                if ("50".equals(areaType) || "65".equals(areaType) || "51".equals(areaType)) {
                    //行车作业实绩子项
                    LIDS1202 lids1202 = new LIDS1202();
                    lids1202.fromMap(paramMap);
                    //根据抓取的XYZ轴是模具区，查询MES模具库存信息获取模具编号
                    List<LIDS0801> lids0801s = dao.query(LIDS0801.QUERY_BY_XY, queryMap);
                    if (CollectionUtils.isNotEmpty(lids0801s)) {
                        LIDS0801 lids0801 = lids0801s.get(0);
                        //模具编号
                        lids1202.setMouldId(lids0801.getMouldId());
                        lids1202.setMouldName(lids0801.getMouldName());
                        //行车作业实绩号
                        lids1202.setCraneResultId(lids1201.getCraneResultId());
                        //行车作业实绩子项号
                        lids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{lids1202.getCraneResultId(), ""}));
                        //数量
                        lids1202.setQuantity(1);
                        //重量
                        lids1202.setNetWeight(craneOperationWeight);
                        //uuid
                        lids1202.setUuid(UUIDUtils.getUUID());
                        //状态为10(作业开始)
                        lids1202.setStatus("10");
                        lids1202.setPackId("空");
                        lids1202.setLabelId("空");

                        //TODO 吊装重量与模具重量一致，将抓取模具的XYZ,区域属性清空
                        if (craneOperationWeight.compareTo(lids0801.getMouldSpecWeightSum()) == 0) {
                            lids0801.setX_position(null);
                            lids0801.setY_position(null);
                            lids0801.setZ_position(null);
                            lids0801.setAreaType("");
                            lids0801.setAreaCode("");
                            lids0801.setAreaName("");
                        }
                        //更新模具库存行车作业实绩单号
                        lids0801.setCraneResultId(lids1201.getCraneResultId());
                        //TODO 以非事务方式调用方法，当前方法报错会回滚updateLids0801，updateLids0801方法报错捕捉处理后不影响当前事务
                        EiInfo updateInfo = new EiInfo();
                        updateInfo.set("lids0801", lids0801);
                        updateInfo.set(EiConstant.serviceName, "LIDS1201");
                        updateInfo.set(EiConstant.methodName, "updateLids0801");
                        updateInfo = XLocalManager.callNoTx(updateInfo);
                        if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                            log("行车释放修改库存模具失败：" + updateInfo.getMsg());
                            System.out.println("行车释放修改库存模具失败：" + updateInfo.getMsg());
                        }

                        //行车作业实绩子项
                        insertBatchList.add(lids1202);
                    } else {
                        //检查此部行车对应的行车作业实绩上是否有捆包号为空的数据，如果有为空的数据，则此作业实绩写入异常标记，后续要报警。
                        lids1201.setAbnormalFlag("1");
                    }

                    //根据抓取区域匹配行车作业清单
                    queryMap.put("startAreaType", areaType);
                    queryMap.put("startAreaCode", areaCode);
                    queryMap.put("startAreaName", areaName);
                    List<LIDS1101> lids1101s = dao.query(LIDS1101.GET_WORK_ORDER_LIST_BY_AREA, queryMap);
                    if (lids1101s.size() == 1) {
                        LIDS1101 lids1101 = lids1101s.get(0);
                        lids1201.setCraneOrderId(lids1101.getCraneOrderId());

                        //将作业清单状态修改为30(执行中)
                        lids1101.setStatus("30");
                        //同时将行车作业清单作业开始时间修改为行车抓取时间
                        lids1101.setStartTime(MapUtils.getString(paramMap, "uploadTime",DateUtil.curDateTimeStr14()));
                        //设置修改人信息为system
                        RecordUtils.setRevisorBeanSys(lids1101);
                        dao.update(LIDS1101.UPDATE_STATUS, lids1101);
                        dao.update(LIDS1102.UPDATE_STATUS, lids1101.toMap());
                    }
                } else {
                    //根据xyz轴查询MES库存，获取捆包信息(先排除Z轴条件查询)
                    queryMap.put("ZPosition", "");
                    List<LIDS0901> lids0901s = dao.query(LIDS0901.QUERY_PACKS_BY_AREA, queryMap);
                    //查询到多个捆包时,增加Z轴的条件再查询
                    queryMap.put("ZPosition", lids1201.getStartXPosition());
                    if (lids0901s.size() > 1) {
                        lids0901s = dao.query(LIDS0901.QUERY_PACKS_BY_AREA, queryMap);
                    }
                    //判断只查到一个捆包继续执行
                    if (CollectionUtils.isNotEmpty(lids0901s) && lids0901s.size() == 1) {
                        EiInfo sendInfo;
                        if ("63".equals(areaType)) {
                            //从退卷区抓取时，生成一个倒库的作业清单，目的区域根据库位推荐指定
                            sendInfo = new EiInfo();
                            sendInfo.set("segNo", lids1201.getSegNo());
                            sendInfo.set("packId", lids0901s.get(0).getPackId());
                            sendInfo.set("craneId", lids0301.getCraneId());
                            sendInfo.set("craneName", lids0301.getCraneName());
                            sendInfo.set(EiConstant.serviceName, "LIDS1101");
                            sendInfo.set(EiConstant.methodName, "finishedProductStorage1");
                            sendInfo = XLocalManager.callNewTx(sendInfo);
                            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                                log("调用LIDS1101#finishedProductStorage1失败：" + sendInfo.getMsg());
                                System.out.println("调用LIDS1101#finishedProductStorage1失败：" + sendInfo.getMsg());
                            }
                        }

                        LIDS0901 lids0901 = lids0901s.get(0);
                        //卷
                        if ("1".equals(lids0901.getActionFlag())) {
                            //行车作业实绩子项
                            LIDS1202 lids1202 = new LIDS1202();
                            lids1202.fromMap(paramMap);
                            //设置抓取的当前捆包号
                            lids1202.setPackId(lids0901.getPackId());
                            //行车作业实绩号
                            lids1202.setCraneResultId(lids1201.getCraneResultId());
                            //行车作业实绩子项号
                            lids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{lids1202.getCraneResultId(), ""}));
                            //净重
                            lids1202.setNetWeight(craneOperationWeight);
                            //捆包数量
                            lids1202.setQuantity(lids0901.getQuantity());
                            //标签号
                            lids1202.setLabelId(lids0901.getLabelId());
                            //uuid
                            lids1202.setUuid(UUIDUtils.getUUID());
                            //状态为10(作业开始)
                            lids1202.setStatus("10");

                            //行车作业实绩子项
                            insertBatchList.add(lids1202);

                            //add到List中，后面清空捆包信息
                            packIdList.add(lids0901.getPackId());

                            //从库位抓取时，调用库位推荐释放方法
                            if("10".equals(areaType)){
                                queryMap.clear();
                                //校验在LIDS0605表中捆包是否存在数据
                                queryMap.put("segNo",lids0901.getSegNo());
                                queryMap.put("warehouseCode",lids0901.getWarehouseCode());
                                queryMap.put("locViewPackId",lids0901.getPackId());
                                queryMap.put("useStatus","30");//30：占用
                                List<LIDS0605> lids0605s = dao.query(LIDS0605.QUERY, queryMap);
                                if(CollectionUtils.isNotEmpty(lids0605s)){
                                    sendInfo = new EiInfo();
                                    Map messageBody = new HashMap();
                                    messageBody.put("segNo",lids0901.getSegNo());
                                    messageBody.put("packId",lids0901.getPackId());
                                    messageBody.put("warehouseCode",lids0901.getWarehouseCode());

                                    sendInfo.set("messageBody",messageBody);
                                    sendInfo.set(EiConstant.serviceName, "LIDS0606");
                                    sendInfo.set(EiConstant.methodName, "warehouseRelease");
                                    sendInfo = XLocalManager.callNewTx(sendInfo);
                                    if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                                        log("调用LIDS0606.warehouseRelease："+sendInfo.getMsg());
                                        System.out.println("调用LIDS0606.warehouseRelease："+sendInfo.getMsg());
                                    }
                                }
                            }

                        } else if ("0".equals(lids0901.getActionFlag())) {
                            //板(根据UWB传入吊装重量与库存捆包吊装重量比较，判断取了几个捆包)
                            BigDecimal tolerance = new BigDecimal(0.05); // 5% 的容差

                            // 计算允许的最大和最小重量范围
                            BigDecimal minWeight = craneOperationWeight.multiply(BigDecimal.ONE.subtract(tolerance));
                            BigDecimal maxWeight = craneOperationWeight.multiply(BigDecimal.ONE.add(tolerance));

                            // 记录累加的捆包信息
                            List<LIDS0901> accumulatedBales = new ArrayList<>();

                            // 累加的捆包数量
                            BigDecimal accumulatedWeight = new BigDecimal("0");
                            int numberOfBales = 0;

                            for (LIDS0901 lids09011 : lids0901s) {
                                accumulatedWeight = accumulatedWeight.add(lids09011.getNetWeight());
                                numberOfBales++;
                                accumulatedBales.add(lids09011);

                                // 如果累加的重量超过最大允许重量，减少最后一个捆包的重量
                                if (accumulatedWeight.compareTo(maxWeight) > 0) {
                                    accumulatedWeight = accumulatedWeight.subtract(lids09011.getNetWeight());
                                    numberOfBales--;
                                    accumulatedBales.remove(accumulatedBales.size() - 1);
                                    break;
                                }

                                // 如果累加的重量达到或超过最小允许重量，停止累加
                                if (accumulatedWeight.compareTo(minWeight) >= 0) {
                                    break;
                                }
                            }

                            // 抓取捆包数量
                            if (accumulatedWeight.compareTo(minWeight) >= 0 && accumulatedWeight.compareTo(maxWeight) <= 0) {
                                System.out.println(numberOfBales);
                            } else {
                                System.out.println(-1);
                            }

                            //抓取的所有板
                            for (LIDS0901 lids09012 : accumulatedBales) {
                                //行车作业实绩子项
                                LIDS1202 lids1202 = new LIDS1202();
                                lids1202.fromMap(paramMap);
                                //设置抓取的当前捆包号
                                lids1202.setPackId(lids09012.getPackId());
                                //行车作业实绩号
                                lids1202.setCraneResultId(lids1201.getCraneResultId());
                                //行车作业实绩子项号
                                lids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{lids1202.getCraneResultId(), ""}));
                                //净重
                                lids1202.setNetWeight(lids09012.getNetWeight());
                                //捆包数量
                                lids1202.setQuantity(lids09012.getQuantity());
                                //标签号
                                lids1202.setLabelId(lids09012.getLabelId());
                                //uuid
                                lids1202.setUuid(UUIDUtils.getUUID());
                                //状态为10(作业开始)
                                lids1202.setStatus("10");

                                //行车作业实绩子项
                                insertBatchList.add(lids1202);
                            }

                            //add到List中，后面清空捆包信息
                            packIdList = accumulatedBales.stream()
                                    .map(LIDS0901::getPackId)
                                    .collect(Collectors.toList());
                        }

                        //保存履历表并清空库存，XYZ轴，区域类型，区域代码，和区域名称(顺带更新一下行车作业实绩单号)
                        if (CollectionUtils.isNotEmpty(packIdList)) {
                            sendInfo = new EiInfo();
                            sendInfo.set("segNo", lids1201.getSegNo());
                            sendInfo.set("craneResultId", lids1201.getCraneResultId());
                            sendInfo.set("packIdList", packIdList);
                            sendInfo.set("actionFlag", lids0901.getActionFlag());
                            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
                            sendInfo.set(EiConstant.methodName, "resetBundleDetails");
                            sendInfo = XLocalManager.callNewTx(sendInfo);
                            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                                log("ServiceLIDSInterfaces.generateCraneOrder调用清空捆包信息传入参数：" + lids1201.getSegNo() + "-" + lids1201.getCraneResultId() + "-" + packIdList + "返回的参数：" + sendInfo.toJSONString());
                                System.out.println("ServiceLIDSInterfaces.generateCraneOrder调用清空捆包信息传入参数：" + lids1201.getSegNo() + "-" + lids1201.getCraneResultId() + "-" + packIdList + "返回的参数：" + sendInfo.toJSONString());
                            }
                        }

                        //查询行车作业清单
                        queryMap.clear();
                        queryMap.put("segNo", lids0901.getSegNo());
                        //从拆包区抓取，不使用行车条件
                        if(!"30".equals(areaType)){
                            queryMap.put("craneId", lids1201.getCraneId());
                        }
                        queryMap.put("packId", lids0901.getPackId());
                        List<LIDS1101> lids1101List = dao.query(LIDS1101.GET_WORK_ORDER_LIST_BY_PACK_ID, queryMap);
                        //匹配到唯一的行车作业清单
                        if (lids1101List.size() == 1) {
                            LIDS1101 lids1101 = lids1101List.get(0);
                            //写入行车作业实绩的行车作业单号
                            lids1201.setCraneOrderId(lids1101.getCraneOrderId());

                            //更新行车作业清单状态为30(作业中)
                            lids1101.setStatus("30");
                            //设置修改人信息为system
                            RecordUtils.setRevisorBeanSys(lids1101);
                            dao.update(LIDS1101.UPDATE_STATUS, lids1101);
                            dao.update(LIDS1102.UPDATE_STATUS, lids1101.toMap());
                        }

                        if ("20".equals(areaType)) {
                            //TODO 根据捆包号，区域代码，区域名称查询占用状态的过跨小车
                            queryMap.clear();
                            queryMap.put("segNo", lids1201.getSegNo());
                            queryMap.put("packId", lids0901.getPackId());
                            queryMap.put("crossingChannels", areaCode);
                            queryMap.put("crossingChannelsName", areaName);
                            queryMap.put("factoryArea", lids0301.getFactoryArea());
                            queryMap.put("factoryBuilding", lids0301.getFactoryBuilding());
                            queryMap.put("status", "20");//状态为20(占用)
                            List<LIDS0401> lids0401s = dao.query(LIDS0401.QUERY_BY_CHANNEL, queryMap);
                            if (CollectionUtils.isNotEmpty(lids0401s)) {
                                //将过跨小车状态修改为空闲并清空捆包号
                                LIDS0401 lids0401 = lids0401s.get(0);
                                lids0401.setStatus("10");
                                lids0401.setPackId("");
                                //设置修改人信息为system
                                RecordUtils.setRevisorBeanSys(lids0401);
                                dao.update(LIDS0401.UPDATE_STATUS, lids0401);
                            }
                        } else if ("30".equals(areaType)) {
                            //TODO 根据捆包号，区域代码，区域名称查询拆包作业清单
                            queryMap.clear();
                            queryMap.put("segNo", lids1201.getSegNo());
                            queryMap.put("packId", lids0901.getPackId());
                            queryMap.put("unpackAreaId", lids0901.getAreaCode());
                            queryMap.put("unpackAreaName", lids0901.getAreaName());
                            List<LIDS1001> lids1001s = dao.query(LIDS1001.QUERY, queryMap);
                            if (CollectionUtils.isNotEmpty(lids1001s)) {
                                //更新拆包作业清单的离开时间
                                LIDS1001 lids1001 = lids1001s.get(0);
                                lids1001.setDepartureTime(DateUtil.curDateTimeStr14());
                                //设置修改人信息为system
                                RecordUtils.setRevisorBeanSys(lids1001);
                                dao.update(LIDS1001.UPDATE_DEPARTURE_TIME, lids1001);
                            }
                        }
                    } else {
                        try {
                            if ("61".equals(areaType)) {
                                EiInfo iInfo = new EiInfo();
                                iInfo.set("paramMap", paramMap);
                                iInfo.set("segNo", lids1201.getSegNo());
                                iInfo.set("factoryArea", lids0301.getFactoryArea());
                                iInfo.set("factoryBuilding", lids0301.getFactoryBuilding());
                                iInfo.set("areaType", lids1201.getStartAreaType());
                                iInfo.set("areaCode", lids1201.getStartAreaCode());
                                iInfo.set("areaName", lids1201.getStartAreaName());
                                iInfo.set("maxPosDirCode", "1");
                                iInfo.set("xPosition", lids1201.getStartXPosition());
                                iInfo.set("yPosition", lids1201.getStartYPosition());
                                iInfo.set("craneResultId", lids1201.getCraneResultId());
                                iInfo.set("craneOperationWeight", craneOperationWeight);
                                iInfo.set(EiConstant.serviceName, "LIDS1201");
                                iInfo.set(EiConstant.methodName, "autoMatchBundlesForTransport");
                                iInfo = XLocalManager.callNoTx(iInfo);
                                if (iInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                                    throw new Exception(iInfo.getMsg());
                                }
                                insertBatchList = (List<LIDS1202>) iInfo.get("insertBatchList");
                                if(CollectionUtils.isEmpty(insertBatchList)){
                                    //检查此部行车对应的行车作业实绩上是否有捆包号为空的数据，如果有为空的数据，则此作业实绩写入异常标记，后续要报警。
                                    lids1201.setAbnormalFlag("1");
                                }
                            }else{
                                //行车作业实绩子项
                                LIDS1202 lids1202 = new LIDS1202();
                                lids1202.fromMap(paramMap);
                                //设置抓取的当前捆包号
                                lids1202.setPackId("");
                                //行车作业实绩号
                                lids1202.setCraneResultId(lids1201.getCraneResultId());
                                //行车作业实绩子项号
                                lids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{lids1202.getCraneResultId(), ""}));
                                //净重
                                lids1202.setNetWeight(craneOperationWeight);
                                //捆包数量
                                lids1202.setQuantity(1);
                                //标签号
                                lids1202.setLabelId("");
                                //uuid
                                lids1202.setUuid(UUIDUtils.getUUID());
                                //状态为10(作业开始)
                                lids1202.setStatus("10");

                                //行车作业实绩子项
                                insertBatchList.add(lids1202);

                                //检查此部行车对应的行车作业实绩上是否有捆包号为空的数据，如果有为空的数据，则此作业实绩写入异常标记，后续要报警。
                                lids1201.setAbnormalFlag("1");
                            }
                        }catch (Exception e){
                            log("下料自动匹配捆包失败：" + e.getMessage());
                            System.out.println("下料自动匹配捆包失败：" + e.getMessage());
                        }
                    }
                }

            } else {
                //检查此部行车对应的行车作业实绩上是否有捆包号为空的数据，如果有为空的数据，则此作业实绩写入异常标记，后续要报警。
                //int count = dao.count(LIDS1202.RESULT_PACKISNULL, lids1201);
                //if (count > 0) {
                //卸货入库捆包号必定为空，写上异常标记
                lids1201.setAbnormalFlag("1");
                //}

                //行车作业实绩子项
                LIDS1202 lids1202 = new LIDS1202();
                lids1202.fromMap(paramMap);
                //当前捆包号设置为空
                lids1202.setPackId("");
                //行车作业实绩号
                lids1202.setCraneResultId(lids1201.getCraneResultId());
                //行车作业实绩子项号
                lids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{lids1202.getCraneResultId(), ""}));
                //净重
                lids1202.setNetWeight(craneOperationWeight);
                //捆包数量
                lids1202.setQuantity(1);
                //uuid
                lids1202.setUuid(UUIDUtils.getUUID());
                //状态为10(作业开始)
                lids1202.setStatus("10");

                //行车作业实绩子项
                insertBatchList.add(lids1202);
            }

            /*if(1==1){
              throw new RuntimeException("测试事务报错，捆包区域，XYZ应该清空，抓取记录正常！");
            }*/
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            String msg = ex.getMessage();
            if (StringUtils.isNotBlank(msg) && msg.length() < 200) {
                outInfo.setMsg("处理行车抓取实绩推送失败，请查看链路！链路ID:" + inInfo.getTraceId() + ",报错(小于200位才打印):" + msg);
            } else {
                outInfo.setMsg("处理行车抓取实绩推送失败，请查看链路！链路ID:" + inInfo.getTraceId());
            }
        }finally {
            //设置修改人信息为system
            RecordUtils.setCreatorBeanSys(lids1201);
            //以非事务方式调用修改方法，若报错不回滚
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDS1201");
            sendInfo.set(EiConstant.methodName, "updateLids1201");
            sendInfo.set("lids1201", lids1201);//行车实绩主项
            sendInfo.set("insertBatchList", insertBatchList);//行车实绩子项
            sendInfo.set("type", "I");//新增
            sendInfo = XLocalManager.callNoTx(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                log("修改行车作业实绩信息失败：" + lids1201.getCraneResultId());
                System.out.println("修改行车作业实绩信息失败：" + lids1201.getCraneResultId());
            }
        }
        return outInfo;
    }


    /**
     * 接收UWB释放信息，修改行车作业实绩并写入实物库存表
     *
     * @param inInfo
     * @return
     */
    public EiInfo receiveUWBAndUpdate(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        LIDS1201 lids1201 = new LIDS1201();
        try {
            // 获取参数
            Map paramMap = inInfo.getMap("paramMap");

            //根据抓取流水号，查询行车作业实绩
            HashMap queryMap = new HashMap();
            queryMap.put("segNo", StringUtils.defaultString((String) paramMap.get("segNo"), ""));
            queryMap.put("inboundSequenceId", StringUtils.defaultString((String) paramMap.get("grabSysId"), ""));
            List<LIDS1201> lids1201List = dao.query(LIDS1201.QUERY, queryMap);
            if (lids1201List.size() != 1) {
                throw new PlatException("根据抓取流水号(依据凭单号)，查询行车作业实绩失败,查询返回：" + (lids1201List.size() == 0 ? 0 : lids1201List.size()) + "条");
            }
            //修改行车作业实绩
            lids1201 = lids1201List.get(0);
            //状态为20(作业结束)
            lids1201.setStatus("20");
            //释放流水号
            lids1201.setOutboundSequenceId(StringUtils.defaultString((String) paramMap.get("releaseSysId"), ""));
            //终到X轴坐标
            lids1201.setEndxPosition(StringUtils.defaultString((String) paramMap.get("x_value"), ""));
            //终到Y轴坐标
            lids1201.setEndyPosition(StringUtils.defaultString((String) paramMap.get("y_value"), ""));
            //终到Z轴坐标
            lids1201.setEndzPosition(StringUtils.defaultString((String) paramMap.get("z_value"), ""));
            // 修改人工号
            lids1201.setRecRevisor(StringUtils.defaultString((String) paramMap.get("recRevisorName"), "system"));
            // 修改人姓名
            lids1201.setRecRevisorName(StringUtils.defaultString((String) paramMap.get("recRevisorName"), "system"));
            // 修改时间
            lids1201.setRecReviseTime(StringUtils.defaultString((String) paramMap.get("uploadTime"), DateUtil.curDateTimeStr14()));

            //查询行车作业实绩子项
            queryMap.clear();
            queryMap.put("segNo", lids1201.getSegNo());
            queryMap.put("craneResultId", lids1201.getCraneResultId());
            queryMap.put("status", "10");
            List<LIDS1202> lids1202List = dao.query(LIDS1202.QUERY, queryMap);

            if (CollectionUtils.isEmpty(lids1202List)) {
                throw new PlatException("查询行车作业实绩子项失败,传入行车作业实绩单号为：" + lids1201.getCraneResultId() + ",返回：" + (lids1202List.size() == 0 ? 0 : lids1202List.size()) + "条");
            }
            LIDS1202 lids1202 = lids1202List.get(0);

            //写入实物库存表(模具操作时，这个lids0901不做修改与新增操作)
            LIDS0901 lids0901 = new LIDS0901();
            lids0901.setUnitCode(lids1201.getUnitCode());
            lids0901.setSegNo(lids1201.getSegNo());
            lids0901.setCraneResultId(lids1201.getCraneResultId());
            lids0901.setX_position(lids1201.getEndxPosition());
            lids0901.setY_position(lids1201.getEndyPosition());
            lids0901.setZ_position(lids1201.getEndzPosition());
            lids0901.setRecRevisor(lids1201.getRecRevisor());
            lids0901.setStatus("10");
            lids0901.setRecRevisorName(lids1201.getRecRevisorName());
            lids0901.setRecReviseTime(lids1201.getRecReviseTime());
            //捆包号
            lids0901.setPackId(StringUtils.isNotBlank(lids1202.getPackId()) ? lids1202.getPackId() : "");
            //并包号
            lids0901.setUnitedPackId("");
            //标签号
            lids0901.setLabelId(StringUtils.isNotBlank(lids1202.getLabelId()) ? lids1202.getLabelId() : "");
            //吊装重量
            lids0901.setCraneOperationWeight(lids1202.getNetWeight());
            //层数标记(先默认为1)
            lids0901.setPosDirCode("1");
            //查询参数
            queryMap.clear();
            queryMap.put("segNo", lids1201.getSegNo());
            queryMap.put("XPosition", lids1201.getEndxPosition());
            queryMap.put("YPosition", lids1201.getEndyPosition());
            queryMap.put("ZPosition", lids1201.getEndzPosition());


            /**
             * 查询行车所属厂区厂房，再根据XY轴坐标转换为区域
             * */
            queryMap.put("craneId", StringUtils.defaultString((String) paramMap.get("craneId"), ""));
            //查询所属区域
            List<LIDS0301> lids0301List = dao.query(LIDS0301.QUERY_BY_CRANE_ID, queryMap);
            String factoryArea = "";
            String factoryBuilding = "";
            String crossArea = "";
            if (CollectionUtils.isNotEmpty(lids0301List)) {
                //根据厂区厂房，XY轴坐标查询所属区域
                LIDS0301 lids0301 = lids0301List.get(0);
                factoryArea = lids0301.getFactoryArea();
                factoryBuilding = lids0301.getFactoryBuilding();
                crossArea = lids0301.getCrossArea();
            }
            queryMap.put("factoryArea", factoryArea);
            queryMap.put("factoryBuilding", factoryBuilding);
            /**
             *根据抓取和释放判断是库位到库位，系统匹配倒库的行车作业清单（生效状态），若匹配到唯一的，则反写行车作业清单号，并更新行车作业清单状态为40完成。
             * （要按照释放的区域位置找相应类型的行车作业清单）
             */
            //终到区域
            Map areaMap = UWBUtils.transitionArea(queryMap, dao);
            String endAreaType = StringUtils.defaultString((String) areaMap.get("areaType"), "");

            //终到区域代码，区域名称
            String endAreaCode = StringUtils.defaultString((String) areaMap.get("areaCode"), "");
            String endAreaName = StringUtils.defaultString((String) areaMap.get("areaName"), "");
            //终到区域装卸货类型
            String endAisleType = StringUtils.defaultString((String) areaMap.get("aisleType"), "");
            //起始区域
            queryMap.put("XPosition", lids1201.getStartXPosition());
            queryMap.put("YPosition", lids1201.getStartYPosition());
            areaMap = UWBUtils.transitionArea(queryMap, dao);
            //起始区域类型
            String startAreaType = StringUtils.defaultString((String) areaMap.get("areaType"), "");
            //起始区域代码，区域名称
            String startAreaCode = StringUtils.defaultString((String) areaMap.get("areaCode"), "");
            String startAreaName = StringUtils.defaultString((String) areaMap.get("areaName"), "");
            //起始区域装卸货类型
            String startAisleType = StringUtils.defaultString((String) areaMap.get("aisleType"), "");

            //根据起始区域->终到区域获取操作方式（清单来源）
            String listSource = "";
            listSource = UWBUtils.getListSource(listSource, startAreaType, endAreaType, startAisleType, endAisleType);
            log("起始至终到转换本次操作为：" + listSource);
            System.out.println("起始至终到转换本次操作为：" + listSource);

            //区域类型
            lids0901.setAreaType(endAreaType);
            lids0901.setAreaCode(endAreaCode);
            lids0901.setAreaName(endAreaName);
            //重新设值为终到坐标
            queryMap.put("XPosition", lids1201.getEndxPosition());
            queryMap.put("YPosition", lids1201.getEndyPosition());
            queryMap.put("ZPosition", lids1201.getEndzPosition());
            //区域类型为库区，查询库位
            if ("10".equals(endAreaType)) {
                //根据XY轴坐标转换为库位附属信息
                Map locationMap = UWBUtils.transitionLocation(queryMap, dao);
                lids0901.setWarehouseCode(StringUtils.defaultString((String) locationMap.get("warehouseCode"), ""));
                lids0901.setWarehouseName(StringUtils.defaultString((String) locationMap.get("warehouseName"), ""));
                lids0901.setAreaCode(StringUtils.defaultString((String) locationMap.get("locationId"), ""));
                lids0901.setAreaName(StringUtils.defaultString((String) locationMap.get("locationName"), ""));
                //层数标记
                lids0901.setPosDirCode(StringUtils.defaultString((String) locationMap.get("posDirCode"), ""));
            }
            //重置查询条件
            queryMap.clear();
            /**
             * 卸货入库，先不对行车作业清单做任何处理，PDA卸货确认时处理
             */
            //非卸货入库
            if (!"10".equals(listSource)) {
                //且抓取时匹配到行车作业清单
                if (StringUtils.isNotBlank(lids1201.getCraneOrderId())) {
                    //更新行车作业清单状态为40完成
                    LIDS1101 lids1101 = new LIDS1101();
                    lids1101.setSegNo(lids1201.getSegNo());
                    lids1101.setCraneOrderId(lids1201.getCraneOrderId());
                    lids1101.setStatus("40");
                    lids1101.setEndTime(DateUtil.curDateTimeStr14());
                    //设置修改人信息为system
                    RecordUtils.setRevisorBeanSys(lids1101);
                    dao.update(LIDS1101.UPDATE_STATUS, lids1101);
                    dao.update(LIDS1102.UPDATE_STATUS, lids1101.toMap());
                } else {//抓取时未匹配行车作业清单
                    //根据起始终到区域匹配行车作业清单
                    queryMap.put("startAreaType", startAreaType);
                    queryMap.put("startAreaCode", startAreaCode);
                    queryMap.put("startAreaName", startAreaName);

                    queryMap.put("endAreaType", endAreaType);
                    queryMap.put("endAreaCode", endAreaCode);
                    queryMap.put("endAreaName", endAreaName);
                    //查询行车作业清单
                    queryMap.put("segNo", lids1201.getSegNo());
                    if(!"30".equals(endAreaType)){
                        queryMap.put("craneId", lids1201.getCraneId());
                    }
                    queryMap.put("packId", lids1202.getPackId());
                    queryMap.put("listSource", listSource);
                    queryMap.put("status", "30");//生效状态
                    List<LIDS1101> lids1101List = dao.query(LIDS1101.GET_WORK_ORDER_LIST_BY_PACK_ID, queryMap);

                    //库区到发货通道优先认为是成品发货，匹配不到再认为是厂内转运
                    if ("20".equals(listSource) && CollectionUtils.isEmpty(lids1101List)) {
                        //60:厂内转运
                        listSource = "60";
                        queryMap.put("listSource", listSource);
                        //重新查询厂内转运的行车作业清单
                        lids1101List = dao.query(LIDS1101.GET_WORK_ORDER_LIST_BY_PACK_ID, queryMap);
                        //20:成品发货
                        listSource = "20";
                    }
                    if (CollectionUtils.isNotEmpty(lids1101List)) {
                        LIDS1101 lids1101 = lids1101List.get(0);
                        //回写行车作业清单
                        lids1201.setCraneOrderId(lids1101.getCraneOrderId());
                        lids1201.setRecReviseTime(DateUtil.curDateTimeStr14());
                        dao.update(LIDS1201.UPDATE_WORK_ORDER, lids1201);
                        //并更新行车作业清单状态为40完成
                        lids1101.setStatus("40");
                        lids1101.setEndTime(DateUtil.curDateTimeStr14());
                        //设置修改人信息为system
                        RecordUtils.setRevisorBeanSys(lids1101);
                        dao.update(LIDS1101.UPDATE_STATUS, lids1101);
                        dao.update(LIDS1102.UPDATE_STATUS, lids1101.toMap());
                    }
                }
            }

            //查询当前捆包是否存在其它生效状态的行车作业清单，同时将生效状态的行车作业清单置为撤销
            if (StringUtils.isNotBlank(lids1202.getPackId())) {
                queryMap.clear();
                queryMap.put("segNo", lids1201.getSegNo());
                queryMap.put("packId", lids1202.getPackId());
                List<HashMap> otherActiveCraneOrders = dao.query(LIDS1101.QUERY_ACTIVE_CRANE_ORDERS, queryMap);
                if (CollectionUtils.isNotEmpty(otherActiveCraneOrders)) {
                    for (HashMap craneOrder : otherActiveCraneOrders) {
                        //状态置为撤销
                        craneOrder.put("status", "00");
                        craneOrder.put("delFlag", "1");
                        //设置修改人信息
                        RecordUtils.setRevisorByCall(craneOrder);
                    }
                    //打印日志
                    log("行车释放捆包时将其它生效状态的行车作业清单置为撤销:" + otherActiveCraneOrders);
                    System.out.println("行车释放捆包时将其它生效状态的行车作业清单置为撤销:" + otherActiveCraneOrders);
                    dao.updateBatch(LIDS1101.UPDATE_STATUS, otherActiveCraneOrders);
                    dao.updateBatch(LIDS1102.UPDATE_STATUS, otherActiveCraneOrders);
                }
            }



            /**
             * 根据不同清单来源,做后续操作(前面若终到区域为库区,会更新捆包库位)
             * 10:卸货入库:插入库存表,后续PDA卸货确认时回写捆包号与行车作业清单号
             * 20:成品发货:修改库存表状态为出库
             * 30:生产上料:
             *    1.由库区到过跨小车时,将过跨小车状态修改为占用,占用捆包为当前捆包,库存捆包当前区域为过跨小车编号名称;
             *    2.过跨小车到拆包区,修改库存捆包区域为拆包区信息,生成相应拆包作业清单;
             *    3.拆包区到上料区,修改库存捆包区域为机组上料区;
             * 40:加工退卷:
             *    1.修改库存捆包所属区域为退卷区区域代码，区域名称;
             *    2.如果是退卷区->库区，要联动IMC进行倒库;(在抓取捆包时，生成倒库的作业清单)
             * 50:倒库:
             *    1.更新库存捆包库位;(终到区域类型为库位，前面会根据XY转换为库位代码，所以直接修改)
             *    2.需要先根据行车作业实绩单号查询所有捆包，再联动IMC进行倒库;
             * 60:厂内转运
             * 70:模具更换
             *    1.若吊装重量不等于模具重量,模具位置不变;
             *    2.更新模具位置;
             * 80:模具维修操作同70
             * 90:机组下料
             *   修改捆包库存信息时，以行车实绩单号作为条件
             */
            if ("10".equals(listSource)) {//卸货入库
                lids0901.setArchiveFlag(lids1201.getArchiveFlag());
                lids0901.setTenantUser(lids1201.getTenantUser());
                lids0901.setDelFlag(0);
                lids0901.setRecCreator(lids1201.getRecCreator());
                lids0901.setRecCreatorName(lids1201.getRecCreatorName());
                lids0901.setRecCreateTime(lids1201.getRecCreateTime());
                //吊装重量
                lids0901.setCraneOperationWeight(lids1202.getNetWeight());
                //净重
                lids0901.setNetWeight(new BigDecimal("0"));
                //毛重
                lids0901.setGrossWeight(new BigDecimal("0"));
                //数量
                lids0901.setQuantity(new Integer("1"));
                //板卷标记
                lids0901.setActionFlag("");
                //内外版标记
                lids0901.setInnerOutterPlateFlag("");
                //uuid
                lids0901.setUuid(UUIDUtils.getUUID());
                //插入库存表
                dao.insert(LIDS0901.INSERT, lids0901);
            } else if ("20".equals(listSource)) {//成品发货
                //TODO 以非事务方式调用方法更新实物库存，当前方法报错会回滚updateLids0901，updateLids0901方法报错捕捉处理后不影响当前事务
                EiInfo updateInfo = new EiInfo();
                updateInfo.set("lids0901", lids0901);
                updateInfo.set(EiConstant.serviceName, "LIDS1201");
                updateInfo.set(EiConstant.methodName, "updateLids0901");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                }

                //修改库存表状态为出库
                lids0901.setStatus("30");
                lids0901.setDelFlag(1);
                //出库时调用，将当前捆包记录插入库存备份表，且修改库存表状态为出库，删除标记为1
                Map itemMap = lids0901.toMap();
                itemMap.put("outMark","1");
                updateInfo = new EiInfo();
                updateInfo.set("queryMap", itemMap);
                updateInfo.set(EiConstant.serviceName, "LIDSInterfaces");
                updateInfo.set(EiConstant.methodName, "inertInterfaceList");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                }
            } else if ("30".equals(listSource)) {//生产上料
                //还要根据终到区域类型进行判断生产上料阶段
                if ("20".equals(endAreaType)) {
                    //TODO 根据厂区代码，厂房代码，过跨通道编码查询过跨小车
                    queryMap.clear();
                    queryMap.put("segNo", lids1201.getSegNo());
                    queryMap.put("factoryArea", factoryArea);
                    queryMap.put("factoryBuilding", factoryBuilding);
                    queryMap.put("crossingChannels", endAreaCode);
                    queryMap.put("crossingChannelsName", endAreaName);
                    List<LIDS0401> lids0401List = dao.query(LIDS0401.QUERY_BY_CHANNEL, queryMap);
                    if (CollectionUtils.isNotEmpty(lids0401List)) {
                        LIDS0401 lids0401 = lids0401List.get(0);
                        /*//区域代码，区域名称为过跨小车ID，过跨小车名称
                        lids0901.setAreaType(endAreaType);
                        lids0901.setAreaCode(lids0401.getTransferCarId());
                        lids0901.setAreaName(lids0401.getTransferCarName());*/
                        //更新过跨小车，状态为占用，占用捆包为当前抓取捆包
                        lids0401.setStatus("20");
                        lids0401.setPackId(lids0901.getPackId());
                        //设置修改人信息为system
                        RecordUtils.setRevisorBeanSys(lids0401);
                        dao.update(LIDS0401.UPDATE_STATUS, lids0401);
                    }

                } else if ("30".equals(endAreaType)) {
                    /*//TODO 根据厂区代码，厂房代码，跨区代码查询拆包区(直接用行车区域XY代码)
                    queryMap.clear();
                    queryMap.put("segNo", lids1201.getSegNo());
                    queryMap.put("areaCode", endAreaCode);
                    queryMap.put("areaName", endAreaName);
                    queryMap.put("factoryArea", factoryArea);
                    queryMap.put("factoryBuilding", factoryBuilding);
                    queryMap.put("crossArea", crossArea);
                    List<LIDS0501> lids0501List = dao.query(LIDS0501.QUERY_BY_CROSS_AREA, queryMap);
                    if (CollectionUtils.isNotEmpty(lids0501List)) {
                        LIDS0501 lids0501 = lids0501List.get(0);*/
                        //区域代码，区域名称为拆包区编号，拆包区名称
                        lids0901.setAreaType(endAreaType);
                        lids0901.setAreaCode(endAreaCode);
                        lids0901.setAreaName(endAreaName);
                        //TODO 同时生成拆包作业清单
                        LIDS1001 lids1001 = new LIDS1001();
                        lids1001.setSegNo(lids1201.getSegNo());
                        lids1001.setUnitCode(lids1201.getUnitCode());
                        lids1001.setUnpackOrderId(SequenceGenerator.getNextSequence(LIDS1001.SEQ_ID, new String[]{lids0901.getSegNo(), ""}));
                        lids1001.setPackId(lids0901.getPackId());
                        lids1001.setUnpackAreaId(endAreaCode);
                        lids1001.setUnpackAreaName(endAreaName);
                        lids1001.setStatus("10");
                        lids1001.setArrivalTime(DateUtil.curDateTimeStr14());//到达时间
                        lids1001.setRecCreator("system");// 创建人工号
                        lids1001.setRecCreatorName("system");// 创建人姓名
                        lids1001.setRecCreateTime(DateUtil.curDateTimeStr14());// 创建时间
                        lids1001.setRecRevisor("system");// 修改人工号
                        lids1001.setRecRevisorName("system");// 修改人姓名
                        lids1001.setRecReviseTime(DateUtil.curDateTimeStr14());// 修改时间
                        lids1001.setUuid(UUIDUtils.getUUID());
                        dao.insert(LIDS1001.INSERT, lids1001);
                    /*}*/
                } else if ("60".equals(endAreaType)) {
                    //TODO 根据区域代码，区域名称设置机组上料区
                    queryMap.clear();
                    queryMap.put("segNo", lids1201.getSegNo());
                    queryMap.put("areaCode", endAreaCode);
                    queryMap.put("areaName", endAreaName);
                    //List<LIDS0701> lids0701List = dao.query(LIDS0701.QUERY, queryMap);
                    //if (CollectionUtils.isNotEmpty(lids0701List)) {
                    //LIDS0701 lids0701 = lids0701List.get(0);
                    //区域代码，区域名称为机组上料区编号，机组上料区名称
                    lids0901.setAreaType(endAreaType);
                    lids0901.setAreaCode(endAreaCode);
                    lids0901.setAreaName(endAreaName);
                    //}

                    try {
                        //一厂捆包到达上料区时，更新是否下发行车工标记
                        if ("F1".equals(factoryBuilding) && StringUtils.isNotBlank(lids0901.getPackId())) {
                            Map map = new HashMap();
                            map.put("segNo", lids0901.getSegNo());
                            map.put("packId", lids0901.getPackId());
                            map.put("ifIssueCraneOperator", "20");
                            map.put("recRevisor", "system");
                            map.put("recRevisorName", "system");
                            map.put("recReviseTime", DateUtil.curDateTimeStr14());
                            dao.update(VIPM0008.UPDATE_ISSUE_CRANE_FLAG, map);

                            List<Map> demandMaterailList = new ArrayList<>();
                            demandMaterailList.add(map);
                            //将结果返回IMC
                            EiInfo sendInfo = new EiInfo();
                            sendInfo.set("demandMaterailList", demandMaterailList);
                            sendInfo.set(EiConstant.serviceName, "LIDSInterfacesImc");
                            sendInfo.set(EiConstant.methodName, "confirmIssueCraneInfo");
                            sendInfo = XLocalManager.callNoTx(sendInfo);
                            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                                throw new PlatException(sendInfo.getMsg());
                            }
                        }
                    } catch (Exception ignored) {
                        log(lids0901.getPackId() + ",更新下发行车工标记处理异常：" + ignored.getMessage());
                        System.out.println(lids0901.getPackId() + ",更新下发行车工标记处理异常：" + ignored.getMessage());
                    }
                }

                //TODO 以非事务方式调用方法更新实物库存，当前方法报错会回滚updateLids0901，updateLids0901方法报错捕捉处理后不影响当前事务
                EiInfo updateInfo = new EiInfo();
                updateInfo.set("lids0901", lids0901);
                updateInfo.set(EiConstant.serviceName, "LIDS1201");
                updateInfo.set(EiConstant.methodName, "updateLids0901");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                }
            } else if ("40".equals(listSource)) {//加工退卷
                //区域类型，区域代码，区域名称为退卷区区域代码，区域名称
                lids0901.setAreaType(endAreaType);
                lids0901.setAreaCode(endAreaCode);
                lids0901.setAreaName(endAreaName);

                //TODO 以非事务方式调用方法，当前方法报错会回滚updateLids0901，updateLids0901方法报错捕捉处理后不影响当前事务
                EiInfo updateInfo = new EiInfo();
                updateInfo.set("lids0901", lids0901);
                updateInfo.set(EiConstant.serviceName, "LIDS1201");
                updateInfo.set(EiConstant.methodName, "updateLids0901");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                }
            } else if ("50".equals(listSource)) {//倒库
                //TODO 以非事务方式调用方法，当前方法报错会回滚updateLids0901，updateLids0901方法报错捕捉处理后不影响当前事务
                EiInfo updateInfo = new EiInfo();
                updateInfo.set("lids0901", lids0901);
                updateInfo.set(EiConstant.serviceName, "LIDS1201");
                updateInfo.set(EiConstant.methodName, "updateLids0901");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                }

                //根据行车实绩单号查询库存捆包
                queryMap.clear();
                queryMap.put("segNo", lids1202.getSegNo());
                queryMap.put("craneResultId", lids1202.getCraneResultId());
                List<LIDS0901> lids0901List = dao.query(LIDS0901.QUERY_PACKS_BY_RESULT_ID, queryMap);
                //调用IMC倒库
                EiInfo sendInfo = new EiInfo();
                //查询开关，是否调用IMC倒库服务
                String switchValue = new SwitchUtils().getProcessSwitchValue("JC000000", "IF_IMC_CALL_TRANSFER_ENABLED", dao);
                if ("1".equals(switchValue)) {
                    sendInfo.addBlock(EiConstant.resultBlock).addRows(lids0901List);
                    sendInfo.set("serviceName", "LIDSInterfaces");
                    sendInfo.set("methodName", "transferInventory");
                    //TODO 以非事务方式调用方法，当前方法报错会回滚transferInventory，transferInventory方法报错捕捉处理后不影响当前事务
                    sendInfo = XLocalManager.callNoTx(sendInfo);
                    if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        log(sendInfo.getMsg());
                        System.out.println(sendInfo.getMsg());
                    }
                }

                //捆包释放到库位时，调用库位推荐占用方法
                if("10".equals(endAreaType)){
                    /*queryMap.clear();
                    //校验在LIDS0605表中捆包是否存在数据
                    queryMap.put("segNo",lids0901.getSegNo());
                    queryMap.put("locViewPackId",lids0901.getPackId());
                    queryMap.put("useStatus","20");//20：预占用
                    List<LIDS0605> lids0605s = dao.query(LIDS0605.QUERY, queryMap);
                    if(CollectionUtils.isNotEmpty(lids0605s)){*/
                        sendInfo = new EiInfo();
                        Map messageBody = new HashMap();
                        messageBody.put("segNo",lids0901.getSegNo());
                        messageBody.put("warehouseCode",lids0901.getWarehouseCode());
                        messageBody.put("factoryArea",factoryArea);
                        messageBody.put("crossArea",crossArea);
                        messageBody.put("factoryBuilding",factoryBuilding);
                        messageBody.put("locationId",lids0901.getAreaCode());
                        messageBody.put("packId",lids0901.getPackId());
                        messageBody.put("xPoint",lids0901.getX_position());
                        messageBody.put("posDirCode",lids0901.getPosDirCode());
                        messageBody.put("originalPackId",lids0901.getOriginalPackId());
                        messageBody.put("netWeight",lids0901.getNetWeight());

                        sendInfo.set("messageBody", messageBody);
                        sendInfo.set(EiConstant.serviceName, "LIDS0606");
                        sendInfo.set(EiConstant.methodName, "occupyCalibration");
                        sendInfo = XLocalManager.callNewTx(sendInfo);
                        if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                            log("调用LIDS0606.occupyCalibration失败："+sendInfo.getMsg());
                            System.out.println("调用LIDS0606.occupyCalibration失败："+sendInfo.getMsg());
                        }
                    }
               /*}*/

            } else if ("60".equals(listSource)) {//厂内转运
            } else if ("70".equals(listSource) || "80".equals(listSource)) {//模具更换70
                //吊装重量不为模具整体重量，MES的模具库位位置不变。
                queryMap.clear();
                queryMap.put("segNo", lids1202.getSegNo());
                queryMap.put("mouldId", lids1202.getMouldId());
                queryMap.put("mouldName", lids1202.getMouldName());
                queryMap.put("craneResultId", lids1202.getCraneResultId());

                //抓取时已根据吊装重量与模具重量匹配，若吊装重量为模具整体重量，清空模具库存位置，这里判断若为空，更新模具库位位置
                List<LIDS0801> lids0801List = dao.query(LIDS0801.QUERY, queryMap);
                if (CollectionUtils.isNotEmpty(lids0801List)) {
                    LIDS0801 lids0801 = lids0801List.get(0);
                    if (StringUtils.isBlank(lids0801.getAreaCode()) || StringUtils.isBlank(lids0801.getAreaName())) {
                        //更新库存表,区域类型和区域代码信息
                        //终到区域类型为模具区//终到区域类型为机组模具台车
                        lids0801.setAreaType(endAreaType);
                        lids0801.setAreaCode(endAreaCode);
                        lids0801.setAreaName(endAreaName);
                        lids0801.setX_position(lids1201.getEndxPosition());
                        lids0801.setY_position(lids1201.getEndyPosition());
                        lids0801.setZ_position(lids1201.getEndzPosition());
                        //TODO 以非事务方式调用方法，当前方法报错会回滚updateLids0801，updateLids0801方法报错捕捉处理后不影响当前事务
                        EiInfo updateInfo = new EiInfo();
                        updateInfo.set("lids0801", lids0801);
                        updateInfo.set(EiConstant.serviceName, "LIDS1201");
                        updateInfo.set(EiConstant.methodName, "updateLids0801");
                        updateInfo = XLocalManager.callNoTx(updateInfo);
                        if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                            log("行车释放修改库存模具失败：" + updateInfo.getMsg());
                            System.out.println("行车释放修改库存模具失败：" + updateInfo.getMsg());
                        }
                    }
                }
            }else if("90".equals(listSource)){//下料区->下料区
                //区域类型，区域代码，区域名称为终到下料区区域代码，区域名称,层数标记
                lids0901.setAreaType(endAreaType);
                lids0901.setAreaCode(endAreaCode);
                lids0901.setAreaName(endAreaName);

                queryMap.clear();
                queryMap.put("segNo", lids1202.getSegNo());
                queryMap.put("areaType", endAreaType);
                queryMap.put("areaCode", endAreaCode);
                queryMap.put("areaName", endAreaName);
                queryMap.put("XPosition", lids1201.getEndxPosition());
                queryMap.put("YPosition", lids1201.getEndyPosition());

                try{
                    if ("61".equals(endAreaType)) {
                        EiInfo iInfo = new EiInfo();
                        iInfo.set("paramMap", paramMap);
                        iInfo.set("segNo", lids0901.getSegNo());
                        iInfo.set("factoryArea", factoryArea);
                        iInfo.set("factoryBuilding", factoryBuilding);
                        iInfo.set("areaType", endAreaType);
                        iInfo.set("areaCode", endAreaCode);
                        iInfo.set("areaName", endAreaName);
                        iInfo.set("maxPosDirCode", "1");
                        iInfo.set("xPosition", lids1201.getEndxPosition());
                        iInfo.set("yPosition", lids1201.getEndyPosition());
                        iInfo.set("craneResultId", "");
                        iInfo.set(EiConstant.serviceName, "LIDS1201");
                        iInfo.set(EiConstant.methodName, "autoMatchBundlesForTransport");
                        iInfo = XLocalManager.callNoTx(iInfo);
                        if (iInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                            throw new Exception(iInfo.getMsg());
                        }
                    }
                }catch (Exception e){
                    log("下料自动匹配捆包失败：" + e.getMessage());
                    System.out.println("下料自动匹配捆包失败：" + e.getMessage());
                }
                //查询终到下料区最大层数标记
                List<LIDS0901> lids0901List = dao.query(LIDS0901.QUERY_MAX_POS_DIR_CODE_AREA, queryMap);
                String maxPosDirCode = "";
                if (CollectionUtils.isNotEmpty(lids0901List)) {
                    maxPosDirCode = lids0901List.get(0).getPosDirCode();
                }

                //TODO 以非事务方式调用方法，当前方法报错会回滚updateLids0901，updateLids0901方法报错捕捉处理后不影响当前事务
                EiInfo updateInfo = new EiInfo();
                updateInfo.set("lids0901", lids0901);
                updateInfo.set("maxPosDirCode", maxPosDirCode);
                updateInfo.set(EiConstant.serviceName, "LIDS1201");
                updateInfo.set(EiConstant.methodName, "updateLids0901");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log(lids0901.getPackId()+"行车释放(退料区->退料区)修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println(lids0901.getPackId()+"行车释放(退料区->退料区)修改库存捆包失败：" + updateInfo.getMsg());
                }

            } else if ("X1".equals(listSource)) {//下料区->未匹配到区域
               /*成品下料匹配区域时，如果匹配不到区域，
                 则默认一个库位，例如Z4机组匹配不到则库位为Z4-00*/

                //查询下料区代码为起始区域的机组
                queryMap.clear();
                queryMap.put("segNo", lids0901.getSegNo());
                queryMap.put("factoryArea",factoryArea);
                queryMap.put("factoryBuilding",factoryBuilding);
                queryMap.put("materialUnloadingArea", startAreaCode);
                queryMap.put("materialUnloadingAreaName", startAreaName);
                List<HashMap> lids0701List = dao.query(LIDS0701.QUERY_MACHINE_BY_AREA, queryMap);
                if (CollectionUtils.isNotEmpty(lids0701List)) {
                    //取出机组代码，拼接-00
                    String machineCode = MapUtils.getString(lids0701List.get(0), "machineCode");

                    //区域类型-库位
                    lids0901.setAreaType("10");
                    lids0901.setAreaCode(machineCode + "-00");
                    lids0901.setAreaName(machineCode + "-00");

                    EiInfo updateInfo = new EiInfo();
                    updateInfo.set("lids0901", lids0901);
                    updateInfo.set(EiConstant.serviceName, "LIDS1201");
                    updateInfo.set(EiConstant.methodName, "updateLids0901");
                    updateInfo = XLocalManager.callNoTx(updateInfo);
                    if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                        log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                        System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    }
                }
            } else {
                /*
                  没有匹配到具体的区域到区域，也把库存的信息修改掉。
                 */
                EiInfo updateInfo = new EiInfo();
                updateInfo.set("lids0901", lids0901);
                updateInfo.set(EiConstant.serviceName, "LIDS1201");
                updateInfo.set(EiConstant.methodName, "updateLids0901");
                updateInfo = XLocalManager.callNoTx(updateInfo);
                if (updateInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                    log("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                    System.out.println("行车释放修改库存捆包失败：" + updateInfo.getMsg());
                }
            }

            /*if(1==1){
                throw new RuntimeException("测试异常");
            }*/
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            String msg = ex.getMessage();
            if (StringUtils.isNotBlank(msg) && msg.length() < 200) {
                outInfo.setMsg("处理行车释放实绩推送失败，请查看链路！链路ID:" + inInfo.getTraceId() + ",报错(小于200位才打印):" + msg);
            } else {
                outInfo.setMsg("处理行车释放实绩推送失败，请查看链路！链路ID:" + inInfo.getTraceId());
            }
        } finally {
            //设置修改人信息为system
            RecordUtils.setRevisorBeanSys(lids1201);
            //以非事务方式调用修改方法，若报错不回滚
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDS1201");
            sendInfo.set(EiConstant.methodName, "updateLids1201");
            sendInfo.set("lids1201", lids1201);//行车实绩主项
            sendInfo.set("type", "U");//修改
            sendInfo = XLocalManager.callNoTx(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                log("修改行车作业实绩信息失败：" + lids1201.getCraneResultId());
                System.out.println("修改行车作业实绩信息失败：" + lids1201.getCraneResultId());
            }
        }
        return outInfo;
    }


    /**
     * 单纯修改库存捆包
     *
     * @param inInfo
     * @return
     */
    public EiInfo updateLids0901(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            LIDS0901 lids0901 = (LIDS0901) inInfo.get("lids0901");

            String maxPosDirCode = (String) inInfo.get("maxPosDirCode");

            Map queryMap = new HashMap();
            queryMap.put("segNo", lids0901.getSegNo());
            queryMap.put("areaType", lids0901.getAreaType());
            queryMap.put("areaCode", lids0901.getAreaCode());
            queryMap.put("areaName", lids0901.getAreaName());
            queryMap.put("XPosition", lids0901.getX_position());
            queryMap.put("YPosition", lids0901.getY_position());
            //查询终到坐标最大层数标记
            List<LIDS0901> lids0901List = dao.query(LIDS0901.QUERY_MAX_POS_DIR_CODE_AREA, queryMap);
            if (CollectionUtils.isNotEmpty(lids0901List)) {
                LIDS0901 lids0901Temp = lids0901List.get(0);
                //捆包为卷,以UWBUtils.transitionLocation转换库位方法计算层数标记为准,不依据maxPosDirCode
                if ("0".equals(lids0901Temp.getActionFlag())) {
                    maxPosDirCode = lids0901Temp.getPosDirCode();
                    if (StringUtils.isBlank(maxPosDirCode)) {
                        maxPosDirCode = "0";
                    }
                } else if ("1".equals(lids0901Temp.getActionFlag())) {
                    maxPosDirCode = "";
                }
            }
            //需要修改的字段
            Map updateMap = new HashMap();
            updateMap.put("maxPosDirCode", maxPosDirCode);
            updateMap.put("warehouseCode", lids0901.getWarehouseCode());
            updateMap.put("warehouseName", lids0901.getWarehouseName());
            updateMap.put("segNo", lids0901.getSegNo());
            updateMap.put("craneResultId", lids0901.getCraneResultId());
            updateMap.put("packId", lids0901.getPackId());
            updateMap.put("labelId", lids0901.getLabelId());
            updateMap.put("x_position", lids0901.getX_position());
            updateMap.put("y_position", lids0901.getY_position());
            updateMap.put("z_position", lids0901.getZ_position());
            updateMap.put("posDirCode", lids0901.getPosDirCode());
            updateMap.put("areaType", lids0901.getAreaType());
            updateMap.put("areaCode", lids0901.getAreaCode());
            updateMap.put("areaName", lids0901.getAreaName());
            updateMap.put("status", lids0901.getStatus());
            updateMap.put("recRevisor", "system");
            updateMap.put("recRevisorName", "system");
            updateMap.put("recReviseTime", DateUtil.curDateTimeStr14());
            updateMap.put("delFlag", lids0901.getDelFlag());
            updateMap.put("craneOperationWeight", lids0901.getCraneOperationWeight());
            dao.update(LIDS0901.UPDATE_INVENTORY_PACK_INFO, updateMap);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 单纯修改库存模具
     *
     * @param inInfo
     * @return
     */
    public EiInfo updateLids0801(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            LIDS0801 lids0801 = (LIDS0801) inInfo.get("lids0801");

            //需要修改的字段
            Map updateMap = new HashMap();
            updateMap.put("segNo", lids0801.getSegNo());
            updateMap.put("mouldId", lids0801.getMouldId());
            updateMap.put("craneResultId", lids0801.getCraneResultId());
            updateMap.put("x_position", lids0801.getX_position());
            updateMap.put("y_position", lids0801.getY_position());
            updateMap.put("z_position",lids0801.getZ_position());
            updateMap.put("posDirCode",lids0801.getPosDirCode());
            updateMap.put("mouldSpecWeightSum",lids0801.getMouldSpecWeightSum());
            updateMap.put("areaType", lids0801.getAreaType());
            updateMap.put("areaCode", lids0801.getAreaCode());
            updateMap.put("areaName", lids0801.getAreaName());
            updateMap.put("status", lids0801.getStatus());
            updateMap.put("recRevisor", "system");
            updateMap.put("recRevisorName", "system");
            updateMap.put("recReviseTime", DateUtil.curDateTimeStr14());
            updateMap.put("delFlag", lids0801.getDelFlag());
            dao.update(LIDS0801.UPDATE_INVENTORY_MOULD_INFO, updateMap);
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 单纯新增/修改行车实绩
     */
    public EiInfo updateLids1201(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //操作类型
            String type = inInfo.getString("type");
            //传入行车实绩主项
            LIDS1201 lids1201 = (LIDS1201) inInfo.get("lids1201");
            //新增
            if ("I".equals(type)) {
                //当操作为新增时，获取传入的行车实绩子项
                List<LIDS1202> insertBatchList = (List<LIDS1202>) inInfo.get("insertBatchList");
                //批量插入行车作业实绩子项
                log("行车作业实绩主项：" + lids1201.getCraneResultId() + "对应插入行车作业实绩子项：" + gson.toJson(insertBatchList));
                System.out.println("行车作业实绩主项：" + lids1201.getCraneResultId() + "对应插入行车作业实绩子项：" + gson.toJson(insertBatchList));

                //判断传入行车实绩主项起始区域为空时,根据坐标查询区域信息
                if (StringUtils.isBlank(lids1201.getStartAreaType()) || StringUtils.isBlank(lids1201.getStartAreaCode())
                        || StringUtils.isBlank(lids1201.getStartAreaName())) {
                    //查询参数
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", lids1201.getSegNo());
                    queryMap.put("XPosition", lids1201.getStartXPosition());
                    queryMap.put("YPosition", lids1201.getStartYPosition());
                    queryMap.put("ZPosition", lids1201.getStartZPosition());
                    queryMap.put("factoryArea", "CQBG");
                    queryMap.put("factoryBuilding", "F1");

                    //起始区域
                    Map areaMap = transitionAreaOrLocation(queryMap);
                    lids1201.setStartAreaType(MapUtils.getString(areaMap, "areaType"));
                    lids1201.setStartAreaCode(MapUtils.getString(areaMap, "areaCode"));
                    lids1201.setStartAreaName(MapUtils.getString(areaMap, "areaName"));
                }
                //插入行车作业实绩
                dao.insert(LIDS1201.INSERT, lids1201);
                dao.insertBatch(LIDS1202.INSERT, insertBatchList);
            } else if ("U".equals(type)) {//修改
                log("修改行车作业实绩信息：" + lids1201.getCraneResultId());
                System.out.println("修改行车作业实绩信息：" + lids1201.getCraneResultId());

                //判断传入行车实绩主项终到区域为空时,根据坐标查询区域信息
                if (StringUtils.isBlank(lids1201.getEndAreaType()) || StringUtils.isBlank(lids1201.getEndAreaCode())
                        || StringUtils.isBlank(lids1201.getEndAreaName())) {
                    //查询参数
                    Map queryMap = new HashMap();
                    queryMap.put("segNo", lids1201.getSegNo());
                    queryMap.put("XPosition", lids1201.getEndxPosition());
                    queryMap.put("YPosition", lids1201.getEndyPosition());
                    queryMap.put("ZPosition", lids1201.getEndzPosition());
                    queryMap.put("factoryArea", "CQBG");
                    queryMap.put("factoryBuilding", "F1");


                    //终到区域
                    Map areaMap = transitionAreaOrLocation(queryMap);
                    lids1201.setEndAreaType(MapUtils.getString(areaMap, "areaType"));
                    lids1201.setEndAreaCode(MapUtils.getString(areaMap, "areaCode"));
                    lids1201.setEndAreaName(MapUtils.getString(areaMap, "areaName"));
                }
                //修改行车作业实绩主项信息
                dao.update(LIDS1201.UPDATE, lids1201.toMap());
                //根据行车实绩主项号修改行车实绩子表状态为20(作业结束)
                dao.update(LIDS1202.UPDATE_STATUS_BY_CRANE_RESULT_ID, lids1201.toMap());
            } else {
                throw new PlatException("操作类型错误！");
            }

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
        }catch (Exception ex){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 依据坐标转为区域信息，若坐标区域为库位，再查询具体库位
     * @param paramMap
     * @return
     */
    public Map transitionAreaOrLocation(Map paramMap) {
        Map areaMap = new HashMap();
        try {
            areaMap = UWBUtils.transitionArea(paramMap, dao);
            String areaType = StringUtils.defaultString((String) areaMap.get("areaType"), "");
            if (StringUtils.isBlank(areaType)) {
                return areaMap;
            }

            //区域类型为库位
            if ("10".equals(areaType)) {
                HashMap queryMap = new HashMap();
                queryMap.putAll(paramMap);
                //根据XY轴坐标转换为库位附属信息
                Map locationMap = UWBUtils.transitionLocation(queryMap, dao);
                areaMap.put("areaCode", MapUtils.getString(locationMap, "locationId"));
                areaMap.put("areaName", MapUtils.getString(locationMap, "locationName"));
            }
        } catch (Exception ex) {
            return areaMap;
        }
        return areaMap;
    }

    /**
     * 横切、飞剪：下料台两个堆垛台，每个堆垛台都做坐标维护，两个堆垛台之间的吊运或者从堆垛台吊运到成品库时，
     * 按先进先出原则，自动匹配吊运捆包，注意先进先出不是IMC实绩的时间，是实绩采集到的时间
     */
    public EiInfo autoMatchBundlesForTransport(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String segNo = inInfo.getString("segNo");
            if (StringUtils.isBlank(segNo)) {
                throw new PlatException("传入字段【系统账套】为空！");
            }
            String factoryArea = inInfo.getString("factoryArea");
            if (StringUtils.isBlank(factoryArea)) {
                throw new PlatException("传入字段【厂区代码】为空！");
            }
            String factoryBuilding = inInfo.getString("factoryBuilding");
            if (StringUtils.isBlank(factoryBuilding)) {
                throw new PlatException("传入字段【厂房代码】为空！");
            }
            String areaType = inInfo.getString("areaType");
            if (StringUtils.isBlank(areaType)) {
                throw new PlatException("传入字段【区域类型】为空！");
            }
            String areaCode = inInfo.getString("areaCode");
            String areaName = inInfo.getString("areaName");
            if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(areaName)) {
                throw new PlatException("传入字段【区域编码】或【区域名称】为空！");
            }
            String maxPosDirCode  = inInfo.getString("maxPosDirCode");
            if (StringUtils.isBlank(maxPosDirCode)) {
                maxPosDirCode = "1";
            }
            String craneOperationWeight = inInfo.getString("craneOperationWeight");
            if (StringUtils.isBlank(craneOperationWeight) || "0".equals(craneOperationWeight)) {
                craneOperationWeight = "";
            }
            String xPosition = inInfo.getString("xPosition");
            String yPosition = inInfo.getString("yPosition");
            String zPosition = inInfo.getString("zPosition");
            if (StringUtils.isBlank(xPosition) || StringUtils.isBlank(yPosition)) {
                throw new PlatException("传入字段【X坐标】或【Y坐标】或【Z坐标】为空！");
            }

            Map paramMap = inInfo.getMap("paramMap");
            if (MapUtils.isEmpty(paramMap)) {
                throw new PlatException("传入字段【paramMap】为空！");
            }
            //动作类型
            String actionType = MapUtils.getString(paramMap, "actionType");

            //当动作类型为抓取，且行车作业实绩单号传入为空时，报错
            String craneResultId = inInfo.getString("craneResultId");
            if ("1".equals(actionType) && StringUtils.isBlank(craneResultId)) {
                throw new PlatException("传入字段【行车作业实绩号】为空！");
            }

            //行车作业实绩子表
            List<LIDS1202> insertBatchList = new ArrayList<>();
            /**
             * 横切、飞剪：下料台两个堆垛台，每个堆垛台都做坐标维护，两个堆垛台之间的吊运或者从堆垛台吊运到成品库时，
             * 按先进先出原则，自动匹配吊运捆包，注意先进先出不是IMC实绩的时间，是实绩采集到的时间
             */
            Map queryMap = new HashMap();
            queryMap.clear();
            queryMap.put("segNo", segNo);
            queryMap.put("factoryArea", factoryArea);
            queryMap.put("factoryBuilding", factoryBuilding);
            queryMap.put("materialUnloadingArea", areaCode);
            queryMap.put("materialUnloadingAreaName", areaName);

            //根据终到下料区匹配机组
            List<HashMap> lids0701List = dao.query(LIDS0701.QUERY_MACHINE_BY_AREA, queryMap);
            if (CollectionUtils.isNotEmpty(lids0701List)) {
                String machineCode = MapUtils.getString(lids0701List.get(0),"machineCode");
                if ("H1".equals(machineCode) || "J4".equals(machineCode)) {
                    queryMap.clear();
                    queryMap.put("segNo", segNo);
                    queryMap.put("machineCode", machineCode);
                    queryMap.put("stackName", areaCode);
                    //根据时间倒序查询捆包生产实绩时间表,吊运标记为未吊运
                    List<VGDM1005> vgdm1005List = dao.query(LIDS1201.QUERY_VGDM1005_BY_MACHINE_CODE, queryMap);
                    if (CollectionUtils.isNotEmpty(vgdm1005List)) {
                        for (VGDM1005 vgdm1005 : vgdm1005List) {
                            //修改捆包生产实绩时间表中的吊运标记为已吊运
                            vgdm1005.setLiftFlag("1");
                            RecordUtils.setRevisorBeanSys(vgdm1005);
                            dao.update(LIDS1201.UPDATE_VGDM1005_LIFT_FLAG, vgdm1005);
                            //生成实物库存表数据
                            LIDS0901 iLids0901 = new LIDS0901();
                            iLids0901.setSegNo(segNo);
                            iLids0901.setUnitCode(segNo);
                            iLids0901.setStatus("10");
                            iLids0901.setUnitedPackId(vgdm1005.getUnitedPackId());
                            iLids0901.setPackId(vgdm1005.getOutPackId());
                            iLids0901.setNetWeight(vgdm1005.getNetWeight());
                            iLids0901.setCraneOperationWeight(StringUtils.isBlank(craneOperationWeight)?vgdm1005.getNetWeight():new BigDecimal(craneOperationWeight));
                            iLids0901.setQuantity(vgdm1005.getQuantity().intValue());
                            iLids0901.setAreaType(areaType);
                            iLids0901.setAreaCode(areaCode);
                            iLids0901.setAreaName(areaName);
                            iLids0901.setCraneResultId(craneResultId);
                            iLids0901.setX_position(xPosition);
                            iLids0901.setY_position(yPosition);
                            iLids0901.setZ_position(zPosition);
                            iLids0901.setPosDirCode(maxPosDirCode);
                            iLids0901.setUuid(UUIDUtils.getUUID());
                            //判断板卷标记
                            if(vgdm1005.getSpecsDesc().contains("*C")){
                                iLids0901.setActionFlag("1");
                            }else{
                                iLids0901.setActionFlag("0");
                            }
                            //创建人信息
                            RecordUtils.setCreatorBeanSys(iLids0901);
                            dao.insert(LIDS0901.INSERT, iLids0901);
                            int posDirCode = Integer.parseInt(maxPosDirCode);
                            maxPosDirCode = (posDirCode + 1) + "";

                            //动作类型为抓取时，补实绩子项
                            if("1".equals(actionType)){
                                //行车作业实绩子项
                                LIDS1202 ilids1202 = new LIDS1202();
                                ilids1202.fromMap(paramMap);
                                //设置抓取的当前捆包号
                                ilids1202.setPackId(vgdm1005.getOutPackId());
                                //行车作业实绩号
                                ilids1202.setCraneResultId(craneResultId);
                                //行车作业实绩子项号
                                ilids1202.setCraneResultSubId(SequenceGenerator.getNextSequence(LIDS1202.SEQ_ID, new String[]{craneResultId, ""}));
                                //净重
                                ilids1202.setNetWeight(vgdm1005.getNetWeight());
                                //捆包数量
                                ilids1202.setQuantity(vgdm1005.getQuantity().intValue());
                                //标签号
                                ilids1202.setLabelId("");
                                //uuid
                                ilids1202.setUuid(UUIDUtils.getUUID());
                                //状态为10(作业开始)
                                ilids1202.setStatus("10");
                                //创建人信息
                                RecordUtils.setCreatorBeanSys(ilids1202);
                                //行车作业实绩子项
                                insertBatchList.add(ilids1202);
                            }
                        }
                    }

                }
            }

            outInfo.set("insertBatchList", insertBatchList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("操作成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
