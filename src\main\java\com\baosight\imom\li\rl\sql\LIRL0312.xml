<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-11-25 14:46:15
       Version :  1.0
    tableName :meli.tlirl0312
     RELEVANCE_TYPE  VARCHAR   NOT NULL,
     RELEVANCE_ID  VARCHAR   NOT NULL,
     UPLOAD_FILE_PATH  VARCHAR   NOT NULL,
     UPLOAD_FILE_NAME  VARCHAR   NOT NULL,
     FIFLE_TYPE  VARCHAR   NOT NULL,
     FIFLE_SIZE  DECIMAL   NOT NULL,
     FILE_ID  VARCHAR   NOT NULL,
     UUID  VARCHAR   NOT NULL,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  VARCHAR   NOT NULL,
     DEL_FLAG  VARCHAR   NOT NULL,
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL
-->
<sqlMap namespace="LIRL0312">

    <sql id="condition">
        <isNotEmpty prepend=" AND " property="relevanceType">
            RELEVANCE_TYPE = #relevanceType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceIdList">
            RELEVANCE_ID in
            <iterate property="relevanceIdList" open="("
                     close=")" conjunction=" , ">
                #relevanceIdList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uploadFilePath">
            UPLOAD_FILE_PATH = #uploadFilePath#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uploadFileName">
            UPLOAD_FILE_NAME = #uploadFileName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fifleType">
            FIFLE_TYPE = #fifleType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fifleSize">
            FIFLE_SIZE = #fifleSize#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fileId">
            FILE_ID = #fileId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="signatureMark">
            SIGNATURE_MARK = #signatureMark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0312">
        SELECT
        RELEVANCE_TYPE as "relevanceType",  <!-- 关联类型 -->
        RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        UPLOAD_FILE_PATH as "uploadFilePath",  <!-- 文件路径 -->
        UPLOAD_FILE_NAME as "uploadFileName",  <!-- 文件名称 -->
        FIFLE_TYPE as "fifleType",  <!-- 文件类型 -->
        FIFLE_SIZE as "fifleSize",  <!-- 文件大小 -->
        FILE_ID as "fileId",  <!-- 文件id -->
        UUID as "uuid",  <!-- 唯一编码 -->
        SIGNATURE_MARK as "signatureMark",  <!-- 签名标记 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" ,<!-- 业务单元代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0312.SEG_NO and DEL_FLAG = 0) as
        "segName" <!-- 业务单元简称 -->
        FROM meli.tlirl0312 tlirl0312 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>

            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*)
        FROM meli.tlirl0312
        WHERE 1 = 1
    </select>


    <select id="queryFile" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.li.rl.dao.LIRL0312">
        SELECT
        tlirl0312.RELEVANCE_TYPE as "relevanceType",  <!-- 关联类型 -->
        tlirl0312.RELEVANCE_ID as "relevanceId",  <!-- 关联ID -->
        tlirl0312.UPLOAD_FILE_PATH as "uploadFilePath",  <!-- 文件路径 -->
        tlirl0312.UPLOAD_FILE_NAME as "uploadFileName",  <!-- 文件名称 -->
        tlirl0312.FIFLE_TYPE as "fifleType",  <!-- 文件类型 -->
        tlirl0312.FIFLE_SIZE as "fifleSize",  <!-- 文件大小 -->
        tlirl0312.FILE_ID as "fileId",  <!-- 文件id -->
        tlirl0312.UUID as "uuid",  <!-- 唯一编码 -->
        tlirl0312.SIGNATURE_MARK as "signatureMark",  <!-- 签名标记 -->
        tlirl0312.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        tlirl0312.REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        tlirl0312.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        tlirl0312.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        tlirl0312.REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        tlirl0312.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        tlirl0312.TENANT_ID as "tenantId",  <!-- 租户ID -->
        tlirl0312.ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        tlirl0312.DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        tlirl0312.SEG_NO as "segNo",  <!-- 系统帐套 -->
        tlirl0312.UNIT_CODE as "unitCode" ,<!-- 业务单元代码 -->
        (select SEG_NAME from ${platSchema}.TVZBM81 where SEG_NO = tlirl0312.SEG_NO and DEL_FLAG = 0) as
        "segName" <!-- 业务单元简称 -->
        from meli.tlirl0312 tlirl0312,
        MELI.tlirl0201 tlirl0201
        where tlirl0312.SEG_NO = #segNo#
        AND tlirl0312.SEG_NO = tlirl0201.SEG_NO
        AND tlirl0312.RELEVANCE_ID = tlirl0201.RESERVATION_NUMBER
        AND tlirl0201.DRIVER_IDENTITY = #driverIdentity#
        AND tlirl0201.DRIVER_TEL = #driverTel#
        AND tlirl0201.DRIVER_NAME = #driverName#
        AND tlirl0312.REC_CREATE_TIME >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        AND tlirl0312.REC_CREATE_TIME &lt;= CURDATE()
        AND tlirl0201.STATUS != '00'
        order by tlirl0312.REC_CREATE_TIME desc
        limit 1
    </select>

    <select id="queryRelevanceId" resultClass="String">
        select RELEVANCE_ID
        from meli.tlirl0312 tlirl0312,
        MELI.tlirl0201 tlirl0201
        where tlirl0312.SEG_NO = #segNo#
        AND tlirl0312.SEG_NO = tlirl0201.SEG_NO
        AND tlirl0312.RELEVANCE_ID = tlirl0201.RESERVATION_NUMBER
        AND tlirl0201.DRIVER_IDENTITY=#driverIdentity#
        AND tlirl0201.DRIVER_TEL=#driverTel#
        AND tlirl0201.DRIVER_NAME=#driverName#
        AND tlirl0312.REC_CREATE_TIME >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        AND tlirl0312.REC_CREATE_TIME &lt;= CURDATE()
        AND tlirl0201.STATUS != '00'
        order by tlirl0312.REC_CREATE_TIME desc
        LIMIT 1;
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="relevanceType">
            RELEVANCE_TYPE = #relevanceType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="relevanceId">
            RELEVANCE_ID = #relevanceId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uploadFilePath">
            UPLOAD_FILE_PATH = #uploadFilePath#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uploadFileName">
            UPLOAD_FILE_NAME = #uploadFileName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fifleType">
            FIFLE_TYPE = #fifleType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fifleSize">
            FIFLE_SIZE = #fifleSize#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fileId">
            FILE_ID = #fileId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO meli.tlirl0312 (RELEVANCE_TYPE,  <!-- 关联类型 -->
        RELEVANCE_ID,  <!-- 关联ID -->
        UPLOAD_FILE_PATH,  <!-- 文件路径 -->
        UPLOAD_FILE_NAME,  <!-- 文件名称 -->
        FIFLE_TYPE,  <!-- 文件类型 -->
        FIFLE_SIZE,  <!-- 文件大小 -->
        FILE_ID,  <!-- 文件id -->
        UUID,  <!-- 唯一编码 -->
        SIGNATURE_MARK,  <!-- 签名标记 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#relevanceType#, #relevanceId#, #uploadFilePath#, #uploadFileName#, #fifleType#, #fifleSize#, #fileId#,
        #uuid#,#signatureMark#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#,
        #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM meli.tlirl0312 WHERE 1=1 and UUID=#uuid#
    </delete>

    <update id="update">
        UPDATE meli.tlirl0312
        SET
        UPLOAD_FILE_PATH = #uploadFilePath#,   <!-- 文件路径 -->
        UPLOAD_FILE_NAME = #uploadFileName#,   <!-- 文件名称 -->
        FIFLE_TYPE = #fifleType#,   <!-- 文件类型 -->
        FIFLE_SIZE = #fifleSize#,   <!-- 文件大小 -->
        FILE_ID = #fileId#,   <!-- 文件id -->
        UUID = #uuid#,   <!-- 唯一编码 -->
        SIGNATURE_MARK = #signatureMark#,   <!-- 签名标记 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#  <!-- 业务单元代码 -->
        WHERE
        RELEVANCE_ID = #relevanceId#
    </update>

    <update id="updateAttachmentPrint">
        UPDATE meli.tlirl0312
        SET ATTACHMENT_PRINT=#attachmentPrint#
        WHERE
        UUID = #uuid#
    </update>
    <select id="querySegNoName" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        select
            SEG_NAME as "segName"
        from
            ${platSchema}.TVZBM81
        where SEG_NO = #segNo#
        and DEL_FLAG = 0
    </select>

    <update id="updateRelevanceId">
        UPDATE meli.tlirl0312
        SET
        RELEVANCE_TYPE = #relevanceType#,  <!-- 关联类型 -->
        RELEVANCE_ID = #relevanceId#,  <!-- 关联ID -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        FILE_ID = #fileId#
        and SEG_NO = #segNo#
    </update>

    <insert id="insertLog">
        INSERT INTO iplat4j.t_system_log (LOG_ID,
                                          OPERATION_TIME,
                                          OPERATION_TYPE,
                                          OPERATION_USER,
                                          OPERATION_CONTENT,
                                          IP_ADDRESS,
                                          OPERATION_RESULT,
                                          MODULE_NAME,
                                          METHOD_NAME,
                                          REQUEST_PARAMS,
                                          RESPONSE_DATA,
                                          ERROR_MSG,
                                          REMARK,
                                          CREATE_TIME,
                                          ACTION_TYPE,
                                          VEHICLE_ID)
        VALUES (#logId#,#operationTime#,#operationType#,#operationUser#,#operationContent#,#ipAddress#, #operationResult#,
                #moduleName#,#methodName#, #requestParams#,#responseData#,#errorMsg#,#remark#,#createTime#,#actionType#,#vehicleId#)
    </insert>

</sqlMap>