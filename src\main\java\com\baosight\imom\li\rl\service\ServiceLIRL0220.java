package com.baosight.imom.li.rl.service;


import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.xservices.em.util.SmsSendManager;
import com.spire.doc.interfaces.IField;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.misc.Hash;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @Author: 张博翔
 * @Description: ${加工中心厂内物流作业查询}
 * @Date: 2024/11/11 09:37
 * @Version: 1.0
 */
public class ServiceLIRL0220 extends ServiceBase {
    private static final Logger log = LoggerFactory.getLogger(ServiceLIRL0220.class);


    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0308().eiMetadata);
        inInfo.addBlock("result2").addBlockMeta(new LIRL0308().eiMetadata);
        inInfo.addBlock("result3").addBlockMeta(new LIRL0308().eiMetadata);

        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String isHouseCars = MapUtils.getString(queryBlock, "isHouseCars", "");//是否厂内车
        String businessType = MapUtils.getString(queryBlock, "businessType", "");//业务类型
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        if ("10".equals(isHouseCars)){
            queryBlock.put("noHouse","10");
        } else if ("20".equals(isHouseCars)){
            queryBlock.put("inHouse","20");
        }else if ("30".equals(isHouseCars)){
            queryBlock.put("outHouse","30");
        }else if ("40".equals(isHouseCars)){
            queryBlock.put("outHouse","30");
            queryBlock.put("leaveFactoryDate",format);
        }

        if ("20".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("voucherNumN","1");
        }else if ("60".equals(businessType)){
            queryBlock.put("businessType","20");
            queryBlock.put("voucherNumN","1");
        }else if ("40".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("voucherNumN","1");
        }else if ("50".equals(businessType)){
            queryBlock.put("businessType","40");
            queryBlock.put("voucherNumN","1");
        }else if ("70".equals(businessType)) {
            queryBlock.put("businessType","50");
            queryBlock.put("voucherNumN","1");
        }else if ("80".equals(businessType)) {
            queryBlock.put("businessType","60");
            queryBlock.put("voucherNumN","1");
        }
        else if ("10".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("handType","10");
        }else if ("30".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("handType","30");
        }
        if ("JC000000".equals(segNo)&&"90".equals(businessType)){
            queryBlock.put("businessType","10");
        }
        EiInfo outInfo = new EiInfo();

        if("JC000000".equals(segNo)){
            outInfo = super.query(inInfo,"LIRL0220.queryCq");
            int a = super.count("LIRL0220.queryCqCount",queryBlock);
            outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
            Map attr = outInfo.getBlock(EiConstant.resultBlock).getAttr();
            attr.put("count", a);
            outInfo.setAttr(attr);
            List<Map<String,Object>> hashMapList = outInfo.getBlock(EiConstant.resultBlock).getRows();
            extracted(hashMapList,"0");
        }else{
            //EiInfo outInfo = new EiInfo();
            outInfo = super.query(inInfo,"LIRL0220.query");
//        List hashMapList = this.dao.query("LIRL0220.query", queryBlock);
            outInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
            List<Map<String,Object>> hashMapList = outInfo.getBlock(EiConstant.resultBlock).getRows();
            extracted(hashMapList,"0");
        }


//        outInfo.getBlock(EiConstant.resultBlock).setRows(hashMapList);
        return outInfo;
    }

    public EiInfo postExport(EiInfo inInfo) {
        Map<String,Object> loginMap = new HashMap();
        loginMap.put("userId",UserSession.getUserId());
        loginMap.put("userName",UserSession.getLoginCName());
        loginMap.put("loginName",UserSession.getLoginName());
        Map queryBlock = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String segName = MapUtils.getString(queryBlock, "segName", "");
        String isHouseCars = MapUtils.getString(queryBlock, "isHouseCars", "");//是否厂内车
        String businessType = MapUtils.getString(queryBlock, "businessType", "");//业务类型
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
//            throw new PlatException("缺少业务单元代码不能查询！");
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }

        if ("10".equals(isHouseCars)){
            queryBlock.put("noHouse","10");
        } else if ("20".equals(isHouseCars)){
            queryBlock.put("inHouse","20");
        }else if ("30".equals(isHouseCars)){
            queryBlock.put("outHouse","30");
        }

        if ("20".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("voucherNumN","1");
        }else if ("60".equals(businessType)){
            queryBlock.put("businessType","20");
            queryBlock.put("voucherNumN","1");
        }else if ("40".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("voucherNumN","1");
        }else if ("50".equals(businessType)){
            queryBlock.put("businessType","40");
            queryBlock.put("voucherNumN","1");
        }else if ("70".equals(businessType)) {
            queryBlock.put("businessType","50");
            queryBlock.put("voucherNumN","1");
        }else if ("80".equals(businessType)) {
            queryBlock.put("businessType","60");
            queryBlock.put("voucherNumN","1");
        } else if ("10".equals(businessType)){
            queryBlock.put("businessType","10");
            queryBlock.put("handType","10");
        }else if ("30".equals(businessType)){
            queryBlock.put("businessType","30");
            queryBlock.put("handType","30");
        }
        List<Map<String,Object>> hashMapList = new ArrayList<>();
        if("JC000000".equals(segNo)){
             hashMapList = dao.queryAll("LIRL0220.queryCq", queryBlock);
        }else{
            hashMapList = dao.queryAll("LIRL0220.query", queryBlock);
        }
        /*inInfo = super.query(inInfo,"LIRL0220.query");*/
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(getExportBlockMeta());
        extracted(hashMapList,"1");
        TreeMap sheetMap = new TreeMap();
        List<Map> detailList = dao.queryAll(LIRL0308.QUERY_EXPROT_DETAILS, queryBlock);
        List<Map> result3List = new ArrayList<>();
        List<Map> result4List = new ArrayList<>();
        for(Map map:detailList){
            if("20".equals(map.get("putInOutFlag"))&&" ".equals(map.get("putoutId"))){
                result3List.add(map);
            }else if("20".equals(map.get("putInOutFlag"))&&StrUtil.isNotBlank((String) map.get("putoutId"))){
                result4List.add(map);
            }
        }
        sheetMap.put("result2",detailList);
        sheetMap.put("result3",result3List);
        sheetMap.put("result4",result4List);
        Map resultMap = EasyExcelUtil.export2FileStorageForMutiSheet(inInfo, loginMap, hashMapList,sheetMap);
        inInfo.setBlock(new EiBlock("excelDoc")).setAttr(resultMap);
        return inInfo;
    }

    private static void extracted(List<Map<String,Object>> hashMapList,String b) {
        for (Map hashMap: hashMapList){
            //判断计算时间为0 给空值
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromRegistrationToEntry",""))){
                hashMap.put("theTimeFromRegistrationToEntry"," ");
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromEnteringTheFactoryToTheStartOfTheJob",""))){
                hashMap.put("theTimeFromEnteringTheFactoryToTheStartOfTheJob"," ");
            }
            if ("0".equals(MapUtils.getString(hashMap,"theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",""))){
                hashMap.put("theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity"," ");
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromTheCompletionOfTheJobToTheFactory",""))){
                hashMap.put("theTimeFromTheCompletionOfTheJobToTheFactory"," ");
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",""))){
                hashMap.put("theTimeFromEnteringTheFactoryToTheCompletionOfTheJob"," ");
            }
            if ("0".equals(MapUtils.getString(hashMap,"theTimeFromEnteringTheFactoryToLeavingTheFactory",""))){
                hashMap.put("theTimeFromEnteringTheFactoryToLeavingTheFactory"," ");
            }
            if ("0".equals(MapUtils.getString(hashMap,"registeredToTheFactoryTime",""))){
                hashMap.put("registeredToTheFactoryTime"," ");
            }
            if ("0时0分".equals(MapUtils.getString(hashMap,"theTimeFromTheFactoryToTheTimeOfReceipt",""))){
                hashMap.put("theTimeFromTheFactoryToTheTimeOfReceipt","0时0分");
            }
            if ("1".equals(b)){
                String isReservation = MapUtils.getString(hashMap, "isReservation", "");
                switch (isReservation) {
                    case "00":
                        hashMap.put("isReservation", "无预约单");
                        break;
                    case "10":
                        hashMap.put("isReservation", "有预约单");
                        break;
                }

                String businessType = MapUtils.getString(hashMap, "businessType", "");
                switch (businessType) {
                    case "10":
                        hashMap.put("businessType", "钢材装货");
                        break;
                    case "20":
                        hashMap.put("businessType", "钢材卸货");
                        break;
                    case "30":
                        hashMap.put("businessType", "钢材卸货+装货");
                        break;
                    case "40":
                        hashMap.put("businessType", "托盘运输");
                        break;
                    case "50":
                        hashMap.put("businessType", "资材卸货");
                        break;
                    case "60":
                        hashMap.put("businessType", "废料提货");
                        break;
                    case "70":
                        hashMap.put("businessType", "欧冶提货");
                    case "80":
                        hashMap.put("businessType", "其他物品运输");
                        break;
                }

                String status = MapUtils.getString(hashMap, "status", "");
                switch (status) {
                    case "00":
                        hashMap.put("status", "撤销");
                        break;
                    case "05":
                        hashMap.put("status", "新增");
                        break;
                    case "10":
                        hashMap.put("status", "进厂登记");
                        break;
                    case "20":
                        hashMap.put("status", "车辆进厂");
                        break;
                    case "30":
                        hashMap.put("status", "开始装卸货");
                        break;
                    case "40":
                        hashMap.put("status", "结束装卸货");
                        break;
                    case "50":
                        hashMap.put("status", "车辆出厂");
                        break;
                    case "60":
                        hashMap.put("status", "车辆签收");
                        break;
                }
                String appointmentStatus = MapUtils.getString(hashMap, "appointmentStatus", "");
                switch (appointmentStatus) {
                    case "00":
                        hashMap.put("appointmentStatus", "撤销");
                        break;
                    case "20":
                        hashMap.put("appointmentStatus", "生效");
                        break;
                    case "99":
                        hashMap.put("appointmentStatus", "完成");
                        break;
                }

                String lateEarlyFlag = MapUtils.getString(hashMap, "lateEarlyFlag", "");
                switch (lateEarlyFlag) {
                    case "0":
                        hashMap.put("lateEarlyFlag", "未标记");
                        break;
                    case "10":
                        hashMap.put("lateEarlyFlag", "迟到");
                        break;
                    case "20":
                        hashMap.put("lateEarlyFlag", "早到");
                        break;
                    case "30":
                        hashMap.put("lateEarlyFlag", "正常");
                        break;
                }
                String handType = MapUtils.getString(hashMap, "handType", "");
                switch (handType) {
                    case "10":
                        hashMap.put("handType", "装货");
                        break;
                    case "20":
                        hashMap.put("handType", "卸货");
                        break;
                    case "30":
                        hashMap.put("handType", "卸货+装货");
                        break;
                    case "40":
                        hashMap.put("handType", "托盘运输");
                        break;
                }
                String nextTarget = MapUtils.getString(hashMap, "nextTarget", "");
                switch (nextTarget) {
                    case "10":
                        hashMap.put("nextTarget", "下一装卸点");
                        break;
                    case "20":
                        hashMap.put("nextTarget", "离厂");
                        break;
                }

                String allocType = MapUtils.getString(hashMap, "allocType", "");
                switch (allocType) {
                    case "10":
                        hashMap.put("allocType", "装货配单");
                        break;
                    case "20":
                        hashMap.put("allocType", "卸货配单");
                        break;
                        case "30":
                        hashMap.put("allocType","现货交易配单");
                        break;
                    default:
                        hashMap.put("allocType","装货,卸货配单");
                }

                String ifPause = MapUtils.getString(hashMap, "ifPause", "");
                switch (ifPause) {
                    case "0":
                        hashMap.put("ifPause", "否");
                        break;
                    case "1":
                        hashMap.put("ifPause", "是");
                        break;
                }
            }
        }
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                hashmap.put("status", 10);//状态
                hashmap.put("delFlag", 0);//记录删除标记

                hashmap.put("remark", MapUtils.getString(hashmap, "remark", "").trim());//备注
                hashmap.put("uuid", StrUtil.getUUID());//UUID
                hashmap.put("recCreate", UserSession.getUserId());//创建人
                hashmap.put("recCreateTime", DateUtil.curDateTimeStr14());//创建人时间
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.insert(inInfo, LIRL0101.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", "00");//记录删除标记
                hashmap.put("delFlag", 1);//记录删除标记
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRM(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", 20);//状态
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRMNO(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0101> query = dao.query(LIRL0101.QUERY, hashmap);
                for (LIRL0101 lirl0101:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0101);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashmap.put("status", 10);//状态
                hashmap.put("recRevise", UserSession.getUserId());//修改人
                hashmap.put("recReviseTime", DateUtil.curDateTimeStr14());//修改人时间
            }
            inInfo = super.update(inInfo, LIRL0101.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String relevanceId = MapUtils.getString(queryBlock, "relevanceId", "");
        List<String> stringList = new ArrayList<>();
        stringList.add(relevanceId);
        //查询车辆作业实绩表 出库单号
        List<LIRL0308> lirl0308s = dao.query(LIRL0308.QUERY, queryBlock);
        for (LIRL0308 lirl0308:lirl0308s){
            stringList.add(lirl0308.getPutoutId());
        }
        queryBlock.put("relevanceIdList",stringList);
        queryBlock.put("relevanceId","");
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0312.QUERY, new LIRL0312(), false, null, "sub_query_status", "sub_result", "sub_result");
        return outInfo;
    }

    /**
     * 捆包附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery2(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub2_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String packId = MapUtils.getString(queryBlock, "packId", "");
        queryBlock.put("relevanceType",packId);
        EiInfo outInfo = new EiInfo();
        outInfo = super.query(inInfo, LIRL0312.QUERY, new LIRL0312(), false, null, "sub2_query_status", "sub_result2", "sub_result2");
        return outInfo;
    }

    /**
     * 安全告知签章附件附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery3(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub3_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String relevanceId = MapUtils.getString(queryBlock, "relevanceId", "");
        String driverName = MapUtils.getString(queryBlock, "driverName", "");
        String driverTel = MapUtils.getString(queryBlock, "driverTel", "");
        String driverIdentity = MapUtils.getString(queryBlock, "driverIdentity", "");
        List<String> stringList = new ArrayList<>();
        stringList.add(relevanceId);
        queryBlock.put("relevanceIdList",stringList);
        queryBlock.put("relevanceId","");
        queryBlock.put("fifleType",".pdf");
        queryBlock.put("driverName",driverName);
        queryBlock.put("driverTel",driverTel);
        queryBlock.put("driverIdentity",driverIdentity);
        queryBlock.put("segNo",segNo);
        EiInfo outInfo = new EiInfo();
        List<LIRL0312> queryLIRL0312 = this.dao.query(LIRL0312.QUERY_FILE, queryBlock);
        outInfo.addBlock("sub_result3").addBlockMeta(new LIRL0312().eiMetadata);
        outInfo.getBlock("sub_result3").setRows(queryLIRL0312);
        return outInfo;
    }

    /**
     * 安全告知签章附件附件查询.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo fileQuery4(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub4_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String relevanceId = MapUtils.getString(queryBlock, "relevanceId", "");
        String driverName = MapUtils.getString(queryBlock, "driverName", "");
        String driverTel = MapUtils.getString(queryBlock, "driverTel", "");
        String driverIdentity = MapUtils.getString(queryBlock, "driverIdentity", "");
        List<String> stringList = new ArrayList<>();
        stringList.add(relevanceId);
        queryBlock.put("relevanceIdList",stringList);
        queryBlock.put("relevanceId","");
        queryBlock.put("fifleType",".pdf");
        queryBlock.put("driverName",driverName);
        queryBlock.put("driverTel",driverTel);
        queryBlock.put("driverIdentity",driverIdentity);
        queryBlock.put("segNo",segNo);
        EiInfo outInfo = new EiInfo();
        List<LIRL0312> queryLIRL0312 = this.dao.query(LIRL0312.QUERY_FILE, queryBlock);
        outInfo.addBlock("sub_result4").addBlockMeta(new LIRL0312().eiMetadata);
        outInfo.getBlock("sub_result4").setRows(queryLIRL0312);
        return outInfo;
    }

    /*厂内作业查询页面，增加出厂照片的查看*/
    public EiInfo fileQuery5(EiInfo inInfo) {
        try{
            EiInfo outInfo = new EiInfo();
            Map queryBlock = inInfo.getBlock("sub5_query_status").getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String vehicleNo = MapUtils.getString(queryBlock, "vehicleNo", "");//车牌号
            String carTraceNo = MapUtils.getString(queryBlock, "carTraceNo", "");//车牌号

            List<String> stringList = new ArrayList<>();
            queryBlock.put("carTraceNo",carTraceNo);
            queryBlock.put("vehicleNo",vehicleNo);
            queryBlock.put("segNo",segNo);
            //查询0408表获取出厂流水号，根据车辆跟踪号，账套，车牌号进行查询
            List<HashMap> lirl0408s = dao.query("LIRL0220.queryOutNumOne", queryBlock);
            if(lirl0408s.size() > 0){
                for(HashMap lirl0408 : lirl0408s){
                    stringList.add((String)lirl0408.get("leaveFactoryId"));
                }
            }else{
                outInfo.setStatus(-1);
                outInfo.setMsg("未查询到出厂信息！");
                return outInfo;
            }
            queryBlock.put("relevanceIdList",stringList);

            List<LIRL0312> queryLIRL0312 = this.dao.query(LIRL0312.QUERY, queryBlock);
            outInfo.addBlock("sub_result5").addBlockMeta(new LIRL0312().eiMetadata);
            outInfo.getBlock("sub_result5").setRows(queryLIRL0312);
            return outInfo;
        }catch (Exception e){
            EiInfo outInfo = new EiInfo();
            outInfo.setStatus(-1);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

    }

    /****
     * 查询入库质量确认单附件
     * @return
     */
    public EiInfo fileQuery7(EiInfo inInfo) {
        try{
            EiInfo outInfo = new EiInfo();
            Map queryBlock = inInfo.getBlock("sub7_query_status").getRow(0);
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String vehicleNo = MapUtils.getString(queryBlock, "vehicleNo", "");//车牌号
            String carTraceNo = MapUtils.getString(queryBlock, "carTraceNo", "");//车牌号

            List<String> stringList = new ArrayList<>();
            queryBlock.put("relevanceId",carTraceNo);
//            queryBlock.put("vehicleNo",vehicleNo);
            queryBlock.put("segNo",segNo);
            List<LIRL0312> queryLIRL0312 = this.dao.query(LIRL0312.QUERY, queryBlock);
            outInfo.addBlock("sub_result7").addBlockMeta(new LIRL0312().eiMetadata);
            outInfo.getBlock("sub_result7").setRows(queryLIRL0312);
            return outInfo;
        }catch (Exception e){
            EiInfo outInfo = new EiInfo();
            outInfo.setStatus(-1);
            outInfo.setMsg(e.getMessage());
            return outInfo;
        }

    }


    public EiBlockMeta getExportBlockMeta() {
        EiBlockMeta eiMetadata = new EiBlockMeta();
        EiColumn eiColumn;

        eiColumn = new EiColumn("isReservation");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("是否预约");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("业务类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setFieldLength(23);
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("车辆跟踪单状态");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("allocateVehicleNo");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("配单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("allocType");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("配单类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("visitUnit");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("拜访单位");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("startOfTransport");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("运输起始地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("purposeOfTransport");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("运输目的地");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("承运商/客户名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("司机姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("telNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("司机手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("idCard");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("司机身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("appointmentStatus");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约单状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lateEarlyFlag");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("迟到早到标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("叫号时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("enterFactory");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("beginEntruckingTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业开始时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("completeUninstallTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业完成时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaveFactoryDate");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("出厂时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handType");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("装卸类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("currentHandPointId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("当前装卸点");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("currentHandPointName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("当前装卸点名称");

        eiColumn = new EiColumn("nextTarget");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("下一目标");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointId");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("目标装卸点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("targetHandPointName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("目标装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("factoryArea");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("厂区编码");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("厂区名称");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromRegistrationToEntry");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("登记至进厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromEnteringTheFactoryToTheStartOfTheJob");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂至作业开始时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业开始至作业完成时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromTheCompletionOfTheJobToTheFactory");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("作业完成至出厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromEnteringTheFactoryToTheCompletionOfTheJob");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂至作业完成时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromEnteringTheFactoryToLeavingTheFactory");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("进厂至出厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("registeredToTheFactoryTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("登记至出厂时长(分钟)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("signOffTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("签收时间");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("theTimeFromTheFactoryToTheTimeOfReceipt");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("出厂至签收时长(时分)");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("finalStationLongitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("终到站经度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("finalStationLatitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("终到站纬度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("signingLocationLongitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("签收地经度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("signingLocationLatitude");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("签收地纬度");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("longitudeLatitudeCheck");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("经纬度校验");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("voucherNum");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("提单号");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("remark");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("备注");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建人");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建人姓名");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建时间");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改人");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改人姓名");

        eiMetadata.addMeta(eiColumn);
        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改时间");



        return eiMetadata;
    }

    /***
     * 发送提货验证码
     * @param inInfo
     * @return
     */
    public EiInfo querySendCode(EiInfo inInfo) {
        List<Map> queryBlockList = inInfo.getBlock(EiConstant.resultBlock).getRows();
        if (CollectionUtils.isEmpty(queryBlockList)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("请至少勾选一条数据");
            return inInfo;
        }
        for (Map queryBlock : queryBlockList) {
            String segNo = MapUtils.getString(queryBlock, "segNo", "");
            String voucherNum = MapUtils.getString(queryBlock, "voucherNum", "");
            String driverName = MapUtils.getString(queryBlock, "driverName", "");
            String telNum = MapUtils.getString(queryBlock, "telNum", "");
            String idCard = MapUtils.getString(queryBlock, "idCard", "");
            String vehicleNo = MapUtils.getString(queryBlock, "vehicleNo", "");
            if (StringUtils.isBlank(voucherNum)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("提单号不能为空");
                return inInfo;
            }
            if (voucherNum.contains(",")){
                for (String voucherNumS : voucherNum.split(",")) {
                    if (voucherNumS.startsWith("ZK")){
                        inInfo = getZkIfo(voucherNumS, segNo, telNum);
                    }else {
                        //获取提单信息
                        inInfo = getVoucherInfo(inInfo, voucherNumS, segNo, idCard, driverName, vehicleNo, telNum);
                    }
                }
            }
            else {
                if (voucherNum.startsWith("ZK")){
                    inInfo = getZkIfo(voucherNum, segNo, telNum);
                }else {
                    inInfo = getVoucherInfo(inInfo, voucherNum, segNo, idCard, driverName, vehicleNo, telNum);
                }
            }
        }
        return inInfo;
    }

    /***
     * 获取提单信息
     * @param inInfo
     * @param voucherNumS
     * @param segNo
     * @param idCard
     * @param driverName
     * @param vehicleNo
     * @param telNum
     * @return
     */
    private EiInfo getVoucherInfo(EiInfo inInfo, String voucherNumS, String segNo, String idCard, String driverName, String vehicleNo, String telNum) {
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        hashMap.put("driverId", idCard);
        hashMap.put("driver", driverName);
        hashMap.put("ladingBillStatus","50");
        hashMap.put("ladingBillIdEq", voucherNumS);
        //先查询提单是否存在，不存在调服务写入车辆登记提单信息表
        String xplatToken = TokenUtils.getXplatToken();
        inInfo.set(EiConstant.serviceId, "S_UV_SL_9020");//查询提单服务
        inInfo.set("main", hashMap);
        EiInfo outInfo = EServiceManager.call(inInfo,xplatToken);
        if (outInfo.getStatus() == -1) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
            throw new RuntimeException(inInfo.getMsg());
        }
        List<HashMap> list = (List<HashMap>) outInfo.get("result");
        if (CollectionUtils.isNotEmpty(list)) {
            for (HashMap map : list) {
                String shipVerifiCode = MapUtils.getString(map, "shipVerifiCode");
                String noPaperFlag = MapUtils.getString(map, "noPaperFlag"); //Y 电子提单
                String userName = MapUtils.getString(map, "userName"); //Y 电子提单
                String ladingSpotName = MapUtils.getString(map, "ladingSpotName"); //Y 电子提单
                String totalWeight = MapUtils.getString(map, "totalWeight"); //Y 电子提单
                if ("Y".equals(noPaperFlag)){
                    if (StringUtils.isNotBlank(shipVerifiCode)){
                        //发送短信
//                        String content = "委托提货通知：请到提货点凭提货验证码："+shipVerifiCode+"，对以下提单提货：["+ voucherNumS +"-"+userName+"-"+ladingSpotName+"-重量:"+new BigDecimal(totalWeight).setScale(3) +"-"+ idCard +"-"+ vehicleNo +"]。";
//                        EiInfo vzbmInfo = new EiInfo();
//                        outInfo.set("content",content);
//                        outInfo.set("mobileNum", telNum);
//                        vzbmInfo = SmsSendManager.sendMobile(outInfo);
//                        if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
//                            //打印日志到elk
//                            log(  "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            //输出到应用日志
//                            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            throw new RuntimeException(vzbmInfo.toJSONString());
//                        } else {
//                            //打印日志到elk
//                            log( "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            //输出到应用日志
//                            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                        }
//                        return vzbmInfo;
                        HashMap<String, Object> messageMap = new HashMap<>();
                        messageMap.put("param1",telNum);
                        messageMap.put("param2",shipVerifiCode);
                        messageMap.put("param3",voucherNumS);
                        messageMap.put("param4",userName);
                        messageMap.put("param5",ladingSpotName);
                        messageMap.put("param6",new BigDecimal(totalWeight).setScale(3));
                        messageMap.put("param7",idCard);
                        messageMap.put("param8",vehicleNo);
                        MessageUtils.sendMessage(messageMap,"MT0000001009");
                    }
                }else {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("提单号："+ voucherNumS +"为纸质提单，无法获取验证码!");
                    throw new RuntimeException(inInfo.getMsg());
                }
            }
        }
        return inInfo;
    }

    /***
     * s获取转库单信息
     * @param voucherNumS
     * @param segNo
     * @param telNum
     * @return
     */
    private EiInfo getZkIfo(String voucherNumS, String segNo, String telNum) {
        //调IMC服务拿到捆包信息
        EiInfo eiInfo1 = new EiInfo();
        eiInfo1.set("segNo", segNo);
        eiInfo1.set("transBillId", voucherNumS);
        eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0044");
        //调post请求
        EiInfo outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
        if (outInfo.getStatus() == -1) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("调用物流中心返回报错:" + outInfo.getMsg());
            throw new RuntimeException(outInfo.getMsg());
        }
        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("未匹配到转库单信息!");
            return outInfo;
        }
        if (CollectionUtils.isNotEmpty(rusult)) {
            for (HashMap hashMap : rusult) {
                String billPassword = MapUtils.getString(hashMap, "billPassword");
                String ebillMark = MapUtils.getString(hashMap, "ebillMark"); //Y 电子提单
                if (ebillMark.equals("1")){
                    if (!StringUtils.isEmpty(billPassword)){
                        //发送短信
//                        String content = "转库单提货通知：请到提货点凭提货单密码："+billPassword+"，对以下转库单提货："+ voucherNumS +"。";
//                        EiInfo vzbmInfo = new EiInfo();
//                        outInfo.set("content",content);
//                        outInfo.set("mobileNum", telNum);
//                        vzbmInfo = SmsSendManager.sendMobile(outInfo);
//                        if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
//                            //打印日志到elk
//                            log(  "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            //输出到应用日志
//                            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            throw new RuntimeException(vzbmInfo.toJSONString());
//                        } else {
//                            //打印日志到elk
//                            log( "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            //输出到应用日志
//                            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                        }
//                        return vzbmInfo;
                        HashMap<String, String> messageMap = new HashMap<>();
                        messageMap.put("param1",telNum);
                        messageMap.put("param2",billPassword);
                        messageMap.put("param3",voucherNumS);
                        MessageUtils.sendMessage(messageMap,"MT0000001009");
                    }
                }else {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("转库单号："+voucherNumS+"为纸质提单，无法获取验证码!");
                    return outInfo;
                }
            }
        }
        return outInfo;
    }
    /***
     * 生成运输计划
     *
     */
    public EiInfo createTransportPlan(EiInfo inInfo) {
        List<HashMap> result = inInfo.getBlock("result").getRows();
        if (CollectionUtils.isEmpty(result)){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("至少勾选一条数据!");
            return inInfo;
        }
        try {
            for (HashMap hashMap : result) {
                String carTraceNo = (String) hashMap.get("carTraceNo");
                String segNo = (String) hashMap.get("segNo");
                String vehicleNo = (String) hashMap.get("vehicleNo");
                String status = (String) hashMap.get("status");
                int statusI = Integer.parseInt(status);
                if (statusI<50) {
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg("车辆出厂/签收状态才可以进行运输计划返单!");
                    return inInfo;
                }
//                //查询车辆出厂运输返单状态
//                HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//                objectObjectHashMap.put("segNo", segNo);
//                objectObjectHashMap.put("carTraceNo", carTraceNo);
//                objectObjectHashMap.put("vehicleNo", vehicleNo);
//                List<LIRL0408> queryLIRL0408 = this.dao.query(LIRL0408.QUERY, objectObjectHashMap);
//                if (CollectionUtils.isNotEmpty(queryLIRL0408)){
//                    String backStatus = queryLIRL0408.get(0).getBackStatus();
//                    if (!"-1".equals(backStatus)){
//                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                        inInfo.setMsg("运输计划已经生成不允许再次生成!");
//                        return inInfo;
//                    }
//                }
                //增加运输返单功能
                List<String> billIds = new ArrayList<>();//提单集合
                List<String> packIds = new ArrayList<>();//捆包集合
                List<HashMap> list = new ArrayList<>();//捆包集合
                HashMap<String, Object> hashMapTwo = new HashMap<>();
                //根据车辆跟踪号、车牌号、账套查询提单、捆包明细
                HashMap<String, String> hashMapOne = new HashMap<>();
                hashMapOne.put("segNo", segNo);
                hashMapOne.put("carTraceNo", carTraceNo);
                hashMapOne.put("vehicleNo", vehicleNo);

                billIds = this.dao.query(LIRL0308.QUERY_B_LINFO, hashMapOne);
                if (CollectionUtils.isNotEmpty(billIds)){
                    //查询捆包
                    packIds = this.dao.query(LIRL0308.QUERY_P_CINFO, hashMapOne);

                    hashMapTwo.put("billIds",billIds);
                    hashMapTwo.put("packIds",packIds);
                    hashMapTwo.put("channelTransId",carTraceNo);
                    hashMapTwo.put("operateType","10");
                    list.add(hashMapTwo);

                    EiInfo outInfo =new EiInfo();
                    inInfo.set("list",list);
                    inInfo.set("segNo",segNo);
                    inInfo.set(EiConstant.serviceId,"S_UC_PR_0424");
                    try {
                        outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE){
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg(outInfo.getMsg());
                            return outInfo;
                        }
                    } catch (Exception e) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg(e.getMessage());
                        return outInfo;
                    }
                }
                //生成运输计划
            }
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /***
     * 查询每个点的装卸时长
     */
    public EiInfo queryLoadingAndUnloadingTypesAndData(EiInfo inInfo) {
        Map queryBlock = inInfo.getBlock("sub6_query_status").getRow(0);
        String segNo = MapUtils.getString(queryBlock, "segNo", "");
        String vehicleNo = MapUtils.getString(queryBlock, "vehicleNo", "");//车牌号
        String carTraceNo = MapUtils.getString(queryBlock, "carTraceNo", "");//车牌号
        try {
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("carTraceNo",carTraceNo);
            List<HashMap> queryLIRL0407 = this.dao.query(LIRL0407.QUERY_ALL_HAND_POINT_TIME, hashMap);
            inInfo.addBlock("sub_result6").setRows(queryLIRL0407);
        }
        catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }
    /***
     * 打印入库质量确认单
     * 	S_LI_RL_0186
     */
    public EiInfo printPutinQualityConfirm(EiInfo eiInfo) {
        List<Map> queryBlockList = eiInfo.getBlock(EiConstant.resultBlock).getRows();
        if (CollectionUtils.isEmpty(queryBlockList)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("请至少勾选一条数据");
            return eiInfo;
        }
        for (Map map : queryBlockList) {
            String segNo = (String) map.get("segNo");
            String allocateVehicleNo = (String) map.get("allocateVehicleNo");
            if (StringUtils.isBlank(allocateVehicleNo)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("当前车辆没有配单信息，不能打印！");
                return eiInfo;
            }
            if (StringUtils.isNotBlank(allocateVehicleNo)){
                try {
                    ArrayList<String> collect = new ArrayList<>();
//            组装附件信息
                    List<HashMap> resultFile = new ArrayList<>();
                    String reportUrl = PlatApplicationContext.getProperty("billPrint.Cq") + PlatApplicationContext.getProperty("P.printParam.Quality.Cq") + "&allocateVehicleNo=" + allocateVehicleNo + "&segNo=" + segNo + "&format=PDF";
                    collect.add(reportUrl);
//                //接入自动打印
//                //查询生效的出库单打印机配置
//                String printName1 = findPrintName(segNo,"20");
//                for (String s : collect) {
//                    HashMap<String, Object> hashMap = new HashMap<>();
//                    hashMap.put("printName",printName1);
//                    hashMap.put("uploadFilePath",s);
//                    resultFile.add(hashMap);
//                }
//
//                // 待新增的附件记录
//                LIRL0312 lirl0312 = new LIRL0312();
//                // lirl0312.setRelevanceId(id);
//                // 文件信息
//                lirl0312.setUploadFileName("入库质量确认书"+DateUtil.curDateTimeStr14()+".pdf");
//                lirl0312.setFifleType(".pdf");
//                lirl0312.setFifleSize(new BigDecimal(10000));
//                lirl0312.setFileId(UUIDUtils.getUUID());
//                // 设置文件下载路径
//                lirl0312.setUploadFilePath(reportUrl);
//                lirl0312.setRecCreator("System");
//                lirl0312.setRecCreatorName("System");
//                lirl0312.setRecRevisor("System");
//                lirl0312.setRecRevisorName("System");
//                lirl0312.setSignatureMark(" ");
//                lirl0312.setSegNo(segNo);
//                lirl0312.setUnitCode(segNo);
//                lirl0312.setRecCreateTime(DateUtil.curDateTimeStr14());
//                lirl0312.setRecReviseTime(DateUtil.curDateTimeStr14());
//                lirl0312.setUuid(UUIDUtils.getUUID());
//                lirl0312.setRelevanceType(UUIDUtils.getUUID());
//                lirl0312.setRelevanceId(carTraceNo);
//                Map insMap = lirl0312.toMap();
//                dao.insert(LIRL0312.INSERT, insMap);
//
//                String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(segNo, "IF_QUALITY_REMOTE_PRINT", dao);
//                String printName = "";
//                String printPort = "";
//                if ("1".equals(ifRemotePrint)) {
//                    for (HashMap url : resultFile) {
//                        String printNameAll = (String) url.get("printName");
//                        String[] split = printNameAll.split("-");
//                        printName= split[0];
//                        printPort=split[1];
//                        uploadPrintFile(MapUtils.getString(url, "uploadFilePath"), "/" + segNo + ".pdf", segNo, "URLTC", printName, printPort);
//                    }
//                } else {
                    eiInfo.set("docUrlList", collect);
//                }
                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                } catch (Exception e) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(e.getMessage());
                }
            }
        }
        eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return eiInfo;
    }
}
