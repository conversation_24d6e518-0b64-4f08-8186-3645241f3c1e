package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.vg.dm.domain.VGDM0402;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 点检完成率统计页面
 * @Date : 2024/4/18
 * @Version : 1.0
 */
public class ServiceVGDM0403 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM0403.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0402().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            Map param = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            List<Map> resultList = dao.query(VGDM0402.QUERY_DO_RATE, param);
            EiBlock resultBlock = inInfo.addBlock(EiConstant.resultBlock);
            resultBlock.set(EiConstant.limitStr, resultList.size());
            resultBlock.setRows(resultList);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2000, new String[]{resultList.size() + ""});
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{e.getMessage()});
        }
        return inInfo;
    }

    /**
     * 汇总查询
     */
    public EiInfo query2(EiInfo inInfo) {
        try {
            if (DaoUtils.isEmptyUnit(inInfo)) {
                return inInfo;
            }
            Map param = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            List<Map> resultList = dao.query(VGDM0402.QUERY_DO_RATE2, param);
            EiBlock resultBlock = inInfo.addBlock(EiConstant.resultBlock);
            resultBlock.set(EiConstant.limitStr, resultList.size());
            resultBlock.setRows(resultList);
            // 返回消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2000, new String[]{resultList.size() + ""});
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{e.getMessage()});
        }
        return inInfo;
    }
}
