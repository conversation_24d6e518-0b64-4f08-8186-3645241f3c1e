package com.baosight.imom.common.utils;

import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 工程项目:.
 * 开发公司:Baosight Software LTD.co Copyright (c) 2022.
 * 类的简介: 解析文件-ResolveFileUtils.
 * 类的描述: 解析前端用户上传文件
 *
 * <AUTHOR>
 * @version 1.0 （开发版本）.
 * @Date 2022/7/14 10:03.
 * @since 1.8 （JDK版本号）.
 */
public class ResolveFileUtils {
    /**
     * 解析excel文件
     *
     * @param ins
     * @param ignoreRows
     * @return lang.String[][]
     * <AUTHOR>
     * @date 2022/5/18 15:49
     */
    public static String[][] getDataByInputStream(InputStream ins, int ignoreRows) throws Exception {
        List<String[]> result = new ArrayList();
        int rowSize = 0;
        Workbook wb = WorkbookFactory.create(ins);
        Cell cell = null;

        for (int sheetIndex = 0; sheetIndex < wb.getNumberOfSheets(); ++sheetIndex) {
            Sheet st = wb.getSheetAt(sheetIndex);

            for (int rowIndex = ignoreRows; rowIndex <= st.getLastRowNum(); ++rowIndex) {
                Row row = st.getRow(rowIndex);
                if (row != null) {
                    int tempRowSize = row.getLastCellNum() + 1;
                    if (tempRowSize > rowSize) {
                        rowSize = tempRowSize;
                    }

                    String[] values = new String[rowSize];
                    Arrays.fill(values, "");
                    boolean hasValue = false;

                    for (short columnIndex = 0; columnIndex <= row.getLastCellNum(); ++columnIndex) {
                        String value = "";
                        cell = row.getCell(columnIndex);
                        if (cell != null) {
                            switch (cell.getCellType()) {
                                case 0:
                                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                                        Date date = cell.getDateCellValue();
                                        if (date != null) {
                                            value = (new SimpleDateFormat("yyyy-MM-dd")).format(date);
                                        } else {
                                            value = "";
                                        }
                                    } else {
                                        value = (new DecimalFormat("0.000000")).format(cell.getNumericCellValue());
                                        if (".000000".equals(value.substring(value.indexOf("."), value.length()))) {
                                            value = (new DecimalFormat("0")).format(cell.getNumericCellValue());
                                        }/* else {
                                            value = String.valueOf(cell.getNumericCellValue());
                                        }*/
                                    }
                                    break;
                                case 1:
                                    value = cell.getStringCellValue();
                                    break;
                                case 2:
                                    if (!"".equals(cell.getStringCellValue())) {
                                        value = cell.getStringCellValue();
                                    } else {
                                        value = cell.getNumericCellValue() + "";
                                    }
                                case 3:
                                    break;
                                case 4:
                                    value = cell.getBooleanCellValue() ? "Y" : "N";
                                    break;
                                case 5:
                                    value = "";
                                    break;
                                default:
                                    value = "";
                            }
                        }

                        if (columnIndex == 0 && "".equals(value.trim())) {
                            break;
                        }

                        values[columnIndex] = rightTrim(value);
                        hasValue = true;
                    }

                    if (hasValue) {
                        result.add(values);
                    }
                }
            }
        }

        ins.close();
        String[][] returnArray = new String[result.size()][rowSize];

        for (int i = 0; i < returnArray.length; ++i) {
            returnArray[i] = (String[]) ((String[]) result.get(i));
        }

        return returnArray;
    }

    /**
     * 去除字符串空格
     *
     * @param str
     * @return lang.String
     * <AUTHOR>
     * @date 2022/7/14 10:04
     */
    private static String rightTrim(String str) {
        if (str == null) {
            return "";
        } else {
            int length = str.length();

            for (int i = length - 1; i >= 0 && str.charAt(i) == ' '; --i) {
                --length;
            }

            return str.substring(0, length);
        }
    }
}
