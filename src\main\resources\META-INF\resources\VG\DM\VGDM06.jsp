<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imom">
    <EF:EFRegion id="inqu" title="查询条件">
        <EF:EFInput ename="inqu_status-0-segNo" cname="系统账套" type="hidden"/>
        <div class="row">
            <EF:EFPopupInput ename="inqu_status-0-unitCode" cname="业务单元代码" colWidth="3"
                             readonly="true" clear="false" containerId="unitInfo" originalInput="true"
                             center="true" required="true">
            </EF:EFPopupInput>
            <EF:EFInput ename="inqu_status-0-segName" cname="业务单元简称" colWidth="3" disabled="true"
                        required="true"/>
            <EF:EFInput ename="inqu_status-0-eArchivesNo" cname="设备代码" placeholder="模糊条件" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false" containerId="equipmentInfo" center="true"
                             ename="inqu_status-0-equipmentName" cname="设备名称" placeholder="模糊条件" colWidth="3"/>

        </div>
        <div class="row">
            <EF:EFInput ename="inqu_status-0-deviceCode" placeholder="模糊条件" cname="分部设备代码" colWidth="3"/>
            <EF:EFPopupInput originalInput="true" clear="false"
                             containerId="deviceInfo" placeholder="模糊条件" ename="inqu_status-0-deviceName"
                             cname="分部设备名称" colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-alarmId" cname="报警编号" placeholder="模糊条件"
                        colWidth="3"/>
            <EF:EFInput ename="inqu_status-0-alarmTag" cname="TAG点" placeholder="模糊条件"
                        colWidth="3"/>

        </div>
        <div class="row">
            <EF:EFSelect ename="inqu_status-0-alarmState" cname="报警状态" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="未恢复" value="未恢复"/>
                <EF:EFOption label="已恢复" value="已恢复"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-confirmStatus" cname="确认状态" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="未确认" value="未确认"/>
                <EF:EFOption label="已确认" value="已确认"/>
            </EF:EFSelect>
            <EF:EFSelect ename="inqu_status-0-alarmTypedm" cname="报警类型" colWidth="3">
                <EF:EFOption label="全部" value=""/>
                <EF:EFOption label="低低报警" value="0"/>
                <EF:EFOption label="低报警" value="1"/>
                <EF:EFOption label="高报警" value="2"/>
                <EF:EFOption label="高高报警" value="3"/>
            </EF:EFSelect>
            <EF:EFDateSpan startName="inqu_status-0-occurTimeStart" role="datetime"
                           endName="inqu_status-0-occurTimeEnd" readonly="true"
                           startCname="报警时间(起)" endCname="报警时间(止)"
                           ratio="3:3" format="yyyy-MM-dd HH:mm:ss" interval="10">
            </EF:EFDateSpan>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="detail" title="报警推送监控">
        <div class="row">
            <EF:EFInput ename="detail_status-0-clientStatus" cname="报警推送状态" colWidth="3" disabled="true"/>
<%--            <label style="color: red;">目前连接的是平台测试环境，报警会一直推送，监听不要长时间开启</label>--%>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="result" title="查询结果">
        <EF:EFGrid blockId="result" autoDraw="no" checkMode="multiple, row" readonly="true" sort="all" isFloat="true">
            <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
            <EF:EFColumn ename="unitCode" cname="业务单元代码" align="center"/>
            <EF:EFComboColumn ename="segNo" cname="业务单元简称" align="center" sort="flase"
                              blockName="unitBlock" valueField="segNo" textField="segName"/>
            <EF:EFColumn ename="alarmId" cname="报警编号" align="center" width="90"/>
            <EF:EFColumn ename="eArchivesNo" cname="设备代码" width="100" align="center"/>
            <EF:EFColumn ename="equipmentName" cname="设备名称"/>
            <EF:EFColumn ename="deviceCode" cname="分部设备代码" width="110" align="center" enable="false"/>
            <EF:EFColumn ename="deviceName" cname="分部设备名称" enable="false"/>
            <EF:EFColumn ename="confirmStatus" cname="确认状态" align="center" width="70"/>
            <EF:EFColumn ename="alarmState" cname="报警状态" align="center" width="70"/>
            <EF:EFColumn ename="alarmTag" cname="TAG名"/>
            <EF:EFColumn ename="alarmTagDesc" cname="TAG描述"/>
            <EF:EFColumn ename="alarmType" cname="报警类型" align="center" width="70"/>
            <EF:EFColumn ename="alarmTagValue" cname="报警值"/>
            <EF:EFColumn ename="occurTime" cname="发生时间" width="160" align="center"/>            
            <EF:EFColumn ename="priority" cname="优先级" align="center" width="70"/>
            <EF:EFColumn ename="recoverTagValue" cname="恢复值"/>
            <EF:EFColumn ename="recoverTime" cname="恢复时间" width="160" align="center"/>
            <EF:EFColumn ename="repeatCount" cname="重复次数" align="center" width="70"/>
            <EF:EFColumn ename="confirmorName" cname="确认人" align="center" width="70"/>
            <EF:EFColumn ename="confirmTime" editType="datetime" width="140" align="center"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyy-MM-dd HH:mm:ss" cname="确认时间"/>
            <EF:EFColumn ename="scadaName" cname="scada名称" align="center" width="90"/>
            <EF:EFComboColumn ename="isFault" cname="是否故障" align="center" width="90">
                <EF:EFOption value="1" label="是"/>
                <EF:EFOption value="0" label="否"/>
            </EF:EFComboColumn>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow url="${ctx}/web/XTSS01" id="unitInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0101" id="equipmentInfo" width="90%" height="60%"/>
    <EF:EFWindow url="${ctx}/web/VGDM0102" id="deviceInfo" width="90%" height="60%"/>
</EF:EFPage>
