$(function () {
    // 业务单元默认条件
    let unitInfo = IMOMUtil.fillUnitInfo();

    //编辑行
    let editorModel;
    //查询
    $("#QUERY").on("click", function (e) {
        const unitCode = $("#inqu_status-0-unitCode").val();
        const segNo = $("#inqu_status-0-segNo").val();
        if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
            NotificationUtil({msg: "请选择业务单元代码!"}, "error");
            return;
        }
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [
                {
                    field: "transferCarId",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "过跨小车编号",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "eArchivesInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "过跨小车查询"
                            })
                        }
                    }
                },
                {
                    field: "transferCarName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "过跨小车名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "eArchivesInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "过跨小车查询"
                            })
                        }
                    }
                },
                {
                    field: "crossArea",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "跨区编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "跨区编码查询"
                            })
                        }
                    }
                },
                {
                    field: "crossAreaName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "跨区名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossAreaInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "跨区编码查询"
                            })
                        }
                    }
                },
                {
                    field: "crossingChannels",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "过跨通道编码",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "过跨通道编码查询"
                            })
                        }
                    }
                },
                {
                    field: "crossingChannelsName",
                    enable: true,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    title: "过跨通道名称",
                    editor: function (container, param) {
                        // 设置产生弹框model
                        if (container.hasClass("fake-edit")) {
                            container.removeClass("fake-edit");
                        } else {
                            editorModel = param.model;
                            IPLAT.Popup.popupContainer({
                                containerId: "crossingChannelsInfo",
                                textElement: $(container),
                                width: 600,
                                center: true,
                                title: "过跨通道编码查询"
                            })
                        }
                    }
                }
            ],
            // 在Grid加载完成后，才能给Grid上的按钮绑定事件
            loadComplete: function (grid) {

            },
            /**
             *双击选中
             */
            onRowDblClick: function (e) {
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    //双击选中前先把双击的数据勾选上
                    resultGrid.unCheckAllRows();
                    resultGrid.setCheckedRows(e.row);
                    //关闭下拉框
                    window.parent[windowId + "Window"].close();
                }
            },
            beforeAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                }
            },
            afterAdd: function (e) {
                const unitCode = $("#inqu_status-0-unitCode").val();
                const segName = $("#inqu_status-0-segName").val();
                const segNo = $("#inqu_status-0-segNo").val();
                if (IPLAT.isBlankString(unitCode) || IPLAT.isBlankString(segNo)) {
                    NotificationUtil({msg: "请选择业务单元代码!"}, "error");
                    e.preventDefault();
                    return;
                }
                resultGrid.setCellValue(0, 'segNo', segNo);
                resultGrid.setCellValue(0, 'unitCode', unitCode);
                resultGrid.setCellValue(0, 'segName', segName);
                resultGrid.setCellValue(0, 'status', "");
                resultGrid.refresh();
            },
            beforeEdit: function (e) {
                //作为弹框显示时不可编辑
                let windowId = $("#inqu_status-0-windowId").val();
                if (!IPLAT.isBlankString(windowId) && typeof windowId !== 'undefined') {
                    e.preventDefault();
                    return;
                }
                /**
                 * 状态不为新增时，不允许编辑
                 */
                if(e.model.status !== "10" && !e.model.isNew()){
                    e.preventDefault();
                    return;
                }
            },
            onDelete: function (e) {
                let rows = e.sender.getCheckedRows();
                for (let i = 0; i < rows.length; i++) {
                    if (rows[i].status !== "10") {
                        NotificationUtil({msg: rows[i].transferCarId + ",勾选数据状态不为新增,不可删除!"}, "error");
                        e.preventDefault();
                        return;
                    }
                }
            }
        }
    }

    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 列表业务单元弹窗
    IMOMUtil.importUnitInfo({
        windowId: "unitInfo",
        notInqu: true,
        afterSelect: function (rows) {
            if (rows.length > 0) {
                let rowNums = resultGrid.getCheckedRowsIndex();
                unitInfo = rows[0];
                $("#inqu_status-0-unitCode").val(rows[0].unitCode);
                $("#inqu_status-0-segNo").val(rows[0].segNo);
                $("#inqu_status-0-segName").val(rows[0].segName);
            }
        }
    });
    //跨区管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossAreaInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                //校验勾选厂区，厂房必须一致(取第一条的数据)
                let factoryArea = rows[0].factoryArea;
                let factoryBuilding = rows[0].factoryBuilding;
                for (let i = 1; i < rows.length; i++) {
                    if (rows[i].factoryArea !== factoryArea || rows[i].factoryBuilding !== factoryBuilding) {
                        NotificationUtil({msg: "所勾选的跨区信息中，厂区厂房不一致!"}, "error");
                        return;
                    }
                }
                //如果勾选多条，跨区信息按,号分隔
                let crossArea = rows.map(c =>c.crossArea).join(`,`);
                let crossAreaName = rows.map(c =>c.crossAreaName).join(`,`);
                resultGrid.setCellValue(editorModel, "crossArea", crossArea);
                resultGrid.setCellValue(editorModel, "crossAreaName", crossAreaName);
                resultGrid.setCellValue(editorModel, "factoryArea", rows[0].factoryArea);
                resultGrid.setCellValue(editorModel, "factoryAreaName", rows[0].factoryAreaName);
                resultGrid.setCellValue(editorModel, "factoryBuilding", rows[0].factoryBuilding);
                resultGrid.setCellValue(editorModel, "factoryBuildingName", rows[0].factoryBuildingName);
            }
        }
    });
    //厂区区域管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "crossingChannelsInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为启用
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("30");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);
            //类型为过跨通道
            iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-areaType").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "crossingChannels", rows[0].areaCode);
                resultGrid.setCellValue(editorModel, "crossingChannelsName", rows[0].areaName);
            }
        }
    });


    //厂区厂房管理弹窗
    IMOMUtil.windowTemplate({
        windowId: "factoryAreaInfo",
        _open: function (e, iframejQuery) {
            console.log(e);
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segName = $("#inqu_status-0-segName").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segName").val(segName);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("#inqu_status-0-status").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            //状态为生效
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").value("20");
            iframejQuery("#inqu_status-0-status").data("kendoDropDownList").enable(false);

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-factoryArea").val(rows[0].factoryArea);
                $("#inqu_status-0-factoryAreaName").val(rows[0].factoryAreaName);
                $("#inqu_status-0-factoryBuilding").val(rows[0].factoryBuilding);
                $("#inqu_status-0-factoryBuildingName").val(rows[0].factoryBuildingName);
            }
        }
    });

    //设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "eArchivesInfo",
        _open: function (e, iframejQuery) {
            const unitCode = $("#inqu_status-0-unitCode").val();
            const segNo = $("#inqu_status-0-segNo").val();
            if (unitCode == null || IPLAT.isBlankString(unitCode) || segNo == null || IPLAT.isBlankString(segNo)) {
                NotificationUtil("操作失败，原因[请先选择业务单元！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-unitCode").val(unitCode);
            iframejQuery("#inqu_status-0-segNo").val(segNo);
            iframejQuery("#inqu_status-0-unitCode").prop("disabled", true);
            iframejQuery("span[data-target='inqu_status-0-unitCode'][data-action='clear']").hide();
            iframejQuery("#inqu_status-0-processCategory").val("CT");//过跨小车
            iframejQuery("#inqu_status-0-processCategorySub").val("GK");//过跨小车

        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                resultGrid.setCellValue(editorModel, "transferCarId", rows[0].eArchivesNo);
                resultGrid.setCellValue(editorModel, "transferCarName", rows[0].equipmentName);
            }
        }
    });
})