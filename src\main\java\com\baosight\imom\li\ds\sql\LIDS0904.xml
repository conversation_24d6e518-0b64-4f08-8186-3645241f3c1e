<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2025-07-11 09:17:25
   		Version :  1.0
		tableName :${meliSchema}.tlids0904
		 SEG_NO  VARCHAR   NOT NULL,
		 UNIT_CODE  VARCHAR   NOT NULL,
		 PACK_ID  VARCHAR   NOT NULL,
		 NET_WEIGHT  DECIMAL,
		 GROSS_WEIGHT  DECIMAL,
		 QUANTITY  DECIMAL,
		 DATA_SOURCE  VARCHAR,
		 CRANE_RESULT_ID  VARCHAR,
		 WAREHOUSE_CODE  VARCHAR,
		 WAREHOUSE_NAME  VARCHAR,
		 LOCATION_ID  VARCHAR,
		 LOCATION_NAME  VARCHAR,
		 PUTOUT_LOCATION_ID  VARCHAR,
		 PUTOUT_LOCATION_NAME  VARCHAR,
		 REC_CREATOR  VARCHAR,
		 REC_CREATOR_NAME  VARCHAR,
		 REC_CREATE_TIME  VARCHAR,
		 REC_REVISOR  VARCHAR,
		 REC_REVISOR_NAME  VARCHAR,
		 REC_REVISE_TIME  VARCHAR,
		 ARCHIVE_FLAG  VARCHAR,
		 TENANT_USER  VARCHAR,
		 DEL_FLAG  SMALLINT,
		 REMAEK  VARCHAR,
		 UUID  VARCHAR   NOT NULL   primarykey,
		 LABEL_ID  VARCHAR
	-->
<sqlMap namespace="LIDS0904">

	<select id="query" parameterClass="java.util.Map"
			resultClass="com.baosight.imom.li.ds.domain.LIDS0904">
		SELECT
		SEG_NO as "segNo",  <!-- 系统账套 -->
		UNIT_CODE as "unitCode",  <!-- 业务单元代代码 -->
		(select tt.SEG_NAME from ${platSchema}.TVZBM81 tt where tt.SEG_NO = t.SEG_NO and tt.DEL_FLAG = 0) as
		"segName", <!-- 业务单元简称 -->
		PACK_ID as "packId",  <!-- 捆包号 -->
		NET_WEIGHT as "netWeight",  <!-- 净重 -->
		GROSS_WEIGHT as "grossWeight",  <!-- 毛重 -->
		QUANTITY as "quantity",  <!-- 件数 -->
		DATA_SOURCE as "dataSource",  <!-- 数据来源 -->
		CRANE_RESULT_ID as "craneResultId",  <!-- 行车实绩单号 -->
		WAREHOUSE_CODE as "warehouseCode",  <!-- 仓库代码 -->
		WAREHOUSE_NAME as "warehouseName",  <!-- 仓库名称 -->
		LOCATION_ID as "locationId",  <!-- 库位代码 -->
		LOCATION_NAME as "locationName",  <!-- 库位名称 -->
		PUTOUT_LOCATION_ID as "putoutLocationId",  <!-- 转出库位代码 -->
		PUTOUT_LOCATION_NAME as "putoutLocationName",  <!-- 转出库位名称 -->
		REC_CREATOR as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		TENANT_USER as "tenantUser",  <!-- 租户 -->
		DEL_FLAG as "delFlag",  <!-- 删除标记 -->
		REMAEK as "remaek",  <!-- 备注 -->
		UUID as "uuid",  <!-- ID -->
		LABEL_ID as "labelId" <!-- 标签号 -->
		FROM ${meliSchema}.tlids0904 t WHERE 1=1
		AND SEG_NO = #segNo#
		<isNotEmpty prepend="AND" property="dataSource">
			DATA_SOURCE = #dataSource#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="packId">
			PACK_ID LIKE '%$packId$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="locationId">
			LOCATION_ID LIKE '%$locationId$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="locationName">
			LOCATION_NAME LIKE '%$locationName$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="putoutLocationId">
			PUTOUT_LOCATION_ID LIKE '%$putoutLocationId$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="putoutLocationName">
			PUTOUT_LOCATION_NAME LIKE '%$putoutLocationName$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTimeStart">
			REC_CREATE_TIME >= #recCreateTimeStart#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTimeEnd">
			REC_CREATE_TIME &lt;= #recCreateTimeEnd#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				REC_CREATE_TIME DESC
			</isEmpty>
		</dynamic>
	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlids0904 WHERE 1=1
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</select>

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlids0904 (SEG_NO,  <!-- 系统账套 -->
		UNIT_CODE,  <!-- 业务单元代代码 -->
		PACK_ID,  <!-- 捆包号 -->
		NET_WEIGHT,  <!-- 净重 -->
		GROSS_WEIGHT,  <!-- 毛重 -->
		QUANTITY,  <!-- 件数 -->
		DATA_SOURCE,  <!-- 数据来源 -->
		CRANE_RESULT_ID,  <!-- 行车实绩单号 -->
		WAREHOUSE_CODE,  <!-- 仓库代码 -->
		WAREHOUSE_NAME,  <!-- 仓库名称 -->
		LOCATION_ID,  <!-- 库位代码 -->
		LOCATION_NAME,  <!-- 库位名称 -->
		PUTOUT_LOCATION_ID,  <!-- 转出库位代码 -->
		PUTOUT_LOCATION_NAME,  <!-- 转出库位名称 -->
		REC_CREATOR,  <!-- 记录创建人 -->
		REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME,  <!-- 记录创建时间 -->
		REC_REVISOR,  <!-- 记录修改人 -->
		REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME,  <!-- 记录修改时间 -->
		ARCHIVE_FLAG,  <!-- 归档标记 -->
		TENANT_USER,  <!-- 租户 -->
		DEL_FLAG,  <!-- 删除标记 -->
		REMAEK,  <!-- 备注 -->
		UUID,  <!-- ID -->
		LABEL_ID  <!-- 标签号 -->
		)
		VALUES (#segNo#, #unitCode#, #packId#, #netWeight#, #grossWeight#, #quantity#, #dataSource#, #craneResultId#,
		#warehouseCode#, #warehouseName#, #locationId#, #locationName#, #putoutLocationId#, #putoutLocationName#,
		#recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#,
		#tenantUser#, #delFlag#, #remaek#, #uuid#, #labelId#)
	</insert>

	<delete id="delete">
		DELETE FROM ${meliSchema}.tlids0904 WHERE
			UUID = #uuid#
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlids0904
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代代码 -->  
					PACK_ID	= #packId#,   <!-- 捆包号 -->  
					NET_WEIGHT	= #netWeight#,   <!-- 净重 -->  
					GROSS_WEIGHT	= #grossWeight#,   <!-- 毛重 -->  
					QUANTITY	= #quantity#,   <!-- 件数 -->  
					DATA_SOURCE	= #dataSource#,   <!-- 数据来源 -->  
					CRANE_RESULT_ID	= #craneResultId#,   <!-- 行车实绩单号 -->  
					WAREHOUSE_CODE	= #warehouseCode#,   <!-- 仓库代码 -->  
					WAREHOUSE_NAME	= #warehouseName#,   <!-- 仓库名称 -->  
					LOCATION_ID	= #locationId#,   <!-- 库位代码 -->  
					LOCATION_NAME	= #locationName#,   <!-- 库位名称 -->  
					PUTOUT_LOCATION_ID	= #putoutLocationId#,   <!-- 转出库位代码 -->  
					PUTOUT_LOCATION_NAME	= #putoutLocationName#,   <!-- 转出库位名称 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					TENANT_USER	= #tenantUser#,   <!-- 租户 -->  
					DEL_FLAG	= #delFlag#,   <!-- 删除标记 -->  
					REMAEK	= #remaek#,   <!-- 备注 -->  
								LABEL_ID	= #labelId#  <!-- 标签号 -->  
			WHERE 	
			UUID = #uuid#
	</update>
  
</sqlMap>