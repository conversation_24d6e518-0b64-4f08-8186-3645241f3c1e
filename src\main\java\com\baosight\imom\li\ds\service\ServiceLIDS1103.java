package com.baosight.imom.li.ds.service;

import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.MapUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.ds.domain.LIDS1103;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实时获取UWB当前位置
 */
public class ServiceLIDS1103 extends ServiceBase {

    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIDS1103().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                outInfo = super.query(inInfo, LIDS1103.QUERY, new LIDS1103());
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }

    /**
     * 获取行车当前位置并新增UWB接口表记录
     *
     * @param inInfo
     * @return
     */
    public EiInfo runTrigger(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //查询条件
            Map paramMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);

            //依据行车编号获取卡号
            paramMap.put("craneId", MapUtils.getString(MesConstant.CARD_MAP, MapUtils.getString(paramMap, "craneId")));
            EiInfo sendInfo = new EiInfo();
            sendInfo.set(EiConstant.serviceName, "LIDSInterfaces");
            sendInfo.set(EiConstant.methodName, "getUwbRealTimeLocation");
            sendInfo.set("paramMap", paramMap);
            sendInfo = XLocalManager.call(sendInfo);
            if (sendInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new PlatException(sendInfo.getMsg());
            }

            //获取UWB实时位置记录
            Map resultMap = sendInfo.getMap("resultMap");
            paramMap.putAll(resultMap);
            //设置创建人信息
            RecordUtils.setCreator(paramMap);

            //新增UWB实时位置记录
            dao.insert(LIDS1103.INSERT, paramMap);

            outInfo.setStatus(EiConstant.STATUS_DEFAULT);
            outInfo.setMsg("执行成功！");
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }


    /**
     * 修改记录备注
     *
     * @param inInfo
     * @return
     */
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            if (!DaoUtils.isEmptyUnit(inInfo)) {
                List<HashMap> resultList = inInfo.getBlock(EiConstant.resultBlock).getRows();
                resultList.forEach(resultMap -> {
                    //设置修改人信息
                    RecordUtils.setRevisor(resultMap);
                });
                //修改行车位置记录
                outInfo = super.update(inInfo, LIDS1103.UPDATE);

                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("修改成功！");
            } else {
                return inInfo;
            }
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
