<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2024-09-25 11:00:15
   		Version :  1.0
		tableName :${meliSchema}.tlirl0402
		 SEG_NO  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 QUEUE_NUMBER  VARCHAR   NOT NULL, 
		 CAR_TRACE_NO  VARCHAR   NOT NULL, 
		 VEHICLE_NO  VARCHAR   NOT NULL, 
		 VOUCHER_NUM  VARCHAR   NOT NULL, 
		 PRIORITY_LEVEL  VARCHAR   NOT NULL, 
		 QUEUE_DATE  VARCHAR   NOT NULL, 
		 TARGET_HAND_POINT_ID  VARCHAR   NOT NULL, 
		 FACTORY_AREA  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  SMALLINT   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 REMARK  VARCHAR   NOT NULL, 
		 SYS_REMARK  VARCHAR   NOT NULL, 
		 UUID  VARCHAR   NOT NULL, 
		 TENANT_ID  VARCHAR   NOT NULL
	-->
<sqlMap namespace="LIRL0402">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueNumber">
			QUEUE_NUMBER = #queueNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="priorityLevel">
			PRIORITY_LEVEL = #priorityLevel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueDate">
			QUEUE_DATE = #queueDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>

		<isNotEmpty prepend=" AND " property="callCount">
			CALL_COUNT = #callCount#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.HashMap" 
			resultClass="com.baosight.imom.li.rl.dao.LIRL0402">
		SELECT
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
				CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
				VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
				VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
				PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
				QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
				TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点代码 -->
				FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				REMARK	as "remark",  <!-- 备注 -->
				SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
				UUID	as "uuid",  <!-- uuid -->
				TENANT_ID	as "tenantId", <!-- 租户ID -->
				CALL_COUNT	as "callCount" <!-- 叫号次数 -->

		FROM ${meliSchema}.tlirl0402 WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
  		</dynamic>
			
	</select>
	<select id="queryInfo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		QUEUE_NUMBER	as "queueNumber",  <!-- 顺序号 -->
		CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
		VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
		VOUCHER_NUM	as "voucherNum",  <!-- 提单号 -->
		PRIORITY_LEVEL	as "priorityLevel",  <!-- 优先级（默认99） -->
		QUEUE_DATE	as "queueDate",  <!-- 排序时间（当前时间） -->
		TARGET_HAND_POINT_ID	as "targetHandPointId",  <!-- 目标装卸点代码 -->
		FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		REMARK	as "remark",  <!-- 备注 -->
		SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
		UUID	as "uuid",  <!-- uuid -->
		TENANT_ID	as "tenantId", <!-- 租户ID -->
		CALL_COUNT	as "callCount", <!-- 叫号次数 -->
		(SELECT HAND_POINT_NAME
		FROM MELI.tlirl0304 tlirl0304
		WHERE 1 = 1
		AND tlirl0304.SEG_NO = tlirl0402.SEG_NO
		AND tlirl0304.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID
		AND tlirl0304.STATUS = '30'
		LIMIT 1)                      AS "handPointName"
		FROM ${meliSchema}.tlirl0402 WHERE 1=1
		<include refid="condition"/>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${meliSchema}.tlirl0402 WHERE 1=1
		<include refid="condition"/>
	</select>
	
	<!--  
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueNumber">
			QUEUE_NUMBER = #queueNumber#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="voucherNum">
			VOUCHER_NUM = #voucherNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="priorityLevel">
			PRIORITY_LEVEL = #priorityLevel#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="queueDate">
			QUEUE_DATE = #queueDate#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointId">
			TARGET_HAND_POINT_ID = #targetHandPointId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="factoryArea">
			FACTORY_AREA = #factoryArea#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="remark">
			REMARK = #remark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sysRemark">
			SYS_REMARK = #sysRemark#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantId">
			TENANT_ID = #tenantId#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO ${meliSchema}.tlirl0402 (SEG_NO,  <!-- 系统账套 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										QUEUE_NUMBER,  <!-- 顺序号 -->
										CAR_TRACE_NO,  <!-- 车辆跟踪号 -->
										VEHICLE_NO,  <!-- 车牌号 -->
										VOUCHER_NUM,  <!-- 提单号 -->
										PRIORITY_LEVEL,  <!-- 优先级（默认99） -->
										QUEUE_DATE,  <!-- 排序时间（当前时间） -->
										TARGET_HAND_POINT_ID,  <!-- 目标装卸点代码 -->
										FACTORY_AREA,  <!-- 厂区 -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										REMARK,  <!-- 备注 -->
										SYS_REMARK,  <!-- 系统备注 -->
										UUID,  <!-- uuid -->
										TENANT_ID,  <!-- 租户ID -->
										CALL_COUNT  <!-- 叫号次数 -->
										)		 
	    VALUES (#segNo#, #unitCode#, #queueNumber#, #carTraceNo#, #vehicleNo#, #voucherNum#, #priorityLevel#, #queueDate#, #targetHandPointId#, #factoryArea#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #archiveFlag#, #delFlag#, #remark#, #sysRemark#, #uuid#, #tenantId#,#callCount#)
	</insert>
  
	<delete id="delete">
		DELETE FROM ${meliSchema}.tlirl0402 WHERE
		1 = 1
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="carTraceNo">
			CAR_TRACE_NO = #carTraceNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="vehicleNo">
			VEHICLE_NO = #vehicleNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointIdEq">
			TARGET_HAND_POINT_ID != #targetHandPointIdEq#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="targetHandPointIdNEq">
			TARGET_HAND_POINT_ID = #targetHandPointIdNEq#
		</isNotEmpty>
	</delete>

	<update id="update">
		UPDATE ${meliSchema}.tlirl0402
		SET 
		SEG_NO	= #segNo#,   <!-- 系统账套 -->  
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->  
					QUEUE_NUMBER	= #queueNumber#,   <!-- 顺序号 -->  
					CAR_TRACE_NO	= #carTraceNo#,   <!-- 车辆跟踪号 -->  
					VEHICLE_NO	= #vehicleNo#,   <!-- 车牌号 -->  
					VOUCHER_NUM	= #voucherNum#,   <!-- 提单号 -->  
					PRIORITY_LEVEL	= #priorityLevel#,   <!-- 优先级（默认99） -->  
					QUEUE_DATE	= #queueDate#,   <!-- 排序时间（当前时间） -->  
					TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点代码 -->  
					FACTORY_AREA	= #factoryArea#,   <!-- 厂区 -->  
					REC_CREATOR	= #recCreator#,   <!-- 记录创建人 -->  
					REC_CREATOR_NAME	= #recCreatorName#,   <!-- 记录创建人姓名 -->  
					REC_CREATE_TIME	= #recCreateTime#,   <!-- 记录创建时间 -->  
					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->  
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->  
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->  
					ARCHIVE_FLAG	= #archiveFlag#,   <!-- 归档标记 -->  
					DEL_FLAG	= #delFlag#,   <!-- 记录删除标记 -->  
					REMARK	= #remark#,   <!-- 备注 -->  
					SYS_REMARK	= #sysRemark#,   <!-- 系统备注 -->  
					UUID	= #uuid#,   <!-- uuid -->  
					TENANT_ID	= #tenantId#,  <!-- 租户ID -->
					CALL_COUNT	= #callCount#  <!-- 租户ID -->

		WHERE
		1 = 1
		AND SEG_NO = #segNo#
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
	</update>

	<select id="queryCallM" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imom.li.rl.dao.LIRL0402">
		select
		tlirl0402.VEHICLE_NO AS "vehicleNo",
		tlirl0402.CAR_TRACE_NO AS "carTraceNo",
		tlirl0402.TARGET_HAND_POINT_ID AS "targetHandPointId",
		tlirl0402.VOUCHER_NUM AS "voucherNum",
		tlirl0402.QUEUE_DATE AS "queueDate",
		tlirl0402.FACTORY_AREA AS "factoryArea"
		from ${meliSchema}.tlirl0402 tlirl0402
		where tlirl0402.SEG_NO =#segNo#
		and  TIMESTAMPDIFF(MINUTE, STR_TO_DATE(tlirl0402.REC_CREATE_TIME, '%Y%m%d%H%i%s'), now()
		) > 15
		and not exists(select tlirl0405.VEHICLE_NO
		from ${meliSchema}.tlirl0405 tlirl0405
		where tlirl0405.SEG_NO = #segNo#
		and
		tlirl0405.VEHICLE_NO = tlirl0402.VEHICLE_NO
		and tlirl0405.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
		and tlirl0405.STATUS != '00')
	</select>

	<select id="queryCallAndHandPointIsExist"
			resultClass="int">
		select count(1)
		from ${meliSchema}.tlirl0401 tlirl0401,
		${meliSchema}.tlirl0305 tlirl0305
		where 1 = 1
		and tlirl0401.SEG_NO =#segNo#
		and tlirl0401.SEG_NO = tlirl0305.SEG_NO
		and tlirl0401.VEHICLE_NO = tlirl0305.VEHICLE_ID
		and tlirl0401.CAR_TRACE_NO = tlirl0305.CAR_TRACE_NO
		and tlirl0305.HAND_POINT_ID = #handPointId#
	</select>

	<update id="updateCallCount">
		UPDATE ${meliSchema}.tlirl0402
		SET
		CALL_COUNT	= #callCount#, <!-- 叫号次数 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#  <!-- 记录修改时间 -->
		WHERE
		1 = 1
		AND SEG_NO = #segNo#
		AND CAR_TRACE_NO = #carTraceNo#
		AND VEHICLE_NO = #vehicleNo#
		AND TARGET_HAND_POINT_ID = #handPointId#
	</update>

	<update id="updateHandPointId">
		UPDATE ${meliSchema}.tlirl0402
		SET
		TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO	= #segNo# and CAR_TRACE_NO	= #carTraceNo#
	</update>

	<update id="updateHandPointIdCQ">
		UPDATE ${meliSchema}.tlirl0402
		SET
		TARGET_HAND_POINT_ID	= #targetHandPointId#,   <!-- 目标装卸点 -->
		REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
		REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	= #recReviseTime#   <!-- 记录修改时间 -->
		WHERE
		SEG_NO	= #segNo# and CAR_TRACE_NO	= #carTraceNo#
		<isNotEmpty prepend=" and " property="voucherNum">
			VOUCHER_NUM= #allocateVehicleNo#
		</isNotEmpty>
		and
		TARGET_HAND_POINT_ID = #oldHandPointId#
	</update>

	<select id="queryMaxQueueNumber" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
		ifnull(MAX(QUEUE_NUMBER),0)+1	as "maxQueueNumber" <!-- 最大顺序号 -->
		FROM ${meliSchema}.tlirl0402 WHERE 1=1
		and SEG_NO	= #segNo#
	</select>

	<select id="queryCallNumVehicleNo" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		select DISTINCT tlirl0402.SEG_NO as "segNo",
		tlirl0402.CAR_TRACE_NO as "carTraceNo",
		tlirl0402.VOUCHER_NUM as "allocateVehicleNo",
		tlirl0402.VEHICLE_NO as "vehicleNo",
		tlirl0402.TARGET_HAND_POINT_ID as "handPointId",
		(select HAND_POINT_NAME
		from meli.tlirl0304 tlirl0304
		where 1 = 1
		and tlirl0304.SEG_NO = tlirl0402.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID
		and tlirl0304.STATUS = '30'
		limit 1) as "handPointName",
		(select TEL_NUM
		from meli.tlirl0301 lirl0301
		where 1 = 1
		and lirl0301.SEG_NO
		= tlirl0402.SEG_NO
		and lirl0301.CAR_TRACE_NO = tlirl0402.CAR_TRACE_NO
		limit 1) as "driverTel"
		from meli.tlirl0402 tlirl0402
		where 1 = 1
		and tlirl0402.SEG_NO = #segNo#
		and exists(select 1
		from meli.tlirl0304 tlirl0304
		where tlirl0304.SEG_NO = tlirl0402.SEG_NO
		and tlirl0304.HAND_POINT_ID = tlirl0402.TARGET_HAND_POINT_ID
		and tlirl0304.FACTORY_BUILDING = #factoryBuilding#)
		<isNotEmpty prepend=" and " property="handPointId">
			tlirl0402.TARGET_HAND_POINT_ID = #handPointId#
		</isNotEmpty>

	</select>
	
	<select id="queryCallNumInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT SEG_NO               as "segNo",
		CAR_TRACE_NO         as "carTraceNo",
		VEHICLE_NO           as "vehicleNo",
		TARGET_HAND_POINT_ID as "targetHandPointId"
		FROM MELI.tlirl0402
		WHERE 1 = 1
		AND SEG_NO = #segNo#
		AND CAR_TRACE_NO = #carTraceNo#
		AND VEHICLE_NO = #vehicleNo#
		AND TARGET_HAND_POINT_ID = #targetHandPointId#
		union
		SELECT SEG_NO               as "segNo",
		CAR_TRACE_NO         as "carTraceNo",
		VEHICLE_NO           as "vehicleNo",
		CURRENT_HAND_POINT_ID as "targetHandPointId"
		FROM MELI.tlirl0301
		WHERE 1 = 1
		AND SEG_NO = #segNo#
		AND CAR_TRACE_NO = #carTraceNo#
		AND VEHICLE_NO = #vehicleNo#
		AND CURRENT_HAND_POINT_ID = #targetHandPointId#
	</select>

	<select id="queryVehicleCountCq"  parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select VEHICLE_NO as "vehicleNo"
		from meli.tlirl0402
		where 1 = 1
		and SEG_NO = #segNo#
	</select>

	<!-- 综合查询车辆完整信息，包括叫号记录、作业状态、排队信息等 -->
	<select id="queryVehicleCompleteInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT

		COALESCE(lirl0402.CALL_COUNT, 0) as "callCount",
		lirl0304.HAND_POINT_NAME as "handPointName",


		(SELECT COUNT(1)
		FROM ${meliSchema}.tlirl0301 work_check
		WHERE work_check.SEG_NO = #segNo#
		AND (work_check.TARGET_HAND_POINT_ID = #handPointId# or work_check.CURRENT_HAND_POINT_ID = #handPointId#)
		AND work_check.STATUS in ('20','30')
		and work_check.CAR_TRACE_NO != #carTraceNo#
		AND work_check.DEL_FLAG = '0') as "workingVehicleCount",

		(SELECT COALESCE(MAX(CAST(QUEUE_NUMBER AS UNSIGNED)), 0)
		FROM ${meliSchema}.tlirl0402 queue_max
		WHERE queue_max.SEG_NO = #segNo#
		AND queue_max.DEL_FLAG = '0') as "maxQueueNumber",

		lirl0301.FACTORY_AREA as "factoryArea",
		lirl0301.TEL_NUM as "telNum",
		lirl0301.DRIVER_NAME as "driverName",
		lirl0301.STATUS as "vehicleStatus"

		FROM ${meliSchema}.tlirl0301 lirl0301

		LEFT JOIN ${meliSchema}.tlirl0402 lirl0402
		ON lirl0402.SEG_NO = lirl0301.SEG_NO
		AND lirl0402.CAR_TRACE_NO = lirl0301.CAR_TRACE_NO
		AND lirl0402.VEHICLE_NO = lirl0301.VEHICLE_NO
		AND lirl0402.DEL_FLAG = '0'

		LEFT JOIN ${meliSchema}.tlirl0304 lirl0304
		ON lirl0304.SEG_NO = #segNo#
		AND lirl0304.HAND_POINT_ID = #handPointId#
		AND lirl0304.STATUS = '30'
		AND lirl0304.DEL_FLAG = '0'

		WHERE lirl0301.SEG_NO = #segNo#
		AND lirl0301.CAR_TRACE_NO = #carTraceNo#
		AND lirl0301.VEHICLE_NO = #vehicleNo#
		AND lirl0301.DEL_FLAG = '0'
		LIMIT 1
	</select>
</sqlMap>