package com.baosight.imom.vg.dm.service;

import com.baosight.imom.common.constants.CacheConstant;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.vg.dm.domain.VGDM0302;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.imom.vg.dm.domain.VGDM0301;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yzj
 * @Description : 采集点位清单页面后台
 * @Date : 2024/8/28
 * @Version : 1.0
 */
public class ServiceVGDM03 extends ServiceBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceVGDM03.class);

    /**
     * 加载
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new VGDM0302().eiMetadata);
        inInfo.addBlock(MesConstant.Iplat.RESULT2_BLOCK).addBlockMeta(new VGDM0301().eiMetadata);
        inInfo.addBlock(CodeValueUtils.getUnitBlock(dao));
        return inInfo;
    }

    /**
     * 查询scada信息
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        return DaoUtils.isEmptyUnit(inInfo) ? inInfo : super.query(inInfo, VGDM0302.QUERY, new VGDM0302());
    }

    /**
     * 根据scada查询点位信息
     */
    public EiInfo queryTag(EiInfo inInfo) {
        return super.query(inInfo, VGDM0301.QUERY, null, false, new VGDM0301().eiMetadata,
                MesConstant.Iplat.INQU2_STATUS_BLOCK, MesConstant.Iplat.RESULT2_BLOCK, MesConstant.Iplat.RESULT2_BLOCK);
    }

    /**
     * 从BC同步采集器scada信息
     */
    public EiInfo syncScada(EiInfo inInfo) {
        try {
            // 前端参数
            String unitCode = inInfo.getCellStr(EiConstant.queryBlock, 0, "unitCode");
            String segNo = inInfo.getCellStr(EiConstant.queryBlock, 0, "segNo");
            if (StrUtil.isBlank(segNo)) {
                throw new PlatException("业务单元代码不能为空");
            }
            // 获取业务单元前缀
            String segNoPrefix = segNo.substring(0, 2);
            // 调用接口获取scada信息
            EiInfo eiInfo = new EiInfo();
            eiInfo.setCell(EiConstant.queryBlock, 0, "scadaName", segNoPrefix);
            // 设置分页查询条件
            eiInfo.addBlock(EiConstant.resultBlock).set(EiConstant.offsetStr, "0");
            eiInfo.addBlock(EiConstant.resultBlock).set(EiConstant.limitStr, "1000");
            // 设置查询服务
            eiInfo.set(EiConstant.serviceId, IplatUtils.SERVICE_QUERY_SCADA);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (outInfo.getStatus() != 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                return outInfo;
            }
            // 删除原数据            
            Map<String, String> delMap = new HashMap<>();
            delMap.put("unitCode", unitCode);
            delMap.put("segNo", segNo);
            delMap.put("delFlag", "1");
            dao.update(VGDM0302.UPDATE, delMap);
            // 数据保存到数据库
            EiBlock block = outInfo.getBlock(EiConstant.resultBlock);
            VGDM0302 vgdm0302;
            List<Map> insList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0302 = new VGDM0302();
                vgdm0302.fromMap(block.getRow(i));
                vgdm0302.setSegNo(segNo);
                vgdm0302.setUnitCode(unitCode);
                Map insMap = vgdm0302.toMap();
                RecordUtils.setCreator(insMap);
                insList.add(insMap);
            }
            DaoUtils.insertBatch(dao, VGDM0302.INSERT, insList);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 从BC同步采集点位信息
     */
    public EiInfo syncTag(EiInfo inInfo) {
        try {
            String scadaName = inInfo.getCellStr(MesConstant.Iplat.INQU2_STATUS_BLOCK, 0, "scadaName");
            // 获取scada信息
            VGDM0302 vgdm0302 = VGDM0302.queryWithCache(dao, scadaName);
            if (vgdm0302 == null) {
                throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
            }
            // 调用接口获取点位信息
            EiBlock block = queryAllTag(vgdm0302.getScadaId());
            VGDM0301 vgdm0301;
            List<Map> insList = new ArrayList<>();
            List<Map> updList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                String tagId = block.getCellStr(i, "tagName");
                // 缓存中存在时跳过
                VGDM0301 dbData = VGDM0301.queryWithCache(dao, tagId);
                if (dbData != null) {
                    this.setTagInfo(dbData, block.getRow(i));
                    Map updMap = dbData.toMap();
                    RecordUtils.setRevisor(updMap);
                    updList.add(updMap);
                } else {
                    vgdm0301 = new VGDM0301();
                    vgdm0301.setTagId(tagId);
                    this.setTagInfo(vgdm0301, block.getRow(i));
                    vgdm0301.setScadaName(scadaName);
                    vgdm0301.setSegNo(vgdm0302.getSegNo());
                    vgdm0301.setUnitCode(vgdm0302.getUnitCode());
                    Map insMap = vgdm0301.toMap();
                    RecordUtils.setCreator(insMap);
                    insList.add(insMap);
                }
            }
            // 批量修改
            DaoUtils.updateBatch(dao, VGDM0301.UPDATE_TAG, updList);
            // 批量新增
            DaoUtils.insertBatch(dao, VGDM0301.INSERT, insList);
            // 返回
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_GENERATE);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 设置点位信息
     *
     * @param vgdm0301 点位
     * @param tagMap   tagMap
     */
    private void setTagInfo(VGDM0301 vgdm0301, Map tagMap) {
        vgdm0301.setTagDesc(MapUtils.getString(tagMap, "desp"));
        vgdm0301.setTagType(MapUtils.getString(tagMap, "tagType"));
        vgdm0301.setDataType(MapUtils.getString(tagMap, "dataType"));
        vgdm0301.setDriverType(MapUtils.getString(tagMap, "driverName"));
        vgdm0301.setDeviceId(MapUtils.getString(tagMap, "deviceName"));
        vgdm0301.setDeviceAddress(MapUtils.getString(tagMap, "ioAddr"));
    }

    /**
     * 每页查询数量
     */
    private static final int PAGE_SIZE = 1000;

    /**
     * 查询所有点位信息
     * <p>
     * 分页查询，每次查询PAGE_SIZE条，直到查询完所有点位信息
     *
     * @param scadaId 采集器id
     * @return 点位信息
     */
    private EiBlock queryAllTag(Integer scadaId) {
        // 调用接口获取点位信息
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("inqu_status-0-scadaId", scadaId);
        // 设置分页查询条件 第一页1000条
        eiInfo.addBlock(EiConstant.resultBlock).set(EiConstant.offsetStr, "0");
        eiInfo.addBlock(EiConstant.resultBlock).set(EiConstant.limitStr, PAGE_SIZE);
        log("初次查询scadaId:" + scadaId + ",offset:0,limit:" + PAGE_SIZE);
        // 设置查询服务
        eiInfo.set(EiConstant.serviceId, IplatUtils.SERVICE_QUERY_TAG);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (outInfo.getStatus() != 0) {
            throw new PlatException(outInfo.getMsg());
        }
        // 初次查询返回的结果
        EiBlock block = outInfo.getBlock(EiConstant.resultBlock);
        // 总数
        int total = block.getInt(EiConstant.countStr);
        int page = (int) Math.ceil((double) total / PAGE_SIZE);
        log("总数:" + total + ",页数:" + page);
        for (int i = 1; i < page; i++) {
            // 设置分页查询条件
            eiInfo.getBlock(EiConstant.resultBlock).set(EiConstant.offsetStr, String.valueOf(i * PAGE_SIZE));
            log("再次查询scadaId:" + scadaId + ",offset:" + i * PAGE_SIZE);
            // 再次查询
            EiInfo tempInfo = XServiceManager.call(eiInfo);
            if (tempInfo.getStatus() != 0) {
                throw new PlatException(tempInfo.getMsg());
            }
            log("第" + i + "页查询成功");
            block.addRows(tempInfo.getBlock(EiConstant.resultBlock).getRows());
        }
        return block;
    }

    /**
     * 清空采集点位相关缓存
     */
    public EiInfo clear(EiInfo inInfo) {
        try {
            Map cache = CacheManager.getCache(CacheConstant.CACHE_TAG);
            cache.clear();
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(MessageCodeConstant.successMessage.MSG_SUCCESS_CLEAR);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 修改采集点位信息上的设备和分部设备信息
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0301 vgdm0301;
            VGDM0301 dbData;
            List<String> idList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0301 = new VGDM0301();
                vgdm0301.fromMap(block.getRow(i));
                dbData = VGDM0301.queryWithCache(dao, vgdm0301.getTagId());
                if (dbData == null) {
                    throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
                }
                vgdm0301.setDelFlag("0");
                Map updMap = vgdm0301.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
                idList.add(vgdm0301.getTagId());
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0301.UPDATE, block.getRows());
            // 清空缓存
            clearCache(idList);
            // 设置返回信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * 清空缓存
     */
    private void clearCache(List<String> idList) {
        Map cache = CacheManager.getCache(CacheConstant.CACHE_TAG);
        for (String id : idList) {
            cache.remove(id);
        }
    }

    /**
     * 删除采集点位信息
     * <p>
     * 删除标记修改为1
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0301 vgdm0301;
            VGDM0301 dbData;
            List<String> idList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0301 = new VGDM0301();
                vgdm0301.fromMap(block.getRow(i));
                dbData = VGDM0301.queryWithCache(dao, vgdm0301.getTagId());
                if (dbData == null) {
                    throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
                }
                dbData.setDelFlag("1");
                Map updMap = dbData.toMap();
                RecordUtils.setRevisor(updMap);
                block.getRows().set(i, updMap);
                idList.add(vgdm0301.getTagId());
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0301.UPDATE, block.getRows());
            // 清空缓存
            clearCache(idList);
            // 设置返回信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }

    /**
     * IHD点位同步
     * <p>
     * 根据点位名称获取IHD数据库中的点位id，用于后续ihd数据查询
     */
    public EiInfo syncIhd(EiInfo inInfo) {
        try {
            EiBlock block = inInfo.getBlock(MesConstant.Iplat.RESULT2_BLOCK);
            VGDM0301 vgdm0301;
            VGDM0301 dbData;
            List<String> tagIdList = new ArrayList<>();
            List<VGDM0301> tagList = new ArrayList<>();
            for (int i = 0; i < block.getRowCount(); i++) {
                vgdm0301 = new VGDM0301();
                vgdm0301.fromMap(block.getRow(i));
                dbData = VGDM0301.queryWithCache(dao, vgdm0301.getTagId());
                if (dbData == null) {
                    throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
                }
                tagIdList.add(vgdm0301.getTagId());
                tagList.add(vgdm0301);
            }
            // 调用接口获取IHD点位信息
            Map<String, Integer> tagIhdIdMap = IhdSdkUtils.queryTagIhdId(block.getCellStr(0, "segNo"), tagIdList);
            // 待更新数据
            List<Map> updList = new ArrayList<>();
            // 清空前端待返回数据
            block.getRows().clear();
            // 循环赋值ihd编号
            for (VGDM0301 tag : tagList) {
                tag.setTagIhdId(MapUtils.getInt(tagIhdIdMap, tag.getTagId()));
                Map updMap = tag.toMap();
                RecordUtils.setRevisor(updMap);
                updList.add(updMap);
                block.getRows().add(updMap);
            }
            // 更新数据库
            DaoUtils.updateBatch(dao, VGDM0301.UPDATE, updList);
            // 清空缓存
            clearCache(tagIdList);
            // 设置返回信息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(ex.getMessage());
        }
        return inInfo;
    }
}
