@charset "utf-8";

/* 确保整个页面不出现滚动条 */
html, body {
    overflow: hidden;
    height: 100vh;
}

dl {
    margin: 0;
}

dd {
    margin: 0;
}

dt {
    margin: 0;
}

ul,
li {
    margin: 0;
    padding: 0;
}

body {
    margin: 0;
    padding: 0;
    color: #cccccc;
    font-family: "微软雅黑";
    overflow: hidden; /* 禁止整个页面滚动 */
    height: 100vh; /* 确保全屏高度 */
}

#bg {
    width: 100%;
    height: 100vh;
    background-color: #324E73;
}

.top {
    width: 100%;
    height: 11%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
}

.left-section {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.realtime-clock {
    color: #FFF;
    font-size: 24px;
    font-weight: bold;
    margin-top: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    letter-spacing: 2px;
}

.header {
    font-size: 36px;
    color: #FFF;
    text-align: center;
    letter-spacing: 12px;
    padding: 0;
    margin: 0;
}

.chang-arr {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    color: #FFF;
    font-size: 16px;
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
}

.status-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
}

.status-info div {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.completed span {
    color: #4CAF50;
    /* 绿色 */
    font-size: 18px;
    font-weight: bold;
}

.remaining span {
    color: #FF0000;
    /* 红色 */
    font-size: 18px;
    font-weight: bold;
}

.status-details {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

.status-details div {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-top: 5px;
}

.status-details i {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 5px;
}

.working {
    color: white;
    padding: 2px 6px;
    font-size: 15px;
    background: #4CAF50;
}

.pending {
    color: white;
    padding: 2px 6px;
    font-size: 15px;
    background: #E67E22;
}

/* con */
.con-all {
    width: 100%;
    height: 89%;
    padding: 5px;
    box-sizing: border-box;
    overflow: hidden; /* 防止整个页面滚动 */
}

.con-main {
    width: 100%;
    height: 100%;
}

.con-main {
    display: flex;
    width: 100%;
    height: 100%;
}

.list-one {
    height: 100%; /* 自适应网格高度 */
    min-height: 200px; /* 最小高度 */
    /*margin: 0 5px 5px 0;*/
    background-color: #033;
    display: flex;
    flex-direction: column;
}

/*.list-one:nth-child(10),*/
/*.list-one:nth-child(11).list-one:nth-child(13) {*/
/*    margin: 0 5px 0 0;*/
/*}*/

.list-one-title {
    height: 35px;
    background: linear-gradient(135deg, #0070C0, #005a9a);
    color: #FFFFFF;
    font-size: 18px;
    line-height: 35px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-one-title-name {
    text-align: center;
}

.list-one-title-value {
    position: absolute;
    right: 5px;
    top: 0;
}

.list-one-complete {
    color: #4CAF50;
}

.list-one-remaining {
    color: #ff0000;
}

.list-one-con {
    flex: 1; /* 占据剩余空间 */
    overflow: hidden;  /* 隐藏滚动条，使用自动滚动 */
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 8px;
    box-sizing: border-box;
}

/* 美化滚动条 */
.list-one-con::-webkit-scrollbar {
    width: 6px;
}

.list-one-con::-webkit-scrollbar-track {
    background: #033;
}

.list-one-con::-webkit-scrollbar-thumb {
    background: #0070C0;
    border-radius: 3px;
}

.list-one-con::-webkit-scrollbar-thumb:hover {
    background: #005a9a;
}

.list-one-con-wrapper {
    width: 100%;
}

.list-one-con-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 100%; /* 确保有足够高度进行滚动 */
}

/* 自动滚动动画 */
@keyframes auto-scroll-vertical {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-50%);
    }
}

/* 鼠标悬停时暂停滚动 */
.list-one-con:hover .list-one-con-content,
.all-con:hover > div {
    animation-play-state: paused !important;
}

.list-one-con-one {
    width: 98%; /* 改为一行一个，占满整行 */
    margin: 5px 1% 0;
    text-align: center;
    /*background: #FF0000;*/
    border-radius: 10px;
    color: #000000;
    font-size: 16px;
}

.div-working-tag,
.div-pending-tag {
    width: 98%; /* 改为一行一个，占满整行 */
    margin: 5px 1% 0;
    text-align: center;
    border-radius: 10px;
    color: #FFFFFF;
    font-size: 16px;
}

.div-working-tag {
    background: #4CAF50;
}

.div-pending-tag {
    background: #E67E22;
}

.con-right {
    width: 20%; /* 减小右侧面板宽度 */
    height: 100%;
    transition: opacity 0.3s, transform 0.3s;
}

.con-right-one {
    width: 43%;
    height: 100%;
    padding-right: 5px;
    box-sizing: border-box;
}

.con-right-one-first,
.con-right-two-first {
    width: 100%;
    height: 50%; /* 占据上半部分 */
    margin: 0 0 5px 0;
    display: flex;
    flex-direction: column;
}

.con-right-one-first-title {
    height: 35px; /* 增加高度与门点标题一致 */
    background-color: #0070C0;
    color: #FFFFFF;
    font-size: 18px;
    line-height: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0; /* 防止被压缩 */
    /*padding: 0 5px;*/
    /*margin: 0 5px 5px 0;*/
}

.right-one-first-title-name {
    flex: 1;
    text-align: center;
}

.right-one-first-title-value {
    text-align: right;
}

.con-right-one-second {
    width: 100%;
    /*background-color: #033;*/
    height: calc(((100% - 15px)/2) + 5px);
}

.con-right-two {
    display: flex;
    flex-direction: column; /* 垂直布局 */
    height: 100%;
    /*flex: 1;*/
}

.con-right-two-second {
    width: 100%;
    height: 50%; /* 占据下半部分 */
    display: flex;
    flex-direction: column;
}

/* 超出滚动 */
.all-con,
.all-con-right-two {
    flex: 1; /* 占据剩余空间 */
    overflow: hidden;  /* 隐藏滚动条，使用自动滚动 */
    position: relative;
    background-color: #033;
    padding: 5px;
    box-sizing: border-box;
}

/* 右侧面板滚动条美化 */
.all-con::-webkit-scrollbar {
    width: 6px;
}

.all-con::-webkit-scrollbar-track {
    background: #033;
}

.all-con::-webkit-scrollbar-thumb {
    background: #0070C0;
    border-radius: 3px;
}

.all-con::-webkit-scrollbar-thumb:hover {
    background: #005a9a;
}

@keyframes scroll {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-50%);
    }
}

.all-con-wrapper {
    width: 100%;
}

.all-con-content {
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* .all-con-one 样式现在在JavaScript中内联设置 */

.con-left {
    margin-right: 5px;
    position: relative;
    width: 80%; /* 增加左侧区域宽度 */
    height: 100%;
    overflow: hidden; /* 完全禁止滚动 */
    /*gap: 5px;*/
}

.page-container {
    position: relative; /* 改为相对定位，允许自动高度 */
    width: 100%;
    height: 100%; /* 固定高度，不允许超出 */
    /*gap: 5px;*/
    opacity: 1; /* 直接显示，不使用动画 */
    transform: none; /* 移除变换 */
    transition: all 0.3s ease;
}

.page-container.active {
    opacity: 1;
    transform: translateY(0);
}

.page-container.incoming {
    transform: translateY(100%);
}

.page-container.outgoing {
    transform: translateY(-100%);
    opacity: 0;
}

/* 新增CSS */
.con-main {
    transition: all 0.3s ease;
}

.layout-collapsed {
    display: none !important;
}

.con-right.layout-expanded {
    display: block;
    flex: 0 0 30% !important;
}

/* 动态宽度调整 */
#con-left {
    transition: width 0.3s ease;
}

.page-container {
    transition: all 0.3s ease;
}

/* 将网格设置转移到布局类 */
.con-left.layout-9 .page-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 280px);
    gap: 5px;
    height: 100%;
    overflow-y: auto;
}

.con-left.visible-layout-9 .page-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(2, 280px);
    gap: 5px;
    height: 100%;
    overflow-y: auto;
}

.con-left.layout-16 .page-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 280px);
    gap: 5px;
    height: 100%;
    overflow-y: auto;
}

.con-left.visible-layout-16 .page-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(4, 280px);
    gap: 5px;
    height: 100%;
    overflow-y: auto;
}

.con-left.layout-all .page-container {
    display: grid;
    grid-template-columns: repeat(9, 1fr);  /* 9列布局，27个装卸点分3行显示 */
    gap: 4px; /* 减小间距 */
    height: 100%; /* 固定高度 */
    grid-template-rows: repeat(3, 1fr); /* 固定3行，每行等高 */
    /*padding: 5px; !* 添加内边距 *!*/
    align-content: stretch; /* 拉伸填满容器 */
    grid-auto-flow: row; /* 确保按行排列 */
}

.con-left.layout-all .list-one {
    height: 100%; /* 自适应网格高度 */
    min-height: 200px; /* 最小高度 */
    max-height: 100%; /* 最大高度不超过网格 */
}

/* 布局按钮相关样式已移除 */

/* 响应式设计 - 保持在一个屏幕内显示所有装卸点 */
@media screen and (max-width: 1600px) {
    .con-left.layout-all .page-container {
        grid-template-columns: repeat(8, 1fr); /* 8列布局，4行显示27个装卸点 */
        grid-template-rows: repeat(4, 1fr); /* 固定4行，每行等高 */
    }
}

@media screen and (max-width: 1400px) {
    .con-left.layout-all .page-container {
        grid-template-columns: repeat(7, 1fr); /* 7列布局，4行显示27个装卸点 */
        grid-template-rows: repeat(4, 1fr); /* 固定4行，每行等高 */
    }
}

@media screen and (max-width: 1200px) {
    .con-left.layout-all .page-container {
        grid-template-columns: repeat(6, 1fr); /* 6列布局，5行显示27个装卸点 */
        grid-template-rows: repeat(5, 1fr); /* 固定5行，每行等高 */
    }
}

@media screen and (max-width: 900px) {
    .con-left.layout-all .page-container {
        grid-template-columns: repeat(5, 1fr); /* 5列布局，6行显示27个装卸点 */
        grid-template-rows: repeat(6, 1fr); /* 固定6行，每行等高 */
    }
}



